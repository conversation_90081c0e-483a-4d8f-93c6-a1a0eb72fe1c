663cd92938f46e934ad388cfead3e593
"use strict";

/**
 * Gradient Descent Algorithm Implementation for System Optimization
 *
 * Implements gradient descent optimization with:
 * - Multiple variants (standard, momentum, Adam, RMSprop)
 * - Numerical gradient computation with finite differences
 * - Adaptive learning rate and step size control
 * - Line search optimization
 * - Constraint handling with projected gradients
 *
 * @version 3.0.0
 * <AUTHOR> Suite Development Team
 */
/* istanbul ignore next */
function cov_hr0h2ihfp() {
  var path = "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\algorithms\\GradientDescent.ts";
  var hash = "aada9f28fc0bbc24d3345be3f9f99b95b0b42bc9";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\algorithms\\GradientDescent.ts",
    statementMap: {
      "0": {
        start: {
          line: 15,
          column: 0
        },
        end: {
          line: 15,
          column: 62
        }
      },
      "1": {
        start: {
          line: 16,
          column: 0
        },
        end: {
          line: 16,
          column: 33
        }
      },
      "2": {
        start: {
          line: 17,
          column: 34
        },
        end: {
          line: 17,
          column: 77
        }
      },
      "3": {
        start: {
          line: 23,
          column: 8
        },
        end: {
          line: 23,
          column: 33
        }
      },
      "4": {
        start: {
          line: 24,
          column: 8
        },
        end: {
          line: 24,
          column: 33
        }
      },
      "5": {
        start: {
          line: 25,
          column: 8
        },
        end: {
          line: 25,
          column: 26
        }
      },
      "6": {
        start: {
          line: 26,
          column: 8
        },
        end: {
          line: 26,
          column: 33
        }
      },
      "7": {
        start: {
          line: 27,
          column: 8
        },
        end: {
          line: 49,
          column: 10
        }
      },
      "8": {
        start: {
          line: 51,
          column: 8
        },
        end: {
          line: 56,
          column: 9
        }
      },
      "9": {
        start: {
          line: 52,
          column: 12
        },
        end: {
          line: 52,
          column: 77
        }
      },
      "10": {
        start: {
          line: 55,
          column: 12
        },
        end: {
          line: 55,
          column: 38
        }
      },
      "11": {
        start: {
          line: 62,
          column: 26
        },
        end: {
          line: 62,
          column: 43
        }
      },
      "12": {
        start: {
          line: 63,
          column: 8
        },
        end: {
          line: 84,
          column: 9
        }
      },
      "13": {
        start: {
          line: 65,
          column: 12
        },
        end: {
          line: 65,
          column: 42
        }
      },
      "14": {
        start: {
          line: 67,
          column: 12
        },
        end: {
          line: 67,
          column: 46
        }
      },
      "15": {
        start: {
          line: 69,
          column: 12
        },
        end: {
          line: 69,
          column: 94
        }
      },
      "16": {
        start: {
          line: 71,
          column: 12
        },
        end: {
          line: 77,
          column: 13
        }
      },
      "17": {
        start: {
          line: 72,
          column: 16
        },
        end: {
          line: 72,
          column: 93
        }
      },
      "18": {
        start: {
          line: 73,
          column: 16
        },
        end: {
          line: 73,
          column: 37
        }
      },
      "19": {
        start: {
          line: 74,
          column: 16
        },
        end: {
          line: 76,
          column: 17
        }
      },
      "20": {
        start: {
          line: 75,
          column: 20
        },
        end: {
          line: 75,
          column: 45
        }
      },
      "21": {
        start: {
          line: 79,
          column: 12
        },
        end: {
          line: 79,
          column: 69
        }
      },
      "22": {
        start: {
          line: 82,
          column: 12
        },
        end: {
          line: 82,
          column: 74
        }
      },
      "23": {
        start: {
          line: 83,
          column: 12
        },
        end: {
          line: 83,
          column: 24
        }
      },
      "24": {
        start: {
          line: 91,
          column: 37
        },
        end: {
          line: 91,
          column: 113
        }
      },
      "25": {
        start: {
          line: 91,
          column: 65
        },
        end: {
          line: 91,
          column: 112
        }
      },
      "26": {
        start: {
          line: 92,
          column: 8
        },
        end: {
          line: 94,
          column: 9
        }
      },
      "27": {
        start: {
          line: 93,
          column: 12
        },
        end: {
          line: 93,
          column: 133
        }
      },
      "28": {
        start: {
          line: 100,
          column: 8
        },
        end: {
          line: 100,
          column: 33
        }
      },
      "29": {
        start: {
          line: 101,
          column: 8
        },
        end: {
          line: 101,
          column: 33
        }
      },
      "30": {
        start: {
          line: 102,
          column: 8
        },
        end: {
          line: 102,
          column: 26
        }
      },
      "31": {
        start: {
          line: 103,
          column: 8
        },
        end: {
          line: 103,
          column: 33
        }
      },
      "32": {
        start: {
          line: 104,
          column: 8
        },
        end: {
          line: 104,
          column: 134
        }
      },
      "33": {
        start: {
          line: 111,
          column: 32
        },
        end: {
          line: 111,
          column: 66
        }
      },
      "34": {
        start: {
          line: 112,
          column: 8
        },
        end: {
          line: 112,
          column: 102
        }
      },
      "35": {
        start: {
          line: 114,
          column: 29
        },
        end: {
          line: 114,
          column: 53
        }
      },
      "36": {
        start: {
          line: 115,
          column: 8
        },
        end: {
          line: 128,
          column: 10
        }
      },
      "37": {
        start: {
          line: 129,
          column: 8
        },
        end: {
          line: 129,
          column: 51
        }
      },
      "38": {
        start: {
          line: 135,
          column: 26
        },
        end: {
          line: 135,
          column: 28
        }
      },
      "39": {
        start: {
          line: 136,
          column: 8
        },
        end: {
          line: 148,
          column: 9
        }
      },
      "40": {
        start: {
          line: 137,
          column: 12
        },
        end: {
          line: 147,
          column: 13
        }
      },
      "41": {
        start: {
          line: 139,
          column: 36
        },
        end: {
          line: 139,
          column: 94
        }
      },
      "42": {
        start: {
          line: 140,
          column: 16
        },
        end: {
          line: 140,
          column: 78
        }
      },
      "43": {
        start: {
          line: 144,
          column: 28
        },
        end: {
          line: 144,
          column: 101
        }
      },
      "44": {
        start: {
          line: 145,
          column: 28
        },
        end: {
          line: 145,
          column: 101
        }
      },
      "45": {
        start: {
          line: 146,
          column: 16
        },
        end: {
          line: 146,
          column: 75
        }
      },
      "46": {
        start: {
          line: 149,
          column: 8
        },
        end: {
          line: 158,
          column: 10
        }
      },
      "47": {
        start: {
          line: 164,
          column: 8
        },
        end: {
          line: 202,
          column: 9
        }
      },
      "48": {
        start: {
          line: 166,
          column: 30
        },
        end: {
          line: 166,
          column: 83
        }
      },
      "49": {
        start: {
          line: 168,
          column: 35
        },
        end: {
          line: 168,
          column: 63
        }
      },
      "50": {
        start: {
          line: 169,
          column: 12
        },
        end: {
          line: 169,
          column: 46
        }
      },
      "51": {
        start: {
          line: 171,
          column: 12
        },
        end: {
          line: 173,
          column: 13
        }
      },
      "52": {
        start: {
          line: 172,
          column: 16
        },
        end: {
          line: 172,
          column: 95
        }
      },
      "53": {
        start: {
          line: 175,
          column: 12
        },
        end: {
          line: 175,
          column: 47
        }
      },
      "54": {
        start: {
          line: 176,
          column: 12
        },
        end: {
          line: 186,
          column: 13
        }
      },
      "55": {
        start: {
          line: 176,
          column: 25
        },
        end: {
          line: 176,
          column: 26
        }
      },
      "56": {
        start: {
          line: 177,
          column: 34
        },
        end: {
          line: 177,
          column: 67
        }
      },
      "57": {
        start: {
          line: 178,
          column: 16
        },
        end: {
          line: 185,
          column: 19
        }
      },
      "58": {
        start: {
          line: 188,
          column: 12
        },
        end: {
          line: 188,
          column: 94
        }
      },
      "59": {
        start: {
          line: 188,
          column: 73
        },
        end: {
          line: 188,
          column: 92
        }
      },
      "60": {
        start: {
          line: 190,
          column: 12
        },
        end: {
          line: 195,
          column: 13
        }
      },
      "61": {
        start: {
          line: 191,
          column: 37
        },
        end: {
          line: 193,
          column: 59
        }
      },
      "62": {
        start: {
          line: 192,
          column: 33
        },
        end: {
          line: 192,
          column: 51
        }
      },
      "63": {
        start: {
          line: 193,
          column: 40
        },
        end: {
          line: 193,
          column: 55
        }
      },
      "64": {
        start: {
          line: 194,
          column: 16
        },
        end: {
          line: 194,
          column: 49
        }
      },
      "65": {
        start: {
          line: 196,
          column: 12
        },
        end: {
          line: 196,
          column: 35
        }
      },
      "66": {
        start: {
          line: 199,
          column: 12
        },
        end: {
          line: 199,
          column: 63
        }
      },
      "67": {
        start: {
          line: 200,
          column: 12
        },
        end: {
          line: 200,
          column: 48
        }
      },
      "68": {
        start: {
          line: 201,
          column: 12
        },
        end: {
          line: 201,
          column: 38
        }
      },
      "69": {
        start: {
          line: 208,
          column: 8
        },
        end: {
          line: 211,
          column: 12
        }
      },
      "70": {
        start: {
          line: 208,
          column: 50
        },
        end: {
          line: 211,
          column: 9
        }
      },
      "71": {
        start: {
          line: 217,
          column: 8
        },
        end: {
          line: 218,
          column: 19
        }
      },
      "72": {
        start: {
          line: 218,
          column: 12
        },
        end: {
          line: 218,
          column: 19
        }
      },
      "73": {
        start: {
          line: 220,
          column: 8
        },
        end: {
          line: 220,
          column: 84
        }
      },
      "74": {
        start: {
          line: 222,
          column: 8
        },
        end: {
          line: 222,
          column: 83
        }
      },
      "75": {
        start: {
          line: 224,
          column: 8
        },
        end: {
          line: 226,
          column: 9
        }
      },
      "76": {
        start: {
          line: 225,
          column: 12
        },
        end: {
          line: 225,
          column: 42
        }
      },
      "77": {
        start: {
          line: 228,
          column: 8
        },
        end: {
          line: 230,
          column: 9
        }
      },
      "78": {
        start: {
          line: 229,
          column: 12
        },
        end: {
          line: 229,
          column: 66
        }
      },
      "79": {
        start: {
          line: 231,
          column: 8
        },
        end: {
          line: 231,
          column: 38
        }
      },
      "80": {
        start: {
          line: 237,
          column: 8
        },
        end: {
          line: 238,
          column: 19
        }
      },
      "81": {
        start: {
          line: 238,
          column: 12
        },
        end: {
          line: 238,
          column: 19
        }
      },
      "82": {
        start: {
          line: 239,
          column: 33
        },
        end: {
          line: 239,
          column: 104
        }
      },
      "83": {
        start: {
          line: 240,
          column: 18
        },
        end: {
          line: 240,
          column: 54
        }
      },
      "84": {
        start: {
          line: 241,
          column: 8
        },
        end: {
          line: 276,
          column: 9
        }
      },
      "85": {
        start: {
          line: 241,
          column: 21
        },
        end: {
          line: 241,
          column: 22
        }
      },
      "86": {
        start: {
          line: 242,
          column: 29
        },
        end: {
          line: 242,
          column: 49
        }
      },
      "87": {
        start: {
          line: 244,
          column: 12
        },
        end: {
          line: 247,
          column: 13
        }
      },
      "88": {
        start: {
          line: 245,
          column: 16
        },
        end: {
          line: 245,
          column: 50
        }
      },
      "89": {
        start: {
          line: 246,
          column: 16
        },
        end: {
          line: 246,
          column: 25
        }
      },
      "90": {
        start: {
          line: 248,
          column: 34
        },
        end: {
          line: 248,
          column: 66
        }
      },
      "91": {
        start: {
          line: 249,
          column: 27
        },
        end: {
          line: 249,
          column: 28
        }
      },
      "92": {
        start: {
          line: 250,
          column: 12
        },
        end: {
          line: 271,
          column: 13
        }
      },
      "93": {
        start: {
          line: 253,
          column: 20
        },
        end: {
          line: 253,
          column: 73
        }
      },
      "94": {
        start: {
          line: 254,
          column: 41
        },
        end: {
          line: 254,
          column: 76
        }
      },
      "95": {
        start: {
          line: 255,
          column: 20
        },
        end: {
          line: 255,
          column: 84
        }
      },
      "96": {
        start: {
          line: 256,
          column: 20
        },
        end: {
          line: 256,
          column: 26
        }
      },
      "97": {
        start: {
          line: 259,
          column: 20
        },
        end: {
          line: 259,
          column: 73
        }
      },
      "98": {
        start: {
          line: 260,
          column: 42
        },
        end: {
          line: 260,
          column: 77
        }
      },
      "99": {
        start: {
          line: 261,
          column: 20
        },
        end: {
          line: 261,
          column: 85
        }
      },
      "100": {
        start: {
          line: 262,
          column: 20
        },
        end: {
          line: 262,
          column: 26
        }
      },
      "101": {
        start: {
          line: 265,
          column: 20
        },
        end: {
          line: 265,
          column: 73
        }
      },
      "102": {
        start: {
          line: 266,
          column: 39
        },
        end: {
          line: 266,
          column: 74
        }
      },
      "103": {
        start: {
          line: 267,
          column: 20
        },
        end: {
          line: 267,
          column: 73
        }
      },
      "104": {
        start: {
          line: 268,
          column: 40
        },
        end: {
          line: 268,
          column: 75
        }
      },
      "105": {
        start: {
          line: 269,
          column: 20
        },
        end: {
          line: 269,
          column: 68
        }
      },
      "106": {
        start: {
          line: 270,
          column: 20
        },
        end: {
          line: 270,
          column: 26
        }
      },
      "107": {
        start: {
          line: 273,
          column: 12
        },
        end: {
          line: 273,
          column: 61
        }
      },
      "108": {
        start: {
          line: 274,
          column: 12
        },
        end: {
          line: 274,
          column: 53
        }
      },
      "109": {
        start: {
          line: 275,
          column: 12
        },
        end: {
          line: 275,
          column: 89
        }
      },
      "110": {
        start: {
          line: 278,
          column: 8
        },
        end: {
          line: 278,
          column: 114
        }
      },
      "111": {
        start: {
          line: 278,
          column: 97
        },
        end: {
          line: 278,
          column: 108
        }
      },
      "112": {
        start: {
          line: 284,
          column: 8
        },
        end: {
          line: 285,
          column: 19
        }
      },
      "113": {
        start: {
          line: 285,
          column: 12
        },
        end: {
          line: 285,
          column: 19
        }
      },
      "114": {
        start: {
          line: 286,
          column: 30
        },
        end: {
          line: 286,
          column: 57
        }
      },
      "115": {
        start: {
          line: 288,
          column: 23
        },
        end: {
          line: 288,
          column: 53
        }
      },
      "116": {
        start: {
          line: 289,
          column: 8
        },
        end: {
          line: 291,
          column: 9
        }
      },
      "117": {
        start: {
          line: 290,
          column: 12
        },
        end: {
          line: 290,
          column: 116
        }
      },
      "118": {
        start: {
          line: 293,
          column: 29
        },
        end: {
          line: 293,
          column: 31
        }
      },
      "119": {
        start: {
          line: 294,
          column: 8
        },
        end: {
          line: 312,
          column: 9
        }
      },
      "120": {
        start: {
          line: 294,
          column: 21
        },
        end: {
          line: 294,
          column: 22
        }
      },
      "121": {
        start: {
          line: 295,
          column: 29
        },
        end: {
          line: 295,
          column: 49
        }
      },
      "122": {
        start: {
          line: 296,
          column: 33
        },
        end: {
          line: 296,
          column: 82
        }
      },
      "123": {
        start: {
          line: 297,
          column: 12
        },
        end: {
          line: 311,
          column: 13
        }
      },
      "124": {
        start: {
          line: 299,
          column: 16
        },
        end: {
          line: 299,
          column: 57
        }
      },
      "125": {
        start: {
          line: 301,
          column: 17
        },
        end: {
          line: 311,
          column: 13
        }
      },
      "126": {
        start: {
          line: 303,
          column: 33
        },
        end: {
          line: 303,
          column: 75
        }
      },
      "127": {
        start: {
          line: 305,
          column: 28
        },
        end: {
          line: 305,
          column: 109
        }
      },
      "128": {
        start: {
          line: 306,
          column: 28
        },
        end: {
          line: 306,
          column: 108
        }
      },
      "129": {
        start: {
          line: 307,
          column: 16
        },
        end: {
          line: 307,
          column: 83
        }
      },
      "130": {
        start: {
          line: 310,
          column: 16
        },
        end: {
          line: 310,
          column: 57
        }
      },
      "131": {
        start: {
          line: 314,
          column: 28
        },
        end: {
          line: 318,
          column: 9
        }
      },
      "132": {
        start: {
          line: 320,
          column: 8
        },
        end: {
          line: 320,
          column: 98
        }
      },
      "133": {
        start: {
          line: 322,
          column: 8
        },
        end: {
          line: 322,
          column: 49
        }
      },
      "134": {
        start: {
          line: 323,
          column: 8
        },
        end: {
          line: 323,
          column: 46
        }
      },
      "135": {
        start: {
          line: 324,
          column: 8
        },
        end: {
          line: 324,
          column: 62
        }
      },
      "136": {
        start: {
          line: 330,
          column: 8
        },
        end: {
          line: 331,
          column: 22
        }
      },
      "137": {
        start: {
          line: 331,
          column: 12
        },
        end: {
          line: 331,
          column: 22
        }
      },
      "138": {
        start: {
          line: 332,
          column: 25
        },
        end: {
          line: 332,
          column: 51
        }
      },
      "139": {
        start: {
          line: 333,
          column: 30
        },
        end: {
          line: 333,
          column: 56
        }
      },
      "140": {
        start: {
          line: 334,
          column: 8
        },
        end: {
          line: 389,
          column: 9
        }
      },
      "141": {
        start: {
          line: 337,
          column: 16
        },
        end: {
          line: 339,
          column: 17
        }
      },
      "142": {
        start: {
          line: 337,
          column: 29
        },
        end: {
          line: 337,
          column: 30
        }
      },
      "143": {
        start: {
          line: 338,
          column: 20
        },
        end: {
          line: 338,
          column: 51
        }
      },
      "144": {
        start: {
          line: 340,
          column: 16
        },
        end: {
          line: 340,
          column: 22
        }
      },
      "145": {
        start: {
          line: 343,
          column: 16
        },
        end: {
          line: 346,
          column: 17
        }
      },
      "146": {
        start: {
          line: 343,
          column: 29
        },
        end: {
          line: 343,
          column: 30
        }
      },
      "147": {
        start: {
          line: 344,
          column: 20
        },
        end: {
          line: 344,
          column: 134
        }
      },
      "148": {
        start: {
          line: 345,
          column: 20
        },
        end: {
          line: 345,
          column: 69
        }
      },
      "149": {
        start: {
          line: 347,
          column: 16
        },
        end: {
          line: 347,
          column: 22
        }
      },
      "150": {
        start: {
          line: 350,
          column: 30
        },
        end: {
          line: 350,
          column: 55
        }
      },
      "151": {
        start: {
          line: 351,
          column: 30
        },
        end: {
          line: 351,
          column: 55
        }
      },
      "152": {
        start: {
          line: 352,
          column: 32
        },
        end: {
          line: 352,
          column: 59
        }
      },
      "153": {
        start: {
          line: 353,
          column: 26
        },
        end: {
          line: 353,
          column: 57
        }
      },
      "154": {
        start: {
          line: 354,
          column: 16
        },
        end: {
          line: 365,
          column: 17
        }
      },
      "155": {
        start: {
          line: 354,
          column: 29
        },
        end: {
          line: 354,
          column: 30
        }
      },
      "156": {
        start: {
          line: 356,
          column: 20
        },
        end: {
          line: 356,
          column: 112
        }
      },
      "157": {
        start: {
          line: 358,
          column: 20
        },
        end: {
          line: 358,
          column: 126
        }
      },
      "158": {
        start: {
          line: 360,
          column: 33
        },
        end: {
          line: 360,
          column: 86
        }
      },
      "159": {
        start: {
          line: 362,
          column: 33
        },
        end: {
          line: 362,
          column: 86
        }
      },
      "160": {
        start: {
          line: 364,
          column: 20
        },
        end: {
          line: 364,
          column: 74
        }
      },
      "161": {
        start: {
          line: 366,
          column: 16
        },
        end: {
          line: 366,
          column: 22
        }
      },
      "162": {
        start: {
          line: 369,
          column: 30
        },
        end: {
          line: 369,
          column: 62
        }
      },
      "163": {
        start: {
          line: 370,
          column: 28
        },
        end: {
          line: 370,
          column: 58
        }
      },
      "164": {
        start: {
          line: 371,
          column: 16
        },
        end: {
          line: 374,
          column: 17
        }
      },
      "165": {
        start: {
          line: 371,
          column: 29
        },
        end: {
          line: 371,
          column: 30
        }
      },
      "166": {
        start: {
          line: 372,
          column: 20
        },
        end: {
          line: 372,
          column: 132
        }
      },
      "167": {
        start: {
          line: 373,
          column: 20
        },
        end: {
          line: 373,
          column: 102
        }
      },
      "168": {
        start: {
          line: 375,
          column: 16
        },
        end: {
          line: 375,
          column: 22
        }
      },
      "169": {
        start: {
          line: 378,
          column: 35
        },
        end: {
          line: 378,
          column: 62
        }
      },
      "170": {
        start: {
          line: 379,
          column: 16
        },
        end: {
          line: 382,
          column: 17
        }
      },
      "171": {
        start: {
          line: 379,
          column: 29
        },
        end: {
          line: 379,
          column: 30
        }
      },
      "172": {
        start: {
          line: 380,
          column: 20
        },
        end: {
          line: 380,
          column: 79
        }
      },
      "173": {
        start: {
          line: 381,
          column: 20
        },
        end: {
          line: 381,
          column: 109
        }
      },
      "174": {
        start: {
          line: 383,
          column: 16
        },
        end: {
          line: 383,
          column: 22
        }
      },
      "175": {
        start: {
          line: 386,
          column: 16
        },
        end: {
          line: 388,
          column: 17
        }
      },
      "176": {
        start: {
          line: 386,
          column: 29
        },
        end: {
          line: 386,
          column: 30
        }
      },
      "177": {
        start: {
          line: 387,
          column: 20
        },
        end: {
          line: 387,
          column: 51
        }
      },
      "178": {
        start: {
          line: 390,
          column: 8
        },
        end: {
          line: 390,
          column: 29
        }
      },
      "179": {
        start: {
          line: 396,
          column: 8
        },
        end: {
          line: 397,
          column: 48
        }
      },
      "180": {
        start: {
          line: 397,
          column: 12
        },
        end: {
          line: 397,
          column: 48
        }
      },
      "181": {
        start: {
          line: 398,
          column: 8
        },
        end: {
          line: 407,
          column: 9
        }
      },
      "182": {
        start: {
          line: 400,
          column: 16
        },
        end: {
          line: 400,
          column: 109
        }
      },
      "183": {
        start: {
          line: 402,
          column: 16
        },
        end: {
          line: 402,
          column: 108
        }
      },
      "184": {
        start: {
          line: 404,
          column: 16
        },
        end: {
          line: 404,
          column: 116
        }
      },
      "185": {
        start: {
          line: 406,
          column: 16
        },
        end: {
          line: 406,
          column: 52
        }
      },
      "186": {
        start: {
          line: 413,
          column: 8
        },
        end: {
          line: 414,
          column: 48
        }
      },
      "187": {
        start: {
          line: 414,
          column: 12
        },
        end: {
          line: 414,
          column: 48
        }
      },
      "188": {
        start: {
          line: 415,
          column: 19
        },
        end: {
          line: 415,
          column: 23
        }
      },
      "189": {
        start: {
          line: 416,
          column: 20
        },
        end: {
          line: 416,
          column: 48
        }
      },
      "190": {
        start: {
          line: 417,
          column: 29
        },
        end: {
          line: 417,
          column: 31
        }
      },
      "191": {
        start: {
          line: 418,
          column: 29
        },
        end: {
          line: 418,
          column: 60
        }
      },
      "192": {
        start: {
          line: 419,
          column: 37
        },
        end: {
          line: 419,
          column: 116
        }
      },
      "193": {
        start: {
          line: 419,
          column: 86
        },
        end: {
          line: 419,
          column: 112
        }
      },
      "194": {
        start: {
          line: 420,
          column: 8
        },
        end: {
          line: 430,
          column: 9
        }
      },
      "195": {
        start: {
          line: 420,
          column: 21
        },
        end: {
          line: 420,
          column: 22
        }
      },
      "196": {
        start: {
          line: 422,
          column: 34
        },
        end: {
          line: 422,
          column: 89
        }
      },
      "197": {
        start: {
          line: 423,
          column: 30
        },
        end: {
          line: 423,
          column: 62
        }
      },
      "198": {
        start: {
          line: 424,
          column: 12
        },
        end: {
          line: 424,
          column: 35
        }
      },
      "199": {
        start: {
          line: 426,
          column: 12
        },
        end: {
          line: 428,
          column: 13
        }
      },
      "200": {
        start: {
          line: 427,
          column: 16
        },
        end: {
          line: 427,
          column: 29
        }
      },
      "201": {
        start: {
          line: 429,
          column: 12
        },
        end: {
          line: 429,
          column: 25
        }
      },
      "202": {
        start: {
          line: 431,
          column: 8
        },
        end: {
          line: 431,
          column: 21
        }
      },
      "203": {
        start: {
          line: 438,
          column: 8
        },
        end: {
          line: 438,
          column: 101
        }
      },
      "204": {
        start: {
          line: 444,
          column: 20
        },
        end: {
          line: 444,
          column: 42
        }
      },
      "205": {
        start: {
          line: 445,
          column: 23
        },
        end: {
          line: 445,
          column: 30
        }
      },
      "206": {
        start: {
          line: 446,
          column: 16
        },
        end: {
          line: 446,
          column: 17
        }
      },
      "207": {
        start: {
          line: 447,
          column: 16
        },
        end: {
          line: 447,
          column: 48
        }
      },
      "208": {
        start: {
          line: 448,
          column: 20
        },
        end: {
          line: 448,
          column: 24
        }
      },
      "209": {
        start: {
          line: 450,
          column: 17
        },
        end: {
          line: 450,
          column: 37
        }
      },
      "210": {
        start: {
          line: 451,
          column: 17
        },
        end: {
          line: 451,
          column: 43
        }
      },
      "211": {
        start: {
          line: 452,
          column: 17
        },
        end: {
          line: 452,
          column: 83
        }
      },
      "212": {
        start: {
          line: 453,
          column: 17
        },
        end: {
          line: 453,
          column: 83
        }
      },
      "213": {
        start: {
          line: 454,
          column: 8
        },
        end: {
          line: 469,
          column: 9
        }
      },
      "214": {
        start: {
          line: 455,
          column: 12
        },
        end: {
          line: 468,
          column: 13
        }
      },
      "215": {
        start: {
          line: 456,
          column: 16
        },
        end: {
          line: 456,
          column: 23
        }
      },
      "216": {
        start: {
          line: 457,
          column: 16
        },
        end: {
          line: 457,
          column: 24
        }
      },
      "217": {
        start: {
          line: 458,
          column: 16
        },
        end: {
          line: 458,
          column: 24
        }
      },
      "218": {
        start: {
          line: 459,
          column: 16
        },
        end: {
          line: 459,
          column: 42
        }
      },
      "219": {
        start: {
          line: 460,
          column: 16
        },
        end: {
          line: 460,
          column: 88
        }
      },
      "220": {
        start: {
          line: 463,
          column: 16
        },
        end: {
          line: 463,
          column: 23
        }
      },
      "221": {
        start: {
          line: 464,
          column: 16
        },
        end: {
          line: 464,
          column: 24
        }
      },
      "222": {
        start: {
          line: 465,
          column: 16
        },
        end: {
          line: 465,
          column: 24
        }
      },
      "223": {
        start: {
          line: 466,
          column: 16
        },
        end: {
          line: 466,
          column: 48
        }
      },
      "224": {
        start: {
          line: 467,
          column: 16
        },
        end: {
          line: 467,
          column: 88
        }
      },
      "225": {
        start: {
          line: 470,
          column: 8
        },
        end: {
          line: 470,
          column: 27
        }
      },
      "226": {
        start: {
          line: 476,
          column: 8
        },
        end: {
          line: 477,
          column: 22
        }
      },
      "227": {
        start: {
          line: 477,
          column: 12
        },
        end: {
          line: 477,
          column: 22
        }
      },
      "228": {
        start: {
          line: 478,
          column: 8
        },
        end: {
          line: 495,
          column: 11
        }
      },
      "229": {
        start: {
          line: 479,
          column: 33
        },
        end: {
          line: 479,
          column: 82
        }
      },
      "230": {
        start: {
          line: 480,
          column: 12
        },
        end: {
          line: 494,
          column: 13
        }
      },
      "231": {
        start: {
          line: 481,
          column: 33
        },
        end: {
          line: 481,
          column: 72
        }
      },
      "232": {
        start: {
          line: 482,
          column: 28
        },
        end: {
          line: 482,
          column: 109
        }
      },
      "233": {
        start: {
          line: 483,
          column: 28
        },
        end: {
          line: 483,
          column: 108
        }
      },
      "234": {
        start: {
          line: 484,
          column: 16
        },
        end: {
          line: 487,
          column: 18
        }
      },
      "235": {
        start: {
          line: 490,
          column: 16
        },
        end: {
          line: 493,
          column: 18
        }
      },
      "236": {
        start: {
          line: 501,
          column: 30
        },
        end: {
          line: 501,
          column: 85
        }
      },
      "237": {
        start: {
          line: 502,
          column: 8
        },
        end: {
          line: 502,
          column: 31
        }
      },
      "238": {
        start: {
          line: 503,
          column: 8
        },
        end: {
          line: 503,
          column: 48
        }
      },
      "239": {
        start: {
          line: 509,
          column: 8
        },
        end: {
          line: 510,
          column: 19
        }
      },
      "240": {
        start: {
          line: 510,
          column: 12
        },
        end: {
          line: 510,
          column: 19
        }
      },
      "241": {
        start: {
          line: 512,
          column: 35
        },
        end: {
          line: 512,
          column: 37
        }
      },
      "242": {
        start: {
          line: 513,
          column: 8
        },
        end: {
          line: 523,
          column: 9
        }
      },
      "243": {
        start: {
          line: 514,
          column: 33
        },
        end: {
          line: 514,
          column: 82
        }
      },
      "244": {
        start: {
          line: 515,
          column: 12
        },
        end: {
          line: 522,
          column: 13
        }
      },
      "245": {
        start: {
          line: 516,
          column: 28
        },
        end: {
          line: 516,
          column: 109
        }
      },
      "246": {
        start: {
          line: 517,
          column: 28
        },
        end: {
          line: 517,
          column: 108
        }
      },
      "247": {
        start: {
          line: 518,
          column: 16
        },
        end: {
          line: 518,
          column: 93
        }
      },
      "248": {
        start: {
          line: 521,
          column: 16
        },
        end: {
          line: 521,
          column: 63
        }
      },
      "249": {
        start: {
          line: 524,
          column: 8
        },
        end: {
          line: 524,
          column: 66
        }
      },
      "250": {
        start: {
          line: 530,
          column: 8
        },
        end: {
          line: 531,
          column: 19
        }
      },
      "251": {
        start: {
          line: 531,
          column: 12
        },
        end: {
          line: 531,
          column: 19
        }
      },
      "252": {
        start: {
          line: 533,
          column: 8
        },
        end: {
          line: 540,
          column: 9
        }
      },
      "253": {
        start: {
          line: 535,
          column: 12
        },
        end: {
          line: 535,
          column: 125
        }
      },
      "254": {
        start: {
          line: 537,
          column: 13
        },
        end: {
          line: 540,
          column: 9
        }
      },
      "255": {
        start: {
          line: 539,
          column: 12
        },
        end: {
          line: 539,
          column: 125
        }
      },
      "256": {
        start: {
          line: 546,
          column: 8
        },
        end: {
          line: 547,
          column: 24
        }
      },
      "257": {
        start: {
          line: 547,
          column: 12
        },
        end: {
          line: 547,
          column: 24
        }
      },
      "258": {
        start: {
          line: 549,
          column: 8
        },
        end: {
          line: 551,
          column: 9
        }
      },
      "259": {
        start: {
          line: 550,
          column: 12
        },
        end: {
          line: 550,
          column: 24
        }
      },
      "260": {
        start: {
          line: 553,
          column: 8
        },
        end: {
          line: 555,
          column: 9
        }
      },
      "261": {
        start: {
          line: 554,
          column: 12
        },
        end: {
          line: 554,
          column: 24
        }
      },
      "262": {
        start: {
          line: 557,
          column: 8
        },
        end: {
          line: 563,
          column: 9
        }
      },
      "263": {
        start: {
          line: 558,
          column: 34
        },
        end: {
          line: 558,
          column: 57
        }
      },
      "264": {
        start: {
          line: 559,
          column: 40
        },
        end: {
          line: 559,
          column: 122
        }
      },
      "265": {
        start: {
          line: 560,
          column: 12
        },
        end: {
          line: 562,
          column: 13
        }
      },
      "266": {
        start: {
          line: 561,
          column: 16
        },
        end: {
          line: 561,
          column: 28
        }
      },
      "267": {
        start: {
          line: 564,
          column: 8
        },
        end: {
          line: 564,
          column: 21
        }
      },
      "268": {
        start: {
          line: 570,
          column: 8
        },
        end: {
          line: 571,
          column: 19
        }
      },
      "269": {
        start: {
          line: 571,
          column: 12
        },
        end: {
          line: 571,
          column: 19
        }
      },
      "270": {
        start: {
          line: 572,
          column: 24
        },
        end: {
          line: 580,
          column: 9
        }
      },
      "271": {
        start: {
          line: 581,
          column: 8
        },
        end: {
          line: 581,
          column: 35
        }
      },
      "272": {
        start: {
          line: 587,
          column: 30
        },
        end: {
          line: 587,
          column: 59
        }
      },
      "273": {
        start: {
          line: 588,
          column: 27
        },
        end: {
          line: 603,
          column: 9
        }
      },
      "274": {
        start: {
          line: 593,
          column: 54
        },
        end: {
          line: 593,
          column: 67
        }
      },
      "275": {
        start: {
          line: 594,
          column: 57
        },
        end: {
          line: 594,
          column: 73
        }
      },
      "276": {
        start: {
          line: 595,
          column: 52
        },
        end: {
          line: 595,
          column: 63
        }
      },
      "277": {
        start: {
          line: 596,
          column: 62
        },
        end: {
          line: 596,
          column: 84
        }
      },
      "278": {
        start: {
          line: 604,
          column: 36
        },
        end: {
          line: 609,
          column: 9
        }
      },
      "279": {
        start: {
          line: 610,
          column: 8
        },
        end: {
          line: 620,
          column: 10
        }
      },
      "280": {
        start: {
          line: 624,
          column: 8
        },
        end: {
          line: 624,
          column: 81
        }
      },
      "281": {
        start: {
          line: 627,
          column: 20
        },
        end: {
          line: 627,
          column: 24
        }
      },
      "282": {
        start: {
          line: 628,
          column: 8
        },
        end: {
          line: 631,
          column: 10
        }
      },
      "283": {
        start: {
          line: 629,
          column: 12
        },
        end: {
          line: 629,
          column: 52
        }
      },
      "284": {
        start: {
          line: 630,
          column: 12
        },
        end: {
          line: 630,
          column: 34
        }
      },
      "285": {
        start: {
          line: 634,
          column: 0
        },
        end: {
          line: 634,
          column: 42
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 22,
            column: 4
          },
          end: {
            line: 22,
            column: 5
          }
        },
        loc: {
          start: {
            line: 22,
            column: 28
          },
          end: {
            line: 57,
            column: 5
          }
        },
        line: 22
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 61,
            column: 4
          },
          end: {
            line: 61,
            column: 5
          }
        },
        loc: {
          start: {
            line: 61,
            column: 68
          },
          end: {
            line: 85,
            column: 5
          }
        },
        line: 61
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 89,
            column: 4
          },
          end: {
            line: 89,
            column: 5
          }
        },
        loc: {
          start: {
            line: 89,
            column: 29
          },
          end: {
            line: 95,
            column: 5
          }
        },
        line: 89
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 91,
            column: 60
          },
          end: {
            line: 91,
            column: 61
          }
        },
        loc: {
          start: {
            line: 91,
            column: 65
          },
          end: {
            line: 91,
            column: 112
          }
        },
        line: 91
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 99,
            column: 4
          },
          end: {
            line: 99,
            column: 5
          }
        },
        loc: {
          start: {
            line: 99,
            column: 33
          },
          end: {
            line: 105,
            column: 5
          }
        },
        line: 99
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 109,
            column: 4
          },
          end: {
            line: 109,
            column: 5
          }
        },
        loc: {
          start: {
            line: 109,
            column: 81
          },
          end: {
            line: 130,
            column: 5
          }
        },
        line: 109
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 134,
            column: 4
          },
          end: {
            line: 134,
            column: 5
          }
        },
        loc: {
          start: {
            line: 134,
            column: 34
          },
          end: {
            line: 159,
            column: 5
          }
        },
        line: 134
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 163,
            column: 4
          },
          end: {
            line: 163,
            column: 5
          }
        },
        loc: {
          start: {
            line: 163,
            column: 86
          },
          end: {
            line: 203,
            column: 5
          }
        },
        line: 163
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 188,
            column: 68
          },
          end: {
            line: 188,
            column: 69
          }
        },
        loc: {
          start: {
            line: 188,
            column: 73
          },
          end: {
            line: 188,
            column: 92
          }
        },
        line: 188
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 192,
            column: 28
          },
          end: {
            line: 192,
            column: 29
          }
        },
        loc: {
          start: {
            line: 192,
            column: 33
          },
          end: {
            line: 192,
            column: 51
          }
        },
        line: 192
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 193,
            column: 28
          },
          end: {
            line: 193,
            column: 29
          }
        },
        loc: {
          start: {
            line: 193,
            column: 40
          },
          end: {
            line: 193,
            column: 55
          }
        },
        line: 193
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 207,
            column: 4
          },
          end: {
            line: 207,
            column: 5
          }
        },
        loc: {
          start: {
            line: 207,
            column: 53
          },
          end: {
            line: 212,
            column: 5
          }
        },
        line: 207
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 208,
            column: 37
          },
          end: {
            line: 208,
            column: 38
          }
        },
        loc: {
          start: {
            line: 208,
            column: 50
          },
          end: {
            line: 211,
            column: 9
          }
        },
        line: 208
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 216,
            column: 4
          },
          end: {
            line: 216,
            column: 5
          }
        },
        loc: {
          start: {
            line: 216,
            column: 76
          },
          end: {
            line: 232,
            column: 5
          }
        },
        line: 216
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 236,
            column: 4
          },
          end: {
            line: 236,
            column: 5
          }
        },
        loc: {
          start: {
            line: 236,
            column: 75
          },
          end: {
            line: 279,
            column: 5
          }
        },
        line: 236
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 278,
            column: 85
          },
          end: {
            line: 278,
            column: 86
          }
        },
        loc: {
          start: {
            line: 278,
            column: 97
          },
          end: {
            line: 278,
            column: 108
          }
        },
        line: 278
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 283,
            column: 4
          },
          end: {
            line: 283,
            column: 5
          }
        },
        loc: {
          start: {
            line: 283,
            column: 74
          },
          end: {
            line: 325,
            column: 5
          }
        },
        line: 283
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 329,
            column: 4
          },
          end: {
            line: 329,
            column: 5
          }
        },
        loc: {
          start: {
            line: 329,
            column: 27
          },
          end: {
            line: 391,
            column: 5
          }
        },
        line: 329
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 395,
            column: 4
          },
          end: {
            line: 395,
            column: 5
          }
        },
        loc: {
          start: {
            line: 395,
            column: 92
          },
          end: {
            line: 408,
            column: 5
          }
        },
        line: 395
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 412,
            column: 4
          },
          end: {
            line: 412,
            column: 5
          }
        },
        loc: {
          start: {
            line: 412,
            column: 91
          },
          end: {
            line: 432,
            column: 5
          }
        },
        line: 412
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 419,
            column: 71
          },
          end: {
            line: 419,
            column: 72
          }
        },
        loc: {
          start: {
            line: 419,
            column: 86
          },
          end: {
            line: 419,
            column: 112
          }
        },
        line: 419
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 436,
            column: 4
          },
          end: {
            line: 436,
            column: 5
          }
        },
        loc: {
          start: {
            line: 436,
            column: 90
          },
          end: {
            line: 439,
            column: 5
          }
        },
        line: 436
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 443,
            column: 4
          },
          end: {
            line: 443,
            column: 5
          }
        },
        loc: {
          start: {
            line: 443,
            column: 98
          },
          end: {
            line: 471,
            column: 5
          }
        },
        line: 443
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 475,
            column: 4
          },
          end: {
            line: 475,
            column: 5
          }
        },
        loc: {
          start: {
            line: 475,
            column: 55
          },
          end: {
            line: 496,
            column: 5
          }
        },
        line: 475
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 478,
            column: 37
          },
          end: {
            line: 478,
            column: 38
          }
        },
        loc: {
          start: {
            line: 478,
            column: 54
          },
          end: {
            line: 495,
            column: 9
          }
        },
        line: 478
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 500,
            column: 4
          },
          end: {
            line: 500,
            column: 5
          }
        },
        loc: {
          start: {
            line: 500,
            column: 69
          },
          end: {
            line: 504,
            column: 5
          }
        },
        line: 500
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 508,
            column: 4
          },
          end: {
            line: 508,
            column: 5
          }
        },
        loc: {
          start: {
            line: 508,
            column: 29
          },
          end: {
            line: 525,
            column: 5
          }
        },
        line: 508
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 529,
            column: 4
          },
          end: {
            line: 529,
            column: 5
          }
        },
        loc: {
          start: {
            line: 529,
            column: 24
          },
          end: {
            line: 541,
            column: 5
          }
        },
        line: 529
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 545,
            column: 4
          },
          end: {
            line: 545,
            column: 5
          }
        },
        loc: {
          start: {
            line: 545,
            column: 22
          },
          end: {
            line: 565,
            column: 5
          }
        },
        line: 545
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 569,
            column: 4
          },
          end: {
            line: 569,
            column: 5
          }
        },
        loc: {
          start: {
            line: 569,
            column: 20
          },
          end: {
            line: 582,
            column: 5
          }
        },
        line: 569
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 586,
            column: 4
          },
          end: {
            line: 586,
            column: 5
          }
        },
        loc: {
          start: {
            line: 586,
            column: 49
          },
          end: {
            line: 621,
            column: 5
          }
        },
        line: 586
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 593,
            column: 49
          },
          end: {
            line: 593,
            column: 50
          }
        },
        loc: {
          start: {
            line: 593,
            column: 54
          },
          end: {
            line: 593,
            column: 67
          }
        },
        line: 593
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 594,
            column: 52
          },
          end: {
            line: 594,
            column: 53
          }
        },
        loc: {
          start: {
            line: 594,
            column: 57
          },
          end: {
            line: 594,
            column: 73
          }
        },
        line: 594
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 595,
            column: 47
          },
          end: {
            line: 595,
            column: 48
          }
        },
        loc: {
          start: {
            line: 595,
            column: 52
          },
          end: {
            line: 595,
            column: 63
          }
        },
        line: 595
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 596,
            column: 57
          },
          end: {
            line: 596,
            column: 58
          }
        },
        loc: {
          start: {
            line: 596,
            column: 62
          },
          end: {
            line: 596,
            column: 84
          }
        },
        line: 596
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 623,
            column: 4
          },
          end: {
            line: 623,
            column: 5
          }
        },
        loc: {
          start: {
            line: 623,
            column: 25
          },
          end: {
            line: 625,
            column: 5
          }
        },
        line: 623
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 626,
            column: 4
          },
          end: {
            line: 626,
            column: 5
          }
        },
        loc: {
          start: {
            line: 626,
            column: 29
          },
          end: {
            line: 632,
            column: 5
          }
        },
        line: 626
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 628,
            column: 15
          },
          end: {
            line: 628,
            column: 16
          }
        },
        loc: {
          start: {
            line: 628,
            column: 21
          },
          end: {
            line: 631,
            column: 9
          }
        },
        line: 628
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 51,
            column: 8
          },
          end: {
            line: 56,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 51,
            column: 8
          },
          end: {
            line: 56,
            column: 9
          }
        }, {
          start: {
            line: 54,
            column: 13
          },
          end: {
            line: 56,
            column: 9
          }
        }],
        line: 51
      },
      "1": {
        loc: {
          start: {
            line: 74,
            column: 16
          },
          end: {
            line: 76,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 74,
            column: 16
          },
          end: {
            line: 76,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 74
      },
      "2": {
        loc: {
          start: {
            line: 91,
            column: 65
          },
          end: {
            line: 91,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 91,
            column: 65
          },
          end: {
            line: 91,
            column: 81
          }
        }, {
          start: {
            line: 91,
            column: 85
          },
          end: {
            line: 91,
            column: 112
          }
        }],
        line: 91
      },
      "3": {
        loc: {
          start: {
            line: 92,
            column: 8
          },
          end: {
            line: 94,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 92,
            column: 8
          },
          end: {
            line: 94,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 92
      },
      "4": {
        loc: {
          start: {
            line: 137,
            column: 12
          },
          end: {
            line: 147,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 137,
            column: 12
          },
          end: {
            line: 147,
            column: 13
          }
        }, {
          start: {
            line: 142,
            column: 17
          },
          end: {
            line: 147,
            column: 13
          }
        }],
        line: 137
      },
      "5": {
        loc: {
          start: {
            line: 137,
            column: 16
          },
          end: {
            line: 137,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 137,
            column: 16
          },
          end: {
            line: 137,
            column: 39
          }
        }, {
          start: {
            line: 137,
            column: 43
          },
          end: {
            line: 137,
            column: 77
          }
        }],
        line: 137
      },
      "6": {
        loc: {
          start: {
            line: 144,
            column: 28
          },
          end: {
            line: 144,
            column: 101
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 144,
            column: 74
          },
          end: {
            line: 144,
            column: 97
          }
        }, {
          start: {
            line: 144,
            column: 100
          },
          end: {
            line: 144,
            column: 101
          }
        }],
        line: 144
      },
      "7": {
        loc: {
          start: {
            line: 145,
            column: 28
          },
          end: {
            line: 145,
            column: 101
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 145,
            column: 74
          },
          end: {
            line: 145,
            column: 97
          }
        }, {
          start: {
            line: 145,
            column: 100
          },
          end: {
            line: 145,
            column: 101
          }
        }],
        line: 145
      },
      "8": {
        loc: {
          start: {
            line: 171,
            column: 12
          },
          end: {
            line: 173,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 171,
            column: 12
          },
          end: {
            line: 173,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 171
      },
      "9": {
        loc: {
          start: {
            line: 180,
            column: 35
          },
          end: {
            line: 180,
            column: 76
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 180,
            column: 51
          },
          end: {
            line: 180,
            column: 63
          }
        }, {
          start: {
            line: 180,
            column: 66
          },
          end: {
            line: 180,
            column: 76
          }
        }],
        line: 180
      },
      "10": {
        loc: {
          start: {
            line: 183,
            column: 30
          },
          end: {
            line: 183,
            column: 63
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 183,
            column: 46
          },
          end: {
            line: 183,
            column: 53
          }
        }, {
          start: {
            line: 183,
            column: 56
          },
          end: {
            line: 183,
            column: 63
          }
        }],
        line: 183
      },
      "11": {
        loc: {
          start: {
            line: 184,
            column: 29
          },
          end: {
            line: 184,
            column: 95
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 184,
            column: 45
          },
          end: {
            line: 184,
            column: 91
          }
        }, {
          start: {
            line: 184,
            column: 94
          },
          end: {
            line: 184,
            column: 95
          }
        }],
        line: 184
      },
      "12": {
        loc: {
          start: {
            line: 190,
            column: 12
          },
          end: {
            line: 195,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 190,
            column: 12
          },
          end: {
            line: 195,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 190
      },
      "13": {
        loc: {
          start: {
            line: 190,
            column: 16
          },
          end: {
            line: 190,
            column: 86
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 190,
            column: 16
          },
          end: {
            line: 190,
            column: 34
          }
        }, {
          start: {
            line: 190,
            column: 38
          },
          end: {
            line: 190,
            column: 86
          }
        }],
        line: 190
      },
      "14": {
        loc: {
          start: {
            line: 217,
            column: 8
          },
          end: {
            line: 218,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 217,
            column: 8
          },
          end: {
            line: 218,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 217
      },
      "15": {
        loc: {
          start: {
            line: 224,
            column: 8
          },
          end: {
            line: 226,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 224,
            column: 8
          },
          end: {
            line: 226,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 224
      },
      "16": {
        loc: {
          start: {
            line: 228,
            column: 8
          },
          end: {
            line: 230,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 228,
            column: 8
          },
          end: {
            line: 230,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 228
      },
      "17": {
        loc: {
          start: {
            line: 237,
            column: 8
          },
          end: {
            line: 238,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 237,
            column: 8
          },
          end: {
            line: 238,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 237
      },
      "18": {
        loc: {
          start: {
            line: 244,
            column: 12
          },
          end: {
            line: 247,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 244,
            column: 12
          },
          end: {
            line: 247,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 244
      },
      "19": {
        loc: {
          start: {
            line: 244,
            column: 16
          },
          end: {
            line: 244,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 244,
            column: 16
          },
          end: {
            line: 244,
            column: 39
          }
        }, {
          start: {
            line: 244,
            column: 43
          },
          end: {
            line: 244,
            column: 77
          }
        }],
        line: 244
      },
      "20": {
        loc: {
          start: {
            line: 250,
            column: 12
          },
          end: {
            line: 271,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 251,
            column: 16
          },
          end: {
            line: 256,
            column: 26
          }
        }, {
          start: {
            line: 257,
            column: 16
          },
          end: {
            line: 262,
            column: 26
          }
        }, {
          start: {
            line: 263,
            column: 16
          },
          end: {
            line: 270,
            column: 26
          }
        }],
        line: 250
      },
      "21": {
        loc: {
          start: {
            line: 275,
            column: 36
          },
          end: {
            line: 275,
            column: 88
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 275,
            column: 83
          },
          end: {
            line: 275,
            column: 84
          }
        }, {
          start: {
            line: 275,
            column: 87
          },
          end: {
            line: 275,
            column: 88
          }
        }],
        line: 275
      },
      "22": {
        loc: {
          start: {
            line: 284,
            column: 8
          },
          end: {
            line: 285,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 284,
            column: 8
          },
          end: {
            line: 285,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 284
      },
      "23": {
        loc: {
          start: {
            line: 289,
            column: 8
          },
          end: {
            line: 291,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 289,
            column: 8
          },
          end: {
            line: 291,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 289
      },
      "24": {
        loc: {
          start: {
            line: 297,
            column: 12
          },
          end: {
            line: 311,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 297,
            column: 12
          },
          end: {
            line: 311,
            column: 13
          }
        }, {
          start: {
            line: 301,
            column: 17
          },
          end: {
            line: 311,
            column: 13
          }
        }],
        line: 297
      },
      "25": {
        loc: {
          start: {
            line: 297,
            column: 16
          },
          end: {
            line: 297,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 297,
            column: 16
          },
          end: {
            line: 297,
            column: 39
          }
        }, {
          start: {
            line: 297,
            column: 43
          },
          end: {
            line: 297,
            column: 77
          }
        }],
        line: 297
      },
      "26": {
        loc: {
          start: {
            line: 301,
            column: 17
          },
          end: {
            line: 311,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 301,
            column: 17
          },
          end: {
            line: 311,
            column: 13
          }
        }, {
          start: {
            line: 309,
            column: 17
          },
          end: {
            line: 311,
            column: 13
          }
        }],
        line: 301
      },
      "27": {
        loc: {
          start: {
            line: 305,
            column: 28
          },
          end: {
            line: 305,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 305,
            column: 74
          },
          end: {
            line: 305,
            column: 97
          }
        }, {
          start: {
            line: 305,
            column: 100
          },
          end: {
            line: 305,
            column: 109
          }
        }],
        line: 305
      },
      "28": {
        loc: {
          start: {
            line: 306,
            column: 28
          },
          end: {
            line: 306,
            column: 108
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 306,
            column: 74
          },
          end: {
            line: 306,
            column: 97
          }
        }, {
          start: {
            line: 306,
            column: 100
          },
          end: {
            line: 306,
            column: 108
          }
        }],
        line: 306
      },
      "29": {
        loc: {
          start: {
            line: 330,
            column: 8
          },
          end: {
            line: 331,
            column: 22
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 330,
            column: 8
          },
          end: {
            line: 331,
            column: 22
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 330
      },
      "30": {
        loc: {
          start: {
            line: 334,
            column: 8
          },
          end: {
            line: 389,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 335,
            column: 12
          },
          end: {
            line: 340,
            column: 22
          }
        }, {
          start: {
            line: 341,
            column: 12
          },
          end: {
            line: 347,
            column: 22
          }
        }, {
          start: {
            line: 348,
            column: 12
          },
          end: {
            line: 366,
            column: 22
          }
        }, {
          start: {
            line: 367,
            column: 12
          },
          end: {
            line: 375,
            column: 22
          }
        }, {
          start: {
            line: 376,
            column: 12
          },
          end: {
            line: 383,
            column: 22
          }
        }, {
          start: {
            line: 384,
            column: 12
          },
          end: {
            line: 388,
            column: 17
          }
        }],
        line: 334
      },
      "31": {
        loc: {
          start: {
            line: 396,
            column: 8
          },
          end: {
            line: 397,
            column: 48
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 396,
            column: 8
          },
          end: {
            line: 397,
            column: 48
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 396
      },
      "32": {
        loc: {
          start: {
            line: 398,
            column: 8
          },
          end: {
            line: 407,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 399,
            column: 12
          },
          end: {
            line: 400,
            column: 109
          }
        }, {
          start: {
            line: 401,
            column: 12
          },
          end: {
            line: 402,
            column: 108
          }
        }, {
          start: {
            line: 403,
            column: 12
          },
          end: {
            line: 404,
            column: 116
          }
        }, {
          start: {
            line: 405,
            column: 12
          },
          end: {
            line: 406,
            column: 52
          }
        }],
        line: 398
      },
      "33": {
        loc: {
          start: {
            line: 413,
            column: 8
          },
          end: {
            line: 414,
            column: 48
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 413,
            column: 8
          },
          end: {
            line: 414,
            column: 48
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 413
      },
      "34": {
        loc: {
          start: {
            line: 426,
            column: 12
          },
          end: {
            line: 428,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 426,
            column: 12
          },
          end: {
            line: 428,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 426
      },
      "35": {
        loc: {
          start: {
            line: 455,
            column: 12
          },
          end: {
            line: 468,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 455,
            column: 12
          },
          end: {
            line: 468,
            column: 13
          }
        }, {
          start: {
            line: 462,
            column: 17
          },
          end: {
            line: 468,
            column: 13
          }
        }],
        line: 455
      },
      "36": {
        loc: {
          start: {
            line: 476,
            column: 8
          },
          end: {
            line: 477,
            column: 22
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 476,
            column: 8
          },
          end: {
            line: 477,
            column: 22
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 476
      },
      "37": {
        loc: {
          start: {
            line: 480,
            column: 12
          },
          end: {
            line: 494,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 480,
            column: 12
          },
          end: {
            line: 494,
            column: 13
          }
        }, {
          start: {
            line: 489,
            column: 17
          },
          end: {
            line: 494,
            column: 13
          }
        }],
        line: 480
      },
      "38": {
        loc: {
          start: {
            line: 482,
            column: 28
          },
          end: {
            line: 482,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 482,
            column: 74
          },
          end: {
            line: 482,
            column: 97
          }
        }, {
          start: {
            line: 482,
            column: 100
          },
          end: {
            line: 482,
            column: 109
          }
        }],
        line: 482
      },
      "39": {
        loc: {
          start: {
            line: 483,
            column: 28
          },
          end: {
            line: 483,
            column: 108
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 483,
            column: 74
          },
          end: {
            line: 483,
            column: 97
          }
        }, {
          start: {
            line: 483,
            column: 100
          },
          end: {
            line: 483,
            column: 108
          }
        }],
        line: 483
      },
      "40": {
        loc: {
          start: {
            line: 509,
            column: 8
          },
          end: {
            line: 510,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 509,
            column: 8
          },
          end: {
            line: 510,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 509
      },
      "41": {
        loc: {
          start: {
            line: 515,
            column: 12
          },
          end: {
            line: 522,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 515,
            column: 12
          },
          end: {
            line: 522,
            column: 13
          }
        }, {
          start: {
            line: 520,
            column: 17
          },
          end: {
            line: 522,
            column: 13
          }
        }],
        line: 515
      },
      "42": {
        loc: {
          start: {
            line: 516,
            column: 28
          },
          end: {
            line: 516,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 516,
            column: 74
          },
          end: {
            line: 516,
            column: 97
          }
        }, {
          start: {
            line: 516,
            column: 100
          },
          end: {
            line: 516,
            column: 109
          }
        }],
        line: 516
      },
      "43": {
        loc: {
          start: {
            line: 517,
            column: 28
          },
          end: {
            line: 517,
            column: 108
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 517,
            column: 74
          },
          end: {
            line: 517,
            column: 97
          }
        }, {
          start: {
            line: 517,
            column: 100
          },
          end: {
            line: 517,
            column: 108
          }
        }],
        line: 517
      },
      "44": {
        loc: {
          start: {
            line: 530,
            column: 8
          },
          end: {
            line: 531,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 530,
            column: 8
          },
          end: {
            line: 531,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 530
      },
      "45": {
        loc: {
          start: {
            line: 533,
            column: 8
          },
          end: {
            line: 540,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 533,
            column: 8
          },
          end: {
            line: 540,
            column: 9
          }
        }, {
          start: {
            line: 537,
            column: 13
          },
          end: {
            line: 540,
            column: 9
          }
        }],
        line: 533
      },
      "46": {
        loc: {
          start: {
            line: 537,
            column: 13
          },
          end: {
            line: 540,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 537,
            column: 13
          },
          end: {
            line: 540,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 537
      },
      "47": {
        loc: {
          start: {
            line: 546,
            column: 8
          },
          end: {
            line: 547,
            column: 24
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 546,
            column: 8
          },
          end: {
            line: 547,
            column: 24
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 546
      },
      "48": {
        loc: {
          start: {
            line: 549,
            column: 8
          },
          end: {
            line: 551,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 549,
            column: 8
          },
          end: {
            line: 551,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 549
      },
      "49": {
        loc: {
          start: {
            line: 553,
            column: 8
          },
          end: {
            line: 555,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 553,
            column: 8
          },
          end: {
            line: 555,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 553
      },
      "50": {
        loc: {
          start: {
            line: 557,
            column: 8
          },
          end: {
            line: 563,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 557,
            column: 8
          },
          end: {
            line: 563,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 557
      },
      "51": {
        loc: {
          start: {
            line: 560,
            column: 12
          },
          end: {
            line: 562,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 560,
            column: 12
          },
          end: {
            line: 562,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 560
      },
      "52": {
        loc: {
          start: {
            line: 570,
            column: 8
          },
          end: {
            line: 571,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 570,
            column: 8
          },
          end: {
            line: 571,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 570
      },
      "53": {
        loc: {
          start: {
            line: 574,
            column: 25
          },
          end: {
            line: 574,
            column: 86
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 574,
            column: 25
          },
          end: {
            line: 574,
            column: 51
          }
        }, {
          start: {
            line: 574,
            column: 55
          },
          end: {
            line: 574,
            column: 86
          }
        }],
        line: 574
      },
      "54": {
        loc: {
          start: {
            line: 578,
            column: 34
          },
          end: {
            line: 578,
            column: 77
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 578,
            column: 72
          },
          end: {
            line: 578,
            column: 73
          }
        }, {
          start: {
            line: 578,
            column: 76
          },
          end: {
            line: 578,
            column: 77
          }
        }],
        line: 578
      },
      "55": {
        loc: {
          start: {
            line: 589,
            column: 29
          },
          end: {
            line: 589,
            column: 62
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 589,
            column: 29
          },
          end: {
            line: 589,
            column: 57
          }
        }, {
          start: {
            line: 589,
            column: 61
          },
          end: {
            line: 589,
            column: 62
          }
        }],
        line: 589
      },
      "56": {
        loc: {
          start: {
            line: 591,
            column: 34
          },
          end: {
            line: 591,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 591,
            column: 34
          },
          end: {
            line: 591,
            column: 62
          }
        }, {
          start: {
            line: 591,
            column: 66
          },
          end: {
            line: 591,
            column: 67
          }
        }],
        line: 591
      },
      "57": {
        loc: {
          start: {
            line: 599,
            column: 35
          },
          end: {
            line: 599,
            column: 71
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 599,
            column: 35
          },
          end: {
            line: 599,
            column: 66
          }
        }, {
          start: {
            line: 599,
            column: 70
          },
          end: {
            line: 599,
            column: 71
          }
        }],
        line: 599
      },
      "58": {
        loc: {
          start: {
            line: 600,
            column: 35
          },
          end: {
            line: 600,
            column: 71
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 600,
            column: 35
          },
          end: {
            line: 600,
            column: 66
          }
        }, {
          start: {
            line: 600,
            column: 70
          },
          end: {
            line: 600,
            column: 71
          }
        }],
        line: 600
      },
      "59": {
        loc: {
          start: {
            line: 601,
            column: 31
          },
          end: {
            line: 601,
            column: 63
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 601,
            column: 31
          },
          end: {
            line: 601,
            column: 58
          }
        }, {
          start: {
            line: 601,
            column: 62
          },
          end: {
            line: 601,
            column: 63
          }
        }],
        line: 601
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0,
      "210": 0,
      "211": 0,
      "212": 0,
      "213": 0,
      "214": 0,
      "215": 0,
      "216": 0,
      "217": 0,
      "218": 0,
      "219": 0,
      "220": 0,
      "221": 0,
      "222": 0,
      "223": 0,
      "224": 0,
      "225": 0,
      "226": 0,
      "227": 0,
      "228": 0,
      "229": 0,
      "230": 0,
      "231": 0,
      "232": 0,
      "233": 0,
      "234": 0,
      "235": 0,
      "236": 0,
      "237": 0,
      "238": 0,
      "239": 0,
      "240": 0,
      "241": 0,
      "242": 0,
      "243": 0,
      "244": 0,
      "245": 0,
      "246": 0,
      "247": 0,
      "248": 0,
      "249": 0,
      "250": 0,
      "251": 0,
      "252": 0,
      "253": 0,
      "254": 0,
      "255": 0,
      "256": 0,
      "257": 0,
      "258": 0,
      "259": 0,
      "260": 0,
      "261": 0,
      "262": 0,
      "263": 0,
      "264": 0,
      "265": 0,
      "266": 0,
      "267": 0,
      "268": 0,
      "269": 0,
      "270": 0,
      "271": 0,
      "272": 0,
      "273": 0,
      "274": 0,
      "275": 0,
      "276": 0,
      "277": 0,
      "278": 0,
      "279": 0,
      "280": 0,
      "281": 0,
      "282": 0,
      "283": 0,
      "284": 0,
      "285": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0, 0, 0, 0, 0],
      "31": [0, 0],
      "32": [0, 0, 0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\algorithms\\GradientDescent.ts",
      mappings: ";AAAA;;;;;;;;;;;;GAYG;;;AAEH,8EAY0C;AAyC1C;;GAEG;AACH,MAAa,eAAe;IAQ1B,YAAY,UAA+C;QANnD,iBAAY,GAAyB,IAAI,CAAC;QAC1C,iBAAY,GAAgC,IAAI,CAAC;QACjD,YAAO,GAAuB,EAAE,CAAC;QAEjC,oBAAe,GAAW,CAAC,CAAC;QAGlC,IAAI,CAAC,UAAU,GAAG;YAChB,aAAa,EAAE,IAAI;YACnB,YAAY,EAAE,IAAI;YAClB,eAAe,EAAE,IAAI;YACrB,eAAe,EAAE,GAAG;YACpB,OAAO,EAAE,MAAM;YACf,mBAAmB,EAAE,GAAG;YACxB,SAAS,EAAE,GAAG;YACd,SAAS,EAAE,KAAK;YAChB,WAAW,EAAE,IAAI;YACjB,gBAAgB,EAAE,GAAG;YACrB,cAAc,EAAE,IAAI;YACpB,cAAc,EAAE,SAAS;YACzB,oBAAoB,EAAE,IAAI;YAC1B,oBAAoB,EAAE,IAAI;YAC1B,UAAU,EAAE,IAAI;YAChB,gBAAgB,EAAE,QAAQ;YAC1B,kBAAkB,EAAE,YAAY;YAChC,kBAAkB,EAAE,IAAI;YACxB,oBAAoB,EAAE,IAAI;YAC1B,iBAAiB,EAAE,IAAI;YACvB,GAAG,UAAU;SACd,CAAC;QAEF,qCAAqC;QACrC,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;YAC5C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;QACnE,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC5B,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,QAAQ,CACnB,OAA4B,EAC5B,iBAAwC,EACxC,mBAA6C;QAE7C,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QAEpC,IAAI,CAAC;YACH,wCAAwC;YACxC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YAE9B,uBAAuB;YACvB,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;YAElC,0BAA0B;YAC1B,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,iBAAiB,EAAE,mBAAmB,CAAC,CAAC;YAElF,yBAAyB;YACzB,OAAO,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,CAAC;gBAC/B,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,iBAAiB,EAAE,mBAAmB,CAAC,CAAC;gBAC7E,IAAI,CAAC,aAAa,EAAE,CAAC;gBAErB,IAAI,IAAI,CAAC,UAAU,CAAC,oBAAoB,EAAE,CAAC;oBACzC,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBAC3B,CAAC;YACH,CAAC;YAED,sBAAsB;YACtB,OAAO,IAAI,CAAC,wBAAwB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QAE3D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;YAC9D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,OAA4B;QAClD,sCAAsC;QACtC,MAAM,oBAAoB,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,cAAc,IAAI,CAAC,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC1G,IAAI,oBAAoB,EAAE,CAAC;YACzB,OAAO,CAAC,IAAI,CAAC,0GAA0G,CAAC,CAAC;QAC3H,CAAC;IACH,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,OAA4B;QACtD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;QAClB,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;QAEzB,OAAO,CAAC,GAAG,CAAC,kCAAkC,IAAI,CAAC,UAAU,CAAC,OAAO,yBAAyB,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC,CAAC;IAChI,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,qBAAqB,CACjC,OAA4B,EAC5B,iBAAwC,EACxC,mBAA6C;QAE7C,4BAA4B;QAC5B,MAAM,eAAe,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QAC3D,MAAM,IAAI,CAAC,gBAAgB,CAAC,eAAe,EAAE,OAAO,EAAE,iBAAiB,EAAE,mBAAmB,CAAC,CAAC;QAE9F,4BAA4B;QAC5B,MAAM,YAAY,GAAG,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC;QAC9C,IAAI,CAAC,YAAY,GAAG;YAClB,QAAQ,EAAE,eAAe;YACzB,QAAQ,EAAE,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;YACzC,QAAQ,EAAE,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;YACzC,KAAK,EAAE,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;YACtC,KAAK,EAAE,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;YACtC,QAAQ,EAAE,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;YACzC,QAAQ,EAAE,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;YACzC,SAAS,EAAE,CAAC;YACZ,YAAY,EAAE,IAAI,CAAC,UAAU,CAAC,YAAY;YAC1C,YAAY,EAAE,CAAC;YACf,QAAQ,EAAE,CAAC;YACX,aAAa,EAAE,eAAe,CAAC,OAAO;SACvC,CAAC;QAEF,IAAI,CAAC,YAAY,GAAG,EAAE,GAAG,eAAe,EAAE,CAAC;IAC7C,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,OAA4B;QACvD,MAAM,SAAS,GAA8C,EAAE,CAAC;QAEhE,KAAK,MAAM,QAAQ,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACzC,IAAI,QAAQ,CAAC,cAAc,IAAI,QAAQ,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAClE,0CAA0C;gBAC1C,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;gBAC/E,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;YAChE,CAAC;iBAAM,CAAC;gBACN,sBAAsB;gBACtB,MAAM,GAAG,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtF,MAAM,GAAG,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtF,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;YAC7D,CAAC;QACH,CAAC;QAED,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,kBAAkB,EAAE;YAC7B,SAAS;YACT,eAAe,EAAE,EAAE;YACnB,oBAAoB,EAAE,EAAE;YACxB,QAAQ,EAAE,IAAI;YACd,OAAO,EAAE,CAAC;YACV,mBAAmB,EAAE,OAAO,CAAC,mBAAmB;YAChD,kBAAkB,EAAE,EAAgC;SACrD,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAC5B,QAA8B,EAC9B,OAA4B,EAC5B,iBAAwC,EACxC,mBAA6C;QAE7C,IAAI,CAAC;YACH,6CAA6C;YAC7C,MAAM,SAAS,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;YAExE,8BAA8B;YAC9B,MAAM,cAAc,GAAG,iBAAiB,CAAC,SAAS,CAAC,CAAC;YACpD,QAAQ,CAAC,OAAO,GAAG,cAAc,CAAC;YAElC,yBAAyB;YACzB,IAAI,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC7C,QAAQ,CAAC,eAAe,CAAC,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC;YACjF,CAAC;YAED,uBAAuB;YACvB,QAAQ,CAAC,oBAAoB,GAAG,EAAE,CAAC;YACnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,mBAAmB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBACpD,MAAM,SAAS,GAAG,mBAAmB,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;gBACpD,QAAQ,CAAC,oBAAoB,CAAC,IAAI,CAAC;oBACjC,YAAY,EAAE,cAAc,CAAC,EAAE;oBAC/B,aAAa,EAAE,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,UAAU;oBACxD,YAAY,EAAE,SAAS;oBACvB,aAAa,EAAE,CAAC;oBAChB,QAAQ,EAAE,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO;oBAC3C,OAAO,EAAE,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;iBAC5E,CAAC,CAAC;YACL,CAAC;YAED,oBAAoB;YACpB,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,YAAY,IAAI,CAAC,CAAC,CAAC;YAElF,4BAA4B;YAC5B,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,IAAI,CAAC,UAAU,CAAC,kBAAkB,KAAK,SAAS,EAAE,CAAC;gBAC3E,MAAM,YAAY,GAAG,QAAQ,CAAC,oBAAoB;qBAC/C,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,YAAY,GAAG,CAAC,CAAC;qBAC/B,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;gBAC1C,QAAQ,CAAC,OAAO,IAAI,YAAY,CAAC;YACnC,CAAC;YAED,IAAI,CAAC,eAAe,EAAE,CAAC;QAEzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACnD,QAAQ,CAAC,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC;YACpC,QAAQ,CAAC,QAAQ,GAAG,KAAK,CAAC;QAC5B,CAAC;IACH,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,QAA8B,EAAE,iBAAyC;QACnG,OAAO,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YACxC,GAAG,QAAQ;YACX,YAAY,EAAE,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC;SAC9C,CAAC,CAAC,CAAC;IACN,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAC5B,OAA4B,EAC5B,iBAAwC,EACxC,mBAA6C;QAE7C,IAAI,CAAC,IAAI,CAAC,YAAY;YAAE,OAAO;QAE/B,mBAAmB;QACnB,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,iBAAiB,EAAE,mBAAmB,CAAC,CAAC;QAE5E,mCAAmC;QACnC,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,iBAAiB,EAAE,mBAAmB,CAAC,CAAC;QAE3E,wCAAwC;QACxC,IAAI,IAAI,CAAC,UAAU,CAAC,kBAAkB,KAAK,YAAY,EAAE,CAAC;YACxD,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QAChC,CAAC;QAED,uBAAuB;QACvB,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC,YAAa,CAAC,OAAO,EAAE,CAAC;YACpE,IAAI,CAAC,YAAY,GAAG,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;QACxD,CAAC;QAED,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC;IAChC,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,eAAe,CAC3B,OAA4B,EAC5B,iBAAwC,EACxC,mBAA6C;QAE7C,IAAI,CAAC,IAAI,CAAC,YAAY;YAAE,OAAO;QAE/B,MAAM,gBAAgB,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;QACjG,MAAM,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC;QAE/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAClD,MAAM,QAAQ,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAEtC,0BAA0B;YAC1B,IAAI,QAAQ,CAAC,cAAc,IAAI,QAAQ,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAClE,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;gBAClC,SAAS;YACX,CAAC;YAED,MAAM,aAAa,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC,YAAsB,CAAC;YAEjE,IAAI,QAAQ,GAAG,CAAC,CAAC;YAEjB,QAAQ,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC;gBACvC,KAAK,SAAS;oBACZ,8BAA8B;oBAC9B,gBAAgB,CAAC,CAAC,CAAC,CAAC,YAAY,GAAG,aAAa,GAAG,CAAC,CAAC;oBACrD,MAAM,YAAY,GAAG,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;oBACzD,QAAQ,GAAG,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;oBAChE,MAAM;gBAER,KAAK,UAAU;oBACb,8BAA8B;oBAC9B,gBAAgB,CAAC,CAAC,CAAC,CAAC,YAAY,GAAG,aAAa,GAAG,CAAC,CAAC;oBACrD,MAAM,aAAa,GAAG,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;oBAC1D,QAAQ,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,aAAa,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC;oBACjE,MAAM;gBAER,KAAK,SAAS;oBACZ,mCAAmC;oBACnC,gBAAgB,CAAC,CAAC,CAAC,CAAC,YAAY,GAAG,aAAa,GAAG,CAAC,CAAC;oBACrD,MAAM,UAAU,GAAG,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;oBACvD,gBAAgB,CAAC,CAAC,CAAC,CAAC,YAAY,GAAG,aAAa,GAAG,CAAC,CAAC;oBACrD,MAAM,WAAW,GAAG,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;oBACxD,QAAQ,GAAG,CAAC,UAAU,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;oBAChD,MAAM;YACV,CAAC;YAED,yBAAyB;YACzB,gBAAgB,CAAC,CAAC,CAAC,CAAC,YAAY,GAAG,aAAa,CAAC;YAEjD,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC;YACzC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,UAAU,CAAC,cAAc,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/E,CAAC;QAED,wBAAwB;QACxB,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,IAAI,CAAC,IAAI,CACxC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAC9D,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,cAAc,CAC1B,OAA4B,EAC5B,iBAAwC,EACxC,mBAA6C;QAE7C,IAAI,CAAC,IAAI,CAAC,YAAY;YAAE,OAAO;QAE/B,MAAM,aAAa,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAElD,sBAAsB;QACtB,IAAI,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC;QAC9C,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;YAC/B,QAAQ,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,aAAa,CAAC,CAAC;QAC1G,CAAC;QAED,kBAAkB;QAClB,MAAM,YAAY,GAA8C,EAAE,CAAC;QAEnE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAClD,MAAM,QAAQ,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAEvE,IAAI,QAAQ,CAAC,cAAc,IAAI,QAAQ,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAClE,oCAAoC;gBACpC,YAAY,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC;YAC3C,CAAC;iBAAM,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE,CAAC;gBAC5C,8BAA8B;gBAC9B,MAAM,QAAQ,GAAG,YAAY,GAAG,QAAQ,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;gBAE5D,eAAe;gBACf,MAAM,GAAG,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;gBAC9F,MAAM,GAAG,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC;gBAC7F,YAAY,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC;YACrE,CAAC;iBAAM,CAAC;gBACN,YAAY,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC;YAC3C,CAAC;QACH,CAAC;QAED,sBAAsB;QACtB,MAAM,WAAW,GAAyB;YACxC,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ;YAC7B,EAAE,EAAE,IAAI,CAAC,kBAAkB,EAAE;YAC7B,SAAS,EAAE,YAAY;SACxB,CAAC;QAEF,wBAAwB;QACxB,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,OAAO,EAAE,iBAAiB,EAAE,mBAAmB,CAAC,CAAC;QAE1F,eAAe;QACf,IAAI,CAAC,YAAY,CAAC,QAAQ,GAAG,WAAW,CAAC;QACzC,IAAI,CAAC,YAAY,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACtC,IAAI,CAAC,YAAY,CAAC,aAAa,GAAG,WAAW,CAAC,OAAO,CAAC;IACxD,CAAC;IAED;;OAEG;IACK,oBAAoB;QAC1B,IAAI,CAAC,IAAI,CAAC,YAAY;YAAE,OAAO,EAAE,CAAC;QAElC,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC;QAC5C,MAAM,aAAa,GAAG,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAEjD,QAAQ,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;YAChC,KAAK,UAAU;gBACb,qCAAqC;gBACrC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;oBACzC,aAAa,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;gBACjC,CAAC;gBACD,MAAM;YAER,KAAK,UAAU;gBACb,gCAAgC;gBAChC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;oBACzC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,mBAAmB,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;oBAClH,aAAa,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBACnD,CAAC;gBACD,MAAM;YAER,KAAK,MAAM;gBACT,iBAAiB;gBACjB,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC;gBACxC,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC;gBACxC,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC;gBAC5C,MAAM,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,GAAG,CAAC,CAAC;gBAE1C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;oBACzC,sCAAsC;oBACtC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;oBAE5F,2CAA2C;oBAC3C,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;oBAE1G,+CAA+C;oBAC/C,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;oBAEnE,oDAAoD;oBACpD,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;oBAEnE,wBAAwB;oBACxB,aAAa,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC;gBACxD,CAAC;gBACD,MAAM;YAER,KAAK,SAAS;gBACZ,UAAU;gBACV,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC;gBAC/C,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC;gBAE3C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;oBACzC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;oBAChH,aAAa,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;gBACpF,CAAC;gBACD,MAAM;YAER,KAAK,SAAS;gBACZ,UAAU;gBACV,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC;gBAE/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;oBACzC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;oBAC3D,aAAa,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC;gBAC3F,CAAC;gBACD,MAAM;YAER;gBACE,uCAAuC;gBACvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;oBACzC,aAAa,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;gBACjC,CAAC;QACL,CAAC;QAED,OAAO,aAAa,CAAC;IACvB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB,CAC7B,OAA4B,EAC5B,iBAAwC,EACxC,mBAA6C,EAC7C,aAAuB;QAEvB,IAAI,CAAC,IAAI,CAAC,YAAY;YAAE,OAAO,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC;QAE5D,QAAQ,IAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC;YACzC,KAAK,QAAQ;gBACX,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,aAAa,CAAC,CAAC;YAC/F,KAAK,OAAO;gBACV,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,aAAa,CAAC,CAAC;YAC9F,KAAK,gBAAgB;gBACnB,OAAO,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,aAAa,CAAC,CAAC;YACtG;gBACE,OAAO,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC;QACxC,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAC5B,OAA4B,EAC5B,iBAAwC,EACxC,mBAA6C,EAC7C,aAAuB;QAEvB,IAAI,CAAC,IAAI,CAAC,YAAY;YAAE,OAAO,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC;QAE5D,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,kBAAkB;QACnC,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC;QACzC,MAAM,YAAY,GAAG,EAAE,CAAC;QAExB,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC;QACrD,MAAM,oBAAoB,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAE7G,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,EAAE,CAAC,EAAE,EAAE,CAAC;YACtC,YAAY;YACZ,MAAM,aAAa,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC;YAC9E,MAAM,SAAS,GAAG,iBAAiB,CAAC,aAAa,CAAC,CAAC;YACnD,IAAI,CAAC,eAAe,EAAE,CAAC;YAEvB,mBAAmB;YACnB,IAAI,SAAS,IAAI,YAAY,GAAG,EAAE,GAAG,KAAK,GAAG,oBAAoB,EAAE,CAAC;gBAClE,OAAO,KAAK,CAAC;YACf,CAAC;YAED,KAAK,IAAI,GAAG,CAAC,CAAC,YAAY;QAC5B,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,eAAe,CAC3B,OAA4B,EAC5B,iBAAwC,EACxC,mBAA6C,EAC7C,aAAuB;QAEvB,iDAAiD;QACjD,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,aAAa,CAAC,CAAC;IAC/F,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,uBAAuB,CACnC,OAA4B,EAC5B,iBAAwC,EACxC,mBAA6C,EAC7C,aAAuB;QAEvB,MAAM,GAAG,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,eAAe;QACnD,MAAM,MAAM,GAAG,CAAC,GAAG,GAAG,CAAC;QAEvB,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,GAAG,CAAC,CAAC;QACzC,MAAM,GAAG,GAAG,IAAI,CAAC;QAEjB,uBAAuB;QACvB,IAAI,EAAE,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAC9B,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAEpC,IAAI,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,iBAAiB,EAAE,aAAa,EAAE,EAAE,CAAC,CAAC;QAC5E,IAAI,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,iBAAiB,EAAE,aAAa,EAAE,EAAE,CAAC,CAAC;QAE5E,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC;YAC7B,IAAI,EAAE,GAAG,EAAE,EAAE,CAAC;gBACZ,CAAC,GAAG,EAAE,CAAC;gBACP,EAAE,GAAG,EAAE,CAAC;gBACR,EAAE,GAAG,EAAE,CAAC;gBACR,EAAE,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;gBAC1B,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,iBAAiB,EAAE,aAAa,EAAE,EAAE,CAAC,CAAC;YAC1E,CAAC;iBAAM,CAAC;gBACN,CAAC,GAAG,EAAE,CAAC;gBACP,EAAE,GAAG,EAAE,CAAC;gBACR,EAAE,GAAG,EAAE,CAAC;gBACR,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;gBAChC,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,iBAAiB,EAAE,aAAa,EAAE,EAAE,CAAC,CAAC;YAC1E,CAAC;QACH,CAAC;QAED,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;IACrB,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,OAA4B,EAAE,aAAuB,EAAE,KAAa;QAC9F,IAAI,CAAC,IAAI,CAAC,YAAY;YAAE,OAAO,EAAE,CAAC;QAElC,OAAO,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,EAAE,EAAE;YAC3C,MAAM,YAAY,GAAG,IAAI,CAAC,YAAa,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAExE,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE,CAAC;gBACrC,MAAM,QAAQ,GAAG,YAAY,GAAG,KAAK,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;gBACzD,MAAM,GAAG,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;gBAC9F,MAAM,GAAG,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC;gBAE7F,OAAO;oBACL,GAAG,QAAQ;oBACX,YAAY,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;iBACrD,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,OAAO;oBACL,GAAG,QAAQ;oBACX,YAAY;iBACb,CAAC;YACJ,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,cAAc,CACpB,OAA4B,EAC5B,iBAAwC,EACxC,aAAuB,EACvB,KAAa;QAEb,MAAM,aAAa,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC;QAC9E,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,OAAO,iBAAiB,CAAC,aAAa,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,OAA4B;QAClD,IAAI,CAAC,IAAI,CAAC,YAAY;YAAE,OAAO;QAE/B,mCAAmC;QACnC,MAAM,kBAAkB,GAA8C,EAAE,CAAC;QAEzE,KAAK,MAAM,QAAQ,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACzC,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAEvE,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE,CAAC;gBACrC,MAAM,GAAG,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;gBAC9F,MAAM,GAAG,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC;gBAC7F,kBAAkB,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC,CAAC;YAC/E,CAAC;iBAAM,CAAC;gBACN,kBAAkB,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC;YACjD,CAAC;QACH,CAAC;QAED,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,kBAAkB,CAAC;IAC5D,CAAC;IAED;;OAEG;IACK,iBAAiB;QACvB,IAAI,CAAC,IAAI,CAAC,YAAY;YAAE,OAAO;QAE/B,gDAAgD;QAChD,IAAI,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,GAAG,EAAE,CAAC;YACzC,0CAA0C;YAC1C,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,CACvC,IAAI,CAAC,UAAU,CAAC,eAAe,EAC/B,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,GAAG,CACrC,CAAC;QACJ,CAAC;aAAM,IAAI,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,GAAG,EAAE,CAAC;YAChD,0CAA0C;YAC1C,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,CACvC,IAAI,CAAC,UAAU,CAAC,eAAe,EAC/B,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,GAAG,CACrC,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,eAAe;QACrB,IAAI,CAAC,IAAI,CAAC,YAAY;YAAE,OAAO,IAAI,CAAC;QAEpC,qBAAqB;QACrB,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC;YACjE,OAAO,IAAI,CAAC;QACd,CAAC;QAED,qBAAqB;QACrB,IAAI,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC;YACvE,OAAO,IAAI,CAAC;QACd,CAAC;QAED,6BAA6B;QAC7B,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,EAAE,EAAE,CAAC;YAC9B,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;YAC9C,MAAM,mBAAmB,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,aAAa,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,WAAW,CAAC;YAE/G,IAAI,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,oBAAoB,EAAE,CAAC;gBACzE,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACK,aAAa;QACnB,IAAI,CAAC,IAAI,CAAC,YAAY;YAAE,OAAO;QAE/B,MAAM,OAAO,GAAqB;YAChC,SAAS,EAAE,IAAI,CAAC,YAAY,CAAC,SAAS;YACtC,WAAW,EAAE,IAAI,CAAC,YAAY,EAAE,OAAO,IAAI,IAAI,CAAC,YAAY,CAAC,aAAa;YAC1E,cAAc,EAAE,IAAI,CAAC,YAAY,CAAC,aAAa;YAC/C,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,aAAa;YAC7C,SAAS,EAAE,CAAC,EAAE,qCAAqC;YACnD,oBAAoB,EAAE,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjE,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACK,wBAAwB,CAAC,OAA4B,EAAE,SAAiB;QAC9E,MAAM,aAAa,GAAG,WAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;QAEpD,MAAM,UAAU,GAA2B;YACzC,eAAe,EAAE,IAAI,CAAC,YAAY,EAAE,SAAS,IAAI,CAAC;YAClD,gBAAgB,EAAE,IAAI,CAAC,eAAe;YACtC,oBAAoB,EAAE,IAAI,CAAC,YAAY,EAAE,SAAS,IAAI,CAAC;YACvD,aAAa;YACb,kBAAkB,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC;YACxD,qBAAqB,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,cAAc,CAAC;YAC9D,gBAAgB,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;YACpD,0BAA0B,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,oBAAoB,CAAC;YACzE,sBAAsB,EAAE;gBACtB,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,OAAO;gBAChC,iBAAiB,EAAE,IAAI,CAAC,YAAY,EAAE,YAAY,IAAI,CAAC;gBACvD,iBAAiB,EAAE,IAAI,CAAC,YAAY,EAAE,YAAY,IAAI,CAAC;gBACvD,aAAa,EAAE,IAAI,CAAC,YAAY,EAAE,QAAQ,IAAI,CAAC;aAChD;SACF,CAAC;QAEF,MAAM,mBAAmB,GAAwB;YAC/C,UAAU,EAAE,IAAI,CAAC,OAAO;YACxB,iBAAiB,EAAE,EAAE;YACrB,gBAAgB,EAAE,EAAE;YACpB,kBAAkB,EAAE,EAAE;SACvB,CAAC;QAEF,OAAO;YACL,SAAS,EAAE,OAAO,CAAC,EAAE;YACrB,MAAM,EAAE,4CAAkB,CAAC,SAAS;YACpC,YAAY,EAAE,IAAI,CAAC,YAAa;YAChC,UAAU;YACV,OAAO,EAAE,mBAAmB;YAC5B,QAAQ,EAAE,EAAE;YACZ,eAAe,EAAE,EAAE;YACnB,QAAQ,EAAE,EAAE;YACZ,MAAM,EAAE,EAAE;SACX,CAAC;IACJ,CAAC;IAED,kBAAkB;IACV,kBAAkB;QACxB,OAAO,UAAU,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IAC3E,CAAC;IAEO,kBAAkB,CAAC,IAAY;QACrC,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,OAAO,GAAG,EAAE;YACV,KAAK,GAAG,CAAC,KAAK,GAAG,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC;YACxC,OAAO,KAAK,GAAG,MAAM,CAAC;QACxB,CAAC,CAAC;IACJ,CAAC;CACF;AA9vBD,0CA8vBC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\algorithms\\GradientDescent.ts"],
      sourcesContent: ["/**\r\n * Gradient Descent Algorithm Implementation for System Optimization\r\n * \r\n * Implements gradient descent optimization with:\r\n * - Multiple variants (standard, momentum, Adam, RMSprop)\r\n * - Numerical gradient computation with finite differences\r\n * - Adaptive learning rate and step size control\r\n * - Line search optimization\r\n * - Constraint handling with projected gradients\r\n * \r\n * @version 3.0.0\r\n * <AUTHOR> Suite Development Team\r\n */\r\n\r\nimport {\r\n  OptimizationSolution,\r\n  OptimizationVariable,\r\n  OptimizationProblem,\r\n  OptimizationResult,\r\n  OptimizationStatus,\r\n  OptimizationStatistics,\r\n  OptimizationHistory,\r\n  IterationHistory,\r\n  SolutionPerformanceMetrics,\r\n  ObjectiveFunctionType,\r\n  ConstraintFunctionType\r\n} from '../types/SystemOptimizationTypes';\r\n\r\nexport interface GradientDescentParameters {\r\n  maxIterations: number;\r\n  learningRate: number;\r\n  learningRateMin: number;\r\n  learningRateMax: number;\r\n  variant: 'standard' | 'momentum' | 'adam' | 'rmsprop' | 'adagrad';\r\n  momentumCoefficient: number;\r\n  adamBeta1: number;\r\n  adamBeta2: number;\r\n  adamEpsilon: number;\r\n  rmspropDecayRate: number;\r\n  rmspropEpsilon: number;\r\n  gradientMethod: 'forward' | 'backward' | 'central';\r\n  finiteDifferenceStep: number;\r\n  adaptiveLearningRate: boolean;\r\n  lineSearch: boolean;\r\n  lineSearchMethod: 'armijo' | 'wolfe' | 'golden_section';\r\n  constraintHandling: 'penalty' | 'projection' | 'barrier';\r\n  penaltyCoefficient: number;\r\n  convergenceTolerance: number;\r\n  gradientTolerance: number;\r\n  seedValue?: number;\r\n}\r\n\r\nexport interface GradientState {\r\n  solution: OptimizationSolution;\r\n  gradient: number[];\r\n  momentum: number[];\r\n  adamM: number[];\r\n  adamV: number[];\r\n  rmspropV: number[];\r\n  adagradG: number[];\r\n  iteration: number;\r\n  learningRate: number;\r\n  gradientNorm: number;\r\n  stepSize: number;\r\n  functionValue: number;\r\n}\r\n\r\n/**\r\n * Gradient Descent optimizer for continuous optimization problems\r\n */\r\nexport class GradientDescent {\r\n  private parameters: GradientDescentParameters;\r\n  private currentState: GradientState | null = null;\r\n  private bestSolution: OptimizationSolution | null = null;\r\n  private history: IterationHistory[] = [];\r\n  private random: () => number;\r\n  private evaluationCount: number = 0;\r\n\r\n  constructor(parameters?: Partial<GradientDescentParameters>) {\r\n    this.parameters = {\r\n      maxIterations: 1000,\r\n      learningRate: 0.01,\r\n      learningRateMin: 1e-6,\r\n      learningRateMax: 1.0,\r\n      variant: 'adam',\r\n      momentumCoefficient: 0.9,\r\n      adamBeta1: 0.9,\r\n      adamBeta2: 0.999,\r\n      adamEpsilon: 1e-8,\r\n      rmspropDecayRate: 0.9,\r\n      rmspropEpsilon: 1e-8,\r\n      gradientMethod: 'central',\r\n      finiteDifferenceStep: 1e-6,\r\n      adaptiveLearningRate: true,\r\n      lineSearch: true,\r\n      lineSearchMethod: 'armijo',\r\n      constraintHandling: 'projection',\r\n      penaltyCoefficient: 1000,\r\n      convergenceTolerance: 1e-6,\r\n      gradientTolerance: 1e-6,\r\n      ...parameters\r\n    };\r\n\r\n    // Initialize random number generator\r\n    if (this.parameters.seedValue !== undefined) {\r\n      this.random = this.createSeededRandom(this.parameters.seedValue);\r\n    } else {\r\n      this.random = Math.random;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Main optimization method\r\n   */\r\n  public async optimize(\r\n    problem: OptimizationProblem,\r\n    objectiveFunction: ObjectiveFunctionType,\r\n    constraintFunctions: ConstraintFunctionType[]\r\n  ): Promise<OptimizationResult> {\r\n    const startTime = performance.now();\r\n    \r\n    try {\r\n      // Validate problem for gradient descent\r\n      this.validateProblem(problem);\r\n      \r\n      // Initialize algorithm\r\n      this.initializeAlgorithm(problem);\r\n      \r\n      // Create initial solution\r\n      await this.createInitialSolution(problem, objectiveFunction, constraintFunctions);\r\n      \r\n      // Main optimization loop\r\n      while (!this.shouldTerminate()) {\r\n        await this.performIteration(problem, objectiveFunction, constraintFunctions);\r\n        this.updateHistory();\r\n        \r\n        if (this.parameters.adaptiveLearningRate) {\r\n          this.adaptLearningRate();\r\n        }\r\n      }\r\n      \r\n      // Create final result\r\n      return this.createOptimizationResult(problem, startTime);\r\n      \r\n    } catch (error) {\r\n      console.error('Gradient descent optimization failed:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Validate problem for gradient descent\r\n   */\r\n  private validateProblem(problem: OptimizationProblem): void {\r\n    // Check for continuous variables only\r\n    const hasDiscreteVariables = problem.variables.some(v => v.discreteValues && v.discreteValues.length > 0);\r\n    if (hasDiscreteVariables) {\r\n      console.warn('Gradient descent works best with continuous variables. Discrete variables will be treated as continuous.');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Initialize algorithm state\r\n   */\r\n  private initializeAlgorithm(problem: OptimizationProblem): void {\r\n    this.currentState = null;\r\n    this.bestSolution = null;\r\n    this.history = [];\r\n    this.evaluationCount = 0;\r\n    \r\n    console.log(`Initializing Gradient Descent (${this.parameters.variant}) with learning rate: ${this.parameters.learningRate}`);\r\n  }\r\n\r\n  /**\r\n   * Create initial solution\r\n   */\r\n  private async createInitialSolution(\r\n    problem: OptimizationProblem,\r\n    objectiveFunction: ObjectiveFunctionType,\r\n    constraintFunctions: ConstraintFunctionType[]\r\n  ): Promise<void> {\r\n    // Generate initial solution\r\n    const initialSolution = this.createRandomSolution(problem);\r\n    await this.evaluateSolution(initialSolution, problem, objectiveFunction, constraintFunctions);\r\n    \r\n    // Initialize gradient state\r\n    const numVariables = problem.variables.length;\r\n    this.currentState = {\r\n      solution: initialSolution,\r\n      gradient: new Array(numVariables).fill(0),\r\n      momentum: new Array(numVariables).fill(0),\r\n      adamM: new Array(numVariables).fill(0),\r\n      adamV: new Array(numVariables).fill(0),\r\n      rmspropV: new Array(numVariables).fill(0),\r\n      adagradG: new Array(numVariables).fill(0),\r\n      iteration: 0,\r\n      learningRate: this.parameters.learningRate,\r\n      gradientNorm: 0,\r\n      stepSize: 0,\r\n      functionValue: initialSolution.fitness\r\n    };\r\n    \r\n    this.bestSolution = { ...initialSolution };\r\n  }\r\n\r\n  /**\r\n   * Create a random solution\r\n   */\r\n  private createRandomSolution(problem: OptimizationProblem): OptimizationSolution {\r\n    const variables: { [variableId: string]: number | string } = {};\r\n    \r\n    for (const variable of problem.variables) {\r\n      if (variable.discreteValues && variable.discreteValues.length > 0) {\r\n        // Discrete variable - select random value\r\n        const randomIndex = Math.floor(this.random() * variable.discreteValues.length);\r\n        variables[variable.id] = variable.discreteValues[randomIndex];\r\n      } else {\r\n        // Continuous variable\r\n        const min = typeof variable.bounds.minimum === 'number' ? variable.bounds.minimum : 0;\r\n        const max = typeof variable.bounds.maximum === 'number' ? variable.bounds.maximum : 1;\r\n        variables[variable.id] = min + this.random() * (max - min);\r\n      }\r\n    }\r\n    \r\n    return {\r\n      id: this.generateSolutionId(),\r\n      variables,\r\n      objectiveValues: {},\r\n      constraintViolations: [],\r\n      feasible: true,\r\n      fitness: 0,\r\n      systemConfiguration: problem.systemConfiguration,\r\n      performanceMetrics: {} as SolutionPerformanceMetrics\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Evaluate solution fitness and constraints\r\n   */\r\n  private async evaluateSolution(\r\n    solution: OptimizationSolution,\r\n    problem: OptimizationProblem,\r\n    objectiveFunction: ObjectiveFunctionType,\r\n    constraintFunctions: ConstraintFunctionType[]\r\n  ): Promise<void> {\r\n    try {\r\n      // Convert solution to optimization variables\r\n      const variables = this.solutionToVariables(solution, problem.variables);\r\n      \r\n      // Evaluate objective function\r\n      const objectiveValue = objectiveFunction(variables);\r\n      solution.fitness = objectiveValue;\r\n      \r\n      // Store objective values\r\n      if (problem.objectives.objectives.length > 0) {\r\n        solution.objectiveValues[problem.objectives.objectives[0].id] = objectiveValue;\r\n      }\r\n      \r\n      // Evaluate constraints\r\n      solution.constraintViolations = [];\r\n      for (let i = 0; i < constraintFunctions.length; i++) {\r\n        const violation = constraintFunctions[i](variables);\r\n        solution.constraintViolations.push({\r\n          constraintId: `constraint_${i}`,\r\n          violationType: violation > 0 ? 'inequality' : 'boundary',\r\n          currentValue: violation,\r\n          requiredValue: 0,\r\n          severity: violation > 0 ? 'major' : 'minor',\r\n          penalty: violation > 0 ? violation * this.parameters.penaltyCoefficient : 0\r\n        });\r\n      }\r\n      \r\n      // Check feasibility\r\n      solution.feasible = solution.constraintViolations.every(v => v.currentValue <= 0);\r\n      \r\n      // Apply constraint handling\r\n      if (!solution.feasible && this.parameters.constraintHandling === 'penalty') {\r\n        const totalPenalty = solution.constraintViolations\r\n          .filter(v => v.currentValue > 0)\r\n          .reduce((sum, v) => sum + v.penalty, 0);\r\n        solution.fitness += totalPenalty;\r\n      }\r\n      \r\n      this.evaluationCount++;\r\n      \r\n    } catch (error) {\r\n      console.error('Error evaluating solution:', error);\r\n      solution.fitness = Number.MAX_VALUE;\r\n      solution.feasible = false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Convert solution to optimization variables\r\n   */\r\n  private solutionToVariables(solution: OptimizationSolution, variableTemplates: OptimizationVariable[]): OptimizationVariable[] {\r\n    return variableTemplates.map(template => ({\r\n      ...template,\r\n      currentValue: solution.variables[template.id]\r\n    }));\r\n  }\r\n\r\n  /**\r\n   * Perform one iteration of gradient descent\r\n   */\r\n  private async performIteration(\r\n    problem: OptimizationProblem,\r\n    objectiveFunction: ObjectiveFunctionType,\r\n    constraintFunctions: ConstraintFunctionType[]\r\n  ): Promise<void> {\r\n    if (!this.currentState) return;\r\n    \r\n    // Compute gradient\r\n    await this.computeGradient(problem, objectiveFunction, constraintFunctions);\r\n    \r\n    // Update solution based on variant\r\n    await this.updateSolution(problem, objectiveFunction, constraintFunctions);\r\n    \r\n    // Apply constraints if using projection\r\n    if (this.parameters.constraintHandling === 'projection') {\r\n      this.projectSolution(problem);\r\n    }\r\n    \r\n    // Update best solution\r\n    if (this.currentState.solution.fitness < this.bestSolution!.fitness) {\r\n      this.bestSolution = { ...this.currentState.solution };\r\n    }\r\n    \r\n    this.currentState.iteration++;\r\n  }\r\n\r\n  /**\r\n   * Compute numerical gradient\r\n   */\r\n  private async computeGradient(\r\n    problem: OptimizationProblem,\r\n    objectiveFunction: ObjectiveFunctionType,\r\n    constraintFunctions: ConstraintFunctionType[]\r\n  ): Promise<void> {\r\n    if (!this.currentState) return;\r\n    \r\n    const currentVariables = this.solutionToVariables(this.currentState.solution, problem.variables);\r\n    const h = this.parameters.finiteDifferenceStep;\r\n    \r\n    for (let i = 0; i < problem.variables.length; i++) {\r\n      const variable = problem.variables[i];\r\n      \r\n      // Skip discrete variables\r\n      if (variable.discreteValues && variable.discreteValues.length > 0) {\r\n        this.currentState.gradient[i] = 0;\r\n        continue;\r\n      }\r\n      \r\n      const originalValue = currentVariables[i].currentValue as number;\r\n      \r\n      let gradient = 0;\r\n      \r\n      switch (this.parameters.gradientMethod) {\r\n        case 'forward':\r\n          // f'(x) \u2248 (f(x+h) - f(x)) / h\r\n          currentVariables[i].currentValue = originalValue + h;\r\n          const forwardValue = objectiveFunction(currentVariables);\r\n          gradient = (forwardValue - this.currentState.functionValue) / h;\r\n          break;\r\n          \r\n        case 'backward':\r\n          // f'(x) \u2248 (f(x) - f(x-h)) / h\r\n          currentVariables[i].currentValue = originalValue - h;\r\n          const backwardValue = objectiveFunction(currentVariables);\r\n          gradient = (this.currentState.functionValue - backwardValue) / h;\r\n          break;\r\n          \r\n        case 'central':\r\n          // f'(x) \u2248 (f(x+h) - f(x-h)) / (2h)\r\n          currentVariables[i].currentValue = originalValue + h;\r\n          const forwardVal = objectiveFunction(currentVariables);\r\n          currentVariables[i].currentValue = originalValue - h;\r\n          const backwardVal = objectiveFunction(currentVariables);\r\n          gradient = (forwardVal - backwardVal) / (2 * h);\r\n          break;\r\n      }\r\n      \r\n      // Restore original value\r\n      currentVariables[i].currentValue = originalValue;\r\n      \r\n      this.currentState.gradient[i] = gradient;\r\n      this.evaluationCount += this.parameters.gradientMethod === 'central' ? 2 : 1;\r\n    }\r\n    \r\n    // Compute gradient norm\r\n    this.currentState.gradientNorm = Math.sqrt(\r\n      this.currentState.gradient.reduce((sum, g) => sum + g * g, 0)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Update solution based on gradient descent variant\r\n   */\r\n  private async updateSolution(\r\n    problem: OptimizationProblem,\r\n    objectiveFunction: ObjectiveFunctionType,\r\n    constraintFunctions: ConstraintFunctionType[]\r\n  ): Promise<void> {\r\n    if (!this.currentState) return;\r\n    \r\n    const stepDirection = this.computeStepDirection();\r\n    \r\n    // Determine step size\r\n    let stepSize = this.currentState.learningRate;\r\n    if (this.parameters.lineSearch) {\r\n      stepSize = await this.performLineSearch(problem, objectiveFunction, constraintFunctions, stepDirection);\r\n    }\r\n    \r\n    // Update solution\r\n    const newVariables: { [variableId: string]: number | string } = {};\r\n    \r\n    for (let i = 0; i < problem.variables.length; i++) {\r\n      const variable = problem.variables[i];\r\n      const currentValue = this.currentState.solution.variables[variable.id];\r\n      \r\n      if (variable.discreteValues && variable.discreteValues.length > 0) {\r\n        // Keep discrete variables unchanged\r\n        newVariables[variable.id] = currentValue;\r\n      } else if (typeof currentValue === 'number') {\r\n        // Update continuous variables\r\n        const newValue = currentValue - stepSize * stepDirection[i];\r\n        \r\n        // Apply bounds\r\n        const min = typeof variable.bounds.minimum === 'number' ? variable.bounds.minimum : -Infinity;\r\n        const max = typeof variable.bounds.maximum === 'number' ? variable.bounds.maximum : Infinity;\r\n        newVariables[variable.id] = Math.max(min, Math.min(max, newValue));\r\n      } else {\r\n        newVariables[variable.id] = currentValue;\r\n      }\r\n    }\r\n    \r\n    // Create new solution\r\n    const newSolution: OptimizationSolution = {\r\n      ...this.currentState.solution,\r\n      id: this.generateSolutionId(),\r\n      variables: newVariables\r\n    };\r\n    \r\n    // Evaluate new solution\r\n    await this.evaluateSolution(newSolution, problem, objectiveFunction, constraintFunctions);\r\n    \r\n    // Update state\r\n    this.currentState.solution = newSolution;\r\n    this.currentState.stepSize = stepSize;\r\n    this.currentState.functionValue = newSolution.fitness;\r\n  }\r\n\r\n  /**\r\n   * Compute step direction based on variant\r\n   */\r\n  private computeStepDirection(): number[] {\r\n    if (!this.currentState) return [];\r\n    \r\n    const gradient = this.currentState.gradient;\r\n    const stepDirection = new Array(gradient.length);\r\n    \r\n    switch (this.parameters.variant) {\r\n      case 'standard':\r\n        // Standard gradient descent: d = -\u2207f\r\n        for (let i = 0; i < gradient.length; i++) {\r\n          stepDirection[i] = gradient[i];\r\n        }\r\n        break;\r\n        \r\n      case 'momentum':\r\n        // Momentum: v = \u03B2*v + \u2207f, d = v\r\n        for (let i = 0; i < gradient.length; i++) {\r\n          this.currentState.momentum[i] = this.parameters.momentumCoefficient * this.currentState.momentum[i] + gradient[i];\r\n          stepDirection[i] = this.currentState.momentum[i];\r\n        }\r\n        break;\r\n        \r\n      case 'adam':\r\n        // Adam optimizer\r\n        const beta1 = this.parameters.adamBeta1;\r\n        const beta2 = this.parameters.adamBeta2;\r\n        const epsilon = this.parameters.adamEpsilon;\r\n        const t = this.currentState.iteration + 1;\r\n        \r\n        for (let i = 0; i < gradient.length; i++) {\r\n          // Update biased first moment estimate\r\n          this.currentState.adamM[i] = beta1 * this.currentState.adamM[i] + (1 - beta1) * gradient[i];\r\n          \r\n          // Update biased second raw moment estimate\r\n          this.currentState.adamV[i] = beta2 * this.currentState.adamV[i] + (1 - beta2) * gradient[i] * gradient[i];\r\n          \r\n          // Compute bias-corrected first moment estimate\r\n          const mHat = this.currentState.adamM[i] / (1 - Math.pow(beta1, t));\r\n          \r\n          // Compute bias-corrected second raw moment estimate\r\n          const vHat = this.currentState.adamV[i] / (1 - Math.pow(beta2, t));\r\n          \r\n          // Update step direction\r\n          stepDirection[i] = mHat / (Math.sqrt(vHat) + epsilon);\r\n        }\r\n        break;\r\n        \r\n      case 'rmsprop':\r\n        // RMSprop\r\n        const decay = this.parameters.rmspropDecayRate;\r\n        const eps = this.parameters.rmspropEpsilon;\r\n        \r\n        for (let i = 0; i < gradient.length; i++) {\r\n          this.currentState.rmspropV[i] = decay * this.currentState.rmspropV[i] + (1 - decay) * gradient[i] * gradient[i];\r\n          stepDirection[i] = gradient[i] / (Math.sqrt(this.currentState.rmspropV[i]) + eps);\r\n        }\r\n        break;\r\n        \r\n      case 'adagrad':\r\n        // Adagrad\r\n        const adagradEps = this.parameters.adamEpsilon;\r\n        \r\n        for (let i = 0; i < gradient.length; i++) {\r\n          this.currentState.adagradG[i] += gradient[i] * gradient[i];\r\n          stepDirection[i] = gradient[i] / (Math.sqrt(this.currentState.adagradG[i]) + adagradEps);\r\n        }\r\n        break;\r\n        \r\n      default:\r\n        // Default to standard gradient descent\r\n        for (let i = 0; i < gradient.length; i++) {\r\n          stepDirection[i] = gradient[i];\r\n        }\r\n    }\r\n    \r\n    return stepDirection;\r\n  }\r\n\r\n  /**\r\n   * Perform line search to find optimal step size\r\n   */\r\n  private async performLineSearch(\r\n    problem: OptimizationProblem,\r\n    objectiveFunction: ObjectiveFunctionType,\r\n    constraintFunctions: ConstraintFunctionType[],\r\n    stepDirection: number[]\r\n  ): Promise<number> {\r\n    if (!this.currentState) return this.parameters.learningRate;\r\n    \r\n    switch (this.parameters.lineSearchMethod) {\r\n      case 'armijo':\r\n        return this.armijoLineSearch(problem, objectiveFunction, constraintFunctions, stepDirection);\r\n      case 'wolfe':\r\n        return this.wolfeLineSearch(problem, objectiveFunction, constraintFunctions, stepDirection);\r\n      case 'golden_section':\r\n        return this.goldenSectionLineSearch(problem, objectiveFunction, constraintFunctions, stepDirection);\r\n      default:\r\n        return this.parameters.learningRate;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Armijo line search\r\n   */\r\n  private async armijoLineSearch(\r\n    problem: OptimizationProblem,\r\n    objectiveFunction: ObjectiveFunctionType,\r\n    constraintFunctions: ConstraintFunctionType[],\r\n    stepDirection: number[]\r\n  ): Promise<number> {\r\n    if (!this.currentState) return this.parameters.learningRate;\r\n    \r\n    const c1 = 1e-4; // Armijo constant\r\n    let alpha = this.parameters.learningRate;\r\n    const maxBacktrack = 20;\r\n    \r\n    const currentValue = this.currentState.functionValue;\r\n    const gradientDotDirection = this.currentState.gradient.reduce((sum, g, i) => sum + g * stepDirection[i], 0);\r\n    \r\n    for (let i = 0; i < maxBacktrack; i++) {\r\n      // Test step\r\n      const testVariables = this.createTestVariables(problem, stepDirection, alpha);\r\n      const testValue = objectiveFunction(testVariables);\r\n      this.evaluationCount++;\r\n      \r\n      // Armijo condition\r\n      if (testValue <= currentValue - c1 * alpha * gradientDotDirection) {\r\n        return alpha;\r\n      }\r\n      \r\n      alpha *= 0.5; // Backtrack\r\n    }\r\n    \r\n    return alpha;\r\n  }\r\n\r\n  /**\r\n   * Wolfe line search (simplified)\r\n   */\r\n  private async wolfeLineSearch(\r\n    problem: OptimizationProblem,\r\n    objectiveFunction: ObjectiveFunctionType,\r\n    constraintFunctions: ConstraintFunctionType[],\r\n    stepDirection: number[]\r\n  ): Promise<number> {\r\n    // Simplified implementation - use Armijo for now\r\n    return this.armijoLineSearch(problem, objectiveFunction, constraintFunctions, stepDirection);\r\n  }\r\n\r\n  /**\r\n   * Golden section line search\r\n   */\r\n  private async goldenSectionLineSearch(\r\n    problem: OptimizationProblem,\r\n    objectiveFunction: ObjectiveFunctionType,\r\n    constraintFunctions: ConstraintFunctionType[],\r\n    stepDirection: number[]\r\n  ): Promise<number> {\r\n    const phi = (1 + Math.sqrt(5)) / 2; // Golden ratio\r\n    const resphi = 2 - phi;\r\n    \r\n    let a = 0;\r\n    let b = this.parameters.learningRate * 2;\r\n    const tol = 1e-6;\r\n    \r\n    // Find initial bracket\r\n    let x1 = a + resphi * (b - a);\r\n    let x2 = a + (1 - resphi) * (b - a);\r\n    \r\n    let f1 = this.evaluateAtStep(problem, objectiveFunction, stepDirection, x1);\r\n    let f2 = this.evaluateAtStep(problem, objectiveFunction, stepDirection, x2);\r\n    \r\n    while (Math.abs(b - a) > tol) {\r\n      if (f1 < f2) {\r\n        b = x2;\r\n        x2 = x1;\r\n        f2 = f1;\r\n        x1 = a + resphi * (b - a);\r\n        f1 = this.evaluateAtStep(problem, objectiveFunction, stepDirection, x1);\r\n      } else {\r\n        a = x1;\r\n        x1 = x2;\r\n        f1 = f2;\r\n        x2 = a + (1 - resphi) * (b - a);\r\n        f2 = this.evaluateAtStep(problem, objectiveFunction, stepDirection, x2);\r\n      }\r\n    }\r\n    \r\n    return (a + b) / 2;\r\n  }\r\n\r\n  /**\r\n   * Create test variables for line search\r\n   */\r\n  private createTestVariables(problem: OptimizationProblem, stepDirection: number[], alpha: number): OptimizationVariable[] {\r\n    if (!this.currentState) return [];\r\n    \r\n    return problem.variables.map((variable, i) => {\r\n      const currentValue = this.currentState!.solution.variables[variable.id];\r\n      \r\n      if (typeof currentValue === 'number') {\r\n        const newValue = currentValue - alpha * stepDirection[i];\r\n        const min = typeof variable.bounds.minimum === 'number' ? variable.bounds.minimum : -Infinity;\r\n        const max = typeof variable.bounds.maximum === 'number' ? variable.bounds.maximum : Infinity;\r\n        \r\n        return {\r\n          ...variable,\r\n          currentValue: Math.max(min, Math.min(max, newValue))\r\n        };\r\n      } else {\r\n        return {\r\n          ...variable,\r\n          currentValue\r\n        };\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Evaluate function at specific step size\r\n   */\r\n  private evaluateAtStep(\r\n    problem: OptimizationProblem,\r\n    objectiveFunction: ObjectiveFunctionType,\r\n    stepDirection: number[],\r\n    alpha: number\r\n  ): number {\r\n    const testVariables = this.createTestVariables(problem, stepDirection, alpha);\r\n    this.evaluationCount++;\r\n    return objectiveFunction(testVariables);\r\n  }\r\n\r\n  /**\r\n   * Project solution onto feasible region\r\n   */\r\n  private projectSolution(problem: OptimizationProblem): void {\r\n    if (!this.currentState) return;\r\n    \r\n    // Simple box constraint projection\r\n    const projectedVariables: { [variableId: string]: number | string } = {};\r\n    \r\n    for (const variable of problem.variables) {\r\n      const currentValue = this.currentState.solution.variables[variable.id];\r\n      \r\n      if (typeof currentValue === 'number') {\r\n        const min = typeof variable.bounds.minimum === 'number' ? variable.bounds.minimum : -Infinity;\r\n        const max = typeof variable.bounds.maximum === 'number' ? variable.bounds.maximum : Infinity;\r\n        projectedVariables[variable.id] = Math.max(min, Math.min(max, currentValue));\r\n      } else {\r\n        projectedVariables[variable.id] = currentValue;\r\n      }\r\n    }\r\n    \r\n    this.currentState.solution.variables = projectedVariables;\r\n  }\r\n\r\n  /**\r\n   * Adapt learning rate based on progress\r\n   */\r\n  private adaptLearningRate(): void {\r\n    if (!this.currentState) return;\r\n    \r\n    // Simple adaptive scheme based on gradient norm\r\n    if (this.currentState.gradientNorm > 1.0) {\r\n      // Large gradient - decrease learning rate\r\n      this.currentState.learningRate = Math.max(\r\n        this.parameters.learningRateMin,\r\n        this.currentState.learningRate * 0.9\r\n      );\r\n    } else if (this.currentState.gradientNorm < 0.1) {\r\n      // Small gradient - increase learning rate\r\n      this.currentState.learningRate = Math.min(\r\n        this.parameters.learningRateMax,\r\n        this.currentState.learningRate * 1.1\r\n      );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Check termination criteria\r\n   */\r\n  private shouldTerminate(): boolean {\r\n    if (!this.currentState) return true;\r\n    \r\n    // Maximum iterations\r\n    if (this.currentState.iteration >= this.parameters.maxIterations) {\r\n      return true;\r\n    }\r\n    \r\n    // Gradient tolerance\r\n    if (this.currentState.gradientNorm < this.parameters.gradientTolerance) {\r\n      return true;\r\n    }\r\n    \r\n    // Function value convergence\r\n    if (this.history.length >= 10) {\r\n      const recentHistory = this.history.slice(-10);\r\n      const functionImprovement = recentHistory[0].bestFitness - recentHistory[recentHistory.length - 1].bestFitness;\r\n      \r\n      if (Math.abs(functionImprovement) < this.parameters.convergenceTolerance) {\r\n        return true;\r\n      }\r\n    }\r\n    \r\n    return false;\r\n  }\r\n\r\n  /**\r\n   * Update optimization history\r\n   */\r\n  private updateHistory(): void {\r\n    if (!this.currentState) return;\r\n    \r\n    const history: IterationHistory = {\r\n      iteration: this.currentState.iteration,\r\n      bestFitness: this.bestSolution?.fitness || this.currentState.functionValue,\r\n      averageFitness: this.currentState.functionValue,\r\n      worstFitness: this.currentState.functionValue,\r\n      diversity: 0, // Not applicable for single solution\r\n      constraintViolations: this.currentState.solution.feasible ? 0 : 1,\r\n      timestamp: new Date()\r\n    };\r\n    \r\n    this.history.push(history);\r\n  }\r\n\r\n  /**\r\n   * Create optimization result\r\n   */\r\n  private createOptimizationResult(problem: OptimizationProblem, startTime: number): OptimizationResult {\r\n    const executionTime = performance.now() - startTime;\r\n    \r\n    const statistics: OptimizationStatistics = {\r\n      totalIterations: this.currentState?.iteration || 0,\r\n      totalEvaluations: this.evaluationCount,\r\n      convergenceIteration: this.currentState?.iteration || 0,\r\n      executionTime,\r\n      bestFitnessHistory: this.history.map(h => h.bestFitness),\r\n      averageFitnessHistory: this.history.map(h => h.averageFitness),\r\n      diversityHistory: this.history.map(h => h.diversity),\r\n      constraintViolationHistory: this.history.map(h => h.constraintViolations),\r\n      algorithmSpecificStats: {\r\n        variant: this.parameters.variant,\r\n        finalLearningRate: this.currentState?.learningRate || 0,\r\n        finalGradientNorm: this.currentState?.gradientNorm || 0,\r\n        finalStepSize: this.currentState?.stepSize || 0\r\n      }\r\n    };\r\n    \r\n    const optimizationHistory: OptimizationHistory = {\r\n      iterations: this.history,\r\n      populationHistory: [],\r\n      parameterHistory: [],\r\n      convergenceMetrics: []\r\n    };\r\n    \r\n    return {\r\n      problemId: problem.id,\r\n      status: OptimizationStatus.CONVERGED,\r\n      bestSolution: this.bestSolution!,\r\n      statistics,\r\n      history: optimizationHistory,\r\n      analysis: {},\r\n      recommendations: [],\r\n      warnings: [],\r\n      errors: []\r\n    };\r\n  }\r\n\r\n  // Utility methods\r\n  private generateSolutionId(): string {\r\n    return `gd_sol_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\r\n  }\r\n\r\n  private createSeededRandom(seed: number): () => number {\r\n    let state = seed;\r\n    return () => {\r\n      state = (state * 9301 + 49297) % 233280;\r\n      return state / 233280;\r\n    };\r\n  }\r\n}\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "aada9f28fc0bbc24d3345be3f9f99b95b0b42bc9"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_hr0h2ihfp = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_hr0h2ihfp();
cov_hr0h2ihfp().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_hr0h2ihfp().s[1]++;
exports.GradientDescent = void 0;
const SystemOptimizationTypes_1 =
/* istanbul ignore next */
(cov_hr0h2ihfp().s[2]++, require("../types/SystemOptimizationTypes"));
/**
 * Gradient Descent optimizer for continuous optimization problems
 */
class GradientDescent {
  constructor(parameters) {
    /* istanbul ignore next */
    cov_hr0h2ihfp().f[0]++;
    cov_hr0h2ihfp().s[3]++;
    this.currentState = null;
    /* istanbul ignore next */
    cov_hr0h2ihfp().s[4]++;
    this.bestSolution = null;
    /* istanbul ignore next */
    cov_hr0h2ihfp().s[5]++;
    this.history = [];
    /* istanbul ignore next */
    cov_hr0h2ihfp().s[6]++;
    this.evaluationCount = 0;
    /* istanbul ignore next */
    cov_hr0h2ihfp().s[7]++;
    this.parameters = {
      maxIterations: 1000,
      learningRate: 0.01,
      learningRateMin: 1e-6,
      learningRateMax: 1.0,
      variant: 'adam',
      momentumCoefficient: 0.9,
      adamBeta1: 0.9,
      adamBeta2: 0.999,
      adamEpsilon: 1e-8,
      rmspropDecayRate: 0.9,
      rmspropEpsilon: 1e-8,
      gradientMethod: 'central',
      finiteDifferenceStep: 1e-6,
      adaptiveLearningRate: true,
      lineSearch: true,
      lineSearchMethod: 'armijo',
      constraintHandling: 'projection',
      penaltyCoefficient: 1000,
      convergenceTolerance: 1e-6,
      gradientTolerance: 1e-6,
      ...parameters
    };
    // Initialize random number generator
    /* istanbul ignore next */
    cov_hr0h2ihfp().s[8]++;
    if (this.parameters.seedValue !== undefined) {
      /* istanbul ignore next */
      cov_hr0h2ihfp().b[0][0]++;
      cov_hr0h2ihfp().s[9]++;
      this.random = this.createSeededRandom(this.parameters.seedValue);
    } else {
      /* istanbul ignore next */
      cov_hr0h2ihfp().b[0][1]++;
      cov_hr0h2ihfp().s[10]++;
      this.random = Math.random;
    }
  }
  /**
   * Main optimization method
   */
  async optimize(problem, objectiveFunction, constraintFunctions) {
    /* istanbul ignore next */
    cov_hr0h2ihfp().f[1]++;
    const startTime =
    /* istanbul ignore next */
    (cov_hr0h2ihfp().s[11]++, performance.now());
    /* istanbul ignore next */
    cov_hr0h2ihfp().s[12]++;
    try {
      /* istanbul ignore next */
      cov_hr0h2ihfp().s[13]++;
      // Validate problem for gradient descent
      this.validateProblem(problem);
      // Initialize algorithm
      /* istanbul ignore next */
      cov_hr0h2ihfp().s[14]++;
      this.initializeAlgorithm(problem);
      // Create initial solution
      /* istanbul ignore next */
      cov_hr0h2ihfp().s[15]++;
      await this.createInitialSolution(problem, objectiveFunction, constraintFunctions);
      // Main optimization loop
      /* istanbul ignore next */
      cov_hr0h2ihfp().s[16]++;
      while (!this.shouldTerminate()) {
        /* istanbul ignore next */
        cov_hr0h2ihfp().s[17]++;
        await this.performIteration(problem, objectiveFunction, constraintFunctions);
        /* istanbul ignore next */
        cov_hr0h2ihfp().s[18]++;
        this.updateHistory();
        /* istanbul ignore next */
        cov_hr0h2ihfp().s[19]++;
        if (this.parameters.adaptiveLearningRate) {
          /* istanbul ignore next */
          cov_hr0h2ihfp().b[1][0]++;
          cov_hr0h2ihfp().s[20]++;
          this.adaptLearningRate();
        } else
        /* istanbul ignore next */
        {
          cov_hr0h2ihfp().b[1][1]++;
        }
      }
      // Create final result
      /* istanbul ignore next */
      cov_hr0h2ihfp().s[21]++;
      return this.createOptimizationResult(problem, startTime);
    } catch (error) {
      /* istanbul ignore next */
      cov_hr0h2ihfp().s[22]++;
      console.error('Gradient descent optimization failed:', error);
      /* istanbul ignore next */
      cov_hr0h2ihfp().s[23]++;
      throw error;
    }
  }
  /**
   * Validate problem for gradient descent
   */
  validateProblem(problem) {
    /* istanbul ignore next */
    cov_hr0h2ihfp().f[2]++;
    // Check for continuous variables only
    const hasDiscreteVariables =
    /* istanbul ignore next */
    (cov_hr0h2ihfp().s[24]++, problem.variables.some(v => {
      /* istanbul ignore next */
      cov_hr0h2ihfp().f[3]++;
      cov_hr0h2ihfp().s[25]++;
      return /* istanbul ignore next */(cov_hr0h2ihfp().b[2][0]++, v.discreteValues) &&
      /* istanbul ignore next */
      (cov_hr0h2ihfp().b[2][1]++, v.discreteValues.length > 0);
    }));
    /* istanbul ignore next */
    cov_hr0h2ihfp().s[26]++;
    if (hasDiscreteVariables) {
      /* istanbul ignore next */
      cov_hr0h2ihfp().b[3][0]++;
      cov_hr0h2ihfp().s[27]++;
      console.warn('Gradient descent works best with continuous variables. Discrete variables will be treated as continuous.');
    } else
    /* istanbul ignore next */
    {
      cov_hr0h2ihfp().b[3][1]++;
    }
  }
  /**
   * Initialize algorithm state
   */
  initializeAlgorithm(problem) {
    /* istanbul ignore next */
    cov_hr0h2ihfp().f[4]++;
    cov_hr0h2ihfp().s[28]++;
    this.currentState = null;
    /* istanbul ignore next */
    cov_hr0h2ihfp().s[29]++;
    this.bestSolution = null;
    /* istanbul ignore next */
    cov_hr0h2ihfp().s[30]++;
    this.history = [];
    /* istanbul ignore next */
    cov_hr0h2ihfp().s[31]++;
    this.evaluationCount = 0;
    /* istanbul ignore next */
    cov_hr0h2ihfp().s[32]++;
    console.log(`Initializing Gradient Descent (${this.parameters.variant}) with learning rate: ${this.parameters.learningRate}`);
  }
  /**
   * Create initial solution
   */
  async createInitialSolution(problem, objectiveFunction, constraintFunctions) {
    /* istanbul ignore next */
    cov_hr0h2ihfp().f[5]++;
    // Generate initial solution
    const initialSolution =
    /* istanbul ignore next */
    (cov_hr0h2ihfp().s[33]++, this.createRandomSolution(problem));
    /* istanbul ignore next */
    cov_hr0h2ihfp().s[34]++;
    await this.evaluateSolution(initialSolution, problem, objectiveFunction, constraintFunctions);
    // Initialize gradient state
    const numVariables =
    /* istanbul ignore next */
    (cov_hr0h2ihfp().s[35]++, problem.variables.length);
    /* istanbul ignore next */
    cov_hr0h2ihfp().s[36]++;
    this.currentState = {
      solution: initialSolution,
      gradient: new Array(numVariables).fill(0),
      momentum: new Array(numVariables).fill(0),
      adamM: new Array(numVariables).fill(0),
      adamV: new Array(numVariables).fill(0),
      rmspropV: new Array(numVariables).fill(0),
      adagradG: new Array(numVariables).fill(0),
      iteration: 0,
      learningRate: this.parameters.learningRate,
      gradientNorm: 0,
      stepSize: 0,
      functionValue: initialSolution.fitness
    };
    /* istanbul ignore next */
    cov_hr0h2ihfp().s[37]++;
    this.bestSolution = {
      ...initialSolution
    };
  }
  /**
   * Create a random solution
   */
  createRandomSolution(problem) {
    /* istanbul ignore next */
    cov_hr0h2ihfp().f[6]++;
    const variables =
    /* istanbul ignore next */
    (cov_hr0h2ihfp().s[38]++, {});
    /* istanbul ignore next */
    cov_hr0h2ihfp().s[39]++;
    for (const variable of problem.variables) {
      /* istanbul ignore next */
      cov_hr0h2ihfp().s[40]++;
      if (
      /* istanbul ignore next */
      (cov_hr0h2ihfp().b[5][0]++, variable.discreteValues) &&
      /* istanbul ignore next */
      (cov_hr0h2ihfp().b[5][1]++, variable.discreteValues.length > 0)) {
        /* istanbul ignore next */
        cov_hr0h2ihfp().b[4][0]++;
        // Discrete variable - select random value
        const randomIndex =
        /* istanbul ignore next */
        (cov_hr0h2ihfp().s[41]++, Math.floor(this.random() * variable.discreteValues.length));
        /* istanbul ignore next */
        cov_hr0h2ihfp().s[42]++;
        variables[variable.id] = variable.discreteValues[randomIndex];
      } else {
        /* istanbul ignore next */
        cov_hr0h2ihfp().b[4][1]++;
        // Continuous variable
        const min =
        /* istanbul ignore next */
        (cov_hr0h2ihfp().s[43]++, typeof variable.bounds.minimum === 'number' ?
        /* istanbul ignore next */
        (cov_hr0h2ihfp().b[6][0]++, variable.bounds.minimum) :
        /* istanbul ignore next */
        (cov_hr0h2ihfp().b[6][1]++, 0));
        const max =
        /* istanbul ignore next */
        (cov_hr0h2ihfp().s[44]++, typeof variable.bounds.maximum === 'number' ?
        /* istanbul ignore next */
        (cov_hr0h2ihfp().b[7][0]++, variable.bounds.maximum) :
        /* istanbul ignore next */
        (cov_hr0h2ihfp().b[7][1]++, 1));
        /* istanbul ignore next */
        cov_hr0h2ihfp().s[45]++;
        variables[variable.id] = min + this.random() * (max - min);
      }
    }
    /* istanbul ignore next */
    cov_hr0h2ihfp().s[46]++;
    return {
      id: this.generateSolutionId(),
      variables,
      objectiveValues: {},
      constraintViolations: [],
      feasible: true,
      fitness: 0,
      systemConfiguration: problem.systemConfiguration,
      performanceMetrics: {}
    };
  }
  /**
   * Evaluate solution fitness and constraints
   */
  async evaluateSolution(solution, problem, objectiveFunction, constraintFunctions) {
    /* istanbul ignore next */
    cov_hr0h2ihfp().f[7]++;
    cov_hr0h2ihfp().s[47]++;
    try {
      // Convert solution to optimization variables
      const variables =
      /* istanbul ignore next */
      (cov_hr0h2ihfp().s[48]++, this.solutionToVariables(solution, problem.variables));
      // Evaluate objective function
      const objectiveValue =
      /* istanbul ignore next */
      (cov_hr0h2ihfp().s[49]++, objectiveFunction(variables));
      /* istanbul ignore next */
      cov_hr0h2ihfp().s[50]++;
      solution.fitness = objectiveValue;
      // Store objective values
      /* istanbul ignore next */
      cov_hr0h2ihfp().s[51]++;
      if (problem.objectives.objectives.length > 0) {
        /* istanbul ignore next */
        cov_hr0h2ihfp().b[8][0]++;
        cov_hr0h2ihfp().s[52]++;
        solution.objectiveValues[problem.objectives.objectives[0].id] = objectiveValue;
      } else
      /* istanbul ignore next */
      {
        cov_hr0h2ihfp().b[8][1]++;
      }
      // Evaluate constraints
      cov_hr0h2ihfp().s[53]++;
      solution.constraintViolations = [];
      /* istanbul ignore next */
      cov_hr0h2ihfp().s[54]++;
      for (let i =
      /* istanbul ignore next */
      (cov_hr0h2ihfp().s[55]++, 0); i < constraintFunctions.length; i++) {
        const violation =
        /* istanbul ignore next */
        (cov_hr0h2ihfp().s[56]++, constraintFunctions[i](variables));
        /* istanbul ignore next */
        cov_hr0h2ihfp().s[57]++;
        solution.constraintViolations.push({
          constraintId: `constraint_${i}`,
          violationType: violation > 0 ?
          /* istanbul ignore next */
          (cov_hr0h2ihfp().b[9][0]++, 'inequality') :
          /* istanbul ignore next */
          (cov_hr0h2ihfp().b[9][1]++, 'boundary'),
          currentValue: violation,
          requiredValue: 0,
          severity: violation > 0 ?
          /* istanbul ignore next */
          (cov_hr0h2ihfp().b[10][0]++, 'major') :
          /* istanbul ignore next */
          (cov_hr0h2ihfp().b[10][1]++, 'minor'),
          penalty: violation > 0 ?
          /* istanbul ignore next */
          (cov_hr0h2ihfp().b[11][0]++, violation * this.parameters.penaltyCoefficient) :
          /* istanbul ignore next */
          (cov_hr0h2ihfp().b[11][1]++, 0)
        });
      }
      // Check feasibility
      /* istanbul ignore next */
      cov_hr0h2ihfp().s[58]++;
      solution.feasible = solution.constraintViolations.every(v => {
        /* istanbul ignore next */
        cov_hr0h2ihfp().f[8]++;
        cov_hr0h2ihfp().s[59]++;
        return v.currentValue <= 0;
      });
      // Apply constraint handling
      /* istanbul ignore next */
      cov_hr0h2ihfp().s[60]++;
      if (
      /* istanbul ignore next */
      (cov_hr0h2ihfp().b[13][0]++, !solution.feasible) &&
      /* istanbul ignore next */
      (cov_hr0h2ihfp().b[13][1]++, this.parameters.constraintHandling === 'penalty')) {
        /* istanbul ignore next */
        cov_hr0h2ihfp().b[12][0]++;
        const totalPenalty =
        /* istanbul ignore next */
        (cov_hr0h2ihfp().s[61]++, solution.constraintViolations.filter(v => {
          /* istanbul ignore next */
          cov_hr0h2ihfp().f[9]++;
          cov_hr0h2ihfp().s[62]++;
          return v.currentValue > 0;
        }).reduce((sum, v) => {
          /* istanbul ignore next */
          cov_hr0h2ihfp().f[10]++;
          cov_hr0h2ihfp().s[63]++;
          return sum + v.penalty;
        }, 0));
        /* istanbul ignore next */
        cov_hr0h2ihfp().s[64]++;
        solution.fitness += totalPenalty;
      } else
      /* istanbul ignore next */
      {
        cov_hr0h2ihfp().b[12][1]++;
      }
      cov_hr0h2ihfp().s[65]++;
      this.evaluationCount++;
    } catch (error) {
      /* istanbul ignore next */
      cov_hr0h2ihfp().s[66]++;
      console.error('Error evaluating solution:', error);
      /* istanbul ignore next */
      cov_hr0h2ihfp().s[67]++;
      solution.fitness = Number.MAX_VALUE;
      /* istanbul ignore next */
      cov_hr0h2ihfp().s[68]++;
      solution.feasible = false;
    }
  }
  /**
   * Convert solution to optimization variables
   */
  solutionToVariables(solution, variableTemplates) {
    /* istanbul ignore next */
    cov_hr0h2ihfp().f[11]++;
    cov_hr0h2ihfp().s[69]++;
    return variableTemplates.map(template => {
      /* istanbul ignore next */
      cov_hr0h2ihfp().f[12]++;
      cov_hr0h2ihfp().s[70]++;
      return {
        ...template,
        currentValue: solution.variables[template.id]
      };
    });
  }
  /**
   * Perform one iteration of gradient descent
   */
  async performIteration(problem, objectiveFunction, constraintFunctions) {
    /* istanbul ignore next */
    cov_hr0h2ihfp().f[13]++;
    cov_hr0h2ihfp().s[71]++;
    if (!this.currentState) {
      /* istanbul ignore next */
      cov_hr0h2ihfp().b[14][0]++;
      cov_hr0h2ihfp().s[72]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_hr0h2ihfp().b[14][1]++;
    }
    // Compute gradient
    cov_hr0h2ihfp().s[73]++;
    await this.computeGradient(problem, objectiveFunction, constraintFunctions);
    // Update solution based on variant
    /* istanbul ignore next */
    cov_hr0h2ihfp().s[74]++;
    await this.updateSolution(problem, objectiveFunction, constraintFunctions);
    // Apply constraints if using projection
    /* istanbul ignore next */
    cov_hr0h2ihfp().s[75]++;
    if (this.parameters.constraintHandling === 'projection') {
      /* istanbul ignore next */
      cov_hr0h2ihfp().b[15][0]++;
      cov_hr0h2ihfp().s[76]++;
      this.projectSolution(problem);
    } else
    /* istanbul ignore next */
    {
      cov_hr0h2ihfp().b[15][1]++;
    }
    // Update best solution
    cov_hr0h2ihfp().s[77]++;
    if (this.currentState.solution.fitness < this.bestSolution.fitness) {
      /* istanbul ignore next */
      cov_hr0h2ihfp().b[16][0]++;
      cov_hr0h2ihfp().s[78]++;
      this.bestSolution = {
        ...this.currentState.solution
      };
    } else
    /* istanbul ignore next */
    {
      cov_hr0h2ihfp().b[16][1]++;
    }
    cov_hr0h2ihfp().s[79]++;
    this.currentState.iteration++;
  }
  /**
   * Compute numerical gradient
   */
  async computeGradient(problem, objectiveFunction, constraintFunctions) {
    /* istanbul ignore next */
    cov_hr0h2ihfp().f[14]++;
    cov_hr0h2ihfp().s[80]++;
    if (!this.currentState) {
      /* istanbul ignore next */
      cov_hr0h2ihfp().b[17][0]++;
      cov_hr0h2ihfp().s[81]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_hr0h2ihfp().b[17][1]++;
    }
    const currentVariables =
    /* istanbul ignore next */
    (cov_hr0h2ihfp().s[82]++, this.solutionToVariables(this.currentState.solution, problem.variables));
    const h =
    /* istanbul ignore next */
    (cov_hr0h2ihfp().s[83]++, this.parameters.finiteDifferenceStep);
    /* istanbul ignore next */
    cov_hr0h2ihfp().s[84]++;
    for (let i =
    /* istanbul ignore next */
    (cov_hr0h2ihfp().s[85]++, 0); i < problem.variables.length; i++) {
      const variable =
      /* istanbul ignore next */
      (cov_hr0h2ihfp().s[86]++, problem.variables[i]);
      // Skip discrete variables
      /* istanbul ignore next */
      cov_hr0h2ihfp().s[87]++;
      if (
      /* istanbul ignore next */
      (cov_hr0h2ihfp().b[19][0]++, variable.discreteValues) &&
      /* istanbul ignore next */
      (cov_hr0h2ihfp().b[19][1]++, variable.discreteValues.length > 0)) {
        /* istanbul ignore next */
        cov_hr0h2ihfp().b[18][0]++;
        cov_hr0h2ihfp().s[88]++;
        this.currentState.gradient[i] = 0;
        /* istanbul ignore next */
        cov_hr0h2ihfp().s[89]++;
        continue;
      } else
      /* istanbul ignore next */
      {
        cov_hr0h2ihfp().b[18][1]++;
      }
      const originalValue =
      /* istanbul ignore next */
      (cov_hr0h2ihfp().s[90]++, currentVariables[i].currentValue);
      let gradient =
      /* istanbul ignore next */
      (cov_hr0h2ihfp().s[91]++, 0);
      /* istanbul ignore next */
      cov_hr0h2ihfp().s[92]++;
      switch (this.parameters.gradientMethod) {
        case 'forward':
          /* istanbul ignore next */
          cov_hr0h2ihfp().b[20][0]++;
          cov_hr0h2ihfp().s[93]++;
          // f'(x) ≈ (f(x+h) - f(x)) / h
          currentVariables[i].currentValue = originalValue + h;
          const forwardValue =
          /* istanbul ignore next */
          (cov_hr0h2ihfp().s[94]++, objectiveFunction(currentVariables));
          /* istanbul ignore next */
          cov_hr0h2ihfp().s[95]++;
          gradient = (forwardValue - this.currentState.functionValue) / h;
          /* istanbul ignore next */
          cov_hr0h2ihfp().s[96]++;
          break;
        case 'backward':
          /* istanbul ignore next */
          cov_hr0h2ihfp().b[20][1]++;
          cov_hr0h2ihfp().s[97]++;
          // f'(x) ≈ (f(x) - f(x-h)) / h
          currentVariables[i].currentValue = originalValue - h;
          const backwardValue =
          /* istanbul ignore next */
          (cov_hr0h2ihfp().s[98]++, objectiveFunction(currentVariables));
          /* istanbul ignore next */
          cov_hr0h2ihfp().s[99]++;
          gradient = (this.currentState.functionValue - backwardValue) / h;
          /* istanbul ignore next */
          cov_hr0h2ihfp().s[100]++;
          break;
        case 'central':
          /* istanbul ignore next */
          cov_hr0h2ihfp().b[20][2]++;
          cov_hr0h2ihfp().s[101]++;
          // f'(x) ≈ (f(x+h) - f(x-h)) / (2h)
          currentVariables[i].currentValue = originalValue + h;
          const forwardVal =
          /* istanbul ignore next */
          (cov_hr0h2ihfp().s[102]++, objectiveFunction(currentVariables));
          /* istanbul ignore next */
          cov_hr0h2ihfp().s[103]++;
          currentVariables[i].currentValue = originalValue - h;
          const backwardVal =
          /* istanbul ignore next */
          (cov_hr0h2ihfp().s[104]++, objectiveFunction(currentVariables));
          /* istanbul ignore next */
          cov_hr0h2ihfp().s[105]++;
          gradient = (forwardVal - backwardVal) / (2 * h);
          /* istanbul ignore next */
          cov_hr0h2ihfp().s[106]++;
          break;
      }
      // Restore original value
      /* istanbul ignore next */
      cov_hr0h2ihfp().s[107]++;
      currentVariables[i].currentValue = originalValue;
      /* istanbul ignore next */
      cov_hr0h2ihfp().s[108]++;
      this.currentState.gradient[i] = gradient;
      /* istanbul ignore next */
      cov_hr0h2ihfp().s[109]++;
      this.evaluationCount += this.parameters.gradientMethod === 'central' ?
      /* istanbul ignore next */
      (cov_hr0h2ihfp().b[21][0]++, 2) :
      /* istanbul ignore next */
      (cov_hr0h2ihfp().b[21][1]++, 1);
    }
    // Compute gradient norm
    /* istanbul ignore next */
    cov_hr0h2ihfp().s[110]++;
    this.currentState.gradientNorm = Math.sqrt(this.currentState.gradient.reduce((sum, g) => {
      /* istanbul ignore next */
      cov_hr0h2ihfp().f[15]++;
      cov_hr0h2ihfp().s[111]++;
      return sum + g * g;
    }, 0));
  }
  /**
   * Update solution based on gradient descent variant
   */
  async updateSolution(problem, objectiveFunction, constraintFunctions) {
    /* istanbul ignore next */
    cov_hr0h2ihfp().f[16]++;
    cov_hr0h2ihfp().s[112]++;
    if (!this.currentState) {
      /* istanbul ignore next */
      cov_hr0h2ihfp().b[22][0]++;
      cov_hr0h2ihfp().s[113]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_hr0h2ihfp().b[22][1]++;
    }
    const stepDirection =
    /* istanbul ignore next */
    (cov_hr0h2ihfp().s[114]++, this.computeStepDirection());
    // Determine step size
    let stepSize =
    /* istanbul ignore next */
    (cov_hr0h2ihfp().s[115]++, this.currentState.learningRate);
    /* istanbul ignore next */
    cov_hr0h2ihfp().s[116]++;
    if (this.parameters.lineSearch) {
      /* istanbul ignore next */
      cov_hr0h2ihfp().b[23][0]++;
      cov_hr0h2ihfp().s[117]++;
      stepSize = await this.performLineSearch(problem, objectiveFunction, constraintFunctions, stepDirection);
    } else
    /* istanbul ignore next */
    {
      cov_hr0h2ihfp().b[23][1]++;
    }
    // Update solution
    const newVariables =
    /* istanbul ignore next */
    (cov_hr0h2ihfp().s[118]++, {});
    /* istanbul ignore next */
    cov_hr0h2ihfp().s[119]++;
    for (let i =
    /* istanbul ignore next */
    (cov_hr0h2ihfp().s[120]++, 0); i < problem.variables.length; i++) {
      const variable =
      /* istanbul ignore next */
      (cov_hr0h2ihfp().s[121]++, problem.variables[i]);
      const currentValue =
      /* istanbul ignore next */
      (cov_hr0h2ihfp().s[122]++, this.currentState.solution.variables[variable.id]);
      /* istanbul ignore next */
      cov_hr0h2ihfp().s[123]++;
      if (
      /* istanbul ignore next */
      (cov_hr0h2ihfp().b[25][0]++, variable.discreteValues) &&
      /* istanbul ignore next */
      (cov_hr0h2ihfp().b[25][1]++, variable.discreteValues.length > 0)) {
        /* istanbul ignore next */
        cov_hr0h2ihfp().b[24][0]++;
        cov_hr0h2ihfp().s[124]++;
        // Keep discrete variables unchanged
        newVariables[variable.id] = currentValue;
      } else {
        /* istanbul ignore next */
        cov_hr0h2ihfp().b[24][1]++;
        cov_hr0h2ihfp().s[125]++;
        if (typeof currentValue === 'number') {
          /* istanbul ignore next */
          cov_hr0h2ihfp().b[26][0]++;
          // Update continuous variables
          const newValue =
          /* istanbul ignore next */
          (cov_hr0h2ihfp().s[126]++, currentValue - stepSize * stepDirection[i]);
          // Apply bounds
          const min =
          /* istanbul ignore next */
          (cov_hr0h2ihfp().s[127]++, typeof variable.bounds.minimum === 'number' ?
          /* istanbul ignore next */
          (cov_hr0h2ihfp().b[27][0]++, variable.bounds.minimum) :
          /* istanbul ignore next */
          (cov_hr0h2ihfp().b[27][1]++, -Infinity));
          const max =
          /* istanbul ignore next */
          (cov_hr0h2ihfp().s[128]++, typeof variable.bounds.maximum === 'number' ?
          /* istanbul ignore next */
          (cov_hr0h2ihfp().b[28][0]++, variable.bounds.maximum) :
          /* istanbul ignore next */
          (cov_hr0h2ihfp().b[28][1]++, Infinity));
          /* istanbul ignore next */
          cov_hr0h2ihfp().s[129]++;
          newVariables[variable.id] = Math.max(min, Math.min(max, newValue));
        } else {
          /* istanbul ignore next */
          cov_hr0h2ihfp().b[26][1]++;
          cov_hr0h2ihfp().s[130]++;
          newVariables[variable.id] = currentValue;
        }
      }
    }
    // Create new solution
    const newSolution =
    /* istanbul ignore next */
    (cov_hr0h2ihfp().s[131]++, {
      ...this.currentState.solution,
      id: this.generateSolutionId(),
      variables: newVariables
    });
    // Evaluate new solution
    /* istanbul ignore next */
    cov_hr0h2ihfp().s[132]++;
    await this.evaluateSolution(newSolution, problem, objectiveFunction, constraintFunctions);
    // Update state
    /* istanbul ignore next */
    cov_hr0h2ihfp().s[133]++;
    this.currentState.solution = newSolution;
    /* istanbul ignore next */
    cov_hr0h2ihfp().s[134]++;
    this.currentState.stepSize = stepSize;
    /* istanbul ignore next */
    cov_hr0h2ihfp().s[135]++;
    this.currentState.functionValue = newSolution.fitness;
  }
  /**
   * Compute step direction based on variant
   */
  computeStepDirection() {
    /* istanbul ignore next */
    cov_hr0h2ihfp().f[17]++;
    cov_hr0h2ihfp().s[136]++;
    if (!this.currentState) {
      /* istanbul ignore next */
      cov_hr0h2ihfp().b[29][0]++;
      cov_hr0h2ihfp().s[137]++;
      return [];
    } else
    /* istanbul ignore next */
    {
      cov_hr0h2ihfp().b[29][1]++;
    }
    const gradient =
    /* istanbul ignore next */
    (cov_hr0h2ihfp().s[138]++, this.currentState.gradient);
    const stepDirection =
    /* istanbul ignore next */
    (cov_hr0h2ihfp().s[139]++, new Array(gradient.length));
    /* istanbul ignore next */
    cov_hr0h2ihfp().s[140]++;
    switch (this.parameters.variant) {
      case 'standard':
        /* istanbul ignore next */
        cov_hr0h2ihfp().b[30][0]++;
        cov_hr0h2ihfp().s[141]++;
        // Standard gradient descent: d = -∇f
        for (let i =
        /* istanbul ignore next */
        (cov_hr0h2ihfp().s[142]++, 0); i < gradient.length; i++) {
          /* istanbul ignore next */
          cov_hr0h2ihfp().s[143]++;
          stepDirection[i] = gradient[i];
        }
        /* istanbul ignore next */
        cov_hr0h2ihfp().s[144]++;
        break;
      case 'momentum':
        /* istanbul ignore next */
        cov_hr0h2ihfp().b[30][1]++;
        cov_hr0h2ihfp().s[145]++;
        // Momentum: v = β*v + ∇f, d = v
        for (let i =
        /* istanbul ignore next */
        (cov_hr0h2ihfp().s[146]++, 0); i < gradient.length; i++) {
          /* istanbul ignore next */
          cov_hr0h2ihfp().s[147]++;
          this.currentState.momentum[i] = this.parameters.momentumCoefficient * this.currentState.momentum[i] + gradient[i];
          /* istanbul ignore next */
          cov_hr0h2ihfp().s[148]++;
          stepDirection[i] = this.currentState.momentum[i];
        }
        /* istanbul ignore next */
        cov_hr0h2ihfp().s[149]++;
        break;
      case 'adam':
        /* istanbul ignore next */
        cov_hr0h2ihfp().b[30][2]++;
        // Adam optimizer
        const beta1 =
        /* istanbul ignore next */
        (cov_hr0h2ihfp().s[150]++, this.parameters.adamBeta1);
        const beta2 =
        /* istanbul ignore next */
        (cov_hr0h2ihfp().s[151]++, this.parameters.adamBeta2);
        const epsilon =
        /* istanbul ignore next */
        (cov_hr0h2ihfp().s[152]++, this.parameters.adamEpsilon);
        const t =
        /* istanbul ignore next */
        (cov_hr0h2ihfp().s[153]++, this.currentState.iteration + 1);
        /* istanbul ignore next */
        cov_hr0h2ihfp().s[154]++;
        for (let i =
        /* istanbul ignore next */
        (cov_hr0h2ihfp().s[155]++, 0); i < gradient.length; i++) {
          /* istanbul ignore next */
          cov_hr0h2ihfp().s[156]++;
          // Update biased first moment estimate
          this.currentState.adamM[i] = beta1 * this.currentState.adamM[i] + (1 - beta1) * gradient[i];
          // Update biased second raw moment estimate
          /* istanbul ignore next */
          cov_hr0h2ihfp().s[157]++;
          this.currentState.adamV[i] = beta2 * this.currentState.adamV[i] + (1 - beta2) * gradient[i] * gradient[i];
          // Compute bias-corrected first moment estimate
          const mHat =
          /* istanbul ignore next */
          (cov_hr0h2ihfp().s[158]++, this.currentState.adamM[i] / (1 - Math.pow(beta1, t)));
          // Compute bias-corrected second raw moment estimate
          const vHat =
          /* istanbul ignore next */
          (cov_hr0h2ihfp().s[159]++, this.currentState.adamV[i] / (1 - Math.pow(beta2, t)));
          // Update step direction
          /* istanbul ignore next */
          cov_hr0h2ihfp().s[160]++;
          stepDirection[i] = mHat / (Math.sqrt(vHat) + epsilon);
        }
        /* istanbul ignore next */
        cov_hr0h2ihfp().s[161]++;
        break;
      case 'rmsprop':
        /* istanbul ignore next */
        cov_hr0h2ihfp().b[30][3]++;
        // RMSprop
        const decay =
        /* istanbul ignore next */
        (cov_hr0h2ihfp().s[162]++, this.parameters.rmspropDecayRate);
        const eps =
        /* istanbul ignore next */
        (cov_hr0h2ihfp().s[163]++, this.parameters.rmspropEpsilon);
        /* istanbul ignore next */
        cov_hr0h2ihfp().s[164]++;
        for (let i =
        /* istanbul ignore next */
        (cov_hr0h2ihfp().s[165]++, 0); i < gradient.length; i++) {
          /* istanbul ignore next */
          cov_hr0h2ihfp().s[166]++;
          this.currentState.rmspropV[i] = decay * this.currentState.rmspropV[i] + (1 - decay) * gradient[i] * gradient[i];
          /* istanbul ignore next */
          cov_hr0h2ihfp().s[167]++;
          stepDirection[i] = gradient[i] / (Math.sqrt(this.currentState.rmspropV[i]) + eps);
        }
        /* istanbul ignore next */
        cov_hr0h2ihfp().s[168]++;
        break;
      case 'adagrad':
        /* istanbul ignore next */
        cov_hr0h2ihfp().b[30][4]++;
        // Adagrad
        const adagradEps =
        /* istanbul ignore next */
        (cov_hr0h2ihfp().s[169]++, this.parameters.adamEpsilon);
        /* istanbul ignore next */
        cov_hr0h2ihfp().s[170]++;
        for (let i =
        /* istanbul ignore next */
        (cov_hr0h2ihfp().s[171]++, 0); i < gradient.length; i++) {
          /* istanbul ignore next */
          cov_hr0h2ihfp().s[172]++;
          this.currentState.adagradG[i] += gradient[i] * gradient[i];
          /* istanbul ignore next */
          cov_hr0h2ihfp().s[173]++;
          stepDirection[i] = gradient[i] / (Math.sqrt(this.currentState.adagradG[i]) + adagradEps);
        }
        /* istanbul ignore next */
        cov_hr0h2ihfp().s[174]++;
        break;
      default:
        /* istanbul ignore next */
        cov_hr0h2ihfp().b[30][5]++;
        cov_hr0h2ihfp().s[175]++;
        // Default to standard gradient descent
        for (let i =
        /* istanbul ignore next */
        (cov_hr0h2ihfp().s[176]++, 0); i < gradient.length; i++) {
          /* istanbul ignore next */
          cov_hr0h2ihfp().s[177]++;
          stepDirection[i] = gradient[i];
        }
    }
    /* istanbul ignore next */
    cov_hr0h2ihfp().s[178]++;
    return stepDirection;
  }
  /**
   * Perform line search to find optimal step size
   */
  async performLineSearch(problem, objectiveFunction, constraintFunctions, stepDirection) {
    /* istanbul ignore next */
    cov_hr0h2ihfp().f[18]++;
    cov_hr0h2ihfp().s[179]++;
    if (!this.currentState) {
      /* istanbul ignore next */
      cov_hr0h2ihfp().b[31][0]++;
      cov_hr0h2ihfp().s[180]++;
      return this.parameters.learningRate;
    } else
    /* istanbul ignore next */
    {
      cov_hr0h2ihfp().b[31][1]++;
    }
    cov_hr0h2ihfp().s[181]++;
    switch (this.parameters.lineSearchMethod) {
      case 'armijo':
        /* istanbul ignore next */
        cov_hr0h2ihfp().b[32][0]++;
        cov_hr0h2ihfp().s[182]++;
        return this.armijoLineSearch(problem, objectiveFunction, constraintFunctions, stepDirection);
      case 'wolfe':
        /* istanbul ignore next */
        cov_hr0h2ihfp().b[32][1]++;
        cov_hr0h2ihfp().s[183]++;
        return this.wolfeLineSearch(problem, objectiveFunction, constraintFunctions, stepDirection);
      case 'golden_section':
        /* istanbul ignore next */
        cov_hr0h2ihfp().b[32][2]++;
        cov_hr0h2ihfp().s[184]++;
        return this.goldenSectionLineSearch(problem, objectiveFunction, constraintFunctions, stepDirection);
      default:
        /* istanbul ignore next */
        cov_hr0h2ihfp().b[32][3]++;
        cov_hr0h2ihfp().s[185]++;
        return this.parameters.learningRate;
    }
  }
  /**
   * Armijo line search
   */
  async armijoLineSearch(problem, objectiveFunction, constraintFunctions, stepDirection) {
    /* istanbul ignore next */
    cov_hr0h2ihfp().f[19]++;
    cov_hr0h2ihfp().s[186]++;
    if (!this.currentState) {
      /* istanbul ignore next */
      cov_hr0h2ihfp().b[33][0]++;
      cov_hr0h2ihfp().s[187]++;
      return this.parameters.learningRate;
    } else
    /* istanbul ignore next */
    {
      cov_hr0h2ihfp().b[33][1]++;
    }
    const c1 =
    /* istanbul ignore next */
    (cov_hr0h2ihfp().s[188]++, 1e-4); // Armijo constant
    let alpha =
    /* istanbul ignore next */
    (cov_hr0h2ihfp().s[189]++, this.parameters.learningRate);
    const maxBacktrack =
    /* istanbul ignore next */
    (cov_hr0h2ihfp().s[190]++, 20);
    const currentValue =
    /* istanbul ignore next */
    (cov_hr0h2ihfp().s[191]++, this.currentState.functionValue);
    const gradientDotDirection =
    /* istanbul ignore next */
    (cov_hr0h2ihfp().s[192]++, this.currentState.gradient.reduce((sum, g, i) => {
      /* istanbul ignore next */
      cov_hr0h2ihfp().f[20]++;
      cov_hr0h2ihfp().s[193]++;
      return sum + g * stepDirection[i];
    }, 0));
    /* istanbul ignore next */
    cov_hr0h2ihfp().s[194]++;
    for (let i =
    /* istanbul ignore next */
    (cov_hr0h2ihfp().s[195]++, 0); i < maxBacktrack; i++) {
      // Test step
      const testVariables =
      /* istanbul ignore next */
      (cov_hr0h2ihfp().s[196]++, this.createTestVariables(problem, stepDirection, alpha));
      const testValue =
      /* istanbul ignore next */
      (cov_hr0h2ihfp().s[197]++, objectiveFunction(testVariables));
      /* istanbul ignore next */
      cov_hr0h2ihfp().s[198]++;
      this.evaluationCount++;
      // Armijo condition
      /* istanbul ignore next */
      cov_hr0h2ihfp().s[199]++;
      if (testValue <= currentValue - c1 * alpha * gradientDotDirection) {
        /* istanbul ignore next */
        cov_hr0h2ihfp().b[34][0]++;
        cov_hr0h2ihfp().s[200]++;
        return alpha;
      } else
      /* istanbul ignore next */
      {
        cov_hr0h2ihfp().b[34][1]++;
      }
      cov_hr0h2ihfp().s[201]++;
      alpha *= 0.5; // Backtrack
    }
    /* istanbul ignore next */
    cov_hr0h2ihfp().s[202]++;
    return alpha;
  }
  /**
   * Wolfe line search (simplified)
   */
  async wolfeLineSearch(problem, objectiveFunction, constraintFunctions, stepDirection) {
    /* istanbul ignore next */
    cov_hr0h2ihfp().f[21]++;
    cov_hr0h2ihfp().s[203]++;
    // Simplified implementation - use Armijo for now
    return this.armijoLineSearch(problem, objectiveFunction, constraintFunctions, stepDirection);
  }
  /**
   * Golden section line search
   */
  async goldenSectionLineSearch(problem, objectiveFunction, constraintFunctions, stepDirection) {
    /* istanbul ignore next */
    cov_hr0h2ihfp().f[22]++;
    const phi =
    /* istanbul ignore next */
    (cov_hr0h2ihfp().s[204]++, (1 + Math.sqrt(5)) / 2); // Golden ratio
    const resphi =
    /* istanbul ignore next */
    (cov_hr0h2ihfp().s[205]++, 2 - phi);
    let a =
    /* istanbul ignore next */
    (cov_hr0h2ihfp().s[206]++, 0);
    let b =
    /* istanbul ignore next */
    (cov_hr0h2ihfp().s[207]++, this.parameters.learningRate * 2);
    const tol =
    /* istanbul ignore next */
    (cov_hr0h2ihfp().s[208]++, 1e-6);
    // Find initial bracket
    let x1 =
    /* istanbul ignore next */
    (cov_hr0h2ihfp().s[209]++, a + resphi * (b - a));
    let x2 =
    /* istanbul ignore next */
    (cov_hr0h2ihfp().s[210]++, a + (1 - resphi) * (b - a));
    let f1 =
    /* istanbul ignore next */
    (cov_hr0h2ihfp().s[211]++, this.evaluateAtStep(problem, objectiveFunction, stepDirection, x1));
    let f2 =
    /* istanbul ignore next */
    (cov_hr0h2ihfp().s[212]++, this.evaluateAtStep(problem, objectiveFunction, stepDirection, x2));
    /* istanbul ignore next */
    cov_hr0h2ihfp().s[213]++;
    while (Math.abs(b - a) > tol) {
      /* istanbul ignore next */
      cov_hr0h2ihfp().s[214]++;
      if (f1 < f2) {
        /* istanbul ignore next */
        cov_hr0h2ihfp().b[35][0]++;
        cov_hr0h2ihfp().s[215]++;
        b = x2;
        /* istanbul ignore next */
        cov_hr0h2ihfp().s[216]++;
        x2 = x1;
        /* istanbul ignore next */
        cov_hr0h2ihfp().s[217]++;
        f2 = f1;
        /* istanbul ignore next */
        cov_hr0h2ihfp().s[218]++;
        x1 = a + resphi * (b - a);
        /* istanbul ignore next */
        cov_hr0h2ihfp().s[219]++;
        f1 = this.evaluateAtStep(problem, objectiveFunction, stepDirection, x1);
      } else {
        /* istanbul ignore next */
        cov_hr0h2ihfp().b[35][1]++;
        cov_hr0h2ihfp().s[220]++;
        a = x1;
        /* istanbul ignore next */
        cov_hr0h2ihfp().s[221]++;
        x1 = x2;
        /* istanbul ignore next */
        cov_hr0h2ihfp().s[222]++;
        f1 = f2;
        /* istanbul ignore next */
        cov_hr0h2ihfp().s[223]++;
        x2 = a + (1 - resphi) * (b - a);
        /* istanbul ignore next */
        cov_hr0h2ihfp().s[224]++;
        f2 = this.evaluateAtStep(problem, objectiveFunction, stepDirection, x2);
      }
    }
    /* istanbul ignore next */
    cov_hr0h2ihfp().s[225]++;
    return (a + b) / 2;
  }
  /**
   * Create test variables for line search
   */
  createTestVariables(problem, stepDirection, alpha) {
    /* istanbul ignore next */
    cov_hr0h2ihfp().f[23]++;
    cov_hr0h2ihfp().s[226]++;
    if (!this.currentState) {
      /* istanbul ignore next */
      cov_hr0h2ihfp().b[36][0]++;
      cov_hr0h2ihfp().s[227]++;
      return [];
    } else
    /* istanbul ignore next */
    {
      cov_hr0h2ihfp().b[36][1]++;
    }
    cov_hr0h2ihfp().s[228]++;
    return problem.variables.map((variable, i) => {
      /* istanbul ignore next */
      cov_hr0h2ihfp().f[24]++;
      const currentValue =
      /* istanbul ignore next */
      (cov_hr0h2ihfp().s[229]++, this.currentState.solution.variables[variable.id]);
      /* istanbul ignore next */
      cov_hr0h2ihfp().s[230]++;
      if (typeof currentValue === 'number') {
        /* istanbul ignore next */
        cov_hr0h2ihfp().b[37][0]++;
        const newValue =
        /* istanbul ignore next */
        (cov_hr0h2ihfp().s[231]++, currentValue - alpha * stepDirection[i]);
        const min =
        /* istanbul ignore next */
        (cov_hr0h2ihfp().s[232]++, typeof variable.bounds.minimum === 'number' ?
        /* istanbul ignore next */
        (cov_hr0h2ihfp().b[38][0]++, variable.bounds.minimum) :
        /* istanbul ignore next */
        (cov_hr0h2ihfp().b[38][1]++, -Infinity));
        const max =
        /* istanbul ignore next */
        (cov_hr0h2ihfp().s[233]++, typeof variable.bounds.maximum === 'number' ?
        /* istanbul ignore next */
        (cov_hr0h2ihfp().b[39][0]++, variable.bounds.maximum) :
        /* istanbul ignore next */
        (cov_hr0h2ihfp().b[39][1]++, Infinity));
        /* istanbul ignore next */
        cov_hr0h2ihfp().s[234]++;
        return {
          ...variable,
          currentValue: Math.max(min, Math.min(max, newValue))
        };
      } else {
        /* istanbul ignore next */
        cov_hr0h2ihfp().b[37][1]++;
        cov_hr0h2ihfp().s[235]++;
        return {
          ...variable,
          currentValue
        };
      }
    });
  }
  /**
   * Evaluate function at specific step size
   */
  evaluateAtStep(problem, objectiveFunction, stepDirection, alpha) {
    /* istanbul ignore next */
    cov_hr0h2ihfp().f[25]++;
    const testVariables =
    /* istanbul ignore next */
    (cov_hr0h2ihfp().s[236]++, this.createTestVariables(problem, stepDirection, alpha));
    /* istanbul ignore next */
    cov_hr0h2ihfp().s[237]++;
    this.evaluationCount++;
    /* istanbul ignore next */
    cov_hr0h2ihfp().s[238]++;
    return objectiveFunction(testVariables);
  }
  /**
   * Project solution onto feasible region
   */
  projectSolution(problem) {
    /* istanbul ignore next */
    cov_hr0h2ihfp().f[26]++;
    cov_hr0h2ihfp().s[239]++;
    if (!this.currentState) {
      /* istanbul ignore next */
      cov_hr0h2ihfp().b[40][0]++;
      cov_hr0h2ihfp().s[240]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_hr0h2ihfp().b[40][1]++;
    }
    // Simple box constraint projection
    const projectedVariables =
    /* istanbul ignore next */
    (cov_hr0h2ihfp().s[241]++, {});
    /* istanbul ignore next */
    cov_hr0h2ihfp().s[242]++;
    for (const variable of problem.variables) {
      const currentValue =
      /* istanbul ignore next */
      (cov_hr0h2ihfp().s[243]++, this.currentState.solution.variables[variable.id]);
      /* istanbul ignore next */
      cov_hr0h2ihfp().s[244]++;
      if (typeof currentValue === 'number') {
        /* istanbul ignore next */
        cov_hr0h2ihfp().b[41][0]++;
        const min =
        /* istanbul ignore next */
        (cov_hr0h2ihfp().s[245]++, typeof variable.bounds.minimum === 'number' ?
        /* istanbul ignore next */
        (cov_hr0h2ihfp().b[42][0]++, variable.bounds.minimum) :
        /* istanbul ignore next */
        (cov_hr0h2ihfp().b[42][1]++, -Infinity));
        const max =
        /* istanbul ignore next */
        (cov_hr0h2ihfp().s[246]++, typeof variable.bounds.maximum === 'number' ?
        /* istanbul ignore next */
        (cov_hr0h2ihfp().b[43][0]++, variable.bounds.maximum) :
        /* istanbul ignore next */
        (cov_hr0h2ihfp().b[43][1]++, Infinity));
        /* istanbul ignore next */
        cov_hr0h2ihfp().s[247]++;
        projectedVariables[variable.id] = Math.max(min, Math.min(max, currentValue));
      } else {
        /* istanbul ignore next */
        cov_hr0h2ihfp().b[41][1]++;
        cov_hr0h2ihfp().s[248]++;
        projectedVariables[variable.id] = currentValue;
      }
    }
    /* istanbul ignore next */
    cov_hr0h2ihfp().s[249]++;
    this.currentState.solution.variables = projectedVariables;
  }
  /**
   * Adapt learning rate based on progress
   */
  adaptLearningRate() {
    /* istanbul ignore next */
    cov_hr0h2ihfp().f[27]++;
    cov_hr0h2ihfp().s[250]++;
    if (!this.currentState) {
      /* istanbul ignore next */
      cov_hr0h2ihfp().b[44][0]++;
      cov_hr0h2ihfp().s[251]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_hr0h2ihfp().b[44][1]++;
    }
    // Simple adaptive scheme based on gradient norm
    cov_hr0h2ihfp().s[252]++;
    if (this.currentState.gradientNorm > 1.0) {
      /* istanbul ignore next */
      cov_hr0h2ihfp().b[45][0]++;
      cov_hr0h2ihfp().s[253]++;
      // Large gradient - decrease learning rate
      this.currentState.learningRate = Math.max(this.parameters.learningRateMin, this.currentState.learningRate * 0.9);
    } else {
      /* istanbul ignore next */
      cov_hr0h2ihfp().b[45][1]++;
      cov_hr0h2ihfp().s[254]++;
      if (this.currentState.gradientNorm < 0.1) {
        /* istanbul ignore next */
        cov_hr0h2ihfp().b[46][0]++;
        cov_hr0h2ihfp().s[255]++;
        // Small gradient - increase learning rate
        this.currentState.learningRate = Math.min(this.parameters.learningRateMax, this.currentState.learningRate * 1.1);
      } else
      /* istanbul ignore next */
      {
        cov_hr0h2ihfp().b[46][1]++;
      }
    }
  }
  /**
   * Check termination criteria
   */
  shouldTerminate() {
    /* istanbul ignore next */
    cov_hr0h2ihfp().f[28]++;
    cov_hr0h2ihfp().s[256]++;
    if (!this.currentState) {
      /* istanbul ignore next */
      cov_hr0h2ihfp().b[47][0]++;
      cov_hr0h2ihfp().s[257]++;
      return true;
    } else
    /* istanbul ignore next */
    {
      cov_hr0h2ihfp().b[47][1]++;
    }
    // Maximum iterations
    cov_hr0h2ihfp().s[258]++;
    if (this.currentState.iteration >= this.parameters.maxIterations) {
      /* istanbul ignore next */
      cov_hr0h2ihfp().b[48][0]++;
      cov_hr0h2ihfp().s[259]++;
      return true;
    } else
    /* istanbul ignore next */
    {
      cov_hr0h2ihfp().b[48][1]++;
    }
    // Gradient tolerance
    cov_hr0h2ihfp().s[260]++;
    if (this.currentState.gradientNorm < this.parameters.gradientTolerance) {
      /* istanbul ignore next */
      cov_hr0h2ihfp().b[49][0]++;
      cov_hr0h2ihfp().s[261]++;
      return true;
    } else
    /* istanbul ignore next */
    {
      cov_hr0h2ihfp().b[49][1]++;
    }
    // Function value convergence
    cov_hr0h2ihfp().s[262]++;
    if (this.history.length >= 10) {
      /* istanbul ignore next */
      cov_hr0h2ihfp().b[50][0]++;
      const recentHistory =
      /* istanbul ignore next */
      (cov_hr0h2ihfp().s[263]++, this.history.slice(-10));
      const functionImprovement =
      /* istanbul ignore next */
      (cov_hr0h2ihfp().s[264]++, recentHistory[0].bestFitness - recentHistory[recentHistory.length - 1].bestFitness);
      /* istanbul ignore next */
      cov_hr0h2ihfp().s[265]++;
      if (Math.abs(functionImprovement) < this.parameters.convergenceTolerance) {
        /* istanbul ignore next */
        cov_hr0h2ihfp().b[51][0]++;
        cov_hr0h2ihfp().s[266]++;
        return true;
      } else
      /* istanbul ignore next */
      {
        cov_hr0h2ihfp().b[51][1]++;
      }
    } else
    /* istanbul ignore next */
    {
      cov_hr0h2ihfp().b[50][1]++;
    }
    cov_hr0h2ihfp().s[267]++;
    return false;
  }
  /**
   * Update optimization history
   */
  updateHistory() {
    /* istanbul ignore next */
    cov_hr0h2ihfp().f[29]++;
    cov_hr0h2ihfp().s[268]++;
    if (!this.currentState) {
      /* istanbul ignore next */
      cov_hr0h2ihfp().b[52][0]++;
      cov_hr0h2ihfp().s[269]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_hr0h2ihfp().b[52][1]++;
    }
    const history =
    /* istanbul ignore next */
    (cov_hr0h2ihfp().s[270]++, {
      iteration: this.currentState.iteration,
      bestFitness:
      /* istanbul ignore next */
      (cov_hr0h2ihfp().b[53][0]++, this.bestSolution?.fitness) ||
      /* istanbul ignore next */
      (cov_hr0h2ihfp().b[53][1]++, this.currentState.functionValue),
      averageFitness: this.currentState.functionValue,
      worstFitness: this.currentState.functionValue,
      diversity: 0,
      // Not applicable for single solution
      constraintViolations: this.currentState.solution.feasible ?
      /* istanbul ignore next */
      (cov_hr0h2ihfp().b[54][0]++, 0) :
      /* istanbul ignore next */
      (cov_hr0h2ihfp().b[54][1]++, 1),
      timestamp: new Date()
    });
    /* istanbul ignore next */
    cov_hr0h2ihfp().s[271]++;
    this.history.push(history);
  }
  /**
   * Create optimization result
   */
  createOptimizationResult(problem, startTime) {
    /* istanbul ignore next */
    cov_hr0h2ihfp().f[30]++;
    const executionTime =
    /* istanbul ignore next */
    (cov_hr0h2ihfp().s[272]++, performance.now() - startTime);
    const statistics =
    /* istanbul ignore next */
    (cov_hr0h2ihfp().s[273]++, {
      totalIterations:
      /* istanbul ignore next */
      (cov_hr0h2ihfp().b[55][0]++, this.currentState?.iteration) ||
      /* istanbul ignore next */
      (cov_hr0h2ihfp().b[55][1]++, 0),
      totalEvaluations: this.evaluationCount,
      convergenceIteration:
      /* istanbul ignore next */
      (cov_hr0h2ihfp().b[56][0]++, this.currentState?.iteration) ||
      /* istanbul ignore next */
      (cov_hr0h2ihfp().b[56][1]++, 0),
      executionTime,
      bestFitnessHistory: this.history.map(h => {
        /* istanbul ignore next */
        cov_hr0h2ihfp().f[31]++;
        cov_hr0h2ihfp().s[274]++;
        return h.bestFitness;
      }),
      averageFitnessHistory: this.history.map(h => {
        /* istanbul ignore next */
        cov_hr0h2ihfp().f[32]++;
        cov_hr0h2ihfp().s[275]++;
        return h.averageFitness;
      }),
      diversityHistory: this.history.map(h => {
        /* istanbul ignore next */
        cov_hr0h2ihfp().f[33]++;
        cov_hr0h2ihfp().s[276]++;
        return h.diversity;
      }),
      constraintViolationHistory: this.history.map(h => {
        /* istanbul ignore next */
        cov_hr0h2ihfp().f[34]++;
        cov_hr0h2ihfp().s[277]++;
        return h.constraintViolations;
      }),
      algorithmSpecificStats: {
        variant: this.parameters.variant,
        finalLearningRate:
        /* istanbul ignore next */
        (cov_hr0h2ihfp().b[57][0]++, this.currentState?.learningRate) ||
        /* istanbul ignore next */
        (cov_hr0h2ihfp().b[57][1]++, 0),
        finalGradientNorm:
        /* istanbul ignore next */
        (cov_hr0h2ihfp().b[58][0]++, this.currentState?.gradientNorm) ||
        /* istanbul ignore next */
        (cov_hr0h2ihfp().b[58][1]++, 0),
        finalStepSize:
        /* istanbul ignore next */
        (cov_hr0h2ihfp().b[59][0]++, this.currentState?.stepSize) ||
        /* istanbul ignore next */
        (cov_hr0h2ihfp().b[59][1]++, 0)
      }
    });
    const optimizationHistory =
    /* istanbul ignore next */
    (cov_hr0h2ihfp().s[278]++, {
      iterations: this.history,
      populationHistory: [],
      parameterHistory: [],
      convergenceMetrics: []
    });
    /* istanbul ignore next */
    cov_hr0h2ihfp().s[279]++;
    return {
      problemId: problem.id,
      status: SystemOptimizationTypes_1.OptimizationStatus.CONVERGED,
      bestSolution: this.bestSolution,
      statistics,
      history: optimizationHistory,
      analysis: {},
      recommendations: [],
      warnings: [],
      errors: []
    };
  }
  // Utility methods
  generateSolutionId() {
    /* istanbul ignore next */
    cov_hr0h2ihfp().f[35]++;
    cov_hr0h2ihfp().s[280]++;
    return `gd_sol_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  createSeededRandom(seed) {
    /* istanbul ignore next */
    cov_hr0h2ihfp().f[36]++;
    let state =
    /* istanbul ignore next */
    (cov_hr0h2ihfp().s[281]++, seed);
    /* istanbul ignore next */
    cov_hr0h2ihfp().s[282]++;
    return () => {
      /* istanbul ignore next */
      cov_hr0h2ihfp().f[37]++;
      cov_hr0h2ihfp().s[283]++;
      state = (state * 9301 + 49297) % 233280;
      /* istanbul ignore next */
      cov_hr0h2ihfp().s[284]++;
      return state / 233280;
    };
  }
}
/* istanbul ignore next */
cov_hr0h2ihfp().s[285]++;
exports.GradientDescent = GradientDescent;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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