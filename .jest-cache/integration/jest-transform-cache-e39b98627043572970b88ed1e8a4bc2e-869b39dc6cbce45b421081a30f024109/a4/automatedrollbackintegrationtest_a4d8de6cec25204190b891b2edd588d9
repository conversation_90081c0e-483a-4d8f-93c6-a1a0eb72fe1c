eebbde814e936690b3807ca522a34e59
"use strict";

/**
 * Automated Rollback Integration Tests
 * 
 * Comprehensive integration tests for automated rollback mechanisms
 * Part of Phase 1 bridging plan for deployment reliability
 * 
 * @see docs/post-implementation-bridging-plan.md Task 1.3
 */

const {
  execSync,
  spawn
} = require('child_process');
const fs = require('fs');
const path = require('path');

// Test configuration
const TEST_CONFIG = {
  HEALTH_CHECK_SCRIPT: path.join(__dirname, '../../scripts/health-check.sh'),
  ROLLBACK_SCRIPT: path.join(__dirname, '../../scripts/automated-rollback.sh'),
  TEST_TIMEOUT: 30000,
  HEALTH_CHECK_TIMEOUT: 10000,
  ROLLBACK_TIMEOUT: 15000
};

// Mock environment variables for testing
const TEST_ENV = {
  BACKEND_URL: 'http://localhost:5000',
  FRONTEND_URL: 'http://localhost:3000',
  AUTH_URL: 'http://localhost:8000',
  POSTGRES_HOST: 'localhost',
  POSTGRES_PORT: '5432',
  POSTGRES_DB: 'sizewise_test',
  POSTGRES_USER: 'sizewise_test',
  REDIS_HOST: 'localhost',
  REDIS_PORT: '6379',
  ENVIRONMENT: 'test'
};

// Mock server setup for testing
class MockServer {
  constructor(port, shouldFail = false) {
    this.port = port;
    this.shouldFail = shouldFail;
    this.server = null;
  }
  start() {
    return new Promise((resolve, reject) => {
      const http = require('http');
      this.server = http.createServer((req, res) => {
        if (this.shouldFail) {
          res.writeHead(500, {
            'Content-Type': 'application/json'
          });
          res.end(JSON.stringify({
            error: 'Mock server failure'
          }));
        } else {
          // Mock different endpoints
          if (req.url === '/api/health') {
            res.writeHead(200, {
              'Content-Type': 'application/json'
            });
            res.end(JSON.stringify({
              status: 'healthy',
              timestamp: new Date().toISOString()
            }));
          } else if (req.url === '/api/calculations/air-duct') {
            res.writeHead(200, {
              'Content-Type': 'application/json'
            });
            res.end(JSON.stringify({
              duct_size: {
                width: 12,
                height: 8
              },
              velocity: 1500
            }));
          } else if (req.url === '/api/compliance/check') {
            res.writeHead(200, {
              'Content-Type': 'application/json'
            });
            res.end(JSON.stringify({
              validation: 'passed',
              compliance: true
            }));
          } else if (req.url === '/api/compliance/standards-info') {
            res.writeHead(200, {
              'Content-Type': 'application/json'
            });
            res.end(JSON.stringify({
              standards: ['ASHRAE 90.2', 'IECC 2024'],
              version: '1.0.0'
            }));
          } else {
            res.writeHead(200, {
              'Content-Type': 'text/html'
            });
            res.end('<html><body>Mock SizeWise Suite</body></html>');
          }
        }
      });
      this.server.listen(this.port, err => {
        if (err) {
          reject(err);
        } else {
          resolve();
        }
      });
    });
  }
  stop() {
    return new Promise(resolve => {
      if (this.server) {
        this.server.close(() => {
          resolve();
        });
      } else {
        resolve();
      }
    });
  }
  setFailureMode(shouldFail) {
    this.shouldFail = shouldFail;
  }
}

// Test utilities
const testUtils = {
  createMockDeploymentHistory: () => {
    const historyPath = path.join(__dirname, '../../logs/deployment-history.json');
    const history = {
      deployments: [{
        deployment_id: 'test-deployment-001',
        status: 'success',
        timestamp: '2024-01-01 10:00:00 UTC',
        environment: 'test'
      }, {
        deployment_id: 'test-deployment-002',
        status: 'success',
        timestamp: '2024-01-01 11:00:00 UTC',
        environment: 'test'
      }, {
        deployment_id: 'test-deployment-003',
        status: 'failed',
        timestamp: '2024-01-01 12:00:00 UTC',
        environment: 'test'
      }]
    };

    // Ensure logs directory exists
    const logsDir = path.dirname(historyPath);
    if (!fs.existsSync(logsDir)) {
      fs.mkdirSync(logsDir, {
        recursive: true
      });
    }
    fs.writeFileSync(historyPath, JSON.stringify(history, null, 2));
    return historyPath;
  },
  cleanupTestFiles: () => {
    const filesToClean = [path.join(__dirname, '../../logs/deployment-history.json'), path.join(__dirname, '../../logs/health-check.log'), path.join(__dirname, '../../logs/rollback.log'), path.join(__dirname, '../../logs/rollback-notifications.log')];
    filesToClean.forEach(file => {
      if (fs.existsSync(file)) {
        fs.unlinkSync(file);
      }
    });
  },
  executeScript: (scriptPath, args = [], env = {}) => {
    return new Promise((resolve, reject) => {
      const fullEnv = {
        ...process.env,
        ...TEST_ENV,
        ...env
      };
      const child = spawn('bash', [scriptPath, ...args], {
        env: fullEnv,
        stdio: 'pipe'
      });
      let stdout = '';
      let stderr = '';
      child.stdout.on('data', data => {
        stdout += data.toString();
      });
      child.stderr.on('data', data => {
        stderr += data.toString();
      });
      child.on('close', code => {
        resolve({
          code,
          stdout,
          stderr
        });
      });
      child.on('error', error => {
        reject(error);
      });
    });
  }
};
describe('Automated Rollback Integration Tests', () => {
  let mockBackend, mockFrontend, mockAuth;
  beforeAll(async () => {
    // Create mock servers
    mockBackend = new MockServer(5000);
    mockFrontend = new MockServer(3000);
    mockAuth = new MockServer(8000);

    // Start mock servers
    await mockBackend.start();
    await mockFrontend.start();
    await mockAuth.start();

    // Create test deployment history
    testUtils.createMockDeploymentHistory();
  }, TEST_CONFIG.TEST_TIMEOUT);
  afterAll(async () => {
    // Stop mock servers
    await mockBackend.stop();
    await mockFrontend.stop();
    await mockAuth.stop();

    // Cleanup test files
    testUtils.cleanupTestFiles();
  });
  beforeEach(() => {
    // Reset mock servers to healthy state
    mockBackend.setFailureMode(false);
    mockFrontend.setFailureMode(false);
    mockAuth.setFailureMode(false);
  });
  describe('Health Check Script', () => {
    test('should pass all health checks when services are healthy', async () => {
      const result = await testUtils.executeScript(TEST_CONFIG.HEALTH_CHECK_SCRIPT);
      expect(result.code).toBe(0);
      expect(result.stdout).toContain('All health checks passed');
      expect(result.stdout).toContain('Backend health check passed');
      expect(result.stdout).toContain('Frontend health check passed');
      expect(result.stdout).toContain('Auth service health check passed');
    }, TEST_CONFIG.HEALTH_CHECK_TIMEOUT);
    test('should fail health checks when backend is unhealthy', async () => {
      mockBackend.setFailureMode(true);
      const result = await testUtils.executeScript(TEST_CONFIG.HEALTH_CHECK_SCRIPT);
      expect(result.code).toBe(1);
      expect(result.stdout).toContain('Backend health check failed');
      expect(result.stdout).toContain('health checks failed');
    }, TEST_CONFIG.HEALTH_CHECK_TIMEOUT);
    test('should fail health checks when frontend is unhealthy', async () => {
      mockFrontend.setFailureMode(true);
      const result = await testUtils.executeScript(TEST_CONFIG.HEALTH_CHECK_SCRIPT);
      expect(result.code).toBe(1);
      expect(result.stdout).toContain('Frontend health check failed');
    }, TEST_CONFIG.HEALTH_CHECK_TIMEOUT);
    test('should test HVAC calculation functionality', async () => {
      const result = await testUtils.executeScript(TEST_CONFIG.HEALTH_CHECK_SCRIPT);
      expect(result.code).toBe(0);
      expect(result.stdout).toContain('HVAC calculation health check passed');
    }, TEST_CONFIG.HEALTH_CHECK_TIMEOUT);
    test('should test compliance system functionality', async () => {
      const result = await testUtils.executeScript(TEST_CONFIG.HEALTH_CHECK_SCRIPT);
      expect(result.code).toBe(0);
      expect(result.stdout).toContain('Compliance system health check passed');
    }, TEST_CONFIG.HEALTH_CHECK_TIMEOUT);
    test('should test advanced compliance standards', async () => {
      const result = await testUtils.executeScript(TEST_CONFIG.HEALTH_CHECK_SCRIPT);
      expect(result.code).toBe(0);
      expect(result.stdout).toContain('Advanced compliance health check passed');
    }, TEST_CONFIG.HEALTH_CHECK_TIMEOUT);
    test('should create health check log file', async () => {
      await testUtils.executeScript(TEST_CONFIG.HEALTH_CHECK_SCRIPT);
      const logPath = path.join(__dirname, '../../logs/health-check.log');
      expect(fs.existsSync(logPath)).toBe(true);
      const logContent = fs.readFileSync(logPath, 'utf8');
      expect(logContent).toContain('Starting comprehensive health checks');
    }, TEST_CONFIG.HEALTH_CHECK_TIMEOUT);
  });
  describe('Automated Rollback Script', () => {
    test('should identify last successful deployment', async () => {
      const result = await testUtils.executeScript(TEST_CONFIG.ROLLBACK_SCRIPT, ['auto']);

      // Should identify test-deployment-002 as the last successful deployment
      expect(result.stdout).toContain('test-deployment-002');
    }, TEST_CONFIG.ROLLBACK_TIMEOUT);
    test('should record rollback attempt in deployment history', async () => {
      await testUtils.executeScript(TEST_CONFIG.ROLLBACK_SCRIPT, ['auto']);
      const historyPath = path.join(__dirname, '../../logs/deployment-history.json');
      const history = JSON.parse(fs.readFileSync(historyPath, 'utf8'));

      // Should have a new rollback entry
      const rollbackEntries = history.deployments.filter(d => d.deployment_id.startsWith('rollback-'));
      expect(rollbackEntries.length).toBeGreaterThan(0);
    }, TEST_CONFIG.ROLLBACK_TIMEOUT);
    test('should create rollback log file', async () => {
      await testUtils.executeScript(TEST_CONFIG.ROLLBACK_SCRIPT, ['auto']);
      const logPath = path.join(__dirname, '../../logs/rollback.log');
      expect(fs.existsSync(logPath)).toBe(true);
      const logContent = fs.readFileSync(logPath, 'utf8');
      expect(logContent).toContain('Starting automated rollback process');
    }, TEST_CONFIG.ROLLBACK_TIMEOUT);
    test('should handle manual rollback with specific deployment ID', async () => {
      const result = await testUtils.executeScript(TEST_CONFIG.ROLLBACK_SCRIPT, ['manual', 'test-deployment-001']);
      expect(result.stdout).toContain('test-deployment-001');
      expect(result.stdout).toContain('manual rollback');
    }, TEST_CONFIG.ROLLBACK_TIMEOUT);
    test('should validate deployment ID format for manual rollback', async () => {
      const result = await testUtils.executeScript(TEST_CONFIG.ROLLBACK_SCRIPT, ['manual']);
      expect(result.code).toBe(1);
      expect(result.stderr).toContain('Manual rollback requires target deployment ID');
    }, TEST_CONFIG.ROLLBACK_TIMEOUT);
    test('should support different rollback types', async () => {
      const dockerResult = await testUtils.executeScript(TEST_CONFIG.ROLLBACK_SCRIPT, ['docker', 'test-deployment-001']);
      expect(dockerResult.stdout).toContain('Docker-based rollback');
      const gitResult = await testUtils.executeScript(TEST_CONFIG.ROLLBACK_SCRIPT, ['git', 'test-deployment-001']);
      expect(gitResult.stdout).toContain('Git-based rollback');
    }, TEST_CONFIG.ROLLBACK_TIMEOUT);
  });
  describe('Integration Scenarios', () => {
    test('should trigger rollback when health checks fail', async () => {
      // Simulate deployment failure by making backend unhealthy
      mockBackend.setFailureMode(true);

      // Run health check (should fail)
      const healthResult = await testUtils.executeScript(TEST_CONFIG.HEALTH_CHECK_SCRIPT);
      expect(healthResult.code).toBe(1);

      // Run rollback (should succeed)
      const rollbackResult = await testUtils.executeScript(TEST_CONFIG.ROLLBACK_SCRIPT, ['auto']);
      expect(rollbackResult.stdout).toContain('rollback');
    }, TEST_CONFIG.TEST_TIMEOUT);
    test('should maintain deployment history across operations', async () => {
      const historyPath = path.join(__dirname, '../../logs/deployment-history.json');

      // Get initial deployment count
      const initialHistory = JSON.parse(fs.readFileSync(historyPath, 'utf8'));
      const initialCount = initialHistory.deployments.length;

      // Perform rollback
      await testUtils.executeScript(TEST_CONFIG.ROLLBACK_SCRIPT, ['auto']);

      // Check that history was updated
      const updatedHistory = JSON.parse(fs.readFileSync(historyPath, 'utf8'));
      expect(updatedHistory.deployments.length).toBeGreaterThan(initialCount);
    }, TEST_CONFIG.TEST_TIMEOUT);
    test('should preserve existing functionality during rollback', async () => {
      // Verify that rollback doesn't break existing health checks
      const preRollbackHealth = await testUtils.executeScript(TEST_CONFIG.HEALTH_CHECK_SCRIPT);
      expect(preRollbackHealth.code).toBe(0);

      // Perform rollback
      await testUtils.executeScript(TEST_CONFIG.ROLLBACK_SCRIPT, ['auto']);

      // Verify health checks still work
      const postRollbackHealth = await testUtils.executeScript(TEST_CONFIG.HEALTH_CHECK_SCRIPT);
      expect(postRollbackHealth.code).toBe(0);
    }, TEST_CONFIG.TEST_TIMEOUT);
    test('should handle rollback timeout scenarios', async () => {
      // Test with very short timeout
      const result = await testUtils.executeScript(TEST_CONFIG.ROLLBACK_SCRIPT, ['auto'], {
        ROLLBACK_TIMEOUT: '1'
      } // 1 second timeout
      );

      // Should handle timeout gracefully
      expect(result.code).toBe(1);
    }, TEST_CONFIG.TEST_TIMEOUT);
    test('should validate rollback completes within 5 minutes requirement', async () => {
      const startTime = Date.now();
      await testUtils.executeScript(TEST_CONFIG.ROLLBACK_SCRIPT, ['auto']);
      const endTime = Date.now();
      const duration = (endTime - startTime) / 1000; // Convert to seconds

      // Should complete within 5 minutes (300 seconds)
      expect(duration).toBeLessThan(300);
    }, TEST_CONFIG.TEST_TIMEOUT);
  });
  describe('Notification and Logging', () => {
    test('should create notification log entries', async () => {
      await testUtils.executeScript(TEST_CONFIG.ROLLBACK_SCRIPT, ['auto']);
      const notificationLogPath = path.join(__dirname, '../../logs/rollback-notifications.log');

      // Check if notification log was created (even without Slack webhook)
      if (fs.existsSync(notificationLogPath)) {
        const logContent = fs.readFileSync(notificationLogPath, 'utf8');
        expect(logContent).toContain('timestamp');
        expect(logContent).toContain('status');
      }
    }, TEST_CONFIG.TEST_TIMEOUT);
    test('should log rollback events with proper timestamps', async () => {
      await testUtils.executeScript(TEST_CONFIG.ROLLBACK_SCRIPT, ['auto']);
      const logPath = path.join(__dirname, '../../logs/rollback.log');
      const logContent = fs.readFileSync(logPath, 'utf8');

      // Check for timestamp format
      expect(logContent).toMatch(/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/);
    }, TEST_CONFIG.TEST_TIMEOUT);
    test('should maintain audit trail for rollback operations', async () => {
      await testUtils.executeScript(TEST_CONFIG.ROLLBACK_SCRIPT, ['manual', 'test-deployment-001']);
      const historyPath = path.join(__dirname, '../../logs/deployment-history.json');
      const history = JSON.parse(fs.readFileSync(historyPath, 'utf8'));

      // Find rollback entry
      const rollbackEntry = history.deployments.find(d => d.deployment_id.startsWith('rollback-'));
      expect(rollbackEntry).toBeDefined();
      expect(rollbackEntry.timestamp).toBeDefined();
      expect(rollbackEntry.environment).toBe('test');
    }, TEST_CONFIG.TEST_TIMEOUT);
  });
});

// Export test utilities for use in other test files
module.exports = {
  testUtils,
  MockServer,
  TEST_CONFIG
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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