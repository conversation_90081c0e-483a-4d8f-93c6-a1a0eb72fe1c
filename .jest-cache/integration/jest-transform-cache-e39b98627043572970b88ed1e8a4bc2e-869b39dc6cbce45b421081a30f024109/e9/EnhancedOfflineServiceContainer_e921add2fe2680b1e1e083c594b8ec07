ae0e452d83391ef273581ac6efe09e3a
"use strict";

/**
 * Enhanced Offline Service Container
 *
 * Implements ChatGPT recommendations with comprehensive DataService integration.
 * Provides robust offline-first architecture with sync preparation.
 *
 * @see docs/refactoring/component-architecture-specification.md
 */
/* istanbul ignore next */
function cov_pbroua0s4() {
  var path = "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\frontend\\lib\\services\\EnhancedOfflineServiceContainer.ts";
  var hash = "fa9b3a0cb95c7370c061cb7b6ea11e1d6222d956";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\frontend\\lib\\services\\EnhancedOfflineServiceContainer.ts",
    statementMap: {
      "0": {
        start: {
          line: 10,
          column: 0
        },
        end: {
          line: 10,
          column: 62
        }
      },
      "1": {
        start: {
          line: 11,
          column: 0
        },
        end: {
          line: 11,
          column: 235
        }
      },
      "2": {
        start: {
          line: 12,
          column: 0
        },
        end: {
          line: 12,
          column: 86
        }
      },
      "3": {
        start: {
          line: 13,
          column: 22
        },
        end: {
          line: 13,
          column: 52
        }
      },
      "4": {
        start: {
          line: 14,
          column: 30
        },
        end: {
          line: 14,
          column: 68
        }
      },
      "5": {
        start: {
          line: 15,
          column: 32
        },
        end: {
          line: 15,
          column: 76
        }
      },
      "6": {
        start: {
          line: 21,
          column: 8
        },
        end: {
          line: 21,
          column: 39
        }
      },
      "7": {
        start: {
          line: 24,
          column: 8
        },
        end: {
          line: 31,
          column: 9
        }
      },
      "8": {
        start: {
          line: 25,
          column: 36
        },
        end: {
          line: 25,
          column: 73
        }
      },
      "9": {
        start: {
          line: 26,
          column: 12
        },
        end: {
          line: 26,
          column: 89
        }
      },
      "10": {
        start: {
          line: 29,
          column: 12
        },
        end: {
          line: 29,
          column: 59
        }
      },
      "11": {
        start: {
          line: 30,
          column: 12
        },
        end: {
          line: 30,
          column: 24
        }
      },
      "12": {
        start: {
          line: 34,
          column: 8
        },
        end: {
          line: 41,
          column: 9
        }
      },
      "13": {
        start: {
          line: 35,
          column: 36
        },
        end: {
          line: 35,
          column: 74
        }
      },
      "14": {
        start: {
          line: 36,
          column: 12
        },
        end: {
          line: 36,
          column: 64
        }
      },
      "15": {
        start: {
          line: 39,
          column: 12
        },
        end: {
          line: 39,
          column: 60
        }
      },
      "16": {
        start: {
          line: 40,
          column: 12
        },
        end: {
          line: 40,
          column: 24
        }
      },
      "17": {
        start: {
          line: 44,
          column: 8
        },
        end: {
          line: 88,
          column: 9
        }
      },
      "18": {
        start: {
          line: 46,
          column: 32
        },
        end: {
          line: 46,
          column: 36
        }
      },
      "19": {
        start: {
          line: 47,
          column: 31
        },
        end: {
          line: 81,
          column: 13
        }
      },
      "20": {
        start: {
          line: 82,
          column: 12
        },
        end: {
          line: 82,
          column: 47
        }
      },
      "21": {
        start: {
          line: 83,
          column: 12
        },
        end: {
          line: 83,
          column: 30
        }
      },
      "22": {
        start: {
          line: 86,
          column: 12
        },
        end: {
          line: 86,
          column: 62
        }
      },
      "23": {
        start: {
          line: 87,
          column: 12
        },
        end: {
          line: 87,
          column: 24
        }
      },
      "24": {
        start: {
          line: 91,
          column: 8
        },
        end: {
          line: 97,
          column: 9
        }
      },
      "25": {
        start: {
          line: 92,
          column: 12
        },
        end: {
          line: 92,
          column: 53
        }
      },
      "26": {
        start: {
          line: 95,
          column: 12
        },
        end: {
          line: 95,
          column: 62
        }
      },
      "27": {
        start: {
          line: 96,
          column: 12
        },
        end: {
          line: 96,
          column: 24
        }
      },
      "28": {
        start: {
          line: 100,
          column: 8
        },
        end: {
          line: 107,
          column: 9
        }
      },
      "29": {
        start: {
          line: 101,
          column: 37
        },
        end: {
          line: 101,
          column: 86
        }
      },
      "30": {
        start: {
          line: 102,
          column: 12
        },
        end: {
          line: 102,
          column: 77
        }
      },
      "31": {
        start: {
          line: 102,
          column: 45
        },
        end: {
          line: 102,
          column: 75
        }
      },
      "32": {
        start: {
          line: 105,
          column: 12
        },
        end: {
          line: 105,
          column: 61
        }
      },
      "33": {
        start: {
          line: 106,
          column: 12
        },
        end: {
          line: 106,
          column: 24
        }
      },
      "34": {
        start: {
          line: 110,
          column: 8
        },
        end: {
          line: 117,
          column: 9
        }
      },
      "35": {
        start: {
          line: 111,
          column: 37
        },
        end: {
          line: 111,
          column: 89
        }
      },
      "36": {
        start: {
          line: 112,
          column: 12
        },
        end: {
          line: 112,
          column: 77
        }
      },
      "37": {
        start: {
          line: 112,
          column: 45
        },
        end: {
          line: 112,
          column: 75
        }
      },
      "38": {
        start: {
          line: 115,
          column: 12
        },
        end: {
          line: 115,
          column: 63
        }
      },
      "39": {
        start: {
          line: 116,
          column: 12
        },
        end: {
          line: 116,
          column: 24
        }
      },
      "40": {
        start: {
          line: 121,
          column: 8
        },
        end: {
          line: 138,
          column: 10
        }
      },
      "41": {
        start: {
          line: 142,
          column: 8
        },
        end: {
          line: 169,
          column: 10
        }
      },
      "42": {
        start: {
          line: 172,
          column: 8
        },
        end: {
          line: 176,
          column: 11
        }
      },
      "43": {
        start: {
          line: 173,
          column: 22
        },
        end: {
          line: 173,
          column: 44
        }
      },
      "44": {
        start: {
          line: 174,
          column: 22
        },
        end: {
          line: 174,
          column: 52
        }
      },
      "45": {
        start: {
          line: 175,
          column: 12
        },
        end: {
          line: 175,
          column: 34
        }
      },
      "46": {
        start: {
          line: 179,
          column: 26
        },
        end: {
          line: 179,
          column: 57
        }
      },
      "47": {
        start: {
          line: 180,
          column: 8
        },
        end: {
          line: 180,
          column: 34
        }
      },
      "48": {
        start: {
          line: 183,
          column: 0
        },
        end: {
          line: 183,
          column: 56
        }
      },
      "49": {
        start: {
          line: 189,
          column: 8
        },
        end: {
          line: 189,
          column: 39
        }
      },
      "50": {
        start: {
          line: 192,
          column: 8
        },
        end: {
          line: 199,
          column: 9
        }
      },
      "51": {
        start: {
          line: 193,
          column: 33
        },
        end: {
          line: 193,
          column: 72
        }
      },
      "52": {
        start: {
          line: 194,
          column: 12
        },
        end: {
          line: 194,
          column: 80
        }
      },
      "53": {
        start: {
          line: 197,
          column: 12
        },
        end: {
          line: 197,
          column: 64
        }
      },
      "54": {
        start: {
          line: 198,
          column: 12
        },
        end: {
          line: 198,
          column: 24
        }
      },
      "55": {
        start: {
          line: 202,
          column: 8
        },
        end: {
          line: 209,
          column: 9
        }
      },
      "56": {
        start: {
          line: 203,
          column: 33
        },
        end: {
          line: 203,
          column: 67
        }
      },
      "57": {
        start: {
          line: 204,
          column: 12
        },
        end: {
          line: 204,
          column: 80
        }
      },
      "58": {
        start: {
          line: 207,
          column: 12
        },
        end: {
          line: 207,
          column: 56
        }
      },
      "59": {
        start: {
          line: 208,
          column: 12
        },
        end: {
          line: 208,
          column: 24
        }
      },
      "60": {
        start: {
          line: 212,
          column: 8
        },
        end: {
          line: 219,
          column: 9
        }
      },
      "61": {
        start: {
          line: 213,
          column: 33
        },
        end: {
          line: 213,
          column: 65
        }
      },
      "62": {
        start: {
          line: 214,
          column: 12
        },
        end: {
          line: 214,
          column: 58
        }
      },
      "63": {
        start: {
          line: 217,
          column: 12
        },
        end: {
          line: 217,
          column: 59
        }
      },
      "64": {
        start: {
          line: 218,
          column: 12
        },
        end: {
          line: 218,
          column: 24
        }
      },
      "65": {
        start: {
          line: 222,
          column: 8
        },
        end: {
          line: 231,
          column: 10
        }
      },
      "66": {
        start: {
          line: 235,
          column: 25
        },
        end: {
          line: 235,
          column: 79
        }
      },
      "67": {
        start: {
          line: 236,
          column: 8
        },
        end: {
          line: 260,
          column: 10
        }
      },
      "68": {
        start: {
          line: 263,
          column: 0
        },
        end: {
          line: 263,
          column: 50
        }
      },
      "69": {
        start: {
          line: 274,
          column: 8
        },
        end: {
          line: 286,
          column: 9
        }
      },
      "70": {
        start: {
          line: 276,
          column: 12
        },
        end: {
          line: 281,
          column: 14
        }
      },
      "71": {
        start: {
          line: 284,
          column: 12
        },
        end: {
          line: 284,
          column: 69
        }
      },
      "72": {
        start: {
          line: 285,
          column: 12
        },
        end: {
          line: 285,
          column: 24
        }
      },
      "73": {
        start: {
          line: 289,
          column: 8
        },
        end: {
          line: 299,
          column: 9
        }
      },
      "74": {
        start: {
          line: 290,
          column: 12
        },
        end: {
          line: 294,
          column: 14
        }
      },
      "75": {
        start: {
          line: 297,
          column: 12
        },
        end: {
          line: 297,
          column: 64
        }
      },
      "76": {
        start: {
          line: 298,
          column: 12
        },
        end: {
          line: 298,
          column: 24
        }
      },
      "77": {
        start: {
          line: 302,
          column: 8
        },
        end: {
          line: 309,
          column: 9
        }
      },
      "78": {
        start: {
          line: 304,
          column: 12
        },
        end: {
          line: 304,
          column: 22
        }
      },
      "79": {
        start: {
          line: 307,
          column: 12
        },
        end: {
          line: 307,
          column: 71
        }
      },
      "80": {
        start: {
          line: 308,
          column: 12
        },
        end: {
          line: 308,
          column: 24
        }
      },
      "81": {
        start: {
          line: 312,
          column: 0
        },
        end: {
          line: 312,
          column: 64
        }
      },
      "82": {
        start: {
          line: 323,
          column: 8
        },
        end: {
          line: 344,
          column: 9
        }
      },
      "83": {
        start: {
          line: 324,
          column: 27
        },
        end: {
          line: 324,
          column: 29
        }
      },
      "84": {
        start: {
          line: 325,
          column: 29
        },
        end: {
          line: 325,
          column: 31
        }
      },
      "85": {
        start: {
          line: 326,
          column: 12
        },
        end: {
          line: 328,
          column: 13
        }
      },
      "86": {
        start: {
          line: 327,
          column: 16
        },
        end: {
          line: 327,
          column: 56
        }
      },
      "87": {
        start: {
          line: 329,
          column: 12
        },
        end: {
          line: 331,
          column: 13
        }
      },
      "88": {
        start: {
          line: 330,
          column: 16
        },
        end: {
          line: 330,
          column: 62
        }
      },
      "89": {
        start: {
          line: 332,
          column: 12
        },
        end: {
          line: 334,
          column: 13
        }
      },
      "90": {
        start: {
          line: 333,
          column: 16
        },
        end: {
          line: 333,
          column: 70
        }
      },
      "91": {
        start: {
          line: 335,
          column: 12
        },
        end: {
          line: 339,
          column: 14
        }
      },
      "92": {
        start: {
          line: 342,
          column: 12
        },
        end: {
          line: 342,
          column: 64
        }
      },
      "93": {
        start: {
          line: 343,
          column: 12
        },
        end: {
          line: 343,
          column: 24
        }
      },
      "94": {
        start: {
          line: 347,
          column: 0
        },
        end: {
          line: 347,
          column: 62
        }
      },
      "95": {
        start: {
          line: 353,
          column: 8
        },
        end: {
          line: 353,
          column: 39
        }
      },
      "96": {
        start: {
          line: 354,
          column: 8
        },
        end: {
          line: 354,
          column: 78
        }
      },
      "97": {
        start: {
          line: 357,
          column: 8
        },
        end: {
          line: 378,
          column: 9
        }
      },
      "98": {
        start: {
          line: 358,
          column: 28
        },
        end: {
          line: 358,
          column: 72
        }
      },
      "99": {
        start: {
          line: 359,
          column: 12
        },
        end: {
          line: 361,
          column: 13
        }
      },
      "100": {
        start: {
          line: 360,
          column: 16
        },
        end: {
          line: 360,
          column: 66
        }
      },
      "101": {
        start: {
          line: 363,
          column: 38
        },
        end: {
          line: 365,
          column: 34
        }
      },
      "102": {
        start: {
          line: 366,
          column: 27
        },
        end: {
          line: 366,
          column: 103
        }
      },
      "103": {
        start: {
          line: 368,
          column: 12
        },
        end: {
          line: 373,
          column: 14
        }
      },
      "104": {
        start: {
          line: 376,
          column: 12
        },
        end: {
          line: 376,
          column: 62
        }
      },
      "105": {
        start: {
          line: 377,
          column: 12
        },
        end: {
          line: 377,
          column: 24
        }
      },
      "106": {
        start: {
          line: 381,
          column: 8
        },
        end: {
          line: 391,
          column: 9
        }
      },
      "107": {
        start: {
          line: 382,
          column: 12
        },
        end: {
          line: 386,
          column: 14
        }
      },
      "108": {
        start: {
          line: 389,
          column: 12
        },
        end: {
          line: 389,
          column: 65
        }
      },
      "109": {
        start: {
          line: 390,
          column: 12
        },
        end: {
          line: 390,
          column: 24
        }
      },
      "110": {
        start: {
          line: 394,
          column: 8
        },
        end: {
          line: 400,
          column: 9
        }
      },
      "111": {
        start: {
          line: 395,
          column: 12
        },
        end: {
          line: 395,
          column: 82
        }
      },
      "112": {
        start: {
          line: 398,
          column: 12
        },
        end: {
          line: 398,
          column: 63
        }
      },
      "113": {
        start: {
          line: 399,
          column: 12
        },
        end: {
          line: 399,
          column: 24
        }
      },
      "114": {
        start: {
          line: 403,
          column: 0
        },
        end: {
          line: 403,
          column: 54
        }
      },
      "115": {
        start: {
          line: 409,
          column: 8
        },
        end: {
          line: 409,
          column: 39
        }
      },
      "116": {
        start: {
          line: 412,
          column: 8
        },
        end: {
          line: 419,
          column: 9
        }
      },
      "117": {
        start: {
          line: 413,
          column: 25
        },
        end: {
          line: 413,
          column: 64
        }
      },
      "118": {
        start: {
          line: 414,
          column: 12
        },
        end: {
          line: 414,
          column: 40
        }
      },
      "119": {
        start: {
          line: 417,
          column: 12
        },
        end: {
          line: 417,
          column: 64
        }
      },
      "120": {
        start: {
          line: 418,
          column: 12
        },
        end: {
          line: 418,
          column: 26
        }
      },
      "121": {
        start: {
          line: 422,
          column: 8
        },
        end: {
          line: 430,
          column: 9
        }
      },
      "122": {
        start: {
          line: 423,
          column: 25
        },
        end: {
          line: 423,
          column: 64
        }
      },
      "123": {
        start: {
          line: 424,
          column: 25
        },
        end: {
          line: 424,
          column: 81
        }
      },
      "124": {
        start: {
          line: 425,
          column: 12
        },
        end: {
          line: 425,
          column: 42
        }
      },
      "125": {
        start: {
          line: 428,
          column: 12
        },
        end: {
          line: 428,
          column: 68
        }
      },
      "126": {
        start: {
          line: 429,
          column: 12
        },
        end: {
          line: 429,
          column: 25
        }
      },
      "127": {
        start: {
          line: 433,
          column: 8
        },
        end: {
          line: 486,
          column: 9
        }
      },
      "128": {
        start: {
          line: 434,
          column: 25
        },
        end: {
          line: 434,
          column: 52
        }
      },
      "129": {
        start: {
          line: 435,
          column: 27
        },
        end: {
          line: 472,
          column: 13
        }
      },
      "130": {
        start: {
          line: 473,
          column: 12
        },
        end: {
          line: 473,
          column: 32
        }
      },
      "131": {
        start: {
          line: 476,
          column: 12
        },
        end: {
          line: 476,
          column: 63
        }
      },
      "132": {
        start: {
          line: 477,
          column: 12
        },
        end: {
          line: 485,
          column: 14
        }
      },
      "133": {
        start: {
          line: 489,
          column: 8
        },
        end: {
          line: 526,
          column: 9
        }
      },
      "134": {
        start: {
          line: 490,
          column: 25
        },
        end: {
          line: 490,
          column: 64
        }
      },
      "135": {
        start: {
          line: 491,
          column: 12
        },
        end: {
          line: 521,
          column: 13
        }
      },
      "136": {
        start: {
          line: 493,
          column: 33
        },
        end: {
          line: 493,
          column: 83
        }
      },
      "137": {
        start: {
          line: 494,
          column: 37
        },
        end: {
          line: 519,
          column: 17
        }
      },
      "138": {
        start: {
          line: 520,
          column: 16
        },
        end: {
          line: 520,
          column: 62
        }
      },
      "139": {
        start: {
          line: 524,
          column: 12
        },
        end: {
          line: 524,
          column: 60
        }
      },
      "140": {
        start: {
          line: 525,
          column: 12
        },
        end: {
          line: 525,
          column: 24
        }
      },
      "141": {
        start: {
          line: 529,
          column: 0
        },
        end: {
          line: 529,
          column: 50
        }
      },
      "142": {
        start: {
          line: 535,
          column: 8
        },
        end: {
          line: 535,
          column: 39
        }
      },
      "143": {
        start: {
          line: 538,
          column: 8
        },
        end: {
          line: 538,
          column: 65
        }
      },
      "144": {
        start: {
          line: 541,
          column: 8
        },
        end: {
          line: 541,
          column: 54
        }
      },
      "145": {
        start: {
          line: 544,
          column: 8
        },
        end: {
          line: 544,
          column: 53
        }
      },
      "146": {
        start: {
          line: 547,
          column: 8
        },
        end: {
          line: 547,
          column: 58
        }
      },
      "147": {
        start: {
          line: 550,
          column: 0
        },
        end: {
          line: 550,
          column: 50
        }
      },
      "148": {
        start: {
          line: 555,
          column: 4
        },
        end: {
          line: 605,
          column: 5
        }
      },
      "149": {
        start: {
          line: 556,
          column: 8
        },
        end: {
          line: 556,
          column: 54
        }
      },
      "150": {
        start: {
          line: 558,
          column: 28
        },
        end: {
          line: 558,
          column: 79
        }
      },
      "151": {
        start: {
          line: 560,
          column: 28
        },
        end: {
          line: 560,
          column: 52
        }
      },
      "152": {
        start: {
          line: 561,
          column: 31
        },
        end: {
          line: 565,
          column: 10
        }
      },
      "153": {
        start: {
          line: 562,
          column: 12
        },
        end: {
          line: 564,
          column: 22
        }
      },
      "154": {
        start: {
          line: 563,
          column: 16
        },
        end: {
          line: 563,
          column: 91
        }
      },
      "155": {
        start: {
          line: 566,
          column: 8
        },
        end: {
          line: 566,
          column: 58
        }
      },
      "156": {
        start: {
          line: 567,
          column: 8
        },
        end: {
          line: 567,
          column: 62
        }
      },
      "157": {
        start: {
          line: 568,
          column: 8
        },
        end: {
          line: 568,
          column: 56
        }
      },
      "158": {
        start: {
          line: 570,
          column: 31
        },
        end: {
          line: 570,
          column: 70
        }
      },
      "159": {
        start: {
          line: 571,
          column: 28
        },
        end: {
          line: 571,
          column: 64
        }
      },
      "160": {
        start: {
          line: 572,
          column: 35
        },
        end: {
          line: 572,
          column: 67
        }
      },
      "161": {
        start: {
          line: 573,
          column: 34
        },
        end: {
          line: 573,
          column: 65
        }
      },
      "162": {
        start: {
          line: 574,
          column: 30
        },
        end: {
          line: 574,
          column: 68
        }
      },
      "163": {
        start: {
          line: 575,
          column: 28
        },
        end: {
          line: 575,
          column: 64
        }
      },
      "164": {
        start: {
          line: 576,
          column: 28
        },
        end: {
          line: 576,
          column: 64
        }
      },
      "165": {
        start: {
          line: 577,
          column: 8
        },
        end: {
          line: 577,
          column: 57
        }
      },
      "166": {
        start: {
          line: 579,
          column: 30
        },
        end: {
          line: 579,
          column: 78
        }
      },
      "167": {
        start: {
          line: 580,
          column: 30
        },
        end: {
          line: 580,
          column: 78
        }
      },
      "168": {
        start: {
          line: 582,
          column: 31
        },
        end: {
          line: 582,
          column: 100
        }
      },
      "169": {
        start: {
          line: 583,
          column: 8
        },
        end: {
          line: 583,
          column: 59
        }
      },
      "170": {
        start: {
          line: 584,
          column: 8
        },
        end: {
          line: 596,
          column: 10
        }
      },
      "171": {
        start: {
          line: 599,
          column: 8
        },
        end: {
          line: 599,
          column: 87
        }
      },
      "172": {
        start: {
          line: 601,
          column: 8
        },
        end: {
          line: 603,
          column: 9
        }
      },
      "173": {
        start: {
          line: 602,
          column: 12
        },
        end: {
          line: 602,
          column: 83
        }
      },
      "174": {
        start: {
          line: 604,
          column: 8
        },
        end: {
          line: 604,
          column: 83
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 20,
            column: 4
          },
          end: {
            line: 20,
            column: 5
          }
        },
        loc: {
          start: {
            line: 20,
            column: 29
          },
          end: {
            line: 22,
            column: 5
          }
        },
        line: 20
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 23,
            column: 4
          },
          end: {
            line: 23,
            column: 5
          }
        },
        loc: {
          start: {
            line: 23,
            column: 25
          },
          end: {
            line: 32,
            column: 5
          }
        },
        line: 23
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 33,
            column: 4
          },
          end: {
            line: 33,
            column: 5
          }
        },
        loc: {
          start: {
            line: 33,
            column: 31
          },
          end: {
            line: 42,
            column: 5
          }
        },
        line: 33
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 43,
            column: 4
          },
          end: {
            line: 43,
            column: 5
          }
        },
        loc: {
          start: {
            line: 43,
            column: 30
          },
          end: {
            line: 89,
            column: 5
          }
        },
        line: 43
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 90,
            column: 4
          },
          end: {
            line: 90,
            column: 5
          }
        },
        loc: {
          start: {
            line: 90,
            column: 28
          },
          end: {
            line: 98,
            column: 5
          }
        },
        line: 90
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 99,
            column: 4
          },
          end: {
            line: 99,
            column: 5
          }
        },
        loc: {
          start: {
            line: 99,
            column: 31
          },
          end: {
            line: 108,
            column: 5
          }
        },
        line: 99
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 102,
            column: 40
          },
          end: {
            line: 102,
            column: 41
          }
        },
        loc: {
          start: {
            line: 102,
            column: 45
          },
          end: {
            line: 102,
            column: 75
          }
        },
        line: 102
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 109,
            column: 4
          },
          end: {
            line: 109,
            column: 5
          }
        },
        loc: {
          start: {
            line: 109,
            column: 40
          },
          end: {
            line: 118,
            column: 5
          }
        },
        line: 109
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 112,
            column: 40
          },
          end: {
            line: 112,
            column: 41
          }
        },
        loc: {
          start: {
            line: 112,
            column: 45
          },
          end: {
            line: 112,
            column: 75
          }
        },
        line: 112
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 119,
            column: 4
          },
          end: {
            line: 119,
            column: 5
          }
        },
        loc: {
          start: {
            line: 119,
            column: 44
          },
          end: {
            line: 139,
            column: 5
          }
        },
        line: 119
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 140,
            column: 4
          },
          end: {
            line: 140,
            column: 5
          }
        },
        loc: {
          start: {
            line: 140,
            column: 38
          },
          end: {
            line: 170,
            column: 5
          }
        },
        line: 140
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 171,
            column: 4
          },
          end: {
            line: 171,
            column: 5
          }
        },
        loc: {
          start: {
            line: 171,
            column: 19
          },
          end: {
            line: 177,
            column: 5
          }
        },
        line: 171
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 172,
            column: 71
          },
          end: {
            line: 172,
            column: 72
          }
        },
        loc: {
          start: {
            line: 172,
            column: 84
          },
          end: {
            line: 176,
            column: 9
          }
        },
        line: 172
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 178,
            column: 4
          },
          end: {
            line: 178,
            column: 5
          }
        },
        loc: {
          start: {
            line: 178,
            column: 28
          },
          end: {
            line: 181,
            column: 5
          }
        },
        line: 178
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 188,
            column: 4
          },
          end: {
            line: 188,
            column: 5
          }
        },
        loc: {
          start: {
            line: 188,
            column: 29
          },
          end: {
            line: 190,
            column: 5
          }
        },
        line: 188
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 191,
            column: 4
          },
          end: {
            line: 191,
            column: 5
          }
        },
        loc: {
          start: {
            line: 191,
            column: 27
          },
          end: {
            line: 200,
            column: 5
          }
        },
        line: 191
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 201,
            column: 4
          },
          end: {
            line: 201,
            column: 5
          }
        },
        loc: {
          start: {
            line: 201,
            column: 26
          },
          end: {
            line: 210,
            column: 5
          }
        },
        line: 201
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 211,
            column: 4
          },
          end: {
            line: 211,
            column: 5
          }
        },
        loc: {
          start: {
            line: 211,
            column: 27
          },
          end: {
            line: 220,
            column: 5
          }
        },
        line: 211
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 221,
            column: 4
          },
          end: {
            line: 221,
            column: 5
          }
        },
        loc: {
          start: {
            line: 221,
            column: 38
          },
          end: {
            line: 232,
            column: 5
          }
        },
        line: 221
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 233,
            column: 4
          },
          end: {
            line: 233,
            column: 5
          }
        },
        loc: {
          start: {
            line: 233,
            column: 32
          },
          end: {
            line: 261,
            column: 5
          }
        },
        line: 233
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 270,
            column: 4
          },
          end: {
            line: 270,
            column: 5
          }
        },
        loc: {
          start: {
            line: 270,
            column: 18
          },
          end: {
            line: 272,
            column: 5
          }
        },
        line: 270
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 273,
            column: 4
          },
          end: {
            line: 273,
            column: 5
          }
        },
        loc: {
          start: {
            line: 273,
            column: 38
          },
          end: {
            line: 287,
            column: 5
          }
        },
        line: 273
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 288,
            column: 4
          },
          end: {
            line: 288,
            column: 5
          }
        },
        loc: {
          start: {
            line: 288,
            column: 35
          },
          end: {
            line: 300,
            column: 5
          }
        },
        line: 288
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 301,
            column: 4
          },
          end: {
            line: 301,
            column: 5
          }
        },
        loc: {
          start: {
            line: 301,
            column: 43
          },
          end: {
            line: 310,
            column: 5
          }
        },
        line: 301
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 319,
            column: 4
          },
          end: {
            line: 319,
            column: 5
          }
        },
        loc: {
          start: {
            line: 319,
            column: 18
          },
          end: {
            line: 321,
            column: 5
          }
        },
        line: 319
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 322,
            column: 4
          },
          end: {
            line: 322,
            column: 5
          }
        },
        loc: {
          start: {
            line: 322,
            column: 35
          },
          end: {
            line: 345,
            column: 5
          }
        },
        line: 322
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 352,
            column: 4
          },
          end: {
            line: 352,
            column: 5
          }
        },
        loc: {
          start: {
            line: 352,
            column: 29
          },
          end: {
            line: 355,
            column: 5
          }
        },
        line: 352
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 356,
            column: 4
          },
          end: {
            line: 356,
            column: 5
          }
        },
        loc: {
          start: {
            line: 356,
            column: 44
          },
          end: {
            line: 379,
            column: 5
          }
        },
        line: 356
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 380,
            column: 4
          },
          end: {
            line: 380,
            column: 5
          }
        },
        loc: {
          start: {
            line: 380,
            column: 36
          },
          end: {
            line: 392,
            column: 5
          }
        },
        line: 380
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 393,
            column: 4
          },
          end: {
            line: 393,
            column: 5
          }
        },
        loc: {
          start: {
            line: 393,
            column: 35
          },
          end: {
            line: 401,
            column: 5
          }
        },
        line: 393
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 408,
            column: 4
          },
          end: {
            line: 408,
            column: 5
          }
        },
        loc: {
          start: {
            line: 408,
            column: 29
          },
          end: {
            line: 410,
            column: 5
          }
        },
        line: 408
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 411,
            column: 4
          },
          end: {
            line: 411,
            column: 5
          }
        },
        loc: {
          start: {
            line: 411,
            column: 27
          },
          end: {
            line: 420,
            column: 5
          }
        },
        line: 411
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 421,
            column: 4
          },
          end: {
            line: 421,
            column: 5
          }
        },
        loc: {
          start: {
            line: 421,
            column: 36
          },
          end: {
            line: 431,
            column: 5
          }
        },
        line: 421
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 432,
            column: 4
          },
          end: {
            line: 432,
            column: 5
          }
        },
        loc: {
          start: {
            line: 432,
            column: 26
          },
          end: {
            line: 487,
            column: 5
          }
        },
        line: 432
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 488,
            column: 4
          },
          end: {
            line: 488,
            column: 5
          }
        },
        loc: {
          start: {
            line: 488,
            column: 31
          },
          end: {
            line: 527,
            column: 5
          }
        },
        line: 488
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 534,
            column: 4
          },
          end: {
            line: 534,
            column: 5
          }
        },
        loc: {
          start: {
            line: 534,
            column: 29
          },
          end: {
            line: 536,
            column: 5
          }
        },
        line: 534
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 537,
            column: 4
          },
          end: {
            line: 537,
            column: 5
          }
        },
        loc: {
          start: {
            line: 537,
            column: 33
          },
          end: {
            line: 539,
            column: 5
          }
        },
        line: 537
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 540,
            column: 4
          },
          end: {
            line: 540,
            column: 5
          }
        },
        loc: {
          start: {
            line: 540,
            column: 32
          },
          end: {
            line: 542,
            column: 5
          }
        },
        line: 540
      },
      "38": {
        name: "(anonymous_38)",
        decl: {
          start: {
            line: 543,
            column: 4
          },
          end: {
            line: 543,
            column: 5
          }
        },
        loc: {
          start: {
            line: 543,
            column: 25
          },
          end: {
            line: 545,
            column: 5
          }
        },
        line: 543
      },
      "39": {
        name: "(anonymous_39)",
        decl: {
          start: {
            line: 546,
            column: 4
          },
          end: {
            line: 546,
            column: 5
          }
        },
        loc: {
          start: {
            line: 546,
            column: 30
          },
          end: {
            line: 548,
            column: 5
          }
        },
        line: 546
      },
      "40": {
        name: "createEnhancedOfflineServiceContainer",
        decl: {
          start: {
            line: 554,
            column: 15
          },
          end: {
            line: 554,
            column: 52
          }
        },
        loc: {
          start: {
            line: 554,
            column: 55
          },
          end: {
            line: 606,
            column: 1
          }
        },
        line: 554
      },
      "41": {
        name: "(anonymous_41)",
        decl: {
          start: {
            line: 561,
            column: 43
          },
          end: {
            line: 561,
            column: 44
          }
        },
        loc: {
          start: {
            line: 561,
            column: 58
          },
          end: {
            line: 565,
            column: 9
          }
        },
        line: 561
      },
      "42": {
        name: "(anonymous_42)",
        decl: {
          start: {
            line: 562,
            column: 23
          },
          end: {
            line: 562,
            column: 24
          }
        },
        loc: {
          start: {
            line: 562,
            column: 29
          },
          end: {
            line: 564,
            column: 13
          }
        },
        line: 562
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 26,
            column: 19
          },
          end: {
            line: 26,
            column: 88
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 26,
            column: 37
          },
          end: {
            line: 26,
            column: 81
          }
        }, {
          start: {
            line: 26,
            column: 84
          },
          end: {
            line: 26,
            column: 88
          }
        }],
        line: 26
      },
      "1": {
        loc: {
          start: {
            line: 48,
            column: 20
          },
          end: {
            line: 48,
            column: 57
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 48,
            column: 20
          },
          end: {
            line: 48,
            column: 34
          }
        }, {
          start: {
            line: 48,
            column: 38
          },
          end: {
            line: 48,
            column: 57
          }
        }],
        line: 48
      },
      "2": {
        loc: {
          start: {
            line: 49,
            column: 24
          },
          end: {
            line: 49,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 49,
            column: 24
          },
          end: {
            line: 49,
            column: 42
          }
        }, {
          start: {
            line: 49,
            column: 46
          },
          end: {
            line: 49,
            column: 64
          }
        }],
        line: 49
      },
      "3": {
        loc: {
          start: {
            line: 50,
            column: 30
          },
          end: {
            line: 50,
            column: 71
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 50,
            column: 30
          },
          end: {
            line: 50,
            column: 54
          }
        }, {
          start: {
            line: 50,
            column: 58
          },
          end: {
            line: 50,
            column: 71
          }
        }],
        line: 50
      },
      "4": {
        loc: {
          start: {
            line: 51,
            column: 32
          },
          end: {
            line: 51,
            column: 90
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 51,
            column: 32
          },
          end: {
            line: 51,
            column: 58
          }
        }, {
          start: {
            line: 51,
            column: 62
          },
          end: {
            line: 51,
            column: 90
          }
        }],
        line: 51
      },
      "5": {
        loc: {
          start: {
            line: 52,
            column: 37
          },
          end: {
            line: 52,
            column: 74
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 52,
            column: 37
          },
          end: {
            line: 52,
            column: 68
          }
        }, {
          start: {
            line: 52,
            column: 72
          },
          end: {
            line: 52,
            column: 74
          }
        }],
        line: 52
      },
      "6": {
        loc: {
          start: {
            line: 53,
            column: 34
          },
          end: {
            line: 53,
            column: 68
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 53,
            column: 34
          },
          end: {
            line: 53,
            column: 62
          }
        }, {
          start: {
            line: 53,
            column: 66
          },
          end: {
            line: 53,
            column: 68
          }
        }],
        line: 53
      },
      "7": {
        loc: {
          start: {
            line: 54,
            column: 29
          },
          end: {
            line: 54,
            column: 58
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 54,
            column: 29
          },
          end: {
            line: 54,
            column: 52
          }
        }, {
          start: {
            line: 54,
            column: 56
          },
          end: {
            line: 54,
            column: 58
          }
        }],
        line: 54
      },
      "8": {
        loc: {
          start: {
            line: 55,
            column: 32
          },
          end: {
            line: 55,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 55,
            column: 32
          },
          end: {
            line: 55,
            column: 58
          }
        }, {
          start: {
            line: 55,
            column: 62
          },
          end: {
            line: 55,
            column: 64
          }
        }],
        line: 55
      },
      "9": {
        loc: {
          start: {
            line: 56,
            column: 30
          },
          end: {
            line: 56,
            column: 82
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 56,
            column: 30
          },
          end: {
            line: 56,
            column: 54
          }
        }, {
          start: {
            line: 56,
            column: 58
          },
          end: {
            line: 56,
            column: 82
          }
        }],
        line: 56
      },
      "10": {
        loc: {
          start: {
            line: 58,
            column: 25
          },
          end: {
            line: 58,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 58,
            column: 25
          },
          end: {
            line: 58,
            column: 44
          }
        }, {
          start: {
            line: 58,
            column: 48
          },
          end: {
            line: 58,
            column: 53
          }
        }],
        line: 58
      },
      "11": {
        loc: {
          start: {
            line: 59,
            column: 23
          },
          end: {
            line: 59,
            column: 46
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 59,
            column: 23
          },
          end: {
            line: 59,
            column: 40
          }
        }, {
          start: {
            line: 59,
            column: 44
          },
          end: {
            line: 59,
            column: 46
          }
        }],
        line: 59
      },
      "12": {
        loc: {
          start: {
            line: 60,
            column: 26
          },
          end: {
            line: 60,
            column: 52
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 60,
            column: 26
          },
          end: {
            line: 60,
            column: 46
          }
        }, {
          start: {
            line: 60,
            column: 50
          },
          end: {
            line: 60,
            column: 52
          }
        }],
        line: 60
      },
      "13": {
        loc: {
          start: {
            line: 61,
            column: 27
          },
          end: {
            line: 61,
            column: 54
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 61,
            column: 27
          },
          end: {
            line: 61,
            column: 48
          }
        }, {
          start: {
            line: 61,
            column: 52
          },
          end: {
            line: 61,
            column: 54
          }
        }],
        line: 61
      },
      "14": {
        loc: {
          start: {
            line: 62,
            column: 42
          },
          end: {
            line: 73,
            column: 17
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 62,
            column: 42
          },
          end: {
            line: 62,
            column: 78
          }
        }, {
          start: {
            line: 62,
            column: 82
          },
          end: {
            line: 73,
            column: 17
          }
        }],
        line: 62
      },
      "15": {
        loc: {
          start: {
            line: 74,
            column: 32
          },
          end: {
            line: 80,
            column: 17
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 74,
            column: 32
          },
          end: {
            line: 74,
            column: 58
          }
        }, {
          start: {
            line: 74,
            column: 62
          },
          end: {
            line: 80,
            column: 17
          }
        }],
        line: 74
      },
      "16": {
        loc: {
          start: {
            line: 174,
            column: 22
          },
          end: {
            line: 174,
            column: 52
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 174,
            column: 33
          },
          end: {
            line: 174,
            column: 34
          }
        }, {
          start: {
            line: 174,
            column: 38
          },
          end: {
            line: 174,
            column: 51
          }
        }],
        line: 174
      },
      "17": {
        loc: {
          start: {
            line: 194,
            column: 19
          },
          end: {
            line: 194,
            column: 79
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 194,
            column: 34
          },
          end: {
            line: 194,
            column: 72
          }
        }, {
          start: {
            line: 194,
            column: 75
          },
          end: {
            line: 194,
            column: 79
          }
        }],
        line: 194
      },
      "18": {
        loc: {
          start: {
            line: 204,
            column: 19
          },
          end: {
            line: 204,
            column: 79
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 204,
            column: 34
          },
          end: {
            line: 204,
            column: 72
          }
        }, {
          start: {
            line: 204,
            column: 75
          },
          end: {
            line: 204,
            column: 79
          }
        }],
        line: 204
      },
      "19": {
        loc: {
          start: {
            line: 235,
            column: 25
          },
          end: {
            line: 235,
            column: 79
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 235,
            column: 55
          },
          end: {
            line: 235,
            column: 67
          }
        }, {
          start: {
            line: 235,
            column: 70
          },
          end: {
            line: 235,
            column: 79
          }
        }],
        line: 235
      },
      "20": {
        loc: {
          start: {
            line: 238,
            column: 18
          },
          end: {
            line: 238,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 238,
            column: 18
          },
          end: {
            line: 238,
            column: 27
          }
        }, {
          start: {
            line: 238,
            column: 31
          },
          end: {
            line: 238,
            column: 45
          }
        }],
        line: 238
      },
      "21": {
        loc: {
          start: {
            line: 292,
            column: 26
          },
          end: {
            line: 292,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 292,
            column: 26
          },
          end: {
            line: 292,
            column: 42
          }
        }, {
          start: {
            line: 292,
            column: 46
          },
          end: {
            line: 292,
            column: 48
          }
        }],
        line: 292
      },
      "22": {
        loc: {
          start: {
            line: 293,
            column: 24
          },
          end: {
            line: 293,
            column: 69
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 293,
            column: 42
          },
          end: {
            line: 293,
            column: 44
          }
        }, {
          start: {
            line: 293,
            column: 47
          },
          end: {
            line: 293,
            column: 69
          }
        }],
        line: 293
      },
      "23": {
        loc: {
          start: {
            line: 326,
            column: 12
          },
          end: {
            line: 328,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 326,
            column: 12
          },
          end: {
            line: 328,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 326
      },
      "24": {
        loc: {
          start: {
            line: 329,
            column: 12
          },
          end: {
            line: 331,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 329,
            column: 12
          },
          end: {
            line: 331,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 329
      },
      "25": {
        loc: {
          start: {
            line: 332,
            column: 12
          },
          end: {
            line: 334,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 332,
            column: 12
          },
          end: {
            line: 334,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 332
      },
      "26": {
        loc: {
          start: {
            line: 359,
            column: 12
          },
          end: {
            line: 361,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 359,
            column: 12
          },
          end: {
            line: 361,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 359
      },
      "27": {
        loc: {
          start: {
            line: 363,
            column: 38
          },
          end: {
            line: 365,
            column: 34
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 363,
            column: 67
          },
          end: {
            line: 363,
            column: 73
          }
        }, {
          start: {
            line: 364,
            column: 16
          },
          end: {
            line: 365,
            column: 34
          }
        }],
        line: 363
      },
      "28": {
        loc: {
          start: {
            line: 364,
            column: 16
          },
          end: {
            line: 365,
            column: 34
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 364,
            column: 43
          },
          end: {
            line: 364,
            column: 48
          }
        }, {
          start: {
            line: 365,
            column: 20
          },
          end: {
            line: 365,
            column: 34
          }
        }],
        line: 364
      },
      "29": {
        loc: {
          start: {
            line: 414,
            column: 19
          },
          end: {
            line: 414,
            column: 39
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 414,
            column: 19
          },
          end: {
            line: 414,
            column: 29
          }
        }, {
          start: {
            line: 414,
            column: 33
          },
          end: {
            line: 414,
            column: 39
          }
        }],
        line: 414
      },
      "30": {
        loc: {
          start: {
            line: 425,
            column: 19
          },
          end: {
            line: 425,
            column: 41
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 425,
            column: 19
          },
          end: {
            line: 425,
            column: 32
          }
        }, {
          start: {
            line: 425,
            column: 36
          },
          end: {
            line: 425,
            column: 41
          }
        }],
        line: 425
      },
      "31": {
        loc: {
          start: {
            line: 491,
            column: 12
          },
          end: {
            line: 521,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 491,
            column: 12
          },
          end: {
            line: 521,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 491
      },
      "32": {
        loc: {
          start: {
            line: 493,
            column: 33
          },
          end: {
            line: 493,
            column: 83
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 493,
            column: 61
          },
          end: {
            line: 493,
            column: 73
          }
        }, {
          start: {
            line: 493,
            column: 76
          },
          end: {
            line: 493,
            column: 83
          }
        }],
        line: 493
      },
      "33": {
        loc: {
          start: {
            line: 496,
            column: 26
          },
          end: {
            line: 496,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 496,
            column: 26
          },
          end: {
            line: 496,
            column: 35
          }
        }, {
          start: {
            line: 496,
            column: 39
          },
          end: {
            line: 496,
            column: 53
          }
        }],
        line: 496
      },
      "34": {
        loc: {
          start: {
            line: 601,
            column: 8
          },
          end: {
            line: 603,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 601,
            column: 8
          },
          end: {
            line: 603,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 601
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\frontend\\lib\\services\\EnhancedOfflineServiceContainer.ts",
      mappings: ";AAAA;;;;;;;GAOG;;;AA8kBH,sFA+DC;AA3oBD,qDAAqE;AACrE,qEAA4G;AAC5G,6EAAuG;AAgCvG;;GAEG;AACH,MAAa,sBAAsB;IACjC,YAAoB,WAAwB;QAAxB,gBAAW,GAAX,WAAW,CAAa;IAAG,CAAC;IAEhD,KAAK,CAAC,UAAU,CAAC,EAAU;QACzB,IAAI,CAAC;YACH,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;YAC9D,OAAO,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAC/E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YAC/C,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,OAAgB;QAChC,IAAI,CAAC;YACH,MAAM,eAAe,GAAG,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC;YAC/D,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;QACtD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YAChD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,IAAsB;QACxC,IAAI,CAAC;YACH,+DAA+D;YAC/D,MAAM,WAAW,GAAG,IAAW,CAAC;YAEhC,MAAM,UAAU,GAAY;gBAC1B,EAAE,EAAE,WAAW,CAAC,EAAE,IAAI,IAAI,CAAC,YAAY,EAAE;gBACzC,MAAM,EAAE,WAAW,CAAC,MAAM,IAAI,kBAAkB;gBAChD,YAAY,EAAE,WAAW,CAAC,YAAY,IAAI,aAAa;gBACvD,cAAc,EAAE,WAAW,CAAC,cAAc,IAAI,IAAI,CAAC,qBAAqB,EAAE;gBAC1E,mBAAmB,EAAE,WAAW,CAAC,mBAAmB,IAAI,EAAE;gBAC1D,gBAAgB,EAAE,WAAW,CAAC,gBAAgB,IAAI,EAAE;gBACpD,WAAW,EAAE,WAAW,CAAC,WAAW,IAAI,EAAE;gBAC1C,cAAc,EAAE,WAAW,CAAC,cAAc,IAAI,EAAE;gBAChD,YAAY,EAAE,WAAW,CAAC,YAAY,IAAI,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBAClE,aAAa,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACvC,OAAO,EAAE,WAAW,CAAC,OAAO,IAAI,KAAK;gBACrC,KAAK,EAAE,WAAW,CAAC,KAAK,IAAI,EAAE;gBAC9B,QAAQ,EAAE,WAAW,CAAC,QAAQ,IAAI,EAAE;gBACpC,SAAS,EAAE,WAAW,CAAC,SAAS,IAAI,EAAE;gBACtC,wBAAwB,EAAE,WAAW,CAAC,wBAAwB,IAAI;oBAChE,KAAK,EAAE,UAAU;oBACjB,iBAAiB,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE;oBAC3C,gBAAgB,EAAE,kBAAkB;oBACpC,kBAAkB,EAAE,MAAM;oBAC1B,eAAe,EAAE,UAAU;oBAC3B,gBAAgB,EAAE,MAAM;oBACxB,gBAAgB,EAAE,IAAI;oBACtB,cAAc,EAAE,GAAG;oBACnB,QAAQ,EAAE,CAAC;oBACX,aAAa,EAAE,GAAG;iBACnB;gBACD,cAAc,EAAE,WAAW,CAAC,cAAc,IAAI;oBAC5C,MAAM,EAAE,IAAI;oBACZ,MAAM,EAAE,IAAI;oBACZ,EAAE,EAAE,KAAK;oBACT,GAAG,EAAE,KAAK;oBACV,IAAI,EAAE,KAAK;iBACZ;aACF,CAAC;YAEF,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;YACnC,OAAO,UAAU,CAAC;QACpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAClD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,EAAU;QAC5B,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;QAC3C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAClD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,MAAc;QAC/B,IAAI,CAAC;YACH,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;YAC3E,OAAO,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC;QACnE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YACjD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,KAAa,EAAE,MAAe;QACjD,IAAI,CAAC;YACH,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YAC9E,OAAO,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC;QACnE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACnD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,sBAAsB,CAAC,eAAgC;QAC7D,mDAAmD;QACnD,OAAO;YACL,EAAE,EAAE,eAAe,CAAC,EAAE;YACtB,MAAM,EAAE,eAAe,CAAC,MAAM;YAC9B,YAAY,EAAE,eAAe,CAAC,YAAY;YAC1C,cAAc,EAAE,eAAe,CAAC,cAAc;YAC9C,mBAAmB,EAAE,eAAe,CAAC,mBAAmB;YACxD,gBAAgB,EAAE,eAAe,CAAC,gBAAgB;YAClD,WAAW,EAAE,eAAe,CAAC,WAAW;YACxC,cAAc,EAAE,eAAe,CAAC,cAAc;YAC9C,YAAY,EAAE,eAAe,CAAC,YAAY;YAC1C,aAAa,EAAE,eAAe,CAAC,aAAa;YAC5C,OAAO,EAAE,eAAe,CAAC,OAAO;YAChC,KAAK,EAAE,eAAe,CAAC,KAAK;YAC5B,QAAQ,EAAE,eAAe,CAAC,QAAQ;YAClC,SAAS,EAAE,eAAe,CAAC,SAAS;YACpC,wBAAwB,EAAE,eAAe,CAAC,wBAAwB;YAClE,cAAc,EAAE,eAAe,CAAC,cAAc;SAC/C,CAAC;IACJ,CAAC;IAEO,wBAAwB,CAAC,OAAgB;QAC/C,mDAAmD;QACnD,OAAO;YACL,GAAG,OAAO;YACV,IAAI,EAAE;gBACJ,OAAO,EAAE,CAAC;gBACV,YAAY,EAAE,IAAI,IAAI,EAAE;gBACxB,cAAc,EAAE,OAAO,CAAC,MAAM;gBAC9B,UAAU,EAAE,OAAO;gBACnB,SAAS,EAAE,EAAE;gBACb,SAAS,EAAE,KAAK;aACjB;YACD,aAAa,EAAE;gBACb,OAAO,EAAE;oBACP,QAAQ,EAAE,KAAK;oBACf,UAAU,EAAE,EAAE;oBACd,WAAW,EAAE,EAAE;iBAChB;gBACD,QAAQ,EAAE,EAAE;gBACZ,IAAI,EAAE;oBACJ,QAAQ,EAAE,KAAK;iBAChB;aACF;YACD,OAAO,EAAE;gBACP,MAAM,EAAE;oBACN,UAAU,EAAE,IAAI;iBACjB;gBACD,OAAO,EAAE,EAAE;aACZ;SACF,CAAC;IACJ,CAAC;IAEO,YAAY;QAClB,OAAO,sCAAsC,CAAC,OAAO,CAAC,OAAO,EAAE,UAAS,CAAC;YACvE,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YACjC,MAAM,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;YACzC,OAAO,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,qBAAqB;QAC3B,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAClD,OAAO,OAAO,SAAS,EAAE,CAAC;IAC5B,CAAC;CACF;AAvKD,wDAuKC;AAED;;GAEG;AACH,MAAa,mBAAmB;IAC9B,YAAoB,WAAwB;QAAxB,gBAAW,GAAX,WAAW,CAAa;IAAG,CAAC;IAEhD,KAAK,CAAC,cAAc;QAClB,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC;YAC7D,OAAO,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QACtE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACpD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,EAAU;QAC1B,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YACxD,OAAO,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QACtE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;YAC5C,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,IAAU;QACzB,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;YACtD,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;QAChD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YAC/C,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,mBAAmB,CAAC,YAA0B;QACpD,OAAO;YACL,EAAE,EAAE,YAAY,CAAC,EAAE;YACnB,KAAK,EAAE,YAAY,CAAC,KAAK;YACzB,IAAI,EAAE,YAAY,CAAC,IAAI;YACvB,IAAI,EAAE,YAAY,CAAC,IAAI;YACvB,OAAO,EAAE,YAAY,CAAC,OAAO;YAC7B,UAAU,EAAE,YAAY,CAAC,UAAU;YACnC,SAAS,EAAE,YAAY,CAAC,SAAS;YACjC,SAAS,EAAE,YAAY,CAAC,SAAS;SAClC,CAAC;IACJ,CAAC;IAEO,qBAAqB,CAAC,IAAU;QACtC,+DAA+D;QAC/D,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,KAAK,aAAa,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAqC,CAAC;QAEzG,OAAO;YACL,GAAG,IAAI;YACP,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,cAAc,EAAE,iCAAiC;YACpE,IAAI,EAAE,QAAQ,EAAE,kBAAkB;YAClC,QAAQ,EAAE,EAAE,EAAE,6BAA6B;YAC3C,IAAI,EAAE;gBACJ,OAAO,EAAE,CAAC;gBACV,YAAY,EAAE,IAAI,IAAI,EAAE;gBACxB,cAAc,EAAE,IAAI,CAAC,EAAE;gBACvB,UAAU,EAAE,OAAO;gBACnB,SAAS,EAAE,EAAE;gBACb,SAAS,EAAE,KAAK;aACjB;YACD,OAAO,EAAE;gBACP,WAAW,EAAE;oBACX,QAAQ,EAAE,IAAI;oBACd,eAAe,EAAE,QAAQ;oBACzB,YAAY,EAAE,MAAM;iBACrB;gBACD,iBAAiB,EAAE;oBACjB,OAAO,EAAE,IAAI;oBACb,QAAQ,EAAE,CAAC,gBAAgB,EAAE,oBAAoB,EAAE,cAAc,CAAC;iBACnE;aACF;SACF,CAAC;IACJ,CAAC;CACF;AA5ED,kDA4EC;AAED;;;GAGG;AACH,MAAa,0BAA0B;IACrC,yCAAyC;IAEzC;QACE,6CAA6C;IAC/C,CAAC;IAED,KAAK,CAAC,mBAAmB,CAAC,MAAwB;QAChD,IAAI,CAAC;YACH,2CAA2C;YAC3C,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,UAAU,EAAE,MAAM;gBAClB,QAAQ,EAAE,CAAC,yCAAyC,CAAC;gBACrD,MAAM,EAAE,CAAC,0CAA0C,CAAC;aACrD,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YACzD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,OAA0B;QAC9C,IAAI,CAAC;YACH,OAAO;gBACL,KAAK,EAAE,OAAO,CAAC,OAAO;gBACtB,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,EAAE;gBAChC,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC;aACtD,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACpD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,SAAiB;QAC3C,IAAI,CAAC;YACH,gDAAgD;YAChD,OAAO,EAAE,CAAC;QACZ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YAC3D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF;AA5CD,gEA4CC;AAED;;;GAGG;AACH,MAAa,yBAAyB;IACpC,4CAA4C;IAE5C;QACE,gDAAgD;IAClD,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,OAAgB;QACpC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,EAAE,CAAC;YAClB,MAAM,QAAQ,GAAG,EAAE,CAAC;YAEpB,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,IAAI,EAAE,EAAE,CAAC;gBAClC,MAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;YAC1C,CAAC;YAED,IAAI,OAAO,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC/B,QAAQ,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;YAChD,CAAC;YAED,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAClC,QAAQ,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;YACxD,CAAC;YAED,OAAO;gBACL,KAAK,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;gBAC1B,MAAM;gBACN,QAAQ;aACT,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACpD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF;AAlCD,8DAkCC;AAED;;GAEG;AACH,MAAa,qBAAqB;IAGhC,YAAoB,WAAwB;QAAxB,gBAAW,GAAX,WAAW,CAAa;QAFpC,kBAAa,GAAG,IAAA,yCAAmB,GAAE,CAAC;IAEC,CAAC;IAEhD,KAAK,CAAC,aAAa,CAAC,SAAiB,EAAE,OAAsB;QAC3D,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YAC7D,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,IAAI,KAAK,CAAC,WAAW,SAAS,YAAY,CAAC,CAAC;YACpD,CAAC;YAED,0CAA0C;YAC1C,MAAM,iBAAiB,GAAG,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;gBACrC,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;oBAClC,OAAO,CAAC,MAAwB,CAAC;YAE3D,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,SAAS,EAAE,OAAO,EAAE,iBAAiB,CAAC,CAAC;YAE5F,kEAAkE;YAClE,OAAO;gBACL,OAAO,EAAE,MAAM,CAAC,OAAO;gBACvB,QAAQ,EAAE,MAAM,CAAC,UAAU,EAAE,EAAE,4BAA4B;gBAC3D,WAAW,EAAE,MAAM,CAAC,WAAW;gBAC/B,KAAK,EAAE,MAAM,CAAC,KAAK;aACpB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAClD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,QAAgB;QACpC,IAAI,CAAC;YACH,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,QAAQ,EAAE,QAAQ;gBAClB,WAAW,EAAE,gBAAgB,QAAQ,WAAW;aACjD,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACrD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,QAAgB;QACnC,IAAI,CAAC;YACH,OAAO,IAAI,IAAI,CAAC,CAAC,qBAAqB,CAAC,EAAE,EAAE,IAAI,EAAE,iBAAiB,EAAE,CAAC,CAAC;QACxE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACnD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF;AArDD,sDAqDC;AAED;;GAEG;AACH,MAAa,mBAAmB;IAC9B,YAAoB,WAAwB;QAAxB,gBAAW,GAAX,WAAW,CAAa;IAAG,CAAC;IAEhD,KAAK,CAAC,cAAc;QAClB,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC;YACrD,OAAO,IAAI,EAAE,IAAI,IAAI,MAAM,CAAC;QAC9B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACpD,OAAO,MAAM,CAAC;QAChB,CAAC;IACH,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,OAAe;QACpC,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC;YACrD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,OAAO,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;YACtE,OAAO,IAAI,EAAE,OAAO,IAAI,KAAK,CAAC;QAChC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YACxD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,KAAK,CAAC,aAAa;QACjB,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;YAEzC,MAAM,MAAM,GAAiC;gBAC3C,IAAI,EAAE;oBACJ,QAAQ,EAAE,CAAC;oBACX,WAAW,EAAE,EAAE;oBACf,WAAW,EAAE,CAAC;oBACd,8BAA8B,EAAE,KAAK;oBACrC,yBAAyB,EAAE,KAAK;oBAChC,gBAAgB,EAAE,KAAK;oBACvB,aAAa,EAAE,KAAK;iBACrB;gBACD,GAAG,EAAE;oBACH,QAAQ,EAAE,EAAE;oBACZ,WAAW,EAAE,GAAG;oBAChB,WAAW,EAAE,CAAC,CAAC;oBACf,8BAA8B,EAAE,IAAI;oBACpC,yBAAyB,EAAE,IAAI;oBAC/B,gBAAgB,EAAE,IAAI;oBACtB,aAAa,EAAE,IAAI;iBACpB;gBACD,UAAU,EAAE;oBACV,QAAQ,EAAE,CAAC,CAAC;oBACZ,WAAW,EAAE,CAAC,CAAC;oBACf,WAAW,EAAE,CAAC,CAAC;oBACf,8BAA8B,EAAE,IAAI;oBACpC,yBAAyB,EAAE,IAAI;oBAC/B,gBAAgB,EAAE,IAAI;oBACtB,aAAa,EAAE,IAAI;iBACpB;gBACD,WAAW,EAAE;oBACX,QAAQ,EAAE,CAAC,CAAC;oBACZ,WAAW,EAAE,CAAC,CAAC;oBACf,WAAW,EAAE,CAAC,CAAC;oBACf,8BAA8B,EAAE,IAAI;oBACpC,yBAAyB,EAAE,IAAI;oBAC/B,gBAAgB,EAAE,IAAI;oBACtB,aAAa,EAAE,IAAI;iBACpB;aACF,CAAC;YAEF,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC;QACtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACnD,OAAO;gBACL,QAAQ,EAAE,CAAC;gBACX,WAAW,EAAE,EAAE;gBACf,WAAW,EAAE,CAAC;gBACd,8BAA8B,EAAE,KAAK;gBACrC,yBAAyB,EAAE,KAAK;gBAChC,gBAAgB,EAAE,KAAK;gBACvB,aAAa,EAAE,KAAK;aACrB,CAAC;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,OAAiB;QACjC,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC;YACrD,IAAI,IAAI,EAAE,CAAC;gBACT,+DAA+D;gBAC/D,MAAM,QAAQ,GAAG,OAAO,KAAK,aAAa,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,OAAwC,CAAC;gBAErG,MAAM,YAAY,GAAG;oBACnB,GAAG,IAAI;oBACP,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,cAAc,EAAE,iCAAiC;oBACpE,IAAI,EAAE,QAAQ;oBACd,QAAQ,EAAE,EAAE,EAAE,6BAA6B;oBAC3C,SAAS,EAAE,IAAI,IAAI,EAAE;oBACrB,IAAI,EAAE;wBACJ,OAAO,EAAE,CAAC;wBACV,YAAY,EAAE,IAAI,IAAI,EAAE;wBACxB,cAAc,EAAE,IAAI,CAAC,EAAE;wBACvB,UAAU,EAAE,OAAgB;wBAC5B,SAAS,EAAE,EAAE;wBACb,SAAS,EAAE,KAAK;qBACjB;oBACD,OAAO,EAAE;wBACP,WAAW,EAAE;4BACX,QAAQ,EAAE,IAAI;4BACd,eAAe,EAAE,QAAiB;4BAClC,YAAY,EAAE,MAAe;yBAC9B;wBACD,iBAAiB,EAAE;4BACjB,OAAO,EAAE,IAAI;4BACb,QAAQ,EAAE,CAAC,gBAAgB,EAAE,oBAAoB,EAAE,cAAc,CAAC;yBACnE;qBACF;iBACF,CAAC;gBACF,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;YAChD,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YAChD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF;AA1HD,kDA0HC;AAED;;GAEG;AACH,MAAa,mBAAmB;IAC9B,YAAoB,WAAwB;QAAxB,gBAAW,GAAX,WAAW,CAAa;IAAG,CAAC;IAEhD,KAAK,CAAC,oBAAoB;QACxB,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,wBAAwB,EAAE,CAAC;IAC3D,CAAC;IAED,KAAK,CAAC,mBAAmB;QACvB,OAAO,IAAI,CAAC,WAAW,CAAC,mBAAmB,EAAE,CAAC;IAChD,CAAC;IAED,KAAK,CAAC,YAAY;QAChB,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE,CAAC;IAC/C,CAAC;IAED,KAAK,CAAC,iBAAiB;QACrB,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,iBAAiB,EAAE,CAAC;IACpD,CAAC;CACF;AAlBD,kDAkBC;AAED;;GAEG;AACI,KAAK,UAAU,qCAAqC;IACzD,IAAI,CAAC;QACH,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;QAE9C,sCAAsC;QACtC,MAAM,WAAW,GAAG,MAAM,IAAA,+BAAiB,EAAC,OAAO,CAAC,CAAC;QAErD,8CAA8C;QAC9C,MAAM,WAAW,GAAG,WAAW,CAAC,UAAU,EAAE,CAAC;QAC7C,MAAM,cAAc,GAAG,IAAI,OAAO,CAAQ,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE;YACtD,UAAU,CAAC,GAAG,EAAE;gBACd,MAAM,CAAC,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC,CAAC;YAC7E,CAAC,EAAE,KAAK,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC;QAEH,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC,CAAC;QAClD,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;QAEtD,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;QAEhD,sCAAsC;QACtC,MAAM,cAAc,GAAG,IAAI,sBAAsB,CAAC,WAAW,CAAC,CAAC;QAC/D,MAAM,WAAW,GAAG,IAAI,mBAAmB,CAAC,WAAW,CAAC,CAAC;QACzD,MAAM,kBAAkB,GAAG,IAAI,0BAA0B,EAAE,CAAC;QAC5D,MAAM,iBAAiB,GAAG,IAAI,yBAAyB,EAAE,CAAC;QAC1D,MAAM,aAAa,GAAG,IAAI,qBAAqB,CAAC,WAAW,CAAC,CAAC;QAC7D,MAAM,WAAW,GAAG,IAAI,mBAAmB,CAAC,WAAW,CAAC,CAAC;QACzD,MAAM,WAAW,GAAG,IAAI,mBAAmB,CAAC,WAAW,CAAC,CAAC;QAEzD,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;QAEjD,gCAAgC;QAChC,MAAM,aAAa,GAAG,IAAA,yCAAmB,GAAE,CAAC;QAC5C,MAAM,aAAa,GAAG,IAAA,yCAAmB,GAAE,CAAC;QAE5C,yBAAyB;QACzB,MAAM,cAAc,GAAG,IAAA,mDAA2B,EAAC,WAAW,CAAC,CAAC;QAEhE,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;QAEnD,OAAO;YACL,WAAW;YACX,cAAc;YACd,WAAW;YACX,kBAAkB;YAClB,iBAAiB;YACjB,aAAa;YACb,WAAW;YACX,cAAc;YACd,aAAa;YACb,aAAa;YACb,WAAW;SACZ,CAAC;IACJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,wDAAwD,EAAE,KAAK,CAAC,CAAC;QAE/E,6BAA6B;QAC7B,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;YAC3B,MAAM,IAAI,KAAK,CAAC,sCAAsC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QACzE,CAAC;QAED,MAAM,IAAI,KAAK,CAAC,yDAAyD,CAAC,CAAC;IAC7E,CAAC;AACH,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\frontend\\lib\\services\\EnhancedOfflineServiceContainer.ts"],
      sourcesContent: ["/**\r\n * Enhanced Offline Service Container\r\n * \r\n * Implements ChatGPT recommendations with comprehensive DataService integration.\r\n * Provides robust offline-first architecture with sync preparation.\r\n * \r\n * @see docs/refactoring/component-architecture-specification.md\r\n */\r\n\r\nimport { createDataService, DataService } from '../data/DataService';\r\nimport { createExportService, createImportService, createBackupService } from '../data/ImportExportService';\r\nimport { createBrowserFeatureManager, BrowserFeatureManager } from '../features/BrowserFeatureManager';\r\nimport {\r\n  CalculationInput,\r\n  CalculationResult,\r\n  ExportOptions,\r\n  ExportResult,\r\n  TierLimits\r\n} from '../../types/air-duct-sizer';\r\nimport { Project } from '../repositories/interfaces/ProjectRepository';\r\nimport { User, UserTier } from '../repositories/interfaces/UserRepository';\r\nimport { SyncableUser, SyncableProject } from '../../types/sync-models';\r\n\r\n/**\r\n * Enhanced service container interface with DataService integration\r\n */\r\nexport interface EnhancedOfflineServiceContainer {\r\n  // Core services\r\n  dataService: DataService;\r\n  projectService: EnhancedProjectService;\r\n  userService: EnhancedUserService;\r\n  calculationService: EnhancedCalculationService;\r\n  validationService: EnhancedValidationService;\r\n  exportService: EnhancedExportService;\r\n  tierService: EnhancedTierService;\r\n  featureManager: BrowserFeatureManager;\r\n  \r\n  // New services from ChatGPT recommendations\r\n  importService: any; // ImportService\r\n  backupService: any; // BackupService\r\n  syncService: EnhancedSyncService;\r\n}\r\n\r\n/**\r\n * Enhanced project service with DataService integration\r\n */\r\nexport class EnhancedProjectService {\r\n  constructor(private dataService: DataService) {}\r\n\r\n  async getProject(id: string): Promise<Project | null> {\r\n    try {\r\n      const syncableProject = await this.dataService.getProject(id);\r\n      return syncableProject ? this.convertToLegacyProject(syncableProject) : null;\r\n    } catch (error) {\r\n      console.error('Failed to get project:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async saveProject(project: Project): Promise<void> {\r\n    try {\r\n      const syncableProject = this.convertToSyncableProject(project);\r\n      await this.dataService.saveProject(syncableProject);\r\n    } catch (error) {\r\n      console.error('Failed to save project:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async createProject(data: Partial<Project>): Promise<Project> {\r\n    try {\r\n      // Cast to any to avoid TypeScript issues with Partial<Project>\r\n      const projectData = data as any;\r\n\r\n      const newProject: Project = {\r\n        id: projectData.id || this.generateUUID(),\r\n        userId: projectData.userId || 'offline-user-001',\r\n        project_name: projectData.project_name || 'New Project',\r\n        project_number: projectData.project_number || this.generateProjectNumber(),\r\n        project_description: projectData.project_description || '',\r\n        project_location: projectData.project_location || '',\r\n        client_name: projectData.client_name || '',\r\n        estimator_name: projectData.estimator_name || '',\r\n        date_created: projectData.date_created || new Date().toISOString(),\r\n        last_modified: new Date().toISOString(),\r\n        version: projectData.version || '1.0',\r\n        rooms: projectData.rooms || [],\r\n        segments: projectData.segments || [],\r\n        equipment: projectData.equipment || [],\r\n        computational_properties: projectData.computational_properties || {\r\n          units: 'Imperial',\r\n          default_duct_size: { width: 12, height: 8 },\r\n          default_material: 'Galvanized Steel',\r\n          default_insulation: 'None',\r\n          default_fitting: 'Standard',\r\n          calibration_mode: 'Auto',\r\n          default_velocity: 1000,\r\n          pressure_class: \"2\",\r\n          altitude: 0,\r\n          friction_rate: 0.1\r\n        },\r\n        code_standards: projectData.code_standards || {\r\n          smacna: true,\r\n          ashrae: true,\r\n          ul: false,\r\n          imc: false,\r\n          nfpa: false\r\n        }\r\n      };\r\n\r\n      await this.saveProject(newProject);\r\n      return newProject;\r\n    } catch (error) {\r\n      console.error('Failed to create project:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async deleteProject(id: string): Promise<void> {\r\n    try {\r\n      await this.dataService.deleteProject(id);\r\n    } catch (error) {\r\n      console.error('Failed to delete project:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async listProjects(userId: string): Promise<Project[]> {\r\n    try {\r\n      const syncableProjects = await this.dataService.listProjectsByUser(userId);\r\n      return syncableProjects.map(p => this.convertToLegacyProject(p));\r\n    } catch (error) {\r\n      console.error('Failed to list projects:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async searchProjects(query: string, userId?: string): Promise<Project[]> {\r\n    try {\r\n      const syncableProjects = await this.dataService.searchProjects(query, userId);\r\n      return syncableProjects.map(p => this.convertToLegacyProject(p));\r\n    } catch (error) {\r\n      console.error('Failed to search projects:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  private convertToLegacyProject(syncableProject: SyncableProject): Project {\r\n    // Convert SyncableProject to legacy Project format\r\n    return {\r\n      id: syncableProject.id,\r\n      userId: syncableProject.userId,\r\n      project_name: syncableProject.project_name,\r\n      project_number: syncableProject.project_number,\r\n      project_description: syncableProject.project_description,\r\n      project_location: syncableProject.project_location,\r\n      client_name: syncableProject.client_name,\r\n      estimator_name: syncableProject.estimator_name,\r\n      date_created: syncableProject.date_created,\r\n      last_modified: syncableProject.last_modified,\r\n      version: syncableProject.version,\r\n      rooms: syncableProject.rooms,\r\n      segments: syncableProject.segments,\r\n      equipment: syncableProject.equipment,\r\n      computational_properties: syncableProject.computational_properties,\r\n      code_standards: syncableProject.code_standards\r\n    };\r\n  }\r\n\r\n  private convertToSyncableProject(project: Project): SyncableProject {\r\n    // Convert legacy Project to SyncableProject format\r\n    return {\r\n      ...project,\r\n      sync: {\r\n        version: 1,\r\n        lastModified: new Date(),\r\n        lastModifiedBy: project.userId,\r\n        syncStatus: 'local',\r\n        changeLog: [],\r\n        isDeleted: false\r\n      },\r\n      collaboration: {\r\n        sharing: {\r\n          isShared: false,\r\n          sharedWith: [],\r\n          permissions: {}\r\n        },\r\n        comments: [],\r\n        lock: {\r\n          isLocked: false\r\n        }\r\n      },\r\n      offline: {\r\n        backup: {\r\n          autoBackup: true\r\n        },\r\n        exports: []\r\n      }\r\n    };\r\n  }\r\n\r\n  private generateUUID(): string {\r\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\r\n      const r = Math.random() * 16 | 0;\r\n      const v = c == 'x' ? r : (r & 0x3 | 0x8);\r\n      return v.toString(16);\r\n    });\r\n  }\r\n\r\n  private generateProjectNumber(): string {\r\n    const timestamp = Date.now().toString().slice(-6);\r\n    return `PRJ-${timestamp}`;\r\n  }\r\n}\r\n\r\n/**\r\n * Enhanced user service with DataService integration\r\n */\r\nexport class EnhancedUserService {\r\n  constructor(private dataService: DataService) {}\r\n\r\n  async getCurrentUser(): Promise<User | null> {\r\n    try {\r\n      const syncableUser = await this.dataService.getCurrentUser();\r\n      return syncableUser ? this.convertToLegacyUser(syncableUser) : null;\r\n    } catch (error) {\r\n      console.error('Failed to get current user:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async getUserById(id: string): Promise<User | null> {\r\n    try {\r\n      const syncableUser = await this.dataService.getUser(id);\r\n      return syncableUser ? this.convertToLegacyUser(syncableUser) : null;\r\n    } catch (error) {\r\n      console.error('Failed to get user:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async updateUser(user: User): Promise<void> {\r\n    try {\r\n      const syncableUser = this.convertToSyncableUser(user);\r\n      await this.dataService.saveUser(syncableUser);\r\n    } catch (error) {\r\n      console.error('Failed to update user:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  private convertToLegacyUser(syncableUser: SyncableUser): User {\r\n    return {\r\n      id: syncableUser.id,\r\n      email: syncableUser.email,\r\n      name: syncableUser.name,\r\n      tier: syncableUser.tier,\r\n      company: syncableUser.company,\r\n      licenseKey: syncableUser.licenseKey,\r\n      createdAt: syncableUser.createdAt,\r\n      updatedAt: syncableUser.updatedAt\r\n    };\r\n  }\r\n\r\n  private convertToSyncableUser(user: User): SyncableUser {\r\n    // Map super_admin to enterprise for SyncableUser compatibility\r\n    const syncTier = user.tier === 'super_admin' ? 'enterprise' : user.tier as 'free' | 'pro' | 'enterprise';\r\n\r\n    return {\r\n      ...user,\r\n      name: user.name || 'Unknown User', // Ensure name is always a string\r\n      tier: syncTier, // Use mapped tier\r\n      settings: {}, // Add default empty settings\r\n      sync: {\r\n        version: 1,\r\n        lastModified: new Date(),\r\n        lastModifiedBy: user.id,\r\n        syncStatus: 'local',\r\n        changeLog: [],\r\n        isDeleted: false\r\n      },\r\n      offline: {\r\n        preferences: {\r\n          autoSave: true,\r\n          backupFrequency: 'weekly',\r\n          exportFormat: 'json'\r\n        },\r\n        licenseValidation: {\r\n          isValid: true,\r\n          features: ['air_duct_sizer', 'project_management', 'basic_export']\r\n        }\r\n      }\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Enhanced calculation service\r\n * TODO: Implement calculation service without backend dependencies\r\n */\r\nexport class EnhancedCalculationService {\r\n  // private calculator: AirDuctCalculator;\r\n\r\n  constructor() {\r\n    // this.calculator = new AirDuctCalculator();\r\n  }\r\n\r\n  async calculateDuctSizing(inputs: CalculationInput): Promise<CalculationResult> {\r\n    try {\r\n      // TODO: Implement actual calculation logic\r\n      return {\r\n        success: false,\r\n        input_data: inputs,\r\n        warnings: ['Calculation service not yet implemented'],\r\n        errors: ['Calculation service is under development']\r\n      };\r\n    } catch (error) {\r\n      console.error('Failed to calculate duct sizing:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async validateResults(results: CalculationResult): Promise<any> {\r\n    try {\r\n      return {\r\n        valid: results.success,\r\n        warnings: results.warnings || [],\r\n        errors: results.success ? [] : ['Calculation failed']\r\n      };\r\n    } catch (error) {\r\n      console.error('Failed to validate results:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async getCalculationHistory(projectId: string): Promise<CalculationResult[]> {\r\n    try {\r\n      // Future enhancement: store calculation history\r\n      return [];\r\n    } catch (error) {\r\n      console.error('Failed to get calculation history:', error);\r\n      throw error;\r\n    }\r\n  }\r\n}\r\n\r\n/**\r\n * Enhanced validation service\r\n * TODO: Implement validation service without backend dependencies\r\n */\r\nexport class EnhancedValidationService {\r\n  // private smacnaValidator: SMACNAValidator;\r\n\r\n  constructor() {\r\n    // this.smacnaValidator = new SMACNAValidator();\r\n  }\r\n\r\n  async validateProject(project: Project): Promise<any> {\r\n    try {\r\n      const errors = [];\r\n      const warnings = [];\r\n\r\n      if (!project.project_name?.trim()) {\r\n        errors.push('Project name is required');\r\n      }\r\n\r\n      if (project.rooms.length === 0) {\r\n        warnings.push('Project has no rooms defined');\r\n      }\r\n\r\n      if (project.segments.length === 0) {\r\n        warnings.push('Project has no duct segments defined');\r\n      }\r\n\r\n      return {\r\n        valid: errors.length === 0,\r\n        errors,\r\n        warnings\r\n      };\r\n    } catch (error) {\r\n      console.error('Failed to validate project:', error);\r\n      throw error;\r\n    }\r\n  }\r\n}\r\n\r\n/**\r\n * Enhanced export service with DataService integration\r\n */\r\nexport class EnhancedExportService {\r\n  private exportService = createExportService();\r\n\r\n  constructor(private dataService: DataService) {}\r\n\r\n  async exportProject(projectId: string, options: ExportOptions): Promise<ExportResult> {\r\n    try {\r\n      const project = await this.dataService.getProject(projectId);\r\n      if (!project) {\r\n        throw new Error(`Project ${projectId} not found`);\r\n      }\r\n\r\n      // Map export format to DataService format\r\n      const dataServiceFormat = options.format === 'excel' ? 'xlsx' :\r\n                                options.format === 'bom' ? 'csv' :\r\n                                options.format as 'pdf' | 'json';\r\n\r\n      const result = await this.exportService.exportEntity('project', project, dataServiceFormat);\r\n\r\n      // Convert DataService ExportResult to air-duct-sizer ExportResult\r\n      return {\r\n        success: result.success,\r\n        exportId: crypto.randomUUID(), // Generate missing exportId\r\n        downloadUrl: result.downloadUrl,\r\n        error: result.error\r\n      };\r\n    } catch (error) {\r\n      console.error('Failed to export project:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async getExportStatus(exportId: string): Promise<ExportResult> {\r\n    try {\r\n      return {\r\n        success: true,\r\n        exportId: exportId,\r\n        downloadUrl: `/api/exports/${exportId}/download`\r\n      };\r\n    } catch (error) {\r\n      console.error('Failed to get export status:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async downloadExport(exportId: string): Promise<Blob> {\r\n    try {\r\n      return new Blob(['Mock export content'], { type: 'application/pdf' });\r\n    } catch (error) {\r\n      console.error('Failed to download export:', error);\r\n      throw error;\r\n    }\r\n  }\r\n}\r\n\r\n/**\r\n * Enhanced tier service\r\n */\r\nexport class EnhancedTierService {\r\n  constructor(private dataService: DataService) {}\r\n\r\n  async getCurrentTier(): Promise<UserTier> {\r\n    try {\r\n      const user = await this.dataService.getCurrentUser();\r\n      return user?.tier || 'free';\r\n    } catch (error) {\r\n      console.error('Failed to get current tier:', error);\r\n      return 'free';\r\n    }\r\n  }\r\n\r\n  async hasFeatureAccess(feature: string): Promise<boolean> {\r\n    try {\r\n      const user = await this.dataService.getCurrentUser();\r\n      const flag = await this.dataService.getFeatureFlag(feature, user?.id);\r\n      return flag?.enabled || false;\r\n    } catch (error) {\r\n      console.error('Failed to check feature access:', error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  async getTierLimits(): Promise<TierLimits> {\r\n    try {\r\n      const tier = await this.getCurrentTier();\r\n      \r\n      const limits: Record<UserTier, TierLimits> = {\r\n        free: {\r\n          maxRooms: 5,\r\n          maxSegments: 10,\r\n          maxProjects: 3,\r\n          canEditComputationalProperties: false,\r\n          canExportWithoutWatermark: false,\r\n          canUseSimulation: false,\r\n          canUseCatalog: false\r\n        },\r\n        pro: {\r\n          maxRooms: 50,\r\n          maxSegments: 100,\r\n          maxProjects: -1,\r\n          canEditComputationalProperties: true,\r\n          canExportWithoutWatermark: true,\r\n          canUseSimulation: true,\r\n          canUseCatalog: true\r\n        },\r\n        enterprise: {\r\n          maxRooms: -1,\r\n          maxSegments: -1,\r\n          maxProjects: -1,\r\n          canEditComputationalProperties: true,\r\n          canExportWithoutWatermark: true,\r\n          canUseSimulation: true,\r\n          canUseCatalog: true\r\n        },\r\n        super_admin: {\r\n          maxRooms: -1,\r\n          maxSegments: -1,\r\n          maxProjects: -1,\r\n          canEditComputationalProperties: true,\r\n          canExportWithoutWatermark: true,\r\n          canUseSimulation: true,\r\n          canUseCatalog: true\r\n        }\r\n      };\r\n\r\n      return limits[tier];\r\n    } catch (error) {\r\n      console.error('Failed to get tier limits:', error);\r\n      return {\r\n        maxRooms: 5,\r\n        maxSegments: 10,\r\n        maxProjects: 3,\r\n        canEditComputationalProperties: false,\r\n        canExportWithoutWatermark: false,\r\n        canUseSimulation: false,\r\n        canUseCatalog: false\r\n      };\r\n    }\r\n  }\r\n\r\n  async upgradeTier(newTier: UserTier): Promise<void> {\r\n    try {\r\n      const user = await this.dataService.getCurrentUser();\r\n      if (user) {\r\n        // Map super_admin to enterprise for SyncableUser compatibility\r\n        const syncTier = newTier === 'super_admin' ? 'enterprise' : newTier as 'free' | 'pro' | 'enterprise';\r\n\r\n        const syncableUser = {\r\n          ...user,\r\n          name: user.name || 'Unknown User', // Ensure name is always a string\r\n          tier: syncTier,\r\n          settings: {}, // Add default empty settings\r\n          updatedAt: new Date(),\r\n          sync: {\r\n            version: 1,\r\n            lastModified: new Date(),\r\n            lastModifiedBy: user.id,\r\n            syncStatus: 'local' as const,\r\n            changeLog: [],\r\n            isDeleted: false\r\n          },\r\n          offline: {\r\n            preferences: {\r\n              autoSave: true,\r\n              backupFrequency: 'weekly' as const,\r\n              exportFormat: 'json' as const\r\n            },\r\n            licenseValidation: {\r\n              isValid: true,\r\n              features: ['air_duct_sizer', 'project_management', 'basic_export']\r\n            }\r\n          }\r\n        };\r\n        await this.dataService.saveUser(syncableUser);\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to upgrade tier:', error);\r\n      throw error;\r\n    }\r\n  }\r\n}\r\n\r\n/**\r\n * Enhanced sync service for future online mode\r\n */\r\nexport class EnhancedSyncService {\r\n  constructor(private dataService: DataService) {}\r\n\r\n  async getPendingOperations() {\r\n    return await this.dataService.getPendingSyncOperations();\r\n  }\r\n\r\n  async getConnectionStatus() {\r\n    return this.dataService.getConnectionStatus();\r\n  }\r\n\r\n  async forceSyncAll() {\r\n    return await this.dataService.forceSyncAll();\r\n  }\r\n\r\n  async getSyncStatistics() {\r\n    return await this.dataService.getSyncStatistics();\r\n  }\r\n}\r\n\r\n/**\r\n * Create enhanced offline service container with robust error handling\r\n */\r\nexport async function createEnhancedOfflineServiceContainer(): Promise<EnhancedOfflineServiceContainer> {\r\n  try {\r\n    console.log('\uD83D\uDD27 Initializing DataService...');\r\n\r\n    // Initialize DataService with timeout\r\n    const dataService = await createDataService('local');\r\n\r\n    // Add timeout for data service initialization\r\n    const initPromise = dataService.initialize();\r\n    const timeoutPromise = new Promise<never>((_, reject) => {\r\n      setTimeout(() => {\r\n        reject(new Error('DataService initialization timed out after 20 seconds'));\r\n      }, 20000);\r\n    });\r\n\r\n    await Promise.race([initPromise, timeoutPromise]);\r\n    console.log('\u2705 DataService initialized successfully');\r\n\r\n    console.log('\uD83D\uDD27 Creating service instances...');\r\n\r\n    // Create services with error handling\r\n    const projectService = new EnhancedProjectService(dataService);\r\n    const userService = new EnhancedUserService(dataService);\r\n    const calculationService = new EnhancedCalculationService();\r\n    const validationService = new EnhancedValidationService();\r\n    const exportService = new EnhancedExportService(dataService);\r\n    const tierService = new EnhancedTierService(dataService);\r\n    const syncService = new EnhancedSyncService(dataService);\r\n\r\n    console.log('\uD83D\uDD27 Creating auxiliary services...');\r\n\r\n    // Create import/export services\r\n    const importService = createImportService();\r\n    const backupService = createBackupService();\r\n\r\n    // Create feature manager\r\n    const featureManager = createBrowserFeatureManager(dataService);\r\n\r\n    console.log('\u2705 All services created successfully');\r\n\r\n    return {\r\n      dataService,\r\n      projectService,\r\n      userService,\r\n      calculationService,\r\n      validationService,\r\n      exportService,\r\n      tierService,\r\n      featureManager,\r\n      importService,\r\n      backupService,\r\n      syncService\r\n    };\r\n  } catch (error) {\r\n    console.error('\u274C Failed to create enhanced offline service container:', error);\r\n\r\n    // Re-throw with more context\r\n    if (error instanceof Error) {\r\n      throw new Error(`Service container creation failed: ${error.message}`);\r\n    }\r\n\r\n    throw new Error('Unknown error occurred while creating service container');\r\n  }\r\n}\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "fa9b3a0cb95c7370c061cb7b6ea11e1d6222d956"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_pbroua0s4 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_pbroua0s4();
cov_pbroua0s4().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_pbroua0s4().s[1]++;
exports.EnhancedSyncService = exports.EnhancedTierService = exports.EnhancedExportService = exports.EnhancedValidationService = exports.EnhancedCalculationService = exports.EnhancedUserService = exports.EnhancedProjectService = void 0;
/* istanbul ignore next */
cov_pbroua0s4().s[2]++;
exports.createEnhancedOfflineServiceContainer = createEnhancedOfflineServiceContainer;
const DataService_1 =
/* istanbul ignore next */
(cov_pbroua0s4().s[3]++, require("../data/DataService"));
const ImportExportService_1 =
/* istanbul ignore next */
(cov_pbroua0s4().s[4]++, require("../data/ImportExportService"));
const BrowserFeatureManager_1 =
/* istanbul ignore next */
(cov_pbroua0s4().s[5]++, require("../features/BrowserFeatureManager"));
/**
 * Enhanced project service with DataService integration
 */
class EnhancedProjectService {
  constructor(dataService) {
    /* istanbul ignore next */
    cov_pbroua0s4().f[0]++;
    cov_pbroua0s4().s[6]++;
    this.dataService = dataService;
  }
  async getProject(id) {
    /* istanbul ignore next */
    cov_pbroua0s4().f[1]++;
    cov_pbroua0s4().s[7]++;
    try {
      const syncableProject =
      /* istanbul ignore next */
      (cov_pbroua0s4().s[8]++, await this.dataService.getProject(id));
      /* istanbul ignore next */
      cov_pbroua0s4().s[9]++;
      return syncableProject ?
      /* istanbul ignore next */
      (cov_pbroua0s4().b[0][0]++, this.convertToLegacyProject(syncableProject)) :
      /* istanbul ignore next */
      (cov_pbroua0s4().b[0][1]++, null);
    } catch (error) {
      /* istanbul ignore next */
      cov_pbroua0s4().s[10]++;
      console.error('Failed to get project:', error);
      /* istanbul ignore next */
      cov_pbroua0s4().s[11]++;
      throw error;
    }
  }
  async saveProject(project) {
    /* istanbul ignore next */
    cov_pbroua0s4().f[2]++;
    cov_pbroua0s4().s[12]++;
    try {
      const syncableProject =
      /* istanbul ignore next */
      (cov_pbroua0s4().s[13]++, this.convertToSyncableProject(project));
      /* istanbul ignore next */
      cov_pbroua0s4().s[14]++;
      await this.dataService.saveProject(syncableProject);
    } catch (error) {
      /* istanbul ignore next */
      cov_pbroua0s4().s[15]++;
      console.error('Failed to save project:', error);
      /* istanbul ignore next */
      cov_pbroua0s4().s[16]++;
      throw error;
    }
  }
  async createProject(data) {
    /* istanbul ignore next */
    cov_pbroua0s4().f[3]++;
    cov_pbroua0s4().s[17]++;
    try {
      // Cast to any to avoid TypeScript issues with Partial<Project>
      const projectData =
      /* istanbul ignore next */
      (cov_pbroua0s4().s[18]++, data);
      const newProject =
      /* istanbul ignore next */
      (cov_pbroua0s4().s[19]++, {
        id:
        /* istanbul ignore next */
        (cov_pbroua0s4().b[1][0]++, projectData.id) ||
        /* istanbul ignore next */
        (cov_pbroua0s4().b[1][1]++, this.generateUUID()),
        userId:
        /* istanbul ignore next */
        (cov_pbroua0s4().b[2][0]++, projectData.userId) ||
        /* istanbul ignore next */
        (cov_pbroua0s4().b[2][1]++, 'offline-user-001'),
        project_name:
        /* istanbul ignore next */
        (cov_pbroua0s4().b[3][0]++, projectData.project_name) ||
        /* istanbul ignore next */
        (cov_pbroua0s4().b[3][1]++, 'New Project'),
        project_number:
        /* istanbul ignore next */
        (cov_pbroua0s4().b[4][0]++, projectData.project_number) ||
        /* istanbul ignore next */
        (cov_pbroua0s4().b[4][1]++, this.generateProjectNumber()),
        project_description:
        /* istanbul ignore next */
        (cov_pbroua0s4().b[5][0]++, projectData.project_description) ||
        /* istanbul ignore next */
        (cov_pbroua0s4().b[5][1]++, ''),
        project_location:
        /* istanbul ignore next */
        (cov_pbroua0s4().b[6][0]++, projectData.project_location) ||
        /* istanbul ignore next */
        (cov_pbroua0s4().b[6][1]++, ''),
        client_name:
        /* istanbul ignore next */
        (cov_pbroua0s4().b[7][0]++, projectData.client_name) ||
        /* istanbul ignore next */
        (cov_pbroua0s4().b[7][1]++, ''),
        estimator_name:
        /* istanbul ignore next */
        (cov_pbroua0s4().b[8][0]++, projectData.estimator_name) ||
        /* istanbul ignore next */
        (cov_pbroua0s4().b[8][1]++, ''),
        date_created:
        /* istanbul ignore next */
        (cov_pbroua0s4().b[9][0]++, projectData.date_created) ||
        /* istanbul ignore next */
        (cov_pbroua0s4().b[9][1]++, new Date().toISOString()),
        last_modified: new Date().toISOString(),
        version:
        /* istanbul ignore next */
        (cov_pbroua0s4().b[10][0]++, projectData.version) ||
        /* istanbul ignore next */
        (cov_pbroua0s4().b[10][1]++, '1.0'),
        rooms:
        /* istanbul ignore next */
        (cov_pbroua0s4().b[11][0]++, projectData.rooms) ||
        /* istanbul ignore next */
        (cov_pbroua0s4().b[11][1]++, []),
        segments:
        /* istanbul ignore next */
        (cov_pbroua0s4().b[12][0]++, projectData.segments) ||
        /* istanbul ignore next */
        (cov_pbroua0s4().b[12][1]++, []),
        equipment:
        /* istanbul ignore next */
        (cov_pbroua0s4().b[13][0]++, projectData.equipment) ||
        /* istanbul ignore next */
        (cov_pbroua0s4().b[13][1]++, []),
        computational_properties:
        /* istanbul ignore next */
        (cov_pbroua0s4().b[14][0]++, projectData.computational_properties) ||
        /* istanbul ignore next */
        (cov_pbroua0s4().b[14][1]++, {
          units: 'Imperial',
          default_duct_size: {
            width: 12,
            height: 8
          },
          default_material: 'Galvanized Steel',
          default_insulation: 'None',
          default_fitting: 'Standard',
          calibration_mode: 'Auto',
          default_velocity: 1000,
          pressure_class: "2",
          altitude: 0,
          friction_rate: 0.1
        }),
        code_standards:
        /* istanbul ignore next */
        (cov_pbroua0s4().b[15][0]++, projectData.code_standards) ||
        /* istanbul ignore next */
        (cov_pbroua0s4().b[15][1]++, {
          smacna: true,
          ashrae: true,
          ul: false,
          imc: false,
          nfpa: false
        })
      });
      /* istanbul ignore next */
      cov_pbroua0s4().s[20]++;
      await this.saveProject(newProject);
      /* istanbul ignore next */
      cov_pbroua0s4().s[21]++;
      return newProject;
    } catch (error) {
      /* istanbul ignore next */
      cov_pbroua0s4().s[22]++;
      console.error('Failed to create project:', error);
      /* istanbul ignore next */
      cov_pbroua0s4().s[23]++;
      throw error;
    }
  }
  async deleteProject(id) {
    /* istanbul ignore next */
    cov_pbroua0s4().f[4]++;
    cov_pbroua0s4().s[24]++;
    try {
      /* istanbul ignore next */
      cov_pbroua0s4().s[25]++;
      await this.dataService.deleteProject(id);
    } catch (error) {
      /* istanbul ignore next */
      cov_pbroua0s4().s[26]++;
      console.error('Failed to delete project:', error);
      /* istanbul ignore next */
      cov_pbroua0s4().s[27]++;
      throw error;
    }
  }
  async listProjects(userId) {
    /* istanbul ignore next */
    cov_pbroua0s4().f[5]++;
    cov_pbroua0s4().s[28]++;
    try {
      const syncableProjects =
      /* istanbul ignore next */
      (cov_pbroua0s4().s[29]++, await this.dataService.listProjectsByUser(userId));
      /* istanbul ignore next */
      cov_pbroua0s4().s[30]++;
      return syncableProjects.map(p => {
        /* istanbul ignore next */
        cov_pbroua0s4().f[6]++;
        cov_pbroua0s4().s[31]++;
        return this.convertToLegacyProject(p);
      });
    } catch (error) {
      /* istanbul ignore next */
      cov_pbroua0s4().s[32]++;
      console.error('Failed to list projects:', error);
      /* istanbul ignore next */
      cov_pbroua0s4().s[33]++;
      throw error;
    }
  }
  async searchProjects(query, userId) {
    /* istanbul ignore next */
    cov_pbroua0s4().f[7]++;
    cov_pbroua0s4().s[34]++;
    try {
      const syncableProjects =
      /* istanbul ignore next */
      (cov_pbroua0s4().s[35]++, await this.dataService.searchProjects(query, userId));
      /* istanbul ignore next */
      cov_pbroua0s4().s[36]++;
      return syncableProjects.map(p => {
        /* istanbul ignore next */
        cov_pbroua0s4().f[8]++;
        cov_pbroua0s4().s[37]++;
        return this.convertToLegacyProject(p);
      });
    } catch (error) {
      /* istanbul ignore next */
      cov_pbroua0s4().s[38]++;
      console.error('Failed to search projects:', error);
      /* istanbul ignore next */
      cov_pbroua0s4().s[39]++;
      throw error;
    }
  }
  convertToLegacyProject(syncableProject) {
    /* istanbul ignore next */
    cov_pbroua0s4().f[9]++;
    cov_pbroua0s4().s[40]++;
    // Convert SyncableProject to legacy Project format
    return {
      id: syncableProject.id,
      userId: syncableProject.userId,
      project_name: syncableProject.project_name,
      project_number: syncableProject.project_number,
      project_description: syncableProject.project_description,
      project_location: syncableProject.project_location,
      client_name: syncableProject.client_name,
      estimator_name: syncableProject.estimator_name,
      date_created: syncableProject.date_created,
      last_modified: syncableProject.last_modified,
      version: syncableProject.version,
      rooms: syncableProject.rooms,
      segments: syncableProject.segments,
      equipment: syncableProject.equipment,
      computational_properties: syncableProject.computational_properties,
      code_standards: syncableProject.code_standards
    };
  }
  convertToSyncableProject(project) {
    /* istanbul ignore next */
    cov_pbroua0s4().f[10]++;
    cov_pbroua0s4().s[41]++;
    // Convert legacy Project to SyncableProject format
    return {
      ...project,
      sync: {
        version: 1,
        lastModified: new Date(),
        lastModifiedBy: project.userId,
        syncStatus: 'local',
        changeLog: [],
        isDeleted: false
      },
      collaboration: {
        sharing: {
          isShared: false,
          sharedWith: [],
          permissions: {}
        },
        comments: [],
        lock: {
          isLocked: false
        }
      },
      offline: {
        backup: {
          autoBackup: true
        },
        exports: []
      }
    };
  }
  generateUUID() {
    /* istanbul ignore next */
    cov_pbroua0s4().f[11]++;
    cov_pbroua0s4().s[42]++;
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
      /* istanbul ignore next */
      cov_pbroua0s4().f[12]++;
      const r =
      /* istanbul ignore next */
      (cov_pbroua0s4().s[43]++, Math.random() * 16 | 0);
      const v =
      /* istanbul ignore next */
      (cov_pbroua0s4().s[44]++, c == 'x' ?
      /* istanbul ignore next */
      (cov_pbroua0s4().b[16][0]++, r) :
      /* istanbul ignore next */
      (cov_pbroua0s4().b[16][1]++, r & 0x3 | 0x8));
      /* istanbul ignore next */
      cov_pbroua0s4().s[45]++;
      return v.toString(16);
    });
  }
  generateProjectNumber() {
    /* istanbul ignore next */
    cov_pbroua0s4().f[13]++;
    const timestamp =
    /* istanbul ignore next */
    (cov_pbroua0s4().s[46]++, Date.now().toString().slice(-6));
    /* istanbul ignore next */
    cov_pbroua0s4().s[47]++;
    return `PRJ-${timestamp}`;
  }
}
/* istanbul ignore next */
cov_pbroua0s4().s[48]++;
exports.EnhancedProjectService = EnhancedProjectService;
/**
 * Enhanced user service with DataService integration
 */
class EnhancedUserService {
  constructor(dataService) {
    /* istanbul ignore next */
    cov_pbroua0s4().f[14]++;
    cov_pbroua0s4().s[49]++;
    this.dataService = dataService;
  }
  async getCurrentUser() {
    /* istanbul ignore next */
    cov_pbroua0s4().f[15]++;
    cov_pbroua0s4().s[50]++;
    try {
      const syncableUser =
      /* istanbul ignore next */
      (cov_pbroua0s4().s[51]++, await this.dataService.getCurrentUser());
      /* istanbul ignore next */
      cov_pbroua0s4().s[52]++;
      return syncableUser ?
      /* istanbul ignore next */
      (cov_pbroua0s4().b[17][0]++, this.convertToLegacyUser(syncableUser)) :
      /* istanbul ignore next */
      (cov_pbroua0s4().b[17][1]++, null);
    } catch (error) {
      /* istanbul ignore next */
      cov_pbroua0s4().s[53]++;
      console.error('Failed to get current user:', error);
      /* istanbul ignore next */
      cov_pbroua0s4().s[54]++;
      throw error;
    }
  }
  async getUserById(id) {
    /* istanbul ignore next */
    cov_pbroua0s4().f[16]++;
    cov_pbroua0s4().s[55]++;
    try {
      const syncableUser =
      /* istanbul ignore next */
      (cov_pbroua0s4().s[56]++, await this.dataService.getUser(id));
      /* istanbul ignore next */
      cov_pbroua0s4().s[57]++;
      return syncableUser ?
      /* istanbul ignore next */
      (cov_pbroua0s4().b[18][0]++, this.convertToLegacyUser(syncableUser)) :
      /* istanbul ignore next */
      (cov_pbroua0s4().b[18][1]++, null);
    } catch (error) {
      /* istanbul ignore next */
      cov_pbroua0s4().s[58]++;
      console.error('Failed to get user:', error);
      /* istanbul ignore next */
      cov_pbroua0s4().s[59]++;
      throw error;
    }
  }
  async updateUser(user) {
    /* istanbul ignore next */
    cov_pbroua0s4().f[17]++;
    cov_pbroua0s4().s[60]++;
    try {
      const syncableUser =
      /* istanbul ignore next */
      (cov_pbroua0s4().s[61]++, this.convertToSyncableUser(user));
      /* istanbul ignore next */
      cov_pbroua0s4().s[62]++;
      await this.dataService.saveUser(syncableUser);
    } catch (error) {
      /* istanbul ignore next */
      cov_pbroua0s4().s[63]++;
      console.error('Failed to update user:', error);
      /* istanbul ignore next */
      cov_pbroua0s4().s[64]++;
      throw error;
    }
  }
  convertToLegacyUser(syncableUser) {
    /* istanbul ignore next */
    cov_pbroua0s4().f[18]++;
    cov_pbroua0s4().s[65]++;
    return {
      id: syncableUser.id,
      email: syncableUser.email,
      name: syncableUser.name,
      tier: syncableUser.tier,
      company: syncableUser.company,
      licenseKey: syncableUser.licenseKey,
      createdAt: syncableUser.createdAt,
      updatedAt: syncableUser.updatedAt
    };
  }
  convertToSyncableUser(user) {
    /* istanbul ignore next */
    cov_pbroua0s4().f[19]++;
    // Map super_admin to enterprise for SyncableUser compatibility
    const syncTier =
    /* istanbul ignore next */
    (cov_pbroua0s4().s[66]++, user.tier === 'super_admin' ?
    /* istanbul ignore next */
    (cov_pbroua0s4().b[19][0]++, 'enterprise') :
    /* istanbul ignore next */
    (cov_pbroua0s4().b[19][1]++, user.tier));
    /* istanbul ignore next */
    cov_pbroua0s4().s[67]++;
    return {
      ...user,
      name:
      /* istanbul ignore next */
      (cov_pbroua0s4().b[20][0]++, user.name) ||
      /* istanbul ignore next */
      (cov_pbroua0s4().b[20][1]++, 'Unknown User'),
      // Ensure name is always a string
      tier: syncTier,
      // Use mapped tier
      settings: {},
      // Add default empty settings
      sync: {
        version: 1,
        lastModified: new Date(),
        lastModifiedBy: user.id,
        syncStatus: 'local',
        changeLog: [],
        isDeleted: false
      },
      offline: {
        preferences: {
          autoSave: true,
          backupFrequency: 'weekly',
          exportFormat: 'json'
        },
        licenseValidation: {
          isValid: true,
          features: ['air_duct_sizer', 'project_management', 'basic_export']
        }
      }
    };
  }
}
/* istanbul ignore next */
cov_pbroua0s4().s[68]++;
exports.EnhancedUserService = EnhancedUserService;
/**
 * Enhanced calculation service
 * TODO: Implement calculation service without backend dependencies
 */
class EnhancedCalculationService {
  // private calculator: AirDuctCalculator;
  constructor() {
    /* istanbul ignore next */
    cov_pbroua0s4().f[20]++;
  } // this.calculator = new AirDuctCalculator();
  async calculateDuctSizing(inputs) {
    /* istanbul ignore next */
    cov_pbroua0s4().f[21]++;
    cov_pbroua0s4().s[69]++;
    try {
      /* istanbul ignore next */
      cov_pbroua0s4().s[70]++;
      // TODO: Implement actual calculation logic
      return {
        success: false,
        input_data: inputs,
        warnings: ['Calculation service not yet implemented'],
        errors: ['Calculation service is under development']
      };
    } catch (error) {
      /* istanbul ignore next */
      cov_pbroua0s4().s[71]++;
      console.error('Failed to calculate duct sizing:', error);
      /* istanbul ignore next */
      cov_pbroua0s4().s[72]++;
      throw error;
    }
  }
  async validateResults(results) {
    /* istanbul ignore next */
    cov_pbroua0s4().f[22]++;
    cov_pbroua0s4().s[73]++;
    try {
      /* istanbul ignore next */
      cov_pbroua0s4().s[74]++;
      return {
        valid: results.success,
        warnings:
        /* istanbul ignore next */
        (cov_pbroua0s4().b[21][0]++, results.warnings) ||
        /* istanbul ignore next */
        (cov_pbroua0s4().b[21][1]++, []),
        errors: results.success ?
        /* istanbul ignore next */
        (cov_pbroua0s4().b[22][0]++, []) :
        /* istanbul ignore next */
        (cov_pbroua0s4().b[22][1]++, ['Calculation failed'])
      };
    } catch (error) {
      /* istanbul ignore next */
      cov_pbroua0s4().s[75]++;
      console.error('Failed to validate results:', error);
      /* istanbul ignore next */
      cov_pbroua0s4().s[76]++;
      throw error;
    }
  }
  async getCalculationHistory(projectId) {
    /* istanbul ignore next */
    cov_pbroua0s4().f[23]++;
    cov_pbroua0s4().s[77]++;
    try {
      /* istanbul ignore next */
      cov_pbroua0s4().s[78]++;
      // Future enhancement: store calculation history
      return [];
    } catch (error) {
      /* istanbul ignore next */
      cov_pbroua0s4().s[79]++;
      console.error('Failed to get calculation history:', error);
      /* istanbul ignore next */
      cov_pbroua0s4().s[80]++;
      throw error;
    }
  }
}
/* istanbul ignore next */
cov_pbroua0s4().s[81]++;
exports.EnhancedCalculationService = EnhancedCalculationService;
/**
 * Enhanced validation service
 * TODO: Implement validation service without backend dependencies
 */
class EnhancedValidationService {
  // private smacnaValidator: SMACNAValidator;
  constructor() {
    /* istanbul ignore next */
    cov_pbroua0s4().f[24]++;
  } // this.smacnaValidator = new SMACNAValidator();
  async validateProject(project) {
    /* istanbul ignore next */
    cov_pbroua0s4().f[25]++;
    cov_pbroua0s4().s[82]++;
    try {
      const errors =
      /* istanbul ignore next */
      (cov_pbroua0s4().s[83]++, []);
      const warnings =
      /* istanbul ignore next */
      (cov_pbroua0s4().s[84]++, []);
      /* istanbul ignore next */
      cov_pbroua0s4().s[85]++;
      if (!project.project_name?.trim()) {
        /* istanbul ignore next */
        cov_pbroua0s4().b[23][0]++;
        cov_pbroua0s4().s[86]++;
        errors.push('Project name is required');
      } else
      /* istanbul ignore next */
      {
        cov_pbroua0s4().b[23][1]++;
      }
      cov_pbroua0s4().s[87]++;
      if (project.rooms.length === 0) {
        /* istanbul ignore next */
        cov_pbroua0s4().b[24][0]++;
        cov_pbroua0s4().s[88]++;
        warnings.push('Project has no rooms defined');
      } else
      /* istanbul ignore next */
      {
        cov_pbroua0s4().b[24][1]++;
      }
      cov_pbroua0s4().s[89]++;
      if (project.segments.length === 0) {
        /* istanbul ignore next */
        cov_pbroua0s4().b[25][0]++;
        cov_pbroua0s4().s[90]++;
        warnings.push('Project has no duct segments defined');
      } else
      /* istanbul ignore next */
      {
        cov_pbroua0s4().b[25][1]++;
      }
      cov_pbroua0s4().s[91]++;
      return {
        valid: errors.length === 0,
        errors,
        warnings
      };
    } catch (error) {
      /* istanbul ignore next */
      cov_pbroua0s4().s[92]++;
      console.error('Failed to validate project:', error);
      /* istanbul ignore next */
      cov_pbroua0s4().s[93]++;
      throw error;
    }
  }
}
/* istanbul ignore next */
cov_pbroua0s4().s[94]++;
exports.EnhancedValidationService = EnhancedValidationService;
/**
 * Enhanced export service with DataService integration
 */
class EnhancedExportService {
  constructor(dataService) {
    /* istanbul ignore next */
    cov_pbroua0s4().f[26]++;
    cov_pbroua0s4().s[95]++;
    this.dataService = dataService;
    /* istanbul ignore next */
    cov_pbroua0s4().s[96]++;
    this.exportService = (0, ImportExportService_1.createExportService)();
  }
  async exportProject(projectId, options) {
    /* istanbul ignore next */
    cov_pbroua0s4().f[27]++;
    cov_pbroua0s4().s[97]++;
    try {
      const project =
      /* istanbul ignore next */
      (cov_pbroua0s4().s[98]++, await this.dataService.getProject(projectId));
      /* istanbul ignore next */
      cov_pbroua0s4().s[99]++;
      if (!project) {
        /* istanbul ignore next */
        cov_pbroua0s4().b[26][0]++;
        cov_pbroua0s4().s[100]++;
        throw new Error(`Project ${projectId} not found`);
      } else
      /* istanbul ignore next */
      {
        cov_pbroua0s4().b[26][1]++;
      }
      // Map export format to DataService format
      const dataServiceFormat =
      /* istanbul ignore next */
      (cov_pbroua0s4().s[101]++, options.format === 'excel' ?
      /* istanbul ignore next */
      (cov_pbroua0s4().b[27][0]++, 'xlsx') :
      /* istanbul ignore next */
      (cov_pbroua0s4().b[27][1]++, options.format === 'bom' ?
      /* istanbul ignore next */
      (cov_pbroua0s4().b[28][0]++, 'csv') :
      /* istanbul ignore next */
      (cov_pbroua0s4().b[28][1]++, options.format)));
      const result =
      /* istanbul ignore next */
      (cov_pbroua0s4().s[102]++, await this.exportService.exportEntity('project', project, dataServiceFormat));
      // Convert DataService ExportResult to air-duct-sizer ExportResult
      /* istanbul ignore next */
      cov_pbroua0s4().s[103]++;
      return {
        success: result.success,
        exportId: crypto.randomUUID(),
        // Generate missing exportId
        downloadUrl: result.downloadUrl,
        error: result.error
      };
    } catch (error) {
      /* istanbul ignore next */
      cov_pbroua0s4().s[104]++;
      console.error('Failed to export project:', error);
      /* istanbul ignore next */
      cov_pbroua0s4().s[105]++;
      throw error;
    }
  }
  async getExportStatus(exportId) {
    /* istanbul ignore next */
    cov_pbroua0s4().f[28]++;
    cov_pbroua0s4().s[106]++;
    try {
      /* istanbul ignore next */
      cov_pbroua0s4().s[107]++;
      return {
        success: true,
        exportId: exportId,
        downloadUrl: `/api/exports/${exportId}/download`
      };
    } catch (error) {
      /* istanbul ignore next */
      cov_pbroua0s4().s[108]++;
      console.error('Failed to get export status:', error);
      /* istanbul ignore next */
      cov_pbroua0s4().s[109]++;
      throw error;
    }
  }
  async downloadExport(exportId) {
    /* istanbul ignore next */
    cov_pbroua0s4().f[29]++;
    cov_pbroua0s4().s[110]++;
    try {
      /* istanbul ignore next */
      cov_pbroua0s4().s[111]++;
      return new Blob(['Mock export content'], {
        type: 'application/pdf'
      });
    } catch (error) {
      /* istanbul ignore next */
      cov_pbroua0s4().s[112]++;
      console.error('Failed to download export:', error);
      /* istanbul ignore next */
      cov_pbroua0s4().s[113]++;
      throw error;
    }
  }
}
/* istanbul ignore next */
cov_pbroua0s4().s[114]++;
exports.EnhancedExportService = EnhancedExportService;
/**
 * Enhanced tier service
 */
class EnhancedTierService {
  constructor(dataService) {
    /* istanbul ignore next */
    cov_pbroua0s4().f[30]++;
    cov_pbroua0s4().s[115]++;
    this.dataService = dataService;
  }
  async getCurrentTier() {
    /* istanbul ignore next */
    cov_pbroua0s4().f[31]++;
    cov_pbroua0s4().s[116]++;
    try {
      const user =
      /* istanbul ignore next */
      (cov_pbroua0s4().s[117]++, await this.dataService.getCurrentUser());
      /* istanbul ignore next */
      cov_pbroua0s4().s[118]++;
      return /* istanbul ignore next */(cov_pbroua0s4().b[29][0]++, user?.tier) ||
      /* istanbul ignore next */
      (cov_pbroua0s4().b[29][1]++, 'free');
    } catch (error) {
      /* istanbul ignore next */
      cov_pbroua0s4().s[119]++;
      console.error('Failed to get current tier:', error);
      /* istanbul ignore next */
      cov_pbroua0s4().s[120]++;
      return 'free';
    }
  }
  async hasFeatureAccess(feature) {
    /* istanbul ignore next */
    cov_pbroua0s4().f[32]++;
    cov_pbroua0s4().s[121]++;
    try {
      const user =
      /* istanbul ignore next */
      (cov_pbroua0s4().s[122]++, await this.dataService.getCurrentUser());
      const flag =
      /* istanbul ignore next */
      (cov_pbroua0s4().s[123]++, await this.dataService.getFeatureFlag(feature, user?.id));
      /* istanbul ignore next */
      cov_pbroua0s4().s[124]++;
      return /* istanbul ignore next */(cov_pbroua0s4().b[30][0]++, flag?.enabled) ||
      /* istanbul ignore next */
      (cov_pbroua0s4().b[30][1]++, false);
    } catch (error) {
      /* istanbul ignore next */
      cov_pbroua0s4().s[125]++;
      console.error('Failed to check feature access:', error);
      /* istanbul ignore next */
      cov_pbroua0s4().s[126]++;
      return false;
    }
  }
  async getTierLimits() {
    /* istanbul ignore next */
    cov_pbroua0s4().f[33]++;
    cov_pbroua0s4().s[127]++;
    try {
      const tier =
      /* istanbul ignore next */
      (cov_pbroua0s4().s[128]++, await this.getCurrentTier());
      const limits =
      /* istanbul ignore next */
      (cov_pbroua0s4().s[129]++, {
        free: {
          maxRooms: 5,
          maxSegments: 10,
          maxProjects: 3,
          canEditComputationalProperties: false,
          canExportWithoutWatermark: false,
          canUseSimulation: false,
          canUseCatalog: false
        },
        pro: {
          maxRooms: 50,
          maxSegments: 100,
          maxProjects: -1,
          canEditComputationalProperties: true,
          canExportWithoutWatermark: true,
          canUseSimulation: true,
          canUseCatalog: true
        },
        enterprise: {
          maxRooms: -1,
          maxSegments: -1,
          maxProjects: -1,
          canEditComputationalProperties: true,
          canExportWithoutWatermark: true,
          canUseSimulation: true,
          canUseCatalog: true
        },
        super_admin: {
          maxRooms: -1,
          maxSegments: -1,
          maxProjects: -1,
          canEditComputationalProperties: true,
          canExportWithoutWatermark: true,
          canUseSimulation: true,
          canUseCatalog: true
        }
      });
      /* istanbul ignore next */
      cov_pbroua0s4().s[130]++;
      return limits[tier];
    } catch (error) {
      /* istanbul ignore next */
      cov_pbroua0s4().s[131]++;
      console.error('Failed to get tier limits:', error);
      /* istanbul ignore next */
      cov_pbroua0s4().s[132]++;
      return {
        maxRooms: 5,
        maxSegments: 10,
        maxProjects: 3,
        canEditComputationalProperties: false,
        canExportWithoutWatermark: false,
        canUseSimulation: false,
        canUseCatalog: false
      };
    }
  }
  async upgradeTier(newTier) {
    /* istanbul ignore next */
    cov_pbroua0s4().f[34]++;
    cov_pbroua0s4().s[133]++;
    try {
      const user =
      /* istanbul ignore next */
      (cov_pbroua0s4().s[134]++, await this.dataService.getCurrentUser());
      /* istanbul ignore next */
      cov_pbroua0s4().s[135]++;
      if (user) {
        /* istanbul ignore next */
        cov_pbroua0s4().b[31][0]++;
        // Map super_admin to enterprise for SyncableUser compatibility
        const syncTier =
        /* istanbul ignore next */
        (cov_pbroua0s4().s[136]++, newTier === 'super_admin' ?
        /* istanbul ignore next */
        (cov_pbroua0s4().b[32][0]++, 'enterprise') :
        /* istanbul ignore next */
        (cov_pbroua0s4().b[32][1]++, newTier));
        const syncableUser =
        /* istanbul ignore next */
        (cov_pbroua0s4().s[137]++, {
          ...user,
          name:
          /* istanbul ignore next */
          (cov_pbroua0s4().b[33][0]++, user.name) ||
          /* istanbul ignore next */
          (cov_pbroua0s4().b[33][1]++, 'Unknown User'),
          // Ensure name is always a string
          tier: syncTier,
          settings: {},
          // Add default empty settings
          updatedAt: new Date(),
          sync: {
            version: 1,
            lastModified: new Date(),
            lastModifiedBy: user.id,
            syncStatus: 'local',
            changeLog: [],
            isDeleted: false
          },
          offline: {
            preferences: {
              autoSave: true,
              backupFrequency: 'weekly',
              exportFormat: 'json'
            },
            licenseValidation: {
              isValid: true,
              features: ['air_duct_sizer', 'project_management', 'basic_export']
            }
          }
        });
        /* istanbul ignore next */
        cov_pbroua0s4().s[138]++;
        await this.dataService.saveUser(syncableUser);
      } else
      /* istanbul ignore next */
      {
        cov_pbroua0s4().b[31][1]++;
      }
    } catch (error) {
      /* istanbul ignore next */
      cov_pbroua0s4().s[139]++;
      console.error('Failed to upgrade tier:', error);
      /* istanbul ignore next */
      cov_pbroua0s4().s[140]++;
      throw error;
    }
  }
}
/* istanbul ignore next */
cov_pbroua0s4().s[141]++;
exports.EnhancedTierService = EnhancedTierService;
/**
 * Enhanced sync service for future online mode
 */
class EnhancedSyncService {
  constructor(dataService) {
    /* istanbul ignore next */
    cov_pbroua0s4().f[35]++;
    cov_pbroua0s4().s[142]++;
    this.dataService = dataService;
  }
  async getPendingOperations() {
    /* istanbul ignore next */
    cov_pbroua0s4().f[36]++;
    cov_pbroua0s4().s[143]++;
    return await this.dataService.getPendingSyncOperations();
  }
  async getConnectionStatus() {
    /* istanbul ignore next */
    cov_pbroua0s4().f[37]++;
    cov_pbroua0s4().s[144]++;
    return this.dataService.getConnectionStatus();
  }
  async forceSyncAll() {
    /* istanbul ignore next */
    cov_pbroua0s4().f[38]++;
    cov_pbroua0s4().s[145]++;
    return await this.dataService.forceSyncAll();
  }
  async getSyncStatistics() {
    /* istanbul ignore next */
    cov_pbroua0s4().f[39]++;
    cov_pbroua0s4().s[146]++;
    return await this.dataService.getSyncStatistics();
  }
}
/* istanbul ignore next */
cov_pbroua0s4().s[147]++;
exports.EnhancedSyncService = EnhancedSyncService;
/**
 * Create enhanced offline service container with robust error handling
 */
async function createEnhancedOfflineServiceContainer() {
  /* istanbul ignore next */
  cov_pbroua0s4().f[40]++;
  cov_pbroua0s4().s[148]++;
  try {
    /* istanbul ignore next */
    cov_pbroua0s4().s[149]++;
    console.log('🔧 Initializing DataService...');
    // Initialize DataService with timeout
    const dataService =
    /* istanbul ignore next */
    (cov_pbroua0s4().s[150]++, await (0, DataService_1.createDataService)('local'));
    // Add timeout for data service initialization
    const initPromise =
    /* istanbul ignore next */
    (cov_pbroua0s4().s[151]++, dataService.initialize());
    const timeoutPromise =
    /* istanbul ignore next */
    (cov_pbroua0s4().s[152]++, new Promise((_, reject) => {
      /* istanbul ignore next */
      cov_pbroua0s4().f[41]++;
      cov_pbroua0s4().s[153]++;
      setTimeout(() => {
        /* istanbul ignore next */
        cov_pbroua0s4().f[42]++;
        cov_pbroua0s4().s[154]++;
        reject(new Error('DataService initialization timed out after 20 seconds'));
      }, 20000);
    }));
    /* istanbul ignore next */
    cov_pbroua0s4().s[155]++;
    await Promise.race([initPromise, timeoutPromise]);
    /* istanbul ignore next */
    cov_pbroua0s4().s[156]++;
    console.log('✅ DataService initialized successfully');
    /* istanbul ignore next */
    cov_pbroua0s4().s[157]++;
    console.log('🔧 Creating service instances...');
    // Create services with error handling
    const projectService =
    /* istanbul ignore next */
    (cov_pbroua0s4().s[158]++, new EnhancedProjectService(dataService));
    const userService =
    /* istanbul ignore next */
    (cov_pbroua0s4().s[159]++, new EnhancedUserService(dataService));
    const calculationService =
    /* istanbul ignore next */
    (cov_pbroua0s4().s[160]++, new EnhancedCalculationService());
    const validationService =
    /* istanbul ignore next */
    (cov_pbroua0s4().s[161]++, new EnhancedValidationService());
    const exportService =
    /* istanbul ignore next */
    (cov_pbroua0s4().s[162]++, new EnhancedExportService(dataService));
    const tierService =
    /* istanbul ignore next */
    (cov_pbroua0s4().s[163]++, new EnhancedTierService(dataService));
    const syncService =
    /* istanbul ignore next */
    (cov_pbroua0s4().s[164]++, new EnhancedSyncService(dataService));
    /* istanbul ignore next */
    cov_pbroua0s4().s[165]++;
    console.log('🔧 Creating auxiliary services...');
    // Create import/export services
    const importService =
    /* istanbul ignore next */
    (cov_pbroua0s4().s[166]++, (0, ImportExportService_1.createImportService)());
    const backupService =
    /* istanbul ignore next */
    (cov_pbroua0s4().s[167]++, (0, ImportExportService_1.createBackupService)());
    // Create feature manager
    const featureManager =
    /* istanbul ignore next */
    (cov_pbroua0s4().s[168]++, (0, BrowserFeatureManager_1.createBrowserFeatureManager)(dataService));
    /* istanbul ignore next */
    cov_pbroua0s4().s[169]++;
    console.log('✅ All services created successfully');
    /* istanbul ignore next */
    cov_pbroua0s4().s[170]++;
    return {
      dataService,
      projectService,
      userService,
      calculationService,
      validationService,
      exportService,
      tierService,
      featureManager,
      importService,
      backupService,
      syncService
    };
  } catch (error) {
    /* istanbul ignore next */
    cov_pbroua0s4().s[171]++;
    console.error('❌ Failed to create enhanced offline service container:', error);
    // Re-throw with more context
    /* istanbul ignore next */
    cov_pbroua0s4().s[172]++;
    if (error instanceof Error) {
      /* istanbul ignore next */
      cov_pbroua0s4().b[34][0]++;
      cov_pbroua0s4().s[173]++;
      throw new Error(`Service container creation failed: ${error.message}`);
    } else
    /* istanbul ignore next */
    {
      cov_pbroua0s4().b[34][1]++;
    }
    cov_pbroua0s4().s[174]++;
    throw new Error('Unknown error occurred while creating service container');
  }
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJjb3ZfcGJyb3VhMHM0IiwicGF0aCIsImhhc2giLCJnbG9iYWwiLCJGdW5jdGlvbiIsImdjdiIsImNvdmVyYWdlRGF0YSIsInN0YXRlbWVudE1hcCIsInN0YXJ0IiwibGluZSIsImNvbHVtbiIsImVuZCIsImZuTWFwIiwibmFtZSIsImRlY2wiLCJsb2MiLCJicmFuY2hNYXAiLCJ0eXBlIiwibG9jYXRpb25zIiwidW5kZWZpbmVkIiwicyIsImYiLCJiIiwiaW5wdXRTb3VyY2VNYXAiLCJmaWxlIiwibWFwcGluZ3MiLCJuYW1lcyIsInNvdXJjZXMiLCJzb3VyY2VzQ29udGVudCIsInZlcnNpb24iLCJfY292ZXJhZ2VTY2hlbWEiLCJjb3ZlcmFnZSIsImFjdHVhbENvdmVyYWdlIiwiZXhwb3J0cyIsImNyZWF0ZUVuaGFuY2VkT2ZmbGluZVNlcnZpY2VDb250YWluZXIiLCJEYXRhU2VydmljZV8xIiwicmVxdWlyZSIsIkltcG9ydEV4cG9ydFNlcnZpY2VfMSIsIkJyb3dzZXJGZWF0dXJlTWFuYWdlcl8xIiwiRW5oYW5jZWRQcm9qZWN0U2VydmljZSIsImNvbnN0cnVjdG9yIiwiZGF0YVNlcnZpY2UiLCJnZXRQcm9qZWN0IiwiaWQiLCJzeW5jYWJsZVByb2plY3QiLCJjb252ZXJ0VG9MZWdhY3lQcm9qZWN0IiwiZXJyb3IiLCJjb25zb2xlIiwic2F2ZVByb2plY3QiLCJwcm9qZWN0IiwiY29udmVydFRvU3luY2FibGVQcm9qZWN0IiwiY3JlYXRlUHJvamVjdCIsImRhdGEiLCJwcm9qZWN0RGF0YSIsIm5ld1Byb2plY3QiLCJnZW5lcmF0ZVVVSUQiLCJ1c2VySWQiLCJwcm9qZWN0X25hbWUiLCJwcm9qZWN0X251bWJlciIsImdlbmVyYXRlUHJvamVjdE51bWJlciIsInByb2plY3RfZGVzY3JpcHRpb24iLCJwcm9qZWN0X2xvY2F0aW9uIiwiY2xpZW50X25hbWUiLCJlc3RpbWF0b3JfbmFtZSIsImRhdGVfY3JlYXRlZCIsIkRhdGUiLCJ0b0lTT1N0cmluZyIsImxhc3RfbW9kaWZpZWQiLCJyb29tcyIsInNlZ21lbnRzIiwiZXF1aXBtZW50IiwiY29tcHV0YXRpb25hbF9wcm9wZXJ0aWVzIiwidW5pdHMiLCJkZWZhdWx0X2R1Y3Rfc2l6ZSIsIndpZHRoIiwiaGVpZ2h0IiwiZGVmYXVsdF9tYXRlcmlhbCIsImRlZmF1bHRfaW5zdWxhdGlvbiIsImRlZmF1bHRfZml0dGluZyIsImNhbGlicmF0aW9uX21vZGUiLCJkZWZhdWx0X3ZlbG9jaXR5IiwicHJlc3N1cmVfY2xhc3MiLCJhbHRpdHVkZSIsImZyaWN0aW9uX3JhdGUiLCJjb2RlX3N0YW5kYXJkcyIsInNtYWNuYSIsImFzaHJhZSIsInVsIiwiaW1jIiwibmZwYSIsImRlbGV0ZVByb2plY3QiLCJsaXN0UHJvamVjdHMiLCJzeW5jYWJsZVByb2plY3RzIiwibGlzdFByb2plY3RzQnlVc2VyIiwibWFwIiwicCIsInNlYXJjaFByb2plY3RzIiwicXVlcnkiLCJzeW5jIiwibGFzdE1vZGlmaWVkIiwibGFzdE1vZGlmaWVkQnkiLCJzeW5jU3RhdHVzIiwiY2hhbmdlTG9nIiwiaXNEZWxldGVkIiwiY29sbGFib3JhdGlvbiIsInNoYXJpbmciLCJpc1NoYXJlZCIsInNoYXJlZFdpdGgiLCJwZXJtaXNzaW9ucyIsImNvbW1lbnRzIiwibG9jayIsImlzTG9ja2VkIiwib2ZmbGluZSIsImJhY2t1cCIsImF1dG9CYWNrdXAiLCJyZXBsYWNlIiwiYyIsInIiLCJNYXRoIiwicmFuZG9tIiwidiIsInRvU3RyaW5nIiwidGltZXN0YW1wIiwibm93Iiwic2xpY2UiLCJFbmhhbmNlZFVzZXJTZXJ2aWNlIiwiZ2V0Q3VycmVudFVzZXIiLCJzeW5jYWJsZVVzZXIiLCJjb252ZXJ0VG9MZWdhY3lVc2VyIiwiZ2V0VXNlckJ5SWQiLCJnZXRVc2VyIiwidXBkYXRlVXNlciIsInVzZXIiLCJjb252ZXJ0VG9TeW5jYWJsZVVzZXIiLCJzYXZlVXNlciIsImVtYWlsIiwidGllciIsImNvbXBhbnkiLCJsaWNlbnNlS2V5IiwiY3JlYXRlZEF0IiwidXBkYXRlZEF0Iiwic3luY1RpZXIiLCJzZXR0aW5ncyIsInByZWZlcmVuY2VzIiwiYXV0b1NhdmUiLCJiYWNrdXBGcmVxdWVuY3kiLCJleHBvcnRGb3JtYXQiLCJsaWNlbnNlVmFsaWRhdGlvbiIsImlzVmFsaWQiLCJmZWF0dXJlcyIsIkVuaGFuY2VkQ2FsY3VsYXRpb25TZXJ2aWNlIiwiY2FsY3VsYXRlRHVjdFNpemluZyIsImlucHV0cyIsInN1Y2Nlc3MiLCJpbnB1dF9kYXRhIiwid2FybmluZ3MiLCJlcnJvcnMiLCJ2YWxpZGF0ZVJlc3VsdHMiLCJyZXN1bHRzIiwidmFsaWQiLCJnZXRDYWxjdWxhdGlvbkhpc3RvcnkiLCJwcm9qZWN0SWQiLCJFbmhhbmNlZFZhbGlkYXRpb25TZXJ2aWNlIiwidmFsaWRhdGVQcm9qZWN0IiwidHJpbSIsInB1c2giLCJsZW5ndGgiLCJFbmhhbmNlZEV4cG9ydFNlcnZpY2UiLCJleHBvcnRTZXJ2aWNlIiwiY3JlYXRlRXhwb3J0U2VydmljZSIsImV4cG9ydFByb2plY3QiLCJvcHRpb25zIiwiRXJyb3IiLCJkYXRhU2VydmljZUZvcm1hdCIsImZvcm1hdCIsInJlc3VsdCIsImV4cG9ydEVudGl0eSIsImV4cG9ydElkIiwiY3J5cHRvIiwicmFuZG9tVVVJRCIsImRvd25sb2FkVXJsIiwiZ2V0RXhwb3J0U3RhdHVzIiwiZG93bmxvYWRFeHBvcnQiLCJCbG9iIiwiRW5oYW5jZWRUaWVyU2VydmljZSIsImdldEN1cnJlbnRUaWVyIiwiaGFzRmVhdHVyZUFjY2VzcyIsImZlYXR1cmUiLCJmbGFnIiwiZ2V0RmVhdHVyZUZsYWciLCJlbmFibGVkIiwiZ2V0VGllckxpbWl0cyIsImxpbWl0cyIsImZyZWUiLCJtYXhSb29tcyIsIm1heFNlZ21lbnRzIiwibWF4UHJvamVjdHMiLCJjYW5FZGl0Q29tcHV0YXRpb25hbFByb3BlcnRpZXMiLCJjYW5FeHBvcnRXaXRob3V0V2F0ZXJtYXJrIiwiY2FuVXNlU2ltdWxhdGlvbiIsImNhblVzZUNhdGFsb2ciLCJwcm8iLCJlbnRlcnByaXNlIiwic3VwZXJfYWRtaW4iLCJ1cGdyYWRlVGllciIsIm5ld1RpZXIiLCJFbmhhbmNlZFN5bmNTZXJ2aWNlIiwiZ2V0UGVuZGluZ09wZXJhdGlvbnMiLCJnZXRQZW5kaW5nU3luY09wZXJhdGlvbnMiLCJnZXRDb25uZWN0aW9uU3RhdHVzIiwiZm9yY2VTeW5jQWxsIiwiZ2V0U3luY1N0YXRpc3RpY3MiLCJsb2ciLCJjcmVhdGVEYXRhU2VydmljZSIsImluaXRQcm9taXNlIiwiaW5pdGlhbGl6ZSIsInRpbWVvdXRQcm9taXNlIiwiUHJvbWlzZSIsIl8iLCJyZWplY3QiLCJzZXRUaW1lb3V0IiwicmFjZSIsInByb2plY3RTZXJ2aWNlIiwidXNlclNlcnZpY2UiLCJjYWxjdWxhdGlvblNlcnZpY2UiLCJ2YWxpZGF0aW9uU2VydmljZSIsInRpZXJTZXJ2aWNlIiwic3luY1NlcnZpY2UiLCJpbXBvcnRTZXJ2aWNlIiwiY3JlYXRlSW1wb3J0U2VydmljZSIsImJhY2t1cFNlcnZpY2UiLCJjcmVhdGVCYWNrdXBTZXJ2aWNlIiwiZmVhdHVyZU1hbmFnZXIiLCJjcmVhdGVCcm93c2VyRmVhdHVyZU1hbmFnZXIiLCJtZXNzYWdlIl0sInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxqb2huclxcRG93bmxvYWRzXFxTaXplV2lzZV9TdWl0ZV9BcHBcXGZyb250ZW5kXFxsaWJcXHNlcnZpY2VzXFxFbmhhbmNlZE9mZmxpbmVTZXJ2aWNlQ29udGFpbmVyLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxyXG4gKiBFbmhhbmNlZCBPZmZsaW5lIFNlcnZpY2UgQ29udGFpbmVyXHJcbiAqIFxyXG4gKiBJbXBsZW1lbnRzIENoYXRHUFQgcmVjb21tZW5kYXRpb25zIHdpdGggY29tcHJlaGVuc2l2ZSBEYXRhU2VydmljZSBpbnRlZ3JhdGlvbi5cclxuICogUHJvdmlkZXMgcm9idXN0IG9mZmxpbmUtZmlyc3QgYXJjaGl0ZWN0dXJlIHdpdGggc3luYyBwcmVwYXJhdGlvbi5cclxuICogXHJcbiAqIEBzZWUgZG9jcy9yZWZhY3RvcmluZy9jb21wb25lbnQtYXJjaGl0ZWN0dXJlLXNwZWNpZmljYXRpb24ubWRcclxuICovXHJcblxyXG5pbXBvcnQgeyBjcmVhdGVEYXRhU2VydmljZSwgRGF0YVNlcnZpY2UgfSBmcm9tICcuLi9kYXRhL0RhdGFTZXJ2aWNlJztcclxuaW1wb3J0IHsgY3JlYXRlRXhwb3J0U2VydmljZSwgY3JlYXRlSW1wb3J0U2VydmljZSwgY3JlYXRlQmFja3VwU2VydmljZSB9IGZyb20gJy4uL2RhdGEvSW1wb3J0RXhwb3J0U2VydmljZSc7XHJcbmltcG9ydCB7IGNyZWF0ZUJyb3dzZXJGZWF0dXJlTWFuYWdlciwgQnJvd3NlckZlYXR1cmVNYW5hZ2VyIH0gZnJvbSAnLi4vZmVhdHVyZXMvQnJvd3NlckZlYXR1cmVNYW5hZ2VyJztcclxuaW1wb3J0IHtcclxuICBDYWxjdWxhdGlvbklucHV0LFxyXG4gIENhbGN1bGF0aW9uUmVzdWx0LFxyXG4gIEV4cG9ydE9wdGlvbnMsXHJcbiAgRXhwb3J0UmVzdWx0LFxyXG4gIFRpZXJMaW1pdHNcclxufSBmcm9tICcuLi8uLi90eXBlcy9haXItZHVjdC1zaXplcic7XHJcbmltcG9ydCB7IFByb2plY3QgfSBmcm9tICcuLi9yZXBvc2l0b3JpZXMvaW50ZXJmYWNlcy9Qcm9qZWN0UmVwb3NpdG9yeSc7XHJcbmltcG9ydCB7IFVzZXIsIFVzZXJUaWVyIH0gZnJvbSAnLi4vcmVwb3NpdG9yaWVzL2ludGVyZmFjZXMvVXNlclJlcG9zaXRvcnknO1xyXG5pbXBvcnQgeyBTeW5jYWJsZVVzZXIsIFN5bmNhYmxlUHJvamVjdCB9IGZyb20gJy4uLy4uL3R5cGVzL3N5bmMtbW9kZWxzJztcclxuXHJcbi8qKlxyXG4gKiBFbmhhbmNlZCBzZXJ2aWNlIGNvbnRhaW5lciBpbnRlcmZhY2Ugd2l0aCBEYXRhU2VydmljZSBpbnRlZ3JhdGlvblxyXG4gKi9cclxuZXhwb3J0IGludGVyZmFjZSBFbmhhbmNlZE9mZmxpbmVTZXJ2aWNlQ29udGFpbmVyIHtcclxuICAvLyBDb3JlIHNlcnZpY2VzXHJcbiAgZGF0YVNlcnZpY2U6IERhdGFTZXJ2aWNlO1xyXG4gIHByb2plY3RTZXJ2aWNlOiBFbmhhbmNlZFByb2plY3RTZXJ2aWNlO1xyXG4gIHVzZXJTZXJ2aWNlOiBFbmhhbmNlZFVzZXJTZXJ2aWNlO1xyXG4gIGNhbGN1bGF0aW9uU2VydmljZTogRW5oYW5jZWRDYWxjdWxhdGlvblNlcnZpY2U7XHJcbiAgdmFsaWRhdGlvblNlcnZpY2U6IEVuaGFuY2VkVmFsaWRhdGlvblNlcnZpY2U7XHJcbiAgZXhwb3J0U2VydmljZTogRW5oYW5jZWRFeHBvcnRTZXJ2aWNlO1xyXG4gIHRpZXJTZXJ2aWNlOiBFbmhhbmNlZFRpZXJTZXJ2aWNlO1xyXG4gIGZlYXR1cmVNYW5hZ2VyOiBCcm93c2VyRmVhdHVyZU1hbmFnZXI7XHJcbiAgXHJcbiAgLy8gTmV3IHNlcnZpY2VzIGZyb20gQ2hhdEdQVCByZWNvbW1lbmRhdGlvbnNcclxuICBpbXBvcnRTZXJ2aWNlOiBhbnk7IC8vIEltcG9ydFNlcnZpY2VcclxuICBiYWNrdXBTZXJ2aWNlOiBhbnk7IC8vIEJhY2t1cFNlcnZpY2VcclxuICBzeW5jU2VydmljZTogRW5oYW5jZWRTeW5jU2VydmljZTtcclxufVxyXG5cclxuLyoqXHJcbiAqIEVuaGFuY2VkIHByb2plY3Qgc2VydmljZSB3aXRoIERhdGFTZXJ2aWNlIGludGVncmF0aW9uXHJcbiAqL1xyXG5leHBvcnQgY2xhc3MgRW5oYW5jZWRQcm9qZWN0U2VydmljZSB7XHJcbiAgY29uc3RydWN0b3IocHJpdmF0ZSBkYXRhU2VydmljZTogRGF0YVNlcnZpY2UpIHt9XHJcblxyXG4gIGFzeW5jIGdldFByb2plY3QoaWQ6IHN0cmluZyk6IFByb21pc2U8UHJvamVjdCB8IG51bGw+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHN5bmNhYmxlUHJvamVjdCA9IGF3YWl0IHRoaXMuZGF0YVNlcnZpY2UuZ2V0UHJvamVjdChpZCk7XHJcbiAgICAgIHJldHVybiBzeW5jYWJsZVByb2plY3QgPyB0aGlzLmNvbnZlcnRUb0xlZ2FjeVByb2plY3Qoc3luY2FibGVQcm9qZWN0KSA6IG51bGw7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gZ2V0IHByb2plY3Q6JywgZXJyb3IpO1xyXG4gICAgICB0aHJvdyBlcnJvcjtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIGFzeW5jIHNhdmVQcm9qZWN0KHByb2plY3Q6IFByb2plY3QpOiBQcm9taXNlPHZvaWQ+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHN5bmNhYmxlUHJvamVjdCA9IHRoaXMuY29udmVydFRvU3luY2FibGVQcm9qZWN0KHByb2plY3QpO1xyXG4gICAgICBhd2FpdCB0aGlzLmRhdGFTZXJ2aWNlLnNhdmVQcm9qZWN0KHN5bmNhYmxlUHJvamVjdCk7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gc2F2ZSBwcm9qZWN0OicsIGVycm9yKTtcclxuICAgICAgdGhyb3cgZXJyb3I7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICBhc3luYyBjcmVhdGVQcm9qZWN0KGRhdGE6IFBhcnRpYWw8UHJvamVjdD4pOiBQcm9taXNlPFByb2plY3Q+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIC8vIENhc3QgdG8gYW55IHRvIGF2b2lkIFR5cGVTY3JpcHQgaXNzdWVzIHdpdGggUGFydGlhbDxQcm9qZWN0PlxyXG4gICAgICBjb25zdCBwcm9qZWN0RGF0YSA9IGRhdGEgYXMgYW55O1xyXG5cclxuICAgICAgY29uc3QgbmV3UHJvamVjdDogUHJvamVjdCA9IHtcclxuICAgICAgICBpZDogcHJvamVjdERhdGEuaWQgfHwgdGhpcy5nZW5lcmF0ZVVVSUQoKSxcclxuICAgICAgICB1c2VySWQ6IHByb2plY3REYXRhLnVzZXJJZCB8fCAnb2ZmbGluZS11c2VyLTAwMScsXHJcbiAgICAgICAgcHJvamVjdF9uYW1lOiBwcm9qZWN0RGF0YS5wcm9qZWN0X25hbWUgfHwgJ05ldyBQcm9qZWN0JyxcclxuICAgICAgICBwcm9qZWN0X251bWJlcjogcHJvamVjdERhdGEucHJvamVjdF9udW1iZXIgfHwgdGhpcy5nZW5lcmF0ZVByb2plY3ROdW1iZXIoKSxcclxuICAgICAgICBwcm9qZWN0X2Rlc2NyaXB0aW9uOiBwcm9qZWN0RGF0YS5wcm9qZWN0X2Rlc2NyaXB0aW9uIHx8ICcnLFxyXG4gICAgICAgIHByb2plY3RfbG9jYXRpb246IHByb2plY3REYXRhLnByb2plY3RfbG9jYXRpb24gfHwgJycsXHJcbiAgICAgICAgY2xpZW50X25hbWU6IHByb2plY3REYXRhLmNsaWVudF9uYW1lIHx8ICcnLFxyXG4gICAgICAgIGVzdGltYXRvcl9uYW1lOiBwcm9qZWN0RGF0YS5lc3RpbWF0b3JfbmFtZSB8fCAnJyxcclxuICAgICAgICBkYXRlX2NyZWF0ZWQ6IHByb2plY3REYXRhLmRhdGVfY3JlYXRlZCB8fCBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXHJcbiAgICAgICAgbGFzdF9tb2RpZmllZDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxyXG4gICAgICAgIHZlcnNpb246IHByb2plY3REYXRhLnZlcnNpb24gfHwgJzEuMCcsXHJcbiAgICAgICAgcm9vbXM6IHByb2plY3REYXRhLnJvb21zIHx8IFtdLFxyXG4gICAgICAgIHNlZ21lbnRzOiBwcm9qZWN0RGF0YS5zZWdtZW50cyB8fCBbXSxcclxuICAgICAgICBlcXVpcG1lbnQ6IHByb2plY3REYXRhLmVxdWlwbWVudCB8fCBbXSxcclxuICAgICAgICBjb21wdXRhdGlvbmFsX3Byb3BlcnRpZXM6IHByb2plY3REYXRhLmNvbXB1dGF0aW9uYWxfcHJvcGVydGllcyB8fCB7XHJcbiAgICAgICAgICB1bml0czogJ0ltcGVyaWFsJyxcclxuICAgICAgICAgIGRlZmF1bHRfZHVjdF9zaXplOiB7IHdpZHRoOiAxMiwgaGVpZ2h0OiA4IH0sXHJcbiAgICAgICAgICBkZWZhdWx0X21hdGVyaWFsOiAnR2FsdmFuaXplZCBTdGVlbCcsXHJcbiAgICAgICAgICBkZWZhdWx0X2luc3VsYXRpb246ICdOb25lJyxcclxuICAgICAgICAgIGRlZmF1bHRfZml0dGluZzogJ1N0YW5kYXJkJyxcclxuICAgICAgICAgIGNhbGlicmF0aW9uX21vZGU6ICdBdXRvJyxcclxuICAgICAgICAgIGRlZmF1bHRfdmVsb2NpdHk6IDEwMDAsXHJcbiAgICAgICAgICBwcmVzc3VyZV9jbGFzczogXCIyXCIsXHJcbiAgICAgICAgICBhbHRpdHVkZTogMCxcclxuICAgICAgICAgIGZyaWN0aW9uX3JhdGU6IDAuMVxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgY29kZV9zdGFuZGFyZHM6IHByb2plY3REYXRhLmNvZGVfc3RhbmRhcmRzIHx8IHtcclxuICAgICAgICAgIHNtYWNuYTogdHJ1ZSxcclxuICAgICAgICAgIGFzaHJhZTogdHJ1ZSxcclxuICAgICAgICAgIHVsOiBmYWxzZSxcclxuICAgICAgICAgIGltYzogZmFsc2UsXHJcbiAgICAgICAgICBuZnBhOiBmYWxzZVxyXG4gICAgICAgIH1cclxuICAgICAgfTtcclxuXHJcbiAgICAgIGF3YWl0IHRoaXMuc2F2ZVByb2plY3QobmV3UHJvamVjdCk7XHJcbiAgICAgIHJldHVybiBuZXdQcm9qZWN0O1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGNyZWF0ZSBwcm9qZWN0OicsIGVycm9yKTtcclxuICAgICAgdGhyb3cgZXJyb3I7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICBhc3luYyBkZWxldGVQcm9qZWN0KGlkOiBzdHJpbmcpOiBQcm9taXNlPHZvaWQ+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGF3YWl0IHRoaXMuZGF0YVNlcnZpY2UuZGVsZXRlUHJvamVjdChpZCk7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gZGVsZXRlIHByb2plY3Q6JywgZXJyb3IpO1xyXG4gICAgICB0aHJvdyBlcnJvcjtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIGFzeW5jIGxpc3RQcm9qZWN0cyh1c2VySWQ6IHN0cmluZyk6IFByb21pc2U8UHJvamVjdFtdPiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCBzeW5jYWJsZVByb2plY3RzID0gYXdhaXQgdGhpcy5kYXRhU2VydmljZS5saXN0UHJvamVjdHNCeVVzZXIodXNlcklkKTtcclxuICAgICAgcmV0dXJuIHN5bmNhYmxlUHJvamVjdHMubWFwKHAgPT4gdGhpcy5jb252ZXJ0VG9MZWdhY3lQcm9qZWN0KHApKTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBsaXN0IHByb2plY3RzOicsIGVycm9yKTtcclxuICAgICAgdGhyb3cgZXJyb3I7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICBhc3luYyBzZWFyY2hQcm9qZWN0cyhxdWVyeTogc3RyaW5nLCB1c2VySWQ/OiBzdHJpbmcpOiBQcm9taXNlPFByb2plY3RbXT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3Qgc3luY2FibGVQcm9qZWN0cyA9IGF3YWl0IHRoaXMuZGF0YVNlcnZpY2Uuc2VhcmNoUHJvamVjdHMocXVlcnksIHVzZXJJZCk7XHJcbiAgICAgIHJldHVybiBzeW5jYWJsZVByb2plY3RzLm1hcChwID0+IHRoaXMuY29udmVydFRvTGVnYWN5UHJvamVjdChwKSk7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gc2VhcmNoIHByb2plY3RzOicsIGVycm9yKTtcclxuICAgICAgdGhyb3cgZXJyb3I7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICBwcml2YXRlIGNvbnZlcnRUb0xlZ2FjeVByb2plY3Qoc3luY2FibGVQcm9qZWN0OiBTeW5jYWJsZVByb2plY3QpOiBQcm9qZWN0IHtcclxuICAgIC8vIENvbnZlcnQgU3luY2FibGVQcm9qZWN0IHRvIGxlZ2FjeSBQcm9qZWN0IGZvcm1hdFxyXG4gICAgcmV0dXJuIHtcclxuICAgICAgaWQ6IHN5bmNhYmxlUHJvamVjdC5pZCxcclxuICAgICAgdXNlcklkOiBzeW5jYWJsZVByb2plY3QudXNlcklkLFxyXG4gICAgICBwcm9qZWN0X25hbWU6IHN5bmNhYmxlUHJvamVjdC5wcm9qZWN0X25hbWUsXHJcbiAgICAgIHByb2plY3RfbnVtYmVyOiBzeW5jYWJsZVByb2plY3QucHJvamVjdF9udW1iZXIsXHJcbiAgICAgIHByb2plY3RfZGVzY3JpcHRpb246IHN5bmNhYmxlUHJvamVjdC5wcm9qZWN0X2Rlc2NyaXB0aW9uLFxyXG4gICAgICBwcm9qZWN0X2xvY2F0aW9uOiBzeW5jYWJsZVByb2plY3QucHJvamVjdF9sb2NhdGlvbixcclxuICAgICAgY2xpZW50X25hbWU6IHN5bmNhYmxlUHJvamVjdC5jbGllbnRfbmFtZSxcclxuICAgICAgZXN0aW1hdG9yX25hbWU6IHN5bmNhYmxlUHJvamVjdC5lc3RpbWF0b3JfbmFtZSxcclxuICAgICAgZGF0ZV9jcmVhdGVkOiBzeW5jYWJsZVByb2plY3QuZGF0ZV9jcmVhdGVkLFxyXG4gICAgICBsYXN0X21vZGlmaWVkOiBzeW5jYWJsZVByb2plY3QubGFzdF9tb2RpZmllZCxcclxuICAgICAgdmVyc2lvbjogc3luY2FibGVQcm9qZWN0LnZlcnNpb24sXHJcbiAgICAgIHJvb21zOiBzeW5jYWJsZVByb2plY3Qucm9vbXMsXHJcbiAgICAgIHNlZ21lbnRzOiBzeW5jYWJsZVByb2plY3Quc2VnbWVudHMsXHJcbiAgICAgIGVxdWlwbWVudDogc3luY2FibGVQcm9qZWN0LmVxdWlwbWVudCxcclxuICAgICAgY29tcHV0YXRpb25hbF9wcm9wZXJ0aWVzOiBzeW5jYWJsZVByb2plY3QuY29tcHV0YXRpb25hbF9wcm9wZXJ0aWVzLFxyXG4gICAgICBjb2RlX3N0YW5kYXJkczogc3luY2FibGVQcm9qZWN0LmNvZGVfc3RhbmRhcmRzXHJcbiAgICB9O1xyXG4gIH1cclxuXHJcbiAgcHJpdmF0ZSBjb252ZXJ0VG9TeW5jYWJsZVByb2plY3QocHJvamVjdDogUHJvamVjdCk6IFN5bmNhYmxlUHJvamVjdCB7XHJcbiAgICAvLyBDb252ZXJ0IGxlZ2FjeSBQcm9qZWN0IHRvIFN5bmNhYmxlUHJvamVjdCBmb3JtYXRcclxuICAgIHJldHVybiB7XHJcbiAgICAgIC4uLnByb2plY3QsXHJcbiAgICAgIHN5bmM6IHtcclxuICAgICAgICB2ZXJzaW9uOiAxLFxyXG4gICAgICAgIGxhc3RNb2RpZmllZDogbmV3IERhdGUoKSxcclxuICAgICAgICBsYXN0TW9kaWZpZWRCeTogcHJvamVjdC51c2VySWQsXHJcbiAgICAgICAgc3luY1N0YXR1czogJ2xvY2FsJyxcclxuICAgICAgICBjaGFuZ2VMb2c6IFtdLFxyXG4gICAgICAgIGlzRGVsZXRlZDogZmFsc2VcclxuICAgICAgfSxcclxuICAgICAgY29sbGFib3JhdGlvbjoge1xyXG4gICAgICAgIHNoYXJpbmc6IHtcclxuICAgICAgICAgIGlzU2hhcmVkOiBmYWxzZSxcclxuICAgICAgICAgIHNoYXJlZFdpdGg6IFtdLFxyXG4gICAgICAgICAgcGVybWlzc2lvbnM6IHt9XHJcbiAgICAgICAgfSxcclxuICAgICAgICBjb21tZW50czogW10sXHJcbiAgICAgICAgbG9jazoge1xyXG4gICAgICAgICAgaXNMb2NrZWQ6IGZhbHNlXHJcbiAgICAgICAgfVxyXG4gICAgICB9LFxyXG4gICAgICBvZmZsaW5lOiB7XHJcbiAgICAgICAgYmFja3VwOiB7XHJcbiAgICAgICAgICBhdXRvQmFja3VwOiB0cnVlXHJcbiAgICAgICAgfSxcclxuICAgICAgICBleHBvcnRzOiBbXVxyXG4gICAgICB9XHJcbiAgICB9O1xyXG4gIH1cclxuXHJcbiAgcHJpdmF0ZSBnZW5lcmF0ZVVVSUQoKTogc3RyaW5nIHtcclxuICAgIHJldHVybiAneHh4eHh4eHgteHh4eC00eHh4LXl4eHgteHh4eHh4eHh4eHh4Jy5yZXBsYWNlKC9beHldL2csIGZ1bmN0aW9uKGMpIHtcclxuICAgICAgY29uc3QgciA9IE1hdGgucmFuZG9tKCkgKiAxNiB8IDA7XHJcbiAgICAgIGNvbnN0IHYgPSBjID09ICd4JyA/IHIgOiAociAmIDB4MyB8IDB4OCk7XHJcbiAgICAgIHJldHVybiB2LnRvU3RyaW5nKDE2KTtcclxuICAgIH0pO1xyXG4gIH1cclxuXHJcbiAgcHJpdmF0ZSBnZW5lcmF0ZVByb2plY3ROdW1iZXIoKTogc3RyaW5nIHtcclxuICAgIGNvbnN0IHRpbWVzdGFtcCA9IERhdGUubm93KCkudG9TdHJpbmcoKS5zbGljZSgtNik7XHJcbiAgICByZXR1cm4gYFBSSi0ke3RpbWVzdGFtcH1gO1xyXG4gIH1cclxufVxyXG5cclxuLyoqXHJcbiAqIEVuaGFuY2VkIHVzZXIgc2VydmljZSB3aXRoIERhdGFTZXJ2aWNlIGludGVncmF0aW9uXHJcbiAqL1xyXG5leHBvcnQgY2xhc3MgRW5oYW5jZWRVc2VyU2VydmljZSB7XHJcbiAgY29uc3RydWN0b3IocHJpdmF0ZSBkYXRhU2VydmljZTogRGF0YVNlcnZpY2UpIHt9XHJcblxyXG4gIGFzeW5jIGdldEN1cnJlbnRVc2VyKCk6IFByb21pc2U8VXNlciB8IG51bGw+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHN5bmNhYmxlVXNlciA9IGF3YWl0IHRoaXMuZGF0YVNlcnZpY2UuZ2V0Q3VycmVudFVzZXIoKTtcclxuICAgICAgcmV0dXJuIHN5bmNhYmxlVXNlciA/IHRoaXMuY29udmVydFRvTGVnYWN5VXNlcihzeW5jYWJsZVVzZXIpIDogbnVsbDtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBnZXQgY3VycmVudCB1c2VyOicsIGVycm9yKTtcclxuICAgICAgdGhyb3cgZXJyb3I7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICBhc3luYyBnZXRVc2VyQnlJZChpZDogc3RyaW5nKTogUHJvbWlzZTxVc2VyIHwgbnVsbD4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3Qgc3luY2FibGVVc2VyID0gYXdhaXQgdGhpcy5kYXRhU2VydmljZS5nZXRVc2VyKGlkKTtcclxuICAgICAgcmV0dXJuIHN5bmNhYmxlVXNlciA/IHRoaXMuY29udmVydFRvTGVnYWN5VXNlcihzeW5jYWJsZVVzZXIpIDogbnVsbDtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBnZXQgdXNlcjonLCBlcnJvcik7XHJcbiAgICAgIHRocm93IGVycm9yO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgYXN5bmMgdXBkYXRlVXNlcih1c2VyOiBVc2VyKTogUHJvbWlzZTx2b2lkPiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCBzeW5jYWJsZVVzZXIgPSB0aGlzLmNvbnZlcnRUb1N5bmNhYmxlVXNlcih1c2VyKTtcclxuICAgICAgYXdhaXQgdGhpcy5kYXRhU2VydmljZS5zYXZlVXNlcihzeW5jYWJsZVVzZXIpO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIHVwZGF0ZSB1c2VyOicsIGVycm9yKTtcclxuICAgICAgdGhyb3cgZXJyb3I7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICBwcml2YXRlIGNvbnZlcnRUb0xlZ2FjeVVzZXIoc3luY2FibGVVc2VyOiBTeW5jYWJsZVVzZXIpOiBVc2VyIHtcclxuICAgIHJldHVybiB7XHJcbiAgICAgIGlkOiBzeW5jYWJsZVVzZXIuaWQsXHJcbiAgICAgIGVtYWlsOiBzeW5jYWJsZVVzZXIuZW1haWwsXHJcbiAgICAgIG5hbWU6IHN5bmNhYmxlVXNlci5uYW1lLFxyXG4gICAgICB0aWVyOiBzeW5jYWJsZVVzZXIudGllcixcclxuICAgICAgY29tcGFueTogc3luY2FibGVVc2VyLmNvbXBhbnksXHJcbiAgICAgIGxpY2Vuc2VLZXk6IHN5bmNhYmxlVXNlci5saWNlbnNlS2V5LFxyXG4gICAgICBjcmVhdGVkQXQ6IHN5bmNhYmxlVXNlci5jcmVhdGVkQXQsXHJcbiAgICAgIHVwZGF0ZWRBdDogc3luY2FibGVVc2VyLnVwZGF0ZWRBdFxyXG4gICAgfTtcclxuICB9XHJcblxyXG4gIHByaXZhdGUgY29udmVydFRvU3luY2FibGVVc2VyKHVzZXI6IFVzZXIpOiBTeW5jYWJsZVVzZXIge1xyXG4gICAgLy8gTWFwIHN1cGVyX2FkbWluIHRvIGVudGVycHJpc2UgZm9yIFN5bmNhYmxlVXNlciBjb21wYXRpYmlsaXR5XHJcbiAgICBjb25zdCBzeW5jVGllciA9IHVzZXIudGllciA9PT0gJ3N1cGVyX2FkbWluJyA/ICdlbnRlcnByaXNlJyA6IHVzZXIudGllciBhcyAnZnJlZScgfCAncHJvJyB8ICdlbnRlcnByaXNlJztcclxuXHJcbiAgICByZXR1cm4ge1xyXG4gICAgICAuLi51c2VyLFxyXG4gICAgICBuYW1lOiB1c2VyLm5hbWUgfHwgJ1Vua25vd24gVXNlcicsIC8vIEVuc3VyZSBuYW1lIGlzIGFsd2F5cyBhIHN0cmluZ1xyXG4gICAgICB0aWVyOiBzeW5jVGllciwgLy8gVXNlIG1hcHBlZCB0aWVyXHJcbiAgICAgIHNldHRpbmdzOiB7fSwgLy8gQWRkIGRlZmF1bHQgZW1wdHkgc2V0dGluZ3NcclxuICAgICAgc3luYzoge1xyXG4gICAgICAgIHZlcnNpb246IDEsXHJcbiAgICAgICAgbGFzdE1vZGlmaWVkOiBuZXcgRGF0ZSgpLFxyXG4gICAgICAgIGxhc3RNb2RpZmllZEJ5OiB1c2VyLmlkLFxyXG4gICAgICAgIHN5bmNTdGF0dXM6ICdsb2NhbCcsXHJcbiAgICAgICAgY2hhbmdlTG9nOiBbXSxcclxuICAgICAgICBpc0RlbGV0ZWQ6IGZhbHNlXHJcbiAgICAgIH0sXHJcbiAgICAgIG9mZmxpbmU6IHtcclxuICAgICAgICBwcmVmZXJlbmNlczoge1xyXG4gICAgICAgICAgYXV0b1NhdmU6IHRydWUsXHJcbiAgICAgICAgICBiYWNrdXBGcmVxdWVuY3k6ICd3ZWVrbHknLFxyXG4gICAgICAgICAgZXhwb3J0Rm9ybWF0OiAnanNvbidcclxuICAgICAgICB9LFxyXG4gICAgICAgIGxpY2Vuc2VWYWxpZGF0aW9uOiB7XHJcbiAgICAgICAgICBpc1ZhbGlkOiB0cnVlLFxyXG4gICAgICAgICAgZmVhdHVyZXM6IFsnYWlyX2R1Y3Rfc2l6ZXInLCAncHJvamVjdF9tYW5hZ2VtZW50JywgJ2Jhc2ljX2V4cG9ydCddXHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9O1xyXG4gIH1cclxufVxyXG5cclxuLyoqXHJcbiAqIEVuaGFuY2VkIGNhbGN1bGF0aW9uIHNlcnZpY2VcclxuICogVE9ETzogSW1wbGVtZW50IGNhbGN1bGF0aW9uIHNlcnZpY2Ugd2l0aG91dCBiYWNrZW5kIGRlcGVuZGVuY2llc1xyXG4gKi9cclxuZXhwb3J0IGNsYXNzIEVuaGFuY2VkQ2FsY3VsYXRpb25TZXJ2aWNlIHtcclxuICAvLyBwcml2YXRlIGNhbGN1bGF0b3I6IEFpckR1Y3RDYWxjdWxhdG9yO1xyXG5cclxuICBjb25zdHJ1Y3RvcigpIHtcclxuICAgIC8vIHRoaXMuY2FsY3VsYXRvciA9IG5ldyBBaXJEdWN0Q2FsY3VsYXRvcigpO1xyXG4gIH1cclxuXHJcbiAgYXN5bmMgY2FsY3VsYXRlRHVjdFNpemluZyhpbnB1dHM6IENhbGN1bGF0aW9uSW5wdXQpOiBQcm9taXNlPENhbGN1bGF0aW9uUmVzdWx0PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICAvLyBUT0RPOiBJbXBsZW1lbnQgYWN0dWFsIGNhbGN1bGF0aW9uIGxvZ2ljXHJcbiAgICAgIHJldHVybiB7XHJcbiAgICAgICAgc3VjY2VzczogZmFsc2UsXHJcbiAgICAgICAgaW5wdXRfZGF0YTogaW5wdXRzLFxyXG4gICAgICAgIHdhcm5pbmdzOiBbJ0NhbGN1bGF0aW9uIHNlcnZpY2Ugbm90IHlldCBpbXBsZW1lbnRlZCddLFxyXG4gICAgICAgIGVycm9yczogWydDYWxjdWxhdGlvbiBzZXJ2aWNlIGlzIHVuZGVyIGRldmVsb3BtZW50J11cclxuICAgICAgfTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBjYWxjdWxhdGUgZHVjdCBzaXppbmc6JywgZXJyb3IpO1xyXG4gICAgICB0aHJvdyBlcnJvcjtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIGFzeW5jIHZhbGlkYXRlUmVzdWx0cyhyZXN1bHRzOiBDYWxjdWxhdGlvblJlc3VsdCk6IFByb21pc2U8YW55PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICByZXR1cm4ge1xyXG4gICAgICAgIHZhbGlkOiByZXN1bHRzLnN1Y2Nlc3MsXHJcbiAgICAgICAgd2FybmluZ3M6IHJlc3VsdHMud2FybmluZ3MgfHwgW10sXHJcbiAgICAgICAgZXJyb3JzOiByZXN1bHRzLnN1Y2Nlc3MgPyBbXSA6IFsnQ2FsY3VsYXRpb24gZmFpbGVkJ11cclxuICAgICAgfTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byB2YWxpZGF0ZSByZXN1bHRzOicsIGVycm9yKTtcclxuICAgICAgdGhyb3cgZXJyb3I7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICBhc3luYyBnZXRDYWxjdWxhdGlvbkhpc3RvcnkocHJvamVjdElkOiBzdHJpbmcpOiBQcm9taXNlPENhbGN1bGF0aW9uUmVzdWx0W10+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIC8vIEZ1dHVyZSBlbmhhbmNlbWVudDogc3RvcmUgY2FsY3VsYXRpb24gaGlzdG9yeVxyXG4gICAgICByZXR1cm4gW107XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gZ2V0IGNhbGN1bGF0aW9uIGhpc3Rvcnk6JywgZXJyb3IpO1xyXG4gICAgICB0aHJvdyBlcnJvcjtcclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbi8qKlxyXG4gKiBFbmhhbmNlZCB2YWxpZGF0aW9uIHNlcnZpY2VcclxuICogVE9ETzogSW1wbGVtZW50IHZhbGlkYXRpb24gc2VydmljZSB3aXRob3V0IGJhY2tlbmQgZGVwZW5kZW5jaWVzXHJcbiAqL1xyXG5leHBvcnQgY2xhc3MgRW5oYW5jZWRWYWxpZGF0aW9uU2VydmljZSB7XHJcbiAgLy8gcHJpdmF0ZSBzbWFjbmFWYWxpZGF0b3I6IFNNQUNOQVZhbGlkYXRvcjtcclxuXHJcbiAgY29uc3RydWN0b3IoKSB7XHJcbiAgICAvLyB0aGlzLnNtYWNuYVZhbGlkYXRvciA9IG5ldyBTTUFDTkFWYWxpZGF0b3IoKTtcclxuICB9XHJcblxyXG4gIGFzeW5jIHZhbGlkYXRlUHJvamVjdChwcm9qZWN0OiBQcm9qZWN0KTogUHJvbWlzZTxhbnk+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IGVycm9ycyA9IFtdO1xyXG4gICAgICBjb25zdCB3YXJuaW5ncyA9IFtdO1xyXG5cclxuICAgICAgaWYgKCFwcm9qZWN0LnByb2plY3RfbmFtZT8udHJpbSgpKSB7XHJcbiAgICAgICAgZXJyb3JzLnB1c2goJ1Byb2plY3QgbmFtZSBpcyByZXF1aXJlZCcpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBpZiAocHJvamVjdC5yb29tcy5sZW5ndGggPT09IDApIHtcclxuICAgICAgICB3YXJuaW5ncy5wdXNoKCdQcm9qZWN0IGhhcyBubyByb29tcyBkZWZpbmVkJyk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGlmIChwcm9qZWN0LnNlZ21lbnRzLmxlbmd0aCA9PT0gMCkge1xyXG4gICAgICAgIHdhcm5pbmdzLnB1c2goJ1Byb2plY3QgaGFzIG5vIGR1Y3Qgc2VnbWVudHMgZGVmaW5lZCcpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICByZXR1cm4ge1xyXG4gICAgICAgIHZhbGlkOiBlcnJvcnMubGVuZ3RoID09PSAwLFxyXG4gICAgICAgIGVycm9ycyxcclxuICAgICAgICB3YXJuaW5nc1xyXG4gICAgICB9O1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIHZhbGlkYXRlIHByb2plY3Q6JywgZXJyb3IpO1xyXG4gICAgICB0aHJvdyBlcnJvcjtcclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbi8qKlxyXG4gKiBFbmhhbmNlZCBleHBvcnQgc2VydmljZSB3aXRoIERhdGFTZXJ2aWNlIGludGVncmF0aW9uXHJcbiAqL1xyXG5leHBvcnQgY2xhc3MgRW5oYW5jZWRFeHBvcnRTZXJ2aWNlIHtcclxuICBwcml2YXRlIGV4cG9ydFNlcnZpY2UgPSBjcmVhdGVFeHBvcnRTZXJ2aWNlKCk7XHJcblxyXG4gIGNvbnN0cnVjdG9yKHByaXZhdGUgZGF0YVNlcnZpY2U6IERhdGFTZXJ2aWNlKSB7fVxyXG5cclxuICBhc3luYyBleHBvcnRQcm9qZWN0KHByb2plY3RJZDogc3RyaW5nLCBvcHRpb25zOiBFeHBvcnRPcHRpb25zKTogUHJvbWlzZTxFeHBvcnRSZXN1bHQ+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHByb2plY3QgPSBhd2FpdCB0aGlzLmRhdGFTZXJ2aWNlLmdldFByb2plY3QocHJvamVjdElkKTtcclxuICAgICAgaWYgKCFwcm9qZWN0KSB7XHJcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBQcm9qZWN0ICR7cHJvamVjdElkfSBub3QgZm91bmRgKTtcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gTWFwIGV4cG9ydCBmb3JtYXQgdG8gRGF0YVNlcnZpY2UgZm9ybWF0XHJcbiAgICAgIGNvbnN0IGRhdGFTZXJ2aWNlRm9ybWF0ID0gb3B0aW9ucy5mb3JtYXQgPT09ICdleGNlbCcgPyAneGxzeCcgOlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9wdGlvbnMuZm9ybWF0ID09PSAnYm9tJyA/ICdjc3YnIDpcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvcHRpb25zLmZvcm1hdCBhcyAncGRmJyB8ICdqc29uJztcclxuXHJcbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHRoaXMuZXhwb3J0U2VydmljZS5leHBvcnRFbnRpdHkoJ3Byb2plY3QnLCBwcm9qZWN0LCBkYXRhU2VydmljZUZvcm1hdCk7XHJcblxyXG4gICAgICAvLyBDb252ZXJ0IERhdGFTZXJ2aWNlIEV4cG9ydFJlc3VsdCB0byBhaXItZHVjdC1zaXplciBFeHBvcnRSZXN1bHRcclxuICAgICAgcmV0dXJuIHtcclxuICAgICAgICBzdWNjZXNzOiByZXN1bHQuc3VjY2VzcyxcclxuICAgICAgICBleHBvcnRJZDogY3J5cHRvLnJhbmRvbVVVSUQoKSwgLy8gR2VuZXJhdGUgbWlzc2luZyBleHBvcnRJZFxyXG4gICAgICAgIGRvd25sb2FkVXJsOiByZXN1bHQuZG93bmxvYWRVcmwsXHJcbiAgICAgICAgZXJyb3I6IHJlc3VsdC5lcnJvclxyXG4gICAgICB9O1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGV4cG9ydCBwcm9qZWN0OicsIGVycm9yKTtcclxuICAgICAgdGhyb3cgZXJyb3I7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICBhc3luYyBnZXRFeHBvcnRTdGF0dXMoZXhwb3J0SWQ6IHN0cmluZyk6IFByb21pc2U8RXhwb3J0UmVzdWx0PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICByZXR1cm4ge1xyXG4gICAgICAgIHN1Y2Nlc3M6IHRydWUsXHJcbiAgICAgICAgZXhwb3J0SWQ6IGV4cG9ydElkLFxyXG4gICAgICAgIGRvd25sb2FkVXJsOiBgL2FwaS9leHBvcnRzLyR7ZXhwb3J0SWR9L2Rvd25sb2FkYFxyXG4gICAgICB9O1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGdldCBleHBvcnQgc3RhdHVzOicsIGVycm9yKTtcclxuICAgICAgdGhyb3cgZXJyb3I7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICBhc3luYyBkb3dubG9hZEV4cG9ydChleHBvcnRJZDogc3RyaW5nKTogUHJvbWlzZTxCbG9iPiB7XHJcbiAgICB0cnkge1xyXG4gICAgICByZXR1cm4gbmV3IEJsb2IoWydNb2NrIGV4cG9ydCBjb250ZW50J10sIHsgdHlwZTogJ2FwcGxpY2F0aW9uL3BkZicgfSk7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gZG93bmxvYWQgZXhwb3J0OicsIGVycm9yKTtcclxuICAgICAgdGhyb3cgZXJyb3I7XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG4vKipcclxuICogRW5oYW5jZWQgdGllciBzZXJ2aWNlXHJcbiAqL1xyXG5leHBvcnQgY2xhc3MgRW5oYW5jZWRUaWVyU2VydmljZSB7XHJcbiAgY29uc3RydWN0b3IocHJpdmF0ZSBkYXRhU2VydmljZTogRGF0YVNlcnZpY2UpIHt9XHJcblxyXG4gIGFzeW5jIGdldEN1cnJlbnRUaWVyKCk6IFByb21pc2U8VXNlclRpZXI+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHVzZXIgPSBhd2FpdCB0aGlzLmRhdGFTZXJ2aWNlLmdldEN1cnJlbnRVc2VyKCk7XHJcbiAgICAgIHJldHVybiB1c2VyPy50aWVyIHx8ICdmcmVlJztcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBnZXQgY3VycmVudCB0aWVyOicsIGVycm9yKTtcclxuICAgICAgcmV0dXJuICdmcmVlJztcclxuICAgIH1cclxuICB9XHJcblxyXG4gIGFzeW5jIGhhc0ZlYXR1cmVBY2Nlc3MoZmVhdHVyZTogc3RyaW5nKTogUHJvbWlzZTxib29sZWFuPiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCB1c2VyID0gYXdhaXQgdGhpcy5kYXRhU2VydmljZS5nZXRDdXJyZW50VXNlcigpO1xyXG4gICAgICBjb25zdCBmbGFnID0gYXdhaXQgdGhpcy5kYXRhU2VydmljZS5nZXRGZWF0dXJlRmxhZyhmZWF0dXJlLCB1c2VyPy5pZCk7XHJcbiAgICAgIHJldHVybiBmbGFnPy5lbmFibGVkIHx8IGZhbHNlO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGNoZWNrIGZlYXR1cmUgYWNjZXNzOicsIGVycm9yKTtcclxuICAgICAgcmV0dXJuIGZhbHNlO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgYXN5bmMgZ2V0VGllckxpbWl0cygpOiBQcm9taXNlPFRpZXJMaW1pdHM+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHRpZXIgPSBhd2FpdCB0aGlzLmdldEN1cnJlbnRUaWVyKCk7XHJcbiAgICAgIFxyXG4gICAgICBjb25zdCBsaW1pdHM6IFJlY29yZDxVc2VyVGllciwgVGllckxpbWl0cz4gPSB7XHJcbiAgICAgICAgZnJlZToge1xyXG4gICAgICAgICAgbWF4Um9vbXM6IDUsXHJcbiAgICAgICAgICBtYXhTZWdtZW50czogMTAsXHJcbiAgICAgICAgICBtYXhQcm9qZWN0czogMyxcclxuICAgICAgICAgIGNhbkVkaXRDb21wdXRhdGlvbmFsUHJvcGVydGllczogZmFsc2UsXHJcbiAgICAgICAgICBjYW5FeHBvcnRXaXRob3V0V2F0ZXJtYXJrOiBmYWxzZSxcclxuICAgICAgICAgIGNhblVzZVNpbXVsYXRpb246IGZhbHNlLFxyXG4gICAgICAgICAgY2FuVXNlQ2F0YWxvZzogZmFsc2VcclxuICAgICAgICB9LFxyXG4gICAgICAgIHBybzoge1xyXG4gICAgICAgICAgbWF4Um9vbXM6IDUwLFxyXG4gICAgICAgICAgbWF4U2VnbWVudHM6IDEwMCxcclxuICAgICAgICAgIG1heFByb2plY3RzOiAtMSxcclxuICAgICAgICAgIGNhbkVkaXRDb21wdXRhdGlvbmFsUHJvcGVydGllczogdHJ1ZSxcclxuICAgICAgICAgIGNhbkV4cG9ydFdpdGhvdXRXYXRlcm1hcms6IHRydWUsXHJcbiAgICAgICAgICBjYW5Vc2VTaW11bGF0aW9uOiB0cnVlLFxyXG4gICAgICAgICAgY2FuVXNlQ2F0YWxvZzogdHJ1ZVxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgZW50ZXJwcmlzZToge1xyXG4gICAgICAgICAgbWF4Um9vbXM6IC0xLFxyXG4gICAgICAgICAgbWF4U2VnbWVudHM6IC0xLFxyXG4gICAgICAgICAgbWF4UHJvamVjdHM6IC0xLFxyXG4gICAgICAgICAgY2FuRWRpdENvbXB1dGF0aW9uYWxQcm9wZXJ0aWVzOiB0cnVlLFxyXG4gICAgICAgICAgY2FuRXhwb3J0V2l0aG91dFdhdGVybWFyazogdHJ1ZSxcclxuICAgICAgICAgIGNhblVzZVNpbXVsYXRpb246IHRydWUsXHJcbiAgICAgICAgICBjYW5Vc2VDYXRhbG9nOiB0cnVlXHJcbiAgICAgICAgfSxcclxuICAgICAgICBzdXBlcl9hZG1pbjoge1xyXG4gICAgICAgICAgbWF4Um9vbXM6IC0xLFxyXG4gICAgICAgICAgbWF4U2VnbWVudHM6IC0xLFxyXG4gICAgICAgICAgbWF4UHJvamVjdHM6IC0xLFxyXG4gICAgICAgICAgY2FuRWRpdENvbXB1dGF0aW9uYWxQcm9wZXJ0aWVzOiB0cnVlLFxyXG4gICAgICAgICAgY2FuRXhwb3J0V2l0aG91dFdhdGVybWFyazogdHJ1ZSxcclxuICAgICAgICAgIGNhblVzZVNpbXVsYXRpb246IHRydWUsXHJcbiAgICAgICAgICBjYW5Vc2VDYXRhbG9nOiB0cnVlXHJcbiAgICAgICAgfVxyXG4gICAgICB9O1xyXG5cclxuICAgICAgcmV0dXJuIGxpbWl0c1t0aWVyXTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBnZXQgdGllciBsaW1pdHM6JywgZXJyb3IpO1xyXG4gICAgICByZXR1cm4ge1xyXG4gICAgICAgIG1heFJvb21zOiA1LFxyXG4gICAgICAgIG1heFNlZ21lbnRzOiAxMCxcclxuICAgICAgICBtYXhQcm9qZWN0czogMyxcclxuICAgICAgICBjYW5FZGl0Q29tcHV0YXRpb25hbFByb3BlcnRpZXM6IGZhbHNlLFxyXG4gICAgICAgIGNhbkV4cG9ydFdpdGhvdXRXYXRlcm1hcms6IGZhbHNlLFxyXG4gICAgICAgIGNhblVzZVNpbXVsYXRpb246IGZhbHNlLFxyXG4gICAgICAgIGNhblVzZUNhdGFsb2c6IGZhbHNlXHJcbiAgICAgIH07XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICBhc3luYyB1cGdyYWRlVGllcihuZXdUaWVyOiBVc2VyVGllcik6IFByb21pc2U8dm9pZD4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgdXNlciA9IGF3YWl0IHRoaXMuZGF0YVNlcnZpY2UuZ2V0Q3VycmVudFVzZXIoKTtcclxuICAgICAgaWYgKHVzZXIpIHtcclxuICAgICAgICAvLyBNYXAgc3VwZXJfYWRtaW4gdG8gZW50ZXJwcmlzZSBmb3IgU3luY2FibGVVc2VyIGNvbXBhdGliaWxpdHlcclxuICAgICAgICBjb25zdCBzeW5jVGllciA9IG5ld1RpZXIgPT09ICdzdXBlcl9hZG1pbicgPyAnZW50ZXJwcmlzZScgOiBuZXdUaWVyIGFzICdmcmVlJyB8ICdwcm8nIHwgJ2VudGVycHJpc2UnO1xyXG5cclxuICAgICAgICBjb25zdCBzeW5jYWJsZVVzZXIgPSB7XHJcbiAgICAgICAgICAuLi51c2VyLFxyXG4gICAgICAgICAgbmFtZTogdXNlci5uYW1lIHx8ICdVbmtub3duIFVzZXInLCAvLyBFbnN1cmUgbmFtZSBpcyBhbHdheXMgYSBzdHJpbmdcclxuICAgICAgICAgIHRpZXI6IHN5bmNUaWVyLFxyXG4gICAgICAgICAgc2V0dGluZ3M6IHt9LCAvLyBBZGQgZGVmYXVsdCBlbXB0eSBzZXR0aW5nc1xyXG4gICAgICAgICAgdXBkYXRlZEF0OiBuZXcgRGF0ZSgpLFxyXG4gICAgICAgICAgc3luYzoge1xyXG4gICAgICAgICAgICB2ZXJzaW9uOiAxLFxyXG4gICAgICAgICAgICBsYXN0TW9kaWZpZWQ6IG5ldyBEYXRlKCksXHJcbiAgICAgICAgICAgIGxhc3RNb2RpZmllZEJ5OiB1c2VyLmlkLFxyXG4gICAgICAgICAgICBzeW5jU3RhdHVzOiAnbG9jYWwnIGFzIGNvbnN0LFxyXG4gICAgICAgICAgICBjaGFuZ2VMb2c6IFtdLFxyXG4gICAgICAgICAgICBpc0RlbGV0ZWQ6IGZhbHNlXHJcbiAgICAgICAgICB9LFxyXG4gICAgICAgICAgb2ZmbGluZToge1xyXG4gICAgICAgICAgICBwcmVmZXJlbmNlczoge1xyXG4gICAgICAgICAgICAgIGF1dG9TYXZlOiB0cnVlLFxyXG4gICAgICAgICAgICAgIGJhY2t1cEZyZXF1ZW5jeTogJ3dlZWtseScgYXMgY29uc3QsXHJcbiAgICAgICAgICAgICAgZXhwb3J0Rm9ybWF0OiAnanNvbicgYXMgY29uc3RcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgbGljZW5zZVZhbGlkYXRpb246IHtcclxuICAgICAgICAgICAgICBpc1ZhbGlkOiB0cnVlLFxyXG4gICAgICAgICAgICAgIGZlYXR1cmVzOiBbJ2Fpcl9kdWN0X3NpemVyJywgJ3Byb2plY3RfbWFuYWdlbWVudCcsICdiYXNpY19leHBvcnQnXVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfTtcclxuICAgICAgICBhd2FpdCB0aGlzLmRhdGFTZXJ2aWNlLnNhdmVVc2VyKHN5bmNhYmxlVXNlcik7XHJcbiAgICAgIH1cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byB1cGdyYWRlIHRpZXI6JywgZXJyb3IpO1xyXG4gICAgICB0aHJvdyBlcnJvcjtcclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbi8qKlxyXG4gKiBFbmhhbmNlZCBzeW5jIHNlcnZpY2UgZm9yIGZ1dHVyZSBvbmxpbmUgbW9kZVxyXG4gKi9cclxuZXhwb3J0IGNsYXNzIEVuaGFuY2VkU3luY1NlcnZpY2Uge1xyXG4gIGNvbnN0cnVjdG9yKHByaXZhdGUgZGF0YVNlcnZpY2U6IERhdGFTZXJ2aWNlKSB7fVxyXG5cclxuICBhc3luYyBnZXRQZW5kaW5nT3BlcmF0aW9ucygpIHtcclxuICAgIHJldHVybiBhd2FpdCB0aGlzLmRhdGFTZXJ2aWNlLmdldFBlbmRpbmdTeW5jT3BlcmF0aW9ucygpO1xyXG4gIH1cclxuXHJcbiAgYXN5bmMgZ2V0Q29ubmVjdGlvblN0YXR1cygpIHtcclxuICAgIHJldHVybiB0aGlzLmRhdGFTZXJ2aWNlLmdldENvbm5lY3Rpb25TdGF0dXMoKTtcclxuICB9XHJcblxyXG4gIGFzeW5jIGZvcmNlU3luY0FsbCgpIHtcclxuICAgIHJldHVybiBhd2FpdCB0aGlzLmRhdGFTZXJ2aWNlLmZvcmNlU3luY0FsbCgpO1xyXG4gIH1cclxuXHJcbiAgYXN5bmMgZ2V0U3luY1N0YXRpc3RpY3MoKSB7XHJcbiAgICByZXR1cm4gYXdhaXQgdGhpcy5kYXRhU2VydmljZS5nZXRTeW5jU3RhdGlzdGljcygpO1xyXG4gIH1cclxufVxyXG5cclxuLyoqXHJcbiAqIENyZWF0ZSBlbmhhbmNlZCBvZmZsaW5lIHNlcnZpY2UgY29udGFpbmVyIHdpdGggcm9idXN0IGVycm9yIGhhbmRsaW5nXHJcbiAqL1xyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gY3JlYXRlRW5oYW5jZWRPZmZsaW5lU2VydmljZUNvbnRhaW5lcigpOiBQcm9taXNlPEVuaGFuY2VkT2ZmbGluZVNlcnZpY2VDb250YWluZXI+IHtcclxuICB0cnkge1xyXG4gICAgY29uc29sZS5sb2coJ/CflKcgSW5pdGlhbGl6aW5nIERhdGFTZXJ2aWNlLi4uJyk7XHJcblxyXG4gICAgLy8gSW5pdGlhbGl6ZSBEYXRhU2VydmljZSB3aXRoIHRpbWVvdXRcclxuICAgIGNvbnN0IGRhdGFTZXJ2aWNlID0gYXdhaXQgY3JlYXRlRGF0YVNlcnZpY2UoJ2xvY2FsJyk7XHJcblxyXG4gICAgLy8gQWRkIHRpbWVvdXQgZm9yIGRhdGEgc2VydmljZSBpbml0aWFsaXphdGlvblxyXG4gICAgY29uc3QgaW5pdFByb21pc2UgPSBkYXRhU2VydmljZS5pbml0aWFsaXplKCk7XHJcbiAgICBjb25zdCB0aW1lb3V0UHJvbWlzZSA9IG5ldyBQcm9taXNlPG5ldmVyPigoXywgcmVqZWN0KSA9PiB7XHJcbiAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAgIHJlamVjdChuZXcgRXJyb3IoJ0RhdGFTZXJ2aWNlIGluaXRpYWxpemF0aW9uIHRpbWVkIG91dCBhZnRlciAyMCBzZWNvbmRzJykpO1xyXG4gICAgICB9LCAyMDAwMCk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBhd2FpdCBQcm9taXNlLnJhY2UoW2luaXRQcm9taXNlLCB0aW1lb3V0UHJvbWlzZV0pO1xyXG4gICAgY29uc29sZS5sb2coJ+KchSBEYXRhU2VydmljZSBpbml0aWFsaXplZCBzdWNjZXNzZnVsbHknKTtcclxuXHJcbiAgICBjb25zb2xlLmxvZygn8J+UpyBDcmVhdGluZyBzZXJ2aWNlIGluc3RhbmNlcy4uLicpO1xyXG5cclxuICAgIC8vIENyZWF0ZSBzZXJ2aWNlcyB3aXRoIGVycm9yIGhhbmRsaW5nXHJcbiAgICBjb25zdCBwcm9qZWN0U2VydmljZSA9IG5ldyBFbmhhbmNlZFByb2plY3RTZXJ2aWNlKGRhdGFTZXJ2aWNlKTtcclxuICAgIGNvbnN0IHVzZXJTZXJ2aWNlID0gbmV3IEVuaGFuY2VkVXNlclNlcnZpY2UoZGF0YVNlcnZpY2UpO1xyXG4gICAgY29uc3QgY2FsY3VsYXRpb25TZXJ2aWNlID0gbmV3IEVuaGFuY2VkQ2FsY3VsYXRpb25TZXJ2aWNlKCk7XHJcbiAgICBjb25zdCB2YWxpZGF0aW9uU2VydmljZSA9IG5ldyBFbmhhbmNlZFZhbGlkYXRpb25TZXJ2aWNlKCk7XHJcbiAgICBjb25zdCBleHBvcnRTZXJ2aWNlID0gbmV3IEVuaGFuY2VkRXhwb3J0U2VydmljZShkYXRhU2VydmljZSk7XHJcbiAgICBjb25zdCB0aWVyU2VydmljZSA9IG5ldyBFbmhhbmNlZFRpZXJTZXJ2aWNlKGRhdGFTZXJ2aWNlKTtcclxuICAgIGNvbnN0IHN5bmNTZXJ2aWNlID0gbmV3IEVuaGFuY2VkU3luY1NlcnZpY2UoZGF0YVNlcnZpY2UpO1xyXG5cclxuICAgIGNvbnNvbGUubG9nKCfwn5SnIENyZWF0aW5nIGF1eGlsaWFyeSBzZXJ2aWNlcy4uLicpO1xyXG5cclxuICAgIC8vIENyZWF0ZSBpbXBvcnQvZXhwb3J0IHNlcnZpY2VzXHJcbiAgICBjb25zdCBpbXBvcnRTZXJ2aWNlID0gY3JlYXRlSW1wb3J0U2VydmljZSgpO1xyXG4gICAgY29uc3QgYmFja3VwU2VydmljZSA9IGNyZWF0ZUJhY2t1cFNlcnZpY2UoKTtcclxuXHJcbiAgICAvLyBDcmVhdGUgZmVhdHVyZSBtYW5hZ2VyXHJcbiAgICBjb25zdCBmZWF0dXJlTWFuYWdlciA9IGNyZWF0ZUJyb3dzZXJGZWF0dXJlTWFuYWdlcihkYXRhU2VydmljZSk7XHJcblxyXG4gICAgY29uc29sZS5sb2coJ+KchSBBbGwgc2VydmljZXMgY3JlYXRlZCBzdWNjZXNzZnVsbHknKTtcclxuXHJcbiAgICByZXR1cm4ge1xyXG4gICAgICBkYXRhU2VydmljZSxcclxuICAgICAgcHJvamVjdFNlcnZpY2UsXHJcbiAgICAgIHVzZXJTZXJ2aWNlLFxyXG4gICAgICBjYWxjdWxhdGlvblNlcnZpY2UsXHJcbiAgICAgIHZhbGlkYXRpb25TZXJ2aWNlLFxyXG4gICAgICBleHBvcnRTZXJ2aWNlLFxyXG4gICAgICB0aWVyU2VydmljZSxcclxuICAgICAgZmVhdHVyZU1hbmFnZXIsXHJcbiAgICAgIGltcG9ydFNlcnZpY2UsXHJcbiAgICAgIGJhY2t1cFNlcnZpY2UsXHJcbiAgICAgIHN5bmNTZXJ2aWNlXHJcbiAgICB9O1xyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKCfinYwgRmFpbGVkIHRvIGNyZWF0ZSBlbmhhbmNlZCBvZmZsaW5lIHNlcnZpY2UgY29udGFpbmVyOicsIGVycm9yKTtcclxuXHJcbiAgICAvLyBSZS10aHJvdyB3aXRoIG1vcmUgY29udGV4dFxyXG4gICAgaWYgKGVycm9yIGluc3RhbmNlb2YgRXJyb3IpIHtcclxuICAgICAgdGhyb3cgbmV3IEVycm9yKGBTZXJ2aWNlIGNvbnRhaW5lciBjcmVhdGlvbiBmYWlsZWQ6ICR7ZXJyb3IubWVzc2FnZX1gKTtcclxuICAgIH1cclxuXHJcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ1Vua25vd24gZXJyb3Igb2NjdXJyZWQgd2hpbGUgY3JlYXRpbmcgc2VydmljZSBjb250YWluZXInKTtcclxuICB9XHJcbn1cclxuIl0sIm1hcHBpbmdzIjoiOztBQUFBOzs7Ozs7OztBQUFBO0FBQUEsU0FBQUEsY0FBQTtFQUFBLElBQUFDLElBQUE7RUFBQSxJQUFBQyxJQUFBO0VBQUEsSUFBQUMsTUFBQSxPQUFBQyxRQUFBO0VBQUEsSUFBQUMsR0FBQTtFQUFBLElBQUFDLFlBQUE7SUFBQUwsSUFBQTtJQUFBTSxZQUFBO01BQUE7UUFBQUMsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7SUFBQTtJQUFBRSxLQUFBO01BQUE7UUFBQUMsSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO0lBQUE7SUFBQU8sU0FBQTtNQUFBO1FBQUFELEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtJQUFBO0lBQUFXLENBQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtJQUFBO0lBQUFDLENBQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtJQUFBO0lBQUFDLENBQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO0lBQUE7SUFBQUMsY0FBQTtNQUFBQyxJQUFBO01BQUFDLFFBQUE7TUFBQUMsS0FBQTtNQUFBQyxPQUFBO01BQUFDLGNBQUE7TUFBQUMsT0FBQTtJQUFBO0lBQUFDLGVBQUE7SUFBQTVCLElBQUE7RUFBQTtFQUFBLElBQUE2QixRQUFBLEdBQUE1QixNQUFBLENBQUFFLEdBQUEsTUFBQUYsTUFBQSxDQUFBRSxHQUFBO0VBQUEsS0FBQTBCLFFBQUEsQ0FBQTlCLElBQUEsS0FBQThCLFFBQUEsQ0FBQTlCLElBQUEsRUFBQUMsSUFBQSxLQUFBQSxJQUFBO0lBQUE2QixRQUFBLENBQUE5QixJQUFBLElBQUFLLFlBQUE7RUFBQTtFQUFBLElBQUEwQixjQUFBLEdBQUFELFFBQUEsQ0FBQTlCLElBQUE7RUFBQTtJQTJDQTtJQUFBRCxhQUFBLFlBQUFBLENBQUE7TUFBQSxPQUFBZ0MsY0FBQTtJQUFBO0VBQUE7RUFBQSxPQUFBQSxjQUFBO0FBQUE7QUFBQWhDLGFBQUE7QUFBQUEsYUFBQSxHQUFBb0IsQ0FBQTs7Ozs7Ozs7O0FBMGlCQWEsT0FBQSxDQUFBQyxxQ0FBQSxHQUFBQSxxQ0FBQTtBQTVrQkEsTUFBQUMsYUFBQTtBQUFBO0FBQUEsQ0FBQW5DLGFBQUEsR0FBQW9CLENBQUEsT0FBQWdCLE9BQUE7QUFDQSxNQUFBQyxxQkFBQTtBQUFBO0FBQUEsQ0FBQXJDLGFBQUEsR0FBQW9CLENBQUEsT0FBQWdCLE9BQUE7QUFDQSxNQUFBRSx1QkFBQTtBQUFBO0FBQUEsQ0FBQXRDLGFBQUEsR0FBQW9CLENBQUEsT0FBQWdCLE9BQUE7QUFnQ0E7OztBQUdBLE1BQWFHLHNCQUFzQjtFQUNqQ0MsWUFBb0JDLFdBQXdCO0lBQUE7SUFBQXpDLGFBQUEsR0FBQXFCLENBQUE7SUFBQXJCLGFBQUEsR0FBQW9CLENBQUE7SUFBeEIsS0FBQXFCLFdBQVcsR0FBWEEsV0FBVztFQUFnQjtFQUUvQyxNQUFNQyxVQUFVQSxDQUFDQyxFQUFVO0lBQUE7SUFBQTNDLGFBQUEsR0FBQXFCLENBQUE7SUFBQXJCLGFBQUEsR0FBQW9CLENBQUE7SUFDekIsSUFBSTtNQUNGLE1BQU13QixlQUFlO01BQUE7TUFBQSxDQUFBNUMsYUFBQSxHQUFBb0IsQ0FBQSxPQUFHLE1BQU0sSUFBSSxDQUFDcUIsV0FBVyxDQUFDQyxVQUFVLENBQUNDLEVBQUUsQ0FBQztNQUFDO01BQUEzQyxhQUFBLEdBQUFvQixDQUFBO01BQzlELE9BQU93QixlQUFlO01BQUE7TUFBQSxDQUFBNUMsYUFBQSxHQUFBc0IsQ0FBQSxVQUFHLElBQUksQ0FBQ3VCLHNCQUFzQixDQUFDRCxlQUFlLENBQUM7TUFBQTtNQUFBLENBQUE1QyxhQUFBLEdBQUFzQixDQUFBLFVBQUcsSUFBSTtJQUM5RSxDQUFDLENBQUMsT0FBT3dCLEtBQUssRUFBRTtNQUFBO01BQUE5QyxhQUFBLEdBQUFvQixDQUFBO01BQ2QyQixPQUFPLENBQUNELEtBQUssQ0FBQyx3QkFBd0IsRUFBRUEsS0FBSyxDQUFDO01BQUM7TUFBQTlDLGFBQUEsR0FBQW9CLENBQUE7TUFDL0MsTUFBTTBCLEtBQUs7SUFDYjtFQUNGO0VBRUEsTUFBTUUsV0FBV0EsQ0FBQ0MsT0FBZ0I7SUFBQTtJQUFBakQsYUFBQSxHQUFBcUIsQ0FBQTtJQUFBckIsYUFBQSxHQUFBb0IsQ0FBQTtJQUNoQyxJQUFJO01BQ0YsTUFBTXdCLGVBQWU7TUFBQTtNQUFBLENBQUE1QyxhQUFBLEdBQUFvQixDQUFBLFFBQUcsSUFBSSxDQUFDOEIsd0JBQXdCLENBQUNELE9BQU8sQ0FBQztNQUFDO01BQUFqRCxhQUFBLEdBQUFvQixDQUFBO01BQy9ELE1BQU0sSUFBSSxDQUFDcUIsV0FBVyxDQUFDTyxXQUFXLENBQUNKLGVBQWUsQ0FBQztJQUNyRCxDQUFDLENBQUMsT0FBT0UsS0FBSyxFQUFFO01BQUE7TUFBQTlDLGFBQUEsR0FBQW9CLENBQUE7TUFDZDJCLE9BQU8sQ0FBQ0QsS0FBSyxDQUFDLHlCQUF5QixFQUFFQSxLQUFLLENBQUM7TUFBQztNQUFBOUMsYUFBQSxHQUFBb0IsQ0FBQTtNQUNoRCxNQUFNMEIsS0FBSztJQUNiO0VBQ0Y7RUFFQSxNQUFNSyxhQUFhQSxDQUFDQyxJQUFzQjtJQUFBO0lBQUFwRCxhQUFBLEdBQUFxQixDQUFBO0lBQUFyQixhQUFBLEdBQUFvQixDQUFBO0lBQ3hDLElBQUk7TUFDRjtNQUNBLE1BQU1pQyxXQUFXO01BQUE7TUFBQSxDQUFBckQsYUFBQSxHQUFBb0IsQ0FBQSxRQUFHZ0MsSUFBVztNQUUvQixNQUFNRSxVQUFVO01BQUE7TUFBQSxDQUFBdEQsYUFBQSxHQUFBb0IsQ0FBQSxRQUFZO1FBQzFCdUIsRUFBRTtRQUFFO1FBQUEsQ0FBQTNDLGFBQUEsR0FBQXNCLENBQUEsVUFBQStCLFdBQVcsQ0FBQ1YsRUFBRTtRQUFBO1FBQUEsQ0FBQTNDLGFBQUEsR0FBQXNCLENBQUEsVUFBSSxJQUFJLENBQUNpQyxZQUFZLEVBQUU7UUFDekNDLE1BQU07UUFBRTtRQUFBLENBQUF4RCxhQUFBLEdBQUFzQixDQUFBLFVBQUErQixXQUFXLENBQUNHLE1BQU07UUFBQTtRQUFBLENBQUF4RCxhQUFBLEdBQUFzQixDQUFBLFVBQUksa0JBQWtCO1FBQ2hEbUMsWUFBWTtRQUFFO1FBQUEsQ0FBQXpELGFBQUEsR0FBQXNCLENBQUEsVUFBQStCLFdBQVcsQ0FBQ0ksWUFBWTtRQUFBO1FBQUEsQ0FBQXpELGFBQUEsR0FBQXNCLENBQUEsVUFBSSxhQUFhO1FBQ3ZEb0MsY0FBYztRQUFFO1FBQUEsQ0FBQTFELGFBQUEsR0FBQXNCLENBQUEsVUFBQStCLFdBQVcsQ0FBQ0ssY0FBYztRQUFBO1FBQUEsQ0FBQTFELGFBQUEsR0FBQXNCLENBQUEsVUFBSSxJQUFJLENBQUNxQyxxQkFBcUIsRUFBRTtRQUMxRUMsbUJBQW1CO1FBQUU7UUFBQSxDQUFBNUQsYUFBQSxHQUFBc0IsQ0FBQSxVQUFBK0IsV0FBVyxDQUFDTyxtQkFBbUI7UUFBQTtRQUFBLENBQUE1RCxhQUFBLEdBQUFzQixDQUFBLFVBQUksRUFBRTtRQUMxRHVDLGdCQUFnQjtRQUFFO1FBQUEsQ0FBQTdELGFBQUEsR0FBQXNCLENBQUEsVUFBQStCLFdBQVcsQ0FBQ1EsZ0JBQWdCO1FBQUE7UUFBQSxDQUFBN0QsYUFBQSxHQUFBc0IsQ0FBQSxVQUFJLEVBQUU7UUFDcER3QyxXQUFXO1FBQUU7UUFBQSxDQUFBOUQsYUFBQSxHQUFBc0IsQ0FBQSxVQUFBK0IsV0FBVyxDQUFDUyxXQUFXO1FBQUE7UUFBQSxDQUFBOUQsYUFBQSxHQUFBc0IsQ0FBQSxVQUFJLEVBQUU7UUFDMUN5QyxjQUFjO1FBQUU7UUFBQSxDQUFBL0QsYUFBQSxHQUFBc0IsQ0FBQSxVQUFBK0IsV0FBVyxDQUFDVSxjQUFjO1FBQUE7UUFBQSxDQUFBL0QsYUFBQSxHQUFBc0IsQ0FBQSxVQUFJLEVBQUU7UUFDaEQwQyxZQUFZO1FBQUU7UUFBQSxDQUFBaEUsYUFBQSxHQUFBc0IsQ0FBQSxVQUFBK0IsV0FBVyxDQUFDVyxZQUFZO1FBQUE7UUFBQSxDQUFBaEUsYUFBQSxHQUFBc0IsQ0FBQSxVQUFJLElBQUkyQyxJQUFJLEVBQUUsQ0FBQ0MsV0FBVyxFQUFFO1FBQ2xFQyxhQUFhLEVBQUUsSUFBSUYsSUFBSSxFQUFFLENBQUNDLFdBQVcsRUFBRTtRQUN2Q3JDLE9BQU87UUFBRTtRQUFBLENBQUE3QixhQUFBLEdBQUFzQixDQUFBLFdBQUErQixXQUFXLENBQUN4QixPQUFPO1FBQUE7UUFBQSxDQUFBN0IsYUFBQSxHQUFBc0IsQ0FBQSxXQUFJLEtBQUs7UUFDckM4QyxLQUFLO1FBQUU7UUFBQSxDQUFBcEUsYUFBQSxHQUFBc0IsQ0FBQSxXQUFBK0IsV0FBVyxDQUFDZSxLQUFLO1FBQUE7UUFBQSxDQUFBcEUsYUFBQSxHQUFBc0IsQ0FBQSxXQUFJLEVBQUU7UUFDOUIrQyxRQUFRO1FBQUU7UUFBQSxDQUFBckUsYUFBQSxHQUFBc0IsQ0FBQSxXQUFBK0IsV0FBVyxDQUFDZ0IsUUFBUTtRQUFBO1FBQUEsQ0FBQXJFLGFBQUEsR0FBQXNCLENBQUEsV0FBSSxFQUFFO1FBQ3BDZ0QsU0FBUztRQUFFO1FBQUEsQ0FBQXRFLGFBQUEsR0FBQXNCLENBQUEsV0FBQStCLFdBQVcsQ0FBQ2lCLFNBQVM7UUFBQTtRQUFBLENBQUF0RSxhQUFBLEdBQUFzQixDQUFBLFdBQUksRUFBRTtRQUN0Q2lELHdCQUF3QjtRQUFFO1FBQUEsQ0FBQXZFLGFBQUEsR0FBQXNCLENBQUEsV0FBQStCLFdBQVcsQ0FBQ2tCLHdCQUF3QjtRQUFBO1FBQUEsQ0FBQXZFLGFBQUEsR0FBQXNCLENBQUEsV0FBSTtVQUNoRWtELEtBQUssRUFBRSxVQUFVO1VBQ2pCQyxpQkFBaUIsRUFBRTtZQUFFQyxLQUFLLEVBQUUsRUFBRTtZQUFFQyxNQUFNLEVBQUU7VUFBQyxDQUFFO1VBQzNDQyxnQkFBZ0IsRUFBRSxrQkFBa0I7VUFDcENDLGtCQUFrQixFQUFFLE1BQU07VUFDMUJDLGVBQWUsRUFBRSxVQUFVO1VBQzNCQyxnQkFBZ0IsRUFBRSxNQUFNO1VBQ3hCQyxnQkFBZ0IsRUFBRSxJQUFJO1VBQ3RCQyxjQUFjLEVBQUUsR0FBRztVQUNuQkMsUUFBUSxFQUFFLENBQUM7VUFDWEMsYUFBYSxFQUFFO1NBQ2hCO1FBQ0RDLGNBQWM7UUFBRTtRQUFBLENBQUFwRixhQUFBLEdBQUFzQixDQUFBLFdBQUErQixXQUFXLENBQUMrQixjQUFjO1FBQUE7UUFBQSxDQUFBcEYsYUFBQSxHQUFBc0IsQ0FBQSxXQUFJO1VBQzVDK0QsTUFBTSxFQUFFLElBQUk7VUFDWkMsTUFBTSxFQUFFLElBQUk7VUFDWkMsRUFBRSxFQUFFLEtBQUs7VUFDVEMsR0FBRyxFQUFFLEtBQUs7VUFDVkMsSUFBSSxFQUFFO1NBQ1A7T0FDRjtNQUFDO01BQUF6RixhQUFBLEdBQUFvQixDQUFBO01BRUYsTUFBTSxJQUFJLENBQUM0QixXQUFXLENBQUNNLFVBQVUsQ0FBQztNQUFDO01BQUF0RCxhQUFBLEdBQUFvQixDQUFBO01BQ25DLE9BQU9rQyxVQUFVO0lBQ25CLENBQUMsQ0FBQyxPQUFPUixLQUFLLEVBQUU7TUFBQTtNQUFBOUMsYUFBQSxHQUFBb0IsQ0FBQTtNQUNkMkIsT0FBTyxDQUFDRCxLQUFLLENBQUMsMkJBQTJCLEVBQUVBLEtBQUssQ0FBQztNQUFDO01BQUE5QyxhQUFBLEdBQUFvQixDQUFBO01BQ2xELE1BQU0wQixLQUFLO0lBQ2I7RUFDRjtFQUVBLE1BQU00QyxhQUFhQSxDQUFDL0MsRUFBVTtJQUFBO0lBQUEzQyxhQUFBLEdBQUFxQixDQUFBO0lBQUFyQixhQUFBLEdBQUFvQixDQUFBO0lBQzVCLElBQUk7TUFBQTtNQUFBcEIsYUFBQSxHQUFBb0IsQ0FBQTtNQUNGLE1BQU0sSUFBSSxDQUFDcUIsV0FBVyxDQUFDaUQsYUFBYSxDQUFDL0MsRUFBRSxDQUFDO0lBQzFDLENBQUMsQ0FBQyxPQUFPRyxLQUFLLEVBQUU7TUFBQTtNQUFBOUMsYUFBQSxHQUFBb0IsQ0FBQTtNQUNkMkIsT0FBTyxDQUFDRCxLQUFLLENBQUMsMkJBQTJCLEVBQUVBLEtBQUssQ0FBQztNQUFDO01BQUE5QyxhQUFBLEdBQUFvQixDQUFBO01BQ2xELE1BQU0wQixLQUFLO0lBQ2I7RUFDRjtFQUVBLE1BQU02QyxZQUFZQSxDQUFDbkMsTUFBYztJQUFBO0lBQUF4RCxhQUFBLEdBQUFxQixDQUFBO0lBQUFyQixhQUFBLEdBQUFvQixDQUFBO0lBQy9CLElBQUk7TUFDRixNQUFNd0UsZ0JBQWdCO01BQUE7TUFBQSxDQUFBNUYsYUFBQSxHQUFBb0IsQ0FBQSxRQUFHLE1BQU0sSUFBSSxDQUFDcUIsV0FBVyxDQUFDb0Qsa0JBQWtCLENBQUNyQyxNQUFNLENBQUM7TUFBQztNQUFBeEQsYUFBQSxHQUFBb0IsQ0FBQTtNQUMzRSxPQUFPd0UsZ0JBQWdCLENBQUNFLEdBQUcsQ0FBQ0MsQ0FBQyxJQUFJO1FBQUE7UUFBQS9GLGFBQUEsR0FBQXFCLENBQUE7UUFBQXJCLGFBQUEsR0FBQW9CLENBQUE7UUFBQSxXQUFJLENBQUN5QixzQkFBc0IsQ0FBQ2tELENBQUMsQ0FBQztNQUFELENBQUMsQ0FBQztJQUNsRSxDQUFDLENBQUMsT0FBT2pELEtBQUssRUFBRTtNQUFBO01BQUE5QyxhQUFBLEdBQUFvQixDQUFBO01BQ2QyQixPQUFPLENBQUNELEtBQUssQ0FBQywwQkFBMEIsRUFBRUEsS0FBSyxDQUFDO01BQUM7TUFBQTlDLGFBQUEsR0FBQW9CLENBQUE7TUFDakQsTUFBTTBCLEtBQUs7SUFDYjtFQUNGO0VBRUEsTUFBTWtELGNBQWNBLENBQUNDLEtBQWEsRUFBRXpDLE1BQWU7SUFBQTtJQUFBeEQsYUFBQSxHQUFBcUIsQ0FBQTtJQUFBckIsYUFBQSxHQUFBb0IsQ0FBQTtJQUNqRCxJQUFJO01BQ0YsTUFBTXdFLGdCQUFnQjtNQUFBO01BQUEsQ0FBQTVGLGFBQUEsR0FBQW9CLENBQUEsUUFBRyxNQUFNLElBQUksQ0FBQ3FCLFdBQVcsQ0FBQ3VELGNBQWMsQ0FBQ0MsS0FBSyxFQUFFekMsTUFBTSxDQUFDO01BQUM7TUFBQXhELGFBQUEsR0FBQW9CLENBQUE7TUFDOUUsT0FBT3dFLGdCQUFnQixDQUFDRSxHQUFHLENBQUNDLENBQUMsSUFBSTtRQUFBO1FBQUEvRixhQUFBLEdBQUFxQixDQUFBO1FBQUFyQixhQUFBLEdBQUFvQixDQUFBO1FBQUEsV0FBSSxDQUFDeUIsc0JBQXNCLENBQUNrRCxDQUFDLENBQUM7TUFBRCxDQUFDLENBQUM7SUFDbEUsQ0FBQyxDQUFDLE9BQU9qRCxLQUFLLEVBQUU7TUFBQTtNQUFBOUMsYUFBQSxHQUFBb0IsQ0FBQTtNQUNkMkIsT0FBTyxDQUFDRCxLQUFLLENBQUMsNEJBQTRCLEVBQUVBLEtBQUssQ0FBQztNQUFDO01BQUE5QyxhQUFBLEdBQUFvQixDQUFBO01BQ25ELE1BQU0wQixLQUFLO0lBQ2I7RUFDRjtFQUVRRCxzQkFBc0JBLENBQUNELGVBQWdDO0lBQUE7SUFBQTVDLGFBQUEsR0FBQXFCLENBQUE7SUFBQXJCLGFBQUEsR0FBQW9CLENBQUE7SUFDN0Q7SUFDQSxPQUFPO01BQ0x1QixFQUFFLEVBQUVDLGVBQWUsQ0FBQ0QsRUFBRTtNQUN0QmEsTUFBTSxFQUFFWixlQUFlLENBQUNZLE1BQU07TUFDOUJDLFlBQVksRUFBRWIsZUFBZSxDQUFDYSxZQUFZO01BQzFDQyxjQUFjLEVBQUVkLGVBQWUsQ0FBQ2MsY0FBYztNQUM5Q0UsbUJBQW1CLEVBQUVoQixlQUFlLENBQUNnQixtQkFBbUI7TUFDeERDLGdCQUFnQixFQUFFakIsZUFBZSxDQUFDaUIsZ0JBQWdCO01BQ2xEQyxXQUFXLEVBQUVsQixlQUFlLENBQUNrQixXQUFXO01BQ3hDQyxjQUFjLEVBQUVuQixlQUFlLENBQUNtQixjQUFjO01BQzlDQyxZQUFZLEVBQUVwQixlQUFlLENBQUNvQixZQUFZO01BQzFDRyxhQUFhLEVBQUV2QixlQUFlLENBQUN1QixhQUFhO01BQzVDdEMsT0FBTyxFQUFFZSxlQUFlLENBQUNmLE9BQU87TUFDaEN1QyxLQUFLLEVBQUV4QixlQUFlLENBQUN3QixLQUFLO01BQzVCQyxRQUFRLEVBQUV6QixlQUFlLENBQUN5QixRQUFRO01BQ2xDQyxTQUFTLEVBQUUxQixlQUFlLENBQUMwQixTQUFTO01BQ3BDQyx3QkFBd0IsRUFBRTNCLGVBQWUsQ0FBQzJCLHdCQUF3QjtNQUNsRWEsY0FBYyxFQUFFeEMsZUFBZSxDQUFDd0M7S0FDakM7RUFDSDtFQUVRbEMsd0JBQXdCQSxDQUFDRCxPQUFnQjtJQUFBO0lBQUFqRCxhQUFBLEdBQUFxQixDQUFBO0lBQUFyQixhQUFBLEdBQUFvQixDQUFBO0lBQy9DO0lBQ0EsT0FBTztNQUNMLEdBQUc2QixPQUFPO01BQ1ZpRCxJQUFJLEVBQUU7UUFDSnJFLE9BQU8sRUFBRSxDQUFDO1FBQ1ZzRSxZQUFZLEVBQUUsSUFBSWxDLElBQUksRUFBRTtRQUN4Qm1DLGNBQWMsRUFBRW5ELE9BQU8sQ0FBQ08sTUFBTTtRQUM5QjZDLFVBQVUsRUFBRSxPQUFPO1FBQ25CQyxTQUFTLEVBQUUsRUFBRTtRQUNiQyxTQUFTLEVBQUU7T0FDWjtNQUNEQyxhQUFhLEVBQUU7UUFDYkMsT0FBTyxFQUFFO1VBQ1BDLFFBQVEsRUFBRSxLQUFLO1VBQ2ZDLFVBQVUsRUFBRSxFQUFFO1VBQ2RDLFdBQVcsRUFBRTtTQUNkO1FBQ0RDLFFBQVEsRUFBRSxFQUFFO1FBQ1pDLElBQUksRUFBRTtVQUNKQyxRQUFRLEVBQUU7O09BRWI7TUFDREMsT0FBTyxFQUFFO1FBQ1BDLE1BQU0sRUFBRTtVQUNOQyxVQUFVLEVBQUU7U0FDYjtRQUNEakYsT0FBTyxFQUFFOztLQUVaO0VBQ0g7RUFFUXNCLFlBQVlBLENBQUE7SUFBQTtJQUFBdkQsYUFBQSxHQUFBcUIsQ0FBQTtJQUFBckIsYUFBQSxHQUFBb0IsQ0FBQTtJQUNsQixPQUFPLHNDQUFzQyxDQUFDK0YsT0FBTyxDQUFDLE9BQU8sRUFBRSxVQUFTQyxDQUFDO01BQUE7TUFBQXBILGFBQUEsR0FBQXFCLENBQUE7TUFDdkUsTUFBTWdHLENBQUM7TUFBQTtNQUFBLENBQUFySCxhQUFBLEdBQUFvQixDQUFBLFFBQUdrRyxJQUFJLENBQUNDLE1BQU0sRUFBRSxHQUFHLEVBQUUsR0FBRyxDQUFDO01BQ2hDLE1BQU1DLENBQUM7TUFBQTtNQUFBLENBQUF4SCxhQUFBLEdBQUFvQixDQUFBLFFBQUdnRyxDQUFDLElBQUksR0FBRztNQUFBO01BQUEsQ0FBQXBILGFBQUEsR0FBQXNCLENBQUEsV0FBRytGLENBQUM7TUFBQTtNQUFBLENBQUFySCxhQUFBLEdBQUFzQixDQUFBLFdBQUkrRixDQUFDLEdBQUcsR0FBRyxHQUFHLEdBQUcsQ0FBQztNQUFDO01BQUFySCxhQUFBLEdBQUFvQixDQUFBO01BQ3pDLE9BQU9vRyxDQUFDLENBQUNDLFFBQVEsQ0FBQyxFQUFFLENBQUM7SUFDdkIsQ0FBQyxDQUFDO0VBQ0o7RUFFUTlELHFCQUFxQkEsQ0FBQTtJQUFBO0lBQUEzRCxhQUFBLEdBQUFxQixDQUFBO0lBQzNCLE1BQU1xRyxTQUFTO0lBQUE7SUFBQSxDQUFBMUgsYUFBQSxHQUFBb0IsQ0FBQSxRQUFHNkMsSUFBSSxDQUFDMEQsR0FBRyxFQUFFLENBQUNGLFFBQVEsRUFBRSxDQUFDRyxLQUFLLENBQUMsQ0FBQyxDQUFDLENBQUM7SUFBQztJQUFBNUgsYUFBQSxHQUFBb0IsQ0FBQTtJQUNsRCxPQUFPLE9BQU9zRyxTQUFTLEVBQUU7RUFDM0I7O0FBQ0Q7QUFBQTFILGFBQUEsR0FBQW9CLENBQUE7QUF2S0RhLE9BQUEsQ0FBQU0sc0JBQUEsR0FBQUEsc0JBQUE7QUF5S0E7OztBQUdBLE1BQWFzRixtQkFBbUI7RUFDOUJyRixZQUFvQkMsV0FBd0I7SUFBQTtJQUFBekMsYUFBQSxHQUFBcUIsQ0FBQTtJQUFBckIsYUFBQSxHQUFBb0IsQ0FBQTtJQUF4QixLQUFBcUIsV0FBVyxHQUFYQSxXQUFXO0VBQWdCO0VBRS9DLE1BQU1xRixjQUFjQSxDQUFBO0lBQUE7SUFBQTlILGFBQUEsR0FBQXFCLENBQUE7SUFBQXJCLGFBQUEsR0FBQW9CLENBQUE7SUFDbEIsSUFBSTtNQUNGLE1BQU0yRyxZQUFZO01BQUE7TUFBQSxDQUFBL0gsYUFBQSxHQUFBb0IsQ0FBQSxRQUFHLE1BQU0sSUFBSSxDQUFDcUIsV0FBVyxDQUFDcUYsY0FBYyxFQUFFO01BQUM7TUFBQTlILGFBQUEsR0FBQW9CLENBQUE7TUFDN0QsT0FBTzJHLFlBQVk7TUFBQTtNQUFBLENBQUEvSCxhQUFBLEdBQUFzQixDQUFBLFdBQUcsSUFBSSxDQUFDMEcsbUJBQW1CLENBQUNELFlBQVksQ0FBQztNQUFBO01BQUEsQ0FBQS9ILGFBQUEsR0FBQXNCLENBQUEsV0FBRyxJQUFJO0lBQ3JFLENBQUMsQ0FBQyxPQUFPd0IsS0FBSyxFQUFFO01BQUE7TUFBQTlDLGFBQUEsR0FBQW9CLENBQUE7TUFDZDJCLE9BQU8sQ0FBQ0QsS0FBSyxDQUFDLDZCQUE2QixFQUFFQSxLQUFLLENBQUM7TUFBQztNQUFBOUMsYUFBQSxHQUFBb0IsQ0FBQTtNQUNwRCxNQUFNMEIsS0FBSztJQUNiO0VBQ0Y7RUFFQSxNQUFNbUYsV0FBV0EsQ0FBQ3RGLEVBQVU7SUFBQTtJQUFBM0MsYUFBQSxHQUFBcUIsQ0FBQTtJQUFBckIsYUFBQSxHQUFBb0IsQ0FBQTtJQUMxQixJQUFJO01BQ0YsTUFBTTJHLFlBQVk7TUFBQTtNQUFBLENBQUEvSCxhQUFBLEdBQUFvQixDQUFBLFFBQUcsTUFBTSxJQUFJLENBQUNxQixXQUFXLENBQUN5RixPQUFPLENBQUN2RixFQUFFLENBQUM7TUFBQztNQUFBM0MsYUFBQSxHQUFBb0IsQ0FBQTtNQUN4RCxPQUFPMkcsWUFBWTtNQUFBO01BQUEsQ0FBQS9ILGFBQUEsR0FBQXNCLENBQUEsV0FBRyxJQUFJLENBQUMwRyxtQkFBbUIsQ0FBQ0QsWUFBWSxDQUFDO01BQUE7TUFBQSxDQUFBL0gsYUFBQSxHQUFBc0IsQ0FBQSxXQUFHLElBQUk7SUFDckUsQ0FBQyxDQUFDLE9BQU93QixLQUFLLEVBQUU7TUFBQTtNQUFBOUMsYUFBQSxHQUFBb0IsQ0FBQTtNQUNkMkIsT0FBTyxDQUFDRCxLQUFLLENBQUMscUJBQXFCLEVBQUVBLEtBQUssQ0FBQztNQUFDO01BQUE5QyxhQUFBLEdBQUFvQixDQUFBO01BQzVDLE1BQU0wQixLQUFLO0lBQ2I7RUFDRjtFQUVBLE1BQU1xRixVQUFVQSxDQUFDQyxJQUFVO0lBQUE7SUFBQXBJLGFBQUEsR0FBQXFCLENBQUE7SUFBQXJCLGFBQUEsR0FBQW9CLENBQUE7SUFDekIsSUFBSTtNQUNGLE1BQU0yRyxZQUFZO01BQUE7TUFBQSxDQUFBL0gsYUFBQSxHQUFBb0IsQ0FBQSxRQUFHLElBQUksQ0FBQ2lILHFCQUFxQixDQUFDRCxJQUFJLENBQUM7TUFBQztNQUFBcEksYUFBQSxHQUFBb0IsQ0FBQTtNQUN0RCxNQUFNLElBQUksQ0FBQ3FCLFdBQVcsQ0FBQzZGLFFBQVEsQ0FBQ1AsWUFBWSxDQUFDO0lBQy9DLENBQUMsQ0FBQyxPQUFPakYsS0FBSyxFQUFFO01BQUE7TUFBQTlDLGFBQUEsR0FBQW9CLENBQUE7TUFDZDJCLE9BQU8sQ0FBQ0QsS0FBSyxDQUFDLHdCQUF3QixFQUFFQSxLQUFLLENBQUM7TUFBQztNQUFBOUMsYUFBQSxHQUFBb0IsQ0FBQTtNQUMvQyxNQUFNMEIsS0FBSztJQUNiO0VBQ0Y7RUFFUWtGLG1CQUFtQkEsQ0FBQ0QsWUFBMEI7SUFBQTtJQUFBL0gsYUFBQSxHQUFBcUIsQ0FBQTtJQUFBckIsYUFBQSxHQUFBb0IsQ0FBQTtJQUNwRCxPQUFPO01BQ0x1QixFQUFFLEVBQUVvRixZQUFZLENBQUNwRixFQUFFO01BQ25CNEYsS0FBSyxFQUFFUixZQUFZLENBQUNRLEtBQUs7TUFDekIxSCxJQUFJLEVBQUVrSCxZQUFZLENBQUNsSCxJQUFJO01BQ3ZCMkgsSUFBSSxFQUFFVCxZQUFZLENBQUNTLElBQUk7TUFDdkJDLE9BQU8sRUFBRVYsWUFBWSxDQUFDVSxPQUFPO01BQzdCQyxVQUFVLEVBQUVYLFlBQVksQ0FBQ1csVUFBVTtNQUNuQ0MsU0FBUyxFQUFFWixZQUFZLENBQUNZLFNBQVM7TUFDakNDLFNBQVMsRUFBRWIsWUFBWSxDQUFDYTtLQUN6QjtFQUNIO0VBRVFQLHFCQUFxQkEsQ0FBQ0QsSUFBVTtJQUFBO0lBQUFwSSxhQUFBLEdBQUFxQixDQUFBO0lBQ3RDO0lBQ0EsTUFBTXdILFFBQVE7SUFBQTtJQUFBLENBQUE3SSxhQUFBLEdBQUFvQixDQUFBLFFBQUdnSCxJQUFJLENBQUNJLElBQUksS0FBSyxhQUFhO0lBQUE7SUFBQSxDQUFBeEksYUFBQSxHQUFBc0IsQ0FBQSxXQUFHLFlBQVk7SUFBQTtJQUFBLENBQUF0QixhQUFBLEdBQUFzQixDQUFBLFdBQUc4RyxJQUFJLENBQUNJLElBQXFDO0lBQUM7SUFBQXhJLGFBQUEsR0FBQW9CLENBQUE7SUFFekcsT0FBTztNQUNMLEdBQUdnSCxJQUFJO01BQ1B2SCxJQUFJO01BQUU7TUFBQSxDQUFBYixhQUFBLEdBQUFzQixDQUFBLFdBQUE4RyxJQUFJLENBQUN2SCxJQUFJO01BQUE7TUFBQSxDQUFBYixhQUFBLEdBQUFzQixDQUFBLFdBQUksY0FBYztNQUFFO01BQ25Da0gsSUFBSSxFQUFFSyxRQUFRO01BQUU7TUFDaEJDLFFBQVEsRUFBRSxFQUFFO01BQUU7TUFDZDVDLElBQUksRUFBRTtRQUNKckUsT0FBTyxFQUFFLENBQUM7UUFDVnNFLFlBQVksRUFBRSxJQUFJbEMsSUFBSSxFQUFFO1FBQ3hCbUMsY0FBYyxFQUFFZ0MsSUFBSSxDQUFDekYsRUFBRTtRQUN2QjBELFVBQVUsRUFBRSxPQUFPO1FBQ25CQyxTQUFTLEVBQUUsRUFBRTtRQUNiQyxTQUFTLEVBQUU7T0FDWjtNQUNEUyxPQUFPLEVBQUU7UUFDUCtCLFdBQVcsRUFBRTtVQUNYQyxRQUFRLEVBQUUsSUFBSTtVQUNkQyxlQUFlLEVBQUUsUUFBUTtVQUN6QkMsWUFBWSxFQUFFO1NBQ2Y7UUFDREMsaUJBQWlCLEVBQUU7VUFDakJDLE9BQU8sRUFBRSxJQUFJO1VBQ2JDLFFBQVEsRUFBRSxDQUFDLGdCQUFnQixFQUFFLG9CQUFvQixFQUFFLGNBQWM7OztLQUd0RTtFQUNIOztBQUNEO0FBQUFySixhQUFBLEdBQUFvQixDQUFBO0FBNUVEYSxPQUFBLENBQUE0RixtQkFBQSxHQUFBQSxtQkFBQTtBQThFQTs7OztBQUlBLE1BQWF5QiwwQkFBMEI7RUFDckM7RUFFQTlHLFlBQUE7SUFBQTtJQUFBeEMsYUFBQSxHQUFBcUIsQ0FBQTtFQUVBLENBQUMsQ0FEQztFQUdGLE1BQU1rSSxtQkFBbUJBLENBQUNDLE1BQXdCO0lBQUE7SUFBQXhKLGFBQUEsR0FBQXFCLENBQUE7SUFBQXJCLGFBQUEsR0FBQW9CLENBQUE7SUFDaEQsSUFBSTtNQUFBO01BQUFwQixhQUFBLEdBQUFvQixDQUFBO01BQ0Y7TUFDQSxPQUFPO1FBQ0xxSSxPQUFPLEVBQUUsS0FBSztRQUNkQyxVQUFVLEVBQUVGLE1BQU07UUFDbEJHLFFBQVEsRUFBRSxDQUFDLHlDQUF5QyxDQUFDO1FBQ3JEQyxNQUFNLEVBQUUsQ0FBQywwQ0FBMEM7T0FDcEQ7SUFDSCxDQUFDLENBQUMsT0FBTzlHLEtBQUssRUFBRTtNQUFBO01BQUE5QyxhQUFBLEdBQUFvQixDQUFBO01BQ2QyQixPQUFPLENBQUNELEtBQUssQ0FBQyxrQ0FBa0MsRUFBRUEsS0FBSyxDQUFDO01BQUM7TUFBQTlDLGFBQUEsR0FBQW9CLENBQUE7TUFDekQsTUFBTTBCLEtBQUs7SUFDYjtFQUNGO0VBRUEsTUFBTStHLGVBQWVBLENBQUNDLE9BQTBCO0lBQUE7SUFBQTlKLGFBQUEsR0FBQXFCLENBQUE7SUFBQXJCLGFBQUEsR0FBQW9CLENBQUE7SUFDOUMsSUFBSTtNQUFBO01BQUFwQixhQUFBLEdBQUFvQixDQUFBO01BQ0YsT0FBTztRQUNMMkksS0FBSyxFQUFFRCxPQUFPLENBQUNMLE9BQU87UUFDdEJFLFFBQVE7UUFBRTtRQUFBLENBQUEzSixhQUFBLEdBQUFzQixDQUFBLFdBQUF3SSxPQUFPLENBQUNILFFBQVE7UUFBQTtRQUFBLENBQUEzSixhQUFBLEdBQUFzQixDQUFBLFdBQUksRUFBRTtRQUNoQ3NJLE1BQU0sRUFBRUUsT0FBTyxDQUFDTCxPQUFPO1FBQUE7UUFBQSxDQUFBekosYUFBQSxHQUFBc0IsQ0FBQSxXQUFHLEVBQUU7UUFBQTtRQUFBLENBQUF0QixhQUFBLEdBQUFzQixDQUFBLFdBQUcsQ0FBQyxvQkFBb0IsQ0FBQztPQUN0RDtJQUNILENBQUMsQ0FBQyxPQUFPd0IsS0FBSyxFQUFFO01BQUE7TUFBQTlDLGFBQUEsR0FBQW9CLENBQUE7TUFDZDJCLE9BQU8sQ0FBQ0QsS0FBSyxDQUFDLDZCQUE2QixFQUFFQSxLQUFLLENBQUM7TUFBQztNQUFBOUMsYUFBQSxHQUFBb0IsQ0FBQTtNQUNwRCxNQUFNMEIsS0FBSztJQUNiO0VBQ0Y7RUFFQSxNQUFNa0gscUJBQXFCQSxDQUFDQyxTQUFpQjtJQUFBO0lBQUFqSyxhQUFBLEdBQUFxQixDQUFBO0lBQUFyQixhQUFBLEdBQUFvQixDQUFBO0lBQzNDLElBQUk7TUFBQTtNQUFBcEIsYUFBQSxHQUFBb0IsQ0FBQTtNQUNGO01BQ0EsT0FBTyxFQUFFO0lBQ1gsQ0FBQyxDQUFDLE9BQU8wQixLQUFLLEVBQUU7TUFBQTtNQUFBOUMsYUFBQSxHQUFBb0IsQ0FBQTtNQUNkMkIsT0FBTyxDQUFDRCxLQUFLLENBQUMsb0NBQW9DLEVBQUVBLEtBQUssQ0FBQztNQUFDO01BQUE5QyxhQUFBLEdBQUFvQixDQUFBO01BQzNELE1BQU0wQixLQUFLO0lBQ2I7RUFDRjs7QUFDRDtBQUFBOUMsYUFBQSxHQUFBb0IsQ0FBQTtBQTVDRGEsT0FBQSxDQUFBcUgsMEJBQUEsR0FBQUEsMEJBQUE7QUE4Q0E7Ozs7QUFJQSxNQUFhWSx5QkFBeUI7RUFDcEM7RUFFQTFILFlBQUE7SUFBQTtJQUFBeEMsYUFBQSxHQUFBcUIsQ0FBQTtFQUVBLENBQUMsQ0FEQztFQUdGLE1BQU04SSxlQUFlQSxDQUFDbEgsT0FBZ0I7SUFBQTtJQUFBakQsYUFBQSxHQUFBcUIsQ0FBQTtJQUFBckIsYUFBQSxHQUFBb0IsQ0FBQTtJQUNwQyxJQUFJO01BQ0YsTUFBTXdJLE1BQU07TUFBQTtNQUFBLENBQUE1SixhQUFBLEdBQUFvQixDQUFBLFFBQUcsRUFBRTtNQUNqQixNQUFNdUksUUFBUTtNQUFBO01BQUEsQ0FBQTNKLGFBQUEsR0FBQW9CLENBQUEsUUFBRyxFQUFFO01BQUM7TUFBQXBCLGFBQUEsR0FBQW9CLENBQUE7TUFFcEIsSUFBSSxDQUFDNkIsT0FBTyxDQUFDUSxZQUFZLEVBQUUyRyxJQUFJLEVBQUUsRUFBRTtRQUFBO1FBQUFwSyxhQUFBLEdBQUFzQixDQUFBO1FBQUF0QixhQUFBLEdBQUFvQixDQUFBO1FBQ2pDd0ksTUFBTSxDQUFDUyxJQUFJLENBQUMsMEJBQTBCLENBQUM7TUFDekMsQ0FBQztNQUFBO01BQUE7UUFBQXJLLGFBQUEsR0FBQXNCLENBQUE7TUFBQTtNQUFBdEIsYUFBQSxHQUFBb0IsQ0FBQTtNQUVELElBQUk2QixPQUFPLENBQUNtQixLQUFLLENBQUNrRyxNQUFNLEtBQUssQ0FBQyxFQUFFO1FBQUE7UUFBQXRLLGFBQUEsR0FBQXNCLENBQUE7UUFBQXRCLGFBQUEsR0FBQW9CLENBQUE7UUFDOUJ1SSxRQUFRLENBQUNVLElBQUksQ0FBQyw4QkFBOEIsQ0FBQztNQUMvQyxDQUFDO01BQUE7TUFBQTtRQUFBckssYUFBQSxHQUFBc0IsQ0FBQTtNQUFBO01BQUF0QixhQUFBLEdBQUFvQixDQUFBO01BRUQsSUFBSTZCLE9BQU8sQ0FBQ29CLFFBQVEsQ0FBQ2lHLE1BQU0sS0FBSyxDQUFDLEVBQUU7UUFBQTtRQUFBdEssYUFBQSxHQUFBc0IsQ0FBQTtRQUFBdEIsYUFBQSxHQUFBb0IsQ0FBQTtRQUNqQ3VJLFFBQVEsQ0FBQ1UsSUFBSSxDQUFDLHNDQUFzQyxDQUFDO01BQ3ZELENBQUM7TUFBQTtNQUFBO1FBQUFySyxhQUFBLEdBQUFzQixDQUFBO01BQUE7TUFBQXRCLGFBQUEsR0FBQW9CLENBQUE7TUFFRCxPQUFPO1FBQ0wySSxLQUFLLEVBQUVILE1BQU0sQ0FBQ1UsTUFBTSxLQUFLLENBQUM7UUFDMUJWLE1BQU07UUFDTkQ7T0FDRDtJQUNILENBQUMsQ0FBQyxPQUFPN0csS0FBSyxFQUFFO01BQUE7TUFBQTlDLGFBQUEsR0FBQW9CLENBQUE7TUFDZDJCLE9BQU8sQ0FBQ0QsS0FBSyxDQUFDLDZCQUE2QixFQUFFQSxLQUFLLENBQUM7TUFBQztNQUFBOUMsYUFBQSxHQUFBb0IsQ0FBQTtNQUNwRCxNQUFNMEIsS0FBSztJQUNiO0VBQ0Y7O0FBQ0Q7QUFBQTlDLGFBQUEsR0FBQW9CLENBQUE7QUFsQ0RhLE9BQUEsQ0FBQWlJLHlCQUFBLEdBQUFBLHlCQUFBO0FBb0NBOzs7QUFHQSxNQUFhSyxxQkFBcUI7RUFHaEMvSCxZQUFvQkMsV0FBd0I7SUFBQTtJQUFBekMsYUFBQSxHQUFBcUIsQ0FBQTtJQUFBckIsYUFBQSxHQUFBb0IsQ0FBQTtJQUF4QixLQUFBcUIsV0FBVyxHQUFYQSxXQUFXO0lBQWE7SUFBQXpDLGFBQUEsR0FBQW9CLENBQUE7SUFGcEMsS0FBQW9KLGFBQWEsR0FBRyxJQUFBbkkscUJBQUEsQ0FBQW9JLG1CQUFtQixHQUFFO0VBRUU7RUFFL0MsTUFBTUMsYUFBYUEsQ0FBQ1QsU0FBaUIsRUFBRVUsT0FBc0I7SUFBQTtJQUFBM0ssYUFBQSxHQUFBcUIsQ0FBQTtJQUFBckIsYUFBQSxHQUFBb0IsQ0FBQTtJQUMzRCxJQUFJO01BQ0YsTUFBTTZCLE9BQU87TUFBQTtNQUFBLENBQUFqRCxhQUFBLEdBQUFvQixDQUFBLFFBQUcsTUFBTSxJQUFJLENBQUNxQixXQUFXLENBQUNDLFVBQVUsQ0FBQ3VILFNBQVMsQ0FBQztNQUFDO01BQUFqSyxhQUFBLEdBQUFvQixDQUFBO01BQzdELElBQUksQ0FBQzZCLE9BQU8sRUFBRTtRQUFBO1FBQUFqRCxhQUFBLEdBQUFzQixDQUFBO1FBQUF0QixhQUFBLEdBQUFvQixDQUFBO1FBQ1osTUFBTSxJQUFJd0osS0FBSyxDQUFDLFdBQVdYLFNBQVMsWUFBWSxDQUFDO01BQ25ELENBQUM7TUFBQTtNQUFBO1FBQUFqSyxhQUFBLEdBQUFzQixDQUFBO01BQUE7TUFFRDtNQUNBLE1BQU11SixpQkFBaUI7TUFBQTtNQUFBLENBQUE3SyxhQUFBLEdBQUFvQixDQUFBLFNBQUd1SixPQUFPLENBQUNHLE1BQU0sS0FBSyxPQUFPO01BQUE7TUFBQSxDQUFBOUssYUFBQSxHQUFBc0IsQ0FBQSxXQUFHLE1BQU07TUFBQTtNQUFBLENBQUF0QixhQUFBLEdBQUFzQixDQUFBLFdBQ25DcUosT0FBTyxDQUFDRyxNQUFNLEtBQUssS0FBSztNQUFBO01BQUEsQ0FBQTlLLGFBQUEsR0FBQXNCLENBQUEsV0FBRyxLQUFLO01BQUE7TUFBQSxDQUFBdEIsYUFBQSxHQUFBc0IsQ0FBQSxXQUNoQ3FKLE9BQU8sQ0FBQ0csTUFBd0I7TUFFMUQsTUFBTUMsTUFBTTtNQUFBO01BQUEsQ0FBQS9LLGFBQUEsR0FBQW9CLENBQUEsU0FBRyxNQUFNLElBQUksQ0FBQ29KLGFBQWEsQ0FBQ1EsWUFBWSxDQUFDLFNBQVMsRUFBRS9ILE9BQU8sRUFBRTRILGlCQUFpQixDQUFDO01BRTNGO01BQUE7TUFBQTdLLGFBQUEsR0FBQW9CLENBQUE7TUFDQSxPQUFPO1FBQ0xxSSxPQUFPLEVBQUVzQixNQUFNLENBQUN0QixPQUFPO1FBQ3ZCd0IsUUFBUSxFQUFFQyxNQUFNLENBQUNDLFVBQVUsRUFBRTtRQUFFO1FBQy9CQyxXQUFXLEVBQUVMLE1BQU0sQ0FBQ0ssV0FBVztRQUMvQnRJLEtBQUssRUFBRWlJLE1BQU0sQ0FBQ2pJO09BQ2Y7SUFDSCxDQUFDLENBQUMsT0FBT0EsS0FBSyxFQUFFO01BQUE7TUFBQTlDLGFBQUEsR0FBQW9CLENBQUE7TUFDZDJCLE9BQU8sQ0FBQ0QsS0FBSyxDQUFDLDJCQUEyQixFQUFFQSxLQUFLLENBQUM7TUFBQztNQUFBOUMsYUFBQSxHQUFBb0IsQ0FBQTtNQUNsRCxNQUFNMEIsS0FBSztJQUNiO0VBQ0Y7RUFFQSxNQUFNdUksZUFBZUEsQ0FBQ0osUUFBZ0I7SUFBQTtJQUFBakwsYUFBQSxHQUFBcUIsQ0FBQTtJQUFBckIsYUFBQSxHQUFBb0IsQ0FBQTtJQUNwQyxJQUFJO01BQUE7TUFBQXBCLGFBQUEsR0FBQW9CLENBQUE7TUFDRixPQUFPO1FBQ0xxSSxPQUFPLEVBQUUsSUFBSTtRQUNid0IsUUFBUSxFQUFFQSxRQUFRO1FBQ2xCRyxXQUFXLEVBQUUsZ0JBQWdCSCxRQUFRO09BQ3RDO0lBQ0gsQ0FBQyxDQUFDLE9BQU9uSSxLQUFLLEVBQUU7TUFBQTtNQUFBOUMsYUFBQSxHQUFBb0IsQ0FBQTtNQUNkMkIsT0FBTyxDQUFDRCxLQUFLLENBQUMsOEJBQThCLEVBQUVBLEtBQUssQ0FBQztNQUFDO01BQUE5QyxhQUFBLEdBQUFvQixDQUFBO01BQ3JELE1BQU0wQixLQUFLO0lBQ2I7RUFDRjtFQUVBLE1BQU13SSxjQUFjQSxDQUFDTCxRQUFnQjtJQUFBO0lBQUFqTCxhQUFBLEdBQUFxQixDQUFBO0lBQUFyQixhQUFBLEdBQUFvQixDQUFBO0lBQ25DLElBQUk7TUFBQTtNQUFBcEIsYUFBQSxHQUFBb0IsQ0FBQTtNQUNGLE9BQU8sSUFBSW1LLElBQUksQ0FBQyxDQUFDLHFCQUFxQixDQUFDLEVBQUU7UUFBRXRLLElBQUksRUFBRTtNQUFpQixDQUFFLENBQUM7SUFDdkUsQ0FBQyxDQUFDLE9BQU82QixLQUFLLEVBQUU7TUFBQTtNQUFBOUMsYUFBQSxHQUFBb0IsQ0FBQTtNQUNkMkIsT0FBTyxDQUFDRCxLQUFLLENBQUMsNEJBQTRCLEVBQUVBLEtBQUssQ0FBQztNQUFDO01BQUE5QyxhQUFBLEdBQUFvQixDQUFBO01BQ25ELE1BQU0wQixLQUFLO0lBQ2I7RUFDRjs7QUFDRDtBQUFBOUMsYUFBQSxHQUFBb0IsQ0FBQTtBQXJERGEsT0FBQSxDQUFBc0kscUJBQUEsR0FBQUEscUJBQUE7QUF1REE7OztBQUdBLE1BQWFpQixtQkFBbUI7RUFDOUJoSixZQUFvQkMsV0FBd0I7SUFBQTtJQUFBekMsYUFBQSxHQUFBcUIsQ0FBQTtJQUFBckIsYUFBQSxHQUFBb0IsQ0FBQTtJQUF4QixLQUFBcUIsV0FBVyxHQUFYQSxXQUFXO0VBQWdCO0VBRS9DLE1BQU1nSixjQUFjQSxDQUFBO0lBQUE7SUFBQXpMLGFBQUEsR0FBQXFCLENBQUE7SUFBQXJCLGFBQUEsR0FBQW9CLENBQUE7SUFDbEIsSUFBSTtNQUNGLE1BQU1nSCxJQUFJO01BQUE7TUFBQSxDQUFBcEksYUFBQSxHQUFBb0IsQ0FBQSxTQUFHLE1BQU0sSUFBSSxDQUFDcUIsV0FBVyxDQUFDcUYsY0FBYyxFQUFFO01BQUM7TUFBQTlILGFBQUEsR0FBQW9CLENBQUE7TUFDckQsT0FBTywyQkFBQXBCLGFBQUEsR0FBQXNCLENBQUEsV0FBQThHLElBQUksRUFBRUksSUFBSTtNQUFBO01BQUEsQ0FBQXhJLGFBQUEsR0FBQXNCLENBQUEsV0FBSSxNQUFNO0lBQzdCLENBQUMsQ0FBQyxPQUFPd0IsS0FBSyxFQUFFO01BQUE7TUFBQTlDLGFBQUEsR0FBQW9CLENBQUE7TUFDZDJCLE9BQU8sQ0FBQ0QsS0FBSyxDQUFDLDZCQUE2QixFQUFFQSxLQUFLLENBQUM7TUFBQztNQUFBOUMsYUFBQSxHQUFBb0IsQ0FBQTtNQUNwRCxPQUFPLE1BQU07SUFDZjtFQUNGO0VBRUEsTUFBTXNLLGdCQUFnQkEsQ0FBQ0MsT0FBZTtJQUFBO0lBQUEzTCxhQUFBLEdBQUFxQixDQUFBO0lBQUFyQixhQUFBLEdBQUFvQixDQUFBO0lBQ3BDLElBQUk7TUFDRixNQUFNZ0gsSUFBSTtNQUFBO01BQUEsQ0FBQXBJLGFBQUEsR0FBQW9CLENBQUEsU0FBRyxNQUFNLElBQUksQ0FBQ3FCLFdBQVcsQ0FBQ3FGLGNBQWMsRUFBRTtNQUNwRCxNQUFNOEQsSUFBSTtNQUFBO01BQUEsQ0FBQTVMLGFBQUEsR0FBQW9CLENBQUEsU0FBRyxNQUFNLElBQUksQ0FBQ3FCLFdBQVcsQ0FBQ29KLGNBQWMsQ0FBQ0YsT0FBTyxFQUFFdkQsSUFBSSxFQUFFekYsRUFBRSxDQUFDO01BQUM7TUFBQTNDLGFBQUEsR0FBQW9CLENBQUE7TUFDdEUsT0FBTywyQkFBQXBCLGFBQUEsR0FBQXNCLENBQUEsV0FBQXNLLElBQUksRUFBRUUsT0FBTztNQUFBO01BQUEsQ0FBQTlMLGFBQUEsR0FBQXNCLENBQUEsV0FBSSxLQUFLO0lBQy9CLENBQUMsQ0FBQyxPQUFPd0IsS0FBSyxFQUFFO01BQUE7TUFBQTlDLGFBQUEsR0FBQW9CLENBQUE7TUFDZDJCLE9BQU8sQ0FBQ0QsS0FBSyxDQUFDLGlDQUFpQyxFQUFFQSxLQUFLLENBQUM7TUFBQztNQUFBOUMsYUFBQSxHQUFBb0IsQ0FBQTtNQUN4RCxPQUFPLEtBQUs7SUFDZDtFQUNGO0VBRUEsTUFBTTJLLGFBQWFBLENBQUE7SUFBQTtJQUFBL0wsYUFBQSxHQUFBcUIsQ0FBQTtJQUFBckIsYUFBQSxHQUFBb0IsQ0FBQTtJQUNqQixJQUFJO01BQ0YsTUFBTW9ILElBQUk7TUFBQTtNQUFBLENBQUF4SSxhQUFBLEdBQUFvQixDQUFBLFNBQUcsTUFBTSxJQUFJLENBQUNxSyxjQUFjLEVBQUU7TUFFeEMsTUFBTU8sTUFBTTtNQUFBO01BQUEsQ0FBQWhNLGFBQUEsR0FBQW9CLENBQUEsU0FBaUM7UUFDM0M2SyxJQUFJLEVBQUU7VUFDSkMsUUFBUSxFQUFFLENBQUM7VUFDWEMsV0FBVyxFQUFFLEVBQUU7VUFDZkMsV0FBVyxFQUFFLENBQUM7VUFDZEMsOEJBQThCLEVBQUUsS0FBSztVQUNyQ0MseUJBQXlCLEVBQUUsS0FBSztVQUNoQ0MsZ0JBQWdCLEVBQUUsS0FBSztVQUN2QkMsYUFBYSxFQUFFO1NBQ2hCO1FBQ0RDLEdBQUcsRUFBRTtVQUNIUCxRQUFRLEVBQUUsRUFBRTtVQUNaQyxXQUFXLEVBQUUsR0FBRztVQUNoQkMsV0FBVyxFQUFFLENBQUMsQ0FBQztVQUNmQyw4QkFBOEIsRUFBRSxJQUFJO1VBQ3BDQyx5QkFBeUIsRUFBRSxJQUFJO1VBQy9CQyxnQkFBZ0IsRUFBRSxJQUFJO1VBQ3RCQyxhQUFhLEVBQUU7U0FDaEI7UUFDREUsVUFBVSxFQUFFO1VBQ1ZSLFFBQVEsRUFBRSxDQUFDLENBQUM7VUFDWkMsV0FBVyxFQUFFLENBQUMsQ0FBQztVQUNmQyxXQUFXLEVBQUUsQ0FBQyxDQUFDO1VBQ2ZDLDhCQUE4QixFQUFFLElBQUk7VUFDcENDLHlCQUF5QixFQUFFLElBQUk7VUFDL0JDLGdCQUFnQixFQUFFLElBQUk7VUFDdEJDLGFBQWEsRUFBRTtTQUNoQjtRQUNERyxXQUFXLEVBQUU7VUFDWFQsUUFBUSxFQUFFLENBQUMsQ0FBQztVQUNaQyxXQUFXLEVBQUUsQ0FBQyxDQUFDO1VBQ2ZDLFdBQVcsRUFBRSxDQUFDLENBQUM7VUFDZkMsOEJBQThCLEVBQUUsSUFBSTtVQUNwQ0MseUJBQXlCLEVBQUUsSUFBSTtVQUMvQkMsZ0JBQWdCLEVBQUUsSUFBSTtVQUN0QkMsYUFBYSxFQUFFOztPQUVsQjtNQUFDO01BQUF4TSxhQUFBLEdBQUFvQixDQUFBO01BRUYsT0FBTzRLLE1BQU0sQ0FBQ3hELElBQUksQ0FBQztJQUNyQixDQUFDLENBQUMsT0FBTzFGLEtBQUssRUFBRTtNQUFBO01BQUE5QyxhQUFBLEdBQUFvQixDQUFBO01BQ2QyQixPQUFPLENBQUNELEtBQUssQ0FBQyw0QkFBNEIsRUFBRUEsS0FBSyxDQUFDO01BQUM7TUFBQTlDLGFBQUEsR0FBQW9CLENBQUE7TUFDbkQsT0FBTztRQUNMOEssUUFBUSxFQUFFLENBQUM7UUFDWEMsV0FBVyxFQUFFLEVBQUU7UUFDZkMsV0FBVyxFQUFFLENBQUM7UUFDZEMsOEJBQThCLEVBQUUsS0FBSztRQUNyQ0MseUJBQXlCLEVBQUUsS0FBSztRQUNoQ0MsZ0JBQWdCLEVBQUUsS0FBSztRQUN2QkMsYUFBYSxFQUFFO09BQ2hCO0lBQ0g7RUFDRjtFQUVBLE1BQU1JLFdBQVdBLENBQUNDLE9BQWlCO0lBQUE7SUFBQTdNLGFBQUEsR0FBQXFCLENBQUE7SUFBQXJCLGFBQUEsR0FBQW9CLENBQUE7SUFDakMsSUFBSTtNQUNGLE1BQU1nSCxJQUFJO01BQUE7TUFBQSxDQUFBcEksYUFBQSxHQUFBb0IsQ0FBQSxTQUFHLE1BQU0sSUFBSSxDQUFDcUIsV0FBVyxDQUFDcUYsY0FBYyxFQUFFO01BQUM7TUFBQTlILGFBQUEsR0FBQW9CLENBQUE7TUFDckQsSUFBSWdILElBQUksRUFBRTtRQUFBO1FBQUFwSSxhQUFBLEdBQUFzQixDQUFBO1FBQ1I7UUFDQSxNQUFNdUgsUUFBUTtRQUFBO1FBQUEsQ0FBQTdJLGFBQUEsR0FBQW9CLENBQUEsU0FBR3lMLE9BQU8sS0FBSyxhQUFhO1FBQUE7UUFBQSxDQUFBN00sYUFBQSxHQUFBc0IsQ0FBQSxXQUFHLFlBQVk7UUFBQTtRQUFBLENBQUF0QixhQUFBLEdBQUFzQixDQUFBLFdBQUd1TCxPQUF3QztRQUVwRyxNQUFNOUUsWUFBWTtRQUFBO1FBQUEsQ0FBQS9ILGFBQUEsR0FBQW9CLENBQUEsU0FBRztVQUNuQixHQUFHZ0gsSUFBSTtVQUNQdkgsSUFBSTtVQUFFO1VBQUEsQ0FBQWIsYUFBQSxHQUFBc0IsQ0FBQSxXQUFBOEcsSUFBSSxDQUFDdkgsSUFBSTtVQUFBO1VBQUEsQ0FBQWIsYUFBQSxHQUFBc0IsQ0FBQSxXQUFJLGNBQWM7VUFBRTtVQUNuQ2tILElBQUksRUFBRUssUUFBUTtVQUNkQyxRQUFRLEVBQUUsRUFBRTtVQUFFO1VBQ2RGLFNBQVMsRUFBRSxJQUFJM0UsSUFBSSxFQUFFO1VBQ3JCaUMsSUFBSSxFQUFFO1lBQ0pyRSxPQUFPLEVBQUUsQ0FBQztZQUNWc0UsWUFBWSxFQUFFLElBQUlsQyxJQUFJLEVBQUU7WUFDeEJtQyxjQUFjLEVBQUVnQyxJQUFJLENBQUN6RixFQUFFO1lBQ3ZCMEQsVUFBVSxFQUFFLE9BQWdCO1lBQzVCQyxTQUFTLEVBQUUsRUFBRTtZQUNiQyxTQUFTLEVBQUU7V0FDWjtVQUNEUyxPQUFPLEVBQUU7WUFDUCtCLFdBQVcsRUFBRTtjQUNYQyxRQUFRLEVBQUUsSUFBSTtjQUNkQyxlQUFlLEVBQUUsUUFBaUI7Y0FDbENDLFlBQVksRUFBRTthQUNmO1lBQ0RDLGlCQUFpQixFQUFFO2NBQ2pCQyxPQUFPLEVBQUUsSUFBSTtjQUNiQyxRQUFRLEVBQUUsQ0FBQyxnQkFBZ0IsRUFBRSxvQkFBb0IsRUFBRSxjQUFjOzs7U0FHdEU7UUFBQztRQUFBckosYUFBQSxHQUFBb0IsQ0FBQTtRQUNGLE1BQU0sSUFBSSxDQUFDcUIsV0FBVyxDQUFDNkYsUUFBUSxDQUFDUCxZQUFZLENBQUM7TUFDL0MsQ0FBQztNQUFBO01BQUE7UUFBQS9ILGFBQUEsR0FBQXNCLENBQUE7TUFBQTtJQUNILENBQUMsQ0FBQyxPQUFPd0IsS0FBSyxFQUFFO01BQUE7TUFBQTlDLGFBQUEsR0FBQW9CLENBQUE7TUFDZDJCLE9BQU8sQ0FBQ0QsS0FBSyxDQUFDLHlCQUF5QixFQUFFQSxLQUFLLENBQUM7TUFBQztNQUFBOUMsYUFBQSxHQUFBb0IsQ0FBQTtNQUNoRCxNQUFNMEIsS0FBSztJQUNiO0VBQ0Y7O0FBQ0Q7QUFBQTlDLGFBQUEsR0FBQW9CLENBQUE7QUExSERhLE9BQUEsQ0FBQXVKLG1CQUFBLEdBQUFBLG1CQUFBO0FBNEhBOzs7QUFHQSxNQUFhc0IsbUJBQW1CO0VBQzlCdEssWUFBb0JDLFdBQXdCO0lBQUE7SUFBQXpDLGFBQUEsR0FBQXFCLENBQUE7SUFBQXJCLGFBQUEsR0FBQW9CLENBQUE7SUFBeEIsS0FBQXFCLFdBQVcsR0FBWEEsV0FBVztFQUFnQjtFQUUvQyxNQUFNc0ssb0JBQW9CQSxDQUFBO0lBQUE7SUFBQS9NLGFBQUEsR0FBQXFCLENBQUE7SUFBQXJCLGFBQUEsR0FBQW9CLENBQUE7SUFDeEIsT0FBTyxNQUFNLElBQUksQ0FBQ3FCLFdBQVcsQ0FBQ3VLLHdCQUF3QixFQUFFO0VBQzFEO0VBRUEsTUFBTUMsbUJBQW1CQSxDQUFBO0lBQUE7SUFBQWpOLGFBQUEsR0FBQXFCLENBQUE7SUFBQXJCLGFBQUEsR0FBQW9CLENBQUE7SUFDdkIsT0FBTyxJQUFJLENBQUNxQixXQUFXLENBQUN3SyxtQkFBbUIsRUFBRTtFQUMvQztFQUVBLE1BQU1DLFlBQVlBLENBQUE7SUFBQTtJQUFBbE4sYUFBQSxHQUFBcUIsQ0FBQTtJQUFBckIsYUFBQSxHQUFBb0IsQ0FBQTtJQUNoQixPQUFPLE1BQU0sSUFBSSxDQUFDcUIsV0FBVyxDQUFDeUssWUFBWSxFQUFFO0VBQzlDO0VBRUEsTUFBTUMsaUJBQWlCQSxDQUFBO0lBQUE7SUFBQW5OLGFBQUEsR0FBQXFCLENBQUE7SUFBQXJCLGFBQUEsR0FBQW9CLENBQUE7SUFDckIsT0FBTyxNQUFNLElBQUksQ0FBQ3FCLFdBQVcsQ0FBQzBLLGlCQUFpQixFQUFFO0VBQ25EOztBQUNEO0FBQUFuTixhQUFBLEdBQUFvQixDQUFBO0FBbEJEYSxPQUFBLENBQUE2SyxtQkFBQSxHQUFBQSxtQkFBQTtBQW9CQTs7O0FBR08sZUFBZTVLLHFDQUFxQ0EsQ0FBQTtFQUFBO0VBQUFsQyxhQUFBLEdBQUFxQixDQUFBO0VBQUFyQixhQUFBLEdBQUFvQixDQUFBO0VBQ3pELElBQUk7SUFBQTtJQUFBcEIsYUFBQSxHQUFBb0IsQ0FBQTtJQUNGMkIsT0FBTyxDQUFDcUssR0FBRyxDQUFDLGdDQUFnQyxDQUFDO0lBRTdDO0lBQ0EsTUFBTTNLLFdBQVc7SUFBQTtJQUFBLENBQUF6QyxhQUFBLEdBQUFvQixDQUFBLFNBQUcsTUFBTSxJQUFBZSxhQUFBLENBQUFrTCxpQkFBaUIsRUFBQyxPQUFPLENBQUM7SUFFcEQ7SUFDQSxNQUFNQyxXQUFXO0lBQUE7SUFBQSxDQUFBdE4sYUFBQSxHQUFBb0IsQ0FBQSxTQUFHcUIsV0FBVyxDQUFDOEssVUFBVSxFQUFFO0lBQzVDLE1BQU1DLGNBQWM7SUFBQTtJQUFBLENBQUF4TixhQUFBLEdBQUFvQixDQUFBLFNBQUcsSUFBSXFNLE9BQU8sQ0FBUSxDQUFDQyxDQUFDLEVBQUVDLE1BQU0sS0FBSTtNQUFBO01BQUEzTixhQUFBLEdBQUFxQixDQUFBO01BQUFyQixhQUFBLEdBQUFvQixDQUFBO01BQ3REd00sVUFBVSxDQUFDLE1BQUs7UUFBQTtRQUFBNU4sYUFBQSxHQUFBcUIsQ0FBQTtRQUFBckIsYUFBQSxHQUFBb0IsQ0FBQTtRQUNkdU0sTUFBTSxDQUFDLElBQUkvQyxLQUFLLENBQUMsdURBQXVELENBQUMsQ0FBQztNQUM1RSxDQUFDLEVBQUUsS0FBSyxDQUFDO0lBQ1gsQ0FBQyxDQUFDO0lBQUM7SUFBQTVLLGFBQUEsR0FBQW9CLENBQUE7SUFFSCxNQUFNcU0sT0FBTyxDQUFDSSxJQUFJLENBQUMsQ0FBQ1AsV0FBVyxFQUFFRSxjQUFjLENBQUMsQ0FBQztJQUFDO0lBQUF4TixhQUFBLEdBQUFvQixDQUFBO0lBQ2xEMkIsT0FBTyxDQUFDcUssR0FBRyxDQUFDLHdDQUF3QyxDQUFDO0lBQUM7SUFBQXBOLGFBQUEsR0FBQW9CLENBQUE7SUFFdEQyQixPQUFPLENBQUNxSyxHQUFHLENBQUMsa0NBQWtDLENBQUM7SUFFL0M7SUFDQSxNQUFNVSxjQUFjO0lBQUE7SUFBQSxDQUFBOU4sYUFBQSxHQUFBb0IsQ0FBQSxTQUFHLElBQUltQixzQkFBc0IsQ0FBQ0UsV0FBVyxDQUFDO0lBQzlELE1BQU1zTCxXQUFXO0lBQUE7SUFBQSxDQUFBL04sYUFBQSxHQUFBb0IsQ0FBQSxTQUFHLElBQUl5RyxtQkFBbUIsQ0FBQ3BGLFdBQVcsQ0FBQztJQUN4RCxNQUFNdUwsa0JBQWtCO0lBQUE7SUFBQSxDQUFBaE8sYUFBQSxHQUFBb0IsQ0FBQSxTQUFHLElBQUlrSSwwQkFBMEIsRUFBRTtJQUMzRCxNQUFNMkUsaUJBQWlCO0lBQUE7SUFBQSxDQUFBak8sYUFBQSxHQUFBb0IsQ0FBQSxTQUFHLElBQUk4SSx5QkFBeUIsRUFBRTtJQUN6RCxNQUFNTSxhQUFhO0lBQUE7SUFBQSxDQUFBeEssYUFBQSxHQUFBb0IsQ0FBQSxTQUFHLElBQUltSixxQkFBcUIsQ0FBQzlILFdBQVcsQ0FBQztJQUM1RCxNQUFNeUwsV0FBVztJQUFBO0lBQUEsQ0FBQWxPLGFBQUEsR0FBQW9CLENBQUEsU0FBRyxJQUFJb0ssbUJBQW1CLENBQUMvSSxXQUFXLENBQUM7SUFDeEQsTUFBTTBMLFdBQVc7SUFBQTtJQUFBLENBQUFuTyxhQUFBLEdBQUFvQixDQUFBLFNBQUcsSUFBSTBMLG1CQUFtQixDQUFDckssV0FBVyxDQUFDO0lBQUM7SUFBQXpDLGFBQUEsR0FBQW9CLENBQUE7SUFFekQyQixPQUFPLENBQUNxSyxHQUFHLENBQUMsbUNBQW1DLENBQUM7SUFFaEQ7SUFDQSxNQUFNZ0IsYUFBYTtJQUFBO0lBQUEsQ0FBQXBPLGFBQUEsR0FBQW9CLENBQUEsU0FBRyxJQUFBaUIscUJBQUEsQ0FBQWdNLG1CQUFtQixHQUFFO0lBQzNDLE1BQU1DLGFBQWE7SUFBQTtJQUFBLENBQUF0TyxhQUFBLEdBQUFvQixDQUFBLFNBQUcsSUFBQWlCLHFCQUFBLENBQUFrTSxtQkFBbUIsR0FBRTtJQUUzQztJQUNBLE1BQU1DLGNBQWM7SUFBQTtJQUFBLENBQUF4TyxhQUFBLEdBQUFvQixDQUFBLFNBQUcsSUFBQWtCLHVCQUFBLENBQUFtTSwyQkFBMkIsRUFBQ2hNLFdBQVcsQ0FBQztJQUFDO0lBQUF6QyxhQUFBLEdBQUFvQixDQUFBO0lBRWhFMkIsT0FBTyxDQUFDcUssR0FBRyxDQUFDLHFDQUFxQyxDQUFDO0lBQUM7SUFBQXBOLGFBQUEsR0FBQW9CLENBQUE7SUFFbkQsT0FBTztNQUNMcUIsV0FBVztNQUNYcUwsY0FBYztNQUNkQyxXQUFXO01BQ1hDLGtCQUFrQjtNQUNsQkMsaUJBQWlCO01BQ2pCekQsYUFBYTtNQUNiMEQsV0FBVztNQUNYTSxjQUFjO01BQ2RKLGFBQWE7TUFDYkUsYUFBYTtNQUNiSDtLQUNEO0VBQ0gsQ0FBQyxDQUFDLE9BQU9yTCxLQUFLLEVBQUU7SUFBQTtJQUFBOUMsYUFBQSxHQUFBb0IsQ0FBQTtJQUNkMkIsT0FBTyxDQUFDRCxLQUFLLENBQUMsd0RBQXdELEVBQUVBLEtBQUssQ0FBQztJQUU5RTtJQUFBO0lBQUE5QyxhQUFBLEdBQUFvQixDQUFBO0lBQ0EsSUFBSTBCLEtBQUssWUFBWThILEtBQUssRUFBRTtNQUFBO01BQUE1SyxhQUFBLEdBQUFzQixDQUFBO01BQUF0QixhQUFBLEdBQUFvQixDQUFBO01BQzFCLE1BQU0sSUFBSXdKLEtBQUssQ0FBQyxzQ0FBc0M5SCxLQUFLLENBQUM0TCxPQUFPLEVBQUUsQ0FBQztJQUN4RSxDQUFDO0lBQUE7SUFBQTtNQUFBMU8sYUFBQSxHQUFBc0IsQ0FBQTtJQUFBO0lBQUF0QixhQUFBLEdBQUFvQixDQUFBO0lBRUQsTUFBTSxJQUFJd0osS0FBSyxDQUFDLHlEQUF5RCxDQUFDO0VBQzVFO0FBQ0YiLCJpZ25vcmVMaXN0IjpbXX0=