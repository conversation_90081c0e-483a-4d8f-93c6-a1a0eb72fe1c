af67d93db07fab25ae0a18af276dae29
"use strict";

/**
 * Simulated Annealing Algorithm Implementation for System Optimization
 *
 * Implements simulated annealing optimization with:
 * - Configurable cooling schedules (linear, exponential, logarithmic, adaptive)
 * - Multiple neighborhood generation strategies
 * - Constraint handling with penalty methods
 * - Adaptive parameter adjustment
 * - Restart mechanisms for global optimization
 *
 * @version 3.0.0
 * <AUTHOR> Suite Development Team
 */
/* istanbul ignore next */
function cov_1p7ah0mc1t() {
  var path = "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\algorithms\\SimulatedAnnealing.ts";
  var hash = "d7a8bc38eb8abc499d60b51629b7eff5ffb9eb9e";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\algorithms\\SimulatedAnnealing.ts",
    statementMap: {
      "0": {
        start: {
          line: 15,
          column: 0
        },
        end: {
          line: 15,
          column: 62
        }
      },
      "1": {
        start: {
          line: 16,
          column: 0
        },
        end: {
          line: 16,
          column: 36
        }
      },
      "2": {
        start: {
          line: 17,
          column: 34
        },
        end: {
          line: 17,
          column: 77
        }
      },
      "3": {
        start: {
          line: 23,
          column: 8
        },
        end: {
          line: 23,
          column: 33
        }
      },
      "4": {
        start: {
          line: 24,
          column: 8
        },
        end: {
          line: 24,
          column: 33
        }
      },
      "5": {
        start: {
          line: 25,
          column: 8
        },
        end: {
          line: 25,
          column: 26
        }
      },
      "6": {
        start: {
          line: 26,
          column: 8
        },
        end: {
          line: 26,
          column: 33
        }
      },
      "7": {
        start: {
          line: 27,
          column: 8
        },
        end: {
          line: 47,
          column: 10
        }
      },
      "8": {
        start: {
          line: 49,
          column: 8
        },
        end: {
          line: 54,
          column: 9
        }
      },
      "9": {
        start: {
          line: 50,
          column: 12
        },
        end: {
          line: 50,
          column: 77
        }
      },
      "10": {
        start: {
          line: 53,
          column: 12
        },
        end: {
          line: 53,
          column: 38
        }
      },
      "11": {
        start: {
          line: 60,
          column: 26
        },
        end: {
          line: 60,
          column: 43
        }
      },
      "12": {
        start: {
          line: 61,
          column: 8
        },
        end: {
          line: 84,
          column: 9
        }
      },
      "13": {
        start: {
          line: 63,
          column: 12
        },
        end: {
          line: 63,
          column: 46
        }
      },
      "14": {
        start: {
          line: 65,
          column: 12
        },
        end: {
          line: 65,
          column: 94
        }
      },
      "15": {
        start: {
          line: 67,
          column: 12
        },
        end: {
          line: 73,
          column: 13
        }
      },
      "16": {
        start: {
          line: 68,
          column: 16
        },
        end: {
          line: 68,
          column: 93
        }
      },
      "17": {
        start: {
          line: 69,
          column: 16
        },
        end: {
          line: 69,
          column: 37
        }
      },
      "18": {
        start: {
          line: 70,
          column: 16
        },
        end: {
          line: 72,
          column: 17
        }
      },
      "19": {
        start: {
          line: 71,
          column: 20
        },
        end: {
          line: 71,
          column: 49
        }
      },
      "20": {
        start: {
          line: 75,
          column: 12
        },
        end: {
          line: 77,
          column: 13
        }
      },
      "21": {
        start: {
          line: 76,
          column: 16
        },
        end: {
          line: 76,
          column: 84
        }
      },
      "22": {
        start: {
          line: 79,
          column: 12
        },
        end: {
          line: 79,
          column: 69
        }
      },
      "23": {
        start: {
          line: 82,
          column: 12
        },
        end: {
          line: 82,
          column: 77
        }
      },
      "24": {
        start: {
          line: 83,
          column: 12
        },
        end: {
          line: 83,
          column: 24
        }
      },
      "25": {
        start: {
          line: 90,
          column: 8
        },
        end: {
          line: 90,
          column: 33
        }
      },
      "26": {
        start: {
          line: 91,
          column: 8
        },
        end: {
          line: 91,
          column: 33
        }
      },
      "27": {
        start: {
          line: 92,
          column: 8
        },
        end: {
          line: 92,
          column: 26
        }
      },
      "28": {
        start: {
          line: 93,
          column: 8
        },
        end: {
          line: 93,
          column: 33
        }
      },
      "29": {
        start: {
          line: 94,
          column: 8
        },
        end: {
          line: 94,
          column: 120
        }
      },
      "30": {
        start: {
          line: 101,
          column: 32
        },
        end: {
          line: 101,
          column: 66
        }
      },
      "31": {
        start: {
          line: 102,
          column: 8
        },
        end: {
          line: 102,
          column: 102
        }
      },
      "32": {
        start: {
          line: 104,
          column: 8
        },
        end: {
          line: 113,
          column: 10
        }
      },
      "33": {
        start: {
          line: 114,
          column: 8
        },
        end: {
          line: 114,
          column: 51
        }
      },
      "34": {
        start: {
          line: 120,
          column: 26
        },
        end: {
          line: 120,
          column: 28
        }
      },
      "35": {
        start: {
          line: 121,
          column: 8
        },
        end: {
          line: 133,
          column: 9
        }
      },
      "36": {
        start: {
          line: 122,
          column: 12
        },
        end: {
          line: 132,
          column: 13
        }
      },
      "37": {
        start: {
          line: 124,
          column: 36
        },
        end: {
          line: 124,
          column: 94
        }
      },
      "38": {
        start: {
          line: 125,
          column: 16
        },
        end: {
          line: 125,
          column: 78
        }
      },
      "39": {
        start: {
          line: 129,
          column: 28
        },
        end: {
          line: 129,
          column: 101
        }
      },
      "40": {
        start: {
          line: 130,
          column: 28
        },
        end: {
          line: 130,
          column: 101
        }
      },
      "41": {
        start: {
          line: 131,
          column: 16
        },
        end: {
          line: 131,
          column: 75
        }
      },
      "42": {
        start: {
          line: 134,
          column: 8
        },
        end: {
          line: 143,
          column: 10
        }
      },
      "43": {
        start: {
          line: 149,
          column: 8
        },
        end: {
          line: 187,
          column: 9
        }
      },
      "44": {
        start: {
          line: 151,
          column: 30
        },
        end: {
          line: 151,
          column: 83
        }
      },
      "45": {
        start: {
          line: 153,
          column: 35
        },
        end: {
          line: 153,
          column: 63
        }
      },
      "46": {
        start: {
          line: 154,
          column: 12
        },
        end: {
          line: 154,
          column: 46
        }
      },
      "47": {
        start: {
          line: 156,
          column: 12
        },
        end: {
          line: 158,
          column: 13
        }
      },
      "48": {
        start: {
          line: 157,
          column: 16
        },
        end: {
          line: 157,
          column: 95
        }
      },
      "49": {
        start: {
          line: 160,
          column: 12
        },
        end: {
          line: 160,
          column: 47
        }
      },
      "50": {
        start: {
          line: 161,
          column: 12
        },
        end: {
          line: 171,
          column: 13
        }
      },
      "51": {
        start: {
          line: 162,
          column: 34
        },
        end: {
          line: 162,
          column: 63
        }
      },
      "52": {
        start: {
          line: 163,
          column: 16
        },
        end: {
          line: 170,
          column: 19
        }
      },
      "53": {
        start: {
          line: 173,
          column: 12
        },
        end: {
          line: 173,
          column: 94
        }
      },
      "54": {
        start: {
          line: 173,
          column: 73
        },
        end: {
          line: 173,
          column: 92
        }
      },
      "55": {
        start: {
          line: 175,
          column: 12
        },
        end: {
          line: 180,
          column: 13
        }
      },
      "56": {
        start: {
          line: 176,
          column: 37
        },
        end: {
          line: 178,
          column: 59
        }
      },
      "57": {
        start: {
          line: 177,
          column: 33
        },
        end: {
          line: 177,
          column: 51
        }
      },
      "58": {
        start: {
          line: 178,
          column: 40
        },
        end: {
          line: 178,
          column: 55
        }
      },
      "59": {
        start: {
          line: 179,
          column: 16
        },
        end: {
          line: 179,
          column: 49
        }
      },
      "60": {
        start: {
          line: 181,
          column: 12
        },
        end: {
          line: 181,
          column: 35
        }
      },
      "61": {
        start: {
          line: 184,
          column: 12
        },
        end: {
          line: 184,
          column: 63
        }
      },
      "62": {
        start: {
          line: 185,
          column: 12
        },
        end: {
          line: 185,
          column: 48
        }
      },
      "63": {
        start: {
          line: 186,
          column: 12
        },
        end: {
          line: 186,
          column: 38
        }
      },
      "64": {
        start: {
          line: 193,
          column: 8
        },
        end: {
          line: 196,
          column: 12
        }
      },
      "65": {
        start: {
          line: 193,
          column: 50
        },
        end: {
          line: 196,
          column: 9
        }
      },
      "66": {
        start: {
          line: 202,
          column: 8
        },
        end: {
          line: 203,
          column: 19
        }
      },
      "67": {
        start: {
          line: 203,
          column: 12
        },
        end: {
          line: 203,
          column: 19
        }
      },
      "68": {
        start: {
          line: 205,
          column: 33
        },
        end: {
          line: 205,
          column: 91
        }
      },
      "69": {
        start: {
          line: 206,
          column: 8
        },
        end: {
          line: 206,
          column: 103
        }
      },
      "70": {
        start: {
          line: 208,
          column: 23
        },
        end: {
          line: 208,
          column: 84
        }
      },
      "71": {
        start: {
          line: 209,
          column: 38
        },
        end: {
          line: 209,
          column: 112
        }
      },
      "72": {
        start: {
          line: 211,
          column: 8
        },
        end: {
          line: 229,
          column: 9
        }
      },
      "73": {
        start: {
          line: 213,
          column: 12
        },
        end: {
          line: 213,
          column: 58
        }
      },
      "74": {
        start: {
          line: 214,
          column: 12
        },
        end: {
          line: 214,
          column: 46
        }
      },
      "75": {
        start: {
          line: 215,
          column: 12
        },
        end: {
          line: 224,
          column: 13
        }
      },
      "76": {
        start: {
          line: 216,
          column: 16
        },
        end: {
          line: 216,
          column: 51
        }
      },
      "77": {
        start: {
          line: 218,
          column: 16
        },
        end: {
          line: 220,
          column: 17
        }
      },
      "78": {
        start: {
          line: 219,
          column: 20
        },
        end: {
          line: 219,
          column: 64
        }
      },
      "79": {
        start: {
          line: 223,
          column: 16
        },
        end: {
          line: 223,
          column: 51
        }
      },
      "80": {
        start: {
          line: 228,
          column: 12
        },
        end: {
          line: 228,
          column: 46
        }
      },
      "81": {
        start: {
          line: 231,
          column: 8
        },
        end: {
          line: 231,
          column: 123
        }
      },
      "82": {
        start: {
          line: 232,
          column: 8
        },
        end: {
          line: 232,
          column: 38
        }
      },
      "83": {
        start: {
          line: 238,
          column: 25
        },
        end: {
          line: 242,
          column: 9
        }
      },
      "84": {
        start: {
          line: 244,
          column: 28
        },
        end: {
          line: 244,
          column: 59
        }
      },
      "85": {
        start: {
          line: 245,
          column: 33
        },
        end: {
          line: 245,
          column: 92
        }
      },
      "86": {
        start: {
          line: 246,
          column: 25
        },
        end: {
          line: 246,
          column: 79
        }
      },
      "87": {
        start: {
          line: 246,
          column: 53
        },
        end: {
          line: 246,
          column: 78
        }
      },
      "88": {
        start: {
          line: 247,
          column: 8
        },
        end: {
          line: 248,
          column: 28
        }
      },
      "89": {
        start: {
          line: 248,
          column: 12
        },
        end: {
          line: 248,
          column: 28
        }
      },
      "90": {
        start: {
          line: 250,
          column: 8
        },
        end: {
          line: 250,
          column: 122
        }
      },
      "91": {
        start: {
          line: 251,
          column: 8
        },
        end: {
          line: 251,
          column: 24
        }
      },
      "92": {
        start: {
          line: 257,
          column: 8
        },
        end: {
          line: 289,
          column: 9
        }
      },
      "93": {
        start: {
          line: 259,
          column: 33
        },
        end: {
          line: 259,
          column: 78
        }
      },
      "94": {
        start: {
          line: 260,
          column: 37
        },
        end: {
          line: 260,
          column: 127
        }
      },
      "95": {
        start: {
          line: 261,
          column: 29
        },
        end: {
          line: 261,
          column: 73
        }
      },
      "96": {
        start: {
          line: 262,
          column: 29
        },
        end: {
          line: 262,
          column: 106
        }
      },
      "97": {
        start: {
          line: 263,
          column: 32
        },
        end: {
          line: 263,
          column: 96
        }
      },
      "98": {
        start: {
          line: 264,
          column: 12
        },
        end: {
          line: 264,
          column: 56
        }
      },
      "99": {
        start: {
          line: 266,
          column: 13
        },
        end: {
          line: 289,
          column: 9
        }
      },
      "100": {
        start: {
          line: 268,
          column: 24
        },
        end: {
          line: 268,
          column: 97
        }
      },
      "101": {
        start: {
          line: 269,
          column: 24
        },
        end: {
          line: 269,
          column: 97
        }
      },
      "102": {
        start: {
          line: 270,
          column: 26
        },
        end: {
          line: 270,
          column: 35
        }
      },
      "103": {
        start: {
          line: 272,
          column: 12
        },
        end: {
          line: 287,
          column: 13
        }
      },
      "104": {
        start: {
          line: 274,
          column: 34
        },
        end: {
          line: 274,
          column: 74
        }
      },
      "105": {
        start: {
          line: 275,
          column: 20
        },
        end: {
          line: 275,
          column: 81
        }
      },
      "106": {
        start: {
          line: 276,
          column: 20
        },
        end: {
          line: 276,
          column: 26
        }
      },
      "107": {
        start: {
          line: 278,
          column: 34
        },
        end: {
          line: 278,
          column: 74
        }
      },
      "108": {
        start: {
          line: 279,
          column: 20
        },
        end: {
          line: 279,
          column: 85
        }
      },
      "109": {
        start: {
          line: 280,
          column: 20
        },
        end: {
          line: 280,
          column: 26
        }
      },
      "110": {
        start: {
          line: 282,
          column: 34
        },
        end: {
          line: 282,
          column: 74
        }
      },
      "111": {
        start: {
          line: 283,
          column: 20
        },
        end: {
          line: 283,
          column: 79
        }
      },
      "112": {
        start: {
          line: 284,
          column: 20
        },
        end: {
          line: 284,
          column: 26
        }
      },
      "113": {
        start: {
          line: 286,
          column: 20
        },
        end: {
          line: 286,
          column: 116
        }
      },
      "114": {
        start: {
          line: 288,
          column: 12
        },
        end: {
          line: 288,
          column: 63
        }
      },
      "115": {
        start: {
          line: 290,
          column: 8
        },
        end: {
          line: 290,
          column: 28
        }
      },
      "116": {
        start: {
          line: 296,
          column: 8
        },
        end: {
          line: 298,
          column: 9
        }
      },
      "117": {
        start: {
          line: 297,
          column: 12
        },
        end: {
          line: 297,
          column: 23
        }
      },
      "118": {
        start: {
          line: 299,
          column: 8
        },
        end: {
          line: 308,
          column: 9
        }
      },
      "119": {
        start: {
          line: 301,
          column: 16
        },
        end: {
          line: 301,
          column: 55
        }
      },
      "120": {
        start: {
          line: 303,
          column: 16
        },
        end: {
          line: 303,
          column: 68
        }
      },
      "121": {
        start: {
          line: 305,
          column: 16
        },
        end: {
          line: 305,
          column: 101
        }
      },
      "122": {
        start: {
          line: 307,
          column: 16
        },
        end: {
          line: 307,
          column: 55
        }
      },
      "123": {
        start: {
          line: 314,
          column: 25
        },
        end: {
          line: 314,
          column: 56
        }
      },
      "124": {
        start: {
          line: 315,
          column: 8
        },
        end: {
          line: 331,
          column: 9
        }
      },
      "125": {
        start: {
          line: 317,
          column: 35
        },
        end: {
          line: 317,
          column: 124
        }
      },
      "126": {
        start: {
          line: 318,
          column: 16
        },
        end: {
          line: 318,
          column: 113
        }
      },
      "127": {
        start: {
          line: 320,
          column: 16
        },
        end: {
          line: 320,
          column: 102
        }
      },
      "128": {
        start: {
          line: 322,
          column: 16
        },
        end: {
          line: 322,
          column: 114
        }
      },
      "129": {
        start: {
          line: 325,
          column: 39
        },
        end: {
          line: 325,
          column: 144
        }
      },
      "130": {
        start: {
          line: 326,
          column: 45
        },
        end: {
          line: 326,
          column: 48
        }
      },
      "131": {
        start: {
          line: 327,
          column: 41
        },
        end: {
          line: 327,
          column: 92
        }
      },
      "132": {
        start: {
          line: 328,
          column: 16
        },
        end: {
          line: 328,
          column: 98
        }
      },
      "133": {
        start: {
          line: 330,
          column: 16
        },
        end: {
          line: 330,
          column: 102
        }
      },
      "134": {
        start: {
          line: 337,
          column: 8
        },
        end: {
          line: 338,
          column: 24
        }
      },
      "135": {
        start: {
          line: 338,
          column: 12
        },
        end: {
          line: 338,
          column: 24
        }
      },
      "136": {
        start: {
          line: 340,
          column: 8
        },
        end: {
          line: 342,
          column: 9
        }
      },
      "137": {
        start: {
          line: 341,
          column: 12
        },
        end: {
          line: 341,
          column: 24
        }
      },
      "138": {
        start: {
          line: 344,
          column: 8
        },
        end: {
          line: 346,
          column: 9
        }
      },
      "139": {
        start: {
          line: 345,
          column: 12
        },
        end: {
          line: 345,
          column: 24
        }
      },
      "140": {
        start: {
          line: 348,
          column: 8
        },
        end: {
          line: 355,
          column: 9
        }
      },
      "141": {
        start: {
          line: 349,
          column: 34
        },
        end: {
          line: 349,
          column: 57
        }
      },
      "142": {
        start: {
          line: 350,
          column: 31
        },
        end: {
          line: 350,
          column: 81
        }
      },
      "143": {
        start: {
          line: 350,
          column: 66
        },
        end: {
          line: 350,
          column: 79
        }
      },
      "144": {
        start: {
          line: 351,
          column: 32
        },
        end: {
          line: 351,
          column: 70
        }
      },
      "145": {
        start: {
          line: 352,
          column: 12
        },
        end: {
          line: 354,
          column: 13
        }
      },
      "146": {
        start: {
          line: 353,
          column: 16
        },
        end: {
          line: 353,
          column: 28
        }
      },
      "147": {
        start: {
          line: 356,
          column: 8
        },
        end: {
          line: 356,
          column: 21
        }
      },
      "148": {
        start: {
          line: 362,
          column: 8
        },
        end: {
          line: 363,
          column: 25
        }
      },
      "149": {
        start: {
          line: 363,
          column: 12
        },
        end: {
          line: 363,
          column: 25
        }
      },
      "150": {
        start: {
          line: 364,
          column: 8
        },
        end: {
          line: 365,
          column: 25
        }
      },
      "151": {
        start: {
          line: 365,
          column: 12
        },
        end: {
          line: 365,
          column: 25
        }
      },
      "152": {
        start: {
          line: 366,
          column: 8
        },
        end: {
          line: 367,
          column: 25
        }
      },
      "153": {
        start: {
          line: 367,
          column: 12
        },
        end: {
          line: 367,
          column: 25
        }
      },
      "154": {
        start: {
          line: 369,
          column: 33
        },
        end: {
          line: 369,
          column: 36
        }
      },
      "155": {
        start: {
          line: 370,
          column: 8
        },
        end: {
          line: 376,
          column: 9
        }
      },
      "156": {
        start: {
          line: 371,
          column: 34
        },
        end: {
          line: 371,
          column: 71
        }
      },
      "157": {
        start: {
          line: 372,
          column: 36
        },
        end: {
          line: 372,
          column: 97
        }
      },
      "158": {
        start: {
          line: 372,
          column: 82
        },
        end: {
          line: 372,
          column: 95
        }
      },
      "159": {
        start: {
          line: 373,
          column: 12
        },
        end: {
          line: 375,
          column: 13
        }
      },
      "160": {
        start: {
          line: 374,
          column: 16
        },
        end: {
          line: 374,
          column: 28
        }
      },
      "161": {
        start: {
          line: 377,
          column: 8
        },
        end: {
          line: 377,
          column: 21
        }
      },
      "162": {
        start: {
          line: 383,
          column: 8
        },
        end: {
          line: 383,
          column: 102
        }
      },
      "163": {
        start: {
          line: 385,
          column: 28
        },
        end: {
          line: 385,
          column: 62
        }
      },
      "164": {
        start: {
          line: 386,
          column: 8
        },
        end: {
          line: 386,
          column: 98
        }
      },
      "165": {
        start: {
          line: 388,
          column: 8
        },
        end: {
          line: 388,
          column: 49
        }
      },
      "166": {
        start: {
          line: 389,
          column: 8
        },
        end: {
          line: 389,
          column: 75
        }
      },
      "167": {
        start: {
          line: 390,
          column: 8
        },
        end: {
          line: 390,
          column: 40
        }
      },
      "168": {
        start: {
          line: 391,
          column: 8
        },
        end: {
          line: 391,
          column: 44
        }
      },
      "169": {
        start: {
          line: 392,
          column: 8
        },
        end: {
          line: 392,
          column: 44
        }
      },
      "170": {
        start: {
          line: 393,
          column: 8
        },
        end: {
          line: 393,
          column: 45
        }
      },
      "171": {
        start: {
          line: 394,
          column: 8
        },
        end: {
          line: 394,
          column: 45
        }
      },
      "172": {
        start: {
          line: 395,
          column: 8
        },
        end: {
          line: 395,
          column: 41
        }
      },
      "173": {
        start: {
          line: 397,
          column: 8
        },
        end: {
          line: 400,
          column: 9
        }
      },
      "174": {
        start: {
          line: 398,
          column: 12
        },
        end: {
          line: 398,
          column: 89
        }
      },
      "175": {
        start: {
          line: 399,
          column: 12
        },
        end: {
          line: 399,
          column: 33
        }
      },
      "176": {
        start: {
          line: 406,
          column: 8
        },
        end: {
          line: 407,
          column: 19
        }
      },
      "177": {
        start: {
          line: 407,
          column: 12
        },
        end: {
          line: 407,
          column: 19
        }
      },
      "178": {
        start: {
          line: 408,
          column: 27
        },
        end: {
          line: 408,
          column: 92
        }
      },
      "179": {
        start: {
          line: 409,
          column: 8
        },
        end: {
          line: 410,
          column: 19
        }
      },
      "180": {
        start: {
          line: 410,
          column: 12
        },
        end: {
          line: 410,
          column: 19
        }
      },
      "181": {
        start: {
          line: 411,
          column: 31
        },
        end: {
          line: 411,
          column: 75
        }
      },
      "182": {
        start: {
          line: 412,
          column: 37
        },
        end: {
          line: 412,
          column: 40
        }
      },
      "183": {
        start: {
          line: 413,
          column: 8
        },
        end: {
          line: 420,
          column: 9
        }
      },
      "184": {
        start: {
          line: 415,
          column: 12
        },
        end: {
          line: 415,
          column: 101
        }
      },
      "185": {
        start: {
          line: 417,
          column: 13
        },
        end: {
          line: 420,
          column: 9
        }
      },
      "186": {
        start: {
          line: 419,
          column: 12
        },
        end: {
          line: 419,
          column: 102
        }
      },
      "187": {
        start: {
          line: 426,
          column: 8
        },
        end: {
          line: 427,
          column: 19
        }
      },
      "188": {
        start: {
          line: 427,
          column: 12
        },
        end: {
          line: 427,
          column: 19
        }
      },
      "189": {
        start: {
          line: 428,
          column: 24
        },
        end: {
          line: 436,
          column: 9
        }
      },
      "190": {
        start: {
          line: 437,
          column: 8
        },
        end: {
          line: 437,
          column: 35
        }
      },
      "191": {
        start: {
          line: 443,
          column: 30
        },
        end: {
          line: 443,
          column: 59
        }
      },
      "192": {
        start: {
          line: 444,
          column: 27
        },
        end: {
          line: 462,
          column: 9
        }
      },
      "193": {
        start: {
          line: 449,
          column: 54
        },
        end: {
          line: 449,
          column: 67
        }
      },
      "194": {
        start: {
          line: 450,
          column: 57
        },
        end: {
          line: 450,
          column: 73
        }
      },
      "195": {
        start: {
          line: 451,
          column: 52
        },
        end: {
          line: 451,
          column: 63
        }
      },
      "196": {
        start: {
          line: 452,
          column: 62
        },
        end: {
          line: 452,
          column: 84
        }
      },
      "197": {
        start: {
          line: 463,
          column: 36
        },
        end: {
          line: 468,
          column: 9
        }
      },
      "198": {
        start: {
          line: 469,
          column: 8
        },
        end: {
          line: 479,
          column: 10
        }
      },
      "199": {
        start: {
          line: 483,
          column: 8
        },
        end: {
          line: 483,
          column: 81
        }
      },
      "200": {
        start: {
          line: 486,
          column: 20
        },
        end: {
          line: 486,
          column: 24
        }
      },
      "201": {
        start: {
          line: 487,
          column: 8
        },
        end: {
          line: 490,
          column: 10
        }
      },
      "202": {
        start: {
          line: 488,
          column: 12
        },
        end: {
          line: 488,
          column: 52
        }
      },
      "203": {
        start: {
          line: 489,
          column: 12
        },
        end: {
          line: 489,
          column: 34
        }
      },
      "204": {
        start: {
          line: 494,
          column: 19
        },
        end: {
          line: 494,
          column: 32
        }
      },
      "205": {
        start: {
          line: 495,
          column: 19
        },
        end: {
          line: 495,
          column: 32
        }
      },
      "206": {
        start: {
          line: 496,
          column: 8
        },
        end: {
          line: 496,
          column: 73
        }
      },
      "207": {
        start: {
          line: 500,
          column: 18
        },
        end: {
          line: 500,
          column: 31
        }
      },
      "208": {
        start: {
          line: 501,
          column: 8
        },
        end: {
          line: 501,
          column: 45
        }
      },
      "209": {
        start: {
          line: 504,
          column: 8
        },
        end: {
          line: 505,
          column: 21
        }
      },
      "210": {
        start: {
          line: 505,
          column: 12
        },
        end: {
          line: 505,
          column: 21
        }
      },
      "211": {
        start: {
          line: 506,
          column: 21
        },
        end: {
          line: 506,
          column: 78
        }
      },
      "212": {
        start: {
          line: 506,
          column: 49
        },
        end: {
          line: 506,
          column: 58
        }
      },
      "213": {
        start: {
          line: 507,
          column: 29
        },
        end: {
          line: 507,
          column: 71
        }
      },
      "214": {
        start: {
          line: 507,
          column: 47
        },
        end: {
          line: 507,
          column: 70
        }
      },
      "215": {
        start: {
          line: 508,
          column: 8
        },
        end: {
          line: 508,
          column: 81
        }
      },
      "216": {
        start: {
          line: 508,
          column: 50
        },
        end: {
          line: 508,
          column: 60
        }
      },
      "217": {
        start: {
          line: 511,
          column: 0
        },
        end: {
          line: 511,
          column: 48
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 22,
            column: 4
          },
          end: {
            line: 22,
            column: 5
          }
        },
        loc: {
          start: {
            line: 22,
            column: 28
          },
          end: {
            line: 55,
            column: 5
          }
        },
        line: 22
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 59,
            column: 4
          },
          end: {
            line: 59,
            column: 5
          }
        },
        loc: {
          start: {
            line: 59,
            column: 68
          },
          end: {
            line: 85,
            column: 5
          }
        },
        line: 59
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 89,
            column: 4
          },
          end: {
            line: 89,
            column: 5
          }
        },
        loc: {
          start: {
            line: 89,
            column: 33
          },
          end: {
            line: 95,
            column: 5
          }
        },
        line: 89
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 99,
            column: 4
          },
          end: {
            line: 99,
            column: 5
          }
        },
        loc: {
          start: {
            line: 99,
            column: 81
          },
          end: {
            line: 115,
            column: 5
          }
        },
        line: 99
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 119,
            column: 4
          },
          end: {
            line: 119,
            column: 5
          }
        },
        loc: {
          start: {
            line: 119,
            column: 34
          },
          end: {
            line: 144,
            column: 5
          }
        },
        line: 119
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 148,
            column: 4
          },
          end: {
            line: 148,
            column: 5
          }
        },
        loc: {
          start: {
            line: 148,
            column: 86
          },
          end: {
            line: 188,
            column: 5
          }
        },
        line: 148
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 173,
            column: 68
          },
          end: {
            line: 173,
            column: 69
          }
        },
        loc: {
          start: {
            line: 173,
            column: 73
          },
          end: {
            line: 173,
            column: 92
          }
        },
        line: 173
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 177,
            column: 28
          },
          end: {
            line: 177,
            column: 29
          }
        },
        loc: {
          start: {
            line: 177,
            column: 33
          },
          end: {
            line: 177,
            column: 51
          }
        },
        line: 177
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 178,
            column: 28
          },
          end: {
            line: 178,
            column: 29
          }
        },
        loc: {
          start: {
            line: 178,
            column: 40
          },
          end: {
            line: 178,
            column: 55
          }
        },
        line: 178
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 192,
            column: 4
          },
          end: {
            line: 192,
            column: 5
          }
        },
        loc: {
          start: {
            line: 192,
            column: 53
          },
          end: {
            line: 197,
            column: 5
          }
        },
        line: 192
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 193,
            column: 37
          },
          end: {
            line: 193,
            column: 38
          }
        },
        loc: {
          start: {
            line: 193,
            column: 50
          },
          end: {
            line: 196,
            column: 9
          }
        },
        line: 193
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 201,
            column: 4
          },
          end: {
            line: 201,
            column: 5
          }
        },
        loc: {
          start: {
            line: 201,
            column: 76
          },
          end: {
            line: 233,
            column: 5
          }
        },
        line: 201
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 237,
            column: 4
          },
          end: {
            line: 237,
            column: 5
          }
        },
        loc: {
          start: {
            line: 237,
            column: 47
          },
          end: {
            line: 252,
            column: 5
          }
        },
        line: 237
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 246,
            column: 48
          },
          end: {
            line: 246,
            column: 49
          }
        },
        loc: {
          start: {
            line: 246,
            column: 53
          },
          end: {
            line: 246,
            column: 78
          }
        },
        line: 246
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 256,
            column: 4
          },
          end: {
            line: 256,
            column: 5
          }
        },
        loc: {
          start: {
            line: 256,
            column: 50
          },
          end: {
            line: 291,
            column: 5
          }
        },
        line: 256
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 295,
            column: 4
          },
          end: {
            line: 295,
            column: 5
          }
        },
        loc: {
          start: {
            line: 295,
            column: 56
          },
          end: {
            line: 309,
            column: 5
          }
        },
        line: 295
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 313,
            column: 4
          },
          end: {
            line: 313,
            column: 5
          }
        },
        loc: {
          start: {
            line: 313,
            column: 53
          },
          end: {
            line: 332,
            column: 5
          }
        },
        line: 313
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 336,
            column: 4
          },
          end: {
            line: 336,
            column: 5
          }
        },
        loc: {
          start: {
            line: 336,
            column: 22
          },
          end: {
            line: 357,
            column: 5
          }
        },
        line: 336
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 350,
            column: 61
          },
          end: {
            line: 350,
            column: 62
          }
        },
        loc: {
          start: {
            line: 350,
            column: 66
          },
          end: {
            line: 350,
            column: 79
          }
        },
        line: 350
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 361,
            column: 4
          },
          end: {
            line: 361,
            column: 5
          }
        },
        loc: {
          start: {
            line: 361,
            column: 20
          },
          end: {
            line: 378,
            column: 5
          }
        },
        line: 361
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 372,
            column: 77
          },
          end: {
            line: 372,
            column: 78
          }
        },
        loc: {
          start: {
            line: 372,
            column: 82
          },
          end: {
            line: 372,
            column: 95
          }
        },
        line: 372
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 382,
            column: 4
          },
          end: {
            line: 382,
            column: 5
          }
        },
        loc: {
          start: {
            line: 382,
            column: 67
          },
          end: {
            line: 401,
            column: 5
          }
        },
        line: 382
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 405,
            column: 4
          },
          end: {
            line: 405,
            column: 5
          }
        },
        loc: {
          start: {
            line: 405,
            column: 28
          },
          end: {
            line: 421,
            column: 5
          }
        },
        line: 405
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 425,
            column: 4
          },
          end: {
            line: 425,
            column: 5
          }
        },
        loc: {
          start: {
            line: 425,
            column: 20
          },
          end: {
            line: 438,
            column: 5
          }
        },
        line: 425
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 442,
            column: 4
          },
          end: {
            line: 442,
            column: 5
          }
        },
        loc: {
          start: {
            line: 442,
            column: 49
          },
          end: {
            line: 480,
            column: 5
          }
        },
        line: 442
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 449,
            column: 49
          },
          end: {
            line: 449,
            column: 50
          }
        },
        loc: {
          start: {
            line: 449,
            column: 54
          },
          end: {
            line: 449,
            column: 67
          }
        },
        line: 449
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 450,
            column: 52
          },
          end: {
            line: 450,
            column: 53
          }
        },
        loc: {
          start: {
            line: 450,
            column: 57
          },
          end: {
            line: 450,
            column: 73
          }
        },
        line: 450
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 451,
            column: 47
          },
          end: {
            line: 451,
            column: 48
          }
        },
        loc: {
          start: {
            line: 451,
            column: 52
          },
          end: {
            line: 451,
            column: 63
          }
        },
        line: 451
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 452,
            column: 57
          },
          end: {
            line: 452,
            column: 58
          }
        },
        loc: {
          start: {
            line: 452,
            column: 62
          },
          end: {
            line: 452,
            column: 84
          }
        },
        line: 452
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 482,
            column: 4
          },
          end: {
            line: 482,
            column: 5
          }
        },
        loc: {
          start: {
            line: 482,
            column: 25
          },
          end: {
            line: 484,
            column: 5
          }
        },
        line: 482
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 485,
            column: 4
          },
          end: {
            line: 485,
            column: 5
          }
        },
        loc: {
          start: {
            line: 485,
            column: 29
          },
          end: {
            line: 491,
            column: 5
          }
        },
        line: 485
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 487,
            column: 15
          },
          end: {
            line: 487,
            column: 16
          }
        },
        loc: {
          start: {
            line: 487,
            column: 21
          },
          end: {
            line: 490,
            column: 9
          }
        },
        line: 487
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 492,
            column: 4
          },
          end: {
            line: 492,
            column: 5
          }
        },
        loc: {
          start: {
            line: 492,
            column: 21
          },
          end: {
            line: 497,
            column: 5
          }
        },
        line: 492
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 498,
            column: 4
          },
          end: {
            line: 498,
            column: 5
          }
        },
        loc: {
          start: {
            line: 498,
            column: 19
          },
          end: {
            line: 502,
            column: 5
          }
        },
        line: 498
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 503,
            column: 4
          },
          end: {
            line: 503,
            column: 5
          }
        },
        loc: {
          start: {
            line: 503,
            column: 30
          },
          end: {
            line: 509,
            column: 5
          }
        },
        line: 503
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 506,
            column: 35
          },
          end: {
            line: 506,
            column: 36
          }
        },
        loc: {
          start: {
            line: 506,
            column: 49
          },
          end: {
            line: 506,
            column: 58
          }
        },
        line: 506
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 507,
            column: 40
          },
          end: {
            line: 507,
            column: 41
          }
        },
        loc: {
          start: {
            line: 507,
            column: 47
          },
          end: {
            line: 507,
            column: 70
          }
        },
        line: 507
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 508,
            column: 35
          },
          end: {
            line: 508,
            column: 36
          }
        },
        loc: {
          start: {
            line: 508,
            column: 50
          },
          end: {
            line: 508,
            column: 60
          }
        },
        line: 508
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 49,
            column: 8
          },
          end: {
            line: 54,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 49,
            column: 8
          },
          end: {
            line: 54,
            column: 9
          }
        }, {
          start: {
            line: 52,
            column: 13
          },
          end: {
            line: 54,
            column: 9
          }
        }],
        line: 49
      },
      "1": {
        loc: {
          start: {
            line: 70,
            column: 16
          },
          end: {
            line: 72,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 70,
            column: 16
          },
          end: {
            line: 72,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 70
      },
      "2": {
        loc: {
          start: {
            line: 75,
            column: 12
          },
          end: {
            line: 77,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 75,
            column: 12
          },
          end: {
            line: 77,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 75
      },
      "3": {
        loc: {
          start: {
            line: 122,
            column: 12
          },
          end: {
            line: 132,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 122,
            column: 12
          },
          end: {
            line: 132,
            column: 13
          }
        }, {
          start: {
            line: 127,
            column: 17
          },
          end: {
            line: 132,
            column: 13
          }
        }],
        line: 122
      },
      "4": {
        loc: {
          start: {
            line: 122,
            column: 16
          },
          end: {
            line: 122,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 122,
            column: 16
          },
          end: {
            line: 122,
            column: 39
          }
        }, {
          start: {
            line: 122,
            column: 43
          },
          end: {
            line: 122,
            column: 77
          }
        }],
        line: 122
      },
      "5": {
        loc: {
          start: {
            line: 129,
            column: 28
          },
          end: {
            line: 129,
            column: 101
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 129,
            column: 74
          },
          end: {
            line: 129,
            column: 97
          }
        }, {
          start: {
            line: 129,
            column: 100
          },
          end: {
            line: 129,
            column: 101
          }
        }],
        line: 129
      },
      "6": {
        loc: {
          start: {
            line: 130,
            column: 28
          },
          end: {
            line: 130,
            column: 101
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 130,
            column: 74
          },
          end: {
            line: 130,
            column: 97
          }
        }, {
          start: {
            line: 130,
            column: 100
          },
          end: {
            line: 130,
            column: 101
          }
        }],
        line: 130
      },
      "7": {
        loc: {
          start: {
            line: 156,
            column: 12
          },
          end: {
            line: 158,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 156,
            column: 12
          },
          end: {
            line: 158,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 156
      },
      "8": {
        loc: {
          start: {
            line: 165,
            column: 35
          },
          end: {
            line: 165,
            column: 76
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 165,
            column: 51
          },
          end: {
            line: 165,
            column: 63
          }
        }, {
          start: {
            line: 165,
            column: 66
          },
          end: {
            line: 165,
            column: 76
          }
        }],
        line: 165
      },
      "9": {
        loc: {
          start: {
            line: 168,
            column: 30
          },
          end: {
            line: 168,
            column: 63
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 168,
            column: 46
          },
          end: {
            line: 168,
            column: 53
          }
        }, {
          start: {
            line: 168,
            column: 56
          },
          end: {
            line: 168,
            column: 63
          }
        }],
        line: 168
      },
      "10": {
        loc: {
          start: {
            line: 169,
            column: 29
          },
          end: {
            line: 169,
            column: 95
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 169,
            column: 45
          },
          end: {
            line: 169,
            column: 91
          }
        }, {
          start: {
            line: 169,
            column: 94
          },
          end: {
            line: 169,
            column: 95
          }
        }],
        line: 169
      },
      "11": {
        loc: {
          start: {
            line: 175,
            column: 12
          },
          end: {
            line: 180,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 175,
            column: 12
          },
          end: {
            line: 180,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 175
      },
      "12": {
        loc: {
          start: {
            line: 175,
            column: 16
          },
          end: {
            line: 175,
            column: 86
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 175,
            column: 16
          },
          end: {
            line: 175,
            column: 34
          }
        }, {
          start: {
            line: 175,
            column: 38
          },
          end: {
            line: 175,
            column: 86
          }
        }],
        line: 175
      },
      "13": {
        loc: {
          start: {
            line: 202,
            column: 8
          },
          end: {
            line: 203,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 202,
            column: 8
          },
          end: {
            line: 203,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 202
      },
      "14": {
        loc: {
          start: {
            line: 211,
            column: 8
          },
          end: {
            line: 229,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 211,
            column: 8
          },
          end: {
            line: 229,
            column: 9
          }
        }, {
          start: {
            line: 226,
            column: 13
          },
          end: {
            line: 229,
            column: 9
          }
        }],
        line: 211
      },
      "15": {
        loc: {
          start: {
            line: 215,
            column: 12
          },
          end: {
            line: 224,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 215,
            column: 12
          },
          end: {
            line: 224,
            column: 13
          }
        }, {
          start: {
            line: 222,
            column: 17
          },
          end: {
            line: 224,
            column: 13
          }
        }],
        line: 215
      },
      "16": {
        loc: {
          start: {
            line: 218,
            column: 16
          },
          end: {
            line: 220,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 218,
            column: 16
          },
          end: {
            line: 220,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 218
      },
      "17": {
        loc: {
          start: {
            line: 247,
            column: 8
          },
          end: {
            line: 248,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 247,
            column: 8
          },
          end: {
            line: 248,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 247
      },
      "18": {
        loc: {
          start: {
            line: 257,
            column: 8
          },
          end: {
            line: 289,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 257,
            column: 8
          },
          end: {
            line: 289,
            column: 9
          }
        }, {
          start: {
            line: 266,
            column: 13
          },
          end: {
            line: 289,
            column: 9
          }
        }],
        line: 257
      },
      "19": {
        loc: {
          start: {
            line: 257,
            column: 12
          },
          end: {
            line: 257,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 257,
            column: 12
          },
          end: {
            line: 257,
            column: 35
          }
        }, {
          start: {
            line: 257,
            column: 39
          },
          end: {
            line: 257,
            column: 73
          }
        }],
        line: 257
      },
      "20": {
        loc: {
          start: {
            line: 266,
            column: 13
          },
          end: {
            line: 289,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 266,
            column: 13
          },
          end: {
            line: 289,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 266
      },
      "21": {
        loc: {
          start: {
            line: 268,
            column: 24
          },
          end: {
            line: 268,
            column: 97
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 268,
            column: 70
          },
          end: {
            line: 268,
            column: 93
          }
        }, {
          start: {
            line: 268,
            column: 96
          },
          end: {
            line: 268,
            column: 97
          }
        }],
        line: 268
      },
      "22": {
        loc: {
          start: {
            line: 269,
            column: 24
          },
          end: {
            line: 269,
            column: 97
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 269,
            column: 70
          },
          end: {
            line: 269,
            column: 93
          }
        }, {
          start: {
            line: 269,
            column: 96
          },
          end: {
            line: 269,
            column: 97
          }
        }],
        line: 269
      },
      "23": {
        loc: {
          start: {
            line: 272,
            column: 12
          },
          end: {
            line: 287,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 273,
            column: 16
          },
          end: {
            line: 276,
            column: 26
          }
        }, {
          start: {
            line: 277,
            column: 16
          },
          end: {
            line: 280,
            column: 26
          }
        }, {
          start: {
            line: 281,
            column: 16
          },
          end: {
            line: 284,
            column: 26
          }
        }, {
          start: {
            line: 285,
            column: 16
          },
          end: {
            line: 286,
            column: 116
          }
        }],
        line: 272
      },
      "24": {
        loc: {
          start: {
            line: 296,
            column: 8
          },
          end: {
            line: 298,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 296,
            column: 8
          },
          end: {
            line: 298,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 296
      },
      "25": {
        loc: {
          start: {
            line: 299,
            column: 8
          },
          end: {
            line: 308,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 300,
            column: 12
          },
          end: {
            line: 301,
            column: 55
          }
        }, {
          start: {
            line: 302,
            column: 12
          },
          end: {
            line: 303,
            column: 68
          }
        }, {
          start: {
            line: 304,
            column: 12
          },
          end: {
            line: 305,
            column: 101
          }
        }, {
          start: {
            line: 306,
            column: 12
          },
          end: {
            line: 307,
            column: 55
          }
        }],
        line: 299
      },
      "26": {
        loc: {
          start: {
            line: 315,
            column: 8
          },
          end: {
            line: 331,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 316,
            column: 12
          },
          end: {
            line: 318,
            column: 113
          }
        }, {
          start: {
            line: 319,
            column: 12
          },
          end: {
            line: 320,
            column: 102
          }
        }, {
          start: {
            line: 321,
            column: 12
          },
          end: {
            line: 322,
            column: 114
          }
        }, {
          start: {
            line: 323,
            column: 12
          },
          end: {
            line: 328,
            column: 98
          }
        }, {
          start: {
            line: 329,
            column: 12
          },
          end: {
            line: 330,
            column: 102
          }
        }],
        line: 315
      },
      "27": {
        loc: {
          start: {
            line: 327,
            column: 41
          },
          end: {
            line: 327,
            column: 92
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 327,
            column: 81
          },
          end: {
            line: 327,
            column: 85
          }
        }, {
          start: {
            line: 327,
            column: 88
          },
          end: {
            line: 327,
            column: 92
          }
        }],
        line: 327
      },
      "28": {
        loc: {
          start: {
            line: 337,
            column: 8
          },
          end: {
            line: 338,
            column: 24
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 337,
            column: 8
          },
          end: {
            line: 338,
            column: 24
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 337
      },
      "29": {
        loc: {
          start: {
            line: 340,
            column: 8
          },
          end: {
            line: 342,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 340,
            column: 8
          },
          end: {
            line: 342,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 340
      },
      "30": {
        loc: {
          start: {
            line: 344,
            column: 8
          },
          end: {
            line: 346,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 344,
            column: 8
          },
          end: {
            line: 346,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 344
      },
      "31": {
        loc: {
          start: {
            line: 348,
            column: 8
          },
          end: {
            line: 355,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 348,
            column: 8
          },
          end: {
            line: 355,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 348
      },
      "32": {
        loc: {
          start: {
            line: 352,
            column: 12
          },
          end: {
            line: 354,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 352,
            column: 12
          },
          end: {
            line: 354,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 352
      },
      "33": {
        loc: {
          start: {
            line: 362,
            column: 8
          },
          end: {
            line: 363,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 362,
            column: 8
          },
          end: {
            line: 363,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 362
      },
      "34": {
        loc: {
          start: {
            line: 364,
            column: 8
          },
          end: {
            line: 365,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 364,
            column: 8
          },
          end: {
            line: 365,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 364
      },
      "35": {
        loc: {
          start: {
            line: 366,
            column: 8
          },
          end: {
            line: 367,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 366,
            column: 8
          },
          end: {
            line: 367,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 366
      },
      "36": {
        loc: {
          start: {
            line: 370,
            column: 8
          },
          end: {
            line: 376,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 370,
            column: 8
          },
          end: {
            line: 376,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 370
      },
      "37": {
        loc: {
          start: {
            line: 373,
            column: 12
          },
          end: {
            line: 375,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 373,
            column: 12
          },
          end: {
            line: 375,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 373
      },
      "38": {
        loc: {
          start: {
            line: 406,
            column: 8
          },
          end: {
            line: 407,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 406,
            column: 8
          },
          end: {
            line: 407,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 406
      },
      "39": {
        loc: {
          start: {
            line: 409,
            column: 8
          },
          end: {
            line: 410,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 409,
            column: 8
          },
          end: {
            line: 410,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 409
      },
      "40": {
        loc: {
          start: {
            line: 413,
            column: 8
          },
          end: {
            line: 420,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 413,
            column: 8
          },
          end: {
            line: 420,
            column: 9
          }
        }, {
          start: {
            line: 417,
            column: 13
          },
          end: {
            line: 420,
            column: 9
          }
        }],
        line: 413
      },
      "41": {
        loc: {
          start: {
            line: 417,
            column: 13
          },
          end: {
            line: 420,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 417,
            column: 13
          },
          end: {
            line: 420,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 417
      },
      "42": {
        loc: {
          start: {
            line: 426,
            column: 8
          },
          end: {
            line: 427,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 426,
            column: 8
          },
          end: {
            line: 427,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 426
      },
      "43": {
        loc: {
          start: {
            line: 426,
            column: 12
          },
          end: {
            line: 426,
            column: 52
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 426,
            column: 12
          },
          end: {
            line: 426,
            column: 30
          }
        }, {
          start: {
            line: 426,
            column: 34
          },
          end: {
            line: 426,
            column: 52
          }
        }],
        line: 426
      },
      "44": {
        loc: {
          start: {
            line: 434,
            column: 34
          },
          end: {
            line: 434,
            column: 77
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 434,
            column: 72
          },
          end: {
            line: 434,
            column: 73
          }
        }, {
          start: {
            line: 434,
            column: 76
          },
          end: {
            line: 434,
            column: 77
          }
        }],
        line: 434
      },
      "45": {
        loc: {
          start: {
            line: 445,
            column: 29
          },
          end: {
            line: 445,
            column: 62
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 445,
            column: 29
          },
          end: {
            line: 445,
            column: 57
          }
        }, {
          start: {
            line: 445,
            column: 61
          },
          end: {
            line: 445,
            column: 62
          }
        }],
        line: 445
      },
      "46": {
        loc: {
          start: {
            line: 447,
            column: 34
          },
          end: {
            line: 447,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 447,
            column: 34
          },
          end: {
            line: 447,
            column: 62
          }
        }, {
          start: {
            line: 447,
            column: 66
          },
          end: {
            line: 447,
            column: 67
          }
        }],
        line: 447
      },
      "47": {
        loc: {
          start: {
            line: 454,
            column: 34
          },
          end: {
            line: 454,
            column: 69
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 454,
            column: 34
          },
          end: {
            line: 454,
            column: 64
          }
        }, {
          start: {
            line: 454,
            column: 68
          },
          end: {
            line: 454,
            column: 69
          }
        }],
        line: 454
      },
      "48": {
        loc: {
          start: {
            line: 455,
            column: 31
          },
          end: {
            line: 455,
            column: 68
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 455,
            column: 31
          },
          end: {
            line: 455,
            column: 63
          }
        }, {
          start: {
            line: 455,
            column: 67
          },
          end: {
            line: 455,
            column: 68
          }
        }],
        line: 455
      },
      "49": {
        loc: {
          start: {
            line: 456,
            column: 31
          },
          end: {
            line: 456,
            column: 68
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 456,
            column: 31
          },
          end: {
            line: 456,
            column: 63
          }
        }, {
          start: {
            line: 456,
            column: 67
          },
          end: {
            line: 456,
            column: 68
          }
        }],
        line: 456
      },
      "50": {
        loc: {
          start: {
            line: 457,
            column: 32
          },
          end: {
            line: 457,
            column: 70
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 457,
            column: 32
          },
          end: {
            line: 457,
            column: 65
          }
        }, {
          start: {
            line: 457,
            column: 69
          },
          end: {
            line: 457,
            column: 70
          }
        }],
        line: 457
      },
      "51": {
        loc: {
          start: {
            line: 458,
            column: 32
          },
          end: {
            line: 458,
            column: 70
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 458,
            column: 32
          },
          end: {
            line: 458,
            column: 65
          }
        }, {
          start: {
            line: 458,
            column: 69
          },
          end: {
            line: 458,
            column: 70
          }
        }],
        line: 458
      },
      "52": {
        loc: {
          start: {
            line: 459,
            column: 30
          },
          end: {
            line: 459,
            column: 66
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 459,
            column: 30
          },
          end: {
            line: 459,
            column: 61
          }
        }, {
          start: {
            line: 459,
            column: 65
          },
          end: {
            line: 459,
            column: 66
          }
        }],
        line: 459
      },
      "53": {
        loc: {
          start: {
            line: 504,
            column: 8
          },
          end: {
            line: 505,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 504,
            column: 8
          },
          end: {
            line: 505,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 504
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0,
      "210": 0,
      "211": 0,
      "212": 0,
      "213": 0,
      "214": 0,
      "215": 0,
      "216": 0,
      "217": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0, 0, 0],
      "24": [0, 0],
      "25": [0, 0, 0, 0],
      "26": [0, 0, 0, 0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\algorithms\\SimulatedAnnealing.ts",
      mappings: ";AAAA;;;;;;;;;;;;GAYG;;;AAEH,8EAa0C;AA8B1C;;GAEG;AACH,MAAa,kBAAkB;IAQ7B,YAAY,UAAkD;QANtD,iBAAY,GAAmB,IAAI,CAAC;QACpC,iBAAY,GAAgC,IAAI,CAAC;QACjD,YAAO,GAAuB,EAAE,CAAC;QAEjC,oBAAe,GAAW,CAAC,CAAC;QAGlC,IAAI,CAAC,UAAU,GAAG;YAChB,kBAAkB,EAAE,IAAI;YACxB,gBAAgB,EAAE,IAAI;YACtB,aAAa,EAAE,IAAI;YACnB,eAAe,EAAE;gBACf,kBAAkB,EAAE,IAAI;gBACxB,gBAAgB,EAAE,IAAI;gBACtB,WAAW,EAAE,IAAI;gBACjB,MAAM,EAAE,aAAa;aACtB;YACD,gBAAgB,EAAE,GAAG;YACrB,kBAAkB,EAAE,UAAU;YAC9B,mBAAmB,EAAE,YAAY;YACjC,kBAAkB,EAAE,SAAS;YAC7B,kBAAkB,EAAE,IAAI;YACxB,cAAc,EAAE,IAAI;YACpB,WAAW,EAAE,CAAC;YACd,kBAAkB,EAAE,GAAG;YACvB,oBAAoB,EAAE,IAAI;YAC1B,GAAG,UAAU;SACd,CAAC;QAEF,qCAAqC;QACrC,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;YAC5C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;QACnE,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC5B,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,QAAQ,CACnB,OAA4B,EAC5B,iBAAwC,EACxC,mBAA6C;QAE7C,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QAEpC,IAAI,CAAC;YACH,uBAAuB;YACvB,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;YAElC,0BAA0B;YAC1B,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,iBAAiB,EAAE,mBAAmB,CAAC,CAAC;YAElF,sBAAsB;YACtB,OAAO,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,CAAC;gBAC/B,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,iBAAiB,EAAE,mBAAmB,CAAC,CAAC;gBAC7E,IAAI,CAAC,aAAa,EAAE,CAAC;gBAErB,IAAI,IAAI,CAAC,UAAU,CAAC,oBAAoB,EAAE,CAAC;oBACzC,IAAI,CAAC,qBAAqB,EAAE,CAAC;gBAC/B,CAAC;YACH,CAAC;YAED,oBAAoB;YACpB,IAAI,IAAI,CAAC,aAAa,EAAE,EAAE,CAAC;gBACzB,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,iBAAiB,EAAE,mBAAmB,CAAC,CAAC;YACtE,CAAC;YAED,sBAAsB;YACtB,OAAO,IAAI,CAAC,wBAAwB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QAE3D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;YACjE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,OAA4B;QACtD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;QAClB,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;QAEzB,OAAO,CAAC,GAAG,CAAC,8DAA8D,IAAI,CAAC,UAAU,CAAC,kBAAkB,EAAE,CAAC,CAAC;IAClH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,qBAAqB,CACjC,OAA4B,EAC5B,iBAAwC,EACxC,mBAA6C;QAE7C,mCAAmC;QACnC,MAAM,eAAe,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QAC3D,MAAM,IAAI,CAAC,gBAAgB,CAAC,eAAe,EAAE,OAAO,EAAE,iBAAiB,EAAE,mBAAmB,CAAC,CAAC;QAE9F,mBAAmB;QACnB,IAAI,CAAC,YAAY,GAAG;YAClB,QAAQ,EAAE,eAAe;YACzB,WAAW,EAAE,IAAI,CAAC,UAAU,CAAC,kBAAkB;YAC/C,SAAS,EAAE,CAAC;YACZ,aAAa,EAAE,CAAC;YAChB,aAAa,EAAE,CAAC;YAChB,cAAc,EAAE,CAAC;YACjB,cAAc,EAAE,CAAC;YACjB,YAAY,EAAE,CAAC;SAChB,CAAC;QAEF,IAAI,CAAC,YAAY,GAAG,EAAE,GAAG,eAAe,EAAE,CAAC;IAC7C,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,OAA4B;QACvD,MAAM,SAAS,GAA8C,EAAE,CAAC;QAEhE,KAAK,MAAM,QAAQ,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACzC,IAAI,QAAQ,CAAC,cAAc,IAAI,QAAQ,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAClE,oBAAoB;gBACpB,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;gBAC/E,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;YAChE,CAAC;iBAAM,CAAC;gBACN,sBAAsB;gBACtB,MAAM,GAAG,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtF,MAAM,GAAG,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtF,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;YAC7D,CAAC;QACH,CAAC;QAED,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,kBAAkB,EAAE;YAC7B,SAAS;YACT,eAAe,EAAE,EAAE;YACnB,oBAAoB,EAAE,EAAE;YACxB,QAAQ,EAAE,IAAI;YACd,OAAO,EAAE,CAAC;YACV,mBAAmB,EAAE,OAAO,CAAC,mBAAmB;YAChD,kBAAkB,EAAE,EAAgC;SACrD,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAC5B,QAA8B,EAC9B,OAA4B,EAC5B,iBAAwC,EACxC,mBAA6C;QAE7C,IAAI,CAAC;YACH,6CAA6C;YAC7C,MAAM,SAAS,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;YAExE,8BAA8B;YAC9B,MAAM,cAAc,GAAG,iBAAiB,CAAC,SAAS,CAAC,CAAC;YACpD,QAAQ,CAAC,OAAO,GAAG,cAAc,CAAC;YAElC,yBAAyB;YACzB,IAAI,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC7C,QAAQ,CAAC,eAAe,CAAC,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC;YACjF,CAAC;YAED,uBAAuB;YACvB,QAAQ,CAAC,oBAAoB,GAAG,EAAE,CAAC;YACnC,KAAK,MAAM,kBAAkB,IAAI,mBAAmB,EAAE,CAAC;gBACrD,MAAM,SAAS,GAAG,kBAAkB,CAAC,SAAS,CAAC,CAAC;gBAChD,QAAQ,CAAC,oBAAoB,CAAC,IAAI,CAAC;oBACjC,YAAY,EAAE,cAAc,QAAQ,CAAC,oBAAoB,CAAC,MAAM,EAAE;oBAClE,aAAa,EAAE,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,UAAU;oBACxD,YAAY,EAAE,SAAS;oBACvB,aAAa,EAAE,CAAC;oBAChB,QAAQ,EAAE,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO;oBAC3C,OAAO,EAAE,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;iBAC5E,CAAC,CAAC;YACL,CAAC;YAED,oBAAoB;YACpB,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,YAAY,IAAI,CAAC,CAAC,CAAC;YAElF,4BAA4B;YAC5B,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,IAAI,CAAC,UAAU,CAAC,kBAAkB,KAAK,SAAS,EAAE,CAAC;gBAC3E,MAAM,YAAY,GAAG,QAAQ,CAAC,oBAAoB;qBAC/C,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,YAAY,GAAG,CAAC,CAAC;qBAC/B,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;gBAC1C,QAAQ,CAAC,OAAO,IAAI,YAAY,CAAC;YACnC,CAAC;YAED,IAAI,CAAC,eAAe,EAAE,CAAC;QAEzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACnD,QAAQ,CAAC,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC;YACpC,QAAQ,CAAC,QAAQ,GAAG,KAAK,CAAC;QAC5B,CAAC;IACH,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,QAA8B,EAAE,iBAAyC;QACnG,OAAO,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YACxC,GAAG,QAAQ;YACX,YAAY,EAAE,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC;SAC9C,CAAC,CAAC,CAAC;IACN,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAC5B,OAA4B,EAC5B,iBAAwC,EACxC,mBAA6C;QAE7C,IAAI,CAAC,IAAI,CAAC,YAAY;YAAE,OAAO;QAE/B,6BAA6B;QAC7B,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QACpF,MAAM,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,OAAO,EAAE,iBAAiB,EAAE,mBAAmB,CAAC,CAAC;QAE/F,mCAAmC;QACnC,MAAM,MAAM,GAAG,gBAAgB,CAAC,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC;QAC7E,MAAM,qBAAqB,GAAG,IAAI,CAAC,8BAA8B,CAAC,MAAM,EAAE,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;QAEzG,gCAAgC;QAChC,IAAI,IAAI,CAAC,MAAM,EAAE,GAAG,qBAAqB,EAAE,CAAC;YAC1C,sBAAsB;YACtB,IAAI,CAAC,YAAY,CAAC,QAAQ,GAAG,gBAAgB,CAAC;YAC9C,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE,CAAC;YAElC,IAAI,MAAM,GAAG,CAAC,EAAE,CAAC;gBACf,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC;gBAEnC,uBAAuB;gBACvB,IAAI,gBAAgB,CAAC,OAAO,GAAG,IAAI,CAAC,YAAa,CAAC,OAAO,EAAE,CAAC;oBAC1D,IAAI,CAAC,YAAY,GAAG,EAAE,GAAG,gBAAgB,EAAE,CAAC;gBAC9C,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC;YACrC,CAAC;QACH,CAAC;aAAM,CAAC;YACN,sBAAsB;YACtB,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE,CAAC;QACpC,CAAC;QAED,qBAAqB;QACrB,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QACnH,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC;IAChC,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,eAAqC,EAAE,OAA4B;QAC1F,MAAM,QAAQ,GAAyB;YACrC,GAAG,eAAe;YAClB,EAAE,EAAE,IAAI,CAAC,kBAAkB,EAAE;YAC7B,SAAS,EAAE,EAAE,GAAG,eAAe,CAAC,SAAS,EAAE;SAC5C,CAAC;QAEF,mCAAmC;QACnC,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QACpD,MAAM,gBAAgB,GAAG,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC;QACrF,MAAM,QAAQ,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,gBAAgB,CAAC,CAAC;QAExE,IAAI,CAAC,QAAQ;YAAE,OAAO,QAAQ,CAAC;QAE/B,uDAAuD;QACvD,QAAQ,CAAC,SAAS,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAC/D,QAAQ,CAAC,SAAS,CAAC,gBAAgB,CAAC,EACpC,QAAQ,CACT,CAAC;QAEF,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACK,qBAAqB,CAAC,YAA6B,EAAE,QAA8B;QACzF,IAAI,QAAQ,CAAC,cAAc,IAAI,QAAQ,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAClE,yDAAyD;YACzD,MAAM,YAAY,GAAG,QAAQ,CAAC,cAAc,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YACnE,MAAM,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,cAAc,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC,CAAC;YAEpH,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,YAAY,GAAG,gBAAgB,CAAC,CAAC;YAC9D,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,YAAY,GAAG,gBAAgB,CAAC,CAAC;YAE/F,MAAM,WAAW,GAAG,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,QAAQ,GAAG,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC;YACrF,OAAO,QAAQ,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;QAC9C,CAAC;aAAM,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE,CAAC;YAC5C,sBAAsB;YACtB,MAAM,GAAG,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;YACtF,MAAM,GAAG,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;YACtF,MAAM,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC;YAExB,IAAI,aAAqB,CAAC;YAE1B,QAAQ,IAAI,CAAC,UAAU,CAAC,kBAAkB,EAAE,CAAC;gBAC3C,KAAK,UAAU;oBACb,MAAM,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC;oBACvD,aAAa,GAAG,YAAY,GAAG,IAAI,CAAC,cAAc,EAAE,GAAG,KAAK,CAAC;oBAC7D,MAAM;gBACR,KAAK,SAAS;oBACZ,MAAM,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC;oBACvD,aAAa,GAAG,YAAY,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;oBACjE,MAAM;gBACR,KAAK,QAAQ;oBACX,MAAM,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC;oBACvD,aAAa,GAAG,YAAY,GAAG,IAAI,CAAC,YAAY,EAAE,GAAG,KAAK,CAAC;oBAC3D,MAAM;gBACR;oBACE,aAAa,GAAG,YAAY,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC;YACpG,CAAC;YAED,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,aAAa,CAAC,CAAC,CAAC;QACrD,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;OAEG;IACK,8BAA8B,CAAC,MAAc,EAAE,WAAmB;QACxE,IAAI,MAAM,IAAI,CAAC,EAAE,CAAC;YAChB,OAAO,GAAG,CAAC,CAAC,oCAAoC;QAClD,CAAC;QAED,QAAQ,IAAI,CAAC,UAAU,CAAC,mBAAmB,EAAE,CAAC;YAC5C,KAAK,YAAY;gBACf,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,WAAW,CAAC,CAAC;YACzC,KAAK,WAAW;gBACd,OAAO,GAAG,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC;YACtD,KAAK,gBAAgB;gBACnB,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,YAAa,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACxF;gBACE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,WAAW,CAAC,CAAC;QAC3C,CAAC;IACH,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,kBAA0B,EAAE,SAAiB;QACrE,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC;QAEjD,QAAQ,QAAQ,CAAC,MAAM,EAAE,CAAC;YACxB,KAAK,QAAQ;gBACX,MAAM,UAAU,GAAG,CAAC,QAAQ,CAAC,kBAAkB,GAAG,QAAQ,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC;gBAC7G,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,gBAAgB,EAAE,QAAQ,CAAC,kBAAkB,GAAG,SAAS,GAAG,UAAU,CAAC,CAAC;YAEnG,KAAK,aAAa;gBAChB,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,gBAAgB,EAAE,kBAAkB,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC;YAExF,KAAK,aAAa;gBAChB,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,gBAAgB,EAAE,QAAQ,CAAC,kBAAkB,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;YAEpG,KAAK,UAAU;gBACb,4CAA4C;gBAC5C,MAAM,cAAc,GAAG,IAAI,CAAC,YAAa,CAAC,aAAa,GAAG,CAAC,IAAI,CAAC,YAAa,CAAC,aAAa,GAAG,IAAI,CAAC,YAAa,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC;gBACpI,MAAM,oBAAoB,GAAG,GAAG,CAAC;gBACjC,MAAM,gBAAgB,GAAG,cAAc,GAAG,oBAAoB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;gBAC7E,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,gBAAgB,EAAE,kBAAkB,GAAG,gBAAgB,CAAC,CAAC;YAEpF;gBACE,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,gBAAgB,EAAE,kBAAkB,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC;QAC1F,CAAC;IACH,CAAC;IAED;;OAEG;IACK,eAAe;QACrB,IAAI,CAAC,IAAI,CAAC,YAAY;YAAE,OAAO,IAAI,CAAC;QAEpC,qBAAqB;QACrB,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC;YACjE,OAAO,IAAI,CAAC;QACd,CAAC;QAED,wBAAwB;QACxB,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,IAAI,IAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC;YACtE,OAAO,IAAI,CAAC;QACd,CAAC;QAED,0DAA0D;QAC1D,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,EAAE,EAAE,CAAC;YAC9B,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;YAC9C,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;YACtE,MAAM,WAAW,GAAG,IAAI,CAAC,YAAa,CAAC,OAAO,GAAG,UAAU,CAAC;YAE5D,IAAI,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,GAAG,IAAI,EAAE,CAAC;gBACjC,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACK,aAAa;QACnB,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc;YAAE,OAAO,KAAK,CAAC;QAClD,IAAI,CAAC,IAAI,CAAC,YAAY;YAAE,OAAO,KAAK,CAAC;QACrC,IAAI,IAAI,CAAC,YAAY,CAAC,YAAY,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW;YAAE,OAAO,KAAK,CAAC;QAEhF,oCAAoC;QACpC,MAAM,gBAAgB,GAAG,GAAG,CAAC;QAC7B,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,gBAAgB,EAAE,CAAC;YAC5C,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,gBAAgB,CAAC,CAAC;YAC5D,MAAM,eAAe,GAAG,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;YAEtF,IAAI,eAAe,GAAG,IAAI,EAAE,CAAC;gBAC3B,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,OAAO,CACnB,OAA4B,EAC5B,iBAAwC,EACxC,mBAA6C;QAE7C,OAAO,CAAC,GAAG,CAAC,2CAA2C,IAAI,CAAC,YAAa,CAAC,YAAY,GAAG,CAAC,GAAG,CAAC,CAAC;QAE/F,6BAA6B;QAC7B,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QACvD,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,OAAO,EAAE,iBAAiB,EAAE,mBAAmB,CAAC,CAAC;QAE1F,uCAAuC;QACvC,IAAI,CAAC,YAAa,CAAC,QAAQ,GAAG,WAAW,CAAC;QAC1C,IAAI,CAAC,YAAa,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC;QACpE,IAAI,CAAC,YAAa,CAAC,SAAS,GAAG,CAAC,CAAC;QACjC,IAAI,CAAC,YAAa,CAAC,aAAa,GAAG,CAAC,CAAC;QACrC,IAAI,CAAC,YAAa,CAAC,aAAa,GAAG,CAAC,CAAC;QACrC,IAAI,CAAC,YAAa,CAAC,cAAc,GAAG,CAAC,CAAC;QACtC,IAAI,CAAC,YAAa,CAAC,cAAc,GAAG,CAAC,CAAC;QACtC,IAAI,CAAC,YAAa,CAAC,YAAY,EAAE,CAAC;QAElC,wBAAwB;QACxB,OAAO,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,CAAC;YAC/B,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,iBAAiB,EAAE,mBAAmB,CAAC,CAAC;YAC7E,IAAI,CAAC,aAAa,EAAE,CAAC;QACvB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,qBAAqB;QAC3B,IAAI,CAAC,IAAI,CAAC,YAAY;YAAE,OAAO;QAE/B,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC;QACrF,IAAI,UAAU,GAAG,EAAE;YAAE,OAAO;QAE5B,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,GAAG,UAAU,CAAC;QACpE,MAAM,oBAAoB,GAAG,GAAG,CAAC;QAEjC,IAAI,cAAc,GAAG,oBAAoB,GAAG,GAAG,EAAE,CAAC;YAChD,oDAAoD;YACpD,IAAI,CAAC,UAAU,CAAC,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,UAAU,CAAC,gBAAgB,GAAG,GAAG,CAAC,CAAC;QAC3F,CAAC;aAAM,IAAI,cAAc,GAAG,oBAAoB,GAAG,GAAG,EAAE,CAAC;YACvD,mDAAmD;YACnD,IAAI,CAAC,UAAU,CAAC,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,gBAAgB,GAAG,GAAG,CAAC,CAAC;QAC5F,CAAC;IACH,CAAC;IAED;;OAEG;IACK,aAAa;QACnB,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,YAAY;YAAE,OAAO;QAErD,MAAM,OAAO,GAAqB;YAChC,SAAS,EAAE,IAAI,CAAC,YAAY,CAAC,SAAS;YACtC,WAAW,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO;YACtC,cAAc,EAAE,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,OAAO;YAClD,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,OAAO;YAChD,SAAS,EAAE,CAAC,EAAE,qCAAqC;YACnD,oBAAoB,EAAE,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjE,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACK,wBAAwB,CAAC,OAA4B,EAAE,SAAiB;QAC9E,MAAM,aAAa,GAAG,WAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;QAEpD,MAAM,UAAU,GAA2B;YACzC,eAAe,EAAE,IAAI,CAAC,YAAY,EAAE,SAAS,IAAI,CAAC;YAClD,gBAAgB,EAAE,IAAI,CAAC,eAAe;YACtC,oBAAoB,EAAE,IAAI,CAAC,YAAY,EAAE,SAAS,IAAI,CAAC;YACvD,aAAa;YACb,kBAAkB,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC;YACxD,qBAAqB,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,cAAc,CAAC;YAC9D,gBAAgB,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;YACpD,0BAA0B,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,oBAAoB,CAAC;YACzE,sBAAsB,EAAE;gBACtB,gBAAgB,EAAE,IAAI,CAAC,YAAY,EAAE,WAAW,IAAI,CAAC;gBACrD,aAAa,EAAE,IAAI,CAAC,YAAY,EAAE,aAAa,IAAI,CAAC;gBACpD,aAAa,EAAE,IAAI,CAAC,YAAY,EAAE,aAAa,IAAI,CAAC;gBACpD,cAAc,EAAE,IAAI,CAAC,YAAY,EAAE,cAAc,IAAI,CAAC;gBACtD,cAAc,EAAE,IAAI,CAAC,YAAY,EAAE,cAAc,IAAI,CAAC;gBACtD,YAAY,EAAE,IAAI,CAAC,YAAY,EAAE,YAAY,IAAI,CAAC;gBAClD,qBAAqB,EAAE,IAAI,CAAC,UAAU,CAAC,gBAAgB;aACxD;SACF,CAAC;QAEF,MAAM,mBAAmB,GAAwB;YAC/C,UAAU,EAAE,IAAI,CAAC,OAAO;YACxB,iBAAiB,EAAE,EAAE;YACrB,gBAAgB,EAAE,EAAE;YACpB,kBAAkB,EAAE,EAAE;SACvB,CAAC;QAEF,OAAO;YACL,SAAS,EAAE,OAAO,CAAC,EAAE;YACrB,MAAM,EAAE,4CAAkB,CAAC,SAAS;YACpC,YAAY,EAAE,IAAI,CAAC,YAAa;YAChC,UAAU;YACV,OAAO,EAAE,mBAAmB;YAC5B,QAAQ,EAAE,EAAE;YACZ,eAAe,EAAE,EAAE;YACnB,QAAQ,EAAE,EAAE;YACZ,MAAM,EAAE,EAAE;SACX,CAAC;IACJ,CAAC;IAED,kBAAkB;IACV,kBAAkB;QACxB,OAAO,UAAU,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IAC3E,CAAC;IAEO,kBAAkB,CAAC,IAAY;QACrC,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,OAAO,GAAG,EAAE;YACV,KAAK,GAAG,CAAC,KAAK,GAAG,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC;YACxC,OAAO,KAAK,GAAG,MAAM,CAAC;QACxB,CAAC,CAAC;IACJ,CAAC;IAEO,cAAc;QACpB,uBAAuB;QACvB,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;QACzB,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;QACzB,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;IACnE,CAAC;IAEO,YAAY;QAClB,8CAA8C;QAC9C,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;QACxB,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;IACvC,CAAC;IAEO,iBAAiB,CAAC,MAAgB;QACxC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAElC,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;QACvE,MAAM,YAAY,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;QAChE,OAAO,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;IAC3E,CAAC;CACF;AAtkBD,gDAskBC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\algorithms\\SimulatedAnnealing.ts"],
      sourcesContent: ["/**\r\n * Simulated Annealing Algorithm Implementation for System Optimization\r\n * \r\n * Implements simulated annealing optimization with:\r\n * - Configurable cooling schedules (linear, exponential, logarithmic, adaptive)\r\n * - Multiple neighborhood generation strategies\r\n * - Constraint handling with penalty methods\r\n * - Adaptive parameter adjustment\r\n * - Restart mechanisms for global optimization\r\n * \r\n * @version 3.0.0\r\n * <AUTHOR> Suite Development Team\r\n */\r\n\r\nimport {\r\n  OptimizationSolution,\r\n  OptimizationVariable,\r\n  OptimizationProblem,\r\n  OptimizationResult,\r\n  OptimizationStatus,\r\n  OptimizationStatistics,\r\n  OptimizationHistory,\r\n  IterationHistory,\r\n  SolutionPerformanceMetrics,\r\n  ObjectiveFunctionType,\r\n  ConstraintFunctionType,\r\n  CoolingSchedule\r\n} from '../types/SystemOptimizationTypes';\r\n\r\nexport interface SimulatedAnnealingParameters {\r\n  initialTemperature: number;\r\n  finalTemperature: number;\r\n  maxIterations: number;\r\n  coolingSchedule: CoolingSchedule;\r\n  neighborhoodSize: number;\r\n  neighborhoodMethod: 'gaussian' | 'uniform' | 'adaptive' | 'cauchy';\r\n  acceptanceCriterion: 'metropolis' | 'boltzmann' | 'fast_annealing';\r\n  constraintHandling: 'penalty' | 'repair' | 'rejection';\r\n  penaltyCoefficient: number;\r\n  restartEnabled: boolean;\r\n  maxRestarts: number;\r\n  restartTemperature: number;\r\n  adaptiveNeighborhood: boolean;\r\n  seedValue?: number;\r\n}\r\n\r\nexport interface SAState {\r\n  solution: OptimizationSolution;\r\n  temperature: number;\r\n  iteration: number;\r\n  acceptedMoves: number;\r\n  rejectedMoves: number;\r\n  improvingMoves: number;\r\n  worseningMoves: number;\r\n  restartCount: number;\r\n}\r\n\r\n/**\r\n * Simulated Annealing optimizer for single-objective optimization problems\r\n */\r\nexport class SimulatedAnnealing {\r\n  private parameters: SimulatedAnnealingParameters;\r\n  private currentState: SAState | null = null;\r\n  private bestSolution: OptimizationSolution | null = null;\r\n  private history: IterationHistory[] = [];\r\n  private random: () => number;\r\n  private evaluationCount: number = 0;\r\n\r\n  constructor(parameters?: Partial<SimulatedAnnealingParameters>) {\r\n    this.parameters = {\r\n      initialTemperature: 1000,\r\n      finalTemperature: 0.01,\r\n      maxIterations: 1000,\r\n      coolingSchedule: {\r\n        initialTemperature: 1000,\r\n        finalTemperature: 0.01,\r\n        coolingRate: 0.95,\r\n        method: 'exponential'\r\n      },\r\n      neighborhoodSize: 0.1,\r\n      neighborhoodMethod: 'gaussian',\r\n      acceptanceCriterion: 'metropolis',\r\n      constraintHandling: 'penalty',\r\n      penaltyCoefficient: 1000,\r\n      restartEnabled: true,\r\n      maxRestarts: 3,\r\n      restartTemperature: 100,\r\n      adaptiveNeighborhood: true,\r\n      ...parameters\r\n    };\r\n\r\n    // Initialize random number generator\r\n    if (this.parameters.seedValue !== undefined) {\r\n      this.random = this.createSeededRandom(this.parameters.seedValue);\r\n    } else {\r\n      this.random = Math.random;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Main optimization method\r\n   */\r\n  public async optimize(\r\n    problem: OptimizationProblem,\r\n    objectiveFunction: ObjectiveFunctionType,\r\n    constraintFunctions: ConstraintFunctionType[]\r\n  ): Promise<OptimizationResult> {\r\n    const startTime = performance.now();\r\n    \r\n    try {\r\n      // Initialize algorithm\r\n      this.initializeAlgorithm(problem);\r\n      \r\n      // Create initial solution\r\n      await this.createInitialSolution(problem, objectiveFunction, constraintFunctions);\r\n      \r\n      // Main annealing loop\r\n      while (!this.shouldTerminate()) {\r\n        await this.performIteration(problem, objectiveFunction, constraintFunctions);\r\n        this.updateHistory();\r\n        \r\n        if (this.parameters.adaptiveNeighborhood) {\r\n          this.adaptNeighborhoodSize();\r\n        }\r\n      }\r\n      \r\n      // Check for restart\r\n      if (this.shouldRestart()) {\r\n        await this.restart(problem, objectiveFunction, constraintFunctions);\r\n      }\r\n      \r\n      // Create final result\r\n      return this.createOptimizationResult(problem, startTime);\r\n      \r\n    } catch (error) {\r\n      console.error('Simulated annealing optimization failed:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Initialize algorithm state\r\n   */\r\n  private initializeAlgorithm(problem: OptimizationProblem): void {\r\n    this.currentState = null;\r\n    this.bestSolution = null;\r\n    this.history = [];\r\n    this.evaluationCount = 0;\r\n    \r\n    console.log(`Initializing Simulated Annealing with initial temperature: ${this.parameters.initialTemperature}`);\r\n  }\r\n\r\n  /**\r\n   * Create initial solution\r\n   */\r\n  private async createInitialSolution(\r\n    problem: OptimizationProblem,\r\n    objectiveFunction: ObjectiveFunctionType,\r\n    constraintFunctions: ConstraintFunctionType[]\r\n  ): Promise<void> {\r\n    // Generate random initial solution\r\n    const initialSolution = this.createRandomSolution(problem);\r\n    await this.evaluateSolution(initialSolution, problem, objectiveFunction, constraintFunctions);\r\n    \r\n    // Initialize state\r\n    this.currentState = {\r\n      solution: initialSolution,\r\n      temperature: this.parameters.initialTemperature,\r\n      iteration: 0,\r\n      acceptedMoves: 0,\r\n      rejectedMoves: 0,\r\n      improvingMoves: 0,\r\n      worseningMoves: 0,\r\n      restartCount: 0\r\n    };\r\n    \r\n    this.bestSolution = { ...initialSolution };\r\n  }\r\n\r\n  /**\r\n   * Create a random solution\r\n   */\r\n  private createRandomSolution(problem: OptimizationProblem): OptimizationSolution {\r\n    const variables: { [variableId: string]: number | string } = {};\r\n    \r\n    for (const variable of problem.variables) {\r\n      if (variable.discreteValues && variable.discreteValues.length > 0) {\r\n        // Discrete variable\r\n        const randomIndex = Math.floor(this.random() * variable.discreteValues.length);\r\n        variables[variable.id] = variable.discreteValues[randomIndex];\r\n      } else {\r\n        // Continuous variable\r\n        const min = typeof variable.bounds.minimum === 'number' ? variable.bounds.minimum : 0;\r\n        const max = typeof variable.bounds.maximum === 'number' ? variable.bounds.maximum : 1;\r\n        variables[variable.id] = min + this.random() * (max - min);\r\n      }\r\n    }\r\n    \r\n    return {\r\n      id: this.generateSolutionId(),\r\n      variables,\r\n      objectiveValues: {},\r\n      constraintViolations: [],\r\n      feasible: true,\r\n      fitness: 0,\r\n      systemConfiguration: problem.systemConfiguration,\r\n      performanceMetrics: {} as SolutionPerformanceMetrics\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Evaluate solution fitness and constraints\r\n   */\r\n  private async evaluateSolution(\r\n    solution: OptimizationSolution,\r\n    problem: OptimizationProblem,\r\n    objectiveFunction: ObjectiveFunctionType,\r\n    constraintFunctions: ConstraintFunctionType[]\r\n  ): Promise<void> {\r\n    try {\r\n      // Convert solution to optimization variables\r\n      const variables = this.solutionToVariables(solution, problem.variables);\r\n      \r\n      // Evaluate objective function\r\n      const objectiveValue = objectiveFunction(variables);\r\n      solution.fitness = objectiveValue;\r\n      \r\n      // Store objective values\r\n      if (problem.objectives.objectives.length > 0) {\r\n        solution.objectiveValues[problem.objectives.objectives[0].id] = objectiveValue;\r\n      }\r\n      \r\n      // Evaluate constraints\r\n      solution.constraintViolations = [];\r\n      for (const constraintFunction of constraintFunctions) {\r\n        const violation = constraintFunction(variables);\r\n        solution.constraintViolations.push({\r\n          constraintId: `constraint_${solution.constraintViolations.length}`,\r\n          violationType: violation > 0 ? 'inequality' : 'boundary',\r\n          currentValue: violation,\r\n          requiredValue: 0,\r\n          severity: violation > 0 ? 'major' : 'minor',\r\n          penalty: violation > 0 ? violation * this.parameters.penaltyCoefficient : 0\r\n        });\r\n      }\r\n      \r\n      // Check feasibility\r\n      solution.feasible = solution.constraintViolations.every(v => v.currentValue <= 0);\r\n      \r\n      // Apply constraint handling\r\n      if (!solution.feasible && this.parameters.constraintHandling === 'penalty') {\r\n        const totalPenalty = solution.constraintViolations\r\n          .filter(v => v.currentValue > 0)\r\n          .reduce((sum, v) => sum + v.penalty, 0);\r\n        solution.fitness += totalPenalty;\r\n      }\r\n      \r\n      this.evaluationCount++;\r\n      \r\n    } catch (error) {\r\n      console.error('Error evaluating solution:', error);\r\n      solution.fitness = Number.MAX_VALUE;\r\n      solution.feasible = false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Convert solution to optimization variables\r\n   */\r\n  private solutionToVariables(solution: OptimizationSolution, variableTemplates: OptimizationVariable[]): OptimizationVariable[] {\r\n    return variableTemplates.map(template => ({\r\n      ...template,\r\n      currentValue: solution.variables[template.id]\r\n    }));\r\n  }\r\n\r\n  /**\r\n   * Perform one iteration of simulated annealing\r\n   */\r\n  private async performIteration(\r\n    problem: OptimizationProblem,\r\n    objectiveFunction: ObjectiveFunctionType,\r\n    constraintFunctions: ConstraintFunctionType[]\r\n  ): Promise<void> {\r\n    if (!this.currentState) return;\r\n    \r\n    // Generate neighbor solution\r\n    const neighborSolution = this.generateNeighbor(this.currentState.solution, problem);\r\n    await this.evaluateSolution(neighborSolution, problem, objectiveFunction, constraintFunctions);\r\n    \r\n    // Calculate acceptance probability\r\n    const deltaE = neighborSolution.fitness - this.currentState.solution.fitness;\r\n    const acceptanceProbability = this.calculateAcceptanceProbability(deltaE, this.currentState.temperature);\r\n    \r\n    // Accept or reject the neighbor\r\n    if (this.random() < acceptanceProbability) {\r\n      // Accept the neighbor\r\n      this.currentState.solution = neighborSolution;\r\n      this.currentState.acceptedMoves++;\r\n      \r\n      if (deltaE < 0) {\r\n        this.currentState.improvingMoves++;\r\n        \r\n        // Update best solution\r\n        if (neighborSolution.fitness < this.bestSolution!.fitness) {\r\n          this.bestSolution = { ...neighborSolution };\r\n        }\r\n      } else {\r\n        this.currentState.worseningMoves++;\r\n      }\r\n    } else {\r\n      // Reject the neighbor\r\n      this.currentState.rejectedMoves++;\r\n    }\r\n    \r\n    // Update temperature\r\n    this.currentState.temperature = this.updateTemperature(this.currentState.temperature, this.currentState.iteration);\r\n    this.currentState.iteration++;\r\n  }\r\n\r\n  /**\r\n   * Generate neighbor solution\r\n   */\r\n  private generateNeighbor(currentSolution: OptimizationSolution, problem: OptimizationProblem): OptimizationSolution {\r\n    const neighbor: OptimizationSolution = {\r\n      ...currentSolution,\r\n      id: this.generateSolutionId(),\r\n      variables: { ...currentSolution.variables }\r\n    };\r\n    \r\n    // Select random variable to modify\r\n    const variableIds = Object.keys(neighbor.variables);\r\n    const randomVariableId = variableIds[Math.floor(this.random() * variableIds.length)];\r\n    const variable = problem.variables.find(v => v.id === randomVariableId);\r\n    \r\n    if (!variable) return neighbor;\r\n    \r\n    // Generate neighbor value based on neighborhood method\r\n    neighbor.variables[randomVariableId] = this.generateNeighborValue(\r\n      neighbor.variables[randomVariableId],\r\n      variable\r\n    );\r\n    \r\n    return neighbor;\r\n  }\r\n\r\n  /**\r\n   * Generate neighbor value for a variable\r\n   */\r\n  private generateNeighborValue(currentValue: number | string, variable: OptimizationVariable): number | string {\r\n    if (variable.discreteValues && variable.discreteValues.length > 0) {\r\n      // Discrete variable - random selection from neighborhood\r\n      const currentIndex = variable.discreteValues.indexOf(currentValue);\r\n      const neighborhoodSize = Math.max(1, Math.floor(variable.discreteValues.length * this.parameters.neighborhoodSize));\r\n      \r\n      const minIndex = Math.max(0, currentIndex - neighborhoodSize);\r\n      const maxIndex = Math.min(variable.discreteValues.length - 1, currentIndex + neighborhoodSize);\r\n      \r\n      const randomIndex = minIndex + Math.floor(this.random() * (maxIndex - minIndex + 1));\r\n      return variable.discreteValues[randomIndex];\r\n    } else if (typeof currentValue === 'number') {\r\n      // Continuous variable\r\n      const min = typeof variable.bounds.minimum === 'number' ? variable.bounds.minimum : 0;\r\n      const max = typeof variable.bounds.maximum === 'number' ? variable.bounds.maximum : 1;\r\n      const range = max - min;\r\n      \r\n      let neighborValue: number;\r\n      \r\n      switch (this.parameters.neighborhoodMethod) {\r\n        case 'gaussian':\r\n          const sigma = range * this.parameters.neighborhoodSize;\r\n          neighborValue = currentValue + this.gaussianRandom() * sigma;\r\n          break;\r\n        case 'uniform':\r\n          const delta = range * this.parameters.neighborhoodSize;\r\n          neighborValue = currentValue + (this.random() - 0.5) * 2 * delta;\r\n          break;\r\n        case 'cauchy':\r\n          const gamma = range * this.parameters.neighborhoodSize;\r\n          neighborValue = currentValue + this.cauchyRandom() * gamma;\r\n          break;\r\n        default:\r\n          neighborValue = currentValue + (this.random() - 0.5) * range * this.parameters.neighborhoodSize;\r\n      }\r\n      \r\n      return Math.max(min, Math.min(max, neighborValue));\r\n    }\r\n    \r\n    return currentValue;\r\n  }\r\n\r\n  /**\r\n   * Calculate acceptance probability\r\n   */\r\n  private calculateAcceptanceProbability(deltaE: number, temperature: number): number {\r\n    if (deltaE <= 0) {\r\n      return 1.0; // Always accept improving solutions\r\n    }\r\n    \r\n    switch (this.parameters.acceptanceCriterion) {\r\n      case 'metropolis':\r\n        return Math.exp(-deltaE / temperature);\r\n      case 'boltzmann':\r\n        return 1.0 / (1.0 + Math.exp(deltaE / temperature));\r\n      case 'fast_annealing':\r\n        return Math.exp(-deltaE / (temperature * Math.log(this.currentState!.iteration + 2)));\r\n      default:\r\n        return Math.exp(-deltaE / temperature);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update temperature according to cooling schedule\r\n   */\r\n  private updateTemperature(currentTemperature: number, iteration: number): number {\r\n    const schedule = this.parameters.coolingSchedule;\r\n    \r\n    switch (schedule.method) {\r\n      case 'linear':\r\n        const linearRate = (schedule.initialTemperature - schedule.finalTemperature) / this.parameters.maxIterations;\r\n        return Math.max(schedule.finalTemperature, schedule.initialTemperature - iteration * linearRate);\r\n        \r\n      case 'exponential':\r\n        return Math.max(schedule.finalTemperature, currentTemperature * schedule.coolingRate);\r\n        \r\n      case 'logarithmic':\r\n        return Math.max(schedule.finalTemperature, schedule.initialTemperature / Math.log(iteration + 2));\r\n        \r\n      case 'adaptive':\r\n        // Adaptive cooling based on acceptance rate\r\n        const acceptanceRate = this.currentState!.acceptedMoves / (this.currentState!.acceptedMoves + this.currentState!.rejectedMoves + 1);\r\n        const targetAcceptanceRate = 0.4;\r\n        const adaptationFactor = acceptanceRate > targetAcceptanceRate ? 0.99 : 0.95;\r\n        return Math.max(schedule.finalTemperature, currentTemperature * adaptationFactor);\r\n        \r\n      default:\r\n        return Math.max(schedule.finalTemperature, currentTemperature * schedule.coolingRate);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Check termination criteria\r\n   */\r\n  private shouldTerminate(): boolean {\r\n    if (!this.currentState) return true;\r\n    \r\n    // Maximum iterations\r\n    if (this.currentState.iteration >= this.parameters.maxIterations) {\r\n      return true;\r\n    }\r\n    \r\n    // Temperature threshold\r\n    if (this.currentState.temperature <= this.parameters.finalTemperature) {\r\n      return true;\r\n    }\r\n    \r\n    // Convergence check (no improvement in recent iterations)\r\n    if (this.history.length >= 50) {\r\n      const recentHistory = this.history.slice(-50);\r\n      const bestRecent = Math.min(...recentHistory.map(h => h.bestFitness));\r\n      const improvement = this.bestSolution!.fitness - bestRecent;\r\n      \r\n      if (Math.abs(improvement) < 1e-6) {\r\n        return true;\r\n      }\r\n    }\r\n    \r\n    return false;\r\n  }\r\n\r\n  /**\r\n   * Check if restart should be performed\r\n   */\r\n  private shouldRestart(): boolean {\r\n    if (!this.parameters.restartEnabled) return false;\r\n    if (!this.currentState) return false;\r\n    if (this.currentState.restartCount >= this.parameters.maxRestarts) return false;\r\n    \r\n    // Restart if stuck in local minimum\r\n    const recentIterations = 100;\r\n    if (this.history.length >= recentIterations) {\r\n      const recentHistory = this.history.slice(-recentIterations);\r\n      const fitnessVariance = this.calculateVariance(recentHistory.map(h => h.bestFitness));\r\n      \r\n      if (fitnessVariance < 1e-6) {\r\n        return true;\r\n      }\r\n    }\r\n    \r\n    return false;\r\n  }\r\n\r\n  /**\r\n   * Restart the algorithm from a new random solution\r\n   */\r\n  private async restart(\r\n    problem: OptimizationProblem,\r\n    objectiveFunction: ObjectiveFunctionType,\r\n    constraintFunctions: ConstraintFunctionType[]\r\n  ): Promise<void> {\r\n    console.log(`Restarting simulated annealing (restart ${this.currentState!.restartCount + 1})`);\r\n    \r\n    // Create new random solution\r\n    const newSolution = this.createRandomSolution(problem);\r\n    await this.evaluateSolution(newSolution, problem, objectiveFunction, constraintFunctions);\r\n    \r\n    // Reset state with restart temperature\r\n    this.currentState!.solution = newSolution;\r\n    this.currentState!.temperature = this.parameters.restartTemperature;\r\n    this.currentState!.iteration = 0;\r\n    this.currentState!.acceptedMoves = 0;\r\n    this.currentState!.rejectedMoves = 0;\r\n    this.currentState!.improvingMoves = 0;\r\n    this.currentState!.worseningMoves = 0;\r\n    this.currentState!.restartCount++;\r\n    \r\n    // Continue optimization\r\n    while (!this.shouldTerminate()) {\r\n      await this.performIteration(problem, objectiveFunction, constraintFunctions);\r\n      this.updateHistory();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Adapt neighborhood size based on acceptance rate\r\n   */\r\n  private adaptNeighborhoodSize(): void {\r\n    if (!this.currentState) return;\r\n    \r\n    const totalMoves = this.currentState.acceptedMoves + this.currentState.rejectedMoves;\r\n    if (totalMoves < 10) return;\r\n    \r\n    const acceptanceRate = this.currentState.acceptedMoves / totalMoves;\r\n    const targetAcceptanceRate = 0.4;\r\n    \r\n    if (acceptanceRate > targetAcceptanceRate + 0.1) {\r\n      // Too many acceptances - increase neighborhood size\r\n      this.parameters.neighborhoodSize = Math.min(0.5, this.parameters.neighborhoodSize * 1.1);\r\n    } else if (acceptanceRate < targetAcceptanceRate - 0.1) {\r\n      // Too few acceptances - decrease neighborhood size\r\n      this.parameters.neighborhoodSize = Math.max(0.01, this.parameters.neighborhoodSize * 0.9);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update optimization history\r\n   */\r\n  private updateHistory(): void {\r\n    if (!this.currentState || !this.bestSolution) return;\r\n    \r\n    const history: IterationHistory = {\r\n      iteration: this.currentState.iteration,\r\n      bestFitness: this.bestSolution.fitness,\r\n      averageFitness: this.currentState.solution.fitness,\r\n      worstFitness: this.currentState.solution.fitness,\r\n      diversity: 0, // Not applicable for single solution\r\n      constraintViolations: this.currentState.solution.feasible ? 0 : 1,\r\n      timestamp: new Date()\r\n    };\r\n    \r\n    this.history.push(history);\r\n  }\r\n\r\n  /**\r\n   * Create optimization result\r\n   */\r\n  private createOptimizationResult(problem: OptimizationProblem, startTime: number): OptimizationResult {\r\n    const executionTime = performance.now() - startTime;\r\n    \r\n    const statistics: OptimizationStatistics = {\r\n      totalIterations: this.currentState?.iteration || 0,\r\n      totalEvaluations: this.evaluationCount,\r\n      convergenceIteration: this.currentState?.iteration || 0,\r\n      executionTime,\r\n      bestFitnessHistory: this.history.map(h => h.bestFitness),\r\n      averageFitnessHistory: this.history.map(h => h.averageFitness),\r\n      diversityHistory: this.history.map(h => h.diversity),\r\n      constraintViolationHistory: this.history.map(h => h.constraintViolations),\r\n      algorithmSpecificStats: {\r\n        finalTemperature: this.currentState?.temperature || 0,\r\n        acceptedMoves: this.currentState?.acceptedMoves || 0,\r\n        rejectedMoves: this.currentState?.rejectedMoves || 0,\r\n        improvingMoves: this.currentState?.improvingMoves || 0,\r\n        worseningMoves: this.currentState?.worseningMoves || 0,\r\n        restartCount: this.currentState?.restartCount || 0,\r\n        finalNeighborhoodSize: this.parameters.neighborhoodSize\r\n      }\r\n    };\r\n    \r\n    const optimizationHistory: OptimizationHistory = {\r\n      iterations: this.history,\r\n      populationHistory: [],\r\n      parameterHistory: [],\r\n      convergenceMetrics: []\r\n    };\r\n    \r\n    return {\r\n      problemId: problem.id,\r\n      status: OptimizationStatus.CONVERGED,\r\n      bestSolution: this.bestSolution!,\r\n      statistics,\r\n      history: optimizationHistory,\r\n      analysis: {},\r\n      recommendations: [],\r\n      warnings: [],\r\n      errors: []\r\n    };\r\n  }\r\n\r\n  // Utility methods\r\n  private generateSolutionId(): string {\r\n    return `sa_sol_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\r\n  }\r\n\r\n  private createSeededRandom(seed: number): () => number {\r\n    let state = seed;\r\n    return () => {\r\n      state = (state * 9301 + 49297) % 233280;\r\n      return state / 233280;\r\n    };\r\n  }\r\n\r\n  private gaussianRandom(): number {\r\n    // Box-Muller transform\r\n    const u1 = this.random();\r\n    const u2 = this.random();\r\n    return Math.sqrt(-2 * Math.log(u1)) * Math.cos(2 * Math.PI * u2);\r\n  }\r\n\r\n  private cauchyRandom(): number {\r\n    // Cauchy distribution using inverse transform\r\n    const u = this.random();\r\n    return Math.tan(Math.PI * (u - 0.5));\r\n  }\r\n\r\n  private calculateVariance(values: number[]): number {\r\n    if (values.length === 0) return 0;\r\n    \r\n    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;\r\n    const squaredDiffs = values.map(val => Math.pow(val - mean, 2));\r\n    return squaredDiffs.reduce((sum, diff) => sum + diff, 0) / values.length;\r\n  }\r\n}\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "d7a8bc38eb8abc499d60b51629b7eff5ffb9eb9e"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1p7ah0mc1t = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1p7ah0mc1t();
cov_1p7ah0mc1t().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1p7ah0mc1t().s[1]++;
exports.SimulatedAnnealing = void 0;
const SystemOptimizationTypes_1 =
/* istanbul ignore next */
(cov_1p7ah0mc1t().s[2]++, require("../types/SystemOptimizationTypes"));
/**
 * Simulated Annealing optimizer for single-objective optimization problems
 */
class SimulatedAnnealing {
  constructor(parameters) {
    /* istanbul ignore next */
    cov_1p7ah0mc1t().f[0]++;
    cov_1p7ah0mc1t().s[3]++;
    this.currentState = null;
    /* istanbul ignore next */
    cov_1p7ah0mc1t().s[4]++;
    this.bestSolution = null;
    /* istanbul ignore next */
    cov_1p7ah0mc1t().s[5]++;
    this.history = [];
    /* istanbul ignore next */
    cov_1p7ah0mc1t().s[6]++;
    this.evaluationCount = 0;
    /* istanbul ignore next */
    cov_1p7ah0mc1t().s[7]++;
    this.parameters = {
      initialTemperature: 1000,
      finalTemperature: 0.01,
      maxIterations: 1000,
      coolingSchedule: {
        initialTemperature: 1000,
        finalTemperature: 0.01,
        coolingRate: 0.95,
        method: 'exponential'
      },
      neighborhoodSize: 0.1,
      neighborhoodMethod: 'gaussian',
      acceptanceCriterion: 'metropolis',
      constraintHandling: 'penalty',
      penaltyCoefficient: 1000,
      restartEnabled: true,
      maxRestarts: 3,
      restartTemperature: 100,
      adaptiveNeighborhood: true,
      ...parameters
    };
    // Initialize random number generator
    /* istanbul ignore next */
    cov_1p7ah0mc1t().s[8]++;
    if (this.parameters.seedValue !== undefined) {
      /* istanbul ignore next */
      cov_1p7ah0mc1t().b[0][0]++;
      cov_1p7ah0mc1t().s[9]++;
      this.random = this.createSeededRandom(this.parameters.seedValue);
    } else {
      /* istanbul ignore next */
      cov_1p7ah0mc1t().b[0][1]++;
      cov_1p7ah0mc1t().s[10]++;
      this.random = Math.random;
    }
  }
  /**
   * Main optimization method
   */
  async optimize(problem, objectiveFunction, constraintFunctions) {
    /* istanbul ignore next */
    cov_1p7ah0mc1t().f[1]++;
    const startTime =
    /* istanbul ignore next */
    (cov_1p7ah0mc1t().s[11]++, performance.now());
    /* istanbul ignore next */
    cov_1p7ah0mc1t().s[12]++;
    try {
      /* istanbul ignore next */
      cov_1p7ah0mc1t().s[13]++;
      // Initialize algorithm
      this.initializeAlgorithm(problem);
      // Create initial solution
      /* istanbul ignore next */
      cov_1p7ah0mc1t().s[14]++;
      await this.createInitialSolution(problem, objectiveFunction, constraintFunctions);
      // Main annealing loop
      /* istanbul ignore next */
      cov_1p7ah0mc1t().s[15]++;
      while (!this.shouldTerminate()) {
        /* istanbul ignore next */
        cov_1p7ah0mc1t().s[16]++;
        await this.performIteration(problem, objectiveFunction, constraintFunctions);
        /* istanbul ignore next */
        cov_1p7ah0mc1t().s[17]++;
        this.updateHistory();
        /* istanbul ignore next */
        cov_1p7ah0mc1t().s[18]++;
        if (this.parameters.adaptiveNeighborhood) {
          /* istanbul ignore next */
          cov_1p7ah0mc1t().b[1][0]++;
          cov_1p7ah0mc1t().s[19]++;
          this.adaptNeighborhoodSize();
        } else
        /* istanbul ignore next */
        {
          cov_1p7ah0mc1t().b[1][1]++;
        }
      }
      // Check for restart
      /* istanbul ignore next */
      cov_1p7ah0mc1t().s[20]++;
      if (this.shouldRestart()) {
        /* istanbul ignore next */
        cov_1p7ah0mc1t().b[2][0]++;
        cov_1p7ah0mc1t().s[21]++;
        await this.restart(problem, objectiveFunction, constraintFunctions);
      } else
      /* istanbul ignore next */
      {
        cov_1p7ah0mc1t().b[2][1]++;
      }
      // Create final result
      cov_1p7ah0mc1t().s[22]++;
      return this.createOptimizationResult(problem, startTime);
    } catch (error) {
      /* istanbul ignore next */
      cov_1p7ah0mc1t().s[23]++;
      console.error('Simulated annealing optimization failed:', error);
      /* istanbul ignore next */
      cov_1p7ah0mc1t().s[24]++;
      throw error;
    }
  }
  /**
   * Initialize algorithm state
   */
  initializeAlgorithm(problem) {
    /* istanbul ignore next */
    cov_1p7ah0mc1t().f[2]++;
    cov_1p7ah0mc1t().s[25]++;
    this.currentState = null;
    /* istanbul ignore next */
    cov_1p7ah0mc1t().s[26]++;
    this.bestSolution = null;
    /* istanbul ignore next */
    cov_1p7ah0mc1t().s[27]++;
    this.history = [];
    /* istanbul ignore next */
    cov_1p7ah0mc1t().s[28]++;
    this.evaluationCount = 0;
    /* istanbul ignore next */
    cov_1p7ah0mc1t().s[29]++;
    console.log(`Initializing Simulated Annealing with initial temperature: ${this.parameters.initialTemperature}`);
  }
  /**
   * Create initial solution
   */
  async createInitialSolution(problem, objectiveFunction, constraintFunctions) {
    /* istanbul ignore next */
    cov_1p7ah0mc1t().f[3]++;
    // Generate random initial solution
    const initialSolution =
    /* istanbul ignore next */
    (cov_1p7ah0mc1t().s[30]++, this.createRandomSolution(problem));
    /* istanbul ignore next */
    cov_1p7ah0mc1t().s[31]++;
    await this.evaluateSolution(initialSolution, problem, objectiveFunction, constraintFunctions);
    // Initialize state
    /* istanbul ignore next */
    cov_1p7ah0mc1t().s[32]++;
    this.currentState = {
      solution: initialSolution,
      temperature: this.parameters.initialTemperature,
      iteration: 0,
      acceptedMoves: 0,
      rejectedMoves: 0,
      improvingMoves: 0,
      worseningMoves: 0,
      restartCount: 0
    };
    /* istanbul ignore next */
    cov_1p7ah0mc1t().s[33]++;
    this.bestSolution = {
      ...initialSolution
    };
  }
  /**
   * Create a random solution
   */
  createRandomSolution(problem) {
    /* istanbul ignore next */
    cov_1p7ah0mc1t().f[4]++;
    const variables =
    /* istanbul ignore next */
    (cov_1p7ah0mc1t().s[34]++, {});
    /* istanbul ignore next */
    cov_1p7ah0mc1t().s[35]++;
    for (const variable of problem.variables) {
      /* istanbul ignore next */
      cov_1p7ah0mc1t().s[36]++;
      if (
      /* istanbul ignore next */
      (cov_1p7ah0mc1t().b[4][0]++, variable.discreteValues) &&
      /* istanbul ignore next */
      (cov_1p7ah0mc1t().b[4][1]++, variable.discreteValues.length > 0)) {
        /* istanbul ignore next */
        cov_1p7ah0mc1t().b[3][0]++;
        // Discrete variable
        const randomIndex =
        /* istanbul ignore next */
        (cov_1p7ah0mc1t().s[37]++, Math.floor(this.random() * variable.discreteValues.length));
        /* istanbul ignore next */
        cov_1p7ah0mc1t().s[38]++;
        variables[variable.id] = variable.discreteValues[randomIndex];
      } else {
        /* istanbul ignore next */
        cov_1p7ah0mc1t().b[3][1]++;
        // Continuous variable
        const min =
        /* istanbul ignore next */
        (cov_1p7ah0mc1t().s[39]++, typeof variable.bounds.minimum === 'number' ?
        /* istanbul ignore next */
        (cov_1p7ah0mc1t().b[5][0]++, variable.bounds.minimum) :
        /* istanbul ignore next */
        (cov_1p7ah0mc1t().b[5][1]++, 0));
        const max =
        /* istanbul ignore next */
        (cov_1p7ah0mc1t().s[40]++, typeof variable.bounds.maximum === 'number' ?
        /* istanbul ignore next */
        (cov_1p7ah0mc1t().b[6][0]++, variable.bounds.maximum) :
        /* istanbul ignore next */
        (cov_1p7ah0mc1t().b[6][1]++, 1));
        /* istanbul ignore next */
        cov_1p7ah0mc1t().s[41]++;
        variables[variable.id] = min + this.random() * (max - min);
      }
    }
    /* istanbul ignore next */
    cov_1p7ah0mc1t().s[42]++;
    return {
      id: this.generateSolutionId(),
      variables,
      objectiveValues: {},
      constraintViolations: [],
      feasible: true,
      fitness: 0,
      systemConfiguration: problem.systemConfiguration,
      performanceMetrics: {}
    };
  }
  /**
   * Evaluate solution fitness and constraints
   */
  async evaluateSolution(solution, problem, objectiveFunction, constraintFunctions) {
    /* istanbul ignore next */
    cov_1p7ah0mc1t().f[5]++;
    cov_1p7ah0mc1t().s[43]++;
    try {
      // Convert solution to optimization variables
      const variables =
      /* istanbul ignore next */
      (cov_1p7ah0mc1t().s[44]++, this.solutionToVariables(solution, problem.variables));
      // Evaluate objective function
      const objectiveValue =
      /* istanbul ignore next */
      (cov_1p7ah0mc1t().s[45]++, objectiveFunction(variables));
      /* istanbul ignore next */
      cov_1p7ah0mc1t().s[46]++;
      solution.fitness = objectiveValue;
      // Store objective values
      /* istanbul ignore next */
      cov_1p7ah0mc1t().s[47]++;
      if (problem.objectives.objectives.length > 0) {
        /* istanbul ignore next */
        cov_1p7ah0mc1t().b[7][0]++;
        cov_1p7ah0mc1t().s[48]++;
        solution.objectiveValues[problem.objectives.objectives[0].id] = objectiveValue;
      } else
      /* istanbul ignore next */
      {
        cov_1p7ah0mc1t().b[7][1]++;
      }
      // Evaluate constraints
      cov_1p7ah0mc1t().s[49]++;
      solution.constraintViolations = [];
      /* istanbul ignore next */
      cov_1p7ah0mc1t().s[50]++;
      for (const constraintFunction of constraintFunctions) {
        const violation =
        /* istanbul ignore next */
        (cov_1p7ah0mc1t().s[51]++, constraintFunction(variables));
        /* istanbul ignore next */
        cov_1p7ah0mc1t().s[52]++;
        solution.constraintViolations.push({
          constraintId: `constraint_${solution.constraintViolations.length}`,
          violationType: violation > 0 ?
          /* istanbul ignore next */
          (cov_1p7ah0mc1t().b[8][0]++, 'inequality') :
          /* istanbul ignore next */
          (cov_1p7ah0mc1t().b[8][1]++, 'boundary'),
          currentValue: violation,
          requiredValue: 0,
          severity: violation > 0 ?
          /* istanbul ignore next */
          (cov_1p7ah0mc1t().b[9][0]++, 'major') :
          /* istanbul ignore next */
          (cov_1p7ah0mc1t().b[9][1]++, 'minor'),
          penalty: violation > 0 ?
          /* istanbul ignore next */
          (cov_1p7ah0mc1t().b[10][0]++, violation * this.parameters.penaltyCoefficient) :
          /* istanbul ignore next */
          (cov_1p7ah0mc1t().b[10][1]++, 0)
        });
      }
      // Check feasibility
      /* istanbul ignore next */
      cov_1p7ah0mc1t().s[53]++;
      solution.feasible = solution.constraintViolations.every(v => {
        /* istanbul ignore next */
        cov_1p7ah0mc1t().f[6]++;
        cov_1p7ah0mc1t().s[54]++;
        return v.currentValue <= 0;
      });
      // Apply constraint handling
      /* istanbul ignore next */
      cov_1p7ah0mc1t().s[55]++;
      if (
      /* istanbul ignore next */
      (cov_1p7ah0mc1t().b[12][0]++, !solution.feasible) &&
      /* istanbul ignore next */
      (cov_1p7ah0mc1t().b[12][1]++, this.parameters.constraintHandling === 'penalty')) {
        /* istanbul ignore next */
        cov_1p7ah0mc1t().b[11][0]++;
        const totalPenalty =
        /* istanbul ignore next */
        (cov_1p7ah0mc1t().s[56]++, solution.constraintViolations.filter(v => {
          /* istanbul ignore next */
          cov_1p7ah0mc1t().f[7]++;
          cov_1p7ah0mc1t().s[57]++;
          return v.currentValue > 0;
        }).reduce((sum, v) => {
          /* istanbul ignore next */
          cov_1p7ah0mc1t().f[8]++;
          cov_1p7ah0mc1t().s[58]++;
          return sum + v.penalty;
        }, 0));
        /* istanbul ignore next */
        cov_1p7ah0mc1t().s[59]++;
        solution.fitness += totalPenalty;
      } else
      /* istanbul ignore next */
      {
        cov_1p7ah0mc1t().b[11][1]++;
      }
      cov_1p7ah0mc1t().s[60]++;
      this.evaluationCount++;
    } catch (error) {
      /* istanbul ignore next */
      cov_1p7ah0mc1t().s[61]++;
      console.error('Error evaluating solution:', error);
      /* istanbul ignore next */
      cov_1p7ah0mc1t().s[62]++;
      solution.fitness = Number.MAX_VALUE;
      /* istanbul ignore next */
      cov_1p7ah0mc1t().s[63]++;
      solution.feasible = false;
    }
  }
  /**
   * Convert solution to optimization variables
   */
  solutionToVariables(solution, variableTemplates) {
    /* istanbul ignore next */
    cov_1p7ah0mc1t().f[9]++;
    cov_1p7ah0mc1t().s[64]++;
    return variableTemplates.map(template => {
      /* istanbul ignore next */
      cov_1p7ah0mc1t().f[10]++;
      cov_1p7ah0mc1t().s[65]++;
      return {
        ...template,
        currentValue: solution.variables[template.id]
      };
    });
  }
  /**
   * Perform one iteration of simulated annealing
   */
  async performIteration(problem, objectiveFunction, constraintFunctions) {
    /* istanbul ignore next */
    cov_1p7ah0mc1t().f[11]++;
    cov_1p7ah0mc1t().s[66]++;
    if (!this.currentState) {
      /* istanbul ignore next */
      cov_1p7ah0mc1t().b[13][0]++;
      cov_1p7ah0mc1t().s[67]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_1p7ah0mc1t().b[13][1]++;
    }
    // Generate neighbor solution
    const neighborSolution =
    /* istanbul ignore next */
    (cov_1p7ah0mc1t().s[68]++, this.generateNeighbor(this.currentState.solution, problem));
    /* istanbul ignore next */
    cov_1p7ah0mc1t().s[69]++;
    await this.evaluateSolution(neighborSolution, problem, objectiveFunction, constraintFunctions);
    // Calculate acceptance probability
    const deltaE =
    /* istanbul ignore next */
    (cov_1p7ah0mc1t().s[70]++, neighborSolution.fitness - this.currentState.solution.fitness);
    const acceptanceProbability =
    /* istanbul ignore next */
    (cov_1p7ah0mc1t().s[71]++, this.calculateAcceptanceProbability(deltaE, this.currentState.temperature));
    // Accept or reject the neighbor
    /* istanbul ignore next */
    cov_1p7ah0mc1t().s[72]++;
    if (this.random() < acceptanceProbability) {
      /* istanbul ignore next */
      cov_1p7ah0mc1t().b[14][0]++;
      cov_1p7ah0mc1t().s[73]++;
      // Accept the neighbor
      this.currentState.solution = neighborSolution;
      /* istanbul ignore next */
      cov_1p7ah0mc1t().s[74]++;
      this.currentState.acceptedMoves++;
      /* istanbul ignore next */
      cov_1p7ah0mc1t().s[75]++;
      if (deltaE < 0) {
        /* istanbul ignore next */
        cov_1p7ah0mc1t().b[15][0]++;
        cov_1p7ah0mc1t().s[76]++;
        this.currentState.improvingMoves++;
        // Update best solution
        /* istanbul ignore next */
        cov_1p7ah0mc1t().s[77]++;
        if (neighborSolution.fitness < this.bestSolution.fitness) {
          /* istanbul ignore next */
          cov_1p7ah0mc1t().b[16][0]++;
          cov_1p7ah0mc1t().s[78]++;
          this.bestSolution = {
            ...neighborSolution
          };
        } else
        /* istanbul ignore next */
        {
          cov_1p7ah0mc1t().b[16][1]++;
        }
      } else {
        /* istanbul ignore next */
        cov_1p7ah0mc1t().b[15][1]++;
        cov_1p7ah0mc1t().s[79]++;
        this.currentState.worseningMoves++;
      }
    } else {
      /* istanbul ignore next */
      cov_1p7ah0mc1t().b[14][1]++;
      cov_1p7ah0mc1t().s[80]++;
      // Reject the neighbor
      this.currentState.rejectedMoves++;
    }
    // Update temperature
    /* istanbul ignore next */
    cov_1p7ah0mc1t().s[81]++;
    this.currentState.temperature = this.updateTemperature(this.currentState.temperature, this.currentState.iteration);
    /* istanbul ignore next */
    cov_1p7ah0mc1t().s[82]++;
    this.currentState.iteration++;
  }
  /**
   * Generate neighbor solution
   */
  generateNeighbor(currentSolution, problem) {
    /* istanbul ignore next */
    cov_1p7ah0mc1t().f[12]++;
    const neighbor =
    /* istanbul ignore next */
    (cov_1p7ah0mc1t().s[83]++, {
      ...currentSolution,
      id: this.generateSolutionId(),
      variables: {
        ...currentSolution.variables
      }
    });
    // Select random variable to modify
    const variableIds =
    /* istanbul ignore next */
    (cov_1p7ah0mc1t().s[84]++, Object.keys(neighbor.variables));
    const randomVariableId =
    /* istanbul ignore next */
    (cov_1p7ah0mc1t().s[85]++, variableIds[Math.floor(this.random() * variableIds.length)]);
    const variable =
    /* istanbul ignore next */
    (cov_1p7ah0mc1t().s[86]++, problem.variables.find(v => {
      /* istanbul ignore next */
      cov_1p7ah0mc1t().f[13]++;
      cov_1p7ah0mc1t().s[87]++;
      return v.id === randomVariableId;
    }));
    /* istanbul ignore next */
    cov_1p7ah0mc1t().s[88]++;
    if (!variable) {
      /* istanbul ignore next */
      cov_1p7ah0mc1t().b[17][0]++;
      cov_1p7ah0mc1t().s[89]++;
      return neighbor;
    } else
    /* istanbul ignore next */
    {
      cov_1p7ah0mc1t().b[17][1]++;
    }
    // Generate neighbor value based on neighborhood method
    cov_1p7ah0mc1t().s[90]++;
    neighbor.variables[randomVariableId] = this.generateNeighborValue(neighbor.variables[randomVariableId], variable);
    /* istanbul ignore next */
    cov_1p7ah0mc1t().s[91]++;
    return neighbor;
  }
  /**
   * Generate neighbor value for a variable
   */
  generateNeighborValue(currentValue, variable) {
    /* istanbul ignore next */
    cov_1p7ah0mc1t().f[14]++;
    cov_1p7ah0mc1t().s[92]++;
    if (
    /* istanbul ignore next */
    (cov_1p7ah0mc1t().b[19][0]++, variable.discreteValues) &&
    /* istanbul ignore next */
    (cov_1p7ah0mc1t().b[19][1]++, variable.discreteValues.length > 0)) {
      /* istanbul ignore next */
      cov_1p7ah0mc1t().b[18][0]++;
      // Discrete variable - random selection from neighborhood
      const currentIndex =
      /* istanbul ignore next */
      (cov_1p7ah0mc1t().s[93]++, variable.discreteValues.indexOf(currentValue));
      const neighborhoodSize =
      /* istanbul ignore next */
      (cov_1p7ah0mc1t().s[94]++, Math.max(1, Math.floor(variable.discreteValues.length * this.parameters.neighborhoodSize)));
      const minIndex =
      /* istanbul ignore next */
      (cov_1p7ah0mc1t().s[95]++, Math.max(0, currentIndex - neighborhoodSize));
      const maxIndex =
      /* istanbul ignore next */
      (cov_1p7ah0mc1t().s[96]++, Math.min(variable.discreteValues.length - 1, currentIndex + neighborhoodSize));
      const randomIndex =
      /* istanbul ignore next */
      (cov_1p7ah0mc1t().s[97]++, minIndex + Math.floor(this.random() * (maxIndex - minIndex + 1)));
      /* istanbul ignore next */
      cov_1p7ah0mc1t().s[98]++;
      return variable.discreteValues[randomIndex];
    } else {
      /* istanbul ignore next */
      cov_1p7ah0mc1t().b[18][1]++;
      cov_1p7ah0mc1t().s[99]++;
      if (typeof currentValue === 'number') {
        /* istanbul ignore next */
        cov_1p7ah0mc1t().b[20][0]++;
        // Continuous variable
        const min =
        /* istanbul ignore next */
        (cov_1p7ah0mc1t().s[100]++, typeof variable.bounds.minimum === 'number' ?
        /* istanbul ignore next */
        (cov_1p7ah0mc1t().b[21][0]++, variable.bounds.minimum) :
        /* istanbul ignore next */
        (cov_1p7ah0mc1t().b[21][1]++, 0));
        const max =
        /* istanbul ignore next */
        (cov_1p7ah0mc1t().s[101]++, typeof variable.bounds.maximum === 'number' ?
        /* istanbul ignore next */
        (cov_1p7ah0mc1t().b[22][0]++, variable.bounds.maximum) :
        /* istanbul ignore next */
        (cov_1p7ah0mc1t().b[22][1]++, 1));
        const range =
        /* istanbul ignore next */
        (cov_1p7ah0mc1t().s[102]++, max - min);
        let neighborValue;
        /* istanbul ignore next */
        cov_1p7ah0mc1t().s[103]++;
        switch (this.parameters.neighborhoodMethod) {
          case 'gaussian':
            /* istanbul ignore next */
            cov_1p7ah0mc1t().b[23][0]++;
            const sigma =
            /* istanbul ignore next */
            (cov_1p7ah0mc1t().s[104]++, range * this.parameters.neighborhoodSize);
            /* istanbul ignore next */
            cov_1p7ah0mc1t().s[105]++;
            neighborValue = currentValue + this.gaussianRandom() * sigma;
            /* istanbul ignore next */
            cov_1p7ah0mc1t().s[106]++;
            break;
          case 'uniform':
            /* istanbul ignore next */
            cov_1p7ah0mc1t().b[23][1]++;
            const delta =
            /* istanbul ignore next */
            (cov_1p7ah0mc1t().s[107]++, range * this.parameters.neighborhoodSize);
            /* istanbul ignore next */
            cov_1p7ah0mc1t().s[108]++;
            neighborValue = currentValue + (this.random() - 0.5) * 2 * delta;
            /* istanbul ignore next */
            cov_1p7ah0mc1t().s[109]++;
            break;
          case 'cauchy':
            /* istanbul ignore next */
            cov_1p7ah0mc1t().b[23][2]++;
            const gamma =
            /* istanbul ignore next */
            (cov_1p7ah0mc1t().s[110]++, range * this.parameters.neighborhoodSize);
            /* istanbul ignore next */
            cov_1p7ah0mc1t().s[111]++;
            neighborValue = currentValue + this.cauchyRandom() * gamma;
            /* istanbul ignore next */
            cov_1p7ah0mc1t().s[112]++;
            break;
          default:
            /* istanbul ignore next */
            cov_1p7ah0mc1t().b[23][3]++;
            cov_1p7ah0mc1t().s[113]++;
            neighborValue = currentValue + (this.random() - 0.5) * range * this.parameters.neighborhoodSize;
        }
        /* istanbul ignore next */
        cov_1p7ah0mc1t().s[114]++;
        return Math.max(min, Math.min(max, neighborValue));
      } else
      /* istanbul ignore next */
      {
        cov_1p7ah0mc1t().b[20][1]++;
      }
    }
    /* istanbul ignore next */
    cov_1p7ah0mc1t().s[115]++;
    return currentValue;
  }
  /**
   * Calculate acceptance probability
   */
  calculateAcceptanceProbability(deltaE, temperature) {
    /* istanbul ignore next */
    cov_1p7ah0mc1t().f[15]++;
    cov_1p7ah0mc1t().s[116]++;
    if (deltaE <= 0) {
      /* istanbul ignore next */
      cov_1p7ah0mc1t().b[24][0]++;
      cov_1p7ah0mc1t().s[117]++;
      return 1.0; // Always accept improving solutions
    } else
    /* istanbul ignore next */
    {
      cov_1p7ah0mc1t().b[24][1]++;
    }
    cov_1p7ah0mc1t().s[118]++;
    switch (this.parameters.acceptanceCriterion) {
      case 'metropolis':
        /* istanbul ignore next */
        cov_1p7ah0mc1t().b[25][0]++;
        cov_1p7ah0mc1t().s[119]++;
        return Math.exp(-deltaE / temperature);
      case 'boltzmann':
        /* istanbul ignore next */
        cov_1p7ah0mc1t().b[25][1]++;
        cov_1p7ah0mc1t().s[120]++;
        return 1.0 / (1.0 + Math.exp(deltaE / temperature));
      case 'fast_annealing':
        /* istanbul ignore next */
        cov_1p7ah0mc1t().b[25][2]++;
        cov_1p7ah0mc1t().s[121]++;
        return Math.exp(-deltaE / (temperature * Math.log(this.currentState.iteration + 2)));
      default:
        /* istanbul ignore next */
        cov_1p7ah0mc1t().b[25][3]++;
        cov_1p7ah0mc1t().s[122]++;
        return Math.exp(-deltaE / temperature);
    }
  }
  /**
   * Update temperature according to cooling schedule
   */
  updateTemperature(currentTemperature, iteration) {
    /* istanbul ignore next */
    cov_1p7ah0mc1t().f[16]++;
    const schedule =
    /* istanbul ignore next */
    (cov_1p7ah0mc1t().s[123]++, this.parameters.coolingSchedule);
    /* istanbul ignore next */
    cov_1p7ah0mc1t().s[124]++;
    switch (schedule.method) {
      case 'linear':
        /* istanbul ignore next */
        cov_1p7ah0mc1t().b[26][0]++;
        const linearRate =
        /* istanbul ignore next */
        (cov_1p7ah0mc1t().s[125]++, (schedule.initialTemperature - schedule.finalTemperature) / this.parameters.maxIterations);
        /* istanbul ignore next */
        cov_1p7ah0mc1t().s[126]++;
        return Math.max(schedule.finalTemperature, schedule.initialTemperature - iteration * linearRate);
      case 'exponential':
        /* istanbul ignore next */
        cov_1p7ah0mc1t().b[26][1]++;
        cov_1p7ah0mc1t().s[127]++;
        return Math.max(schedule.finalTemperature, currentTemperature * schedule.coolingRate);
      case 'logarithmic':
        /* istanbul ignore next */
        cov_1p7ah0mc1t().b[26][2]++;
        cov_1p7ah0mc1t().s[128]++;
        return Math.max(schedule.finalTemperature, schedule.initialTemperature / Math.log(iteration + 2));
      case 'adaptive':
        /* istanbul ignore next */
        cov_1p7ah0mc1t().b[26][3]++;
        // Adaptive cooling based on acceptance rate
        const acceptanceRate =
        /* istanbul ignore next */
        (cov_1p7ah0mc1t().s[129]++, this.currentState.acceptedMoves / (this.currentState.acceptedMoves + this.currentState.rejectedMoves + 1));
        const targetAcceptanceRate =
        /* istanbul ignore next */
        (cov_1p7ah0mc1t().s[130]++, 0.4);
        const adaptationFactor =
        /* istanbul ignore next */
        (cov_1p7ah0mc1t().s[131]++, acceptanceRate > targetAcceptanceRate ?
        /* istanbul ignore next */
        (cov_1p7ah0mc1t().b[27][0]++, 0.99) :
        /* istanbul ignore next */
        (cov_1p7ah0mc1t().b[27][1]++, 0.95));
        /* istanbul ignore next */
        cov_1p7ah0mc1t().s[132]++;
        return Math.max(schedule.finalTemperature, currentTemperature * adaptationFactor);
      default:
        /* istanbul ignore next */
        cov_1p7ah0mc1t().b[26][4]++;
        cov_1p7ah0mc1t().s[133]++;
        return Math.max(schedule.finalTemperature, currentTemperature * schedule.coolingRate);
    }
  }
  /**
   * Check termination criteria
   */
  shouldTerminate() {
    /* istanbul ignore next */
    cov_1p7ah0mc1t().f[17]++;
    cov_1p7ah0mc1t().s[134]++;
    if (!this.currentState) {
      /* istanbul ignore next */
      cov_1p7ah0mc1t().b[28][0]++;
      cov_1p7ah0mc1t().s[135]++;
      return true;
    } else
    /* istanbul ignore next */
    {
      cov_1p7ah0mc1t().b[28][1]++;
    }
    // Maximum iterations
    cov_1p7ah0mc1t().s[136]++;
    if (this.currentState.iteration >= this.parameters.maxIterations) {
      /* istanbul ignore next */
      cov_1p7ah0mc1t().b[29][0]++;
      cov_1p7ah0mc1t().s[137]++;
      return true;
    } else
    /* istanbul ignore next */
    {
      cov_1p7ah0mc1t().b[29][1]++;
    }
    // Temperature threshold
    cov_1p7ah0mc1t().s[138]++;
    if (this.currentState.temperature <= this.parameters.finalTemperature) {
      /* istanbul ignore next */
      cov_1p7ah0mc1t().b[30][0]++;
      cov_1p7ah0mc1t().s[139]++;
      return true;
    } else
    /* istanbul ignore next */
    {
      cov_1p7ah0mc1t().b[30][1]++;
    }
    // Convergence check (no improvement in recent iterations)
    cov_1p7ah0mc1t().s[140]++;
    if (this.history.length >= 50) {
      /* istanbul ignore next */
      cov_1p7ah0mc1t().b[31][0]++;
      const recentHistory =
      /* istanbul ignore next */
      (cov_1p7ah0mc1t().s[141]++, this.history.slice(-50));
      const bestRecent =
      /* istanbul ignore next */
      (cov_1p7ah0mc1t().s[142]++, Math.min(...recentHistory.map(h => {
        /* istanbul ignore next */
        cov_1p7ah0mc1t().f[18]++;
        cov_1p7ah0mc1t().s[143]++;
        return h.bestFitness;
      })));
      const improvement =
      /* istanbul ignore next */
      (cov_1p7ah0mc1t().s[144]++, this.bestSolution.fitness - bestRecent);
      /* istanbul ignore next */
      cov_1p7ah0mc1t().s[145]++;
      if (Math.abs(improvement) < 1e-6) {
        /* istanbul ignore next */
        cov_1p7ah0mc1t().b[32][0]++;
        cov_1p7ah0mc1t().s[146]++;
        return true;
      } else
      /* istanbul ignore next */
      {
        cov_1p7ah0mc1t().b[32][1]++;
      }
    } else
    /* istanbul ignore next */
    {
      cov_1p7ah0mc1t().b[31][1]++;
    }
    cov_1p7ah0mc1t().s[147]++;
    return false;
  }
  /**
   * Check if restart should be performed
   */
  shouldRestart() {
    /* istanbul ignore next */
    cov_1p7ah0mc1t().f[19]++;
    cov_1p7ah0mc1t().s[148]++;
    if (!this.parameters.restartEnabled) {
      /* istanbul ignore next */
      cov_1p7ah0mc1t().b[33][0]++;
      cov_1p7ah0mc1t().s[149]++;
      return false;
    } else
    /* istanbul ignore next */
    {
      cov_1p7ah0mc1t().b[33][1]++;
    }
    cov_1p7ah0mc1t().s[150]++;
    if (!this.currentState) {
      /* istanbul ignore next */
      cov_1p7ah0mc1t().b[34][0]++;
      cov_1p7ah0mc1t().s[151]++;
      return false;
    } else
    /* istanbul ignore next */
    {
      cov_1p7ah0mc1t().b[34][1]++;
    }
    cov_1p7ah0mc1t().s[152]++;
    if (this.currentState.restartCount >= this.parameters.maxRestarts) {
      /* istanbul ignore next */
      cov_1p7ah0mc1t().b[35][0]++;
      cov_1p7ah0mc1t().s[153]++;
      return false;
    } else
    /* istanbul ignore next */
    {
      cov_1p7ah0mc1t().b[35][1]++;
    }
    // Restart if stuck in local minimum
    const recentIterations =
    /* istanbul ignore next */
    (cov_1p7ah0mc1t().s[154]++, 100);
    /* istanbul ignore next */
    cov_1p7ah0mc1t().s[155]++;
    if (this.history.length >= recentIterations) {
      /* istanbul ignore next */
      cov_1p7ah0mc1t().b[36][0]++;
      const recentHistory =
      /* istanbul ignore next */
      (cov_1p7ah0mc1t().s[156]++, this.history.slice(-recentIterations));
      const fitnessVariance =
      /* istanbul ignore next */
      (cov_1p7ah0mc1t().s[157]++, this.calculateVariance(recentHistory.map(h => {
        /* istanbul ignore next */
        cov_1p7ah0mc1t().f[20]++;
        cov_1p7ah0mc1t().s[158]++;
        return h.bestFitness;
      })));
      /* istanbul ignore next */
      cov_1p7ah0mc1t().s[159]++;
      if (fitnessVariance < 1e-6) {
        /* istanbul ignore next */
        cov_1p7ah0mc1t().b[37][0]++;
        cov_1p7ah0mc1t().s[160]++;
        return true;
      } else
      /* istanbul ignore next */
      {
        cov_1p7ah0mc1t().b[37][1]++;
      }
    } else
    /* istanbul ignore next */
    {
      cov_1p7ah0mc1t().b[36][1]++;
    }
    cov_1p7ah0mc1t().s[161]++;
    return false;
  }
  /**
   * Restart the algorithm from a new random solution
   */
  async restart(problem, objectiveFunction, constraintFunctions) {
    /* istanbul ignore next */
    cov_1p7ah0mc1t().f[21]++;
    cov_1p7ah0mc1t().s[162]++;
    console.log(`Restarting simulated annealing (restart ${this.currentState.restartCount + 1})`);
    // Create new random solution
    const newSolution =
    /* istanbul ignore next */
    (cov_1p7ah0mc1t().s[163]++, this.createRandomSolution(problem));
    /* istanbul ignore next */
    cov_1p7ah0mc1t().s[164]++;
    await this.evaluateSolution(newSolution, problem, objectiveFunction, constraintFunctions);
    // Reset state with restart temperature
    /* istanbul ignore next */
    cov_1p7ah0mc1t().s[165]++;
    this.currentState.solution = newSolution;
    /* istanbul ignore next */
    cov_1p7ah0mc1t().s[166]++;
    this.currentState.temperature = this.parameters.restartTemperature;
    /* istanbul ignore next */
    cov_1p7ah0mc1t().s[167]++;
    this.currentState.iteration = 0;
    /* istanbul ignore next */
    cov_1p7ah0mc1t().s[168]++;
    this.currentState.acceptedMoves = 0;
    /* istanbul ignore next */
    cov_1p7ah0mc1t().s[169]++;
    this.currentState.rejectedMoves = 0;
    /* istanbul ignore next */
    cov_1p7ah0mc1t().s[170]++;
    this.currentState.improvingMoves = 0;
    /* istanbul ignore next */
    cov_1p7ah0mc1t().s[171]++;
    this.currentState.worseningMoves = 0;
    /* istanbul ignore next */
    cov_1p7ah0mc1t().s[172]++;
    this.currentState.restartCount++;
    // Continue optimization
    /* istanbul ignore next */
    cov_1p7ah0mc1t().s[173]++;
    while (!this.shouldTerminate()) {
      /* istanbul ignore next */
      cov_1p7ah0mc1t().s[174]++;
      await this.performIteration(problem, objectiveFunction, constraintFunctions);
      /* istanbul ignore next */
      cov_1p7ah0mc1t().s[175]++;
      this.updateHistory();
    }
  }
  /**
   * Adapt neighborhood size based on acceptance rate
   */
  adaptNeighborhoodSize() {
    /* istanbul ignore next */
    cov_1p7ah0mc1t().f[22]++;
    cov_1p7ah0mc1t().s[176]++;
    if (!this.currentState) {
      /* istanbul ignore next */
      cov_1p7ah0mc1t().b[38][0]++;
      cov_1p7ah0mc1t().s[177]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_1p7ah0mc1t().b[38][1]++;
    }
    const totalMoves =
    /* istanbul ignore next */
    (cov_1p7ah0mc1t().s[178]++, this.currentState.acceptedMoves + this.currentState.rejectedMoves);
    /* istanbul ignore next */
    cov_1p7ah0mc1t().s[179]++;
    if (totalMoves < 10) {
      /* istanbul ignore next */
      cov_1p7ah0mc1t().b[39][0]++;
      cov_1p7ah0mc1t().s[180]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_1p7ah0mc1t().b[39][1]++;
    }
    const acceptanceRate =
    /* istanbul ignore next */
    (cov_1p7ah0mc1t().s[181]++, this.currentState.acceptedMoves / totalMoves);
    const targetAcceptanceRate =
    /* istanbul ignore next */
    (cov_1p7ah0mc1t().s[182]++, 0.4);
    /* istanbul ignore next */
    cov_1p7ah0mc1t().s[183]++;
    if (acceptanceRate > targetAcceptanceRate + 0.1) {
      /* istanbul ignore next */
      cov_1p7ah0mc1t().b[40][0]++;
      cov_1p7ah0mc1t().s[184]++;
      // Too many acceptances - increase neighborhood size
      this.parameters.neighborhoodSize = Math.min(0.5, this.parameters.neighborhoodSize * 1.1);
    } else {
      /* istanbul ignore next */
      cov_1p7ah0mc1t().b[40][1]++;
      cov_1p7ah0mc1t().s[185]++;
      if (acceptanceRate < targetAcceptanceRate - 0.1) {
        /* istanbul ignore next */
        cov_1p7ah0mc1t().b[41][0]++;
        cov_1p7ah0mc1t().s[186]++;
        // Too few acceptances - decrease neighborhood size
        this.parameters.neighborhoodSize = Math.max(0.01, this.parameters.neighborhoodSize * 0.9);
      } else
      /* istanbul ignore next */
      {
        cov_1p7ah0mc1t().b[41][1]++;
      }
    }
  }
  /**
   * Update optimization history
   */
  updateHistory() {
    /* istanbul ignore next */
    cov_1p7ah0mc1t().f[23]++;
    cov_1p7ah0mc1t().s[187]++;
    if (
    /* istanbul ignore next */
    (cov_1p7ah0mc1t().b[43][0]++, !this.currentState) ||
    /* istanbul ignore next */
    (cov_1p7ah0mc1t().b[43][1]++, !this.bestSolution)) {
      /* istanbul ignore next */
      cov_1p7ah0mc1t().b[42][0]++;
      cov_1p7ah0mc1t().s[188]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_1p7ah0mc1t().b[42][1]++;
    }
    const history =
    /* istanbul ignore next */
    (cov_1p7ah0mc1t().s[189]++, {
      iteration: this.currentState.iteration,
      bestFitness: this.bestSolution.fitness,
      averageFitness: this.currentState.solution.fitness,
      worstFitness: this.currentState.solution.fitness,
      diversity: 0,
      // Not applicable for single solution
      constraintViolations: this.currentState.solution.feasible ?
      /* istanbul ignore next */
      (cov_1p7ah0mc1t().b[44][0]++, 0) :
      /* istanbul ignore next */
      (cov_1p7ah0mc1t().b[44][1]++, 1),
      timestamp: new Date()
    });
    /* istanbul ignore next */
    cov_1p7ah0mc1t().s[190]++;
    this.history.push(history);
  }
  /**
   * Create optimization result
   */
  createOptimizationResult(problem, startTime) {
    /* istanbul ignore next */
    cov_1p7ah0mc1t().f[24]++;
    const executionTime =
    /* istanbul ignore next */
    (cov_1p7ah0mc1t().s[191]++, performance.now() - startTime);
    const statistics =
    /* istanbul ignore next */
    (cov_1p7ah0mc1t().s[192]++, {
      totalIterations:
      /* istanbul ignore next */
      (cov_1p7ah0mc1t().b[45][0]++, this.currentState?.iteration) ||
      /* istanbul ignore next */
      (cov_1p7ah0mc1t().b[45][1]++, 0),
      totalEvaluations: this.evaluationCount,
      convergenceIteration:
      /* istanbul ignore next */
      (cov_1p7ah0mc1t().b[46][0]++, this.currentState?.iteration) ||
      /* istanbul ignore next */
      (cov_1p7ah0mc1t().b[46][1]++, 0),
      executionTime,
      bestFitnessHistory: this.history.map(h => {
        /* istanbul ignore next */
        cov_1p7ah0mc1t().f[25]++;
        cov_1p7ah0mc1t().s[193]++;
        return h.bestFitness;
      }),
      averageFitnessHistory: this.history.map(h => {
        /* istanbul ignore next */
        cov_1p7ah0mc1t().f[26]++;
        cov_1p7ah0mc1t().s[194]++;
        return h.averageFitness;
      }),
      diversityHistory: this.history.map(h => {
        /* istanbul ignore next */
        cov_1p7ah0mc1t().f[27]++;
        cov_1p7ah0mc1t().s[195]++;
        return h.diversity;
      }),
      constraintViolationHistory: this.history.map(h => {
        /* istanbul ignore next */
        cov_1p7ah0mc1t().f[28]++;
        cov_1p7ah0mc1t().s[196]++;
        return h.constraintViolations;
      }),
      algorithmSpecificStats: {
        finalTemperature:
        /* istanbul ignore next */
        (cov_1p7ah0mc1t().b[47][0]++, this.currentState?.temperature) ||
        /* istanbul ignore next */
        (cov_1p7ah0mc1t().b[47][1]++, 0),
        acceptedMoves:
        /* istanbul ignore next */
        (cov_1p7ah0mc1t().b[48][0]++, this.currentState?.acceptedMoves) ||
        /* istanbul ignore next */
        (cov_1p7ah0mc1t().b[48][1]++, 0),
        rejectedMoves:
        /* istanbul ignore next */
        (cov_1p7ah0mc1t().b[49][0]++, this.currentState?.rejectedMoves) ||
        /* istanbul ignore next */
        (cov_1p7ah0mc1t().b[49][1]++, 0),
        improvingMoves:
        /* istanbul ignore next */
        (cov_1p7ah0mc1t().b[50][0]++, this.currentState?.improvingMoves) ||
        /* istanbul ignore next */
        (cov_1p7ah0mc1t().b[50][1]++, 0),
        worseningMoves:
        /* istanbul ignore next */
        (cov_1p7ah0mc1t().b[51][0]++, this.currentState?.worseningMoves) ||
        /* istanbul ignore next */
        (cov_1p7ah0mc1t().b[51][1]++, 0),
        restartCount:
        /* istanbul ignore next */
        (cov_1p7ah0mc1t().b[52][0]++, this.currentState?.restartCount) ||
        /* istanbul ignore next */
        (cov_1p7ah0mc1t().b[52][1]++, 0),
        finalNeighborhoodSize: this.parameters.neighborhoodSize
      }
    });
    const optimizationHistory =
    /* istanbul ignore next */
    (cov_1p7ah0mc1t().s[197]++, {
      iterations: this.history,
      populationHistory: [],
      parameterHistory: [],
      convergenceMetrics: []
    });
    /* istanbul ignore next */
    cov_1p7ah0mc1t().s[198]++;
    return {
      problemId: problem.id,
      status: SystemOptimizationTypes_1.OptimizationStatus.CONVERGED,
      bestSolution: this.bestSolution,
      statistics,
      history: optimizationHistory,
      analysis: {},
      recommendations: [],
      warnings: [],
      errors: []
    };
  }
  // Utility methods
  generateSolutionId() {
    /* istanbul ignore next */
    cov_1p7ah0mc1t().f[29]++;
    cov_1p7ah0mc1t().s[199]++;
    return `sa_sol_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  createSeededRandom(seed) {
    /* istanbul ignore next */
    cov_1p7ah0mc1t().f[30]++;
    let state =
    /* istanbul ignore next */
    (cov_1p7ah0mc1t().s[200]++, seed);
    /* istanbul ignore next */
    cov_1p7ah0mc1t().s[201]++;
    return () => {
      /* istanbul ignore next */
      cov_1p7ah0mc1t().f[31]++;
      cov_1p7ah0mc1t().s[202]++;
      state = (state * 9301 + 49297) % 233280;
      /* istanbul ignore next */
      cov_1p7ah0mc1t().s[203]++;
      return state / 233280;
    };
  }
  gaussianRandom() {
    /* istanbul ignore next */
    cov_1p7ah0mc1t().f[32]++;
    // Box-Muller transform
    const u1 =
    /* istanbul ignore next */
    (cov_1p7ah0mc1t().s[204]++, this.random());
    const u2 =
    /* istanbul ignore next */
    (cov_1p7ah0mc1t().s[205]++, this.random());
    /* istanbul ignore next */
    cov_1p7ah0mc1t().s[206]++;
    return Math.sqrt(-2 * Math.log(u1)) * Math.cos(2 * Math.PI * u2);
  }
  cauchyRandom() {
    /* istanbul ignore next */
    cov_1p7ah0mc1t().f[33]++;
    // Cauchy distribution using inverse transform
    const u =
    /* istanbul ignore next */
    (cov_1p7ah0mc1t().s[207]++, this.random());
    /* istanbul ignore next */
    cov_1p7ah0mc1t().s[208]++;
    return Math.tan(Math.PI * (u - 0.5));
  }
  calculateVariance(values) {
    /* istanbul ignore next */
    cov_1p7ah0mc1t().f[34]++;
    cov_1p7ah0mc1t().s[209]++;
    if (values.length === 0) {
      /* istanbul ignore next */
      cov_1p7ah0mc1t().b[53][0]++;
      cov_1p7ah0mc1t().s[210]++;
      return 0;
    } else
    /* istanbul ignore next */
    {
      cov_1p7ah0mc1t().b[53][1]++;
    }
    const mean =
    /* istanbul ignore next */
    (cov_1p7ah0mc1t().s[211]++, values.reduce((sum, val) => {
      /* istanbul ignore next */
      cov_1p7ah0mc1t().f[35]++;
      cov_1p7ah0mc1t().s[212]++;
      return sum + val;
    }, 0) / values.length);
    const squaredDiffs =
    /* istanbul ignore next */
    (cov_1p7ah0mc1t().s[213]++, values.map(val => {
      /* istanbul ignore next */
      cov_1p7ah0mc1t().f[36]++;
      cov_1p7ah0mc1t().s[214]++;
      return Math.pow(val - mean, 2);
    }));
    /* istanbul ignore next */
    cov_1p7ah0mc1t().s[215]++;
    return squaredDiffs.reduce((sum, diff) => {
      /* istanbul ignore next */
      cov_1p7ah0mc1t().f[37]++;
      cov_1p7ah0mc1t().s[216]++;
      return sum + diff;
    }, 0) / values.length;
  }
}
/* istanbul ignore next */
cov_1p7ah0mc1t().s[217]++;
exports.SimulatedAnnealing = SimulatedAnnealing;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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