{"version": 3, "names": ["cov_2fjbc9r3tm", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "name", "decl", "loc", "branchMap", "type", "locations", "undefined", "s", "f", "b", "inputSourceMap", "file", "mappings", "names", "sources", "sourcesContent", "version", "_coverageSchema", "coverage", "actualCoverage", "fs_1", "require", "path_1", "loadFittingCoefficients", "dataPath", "join", "__dirname", "rawData", "readFileSync", "JSON", "parse", "error", "console", "warn", "metadata", "standard", "round_fittings", "elbows", "tees", "transitions", "entries_exits", "rectangular_fittings", "special_fittings", "dampers", "diffusers", "fittingCoefficients", "FittingLossCalculator", "calculateVelocityPressure", "input", "velocity", "airDensity", "standardVP", "Math", "pow", "densityRatio", "calculateFittingLoss", "config", "velocityPressure", "kFactorResult", "getKFactor", "pressureLoss", "kFactor", "fittingType", "configuration", "warnings", "recommendations", "fittingData", "getFittingData", "push", "configDescription", "includes", "result", "handleElbowKFactor", "handleTeeKFactor", "handleTransitionKFactor", "handleDamperKFactor", "handleGenericKFactor", "Error", "message", "ductShape", "radius_to_diameter_ratios", "ratio", "parameter", "toString", "ratioData", "parseFloat", "K", "configurations", "configType", "configData", "flowPattern", "subtype", "areaRatio", "flow_patterns", "area_ratios", "kData", "length_to_diameter_ratios", "angle", "angleData", "opening_angles", "angleNum", "types", "Object", "keys", "subtypeData", "getAvailableFittings", "getFittingMetadata", "exports"], "sources": ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\FittingLossCalculator.ts"], "sourcesContent": ["/**\r\n * FittingLossCalculator - Modular calculation service for HVAC fitting losses\r\n * \r\n * MISSION-CRITICAL: Pure TypeScript functions for ASHRAE/SMACNA-compliant fitting loss calculations\r\n * Handles minor losses for elbows, tees, transitions, diffusers, and other HVAC components\r\n * \r\n * @see docs/implementation/duct-physics/fitting-loss-calculations.md\r\n * @see backend/data/fitting_coefficients.json\r\n */\r\n\r\nimport { readFileSync } from 'fs';\r\nimport { join } from 'path';\r\n\r\n// Load fitting coefficients data\r\nconst loadFittingCoefficients = () => {\r\n  try {\r\n    const dataPath = join(__dirname, '../../data/fitting_coefficients.json');\r\n    const rawData = readFileSync(dataPath, 'utf8');\r\n    return JSON.parse(rawData);\r\n  } catch (error) {\r\n    console.warn('Could not load fitting coefficients, using fallback data');\r\n    return {\r\n      metadata: { version: '1.0.0', standard: 'ASHRAE/SMACNA' },\r\n      round_fittings: { elbows: {}, tees: {}, transitions: {}, entries_exits: {} },\r\n      rectangular_fittings: { elbows: {}, transitions: {} },\r\n      special_fittings: { dampers: {}, diffusers: {} }\r\n    };\r\n  }\r\n};\r\n\r\nconst fittingCoefficients = loadFittingCoefficients();\r\n\r\n/**\r\n * Fitting configuration for loss calculations\r\n */\r\nexport interface FittingConfiguration {\r\n  type: string; // e.g., '90deg_round_smooth', 'tee_round_branch_90deg'\r\n  subtype?: string; // e.g., 'radius_to_diameter_ratios', 'flow_patterns'\r\n  parameter?: string | number; // e.g., '1.5', 0.5, 'straight_through'\r\n  ductShape: 'round' | 'rectangular';\r\n  diameter?: number; // inches (for round ducts)\r\n  width?: number; // inches (for rectangular ducts)\r\n  height?: number; // inches (for rectangular ducts)\r\n  additionalParams?: Record<string, any>; // For complex fittings\r\n}\r\n\r\n/**\r\n * Fitting loss calculation result\r\n */\r\nexport interface FittingLossResult {\r\n  kFactor: number; // Loss coefficient\r\n  pressureLoss: number; // inches w.g.\r\n  velocityPressure: number; // inches w.g.\r\n  fittingType: string;\r\n  configuration: string;\r\n  warnings: string[];\r\n  recommendations: string[];\r\n}\r\n\r\n/**\r\n * Velocity pressure calculation input\r\n */\r\nexport interface VelocityPressureInput {\r\n  velocity: number; // FPM\r\n  airDensity?: number; // lb/ft³ (default: 0.075 at standard conditions)\r\n}\r\n\r\n/**\r\n * FittingLossCalculator - Pure calculation functions for fitting losses\r\n * CRITICAL: No dependencies on UI, storage, or external services\r\n */\r\nexport class FittingLossCalculator {\r\n  \r\n  /**\r\n   * Calculate velocity pressure from air velocity\r\n   * Formula: VP = (V/4005)² for standard air density\r\n   */\r\n  public static calculateVelocityPressure(input: VelocityPressureInput): number {\r\n    const { velocity, airDensity = 0.075 } = input;\r\n    \r\n    // Standard formula: VP = ρV²/(2gc) converted to inches w.g.\r\n    // Simplified for standard conditions: VP = (V/4005)²\r\n    const standardVP = Math.pow(velocity / 4005, 2);\r\n    \r\n    // Adjust for non-standard air density\r\n    const densityRatio = airDensity / 0.075;\r\n    return standardVP * densityRatio;\r\n  }\r\n\r\n  /**\r\n   * Calculate fitting loss for a specific fitting configuration\r\n   */\r\n  public static calculateFittingLoss(\r\n    config: FittingConfiguration,\r\n    velocity: number,\r\n    airDensity: number = 0.075\r\n  ): FittingLossResult {\r\n    \r\n    // Calculate velocity pressure\r\n    const velocityPressure = this.calculateVelocityPressure({ velocity, airDensity });\r\n    \r\n    // Get K-factor for the fitting\r\n    const kFactorResult = this.getKFactor(config);\r\n    \r\n    // Calculate pressure loss: ΔP = K × VP\r\n    const pressureLoss = kFactorResult.kFactor * velocityPressure;\r\n    \r\n    return {\r\n      kFactor: kFactorResult.kFactor,\r\n      pressureLoss,\r\n      velocityPressure,\r\n      fittingType: config.type,\r\n      configuration: kFactorResult.configuration,\r\n      warnings: kFactorResult.warnings,\r\n      recommendations: kFactorResult.recommendations\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get K-factor for a specific fitting configuration\r\n   */\r\n  private static getKFactor(config: FittingConfiguration): {\r\n    kFactor: number;\r\n    configuration: string;\r\n    warnings: string[];\r\n    recommendations: string[];\r\n  } {\r\n    const warnings: string[] = [];\r\n    const recommendations: string[] = [];\r\n    \r\n    try {\r\n      const fittingData = this.getFittingData(config);\r\n      \r\n      if (!fittingData) {\r\n        warnings.push(`Fitting type '${config.type}' not found in database`);\r\n        return { kFactor: 0.5, configuration: 'default', warnings, recommendations };\r\n      }\r\n\r\n      // Handle different fitting data structures\r\n      let kFactor: number;\r\n      let configDescription: string;\r\n\r\n      if (config.type.includes('elbow')) {\r\n        const result = this.handleElbowKFactor(fittingData, config);\r\n        kFactor = result.kFactor;\r\n        configDescription = result.configuration;\r\n        warnings.push(...result.warnings);\r\n        recommendations.push(...result.recommendations);\r\n      } else if (config.type.includes('tee')) {\r\n        const result = this.handleTeeKFactor(fittingData, config);\r\n        kFactor = result.kFactor;\r\n        configDescription = result.configuration;\r\n        warnings.push(...result.warnings);\r\n        recommendations.push(...result.recommendations);\r\n      } else if (config.type.includes('transition')) {\r\n        const result = this.handleTransitionKFactor(fittingData, config);\r\n        kFactor = result.kFactor;\r\n        configDescription = result.configuration;\r\n        warnings.push(...result.warnings);\r\n        recommendations.push(...result.recommendations);\r\n      } else if (config.type.includes('damper')) {\r\n        const result = this.handleDamperKFactor(fittingData, config);\r\n        kFactor = result.kFactor;\r\n        configDescription = result.configuration;\r\n        warnings.push(...result.warnings);\r\n        recommendations.push(...result.recommendations);\r\n      } else {\r\n        // Generic fitting handling\r\n        const result = this.handleGenericKFactor(fittingData, config);\r\n        kFactor = result.kFactor;\r\n        configDescription = result.configuration;\r\n        warnings.push(...result.warnings);\r\n        recommendations.push(...result.recommendations);\r\n      }\r\n\r\n      return { kFactor, configuration: configDescription, warnings, recommendations };\r\n\r\n    } catch (error) {\r\n      warnings.push(`Error calculating K-factor: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n      return { kFactor: 0.5, configuration: 'error_default', warnings, recommendations };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get fitting data from the coefficients database\r\n   */\r\n  private static getFittingData(config: FittingConfiguration): any {\r\n    const { type, ductShape } = config;\r\n    \r\n    if (ductShape === 'round') {\r\n      return fittingCoefficients.round_fittings.elbows[type] ||\r\n             fittingCoefficients.round_fittings.tees[type] ||\r\n             fittingCoefficients.round_fittings.transitions[type] ||\r\n             fittingCoefficients.round_fittings.entries_exits[type] ||\r\n             fittingCoefficients.special_fittings.dampers[type] ||\r\n             fittingCoefficients.special_fittings.diffusers[type];\r\n    } else if (ductShape === 'rectangular') {\r\n      return fittingCoefficients.rectangular_fittings.elbows[type] ||\r\n             fittingCoefficients.rectangular_fittings.transitions[type] ||\r\n             fittingCoefficients.special_fittings.dampers[type] ||\r\n             fittingCoefficients.special_fittings.diffusers[type];\r\n    }\r\n    \r\n    return null;\r\n  }\r\n\r\n  /**\r\n   * Handle elbow K-factor calculation\r\n   */\r\n  private static handleElbowKFactor(fittingData: any, config: FittingConfiguration): {\r\n    kFactor: number;\r\n    configuration: string;\r\n    warnings: string[];\r\n    recommendations: string[];\r\n  } {\r\n    const warnings: string[] = [];\r\n    const recommendations: string[] = [];\r\n    \r\n    if (fittingData.radius_to_diameter_ratios) {\r\n      const ratio = config.parameter?.toString() || '1.0';\r\n      const ratioData = fittingData.radius_to_diameter_ratios[ratio];\r\n      \r\n      if (ratioData) {\r\n        if (parseFloat(ratio) < 1.0) {\r\n          warnings.push('Sharp radius elbow may cause excessive pressure loss');\r\n          recommendations.push('Consider using radius-to-diameter ratio ≥ 1.5 for optimal performance');\r\n        }\r\n        return {\r\n          kFactor: ratioData.K,\r\n          configuration: `R/D = ${ratio}`,\r\n          warnings,\r\n          recommendations\r\n        };\r\n      }\r\n    }\r\n    \r\n    if (fittingData.configurations) {\r\n      const configType = config.parameter?.toString() || 'single_miter';\r\n      const configData = fittingData.configurations[configType];\r\n      \r\n      if (configData) {\r\n        if (configType === 'single_miter') {\r\n          warnings.push('Single miter elbow has high pressure loss');\r\n          recommendations.push('Consider using multiple miters or smooth radius elbow');\r\n        }\r\n        return {\r\n          kFactor: configData.K,\r\n          configuration: configType,\r\n          warnings,\r\n          recommendations\r\n        };\r\n      }\r\n    }\r\n    \r\n    warnings.push('Elbow configuration not found, using default');\r\n    return { kFactor: 0.3, configuration: 'default', warnings, recommendations };\r\n  }\r\n\r\n  /**\r\n   * Handle tee K-factor calculation\r\n   */\r\n  private static handleTeeKFactor(fittingData: any, config: FittingConfiguration): {\r\n    kFactor: number;\r\n    configuration: string;\r\n    warnings: string[];\r\n    recommendations: string[];\r\n  } {\r\n    const warnings: string[] = [];\r\n    const recommendations: string[] = [];\r\n    \r\n    const flowPattern = config.subtype || 'straight_through';\r\n    const areaRatio = config.parameter?.toString() || '0.5';\r\n    \r\n    if (fittingData.flow_patterns?.[flowPattern]?.area_ratios?.[areaRatio]) {\r\n      const kData = fittingData.flow_patterns[flowPattern].area_ratios[areaRatio];\r\n      \r\n      if (parseFloat(areaRatio) > 0.75) {\r\n        warnings.push('Large branch area ratio may cause flow imbalance');\r\n        recommendations.push('Consider flow balancing dampers for large branches');\r\n      }\r\n      \r\n      return {\r\n        kFactor: kData.K,\r\n        configuration: `${flowPattern}, area ratio = ${areaRatio}`,\r\n        warnings,\r\n        recommendations\r\n      };\r\n    }\r\n    \r\n    warnings.push('Tee configuration not found, using default');\r\n    return { kFactor: 0.6, configuration: 'default', warnings, recommendations };\r\n  }\r\n\r\n  /**\r\n   * Handle transition K-factor calculation\r\n   */\r\n  private static handleTransitionKFactor(fittingData: any, config: FittingConfiguration): {\r\n    kFactor: number;\r\n    configuration: string;\r\n    warnings: string[];\r\n    recommendations: string[];\r\n  } {\r\n    const warnings: string[] = [];\r\n    const recommendations: string[] = [];\r\n    \r\n    if (fittingData.length_to_diameter_ratios) {\r\n      const ratio = config.parameter?.toString() || '1.0';\r\n      const ratioData = fittingData.length_to_diameter_ratios[ratio];\r\n      \r\n      if (ratioData) {\r\n        if (parseFloat(ratio) < 1.5) {\r\n          warnings.push('Short transition may cause flow separation');\r\n          recommendations.push('Consider using length-to-diameter ratio ≥ 2.0 for gradual transition');\r\n        }\r\n        return {\r\n          kFactor: ratioData.K,\r\n          configuration: `L/D = ${ratio}`,\r\n          warnings,\r\n          recommendations\r\n        };\r\n      }\r\n    }\r\n    \r\n    warnings.push('Transition configuration not found, using default');\r\n    return { kFactor: 0.2, configuration: 'default', warnings, recommendations };\r\n  }\r\n\r\n  /**\r\n   * Handle damper K-factor calculation\r\n   */\r\n  private static handleDamperKFactor(fittingData: any, config: FittingConfiguration): {\r\n    kFactor: number;\r\n    configuration: string;\r\n    warnings: string[];\r\n    recommendations: string[];\r\n  } {\r\n    const warnings: string[] = [];\r\n    const recommendations: string[] = [];\r\n    \r\n    const angle = config.parameter?.toString() || '90';\r\n    const angleData = fittingData.opening_angles?.[angle];\r\n    \r\n    if (angleData) {\r\n      const angleNum = parseFloat(angle);\r\n      if (angleNum < 45) {\r\n        warnings.push('Damper significantly restricting flow');\r\n        recommendations.push('Consider opening damper further to reduce pressure loss');\r\n      }\r\n      \r\n      return {\r\n        kFactor: angleData.K,\r\n        configuration: `${angle}° open`,\r\n        warnings,\r\n        recommendations\r\n      };\r\n    }\r\n    \r\n    warnings.push('Damper angle not found, using default');\r\n    return { kFactor: 0.2, configuration: 'default', warnings, recommendations };\r\n  }\r\n\r\n  /**\r\n   * Handle generic fitting K-factor calculation\r\n   */\r\n  private static handleGenericKFactor(fittingData: any, config: FittingConfiguration): {\r\n    kFactor: number;\r\n    configuration: string;\r\n    warnings: string[];\r\n    recommendations: string[];\r\n  } {\r\n    const warnings: string[] = [];\r\n    const recommendations: string[] = [];\r\n    \r\n    // Try to find K value in various structures\r\n    if (typeof fittingData.K === 'number') {\r\n      return { kFactor: fittingData.K, configuration: 'direct', warnings, recommendations };\r\n    }\r\n    \r\n    if (fittingData.types) {\r\n      const subtype = config.subtype || Object.keys(fittingData.types)[0];\r\n      const subtypeData = fittingData.types[subtype];\r\n      if (subtypeData?.K) {\r\n        return {\r\n          kFactor: subtypeData.K,\r\n          configuration: subtype,\r\n          warnings,\r\n          recommendations\r\n        };\r\n      }\r\n    }\r\n    \r\n    warnings.push('Generic fitting configuration not found, using default');\r\n    return { kFactor: 0.5, configuration: 'default', warnings, recommendations };\r\n  }\r\n\r\n  /**\r\n   * Get available fitting types for a duct shape\r\n   */\r\n  public static getAvailableFittings(ductShape: 'round' | 'rectangular'): string[] {\r\n    if (ductShape === 'round') {\r\n      return [\r\n        ...Object.keys(fittingCoefficients.round_fittings.elbows),\r\n        ...Object.keys(fittingCoefficients.round_fittings.tees),\r\n        ...Object.keys(fittingCoefficients.round_fittings.transitions),\r\n        ...Object.keys(fittingCoefficients.round_fittings.entries_exits),\r\n        ...Object.keys(fittingCoefficients.special_fittings.dampers),\r\n        ...Object.keys(fittingCoefficients.special_fittings.diffusers)\r\n      ];\r\n    } else {\r\n      return [\r\n        ...Object.keys(fittingCoefficients.rectangular_fittings.elbows),\r\n        ...Object.keys(fittingCoefficients.rectangular_fittings.transitions),\r\n        ...Object.keys(fittingCoefficients.special_fittings.dampers),\r\n        ...Object.keys(fittingCoefficients.special_fittings.diffusers)\r\n      ];\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get fitting metadata and description\r\n   */\r\n  public static getFittingMetadata(): typeof fittingCoefficients.metadata {\r\n    return fittingCoefficients.metadata;\r\n  }\r\n}\r\n"], "mappings": ";;AAAA;;;;;;;;;AAAA;AAAA,SAAAA,eAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;MAAA;QAAAC,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAO,SAAA;MAAA;QAAAD,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAW,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,cAAA;MAAAC,IAAA;MAAAC,QAAA;MAAAC,KAAA;MAAAC,OAAA;MAAAC,cAAA;MAAAC,OAAA;IAAA;IAAAC,eAAA;IAAA5B,IAAA;EAAA;EAAA,IAAA6B,QAAA,GAAA5B,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAA0B,QAAA,CAAA9B,IAAA,KAAA8B,QAAA,CAAA9B,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAA6B,QAAA,CAAA9B,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAA0B,cAAA,GAAAD,QAAA,CAAA9B,IAAA;EAAA;IAcM;IAAAD,cAAA,YAAAA,CAAA;MAAA,OAAAgC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAhC,cAAA;AAAAA,cAAA,GAAAoB,CAAA;;;;;;;AAJN,MAAAa,IAAA;AAAA;AAAA,CAAAjC,cAAA,GAAAoB,CAAA,OAAAc,OAAA;AACA,MAAAC,MAAA;AAAA;AAAA,CAAAnC,cAAA,GAAAoB,CAAA,OAAAc,OAAA;AAEA;AAAA;AAAAlC,cAAA,GAAAoB,CAAA;AACA,MAAMgB,uBAAuB,GAAGA,CAAA,KAAK;EAAA;EAAApC,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EACnC,IAAI;IACF,MAAMiB,QAAQ;IAAA;IAAA,CAAArC,cAAA,GAAAoB,CAAA,OAAG,IAAAe,MAAA,CAAAG,IAAI,EAACC,SAAS,EAAE,sCAAsC,CAAC;IACxE,MAAMC,OAAO;IAAA;IAAA,CAAAxC,cAAA,GAAAoB,CAAA,OAAG,IAAAa,IAAA,CAAAQ,YAAY,EAACJ,QAAQ,EAAE,MAAM,CAAC;IAAC;IAAArC,cAAA,GAAAoB,CAAA;IAC/C,OAAOsB,IAAI,CAACC,KAAK,CAACH,OAAO,CAAC;EAC5B,CAAC,CAAC,OAAOI,KAAK,EAAE;IAAA;IAAA5C,cAAA,GAAAoB,CAAA;IACdyB,OAAO,CAACC,IAAI,CAAC,0DAA0D,CAAC;IAAC;IAAA9C,cAAA,GAAAoB,CAAA;IACzE,OAAO;MACL2B,QAAQ,EAAE;QAAElB,OAAO,EAAE,OAAO;QAAEmB,QAAQ,EAAE;MAAe,CAAE;MACzDC,cAAc,EAAE;QAAEC,MAAM,EAAE,EAAE;QAAEC,IAAI,EAAE,EAAE;QAAEC,WAAW,EAAE,EAAE;QAAEC,aAAa,EAAE;MAAE,CAAE;MAC5EC,oBAAoB,EAAE;QAAEJ,MAAM,EAAE,EAAE;QAAEE,WAAW,EAAE;MAAE,CAAE;MACrDG,gBAAgB,EAAE;QAAEC,OAAO,EAAE,EAAE;QAAEC,SAAS,EAAE;MAAE;KAC/C;EACH;AACF,CAAC;AAED,MAAMC,mBAAmB;AAAA;AAAA,CAAA1D,cAAA,GAAAoB,CAAA,QAAGgB,uBAAuB,EAAE;AAqCrD;;;;AAIA,MAAauB,qBAAqB;EAEhC;;;;EAIO,OAAOC,yBAAyBA,CAACC,KAA4B;IAAA;IAAA7D,cAAA,GAAAqB,CAAA;IAClE,MAAM;MAAEyC,QAAQ;MAAEC,UAAU;MAAA;MAAA,CAAA/D,cAAA,GAAAsB,CAAA,UAAG,KAAK;IAAA,CAAE;IAAA;IAAA,CAAAtB,cAAA,GAAAoB,CAAA,QAAGyC,KAAK;IAE9C;IACA;IACA,MAAMG,UAAU;IAAA;IAAA,CAAAhE,cAAA,GAAAoB,CAAA,QAAG6C,IAAI,CAACC,GAAG,CAACJ,QAAQ,GAAG,IAAI,EAAE,CAAC,CAAC;IAE/C;IACA,MAAMK,YAAY;IAAA;IAAA,CAAAnE,cAAA,GAAAoB,CAAA,QAAG2C,UAAU,GAAG,KAAK;IAAC;IAAA/D,cAAA,GAAAoB,CAAA;IACxC,OAAO4C,UAAU,GAAGG,YAAY;EAClC;EAEA;;;EAGO,OAAOC,oBAAoBA,CAChCC,MAA4B,EAC5BP,QAAgB,EAChBC,UAAA;EAAA;EAAA,CAAA/D,cAAA,GAAAsB,CAAA,UAAqB,KAAK;IAAA;IAAAtB,cAAA,GAAAqB,CAAA;IAG1B;IACA,MAAMiD,gBAAgB;IAAA;IAAA,CAAAtE,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACwC,yBAAyB,CAAC;MAAEE,QAAQ;MAAEC;IAAU,CAAE,CAAC;IAEjF;IACA,MAAMQ,aAAa;IAAA;IAAA,CAAAvE,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACoD,UAAU,CAACH,MAAM,CAAC;IAE7C;IACA,MAAMI,YAAY;IAAA;IAAA,CAAAzE,cAAA,GAAAoB,CAAA,QAAGmD,aAAa,CAACG,OAAO,GAAGJ,gBAAgB;IAAC;IAAAtE,cAAA,GAAAoB,CAAA;IAE9D,OAAO;MACLsD,OAAO,EAAEH,aAAa,CAACG,OAAO;MAC9BD,YAAY;MACZH,gBAAgB;MAChBK,WAAW,EAAEN,MAAM,CAACpD,IAAI;MACxB2D,aAAa,EAAEL,aAAa,CAACK,aAAa;MAC1CC,QAAQ,EAAEN,aAAa,CAACM,QAAQ;MAChCC,eAAe,EAAEP,aAAa,CAACO;KAChC;EACH;EAEA;;;EAGQ,OAAON,UAAUA,CAACH,MAA4B;IAAA;IAAArE,cAAA,GAAAqB,CAAA;IAMpD,MAAMwD,QAAQ;IAAA;IAAA,CAAA7E,cAAA,GAAAoB,CAAA,QAAa,EAAE;IAC7B,MAAM0D,eAAe;IAAA;IAAA,CAAA9E,cAAA,GAAAoB,CAAA,QAAa,EAAE;IAAC;IAAApB,cAAA,GAAAoB,CAAA;IAErC,IAAI;MACF,MAAM2D,WAAW;MAAA;MAAA,CAAA/E,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAAC4D,cAAc,CAACX,MAAM,CAAC;MAAC;MAAArE,cAAA,GAAAoB,CAAA;MAEhD,IAAI,CAAC2D,WAAW,EAAE;QAAA;QAAA/E,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAChByD,QAAQ,CAACI,IAAI,CAAC,iBAAiBZ,MAAM,CAACpD,IAAI,yBAAyB,CAAC;QAAC;QAAAjB,cAAA,GAAAoB,CAAA;QACrE,OAAO;UAAEsD,OAAO,EAAE,GAAG;UAAEE,aAAa,EAAE,SAAS;UAAEC,QAAQ;UAAEC;QAAe,CAAE;MAC9E,CAAC;MAAA;MAAA;QAAA9E,cAAA,GAAAsB,CAAA;MAAA;MAED;MACA,IAAIoD,OAAe;MACnB,IAAIQ,iBAAyB;MAAC;MAAAlF,cAAA,GAAAoB,CAAA;MAE9B,IAAIiD,MAAM,CAACpD,IAAI,CAACkE,QAAQ,CAAC,OAAO,CAAC,EAAE;QAAA;QAAAnF,cAAA,GAAAsB,CAAA;QACjC,MAAM8D,MAAM;QAAA;QAAA,CAAApF,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACiE,kBAAkB,CAACN,WAAW,EAAEV,MAAM,CAAC;QAAC;QAAArE,cAAA,GAAAoB,CAAA;QAC5DsD,OAAO,GAAGU,MAAM,CAACV,OAAO;QAAC;QAAA1E,cAAA,GAAAoB,CAAA;QACzB8D,iBAAiB,GAAGE,MAAM,CAACR,aAAa;QAAC;QAAA5E,cAAA,GAAAoB,CAAA;QACzCyD,QAAQ,CAACI,IAAI,CAAC,GAAGG,MAAM,CAACP,QAAQ,CAAC;QAAC;QAAA7E,cAAA,GAAAoB,CAAA;QAClC0D,eAAe,CAACG,IAAI,CAAC,GAAGG,MAAM,CAACN,eAAe,CAAC;MACjD,CAAC,MAAM;QAAA;QAAA9E,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAAA,IAAIiD,MAAM,CAACpD,IAAI,CAACkE,QAAQ,CAAC,KAAK,CAAC,EAAE;UAAA;UAAAnF,cAAA,GAAAsB,CAAA;UACtC,MAAM8D,MAAM;UAAA;UAAA,CAAApF,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACkE,gBAAgB,CAACP,WAAW,EAAEV,MAAM,CAAC;UAAC;UAAArE,cAAA,GAAAoB,CAAA;UAC1DsD,OAAO,GAAGU,MAAM,CAACV,OAAO;UAAC;UAAA1E,cAAA,GAAAoB,CAAA;UACzB8D,iBAAiB,GAAGE,MAAM,CAACR,aAAa;UAAC;UAAA5E,cAAA,GAAAoB,CAAA;UACzCyD,QAAQ,CAACI,IAAI,CAAC,GAAGG,MAAM,CAACP,QAAQ,CAAC;UAAC;UAAA7E,cAAA,GAAAoB,CAAA;UAClC0D,eAAe,CAACG,IAAI,CAAC,GAAGG,MAAM,CAACN,eAAe,CAAC;QACjD,CAAC,MAAM;UAAA;UAAA9E,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAoB,CAAA;UAAA,IAAIiD,MAAM,CAACpD,IAAI,CAACkE,QAAQ,CAAC,YAAY,CAAC,EAAE;YAAA;YAAAnF,cAAA,GAAAsB,CAAA;YAC7C,MAAM8D,MAAM;YAAA;YAAA,CAAApF,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACmE,uBAAuB,CAACR,WAAW,EAAEV,MAAM,CAAC;YAAC;YAAArE,cAAA,GAAAoB,CAAA;YACjEsD,OAAO,GAAGU,MAAM,CAACV,OAAO;YAAC;YAAA1E,cAAA,GAAAoB,CAAA;YACzB8D,iBAAiB,GAAGE,MAAM,CAACR,aAAa;YAAC;YAAA5E,cAAA,GAAAoB,CAAA;YACzCyD,QAAQ,CAACI,IAAI,CAAC,GAAGG,MAAM,CAACP,QAAQ,CAAC;YAAC;YAAA7E,cAAA,GAAAoB,CAAA;YAClC0D,eAAe,CAACG,IAAI,CAAC,GAAGG,MAAM,CAACN,eAAe,CAAC;UACjD,CAAC,MAAM;YAAA;YAAA9E,cAAA,GAAAsB,CAAA;YAAAtB,cAAA,GAAAoB,CAAA;YAAA,IAAIiD,MAAM,CAACpD,IAAI,CAACkE,QAAQ,CAAC,QAAQ,CAAC,EAAE;cAAA;cAAAnF,cAAA,GAAAsB,CAAA;cACzC,MAAM8D,MAAM;cAAA;cAAA,CAAApF,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACoE,mBAAmB,CAACT,WAAW,EAAEV,MAAM,CAAC;cAAC;cAAArE,cAAA,GAAAoB,CAAA;cAC7DsD,OAAO,GAAGU,MAAM,CAACV,OAAO;cAAC;cAAA1E,cAAA,GAAAoB,CAAA;cACzB8D,iBAAiB,GAAGE,MAAM,CAACR,aAAa;cAAC;cAAA5E,cAAA,GAAAoB,CAAA;cACzCyD,QAAQ,CAACI,IAAI,CAAC,GAAGG,MAAM,CAACP,QAAQ,CAAC;cAAC;cAAA7E,cAAA,GAAAoB,CAAA;cAClC0D,eAAe,CAACG,IAAI,CAAC,GAAGG,MAAM,CAACN,eAAe,CAAC;YACjD,CAAC,MAAM;cAAA;cAAA9E,cAAA,GAAAsB,CAAA;cACL;cACA,MAAM8D,MAAM;cAAA;cAAA,CAAApF,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACqE,oBAAoB,CAACV,WAAW,EAAEV,MAAM,CAAC;cAAC;cAAArE,cAAA,GAAAoB,CAAA;cAC9DsD,OAAO,GAAGU,MAAM,CAACV,OAAO;cAAC;cAAA1E,cAAA,GAAAoB,CAAA;cACzB8D,iBAAiB,GAAGE,MAAM,CAACR,aAAa;cAAC;cAAA5E,cAAA,GAAAoB,CAAA;cACzCyD,QAAQ,CAACI,IAAI,CAAC,GAAGG,MAAM,CAACP,QAAQ,CAAC;cAAC;cAAA7E,cAAA,GAAAoB,CAAA;cAClC0D,eAAe,CAACG,IAAI,CAAC,GAAGG,MAAM,CAACN,eAAe,CAAC;YACjD;UAAA;QAAA;MAAA;MAAC;MAAA9E,cAAA,GAAAoB,CAAA;MAED,OAAO;QAAEsD,OAAO;QAAEE,aAAa,EAAEM,iBAAiB;QAAEL,QAAQ;QAAEC;MAAe,CAAE;IAEjF,CAAC,CAAC,OAAOlC,KAAK,EAAE;MAAA;MAAA5C,cAAA,GAAAoB,CAAA;MACdyD,QAAQ,CAACI,IAAI,CAAC,+BAA+BrC,KAAK,YAAY8C,KAAK;MAAA;MAAA,CAAA1F,cAAA,GAAAsB,CAAA,UAAGsB,KAAK,CAAC+C,OAAO;MAAA;MAAA,CAAA3F,cAAA,GAAAsB,CAAA,UAAG,eAAe,GAAE,CAAC;MAAC;MAAAtB,cAAA,GAAAoB,CAAA;MACzG,OAAO;QAAEsD,OAAO,EAAE,GAAG;QAAEE,aAAa,EAAE,eAAe;QAAEC,QAAQ;QAAEC;MAAe,CAAE;IACpF;EACF;EAEA;;;EAGQ,OAAOE,cAAcA,CAACX,MAA4B;IAAA;IAAArE,cAAA,GAAAqB,CAAA;IACxD,MAAM;MAAEJ,IAAI;MAAE2E;IAAS,CAAE;IAAA;IAAA,CAAA5F,cAAA,GAAAoB,CAAA,QAAGiD,MAAM;IAAC;IAAArE,cAAA,GAAAoB,CAAA;IAEnC,IAAIwE,SAAS,KAAK,OAAO,EAAE;MAAA;MAAA5F,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACzB,OAAO,2BAAApB,cAAA,GAAAsB,CAAA,UAAAoC,mBAAmB,CAACT,cAAc,CAACC,MAAM,CAACjC,IAAI,CAAC;MAAA;MAAA,CAAAjB,cAAA,GAAAsB,CAAA,UAC/CoC,mBAAmB,CAACT,cAAc,CAACE,IAAI,CAAClC,IAAI,CAAC;MAAA;MAAA,CAAAjB,cAAA,GAAAsB,CAAA,UAC7CoC,mBAAmB,CAACT,cAAc,CAACG,WAAW,CAACnC,IAAI,CAAC;MAAA;MAAA,CAAAjB,cAAA,GAAAsB,CAAA,UACpDoC,mBAAmB,CAACT,cAAc,CAACI,aAAa,CAACpC,IAAI,CAAC;MAAA;MAAA,CAAAjB,cAAA,GAAAsB,CAAA,UACtDoC,mBAAmB,CAACH,gBAAgB,CAACC,OAAO,CAACvC,IAAI,CAAC;MAAA;MAAA,CAAAjB,cAAA,GAAAsB,CAAA,UAClDoC,mBAAmB,CAACH,gBAAgB,CAACE,SAAS,CAACxC,IAAI,CAAC;IAC7D,CAAC,MAAM;MAAA;MAAAjB,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,IAAIwE,SAAS,KAAK,aAAa,EAAE;QAAA;QAAA5F,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACtC,OAAO,2BAAApB,cAAA,GAAAsB,CAAA,WAAAoC,mBAAmB,CAACJ,oBAAoB,CAACJ,MAAM,CAACjC,IAAI,CAAC;QAAA;QAAA,CAAAjB,cAAA,GAAAsB,CAAA,WACrDoC,mBAAmB,CAACJ,oBAAoB,CAACF,WAAW,CAACnC,IAAI,CAAC;QAAA;QAAA,CAAAjB,cAAA,GAAAsB,CAAA,WAC1DoC,mBAAmB,CAACH,gBAAgB,CAACC,OAAO,CAACvC,IAAI,CAAC;QAAA;QAAA,CAAAjB,cAAA,GAAAsB,CAAA,WAClDoC,mBAAmB,CAACH,gBAAgB,CAACE,SAAS,CAACxC,IAAI,CAAC;MAC7D,CAAC;MAAA;MAAA;QAAAjB,cAAA,GAAAsB,CAAA;MAAA;IAAD;IAAC;IAAAtB,cAAA,GAAAoB,CAAA;IAED,OAAO,IAAI;EACb;EAEA;;;EAGQ,OAAOiE,kBAAkBA,CAACN,WAAgB,EAAEV,MAA4B;IAAA;IAAArE,cAAA,GAAAqB,CAAA;IAM9E,MAAMwD,QAAQ;IAAA;IAAA,CAAA7E,cAAA,GAAAoB,CAAA,QAAa,EAAE;IAC7B,MAAM0D,eAAe;IAAA;IAAA,CAAA9E,cAAA,GAAAoB,CAAA,QAAa,EAAE;IAAC;IAAApB,cAAA,GAAAoB,CAAA;IAErC,IAAI2D,WAAW,CAACc,yBAAyB,EAAE;MAAA;MAAA7F,cAAA,GAAAsB,CAAA;MACzC,MAAMwE,KAAK;MAAA;MAAA,CAAA9F,cAAA,GAAAoB,CAAA;MAAG;MAAA,CAAApB,cAAA,GAAAsB,CAAA,WAAA+C,MAAM,CAAC0B,SAAS,EAAEC,QAAQ,EAAE;MAAA;MAAA,CAAAhG,cAAA,GAAAsB,CAAA,WAAI,KAAK;MACnD,MAAM2E,SAAS;MAAA;MAAA,CAAAjG,cAAA,GAAAoB,CAAA,QAAG2D,WAAW,CAACc,yBAAyB,CAACC,KAAK,CAAC;MAAC;MAAA9F,cAAA,GAAAoB,CAAA;MAE/D,IAAI6E,SAAS,EAAE;QAAA;QAAAjG,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACb,IAAI8E,UAAU,CAACJ,KAAK,CAAC,GAAG,GAAG,EAAE;UAAA;UAAA9F,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAoB,CAAA;UAC3ByD,QAAQ,CAACI,IAAI,CAAC,sDAAsD,CAAC;UAAC;UAAAjF,cAAA,GAAAoB,CAAA;UACtE0D,eAAe,CAACG,IAAI,CAAC,uEAAuE,CAAC;QAC/F,CAAC;QAAA;QAAA;UAAAjF,cAAA,GAAAsB,CAAA;QAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACD,OAAO;UACLsD,OAAO,EAAEuB,SAAS,CAACE,CAAC;UACpBvB,aAAa,EAAE,SAASkB,KAAK,EAAE;UAC/BjB,QAAQ;UACRC;SACD;MACH,CAAC;MAAA;MAAA;QAAA9E,cAAA,GAAAsB,CAAA;MAAA;IACH,CAAC;IAAA;IAAA;MAAAtB,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAED,IAAI2D,WAAW,CAACqB,cAAc,EAAE;MAAA;MAAApG,cAAA,GAAAsB,CAAA;MAC9B,MAAM+E,UAAU;MAAA;MAAA,CAAArG,cAAA,GAAAoB,CAAA;MAAG;MAAA,CAAApB,cAAA,GAAAsB,CAAA,WAAA+C,MAAM,CAAC0B,SAAS,EAAEC,QAAQ,EAAE;MAAA;MAAA,CAAAhG,cAAA,GAAAsB,CAAA,WAAI,cAAc;MACjE,MAAMgF,UAAU;MAAA;MAAA,CAAAtG,cAAA,GAAAoB,CAAA,QAAG2D,WAAW,CAACqB,cAAc,CAACC,UAAU,CAAC;MAAC;MAAArG,cAAA,GAAAoB,CAAA;MAE1D,IAAIkF,UAAU,EAAE;QAAA;QAAAtG,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACd,IAAIiF,UAAU,KAAK,cAAc,EAAE;UAAA;UAAArG,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAoB,CAAA;UACjCyD,QAAQ,CAACI,IAAI,CAAC,2CAA2C,CAAC;UAAC;UAAAjF,cAAA,GAAAoB,CAAA;UAC3D0D,eAAe,CAACG,IAAI,CAAC,uDAAuD,CAAC;QAC/E,CAAC;QAAA;QAAA;UAAAjF,cAAA,GAAAsB,CAAA;QAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACD,OAAO;UACLsD,OAAO,EAAE4B,UAAU,CAACH,CAAC;UACrBvB,aAAa,EAAEyB,UAAU;UACzBxB,QAAQ;UACRC;SACD;MACH,CAAC;MAAA;MAAA;QAAA9E,cAAA,GAAAsB,CAAA;MAAA;IACH,CAAC;IAAA;IAAA;MAAAtB,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAEDyD,QAAQ,CAACI,IAAI,CAAC,8CAA8C,CAAC;IAAC;IAAAjF,cAAA,GAAAoB,CAAA;IAC9D,OAAO;MAAEsD,OAAO,EAAE,GAAG;MAAEE,aAAa,EAAE,SAAS;MAAEC,QAAQ;MAAEC;IAAe,CAAE;EAC9E;EAEA;;;EAGQ,OAAOQ,gBAAgBA,CAACP,WAAgB,EAAEV,MAA4B;IAAA;IAAArE,cAAA,GAAAqB,CAAA;IAM5E,MAAMwD,QAAQ;IAAA;IAAA,CAAA7E,cAAA,GAAAoB,CAAA,QAAa,EAAE;IAC7B,MAAM0D,eAAe;IAAA;IAAA,CAAA9E,cAAA,GAAAoB,CAAA,QAAa,EAAE;IAEpC,MAAMmF,WAAW;IAAA;IAAA,CAAAvG,cAAA,GAAAoB,CAAA;IAAG;IAAA,CAAApB,cAAA,GAAAsB,CAAA,WAAA+C,MAAM,CAACmC,OAAO;IAAA;IAAA,CAAAxG,cAAA,GAAAsB,CAAA,WAAI,kBAAkB;IACxD,MAAMmF,SAAS;IAAA;IAAA,CAAAzG,cAAA,GAAAoB,CAAA;IAAG;IAAA,CAAApB,cAAA,GAAAsB,CAAA,WAAA+C,MAAM,CAAC0B,SAAS,EAAEC,QAAQ,EAAE;IAAA;IAAA,CAAAhG,cAAA,GAAAsB,CAAA,WAAI,KAAK;IAAC;IAAAtB,cAAA,GAAAoB,CAAA;IAExD,IAAI2D,WAAW,CAAC2B,aAAa,GAAGH,WAAW,CAAC,EAAEI,WAAW,GAAGF,SAAS,CAAC,EAAE;MAAA;MAAAzG,cAAA,GAAAsB,CAAA;MACtE,MAAMsF,KAAK;MAAA;MAAA,CAAA5G,cAAA,GAAAoB,CAAA,QAAG2D,WAAW,CAAC2B,aAAa,CAACH,WAAW,CAAC,CAACI,WAAW,CAACF,SAAS,CAAC;MAAC;MAAAzG,cAAA,GAAAoB,CAAA;MAE5E,IAAI8E,UAAU,CAACO,SAAS,CAAC,GAAG,IAAI,EAAE;QAAA;QAAAzG,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAChCyD,QAAQ,CAACI,IAAI,CAAC,kDAAkD,CAAC;QAAC;QAAAjF,cAAA,GAAAoB,CAAA;QAClE0D,eAAe,CAACG,IAAI,CAAC,oDAAoD,CAAC;MAC5E,CAAC;MAAA;MAAA;QAAAjF,cAAA,GAAAsB,CAAA;MAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAED,OAAO;QACLsD,OAAO,EAAEkC,KAAK,CAACT,CAAC;QAChBvB,aAAa,EAAE,GAAG2B,WAAW,kBAAkBE,SAAS,EAAE;QAC1D5B,QAAQ;QACRC;OACD;IACH,CAAC;IAAA;IAAA;MAAA9E,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAEDyD,QAAQ,CAACI,IAAI,CAAC,4CAA4C,CAAC;IAAC;IAAAjF,cAAA,GAAAoB,CAAA;IAC5D,OAAO;MAAEsD,OAAO,EAAE,GAAG;MAAEE,aAAa,EAAE,SAAS;MAAEC,QAAQ;MAAEC;IAAe,CAAE;EAC9E;EAEA;;;EAGQ,OAAOS,uBAAuBA,CAACR,WAAgB,EAAEV,MAA4B;IAAA;IAAArE,cAAA,GAAAqB,CAAA;IAMnF,MAAMwD,QAAQ;IAAA;IAAA,CAAA7E,cAAA,GAAAoB,CAAA,QAAa,EAAE;IAC7B,MAAM0D,eAAe;IAAA;IAAA,CAAA9E,cAAA,GAAAoB,CAAA,QAAa,EAAE;IAAC;IAAApB,cAAA,GAAAoB,CAAA;IAErC,IAAI2D,WAAW,CAAC8B,yBAAyB,EAAE;MAAA;MAAA7G,cAAA,GAAAsB,CAAA;MACzC,MAAMwE,KAAK;MAAA;MAAA,CAAA9F,cAAA,GAAAoB,CAAA;MAAG;MAAA,CAAApB,cAAA,GAAAsB,CAAA,WAAA+C,MAAM,CAAC0B,SAAS,EAAEC,QAAQ,EAAE;MAAA;MAAA,CAAAhG,cAAA,GAAAsB,CAAA,WAAI,KAAK;MACnD,MAAM2E,SAAS;MAAA;MAAA,CAAAjG,cAAA,GAAAoB,CAAA,SAAG2D,WAAW,CAAC8B,yBAAyB,CAACf,KAAK,CAAC;MAAC;MAAA9F,cAAA,GAAAoB,CAAA;MAE/D,IAAI6E,SAAS,EAAE;QAAA;QAAAjG,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACb,IAAI8E,UAAU,CAACJ,KAAK,CAAC,GAAG,GAAG,EAAE;UAAA;UAAA9F,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAoB,CAAA;UAC3ByD,QAAQ,CAACI,IAAI,CAAC,4CAA4C,CAAC;UAAC;UAAAjF,cAAA,GAAAoB,CAAA;UAC5D0D,eAAe,CAACG,IAAI,CAAC,sEAAsE,CAAC;QAC9F,CAAC;QAAA;QAAA;UAAAjF,cAAA,GAAAsB,CAAA;QAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACD,OAAO;UACLsD,OAAO,EAAEuB,SAAS,CAACE,CAAC;UACpBvB,aAAa,EAAE,SAASkB,KAAK,EAAE;UAC/BjB,QAAQ;UACRC;SACD;MACH,CAAC;MAAA;MAAA;QAAA9E,cAAA,GAAAsB,CAAA;MAAA;IACH,CAAC;IAAA;IAAA;MAAAtB,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAEDyD,QAAQ,CAACI,IAAI,CAAC,mDAAmD,CAAC;IAAC;IAAAjF,cAAA,GAAAoB,CAAA;IACnE,OAAO;MAAEsD,OAAO,EAAE,GAAG;MAAEE,aAAa,EAAE,SAAS;MAAEC,QAAQ;MAAEC;IAAe,CAAE;EAC9E;EAEA;;;EAGQ,OAAOU,mBAAmBA,CAACT,WAAgB,EAAEV,MAA4B;IAAA;IAAArE,cAAA,GAAAqB,CAAA;IAM/E,MAAMwD,QAAQ;IAAA;IAAA,CAAA7E,cAAA,GAAAoB,CAAA,SAAa,EAAE;IAC7B,MAAM0D,eAAe;IAAA;IAAA,CAAA9E,cAAA,GAAAoB,CAAA,SAAa,EAAE;IAEpC,MAAM0F,KAAK;IAAA;IAAA,CAAA9G,cAAA,GAAAoB,CAAA;IAAG;IAAA,CAAApB,cAAA,GAAAsB,CAAA,WAAA+C,MAAM,CAAC0B,SAAS,EAAEC,QAAQ,EAAE;IAAA;IAAA,CAAAhG,cAAA,GAAAsB,CAAA,WAAI,IAAI;IAClD,MAAMyF,SAAS;IAAA;IAAA,CAAA/G,cAAA,GAAAoB,CAAA,SAAG2D,WAAW,CAACiC,cAAc,GAAGF,KAAK,CAAC;IAAC;IAAA9G,cAAA,GAAAoB,CAAA;IAEtD,IAAI2F,SAAS,EAAE;MAAA;MAAA/G,cAAA,GAAAsB,CAAA;MACb,MAAM2F,QAAQ;MAAA;MAAA,CAAAjH,cAAA,GAAAoB,CAAA,SAAG8E,UAAU,CAACY,KAAK,CAAC;MAAC;MAAA9G,cAAA,GAAAoB,CAAA;MACnC,IAAI6F,QAAQ,GAAG,EAAE,EAAE;QAAA;QAAAjH,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACjByD,QAAQ,CAACI,IAAI,CAAC,uCAAuC,CAAC;QAAC;QAAAjF,cAAA,GAAAoB,CAAA;QACvD0D,eAAe,CAACG,IAAI,CAAC,yDAAyD,CAAC;MACjF,CAAC;MAAA;MAAA;QAAAjF,cAAA,GAAAsB,CAAA;MAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAED,OAAO;QACLsD,OAAO,EAAEqC,SAAS,CAACZ,CAAC;QACpBvB,aAAa,EAAE,GAAGkC,KAAK,QAAQ;QAC/BjC,QAAQ;QACRC;OACD;IACH,CAAC;IAAA;IAAA;MAAA9E,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAEDyD,QAAQ,CAACI,IAAI,CAAC,uCAAuC,CAAC;IAAC;IAAAjF,cAAA,GAAAoB,CAAA;IACvD,OAAO;MAAEsD,OAAO,EAAE,GAAG;MAAEE,aAAa,EAAE,SAAS;MAAEC,QAAQ;MAAEC;IAAe,CAAE;EAC9E;EAEA;;;EAGQ,OAAOW,oBAAoBA,CAACV,WAAgB,EAAEV,MAA4B;IAAA;IAAArE,cAAA,GAAAqB,CAAA;IAMhF,MAAMwD,QAAQ;IAAA;IAAA,CAAA7E,cAAA,GAAAoB,CAAA,SAAa,EAAE;IAC7B,MAAM0D,eAAe;IAAA;IAAA,CAAA9E,cAAA,GAAAoB,CAAA,SAAa,EAAE;IAEpC;IAAA;IAAApB,cAAA,GAAAoB,CAAA;IACA,IAAI,OAAO2D,WAAW,CAACoB,CAAC,KAAK,QAAQ,EAAE;MAAA;MAAAnG,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACrC,OAAO;QAAEsD,OAAO,EAAEK,WAAW,CAACoB,CAAC;QAAEvB,aAAa,EAAE,QAAQ;QAAEC,QAAQ;QAAEC;MAAe,CAAE;IACvF,CAAC;IAAA;IAAA;MAAA9E,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAED,IAAI2D,WAAW,CAACmC,KAAK,EAAE;MAAA;MAAAlH,cAAA,GAAAsB,CAAA;MACrB,MAAMkF,OAAO;MAAA;MAAA,CAAAxG,cAAA,GAAAoB,CAAA;MAAG;MAAA,CAAApB,cAAA,GAAAsB,CAAA,WAAA+C,MAAM,CAACmC,OAAO;MAAA;MAAA,CAAAxG,cAAA,GAAAsB,CAAA,WAAI6F,MAAM,CAACC,IAAI,CAACrC,WAAW,CAACmC,KAAK,CAAC,CAAC,CAAC,CAAC;MACnE,MAAMG,WAAW;MAAA;MAAA,CAAArH,cAAA,GAAAoB,CAAA,SAAG2D,WAAW,CAACmC,KAAK,CAACV,OAAO,CAAC;MAAC;MAAAxG,cAAA,GAAAoB,CAAA;MAC/C,IAAIiG,WAAW,EAAElB,CAAC,EAAE;QAAA;QAAAnG,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAClB,OAAO;UACLsD,OAAO,EAAE2C,WAAW,CAAClB,CAAC;UACtBvB,aAAa,EAAE4B,OAAO;UACtB3B,QAAQ;UACRC;SACD;MACH,CAAC;MAAA;MAAA;QAAA9E,cAAA,GAAAsB,CAAA;MAAA;IACH,CAAC;IAAA;IAAA;MAAAtB,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAEDyD,QAAQ,CAACI,IAAI,CAAC,wDAAwD,CAAC;IAAC;IAAAjF,cAAA,GAAAoB,CAAA;IACxE,OAAO;MAAEsD,OAAO,EAAE,GAAG;MAAEE,aAAa,EAAE,SAAS;MAAEC,QAAQ;MAAEC;IAAe,CAAE;EAC9E;EAEA;;;EAGO,OAAOwC,oBAAoBA,CAAC1B,SAAkC;IAAA;IAAA5F,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACnE,IAAIwE,SAAS,KAAK,OAAO,EAAE;MAAA;MAAA5F,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACzB,OAAO,CACL,GAAG+F,MAAM,CAACC,IAAI,CAAC1D,mBAAmB,CAACT,cAAc,CAACC,MAAM,CAAC,EACzD,GAAGiE,MAAM,CAACC,IAAI,CAAC1D,mBAAmB,CAACT,cAAc,CAACE,IAAI,CAAC,EACvD,GAAGgE,MAAM,CAACC,IAAI,CAAC1D,mBAAmB,CAACT,cAAc,CAACG,WAAW,CAAC,EAC9D,GAAG+D,MAAM,CAACC,IAAI,CAAC1D,mBAAmB,CAACT,cAAc,CAACI,aAAa,CAAC,EAChE,GAAG8D,MAAM,CAACC,IAAI,CAAC1D,mBAAmB,CAACH,gBAAgB,CAACC,OAAO,CAAC,EAC5D,GAAG2D,MAAM,CAACC,IAAI,CAAC1D,mBAAmB,CAACH,gBAAgB,CAACE,SAAS,CAAC,CAC/D;IACH,CAAC,MAAM;MAAA;MAAAzD,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACL,OAAO,CACL,GAAG+F,MAAM,CAACC,IAAI,CAAC1D,mBAAmB,CAACJ,oBAAoB,CAACJ,MAAM,CAAC,EAC/D,GAAGiE,MAAM,CAACC,IAAI,CAAC1D,mBAAmB,CAACJ,oBAAoB,CAACF,WAAW,CAAC,EACpE,GAAG+D,MAAM,CAACC,IAAI,CAAC1D,mBAAmB,CAACH,gBAAgB,CAACC,OAAO,CAAC,EAC5D,GAAG2D,MAAM,CAACC,IAAI,CAAC1D,mBAAmB,CAACH,gBAAgB,CAACE,SAAS,CAAC,CAC/D;IACH;EACF;EAEA;;;EAGO,OAAO8D,kBAAkBA,CAAA;IAAA;IAAAvH,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAC9B,OAAOsC,mBAAmB,CAACX,QAAQ;EACrC;;AACD;AAAA/C,cAAA,GAAAoB,CAAA;AAjWDoG,OAAA,CAAA7D,qBAAA,GAAAA,qBAAA", "ignoreList": []}