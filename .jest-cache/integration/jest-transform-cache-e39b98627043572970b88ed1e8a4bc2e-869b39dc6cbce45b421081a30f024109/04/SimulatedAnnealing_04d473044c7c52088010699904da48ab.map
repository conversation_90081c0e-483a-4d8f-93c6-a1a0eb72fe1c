{"version": 3, "names": ["cov_1p7ah0mc1t", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "name", "decl", "loc", "branchMap", "type", "locations", "undefined", "s", "f", "b", "inputSourceMap", "file", "mappings", "names", "sources", "sourcesContent", "version", "_coverageSchema", "coverage", "actualCoverage", "SystemOptimizationTypes_1", "require", "SimulatedAnnealing", "constructor", "parameters", "currentState", "bestSolution", "history", "evaluationCount", "initialTemperature", "finalTemperature", "maxIterations", "coolingSchedule", "coolingRate", "method", "neighborhoodSize", "neighborhoodMethod", "acceptanceCriterion", "constraintHandling", "penaltyCoefficient", "restartEnabled", "maxRestarts", "restartTemperature", "adaptiveNeighborhood", "seedValue", "random", "createSeededRandom", "Math", "optimize", "problem", "objectiveFunction", "constraintFunctions", "startTime", "performance", "now", "initializeAlgorithm", "createInitialSolution", "shouldTerminate", "performIteration", "updateHistory", "adaptNeighborhoodSize", "shouldRestart", "restart", "createOptimizationResult", "error", "console", "log", "initialSolution", "createRandomSolution", "evaluateSolution", "solution", "temperature", "iteration", "<PERSON><PERSON><PERSON>s", "<PERSON><PERSON><PERSON>s", "improvingMoves", "worseningMoves", "restartCount", "variables", "variable", "discreteValues", "length", "randomIndex", "floor", "id", "min", "bounds", "minimum", "max", "maximum", "generateSolutionId", "objectiveValues", "constraintViolations", "feasible", "fitness", "systemConfiguration", "performanceMetrics", "solutionToVariables", "objectiveValue", "objectives", "constraintFunction", "violation", "push", "constraintId", "violationType", "currentValue", "requiredValue", "severity", "penalty", "every", "v", "totalPenalty", "filter", "reduce", "sum", "Number", "MAX_VALUE", "variableTemplates", "map", "template", "neighborSolution", "generateNeighbor", "deltaE", "acceptanceProbability", "calculateAcceptanceProbability", "updateTemperature", "currentSolution", "neighbor", "variableIds", "Object", "keys", "randomVariableId", "find", "generateNeighborValue", "currentIndex", "indexOf", "minIndex", "maxIndex", "range", "neighborValue", "sigma", "gaussianRandom", "delta", "gamma", "cauchyRandom", "exp", "currentTemperature", "schedule", "linearRate", "acceptanceRate", "targetAcceptanceRate", "adaptationFactor", "recentHistory", "slice", "bestRecent", "h", "bestFitness", "improvement", "abs", "recentIterations", "fitnessV<PERSON>ce", "calculateVariance", "newSolution", "totalMoves", "averageFitness", "worstFitness", "diversity", "timestamp", "Date", "executionTime", "statistics", "totalIterations", "totalEvaluations", "convergenceIteration", "bestFitnessHistory", "averageFitnessHistory", "diversityHistory", "constraintViolationHistory", "algorithmSpecificStats", "finalNeighborhoodSize", "optimizationHistory", "iterations", "populationHistory", "parameterHistory", "convergenceMetrics", "problemId", "status", "OptimizationStatus", "CONVERGED", "analysis", "recommendations", "warnings", "errors", "toString", "substr", "seed", "state", "u1", "u2", "sqrt", "cos", "PI", "u", "tan", "values", "mean", "val", "squaredDiffs", "pow", "diff", "exports"], "sources": ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\algorithms\\SimulatedAnnealing.ts"], "sourcesContent": ["/**\r\n * Simulated Annealing Algorithm Implementation for System Optimization\r\n * \r\n * Implements simulated annealing optimization with:\r\n * - Configurable cooling schedules (linear, exponential, logarithmic, adaptive)\r\n * - Multiple neighborhood generation strategies\r\n * - Constraint handling with penalty methods\r\n * - Adaptive parameter adjustment\r\n * - Restart mechanisms for global optimization\r\n * \r\n * @version 3.0.0\r\n * <AUTHOR> Suite Development Team\r\n */\r\n\r\nimport {\r\n  OptimizationSolution,\r\n  OptimizationVariable,\r\n  OptimizationProblem,\r\n  OptimizationResult,\r\n  OptimizationStatus,\r\n  OptimizationStatistics,\r\n  OptimizationHistory,\r\n  IterationHistory,\r\n  SolutionPerformanceMetrics,\r\n  ObjectiveFunctionType,\r\n  ConstraintFunctionType,\r\n  CoolingSchedule\r\n} from '../types/SystemOptimizationTypes';\r\n\r\nexport interface SimulatedAnnealingParameters {\r\n  initialTemperature: number;\r\n  finalTemperature: number;\r\n  maxIterations: number;\r\n  coolingSchedule: CoolingSchedule;\r\n  neighborhoodSize: number;\r\n  neighborhoodMethod: 'gaussian' | 'uniform' | 'adaptive' | 'cauchy';\r\n  acceptanceCriterion: 'metropolis' | 'boltzmann' | 'fast_annealing';\r\n  constraintHandling: 'penalty' | 'repair' | 'rejection';\r\n  penaltyCoefficient: number;\r\n  restartEnabled: boolean;\r\n  maxRestarts: number;\r\n  restartTemperature: number;\r\n  adaptiveNeighborhood: boolean;\r\n  seedValue?: number;\r\n}\r\n\r\nexport interface SAState {\r\n  solution: OptimizationSolution;\r\n  temperature: number;\r\n  iteration: number;\r\n  acceptedMoves: number;\r\n  rejectedMoves: number;\r\n  improvingMoves: number;\r\n  worseningMoves: number;\r\n  restartCount: number;\r\n}\r\n\r\n/**\r\n * Simulated Annealing optimizer for single-objective optimization problems\r\n */\r\nexport class SimulatedAnnealing {\r\n  private parameters: SimulatedAnnealingParameters;\r\n  private currentState: SAState | null = null;\r\n  private bestSolution: OptimizationSolution | null = null;\r\n  private history: IterationHistory[] = [];\r\n  private random: () => number;\r\n  private evaluationCount: number = 0;\r\n\r\n  constructor(parameters?: Partial<SimulatedAnnealingParameters>) {\r\n    this.parameters = {\r\n      initialTemperature: 1000,\r\n      finalTemperature: 0.01,\r\n      maxIterations: 1000,\r\n      coolingSchedule: {\r\n        initialTemperature: 1000,\r\n        finalTemperature: 0.01,\r\n        coolingRate: 0.95,\r\n        method: 'exponential'\r\n      },\r\n      neighborhoodSize: 0.1,\r\n      neighborhoodMethod: 'gaussian',\r\n      acceptanceCriterion: 'metropolis',\r\n      constraintHandling: 'penalty',\r\n      penaltyCoefficient: 1000,\r\n      restartEnabled: true,\r\n      maxRestarts: 3,\r\n      restartTemperature: 100,\r\n      adaptiveNeighborhood: true,\r\n      ...parameters\r\n    };\r\n\r\n    // Initialize random number generator\r\n    if (this.parameters.seedValue !== undefined) {\r\n      this.random = this.createSeededRandom(this.parameters.seedValue);\r\n    } else {\r\n      this.random = Math.random;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Main optimization method\r\n   */\r\n  public async optimize(\r\n    problem: OptimizationProblem,\r\n    objectiveFunction: ObjectiveFunctionType,\r\n    constraintFunctions: ConstraintFunctionType[]\r\n  ): Promise<OptimizationResult> {\r\n    const startTime = performance.now();\r\n    \r\n    try {\r\n      // Initialize algorithm\r\n      this.initializeAlgorithm(problem);\r\n      \r\n      // Create initial solution\r\n      await this.createInitialSolution(problem, objectiveFunction, constraintFunctions);\r\n      \r\n      // Main annealing loop\r\n      while (!this.shouldTerminate()) {\r\n        await this.performIteration(problem, objectiveFunction, constraintFunctions);\r\n        this.updateHistory();\r\n        \r\n        if (this.parameters.adaptiveNeighborhood) {\r\n          this.adaptNeighborhoodSize();\r\n        }\r\n      }\r\n      \r\n      // Check for restart\r\n      if (this.shouldRestart()) {\r\n        await this.restart(problem, objectiveFunction, constraintFunctions);\r\n      }\r\n      \r\n      // Create final result\r\n      return this.createOptimizationResult(problem, startTime);\r\n      \r\n    } catch (error) {\r\n      console.error('Simulated annealing optimization failed:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Initialize algorithm state\r\n   */\r\n  private initializeAlgorithm(problem: OptimizationProblem): void {\r\n    this.currentState = null;\r\n    this.bestSolution = null;\r\n    this.history = [];\r\n    this.evaluationCount = 0;\r\n    \r\n    console.log(`Initializing Simulated Annealing with initial temperature: ${this.parameters.initialTemperature}`);\r\n  }\r\n\r\n  /**\r\n   * Create initial solution\r\n   */\r\n  private async createInitialSolution(\r\n    problem: OptimizationProblem,\r\n    objectiveFunction: ObjectiveFunctionType,\r\n    constraintFunctions: ConstraintFunctionType[]\r\n  ): Promise<void> {\r\n    // Generate random initial solution\r\n    const initialSolution = this.createRandomSolution(problem);\r\n    await this.evaluateSolution(initialSolution, problem, objectiveFunction, constraintFunctions);\r\n    \r\n    // Initialize state\r\n    this.currentState = {\r\n      solution: initialSolution,\r\n      temperature: this.parameters.initialTemperature,\r\n      iteration: 0,\r\n      acceptedMoves: 0,\r\n      rejectedMoves: 0,\r\n      improvingMoves: 0,\r\n      worseningMoves: 0,\r\n      restartCount: 0\r\n    };\r\n    \r\n    this.bestSolution = { ...initialSolution };\r\n  }\r\n\r\n  /**\r\n   * Create a random solution\r\n   */\r\n  private createRandomSolution(problem: OptimizationProblem): OptimizationSolution {\r\n    const variables: { [variableId: string]: number | string } = {};\r\n    \r\n    for (const variable of problem.variables) {\r\n      if (variable.discreteValues && variable.discreteValues.length > 0) {\r\n        // Discrete variable\r\n        const randomIndex = Math.floor(this.random() * variable.discreteValues.length);\r\n        variables[variable.id] = variable.discreteValues[randomIndex];\r\n      } else {\r\n        // Continuous variable\r\n        const min = typeof variable.bounds.minimum === 'number' ? variable.bounds.minimum : 0;\r\n        const max = typeof variable.bounds.maximum === 'number' ? variable.bounds.maximum : 1;\r\n        variables[variable.id] = min + this.random() * (max - min);\r\n      }\r\n    }\r\n    \r\n    return {\r\n      id: this.generateSolutionId(),\r\n      variables,\r\n      objectiveValues: {},\r\n      constraintViolations: [],\r\n      feasible: true,\r\n      fitness: 0,\r\n      systemConfiguration: problem.systemConfiguration,\r\n      performanceMetrics: {} as SolutionPerformanceMetrics\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Evaluate solution fitness and constraints\r\n   */\r\n  private async evaluateSolution(\r\n    solution: OptimizationSolution,\r\n    problem: OptimizationProblem,\r\n    objectiveFunction: ObjectiveFunctionType,\r\n    constraintFunctions: ConstraintFunctionType[]\r\n  ): Promise<void> {\r\n    try {\r\n      // Convert solution to optimization variables\r\n      const variables = this.solutionToVariables(solution, problem.variables);\r\n      \r\n      // Evaluate objective function\r\n      const objectiveValue = objectiveFunction(variables);\r\n      solution.fitness = objectiveValue;\r\n      \r\n      // Store objective values\r\n      if (problem.objectives.objectives.length > 0) {\r\n        solution.objectiveValues[problem.objectives.objectives[0].id] = objectiveValue;\r\n      }\r\n      \r\n      // Evaluate constraints\r\n      solution.constraintViolations = [];\r\n      for (const constraintFunction of constraintFunctions) {\r\n        const violation = constraintFunction(variables);\r\n        solution.constraintViolations.push({\r\n          constraintId: `constraint_${solution.constraintViolations.length}`,\r\n          violationType: violation > 0 ? 'inequality' : 'boundary',\r\n          currentValue: violation,\r\n          requiredValue: 0,\r\n          severity: violation > 0 ? 'major' : 'minor',\r\n          penalty: violation > 0 ? violation * this.parameters.penaltyCoefficient : 0\r\n        });\r\n      }\r\n      \r\n      // Check feasibility\r\n      solution.feasible = solution.constraintViolations.every(v => v.currentValue <= 0);\r\n      \r\n      // Apply constraint handling\r\n      if (!solution.feasible && this.parameters.constraintHandling === 'penalty') {\r\n        const totalPenalty = solution.constraintViolations\r\n          .filter(v => v.currentValue > 0)\r\n          .reduce((sum, v) => sum + v.penalty, 0);\r\n        solution.fitness += totalPenalty;\r\n      }\r\n      \r\n      this.evaluationCount++;\r\n      \r\n    } catch (error) {\r\n      console.error('Error evaluating solution:', error);\r\n      solution.fitness = Number.MAX_VALUE;\r\n      solution.feasible = false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Convert solution to optimization variables\r\n   */\r\n  private solutionToVariables(solution: OptimizationSolution, variableTemplates: OptimizationVariable[]): OptimizationVariable[] {\r\n    return variableTemplates.map(template => ({\r\n      ...template,\r\n      currentValue: solution.variables[template.id]\r\n    }));\r\n  }\r\n\r\n  /**\r\n   * Perform one iteration of simulated annealing\r\n   */\r\n  private async performIteration(\r\n    problem: OptimizationProblem,\r\n    objectiveFunction: ObjectiveFunctionType,\r\n    constraintFunctions: ConstraintFunctionType[]\r\n  ): Promise<void> {\r\n    if (!this.currentState) return;\r\n    \r\n    // Generate neighbor solution\r\n    const neighborSolution = this.generateNeighbor(this.currentState.solution, problem);\r\n    await this.evaluateSolution(neighborSolution, problem, objectiveFunction, constraintFunctions);\r\n    \r\n    // Calculate acceptance probability\r\n    const deltaE = neighborSolution.fitness - this.currentState.solution.fitness;\r\n    const acceptanceProbability = this.calculateAcceptanceProbability(deltaE, this.currentState.temperature);\r\n    \r\n    // Accept or reject the neighbor\r\n    if (this.random() < acceptanceProbability) {\r\n      // Accept the neighbor\r\n      this.currentState.solution = neighborSolution;\r\n      this.currentState.acceptedMoves++;\r\n      \r\n      if (deltaE < 0) {\r\n        this.currentState.improvingMoves++;\r\n        \r\n        // Update best solution\r\n        if (neighborSolution.fitness < this.bestSolution!.fitness) {\r\n          this.bestSolution = { ...neighborSolution };\r\n        }\r\n      } else {\r\n        this.currentState.worseningMoves++;\r\n      }\r\n    } else {\r\n      // Reject the neighbor\r\n      this.currentState.rejectedMoves++;\r\n    }\r\n    \r\n    // Update temperature\r\n    this.currentState.temperature = this.updateTemperature(this.currentState.temperature, this.currentState.iteration);\r\n    this.currentState.iteration++;\r\n  }\r\n\r\n  /**\r\n   * Generate neighbor solution\r\n   */\r\n  private generateNeighbor(currentSolution: OptimizationSolution, problem: OptimizationProblem): OptimizationSolution {\r\n    const neighbor: OptimizationSolution = {\r\n      ...currentSolution,\r\n      id: this.generateSolutionId(),\r\n      variables: { ...currentSolution.variables }\r\n    };\r\n    \r\n    // Select random variable to modify\r\n    const variableIds = Object.keys(neighbor.variables);\r\n    const randomVariableId = variableIds[Math.floor(this.random() * variableIds.length)];\r\n    const variable = problem.variables.find(v => v.id === randomVariableId);\r\n    \r\n    if (!variable) return neighbor;\r\n    \r\n    // Generate neighbor value based on neighborhood method\r\n    neighbor.variables[randomVariableId] = this.generateNeighborValue(\r\n      neighbor.variables[randomVariableId],\r\n      variable\r\n    );\r\n    \r\n    return neighbor;\r\n  }\r\n\r\n  /**\r\n   * Generate neighbor value for a variable\r\n   */\r\n  private generateNeighborValue(currentValue: number | string, variable: OptimizationVariable): number | string {\r\n    if (variable.discreteValues && variable.discreteValues.length > 0) {\r\n      // Discrete variable - random selection from neighborhood\r\n      const currentIndex = variable.discreteValues.indexOf(currentValue);\r\n      const neighborhoodSize = Math.max(1, Math.floor(variable.discreteValues.length * this.parameters.neighborhoodSize));\r\n      \r\n      const minIndex = Math.max(0, currentIndex - neighborhoodSize);\r\n      const maxIndex = Math.min(variable.discreteValues.length - 1, currentIndex + neighborhoodSize);\r\n      \r\n      const randomIndex = minIndex + Math.floor(this.random() * (maxIndex - minIndex + 1));\r\n      return variable.discreteValues[randomIndex];\r\n    } else if (typeof currentValue === 'number') {\r\n      // Continuous variable\r\n      const min = typeof variable.bounds.minimum === 'number' ? variable.bounds.minimum : 0;\r\n      const max = typeof variable.bounds.maximum === 'number' ? variable.bounds.maximum : 1;\r\n      const range = max - min;\r\n      \r\n      let neighborValue: number;\r\n      \r\n      switch (this.parameters.neighborhoodMethod) {\r\n        case 'gaussian':\r\n          const sigma = range * this.parameters.neighborhoodSize;\r\n          neighborValue = currentValue + this.gaussianRandom() * sigma;\r\n          break;\r\n        case 'uniform':\r\n          const delta = range * this.parameters.neighborhoodSize;\r\n          neighborValue = currentValue + (this.random() - 0.5) * 2 * delta;\r\n          break;\r\n        case 'cauchy':\r\n          const gamma = range * this.parameters.neighborhoodSize;\r\n          neighborValue = currentValue + this.cauchyRandom() * gamma;\r\n          break;\r\n        default:\r\n          neighborValue = currentValue + (this.random() - 0.5) * range * this.parameters.neighborhoodSize;\r\n      }\r\n      \r\n      return Math.max(min, Math.min(max, neighborValue));\r\n    }\r\n    \r\n    return currentValue;\r\n  }\r\n\r\n  /**\r\n   * Calculate acceptance probability\r\n   */\r\n  private calculateAcceptanceProbability(deltaE: number, temperature: number): number {\r\n    if (deltaE <= 0) {\r\n      return 1.0; // Always accept improving solutions\r\n    }\r\n    \r\n    switch (this.parameters.acceptanceCriterion) {\r\n      case 'metropolis':\r\n        return Math.exp(-deltaE / temperature);\r\n      case 'boltzmann':\r\n        return 1.0 / (1.0 + Math.exp(deltaE / temperature));\r\n      case 'fast_annealing':\r\n        return Math.exp(-deltaE / (temperature * Math.log(this.currentState!.iteration + 2)));\r\n      default:\r\n        return Math.exp(-deltaE / temperature);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update temperature according to cooling schedule\r\n   */\r\n  private updateTemperature(currentTemperature: number, iteration: number): number {\r\n    const schedule = this.parameters.coolingSchedule;\r\n    \r\n    switch (schedule.method) {\r\n      case 'linear':\r\n        const linearRate = (schedule.initialTemperature - schedule.finalTemperature) / this.parameters.maxIterations;\r\n        return Math.max(schedule.finalTemperature, schedule.initialTemperature - iteration * linearRate);\r\n        \r\n      case 'exponential':\r\n        return Math.max(schedule.finalTemperature, currentTemperature * schedule.coolingRate);\r\n        \r\n      case 'logarithmic':\r\n        return Math.max(schedule.finalTemperature, schedule.initialTemperature / Math.log(iteration + 2));\r\n        \r\n      case 'adaptive':\r\n        // Adaptive cooling based on acceptance rate\r\n        const acceptanceRate = this.currentState!.acceptedMoves / (this.currentState!.acceptedMoves + this.currentState!.rejectedMoves + 1);\r\n        const targetAcceptanceRate = 0.4;\r\n        const adaptationFactor = acceptanceRate > targetAcceptanceRate ? 0.99 : 0.95;\r\n        return Math.max(schedule.finalTemperature, currentTemperature * adaptationFactor);\r\n        \r\n      default:\r\n        return Math.max(schedule.finalTemperature, currentTemperature * schedule.coolingRate);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Check termination criteria\r\n   */\r\n  private shouldTerminate(): boolean {\r\n    if (!this.currentState) return true;\r\n    \r\n    // Maximum iterations\r\n    if (this.currentState.iteration >= this.parameters.maxIterations) {\r\n      return true;\r\n    }\r\n    \r\n    // Temperature threshold\r\n    if (this.currentState.temperature <= this.parameters.finalTemperature) {\r\n      return true;\r\n    }\r\n    \r\n    // Convergence check (no improvement in recent iterations)\r\n    if (this.history.length >= 50) {\r\n      const recentHistory = this.history.slice(-50);\r\n      const bestRecent = Math.min(...recentHistory.map(h => h.bestFitness));\r\n      const improvement = this.bestSolution!.fitness - bestRecent;\r\n      \r\n      if (Math.abs(improvement) < 1e-6) {\r\n        return true;\r\n      }\r\n    }\r\n    \r\n    return false;\r\n  }\r\n\r\n  /**\r\n   * Check if restart should be performed\r\n   */\r\n  private shouldRestart(): boolean {\r\n    if (!this.parameters.restartEnabled) return false;\r\n    if (!this.currentState) return false;\r\n    if (this.currentState.restartCount >= this.parameters.maxRestarts) return false;\r\n    \r\n    // Restart if stuck in local minimum\r\n    const recentIterations = 100;\r\n    if (this.history.length >= recentIterations) {\r\n      const recentHistory = this.history.slice(-recentIterations);\r\n      const fitnessVariance = this.calculateVariance(recentHistory.map(h => h.bestFitness));\r\n      \r\n      if (fitnessVariance < 1e-6) {\r\n        return true;\r\n      }\r\n    }\r\n    \r\n    return false;\r\n  }\r\n\r\n  /**\r\n   * Restart the algorithm from a new random solution\r\n   */\r\n  private async restart(\r\n    problem: OptimizationProblem,\r\n    objectiveFunction: ObjectiveFunctionType,\r\n    constraintFunctions: ConstraintFunctionType[]\r\n  ): Promise<void> {\r\n    console.log(`Restarting simulated annealing (restart ${this.currentState!.restartCount + 1})`);\r\n    \r\n    // Create new random solution\r\n    const newSolution = this.createRandomSolution(problem);\r\n    await this.evaluateSolution(newSolution, problem, objectiveFunction, constraintFunctions);\r\n    \r\n    // Reset state with restart temperature\r\n    this.currentState!.solution = newSolution;\r\n    this.currentState!.temperature = this.parameters.restartTemperature;\r\n    this.currentState!.iteration = 0;\r\n    this.currentState!.acceptedMoves = 0;\r\n    this.currentState!.rejectedMoves = 0;\r\n    this.currentState!.improvingMoves = 0;\r\n    this.currentState!.worseningMoves = 0;\r\n    this.currentState!.restartCount++;\r\n    \r\n    // Continue optimization\r\n    while (!this.shouldTerminate()) {\r\n      await this.performIteration(problem, objectiveFunction, constraintFunctions);\r\n      this.updateHistory();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Adapt neighborhood size based on acceptance rate\r\n   */\r\n  private adaptNeighborhoodSize(): void {\r\n    if (!this.currentState) return;\r\n    \r\n    const totalMoves = this.currentState.acceptedMoves + this.currentState.rejectedMoves;\r\n    if (totalMoves < 10) return;\r\n    \r\n    const acceptanceRate = this.currentState.acceptedMoves / totalMoves;\r\n    const targetAcceptanceRate = 0.4;\r\n    \r\n    if (acceptanceRate > targetAcceptanceRate + 0.1) {\r\n      // Too many acceptances - increase neighborhood size\r\n      this.parameters.neighborhoodSize = Math.min(0.5, this.parameters.neighborhoodSize * 1.1);\r\n    } else if (acceptanceRate < targetAcceptanceRate - 0.1) {\r\n      // Too few acceptances - decrease neighborhood size\r\n      this.parameters.neighborhoodSize = Math.max(0.01, this.parameters.neighborhoodSize * 0.9);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update optimization history\r\n   */\r\n  private updateHistory(): void {\r\n    if (!this.currentState || !this.bestSolution) return;\r\n    \r\n    const history: IterationHistory = {\r\n      iteration: this.currentState.iteration,\r\n      bestFitness: this.bestSolution.fitness,\r\n      averageFitness: this.currentState.solution.fitness,\r\n      worstFitness: this.currentState.solution.fitness,\r\n      diversity: 0, // Not applicable for single solution\r\n      constraintViolations: this.currentState.solution.feasible ? 0 : 1,\r\n      timestamp: new Date()\r\n    };\r\n    \r\n    this.history.push(history);\r\n  }\r\n\r\n  /**\r\n   * Create optimization result\r\n   */\r\n  private createOptimizationResult(problem: OptimizationProblem, startTime: number): OptimizationResult {\r\n    const executionTime = performance.now() - startTime;\r\n    \r\n    const statistics: OptimizationStatistics = {\r\n      totalIterations: this.currentState?.iteration || 0,\r\n      totalEvaluations: this.evaluationCount,\r\n      convergenceIteration: this.currentState?.iteration || 0,\r\n      executionTime,\r\n      bestFitnessHistory: this.history.map(h => h.bestFitness),\r\n      averageFitnessHistory: this.history.map(h => h.averageFitness),\r\n      diversityHistory: this.history.map(h => h.diversity),\r\n      constraintViolationHistory: this.history.map(h => h.constraintViolations),\r\n      algorithmSpecificStats: {\r\n        finalTemperature: this.currentState?.temperature || 0,\r\n        acceptedMoves: this.currentState?.acceptedMoves || 0,\r\n        rejectedMoves: this.currentState?.rejectedMoves || 0,\r\n        improvingMoves: this.currentState?.improvingMoves || 0,\r\n        worseningMoves: this.currentState?.worseningMoves || 0,\r\n        restartCount: this.currentState?.restartCount || 0,\r\n        finalNeighborhoodSize: this.parameters.neighborhoodSize\r\n      }\r\n    };\r\n    \r\n    const optimizationHistory: OptimizationHistory = {\r\n      iterations: this.history,\r\n      populationHistory: [],\r\n      parameterHistory: [],\r\n      convergenceMetrics: []\r\n    };\r\n    \r\n    return {\r\n      problemId: problem.id,\r\n      status: OptimizationStatus.CONVERGED,\r\n      bestSolution: this.bestSolution!,\r\n      statistics,\r\n      history: optimizationHistory,\r\n      analysis: {},\r\n      recommendations: [],\r\n      warnings: [],\r\n      errors: []\r\n    };\r\n  }\r\n\r\n  // Utility methods\r\n  private generateSolutionId(): string {\r\n    return `sa_sol_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\r\n  }\r\n\r\n  private createSeededRandom(seed: number): () => number {\r\n    let state = seed;\r\n    return () => {\r\n      state = (state * 9301 + 49297) % 233280;\r\n      return state / 233280;\r\n    };\r\n  }\r\n\r\n  private gaussianRandom(): number {\r\n    // Box-Muller transform\r\n    const u1 = this.random();\r\n    const u2 = this.random();\r\n    return Math.sqrt(-2 * Math.log(u1)) * Math.cos(2 * Math.PI * u2);\r\n  }\r\n\r\n  private cauchyRandom(): number {\r\n    // Cauchy distribution using inverse transform\r\n    const u = this.random();\r\n    return Math.tan(Math.PI * (u - 0.5));\r\n  }\r\n\r\n  private calculateVariance(values: number[]): number {\r\n    if (values.length === 0) return 0;\r\n    \r\n    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;\r\n    const squaredDiffs = values.map(val => Math.pow(val - mean, 2));\r\n    return squaredDiffs.reduce((sum, diff) => sum + diff, 0) / values.length;\r\n  }\r\n}\r\n"], "mappings": ";;AAAA;;;;;;;;;;;;;AAAA;AAAA,SAAAA,eAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;MAAA;QAAAC,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAO,SAAA;MAAA;QAAAD,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;IAAA;IAAAW,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,cAAA;MAAAC,IAAA;MAAAC,QAAA;MAAAC,KAAA;MAAAC,OAAA;MAAAC,cAAA;MAAAC,OAAA;IAAA;IAAAC,eAAA;IAAA5B,IAAA;EAAA;EAAA,IAAA6B,QAAA,GAAA5B,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAA0B,QAAA,CAAA9B,IAAA,KAAA8B,QAAA,CAAA9B,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAA6B,QAAA,CAAA9B,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAA0B,cAAA,GAAAD,QAAA,CAAA9B,IAAA;EAAA;;;;;;;;;;;;;;;;AAcA,MAAAgC,yBAAA;AAAA;AAAA,CAAAjC,cAAA,GAAAoB,CAAA,OAAAc,OAAA;AA2CA;;;AAGA,MAAaC,kBAAkB;EAQ7BC,YAAYC,UAAkD;IAAA;IAAArC,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IANtD,KAAAkB,YAAY,GAAmB,IAAI;IAAC;IAAAtC,cAAA,GAAAoB,CAAA;IACpC,KAAAmB,YAAY,GAAgC,IAAI;IAAC;IAAAvC,cAAA,GAAAoB,CAAA;IACjD,KAAAoB,OAAO,GAAuB,EAAE;IAAC;IAAAxC,cAAA,GAAAoB,CAAA;IAEjC,KAAAqB,eAAe,GAAW,CAAC;IAAC;IAAAzC,cAAA,GAAAoB,CAAA;IAGlC,IAAI,CAACiB,UAAU,GAAG;MAChBK,kBAAkB,EAAE,IAAI;MACxBC,gBAAgB,EAAE,IAAI;MACtBC,aAAa,EAAE,IAAI;MACnBC,eAAe,EAAE;QACfH,kBAAkB,EAAE,IAAI;QACxBC,gBAAgB,EAAE,IAAI;QACtBG,WAAW,EAAE,IAAI;QACjBC,MAAM,EAAE;OACT;MACDC,gBAAgB,EAAE,GAAG;MACrBC,kBAAkB,EAAE,UAAU;MAC9BC,mBAAmB,EAAE,YAAY;MACjCC,kBAAkB,EAAE,SAAS;MAC7BC,kBAAkB,EAAE,IAAI;MACxBC,cAAc,EAAE,IAAI;MACpBC,WAAW,EAAE,CAAC;MACdC,kBAAkB,EAAE,GAAG;MACvBC,oBAAoB,EAAE,IAAI;MAC1B,GAAGnB;KACJ;IAED;IAAA;IAAArC,cAAA,GAAAoB,CAAA;IACA,IAAI,IAAI,CAACiB,UAAU,CAACoB,SAAS,KAAKtC,SAAS,EAAE;MAAA;MAAAnB,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAC3C,IAAI,CAACsC,MAAM,GAAG,IAAI,CAACC,kBAAkB,CAAC,IAAI,CAACtB,UAAU,CAACoB,SAAS,CAAC;IAClE,CAAC,MAAM;MAAA;MAAAzD,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACL,IAAI,CAACsC,MAAM,GAAGE,IAAI,CAACF,MAAM;IAC3B;EACF;EAEA;;;EAGO,MAAMG,QAAQA,CACnBC,OAA4B,EAC5BC,iBAAwC,EACxCC,mBAA6C;IAAA;IAAAhE,cAAA,GAAAqB,CAAA;IAE7C,MAAM4C,SAAS;IAAA;IAAA,CAAAjE,cAAA,GAAAoB,CAAA,QAAG8C,WAAW,CAACC,GAAG,EAAE;IAAC;IAAAnE,cAAA,GAAAoB,CAAA;IAEpC,IAAI;MAAA;MAAApB,cAAA,GAAAoB,CAAA;MACF;MACA,IAAI,CAACgD,mBAAmB,CAACN,OAAO,CAAC;MAEjC;MAAA;MAAA9D,cAAA,GAAAoB,CAAA;MACA,MAAM,IAAI,CAACiD,qBAAqB,CAACP,OAAO,EAAEC,iBAAiB,EAAEC,mBAAmB,CAAC;MAEjF;MAAA;MAAAhE,cAAA,GAAAoB,CAAA;MACA,OAAO,CAAC,IAAI,CAACkD,eAAe,EAAE,EAAE;QAAA;QAAAtE,cAAA,GAAAoB,CAAA;QAC9B,MAAM,IAAI,CAACmD,gBAAgB,CAACT,OAAO,EAAEC,iBAAiB,EAAEC,mBAAmB,CAAC;QAAC;QAAAhE,cAAA,GAAAoB,CAAA;QAC7E,IAAI,CAACoD,aAAa,EAAE;QAAC;QAAAxE,cAAA,GAAAoB,CAAA;QAErB,IAAI,IAAI,CAACiB,UAAU,CAACmB,oBAAoB,EAAE;UAAA;UAAAxD,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAoB,CAAA;UACxC,IAAI,CAACqD,qBAAqB,EAAE;QAC9B,CAAC;QAAA;QAAA;UAAAzE,cAAA,GAAAsB,CAAA;QAAA;MACH;MAEA;MAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACA,IAAI,IAAI,CAACsD,aAAa,EAAE,EAAE;QAAA;QAAA1E,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACxB,MAAM,IAAI,CAACuD,OAAO,CAACb,OAAO,EAAEC,iBAAiB,EAAEC,mBAAmB,CAAC;MACrE,CAAC;MAAA;MAAA;QAAAhE,cAAA,GAAAsB,CAAA;MAAA;MAED;MAAAtB,cAAA,GAAAoB,CAAA;MACA,OAAO,IAAI,CAACwD,wBAAwB,CAACd,OAAO,EAAEG,SAAS,CAAC;IAE1D,CAAC,CAAC,OAAOY,KAAK,EAAE;MAAA;MAAA7E,cAAA,GAAAoB,CAAA;MACd0D,OAAO,CAACD,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAAC;MAAA7E,cAAA,GAAAoB,CAAA;MACjE,MAAMyD,KAAK;IACb;EACF;EAEA;;;EAGQT,mBAAmBA,CAACN,OAA4B;IAAA;IAAA9D,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACtD,IAAI,CAACkB,YAAY,GAAG,IAAI;IAAC;IAAAtC,cAAA,GAAAoB,CAAA;IACzB,IAAI,CAACmB,YAAY,GAAG,IAAI;IAAC;IAAAvC,cAAA,GAAAoB,CAAA;IACzB,IAAI,CAACoB,OAAO,GAAG,EAAE;IAAC;IAAAxC,cAAA,GAAAoB,CAAA;IAClB,IAAI,CAACqB,eAAe,GAAG,CAAC;IAAC;IAAAzC,cAAA,GAAAoB,CAAA;IAEzB0D,OAAO,CAACC,GAAG,CAAC,8DAA8D,IAAI,CAAC1C,UAAU,CAACK,kBAAkB,EAAE,CAAC;EACjH;EAEA;;;EAGQ,MAAM2B,qBAAqBA,CACjCP,OAA4B,EAC5BC,iBAAwC,EACxCC,mBAA6C;IAAA;IAAAhE,cAAA,GAAAqB,CAAA;IAE7C;IACA,MAAM2D,eAAe;IAAA;IAAA,CAAAhF,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAAC6D,oBAAoB,CAACnB,OAAO,CAAC;IAAC;IAAA9D,cAAA,GAAAoB,CAAA;IAC3D,MAAM,IAAI,CAAC8D,gBAAgB,CAACF,eAAe,EAAElB,OAAO,EAAEC,iBAAiB,EAAEC,mBAAmB,CAAC;IAE7F;IAAA;IAAAhE,cAAA,GAAAoB,CAAA;IACA,IAAI,CAACkB,YAAY,GAAG;MAClB6C,QAAQ,EAAEH,eAAe;MACzBI,WAAW,EAAE,IAAI,CAAC/C,UAAU,CAACK,kBAAkB;MAC/C2C,SAAS,EAAE,CAAC;MACZC,aAAa,EAAE,CAAC;MAChBC,aAAa,EAAE,CAAC;MAChBC,cAAc,EAAE,CAAC;MACjBC,cAAc,EAAE,CAAC;MACjBC,YAAY,EAAE;KACf;IAAC;IAAA1F,cAAA,GAAAoB,CAAA;IAEF,IAAI,CAACmB,YAAY,GAAG;MAAE,GAAGyC;IAAe,CAAE;EAC5C;EAEA;;;EAGQC,oBAAoBA,CAACnB,OAA4B;IAAA;IAAA9D,cAAA,GAAAqB,CAAA;IACvD,MAAMsE,SAAS;IAAA;IAAA,CAAA3F,cAAA,GAAAoB,CAAA,QAA8C,EAAE;IAAC;IAAApB,cAAA,GAAAoB,CAAA;IAEhE,KAAK,MAAMwE,QAAQ,IAAI9B,OAAO,CAAC6B,SAAS,EAAE;MAAA;MAAA3F,cAAA,GAAAoB,CAAA;MACxC;MAAI;MAAA,CAAApB,cAAA,GAAAsB,CAAA,UAAAsE,QAAQ,CAACC,cAAc;MAAA;MAAA,CAAA7F,cAAA,GAAAsB,CAAA,UAAIsE,QAAQ,CAACC,cAAc,CAACC,MAAM,GAAG,CAAC,GAAE;QAAA;QAAA9F,cAAA,GAAAsB,CAAA;QACjE;QACA,MAAMyE,WAAW;QAAA;QAAA,CAAA/F,cAAA,GAAAoB,CAAA,QAAGwC,IAAI,CAACoC,KAAK,CAAC,IAAI,CAACtC,MAAM,EAAE,GAAGkC,QAAQ,CAACC,cAAc,CAACC,MAAM,CAAC;QAAC;QAAA9F,cAAA,GAAAoB,CAAA;QAC/EuE,SAAS,CAACC,QAAQ,CAACK,EAAE,CAAC,GAAGL,QAAQ,CAACC,cAAc,CAACE,WAAW,CAAC;MAC/D,CAAC,MAAM;QAAA;QAAA/F,cAAA,GAAAsB,CAAA;QACL;QACA,MAAM4E,GAAG;QAAA;QAAA,CAAAlG,cAAA,GAAAoB,CAAA,QAAG,OAAOwE,QAAQ,CAACO,MAAM,CAACC,OAAO,KAAK,QAAQ;QAAA;QAAA,CAAApG,cAAA,GAAAsB,CAAA,UAAGsE,QAAQ,CAACO,MAAM,CAACC,OAAO;QAAA;QAAA,CAAApG,cAAA,GAAAsB,CAAA,UAAG,CAAC;QACrF,MAAM+E,GAAG;QAAA;QAAA,CAAArG,cAAA,GAAAoB,CAAA,QAAG,OAAOwE,QAAQ,CAACO,MAAM,CAACG,OAAO,KAAK,QAAQ;QAAA;QAAA,CAAAtG,cAAA,GAAAsB,CAAA,UAAGsE,QAAQ,CAACO,MAAM,CAACG,OAAO;QAAA;QAAA,CAAAtG,cAAA,GAAAsB,CAAA,UAAG,CAAC;QAAC;QAAAtB,cAAA,GAAAoB,CAAA;QACtFuE,SAAS,CAACC,QAAQ,CAACK,EAAE,CAAC,GAAGC,GAAG,GAAG,IAAI,CAACxC,MAAM,EAAE,IAAI2C,GAAG,GAAGH,GAAG,CAAC;MAC5D;IACF;IAAC;IAAAlG,cAAA,GAAAoB,CAAA;IAED,OAAO;MACL6E,EAAE,EAAE,IAAI,CAACM,kBAAkB,EAAE;MAC7BZ,SAAS;MACTa,eAAe,EAAE,EAAE;MACnBC,oBAAoB,EAAE,EAAE;MACxBC,QAAQ,EAAE,IAAI;MACdC,OAAO,EAAE,CAAC;MACVC,mBAAmB,EAAE9C,OAAO,CAAC8C,mBAAmB;MAChDC,kBAAkB,EAAE;KACrB;EACH;EAEA;;;EAGQ,MAAM3B,gBAAgBA,CAC5BC,QAA8B,EAC9BrB,OAA4B,EAC5BC,iBAAwC,EACxCC,mBAA6C;IAAA;IAAAhE,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAE7C,IAAI;MACF;MACA,MAAMuE,SAAS;MAAA;MAAA,CAAA3F,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAAC0F,mBAAmB,CAAC3B,QAAQ,EAAErB,OAAO,CAAC6B,SAAS,CAAC;MAEvE;MACA,MAAMoB,cAAc;MAAA;MAAA,CAAA/G,cAAA,GAAAoB,CAAA,QAAG2C,iBAAiB,CAAC4B,SAAS,CAAC;MAAC;MAAA3F,cAAA,GAAAoB,CAAA;MACpD+D,QAAQ,CAACwB,OAAO,GAAGI,cAAc;MAEjC;MAAA;MAAA/G,cAAA,GAAAoB,CAAA;MACA,IAAI0C,OAAO,CAACkD,UAAU,CAACA,UAAU,CAAClB,MAAM,GAAG,CAAC,EAAE;QAAA;QAAA9F,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAC5C+D,QAAQ,CAACqB,eAAe,CAAC1C,OAAO,CAACkD,UAAU,CAACA,UAAU,CAAC,CAAC,CAAC,CAACf,EAAE,CAAC,GAAGc,cAAc;MAChF,CAAC;MAAA;MAAA;QAAA/G,cAAA,GAAAsB,CAAA;MAAA;MAED;MAAAtB,cAAA,GAAAoB,CAAA;MACA+D,QAAQ,CAACsB,oBAAoB,GAAG,EAAE;MAAC;MAAAzG,cAAA,GAAAoB,CAAA;MACnC,KAAK,MAAM6F,kBAAkB,IAAIjD,mBAAmB,EAAE;QACpD,MAAMkD,SAAS;QAAA;QAAA,CAAAlH,cAAA,GAAAoB,CAAA,QAAG6F,kBAAkB,CAACtB,SAAS,CAAC;QAAC;QAAA3F,cAAA,GAAAoB,CAAA;QAChD+D,QAAQ,CAACsB,oBAAoB,CAACU,IAAI,CAAC;UACjCC,YAAY,EAAE,cAAcjC,QAAQ,CAACsB,oBAAoB,CAACX,MAAM,EAAE;UAClEuB,aAAa,EAAEH,SAAS,GAAG,CAAC;UAAA;UAAA,CAAAlH,cAAA,GAAAsB,CAAA,UAAG,YAAY;UAAA;UAAA,CAAAtB,cAAA,GAAAsB,CAAA,UAAG,UAAU;UACxDgG,YAAY,EAAEJ,SAAS;UACvBK,aAAa,EAAE,CAAC;UAChBC,QAAQ,EAAEN,SAAS,GAAG,CAAC;UAAA;UAAA,CAAAlH,cAAA,GAAAsB,CAAA,UAAG,OAAO;UAAA;UAAA,CAAAtB,cAAA,GAAAsB,CAAA,UAAG,OAAO;UAC3CmG,OAAO,EAAEP,SAAS,GAAG,CAAC;UAAA;UAAA,CAAAlH,cAAA,GAAAsB,CAAA,WAAG4F,SAAS,GAAG,IAAI,CAAC7E,UAAU,CAACe,kBAAkB;UAAA;UAAA,CAAApD,cAAA,GAAAsB,CAAA,WAAG,CAAC;SAC5E,CAAC;MACJ;MAEA;MAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACA+D,QAAQ,CAACuB,QAAQ,GAAGvB,QAAQ,CAACsB,oBAAoB,CAACiB,KAAK,CAACC,CAAC,IAAI;QAAA;QAAA3H,cAAA,GAAAqB,CAAA;QAAArB,cAAA,GAAAoB,CAAA;QAAA,OAAAuG,CAAC,CAACL,YAAY,IAAI,CAAC;MAAD,CAAC,CAAC;MAEjF;MAAA;MAAAtH,cAAA,GAAAoB,CAAA;MACA;MAAI;MAAA,CAAApB,cAAA,GAAAsB,CAAA,YAAC6D,QAAQ,CAACuB,QAAQ;MAAA;MAAA,CAAA1G,cAAA,GAAAsB,CAAA,WAAI,IAAI,CAACe,UAAU,CAACc,kBAAkB,KAAK,SAAS,GAAE;QAAA;QAAAnD,cAAA,GAAAsB,CAAA;QAC1E,MAAMsG,YAAY;QAAA;QAAA,CAAA5H,cAAA,GAAAoB,CAAA,QAAG+D,QAAQ,CAACsB,oBAAoB,CAC/CoB,MAAM,CAACF,CAAC,IAAI;UAAA;UAAA3H,cAAA,GAAAqB,CAAA;UAAArB,cAAA,GAAAoB,CAAA;UAAA,OAAAuG,CAAC,CAACL,YAAY,GAAG,CAAC;QAAD,CAAC,CAAC,CAC/BQ,MAAM,CAAC,CAACC,GAAG,EAAEJ,CAAC,KAAK;UAAA;UAAA3H,cAAA,GAAAqB,CAAA;UAAArB,cAAA,GAAAoB,CAAA;UAAA,OAAA2G,GAAG,GAAGJ,CAAC,CAACF,OAAO;QAAP,CAAO,EAAE,CAAC,CAAC;QAAC;QAAAzH,cAAA,GAAAoB,CAAA;QAC1C+D,QAAQ,CAACwB,OAAO,IAAIiB,YAAY;MAClC,CAAC;MAAA;MAAA;QAAA5H,cAAA,GAAAsB,CAAA;MAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAED,IAAI,CAACqB,eAAe,EAAE;IAExB,CAAC,CAAC,OAAOoC,KAAK,EAAE;MAAA;MAAA7E,cAAA,GAAAoB,CAAA;MACd0D,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAAC;MAAA7E,cAAA,GAAAoB,CAAA;MACnD+D,QAAQ,CAACwB,OAAO,GAAGqB,MAAM,CAACC,SAAS;MAAC;MAAAjI,cAAA,GAAAoB,CAAA;MACpC+D,QAAQ,CAACuB,QAAQ,GAAG,KAAK;IAC3B;EACF;EAEA;;;EAGQI,mBAAmBA,CAAC3B,QAA8B,EAAE+C,iBAAyC;IAAA;IAAAlI,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACnG,OAAO8G,iBAAiB,CAACC,GAAG,CAACC,QAAQ,IAAK;MAAA;MAAApI,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAAA;QACxC,GAAGgH,QAAQ;QACXd,YAAY,EAAEnC,QAAQ,CAACQ,SAAS,CAACyC,QAAQ,CAACnC,EAAE;OAC7C;KAAC,CAAC;EACL;EAEA;;;EAGQ,MAAM1B,gBAAgBA,CAC5BT,OAA4B,EAC5BC,iBAAwC,EACxCC,mBAA6C;IAAA;IAAAhE,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAE7C,IAAI,CAAC,IAAI,CAACkB,YAAY,EAAE;MAAA;MAAAtC,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA;IAAA,CAAO;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAE/B;IACA,MAAM+G,gBAAgB;IAAA;IAAA,CAAArI,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACkH,gBAAgB,CAAC,IAAI,CAAChG,YAAY,CAAC6C,QAAQ,EAAErB,OAAO,CAAC;IAAC;IAAA9D,cAAA,GAAAoB,CAAA;IACpF,MAAM,IAAI,CAAC8D,gBAAgB,CAACmD,gBAAgB,EAAEvE,OAAO,EAAEC,iBAAiB,EAAEC,mBAAmB,CAAC;IAE9F;IACA,MAAMuE,MAAM;IAAA;IAAA,CAAAvI,cAAA,GAAAoB,CAAA,QAAGiH,gBAAgB,CAAC1B,OAAO,GAAG,IAAI,CAACrE,YAAY,CAAC6C,QAAQ,CAACwB,OAAO;IAC5E,MAAM6B,qBAAqB;IAAA;IAAA,CAAAxI,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACqH,8BAA8B,CAACF,MAAM,EAAE,IAAI,CAACjG,YAAY,CAAC8C,WAAW,CAAC;IAExG;IAAA;IAAApF,cAAA,GAAAoB,CAAA;IACA,IAAI,IAAI,CAACsC,MAAM,EAAE,GAAG8E,qBAAqB,EAAE;MAAA;MAAAxI,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACzC;MACA,IAAI,CAACkB,YAAY,CAAC6C,QAAQ,GAAGkD,gBAAgB;MAAC;MAAArI,cAAA,GAAAoB,CAAA;MAC9C,IAAI,CAACkB,YAAY,CAACgD,aAAa,EAAE;MAAC;MAAAtF,cAAA,GAAAoB,CAAA;MAElC,IAAImH,MAAM,GAAG,CAAC,EAAE;QAAA;QAAAvI,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACd,IAAI,CAACkB,YAAY,CAACkD,cAAc,EAAE;QAElC;QAAA;QAAAxF,cAAA,GAAAoB,CAAA;QACA,IAAIiH,gBAAgB,CAAC1B,OAAO,GAAG,IAAI,CAACpE,YAAa,CAACoE,OAAO,EAAE;UAAA;UAAA3G,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAoB,CAAA;UACzD,IAAI,CAACmB,YAAY,GAAG;YAAE,GAAG8F;UAAgB,CAAE;QAC7C,CAAC;QAAA;QAAA;UAAArI,cAAA,GAAAsB,CAAA;QAAA;MACH,CAAC,MAAM;QAAA;QAAAtB,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACL,IAAI,CAACkB,YAAY,CAACmD,cAAc,EAAE;MACpC;IACF,CAAC,MAAM;MAAA;MAAAzF,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACL;MACA,IAAI,CAACkB,YAAY,CAACiD,aAAa,EAAE;IACnC;IAEA;IAAA;IAAAvF,cAAA,GAAAoB,CAAA;IACA,IAAI,CAACkB,YAAY,CAAC8C,WAAW,GAAG,IAAI,CAACsD,iBAAiB,CAAC,IAAI,CAACpG,YAAY,CAAC8C,WAAW,EAAE,IAAI,CAAC9C,YAAY,CAAC+C,SAAS,CAAC;IAAC;IAAArF,cAAA,GAAAoB,CAAA;IACnH,IAAI,CAACkB,YAAY,CAAC+C,SAAS,EAAE;EAC/B;EAEA;;;EAGQiD,gBAAgBA,CAACK,eAAqC,EAAE7E,OAA4B;IAAA;IAAA9D,cAAA,GAAAqB,CAAA;IAC1F,MAAMuH,QAAQ;IAAA;IAAA,CAAA5I,cAAA,GAAAoB,CAAA,QAAyB;MACrC,GAAGuH,eAAe;MAClB1C,EAAE,EAAE,IAAI,CAACM,kBAAkB,EAAE;MAC7BZ,SAAS,EAAE;QAAE,GAAGgD,eAAe,CAAChD;MAAS;KAC1C;IAED;IACA,MAAMkD,WAAW;IAAA;IAAA,CAAA7I,cAAA,GAAAoB,CAAA,QAAG0H,MAAM,CAACC,IAAI,CAACH,QAAQ,CAACjD,SAAS,CAAC;IACnD,MAAMqD,gBAAgB;IAAA;IAAA,CAAAhJ,cAAA,GAAAoB,CAAA,QAAGyH,WAAW,CAACjF,IAAI,CAACoC,KAAK,CAAC,IAAI,CAACtC,MAAM,EAAE,GAAGmF,WAAW,CAAC/C,MAAM,CAAC,CAAC;IACpF,MAAMF,QAAQ;IAAA;IAAA,CAAA5F,cAAA,GAAAoB,CAAA,QAAG0C,OAAO,CAAC6B,SAAS,CAACsD,IAAI,CAACtB,CAAC,IAAI;MAAA;MAAA3H,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAAA,OAAAuG,CAAC,CAAC1B,EAAE,KAAK+C,gBAAgB;IAAhB,CAAgB,CAAC;IAAC;IAAAhJ,cAAA,GAAAoB,CAAA;IAExE,IAAI,CAACwE,QAAQ,EAAE;MAAA;MAAA5F,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,OAAOwH,QAAQ;IAAA,CAAC;IAAA;IAAA;MAAA5I,cAAA,GAAAsB,CAAA;IAAA;IAE/B;IAAAtB,cAAA,GAAAoB,CAAA;IACAwH,QAAQ,CAACjD,SAAS,CAACqD,gBAAgB,CAAC,GAAG,IAAI,CAACE,qBAAqB,CAC/DN,QAAQ,CAACjD,SAAS,CAACqD,gBAAgB,CAAC,EACpCpD,QAAQ,CACT;IAAC;IAAA5F,cAAA,GAAAoB,CAAA;IAEF,OAAOwH,QAAQ;EACjB;EAEA;;;EAGQM,qBAAqBA,CAAC5B,YAA6B,EAAE1B,QAA8B;IAAA;IAAA5F,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACzF;IAAI;IAAA,CAAApB,cAAA,GAAAsB,CAAA,WAAAsE,QAAQ,CAACC,cAAc;IAAA;IAAA,CAAA7F,cAAA,GAAAsB,CAAA,WAAIsE,QAAQ,CAACC,cAAc,CAACC,MAAM,GAAG,CAAC,GAAE;MAAA;MAAA9F,cAAA,GAAAsB,CAAA;MACjE;MACA,MAAM6H,YAAY;MAAA;MAAA,CAAAnJ,cAAA,GAAAoB,CAAA,QAAGwE,QAAQ,CAACC,cAAc,CAACuD,OAAO,CAAC9B,YAAY,CAAC;MAClE,MAAMtE,gBAAgB;MAAA;MAAA,CAAAhD,cAAA,GAAAoB,CAAA,QAAGwC,IAAI,CAACyC,GAAG,CAAC,CAAC,EAAEzC,IAAI,CAACoC,KAAK,CAACJ,QAAQ,CAACC,cAAc,CAACC,MAAM,GAAG,IAAI,CAACzD,UAAU,CAACW,gBAAgB,CAAC,CAAC;MAEnH,MAAMqG,QAAQ;MAAA;MAAA,CAAArJ,cAAA,GAAAoB,CAAA,QAAGwC,IAAI,CAACyC,GAAG,CAAC,CAAC,EAAE8C,YAAY,GAAGnG,gBAAgB,CAAC;MAC7D,MAAMsG,QAAQ;MAAA;MAAA,CAAAtJ,cAAA,GAAAoB,CAAA,QAAGwC,IAAI,CAACsC,GAAG,CAACN,QAAQ,CAACC,cAAc,CAACC,MAAM,GAAG,CAAC,EAAEqD,YAAY,GAAGnG,gBAAgB,CAAC;MAE9F,MAAM+C,WAAW;MAAA;MAAA,CAAA/F,cAAA,GAAAoB,CAAA,QAAGiI,QAAQ,GAAGzF,IAAI,CAACoC,KAAK,CAAC,IAAI,CAACtC,MAAM,EAAE,IAAI4F,QAAQ,GAAGD,QAAQ,GAAG,CAAC,CAAC,CAAC;MAAC;MAAArJ,cAAA,GAAAoB,CAAA;MACrF,OAAOwE,QAAQ,CAACC,cAAc,CAACE,WAAW,CAAC;IAC7C,CAAC,MAAM;MAAA;MAAA/F,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,IAAI,OAAOkG,YAAY,KAAK,QAAQ,EAAE;QAAA;QAAAtH,cAAA,GAAAsB,CAAA;QAC3C;QACA,MAAM4E,GAAG;QAAA;QAAA,CAAAlG,cAAA,GAAAoB,CAAA,SAAG,OAAOwE,QAAQ,CAACO,MAAM,CAACC,OAAO,KAAK,QAAQ;QAAA;QAAA,CAAApG,cAAA,GAAAsB,CAAA,WAAGsE,QAAQ,CAACO,MAAM,CAACC,OAAO;QAAA;QAAA,CAAApG,cAAA,GAAAsB,CAAA,WAAG,CAAC;QACrF,MAAM+E,GAAG;QAAA;QAAA,CAAArG,cAAA,GAAAoB,CAAA,SAAG,OAAOwE,QAAQ,CAACO,MAAM,CAACG,OAAO,KAAK,QAAQ;QAAA;QAAA,CAAAtG,cAAA,GAAAsB,CAAA,WAAGsE,QAAQ,CAACO,MAAM,CAACG,OAAO;QAAA;QAAA,CAAAtG,cAAA,GAAAsB,CAAA,WAAG,CAAC;QACrF,MAAMiI,KAAK;QAAA;QAAA,CAAAvJ,cAAA,GAAAoB,CAAA,SAAGiF,GAAG,GAAGH,GAAG;QAEvB,IAAIsD,aAAqB;QAAC;QAAAxJ,cAAA,GAAAoB,CAAA;QAE1B,QAAQ,IAAI,CAACiB,UAAU,CAACY,kBAAkB;UACxC,KAAK,UAAU;YAAA;YAAAjD,cAAA,GAAAsB,CAAA;YACb,MAAMmI,KAAK;YAAA;YAAA,CAAAzJ,cAAA,GAAAoB,CAAA,SAAGmI,KAAK,GAAG,IAAI,CAAClH,UAAU,CAACW,gBAAgB;YAAC;YAAAhD,cAAA,GAAAoB,CAAA;YACvDoI,aAAa,GAAGlC,YAAY,GAAG,IAAI,CAACoC,cAAc,EAAE,GAAGD,KAAK;YAAC;YAAAzJ,cAAA,GAAAoB,CAAA;YAC7D;UACF,KAAK,SAAS;YAAA;YAAApB,cAAA,GAAAsB,CAAA;YACZ,MAAMqI,KAAK;YAAA;YAAA,CAAA3J,cAAA,GAAAoB,CAAA,SAAGmI,KAAK,GAAG,IAAI,CAAClH,UAAU,CAACW,gBAAgB;YAAC;YAAAhD,cAAA,GAAAoB,CAAA;YACvDoI,aAAa,GAAGlC,YAAY,GAAG,CAAC,IAAI,CAAC5D,MAAM,EAAE,GAAG,GAAG,IAAI,CAAC,GAAGiG,KAAK;YAAC;YAAA3J,cAAA,GAAAoB,CAAA;YACjE;UACF,KAAK,QAAQ;YAAA;YAAApB,cAAA,GAAAsB,CAAA;YACX,MAAMsI,KAAK;YAAA;YAAA,CAAA5J,cAAA,GAAAoB,CAAA,SAAGmI,KAAK,GAAG,IAAI,CAAClH,UAAU,CAACW,gBAAgB;YAAC;YAAAhD,cAAA,GAAAoB,CAAA;YACvDoI,aAAa,GAAGlC,YAAY,GAAG,IAAI,CAACuC,YAAY,EAAE,GAAGD,KAAK;YAAC;YAAA5J,cAAA,GAAAoB,CAAA;YAC3D;UACF;YAAA;YAAApB,cAAA,GAAAsB,CAAA;YAAAtB,cAAA,GAAAoB,CAAA;YACEoI,aAAa,GAAGlC,YAAY,GAAG,CAAC,IAAI,CAAC5D,MAAM,EAAE,GAAG,GAAG,IAAI6F,KAAK,GAAG,IAAI,CAAClH,UAAU,CAACW,gBAAgB;QACnG;QAAC;QAAAhD,cAAA,GAAAoB,CAAA;QAED,OAAOwC,IAAI,CAACyC,GAAG,CAACH,GAAG,EAAEtC,IAAI,CAACsC,GAAG,CAACG,GAAG,EAAEmD,aAAa,CAAC,CAAC;MACpD,CAAC;MAAA;MAAA;QAAAxJ,cAAA,GAAAsB,CAAA;MAAA;IAAD;IAAC;IAAAtB,cAAA,GAAAoB,CAAA;IAED,OAAOkG,YAAY;EACrB;EAEA;;;EAGQmB,8BAA8BA,CAACF,MAAc,EAAEnD,WAAmB;IAAA;IAAApF,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACxE,IAAImH,MAAM,IAAI,CAAC,EAAE;MAAA;MAAAvI,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACf,OAAO,GAAG,CAAC,CAAC;IACd,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAED,QAAQ,IAAI,CAACiB,UAAU,CAACa,mBAAmB;MACzC,KAAK,YAAY;QAAA;QAAAlD,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACf,OAAOwC,IAAI,CAACkG,GAAG,CAAC,CAACvB,MAAM,GAAGnD,WAAW,CAAC;MACxC,KAAK,WAAW;QAAA;QAAApF,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACd,OAAO,GAAG,IAAI,GAAG,GAAGwC,IAAI,CAACkG,GAAG,CAACvB,MAAM,GAAGnD,WAAW,CAAC,CAAC;MACrD,KAAK,gBAAgB;QAAA;QAAApF,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACnB,OAAOwC,IAAI,CAACkG,GAAG,CAAC,CAACvB,MAAM,IAAInD,WAAW,GAAGxB,IAAI,CAACmB,GAAG,CAAC,IAAI,CAACzC,YAAa,CAAC+C,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;MACvF;QAAA;QAAArF,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACE,OAAOwC,IAAI,CAACkG,GAAG,CAAC,CAACvB,MAAM,GAAGnD,WAAW,CAAC;IAC1C;EACF;EAEA;;;EAGQsD,iBAAiBA,CAACqB,kBAA0B,EAAE1E,SAAiB;IAAA;IAAArF,cAAA,GAAAqB,CAAA;IACrE,MAAM2I,QAAQ;IAAA;IAAA,CAAAhK,cAAA,GAAAoB,CAAA,SAAG,IAAI,CAACiB,UAAU,CAACQ,eAAe;IAAC;IAAA7C,cAAA,GAAAoB,CAAA;IAEjD,QAAQ4I,QAAQ,CAACjH,MAAM;MACrB,KAAK,QAAQ;QAAA;QAAA/C,cAAA,GAAAsB,CAAA;QACX,MAAM2I,UAAU;QAAA;QAAA,CAAAjK,cAAA,GAAAoB,CAAA,SAAG,CAAC4I,QAAQ,CAACtH,kBAAkB,GAAGsH,QAAQ,CAACrH,gBAAgB,IAAI,IAAI,CAACN,UAAU,CAACO,aAAa;QAAC;QAAA5C,cAAA,GAAAoB,CAAA;QAC7G,OAAOwC,IAAI,CAACyC,GAAG,CAAC2D,QAAQ,CAACrH,gBAAgB,EAAEqH,QAAQ,CAACtH,kBAAkB,GAAG2C,SAAS,GAAG4E,UAAU,CAAC;MAElG,KAAK,aAAa;QAAA;QAAAjK,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAChB,OAAOwC,IAAI,CAACyC,GAAG,CAAC2D,QAAQ,CAACrH,gBAAgB,EAAEoH,kBAAkB,GAAGC,QAAQ,CAAClH,WAAW,CAAC;MAEvF,KAAK,aAAa;QAAA;QAAA9C,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAChB,OAAOwC,IAAI,CAACyC,GAAG,CAAC2D,QAAQ,CAACrH,gBAAgB,EAAEqH,QAAQ,CAACtH,kBAAkB,GAAGkB,IAAI,CAACmB,GAAG,CAACM,SAAS,GAAG,CAAC,CAAC,CAAC;MAEnG,KAAK,UAAU;QAAA;QAAArF,cAAA,GAAAsB,CAAA;QACb;QACA,MAAM4I,cAAc;QAAA;QAAA,CAAAlK,cAAA,GAAAoB,CAAA,SAAG,IAAI,CAACkB,YAAa,CAACgD,aAAa,IAAI,IAAI,CAAChD,YAAa,CAACgD,aAAa,GAAG,IAAI,CAAChD,YAAa,CAACiD,aAAa,GAAG,CAAC,CAAC;QACnI,MAAM4E,oBAAoB;QAAA;QAAA,CAAAnK,cAAA,GAAAoB,CAAA,SAAG,GAAG;QAChC,MAAMgJ,gBAAgB;QAAA;QAAA,CAAApK,cAAA,GAAAoB,CAAA,SAAG8I,cAAc,GAAGC,oBAAoB;QAAA;QAAA,CAAAnK,cAAA,GAAAsB,CAAA,WAAG,IAAI;QAAA;QAAA,CAAAtB,cAAA,GAAAsB,CAAA,WAAG,IAAI;QAAC;QAAAtB,cAAA,GAAAoB,CAAA;QAC7E,OAAOwC,IAAI,CAACyC,GAAG,CAAC2D,QAAQ,CAACrH,gBAAgB,EAAEoH,kBAAkB,GAAGK,gBAAgB,CAAC;MAEnF;QAAA;QAAApK,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACE,OAAOwC,IAAI,CAACyC,GAAG,CAAC2D,QAAQ,CAACrH,gBAAgB,EAAEoH,kBAAkB,GAAGC,QAAQ,CAAClH,WAAW,CAAC;IACzF;EACF;EAEA;;;EAGQwB,eAAeA,CAAA;IAAA;IAAAtE,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACrB,IAAI,CAAC,IAAI,CAACkB,YAAY,EAAE;MAAA;MAAAtC,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,OAAO,IAAI;IAAA,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAEpC;IAAAtB,cAAA,GAAAoB,CAAA;IACA,IAAI,IAAI,CAACkB,YAAY,CAAC+C,SAAS,IAAI,IAAI,CAAChD,UAAU,CAACO,aAAa,EAAE;MAAA;MAAA5C,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAChE,OAAO,IAAI;IACb,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAED;IAAAtB,cAAA,GAAAoB,CAAA;IACA,IAAI,IAAI,CAACkB,YAAY,CAAC8C,WAAW,IAAI,IAAI,CAAC/C,UAAU,CAACM,gBAAgB,EAAE;MAAA;MAAA3C,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACrE,OAAO,IAAI;IACb,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAED;IAAAtB,cAAA,GAAAoB,CAAA;IACA,IAAI,IAAI,CAACoB,OAAO,CAACsD,MAAM,IAAI,EAAE,EAAE;MAAA;MAAA9F,cAAA,GAAAsB,CAAA;MAC7B,MAAM+I,aAAa;MAAA;MAAA,CAAArK,cAAA,GAAAoB,CAAA,SAAG,IAAI,CAACoB,OAAO,CAAC8H,KAAK,CAAC,CAAC,EAAE,CAAC;MAC7C,MAAMC,UAAU;MAAA;MAAA,CAAAvK,cAAA,GAAAoB,CAAA,SAAGwC,IAAI,CAACsC,GAAG,CAAC,GAAGmE,aAAa,CAAClC,GAAG,CAACqC,CAAC,IAAI;QAAA;QAAAxK,cAAA,GAAAqB,CAAA;QAAArB,cAAA,GAAAoB,CAAA;QAAA,OAAAoJ,CAAC,CAACC,WAAW;MAAX,CAAW,CAAC,CAAC;MACrE,MAAMC,WAAW;MAAA;MAAA,CAAA1K,cAAA,GAAAoB,CAAA,SAAG,IAAI,CAACmB,YAAa,CAACoE,OAAO,GAAG4D,UAAU;MAAC;MAAAvK,cAAA,GAAAoB,CAAA;MAE5D,IAAIwC,IAAI,CAAC+G,GAAG,CAACD,WAAW,CAAC,GAAG,IAAI,EAAE;QAAA;QAAA1K,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAChC,OAAO,IAAI;MACb,CAAC;MAAA;MAAA;QAAApB,cAAA,GAAAsB,CAAA;MAAA;IACH,CAAC;IAAA;IAAA;MAAAtB,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAED,OAAO,KAAK;EACd;EAEA;;;EAGQsD,aAAaA,CAAA;IAAA;IAAA1E,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACnB,IAAI,CAAC,IAAI,CAACiB,UAAU,CAACgB,cAAc,EAAE;MAAA;MAAArD,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,OAAO,KAAK;IAAA,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAClD,IAAI,CAAC,IAAI,CAACkB,YAAY,EAAE;MAAA;MAAAtC,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,OAAO,KAAK;IAAA,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IACrC,IAAI,IAAI,CAACkB,YAAY,CAACoD,YAAY,IAAI,IAAI,CAACrD,UAAU,CAACiB,WAAW,EAAE;MAAA;MAAAtD,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,OAAO,KAAK;IAAA,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAEhF;IACA,MAAMsJ,gBAAgB;IAAA;IAAA,CAAA5K,cAAA,GAAAoB,CAAA,SAAG,GAAG;IAAC;IAAApB,cAAA,GAAAoB,CAAA;IAC7B,IAAI,IAAI,CAACoB,OAAO,CAACsD,MAAM,IAAI8E,gBAAgB,EAAE;MAAA;MAAA5K,cAAA,GAAAsB,CAAA;MAC3C,MAAM+I,aAAa;MAAA;MAAA,CAAArK,cAAA,GAAAoB,CAAA,SAAG,IAAI,CAACoB,OAAO,CAAC8H,KAAK,CAAC,CAACM,gBAAgB,CAAC;MAC3D,MAAMC,eAAe;MAAA;MAAA,CAAA7K,cAAA,GAAAoB,CAAA,SAAG,IAAI,CAAC0J,iBAAiB,CAACT,aAAa,CAAClC,GAAG,CAACqC,CAAC,IAAI;QAAA;QAAAxK,cAAA,GAAAqB,CAAA;QAAArB,cAAA,GAAAoB,CAAA;QAAA,OAAAoJ,CAAC,CAACC,WAAW;MAAX,CAAW,CAAC,CAAC;MAAC;MAAAzK,cAAA,GAAAoB,CAAA;MAEtF,IAAIyJ,eAAe,GAAG,IAAI,EAAE;QAAA;QAAA7K,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAC1B,OAAO,IAAI;MACb,CAAC;MAAA;MAAA;QAAApB,cAAA,GAAAsB,CAAA;MAAA;IACH,CAAC;IAAA;IAAA;MAAAtB,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAED,OAAO,KAAK;EACd;EAEA;;;EAGQ,MAAMuD,OAAOA,CACnBb,OAA4B,EAC5BC,iBAAwC,EACxCC,mBAA6C;IAAA;IAAAhE,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAE7C0D,OAAO,CAACC,GAAG,CAAC,2CAA2C,IAAI,CAACzC,YAAa,CAACoD,YAAY,GAAG,CAAC,GAAG,CAAC;IAE9F;IACA,MAAMqF,WAAW;IAAA;IAAA,CAAA/K,cAAA,GAAAoB,CAAA,SAAG,IAAI,CAAC6D,oBAAoB,CAACnB,OAAO,CAAC;IAAC;IAAA9D,cAAA,GAAAoB,CAAA;IACvD,MAAM,IAAI,CAAC8D,gBAAgB,CAAC6F,WAAW,EAAEjH,OAAO,EAAEC,iBAAiB,EAAEC,mBAAmB,CAAC;IAEzF;IAAA;IAAAhE,cAAA,GAAAoB,CAAA;IACA,IAAI,CAACkB,YAAa,CAAC6C,QAAQ,GAAG4F,WAAW;IAAC;IAAA/K,cAAA,GAAAoB,CAAA;IAC1C,IAAI,CAACkB,YAAa,CAAC8C,WAAW,GAAG,IAAI,CAAC/C,UAAU,CAACkB,kBAAkB;IAAC;IAAAvD,cAAA,GAAAoB,CAAA;IACpE,IAAI,CAACkB,YAAa,CAAC+C,SAAS,GAAG,CAAC;IAAC;IAAArF,cAAA,GAAAoB,CAAA;IACjC,IAAI,CAACkB,YAAa,CAACgD,aAAa,GAAG,CAAC;IAAC;IAAAtF,cAAA,GAAAoB,CAAA;IACrC,IAAI,CAACkB,YAAa,CAACiD,aAAa,GAAG,CAAC;IAAC;IAAAvF,cAAA,GAAAoB,CAAA;IACrC,IAAI,CAACkB,YAAa,CAACkD,cAAc,GAAG,CAAC;IAAC;IAAAxF,cAAA,GAAAoB,CAAA;IACtC,IAAI,CAACkB,YAAa,CAACmD,cAAc,GAAG,CAAC;IAAC;IAAAzF,cAAA,GAAAoB,CAAA;IACtC,IAAI,CAACkB,YAAa,CAACoD,YAAY,EAAE;IAEjC;IAAA;IAAA1F,cAAA,GAAAoB,CAAA;IACA,OAAO,CAAC,IAAI,CAACkD,eAAe,EAAE,EAAE;MAAA;MAAAtE,cAAA,GAAAoB,CAAA;MAC9B,MAAM,IAAI,CAACmD,gBAAgB,CAACT,OAAO,EAAEC,iBAAiB,EAAEC,mBAAmB,CAAC;MAAC;MAAAhE,cAAA,GAAAoB,CAAA;MAC7E,IAAI,CAACoD,aAAa,EAAE;IACtB;EACF;EAEA;;;EAGQC,qBAAqBA,CAAA;IAAA;IAAAzE,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAC3B,IAAI,CAAC,IAAI,CAACkB,YAAY,EAAE;MAAA;MAAAtC,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA;IAAA,CAAO;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAE/B,MAAM0J,UAAU;IAAA;IAAA,CAAAhL,cAAA,GAAAoB,CAAA,SAAG,IAAI,CAACkB,YAAY,CAACgD,aAAa,GAAG,IAAI,CAAChD,YAAY,CAACiD,aAAa;IAAC;IAAAvF,cAAA,GAAAoB,CAAA;IACrF,IAAI4J,UAAU,GAAG,EAAE,EAAE;MAAA;MAAAhL,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA;IAAA,CAAO;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAE5B,MAAM4I,cAAc;IAAA;IAAA,CAAAlK,cAAA,GAAAoB,CAAA,SAAG,IAAI,CAACkB,YAAY,CAACgD,aAAa,GAAG0F,UAAU;IACnE,MAAMb,oBAAoB;IAAA;IAAA,CAAAnK,cAAA,GAAAoB,CAAA,SAAG,GAAG;IAAC;IAAApB,cAAA,GAAAoB,CAAA;IAEjC,IAAI8I,cAAc,GAAGC,oBAAoB,GAAG,GAAG,EAAE;MAAA;MAAAnK,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAC/C;MACA,IAAI,CAACiB,UAAU,CAACW,gBAAgB,GAAGY,IAAI,CAACsC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC7D,UAAU,CAACW,gBAAgB,GAAG,GAAG,CAAC;IAC1F,CAAC,MAAM;MAAA;MAAAhD,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,IAAI8I,cAAc,GAAGC,oBAAoB,GAAG,GAAG,EAAE;QAAA;QAAAnK,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACtD;QACA,IAAI,CAACiB,UAAU,CAACW,gBAAgB,GAAGY,IAAI,CAACyC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAChE,UAAU,CAACW,gBAAgB,GAAG,GAAG,CAAC;MAC3F,CAAC;MAAA;MAAA;QAAAhD,cAAA,GAAAsB,CAAA;MAAA;IAAD;EACF;EAEA;;;EAGQkD,aAAaA,CAAA;IAAA;IAAAxE,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACnB;IAAI;IAAA,CAAApB,cAAA,GAAAsB,CAAA,YAAC,IAAI,CAACgB,YAAY;IAAA;IAAA,CAAAtC,cAAA,GAAAsB,CAAA,WAAI,CAAC,IAAI,CAACiB,YAAY,GAAE;MAAA;MAAAvC,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA;IAAA,CAAO;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAErD,MAAMkB,OAAO;IAAA;IAAA,CAAAxC,cAAA,GAAAoB,CAAA,SAAqB;MAChCiE,SAAS,EAAE,IAAI,CAAC/C,YAAY,CAAC+C,SAAS;MACtCoF,WAAW,EAAE,IAAI,CAAClI,YAAY,CAACoE,OAAO;MACtCsE,cAAc,EAAE,IAAI,CAAC3I,YAAY,CAAC6C,QAAQ,CAACwB,OAAO;MAClDuE,YAAY,EAAE,IAAI,CAAC5I,YAAY,CAAC6C,QAAQ,CAACwB,OAAO;MAChDwE,SAAS,EAAE,CAAC;MAAE;MACd1E,oBAAoB,EAAE,IAAI,CAACnE,YAAY,CAAC6C,QAAQ,CAACuB,QAAQ;MAAA;MAAA,CAAA1G,cAAA,GAAAsB,CAAA,WAAG,CAAC;MAAA;MAAA,CAAAtB,cAAA,GAAAsB,CAAA,WAAG,CAAC;MACjE8J,SAAS,EAAE,IAAIC,IAAI;KACpB;IAAC;IAAArL,cAAA,GAAAoB,CAAA;IAEF,IAAI,CAACoB,OAAO,CAAC2E,IAAI,CAAC3E,OAAO,CAAC;EAC5B;EAEA;;;EAGQoC,wBAAwBA,CAACd,OAA4B,EAAEG,SAAiB;IAAA;IAAAjE,cAAA,GAAAqB,CAAA;IAC9E,MAAMiK,aAAa;IAAA;IAAA,CAAAtL,cAAA,GAAAoB,CAAA,SAAG8C,WAAW,CAACC,GAAG,EAAE,GAAGF,SAAS;IAEnD,MAAMsH,UAAU;IAAA;IAAA,CAAAvL,cAAA,GAAAoB,CAAA,SAA2B;MACzCoK,eAAe;MAAE;MAAA,CAAAxL,cAAA,GAAAsB,CAAA,eAAI,CAACgB,YAAY,EAAE+C,SAAS;MAAA;MAAA,CAAArF,cAAA,GAAAsB,CAAA,WAAI,CAAC;MAClDmK,gBAAgB,EAAE,IAAI,CAAChJ,eAAe;MACtCiJ,oBAAoB;MAAE;MAAA,CAAA1L,cAAA,GAAAsB,CAAA,eAAI,CAACgB,YAAY,EAAE+C,SAAS;MAAA;MAAA,CAAArF,cAAA,GAAAsB,CAAA,WAAI,CAAC;MACvDgK,aAAa;MACbK,kBAAkB,EAAE,IAAI,CAACnJ,OAAO,CAAC2F,GAAG,CAACqC,CAAC,IAAI;QAAA;QAAAxK,cAAA,GAAAqB,CAAA;QAAArB,cAAA,GAAAoB,CAAA;QAAA,OAAAoJ,CAAC,CAACC,WAAW;MAAX,CAAW,CAAC;MACxDmB,qBAAqB,EAAE,IAAI,CAACpJ,OAAO,CAAC2F,GAAG,CAACqC,CAAC,IAAI;QAAA;QAAAxK,cAAA,GAAAqB,CAAA;QAAArB,cAAA,GAAAoB,CAAA;QAAA,OAAAoJ,CAAC,CAACS,cAAc;MAAd,CAAc,CAAC;MAC9DY,gBAAgB,EAAE,IAAI,CAACrJ,OAAO,CAAC2F,GAAG,CAACqC,CAAC,IAAI;QAAA;QAAAxK,cAAA,GAAAqB,CAAA;QAAArB,cAAA,GAAAoB,CAAA;QAAA,OAAAoJ,CAAC,CAACW,SAAS;MAAT,CAAS,CAAC;MACpDW,0BAA0B,EAAE,IAAI,CAACtJ,OAAO,CAAC2F,GAAG,CAACqC,CAAC,IAAI;QAAA;QAAAxK,cAAA,GAAAqB,CAAA;QAAArB,cAAA,GAAAoB,CAAA;QAAA,OAAAoJ,CAAC,CAAC/D,oBAAoB;MAApB,CAAoB,CAAC;MACzEsF,sBAAsB,EAAE;QACtBpJ,gBAAgB;QAAE;QAAA,CAAA3C,cAAA,GAAAsB,CAAA,eAAI,CAACgB,YAAY,EAAE8C,WAAW;QAAA;QAAA,CAAApF,cAAA,GAAAsB,CAAA,WAAI,CAAC;QACrDgE,aAAa;QAAE;QAAA,CAAAtF,cAAA,GAAAsB,CAAA,eAAI,CAACgB,YAAY,EAAEgD,aAAa;QAAA;QAAA,CAAAtF,cAAA,GAAAsB,CAAA,WAAI,CAAC;QACpDiE,aAAa;QAAE;QAAA,CAAAvF,cAAA,GAAAsB,CAAA,eAAI,CAACgB,YAAY,EAAEiD,aAAa;QAAA;QAAA,CAAAvF,cAAA,GAAAsB,CAAA,WAAI,CAAC;QACpDkE,cAAc;QAAE;QAAA,CAAAxF,cAAA,GAAAsB,CAAA,eAAI,CAACgB,YAAY,EAAEkD,cAAc;QAAA;QAAA,CAAAxF,cAAA,GAAAsB,CAAA,WAAI,CAAC;QACtDmE,cAAc;QAAE;QAAA,CAAAzF,cAAA,GAAAsB,CAAA,eAAI,CAACgB,YAAY,EAAEmD,cAAc;QAAA;QAAA,CAAAzF,cAAA,GAAAsB,CAAA,WAAI,CAAC;QACtDoE,YAAY;QAAE;QAAA,CAAA1F,cAAA,GAAAsB,CAAA,eAAI,CAACgB,YAAY,EAAEoD,YAAY;QAAA;QAAA,CAAA1F,cAAA,GAAAsB,CAAA,WAAI,CAAC;QAClD0K,qBAAqB,EAAE,IAAI,CAAC3J,UAAU,CAACW;;KAE1C;IAED,MAAMiJ,mBAAmB;IAAA;IAAA,CAAAjM,cAAA,GAAAoB,CAAA,SAAwB;MAC/C8K,UAAU,EAAE,IAAI,CAAC1J,OAAO;MACxB2J,iBAAiB,EAAE,EAAE;MACrBC,gBAAgB,EAAE,EAAE;MACpBC,kBAAkB,EAAE;KACrB;IAAC;IAAArM,cAAA,GAAAoB,CAAA;IAEF,OAAO;MACLkL,SAAS,EAAExI,OAAO,CAACmC,EAAE;MACrBsG,MAAM,EAAEtK,yBAAA,CAAAuK,kBAAkB,CAACC,SAAS;MACpClK,YAAY,EAAE,IAAI,CAACA,YAAa;MAChCgJ,UAAU;MACV/I,OAAO,EAAEyJ,mBAAmB;MAC5BS,QAAQ,EAAE,EAAE;MACZC,eAAe,EAAE,EAAE;MACnBC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE;KACT;EACH;EAEA;EACQtG,kBAAkBA,CAAA;IAAA;IAAAvG,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACxB,OAAO,UAAUiK,IAAI,CAAClH,GAAG,EAAE,IAAIP,IAAI,CAACF,MAAM,EAAE,CAACoJ,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;EAC1E;EAEQpJ,kBAAkBA,CAACqJ,IAAY;IAAA;IAAAhN,cAAA,GAAAqB,CAAA;IACrC,IAAI4L,KAAK;IAAA;IAAA,CAAAjN,cAAA,GAAAoB,CAAA,SAAG4L,IAAI;IAAC;IAAAhN,cAAA,GAAAoB,CAAA;IACjB,OAAO,MAAK;MAAA;MAAApB,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MACV6L,KAAK,GAAG,CAACA,KAAK,GAAG,IAAI,GAAG,KAAK,IAAI,MAAM;MAAC;MAAAjN,cAAA,GAAAoB,CAAA;MACxC,OAAO6L,KAAK,GAAG,MAAM;IACvB,CAAC;EACH;EAEQvD,cAAcA,CAAA;IAAA;IAAA1J,cAAA,GAAAqB,CAAA;IACpB;IACA,MAAM6L,EAAE;IAAA;IAAA,CAAAlN,cAAA,GAAAoB,CAAA,SAAG,IAAI,CAACsC,MAAM,EAAE;IACxB,MAAMyJ,EAAE;IAAA;IAAA,CAAAnN,cAAA,GAAAoB,CAAA,SAAG,IAAI,CAACsC,MAAM,EAAE;IAAC;IAAA1D,cAAA,GAAAoB,CAAA;IACzB,OAAOwC,IAAI,CAACwJ,IAAI,CAAC,CAAC,CAAC,GAAGxJ,IAAI,CAACmB,GAAG,CAACmI,EAAE,CAAC,CAAC,GAAGtJ,IAAI,CAACyJ,GAAG,CAAC,CAAC,GAAGzJ,IAAI,CAAC0J,EAAE,GAAGH,EAAE,CAAC;EAClE;EAEQtD,YAAYA,CAAA;IAAA;IAAA7J,cAAA,GAAAqB,CAAA;IAClB;IACA,MAAMkM,CAAC;IAAA;IAAA,CAAAvN,cAAA,GAAAoB,CAAA,SAAG,IAAI,CAACsC,MAAM,EAAE;IAAC;IAAA1D,cAAA,GAAAoB,CAAA;IACxB,OAAOwC,IAAI,CAAC4J,GAAG,CAAC5J,IAAI,CAAC0J,EAAE,IAAIC,CAAC,GAAG,GAAG,CAAC,CAAC;EACtC;EAEQzC,iBAAiBA,CAAC2C,MAAgB;IAAA;IAAAzN,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACxC,IAAIqM,MAAM,CAAC3H,MAAM,KAAK,CAAC,EAAE;MAAA;MAAA9F,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,OAAO,CAAC;IAAA,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAElC,MAAMoM,IAAI;IAAA;IAAA,CAAA1N,cAAA,GAAAoB,CAAA,SAAGqM,MAAM,CAAC3F,MAAM,CAAC,CAACC,GAAG,EAAE4F,GAAG,KAAK;MAAA;MAAA3N,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAAA,OAAA2G,GAAG,GAAG4F,GAAG;IAAH,CAAG,EAAE,CAAC,CAAC,GAAGF,MAAM,CAAC3H,MAAM;IACtE,MAAM8H,YAAY;IAAA;IAAA,CAAA5N,cAAA,GAAAoB,CAAA,SAAGqM,MAAM,CAACtF,GAAG,CAACwF,GAAG,IAAI;MAAA;MAAA3N,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAAA,OAAAwC,IAAI,CAACiK,GAAG,CAACF,GAAG,GAAGD,IAAI,EAAE,CAAC,CAAC;IAAD,CAAC,CAAC;IAAC;IAAA1N,cAAA,GAAAoB,CAAA;IAChE,OAAOwM,YAAY,CAAC9F,MAAM,CAAC,CAACC,GAAG,EAAE+F,IAAI,KAAK;MAAA;MAAA9N,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAAA,OAAA2G,GAAG,GAAG+F,IAAI;IAAJ,CAAI,EAAE,CAAC,CAAC,GAAGL,MAAM,CAAC3H,MAAM;EAC1E;;AACD;AAAA9F,cAAA,GAAAoB,CAAA;AAtkBD2M,OAAA,CAAA5L,kBAAA,GAAAA,kBAAA", "ignoreList": []}