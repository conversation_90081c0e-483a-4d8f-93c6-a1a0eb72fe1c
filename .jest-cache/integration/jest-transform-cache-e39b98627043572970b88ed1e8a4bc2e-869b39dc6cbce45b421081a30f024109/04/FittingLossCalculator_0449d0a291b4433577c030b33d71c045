23257fd08baed04ecd1def9a7efb1a19
"use strict";

/**
 * FittingLossCalculator - Modular calculation service for HVAC fitting losses
 *
 * MISSION-CRITICAL: Pure TypeScript functions for ASHRAE/SMACNA-compliant fitting loss calculations
 * Handles minor losses for elbows, tees, transitions, diffusers, and other HVAC components
 *
 * @see docs/implementation/duct-physics/fitting-loss-calculations.md
 * @see backend/data/fitting_coefficients.json
 */
/* istanbul ignore next */
function cov_2fjbc9r3tm() {
  var path = "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\FittingLossCalculator.ts";
  var hash = "e91a1b068362ed2a414b742b4982d879760938f3";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\FittingLossCalculator.ts",
    statementMap: {
      "0": {
        start: {
          line: 11,
          column: 0
        },
        end: {
          line: 11,
          column: 62
        }
      },
      "1": {
        start: {
          line: 12,
          column: 0
        },
        end: {
          line: 12,
          column: 39
        }
      },
      "2": {
        start: {
          line: 13,
          column: 13
        },
        end: {
          line: 13,
          column: 26
        }
      },
      "3": {
        start: {
          line: 14,
          column: 15
        },
        end: {
          line: 14,
          column: 30
        }
      },
      "4": {
        start: {
          line: 16,
          column: 32
        },
        end: {
          line: 31,
          column: 1
        }
      },
      "5": {
        start: {
          line: 17,
          column: 4
        },
        end: {
          line: 30,
          column: 5
        }
      },
      "6": {
        start: {
          line: 18,
          column: 25
        },
        end: {
          line: 18,
          column: 92
        }
      },
      "7": {
        start: {
          line: 19,
          column: 24
        },
        end: {
          line: 19,
          column: 64
        }
      },
      "8": {
        start: {
          line: 20,
          column: 8
        },
        end: {
          line: 20,
          column: 35
        }
      },
      "9": {
        start: {
          line: 23,
          column: 8
        },
        end: {
          line: 23,
          column: 81
        }
      },
      "10": {
        start: {
          line: 24,
          column: 8
        },
        end: {
          line: 29,
          column: 10
        }
      },
      "11": {
        start: {
          line: 32,
          column: 28
        },
        end: {
          line: 32,
          column: 53
        }
      },
      "12": {
        start: {
          line: 43,
          column: 49
        },
        end: {
          line: 43,
          column: 54
        }
      },
      "13": {
        start: {
          line: 46,
          column: 27
        },
        end: {
          line: 46,
          column: 55
        }
      },
      "14": {
        start: {
          line: 48,
          column: 29
        },
        end: {
          line: 48,
          column: 47
        }
      },
      "15": {
        start: {
          line: 49,
          column: 8
        },
        end: {
          line: 49,
          column: 41
        }
      },
      "16": {
        start: {
          line: 56,
          column: 33
        },
        end: {
          line: 56,
          column: 89
        }
      },
      "17": {
        start: {
          line: 58,
          column: 30
        },
        end: {
          line: 58,
          column: 53
        }
      },
      "18": {
        start: {
          line: 60,
          column: 29
        },
        end: {
          line: 60,
          column: 69
        }
      },
      "19": {
        start: {
          line: 61,
          column: 8
        },
        end: {
          line: 69,
          column: 10
        }
      },
      "20": {
        start: {
          line: 75,
          column: 25
        },
        end: {
          line: 75,
          column: 27
        }
      },
      "21": {
        start: {
          line: 76,
          column: 32
        },
        end: {
          line: 76,
          column: 34
        }
      },
      "22": {
        start: {
          line: 77,
          column: 8
        },
        end: {
          line: 127,
          column: 9
        }
      },
      "23": {
        start: {
          line: 78,
          column: 32
        },
        end: {
          line: 78,
          column: 59
        }
      },
      "24": {
        start: {
          line: 79,
          column: 12
        },
        end: {
          line: 82,
          column: 13
        }
      },
      "25": {
        start: {
          line: 80,
          column: 16
        },
        end: {
          line: 80,
          column: 85
        }
      },
      "26": {
        start: {
          line: 81,
          column: 16
        },
        end: {
          line: 81,
          column: 93
        }
      },
      "27": {
        start: {
          line: 86,
          column: 12
        },
        end: {
          line: 121,
          column: 13
        }
      },
      "28": {
        start: {
          line: 87,
          column: 31
        },
        end: {
          line: 87,
          column: 75
        }
      },
      "29": {
        start: {
          line: 88,
          column: 16
        },
        end: {
          line: 88,
          column: 41
        }
      },
      "30": {
        start: {
          line: 89,
          column: 16
        },
        end: {
          line: 89,
          column: 57
        }
      },
      "31": {
        start: {
          line: 90,
          column: 16
        },
        end: {
          line: 90,
          column: 50
        }
      },
      "32": {
        start: {
          line: 91,
          column: 16
        },
        end: {
          line: 91,
          column: 64
        }
      },
      "33": {
        start: {
          line: 93,
          column: 17
        },
        end: {
          line: 121,
          column: 13
        }
      },
      "34": {
        start: {
          line: 94,
          column: 31
        },
        end: {
          line: 94,
          column: 73
        }
      },
      "35": {
        start: {
          line: 95,
          column: 16
        },
        end: {
          line: 95,
          column: 41
        }
      },
      "36": {
        start: {
          line: 96,
          column: 16
        },
        end: {
          line: 96,
          column: 57
        }
      },
      "37": {
        start: {
          line: 97,
          column: 16
        },
        end: {
          line: 97,
          column: 50
        }
      },
      "38": {
        start: {
          line: 98,
          column: 16
        },
        end: {
          line: 98,
          column: 64
        }
      },
      "39": {
        start: {
          line: 100,
          column: 17
        },
        end: {
          line: 121,
          column: 13
        }
      },
      "40": {
        start: {
          line: 101,
          column: 31
        },
        end: {
          line: 101,
          column: 80
        }
      },
      "41": {
        start: {
          line: 102,
          column: 16
        },
        end: {
          line: 102,
          column: 41
        }
      },
      "42": {
        start: {
          line: 103,
          column: 16
        },
        end: {
          line: 103,
          column: 57
        }
      },
      "43": {
        start: {
          line: 104,
          column: 16
        },
        end: {
          line: 104,
          column: 50
        }
      },
      "44": {
        start: {
          line: 105,
          column: 16
        },
        end: {
          line: 105,
          column: 64
        }
      },
      "45": {
        start: {
          line: 107,
          column: 17
        },
        end: {
          line: 121,
          column: 13
        }
      },
      "46": {
        start: {
          line: 108,
          column: 31
        },
        end: {
          line: 108,
          column: 76
        }
      },
      "47": {
        start: {
          line: 109,
          column: 16
        },
        end: {
          line: 109,
          column: 41
        }
      },
      "48": {
        start: {
          line: 110,
          column: 16
        },
        end: {
          line: 110,
          column: 57
        }
      },
      "49": {
        start: {
          line: 111,
          column: 16
        },
        end: {
          line: 111,
          column: 50
        }
      },
      "50": {
        start: {
          line: 112,
          column: 16
        },
        end: {
          line: 112,
          column: 64
        }
      },
      "51": {
        start: {
          line: 116,
          column: 31
        },
        end: {
          line: 116,
          column: 77
        }
      },
      "52": {
        start: {
          line: 117,
          column: 16
        },
        end: {
          line: 117,
          column: 41
        }
      },
      "53": {
        start: {
          line: 118,
          column: 16
        },
        end: {
          line: 118,
          column: 57
        }
      },
      "54": {
        start: {
          line: 119,
          column: 16
        },
        end: {
          line: 119,
          column: 50
        }
      },
      "55": {
        start: {
          line: 120,
          column: 16
        },
        end: {
          line: 120,
          column: 64
        }
      },
      "56": {
        start: {
          line: 122,
          column: 12
        },
        end: {
          line: 122,
          column: 92
        }
      },
      "57": {
        start: {
          line: 125,
          column: 12
        },
        end: {
          line: 125,
          column: 117
        }
      },
      "58": {
        start: {
          line: 126,
          column: 12
        },
        end: {
          line: 126,
          column: 95
        }
      },
      "59": {
        start: {
          line: 133,
          column: 36
        },
        end: {
          line: 133,
          column: 42
        }
      },
      "60": {
        start: {
          line: 134,
          column: 8
        },
        end: {
          line: 147,
          column: 9
        }
      },
      "61": {
        start: {
          line: 135,
          column: 12
        },
        end: {
          line: 140,
          column: 69
        }
      },
      "62": {
        start: {
          line: 142,
          column: 13
        },
        end: {
          line: 147,
          column: 9
        }
      },
      "63": {
        start: {
          line: 143,
          column: 12
        },
        end: {
          line: 146,
          column: 69
        }
      },
      "64": {
        start: {
          line: 148,
          column: 8
        },
        end: {
          line: 148,
          column: 20
        }
      },
      "65": {
        start: {
          line: 154,
          column: 25
        },
        end: {
          line: 154,
          column: 27
        }
      },
      "66": {
        start: {
          line: 155,
          column: 32
        },
        end: {
          line: 155,
          column: 34
        }
      },
      "67": {
        start: {
          line: 156,
          column: 8
        },
        end: {
          line: 171,
          column: 9
        }
      },
      "68": {
        start: {
          line: 157,
          column: 26
        },
        end: {
          line: 157,
          column: 63
        }
      },
      "69": {
        start: {
          line: 158,
          column: 30
        },
        end: {
          line: 158,
          column: 74
        }
      },
      "70": {
        start: {
          line: 159,
          column: 12
        },
        end: {
          line: 170,
          column: 13
        }
      },
      "71": {
        start: {
          line: 160,
          column: 16
        },
        end: {
          line: 163,
          column: 17
        }
      },
      "72": {
        start: {
          line: 161,
          column: 20
        },
        end: {
          line: 161,
          column: 90
        }
      },
      "73": {
        start: {
          line: 162,
          column: 20
        },
        end: {
          line: 162,
          column: 114
        }
      },
      "74": {
        start: {
          line: 164,
          column: 16
        },
        end: {
          line: 169,
          column: 18
        }
      },
      "75": {
        start: {
          line: 172,
          column: 8
        },
        end: {
          line: 187,
          column: 9
        }
      },
      "76": {
        start: {
          line: 173,
          column: 31
        },
        end: {
          line: 173,
          column: 77
        }
      },
      "77": {
        start: {
          line: 174,
          column: 31
        },
        end: {
          line: 174,
          column: 69
        }
      },
      "78": {
        start: {
          line: 175,
          column: 12
        },
        end: {
          line: 186,
          column: 13
        }
      },
      "79": {
        start: {
          line: 176,
          column: 16
        },
        end: {
          line: 179,
          column: 17
        }
      },
      "80": {
        start: {
          line: 177,
          column: 20
        },
        end: {
          line: 177,
          column: 79
        }
      },
      "81": {
        start: {
          line: 178,
          column: 20
        },
        end: {
          line: 178,
          column: 98
        }
      },
      "82": {
        start: {
          line: 180,
          column: 16
        },
        end: {
          line: 185,
          column: 18
        }
      },
      "83": {
        start: {
          line: 188,
          column: 8
        },
        end: {
          line: 188,
          column: 70
        }
      },
      "84": {
        start: {
          line: 189,
          column: 8
        },
        end: {
          line: 189,
          column: 85
        }
      },
      "85": {
        start: {
          line: 195,
          column: 25
        },
        end: {
          line: 195,
          column: 27
        }
      },
      "86": {
        start: {
          line: 196,
          column: 32
        },
        end: {
          line: 196,
          column: 34
        }
      },
      "87": {
        start: {
          line: 197,
          column: 28
        },
        end: {
          line: 197,
          column: 64
        }
      },
      "88": {
        start: {
          line: 198,
          column: 26
        },
        end: {
          line: 198,
          column: 63
        }
      },
      "89": {
        start: {
          line: 199,
          column: 8
        },
        end: {
          line: 211,
          column: 9
        }
      },
      "90": {
        start: {
          line: 200,
          column: 26
        },
        end: {
          line: 200,
          column: 87
        }
      },
      "91": {
        start: {
          line: 201,
          column: 12
        },
        end: {
          line: 204,
          column: 13
        }
      },
      "92": {
        start: {
          line: 202,
          column: 16
        },
        end: {
          line: 202,
          column: 82
        }
      },
      "93": {
        start: {
          line: 203,
          column: 16
        },
        end: {
          line: 203,
          column: 91
        }
      },
      "94": {
        start: {
          line: 205,
          column: 12
        },
        end: {
          line: 210,
          column: 14
        }
      },
      "95": {
        start: {
          line: 212,
          column: 8
        },
        end: {
          line: 212,
          column: 68
        }
      },
      "96": {
        start: {
          line: 213,
          column: 8
        },
        end: {
          line: 213,
          column: 85
        }
      },
      "97": {
        start: {
          line: 219,
          column: 25
        },
        end: {
          line: 219,
          column: 27
        }
      },
      "98": {
        start: {
          line: 220,
          column: 32
        },
        end: {
          line: 220,
          column: 34
        }
      },
      "99": {
        start: {
          line: 221,
          column: 8
        },
        end: {
          line: 236,
          column: 9
        }
      },
      "100": {
        start: {
          line: 222,
          column: 26
        },
        end: {
          line: 222,
          column: 63
        }
      },
      "101": {
        start: {
          line: 223,
          column: 30
        },
        end: {
          line: 223,
          column: 74
        }
      },
      "102": {
        start: {
          line: 224,
          column: 12
        },
        end: {
          line: 235,
          column: 13
        }
      },
      "103": {
        start: {
          line: 225,
          column: 16
        },
        end: {
          line: 228,
          column: 17
        }
      },
      "104": {
        start: {
          line: 226,
          column: 20
        },
        end: {
          line: 226,
          column: 80
        }
      },
      "105": {
        start: {
          line: 227,
          column: 20
        },
        end: {
          line: 227,
          column: 113
        }
      },
      "106": {
        start: {
          line: 229,
          column: 16
        },
        end: {
          line: 234,
          column: 18
        }
      },
      "107": {
        start: {
          line: 237,
          column: 8
        },
        end: {
          line: 237,
          column: 75
        }
      },
      "108": {
        start: {
          line: 238,
          column: 8
        },
        end: {
          line: 238,
          column: 85
        }
      },
      "109": {
        start: {
          line: 244,
          column: 25
        },
        end: {
          line: 244,
          column: 27
        }
      },
      "110": {
        start: {
          line: 245,
          column: 32
        },
        end: {
          line: 245,
          column: 34
        }
      },
      "111": {
        start: {
          line: 246,
          column: 22
        },
        end: {
          line: 246,
          column: 58
        }
      },
      "112": {
        start: {
          line: 247,
          column: 26
        },
        end: {
          line: 247,
          column: 61
        }
      },
      "113": {
        start: {
          line: 248,
          column: 8
        },
        end: {
          line: 260,
          column: 9
        }
      },
      "114": {
        start: {
          line: 249,
          column: 29
        },
        end: {
          line: 249,
          column: 46
        }
      },
      "115": {
        start: {
          line: 250,
          column: 12
        },
        end: {
          line: 253,
          column: 13
        }
      },
      "116": {
        start: {
          line: 251,
          column: 16
        },
        end: {
          line: 251,
          column: 71
        }
      },
      "117": {
        start: {
          line: 252,
          column: 16
        },
        end: {
          line: 252,
          column: 96
        }
      },
      "118": {
        start: {
          line: 254,
          column: 12
        },
        end: {
          line: 259,
          column: 14
        }
      },
      "119": {
        start: {
          line: 261,
          column: 8
        },
        end: {
          line: 261,
          column: 63
        }
      },
      "120": {
        start: {
          line: 262,
          column: 8
        },
        end: {
          line: 262,
          column: 85
        }
      },
      "121": {
        start: {
          line: 268,
          column: 25
        },
        end: {
          line: 268,
          column: 27
        }
      },
      "122": {
        start: {
          line: 269,
          column: 32
        },
        end: {
          line: 269,
          column: 34
        }
      },
      "123": {
        start: {
          line: 271,
          column: 8
        },
        end: {
          line: 273,
          column: 9
        }
      },
      "124": {
        start: {
          line: 272,
          column: 12
        },
        end: {
          line: 272,
          column: 98
        }
      },
      "125": {
        start: {
          line: 274,
          column: 8
        },
        end: {
          line: 285,
          column: 9
        }
      },
      "126": {
        start: {
          line: 275,
          column: 28
        },
        end: {
          line: 275,
          column: 79
        }
      },
      "127": {
        start: {
          line: 276,
          column: 32
        },
        end: {
          line: 276,
          column: 58
        }
      },
      "128": {
        start: {
          line: 277,
          column: 12
        },
        end: {
          line: 284,
          column: 13
        }
      },
      "129": {
        start: {
          line: 278,
          column: 16
        },
        end: {
          line: 283,
          column: 18
        }
      },
      "130": {
        start: {
          line: 286,
          column: 8
        },
        end: {
          line: 286,
          column: 80
        }
      },
      "131": {
        start: {
          line: 287,
          column: 8
        },
        end: {
          line: 287,
          column: 85
        }
      },
      "132": {
        start: {
          line: 293,
          column: 8
        },
        end: {
          line: 310,
          column: 9
        }
      },
      "133": {
        start: {
          line: 294,
          column: 12
        },
        end: {
          line: 301,
          column: 14
        }
      },
      "134": {
        start: {
          line: 304,
          column: 12
        },
        end: {
          line: 309,
          column: 14
        }
      },
      "135": {
        start: {
          line: 316,
          column: 8
        },
        end: {
          line: 316,
          column: 44
        }
      },
      "136": {
        start: {
          line: 319,
          column: 0
        },
        end: {
          line: 319,
          column: 54
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 16,
            column: 32
          },
          end: {
            line: 16,
            column: 33
          }
        },
        loc: {
          start: {
            line: 16,
            column: 38
          },
          end: {
            line: 31,
            column: 1
          }
        },
        line: 16
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 42,
            column: 4
          },
          end: {
            line: 42,
            column: 5
          }
        },
        loc: {
          start: {
            line: 42,
            column: 44
          },
          end: {
            line: 50,
            column: 5
          }
        },
        line: 42
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 54,
            column: 4
          },
          end: {
            line: 54,
            column: 5
          }
        },
        loc: {
          start: {
            line: 54,
            column: 70
          },
          end: {
            line: 70,
            column: 5
          }
        },
        line: 54
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 74,
            column: 4
          },
          end: {
            line: 74,
            column: 5
          }
        },
        loc: {
          start: {
            line: 74,
            column: 30
          },
          end: {
            line: 128,
            column: 5
          }
        },
        line: 74
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 132,
            column: 4
          },
          end: {
            line: 132,
            column: 5
          }
        },
        loc: {
          start: {
            line: 132,
            column: 34
          },
          end: {
            line: 149,
            column: 5
          }
        },
        line: 132
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 153,
            column: 4
          },
          end: {
            line: 153,
            column: 5
          }
        },
        loc: {
          start: {
            line: 153,
            column: 51
          },
          end: {
            line: 190,
            column: 5
          }
        },
        line: 153
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 194,
            column: 4
          },
          end: {
            line: 194,
            column: 5
          }
        },
        loc: {
          start: {
            line: 194,
            column: 49
          },
          end: {
            line: 214,
            column: 5
          }
        },
        line: 194
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 218,
            column: 4
          },
          end: {
            line: 218,
            column: 5
          }
        },
        loc: {
          start: {
            line: 218,
            column: 56
          },
          end: {
            line: 239,
            column: 5
          }
        },
        line: 218
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 243,
            column: 4
          },
          end: {
            line: 243,
            column: 5
          }
        },
        loc: {
          start: {
            line: 243,
            column: 52
          },
          end: {
            line: 263,
            column: 5
          }
        },
        line: 243
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 267,
            column: 4
          },
          end: {
            line: 267,
            column: 5
          }
        },
        loc: {
          start: {
            line: 267,
            column: 53
          },
          end: {
            line: 288,
            column: 5
          }
        },
        line: 267
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 292,
            column: 4
          },
          end: {
            line: 292,
            column: 5
          }
        },
        loc: {
          start: {
            line: 292,
            column: 43
          },
          end: {
            line: 311,
            column: 5
          }
        },
        line: 292
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 315,
            column: 4
          },
          end: {
            line: 315,
            column: 5
          }
        },
        loc: {
          start: {
            line: 315,
            column: 32
          },
          end: {
            line: 317,
            column: 5
          }
        },
        line: 315
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 43,
            column: 26
          },
          end: {
            line: 43,
            column: 44
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 43,
            column: 39
          },
          end: {
            line: 43,
            column: 44
          }
        }],
        line: 43
      },
      "1": {
        loc: {
          start: {
            line: 54,
            column: 50
          },
          end: {
            line: 54,
            column: 68
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 54,
            column: 63
          },
          end: {
            line: 54,
            column: 68
          }
        }],
        line: 54
      },
      "2": {
        loc: {
          start: {
            line: 79,
            column: 12
          },
          end: {
            line: 82,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 79,
            column: 12
          },
          end: {
            line: 82,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 79
      },
      "3": {
        loc: {
          start: {
            line: 86,
            column: 12
          },
          end: {
            line: 121,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 86,
            column: 12
          },
          end: {
            line: 121,
            column: 13
          }
        }, {
          start: {
            line: 93,
            column: 17
          },
          end: {
            line: 121,
            column: 13
          }
        }],
        line: 86
      },
      "4": {
        loc: {
          start: {
            line: 93,
            column: 17
          },
          end: {
            line: 121,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 93,
            column: 17
          },
          end: {
            line: 121,
            column: 13
          }
        }, {
          start: {
            line: 100,
            column: 17
          },
          end: {
            line: 121,
            column: 13
          }
        }],
        line: 93
      },
      "5": {
        loc: {
          start: {
            line: 100,
            column: 17
          },
          end: {
            line: 121,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 100,
            column: 17
          },
          end: {
            line: 121,
            column: 13
          }
        }, {
          start: {
            line: 107,
            column: 17
          },
          end: {
            line: 121,
            column: 13
          }
        }],
        line: 100
      },
      "6": {
        loc: {
          start: {
            line: 107,
            column: 17
          },
          end: {
            line: 121,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 107,
            column: 17
          },
          end: {
            line: 121,
            column: 13
          }
        }, {
          start: {
            line: 114,
            column: 17
          },
          end: {
            line: 121,
            column: 13
          }
        }],
        line: 107
      },
      "7": {
        loc: {
          start: {
            line: 125,
            column: 57
          },
          end: {
            line: 125,
            column: 113
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 125,
            column: 82
          },
          end: {
            line: 125,
            column: 95
          }
        }, {
          start: {
            line: 125,
            column: 98
          },
          end: {
            line: 125,
            column: 113
          }
        }],
        line: 125
      },
      "8": {
        loc: {
          start: {
            line: 134,
            column: 8
          },
          end: {
            line: 147,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 134,
            column: 8
          },
          end: {
            line: 147,
            column: 9
          }
        }, {
          start: {
            line: 142,
            column: 13
          },
          end: {
            line: 147,
            column: 9
          }
        }],
        line: 134
      },
      "9": {
        loc: {
          start: {
            line: 135,
            column: 19
          },
          end: {
            line: 140,
            column: 68
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 135,
            column: 19
          },
          end: {
            line: 135,
            column: 66
          }
        }, {
          start: {
            line: 136,
            column: 16
          },
          end: {
            line: 136,
            column: 61
          }
        }, {
          start: {
            line: 137,
            column: 16
          },
          end: {
            line: 137,
            column: 68
          }
        }, {
          start: {
            line: 138,
            column: 16
          },
          end: {
            line: 138,
            column: 70
          }
        }, {
          start: {
            line: 139,
            column: 16
          },
          end: {
            line: 139,
            column: 66
          }
        }, {
          start: {
            line: 140,
            column: 16
          },
          end: {
            line: 140,
            column: 68
          }
        }],
        line: 135
      },
      "10": {
        loc: {
          start: {
            line: 142,
            column: 13
          },
          end: {
            line: 147,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 142,
            column: 13
          },
          end: {
            line: 147,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 142
      },
      "11": {
        loc: {
          start: {
            line: 143,
            column: 19
          },
          end: {
            line: 146,
            column: 68
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 143,
            column: 19
          },
          end: {
            line: 143,
            column: 72
          }
        }, {
          start: {
            line: 144,
            column: 16
          },
          end: {
            line: 144,
            column: 74
          }
        }, {
          start: {
            line: 145,
            column: 16
          },
          end: {
            line: 145,
            column: 66
          }
        }, {
          start: {
            line: 146,
            column: 16
          },
          end: {
            line: 146,
            column: 68
          }
        }],
        line: 143
      },
      "12": {
        loc: {
          start: {
            line: 156,
            column: 8
          },
          end: {
            line: 171,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 156,
            column: 8
          },
          end: {
            line: 171,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 156
      },
      "13": {
        loc: {
          start: {
            line: 157,
            column: 26
          },
          end: {
            line: 157,
            column: 63
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 157,
            column: 26
          },
          end: {
            line: 157,
            column: 54
          }
        }, {
          start: {
            line: 157,
            column: 58
          },
          end: {
            line: 157,
            column: 63
          }
        }],
        line: 157
      },
      "14": {
        loc: {
          start: {
            line: 159,
            column: 12
          },
          end: {
            line: 170,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 159,
            column: 12
          },
          end: {
            line: 170,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 159
      },
      "15": {
        loc: {
          start: {
            line: 160,
            column: 16
          },
          end: {
            line: 163,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 160,
            column: 16
          },
          end: {
            line: 163,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 160
      },
      "16": {
        loc: {
          start: {
            line: 172,
            column: 8
          },
          end: {
            line: 187,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 172,
            column: 8
          },
          end: {
            line: 187,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 172
      },
      "17": {
        loc: {
          start: {
            line: 173,
            column: 31
          },
          end: {
            line: 173,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 173,
            column: 31
          },
          end: {
            line: 173,
            column: 59
          }
        }, {
          start: {
            line: 173,
            column: 63
          },
          end: {
            line: 173,
            column: 77
          }
        }],
        line: 173
      },
      "18": {
        loc: {
          start: {
            line: 175,
            column: 12
          },
          end: {
            line: 186,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 175,
            column: 12
          },
          end: {
            line: 186,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 175
      },
      "19": {
        loc: {
          start: {
            line: 176,
            column: 16
          },
          end: {
            line: 179,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 176,
            column: 16
          },
          end: {
            line: 179,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 176
      },
      "20": {
        loc: {
          start: {
            line: 197,
            column: 28
          },
          end: {
            line: 197,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 197,
            column: 28
          },
          end: {
            line: 197,
            column: 42
          }
        }, {
          start: {
            line: 197,
            column: 46
          },
          end: {
            line: 197,
            column: 64
          }
        }],
        line: 197
      },
      "21": {
        loc: {
          start: {
            line: 198,
            column: 26
          },
          end: {
            line: 198,
            column: 63
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 198,
            column: 26
          },
          end: {
            line: 198,
            column: 54
          }
        }, {
          start: {
            line: 198,
            column: 58
          },
          end: {
            line: 198,
            column: 63
          }
        }],
        line: 198
      },
      "22": {
        loc: {
          start: {
            line: 199,
            column: 8
          },
          end: {
            line: 211,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 199,
            column: 8
          },
          end: {
            line: 211,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 199
      },
      "23": {
        loc: {
          start: {
            line: 201,
            column: 12
          },
          end: {
            line: 204,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 201,
            column: 12
          },
          end: {
            line: 204,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 201
      },
      "24": {
        loc: {
          start: {
            line: 221,
            column: 8
          },
          end: {
            line: 236,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 221,
            column: 8
          },
          end: {
            line: 236,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 221
      },
      "25": {
        loc: {
          start: {
            line: 222,
            column: 26
          },
          end: {
            line: 222,
            column: 63
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 222,
            column: 26
          },
          end: {
            line: 222,
            column: 54
          }
        }, {
          start: {
            line: 222,
            column: 58
          },
          end: {
            line: 222,
            column: 63
          }
        }],
        line: 222
      },
      "26": {
        loc: {
          start: {
            line: 224,
            column: 12
          },
          end: {
            line: 235,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 224,
            column: 12
          },
          end: {
            line: 235,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 224
      },
      "27": {
        loc: {
          start: {
            line: 225,
            column: 16
          },
          end: {
            line: 228,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 225,
            column: 16
          },
          end: {
            line: 228,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 225
      },
      "28": {
        loc: {
          start: {
            line: 246,
            column: 22
          },
          end: {
            line: 246,
            column: 58
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 246,
            column: 22
          },
          end: {
            line: 246,
            column: 50
          }
        }, {
          start: {
            line: 246,
            column: 54
          },
          end: {
            line: 246,
            column: 58
          }
        }],
        line: 246
      },
      "29": {
        loc: {
          start: {
            line: 248,
            column: 8
          },
          end: {
            line: 260,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 248,
            column: 8
          },
          end: {
            line: 260,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 248
      },
      "30": {
        loc: {
          start: {
            line: 250,
            column: 12
          },
          end: {
            line: 253,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 250,
            column: 12
          },
          end: {
            line: 253,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 250
      },
      "31": {
        loc: {
          start: {
            line: 271,
            column: 8
          },
          end: {
            line: 273,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 271,
            column: 8
          },
          end: {
            line: 273,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 271
      },
      "32": {
        loc: {
          start: {
            line: 274,
            column: 8
          },
          end: {
            line: 285,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 274,
            column: 8
          },
          end: {
            line: 285,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 274
      },
      "33": {
        loc: {
          start: {
            line: 275,
            column: 28
          },
          end: {
            line: 275,
            column: 79
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 275,
            column: 28
          },
          end: {
            line: 275,
            column: 42
          }
        }, {
          start: {
            line: 275,
            column: 46
          },
          end: {
            line: 275,
            column: 79
          }
        }],
        line: 275
      },
      "34": {
        loc: {
          start: {
            line: 277,
            column: 12
          },
          end: {
            line: 284,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 277,
            column: 12
          },
          end: {
            line: 284,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 277
      },
      "35": {
        loc: {
          start: {
            line: 293,
            column: 8
          },
          end: {
            line: 310,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 293,
            column: 8
          },
          end: {
            line: 310,
            column: 9
          }
        }, {
          start: {
            line: 303,
            column: 13
          },
          end: {
            line: 310,
            column: 9
          }
        }],
        line: 293
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0
    },
    b: {
      "0": [0],
      "1": [0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0, 0, 0, 0, 0],
      "10": [0, 0],
      "11": [0, 0, 0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\FittingLossCalculator.ts",
      mappings: ";AAAA;;;;;;;;GAQG;;;AAEH,2BAAkC;AAClC,+BAA4B;AAE5B,iCAAiC;AACjC,MAAM,uBAAuB,GAAG,GAAG,EAAE;IACnC,IAAI,CAAC;QACH,MAAM,QAAQ,GAAG,IAAA,WAAI,EAAC,SAAS,EAAE,sCAAsC,CAAC,CAAC;QACzE,MAAM,OAAO,GAAG,IAAA,iBAAY,EAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAC/C,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IAC7B,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,IAAI,CAAC,0DAA0D,CAAC,CAAC;QACzE,OAAO;YACL,QAAQ,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,eAAe,EAAE;YACzD,cAAc,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,WAAW,EAAE,EAAE,EAAE,aAAa,EAAE,EAAE,EAAE;YAC5E,oBAAoB,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,WAAW,EAAE,EAAE,EAAE;YACrD,gBAAgB,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE;SACjD,CAAC;IACJ,CAAC;AACH,CAAC,CAAC;AAEF,MAAM,mBAAmB,GAAG,uBAAuB,EAAE,CAAC;AAqCtD;;;GAGG;AACH,MAAa,qBAAqB;IAEhC;;;OAGG;IACI,MAAM,CAAC,yBAAyB,CAAC,KAA4B;QAClE,MAAM,EAAE,QAAQ,EAAE,UAAU,GAAG,KAAK,EAAE,GAAG,KAAK,CAAC;QAE/C,4DAA4D;QAC5D,qDAAqD;QACrD,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC;QAEhD,sCAAsC;QACtC,MAAM,YAAY,GAAG,UAAU,GAAG,KAAK,CAAC;QACxC,OAAO,UAAU,GAAG,YAAY,CAAC;IACnC,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,oBAAoB,CAChC,MAA4B,EAC5B,QAAgB,EAChB,aAAqB,KAAK;QAG1B,8BAA8B;QAC9B,MAAM,gBAAgB,GAAG,IAAI,CAAC,yBAAyB,CAAC,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC,CAAC;QAElF,+BAA+B;QAC/B,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAE9C,uCAAuC;QACvC,MAAM,YAAY,GAAG,aAAa,CAAC,OAAO,GAAG,gBAAgB,CAAC;QAE9D,OAAO;YACL,OAAO,EAAE,aAAa,CAAC,OAAO;YAC9B,YAAY;YACZ,gBAAgB;YAChB,WAAW,EAAE,MAAM,CAAC,IAAI;YACxB,aAAa,EAAE,aAAa,CAAC,aAAa;YAC1C,QAAQ,EAAE,aAAa,CAAC,QAAQ;YAChC,eAAe,EAAE,aAAa,CAAC,eAAe;SAC/C,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,UAAU,CAAC,MAA4B;QAMpD,MAAM,QAAQ,GAAa,EAAE,CAAC;QAC9B,MAAM,eAAe,GAAa,EAAE,CAAC;QAErC,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YAEhD,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjB,QAAQ,CAAC,IAAI,CAAC,iBAAiB,MAAM,CAAC,IAAI,yBAAyB,CAAC,CAAC;gBACrE,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,aAAa,EAAE,SAAS,EAAE,QAAQ,EAAE,eAAe,EAAE,CAAC;YAC/E,CAAC;YAED,2CAA2C;YAC3C,IAAI,OAAe,CAAC;YACpB,IAAI,iBAAyB,CAAC;YAE9B,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBAClC,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;gBAC5D,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;gBACzB,iBAAiB,GAAG,MAAM,CAAC,aAAa,CAAC;gBACzC,QAAQ,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;gBAClC,eAAe,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,eAAe,CAAC,CAAC;YAClD,CAAC;iBAAM,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;gBACvC,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;gBAC1D,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;gBACzB,iBAAiB,GAAG,MAAM,CAAC,aAAa,CAAC;gBACzC,QAAQ,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;gBAClC,eAAe,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,eAAe,CAAC,CAAC;YAClD,CAAC;iBAAM,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;gBAC9C,MAAM,MAAM,GAAG,IAAI,CAAC,uBAAuB,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;gBACjE,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;gBACzB,iBAAiB,GAAG,MAAM,CAAC,aAAa,CAAC;gBACzC,QAAQ,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;gBAClC,eAAe,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,eAAe,CAAC,CAAC;YAClD,CAAC;iBAAM,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC1C,MAAM,MAAM,GAAG,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;gBAC7D,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;gBACzB,iBAAiB,GAAG,MAAM,CAAC,aAAa,CAAC;gBACzC,QAAQ,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;gBAClC,eAAe,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,eAAe,CAAC,CAAC;YAClD,CAAC;iBAAM,CAAC;gBACN,2BAA2B;gBAC3B,MAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;gBAC9D,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;gBACzB,iBAAiB,GAAG,MAAM,CAAC,aAAa,CAAC;gBACzC,QAAQ,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;gBAClC,eAAe,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,eAAe,CAAC,CAAC;YAClD,CAAC;YAED,OAAO,EAAE,OAAO,EAAE,aAAa,EAAE,iBAAiB,EAAE,QAAQ,EAAE,eAAe,EAAE,CAAC;QAElF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,QAAQ,CAAC,IAAI,CAAC,+BAA+B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;YACzG,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,aAAa,EAAE,eAAe,EAAE,QAAQ,EAAE,eAAe,EAAE,CAAC;QACrF,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,cAAc,CAAC,MAA4B;QACxD,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,MAAM,CAAC;QAEnC,IAAI,SAAS,KAAK,OAAO,EAAE,CAAC;YAC1B,OAAO,mBAAmB,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC;gBAC/C,mBAAmB,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC;gBAC7C,mBAAmB,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC;gBACpD,mBAAmB,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,CAAC;gBACtD,mBAAmB,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC;gBAClD,mBAAmB,CAAC,gBAAgB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAC9D,CAAC;aAAM,IAAI,SAAS,KAAK,aAAa,EAAE,CAAC;YACvC,OAAO,mBAAmB,CAAC,oBAAoB,CAAC,MAAM,CAAC,IAAI,CAAC;gBACrD,mBAAmB,CAAC,oBAAoB,CAAC,WAAW,CAAC,IAAI,CAAC;gBAC1D,mBAAmB,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC;gBAClD,mBAAmB,CAAC,gBAAgB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAC9D,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,kBAAkB,CAAC,WAAgB,EAAE,MAA4B;QAM9E,MAAM,QAAQ,GAAa,EAAE,CAAC;QAC9B,MAAM,eAAe,GAAa,EAAE,CAAC;QAErC,IAAI,WAAW,CAAC,yBAAyB,EAAE,CAAC;YAC1C,MAAM,KAAK,GAAG,MAAM,CAAC,SAAS,EAAE,QAAQ,EAAE,IAAI,KAAK,CAAC;YACpD,MAAM,SAAS,GAAG,WAAW,CAAC,yBAAyB,CAAC,KAAK,CAAC,CAAC;YAE/D,IAAI,SAAS,EAAE,CAAC;gBACd,IAAI,UAAU,CAAC,KAAK,CAAC,GAAG,GAAG,EAAE,CAAC;oBAC5B,QAAQ,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;oBACtE,eAAe,CAAC,IAAI,CAAC,uEAAuE,CAAC,CAAC;gBAChG,CAAC;gBACD,OAAO;oBACL,OAAO,EAAE,SAAS,CAAC,CAAC;oBACpB,aAAa,EAAE,SAAS,KAAK,EAAE;oBAC/B,QAAQ;oBACR,eAAe;iBAChB,CAAC;YACJ,CAAC;QACH,CAAC;QAED,IAAI,WAAW,CAAC,cAAc,EAAE,CAAC;YAC/B,MAAM,UAAU,GAAG,MAAM,CAAC,SAAS,EAAE,QAAQ,EAAE,IAAI,cAAc,CAAC;YAClE,MAAM,UAAU,GAAG,WAAW,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;YAE1D,IAAI,UAAU,EAAE,CAAC;gBACf,IAAI,UAAU,KAAK,cAAc,EAAE,CAAC;oBAClC,QAAQ,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;oBAC3D,eAAe,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;gBAChF,CAAC;gBACD,OAAO;oBACL,OAAO,EAAE,UAAU,CAAC,CAAC;oBACrB,aAAa,EAAE,UAAU;oBACzB,QAAQ;oBACR,eAAe;iBAChB,CAAC;YACJ,CAAC;QACH,CAAC;QAED,QAAQ,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;QAC9D,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,aAAa,EAAE,SAAS,EAAE,QAAQ,EAAE,eAAe,EAAE,CAAC;IAC/E,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,gBAAgB,CAAC,WAAgB,EAAE,MAA4B;QAM5E,MAAM,QAAQ,GAAa,EAAE,CAAC;QAC9B,MAAM,eAAe,GAAa,EAAE,CAAC;QAErC,MAAM,WAAW,GAAG,MAAM,CAAC,OAAO,IAAI,kBAAkB,CAAC;QACzD,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,EAAE,QAAQ,EAAE,IAAI,KAAK,CAAC;QAExD,IAAI,WAAW,CAAC,aAAa,EAAE,CAAC,WAAW,CAAC,EAAE,WAAW,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC;YACvE,MAAM,KAAK,GAAG,WAAW,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;YAE5E,IAAI,UAAU,CAAC,SAAS,CAAC,GAAG,IAAI,EAAE,CAAC;gBACjC,QAAQ,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;gBAClE,eAAe,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;YAC7E,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,KAAK,CAAC,CAAC;gBAChB,aAAa,EAAE,GAAG,WAAW,kBAAkB,SAAS,EAAE;gBAC1D,QAAQ;gBACR,eAAe;aAChB,CAAC;QACJ,CAAC;QAED,QAAQ,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;QAC5D,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,aAAa,EAAE,SAAS,EAAE,QAAQ,EAAE,eAAe,EAAE,CAAC;IAC/E,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,uBAAuB,CAAC,WAAgB,EAAE,MAA4B;QAMnF,MAAM,QAAQ,GAAa,EAAE,CAAC;QAC9B,MAAM,eAAe,GAAa,EAAE,CAAC;QAErC,IAAI,WAAW,CAAC,yBAAyB,EAAE,CAAC;YAC1C,MAAM,KAAK,GAAG,MAAM,CAAC,SAAS,EAAE,QAAQ,EAAE,IAAI,KAAK,CAAC;YACpD,MAAM,SAAS,GAAG,WAAW,CAAC,yBAAyB,CAAC,KAAK,CAAC,CAAC;YAE/D,IAAI,SAAS,EAAE,CAAC;gBACd,IAAI,UAAU,CAAC,KAAK,CAAC,GAAG,GAAG,EAAE,CAAC;oBAC5B,QAAQ,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;oBAC5D,eAAe,CAAC,IAAI,CAAC,sEAAsE,CAAC,CAAC;gBAC/F,CAAC;gBACD,OAAO;oBACL,OAAO,EAAE,SAAS,CAAC,CAAC;oBACpB,aAAa,EAAE,SAAS,KAAK,EAAE;oBAC/B,QAAQ;oBACR,eAAe;iBAChB,CAAC;YACJ,CAAC;QACH,CAAC;QAED,QAAQ,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;QACnE,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,aAAa,EAAE,SAAS,EAAE,QAAQ,EAAE,eAAe,EAAE,CAAC;IAC/E,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,mBAAmB,CAAC,WAAgB,EAAE,MAA4B;QAM/E,MAAM,QAAQ,GAAa,EAAE,CAAC;QAC9B,MAAM,eAAe,GAAa,EAAE,CAAC;QAErC,MAAM,KAAK,GAAG,MAAM,CAAC,SAAS,EAAE,QAAQ,EAAE,IAAI,IAAI,CAAC;QACnD,MAAM,SAAS,GAAG,WAAW,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,CAAC;QAEtD,IAAI,SAAS,EAAE,CAAC;YACd,MAAM,QAAQ,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;YACnC,IAAI,QAAQ,GAAG,EAAE,EAAE,CAAC;gBAClB,QAAQ,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;gBACvD,eAAe,CAAC,IAAI,CAAC,yDAAyD,CAAC,CAAC;YAClF,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,SAAS,CAAC,CAAC;gBACpB,aAAa,EAAE,GAAG,KAAK,QAAQ;gBAC/B,QAAQ;gBACR,eAAe;aAChB,CAAC;QACJ,CAAC;QAED,QAAQ,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;QACvD,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,aAAa,EAAE,SAAS,EAAE,QAAQ,EAAE,eAAe,EAAE,CAAC;IAC/E,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,oBAAoB,CAAC,WAAgB,EAAE,MAA4B;QAMhF,MAAM,QAAQ,GAAa,EAAE,CAAC;QAC9B,MAAM,eAAe,GAAa,EAAE,CAAC;QAErC,4CAA4C;QAC5C,IAAI,OAAO,WAAW,CAAC,CAAC,KAAK,QAAQ,EAAE,CAAC;YACtC,OAAO,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC,EAAE,aAAa,EAAE,QAAQ,EAAE,QAAQ,EAAE,eAAe,EAAE,CAAC;QACxF,CAAC;QAED,IAAI,WAAW,CAAC,KAAK,EAAE,CAAC;YACtB,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YACpE,MAAM,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAC/C,IAAI,WAAW,EAAE,CAAC,EAAE,CAAC;gBACnB,OAAO;oBACL,OAAO,EAAE,WAAW,CAAC,CAAC;oBACtB,aAAa,EAAE,OAAO;oBACtB,QAAQ;oBACR,eAAe;iBAChB,CAAC;YACJ,CAAC;QACH,CAAC;QAED,QAAQ,CAAC,IAAI,CAAC,wDAAwD,CAAC,CAAC;QACxE,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,aAAa,EAAE,SAAS,EAAE,QAAQ,EAAE,eAAe,EAAE,CAAC;IAC/E,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,oBAAoB,CAAC,SAAkC;QACnE,IAAI,SAAS,KAAK,OAAO,EAAE,CAAC;YAC1B,OAAO;gBACL,GAAG,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,cAAc,CAAC,MAAM,CAAC;gBACzD,GAAG,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,cAAc,CAAC,IAAI,CAAC;gBACvD,GAAG,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,cAAc,CAAC,WAAW,CAAC;gBAC9D,GAAG,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,cAAc,CAAC,aAAa,CAAC;gBAChE,GAAG,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,OAAO,CAAC;gBAC5D,GAAG,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,SAAS,CAAC;aAC/D,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,OAAO;gBACL,GAAG,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,oBAAoB,CAAC,MAAM,CAAC;gBAC/D,GAAG,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,oBAAoB,CAAC,WAAW,CAAC;gBACpE,GAAG,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,OAAO,CAAC;gBAC5D,GAAG,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,SAAS,CAAC;aAC/D,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,kBAAkB;QAC9B,OAAO,mBAAmB,CAAC,QAAQ,CAAC;IACtC,CAAC;CACF;AAjWD,sDAiWC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\FittingLossCalculator.ts"],
      sourcesContent: ["/**\r\n * FittingLossCalculator - Modular calculation service for HVAC fitting losses\r\n * \r\n * MISSION-CRITICAL: Pure TypeScript functions for ASHRAE/SMACNA-compliant fitting loss calculations\r\n * Handles minor losses for elbows, tees, transitions, diffusers, and other HVAC components\r\n * \r\n * @see docs/implementation/duct-physics/fitting-loss-calculations.md\r\n * @see backend/data/fitting_coefficients.json\r\n */\r\n\r\nimport { readFileSync } from 'fs';\r\nimport { join } from 'path';\r\n\r\n// Load fitting coefficients data\r\nconst loadFittingCoefficients = () => {\r\n  try {\r\n    const dataPath = join(__dirname, '../../data/fitting_coefficients.json');\r\n    const rawData = readFileSync(dataPath, 'utf8');\r\n    return JSON.parse(rawData);\r\n  } catch (error) {\r\n    console.warn('Could not load fitting coefficients, using fallback data');\r\n    return {\r\n      metadata: { version: '1.0.0', standard: 'ASHRAE/SMACNA' },\r\n      round_fittings: { elbows: {}, tees: {}, transitions: {}, entries_exits: {} },\r\n      rectangular_fittings: { elbows: {}, transitions: {} },\r\n      special_fittings: { dampers: {}, diffusers: {} }\r\n    };\r\n  }\r\n};\r\n\r\nconst fittingCoefficients = loadFittingCoefficients();\r\n\r\n/**\r\n * Fitting configuration for loss calculations\r\n */\r\nexport interface FittingConfiguration {\r\n  type: string; // e.g., '90deg_round_smooth', 'tee_round_branch_90deg'\r\n  subtype?: string; // e.g., 'radius_to_diameter_ratios', 'flow_patterns'\r\n  parameter?: string | number; // e.g., '1.5', 0.5, 'straight_through'\r\n  ductShape: 'round' | 'rectangular';\r\n  diameter?: number; // inches (for round ducts)\r\n  width?: number; // inches (for rectangular ducts)\r\n  height?: number; // inches (for rectangular ducts)\r\n  additionalParams?: Record<string, any>; // For complex fittings\r\n}\r\n\r\n/**\r\n * Fitting loss calculation result\r\n */\r\nexport interface FittingLossResult {\r\n  kFactor: number; // Loss coefficient\r\n  pressureLoss: number; // inches w.g.\r\n  velocityPressure: number; // inches w.g.\r\n  fittingType: string;\r\n  configuration: string;\r\n  warnings: string[];\r\n  recommendations: string[];\r\n}\r\n\r\n/**\r\n * Velocity pressure calculation input\r\n */\r\nexport interface VelocityPressureInput {\r\n  velocity: number; // FPM\r\n  airDensity?: number; // lb/ft\xB3 (default: 0.075 at standard conditions)\r\n}\r\n\r\n/**\r\n * FittingLossCalculator - Pure calculation functions for fitting losses\r\n * CRITICAL: No dependencies on UI, storage, or external services\r\n */\r\nexport class FittingLossCalculator {\r\n  \r\n  /**\r\n   * Calculate velocity pressure from air velocity\r\n   * Formula: VP = (V/4005)\xB2 for standard air density\r\n   */\r\n  public static calculateVelocityPressure(input: VelocityPressureInput): number {\r\n    const { velocity, airDensity = 0.075 } = input;\r\n    \r\n    // Standard formula: VP = \u03C1V\xB2/(2gc) converted to inches w.g.\r\n    // Simplified for standard conditions: VP = (V/4005)\xB2\r\n    const standardVP = Math.pow(velocity / 4005, 2);\r\n    \r\n    // Adjust for non-standard air density\r\n    const densityRatio = airDensity / 0.075;\r\n    return standardVP * densityRatio;\r\n  }\r\n\r\n  /**\r\n   * Calculate fitting loss for a specific fitting configuration\r\n   */\r\n  public static calculateFittingLoss(\r\n    config: FittingConfiguration,\r\n    velocity: number,\r\n    airDensity: number = 0.075\r\n  ): FittingLossResult {\r\n    \r\n    // Calculate velocity pressure\r\n    const velocityPressure = this.calculateVelocityPressure({ velocity, airDensity });\r\n    \r\n    // Get K-factor for the fitting\r\n    const kFactorResult = this.getKFactor(config);\r\n    \r\n    // Calculate pressure loss: \u0394P = K \xD7 VP\r\n    const pressureLoss = kFactorResult.kFactor * velocityPressure;\r\n    \r\n    return {\r\n      kFactor: kFactorResult.kFactor,\r\n      pressureLoss,\r\n      velocityPressure,\r\n      fittingType: config.type,\r\n      configuration: kFactorResult.configuration,\r\n      warnings: kFactorResult.warnings,\r\n      recommendations: kFactorResult.recommendations\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get K-factor for a specific fitting configuration\r\n   */\r\n  private static getKFactor(config: FittingConfiguration): {\r\n    kFactor: number;\r\n    configuration: string;\r\n    warnings: string[];\r\n    recommendations: string[];\r\n  } {\r\n    const warnings: string[] = [];\r\n    const recommendations: string[] = [];\r\n    \r\n    try {\r\n      const fittingData = this.getFittingData(config);\r\n      \r\n      if (!fittingData) {\r\n        warnings.push(`Fitting type '${config.type}' not found in database`);\r\n        return { kFactor: 0.5, configuration: 'default', warnings, recommendations };\r\n      }\r\n\r\n      // Handle different fitting data structures\r\n      let kFactor: number;\r\n      let configDescription: string;\r\n\r\n      if (config.type.includes('elbow')) {\r\n        const result = this.handleElbowKFactor(fittingData, config);\r\n        kFactor = result.kFactor;\r\n        configDescription = result.configuration;\r\n        warnings.push(...result.warnings);\r\n        recommendations.push(...result.recommendations);\r\n      } else if (config.type.includes('tee')) {\r\n        const result = this.handleTeeKFactor(fittingData, config);\r\n        kFactor = result.kFactor;\r\n        configDescription = result.configuration;\r\n        warnings.push(...result.warnings);\r\n        recommendations.push(...result.recommendations);\r\n      } else if (config.type.includes('transition')) {\r\n        const result = this.handleTransitionKFactor(fittingData, config);\r\n        kFactor = result.kFactor;\r\n        configDescription = result.configuration;\r\n        warnings.push(...result.warnings);\r\n        recommendations.push(...result.recommendations);\r\n      } else if (config.type.includes('damper')) {\r\n        const result = this.handleDamperKFactor(fittingData, config);\r\n        kFactor = result.kFactor;\r\n        configDescription = result.configuration;\r\n        warnings.push(...result.warnings);\r\n        recommendations.push(...result.recommendations);\r\n      } else {\r\n        // Generic fitting handling\r\n        const result = this.handleGenericKFactor(fittingData, config);\r\n        kFactor = result.kFactor;\r\n        configDescription = result.configuration;\r\n        warnings.push(...result.warnings);\r\n        recommendations.push(...result.recommendations);\r\n      }\r\n\r\n      return { kFactor, configuration: configDescription, warnings, recommendations };\r\n\r\n    } catch (error) {\r\n      warnings.push(`Error calculating K-factor: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n      return { kFactor: 0.5, configuration: 'error_default', warnings, recommendations };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get fitting data from the coefficients database\r\n   */\r\n  private static getFittingData(config: FittingConfiguration): any {\r\n    const { type, ductShape } = config;\r\n    \r\n    if (ductShape === 'round') {\r\n      return fittingCoefficients.round_fittings.elbows[type] ||\r\n             fittingCoefficients.round_fittings.tees[type] ||\r\n             fittingCoefficients.round_fittings.transitions[type] ||\r\n             fittingCoefficients.round_fittings.entries_exits[type] ||\r\n             fittingCoefficients.special_fittings.dampers[type] ||\r\n             fittingCoefficients.special_fittings.diffusers[type];\r\n    } else if (ductShape === 'rectangular') {\r\n      return fittingCoefficients.rectangular_fittings.elbows[type] ||\r\n             fittingCoefficients.rectangular_fittings.transitions[type] ||\r\n             fittingCoefficients.special_fittings.dampers[type] ||\r\n             fittingCoefficients.special_fittings.diffusers[type];\r\n    }\r\n    \r\n    return null;\r\n  }\r\n\r\n  /**\r\n   * Handle elbow K-factor calculation\r\n   */\r\n  private static handleElbowKFactor(fittingData: any, config: FittingConfiguration): {\r\n    kFactor: number;\r\n    configuration: string;\r\n    warnings: string[];\r\n    recommendations: string[];\r\n  } {\r\n    const warnings: string[] = [];\r\n    const recommendations: string[] = [];\r\n    \r\n    if (fittingData.radius_to_diameter_ratios) {\r\n      const ratio = config.parameter?.toString() || '1.0';\r\n      const ratioData = fittingData.radius_to_diameter_ratios[ratio];\r\n      \r\n      if (ratioData) {\r\n        if (parseFloat(ratio) < 1.0) {\r\n          warnings.push('Sharp radius elbow may cause excessive pressure loss');\r\n          recommendations.push('Consider using radius-to-diameter ratio \u2265 1.5 for optimal performance');\r\n        }\r\n        return {\r\n          kFactor: ratioData.K,\r\n          configuration: `R/D = ${ratio}`,\r\n          warnings,\r\n          recommendations\r\n        };\r\n      }\r\n    }\r\n    \r\n    if (fittingData.configurations) {\r\n      const configType = config.parameter?.toString() || 'single_miter';\r\n      const configData = fittingData.configurations[configType];\r\n      \r\n      if (configData) {\r\n        if (configType === 'single_miter') {\r\n          warnings.push('Single miter elbow has high pressure loss');\r\n          recommendations.push('Consider using multiple miters or smooth radius elbow');\r\n        }\r\n        return {\r\n          kFactor: configData.K,\r\n          configuration: configType,\r\n          warnings,\r\n          recommendations\r\n        };\r\n      }\r\n    }\r\n    \r\n    warnings.push('Elbow configuration not found, using default');\r\n    return { kFactor: 0.3, configuration: 'default', warnings, recommendations };\r\n  }\r\n\r\n  /**\r\n   * Handle tee K-factor calculation\r\n   */\r\n  private static handleTeeKFactor(fittingData: any, config: FittingConfiguration): {\r\n    kFactor: number;\r\n    configuration: string;\r\n    warnings: string[];\r\n    recommendations: string[];\r\n  } {\r\n    const warnings: string[] = [];\r\n    const recommendations: string[] = [];\r\n    \r\n    const flowPattern = config.subtype || 'straight_through';\r\n    const areaRatio = config.parameter?.toString() || '0.5';\r\n    \r\n    if (fittingData.flow_patterns?.[flowPattern]?.area_ratios?.[areaRatio]) {\r\n      const kData = fittingData.flow_patterns[flowPattern].area_ratios[areaRatio];\r\n      \r\n      if (parseFloat(areaRatio) > 0.75) {\r\n        warnings.push('Large branch area ratio may cause flow imbalance');\r\n        recommendations.push('Consider flow balancing dampers for large branches');\r\n      }\r\n      \r\n      return {\r\n        kFactor: kData.K,\r\n        configuration: `${flowPattern}, area ratio = ${areaRatio}`,\r\n        warnings,\r\n        recommendations\r\n      };\r\n    }\r\n    \r\n    warnings.push('Tee configuration not found, using default');\r\n    return { kFactor: 0.6, configuration: 'default', warnings, recommendations };\r\n  }\r\n\r\n  /**\r\n   * Handle transition K-factor calculation\r\n   */\r\n  private static handleTransitionKFactor(fittingData: any, config: FittingConfiguration): {\r\n    kFactor: number;\r\n    configuration: string;\r\n    warnings: string[];\r\n    recommendations: string[];\r\n  } {\r\n    const warnings: string[] = [];\r\n    const recommendations: string[] = [];\r\n    \r\n    if (fittingData.length_to_diameter_ratios) {\r\n      const ratio = config.parameter?.toString() || '1.0';\r\n      const ratioData = fittingData.length_to_diameter_ratios[ratio];\r\n      \r\n      if (ratioData) {\r\n        if (parseFloat(ratio) < 1.5) {\r\n          warnings.push('Short transition may cause flow separation');\r\n          recommendations.push('Consider using length-to-diameter ratio \u2265 2.0 for gradual transition');\r\n        }\r\n        return {\r\n          kFactor: ratioData.K,\r\n          configuration: `L/D = ${ratio}`,\r\n          warnings,\r\n          recommendations\r\n        };\r\n      }\r\n    }\r\n    \r\n    warnings.push('Transition configuration not found, using default');\r\n    return { kFactor: 0.2, configuration: 'default', warnings, recommendations };\r\n  }\r\n\r\n  /**\r\n   * Handle damper K-factor calculation\r\n   */\r\n  private static handleDamperKFactor(fittingData: any, config: FittingConfiguration): {\r\n    kFactor: number;\r\n    configuration: string;\r\n    warnings: string[];\r\n    recommendations: string[];\r\n  } {\r\n    const warnings: string[] = [];\r\n    const recommendations: string[] = [];\r\n    \r\n    const angle = config.parameter?.toString() || '90';\r\n    const angleData = fittingData.opening_angles?.[angle];\r\n    \r\n    if (angleData) {\r\n      const angleNum = parseFloat(angle);\r\n      if (angleNum < 45) {\r\n        warnings.push('Damper significantly restricting flow');\r\n        recommendations.push('Consider opening damper further to reduce pressure loss');\r\n      }\r\n      \r\n      return {\r\n        kFactor: angleData.K,\r\n        configuration: `${angle}\xB0 open`,\r\n        warnings,\r\n        recommendations\r\n      };\r\n    }\r\n    \r\n    warnings.push('Damper angle not found, using default');\r\n    return { kFactor: 0.2, configuration: 'default', warnings, recommendations };\r\n  }\r\n\r\n  /**\r\n   * Handle generic fitting K-factor calculation\r\n   */\r\n  private static handleGenericKFactor(fittingData: any, config: FittingConfiguration): {\r\n    kFactor: number;\r\n    configuration: string;\r\n    warnings: string[];\r\n    recommendations: string[];\r\n  } {\r\n    const warnings: string[] = [];\r\n    const recommendations: string[] = [];\r\n    \r\n    // Try to find K value in various structures\r\n    if (typeof fittingData.K === 'number') {\r\n      return { kFactor: fittingData.K, configuration: 'direct', warnings, recommendations };\r\n    }\r\n    \r\n    if (fittingData.types) {\r\n      const subtype = config.subtype || Object.keys(fittingData.types)[0];\r\n      const subtypeData = fittingData.types[subtype];\r\n      if (subtypeData?.K) {\r\n        return {\r\n          kFactor: subtypeData.K,\r\n          configuration: subtype,\r\n          warnings,\r\n          recommendations\r\n        };\r\n      }\r\n    }\r\n    \r\n    warnings.push('Generic fitting configuration not found, using default');\r\n    return { kFactor: 0.5, configuration: 'default', warnings, recommendations };\r\n  }\r\n\r\n  /**\r\n   * Get available fitting types for a duct shape\r\n   */\r\n  public static getAvailableFittings(ductShape: 'round' | 'rectangular'): string[] {\r\n    if (ductShape === 'round') {\r\n      return [\r\n        ...Object.keys(fittingCoefficients.round_fittings.elbows),\r\n        ...Object.keys(fittingCoefficients.round_fittings.tees),\r\n        ...Object.keys(fittingCoefficients.round_fittings.transitions),\r\n        ...Object.keys(fittingCoefficients.round_fittings.entries_exits),\r\n        ...Object.keys(fittingCoefficients.special_fittings.dampers),\r\n        ...Object.keys(fittingCoefficients.special_fittings.diffusers)\r\n      ];\r\n    } else {\r\n      return [\r\n        ...Object.keys(fittingCoefficients.rectangular_fittings.elbows),\r\n        ...Object.keys(fittingCoefficients.rectangular_fittings.transitions),\r\n        ...Object.keys(fittingCoefficients.special_fittings.dampers),\r\n        ...Object.keys(fittingCoefficients.special_fittings.diffusers)\r\n      ];\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get fitting metadata and description\r\n   */\r\n  public static getFittingMetadata(): typeof fittingCoefficients.metadata {\r\n    return fittingCoefficients.metadata;\r\n  }\r\n}\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "e91a1b068362ed2a414b742b4982d879760938f3"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2fjbc9r3tm = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2fjbc9r3tm();
cov_2fjbc9r3tm().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_2fjbc9r3tm().s[1]++;
exports.FittingLossCalculator = void 0;
const fs_1 =
/* istanbul ignore next */
(cov_2fjbc9r3tm().s[2]++, require("fs"));
const path_1 =
/* istanbul ignore next */
(cov_2fjbc9r3tm().s[3]++, require("path"));
// Load fitting coefficients data
/* istanbul ignore next */
cov_2fjbc9r3tm().s[4]++;
const loadFittingCoefficients = () => {
  /* istanbul ignore next */
  cov_2fjbc9r3tm().f[0]++;
  cov_2fjbc9r3tm().s[5]++;
  try {
    const dataPath =
    /* istanbul ignore next */
    (cov_2fjbc9r3tm().s[6]++, (0, path_1.join)(__dirname, '../../data/fitting_coefficients.json'));
    const rawData =
    /* istanbul ignore next */
    (cov_2fjbc9r3tm().s[7]++, (0, fs_1.readFileSync)(dataPath, 'utf8'));
    /* istanbul ignore next */
    cov_2fjbc9r3tm().s[8]++;
    return JSON.parse(rawData);
  } catch (error) {
    /* istanbul ignore next */
    cov_2fjbc9r3tm().s[9]++;
    console.warn('Could not load fitting coefficients, using fallback data');
    /* istanbul ignore next */
    cov_2fjbc9r3tm().s[10]++;
    return {
      metadata: {
        version: '1.0.0',
        standard: 'ASHRAE/SMACNA'
      },
      round_fittings: {
        elbows: {},
        tees: {},
        transitions: {},
        entries_exits: {}
      },
      rectangular_fittings: {
        elbows: {},
        transitions: {}
      },
      special_fittings: {
        dampers: {},
        diffusers: {}
      }
    };
  }
};
const fittingCoefficients =
/* istanbul ignore next */
(cov_2fjbc9r3tm().s[11]++, loadFittingCoefficients());
/**
 * FittingLossCalculator - Pure calculation functions for fitting losses
 * CRITICAL: No dependencies on UI, storage, or external services
 */
class FittingLossCalculator {
  /**
   * Calculate velocity pressure from air velocity
   * Formula: VP = (V/4005)² for standard air density
   */
  static calculateVelocityPressure(input) {
    /* istanbul ignore next */
    cov_2fjbc9r3tm().f[1]++;
    const {
      velocity,
      airDensity =
      /* istanbul ignore next */
      (cov_2fjbc9r3tm().b[0][0]++, 0.075)
    } =
    /* istanbul ignore next */
    (cov_2fjbc9r3tm().s[12]++, input);
    // Standard formula: VP = ρV²/(2gc) converted to inches w.g.
    // Simplified for standard conditions: VP = (V/4005)²
    const standardVP =
    /* istanbul ignore next */
    (cov_2fjbc9r3tm().s[13]++, Math.pow(velocity / 4005, 2));
    // Adjust for non-standard air density
    const densityRatio =
    /* istanbul ignore next */
    (cov_2fjbc9r3tm().s[14]++, airDensity / 0.075);
    /* istanbul ignore next */
    cov_2fjbc9r3tm().s[15]++;
    return standardVP * densityRatio;
  }
  /**
   * Calculate fitting loss for a specific fitting configuration
   */
  static calculateFittingLoss(config, velocity, airDensity =
  /* istanbul ignore next */
  (cov_2fjbc9r3tm().b[1][0]++, 0.075)) {
    /* istanbul ignore next */
    cov_2fjbc9r3tm().f[2]++;
    // Calculate velocity pressure
    const velocityPressure =
    /* istanbul ignore next */
    (cov_2fjbc9r3tm().s[16]++, this.calculateVelocityPressure({
      velocity,
      airDensity
    }));
    // Get K-factor for the fitting
    const kFactorResult =
    /* istanbul ignore next */
    (cov_2fjbc9r3tm().s[17]++, this.getKFactor(config));
    // Calculate pressure loss: ΔP = K × VP
    const pressureLoss =
    /* istanbul ignore next */
    (cov_2fjbc9r3tm().s[18]++, kFactorResult.kFactor * velocityPressure);
    /* istanbul ignore next */
    cov_2fjbc9r3tm().s[19]++;
    return {
      kFactor: kFactorResult.kFactor,
      pressureLoss,
      velocityPressure,
      fittingType: config.type,
      configuration: kFactorResult.configuration,
      warnings: kFactorResult.warnings,
      recommendations: kFactorResult.recommendations
    };
  }
  /**
   * Get K-factor for a specific fitting configuration
   */
  static getKFactor(config) {
    /* istanbul ignore next */
    cov_2fjbc9r3tm().f[3]++;
    const warnings =
    /* istanbul ignore next */
    (cov_2fjbc9r3tm().s[20]++, []);
    const recommendations =
    /* istanbul ignore next */
    (cov_2fjbc9r3tm().s[21]++, []);
    /* istanbul ignore next */
    cov_2fjbc9r3tm().s[22]++;
    try {
      const fittingData =
      /* istanbul ignore next */
      (cov_2fjbc9r3tm().s[23]++, this.getFittingData(config));
      /* istanbul ignore next */
      cov_2fjbc9r3tm().s[24]++;
      if (!fittingData) {
        /* istanbul ignore next */
        cov_2fjbc9r3tm().b[2][0]++;
        cov_2fjbc9r3tm().s[25]++;
        warnings.push(`Fitting type '${config.type}' not found in database`);
        /* istanbul ignore next */
        cov_2fjbc9r3tm().s[26]++;
        return {
          kFactor: 0.5,
          configuration: 'default',
          warnings,
          recommendations
        };
      } else
      /* istanbul ignore next */
      {
        cov_2fjbc9r3tm().b[2][1]++;
      }
      // Handle different fitting data structures
      let kFactor;
      let configDescription;
      /* istanbul ignore next */
      cov_2fjbc9r3tm().s[27]++;
      if (config.type.includes('elbow')) {
        /* istanbul ignore next */
        cov_2fjbc9r3tm().b[3][0]++;
        const result =
        /* istanbul ignore next */
        (cov_2fjbc9r3tm().s[28]++, this.handleElbowKFactor(fittingData, config));
        /* istanbul ignore next */
        cov_2fjbc9r3tm().s[29]++;
        kFactor = result.kFactor;
        /* istanbul ignore next */
        cov_2fjbc9r3tm().s[30]++;
        configDescription = result.configuration;
        /* istanbul ignore next */
        cov_2fjbc9r3tm().s[31]++;
        warnings.push(...result.warnings);
        /* istanbul ignore next */
        cov_2fjbc9r3tm().s[32]++;
        recommendations.push(...result.recommendations);
      } else {
        /* istanbul ignore next */
        cov_2fjbc9r3tm().b[3][1]++;
        cov_2fjbc9r3tm().s[33]++;
        if (config.type.includes('tee')) {
          /* istanbul ignore next */
          cov_2fjbc9r3tm().b[4][0]++;
          const result =
          /* istanbul ignore next */
          (cov_2fjbc9r3tm().s[34]++, this.handleTeeKFactor(fittingData, config));
          /* istanbul ignore next */
          cov_2fjbc9r3tm().s[35]++;
          kFactor = result.kFactor;
          /* istanbul ignore next */
          cov_2fjbc9r3tm().s[36]++;
          configDescription = result.configuration;
          /* istanbul ignore next */
          cov_2fjbc9r3tm().s[37]++;
          warnings.push(...result.warnings);
          /* istanbul ignore next */
          cov_2fjbc9r3tm().s[38]++;
          recommendations.push(...result.recommendations);
        } else {
          /* istanbul ignore next */
          cov_2fjbc9r3tm().b[4][1]++;
          cov_2fjbc9r3tm().s[39]++;
          if (config.type.includes('transition')) {
            /* istanbul ignore next */
            cov_2fjbc9r3tm().b[5][0]++;
            const result =
            /* istanbul ignore next */
            (cov_2fjbc9r3tm().s[40]++, this.handleTransitionKFactor(fittingData, config));
            /* istanbul ignore next */
            cov_2fjbc9r3tm().s[41]++;
            kFactor = result.kFactor;
            /* istanbul ignore next */
            cov_2fjbc9r3tm().s[42]++;
            configDescription = result.configuration;
            /* istanbul ignore next */
            cov_2fjbc9r3tm().s[43]++;
            warnings.push(...result.warnings);
            /* istanbul ignore next */
            cov_2fjbc9r3tm().s[44]++;
            recommendations.push(...result.recommendations);
          } else {
            /* istanbul ignore next */
            cov_2fjbc9r3tm().b[5][1]++;
            cov_2fjbc9r3tm().s[45]++;
            if (config.type.includes('damper')) {
              /* istanbul ignore next */
              cov_2fjbc9r3tm().b[6][0]++;
              const result =
              /* istanbul ignore next */
              (cov_2fjbc9r3tm().s[46]++, this.handleDamperKFactor(fittingData, config));
              /* istanbul ignore next */
              cov_2fjbc9r3tm().s[47]++;
              kFactor = result.kFactor;
              /* istanbul ignore next */
              cov_2fjbc9r3tm().s[48]++;
              configDescription = result.configuration;
              /* istanbul ignore next */
              cov_2fjbc9r3tm().s[49]++;
              warnings.push(...result.warnings);
              /* istanbul ignore next */
              cov_2fjbc9r3tm().s[50]++;
              recommendations.push(...result.recommendations);
            } else {
              /* istanbul ignore next */
              cov_2fjbc9r3tm().b[6][1]++;
              // Generic fitting handling
              const result =
              /* istanbul ignore next */
              (cov_2fjbc9r3tm().s[51]++, this.handleGenericKFactor(fittingData, config));
              /* istanbul ignore next */
              cov_2fjbc9r3tm().s[52]++;
              kFactor = result.kFactor;
              /* istanbul ignore next */
              cov_2fjbc9r3tm().s[53]++;
              configDescription = result.configuration;
              /* istanbul ignore next */
              cov_2fjbc9r3tm().s[54]++;
              warnings.push(...result.warnings);
              /* istanbul ignore next */
              cov_2fjbc9r3tm().s[55]++;
              recommendations.push(...result.recommendations);
            }
          }
        }
      }
      /* istanbul ignore next */
      cov_2fjbc9r3tm().s[56]++;
      return {
        kFactor,
        configuration: configDescription,
        warnings,
        recommendations
      };
    } catch (error) {
      /* istanbul ignore next */
      cov_2fjbc9r3tm().s[57]++;
      warnings.push(`Error calculating K-factor: ${error instanceof Error ?
      /* istanbul ignore next */
      (cov_2fjbc9r3tm().b[7][0]++, error.message) :
      /* istanbul ignore next */
      (cov_2fjbc9r3tm().b[7][1]++, 'Unknown error')}`);
      /* istanbul ignore next */
      cov_2fjbc9r3tm().s[58]++;
      return {
        kFactor: 0.5,
        configuration: 'error_default',
        warnings,
        recommendations
      };
    }
  }
  /**
   * Get fitting data from the coefficients database
   */
  static getFittingData(config) {
    /* istanbul ignore next */
    cov_2fjbc9r3tm().f[4]++;
    const {
      type,
      ductShape
    } =
    /* istanbul ignore next */
    (cov_2fjbc9r3tm().s[59]++, config);
    /* istanbul ignore next */
    cov_2fjbc9r3tm().s[60]++;
    if (ductShape === 'round') {
      /* istanbul ignore next */
      cov_2fjbc9r3tm().b[8][0]++;
      cov_2fjbc9r3tm().s[61]++;
      return /* istanbul ignore next */(cov_2fjbc9r3tm().b[9][0]++, fittingCoefficients.round_fittings.elbows[type]) ||
      /* istanbul ignore next */
      (cov_2fjbc9r3tm().b[9][1]++, fittingCoefficients.round_fittings.tees[type]) ||
      /* istanbul ignore next */
      (cov_2fjbc9r3tm().b[9][2]++, fittingCoefficients.round_fittings.transitions[type]) ||
      /* istanbul ignore next */
      (cov_2fjbc9r3tm().b[9][3]++, fittingCoefficients.round_fittings.entries_exits[type]) ||
      /* istanbul ignore next */
      (cov_2fjbc9r3tm().b[9][4]++, fittingCoefficients.special_fittings.dampers[type]) ||
      /* istanbul ignore next */
      (cov_2fjbc9r3tm().b[9][5]++, fittingCoefficients.special_fittings.diffusers[type]);
    } else {
      /* istanbul ignore next */
      cov_2fjbc9r3tm().b[8][1]++;
      cov_2fjbc9r3tm().s[62]++;
      if (ductShape === 'rectangular') {
        /* istanbul ignore next */
        cov_2fjbc9r3tm().b[10][0]++;
        cov_2fjbc9r3tm().s[63]++;
        return /* istanbul ignore next */(cov_2fjbc9r3tm().b[11][0]++, fittingCoefficients.rectangular_fittings.elbows[type]) ||
        /* istanbul ignore next */
        (cov_2fjbc9r3tm().b[11][1]++, fittingCoefficients.rectangular_fittings.transitions[type]) ||
        /* istanbul ignore next */
        (cov_2fjbc9r3tm().b[11][2]++, fittingCoefficients.special_fittings.dampers[type]) ||
        /* istanbul ignore next */
        (cov_2fjbc9r3tm().b[11][3]++, fittingCoefficients.special_fittings.diffusers[type]);
      } else
      /* istanbul ignore next */
      {
        cov_2fjbc9r3tm().b[10][1]++;
      }
    }
    /* istanbul ignore next */
    cov_2fjbc9r3tm().s[64]++;
    return null;
  }
  /**
   * Handle elbow K-factor calculation
   */
  static handleElbowKFactor(fittingData, config) {
    /* istanbul ignore next */
    cov_2fjbc9r3tm().f[5]++;
    const warnings =
    /* istanbul ignore next */
    (cov_2fjbc9r3tm().s[65]++, []);
    const recommendations =
    /* istanbul ignore next */
    (cov_2fjbc9r3tm().s[66]++, []);
    /* istanbul ignore next */
    cov_2fjbc9r3tm().s[67]++;
    if (fittingData.radius_to_diameter_ratios) {
      /* istanbul ignore next */
      cov_2fjbc9r3tm().b[12][0]++;
      const ratio =
      /* istanbul ignore next */
      (cov_2fjbc9r3tm().s[68]++,
      /* istanbul ignore next */
      (cov_2fjbc9r3tm().b[13][0]++, config.parameter?.toString()) ||
      /* istanbul ignore next */
      (cov_2fjbc9r3tm().b[13][1]++, '1.0'));
      const ratioData =
      /* istanbul ignore next */
      (cov_2fjbc9r3tm().s[69]++, fittingData.radius_to_diameter_ratios[ratio]);
      /* istanbul ignore next */
      cov_2fjbc9r3tm().s[70]++;
      if (ratioData) {
        /* istanbul ignore next */
        cov_2fjbc9r3tm().b[14][0]++;
        cov_2fjbc9r3tm().s[71]++;
        if (parseFloat(ratio) < 1.0) {
          /* istanbul ignore next */
          cov_2fjbc9r3tm().b[15][0]++;
          cov_2fjbc9r3tm().s[72]++;
          warnings.push('Sharp radius elbow may cause excessive pressure loss');
          /* istanbul ignore next */
          cov_2fjbc9r3tm().s[73]++;
          recommendations.push('Consider using radius-to-diameter ratio ≥ 1.5 for optimal performance');
        } else
        /* istanbul ignore next */
        {
          cov_2fjbc9r3tm().b[15][1]++;
        }
        cov_2fjbc9r3tm().s[74]++;
        return {
          kFactor: ratioData.K,
          configuration: `R/D = ${ratio}`,
          warnings,
          recommendations
        };
      } else
      /* istanbul ignore next */
      {
        cov_2fjbc9r3tm().b[14][1]++;
      }
    } else
    /* istanbul ignore next */
    {
      cov_2fjbc9r3tm().b[12][1]++;
    }
    cov_2fjbc9r3tm().s[75]++;
    if (fittingData.configurations) {
      /* istanbul ignore next */
      cov_2fjbc9r3tm().b[16][0]++;
      const configType =
      /* istanbul ignore next */
      (cov_2fjbc9r3tm().s[76]++,
      /* istanbul ignore next */
      (cov_2fjbc9r3tm().b[17][0]++, config.parameter?.toString()) ||
      /* istanbul ignore next */
      (cov_2fjbc9r3tm().b[17][1]++, 'single_miter'));
      const configData =
      /* istanbul ignore next */
      (cov_2fjbc9r3tm().s[77]++, fittingData.configurations[configType]);
      /* istanbul ignore next */
      cov_2fjbc9r3tm().s[78]++;
      if (configData) {
        /* istanbul ignore next */
        cov_2fjbc9r3tm().b[18][0]++;
        cov_2fjbc9r3tm().s[79]++;
        if (configType === 'single_miter') {
          /* istanbul ignore next */
          cov_2fjbc9r3tm().b[19][0]++;
          cov_2fjbc9r3tm().s[80]++;
          warnings.push('Single miter elbow has high pressure loss');
          /* istanbul ignore next */
          cov_2fjbc9r3tm().s[81]++;
          recommendations.push('Consider using multiple miters or smooth radius elbow');
        } else
        /* istanbul ignore next */
        {
          cov_2fjbc9r3tm().b[19][1]++;
        }
        cov_2fjbc9r3tm().s[82]++;
        return {
          kFactor: configData.K,
          configuration: configType,
          warnings,
          recommendations
        };
      } else
      /* istanbul ignore next */
      {
        cov_2fjbc9r3tm().b[18][1]++;
      }
    } else
    /* istanbul ignore next */
    {
      cov_2fjbc9r3tm().b[16][1]++;
    }
    cov_2fjbc9r3tm().s[83]++;
    warnings.push('Elbow configuration not found, using default');
    /* istanbul ignore next */
    cov_2fjbc9r3tm().s[84]++;
    return {
      kFactor: 0.3,
      configuration: 'default',
      warnings,
      recommendations
    };
  }
  /**
   * Handle tee K-factor calculation
   */
  static handleTeeKFactor(fittingData, config) {
    /* istanbul ignore next */
    cov_2fjbc9r3tm().f[6]++;
    const warnings =
    /* istanbul ignore next */
    (cov_2fjbc9r3tm().s[85]++, []);
    const recommendations =
    /* istanbul ignore next */
    (cov_2fjbc9r3tm().s[86]++, []);
    const flowPattern =
    /* istanbul ignore next */
    (cov_2fjbc9r3tm().s[87]++,
    /* istanbul ignore next */
    (cov_2fjbc9r3tm().b[20][0]++, config.subtype) ||
    /* istanbul ignore next */
    (cov_2fjbc9r3tm().b[20][1]++, 'straight_through'));
    const areaRatio =
    /* istanbul ignore next */
    (cov_2fjbc9r3tm().s[88]++,
    /* istanbul ignore next */
    (cov_2fjbc9r3tm().b[21][0]++, config.parameter?.toString()) ||
    /* istanbul ignore next */
    (cov_2fjbc9r3tm().b[21][1]++, '0.5'));
    /* istanbul ignore next */
    cov_2fjbc9r3tm().s[89]++;
    if (fittingData.flow_patterns?.[flowPattern]?.area_ratios?.[areaRatio]) {
      /* istanbul ignore next */
      cov_2fjbc9r3tm().b[22][0]++;
      const kData =
      /* istanbul ignore next */
      (cov_2fjbc9r3tm().s[90]++, fittingData.flow_patterns[flowPattern].area_ratios[areaRatio]);
      /* istanbul ignore next */
      cov_2fjbc9r3tm().s[91]++;
      if (parseFloat(areaRatio) > 0.75) {
        /* istanbul ignore next */
        cov_2fjbc9r3tm().b[23][0]++;
        cov_2fjbc9r3tm().s[92]++;
        warnings.push('Large branch area ratio may cause flow imbalance');
        /* istanbul ignore next */
        cov_2fjbc9r3tm().s[93]++;
        recommendations.push('Consider flow balancing dampers for large branches');
      } else
      /* istanbul ignore next */
      {
        cov_2fjbc9r3tm().b[23][1]++;
      }
      cov_2fjbc9r3tm().s[94]++;
      return {
        kFactor: kData.K,
        configuration: `${flowPattern}, area ratio = ${areaRatio}`,
        warnings,
        recommendations
      };
    } else
    /* istanbul ignore next */
    {
      cov_2fjbc9r3tm().b[22][1]++;
    }
    cov_2fjbc9r3tm().s[95]++;
    warnings.push('Tee configuration not found, using default');
    /* istanbul ignore next */
    cov_2fjbc9r3tm().s[96]++;
    return {
      kFactor: 0.6,
      configuration: 'default',
      warnings,
      recommendations
    };
  }
  /**
   * Handle transition K-factor calculation
   */
  static handleTransitionKFactor(fittingData, config) {
    /* istanbul ignore next */
    cov_2fjbc9r3tm().f[7]++;
    const warnings =
    /* istanbul ignore next */
    (cov_2fjbc9r3tm().s[97]++, []);
    const recommendations =
    /* istanbul ignore next */
    (cov_2fjbc9r3tm().s[98]++, []);
    /* istanbul ignore next */
    cov_2fjbc9r3tm().s[99]++;
    if (fittingData.length_to_diameter_ratios) {
      /* istanbul ignore next */
      cov_2fjbc9r3tm().b[24][0]++;
      const ratio =
      /* istanbul ignore next */
      (cov_2fjbc9r3tm().s[100]++,
      /* istanbul ignore next */
      (cov_2fjbc9r3tm().b[25][0]++, config.parameter?.toString()) ||
      /* istanbul ignore next */
      (cov_2fjbc9r3tm().b[25][1]++, '1.0'));
      const ratioData =
      /* istanbul ignore next */
      (cov_2fjbc9r3tm().s[101]++, fittingData.length_to_diameter_ratios[ratio]);
      /* istanbul ignore next */
      cov_2fjbc9r3tm().s[102]++;
      if (ratioData) {
        /* istanbul ignore next */
        cov_2fjbc9r3tm().b[26][0]++;
        cov_2fjbc9r3tm().s[103]++;
        if (parseFloat(ratio) < 1.5) {
          /* istanbul ignore next */
          cov_2fjbc9r3tm().b[27][0]++;
          cov_2fjbc9r3tm().s[104]++;
          warnings.push('Short transition may cause flow separation');
          /* istanbul ignore next */
          cov_2fjbc9r3tm().s[105]++;
          recommendations.push('Consider using length-to-diameter ratio ≥ 2.0 for gradual transition');
        } else
        /* istanbul ignore next */
        {
          cov_2fjbc9r3tm().b[27][1]++;
        }
        cov_2fjbc9r3tm().s[106]++;
        return {
          kFactor: ratioData.K,
          configuration: `L/D = ${ratio}`,
          warnings,
          recommendations
        };
      } else
      /* istanbul ignore next */
      {
        cov_2fjbc9r3tm().b[26][1]++;
      }
    } else
    /* istanbul ignore next */
    {
      cov_2fjbc9r3tm().b[24][1]++;
    }
    cov_2fjbc9r3tm().s[107]++;
    warnings.push('Transition configuration not found, using default');
    /* istanbul ignore next */
    cov_2fjbc9r3tm().s[108]++;
    return {
      kFactor: 0.2,
      configuration: 'default',
      warnings,
      recommendations
    };
  }
  /**
   * Handle damper K-factor calculation
   */
  static handleDamperKFactor(fittingData, config) {
    /* istanbul ignore next */
    cov_2fjbc9r3tm().f[8]++;
    const warnings =
    /* istanbul ignore next */
    (cov_2fjbc9r3tm().s[109]++, []);
    const recommendations =
    /* istanbul ignore next */
    (cov_2fjbc9r3tm().s[110]++, []);
    const angle =
    /* istanbul ignore next */
    (cov_2fjbc9r3tm().s[111]++,
    /* istanbul ignore next */
    (cov_2fjbc9r3tm().b[28][0]++, config.parameter?.toString()) ||
    /* istanbul ignore next */
    (cov_2fjbc9r3tm().b[28][1]++, '90'));
    const angleData =
    /* istanbul ignore next */
    (cov_2fjbc9r3tm().s[112]++, fittingData.opening_angles?.[angle]);
    /* istanbul ignore next */
    cov_2fjbc9r3tm().s[113]++;
    if (angleData) {
      /* istanbul ignore next */
      cov_2fjbc9r3tm().b[29][0]++;
      const angleNum =
      /* istanbul ignore next */
      (cov_2fjbc9r3tm().s[114]++, parseFloat(angle));
      /* istanbul ignore next */
      cov_2fjbc9r3tm().s[115]++;
      if (angleNum < 45) {
        /* istanbul ignore next */
        cov_2fjbc9r3tm().b[30][0]++;
        cov_2fjbc9r3tm().s[116]++;
        warnings.push('Damper significantly restricting flow');
        /* istanbul ignore next */
        cov_2fjbc9r3tm().s[117]++;
        recommendations.push('Consider opening damper further to reduce pressure loss');
      } else
      /* istanbul ignore next */
      {
        cov_2fjbc9r3tm().b[30][1]++;
      }
      cov_2fjbc9r3tm().s[118]++;
      return {
        kFactor: angleData.K,
        configuration: `${angle}° open`,
        warnings,
        recommendations
      };
    } else
    /* istanbul ignore next */
    {
      cov_2fjbc9r3tm().b[29][1]++;
    }
    cov_2fjbc9r3tm().s[119]++;
    warnings.push('Damper angle not found, using default');
    /* istanbul ignore next */
    cov_2fjbc9r3tm().s[120]++;
    return {
      kFactor: 0.2,
      configuration: 'default',
      warnings,
      recommendations
    };
  }
  /**
   * Handle generic fitting K-factor calculation
   */
  static handleGenericKFactor(fittingData, config) {
    /* istanbul ignore next */
    cov_2fjbc9r3tm().f[9]++;
    const warnings =
    /* istanbul ignore next */
    (cov_2fjbc9r3tm().s[121]++, []);
    const recommendations =
    /* istanbul ignore next */
    (cov_2fjbc9r3tm().s[122]++, []);
    // Try to find K value in various structures
    /* istanbul ignore next */
    cov_2fjbc9r3tm().s[123]++;
    if (typeof fittingData.K === 'number') {
      /* istanbul ignore next */
      cov_2fjbc9r3tm().b[31][0]++;
      cov_2fjbc9r3tm().s[124]++;
      return {
        kFactor: fittingData.K,
        configuration: 'direct',
        warnings,
        recommendations
      };
    } else
    /* istanbul ignore next */
    {
      cov_2fjbc9r3tm().b[31][1]++;
    }
    cov_2fjbc9r3tm().s[125]++;
    if (fittingData.types) {
      /* istanbul ignore next */
      cov_2fjbc9r3tm().b[32][0]++;
      const subtype =
      /* istanbul ignore next */
      (cov_2fjbc9r3tm().s[126]++,
      /* istanbul ignore next */
      (cov_2fjbc9r3tm().b[33][0]++, config.subtype) ||
      /* istanbul ignore next */
      (cov_2fjbc9r3tm().b[33][1]++, Object.keys(fittingData.types)[0]));
      const subtypeData =
      /* istanbul ignore next */
      (cov_2fjbc9r3tm().s[127]++, fittingData.types[subtype]);
      /* istanbul ignore next */
      cov_2fjbc9r3tm().s[128]++;
      if (subtypeData?.K) {
        /* istanbul ignore next */
        cov_2fjbc9r3tm().b[34][0]++;
        cov_2fjbc9r3tm().s[129]++;
        return {
          kFactor: subtypeData.K,
          configuration: subtype,
          warnings,
          recommendations
        };
      } else
      /* istanbul ignore next */
      {
        cov_2fjbc9r3tm().b[34][1]++;
      }
    } else
    /* istanbul ignore next */
    {
      cov_2fjbc9r3tm().b[32][1]++;
    }
    cov_2fjbc9r3tm().s[130]++;
    warnings.push('Generic fitting configuration not found, using default');
    /* istanbul ignore next */
    cov_2fjbc9r3tm().s[131]++;
    return {
      kFactor: 0.5,
      configuration: 'default',
      warnings,
      recommendations
    };
  }
  /**
   * Get available fitting types for a duct shape
   */
  static getAvailableFittings(ductShape) {
    /* istanbul ignore next */
    cov_2fjbc9r3tm().f[10]++;
    cov_2fjbc9r3tm().s[132]++;
    if (ductShape === 'round') {
      /* istanbul ignore next */
      cov_2fjbc9r3tm().b[35][0]++;
      cov_2fjbc9r3tm().s[133]++;
      return [...Object.keys(fittingCoefficients.round_fittings.elbows), ...Object.keys(fittingCoefficients.round_fittings.tees), ...Object.keys(fittingCoefficients.round_fittings.transitions), ...Object.keys(fittingCoefficients.round_fittings.entries_exits), ...Object.keys(fittingCoefficients.special_fittings.dampers), ...Object.keys(fittingCoefficients.special_fittings.diffusers)];
    } else {
      /* istanbul ignore next */
      cov_2fjbc9r3tm().b[35][1]++;
      cov_2fjbc9r3tm().s[134]++;
      return [...Object.keys(fittingCoefficients.rectangular_fittings.elbows), ...Object.keys(fittingCoefficients.rectangular_fittings.transitions), ...Object.keys(fittingCoefficients.special_fittings.dampers), ...Object.keys(fittingCoefficients.special_fittings.diffusers)];
    }
  }
  /**
   * Get fitting metadata and description
   */
  static getFittingMetadata() {
    /* istanbul ignore next */
    cov_2fjbc9r3tm().f[11]++;
    cov_2fjbc9r3tm().s[135]++;
    return fittingCoefficients.metadata;
  }
}
/* istanbul ignore next */
cov_2fjbc9r3tm().s[136]++;
exports.FittingLossCalculator = FittingLossCalculator;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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