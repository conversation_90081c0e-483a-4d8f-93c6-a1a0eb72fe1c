38757b4a0fe06afb9a8ce192592066a1
"use strict";

/**
 * System Performance Analysis Engine
 *
 * Comprehensive performance analysis service for Phase 3 Priority 3: Advanced System Analysis Tools
 * Provides real-time monitoring, trend analysis, efficiency calculations, and performance benchmarking
 * capabilities for HVAC duct systems.
 *
 * @version 3.0.0
 * <AUTHOR> Suite Development Team
 */
/* istanbul ignore next */
function cov_sd8kdgqk0() {
  var path = "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\SystemPerformanceAnalysisEngine.ts";
  var hash = "0ab3351d35486dd2fc51b40b60c8eeb191be70fc";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\SystemPerformanceAnalysisEngine.ts",
    statementMap: {
      "0": {
        start: {
          line: 12,
          column: 0
        },
        end: {
          line: 12,
          column: 62
        }
      },
      "1": {
        start: {
          line: 13,
          column: 0
        },
        end: {
          line: 13,
          column: 49
        }
      },
      "2": {
        start: {
          line: 14,
          column: 30
        },
        end: {
          line: 14,
          column: 68
        }
      },
      "3": {
        start: {
          line: 15,
          column: 35
        },
        end: {
          line: 15,
          column: 72
        }
      },
      "4": {
        start: {
          line: 31,
          column: 8
        },
        end: {
          line: 68,
          column: 9
        }
      },
      "5": {
        start: {
          line: 32,
          column: 31
        },
        end: {
          line: 32,
          column: 78
        }
      },
      "6": {
        start: {
          line: 33,
          column: 30
        },
        end: {
          line: 33,
          column: 40
        }
      },
      "7": {
        start: {
          line: 35,
          column: 39
        },
        end: {
          line: 35,
          column: 113
        }
      },
      "8": {
        start: {
          line: 37,
          column: 34
        },
        end: {
          line: 39,
          column: 51
        }
      },
      "9": {
        start: {
          line: 41,
          column: 40
        },
        end: {
          line: 41,
          column: 118
        }
      },
      "10": {
        start: {
          line: 43,
          column: 39
        },
        end: {
          line: 43,
          column: 114
        }
      },
      "11": {
        start: {
          line: 45,
          column: 38
        },
        end: {
          line: 45,
          column: 108
        }
      },
      "12": {
        start: {
          line: 47,
          column: 36
        },
        end: {
          line: 47,
          column: 142
        }
      },
      "13": {
        start: {
          line: 49,
          column: 40
        },
        end: {
          line: 49,
          column: 118
        }
      },
      "14": {
        start: {
          line: 50,
          column: 29
        },
        end: {
          line: 61,
          column: 13
        }
      },
      "15": {
        start: {
          line: 63,
          column: 12
        },
        end: {
          line: 63,
          column: 58
        }
      },
      "16": {
        start: {
          line: 64,
          column: 12
        },
        end: {
          line: 64,
          column: 28
        }
      },
      "17": {
        start: {
          line: 67,
          column: 12
        },
        end: {
          line: 67,
          column: 127
        }
      },
      "18": {
        start: {
          line: 74,
          column: 26
        },
        end: {
          line: 74,
          column: 36
        }
      },
      "19": {
        start: {
          line: 76,
          column: 37
        },
        end: {
          line: 80,
          column: 10
        }
      },
      "20": {
        start: {
          line: 82,
          column: 31
        },
        end: {
          line: 82,
          column: 120
        }
      },
      "21": {
        start: {
          line: 84,
          column: 31
        },
        end: {
          line: 84,
          column: 80
        }
      },
      "22": {
        start: {
          line: 86,
          column: 34
        },
        end: {
          line: 86,
          column: 130
        }
      },
      "23": {
        start: {
          line: 88,
          column: 37
        },
        end: {
          line: 88,
          column: 108
        }
      },
      "24": {
        start: {
          line: 90,
          column: 31
        },
        end: {
          line: 90,
          column: 92
        }
      },
      "25": {
        start: {
          line: 91,
          column: 35
        },
        end: {
          line: 119,
          column: 9
        }
      },
      "26": {
        start: {
          line: 120,
          column: 8
        },
        end: {
          line: 120,
          column: 34
        }
      },
      "27": {
        start: {
          line: 126,
          column: 8
        },
        end: {
          line: 139,
          column: 10
        }
      },
      "28": {
        start: {
          line: 145,
          column: 30
        },
        end: {
          line: 145,
          column: 80
        }
      },
      "29": {
        start: {
          line: 146,
          column: 31
        },
        end: {
          line: 146,
          column: 82
        }
      },
      "30": {
        start: {
          line: 149,
          column: 27
        },
        end: {
          line: 149,
          column: 103
        }
      },
      "31": {
        start: {
          line: 150,
          column: 22
        },
        end: {
          line: 150,
          column: 76
        }
      },
      "32": {
        start: {
          line: 151,
          column: 24
        },
        end: {
          line: 151,
          column: 37
        }
      },
      "33": {
        start: {
          line: 152,
          column: 22
        },
        end: {
          line: 152,
          column: 71
        }
      },
      "34": {
        start: {
          line: 153,
          column: 30
        },
        end: {
          line: 178,
          column: 9
        }
      },
      "35": {
        start: {
          line: 179,
          column: 8
        },
        end: {
          line: 184,
          column: 10
        }
      },
      "36": {
        start: {
          line: 190,
          column: 30
        },
        end: {
          line: 190,
          column: 80
        }
      },
      "37": {
        start: {
          line: 193,
          column: 29
        },
        end: {
          line: 193,
          column: 49
        }
      },
      "38": {
        start: {
          line: 194,
          column: 27
        },
        end: {
          line: 194,
          column: 63
        }
      },
      "39": {
        start: {
          line: 195,
          column: 8
        },
        end: {
          line: 203,
          column: 10
        }
      },
      "40": {
        start: {
          line: 209,
          column: 30
        },
        end: {
          line: 209,
          column: 80
        }
      },
      "41": {
        start: {
          line: 210,
          column: 31
        },
        end: {
          line: 210,
          column: 82
        }
      },
      "42": {
        start: {
          line: 212,
          column: 33
        },
        end: {
          line: 212,
          column: 78
        }
      },
      "43": {
        start: {
          line: 213,
          column: 36
        },
        end: {
          line: 213,
          column: 118
        }
      },
      "44": {
        start: {
          line: 214,
          column: 39
        },
        end: {
          line: 214,
          column: 75
        }
      },
      "45": {
        start: {
          line: 215,
          column: 8
        },
        end: {
          line: 219,
          column: 10
        }
      },
      "46": {
        start: {
          line: 225,
          column: 30
        },
        end: {
          line: 225,
          column: 80
        }
      },
      "47": {
        start: {
          line: 227,
          column: 25
        },
        end: {
          line: 227,
          column: 44
        }
      },
      "48": {
        start: {
          line: 228,
          column: 27
        },
        end: {
          line: 228,
          column: 64
        }
      },
      "49": {
        start: {
          line: 229,
          column: 31
        },
        end: {
          line: 229,
          column: 61
        }
      },
      "50": {
        start: {
          line: 230,
          column: 32
        },
        end: {
          line: 230,
          column: 84
        }
      },
      "51": {
        start: {
          line: 231,
          column: 8
        },
        end: {
          line: 237,
          column: 10
        }
      },
      "52": {
        start: {
          line: 244,
          column: 30
        },
        end: {
          line: 244,
          column: 34
        }
      },
      "53": {
        start: {
          line: 245,
          column: 34
        },
        end: {
          line: 245,
          column: 38
        }
      },
      "54": {
        start: {
          line: 246,
          column: 29
        },
        end: {
          line: 246,
          column: 89
        }
      },
      "55": {
        start: {
          line: 248,
          column: 8
        },
        end: {
          line: 257,
          column: 71
        }
      },
      "56": {
        start: {
          line: 249,
          column: 12
        },
        end: {
          line: 249,
          column: 72
        }
      },
      "57": {
        start: {
          line: 250,
          column: 13
        },
        end: {
          line: 257,
          column: 71
        }
      },
      "58": {
        start: {
          line: 251,
          column: 12
        },
        end: {
          line: 251,
          column: 67
        }
      },
      "59": {
        start: {
          line: 252,
          column: 13
        },
        end: {
          line: 257,
          column: 71
        }
      },
      "60": {
        start: {
          line: 253,
          column: 12
        },
        end: {
          line: 253,
          column: 73
        }
      },
      "61": {
        start: {
          line: 254,
          column: 13
        },
        end: {
          line: 257,
          column: 71
        }
      },
      "62": {
        start: {
          line: 255,
          column: 12
        },
        end: {
          line: 255,
          column: 67
        }
      },
      "63": {
        start: {
          line: 257,
          column: 12
        },
        end: {
          line: 257,
          column: 71
        }
      },
      "64": {
        start: {
          line: 258,
          column: 8
        },
        end: {
          line: 265,
          column: 10
        }
      },
      "65": {
        start: {
          line: 271,
          column: 8
        },
        end: {
          line: 294,
          column: 9
        }
      },
      "66": {
        start: {
          line: 273,
          column: 16
        },
        end: {
          line: 278,
          column: 18
        }
      },
      "67": {
        start: {
          line: 280,
          column: 16
        },
        end: {
          line: 285,
          column: 18
        }
      },
      "68": {
        start: {
          line: 287,
          column: 16
        },
        end: {
          line: 291,
          column: 18
        }
      },
      "69": {
        start: {
          line: 293,
          column: 16
        },
        end: {
          line: 293,
          column: 70
        }
      },
      "70": {
        start: {
          line: 302,
          column: 8
        },
        end: {
          line: 312,
          column: 10
        }
      },
      "71": {
        start: {
          line: 318,
          column: 26
        },
        end: {
          line: 318,
          column: 36
        }
      },
      "72": {
        start: {
          line: 319,
          column: 23
        },
        end: {
          line: 319,
          column: 65
        }
      },
      "73": {
        start: {
          line: 320,
          column: 8
        },
        end: {
          line: 320,
          column: 61
        }
      },
      "74": {
        start: {
          line: 326,
          column: 8
        },
        end: {
          line: 368,
          column: 10
        }
      },
      "75": {
        start: {
          line: 374,
          column: 8
        },
        end: {
          line: 376,
          column: 9
        }
      },
      "76": {
        start: {
          line: 375,
          column: 12
        },
        end: {
          line: 375,
          column: 53
        }
      },
      "77": {
        start: {
          line: 378,
          column: 30
        },
        end: {
          line: 378,
          column: 74
        }
      },
      "78": {
        start: {
          line: 380,
          column: 33
        },
        end: {
          line: 380,
          column: 76
        }
      },
      "79": {
        start: {
          line: 382,
          column: 26
        },
        end: {
          line: 382,
          column: 78
        }
      },
      "80": {
        start: {
          line: 384,
          column: 35
        },
        end: {
          line: 384,
          column: 82
        }
      },
      "81": {
        start: {
          line: 386,
          column: 32
        },
        end: {
          line: 386,
          column: 77
        }
      },
      "82": {
        start: {
          line: 387,
          column: 26
        },
        end: {
          line: 392,
          column: 9
        }
      },
      "83": {
        start: {
          line: 393,
          column: 8
        },
        end: {
          line: 401,
          column: 10
        }
      },
      "84": {
        start: {
          line: 407,
          column: 8
        },
        end: {
          line: 409,
          column: 9
        }
      },
      "85": {
        start: {
          line: 408,
          column: 12
        },
        end: {
          line: 408,
          column: 92
        }
      },
      "86": {
        start: {
          line: 411,
          column: 33
        },
        end: {
          line: 411,
          column: 88
        }
      },
      "87": {
        start: {
          line: 411,
          column: 60
        },
        end: {
          line: 411,
          column: 87
        }
      },
      "88": {
        start: {
          line: 412,
          column: 18
        },
        end: {
          line: 412,
          column: 41
        }
      },
      "89": {
        start: {
          line: 413,
          column: 18
        },
        end: {
          line: 413,
          column: 56
        }
      },
      "90": {
        start: {
          line: 413,
          column: 54
        },
        end: {
          line: 413,
          column: 55
        }
      },
      "91": {
        start: {
          line: 414,
          column: 21
        },
        end: {
          line: 414,
          column: 49
        }
      },
      "92": {
        start: {
          line: 414,
          column: 40
        },
        end: {
          line: 414,
          column: 45
        }
      },
      "93": {
        start: {
          line: 415,
          column: 21
        },
        end: {
          line: 415,
          column: 64
        }
      },
      "94": {
        start: {
          line: 415,
          column: 55
        },
        end: {
          line: 415,
          column: 60
        }
      },
      "95": {
        start: {
          line: 416,
          column: 22
        },
        end: {
          line: 416,
          column: 81
        }
      },
      "96": {
        start: {
          line: 416,
          column: 47
        },
        end: {
          line: 416,
          column: 77
        }
      },
      "97": {
        start: {
          line: 417,
          column: 22
        },
        end: {
          line: 417,
          column: 61
        }
      },
      "98": {
        start: {
          line: 417,
          column: 44
        },
        end: {
          line: 417,
          column: 57
        }
      },
      "99": {
        start: {
          line: 418,
          column: 22
        },
        end: {
          line: 418,
          column: 75
        }
      },
      "100": {
        start: {
          line: 419,
          column: 26
        },
        end: {
          line: 419,
          column: 41
        }
      },
      "101": {
        start: {
          line: 421,
          column: 8
        },
        end: {
          line: 429,
          column: 9
        }
      },
      "102": {
        start: {
          line: 422,
          column: 12
        },
        end: {
          line: 422,
          column: 68
        }
      },
      "103": {
        start: {
          line: 424,
          column: 13
        },
        end: {
          line: 429,
          column: 9
        }
      },
      "104": {
        start: {
          line: 425,
          column: 12
        },
        end: {
          line: 425,
          column: 71
        }
      },
      "105": {
        start: {
          line: 428,
          column: 12
        },
        end: {
          line: 428,
          column: 71
        }
      },
      "106": {
        start: {
          line: 430,
          column: 8
        },
        end: {
          line: 430,
          column: 40
        }
      },
      "107": {
        start: {
          line: 438,
          column: 8
        },
        end: {
          line: 451,
          column: 10
        }
      },
      "108": {
        start: {
          line: 457,
          column: 26
        },
        end: {
          line: 457,
          column: 28
        }
      },
      "109": {
        start: {
          line: 459,
          column: 8
        },
        end: {
          line: 494,
          column: 9
        }
      },
      "110": {
        start: {
          line: 460,
          column: 35
        },
        end: {
          line: 460,
          column: 99
        }
      },
      "111": {
        start: {
          line: 461,
          column: 38
        },
        end: {
          line: 461,
          column: 75
        }
      },
      "112": {
        start: {
          line: 462,
          column: 35
        },
        end: {
          line: 462,
          column: 69
        }
      },
      "113": {
        start: {
          line: 463,
          column: 12
        },
        end: {
          line: 493,
          column: 13
        }
      },
      "114": {
        start: {
          line: 464,
          column: 16
        },
        end: {
          line: 492,
          column: 19
        }
      },
      "115": {
        start: {
          line: 495,
          column: 8
        },
        end: {
          line: 495,
          column: 25
        }
      },
      "116": {
        start: {
          line: 503,
          column: 8
        },
        end: {
          line: 535,
          column: 10
        }
      },
      "117": {
        start: {
          line: 542,
          column: 8
        },
        end: {
          line: 580,
          column: 10
        }
      },
      "118": {
        start: {
          line: 588,
          column: 33
        },
        end: {
          line: 588,
          column: 74
        }
      },
      "119": {
        start: {
          line: 589,
          column: 32
        },
        end: {
          line: 589,
          column: 34
        }
      },
      "120": {
        start: {
          line: 590,
          column: 28
        },
        end: {
          line: 590,
          column: 30
        }
      },
      "121": {
        start: {
          line: 591,
          column: 8
        },
        end: {
          line: 614,
          column: 10
        }
      },
      "122": {
        start: {
          line: 622,
          column: 34
        },
        end: {
          line: 622,
          column: 35
        }
      },
      "123": {
        start: {
          line: 623,
          column: 23
        },
        end: {
          line: 623,
          column: 60
        }
      },
      "124": {
        start: {
          line: 625,
          column: 8
        },
        end: {
          line: 626,
          column: 22
        }
      },
      "125": {
        start: {
          line: 626,
          column: 12
        },
        end: {
          line: 626,
          column: 22
        }
      },
      "126": {
        start: {
          line: 627,
          column: 8
        },
        end: {
          line: 628,
          column: 22
        }
      },
      "127": {
        start: {
          line: 628,
          column: 12
        },
        end: {
          line: 628,
          column: 22
        }
      },
      "128": {
        start: {
          line: 629,
          column: 8
        },
        end: {
          line: 630,
          column: 38
        }
      },
      "129": {
        start: {
          line: 630,
          column: 12
        },
        end: {
          line: 630,
          column: 38
        }
      },
      "130": {
        start: {
          line: 631,
          column: 8
        },
        end: {
          line: 632,
          column: 38
        }
      },
      "131": {
        start: {
          line: 632,
          column: 12
        },
        end: {
          line: 632,
          column: 38
        }
      },
      "132": {
        start: {
          line: 633,
          column: 8
        },
        end: {
          line: 634,
          column: 22
        }
      },
      "133": {
        start: {
          line: 634,
          column: 12
        },
        end: {
          line: 634,
          column: 22
        }
      },
      "134": {
        start: {
          line: 635,
          column: 8
        },
        end: {
          line: 635,
          column: 17
        }
      },
      "135": {
        start: {
          line: 642,
          column: 8
        },
        end: {
          line: 673,
          column: 10
        }
      },
      "136": {
        start: {
          line: 679,
          column: 23
        },
        end: {
          line: 679,
          column: 25
        }
      },
      "137": {
        start: {
          line: 681,
          column: 8
        },
        end: {
          line: 698,
          column: 9
        }
      },
      "138": {
        start: {
          line: 682,
          column: 12
        },
        end: {
          line: 697,
          column: 15
        }
      },
      "139": {
        start: {
          line: 700,
          column: 8
        },
        end: {
          line: 717,
          column: 9
        }
      },
      "140": {
        start: {
          line: 701,
          column: 12
        },
        end: {
          line: 716,
          column: 15
        }
      },
      "141": {
        start: {
          line: 718,
          column: 8
        },
        end: {
          line: 718,
          column: 22
        }
      },
      "142": {
        start: {
          line: 724,
          column: 32
        },
        end: {
          line: 724,
          column: 34
        }
      },
      "143": {
        start: {
          line: 726,
          column: 8
        },
        end: {
          line: 750,
          column: 9
        }
      },
      "144": {
        start: {
          line: 727,
          column: 12
        },
        end: {
          line: 749,
          column: 15
        }
      },
      "145": {
        start: {
          line: 751,
          column: 8
        },
        end: {
          line: 751,
          column: 31
        }
      },
      "146": {
        start: {
          line: 758,
          column: 8
        },
        end: {
          line: 784,
          column: 10
        }
      },
      "147": {
        start: {
          line: 787,
          column: 0
        },
        end: {
          line: 787,
          column: 74
        }
      },
      "148": {
        start: {
          line: 788,
          column: 0
        },
        end: {
          line: 788,
          column: 50
        }
      },
      "149": {
        start: {
          line: 789,
          column: 0
        },
        end: {
          line: 789,
          column: 59
        }
      },
      "150": {
        start: {
          line: 790,
          column: 0
        },
        end: {
          line: 790,
          column: 63
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 30,
            column: 4
          },
          end: {
            line: 30,
            column: 5
          }
        },
        loc: {
          start: {
            line: 30,
            column: 94
          },
          end: {
            line: 69,
            column: 5
          }
        },
        line: 30
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 73,
            column: 4
          },
          end: {
            line: 73,
            column: 5
          }
        },
        loc: {
          start: {
            line: 73,
            column: 81
          },
          end: {
            line: 121,
            column: 5
          }
        },
        line: 73
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 125,
            column: 4
          },
          end: {
            line: 125,
            column: 5
          }
        },
        loc: {
          start: {
            line: 125,
            column: 88
          },
          end: {
            line: 140,
            column: 5
          }
        },
        line: 125
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 144,
            column: 4
          },
          end: {
            line: 144,
            column: 5
          }
        },
        loc: {
          start: {
            line: 144,
            column: 72
          },
          end: {
            line: 185,
            column: 5
          }
        },
        line: 144
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 189,
            column: 4
          },
          end: {
            line: 189,
            column: 5
          }
        },
        loc: {
          start: {
            line: 189,
            column: 56
          },
          end: {
            line: 204,
            column: 5
          }
        },
        line: 189
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 208,
            column: 4
          },
          end: {
            line: 208,
            column: 5
          }
        },
        loc: {
          start: {
            line: 208,
            column: 103
          },
          end: {
            line: 220,
            column: 5
          }
        },
        line: 208
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 224,
            column: 4
          },
          end: {
            line: 224,
            column: 5
          }
        },
        loc: {
          start: {
            line: 224,
            column: 78
          },
          end: {
            line: 238,
            column: 5
          }
        },
        line: 224
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 242,
            column: 4
          },
          end: {
            line: 242,
            column: 5
          }
        },
        loc: {
          start: {
            line: 242,
            column: 68
          },
          end: {
            line: 266,
            column: 5
          }
        },
        line: 242
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 270,
            column: 4
          },
          end: {
            line: 270,
            column: 5
          }
        },
        loc: {
          start: {
            line: 270,
            column: 49
          },
          end: {
            line: 295,
            column: 5
          }
        },
        line: 270
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 299,
            column: 4
          },
          end: {
            line: 299,
            column: 5
          }
        },
        loc: {
          start: {
            line: 299,
            column: 61
          },
          end: {
            line: 313,
            column: 5
          }
        },
        line: 299
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 317,
            column: 4
          },
          end: {
            line: 317,
            column: 5
          }
        },
        loc: {
          start: {
            line: 317,
            column: 40
          },
          end: {
            line: 321,
            column: 5
          }
        },
        line: 317
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 325,
            column: 4
          },
          end: {
            line: 325,
            column: 5
          }
        },
        loc: {
          start: {
            line: 325,
            column: 40
          },
          end: {
            line: 369,
            column: 5
          }
        },
        line: 325
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 373,
            column: 4
          },
          end: {
            line: 373,
            column: 5
          }
        },
        loc: {
          start: {
            line: 373,
            column: 70
          },
          end: {
            line: 402,
            column: 5
          }
        },
        line: 373
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 406,
            column: 4
          },
          end: {
            line: 406,
            column: 5
          }
        },
        loc: {
          start: {
            line: 406,
            column: 51
          },
          end: {
            line: 431,
            column: 5
          }
        },
        line: 406
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 411,
            column: 52
          },
          end: {
            line: 411,
            column: 53
          }
        },
        loc: {
          start: {
            line: 411,
            column: 60
          },
          end: {
            line: 411,
            column: 87
          }
        },
        line: 411
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 413,
            column: 44
          },
          end: {
            line: 413,
            column: 45
          }
        },
        loc: {
          start: {
            line: 413,
            column: 54
          },
          end: {
            line: 413,
            column: 55
          }
        },
        line: 413
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 414,
            column: 30
          },
          end: {
            line: 414,
            column: 31
          }
        },
        loc: {
          start: {
            line: 414,
            column: 40
          },
          end: {
            line: 414,
            column: 45
          }
        },
        line: 414
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 415,
            column: 45
          },
          end: {
            line: 415,
            column: 46
          }
        },
        loc: {
          start: {
            line: 415,
            column: 55
          },
          end: {
            line: 415,
            column: 60
          }
        },
        line: 415
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 416,
            column: 31
          },
          end: {
            line: 416,
            column: 32
          }
        },
        loc: {
          start: {
            line: 416,
            column: 47
          },
          end: {
            line: 416,
            column: 77
          }
        },
        line: 416
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 417,
            column: 31
          },
          end: {
            line: 417,
            column: 32
          }
        },
        loc: {
          start: {
            line: 417,
            column: 44
          },
          end: {
            line: 417,
            column: 57
          }
        },
        line: 417
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 435,
            column: 4
          },
          end: {
            line: 435,
            column: 5
          }
        },
        loc: {
          start: {
            line: 435,
            column: 50
          },
          end: {
            line: 452,
            column: 5
          }
        },
        line: 435
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 456,
            column: 4
          },
          end: {
            line: 456,
            column: 5
          }
        },
        loc: {
          start: {
            line: 456,
            column: 59
          },
          end: {
            line: 496,
            column: 5
          }
        },
        line: 456
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 500,
            column: 4
          },
          end: {
            line: 500,
            column: 5
          }
        },
        loc: {
          start: {
            line: 500,
            column: 54
          },
          end: {
            line: 536,
            column: 5
          }
        },
        line: 500
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 540,
            column: 4
          },
          end: {
            line: 540,
            column: 5
          }
        },
        loc: {
          start: {
            line: 540,
            column: 52
          },
          end: {
            line: 581,
            column: 5
          }
        },
        line: 540
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 585,
            column: 4
          },
          end: {
            line: 585,
            column: 5
          }
        },
        loc: {
          start: {
            line: 585,
            column: 85
          },
          end: {
            line: 615,
            column: 5
          }
        },
        line: 585
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 619,
            column: 4
          },
          end: {
            line: 619,
            column: 5
          }
        },
        loc: {
          start: {
            line: 619,
            column: 47
          },
          end: {
            line: 636,
            column: 5
          }
        },
        line: 619
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 640,
            column: 4
          },
          end: {
            line: 640,
            column: 5
          }
        },
        loc: {
          start: {
            line: 640,
            column: 82
          },
          end: {
            line: 674,
            column: 5
          }
        },
        line: 640
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 678,
            column: 4
          },
          end: {
            line: 678,
            column: 5
          }
        },
        loc: {
          start: {
            line: 678,
            column: 77
          },
          end: {
            line: 719,
            column: 5
          }
        },
        line: 678
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 723,
            column: 4
          },
          end: {
            line: 723,
            column: 5
          }
        },
        loc: {
          start: {
            line: 723,
            column: 113
          },
          end: {
            line: 752,
            column: 5
          }
        },
        line: 723
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 756,
            column: 4
          },
          end: {
            line: 756,
            column: 5
          }
        },
        loc: {
          start: {
            line: 756,
            column: 85
          },
          end: {
            line: 785,
            column: 5
          }
        },
        line: 756
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 37,
            column: 34
          },
          end: {
            line: 39,
            column: 51
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 38,
            column: 18
          },
          end: {
            line: 38,
            column: 85
          }
        }, {
          start: {
            line: 39,
            column: 18
          },
          end: {
            line: 39,
            column: 51
          }
        }],
        line: 37
      },
      "1": {
        loc: {
          start: {
            line: 37,
            column: 34
          },
          end: {
            line: 37,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 34
          },
          end: {
            line: 37,
            column: 48
          }
        }, {
          start: {
            line: 37,
            column: 52
          },
          end: {
            line: 37,
            column: 77
          }
        }],
        line: 37
      },
      "2": {
        loc: {
          start: {
            line: 67,
            column: 67
          },
          end: {
            line: 67,
            column: 123
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 67,
            column: 92
          },
          end: {
            line: 67,
            column: 105
          }
        }, {
          start: {
            line: 67,
            column: 108
          },
          end: {
            line: 67,
            column: 123
          }
        }],
        line: 67
      },
      "3": {
        loc: {
          start: {
            line: 94,
            column: 51
          },
          end: {
            line: 94,
            column: 134
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 94,
            column: 51
          },
          end: {
            line: 94,
            column: 86
          }
        }, {
          start: {
            line: 94,
            column: 90
          },
          end: {
            line: 94,
            column: 134
          }
        }],
        line: 94
      },
      "4": {
        loc: {
          start: {
            line: 95,
            column: 53
          },
          end: {
            line: 95,
            column: 138
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 95,
            column: 53
          },
          end: {
            line: 95,
            column: 90
          }
        }, {
          start: {
            line: 95,
            column: 94
          },
          end: {
            line: 95,
            column: 138
          }
        }],
        line: 95
      },
      "5": {
        loc: {
          start: {
            line: 125,
            column: 71
          },
          end: {
            line: 125,
            column: 86
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 125,
            column: 82
          },
          end: {
            line: 125,
            column: 86
          }
        }],
        line: 125
      },
      "6": {
        loc: {
          start: {
            line: 248,
            column: 8
          },
          end: {
            line: 257,
            column: 71
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 248,
            column: 8
          },
          end: {
            line: 257,
            column: 71
          }
        }, {
          start: {
            line: 250,
            column: 13
          },
          end: {
            line: 257,
            column: 71
          }
        }],
        line: 248
      },
      "7": {
        loc: {
          start: {
            line: 250,
            column: 13
          },
          end: {
            line: 257,
            column: 71
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 250,
            column: 13
          },
          end: {
            line: 257,
            column: 71
          }
        }, {
          start: {
            line: 252,
            column: 13
          },
          end: {
            line: 257,
            column: 71
          }
        }],
        line: 250
      },
      "8": {
        loc: {
          start: {
            line: 252,
            column: 13
          },
          end: {
            line: 257,
            column: 71
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 252,
            column: 13
          },
          end: {
            line: 257,
            column: 71
          }
        }, {
          start: {
            line: 254,
            column: 13
          },
          end: {
            line: 257,
            column: 71
          }
        }],
        line: 252
      },
      "9": {
        loc: {
          start: {
            line: 254,
            column: 13
          },
          end: {
            line: 257,
            column: 71
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 254,
            column: 13
          },
          end: {
            line: 257,
            column: 71
          }
        }, {
          start: {
            line: 257,
            column: 12
          },
          end: {
            line: 257,
            column: 71
          }
        }],
        line: 254
      },
      "10": {
        loc: {
          start: {
            line: 263,
            column: 27
          },
          end: {
            line: 263,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 263,
            column: 90
          },
          end: {
            line: 263,
            column: 110
          }
        }, {
          start: {
            line: 263,
            column: 113
          },
          end: {
            line: 263,
            column: 115
          }
        }],
        line: 263
      },
      "11": {
        loc: {
          start: {
            line: 271,
            column: 8
          },
          end: {
            line: 294,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 272,
            column: 12
          },
          end: {
            line: 278,
            column: 18
          }
        }, {
          start: {
            line: 279,
            column: 12
          },
          end: {
            line: 285,
            column: 18
          }
        }, {
          start: {
            line: 286,
            column: 12
          },
          end: {
            line: 291,
            column: 18
          }
        }, {
          start: {
            line: 292,
            column: 12
          },
          end: {
            line: 293,
            column: 70
          }
        }],
        line: 271
      },
      "12": {
        loc: {
          start: {
            line: 374,
            column: 8
          },
          end: {
            line: 376,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 374,
            column: 8
          },
          end: {
            line: 376,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 374
      },
      "13": {
        loc: {
          start: {
            line: 407,
            column: 8
          },
          end: {
            line: 409,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 407,
            column: 8
          },
          end: {
            line: 409,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 407
      },
      "14": {
        loc: {
          start: {
            line: 421,
            column: 8
          },
          end: {
            line: 429,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 421,
            column: 8
          },
          end: {
            line: 429,
            column: 9
          }
        }, {
          start: {
            line: 424,
            column: 13
          },
          end: {
            line: 429,
            column: 9
          }
        }],
        line: 421
      },
      "15": {
        loc: {
          start: {
            line: 424,
            column: 13
          },
          end: {
            line: 429,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 424,
            column: 13
          },
          end: {
            line: 429,
            column: 9
          }
        }, {
          start: {
            line: 427,
            column: 13
          },
          end: {
            line: 429,
            column: 9
          }
        }],
        line: 424
      },
      "16": {
        loc: {
          start: {
            line: 459,
            column: 8
          },
          end: {
            line: 494,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 459,
            column: 8
          },
          end: {
            line: 494,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 459
      },
      "17": {
        loc: {
          start: {
            line: 463,
            column: 12
          },
          end: {
            line: 493,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 463,
            column: 12
          },
          end: {
            line: 493,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 463
      },
      "18": {
        loc: {
          start: {
            line: 468,
            column: 30
          },
          end: {
            line: 468,
            column: 71
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 468,
            column: 52
          },
          end: {
            line: 468,
            column: 62
          }
        }, {
          start: {
            line: 468,
            column: 65
          },
          end: {
            line: 468,
            column: 71
          }
        }],
        line: 468
      },
      "19": {
        loc: {
          start: {
            line: 625,
            column: 8
          },
          end: {
            line: 626,
            column: 22
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 625,
            column: 8
          },
          end: {
            line: 626,
            column: 22
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 625
      },
      "20": {
        loc: {
          start: {
            line: 627,
            column: 8
          },
          end: {
            line: 628,
            column: 22
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 627,
            column: 8
          },
          end: {
            line: 628,
            column: 22
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 627
      },
      "21": {
        loc: {
          start: {
            line: 629,
            column: 8
          },
          end: {
            line: 630,
            column: 38
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 629,
            column: 8
          },
          end: {
            line: 630,
            column: 38
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 629
      },
      "22": {
        loc: {
          start: {
            line: 631,
            column: 8
          },
          end: {
            line: 632,
            column: 38
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 631,
            column: 8
          },
          end: {
            line: 632,
            column: 38
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 631
      },
      "23": {
        loc: {
          start: {
            line: 633,
            column: 8
          },
          end: {
            line: 634,
            column: 22
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 633,
            column: 8
          },
          end: {
            line: 634,
            column: 22
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 633
      },
      "24": {
        loc: {
          start: {
            line: 681,
            column: 8
          },
          end: {
            line: 698,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 681,
            column: 8
          },
          end: {
            line: 698,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 681
      },
      "25": {
        loc: {
          start: {
            line: 700,
            column: 8
          },
          end: {
            line: 717,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 700,
            column: 8
          },
          end: {
            line: 717,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 700
      },
      "26": {
        loc: {
          start: {
            line: 726,
            column: 8
          },
          end: {
            line: 750,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 726,
            column: 8
          },
          end: {
            line: 750,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 726
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0, 0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\SystemPerformanceAnalysisEngine.ts",
      mappings: ";AAAA;;;;;;;;;GASG;;;AAEH,qEA4BqC;AAErC,yEAAsE;AAKtE;;;;;;;;;GASG;AACH,MAAa,+BAA+B;IAK1C;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,wBAAwB,CAC1C,mBAAwC,EACxC,aAA4B,EAC5B,cAAqC;QAErC,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC;YACnE,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;YAE7B,wCAAwC;YACxC,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAC/D,mBAAmB,EACnB,aAAa,CACd,CAAC;YAEF,yDAAyD;YACzD,MAAM,aAAa,GAAG,cAAc,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC;gBAC/D,CAAC,CAAC,MAAM,IAAI,CAAC,oBAAoB,CAAC,cAAc,EAAE,kBAAkB,CAAC;gBACrE,CAAC,CAAC,IAAI,CAAC,0BAA0B,EAAE,CAAC;YAEtC,oCAAoC;YACpC,MAAM,mBAAmB,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAC/D,mBAAmB,EACnB,kBAAkB,CACnB,CAAC;YAEF,4BAA4B;YAC5B,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAC3D,mBAAmB,EACnB,kBAAkB,CACnB,CAAC;YAEF,0CAA0C;YAC1C,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAC1D,kBAAkB,EAClB,cAAc,CACf,CAAC;YAEF,uCAAuC;YACvC,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kCAAkC,CACnE,kBAAkB,EAClB,kBAAkB,EAClB,mBAAmB,CACpB,CAAC;YAEF,+BAA+B;YAC/B,MAAM,mBAAmB,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAC/D,kBAAkB,EAClB,mBAAmB,CACpB,CAAC;YAEF,MAAM,QAAQ,GAAwB;gBACpC,EAAE,EAAE,UAAU;gBACd,QAAQ,EAAE,mBAAmB,CAAC,EAAE;gBAChC,iBAAiB,EAAE,SAAS;gBAC5B,kBAAkB;gBAClB,aAAa;gBACb,mBAAmB;gBACnB,kBAAkB;gBAClB,iBAAiB;gBACjB,eAAe;gBACf,mBAAmB;aACpB,CAAC;YAEF,0CAA0C;YAC1C,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YAE9C,OAAO,QAAQ,CAAC;QAElB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,uCAAuC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;QACrH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,KAAK,CAAC,2BAA2B,CAC9C,mBAAwC,EACxC,aAA4B;QAE5B,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAE7B,uDAAuD;QACvD,MAAM,oBAAoB,GAAG,mDAAwB,CAAC,+BAA+B,CAAC;YACpF,QAAQ,EAAE,IAAI,CAAC,4BAA4B,CAAC,mBAAmB,CAAC;YAChE,UAAU,EAAE,mBAAmB,CAAC,UAAU;YAC1C,gBAAgB,EAAE,mBAAmB,CAAC,mBAAmB;SAC1D,CAAC,CAAC;QAEH,oCAAoC;QACpC,MAAM,cAAc,GAAG,IAAI,CAAC,uBAAuB,CACjD,mBAAmB,EACnB,oBAAoB,CAAC,iBAAiB,CACvC,CAAC;QAEF,4BAA4B;QAC5B,MAAM,cAAc,GAAG,IAAI,CAAC,uBAAuB,CAAC,mBAAmB,CAAC,CAAC;QAEzE,sCAAsC;QACtC,MAAM,iBAAiB,GAAG,IAAI,CAAC,gCAAgC,CAC7D,mBAAmB,EACnB,cAAc,EACd,oBAAoB,CACrB,CAAC;QAEF,kCAAkC;QAClC,MAAM,oBAAoB,GAAG,IAAI,CAAC,6BAA6B,CAC7D,mBAAmB,EACnB,cAAc,CACf,CAAC;QAEF,mCAAmC;QACnC,MAAM,cAAc,GAAG,IAAI,CAAC,mBAAmB,CAC7C,mBAAmB,EACnB,cAAc,CACf,CAAC;QAEF,MAAM,kBAAkB,GAAuB;YAC7C,4BAA4B;YAC5B,mBAAmB,EAAE,IAAI,CAAC,iBAAiB,CACzC,oBAAoB,CAAC,iBAAiB,EACtC,OAAO,EACP,uCAAiB,CAAC,UAAU,EAC5B,sCAAgB,CAAC,IAAI,EACrB,SAAS,CACV;YACD,cAAc,EAAE,IAAI,CAAC,iBAAiB,CACpC,oBAAoB,CAAC,cAAc,IAAI,oBAAoB,CAAC,iBAAiB,GAAG,GAAG,EACnF,OAAO,EACP,uCAAiB,CAAC,UAAU,EAC5B,sCAAgB,CAAC,IAAI,EACrB,SAAS,CACV;YACD,gBAAgB,EAAE,IAAI,CAAC,iBAAiB,CACtC,oBAAoB,CAAC,gBAAgB,IAAI,oBAAoB,CAAC,iBAAiB,GAAG,GAAG,EACrF,OAAO,EACP,uCAAiB,CAAC,UAAU,EAC5B,sCAAgB,CAAC,IAAI,EACrB,SAAS,CACV;YACD,YAAY,EAAE,IAAI,CAAC,iBAAiB,CAClC,cAAc,CAAC,YAAY,EAC3B,KAAK,EACL,uCAAiB,CAAC,UAAU,EAC5B,sCAAgB,CAAC,IAAI,EACrB,SAAS,CACV;YACD,aAAa,EAAE,IAAI,CAAC,iBAAiB,CACnC,mBAAmB,CAAC,gBAAgB,CAAC,aAAa,EAClD,KAAK,EACL,uCAAiB,CAAC,iBAAiB,EACnC,sCAAgB,CAAC,IAAI,EACrB,SAAS,CACV;YACD,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,CACvC,cAAc,CAAC,UAAU,EACzB,GAAG,EACH,uCAAiB,CAAC,UAAU,EAC5B,sCAAgB,CAAC,MAAM,EACvB,SAAS,CACV;YAED,kBAAkB;YAClB,QAAQ,EAAE,IAAI,CAAC,iBAAiB,CAC9B,cAAc,CAAC,KAAK,EACpB,IAAI,EACJ,uCAAiB,CAAC,UAAU,EAC5B,sCAAgB,CAAC,IAAI,EACrB,SAAS,CACV;YACD,aAAa,EAAE,IAAI,CAAC,iBAAiB,CACnC,cAAc,CAAC,UAAU,EACzB,GAAG,EACH,uCAAiB,CAAC,UAAU,EAC5B,sCAAgB,CAAC,IAAI,EACrB,SAAS,CACV;YACD,QAAQ,EAAE,IAAI,CAAC,iBAAiB,CAC9B,cAAc,CAAC,KAAK,EACpB,KAAK,EACL,uCAAiB,CAAC,UAAU,EAC5B,sCAAgB,CAAC,MAAM,EACvB,SAAS,CACV;YACD,gBAAgB,EAAE,cAAc,CAAC,aAAa;YAE9C,oBAAoB;YACpB,gBAAgB,EAAE,IAAI,CAAC,iBAAiB,CACtC,iBAAiB,CAAC,gBAAgB,EAClC,GAAG,EACH,uCAAiB,CAAC,UAAU,EAC5B,sCAAgB,CAAC,IAAI,EACrB,SAAS,CACV;YACD,mBAAmB,EAAE,IAAI,CAAC,iBAAiB,CACzC,iBAAiB,CAAC,mBAAmB,EACrC,GAAG,EACH,uCAAiB,CAAC,UAAU,EAC5B,sCAAgB,CAAC,IAAI,EACrB,SAAS,CACV;YACD,sBAAsB,EAAE,IAAI,CAAC,iBAAiB,CAC5C,iBAAiB,CAAC,sBAAsB,EACxC,GAAG,EACH,uCAAiB,CAAC,UAAU,EAC5B,sCAAgB,CAAC,MAAM,EACvB,SAAS,CACV;YAED,wBAAwB;YACxB,UAAU,EAAE,IAAI,CAAC,iBAAiB,CAChC,oBAAoB,CAAC,UAAU,EAC/B,KAAK,EACL,uCAAiB,CAAC,SAAS,EAC3B,sCAAgB,CAAC,MAAM,EACvB,SAAS,CACV;YACD,cAAc,EAAE,IAAI,CAAC,iBAAiB,CACpC,oBAAoB,CAAC,cAAc,EACnC,MAAM,EACN,uCAAiB,CAAC,SAAS,EAC3B,sCAAgB,CAAC,GAAG,EACpB,SAAS,CACV;YACD,eAAe,EAAE,IAAI,CAAC,iBAAiB,CACrC,oBAAoB,CAAC,eAAe,EACpC,IAAI,EACJ,uCAAiB,CAAC,UAAU,EAC5B,sCAAgB,CAAC,MAAM,EACvB,SAAS,CACV;YAED,mCAAmC;YACnC,kBAAkB,EAAE,IAAI,CAAC,iBAAiB,CACxC,oBAAoB,CAAC,kBAAkB,EACvC,OAAO,EACP,uCAAiB,CAAC,SAAS,EAC3B,sCAAgB,CAAC,MAAM,EACvB,SAAS,CACV;YACD,gBAAgB,EAAE,IAAI,CAAC,iBAAiB,CACtC,oBAAoB,CAAC,gBAAgB,EACrC,OAAO,EACP,uCAAiB,CAAC,SAAS,EAC3B,sCAAgB,CAAC,MAAM,EACvB,SAAS,CACV;YACD,eAAe,EAAE,EAAE,EAAE,6CAA6C;YAElE,iBAAiB;YACjB,cAAc;YACd,gBAAgB,EAAE,cAAc,CAAC,gBAAgB;SAClD,CAAC;QAEF,OAAO,kBAAkB,CAAC;IAC5B,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,iBAAiB,CAC9B,KAAa,EACb,KAAa,EACb,MAAyB,EACzB,OAAyB,EACzB,SAAe,EACf,WAAmB,IAAI;QAEvB,OAAO;YACL,KAAK;YACL,KAAK;YACL,QAAQ;YACR,SAAS;YACT,MAAM;YACN,gBAAgB,EAAE,OAAO;YACzB,iBAAiB,EAAE;gBACjB,UAAU,EAAE,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC;gBACxC,UAAU,EAAE,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC;gBACxC,eAAe,EAAE,QAAQ,GAAG,GAAG;gBAC/B,gBAAgB,EAAE,QAAiB;aACpC;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,uBAAuB,CACpC,mBAAwC,EACxC,cAAsB;QAEtB,MAAM,aAAa,GAAG,mBAAmB,CAAC,gBAAgB,CAAC,aAAa,CAAC;QACzE,MAAM,cAAc,GAAG,mBAAmB,CAAC,gBAAgB,CAAC,cAAc,CAAC;QAE3E,yCAAyC;QACzC,sEAAsE;QACtE,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC,cAAc,GAAG,cAAc,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QAChG,MAAM,KAAK,GAAG,CAAC,aAAa,GAAG,cAAc,CAAC,GAAG,CAAC,IAAI,GAAG,UAAU,CAAC,CAAC,CAAC,KAAK;QAC3E,MAAM,OAAO,GAAG,KAAK,GAAG,KAAK,CAAC,CAAC,gBAAgB;QAC/C,MAAM,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC,CAAC,CAAC,+BAA+B;QAEhG,MAAM,aAAa,GAAqB;YACtC,cAAc,EAAE;gBACd,OAAO,EAAE,aAAa;gBACtB,QAAQ,EAAE,cAAc;gBACxB,KAAK,EAAE,OAAO;gBACd,UAAU,EAAE,UAAU,GAAG,GAAG;gBAC5B,KAAK;aACN;YACD,WAAW,EAAE;gBACX,OAAO,EAAE,aAAa;gBACtB,QAAQ,EAAE,cAAc;gBACxB,KAAK,EAAE,CAAC,aAAa,GAAG,cAAc,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,GAAG,KAAK;gBAC9D,UAAU,EAAE,EAAE;gBACd,KAAK,EAAE,IAAI;aACZ;YACD,qBAAqB,EAAE,UAAU,GAAG,GAAG;YACvC,kBAAkB,EAAE,EAAE;YACtB,WAAW,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,aAAa,GAAG,GAAG,GAAG,aAAa,CAAC,GAAG,aAAa,GAAG,GAAG,CAAC;YACrF,WAAW,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,aAAa,GAAG,GAAG,GAAG,aAAa,CAAC,GAAG,aAAa,GAAG,GAAG,CAAC;YACrF,yBAAyB,EAAE;gBACzB,UAAU,EAAE,aAAa,GAAG,GAAG;gBAC/B,UAAU,EAAE,aAAa,GAAG,GAAG;gBAC/B,WAAW,EAAE,cAAc,GAAG,GAAG;gBACjC,WAAW,EAAE,cAAc,GAAG,GAAG;aAClC;SACF,CAAC;QAEF,OAAO;YACL,KAAK,EAAE,OAAO;YACd,UAAU,EAAE,UAAU,GAAG,GAAG;YAC5B,KAAK;YACL,aAAa;SACd,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,uBAAuB,CAAC,mBAAwC;QAC7E,MAAM,aAAa,GAAG,mBAAmB,CAAC,gBAAgB,CAAC,aAAa,CAAC;QAEzE,iCAAiC;QACjC,8DAA8D;QAC9D,MAAM,YAAY,GAAG,aAAa,GAAG,IAAI,CAAC,CAAC,oBAAoB;QAC/D,MAAM,UAAU,GAAG,CAAC,YAAY,GAAG,aAAa,CAAC,GAAG,GAAG,CAAC;QAExD,OAAO;YACL,YAAY;YACZ,UAAU;YACV,gBAAgB,EAAE;gBAChB,eAAe,EAAE,IAAI,EAAE,aAAa;gBACpC,oBAAoB,EAAE,IAAI;gBAC1B,KAAK,EAAE,EAAE;aACV;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,gCAAgC,CAC7C,mBAAwC,EACxC,cAAmB,EACnB,oBAAyB;QAEzB,MAAM,aAAa,GAAG,mBAAmB,CAAC,gBAAgB,CAAC,aAAa,CAAC;QACzE,MAAM,cAAc,GAAG,mBAAmB,CAAC,gBAAgB,CAAC,cAAc,CAAC;QAE3E,uCAAuC;QACvC,MAAM,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,cAAc,CAAC,UAAU,GAAG,GAAG,CAAC,CAAC,CAAC,4BAA4B;QACpG,MAAM,mBAAmB,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG,CAAC,oBAAoB,CAAC,iBAAiB,GAAG,cAAc,CAAC,GAAG,EAAE,CAAC,CAAC;QAC/G,MAAM,sBAAsB,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,gBAAgB,GAAG,GAAG,CAAC,CAAC,CAAC,kCAAkC;QAEvG,OAAO;YACL,gBAAgB;YAChB,mBAAmB;YACnB,sBAAsB;SACvB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,6BAA6B,CAC1C,mBAAwC,EACxC,cAAmB;QAEnB,MAAM,aAAa,GAAG,mBAAmB,CAAC,gBAAgB,CAAC,aAAa,CAAC;QAEzE,wCAAwC;QACxC,MAAM,QAAQ,GAAG,aAAa,GAAG,GAAG,CAAC,CAAC,kCAAkC;QACxE,MAAM,UAAU,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,yBAAyB;QACnF,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,6BAA6B;QACpF,MAAM,eAAe,GAAG,cAAc,CAAC,KAAK,GAAG,IAAI,GAAG,CAAC,aAAa,GAAG,IAAI,CAAC,CAAC,CAAC,gBAAgB;QAE9F,OAAO;YACL,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;YAClD,cAAc,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;YAC5D,eAAe,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,eAAe,CAAC,CAAC;YAC9D,kBAAkB,EAAE,GAAG,EAAE,uBAAuB;YAChD,gBAAgB,EAAE,GAAG,CAAC,6BAA6B;SACpD,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,mBAAmB,CAChC,mBAAwC,EACxC,cAAmB;QAEnB,gCAAgC;QAChC,MAAM,aAAa,GAAG,IAAI,CAAC,CAAC,gBAAgB;QAC5C,MAAM,iBAAiB,GAAG,IAAI,CAAC,CAAC,gBAAgB;QAChD,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,aAAa,GAAG,iBAAiB,CAAC,GAAG,GAAG,CAAC,CAAC;QAElF,IAAI,YAA0B,CAAC;QAC/B,IAAI,YAAY,IAAI,EAAE;YAAE,YAAY,GAAG,kCAAY,CAAC,SAAS,CAAC;aACzD,IAAI,YAAY,IAAI,EAAE;YAAE,YAAY,GAAG,kCAAY,CAAC,IAAI,CAAC;aACzD,IAAI,YAAY,IAAI,EAAE;YAAE,YAAY,GAAG,kCAAY,CAAC,UAAU,CAAC;aAC/D,IAAI,YAAY,IAAI,EAAE;YAAE,YAAY,GAAG,kCAAY,CAAC,IAAI,CAAC;;YACzD,YAAY,GAAG,kCAAY,CAAC,QAAQ,CAAC;QAE1C,OAAO;YACL,YAAY;YACZ,aAAa;YACb,iBAAiB;YACjB,YAAY;YACZ,aAAa,EAAE,YAAY,KAAK,kCAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE;YACjF,sBAAsB,EAAE,IAAI,CAAC,8BAA8B,CAAC,YAAY,CAAC;SAC1E,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,8BAA8B,CAAC,KAAmB;QAC/D,QAAQ,KAAK,EAAE,CAAC;YACd,KAAK,kCAAY,CAAC,QAAQ;gBACxB,OAAO;oBACL,uCAAuC;oBACvC,oCAAoC;oBACpC,mCAAmC;oBACnC,qCAAqC;iBACtC,CAAC;YACJ,KAAK,kCAAY,CAAC,IAAI;gBACpB,OAAO;oBACL,gCAAgC;oBAChC,yBAAyB;oBACzB,wBAAwB;oBACxB,4BAA4B;iBAC7B,CAAC;YACJ,KAAK,kCAAY,CAAC,UAAU;gBAC1B,OAAO;oBACL,2CAA2C;oBAC3C,mCAAmC;oBACnC,+BAA+B;iBAChC,CAAC;YACJ;gBACE,OAAO,CAAC,4CAA4C,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,4BAA4B,CAAC,mBAAwC;QAClF,mCAAmC;QACnC,gEAAgE;QAChE,OAAO;YACL;gBACE,EAAE,EAAE,aAAa;gBACjB,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE,EAAE;gBACZ,QAAQ,EAAE,kBAAkB;gBAC5B,SAAS,EAAE,MAAM;gBACjB,OAAO,EAAE,mBAAmB,CAAC,gBAAgB,CAAC,aAAa;gBAC3D,QAAQ,EAAE,EAAE;aACb;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,kBAAkB,CAAC,QAAgB;QAChD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC1D,OAAO,YAAY,QAAQ,IAAI,SAAS,IAAI,MAAM,EAAE,CAAC;IACvD,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,0BAA0B;QACvC,OAAO;YACL,SAAS,EAAE;gBACT,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,OAAO,EAAE,IAAI,IAAI,EAAE;gBACnB,QAAQ,EAAE,CAAC;gBACX,KAAK,EAAE,MAAe;aACvB;YACD,cAAc,EAAE,oCAAc,CAAC,MAAM;YACrC,cAAc,EAAE,CAAC;YACjB,gBAAgB,EAAE,EAAE;YACpB,SAAS,EAAE,EAAE;YACb,kBAAkB,EAAE;gBAClB,eAAe,EAAE,EAAE;gBACnB,oBAAoB,EAAE,EAAE;gBACxB,kBAAkB,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,eAAe,EAAE,CAAC,EAAE;gBACxE,eAAe,EAAE;oBACf,SAAS,EAAE,mBAA4B;oBACvC,QAAQ,EAAE,CAAC;oBACX,gBAAgB,EAAE,IAAI,IAAI,EAAE;oBAC5B,UAAU,EAAE,CAAC;oBACb,eAAe,EAAE,CAAC;iBACnB;gBACD,UAAU,EAAE,EAAE;gBACd,SAAS,EAAE,EAAE;aACd;YACD,eAAe,EAAE;gBACf,sBAAsB,EAAE,GAAG,EAAE,sBAAsB;gBACnD,oBAAoB,EAAE,EAAE;gBACxB,kBAAkB,EAAE,EAAE;gBACtB,iBAAiB,EAAE;oBACjB,qBAAqB,EAAE,EAAE,iBAAiB,EAAE,CAAC,EAAE,cAAc,EAAE,CAAC,EAAE,UAAU,EAAE,IAAI,EAAE;oBACpF,qBAAqB,EAAE,EAAE,iBAAiB,EAAE,CAAC,EAAE,EAAE,cAAc,EAAE,CAAC,CAAC,EAAE,UAAU,EAAE,IAAI,EAAE;oBACvF,mBAAmB,EAAE,EAAE,iBAAiB,EAAE,CAAC,EAAE,EAAE,cAAc,EAAE,CAAC,CAAC,EAAE,UAAU,EAAE,KAAK,EAAE;iBACvF;gBACD,iBAAiB,EAAE;oBACjB,UAAU,EAAE,CAAC;oBACb,UAAU,EAAE,EAAE;oBACd,aAAa,EAAE,EAAE;oBACjB,eAAe,EAAE,EAAE;oBACnB,cAAc,EAAE,CAAC,qBAAqB,EAAE,6BAA6B,CAAC;iBACvE;aACF;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,KAAK,CAAC,oBAAoB,CACvC,cAAoC,EACpC,cAAkC;QAElC,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9B,OAAO,IAAI,CAAC,0BAA0B,EAAE,CAAC;QAC3C,CAAC;QAED,0CAA0C;QAC1C,MAAM,aAAa,GAAG,IAAI,CAAC,uBAAuB,CAAC,cAAc,CAAC,CAAC;QAEnE,2BAA2B;QAC3B,MAAM,gBAAgB,GAAG,IAAI,CAAC,sBAAsB,CAAC,cAAc,CAAC,CAAC;QAErE,mBAAmB;QACnB,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;QAEvE,+BAA+B;QAC/B,MAAM,kBAAkB,GAAG,IAAI,CAAC,0BAA0B,CAAC,cAAc,CAAC,CAAC;QAE3E,6BAA6B;QAC7B,MAAM,eAAe,GAAG,IAAI,CAAC,wBAAwB,CAAC,cAAc,CAAC,CAAC;QAEtE,MAAM,SAAS,GAAG;YAChB,SAAS,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC,mBAAmB,CAAC,SAAS;YAC1D,OAAO,EAAE,cAAc,CAAC,mBAAmB,CAAC,SAAS;YACrD,QAAQ,EAAE,cAAc,CAAC,MAAM;YAC/B,KAAK,EAAE,MAAe;SACvB,CAAC;QAEF,OAAO;YACL,SAAS;YACT,cAAc,EAAE,aAAa,CAAC,SAAS;YACvC,cAAc,EAAE,aAAa,CAAC,SAAS;YACvC,gBAAgB;YAChB,SAAS;YACT,kBAAkB;YAClB,eAAe;SAChB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,uBAAuB,CAAC,cAAoC;QAIzE,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9B,OAAO,EAAE,SAAS,EAAE,oCAAc,CAAC,MAAM,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC;QAC5D,CAAC;QAED,gDAAgD;QAChD,MAAM,gBAAgB,GAAG,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QACjF,MAAM,CAAC,GAAG,gBAAgB,CAAC,MAAM,CAAC;QAClC,MAAM,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QAEjD,MAAM,IAAI,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAC1C,MAAM,IAAI,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QACzD,MAAM,KAAK,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,EAAE,GAAG,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC1E,MAAM,KAAK,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC;QAEtD,MAAM,KAAK,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;QACpE,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAElC,IAAI,SAAyB,CAAC;QAC9B,IAAI,SAAS,GAAG,GAAG,EAAE,CAAC;YACpB,SAAS,GAAG,oCAAc,CAAC,MAAM,CAAC;QACpC,CAAC;aAAM,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;YACrB,SAAS,GAAG,oCAAc,CAAC,SAAS,CAAC;QACvC,CAAC;aAAM,CAAC;YACN,SAAS,GAAG,oCAAc,CAAC,SAAS,CAAC;QACvC,CAAC;QAED,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC;IAClC,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,sBAAsB,CAAC,cAAoC;QACxE,wCAAwC;QACxC,sEAAsE;QACtE,OAAO;YACL;gBACE,MAAM,EAAE,QAAiB;gBACzB,kBAAkB,EAAE,EAAE;gBACtB,oBAAoB,EAAE,CAAC;gBACvB,aAAa,EAAE,CAAC,sBAAsB,EAAE,qBAAqB,CAAC;aAC/D;YACD;gBACE,MAAM,EAAE,QAAiB;gBACzB,kBAAkB,EAAE,EAAE;gBACtB,oBAAoB,EAAE,CAAC;gBACvB,aAAa,EAAE,CAAC,gBAAgB,EAAE,4BAA4B,CAAC;aAChE;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,eAAe,CAC5B,cAAoC,EACpC,cAAkC;QAElC,MAAM,SAAS,GAAyB,EAAE,CAAC;QAE3C,mCAAmC;QACnC,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9B,MAAM,cAAc,GAAG,cAAc,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,gBAAgB,CAAC,KAAK,CAAC;YACxF,MAAM,iBAAiB,GAAG,cAAc,CAAC,gBAAgB,CAAC,KAAK,CAAC;YAChE,MAAM,cAAc,GAAG,cAAc,GAAG,iBAAiB,CAAC;YAE1D,IAAI,cAAc,GAAG,CAAC,EAAE,CAAC,CAAC,oBAAoB;gBAC5C,SAAS,CAAC,IAAI,CAAC;oBACb,EAAE,EAAE,WAAW,IAAI,CAAC,GAAG,EAAE,EAAE;oBAC3B,kBAAkB,EAAE,IAAI,IAAI,EAAE;oBAC9B,WAAW,EAAE,iCAAW,CAAC,aAAa;oBACtC,QAAQ,EAAE,cAAc,GAAG,EAAE,CAAC,CAAC,CAAC,UAAmB,CAAC,CAAC,CAAC,MAAe;oBACrE,eAAe,EAAE,CAAC,kBAAkB,CAAC;oBACrC,kBAAkB,EAAE,cAAc;oBAClC,QAAQ,EAAE,CAAC;oBACX,cAAc,EAAE;wBACd;4BACE,KAAK,EAAE,iBAAiB;4BACxB,WAAW,EAAE,EAAE;4BACf,QAAQ,EAAE,mBAA4B;4BACtC,eAAe,EAAE,CAAC,4BAA4B,EAAE,0BAA0B,CAAC;yBAC5E;wBACD;4BACE,KAAK,EAAE,mBAAmB;4BAC1B,WAAW,EAAE,EAAE;4BACf,QAAQ,EAAE,mBAA4B;4BACtC,eAAe,EAAE,CAAC,wBAAwB,EAAE,yBAAyB,CAAC;yBACvE;qBACF;oBACD,kBAAkB,EAAE;wBAClB,0BAA0B;wBAC1B,qBAAqB;wBACrB,yBAAyB;qBAC1B;oBACD,QAAQ,EAAE,KAAK;iBAChB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,0BAA0B,CAAC,cAAoC;QAC5E,iCAAiC;QACjC,sDAAsD;QACtD,OAAO;YACL,eAAe,EAAE,EAAE;YACnB,oBAAoB,EAAE;gBACpB;oBACE,MAAM,EAAE,kBAAkB;oBAC1B,YAAY,EAAE,EAAE;oBAChB,cAAc,EAAE,EAAE;oBAClB,aAAa,EAAE,CAAC,GAAG;oBACnB,YAAY,EAAE,CAAC;iBAChB;aACF;YACD,kBAAkB,EAAE;gBAClB,UAAU,EAAE,EAAE;gBACd,UAAU,EAAE,EAAE;gBACd,eAAe,EAAE,EAAE;aACpB;YACD,eAAe,EAAE;gBACf,SAAS,EAAE,aAAsB;gBACjC,QAAQ,EAAE,EAAE;gBACZ,gBAAgB,EAAE,IAAI,IAAI,EAAE;gBAC5B,UAAU,EAAE,cAAc,CAAC,MAAM;gBACjC,eAAe,EAAE,IAAI;aACtB;YACD,UAAU,EAAE;gBACV;oBACE,MAAM,EAAE,kBAAkB;oBAC1B,MAAM,EAAE,EAAE;oBACV,YAAY,EAAE,IAAI;oBAClB,oBAAoB,EAAE,CAAC,4BAA4B,EAAE,qBAAqB,CAAC;iBAC5E;aACF;YACD,SAAS,EAAE,EAAE;SACd,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,wBAAwB,CAAC,cAAoC;QAC1E,qCAAqC;QACrC,OAAO;YACL,sBAAsB,EAAE,GAAG,EAAE,gBAAgB;YAC7C,oBAAoB,EAAE;gBACpB;oBACE,WAAW,EAAE,UAAU;oBACvB,aAAa,EAAE,KAAc;oBAC7B,eAAe,EAAE,GAAG;oBACpB,gBAAgB,EAAE,EAAE;oBACpB,sBAAsB,EAAE,EAAE;oBAC1B,oBAAoB,EAAE,EAAE;iBACzB;aACF;YACD,kBAAkB,EAAE;gBAClB;oBACE,MAAM,EAAE,iBAAiB;oBACzB,MAAM,EAAE,EAAE;oBACV,YAAY,EAAE,KAAK;oBACnB,oBAAoB,EAAE,CAAC,6BAA6B,CAAC;iBACtD;gBACD;oBACE,MAAM,EAAE,qBAAqB;oBAC7B,MAAM,EAAE,EAAE;oBACV,YAAY,EAAE,IAAI;oBAClB,oBAAoB,EAAE,CAAC,gCAAgC,EAAE,gBAAgB,CAAC;iBAC3E;aACF;YACD,iBAAiB,EAAE;gBACjB,qBAAqB,EAAE,EAAE,iBAAiB,EAAE,CAAC,EAAE,cAAc,EAAE,CAAC,EAAE,UAAU,EAAE,IAAI,EAAE;gBACpF,qBAAqB,EAAE,EAAE,iBAAiB,EAAE,CAAC,EAAE,EAAE,cAAc,EAAE,CAAC,CAAC,EAAE,UAAU,EAAE,IAAI,EAAE;gBACvF,mBAAmB,EAAE,EAAE,iBAAiB,EAAE,CAAC,EAAE,EAAE,cAAc,EAAE,CAAC,CAAC,EAAE,UAAU,EAAE,KAAK,EAAE;aACvF;YACD,iBAAiB,EAAE;gBACjB,UAAU,EAAE,CAAC;gBACb,UAAU,EAAE,EAAE;gBACd,aAAa,EAAE,EAAE;gBACjB,eAAe,EAAE,EAAE;gBACnB,cAAc,EAAE,CAAC,qBAAqB,EAAE,6BAA6B,CAAC;aACvE;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,KAAK,CAAC,0BAA0B,CAC7C,mBAAwC,EACxC,kBAAsC;QAEtC,kCAAkC;QAClC,8DAA8D;QAC9D,MAAM,gBAAgB,GAAG,kBAAkB,CAAC,gBAAgB,CAAC,KAAK,CAAC;QACnE,MAAM,eAAe,GAAG,EAAE,CAAC,CAAC,2BAA2B;QACvD,MAAM,WAAW,GAAG,EAAE,CAAC,CAAC,4BAA4B;QAEpD,OAAO;YACL,aAAa,EAAE,mCAAa,CAAC,gBAAgB;YAC7C,eAAe,EAAE,6BAA6B;YAC9C,iBAAiB,EAAE,gBAAgB;YACnC,cAAc,EAAE,eAAe;YAC/B,UAAU,EAAE,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,eAAe,CAAC;YACvE,cAAc,EAAE,eAAe,GAAG,gBAAgB;YAClD,oBAAoB,EAAE,WAAW,GAAG,gBAAgB;YACpD,cAAc,EAAE;gBACd;oBACE,QAAQ,EAAE,kBAAkB;oBAC5B,UAAU,EAAE,sBAAsB;oBAClC,iBAAiB,EAAE,EAAE;oBACrB,qBAAqB,EAAE;wBACrB,IAAI,EAAE,QAAiB;wBACvB,GAAG,EAAE,CAAC;wBACN,YAAY,EAAE,QAAiB;wBAC/B,WAAW,EAAE,IAAI;wBACjB,cAAc,EAAE,IAAI;qBACrB;oBACD,qBAAqB,EAAE,EAAE,GAAG,gBAAgB;iBAC7C;aACF;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,mBAAmB,CAAC,KAAa,EAAE,OAAe;QAC/D,oCAAoC;QACpC,2DAA2D;QAC3D,MAAM,iBAAiB,GAAG,CAAC,CAAC;QAC5B,MAAM,MAAM,GAAG,CAAC,KAAK,GAAG,OAAO,CAAC,GAAG,iBAAiB,CAAC;QAErD,6CAA6C;QAC7C,IAAI,MAAM,IAAI,CAAC;YAAE,OAAO,EAAE,CAAC;QAC3B,IAAI,MAAM,IAAI,CAAC;YAAE,OAAO,EAAE,CAAC;QAC3B,IAAI,MAAM,IAAI,CAAC;YAAE,OAAO,EAAE,GAAG,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC;QAC3C,IAAI,MAAM,IAAI,CAAC,CAAC;YAAE,OAAO,EAAE,GAAG,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC;QAC5C,IAAI,MAAM,IAAI,CAAC,CAAC;YAAE,OAAO,EAAE,CAAC;QAC5B,OAAO,CAAC,CAAC;IACX,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,KAAK,CAAC,uBAAuB,CAC1C,mBAAwC,EACxC,kBAAsC;QAEtC,iCAAiC;QACjC,OAAO;YACL,iBAAiB,EAAE;gBACjB,KAAK,EAAE,kBAAkB,CAAC,gBAAgB,CAAC,KAAK;gBAChD,KAAK,EAAE,GAAG;gBACV,iBAAiB,EAAE,YAAqB;gBACxC,QAAQ,EAAE,GAAG;gBACb,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB;YACD,qBAAqB,EAAE;gBACrB;oBACE,WAAW,EAAE,UAAU;oBACvB,aAAa,EAAE,KAAc;oBAC7B,UAAU,EAAE,kBAAkB,CAAC,aAAa,CAAC,KAAK;oBAClD,eAAe,EAAE,EAAE;oBACnB,iBAAiB,EAAE,IAAI;oBACvB,iBAAiB,EAAE,MAAe;iBACnC;aACF;YACD,gBAAgB,EAAE,EAAE;YACpB,gBAAgB,EAAE,EAAE;YACpB,wBAAwB,EAAE,EAAE;YAC5B,mBAAmB,EAAE;gBACnB,aAAa,EAAE,mCAAa,CAAC,gBAAgB;gBAC7C,eAAe,EAAE,oBAAoB;gBACrC,iBAAiB,EAAE,kBAAkB,CAAC,gBAAgB,CAAC,KAAK;gBAC5D,cAAc,EAAE,EAAE;gBAClB,UAAU,EAAE,EAAE;gBACd,cAAc,EAAE,CAAC;gBACjB,oBAAoB,EAAE,EAAE;gBACxB,cAAc,EAAE,EAAE;aACnB;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,KAAK,CAAC,uBAAuB,CAC1C,kBAAsC,EACtC,cAAqC;QAErC,MAAM,MAAM,GAAuB,EAAE,CAAC;QAEtC,6BAA6B;QAC7B,IAAI,kBAAkB,CAAC,gBAAgB,CAAC,KAAK,GAAG,EAAE,EAAE,CAAC;YACnD,MAAM,CAAC,IAAI,CAAC;gBACV,EAAE,EAAE,SAAS,IAAI,CAAC,GAAG,EAAE,aAAa;gBACpC,SAAS,EAAE,+BAAS,CAAC,kBAAkB;gBACvC,QAAQ,EAAE,mCAAa,CAAC,IAAI;gBAC5B,MAAM,EAAE,kBAAkB;gBAC1B,YAAY,EAAE,kBAAkB,CAAC,gBAAgB,CAAC,KAAK;gBACvD,cAAc,EAAE,EAAE;gBAClB,OAAO,EAAE,8CAA8C;gBACvD,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,YAAY,EAAE,KAAK;gBACnB,kBAAkB,EAAE;oBAClB,wBAAwB;oBACxB,sBAAsB;oBACtB,4BAA4B;iBAC7B;aACF,CAAC,CAAC;QACL,CAAC;QAED,2BAA2B;QAC3B,IAAI,kBAAkB,CAAC,mBAAmB,CAAC,KAAK,GAAG,GAAG,EAAE,CAAC;YACvD,MAAM,CAAC,IAAI,CAAC;gBACV,EAAE,EAAE,SAAS,IAAI,CAAC,GAAG,EAAE,WAAW;gBAClC,SAAS,EAAE,+BAAS,CAAC,kBAAkB;gBACvC,QAAQ,EAAE,mCAAa,CAAC,MAAM;gBAC9B,MAAM,EAAE,qBAAqB;gBAC7B,YAAY,EAAE,kBAAkB,CAAC,mBAAmB,CAAC,KAAK;gBAC1D,cAAc,EAAE,GAAG;gBACnB,OAAO,EAAE,sCAAsC;gBAC/C,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,YAAY,EAAE,KAAK;gBACnB,kBAAkB,EAAE;oBAClB,yBAAyB;oBACzB,yBAAyB;oBACzB,iBAAiB;iBAClB;aACF,CAAC,CAAC;QACL,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,KAAK,CAAC,kCAAkC,CACrD,kBAAsC,EACtC,kBAAsC,EACtC,mBAAwC;QAExC,MAAM,eAAe,GAAgC,EAAE,CAAC;QAExD,wCAAwC;QACxC,IAAI,kBAAkB,CAAC,gBAAgB,CAAC,KAAK,GAAG,EAAE,EAAE,CAAC;YACnD,eAAe,CAAC,IAAI,CAAC;gBACnB,EAAE,EAAE,OAAO,IAAI,CAAC,GAAG,EAAE,aAAa;gBAClC,IAAI,EAAE,wCAAkB,CAAC,YAAY;gBACrC,QAAQ,EAAE,4CAAsB,CAAC,IAAI;gBACrC,KAAK,EAAE,2BAA2B;gBAClC,WAAW,EAAE,2FAA2F;gBACxG,cAAc,EAAE;oBACd,aAAa,EAAE,EAAE;oBACjB,WAAW,EAAE,IAAI;oBACjB,sBAAsB,EAAE,EAAE;oBAC1B,iBAAiB,EAAE,IAAI;oBACvB,sBAAsB,EAAE,CAAC;iBAC1B;gBACD,kBAAkB,EAAE,IAAI;gBACxB,aAAa,EAAE,EAAE;gBACjB,wBAAwB,EAAE,UAAmB;gBAC7C,eAAe,EAAE;oBACf,6CAA6C;oBAC7C,qBAAqB;oBACrB,4BAA4B;iBAC7B;gBACD,QAAQ,EAAE,WAAW;aACtB,CAAC,CAAC;QACL,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,KAAK,CAAC,0BAA0B,CAC7C,kBAAsC,EACtC,mBAAwC;QAExC,kCAAkC;QAClC,OAAO;YACL,kBAAkB,EAAE,GAAG,EAAE,0BAA0B;YACnD,mBAAmB,EAAE;gBACnB;oBACE,MAAM,EAAE,kBAAkB;oBAC1B,WAAW,EAAE,IAAI;oBACjB,OAAO,EAAE,CAAC,mBAAmB,EAAE,yBAAyB,CAAC;oBACzD,eAAe,EAAE,EAAE;iBACpB;aACF;YACD,mBAAmB,EAAE;gBACnB,UAAU,EAAE;oBACV;wBACE,SAAS,EAAE,SAAS;wBACpB,WAAW,EAAE,GAAG;wBAChB,MAAM,EAAE,MAAe;qBACxB;iBACF;gBACD,UAAU,EAAE,CAAC,SAAS,EAAE,UAAU,CAAC;gBACnC,uBAAuB,EAAE,CAAC,sBAAsB,EAAE,mBAAmB,CAAC;aACvE;YACD,eAAe,EAAE;gBACf,8BAA8B;gBAC9B,6BAA6B;gBAC7B,6BAA6B;aAC9B;SACF,CAAC;IACJ,CAAC;;AAx/BH,0EAy/BC;AAx/ByB,uCAAO,GAAG,OAAO,CAAC;AAClB,8CAAc,GAAG,IAAI,GAAG,EAA+B,CAAC;AACxD,kDAAkB,GAAG,IAAI,GAAG,EAA+B,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\SystemPerformanceAnalysisEngine.ts"],
      sourcesContent: ["/**\r\n * System Performance Analysis Engine\r\n * \r\n * Comprehensive performance analysis service for Phase 3 Priority 3: Advanced System Analysis Tools\r\n * Provides real-time monitoring, trend analysis, efficiency calculations, and performance benchmarking\r\n * capabilities for HVAC duct systems.\r\n * \r\n * @version 3.0.0\r\n * <AUTHOR> Suite Development Team\r\n */\r\n\r\nimport {\r\n  SystemAnalysis,\r\n  PerformanceAnalysis,\r\n  PerformanceMetrics,\r\n  TrendAnalysis,\r\n  BenchmarkComparison,\r\n  EfficiencyAnalysis,\r\n  PerformanceAlert,\r\n  PerformanceRecommendation,\r\n  UncertaintyAnalysis,\r\n  SystemConfiguration,\r\n  AnalysisType,\r\n  AnalysisScope,\r\n  Measurement,\r\n  MeasurementSource,\r\n  QualityIndicator,\r\n  FanCurvePosition,\r\n  BalanceQuality,\r\n  BalanceGrade,\r\n  TrendDirection,\r\n  AlertType,\r\n  AlertSeverity,\r\n  RecommendationType,\r\n  RecommendationPriority,\r\n  BenchmarkType,\r\n  PerformanceAnomaly,\r\n  AnomalyType,\r\n  PredictiveAnalysis\r\n} from './types/SystemAnalysisTypes';\r\n\r\nimport { SystemPressureCalculator } from './SystemPressureCalculator';\r\nimport { FittingLossCalculator } from './FittingLossCalculator';\r\nimport { AdvancedFittingCalculator } from './AdvancedFittingCalculator';\r\nimport { AirPropertiesCalculator } from './AirPropertiesCalculator';\r\n\r\n/**\r\n * Main System Performance Analysis Engine\r\n * \r\n * Provides comprehensive performance analysis capabilities including:\r\n * - Real-time performance monitoring\r\n * - Trend analysis and forecasting\r\n * - Efficiency calculations and benchmarking\r\n * - Anomaly detection and alerting\r\n * - Performance recommendations\r\n */\r\nexport class SystemPerformanceAnalysisEngine {\r\n  private static readonly VERSION = '3.0.0';\r\n  private static readonly ANALYSIS_CACHE = new Map<string, PerformanceAnalysis>();\r\n  private static readonly BENCHMARK_DATABASE = new Map<string, BenchmarkComparison>();\r\n\r\n  /**\r\n   * Perform comprehensive system performance analysis\r\n   */\r\n  public static async analyzeSystemPerformance(\r\n    systemConfiguration: SystemConfiguration,\r\n    analysisScope: AnalysisScope,\r\n    historicalData?: PerformanceMetrics[]\r\n  ): Promise<PerformanceAnalysis> {\r\n    try {\r\n      const analysisId = this.generateAnalysisId(systemConfiguration.id);\r\n      const timestamp = new Date();\r\n\r\n      // Calculate current performance metrics\r\n      const performanceMetrics = await this.calculatePerformanceMetrics(\r\n        systemConfiguration,\r\n        analysisScope\r\n      );\r\n\r\n      // Perform trend analysis if historical data is available\r\n      const trendAnalysis = historicalData && historicalData.length > 0\r\n        ? await this.performTrendAnalysis(historicalData, performanceMetrics)\r\n        : this.createDefaultTrendAnalysis();\r\n\r\n      // Benchmark against similar systems\r\n      const benchmarkComparison = await this.performBenchmarkComparison(\r\n        systemConfiguration,\r\n        performanceMetrics\r\n      );\r\n\r\n      // Analyze system efficiency\r\n      const efficiencyAnalysis = await this.analyzeSystemEfficiency(\r\n        systemConfiguration,\r\n        performanceMetrics\r\n      );\r\n\r\n      // Detect performance alerts and anomalies\r\n      const alertsAndWarnings = await this.detectPerformanceAlerts(\r\n        performanceMetrics,\r\n        historicalData\r\n      );\r\n\r\n      // Generate performance recommendations\r\n      const recommendations = await this.generatePerformanceRecommendations(\r\n        performanceMetrics,\r\n        efficiencyAnalysis,\r\n        benchmarkComparison\r\n      );\r\n\r\n      // Perform uncertainty analysis\r\n      const uncertaintyAnalysis = await this.performUncertaintyAnalysis(\r\n        performanceMetrics,\r\n        systemConfiguration\r\n      );\r\n\r\n      const analysis: PerformanceAnalysis = {\r\n        id: analysisId,\r\n        systemId: systemConfiguration.id,\r\n        analysisTimestamp: timestamp,\r\n        performanceMetrics,\r\n        trendAnalysis,\r\n        benchmarkComparison,\r\n        efficiencyAnalysis,\r\n        alertsAndWarnings,\r\n        recommendations,\r\n        uncertaintyAnalysis\r\n      };\r\n\r\n      // Cache the analysis for future reference\r\n      this.ANALYSIS_CACHE.set(analysisId, analysis);\r\n\r\n      return analysis;\r\n\r\n    } catch (error) {\r\n      throw new Error(`System performance analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Calculate comprehensive performance metrics for the system\r\n   */\r\n  private static async calculatePerformanceMetrics(\r\n    systemConfiguration: SystemConfiguration,\r\n    analysisScope: AnalysisScope\r\n  ): Promise<PerformanceMetrics> {\r\n    const timestamp = new Date();\r\n\r\n    // Calculate system pressure using existing calculators\r\n    const systemPressureResult = SystemPressureCalculator.calculateEnhancedSystemPressure({\r\n      segments: this.createDuctSegmentsFromConfig(systemConfiguration),\r\n      systemType: systemConfiguration.systemType,\r\n      designConditions: systemConfiguration.operatingConditions\r\n    });\r\n\r\n    // Calculate fan performance metrics\r\n    const fanPerformance = this.calculateFanPerformance(\r\n      systemConfiguration,\r\n      systemPressureResult.totalPressureLoss\r\n    );\r\n\r\n    // Calculate airflow metrics\r\n    const airflowMetrics = this.calculateAirflowMetrics(systemConfiguration);\r\n\r\n    // Calculate system efficiency metrics\r\n    const efficiencyMetrics = this.calculateSystemEfficiencyMetrics(\r\n      systemConfiguration,\r\n      fanPerformance,\r\n      systemPressureResult\r\n    );\r\n\r\n    // Calculate environmental metrics\r\n    const environmentalMetrics = this.calculateEnvironmentalMetrics(\r\n      systemConfiguration,\r\n      fanPerformance\r\n    );\r\n\r\n    // Calculate system balance quality\r\n    const balanceQuality = this.assessSystemBalance(\r\n      systemConfiguration,\r\n      airflowMetrics\r\n    );\r\n\r\n    const performanceMetrics: PerformanceMetrics = {\r\n      // Pressure and Flow Metrics\r\n      totalSystemPressure: this.createMeasurement(\r\n        systemPressureResult.totalPressureLoss,\r\n        'in wg',\r\n        MeasurementSource.CALCULATED,\r\n        QualityIndicator.HIGH,\r\n        timestamp\r\n      ),\r\n      staticPressure: this.createMeasurement(\r\n        systemPressureResult.staticPressure || systemPressureResult.totalPressureLoss * 0.8,\r\n        'in wg',\r\n        MeasurementSource.CALCULATED,\r\n        QualityIndicator.HIGH,\r\n        timestamp\r\n      ),\r\n      velocityPressure: this.createMeasurement(\r\n        systemPressureResult.velocityPressure || systemPressureResult.totalPressureLoss * 0.2,\r\n        'in wg',\r\n        MeasurementSource.CALCULATED,\r\n        QualityIndicator.HIGH,\r\n        timestamp\r\n      ),\r\n      totalAirflow: this.createMeasurement(\r\n        airflowMetrics.totalAirflow,\r\n        'CFM',\r\n        MeasurementSource.CALCULATED,\r\n        QualityIndicator.HIGH,\r\n        timestamp\r\n      ),\r\n      designAirflow: this.createMeasurement(\r\n        systemConfiguration.designParameters.designAirflow,\r\n        'CFM',\r\n        MeasurementSource.MANUFACTURER_DATA,\r\n        QualityIndicator.HIGH,\r\n        timestamp\r\n      ),\r\n      airflowEfficiency: this.createMeasurement(\r\n        airflowMetrics.efficiency,\r\n        '%',\r\n        MeasurementSource.CALCULATED,\r\n        QualityIndicator.MEDIUM,\r\n        timestamp\r\n      ),\r\n\r\n      // Fan Performance\r\n      fanPower: this.createMeasurement(\r\n        fanPerformance.power,\r\n        'kW',\r\n        MeasurementSource.CALCULATED,\r\n        QualityIndicator.HIGH,\r\n        timestamp\r\n      ),\r\n      fanEfficiency: this.createMeasurement(\r\n        fanPerformance.efficiency,\r\n        '%',\r\n        MeasurementSource.CALCULATED,\r\n        QualityIndicator.HIGH,\r\n        timestamp\r\n      ),\r\n      fanSpeed: this.createMeasurement(\r\n        fanPerformance.speed,\r\n        'RPM',\r\n        MeasurementSource.CALCULATED,\r\n        QualityIndicator.MEDIUM,\r\n        timestamp\r\n      ),\r\n      fanCurvePosition: fanPerformance.curvePosition,\r\n\r\n      // System Efficiency\r\n      systemEfficiency: this.createMeasurement(\r\n        efficiencyMetrics.systemEfficiency,\r\n        '%',\r\n        MeasurementSource.CALCULATED,\r\n        QualityIndicator.HIGH,\r\n        timestamp\r\n      ),\r\n      transportEfficiency: this.createMeasurement(\r\n        efficiencyMetrics.transportEfficiency,\r\n        '%',\r\n        MeasurementSource.CALCULATED,\r\n        QualityIndicator.HIGH,\r\n        timestamp\r\n      ),\r\n      distributionEfficiency: this.createMeasurement(\r\n        efficiencyMetrics.distributionEfficiency,\r\n        '%',\r\n        MeasurementSource.CALCULATED,\r\n        QualityIndicator.MEDIUM,\r\n        timestamp\r\n      ),\r\n\r\n      // Environmental Metrics\r\n      noiseLevel: this.createMeasurement(\r\n        environmentalMetrics.noiseLevel,\r\n        'dBA',\r\n        MeasurementSource.ESTIMATED,\r\n        QualityIndicator.MEDIUM,\r\n        timestamp\r\n      ),\r\n      vibrationLevel: this.createMeasurement(\r\n        environmentalMetrics.vibrationLevel,\r\n        'mm/s',\r\n        MeasurementSource.ESTIMATED,\r\n        QualityIndicator.LOW,\r\n        timestamp\r\n      ),\r\n      temperatureRise: this.createMeasurement(\r\n        environmentalMetrics.temperatureRise,\r\n        '\xB0F',\r\n        MeasurementSource.CALCULATED,\r\n        QualityIndicator.MEDIUM,\r\n        timestamp\r\n      ),\r\n\r\n      // Filter and Component Performance\r\n      filterPressureDrop: this.createMeasurement(\r\n        environmentalMetrics.filterPressureDrop,\r\n        'in wg',\r\n        MeasurementSource.ESTIMATED,\r\n        QualityIndicator.MEDIUM,\r\n        timestamp\r\n      ),\r\n      coilPressureDrop: this.createMeasurement(\r\n        environmentalMetrics.coilPressureDrop,\r\n        'in wg',\r\n        MeasurementSource.ESTIMATED,\r\n        QualityIndicator.MEDIUM,\r\n        timestamp\r\n      ),\r\n      dampersPosition: [], // Would be populated from actual system data\r\n\r\n      // System Balance\r\n      balanceQuality,\r\n      flowDistribution: airflowMetrics.flowDistribution\r\n    };\r\n\r\n    return performanceMetrics;\r\n  }\r\n\r\n  /**\r\n   * Create a standardized measurement object\r\n   */\r\n  private static createMeasurement(\r\n    value: number,\r\n    units: string,\r\n    source: MeasurementSource,\r\n    quality: QualityIndicator,\r\n    timestamp: Date,\r\n    accuracy: number = 0.95\r\n  ): Measurement {\r\n    return {\r\n      value,\r\n      units,\r\n      accuracy,\r\n      timestamp,\r\n      source,\r\n      qualityIndicator: quality,\r\n      uncertaintyBounds: {\r\n        lowerBound: value * (1 - (1 - accuracy)),\r\n        upperBound: value * (1 + (1 - accuracy)),\r\n        confidenceLevel: accuracy * 100,\r\n        distributionType: 'normal' as const\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate fan performance metrics\r\n   */\r\n  private static calculateFanPerformance(\r\n    systemConfiguration: SystemConfiguration,\r\n    systemPressure: number\r\n  ): any {\r\n    const designAirflow = systemConfiguration.designParameters.designAirflow;\r\n    const designPressure = systemConfiguration.designParameters.designPressure;\r\n\r\n    // Simplified fan performance calculation\r\n    // In practice, this would use actual fan curves and manufacturer data\r\n    const efficiency = Math.max(0.6, Math.min(0.9, 0.8 - (systemPressure - designPressure) * 0.05));\r\n    const power = (designAirflow * systemPressure) / (6356 * efficiency); // HP\r\n    const powerKW = power * 0.746; // Convert to kW\r\n    const speed = 1800 * Math.sqrt(systemPressure / designPressure); // Simplified speed calculation\r\n\r\n    const curvePosition: FanCurvePosition = {\r\n      operatingPoint: {\r\n        airflow: designAirflow,\r\n        pressure: systemPressure,\r\n        power: powerKW,\r\n        efficiency: efficiency * 100,\r\n        speed\r\n      },\r\n      designPoint: {\r\n        airflow: designAirflow,\r\n        pressure: designPressure,\r\n        power: (designAirflow * designPressure) / (6356 * 0.8) * 0.746,\r\n        efficiency: 80,\r\n        speed: 1800\r\n      },\r\n      efficiencyAtOperating: efficiency * 100,\r\n      efficiencyAtDesign: 80,\r\n      surgeMargin: Math.max(0, (designAirflow * 0.7 - designAirflow) / designAirflow * 100),\r\n      stallMargin: Math.max(0, (designAirflow * 1.3 - designAirflow) / designAirflow * 100),\r\n      recommendedOperatingRange: {\r\n        minAirflow: designAirflow * 0.7,\r\n        maxAirflow: designAirflow * 1.3,\r\n        minPressure: designPressure * 0.5,\r\n        maxPressure: designPressure * 1.5\r\n      }\r\n    };\r\n\r\n    return {\r\n      power: powerKW,\r\n      efficiency: efficiency * 100,\r\n      speed,\r\n      curvePosition\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate airflow metrics and distribution\r\n   */\r\n  private static calculateAirflowMetrics(systemConfiguration: SystemConfiguration): any {\r\n    const designAirflow = systemConfiguration.designParameters.designAirflow;\r\n    \r\n    // Simplified airflow calculation\r\n    // In practice, this would consider actual system measurements\r\n    const totalAirflow = designAirflow * 0.95; // Assume 5% leakage\r\n    const efficiency = (totalAirflow / designAirflow) * 100;\r\n\r\n    return {\r\n      totalAirflow,\r\n      efficiency,\r\n      flowDistribution: {\r\n        uniformityIndex: 0.85, // Simplified\r\n        variationCoefficient: 0.15,\r\n        zones: []\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate system efficiency metrics\r\n   */\r\n  private static calculateSystemEfficiencyMetrics(\r\n    systemConfiguration: SystemConfiguration,\r\n    fanPerformance: any,\r\n    systemPressureResult: any\r\n  ): any {\r\n    const designAirflow = systemConfiguration.designParameters.designAirflow;\r\n    const designPressure = systemConfiguration.designParameters.designPressure;\r\n\r\n    // Calculate various efficiency metrics\r\n    const systemEfficiency = Math.min(95, fanPerformance.efficiency * 0.9); // Account for system losses\r\n    const transportEfficiency = Math.min(90, 100 - (systemPressureResult.totalPressureLoss / designPressure) * 10);\r\n    const distributionEfficiency = Math.min(85, systemEfficiency * 0.9); // Account for distribution losses\r\n\r\n    return {\r\n      systemEfficiency,\r\n      transportEfficiency,\r\n      distributionEfficiency\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate environmental metrics\r\n   */\r\n  private static calculateEnvironmentalMetrics(\r\n    systemConfiguration: SystemConfiguration,\r\n    fanPerformance: any\r\n  ): any {\r\n    const designAirflow = systemConfiguration.designParameters.designAirflow;\r\n\r\n    // Simplified environmental calculations\r\n    const velocity = designAirflow / 144; // Simplified velocity calculation\r\n    const noiseLevel = 40 + 20 * Math.log10(velocity / 1000); // Simplified noise model\r\n    const vibrationLevel = Math.max(0.5, velocity / 2000); // Simplified vibration model\r\n    const temperatureRise = fanPerformance.power * 3412 / (designAirflow * 1.08); // Fan heat rise\r\n\r\n    return {\r\n      noiseLevel: Math.max(35, Math.min(65, noiseLevel)),\r\n      vibrationLevel: Math.max(0.1, Math.min(5.0, vibrationLevel)),\r\n      temperatureRise: Math.max(0.5, Math.min(5.0, temperatureRise)),\r\n      filterPressureDrop: 0.5, // Typical clean filter\r\n      coilPressureDrop: 0.8 // Typical coil pressure drop\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Assess system balance quality\r\n   */\r\n  private static assessSystemBalance(\r\n    systemConfiguration: SystemConfiguration,\r\n    airflowMetrics: any\r\n  ): BalanceQuality {\r\n    // Simplified balance assessment\r\n    const flowVariation = 0.15; // 15% variation\r\n    const pressureVariation = 0.12; // 12% variation\r\n    const overallScore = Math.max(0, 100 - (flowVariation + pressureVariation) * 200);\r\n\r\n    let balanceGrade: BalanceGrade;\r\n    if (overallScore >= 90) balanceGrade = BalanceGrade.EXCELLENT;\r\n    else if (overallScore >= 80) balanceGrade = BalanceGrade.GOOD;\r\n    else if (overallScore >= 70) balanceGrade = BalanceGrade.ACCEPTABLE;\r\n    else if (overallScore >= 60) balanceGrade = BalanceGrade.POOR;\r\n    else balanceGrade = BalanceGrade.CRITICAL;\r\n\r\n    return {\r\n      overallScore,\r\n      flowVariation,\r\n      pressureVariation,\r\n      balanceGrade,\r\n      criticalZones: balanceGrade === BalanceGrade.CRITICAL ? ['Zone 1', 'Zone 3'] : [],\r\n      balanceRecommendations: this.generateBalanceRecommendations(balanceGrade)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Generate balance recommendations based on grade\r\n   */\r\n  private static generateBalanceRecommendations(grade: BalanceGrade): string[] {\r\n    switch (grade) {\r\n      case BalanceGrade.CRITICAL:\r\n        return [\r\n          'Immediate system rebalancing required',\r\n          'Check for blocked dampers or ducts',\r\n          'Verify fan operation and capacity',\r\n          'Consider professional commissioning'\r\n        ];\r\n      case BalanceGrade.POOR:\r\n        return [\r\n          'System rebalancing recommended',\r\n          'Adjust damper positions',\r\n          'Check for duct leakage',\r\n          'Verify design calculations'\r\n        ];\r\n      case BalanceGrade.ACCEPTABLE:\r\n        return [\r\n          'Minor adjustments may improve performance',\r\n          'Monitor system performance trends',\r\n          'Consider seasonal adjustments'\r\n        ];\r\n      default:\r\n        return ['System balance is within acceptable limits'];\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Create duct segments from system configuration\r\n   */\r\n  private static createDuctSegmentsFromConfig(systemConfiguration: SystemConfiguration): any[] {\r\n    // Simplified duct segment creation\r\n    // In practice, this would parse the actual system configuration\r\n    return [\r\n      {\r\n        id: 'main_supply',\r\n        length: 100,\r\n        diameter: 24,\r\n        material: 'galvanized_steel',\r\n        roughness: 0.0015,\r\n        airflow: systemConfiguration.designParameters.designAirflow,\r\n        fittings: []\r\n      }\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Generate unique analysis ID\r\n   */\r\n  private static generateAnalysisId(systemId: string): string {\r\n    const timestamp = Date.now();\r\n    const random = Math.random().toString(36).substring(2, 8);\r\n    return `analysis_${systemId}_${timestamp}_${random}`;\r\n  }\r\n\r\n  /**\r\n   * Create default trend analysis when no historical data is available\r\n   */\r\n  private static createDefaultTrendAnalysis(): TrendAnalysis {\r\n    return {\r\n      timeRange: {\r\n        startDate: new Date(),\r\n        endDate: new Date(),\r\n        duration: 0,\r\n        units: 'days' as const\r\n      },\r\n      trendDirection: TrendDirection.STABLE,\r\n      trendMagnitude: 0,\r\n      seasonalPatterns: [],\r\n      anomalies: [],\r\n      predictiveAnalysis: {\r\n        forecastHorizon: 12,\r\n        predictedPerformance: [],\r\n        confidenceInterval: { lowerBound: 0, upperBound: 0, confidenceLevel: 0 },\r\n        predictionModel: {\r\n          modelType: 'linear_regression' as const,\r\n          accuracy: 0,\r\n          lastTrainingDate: new Date(),\r\n          dataPoints: 0,\r\n          validationScore: 0\r\n        },\r\n        keyFactors: [],\r\n        scenarios: []\r\n      },\r\n      degradationRate: {\r\n        overallDegradationRate: 2.0, // 2% per year typical\r\n        componentDegradation: [],\r\n        degradationFactors: [],\r\n        maintenanceImpact: {\r\n          preventiveMaintenance: { performanceImpact: 5, lifespanImpact: 2, costImpact: 1000 },\r\n          correctiveMaintenance: { performanceImpact: -10, lifespanImpact: -1, costImpact: 5000 },\r\n          deferredMaintenance: { performanceImpact: -15, lifespanImpact: -3, costImpact: 10000 }\r\n        },\r\n        projectedLifespan: {\r\n          currentAge: 0,\r\n          designLife: 20,\r\n          projectedLife: 18,\r\n          confidenceLevel: 80,\r\n          keyAssumptions: ['Regular maintenance', 'Normal operating conditions']\r\n        }\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Perform trend analysis on historical performance data\r\n   */\r\n  private static async performTrendAnalysis(\r\n    historicalData: PerformanceMetrics[],\r\n    currentMetrics: PerformanceMetrics\r\n  ): Promise<TrendAnalysis> {\r\n    if (historicalData.length < 2) {\r\n      return this.createDefaultTrendAnalysis();\r\n    }\r\n\r\n    // Calculate trend direction and magnitude\r\n    const trendAnalysis = this.calculateTrendDirection(historicalData);\r\n\r\n    // Detect seasonal patterns\r\n    const seasonalPatterns = this.detectSeasonalPatterns(historicalData);\r\n\r\n    // Detect anomalies\r\n    const anomalies = this.detectAnomalies(historicalData, currentMetrics);\r\n\r\n    // Generate predictive analysis\r\n    const predictiveAnalysis = this.generatePredictiveAnalysis(historicalData);\r\n\r\n    // Calculate degradation rate\r\n    const degradationRate = this.calculateDegradationRate(historicalData);\r\n\r\n    const timeRange = {\r\n      startDate: historicalData[0].totalSystemPressure.timestamp,\r\n      endDate: currentMetrics.totalSystemPressure.timestamp,\r\n      duration: historicalData.length,\r\n      units: 'days' as const\r\n    };\r\n\r\n    return {\r\n      timeRange,\r\n      trendDirection: trendAnalysis.direction,\r\n      trendMagnitude: trendAnalysis.magnitude,\r\n      seasonalPatterns,\r\n      anomalies,\r\n      predictiveAnalysis,\r\n      degradationRate\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate trend direction from historical data\r\n   */\r\n  private static calculateTrendDirection(historicalData: PerformanceMetrics[]): {\r\n    direction: TrendDirection;\r\n    magnitude: number;\r\n  } {\r\n    if (historicalData.length < 3) {\r\n      return { direction: TrendDirection.STABLE, magnitude: 0 };\r\n    }\r\n\r\n    // Simple linear regression on system efficiency\r\n    const efficiencyValues = historicalData.map(data => data.systemEfficiency.value);\r\n    const n = efficiencyValues.length;\r\n    const x = Array.from({ length: n }, (_, i) => i);\r\n\r\n    const sumX = x.reduce((a, b) => a + b, 0);\r\n    const sumY = efficiencyValues.reduce((a, b) => a + b, 0);\r\n    const sumXY = x.reduce((sum, xi, i) => sum + xi * efficiencyValues[i], 0);\r\n    const sumXX = x.reduce((sum, xi) => sum + xi * xi, 0);\r\n\r\n    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);\r\n    const magnitude = Math.abs(slope);\r\n\r\n    let direction: TrendDirection;\r\n    if (magnitude < 0.1) {\r\n      direction = TrendDirection.STABLE;\r\n    } else if (slope > 0) {\r\n      direction = TrendDirection.IMPROVING;\r\n    } else {\r\n      direction = TrendDirection.DEGRADING;\r\n    }\r\n\r\n    return { direction, magnitude };\r\n  }\r\n\r\n  /**\r\n   * Detect seasonal patterns in performance data\r\n   */\r\n  private static detectSeasonalPatterns(historicalData: PerformanceMetrics[]): any[] {\r\n    // Simplified seasonal pattern detection\r\n    // In practice, this would use more sophisticated time series analysis\r\n    return [\r\n      {\r\n        season: 'summer' as const,\r\n        averagePerformance: 85,\r\n        performanceVariation: 5,\r\n        typicalIssues: ['Higher cooling loads', 'Increased fan power']\r\n      },\r\n      {\r\n        season: 'winter' as const,\r\n        averagePerformance: 88,\r\n        performanceVariation: 3,\r\n        typicalIssues: ['Filter loading', 'Heating coil pressure drop']\r\n      }\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Detect performance anomalies\r\n   */\r\n  private static detectAnomalies(\r\n    historicalData: PerformanceMetrics[],\r\n    currentMetrics: PerformanceMetrics\r\n  ): PerformanceAnomaly[] {\r\n    const anomalies: PerformanceAnomaly[] = [];\r\n\r\n    // Check for sudden efficiency drop\r\n    if (historicalData.length > 0) {\r\n      const lastEfficiency = historicalData[historicalData.length - 1].systemEfficiency.value;\r\n      const currentEfficiency = currentMetrics.systemEfficiency.value;\r\n      const efficiencyDrop = lastEfficiency - currentEfficiency;\r\n\r\n      if (efficiencyDrop > 5) { // 5% drop threshold\r\n        anomalies.push({\r\n          id: `anomaly_${Date.now()}`,\r\n          detectionTimestamp: new Date(),\r\n          anomalyType: AnomalyType.SUDDEN_CHANGE,\r\n          severity: efficiencyDrop > 10 ? 'critical' as const : 'high' as const,\r\n          affectedMetrics: ['systemEfficiency'],\r\n          deviationMagnitude: efficiencyDrop,\r\n          duration: 1,\r\n          possibleCauses: [\r\n            {\r\n              cause: 'Filter blockage',\r\n              probability: 60,\r\n              category: 'maintenance_issue' as const,\r\n              diagnosticSteps: ['Check filter pressure drop', 'Inspect filter condition']\r\n            },\r\n            {\r\n              cause: 'Fan belt slippage',\r\n              probability: 30,\r\n              category: 'equipment_failure' as const,\r\n              diagnosticSteps: ['Check fan belt tension', 'Inspect motor operation']\r\n            }\r\n          ],\r\n          recommendedActions: [\r\n            'Replace or clean filters',\r\n            'Check fan operation',\r\n            'Verify damper positions'\r\n          ],\r\n          resolved: false\r\n        });\r\n      }\r\n    }\r\n\r\n    return anomalies;\r\n  }\r\n\r\n  /**\r\n   * Generate predictive analysis\r\n   */\r\n  private static generatePredictiveAnalysis(historicalData: PerformanceMetrics[]): PredictiveAnalysis {\r\n    // Simplified predictive analysis\r\n    // In practice, this would use machine learning models\r\n    return {\r\n      forecastHorizon: 12,\r\n      predictedPerformance: [\r\n        {\r\n          metric: 'systemEfficiency',\r\n          currentValue: 85,\r\n          predictedValue: 83,\r\n          changePercent: -2.4,\r\n          timeToTarget: 6\r\n        }\r\n      ],\r\n      confidenceInterval: {\r\n        lowerBound: 80,\r\n        upperBound: 86,\r\n        confidenceLevel: 85\r\n      },\r\n      predictionModel: {\r\n        modelType: 'time_series' as const,\r\n        accuracy: 85,\r\n        lastTrainingDate: new Date(),\r\n        dataPoints: historicalData.length,\r\n        validationScore: 0.85\r\n      },\r\n      keyFactors: [\r\n        {\r\n          factor: 'Filter condition',\r\n          impact: 40,\r\n          controllable: true,\r\n          mitigationStrategies: ['Regular filter replacement', 'Pressure monitoring']\r\n        }\r\n      ],\r\n      scenarios: []\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate system degradation rate\r\n   */\r\n  private static calculateDegradationRate(historicalData: PerformanceMetrics[]): any {\r\n    // Simplified degradation calculation\r\n    return {\r\n      overallDegradationRate: 2.5, // 2.5% per year\r\n      componentDegradation: [\r\n        {\r\n          componentId: 'main_fan',\r\n          componentType: 'fan' as const,\r\n          degradationRate: 1.5,\r\n          currentCondition: 92,\r\n          estimatedRemainingLife: 12,\r\n          replacementThreshold: 70\r\n        }\r\n      ],\r\n      degradationFactors: [\r\n        {\r\n          factor: 'Operating hours',\r\n          impact: 50,\r\n          controllable: false,\r\n          mitigationStrategies: ['Optimize operating schedule']\r\n        },\r\n        {\r\n          factor: 'Maintenance quality',\r\n          impact: 30,\r\n          controllable: true,\r\n          mitigationStrategies: ['Preventive maintenance program', 'Staff training']\r\n        }\r\n      ],\r\n      maintenanceImpact: {\r\n        preventiveMaintenance: { performanceImpact: 5, lifespanImpact: 2, costImpact: 1000 },\r\n        correctiveMaintenance: { performanceImpact: -10, lifespanImpact: -1, costImpact: 5000 },\r\n        deferredMaintenance: { performanceImpact: -15, lifespanImpact: -3, costImpact: 10000 }\r\n      },\r\n      projectedLifespan: {\r\n        currentAge: 3,\r\n        designLife: 20,\r\n        projectedLife: 18,\r\n        confidenceLevel: 80,\r\n        keyAssumptions: ['Regular maintenance', 'Normal operating conditions']\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Perform benchmark comparison against similar systems\r\n   */\r\n  private static async performBenchmarkComparison(\r\n    systemConfiguration: SystemConfiguration,\r\n    performanceMetrics: PerformanceMetrics\r\n  ): Promise<BenchmarkComparison> {\r\n    // Simplified benchmark comparison\r\n    // In practice, this would query a database of similar systems\r\n    const systemEfficiency = performanceMetrics.systemEfficiency.value;\r\n    const industryAverage = 82; // Typical industry average\r\n    const bestInClass = 92; // Best in class performance\r\n\r\n    return {\r\n      benchmarkType: BenchmarkType.INDUSTRY_AVERAGE,\r\n      benchmarkSource: 'ASHRAE Performance Database',\r\n      systemPerformance: systemEfficiency,\r\n      benchmarkValue: industryAverage,\r\n      percentile: this.calculatePercentile(systemEfficiency, industryAverage),\r\n      performanceGap: industryAverage - systemEfficiency,\r\n      improvementPotential: bestInClass - systemEfficiency,\r\n      similarSystems: [\r\n        {\r\n          systemId: 'similar_system_1',\r\n          systemName: 'Office Building HVAC',\r\n          performanceMetric: 84,\r\n          systemCharacteristics: {\r\n            size: 'medium' as const,\r\n            age: 5,\r\n            buildingType: 'office' as const,\r\n            climateZone: '4A',\r\n            operatingHours: 2500\r\n          },\r\n          performanceDifference: 84 - systemEfficiency\r\n        }\r\n      ]\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate percentile ranking\r\n   */\r\n  private static calculatePercentile(value: number, average: number): number {\r\n    // Simplified percentile calculation\r\n    // Assumes normal distribution with standard deviation of 8\r\n    const standardDeviation = 8;\r\n    const zScore = (value - average) / standardDeviation;\r\n\r\n    // Convert z-score to percentile (simplified)\r\n    if (zScore >= 2) return 97;\r\n    if (zScore >= 1) return 84;\r\n    if (zScore >= 0) return 50 + (zScore * 34);\r\n    if (zScore >= -1) return 50 + (zScore * 34);\r\n    if (zScore >= -2) return 16;\r\n    return 3;\r\n  }\r\n\r\n  /**\r\n   * Analyze system efficiency in detail\r\n   */\r\n  private static async analyzeSystemEfficiency(\r\n    systemConfiguration: SystemConfiguration,\r\n    performanceMetrics: PerformanceMetrics\r\n  ): Promise<EfficiencyAnalysis> {\r\n    // Simplified efficiency analysis\r\n    return {\r\n      overallEfficiency: {\r\n        value: performanceMetrics.systemEfficiency.value,\r\n        units: '%',\r\n        calculationMethod: 'calculated' as const,\r\n        accuracy: 0.9,\r\n        timestamp: new Date()\r\n      },\r\n      componentEfficiencies: [\r\n        {\r\n          componentId: 'main_fan',\r\n          componentType: 'fan' as const,\r\n          efficiency: performanceMetrics.fanEfficiency.value,\r\n          ratedEfficiency: 85,\r\n          degradationFactor: 0.95,\r\n          maintenanceStatus: 'good' as const\r\n        }\r\n      ],\r\n      efficiencyTrends: [],\r\n      efficiencyLosses: [],\r\n      improvementOpportunities: [],\r\n      benchmarkComparison: {\r\n        benchmarkType: BenchmarkType.INDUSTRY_AVERAGE,\r\n        benchmarkSource: 'Industry Standards',\r\n        systemPerformance: performanceMetrics.systemEfficiency.value,\r\n        benchmarkValue: 82,\r\n        percentile: 65,\r\n        performanceGap: 0,\r\n        improvementPotential: 10,\r\n        similarSystems: []\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Detect performance alerts and warnings\r\n   */\r\n  private static async detectPerformanceAlerts(\r\n    performanceMetrics: PerformanceMetrics,\r\n    historicalData?: PerformanceMetrics[]\r\n  ): Promise<PerformanceAlert[]> {\r\n    const alerts: PerformanceAlert[] = [];\r\n\r\n    // Check efficiency threshold\r\n    if (performanceMetrics.systemEfficiency.value < 75) {\r\n      alerts.push({\r\n        id: `alert_${Date.now()}_efficiency`,\r\n        alertType: AlertType.THRESHOLD_EXCEEDED,\r\n        severity: AlertSeverity.HIGH,\r\n        metric: 'systemEfficiency',\r\n        currentValue: performanceMetrics.systemEfficiency.value,\r\n        thresholdValue: 75,\r\n        message: 'System efficiency below acceptable threshold',\r\n        timestamp: new Date(),\r\n        acknowledged: false,\r\n        recommendedActions: [\r\n          'Check filter condition',\r\n          'Verify fan operation',\r\n          'Inspect ductwork for leaks'\r\n        ]\r\n      });\r\n    }\r\n\r\n    // Check pressure threshold\r\n    if (performanceMetrics.totalSystemPressure.value > 4.0) {\r\n      alerts.push({\r\n        id: `alert_${Date.now()}_pressure`,\r\n        alertType: AlertType.THRESHOLD_EXCEEDED,\r\n        severity: AlertSeverity.MEDIUM,\r\n        metric: 'totalSystemPressure',\r\n        currentValue: performanceMetrics.totalSystemPressure.value,\r\n        thresholdValue: 4.0,\r\n        message: 'System pressure higher than expected',\r\n        timestamp: new Date(),\r\n        acknowledged: false,\r\n        recommendedActions: [\r\n          'Check for blocked ducts',\r\n          'Verify damper positions',\r\n          'Inspect filters'\r\n        ]\r\n      });\r\n    }\r\n\r\n    return alerts;\r\n  }\r\n\r\n  /**\r\n   * Generate performance recommendations\r\n   */\r\n  private static async generatePerformanceRecommendations(\r\n    performanceMetrics: PerformanceMetrics,\r\n    efficiencyAnalysis: EfficiencyAnalysis,\r\n    benchmarkComparison: BenchmarkComparison\r\n  ): Promise<PerformanceRecommendation[]> {\r\n    const recommendations: PerformanceRecommendation[] = [];\r\n\r\n    // Efficiency improvement recommendation\r\n    if (performanceMetrics.systemEfficiency.value < 85) {\r\n      recommendations.push({\r\n        id: `rec_${Date.now()}_efficiency`,\r\n        type: RecommendationType.OPTIMIZATION,\r\n        priority: RecommendationPriority.HIGH,\r\n        title: 'Improve System Efficiency',\r\n        description: 'System efficiency is below optimal levels. Consider implementing efficiency improvements.',\r\n        expectedImpact: {\r\n          energySavings: 15,\r\n          costSavings: 2500,\r\n          performanceImprovement: 10,\r\n          emissionReduction: 1200,\r\n          reliabilityImprovement: 5\r\n        },\r\n        implementationCost: 5000,\r\n        paybackPeriod: 24,\r\n        implementationComplexity: 'moderate' as const,\r\n        requiredActions: [\r\n          'Replace filters with high-efficiency models',\r\n          'Seal ductwork leaks',\r\n          'Optimize fan speed control'\r\n        ],\r\n        timeline: '2-4 weeks'\r\n      });\r\n    }\r\n\r\n    return recommendations;\r\n  }\r\n\r\n  /**\r\n   * Perform uncertainty analysis on performance metrics\r\n   */\r\n  private static async performUncertaintyAnalysis(\r\n    performanceMetrics: PerformanceMetrics,\r\n    systemConfiguration: SystemConfiguration\r\n  ): Promise<UncertaintyAnalysis> {\r\n    // Simplified uncertainty analysis\r\n    return {\r\n      overallUncertainty: 0.1, // 10% overall uncertainty\r\n      metricUncertainties: [\r\n        {\r\n          metric: 'systemEfficiency',\r\n          uncertainty: 0.05,\r\n          sources: ['measurement error', 'calculation assumptions'],\r\n          confidenceLevel: 90\r\n        }\r\n      ],\r\n      sensitivityAnalysis: {\r\n        parameters: [\r\n          {\r\n            parameter: 'airflow',\r\n            sensitivity: 0.8,\r\n            impact: 'high' as const\r\n          }\r\n        ],\r\n        keyDrivers: ['airflow', 'pressure'],\r\n        uncertaintyContributors: ['measurement accuracy', 'model assumptions']\r\n      },\r\n      recommendations: [\r\n        'Improve measurement accuracy',\r\n        'Calibrate sensors regularly',\r\n        'Validate calculation models'\r\n      ]\r\n    };\r\n  }\r\n}\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "0ab3351d35486dd2fc51b40b60c8eeb191be70fc"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_sd8kdgqk0 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_sd8kdgqk0();
cov_sd8kdgqk0().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_sd8kdgqk0().s[1]++;
exports.SystemPerformanceAnalysisEngine = void 0;
const SystemAnalysisTypes_1 =
/* istanbul ignore next */
(cov_sd8kdgqk0().s[2]++, require("./types/SystemAnalysisTypes"));
const SystemPressureCalculator_1 =
/* istanbul ignore next */
(cov_sd8kdgqk0().s[3]++, require("./SystemPressureCalculator"));
/**
 * Main System Performance Analysis Engine
 *
 * Provides comprehensive performance analysis capabilities including:
 * - Real-time performance monitoring
 * - Trend analysis and forecasting
 * - Efficiency calculations and benchmarking
 * - Anomaly detection and alerting
 * - Performance recommendations
 */
class SystemPerformanceAnalysisEngine {
  /**
   * Perform comprehensive system performance analysis
   */
  static async analyzeSystemPerformance(systemConfiguration, analysisScope, historicalData) {
    /* istanbul ignore next */
    cov_sd8kdgqk0().f[0]++;
    cov_sd8kdgqk0().s[4]++;
    try {
      const analysisId =
      /* istanbul ignore next */
      (cov_sd8kdgqk0().s[5]++, this.generateAnalysisId(systemConfiguration.id));
      const timestamp =
      /* istanbul ignore next */
      (cov_sd8kdgqk0().s[6]++, new Date());
      // Calculate current performance metrics
      const performanceMetrics =
      /* istanbul ignore next */
      (cov_sd8kdgqk0().s[7]++, await this.calculatePerformanceMetrics(systemConfiguration, analysisScope));
      // Perform trend analysis if historical data is available
      const trendAnalysis =
      /* istanbul ignore next */
      (cov_sd8kdgqk0().s[8]++,
      /* istanbul ignore next */
      (cov_sd8kdgqk0().b[1][0]++, historicalData) &&
      /* istanbul ignore next */
      (cov_sd8kdgqk0().b[1][1]++, historicalData.length > 0) ?
      /* istanbul ignore next */
      (cov_sd8kdgqk0().b[0][0]++, await this.performTrendAnalysis(historicalData, performanceMetrics)) :
      /* istanbul ignore next */
      (cov_sd8kdgqk0().b[0][1]++, this.createDefaultTrendAnalysis()));
      // Benchmark against similar systems
      const benchmarkComparison =
      /* istanbul ignore next */
      (cov_sd8kdgqk0().s[9]++, await this.performBenchmarkComparison(systemConfiguration, performanceMetrics));
      // Analyze system efficiency
      const efficiencyAnalysis =
      /* istanbul ignore next */
      (cov_sd8kdgqk0().s[10]++, await this.analyzeSystemEfficiency(systemConfiguration, performanceMetrics));
      // Detect performance alerts and anomalies
      const alertsAndWarnings =
      /* istanbul ignore next */
      (cov_sd8kdgqk0().s[11]++, await this.detectPerformanceAlerts(performanceMetrics, historicalData));
      // Generate performance recommendations
      const recommendations =
      /* istanbul ignore next */
      (cov_sd8kdgqk0().s[12]++, await this.generatePerformanceRecommendations(performanceMetrics, efficiencyAnalysis, benchmarkComparison));
      // Perform uncertainty analysis
      const uncertaintyAnalysis =
      /* istanbul ignore next */
      (cov_sd8kdgqk0().s[13]++, await this.performUncertaintyAnalysis(performanceMetrics, systemConfiguration));
      const analysis =
      /* istanbul ignore next */
      (cov_sd8kdgqk0().s[14]++, {
        id: analysisId,
        systemId: systemConfiguration.id,
        analysisTimestamp: timestamp,
        performanceMetrics,
        trendAnalysis,
        benchmarkComparison,
        efficiencyAnalysis,
        alertsAndWarnings,
        recommendations,
        uncertaintyAnalysis
      });
      // Cache the analysis for future reference
      /* istanbul ignore next */
      cov_sd8kdgqk0().s[15]++;
      this.ANALYSIS_CACHE.set(analysisId, analysis);
      /* istanbul ignore next */
      cov_sd8kdgqk0().s[16]++;
      return analysis;
    } catch (error) {
      /* istanbul ignore next */
      cov_sd8kdgqk0().s[17]++;
      throw new Error(`System performance analysis failed: ${error instanceof Error ?
      /* istanbul ignore next */
      (cov_sd8kdgqk0().b[2][0]++, error.message) :
      /* istanbul ignore next */
      (cov_sd8kdgqk0().b[2][1]++, 'Unknown error')}`);
    }
  }
  /**
   * Calculate comprehensive performance metrics for the system
   */
  static async calculatePerformanceMetrics(systemConfiguration, analysisScope) {
    /* istanbul ignore next */
    cov_sd8kdgqk0().f[1]++;
    const timestamp =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[18]++, new Date());
    // Calculate system pressure using existing calculators
    const systemPressureResult =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[19]++, SystemPressureCalculator_1.SystemPressureCalculator.calculateEnhancedSystemPressure({
      segments: this.createDuctSegmentsFromConfig(systemConfiguration),
      systemType: systemConfiguration.systemType,
      designConditions: systemConfiguration.operatingConditions
    }));
    // Calculate fan performance metrics
    const fanPerformance =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[20]++, this.calculateFanPerformance(systemConfiguration, systemPressureResult.totalPressureLoss));
    // Calculate airflow metrics
    const airflowMetrics =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[21]++, this.calculateAirflowMetrics(systemConfiguration));
    // Calculate system efficiency metrics
    const efficiencyMetrics =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[22]++, this.calculateSystemEfficiencyMetrics(systemConfiguration, fanPerformance, systemPressureResult));
    // Calculate environmental metrics
    const environmentalMetrics =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[23]++, this.calculateEnvironmentalMetrics(systemConfiguration, fanPerformance));
    // Calculate system balance quality
    const balanceQuality =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[24]++, this.assessSystemBalance(systemConfiguration, airflowMetrics));
    const performanceMetrics =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[25]++, {
      // Pressure and Flow Metrics
      totalSystemPressure: this.createMeasurement(systemPressureResult.totalPressureLoss, 'in wg', SystemAnalysisTypes_1.MeasurementSource.CALCULATED, SystemAnalysisTypes_1.QualityIndicator.HIGH, timestamp),
      staticPressure: this.createMeasurement(
      /* istanbul ignore next */
      (cov_sd8kdgqk0().b[3][0]++, systemPressureResult.staticPressure) ||
      /* istanbul ignore next */
      (cov_sd8kdgqk0().b[3][1]++, systemPressureResult.totalPressureLoss * 0.8), 'in wg', SystemAnalysisTypes_1.MeasurementSource.CALCULATED, SystemAnalysisTypes_1.QualityIndicator.HIGH, timestamp),
      velocityPressure: this.createMeasurement(
      /* istanbul ignore next */
      (cov_sd8kdgqk0().b[4][0]++, systemPressureResult.velocityPressure) ||
      /* istanbul ignore next */
      (cov_sd8kdgqk0().b[4][1]++, systemPressureResult.totalPressureLoss * 0.2), 'in wg', SystemAnalysisTypes_1.MeasurementSource.CALCULATED, SystemAnalysisTypes_1.QualityIndicator.HIGH, timestamp),
      totalAirflow: this.createMeasurement(airflowMetrics.totalAirflow, 'CFM', SystemAnalysisTypes_1.MeasurementSource.CALCULATED, SystemAnalysisTypes_1.QualityIndicator.HIGH, timestamp),
      designAirflow: this.createMeasurement(systemConfiguration.designParameters.designAirflow, 'CFM', SystemAnalysisTypes_1.MeasurementSource.MANUFACTURER_DATA, SystemAnalysisTypes_1.QualityIndicator.HIGH, timestamp),
      airflowEfficiency: this.createMeasurement(airflowMetrics.efficiency, '%', SystemAnalysisTypes_1.MeasurementSource.CALCULATED, SystemAnalysisTypes_1.QualityIndicator.MEDIUM, timestamp),
      // Fan Performance
      fanPower: this.createMeasurement(fanPerformance.power, 'kW', SystemAnalysisTypes_1.MeasurementSource.CALCULATED, SystemAnalysisTypes_1.QualityIndicator.HIGH, timestamp),
      fanEfficiency: this.createMeasurement(fanPerformance.efficiency, '%', SystemAnalysisTypes_1.MeasurementSource.CALCULATED, SystemAnalysisTypes_1.QualityIndicator.HIGH, timestamp),
      fanSpeed: this.createMeasurement(fanPerformance.speed, 'RPM', SystemAnalysisTypes_1.MeasurementSource.CALCULATED, SystemAnalysisTypes_1.QualityIndicator.MEDIUM, timestamp),
      fanCurvePosition: fanPerformance.curvePosition,
      // System Efficiency
      systemEfficiency: this.createMeasurement(efficiencyMetrics.systemEfficiency, '%', SystemAnalysisTypes_1.MeasurementSource.CALCULATED, SystemAnalysisTypes_1.QualityIndicator.HIGH, timestamp),
      transportEfficiency: this.createMeasurement(efficiencyMetrics.transportEfficiency, '%', SystemAnalysisTypes_1.MeasurementSource.CALCULATED, SystemAnalysisTypes_1.QualityIndicator.HIGH, timestamp),
      distributionEfficiency: this.createMeasurement(efficiencyMetrics.distributionEfficiency, '%', SystemAnalysisTypes_1.MeasurementSource.CALCULATED, SystemAnalysisTypes_1.QualityIndicator.MEDIUM, timestamp),
      // Environmental Metrics
      noiseLevel: this.createMeasurement(environmentalMetrics.noiseLevel, 'dBA', SystemAnalysisTypes_1.MeasurementSource.ESTIMATED, SystemAnalysisTypes_1.QualityIndicator.MEDIUM, timestamp),
      vibrationLevel: this.createMeasurement(environmentalMetrics.vibrationLevel, 'mm/s', SystemAnalysisTypes_1.MeasurementSource.ESTIMATED, SystemAnalysisTypes_1.QualityIndicator.LOW, timestamp),
      temperatureRise: this.createMeasurement(environmentalMetrics.temperatureRise, '°F', SystemAnalysisTypes_1.MeasurementSource.CALCULATED, SystemAnalysisTypes_1.QualityIndicator.MEDIUM, timestamp),
      // Filter and Component Performance
      filterPressureDrop: this.createMeasurement(environmentalMetrics.filterPressureDrop, 'in wg', SystemAnalysisTypes_1.MeasurementSource.ESTIMATED, SystemAnalysisTypes_1.QualityIndicator.MEDIUM, timestamp),
      coilPressureDrop: this.createMeasurement(environmentalMetrics.coilPressureDrop, 'in wg', SystemAnalysisTypes_1.MeasurementSource.ESTIMATED, SystemAnalysisTypes_1.QualityIndicator.MEDIUM, timestamp),
      dampersPosition: [],
      // Would be populated from actual system data
      // System Balance
      balanceQuality,
      flowDistribution: airflowMetrics.flowDistribution
    });
    /* istanbul ignore next */
    cov_sd8kdgqk0().s[26]++;
    return performanceMetrics;
  }
  /**
   * Create a standardized measurement object
   */
  static createMeasurement(value, units, source, quality, timestamp, accuracy =
  /* istanbul ignore next */
  (cov_sd8kdgqk0().b[5][0]++, 0.95)) {
    /* istanbul ignore next */
    cov_sd8kdgqk0().f[2]++;
    cov_sd8kdgqk0().s[27]++;
    return {
      value,
      units,
      accuracy,
      timestamp,
      source,
      qualityIndicator: quality,
      uncertaintyBounds: {
        lowerBound: value * (1 - (1 - accuracy)),
        upperBound: value * (1 + (1 - accuracy)),
        confidenceLevel: accuracy * 100,
        distributionType: 'normal'
      }
    };
  }
  /**
   * Calculate fan performance metrics
   */
  static calculateFanPerformance(systemConfiguration, systemPressure) {
    /* istanbul ignore next */
    cov_sd8kdgqk0().f[3]++;
    const designAirflow =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[28]++, systemConfiguration.designParameters.designAirflow);
    const designPressure =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[29]++, systemConfiguration.designParameters.designPressure);
    // Simplified fan performance calculation
    // In practice, this would use actual fan curves and manufacturer data
    const efficiency =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[30]++, Math.max(0.6, Math.min(0.9, 0.8 - (systemPressure - designPressure) * 0.05)));
    const power =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[31]++, designAirflow * systemPressure / (6356 * efficiency)); // HP
    const powerKW =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[32]++, power * 0.746); // Convert to kW
    const speed =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[33]++, 1800 * Math.sqrt(systemPressure / designPressure)); // Simplified speed calculation
    const curvePosition =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[34]++, {
      operatingPoint: {
        airflow: designAirflow,
        pressure: systemPressure,
        power: powerKW,
        efficiency: efficiency * 100,
        speed
      },
      designPoint: {
        airflow: designAirflow,
        pressure: designPressure,
        power: designAirflow * designPressure / (6356 * 0.8) * 0.746,
        efficiency: 80,
        speed: 1800
      },
      efficiencyAtOperating: efficiency * 100,
      efficiencyAtDesign: 80,
      surgeMargin: Math.max(0, (designAirflow * 0.7 - designAirflow) / designAirflow * 100),
      stallMargin: Math.max(0, (designAirflow * 1.3 - designAirflow) / designAirflow * 100),
      recommendedOperatingRange: {
        minAirflow: designAirflow * 0.7,
        maxAirflow: designAirflow * 1.3,
        minPressure: designPressure * 0.5,
        maxPressure: designPressure * 1.5
      }
    });
    /* istanbul ignore next */
    cov_sd8kdgqk0().s[35]++;
    return {
      power: powerKW,
      efficiency: efficiency * 100,
      speed,
      curvePosition
    };
  }
  /**
   * Calculate airflow metrics and distribution
   */
  static calculateAirflowMetrics(systemConfiguration) {
    /* istanbul ignore next */
    cov_sd8kdgqk0().f[4]++;
    const designAirflow =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[36]++, systemConfiguration.designParameters.designAirflow);
    // Simplified airflow calculation
    // In practice, this would consider actual system measurements
    const totalAirflow =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[37]++, designAirflow * 0.95); // Assume 5% leakage
    const efficiency =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[38]++, totalAirflow / designAirflow * 100);
    /* istanbul ignore next */
    cov_sd8kdgqk0().s[39]++;
    return {
      totalAirflow,
      efficiency,
      flowDistribution: {
        uniformityIndex: 0.85,
        // Simplified
        variationCoefficient: 0.15,
        zones: []
      }
    };
  }
  /**
   * Calculate system efficiency metrics
   */
  static calculateSystemEfficiencyMetrics(systemConfiguration, fanPerformance, systemPressureResult) {
    /* istanbul ignore next */
    cov_sd8kdgqk0().f[5]++;
    const designAirflow =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[40]++, systemConfiguration.designParameters.designAirflow);
    const designPressure =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[41]++, systemConfiguration.designParameters.designPressure);
    // Calculate various efficiency metrics
    const systemEfficiency =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[42]++, Math.min(95, fanPerformance.efficiency * 0.9)); // Account for system losses
    const transportEfficiency =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[43]++, Math.min(90, 100 - systemPressureResult.totalPressureLoss / designPressure * 10));
    const distributionEfficiency =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[44]++, Math.min(85, systemEfficiency * 0.9)); // Account for distribution losses
    /* istanbul ignore next */
    cov_sd8kdgqk0().s[45]++;
    return {
      systemEfficiency,
      transportEfficiency,
      distributionEfficiency
    };
  }
  /**
   * Calculate environmental metrics
   */
  static calculateEnvironmentalMetrics(systemConfiguration, fanPerformance) {
    /* istanbul ignore next */
    cov_sd8kdgqk0().f[6]++;
    const designAirflow =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[46]++, systemConfiguration.designParameters.designAirflow);
    // Simplified environmental calculations
    const velocity =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[47]++, designAirflow / 144); // Simplified velocity calculation
    const noiseLevel =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[48]++, 40 + 20 * Math.log10(velocity / 1000)); // Simplified noise model
    const vibrationLevel =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[49]++, Math.max(0.5, velocity / 2000)); // Simplified vibration model
    const temperatureRise =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[50]++, fanPerformance.power * 3412 / (designAirflow * 1.08)); // Fan heat rise
    /* istanbul ignore next */
    cov_sd8kdgqk0().s[51]++;
    return {
      noiseLevel: Math.max(35, Math.min(65, noiseLevel)),
      vibrationLevel: Math.max(0.1, Math.min(5.0, vibrationLevel)),
      temperatureRise: Math.max(0.5, Math.min(5.0, temperatureRise)),
      filterPressureDrop: 0.5,
      // Typical clean filter
      coilPressureDrop: 0.8 // Typical coil pressure drop
    };
  }
  /**
   * Assess system balance quality
   */
  static assessSystemBalance(systemConfiguration, airflowMetrics) {
    /* istanbul ignore next */
    cov_sd8kdgqk0().f[7]++;
    // Simplified balance assessment
    const flowVariation =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[52]++, 0.15); // 15% variation
    const pressureVariation =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[53]++, 0.12); // 12% variation
    const overallScore =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[54]++, Math.max(0, 100 - (flowVariation + pressureVariation) * 200));
    let balanceGrade;
    /* istanbul ignore next */
    cov_sd8kdgqk0().s[55]++;
    if (overallScore >= 90) {
      /* istanbul ignore next */
      cov_sd8kdgqk0().b[6][0]++;
      cov_sd8kdgqk0().s[56]++;
      balanceGrade = SystemAnalysisTypes_1.BalanceGrade.EXCELLENT;
    } else {
      /* istanbul ignore next */
      cov_sd8kdgqk0().b[6][1]++;
      cov_sd8kdgqk0().s[57]++;
      if (overallScore >= 80) {
        /* istanbul ignore next */
        cov_sd8kdgqk0().b[7][0]++;
        cov_sd8kdgqk0().s[58]++;
        balanceGrade = SystemAnalysisTypes_1.BalanceGrade.GOOD;
      } else {
        /* istanbul ignore next */
        cov_sd8kdgqk0().b[7][1]++;
        cov_sd8kdgqk0().s[59]++;
        if (overallScore >= 70) {
          /* istanbul ignore next */
          cov_sd8kdgqk0().b[8][0]++;
          cov_sd8kdgqk0().s[60]++;
          balanceGrade = SystemAnalysisTypes_1.BalanceGrade.ACCEPTABLE;
        } else {
          /* istanbul ignore next */
          cov_sd8kdgqk0().b[8][1]++;
          cov_sd8kdgqk0().s[61]++;
          if (overallScore >= 60) {
            /* istanbul ignore next */
            cov_sd8kdgqk0().b[9][0]++;
            cov_sd8kdgqk0().s[62]++;
            balanceGrade = SystemAnalysisTypes_1.BalanceGrade.POOR;
          } else {
            /* istanbul ignore next */
            cov_sd8kdgqk0().b[9][1]++;
            cov_sd8kdgqk0().s[63]++;
            balanceGrade = SystemAnalysisTypes_1.BalanceGrade.CRITICAL;
          }
        }
      }
    }
    /* istanbul ignore next */
    cov_sd8kdgqk0().s[64]++;
    return {
      overallScore,
      flowVariation,
      pressureVariation,
      balanceGrade,
      criticalZones: balanceGrade === SystemAnalysisTypes_1.BalanceGrade.CRITICAL ?
      /* istanbul ignore next */
      (cov_sd8kdgqk0().b[10][0]++, ['Zone 1', 'Zone 3']) :
      /* istanbul ignore next */
      (cov_sd8kdgqk0().b[10][1]++, []),
      balanceRecommendations: this.generateBalanceRecommendations(balanceGrade)
    };
  }
  /**
   * Generate balance recommendations based on grade
   */
  static generateBalanceRecommendations(grade) {
    /* istanbul ignore next */
    cov_sd8kdgqk0().f[8]++;
    cov_sd8kdgqk0().s[65]++;
    switch (grade) {
      case SystemAnalysisTypes_1.BalanceGrade.CRITICAL:
        /* istanbul ignore next */
        cov_sd8kdgqk0().b[11][0]++;
        cov_sd8kdgqk0().s[66]++;
        return ['Immediate system rebalancing required', 'Check for blocked dampers or ducts', 'Verify fan operation and capacity', 'Consider professional commissioning'];
      case SystemAnalysisTypes_1.BalanceGrade.POOR:
        /* istanbul ignore next */
        cov_sd8kdgqk0().b[11][1]++;
        cov_sd8kdgqk0().s[67]++;
        return ['System rebalancing recommended', 'Adjust damper positions', 'Check for duct leakage', 'Verify design calculations'];
      case SystemAnalysisTypes_1.BalanceGrade.ACCEPTABLE:
        /* istanbul ignore next */
        cov_sd8kdgqk0().b[11][2]++;
        cov_sd8kdgqk0().s[68]++;
        return ['Minor adjustments may improve performance', 'Monitor system performance trends', 'Consider seasonal adjustments'];
      default:
        /* istanbul ignore next */
        cov_sd8kdgqk0().b[11][3]++;
        cov_sd8kdgqk0().s[69]++;
        return ['System balance is within acceptable limits'];
    }
  }
  /**
   * Create duct segments from system configuration
   */
  static createDuctSegmentsFromConfig(systemConfiguration) {
    /* istanbul ignore next */
    cov_sd8kdgqk0().f[9]++;
    cov_sd8kdgqk0().s[70]++;
    // Simplified duct segment creation
    // In practice, this would parse the actual system configuration
    return [{
      id: 'main_supply',
      length: 100,
      diameter: 24,
      material: 'galvanized_steel',
      roughness: 0.0015,
      airflow: systemConfiguration.designParameters.designAirflow,
      fittings: []
    }];
  }
  /**
   * Generate unique analysis ID
   */
  static generateAnalysisId(systemId) {
    /* istanbul ignore next */
    cov_sd8kdgqk0().f[10]++;
    const timestamp =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[71]++, Date.now());
    const random =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[72]++, Math.random().toString(36).substring(2, 8));
    /* istanbul ignore next */
    cov_sd8kdgqk0().s[73]++;
    return `analysis_${systemId}_${timestamp}_${random}`;
  }
  /**
   * Create default trend analysis when no historical data is available
   */
  static createDefaultTrendAnalysis() {
    /* istanbul ignore next */
    cov_sd8kdgqk0().f[11]++;
    cov_sd8kdgqk0().s[74]++;
    return {
      timeRange: {
        startDate: new Date(),
        endDate: new Date(),
        duration: 0,
        units: 'days'
      },
      trendDirection: SystemAnalysisTypes_1.TrendDirection.STABLE,
      trendMagnitude: 0,
      seasonalPatterns: [],
      anomalies: [],
      predictiveAnalysis: {
        forecastHorizon: 12,
        predictedPerformance: [],
        confidenceInterval: {
          lowerBound: 0,
          upperBound: 0,
          confidenceLevel: 0
        },
        predictionModel: {
          modelType: 'linear_regression',
          accuracy: 0,
          lastTrainingDate: new Date(),
          dataPoints: 0,
          validationScore: 0
        },
        keyFactors: [],
        scenarios: []
      },
      degradationRate: {
        overallDegradationRate: 2.0,
        // 2% per year typical
        componentDegradation: [],
        degradationFactors: [],
        maintenanceImpact: {
          preventiveMaintenance: {
            performanceImpact: 5,
            lifespanImpact: 2,
            costImpact: 1000
          },
          correctiveMaintenance: {
            performanceImpact: -10,
            lifespanImpact: -1,
            costImpact: 5000
          },
          deferredMaintenance: {
            performanceImpact: -15,
            lifespanImpact: -3,
            costImpact: 10000
          }
        },
        projectedLifespan: {
          currentAge: 0,
          designLife: 20,
          projectedLife: 18,
          confidenceLevel: 80,
          keyAssumptions: ['Regular maintenance', 'Normal operating conditions']
        }
      }
    };
  }
  /**
   * Perform trend analysis on historical performance data
   */
  static async performTrendAnalysis(historicalData, currentMetrics) {
    /* istanbul ignore next */
    cov_sd8kdgqk0().f[12]++;
    cov_sd8kdgqk0().s[75]++;
    if (historicalData.length < 2) {
      /* istanbul ignore next */
      cov_sd8kdgqk0().b[12][0]++;
      cov_sd8kdgqk0().s[76]++;
      return this.createDefaultTrendAnalysis();
    } else
    /* istanbul ignore next */
    {
      cov_sd8kdgqk0().b[12][1]++;
    }
    // Calculate trend direction and magnitude
    const trendAnalysis =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[77]++, this.calculateTrendDirection(historicalData));
    // Detect seasonal patterns
    const seasonalPatterns =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[78]++, this.detectSeasonalPatterns(historicalData));
    // Detect anomalies
    const anomalies =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[79]++, this.detectAnomalies(historicalData, currentMetrics));
    // Generate predictive analysis
    const predictiveAnalysis =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[80]++, this.generatePredictiveAnalysis(historicalData));
    // Calculate degradation rate
    const degradationRate =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[81]++, this.calculateDegradationRate(historicalData));
    const timeRange =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[82]++, {
      startDate: historicalData[0].totalSystemPressure.timestamp,
      endDate: currentMetrics.totalSystemPressure.timestamp,
      duration: historicalData.length,
      units: 'days'
    });
    /* istanbul ignore next */
    cov_sd8kdgqk0().s[83]++;
    return {
      timeRange,
      trendDirection: trendAnalysis.direction,
      trendMagnitude: trendAnalysis.magnitude,
      seasonalPatterns,
      anomalies,
      predictiveAnalysis,
      degradationRate
    };
  }
  /**
   * Calculate trend direction from historical data
   */
  static calculateTrendDirection(historicalData) {
    /* istanbul ignore next */
    cov_sd8kdgqk0().f[13]++;
    cov_sd8kdgqk0().s[84]++;
    if (historicalData.length < 3) {
      /* istanbul ignore next */
      cov_sd8kdgqk0().b[13][0]++;
      cov_sd8kdgqk0().s[85]++;
      return {
        direction: SystemAnalysisTypes_1.TrendDirection.STABLE,
        magnitude: 0
      };
    } else
    /* istanbul ignore next */
    {
      cov_sd8kdgqk0().b[13][1]++;
    }
    // Simple linear regression on system efficiency
    const efficiencyValues =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[86]++, historicalData.map(data => {
      /* istanbul ignore next */
      cov_sd8kdgqk0().f[14]++;
      cov_sd8kdgqk0().s[87]++;
      return data.systemEfficiency.value;
    }));
    const n =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[88]++, efficiencyValues.length);
    const x =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[89]++, Array.from({
      length: n
    }, (_, i) => {
      /* istanbul ignore next */
      cov_sd8kdgqk0().f[15]++;
      cov_sd8kdgqk0().s[90]++;
      return i;
    }));
    const sumX =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[91]++, x.reduce((a, b) => {
      /* istanbul ignore next */
      cov_sd8kdgqk0().f[16]++;
      cov_sd8kdgqk0().s[92]++;
      return a + b;
    }, 0));
    const sumY =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[93]++, efficiencyValues.reduce((a, b) => {
      /* istanbul ignore next */
      cov_sd8kdgqk0().f[17]++;
      cov_sd8kdgqk0().s[94]++;
      return a + b;
    }, 0));
    const sumXY =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[95]++, x.reduce((sum, xi, i) => {
      /* istanbul ignore next */
      cov_sd8kdgqk0().f[18]++;
      cov_sd8kdgqk0().s[96]++;
      return sum + xi * efficiencyValues[i];
    }, 0));
    const sumXX =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[97]++, x.reduce((sum, xi) => {
      /* istanbul ignore next */
      cov_sd8kdgqk0().f[19]++;
      cov_sd8kdgqk0().s[98]++;
      return sum + xi * xi;
    }, 0));
    const slope =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[99]++, (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX));
    const magnitude =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[100]++, Math.abs(slope));
    let direction;
    /* istanbul ignore next */
    cov_sd8kdgqk0().s[101]++;
    if (magnitude < 0.1) {
      /* istanbul ignore next */
      cov_sd8kdgqk0().b[14][0]++;
      cov_sd8kdgqk0().s[102]++;
      direction = SystemAnalysisTypes_1.TrendDirection.STABLE;
    } else {
      /* istanbul ignore next */
      cov_sd8kdgqk0().b[14][1]++;
      cov_sd8kdgqk0().s[103]++;
      if (slope > 0) {
        /* istanbul ignore next */
        cov_sd8kdgqk0().b[15][0]++;
        cov_sd8kdgqk0().s[104]++;
        direction = SystemAnalysisTypes_1.TrendDirection.IMPROVING;
      } else {
        /* istanbul ignore next */
        cov_sd8kdgqk0().b[15][1]++;
        cov_sd8kdgqk0().s[105]++;
        direction = SystemAnalysisTypes_1.TrendDirection.DEGRADING;
      }
    }
    /* istanbul ignore next */
    cov_sd8kdgqk0().s[106]++;
    return {
      direction,
      magnitude
    };
  }
  /**
   * Detect seasonal patterns in performance data
   */
  static detectSeasonalPatterns(historicalData) {
    /* istanbul ignore next */
    cov_sd8kdgqk0().f[20]++;
    cov_sd8kdgqk0().s[107]++;
    // Simplified seasonal pattern detection
    // In practice, this would use more sophisticated time series analysis
    return [{
      season: 'summer',
      averagePerformance: 85,
      performanceVariation: 5,
      typicalIssues: ['Higher cooling loads', 'Increased fan power']
    }, {
      season: 'winter',
      averagePerformance: 88,
      performanceVariation: 3,
      typicalIssues: ['Filter loading', 'Heating coil pressure drop']
    }];
  }
  /**
   * Detect performance anomalies
   */
  static detectAnomalies(historicalData, currentMetrics) {
    /* istanbul ignore next */
    cov_sd8kdgqk0().f[21]++;
    const anomalies =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[108]++, []);
    // Check for sudden efficiency drop
    /* istanbul ignore next */
    cov_sd8kdgqk0().s[109]++;
    if (historicalData.length > 0) {
      /* istanbul ignore next */
      cov_sd8kdgqk0().b[16][0]++;
      const lastEfficiency =
      /* istanbul ignore next */
      (cov_sd8kdgqk0().s[110]++, historicalData[historicalData.length - 1].systemEfficiency.value);
      const currentEfficiency =
      /* istanbul ignore next */
      (cov_sd8kdgqk0().s[111]++, currentMetrics.systemEfficiency.value);
      const efficiencyDrop =
      /* istanbul ignore next */
      (cov_sd8kdgqk0().s[112]++, lastEfficiency - currentEfficiency);
      /* istanbul ignore next */
      cov_sd8kdgqk0().s[113]++;
      if (efficiencyDrop > 5) {
        /* istanbul ignore next */
        cov_sd8kdgqk0().b[17][0]++;
        cov_sd8kdgqk0().s[114]++;
        // 5% drop threshold
        anomalies.push({
          id: `anomaly_${Date.now()}`,
          detectionTimestamp: new Date(),
          anomalyType: SystemAnalysisTypes_1.AnomalyType.SUDDEN_CHANGE,
          severity: efficiencyDrop > 10 ?
          /* istanbul ignore next */
          (cov_sd8kdgqk0().b[18][0]++, 'critical') :
          /* istanbul ignore next */
          (cov_sd8kdgqk0().b[18][1]++, 'high'),
          affectedMetrics: ['systemEfficiency'],
          deviationMagnitude: efficiencyDrop,
          duration: 1,
          possibleCauses: [{
            cause: 'Filter blockage',
            probability: 60,
            category: 'maintenance_issue',
            diagnosticSteps: ['Check filter pressure drop', 'Inspect filter condition']
          }, {
            cause: 'Fan belt slippage',
            probability: 30,
            category: 'equipment_failure',
            diagnosticSteps: ['Check fan belt tension', 'Inspect motor operation']
          }],
          recommendedActions: ['Replace or clean filters', 'Check fan operation', 'Verify damper positions'],
          resolved: false
        });
      } else
      /* istanbul ignore next */
      {
        cov_sd8kdgqk0().b[17][1]++;
      }
    } else
    /* istanbul ignore next */
    {
      cov_sd8kdgqk0().b[16][1]++;
    }
    cov_sd8kdgqk0().s[115]++;
    return anomalies;
  }
  /**
   * Generate predictive analysis
   */
  static generatePredictiveAnalysis(historicalData) {
    /* istanbul ignore next */
    cov_sd8kdgqk0().f[22]++;
    cov_sd8kdgqk0().s[116]++;
    // Simplified predictive analysis
    // In practice, this would use machine learning models
    return {
      forecastHorizon: 12,
      predictedPerformance: [{
        metric: 'systemEfficiency',
        currentValue: 85,
        predictedValue: 83,
        changePercent: -2.4,
        timeToTarget: 6
      }],
      confidenceInterval: {
        lowerBound: 80,
        upperBound: 86,
        confidenceLevel: 85
      },
      predictionModel: {
        modelType: 'time_series',
        accuracy: 85,
        lastTrainingDate: new Date(),
        dataPoints: historicalData.length,
        validationScore: 0.85
      },
      keyFactors: [{
        factor: 'Filter condition',
        impact: 40,
        controllable: true,
        mitigationStrategies: ['Regular filter replacement', 'Pressure monitoring']
      }],
      scenarios: []
    };
  }
  /**
   * Calculate system degradation rate
   */
  static calculateDegradationRate(historicalData) {
    /* istanbul ignore next */
    cov_sd8kdgqk0().f[23]++;
    cov_sd8kdgqk0().s[117]++;
    // Simplified degradation calculation
    return {
      overallDegradationRate: 2.5,
      // 2.5% per year
      componentDegradation: [{
        componentId: 'main_fan',
        componentType: 'fan',
        degradationRate: 1.5,
        currentCondition: 92,
        estimatedRemainingLife: 12,
        replacementThreshold: 70
      }],
      degradationFactors: [{
        factor: 'Operating hours',
        impact: 50,
        controllable: false,
        mitigationStrategies: ['Optimize operating schedule']
      }, {
        factor: 'Maintenance quality',
        impact: 30,
        controllable: true,
        mitigationStrategies: ['Preventive maintenance program', 'Staff training']
      }],
      maintenanceImpact: {
        preventiveMaintenance: {
          performanceImpact: 5,
          lifespanImpact: 2,
          costImpact: 1000
        },
        correctiveMaintenance: {
          performanceImpact: -10,
          lifespanImpact: -1,
          costImpact: 5000
        },
        deferredMaintenance: {
          performanceImpact: -15,
          lifespanImpact: -3,
          costImpact: 10000
        }
      },
      projectedLifespan: {
        currentAge: 3,
        designLife: 20,
        projectedLife: 18,
        confidenceLevel: 80,
        keyAssumptions: ['Regular maintenance', 'Normal operating conditions']
      }
    };
  }
  /**
   * Perform benchmark comparison against similar systems
   */
  static async performBenchmarkComparison(systemConfiguration, performanceMetrics) {
    /* istanbul ignore next */
    cov_sd8kdgqk0().f[24]++;
    // Simplified benchmark comparison
    // In practice, this would query a database of similar systems
    const systemEfficiency =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[118]++, performanceMetrics.systemEfficiency.value);
    const industryAverage =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[119]++, 82); // Typical industry average
    const bestInClass =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[120]++, 92); // Best in class performance
    /* istanbul ignore next */
    cov_sd8kdgqk0().s[121]++;
    return {
      benchmarkType: SystemAnalysisTypes_1.BenchmarkType.INDUSTRY_AVERAGE,
      benchmarkSource: 'ASHRAE Performance Database',
      systemPerformance: systemEfficiency,
      benchmarkValue: industryAverage,
      percentile: this.calculatePercentile(systemEfficiency, industryAverage),
      performanceGap: industryAverage - systemEfficiency,
      improvementPotential: bestInClass - systemEfficiency,
      similarSystems: [{
        systemId: 'similar_system_1',
        systemName: 'Office Building HVAC',
        performanceMetric: 84,
        systemCharacteristics: {
          size: 'medium',
          age: 5,
          buildingType: 'office',
          climateZone: '4A',
          operatingHours: 2500
        },
        performanceDifference: 84 - systemEfficiency
      }]
    };
  }
  /**
   * Calculate percentile ranking
   */
  static calculatePercentile(value, average) {
    /* istanbul ignore next */
    cov_sd8kdgqk0().f[25]++;
    // Simplified percentile calculation
    // Assumes normal distribution with standard deviation of 8
    const standardDeviation =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[122]++, 8);
    const zScore =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[123]++, (value - average) / standardDeviation);
    // Convert z-score to percentile (simplified)
    /* istanbul ignore next */
    cov_sd8kdgqk0().s[124]++;
    if (zScore >= 2) {
      /* istanbul ignore next */
      cov_sd8kdgqk0().b[19][0]++;
      cov_sd8kdgqk0().s[125]++;
      return 97;
    } else
    /* istanbul ignore next */
    {
      cov_sd8kdgqk0().b[19][1]++;
    }
    cov_sd8kdgqk0().s[126]++;
    if (zScore >= 1) {
      /* istanbul ignore next */
      cov_sd8kdgqk0().b[20][0]++;
      cov_sd8kdgqk0().s[127]++;
      return 84;
    } else
    /* istanbul ignore next */
    {
      cov_sd8kdgqk0().b[20][1]++;
    }
    cov_sd8kdgqk0().s[128]++;
    if (zScore >= 0) {
      /* istanbul ignore next */
      cov_sd8kdgqk0().b[21][0]++;
      cov_sd8kdgqk0().s[129]++;
      return 50 + zScore * 34;
    } else
    /* istanbul ignore next */
    {
      cov_sd8kdgqk0().b[21][1]++;
    }
    cov_sd8kdgqk0().s[130]++;
    if (zScore >= -1) {
      /* istanbul ignore next */
      cov_sd8kdgqk0().b[22][0]++;
      cov_sd8kdgqk0().s[131]++;
      return 50 + zScore * 34;
    } else
    /* istanbul ignore next */
    {
      cov_sd8kdgqk0().b[22][1]++;
    }
    cov_sd8kdgqk0().s[132]++;
    if (zScore >= -2) {
      /* istanbul ignore next */
      cov_sd8kdgqk0().b[23][0]++;
      cov_sd8kdgqk0().s[133]++;
      return 16;
    } else
    /* istanbul ignore next */
    {
      cov_sd8kdgqk0().b[23][1]++;
    }
    cov_sd8kdgqk0().s[134]++;
    return 3;
  }
  /**
   * Analyze system efficiency in detail
   */
  static async analyzeSystemEfficiency(systemConfiguration, performanceMetrics) {
    /* istanbul ignore next */
    cov_sd8kdgqk0().f[26]++;
    cov_sd8kdgqk0().s[135]++;
    // Simplified efficiency analysis
    return {
      overallEfficiency: {
        value: performanceMetrics.systemEfficiency.value,
        units: '%',
        calculationMethod: 'calculated',
        accuracy: 0.9,
        timestamp: new Date()
      },
      componentEfficiencies: [{
        componentId: 'main_fan',
        componentType: 'fan',
        efficiency: performanceMetrics.fanEfficiency.value,
        ratedEfficiency: 85,
        degradationFactor: 0.95,
        maintenanceStatus: 'good'
      }],
      efficiencyTrends: [],
      efficiencyLosses: [],
      improvementOpportunities: [],
      benchmarkComparison: {
        benchmarkType: SystemAnalysisTypes_1.BenchmarkType.INDUSTRY_AVERAGE,
        benchmarkSource: 'Industry Standards',
        systemPerformance: performanceMetrics.systemEfficiency.value,
        benchmarkValue: 82,
        percentile: 65,
        performanceGap: 0,
        improvementPotential: 10,
        similarSystems: []
      }
    };
  }
  /**
   * Detect performance alerts and warnings
   */
  static async detectPerformanceAlerts(performanceMetrics, historicalData) {
    /* istanbul ignore next */
    cov_sd8kdgqk0().f[27]++;
    const alerts =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[136]++, []);
    // Check efficiency threshold
    /* istanbul ignore next */
    cov_sd8kdgqk0().s[137]++;
    if (performanceMetrics.systemEfficiency.value < 75) {
      /* istanbul ignore next */
      cov_sd8kdgqk0().b[24][0]++;
      cov_sd8kdgqk0().s[138]++;
      alerts.push({
        id: `alert_${Date.now()}_efficiency`,
        alertType: SystemAnalysisTypes_1.AlertType.THRESHOLD_EXCEEDED,
        severity: SystemAnalysisTypes_1.AlertSeverity.HIGH,
        metric: 'systemEfficiency',
        currentValue: performanceMetrics.systemEfficiency.value,
        thresholdValue: 75,
        message: 'System efficiency below acceptable threshold',
        timestamp: new Date(),
        acknowledged: false,
        recommendedActions: ['Check filter condition', 'Verify fan operation', 'Inspect ductwork for leaks']
      });
    } else
    /* istanbul ignore next */
    {
      cov_sd8kdgqk0().b[24][1]++;
    }
    // Check pressure threshold
    cov_sd8kdgqk0().s[139]++;
    if (performanceMetrics.totalSystemPressure.value > 4.0) {
      /* istanbul ignore next */
      cov_sd8kdgqk0().b[25][0]++;
      cov_sd8kdgqk0().s[140]++;
      alerts.push({
        id: `alert_${Date.now()}_pressure`,
        alertType: SystemAnalysisTypes_1.AlertType.THRESHOLD_EXCEEDED,
        severity: SystemAnalysisTypes_1.AlertSeverity.MEDIUM,
        metric: 'totalSystemPressure',
        currentValue: performanceMetrics.totalSystemPressure.value,
        thresholdValue: 4.0,
        message: 'System pressure higher than expected',
        timestamp: new Date(),
        acknowledged: false,
        recommendedActions: ['Check for blocked ducts', 'Verify damper positions', 'Inspect filters']
      });
    } else
    /* istanbul ignore next */
    {
      cov_sd8kdgqk0().b[25][1]++;
    }
    cov_sd8kdgqk0().s[141]++;
    return alerts;
  }
  /**
   * Generate performance recommendations
   */
  static async generatePerformanceRecommendations(performanceMetrics, efficiencyAnalysis, benchmarkComparison) {
    /* istanbul ignore next */
    cov_sd8kdgqk0().f[28]++;
    const recommendations =
    /* istanbul ignore next */
    (cov_sd8kdgqk0().s[142]++, []);
    // Efficiency improvement recommendation
    /* istanbul ignore next */
    cov_sd8kdgqk0().s[143]++;
    if (performanceMetrics.systemEfficiency.value < 85) {
      /* istanbul ignore next */
      cov_sd8kdgqk0().b[26][0]++;
      cov_sd8kdgqk0().s[144]++;
      recommendations.push({
        id: `rec_${Date.now()}_efficiency`,
        type: SystemAnalysisTypes_1.RecommendationType.OPTIMIZATION,
        priority: SystemAnalysisTypes_1.RecommendationPriority.HIGH,
        title: 'Improve System Efficiency',
        description: 'System efficiency is below optimal levels. Consider implementing efficiency improvements.',
        expectedImpact: {
          energySavings: 15,
          costSavings: 2500,
          performanceImprovement: 10,
          emissionReduction: 1200,
          reliabilityImprovement: 5
        },
        implementationCost: 5000,
        paybackPeriod: 24,
        implementationComplexity: 'moderate',
        requiredActions: ['Replace filters with high-efficiency models', 'Seal ductwork leaks', 'Optimize fan speed control'],
        timeline: '2-4 weeks'
      });
    } else
    /* istanbul ignore next */
    {
      cov_sd8kdgqk0().b[26][1]++;
    }
    cov_sd8kdgqk0().s[145]++;
    return recommendations;
  }
  /**
   * Perform uncertainty analysis on performance metrics
   */
  static async performUncertaintyAnalysis(performanceMetrics, systemConfiguration) {
    /* istanbul ignore next */
    cov_sd8kdgqk0().f[29]++;
    cov_sd8kdgqk0().s[146]++;
    // Simplified uncertainty analysis
    return {
      overallUncertainty: 0.1,
      // 10% overall uncertainty
      metricUncertainties: [{
        metric: 'systemEfficiency',
        uncertainty: 0.05,
        sources: ['measurement error', 'calculation assumptions'],
        confidenceLevel: 90
      }],
      sensitivityAnalysis: {
        parameters: [{
          parameter: 'airflow',
          sensitivity: 0.8,
          impact: 'high'
        }],
        keyDrivers: ['airflow', 'pressure'],
        uncertaintyContributors: ['measurement accuracy', 'model assumptions']
      },
      recommendations: ['Improve measurement accuracy', 'Calibrate sensors regularly', 'Validate calculation models']
    };
  }
}
/* istanbul ignore next */
cov_sd8kdgqk0().s[147]++;
exports.SystemPerformanceAnalysisEngine = SystemPerformanceAnalysisEngine;
/* istanbul ignore next */
cov_sd8kdgqk0().s[148]++;
SystemPerformanceAnalysisEngine.VERSION = '3.0.0';
/* istanbul ignore next */
cov_sd8kdgqk0().s[149]++;
SystemPerformanceAnalysisEngine.ANALYSIS_CACHE = new Map();
/* istanbul ignore next */
cov_sd8kdgqk0().s[150]++;
SystemPerformanceAnalysisEngine.BENCHMARK_DATABASE = new Map();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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