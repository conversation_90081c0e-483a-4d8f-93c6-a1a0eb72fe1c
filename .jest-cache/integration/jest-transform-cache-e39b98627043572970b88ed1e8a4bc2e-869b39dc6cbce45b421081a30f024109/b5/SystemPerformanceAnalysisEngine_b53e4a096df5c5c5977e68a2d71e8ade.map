{"version": 3, "names": ["cov_sd8kdgqk0", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "name", "decl", "loc", "branchMap", "type", "locations", "undefined", "s", "f", "b", "inputSourceMap", "file", "mappings", "names", "sources", "sourcesContent", "version", "_coverageSchema", "coverage", "actualCoverage", "SystemAnalysisTypes_1", "require", "SystemPressureCalculator_1", "SystemPerformanceAnalysisEngine", "analyzeSystemPerformance", "systemConfiguration", "analysisScope", "historicalData", "analysisId", "generateAnalysisId", "id", "timestamp", "Date", "performanceMetrics", "calculatePerformanceMetrics", "trendAnalysis", "length", "performTrendAnalysis", "createDefaultTrendAnalysis", "benchmarkComparison", "performBenchmarkComparison", "efficiencyAnalysis", "analyzeSystemEfficiency", "alertsAndWarnings", "detectPerformanceAlerts", "recommendations", "generatePerformanceRecommendations", "uncertaintyAnalysis", "performUncertaintyAnalysis", "analysis", "systemId", "analysisTimestamp", "ANALYSIS_CACHE", "set", "error", "Error", "message", "systemPressureResult", "SystemPressureCalculator", "calculateEnhancedSystemPressure", "segments", "createDuctSegmentsFromConfig", "systemType", "designConditions", "operatingConditions", "fanPerformance", "calculateFanPerformance", "totalPressureLoss", "airflowMetrics", "calculateAirflowMetrics", "efficiencyMetrics", "calculateSystemEfficiencyMetrics", "environmentalMetrics", "calculateEnvironmentalMetrics", "balanceQuality", "assessSystemBalance", "totalSystemPressure", "createMeasurement", "MeasurementSource", "CALCULATED", "QualityIndicator", "HIGH", "staticPressure", "velocityPressure", "totalAirflow", "designAirflow", "designParameters", "MANUFACTURER_DATA", "airflowEfficiency", "efficiency", "MEDIUM", "fan<PERSON>ower", "power", "fanEfficiency", "fanSpeed", "speed", "fanCurvePosition", "curvePosition", "systemEfficiency", "transportEfficiency", "distributionEfficiency", "noiseLevel", "ESTIMATED", "vibrationLevel", "LOW", "temperatureRise", "filterPressureDrop", "coilPressureDrop", "dampersPosition", "flowDistribution", "value", "units", "source", "quality", "accuracy", "qualityIndicator", "uncertaintyBounds", "lowerBound", "upperBound", "confidenceLevel", "distributionType", "systemPressure", "designPressure", "Math", "max", "min", "powerKW", "sqrt", "operatingPoint", "airflow", "pressure", "designPoint", "efficiencyAtOperating", "efficiencyAtDesign", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "recommendedOperatingRange", "minAirflow", "maxAirflow", "minPressure", "maxPressure", "uniformityIndex", "variationCoefficient", "zones", "velocity", "log10", "flowVariation", "pressureVariation", "overallScore", "balanceGrade", "BalanceGrade", "EXCELLENT", "GOOD", "ACCEPTABLE", "POOR", "CRITICAL", "criticalZones", "balanceRecommendations", "generateBalanceRecommendations", "grade", "diameter", "material", "roughness", "fittings", "now", "random", "toString", "substring", "timeRange", "startDate", "endDate", "duration", "trendDirection", "TrendDirection", "STABLE", "trendMagnitude", "seasonalPatterns", "anomalies", "predictiveAnalysis", "forecastHorizon", "predictedPerformance", "confidenceInterval", "predictionModel", "modelType", "lastTrainingDate", "dataPoints", "validationScore", "keyFactors", "scenarios", "degradationRate", "overallDegradationRate", "componentDegradation", "degradationFactors", "maintenanceImpact", "preventiveMaintenance", "performanceImpact", "lifespanImpact", "costImpact", "correctiveMaintenance", "deferredMaintenance", "projectedLifespan", "currentAge", "designLife", "projectedLife", "keyAssumptions", "currentMetrics", "calculateTrendDirection", "detectSeasonalPatterns", "detectAnomalies", "generatePredictiveAnalysis", "calculateDegradationRate", "direction", "magnitude", "efficiencyValues", "map", "data", "n", "x", "Array", "from", "_", "i", "sumX", "reduce", "a", "sumY", "sumXY", "sum", "xi", "sumXX", "slope", "abs", "IMPROVING", "DEGRADING", "season", "averagePerformance", "performanceVariation", "typicalIssues", "lastEfficiency", "currentEfficiency", "efficiencyDrop", "push", "detectionTimestamp", "anomalyType", "AnomalyType", "SUDDEN_CHANGE", "severity", "affectedMetrics", "deviationMagnitude", "<PERSON><PERSON><PERSON><PERSON>", "cause", "probability", "category", "diagnosticSteps", "recommendedActions", "resolved", "metric", "currentValue", "predictedValue", "changePercent", "timeT<PERSON><PERSON><PERSON><PERSON>", "factor", "impact", "controllable", "mitigationStrategies", "componentId", "componentType", "currentCondition", "estimatedRemainingLife", "replacementT<PERSON><PERSON>old", "industryAverage", "bestInClass", "benchmarkType", "BenchmarkType", "INDUSTRY_AVERAGE", "benchmarkSource", "systemPerformance", "benchmarkValue", "percentile", "calculatePercentile", "performanceGap", "improvementPotential", "similarSystems", "systemName", "performanceMetric", "systemCharacteristics", "size", "age", "buildingType", "climateZone", "operatingHours", "performanceDifference", "average", "standardDeviation", "zScore", "overallEfficiency", "calculationMethod", "componentEfficiencies", "ratedEfficiency", "degradationFactor", "maintenanceStatus", "efficiencyTrends", "efficiencyLosses", "improvementOpportunities", "alerts", "alertType", "AlertType", "THRESHOLD_EXCEEDED", "Alert<PERSON>everity", "thresholdValue", "acknowledged", "RecommendationType", "OPTIMIZATION", "priority", "RecommendationPriority", "title", "description", "expectedImpact", "energySavings", "costSavings", "performanceImprovement", "emissionReduction", "reliabilityImprovement", "implementationCost", "paybackPeriod", "implementationComplexity", "requiredActions", "timeline", "overallUncertainty", "metricUncertainties", "uncertainty", "sensitivityAnalysis", "parameters", "parameter", "sensitivity", "keyDrivers", "uncertaintyContributors", "exports", "VERSION", "Map", "BENCHMARK_DATABASE"], "sources": ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\SystemPerformanceAnalysisEngine.ts"], "sourcesContent": ["/**\r\n * System Performance Analysis Engine\r\n * \r\n * Comprehensive performance analysis service for Phase 3 Priority 3: Advanced System Analysis Tools\r\n * Provides real-time monitoring, trend analysis, efficiency calculations, and performance benchmarking\r\n * capabilities for HVAC duct systems.\r\n * \r\n * @version 3.0.0\r\n * <AUTHOR> Suite Development Team\r\n */\r\n\r\nimport {\r\n  SystemAnalysis,\r\n  PerformanceAnalysis,\r\n  PerformanceMetrics,\r\n  TrendAnalysis,\r\n  BenchmarkComparison,\r\n  EfficiencyAnalysis,\r\n  PerformanceAlert,\r\n  PerformanceRecommendation,\r\n  UncertaintyAnalysis,\r\n  SystemConfiguration,\r\n  AnalysisType,\r\n  AnalysisScope,\r\n  Measurement,\r\n  MeasurementSource,\r\n  QualityIndicator,\r\n  FanCurvePosition,\r\n  BalanceQuality,\r\n  BalanceGrade,\r\n  TrendDirection,\r\n  AlertType,\r\n  AlertSeverity,\r\n  RecommendationType,\r\n  RecommendationPriority,\r\n  BenchmarkType,\r\n  PerformanceAnomaly,\r\n  AnomalyType,\r\n  PredictiveAnalysis\r\n} from './types/SystemAnalysisTypes';\r\n\r\nimport { SystemPressureCalculator } from './SystemPressureCalculator';\r\nimport { FittingLossCalculator } from './FittingLossCalculator';\r\nimport { AdvancedFittingCalculator } from './AdvancedFittingCalculator';\r\nimport { AirPropertiesCalculator } from './AirPropertiesCalculator';\r\n\r\n/**\r\n * Main System Performance Analysis Engine\r\n * \r\n * Provides comprehensive performance analysis capabilities including:\r\n * - Real-time performance monitoring\r\n * - Trend analysis and forecasting\r\n * - Efficiency calculations and benchmarking\r\n * - Anomaly detection and alerting\r\n * - Performance recommendations\r\n */\r\nexport class SystemPerformanceAnalysisEngine {\r\n  private static readonly VERSION = '3.0.0';\r\n  private static readonly ANALYSIS_CACHE = new Map<string, PerformanceAnalysis>();\r\n  private static readonly BENCHMARK_DATABASE = new Map<string, BenchmarkComparison>();\r\n\r\n  /**\r\n   * Perform comprehensive system performance analysis\r\n   */\r\n  public static async analyzeSystemPerformance(\r\n    systemConfiguration: SystemConfiguration,\r\n    analysisScope: AnalysisScope,\r\n    historicalData?: PerformanceMetrics[]\r\n  ): Promise<PerformanceAnalysis> {\r\n    try {\r\n      const analysisId = this.generateAnalysisId(systemConfiguration.id);\r\n      const timestamp = new Date();\r\n\r\n      // Calculate current performance metrics\r\n      const performanceMetrics = await this.calculatePerformanceMetrics(\r\n        systemConfiguration,\r\n        analysisScope\r\n      );\r\n\r\n      // Perform trend analysis if historical data is available\r\n      const trendAnalysis = historicalData && historicalData.length > 0\r\n        ? await this.performTrendAnalysis(historicalData, performanceMetrics)\r\n        : this.createDefaultTrendAnalysis();\r\n\r\n      // Benchmark against similar systems\r\n      const benchmarkComparison = await this.performBenchmarkComparison(\r\n        systemConfiguration,\r\n        performanceMetrics\r\n      );\r\n\r\n      // Analyze system efficiency\r\n      const efficiencyAnalysis = await this.analyzeSystemEfficiency(\r\n        systemConfiguration,\r\n        performanceMetrics\r\n      );\r\n\r\n      // Detect performance alerts and anomalies\r\n      const alertsAndWarnings = await this.detectPerformanceAlerts(\r\n        performanceMetrics,\r\n        historicalData\r\n      );\r\n\r\n      // Generate performance recommendations\r\n      const recommendations = await this.generatePerformanceRecommendations(\r\n        performanceMetrics,\r\n        efficiencyAnalysis,\r\n        benchmarkComparison\r\n      );\r\n\r\n      // Perform uncertainty analysis\r\n      const uncertaintyAnalysis = await this.performUncertaintyAnalysis(\r\n        performanceMetrics,\r\n        systemConfiguration\r\n      );\r\n\r\n      const analysis: PerformanceAnalysis = {\r\n        id: analysisId,\r\n        systemId: systemConfiguration.id,\r\n        analysisTimestamp: timestamp,\r\n        performanceMetrics,\r\n        trendAnalysis,\r\n        benchmarkComparison,\r\n        efficiencyAnalysis,\r\n        alertsAndWarnings,\r\n        recommendations,\r\n        uncertaintyAnalysis\r\n      };\r\n\r\n      // Cache the analysis for future reference\r\n      this.ANALYSIS_CACHE.set(analysisId, analysis);\r\n\r\n      return analysis;\r\n\r\n    } catch (error) {\r\n      throw new Error(`System performance analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Calculate comprehensive performance metrics for the system\r\n   */\r\n  private static async calculatePerformanceMetrics(\r\n    systemConfiguration: SystemConfiguration,\r\n    analysisScope: AnalysisScope\r\n  ): Promise<PerformanceMetrics> {\r\n    const timestamp = new Date();\r\n\r\n    // Calculate system pressure using existing calculators\r\n    const systemPressureResult = SystemPressureCalculator.calculateEnhancedSystemPressure({\r\n      segments: this.createDuctSegmentsFromConfig(systemConfiguration),\r\n      systemType: systemConfiguration.systemType,\r\n      designConditions: systemConfiguration.operatingConditions\r\n    });\r\n\r\n    // Calculate fan performance metrics\r\n    const fanPerformance = this.calculateFanPerformance(\r\n      systemConfiguration,\r\n      systemPressureResult.totalPressureLoss\r\n    );\r\n\r\n    // Calculate airflow metrics\r\n    const airflowMetrics = this.calculateAirflowMetrics(systemConfiguration);\r\n\r\n    // Calculate system efficiency metrics\r\n    const efficiencyMetrics = this.calculateSystemEfficiencyMetrics(\r\n      systemConfiguration,\r\n      fanPerformance,\r\n      systemPressureResult\r\n    );\r\n\r\n    // Calculate environmental metrics\r\n    const environmentalMetrics = this.calculateEnvironmentalMetrics(\r\n      systemConfiguration,\r\n      fanPerformance\r\n    );\r\n\r\n    // Calculate system balance quality\r\n    const balanceQuality = this.assessSystemBalance(\r\n      systemConfiguration,\r\n      airflowMetrics\r\n    );\r\n\r\n    const performanceMetrics: PerformanceMetrics = {\r\n      // Pressure and Flow Metrics\r\n      totalSystemPressure: this.createMeasurement(\r\n        systemPressureResult.totalPressureLoss,\r\n        'in wg',\r\n        MeasurementSource.CALCULATED,\r\n        QualityIndicator.HIGH,\r\n        timestamp\r\n      ),\r\n      staticPressure: this.createMeasurement(\r\n        systemPressureResult.staticPressure || systemPressureResult.totalPressureLoss * 0.8,\r\n        'in wg',\r\n        MeasurementSource.CALCULATED,\r\n        QualityIndicator.HIGH,\r\n        timestamp\r\n      ),\r\n      velocityPressure: this.createMeasurement(\r\n        systemPressureResult.velocityPressure || systemPressureResult.totalPressureLoss * 0.2,\r\n        'in wg',\r\n        MeasurementSource.CALCULATED,\r\n        QualityIndicator.HIGH,\r\n        timestamp\r\n      ),\r\n      totalAirflow: this.createMeasurement(\r\n        airflowMetrics.totalAirflow,\r\n        'CFM',\r\n        MeasurementSource.CALCULATED,\r\n        QualityIndicator.HIGH,\r\n        timestamp\r\n      ),\r\n      designAirflow: this.createMeasurement(\r\n        systemConfiguration.designParameters.designAirflow,\r\n        'CFM',\r\n        MeasurementSource.MANUFACTURER_DATA,\r\n        QualityIndicator.HIGH,\r\n        timestamp\r\n      ),\r\n      airflowEfficiency: this.createMeasurement(\r\n        airflowMetrics.efficiency,\r\n        '%',\r\n        MeasurementSource.CALCULATED,\r\n        QualityIndicator.MEDIUM,\r\n        timestamp\r\n      ),\r\n\r\n      // Fan Performance\r\n      fanPower: this.createMeasurement(\r\n        fanPerformance.power,\r\n        'kW',\r\n        MeasurementSource.CALCULATED,\r\n        QualityIndicator.HIGH,\r\n        timestamp\r\n      ),\r\n      fanEfficiency: this.createMeasurement(\r\n        fanPerformance.efficiency,\r\n        '%',\r\n        MeasurementSource.CALCULATED,\r\n        QualityIndicator.HIGH,\r\n        timestamp\r\n      ),\r\n      fanSpeed: this.createMeasurement(\r\n        fanPerformance.speed,\r\n        'RPM',\r\n        MeasurementSource.CALCULATED,\r\n        QualityIndicator.MEDIUM,\r\n        timestamp\r\n      ),\r\n      fanCurvePosition: fanPerformance.curvePosition,\r\n\r\n      // System Efficiency\r\n      systemEfficiency: this.createMeasurement(\r\n        efficiencyMetrics.systemEfficiency,\r\n        '%',\r\n        MeasurementSource.CALCULATED,\r\n        QualityIndicator.HIGH,\r\n        timestamp\r\n      ),\r\n      transportEfficiency: this.createMeasurement(\r\n        efficiencyMetrics.transportEfficiency,\r\n        '%',\r\n        MeasurementSource.CALCULATED,\r\n        QualityIndicator.HIGH,\r\n        timestamp\r\n      ),\r\n      distributionEfficiency: this.createMeasurement(\r\n        efficiencyMetrics.distributionEfficiency,\r\n        '%',\r\n        MeasurementSource.CALCULATED,\r\n        QualityIndicator.MEDIUM,\r\n        timestamp\r\n      ),\r\n\r\n      // Environmental Metrics\r\n      noiseLevel: this.createMeasurement(\r\n        environmentalMetrics.noiseLevel,\r\n        'dBA',\r\n        MeasurementSource.ESTIMATED,\r\n        QualityIndicator.MEDIUM,\r\n        timestamp\r\n      ),\r\n      vibrationLevel: this.createMeasurement(\r\n        environmentalMetrics.vibrationLevel,\r\n        'mm/s',\r\n        MeasurementSource.ESTIMATED,\r\n        QualityIndicator.LOW,\r\n        timestamp\r\n      ),\r\n      temperatureRise: this.createMeasurement(\r\n        environmentalMetrics.temperatureRise,\r\n        '°F',\r\n        MeasurementSource.CALCULATED,\r\n        QualityIndicator.MEDIUM,\r\n        timestamp\r\n      ),\r\n\r\n      // Filter and Component Performance\r\n      filterPressureDrop: this.createMeasurement(\r\n        environmentalMetrics.filterPressureDrop,\r\n        'in wg',\r\n        MeasurementSource.ESTIMATED,\r\n        QualityIndicator.MEDIUM,\r\n        timestamp\r\n      ),\r\n      coilPressureDrop: this.createMeasurement(\r\n        environmentalMetrics.coilPressureDrop,\r\n        'in wg',\r\n        MeasurementSource.ESTIMATED,\r\n        QualityIndicator.MEDIUM,\r\n        timestamp\r\n      ),\r\n      dampersPosition: [], // Would be populated from actual system data\r\n\r\n      // System Balance\r\n      balanceQuality,\r\n      flowDistribution: airflowMetrics.flowDistribution\r\n    };\r\n\r\n    return performanceMetrics;\r\n  }\r\n\r\n  /**\r\n   * Create a standardized measurement object\r\n   */\r\n  private static createMeasurement(\r\n    value: number,\r\n    units: string,\r\n    source: MeasurementSource,\r\n    quality: QualityIndicator,\r\n    timestamp: Date,\r\n    accuracy: number = 0.95\r\n  ): Measurement {\r\n    return {\r\n      value,\r\n      units,\r\n      accuracy,\r\n      timestamp,\r\n      source,\r\n      qualityIndicator: quality,\r\n      uncertaintyBounds: {\r\n        lowerBound: value * (1 - (1 - accuracy)),\r\n        upperBound: value * (1 + (1 - accuracy)),\r\n        confidenceLevel: accuracy * 100,\r\n        distributionType: 'normal' as const\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate fan performance metrics\r\n   */\r\n  private static calculateFanPerformance(\r\n    systemConfiguration: SystemConfiguration,\r\n    systemPressure: number\r\n  ): any {\r\n    const designAirflow = systemConfiguration.designParameters.designAirflow;\r\n    const designPressure = systemConfiguration.designParameters.designPressure;\r\n\r\n    // Simplified fan performance calculation\r\n    // In practice, this would use actual fan curves and manufacturer data\r\n    const efficiency = Math.max(0.6, Math.min(0.9, 0.8 - (systemPressure - designPressure) * 0.05));\r\n    const power = (designAirflow * systemPressure) / (6356 * efficiency); // HP\r\n    const powerKW = power * 0.746; // Convert to kW\r\n    const speed = 1800 * Math.sqrt(systemPressure / designPressure); // Simplified speed calculation\r\n\r\n    const curvePosition: FanCurvePosition = {\r\n      operatingPoint: {\r\n        airflow: designAirflow,\r\n        pressure: systemPressure,\r\n        power: powerKW,\r\n        efficiency: efficiency * 100,\r\n        speed\r\n      },\r\n      designPoint: {\r\n        airflow: designAirflow,\r\n        pressure: designPressure,\r\n        power: (designAirflow * designPressure) / (6356 * 0.8) * 0.746,\r\n        efficiency: 80,\r\n        speed: 1800\r\n      },\r\n      efficiencyAtOperating: efficiency * 100,\r\n      efficiencyAtDesign: 80,\r\n      surgeMargin: Math.max(0, (designAirflow * 0.7 - designAirflow) / designAirflow * 100),\r\n      stallMargin: Math.max(0, (designAirflow * 1.3 - designAirflow) / designAirflow * 100),\r\n      recommendedOperatingRange: {\r\n        minAirflow: designAirflow * 0.7,\r\n        maxAirflow: designAirflow * 1.3,\r\n        minPressure: designPressure * 0.5,\r\n        maxPressure: designPressure * 1.5\r\n      }\r\n    };\r\n\r\n    return {\r\n      power: powerKW,\r\n      efficiency: efficiency * 100,\r\n      speed,\r\n      curvePosition\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate airflow metrics and distribution\r\n   */\r\n  private static calculateAirflowMetrics(systemConfiguration: SystemConfiguration): any {\r\n    const designAirflow = systemConfiguration.designParameters.designAirflow;\r\n    \r\n    // Simplified airflow calculation\r\n    // In practice, this would consider actual system measurements\r\n    const totalAirflow = designAirflow * 0.95; // Assume 5% leakage\r\n    const efficiency = (totalAirflow / designAirflow) * 100;\r\n\r\n    return {\r\n      totalAirflow,\r\n      efficiency,\r\n      flowDistribution: {\r\n        uniformityIndex: 0.85, // Simplified\r\n        variationCoefficient: 0.15,\r\n        zones: []\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate system efficiency metrics\r\n   */\r\n  private static calculateSystemEfficiencyMetrics(\r\n    systemConfiguration: SystemConfiguration,\r\n    fanPerformance: any,\r\n    systemPressureResult: any\r\n  ): any {\r\n    const designAirflow = systemConfiguration.designParameters.designAirflow;\r\n    const designPressure = systemConfiguration.designParameters.designPressure;\r\n\r\n    // Calculate various efficiency metrics\r\n    const systemEfficiency = Math.min(95, fanPerformance.efficiency * 0.9); // Account for system losses\r\n    const transportEfficiency = Math.min(90, 100 - (systemPressureResult.totalPressureLoss / designPressure) * 10);\r\n    const distributionEfficiency = Math.min(85, systemEfficiency * 0.9); // Account for distribution losses\r\n\r\n    return {\r\n      systemEfficiency,\r\n      transportEfficiency,\r\n      distributionEfficiency\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate environmental metrics\r\n   */\r\n  private static calculateEnvironmentalMetrics(\r\n    systemConfiguration: SystemConfiguration,\r\n    fanPerformance: any\r\n  ): any {\r\n    const designAirflow = systemConfiguration.designParameters.designAirflow;\r\n\r\n    // Simplified environmental calculations\r\n    const velocity = designAirflow / 144; // Simplified velocity calculation\r\n    const noiseLevel = 40 + 20 * Math.log10(velocity / 1000); // Simplified noise model\r\n    const vibrationLevel = Math.max(0.5, velocity / 2000); // Simplified vibration model\r\n    const temperatureRise = fanPerformance.power * 3412 / (designAirflow * 1.08); // Fan heat rise\r\n\r\n    return {\r\n      noiseLevel: Math.max(35, Math.min(65, noiseLevel)),\r\n      vibrationLevel: Math.max(0.1, Math.min(5.0, vibrationLevel)),\r\n      temperatureRise: Math.max(0.5, Math.min(5.0, temperatureRise)),\r\n      filterPressureDrop: 0.5, // Typical clean filter\r\n      coilPressureDrop: 0.8 // Typical coil pressure drop\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Assess system balance quality\r\n   */\r\n  private static assessSystemBalance(\r\n    systemConfiguration: SystemConfiguration,\r\n    airflowMetrics: any\r\n  ): BalanceQuality {\r\n    // Simplified balance assessment\r\n    const flowVariation = 0.15; // 15% variation\r\n    const pressureVariation = 0.12; // 12% variation\r\n    const overallScore = Math.max(0, 100 - (flowVariation + pressureVariation) * 200);\r\n\r\n    let balanceGrade: BalanceGrade;\r\n    if (overallScore >= 90) balanceGrade = BalanceGrade.EXCELLENT;\r\n    else if (overallScore >= 80) balanceGrade = BalanceGrade.GOOD;\r\n    else if (overallScore >= 70) balanceGrade = BalanceGrade.ACCEPTABLE;\r\n    else if (overallScore >= 60) balanceGrade = BalanceGrade.POOR;\r\n    else balanceGrade = BalanceGrade.CRITICAL;\r\n\r\n    return {\r\n      overallScore,\r\n      flowVariation,\r\n      pressureVariation,\r\n      balanceGrade,\r\n      criticalZones: balanceGrade === BalanceGrade.CRITICAL ? ['Zone 1', 'Zone 3'] : [],\r\n      balanceRecommendations: this.generateBalanceRecommendations(balanceGrade)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Generate balance recommendations based on grade\r\n   */\r\n  private static generateBalanceRecommendations(grade: BalanceGrade): string[] {\r\n    switch (grade) {\r\n      case BalanceGrade.CRITICAL:\r\n        return [\r\n          'Immediate system rebalancing required',\r\n          'Check for blocked dampers or ducts',\r\n          'Verify fan operation and capacity',\r\n          'Consider professional commissioning'\r\n        ];\r\n      case BalanceGrade.POOR:\r\n        return [\r\n          'System rebalancing recommended',\r\n          'Adjust damper positions',\r\n          'Check for duct leakage',\r\n          'Verify design calculations'\r\n        ];\r\n      case BalanceGrade.ACCEPTABLE:\r\n        return [\r\n          'Minor adjustments may improve performance',\r\n          'Monitor system performance trends',\r\n          'Consider seasonal adjustments'\r\n        ];\r\n      default:\r\n        return ['System balance is within acceptable limits'];\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Create duct segments from system configuration\r\n   */\r\n  private static createDuctSegmentsFromConfig(systemConfiguration: SystemConfiguration): any[] {\r\n    // Simplified duct segment creation\r\n    // In practice, this would parse the actual system configuration\r\n    return [\r\n      {\r\n        id: 'main_supply',\r\n        length: 100,\r\n        diameter: 24,\r\n        material: 'galvanized_steel',\r\n        roughness: 0.0015,\r\n        airflow: systemConfiguration.designParameters.designAirflow,\r\n        fittings: []\r\n      }\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Generate unique analysis ID\r\n   */\r\n  private static generateAnalysisId(systemId: string): string {\r\n    const timestamp = Date.now();\r\n    const random = Math.random().toString(36).substring(2, 8);\r\n    return `analysis_${systemId}_${timestamp}_${random}`;\r\n  }\r\n\r\n  /**\r\n   * Create default trend analysis when no historical data is available\r\n   */\r\n  private static createDefaultTrendAnalysis(): TrendAnalysis {\r\n    return {\r\n      timeRange: {\r\n        startDate: new Date(),\r\n        endDate: new Date(),\r\n        duration: 0,\r\n        units: 'days' as const\r\n      },\r\n      trendDirection: TrendDirection.STABLE,\r\n      trendMagnitude: 0,\r\n      seasonalPatterns: [],\r\n      anomalies: [],\r\n      predictiveAnalysis: {\r\n        forecastHorizon: 12,\r\n        predictedPerformance: [],\r\n        confidenceInterval: { lowerBound: 0, upperBound: 0, confidenceLevel: 0 },\r\n        predictionModel: {\r\n          modelType: 'linear_regression' as const,\r\n          accuracy: 0,\r\n          lastTrainingDate: new Date(),\r\n          dataPoints: 0,\r\n          validationScore: 0\r\n        },\r\n        keyFactors: [],\r\n        scenarios: []\r\n      },\r\n      degradationRate: {\r\n        overallDegradationRate: 2.0, // 2% per year typical\r\n        componentDegradation: [],\r\n        degradationFactors: [],\r\n        maintenanceImpact: {\r\n          preventiveMaintenance: { performanceImpact: 5, lifespanImpact: 2, costImpact: 1000 },\r\n          correctiveMaintenance: { performanceImpact: -10, lifespanImpact: -1, costImpact: 5000 },\r\n          deferredMaintenance: { performanceImpact: -15, lifespanImpact: -3, costImpact: 10000 }\r\n        },\r\n        projectedLifespan: {\r\n          currentAge: 0,\r\n          designLife: 20,\r\n          projectedLife: 18,\r\n          confidenceLevel: 80,\r\n          keyAssumptions: ['Regular maintenance', 'Normal operating conditions']\r\n        }\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Perform trend analysis on historical performance data\r\n   */\r\n  private static async performTrendAnalysis(\r\n    historicalData: PerformanceMetrics[],\r\n    currentMetrics: PerformanceMetrics\r\n  ): Promise<TrendAnalysis> {\r\n    if (historicalData.length < 2) {\r\n      return this.createDefaultTrendAnalysis();\r\n    }\r\n\r\n    // Calculate trend direction and magnitude\r\n    const trendAnalysis = this.calculateTrendDirection(historicalData);\r\n\r\n    // Detect seasonal patterns\r\n    const seasonalPatterns = this.detectSeasonalPatterns(historicalData);\r\n\r\n    // Detect anomalies\r\n    const anomalies = this.detectAnomalies(historicalData, currentMetrics);\r\n\r\n    // Generate predictive analysis\r\n    const predictiveAnalysis = this.generatePredictiveAnalysis(historicalData);\r\n\r\n    // Calculate degradation rate\r\n    const degradationRate = this.calculateDegradationRate(historicalData);\r\n\r\n    const timeRange = {\r\n      startDate: historicalData[0].totalSystemPressure.timestamp,\r\n      endDate: currentMetrics.totalSystemPressure.timestamp,\r\n      duration: historicalData.length,\r\n      units: 'days' as const\r\n    };\r\n\r\n    return {\r\n      timeRange,\r\n      trendDirection: trendAnalysis.direction,\r\n      trendMagnitude: trendAnalysis.magnitude,\r\n      seasonalPatterns,\r\n      anomalies,\r\n      predictiveAnalysis,\r\n      degradationRate\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate trend direction from historical data\r\n   */\r\n  private static calculateTrendDirection(historicalData: PerformanceMetrics[]): {\r\n    direction: TrendDirection;\r\n    magnitude: number;\r\n  } {\r\n    if (historicalData.length < 3) {\r\n      return { direction: TrendDirection.STABLE, magnitude: 0 };\r\n    }\r\n\r\n    // Simple linear regression on system efficiency\r\n    const efficiencyValues = historicalData.map(data => data.systemEfficiency.value);\r\n    const n = efficiencyValues.length;\r\n    const x = Array.from({ length: n }, (_, i) => i);\r\n\r\n    const sumX = x.reduce((a, b) => a + b, 0);\r\n    const sumY = efficiencyValues.reduce((a, b) => a + b, 0);\r\n    const sumXY = x.reduce((sum, xi, i) => sum + xi * efficiencyValues[i], 0);\r\n    const sumXX = x.reduce((sum, xi) => sum + xi * xi, 0);\r\n\r\n    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);\r\n    const magnitude = Math.abs(slope);\r\n\r\n    let direction: TrendDirection;\r\n    if (magnitude < 0.1) {\r\n      direction = TrendDirection.STABLE;\r\n    } else if (slope > 0) {\r\n      direction = TrendDirection.IMPROVING;\r\n    } else {\r\n      direction = TrendDirection.DEGRADING;\r\n    }\r\n\r\n    return { direction, magnitude };\r\n  }\r\n\r\n  /**\r\n   * Detect seasonal patterns in performance data\r\n   */\r\n  private static detectSeasonalPatterns(historicalData: PerformanceMetrics[]): any[] {\r\n    // Simplified seasonal pattern detection\r\n    // In practice, this would use more sophisticated time series analysis\r\n    return [\r\n      {\r\n        season: 'summer' as const,\r\n        averagePerformance: 85,\r\n        performanceVariation: 5,\r\n        typicalIssues: ['Higher cooling loads', 'Increased fan power']\r\n      },\r\n      {\r\n        season: 'winter' as const,\r\n        averagePerformance: 88,\r\n        performanceVariation: 3,\r\n        typicalIssues: ['Filter loading', 'Heating coil pressure drop']\r\n      }\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Detect performance anomalies\r\n   */\r\n  private static detectAnomalies(\r\n    historicalData: PerformanceMetrics[],\r\n    currentMetrics: PerformanceMetrics\r\n  ): PerformanceAnomaly[] {\r\n    const anomalies: PerformanceAnomaly[] = [];\r\n\r\n    // Check for sudden efficiency drop\r\n    if (historicalData.length > 0) {\r\n      const lastEfficiency = historicalData[historicalData.length - 1].systemEfficiency.value;\r\n      const currentEfficiency = currentMetrics.systemEfficiency.value;\r\n      const efficiencyDrop = lastEfficiency - currentEfficiency;\r\n\r\n      if (efficiencyDrop > 5) { // 5% drop threshold\r\n        anomalies.push({\r\n          id: `anomaly_${Date.now()}`,\r\n          detectionTimestamp: new Date(),\r\n          anomalyType: AnomalyType.SUDDEN_CHANGE,\r\n          severity: efficiencyDrop > 10 ? 'critical' as const : 'high' as const,\r\n          affectedMetrics: ['systemEfficiency'],\r\n          deviationMagnitude: efficiencyDrop,\r\n          duration: 1,\r\n          possibleCauses: [\r\n            {\r\n              cause: 'Filter blockage',\r\n              probability: 60,\r\n              category: 'maintenance_issue' as const,\r\n              diagnosticSteps: ['Check filter pressure drop', 'Inspect filter condition']\r\n            },\r\n            {\r\n              cause: 'Fan belt slippage',\r\n              probability: 30,\r\n              category: 'equipment_failure' as const,\r\n              diagnosticSteps: ['Check fan belt tension', 'Inspect motor operation']\r\n            }\r\n          ],\r\n          recommendedActions: [\r\n            'Replace or clean filters',\r\n            'Check fan operation',\r\n            'Verify damper positions'\r\n          ],\r\n          resolved: false\r\n        });\r\n      }\r\n    }\r\n\r\n    return anomalies;\r\n  }\r\n\r\n  /**\r\n   * Generate predictive analysis\r\n   */\r\n  private static generatePredictiveAnalysis(historicalData: PerformanceMetrics[]): PredictiveAnalysis {\r\n    // Simplified predictive analysis\r\n    // In practice, this would use machine learning models\r\n    return {\r\n      forecastHorizon: 12,\r\n      predictedPerformance: [\r\n        {\r\n          metric: 'systemEfficiency',\r\n          currentValue: 85,\r\n          predictedValue: 83,\r\n          changePercent: -2.4,\r\n          timeToTarget: 6\r\n        }\r\n      ],\r\n      confidenceInterval: {\r\n        lowerBound: 80,\r\n        upperBound: 86,\r\n        confidenceLevel: 85\r\n      },\r\n      predictionModel: {\r\n        modelType: 'time_series' as const,\r\n        accuracy: 85,\r\n        lastTrainingDate: new Date(),\r\n        dataPoints: historicalData.length,\r\n        validationScore: 0.85\r\n      },\r\n      keyFactors: [\r\n        {\r\n          factor: 'Filter condition',\r\n          impact: 40,\r\n          controllable: true,\r\n          mitigationStrategies: ['Regular filter replacement', 'Pressure monitoring']\r\n        }\r\n      ],\r\n      scenarios: []\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate system degradation rate\r\n   */\r\n  private static calculateDegradationRate(historicalData: PerformanceMetrics[]): any {\r\n    // Simplified degradation calculation\r\n    return {\r\n      overallDegradationRate: 2.5, // 2.5% per year\r\n      componentDegradation: [\r\n        {\r\n          componentId: 'main_fan',\r\n          componentType: 'fan' as const,\r\n          degradationRate: 1.5,\r\n          currentCondition: 92,\r\n          estimatedRemainingLife: 12,\r\n          replacementThreshold: 70\r\n        }\r\n      ],\r\n      degradationFactors: [\r\n        {\r\n          factor: 'Operating hours',\r\n          impact: 50,\r\n          controllable: false,\r\n          mitigationStrategies: ['Optimize operating schedule']\r\n        },\r\n        {\r\n          factor: 'Maintenance quality',\r\n          impact: 30,\r\n          controllable: true,\r\n          mitigationStrategies: ['Preventive maintenance program', 'Staff training']\r\n        }\r\n      ],\r\n      maintenanceImpact: {\r\n        preventiveMaintenance: { performanceImpact: 5, lifespanImpact: 2, costImpact: 1000 },\r\n        correctiveMaintenance: { performanceImpact: -10, lifespanImpact: -1, costImpact: 5000 },\r\n        deferredMaintenance: { performanceImpact: -15, lifespanImpact: -3, costImpact: 10000 }\r\n      },\r\n      projectedLifespan: {\r\n        currentAge: 3,\r\n        designLife: 20,\r\n        projectedLife: 18,\r\n        confidenceLevel: 80,\r\n        keyAssumptions: ['Regular maintenance', 'Normal operating conditions']\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Perform benchmark comparison against similar systems\r\n   */\r\n  private static async performBenchmarkComparison(\r\n    systemConfiguration: SystemConfiguration,\r\n    performanceMetrics: PerformanceMetrics\r\n  ): Promise<BenchmarkComparison> {\r\n    // Simplified benchmark comparison\r\n    // In practice, this would query a database of similar systems\r\n    const systemEfficiency = performanceMetrics.systemEfficiency.value;\r\n    const industryAverage = 82; // Typical industry average\r\n    const bestInClass = 92; // Best in class performance\r\n\r\n    return {\r\n      benchmarkType: BenchmarkType.INDUSTRY_AVERAGE,\r\n      benchmarkSource: 'ASHRAE Performance Database',\r\n      systemPerformance: systemEfficiency,\r\n      benchmarkValue: industryAverage,\r\n      percentile: this.calculatePercentile(systemEfficiency, industryAverage),\r\n      performanceGap: industryAverage - systemEfficiency,\r\n      improvementPotential: bestInClass - systemEfficiency,\r\n      similarSystems: [\r\n        {\r\n          systemId: 'similar_system_1',\r\n          systemName: 'Office Building HVAC',\r\n          performanceMetric: 84,\r\n          systemCharacteristics: {\r\n            size: 'medium' as const,\r\n            age: 5,\r\n            buildingType: 'office' as const,\r\n            climateZone: '4A',\r\n            operatingHours: 2500\r\n          },\r\n          performanceDifference: 84 - systemEfficiency\r\n        }\r\n      ]\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate percentile ranking\r\n   */\r\n  private static calculatePercentile(value: number, average: number): number {\r\n    // Simplified percentile calculation\r\n    // Assumes normal distribution with standard deviation of 8\r\n    const standardDeviation = 8;\r\n    const zScore = (value - average) / standardDeviation;\r\n\r\n    // Convert z-score to percentile (simplified)\r\n    if (zScore >= 2) return 97;\r\n    if (zScore >= 1) return 84;\r\n    if (zScore >= 0) return 50 + (zScore * 34);\r\n    if (zScore >= -1) return 50 + (zScore * 34);\r\n    if (zScore >= -2) return 16;\r\n    return 3;\r\n  }\r\n\r\n  /**\r\n   * Analyze system efficiency in detail\r\n   */\r\n  private static async analyzeSystemEfficiency(\r\n    systemConfiguration: SystemConfiguration,\r\n    performanceMetrics: PerformanceMetrics\r\n  ): Promise<EfficiencyAnalysis> {\r\n    // Simplified efficiency analysis\r\n    return {\r\n      overallEfficiency: {\r\n        value: performanceMetrics.systemEfficiency.value,\r\n        units: '%',\r\n        calculationMethod: 'calculated' as const,\r\n        accuracy: 0.9,\r\n        timestamp: new Date()\r\n      },\r\n      componentEfficiencies: [\r\n        {\r\n          componentId: 'main_fan',\r\n          componentType: 'fan' as const,\r\n          efficiency: performanceMetrics.fanEfficiency.value,\r\n          ratedEfficiency: 85,\r\n          degradationFactor: 0.95,\r\n          maintenanceStatus: 'good' as const\r\n        }\r\n      ],\r\n      efficiencyTrends: [],\r\n      efficiencyLosses: [],\r\n      improvementOpportunities: [],\r\n      benchmarkComparison: {\r\n        benchmarkType: BenchmarkType.INDUSTRY_AVERAGE,\r\n        benchmarkSource: 'Industry Standards',\r\n        systemPerformance: performanceMetrics.systemEfficiency.value,\r\n        benchmarkValue: 82,\r\n        percentile: 65,\r\n        performanceGap: 0,\r\n        improvementPotential: 10,\r\n        similarSystems: []\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Detect performance alerts and warnings\r\n   */\r\n  private static async detectPerformanceAlerts(\r\n    performanceMetrics: PerformanceMetrics,\r\n    historicalData?: PerformanceMetrics[]\r\n  ): Promise<PerformanceAlert[]> {\r\n    const alerts: PerformanceAlert[] = [];\r\n\r\n    // Check efficiency threshold\r\n    if (performanceMetrics.systemEfficiency.value < 75) {\r\n      alerts.push({\r\n        id: `alert_${Date.now()}_efficiency`,\r\n        alertType: AlertType.THRESHOLD_EXCEEDED,\r\n        severity: AlertSeverity.HIGH,\r\n        metric: 'systemEfficiency',\r\n        currentValue: performanceMetrics.systemEfficiency.value,\r\n        thresholdValue: 75,\r\n        message: 'System efficiency below acceptable threshold',\r\n        timestamp: new Date(),\r\n        acknowledged: false,\r\n        recommendedActions: [\r\n          'Check filter condition',\r\n          'Verify fan operation',\r\n          'Inspect ductwork for leaks'\r\n        ]\r\n      });\r\n    }\r\n\r\n    // Check pressure threshold\r\n    if (performanceMetrics.totalSystemPressure.value > 4.0) {\r\n      alerts.push({\r\n        id: `alert_${Date.now()}_pressure`,\r\n        alertType: AlertType.THRESHOLD_EXCEEDED,\r\n        severity: AlertSeverity.MEDIUM,\r\n        metric: 'totalSystemPressure',\r\n        currentValue: performanceMetrics.totalSystemPressure.value,\r\n        thresholdValue: 4.0,\r\n        message: 'System pressure higher than expected',\r\n        timestamp: new Date(),\r\n        acknowledged: false,\r\n        recommendedActions: [\r\n          'Check for blocked ducts',\r\n          'Verify damper positions',\r\n          'Inspect filters'\r\n        ]\r\n      });\r\n    }\r\n\r\n    return alerts;\r\n  }\r\n\r\n  /**\r\n   * Generate performance recommendations\r\n   */\r\n  private static async generatePerformanceRecommendations(\r\n    performanceMetrics: PerformanceMetrics,\r\n    efficiencyAnalysis: EfficiencyAnalysis,\r\n    benchmarkComparison: BenchmarkComparison\r\n  ): Promise<PerformanceRecommendation[]> {\r\n    const recommendations: PerformanceRecommendation[] = [];\r\n\r\n    // Efficiency improvement recommendation\r\n    if (performanceMetrics.systemEfficiency.value < 85) {\r\n      recommendations.push({\r\n        id: `rec_${Date.now()}_efficiency`,\r\n        type: RecommendationType.OPTIMIZATION,\r\n        priority: RecommendationPriority.HIGH,\r\n        title: 'Improve System Efficiency',\r\n        description: 'System efficiency is below optimal levels. Consider implementing efficiency improvements.',\r\n        expectedImpact: {\r\n          energySavings: 15,\r\n          costSavings: 2500,\r\n          performanceImprovement: 10,\r\n          emissionReduction: 1200,\r\n          reliabilityImprovement: 5\r\n        },\r\n        implementationCost: 5000,\r\n        paybackPeriod: 24,\r\n        implementationComplexity: 'moderate' as const,\r\n        requiredActions: [\r\n          'Replace filters with high-efficiency models',\r\n          'Seal ductwork leaks',\r\n          'Optimize fan speed control'\r\n        ],\r\n        timeline: '2-4 weeks'\r\n      });\r\n    }\r\n\r\n    return recommendations;\r\n  }\r\n\r\n  /**\r\n   * Perform uncertainty analysis on performance metrics\r\n   */\r\n  private static async performUncertaintyAnalysis(\r\n    performanceMetrics: PerformanceMetrics,\r\n    systemConfiguration: SystemConfiguration\r\n  ): Promise<UncertaintyAnalysis> {\r\n    // Simplified uncertainty analysis\r\n    return {\r\n      overallUncertainty: 0.1, // 10% overall uncertainty\r\n      metricUncertainties: [\r\n        {\r\n          metric: 'systemEfficiency',\r\n          uncertainty: 0.05,\r\n          sources: ['measurement error', 'calculation assumptions'],\r\n          confidenceLevel: 90\r\n        }\r\n      ],\r\n      sensitivityAnalysis: {\r\n        parameters: [\r\n          {\r\n            parameter: 'airflow',\r\n            sensitivity: 0.8,\r\n            impact: 'high' as const\r\n          }\r\n        ],\r\n        keyDrivers: ['airflow', 'pressure'],\r\n        uncertaintyContributors: ['measurement accuracy', 'model assumptions']\r\n      },\r\n      recommendations: [\r\n        'Improve measurement accuracy',\r\n        'Calibrate sensors regularly',\r\n        'Validate calculation models'\r\n      ]\r\n    };\r\n  }\r\n}\r\n"], "mappings": ";;AAAA;;;;;;;;;;AAAA;AAAA,SAAAA,cAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;MAAA;QAAAC,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAO,SAAA;MAAA;QAAAD,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;IAAA;IAAAW,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,cAAA;MAAAC,IAAA;MAAAC,QAAA;MAAAC,KAAA;MAAAC,OAAA;MAAAC,cAAA;MAAAC,OAAA;IAAA;IAAAC,eAAA;IAAA5B,IAAA;EAAA;EAAA,IAAA6B,QAAA,GAAA5B,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAA0B,QAAA,CAAA9B,IAAA,KAAA8B,QAAA,CAAA9B,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAA6B,QAAA,CAAA9B,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAA0B,cAAA,GAAAD,QAAA,CAAA9B,IAAA;EAAA;IA8CA;IAAAD,aAAA,YAAAA,CAAA;MAAA,OAAAgC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAhC,aAAA;AAAAA,aAAA,GAAAoB,CAAA;;;;;;;AAnCA,MAAAa,qBAAA;AAAA;AAAA,CAAAjC,aAAA,GAAAoB,CAAA,OAAAc,OAAA;AA8BA,MAAAC,0BAAA;AAAA;AAAA,CAAAnC,aAAA,GAAAoB,CAAA,OAAAc,OAAA;AAKA;;;;;;;;;;AAUA,MAAaE,+BAA+B;EAK1C;;;EAGO,aAAaC,wBAAwBA,CAC1CC,mBAAwC,EACxCC,aAA4B,EAC5BC,cAAqC;IAAA;IAAAxC,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAErC,IAAI;MACF,MAAMqB,UAAU;MAAA;MAAA,CAAAzC,aAAA,GAAAoB,CAAA,OAAG,IAAI,CAACsB,kBAAkB,CAACJ,mBAAmB,CAACK,EAAE,CAAC;MAClE,MAAMC,SAAS;MAAA;MAAA,CAAA5C,aAAA,GAAAoB,CAAA,OAAG,IAAIyB,IAAI,EAAE;MAE5B;MACA,MAAMC,kBAAkB;MAAA;MAAA,CAAA9C,aAAA,GAAAoB,CAAA,OAAG,MAAM,IAAI,CAAC2B,2BAA2B,CAC/DT,mBAAmB,EACnBC,aAAa,CACd;MAED;MACA,MAAMS,aAAa;MAAA;MAAA,CAAAhD,aAAA,GAAAoB,CAAA;MAAG;MAAA,CAAApB,aAAA,GAAAsB,CAAA,UAAAkB,cAAc;MAAA;MAAA,CAAAxC,aAAA,GAAAsB,CAAA,UAAIkB,cAAc,CAACS,MAAM,GAAG,CAAC;MAAA;MAAA,CAAAjD,aAAA,GAAAsB,CAAA,UAC7D,MAAM,IAAI,CAAC4B,oBAAoB,CAACV,cAAc,EAAEM,kBAAkB,CAAC;MAAA;MAAA,CAAA9C,aAAA,GAAAsB,CAAA,UACnE,IAAI,CAAC6B,0BAA0B,EAAE;MAErC;MACA,MAAMC,mBAAmB;MAAA;MAAA,CAAApD,aAAA,GAAAoB,CAAA,OAAG,MAAM,IAAI,CAACiC,0BAA0B,CAC/Df,mBAAmB,EACnBQ,kBAAkB,CACnB;MAED;MACA,MAAMQ,kBAAkB;MAAA;MAAA,CAAAtD,aAAA,GAAAoB,CAAA,QAAG,MAAM,IAAI,CAACmC,uBAAuB,CAC3DjB,mBAAmB,EACnBQ,kBAAkB,CACnB;MAED;MACA,MAAMU,iBAAiB;MAAA;MAAA,CAAAxD,aAAA,GAAAoB,CAAA,QAAG,MAAM,IAAI,CAACqC,uBAAuB,CAC1DX,kBAAkB,EAClBN,cAAc,CACf;MAED;MACA,MAAMkB,eAAe;MAAA;MAAA,CAAA1D,aAAA,GAAAoB,CAAA,QAAG,MAAM,IAAI,CAACuC,kCAAkC,CACnEb,kBAAkB,EAClBQ,kBAAkB,EAClBF,mBAAmB,CACpB;MAED;MACA,MAAMQ,mBAAmB;MAAA;MAAA,CAAA5D,aAAA,GAAAoB,CAAA,QAAG,MAAM,IAAI,CAACyC,0BAA0B,CAC/Df,kBAAkB,EAClBR,mBAAmB,CACpB;MAED,MAAMwB,QAAQ;MAAA;MAAA,CAAA9D,aAAA,GAAAoB,CAAA,QAAwB;QACpCuB,EAAE,EAAEF,UAAU;QACdsB,QAAQ,EAAEzB,mBAAmB,CAACK,EAAE;QAChCqB,iBAAiB,EAAEpB,SAAS;QAC5BE,kBAAkB;QAClBE,aAAa;QACbI,mBAAmB;QACnBE,kBAAkB;QAClBE,iBAAiB;QACjBE,eAAe;QACfE;OACD;MAED;MAAA;MAAA5D,aAAA,GAAAoB,CAAA;MACA,IAAI,CAAC6C,cAAc,CAACC,GAAG,CAACzB,UAAU,EAAEqB,QAAQ,CAAC;MAAC;MAAA9D,aAAA,GAAAoB,CAAA;MAE9C,OAAO0C,QAAQ;IAEjB,CAAC,CAAC,OAAOK,KAAK,EAAE;MAAA;MAAAnE,aAAA,GAAAoB,CAAA;MACd,MAAM,IAAIgD,KAAK,CAAC,uCAAuCD,KAAK,YAAYC,KAAK;MAAA;MAAA,CAAApE,aAAA,GAAAsB,CAAA,UAAG6C,KAAK,CAACE,OAAO;MAAA;MAAA,CAAArE,aAAA,GAAAsB,CAAA,UAAG,eAAe,GAAE,CAAC;IACpH;EACF;EAEA;;;EAGQ,aAAayB,2BAA2BA,CAC9CT,mBAAwC,EACxCC,aAA4B;IAAA;IAAAvC,aAAA,GAAAqB,CAAA;IAE5B,MAAMuB,SAAS;IAAA;IAAA,CAAA5C,aAAA,GAAAoB,CAAA,QAAG,IAAIyB,IAAI,EAAE;IAE5B;IACA,MAAMyB,oBAAoB;IAAA;IAAA,CAAAtE,aAAA,GAAAoB,CAAA,QAAGe,0BAAA,CAAAoC,wBAAwB,CAACC,+BAA+B,CAAC;MACpFC,QAAQ,EAAE,IAAI,CAACC,4BAA4B,CAACpC,mBAAmB,CAAC;MAChEqC,UAAU,EAAErC,mBAAmB,CAACqC,UAAU;MAC1CC,gBAAgB,EAAEtC,mBAAmB,CAACuC;KACvC,CAAC;IAEF;IACA,MAAMC,cAAc;IAAA;IAAA,CAAA9E,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAAC2D,uBAAuB,CACjDzC,mBAAmB,EACnBgC,oBAAoB,CAACU,iBAAiB,CACvC;IAED;IACA,MAAMC,cAAc;IAAA;IAAA,CAAAjF,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAAC8D,uBAAuB,CAAC5C,mBAAmB,CAAC;IAExE;IACA,MAAM6C,iBAAiB;IAAA;IAAA,CAAAnF,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAACgE,gCAAgC,CAC7D9C,mBAAmB,EACnBwC,cAAc,EACdR,oBAAoB,CACrB;IAED;IACA,MAAMe,oBAAoB;IAAA;IAAA,CAAArF,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAACkE,6BAA6B,CAC7DhD,mBAAmB,EACnBwC,cAAc,CACf;IAED;IACA,MAAMS,cAAc;IAAA;IAAA,CAAAvF,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAACoE,mBAAmB,CAC7ClD,mBAAmB,EACnB2C,cAAc,CACf;IAED,MAAMnC,kBAAkB;IAAA;IAAA,CAAA9C,aAAA,GAAAoB,CAAA,QAAuB;MAC7C;MACAqE,mBAAmB,EAAE,IAAI,CAACC,iBAAiB,CACzCpB,oBAAoB,CAACU,iBAAiB,EACtC,OAAO,EACP/C,qBAAA,CAAA0D,iBAAiB,CAACC,UAAU,EAC5B3D,qBAAA,CAAA4D,gBAAgB,CAACC,IAAI,EACrBlD,SAAS,CACV;MACDmD,cAAc,EAAE,IAAI,CAACL,iBAAiB;MACpC;MAAA,CAAA1F,aAAA,GAAAsB,CAAA,UAAAgD,oBAAoB,CAACyB,cAAc;MAAA;MAAA,CAAA/F,aAAA,GAAAsB,CAAA,UAAIgD,oBAAoB,CAACU,iBAAiB,GAAG,GAAG,GACnF,OAAO,EACP/C,qBAAA,CAAA0D,iBAAiB,CAACC,UAAU,EAC5B3D,qBAAA,CAAA4D,gBAAgB,CAACC,IAAI,EACrBlD,SAAS,CACV;MACDoD,gBAAgB,EAAE,IAAI,CAACN,iBAAiB;MACtC;MAAA,CAAA1F,aAAA,GAAAsB,CAAA,UAAAgD,oBAAoB,CAAC0B,gBAAgB;MAAA;MAAA,CAAAhG,aAAA,GAAAsB,CAAA,UAAIgD,oBAAoB,CAACU,iBAAiB,GAAG,GAAG,GACrF,OAAO,EACP/C,qBAAA,CAAA0D,iBAAiB,CAACC,UAAU,EAC5B3D,qBAAA,CAAA4D,gBAAgB,CAACC,IAAI,EACrBlD,SAAS,CACV;MACDqD,YAAY,EAAE,IAAI,CAACP,iBAAiB,CAClCT,cAAc,CAACgB,YAAY,EAC3B,KAAK,EACLhE,qBAAA,CAAA0D,iBAAiB,CAACC,UAAU,EAC5B3D,qBAAA,CAAA4D,gBAAgB,CAACC,IAAI,EACrBlD,SAAS,CACV;MACDsD,aAAa,EAAE,IAAI,CAACR,iBAAiB,CACnCpD,mBAAmB,CAAC6D,gBAAgB,CAACD,aAAa,EAClD,KAAK,EACLjE,qBAAA,CAAA0D,iBAAiB,CAACS,iBAAiB,EACnCnE,qBAAA,CAAA4D,gBAAgB,CAACC,IAAI,EACrBlD,SAAS,CACV;MACDyD,iBAAiB,EAAE,IAAI,CAACX,iBAAiB,CACvCT,cAAc,CAACqB,UAAU,EACzB,GAAG,EACHrE,qBAAA,CAAA0D,iBAAiB,CAACC,UAAU,EAC5B3D,qBAAA,CAAA4D,gBAAgB,CAACU,MAAM,EACvB3D,SAAS,CACV;MAED;MACA4D,QAAQ,EAAE,IAAI,CAACd,iBAAiB,CAC9BZ,cAAc,CAAC2B,KAAK,EACpB,IAAI,EACJxE,qBAAA,CAAA0D,iBAAiB,CAACC,UAAU,EAC5B3D,qBAAA,CAAA4D,gBAAgB,CAACC,IAAI,EACrBlD,SAAS,CACV;MACD8D,aAAa,EAAE,IAAI,CAAChB,iBAAiB,CACnCZ,cAAc,CAACwB,UAAU,EACzB,GAAG,EACHrE,qBAAA,CAAA0D,iBAAiB,CAACC,UAAU,EAC5B3D,qBAAA,CAAA4D,gBAAgB,CAACC,IAAI,EACrBlD,SAAS,CACV;MACD+D,QAAQ,EAAE,IAAI,CAACjB,iBAAiB,CAC9BZ,cAAc,CAAC8B,KAAK,EACpB,KAAK,EACL3E,qBAAA,CAAA0D,iBAAiB,CAACC,UAAU,EAC5B3D,qBAAA,CAAA4D,gBAAgB,CAACU,MAAM,EACvB3D,SAAS,CACV;MACDiE,gBAAgB,EAAE/B,cAAc,CAACgC,aAAa;MAE9C;MACAC,gBAAgB,EAAE,IAAI,CAACrB,iBAAiB,CACtCP,iBAAiB,CAAC4B,gBAAgB,EAClC,GAAG,EACH9E,qBAAA,CAAA0D,iBAAiB,CAACC,UAAU,EAC5B3D,qBAAA,CAAA4D,gBAAgB,CAACC,IAAI,EACrBlD,SAAS,CACV;MACDoE,mBAAmB,EAAE,IAAI,CAACtB,iBAAiB,CACzCP,iBAAiB,CAAC6B,mBAAmB,EACrC,GAAG,EACH/E,qBAAA,CAAA0D,iBAAiB,CAACC,UAAU,EAC5B3D,qBAAA,CAAA4D,gBAAgB,CAACC,IAAI,EACrBlD,SAAS,CACV;MACDqE,sBAAsB,EAAE,IAAI,CAACvB,iBAAiB,CAC5CP,iBAAiB,CAAC8B,sBAAsB,EACxC,GAAG,EACHhF,qBAAA,CAAA0D,iBAAiB,CAACC,UAAU,EAC5B3D,qBAAA,CAAA4D,gBAAgB,CAACU,MAAM,EACvB3D,SAAS,CACV;MAED;MACAsE,UAAU,EAAE,IAAI,CAACxB,iBAAiB,CAChCL,oBAAoB,CAAC6B,UAAU,EAC/B,KAAK,EACLjF,qBAAA,CAAA0D,iBAAiB,CAACwB,SAAS,EAC3BlF,qBAAA,CAAA4D,gBAAgB,CAACU,MAAM,EACvB3D,SAAS,CACV;MACDwE,cAAc,EAAE,IAAI,CAAC1B,iBAAiB,CACpCL,oBAAoB,CAAC+B,cAAc,EACnC,MAAM,EACNnF,qBAAA,CAAA0D,iBAAiB,CAACwB,SAAS,EAC3BlF,qBAAA,CAAA4D,gBAAgB,CAACwB,GAAG,EACpBzE,SAAS,CACV;MACD0E,eAAe,EAAE,IAAI,CAAC5B,iBAAiB,CACrCL,oBAAoB,CAACiC,eAAe,EACpC,IAAI,EACJrF,qBAAA,CAAA0D,iBAAiB,CAACC,UAAU,EAC5B3D,qBAAA,CAAA4D,gBAAgB,CAACU,MAAM,EACvB3D,SAAS,CACV;MAED;MACA2E,kBAAkB,EAAE,IAAI,CAAC7B,iBAAiB,CACxCL,oBAAoB,CAACkC,kBAAkB,EACvC,OAAO,EACPtF,qBAAA,CAAA0D,iBAAiB,CAACwB,SAAS,EAC3BlF,qBAAA,CAAA4D,gBAAgB,CAACU,MAAM,EACvB3D,SAAS,CACV;MACD4E,gBAAgB,EAAE,IAAI,CAAC9B,iBAAiB,CACtCL,oBAAoB,CAACmC,gBAAgB,EACrC,OAAO,EACPvF,qBAAA,CAAA0D,iBAAiB,CAACwB,SAAS,EAC3BlF,qBAAA,CAAA4D,gBAAgB,CAACU,MAAM,EACvB3D,SAAS,CACV;MACD6E,eAAe,EAAE,EAAE;MAAE;MAErB;MACAlC,cAAc;MACdmC,gBAAgB,EAAEzC,cAAc,CAACyC;KAClC;IAAC;IAAA1H,aAAA,GAAAoB,CAAA;IAEF,OAAO0B,kBAAkB;EAC3B;EAEA;;;EAGQ,OAAO4C,iBAAiBA,CAC9BiC,KAAa,EACbC,KAAa,EACbC,MAAyB,EACzBC,OAAyB,EACzBlF,SAAe,EACfmF,QAAA;EAAA;EAAA,CAAA/H,aAAA,GAAAsB,CAAA,UAAmB,IAAI;IAAA;IAAAtB,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAEvB,OAAO;MACLuG,KAAK;MACLC,KAAK;MACLG,QAAQ;MACRnF,SAAS;MACTiF,MAAM;MACNG,gBAAgB,EAAEF,OAAO;MACzBG,iBAAiB,EAAE;QACjBC,UAAU,EAAEP,KAAK,IAAI,CAAC,IAAI,CAAC,GAAGI,QAAQ,CAAC,CAAC;QACxCI,UAAU,EAAER,KAAK,IAAI,CAAC,IAAI,CAAC,GAAGI,QAAQ,CAAC,CAAC;QACxCK,eAAe,EAAEL,QAAQ,GAAG,GAAG;QAC/BM,gBAAgB,EAAE;;KAErB;EACH;EAEA;;;EAGQ,OAAOtD,uBAAuBA,CACpCzC,mBAAwC,EACxCgG,cAAsB;IAAA;IAAAtI,aAAA,GAAAqB,CAAA;IAEtB,MAAM6E,aAAa;IAAA;IAAA,CAAAlG,aAAA,GAAAoB,CAAA,QAAGkB,mBAAmB,CAAC6D,gBAAgB,CAACD,aAAa;IACxE,MAAMqC,cAAc;IAAA;IAAA,CAAAvI,aAAA,GAAAoB,CAAA,QAAGkB,mBAAmB,CAAC6D,gBAAgB,CAACoC,cAAc;IAE1E;IACA;IACA,MAAMjC,UAAU;IAAA;IAAA,CAAAtG,aAAA,GAAAoB,CAAA,QAAGoH,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,GAAG,CAAC,GAAG,EAAE,GAAG,GAAG,CAACJ,cAAc,GAAGC,cAAc,IAAI,IAAI,CAAC,CAAC;IAC/F,MAAM9B,KAAK;IAAA;IAAA,CAAAzG,aAAA,GAAAoB,CAAA,QAAI8E,aAAa,GAAGoC,cAAc,IAAK,IAAI,GAAGhC,UAAU,CAAC,EAAC,CAAC;IACtE,MAAMqC,OAAO;IAAA;IAAA,CAAA3I,aAAA,GAAAoB,CAAA,QAAGqF,KAAK,GAAG,KAAK,EAAC,CAAC;IAC/B,MAAMG,KAAK;IAAA;IAAA,CAAA5G,aAAA,GAAAoB,CAAA,QAAG,IAAI,GAAGoH,IAAI,CAACI,IAAI,CAACN,cAAc,GAAGC,cAAc,CAAC,EAAC,CAAC;IAEjE,MAAMzB,aAAa;IAAA;IAAA,CAAA9G,aAAA,GAAAoB,CAAA,QAAqB;MACtCyH,cAAc,EAAE;QACdC,OAAO,EAAE5C,aAAa;QACtB6C,QAAQ,EAAET,cAAc;QACxB7B,KAAK,EAAEkC,OAAO;QACdrC,UAAU,EAAEA,UAAU,GAAG,GAAG;QAC5BM;OACD;MACDoC,WAAW,EAAE;QACXF,OAAO,EAAE5C,aAAa;QACtB6C,QAAQ,EAAER,cAAc;QACxB9B,KAAK,EAAGP,aAAa,GAAGqC,cAAc,IAAK,IAAI,GAAG,GAAG,CAAC,GAAG,KAAK;QAC9DjC,UAAU,EAAE,EAAE;QACdM,KAAK,EAAE;OACR;MACDqC,qBAAqB,EAAE3C,UAAU,GAAG,GAAG;MACvC4C,kBAAkB,EAAE,EAAE;MACtBC,WAAW,EAAEX,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAACvC,aAAa,GAAG,GAAG,GAAGA,aAAa,IAAIA,aAAa,GAAG,GAAG,CAAC;MACrFkD,WAAW,EAAEZ,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAACvC,aAAa,GAAG,GAAG,GAAGA,aAAa,IAAIA,aAAa,GAAG,GAAG,CAAC;MACrFmD,yBAAyB,EAAE;QACzBC,UAAU,EAAEpD,aAAa,GAAG,GAAG;QAC/BqD,UAAU,EAAErD,aAAa,GAAG,GAAG;QAC/BsD,WAAW,EAAEjB,cAAc,GAAG,GAAG;QACjCkB,WAAW,EAAElB,cAAc,GAAG;;KAEjC;IAAC;IAAAvI,aAAA,GAAAoB,CAAA;IAEF,OAAO;MACLqF,KAAK,EAAEkC,OAAO;MACdrC,UAAU,EAAEA,UAAU,GAAG,GAAG;MAC5BM,KAAK;MACLE;KACD;EACH;EAEA;;;EAGQ,OAAO5B,uBAAuBA,CAAC5C,mBAAwC;IAAA;IAAAtC,aAAA,GAAAqB,CAAA;IAC7E,MAAM6E,aAAa;IAAA;IAAA,CAAAlG,aAAA,GAAAoB,CAAA,QAAGkB,mBAAmB,CAAC6D,gBAAgB,CAACD,aAAa;IAExE;IACA;IACA,MAAMD,YAAY;IAAA;IAAA,CAAAjG,aAAA,GAAAoB,CAAA,QAAG8E,aAAa,GAAG,IAAI,EAAC,CAAC;IAC3C,MAAMI,UAAU;IAAA;IAAA,CAAAtG,aAAA,GAAAoB,CAAA,QAAI6E,YAAY,GAAGC,aAAa,GAAI,GAAG;IAAC;IAAAlG,aAAA,GAAAoB,CAAA;IAExD,OAAO;MACL6E,YAAY;MACZK,UAAU;MACVoB,gBAAgB,EAAE;QAChBgC,eAAe,EAAE,IAAI;QAAE;QACvBC,oBAAoB,EAAE,IAAI;QAC1BC,KAAK,EAAE;;KAEV;EACH;EAEA;;;EAGQ,OAAOxE,gCAAgCA,CAC7C9C,mBAAwC,EACxCwC,cAAmB,EACnBR,oBAAyB;IAAA;IAAAtE,aAAA,GAAAqB,CAAA;IAEzB,MAAM6E,aAAa;IAAA;IAAA,CAAAlG,aAAA,GAAAoB,CAAA,QAAGkB,mBAAmB,CAAC6D,gBAAgB,CAACD,aAAa;IACxE,MAAMqC,cAAc;IAAA;IAAA,CAAAvI,aAAA,GAAAoB,CAAA,QAAGkB,mBAAmB,CAAC6D,gBAAgB,CAACoC,cAAc;IAE1E;IACA,MAAMxB,gBAAgB;IAAA;IAAA,CAAA/G,aAAA,GAAAoB,CAAA,QAAGoH,IAAI,CAACE,GAAG,CAAC,EAAE,EAAE5D,cAAc,CAACwB,UAAU,GAAG,GAAG,CAAC,EAAC,CAAC;IACxE,MAAMU,mBAAmB;IAAA;IAAA,CAAAhH,aAAA,GAAAoB,CAAA,QAAGoH,IAAI,CAACE,GAAG,CAAC,EAAE,EAAE,GAAG,GAAIpE,oBAAoB,CAACU,iBAAiB,GAAGuD,cAAc,GAAI,EAAE,CAAC;IAC9G,MAAMtB,sBAAsB;IAAA;IAAA,CAAAjH,aAAA,GAAAoB,CAAA,QAAGoH,IAAI,CAACE,GAAG,CAAC,EAAE,EAAE3B,gBAAgB,GAAG,GAAG,CAAC,EAAC,CAAC;IAAA;IAAA/G,aAAA,GAAAoB,CAAA;IAErE,OAAO;MACL2F,gBAAgB;MAChBC,mBAAmB;MACnBC;KACD;EACH;EAEA;;;EAGQ,OAAO3B,6BAA6BA,CAC1ChD,mBAAwC,EACxCwC,cAAmB;IAAA;IAAA9E,aAAA,GAAAqB,CAAA;IAEnB,MAAM6E,aAAa;IAAA;IAAA,CAAAlG,aAAA,GAAAoB,CAAA,QAAGkB,mBAAmB,CAAC6D,gBAAgB,CAACD,aAAa;IAExE;IACA,MAAM2D,QAAQ;IAAA;IAAA,CAAA7J,aAAA,GAAAoB,CAAA,QAAG8E,aAAa,GAAG,GAAG,EAAC,CAAC;IACtC,MAAMgB,UAAU;IAAA;IAAA,CAAAlH,aAAA,GAAAoB,CAAA,QAAG,EAAE,GAAG,EAAE,GAAGoH,IAAI,CAACsB,KAAK,CAACD,QAAQ,GAAG,IAAI,CAAC,EAAC,CAAC;IAC1D,MAAMzC,cAAc;IAAA;IAAA,CAAApH,aAAA,GAAAoB,CAAA,QAAGoH,IAAI,CAACC,GAAG,CAAC,GAAG,EAAEoB,QAAQ,GAAG,IAAI,CAAC,EAAC,CAAC;IACvD,MAAMvC,eAAe;IAAA;IAAA,CAAAtH,aAAA,GAAAoB,CAAA,QAAG0D,cAAc,CAAC2B,KAAK,GAAG,IAAI,IAAIP,aAAa,GAAG,IAAI,CAAC,EAAC,CAAC;IAAA;IAAAlG,aAAA,GAAAoB,CAAA;IAE9E,OAAO;MACL8F,UAAU,EAAEsB,IAAI,CAACC,GAAG,CAAC,EAAE,EAAED,IAAI,CAACE,GAAG,CAAC,EAAE,EAAExB,UAAU,CAAC,CAAC;MAClDE,cAAc,EAAEoB,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,GAAG,CAAC,GAAG,EAAEtB,cAAc,CAAC,CAAC;MAC5DE,eAAe,EAAEkB,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,GAAG,CAAC,GAAG,EAAEpB,eAAe,CAAC,CAAC;MAC9DC,kBAAkB,EAAE,GAAG;MAAE;MACzBC,gBAAgB,EAAE,GAAG,CAAC;KACvB;EACH;EAEA;;;EAGQ,OAAOhC,mBAAmBA,CAChClD,mBAAwC,EACxC2C,cAAmB;IAAA;IAAAjF,aAAA,GAAAqB,CAAA;IAEnB;IACA,MAAM0I,aAAa;IAAA;IAAA,CAAA/J,aAAA,GAAAoB,CAAA,QAAG,IAAI,EAAC,CAAC;IAC5B,MAAM4I,iBAAiB;IAAA;IAAA,CAAAhK,aAAA,GAAAoB,CAAA,QAAG,IAAI,EAAC,CAAC;IAChC,MAAM6I,YAAY;IAAA;IAAA,CAAAjK,aAAA,GAAAoB,CAAA,QAAGoH,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,GAAG,GAAG,CAACsB,aAAa,GAAGC,iBAAiB,IAAI,GAAG,CAAC;IAEjF,IAAIE,YAA0B;IAAC;IAAAlK,aAAA,GAAAoB,CAAA;IAC/B,IAAI6I,YAAY,IAAI,EAAE,EAAE;MAAA;MAAAjK,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA8I,YAAY,GAAGjI,qBAAA,CAAAkI,YAAY,CAACC,SAAS;IAAA,CAAC,MACzD;MAAA;MAAApK,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA,IAAI6I,YAAY,IAAI,EAAE,EAAE;QAAA;QAAAjK,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QAAA8I,YAAY,GAAGjI,qBAAA,CAAAkI,YAAY,CAACE,IAAI;MAAA,CAAC,MACzD;QAAA;QAAArK,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QAAA,IAAI6I,YAAY,IAAI,EAAE,EAAE;UAAA;UAAAjK,aAAA,GAAAsB,CAAA;UAAAtB,aAAA,GAAAoB,CAAA;UAAA8I,YAAY,GAAGjI,qBAAA,CAAAkI,YAAY,CAACG,UAAU;QAAA,CAAC,MAC/D;UAAA;UAAAtK,aAAA,GAAAsB,CAAA;UAAAtB,aAAA,GAAAoB,CAAA;UAAA,IAAI6I,YAAY,IAAI,EAAE,EAAE;YAAA;YAAAjK,aAAA,GAAAsB,CAAA;YAAAtB,aAAA,GAAAoB,CAAA;YAAA8I,YAAY,GAAGjI,qBAAA,CAAAkI,YAAY,CAACI,IAAI;UAAA,CAAC,MACzD;YAAA;YAAAvK,aAAA,GAAAsB,CAAA;YAAAtB,aAAA,GAAAoB,CAAA;YAAA8I,YAAY,GAAGjI,qBAAA,CAAAkI,YAAY,CAACK,QAAQ;UAAA;QAAA;MAAA;IAAA;IAAC;IAAAxK,aAAA,GAAAoB,CAAA;IAE1C,OAAO;MACL6I,YAAY;MACZF,aAAa;MACbC,iBAAiB;MACjBE,YAAY;MACZO,aAAa,EAAEP,YAAY,KAAKjI,qBAAA,CAAAkI,YAAY,CAACK,QAAQ;MAAA;MAAA,CAAAxK,aAAA,GAAAsB,CAAA,WAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC;MAAA;MAAA,CAAAtB,aAAA,GAAAsB,CAAA,WAAG,EAAE;MACjFoJ,sBAAsB,EAAE,IAAI,CAACC,8BAA8B,CAACT,YAAY;KACzE;EACH;EAEA;;;EAGQ,OAAOS,8BAA8BA,CAACC,KAAmB;IAAA;IAAA5K,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAC/D,QAAQwJ,KAAK;MACX,KAAK3I,qBAAA,CAAAkI,YAAY,CAACK,QAAQ;QAAA;QAAAxK,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACxB,OAAO,CACL,uCAAuC,EACvC,oCAAoC,EACpC,mCAAmC,EACnC,qCAAqC,CACtC;MACH,KAAKa,qBAAA,CAAAkI,YAAY,CAACI,IAAI;QAAA;QAAAvK,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACpB,OAAO,CACL,gCAAgC,EAChC,yBAAyB,EACzB,wBAAwB,EACxB,4BAA4B,CAC7B;MACH,KAAKa,qBAAA,CAAAkI,YAAY,CAACG,UAAU;QAAA;QAAAtK,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QAC1B,OAAO,CACL,2CAA2C,EAC3C,mCAAmC,EACnC,+BAA+B,CAChC;MACH;QAAA;QAAApB,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACE,OAAO,CAAC,4CAA4C,CAAC;IACzD;EACF;EAEA;;;EAGQ,OAAOsD,4BAA4BA,CAACpC,mBAAwC;IAAA;IAAAtC,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAClF;IACA;IACA,OAAO,CACL;MACEuB,EAAE,EAAE,aAAa;MACjBM,MAAM,EAAE,GAAG;MACX4H,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,kBAAkB;MAC5BC,SAAS,EAAE,MAAM;MACjBjC,OAAO,EAAExG,mBAAmB,CAAC6D,gBAAgB,CAACD,aAAa;MAC3D8E,QAAQ,EAAE;KACX,CACF;EACH;EAEA;;;EAGQ,OAAOtI,kBAAkBA,CAACqB,QAAgB;IAAA;IAAA/D,aAAA,GAAAqB,CAAA;IAChD,MAAMuB,SAAS;IAAA;IAAA,CAAA5C,aAAA,GAAAoB,CAAA,QAAGyB,IAAI,CAACoI,GAAG,EAAE;IAC5B,MAAMC,MAAM;IAAA;IAAA,CAAAlL,aAAA,GAAAoB,CAAA,QAAGoH,IAAI,CAAC0C,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;IAAC;IAAApL,aAAA,GAAAoB,CAAA;IAC1D,OAAO,YAAY2C,QAAQ,IAAInB,SAAS,IAAIsI,MAAM,EAAE;EACtD;EAEA;;;EAGQ,OAAO/H,0BAA0BA,CAAA;IAAA;IAAAnD,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACvC,OAAO;MACLiK,SAAS,EAAE;QACTC,SAAS,EAAE,IAAIzI,IAAI,EAAE;QACrB0I,OAAO,EAAE,IAAI1I,IAAI,EAAE;QACnB2I,QAAQ,EAAE,CAAC;QACX5D,KAAK,EAAE;OACR;MACD6D,cAAc,EAAExJ,qBAAA,CAAAyJ,cAAc,CAACC,MAAM;MACrCC,cAAc,EAAE,CAAC;MACjBC,gBAAgB,EAAE,EAAE;MACpBC,SAAS,EAAE,EAAE;MACbC,kBAAkB,EAAE;QAClBC,eAAe,EAAE,EAAE;QACnBC,oBAAoB,EAAE,EAAE;QACxBC,kBAAkB,EAAE;UAAEhE,UAAU,EAAE,CAAC;UAAEC,UAAU,EAAE,CAAC;UAAEC,eAAe,EAAE;QAAC,CAAE;QACxE+D,eAAe,EAAE;UACfC,SAAS,EAAE,mBAA4B;UACvCrE,QAAQ,EAAE,CAAC;UACXsE,gBAAgB,EAAE,IAAIxJ,IAAI,EAAE;UAC5ByJ,UAAU,EAAE,CAAC;UACbC,eAAe,EAAE;SAClB;QACDC,UAAU,EAAE,EAAE;QACdC,SAAS,EAAE;OACZ;MACDC,eAAe,EAAE;QACfC,sBAAsB,EAAE,GAAG;QAAE;QAC7BC,oBAAoB,EAAE,EAAE;QACxBC,kBAAkB,EAAE,EAAE;QACtBC,iBAAiB,EAAE;UACjBC,qBAAqB,EAAE;YAAEC,iBAAiB,EAAE,CAAC;YAAEC,cAAc,EAAE,CAAC;YAAEC,UAAU,EAAE;UAAI,CAAE;UACpFC,qBAAqB,EAAE;YAAEH,iBAAiB,EAAE,CAAC,EAAE;YAAEC,cAAc,EAAE,CAAC,CAAC;YAAEC,UAAU,EAAE;UAAI,CAAE;UACvFE,mBAAmB,EAAE;YAAEJ,iBAAiB,EAAE,CAAC,EAAE;YAAEC,cAAc,EAAE,CAAC,CAAC;YAAEC,UAAU,EAAE;UAAK;SACrF;QACDG,iBAAiB,EAAE;UACjBC,UAAU,EAAE,CAAC;UACbC,UAAU,EAAE,EAAE;UACdC,aAAa,EAAE,EAAE;UACjBpF,eAAe,EAAE,EAAE;UACnBqF,cAAc,EAAE,CAAC,qBAAqB,EAAE,6BAA6B;;;KAG1E;EACH;EAEA;;;EAGQ,aAAavK,oBAAoBA,CACvCV,cAAoC,EACpCkL,cAAkC;IAAA;IAAA1N,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAElC,IAAIoB,cAAc,CAACS,MAAM,GAAG,CAAC,EAAE;MAAA;MAAAjD,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAC7B,OAAO,IAAI,CAAC+B,0BAA0B,EAAE;IAC1C,CAAC;IAAA;IAAA;MAAAnD,aAAA,GAAAsB,CAAA;IAAA;IAED;IACA,MAAM0B,aAAa;IAAA;IAAA,CAAAhD,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAACuM,uBAAuB,CAACnL,cAAc,CAAC;IAElE;IACA,MAAMqJ,gBAAgB;IAAA;IAAA,CAAA7L,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAACwM,sBAAsB,CAACpL,cAAc,CAAC;IAEpE;IACA,MAAMsJ,SAAS;IAAA;IAAA,CAAA9L,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAACyM,eAAe,CAACrL,cAAc,EAAEkL,cAAc,CAAC;IAEtE;IACA,MAAM3B,kBAAkB;IAAA;IAAA,CAAA/L,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAAC0M,0BAA0B,CAACtL,cAAc,CAAC;IAE1E;IACA,MAAMkK,eAAe;IAAA;IAAA,CAAA1M,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAAC2M,wBAAwB,CAACvL,cAAc,CAAC;IAErE,MAAM6I,SAAS;IAAA;IAAA,CAAArL,aAAA,GAAAoB,CAAA,QAAG;MAChBkK,SAAS,EAAE9I,cAAc,CAAC,CAAC,CAAC,CAACiD,mBAAmB,CAAC7C,SAAS;MAC1D2I,OAAO,EAAEmC,cAAc,CAACjI,mBAAmB,CAAC7C,SAAS;MACrD4I,QAAQ,EAAEhJ,cAAc,CAACS,MAAM;MAC/B2E,KAAK,EAAE;KACR;IAAC;IAAA5H,aAAA,GAAAoB,CAAA;IAEF,OAAO;MACLiK,SAAS;MACTI,cAAc,EAAEzI,aAAa,CAACgL,SAAS;MACvCpC,cAAc,EAAE5I,aAAa,CAACiL,SAAS;MACvCpC,gBAAgB;MAChBC,SAAS;MACTC,kBAAkB;MAClBW;KACD;EACH;EAEA;;;EAGQ,OAAOiB,uBAAuBA,CAACnL,cAAoC;IAAA;IAAAxC,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAIzE,IAAIoB,cAAc,CAACS,MAAM,GAAG,CAAC,EAAE;MAAA;MAAAjD,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAC7B,OAAO;QAAE4M,SAAS,EAAE/L,qBAAA,CAAAyJ,cAAc,CAACC,MAAM;QAAEsC,SAAS,EAAE;MAAC,CAAE;IAC3D,CAAC;IAAA;IAAA;MAAAjO,aAAA,GAAAsB,CAAA;IAAA;IAED;IACA,MAAM4M,gBAAgB;IAAA;IAAA,CAAAlO,aAAA,GAAAoB,CAAA,QAAGoB,cAAc,CAAC2L,GAAG,CAACC,IAAI,IAAI;MAAA;MAAApO,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MAAA,OAAAgN,IAAI,CAACrH,gBAAgB,CAACY,KAAK;IAAL,CAAK,CAAC;IAChF,MAAM0G,CAAC;IAAA;IAAA,CAAArO,aAAA,GAAAoB,CAAA,QAAG8M,gBAAgB,CAACjL,MAAM;IACjC,MAAMqL,CAAC;IAAA;IAAA,CAAAtO,aAAA,GAAAoB,CAAA,QAAGmN,KAAK,CAACC,IAAI,CAAC;MAAEvL,MAAM,EAAEoL;IAAC,CAAE,EAAE,CAACI,CAAC,EAAEC,CAAC,KAAKA;MAAAA;MAAAA,uBAAA;MAAAA,uBAAA;MAAAA,MAAA,CAAAA,CAAC;IAAD,CAAC,CAAC;IAEhD,MAAMC,IAAI;IAAA;IAAA,CAAA3O,aAAA,GAAAoB,CAAA,QAAGkN,CAAC,CAACM,MAAM,CAAC,CAACC,CAAC,EAAEvN,CAAC,KAAK;MAAA;MAAAtB,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MAAA,OAAAyN,CAAC,GAAGvN,CAAC;IAAD,CAAC,EAAE,CAAC,CAAC;IACzC,MAAMwN,IAAI;IAAA;IAAA,CAAA9O,aAAA,GAAAoB,CAAA,QAAG8M,gBAAgB,CAACU,MAAM,CAAC,CAACC,CAAC,EAAEvN,CAAC,KAAK;MAAA;MAAAtB,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MAAA,OAAAyN,CAAC,GAAGvN,CAAC;IAAD,CAAC,EAAE,CAAC,CAAC;IACxD,MAAMyN,KAAK;IAAA;IAAA,CAAA/O,aAAA,GAAAoB,CAAA,QAAGkN,CAAC,CAACM,MAAM,CAAC,CAACI,GAAG,EAAEC,EAAE,EAAEP,CAAC,KAAK;MAAA;MAAA1O,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MAAA,OAAA4N,GAAG,GAAGC,EAAE,GAAGf,gBAAgB,CAACQ,CAAC,CAAC;IAAD,CAAC,EAAE,CAAC,CAAC;IACzE,MAAMQ,KAAK;IAAA;IAAA,CAAAlP,aAAA,GAAAoB,CAAA,QAAGkN,CAAC,CAACM,MAAM,CAAC,CAACI,GAAG,EAAEC,EAAE,KAAK;MAAA;MAAAjP,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MAAA,OAAA4N,GAAG,GAAGC,EAAE,GAAGA,EAAE;IAAF,CAAE,EAAE,CAAC,CAAC;IAErD,MAAME,KAAK;IAAA;IAAA,CAAAnP,aAAA,GAAAoB,CAAA,QAAG,CAACiN,CAAC,GAAGU,KAAK,GAAGJ,IAAI,GAAGG,IAAI,KAAKT,CAAC,GAAGa,KAAK,GAAGP,IAAI,GAAGA,IAAI,CAAC;IACnE,MAAMV,SAAS;IAAA;IAAA,CAAAjO,aAAA,GAAAoB,CAAA,SAAGoH,IAAI,CAAC4G,GAAG,CAACD,KAAK,CAAC;IAEjC,IAAInB,SAAyB;IAAC;IAAAhO,aAAA,GAAAoB,CAAA;IAC9B,IAAI6M,SAAS,GAAG,GAAG,EAAE;MAAA;MAAAjO,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACnB4M,SAAS,GAAG/L,qBAAA,CAAAyJ,cAAc,CAACC,MAAM;IACnC,CAAC,MAAM;MAAA;MAAA3L,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA,IAAI+N,KAAK,GAAG,CAAC,EAAE;QAAA;QAAAnP,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACpB4M,SAAS,GAAG/L,qBAAA,CAAAyJ,cAAc,CAAC2D,SAAS;MACtC,CAAC,MAAM;QAAA;QAAArP,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACL4M,SAAS,GAAG/L,qBAAA,CAAAyJ,cAAc,CAAC4D,SAAS;MACtC;IAAA;IAAC;IAAAtP,aAAA,GAAAoB,CAAA;IAED,OAAO;MAAE4M,SAAS;MAAEC;IAAS,CAAE;EACjC;EAEA;;;EAGQ,OAAOL,sBAAsBA,CAACpL,cAAoC;IAAA;IAAAxC,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACxE;IACA;IACA,OAAO,CACL;MACEmO,MAAM,EAAE,QAAiB;MACzBC,kBAAkB,EAAE,EAAE;MACtBC,oBAAoB,EAAE,CAAC;MACvBC,aAAa,EAAE,CAAC,sBAAsB,EAAE,qBAAqB;KAC9D,EACD;MACEH,MAAM,EAAE,QAAiB;MACzBC,kBAAkB,EAAE,EAAE;MACtBC,oBAAoB,EAAE,CAAC;MACvBC,aAAa,EAAE,CAAC,gBAAgB,EAAE,4BAA4B;KAC/D,CACF;EACH;EAEA;;;EAGQ,OAAO7B,eAAeA,CAC5BrL,cAAoC,EACpCkL,cAAkC;IAAA;IAAA1N,aAAA,GAAAqB,CAAA;IAElC,MAAMyK,SAAS;IAAA;IAAA,CAAA9L,aAAA,GAAAoB,CAAA,SAAyB,EAAE;IAE1C;IAAA;IAAApB,aAAA,GAAAoB,CAAA;IACA,IAAIoB,cAAc,CAACS,MAAM,GAAG,CAAC,EAAE;MAAA;MAAAjD,aAAA,GAAAsB,CAAA;MAC7B,MAAMqO,cAAc;MAAA;MAAA,CAAA3P,aAAA,GAAAoB,CAAA,SAAGoB,cAAc,CAACA,cAAc,CAACS,MAAM,GAAG,CAAC,CAAC,CAAC8D,gBAAgB,CAACY,KAAK;MACvF,MAAMiI,iBAAiB;MAAA;MAAA,CAAA5P,aAAA,GAAAoB,CAAA,SAAGsM,cAAc,CAAC3G,gBAAgB,CAACY,KAAK;MAC/D,MAAMkI,cAAc;MAAA;MAAA,CAAA7P,aAAA,GAAAoB,CAAA,SAAGuO,cAAc,GAAGC,iBAAiB;MAAC;MAAA5P,aAAA,GAAAoB,CAAA;MAE1D,IAAIyO,cAAc,GAAG,CAAC,EAAE;QAAA;QAAA7P,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QAAE;QACxB0K,SAAS,CAACgE,IAAI,CAAC;UACbnN,EAAE,EAAE,WAAWE,IAAI,CAACoI,GAAG,EAAE,EAAE;UAC3B8E,kBAAkB,EAAE,IAAIlN,IAAI,EAAE;UAC9BmN,WAAW,EAAE/N,qBAAA,CAAAgO,WAAW,CAACC,aAAa;UACtCC,QAAQ,EAAEN,cAAc,GAAG,EAAE;UAAA;UAAA,CAAA7P,aAAA,GAAAsB,CAAA,WAAG,UAAmB;UAAA;UAAA,CAAAtB,aAAA,GAAAsB,CAAA,WAAG,MAAe;UACrE8O,eAAe,EAAE,CAAC,kBAAkB,CAAC;UACrCC,kBAAkB,EAAER,cAAc;UAClCrE,QAAQ,EAAE,CAAC;UACX8E,cAAc,EAAE,CACd;YACEC,KAAK,EAAE,iBAAiB;YACxBC,WAAW,EAAE,EAAE;YACfC,QAAQ,EAAE,mBAA4B;YACtCC,eAAe,EAAE,CAAC,4BAA4B,EAAE,0BAA0B;WAC3E,EACD;YACEH,KAAK,EAAE,mBAAmB;YAC1BC,WAAW,EAAE,EAAE;YACfC,QAAQ,EAAE,mBAA4B;YACtCC,eAAe,EAAE,CAAC,wBAAwB,EAAE,yBAAyB;WACtE,CACF;UACDC,kBAAkB,EAAE,CAClB,0BAA0B,EAC1B,qBAAqB,EACrB,yBAAyB,CAC1B;UACDC,QAAQ,EAAE;SACX,CAAC;MACJ,CAAC;MAAA;MAAA;QAAA5Q,aAAA,GAAAsB,CAAA;MAAA;IACH,CAAC;IAAA;IAAA;MAAAtB,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAED,OAAO0K,SAAS;EAClB;EAEA;;;EAGQ,OAAOgC,0BAA0BA,CAACtL,cAAoC;IAAA;IAAAxC,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAC5E;IACA;IACA,OAAO;MACL4K,eAAe,EAAE,EAAE;MACnBC,oBAAoB,EAAE,CACpB;QACE4E,MAAM,EAAE,kBAAkB;QAC1BC,YAAY,EAAE,EAAE;QAChBC,cAAc,EAAE,EAAE;QAClBC,aAAa,EAAE,CAAC,GAAG;QACnBC,YAAY,EAAE;OACf,CACF;MACD/E,kBAAkB,EAAE;QAClBhE,UAAU,EAAE,EAAE;QACdC,UAAU,EAAE,EAAE;QACdC,eAAe,EAAE;OAClB;MACD+D,eAAe,EAAE;QACfC,SAAS,EAAE,aAAsB;QACjCrE,QAAQ,EAAE,EAAE;QACZsE,gBAAgB,EAAE,IAAIxJ,IAAI,EAAE;QAC5ByJ,UAAU,EAAE9J,cAAc,CAACS,MAAM;QACjCsJ,eAAe,EAAE;OAClB;MACDC,UAAU,EAAE,CACV;QACE0E,MAAM,EAAE,kBAAkB;QAC1BC,MAAM,EAAE,EAAE;QACVC,YAAY,EAAE,IAAI;QAClBC,oBAAoB,EAAE,CAAC,4BAA4B,EAAE,qBAAqB;OAC3E,CACF;MACD5E,SAAS,EAAE;KACZ;EACH;EAEA;;;EAGQ,OAAOsB,wBAAwBA,CAACvL,cAAoC;IAAA;IAAAxC,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAC1E;IACA,OAAO;MACLuL,sBAAsB,EAAE,GAAG;MAAE;MAC7BC,oBAAoB,EAAE,CACpB;QACE0E,WAAW,EAAE,UAAU;QACvBC,aAAa,EAAE,KAAc;QAC7B7E,eAAe,EAAE,GAAG;QACpB8E,gBAAgB,EAAE,EAAE;QACpBC,sBAAsB,EAAE,EAAE;QAC1BC,oBAAoB,EAAE;OACvB,CACF;MACD7E,kBAAkB,EAAE,CAClB;QACEqE,MAAM,EAAE,iBAAiB;QACzBC,MAAM,EAAE,EAAE;QACVC,YAAY,EAAE,KAAK;QACnBC,oBAAoB,EAAE,CAAC,6BAA6B;OACrD,EACD;QACEH,MAAM,EAAE,qBAAqB;QAC7BC,MAAM,EAAE,EAAE;QACVC,YAAY,EAAE,IAAI;QAClBC,oBAAoB,EAAE,CAAC,gCAAgC,EAAE,gBAAgB;OAC1E,CACF;MACDvE,iBAAiB,EAAE;QACjBC,qBAAqB,EAAE;UAAEC,iBAAiB,EAAE,CAAC;UAAEC,cAAc,EAAE,CAAC;UAAEC,UAAU,EAAE;QAAI,CAAE;QACpFC,qBAAqB,EAAE;UAAEH,iBAAiB,EAAE,CAAC,EAAE;UAAEC,cAAc,EAAE,CAAC,CAAC;UAAEC,UAAU,EAAE;QAAI,CAAE;QACvFE,mBAAmB,EAAE;UAAEJ,iBAAiB,EAAE,CAAC,EAAE;UAAEC,cAAc,EAAE,CAAC,CAAC;UAAEC,UAAU,EAAE;QAAK;OACrF;MACDG,iBAAiB,EAAE;QACjBC,UAAU,EAAE,CAAC;QACbC,UAAU,EAAE,EAAE;QACdC,aAAa,EAAE,EAAE;QACjBpF,eAAe,EAAE,EAAE;QACnBqF,cAAc,EAAE,CAAC,qBAAqB,EAAE,6BAA6B;;KAExE;EACH;EAEA;;;EAGQ,aAAapK,0BAA0BA,CAC7Cf,mBAAwC,EACxCQ,kBAAsC;IAAA;IAAA9C,aAAA,GAAAqB,CAAA;IAEtC;IACA;IACA,MAAM0F,gBAAgB;IAAA;IAAA,CAAA/G,aAAA,GAAAoB,CAAA,SAAG0B,kBAAkB,CAACiE,gBAAgB,CAACY,KAAK;IAClE,MAAMgK,eAAe;IAAA;IAAA,CAAA3R,aAAA,GAAAoB,CAAA,SAAG,EAAE,EAAC,CAAC;IAC5B,MAAMwQ,WAAW;IAAA;IAAA,CAAA5R,aAAA,GAAAoB,CAAA,SAAG,EAAE,EAAC,CAAC;IAAA;IAAApB,aAAA,GAAAoB,CAAA;IAExB,OAAO;MACLyQ,aAAa,EAAE5P,qBAAA,CAAA6P,aAAa,CAACC,gBAAgB;MAC7CC,eAAe,EAAE,6BAA6B;MAC9CC,iBAAiB,EAAElL,gBAAgB;MACnCmL,cAAc,EAAEP,eAAe;MAC/BQ,UAAU,EAAE,IAAI,CAACC,mBAAmB,CAACrL,gBAAgB,EAAE4K,eAAe,CAAC;MACvEU,cAAc,EAAEV,eAAe,GAAG5K,gBAAgB;MAClDuL,oBAAoB,EAAEV,WAAW,GAAG7K,gBAAgB;MACpDwL,cAAc,EAAE,CACd;QACExO,QAAQ,EAAE,kBAAkB;QAC5ByO,UAAU,EAAE,sBAAsB;QAClCC,iBAAiB,EAAE,EAAE;QACrBC,qBAAqB,EAAE;UACrBC,IAAI,EAAE,QAAiB;UACvBC,GAAG,EAAE,CAAC;UACNC,YAAY,EAAE,QAAiB;UAC/BC,WAAW,EAAE,IAAI;UACjBC,cAAc,EAAE;SACjB;QACDC,qBAAqB,EAAE,EAAE,GAAGjM;OAC7B;KAEJ;EACH;EAEA;;;EAGQ,OAAOqL,mBAAmBA,CAACzK,KAAa,EAAEsL,OAAe;IAAA;IAAAjT,aAAA,GAAAqB,CAAA;IAC/D;IACA;IACA,MAAM6R,iBAAiB;IAAA;IAAA,CAAAlT,aAAA,GAAAoB,CAAA,SAAG,CAAC;IAC3B,MAAM+R,MAAM;IAAA;IAAA,CAAAnT,aAAA,GAAAoB,CAAA,SAAG,CAACuG,KAAK,GAAGsL,OAAO,IAAIC,iBAAiB;IAEpD;IAAA;IAAAlT,aAAA,GAAAoB,CAAA;IACA,IAAI+R,MAAM,IAAI,CAAC,EAAE;MAAA;MAAAnT,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA,OAAO,EAAE;IAAA,CAAC;IAAA;IAAA;MAAApB,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAC3B,IAAI+R,MAAM,IAAI,CAAC,EAAE;MAAA;MAAAnT,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA,OAAO,EAAE;IAAA,CAAC;IAAA;IAAA;MAAApB,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAC3B,IAAI+R,MAAM,IAAI,CAAC,EAAE;MAAA;MAAAnT,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA,OAAO,EAAE,GAAI+R,MAAM,GAAG,EAAG;IAAA,CAAC;IAAA;IAAA;MAAAnT,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAC3C,IAAI+R,MAAM,IAAI,CAAC,CAAC,EAAE;MAAA;MAAAnT,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA,OAAO,EAAE,GAAI+R,MAAM,GAAG,EAAG;IAAA,CAAC;IAAA;IAAA;MAAAnT,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAC5C,IAAI+R,MAAM,IAAI,CAAC,CAAC,EAAE;MAAA;MAAAnT,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA,OAAO,EAAE;IAAA,CAAC;IAAA;IAAA;MAAApB,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAC5B,OAAO,CAAC;EACV;EAEA;;;EAGQ,aAAamC,uBAAuBA,CAC1CjB,mBAAwC,EACxCQ,kBAAsC;IAAA;IAAA9C,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAEtC;IACA,OAAO;MACLgS,iBAAiB,EAAE;QACjBzL,KAAK,EAAE7E,kBAAkB,CAACiE,gBAAgB,CAACY,KAAK;QAChDC,KAAK,EAAE,GAAG;QACVyL,iBAAiB,EAAE,YAAqB;QACxCtL,QAAQ,EAAE,GAAG;QACbnF,SAAS,EAAE,IAAIC,IAAI;OACpB;MACDyQ,qBAAqB,EAAE,CACrB;QACEhC,WAAW,EAAE,UAAU;QACvBC,aAAa,EAAE,KAAc;QAC7BjL,UAAU,EAAExD,kBAAkB,CAAC4D,aAAa,CAACiB,KAAK;QAClD4L,eAAe,EAAE,EAAE;QACnBC,iBAAiB,EAAE,IAAI;QACvBC,iBAAiB,EAAE;OACpB,CACF;MACDC,gBAAgB,EAAE,EAAE;MACpBC,gBAAgB,EAAE,EAAE;MACpBC,wBAAwB,EAAE,EAAE;MAC5BxQ,mBAAmB,EAAE;QACnByO,aAAa,EAAE5P,qBAAA,CAAA6P,aAAa,CAACC,gBAAgB;QAC7CC,eAAe,EAAE,oBAAoB;QACrCC,iBAAiB,EAAEnP,kBAAkB,CAACiE,gBAAgB,CAACY,KAAK;QAC5DuK,cAAc,EAAE,EAAE;QAClBC,UAAU,EAAE,EAAE;QACdE,cAAc,EAAE,CAAC;QACjBC,oBAAoB,EAAE,EAAE;QACxBC,cAAc,EAAE;;KAEnB;EACH;EAEA;;;EAGQ,aAAa9O,uBAAuBA,CAC1CX,kBAAsC,EACtCN,cAAqC;IAAA;IAAAxC,aAAA,GAAAqB,CAAA;IAErC,MAAMwS,MAAM;IAAA;IAAA,CAAA7T,aAAA,GAAAoB,CAAA,SAAuB,EAAE;IAErC;IAAA;IAAApB,aAAA,GAAAoB,CAAA;IACA,IAAI0B,kBAAkB,CAACiE,gBAAgB,CAACY,KAAK,GAAG,EAAE,EAAE;MAAA;MAAA3H,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAClDyS,MAAM,CAAC/D,IAAI,CAAC;QACVnN,EAAE,EAAE,SAASE,IAAI,CAACoI,GAAG,EAAE,aAAa;QACpC6I,SAAS,EAAE7R,qBAAA,CAAA8R,SAAS,CAACC,kBAAkB;QACvC7D,QAAQ,EAAElO,qBAAA,CAAAgS,aAAa,CAACnO,IAAI;QAC5B+K,MAAM,EAAE,kBAAkB;QAC1BC,YAAY,EAAEhO,kBAAkB,CAACiE,gBAAgB,CAACY,KAAK;QACvDuM,cAAc,EAAE,EAAE;QAClB7P,OAAO,EAAE,8CAA8C;QACvDzB,SAAS,EAAE,IAAIC,IAAI,EAAE;QACrBsR,YAAY,EAAE,KAAK;QACnBxD,kBAAkB,EAAE,CAClB,wBAAwB,EACxB,sBAAsB,EACtB,4BAA4B;OAE/B,CAAC;IACJ,CAAC;IAAA;IAAA;MAAA3Q,aAAA,GAAAsB,CAAA;IAAA;IAED;IAAAtB,aAAA,GAAAoB,CAAA;IACA,IAAI0B,kBAAkB,CAAC2C,mBAAmB,CAACkC,KAAK,GAAG,GAAG,EAAE;MAAA;MAAA3H,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACtDyS,MAAM,CAAC/D,IAAI,CAAC;QACVnN,EAAE,EAAE,SAASE,IAAI,CAACoI,GAAG,EAAE,WAAW;QAClC6I,SAAS,EAAE7R,qBAAA,CAAA8R,SAAS,CAACC,kBAAkB;QACvC7D,QAAQ,EAAElO,qBAAA,CAAAgS,aAAa,CAAC1N,MAAM;QAC9BsK,MAAM,EAAE,qBAAqB;QAC7BC,YAAY,EAAEhO,kBAAkB,CAAC2C,mBAAmB,CAACkC,KAAK;QAC1DuM,cAAc,EAAE,GAAG;QACnB7P,OAAO,EAAE,sCAAsC;QAC/CzB,SAAS,EAAE,IAAIC,IAAI,EAAE;QACrBsR,YAAY,EAAE,KAAK;QACnBxD,kBAAkB,EAAE,CAClB,yBAAyB,EACzB,yBAAyB,EACzB,iBAAiB;OAEpB,CAAC;IACJ,CAAC;IAAA;IAAA;MAAA3Q,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAED,OAAOyS,MAAM;EACf;EAEA;;;EAGQ,aAAalQ,kCAAkCA,CACrDb,kBAAsC,EACtCQ,kBAAsC,EACtCF,mBAAwC;IAAA;IAAApD,aAAA,GAAAqB,CAAA;IAExC,MAAMqC,eAAe;IAAA;IAAA,CAAA1D,aAAA,GAAAoB,CAAA,SAAgC,EAAE;IAEvD;IAAA;IAAApB,aAAA,GAAAoB,CAAA;IACA,IAAI0B,kBAAkB,CAACiE,gBAAgB,CAACY,KAAK,GAAG,EAAE,EAAE;MAAA;MAAA3H,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAClDsC,eAAe,CAACoM,IAAI,CAAC;QACnBnN,EAAE,EAAE,OAAOE,IAAI,CAACoI,GAAG,EAAE,aAAa;QAClChK,IAAI,EAAEgB,qBAAA,CAAAmS,kBAAkB,CAACC,YAAY;QACrCC,QAAQ,EAAErS,qBAAA,CAAAsS,sBAAsB,CAACzO,IAAI;QACrC0O,KAAK,EAAE,2BAA2B;QAClCC,WAAW,EAAE,2FAA2F;QACxGC,cAAc,EAAE;UACdC,aAAa,EAAE,EAAE;UACjBC,WAAW,EAAE,IAAI;UACjBC,sBAAsB,EAAE,EAAE;UAC1BC,iBAAiB,EAAE,IAAI;UACvBC,sBAAsB,EAAE;SACzB;QACDC,kBAAkB,EAAE,IAAI;QACxBC,aAAa,EAAE,EAAE;QACjBC,wBAAwB,EAAE,UAAmB;QAC7CC,eAAe,EAAE,CACf,6CAA6C,EAC7C,qBAAqB,EACrB,4BAA4B,CAC7B;QACDC,QAAQ,EAAE;OACX,CAAC;IACJ,CAAC;IAAA;IAAA;MAAApV,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAED,OAAOsC,eAAe;EACxB;EAEA;;;EAGQ,aAAaG,0BAA0BA,CAC7Cf,kBAAsC,EACtCR,mBAAwC;IAAA;IAAAtC,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAExC;IACA,OAAO;MACLiU,kBAAkB,EAAE,GAAG;MAAE;MACzBC,mBAAmB,EAAE,CACnB;QACEzE,MAAM,EAAE,kBAAkB;QAC1B0E,WAAW,EAAE,IAAI;QACjB5T,OAAO,EAAE,CAAC,mBAAmB,EAAE,yBAAyB,CAAC;QACzDyG,eAAe,EAAE;OAClB,CACF;MACDoN,mBAAmB,EAAE;QACnBC,UAAU,EAAE,CACV;UACEC,SAAS,EAAE,SAAS;UACpBC,WAAW,EAAE,GAAG;UAChBxE,MAAM,EAAE;SACT,CACF;QACDyE,UAAU,EAAE,CAAC,SAAS,EAAE,UAAU,CAAC;QACnCC,uBAAuB,EAAE,CAAC,sBAAsB,EAAE,mBAAmB;OACtE;MACDnS,eAAe,EAAE,CACf,8BAA8B,EAC9B,6BAA6B,EAC7B,6BAA6B;KAEhC;EACH;;;;AAx/BFoS,OAAA,CAAA1T,+BAAA,GAAAA,+BAAA;AAy/BC;AAAApC,aAAA,GAAAoB,CAAA;AAx/ByBgB,+BAAA,CAAA2T,OAAO,GAAG,OAAO;AAAC;AAAA/V,aAAA,GAAAoB,CAAA;AAClBgB,+BAAA,CAAA6B,cAAc,GAAG,IAAI+R,GAAG,EAA+B;AAAC;AAAAhW,aAAA,GAAAoB,CAAA;AACxDgB,+BAAA,CAAA6T,kBAAkB,GAAG,IAAID,GAAG,EAA+B", "ignoreList": []}