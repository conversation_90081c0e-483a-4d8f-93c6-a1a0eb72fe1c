972bfe3c910e4668262b613beb61c36f
"use strict";

/**
 * Environmental Impact Assessment Engine
 *
 * Comprehensive environmental impact assessment service for Phase 3 Priority 3: Advanced System Analysis Tools
 * Provides carbon footprint calculation, environmental compliance checking, sustainability metrics,
 * and green building certification support for HVAC duct systems.
 *
 * @version 3.0.0
 * <AUTHOR> Suite Development Team
 */
/* istanbul ignore next */
function cov_1ktvp498qw() {
  var path = "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\EnvironmentalImpactAssessmentEngine.ts";
  var hash = "0749e186314117226586e514e4b61f8edceb8545";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\EnvironmentalImpactAssessmentEngine.ts",
    statementMap: {
      "0": {
        start: {
          line: 12,
          column: 0
        },
        end: {
          line: 12,
          column: 62
        }
      },
      "1": {
        start: {
          line: 13,
          column: 0
        },
        end: {
          line: 13,
          column: 53
        }
      },
      "2": {
        start: {
          line: 14,
          column: 30
        },
        end: {
          line: 14,
          column: 68
        }
      },
      "3": {
        start: {
          line: 31,
          column: 8
        },
        end: {
          line: 63,
          column: 9
        }
      },
      "4": {
        start: {
          line: 32,
          column: 31
        },
        end: {
          line: 32,
          column: 78
        }
      },
      "5": {
        start: {
          line: 33,
          column: 30
        },
        end: {
          line: 33,
          column: 40
        }
      },
      "6": {
        start: {
          line: 35,
          column: 36
        },
        end: {
          line: 35,
          column: 140
        }
      },
      "7": {
        start: {
          line: 37,
          column: 42
        },
        end: {
          line: 37,
          column: 137
        }
      },
      "8": {
        start: {
          line: 39,
          column: 44
        },
        end: {
          line: 39,
          column: 144
        }
      },
      "9": {
        start: {
          line: 41,
          column: 48
        },
        end: {
          line: 41,
          column: 163
        }
      },
      "10": {
        start: {
          line: 43,
          column: 40
        },
        end: {
          line: 43,
          column: 131
        }
      },
      "11": {
        start: {
          line: 45,
          column: 36
        },
        end: {
          line: 45,
          column: 169
        }
      },
      "12": {
        start: {
          line: 46,
          column: 29
        },
        end: {
          line: 56,
          column: 13
        }
      },
      "13": {
        start: {
          line: 58,
          column: 12
        },
        end: {
          line: 58,
          column: 63
        }
      },
      "14": {
        start: {
          line: 59,
          column: 12
        },
        end: {
          line: 59,
          column: 28
        }
      },
      "15": {
        start: {
          line: 62,
          column: 12
        },
        end: {
          line: 62,
          column: 131
        }
      },
      "16": {
        start: {
          line: 69,
          column: 30
        },
        end: {
          line: 69,
          column: 80
        }
      },
      "17": {
        start: {
          line: 70,
          column: 40
        },
        end: {
          line: 70,
          column: 95
        }
      },
      "18": {
        start: {
          line: 72,
          column: 31
        },
        end: {
          line: 72,
          column: 75
        }
      },
      "19": {
        start: {
          line: 74,
          column: 37
        },
        end: {
          line: 74,
          column: 241
        }
      },
      "20": {
        start: {
          line: 76,
          column: 34
        },
        end: {
          line: 76,
          column: 92
        }
      },
      "21": {
        start: {
          line: 78,
          column: 37
        },
        end: {
          line: 78,
          column: 92
        }
      },
      "22": {
        start: {
          line: 80,
          column: 31
        },
        end: {
          line: 80,
          column: 276
        }
      },
      "23": {
        start: {
          line: 82,
          column: 34
        },
        end: {
          line: 104,
          column: 9
        }
      },
      "24": {
        start: {
          line: 106,
          column: 31
        },
        end: {
          line: 106,
          column: 99
        }
      },
      "25": {
        start: {
          line: 108,
          column: 36
        },
        end: {
          line: 108,
          column: 139
        }
      },
      "26": {
        start: {
          line: 110,
          column: 36
        },
        end: {
          line: 110,
          column: 127
        }
      },
      "27": {
        start: {
          line: 111,
          column: 8
        },
        end: {
          line: 119,
          column: 10
        }
      },
      "28": {
        start: {
          line: 125,
          column: 8
        },
        end: {
          line: 127,
          column: 9
        }
      },
      "29": {
        start: {
          line: 126,
          column: 12
        },
        end: {
          line: 126,
          column: 69
        }
      },
      "30": {
        start: {
          line: 129,
          column: 32
        },
        end: {
          line: 137,
          column: 9
        }
      },
      "31": {
        start: {
          line: 138,
          column: 8
        },
        end: {
          line: 138,
          column: 82
        }
      },
      "32": {
        start: {
          line: 144,
          column: 30
        },
        end: {
          line: 144,
          column: 80
        }
      },
      "33": {
        start: {
          line: 146,
          column: 35
        },
        end: {
          line: 153,
          column: 9
        }
      },
      "34": {
        start: {
          line: 155,
          column: 34
        },
        end: {
          line: 160,
          column: 87
        }
      },
      "35": {
        start: {
          line: 162,
          column: 40
        },
        end: {
          line: 162,
          column: 62
        }
      },
      "36": {
        start: {
          line: 163,
          column: 8
        },
        end: {
          line: 163,
          column: 203
        }
      },
      "37": {
        start: {
          line: 171,
          column: 32
        },
        end: {
          line: 172,
          column: 64
        }
      },
      "38": {
        start: {
          line: 173,
          column: 8
        },
        end: {
          line: 175,
          column: 9
        }
      },
      "39": {
        start: {
          line: 174,
          column: 12
        },
        end: {
          line: 174,
          column: 185
        }
      },
      "40": {
        start: {
          line: 176,
          column: 30
        },
        end: {
          line: 176,
          column: 80
        }
      },
      "41": {
        start: {
          line: 177,
          column: 34
        },
        end: {
          line: 177,
          column: 55
        }
      },
      "42": {
        start: {
          line: 178,
          column: 28
        },
        end: {
          line: 178,
          column: 32
        }
      },
      "43": {
        start: {
          line: 179,
          column: 20
        },
        end: {
          line: 179,
          column: 60
        }
      },
      "44": {
        start: {
          line: 180,
          column: 30
        },
        end: {
          line: 180,
          column: 61
        }
      },
      "45": {
        start: {
          line: 181,
          column: 32
        },
        end: {
          line: 181,
          column: 51
        }
      },
      "46": {
        start: {
          line: 182,
          column: 8
        },
        end: {
          line: 182,
          column: 195
        }
      },
      "47": {
        start: {
          line: 188,
          column: 8
        },
        end: {
          line: 194,
          column: 10
        }
      },
      "48": {
        start: {
          line: 201,
          column: 35
        },
        end: {
          line: 201,
          column: 58
        }
      },
      "49": {
        start: {
          line: 202,
          column: 35
        },
        end: {
          line: 202,
          column: 57
        }
      },
      "50": {
        start: {
          line: 203,
          column: 8
        },
        end: {
          line: 209,
          column: 10
        }
      },
      "51": {
        start: {
          line: 215,
          column: 30
        },
        end: {
          line: 215,
          column: 32
        }
      },
      "52": {
        start: {
          line: 217,
          column: 8
        },
        end: {
          line: 224,
          column: 11
        }
      },
      "53": {
        start: {
          line: 226,
          column: 8
        },
        end: {
          line: 233,
          column: 11
        }
      },
      "54": {
        start: {
          line: 235,
          column: 8
        },
        end: {
          line: 242,
          column: 11
        }
      },
      "55": {
        start: {
          line: 243,
          column: 8
        },
        end: {
          line: 243,
          column: 29
        }
      },
      "56": {
        start: {
          line: 249,
          column: 34
        },
        end: {
          line: 249,
          column: 64
        }
      },
      "57": {
        start: {
          line: 251,
          column: 27
        },
        end: {
          line: 256,
          column: 9
        }
      },
      "58": {
        start: {
          line: 257,
          column: 27
        },
        end: {
          line: 257,
          column: 107
        }
      },
      "59": {
        start: {
          line: 258,
          column: 8
        },
        end: {
          line: 266,
          column: 10
        }
      },
      "60": {
        start: {
          line: 273,
          column: 22
        },
        end: {
          line: 273,
          column: 41
        }
      },
      "61": {
        start: {
          line: 274,
          column: 8
        },
        end: {
          line: 275,
          column: 22
        }
      },
      "62": {
        start: {
          line: 275,
          column: 12
        },
        end: {
          line: 275,
          column: 22
        }
      },
      "63": {
        start: {
          line: 276,
          column: 8
        },
        end: {
          line: 277,
          column: 22
        }
      },
      "64": {
        start: {
          line: 277,
          column: 12
        },
        end: {
          line: 277,
          column: 22
        }
      },
      "65": {
        start: {
          line: 278,
          column: 8
        },
        end: {
          line: 279,
          column: 22
        }
      },
      "66": {
        start: {
          line: 279,
          column: 12
        },
        end: {
          line: 279,
          column: 22
        }
      },
      "67": {
        start: {
          line: 280,
          column: 8
        },
        end: {
          line: 281,
          column: 22
        }
      },
      "68": {
        start: {
          line: 281,
          column: 12
        },
        end: {
          line: 281,
          column: 22
        }
      },
      "69": {
        start: {
          line: 282,
          column: 8
        },
        end: {
          line: 283,
          column: 22
        }
      },
      "70": {
        start: {
          line: 283,
          column: 12
        },
        end: {
          line: 283,
          column: 22
        }
      },
      "71": {
        start: {
          line: 284,
          column: 8
        },
        end: {
          line: 284,
          column: 17
        }
      },
      "72": {
        start: {
          line: 290,
          column: 30
        },
        end: {
          line: 290,
          column: 80
        }
      },
      "73": {
        start: {
          line: 291,
          column: 40
        },
        end: {
          line: 291,
          column: 95
        }
      },
      "74": {
        start: {
          line: 293,
          column: 33
        },
        end: {
          line: 298,
          column: 9
        }
      },
      "75": {
        start: {
          line: 300,
          column: 32
        },
        end: {
          line: 305,
          column: 9
        }
      },
      "76": {
        start: {
          line: 307,
          column: 35
        },
        end: {
          line: 312,
          column: 9
        }
      },
      "77": {
        start: {
          line: 314,
          column: 35
        },
        end: {
          line: 314,
          column: 122
        }
      },
      "78": {
        start: {
          line: 316,
          column: 38
        },
        end: {
          line: 323,
          column: 9
        }
      },
      "79": {
        start: {
          line: 324,
          column: 8
        },
        end: {
          line: 331,
          column: 10
        }
      },
      "80": {
        start: {
          line: 338,
          column: 8
        },
        end: {
          line: 349,
          column: 9
        }
      },
      "81": {
        start: {
          line: 339,
          column: 12
        },
        end: {
          line: 339,
          column: 33
        }
      },
      "82": {
        start: {
          line: 341,
          column: 13
        },
        end: {
          line: 349,
          column: 9
        }
      },
      "83": {
        start: {
          line: 342,
          column: 12
        },
        end: {
          line: 342,
          column: 28
        }
      },
      "84": {
        start: {
          line: 344,
          column: 13
        },
        end: {
          line: 349,
          column: 9
        }
      },
      "85": {
        start: {
          line: 345,
          column: 12
        },
        end: {
          line: 345,
          column: 31
        }
      },
      "86": {
        start: {
          line: 348,
          column: 12
        },
        end: {
          line: 348,
          column: 28
        }
      },
      "87": {
        start: {
          line: 350,
          column: 8
        },
        end: {
          line: 355,
          column: 10
        }
      },
      "88": {
        start: {
          line: 361,
          column: 8
        },
        end: {
          line: 362,
          column: 22
        }
      },
      "89": {
        start: {
          line: 362,
          column: 12
        },
        end: {
          line: 362,
          column: 22
        }
      },
      "90": {
        start: {
          line: 363,
          column: 8
        },
        end: {
          line: 364,
          column: 22
        }
      },
      "91": {
        start: {
          line: 364,
          column: 12
        },
        end: {
          line: 364,
          column: 22
        }
      },
      "92": {
        start: {
          line: 365,
          column: 8
        },
        end: {
          line: 366,
          column: 22
        }
      },
      "93": {
        start: {
          line: 366,
          column: 12
        },
        end: {
          line: 366,
          column: 22
        }
      },
      "94": {
        start: {
          line: 367,
          column: 8
        },
        end: {
          line: 368,
          column: 22
        }
      },
      "95": {
        start: {
          line: 368,
          column: 12
        },
        end: {
          line: 368,
          column: 22
        }
      },
      "96": {
        start: {
          line: 369,
          column: 8
        },
        end: {
          line: 369,
          column: 17
        }
      },
      "97": {
        start: {
          line: 376,
          column: 8
        },
        end: {
          line: 387,
          column: 9
        }
      },
      "98": {
        start: {
          line: 377,
          column: 12
        },
        end: {
          line: 377,
          column: 33
        }
      },
      "99": {
        start: {
          line: 379,
          column: 13
        },
        end: {
          line: 387,
          column: 9
        }
      },
      "100": {
        start: {
          line: 380,
          column: 12
        },
        end: {
          line: 380,
          column: 28
        }
      },
      "101": {
        start: {
          line: 382,
          column: 13
        },
        end: {
          line: 387,
          column: 9
        }
      },
      "102": {
        start: {
          line: 383,
          column: 12
        },
        end: {
          line: 383,
          column: 31
        }
      },
      "103": {
        start: {
          line: 386,
          column: 12
        },
        end: {
          line: 386,
          column: 28
        }
      },
      "104": {
        start: {
          line: 388,
          column: 8
        },
        end: {
          line: 393,
          column: 10
        }
      },
      "105": {
        start: {
          line: 400,
          column: 39
        },
        end: {
          line: 400,
          column: 124
        }
      },
      "106": {
        start: {
          line: 400,
          column: 105
        },
        end: {
          line: 400,
          column: 120
        }
      },
      "107": {
        start: {
          line: 401,
          column: 8
        },
        end: {
          line: 406,
          column: 10
        }
      },
      "108": {
        start: {
          line: 412,
          column: 31
        },
        end: {
          line: 412,
          column: 82
        }
      },
      "109": {
        start: {
          line: 413,
          column: 8
        },
        end: {
          line: 414,
          column: 31
        }
      },
      "110": {
        start: {
          line: 414,
          column: 12
        },
        end: {
          line: 414,
          column: 31
        }
      },
      "111": {
        start: {
          line: 415,
          column: 8
        },
        end: {
          line: 416,
          column: 26
        }
      },
      "112": {
        start: {
          line: 416,
          column: 12
        },
        end: {
          line: 416,
          column: 26
        }
      },
      "113": {
        start: {
          line: 417,
          column: 8
        },
        end: {
          line: 418,
          column: 29
        }
      },
      "114": {
        start: {
          line: 418,
          column: 12
        },
        end: {
          line: 418,
          column: 29
        }
      },
      "115": {
        start: {
          line: 419,
          column: 8
        },
        end: {
          line: 419,
          column: 22
        }
      },
      "116": {
        start: {
          line: 426,
          column: 34
        },
        end: {
          line: 426,
          column: 91
        }
      },
      "117": {
        start: {
          line: 427,
          column: 30
        },
        end: {
          line: 427,
          column: 80
        }
      },
      "118": {
        start: {
          line: 428,
          column: 8
        },
        end: {
          line: 430,
          column: 9
        }
      },
      "119": {
        start: {
          line: 429,
          column: 12
        },
        end: {
          line: 429,
          column: 39
        }
      },
      "120": {
        start: {
          line: 431,
          column: 8
        },
        end: {
          line: 431,
          column: 36
        }
      },
      "121": {
        start: {
          line: 437,
          column: 30
        },
        end: {
          line: 437,
          column: 80
        }
      },
      "122": {
        start: {
          line: 439,
          column: 28
        },
        end: {
          line: 439,
          column: 47
        }
      },
      "123": {
        start: {
          line: 440,
          column: 33
        },
        end: {
          line: 440,
          column: 53
        }
      },
      "124": {
        start: {
          line: 441,
          column: 8
        },
        end: {
          line: 441,
          column: 46
        }
      },
      "125": {
        start: {
          line: 448,
          column: 29
        },
        end: {
          line: 448,
          column: 33
        }
      },
      "126": {
        start: {
          line: 449,
          column: 32
        },
        end: {
          line: 449,
          column: 36
        }
      },
      "127": {
        start: {
          line: 450,
          column: 29
        },
        end: {
          line: 450,
          column: 33
        }
      },
      "128": {
        start: {
          line: 451,
          column: 35
        },
        end: {
          line: 451,
          column: 39
        }
      },
      "129": {
        start: {
          line: 452,
          column: 38
        },
        end: {
          line: 452,
          column: 42
        }
      },
      "130": {
        start: {
          line: 453,
          column: 35
        },
        end: {
          line: 453,
          column: 39
        }
      },
      "131": {
        start: {
          line: 454,
          column: 8
        },
        end: {
          line: 456,
          column: 53
        }
      },
      "132": {
        start: {
          line: 463,
          column: 28
        },
        end: {
          line: 463,
          column: 88
        }
      },
      "133": {
        start: {
          line: 464,
          column: 28
        },
        end: {
          line: 464,
          column: 88
        }
      },
      "134": {
        start: {
          line: 465,
          column: 30
        },
        end: {
          line: 465,
          column: 77
        }
      },
      "135": {
        start: {
          line: 466,
          column: 30
        },
        end: {
          line: 466,
          column: 89
        }
      },
      "136": {
        start: {
          line: 467,
          column: 8
        },
        end: {
          line: 474,
          column: 10
        }
      },
      "137": {
        start: {
          line: 480,
          column: 8
        },
        end: {
          line: 481,
          column: 23
        }
      },
      "138": {
        start: {
          line: 481,
          column: 12
        },
        end: {
          line: 481,
          column: 23
        }
      },
      "139": {
        start: {
          line: 482,
          column: 8
        },
        end: {
          line: 483,
          column: 22
        }
      },
      "140": {
        start: {
          line: 483,
          column: 12
        },
        end: {
          line: 483,
          column: 22
        }
      },
      "141": {
        start: {
          line: 484,
          column: 8
        },
        end: {
          line: 485,
          column: 22
        }
      },
      "142": {
        start: {
          line: 485,
          column: 12
        },
        end: {
          line: 485,
          column: 22
        }
      },
      "143": {
        start: {
          line: 486,
          column: 8
        },
        end: {
          line: 487,
          column: 22
        }
      },
      "144": {
        start: {
          line: 487,
          column: 12
        },
        end: {
          line: 487,
          column: 22
        }
      },
      "145": {
        start: {
          line: 488,
          column: 8
        },
        end: {
          line: 489,
          column: 22
        }
      },
      "146": {
        start: {
          line: 489,
          column: 12
        },
        end: {
          line: 489,
          column: 22
        }
      },
      "147": {
        start: {
          line: 490,
          column: 8
        },
        end: {
          line: 490,
          column: 18
        }
      },
      "148": {
        start: {
          line: 496,
          column: 8
        },
        end: {
          line: 497,
          column: 23
        }
      },
      "149": {
        start: {
          line: 497,
          column: 12
        },
        end: {
          line: 497,
          column: 23
        }
      },
      "150": {
        start: {
          line: 498,
          column: 8
        },
        end: {
          line: 499,
          column: 22
        }
      },
      "151": {
        start: {
          line: 499,
          column: 12
        },
        end: {
          line: 499,
          column: 22
        }
      },
      "152": {
        start: {
          line: 500,
          column: 8
        },
        end: {
          line: 501,
          column: 22
        }
      },
      "153": {
        start: {
          line: 501,
          column: 12
        },
        end: {
          line: 501,
          column: 22
        }
      },
      "154": {
        start: {
          line: 502,
          column: 8
        },
        end: {
          line: 503,
          column: 22
        }
      },
      "155": {
        start: {
          line: 503,
          column: 12
        },
        end: {
          line: 503,
          column: 22
        }
      },
      "156": {
        start: {
          line: 504,
          column: 8
        },
        end: {
          line: 505,
          column: 22
        }
      },
      "157": {
        start: {
          line: 505,
          column: 12
        },
        end: {
          line: 505,
          column: 22
        }
      },
      "158": {
        start: {
          line: 506,
          column: 8
        },
        end: {
          line: 506,
          column: 18
        }
      },
      "159": {
        start: {
          line: 512,
          column: 30
        },
        end: {
          line: 512,
          column: 66
        }
      },
      "160": {
        start: {
          line: 514,
          column: 8
        },
        end: {
          line: 525,
          column: 9
        }
      },
      "161": {
        start: {
          line: 515,
          column: 12
        },
        end: {
          line: 515,
          column: 34
        }
      },
      "162": {
        start: {
          line: 517,
          column: 13
        },
        end: {
          line: 525,
          column: 9
        }
      },
      "163": {
        start: {
          line: 518,
          column: 12
        },
        end: {
          line: 518,
          column: 33
        }
      },
      "164": {
        start: {
          line: 520,
          column: 13
        },
        end: {
          line: 525,
          column: 9
        }
      },
      "165": {
        start: {
          line: 521,
          column: 12
        },
        end: {
          line: 521,
          column: 33
        }
      },
      "166": {
        start: {
          line: 524,
          column: 12
        },
        end: {
          line: 524,
          column: 33
        }
      },
      "167": {
        start: {
          line: 526,
          column: 8
        },
        end: {
          line: 526,
          column: 53
        }
      },
      "168": {
        start: {
          line: 532,
          column: 8
        },
        end: {
          line: 533,
          column: 31
        }
      },
      "169": {
        start: {
          line: 533,
          column: 12
        },
        end: {
          line: 533,
          column: 31
        }
      },
      "170": {
        start: {
          line: 534,
          column: 8
        },
        end: {
          line: 535,
          column: 31
        }
      },
      "171": {
        start: {
          line: 535,
          column: 12
        },
        end: {
          line: 535,
          column: 31
        }
      },
      "172": {
        start: {
          line: 536,
          column: 8
        },
        end: {
          line: 537,
          column: 26
        }
      },
      "173": {
        start: {
          line: 537,
          column: 12
        },
        end: {
          line: 537,
          column: 26
        }
      },
      "174": {
        start: {
          line: 538,
          column: 8
        },
        end: {
          line: 539,
          column: 26
        }
      },
      "175": {
        start: {
          line: 539,
          column: 12
        },
        end: {
          line: 539,
          column: 26
        }
      },
      "176": {
        start: {
          line: 540,
          column: 8
        },
        end: {
          line: 541,
          column: 26
        }
      },
      "177": {
        start: {
          line: 541,
          column: 12
        },
        end: {
          line: 541,
          column: 26
        }
      },
      "178": {
        start: {
          line: 542,
          column: 8
        },
        end: {
          line: 542,
          column: 27
        }
      },
      "179": {
        start: {
          line: 548,
          column: 22
        },
        end: {
          line: 548,
          column: 24
        }
      },
      "180": {
        start: {
          line: 549,
          column: 8
        },
        end: {
          line: 550,
          column: 44
        }
      },
      "181": {
        start: {
          line: 550,
          column: 12
        },
        end: {
          line: 550,
          column: 44
        }
      },
      "182": {
        start: {
          line: 551,
          column: 8
        },
        end: {
          line: 552,
          column: 43
        }
      },
      "183": {
        start: {
          line: 552,
          column: 12
        },
        end: {
          line: 552,
          column: 43
        }
      },
      "184": {
        start: {
          line: 553,
          column: 8
        },
        end: {
          line: 554,
          column: 46
        }
      },
      "185": {
        start: {
          line: 554,
          column: 12
        },
        end: {
          line: 554,
          column: 46
        }
      },
      "186": {
        start: {
          line: 555,
          column: 8
        },
        end: {
          line: 555,
          column: 21
        }
      },
      "187": {
        start: {
          line: 562,
          column: 32
        },
        end: {
          line: 562,
          column: 36
        }
      },
      "188": {
        start: {
          line: 563,
          column: 33
        },
        end: {
          line: 563,
          column: 55
        }
      },
      "189": {
        start: {
          line: 564,
          column: 8
        },
        end: {
          line: 565,
          column: 23
        }
      },
      "190": {
        start: {
          line: 565,
          column: 12
        },
        end: {
          line: 565,
          column: 23
        }
      },
      "191": {
        start: {
          line: 566,
          column: 29
        },
        end: {
          line: 566,
          column: 33
        }
      },
      "192": {
        start: {
          line: 567,
          column: 25
        },
        end: {
          line: 567,
          column: 112
        }
      },
      "193": {
        start: {
          line: 568,
          column: 8
        },
        end: {
          line: 568,
          column: 39
        }
      },
      "194": {
        start: {
          line: 574,
          column: 29
        },
        end: {
          line: 574,
          column: 60
        }
      },
      "195": {
        start: {
          line: 576,
          column: 8
        },
        end: {
          line: 584,
          column: 9
        }
      },
      "196": {
        start: {
          line: 577,
          column: 12
        },
        end: {
          line: 577,
          column: 36
        }
      },
      "197": {
        start: {
          line: 579,
          column: 13
        },
        end: {
          line: 584,
          column: 9
        }
      },
      "198": {
        start: {
          line: 580,
          column: 12
        },
        end: {
          line: 580,
          column: 41
        }
      },
      "199": {
        start: {
          line: 583,
          column: 12
        },
        end: {
          line: 583,
          column: 40
        }
      },
      "200": {
        start: {
          line: 586,
          column: 8
        },
        end: {
          line: 594,
          column: 9
        }
      },
      "201": {
        start: {
          line: 587,
          column: 12
        },
        end: {
          line: 587,
          column: 38
        }
      },
      "202": {
        start: {
          line: 589,
          column: 13
        },
        end: {
          line: 594,
          column: 9
        }
      },
      "203": {
        start: {
          line: 590,
          column: 12
        },
        end: {
          line: 590,
          column: 43
        }
      },
      "204": {
        start: {
          line: 593,
          column: 12
        },
        end: {
          line: 593,
          column: 42
        }
      },
      "205": {
        start: {
          line: 596,
          column: 8
        },
        end: {
          line: 604,
          column: 9
        }
      },
      "206": {
        start: {
          line: 597,
          column: 12
        },
        end: {
          line: 597,
          column: 45
        }
      },
      "207": {
        start: {
          line: 599,
          column: 13
        },
        end: {
          line: 604,
          column: 9
        }
      },
      "208": {
        start: {
          line: 600,
          column: 12
        },
        end: {
          line: 600,
          column: 50
        }
      },
      "209": {
        start: {
          line: 603,
          column: 12
        },
        end: {
          line: 603,
          column: 49
        }
      },
      "210": {
        start: {
          line: 605,
          column: 8
        },
        end: {
          line: 610,
          column: 10
        }
      },
      "211": {
        start: {
          line: 616,
          column: 26
        },
        end: {
          line: 616,
          column: 36
        }
      },
      "212": {
        start: {
          line: 617,
          column: 23
        },
        end: {
          line: 617,
          column: 65
        }
      },
      "213": {
        start: {
          line: 618,
          column: 8
        },
        end: {
          line: 618,
          column: 75
        }
      },
      "214": {
        start: {
          line: 621,
          column: 0
        },
        end: {
          line: 621,
          column: 82
        }
      },
      "215": {
        start: {
          line: 622,
          column: 0
        },
        end: {
          line: 622,
          column: 54
        }
      },
      "216": {
        start: {
          line: 623,
          column: 0
        },
        end: {
          line: 623,
          column: 68
        }
      },
      "217": {
        start: {
          line: 625,
          column: 0
        },
        end: {
          line: 647,
          column: 2
        }
      },
      "218": {
        start: {
          line: 649,
          column: 0
        },
        end: {
          line: 656,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 30,
            column: 4
          },
          end: {
            line: 30,
            column: 5
          }
        },
        loc: {
          start: {
            line: 30,
            column: 112
          },
          end: {
            line: 64,
            column: 5
          }
        },
        line: 30
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 68,
            column: 4
          },
          end: {
            line: 68,
            column: 5
          }
        },
        loc: {
          start: {
            line: 68,
            column: 111
          },
          end: {
            line: 120,
            column: 5
          }
        },
        line: 68
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 124,
            column: 4
          },
          end: {
            line: 124,
            column: 5
          }
        },
        loc: {
          start: {
            line: 124,
            column: 51
          },
          end: {
            line: 139,
            column: 5
          }
        },
        line: 124
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 143,
            column: 4
          },
          end: {
            line: 143,
            column: 5
          }
        },
        loc: {
          start: {
            line: 143,
            column: 65
          },
          end: {
            line: 164,
            column: 5
          }
        },
        line: 143
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 168,
            column: 4
          },
          end: {
            line: 168,
            column: 5
          }
        },
        loc: {
          start: {
            line: 168,
            column: 62
          },
          end: {
            line: 183,
            column: 5
          }
        },
        line: 168
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 187,
            column: 4
          },
          end: {
            line: 187,
            column: 5
          }
        },
        loc: {
          start: {
            line: 187,
            column: 85
          },
          end: {
            line: 195,
            column: 5
          }
        },
        line: 187
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 199,
            column: 4
          },
          end: {
            line: 199,
            column: 5
          }
        },
        loc: {
          start: {
            line: 199,
            column: 71
          },
          end: {
            line: 210,
            column: 5
          }
        },
        line: 199
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 214,
            column: 4
          },
          end: {
            line: 214,
            column: 5
          }
        },
        loc: {
          start: {
            line: 214,
            column: 98
          },
          end: {
            line: 244,
            column: 5
          }
        },
        line: 214
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 248,
            column: 4
          },
          end: {
            line: 248,
            column: 5
          }
        },
        loc: {
          start: {
            line: 248,
            column: 92
          },
          end: {
            line: 267,
            column: 5
          }
        },
        line: 248
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 271,
            column: 4
          },
          end: {
            line: 271,
            column: 5
          }
        },
        loc: {
          start: {
            line: 271,
            column: 60
          },
          end: {
            line: 285,
            column: 5
          }
        },
        line: 271
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 289,
            column: 4
          },
          end: {
            line: 289,
            column: 5
          }
        },
        loc: {
          start: {
            line: 289,
            column: 102
          },
          end: {
            line: 332,
            column: 5
          }
        },
        line: 289
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 336,
            column: 4
          },
          end: {
            line: 336,
            column: 5
          }
        },
        loc: {
          start: {
            line: 336,
            column: 45
          },
          end: {
            line: 356,
            column: 5
          }
        },
        line: 336
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 360,
            column: 4
          },
          end: {
            line: 360,
            column: 5
          }
        },
        loc: {
          start: {
            line: 360,
            column: 39
          },
          end: {
            line: 370,
            column: 5
          }
        },
        line: 360
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 374,
            column: 4
          },
          end: {
            line: 374,
            column: 5
          }
        },
        loc: {
          start: {
            line: 374,
            column: 47
          },
          end: {
            line: 394,
            column: 5
          }
        },
        line: 374
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 398,
            column: 4
          },
          end: {
            line: 398,
            column: 5
          }
        },
        loc: {
          start: {
            line: 398,
            column: 60
          },
          end: {
            line: 407,
            column: 5
          }
        },
        line: 398
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 400,
            column: 85
          },
          end: {
            line: 400,
            column: 86
          }
        },
        loc: {
          start: {
            line: 400,
            column: 105
          },
          end: {
            line: 400,
            column: 120
          }
        },
        line: 400
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 411,
            column: 4
          },
          end: {
            line: 411,
            column: 5
          }
        },
        loc: {
          start: {
            line: 411,
            column: 58
          },
          end: {
            line: 420,
            column: 5
          }
        },
        line: 411
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 424,
            column: 4
          },
          end: {
            line: 424,
            column: 5
          }
        },
        loc: {
          start: {
            line: 424,
            column: 52
          },
          end: {
            line: 432,
            column: 5
          }
        },
        line: 424
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 436,
            column: 4
          },
          end: {
            line: 436,
            column: 5
          }
        },
        loc: {
          start: {
            line: 436,
            column: 57
          },
          end: {
            line: 442,
            column: 5
          }
        },
        line: 436
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 446,
            column: 4
          },
          end: {
            line: 446,
            column: 5
          }
        },
        loc: {
          start: {
            line: 446,
            column: 60
          },
          end: {
            line: 457,
            column: 5
          }
        },
        line: 446
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 461,
            column: 4
          },
          end: {
            line: 461,
            column: 5
          }
        },
        loc: {
          start: {
            line: 461,
            column: 94
          },
          end: {
            line: 475,
            column: 5
          }
        },
        line: 461
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 479,
            column: 4
          },
          end: {
            line: 479,
            column: 5
          }
        },
        loc: {
          start: {
            line: 479,
            column: 37
          },
          end: {
            line: 491,
            column: 5
          }
        },
        line: 479
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 495,
            column: 4
          },
          end: {
            line: 495,
            column: 5
          }
        },
        loc: {
          start: {
            line: 495,
            column: 43
          },
          end: {
            line: 507,
            column: 5
          }
        },
        line: 495
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 511,
            column: 4
          },
          end: {
            line: 511,
            column: 5
          }
        },
        loc: {
          start: {
            line: 511,
            column: 54
          },
          end: {
            line: 527,
            column: 5
          }
        },
        line: 511
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 531,
            column: 4
          },
          end: {
            line: 531,
            column: 5
          }
        },
        loc: {
          start: {
            line: 531,
            column: 33
          },
          end: {
            line: 543,
            column: 5
          }
        },
        line: 531
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 547,
            column: 4
          },
          end: {
            line: 547,
            column: 5
          }
        },
        loc: {
          start: {
            line: 547,
            column: 77
          },
          end: {
            line: 556,
            column: 5
          }
        },
        line: 547
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 560,
            column: 4
          },
          end: {
            line: 560,
            column: 5
          }
        },
        loc: {
          start: {
            line: 560,
            column: 61
          },
          end: {
            line: 569,
            column: 5
          }
        },
        line: 560
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 573,
            column: 4
          },
          end: {
            line: 573,
            column: 5
          }
        },
        loc: {
          start: {
            line: 573,
            column: 60
          },
          end: {
            line: 611,
            column: 5
          }
        },
        line: 573
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 615,
            column: 4
          },
          end: {
            line: 615,
            column: 5
          }
        },
        loc: {
          start: {
            line: 615,
            column: 40
          },
          end: {
            line: 619,
            column: 5
          }
        },
        line: 615
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 62,
            column: 71
          },
          end: {
            line: 62,
            column: 127
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 62,
            column: 96
          },
          end: {
            line: 62,
            column: 109
          }
        }, {
          start: {
            line: 62,
            column: 112
          },
          end: {
            line: 62,
            column: 127
          }
        }],
        line: 62
      },
      "1": {
        loc: {
          start: {
            line: 125,
            column: 8
          },
          end: {
            line: 127,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 125,
            column: 8
          },
          end: {
            line: 127,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 125
      },
      "2": {
        loc: {
          start: {
            line: 138,
            column: 15
          },
          end: {
            line: 138,
            column: 81
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 138,
            column: 15
          },
          end: {
            line: 138,
            column: 51
          }
        }, {
          start: {
            line: 138,
            column: 55
          },
          end: {
            line: 138,
            column: 81
          }
        }],
        line: 138
      },
      "3": {
        loc: {
          start: {
            line: 171,
            column: 32
          },
          end: {
            line: 172,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 171,
            column: 32
          },
          end: {
            line: 171,
            column: 82
          }
        }, {
          start: {
            line: 172,
            column: 12
          },
          end: {
            line: 172,
            column: 64
          }
        }],
        line: 171
      },
      "4": {
        loc: {
          start: {
            line: 173,
            column: 8
          },
          end: {
            line: 175,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 173,
            column: 8
          },
          end: {
            line: 175,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 173
      },
      "5": {
        loc: {
          start: {
            line: 187,
            column: 69
          },
          end: {
            line: 187,
            column: 83
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 187,
            column: 80
          },
          end: {
            line: 187,
            column: 83
          }
        }],
        line: 187
      },
      "6": {
        loc: {
          start: {
            line: 274,
            column: 8
          },
          end: {
            line: 275,
            column: 22
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 274,
            column: 8
          },
          end: {
            line: 275,
            column: 22
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 274
      },
      "7": {
        loc: {
          start: {
            line: 276,
            column: 8
          },
          end: {
            line: 277,
            column: 22
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 276,
            column: 8
          },
          end: {
            line: 277,
            column: 22
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 276
      },
      "8": {
        loc: {
          start: {
            line: 278,
            column: 8
          },
          end: {
            line: 279,
            column: 22
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 278,
            column: 8
          },
          end: {
            line: 279,
            column: 22
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 278
      },
      "9": {
        loc: {
          start: {
            line: 280,
            column: 8
          },
          end: {
            line: 281,
            column: 22
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 280,
            column: 8
          },
          end: {
            line: 281,
            column: 22
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 280
      },
      "10": {
        loc: {
          start: {
            line: 282,
            column: 8
          },
          end: {
            line: 283,
            column: 22
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 282,
            column: 8
          },
          end: {
            line: 283,
            column: 22
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 282
      },
      "11": {
        loc: {
          start: {
            line: 338,
            column: 8
          },
          end: {
            line: 349,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 338,
            column: 8
          },
          end: {
            line: 349,
            column: 9
          }
        }, {
          start: {
            line: 341,
            column: 13
          },
          end: {
            line: 349,
            column: 9
          }
        }],
        line: 338
      },
      "12": {
        loc: {
          start: {
            line: 341,
            column: 13
          },
          end: {
            line: 349,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 341,
            column: 13
          },
          end: {
            line: 349,
            column: 9
          }
        }, {
          start: {
            line: 344,
            column: 13
          },
          end: {
            line: 349,
            column: 9
          }
        }],
        line: 341
      },
      "13": {
        loc: {
          start: {
            line: 344,
            column: 13
          },
          end: {
            line: 349,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 344,
            column: 13
          },
          end: {
            line: 349,
            column: 9
          }
        }, {
          start: {
            line: 347,
            column: 13
          },
          end: {
            line: 349,
            column: 9
          }
        }],
        line: 344
      },
      "14": {
        loc: {
          start: {
            line: 361,
            column: 8
          },
          end: {
            line: 362,
            column: 22
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 361,
            column: 8
          },
          end: {
            line: 362,
            column: 22
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 361
      },
      "15": {
        loc: {
          start: {
            line: 363,
            column: 8
          },
          end: {
            line: 364,
            column: 22
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 363,
            column: 8
          },
          end: {
            line: 364,
            column: 22
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 363
      },
      "16": {
        loc: {
          start: {
            line: 365,
            column: 8
          },
          end: {
            line: 366,
            column: 22
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 365,
            column: 8
          },
          end: {
            line: 366,
            column: 22
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 365
      },
      "17": {
        loc: {
          start: {
            line: 367,
            column: 8
          },
          end: {
            line: 368,
            column: 22
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 367,
            column: 8
          },
          end: {
            line: 368,
            column: 22
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 367
      },
      "18": {
        loc: {
          start: {
            line: 376,
            column: 8
          },
          end: {
            line: 387,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 376,
            column: 8
          },
          end: {
            line: 387,
            column: 9
          }
        }, {
          start: {
            line: 379,
            column: 13
          },
          end: {
            line: 387,
            column: 9
          }
        }],
        line: 376
      },
      "19": {
        loc: {
          start: {
            line: 379,
            column: 13
          },
          end: {
            line: 387,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 379,
            column: 13
          },
          end: {
            line: 387,
            column: 9
          }
        }, {
          start: {
            line: 382,
            column: 13
          },
          end: {
            line: 387,
            column: 9
          }
        }],
        line: 379
      },
      "20": {
        loc: {
          start: {
            line: 382,
            column: 13
          },
          end: {
            line: 387,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 382,
            column: 13
          },
          end: {
            line: 387,
            column: 9
          }
        }, {
          start: {
            line: 385,
            column: 13
          },
          end: {
            line: 387,
            column: 9
          }
        }],
        line: 382
      },
      "21": {
        loc: {
          start: {
            line: 413,
            column: 8
          },
          end: {
            line: 414,
            column: 31
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 413,
            column: 8
          },
          end: {
            line: 414,
            column: 31
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 413
      },
      "22": {
        loc: {
          start: {
            line: 415,
            column: 8
          },
          end: {
            line: 416,
            column: 26
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 415,
            column: 8
          },
          end: {
            line: 416,
            column: 26
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 415
      },
      "23": {
        loc: {
          start: {
            line: 417,
            column: 8
          },
          end: {
            line: 418,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 417,
            column: 8
          },
          end: {
            line: 418,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 417
      },
      "24": {
        loc: {
          start: {
            line: 428,
            column: 8
          },
          end: {
            line: 430,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 428,
            column: 8
          },
          end: {
            line: 430,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 428
      },
      "25": {
        loc: {
          start: {
            line: 480,
            column: 8
          },
          end: {
            line: 481,
            column: 23
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 480,
            column: 8
          },
          end: {
            line: 481,
            column: 23
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 480
      },
      "26": {
        loc: {
          start: {
            line: 482,
            column: 8
          },
          end: {
            line: 483,
            column: 22
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 482,
            column: 8
          },
          end: {
            line: 483,
            column: 22
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 482
      },
      "27": {
        loc: {
          start: {
            line: 484,
            column: 8
          },
          end: {
            line: 485,
            column: 22
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 484,
            column: 8
          },
          end: {
            line: 485,
            column: 22
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 484
      },
      "28": {
        loc: {
          start: {
            line: 486,
            column: 8
          },
          end: {
            line: 487,
            column: 22
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 486,
            column: 8
          },
          end: {
            line: 487,
            column: 22
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 486
      },
      "29": {
        loc: {
          start: {
            line: 488,
            column: 8
          },
          end: {
            line: 489,
            column: 22
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 488,
            column: 8
          },
          end: {
            line: 489,
            column: 22
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 488
      },
      "30": {
        loc: {
          start: {
            line: 496,
            column: 8
          },
          end: {
            line: 497,
            column: 23
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 496,
            column: 8
          },
          end: {
            line: 497,
            column: 23
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 496
      },
      "31": {
        loc: {
          start: {
            line: 498,
            column: 8
          },
          end: {
            line: 499,
            column: 22
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 498,
            column: 8
          },
          end: {
            line: 499,
            column: 22
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 498
      },
      "32": {
        loc: {
          start: {
            line: 500,
            column: 8
          },
          end: {
            line: 501,
            column: 22
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 500,
            column: 8
          },
          end: {
            line: 501,
            column: 22
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 500
      },
      "33": {
        loc: {
          start: {
            line: 502,
            column: 8
          },
          end: {
            line: 503,
            column: 22
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 502,
            column: 8
          },
          end: {
            line: 503,
            column: 22
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 502
      },
      "34": {
        loc: {
          start: {
            line: 504,
            column: 8
          },
          end: {
            line: 505,
            column: 22
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 504,
            column: 8
          },
          end: {
            line: 505,
            column: 22
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 504
      },
      "35": {
        loc: {
          start: {
            line: 514,
            column: 8
          },
          end: {
            line: 525,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 514,
            column: 8
          },
          end: {
            line: 525,
            column: 9
          }
        }, {
          start: {
            line: 517,
            column: 13
          },
          end: {
            line: 525,
            column: 9
          }
        }],
        line: 514
      },
      "36": {
        loc: {
          start: {
            line: 517,
            column: 13
          },
          end: {
            line: 525,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 517,
            column: 13
          },
          end: {
            line: 525,
            column: 9
          }
        }, {
          start: {
            line: 520,
            column: 13
          },
          end: {
            line: 525,
            column: 9
          }
        }],
        line: 517
      },
      "37": {
        loc: {
          start: {
            line: 520,
            column: 13
          },
          end: {
            line: 525,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 520,
            column: 13
          },
          end: {
            line: 525,
            column: 9
          }
        }, {
          start: {
            line: 523,
            column: 13
          },
          end: {
            line: 525,
            column: 9
          }
        }],
        line: 520
      },
      "38": {
        loc: {
          start: {
            line: 532,
            column: 8
          },
          end: {
            line: 533,
            column: 31
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 532,
            column: 8
          },
          end: {
            line: 533,
            column: 31
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 532
      },
      "39": {
        loc: {
          start: {
            line: 534,
            column: 8
          },
          end: {
            line: 535,
            column: 31
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 534,
            column: 8
          },
          end: {
            line: 535,
            column: 31
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 534
      },
      "40": {
        loc: {
          start: {
            line: 536,
            column: 8
          },
          end: {
            line: 537,
            column: 26
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 536,
            column: 8
          },
          end: {
            line: 537,
            column: 26
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 536
      },
      "41": {
        loc: {
          start: {
            line: 538,
            column: 8
          },
          end: {
            line: 539,
            column: 26
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 538,
            column: 8
          },
          end: {
            line: 539,
            column: 26
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 538
      },
      "42": {
        loc: {
          start: {
            line: 540,
            column: 8
          },
          end: {
            line: 541,
            column: 26
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 540,
            column: 8
          },
          end: {
            line: 541,
            column: 26
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 540
      },
      "43": {
        loc: {
          start: {
            line: 549,
            column: 8
          },
          end: {
            line: 550,
            column: 44
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 549,
            column: 8
          },
          end: {
            line: 550,
            column: 44
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 549
      },
      "44": {
        loc: {
          start: {
            line: 551,
            column: 8
          },
          end: {
            line: 552,
            column: 43
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 551,
            column: 8
          },
          end: {
            line: 552,
            column: 43
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 551
      },
      "45": {
        loc: {
          start: {
            line: 553,
            column: 8
          },
          end: {
            line: 554,
            column: 46
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 553,
            column: 8
          },
          end: {
            line: 554,
            column: 46
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 553
      },
      "46": {
        loc: {
          start: {
            line: 564,
            column: 8
          },
          end: {
            line: 565,
            column: 23
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 564,
            column: 8
          },
          end: {
            line: 565,
            column: 23
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 564
      },
      "47": {
        loc: {
          start: {
            line: 576,
            column: 8
          },
          end: {
            line: 584,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 576,
            column: 8
          },
          end: {
            line: 584,
            column: 9
          }
        }, {
          start: {
            line: 579,
            column: 13
          },
          end: {
            line: 584,
            column: 9
          }
        }],
        line: 576
      },
      "48": {
        loc: {
          start: {
            line: 579,
            column: 13
          },
          end: {
            line: 584,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 579,
            column: 13
          },
          end: {
            line: 584,
            column: 9
          }
        }, {
          start: {
            line: 582,
            column: 13
          },
          end: {
            line: 584,
            column: 9
          }
        }],
        line: 579
      },
      "49": {
        loc: {
          start: {
            line: 586,
            column: 8
          },
          end: {
            line: 594,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 586,
            column: 8
          },
          end: {
            line: 594,
            column: 9
          }
        }, {
          start: {
            line: 589,
            column: 13
          },
          end: {
            line: 594,
            column: 9
          }
        }],
        line: 586
      },
      "50": {
        loc: {
          start: {
            line: 589,
            column: 13
          },
          end: {
            line: 594,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 589,
            column: 13
          },
          end: {
            line: 594,
            column: 9
          }
        }, {
          start: {
            line: 592,
            column: 13
          },
          end: {
            line: 594,
            column: 9
          }
        }],
        line: 589
      },
      "51": {
        loc: {
          start: {
            line: 596,
            column: 8
          },
          end: {
            line: 604,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 596,
            column: 8
          },
          end: {
            line: 604,
            column: 9
          }
        }, {
          start: {
            line: 599,
            column: 13
          },
          end: {
            line: 604,
            column: 9
          }
        }],
        line: 596
      },
      "52": {
        loc: {
          start: {
            line: 599,
            column: 13
          },
          end: {
            line: 604,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 599,
            column: 13
          },
          end: {
            line: 604,
            column: 9
          }
        }, {
          start: {
            line: 602,
            column: 13
          },
          end: {
            line: 604,
            column: 9
          }
        }],
        line: 599
      },
      "53": {
        loc: {
          start: {
            line: 608,
            column: 33
          },
          end: {
            line: 608,
            column: 93
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 608,
            column: 72
          },
          end: {
            line: 608,
            column: 79
          }
        }, {
          start: {
            line: 608,
            column: 82
          },
          end: {
            line: 608,
            column: 93
          }
        }],
        line: 608
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0,
      "210": 0,
      "211": 0,
      "212": 0,
      "213": 0,
      "214": 0,
      "215": 0,
      "216": 0,
      "217": 0,
      "218": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\EnvironmentalImpactAssessmentEngine.ts",
      mappings: ";AAAA;;;;;;;;;GASG;;;AAEH,qEAeqC;AAErC;;;;;;;;;;GAUG;AACH,MAAa,mCAAmC;IAuC9C;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,yBAAyB,CAC3C,mBAAwC,EACxC,cAA8B,EAC9B,gBAAmC,EACnC,YAA2B;QAE3B,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC;YACnE,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;YAE7B,6BAA6B;YAC7B,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,wBAAwB,CACzD,mBAAmB,EACnB,cAAc,EACd,gBAAgB,EAChB,YAAY,CACb,CAAC;YAEF,mCAAmC;YACnC,MAAM,qBAAqB,GAAG,MAAM,IAAI,CAAC,8BAA8B,CACrE,mBAAmB,EACnB,cAAc,EACd,eAAe,CAChB,CAAC;YAEF,mCAAmC;YACnC,MAAM,uBAAuB,GAAG,MAAM,IAAI,CAAC,6BAA6B,CACtE,mBAAmB,EACnB,cAAc,EACd,qBAAqB,CACtB,CAAC;YAEF,wCAAwC;YACxC,MAAM,2BAA2B,GAAG,MAAM,IAAI,CAAC,mCAAmC,CAChF,mBAAmB,EACnB,qBAAqB,EACrB,uBAAuB,CACxB,CAAC;YAEF,+BAA+B;YAC/B,MAAM,mBAAmB,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAC/D,mBAAmB,EACnB,cAAc,EACd,eAAe,CAChB,CAAC;YAEF,yCAAyC;YACzC,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,oCAAoC,CACrE,mBAAmB,EACnB,eAAe,EACf,qBAAqB,EACrB,uBAAuB,CACxB,CAAC;YAEF,MAAM,QAAQ,GAAgC;gBAC5C,EAAE,EAAE,UAAU;gBACd,QAAQ,EAAE,mBAAmB,CAAC,EAAE;gBAChC,iBAAiB,EAAE,SAAS;gBAC5B,eAAe;gBACf,qBAAqB;gBACrB,uBAAuB;gBACvB,2BAA2B;gBAC3B,mBAAmB;gBACnB,eAAe;aAChB,CAAC;YAEF,qBAAqB;YACrB,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YAEnD,OAAO,QAAQ,CAAC;QAElB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,2CAA2C,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;QACzH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,KAAK,CAAC,wBAAwB,CAC3C,mBAAwC,EACxC,cAA8B,EAC9B,gBAAmC,EACnC,YAA2B;QAE3B,MAAM,aAAa,GAAG,mBAAmB,CAAC,gBAAgB,CAAC,aAAa,CAAC;QACzE,MAAM,uBAAuB,GAAG,cAAc,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,KAAK,CAAC;QAExF,wCAAwC;QACxC,MAAM,cAAc,GAAG,IAAI,CAAC,yBAAyB,CAAC,YAAY,CAAC,CAAC;QAEpE,4CAA4C;QAC5C,MAAM,oBAAoB,GAAG,IAAI,CAAC,yBAAyB,CACzD,uBAAuB,GAAG,cAAc,EACxC,mCAAa,CAAC,OAAO,EACrB,+BAAS,CAAC,QAAQ,EAClB,mCAAa,CAAC,OAAO,CACtB,CAAC;QAEF,yCAAyC;QACzC,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,mBAAmB,CAAC,CAAC;QAErF,2DAA2D;QAC3D,MAAM,oBAAoB,GAAG,IAAI,CAAC,6BAA6B,CAAC,mBAAmB,CAAC,CAAC;QAErF,kBAAkB;QAClB,MAAM,cAAc,GAAG,IAAI,CAAC,yBAAyB,CACnD,oBAAoB,CAAC,KAAK,GAAG,iBAAiB,CAAC,KAAK,GAAG,oBAAoB,CAAC,KAAK,EACjF,mCAAa,CAAC,OAAO,EACrB,+BAAS,CAAC,QAAQ,EAClB,mCAAa,CAAC,OAAO,CACtB,CAAC;QAEF,gCAAgC;QAChC,MAAM,iBAAiB,GAAG;YACxB;gBACE,MAAM,EAAE,yBAAyB;gBACjC,SAAS,EAAE,oBAAoB,CAAC,KAAK;gBACrC,UAAU,EAAE,CAAC,oBAAoB,CAAC,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,GAAG,GAAG;gBACrE,KAAK,EAAE,mCAAa,CAAC,OAAO;gBAC5B,cAAc;aACf;YACD;gBACE,MAAM,EAAE,iBAAiB;gBACzB,SAAS,EAAE,iBAAiB,CAAC,KAAK;gBAClC,UAAU,EAAE,CAAC,iBAAiB,CAAC,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,GAAG,GAAG;gBAClE,KAAK,EAAE,mCAAa,CAAC,OAAO;gBAC5B,cAAc,EAAE,CAAC;aAClB;YACD;gBACE,MAAM,EAAE,qBAAqB;gBAC7B,SAAS,EAAE,oBAAoB,CAAC,KAAK;gBACrC,UAAU,EAAE,CAAC,oBAAoB,CAAC,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,GAAG,GAAG;gBACrE,KAAK,EAAE,mCAAa,CAAC,OAAO;gBAC5B,cAAc,EAAE,CAAC;aAClB;SACF,CAAC;QAEF,2BAA2B;QAC3B,MAAM,cAAc,GAAG,IAAI,CAAC,uBAAuB,CAAC,cAAc,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;QAE5F,uBAAuB;QACvB,MAAM,mBAAmB,GAAG,IAAI,CAAC,2BAA2B,CAC1D,cAAc,CAAC,KAAK,EACpB,oBAAoB,CAAC,KAAK,EAC1B,mBAAmB,CACpB,CAAC;QAEF,uBAAuB;QACvB,MAAM,mBAAmB,GAAG,IAAI,CAAC,4BAA4B,CAC3D,cAAc,CAAC,KAAK,EACpB,aAAa,EACb,mBAAmB,CACpB,CAAC;QAEF,OAAO;YACL,cAAc;YACd,oBAAoB;YACpB,iBAAiB;YACjB,iBAAiB;YACjB,cAAc;YACd,mBAAmB;YACnB,mBAAmB;SACpB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,yBAAyB,CAAC,YAA2B;QAClE,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,OAAO,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,eAAe,CAAC;QAC3D,CAAC;QAED,yCAAyC;QACzC,MAAM,eAAe,GAA8B;YACjD,OAAO,EAAE,IAAI,EAAE,4BAA4B;YAC3C,OAAO,EAAE,IAAI,EAAE,QAAQ;YACvB,OAAO,EAAE,IAAI,EAAE,WAAW;YAC1B,OAAO,EAAE,IAAI,EAAE,UAAU;YACzB,OAAO,EAAE,IAAI,EAAE,2BAA2B;YAC1C,OAAO,EAAE,IAAI,EAAE,6BAA6B;YAC5C,SAAS,EAAE,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,eAAe;SAC7D,CAAC;QAEF,OAAO,eAAe,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,eAAe,CAAC,SAAS,CAAC,CAAC;IAC5E,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,KAAK,CAAC,0BAA0B,CAC7C,mBAAwC;QAExC,MAAM,aAAa,GAAG,mBAAmB,CAAC,gBAAgB,CAAC,aAAa,CAAC;QAEzE,gCAAgC;QAChC,MAAM,kBAAkB,GAAG;YACzB,aAAa,EAAE,aAAa,GAAG,IAAI,CAAC,kBAAkB,CAAC,cAAc;YACrE,aAAa,EAAE,aAAa,GAAG,IAAI,CAAC,kBAAkB,CAAC,cAAc;YACrE,UAAU,EAAE,aAAa,GAAG,IAAI,CAAC,kBAAkB,CAAC,UAAU;YAC9D,YAAY,EAAE,aAAa,GAAG,IAAI,CAAC,kBAAkB,CAAC,aAAa;YACnE,QAAQ,EAAE,aAAa,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS;YAC3D,WAAW,EAAE,aAAa,GAAG,IAAI,CAAC,kBAAkB,CAAC,YAAY;SAClE,CAAC;QAEF,iDAAiD;QACjD,MAAM,iBAAiB,GACrB,CAAC,kBAAkB,CAAC,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,gBAAgB,CAAC;YACrF,CAAC,kBAAkB,CAAC,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,KAAK,CAAC;YAC1E,CAAC,kBAAkB,CAAC,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,UAAU,CAAC;YAC5E,CAAC,kBAAkB,CAAC,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,KAAK,CAAC;YACzE,CAAC,kBAAkB,CAAC,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,KAAK,CAAC;YACrE,CAAC,kBAAkB,CAAC,WAAW,GAAG,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAE9E,kDAAkD;QAClD,MAAM,uBAAuB,GAAG,iBAAiB,GAAG,EAAE,CAAC;QAEvD,OAAO,IAAI,CAAC,yBAAyB,CACnC,uBAAuB,EACvB,mCAAa,CAAC,OAAO,EACrB,+BAAS,CAAC,QAAQ,EAClB,mCAAa,CAAC,OAAO,CACtB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,6BAA6B,CAC1C,mBAAwC;QAExC,+CAA+C;QAC/C,+DAA+D;QAC/D,MAAM,eAAe,GAAG,mBAAmB,CAAC,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC;YACnD,mBAAmB,CAAC,UAAU,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;QAE5E,IAAI,CAAC,eAAe,EAAE,CAAC;YACrB,OAAO,IAAI,CAAC,yBAAyB,CAAC,CAAC,EAAE,mCAAa,CAAC,OAAO,EAAE,+BAAS,CAAC,QAAQ,EAAE,mCAAa,CAAC,OAAO,CAAC,CAAC;QAC7G,CAAC;QAED,MAAM,aAAa,GAAG,mBAAmB,CAAC,gBAAgB,CAAC,aAAa,CAAC;QACzE,MAAM,iBAAiB,GAAG,aAAa,GAAG,KAAK,CAAC,CAAC,sCAAsC;QACvF,MAAM,WAAW,GAAG,IAAI,CAAC,CAAC,oBAAoB;QAC9C,MAAM,GAAG,GAAG,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,eAAe;QAErE,MAAM,aAAa,GAAG,iBAAiB,GAAG,WAAW,CAAC;QACtD,MAAM,eAAe,GAAG,aAAa,GAAG,GAAG,CAAC;QAE5C,OAAO,IAAI,CAAC,yBAAyB,CACnC,eAAe,EACf,mCAAa,CAAC,OAAO,EACrB,+BAAS,CAAC,QAAQ,EAClB,mCAAa,CAAC,OAAO,CACtB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,yBAAyB,CACtC,KAAa,EACb,KAAoB,EACpB,SAAoB,EACpB,KAAoB,EACpB,WAAmB,GAAG;QAEtB,OAAO;YACL,KAAK;YACL,KAAK;YACL,SAAS;YACT,KAAK;YACL,QAAQ;SACT,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,uBAAuB,CACpC,gBAAwB,EACxB,gBAAmC;QAEnC,+BAA+B;QAC/B,MAAM,kBAAkB,GAAG,gBAAgB,GAAG,IAAI,CAAC,CAAC,gCAAgC;QACpF,MAAM,kBAAkB,GAAG,gBAAgB,GAAG,GAAG,CAAC,CAAC,0BAA0B;QAE7E,OAAO;YACL,gBAAgB;YAChB,cAAc,EAAE,WAAoB;YACpC,kBAAkB;YAClB,kBAAkB;YAClB,WAAW,EAAE,EAAE,CAAC,QAAQ;SACzB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,2BAA2B,CACxC,cAAsB,EACtB,oBAA4B,EAC5B,mBAAwC;QAExC,MAAM,aAAa,GAAwB,EAAE,CAAC;QAE9C,+BAA+B;QAC/B,aAAa,CAAC,IAAI,CAAC;YACjB,IAAI,EAAE,kBAAkB;YACxB,SAAS,EAAE,oBAAoB,GAAG,GAAG,EAAE,uBAAuB;YAC9D,IAAI,EAAE,oBAAoB,GAAG,IAAI,EAAE,gBAAgB;YACnD,cAAc,EAAE,wDAAwD;YACxE,aAAa,EAAE,CAAC,EAAE,QAAQ;YAC1B,kBAAkB,EAAE,CAAC,qBAAqB,EAAE,mBAAmB,EAAE,iBAAiB,CAAC;SACpF,CAAC,CAAC;QAEH,gCAAgC;QAChC,aAAa,CAAC,IAAI,CAAC;YACjB,IAAI,EAAE,mBAAmB;YACzB,SAAS,EAAE,oBAAoB,GAAG,IAAI,EAAE,0BAA0B;YAClE,IAAI,EAAE,CAAC,IAAI,EAAE,eAAe;YAC5B,cAAc,EAAE,iDAAiD;YACjE,aAAa,EAAE,CAAC,EAAE,QAAQ;YAC1B,kBAAkB,EAAE,CAAC,yBAAyB,EAAE,sBAAsB,EAAE,yBAAyB,CAAC;SACnG,CAAC,CAAC;QAEH,mCAAmC;QACnC,aAAa,CAAC,IAAI,CAAC;YACjB,IAAI,EAAE,sBAAsB;YAC5B,SAAS,EAAE,cAAc,GAAG,GAAG,EAAE,uBAAuB;YACxD,IAAI,EAAE,cAAc,GAAG,KAAK,EAAE,iBAAiB;YAC/C,cAAc,EAAE,yCAAyC;YACzD,aAAa,EAAE,QAAQ,EAAE,uBAAuB;YAChD,kBAAkB,EAAE,CAAC,sBAAsB,EAAE,sBAAsB,EAAE,mBAAmB,CAAC;SAC1F,CAAC,CAAC;QAEH,OAAO,aAAa,CAAC;IACvB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,4BAA4B,CACzC,cAAsB,EACtB,aAAqB,EACrB,mBAAwC;QAExC,MAAM,iBAAiB,GAAG,cAAc,GAAG,aAAa,CAAC,CAAC,cAAc;QAExE,yCAAyC;QACzC,MAAM,UAAU,GAAG;YACjB,eAAe,EAAE,IAAI;YACrB,YAAY,EAAE,IAAI;YAClB,gBAAgB,EAAE,IAAI;YACtB,aAAa,EAAE,IAAI;SACpB,CAAC;QAEF,MAAM,UAAU,GAAG,IAAI,CAAC,4BAA4B,CAAC,iBAAiB,EAAE,UAAU,CAAC,eAAe,CAAC,CAAC;QAEpG,OAAO;YACL,aAAa,EAAE,kBAAkB;YACjC,gBAAgB,EAAE,iBAAiB;YACnC,kBAAkB,EAAE,UAAU,CAAC,eAAe;YAC9C,UAAU;YACV,oBAAoB,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,iBAAiB,GAAG,UAAU,CAAC,YAAY,CAAC;YAC9E,aAAa,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,iBAAiB,GAAG,UAAU,CAAC,gBAAgB,CAAC;YAC3E,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,iBAAiB,GAAG,UAAU,CAAC,aAAa,CAAC;SACtE,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,4BAA4B,CAAC,SAAiB,EAAE,OAAe;QAC5E,oCAAoC;QACpC,MAAM,KAAK,GAAG,SAAS,GAAG,OAAO,CAAC;QAElC,IAAI,KAAK,IAAI,GAAG;YAAE,OAAO,EAAE,CAAC,CAAC,SAAS;QACtC,IAAI,KAAK,IAAI,GAAG;YAAE,OAAO,EAAE,CAAC,CAAC,UAAU;QACvC,IAAI,KAAK,IAAI,GAAG;YAAE,OAAO,EAAE,CAAC,CAAC,UAAU;QACvC,IAAI,KAAK,IAAI,GAAG;YAAE,OAAO,EAAE,CAAC,CAAC,UAAU;QACvC,IAAI,KAAK,IAAI,GAAG;YAAE,OAAO,EAAE,CAAC,CAAC,aAAa;QAC1C,OAAO,CAAC,CAAC,CAAC,YAAY;IACxB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,KAAK,CAAC,8BAA8B,CACjD,mBAAwC,EACxC,cAA8B,EAC9B,eAAgC;QAEhC,MAAM,aAAa,GAAG,mBAAmB,CAAC,gBAAgB,CAAC,aAAa,CAAC;QACzE,MAAM,uBAAuB,GAAG,cAAc,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,KAAK,CAAC;QAExF,4BAA4B;QAC5B,MAAM,gBAAgB,GAAG;YACvB,gBAAgB,EAAE,cAAc,CAAC,iBAAiB,CAAC,gBAAgB,EAAE,QAAQ;YAC7E,sBAAsB,EAAE,uBAAuB,GAAG,CAAC,aAAa,GAAG,IAAI,CAAC,EAAE,eAAe;YACzF,gBAAgB,EAAE,cAAc,CAAC,iBAAiB,CAAC,gBAAgB,EAAE,IAAI;YACzE,mBAAmB,EAAE,IAAI,CAAC,4BAA4B,CAAC,cAAc,CAAC,iBAAiB,CAAC,gBAAgB,CAAC;SAC1G,CAAC;QAEF,2BAA2B;QAC3B,MAAM,eAAe,GAAG;YACtB,iBAAiB,EAAE,eAAe,CAAC,cAAc,CAAC,KAAK,GAAG,aAAa,EAAE,mBAAmB;YAC5F,oBAAoB,EAAE,eAAe,CAAC,oBAAoB,CAAC,KAAK,GAAG,uBAAuB,EAAE,cAAc;YAC1G,iBAAiB,EAAE,eAAe,CAAC,iBAAiB,CAAC,KAAK,GAAG,aAAa,EAAE,uBAAuB;YACnG,mBAAmB,EAAE,IAAI,CAAC,wBAAwB,CAAC,eAAe,CAAC,cAAc,CAAC,KAAK,GAAG,aAAa,CAAC;SACzG,CAAC;QAEF,8BAA8B;QAC9B,MAAM,kBAAkB,GAAG;YACzB,kBAAkB,EAAE,IAAI,CAAC,2BAA2B,CAAC,mBAAmB,CAAC;YACzE,UAAU,EAAE,IAAI,CAAC,mBAAmB,CAAC,mBAAmB,CAAC,EAAE,SAAS;YACpE,eAAe,EAAE,IAAI,CAAC,wBAAwB,CAAC,mBAAmB,CAAC,EAAE,UAAU;YAC/E,kBAAkB,EAAE,IAAI,CAAC,2BAA2B,CAAC,mBAAmB,CAAC,CAAC,IAAI;SAC/E,CAAC;QAEF,kCAAkC;QAClC,MAAM,kBAAkB,GAAG,IAAI,CAAC,2BAA2B,CACzD,gBAAgB,EAChB,eAAe,EACf,kBAAkB,CACnB,CAAC;QAEF,sCAAsC;QACtC,MAAM,qBAAqB,GAAG;YAC5B,sBAAsB,EAAE,IAAI;YAC5B,eAAe,EAAE,IAAI,CAAC,iCAAiC,CAAC,eAAe,CAAC,cAAc,CAAC,KAAK,CAAC;YAC7F,sBAAsB,EAAE,GAAG,EAAE,mBAAmB;YAChD,yBAAyB,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,GAAG,gBAAgB,CAAC,gBAAgB,CAAC,GAAG,GAAG,CAAC;YACzF,qBAAqB,EAAE,GAAG,EAAE,cAAc;YAC1C,wBAAwB,EAAE,EAAE,CAAC,iBAAiB;SAC/C,CAAC;QAEF,OAAO;YACL,gBAAgB;YAChB,eAAe;YACf,kBAAkB;YAClB,kBAAkB;YAClB,qBAAqB;YACrB,sBAAsB,EAAE,IAAI,CAAC,4BAA4B,CAAC,kBAAkB,CAAC;SAC9E,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,4BAA4B,CAAC,GAAW;QACrD,IAAI,MAAc,CAAC;QACnB,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC;YACf,MAAM,GAAG,WAAW,CAAC;QACvB,CAAC;aAAM,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC;YACtB,MAAM,GAAG,MAAM,CAAC;QAClB,CAAC;aAAM,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,MAAM,GAAG,SAAS,CAAC;QACrB,CAAC;aAAM,CAAC;YACN,MAAM,GAAG,MAAM,CAAC;QAClB,CAAC;QAED,OAAO;YACL,MAAM;YACN,UAAU,EAAE,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC;YAC5C,oBAAoB,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,EAAE,iCAAiC;YAC/E,eAAe,EAAE,GAAG;SACrB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,sBAAsB,CAAC,GAAW;QAC/C,IAAI,GAAG,IAAI,GAAG;YAAE,OAAO,EAAE,CAAC;QAC1B,IAAI,GAAG,IAAI,GAAG;YAAE,OAAO,EAAE,CAAC;QAC1B,IAAI,GAAG,IAAI,IAAI;YAAE,OAAO,EAAE,CAAC;QAC3B,IAAI,GAAG,IAAI,GAAG;YAAE,OAAO,EAAE,CAAC;QAC1B,OAAO,CAAC,CAAC;IACX,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,wBAAwB,CAAC,SAAiB;QACvD,IAAI,MAAc,CAAC;QACnB,IAAI,SAAS,IAAI,IAAI,EAAE,CAAC;YACtB,MAAM,GAAG,WAAW,CAAC;QACvB,CAAC;aAAM,IAAI,SAAS,IAAI,IAAI,EAAE,CAAC;YAC7B,MAAM,GAAG,MAAM,CAAC;QAClB,CAAC;aAAM,IAAI,SAAS,IAAI,IAAI,EAAE,CAAC;YAC7B,MAAM,GAAG,SAAS,CAAC;QACrB,CAAC;aAAM,CAAC;YACN,MAAM,GAAG,MAAM,CAAC;QAClB,CAAC;QAED,OAAO;YACL,MAAM;YACN,UAAU,EAAE,IAAI,CAAC,4BAA4B,CAAC,SAAS,EAAE,IAAI,CAAC;YAC9D,oBAAoB,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,GAAG,IAAI,CAAC;YACnD,eAAe,EAAE,IAAI;SACtB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,2BAA2B,CAAC,mBAAwC;QACjF,8CAA8C;QAC9C,MAAM,sBAAsB,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,SAAS,EAAE,EAAE,CAAC,GAAG,GAAG,SAAS,EAAE,CAAC,CAAC,CAAC;QAErH,OAAO;YACL,iBAAiB,EAAE,sBAAsB,EAAE,SAAS;YACpD,iBAAiB,EAAE,EAAE,EAAE,iCAAiC;YACxD,gBAAgB,EAAE,IAAI,CAAC,yBAAyB,CAAC,mBAAmB,CAAC;YACrE,sBAAsB,EAAE,KAAK,CAAC,yBAAyB;SACxD,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,yBAAyB,CAAC,mBAAwC;QAC/E,MAAM,cAAc,GAAG,mBAAmB,CAAC,gBAAgB,CAAC,cAAc,CAAC;QAE3E,IAAI,cAAc,IAAI,GAAG;YAAE,OAAO,WAAW,CAAC;QAC9C,IAAI,cAAc,IAAI,GAAG;YAAE,OAAO,MAAM,CAAC;QACzC,IAAI,cAAc,IAAI,GAAG;YAAE,OAAO,SAAS,CAAC;QAC5C,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,mBAAmB,CAAC,mBAAwC;QACzE,4EAA4E;QAC5E,MAAM,iBAAiB,GAAG,mBAAmB,CAAC,UAAU,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;QACpF,MAAM,aAAa,GAAG,mBAAmB,CAAC,gBAAgB,CAAC,aAAa,CAAC;QAEzE,IAAI,iBAAiB,EAAE,CAAC;YACtB,OAAO,aAAa,GAAG,GAAG,CAAC,CAAC,8BAA8B;QAC5D,CAAC;QAED,OAAO,aAAa,GAAG,IAAI,CAAC,CAAC,yCAAyC;IACxE,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,wBAAwB,CAAC,mBAAwC;QAC9E,MAAM,aAAa,GAAG,mBAAmB,CAAC,gBAAgB,CAAC,aAAa,CAAC;QAEzE,wDAAwD;QACxD,MAAM,WAAW,GAAG,aAAa,GAAG,GAAG,CAAC,CAAC,yBAAyB;QAClE,MAAM,gBAAgB,GAAG,aAAa,GAAG,IAAI,CAAC,CAAC,mCAAmC;QAElF,OAAO,WAAW,GAAG,gBAAgB,CAAC;IACxC,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,2BAA2B,CAAC,mBAAwC;QACjF,sDAAsD;QACtD,MAAM,YAAY,GAAG,IAAI,CAAC,CAAC,yBAAyB;QACpD,MAAM,eAAe,GAAG,IAAI,CAAC,CAAC,eAAe;QAC7C,MAAM,YAAY,GAAG,IAAI,CAAC,CAAC,qBAAqB;QAEhD,MAAM,kBAAkB,GAAG,IAAI,CAAC,CAAC,iBAAiB;QAClD,MAAM,qBAAqB,GAAG,IAAI,CAAC,CAAC,iBAAiB;QACrD,MAAM,kBAAkB,GAAG,IAAI,CAAC,CAAC,iBAAiB;QAElD,OAAO,CAAC,YAAY,GAAG,kBAAkB;YACjC,eAAe,GAAG,qBAAqB;YACvC,YAAY,GAAG,kBAAkB,CAAC,GAAG,GAAG,CAAC;IACnD,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,2BAA2B,CACxC,gBAAqB,EACrB,eAAoB,EACpB,kBAAuB;QAEvB,kCAAkC;QAClC,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;QACjF,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC;QACjF,MAAM,aAAa,GAAG,IAAI,CAAC,sBAAsB,CAAC,kBAAkB,CAAC,CAAC;QAEtE,MAAM,YAAY,GAAG,CAAC,WAAW,GAAG,GAAG,GAAG,WAAW,GAAG,GAAG,GAAG,aAAa,GAAG,GAAG,CAAC,CAAC;QAEnF,OAAO;YACL,YAAY;YACZ,WAAW;YACX,WAAW;YACX,aAAa;YACb,MAAM,EAAE,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC;YACzC,gBAAgB,EAAE,IAAI,CAAC,wBAAwB,CAAC,WAAW,EAAE,WAAW,EAAE,aAAa,CAAC;SACzF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,oBAAoB,CAAC,GAAW;QAC7C,IAAI,GAAG,IAAI,GAAG;YAAE,OAAO,GAAG,CAAC;QAC3B,IAAI,GAAG,IAAI,GAAG;YAAE,OAAO,EAAE,CAAC;QAC1B,IAAI,GAAG,IAAI,IAAI;YAAE,OAAO,EAAE,CAAC;QAC3B,IAAI,GAAG,IAAI,GAAG;YAAE,OAAO,EAAE,CAAC;QAC1B,IAAI,GAAG,IAAI,GAAG;YAAE,OAAO,EAAE,CAAC;QAC1B,OAAO,EAAE,CAAC;IACZ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,oBAAoB,CAAC,SAAiB;QACnD,IAAI,SAAS,IAAI,IAAI;YAAE,OAAO,GAAG,CAAC;QAClC,IAAI,SAAS,IAAI,IAAI;YAAE,OAAO,EAAE,CAAC;QACjC,IAAI,SAAS,IAAI,IAAI;YAAE,OAAO,EAAE,CAAC;QACjC,IAAI,SAAS,IAAI,IAAI;YAAE,OAAO,EAAE,CAAC;QACjC,IAAI,SAAS,IAAI,IAAI;YAAE,OAAO,EAAE,CAAC;QACjC,OAAO,EAAE,CAAC;IACZ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,sBAAsB,CAAC,kBAAuB;QAC3D,MAAM,aAAa,GAAG,kBAAkB,CAAC,iBAAiB,CAAC,CAAC,QAAQ;QAEpE,IAAI,eAAuB,CAAC;QAC5B,IAAI,kBAAkB,CAAC,gBAAgB,KAAK,WAAW,EAAE,CAAC;YACxD,eAAe,GAAG,GAAG,CAAC;QACxB,CAAC;aAAM,IAAI,kBAAkB,CAAC,gBAAgB,KAAK,MAAM,EAAE,CAAC;YAC1D,eAAe,GAAG,EAAE,CAAC;QACvB,CAAC;aAAM,IAAI,kBAAkB,CAAC,gBAAgB,KAAK,SAAS,EAAE,CAAC;YAC7D,eAAe,GAAG,EAAE,CAAC;QACvB,CAAC;aAAM,CAAC;YACN,eAAe,GAAG,EAAE,CAAC;QACvB,CAAC;QAED,OAAO,CAAC,aAAa,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,cAAc,CAAC,KAAa;QACzC,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,WAAW,CAAC;QACpC,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,WAAW,CAAC;QACpC,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,MAAM,CAAC;QAC/B,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,MAAM,CAAC;QAC/B,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,MAAM,CAAC;QAC/B,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,wBAAwB,CACrC,WAAmB,EACnB,WAAmB,EACnB,aAAqB;QAErB,MAAM,KAAK,GAAa,EAAE,CAAC;QAE3B,IAAI,WAAW,GAAG,EAAE;YAAE,KAAK,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACtD,IAAI,WAAW,GAAG,EAAE;YAAE,KAAK,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACrD,IAAI,aAAa,GAAG,EAAE;YAAE,KAAK,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAE1D,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,iCAAiC,CAAC,cAAsB;QACrE,+DAA+D;QAC/D,MAAM,eAAe,GAAG,IAAI,CAAC,CAAC,yCAAyC;QACvE,MAAM,gBAAgB,GAAG,cAAc,GAAG,KAAK,CAAC,CAAC,6BAA6B;QAE9E,IAAI,gBAAgB,IAAI,eAAe;YAAE,OAAO,GAAG,CAAC;QAEpD,MAAM,YAAY,GAAG,IAAI,CAAC,CAAC,0BAA0B;QACrD,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,YAAY,GAAG,gBAAgB,CAAC,GAAG,CAAC,YAAY,GAAG,eAAe,CAAC,GAAG,GAAG,CAAC,CAAC;QAEzG,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,4BAA4B,CAAC,kBAAsC;QAChF,MAAM,YAAY,GAAG,kBAAkB,CAAC,YAAY,CAAC;QAErD,IAAI,aAAqB,CAAC;QAC1B,IAAI,YAAY,IAAI,EAAE,EAAE,CAAC;YACvB,aAAa,GAAG,OAAO,CAAC;QAC1B,CAAC;aAAM,IAAI,YAAY,IAAI,EAAE,EAAE,CAAC;YAC9B,aAAa,GAAG,YAAY,CAAC;QAC/B,CAAC;aAAM,CAAC;YACN,aAAa,GAAG,WAAW,CAAC;QAC9B,CAAC;QAED,IAAI,eAAuB,CAAC;QAC5B,IAAI,YAAY,IAAI,EAAE,EAAE,CAAC;YACvB,eAAe,GAAG,OAAO,CAAC;QAC5B,CAAC;aAAM,IAAI,YAAY,IAAI,EAAE,EAAE,CAAC;YAC9B,eAAe,GAAG,YAAY,CAAC;QACjC,CAAC;aAAM,CAAC;YACN,eAAe,GAAG,WAAW,CAAC;QAChC,CAAC;QAED,IAAI,sBAA8B,CAAC;QACnC,IAAI,YAAY,IAAI,EAAE,EAAE,CAAC;YACvB,sBAAsB,GAAG,OAAO,CAAC;QACnC,CAAC;aAAM,IAAI,YAAY,IAAI,EAAE,EAAE,CAAC;YAC9B,sBAAsB,GAAG,YAAY,CAAC;QACxC,CAAC;aAAM,CAAC;YACN,sBAAsB,GAAG,WAAW,CAAC;QACvC,CAAC;QAED,OAAO;YACL,aAAa;YACb,eAAe;YACf,mBAAmB,EAAE,kBAAkB,CAAC,WAAW,IAAI,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,WAAW;YACjF,sBAAsB;SACvB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,kBAAkB,CAAC,QAAgB;QAChD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC1D,OAAO,0BAA0B,QAAQ,IAAI,SAAS,IAAI,MAAM,EAAE,CAAC;IACrE,CAAC;;AA7wBH,kFA8wBC;AA7wByB,2CAAO,GAAG,OAAO,CAAC;AAClB,uDAAmB,GAAG,IAAI,GAAG,EAAuC,CAAC;AAE7F,6CAA6C;AACrB,oDAAgB,GAAG;IACzC,WAAW,EAAE;QACX,eAAe,EAAE,GAAG,EAAE,cAAc;QACpC,IAAI,EAAE,IAAI;QACV,WAAW,EAAE,IAAI;QACjB,SAAS,EAAE,IAAI;QACf,OAAO,EAAE,KAAK;KACf;IACD,SAAS,EAAE;QACT,KAAK,EAAE,IAAI,EAAE,aAAa;QAC1B,QAAQ,EAAE,IAAI;QACd,MAAM,EAAE,IAAI;QACZ,gBAAgB,EAAE,GAAG;QACrB,eAAe,EAAE,GAAG;QACpB,UAAU,EAAE,GAAG;KAChB;IACD,YAAY,EAAE;QACZ,KAAK,EAAE,IAAI,EAAE,mBAAmB;QAChC,KAAK,EAAE,IAAI;QACX,GAAG,EAAE,GAAG;QACR,OAAO,EAAE,CAAC,CAAC,uBAAuB;KACnC;CACF,CAAC;AAEF,mCAAmC;AACX,sDAAkB,GAAG;IAC3C,cAAc,EAAE,GAAG,EAAE,mBAAmB;IACxC,cAAc,EAAE,GAAG;IACnB,UAAU,EAAE,GAAG;IACf,aAAa,EAAE,GAAG;IAClB,SAAS,EAAE,GAAG;IACd,YAAY,EAAE,GAAG;CAClB,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\EnvironmentalImpactAssessmentEngine.ts"],
      sourcesContent: ["/**\r\n * Environmental Impact Assessment Engine\r\n * \r\n * Comprehensive environmental impact assessment service for Phase 3 Priority 3: Advanced System Analysis Tools\r\n * Provides carbon footprint calculation, environmental compliance checking, sustainability metrics,\r\n * and green building certification support for HVAC duct systems.\r\n * \r\n * @version 3.0.0\r\n * <AUTHOR> Suite Development Team\r\n */\r\n\r\nimport {\r\n  EnvironmentalImpactAnalysis,\r\n  CarbonFootprint,\r\n  SustainabilityMetrics,\r\n  GreenBuildingCompliance,\r\n  EnvironmentalCertification,\r\n  LifecycleAssessment,\r\n  EnvironmentalRecommendation,\r\n  EmissionMeasurement,\r\n  EmissionUnits,\r\n  EmissionScope,\r\n  SystemConfiguration,\r\n  EnergyAnalysis,\r\n  TimeFrame,\r\n  MeasurementSource\r\n} from './types/SystemAnalysisTypes';\r\n\r\n/**\r\n * Environmental Impact Assessment Engine\r\n * \r\n * Provides comprehensive environmental impact assessment capabilities including:\r\n * - Carbon footprint calculation and tracking\r\n * - Sustainability metrics and benchmarking\r\n * - Green building certification support (LEED, BREEAM, etc.)\r\n * - Environmental compliance checking\r\n * - Lifecycle environmental impact assessment\r\n * - Environmental improvement recommendations\r\n */\r\nexport class EnvironmentalImpactAssessmentEngine {\r\n  private static readonly VERSION = '3.0.0';\r\n  private static readonly ENVIRONMENTAL_CACHE = new Map<string, EnvironmentalImpactAnalysis>();\r\n  \r\n  // Carbon emission factors (kg CO2e per unit)\r\n  private static readonly EMISSION_FACTORS = {\r\n    ELECTRICITY: {\r\n      US_GRID_AVERAGE: 0.4, // kg CO2e/kWh\r\n      COAL: 0.82,\r\n      NATURAL_GAS: 0.35,\r\n      RENEWABLE: 0.02,\r\n      NUCLEAR: 0.012\r\n    },\r\n    MATERIALS: {\r\n      STEEL: 1.85, // kg CO2e/kg\r\n      ALUMINUM: 8.24,\r\n      COPPER: 2.95,\r\n      GALVANIZED_STEEL: 2.1,\r\n      STAINLESS_STEEL: 2.9,\r\n      INSULATION: 1.2\r\n    },\r\n    REFRIGERANTS: {\r\n      R410A: 2088, // kg CO2e/kg (GWP)\r\n      R134A: 1430,\r\n      R32: 675,\r\n      NATURAL: 1 // Natural refrigerants\r\n    }\r\n  };\r\n\r\n  // Material quantities (kg per CFM)\r\n  private static readonly MATERIAL_INTENSITY = {\r\n    DUCTWORK_STEEL: 0.8, // kg steel per CFM\r\n    FITTINGS_STEEL: 0.2,\r\n    INSULATION: 0.3,\r\n    DAMPERS_STEEL: 0.1,\r\n    FAN_STEEL: 0.5,\r\n    FAN_ALUMINUM: 0.2\r\n  };\r\n\r\n  /**\r\n   * Perform comprehensive environmental impact assessment\r\n   */\r\n  public static async assessEnvironmentalImpact(\r\n    systemConfiguration: SystemConfiguration,\r\n    energyAnalysis: EnergyAnalysis,\r\n    operatingProfile?: OperatingProfile,\r\n    locationData?: LocationData\r\n  ): Promise<EnvironmentalImpactAnalysis> {\r\n    try {\r\n      const analysisId = this.generateAnalysisId(systemConfiguration.id);\r\n      const timestamp = new Date();\r\n\r\n      // Calculate carbon footprint\r\n      const carbonFootprint = await this.calculateCarbonFootprint(\r\n        systemConfiguration,\r\n        energyAnalysis,\r\n        operatingProfile,\r\n        locationData\r\n      );\r\n\r\n      // Calculate sustainability metrics\r\n      const sustainabilityMetrics = await this.calculateSustainabilityMetrics(\r\n        systemConfiguration,\r\n        energyAnalysis,\r\n        carbonFootprint\r\n      );\r\n\r\n      // Assess green building compliance\r\n      const greenBuildingCompliance = await this.assessGreenBuildingCompliance(\r\n        systemConfiguration,\r\n        energyAnalysis,\r\n        sustainabilityMetrics\r\n      );\r\n\r\n      // Evaluate environmental certifications\r\n      const environmentalCertifications = await this.evaluateEnvironmentalCertifications(\r\n        systemConfiguration,\r\n        sustainabilityMetrics,\r\n        greenBuildingCompliance\r\n      );\r\n\r\n      // Perform lifecycle assessment\r\n      const lifecycleAssessment = await this.performLifecycleAssessment(\r\n        systemConfiguration,\r\n        energyAnalysis,\r\n        carbonFootprint\r\n      );\r\n\r\n      // Generate environmental recommendations\r\n      const recommendations = await this.generateEnvironmentalRecommendations(\r\n        systemConfiguration,\r\n        carbonFootprint,\r\n        sustainabilityMetrics,\r\n        greenBuildingCompliance\r\n      );\r\n\r\n      const analysis: EnvironmentalImpactAnalysis = {\r\n        id: analysisId,\r\n        systemId: systemConfiguration.id,\r\n        analysisTimestamp: timestamp,\r\n        carbonFootprint,\r\n        sustainabilityMetrics,\r\n        greenBuildingCompliance,\r\n        environmentalCertifications,\r\n        lifecycleAssessment,\r\n        recommendations\r\n      };\r\n\r\n      // Cache the analysis\r\n      this.ENVIRONMENTAL_CACHE.set(analysisId, analysis);\r\n\r\n      return analysis;\r\n\r\n    } catch (error) {\r\n      throw new Error(`Environmental impact assessment failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Calculate comprehensive carbon footprint\r\n   */\r\n  private static async calculateCarbonFootprint(\r\n    systemConfiguration: SystemConfiguration,\r\n    energyAnalysis: EnergyAnalysis,\r\n    operatingProfile?: OperatingProfile,\r\n    locationData?: LocationData\r\n  ): Promise<CarbonFootprint> {\r\n    const designAirflow = systemConfiguration.designParameters.designAirflow;\r\n    const annualEnergyConsumption = energyAnalysis.energyConsumption.totalConsumption.value;\r\n\r\n    // Get location-specific emission factor\r\n    const emissionFactor = this.getLocationEmissionFactor(locationData);\r\n\r\n    // Calculate operational emissions (Scope 2)\r\n    const operationalEmissions = this.createEmissionMeasurement(\r\n      annualEnergyConsumption * emissionFactor,\r\n      EmissionUnits.KG_CO2E,\r\n      TimeFrame.ANNUALLY,\r\n      EmissionScope.SCOPE_2\r\n    );\r\n\r\n    // Calculate embodied emissions (Scope 3)\r\n    const embodiedEmissions = await this.calculateEmbodiedEmissions(systemConfiguration);\r\n\r\n    // Calculate refrigerant emissions (Scope 1, if applicable)\r\n    const refrigerantEmissions = this.calculateRefrigerantEmissions(systemConfiguration);\r\n\r\n    // Total emissions\r\n    const totalEmissions = this.createEmissionMeasurement(\r\n      operationalEmissions.value + embodiedEmissions.value + refrigerantEmissions.value,\r\n      EmissionUnits.KG_CO2E,\r\n      TimeFrame.ANNUALLY,\r\n      EmissionScope.SCOPE_2\r\n    );\r\n\r\n    // Emissions by source breakdown\r\n    const emissionsBySource = [\r\n      {\r\n        source: 'Electricity Consumption',\r\n        emissions: operationalEmissions.value,\r\n        percentage: (operationalEmissions.value / totalEmissions.value) * 100,\r\n        scope: EmissionScope.SCOPE_2,\r\n        emissionFactor\r\n      },\r\n      {\r\n        source: 'Embodied Carbon',\r\n        emissions: embodiedEmissions.value,\r\n        percentage: (embodiedEmissions.value / totalEmissions.value) * 100,\r\n        scope: EmissionScope.SCOPE_3,\r\n        emissionFactor: 0\r\n      },\r\n      {\r\n        source: 'Refrigerant Leakage',\r\n        emissions: refrigerantEmissions.value,\r\n        percentage: (refrigerantEmissions.value / totalEmissions.value) * 100,\r\n        scope: EmissionScope.SCOPE_1,\r\n        emissionFactor: 0\r\n      }\r\n    ];\r\n\r\n    // Emissions trend analysis\r\n    const emissionsTrend = this.calculateEmissionsTrend(totalEmissions.value, operatingProfile);\r\n\r\n    // Offset opportunities\r\n    const offsetOpportunities = this.identifyOffsetOpportunities(\r\n      totalEmissions.value,\r\n      operationalEmissions.value,\r\n      systemConfiguration\r\n    );\r\n\r\n    // Benchmark comparison\r\n    const benchmarkComparison = this.performEmissionsBenchmarking(\r\n      totalEmissions.value,\r\n      designAirflow,\r\n      systemConfiguration\r\n    );\r\n\r\n    return {\r\n      totalEmissions,\r\n      operationalEmissions,\r\n      embodiedEmissions,\r\n      emissionsBySource,\r\n      emissionsTrend,\r\n      offsetOpportunities,\r\n      benchmarkComparison\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get location-specific emission factor\r\n   */\r\n  private static getLocationEmissionFactor(locationData?: LocationData): number {\r\n    if (!locationData) {\r\n      return this.EMISSION_FACTORS.ELECTRICITY.US_GRID_AVERAGE;\r\n    }\r\n\r\n    // Regional emission factors (simplified)\r\n    const regionalFactors: { [key: string]: number } = {\r\n      'US-CA': 0.25, // California (cleaner grid)\r\n      'US-TX': 0.45, // Texas\r\n      'US-NY': 0.28, // New York\r\n      'US-FL': 0.42, // Florida\r\n      'US-WA': 0.15, // Washington (hydro power)\r\n      'US-WV': 0.75, // West Virginia (coal heavy)\r\n      'DEFAULT': this.EMISSION_FACTORS.ELECTRICITY.US_GRID_AVERAGE\r\n    };\r\n\r\n    return regionalFactors[locationData.region] || regionalFactors['DEFAULT'];\r\n  }\r\n\r\n  /**\r\n   * Calculate embodied emissions from materials\r\n   */\r\n  private static async calculateEmbodiedEmissions(\r\n    systemConfiguration: SystemConfiguration\r\n  ): Promise<EmissionMeasurement> {\r\n    const designAirflow = systemConfiguration.designParameters.designAirflow;\r\n\r\n    // Calculate material quantities\r\n    const materialQuantities = {\r\n      ductworkSteel: designAirflow * this.MATERIAL_INTENSITY.DUCTWORK_STEEL,\r\n      fittingsSteel: designAirflow * this.MATERIAL_INTENSITY.FITTINGS_STEEL,\r\n      insulation: designAirflow * this.MATERIAL_INTENSITY.INSULATION,\r\n      dampersSteel: designAirflow * this.MATERIAL_INTENSITY.DAMPERS_STEEL,\r\n      fanSteel: designAirflow * this.MATERIAL_INTENSITY.FAN_STEEL,\r\n      fanAluminum: designAirflow * this.MATERIAL_INTENSITY.FAN_ALUMINUM\r\n    };\r\n\r\n    // Calculate embodied emissions for each material\r\n    const embodiedEmissions = \r\n      (materialQuantities.ductworkSteel * this.EMISSION_FACTORS.MATERIALS.GALVANIZED_STEEL) +\r\n      (materialQuantities.fittingsSteel * this.EMISSION_FACTORS.MATERIALS.STEEL) +\r\n      (materialQuantities.insulation * this.EMISSION_FACTORS.MATERIALS.INSULATION) +\r\n      (materialQuantities.dampersSteel * this.EMISSION_FACTORS.MATERIALS.STEEL) +\r\n      (materialQuantities.fanSteel * this.EMISSION_FACTORS.MATERIALS.STEEL) +\r\n      (materialQuantities.fanAluminum * this.EMISSION_FACTORS.MATERIALS.ALUMINUM);\r\n\r\n    // Amortize over equipment life (20 years typical)\r\n    const annualEmbodiedEmissions = embodiedEmissions / 20;\r\n\r\n    return this.createEmissionMeasurement(\r\n      annualEmbodiedEmissions,\r\n      EmissionUnits.KG_CO2E,\r\n      TimeFrame.ANNUALLY,\r\n      EmissionScope.SCOPE_3\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Calculate refrigerant emissions\r\n   */\r\n  private static calculateRefrigerantEmissions(\r\n    systemConfiguration: SystemConfiguration\r\n  ): EmissionMeasurement {\r\n    // Simplified refrigerant emissions calculation\r\n    // Assumes 2% annual leakage rate for systems with refrigerants\r\n    const hasRefrigerants = systemConfiguration.systemType.includes('cooling') || \r\n                           systemConfiguration.systemType.includes('heat_pump');\r\n\r\n    if (!hasRefrigerants) {\r\n      return this.createEmissionMeasurement(0, EmissionUnits.KG_CO2E, TimeFrame.ANNUALLY, EmissionScope.SCOPE_1);\r\n    }\r\n\r\n    const designAirflow = systemConfiguration.designParameters.designAirflow;\r\n    const refrigerantCharge = designAirflow * 0.001; // kg refrigerant per CFM (simplified)\r\n    const leakageRate = 0.02; // 2% annual leakage\r\n    const gwp = this.EMISSION_FACTORS.REFRIGERANTS.R410A; // Assume R410A\r\n\r\n    const annualLeakage = refrigerantCharge * leakageRate;\r\n    const annualEmissions = annualLeakage * gwp;\r\n\r\n    return this.createEmissionMeasurement(\r\n      annualEmissions,\r\n      EmissionUnits.KG_CO2E,\r\n      TimeFrame.ANNUALLY,\r\n      EmissionScope.SCOPE_1\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Create standardized emission measurement\r\n   */\r\n  private static createEmissionMeasurement(\r\n    value: number,\r\n    units: EmissionUnits,\r\n    timeFrame: TimeFrame,\r\n    scope: EmissionScope,\r\n    accuracy: number = 0.8\r\n  ): EmissionMeasurement {\r\n    return {\r\n      value,\r\n      units,\r\n      timeFrame,\r\n      scope,\r\n      accuracy\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate emissions trend\r\n   */\r\n  private static calculateEmissionsTrend(\r\n    currentEmissions: number,\r\n    operatingProfile?: OperatingProfile\r\n  ): EmissionsTrend {\r\n    // Simplified trend calculation\r\n    const projectedEmissions = currentEmissions * 0.98; // 2% annual improvement assumed\r\n    const reductionPotential = currentEmissions * 0.4; // 40% reduction potential\r\n\r\n    return {\r\n      currentEmissions,\r\n      trendDirection: 'improving' as const,\r\n      projectedEmissions,\r\n      reductionPotential,\r\n      timeHorizon: 10 // years\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Identify carbon offset opportunities\r\n   */\r\n  private static identifyOffsetOpportunities(\r\n    totalEmissions: number,\r\n    operationalEmissions: number,\r\n    systemConfiguration: SystemConfiguration\r\n  ): OffsetOpportunity[] {\r\n    const opportunities: OffsetOpportunity[] = [];\r\n\r\n    // Renewable energy opportunity\r\n    opportunities.push({\r\n      type: 'Renewable Energy',\r\n      potential: operationalEmissions * 0.9, // 90% offset potential\r\n      cost: operationalEmissions * 0.02, // $0.02/kg CO2e\r\n      implementation: 'On-site solar installation or renewable energy credits',\r\n      paybackPeriod: 8, // years\r\n      additionalBenefits: ['Energy cost savings', 'Grid independence', 'Marketing value']\r\n    });\r\n\r\n    // Energy efficiency opportunity\r\n    opportunities.push({\r\n      type: 'Energy Efficiency',\r\n      potential: operationalEmissions * 0.25, // 25% reduction potential\r\n      cost: -5000, // Cost savings\r\n      implementation: 'System optimization and efficiency improvements',\r\n      paybackPeriod: 2, // years\r\n      additionalBenefits: ['Reduced operating costs', 'Improved performance', 'Extended equipment life']\r\n    });\r\n\r\n    // Carbon sequestration opportunity\r\n    opportunities.push({\r\n      type: 'Carbon Sequestration',\r\n      potential: totalEmissions * 0.1, // 10% offset potential\r\n      cost: totalEmissions * 0.015, // $0.015/kg CO2e\r\n      implementation: 'Tree planting or carbon credit purchase',\r\n      paybackPeriod: Infinity, // No financial payback\r\n      additionalBenefits: ['Biodiversity support', 'Community engagement', 'Brand enhancement']\r\n    });\r\n\r\n    return opportunities;\r\n  }\r\n\r\n  /**\r\n   * Perform emissions benchmarking\r\n   */\r\n  private static performEmissionsBenchmarking(\r\n    totalEmissions: number,\r\n    designAirflow: number,\r\n    systemConfiguration: SystemConfiguration\r\n  ): EmissionsBenchmark {\r\n    const emissionIntensity = totalEmissions / designAirflow; // kg CO2e/CFM\r\n\r\n    // Industry benchmarks (kg CO2e/CFM/year)\r\n    const benchmarks = {\r\n      industryAverage: 0.12,\r\n      bestPractice: 0.06,\r\n      regulatoryTarget: 0.10,\r\n      netZeroTarget: 0.02\r\n    };\r\n\r\n    const percentile = this.calculateEmissionsPercentile(emissionIntensity, benchmarks.industryAverage);\r\n\r\n    return {\r\n      benchmarkType: 'Industry Average',\r\n      currentIntensity: emissionIntensity,\r\n      benchmarkIntensity: benchmarks.industryAverage,\r\n      percentile,\r\n      improvementPotential: Math.max(0, emissionIntensity - benchmarks.bestPractice),\r\n      complianceGap: Math.max(0, emissionIntensity - benchmarks.regulatoryTarget),\r\n      netZeroGap: Math.max(0, emissionIntensity - benchmarks.netZeroTarget)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate emissions percentile\r\n   */\r\n  private static calculateEmissionsPercentile(intensity: number, average: number): number {\r\n    // Simplified percentile calculation\r\n    const ratio = intensity / average;\r\n    \r\n    if (ratio <= 0.5) return 95; // Top 5%\r\n    if (ratio <= 0.7) return 80; // Top 20%\r\n    if (ratio <= 0.9) return 60; // Top 40%\r\n    if (ratio <= 1.1) return 40; // Average\r\n    if (ratio <= 1.3) return 20; // Bottom 20%\r\n    return 5; // Bottom 5%\r\n  }\r\n\r\n  /**\r\n   * Calculate sustainability metrics\r\n   */\r\n  private static async calculateSustainabilityMetrics(\r\n    systemConfiguration: SystemConfiguration,\r\n    energyAnalysis: EnergyAnalysis,\r\n    carbonFootprint: CarbonFootprint\r\n  ): Promise<SustainabilityMetrics> {\r\n    const designAirflow = systemConfiguration.designParameters.designAirflow;\r\n    const annualEnergyConsumption = energyAnalysis.energyConsumption.totalConsumption.value;\r\n\r\n    // Energy efficiency metrics\r\n    const energyEfficiency = {\r\n      specificFanPower: energyAnalysis.efficiencyMetrics.specificFanPower, // W/CFM\r\n      energyUtilizationIndex: annualEnergyConsumption / (designAirflow * 8760), // kWh/CFM/year\r\n      systemEfficiency: energyAnalysis.efficiencyMetrics.systemEfficiency, // %\r\n      benchmarkComparison: this.calculateEfficiencyBenchmark(energyAnalysis.efficiencyMetrics.specificFanPower)\r\n    };\r\n\r\n    // Carbon intensity metrics\r\n    const carbonIntensity = {\r\n      emissionIntensity: carbonFootprint.totalEmissions.value / designAirflow, // kg CO2e/CFM/year\r\n      operationalIntensity: carbonFootprint.operationalEmissions.value / annualEnergyConsumption, // kg CO2e/kWh\r\n      embodiedIntensity: carbonFootprint.embodiedEmissions.value / designAirflow, // kg CO2e/CFM (annual)\r\n      benchmarkComparison: this.calculateCarbonBenchmark(carbonFootprint.totalEmissions.value / designAirflow)\r\n    };\r\n\r\n    // Resource efficiency metrics\r\n    const resourceEfficiency = {\r\n      materialEfficiency: this.calculateMaterialEfficiency(systemConfiguration),\r\n      waterUsage: this.calculateWaterUsage(systemConfiguration), // L/year\r\n      wasteGeneration: this.calculateWasteGeneration(systemConfiguration), // kg/year\r\n      recyclingPotential: this.calculateRecyclingPotential(systemConfiguration) // %\r\n    };\r\n\r\n    // Environmental performance score\r\n    const environmentalScore = this.calculateEnvironmentalScore(\r\n      energyEfficiency,\r\n      carbonIntensity,\r\n      resourceEfficiency\r\n    );\r\n\r\n    // Sustainability targets and progress\r\n    const sustainabilityTargets = {\r\n      carbonNeutralityTarget: 2030,\r\n      currentProgress: this.calculateCarbonNeutralityProgress(carbonFootprint.totalEmissions.value),\r\n      energyEfficiencyTarget: 1.0, // W/CFM SFP target\r\n      currentEfficiencyProgress: Math.min(100, (1.0 / energyEfficiency.specificFanPower) * 100),\r\n      renewableEnergyTarget: 100, // % renewable\r\n      currentRenewableProgress: 20 // % (simplified)\r\n    };\r\n\r\n    return {\r\n      energyEfficiency,\r\n      carbonIntensity,\r\n      resourceEfficiency,\r\n      environmentalScore,\r\n      sustainabilityTargets,\r\n      certificationReadiness: this.assessCertificationReadiness(environmentalScore)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate efficiency benchmark\r\n   */\r\n  private static calculateEfficiencyBenchmark(sfp: number): EfficiencyBenchmark {\r\n    let rating: string;\r\n    if (sfp <= 0.8) {\r\n      rating = 'Excellent';\r\n    } else if (sfp <= 1.0) {\r\n      rating = 'Good';\r\n    } else if (sfp <= 1.25) {\r\n      rating = 'Average';\r\n    } else {\r\n      rating = 'Poor';\r\n    }\r\n\r\n    return {\r\n      rating,\r\n      percentile: this.calculateSFPPercentile(sfp),\r\n      improvementPotential: Math.max(0, sfp - 0.8), // Improvement to excellent level\r\n      industryAverage: 1.1\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate SFP percentile\r\n   */\r\n  private static calculateSFPPercentile(sfp: number): number {\r\n    if (sfp <= 0.8) return 95;\r\n    if (sfp <= 1.0) return 80;\r\n    if (sfp <= 1.25) return 50;\r\n    if (sfp <= 1.5) return 20;\r\n    return 5;\r\n  }\r\n\r\n  /**\r\n   * Calculate carbon benchmark\r\n   */\r\n  private static calculateCarbonBenchmark(intensity: number): CarbonBenchmark {\r\n    let rating: string;\r\n    if (intensity <= 0.06) {\r\n      rating = 'Excellent';\r\n    } else if (intensity <= 0.10) {\r\n      rating = 'Good';\r\n    } else if (intensity <= 0.15) {\r\n      rating = 'Average';\r\n    } else {\r\n      rating = 'Poor';\r\n    }\r\n\r\n    return {\r\n      rating,\r\n      percentile: this.calculateEmissionsPercentile(intensity, 0.12),\r\n      improvementPotential: Math.max(0, intensity - 0.06),\r\n      industryAverage: 0.12\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate material efficiency\r\n   */\r\n  private static calculateMaterialEfficiency(systemConfiguration: SystemConfiguration): MaterialEfficiency {\r\n    // Calculate total material intensity (kg/CFM)\r\n    const totalMaterialIntensity = Object.values(this.MATERIAL_INTENSITY).reduce((sum, intensity) => sum + intensity, 0);\r\n\r\n    return {\r\n      materialIntensity: totalMaterialIntensity, // kg/CFM\r\n      recyclableContent: 85, // % (typical for steel ductwork)\r\n      durabilityRating: this.calculateDurabilityRating(systemConfiguration),\r\n      maintenanceRequirement: 'Low' // Based on system design\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate durability rating\r\n   */\r\n  private static calculateDurabilityRating(systemConfiguration: SystemConfiguration): string {\r\n    const designPressure = systemConfiguration.designParameters.designPressure;\r\n\r\n    if (designPressure <= 2.0) return 'Excellent';\r\n    if (designPressure <= 4.0) return 'Good';\r\n    if (designPressure <= 6.0) return 'Average';\r\n    return 'Fair';\r\n  }\r\n\r\n  /**\r\n   * Calculate water usage\r\n   */\r\n  private static calculateWaterUsage(systemConfiguration: SystemConfiguration): number {\r\n    // Simplified water usage calculation (mainly for humidification if present)\r\n    const hasHumidification = systemConfiguration.systemType.includes('humidification');\r\n    const designAirflow = systemConfiguration.designParameters.designAirflow;\r\n\r\n    if (hasHumidification) {\r\n      return designAirflow * 0.5; // L/year per CFM (simplified)\r\n    }\r\n\r\n    return designAirflow * 0.05; // Minimal water for cleaning/maintenance\r\n  }\r\n\r\n  /**\r\n   * Calculate waste generation\r\n   */\r\n  private static calculateWasteGeneration(systemConfiguration: SystemConfiguration): number {\r\n    const designAirflow = systemConfiguration.designParameters.designAirflow;\r\n\r\n    // Annual waste from filter replacements and maintenance\r\n    const filterWaste = designAirflow * 0.1; // kg/year (filter media)\r\n    const maintenanceWaste = designAirflow * 0.02; // kg/year (misc maintenance waste)\r\n\r\n    return filterWaste + maintenanceWaste;\r\n  }\r\n\r\n  /**\r\n   * Calculate recycling potential\r\n   */\r\n  private static calculateRecyclingPotential(systemConfiguration: SystemConfiguration): number {\r\n    // Steel ductwork and components are highly recyclable\r\n    const steelContent = 0.85; // 85% of system is steel\r\n    const aluminumContent = 0.10; // 10% aluminum\r\n    const otherContent = 0.05; // 5% other materials\r\n\r\n    const steelRecyclability = 0.95; // 95% recyclable\r\n    const aluminumRecyclability = 0.90; // 90% recyclable\r\n    const otherRecyclability = 0.30; // 30% recyclable\r\n\r\n    return (steelContent * steelRecyclability +\r\n            aluminumContent * aluminumRecyclability +\r\n            otherContent * otherRecyclability) * 100;\r\n  }\r\n\r\n  /**\r\n   * Calculate environmental score\r\n   */\r\n  private static calculateEnvironmentalScore(\r\n    energyEfficiency: any,\r\n    carbonIntensity: any,\r\n    resourceEfficiency: any\r\n  ): EnvironmentalScore {\r\n    // Weighted scoring system (0-100)\r\n    const energyScore = this.calculateEnergyScore(energyEfficiency.specificFanPower);\r\n    const carbonScore = this.calculateCarbonScore(carbonIntensity.emissionIntensity);\r\n    const resourceScore = this.calculateResourceScore(resourceEfficiency);\r\n\r\n    const overallScore = (energyScore * 0.4 + carbonScore * 0.4 + resourceScore * 0.2);\r\n\r\n    return {\r\n      overallScore,\r\n      energyScore,\r\n      carbonScore,\r\n      resourceScore,\r\n      rating: this.getScoreRating(overallScore),\r\n      improvementAreas: this.identifyImprovementAreas(energyScore, carbonScore, resourceScore)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate energy score\r\n   */\r\n  private static calculateEnergyScore(sfp: number): number {\r\n    if (sfp <= 0.8) return 100;\r\n    if (sfp <= 1.0) return 85;\r\n    if (sfp <= 1.25) return 70;\r\n    if (sfp <= 1.5) return 50;\r\n    if (sfp <= 2.0) return 30;\r\n    return 10;\r\n  }\r\n\r\n  /**\r\n   * Calculate carbon score\r\n   */\r\n  private static calculateCarbonScore(intensity: number): number {\r\n    if (intensity <= 0.06) return 100;\r\n    if (intensity <= 0.10) return 85;\r\n    if (intensity <= 0.15) return 70;\r\n    if (intensity <= 0.20) return 50;\r\n    if (intensity <= 0.30) return 30;\r\n    return 10;\r\n  }\r\n\r\n  /**\r\n   * Calculate resource score\r\n   */\r\n  private static calculateResourceScore(resourceEfficiency: any): number {\r\n    const materialScore = resourceEfficiency.recyclableContent; // 0-100\r\n\r\n    let durabilityScore: number;\r\n    if (resourceEfficiency.durabilityRating === 'Excellent') {\r\n      durabilityScore = 100;\r\n    } else if (resourceEfficiency.durabilityRating === 'Good') {\r\n      durabilityScore = 80;\r\n    } else if (resourceEfficiency.durabilityRating === 'Average') {\r\n      durabilityScore = 60;\r\n    } else {\r\n      durabilityScore = 40;\r\n    }\r\n\r\n    return (materialScore + durabilityScore) / 2;\r\n  }\r\n\r\n  /**\r\n   * Get score rating\r\n   */\r\n  private static getScoreRating(score: number): string {\r\n    if (score >= 90) return 'Excellent';\r\n    if (score >= 80) return 'Very Good';\r\n    if (score >= 70) return 'Good';\r\n    if (score >= 60) return 'Fair';\r\n    if (score >= 50) return 'Poor';\r\n    return 'Very Poor';\r\n  }\r\n\r\n  /**\r\n   * Identify improvement areas\r\n   */\r\n  private static identifyImprovementAreas(\r\n    energyScore: number,\r\n    carbonScore: number,\r\n    resourceScore: number\r\n  ): string[] {\r\n    const areas: string[] = [];\r\n\r\n    if (energyScore < 70) areas.push('Energy Efficiency');\r\n    if (carbonScore < 70) areas.push('Carbon Emissions');\r\n    if (resourceScore < 70) areas.push('Resource Efficiency');\r\n\r\n    return areas;\r\n  }\r\n\r\n  /**\r\n   * Calculate carbon neutrality progress\r\n   */\r\n  private static calculateCarbonNeutralityProgress(totalEmissions: number): number {\r\n    // Simplified progress calculation based on emissions intensity\r\n    const targetIntensity = 0.02; // kg CO2e/CFM/year for carbon neutrality\r\n    const currentIntensity = totalEmissions / 10000; // Assuming 10,000 CFM system\r\n\r\n    if (currentIntensity <= targetIntensity) return 100;\r\n\r\n    const maxIntensity = 0.30; // Baseline high emissions\r\n    const progress = Math.max(0, (maxIntensity - currentIntensity) / (maxIntensity - targetIntensity) * 100);\r\n\r\n    return Math.min(100, progress);\r\n  }\r\n\r\n  /**\r\n   * Assess certification readiness\r\n   */\r\n  private static assessCertificationReadiness(environmentalScore: EnvironmentalScore): CertificationReadiness {\r\n    const overallScore = environmentalScore.overallScore;\r\n\r\n    let leedReadiness: string;\r\n    if (overallScore >= 70) {\r\n      leedReadiness = 'Ready';\r\n    } else if (overallScore >= 60) {\r\n      leedReadiness = 'Near Ready';\r\n    } else {\r\n      leedReadiness = 'Not Ready';\r\n    }\r\n\r\n    let breeamReadiness: string;\r\n    if (overallScore >= 75) {\r\n      breeamReadiness = 'Ready';\r\n    } else if (overallScore >= 65) {\r\n      breeamReadiness = 'Near Ready';\r\n    } else {\r\n      breeamReadiness = 'Not Ready';\r\n    }\r\n\r\n    let greenBuildingReadiness: string;\r\n    if (overallScore >= 80) {\r\n      greenBuildingReadiness = 'Ready';\r\n    } else if (overallScore >= 70) {\r\n      greenBuildingReadiness = 'Near Ready';\r\n    } else {\r\n      greenBuildingReadiness = 'Not Ready';\r\n    }\r\n\r\n    return {\r\n      leedReadiness,\r\n      breeamReadiness,\r\n      energyStarReadiness: environmentalScore.energyScore >= 80 ? 'Ready' : 'Not Ready',\r\n      greenBuildingReadiness\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Generate unique analysis ID\r\n   */\r\n  private static generateAnalysisId(systemId: string): string {\r\n    const timestamp = Date.now();\r\n    const random = Math.random().toString(36).substring(2, 8);\r\n    return `environmental_analysis_${systemId}_${timestamp}_${random}`;\r\n  }\r\n}\r\n\r\n// Supporting interfaces\r\ninterface OperatingProfile {\r\n  annualOperatingHours: number;\r\n  loadProfile: 'constant' | 'variable' | 'seasonal';\r\n  seasonalVariation: number; // %\r\n  futureGrowth: number; // % per year\r\n}\r\n\r\ninterface LocationData {\r\n  region: string;\r\n  climateZone: string;\r\n  gridMix: {\r\n    renewable: number; // %\r\n    nuclear: number; // %\r\n    naturalGas: number; // %\r\n    coal: number; // %\r\n    other: number; // %\r\n  };\r\n  localRegulations: string[];\r\n}\r\n\r\ninterface EmissionsTrend {\r\n  currentEmissions: number;\r\n  trendDirection: 'improving' | 'stable' | 'worsening';\r\n  projectedEmissions: number;\r\n  reductionPotential: number;\r\n  timeHorizon: number;\r\n}\r\n\r\ninterface OffsetOpportunity {\r\n  type: string;\r\n  potential: number; // kg CO2e/year\r\n  cost: number; // $/year\r\n  implementation: string;\r\n  paybackPeriod: number; // years\r\n  additionalBenefits: string[];\r\n}\r\n\r\ninterface EmissionsBenchmark {\r\n  benchmarkType: string;\r\n  currentIntensity: number; // kg CO2e/CFM/year\r\n  benchmarkIntensity: number;\r\n  percentile: number;\r\n  improvementPotential: number;\r\n  complianceGap: number;\r\n  netZeroGap: number;\r\n}\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "0749e186314117226586e514e4b61f8edceb8545"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1ktvp498qw = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1ktvp498qw();
cov_1ktvp498qw().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1ktvp498qw().s[1]++;
exports.EnvironmentalImpactAssessmentEngine = void 0;
const SystemAnalysisTypes_1 =
/* istanbul ignore next */
(cov_1ktvp498qw().s[2]++, require("./types/SystemAnalysisTypes"));
/**
 * Environmental Impact Assessment Engine
 *
 * Provides comprehensive environmental impact assessment capabilities including:
 * - Carbon footprint calculation and tracking
 * - Sustainability metrics and benchmarking
 * - Green building certification support (LEED, BREEAM, etc.)
 * - Environmental compliance checking
 * - Lifecycle environmental impact assessment
 * - Environmental improvement recommendations
 */
class EnvironmentalImpactAssessmentEngine {
  /**
   * Perform comprehensive environmental impact assessment
   */
  static async assessEnvironmentalImpact(systemConfiguration, energyAnalysis, operatingProfile, locationData) {
    /* istanbul ignore next */
    cov_1ktvp498qw().f[0]++;
    cov_1ktvp498qw().s[3]++;
    try {
      const analysisId =
      /* istanbul ignore next */
      (cov_1ktvp498qw().s[4]++, this.generateAnalysisId(systemConfiguration.id));
      const timestamp =
      /* istanbul ignore next */
      (cov_1ktvp498qw().s[5]++, new Date());
      // Calculate carbon footprint
      const carbonFootprint =
      /* istanbul ignore next */
      (cov_1ktvp498qw().s[6]++, await this.calculateCarbonFootprint(systemConfiguration, energyAnalysis, operatingProfile, locationData));
      // Calculate sustainability metrics
      const sustainabilityMetrics =
      /* istanbul ignore next */
      (cov_1ktvp498qw().s[7]++, await this.calculateSustainabilityMetrics(systemConfiguration, energyAnalysis, carbonFootprint));
      // Assess green building compliance
      const greenBuildingCompliance =
      /* istanbul ignore next */
      (cov_1ktvp498qw().s[8]++, await this.assessGreenBuildingCompliance(systemConfiguration, energyAnalysis, sustainabilityMetrics));
      // Evaluate environmental certifications
      const environmentalCertifications =
      /* istanbul ignore next */
      (cov_1ktvp498qw().s[9]++, await this.evaluateEnvironmentalCertifications(systemConfiguration, sustainabilityMetrics, greenBuildingCompliance));
      // Perform lifecycle assessment
      const lifecycleAssessment =
      /* istanbul ignore next */
      (cov_1ktvp498qw().s[10]++, await this.performLifecycleAssessment(systemConfiguration, energyAnalysis, carbonFootprint));
      // Generate environmental recommendations
      const recommendations =
      /* istanbul ignore next */
      (cov_1ktvp498qw().s[11]++, await this.generateEnvironmentalRecommendations(systemConfiguration, carbonFootprint, sustainabilityMetrics, greenBuildingCompliance));
      const analysis =
      /* istanbul ignore next */
      (cov_1ktvp498qw().s[12]++, {
        id: analysisId,
        systemId: systemConfiguration.id,
        analysisTimestamp: timestamp,
        carbonFootprint,
        sustainabilityMetrics,
        greenBuildingCompliance,
        environmentalCertifications,
        lifecycleAssessment,
        recommendations
      });
      // Cache the analysis
      /* istanbul ignore next */
      cov_1ktvp498qw().s[13]++;
      this.ENVIRONMENTAL_CACHE.set(analysisId, analysis);
      /* istanbul ignore next */
      cov_1ktvp498qw().s[14]++;
      return analysis;
    } catch (error) {
      /* istanbul ignore next */
      cov_1ktvp498qw().s[15]++;
      throw new Error(`Environmental impact assessment failed: ${error instanceof Error ?
      /* istanbul ignore next */
      (cov_1ktvp498qw().b[0][0]++, error.message) :
      /* istanbul ignore next */
      (cov_1ktvp498qw().b[0][1]++, 'Unknown error')}`);
    }
  }
  /**
   * Calculate comprehensive carbon footprint
   */
  static async calculateCarbonFootprint(systemConfiguration, energyAnalysis, operatingProfile, locationData) {
    /* istanbul ignore next */
    cov_1ktvp498qw().f[1]++;
    const designAirflow =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[16]++, systemConfiguration.designParameters.designAirflow);
    const annualEnergyConsumption =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[17]++, energyAnalysis.energyConsumption.totalConsumption.value);
    // Get location-specific emission factor
    const emissionFactor =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[18]++, this.getLocationEmissionFactor(locationData));
    // Calculate operational emissions (Scope 2)
    const operationalEmissions =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[19]++, this.createEmissionMeasurement(annualEnergyConsumption * emissionFactor, SystemAnalysisTypes_1.EmissionUnits.KG_CO2E, SystemAnalysisTypes_1.TimeFrame.ANNUALLY, SystemAnalysisTypes_1.EmissionScope.SCOPE_2));
    // Calculate embodied emissions (Scope 3)
    const embodiedEmissions =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[20]++, await this.calculateEmbodiedEmissions(systemConfiguration));
    // Calculate refrigerant emissions (Scope 1, if applicable)
    const refrigerantEmissions =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[21]++, this.calculateRefrigerantEmissions(systemConfiguration));
    // Total emissions
    const totalEmissions =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[22]++, this.createEmissionMeasurement(operationalEmissions.value + embodiedEmissions.value + refrigerantEmissions.value, SystemAnalysisTypes_1.EmissionUnits.KG_CO2E, SystemAnalysisTypes_1.TimeFrame.ANNUALLY, SystemAnalysisTypes_1.EmissionScope.SCOPE_2));
    // Emissions by source breakdown
    const emissionsBySource =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[23]++, [{
      source: 'Electricity Consumption',
      emissions: operationalEmissions.value,
      percentage: operationalEmissions.value / totalEmissions.value * 100,
      scope: SystemAnalysisTypes_1.EmissionScope.SCOPE_2,
      emissionFactor
    }, {
      source: 'Embodied Carbon',
      emissions: embodiedEmissions.value,
      percentage: embodiedEmissions.value / totalEmissions.value * 100,
      scope: SystemAnalysisTypes_1.EmissionScope.SCOPE_3,
      emissionFactor: 0
    }, {
      source: 'Refrigerant Leakage',
      emissions: refrigerantEmissions.value,
      percentage: refrigerantEmissions.value / totalEmissions.value * 100,
      scope: SystemAnalysisTypes_1.EmissionScope.SCOPE_1,
      emissionFactor: 0
    }]);
    // Emissions trend analysis
    const emissionsTrend =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[24]++, this.calculateEmissionsTrend(totalEmissions.value, operatingProfile));
    // Offset opportunities
    const offsetOpportunities =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[25]++, this.identifyOffsetOpportunities(totalEmissions.value, operationalEmissions.value, systemConfiguration));
    // Benchmark comparison
    const benchmarkComparison =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[26]++, this.performEmissionsBenchmarking(totalEmissions.value, designAirflow, systemConfiguration));
    /* istanbul ignore next */
    cov_1ktvp498qw().s[27]++;
    return {
      totalEmissions,
      operationalEmissions,
      embodiedEmissions,
      emissionsBySource,
      emissionsTrend,
      offsetOpportunities,
      benchmarkComparison
    };
  }
  /**
   * Get location-specific emission factor
   */
  static getLocationEmissionFactor(locationData) {
    /* istanbul ignore next */
    cov_1ktvp498qw().f[2]++;
    cov_1ktvp498qw().s[28]++;
    if (!locationData) {
      /* istanbul ignore next */
      cov_1ktvp498qw().b[1][0]++;
      cov_1ktvp498qw().s[29]++;
      return this.EMISSION_FACTORS.ELECTRICITY.US_GRID_AVERAGE;
    } else
    /* istanbul ignore next */
    {
      cov_1ktvp498qw().b[1][1]++;
    }
    // Regional emission factors (simplified)
    const regionalFactors =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[30]++, {
      'US-CA': 0.25,
      // California (cleaner grid)
      'US-TX': 0.45,
      // Texas
      'US-NY': 0.28,
      // New York
      'US-FL': 0.42,
      // Florida
      'US-WA': 0.15,
      // Washington (hydro power)
      'US-WV': 0.75,
      // West Virginia (coal heavy)
      'DEFAULT': this.EMISSION_FACTORS.ELECTRICITY.US_GRID_AVERAGE
    });
    /* istanbul ignore next */
    cov_1ktvp498qw().s[31]++;
    return /* istanbul ignore next */(cov_1ktvp498qw().b[2][0]++, regionalFactors[locationData.region]) ||
    /* istanbul ignore next */
    (cov_1ktvp498qw().b[2][1]++, regionalFactors['DEFAULT']);
  }
  /**
   * Calculate embodied emissions from materials
   */
  static async calculateEmbodiedEmissions(systemConfiguration) {
    /* istanbul ignore next */
    cov_1ktvp498qw().f[3]++;
    const designAirflow =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[32]++, systemConfiguration.designParameters.designAirflow);
    // Calculate material quantities
    const materialQuantities =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[33]++, {
      ductworkSteel: designAirflow * this.MATERIAL_INTENSITY.DUCTWORK_STEEL,
      fittingsSteel: designAirflow * this.MATERIAL_INTENSITY.FITTINGS_STEEL,
      insulation: designAirflow * this.MATERIAL_INTENSITY.INSULATION,
      dampersSteel: designAirflow * this.MATERIAL_INTENSITY.DAMPERS_STEEL,
      fanSteel: designAirflow * this.MATERIAL_INTENSITY.FAN_STEEL,
      fanAluminum: designAirflow * this.MATERIAL_INTENSITY.FAN_ALUMINUM
    });
    // Calculate embodied emissions for each material
    const embodiedEmissions =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[34]++, materialQuantities.ductworkSteel * this.EMISSION_FACTORS.MATERIALS.GALVANIZED_STEEL + materialQuantities.fittingsSteel * this.EMISSION_FACTORS.MATERIALS.STEEL + materialQuantities.insulation * this.EMISSION_FACTORS.MATERIALS.INSULATION + materialQuantities.dampersSteel * this.EMISSION_FACTORS.MATERIALS.STEEL + materialQuantities.fanSteel * this.EMISSION_FACTORS.MATERIALS.STEEL + materialQuantities.fanAluminum * this.EMISSION_FACTORS.MATERIALS.ALUMINUM);
    // Amortize over equipment life (20 years typical)
    const annualEmbodiedEmissions =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[35]++, embodiedEmissions / 20);
    /* istanbul ignore next */
    cov_1ktvp498qw().s[36]++;
    return this.createEmissionMeasurement(annualEmbodiedEmissions, SystemAnalysisTypes_1.EmissionUnits.KG_CO2E, SystemAnalysisTypes_1.TimeFrame.ANNUALLY, SystemAnalysisTypes_1.EmissionScope.SCOPE_3);
  }
  /**
   * Calculate refrigerant emissions
   */
  static calculateRefrigerantEmissions(systemConfiguration) {
    /* istanbul ignore next */
    cov_1ktvp498qw().f[4]++;
    // Simplified refrigerant emissions calculation
    // Assumes 2% annual leakage rate for systems with refrigerants
    const hasRefrigerants =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[37]++,
    /* istanbul ignore next */
    (cov_1ktvp498qw().b[3][0]++, systemConfiguration.systemType.includes('cooling')) ||
    /* istanbul ignore next */
    (cov_1ktvp498qw().b[3][1]++, systemConfiguration.systemType.includes('heat_pump')));
    /* istanbul ignore next */
    cov_1ktvp498qw().s[38]++;
    if (!hasRefrigerants) {
      /* istanbul ignore next */
      cov_1ktvp498qw().b[4][0]++;
      cov_1ktvp498qw().s[39]++;
      return this.createEmissionMeasurement(0, SystemAnalysisTypes_1.EmissionUnits.KG_CO2E, SystemAnalysisTypes_1.TimeFrame.ANNUALLY, SystemAnalysisTypes_1.EmissionScope.SCOPE_1);
    } else
    /* istanbul ignore next */
    {
      cov_1ktvp498qw().b[4][1]++;
    }
    const designAirflow =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[40]++, systemConfiguration.designParameters.designAirflow);
    const refrigerantCharge =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[41]++, designAirflow * 0.001); // kg refrigerant per CFM (simplified)
    const leakageRate =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[42]++, 0.02); // 2% annual leakage
    const gwp =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[43]++, this.EMISSION_FACTORS.REFRIGERANTS.R410A); // Assume R410A
    const annualLeakage =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[44]++, refrigerantCharge * leakageRate);
    const annualEmissions =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[45]++, annualLeakage * gwp);
    /* istanbul ignore next */
    cov_1ktvp498qw().s[46]++;
    return this.createEmissionMeasurement(annualEmissions, SystemAnalysisTypes_1.EmissionUnits.KG_CO2E, SystemAnalysisTypes_1.TimeFrame.ANNUALLY, SystemAnalysisTypes_1.EmissionScope.SCOPE_1);
  }
  /**
   * Create standardized emission measurement
   */
  static createEmissionMeasurement(value, units, timeFrame, scope, accuracy =
  /* istanbul ignore next */
  (cov_1ktvp498qw().b[5][0]++, 0.8)) {
    /* istanbul ignore next */
    cov_1ktvp498qw().f[5]++;
    cov_1ktvp498qw().s[47]++;
    return {
      value,
      units,
      timeFrame,
      scope,
      accuracy
    };
  }
  /**
   * Calculate emissions trend
   */
  static calculateEmissionsTrend(currentEmissions, operatingProfile) {
    /* istanbul ignore next */
    cov_1ktvp498qw().f[6]++;
    // Simplified trend calculation
    const projectedEmissions =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[48]++, currentEmissions * 0.98); // 2% annual improvement assumed
    const reductionPotential =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[49]++, currentEmissions * 0.4); // 40% reduction potential
    /* istanbul ignore next */
    cov_1ktvp498qw().s[50]++;
    return {
      currentEmissions,
      trendDirection: 'improving',
      projectedEmissions,
      reductionPotential,
      timeHorizon: 10 // years
    };
  }
  /**
   * Identify carbon offset opportunities
   */
  static identifyOffsetOpportunities(totalEmissions, operationalEmissions, systemConfiguration) {
    /* istanbul ignore next */
    cov_1ktvp498qw().f[7]++;
    const opportunities =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[51]++, []);
    // Renewable energy opportunity
    /* istanbul ignore next */
    cov_1ktvp498qw().s[52]++;
    opportunities.push({
      type: 'Renewable Energy',
      potential: operationalEmissions * 0.9,
      // 90% offset potential
      cost: operationalEmissions * 0.02,
      // $0.02/kg CO2e
      implementation: 'On-site solar installation or renewable energy credits',
      paybackPeriod: 8,
      // years
      additionalBenefits: ['Energy cost savings', 'Grid independence', 'Marketing value']
    });
    // Energy efficiency opportunity
    /* istanbul ignore next */
    cov_1ktvp498qw().s[53]++;
    opportunities.push({
      type: 'Energy Efficiency',
      potential: operationalEmissions * 0.25,
      // 25% reduction potential
      cost: -5000,
      // Cost savings
      implementation: 'System optimization and efficiency improvements',
      paybackPeriod: 2,
      // years
      additionalBenefits: ['Reduced operating costs', 'Improved performance', 'Extended equipment life']
    });
    // Carbon sequestration opportunity
    /* istanbul ignore next */
    cov_1ktvp498qw().s[54]++;
    opportunities.push({
      type: 'Carbon Sequestration',
      potential: totalEmissions * 0.1,
      // 10% offset potential
      cost: totalEmissions * 0.015,
      // $0.015/kg CO2e
      implementation: 'Tree planting or carbon credit purchase',
      paybackPeriod: Infinity,
      // No financial payback
      additionalBenefits: ['Biodiversity support', 'Community engagement', 'Brand enhancement']
    });
    /* istanbul ignore next */
    cov_1ktvp498qw().s[55]++;
    return opportunities;
  }
  /**
   * Perform emissions benchmarking
   */
  static performEmissionsBenchmarking(totalEmissions, designAirflow, systemConfiguration) {
    /* istanbul ignore next */
    cov_1ktvp498qw().f[8]++;
    const emissionIntensity =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[56]++, totalEmissions / designAirflow); // kg CO2e/CFM
    // Industry benchmarks (kg CO2e/CFM/year)
    const benchmarks =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[57]++, {
      industryAverage: 0.12,
      bestPractice: 0.06,
      regulatoryTarget: 0.10,
      netZeroTarget: 0.02
    });
    const percentile =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[58]++, this.calculateEmissionsPercentile(emissionIntensity, benchmarks.industryAverage));
    /* istanbul ignore next */
    cov_1ktvp498qw().s[59]++;
    return {
      benchmarkType: 'Industry Average',
      currentIntensity: emissionIntensity,
      benchmarkIntensity: benchmarks.industryAverage,
      percentile,
      improvementPotential: Math.max(0, emissionIntensity - benchmarks.bestPractice),
      complianceGap: Math.max(0, emissionIntensity - benchmarks.regulatoryTarget),
      netZeroGap: Math.max(0, emissionIntensity - benchmarks.netZeroTarget)
    };
  }
  /**
   * Calculate emissions percentile
   */
  static calculateEmissionsPercentile(intensity, average) {
    /* istanbul ignore next */
    cov_1ktvp498qw().f[9]++;
    // Simplified percentile calculation
    const ratio =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[60]++, intensity / average);
    /* istanbul ignore next */
    cov_1ktvp498qw().s[61]++;
    if (ratio <= 0.5) {
      /* istanbul ignore next */
      cov_1ktvp498qw().b[6][0]++;
      cov_1ktvp498qw().s[62]++;
      return 95;
    } else
    /* istanbul ignore next */
    {
      cov_1ktvp498qw().b[6][1]++;
    } // Top 5%
    cov_1ktvp498qw().s[63]++;
    if (ratio <= 0.7) {
      /* istanbul ignore next */
      cov_1ktvp498qw().b[7][0]++;
      cov_1ktvp498qw().s[64]++;
      return 80;
    } else
    /* istanbul ignore next */
    {
      cov_1ktvp498qw().b[7][1]++;
    } // Top 20%
    cov_1ktvp498qw().s[65]++;
    if (ratio <= 0.9) {
      /* istanbul ignore next */
      cov_1ktvp498qw().b[8][0]++;
      cov_1ktvp498qw().s[66]++;
      return 60;
    } else
    /* istanbul ignore next */
    {
      cov_1ktvp498qw().b[8][1]++;
    } // Top 40%
    cov_1ktvp498qw().s[67]++;
    if (ratio <= 1.1) {
      /* istanbul ignore next */
      cov_1ktvp498qw().b[9][0]++;
      cov_1ktvp498qw().s[68]++;
      return 40;
    } else
    /* istanbul ignore next */
    {
      cov_1ktvp498qw().b[9][1]++;
    } // Average
    cov_1ktvp498qw().s[69]++;
    if (ratio <= 1.3) {
      /* istanbul ignore next */
      cov_1ktvp498qw().b[10][0]++;
      cov_1ktvp498qw().s[70]++;
      return 20;
    } else
    /* istanbul ignore next */
    {
      cov_1ktvp498qw().b[10][1]++;
    } // Bottom 20%
    cov_1ktvp498qw().s[71]++;
    return 5; // Bottom 5%
  }
  /**
   * Calculate sustainability metrics
   */
  static async calculateSustainabilityMetrics(systemConfiguration, energyAnalysis, carbonFootprint) {
    /* istanbul ignore next */
    cov_1ktvp498qw().f[10]++;
    const designAirflow =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[72]++, systemConfiguration.designParameters.designAirflow);
    const annualEnergyConsumption =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[73]++, energyAnalysis.energyConsumption.totalConsumption.value);
    // Energy efficiency metrics
    const energyEfficiency =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[74]++, {
      specificFanPower: energyAnalysis.efficiencyMetrics.specificFanPower,
      // W/CFM
      energyUtilizationIndex: annualEnergyConsumption / (designAirflow * 8760),
      // kWh/CFM/year
      systemEfficiency: energyAnalysis.efficiencyMetrics.systemEfficiency,
      // %
      benchmarkComparison: this.calculateEfficiencyBenchmark(energyAnalysis.efficiencyMetrics.specificFanPower)
    });
    // Carbon intensity metrics
    const carbonIntensity =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[75]++, {
      emissionIntensity: carbonFootprint.totalEmissions.value / designAirflow,
      // kg CO2e/CFM/year
      operationalIntensity: carbonFootprint.operationalEmissions.value / annualEnergyConsumption,
      // kg CO2e/kWh
      embodiedIntensity: carbonFootprint.embodiedEmissions.value / designAirflow,
      // kg CO2e/CFM (annual)
      benchmarkComparison: this.calculateCarbonBenchmark(carbonFootprint.totalEmissions.value / designAirflow)
    });
    // Resource efficiency metrics
    const resourceEfficiency =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[76]++, {
      materialEfficiency: this.calculateMaterialEfficiency(systemConfiguration),
      waterUsage: this.calculateWaterUsage(systemConfiguration),
      // L/year
      wasteGeneration: this.calculateWasteGeneration(systemConfiguration),
      // kg/year
      recyclingPotential: this.calculateRecyclingPotential(systemConfiguration) // %
    });
    // Environmental performance score
    const environmentalScore =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[77]++, this.calculateEnvironmentalScore(energyEfficiency, carbonIntensity, resourceEfficiency));
    // Sustainability targets and progress
    const sustainabilityTargets =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[78]++, {
      carbonNeutralityTarget: 2030,
      currentProgress: this.calculateCarbonNeutralityProgress(carbonFootprint.totalEmissions.value),
      energyEfficiencyTarget: 1.0,
      // W/CFM SFP target
      currentEfficiencyProgress: Math.min(100, 1.0 / energyEfficiency.specificFanPower * 100),
      renewableEnergyTarget: 100,
      // % renewable
      currentRenewableProgress: 20 // % (simplified)
    });
    /* istanbul ignore next */
    cov_1ktvp498qw().s[79]++;
    return {
      energyEfficiency,
      carbonIntensity,
      resourceEfficiency,
      environmentalScore,
      sustainabilityTargets,
      certificationReadiness: this.assessCertificationReadiness(environmentalScore)
    };
  }
  /**
   * Calculate efficiency benchmark
   */
  static calculateEfficiencyBenchmark(sfp) {
    /* istanbul ignore next */
    cov_1ktvp498qw().f[11]++;
    let rating;
    /* istanbul ignore next */
    cov_1ktvp498qw().s[80]++;
    if (sfp <= 0.8) {
      /* istanbul ignore next */
      cov_1ktvp498qw().b[11][0]++;
      cov_1ktvp498qw().s[81]++;
      rating = 'Excellent';
    } else {
      /* istanbul ignore next */
      cov_1ktvp498qw().b[11][1]++;
      cov_1ktvp498qw().s[82]++;
      if (sfp <= 1.0) {
        /* istanbul ignore next */
        cov_1ktvp498qw().b[12][0]++;
        cov_1ktvp498qw().s[83]++;
        rating = 'Good';
      } else {
        /* istanbul ignore next */
        cov_1ktvp498qw().b[12][1]++;
        cov_1ktvp498qw().s[84]++;
        if (sfp <= 1.25) {
          /* istanbul ignore next */
          cov_1ktvp498qw().b[13][0]++;
          cov_1ktvp498qw().s[85]++;
          rating = 'Average';
        } else {
          /* istanbul ignore next */
          cov_1ktvp498qw().b[13][1]++;
          cov_1ktvp498qw().s[86]++;
          rating = 'Poor';
        }
      }
    }
    /* istanbul ignore next */
    cov_1ktvp498qw().s[87]++;
    return {
      rating,
      percentile: this.calculateSFPPercentile(sfp),
      improvementPotential: Math.max(0, sfp - 0.8),
      // Improvement to excellent level
      industryAverage: 1.1
    };
  }
  /**
   * Calculate SFP percentile
   */
  static calculateSFPPercentile(sfp) {
    /* istanbul ignore next */
    cov_1ktvp498qw().f[12]++;
    cov_1ktvp498qw().s[88]++;
    if (sfp <= 0.8) {
      /* istanbul ignore next */
      cov_1ktvp498qw().b[14][0]++;
      cov_1ktvp498qw().s[89]++;
      return 95;
    } else
    /* istanbul ignore next */
    {
      cov_1ktvp498qw().b[14][1]++;
    }
    cov_1ktvp498qw().s[90]++;
    if (sfp <= 1.0) {
      /* istanbul ignore next */
      cov_1ktvp498qw().b[15][0]++;
      cov_1ktvp498qw().s[91]++;
      return 80;
    } else
    /* istanbul ignore next */
    {
      cov_1ktvp498qw().b[15][1]++;
    }
    cov_1ktvp498qw().s[92]++;
    if (sfp <= 1.25) {
      /* istanbul ignore next */
      cov_1ktvp498qw().b[16][0]++;
      cov_1ktvp498qw().s[93]++;
      return 50;
    } else
    /* istanbul ignore next */
    {
      cov_1ktvp498qw().b[16][1]++;
    }
    cov_1ktvp498qw().s[94]++;
    if (sfp <= 1.5) {
      /* istanbul ignore next */
      cov_1ktvp498qw().b[17][0]++;
      cov_1ktvp498qw().s[95]++;
      return 20;
    } else
    /* istanbul ignore next */
    {
      cov_1ktvp498qw().b[17][1]++;
    }
    cov_1ktvp498qw().s[96]++;
    return 5;
  }
  /**
   * Calculate carbon benchmark
   */
  static calculateCarbonBenchmark(intensity) {
    /* istanbul ignore next */
    cov_1ktvp498qw().f[13]++;
    let rating;
    /* istanbul ignore next */
    cov_1ktvp498qw().s[97]++;
    if (intensity <= 0.06) {
      /* istanbul ignore next */
      cov_1ktvp498qw().b[18][0]++;
      cov_1ktvp498qw().s[98]++;
      rating = 'Excellent';
    } else {
      /* istanbul ignore next */
      cov_1ktvp498qw().b[18][1]++;
      cov_1ktvp498qw().s[99]++;
      if (intensity <= 0.10) {
        /* istanbul ignore next */
        cov_1ktvp498qw().b[19][0]++;
        cov_1ktvp498qw().s[100]++;
        rating = 'Good';
      } else {
        /* istanbul ignore next */
        cov_1ktvp498qw().b[19][1]++;
        cov_1ktvp498qw().s[101]++;
        if (intensity <= 0.15) {
          /* istanbul ignore next */
          cov_1ktvp498qw().b[20][0]++;
          cov_1ktvp498qw().s[102]++;
          rating = 'Average';
        } else {
          /* istanbul ignore next */
          cov_1ktvp498qw().b[20][1]++;
          cov_1ktvp498qw().s[103]++;
          rating = 'Poor';
        }
      }
    }
    /* istanbul ignore next */
    cov_1ktvp498qw().s[104]++;
    return {
      rating,
      percentile: this.calculateEmissionsPercentile(intensity, 0.12),
      improvementPotential: Math.max(0, intensity - 0.06),
      industryAverage: 0.12
    };
  }
  /**
   * Calculate material efficiency
   */
  static calculateMaterialEfficiency(systemConfiguration) {
    /* istanbul ignore next */
    cov_1ktvp498qw().f[14]++;
    // Calculate total material intensity (kg/CFM)
    const totalMaterialIntensity =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[105]++, Object.values(this.MATERIAL_INTENSITY).reduce((sum, intensity) => {
      /* istanbul ignore next */
      cov_1ktvp498qw().f[15]++;
      cov_1ktvp498qw().s[106]++;
      return sum + intensity;
    }, 0));
    /* istanbul ignore next */
    cov_1ktvp498qw().s[107]++;
    return {
      materialIntensity: totalMaterialIntensity,
      // kg/CFM
      recyclableContent: 85,
      // % (typical for steel ductwork)
      durabilityRating: this.calculateDurabilityRating(systemConfiguration),
      maintenanceRequirement: 'Low' // Based on system design
    };
  }
  /**
   * Calculate durability rating
   */
  static calculateDurabilityRating(systemConfiguration) {
    /* istanbul ignore next */
    cov_1ktvp498qw().f[16]++;
    const designPressure =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[108]++, systemConfiguration.designParameters.designPressure);
    /* istanbul ignore next */
    cov_1ktvp498qw().s[109]++;
    if (designPressure <= 2.0) {
      /* istanbul ignore next */
      cov_1ktvp498qw().b[21][0]++;
      cov_1ktvp498qw().s[110]++;
      return 'Excellent';
    } else
    /* istanbul ignore next */
    {
      cov_1ktvp498qw().b[21][1]++;
    }
    cov_1ktvp498qw().s[111]++;
    if (designPressure <= 4.0) {
      /* istanbul ignore next */
      cov_1ktvp498qw().b[22][0]++;
      cov_1ktvp498qw().s[112]++;
      return 'Good';
    } else
    /* istanbul ignore next */
    {
      cov_1ktvp498qw().b[22][1]++;
    }
    cov_1ktvp498qw().s[113]++;
    if (designPressure <= 6.0) {
      /* istanbul ignore next */
      cov_1ktvp498qw().b[23][0]++;
      cov_1ktvp498qw().s[114]++;
      return 'Average';
    } else
    /* istanbul ignore next */
    {
      cov_1ktvp498qw().b[23][1]++;
    }
    cov_1ktvp498qw().s[115]++;
    return 'Fair';
  }
  /**
   * Calculate water usage
   */
  static calculateWaterUsage(systemConfiguration) {
    /* istanbul ignore next */
    cov_1ktvp498qw().f[17]++;
    // Simplified water usage calculation (mainly for humidification if present)
    const hasHumidification =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[116]++, systemConfiguration.systemType.includes('humidification'));
    const designAirflow =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[117]++, systemConfiguration.designParameters.designAirflow);
    /* istanbul ignore next */
    cov_1ktvp498qw().s[118]++;
    if (hasHumidification) {
      /* istanbul ignore next */
      cov_1ktvp498qw().b[24][0]++;
      cov_1ktvp498qw().s[119]++;
      return designAirflow * 0.5; // L/year per CFM (simplified)
    } else
    /* istanbul ignore next */
    {
      cov_1ktvp498qw().b[24][1]++;
    }
    cov_1ktvp498qw().s[120]++;
    return designAirflow * 0.05; // Minimal water for cleaning/maintenance
  }
  /**
   * Calculate waste generation
   */
  static calculateWasteGeneration(systemConfiguration) {
    /* istanbul ignore next */
    cov_1ktvp498qw().f[18]++;
    const designAirflow =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[121]++, systemConfiguration.designParameters.designAirflow);
    // Annual waste from filter replacements and maintenance
    const filterWaste =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[122]++, designAirflow * 0.1); // kg/year (filter media)
    const maintenanceWaste =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[123]++, designAirflow * 0.02); // kg/year (misc maintenance waste)
    /* istanbul ignore next */
    cov_1ktvp498qw().s[124]++;
    return filterWaste + maintenanceWaste;
  }
  /**
   * Calculate recycling potential
   */
  static calculateRecyclingPotential(systemConfiguration) {
    /* istanbul ignore next */
    cov_1ktvp498qw().f[19]++;
    // Steel ductwork and components are highly recyclable
    const steelContent =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[125]++, 0.85); // 85% of system is steel
    const aluminumContent =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[126]++, 0.10); // 10% aluminum
    const otherContent =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[127]++, 0.05); // 5% other materials
    const steelRecyclability =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[128]++, 0.95); // 95% recyclable
    const aluminumRecyclability =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[129]++, 0.90); // 90% recyclable
    const otherRecyclability =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[130]++, 0.30); // 30% recyclable
    /* istanbul ignore next */
    cov_1ktvp498qw().s[131]++;
    return (steelContent * steelRecyclability + aluminumContent * aluminumRecyclability + otherContent * otherRecyclability) * 100;
  }
  /**
   * Calculate environmental score
   */
  static calculateEnvironmentalScore(energyEfficiency, carbonIntensity, resourceEfficiency) {
    /* istanbul ignore next */
    cov_1ktvp498qw().f[20]++;
    // Weighted scoring system (0-100)
    const energyScore =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[132]++, this.calculateEnergyScore(energyEfficiency.specificFanPower));
    const carbonScore =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[133]++, this.calculateCarbonScore(carbonIntensity.emissionIntensity));
    const resourceScore =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[134]++, this.calculateResourceScore(resourceEfficiency));
    const overallScore =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[135]++, energyScore * 0.4 + carbonScore * 0.4 + resourceScore * 0.2);
    /* istanbul ignore next */
    cov_1ktvp498qw().s[136]++;
    return {
      overallScore,
      energyScore,
      carbonScore,
      resourceScore,
      rating: this.getScoreRating(overallScore),
      improvementAreas: this.identifyImprovementAreas(energyScore, carbonScore, resourceScore)
    };
  }
  /**
   * Calculate energy score
   */
  static calculateEnergyScore(sfp) {
    /* istanbul ignore next */
    cov_1ktvp498qw().f[21]++;
    cov_1ktvp498qw().s[137]++;
    if (sfp <= 0.8) {
      /* istanbul ignore next */
      cov_1ktvp498qw().b[25][0]++;
      cov_1ktvp498qw().s[138]++;
      return 100;
    } else
    /* istanbul ignore next */
    {
      cov_1ktvp498qw().b[25][1]++;
    }
    cov_1ktvp498qw().s[139]++;
    if (sfp <= 1.0) {
      /* istanbul ignore next */
      cov_1ktvp498qw().b[26][0]++;
      cov_1ktvp498qw().s[140]++;
      return 85;
    } else
    /* istanbul ignore next */
    {
      cov_1ktvp498qw().b[26][1]++;
    }
    cov_1ktvp498qw().s[141]++;
    if (sfp <= 1.25) {
      /* istanbul ignore next */
      cov_1ktvp498qw().b[27][0]++;
      cov_1ktvp498qw().s[142]++;
      return 70;
    } else
    /* istanbul ignore next */
    {
      cov_1ktvp498qw().b[27][1]++;
    }
    cov_1ktvp498qw().s[143]++;
    if (sfp <= 1.5) {
      /* istanbul ignore next */
      cov_1ktvp498qw().b[28][0]++;
      cov_1ktvp498qw().s[144]++;
      return 50;
    } else
    /* istanbul ignore next */
    {
      cov_1ktvp498qw().b[28][1]++;
    }
    cov_1ktvp498qw().s[145]++;
    if (sfp <= 2.0) {
      /* istanbul ignore next */
      cov_1ktvp498qw().b[29][0]++;
      cov_1ktvp498qw().s[146]++;
      return 30;
    } else
    /* istanbul ignore next */
    {
      cov_1ktvp498qw().b[29][1]++;
    }
    cov_1ktvp498qw().s[147]++;
    return 10;
  }
  /**
   * Calculate carbon score
   */
  static calculateCarbonScore(intensity) {
    /* istanbul ignore next */
    cov_1ktvp498qw().f[22]++;
    cov_1ktvp498qw().s[148]++;
    if (intensity <= 0.06) {
      /* istanbul ignore next */
      cov_1ktvp498qw().b[30][0]++;
      cov_1ktvp498qw().s[149]++;
      return 100;
    } else
    /* istanbul ignore next */
    {
      cov_1ktvp498qw().b[30][1]++;
    }
    cov_1ktvp498qw().s[150]++;
    if (intensity <= 0.10) {
      /* istanbul ignore next */
      cov_1ktvp498qw().b[31][0]++;
      cov_1ktvp498qw().s[151]++;
      return 85;
    } else
    /* istanbul ignore next */
    {
      cov_1ktvp498qw().b[31][1]++;
    }
    cov_1ktvp498qw().s[152]++;
    if (intensity <= 0.15) {
      /* istanbul ignore next */
      cov_1ktvp498qw().b[32][0]++;
      cov_1ktvp498qw().s[153]++;
      return 70;
    } else
    /* istanbul ignore next */
    {
      cov_1ktvp498qw().b[32][1]++;
    }
    cov_1ktvp498qw().s[154]++;
    if (intensity <= 0.20) {
      /* istanbul ignore next */
      cov_1ktvp498qw().b[33][0]++;
      cov_1ktvp498qw().s[155]++;
      return 50;
    } else
    /* istanbul ignore next */
    {
      cov_1ktvp498qw().b[33][1]++;
    }
    cov_1ktvp498qw().s[156]++;
    if (intensity <= 0.30) {
      /* istanbul ignore next */
      cov_1ktvp498qw().b[34][0]++;
      cov_1ktvp498qw().s[157]++;
      return 30;
    } else
    /* istanbul ignore next */
    {
      cov_1ktvp498qw().b[34][1]++;
    }
    cov_1ktvp498qw().s[158]++;
    return 10;
  }
  /**
   * Calculate resource score
   */
  static calculateResourceScore(resourceEfficiency) {
    /* istanbul ignore next */
    cov_1ktvp498qw().f[23]++;
    const materialScore =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[159]++, resourceEfficiency.recyclableContent); // 0-100
    let durabilityScore;
    /* istanbul ignore next */
    cov_1ktvp498qw().s[160]++;
    if (resourceEfficiency.durabilityRating === 'Excellent') {
      /* istanbul ignore next */
      cov_1ktvp498qw().b[35][0]++;
      cov_1ktvp498qw().s[161]++;
      durabilityScore = 100;
    } else {
      /* istanbul ignore next */
      cov_1ktvp498qw().b[35][1]++;
      cov_1ktvp498qw().s[162]++;
      if (resourceEfficiency.durabilityRating === 'Good') {
        /* istanbul ignore next */
        cov_1ktvp498qw().b[36][0]++;
        cov_1ktvp498qw().s[163]++;
        durabilityScore = 80;
      } else {
        /* istanbul ignore next */
        cov_1ktvp498qw().b[36][1]++;
        cov_1ktvp498qw().s[164]++;
        if (resourceEfficiency.durabilityRating === 'Average') {
          /* istanbul ignore next */
          cov_1ktvp498qw().b[37][0]++;
          cov_1ktvp498qw().s[165]++;
          durabilityScore = 60;
        } else {
          /* istanbul ignore next */
          cov_1ktvp498qw().b[37][1]++;
          cov_1ktvp498qw().s[166]++;
          durabilityScore = 40;
        }
      }
    }
    /* istanbul ignore next */
    cov_1ktvp498qw().s[167]++;
    return (materialScore + durabilityScore) / 2;
  }
  /**
   * Get score rating
   */
  static getScoreRating(score) {
    /* istanbul ignore next */
    cov_1ktvp498qw().f[24]++;
    cov_1ktvp498qw().s[168]++;
    if (score >= 90) {
      /* istanbul ignore next */
      cov_1ktvp498qw().b[38][0]++;
      cov_1ktvp498qw().s[169]++;
      return 'Excellent';
    } else
    /* istanbul ignore next */
    {
      cov_1ktvp498qw().b[38][1]++;
    }
    cov_1ktvp498qw().s[170]++;
    if (score >= 80) {
      /* istanbul ignore next */
      cov_1ktvp498qw().b[39][0]++;
      cov_1ktvp498qw().s[171]++;
      return 'Very Good';
    } else
    /* istanbul ignore next */
    {
      cov_1ktvp498qw().b[39][1]++;
    }
    cov_1ktvp498qw().s[172]++;
    if (score >= 70) {
      /* istanbul ignore next */
      cov_1ktvp498qw().b[40][0]++;
      cov_1ktvp498qw().s[173]++;
      return 'Good';
    } else
    /* istanbul ignore next */
    {
      cov_1ktvp498qw().b[40][1]++;
    }
    cov_1ktvp498qw().s[174]++;
    if (score >= 60) {
      /* istanbul ignore next */
      cov_1ktvp498qw().b[41][0]++;
      cov_1ktvp498qw().s[175]++;
      return 'Fair';
    } else
    /* istanbul ignore next */
    {
      cov_1ktvp498qw().b[41][1]++;
    }
    cov_1ktvp498qw().s[176]++;
    if (score >= 50) {
      /* istanbul ignore next */
      cov_1ktvp498qw().b[42][0]++;
      cov_1ktvp498qw().s[177]++;
      return 'Poor';
    } else
    /* istanbul ignore next */
    {
      cov_1ktvp498qw().b[42][1]++;
    }
    cov_1ktvp498qw().s[178]++;
    return 'Very Poor';
  }
  /**
   * Identify improvement areas
   */
  static identifyImprovementAreas(energyScore, carbonScore, resourceScore) {
    /* istanbul ignore next */
    cov_1ktvp498qw().f[25]++;
    const areas =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[179]++, []);
    /* istanbul ignore next */
    cov_1ktvp498qw().s[180]++;
    if (energyScore < 70) {
      /* istanbul ignore next */
      cov_1ktvp498qw().b[43][0]++;
      cov_1ktvp498qw().s[181]++;
      areas.push('Energy Efficiency');
    } else
    /* istanbul ignore next */
    {
      cov_1ktvp498qw().b[43][1]++;
    }
    cov_1ktvp498qw().s[182]++;
    if (carbonScore < 70) {
      /* istanbul ignore next */
      cov_1ktvp498qw().b[44][0]++;
      cov_1ktvp498qw().s[183]++;
      areas.push('Carbon Emissions');
    } else
    /* istanbul ignore next */
    {
      cov_1ktvp498qw().b[44][1]++;
    }
    cov_1ktvp498qw().s[184]++;
    if (resourceScore < 70) {
      /* istanbul ignore next */
      cov_1ktvp498qw().b[45][0]++;
      cov_1ktvp498qw().s[185]++;
      areas.push('Resource Efficiency');
    } else
    /* istanbul ignore next */
    {
      cov_1ktvp498qw().b[45][1]++;
    }
    cov_1ktvp498qw().s[186]++;
    return areas;
  }
  /**
   * Calculate carbon neutrality progress
   */
  static calculateCarbonNeutralityProgress(totalEmissions) {
    /* istanbul ignore next */
    cov_1ktvp498qw().f[26]++;
    // Simplified progress calculation based on emissions intensity
    const targetIntensity =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[187]++, 0.02); // kg CO2e/CFM/year for carbon neutrality
    const currentIntensity =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[188]++, totalEmissions / 10000); // Assuming 10,000 CFM system
    /* istanbul ignore next */
    cov_1ktvp498qw().s[189]++;
    if (currentIntensity <= targetIntensity) {
      /* istanbul ignore next */
      cov_1ktvp498qw().b[46][0]++;
      cov_1ktvp498qw().s[190]++;
      return 100;
    } else
    /* istanbul ignore next */
    {
      cov_1ktvp498qw().b[46][1]++;
    }
    const maxIntensity =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[191]++, 0.30); // Baseline high emissions
    const progress =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[192]++, Math.max(0, (maxIntensity - currentIntensity) / (maxIntensity - targetIntensity) * 100));
    /* istanbul ignore next */
    cov_1ktvp498qw().s[193]++;
    return Math.min(100, progress);
  }
  /**
   * Assess certification readiness
   */
  static assessCertificationReadiness(environmentalScore) {
    /* istanbul ignore next */
    cov_1ktvp498qw().f[27]++;
    const overallScore =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[194]++, environmentalScore.overallScore);
    let leedReadiness;
    /* istanbul ignore next */
    cov_1ktvp498qw().s[195]++;
    if (overallScore >= 70) {
      /* istanbul ignore next */
      cov_1ktvp498qw().b[47][0]++;
      cov_1ktvp498qw().s[196]++;
      leedReadiness = 'Ready';
    } else {
      /* istanbul ignore next */
      cov_1ktvp498qw().b[47][1]++;
      cov_1ktvp498qw().s[197]++;
      if (overallScore >= 60) {
        /* istanbul ignore next */
        cov_1ktvp498qw().b[48][0]++;
        cov_1ktvp498qw().s[198]++;
        leedReadiness = 'Near Ready';
      } else {
        /* istanbul ignore next */
        cov_1ktvp498qw().b[48][1]++;
        cov_1ktvp498qw().s[199]++;
        leedReadiness = 'Not Ready';
      }
    }
    let breeamReadiness;
    /* istanbul ignore next */
    cov_1ktvp498qw().s[200]++;
    if (overallScore >= 75) {
      /* istanbul ignore next */
      cov_1ktvp498qw().b[49][0]++;
      cov_1ktvp498qw().s[201]++;
      breeamReadiness = 'Ready';
    } else {
      /* istanbul ignore next */
      cov_1ktvp498qw().b[49][1]++;
      cov_1ktvp498qw().s[202]++;
      if (overallScore >= 65) {
        /* istanbul ignore next */
        cov_1ktvp498qw().b[50][0]++;
        cov_1ktvp498qw().s[203]++;
        breeamReadiness = 'Near Ready';
      } else {
        /* istanbul ignore next */
        cov_1ktvp498qw().b[50][1]++;
        cov_1ktvp498qw().s[204]++;
        breeamReadiness = 'Not Ready';
      }
    }
    let greenBuildingReadiness;
    /* istanbul ignore next */
    cov_1ktvp498qw().s[205]++;
    if (overallScore >= 80) {
      /* istanbul ignore next */
      cov_1ktvp498qw().b[51][0]++;
      cov_1ktvp498qw().s[206]++;
      greenBuildingReadiness = 'Ready';
    } else {
      /* istanbul ignore next */
      cov_1ktvp498qw().b[51][1]++;
      cov_1ktvp498qw().s[207]++;
      if (overallScore >= 70) {
        /* istanbul ignore next */
        cov_1ktvp498qw().b[52][0]++;
        cov_1ktvp498qw().s[208]++;
        greenBuildingReadiness = 'Near Ready';
      } else {
        /* istanbul ignore next */
        cov_1ktvp498qw().b[52][1]++;
        cov_1ktvp498qw().s[209]++;
        greenBuildingReadiness = 'Not Ready';
      }
    }
    /* istanbul ignore next */
    cov_1ktvp498qw().s[210]++;
    return {
      leedReadiness,
      breeamReadiness,
      energyStarReadiness: environmentalScore.energyScore >= 80 ?
      /* istanbul ignore next */
      (cov_1ktvp498qw().b[53][0]++, 'Ready') :
      /* istanbul ignore next */
      (cov_1ktvp498qw().b[53][1]++, 'Not Ready'),
      greenBuildingReadiness
    };
  }
  /**
   * Generate unique analysis ID
   */
  static generateAnalysisId(systemId) {
    /* istanbul ignore next */
    cov_1ktvp498qw().f[28]++;
    const timestamp =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[211]++, Date.now());
    const random =
    /* istanbul ignore next */
    (cov_1ktvp498qw().s[212]++, Math.random().toString(36).substring(2, 8));
    /* istanbul ignore next */
    cov_1ktvp498qw().s[213]++;
    return `environmental_analysis_${systemId}_${timestamp}_${random}`;
  }
}
/* istanbul ignore next */
cov_1ktvp498qw().s[214]++;
exports.EnvironmentalImpactAssessmentEngine = EnvironmentalImpactAssessmentEngine;
/* istanbul ignore next */
cov_1ktvp498qw().s[215]++;
EnvironmentalImpactAssessmentEngine.VERSION = '3.0.0';
/* istanbul ignore next */
cov_1ktvp498qw().s[216]++;
EnvironmentalImpactAssessmentEngine.ENVIRONMENTAL_CACHE = new Map();
// Carbon emission factors (kg CO2e per unit)
/* istanbul ignore next */
cov_1ktvp498qw().s[217]++;
EnvironmentalImpactAssessmentEngine.EMISSION_FACTORS = {
  ELECTRICITY: {
    US_GRID_AVERAGE: 0.4,
    // kg CO2e/kWh
    COAL: 0.82,
    NATURAL_GAS: 0.35,
    RENEWABLE: 0.02,
    NUCLEAR: 0.012
  },
  MATERIALS: {
    STEEL: 1.85,
    // kg CO2e/kg
    ALUMINUM: 8.24,
    COPPER: 2.95,
    GALVANIZED_STEEL: 2.1,
    STAINLESS_STEEL: 2.9,
    INSULATION: 1.2
  },
  REFRIGERANTS: {
    R410A: 2088,
    // kg CO2e/kg (GWP)
    R134A: 1430,
    R32: 675,
    NATURAL: 1 // Natural refrigerants
  }
};
// Material quantities (kg per CFM)
/* istanbul ignore next */
cov_1ktvp498qw().s[218]++;
EnvironmentalImpactAssessmentEngine.MATERIAL_INTENSITY = {
  DUCTWORK_STEEL: 0.8,
  // kg steel per CFM
  FITTINGS_STEEL: 0.2,
  INSULATION: 0.3,
  DAMPERS_STEEL: 0.1,
  FAN_STEEL: 0.5,
  FAN_ALUMINUM: 0.2
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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