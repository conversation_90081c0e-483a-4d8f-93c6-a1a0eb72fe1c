{"version": 3, "names": ["cov_1ktvp498qw", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "name", "decl", "loc", "branchMap", "type", "locations", "undefined", "s", "f", "b", "inputSourceMap", "file", "mappings", "names", "sources", "sourcesContent", "version", "_coverageSchema", "coverage", "actualCoverage", "SystemAnalysisTypes_1", "require", "EnvironmentalImpactAssessmentEngine", "assessEnvironmentalImpact", "systemConfiguration", "energyAnalysis", "operatingProfile", "locationData", "analysisId", "generateAnalysisId", "id", "timestamp", "Date", "carbonFootprint", "calculateCarbonFootprint", "sustainabilityMetrics", "calculateSustainabilityMetrics", "greenBuildingCompliance", "assessGreenBuildingCompliance", "environmentalCertifications", "evaluateEnvironmentalCertifications", "lifecycleAssessment", "performLifecycleAssessment", "recommendations", "generateEnvironmentalRecommendations", "analysis", "systemId", "analysisTimestamp", "ENVIRONMENTAL_CACHE", "set", "error", "Error", "message", "designAirflow", "designParameters", "annualEnergyConsumption", "energyConsumption", "totalConsumption", "value", "emissionFactor", "getLocationEmissionFactor", "operationalEmissions", "createEmissionMeasurement", "EmissionUnits", "KG_CO2E", "TimeFrame", "ANNUALLY", "EmissionScope", "SCOPE_2", "embodiedEmissions", "calculateEmbodiedEmissions", "refrigerantEmissions", "calculateRefrigerantEmissions", "totalEmissions", "emissionsBySource", "source", "emissions", "percentage", "scope", "SCOPE_3", "SCOPE_1", "emissionsTrend", "calculateEmissionsTrend", "offsetOpportunities", "identifyOffsetOpportunities", "benchmarkComparison", "performEmissionsBenchmarking", "EMISSION_FACTORS", "ELECTRICITY", "US_GRID_AVERAGE", "regionalFactors", "region", "materialQuantities", "ductworkSteel", "MATERIAL_INTENSITY", "DUCTWORK_STEEL", "fittingsSteel", "FITTINGS_STEEL", "insulation", "INSULATION", "dampersSteel", "DAMPERS_STEEL", "fanSteel", "FAN_STEEL", "fanAluminum", "FAN_ALUMINUM", "MATERIALS", "GALVANIZED_STEEL", "STEEL", "ALUMINUM", "annualEmbodiedEmissions", "hasRefrigerants", "systemType", "includes", "refrigerantCharge", "leakageRate", "gwp", "REFRIGERANTS", "R410A", "annualLeakage", "annualEmissions", "units", "timeFrame", "accuracy", "currentEmissions", "projectedEmissions", "reductionPotential", "trendDirection", "timeHorizon", "opportunities", "push", "potential", "cost", "implementation", "paybackPeriod", "additionalBenefits", "Infinity", "emissionIntensity", "benchmarks", "industryAverage", "bestPractice", "regulatoryTarget", "netZeroTarget", "percentile", "calculateEmissionsPercentile", "benchmarkType", "currentIntensity", "benchmarkIntensity", "improvementPotential", "Math", "max", "complianceGap", "netZeroGap", "intensity", "average", "ratio", "energyEfficiency", "specificFan<PERSON>ower", "efficiencyMetrics", "energyUtilizationIndex", "systemEfficiency", "calculateEfficiencyBenchmark", "carbonIntensity", "operationalIntensity", "embodiedIntensity", "calculateCarbonBenchmark", "resourceEfficiency", "materialEfficiency", "calculateMaterialEfficiency", "waterUsage", "calculateWaterUsage", "wasteGeneration", "calculateWasteGeneration", "recyclingPotential", "calculateRecyclingPotential", "environmentalScore", "calculateEnvironmentalScore", "sustainabilityTargets", "carbonNeutralityTarget", "currentProgress", "calculateCarbonNeutralityProgress", "energyEfficiencyTarget", "currentEfficiencyProgress", "min", "renewableEnergyTarget", "currentRenewableProgress", "certificationReadiness", "assessCertificationReadiness", "sfp", "rating", "calculateSFPPercentile", "totalMaterialIntensity", "Object", "values", "reduce", "sum", "materialIntensity", "recyclable<PERSON><PERSON>nt", "durabilityRating", "calculateDurabilityRating", "maintenanceRequirement", "designPressure", "hasHumidification", "filterWaste", "maintenanceWaste", "steelContent", "aluminumContent", "otherContent", "steelRecyclability", "aluminumRecyclability", "otherRecyclability", "energyScore", "calculateEnergyScore", "carbonScore", "calculateCarbonScore", "resourceScore", "calculateResourceScore", "overallScore", "getScoreRating", "improvementAreas", "identifyImprovementAreas", "materialScore", "durabilityScore", "score", "areas", "targetIntensity", "maxIntensity", "progress", "leedReadiness", "breeamReadiness", "greenBuildingReadiness", "energyStarReadiness", "now", "random", "toString", "substring", "exports", "VERSION", "Map", "COAL", "NATURAL_GAS", "RENEWABLE", "NUCLEAR", "COPPER", "STAINLESS_STEEL", "R134A", "R32", "NATURAL"], "sources": ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\EnvironmentalImpactAssessmentEngine.ts"], "sourcesContent": ["/**\r\n * Environmental Impact Assessment Engine\r\n * \r\n * Comprehensive environmental impact assessment service for Phase 3 Priority 3: Advanced System Analysis Tools\r\n * Provides carbon footprint calculation, environmental compliance checking, sustainability metrics,\r\n * and green building certification support for HVAC duct systems.\r\n * \r\n * @version 3.0.0\r\n * <AUTHOR> Suite Development Team\r\n */\r\n\r\nimport {\r\n  EnvironmentalImpactAnalysis,\r\n  CarbonFootprint,\r\n  SustainabilityMetrics,\r\n  GreenBuildingCompliance,\r\n  EnvironmentalCertification,\r\n  LifecycleAssessment,\r\n  EnvironmentalRecommendation,\r\n  EmissionMeasurement,\r\n  EmissionUnits,\r\n  EmissionScope,\r\n  SystemConfiguration,\r\n  EnergyAnalysis,\r\n  TimeFrame,\r\n  MeasurementSource\r\n} from './types/SystemAnalysisTypes';\r\n\r\n/**\r\n * Environmental Impact Assessment Engine\r\n * \r\n * Provides comprehensive environmental impact assessment capabilities including:\r\n * - Carbon footprint calculation and tracking\r\n * - Sustainability metrics and benchmarking\r\n * - Green building certification support (LEED, BREEAM, etc.)\r\n * - Environmental compliance checking\r\n * - Lifecycle environmental impact assessment\r\n * - Environmental improvement recommendations\r\n */\r\nexport class EnvironmentalImpactAssessmentEngine {\r\n  private static readonly VERSION = '3.0.0';\r\n  private static readonly ENVIRONMENTAL_CACHE = new Map<string, EnvironmentalImpactAnalysis>();\r\n  \r\n  // Carbon emission factors (kg CO2e per unit)\r\n  private static readonly EMISSION_FACTORS = {\r\n    ELECTRICITY: {\r\n      US_GRID_AVERAGE: 0.4, // kg CO2e/kWh\r\n      COAL: 0.82,\r\n      NATURAL_GAS: 0.35,\r\n      RENEWABLE: 0.02,\r\n      NUCLEAR: 0.012\r\n    },\r\n    MATERIALS: {\r\n      STEEL: 1.85, // kg CO2e/kg\r\n      ALUMINUM: 8.24,\r\n      COPPER: 2.95,\r\n      GALVANIZED_STEEL: 2.1,\r\n      STAINLESS_STEEL: 2.9,\r\n      INSULATION: 1.2\r\n    },\r\n    REFRIGERANTS: {\r\n      R410A: 2088, // kg CO2e/kg (GWP)\r\n      R134A: 1430,\r\n      R32: 675,\r\n      NATURAL: 1 // Natural refrigerants\r\n    }\r\n  };\r\n\r\n  // Material quantities (kg per CFM)\r\n  private static readonly MATERIAL_INTENSITY = {\r\n    DUCTWORK_STEEL: 0.8, // kg steel per CFM\r\n    FITTINGS_STEEL: 0.2,\r\n    INSULATION: 0.3,\r\n    DAMPERS_STEEL: 0.1,\r\n    FAN_STEEL: 0.5,\r\n    FAN_ALUMINUM: 0.2\r\n  };\r\n\r\n  /**\r\n   * Perform comprehensive environmental impact assessment\r\n   */\r\n  public static async assessEnvironmentalImpact(\r\n    systemConfiguration: SystemConfiguration,\r\n    energyAnalysis: EnergyAnalysis,\r\n    operatingProfile?: OperatingProfile,\r\n    locationData?: LocationData\r\n  ): Promise<EnvironmentalImpactAnalysis> {\r\n    try {\r\n      const analysisId = this.generateAnalysisId(systemConfiguration.id);\r\n      const timestamp = new Date();\r\n\r\n      // Calculate carbon footprint\r\n      const carbonFootprint = await this.calculateCarbonFootprint(\r\n        systemConfiguration,\r\n        energyAnalysis,\r\n        operatingProfile,\r\n        locationData\r\n      );\r\n\r\n      // Calculate sustainability metrics\r\n      const sustainabilityMetrics = await this.calculateSustainabilityMetrics(\r\n        systemConfiguration,\r\n        energyAnalysis,\r\n        carbonFootprint\r\n      );\r\n\r\n      // Assess green building compliance\r\n      const greenBuildingCompliance = await this.assessGreenBuildingCompliance(\r\n        systemConfiguration,\r\n        energyAnalysis,\r\n        sustainabilityMetrics\r\n      );\r\n\r\n      // Evaluate environmental certifications\r\n      const environmentalCertifications = await this.evaluateEnvironmentalCertifications(\r\n        systemConfiguration,\r\n        sustainabilityMetrics,\r\n        greenBuildingCompliance\r\n      );\r\n\r\n      // Perform lifecycle assessment\r\n      const lifecycleAssessment = await this.performLifecycleAssessment(\r\n        systemConfiguration,\r\n        energyAnalysis,\r\n        carbonFootprint\r\n      );\r\n\r\n      // Generate environmental recommendations\r\n      const recommendations = await this.generateEnvironmentalRecommendations(\r\n        systemConfiguration,\r\n        carbonFootprint,\r\n        sustainabilityMetrics,\r\n        greenBuildingCompliance\r\n      );\r\n\r\n      const analysis: EnvironmentalImpactAnalysis = {\r\n        id: analysisId,\r\n        systemId: systemConfiguration.id,\r\n        analysisTimestamp: timestamp,\r\n        carbonFootprint,\r\n        sustainabilityMetrics,\r\n        greenBuildingCompliance,\r\n        environmentalCertifications,\r\n        lifecycleAssessment,\r\n        recommendations\r\n      };\r\n\r\n      // Cache the analysis\r\n      this.ENVIRONMENTAL_CACHE.set(analysisId, analysis);\r\n\r\n      return analysis;\r\n\r\n    } catch (error) {\r\n      throw new Error(`Environmental impact assessment failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Calculate comprehensive carbon footprint\r\n   */\r\n  private static async calculateCarbonFootprint(\r\n    systemConfiguration: SystemConfiguration,\r\n    energyAnalysis: EnergyAnalysis,\r\n    operatingProfile?: OperatingProfile,\r\n    locationData?: LocationData\r\n  ): Promise<CarbonFootprint> {\r\n    const designAirflow = systemConfiguration.designParameters.designAirflow;\r\n    const annualEnergyConsumption = energyAnalysis.energyConsumption.totalConsumption.value;\r\n\r\n    // Get location-specific emission factor\r\n    const emissionFactor = this.getLocationEmissionFactor(locationData);\r\n\r\n    // Calculate operational emissions (Scope 2)\r\n    const operationalEmissions = this.createEmissionMeasurement(\r\n      annualEnergyConsumption * emissionFactor,\r\n      EmissionUnits.KG_CO2E,\r\n      TimeFrame.ANNUALLY,\r\n      EmissionScope.SCOPE_2\r\n    );\r\n\r\n    // Calculate embodied emissions (Scope 3)\r\n    const embodiedEmissions = await this.calculateEmbodiedEmissions(systemConfiguration);\r\n\r\n    // Calculate refrigerant emissions (Scope 1, if applicable)\r\n    const refrigerantEmissions = this.calculateRefrigerantEmissions(systemConfiguration);\r\n\r\n    // Total emissions\r\n    const totalEmissions = this.createEmissionMeasurement(\r\n      operationalEmissions.value + embodiedEmissions.value + refrigerantEmissions.value,\r\n      EmissionUnits.KG_CO2E,\r\n      TimeFrame.ANNUALLY,\r\n      EmissionScope.SCOPE_2\r\n    );\r\n\r\n    // Emissions by source breakdown\r\n    const emissionsBySource = [\r\n      {\r\n        source: 'Electricity Consumption',\r\n        emissions: operationalEmissions.value,\r\n        percentage: (operationalEmissions.value / totalEmissions.value) * 100,\r\n        scope: EmissionScope.SCOPE_2,\r\n        emissionFactor\r\n      },\r\n      {\r\n        source: 'Embodied Carbon',\r\n        emissions: embodiedEmissions.value,\r\n        percentage: (embodiedEmissions.value / totalEmissions.value) * 100,\r\n        scope: EmissionScope.SCOPE_3,\r\n        emissionFactor: 0\r\n      },\r\n      {\r\n        source: 'Refrigerant Leakage',\r\n        emissions: refrigerantEmissions.value,\r\n        percentage: (refrigerantEmissions.value / totalEmissions.value) * 100,\r\n        scope: EmissionScope.SCOPE_1,\r\n        emissionFactor: 0\r\n      }\r\n    ];\r\n\r\n    // Emissions trend analysis\r\n    const emissionsTrend = this.calculateEmissionsTrend(totalEmissions.value, operatingProfile);\r\n\r\n    // Offset opportunities\r\n    const offsetOpportunities = this.identifyOffsetOpportunities(\r\n      totalEmissions.value,\r\n      operationalEmissions.value,\r\n      systemConfiguration\r\n    );\r\n\r\n    // Benchmark comparison\r\n    const benchmarkComparison = this.performEmissionsBenchmarking(\r\n      totalEmissions.value,\r\n      designAirflow,\r\n      systemConfiguration\r\n    );\r\n\r\n    return {\r\n      totalEmissions,\r\n      operationalEmissions,\r\n      embodiedEmissions,\r\n      emissionsBySource,\r\n      emissionsTrend,\r\n      offsetOpportunities,\r\n      benchmarkComparison\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get location-specific emission factor\r\n   */\r\n  private static getLocationEmissionFactor(locationData?: LocationData): number {\r\n    if (!locationData) {\r\n      return this.EMISSION_FACTORS.ELECTRICITY.US_GRID_AVERAGE;\r\n    }\r\n\r\n    // Regional emission factors (simplified)\r\n    const regionalFactors: { [key: string]: number } = {\r\n      'US-CA': 0.25, // California (cleaner grid)\r\n      'US-TX': 0.45, // Texas\r\n      'US-NY': 0.28, // New York\r\n      'US-FL': 0.42, // Florida\r\n      'US-WA': 0.15, // Washington (hydro power)\r\n      'US-WV': 0.75, // West Virginia (coal heavy)\r\n      'DEFAULT': this.EMISSION_FACTORS.ELECTRICITY.US_GRID_AVERAGE\r\n    };\r\n\r\n    return regionalFactors[locationData.region] || regionalFactors['DEFAULT'];\r\n  }\r\n\r\n  /**\r\n   * Calculate embodied emissions from materials\r\n   */\r\n  private static async calculateEmbodiedEmissions(\r\n    systemConfiguration: SystemConfiguration\r\n  ): Promise<EmissionMeasurement> {\r\n    const designAirflow = systemConfiguration.designParameters.designAirflow;\r\n\r\n    // Calculate material quantities\r\n    const materialQuantities = {\r\n      ductworkSteel: designAirflow * this.MATERIAL_INTENSITY.DUCTWORK_STEEL,\r\n      fittingsSteel: designAirflow * this.MATERIAL_INTENSITY.FITTINGS_STEEL,\r\n      insulation: designAirflow * this.MATERIAL_INTENSITY.INSULATION,\r\n      dampersSteel: designAirflow * this.MATERIAL_INTENSITY.DAMPERS_STEEL,\r\n      fanSteel: designAirflow * this.MATERIAL_INTENSITY.FAN_STEEL,\r\n      fanAluminum: designAirflow * this.MATERIAL_INTENSITY.FAN_ALUMINUM\r\n    };\r\n\r\n    // Calculate embodied emissions for each material\r\n    const embodiedEmissions = \r\n      (materialQuantities.ductworkSteel * this.EMISSION_FACTORS.MATERIALS.GALVANIZED_STEEL) +\r\n      (materialQuantities.fittingsSteel * this.EMISSION_FACTORS.MATERIALS.STEEL) +\r\n      (materialQuantities.insulation * this.EMISSION_FACTORS.MATERIALS.INSULATION) +\r\n      (materialQuantities.dampersSteel * this.EMISSION_FACTORS.MATERIALS.STEEL) +\r\n      (materialQuantities.fanSteel * this.EMISSION_FACTORS.MATERIALS.STEEL) +\r\n      (materialQuantities.fanAluminum * this.EMISSION_FACTORS.MATERIALS.ALUMINUM);\r\n\r\n    // Amortize over equipment life (20 years typical)\r\n    const annualEmbodiedEmissions = embodiedEmissions / 20;\r\n\r\n    return this.createEmissionMeasurement(\r\n      annualEmbodiedEmissions,\r\n      EmissionUnits.KG_CO2E,\r\n      TimeFrame.ANNUALLY,\r\n      EmissionScope.SCOPE_3\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Calculate refrigerant emissions\r\n   */\r\n  private static calculateRefrigerantEmissions(\r\n    systemConfiguration: SystemConfiguration\r\n  ): EmissionMeasurement {\r\n    // Simplified refrigerant emissions calculation\r\n    // Assumes 2% annual leakage rate for systems with refrigerants\r\n    const hasRefrigerants = systemConfiguration.systemType.includes('cooling') || \r\n                           systemConfiguration.systemType.includes('heat_pump');\r\n\r\n    if (!hasRefrigerants) {\r\n      return this.createEmissionMeasurement(0, EmissionUnits.KG_CO2E, TimeFrame.ANNUALLY, EmissionScope.SCOPE_1);\r\n    }\r\n\r\n    const designAirflow = systemConfiguration.designParameters.designAirflow;\r\n    const refrigerantCharge = designAirflow * 0.001; // kg refrigerant per CFM (simplified)\r\n    const leakageRate = 0.02; // 2% annual leakage\r\n    const gwp = this.EMISSION_FACTORS.REFRIGERANTS.R410A; // Assume R410A\r\n\r\n    const annualLeakage = refrigerantCharge * leakageRate;\r\n    const annualEmissions = annualLeakage * gwp;\r\n\r\n    return this.createEmissionMeasurement(\r\n      annualEmissions,\r\n      EmissionUnits.KG_CO2E,\r\n      TimeFrame.ANNUALLY,\r\n      EmissionScope.SCOPE_1\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Create standardized emission measurement\r\n   */\r\n  private static createEmissionMeasurement(\r\n    value: number,\r\n    units: EmissionUnits,\r\n    timeFrame: TimeFrame,\r\n    scope: EmissionScope,\r\n    accuracy: number = 0.8\r\n  ): EmissionMeasurement {\r\n    return {\r\n      value,\r\n      units,\r\n      timeFrame,\r\n      scope,\r\n      accuracy\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate emissions trend\r\n   */\r\n  private static calculateEmissionsTrend(\r\n    currentEmissions: number,\r\n    operatingProfile?: OperatingProfile\r\n  ): EmissionsTrend {\r\n    // Simplified trend calculation\r\n    const projectedEmissions = currentEmissions * 0.98; // 2% annual improvement assumed\r\n    const reductionPotential = currentEmissions * 0.4; // 40% reduction potential\r\n\r\n    return {\r\n      currentEmissions,\r\n      trendDirection: 'improving' as const,\r\n      projectedEmissions,\r\n      reductionPotential,\r\n      timeHorizon: 10 // years\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Identify carbon offset opportunities\r\n   */\r\n  private static identifyOffsetOpportunities(\r\n    totalEmissions: number,\r\n    operationalEmissions: number,\r\n    systemConfiguration: SystemConfiguration\r\n  ): OffsetOpportunity[] {\r\n    const opportunities: OffsetOpportunity[] = [];\r\n\r\n    // Renewable energy opportunity\r\n    opportunities.push({\r\n      type: 'Renewable Energy',\r\n      potential: operationalEmissions * 0.9, // 90% offset potential\r\n      cost: operationalEmissions * 0.02, // $0.02/kg CO2e\r\n      implementation: 'On-site solar installation or renewable energy credits',\r\n      paybackPeriod: 8, // years\r\n      additionalBenefits: ['Energy cost savings', 'Grid independence', 'Marketing value']\r\n    });\r\n\r\n    // Energy efficiency opportunity\r\n    opportunities.push({\r\n      type: 'Energy Efficiency',\r\n      potential: operationalEmissions * 0.25, // 25% reduction potential\r\n      cost: -5000, // Cost savings\r\n      implementation: 'System optimization and efficiency improvements',\r\n      paybackPeriod: 2, // years\r\n      additionalBenefits: ['Reduced operating costs', 'Improved performance', 'Extended equipment life']\r\n    });\r\n\r\n    // Carbon sequestration opportunity\r\n    opportunities.push({\r\n      type: 'Carbon Sequestration',\r\n      potential: totalEmissions * 0.1, // 10% offset potential\r\n      cost: totalEmissions * 0.015, // $0.015/kg CO2e\r\n      implementation: 'Tree planting or carbon credit purchase',\r\n      paybackPeriod: Infinity, // No financial payback\r\n      additionalBenefits: ['Biodiversity support', 'Community engagement', 'Brand enhancement']\r\n    });\r\n\r\n    return opportunities;\r\n  }\r\n\r\n  /**\r\n   * Perform emissions benchmarking\r\n   */\r\n  private static performEmissionsBenchmarking(\r\n    totalEmissions: number,\r\n    designAirflow: number,\r\n    systemConfiguration: SystemConfiguration\r\n  ): EmissionsBenchmark {\r\n    const emissionIntensity = totalEmissions / designAirflow; // kg CO2e/CFM\r\n\r\n    // Industry benchmarks (kg CO2e/CFM/year)\r\n    const benchmarks = {\r\n      industryAverage: 0.12,\r\n      bestPractice: 0.06,\r\n      regulatoryTarget: 0.10,\r\n      netZeroTarget: 0.02\r\n    };\r\n\r\n    const percentile = this.calculateEmissionsPercentile(emissionIntensity, benchmarks.industryAverage);\r\n\r\n    return {\r\n      benchmarkType: 'Industry Average',\r\n      currentIntensity: emissionIntensity,\r\n      benchmarkIntensity: benchmarks.industryAverage,\r\n      percentile,\r\n      improvementPotential: Math.max(0, emissionIntensity - benchmarks.bestPractice),\r\n      complianceGap: Math.max(0, emissionIntensity - benchmarks.regulatoryTarget),\r\n      netZeroGap: Math.max(0, emissionIntensity - benchmarks.netZeroTarget)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate emissions percentile\r\n   */\r\n  private static calculateEmissionsPercentile(intensity: number, average: number): number {\r\n    // Simplified percentile calculation\r\n    const ratio = intensity / average;\r\n    \r\n    if (ratio <= 0.5) return 95; // Top 5%\r\n    if (ratio <= 0.7) return 80; // Top 20%\r\n    if (ratio <= 0.9) return 60; // Top 40%\r\n    if (ratio <= 1.1) return 40; // Average\r\n    if (ratio <= 1.3) return 20; // Bottom 20%\r\n    return 5; // Bottom 5%\r\n  }\r\n\r\n  /**\r\n   * Calculate sustainability metrics\r\n   */\r\n  private static async calculateSustainabilityMetrics(\r\n    systemConfiguration: SystemConfiguration,\r\n    energyAnalysis: EnergyAnalysis,\r\n    carbonFootprint: CarbonFootprint\r\n  ): Promise<SustainabilityMetrics> {\r\n    const designAirflow = systemConfiguration.designParameters.designAirflow;\r\n    const annualEnergyConsumption = energyAnalysis.energyConsumption.totalConsumption.value;\r\n\r\n    // Energy efficiency metrics\r\n    const energyEfficiency = {\r\n      specificFanPower: energyAnalysis.efficiencyMetrics.specificFanPower, // W/CFM\r\n      energyUtilizationIndex: annualEnergyConsumption / (designAirflow * 8760), // kWh/CFM/year\r\n      systemEfficiency: energyAnalysis.efficiencyMetrics.systemEfficiency, // %\r\n      benchmarkComparison: this.calculateEfficiencyBenchmark(energyAnalysis.efficiencyMetrics.specificFanPower)\r\n    };\r\n\r\n    // Carbon intensity metrics\r\n    const carbonIntensity = {\r\n      emissionIntensity: carbonFootprint.totalEmissions.value / designAirflow, // kg CO2e/CFM/year\r\n      operationalIntensity: carbonFootprint.operationalEmissions.value / annualEnergyConsumption, // kg CO2e/kWh\r\n      embodiedIntensity: carbonFootprint.embodiedEmissions.value / designAirflow, // kg CO2e/CFM (annual)\r\n      benchmarkComparison: this.calculateCarbonBenchmark(carbonFootprint.totalEmissions.value / designAirflow)\r\n    };\r\n\r\n    // Resource efficiency metrics\r\n    const resourceEfficiency = {\r\n      materialEfficiency: this.calculateMaterialEfficiency(systemConfiguration),\r\n      waterUsage: this.calculateWaterUsage(systemConfiguration), // L/year\r\n      wasteGeneration: this.calculateWasteGeneration(systemConfiguration), // kg/year\r\n      recyclingPotential: this.calculateRecyclingPotential(systemConfiguration) // %\r\n    };\r\n\r\n    // Environmental performance score\r\n    const environmentalScore = this.calculateEnvironmentalScore(\r\n      energyEfficiency,\r\n      carbonIntensity,\r\n      resourceEfficiency\r\n    );\r\n\r\n    // Sustainability targets and progress\r\n    const sustainabilityTargets = {\r\n      carbonNeutralityTarget: 2030,\r\n      currentProgress: this.calculateCarbonNeutralityProgress(carbonFootprint.totalEmissions.value),\r\n      energyEfficiencyTarget: 1.0, // W/CFM SFP target\r\n      currentEfficiencyProgress: Math.min(100, (1.0 / energyEfficiency.specificFanPower) * 100),\r\n      renewableEnergyTarget: 100, // % renewable\r\n      currentRenewableProgress: 20 // % (simplified)\r\n    };\r\n\r\n    return {\r\n      energyEfficiency,\r\n      carbonIntensity,\r\n      resourceEfficiency,\r\n      environmentalScore,\r\n      sustainabilityTargets,\r\n      certificationReadiness: this.assessCertificationReadiness(environmentalScore)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate efficiency benchmark\r\n   */\r\n  private static calculateEfficiencyBenchmark(sfp: number): EfficiencyBenchmark {\r\n    let rating: string;\r\n    if (sfp <= 0.8) {\r\n      rating = 'Excellent';\r\n    } else if (sfp <= 1.0) {\r\n      rating = 'Good';\r\n    } else if (sfp <= 1.25) {\r\n      rating = 'Average';\r\n    } else {\r\n      rating = 'Poor';\r\n    }\r\n\r\n    return {\r\n      rating,\r\n      percentile: this.calculateSFPPercentile(sfp),\r\n      improvementPotential: Math.max(0, sfp - 0.8), // Improvement to excellent level\r\n      industryAverage: 1.1\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate SFP percentile\r\n   */\r\n  private static calculateSFPPercentile(sfp: number): number {\r\n    if (sfp <= 0.8) return 95;\r\n    if (sfp <= 1.0) return 80;\r\n    if (sfp <= 1.25) return 50;\r\n    if (sfp <= 1.5) return 20;\r\n    return 5;\r\n  }\r\n\r\n  /**\r\n   * Calculate carbon benchmark\r\n   */\r\n  private static calculateCarbonBenchmark(intensity: number): CarbonBenchmark {\r\n    let rating: string;\r\n    if (intensity <= 0.06) {\r\n      rating = 'Excellent';\r\n    } else if (intensity <= 0.10) {\r\n      rating = 'Good';\r\n    } else if (intensity <= 0.15) {\r\n      rating = 'Average';\r\n    } else {\r\n      rating = 'Poor';\r\n    }\r\n\r\n    return {\r\n      rating,\r\n      percentile: this.calculateEmissionsPercentile(intensity, 0.12),\r\n      improvementPotential: Math.max(0, intensity - 0.06),\r\n      industryAverage: 0.12\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate material efficiency\r\n   */\r\n  private static calculateMaterialEfficiency(systemConfiguration: SystemConfiguration): MaterialEfficiency {\r\n    // Calculate total material intensity (kg/CFM)\r\n    const totalMaterialIntensity = Object.values(this.MATERIAL_INTENSITY).reduce((sum, intensity) => sum + intensity, 0);\r\n\r\n    return {\r\n      materialIntensity: totalMaterialIntensity, // kg/CFM\r\n      recyclableContent: 85, // % (typical for steel ductwork)\r\n      durabilityRating: this.calculateDurabilityRating(systemConfiguration),\r\n      maintenanceRequirement: 'Low' // Based on system design\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate durability rating\r\n   */\r\n  private static calculateDurabilityRating(systemConfiguration: SystemConfiguration): string {\r\n    const designPressure = systemConfiguration.designParameters.designPressure;\r\n\r\n    if (designPressure <= 2.0) return 'Excellent';\r\n    if (designPressure <= 4.0) return 'Good';\r\n    if (designPressure <= 6.0) return 'Average';\r\n    return 'Fair';\r\n  }\r\n\r\n  /**\r\n   * Calculate water usage\r\n   */\r\n  private static calculateWaterUsage(systemConfiguration: SystemConfiguration): number {\r\n    // Simplified water usage calculation (mainly for humidification if present)\r\n    const hasHumidification = systemConfiguration.systemType.includes('humidification');\r\n    const designAirflow = systemConfiguration.designParameters.designAirflow;\r\n\r\n    if (hasHumidification) {\r\n      return designAirflow * 0.5; // L/year per CFM (simplified)\r\n    }\r\n\r\n    return designAirflow * 0.05; // Minimal water for cleaning/maintenance\r\n  }\r\n\r\n  /**\r\n   * Calculate waste generation\r\n   */\r\n  private static calculateWasteGeneration(systemConfiguration: SystemConfiguration): number {\r\n    const designAirflow = systemConfiguration.designParameters.designAirflow;\r\n\r\n    // Annual waste from filter replacements and maintenance\r\n    const filterWaste = designAirflow * 0.1; // kg/year (filter media)\r\n    const maintenanceWaste = designAirflow * 0.02; // kg/year (misc maintenance waste)\r\n\r\n    return filterWaste + maintenanceWaste;\r\n  }\r\n\r\n  /**\r\n   * Calculate recycling potential\r\n   */\r\n  private static calculateRecyclingPotential(systemConfiguration: SystemConfiguration): number {\r\n    // Steel ductwork and components are highly recyclable\r\n    const steelContent = 0.85; // 85% of system is steel\r\n    const aluminumContent = 0.10; // 10% aluminum\r\n    const otherContent = 0.05; // 5% other materials\r\n\r\n    const steelRecyclability = 0.95; // 95% recyclable\r\n    const aluminumRecyclability = 0.90; // 90% recyclable\r\n    const otherRecyclability = 0.30; // 30% recyclable\r\n\r\n    return (steelContent * steelRecyclability +\r\n            aluminumContent * aluminumRecyclability +\r\n            otherContent * otherRecyclability) * 100;\r\n  }\r\n\r\n  /**\r\n   * Calculate environmental score\r\n   */\r\n  private static calculateEnvironmentalScore(\r\n    energyEfficiency: any,\r\n    carbonIntensity: any,\r\n    resourceEfficiency: any\r\n  ): EnvironmentalScore {\r\n    // Weighted scoring system (0-100)\r\n    const energyScore = this.calculateEnergyScore(energyEfficiency.specificFanPower);\r\n    const carbonScore = this.calculateCarbonScore(carbonIntensity.emissionIntensity);\r\n    const resourceScore = this.calculateResourceScore(resourceEfficiency);\r\n\r\n    const overallScore = (energyScore * 0.4 + carbonScore * 0.4 + resourceScore * 0.2);\r\n\r\n    return {\r\n      overallScore,\r\n      energyScore,\r\n      carbonScore,\r\n      resourceScore,\r\n      rating: this.getScoreRating(overallScore),\r\n      improvementAreas: this.identifyImprovementAreas(energyScore, carbonScore, resourceScore)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate energy score\r\n   */\r\n  private static calculateEnergyScore(sfp: number): number {\r\n    if (sfp <= 0.8) return 100;\r\n    if (sfp <= 1.0) return 85;\r\n    if (sfp <= 1.25) return 70;\r\n    if (sfp <= 1.5) return 50;\r\n    if (sfp <= 2.0) return 30;\r\n    return 10;\r\n  }\r\n\r\n  /**\r\n   * Calculate carbon score\r\n   */\r\n  private static calculateCarbonScore(intensity: number): number {\r\n    if (intensity <= 0.06) return 100;\r\n    if (intensity <= 0.10) return 85;\r\n    if (intensity <= 0.15) return 70;\r\n    if (intensity <= 0.20) return 50;\r\n    if (intensity <= 0.30) return 30;\r\n    return 10;\r\n  }\r\n\r\n  /**\r\n   * Calculate resource score\r\n   */\r\n  private static calculateResourceScore(resourceEfficiency: any): number {\r\n    const materialScore = resourceEfficiency.recyclableContent; // 0-100\r\n\r\n    let durabilityScore: number;\r\n    if (resourceEfficiency.durabilityRating === 'Excellent') {\r\n      durabilityScore = 100;\r\n    } else if (resourceEfficiency.durabilityRating === 'Good') {\r\n      durabilityScore = 80;\r\n    } else if (resourceEfficiency.durabilityRating === 'Average') {\r\n      durabilityScore = 60;\r\n    } else {\r\n      durabilityScore = 40;\r\n    }\r\n\r\n    return (materialScore + durabilityScore) / 2;\r\n  }\r\n\r\n  /**\r\n   * Get score rating\r\n   */\r\n  private static getScoreRating(score: number): string {\r\n    if (score >= 90) return 'Excellent';\r\n    if (score >= 80) return 'Very Good';\r\n    if (score >= 70) return 'Good';\r\n    if (score >= 60) return 'Fair';\r\n    if (score >= 50) return 'Poor';\r\n    return 'Very Poor';\r\n  }\r\n\r\n  /**\r\n   * Identify improvement areas\r\n   */\r\n  private static identifyImprovementAreas(\r\n    energyScore: number,\r\n    carbonScore: number,\r\n    resourceScore: number\r\n  ): string[] {\r\n    const areas: string[] = [];\r\n\r\n    if (energyScore < 70) areas.push('Energy Efficiency');\r\n    if (carbonScore < 70) areas.push('Carbon Emissions');\r\n    if (resourceScore < 70) areas.push('Resource Efficiency');\r\n\r\n    return areas;\r\n  }\r\n\r\n  /**\r\n   * Calculate carbon neutrality progress\r\n   */\r\n  private static calculateCarbonNeutralityProgress(totalEmissions: number): number {\r\n    // Simplified progress calculation based on emissions intensity\r\n    const targetIntensity = 0.02; // kg CO2e/CFM/year for carbon neutrality\r\n    const currentIntensity = totalEmissions / 10000; // Assuming 10,000 CFM system\r\n\r\n    if (currentIntensity <= targetIntensity) return 100;\r\n\r\n    const maxIntensity = 0.30; // Baseline high emissions\r\n    const progress = Math.max(0, (maxIntensity - currentIntensity) / (maxIntensity - targetIntensity) * 100);\r\n\r\n    return Math.min(100, progress);\r\n  }\r\n\r\n  /**\r\n   * Assess certification readiness\r\n   */\r\n  private static assessCertificationReadiness(environmentalScore: EnvironmentalScore): CertificationReadiness {\r\n    const overallScore = environmentalScore.overallScore;\r\n\r\n    let leedReadiness: string;\r\n    if (overallScore >= 70) {\r\n      leedReadiness = 'Ready';\r\n    } else if (overallScore >= 60) {\r\n      leedReadiness = 'Near Ready';\r\n    } else {\r\n      leedReadiness = 'Not Ready';\r\n    }\r\n\r\n    let breeamReadiness: string;\r\n    if (overallScore >= 75) {\r\n      breeamReadiness = 'Ready';\r\n    } else if (overallScore >= 65) {\r\n      breeamReadiness = 'Near Ready';\r\n    } else {\r\n      breeamReadiness = 'Not Ready';\r\n    }\r\n\r\n    let greenBuildingReadiness: string;\r\n    if (overallScore >= 80) {\r\n      greenBuildingReadiness = 'Ready';\r\n    } else if (overallScore >= 70) {\r\n      greenBuildingReadiness = 'Near Ready';\r\n    } else {\r\n      greenBuildingReadiness = 'Not Ready';\r\n    }\r\n\r\n    return {\r\n      leedReadiness,\r\n      breeamReadiness,\r\n      energyStarReadiness: environmentalScore.energyScore >= 80 ? 'Ready' : 'Not Ready',\r\n      greenBuildingReadiness\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Generate unique analysis ID\r\n   */\r\n  private static generateAnalysisId(systemId: string): string {\r\n    const timestamp = Date.now();\r\n    const random = Math.random().toString(36).substring(2, 8);\r\n    return `environmental_analysis_${systemId}_${timestamp}_${random}`;\r\n  }\r\n}\r\n\r\n// Supporting interfaces\r\ninterface OperatingProfile {\r\n  annualOperatingHours: number;\r\n  loadProfile: 'constant' | 'variable' | 'seasonal';\r\n  seasonalVariation: number; // %\r\n  futureGrowth: number; // % per year\r\n}\r\n\r\ninterface LocationData {\r\n  region: string;\r\n  climateZone: string;\r\n  gridMix: {\r\n    renewable: number; // %\r\n    nuclear: number; // %\r\n    naturalGas: number; // %\r\n    coal: number; // %\r\n    other: number; // %\r\n  };\r\n  localRegulations: string[];\r\n}\r\n\r\ninterface EmissionsTrend {\r\n  currentEmissions: number;\r\n  trendDirection: 'improving' | 'stable' | 'worsening';\r\n  projectedEmissions: number;\r\n  reductionPotential: number;\r\n  timeHorizon: number;\r\n}\r\n\r\ninterface OffsetOpportunity {\r\n  type: string;\r\n  potential: number; // kg CO2e/year\r\n  cost: number; // $/year\r\n  implementation: string;\r\n  paybackPeriod: number; // years\r\n  additionalBenefits: string[];\r\n}\r\n\r\ninterface EmissionsBenchmark {\r\n  benchmarkType: string;\r\n  currentIntensity: number; // kg CO2e/CFM/year\r\n  benchmarkIntensity: number;\r\n  percentile: number;\r\n  improvementPotential: number;\r\n  complianceGap: number;\r\n  netZeroGap: number;\r\n}\r\n"], "mappings": ";;AAAA;;;;;;;;;;AAAA;AAAA,SAAAA,eAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;MAAA;QAAAC,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAO,SAAA;MAAA;QAAAD,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAW,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,cAAA;MAAAC,IAAA;MAAAC,QAAA;MAAAC,KAAA;MAAAC,OAAA;MAAAC,cAAA;MAAAC,OAAA;IAAA;IAAAC,eAAA;IAAA5B,IAAA;EAAA;EAAA,IAAA6B,QAAA,GAAA5B,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAA0B,QAAA,CAAA9B,IAAA,KAAA8B,QAAA,CAAA9B,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAA6B,QAAA,CAAA9B,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAA0B,cAAA,GAAAD,QAAA,CAAA9B,IAAA;EAAA;;;;;;;;;;;;;;;;AAWA,MAAAgC,qBAAA;AAAA;AAAA,CAAAjC,cAAA,GAAAoB,CAAA,OAAAc,OAAA;AAiBA;;;;;;;;;;;AAWA,MAAaC,mCAAmC;EAuC9C;;;EAGO,aAAaC,yBAAyBA,CAC3CC,mBAAwC,EACxCC,cAA8B,EAC9BC,gBAAmC,EACnCC,YAA2B;IAAA;IAAAxC,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAE3B,IAAI;MACF,MAAMqB,UAAU;MAAA;MAAA,CAAAzC,cAAA,GAAAoB,CAAA,OAAG,IAAI,CAACsB,kBAAkB,CAACL,mBAAmB,CAACM,EAAE,CAAC;MAClE,MAAMC,SAAS;MAAA;MAAA,CAAA5C,cAAA,GAAAoB,CAAA,OAAG,IAAIyB,IAAI,EAAE;MAE5B;MACA,MAAMC,eAAe;MAAA;MAAA,CAAA9C,cAAA,GAAAoB,CAAA,OAAG,MAAM,IAAI,CAAC2B,wBAAwB,CACzDV,mBAAmB,EACnBC,cAAc,EACdC,gBAAgB,EAChBC,YAAY,CACb;MAED;MACA,MAAMQ,qBAAqB;MAAA;MAAA,CAAAhD,cAAA,GAAAoB,CAAA,OAAG,MAAM,IAAI,CAAC6B,8BAA8B,CACrEZ,mBAAmB,EACnBC,cAAc,EACdQ,eAAe,CAChB;MAED;MACA,MAAMI,uBAAuB;MAAA;MAAA,CAAAlD,cAAA,GAAAoB,CAAA,OAAG,MAAM,IAAI,CAAC+B,6BAA6B,CACtEd,mBAAmB,EACnBC,cAAc,EACdU,qBAAqB,CACtB;MAED;MACA,MAAMI,2BAA2B;MAAA;MAAA,CAAApD,cAAA,GAAAoB,CAAA,OAAG,MAAM,IAAI,CAACiC,mCAAmC,CAChFhB,mBAAmB,EACnBW,qBAAqB,EACrBE,uBAAuB,CACxB;MAED;MACA,MAAMI,mBAAmB;MAAA;MAAA,CAAAtD,cAAA,GAAAoB,CAAA,QAAG,MAAM,IAAI,CAACmC,0BAA0B,CAC/DlB,mBAAmB,EACnBC,cAAc,EACdQ,eAAe,CAChB;MAED;MACA,MAAMU,eAAe;MAAA;MAAA,CAAAxD,cAAA,GAAAoB,CAAA,QAAG,MAAM,IAAI,CAACqC,oCAAoC,CACrEpB,mBAAmB,EACnBS,eAAe,EACfE,qBAAqB,EACrBE,uBAAuB,CACxB;MAED,MAAMQ,QAAQ;MAAA;MAAA,CAAA1D,cAAA,GAAAoB,CAAA,QAAgC;QAC5CuB,EAAE,EAAEF,UAAU;QACdkB,QAAQ,EAAEtB,mBAAmB,CAACM,EAAE;QAChCiB,iBAAiB,EAAEhB,SAAS;QAC5BE,eAAe;QACfE,qBAAqB;QACrBE,uBAAuB;QACvBE,2BAA2B;QAC3BE,mBAAmB;QACnBE;OACD;MAED;MAAA;MAAAxD,cAAA,GAAAoB,CAAA;MACA,IAAI,CAACyC,mBAAmB,CAACC,GAAG,CAACrB,UAAU,EAAEiB,QAAQ,CAAC;MAAC;MAAA1D,cAAA,GAAAoB,CAAA;MAEnD,OAAOsC,QAAQ;IAEjB,CAAC,CAAC,OAAOK,KAAK,EAAE;MAAA;MAAA/D,cAAA,GAAAoB,CAAA;MACd,MAAM,IAAI4C,KAAK,CAAC,2CAA2CD,KAAK,YAAYC,KAAK;MAAA;MAAA,CAAAhE,cAAA,GAAAsB,CAAA,UAAGyC,KAAK,CAACE,OAAO;MAAA;MAAA,CAAAjE,cAAA,GAAAsB,CAAA,UAAG,eAAe,GAAE,CAAC;IACxH;EACF;EAEA;;;EAGQ,aAAayB,wBAAwBA,CAC3CV,mBAAwC,EACxCC,cAA8B,EAC9BC,gBAAmC,EACnCC,YAA2B;IAAA;IAAAxC,cAAA,GAAAqB,CAAA;IAE3B,MAAM6C,aAAa;IAAA;IAAA,CAAAlE,cAAA,GAAAoB,CAAA,QAAGiB,mBAAmB,CAAC8B,gBAAgB,CAACD,aAAa;IACxE,MAAME,uBAAuB;IAAA;IAAA,CAAApE,cAAA,GAAAoB,CAAA,QAAGkB,cAAc,CAAC+B,iBAAiB,CAACC,gBAAgB,CAACC,KAAK;IAEvF;IACA,MAAMC,cAAc;IAAA;IAAA,CAAAxE,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACqD,yBAAyB,CAACjC,YAAY,CAAC;IAEnE;IACA,MAAMkC,oBAAoB;IAAA;IAAA,CAAA1E,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACuD,yBAAyB,CACzDP,uBAAuB,GAAGI,cAAc,EACxCvC,qBAAA,CAAA2C,aAAa,CAACC,OAAO,EACrB5C,qBAAA,CAAA6C,SAAS,CAACC,QAAQ,EAClB9C,qBAAA,CAAA+C,aAAa,CAACC,OAAO,CACtB;IAED;IACA,MAAMC,iBAAiB;IAAA;IAAA,CAAAlF,cAAA,GAAAoB,CAAA,QAAG,MAAM,IAAI,CAAC+D,0BAA0B,CAAC9C,mBAAmB,CAAC;IAEpF;IACA,MAAM+C,oBAAoB;IAAA;IAAA,CAAApF,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACiE,6BAA6B,CAAChD,mBAAmB,CAAC;IAEpF;IACA,MAAMiD,cAAc;IAAA;IAAA,CAAAtF,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACuD,yBAAyB,CACnDD,oBAAoB,CAACH,KAAK,GAAGW,iBAAiB,CAACX,KAAK,GAAGa,oBAAoB,CAACb,KAAK,EACjFtC,qBAAA,CAAA2C,aAAa,CAACC,OAAO,EACrB5C,qBAAA,CAAA6C,SAAS,CAACC,QAAQ,EAClB9C,qBAAA,CAAA+C,aAAa,CAACC,OAAO,CACtB;IAED;IACA,MAAMM,iBAAiB;IAAA;IAAA,CAAAvF,cAAA,GAAAoB,CAAA,QAAG,CACxB;MACEoE,MAAM,EAAE,yBAAyB;MACjCC,SAAS,EAAEf,oBAAoB,CAACH,KAAK;MACrCmB,UAAU,EAAGhB,oBAAoB,CAACH,KAAK,GAAGe,cAAc,CAACf,KAAK,GAAI,GAAG;MACrEoB,KAAK,EAAE1D,qBAAA,CAAA+C,aAAa,CAACC,OAAO;MAC5BT;KACD,EACD;MACEgB,MAAM,EAAE,iBAAiB;MACzBC,SAAS,EAAEP,iBAAiB,CAACX,KAAK;MAClCmB,UAAU,EAAGR,iBAAiB,CAACX,KAAK,GAAGe,cAAc,CAACf,KAAK,GAAI,GAAG;MAClEoB,KAAK,EAAE1D,qBAAA,CAAA+C,aAAa,CAACY,OAAO;MAC5BpB,cAAc,EAAE;KACjB,EACD;MACEgB,MAAM,EAAE,qBAAqB;MAC7BC,SAAS,EAAEL,oBAAoB,CAACb,KAAK;MACrCmB,UAAU,EAAGN,oBAAoB,CAACb,KAAK,GAAGe,cAAc,CAACf,KAAK,GAAI,GAAG;MACrEoB,KAAK,EAAE1D,qBAAA,CAAA+C,aAAa,CAACa,OAAO;MAC5BrB,cAAc,EAAE;KACjB,CACF;IAED;IACA,MAAMsB,cAAc;IAAA;IAAA,CAAA9F,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAAC2E,uBAAuB,CAACT,cAAc,CAACf,KAAK,EAAEhC,gBAAgB,CAAC;IAE3F;IACA,MAAMyD,mBAAmB;IAAA;IAAA,CAAAhG,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAAC6E,2BAA2B,CAC1DX,cAAc,CAACf,KAAK,EACpBG,oBAAoB,CAACH,KAAK,EAC1BlC,mBAAmB,CACpB;IAED;IACA,MAAM6D,mBAAmB;IAAA;IAAA,CAAAlG,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAAC+E,4BAA4B,CAC3Db,cAAc,CAACf,KAAK,EACpBL,aAAa,EACb7B,mBAAmB,CACpB;IAAC;IAAArC,cAAA,GAAAoB,CAAA;IAEF,OAAO;MACLkE,cAAc;MACdZ,oBAAoB;MACpBQ,iBAAiB;MACjBK,iBAAiB;MACjBO,cAAc;MACdE,mBAAmB;MACnBE;KACD;EACH;EAEA;;;EAGQ,OAAOzB,yBAAyBA,CAACjC,YAA2B;IAAA;IAAAxC,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAClE,IAAI,CAACoB,YAAY,EAAE;MAAA;MAAAxC,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACjB,OAAO,IAAI,CAACgF,gBAAgB,CAACC,WAAW,CAACC,eAAe;IAC1D,CAAC;IAAA;IAAA;MAAAtG,cAAA,GAAAsB,CAAA;IAAA;IAED;IACA,MAAMiF,eAAe;IAAA;IAAA,CAAAvG,cAAA,GAAAoB,CAAA,QAA8B;MACjD,OAAO,EAAE,IAAI;MAAE;MACf,OAAO,EAAE,IAAI;MAAE;MACf,OAAO,EAAE,IAAI;MAAE;MACf,OAAO,EAAE,IAAI;MAAE;MACf,OAAO,EAAE,IAAI;MAAE;MACf,OAAO,EAAE,IAAI;MAAE;MACf,SAAS,EAAE,IAAI,CAACgF,gBAAgB,CAACC,WAAW,CAACC;KAC9C;IAAC;IAAAtG,cAAA,GAAAoB,CAAA;IAEF,OAAO,2BAAApB,cAAA,GAAAsB,CAAA,UAAAiF,eAAe,CAAC/D,YAAY,CAACgE,MAAM,CAAC;IAAA;IAAA,CAAAxG,cAAA,GAAAsB,CAAA,UAAIiF,eAAe,CAAC,SAAS,CAAC;EAC3E;EAEA;;;EAGQ,aAAapB,0BAA0BA,CAC7C9C,mBAAwC;IAAA;IAAArC,cAAA,GAAAqB,CAAA;IAExC,MAAM6C,aAAa;IAAA;IAAA,CAAAlE,cAAA,GAAAoB,CAAA,QAAGiB,mBAAmB,CAAC8B,gBAAgB,CAACD,aAAa;IAExE;IACA,MAAMuC,kBAAkB;IAAA;IAAA,CAAAzG,cAAA,GAAAoB,CAAA,QAAG;MACzBsF,aAAa,EAAExC,aAAa,GAAG,IAAI,CAACyC,kBAAkB,CAACC,cAAc;MACrEC,aAAa,EAAE3C,aAAa,GAAG,IAAI,CAACyC,kBAAkB,CAACG,cAAc;MACrEC,UAAU,EAAE7C,aAAa,GAAG,IAAI,CAACyC,kBAAkB,CAACK,UAAU;MAC9DC,YAAY,EAAE/C,aAAa,GAAG,IAAI,CAACyC,kBAAkB,CAACO,aAAa;MACnEC,QAAQ,EAAEjD,aAAa,GAAG,IAAI,CAACyC,kBAAkB,CAACS,SAAS;MAC3DC,WAAW,EAAEnD,aAAa,GAAG,IAAI,CAACyC,kBAAkB,CAACW;KACtD;IAED;IACA,MAAMpC,iBAAiB;IAAA;IAAA,CAAAlF,cAAA,GAAAoB,CAAA,QACpBqF,kBAAkB,CAACC,aAAa,GAAG,IAAI,CAACN,gBAAgB,CAACmB,SAAS,CAACC,gBAAgB,GACnFf,kBAAkB,CAACI,aAAa,GAAG,IAAI,CAACT,gBAAgB,CAACmB,SAAS,CAACE,KAAM,GACzEhB,kBAAkB,CAACM,UAAU,GAAG,IAAI,CAACX,gBAAgB,CAACmB,SAAS,CAACP,UAAW,GAC3EP,kBAAkB,CAACQ,YAAY,GAAG,IAAI,CAACb,gBAAgB,CAACmB,SAAS,CAACE,KAAM,GACxEhB,kBAAkB,CAACU,QAAQ,GAAG,IAAI,CAACf,gBAAgB,CAACmB,SAAS,CAACE,KAAM,GACpEhB,kBAAkB,CAACY,WAAW,GAAG,IAAI,CAACjB,gBAAgB,CAACmB,SAAS,CAACG,QAAS;IAE7E;IACA,MAAMC,uBAAuB;IAAA;IAAA,CAAA3H,cAAA,GAAAoB,CAAA,QAAG8D,iBAAiB,GAAG,EAAE;IAAC;IAAAlF,cAAA,GAAAoB,CAAA;IAEvD,OAAO,IAAI,CAACuD,yBAAyB,CACnCgD,uBAAuB,EACvB1F,qBAAA,CAAA2C,aAAa,CAACC,OAAO,EACrB5C,qBAAA,CAAA6C,SAAS,CAACC,QAAQ,EAClB9C,qBAAA,CAAA+C,aAAa,CAACY,OAAO,CACtB;EACH;EAEA;;;EAGQ,OAAOP,6BAA6BA,CAC1ChD,mBAAwC;IAAA;IAAArC,cAAA,GAAAqB,CAAA;IAExC;IACA;IACA,MAAMuG,eAAe;IAAA;IAAA,CAAA5H,cAAA,GAAAoB,CAAA;IAAG;IAAA,CAAApB,cAAA,GAAAsB,CAAA,UAAAe,mBAAmB,CAACwF,UAAU,CAACC,QAAQ,CAAC,SAAS,CAAC;IAAA;IAAA,CAAA9H,cAAA,GAAAsB,CAAA,UACnDe,mBAAmB,CAACwF,UAAU,CAACC,QAAQ,CAAC,WAAW,CAAC;IAAC;IAAA9H,cAAA,GAAAoB,CAAA;IAE5E,IAAI,CAACwG,eAAe,EAAE;MAAA;MAAA5H,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACpB,OAAO,IAAI,CAACuD,yBAAyB,CAAC,CAAC,EAAE1C,qBAAA,CAAA2C,aAAa,CAACC,OAAO,EAAE5C,qBAAA,CAAA6C,SAAS,CAACC,QAAQ,EAAE9C,qBAAA,CAAA+C,aAAa,CAACa,OAAO,CAAC;IAC5G,CAAC;IAAA;IAAA;MAAA7F,cAAA,GAAAsB,CAAA;IAAA;IAED,MAAM4C,aAAa;IAAA;IAAA,CAAAlE,cAAA,GAAAoB,CAAA,QAAGiB,mBAAmB,CAAC8B,gBAAgB,CAACD,aAAa;IACxE,MAAM6D,iBAAiB;IAAA;IAAA,CAAA/H,cAAA,GAAAoB,CAAA,QAAG8C,aAAa,GAAG,KAAK,EAAC,CAAC;IACjD,MAAM8D,WAAW;IAAA;IAAA,CAAAhI,cAAA,GAAAoB,CAAA,QAAG,IAAI,EAAC,CAAC;IAC1B,MAAM6G,GAAG;IAAA;IAAA,CAAAjI,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACgF,gBAAgB,CAAC8B,YAAY,CAACC,KAAK,EAAC,CAAC;IAEtD,MAAMC,aAAa;IAAA;IAAA,CAAApI,cAAA,GAAAoB,CAAA,QAAG2G,iBAAiB,GAAGC,WAAW;IACrD,MAAMK,eAAe;IAAA;IAAA,CAAArI,cAAA,GAAAoB,CAAA,QAAGgH,aAAa,GAAGH,GAAG;IAAC;IAAAjI,cAAA,GAAAoB,CAAA;IAE5C,OAAO,IAAI,CAACuD,yBAAyB,CACnC0D,eAAe,EACfpG,qBAAA,CAAA2C,aAAa,CAACC,OAAO,EACrB5C,qBAAA,CAAA6C,SAAS,CAACC,QAAQ,EAClB9C,qBAAA,CAAA+C,aAAa,CAACa,OAAO,CACtB;EACH;EAEA;;;EAGQ,OAAOlB,yBAAyBA,CACtCJ,KAAa,EACb+D,KAAoB,EACpBC,SAAoB,EACpB5C,KAAoB,EACpB6C,QAAA;EAAA;EAAA,CAAAxI,cAAA,GAAAsB,CAAA,UAAmB,GAAG;IAAA;IAAAtB,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAEtB,OAAO;MACLmD,KAAK;MACL+D,KAAK;MACLC,SAAS;MACT5C,KAAK;MACL6C;KACD;EACH;EAEA;;;EAGQ,OAAOzC,uBAAuBA,CACpC0C,gBAAwB,EACxBlG,gBAAmC;IAAA;IAAAvC,cAAA,GAAAqB,CAAA;IAEnC;IACA,MAAMqH,kBAAkB;IAAA;IAAA,CAAA1I,cAAA,GAAAoB,CAAA,QAAGqH,gBAAgB,GAAG,IAAI,EAAC,CAAC;IACpD,MAAME,kBAAkB;IAAA;IAAA,CAAA3I,cAAA,GAAAoB,CAAA,QAAGqH,gBAAgB,GAAG,GAAG,EAAC,CAAC;IAAA;IAAAzI,cAAA,GAAAoB,CAAA;IAEnD,OAAO;MACLqH,gBAAgB;MAChBG,cAAc,EAAE,WAAoB;MACpCF,kBAAkB;MAClBC,kBAAkB;MAClBE,WAAW,EAAE,EAAE,CAAC;KACjB;EACH;EAEA;;;EAGQ,OAAO5C,2BAA2BA,CACxCX,cAAsB,EACtBZ,oBAA4B,EAC5BrC,mBAAwC;IAAA;IAAArC,cAAA,GAAAqB,CAAA;IAExC,MAAMyH,aAAa;IAAA;IAAA,CAAA9I,cAAA,GAAAoB,CAAA,QAAwB,EAAE;IAE7C;IAAA;IAAApB,cAAA,GAAAoB,CAAA;IACA0H,aAAa,CAACC,IAAI,CAAC;MACjB9H,IAAI,EAAE,kBAAkB;MACxB+H,SAAS,EAAEtE,oBAAoB,GAAG,GAAG;MAAE;MACvCuE,IAAI,EAAEvE,oBAAoB,GAAG,IAAI;MAAE;MACnCwE,cAAc,EAAE,wDAAwD;MACxEC,aAAa,EAAE,CAAC;MAAE;MAClBC,kBAAkB,EAAE,CAAC,qBAAqB,EAAE,mBAAmB,EAAE,iBAAiB;KACnF,CAAC;IAEF;IAAA;IAAApJ,cAAA,GAAAoB,CAAA;IACA0H,aAAa,CAACC,IAAI,CAAC;MACjB9H,IAAI,EAAE,mBAAmB;MACzB+H,SAAS,EAAEtE,oBAAoB,GAAG,IAAI;MAAE;MACxCuE,IAAI,EAAE,CAAC,IAAI;MAAE;MACbC,cAAc,EAAE,iDAAiD;MACjEC,aAAa,EAAE,CAAC;MAAE;MAClBC,kBAAkB,EAAE,CAAC,yBAAyB,EAAE,sBAAsB,EAAE,yBAAyB;KAClG,CAAC;IAEF;IAAA;IAAApJ,cAAA,GAAAoB,CAAA;IACA0H,aAAa,CAACC,IAAI,CAAC;MACjB9H,IAAI,EAAE,sBAAsB;MAC5B+H,SAAS,EAAE1D,cAAc,GAAG,GAAG;MAAE;MACjC2D,IAAI,EAAE3D,cAAc,GAAG,KAAK;MAAE;MAC9B4D,cAAc,EAAE,yCAAyC;MACzDC,aAAa,EAAEE,QAAQ;MAAE;MACzBD,kBAAkB,EAAE,CAAC,sBAAsB,EAAE,sBAAsB,EAAE,mBAAmB;KACzF,CAAC;IAAC;IAAApJ,cAAA,GAAAoB,CAAA;IAEH,OAAO0H,aAAa;EACtB;EAEA;;;EAGQ,OAAO3C,4BAA4BA,CACzCb,cAAsB,EACtBpB,aAAqB,EACrB7B,mBAAwC;IAAA;IAAArC,cAAA,GAAAqB,CAAA;IAExC,MAAMiI,iBAAiB;IAAA;IAAA,CAAAtJ,cAAA,GAAAoB,CAAA,QAAGkE,cAAc,GAAGpB,aAAa,EAAC,CAAC;IAE1D;IACA,MAAMqF,UAAU;IAAA;IAAA,CAAAvJ,cAAA,GAAAoB,CAAA,QAAG;MACjBoI,eAAe,EAAE,IAAI;MACrBC,YAAY,EAAE,IAAI;MAClBC,gBAAgB,EAAE,IAAI;MACtBC,aAAa,EAAE;KAChB;IAED,MAAMC,UAAU;IAAA;IAAA,CAAA5J,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACyI,4BAA4B,CAACP,iBAAiB,EAAEC,UAAU,CAACC,eAAe,CAAC;IAAC;IAAAxJ,cAAA,GAAAoB,CAAA;IAEpG,OAAO;MACL0I,aAAa,EAAE,kBAAkB;MACjCC,gBAAgB,EAAET,iBAAiB;MACnCU,kBAAkB,EAAET,UAAU,CAACC,eAAe;MAC9CI,UAAU;MACVK,oBAAoB,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEb,iBAAiB,GAAGC,UAAU,CAACE,YAAY,CAAC;MAC9EW,aAAa,EAAEF,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEb,iBAAiB,GAAGC,UAAU,CAACG,gBAAgB,CAAC;MAC3EW,UAAU,EAAEH,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEb,iBAAiB,GAAGC,UAAU,CAACI,aAAa;KACrE;EACH;EAEA;;;EAGQ,OAAOE,4BAA4BA,CAACS,SAAiB,EAAEC,OAAe;IAAA;IAAAvK,cAAA,GAAAqB,CAAA;IAC5E;IACA,MAAMmJ,KAAK;IAAA;IAAA,CAAAxK,cAAA,GAAAoB,CAAA,QAAGkJ,SAAS,GAAGC,OAAO;IAAC;IAAAvK,cAAA,GAAAoB,CAAA;IAElC,IAAIoJ,KAAK,IAAI,GAAG,EAAE;MAAA;MAAAxK,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,OAAO,EAAE;IAAA,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA,EAAC;IAAAtB,cAAA,GAAAoB,CAAA;IAC7B,IAAIoJ,KAAK,IAAI,GAAG,EAAE;MAAA;MAAAxK,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,OAAO,EAAE;IAAA,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA,EAAC;IAAAtB,cAAA,GAAAoB,CAAA;IAC7B,IAAIoJ,KAAK,IAAI,GAAG,EAAE;MAAA;MAAAxK,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,OAAO,EAAE;IAAA,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA,EAAC;IAAAtB,cAAA,GAAAoB,CAAA;IAC7B,IAAIoJ,KAAK,IAAI,GAAG,EAAE;MAAA;MAAAxK,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,OAAO,EAAE;IAAA,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA,EAAC;IAAAtB,cAAA,GAAAoB,CAAA;IAC7B,IAAIoJ,KAAK,IAAI,GAAG,EAAE;MAAA;MAAAxK,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,OAAO,EAAE;IAAA,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA,EAAC;IAAAtB,cAAA,GAAAoB,CAAA;IAC7B,OAAO,CAAC,CAAC,CAAC;EACZ;EAEA;;;EAGQ,aAAa6B,8BAA8BA,CACjDZ,mBAAwC,EACxCC,cAA8B,EAC9BQ,eAAgC;IAAA;IAAA9C,cAAA,GAAAqB,CAAA;IAEhC,MAAM6C,aAAa;IAAA;IAAA,CAAAlE,cAAA,GAAAoB,CAAA,QAAGiB,mBAAmB,CAAC8B,gBAAgB,CAACD,aAAa;IACxE,MAAME,uBAAuB;IAAA;IAAA,CAAApE,cAAA,GAAAoB,CAAA,QAAGkB,cAAc,CAAC+B,iBAAiB,CAACC,gBAAgB,CAACC,KAAK;IAEvF;IACA,MAAMkG,gBAAgB;IAAA;IAAA,CAAAzK,cAAA,GAAAoB,CAAA,QAAG;MACvBsJ,gBAAgB,EAAEpI,cAAc,CAACqI,iBAAiB,CAACD,gBAAgB;MAAE;MACrEE,sBAAsB,EAAExG,uBAAuB,IAAIF,aAAa,GAAG,IAAI,CAAC;MAAE;MAC1E2G,gBAAgB,EAAEvI,cAAc,CAACqI,iBAAiB,CAACE,gBAAgB;MAAE;MACrE3E,mBAAmB,EAAE,IAAI,CAAC4E,4BAA4B,CAACxI,cAAc,CAACqI,iBAAiB,CAACD,gBAAgB;KACzG;IAED;IACA,MAAMK,eAAe;IAAA;IAAA,CAAA/K,cAAA,GAAAoB,CAAA,QAAG;MACtBkI,iBAAiB,EAAExG,eAAe,CAACwC,cAAc,CAACf,KAAK,GAAGL,aAAa;MAAE;MACzE8G,oBAAoB,EAAElI,eAAe,CAAC4B,oBAAoB,CAACH,KAAK,GAAGH,uBAAuB;MAAE;MAC5F6G,iBAAiB,EAAEnI,eAAe,CAACoC,iBAAiB,CAACX,KAAK,GAAGL,aAAa;MAAE;MAC5EgC,mBAAmB,EAAE,IAAI,CAACgF,wBAAwB,CAACpI,eAAe,CAACwC,cAAc,CAACf,KAAK,GAAGL,aAAa;KACxG;IAED;IACA,MAAMiH,kBAAkB;IAAA;IAAA,CAAAnL,cAAA,GAAAoB,CAAA,QAAG;MACzBgK,kBAAkB,EAAE,IAAI,CAACC,2BAA2B,CAAChJ,mBAAmB,CAAC;MACzEiJ,UAAU,EAAE,IAAI,CAACC,mBAAmB,CAAClJ,mBAAmB,CAAC;MAAE;MAC3DmJ,eAAe,EAAE,IAAI,CAACC,wBAAwB,CAACpJ,mBAAmB,CAAC;MAAE;MACrEqJ,kBAAkB,EAAE,IAAI,CAACC,2BAA2B,CAACtJ,mBAAmB,CAAC,CAAC;KAC3E;IAED;IACA,MAAMuJ,kBAAkB;IAAA;IAAA,CAAA5L,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACyK,2BAA2B,CACzDpB,gBAAgB,EAChBM,eAAe,EACfI,kBAAkB,CACnB;IAED;IACA,MAAMW,qBAAqB;IAAA;IAAA,CAAA9L,cAAA,GAAAoB,CAAA,QAAG;MAC5B2K,sBAAsB,EAAE,IAAI;MAC5BC,eAAe,EAAE,IAAI,CAACC,iCAAiC,CAACnJ,eAAe,CAACwC,cAAc,CAACf,KAAK,CAAC;MAC7F2H,sBAAsB,EAAE,GAAG;MAAE;MAC7BC,yBAAyB,EAAEjC,IAAI,CAACkC,GAAG,CAAC,GAAG,EAAG,GAAG,GAAG3B,gBAAgB,CAACC,gBAAgB,GAAI,GAAG,CAAC;MACzF2B,qBAAqB,EAAE,GAAG;MAAE;MAC5BC,wBAAwB,EAAE,EAAE,CAAC;KAC9B;IAAC;IAAAtM,cAAA,GAAAoB,CAAA;IAEF,OAAO;MACLqJ,gBAAgB;MAChBM,eAAe;MACfI,kBAAkB;MAClBS,kBAAkB;MAClBE,qBAAqB;MACrBS,sBAAsB,EAAE,IAAI,CAACC,4BAA4B,CAACZ,kBAAkB;KAC7E;EACH;EAEA;;;EAGQ,OAAOd,4BAA4BA,CAAC2B,GAAW;IAAA;IAAAzM,cAAA,GAAAqB,CAAA;IACrD,IAAIqL,MAAc;IAAC;IAAA1M,cAAA,GAAAoB,CAAA;IACnB,IAAIqL,GAAG,IAAI,GAAG,EAAE;MAAA;MAAAzM,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACdsL,MAAM,GAAG,WAAW;IACtB,CAAC,MAAM;MAAA;MAAA1M,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,IAAIqL,GAAG,IAAI,GAAG,EAAE;QAAA;QAAAzM,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACrBsL,MAAM,GAAG,MAAM;MACjB,CAAC,MAAM;QAAA;QAAA1M,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAAA,IAAIqL,GAAG,IAAI,IAAI,EAAE;UAAA;UAAAzM,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAoB,CAAA;UACtBsL,MAAM,GAAG,SAAS;QACpB,CAAC,MAAM;UAAA;UAAA1M,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAoB,CAAA;UACLsL,MAAM,GAAG,MAAM;QACjB;MAAA;IAAA;IAAC;IAAA1M,cAAA,GAAAoB,CAAA;IAED,OAAO;MACLsL,MAAM;MACN9C,UAAU,EAAE,IAAI,CAAC+C,sBAAsB,CAACF,GAAG,CAAC;MAC5CxC,oBAAoB,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEsC,GAAG,GAAG,GAAG,CAAC;MAAE;MAC9CjD,eAAe,EAAE;KAClB;EACH;EAEA;;;EAGQ,OAAOmD,sBAAsBA,CAACF,GAAW;IAAA;IAAAzM,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAC/C,IAAIqL,GAAG,IAAI,GAAG,EAAE;MAAA;MAAAzM,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,OAAO,EAAE;IAAA,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAC1B,IAAIqL,GAAG,IAAI,GAAG,EAAE;MAAA;MAAAzM,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,OAAO,EAAE;IAAA,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAC1B,IAAIqL,GAAG,IAAI,IAAI,EAAE;MAAA;MAAAzM,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,OAAO,EAAE;IAAA,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAC3B,IAAIqL,GAAG,IAAI,GAAG,EAAE;MAAA;MAAAzM,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,OAAO,EAAE;IAAA,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAC1B,OAAO,CAAC;EACV;EAEA;;;EAGQ,OAAO8J,wBAAwBA,CAACZ,SAAiB;IAAA;IAAAtK,cAAA,GAAAqB,CAAA;IACvD,IAAIqL,MAAc;IAAC;IAAA1M,cAAA,GAAAoB,CAAA;IACnB,IAAIkJ,SAAS,IAAI,IAAI,EAAE;MAAA;MAAAtK,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACrBsL,MAAM,GAAG,WAAW;IACtB,CAAC,MAAM;MAAA;MAAA1M,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,IAAIkJ,SAAS,IAAI,IAAI,EAAE;QAAA;QAAAtK,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAC5BsL,MAAM,GAAG,MAAM;MACjB,CAAC,MAAM;QAAA;QAAA1M,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAAA,IAAIkJ,SAAS,IAAI,IAAI,EAAE;UAAA;UAAAtK,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAoB,CAAA;UAC5BsL,MAAM,GAAG,SAAS;QACpB,CAAC,MAAM;UAAA;UAAA1M,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAoB,CAAA;UACLsL,MAAM,GAAG,MAAM;QACjB;MAAA;IAAA;IAAC;IAAA1M,cAAA,GAAAoB,CAAA;IAED,OAAO;MACLsL,MAAM;MACN9C,UAAU,EAAE,IAAI,CAACC,4BAA4B,CAACS,SAAS,EAAE,IAAI,CAAC;MAC9DL,oBAAoB,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEG,SAAS,GAAG,IAAI,CAAC;MACnDd,eAAe,EAAE;KAClB;EACH;EAEA;;;EAGQ,OAAO6B,2BAA2BA,CAAChJ,mBAAwC;IAAA;IAAArC,cAAA,GAAAqB,CAAA;IACjF;IACA,MAAMuL,sBAAsB;IAAA;IAAA,CAAA5M,cAAA,GAAAoB,CAAA,SAAGyL,MAAM,CAACC,MAAM,CAAC,IAAI,CAACnG,kBAAkB,CAAC,CAACoG,MAAM,CAAC,CAACC,GAAG,EAAE1C,SAAS,KAAK;MAAA;MAAAtK,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAAA,OAAA4L,GAAG,GAAG1C,SAAS;IAAT,CAAS,EAAE,CAAC,CAAC;IAAC;IAAAtK,cAAA,GAAAoB,CAAA;IAErH,OAAO;MACL6L,iBAAiB,EAAEL,sBAAsB;MAAE;MAC3CM,iBAAiB,EAAE,EAAE;MAAE;MACvBC,gBAAgB,EAAE,IAAI,CAACC,yBAAyB,CAAC/K,mBAAmB,CAAC;MACrEgL,sBAAsB,EAAE,KAAK,CAAC;KAC/B;EACH;EAEA;;;EAGQ,OAAOD,yBAAyBA,CAAC/K,mBAAwC;IAAA;IAAArC,cAAA,GAAAqB,CAAA;IAC/E,MAAMiM,cAAc;IAAA;IAAA,CAAAtN,cAAA,GAAAoB,CAAA,SAAGiB,mBAAmB,CAAC8B,gBAAgB,CAACmJ,cAAc;IAAC;IAAAtN,cAAA,GAAAoB,CAAA;IAE3E,IAAIkM,cAAc,IAAI,GAAG,EAAE;MAAA;MAAAtN,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,OAAO,WAAW;IAAA,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAC9C,IAAIkM,cAAc,IAAI,GAAG,EAAE;MAAA;MAAAtN,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,OAAO,MAAM;IAAA,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IACzC,IAAIkM,cAAc,IAAI,GAAG,EAAE;MAAA;MAAAtN,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,OAAO,SAAS;IAAA,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAC5C,OAAO,MAAM;EACf;EAEA;;;EAGQ,OAAOmK,mBAAmBA,CAAClJ,mBAAwC;IAAA;IAAArC,cAAA,GAAAqB,CAAA;IACzE;IACA,MAAMkM,iBAAiB;IAAA;IAAA,CAAAvN,cAAA,GAAAoB,CAAA,SAAGiB,mBAAmB,CAACwF,UAAU,CAACC,QAAQ,CAAC,gBAAgB,CAAC;IACnF,MAAM5D,aAAa;IAAA;IAAA,CAAAlE,cAAA,GAAAoB,CAAA,SAAGiB,mBAAmB,CAAC8B,gBAAgB,CAACD,aAAa;IAAC;IAAAlE,cAAA,GAAAoB,CAAA;IAEzE,IAAImM,iBAAiB,EAAE;MAAA;MAAAvN,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACrB,OAAO8C,aAAa,GAAG,GAAG,CAAC,CAAC;IAC9B,CAAC;IAAA;IAAA;MAAAlE,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAED,OAAO8C,aAAa,GAAG,IAAI,CAAC,CAAC;EAC/B;EAEA;;;EAGQ,OAAOuH,wBAAwBA,CAACpJ,mBAAwC;IAAA;IAAArC,cAAA,GAAAqB,CAAA;IAC9E,MAAM6C,aAAa;IAAA;IAAA,CAAAlE,cAAA,GAAAoB,CAAA,SAAGiB,mBAAmB,CAAC8B,gBAAgB,CAACD,aAAa;IAExE;IACA,MAAMsJ,WAAW;IAAA;IAAA,CAAAxN,cAAA,GAAAoB,CAAA,SAAG8C,aAAa,GAAG,GAAG,EAAC,CAAC;IACzC,MAAMuJ,gBAAgB;IAAA;IAAA,CAAAzN,cAAA,GAAAoB,CAAA,SAAG8C,aAAa,GAAG,IAAI,EAAC,CAAC;IAAA;IAAAlE,cAAA,GAAAoB,CAAA;IAE/C,OAAOoM,WAAW,GAAGC,gBAAgB;EACvC;EAEA;;;EAGQ,OAAO9B,2BAA2BA,CAACtJ,mBAAwC;IAAA;IAAArC,cAAA,GAAAqB,CAAA;IACjF;IACA,MAAMqM,YAAY;IAAA;IAAA,CAAA1N,cAAA,GAAAoB,CAAA,SAAG,IAAI,EAAC,CAAC;IAC3B,MAAMuM,eAAe;IAAA;IAAA,CAAA3N,cAAA,GAAAoB,CAAA,SAAG,IAAI,EAAC,CAAC;IAC9B,MAAMwM,YAAY;IAAA;IAAA,CAAA5N,cAAA,GAAAoB,CAAA,SAAG,IAAI,EAAC,CAAC;IAE3B,MAAMyM,kBAAkB;IAAA;IAAA,CAAA7N,cAAA,GAAAoB,CAAA,SAAG,IAAI,EAAC,CAAC;IACjC,MAAM0M,qBAAqB;IAAA;IAAA,CAAA9N,cAAA,GAAAoB,CAAA,SAAG,IAAI,EAAC,CAAC;IACpC,MAAM2M,kBAAkB;IAAA;IAAA,CAAA/N,cAAA,GAAAoB,CAAA,SAAG,IAAI,EAAC,CAAC;IAAA;IAAApB,cAAA,GAAAoB,CAAA;IAEjC,OAAO,CAACsM,YAAY,GAAGG,kBAAkB,GACjCF,eAAe,GAAGG,qBAAqB,GACvCF,YAAY,GAAGG,kBAAkB,IAAI,GAAG;EAClD;EAEA;;;EAGQ,OAAOlC,2BAA2BA,CACxCpB,gBAAqB,EACrBM,eAAoB,EACpBI,kBAAuB;IAAA;IAAAnL,cAAA,GAAAqB,CAAA;IAEvB;IACA,MAAM2M,WAAW;IAAA;IAAA,CAAAhO,cAAA,GAAAoB,CAAA,SAAG,IAAI,CAAC6M,oBAAoB,CAACxD,gBAAgB,CAACC,gBAAgB,CAAC;IAChF,MAAMwD,WAAW;IAAA;IAAA,CAAAlO,cAAA,GAAAoB,CAAA,SAAG,IAAI,CAAC+M,oBAAoB,CAACpD,eAAe,CAACzB,iBAAiB,CAAC;IAChF,MAAM8E,aAAa;IAAA;IAAA,CAAApO,cAAA,GAAAoB,CAAA,SAAG,IAAI,CAACiN,sBAAsB,CAAClD,kBAAkB,CAAC;IAErE,MAAMmD,YAAY;IAAA;IAAA,CAAAtO,cAAA,GAAAoB,CAAA,SAAI4M,WAAW,GAAG,GAAG,GAAGE,WAAW,GAAG,GAAG,GAAGE,aAAa,GAAG,GAAG,CAAC;IAAC;IAAApO,cAAA,GAAAoB,CAAA;IAEnF,OAAO;MACLkN,YAAY;MACZN,WAAW;MACXE,WAAW;MACXE,aAAa;MACb1B,MAAM,EAAE,IAAI,CAAC6B,cAAc,CAACD,YAAY,CAAC;MACzCE,gBAAgB,EAAE,IAAI,CAACC,wBAAwB,CAACT,WAAW,EAAEE,WAAW,EAAEE,aAAa;KACxF;EACH;EAEA;;;EAGQ,OAAOH,oBAAoBA,CAACxB,GAAW;IAAA;IAAAzM,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAC7C,IAAIqL,GAAG,IAAI,GAAG,EAAE;MAAA;MAAAzM,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,OAAO,GAAG;IAAA,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAC3B,IAAIqL,GAAG,IAAI,GAAG,EAAE;MAAA;MAAAzM,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,OAAO,EAAE;IAAA,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAC1B,IAAIqL,GAAG,IAAI,IAAI,EAAE;MAAA;MAAAzM,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,OAAO,EAAE;IAAA,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAC3B,IAAIqL,GAAG,IAAI,GAAG,EAAE;MAAA;MAAAzM,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,OAAO,EAAE;IAAA,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAC1B,IAAIqL,GAAG,IAAI,GAAG,EAAE;MAAA;MAAAzM,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,OAAO,EAAE;IAAA,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAC1B,OAAO,EAAE;EACX;EAEA;;;EAGQ,OAAO+M,oBAAoBA,CAAC7D,SAAiB;IAAA;IAAAtK,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACnD,IAAIkJ,SAAS,IAAI,IAAI,EAAE;MAAA;MAAAtK,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,OAAO,GAAG;IAAA,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAClC,IAAIkJ,SAAS,IAAI,IAAI,EAAE;MAAA;MAAAtK,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,OAAO,EAAE;IAAA,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IACjC,IAAIkJ,SAAS,IAAI,IAAI,EAAE;MAAA;MAAAtK,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,OAAO,EAAE;IAAA,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IACjC,IAAIkJ,SAAS,IAAI,IAAI,EAAE;MAAA;MAAAtK,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,OAAO,EAAE;IAAA,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IACjC,IAAIkJ,SAAS,IAAI,IAAI,EAAE;MAAA;MAAAtK,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,OAAO,EAAE;IAAA,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IACjC,OAAO,EAAE;EACX;EAEA;;;EAGQ,OAAOiN,sBAAsBA,CAAClD,kBAAuB;IAAA;IAAAnL,cAAA,GAAAqB,CAAA;IAC3D,MAAMqN,aAAa;IAAA;IAAA,CAAA1O,cAAA,GAAAoB,CAAA,SAAG+J,kBAAkB,CAAC+B,iBAAiB,EAAC,CAAC;IAE5D,IAAIyB,eAAuB;IAAC;IAAA3O,cAAA,GAAAoB,CAAA;IAC5B,IAAI+J,kBAAkB,CAACgC,gBAAgB,KAAK,WAAW,EAAE;MAAA;MAAAnN,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACvDuN,eAAe,GAAG,GAAG;IACvB,CAAC,MAAM;MAAA;MAAA3O,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,IAAI+J,kBAAkB,CAACgC,gBAAgB,KAAK,MAAM,EAAE;QAAA;QAAAnN,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACzDuN,eAAe,GAAG,EAAE;MACtB,CAAC,MAAM;QAAA;QAAA3O,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAAA,IAAI+J,kBAAkB,CAACgC,gBAAgB,KAAK,SAAS,EAAE;UAAA;UAAAnN,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAoB,CAAA;UAC5DuN,eAAe,GAAG,EAAE;QACtB,CAAC,MAAM;UAAA;UAAA3O,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAoB,CAAA;UACLuN,eAAe,GAAG,EAAE;QACtB;MAAA;IAAA;IAAC;IAAA3O,cAAA,GAAAoB,CAAA;IAED,OAAO,CAACsN,aAAa,GAAGC,eAAe,IAAI,CAAC;EAC9C;EAEA;;;EAGQ,OAAOJ,cAAcA,CAACK,KAAa;IAAA;IAAA5O,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACzC,IAAIwN,KAAK,IAAI,EAAE,EAAE;MAAA;MAAA5O,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,OAAO,WAAW;IAAA,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IACpC,IAAIwN,KAAK,IAAI,EAAE,EAAE;MAAA;MAAA5O,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,OAAO,WAAW;IAAA,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IACpC,IAAIwN,KAAK,IAAI,EAAE,EAAE;MAAA;MAAA5O,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,OAAO,MAAM;IAAA,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAC/B,IAAIwN,KAAK,IAAI,EAAE,EAAE;MAAA;MAAA5O,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,OAAO,MAAM;IAAA,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAC/B,IAAIwN,KAAK,IAAI,EAAE,EAAE;MAAA;MAAA5O,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,OAAO,MAAM;IAAA,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAC/B,OAAO,WAAW;EACpB;EAEA;;;EAGQ,OAAOqN,wBAAwBA,CACrCT,WAAmB,EACnBE,WAAmB,EACnBE,aAAqB;IAAA;IAAApO,cAAA,GAAAqB,CAAA;IAErB,MAAMwN,KAAK;IAAA;IAAA,CAAA7O,cAAA,GAAAoB,CAAA,SAAa,EAAE;IAAC;IAAApB,cAAA,GAAAoB,CAAA;IAE3B,IAAI4M,WAAW,GAAG,EAAE,EAAE;MAAA;MAAAhO,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAAyN,KAAK,CAAC9F,IAAI,CAAC,mBAAmB,CAAC;IAAA,CAAC;IAAA;IAAA;MAAA/I,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IACtD,IAAI8M,WAAW,GAAG,EAAE,EAAE;MAAA;MAAAlO,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAAyN,KAAK,CAAC9F,IAAI,CAAC,kBAAkB,CAAC;IAAA,CAAC;IAAA;IAAA;MAAA/I,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IACrD,IAAIgN,aAAa,GAAG,EAAE,EAAE;MAAA;MAAApO,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAAyN,KAAK,CAAC9F,IAAI,CAAC,qBAAqB,CAAC;IAAA,CAAC;IAAA;IAAA;MAAA/I,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAE1D,OAAOyN,KAAK;EACd;EAEA;;;EAGQ,OAAO5C,iCAAiCA,CAAC3G,cAAsB;IAAA;IAAAtF,cAAA,GAAAqB,CAAA;IACrE;IACA,MAAMyN,eAAe;IAAA;IAAA,CAAA9O,cAAA,GAAAoB,CAAA,SAAG,IAAI,EAAC,CAAC;IAC9B,MAAM2I,gBAAgB;IAAA;IAAA,CAAA/J,cAAA,GAAAoB,CAAA,SAAGkE,cAAc,GAAG,KAAK,EAAC,CAAC;IAAA;IAAAtF,cAAA,GAAAoB,CAAA;IAEjD,IAAI2I,gBAAgB,IAAI+E,eAAe,EAAE;MAAA;MAAA9O,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,OAAO,GAAG;IAAA,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAEpD,MAAMyN,YAAY;IAAA;IAAA,CAAA/O,cAAA,GAAAoB,CAAA,SAAG,IAAI,EAAC,CAAC;IAC3B,MAAM4N,QAAQ;IAAA;IAAA,CAAAhP,cAAA,GAAAoB,CAAA,SAAG8I,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAAC4E,YAAY,GAAGhF,gBAAgB,KAAKgF,YAAY,GAAGD,eAAe,CAAC,GAAG,GAAG,CAAC;IAAC;IAAA9O,cAAA,GAAAoB,CAAA;IAEzG,OAAO8I,IAAI,CAACkC,GAAG,CAAC,GAAG,EAAE4C,QAAQ,CAAC;EAChC;EAEA;;;EAGQ,OAAOxC,4BAA4BA,CAACZ,kBAAsC;IAAA;IAAA5L,cAAA,GAAAqB,CAAA;IAChF,MAAMiN,YAAY;IAAA;IAAA,CAAAtO,cAAA,GAAAoB,CAAA,SAAGwK,kBAAkB,CAAC0C,YAAY;IAEpD,IAAIW,aAAqB;IAAC;IAAAjP,cAAA,GAAAoB,CAAA;IAC1B,IAAIkN,YAAY,IAAI,EAAE,EAAE;MAAA;MAAAtO,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACtB6N,aAAa,GAAG,OAAO;IACzB,CAAC,MAAM;MAAA;MAAAjP,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,IAAIkN,YAAY,IAAI,EAAE,EAAE;QAAA;QAAAtO,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAC7B6N,aAAa,GAAG,YAAY;MAC9B,CAAC,MAAM;QAAA;QAAAjP,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACL6N,aAAa,GAAG,WAAW;MAC7B;IAAA;IAEA,IAAIC,eAAuB;IAAC;IAAAlP,cAAA,GAAAoB,CAAA;IAC5B,IAAIkN,YAAY,IAAI,EAAE,EAAE;MAAA;MAAAtO,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACtB8N,eAAe,GAAG,OAAO;IAC3B,CAAC,MAAM;MAAA;MAAAlP,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,IAAIkN,YAAY,IAAI,EAAE,EAAE;QAAA;QAAAtO,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAC7B8N,eAAe,GAAG,YAAY;MAChC,CAAC,MAAM;QAAA;QAAAlP,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACL8N,eAAe,GAAG,WAAW;MAC/B;IAAA;IAEA,IAAIC,sBAA8B;IAAC;IAAAnP,cAAA,GAAAoB,CAAA;IACnC,IAAIkN,YAAY,IAAI,EAAE,EAAE;MAAA;MAAAtO,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACtB+N,sBAAsB,GAAG,OAAO;IAClC,CAAC,MAAM;MAAA;MAAAnP,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,IAAIkN,YAAY,IAAI,EAAE,EAAE;QAAA;QAAAtO,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAC7B+N,sBAAsB,GAAG,YAAY;MACvC,CAAC,MAAM;QAAA;QAAAnP,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACL+N,sBAAsB,GAAG,WAAW;MACtC;IAAA;IAAC;IAAAnP,cAAA,GAAAoB,CAAA;IAED,OAAO;MACL6N,aAAa;MACbC,eAAe;MACfE,mBAAmB,EAAExD,kBAAkB,CAACoC,WAAW,IAAI,EAAE;MAAA;MAAA,CAAAhO,cAAA,GAAAsB,CAAA,WAAG,OAAO;MAAA;MAAA,CAAAtB,cAAA,GAAAsB,CAAA,WAAG,WAAW;MACjF6N;KACD;EACH;EAEA;;;EAGQ,OAAOzM,kBAAkBA,CAACiB,QAAgB;IAAA;IAAA3D,cAAA,GAAAqB,CAAA;IAChD,MAAMuB,SAAS;IAAA;IAAA,CAAA5C,cAAA,GAAAoB,CAAA,SAAGyB,IAAI,CAACwM,GAAG,EAAE;IAC5B,MAAMC,MAAM;IAAA;IAAA,CAAAtP,cAAA,GAAAoB,CAAA,SAAG8I,IAAI,CAACoF,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;IAAC;IAAAxP,cAAA,GAAAoB,CAAA;IAC1D,OAAO,0BAA0BuC,QAAQ,IAAIf,SAAS,IAAI0M,MAAM,EAAE;EACpE;;;;AA7wBFG,OAAA,CAAAtN,mCAAA,GAAAA,mCAAA;AA8wBC;AAAAnC,cAAA,GAAAoB,CAAA;AA7wByBe,mCAAA,CAAAuN,OAAO,GAAG,OAAO;AAAC;AAAA1P,cAAA,GAAAoB,CAAA;AAClBe,mCAAA,CAAA0B,mBAAmB,GAAG,IAAI8L,GAAG,EAAuC;AAE5F;AAAA;AAAA3P,cAAA,GAAAoB,CAAA;AACwBe,mCAAA,CAAAiE,gBAAgB,GAAG;EACzCC,WAAW,EAAE;IACXC,eAAe,EAAE,GAAG;IAAE;IACtBsJ,IAAI,EAAE,IAAI;IACVC,WAAW,EAAE,IAAI;IACjBC,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE;GACV;EACDxI,SAAS,EAAE;IACTE,KAAK,EAAE,IAAI;IAAE;IACbC,QAAQ,EAAE,IAAI;IACdsI,MAAM,EAAE,IAAI;IACZxI,gBAAgB,EAAE,GAAG;IACrByI,eAAe,EAAE,GAAG;IACpBjJ,UAAU,EAAE;GACb;EACDkB,YAAY,EAAE;IACZC,KAAK,EAAE,IAAI;IAAE;IACb+H,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE,GAAG;IACRC,OAAO,EAAE,CAAC,CAAC;;CAEd;AAED;AAAA;AAAApQ,cAAA,GAAAoB,CAAA;AACwBe,mCAAA,CAAAwE,kBAAkB,GAAG;EAC3CC,cAAc,EAAE,GAAG;EAAE;EACrBE,cAAc,EAAE,GAAG;EACnBE,UAAU,EAAE,GAAG;EACfE,aAAa,EAAE,GAAG;EAClBE,SAAS,EAAE,GAAG;EACdE,YAAY,EAAE;CACf", "ignoreList": []}