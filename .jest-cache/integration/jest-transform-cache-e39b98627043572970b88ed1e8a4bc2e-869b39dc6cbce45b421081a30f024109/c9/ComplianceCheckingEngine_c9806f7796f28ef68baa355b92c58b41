433199afcd1f00eed712e5f333c08355
"use strict";

/**
 * Compliance Checking and Validation Engine
 *
 * Comprehensive compliance checking service for Phase 3 Priority 3: Advanced System Analysis Tools
 * Provides SMACNA, ASHRAE, and local code compliance checking with automated validation,
 * reporting, and certification support for HVAC duct systems.
 *
 * @version 3.0.0
 * <AUTHOR> Suite Development Team
 */
/* istanbul ignore next */
function cov_is4rpdbyg() {
  var path = "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\ComplianceCheckingEngine.ts";
  var hash = "342e9922fc081991a44e69c14606d1ed1ce1f541";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\ComplianceCheckingEngine.ts",
    statementMap: {
      "0": {
        start: {
          line: 12,
          column: 0
        },
        end: {
          line: 12,
          column: 62
        }
      },
      "1": {
        start: {
          line: 13,
          column: 0
        },
        end: {
          line: 13,
          column: 42
        }
      },
      "2": {
        start: {
          line: 14,
          column: 30
        },
        end: {
          line: 14,
          column: 68
        }
      },
      "3": {
        start: {
          line: 32,
          column: 8
        },
        end: {
          line: 79,
          column: 9
        }
      },
      "4": {
        start: {
          line: 33,
          column: 31
        },
        end: {
          line: 33,
          column: 78
        }
      },
      "5": {
        start: {
          line: 34,
          column: 30
        },
        end: {
          line: 34,
          column: 40
        }
      },
      "6": {
        start: {
          line: 36,
          column: 40
        },
        end: {
          line: 36,
          column: 107
        }
      },
      "7": {
        start: {
          line: 38,
          column: 37
        },
        end: {
          line: 38,
          column: 126
        }
      },
      "8": {
        start: {
          line: 40,
          column: 37
        },
        end: {
          line: 40,
          column: 110
        }
      },
      "9": {
        start: {
          line: 42,
          column: 41
        },
        end: {
          line: 42,
          column: 131
        }
      },
      "10": {
        start: {
          line: 44,
          column: 44
        },
        end: {
          line: 44,
          column: 144
        }
      },
      "11": {
        start: {
          line: 46,
          column: 40
        },
        end: {
          line: 46,
          column: 133
        }
      },
      "12": {
        start: {
          line: 48,
          column: 38
        },
        end: {
          line: 54,
          column: 14
        }
      },
      "13": {
        start: {
          line: 56,
          column: 36
        },
        end: {
          line: 56,
          column: 207
        }
      },
      "14": {
        start: {
          line: 58,
          column: 46
        },
        end: {
          line: 58,
          column: 147
        }
      },
      "15": {
        start: {
          line: 59,
          column: 29
        },
        end: {
          line: 72,
          column: 13
        }
      },
      "16": {
        start: {
          line: 74,
          column: 12
        },
        end: {
          line: 74,
          column: 60
        }
      },
      "17": {
        start: {
          line: 75,
          column: 12
        },
        end: {
          line: 75,
          column: 28
        }
      },
      "18": {
        start: {
          line: 78,
          column: 12
        },
        end: {
          line: 78,
          column: 119
        }
      },
      "19": {
        start: {
          line: 85,
          column: 26
        },
        end: {
          line: 85,
          column: 28
        }
      },
      "20": {
        start: {
          line: 87,
          column: 8
        },
        end: {
          line: 93,
          column: 11
        }
      },
      "21": {
        start: {
          line: 94,
          column: 8
        },
        end: {
          line: 100,
          column: 11
        }
      },
      "22": {
        start: {
          line: 102,
          column: 8
        },
        end: {
          line: 122,
          column: 9
        }
      },
      "23": {
        start: {
          line: 103,
          column: 12
        },
        end: {
          line: 111,
          column: 13
        }
      },
      "24": {
        start: {
          line: 104,
          column: 16
        },
        end: {
          line: 110,
          column: 19
        }
      },
      "25": {
        start: {
          line: 113,
          column: 12
        },
        end: {
          line: 121,
          column: 13
        }
      },
      "26": {
        start: {
          line: 114,
          column: 16
        },
        end: {
          line: 120,
          column: 19
        }
      },
      "27": {
        start: {
          line: 124,
          column: 8
        },
        end: {
          line: 136,
          column: 9
        }
      },
      "28": {
        start: {
          line: 125,
          column: 12
        },
        end: {
          line: 135,
          column: 15
        }
      },
      "29": {
        start: {
          line: 126,
          column: 16
        },
        end: {
          line: 134,
          column: 17
        }
      },
      "30": {
        start: {
          line: 126,
          column: 41
        },
        end: {
          line: 126,
          column: 56
        }
      },
      "31": {
        start: {
          line: 127,
          column: 20
        },
        end: {
          line: 133,
          column: 23
        }
      },
      "32": {
        start: {
          line: 137,
          column: 8
        },
        end: {
          line: 137,
          column: 25
        }
      },
      "33": {
        start: {
          line: 143,
          column: 23
        },
        end: {
          line: 143,
          column: 25
        }
      },
      "34": {
        start: {
          line: 145,
          column: 30
        },
        end: {
          line: 145,
          column: 74
        }
      },
      "35": {
        start: {
          line: 146,
          column: 31
        },
        end: {
          line: 146,
          column: 80
        }
      },
      "36": {
        start: {
          line: 147,
          column: 8
        },
        end: {
          line: 155,
          column: 11
        }
      },
      "37": {
        start: {
          line: 157,
          column: 38
        },
        end: {
          line: 157,
          column: 83
        }
      },
      "38": {
        start: {
          line: 158,
          column: 8
        },
        end: {
          line: 166,
          column: 11
        }
      },
      "39": {
        start: {
          line: 168,
          column: 33
        },
        end: {
          line: 168,
          column: 78
        }
      },
      "40": {
        start: {
          line: 169,
          column: 29
        },
        end: {
          line: 169,
          column: 71
        }
      },
      "41": {
        start: {
          line: 170,
          column: 8
        },
        end: {
          line: 178,
          column: 11
        }
      },
      "42": {
        start: {
          line: 180,
          column: 32
        },
        end: {
          line: 180,
          column: 128
        }
      },
      "43": {
        start: {
          line: 180,
          column: 55
        },
        end: {
          line: 180,
          column: 120
        }
      },
      "44": {
        start: {
          line: 181,
          column: 30
        },
        end: {
          line: 185,
          column: 68
        }
      },
      "45": {
        start: {
          line: 186,
          column: 8
        },
        end: {
          line: 194,
          column: 10
        }
      },
      "46": {
        start: {
          line: 192,
          column: 51
        },
        end: {
          line: 192,
          column: 77
        }
      },
      "47": {
        start: {
          line: 193,
          column: 45
        },
        end: {
          line: 193,
          column: 73
        }
      },
      "48": {
        start: {
          line: 202,
          column: 8
        },
        end: {
          line: 202,
          column: 20
        }
      },
      "49": {
        start: {
          line: 209,
          column: 31
        },
        end: {
          line: 209,
          column: 82
        }
      },
      "50": {
        start: {
          line: 211,
          column: 8
        },
        end: {
          line: 212,
          column: 23
        }
      },
      "51": {
        start: {
          line: 212,
          column: 12
        },
        end: {
          line: 212,
          column: 23
        }
      },
      "52": {
        start: {
          line: 213,
          column: 8
        },
        end: {
          line: 214,
          column: 23
        }
      },
      "53": {
        start: {
          line: 214,
          column: 12
        },
        end: {
          line: 214,
          column: 23
        }
      },
      "54": {
        start: {
          line: 215,
          column: 8
        },
        end: {
          line: 215,
          column: 19
        }
      },
      "55": {
        start: {
          line: 221,
          column: 23
        },
        end: {
          line: 221,
          column: 25
        }
      },
      "56": {
        start: {
          line: 223,
          column: 31
        },
        end: {
          line: 223,
          column: 82
        }
      },
      "57": {
        start: {
          line: 224,
          column: 26
        },
        end: {
          line: 224,
          column: 87
        }
      },
      "58": {
        start: {
          line: 225,
          column: 30
        },
        end: {
          line: 227,
          column: 73
        }
      },
      "59": {
        start: {
          line: 228,
          column: 8
        },
        end: {
          line: 236,
          column: 11
        }
      },
      "60": {
        start: {
          line: 238,
          column: 33
        },
        end: {
          line: 238,
          column: 78
        }
      },
      "61": {
        start: {
          line: 239,
          column: 29
        },
        end: {
          line: 239,
          column: 77
        }
      },
      "62": {
        start: {
          line: 240,
          column: 29
        },
        end: {
          line: 240,
          column: 75
        }
      },
      "63": {
        start: {
          line: 241,
          column: 8
        },
        end: {
          line: 249,
          column: 11
        }
      },
      "64": {
        start: {
          line: 251,
          column: 41
        },
        end: {
          line: 251,
          column: 89
        }
      },
      "65": {
        start: {
          line: 252,
          column: 8
        },
        end: {
          line: 260,
          column: 11
        }
      },
      "66": {
        start: {
          line: 261,
          column: 32
        },
        end: {
          line: 261,
          column: 128
        }
      },
      "67": {
        start: {
          line: 261,
          column: 55
        },
        end: {
          line: 261,
          column: 120
        }
      },
      "68": {
        start: {
          line: 263,
          column: 8
        },
        end: {
          line: 271,
          column: 9
        }
      },
      "69": {
        start: {
          line: 264,
          column: 12
        },
        end: {
          line: 264,
          column: 77
        }
      },
      "70": {
        start: {
          line: 266,
          column: 13
        },
        end: {
          line: 271,
          column: 9
        }
      },
      "71": {
        start: {
          line: 267,
          column: 12
        },
        end: {
          line: 267,
          column: 87
        }
      },
      "72": {
        start: {
          line: 270,
          column: 12
        },
        end: {
          line: 270,
          column: 81
        }
      },
      "73": {
        start: {
          line: 272,
          column: 8
        },
        end: {
          line: 280,
          column: 10
        }
      },
      "74": {
        start: {
          line: 278,
          column: 51
        },
        end: {
          line: 278,
          column: 77
        }
      },
      "75": {
        start: {
          line: 279,
          column: 45
        },
        end: {
          line: 279,
          column: 73
        }
      },
      "76": {
        start: {
          line: 286,
          column: 8
        },
        end: {
          line: 287,
          column: 29
        }
      },
      "77": {
        start: {
          line: 287,
          column: 12
        },
        end: {
          line: 287,
          column: 29
        }
      },
      "78": {
        start: {
          line: 288,
          column: 8
        },
        end: {
          line: 289,
          column: 29
        }
      },
      "79": {
        start: {
          line: 289,
          column: 12
        },
        end: {
          line: 289,
          column: 29
        }
      },
      "80": {
        start: {
          line: 290,
          column: 8
        },
        end: {
          line: 291,
          column: 29
        }
      },
      "81": {
        start: {
          line: 291,
          column: 12
        },
        end: {
          line: 291,
          column: 29
        }
      },
      "82": {
        start: {
          line: 292,
          column: 8
        },
        end: {
          line: 292,
          column: 25
        }
      },
      "83": {
        start: {
          line: 298,
          column: 31
        },
        end: {
          line: 298,
          column: 82
        }
      },
      "84": {
        start: {
          line: 300,
          column: 8
        },
        end: {
          line: 300,
          column: 37
        }
      },
      "85": {
        start: {
          line: 306,
          column: 23
        },
        end: {
          line: 306,
          column: 25
        }
      },
      "86": {
        start: {
          line: 308,
          column: 27
        },
        end: {
          line: 308,
          column: 68
        }
      },
      "87": {
        start: {
          line: 309,
          column: 30
        },
        end: {
          line: 309,
          column: 90
        }
      },
      "88": {
        start: {
          line: 310,
          column: 31
        },
        end: {
          line: 310,
          column: 80
        }
      },
      "89": {
        start: {
          line: 311,
          column: 8
        },
        end: {
          line: 319,
          column: 11
        }
      },
      "90": {
        start: {
          line: 321,
          column: 31
        },
        end: {
          line: 321,
          column: 73
        }
      },
      "91": {
        start: {
          line: 322,
          column: 8
        },
        end: {
          line: 330,
          column: 11
        }
      },
      "92": {
        start: {
          line: 331,
          column: 32
        },
        end: {
          line: 331,
          column: 128
        }
      },
      "93": {
        start: {
          line: 331,
          column: 55
        },
        end: {
          line: 331,
          column: 120
        }
      },
      "94": {
        start: {
          line: 332,
          column: 30
        },
        end: {
          line: 334,
          column: 64
        }
      },
      "95": {
        start: {
          line: 335,
          column: 8
        },
        end: {
          line: 343,
          column: 10
        }
      },
      "96": {
        start: {
          line: 341,
          column: 51
        },
        end: {
          line: 341,
          column: 77
        }
      },
      "97": {
        start: {
          line: 342,
          column: 45
        },
        end: {
          line: 342,
          column: 73
        }
      },
      "98": {
        start: {
          line: 349,
          column: 8
        },
        end: {
          line: 350,
          column: 30
        }
      },
      "99": {
        start: {
          line: 350,
          column: 12
        },
        end: {
          line: 350,
          column: 30
        }
      },
      "100": {
        start: {
          line: 351,
          column: 8
        },
        end: {
          line: 351,
          column: 27
        }
      },
      "101": {
        start: {
          line: 357,
          column: 8
        },
        end: {
          line: 358,
          column: 62
        }
      },
      "102": {
        start: {
          line: 358,
          column: 12
        },
        end: {
          line: 358,
          column: 62
        }
      },
      "103": {
        start: {
          line: 359,
          column: 8
        },
        end: {
          line: 359,
          column: 54
        }
      },
      "104": {
        start: {
          line: 366,
          column: 8
        },
        end: {
          line: 366,
          column: 20
        }
      },
      "105": {
        start: {
          line: 372,
          column: 23
        },
        end: {
          line: 372,
          column: 25
        }
      },
      "106": {
        start: {
          line: 374,
          column: 32
        },
        end: {
          line: 375,
          column: 62
        }
      },
      "107": {
        start: {
          line: 376,
          column: 28
        },
        end: {
          line: 376,
          column: 32
        }
      },
      "108": {
        start: {
          line: 377,
          column: 8
        },
        end: {
          line: 385,
          column: 11
        }
      },
      "109": {
        start: {
          line: 387,
          column: 37
        },
        end: {
          line: 387,
          column: 89
        }
      },
      "110": {
        start: {
          line: 388,
          column: 8
        },
        end: {
          line: 396,
          column: 11
        }
      },
      "111": {
        start: {
          line: 397,
          column: 32
        },
        end: {
          line: 397,
          column: 128
        }
      },
      "112": {
        start: {
          line: 397,
          column: 55
        },
        end: {
          line: 397,
          column: 120
        }
      },
      "113": {
        start: {
          line: 398,
          column: 30
        },
        end: {
          line: 400,
          column: 70
        }
      },
      "114": {
        start: {
          line: 401,
          column: 8
        },
        end: {
          line: 409,
          column: 10
        }
      },
      "115": {
        start: {
          line: 407,
          column: 51
        },
        end: {
          line: 407,
          column: 77
        }
      },
      "116": {
        start: {
          line: 408,
          column: 45
        },
        end: {
          line: 408,
          column: 73
        }
      },
      "117": {
        start: {
          line: 416,
          column: 8
        },
        end: {
          line: 416,
          column: 20
        }
      },
      "118": {
        start: {
          line: 422,
          column: 23
        },
        end: {
          line: 422,
          column: 25
        }
      },
      "119": {
        start: {
          line: 424,
          column: 31
        },
        end: {
          line: 424,
          column: 76
        }
      },
      "120": {
        start: {
          line: 425,
          column: 27
        },
        end: {
          line: 425,
          column: 29
        }
      },
      "121": {
        start: {
          line: 426,
          column: 8
        },
        end: {
          line: 434,
          column: 11
        }
      },
      "122": {
        start: {
          line: 436,
          column: 31
        },
        end: {
          line: 436,
          column: 72
        }
      },
      "123": {
        start: {
          line: 437,
          column: 8
        },
        end: {
          line: 445,
          column: 11
        }
      },
      "124": {
        start: {
          line: 446,
          column: 32
        },
        end: {
          line: 446,
          column: 128
        }
      },
      "125": {
        start: {
          line: 446,
          column: 55
        },
        end: {
          line: 446,
          column: 120
        }
      },
      "126": {
        start: {
          line: 447,
          column: 30
        },
        end: {
          line: 449,
          column: 70
        }
      },
      "127": {
        start: {
          line: 450,
          column: 8
        },
        end: {
          line: 458,
          column: 10
        }
      },
      "128": {
        start: {
          line: 456,
          column: 51
        },
        end: {
          line: 456,
          column: 77
        }
      },
      "129": {
        start: {
          line: 457,
          column: 45
        },
        end: {
          line: 457,
          column: 73
        }
      },
      "130": {
        start: {
          line: 464,
          column: 30
        },
        end: {
          line: 464,
          column: 80
        }
      },
      "131": {
        start: {
          line: 465,
          column: 31
        },
        end: {
          line: 465,
          column: 82
        }
      },
      "132": {
        start: {
          line: 467,
          column: 26
        },
        end: {
          line: 467,
          column: 28
        }
      },
      "133": {
        start: {
          line: 468,
          column: 26
        },
        end: {
          line: 468,
          column: 63
        }
      },
      "134": {
        start: {
          line: 469,
          column: 30
        },
        end: {
          line: 469,
          column: 48
        }
      },
      "135": {
        start: {
          line: 470,
          column: 8
        },
        end: {
          line: 470,
          column: 65
        }
      },
      "136": {
        start: {
          line: 477,
          column: 8
        },
        end: {
          line: 477,
          column: 20
        }
      },
      "137": {
        start: {
          line: 483,
          column: 26
        },
        end: {
          line: 483,
          column: 66
        }
      },
      "138": {
        start: {
          line: 483,
          column: 52
        },
        end: {
          line: 483,
          column: 65
        }
      },
      "139": {
        start: {
          line: 484,
          column: 32
        },
        end: {
          line: 484,
          column: 131
        }
      },
      "140": {
        start: {
          line: 484,
          column: 58
        },
        end: {
          line: 484,
          column: 123
        }
      },
      "141": {
        start: {
          line: 486,
          column: 37
        },
        end: {
          line: 486,
          column: 79
        }
      },
      "142": {
        start: {
          line: 487,
          column: 8
        },
        end: {
          line: 495,
          column: 9
        }
      },
      "143": {
        start: {
          line: 488,
          column: 12
        },
        end: {
          line: 488,
          column: 77
        }
      },
      "144": {
        start: {
          line: 490,
          column: 13
        },
        end: {
          line: 495,
          column: 9
        }
      },
      "145": {
        start: {
          line: 491,
          column: 12
        },
        end: {
          line: 491,
          column: 87
        }
      },
      "146": {
        start: {
          line: 494,
          column: 12
        },
        end: {
          line: 494,
          column: 81
        }
      },
      "147": {
        start: {
          line: 496,
          column: 8
        },
        end: {
          line: 504,
          column: 10
        }
      },
      "148": {
        start: {
          line: 502,
          column: 54
        },
        end: {
          line: 502,
          column: 80
        }
      },
      "149": {
        start: {
          line: 503,
          column: 48
        },
        end: {
          line: 503,
          column: 76
        }
      },
      "150": {
        start: {
          line: 510,
          column: 32
        },
        end: {
          line: 510,
          column: 34
        }
      },
      "151": {
        start: {
          line: 512,
          column: 35
        },
        end: {
          line: 512,
          column: 158
        }
      },
      "152": {
        start: {
          line: 512,
          column: 61
        },
        end: {
          line: 512,
          column: 157
        }
      },
      "153": {
        start: {
          line: 512,
          column: 91
        },
        end: {
          line: 512,
          column: 156
        }
      },
      "154": {
        start: {
          line: 513,
          column: 8
        },
        end: {
          line: 552,
          column: 11
        }
      },
      "155": {
        start: {
          line: 514,
          column: 12
        },
        end: {
          line: 532,
          column: 13
        }
      },
      "156": {
        start: {
          line: 515,
          column: 16
        },
        end: {
          line: 531,
          column: 19
        }
      },
      "157": {
        start: {
          line: 533,
          column: 12
        },
        end: {
          line: 551,
          column: 13
        }
      },
      "158": {
        start: {
          line: 534,
          column: 16
        },
        end: {
          line: 550,
          column: 19
        }
      },
      "159": {
        start: {
          line: 553,
          column: 8
        },
        end: {
          line: 553,
          column: 31
        }
      },
      "160": {
        start: {
          line: 559,
          column: 29
        },
        end: {
          line: 559,
          column: 31
        }
      },
      "161": {
        start: {
          line: 561,
          column: 8
        },
        end: {
          line: 574,
          column: 9
        }
      },
      "162": {
        start: {
          line: 562,
          column: 12
        },
        end: {
          line: 573,
          column: 15
        }
      },
      "163": {
        start: {
          line: 576,
          column: 8
        },
        end: {
          line: 589,
          column: 9
        }
      },
      "164": {
        start: {
          line: 577,
          column: 12
        },
        end: {
          line: 588,
          column: 15
        }
      },
      "165": {
        start: {
          line: 590,
          column: 8
        },
        end: {
          line: 590,
          column: 28
        }
      },
      "166": {
        start: {
          line: 596,
          column: 26
        },
        end: {
          line: 596,
          column: 36
        }
      },
      "167": {
        start: {
          line: 597,
          column: 23
        },
        end: {
          line: 597,
          column: 65
        }
      },
      "168": {
        start: {
          line: 598,
          column: 8
        },
        end: {
          line: 598,
          column: 72
        }
      },
      "169": {
        start: {
          line: 601,
          column: 0
        },
        end: {
          line: 601,
          column: 60
        }
      },
      "170": {
        start: {
          line: 602,
          column: 0
        },
        end: {
          line: 602,
          column: 43
        }
      },
      "171": {
        start: {
          line: 603,
          column: 0
        },
        end: {
          line: 603,
          column: 54
        }
      },
      "172": {
        start: {
          line: 605,
          column: 0
        },
        end: {
          line: 622,
          column: 2
        }
      },
      "173": {
        start: {
          line: 624,
          column: 0
        },
        end: {
          line: 641,
          column: 2
        }
      },
      "174": {
        start: {
          line: 643,
          column: 0
        },
        end: {
          line: 659,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 31,
            column: 4
          },
          end: {
            line: 31,
            column: 5
          }
        },
        loc: {
          start: {
            line: 31,
            column: 157
          },
          end: {
            line: 80,
            column: 5
          }
        },
        line: 31
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 84,
            column: 4
          },
          end: {
            line: 84,
            column: 5
          }
        },
        loc: {
          start: {
            line: 84,
            column: 74
          },
          end: {
            line: 138,
            column: 5
          }
        },
        line: 84
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 125,
            column: 36
          },
          end: {
            line: 125,
            column: 37
          }
        },
        loc: {
          start: {
            line: 125,
            column: 44
          },
          end: {
            line: 135,
            column: 13
          }
        },
        line: 125
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 126,
            column: 36
          },
          end: {
            line: 126,
            column: 37
          }
        },
        loc: {
          start: {
            line: 126,
            column: 41
          },
          end: {
            line: 126,
            column: 56
          }
        },
        line: 126
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 142,
            column: 4
          },
          end: {
            line: 142,
            column: 5
          }
        },
        loc: {
          start: {
            line: 142,
            column: 96
          },
          end: {
            line: 195,
            column: 5
          }
        },
        line: 142
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 180,
            column: 46
          },
          end: {
            line: 180,
            column: 47
          }
        },
        loc: {
          start: {
            line: 180,
            column: 55
          },
          end: {
            line: 180,
            column: 120
          }
        },
        line: 180
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 192,
            column: 42
          },
          end: {
            line: 192,
            column: 43
          }
        },
        loc: {
          start: {
            line: 192,
            column: 51
          },
          end: {
            line: 192,
            column: 77
          }
        },
        line: 192
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 193,
            column: 36
          },
          end: {
            line: 193,
            column: 37
          }
        },
        loc: {
          start: {
            line: 193,
            column: 45
          },
          end: {
            line: 193,
            column: 73
          }
        },
        line: 193
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 199,
            column: 4
          },
          end: {
            line: 199,
            column: 5
          }
        },
        loc: {
          start: {
            line: 199,
            column: 52
          },
          end: {
            line: 203,
            column: 5
          }
        },
        line: 199
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 207,
            column: 4
          },
          end: {
            line: 207,
            column: 5
          }
        },
        loc: {
          start: {
            line: 207,
            column: 52
          },
          end: {
            line: 216,
            column: 5
          }
        },
        line: 207
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 220,
            column: 4
          },
          end: {
            line: 220,
            column: 5
          }
        },
        loc: {
          start: {
            line: 220,
            column: 80
          },
          end: {
            line: 281,
            column: 5
          }
        },
        line: 220
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 261,
            column: 46
          },
          end: {
            line: 261,
            column: 47
          }
        },
        loc: {
          start: {
            line: 261,
            column: 55
          },
          end: {
            line: 261,
            column: 120
          }
        },
        line: 261
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 278,
            column: 42
          },
          end: {
            line: 278,
            column: 43
          }
        },
        loc: {
          start: {
            line: 278,
            column: 51
          },
          end: {
            line: 278,
            column: 77
          }
        },
        line: 278
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 279,
            column: 36
          },
          end: {
            line: 279,
            column: 37
          }
        },
        loc: {
          start: {
            line: 279,
            column: 45
          },
          end: {
            line: 279,
            column: 73
          }
        },
        line: 279
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 285,
            column: 4
          },
          end: {
            line: 285,
            column: 5
          }
        },
        loc: {
          start: {
            line: 285,
            column: 49
          },
          end: {
            line: 293,
            column: 5
          }
        },
        line: 285
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 297,
            column: 4
          },
          end: {
            line: 297,
            column: 5
          }
        },
        loc: {
          start: {
            line: 297,
            column: 55
          },
          end: {
            line: 301,
            column: 5
          }
        },
        line: 297
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 305,
            column: 4
          },
          end: {
            line: 305,
            column: 5
          }
        },
        loc: {
          start: {
            line: 305,
            column: 97
          },
          end: {
            line: 344,
            column: 5
          }
        },
        line: 305
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 331,
            column: 46
          },
          end: {
            line: 331,
            column: 47
          }
        },
        loc: {
          start: {
            line: 331,
            column: 55
          },
          end: {
            line: 331,
            column: 120
          }
        },
        line: 331
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 341,
            column: 42
          },
          end: {
            line: 341,
            column: 43
          }
        },
        loc: {
          start: {
            line: 341,
            column: 51
          },
          end: {
            line: 341,
            column: 77
          }
        },
        line: 341
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 342,
            column: 36
          },
          end: {
            line: 342,
            column: 37
          }
        },
        loc: {
          start: {
            line: 342,
            column: 45
          },
          end: {
            line: 342,
            column: 73
          }
        },
        line: 342
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 348,
            column: 4
          },
          end: {
            line: 348,
            column: 5
          }
        },
        loc: {
          start: {
            line: 348,
            column: 48
          },
          end: {
            line: 352,
            column: 5
          }
        },
        line: 348
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 356,
            column: 4
          },
          end: {
            line: 356,
            column: 5
          }
        },
        loc: {
          start: {
            line: 356,
            column: 67
          },
          end: {
            line: 360,
            column: 5
          }
        },
        line: 356
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 364,
            column: 4
          },
          end: {
            line: 364,
            column: 5
          }
        },
        loc: {
          start: {
            line: 364,
            column: 49
          },
          end: {
            line: 367,
            column: 5
          }
        },
        line: 364
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 371,
            column: 4
          },
          end: {
            line: 371,
            column: 5
          }
        },
        loc: {
          start: {
            line: 371,
            column: 107
          },
          end: {
            line: 410,
            column: 5
          }
        },
        line: 371
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 397,
            column: 46
          },
          end: {
            line: 397,
            column: 47
          }
        },
        loc: {
          start: {
            line: 397,
            column: 55
          },
          end: {
            line: 397,
            column: 120
          }
        },
        line: 397
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 407,
            column: 42
          },
          end: {
            line: 407,
            column: 43
          }
        },
        loc: {
          start: {
            line: 407,
            column: 51
          },
          end: {
            line: 407,
            column: 77
          }
        },
        line: 407
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 408,
            column: 36
          },
          end: {
            line: 408,
            column: 37
          }
        },
        loc: {
          start: {
            line: 408,
            column: 45
          },
          end: {
            line: 408,
            column: 73
          }
        },
        line: 408
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 414,
            column: 4
          },
          end: {
            line: 414,
            column: 5
          }
        },
        loc: {
          start: {
            line: 414,
            column: 59
          },
          end: {
            line: 417,
            column: 5
          }
        },
        line: 414
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 421,
            column: 4
          },
          end: {
            line: 421,
            column: 5
          }
        },
        loc: {
          start: {
            line: 421,
            column: 100
          },
          end: {
            line: 459,
            column: 5
          }
        },
        line: 421
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 446,
            column: 46
          },
          end: {
            line: 446,
            column: 47
          }
        },
        loc: {
          start: {
            line: 446,
            column: 55
          },
          end: {
            line: 446,
            column: 120
          }
        },
        line: 446
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 456,
            column: 42
          },
          end: {
            line: 456,
            column: 43
          }
        },
        loc: {
          start: {
            line: 456,
            column: 51
          },
          end: {
            line: 456,
            column: 77
          }
        },
        line: 456
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 457,
            column: 36
          },
          end: {
            line: 457,
            column: 37
          }
        },
        loc: {
          start: {
            line: 457,
            column: 45
          },
          end: {
            line: 457,
            column: 73
          }
        },
        line: 457
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 463,
            column: 4
          },
          end: {
            line: 463,
            column: 5
          }
        },
        loc: {
          start: {
            line: 463,
            column: 52
          },
          end: {
            line: 471,
            column: 5
          }
        },
        line: 463
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 475,
            column: 4
          },
          end: {
            line: 475,
            column: 5
          }
        },
        loc: {
          start: {
            line: 475,
            column: 48
          },
          end: {
            line: 478,
            column: 5
          }
        },
        line: 475
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 482,
            column: 4
          },
          end: {
            line: 482,
            column: 5
          }
        },
        loc: {
          start: {
            line: 482,
            column: 47
          },
          end: {
            line: 505,
            column: 5
          }
        },
        line: 482
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 483,
            column: 42
          },
          end: {
            line: 483,
            column: 43
          }
        },
        loc: {
          start: {
            line: 483,
            column: 52
          },
          end: {
            line: 483,
            column: 65
          }
        },
        line: 483
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 484,
            column: 49
          },
          end: {
            line: 484,
            column: 50
          }
        },
        loc: {
          start: {
            line: 484,
            column: 58
          },
          end: {
            line: 484,
            column: 123
          }
        },
        line: 484
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 502,
            column: 45
          },
          end: {
            line: 502,
            column: 46
          }
        },
        loc: {
          start: {
            line: 502,
            column: 54
          },
          end: {
            line: 502,
            column: 80
          }
        },
        line: 502
      },
      "38": {
        name: "(anonymous_38)",
        decl: {
          start: {
            line: 503,
            column: 39
          },
          end: {
            line: 503,
            column: 40
          }
        },
        loc: {
          start: {
            line: 503,
            column: 48
          },
          end: {
            line: 503,
            column: 76
          }
        },
        line: 503
      },
      "39": {
        name: "(anonymous_39)",
        decl: {
          start: {
            line: 509,
            column: 4
          },
          end: {
            line: 509,
            column: 5
          }
        },
        loc: {
          start: {
            line: 509,
            column: 81
          },
          end: {
            line: 554,
            column: 5
          }
        },
        line: 509
      },
      "40": {
        name: "(anonymous_40)",
        decl: {
          start: {
            line: 512,
            column: 51
          },
          end: {
            line: 512,
            column: 52
          }
        },
        loc: {
          start: {
            line: 512,
            column: 61
          },
          end: {
            line: 512,
            column: 157
          }
        },
        line: 512
      },
      "41": {
        name: "(anonymous_41)",
        decl: {
          start: {
            line: 512,
            column: 82
          },
          end: {
            line: 512,
            column: 83
          }
        },
        loc: {
          start: {
            line: 512,
            column: 91
          },
          end: {
            line: 512,
            column: 156
          }
        },
        line: 512
      },
      "42": {
        name: "(anonymous_42)",
        decl: {
          start: {
            line: 513,
            column: 35
          },
          end: {
            line: 513,
            column: 36
          }
        },
        loc: {
          start: {
            line: 513,
            column: 44
          },
          end: {
            line: 552,
            column: 9
          }
        },
        line: 513
      },
      "43": {
        name: "(anonymous_43)",
        decl: {
          start: {
            line: 558,
            column: 4
          },
          end: {
            line: 558,
            column: 5
          }
        },
        loc: {
          start: {
            line: 558,
            column: 108
          },
          end: {
            line: 591,
            column: 5
          }
        },
        line: 558
      },
      "44": {
        name: "(anonymous_44)",
        decl: {
          start: {
            line: 595,
            column: 4
          },
          end: {
            line: 595,
            column: 5
          }
        },
        loc: {
          start: {
            line: 595,
            column: 40
          },
          end: {
            line: 599,
            column: 5
          }
        },
        line: 595
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 78,
            column: 59
          },
          end: {
            line: 78,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 78,
            column: 84
          },
          end: {
            line: 78,
            column: 97
          }
        }, {
          start: {
            line: 78,
            column: 100
          },
          end: {
            line: 78,
            column: 115
          }
        }],
        line: 78
      },
      "1": {
        loc: {
          start: {
            line: 102,
            column: 8
          },
          end: {
            line: 122,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 102,
            column: 8
          },
          end: {
            line: 122,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 102
      },
      "2": {
        loc: {
          start: {
            line: 103,
            column: 12
          },
          end: {
            line: 111,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 103,
            column: 12
          },
          end: {
            line: 111,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 103
      },
      "3": {
        loc: {
          start: {
            line: 113,
            column: 12
          },
          end: {
            line: 121,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 113,
            column: 12
          },
          end: {
            line: 121,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 113
      },
      "4": {
        loc: {
          start: {
            line: 124,
            column: 8
          },
          end: {
            line: 136,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 124,
            column: 8
          },
          end: {
            line: 136,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 124
      },
      "5": {
        loc: {
          start: {
            line: 126,
            column: 16
          },
          end: {
            line: 134,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 126,
            column: 16
          },
          end: {
            line: 134,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 126
      },
      "6": {
        loc: {
          start: {
            line: 152,
            column: 20
          },
          end: {
            line: 152,
            column: 157
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 152,
            column: 54
          },
          end: {
            line: 152,
            column: 102
          }
        }, {
          start: {
            line: 152,
            column: 105
          },
          end: {
            line: 152,
            column: 157
          }
        }],
        line: 152
      },
      "7": {
        loc: {
          start: {
            line: 153,
            column: 22
          },
          end: {
            line: 153,
            column: 72
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 153,
            column: 56
          },
          end: {
            line: 153,
            column: 62
          }
        }, {
          start: {
            line: 153,
            column: 65
          },
          end: {
            line: 153,
            column: 72
          }
        }],
        line: 153
      },
      "8": {
        loc: {
          start: {
            line: 162,
            column: 20
          },
          end: {
            line: 162,
            column: 69
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 162,
            column: 44
          },
          end: {
            line: 162,
            column: 54
          }
        }, {
          start: {
            line: 162,
            column: 57
          },
          end: {
            line: 162,
            column: 69
          }
        }],
        line: 162
      },
      "9": {
        loc: {
          start: {
            line: 163,
            column: 20
          },
          end: {
            line: 163,
            column: 147
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 163,
            column: 44
          },
          end: {
            line: 163,
            column: 92
          }
        }, {
          start: {
            line: 163,
            column: 95
          },
          end: {
            line: 163,
            column: 147
          }
        }],
        line: 163
      },
      "10": {
        loc: {
          start: {
            line: 164,
            column: 22
          },
          end: {
            line: 164,
            column: 64
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 164,
            column: 46
          },
          end: {
            line: 164,
            column: 52
          }
        }, {
          start: {
            line: 164,
            column: 55
          },
          end: {
            line: 164,
            column: 64
          }
        }],
        line: 164
      },
      "11": {
        loc: {
          start: {
            line: 175,
            column: 20
          },
          end: {
            line: 175,
            column: 158
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 175,
            column: 55
          },
          end: {
            line: 175,
            column: 103
          }
        }, {
          start: {
            line: 175,
            column: 106
          },
          end: {
            line: 175,
            column: 158
          }
        }],
        line: 175
      },
      "12": {
        loc: {
          start: {
            line: 176,
            column: 22
          },
          end: {
            line: 176,
            column: 73
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 176,
            column: 57
          },
          end: {
            line: 176,
            column: 63
          }
        }, {
          start: {
            line: 176,
            column: 66
          },
          end: {
            line: 176,
            column: 73
          }
        }],
        line: 176
      },
      "13": {
        loc: {
          start: {
            line: 181,
            column: 30
          },
          end: {
            line: 185,
            column: 68
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 182,
            column: 12
          },
          end: {
            line: 182,
            column: 60
          }
        }, {
          start: {
            line: 183,
            column: 12
          },
          end: {
            line: 185,
            column: 68
          }
        }],
        line: 181
      },
      "14": {
        loc: {
          start: {
            line: 183,
            column: 12
          },
          end: {
            line: 185,
            column: 68
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 184,
            column: 16
          },
          end: {
            line: 184,
            column: 74
          }
        }, {
          start: {
            line: 185,
            column: 16
          },
          end: {
            line: 185,
            column: 68
          }
        }],
        line: 183
      },
      "15": {
        loc: {
          start: {
            line: 211,
            column: 8
          },
          end: {
            line: 212,
            column: 23
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 211,
            column: 8
          },
          end: {
            line: 212,
            column: 23
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 211
      },
      "16": {
        loc: {
          start: {
            line: 213,
            column: 8
          },
          end: {
            line: 214,
            column: 23
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 213,
            column: 8
          },
          end: {
            line: 214,
            column: 23
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 213
      },
      "17": {
        loc: {
          start: {
            line: 224,
            column: 26
          },
          end: {
            line: 224,
            column: 87
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 224,
            column: 26
          },
          end: {
            line: 224,
            column: 70
          }
        }, {
          start: {
            line: 224,
            column: 74
          },
          end: {
            line: 224,
            column: 87
          }
        }],
        line: 224
      },
      "18": {
        loc: {
          start: {
            line: 225,
            column: 30
          },
          end: {
            line: 227,
            column: 73
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 226,
            column: 12
          },
          end: {
            line: 226,
            column: 67
          }
        }, {
          start: {
            line: 227,
            column: 12
          },
          end: {
            line: 227,
            column: 73
          }
        }],
        line: 225
      },
      "19": {
        loc: {
          start: {
            line: 233,
            column: 20
          },
          end: {
            line: 233,
            column: 157
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 233,
            column: 54
          },
          end: {
            line: 233,
            column: 102
          }
        }, {
          start: {
            line: 233,
            column: 105
          },
          end: {
            line: 233,
            column: 157
          }
        }],
        line: 233
      },
      "20": {
        loc: {
          start: {
            line: 234,
            column: 22
          },
          end: {
            line: 234,
            column: 72
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 234,
            column: 56
          },
          end: {
            line: 234,
            column: 62
          }
        }, {
          start: {
            line: 234,
            column: 65
          },
          end: {
            line: 234,
            column: 72
          }
        }],
        line: 234
      },
      "21": {
        loc: {
          start: {
            line: 246,
            column: 20
          },
          end: {
            line: 246,
            column: 164
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 246,
            column: 61
          },
          end: {
            line: 246,
            column: 109
          }
        }, {
          start: {
            line: 246,
            column: 112
          },
          end: {
            line: 246,
            column: 164
          }
        }],
        line: 246
      },
      "22": {
        loc: {
          start: {
            line: 247,
            column: 22
          },
          end: {
            line: 247,
            column: 81
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 247,
            column: 63
          },
          end: {
            line: 247,
            column: 69
          }
        }, {
          start: {
            line: 247,
            column: 72
          },
          end: {
            line: 247,
            column: 81
          }
        }],
        line: 247
      },
      "23": {
        loc: {
          start: {
            line: 256,
            column: 20
          },
          end: {
            line: 256,
            column: 72
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 256,
            column: 47
          },
          end: {
            line: 256,
            column: 57
          }
        }, {
          start: {
            line: 256,
            column: 60
          },
          end: {
            line: 256,
            column: 72
          }
        }],
        line: 256
      },
      "24": {
        loc: {
          start: {
            line: 257,
            column: 20
          },
          end: {
            line: 257,
            column: 150
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 257,
            column: 47
          },
          end: {
            line: 257,
            column: 95
          }
        }, {
          start: {
            line: 257,
            column: 98
          },
          end: {
            line: 257,
            column: 150
          }
        }],
        line: 257
      },
      "25": {
        loc: {
          start: {
            line: 258,
            column: 22
          },
          end: {
            line: 258,
            column: 67
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 258,
            column: 49
          },
          end: {
            line: 258,
            column: 55
          }
        }, {
          start: {
            line: 258,
            column: 58
          },
          end: {
            line: 258,
            column: 67
          }
        }],
        line: 258
      },
      "26": {
        loc: {
          start: {
            line: 263,
            column: 8
          },
          end: {
            line: 271,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 263,
            column: 8
          },
          end: {
            line: 271,
            column: 9
          }
        }, {
          start: {
            line: 266,
            column: 13
          },
          end: {
            line: 271,
            column: 9
          }
        }],
        line: 263
      },
      "27": {
        loc: {
          start: {
            line: 266,
            column: 13
          },
          end: {
            line: 271,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 266,
            column: 13
          },
          end: {
            line: 271,
            column: 9
          }
        }, {
          start: {
            line: 269,
            column: 13
          },
          end: {
            line: 271,
            column: 9
          }
        }],
        line: 266
      },
      "28": {
        loc: {
          start: {
            line: 286,
            column: 8
          },
          end: {
            line: 287,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 286,
            column: 8
          },
          end: {
            line: 287,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 286
      },
      "29": {
        loc: {
          start: {
            line: 288,
            column: 8
          },
          end: {
            line: 289,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 288,
            column: 8
          },
          end: {
            line: 289,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 288
      },
      "30": {
        loc: {
          start: {
            line: 290,
            column: 8
          },
          end: {
            line: 291,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 290,
            column: 8
          },
          end: {
            line: 291,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 290
      },
      "31": {
        loc: {
          start: {
            line: 316,
            column: 20
          },
          end: {
            line: 316,
            column: 157
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 316,
            column: 54
          },
          end: {
            line: 316,
            column: 102
          }
        }, {
          start: {
            line: 316,
            column: 105
          },
          end: {
            line: 316,
            column: 157
          }
        }],
        line: 316
      },
      "32": {
        loc: {
          start: {
            line: 317,
            column: 22
          },
          end: {
            line: 317,
            column: 72
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 317,
            column: 56
          },
          end: {
            line: 317,
            column: 62
          }
        }, {
          start: {
            line: 317,
            column: 65
          },
          end: {
            line: 317,
            column: 72
          }
        }],
        line: 317
      },
      "33": {
        loc: {
          start: {
            line: 326,
            column: 20
          },
          end: {
            line: 326,
            column: 66
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 326,
            column: 37
          },
          end: {
            line: 326,
            column: 48
          }
        }, {
          start: {
            line: 326,
            column: 51
          },
          end: {
            line: 326,
            column: 66
          }
        }],
        line: 326
      },
      "34": {
        loc: {
          start: {
            line: 327,
            column: 20
          },
          end: {
            line: 327,
            column: 140
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 327,
            column: 37
          },
          end: {
            line: 327,
            column: 85
          }
        }, {
          start: {
            line: 327,
            column: 88
          },
          end: {
            line: 327,
            column: 140
          }
        }],
        line: 327
      },
      "35": {
        loc: {
          start: {
            line: 328,
            column: 22
          },
          end: {
            line: 328,
            column: 55
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 328,
            column: 39
          },
          end: {
            line: 328,
            column: 45
          }
        }, {
          start: {
            line: 328,
            column: 48
          },
          end: {
            line: 328,
            column: 55
          }
        }],
        line: 328
      },
      "36": {
        loc: {
          start: {
            line: 332,
            column: 30
          },
          end: {
            line: 334,
            column: 64
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 333,
            column: 12
          },
          end: {
            line: 333,
            column: 60
          }
        }, {
          start: {
            line: 334,
            column: 12
          },
          end: {
            line: 334,
            column: 64
          }
        }],
        line: 332
      },
      "37": {
        loc: {
          start: {
            line: 349,
            column: 8
          },
          end: {
            line: 350,
            column: 30
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 349,
            column: 8
          },
          end: {
            line: 350,
            column: 30
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 349
      },
      "38": {
        loc: {
          start: {
            line: 357,
            column: 8
          },
          end: {
            line: 358,
            column: 62
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 357,
            column: 8
          },
          end: {
            line: 358,
            column: 62
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 357
      },
      "39": {
        loc: {
          start: {
            line: 382,
            column: 20
          },
          end: {
            line: 382,
            column: 156
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 382,
            column: 53
          },
          end: {
            line: 382,
            column: 101
          }
        }, {
          start: {
            line: 382,
            column: 104
          },
          end: {
            line: 382,
            column: 156
          }
        }],
        line: 382
      },
      "40": {
        loc: {
          start: {
            line: 383,
            column: 22
          },
          end: {
            line: 383,
            column: 73
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 383,
            column: 55
          },
          end: {
            line: 383,
            column: 61
          }
        }, {
          start: {
            line: 383,
            column: 64
          },
          end: {
            line: 383,
            column: 73
          }
        }],
        line: 383
      },
      "41": {
        loc: {
          start: {
            line: 392,
            column: 20
          },
          end: {
            line: 392,
            column: 72
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 392,
            column: 43
          },
          end: {
            line: 392,
            column: 54
          }
        }, {
          start: {
            line: 392,
            column: 57
          },
          end: {
            line: 392,
            column: 72
          }
        }],
        line: 392
      },
      "42": {
        loc: {
          start: {
            line: 393,
            column: 20
          },
          end: {
            line: 393,
            column: 146
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 393,
            column: 43
          },
          end: {
            line: 393,
            column: 91
          }
        }, {
          start: {
            line: 393,
            column: 94
          },
          end: {
            line: 393,
            column: 146
          }
        }],
        line: 393
      },
      "43": {
        loc: {
          start: {
            line: 394,
            column: 22
          },
          end: {
            line: 394,
            column: 63
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 394,
            column: 45
          },
          end: {
            line: 394,
            column: 51
          }
        }, {
          start: {
            line: 394,
            column: 54
          },
          end: {
            line: 394,
            column: 63
          }
        }],
        line: 394
      },
      "44": {
        loc: {
          start: {
            line: 398,
            column: 30
          },
          end: {
            line: 400,
            column: 70
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 399,
            column: 12
          },
          end: {
            line: 399,
            column: 60
          }
        }, {
          start: {
            line: 400,
            column: 12
          },
          end: {
            line: 400,
            column: 70
          }
        }],
        line: 398
      },
      "45": {
        loc: {
          start: {
            line: 431,
            column: 20
          },
          end: {
            line: 431,
            column: 154
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 431,
            column: 51
          },
          end: {
            line: 431,
            column: 99
          }
        }, {
          start: {
            line: 431,
            column: 102
          },
          end: {
            line: 431,
            column: 154
          }
        }],
        line: 431
      },
      "46": {
        loc: {
          start: {
            line: 432,
            column: 22
          },
          end: {
            line: 432,
            column: 71
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 432,
            column: 53
          },
          end: {
            line: 432,
            column: 59
          }
        }, {
          start: {
            line: 432,
            column: 62
          },
          end: {
            line: 432,
            column: 71
          }
        }],
        line: 432
      },
      "47": {
        loc: {
          start: {
            line: 441,
            column: 20
          },
          end: {
            line: 441,
            column: 66
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 441,
            column: 37
          },
          end: {
            line: 441,
            column: 48
          }
        }, {
          start: {
            line: 441,
            column: 51
          },
          end: {
            line: 441,
            column: 66
          }
        }],
        line: 441
      },
      "48": {
        loc: {
          start: {
            line: 442,
            column: 20
          },
          end: {
            line: 442,
            column: 140
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 442,
            column: 37
          },
          end: {
            line: 442,
            column: 85
          }
        }, {
          start: {
            line: 442,
            column: 88
          },
          end: {
            line: 442,
            column: 140
          }
        }],
        line: 442
      },
      "49": {
        loc: {
          start: {
            line: 443,
            column: 22
          },
          end: {
            line: 443,
            column: 55
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 443,
            column: 39
          },
          end: {
            line: 443,
            column: 45
          }
        }, {
          start: {
            line: 443,
            column: 48
          },
          end: {
            line: 443,
            column: 55
          }
        }],
        line: 443
      },
      "50": {
        loc: {
          start: {
            line: 447,
            column: 30
          },
          end: {
            line: 449,
            column: 70
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 448,
            column: 12
          },
          end: {
            line: 448,
            column: 60
          }
        }, {
          start: {
            line: 449,
            column: 12
          },
          end: {
            line: 449,
            column: 70
          }
        }],
        line: 447
      },
      "51": {
        loc: {
          start: {
            line: 487,
            column: 8
          },
          end: {
            line: 495,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 487,
            column: 8
          },
          end: {
            line: 495,
            column: 9
          }
        }, {
          start: {
            line: 490,
            column: 13
          },
          end: {
            line: 495,
            column: 9
          }
        }],
        line: 487
      },
      "52": {
        loc: {
          start: {
            line: 490,
            column: 13
          },
          end: {
            line: 495,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 490,
            column: 13
          },
          end: {
            line: 495,
            column: 9
          }
        }, {
          start: {
            line: 493,
            column: 13
          },
          end: {
            line: 495,
            column: 9
          }
        }],
        line: 490
      },
      "53": {
        loc: {
          start: {
            line: 514,
            column: 12
          },
          end: {
            line: 532,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 514,
            column: 12
          },
          end: {
            line: 532,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 514
      },
      "54": {
        loc: {
          start: {
            line: 533,
            column: 12
          },
          end: {
            line: 551,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 533,
            column: 12
          },
          end: {
            line: 551,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 533
      },
      "55": {
        loc: {
          start: {
            line: 561,
            column: 8
          },
          end: {
            line: 574,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 561,
            column: 8
          },
          end: {
            line: 574,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 561
      },
      "56": {
        loc: {
          start: {
            line: 576,
            column: 8
          },
          end: {
            line: 589,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 576,
            column: 8
          },
          end: {
            line: 589,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 576
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\ComplianceCheckingEngine.ts",
      mappings: ";AAAA;;;;;;;;;GASG;;;AAEH,qEAYqC;AAErC;;;;;;;;;;;GAWG;AACH,MAAa,wBAAwB;IA+DnC;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,yBAAyB,CAC3C,mBAAwC,EACxC,kBAAsC,EACtC,cAA8B,EAC9B,qBAAkD,EAClD,eAA0B,EAC1B,eAAiC;QAEjC,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC;YACnE,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;YAE7B,iCAAiC;YACjC,MAAM,mBAAmB,GAAG,IAAI,CAAC,4BAA4B,CAAC,eAAe,EAAE,eAAe,CAAC,CAAC;YAEhG,0BAA0B;YAC1B,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,qBAAqB,CACvD,mBAAmB,EACnB,kBAAkB,EAClB,cAAc,CACf,CAAC;YAEF,0BAA0B;YAC1B,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,qBAAqB,CACvD,mBAAmB,EACnB,kBAAkB,CACnB,CAAC;YAEF,+BAA+B;YAC/B,MAAM,oBAAoB,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAC/D,mBAAmB,EACnB,cAAc,EACd,eAAe,CAChB,CAAC;YAEF,iCAAiC;YACjC,MAAM,uBAAuB,GAAG,MAAM,IAAI,CAAC,4BAA4B,CACrE,mBAAmB,EACnB,qBAAqB,EACrB,eAAe,CAChB,CAAC;YAEF,8BAA8B;YAC9B,MAAM,mBAAmB,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAC7D,mBAAmB,EACnB,kBAAkB,EAClB,eAAe,CAChB,CAAC;YAEF,+BAA+B;YAC/B,MAAM,iBAAiB,GAAG,IAAI,CAAC,0BAA0B,CAAC;gBACxD,gBAAgB;gBAChB,gBAAgB;gBAChB,oBAAoB;gBACpB,uBAAuB;gBACvB,mBAAmB;aACpB,CAAC,CAAC;YAEH,sCAAsC;YACtC,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,iCAAiC,CAClE,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,uBAAuB,EAAE,mBAAmB,CAAC,EACxG,mBAAmB,CACpB,CAAC;YAEF,oCAAoC;YACpC,MAAM,yBAAyB,GAAG,MAAM,IAAI,CAAC,+BAA+B,CAC1E,iBAAiB,EACjB,qBAAqB,EACrB,eAAe,CAChB,CAAC;YAEF,MAAM,QAAQ,GAAuB;gBACnC,EAAE,EAAE,UAAU;gBACd,QAAQ,EAAE,mBAAmB,CAAC,EAAE;gBAChC,iBAAiB,EAAE,SAAS;gBAC5B,mBAAmB;gBACnB,gBAAgB;gBAChB,gBAAgB;gBAChB,oBAAoB;gBACpB,uBAAuB;gBACvB,mBAAmB;gBACnB,iBAAiB;gBACjB,eAAe;gBACf,yBAAyB;aAC1B,CAAC;YAEF,qBAAqB;YACrB,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YAEhD,OAAO,QAAQ,CAAC;QAElB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,+BAA+B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;QAC7G,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,4BAA4B,CACzC,eAA0B,EAC1B,eAAiC;QAEjC,MAAM,SAAS,GAAyB,EAAE,CAAC;QAE3C,8BAA8B;QAC9B,SAAS,CAAC,IAAI,CAAC;YACb,IAAI,EAAE,aAAa;YACnB,OAAO,EAAE,MAAM;YACf,KAAK,EAAE,mBAAmB;YAC1B,SAAS,EAAE,IAAI;YACf,WAAW,EAAE,qEAAqE;SACnF,CAAC,CAAC;QAEH,SAAS,CAAC,IAAI,CAAC;YACb,IAAI,EAAE,QAAQ;YACd,OAAO,EAAE,MAAM;YACf,KAAK,EAAE,mBAAmB;YAC1B,SAAS,EAAE,IAAI;YACf,WAAW,EAAE,kCAAkC;SAChD,CAAC,CAAC;QAEH,8BAA8B;QAC9B,IAAI,eAAe,EAAE,CAAC;YACpB,IAAI,eAAe,CAAC,KAAK,KAAK,IAAI,EAAE,CAAC;gBACnC,SAAS,CAAC,IAAI,CAAC;oBACb,IAAI,EAAE,UAAU;oBAChB,OAAO,EAAE,MAAM;oBACf,KAAK,EAAE,mBAAmB;oBAC1B,SAAS,EAAE,IAAI;oBACf,WAAW,EAAE,iDAAiD;iBAC/D,CAAC,CAAC;YACL,CAAC;YAED,iCAAiC;YACjC,IAAI,eAAe,CAAC,OAAO,KAAK,IAAI,EAAE,CAAC;gBACrC,SAAS,CAAC,IAAI,CAAC;oBACb,IAAI,EAAE,MAAM;oBACZ,OAAO,EAAE,MAAM;oBACf,KAAK,EAAE,aAAa;oBACpB,SAAS,EAAE,IAAI;oBACf,WAAW,EAAE,wCAAwC;iBACtD,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,qCAAqC;QACrC,IAAI,eAAe,EAAE,CAAC;YACpB,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBAC7B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC;oBAC1C,SAAS,CAAC,IAAI,CAAC;wBACb,IAAI,EAAE,IAAI;wBACV,OAAO,EAAE,SAAS;wBAClB,KAAK,EAAE,YAAY;wBACnB,SAAS,EAAE,IAAI;wBACf,WAAW,EAAE,wBAAwB,IAAI,EAAE;qBAC5C,CAAC,CAAC;gBACL,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,KAAK,CAAC,qBAAqB,CACxC,mBAAwC,EACxC,kBAAsC,EACtC,cAA8B;QAE9B,MAAM,MAAM,GAAuB,EAAE,CAAC;QAEtC,6BAA6B;QAC7B,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,WAAW,CAAC;QACnE,MAAM,cAAc,GAAG,cAAc,CAAC,iBAAiB,CAAC,gBAAgB,CAAC;QAEzE,MAAM,CAAC,IAAI,CAAC;YACV,WAAW,EAAE,sBAAsB;YACnC,QAAQ,EAAE,kCAAkC;YAC5C,KAAK,EAAE,GAAG,aAAa,QAAQ;YAC/B,MAAM,EAAE,GAAG,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ;YAC5C,MAAM,EAAE,cAAc,IAAI,aAAa,CAAC,CAAC,CAAC,sCAAgB,CAAC,SAAS,CAAC,CAAC,CAAC,sCAAgB,CAAC,aAAa;YACrG,QAAQ,EAAE,cAAc,IAAI,aAAa,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO;YAC5D,WAAW,EAAE,qDAAqD;SACnE,CAAC,CAAC;QAEH,qCAAqC;QACrC,MAAM,qBAAqB,GAAG,IAAI,CAAC,mBAAmB,CAAC,mBAAmB,CAAC,CAAC;QAC5E,MAAM,CAAC,IAAI,CAAC;YACV,WAAW,EAAE,iBAAiB;YAC9B,QAAQ,EAAE,gCAAgC;YAC1C,KAAK,EAAE,sDAAsD;YAC7D,MAAM,EAAE,qBAAqB,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,YAAY;YACzD,MAAM,EAAE,qBAAqB,CAAC,CAAC,CAAC,sCAAgB,CAAC,SAAS,CAAC,CAAC,CAAC,sCAAgB,CAAC,aAAa;YAC3F,QAAQ,EAAE,qBAAqB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;YACpD,WAAW,EAAE,8CAA8C;SAC5D,CAAC,CAAC;QAEH,kCAAkC;QAClC,MAAM,gBAAgB,GAAG,IAAI,CAAC,mBAAmB,CAAC,mBAAmB,CAAC,CAAC;QACvE,MAAM,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,MAAM,CAAC;QAEhE,MAAM,CAAC,IAAI,CAAC;YACV,WAAW,EAAE,cAAc;YAC3B,QAAQ,EAAE,kCAAkC;YAC5C,KAAK,EAAE,GAAG,YAAY,qBAAqB;YAC3C,MAAM,EAAE,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,aAAa;YACnD,MAAM,EAAE,gBAAgB,IAAI,YAAY,CAAC,CAAC,CAAC,sCAAgB,CAAC,SAAS,CAAC,CAAC,CAAC,sCAAgB,CAAC,aAAa;YACtG,QAAQ,EAAE,gBAAgB,IAAI,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO;YAC7D,WAAW,EAAE,mDAAmD;SACjE,CAAC,CAAC;QAEH,+BAA+B;QAC/B,MAAM,eAAe,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,KAAK,sCAAgB,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC;QACnG,MAAM,aAAa,GAAG,eAAe,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC;YACvD,sCAAgB,CAAC,SAAS,CAAC,CAAC;YAC5B,eAAe,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACnC,sCAAgB,CAAC,mBAAmB,CAAC,CAAC;gBACtC,sCAAgB,CAAC,aAAa,CAAC;QAEnC,OAAO;YACL,QAAQ,EAAE,kBAAkB;YAC5B,aAAa;YACb,oBAAoB,EAAE,CAAC,eAAe,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,GAAG;YAC7D,MAAM;YACN,OAAO,EAAE,GAAG,eAAe,OAAO,MAAM,CAAC,MAAM,mBAAmB;YAClE,cAAc,EAAE,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,MAAM;YACzE,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,KAAK,SAAS,CAAC,CAAC,MAAM;SACtE,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,mBAAmB,CAAC,mBAAwC;QACzE,wDAAwD;QACxD,8EAA8E;QAC9E,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,mBAAmB,CAAC,mBAAwC;QACzE,yDAAyD;QACzD,MAAM,cAAc,GAAG,mBAAmB,CAAC,gBAAgB,CAAC,cAAc,CAAC;QAE3E,oDAAoD;QACpD,IAAI,cAAc,GAAG,GAAG;YAAE,OAAO,GAAG,CAAC,CAAC,iBAAiB;QACvD,IAAI,cAAc,GAAG,GAAG;YAAE,OAAO,GAAG,CAAC,CAAC,mBAAmB;QACzD,OAAO,GAAG,CAAC,CAAC,gBAAgB;IAC9B,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,KAAK,CAAC,qBAAqB,CACxC,mBAAwC,EACxC,kBAAsC;QAEtC,MAAM,MAAM,GAAuB,EAAE,CAAC;QAEtC,oCAAoC;QACpC,MAAM,cAAc,GAAG,mBAAmB,CAAC,gBAAgB,CAAC,cAAc,CAAC;QAC3E,MAAM,SAAS,GAAG,mBAAmB,CAAC,iBAAiB,EAAE,KAAK,IAAI,aAAa,CAAC;QAChF,MAAM,aAAa,GAAG,SAAS,KAAK,OAAO,CAAC,CAAC;YAC3C,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YACzD,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,YAAY,CAAC,WAAW,CAAC;QAEhE,MAAM,CAAC,IAAI,CAAC;YACV,WAAW,EAAE,kCAAkC;YAC/C,QAAQ,EAAE,yCAAyC;YACnD,KAAK,EAAE,GAAG,aAAa,iBAAiB,SAAS,QAAQ;YACzD,MAAM,EAAE,GAAG,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,WAAW;YAC/C,MAAM,EAAE,cAAc,IAAI,aAAa,CAAC,CAAC,CAAC,sCAAgB,CAAC,SAAS,CAAC,CAAC,CAAC,sCAAgB,CAAC,aAAa;YACrG,QAAQ,EAAE,cAAc,IAAI,aAAa,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO;YAC5D,WAAW,EAAE,+DAA+D;SAC7E,CAAC,CAAC;QAEH,6BAA6B;QAC7B,MAAM,gBAAgB,GAAG,IAAI,CAAC,mBAAmB,CAAC,mBAAmB,CAAC,CAAC;QACvE,MAAM,YAAY,GAAG,IAAI,CAAC,2BAA2B,CAAC,cAAc,CAAC,CAAC;QACtE,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,YAA6D,CAAC,CAAC;QAErH,MAAM,CAAC,IAAI,CAAC;YACV,WAAW,EAAE,kBAAkB,YAAY,EAAE;YAC7C,QAAQ,EAAE,yCAAyC;YACnD,KAAK,EAAE,GAAG,YAAY,0BAA0B;YAChD,MAAM,EAAE,GAAG,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,0BAA0B;YACtE,MAAM,EAAE,CAAC,gBAAgB,GAAG,CAAC,CAAC,IAAI,YAAY,CAAC,CAAC,CAAC,sCAAgB,CAAC,SAAS,CAAC,CAAC,CAAC,sCAAgB,CAAC,aAAa;YAC5G,QAAQ,EAAE,CAAC,gBAAgB,GAAG,CAAC,CAAC,IAAI,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;YACrE,WAAW,EAAE,iDAAiD;SAC/D,CAAC,CAAC;QAEH,0CAA0C;QAC1C,MAAM,wBAAwB,GAAG,IAAI,CAAC,sBAAsB,CAAC,mBAAmB,CAAC,CAAC;QAClF,MAAM,CAAC,IAAI,CAAC;YACV,WAAW,EAAE,oBAAoB;YACjC,QAAQ,EAAE,yCAAyC;YACnD,KAAK,EAAE,6CAA6C;YACpD,MAAM,EAAE,wBAAwB,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,YAAY;YAC5D,MAAM,EAAE,wBAAwB,CAAC,CAAC,CAAC,sCAAgB,CAAC,SAAS,CAAC,CAAC,CAAC,sCAAgB,CAAC,aAAa;YAC9F,QAAQ,EAAE,wBAAwB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;YACvD,WAAW,EAAE,2DAA2D;SACzE,CAAC,CAAC;QAEH,MAAM,eAAe,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,KAAK,sCAAgB,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC;QAEnG,IAAI,aAA+B,CAAC;QACpC,IAAI,eAAe,KAAK,MAAM,CAAC,MAAM,EAAE,CAAC;YACtC,aAAa,GAAG,sCAAgB,CAAC,SAAS,CAAC;QAC7C,CAAC;aAAM,IAAI,eAAe,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC/C,aAAa,GAAG,sCAAgB,CAAC,mBAAmB,CAAC;QACvD,CAAC;aAAM,CAAC;YACN,aAAa,GAAG,sCAAgB,CAAC,aAAa,CAAC;QACjD,CAAC;QAED,OAAO;YACL,QAAQ,EAAE,+BAA+B;YACzC,aAAa;YACb,oBAAoB,EAAE,CAAC,eAAe,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,GAAG;YAC7D,MAAM;YACN,OAAO,EAAE,GAAG,eAAe,OAAO,MAAM,CAAC,MAAM,0BAA0B;YACzE,cAAc,EAAE,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,MAAM;YACzE,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,KAAK,SAAS,CAAC,CAAC,MAAM;SACtE,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,2BAA2B,CAAC,QAAgB;QACzD,IAAI,QAAQ,IAAI,GAAG;YAAE,OAAO,SAAS,CAAC;QACtC,IAAI,QAAQ,IAAI,GAAG;YAAE,OAAO,SAAS,CAAC;QACtC,IAAI,QAAQ,IAAI,GAAG;YAAE,OAAO,SAAS,CAAC;QACtC,OAAO,SAAS,CAAC,CAAC,kCAAkC;IACtD,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,sBAAsB,CAAC,mBAAwC;QAC5E,MAAM,cAAc,GAAG,mBAAmB,CAAC,gBAAgB,CAAC,cAAc,CAAC;QAC3E,+EAA+E;QAC/E,OAAO,cAAc,IAAI,GAAG,CAAC;IAC/B,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,KAAK,CAAC,yBAAyB,CAC5C,mBAAwC,EACxC,cAA8B,EAC9B,eAAiC;QAEjC,MAAM,MAAM,GAAuB,EAAE,CAAC;QAEtC,mCAAmC;QACnC,MAAM,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC;QAC7D,MAAM,aAAa,GAAG,IAAI,CAAC,0BAA0B,CAAC,UAAU,EAAE,eAAe,CAAC,CAAC;QACnF,MAAM,cAAc,GAAG,cAAc,CAAC,iBAAiB,CAAC,gBAAgB,CAAC;QAEzE,MAAM,CAAC,IAAI,CAAC;YACV,WAAW,EAAE,6BAA6B;YAC1C,QAAQ,EAAE,GAAG,UAAU,yBAAyB;YAChD,KAAK,EAAE,GAAG,aAAa,QAAQ;YAC/B,MAAM,EAAE,GAAG,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ;YAC5C,MAAM,EAAE,cAAc,IAAI,aAAa,CAAC,CAAC,CAAC,sCAAgB,CAAC,SAAS,CAAC,CAAC,CAAC,sCAAgB,CAAC,aAAa;YACrG,QAAQ,EAAE,cAAc,IAAI,aAAa,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO;YAC5D,WAAW,EAAE,GAAG,UAAU,8BAA8B;SACzD,CAAC,CAAC;QAEH,4BAA4B;QAC5B,MAAM,cAAc,GAAG,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC;QAClE,MAAM,CAAC,IAAI,CAAC;YACV,WAAW,EAAE,cAAc;YAC3B,QAAQ,EAAE,GAAG,UAAU,4BAA4B;YACnD,KAAK,EAAE,gCAAgC;YACvC,MAAM,EAAE,cAAc,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,eAAe;YACtD,MAAM,EAAE,cAAc,CAAC,CAAC,CAAC,sCAAgB,CAAC,SAAS,CAAC,CAAC,CAAC,sCAAgB,CAAC,aAAa;YACpF,QAAQ,EAAE,cAAc,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO;YAC3C,WAAW,EAAE,uCAAuC;SACrD,CAAC,CAAC;QAEH,MAAM,eAAe,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,KAAK,sCAAgB,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC;QACnG,MAAM,aAAa,GAAG,eAAe,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC;YACvD,sCAAgB,CAAC,SAAS,CAAC,CAAC;YAC5B,sCAAgB,CAAC,aAAa,CAAC;QAEjC,OAAO;YACL,QAAQ,EAAE,UAAU;YACpB,aAAa;YACb,oBAAoB,EAAE,CAAC,eAAe,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,GAAG;YAC7D,MAAM;YACN,OAAO,EAAE,GAAG,eAAe,OAAO,MAAM,CAAC,MAAM,IAAI,UAAU,mBAAmB;YAChF,cAAc,EAAE,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,MAAM;YACzE,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,KAAK,SAAS,CAAC,CAAC,MAAM;SACtE,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,mBAAmB,CAAC,eAAiC;QAClE,IAAI,eAAe,EAAE,KAAK,KAAK,IAAI;YAAE,OAAO,UAAU,CAAC;QACvD,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,0BAA0B,CAAC,UAAkB,EAAE,eAAiC;QAC7F,IAAI,UAAU,KAAK,UAAU;YAAE,OAAO,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,SAAS,CAAC;QACjF,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC;IAChD,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,gBAAgB,CAAC,mBAAwC;QACtE,sDAAsD;QACtD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,KAAK,CAAC,4BAA4B,CAC/C,mBAAwC,EACxC,qBAAkD,EAClD,eAAiC;QAEjC,MAAM,MAAM,GAAuB,EAAE,CAAC;QAEtC,8CAA8C;QAC9C,MAAM,eAAe,GAAG,qBAAqB,CAAC,eAAe,CAAC,cAAc,CAAC,KAAK;YAC3D,mBAAmB,CAAC,gBAAgB,CAAC,aAAa,CAAC;QAC1E,MAAM,WAAW,GAAG,IAAI,CAAC,CAAC,mCAAmC;QAE7D,MAAM,CAAC,IAAI,CAAC;YACV,WAAW,EAAE,wBAAwB;YACrC,QAAQ,EAAE,2BAA2B;YACrC,KAAK,EAAE,GAAG,WAAW,mBAAmB;YACxC,MAAM,EAAE,GAAG,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,mBAAmB;YACxD,MAAM,EAAE,eAAe,IAAI,WAAW,CAAC,CAAC,CAAC,sCAAgB,CAAC,SAAS,CAAC,CAAC,CAAC,sCAAgB,CAAC,aAAa;YACpG,QAAQ,EAAE,eAAe,IAAI,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;YAC7D,WAAW,EAAE,8CAA8C;SAC5D,CAAC,CAAC;QAEH,yCAAyC;QACzC,MAAM,oBAAoB,GAAG,IAAI,CAAC,0BAA0B,CAAC,mBAAmB,CAAC,CAAC;QAClF,MAAM,CAAC,IAAI,CAAC;YACV,WAAW,EAAE,uBAAuB;YACpC,QAAQ,EAAE,kBAAkB;YAC5B,KAAK,EAAE,+BAA+B;YACtC,MAAM,EAAE,oBAAoB,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,eAAe;YAC5D,MAAM,EAAE,oBAAoB,CAAC,CAAC,CAAC,sCAAgB,CAAC,SAAS,CAAC,CAAC,CAAC,sCAAgB,CAAC,aAAa;YAC1F,QAAQ,EAAE,oBAAoB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;YACnD,WAAW,EAAE,wCAAwC;SACtD,CAAC,CAAC;QAEH,MAAM,eAAe,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,KAAK,sCAAgB,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC;QACnG,MAAM,aAAa,GAAG,eAAe,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC;YACvD,sCAAgB,CAAC,SAAS,CAAC,CAAC;YAC5B,sCAAgB,CAAC,mBAAmB,CAAC;QAEvC,OAAO;YACL,QAAQ,EAAE,2BAA2B;YACrC,aAAa;YACb,oBAAoB,EAAE,CAAC,eAAe,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,GAAG;YAC7D,MAAM;YACN,OAAO,EAAE,GAAG,eAAe,OAAO,MAAM,CAAC,MAAM,iCAAiC;YAChF,cAAc,EAAE,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,MAAM;YACzE,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,KAAK,SAAS,CAAC,CAAC,MAAM;SACtE,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,0BAA0B,CAAC,mBAAwC;QAChF,mDAAmD;QACnD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,KAAK,CAAC,wBAAwB,CAC3C,mBAAwC,EACxC,kBAAsC,EACtC,eAAiC;QAEjC,MAAM,MAAM,GAAuB,EAAE,CAAC;QAEtC,qCAAqC;QACrC,MAAM,cAAc,GAAG,IAAI,CAAC,mBAAmB,CAAC,mBAAmB,CAAC,CAAC;QACrE,MAAM,UAAU,GAAG,EAAE,CAAC,CAAC,4BAA4B;QAEnD,MAAM,CAAC,IAAI,CAAC;YACV,WAAW,EAAE,mBAAmB;YAChC,QAAQ,EAAE,qBAAqB;YAC/B,KAAK,EAAE,GAAG,UAAU,MAAM;YAC1B,MAAM,EAAE,GAAG,cAAc,gBAAgB;YACzC,MAAM,EAAE,cAAc,IAAI,UAAU,CAAC,CAAC,CAAC,sCAAgB,CAAC,SAAS,CAAC,CAAC,CAAC,sCAAgB,CAAC,aAAa;YAClG,QAAQ,EAAE,cAAc,IAAI,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;YAC3D,WAAW,EAAE,kCAAkC;SAChD,CAAC,CAAC;QAEH,wCAAwC;QACxC,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC;QACjE,MAAM,CAAC,IAAI,CAAC;YACV,WAAW,EAAE,qBAAqB;YAClC,QAAQ,EAAE,iBAAiB;YAC3B,KAAK,EAAE,gCAAgC;YACvC,MAAM,EAAE,cAAc,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,eAAe;YACtD,MAAM,EAAE,cAAc,CAAC,CAAC,CAAC,sCAAgB,CAAC,SAAS,CAAC,CAAC,CAAC,sCAAgB,CAAC,aAAa;YACpF,QAAQ,EAAE,cAAc,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO;YAC3C,WAAW,EAAE,+CAA+C;SAC7D,CAAC,CAAC;QAEH,MAAM,eAAe,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,KAAK,sCAAgB,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC;QACnG,MAAM,aAAa,GAAG,eAAe,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC;YACvD,sCAAgB,CAAC,SAAS,CAAC,CAAC;YAC5B,sCAAgB,CAAC,mBAAmB,CAAC;QAEvC,OAAO;YACL,QAAQ,EAAE,qBAAqB;YAC/B,aAAa;YACb,oBAAoB,EAAE,CAAC,eAAe,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,GAAG;YAC7D,MAAM;YACN,OAAO,EAAE,GAAG,eAAe,OAAO,MAAM,CAAC,MAAM,8BAA8B;YAC7E,cAAc,EAAE,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,MAAM;YACzE,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,KAAK,SAAS,CAAC,CAAC,MAAM;SACtE,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,mBAAmB,CAAC,mBAAwC;QACzE,MAAM,aAAa,GAAG,mBAAmB,CAAC,gBAAgB,CAAC,aAAa,CAAC;QACzE,MAAM,cAAc,GAAG,mBAAmB,CAAC,gBAAgB,CAAC,cAAc,CAAC;QAE3E,8BAA8B;QAC9B,MAAM,SAAS,GAAG,EAAE,CAAC,CAAC,WAAW;QACjC,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,oBAAoB;QAC7E,MAAM,aAAa,GAAG,cAAc,GAAG,CAAC,CAAC,CAAC,wBAAwB;QAElE,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,SAAS,GAAG,aAAa,CAAC,CAAC;IAC3D,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,eAAe,CAAC,mBAAwC;QACrE,6DAA6D;QAC7D,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,0BAA0B,CAAC,OAA2B;QACnE,MAAM,SAAS,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAC3D,MAAM,eAAe,GAAG,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,KAAK,sCAAgB,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC;QAEtG,IAAI,aAA+B,CAAC;QACpC,MAAM,oBAAoB,GAAG,CAAC,eAAe,GAAG,SAAS,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC;QAExE,IAAI,oBAAoB,KAAK,GAAG,EAAE,CAAC;YACjC,aAAa,GAAG,sCAAgB,CAAC,SAAS,CAAC;QAC7C,CAAC;aAAM,IAAI,oBAAoB,IAAI,EAAE,EAAE,CAAC;YACtC,aAAa,GAAG,sCAAgB,CAAC,mBAAmB,CAAC;QACvD,CAAC;aAAM,CAAC;YACN,aAAa,GAAG,sCAAgB,CAAC,aAAa,CAAC;QACjD,CAAC;QAED,OAAO;YACL,QAAQ,EAAE,oBAAoB;YAC9B,aAAa;YACb,oBAAoB;YACpB,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,GAAG,eAAe,OAAO,SAAS,CAAC,MAAM,yBAAyB;YAC3E,cAAc,EAAE,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,MAAM;YAC5E,QAAQ,EAAE,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,KAAK,SAAS,CAAC,CAAC,MAAM;SACzE,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,KAAK,CAAC,iCAAiC,CACpD,OAA2B,EAC3B,mBAAwC;QAExC,MAAM,eAAe,GAA+B,EAAE,CAAC;QAEvD,mCAAmC;QACnC,MAAM,kBAAkB,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAClD,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,KAAK,sCAAgB,CAAC,SAAS,CAAC,CAC3E,CAAC;QAEF,kBAAkB,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACjC,IAAI,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC5C,eAAe,CAAC,IAAI,CAAC;oBACnB,EAAE,EAAE,wBAAwB;oBAC5B,QAAQ,EAAE,MAAM;oBAChB,QAAQ,EAAE,mBAAmB;oBAC7B,KAAK,EAAE,8BAA8B;oBACrC,WAAW,EAAE,2HAA2H;oBACxI,iBAAiB,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC;oBACnC,aAAa,EAAE,KAAK;oBACpB,gBAAgB,EAAE,IAAI;oBACtB,kBAAkB,EAAE,WAAW;oBAC/B,OAAO,EAAE;wBACP,gDAAgD;wBAChD,2CAA2C;wBAC3C,iCAAiC;wBACjC,qCAAqC;qBACtC;iBACF,CAAC,CAAC;YACL,CAAC;YAED,IAAI,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC1C,eAAe,CAAC,IAAI,CAAC;oBACnB,EAAE,EAAE,0BAA0B;oBAC9B,QAAQ,EAAE,QAAQ;oBAClB,QAAQ,EAAE,mBAAmB;oBAC7B,KAAK,EAAE,sBAAsB;oBAC7B,WAAW,EAAE,+EAA+E;oBAC5F,iBAAiB,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC;oBACnC,aAAa,EAAE,IAAI;oBACnB,gBAAgB,EAAE,IAAI;oBACtB,kBAAkB,EAAE,WAAW;oBAC/B,OAAO,EAAE;wBACP,8CAA8C;wBAC9C,kCAAkC;wBAClC,8BAA8B;wBAC9B,8CAA8C;qBAC/C;iBACF,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,eAAe,CAAC;IACzB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,KAAK,CAAC,+BAA+B,CAClD,iBAAmC,EACnC,qBAAkD,EAClD,eAAiC;QAEjC,MAAM,YAAY,GAA+B,EAAE,CAAC;QAEpD,kCAAkC;QAClC,IAAI,iBAAiB,CAAC,oBAAoB,IAAI,EAAE,EAAE,CAAC;YACjD,YAAY,CAAC,IAAI,CAAC;gBAChB,aAAa,EAAE,MAAM;gBACrB,KAAK,EAAE,QAAQ;gBACf,MAAM,EAAE,UAAU;gBAClB,eAAe,EAAE;oBACf,0BAA0B;oBAC1B,gCAAgC;oBAChC,4CAA4C;iBAC7C;gBACD,eAAe,EAAE,EAAE;gBACnB,oBAAoB,EAAE,GAAG;aAC1B,CAAC,CAAC;QACL,CAAC;QAED,4BAA4B;QAC5B,IAAI,qBAAqB,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,gBAAgB,IAAI,EAAE,EAAE,CAAC;YACxF,YAAY,CAAC,IAAI,CAAC;gBAChB,aAAa,EAAE,aAAa;gBAC5B,KAAK,EAAE,WAAW;gBAClB,MAAM,EAAE,UAAU;gBAClB,eAAe,EAAE;oBACf,gCAAgC;oBAChC,mCAAmC;oBACnC,oCAAoC;iBACrC;gBACD,eAAe,EAAE,CAAC,EAAE,0BAA0B;gBAC9C,oBAAoB,EAAE,CAAC;aACxB,CAAC,CAAC;QACL,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,kBAAkB,CAAC,QAAgB;QAChD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC1D,OAAO,uBAAuB,QAAQ,IAAI,SAAS,IAAI,MAAM,EAAE,CAAC;IAClE,CAAC;;AAhwBH,4DAiwBC;AAhwByB,gCAAO,GAAG,OAAO,CAAC;AAClB,yCAAgB,GAAG,IAAI,GAAG,EAA8B,CAAC;AAEjF,wBAAwB;AACA,0CAAiB,GAAG;IAC1C,SAAS,EAAE;QACT,WAAW,EAAE,IAAI,EAAE,QAAQ;QAC3B,aAAa,EAAE,IAAI,EAAE,QAAQ;QAC7B,eAAe,EAAE,IAAI,CAAC,QAAQ;KAC/B;IACD,YAAY,EAAE;QACZ,MAAM,EAAE,CAAC,EAAE,sBAAsB;QACjC,MAAM,EAAE,CAAC,EAAE,sBAAsB;QACjC,OAAO,EAAE,CAAC,CAAC,sBAAsB;KAClC;IACD,UAAU,EAAE;QACV,cAAc,EAAE,CAAC,EAAE,UAAU;QAC7B,oBAAoB,EAAE,CAAC,EAAE,UAAU;QACnC,cAAc,EAAE,CAAC,EAAE,UAAU;QAC7B,oBAAoB,EAAE,CAAC,CAAC,UAAU;KACnC;CACF,CAAC;AAEF,mBAAmB;AACK,sCAAa,GAAG;IACtC,iBAAiB,EAAE;QACjB,YAAY,EAAE;YACZ,WAAW,EAAE,EAAE,EAAE,WAAW;YAC5B,KAAK,EAAE,EAAE,CAAC,WAAW;SACtB;QACD,aAAa,EAAE;YACb,mBAAmB,EAAE,EAAE,EAAE,SAAS;YAClC,aAAa,EAAE,EAAE,CAAC,SAAS;SAC5B;KACF;IACD,aAAa,EAAE;QACb,OAAO,EAAE,CAAC,EAAE,0BAA0B;QACtC,OAAO,EAAE,CAAC;QACV,OAAO,EAAE,EAAE;QACX,OAAO,EAAE,EAAE;KACZ;CACF,CAAC;AAEF,qBAAqB;AACG,2CAAkB,GAAG;IAC3C,IAAI,EAAE;QACJ,SAAS,EAAE,IAAI,EAAE,QAAQ;QACzB,YAAY,EAAE,UAAU;QACxB,gBAAgB,EAAE;YAChB,QAAQ,EAAE,CAAC,EAAE,UAAU;YACvB,QAAQ,EAAE,CAAC;YACX,QAAQ,EAAE,CAAC;YACX,QAAQ,EAAE,CAAC;SACZ;KACF;IACD,QAAQ,EAAE;QACR,SAAS,EAAE,GAAG,EAAE,qBAAqB;QACrC,YAAY,EAAE,CAAC,EAAE,sBAAsB;QACvC,UAAU,EAAE,CAAC,CAAC,UAAU;KACzB;CACF,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\ComplianceCheckingEngine.ts"],
      sourcesContent: ["/**\r\n * Compliance Checking and Validation Engine\r\n * \r\n * Comprehensive compliance checking service for Phase 3 Priority 3: Advanced System Analysis Tools\r\n * Provides SMACNA, ASHRAE, and local code compliance checking with automated validation,\r\n * reporting, and certification support for HVAC duct systems.\r\n * \r\n * @version 3.0.0\r\n * <AUTHOR> Suite Development Team\r\n */\r\n\r\nimport {\r\n  ComplianceAnalysis,\r\n  ComplianceStandard,\r\n  ComplianceResult,\r\n  ComplianceStatus,\r\n  ComplianceRecommendation,\r\n  ValidationResult,\r\n  CertificationRequirement,\r\n  SystemConfiguration,\r\n  PerformanceMetrics,\r\n  EnergyAnalysis,\r\n  EnvironmentalImpactAnalysis\r\n} from './types/SystemAnalysisTypes';\r\n\r\n/**\r\n * Compliance Checking and Validation Engine\r\n * \r\n * Provides comprehensive compliance checking capabilities including:\r\n * - SMACNA standard compliance checking\r\n * - ASHRAE standard compliance validation\r\n * - Local building code compliance\r\n * - Energy code compliance (IECC, Title 24, etc.)\r\n * - Environmental regulation compliance\r\n * - Automated validation and reporting\r\n * - Certification support and documentation\r\n */\r\nexport class ComplianceCheckingEngine {\r\n  private static readonly VERSION = '3.0.0';\r\n  private static readonly COMPLIANCE_CACHE = new Map<string, ComplianceAnalysis>();\r\n  \r\n  // ASHRAE 90.1 Standards\r\n  private static readonly ASHRAE_901_LIMITS = {\r\n    FAN_POWER: {\r\n      SUPPLY_ONLY: 1.25, // W/CFM\r\n      SUPPLY_RETURN: 1.25, // W/CFM\r\n      COMPLEX_SYSTEMS: 1.25 // W/CFM\r\n    },\r\n    DUCT_LEAKAGE: {\r\n      SUPPLY: 4, // % of design airflow\r\n      RETURN: 3, // % of design airflow\r\n      EXHAUST: 4 // % of design airflow\r\n    },\r\n    INSULATION: {\r\n      SUPPLY_OUTDOOR: 8, // R-value\r\n      SUPPLY_UNCONDITIONED: 6, // R-value\r\n      RETURN_OUTDOOR: 6, // R-value\r\n      RETURN_UNCONDITIONED: 4 // R-value\r\n    }\r\n  };\r\n\r\n  // SMACNA Standards\r\n  private static readonly SMACNA_LIMITS = {\r\n    DUCT_CONSTRUCTION: {\r\n      MAX_PRESSURE: {\r\n        RECTANGULAR: 10, // in. w.g.\r\n        ROUND: 20 // in. w.g.\r\n      },\r\n      REINFORCEMENT: {\r\n        RECTANGULAR_SPACING: 48, // inches\r\n        ROUND_SPACING: 60 // inches\r\n      }\r\n    },\r\n    LEAKAGE_CLASS: {\r\n      CLASS_1: 4, // CFM/100 sq ft @ 1\" w.g.\r\n      CLASS_2: 6,\r\n      CLASS_3: 12,\r\n      CLASS_6: 30\r\n    }\r\n  };\r\n\r\n  // Energy Code Limits\r\n  private static readonly ENERGY_CODE_LIMITS = {\r\n    IECC: {\r\n      FAN_POWER: 1.25, // W/CFM\r\n      DUCT_SEALING: 'Required',\r\n      INSULATION_ZONES: {\r\n        ZONE_1_2: 4, // R-value\r\n        ZONE_3_4: 6,\r\n        ZONE_5_6: 8,\r\n        ZONE_7_8: 8\r\n      }\r\n    },\r\n    TITLE_24: {\r\n      FAN_POWER: 1.0, // W/CFM (California)\r\n      DUCT_LEAKAGE: 6, // % of design airflow\r\n      INSULATION: 8 // R-value\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Perform comprehensive compliance analysis\r\n   */\r\n  public static async performComplianceAnalysis(\r\n    systemConfiguration: SystemConfiguration,\r\n    performanceMetrics: PerformanceMetrics,\r\n    energyAnalysis: EnergyAnalysis,\r\n    environmentalAnalysis: EnvironmentalImpactAnalysis,\r\n    applicableCodes?: string[],\r\n    projectLocation?: ProjectLocation\r\n  ): Promise<ComplianceAnalysis> {\r\n    try {\r\n      const analysisId = this.generateAnalysisId(systemConfiguration.id);\r\n      const timestamp = new Date();\r\n\r\n      // Determine applicable standards\r\n      const applicableStandards = this.determineApplicableStandards(applicableCodes, projectLocation);\r\n\r\n      // Check ASHRAE compliance\r\n      const ashraeCompliance = await this.checkASHRAECompliance(\r\n        systemConfiguration,\r\n        performanceMetrics,\r\n        energyAnalysis\r\n      );\r\n\r\n      // Check SMACNA compliance\r\n      const smacnaCompliance = await this.checkSMACNACompliance(\r\n        systemConfiguration,\r\n        performanceMetrics\r\n      );\r\n\r\n      // Check energy code compliance\r\n      const energyCodeCompliance = await this.checkEnergyCodeCompliance(\r\n        systemConfiguration,\r\n        energyAnalysis,\r\n        projectLocation\r\n      );\r\n\r\n      // Check environmental compliance\r\n      const environmentalCompliance = await this.checkEnvironmentalCompliance(\r\n        systemConfiguration,\r\n        environmentalAnalysis,\r\n        projectLocation\r\n      );\r\n\r\n      // Check local code compliance\r\n      const localCodeCompliance = await this.checkLocalCodeCompliance(\r\n        systemConfiguration,\r\n        performanceMetrics,\r\n        projectLocation\r\n      );\r\n\r\n      // Aggregate compliance results\r\n      const overallCompliance = this.aggregateComplianceResults([\r\n        ashraeCompliance,\r\n        smacnaCompliance,\r\n        energyCodeCompliance,\r\n        environmentalCompliance,\r\n        localCodeCompliance\r\n      ]);\r\n\r\n      // Generate compliance recommendations\r\n      const recommendations = await this.generateComplianceRecommendations(\r\n        [ashraeCompliance, smacnaCompliance, energyCodeCompliance, environmentalCompliance, localCodeCompliance],\r\n        systemConfiguration\r\n      );\r\n\r\n      // Assess certification requirements\r\n      const certificationRequirements = await this.assessCertificationRequirements(\r\n        overallCompliance,\r\n        environmentalAnalysis,\r\n        projectLocation\r\n      );\r\n\r\n      const analysis: ComplianceAnalysis = {\r\n        id: analysisId,\r\n        systemId: systemConfiguration.id,\r\n        analysisTimestamp: timestamp,\r\n        applicableStandards,\r\n        ashraeCompliance,\r\n        smacnaCompliance,\r\n        energyCodeCompliance,\r\n        environmentalCompliance,\r\n        localCodeCompliance,\r\n        overallCompliance,\r\n        recommendations,\r\n        certificationRequirements\r\n      };\r\n\r\n      // Cache the analysis\r\n      this.COMPLIANCE_CACHE.set(analysisId, analysis);\r\n\r\n      return analysis;\r\n\r\n    } catch (error) {\r\n      throw new Error(`Compliance analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Determine applicable standards based on location and project type\r\n   */\r\n  private static determineApplicableStandards(\r\n    applicableCodes?: string[],\r\n    projectLocation?: ProjectLocation\r\n  ): ComplianceStandard[] {\r\n    const standards: ComplianceStandard[] = [];\r\n\r\n    // Always applicable standards\r\n    standards.push({\r\n      name: 'ASHRAE 90.1',\r\n      version: '2019',\r\n      scope: 'Energy Efficiency',\r\n      mandatory: true,\r\n      description: 'Energy Standard for Buildings Except Low-Rise Residential Buildings'\r\n    });\r\n\r\n    standards.push({\r\n      name: 'SMACNA',\r\n      version: '2006',\r\n      scope: 'Duct Construction',\r\n      mandatory: true,\r\n      description: 'HVAC Duct Construction Standards'\r\n    });\r\n\r\n    // Location-specific standards\r\n    if (projectLocation) {\r\n      if (projectLocation.state === 'CA') {\r\n        standards.push({\r\n          name: 'Title 24',\r\n          version: '2022',\r\n          scope: 'Energy Efficiency',\r\n          mandatory: true,\r\n          description: 'California Building Energy Efficiency Standards'\r\n        });\r\n      }\r\n\r\n      // Add IECC for most US locations\r\n      if (projectLocation.country === 'US') {\r\n        standards.push({\r\n          name: 'IECC',\r\n          version: '2021',\r\n          scope: 'Energy Code',\r\n          mandatory: true,\r\n          description: 'International Energy Conservation Code'\r\n        });\r\n      }\r\n    }\r\n\r\n    // Add any explicitly specified codes\r\n    if (applicableCodes) {\r\n      applicableCodes.forEach(code => {\r\n        if (!standards.find(s => s.name === code)) {\r\n          standards.push({\r\n            name: code,\r\n            version: 'Current',\r\n            scope: 'Local Code',\r\n            mandatory: true,\r\n            description: `Local building code: ${code}`\r\n          });\r\n        }\r\n      });\r\n    }\r\n\r\n    return standards;\r\n  }\r\n\r\n  /**\r\n   * Check ASHRAE 90.1 compliance\r\n   */\r\n  private static async checkASHRAECompliance(\r\n    systemConfiguration: SystemConfiguration,\r\n    performanceMetrics: PerformanceMetrics,\r\n    energyAnalysis: EnergyAnalysis\r\n  ): Promise<ComplianceResult> {\r\n    const checks: ValidationResult[] = [];\r\n\r\n    // Fan power compliance check\r\n    const fanPowerLimit = this.ASHRAE_901_LIMITS.FAN_POWER.SUPPLY_ONLY;\r\n    const actualFanPower = energyAnalysis.efficiencyMetrics.specificFanPower;\r\n    \r\n    checks.push({\r\n      requirement: 'Fan Power Limitation',\r\n      standard: 'ASHRAE 90.1-2019 Section *******',\r\n      limit: `${fanPowerLimit} W/CFM`,\r\n      actual: `${actualFanPower.toFixed(2)} W/CFM`,\r\n      status: actualFanPower <= fanPowerLimit ? ComplianceStatus.COMPLIANT : ComplianceStatus.NON_COMPLIANT,\r\n      severity: actualFanPower <= fanPowerLimit ? 'info' : 'error',\r\n      description: 'Maximum allowable fan power for supply-only systems'\r\n    });\r\n\r\n    // Duct insulation check (simplified)\r\n    const hasAdequateInsulation = this.checkDuctInsulation(systemConfiguration);\r\n    checks.push({\r\n      requirement: 'Duct Insulation',\r\n      standard: 'ASHRAE 90.1-2019 Section 6.4.4',\r\n      limit: 'R-6 minimum for supply ducts in unconditioned spaces',\r\n      actual: hasAdequateInsulation ? 'Adequate' : 'Inadequate',\r\n      status: hasAdequateInsulation ? ComplianceStatus.COMPLIANT : ComplianceStatus.NON_COMPLIANT,\r\n      severity: hasAdequateInsulation ? 'info' : 'warning',\r\n      description: 'Minimum insulation requirements for ductwork'\r\n    });\r\n\r\n    // Duct leakage check (simplified)\r\n    const estimatedLeakage = this.estimateDuctLeakage(systemConfiguration);\r\n    const leakageLimit = this.ASHRAE_901_LIMITS.DUCT_LEAKAGE.SUPPLY;\r\n    \r\n    checks.push({\r\n      requirement: 'Duct Leakage',\r\n      standard: 'ASHRAE 90.1-2019 Section *******',\r\n      limit: `${leakageLimit}% of design airflow`,\r\n      actual: `${estimatedLeakage.toFixed(1)}% estimated`,\r\n      status: estimatedLeakage <= leakageLimit ? ComplianceStatus.COMPLIANT : ComplianceStatus.NON_COMPLIANT,\r\n      severity: estimatedLeakage <= leakageLimit ? 'info' : 'error',\r\n      description: 'Maximum allowable duct leakage for supply systems'\r\n    });\r\n\r\n    // Calculate overall compliance\r\n    const compliantChecks = checks.filter(check => check.status === ComplianceStatus.COMPLIANT).length;\r\n    const overallStatus = compliantChecks === checks.length ? \r\n      ComplianceStatus.COMPLIANT : \r\n      compliantChecks > checks.length / 2 ? \r\n        ComplianceStatus.PARTIALLY_COMPLIANT : \r\n        ComplianceStatus.NON_COMPLIANT;\r\n\r\n    return {\r\n      standard: 'ASHRAE 90.1-2019',\r\n      overallStatus,\r\n      compliancePercentage: (compliantChecks / checks.length) * 100,\r\n      checks,\r\n      summary: `${compliantChecks} of ${checks.length} requirements met`,\r\n      criticalIssues: checks.filter(check => check.severity === 'error').length,\r\n      warnings: checks.filter(check => check.severity === 'warning').length\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Check duct insulation adequacy (simplified)\r\n   */\r\n  private static checkDuctInsulation(systemConfiguration: SystemConfiguration): boolean {\r\n    // Simplified check - assume adequate insulation for now\r\n    // In a real implementation, this would check actual insulation specifications\r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Estimate duct leakage (simplified)\r\n   */\r\n  private static estimateDuctLeakage(systemConfiguration: SystemConfiguration): number {\r\n    // Simplified leakage estimation based on system pressure\r\n    const systemPressure = systemConfiguration.designParameters.designPressure;\r\n    \r\n    // Higher pressure systems tend to have more leakage\r\n    if (systemPressure > 4.0) return 5.5; // Higher leakage\r\n    if (systemPressure > 2.0) return 3.5; // Moderate leakage\r\n    return 2.5; // Lower leakage\r\n  }\r\n\r\n  /**\r\n   * Check SMACNA compliance\r\n   */\r\n  private static async checkSMACNACompliance(\r\n    systemConfiguration: SystemConfiguration,\r\n    performanceMetrics: PerformanceMetrics\r\n  ): Promise<ComplianceResult> {\r\n    const checks: ValidationResult[] = [];\r\n\r\n    // Duct construction pressure limits\r\n    const systemPressure = systemConfiguration.designParameters.designPressure;\r\n    const ductShape = systemConfiguration.ductConfiguration?.shape || 'rectangular';\r\n    const pressureLimit = ductShape === 'round' ?\r\n      this.SMACNA_LIMITS.DUCT_CONSTRUCTION.MAX_PRESSURE.ROUND :\r\n      this.SMACNA_LIMITS.DUCT_CONSTRUCTION.MAX_PRESSURE.RECTANGULAR;\r\n\r\n    checks.push({\r\n      requirement: 'Duct Construction Pressure Limit',\r\n      standard: 'SMACNA HVAC Duct Construction Standards',\r\n      limit: `${pressureLimit} in. w.g. for ${ductShape} ducts`,\r\n      actual: `${systemPressure.toFixed(1)} in. w.g.`,\r\n      status: systemPressure <= pressureLimit ? ComplianceStatus.COMPLIANT : ComplianceStatus.NON_COMPLIANT,\r\n      severity: systemPressure <= pressureLimit ? 'info' : 'error',\r\n      description: 'Maximum allowable static pressure for duct construction class'\r\n    });\r\n\r\n    // Leakage class requirements\r\n    const estimatedLeakage = this.estimateDuctLeakage(systemConfiguration);\r\n    const leakageClass = this.determineSMACNALeakageClass(systemPressure);\r\n    const leakageLimit = this.SMACNA_LIMITS.LEAKAGE_CLASS[leakageClass as keyof typeof this.SMACNA_LIMITS.LEAKAGE_CLASS];\r\n\r\n    checks.push({\r\n      requirement: `SMACNA Leakage ${leakageClass}`,\r\n      standard: 'SMACNA HVAC Duct Construction Standards',\r\n      limit: `${leakageLimit} CFM/100 sq ft @ 1\" w.g.`,\r\n      actual: `${(estimatedLeakage * 2).toFixed(1)} CFM/100 sq ft estimated`,\r\n      status: (estimatedLeakage * 2) <= leakageLimit ? ComplianceStatus.COMPLIANT : ComplianceStatus.NON_COMPLIANT,\r\n      severity: (estimatedLeakage * 2) <= leakageLimit ? 'info' : 'warning',\r\n      description: 'SMACNA duct leakage classification requirements'\r\n    });\r\n\r\n    // Reinforcement requirements (simplified)\r\n    const hasAdequateReinforcement = this.checkDuctReinforcement(systemConfiguration);\r\n    checks.push({\r\n      requirement: 'Duct Reinforcement',\r\n      standard: 'SMACNA HVAC Duct Construction Standards',\r\n      limit: 'Adequate reinforcement per SMACNA standards',\r\n      actual: hasAdequateReinforcement ? 'Adequate' : 'Inadequate',\r\n      status: hasAdequateReinforcement ? ComplianceStatus.COMPLIANT : ComplianceStatus.NON_COMPLIANT,\r\n      severity: hasAdequateReinforcement ? 'info' : 'warning',\r\n      description: 'Duct reinforcement requirements for system pressure class'\r\n    });\r\n\r\n    const compliantChecks = checks.filter(check => check.status === ComplianceStatus.COMPLIANT).length;\r\n\r\n    let overallStatus: ComplianceStatus;\r\n    if (compliantChecks === checks.length) {\r\n      overallStatus = ComplianceStatus.COMPLIANT;\r\n    } else if (compliantChecks > checks.length / 2) {\r\n      overallStatus = ComplianceStatus.PARTIALLY_COMPLIANT;\r\n    } else {\r\n      overallStatus = ComplianceStatus.NON_COMPLIANT;\r\n    }\r\n\r\n    return {\r\n      standard: 'SMACNA HVAC Duct Construction',\r\n      overallStatus,\r\n      compliancePercentage: (compliantChecks / checks.length) * 100,\r\n      checks,\r\n      summary: `${compliantChecks} of ${checks.length} SMACNA requirements met`,\r\n      criticalIssues: checks.filter(check => check.severity === 'error').length,\r\n      warnings: checks.filter(check => check.severity === 'warning').length\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Determine SMACNA leakage class based on system pressure\r\n   */\r\n  private static determineSMACNALeakageClass(pressure: number): string {\r\n    if (pressure <= 2.0) return 'CLASS_3';\r\n    if (pressure <= 4.0) return 'CLASS_2';\r\n    if (pressure <= 6.0) return 'CLASS_1';\r\n    return 'CLASS_1'; // Highest class for high pressure\r\n  }\r\n\r\n  /**\r\n   * Check duct reinforcement adequacy (simplified)\r\n   */\r\n  private static checkDuctReinforcement(systemConfiguration: SystemConfiguration): boolean {\r\n    const systemPressure = systemConfiguration.designParameters.designPressure;\r\n    // Simplified check - assume adequate reinforcement for pressures under 6\" w.g.\r\n    return systemPressure <= 6.0;\r\n  }\r\n\r\n  /**\r\n   * Check energy code compliance\r\n   */\r\n  private static async checkEnergyCodeCompliance(\r\n    systemConfiguration: SystemConfiguration,\r\n    energyAnalysis: EnergyAnalysis,\r\n    projectLocation?: ProjectLocation\r\n  ): Promise<ComplianceResult> {\r\n    const checks: ValidationResult[] = [];\r\n\r\n    // Determine applicable energy code\r\n    const energyCode = this.determineEnergyCode(projectLocation);\r\n    const fanPowerLimit = this.getEnergyCodeFanPowerLimit(energyCode, projectLocation);\r\n    const actualFanPower = energyAnalysis.efficiencyMetrics.specificFanPower;\r\n\r\n    checks.push({\r\n      requirement: 'Energy Code Fan Power Limit',\r\n      standard: `${energyCode} Fan Power Requirements`,\r\n      limit: `${fanPowerLimit} W/CFM`,\r\n      actual: `${actualFanPower.toFixed(2)} W/CFM`,\r\n      status: actualFanPower <= fanPowerLimit ? ComplianceStatus.COMPLIANT : ComplianceStatus.NON_COMPLIANT,\r\n      severity: actualFanPower <= fanPowerLimit ? 'info' : 'error',\r\n      description: `${energyCode} maximum allowable fan power`\r\n    });\r\n\r\n    // Duct sealing requirements\r\n    const hasDuctSealing = this.checkDuctSealing(systemConfiguration);\r\n    checks.push({\r\n      requirement: 'Duct Sealing',\r\n      standard: `${energyCode} Duct Sealing Requirements`,\r\n      limit: 'Duct sealing required per code',\r\n      actual: hasDuctSealing ? 'Compliant' : 'Non-compliant',\r\n      status: hasDuctSealing ? ComplianceStatus.COMPLIANT : ComplianceStatus.NON_COMPLIANT,\r\n      severity: hasDuctSealing ? 'info' : 'error',\r\n      description: 'Energy code duct sealing requirements'\r\n    });\r\n\r\n    const compliantChecks = checks.filter(check => check.status === ComplianceStatus.COMPLIANT).length;\r\n    const overallStatus = compliantChecks === checks.length ?\r\n      ComplianceStatus.COMPLIANT :\r\n      ComplianceStatus.NON_COMPLIANT;\r\n\r\n    return {\r\n      standard: energyCode,\r\n      overallStatus,\r\n      compliancePercentage: (compliantChecks / checks.length) * 100,\r\n      checks,\r\n      summary: `${compliantChecks} of ${checks.length} ${energyCode} requirements met`,\r\n      criticalIssues: checks.filter(check => check.severity === 'error').length,\r\n      warnings: checks.filter(check => check.severity === 'warning').length\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Determine applicable energy code\r\n   */\r\n  private static determineEnergyCode(projectLocation?: ProjectLocation): string {\r\n    if (projectLocation?.state === 'CA') return 'Title 24';\r\n    return 'IECC 2021';\r\n  }\r\n\r\n  /**\r\n   * Get energy code fan power limit\r\n   */\r\n  private static getEnergyCodeFanPowerLimit(energyCode: string, projectLocation?: ProjectLocation): number {\r\n    if (energyCode === 'Title 24') return this.ENERGY_CODE_LIMITS.TITLE_24.FAN_POWER;\r\n    return this.ENERGY_CODE_LIMITS.IECC.FAN_POWER;\r\n  }\r\n\r\n  /**\r\n   * Check duct sealing compliance (simplified)\r\n   */\r\n  private static checkDuctSealing(systemConfiguration: SystemConfiguration): boolean {\r\n    // Simplified check - assume duct sealing is specified\r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Check environmental compliance\r\n   */\r\n  private static async checkEnvironmentalCompliance(\r\n    systemConfiguration: SystemConfiguration,\r\n    environmentalAnalysis: EnvironmentalImpactAnalysis,\r\n    projectLocation?: ProjectLocation\r\n  ): Promise<ComplianceResult> {\r\n    const checks: ValidationResult[] = [];\r\n\r\n    // Carbon emissions compliance (if applicable)\r\n    const carbonIntensity = environmentalAnalysis.carbonFootprint.totalEmissions.value /\r\n                           systemConfiguration.designParameters.designAirflow;\r\n    const carbonLimit = 0.15; // kg CO2e/CFM/year (example limit)\r\n\r\n    checks.push({\r\n      requirement: 'Carbon Emissions Limit',\r\n      standard: 'Environmental Regulations',\r\n      limit: `${carbonLimit} kg CO2e/CFM/year`,\r\n      actual: `${carbonIntensity.toFixed(3)} kg CO2e/CFM/year`,\r\n      status: carbonIntensity <= carbonLimit ? ComplianceStatus.COMPLIANT : ComplianceStatus.NON_COMPLIANT,\r\n      severity: carbonIntensity <= carbonLimit ? 'info' : 'warning',\r\n      description: 'Maximum allowable carbon emissions intensity'\r\n    });\r\n\r\n    // Refrigerant compliance (if applicable)\r\n    const hasLowGWPRefrigerant = this.checkRefrigerantCompliance(systemConfiguration);\r\n    checks.push({\r\n      requirement: 'Refrigerant GWP Limit',\r\n      standard: 'EPA SNAP Program',\r\n      limit: 'Low GWP refrigerants required',\r\n      actual: hasLowGWPRefrigerant ? 'Compliant' : 'Non-compliant',\r\n      status: hasLowGWPRefrigerant ? ComplianceStatus.COMPLIANT : ComplianceStatus.NON_COMPLIANT,\r\n      severity: hasLowGWPRefrigerant ? 'info' : 'warning',\r\n      description: 'EPA refrigerant regulations compliance'\r\n    });\r\n\r\n    const compliantChecks = checks.filter(check => check.status === ComplianceStatus.COMPLIANT).length;\r\n    const overallStatus = compliantChecks === checks.length ?\r\n      ComplianceStatus.COMPLIANT :\r\n      ComplianceStatus.PARTIALLY_COMPLIANT;\r\n\r\n    return {\r\n      standard: 'Environmental Regulations',\r\n      overallStatus,\r\n      compliancePercentage: (compliantChecks / checks.length) * 100,\r\n      checks,\r\n      summary: `${compliantChecks} of ${checks.length} environmental requirements met`,\r\n      criticalIssues: checks.filter(check => check.severity === 'error').length,\r\n      warnings: checks.filter(check => check.severity === 'warning').length\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Check refrigerant compliance (simplified)\r\n   */\r\n  private static checkRefrigerantCompliance(systemConfiguration: SystemConfiguration): boolean {\r\n    // Simplified check - assume compliant refrigerants\r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Check local code compliance\r\n   */\r\n  private static async checkLocalCodeCompliance(\r\n    systemConfiguration: SystemConfiguration,\r\n    performanceMetrics: PerformanceMetrics,\r\n    projectLocation?: ProjectLocation\r\n  ): Promise<ComplianceResult> {\r\n    const checks: ValidationResult[] = [];\r\n\r\n    // Local noise requirements (example)\r\n    const estimatedNoise = this.estimateSystemNoise(systemConfiguration);\r\n    const noiseLimit = 55; // dBA (example local limit)\r\n\r\n    checks.push({\r\n      requirement: 'Noise Level Limit',\r\n      standard: 'Local Building Code',\r\n      limit: `${noiseLimit} dBA`,\r\n      actual: `${estimatedNoise} dBA estimated`,\r\n      status: estimatedNoise <= noiseLimit ? ComplianceStatus.COMPLIANT : ComplianceStatus.NON_COMPLIANT,\r\n      severity: estimatedNoise <= noiseLimit ? 'info' : 'warning',\r\n      description: 'Local noise ordinance compliance'\r\n    });\r\n\r\n    // Fire safety requirements (simplified)\r\n    const hasFireDampers = this.checkFireSafety(systemConfiguration);\r\n    checks.push({\r\n      requirement: 'Fire Safety Systems',\r\n      standard: 'Local Fire Code',\r\n      limit: 'Fire dampers required per code',\r\n      actual: hasFireDampers ? 'Compliant' : 'Non-compliant',\r\n      status: hasFireDampers ? ComplianceStatus.COMPLIANT : ComplianceStatus.NON_COMPLIANT,\r\n      severity: hasFireDampers ? 'info' : 'error',\r\n      description: 'Local fire code requirements for HVAC systems'\r\n    });\r\n\r\n    const compliantChecks = checks.filter(check => check.status === ComplianceStatus.COMPLIANT).length;\r\n    const overallStatus = compliantChecks === checks.length ?\r\n      ComplianceStatus.COMPLIANT :\r\n      ComplianceStatus.PARTIALLY_COMPLIANT;\r\n\r\n    return {\r\n      standard: 'Local Building Code',\r\n      overallStatus,\r\n      compliancePercentage: (compliantChecks / checks.length) * 100,\r\n      checks,\r\n      summary: `${compliantChecks} of ${checks.length} local code requirements met`,\r\n      criticalIssues: checks.filter(check => check.severity === 'error').length,\r\n      warnings: checks.filter(check => check.severity === 'warning').length\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Estimate system noise (simplified)\r\n   */\r\n  private static estimateSystemNoise(systemConfiguration: SystemConfiguration): number {\r\n    const designAirflow = systemConfiguration.designParameters.designAirflow;\r\n    const systemPressure = systemConfiguration.designParameters.designPressure;\r\n\r\n    // Simplified noise estimation\r\n    const baseNoise = 45; // dBA base\r\n    const flowNoise = Math.log10(designAirflow / 1000) * 10; // Flow contribution\r\n    const pressureNoise = systemPressure * 2; // Pressure contribution\r\n\r\n    return Math.round(baseNoise + flowNoise + pressureNoise);\r\n  }\r\n\r\n  /**\r\n   * Check fire safety compliance (simplified)\r\n   */\r\n  private static checkFireSafety(systemConfiguration: SystemConfiguration): boolean {\r\n    // Simplified check - assume fire safety systems are included\r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Aggregate compliance results\r\n   */\r\n  private static aggregateComplianceResults(results: ComplianceResult[]): ComplianceResult {\r\n    const allChecks = results.flatMap(result => result.checks);\r\n    const compliantChecks = allChecks.filter(check => check.status === ComplianceStatus.COMPLIANT).length;\r\n\r\n    let overallStatus: ComplianceStatus;\r\n    const compliancePercentage = (compliantChecks / allChecks.length) * 100;\r\n\r\n    if (compliancePercentage === 100) {\r\n      overallStatus = ComplianceStatus.COMPLIANT;\r\n    } else if (compliancePercentage >= 80) {\r\n      overallStatus = ComplianceStatus.PARTIALLY_COMPLIANT;\r\n    } else {\r\n      overallStatus = ComplianceStatus.NON_COMPLIANT;\r\n    }\r\n\r\n    return {\r\n      standard: 'Overall Compliance',\r\n      overallStatus,\r\n      compliancePercentage,\r\n      checks: allChecks,\r\n      summary: `${compliantChecks} of ${allChecks.length} total requirements met`,\r\n      criticalIssues: allChecks.filter(check => check.severity === 'error').length,\r\n      warnings: allChecks.filter(check => check.severity === 'warning').length\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Generate compliance recommendations\r\n   */\r\n  private static async generateComplianceRecommendations(\r\n    results: ComplianceResult[],\r\n    systemConfiguration: SystemConfiguration\r\n  ): Promise<ComplianceRecommendation[]> {\r\n    const recommendations: ComplianceRecommendation[] = [];\r\n\r\n    // Analyze all non-compliant checks\r\n    const nonCompliantChecks = results.flatMap(result =>\r\n      result.checks.filter(check => check.status !== ComplianceStatus.COMPLIANT)\r\n    );\r\n\r\n    nonCompliantChecks.forEach(check => {\r\n      if (check.requirement.includes('Fan Power')) {\r\n        recommendations.push({\r\n          id: 'fan_power_optimization',\r\n          priority: 'High',\r\n          category: 'Energy Efficiency',\r\n          title: 'Reduce Fan Power Consumption',\r\n          description: 'System exceeds maximum allowable fan power. Consider optimizing ductwork design or upgrading to more efficient equipment.',\r\n          affectedStandards: [check.standard],\r\n          estimatedCost: 15000,\r\n          estimatedSavings: 5000,\r\n          implementationTime: '2-4 weeks',\r\n          actions: [\r\n            'Optimize duct sizing to reduce pressure losses',\r\n            'Consider variable frequency drives (VFDs)',\r\n            'Upgrade to high-efficiency fans',\r\n            'Review system design for oversizing'\r\n          ]\r\n        });\r\n      }\r\n\r\n      if (check.requirement.includes('Leakage')) {\r\n        recommendations.push({\r\n          id: 'duct_sealing_improvement',\r\n          priority: 'Medium',\r\n          category: 'Duct Construction',\r\n          title: 'Improve Duct Sealing',\r\n          description: 'Duct leakage exceeds allowable limits. Enhanced sealing methods are required.',\r\n          affectedStandards: [check.standard],\r\n          estimatedCost: 8000,\r\n          estimatedSavings: 3000,\r\n          implementationTime: '1-2 weeks',\r\n          actions: [\r\n            'Implement comprehensive duct sealing program',\r\n            'Use mastic sealant at all joints',\r\n            'Perform duct blaster testing',\r\n            'Upgrade to higher leakage class construction'\r\n          ]\r\n        });\r\n      }\r\n    });\r\n\r\n    return recommendations;\r\n  }\r\n\r\n  /**\r\n   * Assess certification requirements\r\n   */\r\n  private static async assessCertificationRequirements(\r\n    overallCompliance: ComplianceResult,\r\n    environmentalAnalysis: EnvironmentalImpactAnalysis,\r\n    projectLocation?: ProjectLocation\r\n  ): Promise<CertificationRequirement[]> {\r\n    const requirements: CertificationRequirement[] = [];\r\n\r\n    // LEED certification requirements\r\n    if (overallCompliance.compliancePercentage >= 80) {\r\n      requirements.push({\r\n        certification: 'LEED',\r\n        level: 'Silver',\r\n        status: 'Eligible',\r\n        requiredActions: [\r\n          'Complete energy modeling',\r\n          'Document commissioning process',\r\n          'Provide equipment efficiency documentation'\r\n        ],\r\n        estimatedPoints: 12,\r\n        totalPointsAvailable: 110\r\n      });\r\n    }\r\n\r\n    // ENERGY STAR certification\r\n    if (environmentalAnalysis.sustainabilityMetrics.energyEfficiency.systemEfficiency >= 85) {\r\n      requirements.push({\r\n        certification: 'ENERGY STAR',\r\n        level: 'Certified',\r\n        status: 'Eligible',\r\n        requiredActions: [\r\n          'Submit energy performance data',\r\n          'Complete third-party verification',\r\n          'Maintain performance for 12 months'\r\n        ],\r\n        estimatedPoints: 0, // Pass/fail certification\r\n        totalPointsAvailable: 0\r\n      });\r\n    }\r\n\r\n    return requirements;\r\n  }\r\n\r\n  /**\r\n   * Generate unique analysis ID\r\n   */\r\n  private static generateAnalysisId(systemId: string): string {\r\n    const timestamp = Date.now();\r\n    const random = Math.random().toString(36).substring(2, 8);\r\n    return `compliance_analysis_${systemId}_${timestamp}_${random}`;\r\n  }\r\n}\r\n\r\n// Supporting interfaces\r\ninterface ProjectLocation {\r\n  country: string;\r\n  state: string;\r\n  city: string;\r\n  climateZone: string;\r\n  jurisdiction: string;\r\n}\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "342e9922fc081991a44e69c14606d1ed1ce1f541"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_is4rpdbyg = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_is4rpdbyg();
cov_is4rpdbyg().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_is4rpdbyg().s[1]++;
exports.ComplianceCheckingEngine = void 0;
const SystemAnalysisTypes_1 =
/* istanbul ignore next */
(cov_is4rpdbyg().s[2]++, require("./types/SystemAnalysisTypes"));
/**
 * Compliance Checking and Validation Engine
 *
 * Provides comprehensive compliance checking capabilities including:
 * - SMACNA standard compliance checking
 * - ASHRAE standard compliance validation
 * - Local building code compliance
 * - Energy code compliance (IECC, Title 24, etc.)
 * - Environmental regulation compliance
 * - Automated validation and reporting
 * - Certification support and documentation
 */
class ComplianceCheckingEngine {
  /**
   * Perform comprehensive compliance analysis
   */
  static async performComplianceAnalysis(systemConfiguration, performanceMetrics, energyAnalysis, environmentalAnalysis, applicableCodes, projectLocation) {
    /* istanbul ignore next */
    cov_is4rpdbyg().f[0]++;
    cov_is4rpdbyg().s[3]++;
    try {
      const analysisId =
      /* istanbul ignore next */
      (cov_is4rpdbyg().s[4]++, this.generateAnalysisId(systemConfiguration.id));
      const timestamp =
      /* istanbul ignore next */
      (cov_is4rpdbyg().s[5]++, new Date());
      // Determine applicable standards
      const applicableStandards =
      /* istanbul ignore next */
      (cov_is4rpdbyg().s[6]++, this.determineApplicableStandards(applicableCodes, projectLocation));
      // Check ASHRAE compliance
      const ashraeCompliance =
      /* istanbul ignore next */
      (cov_is4rpdbyg().s[7]++, await this.checkASHRAECompliance(systemConfiguration, performanceMetrics, energyAnalysis));
      // Check SMACNA compliance
      const smacnaCompliance =
      /* istanbul ignore next */
      (cov_is4rpdbyg().s[8]++, await this.checkSMACNACompliance(systemConfiguration, performanceMetrics));
      // Check energy code compliance
      const energyCodeCompliance =
      /* istanbul ignore next */
      (cov_is4rpdbyg().s[9]++, await this.checkEnergyCodeCompliance(systemConfiguration, energyAnalysis, projectLocation));
      // Check environmental compliance
      const environmentalCompliance =
      /* istanbul ignore next */
      (cov_is4rpdbyg().s[10]++, await this.checkEnvironmentalCompliance(systemConfiguration, environmentalAnalysis, projectLocation));
      // Check local code compliance
      const localCodeCompliance =
      /* istanbul ignore next */
      (cov_is4rpdbyg().s[11]++, await this.checkLocalCodeCompliance(systemConfiguration, performanceMetrics, projectLocation));
      // Aggregate compliance results
      const overallCompliance =
      /* istanbul ignore next */
      (cov_is4rpdbyg().s[12]++, this.aggregateComplianceResults([ashraeCompliance, smacnaCompliance, energyCodeCompliance, environmentalCompliance, localCodeCompliance]));
      // Generate compliance recommendations
      const recommendations =
      /* istanbul ignore next */
      (cov_is4rpdbyg().s[13]++, await this.generateComplianceRecommendations([ashraeCompliance, smacnaCompliance, energyCodeCompliance, environmentalCompliance, localCodeCompliance], systemConfiguration));
      // Assess certification requirements
      const certificationRequirements =
      /* istanbul ignore next */
      (cov_is4rpdbyg().s[14]++, await this.assessCertificationRequirements(overallCompliance, environmentalAnalysis, projectLocation));
      const analysis =
      /* istanbul ignore next */
      (cov_is4rpdbyg().s[15]++, {
        id: analysisId,
        systemId: systemConfiguration.id,
        analysisTimestamp: timestamp,
        applicableStandards,
        ashraeCompliance,
        smacnaCompliance,
        energyCodeCompliance,
        environmentalCompliance,
        localCodeCompliance,
        overallCompliance,
        recommendations,
        certificationRequirements
      });
      // Cache the analysis
      /* istanbul ignore next */
      cov_is4rpdbyg().s[16]++;
      this.COMPLIANCE_CACHE.set(analysisId, analysis);
      /* istanbul ignore next */
      cov_is4rpdbyg().s[17]++;
      return analysis;
    } catch (error) {
      /* istanbul ignore next */
      cov_is4rpdbyg().s[18]++;
      throw new Error(`Compliance analysis failed: ${error instanceof Error ?
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[0][0]++, error.message) :
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[0][1]++, 'Unknown error')}`);
    }
  }
  /**
   * Determine applicable standards based on location and project type
   */
  static determineApplicableStandards(applicableCodes, projectLocation) {
    /* istanbul ignore next */
    cov_is4rpdbyg().f[1]++;
    const standards =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[19]++, []);
    // Always applicable standards
    /* istanbul ignore next */
    cov_is4rpdbyg().s[20]++;
    standards.push({
      name: 'ASHRAE 90.1',
      version: '2019',
      scope: 'Energy Efficiency',
      mandatory: true,
      description: 'Energy Standard for Buildings Except Low-Rise Residential Buildings'
    });
    /* istanbul ignore next */
    cov_is4rpdbyg().s[21]++;
    standards.push({
      name: 'SMACNA',
      version: '2006',
      scope: 'Duct Construction',
      mandatory: true,
      description: 'HVAC Duct Construction Standards'
    });
    // Location-specific standards
    /* istanbul ignore next */
    cov_is4rpdbyg().s[22]++;
    if (projectLocation) {
      /* istanbul ignore next */
      cov_is4rpdbyg().b[1][0]++;
      cov_is4rpdbyg().s[23]++;
      if (projectLocation.state === 'CA') {
        /* istanbul ignore next */
        cov_is4rpdbyg().b[2][0]++;
        cov_is4rpdbyg().s[24]++;
        standards.push({
          name: 'Title 24',
          version: '2022',
          scope: 'Energy Efficiency',
          mandatory: true,
          description: 'California Building Energy Efficiency Standards'
        });
      } else
      /* istanbul ignore next */
      {
        cov_is4rpdbyg().b[2][1]++;
      }
      // Add IECC for most US locations
      cov_is4rpdbyg().s[25]++;
      if (projectLocation.country === 'US') {
        /* istanbul ignore next */
        cov_is4rpdbyg().b[3][0]++;
        cov_is4rpdbyg().s[26]++;
        standards.push({
          name: 'IECC',
          version: '2021',
          scope: 'Energy Code',
          mandatory: true,
          description: 'International Energy Conservation Code'
        });
      } else
      /* istanbul ignore next */
      {
        cov_is4rpdbyg().b[3][1]++;
      }
    } else
    /* istanbul ignore next */
    {
      cov_is4rpdbyg().b[1][1]++;
    }
    // Add any explicitly specified codes
    cov_is4rpdbyg().s[27]++;
    if (applicableCodes) {
      /* istanbul ignore next */
      cov_is4rpdbyg().b[4][0]++;
      cov_is4rpdbyg().s[28]++;
      applicableCodes.forEach(code => {
        /* istanbul ignore next */
        cov_is4rpdbyg().f[2]++;
        cov_is4rpdbyg().s[29]++;
        if (!standards.find(s => {
          /* istanbul ignore next */
          cov_is4rpdbyg().f[3]++;
          cov_is4rpdbyg().s[30]++;
          return s.name === code;
        })) {
          /* istanbul ignore next */
          cov_is4rpdbyg().b[5][0]++;
          cov_is4rpdbyg().s[31]++;
          standards.push({
            name: code,
            version: 'Current',
            scope: 'Local Code',
            mandatory: true,
            description: `Local building code: ${code}`
          });
        } else
        /* istanbul ignore next */
        {
          cov_is4rpdbyg().b[5][1]++;
        }
      });
    } else
    /* istanbul ignore next */
    {
      cov_is4rpdbyg().b[4][1]++;
    }
    cov_is4rpdbyg().s[32]++;
    return standards;
  }
  /**
   * Check ASHRAE 90.1 compliance
   */
  static async checkASHRAECompliance(systemConfiguration, performanceMetrics, energyAnalysis) {
    /* istanbul ignore next */
    cov_is4rpdbyg().f[4]++;
    const checks =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[33]++, []);
    // Fan power compliance check
    const fanPowerLimit =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[34]++, this.ASHRAE_901_LIMITS.FAN_POWER.SUPPLY_ONLY);
    const actualFanPower =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[35]++, energyAnalysis.efficiencyMetrics.specificFanPower);
    /* istanbul ignore next */
    cov_is4rpdbyg().s[36]++;
    checks.push({
      requirement: 'Fan Power Limitation',
      standard: 'ASHRAE 90.1-2019 Section *******',
      limit: `${fanPowerLimit} W/CFM`,
      actual: `${actualFanPower.toFixed(2)} W/CFM`,
      status: actualFanPower <= fanPowerLimit ?
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[6][0]++, SystemAnalysisTypes_1.ComplianceStatus.COMPLIANT) :
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[6][1]++, SystemAnalysisTypes_1.ComplianceStatus.NON_COMPLIANT),
      severity: actualFanPower <= fanPowerLimit ?
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[7][0]++, 'info') :
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[7][1]++, 'error'),
      description: 'Maximum allowable fan power for supply-only systems'
    });
    // Duct insulation check (simplified)
    const hasAdequateInsulation =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[37]++, this.checkDuctInsulation(systemConfiguration));
    /* istanbul ignore next */
    cov_is4rpdbyg().s[38]++;
    checks.push({
      requirement: 'Duct Insulation',
      standard: 'ASHRAE 90.1-2019 Section 6.4.4',
      limit: 'R-6 minimum for supply ducts in unconditioned spaces',
      actual: hasAdequateInsulation ?
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[8][0]++, 'Adequate') :
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[8][1]++, 'Inadequate'),
      status: hasAdequateInsulation ?
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[9][0]++, SystemAnalysisTypes_1.ComplianceStatus.COMPLIANT) :
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[9][1]++, SystemAnalysisTypes_1.ComplianceStatus.NON_COMPLIANT),
      severity: hasAdequateInsulation ?
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[10][0]++, 'info') :
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[10][1]++, 'warning'),
      description: 'Minimum insulation requirements for ductwork'
    });
    // Duct leakage check (simplified)
    const estimatedLeakage =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[39]++, this.estimateDuctLeakage(systemConfiguration));
    const leakageLimit =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[40]++, this.ASHRAE_901_LIMITS.DUCT_LEAKAGE.SUPPLY);
    /* istanbul ignore next */
    cov_is4rpdbyg().s[41]++;
    checks.push({
      requirement: 'Duct Leakage',
      standard: 'ASHRAE 90.1-2019 Section *******',
      limit: `${leakageLimit}% of design airflow`,
      actual: `${estimatedLeakage.toFixed(1)}% estimated`,
      status: estimatedLeakage <= leakageLimit ?
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[11][0]++, SystemAnalysisTypes_1.ComplianceStatus.COMPLIANT) :
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[11][1]++, SystemAnalysisTypes_1.ComplianceStatus.NON_COMPLIANT),
      severity: estimatedLeakage <= leakageLimit ?
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[12][0]++, 'info') :
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[12][1]++, 'error'),
      description: 'Maximum allowable duct leakage for supply systems'
    });
    // Calculate overall compliance
    const compliantChecks =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[42]++, checks.filter(check => {
      /* istanbul ignore next */
      cov_is4rpdbyg().f[5]++;
      cov_is4rpdbyg().s[43]++;
      return check.status === SystemAnalysisTypes_1.ComplianceStatus.COMPLIANT;
    }).length);
    const overallStatus =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[44]++, compliantChecks === checks.length ?
    /* istanbul ignore next */
    (cov_is4rpdbyg().b[13][0]++, SystemAnalysisTypes_1.ComplianceStatus.COMPLIANT) :
    /* istanbul ignore next */
    (cov_is4rpdbyg().b[13][1]++, compliantChecks > checks.length / 2 ?
    /* istanbul ignore next */
    (cov_is4rpdbyg().b[14][0]++, SystemAnalysisTypes_1.ComplianceStatus.PARTIALLY_COMPLIANT) :
    /* istanbul ignore next */
    (cov_is4rpdbyg().b[14][1]++, SystemAnalysisTypes_1.ComplianceStatus.NON_COMPLIANT)));
    /* istanbul ignore next */
    cov_is4rpdbyg().s[45]++;
    return {
      standard: 'ASHRAE 90.1-2019',
      overallStatus,
      compliancePercentage: compliantChecks / checks.length * 100,
      checks,
      summary: `${compliantChecks} of ${checks.length} requirements met`,
      criticalIssues: checks.filter(check => {
        /* istanbul ignore next */
        cov_is4rpdbyg().f[6]++;
        cov_is4rpdbyg().s[46]++;
        return check.severity === 'error';
      }).length,
      warnings: checks.filter(check => {
        /* istanbul ignore next */
        cov_is4rpdbyg().f[7]++;
        cov_is4rpdbyg().s[47]++;
        return check.severity === 'warning';
      }).length
    };
  }
  /**
   * Check duct insulation adequacy (simplified)
   */
  static checkDuctInsulation(systemConfiguration) {
    /* istanbul ignore next */
    cov_is4rpdbyg().f[8]++;
    cov_is4rpdbyg().s[48]++;
    // Simplified check - assume adequate insulation for now
    // In a real implementation, this would check actual insulation specifications
    return true;
  }
  /**
   * Estimate duct leakage (simplified)
   */
  static estimateDuctLeakage(systemConfiguration) {
    /* istanbul ignore next */
    cov_is4rpdbyg().f[9]++;
    // Simplified leakage estimation based on system pressure
    const systemPressure =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[49]++, systemConfiguration.designParameters.designPressure);
    // Higher pressure systems tend to have more leakage
    /* istanbul ignore next */
    cov_is4rpdbyg().s[50]++;
    if (systemPressure > 4.0) {
      /* istanbul ignore next */
      cov_is4rpdbyg().b[15][0]++;
      cov_is4rpdbyg().s[51]++;
      return 5.5;
    } else
    /* istanbul ignore next */
    {
      cov_is4rpdbyg().b[15][1]++;
    } // Higher leakage
    cov_is4rpdbyg().s[52]++;
    if (systemPressure > 2.0) {
      /* istanbul ignore next */
      cov_is4rpdbyg().b[16][0]++;
      cov_is4rpdbyg().s[53]++;
      return 3.5;
    } else
    /* istanbul ignore next */
    {
      cov_is4rpdbyg().b[16][1]++;
    } // Moderate leakage
    cov_is4rpdbyg().s[54]++;
    return 2.5; // Lower leakage
  }
  /**
   * Check SMACNA compliance
   */
  static async checkSMACNACompliance(systemConfiguration, performanceMetrics) {
    /* istanbul ignore next */
    cov_is4rpdbyg().f[10]++;
    const checks =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[55]++, []);
    // Duct construction pressure limits
    const systemPressure =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[56]++, systemConfiguration.designParameters.designPressure);
    const ductShape =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[57]++,
    /* istanbul ignore next */
    (cov_is4rpdbyg().b[17][0]++, systemConfiguration.ductConfiguration?.shape) ||
    /* istanbul ignore next */
    (cov_is4rpdbyg().b[17][1]++, 'rectangular'));
    const pressureLimit =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[58]++, ductShape === 'round' ?
    /* istanbul ignore next */
    (cov_is4rpdbyg().b[18][0]++, this.SMACNA_LIMITS.DUCT_CONSTRUCTION.MAX_PRESSURE.ROUND) :
    /* istanbul ignore next */
    (cov_is4rpdbyg().b[18][1]++, this.SMACNA_LIMITS.DUCT_CONSTRUCTION.MAX_PRESSURE.RECTANGULAR));
    /* istanbul ignore next */
    cov_is4rpdbyg().s[59]++;
    checks.push({
      requirement: 'Duct Construction Pressure Limit',
      standard: 'SMACNA HVAC Duct Construction Standards',
      limit: `${pressureLimit} in. w.g. for ${ductShape} ducts`,
      actual: `${systemPressure.toFixed(1)} in. w.g.`,
      status: systemPressure <= pressureLimit ?
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[19][0]++, SystemAnalysisTypes_1.ComplianceStatus.COMPLIANT) :
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[19][1]++, SystemAnalysisTypes_1.ComplianceStatus.NON_COMPLIANT),
      severity: systemPressure <= pressureLimit ?
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[20][0]++, 'info') :
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[20][1]++, 'error'),
      description: 'Maximum allowable static pressure for duct construction class'
    });
    // Leakage class requirements
    const estimatedLeakage =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[60]++, this.estimateDuctLeakage(systemConfiguration));
    const leakageClass =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[61]++, this.determineSMACNALeakageClass(systemPressure));
    const leakageLimit =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[62]++, this.SMACNA_LIMITS.LEAKAGE_CLASS[leakageClass]);
    /* istanbul ignore next */
    cov_is4rpdbyg().s[63]++;
    checks.push({
      requirement: `SMACNA Leakage ${leakageClass}`,
      standard: 'SMACNA HVAC Duct Construction Standards',
      limit: `${leakageLimit} CFM/100 sq ft @ 1" w.g.`,
      actual: `${(estimatedLeakage * 2).toFixed(1)} CFM/100 sq ft estimated`,
      status: estimatedLeakage * 2 <= leakageLimit ?
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[21][0]++, SystemAnalysisTypes_1.ComplianceStatus.COMPLIANT) :
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[21][1]++, SystemAnalysisTypes_1.ComplianceStatus.NON_COMPLIANT),
      severity: estimatedLeakage * 2 <= leakageLimit ?
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[22][0]++, 'info') :
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[22][1]++, 'warning'),
      description: 'SMACNA duct leakage classification requirements'
    });
    // Reinforcement requirements (simplified)
    const hasAdequateReinforcement =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[64]++, this.checkDuctReinforcement(systemConfiguration));
    /* istanbul ignore next */
    cov_is4rpdbyg().s[65]++;
    checks.push({
      requirement: 'Duct Reinforcement',
      standard: 'SMACNA HVAC Duct Construction Standards',
      limit: 'Adequate reinforcement per SMACNA standards',
      actual: hasAdequateReinforcement ?
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[23][0]++, 'Adequate') :
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[23][1]++, 'Inadequate'),
      status: hasAdequateReinforcement ?
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[24][0]++, SystemAnalysisTypes_1.ComplianceStatus.COMPLIANT) :
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[24][1]++, SystemAnalysisTypes_1.ComplianceStatus.NON_COMPLIANT),
      severity: hasAdequateReinforcement ?
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[25][0]++, 'info') :
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[25][1]++, 'warning'),
      description: 'Duct reinforcement requirements for system pressure class'
    });
    const compliantChecks =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[66]++, checks.filter(check => {
      /* istanbul ignore next */
      cov_is4rpdbyg().f[11]++;
      cov_is4rpdbyg().s[67]++;
      return check.status === SystemAnalysisTypes_1.ComplianceStatus.COMPLIANT;
    }).length);
    let overallStatus;
    /* istanbul ignore next */
    cov_is4rpdbyg().s[68]++;
    if (compliantChecks === checks.length) {
      /* istanbul ignore next */
      cov_is4rpdbyg().b[26][0]++;
      cov_is4rpdbyg().s[69]++;
      overallStatus = SystemAnalysisTypes_1.ComplianceStatus.COMPLIANT;
    } else {
      /* istanbul ignore next */
      cov_is4rpdbyg().b[26][1]++;
      cov_is4rpdbyg().s[70]++;
      if (compliantChecks > checks.length / 2) {
        /* istanbul ignore next */
        cov_is4rpdbyg().b[27][0]++;
        cov_is4rpdbyg().s[71]++;
        overallStatus = SystemAnalysisTypes_1.ComplianceStatus.PARTIALLY_COMPLIANT;
      } else {
        /* istanbul ignore next */
        cov_is4rpdbyg().b[27][1]++;
        cov_is4rpdbyg().s[72]++;
        overallStatus = SystemAnalysisTypes_1.ComplianceStatus.NON_COMPLIANT;
      }
    }
    /* istanbul ignore next */
    cov_is4rpdbyg().s[73]++;
    return {
      standard: 'SMACNA HVAC Duct Construction',
      overallStatus,
      compliancePercentage: compliantChecks / checks.length * 100,
      checks,
      summary: `${compliantChecks} of ${checks.length} SMACNA requirements met`,
      criticalIssues: checks.filter(check => {
        /* istanbul ignore next */
        cov_is4rpdbyg().f[12]++;
        cov_is4rpdbyg().s[74]++;
        return check.severity === 'error';
      }).length,
      warnings: checks.filter(check => {
        /* istanbul ignore next */
        cov_is4rpdbyg().f[13]++;
        cov_is4rpdbyg().s[75]++;
        return check.severity === 'warning';
      }).length
    };
  }
  /**
   * Determine SMACNA leakage class based on system pressure
   */
  static determineSMACNALeakageClass(pressure) {
    /* istanbul ignore next */
    cov_is4rpdbyg().f[14]++;
    cov_is4rpdbyg().s[76]++;
    if (pressure <= 2.0) {
      /* istanbul ignore next */
      cov_is4rpdbyg().b[28][0]++;
      cov_is4rpdbyg().s[77]++;
      return 'CLASS_3';
    } else
    /* istanbul ignore next */
    {
      cov_is4rpdbyg().b[28][1]++;
    }
    cov_is4rpdbyg().s[78]++;
    if (pressure <= 4.0) {
      /* istanbul ignore next */
      cov_is4rpdbyg().b[29][0]++;
      cov_is4rpdbyg().s[79]++;
      return 'CLASS_2';
    } else
    /* istanbul ignore next */
    {
      cov_is4rpdbyg().b[29][1]++;
    }
    cov_is4rpdbyg().s[80]++;
    if (pressure <= 6.0) {
      /* istanbul ignore next */
      cov_is4rpdbyg().b[30][0]++;
      cov_is4rpdbyg().s[81]++;
      return 'CLASS_1';
    } else
    /* istanbul ignore next */
    {
      cov_is4rpdbyg().b[30][1]++;
    }
    cov_is4rpdbyg().s[82]++;
    return 'CLASS_1'; // Highest class for high pressure
  }
  /**
   * Check duct reinforcement adequacy (simplified)
   */
  static checkDuctReinforcement(systemConfiguration) {
    /* istanbul ignore next */
    cov_is4rpdbyg().f[15]++;
    const systemPressure =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[83]++, systemConfiguration.designParameters.designPressure);
    // Simplified check - assume adequate reinforcement for pressures under 6" w.g.
    /* istanbul ignore next */
    cov_is4rpdbyg().s[84]++;
    return systemPressure <= 6.0;
  }
  /**
   * Check energy code compliance
   */
  static async checkEnergyCodeCompliance(systemConfiguration, energyAnalysis, projectLocation) {
    /* istanbul ignore next */
    cov_is4rpdbyg().f[16]++;
    const checks =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[85]++, []);
    // Determine applicable energy code
    const energyCode =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[86]++, this.determineEnergyCode(projectLocation));
    const fanPowerLimit =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[87]++, this.getEnergyCodeFanPowerLimit(energyCode, projectLocation));
    const actualFanPower =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[88]++, energyAnalysis.efficiencyMetrics.specificFanPower);
    /* istanbul ignore next */
    cov_is4rpdbyg().s[89]++;
    checks.push({
      requirement: 'Energy Code Fan Power Limit',
      standard: `${energyCode} Fan Power Requirements`,
      limit: `${fanPowerLimit} W/CFM`,
      actual: `${actualFanPower.toFixed(2)} W/CFM`,
      status: actualFanPower <= fanPowerLimit ?
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[31][0]++, SystemAnalysisTypes_1.ComplianceStatus.COMPLIANT) :
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[31][1]++, SystemAnalysisTypes_1.ComplianceStatus.NON_COMPLIANT),
      severity: actualFanPower <= fanPowerLimit ?
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[32][0]++, 'info') :
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[32][1]++, 'error'),
      description: `${energyCode} maximum allowable fan power`
    });
    // Duct sealing requirements
    const hasDuctSealing =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[90]++, this.checkDuctSealing(systemConfiguration));
    /* istanbul ignore next */
    cov_is4rpdbyg().s[91]++;
    checks.push({
      requirement: 'Duct Sealing',
      standard: `${energyCode} Duct Sealing Requirements`,
      limit: 'Duct sealing required per code',
      actual: hasDuctSealing ?
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[33][0]++, 'Compliant') :
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[33][1]++, 'Non-compliant'),
      status: hasDuctSealing ?
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[34][0]++, SystemAnalysisTypes_1.ComplianceStatus.COMPLIANT) :
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[34][1]++, SystemAnalysisTypes_1.ComplianceStatus.NON_COMPLIANT),
      severity: hasDuctSealing ?
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[35][0]++, 'info') :
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[35][1]++, 'error'),
      description: 'Energy code duct sealing requirements'
    });
    const compliantChecks =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[92]++, checks.filter(check => {
      /* istanbul ignore next */
      cov_is4rpdbyg().f[17]++;
      cov_is4rpdbyg().s[93]++;
      return check.status === SystemAnalysisTypes_1.ComplianceStatus.COMPLIANT;
    }).length);
    const overallStatus =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[94]++, compliantChecks === checks.length ?
    /* istanbul ignore next */
    (cov_is4rpdbyg().b[36][0]++, SystemAnalysisTypes_1.ComplianceStatus.COMPLIANT) :
    /* istanbul ignore next */
    (cov_is4rpdbyg().b[36][1]++, SystemAnalysisTypes_1.ComplianceStatus.NON_COMPLIANT));
    /* istanbul ignore next */
    cov_is4rpdbyg().s[95]++;
    return {
      standard: energyCode,
      overallStatus,
      compliancePercentage: compliantChecks / checks.length * 100,
      checks,
      summary: `${compliantChecks} of ${checks.length} ${energyCode} requirements met`,
      criticalIssues: checks.filter(check => {
        /* istanbul ignore next */
        cov_is4rpdbyg().f[18]++;
        cov_is4rpdbyg().s[96]++;
        return check.severity === 'error';
      }).length,
      warnings: checks.filter(check => {
        /* istanbul ignore next */
        cov_is4rpdbyg().f[19]++;
        cov_is4rpdbyg().s[97]++;
        return check.severity === 'warning';
      }).length
    };
  }
  /**
   * Determine applicable energy code
   */
  static determineEnergyCode(projectLocation) {
    /* istanbul ignore next */
    cov_is4rpdbyg().f[20]++;
    cov_is4rpdbyg().s[98]++;
    if (projectLocation?.state === 'CA') {
      /* istanbul ignore next */
      cov_is4rpdbyg().b[37][0]++;
      cov_is4rpdbyg().s[99]++;
      return 'Title 24';
    } else
    /* istanbul ignore next */
    {
      cov_is4rpdbyg().b[37][1]++;
    }
    cov_is4rpdbyg().s[100]++;
    return 'IECC 2021';
  }
  /**
   * Get energy code fan power limit
   */
  static getEnergyCodeFanPowerLimit(energyCode, projectLocation) {
    /* istanbul ignore next */
    cov_is4rpdbyg().f[21]++;
    cov_is4rpdbyg().s[101]++;
    if (energyCode === 'Title 24') {
      /* istanbul ignore next */
      cov_is4rpdbyg().b[38][0]++;
      cov_is4rpdbyg().s[102]++;
      return this.ENERGY_CODE_LIMITS.TITLE_24.FAN_POWER;
    } else
    /* istanbul ignore next */
    {
      cov_is4rpdbyg().b[38][1]++;
    }
    cov_is4rpdbyg().s[103]++;
    return this.ENERGY_CODE_LIMITS.IECC.FAN_POWER;
  }
  /**
   * Check duct sealing compliance (simplified)
   */
  static checkDuctSealing(systemConfiguration) {
    /* istanbul ignore next */
    cov_is4rpdbyg().f[22]++;
    cov_is4rpdbyg().s[104]++;
    // Simplified check - assume duct sealing is specified
    return true;
  }
  /**
   * Check environmental compliance
   */
  static async checkEnvironmentalCompliance(systemConfiguration, environmentalAnalysis, projectLocation) {
    /* istanbul ignore next */
    cov_is4rpdbyg().f[23]++;
    const checks =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[105]++, []);
    // Carbon emissions compliance (if applicable)
    const carbonIntensity =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[106]++, environmentalAnalysis.carbonFootprint.totalEmissions.value / systemConfiguration.designParameters.designAirflow);
    const carbonLimit =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[107]++, 0.15); // kg CO2e/CFM/year (example limit)
    /* istanbul ignore next */
    cov_is4rpdbyg().s[108]++;
    checks.push({
      requirement: 'Carbon Emissions Limit',
      standard: 'Environmental Regulations',
      limit: `${carbonLimit} kg CO2e/CFM/year`,
      actual: `${carbonIntensity.toFixed(3)} kg CO2e/CFM/year`,
      status: carbonIntensity <= carbonLimit ?
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[39][0]++, SystemAnalysisTypes_1.ComplianceStatus.COMPLIANT) :
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[39][1]++, SystemAnalysisTypes_1.ComplianceStatus.NON_COMPLIANT),
      severity: carbonIntensity <= carbonLimit ?
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[40][0]++, 'info') :
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[40][1]++, 'warning'),
      description: 'Maximum allowable carbon emissions intensity'
    });
    // Refrigerant compliance (if applicable)
    const hasLowGWPRefrigerant =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[109]++, this.checkRefrigerantCompliance(systemConfiguration));
    /* istanbul ignore next */
    cov_is4rpdbyg().s[110]++;
    checks.push({
      requirement: 'Refrigerant GWP Limit',
      standard: 'EPA SNAP Program',
      limit: 'Low GWP refrigerants required',
      actual: hasLowGWPRefrigerant ?
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[41][0]++, 'Compliant') :
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[41][1]++, 'Non-compliant'),
      status: hasLowGWPRefrigerant ?
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[42][0]++, SystemAnalysisTypes_1.ComplianceStatus.COMPLIANT) :
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[42][1]++, SystemAnalysisTypes_1.ComplianceStatus.NON_COMPLIANT),
      severity: hasLowGWPRefrigerant ?
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[43][0]++, 'info') :
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[43][1]++, 'warning'),
      description: 'EPA refrigerant regulations compliance'
    });
    const compliantChecks =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[111]++, checks.filter(check => {
      /* istanbul ignore next */
      cov_is4rpdbyg().f[24]++;
      cov_is4rpdbyg().s[112]++;
      return check.status === SystemAnalysisTypes_1.ComplianceStatus.COMPLIANT;
    }).length);
    const overallStatus =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[113]++, compliantChecks === checks.length ?
    /* istanbul ignore next */
    (cov_is4rpdbyg().b[44][0]++, SystemAnalysisTypes_1.ComplianceStatus.COMPLIANT) :
    /* istanbul ignore next */
    (cov_is4rpdbyg().b[44][1]++, SystemAnalysisTypes_1.ComplianceStatus.PARTIALLY_COMPLIANT));
    /* istanbul ignore next */
    cov_is4rpdbyg().s[114]++;
    return {
      standard: 'Environmental Regulations',
      overallStatus,
      compliancePercentage: compliantChecks / checks.length * 100,
      checks,
      summary: `${compliantChecks} of ${checks.length} environmental requirements met`,
      criticalIssues: checks.filter(check => {
        /* istanbul ignore next */
        cov_is4rpdbyg().f[25]++;
        cov_is4rpdbyg().s[115]++;
        return check.severity === 'error';
      }).length,
      warnings: checks.filter(check => {
        /* istanbul ignore next */
        cov_is4rpdbyg().f[26]++;
        cov_is4rpdbyg().s[116]++;
        return check.severity === 'warning';
      }).length
    };
  }
  /**
   * Check refrigerant compliance (simplified)
   */
  static checkRefrigerantCompliance(systemConfiguration) {
    /* istanbul ignore next */
    cov_is4rpdbyg().f[27]++;
    cov_is4rpdbyg().s[117]++;
    // Simplified check - assume compliant refrigerants
    return true;
  }
  /**
   * Check local code compliance
   */
  static async checkLocalCodeCompliance(systemConfiguration, performanceMetrics, projectLocation) {
    /* istanbul ignore next */
    cov_is4rpdbyg().f[28]++;
    const checks =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[118]++, []);
    // Local noise requirements (example)
    const estimatedNoise =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[119]++, this.estimateSystemNoise(systemConfiguration));
    const noiseLimit =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[120]++, 55); // dBA (example local limit)
    /* istanbul ignore next */
    cov_is4rpdbyg().s[121]++;
    checks.push({
      requirement: 'Noise Level Limit',
      standard: 'Local Building Code',
      limit: `${noiseLimit} dBA`,
      actual: `${estimatedNoise} dBA estimated`,
      status: estimatedNoise <= noiseLimit ?
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[45][0]++, SystemAnalysisTypes_1.ComplianceStatus.COMPLIANT) :
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[45][1]++, SystemAnalysisTypes_1.ComplianceStatus.NON_COMPLIANT),
      severity: estimatedNoise <= noiseLimit ?
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[46][0]++, 'info') :
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[46][1]++, 'warning'),
      description: 'Local noise ordinance compliance'
    });
    // Fire safety requirements (simplified)
    const hasFireDampers =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[122]++, this.checkFireSafety(systemConfiguration));
    /* istanbul ignore next */
    cov_is4rpdbyg().s[123]++;
    checks.push({
      requirement: 'Fire Safety Systems',
      standard: 'Local Fire Code',
      limit: 'Fire dampers required per code',
      actual: hasFireDampers ?
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[47][0]++, 'Compliant') :
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[47][1]++, 'Non-compliant'),
      status: hasFireDampers ?
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[48][0]++, SystemAnalysisTypes_1.ComplianceStatus.COMPLIANT) :
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[48][1]++, SystemAnalysisTypes_1.ComplianceStatus.NON_COMPLIANT),
      severity: hasFireDampers ?
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[49][0]++, 'info') :
      /* istanbul ignore next */
      (cov_is4rpdbyg().b[49][1]++, 'error'),
      description: 'Local fire code requirements for HVAC systems'
    });
    const compliantChecks =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[124]++, checks.filter(check => {
      /* istanbul ignore next */
      cov_is4rpdbyg().f[29]++;
      cov_is4rpdbyg().s[125]++;
      return check.status === SystemAnalysisTypes_1.ComplianceStatus.COMPLIANT;
    }).length);
    const overallStatus =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[126]++, compliantChecks === checks.length ?
    /* istanbul ignore next */
    (cov_is4rpdbyg().b[50][0]++, SystemAnalysisTypes_1.ComplianceStatus.COMPLIANT) :
    /* istanbul ignore next */
    (cov_is4rpdbyg().b[50][1]++, SystemAnalysisTypes_1.ComplianceStatus.PARTIALLY_COMPLIANT));
    /* istanbul ignore next */
    cov_is4rpdbyg().s[127]++;
    return {
      standard: 'Local Building Code',
      overallStatus,
      compliancePercentage: compliantChecks / checks.length * 100,
      checks,
      summary: `${compliantChecks} of ${checks.length} local code requirements met`,
      criticalIssues: checks.filter(check => {
        /* istanbul ignore next */
        cov_is4rpdbyg().f[30]++;
        cov_is4rpdbyg().s[128]++;
        return check.severity === 'error';
      }).length,
      warnings: checks.filter(check => {
        /* istanbul ignore next */
        cov_is4rpdbyg().f[31]++;
        cov_is4rpdbyg().s[129]++;
        return check.severity === 'warning';
      }).length
    };
  }
  /**
   * Estimate system noise (simplified)
   */
  static estimateSystemNoise(systemConfiguration) {
    /* istanbul ignore next */
    cov_is4rpdbyg().f[32]++;
    const designAirflow =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[130]++, systemConfiguration.designParameters.designAirflow);
    const systemPressure =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[131]++, systemConfiguration.designParameters.designPressure);
    // Simplified noise estimation
    const baseNoise =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[132]++, 45); // dBA base
    const flowNoise =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[133]++, Math.log10(designAirflow / 1000) * 10); // Flow contribution
    const pressureNoise =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[134]++, systemPressure * 2); // Pressure contribution
    /* istanbul ignore next */
    cov_is4rpdbyg().s[135]++;
    return Math.round(baseNoise + flowNoise + pressureNoise);
  }
  /**
   * Check fire safety compliance (simplified)
   */
  static checkFireSafety(systemConfiguration) {
    /* istanbul ignore next */
    cov_is4rpdbyg().f[33]++;
    cov_is4rpdbyg().s[136]++;
    // Simplified check - assume fire safety systems are included
    return true;
  }
  /**
   * Aggregate compliance results
   */
  static aggregateComplianceResults(results) {
    /* istanbul ignore next */
    cov_is4rpdbyg().f[34]++;
    const allChecks =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[137]++, results.flatMap(result => {
      /* istanbul ignore next */
      cov_is4rpdbyg().f[35]++;
      cov_is4rpdbyg().s[138]++;
      return result.checks;
    }));
    const compliantChecks =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[139]++, allChecks.filter(check => {
      /* istanbul ignore next */
      cov_is4rpdbyg().f[36]++;
      cov_is4rpdbyg().s[140]++;
      return check.status === SystemAnalysisTypes_1.ComplianceStatus.COMPLIANT;
    }).length);
    let overallStatus;
    const compliancePercentage =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[141]++, compliantChecks / allChecks.length * 100);
    /* istanbul ignore next */
    cov_is4rpdbyg().s[142]++;
    if (compliancePercentage === 100) {
      /* istanbul ignore next */
      cov_is4rpdbyg().b[51][0]++;
      cov_is4rpdbyg().s[143]++;
      overallStatus = SystemAnalysisTypes_1.ComplianceStatus.COMPLIANT;
    } else {
      /* istanbul ignore next */
      cov_is4rpdbyg().b[51][1]++;
      cov_is4rpdbyg().s[144]++;
      if (compliancePercentage >= 80) {
        /* istanbul ignore next */
        cov_is4rpdbyg().b[52][0]++;
        cov_is4rpdbyg().s[145]++;
        overallStatus = SystemAnalysisTypes_1.ComplianceStatus.PARTIALLY_COMPLIANT;
      } else {
        /* istanbul ignore next */
        cov_is4rpdbyg().b[52][1]++;
        cov_is4rpdbyg().s[146]++;
        overallStatus = SystemAnalysisTypes_1.ComplianceStatus.NON_COMPLIANT;
      }
    }
    /* istanbul ignore next */
    cov_is4rpdbyg().s[147]++;
    return {
      standard: 'Overall Compliance',
      overallStatus,
      compliancePercentage,
      checks: allChecks,
      summary: `${compliantChecks} of ${allChecks.length} total requirements met`,
      criticalIssues: allChecks.filter(check => {
        /* istanbul ignore next */
        cov_is4rpdbyg().f[37]++;
        cov_is4rpdbyg().s[148]++;
        return check.severity === 'error';
      }).length,
      warnings: allChecks.filter(check => {
        /* istanbul ignore next */
        cov_is4rpdbyg().f[38]++;
        cov_is4rpdbyg().s[149]++;
        return check.severity === 'warning';
      }).length
    };
  }
  /**
   * Generate compliance recommendations
   */
  static async generateComplianceRecommendations(results, systemConfiguration) {
    /* istanbul ignore next */
    cov_is4rpdbyg().f[39]++;
    const recommendations =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[150]++, []);
    // Analyze all non-compliant checks
    const nonCompliantChecks =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[151]++, results.flatMap(result => {
      /* istanbul ignore next */
      cov_is4rpdbyg().f[40]++;
      cov_is4rpdbyg().s[152]++;
      return result.checks.filter(check => {
        /* istanbul ignore next */
        cov_is4rpdbyg().f[41]++;
        cov_is4rpdbyg().s[153]++;
        return check.status !== SystemAnalysisTypes_1.ComplianceStatus.COMPLIANT;
      });
    }));
    /* istanbul ignore next */
    cov_is4rpdbyg().s[154]++;
    nonCompliantChecks.forEach(check => {
      /* istanbul ignore next */
      cov_is4rpdbyg().f[42]++;
      cov_is4rpdbyg().s[155]++;
      if (check.requirement.includes('Fan Power')) {
        /* istanbul ignore next */
        cov_is4rpdbyg().b[53][0]++;
        cov_is4rpdbyg().s[156]++;
        recommendations.push({
          id: 'fan_power_optimization',
          priority: 'High',
          category: 'Energy Efficiency',
          title: 'Reduce Fan Power Consumption',
          description: 'System exceeds maximum allowable fan power. Consider optimizing ductwork design or upgrading to more efficient equipment.',
          affectedStandards: [check.standard],
          estimatedCost: 15000,
          estimatedSavings: 5000,
          implementationTime: '2-4 weeks',
          actions: ['Optimize duct sizing to reduce pressure losses', 'Consider variable frequency drives (VFDs)', 'Upgrade to high-efficiency fans', 'Review system design for oversizing']
        });
      } else
      /* istanbul ignore next */
      {
        cov_is4rpdbyg().b[53][1]++;
      }
      cov_is4rpdbyg().s[157]++;
      if (check.requirement.includes('Leakage')) {
        /* istanbul ignore next */
        cov_is4rpdbyg().b[54][0]++;
        cov_is4rpdbyg().s[158]++;
        recommendations.push({
          id: 'duct_sealing_improvement',
          priority: 'Medium',
          category: 'Duct Construction',
          title: 'Improve Duct Sealing',
          description: 'Duct leakage exceeds allowable limits. Enhanced sealing methods are required.',
          affectedStandards: [check.standard],
          estimatedCost: 8000,
          estimatedSavings: 3000,
          implementationTime: '1-2 weeks',
          actions: ['Implement comprehensive duct sealing program', 'Use mastic sealant at all joints', 'Perform duct blaster testing', 'Upgrade to higher leakage class construction']
        });
      } else
      /* istanbul ignore next */
      {
        cov_is4rpdbyg().b[54][1]++;
      }
    });
    /* istanbul ignore next */
    cov_is4rpdbyg().s[159]++;
    return recommendations;
  }
  /**
   * Assess certification requirements
   */
  static async assessCertificationRequirements(overallCompliance, environmentalAnalysis, projectLocation) {
    /* istanbul ignore next */
    cov_is4rpdbyg().f[43]++;
    const requirements =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[160]++, []);
    // LEED certification requirements
    /* istanbul ignore next */
    cov_is4rpdbyg().s[161]++;
    if (overallCompliance.compliancePercentage >= 80) {
      /* istanbul ignore next */
      cov_is4rpdbyg().b[55][0]++;
      cov_is4rpdbyg().s[162]++;
      requirements.push({
        certification: 'LEED',
        level: 'Silver',
        status: 'Eligible',
        requiredActions: ['Complete energy modeling', 'Document commissioning process', 'Provide equipment efficiency documentation'],
        estimatedPoints: 12,
        totalPointsAvailable: 110
      });
    } else
    /* istanbul ignore next */
    {
      cov_is4rpdbyg().b[55][1]++;
    }
    // ENERGY STAR certification
    cov_is4rpdbyg().s[163]++;
    if (environmentalAnalysis.sustainabilityMetrics.energyEfficiency.systemEfficiency >= 85) {
      /* istanbul ignore next */
      cov_is4rpdbyg().b[56][0]++;
      cov_is4rpdbyg().s[164]++;
      requirements.push({
        certification: 'ENERGY STAR',
        level: 'Certified',
        status: 'Eligible',
        requiredActions: ['Submit energy performance data', 'Complete third-party verification', 'Maintain performance for 12 months'],
        estimatedPoints: 0,
        // Pass/fail certification
        totalPointsAvailable: 0
      });
    } else
    /* istanbul ignore next */
    {
      cov_is4rpdbyg().b[56][1]++;
    }
    cov_is4rpdbyg().s[165]++;
    return requirements;
  }
  /**
   * Generate unique analysis ID
   */
  static generateAnalysisId(systemId) {
    /* istanbul ignore next */
    cov_is4rpdbyg().f[44]++;
    const timestamp =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[166]++, Date.now());
    const random =
    /* istanbul ignore next */
    (cov_is4rpdbyg().s[167]++, Math.random().toString(36).substring(2, 8));
    /* istanbul ignore next */
    cov_is4rpdbyg().s[168]++;
    return `compliance_analysis_${systemId}_${timestamp}_${random}`;
  }
}
/* istanbul ignore next */
cov_is4rpdbyg().s[169]++;
exports.ComplianceCheckingEngine = ComplianceCheckingEngine;
/* istanbul ignore next */
cov_is4rpdbyg().s[170]++;
ComplianceCheckingEngine.VERSION = '3.0.0';
/* istanbul ignore next */
cov_is4rpdbyg().s[171]++;
ComplianceCheckingEngine.COMPLIANCE_CACHE = new Map();
// ASHRAE 90.1 Standards
/* istanbul ignore next */
cov_is4rpdbyg().s[172]++;
ComplianceCheckingEngine.ASHRAE_901_LIMITS = {
  FAN_POWER: {
    SUPPLY_ONLY: 1.25,
    // W/CFM
    SUPPLY_RETURN: 1.25,
    // W/CFM
    COMPLEX_SYSTEMS: 1.25 // W/CFM
  },
  DUCT_LEAKAGE: {
    SUPPLY: 4,
    // % of design airflow
    RETURN: 3,
    // % of design airflow
    EXHAUST: 4 // % of design airflow
  },
  INSULATION: {
    SUPPLY_OUTDOOR: 8,
    // R-value
    SUPPLY_UNCONDITIONED: 6,
    // R-value
    RETURN_OUTDOOR: 6,
    // R-value
    RETURN_UNCONDITIONED: 4 // R-value
  }
};
// SMACNA Standards
/* istanbul ignore next */
cov_is4rpdbyg().s[173]++;
ComplianceCheckingEngine.SMACNA_LIMITS = {
  DUCT_CONSTRUCTION: {
    MAX_PRESSURE: {
      RECTANGULAR: 10,
      // in. w.g.
      ROUND: 20 // in. w.g.
    },
    REINFORCEMENT: {
      RECTANGULAR_SPACING: 48,
      // inches
      ROUND_SPACING: 60 // inches
    }
  },
  LEAKAGE_CLASS: {
    CLASS_1: 4,
    // CFM/100 sq ft @ 1" w.g.
    CLASS_2: 6,
    CLASS_3: 12,
    CLASS_6: 30
  }
};
// Energy Code Limits
/* istanbul ignore next */
cov_is4rpdbyg().s[174]++;
ComplianceCheckingEngine.ENERGY_CODE_LIMITS = {
  IECC: {
    FAN_POWER: 1.25,
    // W/CFM
    DUCT_SEALING: 'Required',
    INSULATION_ZONES: {
      ZONE_1_2: 4,
      // R-value
      ZONE_3_4: 6,
      ZONE_5_6: 8,
      ZONE_7_8: 8
    }
  },
  TITLE_24: {
    FAN_POWER: 1.0,
    // W/CFM (California)
    DUCT_LEAKAGE: 6,
    // % of design airflow
    INSULATION: 8 // R-value
  }
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************