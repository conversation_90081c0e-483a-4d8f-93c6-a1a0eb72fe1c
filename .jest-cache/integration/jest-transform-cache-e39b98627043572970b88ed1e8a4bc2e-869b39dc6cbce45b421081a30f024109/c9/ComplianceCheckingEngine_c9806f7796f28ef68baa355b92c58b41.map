{"version": 3, "names": ["cov_is4rpdbyg", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "name", "decl", "loc", "branchMap", "type", "locations", "undefined", "s", "f", "b", "inputSourceMap", "file", "mappings", "names", "sources", "sourcesContent", "version", "_coverageSchema", "coverage", "actualCoverage", "SystemAnalysisTypes_1", "require", "ComplianceCheckingEngine", "performComplianceAnalysis", "systemConfiguration", "performanceMetrics", "energyAnalysis", "environmentalAnalysis", "applicableCodes", "projectLocation", "analysisId", "generateAnalysisId", "id", "timestamp", "Date", "applicableStandards", "determineApplicableStandards", "ashraeCompliance", "checkASHRAECompliance", "smacnaCompliance", "checkSMACNACompliance", "energyCodeCompliance", "checkEnergyCodeCompliance", "environmentalCompliance", "checkEnvironmentalCompliance", "localCodeCompliance", "checkLocalCodeCompliance", "overallCompliance", "aggregateComplianceResults", "recommendations", "generateComplianceRecommendations", "certificationRequirements", "assessCertificationRequirements", "analysis", "systemId", "analysisTimestamp", "COMPLIANCE_CACHE", "set", "error", "Error", "message", "standards", "push", "scope", "mandatory", "description", "state", "country", "for<PERSON>ach", "code", "find", "checks", "fanPowerLimit", "ASHRAE_901_LIMITS", "FAN_POWER", "SUPPLY_ONLY", "actualFan<PERSON><PERSON>er", "efficiencyMetrics", "specificFan<PERSON>ower", "requirement", "standard", "limit", "actual", "toFixed", "status", "ComplianceStatus", "COMPLIANT", "NON_COMPLIANT", "severity", "hasAdequateInsulation", "checkDuctInsulation", "estimatedLeakage", "estimateDuctLeakage", "leakageLimit", "DUCT_LEAKAGE", "SUPPLY", "compliantChecks", "filter", "check", "length", "overallStatus", "PARTIALLY_COMPLIANT", "compliancePercentage", "summary", "criticalIssues", "warnings", "systemPressure", "designParameters", "designPressure", "ductShape", "ductConfiguration", "shape", "pressureLimit", "SMACNA_LIMITS", "DUCT_CONSTRUCTION", "MAX_PRESSURE", "ROUND", "RECTANGULAR", "leakageClass", "determineSMACNALeakageClass", "LEAKAGE_CLASS", "hasAdequateReinforcement", "checkDuctReinforcement", "pressure", "energyCode", "determineEnergyCode", "getEnergyCodeFanPowerLimit", "hasDuctSealing", "checkDuctSealing", "ENERGY_CODE_LIMITS", "TITLE_24", "IECC", "carbonIntensity", "carbonFootprint", "totalEmissions", "value", "designAirflow", "carbonLimit", "hasLowGWPRefrigerant", "checkRefrigerantCompliance", "estimatedNoise", "estimateSystemNoise", "noiseLimit", "hasFireDampers", "checkFireSafety", "baseNoise", "flowNoise", "Math", "log10", "pressureNoise", "round", "results", "allChecks", "flatMap", "result", "nonCompliantChecks", "includes", "priority", "category", "title", "affectedStandards", "estimatedCost", "estimatedSavings", "implementationTime", "actions", "requirements", "certification", "level", "requiredActions", "estimatedPoints", "totalPointsAvailable", "sustainabilityMetrics", "energyEfficiency", "systemEfficiency", "now", "random", "toString", "substring", "exports", "VERSION", "Map", "SUPPLY_RETURN", "COMPLEX_SYSTEMS", "RETURN", "EXHAUST", "INSULATION", "SUPPLY_OUTDOOR", "SUPPLY_UNCONDITIONED", "RETURN_OUTDOOR", "RETURN_UNCONDITIONED", "REINFORCEMENT", "RECTANGULAR_SPACING", "ROUND_SPACING", "CLASS_1", "CLASS_2", "CLASS_3", "CLASS_6", "DUCT_SEALING", "INSULATION_ZONES", "ZONE_1_2", "ZONE_3_4", "ZONE_5_6", "ZONE_7_8"], "sources": ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\ComplianceCheckingEngine.ts"], "sourcesContent": ["/**\r\n * Compliance Checking and Validation Engine\r\n * \r\n * Comprehensive compliance checking service for Phase 3 Priority 3: Advanced System Analysis Tools\r\n * Provides SMACNA, ASHRAE, and local code compliance checking with automated validation,\r\n * reporting, and certification support for HVAC duct systems.\r\n * \r\n * @version 3.0.0\r\n * <AUTHOR> Suite Development Team\r\n */\r\n\r\nimport {\r\n  ComplianceAnalysis,\r\n  ComplianceStandard,\r\n  ComplianceResult,\r\n  ComplianceStatus,\r\n  ComplianceRecommendation,\r\n  ValidationResult,\r\n  CertificationRequirement,\r\n  SystemConfiguration,\r\n  PerformanceMetrics,\r\n  EnergyAnalysis,\r\n  EnvironmentalImpactAnalysis\r\n} from './types/SystemAnalysisTypes';\r\n\r\n/**\r\n * Compliance Checking and Validation Engine\r\n * \r\n * Provides comprehensive compliance checking capabilities including:\r\n * - SMACNA standard compliance checking\r\n * - ASHRAE standard compliance validation\r\n * - Local building code compliance\r\n * - Energy code compliance (IECC, Title 24, etc.)\r\n * - Environmental regulation compliance\r\n * - Automated validation and reporting\r\n * - Certification support and documentation\r\n */\r\nexport class ComplianceCheckingEngine {\r\n  private static readonly VERSION = '3.0.0';\r\n  private static readonly COMPLIANCE_CACHE = new Map<string, ComplianceAnalysis>();\r\n  \r\n  // ASHRAE 90.1 Standards\r\n  private static readonly ASHRAE_901_LIMITS = {\r\n    FAN_POWER: {\r\n      SUPPLY_ONLY: 1.25, // W/CFM\r\n      SUPPLY_RETURN: 1.25, // W/CFM\r\n      COMPLEX_SYSTEMS: 1.25 // W/CFM\r\n    },\r\n    DUCT_LEAKAGE: {\r\n      SUPPLY: 4, // % of design airflow\r\n      RETURN: 3, // % of design airflow\r\n      EXHAUST: 4 // % of design airflow\r\n    },\r\n    INSULATION: {\r\n      SUPPLY_OUTDOOR: 8, // R-value\r\n      SUPPLY_UNCONDITIONED: 6, // R-value\r\n      RETURN_OUTDOOR: 6, // R-value\r\n      RETURN_UNCONDITIONED: 4 // R-value\r\n    }\r\n  };\r\n\r\n  // SMACNA Standards\r\n  private static readonly SMACNA_LIMITS = {\r\n    DUCT_CONSTRUCTION: {\r\n      MAX_PRESSURE: {\r\n        RECTANGULAR: 10, // in. w.g.\r\n        ROUND: 20 // in. w.g.\r\n      },\r\n      REINFORCEMENT: {\r\n        RECTANGULAR_SPACING: 48, // inches\r\n        ROUND_SPACING: 60 // inches\r\n      }\r\n    },\r\n    LEAKAGE_CLASS: {\r\n      CLASS_1: 4, // CFM/100 sq ft @ 1\" w.g.\r\n      CLASS_2: 6,\r\n      CLASS_3: 12,\r\n      CLASS_6: 30\r\n    }\r\n  };\r\n\r\n  // Energy Code Limits\r\n  private static readonly ENERGY_CODE_LIMITS = {\r\n    IECC: {\r\n      FAN_POWER: 1.25, // W/CFM\r\n      DUCT_SEALING: 'Required',\r\n      INSULATION_ZONES: {\r\n        ZONE_1_2: 4, // R-value\r\n        ZONE_3_4: 6,\r\n        ZONE_5_6: 8,\r\n        ZONE_7_8: 8\r\n      }\r\n    },\r\n    TITLE_24: {\r\n      FAN_POWER: 1.0, // W/CFM (California)\r\n      DUCT_LEAKAGE: 6, // % of design airflow\r\n      INSULATION: 8 // R-value\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Perform comprehensive compliance analysis\r\n   */\r\n  public static async performComplianceAnalysis(\r\n    systemConfiguration: SystemConfiguration,\r\n    performanceMetrics: PerformanceMetrics,\r\n    energyAnalysis: EnergyAnalysis,\r\n    environmentalAnalysis: EnvironmentalImpactAnalysis,\r\n    applicableCodes?: string[],\r\n    projectLocation?: ProjectLocation\r\n  ): Promise<ComplianceAnalysis> {\r\n    try {\r\n      const analysisId = this.generateAnalysisId(systemConfiguration.id);\r\n      const timestamp = new Date();\r\n\r\n      // Determine applicable standards\r\n      const applicableStandards = this.determineApplicableStandards(applicableCodes, projectLocation);\r\n\r\n      // Check ASHRAE compliance\r\n      const ashraeCompliance = await this.checkASHRAECompliance(\r\n        systemConfiguration,\r\n        performanceMetrics,\r\n        energyAnalysis\r\n      );\r\n\r\n      // Check SMACNA compliance\r\n      const smacnaCompliance = await this.checkSMACNACompliance(\r\n        systemConfiguration,\r\n        performanceMetrics\r\n      );\r\n\r\n      // Check energy code compliance\r\n      const energyCodeCompliance = await this.checkEnergyCodeCompliance(\r\n        systemConfiguration,\r\n        energyAnalysis,\r\n        projectLocation\r\n      );\r\n\r\n      // Check environmental compliance\r\n      const environmentalCompliance = await this.checkEnvironmentalCompliance(\r\n        systemConfiguration,\r\n        environmentalAnalysis,\r\n        projectLocation\r\n      );\r\n\r\n      // Check local code compliance\r\n      const localCodeCompliance = await this.checkLocalCodeCompliance(\r\n        systemConfiguration,\r\n        performanceMetrics,\r\n        projectLocation\r\n      );\r\n\r\n      // Aggregate compliance results\r\n      const overallCompliance = this.aggregateComplianceResults([\r\n        ashraeCompliance,\r\n        smacnaCompliance,\r\n        energyCodeCompliance,\r\n        environmentalCompliance,\r\n        localCodeCompliance\r\n      ]);\r\n\r\n      // Generate compliance recommendations\r\n      const recommendations = await this.generateComplianceRecommendations(\r\n        [ashraeCompliance, smacnaCompliance, energyCodeCompliance, environmentalCompliance, localCodeCompliance],\r\n        systemConfiguration\r\n      );\r\n\r\n      // Assess certification requirements\r\n      const certificationRequirements = await this.assessCertificationRequirements(\r\n        overallCompliance,\r\n        environmentalAnalysis,\r\n        projectLocation\r\n      );\r\n\r\n      const analysis: ComplianceAnalysis = {\r\n        id: analysisId,\r\n        systemId: systemConfiguration.id,\r\n        analysisTimestamp: timestamp,\r\n        applicableStandards,\r\n        ashraeCompliance,\r\n        smacnaCompliance,\r\n        energyCodeCompliance,\r\n        environmentalCompliance,\r\n        localCodeCompliance,\r\n        overallCompliance,\r\n        recommendations,\r\n        certificationRequirements\r\n      };\r\n\r\n      // Cache the analysis\r\n      this.COMPLIANCE_CACHE.set(analysisId, analysis);\r\n\r\n      return analysis;\r\n\r\n    } catch (error) {\r\n      throw new Error(`Compliance analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Determine applicable standards based on location and project type\r\n   */\r\n  private static determineApplicableStandards(\r\n    applicableCodes?: string[],\r\n    projectLocation?: ProjectLocation\r\n  ): ComplianceStandard[] {\r\n    const standards: ComplianceStandard[] = [];\r\n\r\n    // Always applicable standards\r\n    standards.push({\r\n      name: 'ASHRAE 90.1',\r\n      version: '2019',\r\n      scope: 'Energy Efficiency',\r\n      mandatory: true,\r\n      description: 'Energy Standard for Buildings Except Low-Rise Residential Buildings'\r\n    });\r\n\r\n    standards.push({\r\n      name: 'SMACNA',\r\n      version: '2006',\r\n      scope: 'Duct Construction',\r\n      mandatory: true,\r\n      description: 'HVAC Duct Construction Standards'\r\n    });\r\n\r\n    // Location-specific standards\r\n    if (projectLocation) {\r\n      if (projectLocation.state === 'CA') {\r\n        standards.push({\r\n          name: 'Title 24',\r\n          version: '2022',\r\n          scope: 'Energy Efficiency',\r\n          mandatory: true,\r\n          description: 'California Building Energy Efficiency Standards'\r\n        });\r\n      }\r\n\r\n      // Add IECC for most US locations\r\n      if (projectLocation.country === 'US') {\r\n        standards.push({\r\n          name: 'IECC',\r\n          version: '2021',\r\n          scope: 'Energy Code',\r\n          mandatory: true,\r\n          description: 'International Energy Conservation Code'\r\n        });\r\n      }\r\n    }\r\n\r\n    // Add any explicitly specified codes\r\n    if (applicableCodes) {\r\n      applicableCodes.forEach(code => {\r\n        if (!standards.find(s => s.name === code)) {\r\n          standards.push({\r\n            name: code,\r\n            version: 'Current',\r\n            scope: 'Local Code',\r\n            mandatory: true,\r\n            description: `Local building code: ${code}`\r\n          });\r\n        }\r\n      });\r\n    }\r\n\r\n    return standards;\r\n  }\r\n\r\n  /**\r\n   * Check ASHRAE 90.1 compliance\r\n   */\r\n  private static async checkASHRAECompliance(\r\n    systemConfiguration: SystemConfiguration,\r\n    performanceMetrics: PerformanceMetrics,\r\n    energyAnalysis: EnergyAnalysis\r\n  ): Promise<ComplianceResult> {\r\n    const checks: ValidationResult[] = [];\r\n\r\n    // Fan power compliance check\r\n    const fanPowerLimit = this.ASHRAE_901_LIMITS.FAN_POWER.SUPPLY_ONLY;\r\n    const actualFanPower = energyAnalysis.efficiencyMetrics.specificFanPower;\r\n    \r\n    checks.push({\r\n      requirement: 'Fan Power Limitation',\r\n      standard: 'ASHRAE 90.1-2019 Section *******',\r\n      limit: `${fanPowerLimit} W/CFM`,\r\n      actual: `${actualFanPower.toFixed(2)} W/CFM`,\r\n      status: actualFanPower <= fanPowerLimit ? ComplianceStatus.COMPLIANT : ComplianceStatus.NON_COMPLIANT,\r\n      severity: actualFanPower <= fanPowerLimit ? 'info' : 'error',\r\n      description: 'Maximum allowable fan power for supply-only systems'\r\n    });\r\n\r\n    // Duct insulation check (simplified)\r\n    const hasAdequateInsulation = this.checkDuctInsulation(systemConfiguration);\r\n    checks.push({\r\n      requirement: 'Duct Insulation',\r\n      standard: 'ASHRAE 90.1-2019 Section 6.4.4',\r\n      limit: 'R-6 minimum for supply ducts in unconditioned spaces',\r\n      actual: hasAdequateInsulation ? 'Adequate' : 'Inadequate',\r\n      status: hasAdequateInsulation ? ComplianceStatus.COMPLIANT : ComplianceStatus.NON_COMPLIANT,\r\n      severity: hasAdequateInsulation ? 'info' : 'warning',\r\n      description: 'Minimum insulation requirements for ductwork'\r\n    });\r\n\r\n    // Duct leakage check (simplified)\r\n    const estimatedLeakage = this.estimateDuctLeakage(systemConfiguration);\r\n    const leakageLimit = this.ASHRAE_901_LIMITS.DUCT_LEAKAGE.SUPPLY;\r\n    \r\n    checks.push({\r\n      requirement: 'Duct Leakage',\r\n      standard: 'ASHRAE 90.1-2019 Section *******',\r\n      limit: `${leakageLimit}% of design airflow`,\r\n      actual: `${estimatedLeakage.toFixed(1)}% estimated`,\r\n      status: estimatedLeakage <= leakageLimit ? ComplianceStatus.COMPLIANT : ComplianceStatus.NON_COMPLIANT,\r\n      severity: estimatedLeakage <= leakageLimit ? 'info' : 'error',\r\n      description: 'Maximum allowable duct leakage for supply systems'\r\n    });\r\n\r\n    // Calculate overall compliance\r\n    const compliantChecks = checks.filter(check => check.status === ComplianceStatus.COMPLIANT).length;\r\n    const overallStatus = compliantChecks === checks.length ? \r\n      ComplianceStatus.COMPLIANT : \r\n      compliantChecks > checks.length / 2 ? \r\n        ComplianceStatus.PARTIALLY_COMPLIANT : \r\n        ComplianceStatus.NON_COMPLIANT;\r\n\r\n    return {\r\n      standard: 'ASHRAE 90.1-2019',\r\n      overallStatus,\r\n      compliancePercentage: (compliantChecks / checks.length) * 100,\r\n      checks,\r\n      summary: `${compliantChecks} of ${checks.length} requirements met`,\r\n      criticalIssues: checks.filter(check => check.severity === 'error').length,\r\n      warnings: checks.filter(check => check.severity === 'warning').length\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Check duct insulation adequacy (simplified)\r\n   */\r\n  private static checkDuctInsulation(systemConfiguration: SystemConfiguration): boolean {\r\n    // Simplified check - assume adequate insulation for now\r\n    // In a real implementation, this would check actual insulation specifications\r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Estimate duct leakage (simplified)\r\n   */\r\n  private static estimateDuctLeakage(systemConfiguration: SystemConfiguration): number {\r\n    // Simplified leakage estimation based on system pressure\r\n    const systemPressure = systemConfiguration.designParameters.designPressure;\r\n    \r\n    // Higher pressure systems tend to have more leakage\r\n    if (systemPressure > 4.0) return 5.5; // Higher leakage\r\n    if (systemPressure > 2.0) return 3.5; // Moderate leakage\r\n    return 2.5; // Lower leakage\r\n  }\r\n\r\n  /**\r\n   * Check SMACNA compliance\r\n   */\r\n  private static async checkSMACNACompliance(\r\n    systemConfiguration: SystemConfiguration,\r\n    performanceMetrics: PerformanceMetrics\r\n  ): Promise<ComplianceResult> {\r\n    const checks: ValidationResult[] = [];\r\n\r\n    // Duct construction pressure limits\r\n    const systemPressure = systemConfiguration.designParameters.designPressure;\r\n    const ductShape = systemConfiguration.ductConfiguration?.shape || 'rectangular';\r\n    const pressureLimit = ductShape === 'round' ?\r\n      this.SMACNA_LIMITS.DUCT_CONSTRUCTION.MAX_PRESSURE.ROUND :\r\n      this.SMACNA_LIMITS.DUCT_CONSTRUCTION.MAX_PRESSURE.RECTANGULAR;\r\n\r\n    checks.push({\r\n      requirement: 'Duct Construction Pressure Limit',\r\n      standard: 'SMACNA HVAC Duct Construction Standards',\r\n      limit: `${pressureLimit} in. w.g. for ${ductShape} ducts`,\r\n      actual: `${systemPressure.toFixed(1)} in. w.g.`,\r\n      status: systemPressure <= pressureLimit ? ComplianceStatus.COMPLIANT : ComplianceStatus.NON_COMPLIANT,\r\n      severity: systemPressure <= pressureLimit ? 'info' : 'error',\r\n      description: 'Maximum allowable static pressure for duct construction class'\r\n    });\r\n\r\n    // Leakage class requirements\r\n    const estimatedLeakage = this.estimateDuctLeakage(systemConfiguration);\r\n    const leakageClass = this.determineSMACNALeakageClass(systemPressure);\r\n    const leakageLimit = this.SMACNA_LIMITS.LEAKAGE_CLASS[leakageClass as keyof typeof this.SMACNA_LIMITS.LEAKAGE_CLASS];\r\n\r\n    checks.push({\r\n      requirement: `SMACNA Leakage ${leakageClass}`,\r\n      standard: 'SMACNA HVAC Duct Construction Standards',\r\n      limit: `${leakageLimit} CFM/100 sq ft @ 1\" w.g.`,\r\n      actual: `${(estimatedLeakage * 2).toFixed(1)} CFM/100 sq ft estimated`,\r\n      status: (estimatedLeakage * 2) <= leakageLimit ? ComplianceStatus.COMPLIANT : ComplianceStatus.NON_COMPLIANT,\r\n      severity: (estimatedLeakage * 2) <= leakageLimit ? 'info' : 'warning',\r\n      description: 'SMACNA duct leakage classification requirements'\r\n    });\r\n\r\n    // Reinforcement requirements (simplified)\r\n    const hasAdequateReinforcement = this.checkDuctReinforcement(systemConfiguration);\r\n    checks.push({\r\n      requirement: 'Duct Reinforcement',\r\n      standard: 'SMACNA HVAC Duct Construction Standards',\r\n      limit: 'Adequate reinforcement per SMACNA standards',\r\n      actual: hasAdequateReinforcement ? 'Adequate' : 'Inadequate',\r\n      status: hasAdequateReinforcement ? ComplianceStatus.COMPLIANT : ComplianceStatus.NON_COMPLIANT,\r\n      severity: hasAdequateReinforcement ? 'info' : 'warning',\r\n      description: 'Duct reinforcement requirements for system pressure class'\r\n    });\r\n\r\n    const compliantChecks = checks.filter(check => check.status === ComplianceStatus.COMPLIANT).length;\r\n\r\n    let overallStatus: ComplianceStatus;\r\n    if (compliantChecks === checks.length) {\r\n      overallStatus = ComplianceStatus.COMPLIANT;\r\n    } else if (compliantChecks > checks.length / 2) {\r\n      overallStatus = ComplianceStatus.PARTIALLY_COMPLIANT;\r\n    } else {\r\n      overallStatus = ComplianceStatus.NON_COMPLIANT;\r\n    }\r\n\r\n    return {\r\n      standard: 'SMACNA HVAC Duct Construction',\r\n      overallStatus,\r\n      compliancePercentage: (compliantChecks / checks.length) * 100,\r\n      checks,\r\n      summary: `${compliantChecks} of ${checks.length} SMACNA requirements met`,\r\n      criticalIssues: checks.filter(check => check.severity === 'error').length,\r\n      warnings: checks.filter(check => check.severity === 'warning').length\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Determine SMACNA leakage class based on system pressure\r\n   */\r\n  private static determineSMACNALeakageClass(pressure: number): string {\r\n    if (pressure <= 2.0) return 'CLASS_3';\r\n    if (pressure <= 4.0) return 'CLASS_2';\r\n    if (pressure <= 6.0) return 'CLASS_1';\r\n    return 'CLASS_1'; // Highest class for high pressure\r\n  }\r\n\r\n  /**\r\n   * Check duct reinforcement adequacy (simplified)\r\n   */\r\n  private static checkDuctReinforcement(systemConfiguration: SystemConfiguration): boolean {\r\n    const systemPressure = systemConfiguration.designParameters.designPressure;\r\n    // Simplified check - assume adequate reinforcement for pressures under 6\" w.g.\r\n    return systemPressure <= 6.0;\r\n  }\r\n\r\n  /**\r\n   * Check energy code compliance\r\n   */\r\n  private static async checkEnergyCodeCompliance(\r\n    systemConfiguration: SystemConfiguration,\r\n    energyAnalysis: EnergyAnalysis,\r\n    projectLocation?: ProjectLocation\r\n  ): Promise<ComplianceResult> {\r\n    const checks: ValidationResult[] = [];\r\n\r\n    // Determine applicable energy code\r\n    const energyCode = this.determineEnergyCode(projectLocation);\r\n    const fanPowerLimit = this.getEnergyCodeFanPowerLimit(energyCode, projectLocation);\r\n    const actualFanPower = energyAnalysis.efficiencyMetrics.specificFanPower;\r\n\r\n    checks.push({\r\n      requirement: 'Energy Code Fan Power Limit',\r\n      standard: `${energyCode} Fan Power Requirements`,\r\n      limit: `${fanPowerLimit} W/CFM`,\r\n      actual: `${actualFanPower.toFixed(2)} W/CFM`,\r\n      status: actualFanPower <= fanPowerLimit ? ComplianceStatus.COMPLIANT : ComplianceStatus.NON_COMPLIANT,\r\n      severity: actualFanPower <= fanPowerLimit ? 'info' : 'error',\r\n      description: `${energyCode} maximum allowable fan power`\r\n    });\r\n\r\n    // Duct sealing requirements\r\n    const hasDuctSealing = this.checkDuctSealing(systemConfiguration);\r\n    checks.push({\r\n      requirement: 'Duct Sealing',\r\n      standard: `${energyCode} Duct Sealing Requirements`,\r\n      limit: 'Duct sealing required per code',\r\n      actual: hasDuctSealing ? 'Compliant' : 'Non-compliant',\r\n      status: hasDuctSealing ? ComplianceStatus.COMPLIANT : ComplianceStatus.NON_COMPLIANT,\r\n      severity: hasDuctSealing ? 'info' : 'error',\r\n      description: 'Energy code duct sealing requirements'\r\n    });\r\n\r\n    const compliantChecks = checks.filter(check => check.status === ComplianceStatus.COMPLIANT).length;\r\n    const overallStatus = compliantChecks === checks.length ?\r\n      ComplianceStatus.COMPLIANT :\r\n      ComplianceStatus.NON_COMPLIANT;\r\n\r\n    return {\r\n      standard: energyCode,\r\n      overallStatus,\r\n      compliancePercentage: (compliantChecks / checks.length) * 100,\r\n      checks,\r\n      summary: `${compliantChecks} of ${checks.length} ${energyCode} requirements met`,\r\n      criticalIssues: checks.filter(check => check.severity === 'error').length,\r\n      warnings: checks.filter(check => check.severity === 'warning').length\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Determine applicable energy code\r\n   */\r\n  private static determineEnergyCode(projectLocation?: ProjectLocation): string {\r\n    if (projectLocation?.state === 'CA') return 'Title 24';\r\n    return 'IECC 2021';\r\n  }\r\n\r\n  /**\r\n   * Get energy code fan power limit\r\n   */\r\n  private static getEnergyCodeFanPowerLimit(energyCode: string, projectLocation?: ProjectLocation): number {\r\n    if (energyCode === 'Title 24') return this.ENERGY_CODE_LIMITS.TITLE_24.FAN_POWER;\r\n    return this.ENERGY_CODE_LIMITS.IECC.FAN_POWER;\r\n  }\r\n\r\n  /**\r\n   * Check duct sealing compliance (simplified)\r\n   */\r\n  private static checkDuctSealing(systemConfiguration: SystemConfiguration): boolean {\r\n    // Simplified check - assume duct sealing is specified\r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Check environmental compliance\r\n   */\r\n  private static async checkEnvironmentalCompliance(\r\n    systemConfiguration: SystemConfiguration,\r\n    environmentalAnalysis: EnvironmentalImpactAnalysis,\r\n    projectLocation?: ProjectLocation\r\n  ): Promise<ComplianceResult> {\r\n    const checks: ValidationResult[] = [];\r\n\r\n    // Carbon emissions compliance (if applicable)\r\n    const carbonIntensity = environmentalAnalysis.carbonFootprint.totalEmissions.value /\r\n                           systemConfiguration.designParameters.designAirflow;\r\n    const carbonLimit = 0.15; // kg CO2e/CFM/year (example limit)\r\n\r\n    checks.push({\r\n      requirement: 'Carbon Emissions Limit',\r\n      standard: 'Environmental Regulations',\r\n      limit: `${carbonLimit} kg CO2e/CFM/year`,\r\n      actual: `${carbonIntensity.toFixed(3)} kg CO2e/CFM/year`,\r\n      status: carbonIntensity <= carbonLimit ? ComplianceStatus.COMPLIANT : ComplianceStatus.NON_COMPLIANT,\r\n      severity: carbonIntensity <= carbonLimit ? 'info' : 'warning',\r\n      description: 'Maximum allowable carbon emissions intensity'\r\n    });\r\n\r\n    // Refrigerant compliance (if applicable)\r\n    const hasLowGWPRefrigerant = this.checkRefrigerantCompliance(systemConfiguration);\r\n    checks.push({\r\n      requirement: 'Refrigerant GWP Limit',\r\n      standard: 'EPA SNAP Program',\r\n      limit: 'Low GWP refrigerants required',\r\n      actual: hasLowGWPRefrigerant ? 'Compliant' : 'Non-compliant',\r\n      status: hasLowGWPRefrigerant ? ComplianceStatus.COMPLIANT : ComplianceStatus.NON_COMPLIANT,\r\n      severity: hasLowGWPRefrigerant ? 'info' : 'warning',\r\n      description: 'EPA refrigerant regulations compliance'\r\n    });\r\n\r\n    const compliantChecks = checks.filter(check => check.status === ComplianceStatus.COMPLIANT).length;\r\n    const overallStatus = compliantChecks === checks.length ?\r\n      ComplianceStatus.COMPLIANT :\r\n      ComplianceStatus.PARTIALLY_COMPLIANT;\r\n\r\n    return {\r\n      standard: 'Environmental Regulations',\r\n      overallStatus,\r\n      compliancePercentage: (compliantChecks / checks.length) * 100,\r\n      checks,\r\n      summary: `${compliantChecks} of ${checks.length} environmental requirements met`,\r\n      criticalIssues: checks.filter(check => check.severity === 'error').length,\r\n      warnings: checks.filter(check => check.severity === 'warning').length\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Check refrigerant compliance (simplified)\r\n   */\r\n  private static checkRefrigerantCompliance(systemConfiguration: SystemConfiguration): boolean {\r\n    // Simplified check - assume compliant refrigerants\r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Check local code compliance\r\n   */\r\n  private static async checkLocalCodeCompliance(\r\n    systemConfiguration: SystemConfiguration,\r\n    performanceMetrics: PerformanceMetrics,\r\n    projectLocation?: ProjectLocation\r\n  ): Promise<ComplianceResult> {\r\n    const checks: ValidationResult[] = [];\r\n\r\n    // Local noise requirements (example)\r\n    const estimatedNoise = this.estimateSystemNoise(systemConfiguration);\r\n    const noiseLimit = 55; // dBA (example local limit)\r\n\r\n    checks.push({\r\n      requirement: 'Noise Level Limit',\r\n      standard: 'Local Building Code',\r\n      limit: `${noiseLimit} dBA`,\r\n      actual: `${estimatedNoise} dBA estimated`,\r\n      status: estimatedNoise <= noiseLimit ? ComplianceStatus.COMPLIANT : ComplianceStatus.NON_COMPLIANT,\r\n      severity: estimatedNoise <= noiseLimit ? 'info' : 'warning',\r\n      description: 'Local noise ordinance compliance'\r\n    });\r\n\r\n    // Fire safety requirements (simplified)\r\n    const hasFireDampers = this.checkFireSafety(systemConfiguration);\r\n    checks.push({\r\n      requirement: 'Fire Safety Systems',\r\n      standard: 'Local Fire Code',\r\n      limit: 'Fire dampers required per code',\r\n      actual: hasFireDampers ? 'Compliant' : 'Non-compliant',\r\n      status: hasFireDampers ? ComplianceStatus.COMPLIANT : ComplianceStatus.NON_COMPLIANT,\r\n      severity: hasFireDampers ? 'info' : 'error',\r\n      description: 'Local fire code requirements for HVAC systems'\r\n    });\r\n\r\n    const compliantChecks = checks.filter(check => check.status === ComplianceStatus.COMPLIANT).length;\r\n    const overallStatus = compliantChecks === checks.length ?\r\n      ComplianceStatus.COMPLIANT :\r\n      ComplianceStatus.PARTIALLY_COMPLIANT;\r\n\r\n    return {\r\n      standard: 'Local Building Code',\r\n      overallStatus,\r\n      compliancePercentage: (compliantChecks / checks.length) * 100,\r\n      checks,\r\n      summary: `${compliantChecks} of ${checks.length} local code requirements met`,\r\n      criticalIssues: checks.filter(check => check.severity === 'error').length,\r\n      warnings: checks.filter(check => check.severity === 'warning').length\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Estimate system noise (simplified)\r\n   */\r\n  private static estimateSystemNoise(systemConfiguration: SystemConfiguration): number {\r\n    const designAirflow = systemConfiguration.designParameters.designAirflow;\r\n    const systemPressure = systemConfiguration.designParameters.designPressure;\r\n\r\n    // Simplified noise estimation\r\n    const baseNoise = 45; // dBA base\r\n    const flowNoise = Math.log10(designAirflow / 1000) * 10; // Flow contribution\r\n    const pressureNoise = systemPressure * 2; // Pressure contribution\r\n\r\n    return Math.round(baseNoise + flowNoise + pressureNoise);\r\n  }\r\n\r\n  /**\r\n   * Check fire safety compliance (simplified)\r\n   */\r\n  private static checkFireSafety(systemConfiguration: SystemConfiguration): boolean {\r\n    // Simplified check - assume fire safety systems are included\r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Aggregate compliance results\r\n   */\r\n  private static aggregateComplianceResults(results: ComplianceResult[]): ComplianceResult {\r\n    const allChecks = results.flatMap(result => result.checks);\r\n    const compliantChecks = allChecks.filter(check => check.status === ComplianceStatus.COMPLIANT).length;\r\n\r\n    let overallStatus: ComplianceStatus;\r\n    const compliancePercentage = (compliantChecks / allChecks.length) * 100;\r\n\r\n    if (compliancePercentage === 100) {\r\n      overallStatus = ComplianceStatus.COMPLIANT;\r\n    } else if (compliancePercentage >= 80) {\r\n      overallStatus = ComplianceStatus.PARTIALLY_COMPLIANT;\r\n    } else {\r\n      overallStatus = ComplianceStatus.NON_COMPLIANT;\r\n    }\r\n\r\n    return {\r\n      standard: 'Overall Compliance',\r\n      overallStatus,\r\n      compliancePercentage,\r\n      checks: allChecks,\r\n      summary: `${compliantChecks} of ${allChecks.length} total requirements met`,\r\n      criticalIssues: allChecks.filter(check => check.severity === 'error').length,\r\n      warnings: allChecks.filter(check => check.severity === 'warning').length\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Generate compliance recommendations\r\n   */\r\n  private static async generateComplianceRecommendations(\r\n    results: ComplianceResult[],\r\n    systemConfiguration: SystemConfiguration\r\n  ): Promise<ComplianceRecommendation[]> {\r\n    const recommendations: ComplianceRecommendation[] = [];\r\n\r\n    // Analyze all non-compliant checks\r\n    const nonCompliantChecks = results.flatMap(result =>\r\n      result.checks.filter(check => check.status !== ComplianceStatus.COMPLIANT)\r\n    );\r\n\r\n    nonCompliantChecks.forEach(check => {\r\n      if (check.requirement.includes('Fan Power')) {\r\n        recommendations.push({\r\n          id: 'fan_power_optimization',\r\n          priority: 'High',\r\n          category: 'Energy Efficiency',\r\n          title: 'Reduce Fan Power Consumption',\r\n          description: 'System exceeds maximum allowable fan power. Consider optimizing ductwork design or upgrading to more efficient equipment.',\r\n          affectedStandards: [check.standard],\r\n          estimatedCost: 15000,\r\n          estimatedSavings: 5000,\r\n          implementationTime: '2-4 weeks',\r\n          actions: [\r\n            'Optimize duct sizing to reduce pressure losses',\r\n            'Consider variable frequency drives (VFDs)',\r\n            'Upgrade to high-efficiency fans',\r\n            'Review system design for oversizing'\r\n          ]\r\n        });\r\n      }\r\n\r\n      if (check.requirement.includes('Leakage')) {\r\n        recommendations.push({\r\n          id: 'duct_sealing_improvement',\r\n          priority: 'Medium',\r\n          category: 'Duct Construction',\r\n          title: 'Improve Duct Sealing',\r\n          description: 'Duct leakage exceeds allowable limits. Enhanced sealing methods are required.',\r\n          affectedStandards: [check.standard],\r\n          estimatedCost: 8000,\r\n          estimatedSavings: 3000,\r\n          implementationTime: '1-2 weeks',\r\n          actions: [\r\n            'Implement comprehensive duct sealing program',\r\n            'Use mastic sealant at all joints',\r\n            'Perform duct blaster testing',\r\n            'Upgrade to higher leakage class construction'\r\n          ]\r\n        });\r\n      }\r\n    });\r\n\r\n    return recommendations;\r\n  }\r\n\r\n  /**\r\n   * Assess certification requirements\r\n   */\r\n  private static async assessCertificationRequirements(\r\n    overallCompliance: ComplianceResult,\r\n    environmentalAnalysis: EnvironmentalImpactAnalysis,\r\n    projectLocation?: ProjectLocation\r\n  ): Promise<CertificationRequirement[]> {\r\n    const requirements: CertificationRequirement[] = [];\r\n\r\n    // LEED certification requirements\r\n    if (overallCompliance.compliancePercentage >= 80) {\r\n      requirements.push({\r\n        certification: 'LEED',\r\n        level: 'Silver',\r\n        status: 'Eligible',\r\n        requiredActions: [\r\n          'Complete energy modeling',\r\n          'Document commissioning process',\r\n          'Provide equipment efficiency documentation'\r\n        ],\r\n        estimatedPoints: 12,\r\n        totalPointsAvailable: 110\r\n      });\r\n    }\r\n\r\n    // ENERGY STAR certification\r\n    if (environmentalAnalysis.sustainabilityMetrics.energyEfficiency.systemEfficiency >= 85) {\r\n      requirements.push({\r\n        certification: 'ENERGY STAR',\r\n        level: 'Certified',\r\n        status: 'Eligible',\r\n        requiredActions: [\r\n          'Submit energy performance data',\r\n          'Complete third-party verification',\r\n          'Maintain performance for 12 months'\r\n        ],\r\n        estimatedPoints: 0, // Pass/fail certification\r\n        totalPointsAvailable: 0\r\n      });\r\n    }\r\n\r\n    return requirements;\r\n  }\r\n\r\n  /**\r\n   * Generate unique analysis ID\r\n   */\r\n  private static generateAnalysisId(systemId: string): string {\r\n    const timestamp = Date.now();\r\n    const random = Math.random().toString(36).substring(2, 8);\r\n    return `compliance_analysis_${systemId}_${timestamp}_${random}`;\r\n  }\r\n}\r\n\r\n// Supporting interfaces\r\ninterface ProjectLocation {\r\n  country: string;\r\n  state: string;\r\n  city: string;\r\n  climateZone: string;\r\n  jurisdiction: string;\r\n}\r\n"], "mappings": ";;AAAA;;;;;;;;;;AAAA;AAAA,SAAAA,cAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;MAAA;QAAAC,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAO,SAAA;MAAA;QAAAD,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;IAAA;IAAAW,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,cAAA;MAAAC,IAAA;MAAAC,QAAA;MAAAC,KAAA;MAAAC,OAAA;MAAAC,cAAA;MAAAC,OAAA;IAAA;IAAAC,eAAA;IAAA5B,IAAA;EAAA;EAAA,IAAA6B,QAAA,GAAA5B,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAA0B,QAAA,CAAA9B,IAAA,KAAA8B,QAAA,CAAA9B,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAA6B,QAAA,CAAA9B,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAA0B,cAAA,GAAAD,QAAA,CAAA9B,IAAA;EAAA;;;;;;;;;;;;;;;;AAWA,MAAAgC,qBAAA;AAAA;AAAA,CAAAjC,aAAA,GAAAoB,CAAA,OAAAc,OAAA;AAcA;;;;;;;;;;;;AAYA,MAAaC,wBAAwB;EA+DnC;;;EAGO,aAAaC,yBAAyBA,CAC3CC,mBAAwC,EACxCC,kBAAsC,EACtCC,cAA8B,EAC9BC,qBAAkD,EAClDC,eAA0B,EAC1BC,eAAiC;IAAA;IAAA1C,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAEjC,IAAI;MACF,MAAMuB,UAAU;MAAA;MAAA,CAAA3C,aAAA,GAAAoB,CAAA,OAAG,IAAI,CAACwB,kBAAkB,CAACP,mBAAmB,CAACQ,EAAE,CAAC;MAClE,MAAMC,SAAS;MAAA;MAAA,CAAA9C,aAAA,GAAAoB,CAAA,OAAG,IAAI2B,IAAI,EAAE;MAE5B;MACA,MAAMC,mBAAmB;MAAA;MAAA,CAAAhD,aAAA,GAAAoB,CAAA,OAAG,IAAI,CAAC6B,4BAA4B,CAACR,eAAe,EAAEC,eAAe,CAAC;MAE/F;MACA,MAAMQ,gBAAgB;MAAA;MAAA,CAAAlD,aAAA,GAAAoB,CAAA,OAAG,MAAM,IAAI,CAAC+B,qBAAqB,CACvDd,mBAAmB,EACnBC,kBAAkB,EAClBC,cAAc,CACf;MAED;MACA,MAAMa,gBAAgB;MAAA;MAAA,CAAApD,aAAA,GAAAoB,CAAA,OAAG,MAAM,IAAI,CAACiC,qBAAqB,CACvDhB,mBAAmB,EACnBC,kBAAkB,CACnB;MAED;MACA,MAAMgB,oBAAoB;MAAA;MAAA,CAAAtD,aAAA,GAAAoB,CAAA,OAAG,MAAM,IAAI,CAACmC,yBAAyB,CAC/DlB,mBAAmB,EACnBE,cAAc,EACdG,eAAe,CAChB;MAED;MACA,MAAMc,uBAAuB;MAAA;MAAA,CAAAxD,aAAA,GAAAoB,CAAA,QAAG,MAAM,IAAI,CAACqC,4BAA4B,CACrEpB,mBAAmB,EACnBG,qBAAqB,EACrBE,eAAe,CAChB;MAED;MACA,MAAMgB,mBAAmB;MAAA;MAAA,CAAA1D,aAAA,GAAAoB,CAAA,QAAG,MAAM,IAAI,CAACuC,wBAAwB,CAC7DtB,mBAAmB,EACnBC,kBAAkB,EAClBI,eAAe,CAChB;MAED;MACA,MAAMkB,iBAAiB;MAAA;MAAA,CAAA5D,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAACyC,0BAA0B,CAAC,CACxDX,gBAAgB,EAChBE,gBAAgB,EAChBE,oBAAoB,EACpBE,uBAAuB,EACvBE,mBAAmB,CACpB,CAAC;MAEF;MACA,MAAMI,eAAe;MAAA;MAAA,CAAA9D,aAAA,GAAAoB,CAAA,QAAG,MAAM,IAAI,CAAC2C,iCAAiC,CAClE,CAACb,gBAAgB,EAAEE,gBAAgB,EAAEE,oBAAoB,EAAEE,uBAAuB,EAAEE,mBAAmB,CAAC,EACxGrB,mBAAmB,CACpB;MAED;MACA,MAAM2B,yBAAyB;MAAA;MAAA,CAAAhE,aAAA,GAAAoB,CAAA,QAAG,MAAM,IAAI,CAAC6C,+BAA+B,CAC1EL,iBAAiB,EACjBpB,qBAAqB,EACrBE,eAAe,CAChB;MAED,MAAMwB,QAAQ;MAAA;MAAA,CAAAlE,aAAA,GAAAoB,CAAA,QAAuB;QACnCyB,EAAE,EAAEF,UAAU;QACdwB,QAAQ,EAAE9B,mBAAmB,CAACQ,EAAE;QAChCuB,iBAAiB,EAAEtB,SAAS;QAC5BE,mBAAmB;QACnBE,gBAAgB;QAChBE,gBAAgB;QAChBE,oBAAoB;QACpBE,uBAAuB;QACvBE,mBAAmB;QACnBE,iBAAiB;QACjBE,eAAe;QACfE;OACD;MAED;MAAA;MAAAhE,aAAA,GAAAoB,CAAA;MACA,IAAI,CAACiD,gBAAgB,CAACC,GAAG,CAAC3B,UAAU,EAAEuB,QAAQ,CAAC;MAAC;MAAAlE,aAAA,GAAAoB,CAAA;MAEhD,OAAO8C,QAAQ;IAEjB,CAAC,CAAC,OAAOK,KAAK,EAAE;MAAA;MAAAvE,aAAA,GAAAoB,CAAA;MACd,MAAM,IAAIoD,KAAK,CAAC,+BAA+BD,KAAK,YAAYC,KAAK;MAAA;MAAA,CAAAxE,aAAA,GAAAsB,CAAA,UAAGiD,KAAK,CAACE,OAAO;MAAA;MAAA,CAAAzE,aAAA,GAAAsB,CAAA,UAAG,eAAe,GAAE,CAAC;IAC5G;EACF;EAEA;;;EAGQ,OAAO2B,4BAA4BA,CACzCR,eAA0B,EAC1BC,eAAiC;IAAA;IAAA1C,aAAA,GAAAqB,CAAA;IAEjC,MAAMqD,SAAS;IAAA;IAAA,CAAA1E,aAAA,GAAAoB,CAAA,QAAyB,EAAE;IAE1C;IAAA;IAAApB,aAAA,GAAAoB,CAAA;IACAsD,SAAS,CAACC,IAAI,CAAC;MACb9D,IAAI,EAAE,aAAa;MACnBgB,OAAO,EAAE,MAAM;MACf+C,KAAK,EAAE,mBAAmB;MAC1BC,SAAS,EAAE,IAAI;MACfC,WAAW,EAAE;KACd,CAAC;IAAC;IAAA9E,aAAA,GAAAoB,CAAA;IAEHsD,SAAS,CAACC,IAAI,CAAC;MACb9D,IAAI,EAAE,QAAQ;MACdgB,OAAO,EAAE,MAAM;MACf+C,KAAK,EAAE,mBAAmB;MAC1BC,SAAS,EAAE,IAAI;MACfC,WAAW,EAAE;KACd,CAAC;IAEF;IAAA;IAAA9E,aAAA,GAAAoB,CAAA;IACA,IAAIsB,eAAe,EAAE;MAAA;MAAA1C,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACnB,IAAIsB,eAAe,CAACqC,KAAK,KAAK,IAAI,EAAE;QAAA;QAAA/E,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QAClCsD,SAAS,CAACC,IAAI,CAAC;UACb9D,IAAI,EAAE,UAAU;UAChBgB,OAAO,EAAE,MAAM;UACf+C,KAAK,EAAE,mBAAmB;UAC1BC,SAAS,EAAE,IAAI;UACfC,WAAW,EAAE;SACd,CAAC;MACJ,CAAC;MAAA;MAAA;QAAA9E,aAAA,GAAAsB,CAAA;MAAA;MAED;MAAAtB,aAAA,GAAAoB,CAAA;MACA,IAAIsB,eAAe,CAACsC,OAAO,KAAK,IAAI,EAAE;QAAA;QAAAhF,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACpCsD,SAAS,CAACC,IAAI,CAAC;UACb9D,IAAI,EAAE,MAAM;UACZgB,OAAO,EAAE,MAAM;UACf+C,KAAK,EAAE,aAAa;UACpBC,SAAS,EAAE,IAAI;UACfC,WAAW,EAAE;SACd,CAAC;MACJ,CAAC;MAAA;MAAA;QAAA9E,aAAA,GAAAsB,CAAA;MAAA;IACH,CAAC;IAAA;IAAA;MAAAtB,aAAA,GAAAsB,CAAA;IAAA;IAED;IAAAtB,aAAA,GAAAoB,CAAA;IACA,IAAIqB,eAAe,EAAE;MAAA;MAAAzC,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACnBqB,eAAe,CAACwC,OAAO,CAACC,IAAI,IAAG;QAAA;QAAAlF,aAAA,GAAAqB,CAAA;QAAArB,aAAA,GAAAoB,CAAA;QAC7B,IAAI,CAACsD,SAAS,CAACS,IAAI,CAAC/D,CAAC,IAAI;UAAA;UAAApB,aAAA,GAAAqB,CAAA;UAAArB,aAAA,GAAAoB,CAAA;UAAA,OAAAA,CAAC,CAACP,IAAI,KAAKqE,IAAI;QAAJ,CAAI,CAAC,EAAE;UAAA;UAAAlF,aAAA,GAAAsB,CAAA;UAAAtB,aAAA,GAAAoB,CAAA;UACzCsD,SAAS,CAACC,IAAI,CAAC;YACb9D,IAAI,EAAEqE,IAAI;YACVrD,OAAO,EAAE,SAAS;YAClB+C,KAAK,EAAE,YAAY;YACnBC,SAAS,EAAE,IAAI;YACfC,WAAW,EAAE,wBAAwBI,IAAI;WAC1C,CAAC;QACJ,CAAC;QAAA;QAAA;UAAAlF,aAAA,GAAAsB,CAAA;QAAA;MACH,CAAC,CAAC;IACJ,CAAC;IAAA;IAAA;MAAAtB,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAED,OAAOsD,SAAS;EAClB;EAEA;;;EAGQ,aAAavB,qBAAqBA,CACxCd,mBAAwC,EACxCC,kBAAsC,EACtCC,cAA8B;IAAA;IAAAvC,aAAA,GAAAqB,CAAA;IAE9B,MAAM+D,MAAM;IAAA;IAAA,CAAApF,aAAA,GAAAoB,CAAA,QAAuB,EAAE;IAErC;IACA,MAAMiE,aAAa;IAAA;IAAA,CAAArF,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAACkE,iBAAiB,CAACC,SAAS,CAACC,WAAW;IAClE,MAAMC,cAAc;IAAA;IAAA,CAAAzF,aAAA,GAAAoB,CAAA,QAAGmB,cAAc,CAACmD,iBAAiB,CAACC,gBAAgB;IAAC;IAAA3F,aAAA,GAAAoB,CAAA;IAEzEgE,MAAM,CAACT,IAAI,CAAC;MACViB,WAAW,EAAE,sBAAsB;MACnCC,QAAQ,EAAE,kCAAkC;MAC5CC,KAAK,EAAE,GAAGT,aAAa,QAAQ;MAC/BU,MAAM,EAAE,GAAGN,cAAc,CAACO,OAAO,CAAC,CAAC,CAAC,QAAQ;MAC5CC,MAAM,EAAER,cAAc,IAAIJ,aAAa;MAAA;MAAA,CAAArF,aAAA,GAAAsB,CAAA,UAAGW,qBAAA,CAAAiE,gBAAgB,CAACC,SAAS;MAAA;MAAA,CAAAnG,aAAA,GAAAsB,CAAA,UAAGW,qBAAA,CAAAiE,gBAAgB,CAACE,aAAa;MACrGC,QAAQ,EAAEZ,cAAc,IAAIJ,aAAa;MAAA;MAAA,CAAArF,aAAA,GAAAsB,CAAA,UAAG,MAAM;MAAA;MAAA,CAAAtB,aAAA,GAAAsB,CAAA,UAAG,OAAO;MAC5DwD,WAAW,EAAE;KACd,CAAC;IAEF;IACA,MAAMwB,qBAAqB;IAAA;IAAA,CAAAtG,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAACmF,mBAAmB,CAAClE,mBAAmB,CAAC;IAAC;IAAArC,aAAA,GAAAoB,CAAA;IAC5EgE,MAAM,CAACT,IAAI,CAAC;MACViB,WAAW,EAAE,iBAAiB;MAC9BC,QAAQ,EAAE,gCAAgC;MAC1CC,KAAK,EAAE,sDAAsD;MAC7DC,MAAM,EAAEO,qBAAqB;MAAA;MAAA,CAAAtG,aAAA,GAAAsB,CAAA,UAAG,UAAU;MAAA;MAAA,CAAAtB,aAAA,GAAAsB,CAAA,UAAG,YAAY;MACzD2E,MAAM,EAAEK,qBAAqB;MAAA;MAAA,CAAAtG,aAAA,GAAAsB,CAAA,UAAGW,qBAAA,CAAAiE,gBAAgB,CAACC,SAAS;MAAA;MAAA,CAAAnG,aAAA,GAAAsB,CAAA,UAAGW,qBAAA,CAAAiE,gBAAgB,CAACE,aAAa;MAC3FC,QAAQ,EAAEC,qBAAqB;MAAA;MAAA,CAAAtG,aAAA,GAAAsB,CAAA,WAAG,MAAM;MAAA;MAAA,CAAAtB,aAAA,GAAAsB,CAAA,WAAG,SAAS;MACpDwD,WAAW,EAAE;KACd,CAAC;IAEF;IACA,MAAM0B,gBAAgB;IAAA;IAAA,CAAAxG,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAACqF,mBAAmB,CAACpE,mBAAmB,CAAC;IACtE,MAAMqE,YAAY;IAAA;IAAA,CAAA1G,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAACkE,iBAAiB,CAACqB,YAAY,CAACC,MAAM;IAAC;IAAA5G,aAAA,GAAAoB,CAAA;IAEhEgE,MAAM,CAACT,IAAI,CAAC;MACViB,WAAW,EAAE,cAAc;MAC3BC,QAAQ,EAAE,kCAAkC;MAC5CC,KAAK,EAAE,GAAGY,YAAY,qBAAqB;MAC3CX,MAAM,EAAE,GAAGS,gBAAgB,CAACR,OAAO,CAAC,CAAC,CAAC,aAAa;MACnDC,MAAM,EAAEO,gBAAgB,IAAIE,YAAY;MAAA;MAAA,CAAA1G,aAAA,GAAAsB,CAAA,WAAGW,qBAAA,CAAAiE,gBAAgB,CAACC,SAAS;MAAA;MAAA,CAAAnG,aAAA,GAAAsB,CAAA,WAAGW,qBAAA,CAAAiE,gBAAgB,CAACE,aAAa;MACtGC,QAAQ,EAAEG,gBAAgB,IAAIE,YAAY;MAAA;MAAA,CAAA1G,aAAA,GAAAsB,CAAA,WAAG,MAAM;MAAA;MAAA,CAAAtB,aAAA,GAAAsB,CAAA,WAAG,OAAO;MAC7DwD,WAAW,EAAE;KACd,CAAC;IAEF;IACA,MAAM+B,eAAe;IAAA;IAAA,CAAA7G,aAAA,GAAAoB,CAAA,QAAGgE,MAAM,CAAC0B,MAAM,CAACC,KAAK,IAAI;MAAA;MAAA/G,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MAAA,OAAA2F,KAAK,CAACd,MAAM,KAAKhE,qBAAA,CAAAiE,gBAAgB,CAACC,SAAS;IAAT,CAAS,CAAC,CAACa,MAAM;IAClG,MAAMC,aAAa;IAAA;IAAA,CAAAjH,aAAA,GAAAoB,CAAA,QAAGyF,eAAe,KAAKzB,MAAM,CAAC4B,MAAM;IAAA;IAAA,CAAAhH,aAAA,GAAAsB,CAAA,WACrDW,qBAAA,CAAAiE,gBAAgB,CAACC,SAAS;IAAA;IAAA,CAAAnG,aAAA,GAAAsB,CAAA,WAC1BuF,eAAe,GAAGzB,MAAM,CAAC4B,MAAM,GAAG,CAAC;IAAA;IAAA,CAAAhH,aAAA,GAAAsB,CAAA,WACjCW,qBAAA,CAAAiE,gBAAgB,CAACgB,mBAAmB;IAAA;IAAA,CAAAlH,aAAA,GAAAsB,CAAA,WACpCW,qBAAA,CAAAiE,gBAAgB,CAACE,aAAa;IAAC;IAAApG,aAAA,GAAAoB,CAAA;IAEnC,OAAO;MACLyE,QAAQ,EAAE,kBAAkB;MAC5BoB,aAAa;MACbE,oBAAoB,EAAGN,eAAe,GAAGzB,MAAM,CAAC4B,MAAM,GAAI,GAAG;MAC7D5B,MAAM;MACNgC,OAAO,EAAE,GAAGP,eAAe,OAAOzB,MAAM,CAAC4B,MAAM,mBAAmB;MAClEK,cAAc,EAAEjC,MAAM,CAAC0B,MAAM,CAACC,KAAK,IAAI;QAAA;QAAA/G,aAAA,GAAAqB,CAAA;QAAArB,aAAA,GAAAoB,CAAA;QAAA,OAAA2F,KAAK,CAACV,QAAQ,KAAK,OAAO;MAAP,CAAO,CAAC,CAACW,MAAM;MACzEM,QAAQ,EAAElC,MAAM,CAAC0B,MAAM,CAACC,KAAK,IAAI;QAAA;QAAA/G,aAAA,GAAAqB,CAAA;QAAArB,aAAA,GAAAoB,CAAA;QAAA,OAAA2F,KAAK,CAACV,QAAQ,KAAK,SAAS;MAAT,CAAS,CAAC,CAACW;KAChE;EACH;EAEA;;;EAGQ,OAAOT,mBAAmBA,CAAClE,mBAAwC;IAAA;IAAArC,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACzE;IACA;IACA,OAAO,IAAI;EACb;EAEA;;;EAGQ,OAAOqF,mBAAmBA,CAACpE,mBAAwC;IAAA;IAAArC,aAAA,GAAAqB,CAAA;IACzE;IACA,MAAMkG,cAAc;IAAA;IAAA,CAAAvH,aAAA,GAAAoB,CAAA,QAAGiB,mBAAmB,CAACmF,gBAAgB,CAACC,cAAc;IAE1E;IAAA;IAAAzH,aAAA,GAAAoB,CAAA;IACA,IAAImG,cAAc,GAAG,GAAG,EAAE;MAAA;MAAAvH,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA,OAAO,GAAG;IAAA,CAAC;IAAA;IAAA;MAAApB,aAAA,GAAAsB,CAAA;IAAA,EAAC;IAAAtB,aAAA,GAAAoB,CAAA;IACtC,IAAImG,cAAc,GAAG,GAAG,EAAE;MAAA;MAAAvH,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA,OAAO,GAAG;IAAA,CAAC;IAAA;IAAA;MAAApB,aAAA,GAAAsB,CAAA;IAAA,EAAC;IAAAtB,aAAA,GAAAoB,CAAA;IACtC,OAAO,GAAG,CAAC,CAAC;EACd;EAEA;;;EAGQ,aAAaiC,qBAAqBA,CACxChB,mBAAwC,EACxCC,kBAAsC;IAAA;IAAAtC,aAAA,GAAAqB,CAAA;IAEtC,MAAM+D,MAAM;IAAA;IAAA,CAAApF,aAAA,GAAAoB,CAAA,QAAuB,EAAE;IAErC;IACA,MAAMmG,cAAc;IAAA;IAAA,CAAAvH,aAAA,GAAAoB,CAAA,QAAGiB,mBAAmB,CAACmF,gBAAgB,CAACC,cAAc;IAC1E,MAAMC,SAAS;IAAA;IAAA,CAAA1H,aAAA,GAAAoB,CAAA;IAAG;IAAA,CAAApB,aAAA,GAAAsB,CAAA,WAAAe,mBAAmB,CAACsF,iBAAiB,EAAEC,KAAK;IAAA;IAAA,CAAA5H,aAAA,GAAAsB,CAAA,WAAI,aAAa;IAC/E,MAAMuG,aAAa;IAAA;IAAA,CAAA7H,aAAA,GAAAoB,CAAA,QAAGsG,SAAS,KAAK,OAAO;IAAA;IAAA,CAAA1H,aAAA,GAAAsB,CAAA,WACzC,IAAI,CAACwG,aAAa,CAACC,iBAAiB,CAACC,YAAY,CAACC,KAAK;IAAA;IAAA,CAAAjI,aAAA,GAAAsB,CAAA,WACvD,IAAI,CAACwG,aAAa,CAACC,iBAAiB,CAACC,YAAY,CAACE,WAAW;IAAC;IAAAlI,aAAA,GAAAoB,CAAA;IAEhEgE,MAAM,CAACT,IAAI,CAAC;MACViB,WAAW,EAAE,kCAAkC;MAC/CC,QAAQ,EAAE,yCAAyC;MACnDC,KAAK,EAAE,GAAG+B,aAAa,iBAAiBH,SAAS,QAAQ;MACzD3B,MAAM,EAAE,GAAGwB,cAAc,CAACvB,OAAO,CAAC,CAAC,CAAC,WAAW;MAC/CC,MAAM,EAAEsB,cAAc,IAAIM,aAAa;MAAA;MAAA,CAAA7H,aAAA,GAAAsB,CAAA,WAAGW,qBAAA,CAAAiE,gBAAgB,CAACC,SAAS;MAAA;MAAA,CAAAnG,aAAA,GAAAsB,CAAA,WAAGW,qBAAA,CAAAiE,gBAAgB,CAACE,aAAa;MACrGC,QAAQ,EAAEkB,cAAc,IAAIM,aAAa;MAAA;MAAA,CAAA7H,aAAA,GAAAsB,CAAA,WAAG,MAAM;MAAA;MAAA,CAAAtB,aAAA,GAAAsB,CAAA,WAAG,OAAO;MAC5DwD,WAAW,EAAE;KACd,CAAC;IAEF;IACA,MAAM0B,gBAAgB;IAAA;IAAA,CAAAxG,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAACqF,mBAAmB,CAACpE,mBAAmB,CAAC;IACtE,MAAM8F,YAAY;IAAA;IAAA,CAAAnI,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAACgH,2BAA2B,CAACb,cAAc,CAAC;IACrE,MAAMb,YAAY;IAAA;IAAA,CAAA1G,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAAC0G,aAAa,CAACO,aAAa,CAACF,YAA6D,CAAC;IAAC;IAAAnI,aAAA,GAAAoB,CAAA;IAErHgE,MAAM,CAACT,IAAI,CAAC;MACViB,WAAW,EAAE,kBAAkBuC,YAAY,EAAE;MAC7CtC,QAAQ,EAAE,yCAAyC;MACnDC,KAAK,EAAE,GAAGY,YAAY,0BAA0B;MAChDX,MAAM,EAAE,GAAG,CAACS,gBAAgB,GAAG,CAAC,EAAER,OAAO,CAAC,CAAC,CAAC,0BAA0B;MACtEC,MAAM,EAAGO,gBAAgB,GAAG,CAAC,IAAKE,YAAY;MAAA;MAAA,CAAA1G,aAAA,GAAAsB,CAAA,WAAGW,qBAAA,CAAAiE,gBAAgB,CAACC,SAAS;MAAA;MAAA,CAAAnG,aAAA,GAAAsB,CAAA,WAAGW,qBAAA,CAAAiE,gBAAgB,CAACE,aAAa;MAC5GC,QAAQ,EAAGG,gBAAgB,GAAG,CAAC,IAAKE,YAAY;MAAA;MAAA,CAAA1G,aAAA,GAAAsB,CAAA,WAAG,MAAM;MAAA;MAAA,CAAAtB,aAAA,GAAAsB,CAAA,WAAG,SAAS;MACrEwD,WAAW,EAAE;KACd,CAAC;IAEF;IACA,MAAMwD,wBAAwB;IAAA;IAAA,CAAAtI,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAACmH,sBAAsB,CAAClG,mBAAmB,CAAC;IAAC;IAAArC,aAAA,GAAAoB,CAAA;IAClFgE,MAAM,CAACT,IAAI,CAAC;MACViB,WAAW,EAAE,oBAAoB;MACjCC,QAAQ,EAAE,yCAAyC;MACnDC,KAAK,EAAE,6CAA6C;MACpDC,MAAM,EAAEuC,wBAAwB;MAAA;MAAA,CAAAtI,aAAA,GAAAsB,CAAA,WAAG,UAAU;MAAA;MAAA,CAAAtB,aAAA,GAAAsB,CAAA,WAAG,YAAY;MAC5D2E,MAAM,EAAEqC,wBAAwB;MAAA;MAAA,CAAAtI,aAAA,GAAAsB,CAAA,WAAGW,qBAAA,CAAAiE,gBAAgB,CAACC,SAAS;MAAA;MAAA,CAAAnG,aAAA,GAAAsB,CAAA,WAAGW,qBAAA,CAAAiE,gBAAgB,CAACE,aAAa;MAC9FC,QAAQ,EAAEiC,wBAAwB;MAAA;MAAA,CAAAtI,aAAA,GAAAsB,CAAA,WAAG,MAAM;MAAA;MAAA,CAAAtB,aAAA,GAAAsB,CAAA,WAAG,SAAS;MACvDwD,WAAW,EAAE;KACd,CAAC;IAEF,MAAM+B,eAAe;IAAA;IAAA,CAAA7G,aAAA,GAAAoB,CAAA,QAAGgE,MAAM,CAAC0B,MAAM,CAACC,KAAK,IAAI;MAAA;MAAA/G,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MAAA,OAAA2F,KAAK,CAACd,MAAM,KAAKhE,qBAAA,CAAAiE,gBAAgB,CAACC,SAAS;IAAT,CAAS,CAAC,CAACa,MAAM;IAElG,IAAIC,aAA+B;IAAC;IAAAjH,aAAA,GAAAoB,CAAA;IACpC,IAAIyF,eAAe,KAAKzB,MAAM,CAAC4B,MAAM,EAAE;MAAA;MAAAhH,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACrC6F,aAAa,GAAGhF,qBAAA,CAAAiE,gBAAgB,CAACC,SAAS;IAC5C,CAAC,MAAM;MAAA;MAAAnG,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA,IAAIyF,eAAe,GAAGzB,MAAM,CAAC4B,MAAM,GAAG,CAAC,EAAE;QAAA;QAAAhH,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QAC9C6F,aAAa,GAAGhF,qBAAA,CAAAiE,gBAAgB,CAACgB,mBAAmB;MACtD,CAAC,MAAM;QAAA;QAAAlH,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACL6F,aAAa,GAAGhF,qBAAA,CAAAiE,gBAAgB,CAACE,aAAa;MAChD;IAAA;IAAC;IAAApG,aAAA,GAAAoB,CAAA;IAED,OAAO;MACLyE,QAAQ,EAAE,+BAA+B;MACzCoB,aAAa;MACbE,oBAAoB,EAAGN,eAAe,GAAGzB,MAAM,CAAC4B,MAAM,GAAI,GAAG;MAC7D5B,MAAM;MACNgC,OAAO,EAAE,GAAGP,eAAe,OAAOzB,MAAM,CAAC4B,MAAM,0BAA0B;MACzEK,cAAc,EAAEjC,MAAM,CAAC0B,MAAM,CAACC,KAAK,IAAI;QAAA;QAAA/G,aAAA,GAAAqB,CAAA;QAAArB,aAAA,GAAAoB,CAAA;QAAA,OAAA2F,KAAK,CAACV,QAAQ,KAAK,OAAO;MAAP,CAAO,CAAC,CAACW,MAAM;MACzEM,QAAQ,EAAElC,MAAM,CAAC0B,MAAM,CAACC,KAAK,IAAI;QAAA;QAAA/G,aAAA,GAAAqB,CAAA;QAAArB,aAAA,GAAAoB,CAAA;QAAA,OAAA2F,KAAK,CAACV,QAAQ,KAAK,SAAS;MAAT,CAAS,CAAC,CAACW;KAChE;EACH;EAEA;;;EAGQ,OAAOoB,2BAA2BA,CAACI,QAAgB;IAAA;IAAAxI,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACzD,IAAIoH,QAAQ,IAAI,GAAG,EAAE;MAAA;MAAAxI,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA,OAAO,SAAS;IAAA,CAAC;IAAA;IAAA;MAAApB,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IACtC,IAAIoH,QAAQ,IAAI,GAAG,EAAE;MAAA;MAAAxI,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA,OAAO,SAAS;IAAA,CAAC;IAAA;IAAA;MAAApB,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IACtC,IAAIoH,QAAQ,IAAI,GAAG,EAAE;MAAA;MAAAxI,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA,OAAO,SAAS;IAAA,CAAC;IAAA;IAAA;MAAApB,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IACtC,OAAO,SAAS,CAAC,CAAC;EACpB;EAEA;;;EAGQ,OAAOmH,sBAAsBA,CAAClG,mBAAwC;IAAA;IAAArC,aAAA,GAAAqB,CAAA;IAC5E,MAAMkG,cAAc;IAAA;IAAA,CAAAvH,aAAA,GAAAoB,CAAA,QAAGiB,mBAAmB,CAACmF,gBAAgB,CAACC,cAAc;IAC1E;IAAA;IAAAzH,aAAA,GAAAoB,CAAA;IACA,OAAOmG,cAAc,IAAI,GAAG;EAC9B;EAEA;;;EAGQ,aAAahE,yBAAyBA,CAC5ClB,mBAAwC,EACxCE,cAA8B,EAC9BG,eAAiC;IAAA;IAAA1C,aAAA,GAAAqB,CAAA;IAEjC,MAAM+D,MAAM;IAAA;IAAA,CAAApF,aAAA,GAAAoB,CAAA,QAAuB,EAAE;IAErC;IACA,MAAMqH,UAAU;IAAA;IAAA,CAAAzI,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAACsH,mBAAmB,CAAChG,eAAe,CAAC;IAC5D,MAAM2C,aAAa;IAAA;IAAA,CAAArF,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAACuH,0BAA0B,CAACF,UAAU,EAAE/F,eAAe,CAAC;IAClF,MAAM+C,cAAc;IAAA;IAAA,CAAAzF,aAAA,GAAAoB,CAAA,QAAGmB,cAAc,CAACmD,iBAAiB,CAACC,gBAAgB;IAAC;IAAA3F,aAAA,GAAAoB,CAAA;IAEzEgE,MAAM,CAACT,IAAI,CAAC;MACViB,WAAW,EAAE,6BAA6B;MAC1CC,QAAQ,EAAE,GAAG4C,UAAU,yBAAyB;MAChD3C,KAAK,EAAE,GAAGT,aAAa,QAAQ;MAC/BU,MAAM,EAAE,GAAGN,cAAc,CAACO,OAAO,CAAC,CAAC,CAAC,QAAQ;MAC5CC,MAAM,EAAER,cAAc,IAAIJ,aAAa;MAAA;MAAA,CAAArF,aAAA,GAAAsB,CAAA,WAAGW,qBAAA,CAAAiE,gBAAgB,CAACC,SAAS;MAAA;MAAA,CAAAnG,aAAA,GAAAsB,CAAA,WAAGW,qBAAA,CAAAiE,gBAAgB,CAACE,aAAa;MACrGC,QAAQ,EAAEZ,cAAc,IAAIJ,aAAa;MAAA;MAAA,CAAArF,aAAA,GAAAsB,CAAA,WAAG,MAAM;MAAA;MAAA,CAAAtB,aAAA,GAAAsB,CAAA,WAAG,OAAO;MAC5DwD,WAAW,EAAE,GAAG2D,UAAU;KAC3B,CAAC;IAEF;IACA,MAAMG,cAAc;IAAA;IAAA,CAAA5I,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAACyH,gBAAgB,CAACxG,mBAAmB,CAAC;IAAC;IAAArC,aAAA,GAAAoB,CAAA;IAClEgE,MAAM,CAACT,IAAI,CAAC;MACViB,WAAW,EAAE,cAAc;MAC3BC,QAAQ,EAAE,GAAG4C,UAAU,4BAA4B;MACnD3C,KAAK,EAAE,gCAAgC;MACvCC,MAAM,EAAE6C,cAAc;MAAA;MAAA,CAAA5I,aAAA,GAAAsB,CAAA,WAAG,WAAW;MAAA;MAAA,CAAAtB,aAAA,GAAAsB,CAAA,WAAG,eAAe;MACtD2E,MAAM,EAAE2C,cAAc;MAAA;MAAA,CAAA5I,aAAA,GAAAsB,CAAA,WAAGW,qBAAA,CAAAiE,gBAAgB,CAACC,SAAS;MAAA;MAAA,CAAAnG,aAAA,GAAAsB,CAAA,WAAGW,qBAAA,CAAAiE,gBAAgB,CAACE,aAAa;MACpFC,QAAQ,EAAEuC,cAAc;MAAA;MAAA,CAAA5I,aAAA,GAAAsB,CAAA,WAAG,MAAM;MAAA;MAAA,CAAAtB,aAAA,GAAAsB,CAAA,WAAG,OAAO;MAC3CwD,WAAW,EAAE;KACd,CAAC;IAEF,MAAM+B,eAAe;IAAA;IAAA,CAAA7G,aAAA,GAAAoB,CAAA,QAAGgE,MAAM,CAAC0B,MAAM,CAACC,KAAK,IAAI;MAAA;MAAA/G,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MAAA,OAAA2F,KAAK,CAACd,MAAM,KAAKhE,qBAAA,CAAAiE,gBAAgB,CAACC,SAAS;IAAT,CAAS,CAAC,CAACa,MAAM;IAClG,MAAMC,aAAa;IAAA;IAAA,CAAAjH,aAAA,GAAAoB,CAAA,QAAGyF,eAAe,KAAKzB,MAAM,CAAC4B,MAAM;IAAA;IAAA,CAAAhH,aAAA,GAAAsB,CAAA,WACrDW,qBAAA,CAAAiE,gBAAgB,CAACC,SAAS;IAAA;IAAA,CAAAnG,aAAA,GAAAsB,CAAA,WAC1BW,qBAAA,CAAAiE,gBAAgB,CAACE,aAAa;IAAC;IAAApG,aAAA,GAAAoB,CAAA;IAEjC,OAAO;MACLyE,QAAQ,EAAE4C,UAAU;MACpBxB,aAAa;MACbE,oBAAoB,EAAGN,eAAe,GAAGzB,MAAM,CAAC4B,MAAM,GAAI,GAAG;MAC7D5B,MAAM;MACNgC,OAAO,EAAE,GAAGP,eAAe,OAAOzB,MAAM,CAAC4B,MAAM,IAAIyB,UAAU,mBAAmB;MAChFpB,cAAc,EAAEjC,MAAM,CAAC0B,MAAM,CAACC,KAAK,IAAI;QAAA;QAAA/G,aAAA,GAAAqB,CAAA;QAAArB,aAAA,GAAAoB,CAAA;QAAA,OAAA2F,KAAK,CAACV,QAAQ,KAAK,OAAO;MAAP,CAAO,CAAC,CAACW,MAAM;MACzEM,QAAQ,EAAElC,MAAM,CAAC0B,MAAM,CAACC,KAAK,IAAI;QAAA;QAAA/G,aAAA,GAAAqB,CAAA;QAAArB,aAAA,GAAAoB,CAAA;QAAA,OAAA2F,KAAK,CAACV,QAAQ,KAAK,SAAS;MAAT,CAAS,CAAC,CAACW;KAChE;EACH;EAEA;;;EAGQ,OAAO0B,mBAAmBA,CAAChG,eAAiC;IAAA;IAAA1C,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAClE,IAAIsB,eAAe,EAAEqC,KAAK,KAAK,IAAI,EAAE;MAAA;MAAA/E,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA,OAAO,UAAU;IAAA,CAAC;IAAA;IAAA;MAAApB,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IACvD,OAAO,WAAW;EACpB;EAEA;;;EAGQ,OAAOuH,0BAA0BA,CAACF,UAAkB,EAAE/F,eAAiC;IAAA;IAAA1C,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAC7F,IAAIqH,UAAU,KAAK,UAAU,EAAE;MAAA;MAAAzI,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA,OAAO,IAAI,CAAC0H,kBAAkB,CAACC,QAAQ,CAACxD,SAAS;IAAA,CAAC;IAAA;IAAA;MAAAvF,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IACjF,OAAO,IAAI,CAAC0H,kBAAkB,CAACE,IAAI,CAACzD,SAAS;EAC/C;EAEA;;;EAGQ,OAAOsD,gBAAgBA,CAACxG,mBAAwC;IAAA;IAAArC,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACtE;IACA,OAAO,IAAI;EACb;EAEA;;;EAGQ,aAAaqC,4BAA4BA,CAC/CpB,mBAAwC,EACxCG,qBAAkD,EAClDE,eAAiC;IAAA;IAAA1C,aAAA,GAAAqB,CAAA;IAEjC,MAAM+D,MAAM;IAAA;IAAA,CAAApF,aAAA,GAAAoB,CAAA,SAAuB,EAAE;IAErC;IACA,MAAM6H,eAAe;IAAA;IAAA,CAAAjJ,aAAA,GAAAoB,CAAA,SAAGoB,qBAAqB,CAAC0G,eAAe,CAACC,cAAc,CAACC,KAAK,GAC3D/G,mBAAmB,CAACmF,gBAAgB,CAAC6B,aAAa;IACzE,MAAMC,WAAW;IAAA;IAAA,CAAAtJ,aAAA,GAAAoB,CAAA,SAAG,IAAI,EAAC,CAAC;IAAA;IAAApB,aAAA,GAAAoB,CAAA;IAE1BgE,MAAM,CAACT,IAAI,CAAC;MACViB,WAAW,EAAE,wBAAwB;MACrCC,QAAQ,EAAE,2BAA2B;MACrCC,KAAK,EAAE,GAAGwD,WAAW,mBAAmB;MACxCvD,MAAM,EAAE,GAAGkD,eAAe,CAACjD,OAAO,CAAC,CAAC,CAAC,mBAAmB;MACxDC,MAAM,EAAEgD,eAAe,IAAIK,WAAW;MAAA;MAAA,CAAAtJ,aAAA,GAAAsB,CAAA,WAAGW,qBAAA,CAAAiE,gBAAgB,CAACC,SAAS;MAAA;MAAA,CAAAnG,aAAA,GAAAsB,CAAA,WAAGW,qBAAA,CAAAiE,gBAAgB,CAACE,aAAa;MACpGC,QAAQ,EAAE4C,eAAe,IAAIK,WAAW;MAAA;MAAA,CAAAtJ,aAAA,GAAAsB,CAAA,WAAG,MAAM;MAAA;MAAA,CAAAtB,aAAA,GAAAsB,CAAA,WAAG,SAAS;MAC7DwD,WAAW,EAAE;KACd,CAAC;IAEF;IACA,MAAMyE,oBAAoB;IAAA;IAAA,CAAAvJ,aAAA,GAAAoB,CAAA,SAAG,IAAI,CAACoI,0BAA0B,CAACnH,mBAAmB,CAAC;IAAC;IAAArC,aAAA,GAAAoB,CAAA;IAClFgE,MAAM,CAACT,IAAI,CAAC;MACViB,WAAW,EAAE,uBAAuB;MACpCC,QAAQ,EAAE,kBAAkB;MAC5BC,KAAK,EAAE,+BAA+B;MACtCC,MAAM,EAAEwD,oBAAoB;MAAA;MAAA,CAAAvJ,aAAA,GAAAsB,CAAA,WAAG,WAAW;MAAA;MAAA,CAAAtB,aAAA,GAAAsB,CAAA,WAAG,eAAe;MAC5D2E,MAAM,EAAEsD,oBAAoB;MAAA;MAAA,CAAAvJ,aAAA,GAAAsB,CAAA,WAAGW,qBAAA,CAAAiE,gBAAgB,CAACC,SAAS;MAAA;MAAA,CAAAnG,aAAA,GAAAsB,CAAA,WAAGW,qBAAA,CAAAiE,gBAAgB,CAACE,aAAa;MAC1FC,QAAQ,EAAEkD,oBAAoB;MAAA;MAAA,CAAAvJ,aAAA,GAAAsB,CAAA,WAAG,MAAM;MAAA;MAAA,CAAAtB,aAAA,GAAAsB,CAAA,WAAG,SAAS;MACnDwD,WAAW,EAAE;KACd,CAAC;IAEF,MAAM+B,eAAe;IAAA;IAAA,CAAA7G,aAAA,GAAAoB,CAAA,SAAGgE,MAAM,CAAC0B,MAAM,CAACC,KAAK,IAAI;MAAA;MAAA/G,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MAAA,OAAA2F,KAAK,CAACd,MAAM,KAAKhE,qBAAA,CAAAiE,gBAAgB,CAACC,SAAS;IAAT,CAAS,CAAC,CAACa,MAAM;IAClG,MAAMC,aAAa;IAAA;IAAA,CAAAjH,aAAA,GAAAoB,CAAA,SAAGyF,eAAe,KAAKzB,MAAM,CAAC4B,MAAM;IAAA;IAAA,CAAAhH,aAAA,GAAAsB,CAAA,WACrDW,qBAAA,CAAAiE,gBAAgB,CAACC,SAAS;IAAA;IAAA,CAAAnG,aAAA,GAAAsB,CAAA,WAC1BW,qBAAA,CAAAiE,gBAAgB,CAACgB,mBAAmB;IAAC;IAAAlH,aAAA,GAAAoB,CAAA;IAEvC,OAAO;MACLyE,QAAQ,EAAE,2BAA2B;MACrCoB,aAAa;MACbE,oBAAoB,EAAGN,eAAe,GAAGzB,MAAM,CAAC4B,MAAM,GAAI,GAAG;MAC7D5B,MAAM;MACNgC,OAAO,EAAE,GAAGP,eAAe,OAAOzB,MAAM,CAAC4B,MAAM,iCAAiC;MAChFK,cAAc,EAAEjC,MAAM,CAAC0B,MAAM,CAACC,KAAK,IAAI;QAAA;QAAA/G,aAAA,GAAAqB,CAAA;QAAArB,aAAA,GAAAoB,CAAA;QAAA,OAAA2F,KAAK,CAACV,QAAQ,KAAK,OAAO;MAAP,CAAO,CAAC,CAACW,MAAM;MACzEM,QAAQ,EAAElC,MAAM,CAAC0B,MAAM,CAACC,KAAK,IAAI;QAAA;QAAA/G,aAAA,GAAAqB,CAAA;QAAArB,aAAA,GAAAoB,CAAA;QAAA,OAAA2F,KAAK,CAACV,QAAQ,KAAK,SAAS;MAAT,CAAS,CAAC,CAACW;KAChE;EACH;EAEA;;;EAGQ,OAAOwC,0BAA0BA,CAACnH,mBAAwC;IAAA;IAAArC,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAChF;IACA,OAAO,IAAI;EACb;EAEA;;;EAGQ,aAAauC,wBAAwBA,CAC3CtB,mBAAwC,EACxCC,kBAAsC,EACtCI,eAAiC;IAAA;IAAA1C,aAAA,GAAAqB,CAAA;IAEjC,MAAM+D,MAAM;IAAA;IAAA,CAAApF,aAAA,GAAAoB,CAAA,SAAuB,EAAE;IAErC;IACA,MAAMqI,cAAc;IAAA;IAAA,CAAAzJ,aAAA,GAAAoB,CAAA,SAAG,IAAI,CAACsI,mBAAmB,CAACrH,mBAAmB,CAAC;IACpE,MAAMsH,UAAU;IAAA;IAAA,CAAA3J,aAAA,GAAAoB,CAAA,SAAG,EAAE,EAAC,CAAC;IAAA;IAAApB,aAAA,GAAAoB,CAAA;IAEvBgE,MAAM,CAACT,IAAI,CAAC;MACViB,WAAW,EAAE,mBAAmB;MAChCC,QAAQ,EAAE,qBAAqB;MAC/BC,KAAK,EAAE,GAAG6D,UAAU,MAAM;MAC1B5D,MAAM,EAAE,GAAG0D,cAAc,gBAAgB;MACzCxD,MAAM,EAAEwD,cAAc,IAAIE,UAAU;MAAA;MAAA,CAAA3J,aAAA,GAAAsB,CAAA,WAAGW,qBAAA,CAAAiE,gBAAgB,CAACC,SAAS;MAAA;MAAA,CAAAnG,aAAA,GAAAsB,CAAA,WAAGW,qBAAA,CAAAiE,gBAAgB,CAACE,aAAa;MAClGC,QAAQ,EAAEoD,cAAc,IAAIE,UAAU;MAAA;MAAA,CAAA3J,aAAA,GAAAsB,CAAA,WAAG,MAAM;MAAA;MAAA,CAAAtB,aAAA,GAAAsB,CAAA,WAAG,SAAS;MAC3DwD,WAAW,EAAE;KACd,CAAC;IAEF;IACA,MAAM8E,cAAc;IAAA;IAAA,CAAA5J,aAAA,GAAAoB,CAAA,SAAG,IAAI,CAACyI,eAAe,CAACxH,mBAAmB,CAAC;IAAC;IAAArC,aAAA,GAAAoB,CAAA;IACjEgE,MAAM,CAACT,IAAI,CAAC;MACViB,WAAW,EAAE,qBAAqB;MAClCC,QAAQ,EAAE,iBAAiB;MAC3BC,KAAK,EAAE,gCAAgC;MACvCC,MAAM,EAAE6D,cAAc;MAAA;MAAA,CAAA5J,aAAA,GAAAsB,CAAA,WAAG,WAAW;MAAA;MAAA,CAAAtB,aAAA,GAAAsB,CAAA,WAAG,eAAe;MACtD2E,MAAM,EAAE2D,cAAc;MAAA;MAAA,CAAA5J,aAAA,GAAAsB,CAAA,WAAGW,qBAAA,CAAAiE,gBAAgB,CAACC,SAAS;MAAA;MAAA,CAAAnG,aAAA,GAAAsB,CAAA,WAAGW,qBAAA,CAAAiE,gBAAgB,CAACE,aAAa;MACpFC,QAAQ,EAAEuD,cAAc;MAAA;MAAA,CAAA5J,aAAA,GAAAsB,CAAA,WAAG,MAAM;MAAA;MAAA,CAAAtB,aAAA,GAAAsB,CAAA,WAAG,OAAO;MAC3CwD,WAAW,EAAE;KACd,CAAC;IAEF,MAAM+B,eAAe;IAAA;IAAA,CAAA7G,aAAA,GAAAoB,CAAA,SAAGgE,MAAM,CAAC0B,MAAM,CAACC,KAAK,IAAI;MAAA;MAAA/G,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MAAA,OAAA2F,KAAK,CAACd,MAAM,KAAKhE,qBAAA,CAAAiE,gBAAgB,CAACC,SAAS;IAAT,CAAS,CAAC,CAACa,MAAM;IAClG,MAAMC,aAAa;IAAA;IAAA,CAAAjH,aAAA,GAAAoB,CAAA,SAAGyF,eAAe,KAAKzB,MAAM,CAAC4B,MAAM;IAAA;IAAA,CAAAhH,aAAA,GAAAsB,CAAA,WACrDW,qBAAA,CAAAiE,gBAAgB,CAACC,SAAS;IAAA;IAAA,CAAAnG,aAAA,GAAAsB,CAAA,WAC1BW,qBAAA,CAAAiE,gBAAgB,CAACgB,mBAAmB;IAAC;IAAAlH,aAAA,GAAAoB,CAAA;IAEvC,OAAO;MACLyE,QAAQ,EAAE,qBAAqB;MAC/BoB,aAAa;MACbE,oBAAoB,EAAGN,eAAe,GAAGzB,MAAM,CAAC4B,MAAM,GAAI,GAAG;MAC7D5B,MAAM;MACNgC,OAAO,EAAE,GAAGP,eAAe,OAAOzB,MAAM,CAAC4B,MAAM,8BAA8B;MAC7EK,cAAc,EAAEjC,MAAM,CAAC0B,MAAM,CAACC,KAAK,IAAI;QAAA;QAAA/G,aAAA,GAAAqB,CAAA;QAAArB,aAAA,GAAAoB,CAAA;QAAA,OAAA2F,KAAK,CAACV,QAAQ,KAAK,OAAO;MAAP,CAAO,CAAC,CAACW,MAAM;MACzEM,QAAQ,EAAElC,MAAM,CAAC0B,MAAM,CAACC,KAAK,IAAI;QAAA;QAAA/G,aAAA,GAAAqB,CAAA;QAAArB,aAAA,GAAAoB,CAAA;QAAA,OAAA2F,KAAK,CAACV,QAAQ,KAAK,SAAS;MAAT,CAAS,CAAC,CAACW;KAChE;EACH;EAEA;;;EAGQ,OAAO0C,mBAAmBA,CAACrH,mBAAwC;IAAA;IAAArC,aAAA,GAAAqB,CAAA;IACzE,MAAMgI,aAAa;IAAA;IAAA,CAAArJ,aAAA,GAAAoB,CAAA,SAAGiB,mBAAmB,CAACmF,gBAAgB,CAAC6B,aAAa;IACxE,MAAM9B,cAAc;IAAA;IAAA,CAAAvH,aAAA,GAAAoB,CAAA,SAAGiB,mBAAmB,CAACmF,gBAAgB,CAACC,cAAc;IAE1E;IACA,MAAMqC,SAAS;IAAA;IAAA,CAAA9J,aAAA,GAAAoB,CAAA,SAAG,EAAE,EAAC,CAAC;IACtB,MAAM2I,SAAS;IAAA;IAAA,CAAA/J,aAAA,GAAAoB,CAAA,SAAG4I,IAAI,CAACC,KAAK,CAACZ,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,EAAC,CAAC;IACzD,MAAMa,aAAa;IAAA;IAAA,CAAAlK,aAAA,GAAAoB,CAAA,SAAGmG,cAAc,GAAG,CAAC,EAAC,CAAC;IAAA;IAAAvH,aAAA,GAAAoB,CAAA;IAE1C,OAAO4I,IAAI,CAACG,KAAK,CAACL,SAAS,GAAGC,SAAS,GAAGG,aAAa,CAAC;EAC1D;EAEA;;;EAGQ,OAAOL,eAAeA,CAACxH,mBAAwC;IAAA;IAAArC,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACrE;IACA,OAAO,IAAI;EACb;EAEA;;;EAGQ,OAAOyC,0BAA0BA,CAACuG,OAA2B;IAAA;IAAApK,aAAA,GAAAqB,CAAA;IACnE,MAAMgJ,SAAS;IAAA;IAAA,CAAArK,aAAA,GAAAoB,CAAA,SAAGgJ,OAAO,CAACE,OAAO,CAACC,MAAM,IAAI;MAAA;MAAAvK,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MAAA,OAAAmJ,MAAM,CAACnF,MAAM;IAAN,CAAM,CAAC;IAC1D,MAAMyB,eAAe;IAAA;IAAA,CAAA7G,aAAA,GAAAoB,CAAA,SAAGiJ,SAAS,CAACvD,MAAM,CAACC,KAAK,IAAI;MAAA;MAAA/G,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MAAA,OAAA2F,KAAK,CAACd,MAAM,KAAKhE,qBAAA,CAAAiE,gBAAgB,CAACC,SAAS;IAAT,CAAS,CAAC,CAACa,MAAM;IAErG,IAAIC,aAA+B;IACnC,MAAME,oBAAoB;IAAA;IAAA,CAAAnH,aAAA,GAAAoB,CAAA,SAAIyF,eAAe,GAAGwD,SAAS,CAACrD,MAAM,GAAI,GAAG;IAAC;IAAAhH,aAAA,GAAAoB,CAAA;IAExE,IAAI+F,oBAAoB,KAAK,GAAG,EAAE;MAAA;MAAAnH,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAChC6F,aAAa,GAAGhF,qBAAA,CAAAiE,gBAAgB,CAACC,SAAS;IAC5C,CAAC,MAAM;MAAA;MAAAnG,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA,IAAI+F,oBAAoB,IAAI,EAAE,EAAE;QAAA;QAAAnH,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACrC6F,aAAa,GAAGhF,qBAAA,CAAAiE,gBAAgB,CAACgB,mBAAmB;MACtD,CAAC,MAAM;QAAA;QAAAlH,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACL6F,aAAa,GAAGhF,qBAAA,CAAAiE,gBAAgB,CAACE,aAAa;MAChD;IAAA;IAAC;IAAApG,aAAA,GAAAoB,CAAA;IAED,OAAO;MACLyE,QAAQ,EAAE,oBAAoB;MAC9BoB,aAAa;MACbE,oBAAoB;MACpB/B,MAAM,EAAEiF,SAAS;MACjBjD,OAAO,EAAE,GAAGP,eAAe,OAAOwD,SAAS,CAACrD,MAAM,yBAAyB;MAC3EK,cAAc,EAAEgD,SAAS,CAACvD,MAAM,CAACC,KAAK,IAAI;QAAA;QAAA/G,aAAA,GAAAqB,CAAA;QAAArB,aAAA,GAAAoB,CAAA;QAAA,OAAA2F,KAAK,CAACV,QAAQ,KAAK,OAAO;MAAP,CAAO,CAAC,CAACW,MAAM;MAC5EM,QAAQ,EAAE+C,SAAS,CAACvD,MAAM,CAACC,KAAK,IAAI;QAAA;QAAA/G,aAAA,GAAAqB,CAAA;QAAArB,aAAA,GAAAoB,CAAA;QAAA,OAAA2F,KAAK,CAACV,QAAQ,KAAK,SAAS;MAAT,CAAS,CAAC,CAACW;KACnE;EACH;EAEA;;;EAGQ,aAAajD,iCAAiCA,CACpDqG,OAA2B,EAC3B/H,mBAAwC;IAAA;IAAArC,aAAA,GAAAqB,CAAA;IAExC,MAAMyC,eAAe;IAAA;IAAA,CAAA9D,aAAA,GAAAoB,CAAA,SAA+B,EAAE;IAEtD;IACA,MAAMoJ,kBAAkB;IAAA;IAAA,CAAAxK,aAAA,GAAAoB,CAAA,SAAGgJ,OAAO,CAACE,OAAO,CAACC,MAAM,IAC/C;MAAA;MAAAvK,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MAAA,OAAAmJ,MAAM,CAACnF,MAAM,CAAC0B,MAAM,CAACC,KAAK,IAAI;QAAA;QAAA/G,aAAA,GAAAqB,CAAA;QAAArB,aAAA,GAAAoB,CAAA;QAAA,OAAA2F,KAAK,CAACd,MAAM,KAAKhE,qBAAA,CAAAiE,gBAAgB,CAACC,SAAS;MAAT,CAAS,CAAC;IAAD,CAAC,CAC3E;IAAC;IAAAnG,aAAA,GAAAoB,CAAA;IAEFoJ,kBAAkB,CAACvF,OAAO,CAAC8B,KAAK,IAAG;MAAA;MAAA/G,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MACjC,IAAI2F,KAAK,CAACnB,WAAW,CAAC6E,QAAQ,CAAC,WAAW,CAAC,EAAE;QAAA;QAAAzK,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QAC3C0C,eAAe,CAACa,IAAI,CAAC;UACnB9B,EAAE,EAAE,wBAAwB;UAC5B6H,QAAQ,EAAE,MAAM;UAChBC,QAAQ,EAAE,mBAAmB;UAC7BC,KAAK,EAAE,8BAA8B;UACrC9F,WAAW,EAAE,2HAA2H;UACxI+F,iBAAiB,EAAE,CAAC9D,KAAK,CAAClB,QAAQ,CAAC;UACnCiF,aAAa,EAAE,KAAK;UACpBC,gBAAgB,EAAE,IAAI;UACtBC,kBAAkB,EAAE,WAAW;UAC/BC,OAAO,EAAE,CACP,gDAAgD,EAChD,2CAA2C,EAC3C,iCAAiC,EACjC,qCAAqC;SAExC,CAAC;MACJ,CAAC;MAAA;MAAA;QAAAjL,aAAA,GAAAsB,CAAA;MAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAED,IAAI2F,KAAK,CAACnB,WAAW,CAAC6E,QAAQ,CAAC,SAAS,CAAC,EAAE;QAAA;QAAAzK,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACzC0C,eAAe,CAACa,IAAI,CAAC;UACnB9B,EAAE,EAAE,0BAA0B;UAC9B6H,QAAQ,EAAE,QAAQ;UAClBC,QAAQ,EAAE,mBAAmB;UAC7BC,KAAK,EAAE,sBAAsB;UAC7B9F,WAAW,EAAE,+EAA+E;UAC5F+F,iBAAiB,EAAE,CAAC9D,KAAK,CAAClB,QAAQ,CAAC;UACnCiF,aAAa,EAAE,IAAI;UACnBC,gBAAgB,EAAE,IAAI;UACtBC,kBAAkB,EAAE,WAAW;UAC/BC,OAAO,EAAE,CACP,8CAA8C,EAC9C,kCAAkC,EAClC,8BAA8B,EAC9B,8CAA8C;SAEjD,CAAC;MACJ,CAAC;MAAA;MAAA;QAAAjL,aAAA,GAAAsB,CAAA;MAAA;IACH,CAAC,CAAC;IAAC;IAAAtB,aAAA,GAAAoB,CAAA;IAEH,OAAO0C,eAAe;EACxB;EAEA;;;EAGQ,aAAaG,+BAA+BA,CAClDL,iBAAmC,EACnCpB,qBAAkD,EAClDE,eAAiC;IAAA;IAAA1C,aAAA,GAAAqB,CAAA;IAEjC,MAAM6J,YAAY;IAAA;IAAA,CAAAlL,aAAA,GAAAoB,CAAA,SAA+B,EAAE;IAEnD;IAAA;IAAApB,aAAA,GAAAoB,CAAA;IACA,IAAIwC,iBAAiB,CAACuD,oBAAoB,IAAI,EAAE,EAAE;MAAA;MAAAnH,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAChD8J,YAAY,CAACvG,IAAI,CAAC;QAChBwG,aAAa,EAAE,MAAM;QACrBC,KAAK,EAAE,QAAQ;QACfnF,MAAM,EAAE,UAAU;QAClBoF,eAAe,EAAE,CACf,0BAA0B,EAC1B,gCAAgC,EAChC,4CAA4C,CAC7C;QACDC,eAAe,EAAE,EAAE;QACnBC,oBAAoB,EAAE;OACvB,CAAC;IACJ,CAAC;IAAA;IAAA;MAAAvL,aAAA,GAAAsB,CAAA;IAAA;IAED;IAAAtB,aAAA,GAAAoB,CAAA;IACA,IAAIoB,qBAAqB,CAACgJ,qBAAqB,CAACC,gBAAgB,CAACC,gBAAgB,IAAI,EAAE,EAAE;MAAA;MAAA1L,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACvF8J,YAAY,CAACvG,IAAI,CAAC;QAChBwG,aAAa,EAAE,aAAa;QAC5BC,KAAK,EAAE,WAAW;QAClBnF,MAAM,EAAE,UAAU;QAClBoF,eAAe,EAAE,CACf,gCAAgC,EAChC,mCAAmC,EACnC,oCAAoC,CACrC;QACDC,eAAe,EAAE,CAAC;QAAE;QACpBC,oBAAoB,EAAE;OACvB,CAAC;IACJ,CAAC;IAAA;IAAA;MAAAvL,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAED,OAAO8J,YAAY;EACrB;EAEA;;;EAGQ,OAAOtI,kBAAkBA,CAACuB,QAAgB;IAAA;IAAAnE,aAAA,GAAAqB,CAAA;IAChD,MAAMyB,SAAS;IAAA;IAAA,CAAA9C,aAAA,GAAAoB,CAAA,SAAG2B,IAAI,CAAC4I,GAAG,EAAE;IAC5B,MAAMC,MAAM;IAAA;IAAA,CAAA5L,aAAA,GAAAoB,CAAA,SAAG4I,IAAI,CAAC4B,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;IAAC;IAAA9L,aAAA,GAAAoB,CAAA;IAC1D,OAAO,uBAAuB+C,QAAQ,IAAIrB,SAAS,IAAI8I,MAAM,EAAE;EACjE;;;;AAhwBFG,OAAA,CAAA5J,wBAAA,GAAAA,wBAAA;AAiwBC;AAAAnC,aAAA,GAAAoB,CAAA;AAhwByBe,wBAAA,CAAA6J,OAAO,GAAG,OAAO;AAAC;AAAAhM,aAAA,GAAAoB,CAAA;AAClBe,wBAAA,CAAAkC,gBAAgB,GAAG,IAAI4H,GAAG,EAA8B;AAEhF;AAAA;AAAAjM,aAAA,GAAAoB,CAAA;AACwBe,wBAAA,CAAAmD,iBAAiB,GAAG;EAC1CC,SAAS,EAAE;IACTC,WAAW,EAAE,IAAI;IAAE;IACnB0G,aAAa,EAAE,IAAI;IAAE;IACrBC,eAAe,EAAE,IAAI,CAAC;GACvB;EACDxF,YAAY,EAAE;IACZC,MAAM,EAAE,CAAC;IAAE;IACXwF,MAAM,EAAE,CAAC;IAAE;IACXC,OAAO,EAAE,CAAC,CAAC;GACZ;EACDC,UAAU,EAAE;IACVC,cAAc,EAAE,CAAC;IAAE;IACnBC,oBAAoB,EAAE,CAAC;IAAE;IACzBC,cAAc,EAAE,CAAC;IAAE;IACnBC,oBAAoB,EAAE,CAAC,CAAC;;CAE3B;AAED;AAAA;AAAA1M,aAAA,GAAAoB,CAAA;AACwBe,wBAAA,CAAA2F,aAAa,GAAG;EACtCC,iBAAiB,EAAE;IACjBC,YAAY,EAAE;MACZE,WAAW,EAAE,EAAE;MAAE;MACjBD,KAAK,EAAE,EAAE,CAAC;KACX;IACD0E,aAAa,EAAE;MACbC,mBAAmB,EAAE,EAAE;MAAE;MACzBC,aAAa,EAAE,EAAE,CAAC;;GAErB;EACDxE,aAAa,EAAE;IACbyE,OAAO,EAAE,CAAC;IAAE;IACZC,OAAO,EAAE,CAAC;IACVC,OAAO,EAAE,EAAE;IACXC,OAAO,EAAE;;CAEZ;AAED;AAAA;AAAAjN,aAAA,GAAAoB,CAAA;AACwBe,wBAAA,CAAA2G,kBAAkB,GAAG;EAC3CE,IAAI,EAAE;IACJzD,SAAS,EAAE,IAAI;IAAE;IACjB2H,YAAY,EAAE,UAAU;IACxBC,gBAAgB,EAAE;MAChBC,QAAQ,EAAE,CAAC;MAAE;MACbC,QAAQ,EAAE,CAAC;MACXC,QAAQ,EAAE,CAAC;MACXC,QAAQ,EAAE;;GAEb;EACDxE,QAAQ,EAAE;IACRxD,SAAS,EAAE,GAAG;IAAE;IAChBoB,YAAY,EAAE,CAAC;IAAE;IACjB2F,UAAU,EAAE,CAAC,CAAC;;CAEjB", "ignoreList": []}