656167cdd81ddc483296b03233c3ed57
"use strict";

/**
 * AirPropertiesCalculator - Advanced air property calculations with interpolation
 *
 * Provides utility functions for:
 * - Air property interpolation from enhanced data files
 * - Temperature, pressure, and humidity corrections
 * - Elevation effects calculation
 * - Non-standard condition adjustments
 */
/* istanbul ignore next */
function cov_37yapc4cs() {
  var path = "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\AirPropertiesCalculator.ts";
  var hash = "d5e1904a5d2fa8c86c7afd4a596170a6ccac18d1";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\AirPropertiesCalculator.ts",
    statementMap: {
      "0": {
        start: {
          line: 11,
          column: 0
        },
        end: {
          line: 11,
          column: 62
        }
      },
      "1": {
        start: {
          line: 12,
          column: 0
        },
        end: {
          line: 12,
          column: 41
        }
      },
      "2": {
        start: {
          line: 13,
          column: 13
        },
        end: {
          line: 13,
          column: 26
        }
      },
      "3": {
        start: {
          line: 14,
          column: 15
        },
        end: {
          line: 14,
          column: 30
        }
      },
      "4": {
        start: {
          line: 23,
          column: 8
        },
        end: {
          line: 33,
          column: 9
        }
      },
      "5": {
        start: {
          line: 24,
          column: 12
        },
        end: {
          line: 32,
          column: 13
        }
      },
      "6": {
        start: {
          line: 25,
          column: 33
        },
        end: {
          line: 25,
          column: 94
        }
      },
      "7": {
        start: {
          line: 26,
          column: 32
        },
        end: {
          line: 26,
          column: 72
        }
      },
      "8": {
        start: {
          line: 27,
          column: 16
        },
        end: {
          line: 27,
          column: 61
        }
      },
      "9": {
        start: {
          line: 30,
          column: 16
        },
        end: {
          line: 30,
          column: 83
        }
      },
      "10": {
        start: {
          line: 31,
          column: 16
        },
        end: {
          line: 31,
          column: 73
        }
      },
      "11": {
        start: {
          line: 34,
          column: 8
        },
        end: {
          line: 34,
          column: 38
        }
      },
      "12": {
        start: {
          line: 40,
          column: 8
        },
        end: {
          line: 50,
          column: 9
        }
      },
      "13": {
        start: {
          line: 41,
          column: 12
        },
        end: {
          line: 49,
          column: 13
        }
      },
      "14": {
        start: {
          line: 42,
          column: 33
        },
        end: {
          line: 42,
          column: 97
        }
      },
      "15": {
        start: {
          line: 43,
          column: 32
        },
        end: {
          line: 43,
          column: 72
        }
      },
      "16": {
        start: {
          line: 44,
          column: 16
        },
        end: {
          line: 44,
          column: 64
        }
      },
      "17": {
        start: {
          line: 47,
          column: 16
        },
        end: {
          line: 47,
          column: 86
        }
      },
      "18": {
        start: {
          line: 48,
          column: 16
        },
        end: {
          line: 48,
          column: 79
        }
      },
      "19": {
        start: {
          line: 51,
          column: 8
        },
        end: {
          line: 51,
          column: 41
        }
      },
      "20": {
        start: {
          line: 57,
          column: 8
        },
        end: {
          line: 67,
          column: 9
        }
      },
      "21": {
        start: {
          line: 58,
          column: 12
        },
        end: {
          line: 66,
          column: 13
        }
      },
      "22": {
        start: {
          line: 59,
          column: 33
        },
        end: {
          line: 59,
          column: 94
        }
      },
      "23": {
        start: {
          line: 60,
          column: 32
        },
        end: {
          line: 60,
          column: 72
        }
      },
      "24": {
        start: {
          line: 61,
          column: 16
        },
        end: {
          line: 61,
          column: 61
        }
      },
      "25": {
        start: {
          line: 64,
          column: 16
        },
        end: {
          line: 64,
          column: 83
        }
      },
      "26": {
        start: {
          line: 65,
          column: 16
        },
        end: {
          line: 65,
          column: 73
        }
      },
      "27": {
        start: {
          line: 68,
          column: 8
        },
        end: {
          line: 68,
          column: 38
        }
      },
      "28": {
        start: {
          line: 74,
          column: 21
        },
        end: {
          line: 74,
          column: 49
        }
      },
      "29": {
        start: {
          line: 75,
          column: 25
        },
        end: {
          line: 75,
          column: 27
        }
      },
      "30": {
        start: {
          line: 76,
          column: 22
        },
        end: {
          line: 76,
          column: 24
        }
      },
      "31": {
        start: {
          line: 78,
          column: 8
        },
        end: {
          line: 78,
          column: 57
        }
      },
      "32": {
        start: {
          line: 80,
          column: 26
        },
        end: {
          line: 80,
          column: 93
        }
      },
      "33": {
        start: {
          line: 82,
          column: 27
        },
        end: {
          line: 82,
          column: 82
        }
      },
      "34": {
        start: {
          line: 83,
          column: 31
        },
        end: {
          line: 83,
          column: 101
        }
      },
      "35": {
        start: {
          line: 84,
          column: 31
        },
        end: {
          line: 84,
          column: 86
        }
      },
      "36": {
        start: {
          line: 86,
          column: 33
        },
        end: {
          line: 86,
          column: 84
        }
      },
      "37": {
        start: {
          line: 87,
          column: 35
        },
        end: {
          line: 87,
          column: 67
        }
      },
      "38": {
        start: {
          line: 89,
          column: 8
        },
        end: {
          line: 89,
          column: 63
        }
      },
      "39": {
        start: {
          line: 90,
          column: 8
        },
        end: {
          line: 103,
          column: 10
        }
      },
      "40": {
        start: {
          line: 109,
          column: 61
        },
        end: {
          line: 109,
          column: 67
        }
      },
      "41": {
        start: {
          line: 110,
          column: 25
        },
        end: {
          line: 110,
          column: 27
        }
      },
      "42": {
        start: {
          line: 112,
          column: 27
        },
        end: {
          line: 117,
          column: 9
        }
      },
      "43": {
        start: {
          line: 120,
          column: 8
        },
        end: {
          line: 133,
          column: 9
        }
      },
      "44": {
        start: {
          line: 122,
          column: 27
        },
        end: {
          line: 122,
          column: 58
        }
      },
      "45": {
        start: {
          line: 123,
          column: 12
        },
        end: {
          line: 123,
          column: 82
        }
      },
      "46": {
        start: {
          line: 124,
          column: 12
        },
        end: {
          line: 124,
          column: 66
        }
      },
      "47": {
        start: {
          line: 128,
          column: 12
        },
        end: {
          line: 128,
          column: 60
        }
      },
      "48": {
        start: {
          line: 129,
          column: 12
        },
        end: {
          line: 129,
          column: 58
        }
      },
      "49": {
        start: {
          line: 130,
          column: 12
        },
        end: {
          line: 132,
          column: 13
        }
      },
      "50": {
        start: {
          line: 131,
          column: 16
        },
        end: {
          line: 131,
          column: 102
        }
      },
      "51": {
        start: {
          line: 135,
          column: 31
        },
        end: {
          line: 135,
          column: 84
        }
      },
      "52": {
        start: {
          line: 136,
          column: 35
        },
        end: {
          line: 136,
          column: 87
        }
      },
      "53": {
        start: {
          line: 137,
          column: 35
        },
        end: {
          line: 137,
          column: 88
        }
      },
      "54": {
        start: {
          line: 139,
          column: 35
        },
        end: {
          line: 139,
          column: 91
        }
      },
      "55": {
        start: {
          line: 140,
          column: 28
        },
        end: {
          line: 140,
          column: 65
        }
      },
      "56": {
        start: {
          line: 142,
          column: 25
        },
        end: {
          line: 142,
          column: 64
        }
      },
      "57": {
        start: {
          line: 144,
          column: 8
        },
        end: {
          line: 146,
          column: 9
        }
      },
      "58": {
        start: {
          line: 145,
          column: 12
        },
        end: {
          line: 145,
          column: 115
        }
      },
      "59": {
        start: {
          line: 147,
          column: 8
        },
        end: {
          line: 158,
          column: 10
        }
      },
      "60": {
        start: {
          line: 164,
          column: 21
        },
        end: {
          line: 164,
          column: 49
        }
      },
      "61": {
        start: {
          line: 165,
          column: 25
        },
        end: {
          line: 165,
          column: 27
        }
      },
      "62": {
        start: {
          line: 166,
          column: 22
        },
        end: {
          line: 166,
          column: 24
        }
      },
      "63": {
        start: {
          line: 167,
          column: 29
        },
        end: {
          line: 167,
          column: 53
        }
      },
      "64": {
        start: {
          line: 168,
          column: 8
        },
        end: {
          line: 171,
          column: 9
        }
      },
      "65": {
        start: {
          line: 169,
          column: 12
        },
        end: {
          line: 169,
          column: 94
        }
      },
      "66": {
        start: {
          line: 170,
          column: 12
        },
        end: {
          line: 170,
          column: 96
        }
      },
      "67": {
        start: {
          line: 172,
          column: 24
        },
        end: {
          line: 172,
          column: 51
        }
      },
      "68": {
        start: {
          line: 174,
          column: 8
        },
        end: {
          line: 181,
          column: 9
        }
      },
      "69": {
        start: {
          line: 175,
          column: 32
        },
        end: {
          line: 175,
          column: 84
        }
      },
      "70": {
        start: {
          line: 176,
          column: 12
        },
        end: {
          line: 176,
          column: 37
        }
      },
      "71": {
        start: {
          line: 177,
          column: 12
        },
        end: {
          line: 179,
          column: 13
        }
      },
      "72": {
        start: {
          line: 178,
          column: 16
        },
        end: {
          line: 178,
          column: 126
        }
      },
      "73": {
        start: {
          line: 180,
          column: 12
        },
        end: {
          line: 180,
          column: 74
        }
      },
      "74": {
        start: {
          line: 183,
          column: 8
        },
        end: {
          line: 189,
          column: 9
        }
      },
      "75": {
        start: {
          line: 184,
          column: 36
        },
        end: {
          line: 184,
          column: 85
        }
      },
      "76": {
        start: {
          line: 185,
          column: 12
        },
        end: {
          line: 185,
          column: 41
        }
      },
      "77": {
        start: {
          line: 186,
          column: 12
        },
        end: {
          line: 188,
          column: 13
        }
      },
      "78": {
        start: {
          line: 187,
          column: 16
        },
        end: {
          line: 187,
          column: 108
        }
      },
      "79": {
        start: {
          line: 191,
          column: 8
        },
        end: {
          line: 193,
          column: 9
        }
      },
      "80": {
        start: {
          line: 192,
          column: 12
        },
        end: {
          line: 192,
          column: 43
        }
      },
      "81": {
        start: {
          line: 194,
          column: 8
        },
        end: {
          line: 194,
          column: 46
        }
      },
      "82": {
        start: {
          line: 200,
          column: 25
        },
        end: {
          line: 200,
          column: 27
        }
      },
      "83": {
        start: {
          line: 202,
          column: 33
        },
        end: {
          line: 202,
          column: 38
        }
      },
      "84": {
        start: {
          line: 203,
          column: 37
        },
        end: {
          line: 203,
          column: 44
        }
      },
      "85": {
        start: {
          line: 204,
          column: 29
        },
        end: {
          line: 204,
          column: 31
        }
      },
      "86": {
        start: {
          line: 206,
          column: 30
        },
        end: {
          line: 206,
          column: 75
        }
      },
      "87": {
        start: {
          line: 208,
          column: 29
        },
        end: {
          line: 208,
          column: 42
        }
      },
      "88": {
        start: {
          line: 210,
          column: 38
        },
        end: {
          line: 210,
          column: 86
        }
      },
      "89": {
        start: {
          line: 211,
          column: 34
        },
        end: {
          line: 211,
          column: 92
        }
      },
      "90": {
        start: {
          line: 213,
          column: 8
        },
        end: {
          line: 215,
          column: 9
        }
      },
      "91": {
        start: {
          line: 214,
          column: 12
        },
        end: {
          line: 214,
          column: 101
        }
      },
      "92": {
        start: {
          line: 216,
          column: 8
        },
        end: {
          line: 218,
          column: 9
        }
      },
      "93": {
        start: {
          line: 217,
          column: 12
        },
        end: {
          line: 217,
          column: 91
        }
      },
      "94": {
        start: {
          line: 219,
          column: 8
        },
        end: {
          line: 221,
          column: 9
        }
      },
      "95": {
        start: {
          line: 220,
          column: 12
        },
        end: {
          line: 220,
          column: 73
        }
      },
      "96": {
        start: {
          line: 222,
          column: 8
        },
        end: {
          line: 227,
          column: 10
        }
      },
      "97": {
        start: {
          line: 233,
          column: 8
        },
        end: {
          line: 234,
          column: 22
        }
      },
      "98": {
        start: {
          line: 234,
          column: 12
        },
        end: {
          line: 234,
          column: 22
        }
      },
      "99": {
        start: {
          line: 235,
          column: 8
        },
        end: {
          line: 235,
          column: 53
        }
      },
      "100": {
        start: {
          line: 241,
          column: 26
        },
        end: {
          line: 241,
          column: 53
        }
      },
      "101": {
        start: {
          line: 242,
          column: 22
        },
        end: {
          line: 242,
          column: 78
        }
      },
      "102": {
        start: {
          line: 242,
          column: 72
        },
        end: {
          line: 242,
          column: 77
        }
      },
      "103": {
        start: {
          line: 244,
          column: 24
        },
        end: {
          line: 244,
          column: 32
        }
      },
      "104": {
        start: {
          line: 245,
          column: 24
        },
        end: {
          line: 245,
          column: 47
        }
      },
      "105": {
        start: {
          line: 246,
          column: 8
        },
        end: {
          line: 252,
          column: 9
        }
      },
      "106": {
        start: {
          line: 246,
          column: 21
        },
        end: {
          line: 246,
          column: 22
        }
      },
      "107": {
        start: {
          line: 247,
          column: 12
        },
        end: {
          line: 251,
          column: 13
        }
      },
      "108": {
        start: {
          line: 248,
          column: 16
        },
        end: {
          line: 248,
          column: 37
        }
      },
      "109": {
        start: {
          line: 249,
          column: 16
        },
        end: {
          line: 249,
          column: 41
        }
      },
      "110": {
        start: {
          line: 250,
          column: 16
        },
        end: {
          line: 250,
          column: 22
        }
      },
      "111": {
        start: {
          line: 254,
          column: 8
        },
        end: {
          line: 256,
          column: 9
        }
      },
      "112": {
        start: {
          line: 255,
          column: 12
        },
        end: {
          line: 255,
          column: 53
        }
      },
      "113": {
        start: {
          line: 258,
          column: 27
        },
        end: {
          line: 258,
          column: 58
        }
      },
      "114": {
        start: {
          line: 259,
          column: 27
        },
        end: {
          line: 259,
          column: 58
        }
      },
      "115": {
        start: {
          line: 260,
          column: 8
        },
        end: {
          line: 265,
          column: 10
        }
      },
      "116": {
        start: {
          line: 271,
          column: 24
        },
        end: {
          line: 271,
          column: 52
        }
      },
      "117": {
        start: {
          line: 272,
          column: 27
        },
        end: {
          line: 272,
          column: 81
        }
      },
      "118": {
        start: {
          line: 272,
          column: 75
        },
        end: {
          line: 272,
          column: 80
        }
      },
      "119": {
        start: {
          line: 274,
          column: 23
        },
        end: {
          line: 274,
          column: 36
        }
      },
      "120": {
        start: {
          line: 275,
          column: 23
        },
        end: {
          line: 275,
          column: 56
        }
      },
      "121": {
        start: {
          line: 276,
          column: 8
        },
        end: {
          line: 282,
          column: 9
        }
      },
      "122": {
        start: {
          line: 276,
          column: 21
        },
        end: {
          line: 276,
          column: 22
        }
      },
      "123": {
        start: {
          line: 277,
          column: 12
        },
        end: {
          line: 281,
          column: 13
        }
      },
      "124": {
        start: {
          line: 278,
          column: 16
        },
        end: {
          line: 278,
          column: 41
        }
      },
      "125": {
        start: {
          line: 279,
          column: 16
        },
        end: {
          line: 279,
          column: 45
        }
      },
      "126": {
        start: {
          line: 280,
          column: 16
        },
        end: {
          line: 280,
          column: 22
        }
      },
      "127": {
        start: {
          line: 284,
          column: 8
        },
        end: {
          line: 286,
          column: 9
        }
      },
      "128": {
        start: {
          line: 285,
          column: 12
        },
        end: {
          line: 285,
          column: 48
        }
      },
      "129": {
        start: {
          line: 288,
          column: 24
        },
        end: {
          line: 288,
          column: 52
        }
      },
      "130": {
        start: {
          line: 289,
          column: 24
        },
        end: {
          line: 289,
          column: 52
        }
      },
      "131": {
        start: {
          line: 290,
          column: 8
        },
        end: {
          line: 290,
          column: 86
        }
      },
      "132": {
        start: {
          line: 298,
          column: 8
        },
        end: {
          line: 303,
          column: 10
        }
      },
      "133": {
        start: {
          line: 306,
          column: 8
        },
        end: {
          line: 308,
          column: 10
        }
      },
      "134": {
        start: {
          line: 311,
          column: 8
        },
        end: {
          line: 315,
          column: 10
        }
      },
      "135": {
        start: {
          line: 319,
          column: 8
        },
        end: {
          line: 321,
          column: 9
        }
      },
      "136": {
        start: {
          line: 320,
          column: 12
        },
        end: {
          line: 320,
          column: 109
        }
      },
      "137": {
        start: {
          line: 324,
          column: 8
        },
        end: {
          line: 324,
          column: 69
        }
      },
      "138": {
        start: {
          line: 327,
          column: 8
        },
        end: {
          line: 329,
          column: 9
        }
      },
      "139": {
        start: {
          line: 328,
          column: 12
        },
        end: {
          line: 328,
          column: 65
        }
      },
      "140": {
        start: {
          line: 330,
          column: 8
        },
        end: {
          line: 332,
          column: 9
        }
      },
      "141": {
        start: {
          line: 331,
          column: 12
        },
        end: {
          line: 331,
          column: 36
        }
      },
      "142": {
        start: {
          line: 333,
          column: 8
        },
        end: {
          line: 333,
          column: 19
        }
      },
      "143": {
        start: {
          line: 336,
          column: 8
        },
        end: {
          line: 336,
          column: 46
        }
      },
      "144": {
        start: {
          line: 339,
          column: 21
        },
        end: {
          line: 339,
          column: 52
        }
      },
      "145": {
        start: {
          line: 340,
          column: 32
        },
        end: {
          line: 340,
          column: 60
        }
      },
      "146": {
        start: {
          line: 342,
          column: 22
        },
        end: {
          line: 342,
          column: 84
        }
      },
      "147": {
        start: {
          line: 342,
          column: 78
        },
        end: {
          line: 342,
          column: 83
        }
      },
      "148": {
        start: {
          line: 343,
          column: 8
        },
        end: {
          line: 349,
          column: 9
        }
      },
      "149": {
        start: {
          line: 344,
          column: 12
        },
        end: {
          line: 348,
          column: 13
        }
      },
      "150": {
        start: {
          line: 345,
          column: 16
        },
        end: {
          line: 347,
          column: 17
        }
      },
      "151": {
        start: {
          line: 346,
          column: 20
        },
        end: {
          line: 346,
          column: 78
        }
      },
      "152": {
        start: {
          line: 350,
          column: 8
        },
        end: {
          line: 350,
          column: 19
        }
      },
      "153": {
        start: {
          line: 353,
          column: 21
        },
        end: {
          line: 353,
          column: 52
        }
      },
      "154": {
        start: {
          line: 354,
          column: 31
        },
        end: {
          line: 354,
          column: 56
        }
      },
      "155": {
        start: {
          line: 356,
          column: 26
        },
        end: {
          line: 356,
          column: 87
        }
      },
      "156": {
        start: {
          line: 356,
          column: 81
        },
        end: {
          line: 356,
          column: 86
        }
      },
      "157": {
        start: {
          line: 357,
          column: 8
        },
        end: {
          line: 363,
          column: 9
        }
      },
      "158": {
        start: {
          line: 358,
          column: 12
        },
        end: {
          line: 362,
          column: 13
        }
      },
      "159": {
        start: {
          line: 359,
          column: 16
        },
        end: {
          line: 361,
          column: 17
        }
      },
      "160": {
        start: {
          line: 360,
          column: 20
        },
        end: {
          line: 360,
          column: 76
        }
      },
      "161": {
        start: {
          line: 364,
          column: 8
        },
        end: {
          line: 364,
          column: 19
        }
      },
      "162": {
        start: {
          line: 367,
          column: 21
        },
        end: {
          line: 367,
          column: 52
        }
      },
      "163": {
        start: {
          line: 368,
          column: 36
        },
        end: {
          line: 368,
          column: 61
        }
      },
      "164": {
        start: {
          line: 370,
          column: 27
        },
        end: {
          line: 370,
          column: 93
        }
      },
      "165": {
        start: {
          line: 370,
          column: 87
        },
        end: {
          line: 370,
          column: 92
        }
      },
      "166": {
        start: {
          line: 371,
          column: 8
        },
        end: {
          line: 377,
          column: 9
        }
      },
      "167": {
        start: {
          line: 372,
          column: 12
        },
        end: {
          line: 376,
          column: 13
        }
      },
      "168": {
        start: {
          line: 373,
          column: 16
        },
        end: {
          line: 375,
          column: 17
        }
      },
      "169": {
        start: {
          line: 374,
          column: 20
        },
        end: {
          line: 374,
          column: 81
        }
      },
      "170": {
        start: {
          line: 378,
          column: 8
        },
        end: {
          line: 378,
          column: 19
        }
      },
      "171": {
        start: {
          line: 381,
          column: 21
        },
        end: {
          line: 381,
          column: 46
        }
      },
      "172": {
        start: {
          line: 383,
          column: 8
        },
        end: {
          line: 384,
          column: 43
        }
      },
      "173": {
        start: {
          line: 384,
          column: 12
        },
        end: {
          line: 384,
          column: 43
        }
      },
      "174": {
        start: {
          line: 385,
          column: 8
        },
        end: {
          line: 386,
          column: 50
        }
      },
      "175": {
        start: {
          line: 386,
          column: 12
        },
        end: {
          line: 386,
          column: 50
        }
      },
      "176": {
        start: {
          line: 387,
          column: 8
        },
        end: {
          line: 388,
          column: 51
        }
      },
      "177": {
        start: {
          line: 388,
          column: 12
        },
        end: {
          line: 388,
          column: 51
        }
      },
      "178": {
        start: {
          line: 389,
          column: 8
        },
        end: {
          line: 390,
          column: 51
        }
      },
      "179": {
        start: {
          line: 390,
          column: 12
        },
        end: {
          line: 390,
          column: 51
        }
      },
      "180": {
        start: {
          line: 391,
          column: 8
        },
        end: {
          line: 391,
          column: 47
        }
      },
      "181": {
        start: {
          line: 394,
          column: 8
        },
        end: {
          line: 396,
          column: 9
        }
      },
      "182": {
        start: {
          line: 395,
          column: 12
        },
        end: {
          line: 395,
          column: 105
        }
      },
      "183": {
        start: {
          line: 397,
          column: 8
        },
        end: {
          line: 399,
          column: 9
        }
      },
      "184": {
        start: {
          line: 398,
          column: 12
        },
        end: {
          line: 398,
          column: 104
        }
      },
      "185": {
        start: {
          line: 400,
          column: 8
        },
        end: {
          line: 402,
          column: 9
        }
      },
      "186": {
        start: {
          line: 401,
          column: 12
        },
        end: {
          line: 401,
          column: 117
        }
      },
      "187": {
        start: {
          line: 405,
          column: 0
        },
        end: {
          line: 405,
          column: 58
        }
      },
      "188": {
        start: {
          line: 406,
          column: 0
        },
        end: {
          line: 406,
          column: 49
        }
      },
      "189": {
        start: {
          line: 407,
          column: 0
        },
        end: {
          line: 407,
          column: 52
        }
      },
      "190": {
        start: {
          line: 408,
          column: 0
        },
        end: {
          line: 408,
          column: 49
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 22,
            column: 4
          },
          end: {
            line: 22,
            column: 5
          }
        },
        loc: {
          start: {
            line: 22,
            column: 35
          },
          end: {
            line: 35,
            column: 5
          }
        },
        line: 22
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 39,
            column: 4
          },
          end: {
            line: 39,
            column: 5
          }
        },
        loc: {
          start: {
            line: 39,
            column: 38
          },
          end: {
            line: 52,
            column: 5
          }
        },
        line: 39
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 56,
            column: 4
          },
          end: {
            line: 56,
            column: 5
          }
        },
        loc: {
          start: {
            line: 56,
            column: 35
          },
          end: {
            line: 69,
            column: 5
          }
        },
        line: 56
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 73,
            column: 4
          },
          end: {
            line: 73,
            column: 5
          }
        },
        loc: {
          start: {
            line: 73,
            column: 46
          },
          end: {
            line: 104,
            column: 5
          }
        },
        line: 73
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 108,
            column: 4
          },
          end: {
            line: 108,
            column: 5
          }
        },
        loc: {
          start: {
            line: 108,
            column: 45
          },
          end: {
            line: 159,
            column: 5
          }
        },
        line: 108
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 163,
            column: 4
          },
          end: {
            line: 163,
            column: 5
          }
        },
        loc: {
          start: {
            line: 163,
            column: 73
          },
          end: {
            line: 195,
            column: 5
          }
        },
        line: 163
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 199,
            column: 4
          },
          end: {
            line: 199,
            column: 5
          }
        },
        loc: {
          start: {
            line: 199,
            column: 47
          },
          end: {
            line: 228,
            column: 5
          }
        },
        line: 199
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 232,
            column: 4
          },
          end: {
            line: 232,
            column: 5
          }
        },
        loc: {
          start: {
            line: 232,
            column: 48
          },
          end: {
            line: 236,
            column: 5
          }
        },
        line: 232
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 240,
            column: 4
          },
          end: {
            line: 240,
            column: 5
          }
        },
        loc: {
          start: {
            line: 240,
            column: 63
          },
          end: {
            line: 266,
            column: 5
          }
        },
        line: 240
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 242,
            column: 62
          },
          end: {
            line: 242,
            column: 63
          }
        },
        loc: {
          start: {
            line: 242,
            column: 72
          },
          end: {
            line: 242,
            column: 77
          }
        },
        line: 242
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 270,
            column: 4
          },
          end: {
            line: 270,
            column: 5
          }
        },
        loc: {
          start: {
            line: 270,
            column: 55
          },
          end: {
            line: 291,
            column: 5
          }
        },
        line: 270
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 272,
            column: 65
          },
          end: {
            line: 272,
            column: 66
          }
        },
        loc: {
          start: {
            line: 272,
            column: 75
          },
          end: {
            line: 272,
            column: 80
          }
        },
        line: 272
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 297,
            column: 4
          },
          end: {
            line: 297,
            column: 5
          }
        },
        loc: {
          start: {
            line: 297,
            column: 38
          },
          end: {
            line: 304,
            column: 5
          }
        },
        line: 297
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 305,
            column: 4
          },
          end: {
            line: 305,
            column: 5
          }
        },
        loc: {
          start: {
            line: 305,
            column: 41
          },
          end: {
            line: 309,
            column: 5
          }
        },
        line: 305
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 310,
            column: 4
          },
          end: {
            line: 310,
            column: 5
          }
        },
        loc: {
          start: {
            line: 310,
            column: 38
          },
          end: {
            line: 316,
            column: 5
          }
        },
        line: 310
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 318,
            column: 4
          },
          end: {
            line: 318,
            column: 5
          }
        },
        loc: {
          start: {
            line: 318,
            column: 55
          },
          end: {
            line: 322,
            column: 5
          }
        },
        line: 318
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 323,
            column: 4
          },
          end: {
            line: 323,
            column: 5
          }
        },
        loc: {
          start: {
            line: 323,
            column: 51
          },
          end: {
            line: 325,
            column: 5
          }
        },
        line: 323
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 326,
            column: 4
          },
          end: {
            line: 326,
            column: 5
          }
        },
        loc: {
          start: {
            line: 326,
            column: 55
          },
          end: {
            line: 334,
            column: 5
          }
        },
        line: 326
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 335,
            column: 4
          },
          end: {
            line: 335,
            column: 5
          }
        },
        loc: {
          start: {
            line: 335,
            column: 45
          },
          end: {
            line: 337,
            column: 5
          }
        },
        line: 335
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 338,
            column: 4
          },
          end: {
            line: 338,
            column: 5
          }
        },
        loc: {
          start: {
            line: 338,
            column: 49
          },
          end: {
            line: 351,
            column: 5
          }
        },
        line: 338
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 342,
            column: 68
          },
          end: {
            line: 342,
            column: 69
          }
        },
        loc: {
          start: {
            line: 342,
            column: 78
          },
          end: {
            line: 342,
            column: 83
          }
        },
        line: 342
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 352,
            column: 4
          },
          end: {
            line: 352,
            column: 5
          }
        },
        loc: {
          start: {
            line: 352,
            column: 43
          },
          end: {
            line: 365,
            column: 5
          }
        },
        line: 352
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 356,
            column: 71
          },
          end: {
            line: 356,
            column: 72
          }
        },
        loc: {
          start: {
            line: 356,
            column: 81
          },
          end: {
            line: 356,
            column: 86
          }
        },
        line: 356
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 366,
            column: 4
          },
          end: {
            line: 366,
            column: 5
          }
        },
        loc: {
          start: {
            line: 366,
            column: 43
          },
          end: {
            line: 379,
            column: 5
          }
        },
        line: 366
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 370,
            column: 77
          },
          end: {
            line: 370,
            column: 78
          }
        },
        loc: {
          start: {
            line: 370,
            column: 87
          },
          end: {
            line: 370,
            column: 92
          }
        },
        line: 370
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 380,
            column: 4
          },
          end: {
            line: 380,
            column: 5
          }
        },
        loc: {
          start: {
            line: 380,
            column: 45
          },
          end: {
            line: 392,
            column: 5
          }
        },
        line: 380
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 393,
            column: 4
          },
          end: {
            line: 393,
            column: 5
          }
        },
        loc: {
          start: {
            line: 393,
            column: 61
          },
          end: {
            line: 403,
            column: 5
          }
        },
        line: 393
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 23,
            column: 8
          },
          end: {
            line: 33,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 23,
            column: 8
          },
          end: {
            line: 33,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 23
      },
      "1": {
        loc: {
          start: {
            line: 40,
            column: 8
          },
          end: {
            line: 50,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 40,
            column: 8
          },
          end: {
            line: 50,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 40
      },
      "2": {
        loc: {
          start: {
            line: 57,
            column: 8
          },
          end: {
            line: 67,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 57,
            column: 8
          },
          end: {
            line: 67,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 57
      },
      "3": {
        loc: {
          start: {
            line: 84,
            column: 60
          },
          end: {
            line: 84,
            column: 85
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 84,
            column: 60
          },
          end: {
            line: 84,
            column: 79
          }
        }, {
          start: {
            line: 84,
            column: 83
          },
          end: {
            line: 84,
            column: 85
          }
        }],
        line: 84
      },
      "4": {
        loc: {
          start: {
            line: 93,
            column: 53
          },
          end: {
            line: 93,
            column: 118
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 93,
            column: 75
          },
          end: {
            line: 93,
            column: 114
          }
        }, {
          start: {
            line: 93,
            column: 117
          },
          end: {
            line: 93,
            column: 118
          }
        }],
        line: 93
      },
      "5": {
        loc: {
          start: {
            line: 109,
            column: 41
          },
          end: {
            line: 109,
            column: 56
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 109,
            column: 52
          },
          end: {
            line: 109,
            column: 56
          }
        }],
        line: 109
      },
      "6": {
        loc: {
          start: {
            line: 120,
            column: 8
          },
          end: {
            line: 133,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 120,
            column: 8
          },
          end: {
            line: 133,
            column: 9
          }
        }, {
          start: {
            line: 126,
            column: 13
          },
          end: {
            line: 133,
            column: 9
          }
        }],
        line: 120
      },
      "7": {
        loc: {
          start: {
            line: 120,
            column: 12
          },
          end: {
            line: 120,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 120,
            column: 12
          },
          end: {
            line: 120,
            column: 20
          }
        }, {
          start: {
            line: 120,
            column: 24
          },
          end: {
            line: 120,
            column: 39
          }
        }, {
          start: {
            line: 120,
            column: 43
          },
          end: {
            line: 120,
            column: 59
          }
        }],
        line: 120
      },
      "8": {
        loc: {
          start: {
            line: 130,
            column: 12
          },
          end: {
            line: 132,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 130,
            column: 12
          },
          end: {
            line: 132,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 130
      },
      "9": {
        loc: {
          start: {
            line: 130,
            column: 16
          },
          end: {
            line: 130,
            column: 49
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 130,
            column: 16
          },
          end: {
            line: 130,
            column: 30
          }
        }, {
          start: {
            line: 130,
            column: 34
          },
          end: {
            line: 130,
            column: 49
          }
        }],
        line: 130
      },
      "10": {
        loc: {
          start: {
            line: 136,
            column: 62
          },
          end: {
            line: 136,
            column: 86
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 136,
            column: 62
          },
          end: {
            line: 136,
            column: 81
          }
        }, {
          start: {
            line: 136,
            column: 85
          },
          end: {
            line: 136,
            column: 86
          }
        }],
        line: 136
      },
      "11": {
        loc: {
          start: {
            line: 137,
            column: 62
          },
          end: {
            line: 137,
            column: 87
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 137,
            column: 62
          },
          end: {
            line: 137,
            column: 81
          }
        }, {
          start: {
            line: 137,
            column: 85
          },
          end: {
            line: 137,
            column: 87
          }
        }],
        line: 137
      },
      "12": {
        loc: {
          start: {
            line: 144,
            column: 8
          },
          end: {
            line: 146,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 144,
            column: 8
          },
          end: {
            line: 146,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 144
      },
      "13": {
        loc: {
          start: {
            line: 168,
            column: 8
          },
          end: {
            line: 171,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 168,
            column: 8
          },
          end: {
            line: 171,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 168
      },
      "14": {
        loc: {
          start: {
            line: 174,
            column: 8
          },
          end: {
            line: 181,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 174,
            column: 8
          },
          end: {
            line: 181,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 174
      },
      "15": {
        loc: {
          start: {
            line: 177,
            column: 12
          },
          end: {
            line: 179,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 177,
            column: 12
          },
          end: {
            line: 179,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 177
      },
      "16": {
        loc: {
          start: {
            line: 183,
            column: 8
          },
          end: {
            line: 189,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 183,
            column: 8
          },
          end: {
            line: 189,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 183
      },
      "17": {
        loc: {
          start: {
            line: 183,
            column: 12
          },
          end: {
            line: 183,
            column: 81
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 183,
            column: 12
          },
          end: {
            line: 183,
            column: 28
          }
        }, {
          start: {
            line: 183,
            column: 32
          },
          end: {
            line: 183,
            column: 81
          }
        }],
        line: 183
      },
      "18": {
        loc: {
          start: {
            line: 186,
            column: 12
          },
          end: {
            line: 188,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 186,
            column: 12
          },
          end: {
            line: 188,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 186
      },
      "19": {
        loc: {
          start: {
            line: 191,
            column: 8
          },
          end: {
            line: 193,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 191,
            column: 8
          },
          end: {
            line: 193,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 191
      },
      "20": {
        loc: {
          start: {
            line: 213,
            column: 8
          },
          end: {
            line: 215,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 213,
            column: 8
          },
          end: {
            line: 215,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 213
      },
      "21": {
        loc: {
          start: {
            line: 216,
            column: 8
          },
          end: {
            line: 218,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 216,
            column: 8
          },
          end: {
            line: 218,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 216
      },
      "22": {
        loc: {
          start: {
            line: 219,
            column: 8
          },
          end: {
            line: 221,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 219,
            column: 8
          },
          end: {
            line: 221,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 219
      },
      "23": {
        loc: {
          start: {
            line: 233,
            column: 8
          },
          end: {
            line: 234,
            column: 22
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 233,
            column: 8
          },
          end: {
            line: 234,
            column: 22
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 233
      },
      "24": {
        loc: {
          start: {
            line: 247,
            column: 12
          },
          end: {
            line: 251,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 247,
            column: 12
          },
          end: {
            line: 251,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 247
      },
      "25": {
        loc: {
          start: {
            line: 247,
            column: 16
          },
          end: {
            line: 247,
            column: 70
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 247,
            column: 16
          },
          end: {
            line: 247,
            column: 39
          }
        }, {
          start: {
            line: 247,
            column: 43
          },
          end: {
            line: 247,
            column: 70
          }
        }],
        line: 247
      },
      "26": {
        loc: {
          start: {
            line: 254,
            column: 8
          },
          end: {
            line: 256,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 254,
            column: 8
          },
          end: {
            line: 256,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 254
      },
      "27": {
        loc: {
          start: {
            line: 277,
            column: 12
          },
          end: {
            line: 281,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 277,
            column: 12
          },
          end: {
            line: 281,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 277
      },
      "28": {
        loc: {
          start: {
            line: 277,
            column: 16
          },
          end: {
            line: 277,
            column: 74
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 277,
            column: 16
          },
          end: {
            line: 277,
            column: 41
          }
        }, {
          start: {
            line: 277,
            column: 45
          },
          end: {
            line: 277,
            column: 74
          }
        }],
        line: 277
      },
      "29": {
        loc: {
          start: {
            line: 284,
            column: 8
          },
          end: {
            line: 286,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 284,
            column: 8
          },
          end: {
            line: 286,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 284
      },
      "30": {
        loc: {
          start: {
            line: 319,
            column: 8
          },
          end: {
            line: 321,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 319,
            column: 8
          },
          end: {
            line: 321,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 319
      },
      "31": {
        loc: {
          start: {
            line: 319,
            column: 12
          },
          end: {
            line: 319,
            column: 71
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 319,
            column: 12
          },
          end: {
            line: 319,
            column: 39
          }
        }, {
          start: {
            line: 319,
            column: 43
          },
          end: {
            line: 319,
            column: 71
          }
        }],
        line: 319
      },
      "32": {
        loc: {
          start: {
            line: 327,
            column: 8
          },
          end: {
            line: 329,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 327,
            column: 8
          },
          end: {
            line: 329,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 327
      },
      "33": {
        loc: {
          start: {
            line: 330,
            column: 8
          },
          end: {
            line: 332,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 330,
            column: 8
          },
          end: {
            line: 332,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 330
      },
      "34": {
        loc: {
          start: {
            line: 344,
            column: 12
          },
          end: {
            line: 348,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 344,
            column: 12
          },
          end: {
            line: 348,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 344
      },
      "35": {
        loc: {
          start: {
            line: 345,
            column: 16
          },
          end: {
            line: 347,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 345,
            column: 16
          },
          end: {
            line: 347,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 345
      },
      "36": {
        loc: {
          start: {
            line: 358,
            column: 12
          },
          end: {
            line: 362,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 358,
            column: 12
          },
          end: {
            line: 362,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 358
      },
      "37": {
        loc: {
          start: {
            line: 359,
            column: 16
          },
          end: {
            line: 361,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 359,
            column: 16
          },
          end: {
            line: 361,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 359
      },
      "38": {
        loc: {
          start: {
            line: 372,
            column: 12
          },
          end: {
            line: 376,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 372,
            column: 12
          },
          end: {
            line: 376,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 372
      },
      "39": {
        loc: {
          start: {
            line: 373,
            column: 16
          },
          end: {
            line: 375,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 373,
            column: 16
          },
          end: {
            line: 375,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 373
      },
      "40": {
        loc: {
          start: {
            line: 383,
            column: 8
          },
          end: {
            line: 384,
            column: 43
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 383,
            column: 8
          },
          end: {
            line: 384,
            column: 43
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 383
      },
      "41": {
        loc: {
          start: {
            line: 384,
            column: 19
          },
          end: {
            line: 384,
            column: 42
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 384,
            column: 19
          },
          end: {
            line: 384,
            column: 35
          }
        }, {
          start: {
            line: 384,
            column: 39
          },
          end: {
            line: 384,
            column: 42
          }
        }],
        line: 384
      },
      "42": {
        loc: {
          start: {
            line: 385,
            column: 8
          },
          end: {
            line: 386,
            column: 50
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 385,
            column: 8
          },
          end: {
            line: 386,
            column: 50
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 385
      },
      "43": {
        loc: {
          start: {
            line: 386,
            column: 19
          },
          end: {
            line: 386,
            column: 49
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 386,
            column: 19
          },
          end: {
            line: 386,
            column: 42
          }
        }, {
          start: {
            line: 386,
            column: 46
          },
          end: {
            line: 386,
            column: 49
          }
        }],
        line: 386
      },
      "44": {
        loc: {
          start: {
            line: 387,
            column: 8
          },
          end: {
            line: 388,
            column: 51
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 387,
            column: 8
          },
          end: {
            line: 388,
            column: 51
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 387
      },
      "45": {
        loc: {
          start: {
            line: 388,
            column: 19
          },
          end: {
            line: 388,
            column: 50
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 388,
            column: 19
          },
          end: {
            line: 388,
            column: 43
          }
        }, {
          start: {
            line: 388,
            column: 47
          },
          end: {
            line: 388,
            column: 50
          }
        }],
        line: 388
      },
      "46": {
        loc: {
          start: {
            line: 389,
            column: 8
          },
          end: {
            line: 390,
            column: 51
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 389,
            column: 8
          },
          end: {
            line: 390,
            column: 51
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 389
      },
      "47": {
        loc: {
          start: {
            line: 390,
            column: 19
          },
          end: {
            line: 390,
            column: 50
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 390,
            column: 19
          },
          end: {
            line: 390,
            column: 43
          }
        }, {
          start: {
            line: 390,
            column: 47
          },
          end: {
            line: 390,
            column: 50
          }
        }],
        line: 390
      },
      "48": {
        loc: {
          start: {
            line: 391,
            column: 15
          },
          end: {
            line: 391,
            column: 46
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 391,
            column: 15
          },
          end: {
            line: 391,
            column: 39
          }
        }, {
          start: {
            line: 391,
            column: 43
          },
          end: {
            line: 391,
            column: 46
          }
        }],
        line: 391
      },
      "49": {
        loc: {
          start: {
            line: 394,
            column: 8
          },
          end: {
            line: 396,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 394,
            column: 8
          },
          end: {
            line: 396,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 394
      },
      "50": {
        loc: {
          start: {
            line: 394,
            column: 12
          },
          end: {
            line: 394,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 394,
            column: 12
          },
          end: {
            line: 394,
            column: 31
          }
        }, {
          start: {
            line: 394,
            column: 35
          },
          end: {
            line: 394,
            column: 61
          }
        }],
        line: 394
      },
      "51": {
        loc: {
          start: {
            line: 397,
            column: 8
          },
          end: {
            line: 399,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 397,
            column: 8
          },
          end: {
            line: 399,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 397
      },
      "52": {
        loc: {
          start: {
            line: 397,
            column: 12
          },
          end: {
            line: 397,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 397,
            column: 12
          },
          end: {
            line: 397,
            column: 31
          }
        }, {
          start: {
            line: 397,
            column: 35
          },
          end: {
            line: 397,
            column: 59
          }
        }],
        line: 397
      },
      "53": {
        loc: {
          start: {
            line: 400,
            column: 8
          },
          end: {
            line: 402,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 400,
            column: 8
          },
          end: {
            line: 402,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 400
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0],
      "6": [0, 0],
      "7": [0, 0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\AirPropertiesCalculator.ts",
      mappings: ";AAAA;;;;;;;;GAQG;;;AAEH,2BAAkC;AAClC,+BAA4B;AAuD5B;;GAEG;AACH,MAAa,uBAAuB;IAKlC;;OAEG;IACK,MAAM,CAAC,qBAAqB;QAClC,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC5B,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,IAAA,WAAI,EAAC,SAAS,EAAE,gCAAgC,CAAC,CAAC;gBACnE,MAAM,OAAO,GAAG,IAAA,iBAAY,EAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;gBAC/C,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAC/C,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;gBACnE,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAC3D,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAChC,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,wBAAwB;QACrC,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC/B,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,IAAA,WAAI,EAAC,SAAS,EAAE,mCAAmC,CAAC,CAAC;gBACtE,MAAM,OAAO,GAAG,IAAA,iBAAY,EAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;gBAC/C,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAClD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;gBACtE,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,2BAA2B,EAAE,CAAC;YACjE,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACnC,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,qBAAqB;QAClC,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC5B,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,IAAA,WAAI,EAAC,SAAS,EAAE,gCAAgC,CAAC,CAAC;gBACnE,MAAM,OAAO,GAAG,IAAA,iBAAY,EAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;gBAC/C,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAC/C,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;gBACnE,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAC3D,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAChC,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,sBAAsB,CAAC,UAAyB;QAC5D,MAAM,IAAI,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC1C,MAAM,QAAQ,GAAa,EAAE,CAAC;QAC9B,MAAM,KAAK,GAAa,EAAE,CAAC;QAE3B,4BAA4B;QAC5B,IAAI,CAAC,qBAAqB,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QAEjD,qCAAqC;QACrC,MAAM,SAAS,GAAG,IAAI,CAAC,gCAAgC,CAAC,UAAU,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;QAEtF,+BAA+B;QAC/B,MAAM,UAAU,GAAG,IAAI,CAAC,0BAA0B,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;QAC3E,MAAM,cAAc,GAAG,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,QAAQ,EAAE,UAAU,CAAC,QAAQ,CAAC,CAAC;QAC9F,MAAM,cAAc,GAAG,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC;QAE/E,oBAAoB;QACpB,MAAM,gBAAgB,GAAG,SAAS,CAAC,OAAO,GAAG,cAAc,GAAG,cAAc,CAAC;QAC7E,MAAM,kBAAkB,GAAG,SAAS,CAAC,SAAS,GAAG,UAAU,CAAC;QAE5D,kCAAkC;QAClC,IAAI,CAAC,oBAAoB,CAAC,UAAU,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QAEvD,OAAO;YACL,OAAO,EAAE,gBAAgB;YACzB,SAAS,EAAE,kBAAkB;YAC7B,YAAY,EAAE,SAAS,CAAC,aAAa,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,QAAQ,GAAG,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3G,mBAAmB,EAAE,SAAS,CAAC,oBAAoB;YACnD,iBAAiB,EAAE;gBACjB,WAAW,EAAE,UAAU;gBACvB,QAAQ,EAAE,cAAc;gBACxB,QAAQ,EAAE,cAAc;gBACxB,QAAQ,EAAE,UAAU,GAAG,cAAc,GAAG,cAAc;aACvD;YACD,QAAQ;YACR,KAAK;SACN,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,yBAAyB,CAAC,MAA8B;QACpE,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,GAAG,IAAI,EAAE,GAAG,MAAM,CAAC;QAC5D,MAAM,QAAQ,GAAa,EAAE,CAAC;QAE9B,qCAAqC;QACrC,MAAM,UAAU,GAAkB;YAChC,WAAW,EAAE,EAAE;YACf,QAAQ,EAAE,CAAC;YACX,QAAQ,EAAE,EAAE;YACZ,GAAG,aAAa;SACjB,CAAC;QAEF,IAAI,gBAAwB,CAAC;QAC7B,IAAI,iBAAyB,CAAC;QAE9B,IAAI,QAAQ,IAAI,QAAQ,IAAI,GAAG,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;YACpD,sCAAsC;YACtC,MAAM,MAAM,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAC/C,gBAAgB,GAAG,IAAI,CAAC,2BAA2B,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YACtE,iBAAiB,GAAG,iCAAiC,CAAC;QACxD,CAAC;aAAM,CAAC;YACN,0BAA0B;YAC1B,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC;YAChD,iBAAiB,GAAG,yBAAyB,CAAC;YAE9C,IAAI,QAAQ,GAAG,GAAG,IAAI,QAAQ,GAAG,IAAI,EAAE,CAAC;gBACtC,QAAQ,CAAC,IAAI,CAAC,YAAY,QAAQ,kDAAkD,CAAC,CAAC;YACxF,CAAC;QACH,CAAC;QAED,+BAA+B;QAC/B,MAAM,cAAc,GAAG,IAAI,CAAC,wBAAwB,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;QAC7E,MAAM,kBAAkB,GAAG,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,QAAQ,IAAI,CAAC,CAAC,CAAC;QAChF,MAAM,kBAAkB,GAAG,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC;QAEjF,oBAAoB;QACpB,MAAM,kBAAkB,GAAG,cAAc,GAAG,kBAAkB,GAAG,kBAAkB,CAAC;QACpF,MAAM,WAAW,GAAG,gBAAgB,GAAG,kBAAkB,CAAC;QAE1D,kCAAkC;QAClC,MAAM,QAAQ,GAAG,IAAI,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC;QAEzD,sCAAsC;QACtC,IAAI,IAAI,CAAC,GAAG,CAAC,kBAAkB,GAAG,GAAG,CAAC,GAAG,GAAG,EAAE,CAAC;YAC7C,QAAQ,CAAC,IAAI,CAAC,+CAA+C,CAAC,kBAAkB,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACzG,CAAC;QAED,OAAO;YACL,gBAAgB,EAAE,WAAW;YAC7B,gBAAgB,EAAE,QAAQ,CAAC,OAAO;YAClC,iBAAiB,EAAE;gBACjB,WAAW,EAAE,cAAc;gBAC3B,QAAQ,EAAE,kBAAkB;gBAC5B,QAAQ,EAAE,kBAAkB;gBAC5B,QAAQ,EAAE,kBAAkB;aAC7B;YACD,iBAAiB;YACjB,QAAQ;SACT,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,4BAA4B,CACxC,QAAgB,EAChB,GAAY,EACZ,gBAAyB;QAEzB,MAAM,IAAI,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC1C,MAAM,QAAQ,GAAa,EAAE,CAAC;QAC9B,MAAM,KAAK,GAAa,EAAE,CAAC;QAE3B,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAC9C,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,QAAQ,CAAC,IAAI,CAAC,aAAa,QAAQ,6CAA6C,CAAC,CAAC;YAClF,OAAO,IAAI,CAAC,4BAA4B,CAAC,kBAAkB,EAAE,GAAG,EAAE,gBAAgB,CAAC,CAAC;QACtF,CAAC;QAED,IAAI,SAAS,GAAG,YAAY,CAAC,cAAc,CAAC;QAE5C,qBAAqB;QACrB,IAAI,GAAG,KAAK,SAAS,EAAE,CAAC;YACtB,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,aAAa,EAAE,GAAG,CAAC,CAAC;YACzE,SAAS,IAAI,WAAW,CAAC;YAEzB,IAAI,WAAW,GAAG,GAAG,EAAE,CAAC;gBACtB,QAAQ,CAAC,IAAI,CAAC,8CAA8C,GAAG,YAAY,CAAC,WAAW,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YAChH,CAAC;YAED,KAAK,CAAC,IAAI,CAAC,yBAAyB,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAChE,CAAC;QAED,iCAAiC;QACjC,IAAI,gBAAgB,IAAI,YAAY,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,EAAE,CAAC;YAC1E,MAAM,eAAe,GAAG,YAAY,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,CAAC;YAC1E,SAAS,IAAI,eAAe,CAAC;YAE7B,IAAI,eAAe,KAAK,GAAG,EAAE,CAAC;gBAC5B,KAAK,CAAC,IAAI,CAAC,6BAA6B,gBAAgB,MAAM,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YAC9F,CAAC;QACH,CAAC;QAED,8BAA8B;QAC9B,IAAI,YAAY,CAAC,KAAK,EAAE,CAAC;YACvB,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QACjC,CAAC;QAED,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IACxC,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,yBAAyB,CAAC,QAAgB;QAMtD,MAAM,QAAQ,GAAa,EAAE,CAAC;QAE9B,mCAAmC;QACnC,MAAM,gBAAgB,GAAG,KAAK,CAAC,CAAC,QAAQ;QACxC,MAAM,oBAAoB,GAAG,OAAO,CAAC,CAAC,cAAc;QACpD,MAAM,YAAY,GAAG,EAAE,CAAC,CAAC,kBAAkB;QAE3C,2DAA2D;QAC3D,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,QAAQ,GAAG,UAAU,CAAC,EAAE,MAAM,CAAC,CAAC;QAEpE,8EAA8E;QAC9E,MAAM,YAAY,GAAG,aAAa,CAAC;QAEnC,+BAA+B;QAC/B,MAAM,qBAAqB,GAAG,YAAY,GAAG,CAAC,QAAQ,GAAG,oBAAoB,CAAC,CAAC;QAC/E,MAAM,iBAAiB,GAAG,CAAC,YAAY,GAAG,MAAM,CAAC,GAAG,CAAC,qBAAqB,GAAG,MAAM,CAAC,CAAC;QAErF,kCAAkC;QAClC,IAAI,QAAQ,GAAG,IAAI,EAAE,CAAC;YACpB,QAAQ,CAAC,IAAI,CAAC,kBAAkB,QAAQ,+CAA+C,CAAC,CAAC;QAC3F,CAAC;QACD,IAAI,QAAQ,GAAG,IAAI,EAAE,CAAC;YACpB,QAAQ,CAAC,IAAI,CAAC,+DAA+D,CAAC,CAAC;QACjF,CAAC;QACD,IAAI,QAAQ,GAAG,KAAK,EAAE,CAAC;YACrB,QAAQ,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;QAC/D,CAAC;QAED,OAAO;YACL,aAAa;YACb,YAAY;YACZ,iBAAiB;YACjB,QAAQ;SACT,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,iBAAiB,CAAC,CAAS,EAAE,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,EAAU;QACxF,IAAI,EAAE,KAAK,EAAE;YAAE,OAAO,EAAE,CAAC;QACzB,OAAO,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,gCAAgC,CAAC,WAAmB,EAAE,IAAS;QAC5E,MAAM,SAAS,GAAG,IAAI,CAAC,sBAAsB,CAAC;QAC9C,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAEvE,6BAA6B;QAC7B,IAAI,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QACzB,IAAI,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAExC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAC1C,IAAI,WAAW,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,WAAW,IAAI,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;gBAC3D,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBACrB,SAAS,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;gBACzB,MAAM;YACR,CAAC;QACH,CAAC;QAED,kCAAkC;QAClC,IAAI,SAAS,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC;YACtC,OAAO,SAAS,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC3C,CAAC;QAED,6BAA6B;QAC7B,MAAM,UAAU,GAAG,SAAS,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC;QACnD,MAAM,UAAU,GAAG,SAAS,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC;QAEnD,OAAO;YACL,OAAO,EAAE,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,SAAS,EAAE,UAAU,CAAC,OAAO,EAAE,SAAS,EAAE,UAAU,CAAC,OAAO,CAAC;YAC1G,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,SAAS,EAAE,UAAU,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,CAAC,SAAS,CAAC;YAChH,aAAa,EAAE,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,SAAS,EAAE,UAAU,CAAC,aAAa,EAAE,SAAS,EAAE,UAAU,CAAC,aAAa,CAAC;YAC5H,oBAAoB,EAAE,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,SAAS,EAAE,UAAU,CAAC,oBAAoB,EAAE,SAAS,EAAE,UAAU,CAAC,oBAAoB,CAAC;SAClJ,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,2BAA2B,CAAC,QAAgB,EAAE,IAAS;QACpE,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC;QAC7C,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAE1E,2BAA2B;QAC3B,IAAI,QAAQ,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;QAC7B,IAAI,QAAQ,GAAG,UAAU,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAEjD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAC/C,IAAI,QAAQ,IAAI,UAAU,CAAC,CAAC,CAAC,IAAI,QAAQ,IAAI,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;gBAC/D,QAAQ,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;gBACzB,QAAQ,GAAG,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;gBAC7B,MAAM;YACR,CAAC;QACH,CAAC;QAED,kCAAkC;QAClC,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC;YACjC,OAAO,OAAO,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;QACtC,CAAC;QAED,6BAA6B;QAC7B,MAAM,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC7C,MAAM,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;QAE7C,OAAO,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;IAChF,CAAC;IAED,mDAAmD;IACnD,4EAA4E;IAE5E;;OAEG;IACK,MAAM,CAAC,wBAAwB;QACrC,OAAO;YACL,mBAAmB,EAAE,EAAE,WAAW,EAAE,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE;YAC7E,sBAAsB,EAAE;gBACtB,IAAI,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,aAAa,EAAE,KAAK,EAAE,oBAAoB,EAAE,MAAM,EAAE;aAClG;SACF,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,2BAA2B;QACxC,OAAO;YACL,uBAAuB,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;SAC5E,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,wBAAwB;QACrC,OAAO;YACL,SAAS,EAAE;gBACT,gBAAgB,EAAE,EAAE,cAAc,EAAE,MAAM,EAAE,aAAa,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,kBAAkB,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE;aAC7G;SACF,CAAC;IACJ,CAAC;IAED,kDAAkD;IAC1C,MAAM,CAAC,qBAAqB,CAAC,UAAyB,EAAE,QAAkB;QAChF,IAAI,UAAU,CAAC,WAAW,GAAG,EAAE,IAAI,UAAU,CAAC,WAAW,GAAG,GAAG,EAAE,CAAC;YAChE,QAAQ,CAAC,IAAI,CAAC,eAAe,UAAU,CAAC,WAAW,4CAA4C,CAAC,CAAC;QACnG,CAAC;IACH,CAAC;IAEO,MAAM,CAAC,0BAA0B,CAAC,WAAmB;QAC3D,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,WAAW,GAAG,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC;IAC/D,CAAC;IAEO,MAAM,CAAC,uBAAuB,CAAC,QAAiB,EAAE,QAAiB;QACzE,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;YAC3B,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,QAAQ,GAAG,UAAU,CAAC,EAAE,MAAM,CAAC,CAAC;QACvD,CAAC;QACD,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;YAC3B,OAAO,QAAQ,GAAG,KAAK,CAAC;QAC1B,CAAC;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAEO,MAAM,CAAC,uBAAuB,CAAC,QAAgB;QACrD,OAAO,GAAG,GAAG,CAAC,QAAQ,GAAG,GAAG,CAAC,GAAG,KAAK,CAAC;IACxC,CAAC;IAEO,MAAM,CAAC,wBAAwB,CAAC,WAAmB;QACzD,MAAM,IAAI,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAC7C,MAAM,eAAe,GAAG,IAAI,CAAC,uBAAuB,CAAC;QAErD,0CAA0C;QAC1C,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAE7E,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,IAAI,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC;gBACrC,IAAI,IAAI,CAAC,GAAG,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;oBACrC,OAAO,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,iBAAiB,CAAC;gBAC5D,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,GAAG,CAAC,CAAC,uBAAuB;IACrC,CAAC;IAEO,MAAM,CAAC,qBAAqB,CAAC,QAAgB;QACnD,MAAM,IAAI,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAC7C,MAAM,cAAc,GAAG,IAAI,CAAC,oBAAoB,CAAC;QAEjD,uCAAuC;QACvC,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAEhF,KAAK,MAAM,GAAG,IAAI,SAAS,EAAE,CAAC;YAC5B,IAAI,cAAc,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC;gBACnC,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,GAAG,CAAC,GAAG,GAAG,EAAE,CAAC;oBACnC,OAAO,cAAc,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,iBAAiB,CAAC;gBAC1D,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,GAAG,CAAC,CAAC,uBAAuB;IACrC,CAAC;IAEO,MAAM,CAAC,qBAAqB,CAAC,QAAgB;QACnD,MAAM,IAAI,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAC7C,MAAM,mBAAmB,GAAG,IAAI,CAAC,oBAAoB,CAAC;QAEtD,uCAAuC;QACvC,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAEtF,KAAK,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;YAC7B,IAAI,mBAAmB,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC;gBACxC,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;oBACjC,OAAO,mBAAmB,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,iBAAiB,CAAC;gBAC/D,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,GAAG,CAAC,CAAC,uBAAuB;IACrC,CAAC;IAEO,MAAM,CAAC,cAAc,CAAC,YAAiB,EAAE,GAAW;QAC1D,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAEvC,gCAAgC;QAChC,IAAI,GAAG,IAAI,CAAC;YAAE,OAAO,YAAY,CAAC,GAAG,IAAI,GAAG,CAAC;QAC7C,IAAI,GAAG,IAAI,EAAE;YAAE,OAAO,YAAY,CAAC,SAAS,CAAC,IAAI,GAAG,CAAC;QACrD,IAAI,GAAG,IAAI,EAAE;YAAE,OAAO,YAAY,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC;QACtD,IAAI,GAAG,IAAI,EAAE;YAAE,OAAO,YAAY,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC;QAEtD,OAAO,YAAY,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC;IACzC,CAAC;IAEO,MAAM,CAAC,oBAAoB,CAAC,UAAyB,EAAE,QAAkB,EAAE,KAAe;QAChG,IAAI,UAAU,CAAC,QAAQ,IAAI,UAAU,CAAC,QAAQ,GAAG,IAAI,EAAE,CAAC;YACtD,QAAQ,CAAC,IAAI,CAAC,kBAAkB,UAAU,CAAC,QAAQ,wCAAwC,CAAC,CAAC;QAC/F,CAAC;QAED,IAAI,UAAU,CAAC,QAAQ,IAAI,UAAU,CAAC,QAAQ,GAAG,EAAE,EAAE,CAAC;YACpD,QAAQ,CAAC,IAAI,CAAC,kBAAkB,UAAU,CAAC,QAAQ,uCAAuC,CAAC,CAAC;QAC9F,CAAC;QAED,IAAI,UAAU,CAAC,WAAW,GAAG,GAAG,EAAE,CAAC;YACjC,QAAQ,CAAC,IAAI,CAAC,qBAAqB,UAAU,CAAC,WAAW,8CAA8C,CAAC,CAAC;QAC3G,CAAC;IACH,CAAC;;AAhdH,0DAidC;AAhdgB,yCAAiB,GAAQ,IAAI,CAAC;AAC9B,4CAAoB,GAAQ,IAAI,CAAC;AACjC,yCAAiB,GAAQ,IAAI,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\AirPropertiesCalculator.ts"],
      sourcesContent: ["/**\r\n * AirPropertiesCalculator - Advanced air property calculations with interpolation\r\n * \r\n * Provides utility functions for:\r\n * - Air property interpolation from enhanced data files\r\n * - Temperature, pressure, and humidity corrections\r\n * - Elevation effects calculation\r\n * - Non-standard condition adjustments\r\n */\r\n\r\nimport { readFileSync } from 'fs';\r\nimport { join } from 'path';\r\n\r\n/**\r\n * Air condition parameters\r\n */\r\nexport interface AirConditions {\r\n  temperature: number;      // \xB0F\r\n  pressure?: number;        // in Hg\r\n  altitude?: number;        // feet above sea level\r\n  humidity?: number;        // % RH\r\n}\r\n\r\n/**\r\n * Air properties result\r\n */\r\nexport interface AirProperties {\r\n  density: number;          // lb/ft\xB3\r\n  viscosity: number;        // lb/(ft\xB7s)\r\n  specificHeat: number;     // Btu/(lb\xB7\xB0F)\r\n  thermalConductivity: number; // Btu/(hr\xB7ft\xB7\xB0F)\r\n  correctionFactors: {\r\n    temperature: number;\r\n    pressure: number;\r\n    humidity: number;\r\n    combined: number;\r\n  };\r\n  warnings: string[];\r\n  notes: string[];\r\n}\r\n\r\n/**\r\n * Velocity pressure calculation parameters\r\n */\r\nexport interface VelocityPressureParams {\r\n  velocity: number;         // FPM\r\n  airConditions?: AirConditions;\r\n  useTable?: boolean;       // Use lookup table vs formula\r\n}\r\n\r\n/**\r\n * Velocity pressure result\r\n */\r\nexport interface VelocityPressureResult {\r\n  velocityPressure: number; // in wg\r\n  correctedDensity: number; // lb/ft\xB3\r\n  correctionFactors: {\r\n    temperature: number;\r\n    altitude: number;\r\n    humidity: number;\r\n    combined: number;\r\n  };\r\n  calculationMethod: string;\r\n  warnings: string[];\r\n}\r\n\r\n/**\r\n * Enhanced air properties calculator\r\n */\r\nexport class AirPropertiesCalculator {\r\n  private static airPropertiesData: any = null;\r\n  private static velocityPressureData: any = null;\r\n  private static ductRoughnessData: any = null;\r\n\r\n  /**\r\n   * Load air properties data\r\n   */\r\n  private static loadAirPropertiesData() {\r\n    if (!this.airPropertiesData) {\r\n      try {\r\n        const dataPath = join(__dirname, '../../data/air_properties.json');\r\n        const rawData = readFileSync(dataPath, 'utf8');\r\n        this.airPropertiesData = JSON.parse(rawData);\r\n      } catch (error) {\r\n        console.warn('Could not load air properties data, using fallback');\r\n        this.airPropertiesData = this.getFallbackAirProperties();\r\n      }\r\n    }\r\n    return this.airPropertiesData;\r\n  }\r\n\r\n  /**\r\n   * Load velocity pressure data\r\n   */\r\n  private static loadVelocityPressureData() {\r\n    if (!this.velocityPressureData) {\r\n      try {\r\n        const dataPath = join(__dirname, '../../data/velocity_pressure.json');\r\n        const rawData = readFileSync(dataPath, 'utf8');\r\n        this.velocityPressureData = JSON.parse(rawData);\r\n      } catch (error) {\r\n        console.warn('Could not load velocity pressure data, using fallback');\r\n        this.velocityPressureData = this.getFallbackVelocityPressure();\r\n      }\r\n    }\r\n    return this.velocityPressureData;\r\n  }\r\n\r\n  /**\r\n   * Load duct roughness data\r\n   */\r\n  private static loadDuctRoughnessData() {\r\n    if (!this.ductRoughnessData) {\r\n      try {\r\n        const dataPath = join(__dirname, '../../data/duct_roughness.json');\r\n        const rawData = readFileSync(dataPath, 'utf8');\r\n        this.ductRoughnessData = JSON.parse(rawData);\r\n      } catch (error) {\r\n        console.warn('Could not load duct roughness data, using fallback');\r\n        this.ductRoughnessData = this.getFallbackDuctRoughness();\r\n      }\r\n    }\r\n    return this.ductRoughnessData;\r\n  }\r\n\r\n  /**\r\n   * Calculate air properties for given conditions\r\n   */\r\n  public static calculateAirProperties(conditions: AirConditions): AirProperties {\r\n    const data = this.loadAirPropertiesData();\r\n    const warnings: string[] = [];\r\n    const notes: string[] = [];\r\n\r\n    // Validate input conditions\r\n    this.validateAirConditions(conditions, warnings);\r\n\r\n    // Get base properties at temperature\r\n    const baseProps = this.interpolateTemperatureProperties(conditions.temperature, data);\r\n\r\n    // Calculate correction factors\r\n    const tempFactor = this.calculateTemperatureFactor(conditions.temperature);\r\n    const pressureFactor = this.calculatePressureFactor(conditions.pressure, conditions.altitude);\r\n    const humidityFactor = this.calculateHumidityFactor(conditions.humidity || 50);\r\n\r\n    // Apply corrections\r\n    const correctedDensity = baseProps.density * pressureFactor * humidityFactor;\r\n    const correctedViscosity = baseProps.viscosity * tempFactor;\r\n\r\n    // Add condition-specific warnings\r\n    this.addConditionWarnings(conditions, warnings, notes);\r\n\r\n    return {\r\n      density: correctedDensity,\r\n      viscosity: correctedViscosity,\r\n      specificHeat: baseProps.specific_heat * (conditions.humidity ? 1 + (conditions.humidity / 100) * 0.026 : 1),\r\n      thermalConductivity: baseProps.thermal_conductivity,\r\n      correctionFactors: {\r\n        temperature: tempFactor,\r\n        pressure: pressureFactor,\r\n        humidity: humidityFactor,\r\n        combined: tempFactor * pressureFactor * humidityFactor\r\n      },\r\n      warnings,\r\n      notes\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate velocity pressure with corrections\r\n   */\r\n  public static calculateVelocityPressure(params: VelocityPressureParams): VelocityPressureResult {\r\n    const { velocity, airConditions, useTable = true } = params;\r\n    const warnings: string[] = [];\r\n\r\n    // Default conditions if not provided\r\n    const conditions: AirConditions = {\r\n      temperature: 70,\r\n      altitude: 0,\r\n      humidity: 50,\r\n      ...airConditions\r\n    };\r\n\r\n    let velocityPressure: number;\r\n    let calculationMethod: string;\r\n\r\n    if (useTable && velocity >= 100 && velocity <= 5000) {\r\n      // Use lookup table with interpolation\r\n      const vpData = this.loadVelocityPressureData();\r\n      velocityPressure = this.interpolateVelocityPressure(velocity, vpData);\r\n      calculationMethod = 'Table lookup with interpolation';\r\n    } else {\r\n      // Use formula calculation\r\n      velocityPressure = Math.pow(velocity / 4005, 2);\r\n      calculationMethod = 'Formula: VP = (V/4005)\xB2';\r\n      \r\n      if (velocity < 100 || velocity > 5000) {\r\n        warnings.push(`Velocity ${velocity} FPM is outside recommended range (100-5000 FPM)`);\r\n      }\r\n    }\r\n\r\n    // Calculate correction factors\r\n    const tempCorrection = this.getTemperatureCorrection(conditions.temperature);\r\n    const altitudeCorrection = this.getAltitudeCorrection(conditions.altitude || 0);\r\n    const humidityCorrection = this.getHumidityCorrection(conditions.humidity || 50);\r\n\r\n    // Apply corrections\r\n    const combinedCorrection = tempCorrection * altitudeCorrection * humidityCorrection;\r\n    const correctedVP = velocityPressure * combinedCorrection;\r\n\r\n    // Calculate corrected air density\r\n    const airProps = this.calculateAirProperties(conditions);\r\n    \r\n    // Add warnings for extreme conditions\r\n    if (Math.abs(combinedCorrection - 1.0) > 0.1) {\r\n      warnings.push(`Significant air density correction applied: ${(combinedCorrection * 100).toFixed(1)}%`);\r\n    }\r\n\r\n    return {\r\n      velocityPressure: correctedVP,\r\n      correctedDensity: airProps.density,\r\n      correctionFactors: {\r\n        temperature: tempCorrection,\r\n        altitude: altitudeCorrection,\r\n        humidity: humidityCorrection,\r\n        combined: combinedCorrection\r\n      },\r\n      calculationMethod,\r\n      warnings\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get enhanced material roughness with aging and condition factors\r\n   */\r\n  public static getEnhancedMaterialRoughness(\r\n    material: string, \r\n    age?: number, \r\n    surfaceCondition?: string\r\n  ): { roughness: number; warnings: string[]; notes: string[] } {\r\n    const data = this.loadDuctRoughnessData();\r\n    const warnings: string[] = [];\r\n    const notes: string[] = [];\r\n\r\n    const materialData = data.materials[material];\r\n    if (!materialData) {\r\n      warnings.push(`Material '${material}' not found, using galvanized steel default`);\r\n      return this.getEnhancedMaterialRoughness('galvanized_steel', age, surfaceCondition);\r\n    }\r\n\r\n    let roughness = materialData.base_roughness;\r\n\r\n    // Apply aging factor\r\n    if (age !== undefined) {\r\n      const agingFactor = this.getAgingFactor(materialData.aging_factors, age);\r\n      roughness *= agingFactor;\r\n      \r\n      if (agingFactor > 1.5) {\r\n        warnings.push(`Significant roughness increase due to age (${age} years): ${(agingFactor * 100).toFixed(0)}%`);\r\n      }\r\n      \r\n      notes.push(`Aging factor applied: ${agingFactor.toFixed(2)}`);\r\n    }\r\n\r\n    // Apply surface condition factor\r\n    if (surfaceCondition && materialData.surface_conditions[surfaceCondition]) {\r\n      const conditionFactor = materialData.surface_conditions[surfaceCondition];\r\n      roughness *= conditionFactor;\r\n      \r\n      if (conditionFactor !== 1.0) {\r\n        notes.push(`Surface condition factor (${surfaceCondition}): ${conditionFactor.toFixed(2)}`);\r\n      }\r\n    }\r\n\r\n    // Add material-specific notes\r\n    if (materialData.notes) {\r\n      notes.push(materialData.notes);\r\n    }\r\n\r\n    return { roughness, warnings, notes };\r\n  }\r\n\r\n  /**\r\n   * Calculate elevation effects on air properties\r\n   */\r\n  public static calculateElevationEffects(altitude: number): {\r\n    pressureRatio: number;\r\n    densityRatio: number;\r\n    temperatureEffect: number;\r\n    warnings: string[];\r\n  } {\r\n    const warnings: string[] = [];\r\n\r\n    // Standard atmosphere calculations\r\n    const seaLevelPressure = 29.92; // in Hg\r\n    const temperatureLapseRate = 0.00356; // \xB0F per foot\r\n    const standardTemp = 59; // \xB0F at sea level\r\n\r\n    // Calculate pressure ratio (simplified barometric formula)\r\n    const pressureRatio = Math.pow(1 - (altitude * 6.87535e-6), 5.2561);\r\n    \r\n    // Calculate density ratio (proportional to pressure for constant temperature)\r\n    const densityRatio = pressureRatio;\r\n    \r\n    // Calculate temperature effect\r\n    const temperatureAtAltitude = standardTemp - (altitude * temperatureLapseRate);\r\n    const temperatureEffect = (standardTemp + 459.67) / (temperatureAtAltitude + 459.67);\r\n\r\n    // Add warnings for high altitudes\r\n    if (altitude > 5000) {\r\n      warnings.push(`High altitude (${altitude} ft) requires significant density corrections`);\r\n    }\r\n    if (altitude > 8000) {\r\n      warnings.push(`Very high altitude - verify equipment ratings and performance`);\r\n    }\r\n    if (altitude > 10000) {\r\n      warnings.push(`Altitude exceeds typical HVAC design limits`);\r\n    }\r\n\r\n    return {\r\n      pressureRatio,\r\n      densityRatio,\r\n      temperatureEffect,\r\n      warnings\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Linear interpolation utility\r\n   */\r\n  private static linearInterpolate(x: number, x1: number, y1: number, x2: number, y2: number): number {\r\n    if (x1 === x2) return y1;\r\n    return y1 + (x - x1) * (y2 - y1) / (x2 - x1);\r\n  }\r\n\r\n  /**\r\n   * Interpolate temperature properties\r\n   */\r\n  private static interpolateTemperatureProperties(temperature: number, data: any): any {\r\n    const tempProps = data.temperature_properties;\r\n    const temps = Object.keys(tempProps).map(Number).sort((a, b) => a - b);\r\n\r\n    // Find bounding temperatures\r\n    let lowerTemp = temps[0];\r\n    let upperTemp = temps[temps.length - 1];\r\n\r\n    for (let i = 0; i < temps.length - 1; i++) {\r\n      if (temperature >= temps[i] && temperature <= temps[i + 1]) {\r\n        lowerTemp = temps[i];\r\n        upperTemp = temps[i + 1];\r\n        break;\r\n      }\r\n    }\r\n\r\n    // If exact match, return directly\r\n    if (tempProps[temperature.toString()]) {\r\n      return tempProps[temperature.toString()];\r\n    }\r\n\r\n    // Interpolate between bounds\r\n    const lowerProps = tempProps[lowerTemp.toString()];\r\n    const upperProps = tempProps[upperTemp.toString()];\r\n\r\n    return {\r\n      density: this.linearInterpolate(temperature, lowerTemp, lowerProps.density, upperTemp, upperProps.density),\r\n      viscosity: this.linearInterpolate(temperature, lowerTemp, lowerProps.viscosity, upperTemp, upperProps.viscosity),\r\n      specific_heat: this.linearInterpolate(temperature, lowerTemp, lowerProps.specific_heat, upperTemp, upperProps.specific_heat),\r\n      thermal_conductivity: this.linearInterpolate(temperature, lowerTemp, lowerProps.thermal_conductivity, upperTemp, upperProps.thermal_conductivity)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Interpolate velocity pressure from table\r\n   */\r\n  private static interpolateVelocityPressure(velocity: number, data: any): number {\r\n    const vpTable = data.velocity_pressure_table;\r\n    const velocities = Object.keys(vpTable).map(Number).sort((a, b) => a - b);\r\n\r\n    // Find bounding velocities\r\n    let lowerVel = velocities[0];\r\n    let upperVel = velocities[velocities.length - 1];\r\n\r\n    for (let i = 0; i < velocities.length - 1; i++) {\r\n      if (velocity >= velocities[i] && velocity <= velocities[i + 1]) {\r\n        lowerVel = velocities[i];\r\n        upperVel = velocities[i + 1];\r\n        break;\r\n      }\r\n    }\r\n\r\n    // If exact match, return directly\r\n    if (vpTable[velocity.toString()]) {\r\n      return vpTable[velocity.toString()];\r\n    }\r\n\r\n    // Interpolate between bounds\r\n    const lowerVP = vpTable[lowerVel.toString()];\r\n    const upperVP = vpTable[upperVel.toString()];\r\n\r\n    return this.linearInterpolate(velocity, lowerVel, lowerVP, upperVel, upperVP);\r\n  }\r\n\r\n  // Additional helper methods would continue here...\r\n  // (Temperature factor, pressure factor, humidity factor calculations, etc.)\r\n\r\n  /**\r\n   * Fallback data methods\r\n   */\r\n  private static getFallbackAirProperties(): any {\r\n    return {\r\n      standard_conditions: { temperature: 70, density: 0.075, viscosity: 1.204e-5 },\r\n      temperature_properties: {\r\n        \"70\": { density: 0.075, viscosity: 1.204e-5, specific_heat: 0.240, thermal_conductivity: 0.0148 }\r\n      }\r\n    };\r\n  }\r\n\r\n  private static getFallbackVelocityPressure(): any {\r\n    return {\r\n      velocity_pressure_table: { \"1000\": 0.0623, \"1500\": 0.1406, \"2000\": 0.2501 }\r\n    };\r\n  }\r\n\r\n  private static getFallbackDuctRoughness(): any {\r\n    return {\r\n      materials: {\r\n        galvanized_steel: { base_roughness: 0.0003, aging_factors: { new: 1.0 }, surface_conditions: { good: 1.0 } }\r\n      }\r\n    };\r\n  }\r\n\r\n  // Placeholder methods for additional calculations\r\n  private static validateAirConditions(conditions: AirConditions, warnings: string[]): void {\r\n    if (conditions.temperature < 32 || conditions.temperature > 200) {\r\n      warnings.push(`Temperature ${conditions.temperature}\xB0F is outside normal HVAC range (32-200\xB0F)`);\r\n    }\r\n  }\r\n\r\n  private static calculateTemperatureFactor(temperature: number): number {\r\n    return Math.pow((temperature + 459.67) / (70 + 459.67), 0.7);\r\n  }\r\n\r\n  private static calculatePressureFactor(pressure?: number, altitude?: number): number {\r\n    if (altitude !== undefined) {\r\n      return Math.pow(1 - (altitude * 6.87535e-6), 5.2561);\r\n    }\r\n    if (pressure !== undefined) {\r\n      return pressure / 29.92;\r\n    }\r\n    return 1.0;\r\n  }\r\n\r\n  private static calculateHumidityFactor(humidity: number): number {\r\n    return 1.0 - (humidity / 100) * 0.016;\r\n  }\r\n\r\n  private static getTemperatureCorrection(temperature: number): number {\r\n    const data = this.loadVelocityPressureData();\r\n    const tempCorrections = data.temperature_corrections;\r\n    \r\n    // Find closest temperature or interpolate\r\n    const temps = Object.keys(tempCorrections).map(Number).sort((a, b) => a - b);\r\n    \r\n    for (const temp of temps) {\r\n      if (tempCorrections[temp.toString()]) {\r\n        if (Math.abs(temperature - temp) < 5) {\r\n          return tempCorrections[temp.toString()].correction_factor;\r\n        }\r\n      }\r\n    }\r\n    \r\n    return 1.0; // Default if not found\r\n  }\r\n\r\n  private static getAltitudeCorrection(altitude: number): number {\r\n    const data = this.loadVelocityPressureData();\r\n    const altCorrections = data.altitude_corrections;\r\n    \r\n    // Find closest altitude or interpolate\r\n    const altitudes = Object.keys(altCorrections).map(Number).sort((a, b) => a - b);\r\n    \r\n    for (const alt of altitudes) {\r\n      if (altCorrections[alt.toString()]) {\r\n        if (Math.abs(altitude - alt) < 500) {\r\n          return altCorrections[alt.toString()].correction_factor;\r\n        }\r\n      }\r\n    }\r\n    \r\n    return 1.0; // Default if not found\r\n  }\r\n\r\n  private static getHumidityCorrection(humidity: number): number {\r\n    const data = this.loadVelocityPressureData();\r\n    const humidityCorrections = data.humidity_corrections;\r\n    \r\n    // Find closest humidity or interpolate\r\n    const humidities = Object.keys(humidityCorrections).map(Number).sort((a, b) => a - b);\r\n    \r\n    for (const hum of humidities) {\r\n      if (humidityCorrections[hum.toString()]) {\r\n        if (Math.abs(humidity - hum) < 5) {\r\n          return humidityCorrections[hum.toString()].correction_factor;\r\n        }\r\n      }\r\n    }\r\n    \r\n    return 1.0; // Default if not found\r\n  }\r\n\r\n  private static getAgingFactor(agingFactors: any, age: number): number {\r\n    const ages = Object.keys(agingFactors);\r\n    \r\n    // Find appropriate aging factor\r\n    if (age <= 5) return agingFactors.new || 1.0;\r\n    if (age <= 10) return agingFactors['5_years'] || 1.2;\r\n    if (age <= 15) return agingFactors['10_years'] || 1.5;\r\n    if (age <= 20) return agingFactors['15_years'] || 2.0;\r\n    \r\n    return agingFactors['20_years'] || 2.5;\r\n  }\r\n\r\n  private static addConditionWarnings(conditions: AirConditions, warnings: string[], notes: string[]): void {\r\n    if (conditions.altitude && conditions.altitude > 5000) {\r\n      warnings.push(`High altitude (${conditions.altitude} ft) affects air density significantly`);\r\n    }\r\n    \r\n    if (conditions.humidity && conditions.humidity > 80) {\r\n      warnings.push(`High humidity (${conditions.humidity}% RH) may cause condensation in ducts`);\r\n    }\r\n    \r\n    if (conditions.temperature > 180) {\r\n      warnings.push(`High temperature (${conditions.temperature}\xB0F) requires special material considerations`);\r\n    }\r\n  }\r\n}\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "d5e1904a5d2fa8c86c7afd4a596170a6ccac18d1"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_37yapc4cs = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_37yapc4cs();
cov_37yapc4cs().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_37yapc4cs().s[1]++;
exports.AirPropertiesCalculator = void 0;
const fs_1 =
/* istanbul ignore next */
(cov_37yapc4cs().s[2]++, require("fs"));
const path_1 =
/* istanbul ignore next */
(cov_37yapc4cs().s[3]++, require("path"));
/**
 * Enhanced air properties calculator
 */
class AirPropertiesCalculator {
  /**
   * Load air properties data
   */
  static loadAirPropertiesData() {
    /* istanbul ignore next */
    cov_37yapc4cs().f[0]++;
    cov_37yapc4cs().s[4]++;
    if (!this.airPropertiesData) {
      /* istanbul ignore next */
      cov_37yapc4cs().b[0][0]++;
      cov_37yapc4cs().s[5]++;
      try {
        const dataPath =
        /* istanbul ignore next */
        (cov_37yapc4cs().s[6]++, (0, path_1.join)(__dirname, '../../data/air_properties.json'));
        const rawData =
        /* istanbul ignore next */
        (cov_37yapc4cs().s[7]++, (0, fs_1.readFileSync)(dataPath, 'utf8'));
        /* istanbul ignore next */
        cov_37yapc4cs().s[8]++;
        this.airPropertiesData = JSON.parse(rawData);
      } catch (error) {
        /* istanbul ignore next */
        cov_37yapc4cs().s[9]++;
        console.warn('Could not load air properties data, using fallback');
        /* istanbul ignore next */
        cov_37yapc4cs().s[10]++;
        this.airPropertiesData = this.getFallbackAirProperties();
      }
    } else
    /* istanbul ignore next */
    {
      cov_37yapc4cs().b[0][1]++;
    }
    cov_37yapc4cs().s[11]++;
    return this.airPropertiesData;
  }
  /**
   * Load velocity pressure data
   */
  static loadVelocityPressureData() {
    /* istanbul ignore next */
    cov_37yapc4cs().f[1]++;
    cov_37yapc4cs().s[12]++;
    if (!this.velocityPressureData) {
      /* istanbul ignore next */
      cov_37yapc4cs().b[1][0]++;
      cov_37yapc4cs().s[13]++;
      try {
        const dataPath =
        /* istanbul ignore next */
        (cov_37yapc4cs().s[14]++, (0, path_1.join)(__dirname, '../../data/velocity_pressure.json'));
        const rawData =
        /* istanbul ignore next */
        (cov_37yapc4cs().s[15]++, (0, fs_1.readFileSync)(dataPath, 'utf8'));
        /* istanbul ignore next */
        cov_37yapc4cs().s[16]++;
        this.velocityPressureData = JSON.parse(rawData);
      } catch (error) {
        /* istanbul ignore next */
        cov_37yapc4cs().s[17]++;
        console.warn('Could not load velocity pressure data, using fallback');
        /* istanbul ignore next */
        cov_37yapc4cs().s[18]++;
        this.velocityPressureData = this.getFallbackVelocityPressure();
      }
    } else
    /* istanbul ignore next */
    {
      cov_37yapc4cs().b[1][1]++;
    }
    cov_37yapc4cs().s[19]++;
    return this.velocityPressureData;
  }
  /**
   * Load duct roughness data
   */
  static loadDuctRoughnessData() {
    /* istanbul ignore next */
    cov_37yapc4cs().f[2]++;
    cov_37yapc4cs().s[20]++;
    if (!this.ductRoughnessData) {
      /* istanbul ignore next */
      cov_37yapc4cs().b[2][0]++;
      cov_37yapc4cs().s[21]++;
      try {
        const dataPath =
        /* istanbul ignore next */
        (cov_37yapc4cs().s[22]++, (0, path_1.join)(__dirname, '../../data/duct_roughness.json'));
        const rawData =
        /* istanbul ignore next */
        (cov_37yapc4cs().s[23]++, (0, fs_1.readFileSync)(dataPath, 'utf8'));
        /* istanbul ignore next */
        cov_37yapc4cs().s[24]++;
        this.ductRoughnessData = JSON.parse(rawData);
      } catch (error) {
        /* istanbul ignore next */
        cov_37yapc4cs().s[25]++;
        console.warn('Could not load duct roughness data, using fallback');
        /* istanbul ignore next */
        cov_37yapc4cs().s[26]++;
        this.ductRoughnessData = this.getFallbackDuctRoughness();
      }
    } else
    /* istanbul ignore next */
    {
      cov_37yapc4cs().b[2][1]++;
    }
    cov_37yapc4cs().s[27]++;
    return this.ductRoughnessData;
  }
  /**
   * Calculate air properties for given conditions
   */
  static calculateAirProperties(conditions) {
    /* istanbul ignore next */
    cov_37yapc4cs().f[3]++;
    const data =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[28]++, this.loadAirPropertiesData());
    const warnings =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[29]++, []);
    const notes =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[30]++, []);
    // Validate input conditions
    /* istanbul ignore next */
    cov_37yapc4cs().s[31]++;
    this.validateAirConditions(conditions, warnings);
    // Get base properties at temperature
    const baseProps =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[32]++, this.interpolateTemperatureProperties(conditions.temperature, data));
    // Calculate correction factors
    const tempFactor =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[33]++, this.calculateTemperatureFactor(conditions.temperature));
    const pressureFactor =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[34]++, this.calculatePressureFactor(conditions.pressure, conditions.altitude));
    const humidityFactor =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[35]++, this.calculateHumidityFactor(
    /* istanbul ignore next */
    (cov_37yapc4cs().b[3][0]++, conditions.humidity) ||
    /* istanbul ignore next */
    (cov_37yapc4cs().b[3][1]++, 50)));
    // Apply corrections
    const correctedDensity =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[36]++, baseProps.density * pressureFactor * humidityFactor);
    const correctedViscosity =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[37]++, baseProps.viscosity * tempFactor);
    // Add condition-specific warnings
    /* istanbul ignore next */
    cov_37yapc4cs().s[38]++;
    this.addConditionWarnings(conditions, warnings, notes);
    /* istanbul ignore next */
    cov_37yapc4cs().s[39]++;
    return {
      density: correctedDensity,
      viscosity: correctedViscosity,
      specificHeat: baseProps.specific_heat * (conditions.humidity ?
      /* istanbul ignore next */
      (cov_37yapc4cs().b[4][0]++, 1 + conditions.humidity / 100 * 0.026) :
      /* istanbul ignore next */
      (cov_37yapc4cs().b[4][1]++, 1)),
      thermalConductivity: baseProps.thermal_conductivity,
      correctionFactors: {
        temperature: tempFactor,
        pressure: pressureFactor,
        humidity: humidityFactor,
        combined: tempFactor * pressureFactor * humidityFactor
      },
      warnings,
      notes
    };
  }
  /**
   * Calculate velocity pressure with corrections
   */
  static calculateVelocityPressure(params) {
    /* istanbul ignore next */
    cov_37yapc4cs().f[4]++;
    const {
      velocity,
      airConditions,
      useTable =
      /* istanbul ignore next */
      (cov_37yapc4cs().b[5][0]++, true)
    } =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[40]++, params);
    const warnings =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[41]++, []);
    // Default conditions if not provided
    const conditions =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[42]++, {
      temperature: 70,
      altitude: 0,
      humidity: 50,
      ...airConditions
    });
    let velocityPressure;
    let calculationMethod;
    /* istanbul ignore next */
    cov_37yapc4cs().s[43]++;
    if (
    /* istanbul ignore next */
    (cov_37yapc4cs().b[7][0]++, useTable) &&
    /* istanbul ignore next */
    (cov_37yapc4cs().b[7][1]++, velocity >= 100) &&
    /* istanbul ignore next */
    (cov_37yapc4cs().b[7][2]++, velocity <= 5000)) {
      /* istanbul ignore next */
      cov_37yapc4cs().b[6][0]++;
      // Use lookup table with interpolation
      const vpData =
      /* istanbul ignore next */
      (cov_37yapc4cs().s[44]++, this.loadVelocityPressureData());
      /* istanbul ignore next */
      cov_37yapc4cs().s[45]++;
      velocityPressure = this.interpolateVelocityPressure(velocity, vpData);
      /* istanbul ignore next */
      cov_37yapc4cs().s[46]++;
      calculationMethod = 'Table lookup with interpolation';
    } else {
      /* istanbul ignore next */
      cov_37yapc4cs().b[6][1]++;
      cov_37yapc4cs().s[47]++;
      // Use formula calculation
      velocityPressure = Math.pow(velocity / 4005, 2);
      /* istanbul ignore next */
      cov_37yapc4cs().s[48]++;
      calculationMethod = 'Formula: VP = (V/4005)²';
      /* istanbul ignore next */
      cov_37yapc4cs().s[49]++;
      if (
      /* istanbul ignore next */
      (cov_37yapc4cs().b[9][0]++, velocity < 100) ||
      /* istanbul ignore next */
      (cov_37yapc4cs().b[9][1]++, velocity > 5000)) {
        /* istanbul ignore next */
        cov_37yapc4cs().b[8][0]++;
        cov_37yapc4cs().s[50]++;
        warnings.push(`Velocity ${velocity} FPM is outside recommended range (100-5000 FPM)`);
      } else
      /* istanbul ignore next */
      {
        cov_37yapc4cs().b[8][1]++;
      }
    }
    // Calculate correction factors
    const tempCorrection =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[51]++, this.getTemperatureCorrection(conditions.temperature));
    const altitudeCorrection =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[52]++, this.getAltitudeCorrection(
    /* istanbul ignore next */
    (cov_37yapc4cs().b[10][0]++, conditions.altitude) ||
    /* istanbul ignore next */
    (cov_37yapc4cs().b[10][1]++, 0)));
    const humidityCorrection =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[53]++, this.getHumidityCorrection(
    /* istanbul ignore next */
    (cov_37yapc4cs().b[11][0]++, conditions.humidity) ||
    /* istanbul ignore next */
    (cov_37yapc4cs().b[11][1]++, 50)));
    // Apply corrections
    const combinedCorrection =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[54]++, tempCorrection * altitudeCorrection * humidityCorrection);
    const correctedVP =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[55]++, velocityPressure * combinedCorrection);
    // Calculate corrected air density
    const airProps =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[56]++, this.calculateAirProperties(conditions));
    // Add warnings for extreme conditions
    /* istanbul ignore next */
    cov_37yapc4cs().s[57]++;
    if (Math.abs(combinedCorrection - 1.0) > 0.1) {
      /* istanbul ignore next */
      cov_37yapc4cs().b[12][0]++;
      cov_37yapc4cs().s[58]++;
      warnings.push(`Significant air density correction applied: ${(combinedCorrection * 100).toFixed(1)}%`);
    } else
    /* istanbul ignore next */
    {
      cov_37yapc4cs().b[12][1]++;
    }
    cov_37yapc4cs().s[59]++;
    return {
      velocityPressure: correctedVP,
      correctedDensity: airProps.density,
      correctionFactors: {
        temperature: tempCorrection,
        altitude: altitudeCorrection,
        humidity: humidityCorrection,
        combined: combinedCorrection
      },
      calculationMethod,
      warnings
    };
  }
  /**
   * Get enhanced material roughness with aging and condition factors
   */
  static getEnhancedMaterialRoughness(material, age, surfaceCondition) {
    /* istanbul ignore next */
    cov_37yapc4cs().f[5]++;
    const data =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[60]++, this.loadDuctRoughnessData());
    const warnings =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[61]++, []);
    const notes =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[62]++, []);
    const materialData =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[63]++, data.materials[material]);
    /* istanbul ignore next */
    cov_37yapc4cs().s[64]++;
    if (!materialData) {
      /* istanbul ignore next */
      cov_37yapc4cs().b[13][0]++;
      cov_37yapc4cs().s[65]++;
      warnings.push(`Material '${material}' not found, using galvanized steel default`);
      /* istanbul ignore next */
      cov_37yapc4cs().s[66]++;
      return this.getEnhancedMaterialRoughness('galvanized_steel', age, surfaceCondition);
    } else
    /* istanbul ignore next */
    {
      cov_37yapc4cs().b[13][1]++;
    }
    let roughness =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[67]++, materialData.base_roughness);
    // Apply aging factor
    /* istanbul ignore next */
    cov_37yapc4cs().s[68]++;
    if (age !== undefined) {
      /* istanbul ignore next */
      cov_37yapc4cs().b[14][0]++;
      const agingFactor =
      /* istanbul ignore next */
      (cov_37yapc4cs().s[69]++, this.getAgingFactor(materialData.aging_factors, age));
      /* istanbul ignore next */
      cov_37yapc4cs().s[70]++;
      roughness *= agingFactor;
      /* istanbul ignore next */
      cov_37yapc4cs().s[71]++;
      if (agingFactor > 1.5) {
        /* istanbul ignore next */
        cov_37yapc4cs().b[15][0]++;
        cov_37yapc4cs().s[72]++;
        warnings.push(`Significant roughness increase due to age (${age} years): ${(agingFactor * 100).toFixed(0)}%`);
      } else
      /* istanbul ignore next */
      {
        cov_37yapc4cs().b[15][1]++;
      }
      cov_37yapc4cs().s[73]++;
      notes.push(`Aging factor applied: ${agingFactor.toFixed(2)}`);
    } else
    /* istanbul ignore next */
    {
      cov_37yapc4cs().b[14][1]++;
    }
    // Apply surface condition factor
    cov_37yapc4cs().s[74]++;
    if (
    /* istanbul ignore next */
    (cov_37yapc4cs().b[17][0]++, surfaceCondition) &&
    /* istanbul ignore next */
    (cov_37yapc4cs().b[17][1]++, materialData.surface_conditions[surfaceCondition])) {
      /* istanbul ignore next */
      cov_37yapc4cs().b[16][0]++;
      const conditionFactor =
      /* istanbul ignore next */
      (cov_37yapc4cs().s[75]++, materialData.surface_conditions[surfaceCondition]);
      /* istanbul ignore next */
      cov_37yapc4cs().s[76]++;
      roughness *= conditionFactor;
      /* istanbul ignore next */
      cov_37yapc4cs().s[77]++;
      if (conditionFactor !== 1.0) {
        /* istanbul ignore next */
        cov_37yapc4cs().b[18][0]++;
        cov_37yapc4cs().s[78]++;
        notes.push(`Surface condition factor (${surfaceCondition}): ${conditionFactor.toFixed(2)}`);
      } else
      /* istanbul ignore next */
      {
        cov_37yapc4cs().b[18][1]++;
      }
    } else
    /* istanbul ignore next */
    {
      cov_37yapc4cs().b[16][1]++;
    }
    // Add material-specific notes
    cov_37yapc4cs().s[79]++;
    if (materialData.notes) {
      /* istanbul ignore next */
      cov_37yapc4cs().b[19][0]++;
      cov_37yapc4cs().s[80]++;
      notes.push(materialData.notes);
    } else
    /* istanbul ignore next */
    {
      cov_37yapc4cs().b[19][1]++;
    }
    cov_37yapc4cs().s[81]++;
    return {
      roughness,
      warnings,
      notes
    };
  }
  /**
   * Calculate elevation effects on air properties
   */
  static calculateElevationEffects(altitude) {
    /* istanbul ignore next */
    cov_37yapc4cs().f[6]++;
    const warnings =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[82]++, []);
    // Standard atmosphere calculations
    const seaLevelPressure =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[83]++, 29.92); // in Hg
    const temperatureLapseRate =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[84]++, 0.00356); // °F per foot
    const standardTemp =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[85]++, 59); // °F at sea level
    // Calculate pressure ratio (simplified barometric formula)
    const pressureRatio =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[86]++, Math.pow(1 - altitude * 6.87535e-6, 5.2561));
    // Calculate density ratio (proportional to pressure for constant temperature)
    const densityRatio =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[87]++, pressureRatio);
    // Calculate temperature effect
    const temperatureAtAltitude =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[88]++, standardTemp - altitude * temperatureLapseRate);
    const temperatureEffect =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[89]++, (standardTemp + 459.67) / (temperatureAtAltitude + 459.67));
    // Add warnings for high altitudes
    /* istanbul ignore next */
    cov_37yapc4cs().s[90]++;
    if (altitude > 5000) {
      /* istanbul ignore next */
      cov_37yapc4cs().b[20][0]++;
      cov_37yapc4cs().s[91]++;
      warnings.push(`High altitude (${altitude} ft) requires significant density corrections`);
    } else
    /* istanbul ignore next */
    {
      cov_37yapc4cs().b[20][1]++;
    }
    cov_37yapc4cs().s[92]++;
    if (altitude > 8000) {
      /* istanbul ignore next */
      cov_37yapc4cs().b[21][0]++;
      cov_37yapc4cs().s[93]++;
      warnings.push(`Very high altitude - verify equipment ratings and performance`);
    } else
    /* istanbul ignore next */
    {
      cov_37yapc4cs().b[21][1]++;
    }
    cov_37yapc4cs().s[94]++;
    if (altitude > 10000) {
      /* istanbul ignore next */
      cov_37yapc4cs().b[22][0]++;
      cov_37yapc4cs().s[95]++;
      warnings.push(`Altitude exceeds typical HVAC design limits`);
    } else
    /* istanbul ignore next */
    {
      cov_37yapc4cs().b[22][1]++;
    }
    cov_37yapc4cs().s[96]++;
    return {
      pressureRatio,
      densityRatio,
      temperatureEffect,
      warnings
    };
  }
  /**
   * Linear interpolation utility
   */
  static linearInterpolate(x, x1, y1, x2, y2) {
    /* istanbul ignore next */
    cov_37yapc4cs().f[7]++;
    cov_37yapc4cs().s[97]++;
    if (x1 === x2) {
      /* istanbul ignore next */
      cov_37yapc4cs().b[23][0]++;
      cov_37yapc4cs().s[98]++;
      return y1;
    } else
    /* istanbul ignore next */
    {
      cov_37yapc4cs().b[23][1]++;
    }
    cov_37yapc4cs().s[99]++;
    return y1 + (x - x1) * (y2 - y1) / (x2 - x1);
  }
  /**
   * Interpolate temperature properties
   */
  static interpolateTemperatureProperties(temperature, data) {
    /* istanbul ignore next */
    cov_37yapc4cs().f[8]++;
    const tempProps =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[100]++, data.temperature_properties);
    const temps =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[101]++, Object.keys(tempProps).map(Number).sort((a, b) => {
      /* istanbul ignore next */
      cov_37yapc4cs().f[9]++;
      cov_37yapc4cs().s[102]++;
      return a - b;
    }));
    // Find bounding temperatures
    let lowerTemp =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[103]++, temps[0]);
    let upperTemp =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[104]++, temps[temps.length - 1]);
    /* istanbul ignore next */
    cov_37yapc4cs().s[105]++;
    for (let i =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[106]++, 0); i < temps.length - 1; i++) {
      /* istanbul ignore next */
      cov_37yapc4cs().s[107]++;
      if (
      /* istanbul ignore next */
      (cov_37yapc4cs().b[25][0]++, temperature >= temps[i]) &&
      /* istanbul ignore next */
      (cov_37yapc4cs().b[25][1]++, temperature <= temps[i + 1])) {
        /* istanbul ignore next */
        cov_37yapc4cs().b[24][0]++;
        cov_37yapc4cs().s[108]++;
        lowerTemp = temps[i];
        /* istanbul ignore next */
        cov_37yapc4cs().s[109]++;
        upperTemp = temps[i + 1];
        /* istanbul ignore next */
        cov_37yapc4cs().s[110]++;
        break;
      } else
      /* istanbul ignore next */
      {
        cov_37yapc4cs().b[24][1]++;
      }
    }
    // If exact match, return directly
    /* istanbul ignore next */
    cov_37yapc4cs().s[111]++;
    if (tempProps[temperature.toString()]) {
      /* istanbul ignore next */
      cov_37yapc4cs().b[26][0]++;
      cov_37yapc4cs().s[112]++;
      return tempProps[temperature.toString()];
    } else
    /* istanbul ignore next */
    {
      cov_37yapc4cs().b[26][1]++;
    }
    // Interpolate between bounds
    const lowerProps =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[113]++, tempProps[lowerTemp.toString()]);
    const upperProps =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[114]++, tempProps[upperTemp.toString()]);
    /* istanbul ignore next */
    cov_37yapc4cs().s[115]++;
    return {
      density: this.linearInterpolate(temperature, lowerTemp, lowerProps.density, upperTemp, upperProps.density),
      viscosity: this.linearInterpolate(temperature, lowerTemp, lowerProps.viscosity, upperTemp, upperProps.viscosity),
      specific_heat: this.linearInterpolate(temperature, lowerTemp, lowerProps.specific_heat, upperTemp, upperProps.specific_heat),
      thermal_conductivity: this.linearInterpolate(temperature, lowerTemp, lowerProps.thermal_conductivity, upperTemp, upperProps.thermal_conductivity)
    };
  }
  /**
   * Interpolate velocity pressure from table
   */
  static interpolateVelocityPressure(velocity, data) {
    /* istanbul ignore next */
    cov_37yapc4cs().f[10]++;
    const vpTable =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[116]++, data.velocity_pressure_table);
    const velocities =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[117]++, Object.keys(vpTable).map(Number).sort((a, b) => {
      /* istanbul ignore next */
      cov_37yapc4cs().f[11]++;
      cov_37yapc4cs().s[118]++;
      return a - b;
    }));
    // Find bounding velocities
    let lowerVel =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[119]++, velocities[0]);
    let upperVel =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[120]++, velocities[velocities.length - 1]);
    /* istanbul ignore next */
    cov_37yapc4cs().s[121]++;
    for (let i =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[122]++, 0); i < velocities.length - 1; i++) {
      /* istanbul ignore next */
      cov_37yapc4cs().s[123]++;
      if (
      /* istanbul ignore next */
      (cov_37yapc4cs().b[28][0]++, velocity >= velocities[i]) &&
      /* istanbul ignore next */
      (cov_37yapc4cs().b[28][1]++, velocity <= velocities[i + 1])) {
        /* istanbul ignore next */
        cov_37yapc4cs().b[27][0]++;
        cov_37yapc4cs().s[124]++;
        lowerVel = velocities[i];
        /* istanbul ignore next */
        cov_37yapc4cs().s[125]++;
        upperVel = velocities[i + 1];
        /* istanbul ignore next */
        cov_37yapc4cs().s[126]++;
        break;
      } else
      /* istanbul ignore next */
      {
        cov_37yapc4cs().b[27][1]++;
      }
    }
    // If exact match, return directly
    /* istanbul ignore next */
    cov_37yapc4cs().s[127]++;
    if (vpTable[velocity.toString()]) {
      /* istanbul ignore next */
      cov_37yapc4cs().b[29][0]++;
      cov_37yapc4cs().s[128]++;
      return vpTable[velocity.toString()];
    } else
    /* istanbul ignore next */
    {
      cov_37yapc4cs().b[29][1]++;
    }
    // Interpolate between bounds
    const lowerVP =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[129]++, vpTable[lowerVel.toString()]);
    const upperVP =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[130]++, vpTable[upperVel.toString()]);
    /* istanbul ignore next */
    cov_37yapc4cs().s[131]++;
    return this.linearInterpolate(velocity, lowerVel, lowerVP, upperVel, upperVP);
  }
  // Additional helper methods would continue here...
  // (Temperature factor, pressure factor, humidity factor calculations, etc.)
  /**
   * Fallback data methods
   */
  static getFallbackAirProperties() {
    /* istanbul ignore next */
    cov_37yapc4cs().f[12]++;
    cov_37yapc4cs().s[132]++;
    return {
      standard_conditions: {
        temperature: 70,
        density: 0.075,
        viscosity: 1.204e-5
      },
      temperature_properties: {
        "70": {
          density: 0.075,
          viscosity: 1.204e-5,
          specific_heat: 0.240,
          thermal_conductivity: 0.0148
        }
      }
    };
  }
  static getFallbackVelocityPressure() {
    /* istanbul ignore next */
    cov_37yapc4cs().f[13]++;
    cov_37yapc4cs().s[133]++;
    return {
      velocity_pressure_table: {
        "1000": 0.0623,
        "1500": 0.1406,
        "2000": 0.2501
      }
    };
  }
  static getFallbackDuctRoughness() {
    /* istanbul ignore next */
    cov_37yapc4cs().f[14]++;
    cov_37yapc4cs().s[134]++;
    return {
      materials: {
        galvanized_steel: {
          base_roughness: 0.0003,
          aging_factors: {
            new: 1.0
          },
          surface_conditions: {
            good: 1.0
          }
        }
      }
    };
  }
  // Placeholder methods for additional calculations
  static validateAirConditions(conditions, warnings) {
    /* istanbul ignore next */
    cov_37yapc4cs().f[15]++;
    cov_37yapc4cs().s[135]++;
    if (
    /* istanbul ignore next */
    (cov_37yapc4cs().b[31][0]++, conditions.temperature < 32) ||
    /* istanbul ignore next */
    (cov_37yapc4cs().b[31][1]++, conditions.temperature > 200)) {
      /* istanbul ignore next */
      cov_37yapc4cs().b[30][0]++;
      cov_37yapc4cs().s[136]++;
      warnings.push(`Temperature ${conditions.temperature}°F is outside normal HVAC range (32-200°F)`);
    } else
    /* istanbul ignore next */
    {
      cov_37yapc4cs().b[30][1]++;
    }
  }
  static calculateTemperatureFactor(temperature) {
    /* istanbul ignore next */
    cov_37yapc4cs().f[16]++;
    cov_37yapc4cs().s[137]++;
    return Math.pow((temperature + 459.67) / (70 + 459.67), 0.7);
  }
  static calculatePressureFactor(pressure, altitude) {
    /* istanbul ignore next */
    cov_37yapc4cs().f[17]++;
    cov_37yapc4cs().s[138]++;
    if (altitude !== undefined) {
      /* istanbul ignore next */
      cov_37yapc4cs().b[32][0]++;
      cov_37yapc4cs().s[139]++;
      return Math.pow(1 - altitude * 6.87535e-6, 5.2561);
    } else
    /* istanbul ignore next */
    {
      cov_37yapc4cs().b[32][1]++;
    }
    cov_37yapc4cs().s[140]++;
    if (pressure !== undefined) {
      /* istanbul ignore next */
      cov_37yapc4cs().b[33][0]++;
      cov_37yapc4cs().s[141]++;
      return pressure / 29.92;
    } else
    /* istanbul ignore next */
    {
      cov_37yapc4cs().b[33][1]++;
    }
    cov_37yapc4cs().s[142]++;
    return 1.0;
  }
  static calculateHumidityFactor(humidity) {
    /* istanbul ignore next */
    cov_37yapc4cs().f[18]++;
    cov_37yapc4cs().s[143]++;
    return 1.0 - humidity / 100 * 0.016;
  }
  static getTemperatureCorrection(temperature) {
    /* istanbul ignore next */
    cov_37yapc4cs().f[19]++;
    const data =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[144]++, this.loadVelocityPressureData());
    const tempCorrections =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[145]++, data.temperature_corrections);
    // Find closest temperature or interpolate
    const temps =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[146]++, Object.keys(tempCorrections).map(Number).sort((a, b) => {
      /* istanbul ignore next */
      cov_37yapc4cs().f[20]++;
      cov_37yapc4cs().s[147]++;
      return a - b;
    }));
    /* istanbul ignore next */
    cov_37yapc4cs().s[148]++;
    for (const temp of temps) {
      /* istanbul ignore next */
      cov_37yapc4cs().s[149]++;
      if (tempCorrections[temp.toString()]) {
        /* istanbul ignore next */
        cov_37yapc4cs().b[34][0]++;
        cov_37yapc4cs().s[150]++;
        if (Math.abs(temperature - temp) < 5) {
          /* istanbul ignore next */
          cov_37yapc4cs().b[35][0]++;
          cov_37yapc4cs().s[151]++;
          return tempCorrections[temp.toString()].correction_factor;
        } else
        /* istanbul ignore next */
        {
          cov_37yapc4cs().b[35][1]++;
        }
      } else
      /* istanbul ignore next */
      {
        cov_37yapc4cs().b[34][1]++;
      }
    }
    /* istanbul ignore next */
    cov_37yapc4cs().s[152]++;
    return 1.0; // Default if not found
  }
  static getAltitudeCorrection(altitude) {
    /* istanbul ignore next */
    cov_37yapc4cs().f[21]++;
    const data =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[153]++, this.loadVelocityPressureData());
    const altCorrections =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[154]++, data.altitude_corrections);
    // Find closest altitude or interpolate
    const altitudes =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[155]++, Object.keys(altCorrections).map(Number).sort((a, b) => {
      /* istanbul ignore next */
      cov_37yapc4cs().f[22]++;
      cov_37yapc4cs().s[156]++;
      return a - b;
    }));
    /* istanbul ignore next */
    cov_37yapc4cs().s[157]++;
    for (const alt of altitudes) {
      /* istanbul ignore next */
      cov_37yapc4cs().s[158]++;
      if (altCorrections[alt.toString()]) {
        /* istanbul ignore next */
        cov_37yapc4cs().b[36][0]++;
        cov_37yapc4cs().s[159]++;
        if (Math.abs(altitude - alt) < 500) {
          /* istanbul ignore next */
          cov_37yapc4cs().b[37][0]++;
          cov_37yapc4cs().s[160]++;
          return altCorrections[alt.toString()].correction_factor;
        } else
        /* istanbul ignore next */
        {
          cov_37yapc4cs().b[37][1]++;
        }
      } else
      /* istanbul ignore next */
      {
        cov_37yapc4cs().b[36][1]++;
      }
    }
    /* istanbul ignore next */
    cov_37yapc4cs().s[161]++;
    return 1.0; // Default if not found
  }
  static getHumidityCorrection(humidity) {
    /* istanbul ignore next */
    cov_37yapc4cs().f[23]++;
    const data =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[162]++, this.loadVelocityPressureData());
    const humidityCorrections =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[163]++, data.humidity_corrections);
    // Find closest humidity or interpolate
    const humidities =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[164]++, Object.keys(humidityCorrections).map(Number).sort((a, b) => {
      /* istanbul ignore next */
      cov_37yapc4cs().f[24]++;
      cov_37yapc4cs().s[165]++;
      return a - b;
    }));
    /* istanbul ignore next */
    cov_37yapc4cs().s[166]++;
    for (const hum of humidities) {
      /* istanbul ignore next */
      cov_37yapc4cs().s[167]++;
      if (humidityCorrections[hum.toString()]) {
        /* istanbul ignore next */
        cov_37yapc4cs().b[38][0]++;
        cov_37yapc4cs().s[168]++;
        if (Math.abs(humidity - hum) < 5) {
          /* istanbul ignore next */
          cov_37yapc4cs().b[39][0]++;
          cov_37yapc4cs().s[169]++;
          return humidityCorrections[hum.toString()].correction_factor;
        } else
        /* istanbul ignore next */
        {
          cov_37yapc4cs().b[39][1]++;
        }
      } else
      /* istanbul ignore next */
      {
        cov_37yapc4cs().b[38][1]++;
      }
    }
    /* istanbul ignore next */
    cov_37yapc4cs().s[170]++;
    return 1.0; // Default if not found
  }
  static getAgingFactor(agingFactors, age) {
    /* istanbul ignore next */
    cov_37yapc4cs().f[25]++;
    const ages =
    /* istanbul ignore next */
    (cov_37yapc4cs().s[171]++, Object.keys(agingFactors));
    // Find appropriate aging factor
    /* istanbul ignore next */
    cov_37yapc4cs().s[172]++;
    if (age <= 5) {
      /* istanbul ignore next */
      cov_37yapc4cs().b[40][0]++;
      cov_37yapc4cs().s[173]++;
      return /* istanbul ignore next */(cov_37yapc4cs().b[41][0]++, agingFactors.new) ||
      /* istanbul ignore next */
      (cov_37yapc4cs().b[41][1]++, 1.0);
    } else
    /* istanbul ignore next */
    {
      cov_37yapc4cs().b[40][1]++;
    }
    cov_37yapc4cs().s[174]++;
    if (age <= 10) {
      /* istanbul ignore next */
      cov_37yapc4cs().b[42][0]++;
      cov_37yapc4cs().s[175]++;
      return /* istanbul ignore next */(cov_37yapc4cs().b[43][0]++, agingFactors['5_years']) ||
      /* istanbul ignore next */
      (cov_37yapc4cs().b[43][1]++, 1.2);
    } else
    /* istanbul ignore next */
    {
      cov_37yapc4cs().b[42][1]++;
    }
    cov_37yapc4cs().s[176]++;
    if (age <= 15) {
      /* istanbul ignore next */
      cov_37yapc4cs().b[44][0]++;
      cov_37yapc4cs().s[177]++;
      return /* istanbul ignore next */(cov_37yapc4cs().b[45][0]++, agingFactors['10_years']) ||
      /* istanbul ignore next */
      (cov_37yapc4cs().b[45][1]++, 1.5);
    } else
    /* istanbul ignore next */
    {
      cov_37yapc4cs().b[44][1]++;
    }
    cov_37yapc4cs().s[178]++;
    if (age <= 20) {
      /* istanbul ignore next */
      cov_37yapc4cs().b[46][0]++;
      cov_37yapc4cs().s[179]++;
      return /* istanbul ignore next */(cov_37yapc4cs().b[47][0]++, agingFactors['15_years']) ||
      /* istanbul ignore next */
      (cov_37yapc4cs().b[47][1]++, 2.0);
    } else
    /* istanbul ignore next */
    {
      cov_37yapc4cs().b[46][1]++;
    }
    cov_37yapc4cs().s[180]++;
    return /* istanbul ignore next */(cov_37yapc4cs().b[48][0]++, agingFactors['20_years']) ||
    /* istanbul ignore next */
    (cov_37yapc4cs().b[48][1]++, 2.5);
  }
  static addConditionWarnings(conditions, warnings, notes) {
    /* istanbul ignore next */
    cov_37yapc4cs().f[26]++;
    cov_37yapc4cs().s[181]++;
    if (
    /* istanbul ignore next */
    (cov_37yapc4cs().b[50][0]++, conditions.altitude) &&
    /* istanbul ignore next */
    (cov_37yapc4cs().b[50][1]++, conditions.altitude > 5000)) {
      /* istanbul ignore next */
      cov_37yapc4cs().b[49][0]++;
      cov_37yapc4cs().s[182]++;
      warnings.push(`High altitude (${conditions.altitude} ft) affects air density significantly`);
    } else
    /* istanbul ignore next */
    {
      cov_37yapc4cs().b[49][1]++;
    }
    cov_37yapc4cs().s[183]++;
    if (
    /* istanbul ignore next */
    (cov_37yapc4cs().b[52][0]++, conditions.humidity) &&
    /* istanbul ignore next */
    (cov_37yapc4cs().b[52][1]++, conditions.humidity > 80)) {
      /* istanbul ignore next */
      cov_37yapc4cs().b[51][0]++;
      cov_37yapc4cs().s[184]++;
      warnings.push(`High humidity (${conditions.humidity}% RH) may cause condensation in ducts`);
    } else
    /* istanbul ignore next */
    {
      cov_37yapc4cs().b[51][1]++;
    }
    cov_37yapc4cs().s[185]++;
    if (conditions.temperature > 180) {
      /* istanbul ignore next */
      cov_37yapc4cs().b[53][0]++;
      cov_37yapc4cs().s[186]++;
      warnings.push(`High temperature (${conditions.temperature}°F) requires special material considerations`);
    } else
    /* istanbul ignore next */
    {
      cov_37yapc4cs().b[53][1]++;
    }
  }
}
/* istanbul ignore next */
cov_37yapc4cs().s[187]++;
exports.AirPropertiesCalculator = AirPropertiesCalculator;
/* istanbul ignore next */
cov_37yapc4cs().s[188]++;
AirPropertiesCalculator.airPropertiesData = null;
/* istanbul ignore next */
cov_37yapc4cs().s[189]++;
AirPropertiesCalculator.velocityPressureData = null;
/* istanbul ignore next */
cov_37yapc4cs().s[190]++;
AirPropertiesCalculator.ductRoughnessData = null;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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