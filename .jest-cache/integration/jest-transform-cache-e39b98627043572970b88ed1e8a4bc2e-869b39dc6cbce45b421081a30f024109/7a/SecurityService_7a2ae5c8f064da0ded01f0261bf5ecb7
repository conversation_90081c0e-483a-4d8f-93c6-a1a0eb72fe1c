94e8c715c9dd90df78d3ff3271987338
"use strict";

/**
 * Advanced Security Service for SizeWise Suite
 * Handles authentication, MFA, RBAC, and security monitoring
 */
/* istanbul ignore next */
function cov_1fcfbgkfrs() {
  var path = "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\frontend\\lib\\services\\SecurityService.ts";
  var hash = "db3ebc54f72540402c37378ed1f16a06bd186a6b";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\frontend\\lib\\services\\SecurityService.ts",
    statementMap: {
      "0": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 6,
          column: 62
        }
      },
      "1": {
        start: {
          line: 7,
          column: 0
        },
        end: {
          line: 7,
          column: 59
        }
      },
      "2": {
        start: {
          line: 8,
          column: 14
        },
        end: {
          line: 8,
          column: 28
        }
      },
      "3": {
        start: {
          line: 10,
          column: 23
        },
        end: {
          line: 15,
          column: 89
        }
      },
      "4": {
        start: {
          line: 16,
          column: 23
        },
        end: {
          line: 18,
          column: 60
        }
      },
      "5": {
        start: {
          line: 21,
          column: 8
        },
        end: {
          line: 21,
          column: 32
        }
      },
      "6": {
        start: {
          line: 22,
          column: 8
        },
        end: {
          line: 22,
          column: 35
        }
      },
      "7": {
        start: {
          line: 23,
          column: 8
        },
        end: {
          line: 23,
          column: 31
        }
      },
      "8": {
        start: {
          line: 24,
          column: 8
        },
        end: {
          line: 35,
          column: 10
        }
      },
      "9": {
        start: {
          line: 37,
          column: 8
        },
        end: {
          line: 37,
          column: 43
        }
      },
      "10": {
        start: {
          line: 43,
          column: 8
        },
        end: {
          line: 88,
          column: 9
        }
      },
      "11": {
        start: {
          line: 44,
          column: 29
        },
        end: {
          line: 56,
          column: 14
        }
      },
      "12": {
        start: {
          line: 57,
          column: 27
        },
        end: {
          line: 57,
          column: 48
        }
      },
      "13": {
        start: {
          line: 58,
          column: 12
        },
        end: {
          line: 80,
          column: 13
        }
      },
      "14": {
        start: {
          line: 59,
          column: 16
        },
        end: {
          line: 59,
          column: 47
        }
      },
      "15": {
        start: {
          line: 60,
          column: 16
        },
        end: {
          line: 60,
          column: 43
        }
      },
      "16": {
        start: {
          line: 62,
          column: 16
        },
        end: {
          line: 68,
          column: 19
        }
      },
      "17": {
        start: {
          line: 69,
          column: 16
        },
        end: {
          line: 69,
          column: 30
        }
      },
      "18": {
        start: {
          line: 73,
          column: 16
        },
        end: {
          line: 78,
          column: 19
        }
      },
      "19": {
        start: {
          line: 79,
          column: 16
        },
        end: {
          line: 79,
          column: 30
        }
      },
      "20": {
        start: {
          line: 83,
          column: 12
        },
        end: {
          line: 83,
          column: 58
        }
      },
      "21": {
        start: {
          line: 84,
          column: 12
        },
        end: {
          line: 87,
          column: 14
        }
      },
      "22": {
        start: {
          line: 94,
          column: 8
        },
        end: {
          line: 117,
          column: 9
        }
      },
      "23": {
        start: {
          line: 95,
          column: 29
        },
        end: {
          line: 101,
          column: 14
        }
      },
      "24": {
        start: {
          line: 102,
          column: 27
        },
        end: {
          line: 102,
          column: 48
        }
      },
      "25": {
        start: {
          line: 103,
          column: 12
        },
        end: {
          line: 111,
          column: 13
        }
      },
      "26": {
        start: {
          line: 104,
          column: 16
        },
        end: {
          line: 110,
          column: 19
        }
      },
      "27": {
        start: {
          line: 112,
          column: 12
        },
        end: {
          line: 112,
          column: 26
        }
      },
      "28": {
        start: {
          line: 115,
          column: 12
        },
        end: {
          line: 115,
          column: 53
        }
      },
      "29": {
        start: {
          line: 116,
          column: 12
        },
        end: {
          line: 116,
          column: 51
        }
      },
      "30": {
        start: {
          line: 123,
          column: 8
        },
        end: {
          line: 146,
          column: 9
        }
      },
      "31": {
        start: {
          line: 124,
          column: 12
        },
        end: {
          line: 124,
          column: 40
        }
      },
      "32": {
        start: {
          line: 125,
          column: 29
        },
        end: {
          line: 132,
          column: 14
        }
      },
      "33": {
        start: {
          line: 133,
          column: 27
        },
        end: {
          line: 133,
          column: 48
        }
      },
      "34": {
        start: {
          line: 134,
          column: 12
        },
        end: {
          line: 140,
          column: 15
        }
      },
      "35": {
        start: {
          line: 141,
          column: 12
        },
        end: {
          line: 141,
          column: 34
        }
      },
      "36": {
        start: {
          line: 144,
          column: 12
        },
        end: {
          line: 144,
          column: 60
        }
      },
      "37": {
        start: {
          line: 145,
          column: 12
        },
        end: {
          line: 145,
          column: 25
        }
      },
      "38": {
        start: {
          line: 152,
          column: 8
        },
        end: {
          line: 153,
          column: 25
        }
      },
      "39": {
        start: {
          line: 153,
          column: 12
        },
        end: {
          line: 153,
          column: 25
        }
      },
      "40": {
        start: {
          line: 154,
          column: 8
        },
        end: {
          line: 154,
          column: 65
        }
      },
      "41": {
        start: {
          line: 160,
          column: 8
        },
        end: {
          line: 161,
          column: 25
        }
      },
      "42": {
        start: {
          line: 161,
          column: 12
        },
        end: {
          line: 161,
          column: 25
        }
      },
      "43": {
        start: {
          line: 162,
          column: 26
        },
        end: {
          line: 162,
          column: 64
        }
      },
      "44": {
        start: {
          line: 163,
          column: 8
        },
        end: {
          line: 163,
          column: 77
        }
      },
      "45": {
        start: {
          line: 163,
          column: 38
        },
        end: {
          line: 163,
          column: 75
        }
      },
      "46": {
        start: {
          line: 169,
          column: 8
        },
        end: {
          line: 181,
          column: 9
        }
      },
      "47": {
        start: {
          line: 170,
          column: 12
        },
        end: {
          line: 170,
          column: 43
        }
      },
      "48": {
        start: {
          line: 171,
          column: 12
        },
        end: {
          line: 171,
          column: 47
        }
      },
      "49": {
        start: {
          line: 174,
          column: 12
        },
        end: {
          line: 179,
          column: 13
        }
      },
      "50": {
        start: {
          line: 175,
          column: 16
        },
        end: {
          line: 178,
          column: 18
        }
      },
      "51": {
        start: {
          line: 177,
          column: 50
        },
        end: {
          line: 177,
          column: 59
        }
      },
      "52": {
        start: {
          line: 180,
          column: 12
        },
        end: {
          line: 180,
          column: 76
        }
      },
      "53": {
        start: {
          line: 187,
          column: 8
        },
        end: {
          line: 216,
          column: 9
        }
      },
      "54": {
        start: {
          line: 188,
          column: 31
        },
        end: {
          line: 188,
          column: 65
        }
      },
      "55": {
        start: {
          line: 189,
          column: 12
        },
        end: {
          line: 191,
          column: 13
        }
      },
      "56": {
        start: {
          line: 190,
          column: 16
        },
        end: {
          line: 190,
          column: 62
        }
      },
      "57": {
        start: {
          line: 192,
          column: 29
        },
        end: {
          line: 202,
          column: 14
        }
      },
      "58": {
        start: {
          line: 203,
          column: 27
        },
        end: {
          line: 203,
          column: 48
        }
      },
      "59": {
        start: {
          line: 204,
          column: 12
        },
        end: {
          line: 210,
          column: 15
        }
      },
      "60": {
        start: {
          line: 211,
          column: 12
        },
        end: {
          line: 211,
          column: 34
        }
      },
      "61": {
        start: {
          line: 214,
          column: 12
        },
        end: {
          line: 214,
          column: 59
        }
      },
      "62": {
        start: {
          line: 215,
          column: 12
        },
        end: {
          line: 215,
          column: 25
        }
      },
      "63": {
        start: {
          line: 222,
          column: 8
        },
        end: {
          line: 241,
          column: 9
        }
      },
      "64": {
        start: {
          line: 223,
          column: 12
        },
        end: {
          line: 228,
          column: 15
        }
      },
      "65": {
        start: {
          line: 229,
          column: 12
        },
        end: {
          line: 235,
          column: 15
        }
      },
      "66": {
        start: {
          line: 236,
          column: 12
        },
        end: {
          line: 236,
          column: 32
        }
      },
      "67": {
        start: {
          line: 239,
          column: 12
        },
        end: {
          line: 239,
          column: 50
        }
      },
      "68": {
        start: {
          line: 240,
          column: 12
        },
        end: {
          line: 240,
          column: 32
        }
      },
      "69": {
        start: {
          line: 247,
          column: 8
        },
        end: {
          line: 247,
          column: 32
        }
      },
      "70": {
        start: {
          line: 253,
          column: 8
        },
        end: {
          line: 253,
          column: 41
        }
      },
      "71": {
        start: {
          line: 259,
          column: 8
        },
        end: {
          line: 270,
          column: 9
        }
      },
      "72": {
        start: {
          line: 260,
          column: 29
        },
        end: {
          line: 264,
          column: 14
        }
      },
      "73": {
        start: {
          line: 265,
          column: 12
        },
        end: {
          line: 265,
          column: 41
        }
      },
      "74": {
        start: {
          line: 268,
          column: 12
        },
        end: {
          line: 268,
          column: 69
        }
      },
      "75": {
        start: {
          line: 269,
          column: 12
        },
        end: {
          line: 269,
          column: 22
        }
      },
      "76": {
        start: {
          line: 276,
          column: 8
        },
        end: {
          line: 296,
          column: 9
        }
      },
      "77": {
        start: {
          line: 277,
          column: 30
        },
        end: {
          line: 284,
          column: 13
        }
      },
      "78": {
        start: {
          line: 285,
          column: 12
        },
        end: {
          line: 292,
          column: 15
        }
      },
      "79": {
        start: {
          line: 295,
          column: 12
        },
        end: {
          line: 295,
          column: 66
        }
      },
      "80": {
        start: {
          line: 303,
          column: 23
        },
        end: {
          line: 303,
          column: 85
        }
      },
      "81": {
        start: {
          line: 304,
          column: 8
        },
        end: {
          line: 308,
          column: 11
        }
      },
      "82": {
        start: {
          line: 305,
          column: 12
        },
        end: {
          line: 307,
          column: 21
        }
      },
      "83": {
        start: {
          line: 306,
          column: 16
        },
        end: {
          line: 306,
          column: 43
        }
      },
      "84": {
        start: {
          line: 310,
          column: 8
        },
        end: {
          line: 317,
          column: 11
        }
      },
      "85": {
        start: {
          line: 311,
          column: 12
        },
        end: {
          line: 316,
          column: 13
        }
      },
      "86": {
        start: {
          line: 312,
          column: 16
        },
        end: {
          line: 312,
          column: 43
        }
      },
      "87": {
        start: {
          line: 315,
          column: 16
        },
        end: {
          line: 315,
          column: 44
        }
      },
      "88": {
        start: {
          line: 323,
          column: 8
        },
        end: {
          line: 323,
          column: 35
        }
      },
      "89": {
        start: {
          line: 324,
          column: 8
        },
        end: {
          line: 326,
          column: 66
        }
      },
      "90": {
        start: {
          line: 325,
          column: 12
        },
        end: {
          line: 325,
          column: 40
        }
      },
      "91": {
        start: {
          line: 332,
          column: 8
        },
        end: {
          line: 334,
          column: 9
        }
      },
      "92": {
        start: {
          line: 333,
          column: 12
        },
        end: {
          line: 333,
          column: 39
        }
      },
      "93": {
        start: {
          line: 340,
          column: 8
        },
        end: {
          line: 343,
          column: 9
        }
      },
      "94": {
        start: {
          line: 341,
          column: 12
        },
        end: {
          line: 341,
          column: 46
        }
      },
      "95": {
        start: {
          line: 342,
          column: 12
        },
        end: {
          line: 342,
          column: 39
        }
      },
      "96": {
        start: {
          line: 349,
          column: 8
        },
        end: {
          line: 349,
          column: 35
        }
      },
      "97": {
        start: {
          line: 355,
          column: 8
        },
        end: {
          line: 357,
          column: 9
        }
      },
      "98": {
        start: {
          line: 356,
          column: 12
        },
        end: {
          line: 356,
          column: 39
        }
      },
      "99": {
        start: {
          line: 363,
          column: 8
        },
        end: {
          line: 368,
          column: 11
        }
      },
      "100": {
        start: {
          line: 369,
          column: 8
        },
        end: {
          line: 369,
          column: 28
        }
      },
      "101": {
        start: {
          line: 371,
          column: 8
        },
        end: {
          line: 371,
          column: 63
        }
      },
      "102": {
        start: {
          line: 377,
          column: 8
        },
        end: {
          line: 377,
          column: 32
        }
      },
      "103": {
        start: {
          line: 378,
          column: 8
        },
        end: {
          line: 378,
          column: 35
        }
      },
      "104": {
        start: {
          line: 379,
          column: 8
        },
        end: {
          line: 379,
          column: 46
        }
      },
      "105": {
        start: {
          line: 380,
          column: 8
        },
        end: {
          line: 380,
          column: 31
        }
      },
      "106": {
        start: {
          line: 386,
          column: 8
        },
        end: {
          line: 386,
          column: 50
        }
      },
      "107": {
        start: {
          line: 392,
          column: 8
        },
        end: {
          line: 399,
          column: 9
        }
      },
      "108": {
        start: {
          line: 393,
          column: 29
        },
        end: {
          line: 393,
          column: 77
        }
      },
      "109": {
        start: {
          line: 394,
          column: 25
        },
        end: {
          line: 394,
          column: 46
        }
      },
      "110": {
        start: {
          line: 395,
          column: 12
        },
        end: {
          line: 395,
          column: 27
        }
      },
      "111": {
        start: {
          line: 398,
          column: 12
        },
        end: {
          line: 398,
          column: 29
        }
      },
      "112": {
        start: {
          line: 405,
          column: 8
        },
        end: {
          line: 405,
          column: 78
        }
      },
      "113": {
        start: {
          line: 408,
          column: 0
        },
        end: {
          line: 408,
          column: 42
        }
      },
      "114": {
        start: {
          line: 410,
          column: 0
        },
        end: {
          line: 410,
          column: 48
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 20,
            column: 4
          },
          end: {
            line: 20,
            column: 5
          }
        },
        loc: {
          start: {
            line: 20,
            column: 34
          },
          end: {
            line: 38,
            column: 5
          }
        },
        line: 20
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 42,
            column: 4
          },
          end: {
            line: 42,
            column: 5
          }
        },
        loc: {
          start: {
            line: 42,
            column: 50
          },
          end: {
            line: 89,
            column: 5
          }
        },
        line: 42
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 93,
            column: 4
          },
          end: {
            line: 93,
            column: 5
          }
        },
        loc: {
          start: {
            line: 93,
            column: 21
          },
          end: {
            line: 118,
            column: 5
          }
        },
        line: 93
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 122,
            column: 4
          },
          end: {
            line: 122,
            column: 5
          }
        },
        loc: {
          start: {
            line: 122,
            column: 32
          },
          end: {
            line: 147,
            column: 5
          }
        },
        line: 122
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 151,
            column: 4
          },
          end: {
            line: 151,
            column: 5
          }
        },
        loc: {
          start: {
            line: 151,
            column: 30
          },
          end: {
            line: 155,
            column: 5
          }
        },
        line: 151
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 159,
            column: 4
          },
          end: {
            line: 159,
            column: 5
          }
        },
        loc: {
          start: {
            line: 159,
            column: 19
          },
          end: {
            line: 164,
            column: 5
          }
        },
        line: 159
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 163,
            column: 30
          },
          end: {
            line: 163,
            column: 31
          }
        },
        loc: {
          start: {
            line: 163,
            column: 38
          },
          end: {
            line: 163,
            column: 75
          }
        },
        line: 163
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 168,
            column: 4
          },
          end: {
            line: 168,
            column: 5
          }
        },
        loc: {
          start: {
            line: 168,
            column: 31
          },
          end: {
            line: 182,
            column: 5
          }
        },
        line: 168
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 177,
            column: 45
          },
          end: {
            line: 177,
            column: 46
          }
        },
        loc: {
          start: {
            line: 177,
            column: 50
          },
          end: {
            line: 177,
            column: 59
          }
        },
        line: 177
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 186,
            column: 4
          },
          end: {
            line: 186,
            column: 5
          }
        },
        loc: {
          start: {
            line: 186,
            column: 55
          },
          end: {
            line: 217,
            column: 5
          }
        },
        line: 186
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 221,
            column: 4
          },
          end: {
            line: 221,
            column: 5
          }
        },
        loc: {
          start: {
            line: 221,
            column: 19
          },
          end: {
            line: 242,
            column: 5
          }
        },
        line: 221
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 246,
            column: 4
          },
          end: {
            line: 246,
            column: 5
          }
        },
        loc: {
          start: {
            line: 246,
            column: 21
          },
          end: {
            line: 248,
            column: 5
          }
        },
        line: 246
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 252,
            column: 4
          },
          end: {
            line: 252,
            column: 5
          }
        },
        loc: {
          start: {
            line: 252,
            column: 22
          },
          end: {
            line: 254,
            column: 5
          }
        },
        line: 252
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 258,
            column: 4
          },
          end: {
            line: 258,
            column: 5
          }
        },
        loc: {
          start: {
            line: 258,
            column: 40
          },
          end: {
            line: 271,
            column: 5
          }
        },
        line: 258
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 275,
            column: 4
          },
          end: {
            line: 275,
            column: 5
          }
        },
        loc: {
          start: {
            line: 275,
            column: 34
          },
          end: {
            line: 297,
            column: 5
          }
        },
        line: 275
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 301,
            column: 4
          },
          end: {
            line: 301,
            column: 5
          }
        },
        loc: {
          start: {
            line: 301,
            column: 34
          },
          end: {
            line: 318,
            column: 5
          }
        },
        line: 301
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 304,
            column: 23
          },
          end: {
            line: 304,
            column: 24
          }
        },
        loc: {
          start: {
            line: 304,
            column: 32
          },
          end: {
            line: 308,
            column: 9
          }
        },
        line: 304
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 305,
            column: 45
          },
          end: {
            line: 305,
            column: 46
          }
        },
        loc: {
          start: {
            line: 305,
            column: 51
          },
          end: {
            line: 307,
            column: 13
          }
        },
        line: 305
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 310,
            column: 54
          },
          end: {
            line: 310,
            column: 55
          }
        },
        loc: {
          start: {
            line: 310,
            column: 60
          },
          end: {
            line: 317,
            column: 9
          }
        },
        line: 310
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 322,
            column: 4
          },
          end: {
            line: 322,
            column: 5
          }
        },
        loc: {
          start: {
            line: 322,
            column: 26
          },
          end: {
            line: 327,
            column: 5
          }
        },
        line: 322
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 324,
            column: 41
          },
          end: {
            line: 324,
            column: 42
          }
        },
        loc: {
          start: {
            line: 324,
            column: 47
          },
          end: {
            line: 326,
            column: 9
          }
        },
        line: 324
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 331,
            column: 4
          },
          end: {
            line: 331,
            column: 5
          }
        },
        loc: {
          start: {
            line: 331,
            column: 26
          },
          end: {
            line: 335,
            column: 5
          }
        },
        line: 331
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 339,
            column: 4
          },
          end: {
            line: 339,
            column: 5
          }
        },
        loc: {
          start: {
            line: 339,
            column: 26
          },
          end: {
            line: 344,
            column: 5
          }
        },
        line: 339
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 348,
            column: 4
          },
          end: {
            line: 348,
            column: 5
          }
        },
        loc: {
          start: {
            line: 348,
            column: 26
          },
          end: {
            line: 350,
            column: 5
          }
        },
        line: 348
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 354,
            column: 4
          },
          end: {
            line: 354,
            column: 5
          }
        },
        loc: {
          start: {
            line: 354,
            column: 27
          },
          end: {
            line: 358,
            column: 5
          }
        },
        line: 354
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 362,
            column: 4
          },
          end: {
            line: 362,
            column: 5
          }
        },
        loc: {
          start: {
            line: 362,
            column: 33
          },
          end: {
            line: 372,
            column: 5
          }
        },
        line: 362
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 376,
            column: 4
          },
          end: {
            line: 376,
            column: 5
          }
        },
        loc: {
          start: {
            line: 376,
            column: 19
          },
          end: {
            line: 381,
            column: 5
          }
        },
        line: 376
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 385,
            column: 4
          },
          end: {
            line: 385,
            column: 5
          }
        },
        loc: {
          start: {
            line: 385,
            column: 15
          },
          end: {
            line: 387,
            column: 5
          }
        },
        line: 385
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 391,
            column: 4
          },
          end: {
            line: 391,
            column: 5
          }
        },
        loc: {
          start: {
            line: 391,
            column: 24
          },
          end: {
            line: 400,
            column: 5
          }
        },
        line: 391
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 404,
            column: 4
          },
          end: {
            line: 404,
            column: 5
          }
        },
        loc: {
          start: {
            line: 404,
            column: 22
          },
          end: {
            line: 406,
            column: 5
          }
        },
        line: 404
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 20,
            column: 16
          },
          end: {
            line: 20,
            column: 32
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 20,
            column: 26
          },
          end: {
            line: 20,
            column: 32
          }
        }],
        line: 20
      },
      "1": {
        loc: {
          start: {
            line: 58,
            column: 12
          },
          end: {
            line: 80,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 58,
            column: 12
          },
          end: {
            line: 80,
            column: 13
          }
        }, {
          start: {
            line: 71,
            column: 17
          },
          end: {
            line: 80,
            column: 13
          }
        }],
        line: 58
      },
      "2": {
        loc: {
          start: {
            line: 103,
            column: 12
          },
          end: {
            line: 111,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 103,
            column: 12
          },
          end: {
            line: 111,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 103
      },
      "3": {
        loc: {
          start: {
            line: 139,
            column: 27
          },
          end: {
            line: 139,
            column: 60
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 139,
            column: 44
          },
          end: {
            line: 139,
            column: 49
          }
        }, {
          start: {
            line: 139,
            column: 52
          },
          end: {
            line: 139,
            column: 60
          }
        }],
        line: 139
      },
      "4": {
        loc: {
          start: {
            line: 152,
            column: 8
          },
          end: {
            line: 153,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 152,
            column: 8
          },
          end: {
            line: 153,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 152
      },
      "5": {
        loc: {
          start: {
            line: 160,
            column: 8
          },
          end: {
            line: 161,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 160,
            column: 8
          },
          end: {
            line: 161,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 160
      },
      "6": {
        loc: {
          start: {
            line: 162,
            column: 26
          },
          end: {
            line: 162,
            column: 64
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 162,
            column: 49
          },
          end: {
            line: 162,
            column: 54
          }
        }, {
          start: {
            line: 162,
            column: 57
          },
          end: {
            line: 162,
            column: 64
          }
        }],
        line: 162
      },
      "7": {
        loc: {
          start: {
            line: 174,
            column: 12
          },
          end: {
            line: 179,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 174,
            column: 12
          },
          end: {
            line: 179,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 174
      },
      "8": {
        loc: {
          start: {
            line: 189,
            column: 12
          },
          end: {
            line: 191,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 189,
            column: 12
          },
          end: {
            line: 191,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 189
      },
      "9": {
        loc: {
          start: {
            line: 258,
            column: 28
          },
          end: {
            line: 258,
            column: 38
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 258,
            column: 36
          },
          end: {
            line: 258,
            column: 38
          }
        }],
        line: 258
      },
      "10": {
        loc: {
          start: {
            line: 279,
            column: 24
          },
          end: {
            line: 279,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 279,
            column: 24
          },
          end: {
            line: 279,
            column: 44
          }
        }, {
          start: {
            line: 279,
            column: 48
          },
          end: {
            line: 279,
            column: 59
          }
        }],
        line: 279
      },
      "11": {
        loc: {
          start: {
            line: 311,
            column: 12
          },
          end: {
            line: 316,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 311,
            column: 12
          },
          end: {
            line: 316,
            column: 13
          }
        }, {
          start: {
            line: 314,
            column: 17
          },
          end: {
            line: 316,
            column: 13
          }
        }],
        line: 311
      },
      "12": {
        loc: {
          start: {
            line: 332,
            column: 8
          },
          end: {
            line: 334,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 332,
            column: 8
          },
          end: {
            line: 334,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 332
      },
      "13": {
        loc: {
          start: {
            line: 340,
            column: 8
          },
          end: {
            line: 343,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 340,
            column: 8
          },
          end: {
            line: 343,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 340
      },
      "14": {
        loc: {
          start: {
            line: 355,
            column: 8
          },
          end: {
            line: 357,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 355,
            column: 8
          },
          end: {
            line: 357,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 355
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0
    },
    b: {
      "0": [0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\frontend\\lib\\services\\SecurityService.ts",
      mappings: ";AAAA;;;GAGG;;;AAEH,6BAAwB;AAsDxB,qBAAqB;AACrB,MAAM,cAAc,GAAG,OAAC,CAAC,MAAM,EAAE;KAC9B,GAAG,CAAC,EAAE,EAAE,yCAAyC,CAAC;KAClD,KAAK,CAAC,OAAO,EAAE,yCAAyC,CAAC;KACzD,KAAK,CAAC,OAAO,EAAE,yCAAyC,CAAC;KACzD,KAAK,CAAC,OAAO,EAAE,+BAA+B,CAAC;KAC/C,KAAK,CAAC,iCAAiC,EAAE,0CAA0C,CAAC,CAAC;AAExF,MAAM,cAAc,GAAG,OAAC,CAAC,MAAM,EAAE;KAC9B,MAAM,CAAC,CAAC,EAAE,4BAA4B,CAAC;KACvC,KAAK,CAAC,SAAS,EAAE,qCAAqC,CAAC,CAAC;AAE3D,MAAa,eAAe;IAM1B,YAAY,UAAkB,MAAM;QAJ5B,gBAAW,GAAgB,IAAI,CAAC;QAChC,mBAAc,GAA0B,IAAI,CAAC;QAInD,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,cAAc,GAAG;YACpB,iBAAiB,EAAE,EAAE;YACrB,wBAAwB,EAAE,IAAI;YAC9B,wBAAwB,EAAE,IAAI;YAC9B,sBAAsB,EAAE,IAAI;YAC5B,sBAAsB,EAAE,IAAI;YAC5B,kBAAkB,EAAE,EAAE;YACtB,qBAAqB,EAAE,GAAG,EAAE,UAAU;YACtC,iBAAiB,EAAE,CAAC;YACpB,sBAAsB,EAAE,EAAE;YAC1B,WAAW,EAAE,IAAI;SAClB,CAAC;QAEF,gCAAgC;QAChC,IAAI,CAAC,2BAA2B,EAAE,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,KAAa,EAAE,QAAgB,EAAE,QAAiB;QACnE,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC,OAAO,aAAa,EAAE;gBACzD,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE;oBACP,cAAc,EAAE,kBAAkB;iBACnC;gBACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;oBACnB,KAAK;oBACL,QAAQ;oBACR,QAAQ;oBACR,SAAS,EAAE,SAAS,CAAC,SAAS;oBAC9B,SAAS,EAAE,MAAM,IAAI,CAAC,WAAW,EAAE;iBACpC,CAAC;aACH,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YAErC,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBACnB,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC;gBAC/B,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAE3B,gCAAgC;gBAChC,MAAM,IAAI,CAAC,gBAAgB,CAAC;oBAC1B,MAAM,EAAE,wBAAwB;oBAChC,YAAY,EAAE,MAAM;oBACpB,UAAU,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE;oBAC1B,OAAO,EAAE,EAAE,MAAM,EAAE,cAAc,EAAE;oBACnC,SAAS,EAAE,KAAK;iBACjB,CAAC,CAAC;gBAEH,OAAO,MAAM,CAAC;YAChB,CAAC;iBAAM,CAAC;gBACN,4BAA4B;gBAC5B,MAAM,IAAI,CAAC,gBAAgB,CAAC;oBAC1B,MAAM,EAAE,uBAAuB;oBAC/B,YAAY,EAAE,MAAM;oBACpB,OAAO,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE;oBACvC,SAAS,EAAE,QAAQ;iBACpB,CAAC,CAAC;gBAEH,OAAO,MAAM,CAAC;YAChB,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;YAC9C,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,uBAAuB;aAC/B,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ;QACZ,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC,OAAO,iBAAiB,EAAE;gBAC7D,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE;oBACP,eAAe,EAAE,UAAU,IAAI,CAAC,QAAQ,EAAE,EAAE;oBAC5C,cAAc,EAAE,kBAAkB;iBACnC;aACF,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YAErC,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBACnB,MAAM,IAAI,CAAC,gBAAgB,CAAC;oBAC1B,MAAM,EAAE,qBAAqB;oBAC7B,YAAY,EAAE,MAAM;oBACpB,UAAU,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE;oBAChC,OAAO,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE;oBAC3B,SAAS,EAAE,KAAK;iBACjB,CAAC,CAAC;YACL,CAAC;YAED,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;YACzC,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;QACzC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,KAAa;QAChC,IAAI,CAAC;YACH,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAE5B,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC,OAAO,wBAAwB,EAAE;gBACpE,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE;oBACP,eAAe,EAAE,UAAU,IAAI,CAAC,QAAQ,EAAE,EAAE;oBAC5C,cAAc,EAAE,kBAAkB;iBACnC;gBACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,KAAK,EAAE,CAAC;aAChC,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YAErC,MAAM,IAAI,CAAC,gBAAgB,CAAC;gBAC1B,MAAM,EAAE,oBAAoB;gBAC5B,YAAY,EAAE,MAAM;gBACpB,UAAU,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE;gBAChC,OAAO,EAAE,EAAE,OAAO,EAAE,MAAM,CAAC,OAAO,EAAE;gBACpC,SAAS,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ;aAC7C,CAAC,CAAC;YAEH,OAAO,MAAM,CAAC,OAAO,CAAC;QACxB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YAChD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,UAAkB;QAC9B,IAAI,CAAC,IAAI,CAAC,WAAW;YAAE,OAAO,KAAK,CAAC;QACpC,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;IAC3D,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,KAAwB;QAC9B,IAAI,CAAC,IAAI,CAAC,WAAW;YAAE,OAAO,KAAK,CAAC;QACpC,MAAM,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QACzD,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,WAAY,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;IACxE,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,QAAgB;QAC/B,IAAI,CAAC;YACH,cAAc,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YAC/B,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;QACrC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,OAAC,CAAC,QAAQ,EAAE,CAAC;gBAChC,OAAO;oBACL,KAAK,EAAE,KAAK;oBACZ,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC;iBACzC,CAAC;YACJ,CAAC;YACD,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,4BAA4B,CAAC,EAAE,CAAC;QAClE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,eAAuB,EAAE,WAAmB;QAC/D,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;YACtD,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;gBACtB,MAAM,IAAI,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;YAChD,CAAC;YAED,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC,OAAO,uBAAuB,EAAE;gBACnE,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE;oBACP,eAAe,EAAE,UAAU,IAAI,CAAC,QAAQ,EAAE,EAAE;oBAC5C,cAAc,EAAE,kBAAkB;iBACnC;gBACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;oBACnB,eAAe;oBACf,WAAW;iBACZ,CAAC;aACH,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YAErC,MAAM,IAAI,CAAC,gBAAgB,CAAC;gBAC1B,MAAM,EAAE,kBAAkB;gBAC1B,YAAY,EAAE,MAAM;gBACpB,UAAU,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE;gBAChC,OAAO,EAAE,EAAE,OAAO,EAAE,MAAM,CAAC,OAAO,EAAE;gBACpC,SAAS,EAAE,QAAQ;aACpB,CAAC,CAAC;YAEH,OAAO,MAAM,CAAC,OAAO,CAAC;QACxB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YAC/C,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM;QACV,IAAI,CAAC;YACH,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC,OAAO,cAAc,EAAE;gBACzC,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE;oBACP,eAAe,EAAE,UAAU,IAAI,CAAC,QAAQ,EAAE,EAAE;iBAC7C;aACF,CAAC,CAAC;YAEH,MAAM,IAAI,CAAC,gBAAgB,CAAC;gBAC1B,MAAM,EAAE,QAAQ;gBAChB,YAAY,EAAE,MAAM;gBACpB,UAAU,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE;gBAChC,OAAO,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE;gBAC7B,SAAS,EAAE,KAAK;aACjB,CAAC,CAAC;YAEH,IAAI,CAAC,YAAY,EAAE,CAAC;QACtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;YACtC,IAAI,CAAC,YAAY,EAAE,CAAC;QACtB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,eAAe;QACb,OAAO,IAAI,CAAC,WAAW,KAAK,IAAI,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CAAC,QAAgB,EAAE;QACxC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC,OAAO,0BAA0B,KAAK,EAAE,EAAE;gBAC7E,OAAO,EAAE;oBACP,eAAe,EAAE,UAAU,IAAI,CAAC,QAAQ,EAAE,EAAE;iBAC7C;aACF,CAAC,CAAC;YAEH,OAAO,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;QAC/B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YACzD,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAAC,KAA6B;QAC1D,IAAI,CAAC;YACH,MAAM,SAAS,GAAkB;gBAC/B,OAAO,EAAE,IAAI,CAAC,eAAe,EAAE;gBAC/B,MAAM,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,IAAI,WAAW;gBAC3C,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,SAAS,EAAE,MAAM,IAAI,CAAC,WAAW,EAAE;gBACnC,SAAS,EAAE,SAAS,CAAC,SAAS;gBAC9B,GAAG,KAAK;aACQ,CAAC;YAEnB,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC,OAAO,kBAAkB,EAAE;gBAC7C,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE;oBACP,cAAc,EAAE,kBAAkB;oBAClC,eAAe,EAAE,UAAU,IAAI,CAAC,QAAQ,EAAE,EAAE;iBAC7C;gBACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;aAChC,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;QACxD,CAAC;IACH,CAAC;IAED;;OAEG;IACK,2BAA2B;QACjC,wBAAwB;QACxB,MAAM,MAAM,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC;QAE9E,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACrB,QAAQ,CAAC,gBAAgB,CAAC,KAAK,EAAE,GAAG,EAAE;gBACpC,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC7B,CAAC,EAAE,IAAI,CAAC,CAAC;QACX,CAAC,CAAC,CAAC;QAEH,0BAA0B;QAC1B,QAAQ,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,GAAG,EAAE;YACjD,IAAI,QAAQ,CAAC,MAAM,EAAE,CAAC;gBACpB,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC7B,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC9B,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,mBAAmB;QACzB,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAE3B,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,GAAG,EAAE;YACpC,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC9B,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,qBAAqB,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;IAC5D,CAAC;IAED;;OAEG;IACK,mBAAmB;QACzB,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE,CAAC;YAC3B,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC7B,CAAC;IACH,CAAC;IAED;;OAEG;IACK,mBAAmB;QACzB,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAClC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC7B,CAAC;IACH,CAAC;IAED;;OAEG;IACK,mBAAmB;QACzB,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC7B,CAAC;IAED;;OAEG;IACK,oBAAoB;QAC1B,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE,CAAC;YAC3B,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC7B,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,oBAAoB;QAChC,MAAM,IAAI,CAAC,gBAAgB,CAAC;YAC1B,MAAM,EAAE,iBAAiB;YACzB,YAAY,EAAE,SAAS;YACvB,OAAO,EAAE,EAAE,MAAM,EAAE,YAAY,EAAE;YACjC,SAAS,EAAE,KAAK;SACjB,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,EAAE,CAAC;QAEpB,kDAAkD;QAClD,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,+BAA+B,CAAC;IACzD,CAAC;IAED;;OAEG;IACK,YAAY;QAClB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC3B,YAAY,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;QACtC,cAAc,CAAC,KAAK,EAAE,CAAC;IACzB,CAAC;IAED;;OAEG;IACK,QAAQ;QACd,OAAO,YAAY,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,WAAW;QACvB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,mCAAmC,CAAC,CAAC;YAClE,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YACnC,OAAO,IAAI,CAAC,EAAE,CAAC;QACjB,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,SAAS,CAAC;QACnB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,eAAe;QACrB,OAAO,OAAO,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IACxE,CAAC;CACF;AA3aD,0CA2aC;AAED,4BAA4B;AACf,QAAA,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\frontend\\lib\\services\\SecurityService.ts"],
      sourcesContent: ["/**\r\n * Advanced Security Service for SizeWise Suite\r\n * Handles authentication, MFA, RBAC, and security monitoring\r\n */\r\n\r\nimport { z } from 'zod';\r\n\r\n// Types and Interfaces\r\nexport interface User {\r\n  id: string;\r\n  email: string;\r\n  roles: string[];\r\n  permissions: string[];\r\n  mfaEnabled: boolean;\r\n  lastLogin?: Date;\r\n  sessionId?: string;\r\n}\r\n\r\nexport interface AuthenticationResult {\r\n  success: boolean;\r\n  user?: User;\r\n  token?: string;\r\n  error?: string;\r\n  mfaRequired?: boolean;\r\n  sessionId?: string;\r\n}\r\n\r\nexport interface MFASetupResult {\r\n  secret: string;\r\n  qrCodeUrl: string;\r\n  backupCodes: string[];\r\n}\r\n\r\nexport interface SecurityEvent {\r\n  eventId: string;\r\n  userId: string;\r\n  action: string;\r\n  resourceType: string;\r\n  resourceId?: string;\r\n  timestamp: Date;\r\n  ipAddress: string;\r\n  userAgent: string;\r\n  details: Record<string, any>;\r\n  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';\r\n}\r\n\r\nexport interface SecurityPolicy {\r\n  passwordMinLength: number;\r\n  passwordRequireUppercase: boolean;\r\n  passwordRequireLowercase: boolean;\r\n  passwordRequireNumbers: boolean;\r\n  passwordRequireSymbols: boolean;\r\n  passwordMaxAgeDays: number;\r\n  sessionTimeoutMinutes: number;\r\n  maxFailedAttempts: number;\r\n  lockoutDurationMinutes: number;\r\n  mfaRequired: boolean;\r\n}\r\n\r\n// Validation Schemas\r\nconst passwordSchema = z.string()\r\n  .min(12, 'Password must be at least 12 characters')\r\n  .regex(/[A-Z]/, 'Password must contain uppercase letters')\r\n  .regex(/[a-z]/, 'Password must contain lowercase letters')\r\n  .regex(/[0-9]/, 'Password must contain numbers')\r\n  .regex(/[!@#$%^&*()_+\\-=\\[\\]{}|;:,.<>?]/, 'Password must contain special characters');\r\n\r\nconst mfaTokenSchema = z.string()\r\n  .length(6, 'MFA token must be 6 digits')\r\n  .regex(/^\\d{6}$/, 'MFA token must contain only numbers');\r\n\r\nexport class SecurityService {\r\n  private baseUrl: string;\r\n  private currentUser: User | null = null;\r\n  private sessionTimeout: NodeJS.Timeout | null = null;\r\n  private securityPolicy: SecurityPolicy;\r\n\r\n  constructor(baseUrl: string = '/api') {\r\n    this.baseUrl = baseUrl;\r\n    this.securityPolicy = {\r\n      passwordMinLength: 12,\r\n      passwordRequireUppercase: true,\r\n      passwordRequireLowercase: true,\r\n      passwordRequireNumbers: true,\r\n      passwordRequireSymbols: true,\r\n      passwordMaxAgeDays: 90,\r\n      sessionTimeoutMinutes: 480, // 8 hours\r\n      maxFailedAttempts: 5,\r\n      lockoutDurationMinutes: 30,\r\n      mfaRequired: true\r\n    };\r\n\r\n    // Initialize session monitoring\r\n    this.initializeSessionMonitoring();\r\n  }\r\n\r\n  /**\r\n   * Authenticate user with password and optional MFA\r\n   */\r\n  async authenticate(email: string, password: string, mfaToken?: string): Promise<AuthenticationResult> {\r\n    try {\r\n      const response = await fetch(`${this.baseUrl}/auth/login`, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({\r\n          email,\r\n          password,\r\n          mfaToken,\r\n          userAgent: navigator.userAgent,\r\n          ipAddress: await this.getClientIP()\r\n        }),\r\n      });\r\n\r\n      const result = await response.json();\r\n\r\n      if (result.success) {\r\n        this.currentUser = result.user;\r\n        this.startSessionTimeout();\r\n        \r\n        // Log successful authentication\r\n        await this.logSecurityEvent({\r\n          action: 'authentication_success',\r\n          resourceType: 'user',\r\n          resourceId: result.user.id,\r\n          details: { method: 'password_mfa' },\r\n          riskLevel: 'LOW'\r\n        });\r\n\r\n        return result;\r\n      } else {\r\n        // Log failed authentication\r\n        await this.logSecurityEvent({\r\n          action: 'authentication_failed',\r\n          resourceType: 'user',\r\n          details: { email, error: result.error },\r\n          riskLevel: 'MEDIUM'\r\n        });\r\n\r\n        return result;\r\n      }\r\n    } catch (error) {\r\n      console.error('Authentication error:', error);\r\n      return {\r\n        success: false,\r\n        error: 'Authentication failed'\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Setup MFA for user\r\n   */\r\n  async setupMFA(): Promise<MFASetupResult> {\r\n    try {\r\n      const response = await fetch(`${this.baseUrl}/auth/mfa/setup`, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Authorization': `Bearer ${this.getToken()}`,\r\n          'Content-Type': 'application/json',\r\n        },\r\n      });\r\n\r\n      const result = await response.json();\r\n\r\n      if (result.success) {\r\n        await this.logSecurityEvent({\r\n          action: 'mfa_setup_initiated',\r\n          resourceType: 'user',\r\n          resourceId: this.currentUser?.id,\r\n          details: { method: 'totp' },\r\n          riskLevel: 'LOW'\r\n        });\r\n      }\r\n\r\n      return result;\r\n    } catch (error) {\r\n      console.error('MFA setup error:', error);\r\n      throw new Error('Failed to setup MFA');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Verify MFA setup\r\n   */\r\n  async verifyMFASetup(token: string): Promise<boolean> {\r\n    try {\r\n      mfaTokenSchema.parse(token);\r\n\r\n      const response = await fetch(`${this.baseUrl}/auth/mfa/verify-setup`, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Authorization': `Bearer ${this.getToken()}`,\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({ token }),\r\n      });\r\n\r\n      const result = await response.json();\r\n\r\n      await this.logSecurityEvent({\r\n        action: 'mfa_setup_verified',\r\n        resourceType: 'user',\r\n        resourceId: this.currentUser?.id,\r\n        details: { success: result.success },\r\n        riskLevel: result.success ? 'LOW' : 'MEDIUM'\r\n      });\r\n\r\n      return result.success;\r\n    } catch (error) {\r\n      console.error('MFA verification error:', error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Check if user has specific permission\r\n   */\r\n  hasPermission(permission: string): boolean {\r\n    if (!this.currentUser) return false;\r\n    return this.currentUser.permissions.includes(permission);\r\n  }\r\n\r\n  /**\r\n   * Check if user has any of the specified roles\r\n   */\r\n  hasRole(roles: string | string[]): boolean {\r\n    if (!this.currentUser) return false;\r\n    const roleArray = Array.isArray(roles) ? roles : [roles];\r\n    return roleArray.some(role => this.currentUser!.roles.includes(role));\r\n  }\r\n\r\n  /**\r\n   * Validate password against security policy\r\n   */\r\n  validatePassword(password: string): { valid: boolean; errors: string[] } {\r\n    try {\r\n      passwordSchema.parse(password);\r\n      return { valid: true, errors: [] };\r\n    } catch (error) {\r\n      if (error instanceof z.ZodError) {\r\n        return {\r\n          valid: false,\r\n          errors: error.issues.map(e => e.message)\r\n        };\r\n      }\r\n      return { valid: false, errors: ['Password validation failed'] };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Change user password\r\n   */\r\n  async changePassword(currentPassword: string, newPassword: string): Promise<boolean> {\r\n    try {\r\n      const validation = this.validatePassword(newPassword);\r\n      if (!validation.valid) {\r\n        throw new Error(validation.errors.join(', '));\r\n      }\r\n\r\n      const response = await fetch(`${this.baseUrl}/auth/change-password`, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Authorization': `Bearer ${this.getToken()}`,\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({\r\n          currentPassword,\r\n          newPassword\r\n        }),\r\n      });\r\n\r\n      const result = await response.json();\r\n\r\n      await this.logSecurityEvent({\r\n        action: 'password_changed',\r\n        resourceType: 'user',\r\n        resourceId: this.currentUser?.id,\r\n        details: { success: result.success },\r\n        riskLevel: 'MEDIUM'\r\n      });\r\n\r\n      return result.success;\r\n    } catch (error) {\r\n      console.error('Password change error:', error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Logout user\r\n   */\r\n  async logout(): Promise<void> {\r\n    try {\r\n      await fetch(`${this.baseUrl}/auth/logout`, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Authorization': `Bearer ${this.getToken()}`,\r\n        },\r\n      });\r\n\r\n      await this.logSecurityEvent({\r\n        action: 'logout',\r\n        resourceType: 'user',\r\n        resourceId: this.currentUser?.id,\r\n        details: { method: 'manual' },\r\n        riskLevel: 'LOW'\r\n      });\r\n\r\n      this.clearSession();\r\n    } catch (error) {\r\n      console.error('Logout error:', error);\r\n      this.clearSession();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get current user\r\n   */\r\n  getCurrentUser(): User | null {\r\n    return this.currentUser;\r\n  }\r\n\r\n  /**\r\n   * Check if user is authenticated\r\n   */\r\n  isAuthenticated(): boolean {\r\n    return this.currentUser !== null;\r\n  }\r\n\r\n  /**\r\n   * Get security events for current user\r\n   */\r\n  async getSecurityEvents(limit: number = 50): Promise<SecurityEvent[]> {\r\n    try {\r\n      const response = await fetch(`${this.baseUrl}/security/events?limit=${limit}`, {\r\n        headers: {\r\n          'Authorization': `Bearer ${this.getToken()}`,\r\n        },\r\n      });\r\n\r\n      return await response.json();\r\n    } catch (error) {\r\n      console.error('Failed to fetch security events:', error);\r\n      return [];\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Log security event\r\n   */\r\n  private async logSecurityEvent(event: Partial<SecurityEvent>): Promise<void> {\r\n    try {\r\n      const fullEvent: SecurityEvent = {\r\n        eventId: this.generateEventId(),\r\n        userId: this.currentUser?.id || 'anonymous',\r\n        timestamp: new Date(),\r\n        ipAddress: await this.getClientIP(),\r\n        userAgent: navigator.userAgent,\r\n        ...event\r\n      } as SecurityEvent;\r\n\r\n      await fetch(`${this.baseUrl}/security/events`, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n          'Authorization': `Bearer ${this.getToken()}`,\r\n        },\r\n        body: JSON.stringify(fullEvent),\r\n      });\r\n    } catch (error) {\r\n      console.error('Failed to log security event:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Initialize session monitoring\r\n   */\r\n  private initializeSessionMonitoring(): void {\r\n    // Monitor user activity\r\n    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];\r\n    \r\n    events.forEach(event => {\r\n      document.addEventListener(event, () => {\r\n        this.resetSessionTimeout();\r\n      }, true);\r\n    });\r\n\r\n    // Monitor page visibility\r\n    document.addEventListener('visibilitychange', () => {\r\n      if (document.hidden) {\r\n        this.pauseSessionTimeout();\r\n      } else {\r\n        this.resumeSessionTimeout();\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Start session timeout\r\n   */\r\n  private startSessionTimeout(): void {\r\n    this.clearSessionTimeout();\r\n    \r\n    this.sessionTimeout = setTimeout(() => {\r\n      this.handleSessionTimeout();\r\n    }, this.securityPolicy.sessionTimeoutMinutes * 60 * 1000);\r\n  }\r\n\r\n  /**\r\n   * Reset session timeout\r\n   */\r\n  private resetSessionTimeout(): void {\r\n    if (this.isAuthenticated()) {\r\n      this.startSessionTimeout();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Clear session timeout\r\n   */\r\n  private clearSessionTimeout(): void {\r\n    if (this.sessionTimeout) {\r\n      clearTimeout(this.sessionTimeout);\r\n      this.sessionTimeout = null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Pause session timeout\r\n   */\r\n  private pauseSessionTimeout(): void {\r\n    this.clearSessionTimeout();\r\n  }\r\n\r\n  /**\r\n   * Resume session timeout\r\n   */\r\n  private resumeSessionTimeout(): void {\r\n    if (this.isAuthenticated()) {\r\n      this.startSessionTimeout();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Handle session timeout\r\n   */\r\n  private async handleSessionTimeout(): Promise<void> {\r\n    await this.logSecurityEvent({\r\n      action: 'session_timeout',\r\n      resourceType: 'session',\r\n      details: { reason: 'inactivity' },\r\n      riskLevel: 'LOW'\r\n    });\r\n\r\n    this.clearSession();\r\n    \r\n    // Redirect to login or show session expired modal\r\n    window.location.href = '/login?reason=session_expired';\r\n  }\r\n\r\n  /**\r\n   * Clear session data\r\n   */\r\n  private clearSession(): void {\r\n    this.currentUser = null;\r\n    this.clearSessionTimeout();\r\n    localStorage.removeItem('auth_token');\r\n    sessionStorage.clear();\r\n  }\r\n\r\n  /**\r\n   * Get authentication token\r\n   */\r\n  private getToken(): string | null {\r\n    return localStorage.getItem('auth_token');\r\n  }\r\n\r\n  /**\r\n   * Get client IP address\r\n   */\r\n  private async getClientIP(): Promise<string> {\r\n    try {\r\n      const response = await fetch('https://api.ipify.org?format=json');\r\n      const data = await response.json();\r\n      return data.ip;\r\n    } catch {\r\n      return 'unknown';\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Generate unique event ID\r\n   */\r\n  private generateEventId(): string {\r\n    return `evt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const securityService = new SecurityService();\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "db3ebc54f72540402c37378ed1f16a06bd186a6b"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1fcfbgkfrs = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1fcfbgkfrs();
cov_1fcfbgkfrs().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1fcfbgkfrs().s[1]++;
exports.securityService = exports.SecurityService = void 0;
const zod_1 =
/* istanbul ignore next */
(cov_1fcfbgkfrs().s[2]++, require("zod"));
// Validation Schemas
const passwordSchema =
/* istanbul ignore next */
(cov_1fcfbgkfrs().s[3]++, zod_1.z.string().min(12, 'Password must be at least 12 characters').regex(/[A-Z]/, 'Password must contain uppercase letters').regex(/[a-z]/, 'Password must contain lowercase letters').regex(/[0-9]/, 'Password must contain numbers').regex(/[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/, 'Password must contain special characters'));
const mfaTokenSchema =
/* istanbul ignore next */
(cov_1fcfbgkfrs().s[4]++, zod_1.z.string().length(6, 'MFA token must be 6 digits').regex(/^\d{6}$/, 'MFA token must contain only numbers'));
class SecurityService {
  constructor(baseUrl =
  /* istanbul ignore next */
  (cov_1fcfbgkfrs().b[0][0]++, '/api')) {
    /* istanbul ignore next */
    cov_1fcfbgkfrs().f[0]++;
    cov_1fcfbgkfrs().s[5]++;
    this.currentUser = null;
    /* istanbul ignore next */
    cov_1fcfbgkfrs().s[6]++;
    this.sessionTimeout = null;
    /* istanbul ignore next */
    cov_1fcfbgkfrs().s[7]++;
    this.baseUrl = baseUrl;
    /* istanbul ignore next */
    cov_1fcfbgkfrs().s[8]++;
    this.securityPolicy = {
      passwordMinLength: 12,
      passwordRequireUppercase: true,
      passwordRequireLowercase: true,
      passwordRequireNumbers: true,
      passwordRequireSymbols: true,
      passwordMaxAgeDays: 90,
      sessionTimeoutMinutes: 480,
      // 8 hours
      maxFailedAttempts: 5,
      lockoutDurationMinutes: 30,
      mfaRequired: true
    };
    // Initialize session monitoring
    /* istanbul ignore next */
    cov_1fcfbgkfrs().s[9]++;
    this.initializeSessionMonitoring();
  }
  /**
   * Authenticate user with password and optional MFA
   */
  async authenticate(email, password, mfaToken) {
    /* istanbul ignore next */
    cov_1fcfbgkfrs().f[1]++;
    cov_1fcfbgkfrs().s[10]++;
    try {
      const response =
      /* istanbul ignore next */
      (cov_1fcfbgkfrs().s[11]++, await fetch(`${this.baseUrl}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email,
          password,
          mfaToken,
          userAgent: navigator.userAgent,
          ipAddress: await this.getClientIP()
        })
      }));
      const result =
      /* istanbul ignore next */
      (cov_1fcfbgkfrs().s[12]++, await response.json());
      /* istanbul ignore next */
      cov_1fcfbgkfrs().s[13]++;
      if (result.success) {
        /* istanbul ignore next */
        cov_1fcfbgkfrs().b[1][0]++;
        cov_1fcfbgkfrs().s[14]++;
        this.currentUser = result.user;
        /* istanbul ignore next */
        cov_1fcfbgkfrs().s[15]++;
        this.startSessionTimeout();
        // Log successful authentication
        /* istanbul ignore next */
        cov_1fcfbgkfrs().s[16]++;
        await this.logSecurityEvent({
          action: 'authentication_success',
          resourceType: 'user',
          resourceId: result.user.id,
          details: {
            method: 'password_mfa'
          },
          riskLevel: 'LOW'
        });
        /* istanbul ignore next */
        cov_1fcfbgkfrs().s[17]++;
        return result;
      } else {
        /* istanbul ignore next */
        cov_1fcfbgkfrs().b[1][1]++;
        cov_1fcfbgkfrs().s[18]++;
        // Log failed authentication
        await this.logSecurityEvent({
          action: 'authentication_failed',
          resourceType: 'user',
          details: {
            email,
            error: result.error
          },
          riskLevel: 'MEDIUM'
        });
        /* istanbul ignore next */
        cov_1fcfbgkfrs().s[19]++;
        return result;
      }
    } catch (error) {
      /* istanbul ignore next */
      cov_1fcfbgkfrs().s[20]++;
      console.error('Authentication error:', error);
      /* istanbul ignore next */
      cov_1fcfbgkfrs().s[21]++;
      return {
        success: false,
        error: 'Authentication failed'
      };
    }
  }
  /**
   * Setup MFA for user
   */
  async setupMFA() {
    /* istanbul ignore next */
    cov_1fcfbgkfrs().f[2]++;
    cov_1fcfbgkfrs().s[22]++;
    try {
      const response =
      /* istanbul ignore next */
      (cov_1fcfbgkfrs().s[23]++, await fetch(`${this.baseUrl}/auth/mfa/setup`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.getToken()}`,
          'Content-Type': 'application/json'
        }
      }));
      const result =
      /* istanbul ignore next */
      (cov_1fcfbgkfrs().s[24]++, await response.json());
      /* istanbul ignore next */
      cov_1fcfbgkfrs().s[25]++;
      if (result.success) {
        /* istanbul ignore next */
        cov_1fcfbgkfrs().b[2][0]++;
        cov_1fcfbgkfrs().s[26]++;
        await this.logSecurityEvent({
          action: 'mfa_setup_initiated',
          resourceType: 'user',
          resourceId: this.currentUser?.id,
          details: {
            method: 'totp'
          },
          riskLevel: 'LOW'
        });
      } else
      /* istanbul ignore next */
      {
        cov_1fcfbgkfrs().b[2][1]++;
      }
      cov_1fcfbgkfrs().s[27]++;
      return result;
    } catch (error) {
      /* istanbul ignore next */
      cov_1fcfbgkfrs().s[28]++;
      console.error('MFA setup error:', error);
      /* istanbul ignore next */
      cov_1fcfbgkfrs().s[29]++;
      throw new Error('Failed to setup MFA');
    }
  }
  /**
   * Verify MFA setup
   */
  async verifyMFASetup(token) {
    /* istanbul ignore next */
    cov_1fcfbgkfrs().f[3]++;
    cov_1fcfbgkfrs().s[30]++;
    try {
      /* istanbul ignore next */
      cov_1fcfbgkfrs().s[31]++;
      mfaTokenSchema.parse(token);
      const response =
      /* istanbul ignore next */
      (cov_1fcfbgkfrs().s[32]++, await fetch(`${this.baseUrl}/auth/mfa/verify-setup`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.getToken()}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token
        })
      }));
      const result =
      /* istanbul ignore next */
      (cov_1fcfbgkfrs().s[33]++, await response.json());
      /* istanbul ignore next */
      cov_1fcfbgkfrs().s[34]++;
      await this.logSecurityEvent({
        action: 'mfa_setup_verified',
        resourceType: 'user',
        resourceId: this.currentUser?.id,
        details: {
          success: result.success
        },
        riskLevel: result.success ?
        /* istanbul ignore next */
        (cov_1fcfbgkfrs().b[3][0]++, 'LOW') :
        /* istanbul ignore next */
        (cov_1fcfbgkfrs().b[3][1]++, 'MEDIUM')
      });
      /* istanbul ignore next */
      cov_1fcfbgkfrs().s[35]++;
      return result.success;
    } catch (error) {
      /* istanbul ignore next */
      cov_1fcfbgkfrs().s[36]++;
      console.error('MFA verification error:', error);
      /* istanbul ignore next */
      cov_1fcfbgkfrs().s[37]++;
      return false;
    }
  }
  /**
   * Check if user has specific permission
   */
  hasPermission(permission) {
    /* istanbul ignore next */
    cov_1fcfbgkfrs().f[4]++;
    cov_1fcfbgkfrs().s[38]++;
    if (!this.currentUser) {
      /* istanbul ignore next */
      cov_1fcfbgkfrs().b[4][0]++;
      cov_1fcfbgkfrs().s[39]++;
      return false;
    } else
    /* istanbul ignore next */
    {
      cov_1fcfbgkfrs().b[4][1]++;
    }
    cov_1fcfbgkfrs().s[40]++;
    return this.currentUser.permissions.includes(permission);
  }
  /**
   * Check if user has any of the specified roles
   */
  hasRole(roles) {
    /* istanbul ignore next */
    cov_1fcfbgkfrs().f[5]++;
    cov_1fcfbgkfrs().s[41]++;
    if (!this.currentUser) {
      /* istanbul ignore next */
      cov_1fcfbgkfrs().b[5][0]++;
      cov_1fcfbgkfrs().s[42]++;
      return false;
    } else
    /* istanbul ignore next */
    {
      cov_1fcfbgkfrs().b[5][1]++;
    }
    const roleArray =
    /* istanbul ignore next */
    (cov_1fcfbgkfrs().s[43]++, Array.isArray(roles) ?
    /* istanbul ignore next */
    (cov_1fcfbgkfrs().b[6][0]++, roles) :
    /* istanbul ignore next */
    (cov_1fcfbgkfrs().b[6][1]++, [roles]));
    /* istanbul ignore next */
    cov_1fcfbgkfrs().s[44]++;
    return roleArray.some(role => {
      /* istanbul ignore next */
      cov_1fcfbgkfrs().f[6]++;
      cov_1fcfbgkfrs().s[45]++;
      return this.currentUser.roles.includes(role);
    });
  }
  /**
   * Validate password against security policy
   */
  validatePassword(password) {
    /* istanbul ignore next */
    cov_1fcfbgkfrs().f[7]++;
    cov_1fcfbgkfrs().s[46]++;
    try {
      /* istanbul ignore next */
      cov_1fcfbgkfrs().s[47]++;
      passwordSchema.parse(password);
      /* istanbul ignore next */
      cov_1fcfbgkfrs().s[48]++;
      return {
        valid: true,
        errors: []
      };
    } catch (error) {
      /* istanbul ignore next */
      cov_1fcfbgkfrs().s[49]++;
      if (error instanceof zod_1.z.ZodError) {
        /* istanbul ignore next */
        cov_1fcfbgkfrs().b[7][0]++;
        cov_1fcfbgkfrs().s[50]++;
        return {
          valid: false,
          errors: error.issues.map(e => {
            /* istanbul ignore next */
            cov_1fcfbgkfrs().f[8]++;
            cov_1fcfbgkfrs().s[51]++;
            return e.message;
          })
        };
      } else
      /* istanbul ignore next */
      {
        cov_1fcfbgkfrs().b[7][1]++;
      }
      cov_1fcfbgkfrs().s[52]++;
      return {
        valid: false,
        errors: ['Password validation failed']
      };
    }
  }
  /**
   * Change user password
   */
  async changePassword(currentPassword, newPassword) {
    /* istanbul ignore next */
    cov_1fcfbgkfrs().f[9]++;
    cov_1fcfbgkfrs().s[53]++;
    try {
      const validation =
      /* istanbul ignore next */
      (cov_1fcfbgkfrs().s[54]++, this.validatePassword(newPassword));
      /* istanbul ignore next */
      cov_1fcfbgkfrs().s[55]++;
      if (!validation.valid) {
        /* istanbul ignore next */
        cov_1fcfbgkfrs().b[8][0]++;
        cov_1fcfbgkfrs().s[56]++;
        throw new Error(validation.errors.join(', '));
      } else
      /* istanbul ignore next */
      {
        cov_1fcfbgkfrs().b[8][1]++;
      }
      const response =
      /* istanbul ignore next */
      (cov_1fcfbgkfrs().s[57]++, await fetch(`${this.baseUrl}/auth/change-password`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.getToken()}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          currentPassword,
          newPassword
        })
      }));
      const result =
      /* istanbul ignore next */
      (cov_1fcfbgkfrs().s[58]++, await response.json());
      /* istanbul ignore next */
      cov_1fcfbgkfrs().s[59]++;
      await this.logSecurityEvent({
        action: 'password_changed',
        resourceType: 'user',
        resourceId: this.currentUser?.id,
        details: {
          success: result.success
        },
        riskLevel: 'MEDIUM'
      });
      /* istanbul ignore next */
      cov_1fcfbgkfrs().s[60]++;
      return result.success;
    } catch (error) {
      /* istanbul ignore next */
      cov_1fcfbgkfrs().s[61]++;
      console.error('Password change error:', error);
      /* istanbul ignore next */
      cov_1fcfbgkfrs().s[62]++;
      return false;
    }
  }
  /**
   * Logout user
   */
  async logout() {
    /* istanbul ignore next */
    cov_1fcfbgkfrs().f[10]++;
    cov_1fcfbgkfrs().s[63]++;
    try {
      /* istanbul ignore next */
      cov_1fcfbgkfrs().s[64]++;
      await fetch(`${this.baseUrl}/auth/logout`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.getToken()}`
        }
      });
      /* istanbul ignore next */
      cov_1fcfbgkfrs().s[65]++;
      await this.logSecurityEvent({
        action: 'logout',
        resourceType: 'user',
        resourceId: this.currentUser?.id,
        details: {
          method: 'manual'
        },
        riskLevel: 'LOW'
      });
      /* istanbul ignore next */
      cov_1fcfbgkfrs().s[66]++;
      this.clearSession();
    } catch (error) {
      /* istanbul ignore next */
      cov_1fcfbgkfrs().s[67]++;
      console.error('Logout error:', error);
      /* istanbul ignore next */
      cov_1fcfbgkfrs().s[68]++;
      this.clearSession();
    }
  }
  /**
   * Get current user
   */
  getCurrentUser() {
    /* istanbul ignore next */
    cov_1fcfbgkfrs().f[11]++;
    cov_1fcfbgkfrs().s[69]++;
    return this.currentUser;
  }
  /**
   * Check if user is authenticated
   */
  isAuthenticated() {
    /* istanbul ignore next */
    cov_1fcfbgkfrs().f[12]++;
    cov_1fcfbgkfrs().s[70]++;
    return this.currentUser !== null;
  }
  /**
   * Get security events for current user
   */
  async getSecurityEvents(limit =
  /* istanbul ignore next */
  (cov_1fcfbgkfrs().b[9][0]++, 50)) {
    /* istanbul ignore next */
    cov_1fcfbgkfrs().f[13]++;
    cov_1fcfbgkfrs().s[71]++;
    try {
      const response =
      /* istanbul ignore next */
      (cov_1fcfbgkfrs().s[72]++, await fetch(`${this.baseUrl}/security/events?limit=${limit}`, {
        headers: {
          'Authorization': `Bearer ${this.getToken()}`
        }
      }));
      /* istanbul ignore next */
      cov_1fcfbgkfrs().s[73]++;
      return await response.json();
    } catch (error) {
      /* istanbul ignore next */
      cov_1fcfbgkfrs().s[74]++;
      console.error('Failed to fetch security events:', error);
      /* istanbul ignore next */
      cov_1fcfbgkfrs().s[75]++;
      return [];
    }
  }
  /**
   * Log security event
   */
  async logSecurityEvent(event) {
    /* istanbul ignore next */
    cov_1fcfbgkfrs().f[14]++;
    cov_1fcfbgkfrs().s[76]++;
    try {
      const fullEvent =
      /* istanbul ignore next */
      (cov_1fcfbgkfrs().s[77]++, {
        eventId: this.generateEventId(),
        userId:
        /* istanbul ignore next */
        (cov_1fcfbgkfrs().b[10][0]++, this.currentUser?.id) ||
        /* istanbul ignore next */
        (cov_1fcfbgkfrs().b[10][1]++, 'anonymous'),
        timestamp: new Date(),
        ipAddress: await this.getClientIP(),
        userAgent: navigator.userAgent,
        ...event
      });
      /* istanbul ignore next */
      cov_1fcfbgkfrs().s[78]++;
      await fetch(`${this.baseUrl}/security/events`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.getToken()}`
        },
        body: JSON.stringify(fullEvent)
      });
    } catch (error) {
      /* istanbul ignore next */
      cov_1fcfbgkfrs().s[79]++;
      console.error('Failed to log security event:', error);
    }
  }
  /**
   * Initialize session monitoring
   */
  initializeSessionMonitoring() {
    /* istanbul ignore next */
    cov_1fcfbgkfrs().f[15]++;
    // Monitor user activity
    const events =
    /* istanbul ignore next */
    (cov_1fcfbgkfrs().s[80]++, ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart']);
    /* istanbul ignore next */
    cov_1fcfbgkfrs().s[81]++;
    events.forEach(event => {
      /* istanbul ignore next */
      cov_1fcfbgkfrs().f[16]++;
      cov_1fcfbgkfrs().s[82]++;
      document.addEventListener(event, () => {
        /* istanbul ignore next */
        cov_1fcfbgkfrs().f[17]++;
        cov_1fcfbgkfrs().s[83]++;
        this.resetSessionTimeout();
      }, true);
    });
    // Monitor page visibility
    /* istanbul ignore next */
    cov_1fcfbgkfrs().s[84]++;
    document.addEventListener('visibilitychange', () => {
      /* istanbul ignore next */
      cov_1fcfbgkfrs().f[18]++;
      cov_1fcfbgkfrs().s[85]++;
      if (document.hidden) {
        /* istanbul ignore next */
        cov_1fcfbgkfrs().b[11][0]++;
        cov_1fcfbgkfrs().s[86]++;
        this.pauseSessionTimeout();
      } else {
        /* istanbul ignore next */
        cov_1fcfbgkfrs().b[11][1]++;
        cov_1fcfbgkfrs().s[87]++;
        this.resumeSessionTimeout();
      }
    });
  }
  /**
   * Start session timeout
   */
  startSessionTimeout() {
    /* istanbul ignore next */
    cov_1fcfbgkfrs().f[19]++;
    cov_1fcfbgkfrs().s[88]++;
    this.clearSessionTimeout();
    /* istanbul ignore next */
    cov_1fcfbgkfrs().s[89]++;
    this.sessionTimeout = setTimeout(() => {
      /* istanbul ignore next */
      cov_1fcfbgkfrs().f[20]++;
      cov_1fcfbgkfrs().s[90]++;
      this.handleSessionTimeout();
    }, this.securityPolicy.sessionTimeoutMinutes * 60 * 1000);
  }
  /**
   * Reset session timeout
   */
  resetSessionTimeout() {
    /* istanbul ignore next */
    cov_1fcfbgkfrs().f[21]++;
    cov_1fcfbgkfrs().s[91]++;
    if (this.isAuthenticated()) {
      /* istanbul ignore next */
      cov_1fcfbgkfrs().b[12][0]++;
      cov_1fcfbgkfrs().s[92]++;
      this.startSessionTimeout();
    } else
    /* istanbul ignore next */
    {
      cov_1fcfbgkfrs().b[12][1]++;
    }
  }
  /**
   * Clear session timeout
   */
  clearSessionTimeout() {
    /* istanbul ignore next */
    cov_1fcfbgkfrs().f[22]++;
    cov_1fcfbgkfrs().s[93]++;
    if (this.sessionTimeout) {
      /* istanbul ignore next */
      cov_1fcfbgkfrs().b[13][0]++;
      cov_1fcfbgkfrs().s[94]++;
      clearTimeout(this.sessionTimeout);
      /* istanbul ignore next */
      cov_1fcfbgkfrs().s[95]++;
      this.sessionTimeout = null;
    } else
    /* istanbul ignore next */
    {
      cov_1fcfbgkfrs().b[13][1]++;
    }
  }
  /**
   * Pause session timeout
   */
  pauseSessionTimeout() {
    /* istanbul ignore next */
    cov_1fcfbgkfrs().f[23]++;
    cov_1fcfbgkfrs().s[96]++;
    this.clearSessionTimeout();
  }
  /**
   * Resume session timeout
   */
  resumeSessionTimeout() {
    /* istanbul ignore next */
    cov_1fcfbgkfrs().f[24]++;
    cov_1fcfbgkfrs().s[97]++;
    if (this.isAuthenticated()) {
      /* istanbul ignore next */
      cov_1fcfbgkfrs().b[14][0]++;
      cov_1fcfbgkfrs().s[98]++;
      this.startSessionTimeout();
    } else
    /* istanbul ignore next */
    {
      cov_1fcfbgkfrs().b[14][1]++;
    }
  }
  /**
   * Handle session timeout
   */
  async handleSessionTimeout() {
    /* istanbul ignore next */
    cov_1fcfbgkfrs().f[25]++;
    cov_1fcfbgkfrs().s[99]++;
    await this.logSecurityEvent({
      action: 'session_timeout',
      resourceType: 'session',
      details: {
        reason: 'inactivity'
      },
      riskLevel: 'LOW'
    });
    /* istanbul ignore next */
    cov_1fcfbgkfrs().s[100]++;
    this.clearSession();
    // Redirect to login or show session expired modal
    /* istanbul ignore next */
    cov_1fcfbgkfrs().s[101]++;
    window.location.href = '/login?reason=session_expired';
  }
  /**
   * Clear session data
   */
  clearSession() {
    /* istanbul ignore next */
    cov_1fcfbgkfrs().f[26]++;
    cov_1fcfbgkfrs().s[102]++;
    this.currentUser = null;
    /* istanbul ignore next */
    cov_1fcfbgkfrs().s[103]++;
    this.clearSessionTimeout();
    /* istanbul ignore next */
    cov_1fcfbgkfrs().s[104]++;
    localStorage.removeItem('auth_token');
    /* istanbul ignore next */
    cov_1fcfbgkfrs().s[105]++;
    sessionStorage.clear();
  }
  /**
   * Get authentication token
   */
  getToken() {
    /* istanbul ignore next */
    cov_1fcfbgkfrs().f[27]++;
    cov_1fcfbgkfrs().s[106]++;
    return localStorage.getItem('auth_token');
  }
  /**
   * Get client IP address
   */
  async getClientIP() {
    /* istanbul ignore next */
    cov_1fcfbgkfrs().f[28]++;
    cov_1fcfbgkfrs().s[107]++;
    try {
      const response =
      /* istanbul ignore next */
      (cov_1fcfbgkfrs().s[108]++, await fetch('https://api.ipify.org?format=json'));
      const data =
      /* istanbul ignore next */
      (cov_1fcfbgkfrs().s[109]++, await response.json());
      /* istanbul ignore next */
      cov_1fcfbgkfrs().s[110]++;
      return data.ip;
    } catch {
      /* istanbul ignore next */
      cov_1fcfbgkfrs().s[111]++;
      return 'unknown';
    }
  }
  /**
   * Generate unique event ID
   */
  generateEventId() {
    /* istanbul ignore next */
    cov_1fcfbgkfrs().f[29]++;
    cov_1fcfbgkfrs().s[112]++;
    return `evt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
/* istanbul ignore next */
cov_1fcfbgkfrs().s[113]++;
exports.SecurityService = SecurityService;
// Export singleton instance
/* istanbul ignore next */
cov_1fcfbgkfrs().s[114]++;
exports.securityService = new SecurityService();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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