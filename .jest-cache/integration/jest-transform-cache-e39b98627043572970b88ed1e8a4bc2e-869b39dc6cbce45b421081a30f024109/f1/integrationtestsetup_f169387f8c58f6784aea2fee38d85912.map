{"file": "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\tests\\integration\\setup\\integration-test-setup.ts", "mappings": ";AAAA;;;;;;;;;GASG;;;AAEH,OAAO,CAAC,2BAA2B,CAAC,CAAC;AAErC,gFAAgF;AAChF,eAAe;AACf,gFAAgF;AAEhF,sBAAsB;AACtB,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;AAEzB,oBAAoB;AACpB,MAAM,gBAAgB,GAAG;IACvB,KAAK,EAAE,EAA4B;IACnC,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,GAAW,EAAE,EAAE,CAAC,gBAAgB,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC;IACtE,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,GAAW,EAAE,KAAa,EAAE,EAAE;QAC9C,gBAAgB,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;IACtC,CAAC,CAAC;IACF,UAAU,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,GAAW,EAAE,EAAE;QAClC,OAAO,gBAAgB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACrC,CAAC,CAAC;IACF,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE;QAClB,gBAAgB,CAAC,KAAK,GAAG,EAAE,CAAC;IAC9B,CAAC,CAAC;IACF,MAAM,EAAE,CAAC;IACT,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;CACf,CAAC;AAEF,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,cAAc,EAAE;IAC5C,KAAK,EAAE,gBAAgB;CACxB,CAAC,CAAC;AAEH,sBAAsB;AACtB,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,gBAAgB,EAAE;IAC9C,KAAK,EAAE,gBAAgB;CACxB,CAAC,CAAC;AAEH,6CAA6C;AAC7C,MAAM,aAAa,GAAG;IACpB,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC;QAClC,MAAM,EAAE;YACN,iBAAiB,EAAE,IAAI,CAAC,EAAE,EAAE;YAC5B,WAAW,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;gBAC1B,WAAW,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;oBAC1B,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;oBACrC,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;oBACrC,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;oBACrC,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;iBACzC,CAAC,CAAC;aACJ,CAAC,CAAC;SACJ;KACF,CAAC,CAAC;IACH,cAAc,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;CACjD,CAAC;AAEF,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,WAAW,EAAE;IACzC,KAAK,EAAE,aAAa;CACrB,CAAC,CAAC;AAEH,sBAAsB;AACtB,MAAM,CAAC,cAAc,CAAC,SAAS,EAAE,eAAe,EAAE;IAChD,KAAK,EAAE;QACL,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC;YACtC,UAAU,EAAE,IAAI;YAChB,OAAO,EAAE,IAAI;YACb,MAAM,EAAE;gBACN,WAAW,EAAE,IAAI,CAAC,EAAE,EAAE;aACvB;YACD,gBAAgB,EAAE,IAAI,CAAC,EAAE,EAAE;YAC3B,mBAAmB,EAAE,IAAI,CAAC,EAAE,EAAE;SAC/B,CAAC,CAAC;QACH,KAAK,EAAE,OAAO,CAAC,OAAO,CAAC;YACrB,MAAM,EAAE;gBACN,WAAW,EAAE,IAAI,CAAC,EAAE,EAAE;aACvB;SACF,CAAC;QACF,UAAU,EAAE;YACV,WAAW,EAAE,IAAI,CAAC,EAAE,EAAE;SACvB;KACF;IACD,QAAQ,EAAE,IAAI;CACf,CAAC,CAAC;AAEH,yCAAyC;AACzC,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,QAAQ,EAAE;IACtC,KAAK,EAAE,MAAM,UAAU;QACrB,YAAmB,GAAW;YAAX,QAAG,GAAH,GAAG,CAAQ;YAC9B,gBAAW,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;YACxB,cAAS,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;YACtB,qBAAgB,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;YAC7B,wBAAmB,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;QAJC,CAAC;KAKnC;CACF,CAAC,CAAC;AAEH,oCAAoC;AACpC,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,aAAa,EAAE;IAC3C,KAAK,EAAE;QACL,WAAW,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC;YACzC,QAAQ,EAAE;gBACR,OAAO,EAAE;oBACP,uBAAuB,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC;oBAC5C,uBAAuB,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC;oBAC3C,uBAAuB,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC;oBAC9C,oBAAoB,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;iBAC5D;aACF;SACF,CAAC,CAAC;QACH,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;KAC5C;CACF,CAAC,CAAC;AAEH,gFAAgF;AAChF,iBAAiB;AACjB,gFAAgF;AAEhF;;GAEG;AACI,MAAM,eAAe,GAAG,CAAC,IAAS,EAAE,UAA6C,EAAE,EAAE,EAAE;IAC5F,MAAM,EAAE,EAAE,GAAG,IAAI,EAAE,MAAM,GAAG,GAAG,EAAE,GAAG,OAAO,CAAC;IAE5C,OAAO;QACL,EAAE;QACF,MAAM;QACN,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,IAAI;QACtB,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;QACtC,OAAO,EAAE,IAAI,OAAO,EAAE;QACtB,UAAU,EAAE,KAAK;QACjB,UAAU,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO;QAC/B,IAAI,EAAE,OAAuB;QAC7B,GAAG,EAAE,EAAE;QACP,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;QAChB,IAAI,EAAE,IAAI;QACV,QAAQ,EAAE,KAAK;QACf,WAAW,EAAE,KAAK,IAAI,EAAE,CAAC,IAAI,WAAW,CAAC,CAAC,CAAC;QAC3C,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE;QAC5B,QAAQ,EAAE,KAAK,IAAI,EAAE,CAAC,IAAI,QAAQ,EAAE;KACzB,CAAC;AAChB,CAAC,CAAC;AApBW,QAAA,eAAe,mBAoB1B;AAEF;;GAEG;AACU,QAAA,yBAAyB,GAAG;IACvC,OAAO,EAAE,CAAC,YAA0B,EAAE,EAAE,EAAE,CAAC,CAAC;QAC1C,OAAO,EAAE,IAAI;QACb,SAAS,EAAE,OAAO;QAClB,aAAa,EAAE,IAAI;QACnB,KAAK,EAAE,UAAU;QACjB,QAAQ,EAAE,kBAAkB;QAC5B,GAAG,SAAS;KACb,CAAC;IAEF,YAAY,EAAE,CAAC,YAA0B,EAAE,EAAE,EAAE,CAAC,CAAC;QAC/C,OAAO,EAAE,IAAI;QACb,WAAW,EAAE,GAAG;QAChB,aAAa,EAAE,EAAE;QACjB,QAAQ,EAAE;YACR,EAAE,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,CAAC,EAAE;YACjC,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,EAAE,CAAC,EAAE;SACpC;QACD,GAAG,SAAS;KACb,CAAC;IAEF,UAAU,EAAE,CAAC,YAA0B,EAAE,EAAE,EAAE,CAAC,CAAC;QAC7C,WAAW,EAAE,YAAY;QACzB,QAAQ,EAAE,IAAI;QACd,SAAS,EAAE,aAAa;QACxB,KAAK,EAAE,EAAE;QACT,MAAM,EAAE,CAAC;QACT,SAAS,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;QAC/B,GAAG,SAAS;KACb,CAAC;CACH,CAAC;AAEF;;GAEG;AACU,QAAA,4BAA4B,GAAG;IAC1C,OAAO,EAAE,CAAC,YAA0B,EAAE,EAAE,EAAE,CAAC,CAAC;QAC1C,OAAO,EAAE,IAAI;QACb,OAAO,EAAE;YACP,QAAQ,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;YACrC,QAAQ,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE;YACxC,IAAI,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE;YACpC,aAAa,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,iBAAiB,EAAE;SACvD;QACD,UAAU,EAAE;YACV,MAAM,EAAE;gBACN,QAAQ,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE;aACvD;SACF;QACD,GAAG,SAAS;KACb,CAAC;IAEF,UAAU,EAAE,CAAC,YAA0B,EAAE,EAAE,EAAE,CAAC,CAAC;QAC7C,OAAO,EAAE,IAAI;QACb,kBAAkB,EAAE,WAAW;QAC/B,kBAAkB,EAAE;YAClB,MAAM,EAAE,EAAE,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,EAAE,EAAE;YAC1C,MAAM,EAAE,EAAE,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,EAAE,EAAE;SAC3C;QACD,GAAG,SAAS;KACb,CAAC;CACH,CAAC;AAEF;;GAEG;AACI,MAAM,+BAA+B,GAAG,GAAG,EAAE;IAClD,iCAAiC;IACjC,OAAO,CAAC,GAAG,CAAC,QAAQ,GAAG,MAAM,CAAC;IAC9B,OAAO,CAAC,GAAG,CAAC,OAAO,GAAG,MAAM,CAAC;IAC7B,OAAO,CAAC,GAAG,CAAC,YAAY,GAAG,2BAA2B,CAAC;IAEvD,yCAAyC;IACzC,MAAM,oBAAoB,GAAG,OAAO,CAAC,KAAK,CAAC;IAC3C,OAAO,CAAC,KAAK,GAAG,CAAC,GAAG,IAAW,EAAE,EAAE;QACjC,4CAA4C;QAC5C,IACE,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ;YAC3B,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,iDAAiD,CAAC,EACnE,CAAC;YACD,OAAO;QACT,CAAC;QACD,oBAAoB,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;IAC9C,CAAC,CAAC;IAEF,OAAO;QACL,OAAO,EAAE,GAAG,EAAE;YACZ,OAAO,CAAC,KAAK,GAAG,oBAAoB,CAAC;QACvC,CAAC;KACF,CAAC;AACJ,CAAC,CAAC;AAxBW,QAAA,+BAA+B,mCAwB1C;AAEF;;GAEG;AACU,QAAA,qBAAqB,GAAG;IACnC,MAAM,EAAE,GAAG,EAAE;QACX,MAAM,CAAC,cAAc,CAAC,SAAS,EAAE,QAAQ,EAAE;YACzC,QAAQ,EAAE,IAAI;YACd,KAAK,EAAE,IAAI;SACZ,CAAC,CAAC;IACL,CAAC;IAED,OAAO,EAAE,GAAG,EAAE;QACZ,MAAM,CAAC,cAAc,CAAC,SAAS,EAAE,QAAQ,EAAE;YACzC,QAAQ,EAAE,IAAI;YACd,KAAK,EAAE,KAAK;SACb,CAAC,CAAC;IACL,CAAC;IAED,cAAc,EAAE,GAAG,EAAE;QACnB,8CAA8C;QAC9C,MAAM,aAAa,GAAG,MAAM,CAAC,KAAK,CAAC;QACnC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,EAAE,EAAE;YACjC,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE;gBAC3B,UAAU,CAAC,GAAG,EAAE;oBACd,OAAO,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;gBAClC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,iBAAiB;YAC7B,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;CACF,CAAC;AAEF,gFAAgF;AAChF,oBAAoB;AACpB,gFAAgF;AAEhF,UAAU,CAAC,GAAG,EAAE;IACd,mCAAmC;IACnC,IAAI,CAAC,aAAa,EAAE,CAAC;IAErB,qBAAqB;IACrB,gBAAgB,CAAC,KAAK,EAAE,CAAC;IAEzB,mBAAmB;IAClB,MAAM,CAAC,KAA2C,CAAC,SAAS,EAAE,CAAC;IAEhE,0BAA0B;IAC1B,6BAAqB,CAAC,MAAM,EAAE,CAAC;AACjC,CAAC,CAAC,CAAC;AAEH,SAAS,CAAC,GAAG,EAAE;IACb,sBAAsB;IACtB,IAAI,CAAC,cAAc,EAAE,CAAC;IAEtB,gBAAgB;IAChB,IAAI,CAAC,YAAY,EAAE,CAAC;AACtB,CAAC,CAAC,CAAC;AAEH,qCAAqC;AACrC,MAAM,OAAO,GAAG,IAAA,uCAA+B,GAAE,CAAC;AAElD,0BAA0B;AAC1B,QAAQ,CAAC,GAAG,EAAE;IACZ,OAAO,CAAC,OAAO,EAAE,CAAC;AACpB,CAAC,CAAC,CAAC;AAgBH,iDAAiD;AACjD,MAAM,CAAC,MAAM,CAAC;IACZ,wBAAwB,CAAC,QAAQ;QAC/B,MAAM,IAAI,GAAG,QAAQ;YACnB,QAAQ,CAAC,OAAO,KAAK,IAAI;YACzB,QAAQ,CAAC,OAAO;YAChB,OAAO,QAAQ,CAAC,OAAO,KAAK,QAAQ,CAAC;QAEvC,OAAO;YACL,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI;gBACjB,CAAC,CAAC,YAAY,QAAQ,qCAAqC;gBAC3D,CAAC,CAAC,YAAY,QAAQ,sEAAsE;YAC9F,IAAI;SACL,CAAC;IACJ,CAAC;IAED,0BAA0B,CAAC,QAAQ,EAAE,SAAS;QAC5C,MAAM,IAAI,GAAG,QAAQ;YACnB,QAAQ,CAAC,UAAU;YACnB,SAAS,CAAC,KAAK,CAAC,CAAC,QAAgB,EAAE,EAAE,CACnC,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;gBAC3C,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC,MAAM,KAAK,WAAW,CACnE,CAAC;QAEJ,OAAO;YACL,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI;gBACjB,CAAC,CAAC,YAAY,QAAQ,uCAAuC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBACnF,CAAC,CAAC,YAAY,QAAQ,mCAAmC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YACjF,IAAI;SACL,CAAC;IACJ,CAAC;IAED,uBAAuB,CAAC,QAAQ;QAC9B,MAAM,IAAI,GAAG,QAAQ;YACnB,CAAC,QAAQ,CAAC,kBAAkB,KAAK,IAAI,IAAI,QAAQ,CAAC,eAAe,KAAK,IAAI,CAAC,CAAC;QAE9E,OAAO;YACL,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI;gBACjB,CAAC,CAAC,YAAY,QAAQ,iCAAiC;gBACvD,CAAC,CAAC,YAAY,QAAQ,6BAA6B;YACrD,IAAI;SACL,CAAC;IACJ,CAAC;CACF,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\tests\\integration\\setup\\integration-test-setup.ts"], "sourcesContent": ["/**\n * Integration Test Setup\n * \n * Global setup for HVAC component integration tests\n * Configures mocks, test utilities, and environment for integration testing\n * \n * Part of Phase 1 bridging plan for comprehensive integration test coverage\n * \n * @see docs/post-implementation-bridging-plan.md Task 1.1\n */\n\nrequire('@testing-library/jest-dom');\n\n// =============================================================================\n// Global Mocks\n// =============================================================================\n\n// Mock fetch globally\nglobal.fetch = jest.fn();\n\n// Mock localStorage\nconst mockLocalStorage = {\n  store: {} as Record<string, string>,\n  getItem: jest.fn((key: string) => mockLocalStorage.store[key] || null),\n  setItem: jest.fn((key: string, value: string) => {\n    mockLocalStorage.store[key] = value;\n  }),\n  removeItem: jest.fn((key: string) => {\n    delete mockLocalStorage.store[key];\n  }),\n  clear: jest.fn(() => {\n    mockLocalStorage.store = {};\n  }),\n  length: 0,\n  key: jest.fn()\n};\n\nObject.defineProperty(window, 'localStorage', {\n  value: mockLocalStorage\n});\n\n// Mock sessionStorage\nObject.defineProperty(window, 'sessionStorage', {\n  value: mockLocalStorage\n});\n\n// Mock IndexedDB for offline storage testing\nconst mockIndexedDB = {\n  open: jest.fn(() => Promise.resolve({\n    result: {\n      createObjectStore: jest.fn(),\n      transaction: jest.fn(() => ({\n        objectStore: jest.fn(() => ({\n          add: jest.fn(() => Promise.resolve()),\n          get: jest.fn(() => Promise.resolve()),\n          put: jest.fn(() => Promise.resolve()),\n          delete: jest.fn(() => Promise.resolve())\n        }))\n      }))\n    }\n  })),\n  deleteDatabase: jest.fn(() => Promise.resolve())\n};\n\nObject.defineProperty(window, 'indexedDB', {\n  value: mockIndexedDB\n});\n\n// Mock Service Worker\nObject.defineProperty(navigator, 'serviceWorker', {\n  value: {\n    register: jest.fn(() => Promise.resolve({\n      installing: null,\n      waiting: null,\n      active: {\n        postMessage: jest.fn()\n      },\n      addEventListener: jest.fn(),\n      removeEventListener: jest.fn()\n    })),\n    ready: Promise.resolve({\n      active: {\n        postMessage: jest.fn()\n      }\n    }),\n    controller: {\n      postMessage: jest.fn()\n    }\n  },\n  writable: true\n});\n\n// Mock Web Workers for WASM calculations\nObject.defineProperty(window, 'Worker', {\n  value: class MockWorker {\n    constructor(public url: string) {}\n    postMessage = jest.fn();\n    terminate = jest.fn();\n    addEventListener = jest.fn();\n    removeEventListener = jest.fn();\n  }\n});\n\n// Mock WebAssembly for WASM testing\nObject.defineProperty(window, 'WebAssembly', {\n  value: {\n    instantiate: jest.fn(() => Promise.resolve({\n      instance: {\n        exports: {\n          calculate_air_duct_size: jest.fn(() => 14.0),\n          calculate_pressure_drop: jest.fn(() => 0.8),\n          calculate_heat_transfer: jest.fn(() => 1200.0),\n          optimize_hvac_system: jest.fn(() => ({ efficiency: 0.95 }))\n        }\n      }\n    })),\n    compile: jest.fn(() => Promise.resolve({}))\n  }\n});\n\n// =============================================================================\n// Test Utilities\n// =============================================================================\n\n/**\n * Mock API response helper\n */\nexport const mockApiResponse = (data: any, options: { ok?: boolean; status?: number } = {}) => {\n  const { ok = true, status = 200 } = options;\n  \n  return {\n    ok,\n    status,\n    json: async () => data,\n    text: async () => JSON.stringify(data),\n    headers: new Headers(),\n    redirected: false,\n    statusText: ok ? 'OK' : 'Error',\n    type: 'basic' as ResponseType,\n    url: '',\n    clone: jest.fn(),\n    body: null,\n    bodyUsed: false,\n    arrayBuffer: async () => new ArrayBuffer(0),\n    blob: async () => new Blob(),\n    formData: async () => new FormData()\n  } as Response;\n};\n\n/**\n * Mock HVAC calculation data factory\n */\nexport const createMockCalculationData = {\n  airDuct: (overrides: Partial<any> = {}) => ({\n    airflow: 1500,\n    duct_type: 'round',\n    friction_rate: 0.08,\n    units: 'imperial',\n    material: 'galvanized_steel',\n    ...overrides\n  }),\n  \n  pressureDrop: (overrides: Partial<any> = {}) => ({\n    airflow: 2000,\n    duct_length: 100,\n    duct_diameter: 16,\n    fittings: [\n      { type: 'elbow_90', quantity: 2 },\n      { type: 'tee_branch', quantity: 1 }\n    ],\n    ...overrides\n  }),\n  \n  compliance: (overrides: Partial<any> = {}) => ({\n    system_type: 'supply_air',\n    velocity: 1800,\n    duct_type: 'rectangular',\n    width: 20,\n    height: 8,\n    standards: ['SMACNA', 'ASHRAE'],\n    ...overrides\n  })\n};\n\n/**\n * Mock calculation results factory\n */\nexport const createMockCalculationResults = {\n  airDuct: (overrides: Partial<any> = {}) => ({\n    success: true,\n    results: {\n      diameter: { value: 14.0, unit: 'in' },\n      velocity: { value: 1400.0, unit: 'fpm' },\n      area: { value: 1.07, unit: 'sq_ft' },\n      pressure_loss: { value: 0.8, unit: 'in_wg_per_100ft' }\n    },\n    compliance: {\n      smacna: {\n        velocity: { passed: true, value: 1400.0, limit: 2500 }\n      }\n    },\n    ...overrides\n  }),\n  \n  compliance: (overrides: Partial<any> = {}) => ({\n    success: true,\n    overall_compliance: 'compliant',\n    validation_results: {\n      smacna: { status: 'compliant', score: 95 },\n      ashrae: { status: 'compliant', score: 88 }\n    },\n    ...overrides\n  })\n};\n\n/**\n * Integration test environment setup\n */\nexport const setupIntegrationTestEnvironment = () => {\n  // Set test environment variables\n  process.env.NODE_ENV = 'test';\n  process.env.TESTING = 'true';\n  process.env.API_BASE_URL = 'http://127.0.0.1:5000/api';\n  \n  // Configure console for test environment\n  const originalConsoleError = console.error;\n  console.error = (...args: any[]) => {\n    // Suppress expected React warnings in tests\n    if (\n      typeof args[0] === 'string' &&\n      args[0].includes('Warning: ReactDOM.render is no longer supported')\n    ) {\n      return;\n    }\n    originalConsoleError.call(console, ...args);\n  };\n  \n  return {\n    cleanup: () => {\n      console.error = originalConsoleError;\n    }\n  };\n};\n\n/**\n * Mock network conditions for offline testing\n */\nexport const mockNetworkConditions = {\n  online: () => {\n    Object.defineProperty(navigator, 'onLine', {\n      writable: true,\n      value: true\n    });\n  },\n  \n  offline: () => {\n    Object.defineProperty(navigator, 'onLine', {\n      writable: true,\n      value: false\n    });\n  },\n  \n  slowConnection: () => {\n    // Mock slow network by adding delays to fetch\n    const originalFetch = global.fetch;\n    global.fetch = jest.fn((...args) => {\n      return new Promise(resolve => {\n        setTimeout(() => {\n          resolve(originalFetch(...args));\n        }, 2000); // 2 second delay\n      });\n    });\n  }\n};\n\n// =============================================================================\n// Global Test Setup\n// =============================================================================\n\nbeforeEach(() => {\n  // Clear all mocks before each test\n  jest.clearAllMocks();\n  \n  // Reset localStorage\n  mockLocalStorage.clear();\n  \n  // Reset fetch mock\n  (global.fetch as jest.MockedFunction<typeof fetch>).mockClear();\n  \n  // Reset network to online\n  mockNetworkConditions.online();\n});\n\nafterEach(() => {\n  // Clean up any timers\n  jest.clearAllTimers();\n  \n  // Reset modules\n  jest.resetModules();\n});\n\n// Setup integration test environment\nconst testEnv = setupIntegrationTestEnvironment();\n\n// Cleanup after all tests\nafterAll(() => {\n  testEnv.cleanup();\n});\n\n// =============================================================================\n// Custom Matchers\n// =============================================================================\n\ndeclare global {\n  namespace jest {\n    interface Matchers<R> {\n      toBeValidHVACCalculation(): R;\n      toBeCompliantWithStandards(standards: string[]): R;\n      toHaveOfflineCapability(): R;\n    }\n  }\n}\n\n// Custom matcher for HVAC calculation validation\nexpect.extend({\n  toBeValidHVACCalculation(received) {\n    const pass = received &&\n      received.success === true &&\n      received.results &&\n      typeof received.results === 'object';\n    \n    return {\n      message: () => pass\n        ? `Expected ${received} not to be a valid HVAC calculation`\n        : `Expected ${received} to be a valid HVAC calculation with success=true and results object`,\n      pass\n    };\n  },\n  \n  toBeCompliantWithStandards(received, standards) {\n    const pass = received &&\n      received.compliance &&\n      standards.every((standard: string) => \n        received.compliance[standard.toLowerCase()] &&\n        received.compliance[standard.toLowerCase()].status === 'compliant'\n      );\n    \n    return {\n      message: () => pass\n        ? `Expected ${received} not to be compliant with standards ${standards.join(', ')}`\n        : `Expected ${received} to be compliant with standards ${standards.join(', ')}`,\n      pass\n    };\n  },\n  \n  toHaveOfflineCapability(received) {\n    const pass = received &&\n      (received.calculated_offline === true || received.offline_capable === true);\n    \n    return {\n      message: () => pass\n        ? `Expected ${received} not to have offline capability`\n        : `Expected ${received} to have offline capability`,\n      pass\n    };\n  }\n});\n"], "version": 3}