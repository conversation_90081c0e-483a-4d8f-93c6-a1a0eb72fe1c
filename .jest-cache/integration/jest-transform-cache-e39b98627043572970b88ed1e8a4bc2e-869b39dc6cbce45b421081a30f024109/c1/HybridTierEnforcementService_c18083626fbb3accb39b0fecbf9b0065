702eccde6e9fd7d899b155bf713becdc
"use strict";

/**
 * Hybrid Tier Enforcement Service
 *
 * Integrates with the hybrid authentication system to enforce tier-based
 * feature restrictions while maintaining offline-first functionality
 */
/* istanbul ignore next */
function cov_2kcrqoolfe() {
  var path = "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\frontend\\lib\\services\\HybridTierEnforcementService.ts";
  var hash = "090f03bc4c8142492bf597673bf691b98fe685ad";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\frontend\\lib\\services\\HybridTierEnforcementService.ts",
    statementMap: {
      "0": {
        start: {
          line: 8,
          column: 0
        },
        end: {
          line: 8,
          column: 62
        }
      },
      "1": {
        start: {
          line: 9,
          column: 0
        },
        end: {
          line: 9,
          column: 79
        }
      },
      "2": {
        start: {
          line: 10,
          column: 21
        },
        end: {
          line: 10,
          column: 51
        }
      },
      "3": {
        start: {
          line: 16,
          column: 55
        },
        end: {
          line: 16,
          column: 91
        }
      },
      "4": {
        start: {
          line: 17,
          column: 8
        },
        end: {
          line: 39,
          column: 9
        }
      },
      "5": {
        start: {
          line: 18,
          column: 28
        },
        end: {
          line: 18,
          column: 68
        }
      },
      "6": {
        start: {
          line: 19,
          column: 12
        },
        end: {
          line: 26,
          column: 13
        }
      },
      "7": {
        start: {
          line: 20,
          column: 44
        },
        end: {
          line: 20,
          column: 54
        }
      },
      "8": {
        start: {
          line: 21,
          column: 16
        },
        end: {
          line: 25,
          column: 18
        }
      },
      "9": {
        start: {
          line: 27,
          column: 12
        },
        end: {
          line: 27,
          column: 37
        }
      },
      "10": {
        start: {
          line: 31,
          column: 12
        },
        end: {
          line: 37,
          column: 13
        }
      },
      "11": {
        start: {
          line: 32,
          column: 16
        },
        end: {
          line: 36,
          column: 18
        }
      },
      "12": {
        start: {
          line: 38,
          column: 12
        },
        end: {
          line: 38,
          column: 37
        }
      },
      "13": {
        start: {
          line: 45,
          column: 55
        },
        end: {
          line: 45,
          column: 91
        }
      },
      "14": {
        start: {
          line: 46,
          column: 8
        },
        end: {
          line: 68,
          column: 9
        }
      },
      "15": {
        start: {
          line: 47,
          column: 28
        },
        end: {
          line: 47,
          column: 102
        }
      },
      "16": {
        start: {
          line: 48,
          column: 12
        },
        end: {
          line: 55,
          column: 13
        }
      },
      "17": {
        start: {
          line: 49,
          column: 37
        },
        end: {
          line: 49,
          column: 47
        }
      },
      "18": {
        start: {
          line: 50,
          column: 16
        },
        end: {
          line: 54,
          column: 18
        }
      },
      "19": {
        start: {
          line: 56,
          column: 12
        },
        end: {
          line: 56,
          column: 37
        }
      },
      "20": {
        start: {
          line: 60,
          column: 12
        },
        end: {
          line: 66,
          column: 13
        }
      },
      "21": {
        start: {
          line: 61,
          column: 16
        },
        end: {
          line: 65,
          column: 18
        }
      },
      "22": {
        start: {
          line: 67,
          column: 12
        },
        end: {
          line: 67,
          column: 37
        }
      },
      "23": {
        start: {
          line: 74,
          column: 55
        },
        end: {
          line: 74,
          column: 91
        }
      },
      "24": {
        start: {
          line: 75,
          column: 8
        },
        end: {
          line: 99,
          column: 9
        }
      },
      "25": {
        start: {
          line: 76,
          column: 28
        },
        end: {
          line: 76,
          column: 69
        }
      },
      "26": {
        start: {
          line: 77,
          column: 12
        },
        end: {
          line: 84,
          column: 13
        }
      },
      "27": {
        start: {
          line: 78,
          column: 37
        },
        end: {
          line: 78,
          column: 47
        }
      },
      "28": {
        start: {
          line: 79,
          column: 16
        },
        end: {
          line: 83,
          column: 18
        }
      },
      "29": {
        start: {
          line: 85,
          column: 12
        },
        end: {
          line: 89,
          column: 14
        }
      },
      "30": {
        start: {
          line: 93,
          column: 31
        },
        end: {
          line: 93,
          column: 52
        }
      },
      "31": {
        start: {
          line: 94,
          column: 12
        },
        end: {
          line: 98,
          column: 14
        }
      },
      "32": {
        start: {
          line: 105,
          column: 55
        },
        end: {
          line: 105,
          column: 91
        }
      },
      "33": {
        start: {
          line: 106,
          column: 8
        },
        end: {
          line: 125,
          column: 9
        }
      },
      "34": {
        start: {
          line: 107,
          column: 28
        },
        end: {
          line: 107,
          column: 64
        }
      },
      "35": {
        start: {
          line: 108,
          column: 12
        },
        end: {
          line: 114,
          column: 13
        }
      },
      "36": {
        start: {
          line: 109,
          column: 16
        },
        end: {
          line: 113,
          column: 18
        }
      },
      "37": {
        start: {
          line: 115,
          column: 12
        },
        end: {
          line: 115,
          column: 37
        }
      },
      "38": {
        start: {
          line: 119,
          column: 33
        },
        end: {
          line: 119,
          column: 85
        }
      },
      "39": {
        start: {
          line: 120,
          column: 12
        },
        end: {
          line: 124,
          column: 14
        }
      },
      "40": {
        start: {
          line: 131,
          column: 37
        },
        end: {
          line: 131,
          column: 73
        }
      },
      "41": {
        start: {
          line: 133,
          column: 8
        },
        end: {
          line: 135,
          column: 9
        }
      },
      "42": {
        start: {
          line: 134,
          column: 12
        },
        end: {
          line: 134,
          column: 37
        }
      },
      "43": {
        start: {
          line: 136,
          column: 8
        },
        end: {
          line: 161,
          column: 9
        }
      },
      "44": {
        start: {
          line: 138,
          column: 12
        },
        end: {
          line: 145,
          column: 13
        }
      },
      "45": {
        start: {
          line: 139,
          column: 32
        },
        end: {
          line: 139,
          column: 58
        }
      },
      "46": {
        start: {
          line: 140,
          column: 16
        },
        end: {
          line: 144,
          column: 18
        }
      },
      "47": {
        start: {
          line: 147,
          column: 28
        },
        end: {
          line: 147,
          column: 49
        }
      },
      "48": {
        start: {
          line: 148,
          column: 12
        },
        end: {
          line: 152,
          column: 14
        }
      },
      "49": {
        start: {
          line: 156,
          column: 12
        },
        end: {
          line: 160,
          column: 14
        }
      },
      "50": {
        start: {
          line: 167,
          column: 37
        },
        end: {
          line: 167,
          column: 73
        }
      },
      "51": {
        start: {
          line: 168,
          column: 8
        },
        end: {
          line: 188,
          column: 9
        }
      },
      "52": {
        start: {
          line: 169,
          column: 34
        },
        end: {
          line: 169,
          column: 61
        }
      },
      "53": {
        start: {
          line: 171,
          column: 12
        },
        end: {
          line: 176,
          column: 13
        }
      },
      "54": {
        start: {
          line: 172,
          column: 35
        },
        end: {
          line: 172,
          column: 69
        }
      },
      "55": {
        start: {
          line: 173,
          column: 28
        },
        end: {
          line: 173,
          column: 38
        }
      },
      "56": {
        start: {
          line: 174,
          column: 33
        },
        end: {
          line: 174,
          column: 69
        }
      },
      "57": {
        start: {
          line: 175,
          column: 16
        },
        end: {
          line: 175,
          column: 94
        }
      },
      "58": {
        start: {
          line: 177,
          column: 12
        },
        end: {
          line: 187,
          column: 14
        }
      },
      "59": {
        start: {
          line: 190,
          column: 21
        },
        end: {
          line: 190,
          column: 41
        }
      },
      "60": {
        start: {
          line: 191,
          column: 27
        },
        end: {
          line: 191,
          column: 42
        }
      },
      "61": {
        start: {
          line: 192,
          column: 8
        },
        end: {
          line: 201,
          column: 10
        }
      },
      "62": {
        start: {
          line: 207,
          column: 37
        },
        end: {
          line: 207,
          column: 73
        }
      },
      "63": {
        start: {
          line: 209,
          column: 8
        },
        end: {
          line: 211,
          column: 9
        }
      },
      "64": {
        start: {
          line: 210,
          column: 12
        },
        end: {
          line: 210,
          column: 35
        }
      },
      "65": {
        start: {
          line: 212,
          column: 28
        },
        end: {
          line: 212,
          column: 68
        }
      },
      "66": {
        start: {
          line: 214,
          column: 8
        },
        end: {
          line: 216,
          column: 9
        }
      },
      "67": {
        start: {
          line: 215,
          column: 12
        },
        end: {
          line: 215,
          column: 35
        }
      },
      "68": {
        start: {
          line: 218,
          column: 8
        },
        end: {
          line: 231,
          column: 9
        }
      },
      "69": {
        start: {
          line: 219,
          column: 29
        },
        end: {
          line: 225,
          column: 13
        }
      },
      "70": {
        start: {
          line: 226,
          column: 12
        },
        end: {
          line: 230,
          column: 14
        }
      },
      "71": {
        start: {
          line: 233,
          column: 8
        },
        end: {
          line: 245,
          column: 9
        }
      },
      "72": {
        start: {
          line: 234,
          column: 31
        },
        end: {
          line: 234,
          column: 65
        }
      },
      "73": {
        start: {
          line: 235,
          column: 24
        },
        end: {
          line: 235,
          column: 34
        }
      },
      "74": {
        start: {
          line: 236,
          column: 29
        },
        end: {
          line: 236,
          column: 65
        }
      },
      "75": {
        start: {
          line: 237,
          column: 34
        },
        end: {
          line: 237,
          column: 77
        }
      },
      "76": {
        start: {
          line: 238,
          column: 12
        },
        end: {
          line: 244,
          column: 13
        }
      },
      "77": {
        start: {
          line: 239,
          column: 16
        },
        end: {
          line: 243,
          column: 18
        }
      },
      "78": {
        start: {
          line: 246,
          column: 8
        },
        end: {
          line: 246,
          column: 31
        }
      },
      "79": {
        start: {
          line: 249,
          column: 0
        },
        end: {
          line: 249,
          column: 68
        }
      },
      "80": {
        start: {
          line: 251,
          column: 0
        },
        end: {
          line: 251,
          column: 68
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 15,
            column: 4
          },
          end: {
            line: 15,
            column: 5
          }
        },
        loc: {
          start: {
            line: 15,
            column: 30
          },
          end: {
            line: 40,
            column: 5
          }
        },
        line: 15
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 44,
            column: 4
          },
          end: {
            line: 44,
            column: 5
          }
        },
        loc: {
          start: {
            line: 44,
            column: 45
          },
          end: {
            line: 69,
            column: 5
          }
        },
        line: 44
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 73,
            column: 4
          },
          end: {
            line: 73,
            column: 5
          }
        },
        loc: {
          start: {
            line: 73,
            column: 32
          },
          end: {
            line: 100,
            column: 5
          }
        },
        line: 73
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 104,
            column: 4
          },
          end: {
            line: 104,
            column: 5
          }
        },
        loc: {
          start: {
            line: 104,
            column: 27
          },
          end: {
            line: 126,
            column: 5
          }
        },
        line: 104
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 130,
            column: 4
          },
          end: {
            line: 130,
            column: 5
          }
        },
        loc: {
          start: {
            line: 130,
            column: 47
          },
          end: {
            line: 162,
            column: 5
          }
        },
        line: 130
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 166,
            column: 4
          },
          end: {
            line: 166,
            column: 5
          }
        },
        loc: {
          start: {
            line: 166,
            column: 24
          },
          end: {
            line: 202,
            column: 5
          }
        },
        line: 166
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 206,
            column: 4
          },
          end: {
            line: 206,
            column: 5
          }
        },
        loc: {
          start: {
            line: 206,
            column: 43
          },
          end: {
            line: 247,
            column: 5
          }
        },
        line: 206
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 26,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 26,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 19
      },
      "1": {
        loc: {
          start: {
            line: 19,
            column: 16
          },
          end: {
            line: 19,
            column: 38
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 19,
            column: 16
          },
          end: {
            line: 19,
            column: 24
          }
        }, {
          start: {
            line: 19,
            column: 28
          },
          end: {
            line: 19,
            column: 38
          }
        }],
        line: 19
      },
      "2": {
        loc: {
          start: {
            line: 31,
            column: 12
          },
          end: {
            line: 37,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 31,
            column: 12
          },
          end: {
            line: 37,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 31
      },
      "3": {
        loc: {
          start: {
            line: 48,
            column: 12
          },
          end: {
            line: 55,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 48,
            column: 12
          },
          end: {
            line: 55,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 48
      },
      "4": {
        loc: {
          start: {
            line: 48,
            column: 16
          },
          end: {
            line: 48,
            column: 38
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 48,
            column: 16
          },
          end: {
            line: 48,
            column: 24
          }
        }, {
          start: {
            line: 48,
            column: 28
          },
          end: {
            line: 48,
            column: 38
          }
        }],
        line: 48
      },
      "5": {
        loc: {
          start: {
            line: 60,
            column: 12
          },
          end: {
            line: 66,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 60,
            column: 12
          },
          end: {
            line: 66,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 60
      },
      "6": {
        loc: {
          start: {
            line: 60,
            column: 16
          },
          end: {
            line: 60,
            column: 62
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 60,
            column: 16
          },
          end: {
            line: 60,
            column: 37
          }
        }, {
          start: {
            line: 60,
            column: 41
          },
          end: {
            line: 60,
            column: 62
          }
        }],
        line: 60
      },
      "7": {
        loc: {
          start: {
            line: 77,
            column: 12
          },
          end: {
            line: 84,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 77,
            column: 12
          },
          end: {
            line: 84,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 77
      },
      "8": {
        loc: {
          start: {
            line: 82,
            column: 29
          },
          end: {
            line: 82,
            column: 118
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 82,
            column: 58
          },
          end: {
            line: 82,
            column: 106
          }
        }, {
          start: {
            line: 82,
            column: 109
          },
          end: {
            line: 82,
            column: 118
          }
        }],
        line: 82
      },
      "9": {
        loc: {
          start: {
            line: 88,
            column: 25
          },
          end: {
            line: 88,
            column: 96
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 88,
            column: 36
          },
          end: {
            line: 88,
            column: 84
          }
        }, {
          start: {
            line: 88,
            column: 87
          },
          end: {
            line: 88,
            column: 96
          }
        }],
        line: 88
      },
      "10": {
        loc: {
          start: {
            line: 97,
            column: 25
          },
          end: {
            line: 97,
            column: 98
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 97,
            column: 38
          },
          end: {
            line: 97,
            column: 86
          }
        }, {
          start: {
            line: 97,
            column: 89
          },
          end: {
            line: 97,
            column: 98
          }
        }],
        line: 97
      },
      "11": {
        loc: {
          start: {
            line: 108,
            column: 12
          },
          end: {
            line: 114,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 108,
            column: 12
          },
          end: {
            line: 114,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 108
      },
      "12": {
        loc: {
          start: {
            line: 119,
            column: 33
          },
          end: {
            line: 119,
            column: 85
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 119,
            column: 33
          },
          end: {
            line: 119,
            column: 53
          }
        }, {
          start: {
            line: 119,
            column: 57
          },
          end: {
            line: 119,
            column: 85
          }
        }],
        line: 119
      },
      "13": {
        loc: {
          start: {
            line: 122,
            column: 25
          },
          end: {
            line: 122,
            column: 110
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 122,
            column: 41
          },
          end: {
            line: 122,
            column: 98
          }
        }, {
          start: {
            line: 122,
            column: 101
          },
          end: {
            line: 122,
            column: 110
          }
        }],
        line: 122
      },
      "14": {
        loc: {
          start: {
            line: 133,
            column: 8
          },
          end: {
            line: 135,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 133,
            column: 8
          },
          end: {
            line: 135,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 133
      },
      "15": {
        loc: {
          start: {
            line: 138,
            column: 12
          },
          end: {
            line: 145,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 138,
            column: 12
          },
          end: {
            line: 145,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 138
      },
      "16": {
        loc: {
          start: {
            line: 142,
            column: 29
          },
          end: {
            line: 142,
            column: 126
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 142,
            column: 40
          },
          end: {
            line: 142,
            column: 114
          }
        }, {
          start: {
            line: 142,
            column: 117
          },
          end: {
            line: 142,
            column: 126
          }
        }],
        line: 142
      },
      "17": {
        loc: {
          start: {
            line: 150,
            column: 25
          },
          end: {
            line: 150,
            column: 122
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 150,
            column: 36
          },
          end: {
            line: 150,
            column: 110
          }
        }, {
          start: {
            line: 150,
            column: 113
          },
          end: {
            line: 150,
            column: 122
          }
        }],
        line: 150
      },
      "18": {
        loc: {
          start: {
            line: 168,
            column: 8
          },
          end: {
            line: 188,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 168,
            column: 8
          },
          end: {
            line: 188,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 168
      },
      "19": {
        loc: {
          start: {
            line: 171,
            column: 12
          },
          end: {
            line: 176,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 171,
            column: 12
          },
          end: {
            line: 176,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 171
      },
      "20": {
        loc: {
          start: {
            line: 171,
            column: 16
          },
          end: {
            line: 171,
            column: 57
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 171,
            column: 16
          },
          end: {
            line: 171,
            column: 29
          }
        }, {
          start: {
            line: 171,
            column: 33
          },
          end: {
            line: 171,
            column: 57
          }
        }],
        line: 171
      },
      "21": {
        loc: {
          start: {
            line: 182,
            column: 33
          },
          end: {
            line: 182,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 182,
            column: 33
          },
          end: {
            line: 182,
            column: 59
          }
        }, {
          start: {
            line: 182,
            column: 63
          },
          end: {
            line: 182,
            column: 64
          }
        }],
        line: 182
      },
      "22": {
        loc: {
          start: {
            line: 183,
            column: 33
          },
          end: {
            line: 183,
            column: 65
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 183,
            column: 33
          },
          end: {
            line: 183,
            column: 59
          }
        }, {
          start: {
            line: 183,
            column: 63
          },
          end: {
            line: 183,
            column: 65
          }
        }],
        line: 183
      },
      "23": {
        loc: {
          start: {
            line: 184,
            column: 36
          },
          end: {
            line: 184,
            column: 74
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 184,
            column: 36
          },
          end: {
            line: 184,
            column: 65
          }
        }, {
          start: {
            line: 184,
            column: 69
          },
          end: {
            line: 184,
            column: 74
          }
        }],
        line: 184
      },
      "24": {
        loc: {
          start: {
            line: 185,
            column: 31
          },
          end: {
            line: 185,
            column: 69
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 185,
            column: 31
          },
          end: {
            line: 185,
            column: 60
          }
        }, {
          start: {
            line: 185,
            column: 64
          },
          end: {
            line: 185,
            column: 69
          }
        }],
        line: 185
      },
      "25": {
        loc: {
          start: {
            line: 190,
            column: 21
          },
          end: {
            line: 190,
            column: 41
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 190,
            column: 21
          },
          end: {
            line: 190,
            column: 31
          }
        }, {
          start: {
            line: 190,
            column: 35
          },
          end: {
            line: 190,
            column: 41
          }
        }],
        line: 190
      },
      "26": {
        loc: {
          start: {
            line: 196,
            column: 29
          },
          end: {
            line: 196,
            column: 48
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 196,
            column: 42
          },
          end: {
            line: 196,
            column: 43
          }
        }, {
          start: {
            line: 196,
            column: 46
          },
          end: {
            line: 196,
            column: 48
          }
        }],
        line: 196
      },
      "27": {
        loc: {
          start: {
            line: 197,
            column: 29
          },
          end: {
            line: 197,
            column: 49
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 197,
            column: 42
          },
          end: {
            line: 197,
            column: 44
          }
        }, {
          start: {
            line: 197,
            column: 47
          },
          end: {
            line: 197,
            column: 49
          }
        }],
        line: 197
      },
      "28": {
        loc: {
          start: {
            line: 199,
            column: 27
          },
          end: {
            line: 199,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 199,
            column: 27
          },
          end: {
            line: 199,
            column: 41
          }
        }, {
          start: {
            line: 199,
            column: 45
          },
          end: {
            line: 199,
            column: 67
          }
        }],
        line: 199
      },
      "29": {
        loc: {
          start: {
            line: 209,
            column: 8
          },
          end: {
            line: 211,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 209,
            column: 8
          },
          end: {
            line: 211,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 209
      },
      "30": {
        loc: {
          start: {
            line: 212,
            column: 28
          },
          end: {
            line: 212,
            column: 68
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 212,
            column: 28
          },
          end: {
            line: 212,
            column: 44
          }
        }, {
          start: {
            line: 212,
            column: 48
          },
          end: {
            line: 212,
            column: 58
          }
        }, {
          start: {
            line: 212,
            column: 62
          },
          end: {
            line: 212,
            column: 68
          }
        }],
        line: 212
      },
      "31": {
        loc: {
          start: {
            line: 214,
            column: 8
          },
          end: {
            line: 216,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 214,
            column: 8
          },
          end: {
            line: 216,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 214
      },
      "32": {
        loc: {
          start: {
            line: 218,
            column: 8
          },
          end: {
            line: 231,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 218,
            column: 8
          },
          end: {
            line: 231,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 218
      },
      "33": {
        loc: {
          start: {
            line: 228,
            column: 25
          },
          end: {
            line: 228,
            column: 82
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 228,
            column: 25
          },
          end: {
            line: 228,
            column: 42
          }
        }, {
          start: {
            line: 228,
            column: 46
          },
          end: {
            line: 228,
            column: 82
          }
        }],
        line: 228
      },
      "34": {
        loc: {
          start: {
            line: 233,
            column: 8
          },
          end: {
            line: 245,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 233,
            column: 8
          },
          end: {
            line: 245,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 233
      },
      "35": {
        loc: {
          start: {
            line: 233,
            column: 12
          },
          end: {
            line: 233,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 233,
            column: 12
          },
          end: {
            line: 233,
            column: 35
          }
        }, {
          start: {
            line: 233,
            column: 39
          },
          end: {
            line: 233,
            column: 64
          }
        }],
        line: 233
      },
      "36": {
        loc: {
          start: {
            line: 238,
            column: 12
          },
          end: {
            line: 244,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 238,
            column: 12
          },
          end: {
            line: 244,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 238
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\frontend\\lib\\services\\HybridTierEnforcementService.ts",
      mappings: ";AAAA;;;;;GAKG;;;AAEH,oDAAkD;AAclD,MAAa,4BAA4B;IACvC;;OAEG;IACH,KAAK,CAAC,iBAAiB;QACrB,MAAM,EAAE,gBAAgB,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,yBAAY,CAAC,QAAQ,EAAE,CAAA;QAEtE,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,gBAAgB,CAAC,gBAAgB,CAAC,CAAA;YAExD,IAAI,CAAC,OAAO,IAAI,UAAU,EAAE,CAAC;gBAC3B,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,UAAU,CAAA;gBACtC,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,0BAA0B,KAAK,CAAC,cAAc,IAAI,QAAQ,CAAC,YAAY,+CAA+C;oBAC/H,eAAe,EAAE,IAAI;iBACtB,CAAA;YACH,CAAC;YAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAA;QAC1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,+CAA+C;YAC/C,IAAI,IAAI,EAAE,IAAI,KAAK,MAAM,EAAE,CAAC;gBAC1B,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,+EAA+E;oBACxF,eAAe,EAAE,IAAI;iBACtB,CAAA;YACH,CAAC;YAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAA;QAC1B,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CAAC,eAAuB;QAC7C,MAAM,EAAE,gBAAgB,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,yBAAY,CAAC,QAAQ,EAAE,CAAA;QAEtE,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,gBAAgB,CAAC,aAAa,EAAE,EAAE,cAAc,EAAE,eAAe,EAAE,CAAC,CAAA;YAE1F,IAAI,CAAC,OAAO,IAAI,UAAU,EAAE,CAAC;gBAC3B,MAAM,EAAE,QAAQ,EAAE,GAAG,UAAU,CAAA;gBAC/B,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,0BAA0B,eAAe,IAAI,QAAQ,CAAC,wBAAwB,+CAA+C;oBACtI,eAAe,EAAE,IAAI;iBACtB,CAAA;YACH,CAAC;YAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAA;QAC1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,+CAA+C;YAC/C,IAAI,IAAI,EAAE,IAAI,KAAK,MAAM,IAAI,eAAe,IAAI,EAAE,EAAE,CAAC;gBACnD,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,4FAA4F;oBACrG,eAAe,EAAE,IAAI;iBACtB,CAAA;YACH,CAAC;YAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAA;QAC1B,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB;QACvB,MAAM,EAAE,gBAAgB,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,yBAAY,CAAC,QAAQ,EAAE,CAAA;QAEtE,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,gBAAgB,CAAC,iBAAiB,CAAC,CAAA;YAEzD,IAAI,UAAU,EAAE,CAAC;gBACf,MAAM,EAAE,QAAQ,EAAE,GAAG,UAAU,CAAA;gBAC/B,OAAO;oBACL,OAAO,EAAE,QAAQ,CAAC,gBAAgB;oBAClC,WAAW,EAAE,QAAQ,CAAC,mBAAmB;oBACzC,OAAO,EAAE,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC,CAAC,gDAAgD,CAAC,CAAC,CAAC,SAAS;iBACnG,CAAA;YACH,CAAC;YAED,OAAO;gBACL,OAAO;gBACP,WAAW,EAAE,CAAC,OAAO;gBACrB,OAAO,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,gDAAgD,CAAC,CAAC,CAAC,SAAS;aACjF,CAAA;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,+CAA+C;YAC/C,MAAM,UAAU,GAAG,IAAI,EAAE,IAAI,KAAK,MAAM,CAAA;YAExC,OAAO;gBACL,OAAO,EAAE,CAAC,UAAU;gBACpB,WAAW,EAAE,UAAU;gBACvB,OAAO,EAAE,UAAU,CAAC,CAAC,CAAC,gDAAgD,CAAC,CAAC,CAAC,SAAS;aACnF,CAAA;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc;QAClB,MAAM,EAAE,gBAAgB,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,yBAAY,CAAC,QAAQ,EAAE,CAAA;QAEtE,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,gBAAgB,CAAC,YAAY,CAAC,CAAA;YAEpD,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,yDAAyD;oBAClE,eAAe,EAAE,IAAI;iBACtB,CAAA;YACH,CAAC;YAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAA;QAC1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,+CAA+C;YAC/C,MAAM,YAAY,GAAG,IAAI,EAAE,IAAI,KAAK,KAAK,IAAI,IAAI,EAAE,IAAI,KAAK,aAAa,CAAA;YAEzE,OAAO;gBACL,OAAO,EAAE,YAAY;gBACrB,OAAO,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC,yDAAyD,CAAC,CAAC,CAAC,SAAS;gBAC9F,eAAe,EAAE,CAAC,YAAY;aAC/B,CAAA;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kCAAkC;QACtC,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,yBAAY,CAAC,QAAQ,EAAE,CAAA;QAEpD,gCAAgC;QAChC,IAAI,IAAI,EAAE,IAAI,KAAK,aAAa,EAAE,CAAC;YACjC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAA;QAC1B,CAAC;QAED,IAAI,CAAC;YACH,+CAA+C;YAC/C,IAAI,UAAU,EAAE,CAAC;gBACf,MAAM,OAAO,GAAG,UAAU,CAAC,IAAI,KAAK,MAAM,CAAA;gBAC1C,OAAO;oBACL,OAAO;oBACP,OAAO,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,0EAA0E,CAAC,CAAC,CAAC,SAAS;oBAC1G,eAAe,EAAE,CAAC,OAAO;iBAC1B,CAAA;YACH,CAAC;YAED,8BAA8B;YAC9B,MAAM,OAAO,GAAG,IAAI,EAAE,IAAI,KAAK,MAAM,CAAA;YACrC,OAAO;gBACL,OAAO;gBACP,OAAO,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,0EAA0E,CAAC,CAAC,CAAC,SAAS;gBAC1G,eAAe,EAAE,CAAC,OAAO;aAC1B,CAAA;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,+BAA+B;YAC/B,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,iEAAiE;gBAC1E,eAAe,EAAE,IAAI;aACtB,CAAA;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW;QAWf,MAAM,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,yBAAY,CAAC,QAAQ,EAAE,CAAA;QAEpD,IAAI,UAAU,EAAE,CAAC;YACf,MAAM,aAAa,GAAG,UAAU,CAAC,IAAI,KAAK,OAAO,CAAA;YACjD,IAAI,kBAAsC,CAAA;YAE1C,IAAI,aAAa,IAAI,UAAU,CAAC,aAAa,EAAE,CAAC;gBAC9C,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,CAAA;gBACrD,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAA;gBACtB,MAAM,QAAQ,GAAG,UAAU,CAAC,OAAO,EAAE,GAAG,GAAG,CAAC,OAAO,EAAE,CAAA;gBACrD,kBAAkB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAA;YAC/E,CAAC;YAED,OAAO;gBACL,IAAI,EAAE,UAAU,CAAC,IAAI;gBACrB,aAAa;gBACb,kBAAkB;gBAClB,QAAQ,EAAE;oBACR,WAAW,EAAE,UAAU,CAAC,MAAM,CAAC,QAAQ,IAAI,CAAC;oBAC5C,WAAW,EAAE,UAAU,CAAC,MAAM,CAAC,QAAQ,IAAI,EAAE;oBAC7C,cAAc,EAAE,UAAU,CAAC,QAAQ,CAAC,SAAS,IAAI,KAAK;oBACtD,SAAS,EAAE,UAAU,CAAC,QAAQ,CAAC,SAAS,IAAI,KAAK;iBAClD;aACF,CAAA;QACH,CAAC;QAED,oCAAoC;QACpC,MAAM,IAAI,GAAG,IAAI,EAAE,IAAI,IAAI,MAAM,CAAA;QACjC,MAAM,UAAU,GAAG,IAAI,KAAK,MAAM,CAAA;QAElC,OAAO;YACL,IAAI;YACJ,aAAa,EAAE,KAAK;YACpB,QAAQ,EAAE;gBACR,WAAW,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChC,WAAW,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBACjC,cAAc,EAAE,CAAC,UAAU;gBAC3B,SAAS,EAAE,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,aAAa;aACpD;SACF,CAAA;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB,CAAC,OAAe;QAK3C,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,yBAAY,CAAC,QAAQ,EAAE,CAAA;QAEpD,6CAA6C;QAC7C,IAAI,IAAI,EAAE,IAAI,KAAK,aAAa,EAAE,CAAC;YACjC,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,CAAA;QACxB,CAAC;QAED,MAAM,WAAW,GAAG,UAAU,EAAE,IAAI,IAAI,IAAI,EAAE,IAAI,IAAI,MAAM,CAAA;QAE5D,+CAA+C;QAC/C,IAAI,WAAW,KAAK,SAAS,EAAE,CAAC;YAC9B,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,CAAA;QACxB,CAAC;QAED,2CAA2C;QAC3C,IAAI,WAAW,KAAK,MAAM,EAAE,CAAC;YAC3B,MAAM,QAAQ,GAAG;gBACf,QAAQ,EAAE,2CAA2C;gBACrD,QAAQ,EAAE,uDAAuD;gBACjE,OAAO,EAAE,gEAAgE;gBACzE,GAAG,EAAE,mCAAmC;gBACxC,aAAa,EAAE,qDAAqD;aACrE,CAAA;YAED,OAAO;gBACL,IAAI,EAAE,IAAI;gBACV,OAAO,EAAE,QAAQ,CAAC,OAAgC,CAAC,IAAI,oCAAoC;gBAC3F,OAAO,EAAE,oBAAoB;aAC9B,CAAA;QACH,CAAC;QAED,iDAAiD;QACjD,IAAI,WAAW,KAAK,OAAO,IAAI,UAAU,EAAE,aAAa,EAAE,CAAC;YACzD,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,CAAA;YACrD,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAA;YACtB,MAAM,QAAQ,GAAG,UAAU,CAAC,OAAO,EAAE,GAAG,GAAG,CAAC,OAAO,EAAE,CAAA;YACrD,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAA;YAEjE,IAAI,aAAa,IAAI,CAAC,EAAE,CAAC;gBACvB,OAAO;oBACL,IAAI,EAAE,IAAI;oBACV,OAAO,EAAE,yBAAyB,aAAa,0CAA0C;oBACzF,OAAO,EAAE,aAAa;iBACvB,CAAA;YACH,CAAC;QACH,CAAC;QAED,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,CAAA;IACxB,CAAC;CACF;AA5RD,oEA4RC;AAED,4BAA4B;AACf,QAAA,sBAAsB,GAAG,IAAI,4BAA4B,EAAE,CAAA",
      names: [],
      sources: ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\frontend\\lib\\services\\HybridTierEnforcementService.ts"],
      sourcesContent: ["/**\r\n * Hybrid Tier Enforcement Service\r\n * \r\n * Integrates with the hybrid authentication system to enforce tier-based\r\n * feature restrictions while maintaining offline-first functionality\r\n */\r\n\r\nimport { useAuthStore } from '@/stores/auth-store'\r\n\r\nexport interface TierCheckResult {\r\n  allowed: boolean\r\n  message?: string\r\n  upgradeRequired?: boolean\r\n}\r\n\r\nexport interface ExportFeatures {\r\n  highRes: boolean\r\n  watermarked: boolean\r\n  message?: string\r\n}\r\n\r\nexport class HybridTierEnforcementService {\r\n  /**\r\n   * Check if user can create a new project\r\n   */\r\n  async checkProjectLimit(): Promise<TierCheckResult> {\r\n    const { canPerformAction, tierStatus, user } = useAuthStore.getState()\r\n    \r\n    try {\r\n      const allowed = await canPerformAction('create_project')\r\n      \r\n      if (!allowed && tierStatus) {\r\n        const { features, usage } = tierStatus\r\n        return {\r\n          allowed: false,\r\n          message: `Project limit reached (${usage.projects_count}/${features.max_projects}). Upgrade to Premium for unlimited projects.`,\r\n          upgradeRequired: true,\r\n        }\r\n      }\r\n      \r\n      return { allowed: true }\r\n    } catch (error) {\r\n      // Fallback to legacy checking for offline mode\r\n      if (user?.tier === 'free') {\r\n        return {\r\n          allowed: false,\r\n          message: 'Free tier allows up to 3 projects. Upgrade to Premium for unlimited projects.',\r\n          upgradeRequired: true,\r\n        }\r\n      }\r\n      \r\n      return { allowed: true }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Check if user can add a segment to a project\r\n   */\r\n  async checkSegmentLimit(projectSegments: number): Promise<TierCheckResult> {\r\n    const { canPerformAction, tierStatus, user } = useAuthStore.getState()\r\n    \r\n    try {\r\n      const allowed = await canPerformAction('add_segment', { segments_count: projectSegments })\r\n      \r\n      if (!allowed && tierStatus) {\r\n        const { features } = tierStatus\r\n        return {\r\n          allowed: false,\r\n          message: `Segment limit reached (${projectSegments}/${features.max_segments_per_project}). Upgrade to Premium for unlimited segments.`,\r\n          upgradeRequired: true,\r\n        }\r\n      }\r\n      \r\n      return { allowed: true }\r\n    } catch (error) {\r\n      // Fallback to legacy checking for offline mode\r\n      if (user?.tier === 'free' && projectSegments >= 25) {\r\n        return {\r\n          allowed: false,\r\n          message: 'Free tier allows up to 25 segments per project. Upgrade to Premium for unlimited segments.',\r\n          upgradeRequired: true,\r\n        }\r\n      }\r\n      \r\n      return { allowed: true }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Check export features available to user\r\n   */\r\n  async checkExportFeatures(): Promise<ExportFeatures> {\r\n    const { canPerformAction, tierStatus, user } = useAuthStore.getState()\r\n    \r\n    try {\r\n      const highRes = await canPerformAction('high_res_export')\r\n      \r\n      if (tierStatus) {\r\n        const { features } = tierStatus\r\n        return {\r\n          highRes: features.high_res_exports,\r\n          watermarked: features.watermarked_exports,\r\n          message: !features.high_res_exports ? 'Upgrade to Premium for high-resolution exports' : undefined,\r\n        }\r\n      }\r\n      \r\n      return {\r\n        highRes,\r\n        watermarked: !highRes,\r\n        message: !highRes ? 'Upgrade to Premium for high-resolution exports' : undefined,\r\n      }\r\n    } catch (error) {\r\n      // Fallback to legacy checking for offline mode\r\n      const isFreeTier = user?.tier === 'free'\r\n      \r\n      return {\r\n        highRes: !isFreeTier,\r\n        watermarked: isFreeTier,\r\n        message: isFreeTier ? 'Upgrade to Premium for high-resolution exports' : undefined,\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Check if user can access API features\r\n   */\r\n  async checkApiAccess(): Promise<TierCheckResult> {\r\n    const { canPerformAction, tierStatus, user } = useAuthStore.getState()\r\n    \r\n    try {\r\n      const allowed = await canPerformAction('api_access')\r\n      \r\n      if (!allowed) {\r\n        return {\r\n          allowed: false,\r\n          message: 'API access is available with Premium subscription only.',\r\n          upgradeRequired: true,\r\n        }\r\n      }\r\n      \r\n      return { allowed: true }\r\n    } catch (error) {\r\n      // Fallback to legacy checking for offline mode\r\n      const hasApiAccess = user?.tier === 'pro' || user?.tier === 'super_admin'\r\n      \r\n      return {\r\n        allowed: hasApiAccess,\r\n        message: !hasApiAccess ? 'API access is available with Premium subscription only.' : undefined,\r\n        upgradeRequired: !hasApiAccess,\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Check if user can edit computational properties\r\n   */\r\n  async checkComputationalPropertiesAccess(): Promise<TierCheckResult> {\r\n    const { user, tierStatus } = useAuthStore.getState()\r\n    \r\n    // Super admin always has access\r\n    if (user?.tier === 'super_admin') {\r\n      return { allowed: true }\r\n    }\r\n    \r\n    try {\r\n      // For hybrid authentication, check tier status\r\n      if (tierStatus) {\r\n        const allowed = tierStatus.tier !== 'free'\r\n        return {\r\n          allowed,\r\n          message: !allowed ? 'Computational properties editing is available with Premium subscription.' : undefined,\r\n          upgradeRequired: !allowed,\r\n        }\r\n      }\r\n      \r\n      // Fallback to legacy checking\r\n      const allowed = user?.tier !== 'free'\r\n      return {\r\n        allowed,\r\n        message: !allowed ? 'Computational properties editing is available with Premium subscription.' : undefined,\r\n        upgradeRequired: !allowed,\r\n      }\r\n    } catch (error) {\r\n      // Default to restricted access\r\n      return {\r\n        allowed: false,\r\n        message: 'Computational properties editing requires Premium subscription.',\r\n        upgradeRequired: true,\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get user's current tier information\r\n   */\r\n  async getTierInfo(): Promise<{\r\n    tier: string\r\n    isTrialActive: boolean\r\n    trialDaysRemaining?: number\r\n    features: {\r\n      maxProjects: number\r\n      maxSegments: number\r\n      highResExports: boolean\r\n      apiAccess: boolean\r\n    }\r\n  }> {\r\n    const { tierStatus, user } = useAuthStore.getState()\r\n    \r\n    if (tierStatus) {\r\n      const isTrialActive = tierStatus.tier === 'trial'\r\n      let trialDaysRemaining: number | undefined\r\n      \r\n      if (isTrialActive && tierStatus.trial_expires) {\r\n        const expiryDate = new Date(tierStatus.trial_expires)\r\n        const now = new Date()\r\n        const diffTime = expiryDate.getTime() - now.getTime()\r\n        trialDaysRemaining = Math.max(0, Math.ceil(diffTime / (1000 * 60 * 60 * 24)))\r\n      }\r\n      \r\n      return {\r\n        tier: tierStatus.tier,\r\n        isTrialActive,\r\n        trialDaysRemaining,\r\n        features: {\r\n          maxProjects: tierStatus.limits.projects || 3,\r\n          maxSegments: tierStatus.limits.segments || 10,\r\n          highResExports: tierStatus.features.unlimited || false,\r\n          apiAccess: tierStatus.features.unlimited || false,\r\n        },\r\n      }\r\n    }\r\n    \r\n    // Fallback to user tier information\r\n    const tier = user?.tier || 'free'\r\n    const isFreeTier = tier === 'free'\r\n    \r\n    return {\r\n      tier,\r\n      isTrialActive: false,\r\n      features: {\r\n        maxProjects: isFreeTier ? 3 : -1,\r\n        maxSegments: isFreeTier ? 25 : -1,\r\n        highResExports: !isFreeTier,\r\n        apiAccess: tier === 'pro' || tier === 'super_admin',\r\n      },\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Check if user needs to upgrade for a specific feature\r\n   */\r\n  async shouldShowUpgradePrompt(feature: string): Promise<{\r\n    show: boolean\r\n    message?: string\r\n    ctaText?: string\r\n  }> {\r\n    const { user, tierStatus } = useAuthStore.getState()\r\n    \r\n    // Never show upgrade prompts for super admin\r\n    if (user?.tier === 'super_admin') {\r\n      return { show: false }\r\n    }\r\n    \r\n    const currentTier = tierStatus?.tier || user?.tier || 'free'\r\n    \r\n    // Don't show upgrade prompts for premium users\r\n    if (currentTier === 'premium') {\r\n      return { show: false }\r\n    }\r\n    \r\n    // Show upgrade prompts for free tier users\r\n    if (currentTier === 'free') {\r\n      const messages = {\r\n        projects: 'Upgrade to Premium for unlimited projects',\r\n        segments: 'Upgrade to Premium for unlimited segments per project',\r\n        exports: 'Upgrade to Premium for high-resolution, watermark-free exports',\r\n        api: 'Upgrade to Premium for API access',\r\n        computational: 'Upgrade to Premium to edit computational properties',\r\n      }\r\n      \r\n      return {\r\n        show: true,\r\n        message: messages[feature as keyof typeof messages] || 'Upgrade to Premium for full access',\r\n        ctaText: 'Upgrade to Premium',\r\n      }\r\n    }\r\n    \r\n    // Show trial expiration warnings for trial users\r\n    if (currentTier === 'trial' && tierStatus?.trial_expires) {\r\n      const expiryDate = new Date(tierStatus.trial_expires)\r\n      const now = new Date()\r\n      const diffTime = expiryDate.getTime() - now.getTime()\r\n      const daysRemaining = Math.ceil(diffTime / (1000 * 60 * 60 * 24))\r\n      \r\n      if (daysRemaining <= 3) {\r\n        return {\r\n          show: true,\r\n          message: `Your trial expires in ${daysRemaining} days. Upgrade to keep Premium features.`,\r\n          ctaText: 'Upgrade Now',\r\n        }\r\n      }\r\n    }\r\n    \r\n    return { show: false }\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const tierEnforcementService = new HybridTierEnforcementService()\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "090f03bc4c8142492bf597673bf691b98fe685ad"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2kcrqoolfe = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2kcrqoolfe();
cov_2kcrqoolfe().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_2kcrqoolfe().s[1]++;
exports.tierEnforcementService = exports.HybridTierEnforcementService = void 0;
const auth_store_1 =
/* istanbul ignore next */
(cov_2kcrqoolfe().s[2]++, require("@/stores/auth-store"));
class HybridTierEnforcementService {
  /**
   * Check if user can create a new project
   */
  async checkProjectLimit() {
    /* istanbul ignore next */
    cov_2kcrqoolfe().f[0]++;
    const {
      canPerformAction,
      tierStatus,
      user
    } =
    /* istanbul ignore next */
    (cov_2kcrqoolfe().s[3]++, auth_store_1.useAuthStore.getState());
    /* istanbul ignore next */
    cov_2kcrqoolfe().s[4]++;
    try {
      const allowed =
      /* istanbul ignore next */
      (cov_2kcrqoolfe().s[5]++, await canPerformAction('create_project'));
      /* istanbul ignore next */
      cov_2kcrqoolfe().s[6]++;
      if (
      /* istanbul ignore next */
      (cov_2kcrqoolfe().b[1][0]++, !allowed) &&
      /* istanbul ignore next */
      (cov_2kcrqoolfe().b[1][1]++, tierStatus)) {
        /* istanbul ignore next */
        cov_2kcrqoolfe().b[0][0]++;
        const {
          features,
          usage
        } =
        /* istanbul ignore next */
        (cov_2kcrqoolfe().s[7]++, tierStatus);
        /* istanbul ignore next */
        cov_2kcrqoolfe().s[8]++;
        return {
          allowed: false,
          message: `Project limit reached (${usage.projects_count}/${features.max_projects}). Upgrade to Premium for unlimited projects.`,
          upgradeRequired: true
        };
      } else
      /* istanbul ignore next */
      {
        cov_2kcrqoolfe().b[0][1]++;
      }
      cov_2kcrqoolfe().s[9]++;
      return {
        allowed: true
      };
    } catch (error) {
      /* istanbul ignore next */
      cov_2kcrqoolfe().s[10]++;
      // Fallback to legacy checking for offline mode
      if (user?.tier === 'free') {
        /* istanbul ignore next */
        cov_2kcrqoolfe().b[2][0]++;
        cov_2kcrqoolfe().s[11]++;
        return {
          allowed: false,
          message: 'Free tier allows up to 3 projects. Upgrade to Premium for unlimited projects.',
          upgradeRequired: true
        };
      } else
      /* istanbul ignore next */
      {
        cov_2kcrqoolfe().b[2][1]++;
      }
      cov_2kcrqoolfe().s[12]++;
      return {
        allowed: true
      };
    }
  }
  /**
   * Check if user can add a segment to a project
   */
  async checkSegmentLimit(projectSegments) {
    /* istanbul ignore next */
    cov_2kcrqoolfe().f[1]++;
    const {
      canPerformAction,
      tierStatus,
      user
    } =
    /* istanbul ignore next */
    (cov_2kcrqoolfe().s[13]++, auth_store_1.useAuthStore.getState());
    /* istanbul ignore next */
    cov_2kcrqoolfe().s[14]++;
    try {
      const allowed =
      /* istanbul ignore next */
      (cov_2kcrqoolfe().s[15]++, await canPerformAction('add_segment', {
        segments_count: projectSegments
      }));
      /* istanbul ignore next */
      cov_2kcrqoolfe().s[16]++;
      if (
      /* istanbul ignore next */
      (cov_2kcrqoolfe().b[4][0]++, !allowed) &&
      /* istanbul ignore next */
      (cov_2kcrqoolfe().b[4][1]++, tierStatus)) {
        /* istanbul ignore next */
        cov_2kcrqoolfe().b[3][0]++;
        const {
          features
        } =
        /* istanbul ignore next */
        (cov_2kcrqoolfe().s[17]++, tierStatus);
        /* istanbul ignore next */
        cov_2kcrqoolfe().s[18]++;
        return {
          allowed: false,
          message: `Segment limit reached (${projectSegments}/${features.max_segments_per_project}). Upgrade to Premium for unlimited segments.`,
          upgradeRequired: true
        };
      } else
      /* istanbul ignore next */
      {
        cov_2kcrqoolfe().b[3][1]++;
      }
      cov_2kcrqoolfe().s[19]++;
      return {
        allowed: true
      };
    } catch (error) {
      /* istanbul ignore next */
      cov_2kcrqoolfe().s[20]++;
      // Fallback to legacy checking for offline mode
      if (
      /* istanbul ignore next */
      (cov_2kcrqoolfe().b[6][0]++, user?.tier === 'free') &&
      /* istanbul ignore next */
      (cov_2kcrqoolfe().b[6][1]++, projectSegments >= 25)) {
        /* istanbul ignore next */
        cov_2kcrqoolfe().b[5][0]++;
        cov_2kcrqoolfe().s[21]++;
        return {
          allowed: false,
          message: 'Free tier allows up to 25 segments per project. Upgrade to Premium for unlimited segments.',
          upgradeRequired: true
        };
      } else
      /* istanbul ignore next */
      {
        cov_2kcrqoolfe().b[5][1]++;
      }
      cov_2kcrqoolfe().s[22]++;
      return {
        allowed: true
      };
    }
  }
  /**
   * Check export features available to user
   */
  async checkExportFeatures() {
    /* istanbul ignore next */
    cov_2kcrqoolfe().f[2]++;
    const {
      canPerformAction,
      tierStatus,
      user
    } =
    /* istanbul ignore next */
    (cov_2kcrqoolfe().s[23]++, auth_store_1.useAuthStore.getState());
    /* istanbul ignore next */
    cov_2kcrqoolfe().s[24]++;
    try {
      const highRes =
      /* istanbul ignore next */
      (cov_2kcrqoolfe().s[25]++, await canPerformAction('high_res_export'));
      /* istanbul ignore next */
      cov_2kcrqoolfe().s[26]++;
      if (tierStatus) {
        /* istanbul ignore next */
        cov_2kcrqoolfe().b[7][0]++;
        const {
          features
        } =
        /* istanbul ignore next */
        (cov_2kcrqoolfe().s[27]++, tierStatus);
        /* istanbul ignore next */
        cov_2kcrqoolfe().s[28]++;
        return {
          highRes: features.high_res_exports,
          watermarked: features.watermarked_exports,
          message: !features.high_res_exports ?
          /* istanbul ignore next */
          (cov_2kcrqoolfe().b[8][0]++, 'Upgrade to Premium for high-resolution exports') :
          /* istanbul ignore next */
          (cov_2kcrqoolfe().b[8][1]++, undefined)
        };
      } else
      /* istanbul ignore next */
      {
        cov_2kcrqoolfe().b[7][1]++;
      }
      cov_2kcrqoolfe().s[29]++;
      return {
        highRes,
        watermarked: !highRes,
        message: !highRes ?
        /* istanbul ignore next */
        (cov_2kcrqoolfe().b[9][0]++, 'Upgrade to Premium for high-resolution exports') :
        /* istanbul ignore next */
        (cov_2kcrqoolfe().b[9][1]++, undefined)
      };
    } catch (error) {
      // Fallback to legacy checking for offline mode
      const isFreeTier =
      /* istanbul ignore next */
      (cov_2kcrqoolfe().s[30]++, user?.tier === 'free');
      /* istanbul ignore next */
      cov_2kcrqoolfe().s[31]++;
      return {
        highRes: !isFreeTier,
        watermarked: isFreeTier,
        message: isFreeTier ?
        /* istanbul ignore next */
        (cov_2kcrqoolfe().b[10][0]++, 'Upgrade to Premium for high-resolution exports') :
        /* istanbul ignore next */
        (cov_2kcrqoolfe().b[10][1]++, undefined)
      };
    }
  }
  /**
   * Check if user can access API features
   */
  async checkApiAccess() {
    /* istanbul ignore next */
    cov_2kcrqoolfe().f[3]++;
    const {
      canPerformAction,
      tierStatus,
      user
    } =
    /* istanbul ignore next */
    (cov_2kcrqoolfe().s[32]++, auth_store_1.useAuthStore.getState());
    /* istanbul ignore next */
    cov_2kcrqoolfe().s[33]++;
    try {
      const allowed =
      /* istanbul ignore next */
      (cov_2kcrqoolfe().s[34]++, await canPerformAction('api_access'));
      /* istanbul ignore next */
      cov_2kcrqoolfe().s[35]++;
      if (!allowed) {
        /* istanbul ignore next */
        cov_2kcrqoolfe().b[11][0]++;
        cov_2kcrqoolfe().s[36]++;
        return {
          allowed: false,
          message: 'API access is available with Premium subscription only.',
          upgradeRequired: true
        };
      } else
      /* istanbul ignore next */
      {
        cov_2kcrqoolfe().b[11][1]++;
      }
      cov_2kcrqoolfe().s[37]++;
      return {
        allowed: true
      };
    } catch (error) {
      // Fallback to legacy checking for offline mode
      const hasApiAccess =
      /* istanbul ignore next */
      (cov_2kcrqoolfe().s[38]++,
      /* istanbul ignore next */
      (cov_2kcrqoolfe().b[12][0]++, user?.tier === 'pro') ||
      /* istanbul ignore next */
      (cov_2kcrqoolfe().b[12][1]++, user?.tier === 'super_admin'));
      /* istanbul ignore next */
      cov_2kcrqoolfe().s[39]++;
      return {
        allowed: hasApiAccess,
        message: !hasApiAccess ?
        /* istanbul ignore next */
        (cov_2kcrqoolfe().b[13][0]++, 'API access is available with Premium subscription only.') :
        /* istanbul ignore next */
        (cov_2kcrqoolfe().b[13][1]++, undefined),
        upgradeRequired: !hasApiAccess
      };
    }
  }
  /**
   * Check if user can edit computational properties
   */
  async checkComputationalPropertiesAccess() {
    /* istanbul ignore next */
    cov_2kcrqoolfe().f[4]++;
    const {
      user,
      tierStatus
    } =
    /* istanbul ignore next */
    (cov_2kcrqoolfe().s[40]++, auth_store_1.useAuthStore.getState());
    // Super admin always has access
    /* istanbul ignore next */
    cov_2kcrqoolfe().s[41]++;
    if (user?.tier === 'super_admin') {
      /* istanbul ignore next */
      cov_2kcrqoolfe().b[14][0]++;
      cov_2kcrqoolfe().s[42]++;
      return {
        allowed: true
      };
    } else
    /* istanbul ignore next */
    {
      cov_2kcrqoolfe().b[14][1]++;
    }
    cov_2kcrqoolfe().s[43]++;
    try {
      /* istanbul ignore next */
      cov_2kcrqoolfe().s[44]++;
      // For hybrid authentication, check tier status
      if (tierStatus) {
        /* istanbul ignore next */
        cov_2kcrqoolfe().b[15][0]++;
        const allowed =
        /* istanbul ignore next */
        (cov_2kcrqoolfe().s[45]++, tierStatus.tier !== 'free');
        /* istanbul ignore next */
        cov_2kcrqoolfe().s[46]++;
        return {
          allowed,
          message: !allowed ?
          /* istanbul ignore next */
          (cov_2kcrqoolfe().b[16][0]++, 'Computational properties editing is available with Premium subscription.') :
          /* istanbul ignore next */
          (cov_2kcrqoolfe().b[16][1]++, undefined),
          upgradeRequired: !allowed
        };
      } else
      /* istanbul ignore next */
      {
        cov_2kcrqoolfe().b[15][1]++;
      }
      // Fallback to legacy checking
      const allowed =
      /* istanbul ignore next */
      (cov_2kcrqoolfe().s[47]++, user?.tier !== 'free');
      /* istanbul ignore next */
      cov_2kcrqoolfe().s[48]++;
      return {
        allowed,
        message: !allowed ?
        /* istanbul ignore next */
        (cov_2kcrqoolfe().b[17][0]++, 'Computational properties editing is available with Premium subscription.') :
        /* istanbul ignore next */
        (cov_2kcrqoolfe().b[17][1]++, undefined),
        upgradeRequired: !allowed
      };
    } catch (error) {
      /* istanbul ignore next */
      cov_2kcrqoolfe().s[49]++;
      // Default to restricted access
      return {
        allowed: false,
        message: 'Computational properties editing requires Premium subscription.',
        upgradeRequired: true
      };
    }
  }
  /**
   * Get user's current tier information
   */
  async getTierInfo() {
    /* istanbul ignore next */
    cov_2kcrqoolfe().f[5]++;
    const {
      tierStatus,
      user
    } =
    /* istanbul ignore next */
    (cov_2kcrqoolfe().s[50]++, auth_store_1.useAuthStore.getState());
    /* istanbul ignore next */
    cov_2kcrqoolfe().s[51]++;
    if (tierStatus) {
      /* istanbul ignore next */
      cov_2kcrqoolfe().b[18][0]++;
      const isTrialActive =
      /* istanbul ignore next */
      (cov_2kcrqoolfe().s[52]++, tierStatus.tier === 'trial');
      let trialDaysRemaining;
      /* istanbul ignore next */
      cov_2kcrqoolfe().s[53]++;
      if (
      /* istanbul ignore next */
      (cov_2kcrqoolfe().b[20][0]++, isTrialActive) &&
      /* istanbul ignore next */
      (cov_2kcrqoolfe().b[20][1]++, tierStatus.trial_expires)) {
        /* istanbul ignore next */
        cov_2kcrqoolfe().b[19][0]++;
        const expiryDate =
        /* istanbul ignore next */
        (cov_2kcrqoolfe().s[54]++, new Date(tierStatus.trial_expires));
        const now =
        /* istanbul ignore next */
        (cov_2kcrqoolfe().s[55]++, new Date());
        const diffTime =
        /* istanbul ignore next */
        (cov_2kcrqoolfe().s[56]++, expiryDate.getTime() - now.getTime());
        /* istanbul ignore next */
        cov_2kcrqoolfe().s[57]++;
        trialDaysRemaining = Math.max(0, Math.ceil(diffTime / (1000 * 60 * 60 * 24)));
      } else
      /* istanbul ignore next */
      {
        cov_2kcrqoolfe().b[19][1]++;
      }
      cov_2kcrqoolfe().s[58]++;
      return {
        tier: tierStatus.tier,
        isTrialActive,
        trialDaysRemaining,
        features: {
          maxProjects:
          /* istanbul ignore next */
          (cov_2kcrqoolfe().b[21][0]++, tierStatus.limits.projects) ||
          /* istanbul ignore next */
          (cov_2kcrqoolfe().b[21][1]++, 3),
          maxSegments:
          /* istanbul ignore next */
          (cov_2kcrqoolfe().b[22][0]++, tierStatus.limits.segments) ||
          /* istanbul ignore next */
          (cov_2kcrqoolfe().b[22][1]++, 10),
          highResExports:
          /* istanbul ignore next */
          (cov_2kcrqoolfe().b[23][0]++, tierStatus.features.unlimited) ||
          /* istanbul ignore next */
          (cov_2kcrqoolfe().b[23][1]++, false),
          apiAccess:
          /* istanbul ignore next */
          (cov_2kcrqoolfe().b[24][0]++, tierStatus.features.unlimited) ||
          /* istanbul ignore next */
          (cov_2kcrqoolfe().b[24][1]++, false)
        }
      };
    } else
    /* istanbul ignore next */
    {
      cov_2kcrqoolfe().b[18][1]++;
    }
    // Fallback to user tier information
    const tier =
    /* istanbul ignore next */
    (cov_2kcrqoolfe().s[59]++,
    /* istanbul ignore next */
    (cov_2kcrqoolfe().b[25][0]++, user?.tier) ||
    /* istanbul ignore next */
    (cov_2kcrqoolfe().b[25][1]++, 'free'));
    const isFreeTier =
    /* istanbul ignore next */
    (cov_2kcrqoolfe().s[60]++, tier === 'free');
    /* istanbul ignore next */
    cov_2kcrqoolfe().s[61]++;
    return {
      tier,
      isTrialActive: false,
      features: {
        maxProjects: isFreeTier ?
        /* istanbul ignore next */
        (cov_2kcrqoolfe().b[26][0]++, 3) :
        /* istanbul ignore next */
        (cov_2kcrqoolfe().b[26][1]++, -1),
        maxSegments: isFreeTier ?
        /* istanbul ignore next */
        (cov_2kcrqoolfe().b[27][0]++, 25) :
        /* istanbul ignore next */
        (cov_2kcrqoolfe().b[27][1]++, -1),
        highResExports: !isFreeTier,
        apiAccess:
        /* istanbul ignore next */
        (cov_2kcrqoolfe().b[28][0]++, tier === 'pro') ||
        /* istanbul ignore next */
        (cov_2kcrqoolfe().b[28][1]++, tier === 'super_admin')
      }
    };
  }
  /**
   * Check if user needs to upgrade for a specific feature
   */
  async shouldShowUpgradePrompt(feature) {
    /* istanbul ignore next */
    cov_2kcrqoolfe().f[6]++;
    const {
      user,
      tierStatus
    } =
    /* istanbul ignore next */
    (cov_2kcrqoolfe().s[62]++, auth_store_1.useAuthStore.getState());
    // Never show upgrade prompts for super admin
    /* istanbul ignore next */
    cov_2kcrqoolfe().s[63]++;
    if (user?.tier === 'super_admin') {
      /* istanbul ignore next */
      cov_2kcrqoolfe().b[29][0]++;
      cov_2kcrqoolfe().s[64]++;
      return {
        show: false
      };
    } else
    /* istanbul ignore next */
    {
      cov_2kcrqoolfe().b[29][1]++;
    }
    const currentTier =
    /* istanbul ignore next */
    (cov_2kcrqoolfe().s[65]++,
    /* istanbul ignore next */
    (cov_2kcrqoolfe().b[30][0]++, tierStatus?.tier) ||
    /* istanbul ignore next */
    (cov_2kcrqoolfe().b[30][1]++, user?.tier) ||
    /* istanbul ignore next */
    (cov_2kcrqoolfe().b[30][2]++, 'free'));
    // Don't show upgrade prompts for premium users
    /* istanbul ignore next */
    cov_2kcrqoolfe().s[66]++;
    if (currentTier === 'premium') {
      /* istanbul ignore next */
      cov_2kcrqoolfe().b[31][0]++;
      cov_2kcrqoolfe().s[67]++;
      return {
        show: false
      };
    } else
    /* istanbul ignore next */
    {
      cov_2kcrqoolfe().b[31][1]++;
    }
    // Show upgrade prompts for free tier users
    cov_2kcrqoolfe().s[68]++;
    if (currentTier === 'free') {
      /* istanbul ignore next */
      cov_2kcrqoolfe().b[32][0]++;
      const messages =
      /* istanbul ignore next */
      (cov_2kcrqoolfe().s[69]++, {
        projects: 'Upgrade to Premium for unlimited projects',
        segments: 'Upgrade to Premium for unlimited segments per project',
        exports: 'Upgrade to Premium for high-resolution, watermark-free exports',
        api: 'Upgrade to Premium for API access',
        computational: 'Upgrade to Premium to edit computational properties'
      });
      /* istanbul ignore next */
      cov_2kcrqoolfe().s[70]++;
      return {
        show: true,
        message:
        /* istanbul ignore next */
        (cov_2kcrqoolfe().b[33][0]++, messages[feature]) ||
        /* istanbul ignore next */
        (cov_2kcrqoolfe().b[33][1]++, 'Upgrade to Premium for full access'),
        ctaText: 'Upgrade to Premium'
      };
    } else
    /* istanbul ignore next */
    {
      cov_2kcrqoolfe().b[32][1]++;
    }
    // Show trial expiration warnings for trial users
    cov_2kcrqoolfe().s[71]++;
    if (
    /* istanbul ignore next */
    (cov_2kcrqoolfe().b[35][0]++, currentTier === 'trial') &&
    /* istanbul ignore next */
    (cov_2kcrqoolfe().b[35][1]++, tierStatus?.trial_expires)) {
      /* istanbul ignore next */
      cov_2kcrqoolfe().b[34][0]++;
      const expiryDate =
      /* istanbul ignore next */
      (cov_2kcrqoolfe().s[72]++, new Date(tierStatus.trial_expires));
      const now =
      /* istanbul ignore next */
      (cov_2kcrqoolfe().s[73]++, new Date());
      const diffTime =
      /* istanbul ignore next */
      (cov_2kcrqoolfe().s[74]++, expiryDate.getTime() - now.getTime());
      const daysRemaining =
      /* istanbul ignore next */
      (cov_2kcrqoolfe().s[75]++, Math.ceil(diffTime / (1000 * 60 * 60 * 24)));
      /* istanbul ignore next */
      cov_2kcrqoolfe().s[76]++;
      if (daysRemaining <= 3) {
        /* istanbul ignore next */
        cov_2kcrqoolfe().b[36][0]++;
        cov_2kcrqoolfe().s[77]++;
        return {
          show: true,
          message: `Your trial expires in ${daysRemaining} days. Upgrade to keep Premium features.`,
          ctaText: 'Upgrade Now'
        };
      } else
      /* istanbul ignore next */
      {
        cov_2kcrqoolfe().b[36][1]++;
      }
    } else
    /* istanbul ignore next */
    {
      cov_2kcrqoolfe().b[34][1]++;
    }
    cov_2kcrqoolfe().s[78]++;
    return {
      show: false
    };
  }
}
/* istanbul ignore next */
cov_2kcrqoolfe().s[79]++;
exports.HybridTierEnforcementService = HybridTierEnforcementService;
// Export singleton instance
/* istanbul ignore next */
cov_2kcrqoolfe().s[80]++;
exports.tierEnforcementService = new HybridTierEnforcementService();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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