{"version": 3, "names": ["cov_2kcrqoolfe", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "name", "decl", "loc", "branchMap", "type", "locations", "undefined", "s", "f", "b", "inputSourceMap", "file", "mappings", "names", "sources", "sourcesContent", "version", "_coverageSchema", "coverage", "actualCoverage", "auth_store_1", "require", "HybridTierEnforcementService", "checkProjectLimit", "canPerformAction", "tierStatus", "user", "useAuthStore", "getState", "allowed", "features", "usage", "message", "projects_count", "max_projects", "upgradeRequired", "error", "tier", "checkSegmentLimit", "projectSegments", "segments_count", "max_segments_per_project", "checkExportFeatures", "highRes", "high_res_exports", "watermarked", "watermarked_exports", "isFreeTier", "checkApiAccess", "hasApiAccess", "checkComputationalPropertiesAccess", "getTierInfo", "isTrialActive", "trialDaysRemaining", "trial_expires", "expiryDate", "Date", "now", "diffTime", "getTime", "Math", "max", "ceil", "maxProjects", "limits", "projects", "maxSegments", "segments", "highResExports", "unlimited", "apiAccess", "shouldShowUpgradePrompt", "feature", "show", "currentTier", "messages", "exports", "api", "computational", "ctaText", "daysRemaining", "tierEnforcementService"], "sources": ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\frontend\\lib\\services\\HybridTierEnforcementService.ts"], "sourcesContent": ["/**\r\n * Hybrid Tier Enforcement Service\r\n * \r\n * Integrates with the hybrid authentication system to enforce tier-based\r\n * feature restrictions while maintaining offline-first functionality\r\n */\r\n\r\nimport { useAuthStore } from '@/stores/auth-store'\r\n\r\nexport interface TierCheckResult {\r\n  allowed: boolean\r\n  message?: string\r\n  upgradeRequired?: boolean\r\n}\r\n\r\nexport interface ExportFeatures {\r\n  highRes: boolean\r\n  watermarked: boolean\r\n  message?: string\r\n}\r\n\r\nexport class HybridTierEnforcementService {\r\n  /**\r\n   * Check if user can create a new project\r\n   */\r\n  async checkProjectLimit(): Promise<TierCheckResult> {\r\n    const { canPerformAction, tierStatus, user } = useAuthStore.getState()\r\n    \r\n    try {\r\n      const allowed = await canPerformAction('create_project')\r\n      \r\n      if (!allowed && tierStatus) {\r\n        const { features, usage } = tierStatus\r\n        return {\r\n          allowed: false,\r\n          message: `Project limit reached (${usage.projects_count}/${features.max_projects}). Upgrade to Premium for unlimited projects.`,\r\n          upgradeRequired: true,\r\n        }\r\n      }\r\n      \r\n      return { allowed: true }\r\n    } catch (error) {\r\n      // Fallback to legacy checking for offline mode\r\n      if (user?.tier === 'free') {\r\n        return {\r\n          allowed: false,\r\n          message: 'Free tier allows up to 3 projects. Upgrade to Premium for unlimited projects.',\r\n          upgradeRequired: true,\r\n        }\r\n      }\r\n      \r\n      return { allowed: true }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Check if user can add a segment to a project\r\n   */\r\n  async checkSegmentLimit(projectSegments: number): Promise<TierCheckResult> {\r\n    const { canPerformAction, tierStatus, user } = useAuthStore.getState()\r\n    \r\n    try {\r\n      const allowed = await canPerformAction('add_segment', { segments_count: projectSegments })\r\n      \r\n      if (!allowed && tierStatus) {\r\n        const { features } = tierStatus\r\n        return {\r\n          allowed: false,\r\n          message: `Segment limit reached (${projectSegments}/${features.max_segments_per_project}). Upgrade to Premium for unlimited segments.`,\r\n          upgradeRequired: true,\r\n        }\r\n      }\r\n      \r\n      return { allowed: true }\r\n    } catch (error) {\r\n      // Fallback to legacy checking for offline mode\r\n      if (user?.tier === 'free' && projectSegments >= 25) {\r\n        return {\r\n          allowed: false,\r\n          message: 'Free tier allows up to 25 segments per project. Upgrade to Premium for unlimited segments.',\r\n          upgradeRequired: true,\r\n        }\r\n      }\r\n      \r\n      return { allowed: true }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Check export features available to user\r\n   */\r\n  async checkExportFeatures(): Promise<ExportFeatures> {\r\n    const { canPerformAction, tierStatus, user } = useAuthStore.getState()\r\n    \r\n    try {\r\n      const highRes = await canPerformAction('high_res_export')\r\n      \r\n      if (tierStatus) {\r\n        const { features } = tierStatus\r\n        return {\r\n          highRes: features.high_res_exports,\r\n          watermarked: features.watermarked_exports,\r\n          message: !features.high_res_exports ? 'Upgrade to Premium for high-resolution exports' : undefined,\r\n        }\r\n      }\r\n      \r\n      return {\r\n        highRes,\r\n        watermarked: !highRes,\r\n        message: !highRes ? 'Upgrade to Premium for high-resolution exports' : undefined,\r\n      }\r\n    } catch (error) {\r\n      // Fallback to legacy checking for offline mode\r\n      const isFreeTier = user?.tier === 'free'\r\n      \r\n      return {\r\n        highRes: !isFreeTier,\r\n        watermarked: isFreeTier,\r\n        message: isFreeTier ? 'Upgrade to Premium for high-resolution exports' : undefined,\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Check if user can access API features\r\n   */\r\n  async checkApiAccess(): Promise<TierCheckResult> {\r\n    const { canPerformAction, tierStatus, user } = useAuthStore.getState()\r\n    \r\n    try {\r\n      const allowed = await canPerformAction('api_access')\r\n      \r\n      if (!allowed) {\r\n        return {\r\n          allowed: false,\r\n          message: 'API access is available with Premium subscription only.',\r\n          upgradeRequired: true,\r\n        }\r\n      }\r\n      \r\n      return { allowed: true }\r\n    } catch (error) {\r\n      // Fallback to legacy checking for offline mode\r\n      const hasApiAccess = user?.tier === 'pro' || user?.tier === 'super_admin'\r\n      \r\n      return {\r\n        allowed: hasApiAccess,\r\n        message: !hasApiAccess ? 'API access is available with Premium subscription only.' : undefined,\r\n        upgradeRequired: !hasApiAccess,\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Check if user can edit computational properties\r\n   */\r\n  async checkComputationalPropertiesAccess(): Promise<TierCheckResult> {\r\n    const { user, tierStatus } = useAuthStore.getState()\r\n    \r\n    // Super admin always has access\r\n    if (user?.tier === 'super_admin') {\r\n      return { allowed: true }\r\n    }\r\n    \r\n    try {\r\n      // For hybrid authentication, check tier status\r\n      if (tierStatus) {\r\n        const allowed = tierStatus.tier !== 'free'\r\n        return {\r\n          allowed,\r\n          message: !allowed ? 'Computational properties editing is available with Premium subscription.' : undefined,\r\n          upgradeRequired: !allowed,\r\n        }\r\n      }\r\n      \r\n      // Fallback to legacy checking\r\n      const allowed = user?.tier !== 'free'\r\n      return {\r\n        allowed,\r\n        message: !allowed ? 'Computational properties editing is available with Premium subscription.' : undefined,\r\n        upgradeRequired: !allowed,\r\n      }\r\n    } catch (error) {\r\n      // Default to restricted access\r\n      return {\r\n        allowed: false,\r\n        message: 'Computational properties editing requires Premium subscription.',\r\n        upgradeRequired: true,\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get user's current tier information\r\n   */\r\n  async getTierInfo(): Promise<{\r\n    tier: string\r\n    isTrialActive: boolean\r\n    trialDaysRemaining?: number\r\n    features: {\r\n      maxProjects: number\r\n      maxSegments: number\r\n      highResExports: boolean\r\n      apiAccess: boolean\r\n    }\r\n  }> {\r\n    const { tierStatus, user } = useAuthStore.getState()\r\n    \r\n    if (tierStatus) {\r\n      const isTrialActive = tierStatus.tier === 'trial'\r\n      let trialDaysRemaining: number | undefined\r\n      \r\n      if (isTrialActive && tierStatus.trial_expires) {\r\n        const expiryDate = new Date(tierStatus.trial_expires)\r\n        const now = new Date()\r\n        const diffTime = expiryDate.getTime() - now.getTime()\r\n        trialDaysRemaining = Math.max(0, Math.ceil(diffTime / (1000 * 60 * 60 * 24)))\r\n      }\r\n      \r\n      return {\r\n        tier: tierStatus.tier,\r\n        isTrialActive,\r\n        trialDaysRemaining,\r\n        features: {\r\n          maxProjects: tierStatus.limits.projects || 3,\r\n          maxSegments: tierStatus.limits.segments || 10,\r\n          highResExports: tierStatus.features.unlimited || false,\r\n          apiAccess: tierStatus.features.unlimited || false,\r\n        },\r\n      }\r\n    }\r\n    \r\n    // Fallback to user tier information\r\n    const tier = user?.tier || 'free'\r\n    const isFreeTier = tier === 'free'\r\n    \r\n    return {\r\n      tier,\r\n      isTrialActive: false,\r\n      features: {\r\n        maxProjects: isFreeTier ? 3 : -1,\r\n        maxSegments: isFreeTier ? 25 : -1,\r\n        highResExports: !isFreeTier,\r\n        apiAccess: tier === 'pro' || tier === 'super_admin',\r\n      },\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Check if user needs to upgrade for a specific feature\r\n   */\r\n  async shouldShowUpgradePrompt(feature: string): Promise<{\r\n    show: boolean\r\n    message?: string\r\n    ctaText?: string\r\n  }> {\r\n    const { user, tierStatus } = useAuthStore.getState()\r\n    \r\n    // Never show upgrade prompts for super admin\r\n    if (user?.tier === 'super_admin') {\r\n      return { show: false }\r\n    }\r\n    \r\n    const currentTier = tierStatus?.tier || user?.tier || 'free'\r\n    \r\n    // Don't show upgrade prompts for premium users\r\n    if (currentTier === 'premium') {\r\n      return { show: false }\r\n    }\r\n    \r\n    // Show upgrade prompts for free tier users\r\n    if (currentTier === 'free') {\r\n      const messages = {\r\n        projects: 'Upgrade to Premium for unlimited projects',\r\n        segments: 'Upgrade to Premium for unlimited segments per project',\r\n        exports: 'Upgrade to Premium for high-resolution, watermark-free exports',\r\n        api: 'Upgrade to Premium for API access',\r\n        computational: 'Upgrade to Premium to edit computational properties',\r\n      }\r\n      \r\n      return {\r\n        show: true,\r\n        message: messages[feature as keyof typeof messages] || 'Upgrade to Premium for full access',\r\n        ctaText: 'Upgrade to Premium',\r\n      }\r\n    }\r\n    \r\n    // Show trial expiration warnings for trial users\r\n    if (currentTier === 'trial' && tierStatus?.trial_expires) {\r\n      const expiryDate = new Date(tierStatus.trial_expires)\r\n      const now = new Date()\r\n      const diffTime = expiryDate.getTime() - now.getTime()\r\n      const daysRemaining = Math.ceil(diffTime / (1000 * 60 * 60 * 24))\r\n      \r\n      if (daysRemaining <= 3) {\r\n        return {\r\n          show: true,\r\n          message: `Your trial expires in ${daysRemaining} days. Upgrade to keep Premium features.`,\r\n          ctaText: 'Upgrade Now',\r\n        }\r\n      }\r\n    }\r\n    \r\n    return { show: false }\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const tierEnforcementService = new HybridTierEnforcementService()\r\n"], "mappings": ";;AAAA;;;;;;AAAA;AAAA,SAAAA,eAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;MAAA;QAAAC,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAO,SAAA;MAAA;QAAAD,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;IAAA;IAAAW,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,cAAA;MAAAC,IAAA;MAAAC,QAAA;MAAAC,KAAA;MAAAC,OAAA;MAAAC,cAAA;MAAAC,OAAA;IAAA;IAAAC,eAAA;IAAA5B,IAAA;EAAA;EAAA,IAAA6B,QAAA,GAAA5B,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAA0B,QAAA,CAAA9B,IAAA,KAAA8B,QAAA,CAAA9B,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAA6B,QAAA,CAAA9B,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAA0B,cAAA,GAAAD,QAAA,CAAA9B,IAAA;EAAA;IA0BI;IAAAD,cAAA,YAAAA,CAAA;MAAA,OAAAgC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAhC,cAAA;AAAAA,cAAA,GAAAoB,CAAA;;;;;;;AAnBJ,MAAAa,YAAA;AAAA;AAAA,CAAAjC,cAAA,GAAAoB,CAAA,OAAAc,OAAA;AAcA,MAAaC,4BAA4B;EACvC;;;EAGA,MAAMC,iBAAiBA,CAAA;IAAA;IAAApC,cAAA,GAAAqB,CAAA;IACrB,MAAM;MAAEgB,gBAAgB;MAAEC,UAAU;MAAEC;IAAI,CAAE;IAAA;IAAA,CAAAvC,cAAA,GAAAoB,CAAA,OAAGa,YAAA,CAAAO,YAAY,CAACC,QAAQ,EAAE;IAAA;IAAAzC,cAAA,GAAAoB,CAAA;IAEtE,IAAI;MACF,MAAMsB,OAAO;MAAA;MAAA,CAAA1C,cAAA,GAAAoB,CAAA,OAAG,MAAMiB,gBAAgB,CAAC,gBAAgB,CAAC;MAAA;MAAArC,cAAA,GAAAoB,CAAA;MAExD;MAAI;MAAA,CAAApB,cAAA,GAAAsB,CAAA,WAACoB,OAAO;MAAA;MAAA,CAAA1C,cAAA,GAAAsB,CAAA,UAAIgB,UAAU,GAAE;QAAA;QAAAtC,cAAA,GAAAsB,CAAA;QAC1B,MAAM;UAAEqB,QAAQ;UAAEC;QAAK,CAAE;QAAA;QAAA,CAAA5C,cAAA,GAAAoB,CAAA,OAAGkB,UAAU;QAAA;QAAAtC,cAAA,GAAAoB,CAAA;QACtC,OAAO;UACLsB,OAAO,EAAE,KAAK;UACdG,OAAO,EAAE,0BAA0BD,KAAK,CAACE,cAAc,IAAIH,QAAQ,CAACI,YAAY,+CAA+C;UAC/HC,eAAe,EAAE;SAClB;MACH,CAAC;MAAA;MAAA;QAAAhD,cAAA,GAAAsB,CAAA;MAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAED,OAAO;QAAEsB,OAAO,EAAE;MAAI,CAAE;IAC1B,CAAC,CAAC,OAAOO,KAAK,EAAE;MAAA;MAAAjD,cAAA,GAAAoB,CAAA;MACd;MACA,IAAImB,IAAI,EAAEW,IAAI,KAAK,MAAM,EAAE;QAAA;QAAAlD,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACzB,OAAO;UACLsB,OAAO,EAAE,KAAK;UACdG,OAAO,EAAE,+EAA+E;UACxFG,eAAe,EAAE;SAClB;MACH,CAAC;MAAA;MAAA;QAAAhD,cAAA,GAAAsB,CAAA;MAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAED,OAAO;QAAEsB,OAAO,EAAE;MAAI,CAAE;IAC1B;EACF;EAEA;;;EAGA,MAAMS,iBAAiBA,CAACC,eAAuB;IAAA;IAAApD,cAAA,GAAAqB,CAAA;IAC7C,MAAM;MAAEgB,gBAAgB;MAAEC,UAAU;MAAEC;IAAI,CAAE;IAAA;IAAA,CAAAvC,cAAA,GAAAoB,CAAA,QAAGa,YAAA,CAAAO,YAAY,CAACC,QAAQ,EAAE;IAAA;IAAAzC,cAAA,GAAAoB,CAAA;IAEtE,IAAI;MACF,MAAMsB,OAAO;MAAA;MAAA,CAAA1C,cAAA,GAAAoB,CAAA,QAAG,MAAMiB,gBAAgB,CAAC,aAAa,EAAE;QAAEgB,cAAc,EAAED;MAAe,CAAE,CAAC;MAAA;MAAApD,cAAA,GAAAoB,CAAA;MAE1F;MAAI;MAAA,CAAApB,cAAA,GAAAsB,CAAA,WAACoB,OAAO;MAAA;MAAA,CAAA1C,cAAA,GAAAsB,CAAA,UAAIgB,UAAU,GAAE;QAAA;QAAAtC,cAAA,GAAAsB,CAAA;QAC1B,MAAM;UAAEqB;QAAQ,CAAE;QAAA;QAAA,CAAA3C,cAAA,GAAAoB,CAAA,QAAGkB,UAAU;QAAA;QAAAtC,cAAA,GAAAoB,CAAA;QAC/B,OAAO;UACLsB,OAAO,EAAE,KAAK;UACdG,OAAO,EAAE,0BAA0BO,eAAe,IAAIT,QAAQ,CAACW,wBAAwB,+CAA+C;UACtIN,eAAe,EAAE;SAClB;MACH,CAAC;MAAA;MAAA;QAAAhD,cAAA,GAAAsB,CAAA;MAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAED,OAAO;QAAEsB,OAAO,EAAE;MAAI,CAAE;IAC1B,CAAC,CAAC,OAAOO,KAAK,EAAE;MAAA;MAAAjD,cAAA,GAAAoB,CAAA;MACd;MACA;MAAI;MAAA,CAAApB,cAAA,GAAAsB,CAAA,UAAAiB,IAAI,EAAEW,IAAI,KAAK,MAAM;MAAA;MAAA,CAAAlD,cAAA,GAAAsB,CAAA,UAAI8B,eAAe,IAAI,EAAE,GAAE;QAAA;QAAApD,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAClD,OAAO;UACLsB,OAAO,EAAE,KAAK;UACdG,OAAO,EAAE,4FAA4F;UACrGG,eAAe,EAAE;SAClB;MACH,CAAC;MAAA;MAAA;QAAAhD,cAAA,GAAAsB,CAAA;MAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAED,OAAO;QAAEsB,OAAO,EAAE;MAAI,CAAE;IAC1B;EACF;EAEA;;;EAGA,MAAMa,mBAAmBA,CAAA;IAAA;IAAAvD,cAAA,GAAAqB,CAAA;IACvB,MAAM;MAAEgB,gBAAgB;MAAEC,UAAU;MAAEC;IAAI,CAAE;IAAA;IAAA,CAAAvC,cAAA,GAAAoB,CAAA,QAAGa,YAAA,CAAAO,YAAY,CAACC,QAAQ,EAAE;IAAA;IAAAzC,cAAA,GAAAoB,CAAA;IAEtE,IAAI;MACF,MAAMoC,OAAO;MAAA;MAAA,CAAAxD,cAAA,GAAAoB,CAAA,QAAG,MAAMiB,gBAAgB,CAAC,iBAAiB,CAAC;MAAA;MAAArC,cAAA,GAAAoB,CAAA;MAEzD,IAAIkB,UAAU,EAAE;QAAA;QAAAtC,cAAA,GAAAsB,CAAA;QACd,MAAM;UAAEqB;QAAQ,CAAE;QAAA;QAAA,CAAA3C,cAAA,GAAAoB,CAAA,QAAGkB,UAAU;QAAA;QAAAtC,cAAA,GAAAoB,CAAA;QAC/B,OAAO;UACLoC,OAAO,EAAEb,QAAQ,CAACc,gBAAgB;UAClCC,WAAW,EAAEf,QAAQ,CAACgB,mBAAmB;UACzCd,OAAO,EAAE,CAACF,QAAQ,CAACc,gBAAgB;UAAA;UAAA,CAAAzD,cAAA,GAAAsB,CAAA,UAAG,gDAAgD;UAAA;UAAA,CAAAtB,cAAA,GAAAsB,CAAA,UAAGH,SAAS;SACnG;MACH,CAAC;MAAA;MAAA;QAAAnB,cAAA,GAAAsB,CAAA;MAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAED,OAAO;QACLoC,OAAO;QACPE,WAAW,EAAE,CAACF,OAAO;QACrBX,OAAO,EAAE,CAACW,OAAO;QAAA;QAAA,CAAAxD,cAAA,GAAAsB,CAAA,UAAG,gDAAgD;QAAA;QAAA,CAAAtB,cAAA,GAAAsB,CAAA,UAAGH,SAAS;OACjF;IACH,CAAC,CAAC,OAAO8B,KAAK,EAAE;MACd;MACA,MAAMW,UAAU;MAAA;MAAA,CAAA5D,cAAA,GAAAoB,CAAA,QAAGmB,IAAI,EAAEW,IAAI,KAAK,MAAM;MAAA;MAAAlD,cAAA,GAAAoB,CAAA;MAExC,OAAO;QACLoC,OAAO,EAAE,CAACI,UAAU;QACpBF,WAAW,EAAEE,UAAU;QACvBf,OAAO,EAAEe,UAAU;QAAA;QAAA,CAAA5D,cAAA,GAAAsB,CAAA,WAAG,gDAAgD;QAAA;QAAA,CAAAtB,cAAA,GAAAsB,CAAA,WAAGH,SAAS;OACnF;IACH;EACF;EAEA;;;EAGA,MAAM0C,cAAcA,CAAA;IAAA;IAAA7D,cAAA,GAAAqB,CAAA;IAClB,MAAM;MAAEgB,gBAAgB;MAAEC,UAAU;MAAEC;IAAI,CAAE;IAAA;IAAA,CAAAvC,cAAA,GAAAoB,CAAA,QAAGa,YAAA,CAAAO,YAAY,CAACC,QAAQ,EAAE;IAAA;IAAAzC,cAAA,GAAAoB,CAAA;IAEtE,IAAI;MACF,MAAMsB,OAAO;MAAA;MAAA,CAAA1C,cAAA,GAAAoB,CAAA,QAAG,MAAMiB,gBAAgB,CAAC,YAAY,CAAC;MAAA;MAAArC,cAAA,GAAAoB,CAAA;MAEpD,IAAI,CAACsB,OAAO,EAAE;QAAA;QAAA1C,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACZ,OAAO;UACLsB,OAAO,EAAE,KAAK;UACdG,OAAO,EAAE,yDAAyD;UAClEG,eAAe,EAAE;SAClB;MACH,CAAC;MAAA;MAAA;QAAAhD,cAAA,GAAAsB,CAAA;MAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAED,OAAO;QAAEsB,OAAO,EAAE;MAAI,CAAE;IAC1B,CAAC,CAAC,OAAOO,KAAK,EAAE;MACd;MACA,MAAMa,YAAY;MAAA;MAAA,CAAA9D,cAAA,GAAAoB,CAAA;MAAG;MAAA,CAAApB,cAAA,GAAAsB,CAAA,WAAAiB,IAAI,EAAEW,IAAI,KAAK,KAAK;MAAA;MAAA,CAAAlD,cAAA,GAAAsB,CAAA,WAAIiB,IAAI,EAAEW,IAAI,KAAK,aAAa;MAAA;MAAAlD,cAAA,GAAAoB,CAAA;MAEzE,OAAO;QACLsB,OAAO,EAAEoB,YAAY;QACrBjB,OAAO,EAAE,CAACiB,YAAY;QAAA;QAAA,CAAA9D,cAAA,GAAAsB,CAAA,WAAG,yDAAyD;QAAA;QAAA,CAAAtB,cAAA,GAAAsB,CAAA,WAAGH,SAAS;QAC9F6B,eAAe,EAAE,CAACc;OACnB;IACH;EACF;EAEA;;;EAGA,MAAMC,kCAAkCA,CAAA;IAAA;IAAA/D,cAAA,GAAAqB,CAAA;IACtC,MAAM;MAAEkB,IAAI;MAAED;IAAU,CAAE;IAAA;IAAA,CAAAtC,cAAA,GAAAoB,CAAA,QAAGa,YAAA,CAAAO,YAAY,CAACC,QAAQ,EAAE;IAEpD;IAAA;IAAAzC,cAAA,GAAAoB,CAAA;IACA,IAAImB,IAAI,EAAEW,IAAI,KAAK,aAAa,EAAE;MAAA;MAAAlD,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAChC,OAAO;QAAEsB,OAAO,EAAE;MAAI,CAAE;IAC1B,CAAC;IAAA;IAAA;MAAA1C,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAED,IAAI;MAAA;MAAApB,cAAA,GAAAoB,CAAA;MACF;MACA,IAAIkB,UAAU,EAAE;QAAA;QAAAtC,cAAA,GAAAsB,CAAA;QACd,MAAMoB,OAAO;QAAA;QAAA,CAAA1C,cAAA,GAAAoB,CAAA,QAAGkB,UAAU,CAACY,IAAI,KAAK,MAAM;QAAA;QAAAlD,cAAA,GAAAoB,CAAA;QAC1C,OAAO;UACLsB,OAAO;UACPG,OAAO,EAAE,CAACH,OAAO;UAAA;UAAA,CAAA1C,cAAA,GAAAsB,CAAA,WAAG,0EAA0E;UAAA;UAAA,CAAAtB,cAAA,GAAAsB,CAAA,WAAGH,SAAS;UAC1G6B,eAAe,EAAE,CAACN;SACnB;MACH,CAAC;MAAA;MAAA;QAAA1C,cAAA,GAAAsB,CAAA;MAAA;MAED;MACA,MAAMoB,OAAO;MAAA;MAAA,CAAA1C,cAAA,GAAAoB,CAAA,QAAGmB,IAAI,EAAEW,IAAI,KAAK,MAAM;MAAA;MAAAlD,cAAA,GAAAoB,CAAA;MACrC,OAAO;QACLsB,OAAO;QACPG,OAAO,EAAE,CAACH,OAAO;QAAA;QAAA,CAAA1C,cAAA,GAAAsB,CAAA,WAAG,0EAA0E;QAAA;QAAA,CAAAtB,cAAA,GAAAsB,CAAA,WAAGH,SAAS;QAC1G6B,eAAe,EAAE,CAACN;OACnB;IACH,CAAC,CAAC,OAAOO,KAAK,EAAE;MAAA;MAAAjD,cAAA,GAAAoB,CAAA;MACd;MACA,OAAO;QACLsB,OAAO,EAAE,KAAK;QACdG,OAAO,EAAE,iEAAiE;QAC1EG,eAAe,EAAE;OAClB;IACH;EACF;EAEA;;;EAGA,MAAMgB,WAAWA,CAAA;IAAA;IAAAhE,cAAA,GAAAqB,CAAA;IAWf,MAAM;MAAEiB,UAAU;MAAEC;IAAI,CAAE;IAAA;IAAA,CAAAvC,cAAA,GAAAoB,CAAA,QAAGa,YAAA,CAAAO,YAAY,CAACC,QAAQ,EAAE;IAAA;IAAAzC,cAAA,GAAAoB,CAAA;IAEpD,IAAIkB,UAAU,EAAE;MAAA;MAAAtC,cAAA,GAAAsB,CAAA;MACd,MAAM2C,aAAa;MAAA;MAAA,CAAAjE,cAAA,GAAAoB,CAAA,QAAGkB,UAAU,CAACY,IAAI,KAAK,OAAO;MACjD,IAAIgB,kBAAsC;MAAA;MAAAlE,cAAA,GAAAoB,CAAA;MAE1C;MAAI;MAAA,CAAApB,cAAA,GAAAsB,CAAA,WAAA2C,aAAa;MAAA;MAAA,CAAAjE,cAAA,GAAAsB,CAAA,WAAIgB,UAAU,CAAC6B,aAAa,GAAE;QAAA;QAAAnE,cAAA,GAAAsB,CAAA;QAC7C,MAAM8C,UAAU;QAAA;QAAA,CAAApE,cAAA,GAAAoB,CAAA,QAAG,IAAIiD,IAAI,CAAC/B,UAAU,CAAC6B,aAAa,CAAC;QACrD,MAAMG,GAAG;QAAA;QAAA,CAAAtE,cAAA,GAAAoB,CAAA,QAAG,IAAIiD,IAAI,EAAE;QACtB,MAAME,QAAQ;QAAA;QAAA,CAAAvE,cAAA,GAAAoB,CAAA,QAAGgD,UAAU,CAACI,OAAO,EAAE,GAAGF,GAAG,CAACE,OAAO,EAAE;QAAA;QAAAxE,cAAA,GAAAoB,CAAA;QACrD8C,kBAAkB,GAAGO,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,IAAI,CAACJ,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;MAC/E,CAAC;MAAA;MAAA;QAAAvE,cAAA,GAAAsB,CAAA;MAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAED,OAAO;QACL8B,IAAI,EAAEZ,UAAU,CAACY,IAAI;QACrBe,aAAa;QACbC,kBAAkB;QAClBvB,QAAQ,EAAE;UACRiC,WAAW;UAAE;UAAA,CAAA5E,cAAA,GAAAsB,CAAA,WAAAgB,UAAU,CAACuC,MAAM,CAACC,QAAQ;UAAA;UAAA,CAAA9E,cAAA,GAAAsB,CAAA,WAAI,CAAC;UAC5CyD,WAAW;UAAE;UAAA,CAAA/E,cAAA,GAAAsB,CAAA,WAAAgB,UAAU,CAACuC,MAAM,CAACG,QAAQ;UAAA;UAAA,CAAAhF,cAAA,GAAAsB,CAAA,WAAI,EAAE;UAC7C2D,cAAc;UAAE;UAAA,CAAAjF,cAAA,GAAAsB,CAAA,WAAAgB,UAAU,CAACK,QAAQ,CAACuC,SAAS;UAAA;UAAA,CAAAlF,cAAA,GAAAsB,CAAA,WAAI,KAAK;UACtD6D,SAAS;UAAE;UAAA,CAAAnF,cAAA,GAAAsB,CAAA,WAAAgB,UAAU,CAACK,QAAQ,CAACuC,SAAS;UAAA;UAAA,CAAAlF,cAAA,GAAAsB,CAAA,WAAI,KAAK;;OAEpD;IACH,CAAC;IAAA;IAAA;MAAAtB,cAAA,GAAAsB,CAAA;IAAA;IAED;IACA,MAAM4B,IAAI;IAAA;IAAA,CAAAlD,cAAA,GAAAoB,CAAA;IAAG;IAAA,CAAApB,cAAA,GAAAsB,CAAA,WAAAiB,IAAI,EAAEW,IAAI;IAAA;IAAA,CAAAlD,cAAA,GAAAsB,CAAA,WAAI,MAAM;IACjC,MAAMsC,UAAU;IAAA;IAAA,CAAA5D,cAAA,GAAAoB,CAAA,QAAG8B,IAAI,KAAK,MAAM;IAAA;IAAAlD,cAAA,GAAAoB,CAAA;IAElC,OAAO;MACL8B,IAAI;MACJe,aAAa,EAAE,KAAK;MACpBtB,QAAQ,EAAE;QACRiC,WAAW,EAAEhB,UAAU;QAAA;QAAA,CAAA5D,cAAA,GAAAsB,CAAA,WAAG,CAAC;QAAA;QAAA,CAAAtB,cAAA,GAAAsB,CAAA,WAAG,CAAC,CAAC;QAChCyD,WAAW,EAAEnB,UAAU;QAAA;QAAA,CAAA5D,cAAA,GAAAsB,CAAA,WAAG,EAAE;QAAA;QAAA,CAAAtB,cAAA,GAAAsB,CAAA,WAAG,CAAC,CAAC;QACjC2D,cAAc,EAAE,CAACrB,UAAU;QAC3BuB,SAAS;QAAE;QAAA,CAAAnF,cAAA,GAAAsB,CAAA,WAAA4B,IAAI,KAAK,KAAK;QAAA;QAAA,CAAAlD,cAAA,GAAAsB,CAAA,WAAI4B,IAAI,KAAK,aAAa;;KAEtD;EACH;EAEA;;;EAGA,MAAMkC,uBAAuBA,CAACC,OAAe;IAAA;IAAArF,cAAA,GAAAqB,CAAA;IAK3C,MAAM;MAAEkB,IAAI;MAAED;IAAU,CAAE;IAAA;IAAA,CAAAtC,cAAA,GAAAoB,CAAA,QAAGa,YAAA,CAAAO,YAAY,CAACC,QAAQ,EAAE;IAEpD;IAAA;IAAAzC,cAAA,GAAAoB,CAAA;IACA,IAAImB,IAAI,EAAEW,IAAI,KAAK,aAAa,EAAE;MAAA;MAAAlD,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAChC,OAAO;QAAEkE,IAAI,EAAE;MAAK,CAAE;IACxB,CAAC;IAAA;IAAA;MAAAtF,cAAA,GAAAsB,CAAA;IAAA;IAED,MAAMiE,WAAW;IAAA;IAAA,CAAAvF,cAAA,GAAAoB,CAAA;IAAG;IAAA,CAAApB,cAAA,GAAAsB,CAAA,WAAAgB,UAAU,EAAEY,IAAI;IAAA;IAAA,CAAAlD,cAAA,GAAAsB,CAAA,WAAIiB,IAAI,EAAEW,IAAI;IAAA;IAAA,CAAAlD,cAAA,GAAAsB,CAAA,WAAI,MAAM;IAE5D;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IACA,IAAImE,WAAW,KAAK,SAAS,EAAE;MAAA;MAAAvF,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAC7B,OAAO;QAAEkE,IAAI,EAAE;MAAK,CAAE;IACxB,CAAC;IAAA;IAAA;MAAAtF,cAAA,GAAAsB,CAAA;IAAA;IAED;IAAAtB,cAAA,GAAAoB,CAAA;IACA,IAAImE,WAAW,KAAK,MAAM,EAAE;MAAA;MAAAvF,cAAA,GAAAsB,CAAA;MAC1B,MAAMkE,QAAQ;MAAA;MAAA,CAAAxF,cAAA,GAAAoB,CAAA,QAAG;QACf0D,QAAQ,EAAE,2CAA2C;QACrDE,QAAQ,EAAE,uDAAuD;QACjES,OAAO,EAAE,gEAAgE;QACzEC,GAAG,EAAE,mCAAmC;QACxCC,aAAa,EAAE;OAChB;MAAA;MAAA3F,cAAA,GAAAoB,CAAA;MAED,OAAO;QACLkE,IAAI,EAAE,IAAI;QACVzC,OAAO;QAAE;QAAA,CAAA7C,cAAA,GAAAsB,CAAA,WAAAkE,QAAQ,CAACH,OAAgC,CAAC;QAAA;QAAA,CAAArF,cAAA,GAAAsB,CAAA,WAAI,oCAAoC;QAC3FsE,OAAO,EAAE;OACV;IACH,CAAC;IAAA;IAAA;MAAA5F,cAAA,GAAAsB,CAAA;IAAA;IAED;IAAAtB,cAAA,GAAAoB,CAAA;IACA;IAAI;IAAA,CAAApB,cAAA,GAAAsB,CAAA,WAAAiE,WAAW,KAAK,OAAO;IAAA;IAAA,CAAAvF,cAAA,GAAAsB,CAAA,WAAIgB,UAAU,EAAE6B,aAAa,GAAE;MAAA;MAAAnE,cAAA,GAAAsB,CAAA;MACxD,MAAM8C,UAAU;MAAA;MAAA,CAAApE,cAAA,GAAAoB,CAAA,QAAG,IAAIiD,IAAI,CAAC/B,UAAU,CAAC6B,aAAa,CAAC;MACrD,MAAMG,GAAG;MAAA;MAAA,CAAAtE,cAAA,GAAAoB,CAAA,QAAG,IAAIiD,IAAI,EAAE;MACtB,MAAME,QAAQ;MAAA;MAAA,CAAAvE,cAAA,GAAAoB,CAAA,QAAGgD,UAAU,CAACI,OAAO,EAAE,GAAGF,GAAG,CAACE,OAAO,EAAE;MACrD,MAAMqB,aAAa;MAAA;MAAA,CAAA7F,cAAA,GAAAoB,CAAA,QAAGqD,IAAI,CAACE,IAAI,CAACJ,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;MAAA;MAAAvE,cAAA,GAAAoB,CAAA;MAEjE,IAAIyE,aAAa,IAAI,CAAC,EAAE;QAAA;QAAA7F,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACtB,OAAO;UACLkE,IAAI,EAAE,IAAI;UACVzC,OAAO,EAAE,yBAAyBgD,aAAa,0CAA0C;UACzFD,OAAO,EAAE;SACV;MACH,CAAC;MAAA;MAAA;QAAA5F,cAAA,GAAAsB,CAAA;MAAA;IACH,CAAC;IAAA;IAAA;MAAAtB,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAED,OAAO;MAAEkE,IAAI,EAAE;IAAK,CAAE;EACxB;;AACD;AAAAtF,cAAA,GAAAoB,CAAA;AA5RDqE,OAAA,CAAAtD,4BAAA,GAAAA,4BAAA;AA8RA;AAAA;AAAAnC,cAAA,GAAAoB,CAAA;AACaqE,OAAA,CAAAK,sBAAsB,GAAG,IAAI3D,4BAA4B,EAAE", "ignoreList": []}