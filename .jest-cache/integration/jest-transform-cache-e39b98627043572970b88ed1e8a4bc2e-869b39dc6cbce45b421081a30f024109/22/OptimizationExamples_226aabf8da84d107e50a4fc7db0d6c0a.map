{"version": 3, "names": ["cov_jcfe2znb1", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "name", "decl", "loc", "branchMap", "type", "locations", "undefined", "s", "f", "b", "inputSourceMap", "file", "mappings", "names", "sources", "sourcesContent", "version", "_coverageSchema", "coverage", "actualCoverage", "exports", "example1_DuctSizingOptimization", "ductSizingOptimization", "example2_MultiObjectiveOptimization", "multiObjectiveOptimization", "example3_IntegrationWithExistingComponents", "integratedOptimization", "runAllOptimizationExamples", "SystemOptimizationTypes_1", "require", "SystemOptimizationEngine_1", "GeneticAlgorithm_1", "MultiObjectiveOptimizationFramework_1", "console", "log", "systemConfig", "id", "description", "systemType", "designAirflow", "designPressure", "operatingConditions", "temperature", "humidity", "elevation", "components", "specifications", "maxPressure", "maxAirflow", "efficiency", "length", "material", "roughness", "variables", "bounds", "minimum", "maximum", "units", "currentValue", "constraints", "evaluationFunction", "vars", "fanSpeed", "find", "v", "deliveredAirflow", "mainDiameter", "branchDiameter", "velocity", "Math", "PI", "mainLoss", "branchLoss", "totalLoss", "objectiveFunction", "problem", "systemConfiguration", "objectives", "objective", "OptimizationObjective", "MINIMIZE_PRESSURE_LOSS", "weight", "aggregationMethod", "algorithmSettings", "algorithm", "OptimizationAlgorithm", "GENETIC_ALGORITHM", "parameters", "populationSize", "maxIterations", "crossoverRate", "mutationRate", "parallelization", "enabled", "convergenceCriteria", "toleranceValue", "stagnationLimit", "ga", "GeneticAlgorithm", "maxGenerations", "eliteSize", "constraintHandling", "penaltyCoefficient", "constraintFunctions", "map", "c", "result", "optimize", "status", "bestSolution", "fitness", "toFixed", "statistics", "executionTime", "totalEvaluations", "powerRating", "discreteValues", "diameter", "noiseLevel", "log10", "pressureLossObjective", "roughnessFactors", "roughnessFactor", "pressureLoss", "energyConsumptionObjective", "fanEfficiency", "fan<PERSON>ower", "annualEnergyConsumption", "totalCostObjective", "insulationThickness", "materialCosts", "efficiencyPremiums", "materialCost", "ductSurfaceArea", "ductCost", "insulationCost", "fanPremium", "energyConsumption", "operatingCost", "MINIMIZE_ENERGY_CONSUMPTION", "MINIMIZE_TOTAL_COST", "NSGA_II", "moFramework", "MultiObjectiveOptimizationFramework", "paretoSettings", "maxSolutions", "diversityThreshold", "convergenceThreshold", "hypervolume", "referencePoint", "spacing", "targetSpacing", "diversityMaintenance", "archiveSize", "optimizeMultiObjective", "analysis", "paretoFront", "solutions", "tradeoffAnalysis", "kneePoints", "inletDiameter", "outletWidth", "outletHeight", "transitionType", "ductLoss", "transitionLosses", "fitting<PERSON>oss", "velocityPressure", "PARTICLE_SWARM", "inertiaWeight", "accelerationCoefficients", "SystemOptimizationEngine", "optimizeSystem", "error"], "sources": ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\examples\\OptimizationExamples.ts"], "sourcesContent": ["/**\r\n * Optimization Framework Usage Examples\r\n * \r\n * Demonstrates practical usage of the System Optimization Framework with real HVAC scenarios:\r\n * - Single-objective optimization examples\r\n * - Multi-objective optimization with trade-off analysis\r\n * - Integration with existing Phase 1/2/3 Priority 1 components\r\n * - Real-world HVAC system optimization scenarios\r\n * - Best practices and common patterns\r\n * \r\n * @version 3.0.0\r\n * <AUTHOR> Suite Development Team\r\n */\r\n\r\nimport {\r\n  OptimizationProblem,\r\n  OptimizationResult,\r\n  OptimizationAlgorithm,\r\n  OptimizationObjective,\r\n  OptimizationVariable,\r\n  OptimizationConstraint,\r\n  SystemConfiguration,\r\n  ObjectiveFunctionType,\r\n  ConstraintFunctionType\r\n} from '../types/SystemOptimizationTypes';\r\n\r\nimport { SystemOptimizationEngine } from '../SystemOptimizationEngine';\r\nimport { GeneticAlgorithm } from '../algorithms/GeneticAlgorithm';\r\nimport { MultiObjectiveOptimizationFramework } from '../MultiObjectiveOptimizationFramework';\r\n\r\n/**\r\n * Example 1: Single-Objective Duct Sizing Optimization\r\n * \r\n * Optimize duct diameter to minimize pressure loss while meeting airflow requirements\r\n */\r\nexport async function example1_DuctSizingOptimization(): Promise<OptimizationResult> {\r\n  console.log('=== Example 1: Duct Sizing Optimization ===');\r\n  \r\n  // Define system configuration\r\n  const systemConfig: SystemConfiguration = {\r\n    id: 'office_building_hvac',\r\n    name: 'Office Building HVAC System',\r\n    description: 'Main supply air system for 50,000 sq ft office building',\r\n    systemType: 'supply_air',\r\n    designAirflow: 25000, // CFM\r\n    designPressure: 4.0,   // inWC\r\n    operatingConditions: {\r\n      temperature: 72,     // °F\r\n      humidity: 45,        // %RH\r\n      elevation: 1000      // ft above sea level\r\n    },\r\n    components: [\r\n      {\r\n        id: 'main_fan',\r\n        type: 'fan',\r\n        specifications: {\r\n          maxPressure: 8.0,\r\n          maxAirflow: 30000,\r\n          efficiency: 0.82\r\n        }\r\n      },\r\n      {\r\n        id: 'main_duct',\r\n        type: 'duct',\r\n        specifications: {\r\n          length: 200,\r\n          material: 'galvanized_steel',\r\n          roughness: 0.0015\r\n        }\r\n      }\r\n    ]\r\n  };\r\n\r\n  // Define optimization variables\r\n  const variables: OptimizationVariable[] = [\r\n    {\r\n      id: 'main_duct_diameter',\r\n      name: 'Main Duct Diameter',\r\n      description: 'Diameter of the main supply duct',\r\n      type: 'continuous',\r\n      bounds: { minimum: 18, maximum: 36 },\r\n      units: 'inches',\r\n      currentValue: 24\r\n    },\r\n    {\r\n      id: 'branch_duct_diameter',\r\n      name: 'Branch Duct Diameter',\r\n      description: 'Diameter of branch ducts',\r\n      type: 'continuous',\r\n      bounds: { minimum: 8, maximum: 20 },\r\n      units: 'inches',\r\n      currentValue: 12\r\n    },\r\n    {\r\n      id: 'fan_speed',\r\n      name: 'Fan Speed',\r\n      description: 'Fan operating speed percentage',\r\n      type: 'continuous',\r\n      bounds: { minimum: 60, maximum: 100 },\r\n      units: 'percent',\r\n      currentValue: 85\r\n    }\r\n  ];\r\n\r\n  // Define constraints\r\n  const constraints: OptimizationConstraint[] = [\r\n    {\r\n      id: 'airflow_requirement',\r\n      name: 'Minimum Airflow Requirement',\r\n      description: 'System must deliver at least design airflow',\r\n      type: 'inequality',\r\n      bounds: { minimum: 25000 },\r\n      units: 'CFM',\r\n      evaluationFunction: (vars: OptimizationVariable[]) => {\r\n        const fanSpeed = vars.find(v => v.id === 'fan_speed')?.currentValue as number || 85;\r\n        const deliveredAirflow = (fanSpeed / 100) * 30000; // Simplified model\r\n        return 25000 - deliveredAirflow; // Violation if < 0\r\n      }\r\n    },\r\n    {\r\n      id: 'max_pressure_loss',\r\n      name: 'Maximum System Pressure Loss',\r\n      description: 'Total pressure loss must not exceed fan capacity',\r\n      type: 'inequality',\r\n      bounds: { maximum: 6.0 },\r\n      units: 'inWC',\r\n      evaluationFunction: (vars: OptimizationVariable[]) => {\r\n        const mainDiameter = vars.find(v => v.id === 'main_duct_diameter')?.currentValue as number || 24;\r\n        const branchDiameter = vars.find(v => v.id === 'branch_duct_diameter')?.currentValue as number || 12;\r\n        const fanSpeed = vars.find(v => v.id === 'fan_speed')?.currentValue as number || 85;\r\n        \r\n        // Simplified pressure loss calculation\r\n        const velocity = (fanSpeed / 100) * 25000 / (Math.PI * (mainDiameter / 12) ** 2 / 4 * 144);\r\n        const mainLoss = 0.02 * 200 * (velocity / 4005) ** 1.85 / (mainDiameter / 12) ** 1.23;\r\n        const branchLoss = 0.015 * 50 * (velocity / 4005) ** 1.85 / (branchDiameter / 12) ** 1.23;\r\n        \r\n        const totalLoss = mainLoss + branchLoss + 1.5; // Include fittings\r\n        return totalLoss - 6.0; // Violation if > 0\r\n      }\r\n    }\r\n  ];\r\n\r\n  // Define objective function (minimize pressure loss)\r\n  const objectiveFunction: ObjectiveFunctionType = (vars: OptimizationVariable[]): number => {\r\n    const mainDiameter = vars.find(v => v.id === 'main_duct_diameter')?.currentValue as number || 24;\r\n    const branchDiameter = vars.find(v => v.id === 'branch_duct_diameter')?.currentValue as number || 12;\r\n    const fanSpeed = vars.find(v => v.id === 'fan_speed')?.currentValue as number || 85;\r\n    \r\n    // Calculate total system pressure loss\r\n    const velocity = (fanSpeed / 100) * 25000 / (Math.PI * (mainDiameter / 12) ** 2 / 4 * 144);\r\n    const mainLoss = 0.02 * 200 * (velocity / 4005) ** 1.85 / (mainDiameter / 12) ** 1.23;\r\n    const branchLoss = 0.015 * 50 * (velocity / 4005) ** 1.85 / (branchDiameter / 12) ** 1.23;\r\n    \r\n    return mainLoss + branchLoss + 1.5; // Total pressure loss including fittings\r\n  };\r\n\r\n  // Create optimization problem\r\n  const problem: OptimizationProblem = {\r\n    id: 'duct_sizing_optimization',\r\n    name: 'Duct Sizing Optimization',\r\n    description: 'Optimize duct diameters to minimize pressure loss',\r\n    systemConfiguration: systemConfig,\r\n    variables,\r\n    objectives: {\r\n      objectives: [{\r\n        id: 'minimize_pressure_loss',\r\n        objective: OptimizationObjective.MINIMIZE_PRESSURE_LOSS,\r\n        weight: 1.0,\r\n        description: 'Minimize total system pressure loss',\r\n        evaluationFunction: objectiveFunction,\r\n        units: 'inWC'\r\n      }],\r\n      aggregationMethod: 'single_objective'\r\n    },\r\n    constraints,\r\n    algorithmSettings: {\r\n      algorithm: OptimizationAlgorithm.GENETIC_ALGORITHM,\r\n      parameters: {\r\n        populationSize: 50,\r\n        maxIterations: 100,\r\n        crossoverRate: 0.8,\r\n        mutationRate: 0.1\r\n      },\r\n      parallelization: { enabled: true },\r\n      convergenceCriteria: {\r\n        maxIterations: 100,\r\n        toleranceValue: 1e-6,\r\n        stagnationLimit: 20\r\n      }\r\n    },\r\n    convergenceCriteria: {\r\n      maxIterations: 100,\r\n      toleranceValue: 1e-6,\r\n      stagnationLimit: 20\r\n    }\r\n  };\r\n\r\n  // Run optimization\r\n  const ga = new GeneticAlgorithm({\r\n    populationSize: 50,\r\n    maxGenerations: 100,\r\n    crossoverRate: 0.8,\r\n    mutationRate: 0.1,\r\n    eliteSize: 5,\r\n    constraintHandling: 'penalty',\r\n    penaltyCoefficient: 1000\r\n  });\r\n\r\n  const constraintFunctions = constraints.map(c => c.evaluationFunction);\r\n  const result = await ga.optimize(problem, objectiveFunction, constraintFunctions);\r\n\r\n  console.log('Optimization Results:');\r\n  console.log(`Status: ${result.status}`);\r\n  console.log(`Best Fitness: ${result.bestSolution.fitness.toFixed(4)} inWC`);\r\n  console.log(`Main Duct Diameter: ${(result.bestSolution.variables['main_duct_diameter'] as number).toFixed(1)} inches`);\r\n  console.log(`Branch Duct Diameter: ${(result.bestSolution.variables['branch_duct_diameter'] as number).toFixed(1)} inches`);\r\n  console.log(`Fan Speed: ${(result.bestSolution.variables['fan_speed'] as number).toFixed(1)}%`);\r\n  console.log(`Execution Time: ${result.statistics.executionTime.toFixed(2)} ms`);\r\n  console.log(`Total Evaluations: ${result.statistics.totalEvaluations}`);\r\n\r\n  return result;\r\n}\r\n\r\n/**\r\n * Example 2: Multi-Objective HVAC System Optimization\r\n * \r\n * Optimize system for multiple competing objectives: pressure loss, energy consumption, and cost\r\n */\r\nexport async function example2_MultiObjectiveOptimization(): Promise<OptimizationResult> {\r\n  console.log('\\n=== Example 2: Multi-Objective HVAC Optimization ===');\r\n  \r\n  // Define system configuration for a commercial building\r\n  const systemConfig: SystemConfiguration = {\r\n    id: 'commercial_hvac_system',\r\n    name: 'Commercial Building HVAC System',\r\n    description: 'Multi-zone HVAC system with VAV boxes',\r\n    systemType: 'supply_air',\r\n    designAirflow: 50000,\r\n    designPressure: 5.0,\r\n    operatingConditions: {\r\n      temperature: 70,\r\n      humidity: 50,\r\n      elevation: 500\r\n    },\r\n    components: [\r\n      {\r\n        id: 'supply_fan',\r\n        type: 'fan',\r\n        specifications: {\r\n          maxPressure: 10.0,\r\n          maxAirflow: 60000,\r\n          efficiency: 0.85,\r\n          powerRating: 75 // HP\r\n        }\r\n      },\r\n      {\r\n        id: 'main_supply_duct',\r\n        type: 'duct',\r\n        specifications: {\r\n          length: 300,\r\n          material: 'galvanized_steel',\r\n          roughness: 0.0015\r\n        }\r\n      }\r\n    ]\r\n  };\r\n\r\n  // Define optimization variables\r\n  const variables: OptimizationVariable[] = [\r\n    {\r\n      id: 'supply_duct_diameter',\r\n      name: 'Supply Duct Diameter',\r\n      description: 'Main supply duct diameter',\r\n      type: 'continuous',\r\n      bounds: { minimum: 24, maximum: 48 },\r\n      units: 'inches',\r\n      currentValue: 36\r\n    },\r\n    {\r\n      id: 'duct_material',\r\n      name: 'Duct Material',\r\n      description: 'Duct material selection',\r\n      type: 'discrete',\r\n      discreteValues: ['galvanized_steel', 'aluminum', 'fiberglass', 'stainless_steel'],\r\n      units: 'material_type',\r\n      currentValue: 'galvanized_steel'\r\n    },\r\n    {\r\n      id: 'insulation_thickness',\r\n      name: 'Insulation Thickness',\r\n      description: 'Duct insulation thickness',\r\n      type: 'discrete',\r\n      discreteValues: [1, 2, 3, 4],\r\n      units: 'inches',\r\n      currentValue: 2\r\n    },\r\n    {\r\n      id: 'fan_efficiency',\r\n      name: 'Fan Efficiency',\r\n      description: 'Fan motor efficiency selection',\r\n      type: 'discrete',\r\n      discreteValues: [0.80, 0.85, 0.90, 0.93, 0.95],\r\n      units: 'efficiency',\r\n      currentValue: 0.85\r\n    }\r\n  ];\r\n\r\n  // Define constraints\r\n  const constraints: OptimizationConstraint[] = [\r\n    {\r\n      id: 'airflow_delivery',\r\n      name: 'Airflow Delivery Requirement',\r\n      description: 'Must deliver design airflow',\r\n      type: 'inequality',\r\n      bounds: { minimum: 50000 },\r\n      units: 'CFM',\r\n      evaluationFunction: (vars: OptimizationVariable[]) => {\r\n        // Simplified: assume delivered airflow is close to design\r\n        return 50000 - 49000; // Always feasible for this example\r\n      }\r\n    },\r\n    {\r\n      id: 'noise_limit',\r\n      name: 'Noise Level Limit',\r\n      description: 'System noise must not exceed limit',\r\n      type: 'inequality',\r\n      bounds: { maximum: 55 },\r\n      units: 'dBA',\r\n      evaluationFunction: (vars: OptimizationVariable[]) => {\r\n        const diameter = vars.find(v => v.id === 'supply_duct_diameter')?.currentValue as number || 36;\r\n        const velocity = 50000 / (Math.PI * (diameter / 12) ** 2 / 4 * 144);\r\n        const noiseLevel = 30 + 20 * Math.log10(velocity / 1000); // Simplified noise model\r\n        return noiseLevel - 55; // Violation if > 0\r\n      }\r\n    }\r\n  ];\r\n\r\n  // Define objective functions\r\n  const pressureLossObjective: ObjectiveFunctionType = (vars: OptimizationVariable[]): number => {\r\n    const diameter = vars.find(v => v.id === 'supply_duct_diameter')?.currentValue as number || 36;\r\n    const material = vars.find(v => v.id === 'duct_material')?.currentValue as string || 'galvanized_steel';\r\n    \r\n    // Material roughness factors\r\n    const roughnessFactors = {\r\n      'galvanized_steel': 1.0,\r\n      'aluminum': 0.8,\r\n      'fiberglass': 1.2,\r\n      'stainless_steel': 0.6\r\n    };\r\n    \r\n    const roughnessFactor = roughnessFactors[material as keyof typeof roughnessFactors] || 1.0;\r\n    const velocity = 50000 / (Math.PI * (diameter / 12) ** 2 / 4 * 144);\r\n    const pressureLoss = roughnessFactor * 0.025 * 300 * (velocity / 4005) ** 1.85 / (diameter / 12) ** 1.23;\r\n    \r\n    return pressureLoss + 2.0; // Include fittings and equipment losses\r\n  };\r\n\r\n  const energyConsumptionObjective: ObjectiveFunctionType = (vars: OptimizationVariable[]): number => {\r\n    const diameter = vars.find(v => v.id === 'supply_duct_diameter')?.currentValue as number || 36;\r\n    const fanEfficiency = vars.find(v => v.id === 'fan_efficiency')?.currentValue as number || 0.85;\r\n    \r\n    // Calculate fan power based on pressure loss and efficiency\r\n    const pressureLoss = pressureLossObjective(vars);\r\n    const fanPower = (50000 * pressureLoss) / (6356 * fanEfficiency); // HP\r\n    const annualEnergyConsumption = fanPower * 0.746 * 8760 * 0.7; // kWh/year (70% load factor)\r\n    \r\n    return annualEnergyConsumption;\r\n  };\r\n\r\n  const totalCostObjective: ObjectiveFunctionType = (vars: OptimizationVariable[]): number => {\r\n    const diameter = vars.find(v => v.id === 'supply_duct_diameter')?.currentValue as number || 36;\r\n    const material = vars.find(v => v.id === 'duct_material')?.currentValue as string || 'galvanized_steel';\r\n    const insulationThickness = vars.find(v => v.id === 'insulation_thickness')?.currentValue as number || 2;\r\n    const fanEfficiency = vars.find(v => v.id === 'fan_efficiency')?.currentValue as number || 0.85;\r\n    \r\n    // Material costs per sq ft\r\n    const materialCosts = {\r\n      'galvanized_steel': 12,\r\n      'aluminum': 18,\r\n      'fiberglass': 8,\r\n      'stainless_steel': 25\r\n    };\r\n    \r\n    // Fan efficiency premium costs\r\n    const efficiencyPremiums = {\r\n      0.80: 0,\r\n      0.85: 500,\r\n      0.90: 1200,\r\n      0.93: 2000,\r\n      0.95: 3500\r\n    };\r\n    \r\n    const materialCost = materialCosts[material as keyof typeof materialCosts] || 12;\r\n    const ductSurfaceArea = Math.PI * (diameter / 12) * 300; // sq ft\r\n    const ductCost = materialCost * ductSurfaceArea;\r\n    const insulationCost = insulationThickness * 3 * ductSurfaceArea;\r\n    const fanPremium = efficiencyPremiums[fanEfficiency as keyof typeof efficiencyPremiums] || 0;\r\n    \r\n    // Operating cost (energy cost over 10 years)\r\n    const energyConsumption = energyConsumptionObjective(vars);\r\n    const operatingCost = energyConsumption * 0.12 * 10; // $0.12/kWh for 10 years\r\n    \r\n    return ductCost + insulationCost + fanPremium + operatingCost;\r\n  };\r\n\r\n  // Create optimization problem\r\n  const problem: OptimizationProblem = {\r\n    id: 'multi_objective_hvac_optimization',\r\n    name: 'Multi-Objective HVAC System Optimization',\r\n    description: 'Optimize for pressure loss, energy consumption, and total cost',\r\n    systemConfiguration: systemConfig,\r\n    variables,\r\n    objectives: {\r\n      objectives: [\r\n        {\r\n          id: 'minimize_pressure_loss',\r\n          objective: OptimizationObjective.MINIMIZE_PRESSURE_LOSS,\r\n          weight: 0.4,\r\n          description: 'Minimize system pressure loss',\r\n          evaluationFunction: pressureLossObjective,\r\n          units: 'inWC'\r\n        },\r\n        {\r\n          id: 'minimize_energy_consumption',\r\n          objective: OptimizationObjective.MINIMIZE_ENERGY_CONSUMPTION,\r\n          weight: 0.4,\r\n          description: 'Minimize annual energy consumption',\r\n          evaluationFunction: energyConsumptionObjective,\r\n          units: 'kWh/year'\r\n        },\r\n        {\r\n          id: 'minimize_total_cost',\r\n          objective: OptimizationObjective.MINIMIZE_TOTAL_COST,\r\n          weight: 0.2,\r\n          description: 'Minimize total lifecycle cost',\r\n          evaluationFunction: totalCostObjective,\r\n          units: 'USD'\r\n        }\r\n      ],\r\n      aggregationMethod: 'pareto_optimal'\r\n    },\r\n    constraints,\r\n    algorithmSettings: {\r\n      algorithm: OptimizationAlgorithm.NSGA_II,\r\n      parameters: {\r\n        populationSize: 100,\r\n        maxIterations: 150,\r\n        crossoverRate: 0.9,\r\n        mutationRate: 0.1\r\n      },\r\n      parallelization: { enabled: true },\r\n      convergenceCriteria: {\r\n        maxIterations: 150,\r\n        toleranceValue: 1e-6,\r\n        stagnationLimit: 30\r\n      }\r\n    },\r\n    convergenceCriteria: {\r\n      maxIterations: 150,\r\n      toleranceValue: 1e-6,\r\n      stagnationLimit: 30\r\n    }\r\n  };\r\n\r\n  // Run multi-objective optimization\r\n  const moFramework = new MultiObjectiveOptimizationFramework({\r\n    algorithm: 'nsga2',\r\n    populationSize: 100,\r\n    maxGenerations: 150,\r\n    crossoverRate: 0.9,\r\n    mutationRate: 0.1,\r\n    eliteSize: 10,\r\n    paretoSettings: {\r\n      maxSolutions: 50,\r\n      diversityThreshold: 0.01,\r\n      convergenceThreshold: 1e-6,\r\n      hypervolume: { enabled: true, referencePoint: [] },\r\n      spacing: { enabled: true, targetSpacing: 0.1 }\r\n    },\r\n    diversityMaintenance: true,\r\n    archiveSize: 100\r\n  });\r\n\r\n  const constraintFunctions = constraints.map(c => c.evaluationFunction);\r\n  const result = await moFramework.optimizeMultiObjective(\r\n    problem,\r\n    [pressureLossObjective, energyConsumptionObjective, totalCostObjective],\r\n    constraintFunctions\r\n  );\r\n\r\n  console.log('Multi-Objective Optimization Results:');\r\n  console.log(`Status: ${result.status}`);\r\n  console.log(`Pareto Front Size: ${result.analysis?.paretoFront?.solutions.length || 0}`);\r\n  console.log(`Hypervolume: ${result.analysis?.paretoFront?.hypervolume?.toFixed(4) || 'N/A'}`);\r\n  console.log(`Knee Points Found: ${result.analysis?.tradeoffAnalysis?.kneePoints.length || 0}`);\r\n  console.log(`Execution Time: ${result.statistics.executionTime.toFixed(2)} ms`);\r\n  console.log(`Total Evaluations: ${result.statistics.totalEvaluations}`);\r\n\r\n  // Display best compromise solution\r\n  if (result.bestSolution) {\r\n    console.log('\\nBest Compromise Solution:');\r\n    console.log(`Supply Duct Diameter: ${(result.bestSolution.variables['supply_duct_diameter'] as number).toFixed(1)} inches`);\r\n    console.log(`Duct Material: ${result.bestSolution.variables['duct_material']}`);\r\n    console.log(`Insulation Thickness: ${result.bestSolution.variables['insulation_thickness']} inches`);\r\n    console.log(`Fan Efficiency: ${((result.bestSolution.variables['fan_efficiency'] as number) * 100).toFixed(1)}%`);\r\n  }\r\n\r\n  return result;\r\n}\r\n\r\n/**\r\n * Example 3: Integration with Existing Phase 1/2/3 Priority 1 Components\r\n * \r\n * Demonstrates how to use optimization with existing calculation services\r\n */\r\nexport async function example3_IntegrationWithExistingComponents(): Promise<OptimizationResult> {\r\n  console.log('\\n=== Example 3: Integration with Existing Components ===');\r\n  \r\n  // This example shows how the optimization framework integrates with:\r\n  // - SystemPressureCalculator (Phase 1)\r\n  // - FittingLossCalculator (Phase 1) \r\n  // - AdvancedFittingCalculator (Phase 3 Priority 1)\r\n  // - AirPropertiesCalculator (Phase 2)\r\n  \r\n  const systemConfig: SystemConfiguration = {\r\n    id: 'integrated_system',\r\n    name: 'Integrated HVAC System with Advanced Fittings',\r\n    description: 'System using all Phase 1/2/3 Priority 1 components',\r\n    systemType: 'supply_air',\r\n    designAirflow: 15000,\r\n    designPressure: 3.5,\r\n    operatingConditions: {\r\n      temperature: 68,\r\n      humidity: 40,\r\n      elevation: 2000\r\n    },\r\n    components: [\r\n      {\r\n        id: 'main_fan',\r\n        type: 'fan',\r\n        specifications: {\r\n          maxPressure: 6.0,\r\n          maxAirflow: 20000,\r\n          efficiency: 0.88\r\n        }\r\n      },\r\n      {\r\n        id: 'main_duct',\r\n        type: 'duct',\r\n        specifications: {\r\n          length: 150,\r\n          material: 'galvanized_steel',\r\n          roughness: 0.0015\r\n        }\r\n      },\r\n      {\r\n        id: 'transition_fitting',\r\n        type: 'transition',\r\n        specifications: {\r\n          type: 'round_to_rectangular',\r\n          inletDiameter: 20,\r\n          outletWidth: 24,\r\n          outletHeight: 12\r\n        }\r\n      }\r\n    ]\r\n  };\r\n\r\n  // Use SystemOptimizationEngine for integrated optimization\r\n  const variables: OptimizationVariable[] = [\r\n    {\r\n      id: 'main_duct_diameter',\r\n      name: 'Main Duct Diameter',\r\n      description: 'Diameter of main supply duct',\r\n      type: 'continuous',\r\n      bounds: { minimum: 16, maximum: 28 },\r\n      units: 'inches',\r\n      currentValue: 20\r\n    },\r\n    {\r\n      id: 'transition_type',\r\n      name: 'Transition Fitting Type',\r\n      description: 'Type of transition fitting',\r\n      type: 'discrete',\r\n      discreteValues: ['round_to_rectangular', 'round_to_round', 'rectangular_to_round'],\r\n      units: 'fitting_type',\r\n      currentValue: 'round_to_rectangular'\r\n    }\r\n  ];\r\n\r\n  const constraints: OptimizationConstraint[] = [\r\n    {\r\n      id: 'velocity_limit',\r\n      name: 'Maximum Velocity Limit',\r\n      description: 'Duct velocity must not exceed limit',\r\n      type: 'inequality',\r\n      bounds: { maximum: 2500 },\r\n      units: 'FPM',\r\n      evaluationFunction: (vars: OptimizationVariable[]) => {\r\n        const diameter = vars.find(v => v.id === 'main_duct_diameter')?.currentValue as number || 20;\r\n        const velocity = 15000 / (Math.PI * (diameter / 12) ** 2 / 4 * 144);\r\n        return velocity - 2500; // Violation if > 0\r\n      }\r\n    }\r\n  ];\r\n\r\n  const problem: OptimizationProblem = {\r\n    id: 'integrated_optimization',\r\n    name: 'Integrated System Optimization',\r\n    description: 'Optimization using all existing calculation components',\r\n    systemConfiguration: systemConfig,\r\n    variables,\r\n    objectives: {\r\n      objectives: [{\r\n        id: 'minimize_total_pressure_loss',\r\n        objective: OptimizationObjective.MINIMIZE_PRESSURE_LOSS,\r\n        weight: 1.0,\r\n        description: 'Minimize total system pressure loss including advanced fittings',\r\n        evaluationFunction: (vars: OptimizationVariable[]) => {\r\n          // This would integrate with actual Phase 1/2/3 Priority 1 calculators\r\n          const diameter = vars.find(v => v.id === 'main_duct_diameter')?.currentValue as number || 20;\r\n          const transitionType = vars.find(v => v.id === 'transition_type')?.currentValue as string || 'round_to_rectangular';\r\n          \r\n          // Simplified calculation - in practice would use:\r\n          // - SystemPressureCalculator.calculateTotalPressureLoss()\r\n          // - AdvancedFittingCalculator.calculateFittingLoss()\r\n          // - AirPropertiesCalculator.getAirProperties()\r\n          \r\n          const velocity = 15000 / (Math.PI * (diameter / 12) ** 2 / 4 * 144);\r\n          const ductLoss = 0.02 * 150 * (velocity / 4005) ** 1.85 / (diameter / 12) ** 1.23;\r\n          \r\n          // Transition fitting losses (simplified)\r\n          const transitionLosses = {\r\n            'round_to_rectangular': 0.15,\r\n            'round_to_round': 0.05,\r\n            'rectangular_to_round': 0.12\r\n          };\r\n          \r\n          const fittingLoss = transitionLosses[transitionType as keyof typeof transitionLosses] || 0.15;\r\n          const velocityPressure = (velocity / 4005) ** 2;\r\n          \r\n          return ductLoss + fittingLoss * velocityPressure;\r\n        },\r\n        units: 'inWC'\r\n      }],\r\n      aggregationMethod: 'single_objective'\r\n    },\r\n    constraints,\r\n    algorithmSettings: {\r\n      algorithm: OptimizationAlgorithm.PARTICLE_SWARM,\r\n      parameters: {\r\n        populationSize: 30,\r\n        maxIterations: 75,\r\n        inertiaWeight: 0.9,\r\n        accelerationCoefficients: [2.0, 2.0]\r\n      },\r\n      parallelization: { enabled: false },\r\n      convergenceCriteria: {\r\n        maxIterations: 75,\r\n        toleranceValue: 1e-6,\r\n        stagnationLimit: 15\r\n      }\r\n    },\r\n    convergenceCriteria: {\r\n      maxIterations: 75,\r\n      toleranceValue: 1e-6,\r\n      stagnationLimit: 15\r\n    }\r\n  };\r\n\r\n  // Use SystemOptimizationEngine for integrated optimization\r\n  const result = await SystemOptimizationEngine.optimizeSystem(\r\n    problem,\r\n    OptimizationAlgorithm.PARTICLE_SWARM\r\n  );\r\n\r\n  console.log('Integrated Optimization Results:');\r\n  console.log(`Status: ${result.status}`);\r\n  console.log(`Best Fitness: ${result.bestSolution.fitness.toFixed(4)} inWC`);\r\n  console.log(`Optimal Duct Diameter: ${(result.bestSolution.variables['main_duct_diameter'] as number).toFixed(1)} inches`);\r\n  console.log(`Optimal Transition Type: ${result.bestSolution.variables['transition_type']}`);\r\n  console.log(`Execution Time: ${result.statistics.executionTime.toFixed(2)} ms`);\r\n\r\n  return result;\r\n}\r\n\r\n/**\r\n * Run all optimization examples\r\n */\r\nexport async function runAllOptimizationExamples(): Promise<void> {\r\n  console.log('🚀 Running SizeWise Suite Optimization Framework Examples\\n');\r\n  \r\n  try {\r\n    // Example 1: Single-objective optimization\r\n    await example1_DuctSizingOptimization();\r\n    \r\n    // Example 2: Multi-objective optimization\r\n    await example2_MultiObjectiveOptimization();\r\n    \r\n    // Example 3: Integration with existing components\r\n    await example3_IntegrationWithExistingComponents();\r\n    \r\n    console.log('\\n✅ All optimization examples completed successfully!');\r\n    console.log('\\nNext Steps:');\r\n    console.log('1. Review the optimization results and trade-offs');\r\n    console.log('2. Integrate with your specific HVAC system requirements');\r\n    console.log('3. Customize objective functions for your use case');\r\n    console.log('4. Add additional constraints as needed');\r\n    console.log('5. Experiment with different optimization algorithms');\r\n    \r\n  } catch (error) {\r\n    console.error('❌ Error running optimization examples:', error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n// Export individual examples for selective usage\r\nexport {\r\n  example1_DuctSizingOptimization as ductSizingOptimization,\r\n  example2_MultiObjectiveOptimization as multiObjectiveOptimization,\r\n  example3_IntegrationWithExistingComponents as integratedOptimization\r\n};\r\n"], "mappings": ";;AAAA;;;;;;;;;;;;;AAAA;AAAA,SAAAA,cAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;MAAA;QAAAC,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAO,SAAA;MAAA;QAAAD,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAW,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,cAAA;MAAAC,IAAA;MAAAC,QAAA;MAAAC,KAAA;MAAAC,OAAA;MAAAC,cAAA;MAAAC,OAAA;IAAA;IAAAC,eAAA;IAAA5B,IAAA;EAAA;EAAA,IAAA6B,QAAA,GAAA5B,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAA0B,QAAA,CAAA9B,IAAA,KAAA8B,QAAA,CAAA9B,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAA6B,QAAA,CAAA9B,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAA0B,cAAA,GAAAD,QAAA,CAAA9B,IAAA;EAAA;IAmCA;IAAAD,aAAA,YAAAA,CAAA;MAAA,OAAAgC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAhC,aAAA;AAAAA,aAAA,GAAAoB,CAAA;;;;;;AAAAa,OAAA,CAAAC,+BAAA,GAAAA,+BAAA;AA0LC;AAAAlC,aAAA,GAAAoB,CAAA;AAifoCa,OAAA,CAAAE,sBAAA,GAAAD,+BAAA;AAAsB;AAAAlC,aAAA,GAAAoB,CAAA;AA1e3Da,OAAA,CAAAG,mCAAA,GAAAA,mCAAA;AAwRC;AAAApC,aAAA,GAAAoB,CAAA;AAmNwCa,OAAA,CAAAI,0BAAA,GAAAD,mCAAA;AAA0B;AAAApC,aAAA,GAAAoB,CAAA;AA5MnEa,OAAA,CAAAK,0CAAA,GAAAA,0CAAA;AAyKC;AAAAtC,aAAA,GAAAoB,CAAA;AAoC+Ca,OAAA,CAAAM,sBAAA,GAAAD,0CAAA;AAAsB;AAAAtC,aAAA,GAAAoB,CAAA;AA/BtEa,OAAA,CAAAO,0BAAA,GAAAA,0BAAA;AAnqBA,MAAAC,yBAAA;AAAA;AAAA,CAAAzC,aAAA,GAAAoB,CAAA,OAAAsB,OAAA;AAYA,MAAAC,0BAAA;AAAA;AAAA,CAAA3C,aAAA,GAAAoB,CAAA,OAAAsB,OAAA;AACA,MAAAE,kBAAA;AAAA;AAAA,CAAA5C,aAAA,GAAAoB,CAAA,QAAAsB,OAAA;AACA,MAAAG,qCAAA;AAAA;AAAA,CAAA7C,aAAA,GAAAoB,CAAA,QAAAsB,OAAA;AAEA;;;;;AAKO,eAAeR,+BAA+BA,CAAA;EAAA;EAAAlC,aAAA,GAAAqB,CAAA;EAAArB,aAAA,GAAAoB,CAAA;EACnD0B,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;EAE1D;EACA,MAAMC,YAAY;EAAA;EAAA,CAAAhD,aAAA,GAAAoB,CAAA,QAAwB;IACxC6B,EAAE,EAAE,sBAAsB;IAC1BpC,IAAI,EAAE,6BAA6B;IACnCqC,WAAW,EAAE,yDAAyD;IACtEC,UAAU,EAAE,YAAY;IACxBC,aAAa,EAAE,KAAK;IAAE;IACtBC,cAAc,EAAE,GAAG;IAAI;IACvBC,mBAAmB,EAAE;MACnBC,WAAW,EAAE,EAAE;MAAM;MACrBC,QAAQ,EAAE,EAAE;MAAS;MACrBC,SAAS,EAAE,IAAI,CAAM;KACtB;IACDC,UAAU,EAAE,CACV;MACET,EAAE,EAAE,UAAU;MACdhC,IAAI,EAAE,KAAK;MACX0C,cAAc,EAAE;QACdC,WAAW,EAAE,GAAG;QAChBC,UAAU,EAAE,KAAK;QACjBC,UAAU,EAAE;;KAEf,EACD;MACEb,EAAE,EAAE,WAAW;MACfhC,IAAI,EAAE,MAAM;MACZ0C,cAAc,EAAE;QACdI,MAAM,EAAE,GAAG;QACXC,QAAQ,EAAE,kBAAkB;QAC5BC,SAAS,EAAE;;KAEd;GAEJ;EAED;EACA,MAAMC,SAAS;EAAA;EAAA,CAAAlE,aAAA,GAAAoB,CAAA,QAA2B,CACxC;IACE6B,EAAE,EAAE,oBAAoB;IACxBpC,IAAI,EAAE,oBAAoB;IAC1BqC,WAAW,EAAE,kCAAkC;IAC/CjC,IAAI,EAAE,YAAY;IAClBkD,MAAM,EAAE;MAAEC,OAAO,EAAE,EAAE;MAAEC,OAAO,EAAE;IAAE,CAAE;IACpCC,KAAK,EAAE,QAAQ;IACfC,YAAY,EAAE;GACf,EACD;IACEtB,EAAE,EAAE,sBAAsB;IAC1BpC,IAAI,EAAE,sBAAsB;IAC5BqC,WAAW,EAAE,0BAA0B;IACvCjC,IAAI,EAAE,YAAY;IAClBkD,MAAM,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,OAAO,EAAE;IAAE,CAAE;IACnCC,KAAK,EAAE,QAAQ;IACfC,YAAY,EAAE;GACf,EACD;IACEtB,EAAE,EAAE,WAAW;IACfpC,IAAI,EAAE,WAAW;IACjBqC,WAAW,EAAE,gCAAgC;IAC7CjC,IAAI,EAAE,YAAY;IAClBkD,MAAM,EAAE;MAAEC,OAAO,EAAE,EAAE;MAAEC,OAAO,EAAE;IAAG,CAAE;IACrCC,KAAK,EAAE,SAAS;IAChBC,YAAY,EAAE;GACf,CACF;EAED;EACA,MAAMC,WAAW;EAAA;EAAA,CAAAxE,aAAA,GAAAoB,CAAA,QAA6B,CAC5C;IACE6B,EAAE,EAAE,qBAAqB;IACzBpC,IAAI,EAAE,6BAA6B;IACnCqC,WAAW,EAAE,6CAA6C;IAC1DjC,IAAI,EAAE,YAAY;IAClBkD,MAAM,EAAE;MAAEC,OAAO,EAAE;IAAK,CAAE;IAC1BE,KAAK,EAAE,KAAK;IACZG,kBAAkB,EAAGC,IAA4B,IAAI;MAAA;MAAA1E,aAAA,GAAAqB,CAAA;MACnD,MAAMsD,QAAQ;MAAA;MAAA,CAAA3E,aAAA,GAAAoB,CAAA;MAAG;MAAA,CAAApB,aAAA,GAAAsB,CAAA,UAAAoD,IAAI,CAACE,IAAI,CAACC,CAAC,IAAI;QAAA;QAAA7E,aAAA,GAAAqB,CAAA;QAAArB,aAAA,GAAAoB,CAAA;QAAA,OAAAyD,CAAC,CAAC5B,EAAE,KAAK,WAAW;MAAX,CAAW,CAAC,EAAEsB,YAAsB;MAAA;MAAA,CAAAvE,aAAA,GAAAsB,CAAA,UAAI,EAAE;MACnF,MAAMwD,gBAAgB;MAAA;MAAA,CAAA9E,aAAA,GAAAoB,CAAA,QAAIuD,QAAQ,GAAG,GAAG,GAAI,KAAK,EAAC,CAAC;MAAA;MAAA3E,aAAA,GAAAoB,CAAA;MACnD,OAAO,KAAK,GAAG0D,gBAAgB,CAAC,CAAC;IACnC;GACD,EACD;IACE7B,EAAE,EAAE,mBAAmB;IACvBpC,IAAI,EAAE,8BAA8B;IACpCqC,WAAW,EAAE,kDAAkD;IAC/DjC,IAAI,EAAE,YAAY;IAClBkD,MAAM,EAAE;MAAEE,OAAO,EAAE;IAAG,CAAE;IACxBC,KAAK,EAAE,MAAM;IACbG,kBAAkB,EAAGC,IAA4B,IAAI;MAAA;MAAA1E,aAAA,GAAAqB,CAAA;MACnD,MAAM0D,YAAY;MAAA;MAAA,CAAA/E,aAAA,GAAAoB,CAAA;MAAG;MAAA,CAAApB,aAAA,GAAAsB,CAAA,UAAAoD,IAAI,CAACE,IAAI,CAACC,CAAC,IAAI;QAAA;QAAA7E,aAAA,GAAAqB,CAAA;QAAArB,aAAA,GAAAoB,CAAA;QAAA,OAAAyD,CAAC,CAAC5B,EAAE,KAAK,oBAAoB;MAApB,CAAoB,CAAC,EAAEsB,YAAsB;MAAA;MAAA,CAAAvE,aAAA,GAAAsB,CAAA,UAAI,EAAE;MAChG,MAAM0D,cAAc;MAAA;MAAA,CAAAhF,aAAA,GAAAoB,CAAA;MAAG;MAAA,CAAApB,aAAA,GAAAsB,CAAA,UAAAoD,IAAI,CAACE,IAAI,CAACC,CAAC,IAAI;QAAA;QAAA7E,aAAA,GAAAqB,CAAA;QAAArB,aAAA,GAAAoB,CAAA;QAAA,OAAAyD,CAAC,CAAC5B,EAAE,KAAK,sBAAsB;MAAtB,CAAsB,CAAC,EAAEsB,YAAsB;MAAA;MAAA,CAAAvE,aAAA,GAAAsB,CAAA,UAAI,EAAE;MACpG,MAAMqD,QAAQ;MAAA;MAAA,CAAA3E,aAAA,GAAAoB,CAAA;MAAG;MAAA,CAAApB,aAAA,GAAAsB,CAAA,UAAAoD,IAAI,CAACE,IAAI,CAACC,CAAC,IAAI;QAAA;QAAA7E,aAAA,GAAAqB,CAAA;QAAArB,aAAA,GAAAoB,CAAA;QAAA,OAAAyD,CAAC,CAAC5B,EAAE,KAAK,WAAW;MAAX,CAAW,CAAC,EAAEsB,YAAsB;MAAA;MAAA,CAAAvE,aAAA,GAAAsB,CAAA,UAAI,EAAE;MAEnF;MACA,MAAM2D,QAAQ;MAAA;MAAA,CAAAjF,aAAA,GAAAoB,CAAA,QAAIuD,QAAQ,GAAG,GAAG,GAAI,KAAK,IAAIO,IAAI,CAACC,EAAE,GAAG,CAACJ,YAAY,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;MAC1F,MAAMK,QAAQ;MAAA;MAAA,CAAApF,aAAA,GAAAoB,CAAA,QAAG,IAAI,GAAG,GAAG,GAAG,CAAC6D,QAAQ,GAAG,IAAI,KAAK,IAAI,GAAG,CAACF,YAAY,GAAG,EAAE,KAAK,IAAI;MACrF,MAAMM,UAAU;MAAA;MAAA,CAAArF,aAAA,GAAAoB,CAAA,QAAG,KAAK,GAAG,EAAE,GAAG,CAAC6D,QAAQ,GAAG,IAAI,KAAK,IAAI,GAAG,CAACD,cAAc,GAAG,EAAE,KAAK,IAAI;MAEzF,MAAMM,SAAS;MAAA;MAAA,CAAAtF,aAAA,GAAAoB,CAAA,QAAGgE,QAAQ,GAAGC,UAAU,GAAG,GAAG,EAAC,CAAC;MAAA;MAAArF,aAAA,GAAAoB,CAAA;MAC/C,OAAOkE,SAAS,GAAG,GAAG,CAAC,CAAC;IAC1B;GACD,CACF;EAED;EAAA;EAAAtF,aAAA,GAAAoB,CAAA;EACA,MAAMmE,iBAAiB,GAA2Bb,IAA4B,IAAY;IAAA;IAAA1E,aAAA,GAAAqB,CAAA;IACxF,MAAM0D,YAAY;IAAA;IAAA,CAAA/E,aAAA,GAAAoB,CAAA;IAAG;IAAA,CAAApB,aAAA,GAAAsB,CAAA,UAAAoD,IAAI,CAACE,IAAI,CAACC,CAAC,IAAI;MAAA;MAAA7E,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MAAA,OAAAyD,CAAC,CAAC5B,EAAE,KAAK,oBAAoB;IAApB,CAAoB,CAAC,EAAEsB,YAAsB;IAAA;IAAA,CAAAvE,aAAA,GAAAsB,CAAA,UAAI,EAAE;IAChG,MAAM0D,cAAc;IAAA;IAAA,CAAAhF,aAAA,GAAAoB,CAAA;IAAG;IAAA,CAAApB,aAAA,GAAAsB,CAAA,UAAAoD,IAAI,CAACE,IAAI,CAACC,CAAC,IAAI;MAAA;MAAA7E,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MAAA,OAAAyD,CAAC,CAAC5B,EAAE,KAAK,sBAAsB;IAAtB,CAAsB,CAAC,EAAEsB,YAAsB;IAAA;IAAA,CAAAvE,aAAA,GAAAsB,CAAA,UAAI,EAAE;IACpG,MAAMqD,QAAQ;IAAA;IAAA,CAAA3E,aAAA,GAAAoB,CAAA;IAAG;IAAA,CAAApB,aAAA,GAAAsB,CAAA,UAAAoD,IAAI,CAACE,IAAI,CAACC,CAAC,IAAI;MAAA;MAAA7E,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MAAA,OAAAyD,CAAC,CAAC5B,EAAE,KAAK,WAAW;IAAX,CAAW,CAAC,EAAEsB,YAAsB;IAAA;IAAA,CAAAvE,aAAA,GAAAsB,CAAA,UAAI,EAAE;IAEnF;IACA,MAAM2D,QAAQ;IAAA;IAAA,CAAAjF,aAAA,GAAAoB,CAAA,QAAIuD,QAAQ,GAAG,GAAG,GAAI,KAAK,IAAIO,IAAI,CAACC,EAAE,GAAG,CAACJ,YAAY,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;IAC1F,MAAMK,QAAQ;IAAA;IAAA,CAAApF,aAAA,GAAAoB,CAAA,QAAG,IAAI,GAAG,GAAG,GAAG,CAAC6D,QAAQ,GAAG,IAAI,KAAK,IAAI,GAAG,CAACF,YAAY,GAAG,EAAE,KAAK,IAAI;IACrF,MAAMM,UAAU;IAAA;IAAA,CAAArF,aAAA,GAAAoB,CAAA,QAAG,KAAK,GAAG,EAAE,GAAG,CAAC6D,QAAQ,GAAG,IAAI,KAAK,IAAI,GAAG,CAACD,cAAc,GAAG,EAAE,KAAK,IAAI;IAAC;IAAAhF,aAAA,GAAAoB,CAAA;IAE1F,OAAOgE,QAAQ,GAAGC,UAAU,GAAG,GAAG,CAAC,CAAC;EACtC,CAAC;EAED;EACA,MAAMG,OAAO;EAAA;EAAA,CAAAxF,aAAA,GAAAoB,CAAA,QAAwB;IACnC6B,EAAE,EAAE,0BAA0B;IAC9BpC,IAAI,EAAE,0BAA0B;IAChCqC,WAAW,EAAE,mDAAmD;IAChEuC,mBAAmB,EAAEzC,YAAY;IACjCkB,SAAS;IACTwB,UAAU,EAAE;MACVA,UAAU,EAAE,CAAC;QACXzC,EAAE,EAAE,wBAAwB;QAC5B0C,SAAS,EAAElD,yBAAA,CAAAmD,qBAAqB,CAACC,sBAAsB;QACvDC,MAAM,EAAE,GAAG;QACX5C,WAAW,EAAE,qCAAqC;QAClDuB,kBAAkB,EAAEc,iBAAiB;QACrCjB,KAAK,EAAE;OACR,CAAC;MACFyB,iBAAiB,EAAE;KACpB;IACDvB,WAAW;IACXwB,iBAAiB,EAAE;MACjBC,SAAS,EAAExD,yBAAA,CAAAyD,qBAAqB,CAACC,iBAAiB;MAClDC,UAAU,EAAE;QACVC,cAAc,EAAE,EAAE;QAClBC,aAAa,EAAE,GAAG;QAClBC,aAAa,EAAE,GAAG;QAClBC,YAAY,EAAE;OACf;MACDC,eAAe,EAAE;QAAEC,OAAO,EAAE;MAAI,CAAE;MAClCC,mBAAmB,EAAE;QACnBL,aAAa,EAAE,GAAG;QAClBM,cAAc,EAAE,IAAI;QACpBC,eAAe,EAAE;;KAEpB;IACDF,mBAAmB,EAAE;MACnBL,aAAa,EAAE,GAAG;MAClBM,cAAc,EAAE,IAAI;MACpBC,eAAe,EAAE;;GAEpB;EAED;EACA,MAAMC,EAAE;EAAA;EAAA,CAAA9G,aAAA,GAAAoB,CAAA,QAAG,IAAIwB,kBAAA,CAAAmE,gBAAgB,CAAC;IAC9BV,cAAc,EAAE,EAAE;IAClBW,cAAc,EAAE,GAAG;IACnBT,aAAa,EAAE,GAAG;IAClBC,YAAY,EAAE,GAAG;IACjBS,SAAS,EAAE,CAAC;IACZC,kBAAkB,EAAE,SAAS;IAC7BC,kBAAkB,EAAE;GACrB,CAAC;EAEF,MAAMC,mBAAmB;EAAA;EAAA,CAAApH,aAAA,GAAAoB,CAAA,QAAGoD,WAAW,CAAC6C,GAAG,CAACC,CAAC,IAAI;IAAA;IAAAtH,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAAA,OAAAkG,CAAC,CAAC7C,kBAAkB;EAAlB,CAAkB,CAAC;EACtE,MAAM8C,MAAM;EAAA;EAAA,CAAAvH,aAAA,GAAAoB,CAAA,QAAG,MAAM0F,EAAE,CAACU,QAAQ,CAAChC,OAAO,EAAED,iBAAiB,EAAE6B,mBAAmB,CAAC;EAAC;EAAApH,aAAA,GAAAoB,CAAA;EAElF0B,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;EAAC;EAAA/C,aAAA,GAAAoB,CAAA;EACrC0B,OAAO,CAACC,GAAG,CAAC,WAAWwE,MAAM,CAACE,MAAM,EAAE,CAAC;EAAC;EAAAzH,aAAA,GAAAoB,CAAA;EACxC0B,OAAO,CAACC,GAAG,CAAC,iBAAiBwE,MAAM,CAACG,YAAY,CAACC,OAAO,CAACC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC;EAAC;EAAA5H,aAAA,GAAAoB,CAAA;EAC5E0B,OAAO,CAACC,GAAG,CAAC,uBAAwBwE,MAAM,CAACG,YAAY,CAACxD,SAAS,CAAC,oBAAoB,CAAY,CAAC0D,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC;EAAC;EAAA5H,aAAA,GAAAoB,CAAA;EACxH0B,OAAO,CAACC,GAAG,CAAC,yBAA0BwE,MAAM,CAACG,YAAY,CAACxD,SAAS,CAAC,sBAAsB,CAAY,CAAC0D,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC;EAAC;EAAA5H,aAAA,GAAAoB,CAAA;EAC5H0B,OAAO,CAACC,GAAG,CAAC,cAAewE,MAAM,CAACG,YAAY,CAACxD,SAAS,CAAC,WAAW,CAAY,CAAC0D,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;EAAC;EAAA5H,aAAA,GAAAoB,CAAA;EAChG0B,OAAO,CAACC,GAAG,CAAC,mBAAmBwE,MAAM,CAACM,UAAU,CAACC,aAAa,CAACF,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;EAAC;EAAA5H,aAAA,GAAAoB,CAAA;EAChF0B,OAAO,CAACC,GAAG,CAAC,sBAAsBwE,MAAM,CAACM,UAAU,CAACE,gBAAgB,EAAE,CAAC;EAAC;EAAA/H,aAAA,GAAAoB,CAAA;EAExE,OAAOmG,MAAM;AACf;AAEA;;;;;AAKO,eAAenF,mCAAmCA,CAAA;EAAA;EAAApC,aAAA,GAAAqB,CAAA;EAAArB,aAAA,GAAAoB,CAAA;EACvD0B,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;EAErE;EACA,MAAMC,YAAY;EAAA;EAAA,CAAAhD,aAAA,GAAAoB,CAAA,QAAwB;IACxC6B,EAAE,EAAE,wBAAwB;IAC5BpC,IAAI,EAAE,iCAAiC;IACvCqC,WAAW,EAAE,uCAAuC;IACpDC,UAAU,EAAE,YAAY;IACxBC,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE,GAAG;IACnBC,mBAAmB,EAAE;MACnBC,WAAW,EAAE,EAAE;MACfC,QAAQ,EAAE,EAAE;MACZC,SAAS,EAAE;KACZ;IACDC,UAAU,EAAE,CACV;MACET,EAAE,EAAE,YAAY;MAChBhC,IAAI,EAAE,KAAK;MACX0C,cAAc,EAAE;QACdC,WAAW,EAAE,IAAI;QACjBC,UAAU,EAAE,KAAK;QACjBC,UAAU,EAAE,IAAI;QAChBkE,WAAW,EAAE,EAAE,CAAC;;KAEnB,EACD;MACE/E,EAAE,EAAE,kBAAkB;MACtBhC,IAAI,EAAE,MAAM;MACZ0C,cAAc,EAAE;QACdI,MAAM,EAAE,GAAG;QACXC,QAAQ,EAAE,kBAAkB;QAC5BC,SAAS,EAAE;;KAEd;GAEJ;EAED;EACA,MAAMC,SAAS;EAAA;EAAA,CAAAlE,aAAA,GAAAoB,CAAA,QAA2B,CACxC;IACE6B,EAAE,EAAE,sBAAsB;IAC1BpC,IAAI,EAAE,sBAAsB;IAC5BqC,WAAW,EAAE,2BAA2B;IACxCjC,IAAI,EAAE,YAAY;IAClBkD,MAAM,EAAE;MAAEC,OAAO,EAAE,EAAE;MAAEC,OAAO,EAAE;IAAE,CAAE;IACpCC,KAAK,EAAE,QAAQ;IACfC,YAAY,EAAE;GACf,EACD;IACEtB,EAAE,EAAE,eAAe;IACnBpC,IAAI,EAAE,eAAe;IACrBqC,WAAW,EAAE,yBAAyB;IACtCjC,IAAI,EAAE,UAAU;IAChBgH,cAAc,EAAE,CAAC,kBAAkB,EAAE,UAAU,EAAE,YAAY,EAAE,iBAAiB,CAAC;IACjF3D,KAAK,EAAE,eAAe;IACtBC,YAAY,EAAE;GACf,EACD;IACEtB,EAAE,EAAE,sBAAsB;IAC1BpC,IAAI,EAAE,sBAAsB;IAC5BqC,WAAW,EAAE,2BAA2B;IACxCjC,IAAI,EAAE,UAAU;IAChBgH,cAAc,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5B3D,KAAK,EAAE,QAAQ;IACfC,YAAY,EAAE;GACf,EACD;IACEtB,EAAE,EAAE,gBAAgB;IACpBpC,IAAI,EAAE,gBAAgB;IACtBqC,WAAW,EAAE,gCAAgC;IAC7CjC,IAAI,EAAE,UAAU;IAChBgH,cAAc,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC9C3D,KAAK,EAAE,YAAY;IACnBC,YAAY,EAAE;GACf,CACF;EAED;EACA,MAAMC,WAAW;EAAA;EAAA,CAAAxE,aAAA,GAAAoB,CAAA,QAA6B,CAC5C;IACE6B,EAAE,EAAE,kBAAkB;IACtBpC,IAAI,EAAE,8BAA8B;IACpCqC,WAAW,EAAE,6BAA6B;IAC1CjC,IAAI,EAAE,YAAY;IAClBkD,MAAM,EAAE;MAAEC,OAAO,EAAE;IAAK,CAAE;IAC1BE,KAAK,EAAE,KAAK;IACZG,kBAAkB,EAAGC,IAA4B,IAAI;MAAA;MAAA1E,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MACnD;MACA,OAAO,KAAK,GAAG,KAAK,CAAC,CAAC;IACxB;GACD,EACD;IACE6B,EAAE,EAAE,aAAa;IACjBpC,IAAI,EAAE,mBAAmB;IACzBqC,WAAW,EAAE,oCAAoC;IACjDjC,IAAI,EAAE,YAAY;IAClBkD,MAAM,EAAE;MAAEE,OAAO,EAAE;IAAE,CAAE;IACvBC,KAAK,EAAE,KAAK;IACZG,kBAAkB,EAAGC,IAA4B,IAAI;MAAA;MAAA1E,aAAA,GAAAqB,CAAA;MACnD,MAAM6G,QAAQ;MAAA;MAAA,CAAAlI,aAAA,GAAAoB,CAAA;MAAG;MAAA,CAAApB,aAAA,GAAAsB,CAAA,UAAAoD,IAAI,CAACE,IAAI,CAACC,CAAC,IAAI;QAAA;QAAA7E,aAAA,GAAAqB,CAAA;QAAArB,aAAA,GAAAoB,CAAA;QAAA,OAAAyD,CAAC,CAAC5B,EAAE,KAAK,sBAAsB;MAAtB,CAAsB,CAAC,EAAEsB,YAAsB;MAAA;MAAA,CAAAvE,aAAA,GAAAsB,CAAA,UAAI,EAAE;MAC9F,MAAM2D,QAAQ;MAAA;MAAA,CAAAjF,aAAA,GAAAoB,CAAA,QAAG,KAAK,IAAI8D,IAAI,CAACC,EAAE,GAAG,CAAC+C,QAAQ,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;MACnE,MAAMC,UAAU;MAAA;MAAA,CAAAnI,aAAA,GAAAoB,CAAA,QAAG,EAAE,GAAG,EAAE,GAAG8D,IAAI,CAACkD,KAAK,CAACnD,QAAQ,GAAG,IAAI,CAAC,EAAC,CAAC;MAAA;MAAAjF,aAAA,GAAAoB,CAAA;MAC1D,OAAO+G,UAAU,GAAG,EAAE,CAAC,CAAC;IAC1B;GACD,CACF;EAED;EAAA;EAAAnI,aAAA,GAAAoB,CAAA;EACA,MAAMiH,qBAAqB,GAA2B3D,IAA4B,IAAY;IAAA;IAAA1E,aAAA,GAAAqB,CAAA;IAC5F,MAAM6G,QAAQ;IAAA;IAAA,CAAAlI,aAAA,GAAAoB,CAAA;IAAG;IAAA,CAAApB,aAAA,GAAAsB,CAAA,UAAAoD,IAAI,CAACE,IAAI,CAACC,CAAC,IAAI;MAAA;MAAA7E,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MAAA,OAAAyD,CAAC,CAAC5B,EAAE,KAAK,sBAAsB;IAAtB,CAAsB,CAAC,EAAEsB,YAAsB;IAAA;IAAA,CAAAvE,aAAA,GAAAsB,CAAA,UAAI,EAAE;IAC9F,MAAM0C,QAAQ;IAAA;IAAA,CAAAhE,aAAA,GAAAoB,CAAA;IAAG;IAAA,CAAApB,aAAA,GAAAsB,CAAA,UAAAoD,IAAI,CAACE,IAAI,CAACC,CAAC,IAAI;MAAA;MAAA7E,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MAAA,OAAAyD,CAAC,CAAC5B,EAAE,KAAK,eAAe;IAAf,CAAe,CAAC,EAAEsB,YAAsB;IAAA;IAAA,CAAAvE,aAAA,GAAAsB,CAAA,UAAI,kBAAkB;IAEvG;IACA,MAAMgH,gBAAgB;IAAA;IAAA,CAAAtI,aAAA,GAAAoB,CAAA,QAAG;MACvB,kBAAkB,EAAE,GAAG;MACvB,UAAU,EAAE,GAAG;MACf,YAAY,EAAE,GAAG;MACjB,iBAAiB,EAAE;KACpB;IAED,MAAMmH,eAAe;IAAA;IAAA,CAAAvI,aAAA,GAAAoB,CAAA;IAAG;IAAA,CAAApB,aAAA,GAAAsB,CAAA,WAAAgH,gBAAgB,CAACtE,QAAyC,CAAC;IAAA;IAAA,CAAAhE,aAAA,GAAAsB,CAAA,WAAI,GAAG;IAC1F,MAAM2D,QAAQ;IAAA;IAAA,CAAAjF,aAAA,GAAAoB,CAAA,QAAG,KAAK,IAAI8D,IAAI,CAACC,EAAE,GAAG,CAAC+C,QAAQ,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;IACnE,MAAMM,YAAY;IAAA;IAAA,CAAAxI,aAAA,GAAAoB,CAAA,QAAGmH,eAAe,GAAG,KAAK,GAAG,GAAG,GAAG,CAACtD,QAAQ,GAAG,IAAI,KAAK,IAAI,GAAG,CAACiD,QAAQ,GAAG,EAAE,KAAK,IAAI;IAAC;IAAAlI,aAAA,GAAAoB,CAAA;IAEzG,OAAOoH,YAAY,GAAG,GAAG,CAAC,CAAC;EAC7B,CAAC;EAAC;EAAAxI,aAAA,GAAAoB,CAAA;EAEF,MAAMqH,0BAA0B,GAA2B/D,IAA4B,IAAY;IAAA;IAAA1E,aAAA,GAAAqB,CAAA;IACjG,MAAM6G,QAAQ;IAAA;IAAA,CAAAlI,aAAA,GAAAoB,CAAA;IAAG;IAAA,CAAApB,aAAA,GAAAsB,CAAA,WAAAoD,IAAI,CAACE,IAAI,CAACC,CAAC,IAAI;MAAA;MAAA7E,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MAAA,OAAAyD,CAAC,CAAC5B,EAAE,KAAK,sBAAsB;IAAtB,CAAsB,CAAC,EAAEsB,YAAsB;IAAA;IAAA,CAAAvE,aAAA,GAAAsB,CAAA,WAAI,EAAE;IAC9F,MAAMoH,aAAa;IAAA;IAAA,CAAA1I,aAAA,GAAAoB,CAAA;IAAG;IAAA,CAAApB,aAAA,GAAAsB,CAAA,WAAAoD,IAAI,CAACE,IAAI,CAACC,CAAC,IAAI;MAAA;MAAA7E,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MAAA,OAAAyD,CAAC,CAAC5B,EAAE,KAAK,gBAAgB;IAAhB,CAAgB,CAAC,EAAEsB,YAAsB;IAAA;IAAA,CAAAvE,aAAA,GAAAsB,CAAA,WAAI,IAAI;IAE/F;IACA,MAAMkH,YAAY;IAAA;IAAA,CAAAxI,aAAA,GAAAoB,CAAA,QAAGiH,qBAAqB,CAAC3D,IAAI,CAAC;IAChD,MAAMiE,QAAQ;IAAA;IAAA,CAAA3I,aAAA,GAAAoB,CAAA,QAAI,KAAK,GAAGoH,YAAY,IAAK,IAAI,GAAGE,aAAa,CAAC,EAAC,CAAC;IAClE,MAAME,uBAAuB;IAAA;IAAA,CAAA5I,aAAA,GAAAoB,CAAA,QAAGuH,QAAQ,GAAG,KAAK,GAAG,IAAI,GAAG,GAAG,EAAC,CAAC;IAAA;IAAA3I,aAAA,GAAAoB,CAAA;IAE/D,OAAOwH,uBAAuB;EAChC,CAAC;EAAC;EAAA5I,aAAA,GAAAoB,CAAA;EAEF,MAAMyH,kBAAkB,GAA2BnE,IAA4B,IAAY;IAAA;IAAA1E,aAAA,GAAAqB,CAAA;IACzF,MAAM6G,QAAQ;IAAA;IAAA,CAAAlI,aAAA,GAAAoB,CAAA;IAAG;IAAA,CAAApB,aAAA,GAAAsB,CAAA,WAAAoD,IAAI,CAACE,IAAI,CAACC,CAAC,IAAI;MAAA;MAAA7E,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MAAA,OAAAyD,CAAC,CAAC5B,EAAE,KAAK,sBAAsB;IAAtB,CAAsB,CAAC,EAAEsB,YAAsB;IAAA;IAAA,CAAAvE,aAAA,GAAAsB,CAAA,WAAI,EAAE;IAC9F,MAAM0C,QAAQ;IAAA;IAAA,CAAAhE,aAAA,GAAAoB,CAAA;IAAG;IAAA,CAAApB,aAAA,GAAAsB,CAAA,WAAAoD,IAAI,CAACE,IAAI,CAACC,CAAC,IAAI;MAAA;MAAA7E,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MAAA,OAAAyD,CAAC,CAAC5B,EAAE,KAAK,eAAe;IAAf,CAAe,CAAC,EAAEsB,YAAsB;IAAA;IAAA,CAAAvE,aAAA,GAAAsB,CAAA,WAAI,kBAAkB;IACvG,MAAMwH,mBAAmB;IAAA;IAAA,CAAA9I,aAAA,GAAAoB,CAAA;IAAG;IAAA,CAAApB,aAAA,GAAAsB,CAAA,WAAAoD,IAAI,CAACE,IAAI,CAACC,CAAC,IAAI;MAAA;MAAA7E,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MAAA,OAAAyD,CAAC,CAAC5B,EAAE,KAAK,sBAAsB;IAAtB,CAAsB,CAAC,EAAEsB,YAAsB;IAAA;IAAA,CAAAvE,aAAA,GAAAsB,CAAA,WAAI,CAAC;IACxG,MAAMoH,aAAa;IAAA;IAAA,CAAA1I,aAAA,GAAAoB,CAAA;IAAG;IAAA,CAAApB,aAAA,GAAAsB,CAAA,WAAAoD,IAAI,CAACE,IAAI,CAACC,CAAC,IAAI;MAAA;MAAA7E,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MAAA,OAAAyD,CAAC,CAAC5B,EAAE,KAAK,gBAAgB;IAAhB,CAAgB,CAAC,EAAEsB,YAAsB;IAAA;IAAA,CAAAvE,aAAA,GAAAsB,CAAA,WAAI,IAAI;IAE/F;IACA,MAAMyH,aAAa;IAAA;IAAA,CAAA/I,aAAA,GAAAoB,CAAA,QAAG;MACpB,kBAAkB,EAAE,EAAE;MACtB,UAAU,EAAE,EAAE;MACd,YAAY,EAAE,CAAC;MACf,iBAAiB,EAAE;KACpB;IAED;IACA,MAAM4H,kBAAkB;IAAA;IAAA,CAAAhJ,aAAA,GAAAoB,CAAA,QAAG;MACzB,IAAI,EAAE,CAAC;MACP,IAAI,EAAE,GAAG;MACT,IAAI,EAAE,IAAI;MACV,IAAI,EAAE,IAAI;MACV,IAAI,EAAE;KACP;IAED,MAAM6H,YAAY;IAAA;IAAA,CAAAjJ,aAAA,GAAAoB,CAAA;IAAG;IAAA,CAAApB,aAAA,GAAAsB,CAAA,WAAAyH,aAAa,CAAC/E,QAAsC,CAAC;IAAA;IAAA,CAAAhE,aAAA,GAAAsB,CAAA,WAAI,EAAE;IAChF,MAAM4H,eAAe;IAAA;IAAA,CAAAlJ,aAAA,GAAAoB,CAAA,QAAG8D,IAAI,CAACC,EAAE,IAAI+C,QAAQ,GAAG,EAAE,CAAC,GAAG,GAAG,EAAC,CAAC;IACzD,MAAMiB,QAAQ;IAAA;IAAA,CAAAnJ,aAAA,GAAAoB,CAAA,QAAG6H,YAAY,GAAGC,eAAe;IAC/C,MAAME,cAAc;IAAA;IAAA,CAAApJ,aAAA,GAAAoB,CAAA,QAAG0H,mBAAmB,GAAG,CAAC,GAAGI,eAAe;IAChE,MAAMG,UAAU;IAAA;IAAA,CAAArJ,aAAA,GAAAoB,CAAA;IAAG;IAAA,CAAApB,aAAA,GAAAsB,CAAA,WAAA0H,kBAAkB,CAACN,aAAgD,CAAC;IAAA;IAAA,CAAA1I,aAAA,GAAAsB,CAAA,WAAI,CAAC;IAE5F;IACA,MAAMgI,iBAAiB;IAAA;IAAA,CAAAtJ,aAAA,GAAAoB,CAAA,SAAGqH,0BAA0B,CAAC/D,IAAI,CAAC;IAC1D,MAAM6E,aAAa;IAAA;IAAA,CAAAvJ,aAAA,GAAAoB,CAAA,SAAGkI,iBAAiB,GAAG,IAAI,GAAG,EAAE,EAAC,CAAC;IAAA;IAAAtJ,aAAA,GAAAoB,CAAA;IAErD,OAAO+H,QAAQ,GAAGC,cAAc,GAAGC,UAAU,GAAGE,aAAa;EAC/D,CAAC;EAED;EACA,MAAM/D,OAAO;EAAA;EAAA,CAAAxF,aAAA,GAAAoB,CAAA,SAAwB;IACnC6B,EAAE,EAAE,mCAAmC;IACvCpC,IAAI,EAAE,0CAA0C;IAChDqC,WAAW,EAAE,gEAAgE;IAC7EuC,mBAAmB,EAAEzC,YAAY;IACjCkB,SAAS;IACTwB,UAAU,EAAE;MACVA,UAAU,EAAE,CACV;QACEzC,EAAE,EAAE,wBAAwB;QAC5B0C,SAAS,EAAElD,yBAAA,CAAAmD,qBAAqB,CAACC,sBAAsB;QACvDC,MAAM,EAAE,GAAG;QACX5C,WAAW,EAAE,+BAA+B;QAC5CuB,kBAAkB,EAAE4D,qBAAqB;QACzC/D,KAAK,EAAE;OACR,EACD;QACErB,EAAE,EAAE,6BAA6B;QACjC0C,SAAS,EAAElD,yBAAA,CAAAmD,qBAAqB,CAAC4D,2BAA2B;QAC5D1D,MAAM,EAAE,GAAG;QACX5C,WAAW,EAAE,oCAAoC;QACjDuB,kBAAkB,EAAEgE,0BAA0B;QAC9CnE,KAAK,EAAE;OACR,EACD;QACErB,EAAE,EAAE,qBAAqB;QACzB0C,SAAS,EAAElD,yBAAA,CAAAmD,qBAAqB,CAAC6D,mBAAmB;QACpD3D,MAAM,EAAE,GAAG;QACX5C,WAAW,EAAE,+BAA+B;QAC5CuB,kBAAkB,EAAEoE,kBAAkB;QACtCvE,KAAK,EAAE;OACR,CACF;MACDyB,iBAAiB,EAAE;KACpB;IACDvB,WAAW;IACXwB,iBAAiB,EAAE;MACjBC,SAAS,EAAExD,yBAAA,CAAAyD,qBAAqB,CAACwD,OAAO;MACxCtD,UAAU,EAAE;QACVC,cAAc,EAAE,GAAG;QACnBC,aAAa,EAAE,GAAG;QAClBC,aAAa,EAAE,GAAG;QAClBC,YAAY,EAAE;OACf;MACDC,eAAe,EAAE;QAAEC,OAAO,EAAE;MAAI,CAAE;MAClCC,mBAAmB,EAAE;QACnBL,aAAa,EAAE,GAAG;QAClBM,cAAc,EAAE,IAAI;QACpBC,eAAe,EAAE;;KAEpB;IACDF,mBAAmB,EAAE;MACnBL,aAAa,EAAE,GAAG;MAClBM,cAAc,EAAE,IAAI;MACpBC,eAAe,EAAE;;GAEpB;EAED;EACA,MAAM8C,WAAW;EAAA;EAAA,CAAA3J,aAAA,GAAAoB,CAAA,SAAG,IAAIyB,qCAAA,CAAA+G,mCAAmC,CAAC;IAC1D3D,SAAS,EAAE,OAAO;IAClBI,cAAc,EAAE,GAAG;IACnBW,cAAc,EAAE,GAAG;IACnBT,aAAa,EAAE,GAAG;IAClBC,YAAY,EAAE,GAAG;IACjBS,SAAS,EAAE,EAAE;IACb4C,cAAc,EAAE;MACdC,YAAY,EAAE,EAAE;MAChBC,kBAAkB,EAAE,IAAI;MACxBC,oBAAoB,EAAE,IAAI;MAC1BC,WAAW,EAAE;QAAEvD,OAAO,EAAE,IAAI;QAAEwD,cAAc,EAAE;MAAE,CAAE;MAClDC,OAAO,EAAE;QAAEzD,OAAO,EAAE,IAAI;QAAE0D,aAAa,EAAE;MAAG;KAC7C;IACDC,oBAAoB,EAAE,IAAI;IAC1BC,WAAW,EAAE;GACd,CAAC;EAEF,MAAMlD,mBAAmB;EAAA;EAAA,CAAApH,aAAA,GAAAoB,CAAA,SAAGoD,WAAW,CAAC6C,GAAG,CAACC,CAAC,IAAI;IAAA;IAAAtH,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAAA,OAAAkG,CAAC,CAAC7C,kBAAkB;EAAlB,CAAkB,CAAC;EACtE,MAAM8C,MAAM;EAAA;EAAA,CAAAvH,aAAA,GAAAoB,CAAA,SAAG,MAAMuI,WAAW,CAACY,sBAAsB,CACrD/E,OAAO,EACP,CAAC6C,qBAAqB,EAAEI,0BAA0B,EAAEI,kBAAkB,CAAC,EACvEzB,mBAAmB,CACpB;EAAC;EAAApH,aAAA,GAAAoB,CAAA;EAEF0B,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;EAAC;EAAA/C,aAAA,GAAAoB,CAAA;EACrD0B,OAAO,CAACC,GAAG,CAAC,WAAWwE,MAAM,CAACE,MAAM,EAAE,CAAC;EAAC;EAAAzH,aAAA,GAAAoB,CAAA;EACxC0B,OAAO,CAACC,GAAG,CAAC;EAAsB;EAAA,CAAA/C,aAAA,GAAAsB,CAAA,WAAAiG,MAAM,CAACiD,QAAQ,EAAEC,WAAW,EAAEC,SAAS,CAAC3G,MAAM;EAAA;EAAA,CAAA/D,aAAA,GAAAsB,CAAA,WAAI,CAAC,GAAE,CAAC;EAAC;EAAAtB,aAAA,GAAAoB,CAAA;EACzF0B,OAAO,CAACC,GAAG,CAAC;EAAgB;EAAA,CAAA/C,aAAA,GAAAsB,CAAA,WAAAiG,MAAM,CAACiD,QAAQ,EAAEC,WAAW,EAAER,WAAW,EAAErC,OAAO,CAAC,CAAC,CAAC;EAAA;EAAA,CAAA5H,aAAA,GAAAsB,CAAA,WAAI,KAAK,GAAE,CAAC;EAAC;EAAAtB,aAAA,GAAAoB,CAAA;EAC9F0B,OAAO,CAACC,GAAG,CAAC;EAAsB;EAAA,CAAA/C,aAAA,GAAAsB,CAAA,WAAAiG,MAAM,CAACiD,QAAQ,EAAEG,gBAAgB,EAAEC,UAAU,CAAC7G,MAAM;EAAA;EAAA,CAAA/D,aAAA,GAAAsB,CAAA,WAAI,CAAC,GAAE,CAAC;EAAC;EAAAtB,aAAA,GAAAoB,CAAA;EAC/F0B,OAAO,CAACC,GAAG,CAAC,mBAAmBwE,MAAM,CAACM,UAAU,CAACC,aAAa,CAACF,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;EAAC;EAAA5H,aAAA,GAAAoB,CAAA;EAChF0B,OAAO,CAACC,GAAG,CAAC,sBAAsBwE,MAAM,CAACM,UAAU,CAACE,gBAAgB,EAAE,CAAC;EAEvE;EAAA;EAAA/H,aAAA,GAAAoB,CAAA;EACA,IAAImG,MAAM,CAACG,YAAY,EAAE;IAAA;IAAA1H,aAAA,GAAAsB,CAAA;IAAAtB,aAAA,GAAAoB,CAAA;IACvB0B,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;IAAC;IAAA/C,aAAA,GAAAoB,CAAA;IAC3C0B,OAAO,CAACC,GAAG,CAAC,yBAA0BwE,MAAM,CAACG,YAAY,CAACxD,SAAS,CAAC,sBAAsB,CAAY,CAAC0D,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC;IAAC;IAAA5H,aAAA,GAAAoB,CAAA;IAC5H0B,OAAO,CAACC,GAAG,CAAC,kBAAkBwE,MAAM,CAACG,YAAY,CAACxD,SAAS,CAAC,eAAe,CAAC,EAAE,CAAC;IAAC;IAAAlE,aAAA,GAAAoB,CAAA;IAChF0B,OAAO,CAACC,GAAG,CAAC,yBAAyBwE,MAAM,CAACG,YAAY,CAACxD,SAAS,CAAC,sBAAsB,CAAC,SAAS,CAAC;IAAC;IAAAlE,aAAA,GAAAoB,CAAA;IACrG0B,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAEwE,MAAM,CAACG,YAAY,CAACxD,SAAS,CAAC,gBAAgB,CAAY,GAAG,GAAG,EAAE0D,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;EACnH,CAAC;EAAA;EAAA;IAAA5H,aAAA,GAAAsB,CAAA;EAAA;EAAAtB,aAAA,GAAAoB,CAAA;EAED,OAAOmG,MAAM;AACf;AAEA;;;;;AAKO,eAAejF,0CAA0CA,CAAA;EAAA;EAAAtC,aAAA,GAAAqB,CAAA;EAAArB,aAAA,GAAAoB,CAAA;EAC9D0B,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;EAExE;EACA;EACA;EACA;EACA;EAEA,MAAMC,YAAY;EAAA;EAAA,CAAAhD,aAAA,GAAAoB,CAAA,SAAwB;IACxC6B,EAAE,EAAE,mBAAmB;IACvBpC,IAAI,EAAE,+CAA+C;IACrDqC,WAAW,EAAE,oDAAoD;IACjEC,UAAU,EAAE,YAAY;IACxBC,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE,GAAG;IACnBC,mBAAmB,EAAE;MACnBC,WAAW,EAAE,EAAE;MACfC,QAAQ,EAAE,EAAE;MACZC,SAAS,EAAE;KACZ;IACDC,UAAU,EAAE,CACV;MACET,EAAE,EAAE,UAAU;MACdhC,IAAI,EAAE,KAAK;MACX0C,cAAc,EAAE;QACdC,WAAW,EAAE,GAAG;QAChBC,UAAU,EAAE,KAAK;QACjBC,UAAU,EAAE;;KAEf,EACD;MACEb,EAAE,EAAE,WAAW;MACfhC,IAAI,EAAE,MAAM;MACZ0C,cAAc,EAAE;QACdI,MAAM,EAAE,GAAG;QACXC,QAAQ,EAAE,kBAAkB;QAC5BC,SAAS,EAAE;;KAEd,EACD;MACEhB,EAAE,EAAE,oBAAoB;MACxBhC,IAAI,EAAE,YAAY;MAClB0C,cAAc,EAAE;QACd1C,IAAI,EAAE,sBAAsB;QAC5B4J,aAAa,EAAE,EAAE;QACjBC,WAAW,EAAE,EAAE;QACfC,YAAY,EAAE;;KAEjB;GAEJ;EAED;EACA,MAAM7G,SAAS;EAAA;EAAA,CAAAlE,aAAA,GAAAoB,CAAA,SAA2B,CACxC;IACE6B,EAAE,EAAE,oBAAoB;IACxBpC,IAAI,EAAE,oBAAoB;IAC1BqC,WAAW,EAAE,8BAA8B;IAC3CjC,IAAI,EAAE,YAAY;IAClBkD,MAAM,EAAE;MAAEC,OAAO,EAAE,EAAE;MAAEC,OAAO,EAAE;IAAE,CAAE;IACpCC,KAAK,EAAE,QAAQ;IACfC,YAAY,EAAE;GACf,EACD;IACEtB,EAAE,EAAE,iBAAiB;IACrBpC,IAAI,EAAE,yBAAyB;IAC/BqC,WAAW,EAAE,4BAA4B;IACzCjC,IAAI,EAAE,UAAU;IAChBgH,cAAc,EAAE,CAAC,sBAAsB,EAAE,gBAAgB,EAAE,sBAAsB,CAAC;IAClF3D,KAAK,EAAE,cAAc;IACrBC,YAAY,EAAE;GACf,CACF;EAED,MAAMC,WAAW;EAAA;EAAA,CAAAxE,aAAA,GAAAoB,CAAA,SAA6B,CAC5C;IACE6B,EAAE,EAAE,gBAAgB;IACpBpC,IAAI,EAAE,wBAAwB;IAC9BqC,WAAW,EAAE,qCAAqC;IAClDjC,IAAI,EAAE,YAAY;IAClBkD,MAAM,EAAE;MAAEE,OAAO,EAAE;IAAI,CAAE;IACzBC,KAAK,EAAE,KAAK;IACZG,kBAAkB,EAAGC,IAA4B,IAAI;MAAA;MAAA1E,aAAA,GAAAqB,CAAA;MACnD,MAAM6G,QAAQ;MAAA;MAAA,CAAAlI,aAAA,GAAAoB,CAAA;MAAG;MAAA,CAAApB,aAAA,GAAAsB,CAAA,WAAAoD,IAAI,CAACE,IAAI,CAACC,CAAC,IAAI;QAAA;QAAA7E,aAAA,GAAAqB,CAAA;QAAArB,aAAA,GAAAoB,CAAA;QAAA,OAAAyD,CAAC,CAAC5B,EAAE,KAAK,oBAAoB;MAApB,CAAoB,CAAC,EAAEsB,YAAsB;MAAA;MAAA,CAAAvE,aAAA,GAAAsB,CAAA,WAAI,EAAE;MAC5F,MAAM2D,QAAQ;MAAA;MAAA,CAAAjF,aAAA,GAAAoB,CAAA,SAAG,KAAK,IAAI8D,IAAI,CAACC,EAAE,GAAG,CAAC+C,QAAQ,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;MAAC;MAAAlI,aAAA,GAAAoB,CAAA;MACpE,OAAO6D,QAAQ,GAAG,IAAI,CAAC,CAAC;IAC1B;GACD,CACF;EAED,MAAMO,OAAO;EAAA;EAAA,CAAAxF,aAAA,GAAAoB,CAAA,SAAwB;IACnC6B,EAAE,EAAE,yBAAyB;IAC7BpC,IAAI,EAAE,gCAAgC;IACtCqC,WAAW,EAAE,wDAAwD;IACrEuC,mBAAmB,EAAEzC,YAAY;IACjCkB,SAAS;IACTwB,UAAU,EAAE;MACVA,UAAU,EAAE,CAAC;QACXzC,EAAE,EAAE,8BAA8B;QAClC0C,SAAS,EAAElD,yBAAA,CAAAmD,qBAAqB,CAACC,sBAAsB;QACvDC,MAAM,EAAE,GAAG;QACX5C,WAAW,EAAE,iEAAiE;QAC9EuB,kBAAkB,EAAGC,IAA4B,IAAI;UAAA;UAAA1E,aAAA,GAAAqB,CAAA;UACnD;UACA,MAAM6G,QAAQ;UAAA;UAAA,CAAAlI,aAAA,GAAAoB,CAAA;UAAG;UAAA,CAAApB,aAAA,GAAAsB,CAAA,WAAAoD,IAAI,CAACE,IAAI,CAACC,CAAC,IAAI;YAAA;YAAA7E,aAAA,GAAAqB,CAAA;YAAArB,aAAA,GAAAoB,CAAA;YAAA,OAAAyD,CAAC,CAAC5B,EAAE,KAAK,oBAAoB;UAApB,CAAoB,CAAC,EAAEsB,YAAsB;UAAA;UAAA,CAAAvE,aAAA,GAAAsB,CAAA,WAAI,EAAE;UAC5F,MAAM0J,cAAc;UAAA;UAAA,CAAAhL,aAAA,GAAAoB,CAAA;UAAG;UAAA,CAAApB,aAAA,GAAAsB,CAAA,WAAAoD,IAAI,CAACE,IAAI,CAACC,CAAC,IAAI;YAAA;YAAA7E,aAAA,GAAAqB,CAAA;YAAArB,aAAA,GAAAoB,CAAA;YAAA,OAAAyD,CAAC,CAAC5B,EAAE,KAAK,iBAAiB;UAAjB,CAAiB,CAAC,EAAEsB,YAAsB;UAAA;UAAA,CAAAvE,aAAA,GAAAsB,CAAA,WAAI,sBAAsB;UAEnH;UACA;UACA;UACA;UAEA,MAAM2D,QAAQ;UAAA;UAAA,CAAAjF,aAAA,GAAAoB,CAAA,SAAG,KAAK,IAAI8D,IAAI,CAACC,EAAE,GAAG,CAAC+C,QAAQ,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;UACnE,MAAM+C,QAAQ;UAAA;UAAA,CAAAjL,aAAA,GAAAoB,CAAA,SAAG,IAAI,GAAG,GAAG,GAAG,CAAC6D,QAAQ,GAAG,IAAI,KAAK,IAAI,GAAG,CAACiD,QAAQ,GAAG,EAAE,KAAK,IAAI;UAEjF;UACA,MAAMgD,gBAAgB;UAAA;UAAA,CAAAlL,aAAA,GAAAoB,CAAA,SAAG;YACvB,sBAAsB,EAAE,IAAI;YAC5B,gBAAgB,EAAE,IAAI;YACtB,sBAAsB,EAAE;WACzB;UAED,MAAM+J,WAAW;UAAA;UAAA,CAAAnL,aAAA,GAAAoB,CAAA;UAAG;UAAA,CAAApB,aAAA,GAAAsB,CAAA,WAAA4J,gBAAgB,CAACF,cAA+C,CAAC;UAAA;UAAA,CAAAhL,aAAA,GAAAsB,CAAA,WAAI,IAAI;UAC7F,MAAM8J,gBAAgB;UAAA;UAAA,CAAApL,aAAA,GAAAoB,CAAA,SAAG,CAAC6D,QAAQ,GAAG,IAAI,KAAK,CAAC;UAAC;UAAAjF,aAAA,GAAAoB,CAAA;UAEhD,OAAO6J,QAAQ,GAAGE,WAAW,GAAGC,gBAAgB;QAClD,CAAC;QACD9G,KAAK,EAAE;OACR,CAAC;MACFyB,iBAAiB,EAAE;KACpB;IACDvB,WAAW;IACXwB,iBAAiB,EAAE;MACjBC,SAAS,EAAExD,yBAAA,CAAAyD,qBAAqB,CAACmF,cAAc;MAC/CjF,UAAU,EAAE;QACVC,cAAc,EAAE,EAAE;QAClBC,aAAa,EAAE,EAAE;QACjBgF,aAAa,EAAE,GAAG;QAClBC,wBAAwB,EAAE,CAAC,GAAG,EAAE,GAAG;OACpC;MACD9E,eAAe,EAAE;QAAEC,OAAO,EAAE;MAAK,CAAE;MACnCC,mBAAmB,EAAE;QACnBL,aAAa,EAAE,EAAE;QACjBM,cAAc,EAAE,IAAI;QACpBC,eAAe,EAAE;;KAEpB;IACDF,mBAAmB,EAAE;MACnBL,aAAa,EAAE,EAAE;MACjBM,cAAc,EAAE,IAAI;MACpBC,eAAe,EAAE;;GAEpB;EAED;EACA,MAAMU,MAAM;EAAA;EAAA,CAAAvH,aAAA,GAAAoB,CAAA,SAAG,MAAMuB,0BAAA,CAAA6I,wBAAwB,CAACC,cAAc,CAC1DjG,OAAO,EACP/C,yBAAA,CAAAyD,qBAAqB,CAACmF,cAAc,CACrC;EAAC;EAAArL,aAAA,GAAAoB,CAAA;EAEF0B,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;EAAC;EAAA/C,aAAA,GAAAoB,CAAA;EAChD0B,OAAO,CAACC,GAAG,CAAC,WAAWwE,MAAM,CAACE,MAAM,EAAE,CAAC;EAAC;EAAAzH,aAAA,GAAAoB,CAAA;EACxC0B,OAAO,CAACC,GAAG,CAAC,iBAAiBwE,MAAM,CAACG,YAAY,CAACC,OAAO,CAACC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC;EAAC;EAAA5H,aAAA,GAAAoB,CAAA;EAC5E0B,OAAO,CAACC,GAAG,CAAC,0BAA2BwE,MAAM,CAACG,YAAY,CAACxD,SAAS,CAAC,oBAAoB,CAAY,CAAC0D,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC;EAAC;EAAA5H,aAAA,GAAAoB,CAAA;EAC3H0B,OAAO,CAACC,GAAG,CAAC,4BAA4BwE,MAAM,CAACG,YAAY,CAACxD,SAAS,CAAC,iBAAiB,CAAC,EAAE,CAAC;EAAC;EAAAlE,aAAA,GAAAoB,CAAA;EAC5F0B,OAAO,CAACC,GAAG,CAAC,mBAAmBwE,MAAM,CAACM,UAAU,CAACC,aAAa,CAACF,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;EAAC;EAAA5H,aAAA,GAAAoB,CAAA;EAEhF,OAAOmG,MAAM;AACf;AAEA;;;AAGO,eAAe/E,0BAA0BA,CAAA;EAAA;EAAAxC,aAAA,GAAAqB,CAAA;EAAArB,aAAA,GAAAoB,CAAA;EAC9C0B,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;EAAC;EAAA/C,aAAA,GAAAoB,CAAA;EAE3E,IAAI;IAAA;IAAApB,aAAA,GAAAoB,CAAA;IACF;IACA,MAAMc,+BAA+B,EAAE;IAEvC;IAAA;IAAAlC,aAAA,GAAAoB,CAAA;IACA,MAAMgB,mCAAmC,EAAE;IAE3C;IAAA;IAAApC,aAAA,GAAAoB,CAAA;IACA,MAAMkB,0CAA0C,EAAE;IAAC;IAAAtC,aAAA,GAAAoB,CAAA;IAEnD0B,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;IAAC;IAAA/C,aAAA,GAAAoB,CAAA;IACrE0B,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;IAAC;IAAA/C,aAAA,GAAAoB,CAAA;IAC7B0B,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;IAAC;IAAA/C,aAAA,GAAAoB,CAAA;IACjE0B,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;IAAC;IAAA/C,aAAA,GAAAoB,CAAA;IACxE0B,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;IAAC;IAAA/C,aAAA,GAAAoB,CAAA;IAClE0B,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;IAAC;IAAA/C,aAAA,GAAAoB,CAAA;IACvD0B,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;EAErE,CAAC,CAAC,OAAO2I,KAAK,EAAE;IAAA;IAAA1L,aAAA,GAAAoB,CAAA;IACd0B,OAAO,CAAC4I,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;IAAC;IAAA1L,aAAA,GAAAoB,CAAA;IAC/D,MAAMsK,KAAK;EACb;AACF", "ignoreList": []}