6c24935d1c10ed7ea371f2482efc9591
"use strict";

/**
 * Optimization Framework Usage Examples
 *
 * Demonstrates practical usage of the System Optimization Framework with real HVAC scenarios:
 * - Single-objective optimization examples
 * - Multi-objective optimization with trade-off analysis
 * - Integration with existing Phase 1/2/3 Priority 1 components
 * - Real-world HVAC system optimization scenarios
 * - Best practices and common patterns
 *
 * @version 3.0.0
 * <AUTHOR> Suite Development Team
 */
/* istanbul ignore next */
function cov_jcfe2znb1() {
  var path = "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\examples\\OptimizationExamples.ts";
  var hash = "1fff0c043ede7d7bc370592d3d978739f1359d2d";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\examples\\OptimizationExamples.ts",
    statementMap: {
      "0": {
        start: {
          line: 15,
          column: 0
        },
        end: {
          line: 15,
          column: 62
        }
      },
      "1": {
        start: {
          line: 16,
          column: 0
        },
        end: {
          line: 16,
          column: 74
        }
      },
      "2": {
        start: {
          line: 17,
          column: 0
        },
        end: {
          line: 17,
          column: 65
        }
      },
      "3": {
        start: {
          line: 18,
          column: 0
        },
        end: {
          line: 18,
          column: 82
        }
      },
      "4": {
        start: {
          line: 19,
          column: 0
        },
        end: {
          line: 19,
          column: 73
        }
      },
      "5": {
        start: {
          line: 20,
          column: 0
        },
        end: {
          line: 20,
          column: 96
        }
      },
      "6": {
        start: {
          line: 21,
          column: 0
        },
        end: {
          line: 21,
          column: 76
        }
      },
      "7": {
        start: {
          line: 22,
          column: 0
        },
        end: {
          line: 22,
          column: 64
        }
      },
      "8": {
        start: {
          line: 23,
          column: 34
        },
        end: {
          line: 23,
          column: 77
        }
      },
      "9": {
        start: {
          line: 24,
          column: 35
        },
        end: {
          line: 24,
          column: 73
        }
      },
      "10": {
        start: {
          line: 25,
          column: 27
        },
        end: {
          line: 25,
          column: 68
        }
      },
      "11": {
        start: {
          line: 26,
          column: 46
        },
        end: {
          line: 26,
          column: 95
        }
      },
      "12": {
        start: {
          line: 33,
          column: 4
        },
        end: {
          line: 33,
          column: 63
        }
      },
      "13": {
        start: {
          line: 35,
          column: 25
        },
        end: {
          line: 67,
          column: 5
        }
      },
      "14": {
        start: {
          line: 69,
          column: 22
        },
        end: {
          line: 97,
          column: 5
        }
      },
      "15": {
        start: {
          line: 99,
          column: 24
        },
        end: {
          line: 132,
          column: 5
        }
      },
      "16": {
        start: {
          line: 108,
          column: 33
        },
        end: {
          line: 108,
          column: 89
        }
      },
      "17": {
        start: {
          line: 108,
          column: 48
        },
        end: {
          line: 108,
          column: 68
        }
      },
      "18": {
        start: {
          line: 109,
          column: 41
        },
        end: {
          line: 109,
          column: 65
        }
      },
      "19": {
        start: {
          line: 110,
          column: 16
        },
        end: {
          line: 110,
          column: 48
        }
      },
      "20": {
        start: {
          line: 121,
          column: 37
        },
        end: {
          line: 121,
          column: 102
        }
      },
      "21": {
        start: {
          line: 121,
          column: 52
        },
        end: {
          line: 121,
          column: 81
        }
      },
      "22": {
        start: {
          line: 122,
          column: 39
        },
        end: {
          line: 122,
          column: 106
        }
      },
      "23": {
        start: {
          line: 122,
          column: 54
        },
        end: {
          line: 122,
          column: 85
        }
      },
      "24": {
        start: {
          line: 123,
          column: 33
        },
        end: {
          line: 123,
          column: 89
        }
      },
      "25": {
        start: {
          line: 123,
          column: 48
        },
        end: {
          line: 123,
          column: 68
        }
      },
      "26": {
        start: {
          line: 125,
          column: 33
        },
        end: {
          line: 125,
          column: 106
        }
      },
      "27": {
        start: {
          line: 126,
          column: 33
        },
        end: {
          line: 126,
          column: 101
        }
      },
      "28": {
        start: {
          line: 127,
          column: 35
        },
        end: {
          line: 127,
          column: 105
        }
      },
      "29": {
        start: {
          line: 128,
          column: 34
        },
        end: {
          line: 128,
          column: 61
        }
      },
      "30": {
        start: {
          line: 129,
          column: 16
        },
        end: {
          line: 129,
          column: 39
        }
      },
      "31": {
        start: {
          line: 134,
          column: 30
        },
        end: {
          line: 143,
          column: 5
        }
      },
      "32": {
        start: {
          line: 135,
          column: 29
        },
        end: {
          line: 135,
          column: 94
        }
      },
      "33": {
        start: {
          line: 135,
          column: 44
        },
        end: {
          line: 135,
          column: 73
        }
      },
      "34": {
        start: {
          line: 136,
          column: 31
        },
        end: {
          line: 136,
          column: 98
        }
      },
      "35": {
        start: {
          line: 136,
          column: 46
        },
        end: {
          line: 136,
          column: 77
        }
      },
      "36": {
        start: {
          line: 137,
          column: 25
        },
        end: {
          line: 137,
          column: 81
        }
      },
      "37": {
        start: {
          line: 137,
          column: 40
        },
        end: {
          line: 137,
          column: 60
        }
      },
      "38": {
        start: {
          line: 139,
          column: 25
        },
        end: {
          line: 139,
          column: 98
        }
      },
      "39": {
        start: {
          line: 140,
          column: 25
        },
        end: {
          line: 140,
          column: 93
        }
      },
      "40": {
        start: {
          line: 141,
          column: 27
        },
        end: {
          line: 141,
          column: 97
        }
      },
      "41": {
        start: {
          line: 142,
          column: 8
        },
        end: {
          line: 142,
          column: 43
        }
      },
      "42": {
        start: {
          line: 145,
          column: 20
        },
        end: {
          line: 183,
          column: 5
        }
      },
      "43": {
        start: {
          line: 185,
          column: 15
        },
        end: {
          line: 193,
          column: 6
        }
      },
      "44": {
        start: {
          line: 194,
          column: 32
        },
        end: {
          line: 194,
          column: 74
        }
      },
      "45": {
        start: {
          line: 194,
          column: 53
        },
        end: {
          line: 194,
          column: 73
        }
      },
      "46": {
        start: {
          line: 195,
          column: 19
        },
        end: {
          line: 195,
          column: 85
        }
      },
      "47": {
        start: {
          line: 196,
          column: 4
        },
        end: {
          line: 196,
          column: 41
        }
      },
      "48": {
        start: {
          line: 197,
          column: 4
        },
        end: {
          line: 197,
          column: 44
        }
      },
      "49": {
        start: {
          line: 198,
          column: 4
        },
        end: {
          line: 198,
          column: 80
        }
      },
      "50": {
        start: {
          line: 199,
          column: 4
        },
        end: {
          line: 199,
          column: 112
        }
      },
      "51": {
        start: {
          line: 200,
          column: 4
        },
        end: {
          line: 200,
          column: 116
        }
      },
      "52": {
        start: {
          line: 201,
          column: 4
        },
        end: {
          line: 201,
          column: 88
        }
      },
      "53": {
        start: {
          line: 202,
          column: 4
        },
        end: {
          line: 202,
          column: 84
        }
      },
      "54": {
        start: {
          line: 203,
          column: 4
        },
        end: {
          line: 203,
          column: 76
        }
      },
      "55": {
        start: {
          line: 204,
          column: 4
        },
        end: {
          line: 204,
          column: 18
        }
      },
      "56": {
        start: {
          line: 212,
          column: 4
        },
        end: {
          line: 212,
          column: 74
        }
      },
      "57": {
        start: {
          line: 214,
          column: 25
        },
        end: {
          line: 247,
          column: 5
        }
      },
      "58": {
        start: {
          line: 249,
          column: 22
        },
        end: {
          line: 286,
          column: 5
        }
      },
      "59": {
        start: {
          line: 288,
          column: 24
        },
        end: {
          line: 315,
          column: 5
        }
      },
      "60": {
        start: {
          line: 298,
          column: 16
        },
        end: {
          line: 298,
          column: 37
        }
      },
      "61": {
        start: {
          line: 309,
          column: 33
        },
        end: {
          line: 309,
          column: 100
        }
      },
      "62": {
        start: {
          line: 309,
          column: 48
        },
        end: {
          line: 309,
          column: 79
        }
      },
      "63": {
        start: {
          line: 310,
          column: 33
        },
        end: {
          line: 310,
          column: 83
        }
      },
      "64": {
        start: {
          line: 311,
          column: 35
        },
        end: {
          line: 311,
          column: 72
        }
      },
      "65": {
        start: {
          line: 312,
          column: 16
        },
        end: {
          line: 312,
          column: 39
        }
      },
      "66": {
        start: {
          line: 317,
          column: 34
        },
        end: {
          line: 331,
          column: 5
        }
      },
      "67": {
        start: {
          line: 318,
          column: 25
        },
        end: {
          line: 318,
          column: 92
        }
      },
      "68": {
        start: {
          line: 318,
          column: 40
        },
        end: {
          line: 318,
          column: 71
        }
      },
      "69": {
        start: {
          line: 319,
          column: 25
        },
        end: {
          line: 319,
          column: 101
        }
      },
      "70": {
        start: {
          line: 319,
          column: 40
        },
        end: {
          line: 319,
          column: 64
        }
      },
      "71": {
        start: {
          line: 321,
          column: 33
        },
        end: {
          line: 326,
          column: 9
        }
      },
      "72": {
        start: {
          line: 327,
          column: 32
        },
        end: {
          line: 327,
          column: 65
        }
      },
      "73": {
        start: {
          line: 328,
          column: 25
        },
        end: {
          line: 328,
          column: 75
        }
      },
      "74": {
        start: {
          line: 329,
          column: 29
        },
        end: {
          line: 329,
          column: 112
        }
      },
      "75": {
        start: {
          line: 330,
          column: 8
        },
        end: {
          line: 330,
          column: 34
        }
      },
      "76": {
        start: {
          line: 332,
          column: 39
        },
        end: {
          line: 340,
          column: 5
        }
      },
      "77": {
        start: {
          line: 333,
          column: 25
        },
        end: {
          line: 333,
          column: 92
        }
      },
      "78": {
        start: {
          line: 333,
          column: 40
        },
        end: {
          line: 333,
          column: 71
        }
      },
      "79": {
        start: {
          line: 334,
          column: 30
        },
        end: {
          line: 334,
          column: 93
        }
      },
      "80": {
        start: {
          line: 334,
          column: 45
        },
        end: {
          line: 334,
          column: 70
        }
      },
      "81": {
        start: {
          line: 336,
          column: 29
        },
        end: {
          line: 336,
          column: 56
        }
      },
      "82": {
        start: {
          line: 337,
          column: 25
        },
        end: {
          line: 337,
          column: 72
        }
      },
      "83": {
        start: {
          line: 338,
          column: 40
        },
        end: {
          line: 338,
          column: 69
        }
      },
      "84": {
        start: {
          line: 339,
          column: 8
        },
        end: {
          line: 339,
          column: 39
        }
      },
      "85": {
        start: {
          line: 341,
          column: 31
        },
        end: {
          line: 370,
          column: 5
        }
      },
      "86": {
        start: {
          line: 342,
          column: 25
        },
        end: {
          line: 342,
          column: 92
        }
      },
      "87": {
        start: {
          line: 342,
          column: 40
        },
        end: {
          line: 342,
          column: 71
        }
      },
      "88": {
        start: {
          line: 343,
          column: 25
        },
        end: {
          line: 343,
          column: 101
        }
      },
      "89": {
        start: {
          line: 343,
          column: 40
        },
        end: {
          line: 343,
          column: 64
        }
      },
      "90": {
        start: {
          line: 344,
          column: 36
        },
        end: {
          line: 344,
          column: 102
        }
      },
      "91": {
        start: {
          line: 344,
          column: 51
        },
        end: {
          line: 344,
          column: 82
        }
      },
      "92": {
        start: {
          line: 345,
          column: 30
        },
        end: {
          line: 345,
          column: 93
        }
      },
      "93": {
        start: {
          line: 345,
          column: 45
        },
        end: {
          line: 345,
          column: 70
        }
      },
      "94": {
        start: {
          line: 347,
          column: 30
        },
        end: {
          line: 352,
          column: 9
        }
      },
      "95": {
        start: {
          line: 354,
          column: 35
        },
        end: {
          line: 360,
          column: 9
        }
      },
      "96": {
        start: {
          line: 361,
          column: 29
        },
        end: {
          line: 361,
          column: 58
        }
      },
      "97": {
        start: {
          line: 362,
          column: 32
        },
        end: {
          line: 362,
          column: 63
        }
      },
      "98": {
        start: {
          line: 363,
          column: 25
        },
        end: {
          line: 363,
          column: 55
        }
      },
      "99": {
        start: {
          line: 364,
          column: 31
        },
        end: {
          line: 364,
          column: 72
        }
      },
      "100": {
        start: {
          line: 365,
          column: 27
        },
        end: {
          line: 365,
          column: 65
        }
      },
      "101": {
        start: {
          line: 367,
          column: 34
        },
        end: {
          line: 367,
          column: 66
        }
      },
      "102": {
        start: {
          line: 368,
          column: 30
        },
        end: {
          line: 368,
          column: 59
        }
      },
      "103": {
        start: {
          line: 369,
          column: 8
        },
        end: {
          line: 369,
          column: 70
        }
      },
      "104": {
        start: {
          line: 372,
          column: 20
        },
        end: {
          line: 428,
          column: 5
        }
      },
      "105": {
        start: {
          line: 430,
          column: 24
        },
        end: {
          line: 446,
          column: 6
        }
      },
      "106": {
        start: {
          line: 447,
          column: 32
        },
        end: {
          line: 447,
          column: 74
        }
      },
      "107": {
        start: {
          line: 447,
          column: 53
        },
        end: {
          line: 447,
          column: 73
        }
      },
      "108": {
        start: {
          line: 448,
          column: 19
        },
        end: {
          line: 448,
          column: 162
        }
      },
      "109": {
        start: {
          line: 449,
          column: 4
        },
        end: {
          line: 449,
          column: 57
        }
      },
      "110": {
        start: {
          line: 450,
          column: 4
        },
        end: {
          line: 450,
          column: 44
        }
      },
      "111": {
        start: {
          line: 451,
          column: 4
        },
        end: {
          line: 451,
          column: 93
        }
      },
      "112": {
        start: {
          line: 452,
          column: 4
        },
        end: {
          line: 452,
          column: 98
        }
      },
      "113": {
        start: {
          line: 453,
          column: 4
        },
        end: {
          line: 453,
          column: 99
        }
      },
      "114": {
        start: {
          line: 454,
          column: 4
        },
        end: {
          line: 454,
          column: 84
        }
      },
      "115": {
        start: {
          line: 455,
          column: 4
        },
        end: {
          line: 455,
          column: 76
        }
      },
      "116": {
        start: {
          line: 457,
          column: 4
        },
        end: {
          line: 463,
          column: 5
        }
      },
      "117": {
        start: {
          line: 458,
          column: 8
        },
        end: {
          line: 458,
          column: 51
        }
      },
      "118": {
        start: {
          line: 459,
          column: 8
        },
        end: {
          line: 459,
          column: 120
        }
      },
      "119": {
        start: {
          line: 460,
          column: 8
        },
        end: {
          line: 460,
          column: 88
        }
      },
      "120": {
        start: {
          line: 461,
          column: 8
        },
        end: {
          line: 461,
          column: 109
        }
      },
      "121": {
        start: {
          line: 462,
          column: 8
        },
        end: {
          line: 462,
          column: 110
        }
      },
      "122": {
        start: {
          line: 464,
          column: 4
        },
        end: {
          line: 464,
          column: 18
        }
      },
      "123": {
        start: {
          line: 472,
          column: 4
        },
        end: {
          line: 472,
          column: 77
        }
      },
      "124": {
        start: {
          line: 478,
          column: 25
        },
        end: {
          line: 520,
          column: 5
        }
      },
      "125": {
        start: {
          line: 522,
          column: 22
        },
        end: {
          line: 541,
          column: 5
        }
      },
      "126": {
        start: {
          line: 542,
          column: 24
        },
        end: {
          line: 556,
          column: 5
        }
      },
      "127": {
        start: {
          line: 551,
          column: 33
        },
        end: {
          line: 551,
          column: 98
        }
      },
      "128": {
        start: {
          line: 551,
          column: 48
        },
        end: {
          line: 551,
          column: 77
        }
      },
      "129": {
        start: {
          line: 552,
          column: 33
        },
        end: {
          line: 552,
          column: 83
        }
      },
      "130": {
        start: {
          line: 553,
          column: 16
        },
        end: {
          line: 553,
          column: 39
        }
      },
      "131": {
        start: {
          line: 557,
          column: 20
        },
        end: {
          line: 614,
          column: 5
        }
      },
      "132": {
        start: {
          line: 571,
          column: 41
        },
        end: {
          line: 571,
          column: 106
        }
      },
      "133": {
        start: {
          line: 571,
          column: 56
        },
        end: {
          line: 571,
          column: 85
        }
      },
      "134": {
        start: {
          line: 572,
          column: 47
        },
        end: {
          line: 572,
          column: 129
        }
      },
      "135": {
        start: {
          line: 572,
          column: 62
        },
        end: {
          line: 572,
          column: 88
        }
      },
      "136": {
        start: {
          line: 577,
          column: 41
        },
        end: {
          line: 577,
          column: 91
        }
      },
      "137": {
        start: {
          line: 578,
          column: 41
        },
        end: {
          line: 578,
          column: 105
        }
      },
      "138": {
        start: {
          line: 580,
          column: 49
        },
        end: {
          line: 584,
          column: 25
        }
      },
      "139": {
        start: {
          line: 585,
          column: 44
        },
        end: {
          line: 585,
          column: 84
        }
      },
      "140": {
        start: {
          line: 586,
          column: 49
        },
        end: {
          line: 586,
          column: 71
        }
      },
      "141": {
        start: {
          line: 587,
          column: 24
        },
        end: {
          line: 587,
          column: 73
        }
      },
      "142": {
        start: {
          line: 616,
          column: 19
        },
        end: {
          line: 616,
          column: 164
        }
      },
      "143": {
        start: {
          line: 617,
          column: 4
        },
        end: {
          line: 617,
          column: 52
        }
      },
      "144": {
        start: {
          line: 618,
          column: 4
        },
        end: {
          line: 618,
          column: 44
        }
      },
      "145": {
        start: {
          line: 619,
          column: 4
        },
        end: {
          line: 619,
          column: 80
        }
      },
      "146": {
        start: {
          line: 620,
          column: 4
        },
        end: {
          line: 620,
          column: 115
        }
      },
      "147": {
        start: {
          line: 621,
          column: 4
        },
        end: {
          line: 621,
          column: 96
        }
      },
      "148": {
        start: {
          line: 622,
          column: 4
        },
        end: {
          line: 622,
          column: 84
        }
      },
      "149": {
        start: {
          line: 623,
          column: 4
        },
        end: {
          line: 623,
          column: 18
        }
      },
      "150": {
        start: {
          line: 629,
          column: 4
        },
        end: {
          line: 629,
          column: 79
        }
      },
      "151": {
        start: {
          line: 630,
          column: 4
        },
        end: {
          line: 648,
          column: 5
        }
      },
      "152": {
        start: {
          line: 632,
          column: 8
        },
        end: {
          line: 632,
          column: 48
        }
      },
      "153": {
        start: {
          line: 634,
          column: 8
        },
        end: {
          line: 634,
          column: 52
        }
      },
      "154": {
        start: {
          line: 636,
          column: 8
        },
        end: {
          line: 636,
          column: 59
        }
      },
      "155": {
        start: {
          line: 637,
          column: 8
        },
        end: {
          line: 637,
          column: 77
        }
      },
      "156": {
        start: {
          line: 638,
          column: 8
        },
        end: {
          line: 638,
          column: 37
        }
      },
      "157": {
        start: {
          line: 639,
          column: 8
        },
        end: {
          line: 639,
          column: 73
        }
      },
      "158": {
        start: {
          line: 640,
          column: 8
        },
        end: {
          line: 640,
          column: 80
        }
      },
      "159": {
        start: {
          line: 641,
          column: 8
        },
        end: {
          line: 641,
          column: 74
        }
      },
      "160": {
        start: {
          line: 642,
          column: 8
        },
        end: {
          line: 642,
          column: 63
        }
      },
      "161": {
        start: {
          line: 643,
          column: 8
        },
        end: {
          line: 643,
          column: 76
        }
      },
      "162": {
        start: {
          line: 646,
          column: 8
        },
        end: {
          line: 646,
          column: 71
        }
      },
      "163": {
        start: {
          line: 647,
          column: 8
        },
        end: {
          line: 647,
          column: 20
        }
      }
    },
    fnMap: {
      "0": {
        name: "example1_DuctSizingOptimization",
        decl: {
          start: {
            line: 32,
            column: 15
          },
          end: {
            line: 32,
            column: 46
          }
        },
        loc: {
          start: {
            line: 32,
            column: 49
          },
          end: {
            line: 205,
            column: 1
          }
        },
        line: 32
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 107,
            column: 32
          },
          end: {
            line: 107,
            column: 33
          }
        },
        loc: {
          start: {
            line: 107,
            column: 42
          },
          end: {
            line: 111,
            column: 13
          }
        },
        line: 107
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 108,
            column: 43
          },
          end: {
            line: 108,
            column: 44
          }
        },
        loc: {
          start: {
            line: 108,
            column: 48
          },
          end: {
            line: 108,
            column: 68
          }
        },
        line: 108
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 120,
            column: 32
          },
          end: {
            line: 120,
            column: 33
          }
        },
        loc: {
          start: {
            line: 120,
            column: 42
          },
          end: {
            line: 130,
            column: 13
          }
        },
        line: 120
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 121,
            column: 47
          },
          end: {
            line: 121,
            column: 48
          }
        },
        loc: {
          start: {
            line: 121,
            column: 52
          },
          end: {
            line: 121,
            column: 81
          }
        },
        line: 121
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 122,
            column: 49
          },
          end: {
            line: 122,
            column: 50
          }
        },
        loc: {
          start: {
            line: 122,
            column: 54
          },
          end: {
            line: 122,
            column: 85
          }
        },
        line: 122
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 123,
            column: 43
          },
          end: {
            line: 123,
            column: 44
          }
        },
        loc: {
          start: {
            line: 123,
            column: 48
          },
          end: {
            line: 123,
            column: 68
          }
        },
        line: 123
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 134,
            column: 30
          },
          end: {
            line: 134,
            column: 31
          }
        },
        loc: {
          start: {
            line: 134,
            column: 40
          },
          end: {
            line: 143,
            column: 5
          }
        },
        line: 134
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 135,
            column: 39
          },
          end: {
            line: 135,
            column: 40
          }
        },
        loc: {
          start: {
            line: 135,
            column: 44
          },
          end: {
            line: 135,
            column: 73
          }
        },
        line: 135
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 136,
            column: 41
          },
          end: {
            line: 136,
            column: 42
          }
        },
        loc: {
          start: {
            line: 136,
            column: 46
          },
          end: {
            line: 136,
            column: 77
          }
        },
        line: 136
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 137,
            column: 35
          },
          end: {
            line: 137,
            column: 36
          }
        },
        loc: {
          start: {
            line: 137,
            column: 40
          },
          end: {
            line: 137,
            column: 60
          }
        },
        line: 137
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 194,
            column: 48
          },
          end: {
            line: 194,
            column: 49
          }
        },
        loc: {
          start: {
            line: 194,
            column: 53
          },
          end: {
            line: 194,
            column: 73
          }
        },
        line: 194
      },
      "12": {
        name: "example2_MultiObjectiveOptimization",
        decl: {
          start: {
            line: 211,
            column: 15
          },
          end: {
            line: 211,
            column: 50
          }
        },
        loc: {
          start: {
            line: 211,
            column: 53
          },
          end: {
            line: 465,
            column: 1
          }
        },
        line: 211
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 296,
            column: 32
          },
          end: {
            line: 296,
            column: 33
          }
        },
        loc: {
          start: {
            line: 296,
            column: 42
          },
          end: {
            line: 299,
            column: 13
          }
        },
        line: 296
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 308,
            column: 32
          },
          end: {
            line: 308,
            column: 33
          }
        },
        loc: {
          start: {
            line: 308,
            column: 42
          },
          end: {
            line: 313,
            column: 13
          }
        },
        line: 308
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 309,
            column: 43
          },
          end: {
            line: 309,
            column: 44
          }
        },
        loc: {
          start: {
            line: 309,
            column: 48
          },
          end: {
            line: 309,
            column: 79
          }
        },
        line: 309
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 317,
            column: 34
          },
          end: {
            line: 317,
            column: 35
          }
        },
        loc: {
          start: {
            line: 317,
            column: 44
          },
          end: {
            line: 331,
            column: 5
          }
        },
        line: 317
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 318,
            column: 35
          },
          end: {
            line: 318,
            column: 36
          }
        },
        loc: {
          start: {
            line: 318,
            column: 40
          },
          end: {
            line: 318,
            column: 71
          }
        },
        line: 318
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 319,
            column: 35
          },
          end: {
            line: 319,
            column: 36
          }
        },
        loc: {
          start: {
            line: 319,
            column: 40
          },
          end: {
            line: 319,
            column: 64
          }
        },
        line: 319
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 332,
            column: 39
          },
          end: {
            line: 332,
            column: 40
          }
        },
        loc: {
          start: {
            line: 332,
            column: 49
          },
          end: {
            line: 340,
            column: 5
          }
        },
        line: 332
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 333,
            column: 35
          },
          end: {
            line: 333,
            column: 36
          }
        },
        loc: {
          start: {
            line: 333,
            column: 40
          },
          end: {
            line: 333,
            column: 71
          }
        },
        line: 333
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 334,
            column: 40
          },
          end: {
            line: 334,
            column: 41
          }
        },
        loc: {
          start: {
            line: 334,
            column: 45
          },
          end: {
            line: 334,
            column: 70
          }
        },
        line: 334
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 341,
            column: 31
          },
          end: {
            line: 341,
            column: 32
          }
        },
        loc: {
          start: {
            line: 341,
            column: 41
          },
          end: {
            line: 370,
            column: 5
          }
        },
        line: 341
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 342,
            column: 35
          },
          end: {
            line: 342,
            column: 36
          }
        },
        loc: {
          start: {
            line: 342,
            column: 40
          },
          end: {
            line: 342,
            column: 71
          }
        },
        line: 342
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 343,
            column: 35
          },
          end: {
            line: 343,
            column: 36
          }
        },
        loc: {
          start: {
            line: 343,
            column: 40
          },
          end: {
            line: 343,
            column: 64
          }
        },
        line: 343
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 344,
            column: 46
          },
          end: {
            line: 344,
            column: 47
          }
        },
        loc: {
          start: {
            line: 344,
            column: 51
          },
          end: {
            line: 344,
            column: 82
          }
        },
        line: 344
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 345,
            column: 40
          },
          end: {
            line: 345,
            column: 41
          }
        },
        loc: {
          start: {
            line: 345,
            column: 45
          },
          end: {
            line: 345,
            column: 70
          }
        },
        line: 345
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 447,
            column: 48
          },
          end: {
            line: 447,
            column: 49
          }
        },
        loc: {
          start: {
            line: 447,
            column: 53
          },
          end: {
            line: 447,
            column: 73
          }
        },
        line: 447
      },
      "28": {
        name: "example3_IntegrationWithExistingComponents",
        decl: {
          start: {
            line: 471,
            column: 15
          },
          end: {
            line: 471,
            column: 57
          }
        },
        loc: {
          start: {
            line: 471,
            column: 60
          },
          end: {
            line: 624,
            column: 1
          }
        },
        line: 471
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 550,
            column: 32
          },
          end: {
            line: 550,
            column: 33
          }
        },
        loc: {
          start: {
            line: 550,
            column: 42
          },
          end: {
            line: 554,
            column: 13
          }
        },
        line: 550
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 551,
            column: 43
          },
          end: {
            line: 551,
            column: 44
          }
        },
        loc: {
          start: {
            line: 551,
            column: 48
          },
          end: {
            line: 551,
            column: 77
          }
        },
        line: 551
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 569,
            column: 40
          },
          end: {
            line: 569,
            column: 41
          }
        },
        loc: {
          start: {
            line: 569,
            column: 50
          },
          end: {
            line: 588,
            column: 21
          }
        },
        line: 569
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 571,
            column: 51
          },
          end: {
            line: 571,
            column: 52
          }
        },
        loc: {
          start: {
            line: 571,
            column: 56
          },
          end: {
            line: 571,
            column: 85
          }
        },
        line: 571
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 572,
            column: 57
          },
          end: {
            line: 572,
            column: 58
          }
        },
        loc: {
          start: {
            line: 572,
            column: 62
          },
          end: {
            line: 572,
            column: 88
          }
        },
        line: 572
      },
      "34": {
        name: "runAllOptimizationExamples",
        decl: {
          start: {
            line: 628,
            column: 15
          },
          end: {
            line: 628,
            column: 41
          }
        },
        loc: {
          start: {
            line: 628,
            column: 44
          },
          end: {
            line: 649,
            column: 1
          }
        },
        line: 628
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 108,
            column: 33
          },
          end: {
            line: 108,
            column: 89
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 108,
            column: 33
          },
          end: {
            line: 108,
            column: 83
          }
        }, {
          start: {
            line: 108,
            column: 87
          },
          end: {
            line: 108,
            column: 89
          }
        }],
        line: 108
      },
      "1": {
        loc: {
          start: {
            line: 121,
            column: 37
          },
          end: {
            line: 121,
            column: 102
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 121,
            column: 37
          },
          end: {
            line: 121,
            column: 96
          }
        }, {
          start: {
            line: 121,
            column: 100
          },
          end: {
            line: 121,
            column: 102
          }
        }],
        line: 121
      },
      "2": {
        loc: {
          start: {
            line: 122,
            column: 39
          },
          end: {
            line: 122,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 122,
            column: 39
          },
          end: {
            line: 122,
            column: 100
          }
        }, {
          start: {
            line: 122,
            column: 104
          },
          end: {
            line: 122,
            column: 106
          }
        }],
        line: 122
      },
      "3": {
        loc: {
          start: {
            line: 123,
            column: 33
          },
          end: {
            line: 123,
            column: 89
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 123,
            column: 33
          },
          end: {
            line: 123,
            column: 83
          }
        }, {
          start: {
            line: 123,
            column: 87
          },
          end: {
            line: 123,
            column: 89
          }
        }],
        line: 123
      },
      "4": {
        loc: {
          start: {
            line: 135,
            column: 29
          },
          end: {
            line: 135,
            column: 94
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 135,
            column: 29
          },
          end: {
            line: 135,
            column: 88
          }
        }, {
          start: {
            line: 135,
            column: 92
          },
          end: {
            line: 135,
            column: 94
          }
        }],
        line: 135
      },
      "5": {
        loc: {
          start: {
            line: 136,
            column: 31
          },
          end: {
            line: 136,
            column: 98
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 136,
            column: 31
          },
          end: {
            line: 136,
            column: 92
          }
        }, {
          start: {
            line: 136,
            column: 96
          },
          end: {
            line: 136,
            column: 98
          }
        }],
        line: 136
      },
      "6": {
        loc: {
          start: {
            line: 137,
            column: 25
          },
          end: {
            line: 137,
            column: 81
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 137,
            column: 25
          },
          end: {
            line: 137,
            column: 75
          }
        }, {
          start: {
            line: 137,
            column: 79
          },
          end: {
            line: 137,
            column: 81
          }
        }],
        line: 137
      },
      "7": {
        loc: {
          start: {
            line: 309,
            column: 33
          },
          end: {
            line: 309,
            column: 100
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 309,
            column: 33
          },
          end: {
            line: 309,
            column: 94
          }
        }, {
          start: {
            line: 309,
            column: 98
          },
          end: {
            line: 309,
            column: 100
          }
        }],
        line: 309
      },
      "8": {
        loc: {
          start: {
            line: 318,
            column: 25
          },
          end: {
            line: 318,
            column: 92
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 318,
            column: 25
          },
          end: {
            line: 318,
            column: 86
          }
        }, {
          start: {
            line: 318,
            column: 90
          },
          end: {
            line: 318,
            column: 92
          }
        }],
        line: 318
      },
      "9": {
        loc: {
          start: {
            line: 319,
            column: 25
          },
          end: {
            line: 319,
            column: 101
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 319,
            column: 25
          },
          end: {
            line: 319,
            column: 79
          }
        }, {
          start: {
            line: 319,
            column: 83
          },
          end: {
            line: 319,
            column: 101
          }
        }],
        line: 319
      },
      "10": {
        loc: {
          start: {
            line: 327,
            column: 32
          },
          end: {
            line: 327,
            column: 65
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 327,
            column: 32
          },
          end: {
            line: 327,
            column: 58
          }
        }, {
          start: {
            line: 327,
            column: 62
          },
          end: {
            line: 327,
            column: 65
          }
        }],
        line: 327
      },
      "11": {
        loc: {
          start: {
            line: 333,
            column: 25
          },
          end: {
            line: 333,
            column: 92
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 333,
            column: 25
          },
          end: {
            line: 333,
            column: 86
          }
        }, {
          start: {
            line: 333,
            column: 90
          },
          end: {
            line: 333,
            column: 92
          }
        }],
        line: 333
      },
      "12": {
        loc: {
          start: {
            line: 334,
            column: 30
          },
          end: {
            line: 334,
            column: 93
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 334,
            column: 30
          },
          end: {
            line: 334,
            column: 85
          }
        }, {
          start: {
            line: 334,
            column: 89
          },
          end: {
            line: 334,
            column: 93
          }
        }],
        line: 334
      },
      "13": {
        loc: {
          start: {
            line: 342,
            column: 25
          },
          end: {
            line: 342,
            column: 92
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 342,
            column: 25
          },
          end: {
            line: 342,
            column: 86
          }
        }, {
          start: {
            line: 342,
            column: 90
          },
          end: {
            line: 342,
            column: 92
          }
        }],
        line: 342
      },
      "14": {
        loc: {
          start: {
            line: 343,
            column: 25
          },
          end: {
            line: 343,
            column: 101
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 343,
            column: 25
          },
          end: {
            line: 343,
            column: 79
          }
        }, {
          start: {
            line: 343,
            column: 83
          },
          end: {
            line: 343,
            column: 101
          }
        }],
        line: 343
      },
      "15": {
        loc: {
          start: {
            line: 344,
            column: 36
          },
          end: {
            line: 344,
            column: 102
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 344,
            column: 36
          },
          end: {
            line: 344,
            column: 97
          }
        }, {
          start: {
            line: 344,
            column: 101
          },
          end: {
            line: 344,
            column: 102
          }
        }],
        line: 344
      },
      "16": {
        loc: {
          start: {
            line: 345,
            column: 30
          },
          end: {
            line: 345,
            column: 93
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 345,
            column: 30
          },
          end: {
            line: 345,
            column: 85
          }
        }, {
          start: {
            line: 345,
            column: 89
          },
          end: {
            line: 345,
            column: 93
          }
        }],
        line: 345
      },
      "17": {
        loc: {
          start: {
            line: 361,
            column: 29
          },
          end: {
            line: 361,
            column: 58
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 361,
            column: 29
          },
          end: {
            line: 361,
            column: 52
          }
        }, {
          start: {
            line: 361,
            column: 56
          },
          end: {
            line: 361,
            column: 58
          }
        }],
        line: 361
      },
      "18": {
        loc: {
          start: {
            line: 365,
            column: 27
          },
          end: {
            line: 365,
            column: 65
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 365,
            column: 27
          },
          end: {
            line: 365,
            column: 60
          }
        }, {
          start: {
            line: 365,
            column: 64
          },
          end: {
            line: 365,
            column: 65
          }
        }],
        line: 365
      },
      "19": {
        loc: {
          start: {
            line: 451,
            column: 38
          },
          end: {
            line: 451,
            column: 89
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 451,
            column: 38
          },
          end: {
            line: 451,
            column: 84
          }
        }, {
          start: {
            line: 451,
            column: 88
          },
          end: {
            line: 451,
            column: 89
          }
        }],
        line: 451
      },
      "20": {
        loc: {
          start: {
            line: 452,
            column: 32
          },
          end: {
            line: 452,
            column: 94
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 452,
            column: 32
          },
          end: {
            line: 452,
            column: 85
          }
        }, {
          start: {
            line: 452,
            column: 89
          },
          end: {
            line: 452,
            column: 94
          }
        }],
        line: 452
      },
      "21": {
        loc: {
          start: {
            line: 453,
            column: 38
          },
          end: {
            line: 453,
            column: 95
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 453,
            column: 38
          },
          end: {
            line: 453,
            column: 90
          }
        }, {
          start: {
            line: 453,
            column: 94
          },
          end: {
            line: 453,
            column: 95
          }
        }],
        line: 453
      },
      "22": {
        loc: {
          start: {
            line: 457,
            column: 4
          },
          end: {
            line: 463,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 457,
            column: 4
          },
          end: {
            line: 463,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 457
      },
      "23": {
        loc: {
          start: {
            line: 551,
            column: 33
          },
          end: {
            line: 551,
            column: 98
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 551,
            column: 33
          },
          end: {
            line: 551,
            column: 92
          }
        }, {
          start: {
            line: 551,
            column: 96
          },
          end: {
            line: 551,
            column: 98
          }
        }],
        line: 551
      },
      "24": {
        loc: {
          start: {
            line: 571,
            column: 41
          },
          end: {
            line: 571,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 571,
            column: 41
          },
          end: {
            line: 571,
            column: 100
          }
        }, {
          start: {
            line: 571,
            column: 104
          },
          end: {
            line: 571,
            column: 106
          }
        }],
        line: 571
      },
      "25": {
        loc: {
          start: {
            line: 572,
            column: 47
          },
          end: {
            line: 572,
            column: 129
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 572,
            column: 47
          },
          end: {
            line: 572,
            column: 103
          }
        }, {
          start: {
            line: 572,
            column: 107
          },
          end: {
            line: 572,
            column: 129
          }
        }],
        line: 572
      },
      "26": {
        loc: {
          start: {
            line: 585,
            column: 44
          },
          end: {
            line: 585,
            column: 84
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 585,
            column: 44
          },
          end: {
            line: 585,
            column: 76
          }
        }, {
          start: {
            line: 585,
            column: 80
          },
          end: {
            line: 585,
            column: 84
          }
        }],
        line: 585
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\examples\\OptimizationExamples.ts",
      mappings: ";AAAA;;;;;;;;;;;;GAYG;;AAuBH,0EA0LC;AAifoC,iEAAsB;AA1e3D,kFAwRC;AAmNwC,yEAA0B;AA5MnE,gGAyKC;AAoC+C,4EAAsB;AA/BtE,gEAyBC;AA5rBD,8EAU0C;AAE1C,0EAAuE;AACvE,qEAAkE;AAClE,gGAA6F;AAE7F;;;;GAIG;AACI,KAAK,UAAU,+BAA+B;IACnD,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;IAE3D,8BAA8B;IAC9B,MAAM,YAAY,GAAwB;QACxC,EAAE,EAAE,sBAAsB;QAC1B,IAAI,EAAE,6BAA6B;QACnC,WAAW,EAAE,yDAAyD;QACtE,UAAU,EAAE,YAAY;QACxB,aAAa,EAAE,KAAK,EAAE,MAAM;QAC5B,cAAc,EAAE,GAAG,EAAI,OAAO;QAC9B,mBAAmB,EAAE;YACnB,WAAW,EAAE,EAAE,EAAM,KAAK;YAC1B,QAAQ,EAAE,EAAE,EAAS,MAAM;YAC3B,SAAS,EAAE,IAAI,CAAM,qBAAqB;SAC3C;QACD,UAAU,EAAE;YACV;gBACE,EAAE,EAAE,UAAU;gBACd,IAAI,EAAE,KAAK;gBACX,cAAc,EAAE;oBACd,WAAW,EAAE,GAAG;oBAChB,UAAU,EAAE,KAAK;oBACjB,UAAU,EAAE,IAAI;iBACjB;aACF;YACD;gBACE,EAAE,EAAE,WAAW;gBACf,IAAI,EAAE,MAAM;gBACZ,cAAc,EAAE;oBACd,MAAM,EAAE,GAAG;oBACX,QAAQ,EAAE,kBAAkB;oBAC5B,SAAS,EAAE,MAAM;iBAClB;aACF;SACF;KACF,CAAC;IAEF,gCAAgC;IAChC,MAAM,SAAS,GAA2B;QACxC;YACE,EAAE,EAAE,oBAAoB;YACxB,IAAI,EAAE,oBAAoB;YAC1B,WAAW,EAAE,kCAAkC;YAC/C,IAAI,EAAE,YAAY;YAClB,MAAM,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE;YACpC,KAAK,EAAE,QAAQ;YACf,YAAY,EAAE,EAAE;SACjB;QACD;YACE,EAAE,EAAE,sBAAsB;YAC1B,IAAI,EAAE,sBAAsB;YAC5B,WAAW,EAAE,0BAA0B;YACvC,IAAI,EAAE,YAAY;YAClB,MAAM,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE;YACnC,KAAK,EAAE,QAAQ;YACf,YAAY,EAAE,EAAE;SACjB;QACD;YACE,EAAE,EAAE,WAAW;YACf,IAAI,EAAE,WAAW;YACjB,WAAW,EAAE,gCAAgC;YAC7C,IAAI,EAAE,YAAY;YAClB,MAAM,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE;YACrC,KAAK,EAAE,SAAS;YAChB,YAAY,EAAE,EAAE;SACjB;KACF,CAAC;IAEF,qBAAqB;IACrB,MAAM,WAAW,GAA6B;QAC5C;YACE,EAAE,EAAE,qBAAqB;YACzB,IAAI,EAAE,6BAA6B;YACnC,WAAW,EAAE,6CAA6C;YAC1D,IAAI,EAAE,YAAY;YAClB,MAAM,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE;YAC1B,KAAK,EAAE,KAAK;YACZ,kBAAkB,EAAE,CAAC,IAA4B,EAAE,EAAE;gBACnD,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,WAAW,CAAC,EAAE,YAAsB,IAAI,EAAE,CAAC;gBACpF,MAAM,gBAAgB,GAAG,CAAC,QAAQ,GAAG,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,mBAAmB;gBACtE,OAAO,KAAK,GAAG,gBAAgB,CAAC,CAAC,mBAAmB;YACtD,CAAC;SACF;QACD;YACE,EAAE,EAAE,mBAAmB;YACvB,IAAI,EAAE,8BAA8B;YACpC,WAAW,EAAE,kDAAkD;YAC/D,IAAI,EAAE,YAAY;YAClB,MAAM,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE;YACxB,KAAK,EAAE,MAAM;YACb,kBAAkB,EAAE,CAAC,IAA4B,EAAE,EAAE;gBACnD,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,oBAAoB,CAAC,EAAE,YAAsB,IAAI,EAAE,CAAC;gBACjG,MAAM,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,sBAAsB,CAAC,EAAE,YAAsB,IAAI,EAAE,CAAC;gBACrG,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,WAAW,CAAC,EAAE,YAAsB,IAAI,EAAE,CAAC;gBAEpF,uCAAuC;gBACvC,MAAM,QAAQ,GAAG,CAAC,QAAQ,GAAG,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,YAAY,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;gBAC3F,MAAM,QAAQ,GAAG,IAAI,GAAG,GAAG,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,IAAI,GAAG,CAAC,YAAY,GAAG,EAAE,CAAC,IAAI,IAAI,CAAC;gBACtF,MAAM,UAAU,GAAG,KAAK,GAAG,EAAE,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,IAAI,GAAG,CAAC,cAAc,GAAG,EAAE,CAAC,IAAI,IAAI,CAAC;gBAE1F,MAAM,SAAS,GAAG,QAAQ,GAAG,UAAU,GAAG,GAAG,CAAC,CAAC,mBAAmB;gBAClE,OAAO,SAAS,GAAG,GAAG,CAAC,CAAC,mBAAmB;YAC7C,CAAC;SACF;KACF,CAAC;IAEF,qDAAqD;IACrD,MAAM,iBAAiB,GAA0B,CAAC,IAA4B,EAAU,EAAE;QACxF,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,oBAAoB,CAAC,EAAE,YAAsB,IAAI,EAAE,CAAC;QACjG,MAAM,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,sBAAsB,CAAC,EAAE,YAAsB,IAAI,EAAE,CAAC;QACrG,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,WAAW,CAAC,EAAE,YAAsB,IAAI,EAAE,CAAC;QAEpF,uCAAuC;QACvC,MAAM,QAAQ,GAAG,CAAC,QAAQ,GAAG,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,YAAY,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;QAC3F,MAAM,QAAQ,GAAG,IAAI,GAAG,GAAG,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,IAAI,GAAG,CAAC,YAAY,GAAG,EAAE,CAAC,IAAI,IAAI,CAAC;QACtF,MAAM,UAAU,GAAG,KAAK,GAAG,EAAE,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,IAAI,GAAG,CAAC,cAAc,GAAG,EAAE,CAAC,IAAI,IAAI,CAAC;QAE1F,OAAO,QAAQ,GAAG,UAAU,GAAG,GAAG,CAAC,CAAC,yCAAyC;IAC/E,CAAC,CAAC;IAEF,8BAA8B;IAC9B,MAAM,OAAO,GAAwB;QACnC,EAAE,EAAE,0BAA0B;QAC9B,IAAI,EAAE,0BAA0B;QAChC,WAAW,EAAE,mDAAmD;QAChE,mBAAmB,EAAE,YAAY;QACjC,SAAS;QACT,UAAU,EAAE;YACV,UAAU,EAAE,CAAC;oBACX,EAAE,EAAE,wBAAwB;oBAC5B,SAAS,EAAE,+CAAqB,CAAC,sBAAsB;oBACvD,MAAM,EAAE,GAAG;oBACX,WAAW,EAAE,qCAAqC;oBAClD,kBAAkB,EAAE,iBAAiB;oBACrC,KAAK,EAAE,MAAM;iBACd,CAAC;YACF,iBAAiB,EAAE,kBAAkB;SACtC;QACD,WAAW;QACX,iBAAiB,EAAE;YACjB,SAAS,EAAE,+CAAqB,CAAC,iBAAiB;YAClD,UAAU,EAAE;gBACV,cAAc,EAAE,EAAE;gBAClB,aAAa,EAAE,GAAG;gBAClB,aAAa,EAAE,GAAG;gBAClB,YAAY,EAAE,GAAG;aAClB;YACD,eAAe,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE;YAClC,mBAAmB,EAAE;gBACnB,aAAa,EAAE,GAAG;gBAClB,cAAc,EAAE,IAAI;gBACpB,eAAe,EAAE,EAAE;aACpB;SACF;QACD,mBAAmB,EAAE;YACnB,aAAa,EAAE,GAAG;YAClB,cAAc,EAAE,IAAI;YACpB,eAAe,EAAE,EAAE;SACpB;KACF,CAAC;IAEF,mBAAmB;IACnB,MAAM,EAAE,GAAG,IAAI,mCAAgB,CAAC;QAC9B,cAAc,EAAE,EAAE;QAClB,cAAc,EAAE,GAAG;QACnB,aAAa,EAAE,GAAG;QAClB,YAAY,EAAE,GAAG;QACjB,SAAS,EAAE,CAAC;QACZ,kBAAkB,EAAE,SAAS;QAC7B,kBAAkB,EAAE,IAAI;KACzB,CAAC,CAAC;IAEH,MAAM,mBAAmB,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC;IACvE,MAAM,MAAM,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,OAAO,EAAE,iBAAiB,EAAE,mBAAmB,CAAC,CAAC;IAElF,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;IACrC,OAAO,CAAC,GAAG,CAAC,WAAW,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;IACxC,OAAO,CAAC,GAAG,CAAC,iBAAiB,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;IAC5E,OAAO,CAAC,GAAG,CAAC,uBAAwB,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,oBAAoB,CAAY,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;IACxH,OAAO,CAAC,GAAG,CAAC,yBAA0B,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,sBAAsB,CAAY,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;IAC5H,OAAO,CAAC,GAAG,CAAC,cAAe,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,WAAW,CAAY,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAChG,OAAO,CAAC,GAAG,CAAC,mBAAmB,MAAM,CAAC,UAAU,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IAChF,OAAO,CAAC,GAAG,CAAC,sBAAsB,MAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC,CAAC;IAExE,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;GAIG;AACI,KAAK,UAAU,mCAAmC;IACvD,OAAO,CAAC,GAAG,CAAC,wDAAwD,CAAC,CAAC;IAEtE,wDAAwD;IACxD,MAAM,YAAY,GAAwB;QACxC,EAAE,EAAE,wBAAwB;QAC5B,IAAI,EAAE,iCAAiC;QACvC,WAAW,EAAE,uCAAuC;QACpD,UAAU,EAAE,YAAY;QACxB,aAAa,EAAE,KAAK;QACpB,cAAc,EAAE,GAAG;QACnB,mBAAmB,EAAE;YACnB,WAAW,EAAE,EAAE;YACf,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE,GAAG;SACf;QACD,UAAU,EAAE;YACV;gBACE,EAAE,EAAE,YAAY;gBAChB,IAAI,EAAE,KAAK;gBACX,cAAc,EAAE;oBACd,WAAW,EAAE,IAAI;oBACjB,UAAU,EAAE,KAAK;oBACjB,UAAU,EAAE,IAAI;oBAChB,WAAW,EAAE,EAAE,CAAC,KAAK;iBACtB;aACF;YACD;gBACE,EAAE,EAAE,kBAAkB;gBACtB,IAAI,EAAE,MAAM;gBACZ,cAAc,EAAE;oBACd,MAAM,EAAE,GAAG;oBACX,QAAQ,EAAE,kBAAkB;oBAC5B,SAAS,EAAE,MAAM;iBAClB;aACF;SACF;KACF,CAAC;IAEF,gCAAgC;IAChC,MAAM,SAAS,GAA2B;QACxC;YACE,EAAE,EAAE,sBAAsB;YAC1B,IAAI,EAAE,sBAAsB;YAC5B,WAAW,EAAE,2BAA2B;YACxC,IAAI,EAAE,YAAY;YAClB,MAAM,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE;YACpC,KAAK,EAAE,QAAQ;YACf,YAAY,EAAE,EAAE;SACjB;QACD;YACE,EAAE,EAAE,eAAe;YACnB,IAAI,EAAE,eAAe;YACrB,WAAW,EAAE,yBAAyB;YACtC,IAAI,EAAE,UAAU;YAChB,cAAc,EAAE,CAAC,kBAAkB,EAAE,UAAU,EAAE,YAAY,EAAE,iBAAiB,CAAC;YACjF,KAAK,EAAE,eAAe;YACtB,YAAY,EAAE,kBAAkB;SACjC;QACD;YACE,EAAE,EAAE,sBAAsB;YAC1B,IAAI,EAAE,sBAAsB;YAC5B,WAAW,EAAE,2BAA2B;YACxC,IAAI,EAAE,UAAU;YAChB,cAAc,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YAC5B,KAAK,EAAE,QAAQ;YACf,YAAY,EAAE,CAAC;SAChB;QACD;YACE,EAAE,EAAE,gBAAgB;YACpB,IAAI,EAAE,gBAAgB;YACtB,WAAW,EAAE,gCAAgC;YAC7C,IAAI,EAAE,UAAU;YAChB,cAAc,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;YAC9C,KAAK,EAAE,YAAY;YACnB,YAAY,EAAE,IAAI;SACnB;KACF,CAAC;IAEF,qBAAqB;IACrB,MAAM,WAAW,GAA6B;QAC5C;YACE,EAAE,EAAE,kBAAkB;YACtB,IAAI,EAAE,8BAA8B;YACpC,WAAW,EAAE,6BAA6B;YAC1C,IAAI,EAAE,YAAY;YAClB,MAAM,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE;YAC1B,KAAK,EAAE,KAAK;YACZ,kBAAkB,EAAE,CAAC,IAA4B,EAAE,EAAE;gBACnD,0DAA0D;gBAC1D,OAAO,KAAK,GAAG,KAAK,CAAC,CAAC,mCAAmC;YAC3D,CAAC;SACF;QACD;YACE,EAAE,EAAE,aAAa;YACjB,IAAI,EAAE,mBAAmB;YACzB,WAAW,EAAE,oCAAoC;YACjD,IAAI,EAAE,YAAY;YAClB,MAAM,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE;YACvB,KAAK,EAAE,KAAK;YACZ,kBAAkB,EAAE,CAAC,IAA4B,EAAE,EAAE;gBACnD,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,sBAAsB,CAAC,EAAE,YAAsB,IAAI,EAAE,CAAC;gBAC/F,MAAM,QAAQ,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,QAAQ,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;gBACpE,MAAM,UAAU,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,yBAAyB;gBACnF,OAAO,UAAU,GAAG,EAAE,CAAC,CAAC,mBAAmB;YAC7C,CAAC;SACF;KACF,CAAC;IAEF,6BAA6B;IAC7B,MAAM,qBAAqB,GAA0B,CAAC,IAA4B,EAAU,EAAE;QAC5F,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,sBAAsB,CAAC,EAAE,YAAsB,IAAI,EAAE,CAAC;QAC/F,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,eAAe,CAAC,EAAE,YAAsB,IAAI,kBAAkB,CAAC;QAExG,6BAA6B;QAC7B,MAAM,gBAAgB,GAAG;YACvB,kBAAkB,EAAE,GAAG;YACvB,UAAU,EAAE,GAAG;YACf,YAAY,EAAE,GAAG;YACjB,iBAAiB,EAAE,GAAG;SACvB,CAAC;QAEF,MAAM,eAAe,GAAG,gBAAgB,CAAC,QAAyC,CAAC,IAAI,GAAG,CAAC;QAC3F,MAAM,QAAQ,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,QAAQ,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;QACpE,MAAM,YAAY,GAAG,eAAe,GAAG,KAAK,GAAG,GAAG,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,IAAI,GAAG,CAAC,QAAQ,GAAG,EAAE,CAAC,IAAI,IAAI,CAAC;QAEzG,OAAO,YAAY,GAAG,GAAG,CAAC,CAAC,wCAAwC;IACrE,CAAC,CAAC;IAEF,MAAM,0BAA0B,GAA0B,CAAC,IAA4B,EAAU,EAAE;QACjG,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,sBAAsB,CAAC,EAAE,YAAsB,IAAI,EAAE,CAAC;QAC/F,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,gBAAgB,CAAC,EAAE,YAAsB,IAAI,IAAI,CAAC;QAEhG,4DAA4D;QAC5D,MAAM,YAAY,GAAG,qBAAqB,CAAC,IAAI,CAAC,CAAC;QACjD,MAAM,QAAQ,GAAG,CAAC,KAAK,GAAG,YAAY,CAAC,GAAG,CAAC,IAAI,GAAG,aAAa,CAAC,CAAC,CAAC,KAAK;QACvE,MAAM,uBAAuB,GAAG,QAAQ,GAAG,KAAK,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC,6BAA6B;QAE5F,OAAO,uBAAuB,CAAC;IACjC,CAAC,CAAC;IAEF,MAAM,kBAAkB,GAA0B,CAAC,IAA4B,EAAU,EAAE;QACzF,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,sBAAsB,CAAC,EAAE,YAAsB,IAAI,EAAE,CAAC;QAC/F,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,eAAe,CAAC,EAAE,YAAsB,IAAI,kBAAkB,CAAC;QACxG,MAAM,mBAAmB,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,sBAAsB,CAAC,EAAE,YAAsB,IAAI,CAAC,CAAC;QACzG,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,gBAAgB,CAAC,EAAE,YAAsB,IAAI,IAAI,CAAC;QAEhG,2BAA2B;QAC3B,MAAM,aAAa,GAAG;YACpB,kBAAkB,EAAE,EAAE;YACtB,UAAU,EAAE,EAAE;YACd,YAAY,EAAE,CAAC;YACf,iBAAiB,EAAE,EAAE;SACtB,CAAC;QAEF,+BAA+B;QAC/B,MAAM,kBAAkB,GAAG;YACzB,IAAI,EAAE,CAAC;YACP,IAAI,EAAE,GAAG;YACT,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,IAAI;SACX,CAAC;QAEF,MAAM,YAAY,GAAG,aAAa,CAAC,QAAsC,CAAC,IAAI,EAAE,CAAC;QACjF,MAAM,eAAe,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,QAAQ,GAAG,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,QAAQ;QACjE,MAAM,QAAQ,GAAG,YAAY,GAAG,eAAe,CAAC;QAChD,MAAM,cAAc,GAAG,mBAAmB,GAAG,CAAC,GAAG,eAAe,CAAC;QACjE,MAAM,UAAU,GAAG,kBAAkB,CAAC,aAAgD,CAAC,IAAI,CAAC,CAAC;QAE7F,6CAA6C;QAC7C,MAAM,iBAAiB,GAAG,0BAA0B,CAAC,IAAI,CAAC,CAAC;QAC3D,MAAM,aAAa,GAAG,iBAAiB,GAAG,IAAI,GAAG,EAAE,CAAC,CAAC,yBAAyB;QAE9E,OAAO,QAAQ,GAAG,cAAc,GAAG,UAAU,GAAG,aAAa,CAAC;IAChE,CAAC,CAAC;IAEF,8BAA8B;IAC9B,MAAM,OAAO,GAAwB;QACnC,EAAE,EAAE,mCAAmC;QACvC,IAAI,EAAE,0CAA0C;QAChD,WAAW,EAAE,gEAAgE;QAC7E,mBAAmB,EAAE,YAAY;QACjC,SAAS;QACT,UAAU,EAAE;YACV,UAAU,EAAE;gBACV;oBACE,EAAE,EAAE,wBAAwB;oBAC5B,SAAS,EAAE,+CAAqB,CAAC,sBAAsB;oBACvD,MAAM,EAAE,GAAG;oBACX,WAAW,EAAE,+BAA+B;oBAC5C,kBAAkB,EAAE,qBAAqB;oBACzC,KAAK,EAAE,MAAM;iBACd;gBACD;oBACE,EAAE,EAAE,6BAA6B;oBACjC,SAAS,EAAE,+CAAqB,CAAC,2BAA2B;oBAC5D,MAAM,EAAE,GAAG;oBACX,WAAW,EAAE,oCAAoC;oBACjD,kBAAkB,EAAE,0BAA0B;oBAC9C,KAAK,EAAE,UAAU;iBAClB;gBACD;oBACE,EAAE,EAAE,qBAAqB;oBACzB,SAAS,EAAE,+CAAqB,CAAC,mBAAmB;oBACpD,MAAM,EAAE,GAAG;oBACX,WAAW,EAAE,+BAA+B;oBAC5C,kBAAkB,EAAE,kBAAkB;oBACtC,KAAK,EAAE,KAAK;iBACb;aACF;YACD,iBAAiB,EAAE,gBAAgB;SACpC;QACD,WAAW;QACX,iBAAiB,EAAE;YACjB,SAAS,EAAE,+CAAqB,CAAC,OAAO;YACxC,UAAU,EAAE;gBACV,cAAc,EAAE,GAAG;gBACnB,aAAa,EAAE,GAAG;gBAClB,aAAa,EAAE,GAAG;gBAClB,YAAY,EAAE,GAAG;aAClB;YACD,eAAe,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE;YAClC,mBAAmB,EAAE;gBACnB,aAAa,EAAE,GAAG;gBAClB,cAAc,EAAE,IAAI;gBACpB,eAAe,EAAE,EAAE;aACpB;SACF;QACD,mBAAmB,EAAE;YACnB,aAAa,EAAE,GAAG;YAClB,cAAc,EAAE,IAAI;YACpB,eAAe,EAAE,EAAE;SACpB;KACF,CAAC;IAEF,mCAAmC;IACnC,MAAM,WAAW,GAAG,IAAI,yEAAmC,CAAC;QAC1D,SAAS,EAAE,OAAO;QAClB,cAAc,EAAE,GAAG;QACnB,cAAc,EAAE,GAAG;QACnB,aAAa,EAAE,GAAG;QAClB,YAAY,EAAE,GAAG;QACjB,SAAS,EAAE,EAAE;QACb,cAAc,EAAE;YACd,YAAY,EAAE,EAAE;YAChB,kBAAkB,EAAE,IAAI;YACxB,oBAAoB,EAAE,IAAI;YAC1B,WAAW,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,cAAc,EAAE,EAAE,EAAE;YAClD,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,aAAa,EAAE,GAAG,EAAE;SAC/C;QACD,oBAAoB,EAAE,IAAI;QAC1B,WAAW,EAAE,GAAG;KACjB,CAAC,CAAC;IAEH,MAAM,mBAAmB,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC;IACvE,MAAM,MAAM,GAAG,MAAM,WAAW,CAAC,sBAAsB,CACrD,OAAO,EACP,CAAC,qBAAqB,EAAE,0BAA0B,EAAE,kBAAkB,CAAC,EACvE,mBAAmB,CACpB,CAAC;IAEF,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;IACrD,OAAO,CAAC,GAAG,CAAC,WAAW,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;IACxC,OAAO,CAAC,GAAG,CAAC,sBAAsB,MAAM,CAAC,QAAQ,EAAE,WAAW,EAAE,SAAS,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC,CAAC;IACzF,OAAO,CAAC,GAAG,CAAC,gBAAgB,MAAM,CAAC,QAAQ,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC,CAAC,IAAI,KAAK,EAAE,CAAC,CAAC;IAC9F,OAAO,CAAC,GAAG,CAAC,sBAAsB,MAAM,CAAC,QAAQ,EAAE,gBAAgB,EAAE,UAAU,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC,CAAC;IAC/F,OAAO,CAAC,GAAG,CAAC,mBAAmB,MAAM,CAAC,UAAU,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IAChF,OAAO,CAAC,GAAG,CAAC,sBAAsB,MAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC,CAAC;IAExE,mCAAmC;IACnC,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;QACxB,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;QAC3C,OAAO,CAAC,GAAG,CAAC,yBAA0B,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,sBAAsB,CAAY,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;QAC5H,OAAO,CAAC,GAAG,CAAC,kBAAkB,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;QAChF,OAAO,CAAC,GAAG,CAAC,yBAAyB,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;QACrG,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAE,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,gBAAgB,CAAY,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpH,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;GAIG;AACI,KAAK,UAAU,0CAA0C;IAC9D,OAAO,CAAC,GAAG,CAAC,2DAA2D,CAAC,CAAC;IAEzE,qEAAqE;IACrE,uCAAuC;IACvC,qCAAqC;IACrC,mDAAmD;IACnD,sCAAsC;IAEtC,MAAM,YAAY,GAAwB;QACxC,EAAE,EAAE,mBAAmB;QACvB,IAAI,EAAE,+CAA+C;QACrD,WAAW,EAAE,oDAAoD;QACjE,UAAU,EAAE,YAAY;QACxB,aAAa,EAAE,KAAK;QACpB,cAAc,EAAE,GAAG;QACnB,mBAAmB,EAAE;YACnB,WAAW,EAAE,EAAE;YACf,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE,IAAI;SAChB;QACD,UAAU,EAAE;YACV;gBACE,EAAE,EAAE,UAAU;gBACd,IAAI,EAAE,KAAK;gBACX,cAAc,EAAE;oBACd,WAAW,EAAE,GAAG;oBAChB,UAAU,EAAE,KAAK;oBACjB,UAAU,EAAE,IAAI;iBACjB;aACF;YACD;gBACE,EAAE,EAAE,WAAW;gBACf,IAAI,EAAE,MAAM;gBACZ,cAAc,EAAE;oBACd,MAAM,EAAE,GAAG;oBACX,QAAQ,EAAE,kBAAkB;oBAC5B,SAAS,EAAE,MAAM;iBAClB;aACF;YACD;gBACE,EAAE,EAAE,oBAAoB;gBACxB,IAAI,EAAE,YAAY;gBAClB,cAAc,EAAE;oBACd,IAAI,EAAE,sBAAsB;oBAC5B,aAAa,EAAE,EAAE;oBACjB,WAAW,EAAE,EAAE;oBACf,YAAY,EAAE,EAAE;iBACjB;aACF;SACF;KACF,CAAC;IAEF,2DAA2D;IAC3D,MAAM,SAAS,GAA2B;QACxC;YACE,EAAE,EAAE,oBAAoB;YACxB,IAAI,EAAE,oBAAoB;YAC1B,WAAW,EAAE,8BAA8B;YAC3C,IAAI,EAAE,YAAY;YAClB,MAAM,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE;YACpC,KAAK,EAAE,QAAQ;YACf,YAAY,EAAE,EAAE;SACjB;QACD;YACE,EAAE,EAAE,iBAAiB;YACrB,IAAI,EAAE,yBAAyB;YAC/B,WAAW,EAAE,4BAA4B;YACzC,IAAI,EAAE,UAAU;YAChB,cAAc,EAAE,CAAC,sBAAsB,EAAE,gBAAgB,EAAE,sBAAsB,CAAC;YAClF,KAAK,EAAE,cAAc;YACrB,YAAY,EAAE,sBAAsB;SACrC;KACF,CAAC;IAEF,MAAM,WAAW,GAA6B;QAC5C;YACE,EAAE,EAAE,gBAAgB;YACpB,IAAI,EAAE,wBAAwB;YAC9B,WAAW,EAAE,qCAAqC;YAClD,IAAI,EAAE,YAAY;YAClB,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE;YACzB,KAAK,EAAE,KAAK;YACZ,kBAAkB,EAAE,CAAC,IAA4B,EAAE,EAAE;gBACnD,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,oBAAoB,CAAC,EAAE,YAAsB,IAAI,EAAE,CAAC;gBAC7F,MAAM,QAAQ,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,QAAQ,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;gBACpE,OAAO,QAAQ,GAAG,IAAI,CAAC,CAAC,mBAAmB;YAC7C,CAAC;SACF;KACF,CAAC;IAEF,MAAM,OAAO,GAAwB;QACnC,EAAE,EAAE,yBAAyB;QAC7B,IAAI,EAAE,gCAAgC;QACtC,WAAW,EAAE,wDAAwD;QACrE,mBAAmB,EAAE,YAAY;QACjC,SAAS;QACT,UAAU,EAAE;YACV,UAAU,EAAE,CAAC;oBACX,EAAE,EAAE,8BAA8B;oBAClC,SAAS,EAAE,+CAAqB,CAAC,sBAAsB;oBACvD,MAAM,EAAE,GAAG;oBACX,WAAW,EAAE,iEAAiE;oBAC9E,kBAAkB,EAAE,CAAC,IAA4B,EAAE,EAAE;wBACnD,sEAAsE;wBACtE,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,oBAAoB,CAAC,EAAE,YAAsB,IAAI,EAAE,CAAC;wBAC7F,MAAM,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,iBAAiB,CAAC,EAAE,YAAsB,IAAI,sBAAsB,CAAC;wBAEpH,kDAAkD;wBAClD,0DAA0D;wBAC1D,qDAAqD;wBACrD,+CAA+C;wBAE/C,MAAM,QAAQ,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,QAAQ,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;wBACpE,MAAM,QAAQ,GAAG,IAAI,GAAG,GAAG,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,IAAI,GAAG,CAAC,QAAQ,GAAG,EAAE,CAAC,IAAI,IAAI,CAAC;wBAElF,yCAAyC;wBACzC,MAAM,gBAAgB,GAAG;4BACvB,sBAAsB,EAAE,IAAI;4BAC5B,gBAAgB,EAAE,IAAI;4BACtB,sBAAsB,EAAE,IAAI;yBAC7B,CAAC;wBAEF,MAAM,WAAW,GAAG,gBAAgB,CAAC,cAA+C,CAAC,IAAI,IAAI,CAAC;wBAC9F,MAAM,gBAAgB,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;wBAEhD,OAAO,QAAQ,GAAG,WAAW,GAAG,gBAAgB,CAAC;oBACnD,CAAC;oBACD,KAAK,EAAE,MAAM;iBACd,CAAC;YACF,iBAAiB,EAAE,kBAAkB;SACtC;QACD,WAAW;QACX,iBAAiB,EAAE;YACjB,SAAS,EAAE,+CAAqB,CAAC,cAAc;YAC/C,UAAU,EAAE;gBACV,cAAc,EAAE,EAAE;gBAClB,aAAa,EAAE,EAAE;gBACjB,aAAa,EAAE,GAAG;gBAClB,wBAAwB,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;aACrC;YACD,eAAe,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE;YACnC,mBAAmB,EAAE;gBACnB,aAAa,EAAE,EAAE;gBACjB,cAAc,EAAE,IAAI;gBACpB,eAAe,EAAE,EAAE;aACpB;SACF;QACD,mBAAmB,EAAE;YACnB,aAAa,EAAE,EAAE;YACjB,cAAc,EAAE,IAAI;YACpB,eAAe,EAAE,EAAE;SACpB;KACF,CAAC;IAEF,2DAA2D;IAC3D,MAAM,MAAM,GAAG,MAAM,mDAAwB,CAAC,cAAc,CAC1D,OAAO,EACP,+CAAqB,CAAC,cAAc,CACrC,CAAC;IAEF,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;IAChD,OAAO,CAAC,GAAG,CAAC,WAAW,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;IACxC,OAAO,CAAC,GAAG,CAAC,iBAAiB,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;IAC5E,OAAO,CAAC,GAAG,CAAC,0BAA2B,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,oBAAoB,CAAY,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;IAC3H,OAAO,CAAC,GAAG,CAAC,4BAA4B,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;IAC5F,OAAO,CAAC,GAAG,CAAC,mBAAmB,MAAM,CAAC,UAAU,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IAEhF,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,0BAA0B;IAC9C,OAAO,CAAC,GAAG,CAAC,6DAA6D,CAAC,CAAC;IAE3E,IAAI,CAAC;QACH,2CAA2C;QAC3C,MAAM,+BAA+B,EAAE,CAAC;QAExC,0CAA0C;QAC1C,MAAM,mCAAmC,EAAE,CAAC;QAE5C,kDAAkD;QAClD,MAAM,0CAA0C,EAAE,CAAC;QAEnD,OAAO,CAAC,GAAG,CAAC,uDAAuD,CAAC,CAAC;QACrE,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;QAC7B,OAAO,CAAC,GAAG,CAAC,mDAAmD,CAAC,CAAC;QACjE,OAAO,CAAC,GAAG,CAAC,0DAA0D,CAAC,CAAC;QACxE,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAC;QAClE,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;QACvD,OAAO,CAAC,GAAG,CAAC,sDAAsD,CAAC,CAAC;IAEtE,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;QAC/D,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\examples\\OptimizationExamples.ts"],
      sourcesContent: ["/**\r\n * Optimization Framework Usage Examples\r\n * \r\n * Demonstrates practical usage of the System Optimization Framework with real HVAC scenarios:\r\n * - Single-objective optimization examples\r\n * - Multi-objective optimization with trade-off analysis\r\n * - Integration with existing Phase 1/2/3 Priority 1 components\r\n * - Real-world HVAC system optimization scenarios\r\n * - Best practices and common patterns\r\n * \r\n * @version 3.0.0\r\n * <AUTHOR> Suite Development Team\r\n */\r\n\r\nimport {\r\n  OptimizationProblem,\r\n  OptimizationResult,\r\n  OptimizationAlgorithm,\r\n  OptimizationObjective,\r\n  OptimizationVariable,\r\n  OptimizationConstraint,\r\n  SystemConfiguration,\r\n  ObjectiveFunctionType,\r\n  ConstraintFunctionType\r\n} from '../types/SystemOptimizationTypes';\r\n\r\nimport { SystemOptimizationEngine } from '../SystemOptimizationEngine';\r\nimport { GeneticAlgorithm } from '../algorithms/GeneticAlgorithm';\r\nimport { MultiObjectiveOptimizationFramework } from '../MultiObjectiveOptimizationFramework';\r\n\r\n/**\r\n * Example 1: Single-Objective Duct Sizing Optimization\r\n * \r\n * Optimize duct diameter to minimize pressure loss while meeting airflow requirements\r\n */\r\nexport async function example1_DuctSizingOptimization(): Promise<OptimizationResult> {\r\n  console.log('=== Example 1: Duct Sizing Optimization ===');\r\n  \r\n  // Define system configuration\r\n  const systemConfig: SystemConfiguration = {\r\n    id: 'office_building_hvac',\r\n    name: 'Office Building HVAC System',\r\n    description: 'Main supply air system for 50,000 sq ft office building',\r\n    systemType: 'supply_air',\r\n    designAirflow: 25000, // CFM\r\n    designPressure: 4.0,   // inWC\r\n    operatingConditions: {\r\n      temperature: 72,     // \xB0F\r\n      humidity: 45,        // %RH\r\n      elevation: 1000      // ft above sea level\r\n    },\r\n    components: [\r\n      {\r\n        id: 'main_fan',\r\n        type: 'fan',\r\n        specifications: {\r\n          maxPressure: 8.0,\r\n          maxAirflow: 30000,\r\n          efficiency: 0.82\r\n        }\r\n      },\r\n      {\r\n        id: 'main_duct',\r\n        type: 'duct',\r\n        specifications: {\r\n          length: 200,\r\n          material: 'galvanized_steel',\r\n          roughness: 0.0015\r\n        }\r\n      }\r\n    ]\r\n  };\r\n\r\n  // Define optimization variables\r\n  const variables: OptimizationVariable[] = [\r\n    {\r\n      id: 'main_duct_diameter',\r\n      name: 'Main Duct Diameter',\r\n      description: 'Diameter of the main supply duct',\r\n      type: 'continuous',\r\n      bounds: { minimum: 18, maximum: 36 },\r\n      units: 'inches',\r\n      currentValue: 24\r\n    },\r\n    {\r\n      id: 'branch_duct_diameter',\r\n      name: 'Branch Duct Diameter',\r\n      description: 'Diameter of branch ducts',\r\n      type: 'continuous',\r\n      bounds: { minimum: 8, maximum: 20 },\r\n      units: 'inches',\r\n      currentValue: 12\r\n    },\r\n    {\r\n      id: 'fan_speed',\r\n      name: 'Fan Speed',\r\n      description: 'Fan operating speed percentage',\r\n      type: 'continuous',\r\n      bounds: { minimum: 60, maximum: 100 },\r\n      units: 'percent',\r\n      currentValue: 85\r\n    }\r\n  ];\r\n\r\n  // Define constraints\r\n  const constraints: OptimizationConstraint[] = [\r\n    {\r\n      id: 'airflow_requirement',\r\n      name: 'Minimum Airflow Requirement',\r\n      description: 'System must deliver at least design airflow',\r\n      type: 'inequality',\r\n      bounds: { minimum: 25000 },\r\n      units: 'CFM',\r\n      evaluationFunction: (vars: OptimizationVariable[]) => {\r\n        const fanSpeed = vars.find(v => v.id === 'fan_speed')?.currentValue as number || 85;\r\n        const deliveredAirflow = (fanSpeed / 100) * 30000; // Simplified model\r\n        return 25000 - deliveredAirflow; // Violation if < 0\r\n      }\r\n    },\r\n    {\r\n      id: 'max_pressure_loss',\r\n      name: 'Maximum System Pressure Loss',\r\n      description: 'Total pressure loss must not exceed fan capacity',\r\n      type: 'inequality',\r\n      bounds: { maximum: 6.0 },\r\n      units: 'inWC',\r\n      evaluationFunction: (vars: OptimizationVariable[]) => {\r\n        const mainDiameter = vars.find(v => v.id === 'main_duct_diameter')?.currentValue as number || 24;\r\n        const branchDiameter = vars.find(v => v.id === 'branch_duct_diameter')?.currentValue as number || 12;\r\n        const fanSpeed = vars.find(v => v.id === 'fan_speed')?.currentValue as number || 85;\r\n        \r\n        // Simplified pressure loss calculation\r\n        const velocity = (fanSpeed / 100) * 25000 / (Math.PI * (mainDiameter / 12) ** 2 / 4 * 144);\r\n        const mainLoss = 0.02 * 200 * (velocity / 4005) ** 1.85 / (mainDiameter / 12) ** 1.23;\r\n        const branchLoss = 0.015 * 50 * (velocity / 4005) ** 1.85 / (branchDiameter / 12) ** 1.23;\r\n        \r\n        const totalLoss = mainLoss + branchLoss + 1.5; // Include fittings\r\n        return totalLoss - 6.0; // Violation if > 0\r\n      }\r\n    }\r\n  ];\r\n\r\n  // Define objective function (minimize pressure loss)\r\n  const objectiveFunction: ObjectiveFunctionType = (vars: OptimizationVariable[]): number => {\r\n    const mainDiameter = vars.find(v => v.id === 'main_duct_diameter')?.currentValue as number || 24;\r\n    const branchDiameter = vars.find(v => v.id === 'branch_duct_diameter')?.currentValue as number || 12;\r\n    const fanSpeed = vars.find(v => v.id === 'fan_speed')?.currentValue as number || 85;\r\n    \r\n    // Calculate total system pressure loss\r\n    const velocity = (fanSpeed / 100) * 25000 / (Math.PI * (mainDiameter / 12) ** 2 / 4 * 144);\r\n    const mainLoss = 0.02 * 200 * (velocity / 4005) ** 1.85 / (mainDiameter / 12) ** 1.23;\r\n    const branchLoss = 0.015 * 50 * (velocity / 4005) ** 1.85 / (branchDiameter / 12) ** 1.23;\r\n    \r\n    return mainLoss + branchLoss + 1.5; // Total pressure loss including fittings\r\n  };\r\n\r\n  // Create optimization problem\r\n  const problem: OptimizationProblem = {\r\n    id: 'duct_sizing_optimization',\r\n    name: 'Duct Sizing Optimization',\r\n    description: 'Optimize duct diameters to minimize pressure loss',\r\n    systemConfiguration: systemConfig,\r\n    variables,\r\n    objectives: {\r\n      objectives: [{\r\n        id: 'minimize_pressure_loss',\r\n        objective: OptimizationObjective.MINIMIZE_PRESSURE_LOSS,\r\n        weight: 1.0,\r\n        description: 'Minimize total system pressure loss',\r\n        evaluationFunction: objectiveFunction,\r\n        units: 'inWC'\r\n      }],\r\n      aggregationMethod: 'single_objective'\r\n    },\r\n    constraints,\r\n    algorithmSettings: {\r\n      algorithm: OptimizationAlgorithm.GENETIC_ALGORITHM,\r\n      parameters: {\r\n        populationSize: 50,\r\n        maxIterations: 100,\r\n        crossoverRate: 0.8,\r\n        mutationRate: 0.1\r\n      },\r\n      parallelization: { enabled: true },\r\n      convergenceCriteria: {\r\n        maxIterations: 100,\r\n        toleranceValue: 1e-6,\r\n        stagnationLimit: 20\r\n      }\r\n    },\r\n    convergenceCriteria: {\r\n      maxIterations: 100,\r\n      toleranceValue: 1e-6,\r\n      stagnationLimit: 20\r\n    }\r\n  };\r\n\r\n  // Run optimization\r\n  const ga = new GeneticAlgorithm({\r\n    populationSize: 50,\r\n    maxGenerations: 100,\r\n    crossoverRate: 0.8,\r\n    mutationRate: 0.1,\r\n    eliteSize: 5,\r\n    constraintHandling: 'penalty',\r\n    penaltyCoefficient: 1000\r\n  });\r\n\r\n  const constraintFunctions = constraints.map(c => c.evaluationFunction);\r\n  const result = await ga.optimize(problem, objectiveFunction, constraintFunctions);\r\n\r\n  console.log('Optimization Results:');\r\n  console.log(`Status: ${result.status}`);\r\n  console.log(`Best Fitness: ${result.bestSolution.fitness.toFixed(4)} inWC`);\r\n  console.log(`Main Duct Diameter: ${(result.bestSolution.variables['main_duct_diameter'] as number).toFixed(1)} inches`);\r\n  console.log(`Branch Duct Diameter: ${(result.bestSolution.variables['branch_duct_diameter'] as number).toFixed(1)} inches`);\r\n  console.log(`Fan Speed: ${(result.bestSolution.variables['fan_speed'] as number).toFixed(1)}%`);\r\n  console.log(`Execution Time: ${result.statistics.executionTime.toFixed(2)} ms`);\r\n  console.log(`Total Evaluations: ${result.statistics.totalEvaluations}`);\r\n\r\n  return result;\r\n}\r\n\r\n/**\r\n * Example 2: Multi-Objective HVAC System Optimization\r\n * \r\n * Optimize system for multiple competing objectives: pressure loss, energy consumption, and cost\r\n */\r\nexport async function example2_MultiObjectiveOptimization(): Promise<OptimizationResult> {\r\n  console.log('\\n=== Example 2: Multi-Objective HVAC Optimization ===');\r\n  \r\n  // Define system configuration for a commercial building\r\n  const systemConfig: SystemConfiguration = {\r\n    id: 'commercial_hvac_system',\r\n    name: 'Commercial Building HVAC System',\r\n    description: 'Multi-zone HVAC system with VAV boxes',\r\n    systemType: 'supply_air',\r\n    designAirflow: 50000,\r\n    designPressure: 5.0,\r\n    operatingConditions: {\r\n      temperature: 70,\r\n      humidity: 50,\r\n      elevation: 500\r\n    },\r\n    components: [\r\n      {\r\n        id: 'supply_fan',\r\n        type: 'fan',\r\n        specifications: {\r\n          maxPressure: 10.0,\r\n          maxAirflow: 60000,\r\n          efficiency: 0.85,\r\n          powerRating: 75 // HP\r\n        }\r\n      },\r\n      {\r\n        id: 'main_supply_duct',\r\n        type: 'duct',\r\n        specifications: {\r\n          length: 300,\r\n          material: 'galvanized_steel',\r\n          roughness: 0.0015\r\n        }\r\n      }\r\n    ]\r\n  };\r\n\r\n  // Define optimization variables\r\n  const variables: OptimizationVariable[] = [\r\n    {\r\n      id: 'supply_duct_diameter',\r\n      name: 'Supply Duct Diameter',\r\n      description: 'Main supply duct diameter',\r\n      type: 'continuous',\r\n      bounds: { minimum: 24, maximum: 48 },\r\n      units: 'inches',\r\n      currentValue: 36\r\n    },\r\n    {\r\n      id: 'duct_material',\r\n      name: 'Duct Material',\r\n      description: 'Duct material selection',\r\n      type: 'discrete',\r\n      discreteValues: ['galvanized_steel', 'aluminum', 'fiberglass', 'stainless_steel'],\r\n      units: 'material_type',\r\n      currentValue: 'galvanized_steel'\r\n    },\r\n    {\r\n      id: 'insulation_thickness',\r\n      name: 'Insulation Thickness',\r\n      description: 'Duct insulation thickness',\r\n      type: 'discrete',\r\n      discreteValues: [1, 2, 3, 4],\r\n      units: 'inches',\r\n      currentValue: 2\r\n    },\r\n    {\r\n      id: 'fan_efficiency',\r\n      name: 'Fan Efficiency',\r\n      description: 'Fan motor efficiency selection',\r\n      type: 'discrete',\r\n      discreteValues: [0.80, 0.85, 0.90, 0.93, 0.95],\r\n      units: 'efficiency',\r\n      currentValue: 0.85\r\n    }\r\n  ];\r\n\r\n  // Define constraints\r\n  const constraints: OptimizationConstraint[] = [\r\n    {\r\n      id: 'airflow_delivery',\r\n      name: 'Airflow Delivery Requirement',\r\n      description: 'Must deliver design airflow',\r\n      type: 'inequality',\r\n      bounds: { minimum: 50000 },\r\n      units: 'CFM',\r\n      evaluationFunction: (vars: OptimizationVariable[]) => {\r\n        // Simplified: assume delivered airflow is close to design\r\n        return 50000 - 49000; // Always feasible for this example\r\n      }\r\n    },\r\n    {\r\n      id: 'noise_limit',\r\n      name: 'Noise Level Limit',\r\n      description: 'System noise must not exceed limit',\r\n      type: 'inequality',\r\n      bounds: { maximum: 55 },\r\n      units: 'dBA',\r\n      evaluationFunction: (vars: OptimizationVariable[]) => {\r\n        const diameter = vars.find(v => v.id === 'supply_duct_diameter')?.currentValue as number || 36;\r\n        const velocity = 50000 / (Math.PI * (diameter / 12) ** 2 / 4 * 144);\r\n        const noiseLevel = 30 + 20 * Math.log10(velocity / 1000); // Simplified noise model\r\n        return noiseLevel - 55; // Violation if > 0\r\n      }\r\n    }\r\n  ];\r\n\r\n  // Define objective functions\r\n  const pressureLossObjective: ObjectiveFunctionType = (vars: OptimizationVariable[]): number => {\r\n    const diameter = vars.find(v => v.id === 'supply_duct_diameter')?.currentValue as number || 36;\r\n    const material = vars.find(v => v.id === 'duct_material')?.currentValue as string || 'galvanized_steel';\r\n    \r\n    // Material roughness factors\r\n    const roughnessFactors = {\r\n      'galvanized_steel': 1.0,\r\n      'aluminum': 0.8,\r\n      'fiberglass': 1.2,\r\n      'stainless_steel': 0.6\r\n    };\r\n    \r\n    const roughnessFactor = roughnessFactors[material as keyof typeof roughnessFactors] || 1.0;\r\n    const velocity = 50000 / (Math.PI * (diameter / 12) ** 2 / 4 * 144);\r\n    const pressureLoss = roughnessFactor * 0.025 * 300 * (velocity / 4005) ** 1.85 / (diameter / 12) ** 1.23;\r\n    \r\n    return pressureLoss + 2.0; // Include fittings and equipment losses\r\n  };\r\n\r\n  const energyConsumptionObjective: ObjectiveFunctionType = (vars: OptimizationVariable[]): number => {\r\n    const diameter = vars.find(v => v.id === 'supply_duct_diameter')?.currentValue as number || 36;\r\n    const fanEfficiency = vars.find(v => v.id === 'fan_efficiency')?.currentValue as number || 0.85;\r\n    \r\n    // Calculate fan power based on pressure loss and efficiency\r\n    const pressureLoss = pressureLossObjective(vars);\r\n    const fanPower = (50000 * pressureLoss) / (6356 * fanEfficiency); // HP\r\n    const annualEnergyConsumption = fanPower * 0.746 * 8760 * 0.7; // kWh/year (70% load factor)\r\n    \r\n    return annualEnergyConsumption;\r\n  };\r\n\r\n  const totalCostObjective: ObjectiveFunctionType = (vars: OptimizationVariable[]): number => {\r\n    const diameter = vars.find(v => v.id === 'supply_duct_diameter')?.currentValue as number || 36;\r\n    const material = vars.find(v => v.id === 'duct_material')?.currentValue as string || 'galvanized_steel';\r\n    const insulationThickness = vars.find(v => v.id === 'insulation_thickness')?.currentValue as number || 2;\r\n    const fanEfficiency = vars.find(v => v.id === 'fan_efficiency')?.currentValue as number || 0.85;\r\n    \r\n    // Material costs per sq ft\r\n    const materialCosts = {\r\n      'galvanized_steel': 12,\r\n      'aluminum': 18,\r\n      'fiberglass': 8,\r\n      'stainless_steel': 25\r\n    };\r\n    \r\n    // Fan efficiency premium costs\r\n    const efficiencyPremiums = {\r\n      0.80: 0,\r\n      0.85: 500,\r\n      0.90: 1200,\r\n      0.93: 2000,\r\n      0.95: 3500\r\n    };\r\n    \r\n    const materialCost = materialCosts[material as keyof typeof materialCosts] || 12;\r\n    const ductSurfaceArea = Math.PI * (diameter / 12) * 300; // sq ft\r\n    const ductCost = materialCost * ductSurfaceArea;\r\n    const insulationCost = insulationThickness * 3 * ductSurfaceArea;\r\n    const fanPremium = efficiencyPremiums[fanEfficiency as keyof typeof efficiencyPremiums] || 0;\r\n    \r\n    // Operating cost (energy cost over 10 years)\r\n    const energyConsumption = energyConsumptionObjective(vars);\r\n    const operatingCost = energyConsumption * 0.12 * 10; // $0.12/kWh for 10 years\r\n    \r\n    return ductCost + insulationCost + fanPremium + operatingCost;\r\n  };\r\n\r\n  // Create optimization problem\r\n  const problem: OptimizationProblem = {\r\n    id: 'multi_objective_hvac_optimization',\r\n    name: 'Multi-Objective HVAC System Optimization',\r\n    description: 'Optimize for pressure loss, energy consumption, and total cost',\r\n    systemConfiguration: systemConfig,\r\n    variables,\r\n    objectives: {\r\n      objectives: [\r\n        {\r\n          id: 'minimize_pressure_loss',\r\n          objective: OptimizationObjective.MINIMIZE_PRESSURE_LOSS,\r\n          weight: 0.4,\r\n          description: 'Minimize system pressure loss',\r\n          evaluationFunction: pressureLossObjective,\r\n          units: 'inWC'\r\n        },\r\n        {\r\n          id: 'minimize_energy_consumption',\r\n          objective: OptimizationObjective.MINIMIZE_ENERGY_CONSUMPTION,\r\n          weight: 0.4,\r\n          description: 'Minimize annual energy consumption',\r\n          evaluationFunction: energyConsumptionObjective,\r\n          units: 'kWh/year'\r\n        },\r\n        {\r\n          id: 'minimize_total_cost',\r\n          objective: OptimizationObjective.MINIMIZE_TOTAL_COST,\r\n          weight: 0.2,\r\n          description: 'Minimize total lifecycle cost',\r\n          evaluationFunction: totalCostObjective,\r\n          units: 'USD'\r\n        }\r\n      ],\r\n      aggregationMethod: 'pareto_optimal'\r\n    },\r\n    constraints,\r\n    algorithmSettings: {\r\n      algorithm: OptimizationAlgorithm.NSGA_II,\r\n      parameters: {\r\n        populationSize: 100,\r\n        maxIterations: 150,\r\n        crossoverRate: 0.9,\r\n        mutationRate: 0.1\r\n      },\r\n      parallelization: { enabled: true },\r\n      convergenceCriteria: {\r\n        maxIterations: 150,\r\n        toleranceValue: 1e-6,\r\n        stagnationLimit: 30\r\n      }\r\n    },\r\n    convergenceCriteria: {\r\n      maxIterations: 150,\r\n      toleranceValue: 1e-6,\r\n      stagnationLimit: 30\r\n    }\r\n  };\r\n\r\n  // Run multi-objective optimization\r\n  const moFramework = new MultiObjectiveOptimizationFramework({\r\n    algorithm: 'nsga2',\r\n    populationSize: 100,\r\n    maxGenerations: 150,\r\n    crossoverRate: 0.9,\r\n    mutationRate: 0.1,\r\n    eliteSize: 10,\r\n    paretoSettings: {\r\n      maxSolutions: 50,\r\n      diversityThreshold: 0.01,\r\n      convergenceThreshold: 1e-6,\r\n      hypervolume: { enabled: true, referencePoint: [] },\r\n      spacing: { enabled: true, targetSpacing: 0.1 }\r\n    },\r\n    diversityMaintenance: true,\r\n    archiveSize: 100\r\n  });\r\n\r\n  const constraintFunctions = constraints.map(c => c.evaluationFunction);\r\n  const result = await moFramework.optimizeMultiObjective(\r\n    problem,\r\n    [pressureLossObjective, energyConsumptionObjective, totalCostObjective],\r\n    constraintFunctions\r\n  );\r\n\r\n  console.log('Multi-Objective Optimization Results:');\r\n  console.log(`Status: ${result.status}`);\r\n  console.log(`Pareto Front Size: ${result.analysis?.paretoFront?.solutions.length || 0}`);\r\n  console.log(`Hypervolume: ${result.analysis?.paretoFront?.hypervolume?.toFixed(4) || 'N/A'}`);\r\n  console.log(`Knee Points Found: ${result.analysis?.tradeoffAnalysis?.kneePoints.length || 0}`);\r\n  console.log(`Execution Time: ${result.statistics.executionTime.toFixed(2)} ms`);\r\n  console.log(`Total Evaluations: ${result.statistics.totalEvaluations}`);\r\n\r\n  // Display best compromise solution\r\n  if (result.bestSolution) {\r\n    console.log('\\nBest Compromise Solution:');\r\n    console.log(`Supply Duct Diameter: ${(result.bestSolution.variables['supply_duct_diameter'] as number).toFixed(1)} inches`);\r\n    console.log(`Duct Material: ${result.bestSolution.variables['duct_material']}`);\r\n    console.log(`Insulation Thickness: ${result.bestSolution.variables['insulation_thickness']} inches`);\r\n    console.log(`Fan Efficiency: ${((result.bestSolution.variables['fan_efficiency'] as number) * 100).toFixed(1)}%`);\r\n  }\r\n\r\n  return result;\r\n}\r\n\r\n/**\r\n * Example 3: Integration with Existing Phase 1/2/3 Priority 1 Components\r\n * \r\n * Demonstrates how to use optimization with existing calculation services\r\n */\r\nexport async function example3_IntegrationWithExistingComponents(): Promise<OptimizationResult> {\r\n  console.log('\\n=== Example 3: Integration with Existing Components ===');\r\n  \r\n  // This example shows how the optimization framework integrates with:\r\n  // - SystemPressureCalculator (Phase 1)\r\n  // - FittingLossCalculator (Phase 1) \r\n  // - AdvancedFittingCalculator (Phase 3 Priority 1)\r\n  // - AirPropertiesCalculator (Phase 2)\r\n  \r\n  const systemConfig: SystemConfiguration = {\r\n    id: 'integrated_system',\r\n    name: 'Integrated HVAC System with Advanced Fittings',\r\n    description: 'System using all Phase 1/2/3 Priority 1 components',\r\n    systemType: 'supply_air',\r\n    designAirflow: 15000,\r\n    designPressure: 3.5,\r\n    operatingConditions: {\r\n      temperature: 68,\r\n      humidity: 40,\r\n      elevation: 2000\r\n    },\r\n    components: [\r\n      {\r\n        id: 'main_fan',\r\n        type: 'fan',\r\n        specifications: {\r\n          maxPressure: 6.0,\r\n          maxAirflow: 20000,\r\n          efficiency: 0.88\r\n        }\r\n      },\r\n      {\r\n        id: 'main_duct',\r\n        type: 'duct',\r\n        specifications: {\r\n          length: 150,\r\n          material: 'galvanized_steel',\r\n          roughness: 0.0015\r\n        }\r\n      },\r\n      {\r\n        id: 'transition_fitting',\r\n        type: 'transition',\r\n        specifications: {\r\n          type: 'round_to_rectangular',\r\n          inletDiameter: 20,\r\n          outletWidth: 24,\r\n          outletHeight: 12\r\n        }\r\n      }\r\n    ]\r\n  };\r\n\r\n  // Use SystemOptimizationEngine for integrated optimization\r\n  const variables: OptimizationVariable[] = [\r\n    {\r\n      id: 'main_duct_diameter',\r\n      name: 'Main Duct Diameter',\r\n      description: 'Diameter of main supply duct',\r\n      type: 'continuous',\r\n      bounds: { minimum: 16, maximum: 28 },\r\n      units: 'inches',\r\n      currentValue: 20\r\n    },\r\n    {\r\n      id: 'transition_type',\r\n      name: 'Transition Fitting Type',\r\n      description: 'Type of transition fitting',\r\n      type: 'discrete',\r\n      discreteValues: ['round_to_rectangular', 'round_to_round', 'rectangular_to_round'],\r\n      units: 'fitting_type',\r\n      currentValue: 'round_to_rectangular'\r\n    }\r\n  ];\r\n\r\n  const constraints: OptimizationConstraint[] = [\r\n    {\r\n      id: 'velocity_limit',\r\n      name: 'Maximum Velocity Limit',\r\n      description: 'Duct velocity must not exceed limit',\r\n      type: 'inequality',\r\n      bounds: { maximum: 2500 },\r\n      units: 'FPM',\r\n      evaluationFunction: (vars: OptimizationVariable[]) => {\r\n        const diameter = vars.find(v => v.id === 'main_duct_diameter')?.currentValue as number || 20;\r\n        const velocity = 15000 / (Math.PI * (diameter / 12) ** 2 / 4 * 144);\r\n        return velocity - 2500; // Violation if > 0\r\n      }\r\n    }\r\n  ];\r\n\r\n  const problem: OptimizationProblem = {\r\n    id: 'integrated_optimization',\r\n    name: 'Integrated System Optimization',\r\n    description: 'Optimization using all existing calculation components',\r\n    systemConfiguration: systemConfig,\r\n    variables,\r\n    objectives: {\r\n      objectives: [{\r\n        id: 'minimize_total_pressure_loss',\r\n        objective: OptimizationObjective.MINIMIZE_PRESSURE_LOSS,\r\n        weight: 1.0,\r\n        description: 'Minimize total system pressure loss including advanced fittings',\r\n        evaluationFunction: (vars: OptimizationVariable[]) => {\r\n          // This would integrate with actual Phase 1/2/3 Priority 1 calculators\r\n          const diameter = vars.find(v => v.id === 'main_duct_diameter')?.currentValue as number || 20;\r\n          const transitionType = vars.find(v => v.id === 'transition_type')?.currentValue as string || 'round_to_rectangular';\r\n          \r\n          // Simplified calculation - in practice would use:\r\n          // - SystemPressureCalculator.calculateTotalPressureLoss()\r\n          // - AdvancedFittingCalculator.calculateFittingLoss()\r\n          // - AirPropertiesCalculator.getAirProperties()\r\n          \r\n          const velocity = 15000 / (Math.PI * (diameter / 12) ** 2 / 4 * 144);\r\n          const ductLoss = 0.02 * 150 * (velocity / 4005) ** 1.85 / (diameter / 12) ** 1.23;\r\n          \r\n          // Transition fitting losses (simplified)\r\n          const transitionLosses = {\r\n            'round_to_rectangular': 0.15,\r\n            'round_to_round': 0.05,\r\n            'rectangular_to_round': 0.12\r\n          };\r\n          \r\n          const fittingLoss = transitionLosses[transitionType as keyof typeof transitionLosses] || 0.15;\r\n          const velocityPressure = (velocity / 4005) ** 2;\r\n          \r\n          return ductLoss + fittingLoss * velocityPressure;\r\n        },\r\n        units: 'inWC'\r\n      }],\r\n      aggregationMethod: 'single_objective'\r\n    },\r\n    constraints,\r\n    algorithmSettings: {\r\n      algorithm: OptimizationAlgorithm.PARTICLE_SWARM,\r\n      parameters: {\r\n        populationSize: 30,\r\n        maxIterations: 75,\r\n        inertiaWeight: 0.9,\r\n        accelerationCoefficients: [2.0, 2.0]\r\n      },\r\n      parallelization: { enabled: false },\r\n      convergenceCriteria: {\r\n        maxIterations: 75,\r\n        toleranceValue: 1e-6,\r\n        stagnationLimit: 15\r\n      }\r\n    },\r\n    convergenceCriteria: {\r\n      maxIterations: 75,\r\n      toleranceValue: 1e-6,\r\n      stagnationLimit: 15\r\n    }\r\n  };\r\n\r\n  // Use SystemOptimizationEngine for integrated optimization\r\n  const result = await SystemOptimizationEngine.optimizeSystem(\r\n    problem,\r\n    OptimizationAlgorithm.PARTICLE_SWARM\r\n  );\r\n\r\n  console.log('Integrated Optimization Results:');\r\n  console.log(`Status: ${result.status}`);\r\n  console.log(`Best Fitness: ${result.bestSolution.fitness.toFixed(4)} inWC`);\r\n  console.log(`Optimal Duct Diameter: ${(result.bestSolution.variables['main_duct_diameter'] as number).toFixed(1)} inches`);\r\n  console.log(`Optimal Transition Type: ${result.bestSolution.variables['transition_type']}`);\r\n  console.log(`Execution Time: ${result.statistics.executionTime.toFixed(2)} ms`);\r\n\r\n  return result;\r\n}\r\n\r\n/**\r\n * Run all optimization examples\r\n */\r\nexport async function runAllOptimizationExamples(): Promise<void> {\r\n  console.log('\uD83D\uDE80 Running SizeWise Suite Optimization Framework Examples\\n');\r\n  \r\n  try {\r\n    // Example 1: Single-objective optimization\r\n    await example1_DuctSizingOptimization();\r\n    \r\n    // Example 2: Multi-objective optimization\r\n    await example2_MultiObjectiveOptimization();\r\n    \r\n    // Example 3: Integration with existing components\r\n    await example3_IntegrationWithExistingComponents();\r\n    \r\n    console.log('\\n\u2705 All optimization examples completed successfully!');\r\n    console.log('\\nNext Steps:');\r\n    console.log('1. Review the optimization results and trade-offs');\r\n    console.log('2. Integrate with your specific HVAC system requirements');\r\n    console.log('3. Customize objective functions for your use case');\r\n    console.log('4. Add additional constraints as needed');\r\n    console.log('5. Experiment with different optimization algorithms');\r\n    \r\n  } catch (error) {\r\n    console.error('\u274C Error running optimization examples:', error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n// Export individual examples for selective usage\r\nexport {\r\n  example1_DuctSizingOptimization as ductSizingOptimization,\r\n  example2_MultiObjectiveOptimization as multiObjectiveOptimization,\r\n  example3_IntegrationWithExistingComponents as integratedOptimization\r\n};\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "1fff0c043ede7d7bc370592d3d978739f1359d2d"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_jcfe2znb1 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_jcfe2znb1();
cov_jcfe2znb1().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_jcfe2znb1().s[1]++;
exports.example1_DuctSizingOptimization = example1_DuctSizingOptimization;
/* istanbul ignore next */
cov_jcfe2znb1().s[2]++;
exports.ductSizingOptimization = example1_DuctSizingOptimization;
/* istanbul ignore next */
cov_jcfe2znb1().s[3]++;
exports.example2_MultiObjectiveOptimization = example2_MultiObjectiveOptimization;
/* istanbul ignore next */
cov_jcfe2znb1().s[4]++;
exports.multiObjectiveOptimization = example2_MultiObjectiveOptimization;
/* istanbul ignore next */
cov_jcfe2znb1().s[5]++;
exports.example3_IntegrationWithExistingComponents = example3_IntegrationWithExistingComponents;
/* istanbul ignore next */
cov_jcfe2znb1().s[6]++;
exports.integratedOptimization = example3_IntegrationWithExistingComponents;
/* istanbul ignore next */
cov_jcfe2znb1().s[7]++;
exports.runAllOptimizationExamples = runAllOptimizationExamples;
const SystemOptimizationTypes_1 =
/* istanbul ignore next */
(cov_jcfe2znb1().s[8]++, require("../types/SystemOptimizationTypes"));
const SystemOptimizationEngine_1 =
/* istanbul ignore next */
(cov_jcfe2znb1().s[9]++, require("../SystemOptimizationEngine"));
const GeneticAlgorithm_1 =
/* istanbul ignore next */
(cov_jcfe2znb1().s[10]++, require("../algorithms/GeneticAlgorithm"));
const MultiObjectiveOptimizationFramework_1 =
/* istanbul ignore next */
(cov_jcfe2znb1().s[11]++, require("../MultiObjectiveOptimizationFramework"));
/**
 * Example 1: Single-Objective Duct Sizing Optimization
 *
 * Optimize duct diameter to minimize pressure loss while meeting airflow requirements
 */
async function example1_DuctSizingOptimization() {
  /* istanbul ignore next */
  cov_jcfe2znb1().f[0]++;
  cov_jcfe2znb1().s[12]++;
  console.log('=== Example 1: Duct Sizing Optimization ===');
  // Define system configuration
  const systemConfig =
  /* istanbul ignore next */
  (cov_jcfe2znb1().s[13]++, {
    id: 'office_building_hvac',
    name: 'Office Building HVAC System',
    description: 'Main supply air system for 50,000 sq ft office building',
    systemType: 'supply_air',
    designAirflow: 25000,
    // CFM
    designPressure: 4.0,
    // inWC
    operatingConditions: {
      temperature: 72,
      // °F
      humidity: 45,
      // %RH
      elevation: 1000 // ft above sea level
    },
    components: [{
      id: 'main_fan',
      type: 'fan',
      specifications: {
        maxPressure: 8.0,
        maxAirflow: 30000,
        efficiency: 0.82
      }
    }, {
      id: 'main_duct',
      type: 'duct',
      specifications: {
        length: 200,
        material: 'galvanized_steel',
        roughness: 0.0015
      }
    }]
  });
  // Define optimization variables
  const variables =
  /* istanbul ignore next */
  (cov_jcfe2znb1().s[14]++, [{
    id: 'main_duct_diameter',
    name: 'Main Duct Diameter',
    description: 'Diameter of the main supply duct',
    type: 'continuous',
    bounds: {
      minimum: 18,
      maximum: 36
    },
    units: 'inches',
    currentValue: 24
  }, {
    id: 'branch_duct_diameter',
    name: 'Branch Duct Diameter',
    description: 'Diameter of branch ducts',
    type: 'continuous',
    bounds: {
      minimum: 8,
      maximum: 20
    },
    units: 'inches',
    currentValue: 12
  }, {
    id: 'fan_speed',
    name: 'Fan Speed',
    description: 'Fan operating speed percentage',
    type: 'continuous',
    bounds: {
      minimum: 60,
      maximum: 100
    },
    units: 'percent',
    currentValue: 85
  }]);
  // Define constraints
  const constraints =
  /* istanbul ignore next */
  (cov_jcfe2znb1().s[15]++, [{
    id: 'airflow_requirement',
    name: 'Minimum Airflow Requirement',
    description: 'System must deliver at least design airflow',
    type: 'inequality',
    bounds: {
      minimum: 25000
    },
    units: 'CFM',
    evaluationFunction: vars => {
      /* istanbul ignore next */
      cov_jcfe2znb1().f[1]++;
      const fanSpeed =
      /* istanbul ignore next */
      (cov_jcfe2znb1().s[16]++,
      /* istanbul ignore next */
      (cov_jcfe2znb1().b[0][0]++, vars.find(v => {
        /* istanbul ignore next */
        cov_jcfe2znb1().f[2]++;
        cov_jcfe2znb1().s[17]++;
        return v.id === 'fan_speed';
      })?.currentValue) ||
      /* istanbul ignore next */
      (cov_jcfe2znb1().b[0][1]++, 85));
      const deliveredAirflow =
      /* istanbul ignore next */
      (cov_jcfe2znb1().s[18]++, fanSpeed / 100 * 30000); // Simplified model
      /* istanbul ignore next */
      cov_jcfe2znb1().s[19]++;
      return 25000 - deliveredAirflow; // Violation if < 0
    }
  }, {
    id: 'max_pressure_loss',
    name: 'Maximum System Pressure Loss',
    description: 'Total pressure loss must not exceed fan capacity',
    type: 'inequality',
    bounds: {
      maximum: 6.0
    },
    units: 'inWC',
    evaluationFunction: vars => {
      /* istanbul ignore next */
      cov_jcfe2znb1().f[3]++;
      const mainDiameter =
      /* istanbul ignore next */
      (cov_jcfe2znb1().s[20]++,
      /* istanbul ignore next */
      (cov_jcfe2znb1().b[1][0]++, vars.find(v => {
        /* istanbul ignore next */
        cov_jcfe2znb1().f[4]++;
        cov_jcfe2znb1().s[21]++;
        return v.id === 'main_duct_diameter';
      })?.currentValue) ||
      /* istanbul ignore next */
      (cov_jcfe2znb1().b[1][1]++, 24));
      const branchDiameter =
      /* istanbul ignore next */
      (cov_jcfe2znb1().s[22]++,
      /* istanbul ignore next */
      (cov_jcfe2znb1().b[2][0]++, vars.find(v => {
        /* istanbul ignore next */
        cov_jcfe2znb1().f[5]++;
        cov_jcfe2znb1().s[23]++;
        return v.id === 'branch_duct_diameter';
      })?.currentValue) ||
      /* istanbul ignore next */
      (cov_jcfe2znb1().b[2][1]++, 12));
      const fanSpeed =
      /* istanbul ignore next */
      (cov_jcfe2znb1().s[24]++,
      /* istanbul ignore next */
      (cov_jcfe2znb1().b[3][0]++, vars.find(v => {
        /* istanbul ignore next */
        cov_jcfe2znb1().f[6]++;
        cov_jcfe2znb1().s[25]++;
        return v.id === 'fan_speed';
      })?.currentValue) ||
      /* istanbul ignore next */
      (cov_jcfe2znb1().b[3][1]++, 85));
      // Simplified pressure loss calculation
      const velocity =
      /* istanbul ignore next */
      (cov_jcfe2znb1().s[26]++, fanSpeed / 100 * 25000 / (Math.PI * (mainDiameter / 12) ** 2 / 4 * 144));
      const mainLoss =
      /* istanbul ignore next */
      (cov_jcfe2znb1().s[27]++, 0.02 * 200 * (velocity / 4005) ** 1.85 / (mainDiameter / 12) ** 1.23);
      const branchLoss =
      /* istanbul ignore next */
      (cov_jcfe2znb1().s[28]++, 0.015 * 50 * (velocity / 4005) ** 1.85 / (branchDiameter / 12) ** 1.23);
      const totalLoss =
      /* istanbul ignore next */
      (cov_jcfe2znb1().s[29]++, mainLoss + branchLoss + 1.5); // Include fittings
      /* istanbul ignore next */
      cov_jcfe2znb1().s[30]++;
      return totalLoss - 6.0; // Violation if > 0
    }
  }]);
  // Define objective function (minimize pressure loss)
  /* istanbul ignore next */
  cov_jcfe2znb1().s[31]++;
  const objectiveFunction = vars => {
    /* istanbul ignore next */
    cov_jcfe2znb1().f[7]++;
    const mainDiameter =
    /* istanbul ignore next */
    (cov_jcfe2znb1().s[32]++,
    /* istanbul ignore next */
    (cov_jcfe2znb1().b[4][0]++, vars.find(v => {
      /* istanbul ignore next */
      cov_jcfe2znb1().f[8]++;
      cov_jcfe2znb1().s[33]++;
      return v.id === 'main_duct_diameter';
    })?.currentValue) ||
    /* istanbul ignore next */
    (cov_jcfe2znb1().b[4][1]++, 24));
    const branchDiameter =
    /* istanbul ignore next */
    (cov_jcfe2znb1().s[34]++,
    /* istanbul ignore next */
    (cov_jcfe2znb1().b[5][0]++, vars.find(v => {
      /* istanbul ignore next */
      cov_jcfe2znb1().f[9]++;
      cov_jcfe2znb1().s[35]++;
      return v.id === 'branch_duct_diameter';
    })?.currentValue) ||
    /* istanbul ignore next */
    (cov_jcfe2znb1().b[5][1]++, 12));
    const fanSpeed =
    /* istanbul ignore next */
    (cov_jcfe2znb1().s[36]++,
    /* istanbul ignore next */
    (cov_jcfe2znb1().b[6][0]++, vars.find(v => {
      /* istanbul ignore next */
      cov_jcfe2znb1().f[10]++;
      cov_jcfe2znb1().s[37]++;
      return v.id === 'fan_speed';
    })?.currentValue) ||
    /* istanbul ignore next */
    (cov_jcfe2znb1().b[6][1]++, 85));
    // Calculate total system pressure loss
    const velocity =
    /* istanbul ignore next */
    (cov_jcfe2znb1().s[38]++, fanSpeed / 100 * 25000 / (Math.PI * (mainDiameter / 12) ** 2 / 4 * 144));
    const mainLoss =
    /* istanbul ignore next */
    (cov_jcfe2znb1().s[39]++, 0.02 * 200 * (velocity / 4005) ** 1.85 / (mainDiameter / 12) ** 1.23);
    const branchLoss =
    /* istanbul ignore next */
    (cov_jcfe2znb1().s[40]++, 0.015 * 50 * (velocity / 4005) ** 1.85 / (branchDiameter / 12) ** 1.23);
    /* istanbul ignore next */
    cov_jcfe2znb1().s[41]++;
    return mainLoss + branchLoss + 1.5; // Total pressure loss including fittings
  };
  // Create optimization problem
  const problem =
  /* istanbul ignore next */
  (cov_jcfe2znb1().s[42]++, {
    id: 'duct_sizing_optimization',
    name: 'Duct Sizing Optimization',
    description: 'Optimize duct diameters to minimize pressure loss',
    systemConfiguration: systemConfig,
    variables,
    objectives: {
      objectives: [{
        id: 'minimize_pressure_loss',
        objective: SystemOptimizationTypes_1.OptimizationObjective.MINIMIZE_PRESSURE_LOSS,
        weight: 1.0,
        description: 'Minimize total system pressure loss',
        evaluationFunction: objectiveFunction,
        units: 'inWC'
      }],
      aggregationMethod: 'single_objective'
    },
    constraints,
    algorithmSettings: {
      algorithm: SystemOptimizationTypes_1.OptimizationAlgorithm.GENETIC_ALGORITHM,
      parameters: {
        populationSize: 50,
        maxIterations: 100,
        crossoverRate: 0.8,
        mutationRate: 0.1
      },
      parallelization: {
        enabled: true
      },
      convergenceCriteria: {
        maxIterations: 100,
        toleranceValue: 1e-6,
        stagnationLimit: 20
      }
    },
    convergenceCriteria: {
      maxIterations: 100,
      toleranceValue: 1e-6,
      stagnationLimit: 20
    }
  });
  // Run optimization
  const ga =
  /* istanbul ignore next */
  (cov_jcfe2znb1().s[43]++, new GeneticAlgorithm_1.GeneticAlgorithm({
    populationSize: 50,
    maxGenerations: 100,
    crossoverRate: 0.8,
    mutationRate: 0.1,
    eliteSize: 5,
    constraintHandling: 'penalty',
    penaltyCoefficient: 1000
  }));
  const constraintFunctions =
  /* istanbul ignore next */
  (cov_jcfe2znb1().s[44]++, constraints.map(c => {
    /* istanbul ignore next */
    cov_jcfe2znb1().f[11]++;
    cov_jcfe2znb1().s[45]++;
    return c.evaluationFunction;
  }));
  const result =
  /* istanbul ignore next */
  (cov_jcfe2znb1().s[46]++, await ga.optimize(problem, objectiveFunction, constraintFunctions));
  /* istanbul ignore next */
  cov_jcfe2znb1().s[47]++;
  console.log('Optimization Results:');
  /* istanbul ignore next */
  cov_jcfe2znb1().s[48]++;
  console.log(`Status: ${result.status}`);
  /* istanbul ignore next */
  cov_jcfe2znb1().s[49]++;
  console.log(`Best Fitness: ${result.bestSolution.fitness.toFixed(4)} inWC`);
  /* istanbul ignore next */
  cov_jcfe2znb1().s[50]++;
  console.log(`Main Duct Diameter: ${result.bestSolution.variables['main_duct_diameter'].toFixed(1)} inches`);
  /* istanbul ignore next */
  cov_jcfe2znb1().s[51]++;
  console.log(`Branch Duct Diameter: ${result.bestSolution.variables['branch_duct_diameter'].toFixed(1)} inches`);
  /* istanbul ignore next */
  cov_jcfe2znb1().s[52]++;
  console.log(`Fan Speed: ${result.bestSolution.variables['fan_speed'].toFixed(1)}%`);
  /* istanbul ignore next */
  cov_jcfe2znb1().s[53]++;
  console.log(`Execution Time: ${result.statistics.executionTime.toFixed(2)} ms`);
  /* istanbul ignore next */
  cov_jcfe2znb1().s[54]++;
  console.log(`Total Evaluations: ${result.statistics.totalEvaluations}`);
  /* istanbul ignore next */
  cov_jcfe2znb1().s[55]++;
  return result;
}
/**
 * Example 2: Multi-Objective HVAC System Optimization
 *
 * Optimize system for multiple competing objectives: pressure loss, energy consumption, and cost
 */
async function example2_MultiObjectiveOptimization() {
  /* istanbul ignore next */
  cov_jcfe2znb1().f[12]++;
  cov_jcfe2znb1().s[56]++;
  console.log('\n=== Example 2: Multi-Objective HVAC Optimization ===');
  // Define system configuration for a commercial building
  const systemConfig =
  /* istanbul ignore next */
  (cov_jcfe2znb1().s[57]++, {
    id: 'commercial_hvac_system',
    name: 'Commercial Building HVAC System',
    description: 'Multi-zone HVAC system with VAV boxes',
    systemType: 'supply_air',
    designAirflow: 50000,
    designPressure: 5.0,
    operatingConditions: {
      temperature: 70,
      humidity: 50,
      elevation: 500
    },
    components: [{
      id: 'supply_fan',
      type: 'fan',
      specifications: {
        maxPressure: 10.0,
        maxAirflow: 60000,
        efficiency: 0.85,
        powerRating: 75 // HP
      }
    }, {
      id: 'main_supply_duct',
      type: 'duct',
      specifications: {
        length: 300,
        material: 'galvanized_steel',
        roughness: 0.0015
      }
    }]
  });
  // Define optimization variables
  const variables =
  /* istanbul ignore next */
  (cov_jcfe2znb1().s[58]++, [{
    id: 'supply_duct_diameter',
    name: 'Supply Duct Diameter',
    description: 'Main supply duct diameter',
    type: 'continuous',
    bounds: {
      minimum: 24,
      maximum: 48
    },
    units: 'inches',
    currentValue: 36
  }, {
    id: 'duct_material',
    name: 'Duct Material',
    description: 'Duct material selection',
    type: 'discrete',
    discreteValues: ['galvanized_steel', 'aluminum', 'fiberglass', 'stainless_steel'],
    units: 'material_type',
    currentValue: 'galvanized_steel'
  }, {
    id: 'insulation_thickness',
    name: 'Insulation Thickness',
    description: 'Duct insulation thickness',
    type: 'discrete',
    discreteValues: [1, 2, 3, 4],
    units: 'inches',
    currentValue: 2
  }, {
    id: 'fan_efficiency',
    name: 'Fan Efficiency',
    description: 'Fan motor efficiency selection',
    type: 'discrete',
    discreteValues: [0.80, 0.85, 0.90, 0.93, 0.95],
    units: 'efficiency',
    currentValue: 0.85
  }]);
  // Define constraints
  const constraints =
  /* istanbul ignore next */
  (cov_jcfe2znb1().s[59]++, [{
    id: 'airflow_delivery',
    name: 'Airflow Delivery Requirement',
    description: 'Must deliver design airflow',
    type: 'inequality',
    bounds: {
      minimum: 50000
    },
    units: 'CFM',
    evaluationFunction: vars => {
      /* istanbul ignore next */
      cov_jcfe2znb1().f[13]++;
      cov_jcfe2znb1().s[60]++;
      // Simplified: assume delivered airflow is close to design
      return 50000 - 49000; // Always feasible for this example
    }
  }, {
    id: 'noise_limit',
    name: 'Noise Level Limit',
    description: 'System noise must not exceed limit',
    type: 'inequality',
    bounds: {
      maximum: 55
    },
    units: 'dBA',
    evaluationFunction: vars => {
      /* istanbul ignore next */
      cov_jcfe2znb1().f[14]++;
      const diameter =
      /* istanbul ignore next */
      (cov_jcfe2znb1().s[61]++,
      /* istanbul ignore next */
      (cov_jcfe2znb1().b[7][0]++, vars.find(v => {
        /* istanbul ignore next */
        cov_jcfe2znb1().f[15]++;
        cov_jcfe2znb1().s[62]++;
        return v.id === 'supply_duct_diameter';
      })?.currentValue) ||
      /* istanbul ignore next */
      (cov_jcfe2znb1().b[7][1]++, 36));
      const velocity =
      /* istanbul ignore next */
      (cov_jcfe2znb1().s[63]++, 50000 / (Math.PI * (diameter / 12) ** 2 / 4 * 144));
      const noiseLevel =
      /* istanbul ignore next */
      (cov_jcfe2znb1().s[64]++, 30 + 20 * Math.log10(velocity / 1000)); // Simplified noise model
      /* istanbul ignore next */
      cov_jcfe2znb1().s[65]++;
      return noiseLevel - 55; // Violation if > 0
    }
  }]);
  // Define objective functions
  /* istanbul ignore next */
  cov_jcfe2znb1().s[66]++;
  const pressureLossObjective = vars => {
    /* istanbul ignore next */
    cov_jcfe2znb1().f[16]++;
    const diameter =
    /* istanbul ignore next */
    (cov_jcfe2znb1().s[67]++,
    /* istanbul ignore next */
    (cov_jcfe2znb1().b[8][0]++, vars.find(v => {
      /* istanbul ignore next */
      cov_jcfe2znb1().f[17]++;
      cov_jcfe2znb1().s[68]++;
      return v.id === 'supply_duct_diameter';
    })?.currentValue) ||
    /* istanbul ignore next */
    (cov_jcfe2znb1().b[8][1]++, 36));
    const material =
    /* istanbul ignore next */
    (cov_jcfe2znb1().s[69]++,
    /* istanbul ignore next */
    (cov_jcfe2znb1().b[9][0]++, vars.find(v => {
      /* istanbul ignore next */
      cov_jcfe2znb1().f[18]++;
      cov_jcfe2znb1().s[70]++;
      return v.id === 'duct_material';
    })?.currentValue) ||
    /* istanbul ignore next */
    (cov_jcfe2znb1().b[9][1]++, 'galvanized_steel'));
    // Material roughness factors
    const roughnessFactors =
    /* istanbul ignore next */
    (cov_jcfe2znb1().s[71]++, {
      'galvanized_steel': 1.0,
      'aluminum': 0.8,
      'fiberglass': 1.2,
      'stainless_steel': 0.6
    });
    const roughnessFactor =
    /* istanbul ignore next */
    (cov_jcfe2znb1().s[72]++,
    /* istanbul ignore next */
    (cov_jcfe2znb1().b[10][0]++, roughnessFactors[material]) ||
    /* istanbul ignore next */
    (cov_jcfe2znb1().b[10][1]++, 1.0));
    const velocity =
    /* istanbul ignore next */
    (cov_jcfe2znb1().s[73]++, 50000 / (Math.PI * (diameter / 12) ** 2 / 4 * 144));
    const pressureLoss =
    /* istanbul ignore next */
    (cov_jcfe2znb1().s[74]++, roughnessFactor * 0.025 * 300 * (velocity / 4005) ** 1.85 / (diameter / 12) ** 1.23);
    /* istanbul ignore next */
    cov_jcfe2znb1().s[75]++;
    return pressureLoss + 2.0; // Include fittings and equipment losses
  };
  /* istanbul ignore next */
  cov_jcfe2znb1().s[76]++;
  const energyConsumptionObjective = vars => {
    /* istanbul ignore next */
    cov_jcfe2znb1().f[19]++;
    const diameter =
    /* istanbul ignore next */
    (cov_jcfe2znb1().s[77]++,
    /* istanbul ignore next */
    (cov_jcfe2znb1().b[11][0]++, vars.find(v => {
      /* istanbul ignore next */
      cov_jcfe2znb1().f[20]++;
      cov_jcfe2znb1().s[78]++;
      return v.id === 'supply_duct_diameter';
    })?.currentValue) ||
    /* istanbul ignore next */
    (cov_jcfe2znb1().b[11][1]++, 36));
    const fanEfficiency =
    /* istanbul ignore next */
    (cov_jcfe2znb1().s[79]++,
    /* istanbul ignore next */
    (cov_jcfe2znb1().b[12][0]++, vars.find(v => {
      /* istanbul ignore next */
      cov_jcfe2znb1().f[21]++;
      cov_jcfe2znb1().s[80]++;
      return v.id === 'fan_efficiency';
    })?.currentValue) ||
    /* istanbul ignore next */
    (cov_jcfe2znb1().b[12][1]++, 0.85));
    // Calculate fan power based on pressure loss and efficiency
    const pressureLoss =
    /* istanbul ignore next */
    (cov_jcfe2znb1().s[81]++, pressureLossObjective(vars));
    const fanPower =
    /* istanbul ignore next */
    (cov_jcfe2znb1().s[82]++, 50000 * pressureLoss / (6356 * fanEfficiency)); // HP
    const annualEnergyConsumption =
    /* istanbul ignore next */
    (cov_jcfe2znb1().s[83]++, fanPower * 0.746 * 8760 * 0.7); // kWh/year (70% load factor)
    /* istanbul ignore next */
    cov_jcfe2znb1().s[84]++;
    return annualEnergyConsumption;
  };
  /* istanbul ignore next */
  cov_jcfe2znb1().s[85]++;
  const totalCostObjective = vars => {
    /* istanbul ignore next */
    cov_jcfe2znb1().f[22]++;
    const diameter =
    /* istanbul ignore next */
    (cov_jcfe2znb1().s[86]++,
    /* istanbul ignore next */
    (cov_jcfe2znb1().b[13][0]++, vars.find(v => {
      /* istanbul ignore next */
      cov_jcfe2znb1().f[23]++;
      cov_jcfe2znb1().s[87]++;
      return v.id === 'supply_duct_diameter';
    })?.currentValue) ||
    /* istanbul ignore next */
    (cov_jcfe2znb1().b[13][1]++, 36));
    const material =
    /* istanbul ignore next */
    (cov_jcfe2znb1().s[88]++,
    /* istanbul ignore next */
    (cov_jcfe2znb1().b[14][0]++, vars.find(v => {
      /* istanbul ignore next */
      cov_jcfe2znb1().f[24]++;
      cov_jcfe2znb1().s[89]++;
      return v.id === 'duct_material';
    })?.currentValue) ||
    /* istanbul ignore next */
    (cov_jcfe2znb1().b[14][1]++, 'galvanized_steel'));
    const insulationThickness =
    /* istanbul ignore next */
    (cov_jcfe2znb1().s[90]++,
    /* istanbul ignore next */
    (cov_jcfe2znb1().b[15][0]++, vars.find(v => {
      /* istanbul ignore next */
      cov_jcfe2znb1().f[25]++;
      cov_jcfe2znb1().s[91]++;
      return v.id === 'insulation_thickness';
    })?.currentValue) ||
    /* istanbul ignore next */
    (cov_jcfe2znb1().b[15][1]++, 2));
    const fanEfficiency =
    /* istanbul ignore next */
    (cov_jcfe2znb1().s[92]++,
    /* istanbul ignore next */
    (cov_jcfe2znb1().b[16][0]++, vars.find(v => {
      /* istanbul ignore next */
      cov_jcfe2znb1().f[26]++;
      cov_jcfe2znb1().s[93]++;
      return v.id === 'fan_efficiency';
    })?.currentValue) ||
    /* istanbul ignore next */
    (cov_jcfe2znb1().b[16][1]++, 0.85));
    // Material costs per sq ft
    const materialCosts =
    /* istanbul ignore next */
    (cov_jcfe2znb1().s[94]++, {
      'galvanized_steel': 12,
      'aluminum': 18,
      'fiberglass': 8,
      'stainless_steel': 25
    });
    // Fan efficiency premium costs
    const efficiencyPremiums =
    /* istanbul ignore next */
    (cov_jcfe2znb1().s[95]++, {
      0.80: 0,
      0.85: 500,
      0.90: 1200,
      0.93: 2000,
      0.95: 3500
    });
    const materialCost =
    /* istanbul ignore next */
    (cov_jcfe2znb1().s[96]++,
    /* istanbul ignore next */
    (cov_jcfe2znb1().b[17][0]++, materialCosts[material]) ||
    /* istanbul ignore next */
    (cov_jcfe2znb1().b[17][1]++, 12));
    const ductSurfaceArea =
    /* istanbul ignore next */
    (cov_jcfe2znb1().s[97]++, Math.PI * (diameter / 12) * 300); // sq ft
    const ductCost =
    /* istanbul ignore next */
    (cov_jcfe2znb1().s[98]++, materialCost * ductSurfaceArea);
    const insulationCost =
    /* istanbul ignore next */
    (cov_jcfe2znb1().s[99]++, insulationThickness * 3 * ductSurfaceArea);
    const fanPremium =
    /* istanbul ignore next */
    (cov_jcfe2znb1().s[100]++,
    /* istanbul ignore next */
    (cov_jcfe2znb1().b[18][0]++, efficiencyPremiums[fanEfficiency]) ||
    /* istanbul ignore next */
    (cov_jcfe2znb1().b[18][1]++, 0));
    // Operating cost (energy cost over 10 years)
    const energyConsumption =
    /* istanbul ignore next */
    (cov_jcfe2znb1().s[101]++, energyConsumptionObjective(vars));
    const operatingCost =
    /* istanbul ignore next */
    (cov_jcfe2znb1().s[102]++, energyConsumption * 0.12 * 10); // $0.12/kWh for 10 years
    /* istanbul ignore next */
    cov_jcfe2znb1().s[103]++;
    return ductCost + insulationCost + fanPremium + operatingCost;
  };
  // Create optimization problem
  const problem =
  /* istanbul ignore next */
  (cov_jcfe2znb1().s[104]++, {
    id: 'multi_objective_hvac_optimization',
    name: 'Multi-Objective HVAC System Optimization',
    description: 'Optimize for pressure loss, energy consumption, and total cost',
    systemConfiguration: systemConfig,
    variables,
    objectives: {
      objectives: [{
        id: 'minimize_pressure_loss',
        objective: SystemOptimizationTypes_1.OptimizationObjective.MINIMIZE_PRESSURE_LOSS,
        weight: 0.4,
        description: 'Minimize system pressure loss',
        evaluationFunction: pressureLossObjective,
        units: 'inWC'
      }, {
        id: 'minimize_energy_consumption',
        objective: SystemOptimizationTypes_1.OptimizationObjective.MINIMIZE_ENERGY_CONSUMPTION,
        weight: 0.4,
        description: 'Minimize annual energy consumption',
        evaluationFunction: energyConsumptionObjective,
        units: 'kWh/year'
      }, {
        id: 'minimize_total_cost',
        objective: SystemOptimizationTypes_1.OptimizationObjective.MINIMIZE_TOTAL_COST,
        weight: 0.2,
        description: 'Minimize total lifecycle cost',
        evaluationFunction: totalCostObjective,
        units: 'USD'
      }],
      aggregationMethod: 'pareto_optimal'
    },
    constraints,
    algorithmSettings: {
      algorithm: SystemOptimizationTypes_1.OptimizationAlgorithm.NSGA_II,
      parameters: {
        populationSize: 100,
        maxIterations: 150,
        crossoverRate: 0.9,
        mutationRate: 0.1
      },
      parallelization: {
        enabled: true
      },
      convergenceCriteria: {
        maxIterations: 150,
        toleranceValue: 1e-6,
        stagnationLimit: 30
      }
    },
    convergenceCriteria: {
      maxIterations: 150,
      toleranceValue: 1e-6,
      stagnationLimit: 30
    }
  });
  // Run multi-objective optimization
  const moFramework =
  /* istanbul ignore next */
  (cov_jcfe2znb1().s[105]++, new MultiObjectiveOptimizationFramework_1.MultiObjectiveOptimizationFramework({
    algorithm: 'nsga2',
    populationSize: 100,
    maxGenerations: 150,
    crossoverRate: 0.9,
    mutationRate: 0.1,
    eliteSize: 10,
    paretoSettings: {
      maxSolutions: 50,
      diversityThreshold: 0.01,
      convergenceThreshold: 1e-6,
      hypervolume: {
        enabled: true,
        referencePoint: []
      },
      spacing: {
        enabled: true,
        targetSpacing: 0.1
      }
    },
    diversityMaintenance: true,
    archiveSize: 100
  }));
  const constraintFunctions =
  /* istanbul ignore next */
  (cov_jcfe2znb1().s[106]++, constraints.map(c => {
    /* istanbul ignore next */
    cov_jcfe2znb1().f[27]++;
    cov_jcfe2znb1().s[107]++;
    return c.evaluationFunction;
  }));
  const result =
  /* istanbul ignore next */
  (cov_jcfe2znb1().s[108]++, await moFramework.optimizeMultiObjective(problem, [pressureLossObjective, energyConsumptionObjective, totalCostObjective], constraintFunctions));
  /* istanbul ignore next */
  cov_jcfe2znb1().s[109]++;
  console.log('Multi-Objective Optimization Results:');
  /* istanbul ignore next */
  cov_jcfe2znb1().s[110]++;
  console.log(`Status: ${result.status}`);
  /* istanbul ignore next */
  cov_jcfe2znb1().s[111]++;
  console.log(`Pareto Front Size: ${
  /* istanbul ignore next */
  (cov_jcfe2znb1().b[19][0]++, result.analysis?.paretoFront?.solutions.length) ||
  /* istanbul ignore next */
  (cov_jcfe2znb1().b[19][1]++, 0)}`);
  /* istanbul ignore next */
  cov_jcfe2znb1().s[112]++;
  console.log(`Hypervolume: ${
  /* istanbul ignore next */
  (cov_jcfe2znb1().b[20][0]++, result.analysis?.paretoFront?.hypervolume?.toFixed(4)) ||
  /* istanbul ignore next */
  (cov_jcfe2znb1().b[20][1]++, 'N/A')}`);
  /* istanbul ignore next */
  cov_jcfe2znb1().s[113]++;
  console.log(`Knee Points Found: ${
  /* istanbul ignore next */
  (cov_jcfe2znb1().b[21][0]++, result.analysis?.tradeoffAnalysis?.kneePoints.length) ||
  /* istanbul ignore next */
  (cov_jcfe2znb1().b[21][1]++, 0)}`);
  /* istanbul ignore next */
  cov_jcfe2znb1().s[114]++;
  console.log(`Execution Time: ${result.statistics.executionTime.toFixed(2)} ms`);
  /* istanbul ignore next */
  cov_jcfe2znb1().s[115]++;
  console.log(`Total Evaluations: ${result.statistics.totalEvaluations}`);
  // Display best compromise solution
  /* istanbul ignore next */
  cov_jcfe2znb1().s[116]++;
  if (result.bestSolution) {
    /* istanbul ignore next */
    cov_jcfe2znb1().b[22][0]++;
    cov_jcfe2znb1().s[117]++;
    console.log('\nBest Compromise Solution:');
    /* istanbul ignore next */
    cov_jcfe2znb1().s[118]++;
    console.log(`Supply Duct Diameter: ${result.bestSolution.variables['supply_duct_diameter'].toFixed(1)} inches`);
    /* istanbul ignore next */
    cov_jcfe2znb1().s[119]++;
    console.log(`Duct Material: ${result.bestSolution.variables['duct_material']}`);
    /* istanbul ignore next */
    cov_jcfe2znb1().s[120]++;
    console.log(`Insulation Thickness: ${result.bestSolution.variables['insulation_thickness']} inches`);
    /* istanbul ignore next */
    cov_jcfe2znb1().s[121]++;
    console.log(`Fan Efficiency: ${(result.bestSolution.variables['fan_efficiency'] * 100).toFixed(1)}%`);
  } else
  /* istanbul ignore next */
  {
    cov_jcfe2znb1().b[22][1]++;
  }
  cov_jcfe2znb1().s[122]++;
  return result;
}
/**
 * Example 3: Integration with Existing Phase 1/2/3 Priority 1 Components
 *
 * Demonstrates how to use optimization with existing calculation services
 */
async function example3_IntegrationWithExistingComponents() {
  /* istanbul ignore next */
  cov_jcfe2znb1().f[28]++;
  cov_jcfe2znb1().s[123]++;
  console.log('\n=== Example 3: Integration with Existing Components ===');
  // This example shows how the optimization framework integrates with:
  // - SystemPressureCalculator (Phase 1)
  // - FittingLossCalculator (Phase 1) 
  // - AdvancedFittingCalculator (Phase 3 Priority 1)
  // - AirPropertiesCalculator (Phase 2)
  const systemConfig =
  /* istanbul ignore next */
  (cov_jcfe2znb1().s[124]++, {
    id: 'integrated_system',
    name: 'Integrated HVAC System with Advanced Fittings',
    description: 'System using all Phase 1/2/3 Priority 1 components',
    systemType: 'supply_air',
    designAirflow: 15000,
    designPressure: 3.5,
    operatingConditions: {
      temperature: 68,
      humidity: 40,
      elevation: 2000
    },
    components: [{
      id: 'main_fan',
      type: 'fan',
      specifications: {
        maxPressure: 6.0,
        maxAirflow: 20000,
        efficiency: 0.88
      }
    }, {
      id: 'main_duct',
      type: 'duct',
      specifications: {
        length: 150,
        material: 'galvanized_steel',
        roughness: 0.0015
      }
    }, {
      id: 'transition_fitting',
      type: 'transition',
      specifications: {
        type: 'round_to_rectangular',
        inletDiameter: 20,
        outletWidth: 24,
        outletHeight: 12
      }
    }]
  });
  // Use SystemOptimizationEngine for integrated optimization
  const variables =
  /* istanbul ignore next */
  (cov_jcfe2znb1().s[125]++, [{
    id: 'main_duct_diameter',
    name: 'Main Duct Diameter',
    description: 'Diameter of main supply duct',
    type: 'continuous',
    bounds: {
      minimum: 16,
      maximum: 28
    },
    units: 'inches',
    currentValue: 20
  }, {
    id: 'transition_type',
    name: 'Transition Fitting Type',
    description: 'Type of transition fitting',
    type: 'discrete',
    discreteValues: ['round_to_rectangular', 'round_to_round', 'rectangular_to_round'],
    units: 'fitting_type',
    currentValue: 'round_to_rectangular'
  }]);
  const constraints =
  /* istanbul ignore next */
  (cov_jcfe2znb1().s[126]++, [{
    id: 'velocity_limit',
    name: 'Maximum Velocity Limit',
    description: 'Duct velocity must not exceed limit',
    type: 'inequality',
    bounds: {
      maximum: 2500
    },
    units: 'FPM',
    evaluationFunction: vars => {
      /* istanbul ignore next */
      cov_jcfe2znb1().f[29]++;
      const diameter =
      /* istanbul ignore next */
      (cov_jcfe2znb1().s[127]++,
      /* istanbul ignore next */
      (cov_jcfe2znb1().b[23][0]++, vars.find(v => {
        /* istanbul ignore next */
        cov_jcfe2znb1().f[30]++;
        cov_jcfe2znb1().s[128]++;
        return v.id === 'main_duct_diameter';
      })?.currentValue) ||
      /* istanbul ignore next */
      (cov_jcfe2znb1().b[23][1]++, 20));
      const velocity =
      /* istanbul ignore next */
      (cov_jcfe2znb1().s[129]++, 15000 / (Math.PI * (diameter / 12) ** 2 / 4 * 144));
      /* istanbul ignore next */
      cov_jcfe2znb1().s[130]++;
      return velocity - 2500; // Violation if > 0
    }
  }]);
  const problem =
  /* istanbul ignore next */
  (cov_jcfe2znb1().s[131]++, {
    id: 'integrated_optimization',
    name: 'Integrated System Optimization',
    description: 'Optimization using all existing calculation components',
    systemConfiguration: systemConfig,
    variables,
    objectives: {
      objectives: [{
        id: 'minimize_total_pressure_loss',
        objective: SystemOptimizationTypes_1.OptimizationObjective.MINIMIZE_PRESSURE_LOSS,
        weight: 1.0,
        description: 'Minimize total system pressure loss including advanced fittings',
        evaluationFunction: vars => {
          /* istanbul ignore next */
          cov_jcfe2znb1().f[31]++;
          // This would integrate with actual Phase 1/2/3 Priority 1 calculators
          const diameter =
          /* istanbul ignore next */
          (cov_jcfe2znb1().s[132]++,
          /* istanbul ignore next */
          (cov_jcfe2znb1().b[24][0]++, vars.find(v => {
            /* istanbul ignore next */
            cov_jcfe2znb1().f[32]++;
            cov_jcfe2znb1().s[133]++;
            return v.id === 'main_duct_diameter';
          })?.currentValue) ||
          /* istanbul ignore next */
          (cov_jcfe2znb1().b[24][1]++, 20));
          const transitionType =
          /* istanbul ignore next */
          (cov_jcfe2znb1().s[134]++,
          /* istanbul ignore next */
          (cov_jcfe2znb1().b[25][0]++, vars.find(v => {
            /* istanbul ignore next */
            cov_jcfe2znb1().f[33]++;
            cov_jcfe2znb1().s[135]++;
            return v.id === 'transition_type';
          })?.currentValue) ||
          /* istanbul ignore next */
          (cov_jcfe2znb1().b[25][1]++, 'round_to_rectangular'));
          // Simplified calculation - in practice would use:
          // - SystemPressureCalculator.calculateTotalPressureLoss()
          // - AdvancedFittingCalculator.calculateFittingLoss()
          // - AirPropertiesCalculator.getAirProperties()
          const velocity =
          /* istanbul ignore next */
          (cov_jcfe2znb1().s[136]++, 15000 / (Math.PI * (diameter / 12) ** 2 / 4 * 144));
          const ductLoss =
          /* istanbul ignore next */
          (cov_jcfe2znb1().s[137]++, 0.02 * 150 * (velocity / 4005) ** 1.85 / (diameter / 12) ** 1.23);
          // Transition fitting losses (simplified)
          const transitionLosses =
          /* istanbul ignore next */
          (cov_jcfe2znb1().s[138]++, {
            'round_to_rectangular': 0.15,
            'round_to_round': 0.05,
            'rectangular_to_round': 0.12
          });
          const fittingLoss =
          /* istanbul ignore next */
          (cov_jcfe2znb1().s[139]++,
          /* istanbul ignore next */
          (cov_jcfe2znb1().b[26][0]++, transitionLosses[transitionType]) ||
          /* istanbul ignore next */
          (cov_jcfe2znb1().b[26][1]++, 0.15));
          const velocityPressure =
          /* istanbul ignore next */
          (cov_jcfe2znb1().s[140]++, (velocity / 4005) ** 2);
          /* istanbul ignore next */
          cov_jcfe2znb1().s[141]++;
          return ductLoss + fittingLoss * velocityPressure;
        },
        units: 'inWC'
      }],
      aggregationMethod: 'single_objective'
    },
    constraints,
    algorithmSettings: {
      algorithm: SystemOptimizationTypes_1.OptimizationAlgorithm.PARTICLE_SWARM,
      parameters: {
        populationSize: 30,
        maxIterations: 75,
        inertiaWeight: 0.9,
        accelerationCoefficients: [2.0, 2.0]
      },
      parallelization: {
        enabled: false
      },
      convergenceCriteria: {
        maxIterations: 75,
        toleranceValue: 1e-6,
        stagnationLimit: 15
      }
    },
    convergenceCriteria: {
      maxIterations: 75,
      toleranceValue: 1e-6,
      stagnationLimit: 15
    }
  });
  // Use SystemOptimizationEngine for integrated optimization
  const result =
  /* istanbul ignore next */
  (cov_jcfe2znb1().s[142]++, await SystemOptimizationEngine_1.SystemOptimizationEngine.optimizeSystem(problem, SystemOptimizationTypes_1.OptimizationAlgorithm.PARTICLE_SWARM));
  /* istanbul ignore next */
  cov_jcfe2znb1().s[143]++;
  console.log('Integrated Optimization Results:');
  /* istanbul ignore next */
  cov_jcfe2znb1().s[144]++;
  console.log(`Status: ${result.status}`);
  /* istanbul ignore next */
  cov_jcfe2znb1().s[145]++;
  console.log(`Best Fitness: ${result.bestSolution.fitness.toFixed(4)} inWC`);
  /* istanbul ignore next */
  cov_jcfe2znb1().s[146]++;
  console.log(`Optimal Duct Diameter: ${result.bestSolution.variables['main_duct_diameter'].toFixed(1)} inches`);
  /* istanbul ignore next */
  cov_jcfe2znb1().s[147]++;
  console.log(`Optimal Transition Type: ${result.bestSolution.variables['transition_type']}`);
  /* istanbul ignore next */
  cov_jcfe2znb1().s[148]++;
  console.log(`Execution Time: ${result.statistics.executionTime.toFixed(2)} ms`);
  /* istanbul ignore next */
  cov_jcfe2znb1().s[149]++;
  return result;
}
/**
 * Run all optimization examples
 */
async function runAllOptimizationExamples() {
  /* istanbul ignore next */
  cov_jcfe2znb1().f[34]++;
  cov_jcfe2znb1().s[150]++;
  console.log('🚀 Running SizeWise Suite Optimization Framework Examples\n');
  /* istanbul ignore next */
  cov_jcfe2znb1().s[151]++;
  try {
    /* istanbul ignore next */
    cov_jcfe2znb1().s[152]++;
    // Example 1: Single-objective optimization
    await example1_DuctSizingOptimization();
    // Example 2: Multi-objective optimization
    /* istanbul ignore next */
    cov_jcfe2znb1().s[153]++;
    await example2_MultiObjectiveOptimization();
    // Example 3: Integration with existing components
    /* istanbul ignore next */
    cov_jcfe2znb1().s[154]++;
    await example3_IntegrationWithExistingComponents();
    /* istanbul ignore next */
    cov_jcfe2znb1().s[155]++;
    console.log('\n✅ All optimization examples completed successfully!');
    /* istanbul ignore next */
    cov_jcfe2znb1().s[156]++;
    console.log('\nNext Steps:');
    /* istanbul ignore next */
    cov_jcfe2znb1().s[157]++;
    console.log('1. Review the optimization results and trade-offs');
    /* istanbul ignore next */
    cov_jcfe2znb1().s[158]++;
    console.log('2. Integrate with your specific HVAC system requirements');
    /* istanbul ignore next */
    cov_jcfe2znb1().s[159]++;
    console.log('3. Customize objective functions for your use case');
    /* istanbul ignore next */
    cov_jcfe2znb1().s[160]++;
    console.log('4. Add additional constraints as needed');
    /* istanbul ignore next */
    cov_jcfe2znb1().s[161]++;
    console.log('5. Experiment with different optimization algorithms');
  } catch (error) {
    /* istanbul ignore next */
    cov_jcfe2znb1().s[162]++;
    console.error('❌ Error running optimization examples:', error);
    /* istanbul ignore next */
    cov_jcfe2znb1().s[163]++;
    throw error;
  }
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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