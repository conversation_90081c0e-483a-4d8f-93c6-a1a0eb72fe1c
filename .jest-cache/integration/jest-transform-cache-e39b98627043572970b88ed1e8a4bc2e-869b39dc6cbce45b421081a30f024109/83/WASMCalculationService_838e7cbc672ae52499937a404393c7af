f324a17d9060ffc5be4f738c9fa40cf8
"use strict";

/**
 * WebAssembly Calculation Service for SizeWise Suite
 *
 * Provides high-performance HVAC calculations with WASM integration:
 * - Air duct sizing calculations
 * - Pressure drop computations
 * - Heat transfer analysis
 * - Energy efficiency optimization
 * - Graceful fallback to JavaScript implementations
 */
/* istanbul ignore next */
function cov_6h6sdvf2u() {
  var path = "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\frontend\\lib\\services\\WASMCalculationService.ts";
  var hash = "56cba19ac91581239a1517c75425bbaa48d33d51";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\frontend\\lib\\services\\WASMCalculationService.ts",
    statementMap: {
      "0": {
        start: {
          line: 12,
          column: 22
        },
        end: {
          line: 22,
          column: 3
        }
      },
      "1": {
        start: {
          line: 13,
          column: 4
        },
        end: {
          line: 13,
          column: 33
        }
      },
      "2": {
        start: {
          line: 13,
          column: 26
        },
        end: {
          line: 13,
          column: 33
        }
      },
      "3": {
        start: {
          line: 14,
          column: 15
        },
        end: {
          line: 14,
          column: 52
        }
      },
      "4": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 17,
          column: 5
        }
      },
      "5": {
        start: {
          line: 16,
          column: 6
        },
        end: {
          line: 16,
          column: 68
        }
      },
      "6": {
        start: {
          line: 16,
          column: 51
        },
        end: {
          line: 16,
          column: 63
        }
      },
      "7": {
        start: {
          line: 18,
          column: 4
        },
        end: {
          line: 18,
          column: 39
        }
      },
      "8": {
        start: {
          line: 20,
          column: 4
        },
        end: {
          line: 20,
          column: 33
        }
      },
      "9": {
        start: {
          line: 20,
          column: 26
        },
        end: {
          line: 20,
          column: 33
        }
      },
      "10": {
        start: {
          line: 21,
          column: 4
        },
        end: {
          line: 21,
          column: 17
        }
      },
      "11": {
        start: {
          line: 23,
          column: 25
        },
        end: {
          line: 27,
          column: 2
        }
      },
      "12": {
        start: {
          line: 24,
          column: 4
        },
        end: {
          line: 24,
          column: 72
        }
      },
      "13": {
        start: {
          line: 26,
          column: 4
        },
        end: {
          line: 26,
          column: 21
        }
      },
      "14": {
        start: {
          line: 28,
          column: 19
        },
        end: {
          line: 44,
          column: 4
        }
      },
      "15": {
        start: {
          line: 29,
          column: 18
        },
        end: {
          line: 36,
          column: 5
        }
      },
      "16": {
        start: {
          line: 30,
          column: 8
        },
        end: {
          line: 34,
          column: 10
        }
      },
      "17": {
        start: {
          line: 31,
          column: 21
        },
        end: {
          line: 31,
          column: 23
        }
      },
      "18": {
        start: {
          line: 32,
          column: 12
        },
        end: {
          line: 32,
          column: 95
        }
      },
      "19": {
        start: {
          line: 32,
          column: 29
        },
        end: {
          line: 32,
          column: 95
        }
      },
      "20": {
        start: {
          line: 32,
          column: 77
        },
        end: {
          line: 32,
          column: 95
        }
      },
      "21": {
        start: {
          line: 33,
          column: 12
        },
        end: {
          line: 33,
          column: 22
        }
      },
      "22": {
        start: {
          line: 35,
          column: 8
        },
        end: {
          line: 35,
          column: 26
        }
      },
      "23": {
        start: {
          line: 37,
          column: 4
        },
        end: {
          line: 43,
          column: 6
        }
      },
      "24": {
        start: {
          line: 38,
          column: 8
        },
        end: {
          line: 38,
          column: 46
        }
      },
      "25": {
        start: {
          line: 38,
          column: 35
        },
        end: {
          line: 38,
          column: 46
        }
      },
      "26": {
        start: {
          line: 39,
          column: 21
        },
        end: {
          line: 39,
          column: 23
        }
      },
      "27": {
        start: {
          line: 40,
          column: 8
        },
        end: {
          line: 40,
          column: 137
        }
      },
      "28": {
        start: {
          line: 40,
          column: 25
        },
        end: {
          line: 40,
          column: 137
        }
      },
      "29": {
        start: {
          line: 40,
          column: 38
        },
        end: {
          line: 40,
          column: 50
        }
      },
      "30": {
        start: {
          line: 40,
          column: 56
        },
        end: {
          line: 40,
          column: 57
        }
      },
      "31": {
        start: {
          line: 40,
          column: 78
        },
        end: {
          line: 40,
          column: 137
        }
      },
      "32": {
        start: {
          line: 40,
          column: 102
        },
        end: {
          line: 40,
          column: 137
        }
      },
      "33": {
        start: {
          line: 41,
          column: 8
        },
        end: {
          line: 41,
          column: 40
        }
      },
      "34": {
        start: {
          line: 42,
          column: 8
        },
        end: {
          line: 42,
          column: 22
        }
      },
      "35": {
        start: {
          line: 45,
          column: 0
        },
        end: {
          line: 45,
          column: 62
        }
      },
      "36": {
        start: {
          line: 46,
          column: 0
        },
        end: {
          line: 46,
          column: 40
        }
      },
      "37": {
        start: {
          line: 52,
          column: 8
        },
        end: {
          line: 52,
          column: 31
        }
      },
      "38": {
        start: {
          line: 53,
          column: 8
        },
        end: {
          line: 53,
          column: 35
        }
      },
      "39": {
        start: {
          line: 54,
          column: 8
        },
        end: {
          line: 61,
          column: 10
        }
      },
      "40": {
        start: {
          line: 62,
          column: 8
        },
        end: {
          line: 68,
          column: 10
        }
      },
      "41": {
        start: {
          line: 74,
          column: 8
        },
        end: {
          line: 77,
          column: 9
        }
      },
      "42": {
        start: {
          line: 75,
          column: 12
        },
        end: {
          line: 75,
          column: 39
        }
      },
      "43": {
        start: {
          line: 76,
          column: 12
        },
        end: {
          line: 76,
          column: 25
        }
      },
      "44": {
        start: {
          line: 78,
          column: 8
        },
        end: {
          line: 95,
          column: 9
        }
      },
      "45": {
        start: {
          line: 80,
          column: 31
        },
        end: {
          line: 80,
          column: 121
        }
      },
      "46": {
        start: {
          line: 80,
          column: 96
        },
        end: {
          line: 80,
          column: 120
        }
      },
      "47": {
        start: {
          line: 81,
          column: 12
        },
        end: {
          line: 81,
          column: 39
        }
      },
      "48": {
        start: {
          line: 82,
          column: 12
        },
        end: {
          line: 82,
          column: 41
        }
      },
      "49": {
        start: {
          line: 83,
          column: 12
        },
        end: {
          line: 83,
          column: 38
        }
      },
      "50": {
        start: {
          line: 84,
          column: 12
        },
        end: {
          line: 86,
          column: 13
        }
      },
      "51": {
        start: {
          line: 85,
          column: 16
        },
        end: {
          line: 85,
          column: 68
        }
      },
      "52": {
        start: {
          line: 87,
          column: 12
        },
        end: {
          line: 87,
          column: 24
        }
      },
      "53": {
        start: {
          line: 90,
          column: 12
        },
        end: {
          line: 92,
          column: 13
        }
      },
      "54": {
        start: {
          line: 91,
          column: 16
        },
        end: {
          line: 91,
          column: 86
        }
      },
      "55": {
        start: {
          line: 93,
          column: 12
        },
        end: {
          line: 93,
          column: 39
        }
      },
      "56": {
        start: {
          line: 94,
          column: 12
        },
        end: {
          line: 94,
          column: 25
        }
      },
      "57": {
        start: {
          line: 98,
          column: 8
        },
        end: {
          line: 98,
          column: 62
        }
      },
      "58": {
        start: {
          line: 104,
          column: 26
        },
        end: {
          line: 104,
          column: 43
        }
      },
      "59": {
        start: {
          line: 105,
          column: 8
        },
        end: {
          line: 126,
          column: 9
        }
      },
      "60": {
        start: {
          line: 106,
          column: 12
        },
        end: {
          line: 125,
          column: 13
        }
      },
      "61": {
        start: {
          line: 107,
          column: 31
        },
        end: {
          line: 107,
          column: 229
        }
      },
      "62": {
        start: {
          line: 108,
          column: 38
        },
        end: {
          line: 108,
          column: 67
        }
      },
      "63": {
        start: {
          line: 109,
          column: 16
        },
        end: {
          line: 109,
          column: 69
        }
      },
      "64": {
        start: {
          line: 110,
          column: 16
        },
        end: {
          line: 119,
          column: 18
        }
      },
      "65": {
        start: {
          line: 122,
          column: 16
        },
        end: {
          line: 124,
          column: 17
        }
      },
      "66": {
        start: {
          line: 123,
          column: 20
        },
        end: {
          line: 123,
          column: 88
        }
      },
      "67": {
        start: {
          line: 127,
          column: 8
        },
        end: {
          line: 136,
          column: 9
        }
      },
      "68": {
        start: {
          line: 128,
          column: 27
        },
        end: {
          line: 128,
          column: 66
        }
      },
      "69": {
        start: {
          line: 129,
          column: 34
        },
        end: {
          line: 129,
          column: 63
        }
      },
      "70": {
        start: {
          line: 130,
          column: 12
        },
        end: {
          line: 130,
          column: 63
        }
      },
      "71": {
        start: {
          line: 131,
          column: 12
        },
        end: {
          line: 135,
          column: 14
        }
      },
      "72": {
        start: {
          line: 137,
          column: 8
        },
        end: {
          line: 137,
          column: 87
        }
      },
      "73": {
        start: {
          line: 141,
          column: 74
        },
        end: {
          line: 141,
          column: 84
        }
      },
      "74": {
        start: {
          line: 143,
          column: 21
        },
        end: {
          line: 143,
          column: 39
        }
      },
      "75": {
        start: {
          line: 145,
          column: 25
        },
        end: {
          line: 145,
          column: 54
        }
      },
      "76": {
        start: {
          line: 147,
          column: 35
        },
        end: {
          line: 147,
          column: 73
        }
      },
      "77": {
        start: {
          line: 148,
          column: 8
        },
        end: {
          line: 148,
          column: 45
        }
      },
      "78": {
        start: {
          line: 154,
          column: 26
        },
        end: {
          line: 154,
          column: 43
        }
      },
      "79": {
        start: {
          line: 155,
          column: 8
        },
        end: {
          line: 176,
          column: 9
        }
      },
      "80": {
        start: {
          line: 156,
          column: 12
        },
        end: {
          line: 175,
          column: 13
        }
      },
      "81": {
        start: {
          line: 157,
          column: 37
        },
        end: {
          line: 157,
          column: 72
        }
      },
      "82": {
        start: {
          line: 158,
          column: 31
        },
        end: {
          line: 158,
          column: 179
        }
      },
      "83": {
        start: {
          line: 159,
          column: 38
        },
        end: {
          line: 159,
          column: 67
        }
      },
      "84": {
        start: {
          line: 160,
          column: 16
        },
        end: {
          line: 160,
          column: 69
        }
      },
      "85": {
        start: {
          line: 161,
          column: 16
        },
        end: {
          line: 169,
          column: 18
        }
      },
      "86": {
        start: {
          line: 172,
          column: 16
        },
        end: {
          line: 174,
          column: 17
        }
      },
      "87": {
        start: {
          line: 173,
          column: 20
        },
        end: {
          line: 173,
          column: 82
        }
      },
      "88": {
        start: {
          line: 177,
          column: 8
        },
        end: {
          line: 186,
          column: 9
        }
      },
      "89": {
        start: {
          line: 178,
          column: 27
        },
        end: {
          line: 178,
          column: 67
        }
      },
      "90": {
        start: {
          line: 179,
          column: 34
        },
        end: {
          line: 179,
          column: 63
        }
      },
      "91": {
        start: {
          line: 180,
          column: 12
        },
        end: {
          line: 180,
          column: 63
        }
      },
      "92": {
        start: {
          line: 181,
          column: 12
        },
        end: {
          line: 185,
          column: 14
        }
      },
      "93": {
        start: {
          line: 187,
          column: 8
        },
        end: {
          line: 187,
          column: 87
        }
      },
      "94": {
        start: {
          line: 190,
          column: 79
        },
        end: {
          line: 190,
          column: 89
        }
      },
      "95": {
        start: {
          line: 192,
          column: 21
        },
        end: {
          line: 192,
          column: 60
        }
      },
      "96": {
        start: {
          line: 193,
          column: 25
        },
        end: {
          line: 193,
          column: 39
        }
      },
      "97": {
        start: {
          line: 195,
          column: 31
        },
        end: {
          line: 195,
          column: 35
        }
      },
      "98": {
        start: {
          line: 196,
          column: 29
        },
        end: {
          line: 197,
          column: 50
        }
      },
      "99": {
        start: {
          line: 199,
          column: 29
        },
        end: {
          line: 201,
          column: 13
        }
      },
      "100": {
        start: {
          line: 200,
          column: 12
        },
        end: {
          line: 200,
          column: 88
        }
      },
      "101": {
        start: {
          line: 203,
          column: 30
        },
        end: {
          line: 203,
          column: 47
        }
      },
      "102": {
        start: {
          line: 204,
          column: 8
        },
        end: {
          line: 204,
          column: 59
        }
      },
      "103": {
        start: {
          line: 210,
          column: 26
        },
        end: {
          line: 210,
          column: 43
        }
      },
      "104": {
        start: {
          line: 211,
          column: 8
        },
        end: {
          line: 231,
          column: 9
        }
      },
      "105": {
        start: {
          line: 212,
          column: 12
        },
        end: {
          line: 230,
          column: 13
        }
      },
      "106": {
        start: {
          line: 213,
          column: 31
        },
        end: {
          line: 213,
          column: 220
        }
      },
      "107": {
        start: {
          line: 214,
          column: 38
        },
        end: {
          line: 214,
          column: 67
        }
      },
      "108": {
        start: {
          line: 215,
          column: 16
        },
        end: {
          line: 215,
          column: 69
        }
      },
      "109": {
        start: {
          line: 216,
          column: 16
        },
        end: {
          line: 224,
          column: 18
        }
      },
      "110": {
        start: {
          line: 227,
          column: 16
        },
        end: {
          line: 229,
          column: 17
        }
      },
      "111": {
        start: {
          line: 228,
          column: 20
        },
        end: {
          line: 228,
          column: 82
        }
      },
      "112": {
        start: {
          line: 232,
          column: 8
        },
        end: {
          line: 241,
          column: 9
        }
      },
      "113": {
        start: {
          line: 233,
          column: 27
        },
        end: {
          line: 233,
          column: 67
        }
      },
      "114": {
        start: {
          line: 234,
          column: 34
        },
        end: {
          line: 234,
          column: 63
        }
      },
      "115": {
        start: {
          line: 235,
          column: 12
        },
        end: {
          line: 235,
          column: 63
        }
      },
      "116": {
        start: {
          line: 236,
          column: 12
        },
        end: {
          line: 240,
          column: 14
        }
      },
      "117": {
        start: {
          line: 242,
          column: 8
        },
        end: {
          line: 242,
          column: 87
        }
      },
      "118": {
        start: {
          line: 245,
          column: 102
        },
        end: {
          line: 245,
          column: 112
        }
      },
      "119": {
        start: {
          line: 247,
          column: 36
        },
        end: {
          line: 247,
          column: 73
        }
      },
      "120": {
        start: {
          line: 249,
          column: 37
        },
        end: {
          line: 249,
          column: 68
        }
      },
      "121": {
        start: {
          line: 250,
          column: 37
        },
        end: {
          line: 250,
          column: 62
        }
      },
      "122": {
        start: {
          line: 251,
          column: 35
        },
        end: {
          line: 251,
          column: 84
        }
      },
      "123": {
        start: {
          line: 253,
          column: 38
        },
        end: {
          line: 253,
          column: 75
        }
      },
      "124": {
        start: {
          line: 254,
          column: 8
        },
        end: {
          line: 254,
          column: 65
        }
      },
      "125": {
        start: {
          line: 257,
          column: 31
        },
        end: {
          line: 264,
          column: 9
        }
      },
      "126": {
        start: {
          line: 265,
          column: 8
        },
        end: {
          line: 265,
          column: 61
        }
      },
      "127": {
        start: {
          line: 271,
          column: 26
        },
        end: {
          line: 271,
          column: 43
        }
      },
      "128": {
        start: {
          line: 272,
          column: 8
        },
        end: {
          line: 295,
          column: 9
        }
      },
      "129": {
        start: {
          line: 273,
          column: 12
        },
        end: {
          line: 294,
          column: 13
        }
      },
      "130": {
        start: {
          line: 274,
          column: 34
        },
        end: {
          line: 274,
          column: 66
        }
      },
      "131": {
        start: {
          line: 275,
          column: 40
        },
        end: {
          line: 275,
          column: 78
        }
      },
      "132": {
        start: {
          line: 276,
          column: 40
        },
        end: {
          line: 276,
          column: 78
        }
      },
      "133": {
        start: {
          line: 277,
          column: 31
        },
        end: {
          line: 277,
          column: 112
        }
      },
      "134": {
        start: {
          line: 278,
          column: 38
        },
        end: {
          line: 278,
          column: 67
        }
      },
      "135": {
        start: {
          line: 279,
          column: 16
        },
        end: {
          line: 279,
          column: 69
        }
      },
      "136": {
        start: {
          line: 280,
          column: 16
        },
        end: {
          line: 288,
          column: 18
        }
      },
      "137": {
        start: {
          line: 291,
          column: 16
        },
        end: {
          line: 293,
          column: 17
        }
      },
      "138": {
        start: {
          line: 292,
          column: 20
        },
        end: {
          line: 292,
          column: 76
        }
      },
      "139": {
        start: {
          line: 296,
          column: 8
        },
        end: {
          line: 305,
          column: 9
        }
      },
      "140": {
        start: {
          line: 297,
          column: 27
        },
        end: {
          line: 297,
          column: 60
        }
      },
      "141": {
        start: {
          line: 298,
          column: 34
        },
        end: {
          line: 298,
          column: 63
        }
      },
      "142": {
        start: {
          line: 299,
          column: 12
        },
        end: {
          line: 299,
          column: 63
        }
      },
      "143": {
        start: {
          line: 300,
          column: 12
        },
        end: {
          line: 304,
          column: 14
        }
      },
      "144": {
        start: {
          line: 306,
          column: 8
        },
        end: {
          line: 306,
          column: 87
        }
      },
      "145": {
        start: {
          line: 310,
          column: 52
        },
        end: {
          line: 310,
          column: 62
        }
      },
      "146": {
        start: {
          line: 311,
          column: 25
        },
        end: {
          line: 311,
          column: 26
        }
      },
      "147": {
        start: {
          line: 312,
          column: 26
        },
        end: {
          line: 312,
          column: 105
        }
      },
      "148": {
        start: {
          line: 313,
          column: 8
        },
        end: {
          line: 325,
          column: 11
        }
      },
      "149": {
        start: {
          line: 315,
          column: 36
        },
        end: {
          line: 315,
          column: 121
        }
      },
      "150": {
        start: {
          line: 317,
          column: 30
        },
        end: {
          line: 317,
          column: 56
        }
      },
      "151": {
        start: {
          line: 319,
          column: 31
        },
        end: {
          line: 319,
          column: 58
        }
      },
      "152": {
        start: {
          line: 321,
          column: 30
        },
        end: {
          line: 323,
          column: 67
        }
      },
      "153": {
        start: {
          line: 324,
          column: 12
        },
        end: {
          line: 324,
          column: 36
        }
      },
      "154": {
        start: {
          line: 326,
          column: 8
        },
        end: {
          line: 326,
          column: 41
        }
      },
      "155": {
        start: {
          line: 332,
          column: 8
        },
        end: {
          line: 343,
          column: 9
        }
      },
      "156": {
        start: {
          line: 333,
          column: 12
        },
        end: {
          line: 333,
          column: 48
        }
      },
      "157": {
        start: {
          line: 334,
          column: 12
        },
        end: {
          line: 334,
          column: 67
        }
      },
      "158": {
        start: {
          line: 335,
          column: 12
        },
        end: {
          line: 336,
          column: 90
        }
      },
      "159": {
        start: {
          line: 339,
          column: 12
        },
        end: {
          line: 339,
          column: 46
        }
      },
      "160": {
        start: {
          line: 340,
          column: 12
        },
        end: {
          line: 340,
          column: 65
        }
      },
      "161": {
        start: {
          line: 341,
          column: 12
        },
        end: {
          line: 342,
          column: 86
        }
      },
      "162": {
        start: {
          line: 346,
          column: 8
        },
        end: {
          line: 351,
          column: 10
        }
      },
      "163": {
        start: {
          line: 357,
          column: 8
        },
        end: {
          line: 357,
          column: 34
        }
      },
      "164": {
        start: {
          line: 360,
          column: 8
        },
        end: {
          line: 360,
          column: 55
        }
      },
      "165": {
        start: {
          line: 363,
          column: 8
        },
        end: {
          line: 370,
          column: 10
        }
      },
      "166": {
        start: {
          line: 373,
          column: 0
        },
        end: {
          line: 373,
          column: 56
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 12,
            column: 74
          },
          end: {
            line: 12,
            column: 75
          }
        },
        loc: {
          start: {
            line: 12,
            column: 96
          },
          end: {
            line: 19,
            column: 1
          }
        },
        line: 12
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 16,
            column: 38
          },
          end: {
            line: 16,
            column: 39
          }
        },
        loc: {
          start: {
            line: 16,
            column: 49
          },
          end: {
            line: 16,
            column: 65
          }
        },
        line: 16
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 19,
            column: 6
          },
          end: {
            line: 19,
            column: 7
          }
        },
        loc: {
          start: {
            line: 19,
            column: 28
          },
          end: {
            line: 22,
            column: 1
          }
        },
        line: 19
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 23,
            column: 80
          },
          end: {
            line: 23,
            column: 81
          }
        },
        loc: {
          start: {
            line: 23,
            column: 95
          },
          end: {
            line: 25,
            column: 1
          }
        },
        line: 23
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 25,
            column: 5
          },
          end: {
            line: 25,
            column: 6
          }
        },
        loc: {
          start: {
            line: 25,
            column: 20
          },
          end: {
            line: 27,
            column: 1
          }
        },
        line: 25
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 28,
            column: 51
          },
          end: {
            line: 28,
            column: 52
          }
        },
        loc: {
          start: {
            line: 28,
            column: 63
          },
          end: {
            line: 44,
            column: 1
          }
        },
        line: 28
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 29,
            column: 18
          },
          end: {
            line: 29,
            column: 19
          }
        },
        loc: {
          start: {
            line: 29,
            column: 30
          },
          end: {
            line: 36,
            column: 5
          }
        },
        line: 29
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 30,
            column: 48
          },
          end: {
            line: 30,
            column: 49
          }
        },
        loc: {
          start: {
            line: 30,
            column: 61
          },
          end: {
            line: 34,
            column: 9
          }
        },
        line: 30
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 37,
            column: 11
          },
          end: {
            line: 37,
            column: 12
          }
        },
        loc: {
          start: {
            line: 37,
            column: 26
          },
          end: {
            line: 43,
            column: 5
          }
        },
        line: 37
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 51,
            column: 4
          },
          end: {
            line: 51,
            column: 5
          }
        },
        loc: {
          start: {
            line: 51,
            column: 29
          },
          end: {
            line: 69,
            column: 5
          }
        },
        line: 51
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 73,
            column: 4
          },
          end: {
            line: 73,
            column: 5
          }
        },
        loc: {
          start: {
            line: 73,
            column: 23
          },
          end: {
            line: 96,
            column: 5
          }
        },
        line: 73
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 80,
            column: 91
          },
          end: {
            line: 80,
            column: 92
          }
        },
        loc: {
          start: {
            line: 80,
            column: 96
          },
          end: {
            line: 80,
            column: 120
          }
        },
        line: 80
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 97,
            column: 4
          },
          end: {
            line: 97,
            column: 5
          }
        },
        loc: {
          start: {
            line: 97,
            column: 22
          },
          end: {
            line: 99,
            column: 5
          }
        },
        line: 97
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 103,
            column: 4
          },
          end: {
            line: 103,
            column: 5
          }
        },
        loc: {
          start: {
            line: 103,
            column: 37
          },
          end: {
            line: 138,
            column: 5
          }
        },
        line: 103
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 139,
            column: 4
          },
          end: {
            line: 139,
            column: 5
          }
        },
        loc: {
          start: {
            line: 139,
            column: 39
          },
          end: {
            line: 149,
            column: 5
          }
        },
        line: 139
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 153,
            column: 4
          },
          end: {
            line: 153,
            column: 5
          }
        },
        loc: {
          start: {
            line: 153,
            column: 38
          },
          end: {
            line: 188,
            column: 5
          }
        },
        line: 153
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 189,
            column: 4
          },
          end: {
            line: 189,
            column: 5
          }
        },
        loc: {
          start: {
            line: 189,
            column: 40
          },
          end: {
            line: 205,
            column: 5
          }
        },
        line: 189
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 199,
            column: 45
          },
          end: {
            line: 199,
            column: 46
          }
        },
        loc: {
          start: {
            line: 199,
            column: 65
          },
          end: {
            line: 201,
            column: 9
          }
        },
        line: 199
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 209,
            column: 4
          },
          end: {
            line: 209,
            column: 5
          }
        },
        loc: {
          start: {
            line: 209,
            column: 38
          },
          end: {
            line: 243,
            column: 5
          }
        },
        line: 209
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 244,
            column: 4
          },
          end: {
            line: 244,
            column: 5
          }
        },
        loc: {
          start: {
            line: 244,
            column: 40
          },
          end: {
            line: 255,
            column: 5
          }
        },
        line: 244
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 256,
            column: 4
          },
          end: {
            line: 256,
            column: 5
          }
        },
        loc: {
          start: {
            line: 256,
            column: 37
          },
          end: {
            line: 266,
            column: 5
          }
        },
        line: 256
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 270,
            column: 4
          },
          end: {
            line: 270,
            column: 5
          }
        },
        loc: {
          start: {
            line: 270,
            column: 31
          },
          end: {
            line: 307,
            column: 5
          }
        },
        line: 270
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 308,
            column: 4
          },
          end: {
            line: 308,
            column: 5
          }
        },
        loc: {
          start: {
            line: 308,
            column: 33
          },
          end: {
            line: 327,
            column: 5
          }
        },
        line: 308
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 313,
            column: 22
          },
          end: {
            line: 313,
            column: 23
          }
        },
        loc: {
          start: {
            line: 313,
            column: 30
          },
          end: {
            line: 325,
            column: 9
          }
        },
        line: 313
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 331,
            column: 4
          },
          end: {
            line: 331,
            column: 5
          }
        },
        loc: {
          start: {
            line: 331,
            column: 52
          },
          end: {
            line: 344,
            column: 5
          }
        },
        line: 331
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 345,
            column: 4
          },
          end: {
            line: 345,
            column: 5
          }
        },
        loc: {
          start: {
            line: 345,
            column: 28
          },
          end: {
            line: 352,
            column: 5
          }
        },
        line: 345
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 356,
            column: 4
          },
          end: {
            line: 356,
            column: 5
          }
        },
        loc: {
          start: {
            line: 356,
            column: 16
          },
          end: {
            line: 358,
            column: 5
          }
        },
        line: 356
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 359,
            column: 4
          },
          end: {
            line: 359,
            column: 5
          }
        },
        loc: {
          start: {
            line: 359,
            column: 28
          },
          end: {
            line: 361,
            column: 5
          }
        },
        line: 359
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 362,
            column: 4
          },
          end: {
            line: 362,
            column: 5
          }
        },
        loc: {
          start: {
            line: 362,
            column: 19
          },
          end: {
            line: 371,
            column: 5
          }
        },
        line: 362
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 12,
            column: 22
          },
          end: {
            line: 22,
            column: 3
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 12,
            column: 23
          },
          end: {
            line: 12,
            column: 27
          }
        }, {
          start: {
            line: 12,
            column: 31
          },
          end: {
            line: 12,
            column: 51
          }
        }, {
          start: {
            line: 12,
            column: 57
          },
          end: {
            line: 22,
            column: 2
          }
        }],
        line: 12
      },
      "1": {
        loc: {
          start: {
            line: 12,
            column: 57
          },
          end: {
            line: 22,
            column: 2
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 12,
            column: 74
          },
          end: {
            line: 19,
            column: 1
          }
        }, {
          start: {
            line: 19,
            column: 6
          },
          end: {
            line: 22,
            column: 1
          }
        }],
        line: 12
      },
      "2": {
        loc: {
          start: {
            line: 13,
            column: 4
          },
          end: {
            line: 13,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 13,
            column: 4
          },
          end: {
            line: 13,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 13
      },
      "3": {
        loc: {
          start: {
            line: 15,
            column: 4
          },
          end: {
            line: 17,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 15,
            column: 4
          },
          end: {
            line: 17,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 15
      },
      "4": {
        loc: {
          start: {
            line: 15,
            column: 8
          },
          end: {
            line: 15,
            column: 85
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 15,
            column: 8
          },
          end: {
            line: 15,
            column: 13
          }
        }, {
          start: {
            line: 15,
            column: 18
          },
          end: {
            line: 15,
            column: 84
          }
        }],
        line: 15
      },
      "5": {
        loc: {
          start: {
            line: 15,
            column: 18
          },
          end: {
            line: 15,
            column: 84
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 15,
            column: 34
          },
          end: {
            line: 15,
            column: 47
          }
        }, {
          start: {
            line: 15,
            column: 50
          },
          end: {
            line: 15,
            column: 84
          }
        }],
        line: 15
      },
      "6": {
        loc: {
          start: {
            line: 15,
            column: 50
          },
          end: {
            line: 15,
            column: 84
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 15,
            column: 50
          },
          end: {
            line: 15,
            column: 63
          }
        }, {
          start: {
            line: 15,
            column: 67
          },
          end: {
            line: 15,
            column: 84
          }
        }],
        line: 15
      },
      "7": {
        loc: {
          start: {
            line: 20,
            column: 4
          },
          end: {
            line: 20,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 20,
            column: 4
          },
          end: {
            line: 20,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 20
      },
      "8": {
        loc: {
          start: {
            line: 23,
            column: 25
          },
          end: {
            line: 27,
            column: 2
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 23,
            column: 26
          },
          end: {
            line: 23,
            column: 30
          }
        }, {
          start: {
            line: 23,
            column: 34
          },
          end: {
            line: 23,
            column: 57
          }
        }, {
          start: {
            line: 23,
            column: 63
          },
          end: {
            line: 27,
            column: 1
          }
        }],
        line: 23
      },
      "9": {
        loc: {
          start: {
            line: 23,
            column: 63
          },
          end: {
            line: 27,
            column: 1
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 23,
            column: 80
          },
          end: {
            line: 25,
            column: 1
          }
        }, {
          start: {
            line: 25,
            column: 5
          },
          end: {
            line: 27,
            column: 1
          }
        }],
        line: 23
      },
      "10": {
        loc: {
          start: {
            line: 28,
            column: 19
          },
          end: {
            line: 44,
            column: 4
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 28,
            column: 24
          }
        }, {
          start: {
            line: 28,
            column: 28
          },
          end: {
            line: 28,
            column: 45
          }
        }, {
          start: {
            line: 28,
            column: 50
          },
          end: {
            line: 44,
            column: 4
          }
        }],
        line: 28
      },
      "11": {
        loc: {
          start: {
            line: 30,
            column: 18
          },
          end: {
            line: 34,
            column: 9
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 30,
            column: 18
          },
          end: {
            line: 30,
            column: 44
          }
        }, {
          start: {
            line: 30,
            column: 48
          },
          end: {
            line: 34,
            column: 9
          }
        }],
        line: 30
      },
      "12": {
        loc: {
          start: {
            line: 32,
            column: 29
          },
          end: {
            line: 32,
            column: 95
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 32,
            column: 29
          },
          end: {
            line: 32,
            column: 95
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 32
      },
      "13": {
        loc: {
          start: {
            line: 38,
            column: 8
          },
          end: {
            line: 38,
            column: 46
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 38,
            column: 8
          },
          end: {
            line: 38,
            column: 46
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 38
      },
      "14": {
        loc: {
          start: {
            line: 38,
            column: 12
          },
          end: {
            line: 38,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 38,
            column: 12
          },
          end: {
            line: 38,
            column: 15
          }
        }, {
          start: {
            line: 38,
            column: 19
          },
          end: {
            line: 38,
            column: 33
          }
        }],
        line: 38
      },
      "15": {
        loc: {
          start: {
            line: 40,
            column: 8
          },
          end: {
            line: 40,
            column: 137
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 40,
            column: 8
          },
          end: {
            line: 40,
            column: 137
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 40
      },
      "16": {
        loc: {
          start: {
            line: 40,
            column: 78
          },
          end: {
            line: 40,
            column: 137
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 40,
            column: 78
          },
          end: {
            line: 40,
            column: 137
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 40
      },
      "17": {
        loc: {
          start: {
            line: 51,
            column: 16
          },
          end: {
            line: 51,
            column: 27
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 51,
            column: 25
          },
          end: {
            line: 51,
            column: 27
          }
        }],
        line: 51
      },
      "18": {
        loc: {
          start: {
            line: 74,
            column: 8
          },
          end: {
            line: 77,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 74,
            column: 8
          },
          end: {
            line: 77,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 74
      },
      "19": {
        loc: {
          start: {
            line: 84,
            column: 12
          },
          end: {
            line: 86,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 84,
            column: 12
          },
          end: {
            line: 86,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 84
      },
      "20": {
        loc: {
          start: {
            line: 90,
            column: 12
          },
          end: {
            line: 92,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 90,
            column: 12
          },
          end: {
            line: 92,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 90
      },
      "21": {
        loc: {
          start: {
            line: 98,
            column: 15
          },
          end: {
            line: 98,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 98,
            column: 15
          },
          end: {
            line: 98,
            column: 33
          }
        }, {
          start: {
            line: 98,
            column: 37
          },
          end: {
            line: 98,
            column: 61
          }
        }],
        line: 98
      },
      "22": {
        loc: {
          start: {
            line: 105,
            column: 8
          },
          end: {
            line: 126,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 105,
            column: 8
          },
          end: {
            line: 126,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 105
      },
      "23": {
        loc: {
          start: {
            line: 107,
            column: 139
          },
          end: {
            line: 107,
            column: 169
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 107,
            column: 139
          },
          end: {
            line: 107,
            column: 159
          }
        }, {
          start: {
            line: 107,
            column: 163
          },
          end: {
            line: 107,
            column: 169
          }
        }],
        line: 107
      },
      "24": {
        loc: {
          start: {
            line: 107,
            column: 171
          },
          end: {
            line: 107,
            column: 199
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 107,
            column: 171
          },
          end: {
            line: 107,
            column: 193
          }
        }, {
          start: {
            line: 107,
            column: 197
          },
          end: {
            line: 107,
            column: 199
          }
        }],
        line: 107
      },
      "25": {
        loc: {
          start: {
            line: 107,
            column: 201
          },
          end: {
            line: 107,
            column: 228
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 107,
            column: 201
          },
          end: {
            line: 107,
            column: 220
          }
        }, {
          start: {
            line: 107,
            column: 224
          },
          end: {
            line: 107,
            column: 228
          }
        }],
        line: 107
      },
      "26": {
        loc: {
          start: {
            line: 115,
            column: 35
          },
          end: {
            line: 115,
            column: 65
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 115,
            column: 35
          },
          end: {
            line: 115,
            column: 55
          }
        }, {
          start: {
            line: 115,
            column: 59
          },
          end: {
            line: 115,
            column: 65
          }
        }],
        line: 115
      },
      "27": {
        loc: {
          start: {
            line: 116,
            column: 37
          },
          end: {
            line: 116,
            column: 65
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 116,
            column: 37
          },
          end: {
            line: 116,
            column: 59
          }
        }, {
          start: {
            line: 116,
            column: 63
          },
          end: {
            line: 116,
            column: 65
          }
        }],
        line: 116
      },
      "28": {
        loc: {
          start: {
            line: 117,
            column: 34
          },
          end: {
            line: 117,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 117,
            column: 34
          },
          end: {
            line: 117,
            column: 53
          }
        }, {
          start: {
            line: 117,
            column: 57
          },
          end: {
            line: 117,
            column: 61
          }
        }],
        line: 117
      },
      "29": {
        loc: {
          start: {
            line: 122,
            column: 16
          },
          end: {
            line: 124,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 122,
            column: 16
          },
          end: {
            line: 124,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 122
      },
      "30": {
        loc: {
          start: {
            line: 127,
            column: 8
          },
          end: {
            line: 136,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 127,
            column: 8
          },
          end: {
            line: 136,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 127
      },
      "31": {
        loc: {
          start: {
            line: 141,
            column: 51
          },
          end: {
            line: 141,
            column: 69
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 141,
            column: 63
          },
          end: {
            line: 141,
            column: 69
          }
        }],
        line: 141
      },
      "32": {
        loc: {
          start: {
            line: 155,
            column: 8
          },
          end: {
            line: 176,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 155,
            column: 8
          },
          end: {
            line: 176,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 155
      },
      "33": {
        loc: {
          start: {
            line: 158,
            column: 153
          },
          end: {
            line: 158,
            column: 178
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 158,
            column: 153
          },
          end: {
            line: 158,
            column: 173
          }
        }, {
          start: {
            line: 158,
            column: 177
          },
          end: {
            line: 158,
            column: 178
          }
        }],
        line: 158
      },
      "34": {
        loc: {
          start: {
            line: 167,
            column: 35
          },
          end: {
            line: 167,
            column: 60
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 167,
            column: 35
          },
          end: {
            line: 167,
            column: 55
          }
        }, {
          start: {
            line: 167,
            column: 59
          },
          end: {
            line: 167,
            column: 60
          }
        }],
        line: 167
      },
      "35": {
        loc: {
          start: {
            line: 172,
            column: 16
          },
          end: {
            line: 174,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 172,
            column: 16
          },
          end: {
            line: 174,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 172
      },
      "36": {
        loc: {
          start: {
            line: 177,
            column: 8
          },
          end: {
            line: 186,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 177,
            column: 8
          },
          end: {
            line: 186,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 177
      },
      "37": {
        loc: {
          start: {
            line: 190,
            column: 61
          },
          end: {
            line: 190,
            column: 74
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 190,
            column: 73
          },
          end: {
            line: 190,
            column: 74
          }
        }],
        line: 190
      },
      "38": {
        loc: {
          start: {
            line: 211,
            column: 8
          },
          end: {
            line: 231,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 211,
            column: 8
          },
          end: {
            line: 231,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 211
      },
      "39": {
        loc: {
          start: {
            line: 213,
            column: 181
          },
          end: {
            line: 213,
            column: 219
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 213,
            column: 181
          },
          end: {
            line: 213,
            column: 213
          }
        }, {
          start: {
            line: 213,
            column: 217
          },
          end: {
            line: 213,
            column: 219
          }
        }],
        line: 213
      },
      "40": {
        loc: {
          start: {
            line: 222,
            column: 47
          },
          end: {
            line: 222,
            column: 85
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 222,
            column: 47
          },
          end: {
            line: 222,
            column: 79
          }
        }, {
          start: {
            line: 222,
            column: 83
          },
          end: {
            line: 222,
            column: 85
          }
        }],
        line: 222
      },
      "41": {
        loc: {
          start: {
            line: 227,
            column: 16
          },
          end: {
            line: 229,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 227,
            column: 16
          },
          end: {
            line: 229,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 227
      },
      "42": {
        loc: {
          start: {
            line: 232,
            column: 8
          },
          end: {
            line: 241,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 232,
            column: 8
          },
          end: {
            line: 241,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 232
      },
      "43": {
        loc: {
          start: {
            line: 245,
            column: 71
          },
          end: {
            line: 245,
            column: 97
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 245,
            column: 95
          },
          end: {
            line: 245,
            column: 97
          }
        }],
        line: 245
      },
      "44": {
        loc: {
          start: {
            line: 265,
            column: 15
          },
          end: {
            line: 265,
            column: 60
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 265,
            column: 15
          },
          end: {
            line: 265,
            column: 53
          }
        }, {
          start: {
            line: 265,
            column: 57
          },
          end: {
            line: 265,
            column: 60
          }
        }],
        line: 265
      },
      "45": {
        loc: {
          start: {
            line: 272,
            column: 8
          },
          end: {
            line: 295,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 272,
            column: 8
          },
          end: {
            line: 295,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 272
      },
      "46": {
        loc: {
          start: {
            line: 291,
            column: 16
          },
          end: {
            line: 293,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 291,
            column: 16
          },
          end: {
            line: 293,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 291
      },
      "47": {
        loc: {
          start: {
            line: 296,
            column: 8
          },
          end: {
            line: 305,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 296,
            column: 8
          },
          end: {
            line: 305,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 296
      },
      "48": {
        loc: {
          start: {
            line: 332,
            column: 8
          },
          end: {
            line: 343,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 332,
            column: 8
          },
          end: {
            line: 343,
            column: 9
          }
        }, {
          start: {
            line: 338,
            column: 13
          },
          end: {
            line: 343,
            column: 9
          }
        }],
        line: 332
      },
      "49": {
        loc: {
          start: {
            line: 350,
            column: 17
          },
          end: {
            line: 350,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 350,
            column: 17
          },
          end: {
            line: 350,
            column: 56
          }
        }, {
          start: {
            line: 350,
            column: 60
          },
          end: {
            line: 350,
            column: 61
          }
        }],
        line: 350
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0, 0],
      "9": [0, 0],
      "10": [0, 0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\frontend\\lib\\services\\WASMCalculationService.ts",
      mappings: ";AAAA;;;;;;;;;GASG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmEH,gFAAgF;AAChF,0CAA0C;AAC1C,gFAAgF;AAEhF,MAAa,sBAAsB;IAajC,YAAY,SAAgC,EAAE;QAZtC,eAAU,GAAQ,IAAI,CAAC;QACvB,kBAAa,GAAG,KAAK,CAAC;QAEtB,uBAAkB,GAAG;YAC3B,SAAS,EAAE,CAAC;YACZ,OAAO,EAAE,CAAC;YACV,aAAa,EAAE,CAAC;YAChB,WAAW,EAAE,CAAC;YACd,eAAe,EAAE,CAAC;YAClB,aAAa,EAAE,CAAC;SACjB,CAAC;QAGA,IAAI,CAAC,MAAM,GAAG;YACZ,UAAU,EAAE,IAAI;YAChB,YAAY,EAAE,IAAI;YAClB,kBAAkB,EAAE,KAAK;YACzB,cAAc,EAAE,yBAAyB;YACzC,GAAG,MAAM;SACV,CAAC;IACJ,CAAC;IAED,gFAAgF;IAChF,2BAA2B;IAC3B,gFAAgF;IAEhF,KAAK,CAAC,UAAU;QACd,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;YAC5B,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;YAC3B,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC;YACH,iCAAiC;YACjC,MAAM,UAAU,GAAG,yBAAa,IAAI,CAAC,MAAM,CAAC,cAAe,uCAAC,CAAC;YAC7D,MAAM,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC,kBAAkB;YAC9C,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;YAC7B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAE1B,IAAI,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC;gBACnC,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;YACtD,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC;gBACnC,OAAO,CAAC,IAAI,CAAC,gDAAgD,EAAE,KAAK,CAAC,CAAC;YACxE,CAAC;YACD,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;YAC3B,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,eAAe;QACb,OAAO,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC;IACxD,CAAC;IAED,gFAAgF;IAChF,+BAA+B;IAC/B,gFAAgF;IAEhF,oBAAoB,CAAC,UAA6B;QAChD,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QAEpC,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE,CAAC;YAC3B,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,uBAAuB,CACpD,UAAU,CAAC,OAAO,EAClB,UAAU,CAAC,QAAQ,EACnB,UAAU,CAAC,cAAc,EACzB,UAAU,CAAC,SAAS,IAAI,MAAM,EAC9B,UAAU,CAAC,WAAW,IAAI,EAAE,EAC5B,UAAU,CAAC,QAAQ,IAAI,IAAI,CAC5B,CAAC;gBAEF,MAAM,aAAa,GAAG,WAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBACpD,IAAI,CAAC,wBAAwB,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;gBAErD,OAAO;oBACL,KAAK,EAAE,MAAM;oBACb,aAAa;oBACb,MAAM,EAAE,MAAM;oBACd,QAAQ,EAAE;wBACR,SAAS,EAAE,UAAU,CAAC,SAAS,IAAI,MAAM;wBACzC,WAAW,EAAE,UAAU,CAAC,WAAW,IAAI,EAAE;wBACzC,QAAQ,EAAE,UAAU,CAAC,QAAQ,IAAI,IAAI;qBACtC;iBACF,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC;oBACnC,OAAO,CAAC,IAAI,CAAC,8CAA8C,EAAE,KAAK,CAAC,CAAC;gBACtE,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;YAC7B,MAAM,MAAM,GAAG,IAAI,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC;YACvD,MAAM,aAAa,GAAG,WAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YACpD,IAAI,CAAC,wBAAwB,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;YAEnD,OAAO;gBACL,KAAK,EAAE,MAAM;gBACb,aAAa;gBACb,MAAM,EAAE,YAAY;aACrB,CAAC;QACJ,CAAC;QAED,MAAM,IAAI,KAAK,CAAC,6DAA6D,CAAC,CAAC;IACjF,CAAC;IAEO,sBAAsB,CAAC,UAA6B;QAC1D,+CAA+C;QAC/C,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,cAAc,EAAE,SAAS,GAAG,MAAM,EAAE,GAAG,UAAU,CAAC;QAE7E,iCAAiC;QACjC,MAAM,IAAI,GAAG,OAAO,GAAG,QAAQ,CAAC;QAEhC,kDAAkD;QAClD,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC;QAE/C,mCAAmC;QACnC,MAAM,kBAAkB,GAAG,CAAC,GAAG,CAAC,cAAc,GAAG,SAAS,GAAG,GAAG,CAAC,CAAC;QAElE,OAAO,QAAQ,GAAG,kBAAkB,CAAC;IACvC,CAAC;IAED,gFAAgF;IAChF,6BAA6B;IAC7B,gFAAgF;IAEhF,qBAAqB,CAAC,UAAkC;QACtD,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QAEpC,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE,CAAC;YAC3B,IAAI,CAAC;gBACH,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;gBACzD,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,uBAAuB,CACpD,UAAU,CAAC,OAAO,EAClB,UAAU,CAAC,UAAU,EACrB,UAAU,CAAC,YAAY,EACvB,YAAY,EACZ,UAAU,CAAC,SAAS,IAAI,CAAC,CAC1B,CAAC;gBAEF,MAAM,aAAa,GAAG,WAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBACpD,IAAI,CAAC,wBAAwB,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;gBAErD,OAAO;oBACL,KAAK,EAAE,MAAM;oBACb,aAAa;oBACb,MAAM,EAAE,MAAM;oBACd,QAAQ,EAAE;wBACR,aAAa,EAAE,UAAU,CAAC,QAAQ,CAAC,MAAM;wBACzC,SAAS,EAAE,UAAU,CAAC,SAAS,IAAI,CAAC;qBACrC;iBACF,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC;oBACnC,OAAO,CAAC,IAAI,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;gBAChE,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;YAC7B,MAAM,MAAM,GAAG,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,CAAC;YACxD,MAAM,aAAa,GAAG,WAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YACpD,IAAI,CAAC,wBAAwB,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;YAEnD,OAAO;gBACL,KAAK,EAAE,MAAM;gBACb,aAAa;gBACb,MAAM,EAAE,YAAY;aACrB,CAAC;QACJ,CAAC;QAED,MAAM,IAAI,KAAK,CAAC,6DAA6D,CAAC,CAAC;IACjF,CAAC;IAEO,uBAAuB,CAAC,UAAkC;QAChE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,QAAQ,EAAE,SAAS,GAAG,CAAC,EAAE,GAAG,UAAU,CAAC;QAElF,qBAAqB;QACrB,MAAM,IAAI,GAAG,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QACrD,MAAM,QAAQ,GAAG,OAAO,GAAG,IAAI,CAAC;QAEhC,8DAA8D;QAC9D,MAAM,cAAc,GAAG,IAAI,CAAC,CAAC,wBAAwB;QACrD,MAAM,YAAY,GAAG,cAAc,GAAG,CAAC,UAAU,GAAG,YAAY,CAAC;YAC7C,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;QAE3D,yBAAyB;QACzB,MAAM,YAAY,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YACtD,OAAO,KAAK,GAAG,OAAO,CAAC,WAAW,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;QAC9E,CAAC,EAAE,CAAC,CAAC,CAAC;QAEN,0BAA0B;QAC1B,MAAM,aAAa,GAAG,SAAS,GAAG,KAAK,CAAC,CAAC,4BAA4B;QAErE,OAAO,YAAY,GAAG,YAAY,GAAG,aAAa,CAAC;IACrD,CAAC;IAED,gFAAgF;IAChF,6BAA6B;IAC7B,gFAAgF;IAEhF,qBAAqB,CAAC,UAAkC;QACtD,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QAEpC,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE,CAAC;YAC3B,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,uBAAuB,CACpD,UAAU,CAAC,YAAY,EACvB,UAAU,CAAC,YAAY,EACvB,UAAU,CAAC,IAAI,EACf,UAAU,CAAC,QAAQ,EACnB,UAAU,CAAC,SAAS,EACpB,UAAU,CAAC,qBAAqB,IAAI,EAAE,CACvC,CAAC;gBAEF,MAAM,aAAa,GAAG,WAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBACpD,IAAI,CAAC,wBAAwB,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;gBAErD,OAAO;oBACL,KAAK,EAAE,MAAM;oBACb,aAAa;oBACb,MAAM,EAAE,MAAM;oBACd,QAAQ,EAAE;wBACR,QAAQ,EAAE,UAAU,CAAC,QAAQ;wBAC7B,qBAAqB,EAAE,UAAU,CAAC,qBAAqB,IAAI,EAAE;qBAC9D;iBACF,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC;oBACnC,OAAO,CAAC,IAAI,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;gBAChE,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;YAC7B,MAAM,MAAM,GAAG,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,CAAC;YACxD,MAAM,aAAa,GAAG,WAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YACpD,IAAI,CAAC,wBAAwB,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;YAEnD,OAAO;gBACL,KAAK,EAAE,MAAM;gBACb,aAAa;gBACb,MAAM,EAAE,YAAY;aACrB,CAAC;QACJ,CAAC;QAED,MAAM,IAAI,KAAK,CAAC,6DAA6D,CAAC,CAAC;IACjF,CAAC;IAEO,uBAAuB,CAAC,UAAkC;QAChE,MAAM,EAAE,YAAY,EAAE,YAAY,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,qBAAqB,GAAG,EAAE,EAAE,GAAG,UAAU,CAAC;QAEzG,oDAAoD;QACpD,MAAM,mBAAmB,GAAG,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;QAElE,8CAA8C;QAC9C,MAAM,oBAAoB,GAAG,SAAS,GAAG,mBAAmB,CAAC;QAC7D,MAAM,oBAAoB,GAAG,CAAC,GAAG,qBAAqB,CAAC;QACvD,MAAM,kBAAkB,GAAG,CAAC,GAAG,CAAC,oBAAoB,GAAG,oBAAoB,CAAC,CAAC;QAE7E,+BAA+B;QAC/B,MAAM,qBAAqB,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,GAAG,YAAY,CAAC,CAAC;QACpE,OAAO,kBAAkB,GAAG,IAAI,GAAG,qBAAqB,CAAC;IAC3D,CAAC;IAEO,sBAAsB,CAAC,QAAgB;QAC7C,MAAM,cAAc,GAA2B;YAC7C,OAAO,EAAE,EAAE;YACX,UAAU,EAAE,GAAG;YACf,QAAQ,EAAE,GAAG;YACb,YAAY,EAAE,IAAI;YAClB,UAAU,EAAE,GAAG;YACf,MAAM,EAAE,IAAI;SACb,CAAC;QAEF,OAAO,cAAc,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,IAAI,GAAG,CAAC;IACvD,CAAC;IAED,gFAAgF;IAChF,sBAAsB;IACtB,gFAAgF;IAEhF,cAAc,CAAC,UAAwC;QACrD,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QAEpC,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE,CAAC;YAC3B,IAAI,CAAC;gBACH,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACnD,MAAM,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;gBAC/D,MAAM,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;gBAE/D,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,oBAAoB,CACjD,SAAS,EACT,eAAe,EACf,eAAe,CAChB,CAAC;gBAEF,MAAM,aAAa,GAAG,WAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBACpD,IAAI,CAAC,wBAAwB,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;gBAErD,OAAO;oBACL,KAAK,EAAE,MAAM;oBACb,aAAa;oBACb,MAAM,EAAE,MAAM;oBACd,QAAQ,EAAE;wBACR,UAAU,EAAE,UAAU,CAAC,KAAK,CAAC,MAAM;wBACnC,gBAAgB,EAAE,iBAAiB;qBACpC;iBACF,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC;oBACnC,OAAO,CAAC,IAAI,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;gBAC1D,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;YAC7B,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;YACjD,MAAM,aAAa,GAAG,WAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YACpD,IAAI,CAAC,wBAAwB,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;YAEnD,OAAO;gBACL,KAAK,EAAE,MAAM;gBACb,aAAa;gBACb,MAAM,EAAE,YAAY;aACrB,CAAC;QACJ,CAAC;QAED,MAAM,IAAI,KAAK,CAAC,6DAA6D,CAAC,CAAC;IACjF,CAAC;IAEO,gBAAgB,CAAC,UAAwC;QAC/D,oCAAoC;QACpC,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,UAAU,CAAC;QAEvD,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,IAAI,WAAW,GAAG,WAAW,CAAC,UAAU,GAAG,WAAW,CAAC,gBAAgB,GAAG,WAAW,CAAC,WAAW,CAAC;QAElG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACnB,kCAAkC;YAClC,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI,EAAE,WAAW,CAAC,WAAW,CAAC,GAAG,WAAW,CAAC,WAAW,CAAC;YAE9G,oCAAoC;YACpC,MAAM,SAAS,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,wBAAwB;YAEtE,qCAAqC;YACrC,MAAM,UAAU,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,yBAAyB;YAEzE,iBAAiB;YACjB,MAAM,SAAS,GAAG,CAChB,eAAe,GAAG,WAAW,CAAC,gBAAgB;gBAC9C,SAAS,GAAG,WAAW,CAAC,UAAU;gBAClC,UAAU,GAAG,WAAW,CAAC,WAAW,CACrC,GAAG,WAAW,CAAC;YAEhB,UAAU,IAAI,SAAS,CAAC;QAC1B,CAAC,CAAC,CAAC;QAEH,OAAO,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC;IACnC,CAAC;IAED,gFAAgF;IAChF,yBAAyB;IACzB,gFAAgF;IAExE,wBAAwB,CAAC,MAAqB,EAAE,aAAqB;QAC3E,IAAI,MAAM,KAAK,MAAM,EAAE,CAAC;YACtB,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,CAAC;YACpC,IAAI,CAAC,kBAAkB,CAAC,aAAa,IAAI,aAAa,CAAC;YACvD,IAAI,CAAC,kBAAkB,CAAC,eAAe;gBACrC,IAAI,CAAC,kBAAkB,CAAC,aAAa,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC;QAC9E,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;YAClC,IAAI,CAAC,kBAAkB,CAAC,WAAW,IAAI,aAAa,CAAC;YACrD,IAAI,CAAC,kBAAkB,CAAC,aAAa;gBACnC,IAAI,CAAC,kBAAkB,CAAC,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;QAC1E,CAAC;IACH,CAAC;IAED,qBAAqB;QACnB,OAAO;YACL,GAAG,IAAI,CAAC,kBAAkB;YAC1B,aAAa,EAAE,IAAI,CAAC,eAAe,EAAE;YACrC,gBAAgB,EAAE,IAAI,CAAC,kBAAkB,CAAC,aAAa;gBACtC,CAAC,IAAI,CAAC,kBAAkB,CAAC,eAAe,IAAI,CAAC,CAAC;SAChE,CAAC;IACJ,CAAC;IAED,gFAAgF;IAChF,aAAa;IACb,gFAAgF;IAEhF,SAAS;QACP,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;IAC5B,CAAC;IAED,YAAY,CAAC,SAAyC;QACpD,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,SAAS,EAAE,CAAC;IACjD,CAAC;IAED,YAAY;QACV,IAAI,CAAC,kBAAkB,GAAG;YACxB,SAAS,EAAE,CAAC;YACZ,OAAO,EAAE,CAAC;YACV,aAAa,EAAE,CAAC;YAChB,WAAW,EAAE,CAAC;YACd,eAAe,EAAE,CAAC;YAClB,aAAa,EAAE,CAAC;SACjB,CAAC;IACJ,CAAC;CACF;AA/ZD,wDA+ZC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\frontend\\lib\\services\\WASMCalculationService.ts"],
      sourcesContent: ["/**\r\n * WebAssembly Calculation Service for SizeWise Suite\r\n * \r\n * Provides high-performance HVAC calculations with WASM integration:\r\n * - Air duct sizing calculations\r\n * - Pressure drop computations\r\n * - Heat transfer analysis\r\n * - Energy efficiency optimization\r\n * - Graceful fallback to JavaScript implementations\r\n */\r\n\r\n// =============================================================================\r\n// WASM Service Types and Interfaces\r\n// =============================================================================\r\n\r\nexport interface WASMCalculationConfig {\r\n  enableWASM?: boolean;\r\n  fallbackToJS?: boolean;\r\n  performanceLogging?: boolean;\r\n  wasmModulePath?: string;\r\n}\r\n\r\nexport interface CalculationResult {\r\n  value: number;\r\n  executionTime: number;\r\n  method: 'wasm' | 'javascript';\r\n  metadata?: Record<string, any>;\r\n}\r\n\r\nexport interface AirDuctParameters {\r\n  airflow: number; // CFM\r\n  velocity: number; // ft/min\r\n  frictionFactor: number;\r\n  roughness?: number;\r\n  temperature?: number;\r\n  pressure?: number;\r\n}\r\n\r\nexport interface PressureDropParameters {\r\n  airflow: number;\r\n  ductLength: number;\r\n  ductDiameter: number;\r\n  fittings: Array<{\r\n    type: string;\r\n    coefficient: number;\r\n  }>;\r\n  elevation?: number;\r\n}\r\n\r\nexport interface HeatTransferParameters {\r\n  temperature1: number;\r\n  temperature2: number;\r\n  area: number;\r\n  material: string;\r\n  thickness: number;\r\n  convectionCoefficient?: number;\r\n}\r\n\r\nexport interface SystemOptimizationParameters {\r\n  zones: Array<{\r\n    airflow: number;\r\n    temperature: number;\r\n    area: number;\r\n  }>;\r\n  constraints: {\r\n    maxPressureDrop: number;\r\n    maxVelocity: number;\r\n    energyEfficiencyTarget: number;\r\n  };\r\n  preferences: {\r\n    costWeight: number;\r\n    efficiencyWeight: number;\r\n    noiseWeight: number;\r\n  };\r\n}\r\n\r\n// =============================================================================\r\n// WASM Calculation Service Implementation\r\n// =============================================================================\r\n\r\nexport class WASMCalculationService {\r\n  private wasmModule: any = null;\r\n  private isInitialized = false;\r\n  private config: WASMCalculationConfig;\r\n  private performanceMetrics = {\r\n    wasmCalls: 0,\r\n    jsCalls: 0,\r\n    totalWasmTime: 0,\r\n    totalJsTime: 0,\r\n    averageWasmTime: 0,\r\n    averageJsTime: 0\r\n  };\r\n\r\n  constructor(config: WASMCalculationConfig = {}) {\r\n    this.config = {\r\n      enableWASM: true,\r\n      fallbackToJS: true,\r\n      performanceLogging: false,\r\n      wasmModulePath: '../wasm/hvac_calculator',\r\n      ...config\r\n    };\r\n  }\r\n\r\n  // =============================================================================\r\n  // Initialization and Setup\r\n  // =============================================================================\r\n\r\n  async initialize(): Promise<boolean> {\r\n    if (!this.config.enableWASM) {\r\n      this.isInitialized = false;\r\n      return false;\r\n    }\r\n\r\n    try {\r\n      // Dynamic import for WASM module\r\n      const wasmModule = await import(this.config.wasmModulePath!);\r\n      await wasmModule.default(); // Initialize WASM\r\n      this.wasmModule = wasmModule;\r\n      this.isInitialized = true;\r\n      \r\n      if (this.config.performanceLogging) {\r\n        console.log('WASM module initialized successfully');\r\n      }\r\n      \r\n      return true;\r\n    } catch (error) {\r\n      if (this.config.performanceLogging) {\r\n        console.warn('WASM not available, falling back to JavaScript', error);\r\n      }\r\n      this.isInitialized = false;\r\n      return false;\r\n    }\r\n  }\r\n\r\n  isWASMAvailable(): boolean {\r\n    return this.isInitialized && this.wasmModule !== null;\r\n  }\r\n\r\n  // =============================================================================\r\n  // Air Duct Sizing Calculations\r\n  // =============================================================================\r\n\r\n  calculateAirDuctSize(parameters: AirDuctParameters): CalculationResult {\r\n    const startTime = performance.now();\r\n    \r\n    if (this.isWASMAvailable()) {\r\n      try {\r\n        const result = this.wasmModule.calculate_air_duct_size(\r\n          parameters.airflow,\r\n          parameters.velocity,\r\n          parameters.frictionFactor,\r\n          parameters.roughness || 0.0001,\r\n          parameters.temperature || 70,\r\n          parameters.pressure || 14.7\r\n        );\r\n        \r\n        const executionTime = performance.now() - startTime;\r\n        this.updatePerformanceMetrics('wasm', executionTime);\r\n        \r\n        return {\r\n          value: result,\r\n          executionTime,\r\n          method: 'wasm',\r\n          metadata: {\r\n            roughness: parameters.roughness || 0.0001,\r\n            temperature: parameters.temperature || 70,\r\n            pressure: parameters.pressure || 14.7\r\n          }\r\n        };\r\n      } catch (error) {\r\n        if (this.config.performanceLogging) {\r\n          console.warn('WASM calculation failed, falling back to JS:', error);\r\n        }\r\n      }\r\n    }\r\n\r\n    if (this.config.fallbackToJS) {\r\n      const result = this.calculateAirDuctSizeJS(parameters);\r\n      const executionTime = performance.now() - startTime;\r\n      this.updatePerformanceMetrics('js', executionTime);\r\n      \r\n      return {\r\n        value: result,\r\n        executionTime,\r\n        method: 'javascript'\r\n      };\r\n    }\r\n\r\n    throw new Error('WASM calculation failed and JavaScript fallback is disabled');\r\n  }\r\n\r\n  private calculateAirDuctSizeJS(parameters: AirDuctParameters): number {\r\n    // JavaScript implementation of air duct sizing\r\n    const { airflow, velocity, frictionFactor, roughness = 0.0001 } = parameters;\r\n    \r\n    // Calculate cross-sectional area\r\n    const area = airflow / velocity;\r\n    \r\n    // Calculate equivalent diameter for circular duct\r\n    const diameter = Math.sqrt(4 * area / Math.PI);\r\n    \r\n    // Apply friction factor correction\r\n    const frictionCorrection = 1 + (frictionFactor * roughness * 100);\r\n    \r\n    return diameter * frictionCorrection;\r\n  }\r\n\r\n  // =============================================================================\r\n  // Pressure Drop Calculations\r\n  // =============================================================================\r\n\r\n  calculatePressureDrop(parameters: PressureDropParameters): CalculationResult {\r\n    const startTime = performance.now();\r\n    \r\n    if (this.isWASMAvailable()) {\r\n      try {\r\n        const fittingsData = JSON.stringify(parameters.fittings);\r\n        const result = this.wasmModule.calculate_pressure_drop(\r\n          parameters.airflow,\r\n          parameters.ductLength,\r\n          parameters.ductDiameter,\r\n          fittingsData,\r\n          parameters.elevation || 0\r\n        );\r\n        \r\n        const executionTime = performance.now() - startTime;\r\n        this.updatePerformanceMetrics('wasm', executionTime);\r\n        \r\n        return {\r\n          value: result,\r\n          executionTime,\r\n          method: 'wasm',\r\n          metadata: {\r\n            fittingsCount: parameters.fittings.length,\r\n            elevation: parameters.elevation || 0\r\n          }\r\n        };\r\n      } catch (error) {\r\n        if (this.config.performanceLogging) {\r\n          console.warn('WASM pressure drop calculation failed:', error);\r\n        }\r\n      }\r\n    }\r\n\r\n    if (this.config.fallbackToJS) {\r\n      const result = this.calculatePressureDropJS(parameters);\r\n      const executionTime = performance.now() - startTime;\r\n      this.updatePerformanceMetrics('js', executionTime);\r\n      \r\n      return {\r\n        value: result,\r\n        executionTime,\r\n        method: 'javascript'\r\n      };\r\n    }\r\n\r\n    throw new Error('WASM calculation failed and JavaScript fallback is disabled');\r\n  }\r\n\r\n  private calculatePressureDropJS(parameters: PressureDropParameters): number {\r\n    const { airflow, ductLength, ductDiameter, fittings, elevation = 0 } = parameters;\r\n    \r\n    // Calculate velocity\r\n    const area = Math.PI * Math.pow(ductDiameter / 2, 2);\r\n    const velocity = airflow / area;\r\n    \r\n    // Friction pressure drop (simplified Darcy-Weisbach equation)\r\n    const frictionFactor = 0.02; // Simplified assumption\r\n    const frictionDrop = frictionFactor * (ductLength / ductDiameter) * \r\n                        (Math.pow(velocity, 2) / (2 * 32.174));\r\n    \r\n    // Fittings pressure drop\r\n    const fittingsDrop = fittings.reduce((total, fitting) => {\r\n      return total + fitting.coefficient * (Math.pow(velocity, 2) / (2 * 32.174));\r\n    }, 0);\r\n    \r\n    // Elevation pressure drop\r\n    const elevationDrop = elevation * 0.433; // psi per foot of elevation\r\n    \r\n    return frictionDrop + fittingsDrop + elevationDrop;\r\n  }\r\n\r\n  // =============================================================================\r\n  // Heat Transfer Calculations\r\n  // =============================================================================\r\n\r\n  calculateHeatTransfer(parameters: HeatTransferParameters): CalculationResult {\r\n    const startTime = performance.now();\r\n    \r\n    if (this.isWASMAvailable()) {\r\n      try {\r\n        const result = this.wasmModule.calculate_heat_transfer(\r\n          parameters.temperature1,\r\n          parameters.temperature2,\r\n          parameters.area,\r\n          parameters.material,\r\n          parameters.thickness,\r\n          parameters.convectionCoefficient || 10\r\n        );\r\n        \r\n        const executionTime = performance.now() - startTime;\r\n        this.updatePerformanceMetrics('wasm', executionTime);\r\n        \r\n        return {\r\n          value: result,\r\n          executionTime,\r\n          method: 'wasm',\r\n          metadata: {\r\n            material: parameters.material,\r\n            convectionCoefficient: parameters.convectionCoefficient || 10\r\n          }\r\n        };\r\n      } catch (error) {\r\n        if (this.config.performanceLogging) {\r\n          console.warn('WASM heat transfer calculation failed:', error);\r\n        }\r\n      }\r\n    }\r\n\r\n    if (this.config.fallbackToJS) {\r\n      const result = this.calculateHeatTransferJS(parameters);\r\n      const executionTime = performance.now() - startTime;\r\n      this.updatePerformanceMetrics('js', executionTime);\r\n      \r\n      return {\r\n        value: result,\r\n        executionTime,\r\n        method: 'javascript'\r\n      };\r\n    }\r\n\r\n    throw new Error('WASM calculation failed and JavaScript fallback is disabled');\r\n  }\r\n\r\n  private calculateHeatTransferJS(parameters: HeatTransferParameters): number {\r\n    const { temperature1, temperature2, area, material, thickness, convectionCoefficient = 10 } = parameters;\r\n    \r\n    // Material thermal conductivity (simplified lookup)\r\n    const thermalConductivity = this.getThermalConductivity(material);\r\n    \r\n    // Calculate overall heat transfer coefficient\r\n    const conductionResistance = thickness / thermalConductivity;\r\n    const convectionResistance = 1 / convectionCoefficient;\r\n    const overallCoefficient = 1 / (conductionResistance + convectionResistance);\r\n    \r\n    // Calculate heat transfer rate\r\n    const temperatureDifference = Math.abs(temperature1 - temperature2);\r\n    return overallCoefficient * area * temperatureDifference;\r\n  }\r\n\r\n  private getThermalConductivity(material: string): number {\r\n    const conductivities: Record<string, number> = {\r\n      'steel': 45,\r\n      'aluminum': 205,\r\n      'copper': 385,\r\n      'fiberglass': 0.04,\r\n      'concrete': 1.7,\r\n      'wood': 0.12\r\n    };\r\n    \r\n    return conductivities[material.toLowerCase()] || 1.0;\r\n  }\r\n\r\n  // =============================================================================\r\n  // System Optimization\r\n  // =============================================================================\r\n\r\n  optimizeSystem(parameters: SystemOptimizationParameters): CalculationResult {\r\n    const startTime = performance.now();\r\n    \r\n    if (this.isWASMAvailable()) {\r\n      try {\r\n        const zonesData = JSON.stringify(parameters.zones);\r\n        const constraintsData = JSON.stringify(parameters.constraints);\r\n        const preferencesData = JSON.stringify(parameters.preferences);\r\n        \r\n        const result = this.wasmModule.optimize_hvac_system(\r\n          zonesData,\r\n          constraintsData,\r\n          preferencesData\r\n        );\r\n        \r\n        const executionTime = performance.now() - startTime;\r\n        this.updatePerformanceMetrics('wasm', executionTime);\r\n        \r\n        return {\r\n          value: result,\r\n          executionTime,\r\n          method: 'wasm',\r\n          metadata: {\r\n            zonesCount: parameters.zones.length,\r\n            optimizationType: 'multi-objective'\r\n          }\r\n        };\r\n      } catch (error) {\r\n        if (this.config.performanceLogging) {\r\n          console.warn('WASM system optimization failed:', error);\r\n        }\r\n      }\r\n    }\r\n\r\n    if (this.config.fallbackToJS) {\r\n      const result = this.optimizeSystemJS(parameters);\r\n      const executionTime = performance.now() - startTime;\r\n      this.updatePerformanceMetrics('js', executionTime);\r\n      \r\n      return {\r\n        value: result,\r\n        executionTime,\r\n        method: 'javascript'\r\n      };\r\n    }\r\n\r\n    throw new Error('WASM calculation failed and JavaScript fallback is disabled');\r\n  }\r\n\r\n  private optimizeSystemJS(parameters: SystemOptimizationParameters): number {\r\n    // Simplified optimization algorithm\r\n    const { zones, constraints, preferences } = parameters;\r\n    \r\n    let totalScore = 0;\r\n    let totalWeight = preferences.costWeight + preferences.efficiencyWeight + preferences.noiseWeight;\r\n    \r\n    zones.forEach(zone => {\r\n      // Calculate zone efficiency score\r\n      const efficiencyScore = Math.min(zone.airflow / zone.area, constraints.maxVelocity) / constraints.maxVelocity;\r\n      \r\n      // Calculate cost score (simplified)\r\n      const costScore = 1 - (zone.airflow * 0.001); // Simplified cost model\r\n      \r\n      // Calculate noise score (simplified)\r\n      const noiseScore = 1 - (zone.airflow * 0.0005); // Simplified noise model\r\n      \r\n      // Weighted total\r\n      const zoneScore = (\r\n        efficiencyScore * preferences.efficiencyWeight +\r\n        costScore * preferences.costWeight +\r\n        noiseScore * preferences.noiseWeight\r\n      ) / totalWeight;\r\n      \r\n      totalScore += zoneScore;\r\n    });\r\n    \r\n    return totalScore / zones.length;\r\n  }\r\n\r\n  // =============================================================================\r\n  // Performance Monitoring\r\n  // =============================================================================\r\n\r\n  private updatePerformanceMetrics(method: 'wasm' | 'js', executionTime: number): void {\r\n    if (method === 'wasm') {\r\n      this.performanceMetrics.wasmCalls++;\r\n      this.performanceMetrics.totalWasmTime += executionTime;\r\n      this.performanceMetrics.averageWasmTime = \r\n        this.performanceMetrics.totalWasmTime / this.performanceMetrics.wasmCalls;\r\n    } else {\r\n      this.performanceMetrics.jsCalls++;\r\n      this.performanceMetrics.totalJsTime += executionTime;\r\n      this.performanceMetrics.averageJsTime = \r\n        this.performanceMetrics.totalJsTime / this.performanceMetrics.jsCalls;\r\n    }\r\n  }\r\n\r\n  getPerformanceMetrics() {\r\n    return {\r\n      ...this.performanceMetrics,\r\n      wasmAvailable: this.isWASMAvailable(),\r\n      performanceRatio: this.performanceMetrics.averageJsTime / \r\n                       (this.performanceMetrics.averageWasmTime || 1)\r\n    };\r\n  }\r\n\r\n  // =============================================================================\r\n  // Public API\r\n  // =============================================================================\r\n\r\n  getConfig(): WASMCalculationConfig {\r\n    return { ...this.config };\r\n  }\r\n\r\n  updateConfig(newConfig: Partial<WASMCalculationConfig>): void {\r\n    this.config = { ...this.config, ...newConfig };\r\n  }\r\n\r\n  resetMetrics(): void {\r\n    this.performanceMetrics = {\r\n      wasmCalls: 0,\r\n      jsCalls: 0,\r\n      totalWasmTime: 0,\r\n      totalJsTime: 0,\r\n      averageWasmTime: 0,\r\n      averageJsTime: 0\r\n    };\r\n  }\r\n}\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "56cba19ac91581239a1517c75425bbaa48d33d51"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_6h6sdvf2u = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_6h6sdvf2u();
var __createBinding =
/* istanbul ignore next */
(cov_6h6sdvf2u().s[0]++,
/* istanbul ignore next */
(cov_6h6sdvf2u().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_6h6sdvf2u().b[0][1]++, this.__createBinding) ||
/* istanbul ignore next */
(cov_6h6sdvf2u().b[0][2]++, Object.create ?
/* istanbul ignore next */
(cov_6h6sdvf2u().b[1][0]++, function (o, m, k, k2) {
  /* istanbul ignore next */
  cov_6h6sdvf2u().f[0]++;
  cov_6h6sdvf2u().s[1]++;
  if (k2 === undefined) {
    /* istanbul ignore next */
    cov_6h6sdvf2u().b[2][0]++;
    cov_6h6sdvf2u().s[2]++;
    k2 = k;
  } else
  /* istanbul ignore next */
  {
    cov_6h6sdvf2u().b[2][1]++;
  }
  var desc =
  /* istanbul ignore next */
  (cov_6h6sdvf2u().s[3]++, Object.getOwnPropertyDescriptor(m, k));
  /* istanbul ignore next */
  cov_6h6sdvf2u().s[4]++;
  if (
  /* istanbul ignore next */
  (cov_6h6sdvf2u().b[4][0]++, !desc) ||
  /* istanbul ignore next */
  (cov_6h6sdvf2u().b[4][1]++, "get" in desc ?
  /* istanbul ignore next */
  (cov_6h6sdvf2u().b[5][0]++, !m.__esModule) :
  /* istanbul ignore next */
  (cov_6h6sdvf2u().b[5][1]++,
  /* istanbul ignore next */
  (cov_6h6sdvf2u().b[6][0]++, desc.writable) ||
  /* istanbul ignore next */
  (cov_6h6sdvf2u().b[6][1]++, desc.configurable)))) {
    /* istanbul ignore next */
    cov_6h6sdvf2u().b[3][0]++;
    cov_6h6sdvf2u().s[5]++;
    desc = {
      enumerable: true,
      get: function () {
        /* istanbul ignore next */
        cov_6h6sdvf2u().f[1]++;
        cov_6h6sdvf2u().s[6]++;
        return m[k];
      }
    };
  } else
  /* istanbul ignore next */
  {
    cov_6h6sdvf2u().b[3][1]++;
  }
  cov_6h6sdvf2u().s[7]++;
  Object.defineProperty(o, k2, desc);
}) :
/* istanbul ignore next */
(cov_6h6sdvf2u().b[1][1]++, function (o, m, k, k2) {
  /* istanbul ignore next */
  cov_6h6sdvf2u().f[2]++;
  cov_6h6sdvf2u().s[8]++;
  if (k2 === undefined) {
    /* istanbul ignore next */
    cov_6h6sdvf2u().b[7][0]++;
    cov_6h6sdvf2u().s[9]++;
    k2 = k;
  } else
  /* istanbul ignore next */
  {
    cov_6h6sdvf2u().b[7][1]++;
  }
  cov_6h6sdvf2u().s[10]++;
  o[k2] = m[k];
})));
var __setModuleDefault =
/* istanbul ignore next */
(cov_6h6sdvf2u().s[11]++,
/* istanbul ignore next */
(cov_6h6sdvf2u().b[8][0]++, this) &&
/* istanbul ignore next */
(cov_6h6sdvf2u().b[8][1]++, this.__setModuleDefault) ||
/* istanbul ignore next */
(cov_6h6sdvf2u().b[8][2]++, Object.create ?
/* istanbul ignore next */
(cov_6h6sdvf2u().b[9][0]++, function (o, v) {
  /* istanbul ignore next */
  cov_6h6sdvf2u().f[3]++;
  cov_6h6sdvf2u().s[12]++;
  Object.defineProperty(o, "default", {
    enumerable: true,
    value: v
  });
}) :
/* istanbul ignore next */
(cov_6h6sdvf2u().b[9][1]++, function (o, v) {
  /* istanbul ignore next */
  cov_6h6sdvf2u().f[4]++;
  cov_6h6sdvf2u().s[13]++;
  o["default"] = v;
})));
var __importStar =
/* istanbul ignore next */
(cov_6h6sdvf2u().s[14]++,
/* istanbul ignore next */
(cov_6h6sdvf2u().b[10][0]++, this) &&
/* istanbul ignore next */
(cov_6h6sdvf2u().b[10][1]++, this.__importStar) ||
/* istanbul ignore next */
(cov_6h6sdvf2u().b[10][2]++, function () {
  /* istanbul ignore next */
  cov_6h6sdvf2u().f[5]++;
  cov_6h6sdvf2u().s[15]++;
  var ownKeys = function (o) {
    /* istanbul ignore next */
    cov_6h6sdvf2u().f[6]++;
    cov_6h6sdvf2u().s[16]++;
    ownKeys =
    /* istanbul ignore next */
    (cov_6h6sdvf2u().b[11][0]++, Object.getOwnPropertyNames) ||
    /* istanbul ignore next */
    (cov_6h6sdvf2u().b[11][1]++, function (o) {
      /* istanbul ignore next */
      cov_6h6sdvf2u().f[7]++;
      var ar =
      /* istanbul ignore next */
      (cov_6h6sdvf2u().s[17]++, []);
      /* istanbul ignore next */
      cov_6h6sdvf2u().s[18]++;
      for (var k in o) {
        /* istanbul ignore next */
        cov_6h6sdvf2u().s[19]++;
        if (Object.prototype.hasOwnProperty.call(o, k)) {
          /* istanbul ignore next */
          cov_6h6sdvf2u().b[12][0]++;
          cov_6h6sdvf2u().s[20]++;
          ar[ar.length] = k;
        } else
        /* istanbul ignore next */
        {
          cov_6h6sdvf2u().b[12][1]++;
        }
      }
      /* istanbul ignore next */
      cov_6h6sdvf2u().s[21]++;
      return ar;
    });
    /* istanbul ignore next */
    cov_6h6sdvf2u().s[22]++;
    return ownKeys(o);
  };
  /* istanbul ignore next */
  cov_6h6sdvf2u().s[23]++;
  return function (mod) {
    /* istanbul ignore next */
    cov_6h6sdvf2u().f[8]++;
    cov_6h6sdvf2u().s[24]++;
    if (
    /* istanbul ignore next */
    (cov_6h6sdvf2u().b[14][0]++, mod) &&
    /* istanbul ignore next */
    (cov_6h6sdvf2u().b[14][1]++, mod.__esModule)) {
      /* istanbul ignore next */
      cov_6h6sdvf2u().b[13][0]++;
      cov_6h6sdvf2u().s[25]++;
      return mod;
    } else
    /* istanbul ignore next */
    {
      cov_6h6sdvf2u().b[13][1]++;
    }
    var result =
    /* istanbul ignore next */
    (cov_6h6sdvf2u().s[26]++, {});
    /* istanbul ignore next */
    cov_6h6sdvf2u().s[27]++;
    if (mod != null) {
      /* istanbul ignore next */
      cov_6h6sdvf2u().b[15][0]++;
      cov_6h6sdvf2u().s[28]++;
      for (var k =
        /* istanbul ignore next */
        (cov_6h6sdvf2u().s[29]++, ownKeys(mod)), i =
        /* istanbul ignore next */
        (cov_6h6sdvf2u().s[30]++, 0); i < k.length; i++) {
        /* istanbul ignore next */
        cov_6h6sdvf2u().s[31]++;
        if (k[i] !== "default") {
          /* istanbul ignore next */
          cov_6h6sdvf2u().b[16][0]++;
          cov_6h6sdvf2u().s[32]++;
          __createBinding(result, mod, k[i]);
        } else
        /* istanbul ignore next */
        {
          cov_6h6sdvf2u().b[16][1]++;
        }
      }
    } else
    /* istanbul ignore next */
    {
      cov_6h6sdvf2u().b[15][1]++;
    }
    cov_6h6sdvf2u().s[33]++;
    __setModuleDefault(result, mod);
    /* istanbul ignore next */
    cov_6h6sdvf2u().s[34]++;
    return result;
  };
}()));
/* istanbul ignore next */
cov_6h6sdvf2u().s[35]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_6h6sdvf2u().s[36]++;
exports.WASMCalculationService = void 0;
// =============================================================================
// WASM Calculation Service Implementation
// =============================================================================
class WASMCalculationService {
  constructor(config =
  /* istanbul ignore next */
  (cov_6h6sdvf2u().b[17][0]++, {})) {
    /* istanbul ignore next */
    cov_6h6sdvf2u().f[9]++;
    cov_6h6sdvf2u().s[37]++;
    this.wasmModule = null;
    /* istanbul ignore next */
    cov_6h6sdvf2u().s[38]++;
    this.isInitialized = false;
    /* istanbul ignore next */
    cov_6h6sdvf2u().s[39]++;
    this.performanceMetrics = {
      wasmCalls: 0,
      jsCalls: 0,
      totalWasmTime: 0,
      totalJsTime: 0,
      averageWasmTime: 0,
      averageJsTime: 0
    };
    /* istanbul ignore next */
    cov_6h6sdvf2u().s[40]++;
    this.config = {
      enableWASM: true,
      fallbackToJS: true,
      performanceLogging: false,
      wasmModulePath: '../wasm/hvac_calculator',
      ...config
    };
  }
  // =============================================================================
  // Initialization and Setup
  // =============================================================================
  async initialize() {
    /* istanbul ignore next */
    cov_6h6sdvf2u().f[10]++;
    cov_6h6sdvf2u().s[41]++;
    if (!this.config.enableWASM) {
      /* istanbul ignore next */
      cov_6h6sdvf2u().b[18][0]++;
      cov_6h6sdvf2u().s[42]++;
      this.isInitialized = false;
      /* istanbul ignore next */
      cov_6h6sdvf2u().s[43]++;
      return false;
    } else
    /* istanbul ignore next */
    {
      cov_6h6sdvf2u().b[18][1]++;
    }
    cov_6h6sdvf2u().s[44]++;
    try {
      // Dynamic import for WASM module
      const wasmModule =
      /* istanbul ignore next */
      (cov_6h6sdvf2u().s[45]++, await Promise.resolve(`${this.config.wasmModulePath}`).then(s => {
        /* istanbul ignore next */
        cov_6h6sdvf2u().f[11]++;
        cov_6h6sdvf2u().s[46]++;
        return __importStar(require(s));
      }));
      /* istanbul ignore next */
      cov_6h6sdvf2u().s[47]++;
      await wasmModule.default(); // Initialize WASM
      /* istanbul ignore next */
      cov_6h6sdvf2u().s[48]++;
      this.wasmModule = wasmModule;
      /* istanbul ignore next */
      cov_6h6sdvf2u().s[49]++;
      this.isInitialized = true;
      /* istanbul ignore next */
      cov_6h6sdvf2u().s[50]++;
      if (this.config.performanceLogging) {
        /* istanbul ignore next */
        cov_6h6sdvf2u().b[19][0]++;
        cov_6h6sdvf2u().s[51]++;
        console.log('WASM module initialized successfully');
      } else
      /* istanbul ignore next */
      {
        cov_6h6sdvf2u().b[19][1]++;
      }
      cov_6h6sdvf2u().s[52]++;
      return true;
    } catch (error) {
      /* istanbul ignore next */
      cov_6h6sdvf2u().s[53]++;
      if (this.config.performanceLogging) {
        /* istanbul ignore next */
        cov_6h6sdvf2u().b[20][0]++;
        cov_6h6sdvf2u().s[54]++;
        console.warn('WASM not available, falling back to JavaScript', error);
      } else
      /* istanbul ignore next */
      {
        cov_6h6sdvf2u().b[20][1]++;
      }
      cov_6h6sdvf2u().s[55]++;
      this.isInitialized = false;
      /* istanbul ignore next */
      cov_6h6sdvf2u().s[56]++;
      return false;
    }
  }
  isWASMAvailable() {
    /* istanbul ignore next */
    cov_6h6sdvf2u().f[12]++;
    cov_6h6sdvf2u().s[57]++;
    return /* istanbul ignore next */(cov_6h6sdvf2u().b[21][0]++, this.isInitialized) &&
    /* istanbul ignore next */
    (cov_6h6sdvf2u().b[21][1]++, this.wasmModule !== null);
  }
  // =============================================================================
  // Air Duct Sizing Calculations
  // =============================================================================
  calculateAirDuctSize(parameters) {
    /* istanbul ignore next */
    cov_6h6sdvf2u().f[13]++;
    const startTime =
    /* istanbul ignore next */
    (cov_6h6sdvf2u().s[58]++, performance.now());
    /* istanbul ignore next */
    cov_6h6sdvf2u().s[59]++;
    if (this.isWASMAvailable()) {
      /* istanbul ignore next */
      cov_6h6sdvf2u().b[22][0]++;
      cov_6h6sdvf2u().s[60]++;
      try {
        const result =
        /* istanbul ignore next */
        (cov_6h6sdvf2u().s[61]++, this.wasmModule.calculate_air_duct_size(parameters.airflow, parameters.velocity, parameters.frictionFactor,
        /* istanbul ignore next */
        (cov_6h6sdvf2u().b[23][0]++, parameters.roughness) ||
        /* istanbul ignore next */
        (cov_6h6sdvf2u().b[23][1]++, 0.0001),
        /* istanbul ignore next */
        (cov_6h6sdvf2u().b[24][0]++, parameters.temperature) ||
        /* istanbul ignore next */
        (cov_6h6sdvf2u().b[24][1]++, 70),
        /* istanbul ignore next */
        (cov_6h6sdvf2u().b[25][0]++, parameters.pressure) ||
        /* istanbul ignore next */
        (cov_6h6sdvf2u().b[25][1]++, 14.7)));
        const executionTime =
        /* istanbul ignore next */
        (cov_6h6sdvf2u().s[62]++, performance.now() - startTime);
        /* istanbul ignore next */
        cov_6h6sdvf2u().s[63]++;
        this.updatePerformanceMetrics('wasm', executionTime);
        /* istanbul ignore next */
        cov_6h6sdvf2u().s[64]++;
        return {
          value: result,
          executionTime,
          method: 'wasm',
          metadata: {
            roughness:
            /* istanbul ignore next */
            (cov_6h6sdvf2u().b[26][0]++, parameters.roughness) ||
            /* istanbul ignore next */
            (cov_6h6sdvf2u().b[26][1]++, 0.0001),
            temperature:
            /* istanbul ignore next */
            (cov_6h6sdvf2u().b[27][0]++, parameters.temperature) ||
            /* istanbul ignore next */
            (cov_6h6sdvf2u().b[27][1]++, 70),
            pressure:
            /* istanbul ignore next */
            (cov_6h6sdvf2u().b[28][0]++, parameters.pressure) ||
            /* istanbul ignore next */
            (cov_6h6sdvf2u().b[28][1]++, 14.7)
          }
        };
      } catch (error) {
        /* istanbul ignore next */
        cov_6h6sdvf2u().s[65]++;
        if (this.config.performanceLogging) {
          /* istanbul ignore next */
          cov_6h6sdvf2u().b[29][0]++;
          cov_6h6sdvf2u().s[66]++;
          console.warn('WASM calculation failed, falling back to JS:', error);
        } else
        /* istanbul ignore next */
        {
          cov_6h6sdvf2u().b[29][1]++;
        }
      }
    } else
    /* istanbul ignore next */
    {
      cov_6h6sdvf2u().b[22][1]++;
    }
    cov_6h6sdvf2u().s[67]++;
    if (this.config.fallbackToJS) {
      /* istanbul ignore next */
      cov_6h6sdvf2u().b[30][0]++;
      const result =
      /* istanbul ignore next */
      (cov_6h6sdvf2u().s[68]++, this.calculateAirDuctSizeJS(parameters));
      const executionTime =
      /* istanbul ignore next */
      (cov_6h6sdvf2u().s[69]++, performance.now() - startTime);
      /* istanbul ignore next */
      cov_6h6sdvf2u().s[70]++;
      this.updatePerformanceMetrics('js', executionTime);
      /* istanbul ignore next */
      cov_6h6sdvf2u().s[71]++;
      return {
        value: result,
        executionTime,
        method: 'javascript'
      };
    } else
    /* istanbul ignore next */
    {
      cov_6h6sdvf2u().b[30][1]++;
    }
    cov_6h6sdvf2u().s[72]++;
    throw new Error('WASM calculation failed and JavaScript fallback is disabled');
  }
  calculateAirDuctSizeJS(parameters) {
    /* istanbul ignore next */
    cov_6h6sdvf2u().f[14]++;
    // JavaScript implementation of air duct sizing
    const {
      airflow,
      velocity,
      frictionFactor,
      roughness =
      /* istanbul ignore next */
      (cov_6h6sdvf2u().b[31][0]++, 0.0001)
    } =
    /* istanbul ignore next */
    (cov_6h6sdvf2u().s[73]++, parameters);
    // Calculate cross-sectional area
    const area =
    /* istanbul ignore next */
    (cov_6h6sdvf2u().s[74]++, airflow / velocity);
    // Calculate equivalent diameter for circular duct
    const diameter =
    /* istanbul ignore next */
    (cov_6h6sdvf2u().s[75]++, Math.sqrt(4 * area / Math.PI));
    // Apply friction factor correction
    const frictionCorrection =
    /* istanbul ignore next */
    (cov_6h6sdvf2u().s[76]++, 1 + frictionFactor * roughness * 100);
    /* istanbul ignore next */
    cov_6h6sdvf2u().s[77]++;
    return diameter * frictionCorrection;
  }
  // =============================================================================
  // Pressure Drop Calculations
  // =============================================================================
  calculatePressureDrop(parameters) {
    /* istanbul ignore next */
    cov_6h6sdvf2u().f[15]++;
    const startTime =
    /* istanbul ignore next */
    (cov_6h6sdvf2u().s[78]++, performance.now());
    /* istanbul ignore next */
    cov_6h6sdvf2u().s[79]++;
    if (this.isWASMAvailable()) {
      /* istanbul ignore next */
      cov_6h6sdvf2u().b[32][0]++;
      cov_6h6sdvf2u().s[80]++;
      try {
        const fittingsData =
        /* istanbul ignore next */
        (cov_6h6sdvf2u().s[81]++, JSON.stringify(parameters.fittings));
        const result =
        /* istanbul ignore next */
        (cov_6h6sdvf2u().s[82]++, this.wasmModule.calculate_pressure_drop(parameters.airflow, parameters.ductLength, parameters.ductDiameter, fittingsData,
        /* istanbul ignore next */
        (cov_6h6sdvf2u().b[33][0]++, parameters.elevation) ||
        /* istanbul ignore next */
        (cov_6h6sdvf2u().b[33][1]++, 0)));
        const executionTime =
        /* istanbul ignore next */
        (cov_6h6sdvf2u().s[83]++, performance.now() - startTime);
        /* istanbul ignore next */
        cov_6h6sdvf2u().s[84]++;
        this.updatePerformanceMetrics('wasm', executionTime);
        /* istanbul ignore next */
        cov_6h6sdvf2u().s[85]++;
        return {
          value: result,
          executionTime,
          method: 'wasm',
          metadata: {
            fittingsCount: parameters.fittings.length,
            elevation:
            /* istanbul ignore next */
            (cov_6h6sdvf2u().b[34][0]++, parameters.elevation) ||
            /* istanbul ignore next */
            (cov_6h6sdvf2u().b[34][1]++, 0)
          }
        };
      } catch (error) {
        /* istanbul ignore next */
        cov_6h6sdvf2u().s[86]++;
        if (this.config.performanceLogging) {
          /* istanbul ignore next */
          cov_6h6sdvf2u().b[35][0]++;
          cov_6h6sdvf2u().s[87]++;
          console.warn('WASM pressure drop calculation failed:', error);
        } else
        /* istanbul ignore next */
        {
          cov_6h6sdvf2u().b[35][1]++;
        }
      }
    } else
    /* istanbul ignore next */
    {
      cov_6h6sdvf2u().b[32][1]++;
    }
    cov_6h6sdvf2u().s[88]++;
    if (this.config.fallbackToJS) {
      /* istanbul ignore next */
      cov_6h6sdvf2u().b[36][0]++;
      const result =
      /* istanbul ignore next */
      (cov_6h6sdvf2u().s[89]++, this.calculatePressureDropJS(parameters));
      const executionTime =
      /* istanbul ignore next */
      (cov_6h6sdvf2u().s[90]++, performance.now() - startTime);
      /* istanbul ignore next */
      cov_6h6sdvf2u().s[91]++;
      this.updatePerformanceMetrics('js', executionTime);
      /* istanbul ignore next */
      cov_6h6sdvf2u().s[92]++;
      return {
        value: result,
        executionTime,
        method: 'javascript'
      };
    } else
    /* istanbul ignore next */
    {
      cov_6h6sdvf2u().b[36][1]++;
    }
    cov_6h6sdvf2u().s[93]++;
    throw new Error('WASM calculation failed and JavaScript fallback is disabled');
  }
  calculatePressureDropJS(parameters) {
    /* istanbul ignore next */
    cov_6h6sdvf2u().f[16]++;
    const {
      airflow,
      ductLength,
      ductDiameter,
      fittings,
      elevation =
      /* istanbul ignore next */
      (cov_6h6sdvf2u().b[37][0]++, 0)
    } =
    /* istanbul ignore next */
    (cov_6h6sdvf2u().s[94]++, parameters);
    // Calculate velocity
    const area =
    /* istanbul ignore next */
    (cov_6h6sdvf2u().s[95]++, Math.PI * Math.pow(ductDiameter / 2, 2));
    const velocity =
    /* istanbul ignore next */
    (cov_6h6sdvf2u().s[96]++, airflow / area);
    // Friction pressure drop (simplified Darcy-Weisbach equation)
    const frictionFactor =
    /* istanbul ignore next */
    (cov_6h6sdvf2u().s[97]++, 0.02); // Simplified assumption
    const frictionDrop =
    /* istanbul ignore next */
    (cov_6h6sdvf2u().s[98]++, frictionFactor * (ductLength / ductDiameter) * (Math.pow(velocity, 2) / (2 * 32.174)));
    // Fittings pressure drop
    const fittingsDrop =
    /* istanbul ignore next */
    (cov_6h6sdvf2u().s[99]++, fittings.reduce((total, fitting) => {
      /* istanbul ignore next */
      cov_6h6sdvf2u().f[17]++;
      cov_6h6sdvf2u().s[100]++;
      return total + fitting.coefficient * (Math.pow(velocity, 2) / (2 * 32.174));
    }, 0));
    // Elevation pressure drop
    const elevationDrop =
    /* istanbul ignore next */
    (cov_6h6sdvf2u().s[101]++, elevation * 0.433); // psi per foot of elevation
    /* istanbul ignore next */
    cov_6h6sdvf2u().s[102]++;
    return frictionDrop + fittingsDrop + elevationDrop;
  }
  // =============================================================================
  // Heat Transfer Calculations
  // =============================================================================
  calculateHeatTransfer(parameters) {
    /* istanbul ignore next */
    cov_6h6sdvf2u().f[18]++;
    const startTime =
    /* istanbul ignore next */
    (cov_6h6sdvf2u().s[103]++, performance.now());
    /* istanbul ignore next */
    cov_6h6sdvf2u().s[104]++;
    if (this.isWASMAvailable()) {
      /* istanbul ignore next */
      cov_6h6sdvf2u().b[38][0]++;
      cov_6h6sdvf2u().s[105]++;
      try {
        const result =
        /* istanbul ignore next */
        (cov_6h6sdvf2u().s[106]++, this.wasmModule.calculate_heat_transfer(parameters.temperature1, parameters.temperature2, parameters.area, parameters.material, parameters.thickness,
        /* istanbul ignore next */
        (cov_6h6sdvf2u().b[39][0]++, parameters.convectionCoefficient) ||
        /* istanbul ignore next */
        (cov_6h6sdvf2u().b[39][1]++, 10)));
        const executionTime =
        /* istanbul ignore next */
        (cov_6h6sdvf2u().s[107]++, performance.now() - startTime);
        /* istanbul ignore next */
        cov_6h6sdvf2u().s[108]++;
        this.updatePerformanceMetrics('wasm', executionTime);
        /* istanbul ignore next */
        cov_6h6sdvf2u().s[109]++;
        return {
          value: result,
          executionTime,
          method: 'wasm',
          metadata: {
            material: parameters.material,
            convectionCoefficient:
            /* istanbul ignore next */
            (cov_6h6sdvf2u().b[40][0]++, parameters.convectionCoefficient) ||
            /* istanbul ignore next */
            (cov_6h6sdvf2u().b[40][1]++, 10)
          }
        };
      } catch (error) {
        /* istanbul ignore next */
        cov_6h6sdvf2u().s[110]++;
        if (this.config.performanceLogging) {
          /* istanbul ignore next */
          cov_6h6sdvf2u().b[41][0]++;
          cov_6h6sdvf2u().s[111]++;
          console.warn('WASM heat transfer calculation failed:', error);
        } else
        /* istanbul ignore next */
        {
          cov_6h6sdvf2u().b[41][1]++;
        }
      }
    } else
    /* istanbul ignore next */
    {
      cov_6h6sdvf2u().b[38][1]++;
    }
    cov_6h6sdvf2u().s[112]++;
    if (this.config.fallbackToJS) {
      /* istanbul ignore next */
      cov_6h6sdvf2u().b[42][0]++;
      const result =
      /* istanbul ignore next */
      (cov_6h6sdvf2u().s[113]++, this.calculateHeatTransferJS(parameters));
      const executionTime =
      /* istanbul ignore next */
      (cov_6h6sdvf2u().s[114]++, performance.now() - startTime);
      /* istanbul ignore next */
      cov_6h6sdvf2u().s[115]++;
      this.updatePerformanceMetrics('js', executionTime);
      /* istanbul ignore next */
      cov_6h6sdvf2u().s[116]++;
      return {
        value: result,
        executionTime,
        method: 'javascript'
      };
    } else
    /* istanbul ignore next */
    {
      cov_6h6sdvf2u().b[42][1]++;
    }
    cov_6h6sdvf2u().s[117]++;
    throw new Error('WASM calculation failed and JavaScript fallback is disabled');
  }
  calculateHeatTransferJS(parameters) {
    /* istanbul ignore next */
    cov_6h6sdvf2u().f[19]++;
    const {
      temperature1,
      temperature2,
      area,
      material,
      thickness,
      convectionCoefficient =
      /* istanbul ignore next */
      (cov_6h6sdvf2u().b[43][0]++, 10)
    } =
    /* istanbul ignore next */
    (cov_6h6sdvf2u().s[118]++, parameters);
    // Material thermal conductivity (simplified lookup)
    const thermalConductivity =
    /* istanbul ignore next */
    (cov_6h6sdvf2u().s[119]++, this.getThermalConductivity(material));
    // Calculate overall heat transfer coefficient
    const conductionResistance =
    /* istanbul ignore next */
    (cov_6h6sdvf2u().s[120]++, thickness / thermalConductivity);
    const convectionResistance =
    /* istanbul ignore next */
    (cov_6h6sdvf2u().s[121]++, 1 / convectionCoefficient);
    const overallCoefficient =
    /* istanbul ignore next */
    (cov_6h6sdvf2u().s[122]++, 1 / (conductionResistance + convectionResistance));
    // Calculate heat transfer rate
    const temperatureDifference =
    /* istanbul ignore next */
    (cov_6h6sdvf2u().s[123]++, Math.abs(temperature1 - temperature2));
    /* istanbul ignore next */
    cov_6h6sdvf2u().s[124]++;
    return overallCoefficient * area * temperatureDifference;
  }
  getThermalConductivity(material) {
    /* istanbul ignore next */
    cov_6h6sdvf2u().f[20]++;
    const conductivities =
    /* istanbul ignore next */
    (cov_6h6sdvf2u().s[125]++, {
      'steel': 45,
      'aluminum': 205,
      'copper': 385,
      'fiberglass': 0.04,
      'concrete': 1.7,
      'wood': 0.12
    });
    /* istanbul ignore next */
    cov_6h6sdvf2u().s[126]++;
    return /* istanbul ignore next */(cov_6h6sdvf2u().b[44][0]++, conductivities[material.toLowerCase()]) ||
    /* istanbul ignore next */
    (cov_6h6sdvf2u().b[44][1]++, 1.0);
  }
  // =============================================================================
  // System Optimization
  // =============================================================================
  optimizeSystem(parameters) {
    /* istanbul ignore next */
    cov_6h6sdvf2u().f[21]++;
    const startTime =
    /* istanbul ignore next */
    (cov_6h6sdvf2u().s[127]++, performance.now());
    /* istanbul ignore next */
    cov_6h6sdvf2u().s[128]++;
    if (this.isWASMAvailable()) {
      /* istanbul ignore next */
      cov_6h6sdvf2u().b[45][0]++;
      cov_6h6sdvf2u().s[129]++;
      try {
        const zonesData =
        /* istanbul ignore next */
        (cov_6h6sdvf2u().s[130]++, JSON.stringify(parameters.zones));
        const constraintsData =
        /* istanbul ignore next */
        (cov_6h6sdvf2u().s[131]++, JSON.stringify(parameters.constraints));
        const preferencesData =
        /* istanbul ignore next */
        (cov_6h6sdvf2u().s[132]++, JSON.stringify(parameters.preferences));
        const result =
        /* istanbul ignore next */
        (cov_6h6sdvf2u().s[133]++, this.wasmModule.optimize_hvac_system(zonesData, constraintsData, preferencesData));
        const executionTime =
        /* istanbul ignore next */
        (cov_6h6sdvf2u().s[134]++, performance.now() - startTime);
        /* istanbul ignore next */
        cov_6h6sdvf2u().s[135]++;
        this.updatePerformanceMetrics('wasm', executionTime);
        /* istanbul ignore next */
        cov_6h6sdvf2u().s[136]++;
        return {
          value: result,
          executionTime,
          method: 'wasm',
          metadata: {
            zonesCount: parameters.zones.length,
            optimizationType: 'multi-objective'
          }
        };
      } catch (error) {
        /* istanbul ignore next */
        cov_6h6sdvf2u().s[137]++;
        if (this.config.performanceLogging) {
          /* istanbul ignore next */
          cov_6h6sdvf2u().b[46][0]++;
          cov_6h6sdvf2u().s[138]++;
          console.warn('WASM system optimization failed:', error);
        } else
        /* istanbul ignore next */
        {
          cov_6h6sdvf2u().b[46][1]++;
        }
      }
    } else
    /* istanbul ignore next */
    {
      cov_6h6sdvf2u().b[45][1]++;
    }
    cov_6h6sdvf2u().s[139]++;
    if (this.config.fallbackToJS) {
      /* istanbul ignore next */
      cov_6h6sdvf2u().b[47][0]++;
      const result =
      /* istanbul ignore next */
      (cov_6h6sdvf2u().s[140]++, this.optimizeSystemJS(parameters));
      const executionTime =
      /* istanbul ignore next */
      (cov_6h6sdvf2u().s[141]++, performance.now() - startTime);
      /* istanbul ignore next */
      cov_6h6sdvf2u().s[142]++;
      this.updatePerformanceMetrics('js', executionTime);
      /* istanbul ignore next */
      cov_6h6sdvf2u().s[143]++;
      return {
        value: result,
        executionTime,
        method: 'javascript'
      };
    } else
    /* istanbul ignore next */
    {
      cov_6h6sdvf2u().b[47][1]++;
    }
    cov_6h6sdvf2u().s[144]++;
    throw new Error('WASM calculation failed and JavaScript fallback is disabled');
  }
  optimizeSystemJS(parameters) {
    /* istanbul ignore next */
    cov_6h6sdvf2u().f[22]++;
    // Simplified optimization algorithm
    const {
      zones,
      constraints,
      preferences
    } =
    /* istanbul ignore next */
    (cov_6h6sdvf2u().s[145]++, parameters);
    let totalScore =
    /* istanbul ignore next */
    (cov_6h6sdvf2u().s[146]++, 0);
    let totalWeight =
    /* istanbul ignore next */
    (cov_6h6sdvf2u().s[147]++, preferences.costWeight + preferences.efficiencyWeight + preferences.noiseWeight);
    /* istanbul ignore next */
    cov_6h6sdvf2u().s[148]++;
    zones.forEach(zone => {
      /* istanbul ignore next */
      cov_6h6sdvf2u().f[23]++;
      // Calculate zone efficiency score
      const efficiencyScore =
      /* istanbul ignore next */
      (cov_6h6sdvf2u().s[149]++, Math.min(zone.airflow / zone.area, constraints.maxVelocity) / constraints.maxVelocity);
      // Calculate cost score (simplified)
      const costScore =
      /* istanbul ignore next */
      (cov_6h6sdvf2u().s[150]++, 1 - zone.airflow * 0.001); // Simplified cost model
      // Calculate noise score (simplified)
      const noiseScore =
      /* istanbul ignore next */
      (cov_6h6sdvf2u().s[151]++, 1 - zone.airflow * 0.0005); // Simplified noise model
      // Weighted total
      const zoneScore =
      /* istanbul ignore next */
      (cov_6h6sdvf2u().s[152]++, (efficiencyScore * preferences.efficiencyWeight + costScore * preferences.costWeight + noiseScore * preferences.noiseWeight) / totalWeight);
      /* istanbul ignore next */
      cov_6h6sdvf2u().s[153]++;
      totalScore += zoneScore;
    });
    /* istanbul ignore next */
    cov_6h6sdvf2u().s[154]++;
    return totalScore / zones.length;
  }
  // =============================================================================
  // Performance Monitoring
  // =============================================================================
  updatePerformanceMetrics(method, executionTime) {
    /* istanbul ignore next */
    cov_6h6sdvf2u().f[24]++;
    cov_6h6sdvf2u().s[155]++;
    if (method === 'wasm') {
      /* istanbul ignore next */
      cov_6h6sdvf2u().b[48][0]++;
      cov_6h6sdvf2u().s[156]++;
      this.performanceMetrics.wasmCalls++;
      /* istanbul ignore next */
      cov_6h6sdvf2u().s[157]++;
      this.performanceMetrics.totalWasmTime += executionTime;
      /* istanbul ignore next */
      cov_6h6sdvf2u().s[158]++;
      this.performanceMetrics.averageWasmTime = this.performanceMetrics.totalWasmTime / this.performanceMetrics.wasmCalls;
    } else {
      /* istanbul ignore next */
      cov_6h6sdvf2u().b[48][1]++;
      cov_6h6sdvf2u().s[159]++;
      this.performanceMetrics.jsCalls++;
      /* istanbul ignore next */
      cov_6h6sdvf2u().s[160]++;
      this.performanceMetrics.totalJsTime += executionTime;
      /* istanbul ignore next */
      cov_6h6sdvf2u().s[161]++;
      this.performanceMetrics.averageJsTime = this.performanceMetrics.totalJsTime / this.performanceMetrics.jsCalls;
    }
  }
  getPerformanceMetrics() {
    /* istanbul ignore next */
    cov_6h6sdvf2u().f[25]++;
    cov_6h6sdvf2u().s[162]++;
    return {
      ...this.performanceMetrics,
      wasmAvailable: this.isWASMAvailable(),
      performanceRatio: this.performanceMetrics.averageJsTime / (
      /* istanbul ignore next */
      (cov_6h6sdvf2u().b[49][0]++, this.performanceMetrics.averageWasmTime) ||
      /* istanbul ignore next */
      (cov_6h6sdvf2u().b[49][1]++, 1))
    };
  }
  // =============================================================================
  // Public API
  // =============================================================================
  getConfig() {
    /* istanbul ignore next */
    cov_6h6sdvf2u().f[26]++;
    cov_6h6sdvf2u().s[163]++;
    return {
      ...this.config
    };
  }
  updateConfig(newConfig) {
    /* istanbul ignore next */
    cov_6h6sdvf2u().f[27]++;
    cov_6h6sdvf2u().s[164]++;
    this.config = {
      ...this.config,
      ...newConfig
    };
  }
  resetMetrics() {
    /* istanbul ignore next */
    cov_6h6sdvf2u().f[28]++;
    cov_6h6sdvf2u().s[165]++;
    this.performanceMetrics = {
      wasmCalls: 0,
      jsCalls: 0,
      totalWasmTime: 0,
      totalJsTime: 0,
      averageWasmTime: 0,
      averageJsTime: 0
    };
  }
}
/* istanbul ignore next */
cov_6h6sdvf2u().s[166]++;
exports.WASMCalculationService = WASMCalculationService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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