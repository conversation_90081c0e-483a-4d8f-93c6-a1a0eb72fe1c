96301bc4b564599e2be8f52e482af149
"use strict";

/**
 * Genetic Algorithm Implementation for System Optimization
 *
 * Implements genetic algorithm optimization with:
 * - Multi-objective optimization support (NSGA-II)
 * - Configurable selection, crossover, and mutation operators
 * - Constraint handling with penalty methods
 * - Elitism and diversity preservation
 * - Parallel evaluation support
 *
 * @version 3.0.0
 * <AUTHOR> Suite Development Team
 */
/* istanbul ignore next */
function cov_by2crk18o() {
  var path = "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\algorithms\\GeneticAlgorithm.ts";
  var hash = "52ea60186096b27b8461d8128e5bd1fd2b2018b8";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\algorithms\\GeneticAlgorithm.ts",
    statementMap: {
      "0": {
        start: {
          line: 15,
          column: 0
        },
        end: {
          line: 15,
          column: 62
        }
      },
      "1": {
        start: {
          line: 16,
          column: 0
        },
        end: {
          line: 16,
          column: 34
        }
      },
      "2": {
        start: {
          line: 17,
          column: 34
        },
        end: {
          line: 17,
          column: 77
        }
      },
      "3": {
        start: {
          line: 23,
          column: 8
        },
        end: {
          line: 23,
          column: 35
        }
      },
      "4": {
        start: {
          line: 24,
          column: 8
        },
        end: {
          line: 24,
          column: 30
        }
      },
      "5": {
        start: {
          line: 25,
          column: 8
        },
        end: {
          line: 25,
          column: 28
        }
      },
      "6": {
        start: {
          line: 26,
          column: 8
        },
        end: {
          line: 26,
          column: 33
        }
      },
      "7": {
        start: {
          line: 27,
          column: 8
        },
        end: {
          line: 27,
          column: 26
        }
      },
      "8": {
        start: {
          line: 28,
          column: 8
        },
        end: {
          line: 44,
          column: 10
        }
      },
      "9": {
        start: {
          line: 46,
          column: 8
        },
        end: {
          line: 51,
          column: 9
        }
      },
      "10": {
        start: {
          line: 47,
          column: 12
        },
        end: {
          line: 47,
          column: 77
        }
      },
      "11": {
        start: {
          line: 50,
          column: 12
        },
        end: {
          line: 50,
          column: 38
        }
      },
      "12": {
        start: {
          line: 52,
          column: 8
        },
        end: {
          line: 52,
          column: 29
        }
      },
      "13": {
        start: {
          line: 58,
          column: 26
        },
        end: {
          line: 58,
          column: 43
        }
      },
      "14": {
        start: {
          line: 59,
          column: 8
        },
        end: {
          line: 78,
          column: 9
        }
      },
      "15": {
        start: {
          line: 61,
          column: 12
        },
        end: {
          line: 61,
          column: 46
        }
      },
      "16": {
        start: {
          line: 63,
          column: 12
        },
        end: {
          line: 63,
          column: 96
        }
      },
      "17": {
        start: {
          line: 65,
          column: 12
        },
        end: {
          line: 71,
          column: 13
        }
      },
      "18": {
        start: {
          line: 66,
          column: 16
        },
        end: {
          line: 66,
          column: 93
        }
      },
      "19": {
        start: {
          line: 67,
          column: 16
        },
        end: {
          line: 67,
          column: 37
        }
      },
      "20": {
        start: {
          line: 68,
          column: 16
        },
        end: {
          line: 70,
          column: 17
        }
      },
      "21": {
        start: {
          line: 69,
          column: 20
        },
        end: {
          line: 69,
          column: 43
        }
      },
      "22": {
        start: {
          line: 73,
          column: 12
        },
        end: {
          line: 73,
          column: 69
        }
      },
      "23": {
        start: {
          line: 76,
          column: 12
        },
        end: {
          line: 76,
          column: 75
        }
      },
      "24": {
        start: {
          line: 77,
          column: 12
        },
        end: {
          line: 77,
          column: 24
        }
      },
      "25": {
        start: {
          line: 84,
          column: 8
        },
        end: {
          line: 84,
          column: 28
        }
      },
      "26": {
        start: {
          line: 85,
          column: 8
        },
        end: {
          line: 85,
          column: 33
        }
      },
      "27": {
        start: {
          line: 86,
          column: 8
        },
        end: {
          line: 86,
          column: 29
        }
      },
      "28": {
        start: {
          line: 87,
          column: 8
        },
        end: {
          line: 87,
          column: 35
        }
      },
      "29": {
        start: {
          line: 88,
          column: 8
        },
        end: {
          line: 88,
          column: 30
        }
      },
      "30": {
        start: {
          line: 89,
          column: 8
        },
        end: {
          line: 89,
          column: 26
        }
      },
      "31": {
        start: {
          line: 90,
          column: 8
        },
        end: {
          line: 90,
          column: 110
        }
      },
      "32": {
        start: {
          line: 96,
          column: 8
        },
        end: {
          line: 96,
          column: 29
        }
      },
      "33": {
        start: {
          line: 97,
          column: 8
        },
        end: {
          line: 101,
          column: 9
        }
      },
      "34": {
        start: {
          line: 97,
          column: 21
        },
        end: {
          line: 97,
          column: 22
        }
      },
      "35": {
        start: {
          line: 98,
          column: 31
        },
        end: {
          line: 98,
          column: 67
        }
      },
      "36": {
        start: {
          line: 99,
          column: 12
        },
        end: {
          line: 99,
          column: 103
        }
      },
      "37": {
        start: {
          line: 100,
          column: 12
        },
        end: {
          line: 100,
          column: 45
        }
      },
      "38": {
        start: {
          line: 102,
          column: 8
        },
        end: {
          line: 102,
          column: 36
        }
      },
      "39": {
        start: {
          line: 103,
          column: 8
        },
        end: {
          line: 103,
          column: 33
        }
      },
      "40": {
        start: {
          line: 109,
          column: 22
        },
        end: {
          line: 109,
          column: 24
        }
      },
      "41": {
        start: {
          line: 110,
          column: 8
        },
        end: {
          line: 123,
          column: 9
        }
      },
      "42": {
        start: {
          line: 111,
          column: 12
        },
        end: {
          line: 122,
          column: 13
        }
      },
      "43": {
        start: {
          line: 113,
          column: 36
        },
        end: {
          line: 113,
          column: 94
        }
      },
      "44": {
        start: {
          line: 114,
          column: 16
        },
        end: {
          line: 114,
          column: 65
        }
      },
      "45": {
        start: {
          line: 118,
          column: 28
        },
        end: {
          line: 118,
          column: 101
        }
      },
      "46": {
        start: {
          line: 119,
          column: 28
        },
        end: {
          line: 119,
          column: 101
        }
      },
      "47": {
        start: {
          line: 120,
          column: 30
        },
        end: {
          line: 120,
          column: 63
        }
      },
      "48": {
        start: {
          line: 121,
          column: 16
        },
        end: {
          line: 121,
          column: 34
        }
      },
      "49": {
        start: {
          line: 124,
          column: 8
        },
        end: {
          line: 132,
          column: 10
        }
      },
      "50": {
        start: {
          line: 138,
          column: 8
        },
        end: {
          line: 185,
          column: 9
        }
      },
      "51": {
        start: {
          line: 140,
          column: 30
        },
        end: {
          line: 140,
          column: 88
        }
      },
      "52": {
        start: {
          line: 142,
          column: 12
        },
        end: {
          line: 157,
          column: 13
        }
      },
      "53": {
        start: {
          line: 144,
          column: 39
        },
        end: {
          line: 144,
          column: 67
        }
      },
      "54": {
        start: {
          line: 145,
          column: 16
        },
        end: {
          line: 145,
          column: 62
        }
      },
      "55": {
        start: {
          line: 146,
          column: 16
        },
        end: {
          line: 146,
          column: 52
        }
      },
      "56": {
        start: {
          line: 150,
          column: 16
        },
        end: {
          line: 150,
          column: 48
        }
      },
      "57": {
        start: {
          line: 151,
          column: 16
        },
        end: {
          line: 154,
          column: 17
        }
      },
      "58": {
        start: {
          line: 152,
          column: 34
        },
        end: {
          line: 152,
          column: 73
        }
      },
      "59": {
        start: {
          line: 153,
          column: 20
        },
        end: {
          line: 153,
          column: 59
        }
      },
      "60": {
        start: {
          line: 156,
          column: 16
        },
        end: {
          line: 156,
          column: 39
        }
      },
      "61": {
        start: {
          line: 159,
          column: 12
        },
        end: {
          line: 159,
          column: 49
        }
      },
      "62": {
        start: {
          line: 160,
          column: 12
        },
        end: {
          line: 163,
          column: 13
        }
      },
      "63": {
        start: {
          line: 161,
          column: 34
        },
        end: {
          line: 161,
          column: 63
        }
      },
      "64": {
        start: {
          line: 162,
          column: 16
        },
        end: {
          line: 162,
          column: 64
        }
      },
      "65": {
        start: {
          line: 165,
          column: 12
        },
        end: {
          line: 165,
          column: 85
        }
      },
      "66": {
        start: {
          line: 165,
          column: 77
        },
        end: {
          line: 165,
          column: 83
        }
      },
      "67": {
        start: {
          line: 167,
          column: 12
        },
        end: {
          line: 178,
          column: 13
        }
      },
      "68": {
        start: {
          line: 168,
          column: 32
        },
        end: {
          line: 170,
          column: 88
        }
      },
      "69": {
        start: {
          line: 169,
          column: 33
        },
        end: {
          line: 169,
          column: 38
        }
      },
      "70": {
        start: {
          line: 170,
          column: 40
        },
        end: {
          line: 170,
          column: 47
        }
      },
      "71": {
        start: {
          line: 171,
          column: 16
        },
        end: {
          line: 177,
          column: 17
        }
      },
      "72": {
        start: {
          line: 172,
          column: 20
        },
        end: {
          line: 172,
          column: 50
        }
      },
      "73": {
        start: {
          line: 176,
          column: 20
        },
        end: {
          line: 176,
          column: 98
        }
      },
      "74": {
        start: {
          line: 176,
          column: 85
        },
        end: {
          line: 176,
          column: 96
        }
      },
      "75": {
        start: {
          line: 179,
          column: 12
        },
        end: {
          line: 179,
          column: 35
        }
      },
      "76": {
        start: {
          line: 182,
          column: 12
        },
        end: {
          line: 182,
          column: 65
        }
      },
      "77": {
        start: {
          line: 183,
          column: 12
        },
        end: {
          line: 183,
          column: 50
        }
      },
      "78": {
        start: {
          line: 184,
          column: 12
        },
        end: {
          line: 184,
          column: 40
        }
      },
      "79": {
        start: {
          line: 191,
          column: 8
        },
        end: {
          line: 194,
          column: 12
        }
      },
      "80": {
        start: {
          line: 191,
          column: 59
        },
        end: {
          line: 194,
          column: 9
        }
      },
      "81": {
        start: {
          line: 201,
          column: 24
        },
        end: {
          line: 201,
          column: 40
        }
      },
      "82": {
        start: {
          line: 203,
          column: 26
        },
        end: {
          line: 203,
          column: 28
        }
      },
      "83": {
        start: {
          line: 204,
          column: 8
        },
        end: {
          line: 221,
          column: 9
        }
      },
      "84": {
        start: {
          line: 204,
          column: 21
        },
        end: {
          line: 204,
          column: 22
        }
      },
      "85": {
        start: {
          line: 205,
          column: 28
        },
        end: {
          line: 205,
          column: 38
        }
      },
      "86": {
        start: {
          line: 206,
          column: 28
        },
        end: {
          line: 206,
          column: 56
        }
      },
      "87": {
        start: {
          line: 207,
          column: 25
        },
        end: {
          line: 207,
          column: 50
        }
      },
      "88": {
        start: {
          line: 208,
          column: 25
        },
        end: {
          line: 208,
          column: 50
        }
      },
      "89": {
        start: {
          line: 210,
          column: 12
        },
        end: {
          line: 212,
          column: 13
        }
      },
      "90": {
        start: {
          line: 211,
          column: 16
        },
        end: {
          line: 211,
          column: 77
        }
      },
      "91": {
        start: {
          line: 214,
          column: 12
        },
        end: {
          line: 216,
          column: 13
        }
      },
      "92": {
        start: {
          line: 215,
          column: 16
        },
        end: {
          line: 215,
          column: 54
        }
      },
      "93": {
        start: {
          line: 217,
          column: 12
        },
        end: {
          line: 219,
          column: 13
        }
      },
      "94": {
        start: {
          line: 218,
          column: 16
        },
        end: {
          line: 218,
          column: 54
        }
      },
      "95": {
        start: {
          line: 220,
          column: 12
        },
        end: {
          line: 220,
          column: 43
        }
      },
      "96": {
        start: {
          line: 223,
          column: 8
        },
        end: {
          line: 225,
          column: 9
        }
      },
      "97": {
        start: {
          line: 224,
          column: 12
        },
        end: {
          line: 224,
          column: 98
        }
      },
      "98": {
        start: {
          line: 227,
          column: 8
        },
        end: {
          line: 227,
          column: 36
        }
      },
      "99": {
        start: {
          line: 229,
          column: 8
        },
        end: {
          line: 229,
          column: 64
        }
      },
      "100": {
        start: {
          line: 229,
          column: 46
        },
        end: {
          line: 229,
          column: 62
        }
      },
      "101": {
        start: {
          line: 230,
          column: 8
        },
        end: {
          line: 230,
          column: 26
        }
      },
      "102": {
        start: {
          line: 231,
          column: 8
        },
        end: {
          line: 231,
          column: 36
        }
      },
      "103": {
        start: {
          line: 232,
          column: 8
        },
        end: {
          line: 232,
          column: 33
        }
      },
      "104": {
        start: {
          line: 238,
          column: 8
        },
        end: {
          line: 249,
          column: 9
        }
      },
      "105": {
        start: {
          line: 240,
          column: 16
        },
        end: {
          line: 240,
          column: 50
        }
      },
      "106": {
        start: {
          line: 242,
          column: 16
        },
        end: {
          line: 242,
          column: 53
        }
      },
      "107": {
        start: {
          line: 244,
          column: 16
        },
        end: {
          line: 244,
          column: 44
        }
      },
      "108": {
        start: {
          line: 246,
          column: 16
        },
        end: {
          line: 246,
          column: 46
        }
      },
      "109": {
        start: {
          line: 248,
          column: 16
        },
        end: {
          line: 248,
          column: 50
        }
      },
      "110": {
        start: {
          line: 255,
          column: 25
        },
        end: {
          line: 255,
          column: 27
        }
      },
      "111": {
        start: {
          line: 256,
          column: 31
        },
        end: {
          line: 256,
          column: 66
        }
      },
      "112": {
        start: {
          line: 257,
          column: 8
        },
        end: {
          line: 266,
          column: 9
        }
      },
      "113": {
        start: {
          line: 257,
          column: 21
        },
        end: {
          line: 257,
          column: 22
        }
      },
      "114": {
        start: {
          line: 258,
          column: 31
        },
        end: {
          line: 258,
          column: 33
        }
      },
      "115": {
        start: {
          line: 259,
          column: 12
        },
        end: {
          line: 262,
          column: 13
        }
      },
      "116": {
        start: {
          line: 259,
          column: 25
        },
        end: {
          line: 259,
          column: 26
        }
      },
      "117": {
        start: {
          line: 260,
          column: 36
        },
        end: {
          line: 260,
          column: 86
        }
      },
      "118": {
        start: {
          line: 261,
          column: 16
        },
        end: {
          line: 261,
          column: 62
        }
      },
      "119": {
        start: {
          line: 264,
          column: 12
        },
        end: {
          line: 264,
          column: 61
        }
      },
      "120": {
        start: {
          line: 264,
          column: 38
        },
        end: {
          line: 264,
          column: 59
        }
      },
      "121": {
        start: {
          line: 265,
          column: 12
        },
        end: {
          line: 265,
          column: 41
        }
      },
      "122": {
        start: {
          line: 267,
          column: 8
        },
        end: {
          line: 267,
          column: 24
        }
      },
      "123": {
        start: {
          line: 273,
          column: 25
        },
        end: {
          line: 273,
          column: 27
        }
      },
      "124": {
        start: {
          line: 275,
          column: 27
        },
        end: {
          line: 275,
          column: 79
        }
      },
      "125": {
        start: {
          line: 275,
          column: 66
        },
        end: {
          line: 275,
          column: 77
        }
      },
      "126": {
        start: {
          line: 276,
          column: 32
        },
        end: {
          line: 276,
          column: 88
        }
      },
      "127": {
        start: {
          line: 276,
          column: 59
        },
        end: {
          line: 276,
          column: 87
        }
      },
      "128": {
        start: {
          line: 277,
          column: 29
        },
        end: {
          line: 277,
          column: 87
        }
      },
      "129": {
        start: {
          line: 277,
          column: 70
        },
        end: {
          line: 277,
          column: 83
        }
      },
      "130": {
        start: {
          line: 278,
          column: 8
        },
        end: {
          line: 288,
          column: 9
        }
      },
      "131": {
        start: {
          line: 278,
          column: 21
        },
        end: {
          line: 278,
          column: 22
        }
      },
      "132": {
        start: {
          line: 279,
          column: 32
        },
        end: {
          line: 279,
          column: 60
        }
      },
      "133": {
        start: {
          line: 280,
          column: 36
        },
        end: {
          line: 280,
          column: 37
        }
      },
      "134": {
        start: {
          line: 281,
          column: 12
        },
        end: {
          line: 287,
          column: 13
        }
      },
      "135": {
        start: {
          line: 281,
          column: 25
        },
        end: {
          line: 281,
          column: 26
        }
      },
      "136": {
        start: {
          line: 282,
          column: 16
        },
        end: {
          line: 282,
          column: 56
        }
      },
      "137": {
        start: {
          line: 283,
          column: 16
        },
        end: {
          line: 286,
          column: 17
        }
      },
      "138": {
        start: {
          line: 284,
          column: 20
        },
        end: {
          line: 284,
          column: 54
        }
      },
      "139": {
        start: {
          line: 285,
          column: 20
        },
        end: {
          line: 285,
          column: 26
        }
      },
      "140": {
        start: {
          line: 289,
          column: 8
        },
        end: {
          line: 289,
          column: 24
        }
      },
      "141": {
        start: {
          line: 295,
          column: 25
        },
        end: {
          line: 295,
          column: 27
        }
      },
      "142": {
        start: {
          line: 297,
          column: 33
        },
        end: {
          line: 297,
          column: 91
        }
      },
      "143": {
        start: {
          line: 297,
          column: 69
        },
        end: {
          line: 297,
          column: 90
        }
      },
      "144": {
        start: {
          line: 299,
          column: 22
        },
        end: {
          line: 299,
          column: 67
        }
      },
      "145": {
        start: {
          line: 299,
          column: 57
        },
        end: {
          line: 299,
          column: 66
        }
      },
      "146": {
        start: {
          line: 300,
          column: 26
        },
        end: {
          line: 300,
          column: 68
        }
      },
      "147": {
        start: {
          line: 300,
          column: 54
        },
        end: {
          line: 300,
          column: 64
        }
      },
      "148": {
        start: {
          line: 301,
          column: 8
        },
        end: {
          line: 311,
          column: 9
        }
      },
      "149": {
        start: {
          line: 301,
          column: 21
        },
        end: {
          line: 301,
          column: 22
        }
      },
      "150": {
        start: {
          line: 302,
          column: 32
        },
        end: {
          line: 302,
          column: 57
        }
      },
      "151": {
        start: {
          line: 303,
          column: 33
        },
        end: {
          line: 303,
          column: 34
        }
      },
      "152": {
        start: {
          line: 304,
          column: 12
        },
        end: {
          line: 310,
          column: 13
        }
      },
      "153": {
        start: {
          line: 304,
          column: 25
        },
        end: {
          line: 304,
          column: 26
        }
      },
      "154": {
        start: {
          line: 305,
          column: 16
        },
        end: {
          line: 305,
          column: 43
        }
      },
      "155": {
        start: {
          line: 306,
          column: 16
        },
        end: {
          line: 309,
          column: 17
        }
      },
      "156": {
        start: {
          line: 307,
          column: 20
        },
        end: {
          line: 307,
          column: 55
        }
      },
      "157": {
        start: {
          line: 308,
          column: 20
        },
        end: {
          line: 308,
          column: 26
        }
      },
      "158": {
        start: {
          line: 312,
          column: 8
        },
        end: {
          line: 312,
          column: 24
        }
      },
      "159": {
        start: {
          line: 318,
          column: 25
        },
        end: {
          line: 318,
          column: 27
        }
      },
      "160": {
        start: {
          line: 319,
          column: 8
        },
        end: {
          line: 322,
          column: 9
        }
      },
      "161": {
        start: {
          line: 319,
          column: 21
        },
        end: {
          line: 319,
          column: 22
        }
      },
      "162": {
        start: {
          line: 320,
          column: 32
        },
        end: {
          line: 320,
          column: 82
        }
      },
      "163": {
        start: {
          line: 321,
          column: 12
        },
        end: {
          line: 321,
          column: 56
        }
      },
      "164": {
        start: {
          line: 323,
          column: 8
        },
        end: {
          line: 323,
          column: 24
        }
      },
      "165": {
        start: {
          line: 329,
          column: 8
        },
        end: {
          line: 340,
          column: 9
        }
      },
      "166": {
        start: {
          line: 331,
          column: 16
        },
        end: {
          line: 331,
          column: 67
        }
      },
      "167": {
        start: {
          line: 333,
          column: 16
        },
        end: {
          line: 333,
          column: 64
        }
      },
      "168": {
        start: {
          line: 335,
          column: 16
        },
        end: {
          line: 335,
          column: 63
        }
      },
      "169": {
        start: {
          line: 337,
          column: 16
        },
        end: {
          line: 337,
          column: 66
        }
      },
      "170": {
        start: {
          line: 339,
          column: 16
        },
        end: {
          line: 339,
          column: 64
        }
      },
      "171": {
        start: {
          line: 346,
          column: 31
        },
        end: {
          line: 346,
          column: 79
        }
      },
      "172": {
        start: {
          line: 347,
          column: 23
        },
        end: {
          line: 347,
          column: 48
        }
      },
      "173": {
        start: {
          line: 348,
          column: 23
        },
        end: {
          line: 348,
          column: 48
        }
      },
      "174": {
        start: {
          line: 349,
          column: 8
        },
        end: {
          line: 352,
          column: 10
        }
      },
      "175": {
        start: {
          line: 353,
          column: 8
        },
        end: {
          line: 356,
          column: 10
        }
      },
      "176": {
        start: {
          line: 357,
          column: 8
        },
        end: {
          line: 357,
          column: 32
        }
      },
      "177": {
        start: {
          line: 363,
          column: 23
        },
        end: {
          line: 363,
          column: 71
        }
      },
      "178": {
        start: {
          line: 364,
          column: 23
        },
        end: {
          line: 364,
          column: 71
        }
      },
      "179": {
        start: {
          line: 365,
          column: 29
        },
        end: {
          line: 365,
          column: 81
        }
      },
      "180": {
        start: {
          line: 366,
          column: 23
        },
        end: {
          line: 366,
          column: 48
        }
      },
      "181": {
        start: {
          line: 367,
          column: 23
        },
        end: {
          line: 367,
          column: 48
        }
      },
      "182": {
        start: {
          line: 368,
          column: 8
        },
        end: {
          line: 372,
          column: 10
        }
      },
      "183": {
        start: {
          line: 373,
          column: 8
        },
        end: {
          line: 377,
          column: 10
        }
      },
      "184": {
        start: {
          line: 378,
          column: 8
        },
        end: {
          line: 378,
          column: 32
        }
      },
      "185": {
        start: {
          line: 384,
          column: 23
        },
        end: {
          line: 384,
          column: 48
        }
      },
      "186": {
        start: {
          line: 385,
          column: 23
        },
        end: {
          line: 385,
          column: 48
        }
      },
      "187": {
        start: {
          line: 386,
          column: 8
        },
        end: {
          line: 386,
          column: 109
        }
      },
      "188": {
        start: {
          line: 386,
          column: 58
        },
        end: {
          line: 386,
          column: 107
        }
      },
      "189": {
        start: {
          line: 387,
          column: 8
        },
        end: {
          line: 387,
          column: 109
        }
      },
      "190": {
        start: {
          line: 387,
          column: 58
        },
        end: {
          line: 387,
          column: 107
        }
      },
      "191": {
        start: {
          line: 388,
          column: 8
        },
        end: {
          line: 388,
          column: 32
        }
      },
      "192": {
        start: {
          line: 394,
          column: 22
        },
        end: {
          line: 394,
          column: 35
        }
      },
      "193": {
        start: {
          line: 395,
          column: 23
        },
        end: {
          line: 395,
          column: 48
        }
      },
      "194": {
        start: {
          line: 396,
          column: 23
        },
        end: {
          line: 396,
          column: 48
        }
      },
      "195": {
        start: {
          line: 397,
          column: 8
        },
        end: {
          line: 402,
          column: 11
        }
      },
      "196": {
        start: {
          line: 398,
          column: 12
        },
        end: {
          line: 400,
          column: 13
        }
      },
      "197": {
        start: {
          line: 399,
          column: 16
        },
        end: {
          line: 399,
          column: 73
        }
      },
      "198": {
        start: {
          line: 401,
          column: 12
        },
        end: {
          line: 401,
          column: 24
        }
      },
      "199": {
        start: {
          line: 403,
          column: 8
        },
        end: {
          line: 408,
          column: 11
        }
      },
      "200": {
        start: {
          line: 404,
          column: 12
        },
        end: {
          line: 406,
          column: 13
        }
      },
      "201": {
        start: {
          line: 405,
          column: 16
        },
        end: {
          line: 405,
          column: 73
        }
      },
      "202": {
        start: {
          line: 407,
          column: 12
        },
        end: {
          line: 407,
          column: 24
        }
      },
      "203": {
        start: {
          line: 409,
          column: 8
        },
        end: {
          line: 409,
          column: 32
        }
      },
      "204": {
        start: {
          line: 415,
          column: 24
        },
        end: {
          line: 415,
          column: 52
        }
      },
      "205": {
        start: {
          line: 416,
          column: 8
        },
        end: {
          line: 420,
          column: 9
        }
      },
      "206": {
        start: {
          line: 416,
          column: 21
        },
        end: {
          line: 416,
          column: 22
        }
      },
      "207": {
        start: {
          line: 417,
          column: 12
        },
        end: {
          line: 419,
          column: 13
        }
      },
      "208": {
        start: {
          line: 418,
          column: 16
        },
        end: {
          line: 418,
          column: 91
        }
      },
      "209": {
        start: {
          line: 421,
          column: 8
        },
        end: {
          line: 421,
          column: 23
        }
      },
      "210": {
        start: {
          line: 427,
          column: 8
        },
        end: {
          line: 446,
          column: 9
        }
      },
      "211": {
        start: {
          line: 429,
          column: 32
        },
        end: {
          line: 429,
          column: 90
        }
      },
      "212": {
        start: {
          line: 430,
          column: 12
        },
        end: {
          line: 430,
          column: 56
        }
      },
      "213": {
        start: {
          line: 432,
          column: 13
        },
        end: {
          line: 446,
          column: 9
        }
      },
      "214": {
        start: {
          line: 434,
          column: 24
        },
        end: {
          line: 434,
          column: 97
        }
      },
      "215": {
        start: {
          line: 435,
          column: 24
        },
        end: {
          line: 435,
          column: 97
        }
      },
      "216": {
        start: {
          line: 436,
          column: 12
        },
        end: {
          line: 445,
          column: 13
        }
      },
      "217": {
        start: {
          line: 438,
          column: 20
        },
        end: {
          line: 438,
          column: 65
        }
      },
      "218": {
        start: {
          line: 440,
          column: 20
        },
        end: {
          line: 440,
          column: 58
        }
      },
      "219": {
        start: {
          line: 442,
          column: 20
        },
        end: {
          line: 442,
          column: 67
        }
      },
      "220": {
        start: {
          line: 444,
          column: 20
        },
        end: {
          line: 444,
          column: 65
        }
      },
      "221": {
        start: {
          line: 447,
          column: 8
        },
        end: {
          line: 447,
          column: 20
        }
      },
      "222": {
        start: {
          line: 453,
          column: 22
        },
        end: {
          line: 453,
          column: 39
        }
      },
      "223": {
        start: {
          line: 454,
          column: 24
        },
        end: {
          line: 454,
          column: 61
        }
      },
      "224": {
        start: {
          line: 455,
          column: 8
        },
        end: {
          line: 455,
          column: 53
        }
      },
      "225": {
        start: {
          line: 461,
          column: 8
        },
        end: {
          line: 461,
          column: 49
        }
      },
      "226": {
        start: {
          line: 467,
          column: 20
        },
        end: {
          line: 467,
          column: 22
        }
      },
      "227": {
        start: {
          line: 468,
          column: 23
        },
        end: {
          line: 468,
          column: 50
        }
      },
      "228": {
        start: {
          line: 469,
          column: 23
        },
        end: {
          line: 469,
          column: 50
        }
      },
      "229": {
        start: {
          line: 470,
          column: 20
        },
        end: {
          line: 470,
          column: 33
        }
      },
      "230": {
        start: {
          line: 472,
          column: 8
        },
        end: {
          line: 481,
          column: 9
        }
      },
      "231": {
        start: {
          line: 473,
          column: 23
        },
        end: {
          line: 473,
          column: 35
        }
      },
      "232": {
        start: {
          line: 474,
          column: 24
        },
        end: {
          line: 474,
          column: 79
        }
      },
      "233": {
        start: {
          line: 475,
          column: 12
        },
        end: {
          line: 475,
          column: 60
        }
      },
      "234": {
        start: {
          line: 478,
          column: 23
        },
        end: {
          line: 478,
          column: 35
        }
      },
      "235": {
        start: {
          line: 479,
          column: 24
        },
        end: {
          line: 479,
          column: 87
        }
      },
      "236": {
        start: {
          line: 480,
          column: 12
        },
        end: {
          line: 480,
          column: 60
        }
      },
      "237": {
        start: {
          line: 482,
          column: 24
        },
        end: {
          line: 482,
          column: 52
        }
      },
      "238": {
        start: {
          line: 483,
          column: 8
        },
        end: {
          line: 483,
          column: 53
        }
      },
      "239": {
        start: {
          line: 490,
          column: 25
        },
        end: {
          line: 490,
          column: 59
        }
      },
      "240": {
        start: {
          line: 492,
          column: 8
        },
        end: {
          line: 492,
          column: 55
        }
      },
      "241": {
        start: {
          line: 492,
          column: 32
        },
        end: {
          line: 492,
          column: 53
        }
      },
      "242": {
        start: {
          line: 494,
          column: 8
        },
        end: {
          line: 494,
          column: 76
        }
      },
      "243": {
        start: {
          line: 500,
          column: 21
        },
        end: {
          line: 500,
          column: 111
        }
      },
      "244": {
        start: {
          line: 500,
          column: 63
        },
        end: {
          line: 500,
          column: 110
        }
      },
      "245": {
        start: {
          line: 501,
          column: 8
        },
        end: {
          line: 503,
          column: 9
        }
      },
      "246": {
        start: {
          line: 502,
          column: 12
        },
        end: {
          line: 502,
          column: 46
        }
      },
      "247": {
        start: {
          line: 511,
          column: 8
        },
        end: {
          line: 511,
          column: 71
        }
      },
      "248": {
        start: {
          line: 511,
          column: 57
        },
        end: {
          line: 511,
          column: 69
        }
      },
      "249": {
        start: {
          line: 517,
          column: 35
        },
        end: {
          line: 517,
          column: 78
        }
      },
      "250": {
        start: {
          line: 517,
          column: 65
        },
        end: {
          line: 517,
          column: 77
        }
      },
      "251": {
        start: {
          line: 518,
          column: 26
        },
        end: {
          line: 518,
          column: 68
        }
      },
      "252": {
        start: {
          line: 518,
          column: 56
        },
        end: {
          line: 518,
          column: 67
        }
      },
      "253": {
        start: {
          line: 519,
          column: 8
        },
        end: {
          line: 521,
          column: 9
        }
      },
      "254": {
        start: {
          line: 520,
          column: 12
        },
        end: {
          line: 520,
          column: 45
        }
      },
      "255": {
        start: {
          line: 522,
          column: 24
        },
        end: {
          line: 530,
          column: 9
        }
      },
      "256": {
        start: {
          line: 525,
          column: 57
        },
        end: {
          line: 525,
          column: 64
        }
      },
      "257": {
        start: {
          line: 528,
          column: 64
        },
        end: {
          line: 528,
          column: 77
        }
      },
      "258": {
        start: {
          line: 531,
          column: 8
        },
        end: {
          line: 531,
          column: 35
        }
      },
      "259": {
        start: {
          line: 537,
          column: 8
        },
        end: {
          line: 538,
          column: 21
        }
      },
      "260": {
        start: {
          line: 538,
          column: 12
        },
        end: {
          line: 538,
          column: 21
        }
      },
      "261": {
        start: {
          line: 539,
          column: 28
        },
        end: {
          line: 539,
          column: 29
        }
      },
      "262": {
        start: {
          line: 540,
          column: 24
        },
        end: {
          line: 540,
          column: 25
        }
      },
      "263": {
        start: {
          line: 541,
          column: 8
        },
        end: {
          line: 547,
          column: 9
        }
      },
      "264": {
        start: {
          line: 541,
          column: 21
        },
        end: {
          line: 541,
          column: 22
        }
      },
      "265": {
        start: {
          line: 542,
          column: 12
        },
        end: {
          line: 546,
          column: 13
        }
      },
      "266": {
        start: {
          line: 542,
          column: 25
        },
        end: {
          line: 542,
          column: 30
        }
      },
      "267": {
        start: {
          line: 543,
          column: 33
        },
        end: {
          line: 543,
          column: 95
        }
      },
      "268": {
        start: {
          line: 544,
          column: 16
        },
        end: {
          line: 544,
          column: 42
        }
      },
      "269": {
        start: {
          line: 545,
          column: 16
        },
        end: {
          line: 545,
          column: 28
        }
      },
      "270": {
        start: {
          line: 548,
          column: 8
        },
        end: {
          line: 548,
          column: 61
        }
      },
      "271": {
        start: {
          line: 554,
          column: 23
        },
        end: {
          line: 554,
          column: 24
        }
      },
      "272": {
        start: {
          line: 555,
          column: 8
        },
        end: {
          line: 560,
          column: 9
        }
      },
      "273": {
        start: {
          line: 555,
          column: 21
        },
        end: {
          line: 555,
          column: 22
        }
      },
      "274": {
        start: {
          line: 556,
          column: 12
        },
        end: {
          line: 559,
          column: 13
        }
      },
      "275": {
        start: {
          line: 557,
          column: 29
        },
        end: {
          line: 557,
          column: 58
        }
      },
      "276": {
        start: {
          line: 558,
          column: 16
        },
        end: {
          line: 558,
          column: 40
        }
      },
      "277": {
        start: {
          line: 561,
          column: 8
        },
        end: {
          line: 561,
          column: 35
        }
      },
      "278": {
        start: {
          line: 568,
          column: 8
        },
        end: {
          line: 570,
          column: 9
        }
      },
      "279": {
        start: {
          line: 569,
          column: 12
        },
        end: {
          line: 569,
          column: 24
        }
      },
      "280": {
        start: {
          line: 572,
          column: 8
        },
        end: {
          line: 578,
          column: 9
        }
      },
      "281": {
        start: {
          line: 573,
          column: 34
        },
        end: {
          line: 573,
          column: 57
        }
      },
      "282": {
        start: {
          line: 574,
          column: 39
        },
        end: {
          line: 574,
          column: 121
        }
      },
      "283": {
        start: {
          line: 575,
          column: 12
        },
        end: {
          line: 577,
          column: 13
        }
      },
      "284": {
        start: {
          line: 576,
          column: 16
        },
        end: {
          line: 576,
          column: 28
        }
      },
      "285": {
        start: {
          line: 579,
          column: 8
        },
        end: {
          line: 579,
          column: 21
        }
      },
      "286": {
        start: {
          line: 586,
          column: 26
        },
        end: {
          line: 586,
          column: 51
        }
      },
      "287": {
        start: {
          line: 587,
          column: 35
        },
        end: {
          line: 587,
          column: 38
        }
      },
      "288": {
        start: {
          line: 588,
          column: 8
        },
        end: {
          line: 593,
          column: 9
        }
      },
      "289": {
        start: {
          line: 589,
          column: 12
        },
        end: {
          line: 589,
          column: 93
        }
      },
      "290": {
        start: {
          line: 592,
          column: 12
        },
        end: {
          line: 592,
          column: 94
        }
      },
      "291": {
        start: {
          line: 599,
          column: 30
        },
        end: {
          line: 599,
          column: 59
        }
      },
      "292": {
        start: {
          line: 601,
          column: 29
        },
        end: {
          line: 610,
          column: 9
        }
      },
      "293": {
        start: {
          line: 612,
          column: 8
        },
        end: {
          line: 620,
          column: 9
        }
      },
      "294": {
        start: {
          line: 613,
          column: 12
        },
        end: {
          line: 615,
          column: 15
        }
      },
      "295": {
        start: {
          line: 614,
          column: 16
        },
        end: {
          line: 614,
          column: 87
        }
      },
      "296": {
        start: {
          line: 617,
          column: 12
        },
        end: {
          line: 619,
          column: 15
        }
      },
      "297": {
        start: {
          line: 618,
          column: 16
        },
        end: {
          line: 618,
          column: 109
        }
      },
      "298": {
        start: {
          line: 621,
          column: 27
        },
        end: {
          line: 635,
          column: 9
        }
      },
      "299": {
        start: {
          line: 626,
          column: 54
        },
        end: {
          line: 626,
          column: 67
        }
      },
      "300": {
        start: {
          line: 627,
          column: 57
        },
        end: {
          line: 627,
          column: 73
        }
      },
      "301": {
        start: {
          line: 628,
          column: 52
        },
        end: {
          line: 628,
          column: 63
        }
      },
      "302": {
        start: {
          line: 629,
          column: 62
        },
        end: {
          line: 629,
          column: 84
        }
      },
      "303": {
        start: {
          line: 636,
          column: 36
        },
        end: {
          line: 641,
          column: 9
        }
      },
      "304": {
        start: {
          line: 642,
          column: 8
        },
        end: {
          line: 653,
          column: 10
        }
      },
      "305": {
        start: {
          line: 657,
          column: 8
        },
        end: {
          line: 665,
          column: 10
        }
      },
      "306": {
        start: {
          line: 668,
          column: 8
        },
        end: {
          line: 668,
          column: 78
        }
      },
      "307": {
        start: {
          line: 671,
          column: 20
        },
        end: {
          line: 671,
          column: 24
        }
      },
      "308": {
        start: {
          line: 672,
          column: 8
        },
        end: {
          line: 675,
          column: 10
        }
      },
      "309": {
        start: {
          line: 673,
          column: 12
        },
        end: {
          line: 673,
          column: 52
        }
      },
      "310": {
        start: {
          line: 674,
          column: 12
        },
        end: {
          line: 674,
          column: 34
        }
      },
      "311": {
        start: {
          line: 679,
          column: 19
        },
        end: {
          line: 679,
          column: 32
        }
      },
      "312": {
        start: {
          line: 680,
          column: 19
        },
        end: {
          line: 680,
          column: 32
        }
      },
      "313": {
        start: {
          line: 681,
          column: 8
        },
        end: {
          line: 681,
          column: 73
        }
      },
      "314": {
        start: {
          line: 684,
          column: 0
        },
        end: {
          line: 684,
          column: 44
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 22,
            column: 4
          },
          end: {
            line: 22,
            column: 5
          }
        },
        loc: {
          start: {
            line: 22,
            column: 28
          },
          end: {
            line: 53,
            column: 5
          }
        },
        line: 22
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 57,
            column: 4
          },
          end: {
            line: 57,
            column: 5
          }
        },
        loc: {
          start: {
            line: 57,
            column: 68
          },
          end: {
            line: 79,
            column: 5
          }
        },
        line: 57
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 83,
            column: 4
          },
          end: {
            line: 83,
            column: 5
          }
        },
        loc: {
          start: {
            line: 83,
            column: 33
          },
          end: {
            line: 91,
            column: 5
          }
        },
        line: 83
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 95,
            column: 4
          },
          end: {
            line: 95,
            column: 5
          }
        },
        loc: {
          start: {
            line: 95,
            column: 83
          },
          end: {
            line: 104,
            column: 5
          }
        },
        line: 95
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 108,
            column: 4
          },
          end: {
            line: 108,
            column: 5
          }
        },
        loc: {
          start: {
            line: 108,
            column: 36
          },
          end: {
            line: 133,
            column: 5
          }
        },
        line: 108
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 137,
            column: 4
          },
          end: {
            line: 137,
            column: 5
          }
        },
        loc: {
          start: {
            line: 137,
            column: 90
          },
          end: {
            line: 186,
            column: 5
          }
        },
        line: 137
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 165,
            column: 72
          },
          end: {
            line: 165,
            column: 73
          }
        },
        loc: {
          start: {
            line: 165,
            column: 77
          },
          end: {
            line: 165,
            column: 83
          }
        },
        line: 165
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 169,
            column: 28
          },
          end: {
            line: 169,
            column: 29
          }
        },
        loc: {
          start: {
            line: 169,
            column: 33
          },
          end: {
            line: 169,
            column: 38
          }
        },
        line: 169
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 170,
            column: 28
          },
          end: {
            line: 170,
            column: 29
          }
        },
        loc: {
          start: {
            line: 170,
            column: 40
          },
          end: {
            line: 170,
            column: 47
          }
        },
        line: 170
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 176,
            column: 80
          },
          end: {
            line: 176,
            column: 81
          }
        },
        loc: {
          start: {
            line: 176,
            column: 85
          },
          end: {
            line: 176,
            column: 96
          }
        },
        line: 176
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 190,
            column: 4
          },
          end: {
            line: 190,
            column: 5
          }
        },
        loc: {
          start: {
            line: 190,
            column: 47
          },
          end: {
            line: 195,
            column: 5
          }
        },
        line: 190
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 191,
            column: 37
          },
          end: {
            line: 191,
            column: 38
          }
        },
        loc: {
          start: {
            line: 191,
            column: 59
          },
          end: {
            line: 194,
            column: 9
          }
        },
        line: 191
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 199,
            column: 4
          },
          end: {
            line: 199,
            column: 5
          }
        },
        loc: {
          start: {
            line: 199,
            column: 76
          },
          end: {
            line: 233,
            column: 5
          }
        },
        line: 199
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 229,
            column: 32
          },
          end: {
            line: 229,
            column: 33
          }
        },
        loc: {
          start: {
            line: 229,
            column: 46
          },
          end: {
            line: 229,
            column: 62
          }
        },
        line: 229
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 237,
            column: 4
          },
          end: {
            line: 237,
            column: 5
          }
        },
        loc: {
          start: {
            line: 237,
            column: 16
          },
          end: {
            line: 250,
            column: 5
          }
        },
        line: 237
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 254,
            column: 4
          },
          end: {
            line: 254,
            column: 5
          }
        },
        loc: {
          start: {
            line: 254,
            column: 26
          },
          end: {
            line: 268,
            column: 5
          }
        },
        line: 254
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 264,
            column: 28
          },
          end: {
            line: 264,
            column: 29
          }
        },
        loc: {
          start: {
            line: 264,
            column: 38
          },
          end: {
            line: 264,
            column: 59
          }
        },
        line: 264
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 272,
            column: 4
          },
          end: {
            line: 272,
            column: 5
          }
        },
        loc: {
          start: {
            line: 272,
            column: 29
          },
          end: {
            line: 290,
            column: 5
          }
        },
        line: 272
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 275,
            column: 59
          },
          end: {
            line: 275,
            column: 60
          }
        },
        loc: {
          start: {
            line: 275,
            column: 66
          },
          end: {
            line: 275,
            column: 77
          }
        },
        line: 275
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 276,
            column: 52
          },
          end: {
            line: 276,
            column: 53
          }
        },
        loc: {
          start: {
            line: 276,
            column: 59
          },
          end: {
            line: 276,
            column: 87
          }
        },
        line: 276
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 277,
            column: 52
          },
          end: {
            line: 277,
            column: 53
          }
        },
        loc: {
          start: {
            line: 277,
            column: 70
          },
          end: {
            line: 277,
            column: 83
          }
        },
        line: 277
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 294,
            column: 4
          },
          end: {
            line: 294,
            column: 5
          }
        },
        loc: {
          start: {
            line: 294,
            column: 20
          },
          end: {
            line: 313,
            column: 5
          }
        },
        line: 294
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 297,
            column: 59
          },
          end: {
            line: 297,
            column: 60
          }
        },
        loc: {
          start: {
            line: 297,
            column: 69
          },
          end: {
            line: 297,
            column: 90
          }
        },
        line: 297
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 299,
            column: 43
          },
          end: {
            line: 299,
            column: 44
          }
        },
        loc: {
          start: {
            line: 299,
            column: 57
          },
          end: {
            line: 299,
            column: 66
          }
        },
        line: 299
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 300,
            column: 39
          },
          end: {
            line: 300,
            column: 40
          }
        },
        loc: {
          start: {
            line: 300,
            column: 54
          },
          end: {
            line: 300,
            column: 64
          }
        },
        line: 300
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 317,
            column: 4
          },
          end: {
            line: 317,
            column: 5
          }
        },
        loc: {
          start: {
            line: 317,
            column: 22
          },
          end: {
            line: 324,
            column: 5
          }
        },
        line: 317
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 328,
            column: 4
          },
          end: {
            line: 328,
            column: 5
          }
        },
        loc: {
          start: {
            line: 328,
            column: 41
          },
          end: {
            line: 341,
            column: 5
          }
        },
        line: 328
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 345,
            column: 4
          },
          end: {
            line: 345,
            column: 5
          }
        },
        loc: {
          start: {
            line: 345,
            column: 43
          },
          end: {
            line: 358,
            column: 5
          }
        },
        line: 345
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 362,
            column: 4
          },
          end: {
            line: 362,
            column: 5
          }
        },
        loc: {
          start: {
            line: 362,
            column: 40
          },
          end: {
            line: 379,
            column: 5
          }
        },
        line: 362
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 383,
            column: 4
          },
          end: {
            line: 383,
            column: 5
          }
        },
        loc: {
          start: {
            line: 383,
            column: 39
          },
          end: {
            line: 389,
            column: 5
          }
        },
        line: 383
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 386,
            column: 41
          },
          end: {
            line: 386,
            column: 42
          }
        },
        loc: {
          start: {
            line: 386,
            column: 58
          },
          end: {
            line: 386,
            column: 107
          }
        },
        line: 386
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 387,
            column: 41
          },
          end: {
            line: 387,
            column: 42
          }
        },
        loc: {
          start: {
            line: 387,
            column: 58
          },
          end: {
            line: 387,
            column: 107
          }
        },
        line: 387
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 393,
            column: 4
          },
          end: {
            line: 393,
            column: 5
          }
        },
        loc: {
          start: {
            line: 393,
            column: 42
          },
          end: {
            line: 410,
            column: 5
          }
        },
        line: 393
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 397,
            column: 41
          },
          end: {
            line: 397,
            column: 42
          }
        },
        loc: {
          start: {
            line: 397,
            column: 58
          },
          end: {
            line: 402,
            column: 9
          }
        },
        line: 397
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 403,
            column: 41
          },
          end: {
            line: 403,
            column: 42
          }
        },
        loc: {
          start: {
            line: 403,
            column: 58
          },
          end: {
            line: 408,
            column: 9
          }
        },
        line: 403
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 414,
            column: 4
          },
          end: {
            line: 414,
            column: 5
          }
        },
        loc: {
          start: {
            line: 414,
            column: 32
          },
          end: {
            line: 422,
            column: 5
          }
        },
        line: 414
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 426,
            column: 4
          },
          end: {
            line: 426,
            column: 5
          }
        },
        loc: {
          start: {
            line: 426,
            column: 31
          },
          end: {
            line: 448,
            column: 5
          }
        },
        line: 426
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 452,
            column: 4
          },
          end: {
            line: 452,
            column: 5
          }
        },
        loc: {
          start: {
            line: 452,
            column: 38
          },
          end: {
            line: 456,
            column: 5
          }
        },
        line: 452
      },
      "38": {
        name: "(anonymous_38)",
        decl: {
          start: {
            line: 460,
            column: 4
          },
          end: {
            line: 460,
            column: 5
          }
        },
        loc: {
          start: {
            line: 460,
            column: 30
          },
          end: {
            line: 462,
            column: 5
          }
        },
        line: 460
      },
      "39": {
        name: "(anonymous_39)",
        decl: {
          start: {
            line: 466,
            column: 4
          },
          end: {
            line: 466,
            column: 5
          }
        },
        loc: {
          start: {
            line: 466,
            column: 40
          },
          end: {
            line: 484,
            column: 5
          }
        },
        line: 466
      },
      "40": {
        name: "(anonymous_40)",
        decl: {
          start: {
            line: 488,
            column: 4
          },
          end: {
            line: 488,
            column: 5
          }
        },
        loc: {
          start: {
            line: 488,
            column: 27
          },
          end: {
            line: 495,
            column: 5
          }
        },
        line: 488
      },
      "41": {
        name: "(anonymous_41)",
        decl: {
          start: {
            line: 492,
            column: 22
          },
          end: {
            line: 492,
            column: 23
          }
        },
        loc: {
          start: {
            line: 492,
            column: 32
          },
          end: {
            line: 492,
            column: 53
          }
        },
        line: 492
      },
      "42": {
        name: "(anonymous_42)",
        decl: {
          start: {
            line: 499,
            column: 4
          },
          end: {
            line: 499,
            column: 5
          }
        },
        loc: {
          start: {
            line: 499,
            column: 27
          },
          end: {
            line: 504,
            column: 5
          }
        },
        line: 499
      },
      "43": {
        name: "(anonymous_43)",
        decl: {
          start: {
            line: 500,
            column: 44
          },
          end: {
            line: 500,
            column: 45
          }
        },
        loc: {
          start: {
            line: 500,
            column: 63
          },
          end: {
            line: 500,
            column: 110
          }
        },
        line: 500
      },
      "44": {
        name: "(anonymous_44)",
        decl: {
          start: {
            line: 508,
            column: 4
          },
          end: {
            line: 508,
            column: 5
          }
        },
        loc: {
          start: {
            line: 508,
            column: 24
          },
          end: {
            line: 512,
            column: 5
          }
        },
        line: 508
      },
      "45": {
        name: "(anonymous_45)",
        decl: {
          start: {
            line: 511,
            column: 50
          },
          end: {
            line: 511,
            column: 51
          }
        },
        loc: {
          start: {
            line: 511,
            column: 57
          },
          end: {
            line: 511,
            column: 69
          }
        },
        line: 511
      },
      "46": {
        name: "(anonymous_46)",
        decl: {
          start: {
            line: 516,
            column: 4
          },
          end: {
            line: 516,
            column: 5
          }
        },
        loc: {
          start: {
            line: 516,
            column: 20
          },
          end: {
            line: 532,
            column: 5
          }
        },
        line: 516
      },
      "47": {
        name: "(anonymous_47)",
        decl: {
          start: {
            line: 517,
            column: 58
          },
          end: {
            line: 517,
            column: 59
          }
        },
        loc: {
          start: {
            line: 517,
            column: 65
          },
          end: {
            line: 517,
            column: 77
          }
        },
        line: 517
      },
      "48": {
        name: "(anonymous_48)",
        decl: {
          start: {
            line: 518,
            column: 49
          },
          end: {
            line: 518,
            column: 50
          }
        },
        loc: {
          start: {
            line: 518,
            column: 56
          },
          end: {
            line: 518,
            column: 67
          }
        },
        line: 518
      },
      "49": {
        name: "(anonymous_49)",
        decl: {
          start: {
            line: 525,
            column: 45
          },
          end: {
            line: 525,
            column: 46
          }
        },
        loc: {
          start: {
            line: 525,
            column: 57
          },
          end: {
            line: 525,
            column: 64
          }
        },
        line: 525
      },
      "50": {
        name: "(anonymous_50)",
        decl: {
          start: {
            line: 528,
            column: 57
          },
          end: {
            line: 528,
            column: 58
          }
        },
        loc: {
          start: {
            line: 528,
            column: 64
          },
          end: {
            line: 528,
            column: 77
          }
        },
        line: 528
      },
      "51": {
        name: "(anonymous_51)",
        decl: {
          start: {
            line: 536,
            column: 4
          },
          end: {
            line: 536,
            column: 5
          }
        },
        loc: {
          start: {
            line: 536,
            column: 25
          },
          end: {
            line: 549,
            column: 5
          }
        },
        line: 536
      },
      "52": {
        name: "(anonymous_52)",
        decl: {
          start: {
            line: 553,
            column: 4
          },
          end: {
            line: 553,
            column: 5
          }
        },
        loc: {
          start: {
            line: 553,
            column: 34
          },
          end: {
            line: 562,
            column: 5
          }
        },
        line: 553
      },
      "53": {
        name: "(anonymous_53)",
        decl: {
          start: {
            line: 566,
            column: 4
          },
          end: {
            line: 566,
            column: 5
          }
        },
        loc: {
          start: {
            line: 566,
            column: 29
          },
          end: {
            line: 580,
            column: 5
          }
        },
        line: 566
      },
      "54": {
        name: "(anonymous_54)",
        decl: {
          start: {
            line: 584,
            column: 4
          },
          end: {
            line: 584,
            column: 5
          }
        },
        loc: {
          start: {
            line: 584,
            column: 22
          },
          end: {
            line: 594,
            column: 5
          }
        },
        line: 584
      },
      "55": {
        name: "(anonymous_55)",
        decl: {
          start: {
            line: 598,
            column: 4
          },
          end: {
            line: 598,
            column: 5
          }
        },
        loc: {
          start: {
            line: 598,
            column: 49
          },
          end: {
            line: 654,
            column: 5
          }
        },
        line: 598
      },
      "56": {
        name: "(anonymous_56)",
        decl: {
          start: {
            line: 613,
            column: 38
          },
          end: {
            line: 613,
            column: 39
          }
        },
        loc: {
          start: {
            line: 613,
            column: 59
          },
          end: {
            line: 615,
            column: 13
          }
        },
        line: 613
      },
      "57": {
        name: "(anonymous_57)",
        decl: {
          start: {
            line: 617,
            column: 50
          },
          end: {
            line: 617,
            column: 51
          }
        },
        loc: {
          start: {
            line: 617,
            column: 72
          },
          end: {
            line: 619,
            column: 13
          }
        },
        line: 617
      },
      "58": {
        name: "(anonymous_58)",
        decl: {
          start: {
            line: 626,
            column: 49
          },
          end: {
            line: 626,
            column: 50
          }
        },
        loc: {
          start: {
            line: 626,
            column: 54
          },
          end: {
            line: 626,
            column: 67
          }
        },
        line: 626
      },
      "59": {
        name: "(anonymous_59)",
        decl: {
          start: {
            line: 627,
            column: 52
          },
          end: {
            line: 627,
            column: 53
          }
        },
        loc: {
          start: {
            line: 627,
            column: 57
          },
          end: {
            line: 627,
            column: 73
          }
        },
        line: 627
      },
      "60": {
        name: "(anonymous_60)",
        decl: {
          start: {
            line: 628,
            column: 47
          },
          end: {
            line: 628,
            column: 48
          }
        },
        loc: {
          start: {
            line: 628,
            column: 52
          },
          end: {
            line: 628,
            column: 63
          }
        },
        line: 628
      },
      "61": {
        name: "(anonymous_61)",
        decl: {
          start: {
            line: 629,
            column: 57
          },
          end: {
            line: 629,
            column: 58
          }
        },
        loc: {
          start: {
            line: 629,
            column: 62
          },
          end: {
            line: 629,
            column: 84
          }
        },
        line: 629
      },
      "62": {
        name: "(anonymous_62)",
        decl: {
          start: {
            line: 656,
            column: 4
          },
          end: {
            line: 656,
            column: 5
          }
        },
        loc: {
          start: {
            line: 656,
            column: 24
          },
          end: {
            line: 666,
            column: 5
          }
        },
        line: 656
      },
      "63": {
        name: "(anonymous_63)",
        decl: {
          start: {
            line: 667,
            column: 4
          },
          end: {
            line: 667,
            column: 5
          }
        },
        loc: {
          start: {
            line: 667,
            column: 27
          },
          end: {
            line: 669,
            column: 5
          }
        },
        line: 667
      },
      "64": {
        name: "(anonymous_64)",
        decl: {
          start: {
            line: 670,
            column: 4
          },
          end: {
            line: 670,
            column: 5
          }
        },
        loc: {
          start: {
            line: 670,
            column: 29
          },
          end: {
            line: 676,
            column: 5
          }
        },
        line: 670
      },
      "65": {
        name: "(anonymous_65)",
        decl: {
          start: {
            line: 672,
            column: 15
          },
          end: {
            line: 672,
            column: 16
          }
        },
        loc: {
          start: {
            line: 672,
            column: 21
          },
          end: {
            line: 675,
            column: 9
          }
        },
        line: 672
      },
      "66": {
        name: "(anonymous_66)",
        decl: {
          start: {
            line: 677,
            column: 4
          },
          end: {
            line: 677,
            column: 5
          }
        },
        loc: {
          start: {
            line: 677,
            column: 21
          },
          end: {
            line: 682,
            column: 5
          }
        },
        line: 677
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 46,
            column: 8
          },
          end: {
            line: 51,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 46,
            column: 8
          },
          end: {
            line: 51,
            column: 9
          }
        }, {
          start: {
            line: 49,
            column: 13
          },
          end: {
            line: 51,
            column: 9
          }
        }],
        line: 46
      },
      "1": {
        loc: {
          start: {
            line: 68,
            column: 16
          },
          end: {
            line: 70,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 68,
            column: 16
          },
          end: {
            line: 70,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 68
      },
      "2": {
        loc: {
          start: {
            line: 111,
            column: 12
          },
          end: {
            line: 122,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 111,
            column: 12
          },
          end: {
            line: 122,
            column: 13
          }
        }, {
          start: {
            line: 116,
            column: 17
          },
          end: {
            line: 122,
            column: 13
          }
        }],
        line: 111
      },
      "3": {
        loc: {
          start: {
            line: 111,
            column: 16
          },
          end: {
            line: 111,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 111,
            column: 16
          },
          end: {
            line: 111,
            column: 39
          }
        }, {
          start: {
            line: 111,
            column: 43
          },
          end: {
            line: 111,
            column: 77
          }
        }],
        line: 111
      },
      "4": {
        loc: {
          start: {
            line: 118,
            column: 28
          },
          end: {
            line: 118,
            column: 101
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 118,
            column: 74
          },
          end: {
            line: 118,
            column: 97
          }
        }, {
          start: {
            line: 118,
            column: 100
          },
          end: {
            line: 118,
            column: 101
          }
        }],
        line: 118
      },
      "5": {
        loc: {
          start: {
            line: 119,
            column: 28
          },
          end: {
            line: 119,
            column: 101
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 119,
            column: 74
          },
          end: {
            line: 119,
            column: 97
          }
        }, {
          start: {
            line: 119,
            column: 100
          },
          end: {
            line: 119,
            column: 101
          }
        }],
        line: 119
      },
      "6": {
        loc: {
          start: {
            line: 142,
            column: 12
          },
          end: {
            line: 157,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 142,
            column: 12
          },
          end: {
            line: 157,
            column: 13
          }
        }, {
          start: {
            line: 148,
            column: 17
          },
          end: {
            line: 157,
            column: 13
          }
        }],
        line: 142
      },
      "7": {
        loc: {
          start: {
            line: 167,
            column: 12
          },
          end: {
            line: 178,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 167,
            column: 12
          },
          end: {
            line: 178,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 167
      },
      "8": {
        loc: {
          start: {
            line: 167,
            column: 16
          },
          end: {
            line: 167,
            column: 88
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 167,
            column: 16
          },
          end: {
            line: 167,
            column: 36
          }
        }, {
          start: {
            line: 167,
            column: 40
          },
          end: {
            line: 167,
            column: 88
          }
        }],
        line: 167
      },
      "9": {
        loc: {
          start: {
            line: 171,
            column: 16
          },
          end: {
            line: 177,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 171,
            column: 16
          },
          end: {
            line: 177,
            column: 17
          }
        }, {
          start: {
            line: 174,
            column: 21
          },
          end: {
            line: 177,
            column: 17
          }
        }],
        line: 171
      },
      "10": {
        loc: {
          start: {
            line: 206,
            column: 28
          },
          end: {
            line: 206,
            column: 56
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 206,
            column: 28
          },
          end: {
            line: 206,
            column: 42
          }
        }, {
          start: {
            line: 206,
            column: 46
          },
          end: {
            line: 206,
            column: 56
          }
        }],
        line: 206
      },
      "11": {
        loc: {
          start: {
            line: 210,
            column: 12
          },
          end: {
            line: 212,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 210,
            column: 12
          },
          end: {
            line: 212,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 210
      },
      "12": {
        loc: {
          start: {
            line: 214,
            column: 12
          },
          end: {
            line: 216,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 214,
            column: 12
          },
          end: {
            line: 216,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 214
      },
      "13": {
        loc: {
          start: {
            line: 217,
            column: 12
          },
          end: {
            line: 219,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 217,
            column: 12
          },
          end: {
            line: 219,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 217
      },
      "14": {
        loc: {
          start: {
            line: 238,
            column: 8
          },
          end: {
            line: 249,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 239,
            column: 12
          },
          end: {
            line: 240,
            column: 50
          }
        }, {
          start: {
            line: 241,
            column: 12
          },
          end: {
            line: 242,
            column: 53
          }
        }, {
          start: {
            line: 243,
            column: 12
          },
          end: {
            line: 244,
            column: 44
          }
        }, {
          start: {
            line: 245,
            column: 12
          },
          end: {
            line: 246,
            column: 46
          }
        }, {
          start: {
            line: 247,
            column: 12
          },
          end: {
            line: 248,
            column: 50
          }
        }],
        line: 238
      },
      "15": {
        loc: {
          start: {
            line: 256,
            column: 31
          },
          end: {
            line: 256,
            column: 66
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 256,
            column: 31
          },
          end: {
            line: 256,
            column: 61
          }
        }, {
          start: {
            line: 256,
            column: 65
          },
          end: {
            line: 256,
            column: 66
          }
        }],
        line: 256
      },
      "16": {
        loc: {
          start: {
            line: 283,
            column: 16
          },
          end: {
            line: 286,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 283,
            column: 16
          },
          end: {
            line: 286,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 283
      },
      "17": {
        loc: {
          start: {
            line: 306,
            column: 16
          },
          end: {
            line: 309,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 306,
            column: 16
          },
          end: {
            line: 309,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 306
      },
      "18": {
        loc: {
          start: {
            line: 329,
            column: 8
          },
          end: {
            line: 340,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 330,
            column: 12
          },
          end: {
            line: 331,
            column: 67
          }
        }, {
          start: {
            line: 332,
            column: 12
          },
          end: {
            line: 333,
            column: 64
          }
        }, {
          start: {
            line: 334,
            column: 12
          },
          end: {
            line: 335,
            column: 63
          }
        }, {
          start: {
            line: 336,
            column: 12
          },
          end: {
            line: 337,
            column: 66
          }
        }, {
          start: {
            line: 338,
            column: 12
          },
          end: {
            line: 339,
            column: 64
          }
        }],
        line: 329
      },
      "19": {
        loc: {
          start: {
            line: 386,
            column: 58
          },
          end: {
            line: 386,
            column: 107
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 386,
            column: 80
          },
          end: {
            line: 386,
            column: 84
          }
        }, {
          start: {
            line: 386,
            column: 87
          },
          end: {
            line: 386,
            column: 107
          }
        }],
        line: 386
      },
      "20": {
        loc: {
          start: {
            line: 387,
            column: 58
          },
          end: {
            line: 387,
            column: 107
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 387,
            column: 80
          },
          end: {
            line: 387,
            column: 84
          }
        }, {
          start: {
            line: 387,
            column: 87
          },
          end: {
            line: 387,
            column: 107
          }
        }],
        line: 387
      },
      "21": {
        loc: {
          start: {
            line: 398,
            column: 12
          },
          end: {
            line: 400,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 398,
            column: 12
          },
          end: {
            line: 400,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 398
      },
      "22": {
        loc: {
          start: {
            line: 398,
            column: 16
          },
          end: {
            line: 398,
            column: 84
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 398,
            column: 16
          },
          end: {
            line: 398,
            column: 40
          }
        }, {
          start: {
            line: 398,
            column: 44
          },
          end: {
            line: 398,
            column: 84
          }
        }],
        line: 398
      },
      "23": {
        loc: {
          start: {
            line: 404,
            column: 12
          },
          end: {
            line: 406,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 404,
            column: 12
          },
          end: {
            line: 406,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 404
      },
      "24": {
        loc: {
          start: {
            line: 404,
            column: 16
          },
          end: {
            line: 404,
            column: 84
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 404,
            column: 16
          },
          end: {
            line: 404,
            column: 40
          }
        }, {
          start: {
            line: 404,
            column: 44
          },
          end: {
            line: 404,
            column: 84
          }
        }],
        line: 404
      },
      "25": {
        loc: {
          start: {
            line: 417,
            column: 12
          },
          end: {
            line: 419,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 417,
            column: 12
          },
          end: {
            line: 419,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 417
      },
      "26": {
        loc: {
          start: {
            line: 427,
            column: 8
          },
          end: {
            line: 446,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 427,
            column: 8
          },
          end: {
            line: 446,
            column: 9
          }
        }, {
          start: {
            line: 432,
            column: 13
          },
          end: {
            line: 446,
            column: 9
          }
        }],
        line: 427
      },
      "27": {
        loc: {
          start: {
            line: 427,
            column: 12
          },
          end: {
            line: 427,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 427,
            column: 12
          },
          end: {
            line: 427,
            column: 35
          }
        }, {
          start: {
            line: 427,
            column: 39
          },
          end: {
            line: 427,
            column: 73
          }
        }],
        line: 427
      },
      "28": {
        loc: {
          start: {
            line: 432,
            column: 13
          },
          end: {
            line: 446,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 432,
            column: 13
          },
          end: {
            line: 446,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 432
      },
      "29": {
        loc: {
          start: {
            line: 434,
            column: 24
          },
          end: {
            line: 434,
            column: 97
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 434,
            column: 70
          },
          end: {
            line: 434,
            column: 93
          }
        }, {
          start: {
            line: 434,
            column: 96
          },
          end: {
            line: 434,
            column: 97
          }
        }],
        line: 434
      },
      "30": {
        loc: {
          start: {
            line: 435,
            column: 24
          },
          end: {
            line: 435,
            column: 97
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 435,
            column: 70
          },
          end: {
            line: 435,
            column: 93
          }
        }, {
          start: {
            line: 435,
            column: 96
          },
          end: {
            line: 435,
            column: 97
          }
        }],
        line: 435
      },
      "31": {
        loc: {
          start: {
            line: 436,
            column: 12
          },
          end: {
            line: 445,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 437,
            column: 16
          },
          end: {
            line: 438,
            column: 65
          }
        }, {
          start: {
            line: 439,
            column: 16
          },
          end: {
            line: 440,
            column: 58
          }
        }, {
          start: {
            line: 441,
            column: 16
          },
          end: {
            line: 442,
            column: 67
          }
        }, {
          start: {
            line: 443,
            column: 16
          },
          end: {
            line: 444,
            column: 65
          }
        }],
        line: 436
      },
      "32": {
        loc: {
          start: {
            line: 472,
            column: 8
          },
          end: {
            line: 481,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 472,
            column: 8
          },
          end: {
            line: 481,
            column: 9
          }
        }, {
          start: {
            line: 477,
            column: 13
          },
          end: {
            line: 481,
            column: 9
          }
        }],
        line: 472
      },
      "33": {
        loc: {
          start: {
            line: 500,
            column: 63
          },
          end: {
            line: 500,
            column: 110
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 500,
            column: 96
          },
          end: {
            line: 500,
            column: 103
          }
        }, {
          start: {
            line: 500,
            column: 106
          },
          end: {
            line: 500,
            column: 110
          }
        }],
        line: 500
      },
      "34": {
        loc: {
          start: {
            line: 501,
            column: 8
          },
          end: {
            line: 503,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 501,
            column: 8
          },
          end: {
            line: 503,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 501
      },
      "35": {
        loc: {
          start: {
            line: 501,
            column: 12
          },
          end: {
            line: 501,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 501,
            column: 12
          },
          end: {
            line: 501,
            column: 32
          }
        }, {
          start: {
            line: 501,
            column: 36
          },
          end: {
            line: 501,
            column: 78
          }
        }],
        line: 501
      },
      "36": {
        loc: {
          start: {
            line: 519,
            column: 8
          },
          end: {
            line: 521,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 519,
            column: 8
          },
          end: {
            line: 521,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 519
      },
      "37": {
        loc: {
          start: {
            line: 537,
            column: 8
          },
          end: {
            line: 538,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 537,
            column: 8
          },
          end: {
            line: 538,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 537
      },
      "38": {
        loc: {
          start: {
            line: 548,
            column: 15
          },
          end: {
            line: 548,
            column: 60
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 548,
            column: 31
          },
          end: {
            line: 548,
            column: 56
          }
        }, {
          start: {
            line: 548,
            column: 59
          },
          end: {
            line: 548,
            column: 60
          }
        }],
        line: 548
      },
      "39": {
        loc: {
          start: {
            line: 556,
            column: 12
          },
          end: {
            line: 559,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 556,
            column: 12
          },
          end: {
            line: 559,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 556
      },
      "40": {
        loc: {
          start: {
            line: 556,
            column: 16
          },
          end: {
            line: 556,
            column: 86
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 556,
            column: 16
          },
          end: {
            line: 556,
            column: 49
          }
        }, {
          start: {
            line: 556,
            column: 53
          },
          end: {
            line: 556,
            column: 86
          }
        }],
        line: 556
      },
      "41": {
        loc: {
          start: {
            line: 568,
            column: 8
          },
          end: {
            line: 570,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 568,
            column: 8
          },
          end: {
            line: 570,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 568
      },
      "42": {
        loc: {
          start: {
            line: 572,
            column: 8
          },
          end: {
            line: 578,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 572,
            column: 8
          },
          end: {
            line: 578,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 572
      },
      "43": {
        loc: {
          start: {
            line: 575,
            column: 12
          },
          end: {
            line: 577,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 575,
            column: 12
          },
          end: {
            line: 577,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 575
      },
      "44": {
        loc: {
          start: {
            line: 588,
            column: 8
          },
          end: {
            line: 593,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 588,
            column: 8
          },
          end: {
            line: 593,
            column: 9
          }
        }, {
          start: {
            line: 591,
            column: 13
          },
          end: {
            line: 593,
            column: 9
          }
        }],
        line: 588
      },
      "45": {
        loc: {
          start: {
            line: 602,
            column: 16
          },
          end: {
            line: 602,
            column: 56
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 602,
            column: 16
          },
          end: {
            line: 602,
            column: 39
          }
        }, {
          start: {
            line: 602,
            column: 43
          },
          end: {
            line: 602,
            column: 56
          }
        }],
        line: 602
      },
      "46": {
        loc: {
          start: {
            line: 606,
            column: 22
          },
          end: {
            line: 606,
            column: 60
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 606,
            column: 22
          },
          end: {
            line: 606,
            column: 51
          }
        }, {
          start: {
            line: 606,
            column: 55
          },
          end: {
            line: 606,
            column: 60
          }
        }],
        line: 606
      },
      "47": {
        loc: {
          start: {
            line: 607,
            column: 21
          },
          end: {
            line: 607,
            column: 69
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 607,
            column: 21
          },
          end: {
            line: 607,
            column: 49
          }
        }, {
          start: {
            line: 607,
            column: 53
          },
          end: {
            line: 607,
            column: 69
          }
        }],
        line: 607
      },
      "48": {
        loc: {
          start: {
            line: 612,
            column: 8
          },
          end: {
            line: 620,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 612,
            column: 8
          },
          end: {
            line: 620,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 612
      },
      "49": {
        loc: {
          start: {
            line: 618,
            column: 61
          },
          end: {
            line: 618,
            column: 108
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 618,
            column: 61
          },
          end: {
            line: 618,
            column: 103
          }
        }, {
          start: {
            line: 618,
            column: 107
          },
          end: {
            line: 618,
            column: 108
          }
        }],
        line: 618
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0,
      "210": 0,
      "211": 0,
      "212": 0,
      "213": 0,
      "214": 0,
      "215": 0,
      "216": 0,
      "217": 0,
      "218": 0,
      "219": 0,
      "220": 0,
      "221": 0,
      "222": 0,
      "223": 0,
      "224": 0,
      "225": 0,
      "226": 0,
      "227": 0,
      "228": 0,
      "229": 0,
      "230": 0,
      "231": 0,
      "232": 0,
      "233": 0,
      "234": 0,
      "235": 0,
      "236": 0,
      "237": 0,
      "238": 0,
      "239": 0,
      "240": 0,
      "241": 0,
      "242": 0,
      "243": 0,
      "244": 0,
      "245": 0,
      "246": 0,
      "247": 0,
      "248": 0,
      "249": 0,
      "250": 0,
      "251": 0,
      "252": 0,
      "253": 0,
      "254": 0,
      "255": 0,
      "256": 0,
      "257": 0,
      "258": 0,
      "259": 0,
      "260": 0,
      "261": 0,
      "262": 0,
      "263": 0,
      "264": 0,
      "265": 0,
      "266": 0,
      "267": 0,
      "268": 0,
      "269": 0,
      "270": 0,
      "271": 0,
      "272": 0,
      "273": 0,
      "274": 0,
      "275": 0,
      "276": 0,
      "277": 0,
      "278": 0,
      "279": 0,
      "280": 0,
      "281": 0,
      "282": 0,
      "283": 0,
      "284": 0,
      "285": 0,
      "286": 0,
      "287": 0,
      "288": 0,
      "289": 0,
      "290": 0,
      "291": 0,
      "292": 0,
      "293": 0,
      "294": 0,
      "295": 0,
      "296": 0,
      "297": 0,
      "298": 0,
      "299": 0,
      "300": 0,
      "301": 0,
      "302": 0,
      "303": 0,
      "304": 0,
      "305": 0,
      "306": 0,
      "307": 0,
      "308": 0,
      "309": 0,
      "310": 0,
      "311": 0,
      "312": 0,
      "313": 0,
      "314": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0, 0, 0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0, 0, 0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0, 0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\algorithms\\GeneticAlgorithm.ts",
      mappings: ";AAAA;;;;;;;;;;;;GAYG;;;AAEH,8EAgB0C;AAgC1C;;GAEG;AACH,MAAa,gBAAgB;IAU3B,YAAY,UAAgD;QAPpD,mBAAc,GAAsB,IAAI,CAAC;QACzC,gBAAW,GAAiB,EAAE,CAAC;QAC/B,eAAU,GAAW,CAAC,CAAC;QACvB,oBAAe,GAAW,CAAC,CAAC;QAC5B,YAAO,GAAuB,EAAE,CAAC;QAIvC,IAAI,CAAC,UAAU,GAAG;YAChB,cAAc,EAAE,EAAE;YAClB,cAAc,EAAE,GAAG;YACnB,aAAa,EAAE,GAAG;YAClB,YAAY,EAAE,GAAG;YACjB,SAAS,EAAE,CAAC;YACZ,eAAe,EAAE,YAAY;YAC7B,eAAe,EAAE,WAAW;YAC5B,cAAc,EAAE,UAAU;YAC1B,cAAc,EAAE,CAAC;YACjB,oBAAoB,EAAE,IAAI;YAC1B,kBAAkB,EAAE,SAAS;YAC7B,kBAAkB,EAAE,IAAI;YACxB,kBAAkB,EAAE,IAAI;YACxB,kBAAkB,EAAE,KAAK;YACzB,GAAG,UAAU;SACd,CAAC;QAEF,qCAAqC;QACrC,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;YAC5C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;QACnE,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC5B,CAAC;QAED,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;IACvB,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,QAAQ,CACnB,OAA4B,EAC5B,iBAAwC,EACxC,mBAA6C;QAE7C,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QAEpC,IAAI,CAAC;YACH,uBAAuB;YACvB,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;YAElC,4BAA4B;YAC5B,MAAM,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,iBAAiB,EAAE,mBAAmB,CAAC,CAAC;YAEpF,iBAAiB;YACjB,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,CAAC;gBACtC,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,iBAAiB,EAAE,mBAAmB,CAAC,CAAC;gBAC7E,IAAI,CAAC,aAAa,EAAE,CAAC;gBAErB,IAAI,IAAI,CAAC,UAAU,CAAC,kBAAkB,EAAE,CAAC;oBACvC,IAAI,CAAC,eAAe,EAAE,CAAC;gBACzB,CAAC;YACH,CAAC;YAED,sBAAsB;YACtB,OAAO,IAAI,CAAC,wBAAwB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QAE3D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;YAC/D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,OAA4B;QACtD,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;QACpB,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;QACzB,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QACrB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;QAElB,OAAO,CAAC,GAAG,CAAC,wDAAwD,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC,CAAC;IACxG,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,uBAAuB,CACnC,OAA4B,EAC5B,iBAAwC,EACxC,mBAA6C;QAE7C,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QAErB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC,EAAE,EAAE,CAAC;YACxD,MAAM,UAAU,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;YACxD,MAAM,IAAI,CAAC,kBAAkB,CAAC,UAAU,EAAE,OAAO,EAAE,iBAAiB,EAAE,mBAAmB,CAAC,CAAC;YAC3F,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACnC,CAAC;QAED,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG;IACK,sBAAsB,CAAC,OAA4B;QACzD,MAAM,KAAK,GAAwB,EAAE,CAAC;QAEtC,KAAK,MAAM,QAAQ,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACzC,IAAI,QAAQ,CAAC,cAAc,IAAI,QAAQ,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAClE,oBAAoB;gBACpB,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;gBAC/E,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC,CAAC;YACnD,CAAC;iBAAM,CAAC;gBACN,sBAAsB;gBACtB,MAAM,GAAG,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtF,MAAM,GAAG,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtF,MAAM,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;gBAChD,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpB,CAAC;QACH,CAAC;QAED,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,oBAAoB,EAAE;YAC/B,KAAK;YACL,OAAO,EAAE,CAAC;YACV,eAAe,EAAE,EAAE;YACnB,oBAAoB,EAAE,EAAE;YACxB,QAAQ,EAAE,IAAI;YACd,GAAG,EAAE,CAAC;SACP,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAC9B,UAAsB,EACtB,OAA4B,EAC5B,iBAAwC,EACxC,mBAA6C;QAE7C,IAAI,CAAC;YACH,0CAA0C;YAC1C,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,KAAK,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;YAE7E,sBAAsB;YACtB,IAAI,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC/C,mBAAmB;gBACnB,MAAM,cAAc,GAAG,iBAAiB,CAAC,SAAS,CAAC,CAAC;gBACpD,UAAU,CAAC,eAAe,GAAG,CAAC,cAAc,CAAC,CAAC;gBAC9C,UAAU,CAAC,OAAO,GAAG,cAAc,CAAC;YACtC,CAAC;iBAAM,CAAC;gBACN,uDAAuD;gBACvD,UAAU,CAAC,eAAe,GAAG,EAAE,CAAC;gBAChC,KAAK,MAAM,SAAS,IAAI,OAAO,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;oBACtD,MAAM,KAAK,GAAG,SAAS,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;oBACtD,UAAU,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACzC,CAAC;gBACD,mDAAmD;gBACnD,UAAU,CAAC,OAAO,GAAG,CAAC,CAAC;YACzB,CAAC;YAED,uBAAuB;YACvB,UAAU,CAAC,oBAAoB,GAAG,EAAE,CAAC;YACrC,KAAK,MAAM,kBAAkB,IAAI,mBAAmB,EAAE,CAAC;gBACrD,MAAM,SAAS,GAAG,kBAAkB,CAAC,SAAS,CAAC,CAAC;gBAChD,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAClD,CAAC;YAED,oBAAoB;YACpB,UAAU,CAAC,QAAQ,GAAG,UAAU,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;YAEzE,4BAA4B;YAC5B,IAAI,CAAC,UAAU,CAAC,QAAQ,IAAI,IAAI,CAAC,UAAU,CAAC,kBAAkB,KAAK,SAAS,EAAE,CAAC;gBAC7E,MAAM,OAAO,GAAG,UAAU,CAAC,oBAAoB;qBAC5C,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;qBAClB,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC;gBAEvE,IAAI,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAC/C,UAAU,CAAC,OAAO,IAAI,OAAO,CAAC;gBAChC,CAAC;qBAAM,CAAC;oBACN,oDAAoD;oBACpD,UAAU,CAAC,eAAe,GAAG,UAAU,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC;gBAChF,CAAC;YACH,CAAC;YAED,IAAI,CAAC,eAAe,EAAE,CAAC;QAEzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACrD,UAAU,CAAC,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC;YACtC,UAAU,CAAC,QAAQ,GAAG,KAAK,CAAC;QAC9B,CAAC;IACH,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,KAA0B,EAAE,iBAAyC;QAC5F,OAAO,iBAAiB,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;YACjD,GAAG,QAAQ;YACX,YAAY,EAAE,KAAK,CAAC,KAAK,CAAC;SAC3B,CAAC,CAAC,CAAC;IACN,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAC5B,OAA4B,EAC5B,iBAAwC,EACxC,mBAA6C;QAE7C,YAAY;QACZ,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAEjC,yBAAyB;QACzB,MAAM,SAAS,GAAiB,EAAE,CAAC;QAEnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAC3C,MAAM,OAAO,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAC3B,MAAM,OAAO,GAAG,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,8BAA8B;YAE5E,IAAI,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YACvC,IAAI,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAEvC,YAAY;YACZ,IAAI,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC;gBAClD,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;YAC/D,CAAC;YAED,WAAW;YACX,IAAI,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC;gBACjD,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YACxC,CAAC;YACD,IAAI,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC;gBACjD,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YACxC,CAAC;YAED,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACjC,CAAC;QAED,qBAAqB;QACrB,KAAK,MAAM,KAAK,IAAI,SAAS,EAAE,CAAC;YAC9B,MAAM,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,OAAO,EAAE,iBAAiB,EAAE,mBAAmB,CAAC,CAAC;QACxF,CAAC;QAED,cAAc;QACd,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;QAE5B,aAAa;QACb,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,CAAC;QAExD,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG;IACK,SAAS;QACf,QAAQ,IAAI,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC;YACxC,KAAK,YAAY;gBACf,OAAO,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACpC,KAAK,UAAU;gBACb,OAAO,IAAI,CAAC,sBAAsB,EAAE,CAAC;YACvC,KAAK,MAAM;gBACT,OAAO,IAAI,CAAC,aAAa,EAAE,CAAC;YAC9B,KAAK,QAAQ;gBACX,OAAO,IAAI,CAAC,eAAe,EAAE,CAAC;YAChC;gBACE,OAAO,IAAI,CAAC,mBAAmB,EAAE,CAAC;QACtC,CAAC;IACH,CAAC;IAED;;OAEG;IACK,mBAAmB;QACzB,MAAM,QAAQ,GAAiB,EAAE,CAAC;QAClC,MAAM,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,IAAI,CAAC,CAAC;QAE3D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC,EAAE,EAAE,CAAC;YACxD,MAAM,UAAU,GAAiB,EAAE,CAAC;YAEpC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,EAAE,CAAC,EAAE,EAAE,CAAC;gBACxC,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;gBACvE,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC;YAChD,CAAC;YAED,8BAA8B;YAC9B,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC;YACjD,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACK,sBAAsB;QAC5B,MAAM,QAAQ,GAAiB,EAAE,CAAC;QAElC,mEAAmE;QACnE,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;QACxE,MAAM,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,UAAU,GAAG,GAAG,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;QACjF,MAAM,YAAY,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE,CAAC,GAAG,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC;QAEhF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC,EAAE,EAAE,CAAC;YACxD,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,YAAY,CAAC;YACjD,IAAI,iBAAiB,GAAG,CAAC,CAAC;YAE1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBAChD,iBAAiB,IAAI,eAAe,CAAC,CAAC,CAAC,CAAC;gBACxC,IAAI,iBAAiB,IAAI,WAAW,EAAE,CAAC;oBACrC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;oBAClC,MAAM;gBACR,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACK,aAAa;QACnB,MAAM,QAAQ,GAAiB,EAAE,CAAC;QAElC,6BAA6B;QAC7B,MAAM,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC;QAEpF,qCAAqC;QACrC,MAAM,KAAK,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;QAC5D,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC;QAE7D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC,EAAE,EAAE,CAAC;YACxD,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,SAAS,CAAC;YAC9C,IAAI,cAAc,GAAG,CAAC,CAAC;YAEvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBACtC,cAAc,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC3B,IAAI,cAAc,IAAI,WAAW,EAAE,CAAC;oBAClC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;oBACnC,MAAM;gBACR,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACK,eAAe;QACrB,MAAM,QAAQ,GAAiB,EAAE,CAAC;QAElC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC,EAAE,EAAE,CAAC;YACxD,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;YACvE,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC;QAC9C,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACK,SAAS,CAAC,OAAmB,EAAE,OAAmB,EAAE,OAA4B;QACtF,QAAQ,IAAI,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC;YACxC,KAAK,cAAc;gBACjB,OAAO,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YACrD,KAAK,WAAW;gBACd,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAClD,KAAK,SAAS;gBACZ,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YACjD,KAAK,YAAY;gBACf,OAAO,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YACpD;gBACE,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,OAAmB,EAAE,OAAmB;QACnE,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAExE,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QACzC,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAEzC,MAAM,CAAC,KAAK,GAAG;YACb,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,CAAC;YACzC,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,cAAc,CAAC;SACvC,CAAC;QAEF,MAAM,CAAC,KAAK,GAAG;YACb,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,CAAC;YACzC,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,cAAc,CAAC;SACvC,CAAC;QAEF,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAC1B,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,OAAmB,EAAE,OAAmB;QAChE,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAChE,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAChE,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;QAE1E,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QACzC,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAEzC,MAAM,CAAC,KAAK,GAAG;YACb,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC;YAChC,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC;YAClC,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC;SAC5B,CAAC;QAEF,MAAM,CAAC,KAAK,GAAG;YACb,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC;YAChC,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC;YAClC,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC;SAC5B,CAAC;QAEF,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAC1B,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,OAAmB,EAAE,OAAmB;QAC/D,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QACzC,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAEzC,MAAM,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAC/C,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAClD,CAAC;QAEF,MAAM,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAC/C,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAClD,CAAC;QAEF,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAC1B,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,OAAmB,EAAE,OAAmB;QAClE,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;QAE5B,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QACzC,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAEzC,MAAM,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YAC/C,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,OAAO,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,QAAQ,EAAE,CAAC;gBACzE,OAAO,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,GAAI,OAAO,CAAC,KAAK,CAAC,KAAK,CAAY,CAAC;YACvE,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YAC/C,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,OAAO,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,QAAQ,EAAE,CAAC;gBACzE,OAAO,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,GAAI,OAAO,CAAC,KAAK,CAAC,KAAK,CAAY,CAAC;YACvE,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;QAEH,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAC1B,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,UAAsB,EAAE,OAA4B;QACjE,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QAE7C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC9C,IAAI,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC;gBACjD,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7E,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,UAAU,CAAC,IAAqB,EAAE,QAA8B;QACtE,IAAI,QAAQ,CAAC,cAAc,IAAI,QAAQ,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAClE,uCAAuC;YACvC,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YAC/E,OAAO,QAAQ,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;QAC9C,CAAC;aAAM,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;YACpC,sBAAsB;YACtB,MAAM,GAAG,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;YACtF,MAAM,GAAG,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;YAEtF,QAAQ,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC;gBACvC,KAAK,UAAU;oBACb,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;gBAC/C,KAAK,SAAS;oBACZ,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;gBACxC,KAAK,YAAY;oBACf,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;gBACjD;oBACE,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YACjD,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,KAAa,EAAE,GAAW,EAAE,GAAW;QAC9D,MAAM,KAAK,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,eAAe;QAChD,MAAM,OAAO,GAAG,KAAK,GAAG,IAAI,CAAC,cAAc,EAAE,GAAG,KAAK,CAAC;QACtD,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,GAAW,EAAE,GAAW;QAC9C,OAAO,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,KAAa,EAAE,GAAW,EAAE,GAAW;QAChE,MAAM,GAAG,GAAG,EAAE,CAAC,CAAC,qBAAqB;QACrC,MAAM,MAAM,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;QAC3C,MAAM,MAAM,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;QAC3C,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;QAE1B,IAAI,MAAc,CAAC;QACnB,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC;YACf,MAAM,EAAE,GAAG,GAAG,GAAG,MAAM,CAAC;YACxB,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC;YACpE,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;QAClD,CAAC;aAAM,CAAC;YACN,MAAM,EAAE,GAAG,GAAG,GAAG,MAAM,CAAC;YACxB,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC;YAC5E,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;QAClD,CAAC;QAED,MAAM,OAAO,GAAG,KAAK,GAAG,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;QAC7C,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACK,WAAW,CAAC,SAAuB;QACzC,mCAAmC;QACnC,MAAM,QAAQ,GAAG,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,GAAG,SAAS,CAAC,CAAC;QAEpD,kBAAkB;QAClB,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC;QAE/C,kCAAkC;QAClC,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;IACtE,CAAC;IAED;;OAEG;IACK,oBAAoB;QAC1B,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,OAAO,EAAE,EAAE,CACpD,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAChD,CAAC;QAEF,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;YACvE,IAAI,CAAC,cAAc,GAAG,EAAE,GAAG,IAAI,EAAE,CAAC;QACpC,CAAC;IACH,CAAC;IAED;;OAEG;IACK,iBAAiB;QACvB,8CAA8C;QAC9C,mFAAmF;QACnF,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACjE,CAAC;IAED;;OAEG;IACK,aAAa;QACnB,MAAM,kBAAkB,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACvE,MAAM,SAAS,GAAG,kBAAkB,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAE7D,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC3B,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACnC,CAAC;QAED,MAAM,OAAO,GAAqB;YAChC,SAAS,EAAE,IAAI,CAAC,UAAU;YAC1B,WAAW,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC;YACnC,cAAc,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,SAAS,CAAC,MAAM;YAC3E,YAAY,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC;YACpC,SAAS,EAAE,IAAI,CAAC,kBAAkB,EAAE;YACpC,oBAAoB,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,MAAM;YACzE,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACK,kBAAkB;QACxB,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC;YAAE,OAAO,CAAC,CAAC;QAEzC,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,IAAI,SAAS,GAAG,CAAC,CAAC;QAElB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAChD,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBACpD,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChF,aAAa,IAAI,QAAQ,CAAC;gBAC1B,SAAS,EAAE,CAAC;YACd,CAAC;QACH,CAAC;QAED,OAAO,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;IACvD,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,IAAgB,EAAE,IAAgB;QAC1D,IAAI,QAAQ,GAAG,CAAC,CAAC;QAEjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC3C,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE,CAAC;gBAC3E,MAAM,IAAI,GAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAY,GAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAY,CAAC;gBACnE,QAAQ,IAAI,IAAI,GAAG,IAAI,CAAC;YAC1B,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,OAA4B;QAClD,sBAAsB;QACtB,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC;YACtD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,oBAAoB;QACpB,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,EAAE,EAAE,CAAC;YAC9B,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;YAC9C,MAAM,kBAAkB,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,aAAa,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,WAAW,CAAC;YAE9G,IAAI,IAAI,CAAC,GAAG,CAAC,kBAAkB,CAAC,GAAG,OAAO,CAAC,mBAAmB,CAAC,cAAc,EAAE,CAAC;gBAC9E,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACK,eAAe;QACrB,4CAA4C;QAC5C,MAAM,SAAS,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC5C,MAAM,kBAAkB,GAAG,GAAG,CAAC;QAE/B,IAAI,SAAS,GAAG,kBAAkB,EAAE,CAAC;YACnC,IAAI,CAAC,UAAU,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,UAAU,CAAC,YAAY,GAAG,GAAG,CAAC,CAAC;QACnF,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,UAAU,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,YAAY,GAAG,GAAG,CAAC,CAAC;QACpF,CAAC;IACH,CAAC;IAED;;OAEG;IACK,wBAAwB,CAAC,OAA4B,EAAE,SAAiB;QAC9E,MAAM,aAAa,GAAG,WAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;QAEpD,mDAAmD;QACnD,MAAM,YAAY,GAAyB;YACzC,EAAE,EAAE,IAAI,CAAC,cAAc,EAAE,EAAE,IAAI,aAAa;YAC5C,SAAS,EAAE,EAAE;YACb,eAAe,EAAE,EAAE;YACnB,oBAAoB,EAAE,EAAE;YACxB,QAAQ,EAAE,IAAI,CAAC,cAAc,EAAE,QAAQ,IAAI,KAAK;YAChD,OAAO,EAAE,IAAI,CAAC,cAAc,EAAE,OAAO,IAAI,MAAM,CAAC,SAAS;YACzD,mBAAmB,EAAE,OAAO,CAAC,mBAAmB,EAAE,yCAAyC;YAC3F,kBAAkB,EAAE,EAAgC;SACrD,CAAC;QAEF,oBAAoB;QACpB,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE;gBAC5C,YAAY,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,cAAe,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC1E,CAAC,CAAC,CAAC;YAEH,qBAAqB;YACrB,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,KAAK,EAAE,EAAE;gBACzD,YAAY,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,cAAe,CAAC,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAChG,CAAC,CAAC,CAAC;QACL,CAAC;QAED,MAAM,UAAU,GAA2B;YACzC,eAAe,EAAE,IAAI,CAAC,UAAU;YAChC,gBAAgB,EAAE,IAAI,CAAC,eAAe;YACtC,oBAAoB,EAAE,IAAI,CAAC,UAAU;YACrC,aAAa;YACb,kBAAkB,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC;YACxD,qBAAqB,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,cAAc,CAAC;YAC9D,gBAAgB,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;YACpD,0BAA0B,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,oBAAoB,CAAC;YACzE,sBAAsB,EAAE;gBACtB,cAAc,EAAE,IAAI,CAAC,UAAU,CAAC,cAAc;gBAC9C,iBAAiB,EAAE,IAAI,CAAC,UAAU,CAAC,YAAY;gBAC/C,kBAAkB,EAAE,IAAI,CAAC,UAAU,CAAC,aAAa;aAClD;SACF,CAAC;QAEF,MAAM,mBAAmB,GAAwB;YAC/C,UAAU,EAAE,IAAI,CAAC,OAAO;YACxB,iBAAiB,EAAE,EAAE;YACrB,gBAAgB,EAAE,EAAE;YACpB,kBAAkB,EAAE,EAAE;SACvB,CAAC;QAEF,OAAO;YACL,SAAS,EAAE,OAAO,CAAC,EAAE;YACrB,MAAM,EAAE,4CAAkB,CAAC,SAAS;YACpC,YAAY;YACZ,WAAW,EAAE,EAAE,EAAE,qDAAqD;YACtE,UAAU;YACV,OAAO,EAAE,mBAAmB;YAC5B,QAAQ,EAAE,EAAE;YACZ,eAAe,EAAE,EAAE;YACnB,QAAQ,EAAE,EAAE;YACZ,MAAM,EAAE,EAAE;SACX,CAAC;IACJ,CAAC;IAED,kBAAkB;IACV,WAAW,CAAC,MAAkB;QACpC,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,oBAAoB,EAAE;YAC/B,KAAK,EAAE,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC;YACxB,OAAO,EAAE,CAAC;YACV,eAAe,EAAE,EAAE;YACnB,oBAAoB,EAAE,EAAE;YACxB,QAAQ,EAAE,IAAI;YACd,GAAG,EAAE,CAAC;SACP,CAAC;IACJ,CAAC;IAEO,oBAAoB;QAC1B,OAAO,OAAO,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IACxE,CAAC;IAEO,kBAAkB,CAAC,IAAY;QACrC,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,OAAO,GAAG,EAAE;YACV,KAAK,GAAG,CAAC,KAAK,GAAG,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC;YACxC,OAAO,KAAK,GAAG,MAAM,CAAC;QACxB,CAAC,CAAC;IACJ,CAAC;IAEO,cAAc;QACpB,mDAAmD;QACnD,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;QACzB,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;QACzB,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;IACnE,CAAC;CACF;AAjyBD,4CAiyBC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\algorithms\\GeneticAlgorithm.ts"],
      sourcesContent: ["/**\r\n * Genetic Algorithm Implementation for System Optimization\r\n * \r\n * Implements genetic algorithm optimization with:\r\n * - Multi-objective optimization support (NSGA-II)\r\n * - Configurable selection, crossover, and mutation operators\r\n * - Constraint handling with penalty methods\r\n * - Elitism and diversity preservation\r\n * - Parallel evaluation support\r\n * \r\n * @version 3.0.0\r\n * <AUTHOR> Suite Development Team\r\n */\r\n\r\nimport {\r\n  OptimizationSolution,\r\n  OptimizationVariable,\r\n  OptimizationProblem,\r\n  OptimizationResult,\r\n  OptimizationStatus,\r\n  OptimizationStatistics,\r\n  OptimizationHistory,\r\n  IterationHistory,\r\n  PopulationSnapshot,\r\n  SolutionPerformanceMetrics,\r\n  ObjectiveFunctionType,\r\n  ConstraintFunctionType,\r\n  SelectionFunction,\r\n  CrossoverFunction,\r\n  MutationFunction\r\n} from '../types/SystemOptimizationTypes';\r\n\r\nexport interface GeneticAlgorithmParameters {\r\n  populationSize: number;\r\n  maxGenerations: number;\r\n  crossoverRate: number;\r\n  mutationRate: number;\r\n  eliteSize: number;\r\n  selectionMethod: 'tournament' | 'roulette' | 'rank' | 'random';\r\n  crossoverMethod: 'single_point' | 'two_point' | 'uniform' | 'arithmetic';\r\n  mutationMethod: 'gaussian' | 'uniform' | 'polynomial' | 'adaptive';\r\n  tournamentSize?: number;\r\n  diversityMaintenance: boolean;\r\n  constraintHandling: 'penalty' | 'repair' | 'death_penalty';\r\n  penaltyCoefficient: number;\r\n  adaptiveParameters: boolean;\r\n  parallelEvaluation: boolean;\r\n  seedValue?: number;\r\n}\r\n\r\nexport interface Individual {\r\n  id: string;\r\n  genes: (number | string)[];\r\n  fitness: number;\r\n  objectiveValues: number[];\r\n  constraintViolations: number[];\r\n  feasible: boolean;\r\n  dominationRank?: number;\r\n  crowdingDistance?: number;\r\n  age: number;\r\n}\r\n\r\n/**\r\n * Genetic Algorithm optimizer for single and multi-objective optimization\r\n */\r\nexport class GeneticAlgorithm {\r\n  private parameters: GeneticAlgorithmParameters;\r\n  private population: Individual[];\r\n  private bestIndividual: Individual | null = null;\r\n  private paretoFront: Individual[] = [];\r\n  private generation: number = 0;\r\n  private evaluationCount: number = 0;\r\n  private history: IterationHistory[] = [];\r\n  private random: () => number;\r\n\r\n  constructor(parameters?: Partial<GeneticAlgorithmParameters>) {\r\n    this.parameters = {\r\n      populationSize: 50,\r\n      maxGenerations: 100,\r\n      crossoverRate: 0.8,\r\n      mutationRate: 0.1,\r\n      eliteSize: 2,\r\n      selectionMethod: 'tournament',\r\n      crossoverMethod: 'two_point',\r\n      mutationMethod: 'gaussian',\r\n      tournamentSize: 3,\r\n      diversityMaintenance: true,\r\n      constraintHandling: 'penalty',\r\n      penaltyCoefficient: 1000,\r\n      adaptiveParameters: true,\r\n      parallelEvaluation: false,\r\n      ...parameters\r\n    };\r\n\r\n    // Initialize random number generator\r\n    if (this.parameters.seedValue !== undefined) {\r\n      this.random = this.createSeededRandom(this.parameters.seedValue);\r\n    } else {\r\n      this.random = Math.random;\r\n    }\r\n\r\n    this.population = [];\r\n  }\r\n\r\n  /**\r\n   * Main optimization method\r\n   */\r\n  public async optimize(\r\n    problem: OptimizationProblem,\r\n    objectiveFunction: ObjectiveFunctionType,\r\n    constraintFunctions: ConstraintFunctionType[]\r\n  ): Promise<OptimizationResult> {\r\n    const startTime = performance.now();\r\n    \r\n    try {\r\n      // Initialize algorithm\r\n      this.initializeAlgorithm(problem);\r\n      \r\n      // Create initial population\r\n      await this.createInitialPopulation(problem, objectiveFunction, constraintFunctions);\r\n      \r\n      // Evolution loop\r\n      while (!this.shouldTerminate(problem)) {\r\n        await this.evolveGeneration(problem, objectiveFunction, constraintFunctions);\r\n        this.updateHistory();\r\n        \r\n        if (this.parameters.adaptiveParameters) {\r\n          this.adaptParameters();\r\n        }\r\n      }\r\n      \r\n      // Create final result\r\n      return this.createOptimizationResult(problem, startTime);\r\n      \r\n    } catch (error) {\r\n      console.error('Genetic algorithm optimization failed:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Initialize algorithm state\r\n   */\r\n  private initializeAlgorithm(problem: OptimizationProblem): void {\r\n    this.generation = 0;\r\n    this.evaluationCount = 0;\r\n    this.population = [];\r\n    this.bestIndividual = null;\r\n    this.paretoFront = [];\r\n    this.history = [];\r\n    \r\n    console.log(`Initializing Genetic Algorithm with population size: ${this.parameters.populationSize}`);\r\n  }\r\n\r\n  /**\r\n   * Create initial population\r\n   */\r\n  private async createInitialPopulation(\r\n    problem: OptimizationProblem,\r\n    objectiveFunction: ObjectiveFunctionType,\r\n    constraintFunctions: ConstraintFunctionType[]\r\n  ): Promise<void> {\r\n    this.population = [];\r\n    \r\n    for (let i = 0; i < this.parameters.populationSize; i++) {\r\n      const individual = this.createRandomIndividual(problem);\r\n      await this.evaluateIndividual(individual, problem, objectiveFunction, constraintFunctions);\r\n      this.population.push(individual);\r\n    }\r\n    \r\n    this.updateBestIndividual();\r\n    this.updateParetoFront();\r\n  }\r\n\r\n  /**\r\n   * Create a random individual\r\n   */\r\n  private createRandomIndividual(problem: OptimizationProblem): Individual {\r\n    const genes: (number | string)[] = [];\r\n    \r\n    for (const variable of problem.variables) {\r\n      if (variable.discreteValues && variable.discreteValues.length > 0) {\r\n        // Discrete variable\r\n        const randomIndex = Math.floor(this.random() * variable.discreteValues.length);\r\n        genes.push(variable.discreteValues[randomIndex]);\r\n      } else {\r\n        // Continuous variable\r\n        const min = typeof variable.bounds.minimum === 'number' ? variable.bounds.minimum : 0;\r\n        const max = typeof variable.bounds.maximum === 'number' ? variable.bounds.maximum : 1;\r\n        const value = min + this.random() * (max - min);\r\n        genes.push(value);\r\n      }\r\n    }\r\n    \r\n    return {\r\n      id: this.generateIndividualId(),\r\n      genes,\r\n      fitness: 0,\r\n      objectiveValues: [],\r\n      constraintViolations: [],\r\n      feasible: true,\r\n      age: 0\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Evaluate individual fitness\r\n   */\r\n  private async evaluateIndividual(\r\n    individual: Individual,\r\n    problem: OptimizationProblem,\r\n    objectiveFunction: ObjectiveFunctionType,\r\n    constraintFunctions: ConstraintFunctionType[]\r\n  ): Promise<void> {\r\n    try {\r\n      // Convert genes to optimization variables\r\n      const variables = this.genesToVariables(individual.genes, problem.variables);\r\n      \r\n      // Evaluate objectives\r\n      if (problem.objectives.objectives.length === 1) {\r\n        // Single objective\r\n        const objectiveValue = objectiveFunction(variables);\r\n        individual.objectiveValues = [objectiveValue];\r\n        individual.fitness = objectiveValue;\r\n      } else {\r\n        // Multi-objective - evaluate each objective separately\r\n        individual.objectiveValues = [];\r\n        for (const objective of problem.objectives.objectives) {\r\n          const value = objective.evaluationFunction(variables);\r\n          individual.objectiveValues.push(value);\r\n        }\r\n        // Fitness will be calculated during Pareto ranking\r\n        individual.fitness = 0;\r\n      }\r\n      \r\n      // Evaluate constraints\r\n      individual.constraintViolations = [];\r\n      for (const constraintFunction of constraintFunctions) {\r\n        const violation = constraintFunction(variables);\r\n        individual.constraintViolations.push(violation);\r\n      }\r\n      \r\n      // Check feasibility\r\n      individual.feasible = individual.constraintViolations.every(v => v <= 0);\r\n      \r\n      // Apply constraint handling\r\n      if (!individual.feasible && this.parameters.constraintHandling === 'penalty') {\r\n        const penalty = individual.constraintViolations\r\n          .filter(v => v > 0)\r\n          .reduce((sum, v) => sum + v, 0) * this.parameters.penaltyCoefficient;\r\n        \r\n        if (problem.objectives.objectives.length === 1) {\r\n          individual.fitness += penalty;\r\n        } else {\r\n          // Add penalty to all objectives for multi-objective\r\n          individual.objectiveValues = individual.objectiveValues.map(v => v + penalty);\r\n        }\r\n      }\r\n      \r\n      this.evaluationCount++;\r\n      \r\n    } catch (error) {\r\n      console.error('Error evaluating individual:', error);\r\n      individual.fitness = Number.MAX_VALUE;\r\n      individual.feasible = false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Convert genes to optimization variables\r\n   */\r\n  private genesToVariables(genes: (number | string)[], variableTemplates: OptimizationVariable[]): OptimizationVariable[] {\r\n    return variableTemplates.map((template, index) => ({\r\n      ...template,\r\n      currentValue: genes[index]\r\n    }));\r\n  }\r\n\r\n  /**\r\n   * Evolve one generation\r\n   */\r\n  private async evolveGeneration(\r\n    problem: OptimizationProblem,\r\n    objectiveFunction: ObjectiveFunctionType,\r\n    constraintFunctions: ConstraintFunctionType[]\r\n  ): Promise<void> {\r\n    // Selection\r\n    const parents = this.selection();\r\n    \r\n    // Crossover and mutation\r\n    const offspring: Individual[] = [];\r\n    \r\n    for (let i = 0; i < parents.length; i += 2) {\r\n      const parent1 = parents[i];\r\n      const parent2 = parents[i + 1] || parents[0]; // Handle odd population sizes\r\n      \r\n      let child1 = this.createChild(parent1);\r\n      let child2 = this.createChild(parent2);\r\n      \r\n      // Crossover\r\n      if (this.random() < this.parameters.crossoverRate) {\r\n        [child1, child2] = this.crossover(parent1, parent2, problem);\r\n      }\r\n      \r\n      // Mutation\r\n      if (this.random() < this.parameters.mutationRate) {\r\n        child1 = this.mutate(child1, problem);\r\n      }\r\n      if (this.random() < this.parameters.mutationRate) {\r\n        child2 = this.mutate(child2, problem);\r\n      }\r\n      \r\n      offspring.push(child1, child2);\r\n    }\r\n    \r\n    // Evaluate offspring\r\n    for (const child of offspring) {\r\n      await this.evaluateIndividual(child, problem, objectiveFunction, constraintFunctions);\r\n    }\r\n    \r\n    // Replacement\r\n    this.replacement(offspring);\r\n    \r\n    // Update age\r\n    this.population.forEach(individual => individual.age++);\r\n    \r\n    this.generation++;\r\n    this.updateBestIndividual();\r\n    this.updateParetoFront();\r\n  }\r\n\r\n  /**\r\n   * Selection operator\r\n   */\r\n  private selection(): Individual[] {\r\n    switch (this.parameters.selectionMethod) {\r\n      case 'tournament':\r\n        return this.tournamentSelection();\r\n      case 'roulette':\r\n        return this.rouletteWheelSelection();\r\n      case 'rank':\r\n        return this.rankSelection();\r\n      case 'random':\r\n        return this.randomSelection();\r\n      default:\r\n        return this.tournamentSelection();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Tournament selection\r\n   */\r\n  private tournamentSelection(): Individual[] {\r\n    const selected: Individual[] = [];\r\n    const tournamentSize = this.parameters.tournamentSize || 3;\r\n    \r\n    for (let i = 0; i < this.parameters.populationSize; i++) {\r\n      const tournament: Individual[] = [];\r\n      \r\n      for (let j = 0; j < tournamentSize; j++) {\r\n        const randomIndex = Math.floor(this.random() * this.population.length);\r\n        tournament.push(this.population[randomIndex]);\r\n      }\r\n      \r\n      // Select best from tournament\r\n      tournament.sort((a, b) => a.fitness - b.fitness);\r\n      selected.push(tournament[0]);\r\n    }\r\n    \r\n    return selected;\r\n  }\r\n\r\n  /**\r\n   * Roulette wheel selection\r\n   */\r\n  private rouletteWheelSelection(): Individual[] {\r\n    const selected: Individual[] = [];\r\n    \r\n    // Calculate fitness sum (handle minimization by inverting fitness)\r\n    const maxFitness = Math.max(...this.population.map(ind => ind.fitness));\r\n    const adjustedFitness = this.population.map(ind => maxFitness - ind.fitness + 1);\r\n    const totalFitness = adjustedFitness.reduce((sum, fitness) => sum + fitness, 0);\r\n    \r\n    for (let i = 0; i < this.parameters.populationSize; i++) {\r\n      const randomValue = this.random() * totalFitness;\r\n      let cumulativeFitness = 0;\r\n      \r\n      for (let j = 0; j < this.population.length; j++) {\r\n        cumulativeFitness += adjustedFitness[j];\r\n        if (cumulativeFitness >= randomValue) {\r\n          selected.push(this.population[j]);\r\n          break;\r\n        }\r\n      }\r\n    }\r\n    \r\n    return selected;\r\n  }\r\n\r\n  /**\r\n   * Rank selection\r\n   */\r\n  private rankSelection(): Individual[] {\r\n    const selected: Individual[] = [];\r\n    \r\n    // Sort population by fitness\r\n    const sortedPopulation = [...this.population].sort((a, b) => a.fitness - b.fitness);\r\n    \r\n    // Assign ranks (best = highest rank)\r\n    const ranks = sortedPopulation.map((_, index) => index + 1);\r\n    const totalRank = ranks.reduce((sum, rank) => sum + rank, 0);\r\n    \r\n    for (let i = 0; i < this.parameters.populationSize; i++) {\r\n      const randomValue = this.random() * totalRank;\r\n      let cumulativeRank = 0;\r\n      \r\n      for (let j = 0; j < ranks.length; j++) {\r\n        cumulativeRank += ranks[j];\r\n        if (cumulativeRank >= randomValue) {\r\n          selected.push(sortedPopulation[j]);\r\n          break;\r\n        }\r\n      }\r\n    }\r\n    \r\n    return selected;\r\n  }\r\n\r\n  /**\r\n   * Random selection\r\n   */\r\n  private randomSelection(): Individual[] {\r\n    const selected: Individual[] = [];\r\n    \r\n    for (let i = 0; i < this.parameters.populationSize; i++) {\r\n      const randomIndex = Math.floor(this.random() * this.population.length);\r\n      selected.push(this.population[randomIndex]);\r\n    }\r\n    \r\n    return selected;\r\n  }\r\n\r\n  /**\r\n   * Crossover operator\r\n   */\r\n  private crossover(parent1: Individual, parent2: Individual, problem: OptimizationProblem): [Individual, Individual] {\r\n    switch (this.parameters.crossoverMethod) {\r\n      case 'single_point':\r\n        return this.singlePointCrossover(parent1, parent2);\r\n      case 'two_point':\r\n        return this.twoPointCrossover(parent1, parent2);\r\n      case 'uniform':\r\n        return this.uniformCrossover(parent1, parent2);\r\n      case 'arithmetic':\r\n        return this.arithmeticCrossover(parent1, parent2);\r\n      default:\r\n        return this.twoPointCrossover(parent1, parent2);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Single point crossover\r\n   */\r\n  private singlePointCrossover(parent1: Individual, parent2: Individual): [Individual, Individual] {\r\n    const crossoverPoint = Math.floor(this.random() * parent1.genes.length);\r\n    \r\n    const child1 = this.createChild(parent1);\r\n    const child2 = this.createChild(parent2);\r\n    \r\n    child1.genes = [\r\n      ...parent1.genes.slice(0, crossoverPoint),\r\n      ...parent2.genes.slice(crossoverPoint)\r\n    ];\r\n    \r\n    child2.genes = [\r\n      ...parent2.genes.slice(0, crossoverPoint),\r\n      ...parent1.genes.slice(crossoverPoint)\r\n    ];\r\n    \r\n    return [child1, child2];\r\n  }\r\n\r\n  /**\r\n   * Two point crossover\r\n   */\r\n  private twoPointCrossover(parent1: Individual, parent2: Individual): [Individual, Individual] {\r\n    const point1 = Math.floor(this.random() * parent1.genes.length);\r\n    const point2 = Math.floor(this.random() * parent1.genes.length);\r\n    const [start, end] = [Math.min(point1, point2), Math.max(point1, point2)];\r\n    \r\n    const child1 = this.createChild(parent1);\r\n    const child2 = this.createChild(parent2);\r\n    \r\n    child1.genes = [\r\n      ...parent1.genes.slice(0, start),\r\n      ...parent2.genes.slice(start, end),\r\n      ...parent1.genes.slice(end)\r\n    ];\r\n    \r\n    child2.genes = [\r\n      ...parent2.genes.slice(0, start),\r\n      ...parent1.genes.slice(start, end),\r\n      ...parent2.genes.slice(end)\r\n    ];\r\n    \r\n    return [child1, child2];\r\n  }\r\n\r\n  /**\r\n   * Uniform crossover\r\n   */\r\n  private uniformCrossover(parent1: Individual, parent2: Individual): [Individual, Individual] {\r\n    const child1 = this.createChild(parent1);\r\n    const child2 = this.createChild(parent2);\r\n    \r\n    child1.genes = parent1.genes.map((gene, index) => \r\n      this.random() < 0.5 ? gene : parent2.genes[index]\r\n    );\r\n    \r\n    child2.genes = parent2.genes.map((gene, index) => \r\n      this.random() < 0.5 ? gene : parent1.genes[index]\r\n    );\r\n    \r\n    return [child1, child2];\r\n  }\r\n\r\n  /**\r\n   * Arithmetic crossover (for continuous variables)\r\n   */\r\n  private arithmeticCrossover(parent1: Individual, parent2: Individual): [Individual, Individual] {\r\n    const alpha = this.random();\r\n    \r\n    const child1 = this.createChild(parent1);\r\n    const child2 = this.createChild(parent2);\r\n    \r\n    child1.genes = parent1.genes.map((gene, index) => {\r\n      if (typeof gene === 'number' && typeof parent2.genes[index] === 'number') {\r\n        return alpha * gene + (1 - alpha) * (parent2.genes[index] as number);\r\n      }\r\n      return gene;\r\n    });\r\n    \r\n    child2.genes = parent2.genes.map((gene, index) => {\r\n      if (typeof gene === 'number' && typeof parent1.genes[index] === 'number') {\r\n        return alpha * gene + (1 - alpha) * (parent1.genes[index] as number);\r\n      }\r\n      return gene;\r\n    });\r\n    \r\n    return [child1, child2];\r\n  }\r\n\r\n  /**\r\n   * Mutation operator\r\n   */\r\n  private mutate(individual: Individual, problem: OptimizationProblem): Individual {\r\n    const mutated = this.createChild(individual);\r\n    \r\n    for (let i = 0; i < mutated.genes.length; i++) {\r\n      if (this.random() < this.parameters.mutationRate) {\r\n        mutated.genes[i] = this.mutateGene(mutated.genes[i], problem.variables[i]);\r\n      }\r\n    }\r\n    \r\n    return mutated;\r\n  }\r\n\r\n  /**\r\n   * Mutate a single gene\r\n   */\r\n  private mutateGene(gene: number | string, variable: OptimizationVariable): number | string {\r\n    if (variable.discreteValues && variable.discreteValues.length > 0) {\r\n      // Discrete variable - random selection\r\n      const randomIndex = Math.floor(this.random() * variable.discreteValues.length);\r\n      return variable.discreteValues[randomIndex];\r\n    } else if (typeof gene === 'number') {\r\n      // Continuous variable\r\n      const min = typeof variable.bounds.minimum === 'number' ? variable.bounds.minimum : 0;\r\n      const max = typeof variable.bounds.maximum === 'number' ? variable.bounds.maximum : 1;\r\n      \r\n      switch (this.parameters.mutationMethod) {\r\n        case 'gaussian':\r\n          return this.gaussianMutation(gene, min, max);\r\n        case 'uniform':\r\n          return this.uniformMutation(min, max);\r\n        case 'polynomial':\r\n          return this.polynomialMutation(gene, min, max);\r\n        default:\r\n          return this.gaussianMutation(gene, min, max);\r\n      }\r\n    }\r\n    \r\n    return gene;\r\n  }\r\n\r\n  /**\r\n   * Gaussian mutation\r\n   */\r\n  private gaussianMutation(value: number, min: number, max: number): number {\r\n    const sigma = (max - min) * 0.1; // 10% of range\r\n    const mutated = value + this.gaussianRandom() * sigma;\r\n    return Math.max(min, Math.min(max, mutated));\r\n  }\r\n\r\n  /**\r\n   * Uniform mutation\r\n   */\r\n  private uniformMutation(min: number, max: number): number {\r\n    return min + this.random() * (max - min);\r\n  }\r\n\r\n  /**\r\n   * Polynomial mutation\r\n   */\r\n  private polynomialMutation(value: number, min: number, max: number): number {\r\n    const eta = 20; // Distribution index\r\n    const delta1 = (value - min) / (max - min);\r\n    const delta2 = (max - value) / (max - min);\r\n    const rnd = this.random();\r\n    \r\n    let deltaq: number;\r\n    if (rnd <= 0.5) {\r\n      const xy = 1.0 - delta1;\r\n      const val = 2.0 * rnd + (1.0 - 2.0 * rnd) * Math.pow(xy, eta + 1.0);\r\n      deltaq = Math.pow(val, 1.0 / (eta + 1.0)) - 1.0;\r\n    } else {\r\n      const xy = 1.0 - delta2;\r\n      const val = 2.0 * (1.0 - rnd) + 2.0 * (rnd - 0.5) * Math.pow(xy, eta + 1.0);\r\n      deltaq = 1.0 - Math.pow(val, 1.0 / (eta + 1.0));\r\n    }\r\n    \r\n    const mutated = value + deltaq * (max - min);\r\n    return Math.max(min, Math.min(max, mutated));\r\n  }\r\n\r\n  /**\r\n   * Replacement strategy\r\n   */\r\n  private replacement(offspring: Individual[]): void {\r\n    // Combine population and offspring\r\n    const combined = [...this.population, ...offspring];\r\n    \r\n    // Sort by fitness\r\n    combined.sort((a, b) => a.fitness - b.fitness);\r\n    \r\n    // Keep best individuals (elitism)\r\n    this.population = combined.slice(0, this.parameters.populationSize);\r\n  }\r\n\r\n  /**\r\n   * Update best individual\r\n   */\r\n  private updateBestIndividual(): void {\r\n    const best = this.population.reduce((best, current) => \r\n      current.fitness < best.fitness ? current : best\r\n    );\r\n    \r\n    if (!this.bestIndividual || best.fitness < this.bestIndividual.fitness) {\r\n      this.bestIndividual = { ...best };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update Pareto front for multi-objective optimization\r\n   */\r\n  private updateParetoFront(): void {\r\n    // Implementation for Pareto front calculation\r\n    // This is a simplified version - full NSGA-II implementation would be more complex\r\n    this.paretoFront = this.population.filter(ind => ind.feasible);\r\n  }\r\n\r\n  /**\r\n   * Update optimization history\r\n   */\r\n  private updateHistory(): void {\r\n    const feasiblePopulation = this.population.filter(ind => ind.feasible);\r\n    const fitnesses = feasiblePopulation.map(ind => ind.fitness);\r\n    \r\n    if (fitnesses.length === 0) {\r\n      fitnesses.push(Number.MAX_VALUE);\r\n    }\r\n    \r\n    const history: IterationHistory = {\r\n      iteration: this.generation,\r\n      bestFitness: Math.min(...fitnesses),\r\n      averageFitness: fitnesses.reduce((sum, f) => sum + f, 0) / fitnesses.length,\r\n      worstFitness: Math.max(...fitnesses),\r\n      diversity: this.calculateDiversity(),\r\n      constraintViolations: this.population.filter(ind => !ind.feasible).length,\r\n      timestamp: new Date()\r\n    };\r\n    \r\n    this.history.push(history);\r\n  }\r\n\r\n  /**\r\n   * Calculate population diversity\r\n   */\r\n  private calculateDiversity(): number {\r\n    if (this.population.length < 2) return 0;\r\n    \r\n    let totalDistance = 0;\r\n    let pairCount = 0;\r\n    \r\n    for (let i = 0; i < this.population.length; i++) {\r\n      for (let j = i + 1; j < this.population.length; j++) {\r\n        const distance = this.calculateDistance(this.population[i], this.population[j]);\r\n        totalDistance += distance;\r\n        pairCount++;\r\n      }\r\n    }\r\n    \r\n    return pairCount > 0 ? totalDistance / pairCount : 0;\r\n  }\r\n\r\n  /**\r\n   * Calculate distance between two individuals\r\n   */\r\n  private calculateDistance(ind1: Individual, ind2: Individual): number {\r\n    let distance = 0;\r\n    \r\n    for (let i = 0; i < ind1.genes.length; i++) {\r\n      if (typeof ind1.genes[i] === 'number' && typeof ind2.genes[i] === 'number') {\r\n        const diff = (ind1.genes[i] as number) - (ind2.genes[i] as number);\r\n        distance += diff * diff;\r\n      }\r\n    }\r\n    \r\n    return Math.sqrt(distance);\r\n  }\r\n\r\n  /**\r\n   * Check termination criteria\r\n   */\r\n  private shouldTerminate(problem: OptimizationProblem): boolean {\r\n    // Maximum generations\r\n    if (this.generation >= this.parameters.maxGenerations) {\r\n      return true;\r\n    }\r\n    \r\n    // Convergence check\r\n    if (this.history.length >= 10) {\r\n      const recentHistory = this.history.slice(-10);\r\n      const fitnessImprovement = recentHistory[0].bestFitness - recentHistory[recentHistory.length - 1].bestFitness;\r\n      \r\n      if (Math.abs(fitnessImprovement) < problem.convergenceCriteria.toleranceValue) {\r\n        return true;\r\n      }\r\n    }\r\n    \r\n    return false;\r\n  }\r\n\r\n  /**\r\n   * Adapt algorithm parameters during evolution\r\n   */\r\n  private adaptParameters(): void {\r\n    // Adaptive mutation rate based on diversity\r\n    const diversity = this.calculateDiversity();\r\n    const diversityThreshold = 0.1;\r\n    \r\n    if (diversity < diversityThreshold) {\r\n      this.parameters.mutationRate = Math.min(0.5, this.parameters.mutationRate * 1.1);\r\n    } else {\r\n      this.parameters.mutationRate = Math.max(0.01, this.parameters.mutationRate * 0.9);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Create optimization result\r\n   */\r\n  private createOptimizationResult(problem: OptimizationProblem, startTime: number): OptimizationResult {\r\n    const executionTime = performance.now() - startTime;\r\n    \r\n    // Convert best individual to optimization solution\r\n    const bestSolution: OptimizationSolution = {\r\n      id: this.bestIndividual?.id || 'no_solution',\r\n      variables: {},\r\n      objectiveValues: {},\r\n      constraintViolations: [],\r\n      feasible: this.bestIndividual?.feasible || false,\r\n      fitness: this.bestIndividual?.fitness || Number.MAX_VALUE,\r\n      systemConfiguration: problem.systemConfiguration, // Would be updated with optimized values\r\n      performanceMetrics: {} as SolutionPerformanceMetrics\r\n    };\r\n    \r\n    // Convert variables\r\n    if (this.bestIndividual) {\r\n      problem.variables.forEach((variable, index) => {\r\n        bestSolution.variables[variable.id] = this.bestIndividual!.genes[index];\r\n      });\r\n      \r\n      // Convert objectives\r\n      problem.objectives.objectives.forEach((objective, index) => {\r\n        bestSolution.objectiveValues[objective.id] = this.bestIndividual!.objectiveValues[index] || 0;\r\n      });\r\n    }\r\n    \r\n    const statistics: OptimizationStatistics = {\r\n      totalIterations: this.generation,\r\n      totalEvaluations: this.evaluationCount,\r\n      convergenceIteration: this.generation,\r\n      executionTime,\r\n      bestFitnessHistory: this.history.map(h => h.bestFitness),\r\n      averageFitnessHistory: this.history.map(h => h.averageFitness),\r\n      diversityHistory: this.history.map(h => h.diversity),\r\n      constraintViolationHistory: this.history.map(h => h.constraintViolations),\r\n      algorithmSpecificStats: {\r\n        populationSize: this.parameters.populationSize,\r\n        finalMutationRate: this.parameters.mutationRate,\r\n        finalCrossoverRate: this.parameters.crossoverRate\r\n      }\r\n    };\r\n    \r\n    const optimizationHistory: OptimizationHistory = {\r\n      iterations: this.history,\r\n      populationHistory: [],\r\n      parameterHistory: [],\r\n      convergenceMetrics: []\r\n    };\r\n    \r\n    return {\r\n      problemId: problem.id,\r\n      status: OptimizationStatus.CONVERGED,\r\n      bestSolution,\r\n      paretoFront: [], // Would include Pareto solutions for multi-objective\r\n      statistics,\r\n      history: optimizationHistory,\r\n      analysis: {},\r\n      recommendations: [],\r\n      warnings: [],\r\n      errors: []\r\n    };\r\n  }\r\n\r\n  // Utility methods\r\n  private createChild(parent: Individual): Individual {\r\n    return {\r\n      id: this.generateIndividualId(),\r\n      genes: [...parent.genes],\r\n      fitness: 0,\r\n      objectiveValues: [],\r\n      constraintViolations: [],\r\n      feasible: true,\r\n      age: 0\r\n    };\r\n  }\r\n\r\n  private generateIndividualId(): string {\r\n    return `ind_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\r\n  }\r\n\r\n  private createSeededRandom(seed: number): () => number {\r\n    let state = seed;\r\n    return () => {\r\n      state = (state * 9301 + 49297) % 233280;\r\n      return state / 233280;\r\n    };\r\n  }\r\n\r\n  private gaussianRandom(): number {\r\n    // Box-Muller transform for Gaussian random numbers\r\n    const u1 = this.random();\r\n    const u2 = this.random();\r\n    return Math.sqrt(-2 * Math.log(u1)) * Math.cos(2 * Math.PI * u2);\r\n  }\r\n}\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "52ea60186096b27b8461d8128e5bd1fd2b2018b8"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_by2crk18o = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_by2crk18o();
cov_by2crk18o().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_by2crk18o().s[1]++;
exports.GeneticAlgorithm = void 0;
const SystemOptimizationTypes_1 =
/* istanbul ignore next */
(cov_by2crk18o().s[2]++, require("../types/SystemOptimizationTypes"));
/**
 * Genetic Algorithm optimizer for single and multi-objective optimization
 */
class GeneticAlgorithm {
  constructor(parameters) {
    /* istanbul ignore next */
    cov_by2crk18o().f[0]++;
    cov_by2crk18o().s[3]++;
    this.bestIndividual = null;
    /* istanbul ignore next */
    cov_by2crk18o().s[4]++;
    this.paretoFront = [];
    /* istanbul ignore next */
    cov_by2crk18o().s[5]++;
    this.generation = 0;
    /* istanbul ignore next */
    cov_by2crk18o().s[6]++;
    this.evaluationCount = 0;
    /* istanbul ignore next */
    cov_by2crk18o().s[7]++;
    this.history = [];
    /* istanbul ignore next */
    cov_by2crk18o().s[8]++;
    this.parameters = {
      populationSize: 50,
      maxGenerations: 100,
      crossoverRate: 0.8,
      mutationRate: 0.1,
      eliteSize: 2,
      selectionMethod: 'tournament',
      crossoverMethod: 'two_point',
      mutationMethod: 'gaussian',
      tournamentSize: 3,
      diversityMaintenance: true,
      constraintHandling: 'penalty',
      penaltyCoefficient: 1000,
      adaptiveParameters: true,
      parallelEvaluation: false,
      ...parameters
    };
    // Initialize random number generator
    /* istanbul ignore next */
    cov_by2crk18o().s[9]++;
    if (this.parameters.seedValue !== undefined) {
      /* istanbul ignore next */
      cov_by2crk18o().b[0][0]++;
      cov_by2crk18o().s[10]++;
      this.random = this.createSeededRandom(this.parameters.seedValue);
    } else {
      /* istanbul ignore next */
      cov_by2crk18o().b[0][1]++;
      cov_by2crk18o().s[11]++;
      this.random = Math.random;
    }
    /* istanbul ignore next */
    cov_by2crk18o().s[12]++;
    this.population = [];
  }
  /**
   * Main optimization method
   */
  async optimize(problem, objectiveFunction, constraintFunctions) {
    /* istanbul ignore next */
    cov_by2crk18o().f[1]++;
    const startTime =
    /* istanbul ignore next */
    (cov_by2crk18o().s[13]++, performance.now());
    /* istanbul ignore next */
    cov_by2crk18o().s[14]++;
    try {
      /* istanbul ignore next */
      cov_by2crk18o().s[15]++;
      // Initialize algorithm
      this.initializeAlgorithm(problem);
      // Create initial population
      /* istanbul ignore next */
      cov_by2crk18o().s[16]++;
      await this.createInitialPopulation(problem, objectiveFunction, constraintFunctions);
      // Evolution loop
      /* istanbul ignore next */
      cov_by2crk18o().s[17]++;
      while (!this.shouldTerminate(problem)) {
        /* istanbul ignore next */
        cov_by2crk18o().s[18]++;
        await this.evolveGeneration(problem, objectiveFunction, constraintFunctions);
        /* istanbul ignore next */
        cov_by2crk18o().s[19]++;
        this.updateHistory();
        /* istanbul ignore next */
        cov_by2crk18o().s[20]++;
        if (this.parameters.adaptiveParameters) {
          /* istanbul ignore next */
          cov_by2crk18o().b[1][0]++;
          cov_by2crk18o().s[21]++;
          this.adaptParameters();
        } else
        /* istanbul ignore next */
        {
          cov_by2crk18o().b[1][1]++;
        }
      }
      // Create final result
      /* istanbul ignore next */
      cov_by2crk18o().s[22]++;
      return this.createOptimizationResult(problem, startTime);
    } catch (error) {
      /* istanbul ignore next */
      cov_by2crk18o().s[23]++;
      console.error('Genetic algorithm optimization failed:', error);
      /* istanbul ignore next */
      cov_by2crk18o().s[24]++;
      throw error;
    }
  }
  /**
   * Initialize algorithm state
   */
  initializeAlgorithm(problem) {
    /* istanbul ignore next */
    cov_by2crk18o().f[2]++;
    cov_by2crk18o().s[25]++;
    this.generation = 0;
    /* istanbul ignore next */
    cov_by2crk18o().s[26]++;
    this.evaluationCount = 0;
    /* istanbul ignore next */
    cov_by2crk18o().s[27]++;
    this.population = [];
    /* istanbul ignore next */
    cov_by2crk18o().s[28]++;
    this.bestIndividual = null;
    /* istanbul ignore next */
    cov_by2crk18o().s[29]++;
    this.paretoFront = [];
    /* istanbul ignore next */
    cov_by2crk18o().s[30]++;
    this.history = [];
    /* istanbul ignore next */
    cov_by2crk18o().s[31]++;
    console.log(`Initializing Genetic Algorithm with population size: ${this.parameters.populationSize}`);
  }
  /**
   * Create initial population
   */
  async createInitialPopulation(problem, objectiveFunction, constraintFunctions) {
    /* istanbul ignore next */
    cov_by2crk18o().f[3]++;
    cov_by2crk18o().s[32]++;
    this.population = [];
    /* istanbul ignore next */
    cov_by2crk18o().s[33]++;
    for (let i =
    /* istanbul ignore next */
    (cov_by2crk18o().s[34]++, 0); i < this.parameters.populationSize; i++) {
      const individual =
      /* istanbul ignore next */
      (cov_by2crk18o().s[35]++, this.createRandomIndividual(problem));
      /* istanbul ignore next */
      cov_by2crk18o().s[36]++;
      await this.evaluateIndividual(individual, problem, objectiveFunction, constraintFunctions);
      /* istanbul ignore next */
      cov_by2crk18o().s[37]++;
      this.population.push(individual);
    }
    /* istanbul ignore next */
    cov_by2crk18o().s[38]++;
    this.updateBestIndividual();
    /* istanbul ignore next */
    cov_by2crk18o().s[39]++;
    this.updateParetoFront();
  }
  /**
   * Create a random individual
   */
  createRandomIndividual(problem) {
    /* istanbul ignore next */
    cov_by2crk18o().f[4]++;
    const genes =
    /* istanbul ignore next */
    (cov_by2crk18o().s[40]++, []);
    /* istanbul ignore next */
    cov_by2crk18o().s[41]++;
    for (const variable of problem.variables) {
      /* istanbul ignore next */
      cov_by2crk18o().s[42]++;
      if (
      /* istanbul ignore next */
      (cov_by2crk18o().b[3][0]++, variable.discreteValues) &&
      /* istanbul ignore next */
      (cov_by2crk18o().b[3][1]++, variable.discreteValues.length > 0)) {
        /* istanbul ignore next */
        cov_by2crk18o().b[2][0]++;
        // Discrete variable
        const randomIndex =
        /* istanbul ignore next */
        (cov_by2crk18o().s[43]++, Math.floor(this.random() * variable.discreteValues.length));
        /* istanbul ignore next */
        cov_by2crk18o().s[44]++;
        genes.push(variable.discreteValues[randomIndex]);
      } else {
        /* istanbul ignore next */
        cov_by2crk18o().b[2][1]++;
        // Continuous variable
        const min =
        /* istanbul ignore next */
        (cov_by2crk18o().s[45]++, typeof variable.bounds.minimum === 'number' ?
        /* istanbul ignore next */
        (cov_by2crk18o().b[4][0]++, variable.bounds.minimum) :
        /* istanbul ignore next */
        (cov_by2crk18o().b[4][1]++, 0));
        const max =
        /* istanbul ignore next */
        (cov_by2crk18o().s[46]++, typeof variable.bounds.maximum === 'number' ?
        /* istanbul ignore next */
        (cov_by2crk18o().b[5][0]++, variable.bounds.maximum) :
        /* istanbul ignore next */
        (cov_by2crk18o().b[5][1]++, 1));
        const value =
        /* istanbul ignore next */
        (cov_by2crk18o().s[47]++, min + this.random() * (max - min));
        /* istanbul ignore next */
        cov_by2crk18o().s[48]++;
        genes.push(value);
      }
    }
    /* istanbul ignore next */
    cov_by2crk18o().s[49]++;
    return {
      id: this.generateIndividualId(),
      genes,
      fitness: 0,
      objectiveValues: [],
      constraintViolations: [],
      feasible: true,
      age: 0
    };
  }
  /**
   * Evaluate individual fitness
   */
  async evaluateIndividual(individual, problem, objectiveFunction, constraintFunctions) {
    /* istanbul ignore next */
    cov_by2crk18o().f[5]++;
    cov_by2crk18o().s[50]++;
    try {
      // Convert genes to optimization variables
      const variables =
      /* istanbul ignore next */
      (cov_by2crk18o().s[51]++, this.genesToVariables(individual.genes, problem.variables));
      // Evaluate objectives
      /* istanbul ignore next */
      cov_by2crk18o().s[52]++;
      if (problem.objectives.objectives.length === 1) {
        /* istanbul ignore next */
        cov_by2crk18o().b[6][0]++;
        // Single objective
        const objectiveValue =
        /* istanbul ignore next */
        (cov_by2crk18o().s[53]++, objectiveFunction(variables));
        /* istanbul ignore next */
        cov_by2crk18o().s[54]++;
        individual.objectiveValues = [objectiveValue];
        /* istanbul ignore next */
        cov_by2crk18o().s[55]++;
        individual.fitness = objectiveValue;
      } else {
        /* istanbul ignore next */
        cov_by2crk18o().b[6][1]++;
        cov_by2crk18o().s[56]++;
        // Multi-objective - evaluate each objective separately
        individual.objectiveValues = [];
        /* istanbul ignore next */
        cov_by2crk18o().s[57]++;
        for (const objective of problem.objectives.objectives) {
          const value =
          /* istanbul ignore next */
          (cov_by2crk18o().s[58]++, objective.evaluationFunction(variables));
          /* istanbul ignore next */
          cov_by2crk18o().s[59]++;
          individual.objectiveValues.push(value);
        }
        // Fitness will be calculated during Pareto ranking
        /* istanbul ignore next */
        cov_by2crk18o().s[60]++;
        individual.fitness = 0;
      }
      // Evaluate constraints
      /* istanbul ignore next */
      cov_by2crk18o().s[61]++;
      individual.constraintViolations = [];
      /* istanbul ignore next */
      cov_by2crk18o().s[62]++;
      for (const constraintFunction of constraintFunctions) {
        const violation =
        /* istanbul ignore next */
        (cov_by2crk18o().s[63]++, constraintFunction(variables));
        /* istanbul ignore next */
        cov_by2crk18o().s[64]++;
        individual.constraintViolations.push(violation);
      }
      // Check feasibility
      /* istanbul ignore next */
      cov_by2crk18o().s[65]++;
      individual.feasible = individual.constraintViolations.every(v => {
        /* istanbul ignore next */
        cov_by2crk18o().f[6]++;
        cov_by2crk18o().s[66]++;
        return v <= 0;
      });
      // Apply constraint handling
      /* istanbul ignore next */
      cov_by2crk18o().s[67]++;
      if (
      /* istanbul ignore next */
      (cov_by2crk18o().b[8][0]++, !individual.feasible) &&
      /* istanbul ignore next */
      (cov_by2crk18o().b[8][1]++, this.parameters.constraintHandling === 'penalty')) {
        /* istanbul ignore next */
        cov_by2crk18o().b[7][0]++;
        const penalty =
        /* istanbul ignore next */
        (cov_by2crk18o().s[68]++, individual.constraintViolations.filter(v => {
          /* istanbul ignore next */
          cov_by2crk18o().f[7]++;
          cov_by2crk18o().s[69]++;
          return v > 0;
        }).reduce((sum, v) => {
          /* istanbul ignore next */
          cov_by2crk18o().f[8]++;
          cov_by2crk18o().s[70]++;
          return sum + v;
        }, 0) * this.parameters.penaltyCoefficient);
        /* istanbul ignore next */
        cov_by2crk18o().s[71]++;
        if (problem.objectives.objectives.length === 1) {
          /* istanbul ignore next */
          cov_by2crk18o().b[9][0]++;
          cov_by2crk18o().s[72]++;
          individual.fitness += penalty;
        } else {
          /* istanbul ignore next */
          cov_by2crk18o().b[9][1]++;
          cov_by2crk18o().s[73]++;
          // Add penalty to all objectives for multi-objective
          individual.objectiveValues = individual.objectiveValues.map(v => {
            /* istanbul ignore next */
            cov_by2crk18o().f[9]++;
            cov_by2crk18o().s[74]++;
            return v + penalty;
          });
        }
      } else
      /* istanbul ignore next */
      {
        cov_by2crk18o().b[7][1]++;
      }
      cov_by2crk18o().s[75]++;
      this.evaluationCount++;
    } catch (error) {
      /* istanbul ignore next */
      cov_by2crk18o().s[76]++;
      console.error('Error evaluating individual:', error);
      /* istanbul ignore next */
      cov_by2crk18o().s[77]++;
      individual.fitness = Number.MAX_VALUE;
      /* istanbul ignore next */
      cov_by2crk18o().s[78]++;
      individual.feasible = false;
    }
  }
  /**
   * Convert genes to optimization variables
   */
  genesToVariables(genes, variableTemplates) {
    /* istanbul ignore next */
    cov_by2crk18o().f[10]++;
    cov_by2crk18o().s[79]++;
    return variableTemplates.map((template, index) => {
      /* istanbul ignore next */
      cov_by2crk18o().f[11]++;
      cov_by2crk18o().s[80]++;
      return {
        ...template,
        currentValue: genes[index]
      };
    });
  }
  /**
   * Evolve one generation
   */
  async evolveGeneration(problem, objectiveFunction, constraintFunctions) {
    /* istanbul ignore next */
    cov_by2crk18o().f[12]++;
    // Selection
    const parents =
    /* istanbul ignore next */
    (cov_by2crk18o().s[81]++, this.selection());
    // Crossover and mutation
    const offspring =
    /* istanbul ignore next */
    (cov_by2crk18o().s[82]++, []);
    /* istanbul ignore next */
    cov_by2crk18o().s[83]++;
    for (let i =
    /* istanbul ignore next */
    (cov_by2crk18o().s[84]++, 0); i < parents.length; i += 2) {
      const parent1 =
      /* istanbul ignore next */
      (cov_by2crk18o().s[85]++, parents[i]);
      const parent2 =
      /* istanbul ignore next */
      (cov_by2crk18o().s[86]++,
      /* istanbul ignore next */
      (cov_by2crk18o().b[10][0]++, parents[i + 1]) ||
      /* istanbul ignore next */
      (cov_by2crk18o().b[10][1]++, parents[0])); // Handle odd population sizes
      let child1 =
      /* istanbul ignore next */
      (cov_by2crk18o().s[87]++, this.createChild(parent1));
      let child2 =
      /* istanbul ignore next */
      (cov_by2crk18o().s[88]++, this.createChild(parent2));
      // Crossover
      /* istanbul ignore next */
      cov_by2crk18o().s[89]++;
      if (this.random() < this.parameters.crossoverRate) {
        /* istanbul ignore next */
        cov_by2crk18o().b[11][0]++;
        cov_by2crk18o().s[90]++;
        [child1, child2] = this.crossover(parent1, parent2, problem);
      } else
      /* istanbul ignore next */
      {
        cov_by2crk18o().b[11][1]++;
      }
      // Mutation
      cov_by2crk18o().s[91]++;
      if (this.random() < this.parameters.mutationRate) {
        /* istanbul ignore next */
        cov_by2crk18o().b[12][0]++;
        cov_by2crk18o().s[92]++;
        child1 = this.mutate(child1, problem);
      } else
      /* istanbul ignore next */
      {
        cov_by2crk18o().b[12][1]++;
      }
      cov_by2crk18o().s[93]++;
      if (this.random() < this.parameters.mutationRate) {
        /* istanbul ignore next */
        cov_by2crk18o().b[13][0]++;
        cov_by2crk18o().s[94]++;
        child2 = this.mutate(child2, problem);
      } else
      /* istanbul ignore next */
      {
        cov_by2crk18o().b[13][1]++;
      }
      cov_by2crk18o().s[95]++;
      offspring.push(child1, child2);
    }
    // Evaluate offspring
    /* istanbul ignore next */
    cov_by2crk18o().s[96]++;
    for (const child of offspring) {
      /* istanbul ignore next */
      cov_by2crk18o().s[97]++;
      await this.evaluateIndividual(child, problem, objectiveFunction, constraintFunctions);
    }
    // Replacement
    /* istanbul ignore next */
    cov_by2crk18o().s[98]++;
    this.replacement(offspring);
    // Update age
    /* istanbul ignore next */
    cov_by2crk18o().s[99]++;
    this.population.forEach(individual => {
      /* istanbul ignore next */
      cov_by2crk18o().f[13]++;
      cov_by2crk18o().s[100]++;
      return individual.age++;
    });
    /* istanbul ignore next */
    cov_by2crk18o().s[101]++;
    this.generation++;
    /* istanbul ignore next */
    cov_by2crk18o().s[102]++;
    this.updateBestIndividual();
    /* istanbul ignore next */
    cov_by2crk18o().s[103]++;
    this.updateParetoFront();
  }
  /**
   * Selection operator
   */
  selection() {
    /* istanbul ignore next */
    cov_by2crk18o().f[14]++;
    cov_by2crk18o().s[104]++;
    switch (this.parameters.selectionMethod) {
      case 'tournament':
        /* istanbul ignore next */
        cov_by2crk18o().b[14][0]++;
        cov_by2crk18o().s[105]++;
        return this.tournamentSelection();
      case 'roulette':
        /* istanbul ignore next */
        cov_by2crk18o().b[14][1]++;
        cov_by2crk18o().s[106]++;
        return this.rouletteWheelSelection();
      case 'rank':
        /* istanbul ignore next */
        cov_by2crk18o().b[14][2]++;
        cov_by2crk18o().s[107]++;
        return this.rankSelection();
      case 'random':
        /* istanbul ignore next */
        cov_by2crk18o().b[14][3]++;
        cov_by2crk18o().s[108]++;
        return this.randomSelection();
      default:
        /* istanbul ignore next */
        cov_by2crk18o().b[14][4]++;
        cov_by2crk18o().s[109]++;
        return this.tournamentSelection();
    }
  }
  /**
   * Tournament selection
   */
  tournamentSelection() {
    /* istanbul ignore next */
    cov_by2crk18o().f[15]++;
    const selected =
    /* istanbul ignore next */
    (cov_by2crk18o().s[110]++, []);
    const tournamentSize =
    /* istanbul ignore next */
    (cov_by2crk18o().s[111]++,
    /* istanbul ignore next */
    (cov_by2crk18o().b[15][0]++, this.parameters.tournamentSize) ||
    /* istanbul ignore next */
    (cov_by2crk18o().b[15][1]++, 3));
    /* istanbul ignore next */
    cov_by2crk18o().s[112]++;
    for (let i =
    /* istanbul ignore next */
    (cov_by2crk18o().s[113]++, 0); i < this.parameters.populationSize; i++) {
      const tournament =
      /* istanbul ignore next */
      (cov_by2crk18o().s[114]++, []);
      /* istanbul ignore next */
      cov_by2crk18o().s[115]++;
      for (let j =
      /* istanbul ignore next */
      (cov_by2crk18o().s[116]++, 0); j < tournamentSize; j++) {
        const randomIndex =
        /* istanbul ignore next */
        (cov_by2crk18o().s[117]++, Math.floor(this.random() * this.population.length));
        /* istanbul ignore next */
        cov_by2crk18o().s[118]++;
        tournament.push(this.population[randomIndex]);
      }
      // Select best from tournament
      /* istanbul ignore next */
      cov_by2crk18o().s[119]++;
      tournament.sort((a, b) => {
        /* istanbul ignore next */
        cov_by2crk18o().f[16]++;
        cov_by2crk18o().s[120]++;
        return a.fitness - b.fitness;
      });
      /* istanbul ignore next */
      cov_by2crk18o().s[121]++;
      selected.push(tournament[0]);
    }
    /* istanbul ignore next */
    cov_by2crk18o().s[122]++;
    return selected;
  }
  /**
   * Roulette wheel selection
   */
  rouletteWheelSelection() {
    /* istanbul ignore next */
    cov_by2crk18o().f[17]++;
    const selected =
    /* istanbul ignore next */
    (cov_by2crk18o().s[123]++, []);
    // Calculate fitness sum (handle minimization by inverting fitness)
    const maxFitness =
    /* istanbul ignore next */
    (cov_by2crk18o().s[124]++, Math.max(...this.population.map(ind => {
      /* istanbul ignore next */
      cov_by2crk18o().f[18]++;
      cov_by2crk18o().s[125]++;
      return ind.fitness;
    })));
    const adjustedFitness =
    /* istanbul ignore next */
    (cov_by2crk18o().s[126]++, this.population.map(ind => {
      /* istanbul ignore next */
      cov_by2crk18o().f[19]++;
      cov_by2crk18o().s[127]++;
      return maxFitness - ind.fitness + 1;
    }));
    const totalFitness =
    /* istanbul ignore next */
    (cov_by2crk18o().s[128]++, adjustedFitness.reduce((sum, fitness) => {
      /* istanbul ignore next */
      cov_by2crk18o().f[20]++;
      cov_by2crk18o().s[129]++;
      return sum + fitness;
    }, 0));
    /* istanbul ignore next */
    cov_by2crk18o().s[130]++;
    for (let i =
    /* istanbul ignore next */
    (cov_by2crk18o().s[131]++, 0); i < this.parameters.populationSize; i++) {
      const randomValue =
      /* istanbul ignore next */
      (cov_by2crk18o().s[132]++, this.random() * totalFitness);
      let cumulativeFitness =
      /* istanbul ignore next */
      (cov_by2crk18o().s[133]++, 0);
      /* istanbul ignore next */
      cov_by2crk18o().s[134]++;
      for (let j =
      /* istanbul ignore next */
      (cov_by2crk18o().s[135]++, 0); j < this.population.length; j++) {
        /* istanbul ignore next */
        cov_by2crk18o().s[136]++;
        cumulativeFitness += adjustedFitness[j];
        /* istanbul ignore next */
        cov_by2crk18o().s[137]++;
        if (cumulativeFitness >= randomValue) {
          /* istanbul ignore next */
          cov_by2crk18o().b[16][0]++;
          cov_by2crk18o().s[138]++;
          selected.push(this.population[j]);
          /* istanbul ignore next */
          cov_by2crk18o().s[139]++;
          break;
        } else
        /* istanbul ignore next */
        {
          cov_by2crk18o().b[16][1]++;
        }
      }
    }
    /* istanbul ignore next */
    cov_by2crk18o().s[140]++;
    return selected;
  }
  /**
   * Rank selection
   */
  rankSelection() {
    /* istanbul ignore next */
    cov_by2crk18o().f[21]++;
    const selected =
    /* istanbul ignore next */
    (cov_by2crk18o().s[141]++, []);
    // Sort population by fitness
    const sortedPopulation =
    /* istanbul ignore next */
    (cov_by2crk18o().s[142]++, [...this.population].sort((a, b) => {
      /* istanbul ignore next */
      cov_by2crk18o().f[22]++;
      cov_by2crk18o().s[143]++;
      return a.fitness - b.fitness;
    }));
    // Assign ranks (best = highest rank)
    const ranks =
    /* istanbul ignore next */
    (cov_by2crk18o().s[144]++, sortedPopulation.map((_, index) => {
      /* istanbul ignore next */
      cov_by2crk18o().f[23]++;
      cov_by2crk18o().s[145]++;
      return index + 1;
    }));
    const totalRank =
    /* istanbul ignore next */
    (cov_by2crk18o().s[146]++, ranks.reduce((sum, rank) => {
      /* istanbul ignore next */
      cov_by2crk18o().f[24]++;
      cov_by2crk18o().s[147]++;
      return sum + rank;
    }, 0));
    /* istanbul ignore next */
    cov_by2crk18o().s[148]++;
    for (let i =
    /* istanbul ignore next */
    (cov_by2crk18o().s[149]++, 0); i < this.parameters.populationSize; i++) {
      const randomValue =
      /* istanbul ignore next */
      (cov_by2crk18o().s[150]++, this.random() * totalRank);
      let cumulativeRank =
      /* istanbul ignore next */
      (cov_by2crk18o().s[151]++, 0);
      /* istanbul ignore next */
      cov_by2crk18o().s[152]++;
      for (let j =
      /* istanbul ignore next */
      (cov_by2crk18o().s[153]++, 0); j < ranks.length; j++) {
        /* istanbul ignore next */
        cov_by2crk18o().s[154]++;
        cumulativeRank += ranks[j];
        /* istanbul ignore next */
        cov_by2crk18o().s[155]++;
        if (cumulativeRank >= randomValue) {
          /* istanbul ignore next */
          cov_by2crk18o().b[17][0]++;
          cov_by2crk18o().s[156]++;
          selected.push(sortedPopulation[j]);
          /* istanbul ignore next */
          cov_by2crk18o().s[157]++;
          break;
        } else
        /* istanbul ignore next */
        {
          cov_by2crk18o().b[17][1]++;
        }
      }
    }
    /* istanbul ignore next */
    cov_by2crk18o().s[158]++;
    return selected;
  }
  /**
   * Random selection
   */
  randomSelection() {
    /* istanbul ignore next */
    cov_by2crk18o().f[25]++;
    const selected =
    /* istanbul ignore next */
    (cov_by2crk18o().s[159]++, []);
    /* istanbul ignore next */
    cov_by2crk18o().s[160]++;
    for (let i =
    /* istanbul ignore next */
    (cov_by2crk18o().s[161]++, 0); i < this.parameters.populationSize; i++) {
      const randomIndex =
      /* istanbul ignore next */
      (cov_by2crk18o().s[162]++, Math.floor(this.random() * this.population.length));
      /* istanbul ignore next */
      cov_by2crk18o().s[163]++;
      selected.push(this.population[randomIndex]);
    }
    /* istanbul ignore next */
    cov_by2crk18o().s[164]++;
    return selected;
  }
  /**
   * Crossover operator
   */
  crossover(parent1, parent2, problem) {
    /* istanbul ignore next */
    cov_by2crk18o().f[26]++;
    cov_by2crk18o().s[165]++;
    switch (this.parameters.crossoverMethod) {
      case 'single_point':
        /* istanbul ignore next */
        cov_by2crk18o().b[18][0]++;
        cov_by2crk18o().s[166]++;
        return this.singlePointCrossover(parent1, parent2);
      case 'two_point':
        /* istanbul ignore next */
        cov_by2crk18o().b[18][1]++;
        cov_by2crk18o().s[167]++;
        return this.twoPointCrossover(parent1, parent2);
      case 'uniform':
        /* istanbul ignore next */
        cov_by2crk18o().b[18][2]++;
        cov_by2crk18o().s[168]++;
        return this.uniformCrossover(parent1, parent2);
      case 'arithmetic':
        /* istanbul ignore next */
        cov_by2crk18o().b[18][3]++;
        cov_by2crk18o().s[169]++;
        return this.arithmeticCrossover(parent1, parent2);
      default:
        /* istanbul ignore next */
        cov_by2crk18o().b[18][4]++;
        cov_by2crk18o().s[170]++;
        return this.twoPointCrossover(parent1, parent2);
    }
  }
  /**
   * Single point crossover
   */
  singlePointCrossover(parent1, parent2) {
    /* istanbul ignore next */
    cov_by2crk18o().f[27]++;
    const crossoverPoint =
    /* istanbul ignore next */
    (cov_by2crk18o().s[171]++, Math.floor(this.random() * parent1.genes.length));
    const child1 =
    /* istanbul ignore next */
    (cov_by2crk18o().s[172]++, this.createChild(parent1));
    const child2 =
    /* istanbul ignore next */
    (cov_by2crk18o().s[173]++, this.createChild(parent2));
    /* istanbul ignore next */
    cov_by2crk18o().s[174]++;
    child1.genes = [...parent1.genes.slice(0, crossoverPoint), ...parent2.genes.slice(crossoverPoint)];
    /* istanbul ignore next */
    cov_by2crk18o().s[175]++;
    child2.genes = [...parent2.genes.slice(0, crossoverPoint), ...parent1.genes.slice(crossoverPoint)];
    /* istanbul ignore next */
    cov_by2crk18o().s[176]++;
    return [child1, child2];
  }
  /**
   * Two point crossover
   */
  twoPointCrossover(parent1, parent2) {
    /* istanbul ignore next */
    cov_by2crk18o().f[28]++;
    const point1 =
    /* istanbul ignore next */
    (cov_by2crk18o().s[177]++, Math.floor(this.random() * parent1.genes.length));
    const point2 =
    /* istanbul ignore next */
    (cov_by2crk18o().s[178]++, Math.floor(this.random() * parent1.genes.length));
    const [start, end] =
    /* istanbul ignore next */
    (cov_by2crk18o().s[179]++, [Math.min(point1, point2), Math.max(point1, point2)]);
    const child1 =
    /* istanbul ignore next */
    (cov_by2crk18o().s[180]++, this.createChild(parent1));
    const child2 =
    /* istanbul ignore next */
    (cov_by2crk18o().s[181]++, this.createChild(parent2));
    /* istanbul ignore next */
    cov_by2crk18o().s[182]++;
    child1.genes = [...parent1.genes.slice(0, start), ...parent2.genes.slice(start, end), ...parent1.genes.slice(end)];
    /* istanbul ignore next */
    cov_by2crk18o().s[183]++;
    child2.genes = [...parent2.genes.slice(0, start), ...parent1.genes.slice(start, end), ...parent2.genes.slice(end)];
    /* istanbul ignore next */
    cov_by2crk18o().s[184]++;
    return [child1, child2];
  }
  /**
   * Uniform crossover
   */
  uniformCrossover(parent1, parent2) {
    /* istanbul ignore next */
    cov_by2crk18o().f[29]++;
    const child1 =
    /* istanbul ignore next */
    (cov_by2crk18o().s[185]++, this.createChild(parent1));
    const child2 =
    /* istanbul ignore next */
    (cov_by2crk18o().s[186]++, this.createChild(parent2));
    /* istanbul ignore next */
    cov_by2crk18o().s[187]++;
    child1.genes = parent1.genes.map((gene, index) => {
      /* istanbul ignore next */
      cov_by2crk18o().f[30]++;
      cov_by2crk18o().s[188]++;
      return this.random() < 0.5 ?
      /* istanbul ignore next */
      (cov_by2crk18o().b[19][0]++, gene) :
      /* istanbul ignore next */
      (cov_by2crk18o().b[19][1]++, parent2.genes[index]);
    });
    /* istanbul ignore next */
    cov_by2crk18o().s[189]++;
    child2.genes = parent2.genes.map((gene, index) => {
      /* istanbul ignore next */
      cov_by2crk18o().f[31]++;
      cov_by2crk18o().s[190]++;
      return this.random() < 0.5 ?
      /* istanbul ignore next */
      (cov_by2crk18o().b[20][0]++, gene) :
      /* istanbul ignore next */
      (cov_by2crk18o().b[20][1]++, parent1.genes[index]);
    });
    /* istanbul ignore next */
    cov_by2crk18o().s[191]++;
    return [child1, child2];
  }
  /**
   * Arithmetic crossover (for continuous variables)
   */
  arithmeticCrossover(parent1, parent2) {
    /* istanbul ignore next */
    cov_by2crk18o().f[32]++;
    const alpha =
    /* istanbul ignore next */
    (cov_by2crk18o().s[192]++, this.random());
    const child1 =
    /* istanbul ignore next */
    (cov_by2crk18o().s[193]++, this.createChild(parent1));
    const child2 =
    /* istanbul ignore next */
    (cov_by2crk18o().s[194]++, this.createChild(parent2));
    /* istanbul ignore next */
    cov_by2crk18o().s[195]++;
    child1.genes = parent1.genes.map((gene, index) => {
      /* istanbul ignore next */
      cov_by2crk18o().f[33]++;
      cov_by2crk18o().s[196]++;
      if (
      /* istanbul ignore next */
      (cov_by2crk18o().b[22][0]++, typeof gene === 'number') &&
      /* istanbul ignore next */
      (cov_by2crk18o().b[22][1]++, typeof parent2.genes[index] === 'number')) {
        /* istanbul ignore next */
        cov_by2crk18o().b[21][0]++;
        cov_by2crk18o().s[197]++;
        return alpha * gene + (1 - alpha) * parent2.genes[index];
      } else
      /* istanbul ignore next */
      {
        cov_by2crk18o().b[21][1]++;
      }
      cov_by2crk18o().s[198]++;
      return gene;
    });
    /* istanbul ignore next */
    cov_by2crk18o().s[199]++;
    child2.genes = parent2.genes.map((gene, index) => {
      /* istanbul ignore next */
      cov_by2crk18o().f[34]++;
      cov_by2crk18o().s[200]++;
      if (
      /* istanbul ignore next */
      (cov_by2crk18o().b[24][0]++, typeof gene === 'number') &&
      /* istanbul ignore next */
      (cov_by2crk18o().b[24][1]++, typeof parent1.genes[index] === 'number')) {
        /* istanbul ignore next */
        cov_by2crk18o().b[23][0]++;
        cov_by2crk18o().s[201]++;
        return alpha * gene + (1 - alpha) * parent1.genes[index];
      } else
      /* istanbul ignore next */
      {
        cov_by2crk18o().b[23][1]++;
      }
      cov_by2crk18o().s[202]++;
      return gene;
    });
    /* istanbul ignore next */
    cov_by2crk18o().s[203]++;
    return [child1, child2];
  }
  /**
   * Mutation operator
   */
  mutate(individual, problem) {
    /* istanbul ignore next */
    cov_by2crk18o().f[35]++;
    const mutated =
    /* istanbul ignore next */
    (cov_by2crk18o().s[204]++, this.createChild(individual));
    /* istanbul ignore next */
    cov_by2crk18o().s[205]++;
    for (let i =
    /* istanbul ignore next */
    (cov_by2crk18o().s[206]++, 0); i < mutated.genes.length; i++) {
      /* istanbul ignore next */
      cov_by2crk18o().s[207]++;
      if (this.random() < this.parameters.mutationRate) {
        /* istanbul ignore next */
        cov_by2crk18o().b[25][0]++;
        cov_by2crk18o().s[208]++;
        mutated.genes[i] = this.mutateGene(mutated.genes[i], problem.variables[i]);
      } else
      /* istanbul ignore next */
      {
        cov_by2crk18o().b[25][1]++;
      }
    }
    /* istanbul ignore next */
    cov_by2crk18o().s[209]++;
    return mutated;
  }
  /**
   * Mutate a single gene
   */
  mutateGene(gene, variable) {
    /* istanbul ignore next */
    cov_by2crk18o().f[36]++;
    cov_by2crk18o().s[210]++;
    if (
    /* istanbul ignore next */
    (cov_by2crk18o().b[27][0]++, variable.discreteValues) &&
    /* istanbul ignore next */
    (cov_by2crk18o().b[27][1]++, variable.discreteValues.length > 0)) {
      /* istanbul ignore next */
      cov_by2crk18o().b[26][0]++;
      // Discrete variable - random selection
      const randomIndex =
      /* istanbul ignore next */
      (cov_by2crk18o().s[211]++, Math.floor(this.random() * variable.discreteValues.length));
      /* istanbul ignore next */
      cov_by2crk18o().s[212]++;
      return variable.discreteValues[randomIndex];
    } else {
      /* istanbul ignore next */
      cov_by2crk18o().b[26][1]++;
      cov_by2crk18o().s[213]++;
      if (typeof gene === 'number') {
        /* istanbul ignore next */
        cov_by2crk18o().b[28][0]++;
        // Continuous variable
        const min =
        /* istanbul ignore next */
        (cov_by2crk18o().s[214]++, typeof variable.bounds.minimum === 'number' ?
        /* istanbul ignore next */
        (cov_by2crk18o().b[29][0]++, variable.bounds.minimum) :
        /* istanbul ignore next */
        (cov_by2crk18o().b[29][1]++, 0));
        const max =
        /* istanbul ignore next */
        (cov_by2crk18o().s[215]++, typeof variable.bounds.maximum === 'number' ?
        /* istanbul ignore next */
        (cov_by2crk18o().b[30][0]++, variable.bounds.maximum) :
        /* istanbul ignore next */
        (cov_by2crk18o().b[30][1]++, 1));
        /* istanbul ignore next */
        cov_by2crk18o().s[216]++;
        switch (this.parameters.mutationMethod) {
          case 'gaussian':
            /* istanbul ignore next */
            cov_by2crk18o().b[31][0]++;
            cov_by2crk18o().s[217]++;
            return this.gaussianMutation(gene, min, max);
          case 'uniform':
            /* istanbul ignore next */
            cov_by2crk18o().b[31][1]++;
            cov_by2crk18o().s[218]++;
            return this.uniformMutation(min, max);
          case 'polynomial':
            /* istanbul ignore next */
            cov_by2crk18o().b[31][2]++;
            cov_by2crk18o().s[219]++;
            return this.polynomialMutation(gene, min, max);
          default:
            /* istanbul ignore next */
            cov_by2crk18o().b[31][3]++;
            cov_by2crk18o().s[220]++;
            return this.gaussianMutation(gene, min, max);
        }
      } else
      /* istanbul ignore next */
      {
        cov_by2crk18o().b[28][1]++;
      }
    }
    /* istanbul ignore next */
    cov_by2crk18o().s[221]++;
    return gene;
  }
  /**
   * Gaussian mutation
   */
  gaussianMutation(value, min, max) {
    /* istanbul ignore next */
    cov_by2crk18o().f[37]++;
    const sigma =
    /* istanbul ignore next */
    (cov_by2crk18o().s[222]++, (max - min) * 0.1); // 10% of range
    const mutated =
    /* istanbul ignore next */
    (cov_by2crk18o().s[223]++, value + this.gaussianRandom() * sigma);
    /* istanbul ignore next */
    cov_by2crk18o().s[224]++;
    return Math.max(min, Math.min(max, mutated));
  }
  /**
   * Uniform mutation
   */
  uniformMutation(min, max) {
    /* istanbul ignore next */
    cov_by2crk18o().f[38]++;
    cov_by2crk18o().s[225]++;
    return min + this.random() * (max - min);
  }
  /**
   * Polynomial mutation
   */
  polynomialMutation(value, min, max) {
    /* istanbul ignore next */
    cov_by2crk18o().f[39]++;
    const eta =
    /* istanbul ignore next */
    (cov_by2crk18o().s[226]++, 20); // Distribution index
    const delta1 =
    /* istanbul ignore next */
    (cov_by2crk18o().s[227]++, (value - min) / (max - min));
    const delta2 =
    /* istanbul ignore next */
    (cov_by2crk18o().s[228]++, (max - value) / (max - min));
    const rnd =
    /* istanbul ignore next */
    (cov_by2crk18o().s[229]++, this.random());
    let deltaq;
    /* istanbul ignore next */
    cov_by2crk18o().s[230]++;
    if (rnd <= 0.5) {
      /* istanbul ignore next */
      cov_by2crk18o().b[32][0]++;
      const xy =
      /* istanbul ignore next */
      (cov_by2crk18o().s[231]++, 1.0 - delta1);
      const val =
      /* istanbul ignore next */
      (cov_by2crk18o().s[232]++, 2.0 * rnd + (1.0 - 2.0 * rnd) * Math.pow(xy, eta + 1.0));
      /* istanbul ignore next */
      cov_by2crk18o().s[233]++;
      deltaq = Math.pow(val, 1.0 / (eta + 1.0)) - 1.0;
    } else {
      /* istanbul ignore next */
      cov_by2crk18o().b[32][1]++;
      const xy =
      /* istanbul ignore next */
      (cov_by2crk18o().s[234]++, 1.0 - delta2);
      const val =
      /* istanbul ignore next */
      (cov_by2crk18o().s[235]++, 2.0 * (1.0 - rnd) + 2.0 * (rnd - 0.5) * Math.pow(xy, eta + 1.0));
      /* istanbul ignore next */
      cov_by2crk18o().s[236]++;
      deltaq = 1.0 - Math.pow(val, 1.0 / (eta + 1.0));
    }
    const mutated =
    /* istanbul ignore next */
    (cov_by2crk18o().s[237]++, value + deltaq * (max - min));
    /* istanbul ignore next */
    cov_by2crk18o().s[238]++;
    return Math.max(min, Math.min(max, mutated));
  }
  /**
   * Replacement strategy
   */
  replacement(offspring) {
    /* istanbul ignore next */
    cov_by2crk18o().f[40]++;
    // Combine population and offspring
    const combined =
    /* istanbul ignore next */
    (cov_by2crk18o().s[239]++, [...this.population, ...offspring]);
    // Sort by fitness
    /* istanbul ignore next */
    cov_by2crk18o().s[240]++;
    combined.sort((a, b) => {
      /* istanbul ignore next */
      cov_by2crk18o().f[41]++;
      cov_by2crk18o().s[241]++;
      return a.fitness - b.fitness;
    });
    // Keep best individuals (elitism)
    /* istanbul ignore next */
    cov_by2crk18o().s[242]++;
    this.population = combined.slice(0, this.parameters.populationSize);
  }
  /**
   * Update best individual
   */
  updateBestIndividual() {
    /* istanbul ignore next */
    cov_by2crk18o().f[42]++;
    const best =
    /* istanbul ignore next */
    (cov_by2crk18o().s[243]++, this.population.reduce((best, current) => {
      /* istanbul ignore next */
      cov_by2crk18o().f[43]++;
      cov_by2crk18o().s[244]++;
      return current.fitness < best.fitness ?
      /* istanbul ignore next */
      (cov_by2crk18o().b[33][0]++, current) :
      /* istanbul ignore next */
      (cov_by2crk18o().b[33][1]++, best);
    }));
    /* istanbul ignore next */
    cov_by2crk18o().s[245]++;
    if (
    /* istanbul ignore next */
    (cov_by2crk18o().b[35][0]++, !this.bestIndividual) ||
    /* istanbul ignore next */
    (cov_by2crk18o().b[35][1]++, best.fitness < this.bestIndividual.fitness)) {
      /* istanbul ignore next */
      cov_by2crk18o().b[34][0]++;
      cov_by2crk18o().s[246]++;
      this.bestIndividual = {
        ...best
      };
    } else
    /* istanbul ignore next */
    {
      cov_by2crk18o().b[34][1]++;
    }
  }
  /**
   * Update Pareto front for multi-objective optimization
   */
  updateParetoFront() {
    /* istanbul ignore next */
    cov_by2crk18o().f[44]++;
    cov_by2crk18o().s[247]++;
    // Implementation for Pareto front calculation
    // This is a simplified version - full NSGA-II implementation would be more complex
    this.paretoFront = this.population.filter(ind => {
      /* istanbul ignore next */
      cov_by2crk18o().f[45]++;
      cov_by2crk18o().s[248]++;
      return ind.feasible;
    });
  }
  /**
   * Update optimization history
   */
  updateHistory() {
    /* istanbul ignore next */
    cov_by2crk18o().f[46]++;
    const feasiblePopulation =
    /* istanbul ignore next */
    (cov_by2crk18o().s[249]++, this.population.filter(ind => {
      /* istanbul ignore next */
      cov_by2crk18o().f[47]++;
      cov_by2crk18o().s[250]++;
      return ind.feasible;
    }));
    const fitnesses =
    /* istanbul ignore next */
    (cov_by2crk18o().s[251]++, feasiblePopulation.map(ind => {
      /* istanbul ignore next */
      cov_by2crk18o().f[48]++;
      cov_by2crk18o().s[252]++;
      return ind.fitness;
    }));
    /* istanbul ignore next */
    cov_by2crk18o().s[253]++;
    if (fitnesses.length === 0) {
      /* istanbul ignore next */
      cov_by2crk18o().b[36][0]++;
      cov_by2crk18o().s[254]++;
      fitnesses.push(Number.MAX_VALUE);
    } else
    /* istanbul ignore next */
    {
      cov_by2crk18o().b[36][1]++;
    }
    const history =
    /* istanbul ignore next */
    (cov_by2crk18o().s[255]++, {
      iteration: this.generation,
      bestFitness: Math.min(...fitnesses),
      averageFitness: fitnesses.reduce((sum, f) => {
        /* istanbul ignore next */
        cov_by2crk18o().f[49]++;
        cov_by2crk18o().s[256]++;
        return sum + f;
      }, 0) / fitnesses.length,
      worstFitness: Math.max(...fitnesses),
      diversity: this.calculateDiversity(),
      constraintViolations: this.population.filter(ind => {
        /* istanbul ignore next */
        cov_by2crk18o().f[50]++;
        cov_by2crk18o().s[257]++;
        return !ind.feasible;
      }).length,
      timestamp: new Date()
    });
    /* istanbul ignore next */
    cov_by2crk18o().s[258]++;
    this.history.push(history);
  }
  /**
   * Calculate population diversity
   */
  calculateDiversity() {
    /* istanbul ignore next */
    cov_by2crk18o().f[51]++;
    cov_by2crk18o().s[259]++;
    if (this.population.length < 2) {
      /* istanbul ignore next */
      cov_by2crk18o().b[37][0]++;
      cov_by2crk18o().s[260]++;
      return 0;
    } else
    /* istanbul ignore next */
    {
      cov_by2crk18o().b[37][1]++;
    }
    let totalDistance =
    /* istanbul ignore next */
    (cov_by2crk18o().s[261]++, 0);
    let pairCount =
    /* istanbul ignore next */
    (cov_by2crk18o().s[262]++, 0);
    /* istanbul ignore next */
    cov_by2crk18o().s[263]++;
    for (let i =
    /* istanbul ignore next */
    (cov_by2crk18o().s[264]++, 0); i < this.population.length; i++) {
      /* istanbul ignore next */
      cov_by2crk18o().s[265]++;
      for (let j =
      /* istanbul ignore next */
      (cov_by2crk18o().s[266]++, i + 1); j < this.population.length; j++) {
        const distance =
        /* istanbul ignore next */
        (cov_by2crk18o().s[267]++, this.calculateDistance(this.population[i], this.population[j]));
        /* istanbul ignore next */
        cov_by2crk18o().s[268]++;
        totalDistance += distance;
        /* istanbul ignore next */
        cov_by2crk18o().s[269]++;
        pairCount++;
      }
    }
    /* istanbul ignore next */
    cov_by2crk18o().s[270]++;
    return pairCount > 0 ?
    /* istanbul ignore next */
    (cov_by2crk18o().b[38][0]++, totalDistance / pairCount) :
    /* istanbul ignore next */
    (cov_by2crk18o().b[38][1]++, 0);
  }
  /**
   * Calculate distance between two individuals
   */
  calculateDistance(ind1, ind2) {
    /* istanbul ignore next */
    cov_by2crk18o().f[52]++;
    let distance =
    /* istanbul ignore next */
    (cov_by2crk18o().s[271]++, 0);
    /* istanbul ignore next */
    cov_by2crk18o().s[272]++;
    for (let i =
    /* istanbul ignore next */
    (cov_by2crk18o().s[273]++, 0); i < ind1.genes.length; i++) {
      /* istanbul ignore next */
      cov_by2crk18o().s[274]++;
      if (
      /* istanbul ignore next */
      (cov_by2crk18o().b[40][0]++, typeof ind1.genes[i] === 'number') &&
      /* istanbul ignore next */
      (cov_by2crk18o().b[40][1]++, typeof ind2.genes[i] === 'number')) {
        /* istanbul ignore next */
        cov_by2crk18o().b[39][0]++;
        const diff =
        /* istanbul ignore next */
        (cov_by2crk18o().s[275]++, ind1.genes[i] - ind2.genes[i]);
        /* istanbul ignore next */
        cov_by2crk18o().s[276]++;
        distance += diff * diff;
      } else
      /* istanbul ignore next */
      {
        cov_by2crk18o().b[39][1]++;
      }
    }
    /* istanbul ignore next */
    cov_by2crk18o().s[277]++;
    return Math.sqrt(distance);
  }
  /**
   * Check termination criteria
   */
  shouldTerminate(problem) {
    /* istanbul ignore next */
    cov_by2crk18o().f[53]++;
    cov_by2crk18o().s[278]++;
    // Maximum generations
    if (this.generation >= this.parameters.maxGenerations) {
      /* istanbul ignore next */
      cov_by2crk18o().b[41][0]++;
      cov_by2crk18o().s[279]++;
      return true;
    } else
    /* istanbul ignore next */
    {
      cov_by2crk18o().b[41][1]++;
    }
    // Convergence check
    cov_by2crk18o().s[280]++;
    if (this.history.length >= 10) {
      /* istanbul ignore next */
      cov_by2crk18o().b[42][0]++;
      const recentHistory =
      /* istanbul ignore next */
      (cov_by2crk18o().s[281]++, this.history.slice(-10));
      const fitnessImprovement =
      /* istanbul ignore next */
      (cov_by2crk18o().s[282]++, recentHistory[0].bestFitness - recentHistory[recentHistory.length - 1].bestFitness);
      /* istanbul ignore next */
      cov_by2crk18o().s[283]++;
      if (Math.abs(fitnessImprovement) < problem.convergenceCriteria.toleranceValue) {
        /* istanbul ignore next */
        cov_by2crk18o().b[43][0]++;
        cov_by2crk18o().s[284]++;
        return true;
      } else
      /* istanbul ignore next */
      {
        cov_by2crk18o().b[43][1]++;
      }
    } else
    /* istanbul ignore next */
    {
      cov_by2crk18o().b[42][1]++;
    }
    cov_by2crk18o().s[285]++;
    return false;
  }
  /**
   * Adapt algorithm parameters during evolution
   */
  adaptParameters() {
    /* istanbul ignore next */
    cov_by2crk18o().f[54]++;
    // Adaptive mutation rate based on diversity
    const diversity =
    /* istanbul ignore next */
    (cov_by2crk18o().s[286]++, this.calculateDiversity());
    const diversityThreshold =
    /* istanbul ignore next */
    (cov_by2crk18o().s[287]++, 0.1);
    /* istanbul ignore next */
    cov_by2crk18o().s[288]++;
    if (diversity < diversityThreshold) {
      /* istanbul ignore next */
      cov_by2crk18o().b[44][0]++;
      cov_by2crk18o().s[289]++;
      this.parameters.mutationRate = Math.min(0.5, this.parameters.mutationRate * 1.1);
    } else {
      /* istanbul ignore next */
      cov_by2crk18o().b[44][1]++;
      cov_by2crk18o().s[290]++;
      this.parameters.mutationRate = Math.max(0.01, this.parameters.mutationRate * 0.9);
    }
  }
  /**
   * Create optimization result
   */
  createOptimizationResult(problem, startTime) {
    /* istanbul ignore next */
    cov_by2crk18o().f[55]++;
    const executionTime =
    /* istanbul ignore next */
    (cov_by2crk18o().s[291]++, performance.now() - startTime);
    // Convert best individual to optimization solution
    const bestSolution =
    /* istanbul ignore next */
    (cov_by2crk18o().s[292]++, {
      id:
      /* istanbul ignore next */
      (cov_by2crk18o().b[45][0]++, this.bestIndividual?.id) ||
      /* istanbul ignore next */
      (cov_by2crk18o().b[45][1]++, 'no_solution'),
      variables: {},
      objectiveValues: {},
      constraintViolations: [],
      feasible:
      /* istanbul ignore next */
      (cov_by2crk18o().b[46][0]++, this.bestIndividual?.feasible) ||
      /* istanbul ignore next */
      (cov_by2crk18o().b[46][1]++, false),
      fitness:
      /* istanbul ignore next */
      (cov_by2crk18o().b[47][0]++, this.bestIndividual?.fitness) ||
      /* istanbul ignore next */
      (cov_by2crk18o().b[47][1]++, Number.MAX_VALUE),
      systemConfiguration: problem.systemConfiguration,
      // Would be updated with optimized values
      performanceMetrics: {}
    });
    // Convert variables
    /* istanbul ignore next */
    cov_by2crk18o().s[293]++;
    if (this.bestIndividual) {
      /* istanbul ignore next */
      cov_by2crk18o().b[48][0]++;
      cov_by2crk18o().s[294]++;
      problem.variables.forEach((variable, index) => {
        /* istanbul ignore next */
        cov_by2crk18o().f[56]++;
        cov_by2crk18o().s[295]++;
        bestSolution.variables[variable.id] = this.bestIndividual.genes[index];
      });
      // Convert objectives
      /* istanbul ignore next */
      cov_by2crk18o().s[296]++;
      problem.objectives.objectives.forEach((objective, index) => {
        /* istanbul ignore next */
        cov_by2crk18o().f[57]++;
        cov_by2crk18o().s[297]++;
        bestSolution.objectiveValues[objective.id] =
        /* istanbul ignore next */
        (cov_by2crk18o().b[49][0]++, this.bestIndividual.objectiveValues[index]) ||
        /* istanbul ignore next */
        (cov_by2crk18o().b[49][1]++, 0);
      });
    } else
    /* istanbul ignore next */
    {
      cov_by2crk18o().b[48][1]++;
    }
    const statistics =
    /* istanbul ignore next */
    (cov_by2crk18o().s[298]++, {
      totalIterations: this.generation,
      totalEvaluations: this.evaluationCount,
      convergenceIteration: this.generation,
      executionTime,
      bestFitnessHistory: this.history.map(h => {
        /* istanbul ignore next */
        cov_by2crk18o().f[58]++;
        cov_by2crk18o().s[299]++;
        return h.bestFitness;
      }),
      averageFitnessHistory: this.history.map(h => {
        /* istanbul ignore next */
        cov_by2crk18o().f[59]++;
        cov_by2crk18o().s[300]++;
        return h.averageFitness;
      }),
      diversityHistory: this.history.map(h => {
        /* istanbul ignore next */
        cov_by2crk18o().f[60]++;
        cov_by2crk18o().s[301]++;
        return h.diversity;
      }),
      constraintViolationHistory: this.history.map(h => {
        /* istanbul ignore next */
        cov_by2crk18o().f[61]++;
        cov_by2crk18o().s[302]++;
        return h.constraintViolations;
      }),
      algorithmSpecificStats: {
        populationSize: this.parameters.populationSize,
        finalMutationRate: this.parameters.mutationRate,
        finalCrossoverRate: this.parameters.crossoverRate
      }
    });
    const optimizationHistory =
    /* istanbul ignore next */
    (cov_by2crk18o().s[303]++, {
      iterations: this.history,
      populationHistory: [],
      parameterHistory: [],
      convergenceMetrics: []
    });
    /* istanbul ignore next */
    cov_by2crk18o().s[304]++;
    return {
      problemId: problem.id,
      status: SystemOptimizationTypes_1.OptimizationStatus.CONVERGED,
      bestSolution,
      paretoFront: [],
      // Would include Pareto solutions for multi-objective
      statistics,
      history: optimizationHistory,
      analysis: {},
      recommendations: [],
      warnings: [],
      errors: []
    };
  }
  // Utility methods
  createChild(parent) {
    /* istanbul ignore next */
    cov_by2crk18o().f[62]++;
    cov_by2crk18o().s[305]++;
    return {
      id: this.generateIndividualId(),
      genes: [...parent.genes],
      fitness: 0,
      objectiveValues: [],
      constraintViolations: [],
      feasible: true,
      age: 0
    };
  }
  generateIndividualId() {
    /* istanbul ignore next */
    cov_by2crk18o().f[63]++;
    cov_by2crk18o().s[306]++;
    return `ind_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  createSeededRandom(seed) {
    /* istanbul ignore next */
    cov_by2crk18o().f[64]++;
    let state =
    /* istanbul ignore next */
    (cov_by2crk18o().s[307]++, seed);
    /* istanbul ignore next */
    cov_by2crk18o().s[308]++;
    return () => {
      /* istanbul ignore next */
      cov_by2crk18o().f[65]++;
      cov_by2crk18o().s[309]++;
      state = (state * 9301 + 49297) % 233280;
      /* istanbul ignore next */
      cov_by2crk18o().s[310]++;
      return state / 233280;
    };
  }
  gaussianRandom() {
    /* istanbul ignore next */
    cov_by2crk18o().f[66]++;
    // Box-Muller transform for Gaussian random numbers
    const u1 =
    /* istanbul ignore next */
    (cov_by2crk18o().s[311]++, this.random());
    const u2 =
    /* istanbul ignore next */
    (cov_by2crk18o().s[312]++, this.random());
    /* istanbul ignore next */
    cov_by2crk18o().s[313]++;
    return Math.sqrt(-2 * Math.log(u1)) * Math.cos(2 * Math.PI * u2);
  }
}
/* istanbul ignore next */
cov_by2crk18o().s[314]++;
exports.GeneticAlgorithm = GeneticAlgorithm;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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