2d5885f4cbbe0c5dca39926c43eb5201
"use strict";

/**
 * Advanced Caching Service for SizeWise Suite
 *
 * Implements intelligent caching algorithms with:
 * - LRU (Least Recently Used) cache eviction
 * - TTL (Time To Live) expiration
 * - Cache warming and prefetching
 * - Memory pressure management
 * - Performance metrics and monitoring
 * - Multi-tier caching (memory + IndexedDB)
 */
/* istanbul ignore next */
function cov_9rrh6chkn() {
  var path = "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\frontend\\lib\\services\\AdvancedCachingService.ts";
  var hash = "21cd65c787484963ca2fefaed45ea0c5747d052f";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\frontend\\lib\\services\\AdvancedCachingService.ts",
    statementMap: {
      "0": {
        start: {
          line: 13,
          column: 0
        },
        end: {
          line: 13,
          column: 62
        }
      },
      "1": {
        start: {
          line: 14,
          column: 0
        },
        end: {
          line: 14,
          column: 40
        }
      },
      "2": {
        start: {
          line: 20,
          column: 8
        },
        end: {
          line: 20,
          column: 37
        }
      },
      "3": {
        start: {
          line: 21,
          column: 8
        },
        end: {
          line: 21,
          column: 37
        }
      },
      "4": {
        start: {
          line: 22,
          column: 8
        },
        end: {
          line: 22,
          column: 37
        }
      },
      "5": {
        start: {
          line: 23,
          column: 8
        },
        end: {
          line: 23,
          column: 31
        }
      },
      "6": {
        start: {
          line: 24,
          column: 8
        },
        end: {
          line: 24,
          column: 33
        }
      },
      "7": {
        start: {
          line: 25,
          column: 8
        },
        end: {
          line: 33,
          column: 10
        }
      },
      "8": {
        start: {
          line: 34,
          column: 8
        },
        end: {
          line: 44,
          column: 10
        }
      },
      "9": {
        start: {
          line: 45,
          column: 8
        },
        end: {
          line: 45,
          column: 43
        }
      },
      "10": {
        start: {
          line: 46,
          column: 8
        },
        end: {
          line: 46,
          column: 45
        }
      },
      "11": {
        start: {
          line: 52,
          column: 8
        },
        end: {
          line: 52,
          column: 37
        }
      },
      "12": {
        start: {
          line: 54,
          column: 28
        },
        end: {
          line: 54,
          column: 53
        }
      },
      "13": {
        start: {
          line: 55,
          column: 8
        },
        end: {
          line: 60,
          column: 9
        }
      },
      "14": {
        start: {
          line: 56,
          column: 12
        },
        end: {
          line: 56,
          column: 50
        }
      },
      "15": {
        start: {
          line: 57,
          column: 12
        },
        end: {
          line: 57,
          column: 37
        }
      },
      "16": {
        start: {
          line: 58,
          column: 12
        },
        end: {
          line: 58,
          column: 33
        }
      },
      "17": {
        start: {
          line: 59,
          column: 12
        },
        end: {
          line: 59,
          column: 37
        }
      },
      "18": {
        start: {
          line: 62,
          column: 8
        },
        end: {
          line: 74,
          column: 9
        }
      },
      "19": {
        start: {
          line: 63,
          column: 28
        },
        end: {
          line: 63,
          column: 69
        }
      },
      "20": {
        start: {
          line: 64,
          column: 12
        },
        end: {
          line: 70,
          column: 13
        }
      },
      "21": {
        start: {
          line: 66,
          column: 16
        },
        end: {
          line: 66,
          column: 75
        }
      },
      "22": {
        start: {
          line: 67,
          column: 16
        },
        end: {
          line: 67,
          column: 41
        }
      },
      "23": {
        start: {
          line: 68,
          column: 16
        },
        end: {
          line: 68,
          column: 37
        }
      },
      "24": {
        start: {
          line: 69,
          column: 16
        },
        end: {
          line: 69,
          column: 37
        }
      },
      "25": {
        start: {
          line: 73,
          column: 12
        },
        end: {
          line: 73,
          column: 63
        }
      },
      "26": {
        start: {
          line: 75,
          column: 8
        },
        end: {
          line: 75,
          column: 35
        }
      },
      "27": {
        start: {
          line: 76,
          column: 8
        },
        end: {
          line: 76,
          column: 29
        }
      },
      "28": {
        start: {
          line: 77,
          column: 8
        },
        end: {
          line: 77,
          column: 20
        }
      },
      "29": {
        start: {
          line: 80,
          column: 21
        },
        end: {
          line: 80,
          column: 46
        }
      },
      "30": {
        start: {
          line: 82,
          column: 8
        },
        end: {
          line: 82,
          column: 57
        }
      },
      "31": {
        start: {
          line: 84,
          column: 8
        },
        end: {
          line: 86,
          column: 11
        }
      },
      "32": {
        start: {
          line: 85,
          column: 12
        },
        end: {
          line: 85,
          column: 64
        }
      },
      "33": {
        start: {
          line: 89,
          column: 8
        },
        end: {
          line: 89,
          column: 37
        }
      },
      "34": {
        start: {
          line: 90,
          column: 8
        },
        end: {
          line: 90,
          column: 37
        }
      },
      "35": {
        start: {
          line: 91,
          column: 8
        },
        end: {
          line: 96,
          column: 9
        }
      },
      "36": {
        start: {
          line: 92,
          column: 12
        },
        end: {
          line: 92,
          column: 57
        }
      },
      "37": {
        start: {
          line: 95,
          column: 12
        },
        end: {
          line: 95,
          column: 65
        }
      },
      "38": {
        start: {
          line: 99,
          column: 8
        },
        end: {
          line: 99,
          column: 33
        }
      },
      "39": {
        start: {
          line: 100,
          column: 8
        },
        end: {
          line: 100,
          column: 33
        }
      },
      "40": {
        start: {
          line: 101,
          column: 8
        },
        end: {
          line: 106,
          column: 9
        }
      },
      "41": {
        start: {
          line: 102,
          column: 12
        },
        end: {
          line: 102,
          column: 53
        }
      },
      "42": {
        start: {
          line: 105,
          column: 12
        },
        end: {
          line: 105,
          column: 64
        }
      },
      "43": {
        start: {
          line: 107,
          column: 8
        },
        end: {
          line: 107,
          column: 28
        }
      },
      "44": {
        start: {
          line: 113,
          column: 20
        },
        end: {
          line: 113,
          column: 54
        }
      },
      "45": {
        start: {
          line: 114,
          column: 8
        },
        end: {
          line: 114,
          column: 52
        }
      },
      "46": {
        start: {
          line: 117,
          column: 20
        },
        end: {
          line: 117,
          column: 54
        }
      },
      "47": {
        start: {
          line: 118,
          column: 8
        },
        end: {
          line: 118,
          column: 35
        }
      },
      "48": {
        start: {
          line: 121,
          column: 8
        },
        end: {
          line: 122,
          column: 19
        }
      },
      "49": {
        start: {
          line: 122,
          column: 12
        },
        end: {
          line: 122,
          column: 19
        }
      },
      "50": {
        start: {
          line: 123,
          column: 25
        },
        end: {
          line: 123,
          column: 80
        }
      },
      "51": {
        start: {
          line: 123,
          column: 59
        },
        end: {
          line: 123,
          column: 79
        }
      },
      "52": {
        start: {
          line: 124,
          column: 8
        },
        end: {
          line: 125,
          column: 19
        }
      },
      "53": {
        start: {
          line: 125,
          column: 12
        },
        end: {
          line: 125,
          column: 19
        }
      },
      "54": {
        start: {
          line: 126,
          column: 8
        },
        end: {
          line: 143,
          column: 9
        }
      },
      "55": {
        start: {
          line: 128,
          column: 39
        },
        end: {
          line: 133,
          column: 26
        }
      },
      "56": {
        start: {
          line: 134,
          column: 12
        },
        end: {
          line: 139,
          column: 13
        }
      },
      "57": {
        start: {
          line: 135,
          column: 28
        },
        end: {
          line: 135,
          column: 83
        }
      },
      "58": {
        start: {
          line: 136,
          column: 16
        },
        end: {
          line: 138,
          column: 17
        }
      },
      "59": {
        start: {
          line: 137,
          column: 20
        },
        end: {
          line: 137,
          column: 77
        }
      },
      "60": {
        start: {
          line: 142,
          column: 12
        },
        end: {
          line: 142,
          column: 51
        }
      },
      "61": {
        start: {
          line: 149,
          column: 26
        },
        end: {
          line: 149,
          column: 59
        }
      },
      "62": {
        start: {
          line: 150,
          column: 22
        },
        end: {
          line: 159,
          column: 9
        }
      },
      "63": {
        start: {
          line: 161,
          column: 8
        },
        end: {
          line: 161,
          column: 51
        }
      },
      "64": {
        start: {
          line: 162,
          column: 8
        },
        end: {
          line: 162,
          column: 41
        }
      },
      "65": {
        start: {
          line: 163,
          column: 8
        },
        end: {
          line: 163,
          column: 56
        }
      },
      "66": {
        start: {
          line: 164,
          column: 8
        },
        end: {
          line: 164,
          column: 33
        }
      },
      "67": {
        start: {
          line: 167,
          column: 8
        },
        end: {
          line: 182,
          column: 9
        }
      },
      "68": {
        start: {
          line: 168,
          column: 26
        },
        end: {
          line: 177,
          column: 13
        }
      },
      "69": {
        start: {
          line: 178,
          column: 12
        },
        end: {
          line: 178,
          column: 56
        }
      },
      "70": {
        start: {
          line: 181,
          column: 12
        },
        end: {
          line: 181,
          column: 65
        }
      },
      "71": {
        start: {
          line: 185,
          column: 29
        },
        end: {
          line: 185,
          column: 68
        }
      },
      "72": {
        start: {
          line: 186,
          column: 26
        },
        end: {
          line: 186,
          column: 54
        }
      },
      "73": {
        start: {
          line: 187,
          column: 8
        },
        end: {
          line: 201,
          column: 9
        }
      },
      "74": {
        start: {
          line: 188,
          column: 27
        },
        end: {
          line: 188,
          column: 44
        }
      },
      "75": {
        start: {
          line: 189,
          column: 12
        },
        end: {
          line: 200,
          column: 13
        }
      },
      "76": {
        start: {
          line: 190,
          column: 30
        },
        end: {
          line: 190,
          column: 58
        }
      },
      "77": {
        start: {
          line: 191,
          column: 16
        },
        end: {
          line: 196,
          column: 17
        }
      },
      "78": {
        start: {
          line: 192,
          column: 20
        },
        end: {
          line: 192,
          column: 46
        }
      },
      "79": {
        start: {
          line: 193,
          column: 20
        },
        end: {
          line: 193,
          column: 52
        }
      },
      "80": {
        start: {
          line: 194,
          column: 20
        },
        end: {
          line: 194,
          column: 52
        }
      },
      "81": {
        start: {
          line: 195,
          column: 20
        },
        end: {
          line: 195,
          column: 49
        }
      },
      "82": {
        start: {
          line: 199,
          column: 16
        },
        end: {
          line: 199,
          column: 22
        }
      },
      "83": {
        start: {
          line: 204,
          column: 21
        },
        end: {
          line: 204,
          column: 25
        }
      },
      "84": {
        start: {
          line: 205,
          column: 27
        },
        end: {
          line: 205,
          column: 35
        }
      },
      "85": {
        start: {
          line: 206,
          column: 8
        },
        end: {
          line: 211,
          column: 9
        }
      },
      "86": {
        start: {
          line: 207,
          column: 12
        },
        end: {
          line: 210,
          column: 13
        }
      },
      "87": {
        start: {
          line: 208,
          column: 16
        },
        end: {
          line: 208,
          column: 42
        }
      },
      "88": {
        start: {
          line: 209,
          column: 16
        },
        end: {
          line: 209,
          column: 29
        }
      },
      "89": {
        start: {
          line: 212,
          column: 8
        },
        end: {
          line: 212,
          column: 22
        }
      },
      "90": {
        start: {
          line: 215,
          column: 20
        },
        end: {
          line: 215,
          column: 30
        }
      },
      "91": {
        start: {
          line: 216,
          column: 8
        },
        end: {
          line: 216,
          column: 51
        }
      },
      "92": {
        start: {
          line: 219,
          column: 8
        },
        end: {
          line: 219,
          column: 28
        }
      },
      "93": {
        start: {
          line: 220,
          column: 8
        },
        end: {
          line: 220,
          column: 40
        }
      },
      "94": {
        start: {
          line: 221,
          column: 8
        },
        end: {
          line: 221,
          column: 62
        }
      },
      "95": {
        start: {
          line: 224,
          column: 8
        },
        end: {
          line: 224,
          column: 54
        }
      },
      "96": {
        start: {
          line: 227,
          column: 20
        },
        end: {
          line: 227,
          column: 21
        }
      },
      "97": {
        start: {
          line: 228,
          column: 8
        },
        end: {
          line: 230,
          column: 9
        }
      },
      "98": {
        start: {
          line: 229,
          column: 12
        },
        end: {
          line: 229,
          column: 32
        }
      },
      "99": {
        start: {
          line: 231,
          column: 8
        },
        end: {
          line: 231,
          column: 21
        }
      },
      "100": {
        start: {
          line: 234,
          column: 8
        },
        end: {
          line: 234,
          column: 64
        }
      },
      "101": {
        start: {
          line: 237,
          column: 8
        },
        end: {
          line: 237,
          column: 83
        }
      },
      "102": {
        start: {
          line: 238,
          column: 8
        },
        end: {
          line: 238,
          column: 86
        }
      },
      "103": {
        start: {
          line: 241,
          column: 8
        },
        end: {
          line: 251,
          column: 10
        }
      },
      "104": {
        start: {
          line: 254,
          column: 8
        },
        end: {
          line: 254,
          column: 89
        }
      },
      "105": {
        start: {
          line: 257,
          column: 8
        },
        end: {
          line: 260,
          column: 9
        }
      },
      "106": {
        start: {
          line: 263,
          column: 8
        },
        end: {
          line: 271,
          column: 9
        }
      },
      "107": {
        start: {
          line: 264,
          column: 12
        },
        end: {
          line: 270,
          column: 22
        }
      },
      "108": {
        start: {
          line: 265,
          column: 31
        },
        end: {
          line: 265,
          column: 47
        }
      },
      "109": {
        start: {
          line: 266,
          column: 16
        },
        end: {
          line: 269,
          column: 17
        }
      },
      "110": {
        start: {
          line: 268,
          column: 20
        },
        end: {
          line: 268,
          column: 51
        }
      },
      "111": {
        start: {
          line: 274,
          column: 27
        },
        end: {
          line: 274,
          column: 72
        }
      },
      "112": {
        start: {
          line: 275,
          column: 26
        },
        end: {
          line: 275,
          column: 54
        }
      },
      "113": {
        start: {
          line: 276,
          column: 8
        },
        end: {
          line: 290,
          column: 9
        }
      },
      "114": {
        start: {
          line: 277,
          column: 27
        },
        end: {
          line: 277,
          column: 44
        }
      },
      "115": {
        start: {
          line: 278,
          column: 12
        },
        end: {
          line: 289,
          column: 13
        }
      },
      "116": {
        start: {
          line: 279,
          column: 30
        },
        end: {
          line: 279,
          column: 58
        }
      },
      "117": {
        start: {
          line: 280,
          column: 16
        },
        end: {
          line: 285,
          column: 17
        }
      },
      "118": {
        start: {
          line: 281,
          column: 20
        },
        end: {
          line: 281,
          column: 46
        }
      },
      "119": {
        start: {
          line: 282,
          column: 20
        },
        end: {
          line: 282,
          column: 52
        }
      },
      "120": {
        start: {
          line: 283,
          column: 20
        },
        end: {
          line: 283,
          column: 52
        }
      },
      "121": {
        start: {
          line: 284,
          column: 20
        },
        end: {
          line: 284,
          column: 49
        }
      },
      "122": {
        start: {
          line: 288,
          column: 16
        },
        end: {
          line: 288,
          column: 22
        }
      },
      "123": {
        start: {
          line: 296,
          column: 8
        },
        end: {
          line: 296,
          column: 35
        }
      },
      "124": {
        start: {
          line: 299,
          column: 8
        },
        end: {
          line: 299,
          column: 47
        }
      },
      "125": {
        start: {
          line: 303,
          column: 8
        },
        end: {
          line: 305,
          column: 9
        }
      },
      "126": {
        start: {
          line: 304,
          column: 12
        },
        end: {
          line: 304,
          column: 32
        }
      },
      "127": {
        start: {
          line: 308,
          column: 0
        },
        end: {
          line: 308,
          column: 56
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 19,
            column: 4
          },
          end: {
            line: 19,
            column: 5
          }
        },
        loc: {
          start: {
            line: 19,
            column: 39
          },
          end: {
            line: 47,
            column: 5
          }
        },
        line: 19
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 51,
            column: 4
          },
          end: {
            line: 51,
            column: 5
          }
        },
        loc: {
          start: {
            line: 51,
            column: 19
          },
          end: {
            line: 78,
            column: 5
          }
        },
        line: 51
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 79,
            column: 4
          },
          end: {
            line: 79,
            column: 5
          }
        },
        loc: {
          start: {
            line: 79,
            column: 56
          },
          end: {
            line: 87,
            column: 5
          }
        },
        line: 79
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 84,
            column: 60
          },
          end: {
            line: 84,
            column: 61
          }
        },
        loc: {
          start: {
            line: 84,
            column: 69
          },
          end: {
            line: 86,
            column: 9
          }
        },
        line: 84
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 88,
            column: 4
          },
          end: {
            line: 88,
            column: 5
          }
        },
        loc: {
          start: {
            line: 88,
            column: 22
          },
          end: {
            line: 97,
            column: 5
          }
        },
        line: 88
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 98,
            column: 4
          },
          end: {
            line: 98,
            column: 5
          }
        },
        loc: {
          start: {
            line: 98,
            column: 18
          },
          end: {
            line: 108,
            column: 5
          }
        },
        line: 98
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 112,
            column: 4
          },
          end: {
            line: 112,
            column: 5
          }
        },
        loc: {
          start: {
            line: 112,
            column: 65
          },
          end: {
            line: 115,
            column: 5
          }
        },
        line: 112
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 116,
            column: 4
          },
          end: {
            line: 116,
            column: 5
          }
        },
        loc: {
          start: {
            line: 116,
            column: 55
          },
          end: {
            line: 119,
            column: 5
          }
        },
        line: 116
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 120,
            column: 4
          },
          end: {
            line: 120,
            column: 5
          }
        },
        loc: {
          start: {
            line: 120,
            column: 43
          },
          end: {
            line: 144,
            column: 5
          }
        },
        line: 120
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 123,
            column: 54
          },
          end: {
            line: 123,
            column: 55
          }
        },
        loc: {
          start: {
            line: 123,
            column: 59
          },
          end: {
            line: 123,
            column: 79
          }
        },
        line: 123
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 148,
            column: 4
          },
          end: {
            line: 148,
            column: 5
          }
        },
        loc: {
          start: {
            line: 148,
            column: 48
          },
          end: {
            line: 165,
            column: 5
          }
        },
        line: 148
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 166,
            column: 4
          },
          end: {
            line: 166,
            column: 5
          }
        },
        loc: {
          start: {
            line: 166,
            column: 51
          },
          end: {
            line: 183,
            column: 5
          }
        },
        line: 166
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 184,
            column: 4
          },
          end: {
            line: 184,
            column: 5
          }
        },
        loc: {
          start: {
            line: 184,
            column: 45
          },
          end: {
            line: 202,
            column: 5
          }
        },
        line: 184
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 203,
            column: 4
          },
          end: {
            line: 203,
            column: 5
          }
        },
        loc: {
          start: {
            line: 203,
            column: 17
          },
          end: {
            line: 213,
            column: 5
          }
        },
        line: 203
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 214,
            column: 4
          },
          end: {
            line: 214,
            column: 5
          }
        },
        loc: {
          start: {
            line: 214,
            column: 24
          },
          end: {
            line: 217,
            column: 5
          }
        },
        line: 214
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 218,
            column: 4
          },
          end: {
            line: 218,
            column: 5
          }
        },
        loc: {
          start: {
            line: 218,
            column: 31
          },
          end: {
            line: 222,
            column: 5
          }
        },
        line: 218
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 223,
            column: 4
          },
          end: {
            line: 223,
            column: 5
          }
        },
        loc: {
          start: {
            line: 223,
            column: 25
          },
          end: {
            line: 225,
            column: 5
          }
        },
        line: 223
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 226,
            column: 4
          },
          end: {
            line: 226,
            column: 5
          }
        },
        loc: {
          start: {
            line: 226,
            column: 28
          },
          end: {
            line: 232,
            column: 5
          }
        },
        line: 226
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 233,
            column: 4
          },
          end: {
            line: 233,
            column: 5
          }
        },
        loc: {
          start: {
            line: 233,
            column: 24
          },
          end: {
            line: 235,
            column: 5
          }
        },
        line: 233
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 236,
            column: 4
          },
          end: {
            line: 236,
            column: 5
          }
        },
        loc: {
          start: {
            line: 236,
            column: 20
          },
          end: {
            line: 239,
            column: 5
          }
        },
        line: 236
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 240,
            column: 4
          },
          end: {
            line: 240,
            column: 5
          }
        },
        loc: {
          start: {
            line: 240,
            column: 19
          },
          end: {
            line: 252,
            column: 5
          }
        },
        line: 240
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 253,
            column: 4
          },
          end: {
            line: 253,
            column: 5
          }
        },
        loc: {
          start: {
            line: 253,
            column: 21
          },
          end: {
            line: 255,
            column: 5
          }
        },
        line: 253
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 256,
            column: 4
          },
          end: {
            line: 256,
            column: 5
          }
        },
        loc: {
          start: {
            line: 256,
            column: 34
          },
          end: {
            line: 261,
            column: 5
          }
        },
        line: 256
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 262,
            column: 4
          },
          end: {
            line: 262,
            column: 5
          }
        },
        loc: {
          start: {
            line: 262,
            column: 36
          },
          end: {
            line: 272,
            column: 5
          }
        },
        line: 262
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 264,
            column: 24
          },
          end: {
            line: 264,
            column: 25
          }
        },
        loc: {
          start: {
            line: 264,
            column: 30
          },
          end: {
            line: 270,
            column: 13
          }
        },
        line: 264
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 273,
            column: 4
          },
          end: {
            line: 273,
            column: 5
          }
        },
        loc: {
          start: {
            line: 273,
            column: 36
          },
          end: {
            line: 291,
            column: 5
          }
        },
        line: 273
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 295,
            column: 4
          },
          end: {
            line: 295,
            column: 5
          }
        },
        loc: {
          start: {
            line: 295,
            column: 17
          },
          end: {
            line: 297,
            column: 5
          }
        },
        line: 295
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 298,
            column: 4
          },
          end: {
            line: 298,
            column: 5
          }
        },
        loc: {
          start: {
            line: 298,
            column: 34
          },
          end: {
            line: 300,
            column: 5
          }
        },
        line: 298
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 301,
            column: 4
          },
          end: {
            line: 301,
            column: 5
          }
        },
        loc: {
          start: {
            line: 301,
            column: 26
          },
          end: {
            line: 306,
            column: 5
          }
        },
        line: 301
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 19,
            column: 26
          },
          end: {
            line: 19,
            column: 37
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 19,
            column: 35
          },
          end: {
            line: 19,
            column: 37
          }
        }],
        line: 19
      },
      "1": {
        loc: {
          start: {
            line: 55,
            column: 8
          },
          end: {
            line: 60,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 55,
            column: 8
          },
          end: {
            line: 60,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 55
      },
      "2": {
        loc: {
          start: {
            line: 55,
            column: 12
          },
          end: {
            line: 55,
            column: 57
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 55,
            column: 12
          },
          end: {
            line: 55,
            column: 23
          }
        }, {
          start: {
            line: 55,
            column: 27
          },
          end: {
            line: 55,
            column: 57
          }
        }],
        line: 55
      },
      "3": {
        loc: {
          start: {
            line: 64,
            column: 12
          },
          end: {
            line: 70,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 64,
            column: 12
          },
          end: {
            line: 70,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 64
      },
      "4": {
        loc: {
          start: {
            line: 64,
            column: 16
          },
          end: {
            line: 64,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 64,
            column: 16
          },
          end: {
            line: 64,
            column: 23
          }
        }, {
          start: {
            line: 64,
            column: 27
          },
          end: {
            line: 64,
            column: 53
          }
        }],
        line: 64
      },
      "5": {
        loc: {
          start: {
            line: 79,
            column: 26
          },
          end: {
            line: 79,
            column: 54
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 79,
            column: 32
          },
          end: {
            line: 79,
            column: 54
          }
        }],
        line: 79
      },
      "6": {
        loc: {
          start: {
            line: 121,
            column: 8
          },
          end: {
            line: 122,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 121,
            column: 8
          },
          end: {
            line: 122,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 121
      },
      "7": {
        loc: {
          start: {
            line: 124,
            column: 8
          },
          end: {
            line: 125,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 124,
            column: 8
          },
          end: {
            line: 125,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 124
      },
      "8": {
        loc: {
          start: {
            line: 136,
            column: 16
          },
          end: {
            line: 138,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 136,
            column: 16
          },
          end: {
            line: 138,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 136
      },
      "9": {
        loc: {
          start: {
            line: 149,
            column: 26
          },
          end: {
            line: 149,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 149,
            column: 26
          },
          end: {
            line: 149,
            column: 30
          }
        }, {
          start: {
            line: 149,
            column: 34
          },
          end: {
            line: 149,
            column: 59
          }
        }],
        line: 149
      },
      "10": {
        loc: {
          start: {
            line: 187,
            column: 15
          },
          end: {
            line: 187,
            column: 85
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 187,
            column: 15
          },
          end: {
            line: 187,
            column: 56
          }
        }, {
          start: {
            line: 187,
            column: 60
          },
          end: {
            line: 187,
            column: 85
          }
        }],
        line: 187
      },
      "11": {
        loc: {
          start: {
            line: 189,
            column: 12
          },
          end: {
            line: 200,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 189,
            column: 12
          },
          end: {
            line: 200,
            column: 13
          }
        }, {
          start: {
            line: 198,
            column: 17
          },
          end: {
            line: 200,
            column: 13
          }
        }],
        line: 189
      },
      "12": {
        loc: {
          start: {
            line: 191,
            column: 16
          },
          end: {
            line: 196,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 191,
            column: 16
          },
          end: {
            line: 196,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 191
      },
      "13": {
        loc: {
          start: {
            line: 207,
            column: 12
          },
          end: {
            line: 210,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 207,
            column: 12
          },
          end: {
            line: 210,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 207
      },
      "14": {
        loc: {
          start: {
            line: 257,
            column: 8
          },
          end: {
            line: 260,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 257,
            column: 8
          },
          end: {
            line: 260,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 257
      },
      "15": {
        loc: {
          start: {
            line: 257,
            column: 12
          },
          end: {
            line: 257,
            column: 75
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 257,
            column: 12
          },
          end: {
            line: 257,
            column: 42
          }
        }, {
          start: {
            line: 257,
            column: 46
          },
          end: {
            line: 257,
            column: 75
          }
        }],
        line: 257
      },
      "16": {
        loc: {
          start: {
            line: 263,
            column: 8
          },
          end: {
            line: 271,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 263,
            column: 8
          },
          end: {
            line: 271,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 263
      },
      "17": {
        loc: {
          start: {
            line: 263,
            column: 12
          },
          end: {
            line: 263,
            column: 69
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 263,
            column: 12
          },
          end: {
            line: 263,
            column: 44
          }
        }, {
          start: {
            line: 263,
            column: 48
          },
          end: {
            line: 263,
            column: 69
          }
        }],
        line: 263
      },
      "18": {
        loc: {
          start: {
            line: 266,
            column: 16
          },
          end: {
            line: 269,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 266,
            column: 16
          },
          end: {
            line: 269,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 266
      },
      "19": {
        loc: {
          start: {
            line: 266,
            column: 20
          },
          end: {
            line: 266,
            column: 82
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 266,
            column: 20
          },
          end: {
            line: 266,
            column: 26
          }
        }, {
          start: {
            line: 266,
            column: 30
          },
          end: {
            line: 266,
            column: 82
          }
        }],
        line: 266
      },
      "20": {
        loc: {
          start: {
            line: 276,
            column: 15
          },
          end: {
            line: 276,
            column: 68
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 276,
            column: 15
          },
          end: {
            line: 276,
            column: 39
          }
        }, {
          start: {
            line: 276,
            column: 43
          },
          end: {
            line: 276,
            column: 68
          }
        }],
        line: 276
      },
      "21": {
        loc: {
          start: {
            line: 278,
            column: 12
          },
          end: {
            line: 289,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 278,
            column: 12
          },
          end: {
            line: 289,
            column: 13
          }
        }, {
          start: {
            line: 287,
            column: 17
          },
          end: {
            line: 289,
            column: 13
          }
        }],
        line: 278
      },
      "22": {
        loc: {
          start: {
            line: 280,
            column: 16
          },
          end: {
            line: 285,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 280,
            column: 16
          },
          end: {
            line: 285,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 280
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0
    },
    b: {
      "0": [0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\frontend\\lib\\services\\AdvancedCachingService.ts",
      mappings: ";AAAA;;;;;;;;;;GAUG;;;AAgDH,gFAAgF;AAChF,0CAA0C;AAC1C,gFAAgF;AAEhF,MAAa,sBAAsB;IAUjC,YAAY,QAA0B,EAAE,SAA+B,EAAE;QATjE,gBAAW,GAAG,IAAI,GAAG,EAAsB,CAAC;QAC5C,gBAAW,GAAG,IAAI,GAAG,EAAkB,CAAC,CAAC,mBAAmB;QAI5D,uBAAkB,GAAuB,EAAE,CAAC;QAE5C,kBAAa,GAAG,CAAC,CAAC;QAGxB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,MAAM,GAAG;YACZ,aAAa,EAAE,EAAE,EAAE,eAAe;YAClC,UAAU,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,aAAa;YACzC,gBAAgB,EAAE,GAAG,EAAE,gBAAgB;YACvC,kBAAkB,EAAE,IAAI;YACxB,eAAe,EAAE,IAAI;YACrB,cAAc,EAAE,IAAI;YACpB,GAAG,MAAM;SACV,CAAC;QAEF,IAAI,CAAC,OAAO,GAAG;YACb,OAAO,EAAE,CAAC;YACV,QAAQ,EAAE,CAAC;YACX,aAAa,EAAE,CAAC;YAChB,SAAS,EAAE,CAAC;YACZ,WAAW,EAAE,CAAC;YACd,WAAW,EAAE,CAAC;YACd,cAAc,EAAE,CAAC;YACjB,aAAa,EAAE,CAAC;YAChB,gBAAgB,EAAE,CAAC;SACpB,CAAC;QAEF,IAAI,CAAC,2BAA2B,EAAE,CAAC;QACnC,IAAI,CAAC,6BAA6B,EAAE,CAAC;IACvC,CAAC;IAED,gFAAgF;IAChF,wBAAwB;IACxB,gFAAgF;IAEhF,KAAK,CAAC,GAAG,CAAI,GAAW;QACtB,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;QAE7B,2BAA2B;QAC3B,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC9C,IAAI,WAAW,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,EAAE,CAAC;YAClD,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;YACtC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;YACzB,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,OAAO,WAAW,CAAC,KAAU,CAAC;QAChC,CAAC;QAED,wBAAwB;QACxB,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAC1D,IAAI,OAAO,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC1C,0BAA0B;gBAC1B,MAAM,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;gBAC3D,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;gBACzB,IAAI,CAAC,aAAa,EAAE,CAAC;gBACrB,OAAO,OAAO,CAAC,KAAU,CAAC;YAC5B,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;QACrD,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;QAC3B,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,GAAG,CACP,GAAW,EACX,KAAQ,EACR,MAAc,IAAI,CAAC,MAAM,CAAC,UAAU;QAEpC,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAEvC,sBAAsB;QACtB,MAAM,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;QAEjD,iCAAiC;QACjC,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YAC1D,OAAO,CAAC,IAAI,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,GAAW;QACtB,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAC7B,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAE7B,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAC/C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAED,KAAK,CAAC,KAAK;QACT,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;QACzB,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;QAEzB,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;QAC3C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;QACtD,CAAC;QAED,IAAI,CAAC,YAAY,EAAE,CAAC;IACtB,CAAC;IAED,gFAAgF;IAChF,8BAA8B;IAC9B,gFAAgF;IAEhF,KAAK,CAAC,sBAAsB,CAC1B,WAAmB,EACnB,SAAiB,EACjB,MAAyB;QAEzB,MAAM,GAAG,GAAG,QAAQ,WAAW,IAAI,SAAS,EAAE,CAAC;QAC/C,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,8BAA8B;IAC7E,CAAC;IAED,KAAK,CAAC,oBAAoB,CACxB,WAAmB,EACnB,SAAiB;QAEjB,MAAM,GAAG,GAAG,QAAQ,WAAW,IAAI,SAAS,EAAE,CAAC;QAC/C,OAAO,MAAM,IAAI,CAAC,GAAG,CAAoB,GAAG,CAAC,CAAC;IAChD,CAAC;IAED,KAAK,CAAC,mBAAmB,CAAC,WAAmB;QAC3C,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe;YAAE,OAAO;QAEzC,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC;QACzE,IAAI,CAAC,QAAQ;YAAE,OAAO;QAEtB,IAAI,CAAC;YACH,gDAAgD;YAChD,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,YAAY;iBACxD,KAAK,CAAC,aAAa,CAAC;iBACpB,MAAM,CAAC,WAAW,CAAC;iBACnB,OAAO,EAAE;iBACT,KAAK,CAAC,QAAQ,CAAC,WAAW,CAAC;iBAC3B,OAAO,EAAE,CAAC;YAEb,KAAK,MAAM,IAAI,IAAI,kBAAkB,EAAE,CAAC;gBACtC,MAAM,GAAG,GAAG,QAAQ,WAAW,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;gBACpE,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;oBAC/B,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;gBAC3D,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;QACzC,CAAC;IACH,CAAC;IAED,gFAAgF;IAChF,oCAAoC;IACpC,gFAAgF;IAExE,KAAK,CAAC,cAAc,CAC1B,GAAW,EACX,KAAQ,EACR,GAAW,EACX,IAAa;QAEb,MAAM,SAAS,GAAG,IAAI,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QACpD,MAAM,KAAK,GAAkB;YAC3B,GAAG;YACH,KAAK;YACL,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,GAAG;YACH,WAAW,EAAE,CAAC;YACd,YAAY,EAAE,IAAI,CAAC,GAAG,EAAE;YACxB,IAAI,EAAE,SAAS;YACf,UAAU,EAAE,KAAK;SAClB,CAAC;QAEF,oCAAoC;QACpC,MAAM,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;QAE3C,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACjC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAChD,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC3B,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAC7B,GAAW,EACX,KAAQ,EACR,GAAW,EACX,IAAY;QAEZ,IAAI,CAAC;YACH,MAAM,KAAK,GAAkB;gBAC3B,GAAG;gBACH,KAAK;gBACL,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,GAAG;gBACH,WAAW,EAAE,CAAC;gBACd,YAAY,EAAE,IAAI,CAAC,GAAG,EAAE;gBACxB,IAAI;gBACJ,UAAU,EAAE,KAAK;aAClB,CAAC;YAEF,MAAM,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAC9C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,YAAoB;QACrD,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,GAAG,IAAI,GAAG,IAAI,CAAC;QAC7D,IAAI,WAAW,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAE/C,OAAO,WAAW,GAAG,YAAY,GAAG,YAAY,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;YAC9E,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;YACjC,IAAI,MAAM,EAAE,CAAC;gBACX,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;gBAC3C,IAAI,KAAK,EAAE,CAAC;oBACV,WAAW,IAAI,KAAK,CAAC,IAAI,CAAC;oBAC1B,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;oBAChC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;oBAChC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;gBAC/B,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,MAAM;YACR,CAAC;QACH,CAAC;IACH,CAAC;IAEO,UAAU;QAChB,IAAI,MAAM,GAAkB,IAAI,CAAC;QACjC,IAAI,YAAY,GAAG,QAAQ,CAAC;QAE5B,KAAK,MAAM,CAAC,GAAG,EAAE,UAAU,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACjD,IAAI,UAAU,GAAG,YAAY,EAAE,CAAC;gBAC9B,YAAY,GAAG,UAAU,CAAC;gBAC1B,MAAM,GAAG,GAAG,CAAC;YACf,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,YAAY,CAAC,KAAiB;QACpC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,OAAO,CAAC,GAAG,GAAG,KAAK,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC;IAC7C,CAAC;IAEO,mBAAmB,CAAC,KAAiB;QAC3C,KAAK,CAAC,WAAW,EAAE,CAAC;QACpB,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAChC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IACxD,CAAC;IAEO,aAAa,CAAC,KAAU;QAC9B,OAAO,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAChD,CAAC;IAEO,qBAAqB;QAC3B,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC;YAC9C,KAAK,IAAI,KAAK,CAAC,IAAI,CAAC;QACtB,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,iBAAiB;QACvB,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;IAC1D,CAAC;IAEO,aAAa;QACnB,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC;QAC3E,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC;IAChF,CAAC;IAEO,YAAY;QAClB,IAAI,CAAC,OAAO,GAAG;YACb,OAAO,EAAE,CAAC;YACV,QAAQ,EAAE,CAAC;YACX,aAAa,EAAE,CAAC;YAChB,SAAS,EAAE,CAAC;YACZ,WAAW,EAAE,CAAC;YACd,WAAW,EAAE,CAAC;YACd,cAAc,EAAE,CAAC;YACjB,aAAa,EAAE,CAAC;YAChB,gBAAgB,EAAE,CAAC;SACpB,CAAC;IACJ,CAAC;IAEO,SAAS,CAAC,KAAU;QAC1B,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IACnF,CAAC;IAEO,2BAA2B;QACjC,IAAI,IAAI,CAAC,MAAM,CAAC,kBAAkB,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;YACpE,wDAAwD;YACxD,+BAA+B;QACjC,CAAC;IACH,CAAC;IAEO,6BAA6B;QACnC,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,QAAQ,IAAI,SAAS,EAAE,CAAC;YAC9D,WAAW,CAAC,GAAG,EAAE;gBACf,MAAM,MAAM,GAAI,SAAiB,CAAC,MAAM,CAAC;gBACzC,IAAI,MAAM,IAAI,MAAM,CAAC,cAAc,GAAG,MAAM,CAAC,eAAe,GAAG,GAAG,EAAE,CAAC;oBACnE,kDAAkD;oBAClD,IAAI,CAAC,uBAAuB,EAAE,CAAC;gBACjC,CAAC;YACH,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,yBAAyB;QACtC,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,uBAAuB;QACnC,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,aAAa;QAC/E,IAAI,WAAW,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAE/C,OAAO,WAAW,GAAG,UAAU,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;YAC7D,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;YACjC,IAAI,MAAM,EAAE,CAAC;gBACX,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;gBAC3C,IAAI,KAAK,EAAE,CAAC;oBACV,WAAW,IAAI,KAAK,CAAC,IAAI,CAAC;oBAC1B,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;oBAChC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;oBAChC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;gBAC/B,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,MAAM;YACR,CAAC;QACH,CAAC;IACH,CAAC;IAED,gFAAgF;IAChF,wCAAwC;IACxC,gFAAgF;IAEhF,UAAU;QACR,OAAO,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;IAC7B,CAAC;IAED,mBAAmB,CAAC,QAA0B;QAC5C,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACzC,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,IAAc;QAC5B,mCAAmC;QACnC,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,0CAA0C;QACjE,CAAC;IACH,CAAC;CACF;AAnWD,wDAmWC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\frontend\\lib\\services\\AdvancedCachingService.ts"],
      sourcesContent: ["/**\r\n * Advanced Caching Service for SizeWise Suite\r\n * \r\n * Implements intelligent caching algorithms with:\r\n * - LRU (Least Recently Used) cache eviction\r\n * - TTL (Time To Live) expiration\r\n * - Cache warming and prefetching\r\n * - Memory pressure management\r\n * - Performance metrics and monitoring\r\n * - Multi-tier caching (memory + IndexedDB)\r\n */\r\n\r\nimport { SizeWiseDatabase } from '../database/DexieDatabase';\r\nimport { CalculationResult } from '@/types/air-duct-sizer';\r\n\r\n// =============================================================================\r\n// Cache Configuration and Types\r\n// =============================================================================\r\n\r\nexport interface CacheConfig {\r\n  maxMemorySize: number; // Maximum memory cache size in MB\r\n  defaultTTL: number; // Default TTL in milliseconds\r\n  maxIndexedDBSize: number; // Maximum IndexedDB cache size in MB\r\n  compressionEnabled: boolean;\r\n  prefetchEnabled: boolean;\r\n  metricsEnabled: boolean;\r\n}\r\n\r\nexport interface CacheEntry<T = any> {\r\n  key: string;\r\n  value: T;\r\n  timestamp: number;\r\n  ttl: number;\r\n  accessCount: number;\r\n  lastAccessed: number;\r\n  size: number; // Size in bytes\r\n  compressed: boolean;\r\n}\r\n\r\nexport interface CacheMetrics {\r\n  hitRate: number;\r\n  missRate: number;\r\n  totalRequests: number;\r\n  totalHits: number;\r\n  totalMisses: number;\r\n  memoryUsage: number;\r\n  indexedDBUsage: number;\r\n  evictionCount: number;\r\n  compressionRatio: number;\r\n}\r\n\r\nexport interface PrefetchStrategy {\r\n  type: 'calculation' | 'project' | 'spatial';\r\n  patterns: string[];\r\n  priority: number;\r\n  maxPrefetch: number;\r\n}\r\n\r\n// =============================================================================\r\n// Advanced Caching Service Implementation\r\n// =============================================================================\r\n\r\nexport class AdvancedCachingService {\r\n  private memoryCache = new Map<string, CacheEntry>();\r\n  private accessOrder = new Map<string, number>(); // For LRU tracking\r\n  private database: SizeWiseDatabase;\r\n  private config: CacheConfig;\r\n  private metrics: CacheMetrics;\r\n  private prefetchStrategies: PrefetchStrategy[] = [];\r\n  private compressionWorker?: Worker;\r\n  private accessCounter = 0;\r\n\r\n  constructor(database: SizeWiseDatabase, config: Partial<CacheConfig> = {}) {\r\n    this.database = database;\r\n    this.config = {\r\n      maxMemorySize: 50, // 50MB default\r\n      defaultTTL: 30 * 60 * 1000, // 30 minutes\r\n      maxIndexedDBSize: 200, // 200MB default\r\n      compressionEnabled: true,\r\n      prefetchEnabled: true,\r\n      metricsEnabled: true,\r\n      ...config\r\n    };\r\n\r\n    this.metrics = {\r\n      hitRate: 0,\r\n      missRate: 0,\r\n      totalRequests: 0,\r\n      totalHits: 0,\r\n      totalMisses: 0,\r\n      memoryUsage: 0,\r\n      indexedDBUsage: 0,\r\n      evictionCount: 0,\r\n      compressionRatio: 0\r\n    };\r\n\r\n    this.initializeCompressionWorker();\r\n    this.startMemoryPressureMonitoring();\r\n  }\r\n\r\n  // =============================================================================\r\n  // Core Cache Operations\r\n  // =============================================================================\r\n\r\n  async get<T>(key: string): Promise<T | null> {\r\n    this.metrics.totalRequests++;\r\n\r\n    // Check memory cache first\r\n    const memoryEntry = this.memoryCache.get(key);\r\n    if (memoryEntry && this.isValidEntry(memoryEntry)) {\r\n      this.updateAccessMetrics(memoryEntry);\r\n      this.metrics.totalHits++;\r\n      this.updateHitRate();\r\n      return memoryEntry.value as T;\r\n    }\r\n\r\n    // Check IndexedDB cache\r\n    try {\r\n      const dbEntry = await this.database.cacheEntries.get(key);\r\n      if (dbEntry && this.isValidEntry(dbEntry)) {\r\n        // Promote to memory cache\r\n        await this.setMemoryCache(key, dbEntry.value, dbEntry.ttl);\r\n        this.metrics.totalHits++;\r\n        this.updateHitRate();\r\n        return dbEntry.value as T;\r\n      }\r\n    } catch (error) {\r\n      console.warn('IndexedDB cache read error:', error);\r\n    }\r\n\r\n    this.metrics.totalMisses++;\r\n    this.updateHitRate();\r\n    return null;\r\n  }\r\n\r\n  async set<T>(\r\n    key: string, \r\n    value: T, \r\n    ttl: number = this.config.defaultTTL\r\n  ): Promise<void> {\r\n    const size = this.calculateSize(value);\r\n    \r\n    // Set in memory cache\r\n    await this.setMemoryCache(key, value, ttl, size);\r\n    \r\n    // Set in IndexedDB cache (async)\r\n    this.setIndexedDBCache(key, value, ttl, size).catch(error => {\r\n      console.warn('IndexedDB cache write error:', error);\r\n    });\r\n  }\r\n\r\n  async delete(key: string): Promise<void> {\r\n    this.memoryCache.delete(key);\r\n    this.accessOrder.delete(key);\r\n    \r\n    try {\r\n      await this.database.cacheEntries.delete(key);\r\n    } catch (error) {\r\n      console.warn('IndexedDB cache delete error:', error);\r\n    }\r\n  }\r\n\r\n  async clear(): Promise<void> {\r\n    this.memoryCache.clear();\r\n    this.accessOrder.clear();\r\n    \r\n    try {\r\n      await this.database.cacheEntries.clear();\r\n    } catch (error) {\r\n      console.warn('IndexedDB cache clear error:', error);\r\n    }\r\n    \r\n    this.resetMetrics();\r\n  }\r\n\r\n  // =============================================================================\r\n  // Specialized Caching Methods\r\n  // =============================================================================\r\n\r\n  async cacheCalculationResult(\r\n    projectUuid: string,\r\n    inputHash: string,\r\n    result: CalculationResult\r\n  ): Promise<void> {\r\n    const key = `calc:${projectUuid}:${inputHash}`;\r\n    await this.set(key, result, 60 * 60 * 1000); // 1 hour TTL for calculations\r\n  }\r\n\r\n  async getCachedCalculation(\r\n    projectUuid: string,\r\n    inputHash: string\r\n  ): Promise<CalculationResult | null> {\r\n    const key = `calc:${projectUuid}:${inputHash}`;\r\n    return await this.get<CalculationResult>(key);\r\n  }\r\n\r\n  async prefetchProjectData(projectUuid: string): Promise<void> {\r\n    if (!this.config.prefetchEnabled) return;\r\n\r\n    const strategy = this.prefetchStrategies.find(s => s.type === 'project');\r\n    if (!strategy) return;\r\n\r\n    try {\r\n      // Prefetch common calculations for this project\r\n      const recentCalculations = await this.database.calculations\r\n        .where('projectUuid')\r\n        .equals(projectUuid)\r\n        .reverse()\r\n        .limit(strategy.maxPrefetch)\r\n        .toArray();\r\n\r\n      for (const calc of recentCalculations) {\r\n        const key = `calc:${projectUuid}:${this.hashInput(calc.inputData)}`;\r\n        if (!this.memoryCache.has(key)) {\r\n          await this.set(key, calc.result, this.config.defaultTTL);\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.warn('Prefetch error:', error);\r\n    }\r\n  }\r\n\r\n  // =============================================================================\r\n  // Cache Management and Optimization\r\n  // =============================================================================\r\n\r\n  private async setMemoryCache<T>(\r\n    key: string,\r\n    value: T,\r\n    ttl: number,\r\n    size?: number\r\n  ): Promise<void> {\r\n    const entrySize = size || this.calculateSize(value);\r\n    const entry: CacheEntry<T> = {\r\n      key,\r\n      value,\r\n      timestamp: Date.now(),\r\n      ttl,\r\n      accessCount: 1,\r\n      lastAccessed: Date.now(),\r\n      size: entrySize,\r\n      compressed: false\r\n    };\r\n\r\n    // Check if we need to evict entries\r\n    await this.ensureMemoryCapacity(entrySize);\r\n\r\n    this.memoryCache.set(key, entry);\r\n    this.accessOrder.set(key, ++this.accessCounter);\r\n    this.updateMemoryUsage();\r\n  }\r\n\r\n  private async setIndexedDBCache<T>(\r\n    key: string,\r\n    value: T,\r\n    ttl: number,\r\n    size: number\r\n  ): Promise<void> {\r\n    try {\r\n      const entry: CacheEntry<T> = {\r\n        key,\r\n        value,\r\n        timestamp: Date.now(),\r\n        ttl,\r\n        accessCount: 1,\r\n        lastAccessed: Date.now(),\r\n        size,\r\n        compressed: false\r\n      };\r\n\r\n      await this.database.cacheEntries.put(entry);\r\n    } catch (error) {\r\n      console.warn('IndexedDB cache write failed:', error);\r\n    }\r\n  }\r\n\r\n  private async ensureMemoryCapacity(requiredSize: number): Promise<void> {\r\n    const maxSizeBytes = this.config.maxMemorySize * 1024 * 1024;\r\n    let currentSize = this.getCurrentMemoryUsage();\r\n\r\n    while (currentSize + requiredSize > maxSizeBytes && this.memoryCache.size > 0) {\r\n      const lruKey = this.findLRUKey();\r\n      if (lruKey) {\r\n        const entry = this.memoryCache.get(lruKey);\r\n        if (entry) {\r\n          currentSize -= entry.size;\r\n          this.memoryCache.delete(lruKey);\r\n          this.accessOrder.delete(lruKey);\r\n          this.metrics.evictionCount++;\r\n        }\r\n      } else {\r\n        break;\r\n      }\r\n    }\r\n  }\r\n\r\n  private findLRUKey(): string | null {\r\n    let lruKey: string | null = null;\r\n    let oldestAccess = Infinity;\r\n\r\n    for (const [key, accessTime] of this.accessOrder) {\r\n      if (accessTime < oldestAccess) {\r\n        oldestAccess = accessTime;\r\n        lruKey = key;\r\n      }\r\n    }\r\n\r\n    return lruKey;\r\n  }\r\n\r\n  private isValidEntry(entry: CacheEntry): boolean {\r\n    const now = Date.now();\r\n    return (now - entry.timestamp) < entry.ttl;\r\n  }\r\n\r\n  private updateAccessMetrics(entry: CacheEntry): void {\r\n    entry.accessCount++;\r\n    entry.lastAccessed = Date.now();\r\n    this.accessOrder.set(entry.key, ++this.accessCounter);\r\n  }\r\n\r\n  private calculateSize(value: any): number {\r\n    return new Blob([JSON.stringify(value)]).size;\r\n  }\r\n\r\n  private getCurrentMemoryUsage(): number {\r\n    let total = 0;\r\n    for (const entry of this.memoryCache.values()) {\r\n      total += entry.size;\r\n    }\r\n    return total;\r\n  }\r\n\r\n  private updateMemoryUsage(): void {\r\n    this.metrics.memoryUsage = this.getCurrentMemoryUsage();\r\n  }\r\n\r\n  private updateHitRate(): void {\r\n    this.metrics.hitRate = this.metrics.totalHits / this.metrics.totalRequests;\r\n    this.metrics.missRate = this.metrics.totalMisses / this.metrics.totalRequests;\r\n  }\r\n\r\n  private resetMetrics(): void {\r\n    this.metrics = {\r\n      hitRate: 0,\r\n      missRate: 0,\r\n      totalRequests: 0,\r\n      totalHits: 0,\r\n      totalMisses: 0,\r\n      memoryUsage: 0,\r\n      indexedDBUsage: 0,\r\n      evictionCount: 0,\r\n      compressionRatio: 0\r\n    };\r\n  }\r\n\r\n  private hashInput(input: any): string {\r\n    return btoa(JSON.stringify(input)).replace(/[^a-zA-Z0-9]/g, '').substring(0, 16);\r\n  }\r\n\r\n  private initializeCompressionWorker(): void {\r\n    if (this.config.compressionEnabled && typeof Worker !== 'undefined') {\r\n      // Initialize compression worker for large cache entries\r\n      // Implementation would go here\r\n    }\r\n  }\r\n\r\n  private startMemoryPressureMonitoring(): void {\r\n    if (typeof navigator !== 'undefined' && 'memory' in navigator) {\r\n      setInterval(() => {\r\n        const memory = (navigator as any).memory;\r\n        if (memory && memory.usedJSHeapSize > memory.totalJSHeapSize * 0.8) {\r\n          // High memory pressure - aggressive cache cleanup\r\n          this.performEmergencyCleanup();\r\n        }\r\n      }, 30000); // Check every 30 seconds\r\n    }\r\n  }\r\n\r\n  private async performEmergencyCleanup(): Promise<void> {\r\n    const targetSize = this.config.maxMemorySize * 0.5 * 1024 * 1024; // 50% of max\r\n    let currentSize = this.getCurrentMemoryUsage();\r\n\r\n    while (currentSize > targetSize && this.memoryCache.size > 0) {\r\n      const lruKey = this.findLRUKey();\r\n      if (lruKey) {\r\n        const entry = this.memoryCache.get(lruKey);\r\n        if (entry) {\r\n          currentSize -= entry.size;\r\n          this.memoryCache.delete(lruKey);\r\n          this.accessOrder.delete(lruKey);\r\n          this.metrics.evictionCount++;\r\n        }\r\n      } else {\r\n        break;\r\n      }\r\n    }\r\n  }\r\n\r\n  // =============================================================================\r\n  // Public API for Metrics and Management\r\n  // =============================================================================\r\n\r\n  getMetrics(): CacheMetrics {\r\n    return { ...this.metrics };\r\n  }\r\n\r\n  addPrefetchStrategy(strategy: PrefetchStrategy): void {\r\n    this.prefetchStrategies.push(strategy);\r\n  }\r\n\r\n  async warmCache(keys: string[]): Promise<void> {\r\n    // Implementation for cache warming\r\n    for (const key of keys) {\r\n      await this.get(key); // This will populate cache if data exists\r\n    }\r\n  }\r\n}\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "21cd65c787484963ca2fefaed45ea0c5747d052f"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_9rrh6chkn = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_9rrh6chkn();
cov_9rrh6chkn().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_9rrh6chkn().s[1]++;
exports.AdvancedCachingService = void 0;
// =============================================================================
// Advanced Caching Service Implementation
// =============================================================================
class AdvancedCachingService {
  constructor(database, config =
  /* istanbul ignore next */
  (cov_9rrh6chkn().b[0][0]++, {})) {
    /* istanbul ignore next */
    cov_9rrh6chkn().f[0]++;
    cov_9rrh6chkn().s[2]++;
    this.memoryCache = new Map();
    /* istanbul ignore next */
    cov_9rrh6chkn().s[3]++;
    this.accessOrder = new Map(); // For LRU tracking
    /* istanbul ignore next */
    cov_9rrh6chkn().s[4]++;
    this.prefetchStrategies = [];
    /* istanbul ignore next */
    cov_9rrh6chkn().s[5]++;
    this.accessCounter = 0;
    /* istanbul ignore next */
    cov_9rrh6chkn().s[6]++;
    this.database = database;
    /* istanbul ignore next */
    cov_9rrh6chkn().s[7]++;
    this.config = {
      maxMemorySize: 50,
      // 50MB default
      defaultTTL: 30 * 60 * 1000,
      // 30 minutes
      maxIndexedDBSize: 200,
      // 200MB default
      compressionEnabled: true,
      prefetchEnabled: true,
      metricsEnabled: true,
      ...config
    };
    /* istanbul ignore next */
    cov_9rrh6chkn().s[8]++;
    this.metrics = {
      hitRate: 0,
      missRate: 0,
      totalRequests: 0,
      totalHits: 0,
      totalMisses: 0,
      memoryUsage: 0,
      indexedDBUsage: 0,
      evictionCount: 0,
      compressionRatio: 0
    };
    /* istanbul ignore next */
    cov_9rrh6chkn().s[9]++;
    this.initializeCompressionWorker();
    /* istanbul ignore next */
    cov_9rrh6chkn().s[10]++;
    this.startMemoryPressureMonitoring();
  }
  // =============================================================================
  // Core Cache Operations
  // =============================================================================
  async get(key) {
    /* istanbul ignore next */
    cov_9rrh6chkn().f[1]++;
    cov_9rrh6chkn().s[11]++;
    this.metrics.totalRequests++;
    // Check memory cache first
    const memoryEntry =
    /* istanbul ignore next */
    (cov_9rrh6chkn().s[12]++, this.memoryCache.get(key));
    /* istanbul ignore next */
    cov_9rrh6chkn().s[13]++;
    if (
    /* istanbul ignore next */
    (cov_9rrh6chkn().b[2][0]++, memoryEntry) &&
    /* istanbul ignore next */
    (cov_9rrh6chkn().b[2][1]++, this.isValidEntry(memoryEntry))) {
      /* istanbul ignore next */
      cov_9rrh6chkn().b[1][0]++;
      cov_9rrh6chkn().s[14]++;
      this.updateAccessMetrics(memoryEntry);
      /* istanbul ignore next */
      cov_9rrh6chkn().s[15]++;
      this.metrics.totalHits++;
      /* istanbul ignore next */
      cov_9rrh6chkn().s[16]++;
      this.updateHitRate();
      /* istanbul ignore next */
      cov_9rrh6chkn().s[17]++;
      return memoryEntry.value;
    } else
    /* istanbul ignore next */
    {
      cov_9rrh6chkn().b[1][1]++;
    }
    // Check IndexedDB cache
    cov_9rrh6chkn().s[18]++;
    try {
      const dbEntry =
      /* istanbul ignore next */
      (cov_9rrh6chkn().s[19]++, await this.database.cacheEntries.get(key));
      /* istanbul ignore next */
      cov_9rrh6chkn().s[20]++;
      if (
      /* istanbul ignore next */
      (cov_9rrh6chkn().b[4][0]++, dbEntry) &&
      /* istanbul ignore next */
      (cov_9rrh6chkn().b[4][1]++, this.isValidEntry(dbEntry))) {
        /* istanbul ignore next */
        cov_9rrh6chkn().b[3][0]++;
        cov_9rrh6chkn().s[21]++;
        // Promote to memory cache
        await this.setMemoryCache(key, dbEntry.value, dbEntry.ttl);
        /* istanbul ignore next */
        cov_9rrh6chkn().s[22]++;
        this.metrics.totalHits++;
        /* istanbul ignore next */
        cov_9rrh6chkn().s[23]++;
        this.updateHitRate();
        /* istanbul ignore next */
        cov_9rrh6chkn().s[24]++;
        return dbEntry.value;
      } else
      /* istanbul ignore next */
      {
        cov_9rrh6chkn().b[3][1]++;
      }
    } catch (error) {
      /* istanbul ignore next */
      cov_9rrh6chkn().s[25]++;
      console.warn('IndexedDB cache read error:', error);
    }
    /* istanbul ignore next */
    cov_9rrh6chkn().s[26]++;
    this.metrics.totalMisses++;
    /* istanbul ignore next */
    cov_9rrh6chkn().s[27]++;
    this.updateHitRate();
    /* istanbul ignore next */
    cov_9rrh6chkn().s[28]++;
    return null;
  }
  async set(key, value, ttl =
  /* istanbul ignore next */
  (cov_9rrh6chkn().b[5][0]++, this.config.defaultTTL)) {
    /* istanbul ignore next */
    cov_9rrh6chkn().f[2]++;
    const size =
    /* istanbul ignore next */
    (cov_9rrh6chkn().s[29]++, this.calculateSize(value));
    // Set in memory cache
    /* istanbul ignore next */
    cov_9rrh6chkn().s[30]++;
    await this.setMemoryCache(key, value, ttl, size);
    // Set in IndexedDB cache (async)
    /* istanbul ignore next */
    cov_9rrh6chkn().s[31]++;
    this.setIndexedDBCache(key, value, ttl, size).catch(error => {
      /* istanbul ignore next */
      cov_9rrh6chkn().f[3]++;
      cov_9rrh6chkn().s[32]++;
      console.warn('IndexedDB cache write error:', error);
    });
  }
  async delete(key) {
    /* istanbul ignore next */
    cov_9rrh6chkn().f[4]++;
    cov_9rrh6chkn().s[33]++;
    this.memoryCache.delete(key);
    /* istanbul ignore next */
    cov_9rrh6chkn().s[34]++;
    this.accessOrder.delete(key);
    /* istanbul ignore next */
    cov_9rrh6chkn().s[35]++;
    try {
      /* istanbul ignore next */
      cov_9rrh6chkn().s[36]++;
      await this.database.cacheEntries.delete(key);
    } catch (error) {
      /* istanbul ignore next */
      cov_9rrh6chkn().s[37]++;
      console.warn('IndexedDB cache delete error:', error);
    }
  }
  async clear() {
    /* istanbul ignore next */
    cov_9rrh6chkn().f[5]++;
    cov_9rrh6chkn().s[38]++;
    this.memoryCache.clear();
    /* istanbul ignore next */
    cov_9rrh6chkn().s[39]++;
    this.accessOrder.clear();
    /* istanbul ignore next */
    cov_9rrh6chkn().s[40]++;
    try {
      /* istanbul ignore next */
      cov_9rrh6chkn().s[41]++;
      await this.database.cacheEntries.clear();
    } catch (error) {
      /* istanbul ignore next */
      cov_9rrh6chkn().s[42]++;
      console.warn('IndexedDB cache clear error:', error);
    }
    /* istanbul ignore next */
    cov_9rrh6chkn().s[43]++;
    this.resetMetrics();
  }
  // =============================================================================
  // Specialized Caching Methods
  // =============================================================================
  async cacheCalculationResult(projectUuid, inputHash, result) {
    /* istanbul ignore next */
    cov_9rrh6chkn().f[6]++;
    const key =
    /* istanbul ignore next */
    (cov_9rrh6chkn().s[44]++, `calc:${projectUuid}:${inputHash}`);
    /* istanbul ignore next */
    cov_9rrh6chkn().s[45]++;
    await this.set(key, result, 60 * 60 * 1000); // 1 hour TTL for calculations
  }
  async getCachedCalculation(projectUuid, inputHash) {
    /* istanbul ignore next */
    cov_9rrh6chkn().f[7]++;
    const key =
    /* istanbul ignore next */
    (cov_9rrh6chkn().s[46]++, `calc:${projectUuid}:${inputHash}`);
    /* istanbul ignore next */
    cov_9rrh6chkn().s[47]++;
    return await this.get(key);
  }
  async prefetchProjectData(projectUuid) {
    /* istanbul ignore next */
    cov_9rrh6chkn().f[8]++;
    cov_9rrh6chkn().s[48]++;
    if (!this.config.prefetchEnabled) {
      /* istanbul ignore next */
      cov_9rrh6chkn().b[6][0]++;
      cov_9rrh6chkn().s[49]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_9rrh6chkn().b[6][1]++;
    }
    const strategy =
    /* istanbul ignore next */
    (cov_9rrh6chkn().s[50]++, this.prefetchStrategies.find(s => {
      /* istanbul ignore next */
      cov_9rrh6chkn().f[9]++;
      cov_9rrh6chkn().s[51]++;
      return s.type === 'project';
    }));
    /* istanbul ignore next */
    cov_9rrh6chkn().s[52]++;
    if (!strategy) {
      /* istanbul ignore next */
      cov_9rrh6chkn().b[7][0]++;
      cov_9rrh6chkn().s[53]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_9rrh6chkn().b[7][1]++;
    }
    cov_9rrh6chkn().s[54]++;
    try {
      // Prefetch common calculations for this project
      const recentCalculations =
      /* istanbul ignore next */
      (cov_9rrh6chkn().s[55]++, await this.database.calculations.where('projectUuid').equals(projectUuid).reverse().limit(strategy.maxPrefetch).toArray());
      /* istanbul ignore next */
      cov_9rrh6chkn().s[56]++;
      for (const calc of recentCalculations) {
        const key =
        /* istanbul ignore next */
        (cov_9rrh6chkn().s[57]++, `calc:${projectUuid}:${this.hashInput(calc.inputData)}`);
        /* istanbul ignore next */
        cov_9rrh6chkn().s[58]++;
        if (!this.memoryCache.has(key)) {
          /* istanbul ignore next */
          cov_9rrh6chkn().b[8][0]++;
          cov_9rrh6chkn().s[59]++;
          await this.set(key, calc.result, this.config.defaultTTL);
        } else
        /* istanbul ignore next */
        {
          cov_9rrh6chkn().b[8][1]++;
        }
      }
    } catch (error) {
      /* istanbul ignore next */
      cov_9rrh6chkn().s[60]++;
      console.warn('Prefetch error:', error);
    }
  }
  // =============================================================================
  // Cache Management and Optimization
  // =============================================================================
  async setMemoryCache(key, value, ttl, size) {
    /* istanbul ignore next */
    cov_9rrh6chkn().f[10]++;
    const entrySize =
    /* istanbul ignore next */
    (cov_9rrh6chkn().s[61]++,
    /* istanbul ignore next */
    (cov_9rrh6chkn().b[9][0]++, size) ||
    /* istanbul ignore next */
    (cov_9rrh6chkn().b[9][1]++, this.calculateSize(value)));
    const entry =
    /* istanbul ignore next */
    (cov_9rrh6chkn().s[62]++, {
      key,
      value,
      timestamp: Date.now(),
      ttl,
      accessCount: 1,
      lastAccessed: Date.now(),
      size: entrySize,
      compressed: false
    });
    // Check if we need to evict entries
    /* istanbul ignore next */
    cov_9rrh6chkn().s[63]++;
    await this.ensureMemoryCapacity(entrySize);
    /* istanbul ignore next */
    cov_9rrh6chkn().s[64]++;
    this.memoryCache.set(key, entry);
    /* istanbul ignore next */
    cov_9rrh6chkn().s[65]++;
    this.accessOrder.set(key, ++this.accessCounter);
    /* istanbul ignore next */
    cov_9rrh6chkn().s[66]++;
    this.updateMemoryUsage();
  }
  async setIndexedDBCache(key, value, ttl, size) {
    /* istanbul ignore next */
    cov_9rrh6chkn().f[11]++;
    cov_9rrh6chkn().s[67]++;
    try {
      const entry =
      /* istanbul ignore next */
      (cov_9rrh6chkn().s[68]++, {
        key,
        value,
        timestamp: Date.now(),
        ttl,
        accessCount: 1,
        lastAccessed: Date.now(),
        size,
        compressed: false
      });
      /* istanbul ignore next */
      cov_9rrh6chkn().s[69]++;
      await this.database.cacheEntries.put(entry);
    } catch (error) {
      /* istanbul ignore next */
      cov_9rrh6chkn().s[70]++;
      console.warn('IndexedDB cache write failed:', error);
    }
  }
  async ensureMemoryCapacity(requiredSize) {
    /* istanbul ignore next */
    cov_9rrh6chkn().f[12]++;
    const maxSizeBytes =
    /* istanbul ignore next */
    (cov_9rrh6chkn().s[71]++, this.config.maxMemorySize * 1024 * 1024);
    let currentSize =
    /* istanbul ignore next */
    (cov_9rrh6chkn().s[72]++, this.getCurrentMemoryUsage());
    /* istanbul ignore next */
    cov_9rrh6chkn().s[73]++;
    while (
    /* istanbul ignore next */
    (cov_9rrh6chkn().b[10][0]++, currentSize + requiredSize > maxSizeBytes) &&
    /* istanbul ignore next */
    (cov_9rrh6chkn().b[10][1]++, this.memoryCache.size > 0)) {
      const lruKey =
      /* istanbul ignore next */
      (cov_9rrh6chkn().s[74]++, this.findLRUKey());
      /* istanbul ignore next */
      cov_9rrh6chkn().s[75]++;
      if (lruKey) {
        /* istanbul ignore next */
        cov_9rrh6chkn().b[11][0]++;
        const entry =
        /* istanbul ignore next */
        (cov_9rrh6chkn().s[76]++, this.memoryCache.get(lruKey));
        /* istanbul ignore next */
        cov_9rrh6chkn().s[77]++;
        if (entry) {
          /* istanbul ignore next */
          cov_9rrh6chkn().b[12][0]++;
          cov_9rrh6chkn().s[78]++;
          currentSize -= entry.size;
          /* istanbul ignore next */
          cov_9rrh6chkn().s[79]++;
          this.memoryCache.delete(lruKey);
          /* istanbul ignore next */
          cov_9rrh6chkn().s[80]++;
          this.accessOrder.delete(lruKey);
          /* istanbul ignore next */
          cov_9rrh6chkn().s[81]++;
          this.metrics.evictionCount++;
        } else
        /* istanbul ignore next */
        {
          cov_9rrh6chkn().b[12][1]++;
        }
      } else {
        /* istanbul ignore next */
        cov_9rrh6chkn().b[11][1]++;
        cov_9rrh6chkn().s[82]++;
        break;
      }
    }
  }
  findLRUKey() {
    /* istanbul ignore next */
    cov_9rrh6chkn().f[13]++;
    let lruKey =
    /* istanbul ignore next */
    (cov_9rrh6chkn().s[83]++, null);
    let oldestAccess =
    /* istanbul ignore next */
    (cov_9rrh6chkn().s[84]++, Infinity);
    /* istanbul ignore next */
    cov_9rrh6chkn().s[85]++;
    for (const [key, accessTime] of this.accessOrder) {
      /* istanbul ignore next */
      cov_9rrh6chkn().s[86]++;
      if (accessTime < oldestAccess) {
        /* istanbul ignore next */
        cov_9rrh6chkn().b[13][0]++;
        cov_9rrh6chkn().s[87]++;
        oldestAccess = accessTime;
        /* istanbul ignore next */
        cov_9rrh6chkn().s[88]++;
        lruKey = key;
      } else
      /* istanbul ignore next */
      {
        cov_9rrh6chkn().b[13][1]++;
      }
    }
    /* istanbul ignore next */
    cov_9rrh6chkn().s[89]++;
    return lruKey;
  }
  isValidEntry(entry) {
    /* istanbul ignore next */
    cov_9rrh6chkn().f[14]++;
    const now =
    /* istanbul ignore next */
    (cov_9rrh6chkn().s[90]++, Date.now());
    /* istanbul ignore next */
    cov_9rrh6chkn().s[91]++;
    return now - entry.timestamp < entry.ttl;
  }
  updateAccessMetrics(entry) {
    /* istanbul ignore next */
    cov_9rrh6chkn().f[15]++;
    cov_9rrh6chkn().s[92]++;
    entry.accessCount++;
    /* istanbul ignore next */
    cov_9rrh6chkn().s[93]++;
    entry.lastAccessed = Date.now();
    /* istanbul ignore next */
    cov_9rrh6chkn().s[94]++;
    this.accessOrder.set(entry.key, ++this.accessCounter);
  }
  calculateSize(value) {
    /* istanbul ignore next */
    cov_9rrh6chkn().f[16]++;
    cov_9rrh6chkn().s[95]++;
    return new Blob([JSON.stringify(value)]).size;
  }
  getCurrentMemoryUsage() {
    /* istanbul ignore next */
    cov_9rrh6chkn().f[17]++;
    let total =
    /* istanbul ignore next */
    (cov_9rrh6chkn().s[96]++, 0);
    /* istanbul ignore next */
    cov_9rrh6chkn().s[97]++;
    for (const entry of this.memoryCache.values()) {
      /* istanbul ignore next */
      cov_9rrh6chkn().s[98]++;
      total += entry.size;
    }
    /* istanbul ignore next */
    cov_9rrh6chkn().s[99]++;
    return total;
  }
  updateMemoryUsage() {
    /* istanbul ignore next */
    cov_9rrh6chkn().f[18]++;
    cov_9rrh6chkn().s[100]++;
    this.metrics.memoryUsage = this.getCurrentMemoryUsage();
  }
  updateHitRate() {
    /* istanbul ignore next */
    cov_9rrh6chkn().f[19]++;
    cov_9rrh6chkn().s[101]++;
    this.metrics.hitRate = this.metrics.totalHits / this.metrics.totalRequests;
    /* istanbul ignore next */
    cov_9rrh6chkn().s[102]++;
    this.metrics.missRate = this.metrics.totalMisses / this.metrics.totalRequests;
  }
  resetMetrics() {
    /* istanbul ignore next */
    cov_9rrh6chkn().f[20]++;
    cov_9rrh6chkn().s[103]++;
    this.metrics = {
      hitRate: 0,
      missRate: 0,
      totalRequests: 0,
      totalHits: 0,
      totalMisses: 0,
      memoryUsage: 0,
      indexedDBUsage: 0,
      evictionCount: 0,
      compressionRatio: 0
    };
  }
  hashInput(input) {
    /* istanbul ignore next */
    cov_9rrh6chkn().f[21]++;
    cov_9rrh6chkn().s[104]++;
    return btoa(JSON.stringify(input)).replace(/[^a-zA-Z0-9]/g, '').substring(0, 16);
  }
  initializeCompressionWorker() {
    /* istanbul ignore next */
    cov_9rrh6chkn().f[22]++;
    cov_9rrh6chkn().s[105]++;
    if (
    /* istanbul ignore next */
    (cov_9rrh6chkn().b[15][0]++, this.config.compressionEnabled) &&
    /* istanbul ignore next */
    (cov_9rrh6chkn().b[15][1]++, typeof Worker !== 'undefined')) {
      /* istanbul ignore next */
      cov_9rrh6chkn().b[14][0]++;
    } // Initialize compression worker for large cache entries
    // Implementation would go here
    else
    /* istanbul ignore next */
    {
      cov_9rrh6chkn().b[14][1]++;
    }
  }
  startMemoryPressureMonitoring() {
    /* istanbul ignore next */
    cov_9rrh6chkn().f[23]++;
    cov_9rrh6chkn().s[106]++;
    if (
    /* istanbul ignore next */
    (cov_9rrh6chkn().b[17][0]++, typeof navigator !== 'undefined') &&
    /* istanbul ignore next */
    (cov_9rrh6chkn().b[17][1]++, 'memory' in navigator)) {
      /* istanbul ignore next */
      cov_9rrh6chkn().b[16][0]++;
      cov_9rrh6chkn().s[107]++;
      setInterval(() => {
        /* istanbul ignore next */
        cov_9rrh6chkn().f[24]++;
        const memory =
        /* istanbul ignore next */
        (cov_9rrh6chkn().s[108]++, navigator.memory);
        /* istanbul ignore next */
        cov_9rrh6chkn().s[109]++;
        if (
        /* istanbul ignore next */
        (cov_9rrh6chkn().b[19][0]++, memory) &&
        /* istanbul ignore next */
        (cov_9rrh6chkn().b[19][1]++, memory.usedJSHeapSize > memory.totalJSHeapSize * 0.8)) {
          /* istanbul ignore next */
          cov_9rrh6chkn().b[18][0]++;
          cov_9rrh6chkn().s[110]++;
          // High memory pressure - aggressive cache cleanup
          this.performEmergencyCleanup();
        } else
        /* istanbul ignore next */
        {
          cov_9rrh6chkn().b[18][1]++;
        }
      }, 30000); // Check every 30 seconds
    } else
    /* istanbul ignore next */
    {
      cov_9rrh6chkn().b[16][1]++;
    }
  }
  async performEmergencyCleanup() {
    /* istanbul ignore next */
    cov_9rrh6chkn().f[25]++;
    const targetSize =
    /* istanbul ignore next */
    (cov_9rrh6chkn().s[111]++, this.config.maxMemorySize * 0.5 * 1024 * 1024); // 50% of max
    let currentSize =
    /* istanbul ignore next */
    (cov_9rrh6chkn().s[112]++, this.getCurrentMemoryUsage());
    /* istanbul ignore next */
    cov_9rrh6chkn().s[113]++;
    while (
    /* istanbul ignore next */
    (cov_9rrh6chkn().b[20][0]++, currentSize > targetSize) &&
    /* istanbul ignore next */
    (cov_9rrh6chkn().b[20][1]++, this.memoryCache.size > 0)) {
      const lruKey =
      /* istanbul ignore next */
      (cov_9rrh6chkn().s[114]++, this.findLRUKey());
      /* istanbul ignore next */
      cov_9rrh6chkn().s[115]++;
      if (lruKey) {
        /* istanbul ignore next */
        cov_9rrh6chkn().b[21][0]++;
        const entry =
        /* istanbul ignore next */
        (cov_9rrh6chkn().s[116]++, this.memoryCache.get(lruKey));
        /* istanbul ignore next */
        cov_9rrh6chkn().s[117]++;
        if (entry) {
          /* istanbul ignore next */
          cov_9rrh6chkn().b[22][0]++;
          cov_9rrh6chkn().s[118]++;
          currentSize -= entry.size;
          /* istanbul ignore next */
          cov_9rrh6chkn().s[119]++;
          this.memoryCache.delete(lruKey);
          /* istanbul ignore next */
          cov_9rrh6chkn().s[120]++;
          this.accessOrder.delete(lruKey);
          /* istanbul ignore next */
          cov_9rrh6chkn().s[121]++;
          this.metrics.evictionCount++;
        } else
        /* istanbul ignore next */
        {
          cov_9rrh6chkn().b[22][1]++;
        }
      } else {
        /* istanbul ignore next */
        cov_9rrh6chkn().b[21][1]++;
        cov_9rrh6chkn().s[122]++;
        break;
      }
    }
  }
  // =============================================================================
  // Public API for Metrics and Management
  // =============================================================================
  getMetrics() {
    /* istanbul ignore next */
    cov_9rrh6chkn().f[26]++;
    cov_9rrh6chkn().s[123]++;
    return {
      ...this.metrics
    };
  }
  addPrefetchStrategy(strategy) {
    /* istanbul ignore next */
    cov_9rrh6chkn().f[27]++;
    cov_9rrh6chkn().s[124]++;
    this.prefetchStrategies.push(strategy);
  }
  async warmCache(keys) {
    /* istanbul ignore next */
    cov_9rrh6chkn().f[28]++;
    cov_9rrh6chkn().s[125]++;
    // Implementation for cache warming
    for (const key of keys) {
      /* istanbul ignore next */
      cov_9rrh6chkn().s[126]++;
      await this.get(key); // This will populate cache if data exists
    }
  }
}
/* istanbul ignore next */
cov_9rrh6chkn().s[127]++;
exports.AdvancedCachingService = AdvancedCachingService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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