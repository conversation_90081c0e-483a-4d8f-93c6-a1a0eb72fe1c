{"version": 3, "names": ["cov_9rrh6chkn", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "name", "decl", "loc", "branchMap", "type", "locations", "undefined", "s", "f", "b", "inputSourceMap", "file", "mappings", "names", "sources", "sourcesContent", "version", "_coverageSchema", "coverage", "actualCoverage", "AdvancedCachingService", "constructor", "database", "config", "memoryCache", "Map", "accessOrder", "prefetchStrategies", "accessCounter", "maxMemorySize", "defaultTTL", "maxIndexedDBSize", "compressionEnabled", "prefetchEnabled", "metricsEnabled", "metrics", "hitRate", "missRate", "totalRequests", "totalHits", "totalMisses", "memoryUsage", "indexedDBUsage", "evictionCount", "compressionRatio", "initializeCompressionWorker", "startMemoryPressureMonitoring", "get", "key", "memoryEntry", "isValidEntry", "updateAccessMetrics", "updateHitRate", "value", "db<PERSON><PERSON><PERSON>", "cacheEntries", "set<PERSON><PERSON>ory<PERSON>ache", "ttl", "error", "console", "warn", "set", "size", "calculateSize", "setIndexedDBCache", "catch", "delete", "clear", "resetMetrics", "cacheCalculationResult", "projectUuid", "inputHash", "result", "getCachedCalculation", "prefetchProjectData", "strategy", "find", "recentCalculations", "calculations", "where", "equals", "reverse", "limit", "max<PERSON><PERSON><PERSON>tch", "toArray", "calc", "hashInput", "inputData", "has", "entrySize", "entry", "timestamp", "Date", "now", "accessCount", "lastAccessed", "compressed", "ensureMemoryCapacity", "updateMemoryUsage", "put", "requiredSize", "maxSizeBytes", "currentSize", "getCurrentMemoryUsage", "lruKey", "findLRUKey", "oldestAccess", "Infinity", "accessTime", "Blob", "JSON", "stringify", "total", "values", "input", "btoa", "replace", "substring", "Worker", "navigator", "setInterval", "memory", "usedJSHeapSize", "totalJSHeapSize", "performEmergencyCleanup", "targetSize", "getMetrics", "addPrefetchStrategy", "push", "warmCache", "keys", "exports"], "sources": ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\frontend\\lib\\services\\AdvancedCachingService.ts"], "sourcesContent": ["/**\r\n * Advanced Caching Service for SizeWise Suite\r\n * \r\n * Implements intelligent caching algorithms with:\r\n * - LRU (Least Recently Used) cache eviction\r\n * - TTL (Time To Live) expiration\r\n * - Cache warming and prefetching\r\n * - Memory pressure management\r\n * - Performance metrics and monitoring\r\n * - Multi-tier caching (memory + IndexedDB)\r\n */\r\n\r\nimport { SizeWiseDatabase } from '../database/DexieDatabase';\r\nimport { CalculationResult } from '@/types/air-duct-sizer';\r\n\r\n// =============================================================================\r\n// Cache Configuration and Types\r\n// =============================================================================\r\n\r\nexport interface CacheConfig {\r\n  maxMemorySize: number; // Maximum memory cache size in MB\r\n  defaultTTL: number; // Default TTL in milliseconds\r\n  maxIndexedDBSize: number; // Maximum IndexedDB cache size in MB\r\n  compressionEnabled: boolean;\r\n  prefetchEnabled: boolean;\r\n  metricsEnabled: boolean;\r\n}\r\n\r\nexport interface CacheEntry<T = any> {\r\n  key: string;\r\n  value: T;\r\n  timestamp: number;\r\n  ttl: number;\r\n  accessCount: number;\r\n  lastAccessed: number;\r\n  size: number; // Size in bytes\r\n  compressed: boolean;\r\n}\r\n\r\nexport interface CacheMetrics {\r\n  hitRate: number;\r\n  missRate: number;\r\n  totalRequests: number;\r\n  totalHits: number;\r\n  totalMisses: number;\r\n  memoryUsage: number;\r\n  indexedDBUsage: number;\r\n  evictionCount: number;\r\n  compressionRatio: number;\r\n}\r\n\r\nexport interface PrefetchStrategy {\r\n  type: 'calculation' | 'project' | 'spatial';\r\n  patterns: string[];\r\n  priority: number;\r\n  maxPrefetch: number;\r\n}\r\n\r\n// =============================================================================\r\n// Advanced Caching Service Implementation\r\n// =============================================================================\r\n\r\nexport class AdvancedCachingService {\r\n  private memoryCache = new Map<string, CacheEntry>();\r\n  private accessOrder = new Map<string, number>(); // For LRU tracking\r\n  private database: SizeWiseDatabase;\r\n  private config: CacheConfig;\r\n  private metrics: CacheMetrics;\r\n  private prefetchStrategies: PrefetchStrategy[] = [];\r\n  private compressionWorker?: Worker;\r\n  private accessCounter = 0;\r\n\r\n  constructor(database: SizeWiseDatabase, config: Partial<CacheConfig> = {}) {\r\n    this.database = database;\r\n    this.config = {\r\n      maxMemorySize: 50, // 50MB default\r\n      defaultTTL: 30 * 60 * 1000, // 30 minutes\r\n      maxIndexedDBSize: 200, // 200MB default\r\n      compressionEnabled: true,\r\n      prefetchEnabled: true,\r\n      metricsEnabled: true,\r\n      ...config\r\n    };\r\n\r\n    this.metrics = {\r\n      hitRate: 0,\r\n      missRate: 0,\r\n      totalRequests: 0,\r\n      totalHits: 0,\r\n      totalMisses: 0,\r\n      memoryUsage: 0,\r\n      indexedDBUsage: 0,\r\n      evictionCount: 0,\r\n      compressionRatio: 0\r\n    };\r\n\r\n    this.initializeCompressionWorker();\r\n    this.startMemoryPressureMonitoring();\r\n  }\r\n\r\n  // =============================================================================\r\n  // Core Cache Operations\r\n  // =============================================================================\r\n\r\n  async get<T>(key: string): Promise<T | null> {\r\n    this.metrics.totalRequests++;\r\n\r\n    // Check memory cache first\r\n    const memoryEntry = this.memoryCache.get(key);\r\n    if (memoryEntry && this.isValidEntry(memoryEntry)) {\r\n      this.updateAccessMetrics(memoryEntry);\r\n      this.metrics.totalHits++;\r\n      this.updateHitRate();\r\n      return memoryEntry.value as T;\r\n    }\r\n\r\n    // Check IndexedDB cache\r\n    try {\r\n      const dbEntry = await this.database.cacheEntries.get(key);\r\n      if (dbEntry && this.isValidEntry(dbEntry)) {\r\n        // Promote to memory cache\r\n        await this.setMemoryCache(key, dbEntry.value, dbEntry.ttl);\r\n        this.metrics.totalHits++;\r\n        this.updateHitRate();\r\n        return dbEntry.value as T;\r\n      }\r\n    } catch (error) {\r\n      console.warn('IndexedDB cache read error:', error);\r\n    }\r\n\r\n    this.metrics.totalMisses++;\r\n    this.updateHitRate();\r\n    return null;\r\n  }\r\n\r\n  async set<T>(\r\n    key: string, \r\n    value: T, \r\n    ttl: number = this.config.defaultTTL\r\n  ): Promise<void> {\r\n    const size = this.calculateSize(value);\r\n    \r\n    // Set in memory cache\r\n    await this.setMemoryCache(key, value, ttl, size);\r\n    \r\n    // Set in IndexedDB cache (async)\r\n    this.setIndexedDBCache(key, value, ttl, size).catch(error => {\r\n      console.warn('IndexedDB cache write error:', error);\r\n    });\r\n  }\r\n\r\n  async delete(key: string): Promise<void> {\r\n    this.memoryCache.delete(key);\r\n    this.accessOrder.delete(key);\r\n    \r\n    try {\r\n      await this.database.cacheEntries.delete(key);\r\n    } catch (error) {\r\n      console.warn('IndexedDB cache delete error:', error);\r\n    }\r\n  }\r\n\r\n  async clear(): Promise<void> {\r\n    this.memoryCache.clear();\r\n    this.accessOrder.clear();\r\n    \r\n    try {\r\n      await this.database.cacheEntries.clear();\r\n    } catch (error) {\r\n      console.warn('IndexedDB cache clear error:', error);\r\n    }\r\n    \r\n    this.resetMetrics();\r\n  }\r\n\r\n  // =============================================================================\r\n  // Specialized Caching Methods\r\n  // =============================================================================\r\n\r\n  async cacheCalculationResult(\r\n    projectUuid: string,\r\n    inputHash: string,\r\n    result: CalculationResult\r\n  ): Promise<void> {\r\n    const key = `calc:${projectUuid}:${inputHash}`;\r\n    await this.set(key, result, 60 * 60 * 1000); // 1 hour TTL for calculations\r\n  }\r\n\r\n  async getCachedCalculation(\r\n    projectUuid: string,\r\n    inputHash: string\r\n  ): Promise<CalculationResult | null> {\r\n    const key = `calc:${projectUuid}:${inputHash}`;\r\n    return await this.get<CalculationResult>(key);\r\n  }\r\n\r\n  async prefetchProjectData(projectUuid: string): Promise<void> {\r\n    if (!this.config.prefetchEnabled) return;\r\n\r\n    const strategy = this.prefetchStrategies.find(s => s.type === 'project');\r\n    if (!strategy) return;\r\n\r\n    try {\r\n      // Prefetch common calculations for this project\r\n      const recentCalculations = await this.database.calculations\r\n        .where('projectUuid')\r\n        .equals(projectUuid)\r\n        .reverse()\r\n        .limit(strategy.maxPrefetch)\r\n        .toArray();\r\n\r\n      for (const calc of recentCalculations) {\r\n        const key = `calc:${projectUuid}:${this.hashInput(calc.inputData)}`;\r\n        if (!this.memoryCache.has(key)) {\r\n          await this.set(key, calc.result, this.config.defaultTTL);\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.warn('Prefetch error:', error);\r\n    }\r\n  }\r\n\r\n  // =============================================================================\r\n  // Cache Management and Optimization\r\n  // =============================================================================\r\n\r\n  private async setMemoryCache<T>(\r\n    key: string,\r\n    value: T,\r\n    ttl: number,\r\n    size?: number\r\n  ): Promise<void> {\r\n    const entrySize = size || this.calculateSize(value);\r\n    const entry: CacheEntry<T> = {\r\n      key,\r\n      value,\r\n      timestamp: Date.now(),\r\n      ttl,\r\n      accessCount: 1,\r\n      lastAccessed: Date.now(),\r\n      size: entrySize,\r\n      compressed: false\r\n    };\r\n\r\n    // Check if we need to evict entries\r\n    await this.ensureMemoryCapacity(entrySize);\r\n\r\n    this.memoryCache.set(key, entry);\r\n    this.accessOrder.set(key, ++this.accessCounter);\r\n    this.updateMemoryUsage();\r\n  }\r\n\r\n  private async setIndexedDBCache<T>(\r\n    key: string,\r\n    value: T,\r\n    ttl: number,\r\n    size: number\r\n  ): Promise<void> {\r\n    try {\r\n      const entry: CacheEntry<T> = {\r\n        key,\r\n        value,\r\n        timestamp: Date.now(),\r\n        ttl,\r\n        accessCount: 1,\r\n        lastAccessed: Date.now(),\r\n        size,\r\n        compressed: false\r\n      };\r\n\r\n      await this.database.cacheEntries.put(entry);\r\n    } catch (error) {\r\n      console.warn('IndexedDB cache write failed:', error);\r\n    }\r\n  }\r\n\r\n  private async ensureMemoryCapacity(requiredSize: number): Promise<void> {\r\n    const maxSizeBytes = this.config.maxMemorySize * 1024 * 1024;\r\n    let currentSize = this.getCurrentMemoryUsage();\r\n\r\n    while (currentSize + requiredSize > maxSizeBytes && this.memoryCache.size > 0) {\r\n      const lruKey = this.findLRUKey();\r\n      if (lruKey) {\r\n        const entry = this.memoryCache.get(lruKey);\r\n        if (entry) {\r\n          currentSize -= entry.size;\r\n          this.memoryCache.delete(lruKey);\r\n          this.accessOrder.delete(lruKey);\r\n          this.metrics.evictionCount++;\r\n        }\r\n      } else {\r\n        break;\r\n      }\r\n    }\r\n  }\r\n\r\n  private findLRUKey(): string | null {\r\n    let lruKey: string | null = null;\r\n    let oldestAccess = Infinity;\r\n\r\n    for (const [key, accessTime] of this.accessOrder) {\r\n      if (accessTime < oldestAccess) {\r\n        oldestAccess = accessTime;\r\n        lruKey = key;\r\n      }\r\n    }\r\n\r\n    return lruKey;\r\n  }\r\n\r\n  private isValidEntry(entry: CacheEntry): boolean {\r\n    const now = Date.now();\r\n    return (now - entry.timestamp) < entry.ttl;\r\n  }\r\n\r\n  private updateAccessMetrics(entry: CacheEntry): void {\r\n    entry.accessCount++;\r\n    entry.lastAccessed = Date.now();\r\n    this.accessOrder.set(entry.key, ++this.accessCounter);\r\n  }\r\n\r\n  private calculateSize(value: any): number {\r\n    return new Blob([JSON.stringify(value)]).size;\r\n  }\r\n\r\n  private getCurrentMemoryUsage(): number {\r\n    let total = 0;\r\n    for (const entry of this.memoryCache.values()) {\r\n      total += entry.size;\r\n    }\r\n    return total;\r\n  }\r\n\r\n  private updateMemoryUsage(): void {\r\n    this.metrics.memoryUsage = this.getCurrentMemoryUsage();\r\n  }\r\n\r\n  private updateHitRate(): void {\r\n    this.metrics.hitRate = this.metrics.totalHits / this.metrics.totalRequests;\r\n    this.metrics.missRate = this.metrics.totalMisses / this.metrics.totalRequests;\r\n  }\r\n\r\n  private resetMetrics(): void {\r\n    this.metrics = {\r\n      hitRate: 0,\r\n      missRate: 0,\r\n      totalRequests: 0,\r\n      totalHits: 0,\r\n      totalMisses: 0,\r\n      memoryUsage: 0,\r\n      indexedDBUsage: 0,\r\n      evictionCount: 0,\r\n      compressionRatio: 0\r\n    };\r\n  }\r\n\r\n  private hashInput(input: any): string {\r\n    return btoa(JSON.stringify(input)).replace(/[^a-zA-Z0-9]/g, '').substring(0, 16);\r\n  }\r\n\r\n  private initializeCompressionWorker(): void {\r\n    if (this.config.compressionEnabled && typeof Worker !== 'undefined') {\r\n      // Initialize compression worker for large cache entries\r\n      // Implementation would go here\r\n    }\r\n  }\r\n\r\n  private startMemoryPressureMonitoring(): void {\r\n    if (typeof navigator !== 'undefined' && 'memory' in navigator) {\r\n      setInterval(() => {\r\n        const memory = (navigator as any).memory;\r\n        if (memory && memory.usedJSHeapSize > memory.totalJSHeapSize * 0.8) {\r\n          // High memory pressure - aggressive cache cleanup\r\n          this.performEmergencyCleanup();\r\n        }\r\n      }, 30000); // Check every 30 seconds\r\n    }\r\n  }\r\n\r\n  private async performEmergencyCleanup(): Promise<void> {\r\n    const targetSize = this.config.maxMemorySize * 0.5 * 1024 * 1024; // 50% of max\r\n    let currentSize = this.getCurrentMemoryUsage();\r\n\r\n    while (currentSize > targetSize && this.memoryCache.size > 0) {\r\n      const lruKey = this.findLRUKey();\r\n      if (lruKey) {\r\n        const entry = this.memoryCache.get(lruKey);\r\n        if (entry) {\r\n          currentSize -= entry.size;\r\n          this.memoryCache.delete(lruKey);\r\n          this.accessOrder.delete(lruKey);\r\n          this.metrics.evictionCount++;\r\n        }\r\n      } else {\r\n        break;\r\n      }\r\n    }\r\n  }\r\n\r\n  // =============================================================================\r\n  // Public API for Metrics and Management\r\n  // =============================================================================\r\n\r\n  getMetrics(): CacheMetrics {\r\n    return { ...this.metrics };\r\n  }\r\n\r\n  addPrefetchStrategy(strategy: PrefetchStrategy): void {\r\n    this.prefetchStrategies.push(strategy);\r\n  }\r\n\r\n  async warmCache(keys: string[]): Promise<void> {\r\n    // Implementation for cache warming\r\n    for (const key of keys) {\r\n      await this.get(key); // This will populate cache if data exists\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;AAAA;;;;;;;;;;;AAAA;AAAA,SAAAA,cAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;MAAA;QAAAC,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAO,SAAA;MAAA;QAAAD,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;IAAA;IAAAW,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,cAAA;MAAAC,IAAA;MAAAC,QAAA;MAAAC,KAAA;MAAAC,OAAA;MAAAC,cAAA;MAAAC,OAAA;IAAA;IAAAC,eAAA;IAAA5B,IAAA;EAAA;EAAA,IAAA6B,QAAA,GAAA5B,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAA0B,QAAA,CAAA9B,IAAA,KAAA8B,QAAA,CAAA9B,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAA6B,QAAA,CAAA9B,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAA0B,cAAA,GAAAD,QAAA,CAAA9B,IAAA;EAAA;IA2DA;IAAAD,aAAA,YAAAA,CAAA;MAAA,OAAAgC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAhC,aAAA;AAAAA,aAAA,GAAAoB,CAAA;;;;;;;AADA;AACA;AACA;AAEA,MAAaa,sBAAsB;EAUjCC,YAAYC,QAA0B,EAAEC,MAAA;EAAA;EAAA,CAAApC,aAAA,GAAAsB,CAAA,UAA+B,EAAE;IAAA;IAAAtB,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IATjE,KAAAiB,WAAW,GAAG,IAAIC,GAAG,EAAsB;IAAC;IAAAtC,aAAA,GAAAoB,CAAA;IAC5C,KAAAmB,WAAW,GAAG,IAAID,GAAG,EAAkB,CAAC,CAAC;IAAA;IAAAtC,aAAA,GAAAoB,CAAA;IAIzC,KAAAoB,kBAAkB,GAAuB,EAAE;IAAC;IAAAxC,aAAA,GAAAoB,CAAA;IAE5C,KAAAqB,aAAa,GAAG,CAAC;IAAC;IAAAzC,aAAA,GAAAoB,CAAA;IAGxB,IAAI,CAACe,QAAQ,GAAGA,QAAQ;IAAC;IAAAnC,aAAA,GAAAoB,CAAA;IACzB,IAAI,CAACgB,MAAM,GAAG;MACZM,aAAa,EAAE,EAAE;MAAE;MACnBC,UAAU,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;MAAE;MAC5BC,gBAAgB,EAAE,GAAG;MAAE;MACvBC,kBAAkB,EAAE,IAAI;MACxBC,eAAe,EAAE,IAAI;MACrBC,cAAc,EAAE,IAAI;MACpB,GAAGX;KACJ;IAAC;IAAApC,aAAA,GAAAoB,CAAA;IAEF,IAAI,CAAC4B,OAAO,GAAG;MACbC,OAAO,EAAE,CAAC;MACVC,QAAQ,EAAE,CAAC;MACXC,aAAa,EAAE,CAAC;MAChBC,SAAS,EAAE,CAAC;MACZC,WAAW,EAAE,CAAC;MACdC,WAAW,EAAE,CAAC;MACdC,cAAc,EAAE,CAAC;MACjBC,aAAa,EAAE,CAAC;MAChBC,gBAAgB,EAAE;KACnB;IAAC;IAAAzD,aAAA,GAAAoB,CAAA;IAEF,IAAI,CAACsC,2BAA2B,EAAE;IAAC;IAAA1D,aAAA,GAAAoB,CAAA;IACnC,IAAI,CAACuC,6BAA6B,EAAE;EACtC;EAEA;EACA;EACA;EAEA,MAAMC,GAAGA,CAAIC,GAAW;IAAA;IAAA7D,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACtB,IAAI,CAAC4B,OAAO,CAACG,aAAa,EAAE;IAE5B;IACA,MAAMW,WAAW;IAAA;IAAA,CAAA9D,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAACiB,WAAW,CAACuB,GAAG,CAACC,GAAG,CAAC;IAAC;IAAA7D,aAAA,GAAAoB,CAAA;IAC9C;IAAI;IAAA,CAAApB,aAAA,GAAAsB,CAAA,UAAAwC,WAAW;IAAA;IAAA,CAAA9D,aAAA,GAAAsB,CAAA,UAAI,IAAI,CAACyC,YAAY,CAACD,WAAW,CAAC,GAAE;MAAA;MAAA9D,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACjD,IAAI,CAAC4C,mBAAmB,CAACF,WAAW,CAAC;MAAC;MAAA9D,aAAA,GAAAoB,CAAA;MACtC,IAAI,CAAC4B,OAAO,CAACI,SAAS,EAAE;MAAC;MAAApD,aAAA,GAAAoB,CAAA;MACzB,IAAI,CAAC6C,aAAa,EAAE;MAAC;MAAAjE,aAAA,GAAAoB,CAAA;MACrB,OAAO0C,WAAW,CAACI,KAAU;IAC/B,CAAC;IAAA;IAAA;MAAAlE,aAAA,GAAAsB,CAAA;IAAA;IAED;IAAAtB,aAAA,GAAAoB,CAAA;IACA,IAAI;MACF,MAAM+C,OAAO;MAAA;MAAA,CAAAnE,aAAA,GAAAoB,CAAA,QAAG,MAAM,IAAI,CAACe,QAAQ,CAACiC,YAAY,CAACR,GAAG,CAACC,GAAG,CAAC;MAAC;MAAA7D,aAAA,GAAAoB,CAAA;MAC1D;MAAI;MAAA,CAAApB,aAAA,GAAAsB,CAAA,UAAA6C,OAAO;MAAA;MAAA,CAAAnE,aAAA,GAAAsB,CAAA,UAAI,IAAI,CAACyC,YAAY,CAACI,OAAO,CAAC,GAAE;QAAA;QAAAnE,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACzC;QACA,MAAM,IAAI,CAACiD,cAAc,CAACR,GAAG,EAAEM,OAAO,CAACD,KAAK,EAAEC,OAAO,CAACG,GAAG,CAAC;QAAC;QAAAtE,aAAA,GAAAoB,CAAA;QAC3D,IAAI,CAAC4B,OAAO,CAACI,SAAS,EAAE;QAAC;QAAApD,aAAA,GAAAoB,CAAA;QACzB,IAAI,CAAC6C,aAAa,EAAE;QAAC;QAAAjE,aAAA,GAAAoB,CAAA;QACrB,OAAO+C,OAAO,CAACD,KAAU;MAC3B,CAAC;MAAA;MAAA;QAAAlE,aAAA,GAAAsB,CAAA;MAAA;IACH,CAAC,CAAC,OAAOiD,KAAK,EAAE;MAAA;MAAAvE,aAAA,GAAAoB,CAAA;MACdoD,OAAO,CAACC,IAAI,CAAC,6BAA6B,EAAEF,KAAK,CAAC;IACpD;IAAC;IAAAvE,aAAA,GAAAoB,CAAA;IAED,IAAI,CAAC4B,OAAO,CAACK,WAAW,EAAE;IAAC;IAAArD,aAAA,GAAAoB,CAAA;IAC3B,IAAI,CAAC6C,aAAa,EAAE;IAAC;IAAAjE,aAAA,GAAAoB,CAAA;IACrB,OAAO,IAAI;EACb;EAEA,MAAMsD,GAAGA,CACPb,GAAW,EACXK,KAAQ,EACRI,GAAA;EAAA;EAAA,CAAAtE,aAAA,GAAAsB,CAAA,UAAc,IAAI,CAACc,MAAM,CAACO,UAAU;IAAA;IAAA3C,aAAA,GAAAqB,CAAA;IAEpC,MAAMsD,IAAI;IAAA;IAAA,CAAA3E,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAACwD,aAAa,CAACV,KAAK,CAAC;IAEtC;IAAA;IAAAlE,aAAA,GAAAoB,CAAA;IACA,MAAM,IAAI,CAACiD,cAAc,CAACR,GAAG,EAAEK,KAAK,EAAEI,GAAG,EAAEK,IAAI,CAAC;IAEhD;IAAA;IAAA3E,aAAA,GAAAoB,CAAA;IACA,IAAI,CAACyD,iBAAiB,CAAChB,GAAG,EAAEK,KAAK,EAAEI,GAAG,EAAEK,IAAI,CAAC,CAACG,KAAK,CAACP,KAAK,IAAG;MAAA;MAAAvE,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MAC1DoD,OAAO,CAACC,IAAI,CAAC,8BAA8B,EAAEF,KAAK,CAAC;IACrD,CAAC,CAAC;EACJ;EAEA,MAAMQ,MAAMA,CAAClB,GAAW;IAAA;IAAA7D,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACtB,IAAI,CAACiB,WAAW,CAAC0C,MAAM,CAAClB,GAAG,CAAC;IAAC;IAAA7D,aAAA,GAAAoB,CAAA;IAC7B,IAAI,CAACmB,WAAW,CAACwC,MAAM,CAAClB,GAAG,CAAC;IAAC;IAAA7D,aAAA,GAAAoB,CAAA;IAE7B,IAAI;MAAA;MAAApB,aAAA,GAAAoB,CAAA;MACF,MAAM,IAAI,CAACe,QAAQ,CAACiC,YAAY,CAACW,MAAM,CAAClB,GAAG,CAAC;IAC9C,CAAC,CAAC,OAAOU,KAAK,EAAE;MAAA;MAAAvE,aAAA,GAAAoB,CAAA;MACdoD,OAAO,CAACC,IAAI,CAAC,+BAA+B,EAAEF,KAAK,CAAC;IACtD;EACF;EAEA,MAAMS,KAAKA,CAAA;IAAA;IAAAhF,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACT,IAAI,CAACiB,WAAW,CAAC2C,KAAK,EAAE;IAAC;IAAAhF,aAAA,GAAAoB,CAAA;IACzB,IAAI,CAACmB,WAAW,CAACyC,KAAK,EAAE;IAAC;IAAAhF,aAAA,GAAAoB,CAAA;IAEzB,IAAI;MAAA;MAAApB,aAAA,GAAAoB,CAAA;MACF,MAAM,IAAI,CAACe,QAAQ,CAACiC,YAAY,CAACY,KAAK,EAAE;IAC1C,CAAC,CAAC,OAAOT,KAAK,EAAE;MAAA;MAAAvE,aAAA,GAAAoB,CAAA;MACdoD,OAAO,CAACC,IAAI,CAAC,8BAA8B,EAAEF,KAAK,CAAC;IACrD;IAAC;IAAAvE,aAAA,GAAAoB,CAAA;IAED,IAAI,CAAC6D,YAAY,EAAE;EACrB;EAEA;EACA;EACA;EAEA,MAAMC,sBAAsBA,CAC1BC,WAAmB,EACnBC,SAAiB,EACjBC,MAAyB;IAAA;IAAArF,aAAA,GAAAqB,CAAA;IAEzB,MAAMwC,GAAG;IAAA;IAAA,CAAA7D,aAAA,GAAAoB,CAAA,QAAG,QAAQ+D,WAAW,IAAIC,SAAS,EAAE;IAAC;IAAApF,aAAA,GAAAoB,CAAA;IAC/C,MAAM,IAAI,CAACsD,GAAG,CAACb,GAAG,EAAEwB,MAAM,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;EAC/C;EAEA,MAAMC,oBAAoBA,CACxBH,WAAmB,EACnBC,SAAiB;IAAA;IAAApF,aAAA,GAAAqB,CAAA;IAEjB,MAAMwC,GAAG;IAAA;IAAA,CAAA7D,aAAA,GAAAoB,CAAA,QAAG,QAAQ+D,WAAW,IAAIC,SAAS,EAAE;IAAC;IAAApF,aAAA,GAAAoB,CAAA;IAC/C,OAAO,MAAM,IAAI,CAACwC,GAAG,CAAoBC,GAAG,CAAC;EAC/C;EAEA,MAAM0B,mBAAmBA,CAACJ,WAAmB;IAAA;IAAAnF,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAC3C,IAAI,CAAC,IAAI,CAACgB,MAAM,CAACU,eAAe,EAAE;MAAA;MAAA9C,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA;IAAA,CAAO;IAAA;IAAA;MAAApB,aAAA,GAAAsB,CAAA;IAAA;IAEzC,MAAMkE,QAAQ;IAAA;IAAA,CAAAxF,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAACoB,kBAAkB,CAACiD,IAAI,CAACrE,CAAC,IAAI;MAAA;MAAApB,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MAAA,OAAAA,CAAC,CAACH,IAAI,KAAK,SAAS;IAAT,CAAS,CAAC;IAAC;IAAAjB,aAAA,GAAAoB,CAAA;IACzE,IAAI,CAACoE,QAAQ,EAAE;MAAA;MAAAxF,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA;IAAA,CAAO;IAAA;IAAA;MAAApB,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAEtB,IAAI;MACF;MACA,MAAMsE,kBAAkB;MAAA;MAAA,CAAA1F,aAAA,GAAAoB,CAAA,QAAG,MAAM,IAAI,CAACe,QAAQ,CAACwD,YAAY,CACxDC,KAAK,CAAC,aAAa,CAAC,CACpBC,MAAM,CAACV,WAAW,CAAC,CACnBW,OAAO,EAAE,CACTC,KAAK,CAACP,QAAQ,CAACQ,WAAW,CAAC,CAC3BC,OAAO,EAAE;MAAC;MAAAjG,aAAA,GAAAoB,CAAA;MAEb,KAAK,MAAM8E,IAAI,IAAIR,kBAAkB,EAAE;QACrC,MAAM7B,GAAG;QAAA;QAAA,CAAA7D,aAAA,GAAAoB,CAAA,QAAG,QAAQ+D,WAAW,IAAI,IAAI,CAACgB,SAAS,CAACD,IAAI,CAACE,SAAS,CAAC,EAAE;QAAC;QAAApG,aAAA,GAAAoB,CAAA;QACpE,IAAI,CAAC,IAAI,CAACiB,WAAW,CAACgE,GAAG,CAACxC,GAAG,CAAC,EAAE;UAAA;UAAA7D,aAAA,GAAAsB,CAAA;UAAAtB,aAAA,GAAAoB,CAAA;UAC9B,MAAM,IAAI,CAACsD,GAAG,CAACb,GAAG,EAAEqC,IAAI,CAACb,MAAM,EAAE,IAAI,CAACjD,MAAM,CAACO,UAAU,CAAC;QAC1D,CAAC;QAAA;QAAA;UAAA3C,aAAA,GAAAsB,CAAA;QAAA;MACH;IACF,CAAC,CAAC,OAAOiD,KAAK,EAAE;MAAA;MAAAvE,aAAA,GAAAoB,CAAA;MACdoD,OAAO,CAACC,IAAI,CAAC,iBAAiB,EAAEF,KAAK,CAAC;IACxC;EACF;EAEA;EACA;EACA;EAEQ,MAAMF,cAAcA,CAC1BR,GAAW,EACXK,KAAQ,EACRI,GAAW,EACXK,IAAa;IAAA;IAAA3E,aAAA,GAAAqB,CAAA;IAEb,MAAMiF,SAAS;IAAA;IAAA,CAAAtG,aAAA,GAAAoB,CAAA;IAAG;IAAA,CAAApB,aAAA,GAAAsB,CAAA,UAAAqD,IAAI;IAAA;IAAA,CAAA3E,aAAA,GAAAsB,CAAA,UAAI,IAAI,CAACsD,aAAa,CAACV,KAAK,CAAC;IACnD,MAAMqC,KAAK;IAAA;IAAA,CAAAvG,aAAA,GAAAoB,CAAA,QAAkB;MAC3ByC,GAAG;MACHK,KAAK;MACLsC,SAAS,EAAEC,IAAI,CAACC,GAAG,EAAE;MACrBpC,GAAG;MACHqC,WAAW,EAAE,CAAC;MACdC,YAAY,EAAEH,IAAI,CAACC,GAAG,EAAE;MACxB/B,IAAI,EAAE2B,SAAS;MACfO,UAAU,EAAE;KACb;IAED;IAAA;IAAA7G,aAAA,GAAAoB,CAAA;IACA,MAAM,IAAI,CAAC0F,oBAAoB,CAACR,SAAS,CAAC;IAAC;IAAAtG,aAAA,GAAAoB,CAAA;IAE3C,IAAI,CAACiB,WAAW,CAACqC,GAAG,CAACb,GAAG,EAAE0C,KAAK,CAAC;IAAC;IAAAvG,aAAA,GAAAoB,CAAA;IACjC,IAAI,CAACmB,WAAW,CAACmC,GAAG,CAACb,GAAG,EAAE,EAAE,IAAI,CAACpB,aAAa,CAAC;IAAC;IAAAzC,aAAA,GAAAoB,CAAA;IAChD,IAAI,CAAC2F,iBAAiB,EAAE;EAC1B;EAEQ,MAAMlC,iBAAiBA,CAC7BhB,GAAW,EACXK,KAAQ,EACRI,GAAW,EACXK,IAAY;IAAA;IAAA3E,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAEZ,IAAI;MACF,MAAMmF,KAAK;MAAA;MAAA,CAAAvG,aAAA,GAAAoB,CAAA,QAAkB;QAC3ByC,GAAG;QACHK,KAAK;QACLsC,SAAS,EAAEC,IAAI,CAACC,GAAG,EAAE;QACrBpC,GAAG;QACHqC,WAAW,EAAE,CAAC;QACdC,YAAY,EAAEH,IAAI,CAACC,GAAG,EAAE;QACxB/B,IAAI;QACJkC,UAAU,EAAE;OACb;MAAC;MAAA7G,aAAA,GAAAoB,CAAA;MAEF,MAAM,IAAI,CAACe,QAAQ,CAACiC,YAAY,CAAC4C,GAAG,CAACT,KAAK,CAAC;IAC7C,CAAC,CAAC,OAAOhC,KAAK,EAAE;MAAA;MAAAvE,aAAA,GAAAoB,CAAA;MACdoD,OAAO,CAACC,IAAI,CAAC,+BAA+B,EAAEF,KAAK,CAAC;IACtD;EACF;EAEQ,MAAMuC,oBAAoBA,CAACG,YAAoB;IAAA;IAAAjH,aAAA,GAAAqB,CAAA;IACrD,MAAM6F,YAAY;IAAA;IAAA,CAAAlH,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAACgB,MAAM,CAACM,aAAa,GAAG,IAAI,GAAG,IAAI;IAC5D,IAAIyE,WAAW;IAAA;IAAA,CAAAnH,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAACgG,qBAAqB,EAAE;IAAC;IAAApH,aAAA,GAAAoB,CAAA;IAE/C;IAAO;IAAA,CAAApB,aAAA,GAAAsB,CAAA,WAAA6F,WAAW,GAAGF,YAAY,GAAGC,YAAY;IAAA;IAAA,CAAAlH,aAAA,GAAAsB,CAAA,WAAI,IAAI,CAACe,WAAW,CAACsC,IAAI,GAAG,CAAC,GAAE;MAC7E,MAAM0C,MAAM;MAAA;MAAA,CAAArH,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAACkG,UAAU,EAAE;MAAC;MAAAtH,aAAA,GAAAoB,CAAA;MACjC,IAAIiG,MAAM,EAAE;QAAA;QAAArH,aAAA,GAAAsB,CAAA;QACV,MAAMiF,KAAK;QAAA;QAAA,CAAAvG,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAACiB,WAAW,CAACuB,GAAG,CAACyD,MAAM,CAAC;QAAC;QAAArH,aAAA,GAAAoB,CAAA;QAC3C,IAAImF,KAAK,EAAE;UAAA;UAAAvG,aAAA,GAAAsB,CAAA;UAAAtB,aAAA,GAAAoB,CAAA;UACT+F,WAAW,IAAIZ,KAAK,CAAC5B,IAAI;UAAC;UAAA3E,aAAA,GAAAoB,CAAA;UAC1B,IAAI,CAACiB,WAAW,CAAC0C,MAAM,CAACsC,MAAM,CAAC;UAAC;UAAArH,aAAA,GAAAoB,CAAA;UAChC,IAAI,CAACmB,WAAW,CAACwC,MAAM,CAACsC,MAAM,CAAC;UAAC;UAAArH,aAAA,GAAAoB,CAAA;UAChC,IAAI,CAAC4B,OAAO,CAACQ,aAAa,EAAE;QAC9B,CAAC;QAAA;QAAA;UAAAxD,aAAA,GAAAsB,CAAA;QAAA;MACH,CAAC,MAAM;QAAA;QAAAtB,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACL;MACF;IACF;EACF;EAEQkG,UAAUA,CAAA;IAAA;IAAAtH,aAAA,GAAAqB,CAAA;IAChB,IAAIgG,MAAM;IAAA;IAAA,CAAArH,aAAA,GAAAoB,CAAA,QAAkB,IAAI;IAChC,IAAImG,YAAY;IAAA;IAAA,CAAAvH,aAAA,GAAAoB,CAAA,QAAGoG,QAAQ;IAAC;IAAAxH,aAAA,GAAAoB,CAAA;IAE5B,KAAK,MAAM,CAACyC,GAAG,EAAE4D,UAAU,CAAC,IAAI,IAAI,CAAClF,WAAW,EAAE;MAAA;MAAAvC,aAAA,GAAAoB,CAAA;MAChD,IAAIqG,UAAU,GAAGF,YAAY,EAAE;QAAA;QAAAvH,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QAC7BmG,YAAY,GAAGE,UAAU;QAAC;QAAAzH,aAAA,GAAAoB,CAAA;QAC1BiG,MAAM,GAAGxD,GAAG;MACd,CAAC;MAAA;MAAA;QAAA7D,aAAA,GAAAsB,CAAA;MAAA;IACH;IAAC;IAAAtB,aAAA,GAAAoB,CAAA;IAED,OAAOiG,MAAM;EACf;EAEQtD,YAAYA,CAACwC,KAAiB;IAAA;IAAAvG,aAAA,GAAAqB,CAAA;IACpC,MAAMqF,GAAG;IAAA;IAAA,CAAA1G,aAAA,GAAAoB,CAAA,QAAGqF,IAAI,CAACC,GAAG,EAAE;IAAC;IAAA1G,aAAA,GAAAoB,CAAA;IACvB,OAAQsF,GAAG,GAAGH,KAAK,CAACC,SAAS,GAAID,KAAK,CAACjC,GAAG;EAC5C;EAEQN,mBAAmBA,CAACuC,KAAiB;IAAA;IAAAvG,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAC3CmF,KAAK,CAACI,WAAW,EAAE;IAAC;IAAA3G,aAAA,GAAAoB,CAAA;IACpBmF,KAAK,CAACK,YAAY,GAAGH,IAAI,CAACC,GAAG,EAAE;IAAC;IAAA1G,aAAA,GAAAoB,CAAA;IAChC,IAAI,CAACmB,WAAW,CAACmC,GAAG,CAAC6B,KAAK,CAAC1C,GAAG,EAAE,EAAE,IAAI,CAACpB,aAAa,CAAC;EACvD;EAEQmC,aAAaA,CAACV,KAAU;IAAA;IAAAlE,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAC9B,OAAO,IAAIsG,IAAI,CAAC,CAACC,IAAI,CAACC,SAAS,CAAC1D,KAAK,CAAC,CAAC,CAAC,CAACS,IAAI;EAC/C;EAEQyC,qBAAqBA,CAAA;IAAA;IAAApH,aAAA,GAAAqB,CAAA;IAC3B,IAAIwG,KAAK;IAAA;IAAA,CAAA7H,aAAA,GAAAoB,CAAA,QAAG,CAAC;IAAC;IAAApB,aAAA,GAAAoB,CAAA;IACd,KAAK,MAAMmF,KAAK,IAAI,IAAI,CAAClE,WAAW,CAACyF,MAAM,EAAE,EAAE;MAAA;MAAA9H,aAAA,GAAAoB,CAAA;MAC7CyG,KAAK,IAAItB,KAAK,CAAC5B,IAAI;IACrB;IAAC;IAAA3E,aAAA,GAAAoB,CAAA;IACD,OAAOyG,KAAK;EACd;EAEQd,iBAAiBA,CAAA;IAAA;IAAA/G,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACvB,IAAI,CAAC4B,OAAO,CAACM,WAAW,GAAG,IAAI,CAAC8D,qBAAqB,EAAE;EACzD;EAEQnD,aAAaA,CAAA;IAAA;IAAAjE,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACnB,IAAI,CAAC4B,OAAO,CAACC,OAAO,GAAG,IAAI,CAACD,OAAO,CAACI,SAAS,GAAG,IAAI,CAACJ,OAAO,CAACG,aAAa;IAAC;IAAAnD,aAAA,GAAAoB,CAAA;IAC3E,IAAI,CAAC4B,OAAO,CAACE,QAAQ,GAAG,IAAI,CAACF,OAAO,CAACK,WAAW,GAAG,IAAI,CAACL,OAAO,CAACG,aAAa;EAC/E;EAEQ8B,YAAYA,CAAA;IAAA;IAAAjF,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAClB,IAAI,CAAC4B,OAAO,GAAG;MACbC,OAAO,EAAE,CAAC;MACVC,QAAQ,EAAE,CAAC;MACXC,aAAa,EAAE,CAAC;MAChBC,SAAS,EAAE,CAAC;MACZC,WAAW,EAAE,CAAC;MACdC,WAAW,EAAE,CAAC;MACdC,cAAc,EAAE,CAAC;MACjBC,aAAa,EAAE,CAAC;MAChBC,gBAAgB,EAAE;KACnB;EACH;EAEQ0C,SAASA,CAAC4B,KAAU;IAAA;IAAA/H,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAC1B,OAAO4G,IAAI,CAACL,IAAI,CAACC,SAAS,CAACG,KAAK,CAAC,CAAC,CAACE,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;EAClF;EAEQxE,2BAA2BA,CAAA;IAAA;IAAA1D,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACjC;IAAI;IAAA,CAAApB,aAAA,GAAAsB,CAAA,eAAI,CAACc,MAAM,CAACS,kBAAkB;IAAA;IAAA,CAAA7C,aAAA,GAAAsB,CAAA,WAAI,OAAO6G,MAAM,KAAK,WAAW,GAAE;MAAA;MAAAnI,aAAA,GAAAsB,CAAA;IAGrE,CAAC,CAFC;IACA;IAAA;IAAA;IAAA;MAAAtB,aAAA,GAAAsB,CAAA;IAAA;EAEJ;EAEQqC,6BAA6BA,CAAA;IAAA;IAAA3D,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACnC;IAAI;IAAA,CAAApB,aAAA,GAAAsB,CAAA,kBAAO8G,SAAS,KAAK,WAAW;IAAA;IAAA,CAAApI,aAAA,GAAAsB,CAAA,WAAI,QAAQ,IAAI8G,SAAS,GAAE;MAAA;MAAApI,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAC7DiH,WAAW,CAAC,MAAK;QAAA;QAAArI,aAAA,GAAAqB,CAAA;QACf,MAAMiH,MAAM;QAAA;QAAA,CAAAtI,aAAA,GAAAoB,CAAA,SAAIgH,SAAiB,CAACE,MAAM;QAAC;QAAAtI,aAAA,GAAAoB,CAAA;QACzC;QAAI;QAAA,CAAApB,aAAA,GAAAsB,CAAA,WAAAgH,MAAM;QAAA;QAAA,CAAAtI,aAAA,GAAAsB,CAAA,WAAIgH,MAAM,CAACC,cAAc,GAAGD,MAAM,CAACE,eAAe,GAAG,GAAG,GAAE;UAAA;UAAAxI,aAAA,GAAAsB,CAAA;UAAAtB,aAAA,GAAAoB,CAAA;UAClE;UACA,IAAI,CAACqH,uBAAuB,EAAE;QAChC,CAAC;QAAA;QAAA;UAAAzI,aAAA,GAAAsB,CAAA;QAAA;MACH,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;IACb,CAAC;IAAA;IAAA;MAAAtB,aAAA,GAAAsB,CAAA;IAAA;EACH;EAEQ,MAAMmH,uBAAuBA,CAAA;IAAA;IAAAzI,aAAA,GAAAqB,CAAA;IACnC,MAAMqH,UAAU;IAAA;IAAA,CAAA1I,aAAA,GAAAoB,CAAA,SAAG,IAAI,CAACgB,MAAM,CAACM,aAAa,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,EAAC,CAAC;IAClE,IAAIyE,WAAW;IAAA;IAAA,CAAAnH,aAAA,GAAAoB,CAAA,SAAG,IAAI,CAACgG,qBAAqB,EAAE;IAAC;IAAApH,aAAA,GAAAoB,CAAA;IAE/C;IAAO;IAAA,CAAApB,aAAA,GAAAsB,CAAA,WAAA6F,WAAW,GAAGuB,UAAU;IAAA;IAAA,CAAA1I,aAAA,GAAAsB,CAAA,WAAI,IAAI,CAACe,WAAW,CAACsC,IAAI,GAAG,CAAC,GAAE;MAC5D,MAAM0C,MAAM;MAAA;MAAA,CAAArH,aAAA,GAAAoB,CAAA,SAAG,IAAI,CAACkG,UAAU,EAAE;MAAC;MAAAtH,aAAA,GAAAoB,CAAA;MACjC,IAAIiG,MAAM,EAAE;QAAA;QAAArH,aAAA,GAAAsB,CAAA;QACV,MAAMiF,KAAK;QAAA;QAAA,CAAAvG,aAAA,GAAAoB,CAAA,SAAG,IAAI,CAACiB,WAAW,CAACuB,GAAG,CAACyD,MAAM,CAAC;QAAC;QAAArH,aAAA,GAAAoB,CAAA;QAC3C,IAAImF,KAAK,EAAE;UAAA;UAAAvG,aAAA,GAAAsB,CAAA;UAAAtB,aAAA,GAAAoB,CAAA;UACT+F,WAAW,IAAIZ,KAAK,CAAC5B,IAAI;UAAC;UAAA3E,aAAA,GAAAoB,CAAA;UAC1B,IAAI,CAACiB,WAAW,CAAC0C,MAAM,CAACsC,MAAM,CAAC;UAAC;UAAArH,aAAA,GAAAoB,CAAA;UAChC,IAAI,CAACmB,WAAW,CAACwC,MAAM,CAACsC,MAAM,CAAC;UAAC;UAAArH,aAAA,GAAAoB,CAAA;UAChC,IAAI,CAAC4B,OAAO,CAACQ,aAAa,EAAE;QAC9B,CAAC;QAAA;QAAA;UAAAxD,aAAA,GAAAsB,CAAA;QAAA;MACH,CAAC,MAAM;QAAA;QAAAtB,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACL;MACF;IACF;EACF;EAEA;EACA;EACA;EAEAuH,UAAUA,CAAA;IAAA;IAAA3I,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACR,OAAO;MAAE,GAAG,IAAI,CAAC4B;IAAO,CAAE;EAC5B;EAEA4F,mBAAmBA,CAACpD,QAA0B;IAAA;IAAAxF,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAC5C,IAAI,CAACoB,kBAAkB,CAACqG,IAAI,CAACrD,QAAQ,CAAC;EACxC;EAEA,MAAMsD,SAASA,CAACC,IAAc;IAAA;IAAA/I,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAC5B;IACA,KAAK,MAAMyC,GAAG,IAAIkF,IAAI,EAAE;MAAA;MAAA/I,aAAA,GAAAoB,CAAA;MACtB,MAAM,IAAI,CAACwC,GAAG,CAACC,GAAG,CAAC,CAAC,CAAC;IACvB;EACF;;AACD;AAAA7D,aAAA,GAAAoB,CAAA;AAnWD4H,OAAA,CAAA/G,sBAAA,GAAAA,sBAAA", "ignoreList": []}