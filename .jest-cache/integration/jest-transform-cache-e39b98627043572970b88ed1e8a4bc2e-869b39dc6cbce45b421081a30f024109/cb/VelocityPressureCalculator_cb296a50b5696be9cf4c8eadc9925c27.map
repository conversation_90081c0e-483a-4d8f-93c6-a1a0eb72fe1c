{"version": 3, "names": ["cov_l6ts6nd3r", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "name", "decl", "loc", "branchMap", "type", "locations", "undefined", "s", "f", "b", "inputSourceMap", "file", "mappings", "names", "sources", "sourcesContent", "version", "_coverageSchema", "coverage", "actualCoverage", "AirPropertiesCalculator_1", "require", "VelocityPressureMethod", "exports", "ValidationLevel", "VelocityPressureCalculator", "calculateVelocityPressure", "input", "velocity", "method", "ENHANCED_FORMULA", "airConditions", "airDensity", "ductGeometry", "turbulenceCorrection", "compressibilityCorrection", "validationLevel", "STANDARD", "warnings", "recommendations", "validateInputs", "actualAirDensity", "densityCalculationMethod", "airProps", "AirPropertiesCalculator", "calculateAirProperties", "density", "push", "STANDARD_AIR_DENSITY", "corrections", "calculateCorrections", "baseVelocityPressure", "executeCalculationMethod", "correctedVelocityPressure", "combined", "uncertaintyBounds", "calculateUncertaintyBounds", "generateRecommendations", "velocityPressure", "densityRatio", "accuracy", "METHOD_ACCURACY", "calculationDetails", "formula", "getFormulaDescription", "intermediateValues", "combinedCorrection", "dataSource", "standardReference", "getStandardReference", "getOptimalMethod", "inTableRange", "VELOCITY_RANGES", "LOOKUP_TABLE", "min", "max", "inCFDRange", "CFD_CORRECTED", "INTERPOLATED", "calculateVelocityFromPressure", "adjustedVP", "STANDARD_VELOCITY_CONSTANT", "Math", "sqrt", "NONE", "Error", "BASIC", "range", "STRICT", "temperature", "pressure", "altitude", "humidity", "turbulence", "compressibility", "shape", "aspectRatio", "FORMULA", "calculateByFormula", "calculateByLookupTable", "calculateByInterpolation", "calculateByEnhancedFormula", "calculateByCFDCorrection", "pow", "vpResult", "useTable", "error", "baseVP", "velocityCorrection", "cfdCorrection", "baseAccuracy", "adjustedAccuracy", "abs", "uncertainty", "lower", "upper", "confidenceLevel", "VERSION"], "sources": ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\VelocityPressureCalculator.ts"], "sourcesContent": ["/**\r\n * Velocity Pressure Calculator\r\n * \r\n * Comprehensive velocity pressure calculation service for Phase 3: Advanced Calculation Modules\r\n * Provides multiple calculation methods, environmental corrections, and performance optimization\r\n * for HVAC duct system velocity pressure calculations.\r\n * \r\n * @version 3.0.0\r\n * <AUTHOR> Suite Development Team\r\n */\r\n\r\nimport { AirPropertiesCalculator, AirConditions } from './AirPropertiesCalculator';\r\n\r\n/**\r\n * Velocity pressure calculation method options\r\n */\r\nexport enum VelocityPressureMethod {\r\n  FORMULA = 'formula',\r\n  LOOKUP_TABLE = 'lookup_table',\r\n  INTERPOLATED = 'interpolated',\r\n  ENHANCED_FORMULA = 'enhanced_formula',\r\n  CFD_CORRECTED = 'cfd_corrected'\r\n}\r\n\r\n/**\r\n * Velocity pressure calculation input parameters\r\n */\r\nexport interface VelocityPressureInput {\r\n  velocity: number;                    // FPM\r\n  method?: VelocityPressureMethod;     // Calculation method\r\n  airConditions?: AirConditions;       // Environmental conditions\r\n  airDensity?: number;                 // lb/ft³ (overrides calculated density)\r\n  ductGeometry?: DuctGeometry;         // Duct geometry for advanced corrections\r\n  turbulenceCorrection?: boolean;      // Apply turbulence corrections\r\n  compressibilityCorrection?: boolean; // Apply compressibility corrections\r\n  validationLevel?: ValidationLevel;   // Input validation strictness\r\n}\r\n\r\n/**\r\n * Duct geometry for advanced velocity pressure calculations\r\n */\r\nexport interface DuctGeometry {\r\n  shape: 'round' | 'rectangular' | 'oval';\r\n  diameter?: number;                   // inches (for round ducts)\r\n  width?: number;                      // inches (for rectangular ducts)\r\n  height?: number;                     // inches (for rectangular ducts)\r\n  majorAxis?: number;                  // inches (for oval ducts)\r\n  minorAxis?: number;                  // inches (for oval ducts)\r\n  hydraulicDiameter?: number;          // inches (calculated if not provided)\r\n  aspectRatio?: number;                // width/height (calculated if not provided)\r\n}\r\n\r\n/**\r\n * Validation level for input checking\r\n */\r\nexport enum ValidationLevel {\r\n  NONE = 'none',\r\n  BASIC = 'basic',\r\n  STANDARD = 'standard',\r\n  STRICT = 'strict'\r\n}\r\n\r\n/**\r\n * Velocity pressure calculation result\r\n */\r\nexport interface VelocityPressureResult {\r\n  velocityPressure: number;            // inches w.g.\r\n  method: VelocityPressureMethod;      // Method used\r\n  velocity: number;                    // FPM (input velocity)\r\n  airDensity: number;                  // lb/ft³ (actual density used)\r\n  densityRatio: number;                // Ratio to standard density\r\n  corrections: {\r\n    temperature: number;               // Temperature correction factor\r\n    pressure: number;                  // Pressure correction factor\r\n    altitude: number;                  // Altitude correction factor\r\n    humidity: number;                  // Humidity correction factor\r\n    turbulence: number;                // Turbulence correction factor\r\n    compressibility: number;           // Compressibility correction factor\r\n    combined: number;                  // Combined correction factor\r\n  };\r\n  accuracy: number;                    // Estimated accuracy (0-1)\r\n  uncertaintyBounds?: {\r\n    lower: number;                     // Lower bound (inches w.g.)\r\n    upper: number;                     // Upper bound (inches w.g.)\r\n    confidenceLevel: number;           // Confidence level (0-1)\r\n  };\r\n  warnings: string[];\r\n  recommendations: string[];\r\n  calculationDetails: {\r\n    formula: string;                   // Formula used\r\n    intermediateValues: Record<string, number>;\r\n    dataSource: string;                // Source of calculation data\r\n    standardReference: string;         // Reference standard\r\n  };\r\n}\r\n\r\n/**\r\n * Velocity Pressure Calculator\r\n * \r\n * Comprehensive velocity pressure calculation service providing multiple calculation\r\n * methods, environmental corrections, and advanced features for HVAC applications.\r\n */\r\nexport class VelocityPressureCalculator {\r\n  private static readonly VERSION = '3.0.0';\r\n  private static readonly STANDARD_AIR_DENSITY = 0.075; // lb/ft³\r\n  private static readonly STANDARD_VELOCITY_CONSTANT = 4005; // For VP = (V/4005)²\r\n  \r\n  // Velocity ranges for different calculation methods\r\n  private static readonly VELOCITY_RANGES = {\r\n    FORMULA: { min: 0, max: 10000 },\r\n    LOOKUP_TABLE: { min: 100, max: 5000 },\r\n    INTERPOLATED: { min: 50, max: 6000 },\r\n    ENHANCED_FORMULA: { min: 0, max: 15000 },\r\n    CFD_CORRECTED: { min: 500, max: 8000 }\r\n  };\r\n\r\n  // Accuracy estimates for different methods\r\n  private static readonly METHOD_ACCURACY = {\r\n    [VelocityPressureMethod.FORMULA]: 0.95,\r\n    [VelocityPressureMethod.LOOKUP_TABLE]: 0.98,\r\n    [VelocityPressureMethod.INTERPOLATED]: 0.97,\r\n    [VelocityPressureMethod.ENHANCED_FORMULA]: 0.96,\r\n    [VelocityPressureMethod.CFD_CORRECTED]: 0.99\r\n  };\r\n\r\n  /**\r\n   * Calculate velocity pressure using specified method and conditions\r\n   */\r\n  public static calculateVelocityPressure(input: VelocityPressureInput): VelocityPressureResult {\r\n    const {\r\n      velocity,\r\n      method = VelocityPressureMethod.ENHANCED_FORMULA,\r\n      airConditions,\r\n      airDensity,\r\n      ductGeometry,\r\n      turbulenceCorrection = false,\r\n      compressibilityCorrection = false,\r\n      validationLevel = ValidationLevel.STANDARD\r\n    } = input;\r\n\r\n    const warnings: string[] = [];\r\n    const recommendations: string[] = [];\r\n\r\n    // Validate inputs\r\n    this.validateInputs(input, validationLevel, warnings);\r\n\r\n    // Determine air density\r\n    let actualAirDensity: number;\r\n    let densityCalculationMethod: string;\r\n    \r\n    if (airDensity !== undefined) {\r\n      actualAirDensity = airDensity;\r\n      densityCalculationMethod = 'User specified';\r\n    } else if (airConditions) {\r\n      const airProps = AirPropertiesCalculator.calculateAirProperties(airConditions);\r\n      actualAirDensity = airProps.density;\r\n      densityCalculationMethod = 'Calculated from conditions';\r\n      warnings.push(...airProps.warnings);\r\n    } else {\r\n      actualAirDensity = this.STANDARD_AIR_DENSITY;\r\n      densityCalculationMethod = 'Standard conditions assumed';\r\n    }\r\n\r\n    // Calculate corrections\r\n    const corrections = this.calculateCorrections(\r\n      airConditions,\r\n      actualAirDensity,\r\n      ductGeometry,\r\n      turbulenceCorrection,\r\n      compressibilityCorrection\r\n    );\r\n\r\n    // Select and execute calculation method\r\n    const baseVelocityPressure = this.executeCalculationMethod(method, velocity, warnings);\r\n    \r\n    // Apply corrections\r\n    const correctedVelocityPressure = baseVelocityPressure * corrections.combined;\r\n\r\n    // Calculate uncertainty bounds\r\n    const uncertaintyBounds = this.calculateUncertaintyBounds(\r\n      correctedVelocityPressure,\r\n      method,\r\n      velocity,\r\n      corrections\r\n    );\r\n\r\n    // Generate recommendations\r\n    this.generateRecommendations(velocity, method, corrections, recommendations);\r\n\r\n    return {\r\n      velocityPressure: correctedVelocityPressure,\r\n      method,\r\n      velocity,\r\n      airDensity: actualAirDensity,\r\n      densityRatio: actualAirDensity / this.STANDARD_AIR_DENSITY,\r\n      corrections,\r\n      accuracy: this.METHOD_ACCURACY[method],\r\n      uncertaintyBounds,\r\n      warnings,\r\n      recommendations,\r\n      calculationDetails: {\r\n        formula: this.getFormulaDescription(method),\r\n        intermediateValues: {\r\n          baseVelocityPressure,\r\n          densityRatio: actualAirDensity / this.STANDARD_AIR_DENSITY,\r\n          combinedCorrection: corrections.combined\r\n        },\r\n        dataSource: densityCalculationMethod,\r\n        standardReference: this.getStandardReference(method)\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get optimal calculation method for given conditions\r\n   */\r\n  public static getOptimalMethod(\r\n    velocity: number,\r\n    airConditions?: AirConditions,\r\n    accuracy: 'standard' | 'high' | 'maximum' = 'standard'\r\n  ): VelocityPressureMethod {\r\n    // Check velocity ranges\r\n    const inTableRange = velocity >= this.VELOCITY_RANGES.LOOKUP_TABLE.min && \r\n                        velocity <= this.VELOCITY_RANGES.LOOKUP_TABLE.max;\r\n    const inCFDRange = velocity >= this.VELOCITY_RANGES.CFD_CORRECTED.min && \r\n                      velocity <= this.VELOCITY_RANGES.CFD_CORRECTED.max;\r\n\r\n    // Determine optimal method based on accuracy requirements and conditions\r\n    if (accuracy === 'maximum' && inCFDRange) {\r\n      return VelocityPressureMethod.CFD_CORRECTED;\r\n    }\r\n    \r\n    if (accuracy === 'high' && inTableRange) {\r\n      return VelocityPressureMethod.LOOKUP_TABLE;\r\n    }\r\n    \r\n    if (inTableRange && !airConditions) {\r\n      return VelocityPressureMethod.INTERPOLATED;\r\n    }\r\n    \r\n    return VelocityPressureMethod.ENHANCED_FORMULA;\r\n  }\r\n\r\n  /**\r\n   * Calculate velocity from velocity pressure (inverse calculation)\r\n   */\r\n  public static calculateVelocityFromPressure(\r\n    velocityPressure: number,\r\n    airConditions?: AirConditions,\r\n    airDensity?: number\r\n  ): { velocity: number; accuracy: number; warnings: string[] } {\r\n    const warnings: string[] = [];\r\n    \r\n    // Determine air density\r\n    let actualAirDensity: number;\r\n    if (airDensity !== undefined) {\r\n      actualAirDensity = airDensity;\r\n    } else if (airConditions) {\r\n      const airProps = AirPropertiesCalculator.calculateAirProperties(airConditions);\r\n      actualAirDensity = airProps.density;\r\n      warnings.push(...airProps.warnings);\r\n    } else {\r\n      actualAirDensity = this.STANDARD_AIR_DENSITY;\r\n    }\r\n\r\n    // Calculate velocity using inverse formula\r\n    const densityRatio = actualAirDensity / this.STANDARD_AIR_DENSITY;\r\n    const adjustedVP = velocityPressure / densityRatio;\r\n    const velocity = this.STANDARD_VELOCITY_CONSTANT * Math.sqrt(adjustedVP);\r\n\r\n    return {\r\n      velocity,\r\n      accuracy: 0.95,\r\n      warnings\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Validate input parameters\r\n   */\r\n  private static validateInputs(\r\n    input: VelocityPressureInput,\r\n    validationLevel: ValidationLevel,\r\n    warnings: string[]\r\n  ): void {\r\n    if (validationLevel === ValidationLevel.NONE) return;\r\n\r\n    const { velocity, method = VelocityPressureMethod.ENHANCED_FORMULA } = input;\r\n\r\n    // Basic validation\r\n    if (velocity < 0) {\r\n      throw new Error('Velocity cannot be negative');\r\n    }\r\n\r\n    if (validationLevel === ValidationLevel.BASIC) return;\r\n\r\n    // Standard validation\r\n    if (velocity > 10000) {\r\n      warnings.push('Velocity exceeds typical HVAC range (>10,000 FPM)');\r\n    }\r\n\r\n    const range = this.VELOCITY_RANGES[method];\r\n    if (velocity < range.min || velocity > range.max) {\r\n      warnings.push(`Velocity ${velocity} FPM is outside optimal range for ${method} method (${range.min}-${range.max} FPM)`);\r\n    }\r\n\r\n    if (validationLevel === ValidationLevel.STRICT) {\r\n      // Strict validation\r\n      if (velocity < 100) {\r\n        warnings.push('Very low velocity may indicate measurement or input error');\r\n      }\r\n      \r\n      if (velocity > 6000) {\r\n        warnings.push('High velocity may cause noise and energy efficiency issues');\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Calculate environmental and geometric corrections\r\n   */\r\n  private static calculateCorrections(\r\n    airConditions?: AirConditions,\r\n    airDensity?: number,\r\n    ductGeometry?: DuctGeometry,\r\n    turbulenceCorrection?: boolean,\r\n    compressibilityCorrection?: boolean\r\n  ): VelocityPressureResult['corrections'] {\r\n    const corrections = {\r\n      temperature: 1.0,\r\n      pressure: 1.0,\r\n      altitude: 1.0,\r\n      humidity: 1.0,\r\n      turbulence: 1.0,\r\n      compressibility: 1.0,\r\n      combined: 1.0\r\n    };\r\n\r\n    // Density-based corrections\r\n    if (airDensity) {\r\n      const densityRatio = airDensity / this.STANDARD_AIR_DENSITY;\r\n      corrections.temperature = densityRatio;\r\n      corrections.pressure = densityRatio;\r\n      corrections.altitude = densityRatio;\r\n      corrections.humidity = densityRatio;\r\n    }\r\n\r\n    // Turbulence correction (simplified)\r\n    if (turbulenceCorrection && ductGeometry) {\r\n      if (ductGeometry.shape === 'rectangular' && ductGeometry.aspectRatio && ductGeometry.aspectRatio > 3) {\r\n        corrections.turbulence = 1.05; // 5% increase for high aspect ratio\r\n      }\r\n    }\r\n\r\n    // Compressibility correction (simplified)\r\n    if (compressibilityCorrection && airConditions) {\r\n      // Negligible for typical HVAC velocities, but included for completeness\r\n      corrections.compressibility = 1.0;\r\n    }\r\n\r\n    // Calculate combined correction\r\n    corrections.combined = corrections.temperature * corrections.pressure * \r\n                          corrections.altitude * corrections.humidity * \r\n                          corrections.turbulence * corrections.compressibility;\r\n\r\n    return corrections;\r\n  }\r\n\r\n  /**\r\n   * Execute the specified calculation method\r\n   */\r\n  private static executeCalculationMethod(\r\n    method: VelocityPressureMethod,\r\n    velocity: number,\r\n    warnings: string[]\r\n  ): number {\r\n    switch (method) {\r\n      case VelocityPressureMethod.FORMULA:\r\n        return this.calculateByFormula(velocity);\r\n        \r\n      case VelocityPressureMethod.LOOKUP_TABLE:\r\n        return this.calculateByLookupTable(velocity, warnings);\r\n        \r\n      case VelocityPressureMethod.INTERPOLATED:\r\n        return this.calculateByInterpolation(velocity, warnings);\r\n        \r\n      case VelocityPressureMethod.ENHANCED_FORMULA:\r\n        return this.calculateByEnhancedFormula(velocity);\r\n        \r\n      case VelocityPressureMethod.CFD_CORRECTED:\r\n        return this.calculateByCFDCorrection(velocity, warnings);\r\n        \r\n      default:\r\n        throw new Error(`Unsupported calculation method: ${method}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Calculate velocity pressure using standard formula\r\n   */\r\n  private static calculateByFormula(velocity: number): number {\r\n    return Math.pow(velocity / this.STANDARD_VELOCITY_CONSTANT, 2);\r\n  }\r\n\r\n  /**\r\n   * Calculate velocity pressure using lookup table\r\n   */\r\n  private static calculateByLookupTable(velocity: number, warnings: string[]): number {\r\n    try {\r\n      const vpResult = AirPropertiesCalculator.calculateVelocityPressure({\r\n        velocity,\r\n        useTable: true\r\n      });\r\n      warnings.push(...vpResult.warnings);\r\n      return vpResult.velocityPressure;\r\n    } catch (error) {\r\n      warnings.push('Lookup table unavailable, falling back to formula method');\r\n      return this.calculateByFormula(velocity);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Calculate velocity pressure using interpolation\r\n   */\r\n  private static calculateByInterpolation(velocity: number, warnings: string[]): number {\r\n    // Use AirPropertiesCalculator's interpolation method\r\n    return this.calculateByLookupTable(velocity, warnings);\r\n  }\r\n\r\n  /**\r\n   * Calculate velocity pressure using enhanced formula with corrections\r\n   */\r\n  private static calculateByEnhancedFormula(velocity: number): number {\r\n    // Enhanced formula with slight corrections for real-world conditions\r\n    const baseVP = Math.pow(velocity / this.STANDARD_VELOCITY_CONSTANT, 2);\r\n    \r\n    // Apply minor correction for velocity-dependent effects\r\n    const velocityCorrection = 1 + (velocity - 2000) * 0.000001; // Very small correction\r\n    \r\n    return baseVP * Math.max(0.98, Math.min(1.02, velocityCorrection));\r\n  }\r\n\r\n  /**\r\n   * Calculate velocity pressure using CFD-derived corrections\r\n   */\r\n  private static calculateByCFDCorrection(velocity: number, warnings: string[]): number {\r\n    // CFD-derived corrections for improved accuracy\r\n    const baseVP = this.calculateByFormula(velocity);\r\n    \r\n    // Apply CFD-derived correction factors (simplified)\r\n    let cfdCorrection = 1.0;\r\n    \r\n    if (velocity < 1000) {\r\n      cfdCorrection = 0.98; // Slight under-prediction at low velocities\r\n    } else if (velocity > 4000) {\r\n      cfdCorrection = 1.02; // Slight over-prediction at high velocities\r\n    }\r\n    \r\n    warnings.push('CFD corrections applied - results may vary with actual duct configuration');\r\n    \r\n    return baseVP * cfdCorrection;\r\n  }\r\n\r\n  /**\r\n   * Calculate uncertainty bounds for the result\r\n   */\r\n  private static calculateUncertaintyBounds(\r\n    velocityPressure: number,\r\n    method: VelocityPressureMethod,\r\n    velocity: number,\r\n    corrections: VelocityPressureResult['corrections']\r\n  ): VelocityPressureResult['uncertaintyBounds'] {\r\n    const baseAccuracy = this.METHOD_ACCURACY[method];\r\n    \r\n    // Adjust accuracy based on velocity range and corrections\r\n    let adjustedAccuracy = baseAccuracy;\r\n    \r\n    if (velocity < 500 || velocity > 5000) {\r\n      adjustedAccuracy *= 0.95; // Reduced accuracy outside optimal range\r\n    }\r\n    \r\n    if (Math.abs(corrections.combined - 1.0) > 0.1) {\r\n      adjustedAccuracy *= 0.98; // Reduced accuracy with large corrections\r\n    }\r\n    \r\n    const uncertainty = velocityPressure * (1 - adjustedAccuracy);\r\n    \r\n    return {\r\n      lower: velocityPressure - uncertainty,\r\n      upper: velocityPressure + uncertainty,\r\n      confidenceLevel: adjustedAccuracy\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Generate recommendations based on calculation results\r\n   */\r\n  private static generateRecommendations(\r\n    velocity: number,\r\n    method: VelocityPressureMethod,\r\n    corrections: VelocityPressureResult['corrections'],\r\n    recommendations: string[]\r\n  ): void {\r\n    // Velocity-based recommendations\r\n    if (velocity < 500) {\r\n      recommendations.push('Consider increasing velocity to improve accuracy and system performance');\r\n    } else if (velocity > 4000) {\r\n      recommendations.push('High velocity may cause noise issues - consider larger duct size');\r\n    }\r\n    \r\n    // Method-based recommendations\r\n    if (method === VelocityPressureMethod.FORMULA && velocity >= 100 && velocity <= 5000) {\r\n      recommendations.push('Consider using lookup table method for improved accuracy in this velocity range');\r\n    }\r\n    \r\n    // Correction-based recommendations\r\n    if (Math.abs(corrections.combined - 1.0) > 0.05) {\r\n      recommendations.push('Significant environmental corrections applied - verify air conditions');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get formula description for the method\r\n   */\r\n  private static getFormulaDescription(method: VelocityPressureMethod): string {\r\n    switch (method) {\r\n      case VelocityPressureMethod.FORMULA:\r\n        return 'VP = (V/4005)²';\r\n      case VelocityPressureMethod.LOOKUP_TABLE:\r\n        return 'Table lookup with exact values';\r\n      case VelocityPressureMethod.INTERPOLATED:\r\n        return 'Table lookup with linear interpolation';\r\n      case VelocityPressureMethod.ENHANCED_FORMULA:\r\n        return 'VP = (V/4005)² with velocity-dependent corrections';\r\n      case VelocityPressureMethod.CFD_CORRECTED:\r\n        return 'VP = (V/4005)² with CFD-derived corrections';\r\n      default:\r\n        return 'Unknown method';\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get standard reference for the method\r\n   */\r\n  private static getStandardReference(method: VelocityPressureMethod): string {\r\n    switch (method) {\r\n      case VelocityPressureMethod.FORMULA:\r\n      case VelocityPressureMethod.ENHANCED_FORMULA:\r\n        return 'ASHRAE Fundamentals, Chapter 21';\r\n      case VelocityPressureMethod.LOOKUP_TABLE:\r\n      case VelocityPressureMethod.INTERPOLATED:\r\n        return 'ASHRAE Fundamentals, Table 21-1';\r\n      case VelocityPressureMethod.CFD_CORRECTED:\r\n        return 'CFD Analysis and ASHRAE Fundamentals';\r\n      default:\r\n        return 'Internal calculation';\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;AAAA;;;;;;;;;;AAAA;AAAA,SAAAA,cAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;MAAA;QAAAC,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAO,SAAA;MAAA;QAAAD,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAW,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,cAAA;MAAAC,IAAA;MAAAC,QAAA;MAAAC,KAAA;MAAAC,OAAA;MAAAC,cAAA;MAAAC,OAAA;IAAA;IAAAC,eAAA;IAAA5B,IAAA;EAAA;EAAA,IAAA6B,QAAA,GAAA5B,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAA0B,QAAA,CAAA9B,IAAA,KAAA8B,QAAA,CAAA9B,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAA6B,QAAA,CAAA9B,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAA0B,cAAA,GAAAD,QAAA,CAAA9B,IAAA;EAAA;;;;;;;;;;;;;;;;AAWA,MAAAgC,yBAAA;AAAA;AAAA,CAAAjC,aAAA,GAAAoB,CAAA,OAAAc,OAAA;AAEA;;;AAGA,IAAYC,sBAMX;AAAA;AAAAnC,aAAA,GAAAoB,CAAA;AAND,WAAYe,sBAAsB;EAAA;EAAAnC,aAAA,GAAAqB,CAAA;EAAArB,aAAA,GAAAoB,CAAA;EAChCe,sBAAA,uBAAmB;EAAA;EAAAnC,aAAA,GAAAoB,CAAA;EACnBe,sBAAA,iCAA6B;EAAA;EAAAnC,aAAA,GAAAoB,CAAA;EAC7Be,sBAAA,iCAA6B;EAAA;EAAAnC,aAAA,GAAAoB,CAAA;EAC7Be,sBAAA,yCAAqC;EAAA;EAAAnC,aAAA,GAAAoB,CAAA;EACrCe,sBAAA,mCAA+B;AACjC,CAAC;AANW;AAAA,CAAAnC,aAAA,GAAAsB,CAAA,UAAAa,sBAAsB;AAAA;AAAA,CAAAnC,aAAA,GAAAsB,CAAA,UAAAc,OAAA,CAAAD,sBAAA,GAAtBA,sBAAsB;AAoClC;;;AAGA,IAAYE,eAKX;AAAA;AAAArC,aAAA,GAAAoB,CAAA;AALD,WAAYiB,eAAe;EAAA;EAAArC,aAAA,GAAAqB,CAAA;EAAArB,aAAA,GAAAoB,CAAA;EACzBiB,eAAA,iBAAa;EAAA;EAAArC,aAAA,GAAAoB,CAAA;EACbiB,eAAA,mBAAe;EAAA;EAAArC,aAAA,GAAAoB,CAAA;EACfiB,eAAA,yBAAqB;EAAA;EAAArC,aAAA,GAAAoB,CAAA;EACrBiB,eAAA,qBAAiB;AACnB,CAAC;AALW;AAAA,CAAArC,aAAA,GAAAsB,CAAA,UAAAe,eAAe;AAAA;AAAA,CAAArC,aAAA,GAAAsB,CAAA,UAAAc,OAAA,CAAAC,eAAA,GAAfA,eAAe;AAyC3B;;;;;;AAMA,MAAaC,0BAA0B;EAuBrC;;;EAGO,OAAOC,yBAAyBA,CAACC,KAA4B;IAAA;IAAAxC,aAAA,GAAAqB,CAAA;IAClE,MAAM;MACJoB,QAAQ;MACRC,MAAM;MAAA;MAAA,CAAA1C,aAAA,GAAAsB,CAAA,UAAGa,sBAAsB,CAACQ,gBAAgB;MAChDC,aAAa;MACbC,UAAU;MACVC,YAAY;MACZC,oBAAoB;MAAA;MAAA,CAAA/C,aAAA,GAAAsB,CAAA,UAAG,KAAK;MAC5B0B,yBAAyB;MAAA;MAAA,CAAAhD,aAAA,GAAAsB,CAAA,UAAG,KAAK;MACjC2B,eAAe;MAAA;MAAA,CAAAjD,aAAA,GAAAsB,CAAA,UAAGe,eAAe,CAACa,QAAQ;IAAA,CAC3C;IAAA;IAAA,CAAAlD,aAAA,GAAAoB,CAAA,QAAGoB,KAAK;IAET,MAAMW,QAAQ;IAAA;IAAA,CAAAnD,aAAA,GAAAoB,CAAA,QAAa,EAAE;IAC7B,MAAMgC,eAAe;IAAA;IAAA,CAAApD,aAAA,GAAAoB,CAAA,QAAa,EAAE;IAEpC;IAAA;IAAApB,aAAA,GAAAoB,CAAA;IACA,IAAI,CAACiC,cAAc,CAACb,KAAK,EAAES,eAAe,EAAEE,QAAQ,CAAC;IAErD;IACA,IAAIG,gBAAwB;IAC5B,IAAIC,wBAAgC;IAAC;IAAAvD,aAAA,GAAAoB,CAAA;IAErC,IAAIyB,UAAU,KAAK1B,SAAS,EAAE;MAAA;MAAAnB,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAC5BkC,gBAAgB,GAAGT,UAAU;MAAC;MAAA7C,aAAA,GAAAoB,CAAA;MAC9BmC,wBAAwB,GAAG,gBAAgB;IAC7C,CAAC,MAAM;MAAA;MAAAvD,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA,IAAIwB,aAAa,EAAE;QAAA;QAAA5C,aAAA,GAAAsB,CAAA;QACxB,MAAMkC,QAAQ;QAAA;QAAA,CAAAxD,aAAA,GAAAoB,CAAA,QAAGa,yBAAA,CAAAwB,uBAAuB,CAACC,sBAAsB,CAACd,aAAa,CAAC;QAAC;QAAA5C,aAAA,GAAAoB,CAAA;QAC/EkC,gBAAgB,GAAGE,QAAQ,CAACG,OAAO;QAAC;QAAA3D,aAAA,GAAAoB,CAAA;QACpCmC,wBAAwB,GAAG,4BAA4B;QAAC;QAAAvD,aAAA,GAAAoB,CAAA;QACxD+B,QAAQ,CAACS,IAAI,CAAC,GAAGJ,QAAQ,CAACL,QAAQ,CAAC;MACrC,CAAC,MAAM;QAAA;QAAAnD,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACLkC,gBAAgB,GAAG,IAAI,CAACO,oBAAoB;QAAC;QAAA7D,aAAA,GAAAoB,CAAA;QAC7CmC,wBAAwB,GAAG,6BAA6B;MAC1D;IAAA;IAEA;IACA,MAAMO,WAAW;IAAA;IAAA,CAAA9D,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAAC2C,oBAAoB,CAC3CnB,aAAa,EACbU,gBAAgB,EAChBR,YAAY,EACZC,oBAAoB,EACpBC,yBAAyB,CAC1B;IAED;IACA,MAAMgB,oBAAoB;IAAA;IAAA,CAAAhE,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAAC6C,wBAAwB,CAACvB,MAAM,EAAED,QAAQ,EAAEU,QAAQ,CAAC;IAEtF;IACA,MAAMe,yBAAyB;IAAA;IAAA,CAAAlE,aAAA,GAAAoB,CAAA,QAAG4C,oBAAoB,GAAGF,WAAW,CAACK,QAAQ;IAE7E;IACA,MAAMC,iBAAiB;IAAA;IAAA,CAAApE,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAACiD,0BAA0B,CACvDH,yBAAyB,EACzBxB,MAAM,EACND,QAAQ,EACRqB,WAAW,CACZ;IAED;IAAA;IAAA9D,aAAA,GAAAoB,CAAA;IACA,IAAI,CAACkD,uBAAuB,CAAC7B,QAAQ,EAAEC,MAAM,EAAEoB,WAAW,EAAEV,eAAe,CAAC;IAAC;IAAApD,aAAA,GAAAoB,CAAA;IAE7E,OAAO;MACLmD,gBAAgB,EAAEL,yBAAyB;MAC3CxB,MAAM;MACND,QAAQ;MACRI,UAAU,EAAES,gBAAgB;MAC5BkB,YAAY,EAAElB,gBAAgB,GAAG,IAAI,CAACO,oBAAoB;MAC1DC,WAAW;MACXW,QAAQ,EAAE,IAAI,CAACC,eAAe,CAAChC,MAAM,CAAC;MACtC0B,iBAAiB;MACjBjB,QAAQ;MACRC,eAAe;MACfuB,kBAAkB,EAAE;QAClBC,OAAO,EAAE,IAAI,CAACC,qBAAqB,CAACnC,MAAM,CAAC;QAC3CoC,kBAAkB,EAAE;UAClBd,oBAAoB;UACpBQ,YAAY,EAAElB,gBAAgB,GAAG,IAAI,CAACO,oBAAoB;UAC1DkB,kBAAkB,EAAEjB,WAAW,CAACK;SACjC;QACDa,UAAU,EAAEzB,wBAAwB;QACpC0B,iBAAiB,EAAE,IAAI,CAACC,oBAAoB,CAACxC,MAAM;;KAEtD;EACH;EAEA;;;EAGO,OAAOyC,gBAAgBA,CAC5B1C,QAAgB,EAChBG,aAA6B,EAC7B6B,QAAA;EAAA;EAAA,CAAAzE,aAAA,GAAAsB,CAAA,UAA4C,UAAU;IAAA;IAAAtB,aAAA,GAAAqB,CAAA;IAEtD;IACA,MAAM+D,YAAY;IAAA;IAAA,CAAApF,aAAA,GAAAoB,CAAA;IAAG;IAAA,CAAApB,aAAA,GAAAsB,CAAA,UAAAmB,QAAQ,IAAI,IAAI,CAAC4C,eAAe,CAACC,YAAY,CAACC,GAAG;IAAA;IAAA,CAAAvF,aAAA,GAAAsB,CAAA,UAClDmB,QAAQ,IAAI,IAAI,CAAC4C,eAAe,CAACC,YAAY,CAACE,GAAG;IACrE,MAAMC,UAAU;IAAA;IAAA,CAAAzF,aAAA,GAAAoB,CAAA;IAAG;IAAA,CAAApB,aAAA,GAAAsB,CAAA,WAAAmB,QAAQ,IAAI,IAAI,CAAC4C,eAAe,CAACK,aAAa,CAACH,GAAG;IAAA;IAAA,CAAAvF,aAAA,GAAAsB,CAAA,WACnDmB,QAAQ,IAAI,IAAI,CAAC4C,eAAe,CAACK,aAAa,CAACF,GAAG;IAEpE;IAAA;IAAAxF,aAAA,GAAAoB,CAAA;IACA;IAAI;IAAA,CAAApB,aAAA,GAAAsB,CAAA,WAAAmD,QAAQ,KAAK,SAAS;IAAA;IAAA,CAAAzE,aAAA,GAAAsB,CAAA,WAAImE,UAAU,GAAE;MAAA;MAAAzF,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACxC,OAAOe,sBAAsB,CAACuD,aAAa;IAC7C,CAAC;IAAA;IAAA;MAAA1F,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAED;IAAI;IAAA,CAAApB,aAAA,GAAAsB,CAAA,WAAAmD,QAAQ,KAAK,MAAM;IAAA;IAAA,CAAAzE,aAAA,GAAAsB,CAAA,WAAI8D,YAAY,GAAE;MAAA;MAAApF,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACvC,OAAOe,sBAAsB,CAACmD,YAAY;IAC5C,CAAC;IAAA;IAAA;MAAAtF,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAED;IAAI;IAAA,CAAApB,aAAA,GAAAsB,CAAA,WAAA8D,YAAY;IAAA;IAAA,CAAApF,aAAA,GAAAsB,CAAA,WAAI,CAACsB,aAAa,GAAE;MAAA;MAAA5C,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAClC,OAAOe,sBAAsB,CAACwD,YAAY;IAC5C,CAAC;IAAA;IAAA;MAAA3F,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAED,OAAOe,sBAAsB,CAACQ,gBAAgB;EAChD;EAEA;;;EAGO,OAAOiD,6BAA6BA,CACzCrB,gBAAwB,EACxB3B,aAA6B,EAC7BC,UAAmB;IAAA;IAAA7C,aAAA,GAAAqB,CAAA;IAEnB,MAAM8B,QAAQ;IAAA;IAAA,CAAAnD,aAAA,GAAAoB,CAAA,QAAa,EAAE;IAE7B;IACA,IAAIkC,gBAAwB;IAAC;IAAAtD,aAAA,GAAAoB,CAAA;IAC7B,IAAIyB,UAAU,KAAK1B,SAAS,EAAE;MAAA;MAAAnB,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAC5BkC,gBAAgB,GAAGT,UAAU;IAC/B,CAAC,MAAM;MAAA;MAAA7C,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA,IAAIwB,aAAa,EAAE;QAAA;QAAA5C,aAAA,GAAAsB,CAAA;QACxB,MAAMkC,QAAQ;QAAA;QAAA,CAAAxD,aAAA,GAAAoB,CAAA,QAAGa,yBAAA,CAAAwB,uBAAuB,CAACC,sBAAsB,CAACd,aAAa,CAAC;QAAC;QAAA5C,aAAA,GAAAoB,CAAA;QAC/EkC,gBAAgB,GAAGE,QAAQ,CAACG,OAAO;QAAC;QAAA3D,aAAA,GAAAoB,CAAA;QACpC+B,QAAQ,CAACS,IAAI,CAAC,GAAGJ,QAAQ,CAACL,QAAQ,CAAC;MACrC,CAAC,MAAM;QAAA;QAAAnD,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACLkC,gBAAgB,GAAG,IAAI,CAACO,oBAAoB;MAC9C;IAAA;IAEA;IACA,MAAMW,YAAY;IAAA;IAAA,CAAAxE,aAAA,GAAAoB,CAAA,QAAGkC,gBAAgB,GAAG,IAAI,CAACO,oBAAoB;IACjE,MAAMgC,UAAU;IAAA;IAAA,CAAA7F,aAAA,GAAAoB,CAAA,QAAGmD,gBAAgB,GAAGC,YAAY;IAClD,MAAM/B,QAAQ;IAAA;IAAA,CAAAzC,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAAC0E,0BAA0B,GAAGC,IAAI,CAACC,IAAI,CAACH,UAAU,CAAC;IAAC;IAAA7F,aAAA,GAAAoB,CAAA;IAEzE,OAAO;MACLqB,QAAQ;MACRgC,QAAQ,EAAE,IAAI;MACdtB;KACD;EACH;EAEA;;;EAGQ,OAAOE,cAAcA,CAC3Bb,KAA4B,EAC5BS,eAAgC,EAChCE,QAAkB;IAAA;IAAAnD,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAElB,IAAI6B,eAAe,KAAKZ,eAAe,CAAC4D,IAAI,EAAE;MAAA;MAAAjG,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA;IAAA,CAAO;IAAA;IAAA;MAAApB,aAAA,GAAAsB,CAAA;IAAA;IAErD,MAAM;MAAEmB,QAAQ;MAAEC,MAAM;MAAA;MAAA,CAAA1C,aAAA,GAAAsB,CAAA,WAAGa,sBAAsB,CAACQ,gBAAgB;IAAA,CAAE;IAAA;IAAA,CAAA3C,aAAA,GAAAoB,CAAA,QAAGoB,KAAK;IAE5E;IAAA;IAAAxC,aAAA,GAAAoB,CAAA;IACA,IAAIqB,QAAQ,GAAG,CAAC,EAAE;MAAA;MAAAzC,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAChB,MAAM,IAAI8E,KAAK,CAAC,6BAA6B,CAAC;IAChD,CAAC;IAAA;IAAA;MAAAlG,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAED,IAAI6B,eAAe,KAAKZ,eAAe,CAAC8D,KAAK,EAAE;MAAA;MAAAnG,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA;IAAA,CAAO;IAAA;IAAA;MAAApB,aAAA,GAAAsB,CAAA;IAAA;IAEtD;IAAAtB,aAAA,GAAAoB,CAAA;IACA,IAAIqB,QAAQ,GAAG,KAAK,EAAE;MAAA;MAAAzC,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACpB+B,QAAQ,CAACS,IAAI,CAAC,mDAAmD,CAAC;IACpE,CAAC;IAAA;IAAA;MAAA5D,aAAA,GAAAsB,CAAA;IAAA;IAED,MAAM8E,KAAK;IAAA;IAAA,CAAApG,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAACiE,eAAe,CAAC3C,MAAM,CAAC;IAAC;IAAA1C,aAAA,GAAAoB,CAAA;IAC3C;IAAI;IAAA,CAAApB,aAAA,GAAAsB,CAAA,WAAAmB,QAAQ,GAAG2D,KAAK,CAACb,GAAG;IAAA;IAAA,CAAAvF,aAAA,GAAAsB,CAAA,WAAImB,QAAQ,GAAG2D,KAAK,CAACZ,GAAG,GAAE;MAAA;MAAAxF,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAChD+B,QAAQ,CAACS,IAAI,CAAC,YAAYnB,QAAQ,qCAAqCC,MAAM,YAAY0D,KAAK,CAACb,GAAG,IAAIa,KAAK,CAACZ,GAAG,OAAO,CAAC;IACzH,CAAC;IAAA;IAAA;MAAAxF,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAED,IAAI6B,eAAe,KAAKZ,eAAe,CAACgE,MAAM,EAAE;MAAA;MAAArG,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAC9C;MACA,IAAIqB,QAAQ,GAAG,GAAG,EAAE;QAAA;QAAAzC,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QAClB+B,QAAQ,CAACS,IAAI,CAAC,2DAA2D,CAAC;MAC5E,CAAC;MAAA;MAAA;QAAA5D,aAAA,GAAAsB,CAAA;MAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAED,IAAIqB,QAAQ,GAAG,IAAI,EAAE;QAAA;QAAAzC,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACnB+B,QAAQ,CAACS,IAAI,CAAC,4DAA4D,CAAC;MAC7E,CAAC;MAAA;MAAA;QAAA5D,aAAA,GAAAsB,CAAA;MAAA;IACH,CAAC;IAAA;IAAA;MAAAtB,aAAA,GAAAsB,CAAA;IAAA;EACH;EAEA;;;EAGQ,OAAOyC,oBAAoBA,CACjCnB,aAA6B,EAC7BC,UAAmB,EACnBC,YAA2B,EAC3BC,oBAA8B,EAC9BC,yBAAmC;IAAA;IAAAhD,aAAA,GAAAqB,CAAA;IAEnC,MAAMyC,WAAW;IAAA;IAAA,CAAA9D,aAAA,GAAAoB,CAAA,QAAG;MAClBkF,WAAW,EAAE,GAAG;MAChBC,QAAQ,EAAE,GAAG;MACbC,QAAQ,EAAE,GAAG;MACbC,QAAQ,EAAE,GAAG;MACbC,UAAU,EAAE,GAAG;MACfC,eAAe,EAAE,GAAG;MACpBxC,QAAQ,EAAE;KACX;IAED;IAAA;IAAAnE,aAAA,GAAAoB,CAAA;IACA,IAAIyB,UAAU,EAAE;MAAA;MAAA7C,aAAA,GAAAsB,CAAA;MACd,MAAMkD,YAAY;MAAA;MAAA,CAAAxE,aAAA,GAAAoB,CAAA,QAAGyB,UAAU,GAAG,IAAI,CAACgB,oBAAoB;MAAC;MAAA7D,aAAA,GAAAoB,CAAA;MAC5D0C,WAAW,CAACwC,WAAW,GAAG9B,YAAY;MAAC;MAAAxE,aAAA,GAAAoB,CAAA;MACvC0C,WAAW,CAACyC,QAAQ,GAAG/B,YAAY;MAAC;MAAAxE,aAAA,GAAAoB,CAAA;MACpC0C,WAAW,CAAC0C,QAAQ,GAAGhC,YAAY;MAAC;MAAAxE,aAAA,GAAAoB,CAAA;MACpC0C,WAAW,CAAC2C,QAAQ,GAAGjC,YAAY;IACrC,CAAC;IAAA;IAAA;MAAAxE,aAAA,GAAAsB,CAAA;IAAA;IAED;IAAAtB,aAAA,GAAAoB,CAAA;IACA;IAAI;IAAA,CAAApB,aAAA,GAAAsB,CAAA,WAAAyB,oBAAoB;IAAA;IAAA,CAAA/C,aAAA,GAAAsB,CAAA,WAAIwB,YAAY,GAAE;MAAA;MAAA9C,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACxC;MAAI;MAAA,CAAApB,aAAA,GAAAsB,CAAA,WAAAwB,YAAY,CAAC8D,KAAK,KAAK,aAAa;MAAA;MAAA,CAAA5G,aAAA,GAAAsB,CAAA,WAAIwB,YAAY,CAAC+D,WAAW;MAAA;MAAA,CAAA7G,aAAA,GAAAsB,CAAA,WAAIwB,YAAY,CAAC+D,WAAW,GAAG,CAAC,GAAE;QAAA;QAAA7G,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACpG0C,WAAW,CAAC4C,UAAU,GAAG,IAAI,CAAC,CAAC;MACjC,CAAC;MAAA;MAAA;QAAA1G,aAAA,GAAAsB,CAAA;MAAA;IACH,CAAC;IAAA;IAAA;MAAAtB,aAAA,GAAAsB,CAAA;IAAA;IAED;IAAAtB,aAAA,GAAAoB,CAAA;IACA;IAAI;IAAA,CAAApB,aAAA,GAAAsB,CAAA,WAAA0B,yBAAyB;IAAA;IAAA,CAAAhD,aAAA,GAAAsB,CAAA,WAAIsB,aAAa,GAAE;MAAA;MAAA5C,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAC9C;MACA0C,WAAW,CAAC6C,eAAe,GAAG,GAAG;IACnC,CAAC;IAAA;IAAA;MAAA3G,aAAA,GAAAsB,CAAA;IAAA;IAED;IAAAtB,aAAA,GAAAoB,CAAA;IACA0C,WAAW,CAACK,QAAQ,GAAGL,WAAW,CAACwC,WAAW,GAAGxC,WAAW,CAACyC,QAAQ,GAC/CzC,WAAW,CAAC0C,QAAQ,GAAG1C,WAAW,CAAC2C,QAAQ,GAC3C3C,WAAW,CAAC4C,UAAU,GAAG5C,WAAW,CAAC6C,eAAe;IAAC;IAAA3G,aAAA,GAAAoB,CAAA;IAE3E,OAAO0C,WAAW;EACpB;EAEA;;;EAGQ,OAAOG,wBAAwBA,CACrCvB,MAA8B,EAC9BD,QAAgB,EAChBU,QAAkB;IAAA;IAAAnD,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAElB,QAAQsB,MAAM;MACZ,KAAKP,sBAAsB,CAAC2E,OAAO;QAAA;QAAA9G,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACjC,OAAO,IAAI,CAAC2F,kBAAkB,CAACtE,QAAQ,CAAC;MAE1C,KAAKN,sBAAsB,CAACmD,YAAY;QAAA;QAAAtF,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACtC,OAAO,IAAI,CAAC4F,sBAAsB,CAACvE,QAAQ,EAAEU,QAAQ,CAAC;MAExD,KAAKhB,sBAAsB,CAACwD,YAAY;QAAA;QAAA3F,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACtC,OAAO,IAAI,CAAC6F,wBAAwB,CAACxE,QAAQ,EAAEU,QAAQ,CAAC;MAE1D,KAAKhB,sBAAsB,CAACQ,gBAAgB;QAAA;QAAA3C,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QAC1C,OAAO,IAAI,CAAC8F,0BAA0B,CAACzE,QAAQ,CAAC;MAElD,KAAKN,sBAAsB,CAACuD,aAAa;QAAA;QAAA1F,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACvC,OAAO,IAAI,CAAC+F,wBAAwB,CAAC1E,QAAQ,EAAEU,QAAQ,CAAC;MAE1D;QAAA;QAAAnD,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACE,MAAM,IAAI8E,KAAK,CAAC,mCAAmCxD,MAAM,EAAE,CAAC;IAChE;EACF;EAEA;;;EAGQ,OAAOqE,kBAAkBA,CAACtE,QAAgB;IAAA;IAAAzC,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAChD,OAAO2E,IAAI,CAACqB,GAAG,CAAC3E,QAAQ,GAAG,IAAI,CAACqD,0BAA0B,EAAE,CAAC,CAAC;EAChE;EAEA;;;EAGQ,OAAOkB,sBAAsBA,CAACvE,QAAgB,EAAEU,QAAkB;IAAA;IAAAnD,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACxE,IAAI;MACF,MAAMiG,QAAQ;MAAA;MAAA,CAAArH,aAAA,GAAAoB,CAAA,QAAGa,yBAAA,CAAAwB,uBAAuB,CAAClB,yBAAyB,CAAC;QACjEE,QAAQ;QACR6E,QAAQ,EAAE;OACX,CAAC;MAAC;MAAAtH,aAAA,GAAAoB,CAAA;MACH+B,QAAQ,CAACS,IAAI,CAAC,GAAGyD,QAAQ,CAAClE,QAAQ,CAAC;MAAC;MAAAnD,aAAA,GAAAoB,CAAA;MACpC,OAAOiG,QAAQ,CAAC9C,gBAAgB;IAClC,CAAC,CAAC,OAAOgD,KAAK,EAAE;MAAA;MAAAvH,aAAA,GAAAoB,CAAA;MACd+B,QAAQ,CAACS,IAAI,CAAC,0DAA0D,CAAC;MAAC;MAAA5D,aAAA,GAAAoB,CAAA;MAC1E,OAAO,IAAI,CAAC2F,kBAAkB,CAACtE,QAAQ,CAAC;IAC1C;EACF;EAEA;;;EAGQ,OAAOwE,wBAAwBA,CAACxE,QAAgB,EAAEU,QAAkB;IAAA;IAAAnD,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAC1E;IACA,OAAO,IAAI,CAAC4F,sBAAsB,CAACvE,QAAQ,EAAEU,QAAQ,CAAC;EACxD;EAEA;;;EAGQ,OAAO+D,0BAA0BA,CAACzE,QAAgB;IAAA;IAAAzC,aAAA,GAAAqB,CAAA;IACxD;IACA,MAAMmG,MAAM;IAAA;IAAA,CAAAxH,aAAA,GAAAoB,CAAA,SAAG2E,IAAI,CAACqB,GAAG,CAAC3E,QAAQ,GAAG,IAAI,CAACqD,0BAA0B,EAAE,CAAC,CAAC;IAEtE;IACA,MAAM2B,kBAAkB;IAAA;IAAA,CAAAzH,aAAA,GAAAoB,CAAA,SAAG,CAAC,GAAG,CAACqB,QAAQ,GAAG,IAAI,IAAI,QAAQ,EAAC,CAAC;IAAA;IAAAzC,aAAA,GAAAoB,CAAA;IAE7D,OAAOoG,MAAM,GAAGzB,IAAI,CAACP,GAAG,CAAC,IAAI,EAAEO,IAAI,CAACR,GAAG,CAAC,IAAI,EAAEkC,kBAAkB,CAAC,CAAC;EACpE;EAEA;;;EAGQ,OAAON,wBAAwBA,CAAC1E,QAAgB,EAAEU,QAAkB;IAAA;IAAAnD,aAAA,GAAAqB,CAAA;IAC1E;IACA,MAAMmG,MAAM;IAAA;IAAA,CAAAxH,aAAA,GAAAoB,CAAA,SAAG,IAAI,CAAC2F,kBAAkB,CAACtE,QAAQ,CAAC;IAEhD;IACA,IAAIiF,aAAa;IAAA;IAAA,CAAA1H,aAAA,GAAAoB,CAAA,SAAG,GAAG;IAAC;IAAApB,aAAA,GAAAoB,CAAA;IAExB,IAAIqB,QAAQ,GAAG,IAAI,EAAE;MAAA;MAAAzC,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACnBsG,aAAa,GAAG,IAAI,CAAC,CAAC;IACxB,CAAC,MAAM;MAAA;MAAA1H,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA,IAAIqB,QAAQ,GAAG,IAAI,EAAE;QAAA;QAAAzC,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QAC1BsG,aAAa,GAAG,IAAI,CAAC,CAAC;MACxB,CAAC;MAAA;MAAA;QAAA1H,aAAA,GAAAsB,CAAA;MAAA;IAAD;IAAC;IAAAtB,aAAA,GAAAoB,CAAA;IAED+B,QAAQ,CAACS,IAAI,CAAC,2EAA2E,CAAC;IAAC;IAAA5D,aAAA,GAAAoB,CAAA;IAE3F,OAAOoG,MAAM,GAAGE,aAAa;EAC/B;EAEA;;;EAGQ,OAAOrD,0BAA0BA,CACvCE,gBAAwB,EACxB7B,MAA8B,EAC9BD,QAAgB,EAChBqB,WAAkD;IAAA;IAAA9D,aAAA,GAAAqB,CAAA;IAElD,MAAMsG,YAAY;IAAA;IAAA,CAAA3H,aAAA,GAAAoB,CAAA,SAAG,IAAI,CAACsD,eAAe,CAAChC,MAAM,CAAC;IAEjD;IACA,IAAIkF,gBAAgB;IAAA;IAAA,CAAA5H,aAAA,GAAAoB,CAAA,SAAGuG,YAAY;IAAC;IAAA3H,aAAA,GAAAoB,CAAA;IAEpC;IAAI;IAAA,CAAApB,aAAA,GAAAsB,CAAA,WAAAmB,QAAQ,GAAG,GAAG;IAAA;IAAA,CAAAzC,aAAA,GAAAsB,CAAA,WAAImB,QAAQ,GAAG,IAAI,GAAE;MAAA;MAAAzC,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACrCwG,gBAAgB,IAAI,IAAI,CAAC,CAAC;IAC5B,CAAC;IAAA;IAAA;MAAA5H,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAED,IAAI2E,IAAI,CAAC8B,GAAG,CAAC/D,WAAW,CAACK,QAAQ,GAAG,GAAG,CAAC,GAAG,GAAG,EAAE;MAAA;MAAAnE,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAC9CwG,gBAAgB,IAAI,IAAI,CAAC,CAAC;IAC5B,CAAC;IAAA;IAAA;MAAA5H,aAAA,GAAAsB,CAAA;IAAA;IAED,MAAMwG,WAAW;IAAA;IAAA,CAAA9H,aAAA,GAAAoB,CAAA,SAAGmD,gBAAgB,IAAI,CAAC,GAAGqD,gBAAgB,CAAC;IAAC;IAAA5H,aAAA,GAAAoB,CAAA;IAE9D,OAAO;MACL2G,KAAK,EAAExD,gBAAgB,GAAGuD,WAAW;MACrCE,KAAK,EAAEzD,gBAAgB,GAAGuD,WAAW;MACrCG,eAAe,EAAEL;KAClB;EACH;EAEA;;;EAGQ,OAAOtD,uBAAuBA,CACpC7B,QAAgB,EAChBC,MAA8B,EAC9BoB,WAAkD,EAClDV,eAAyB;IAAA;IAAApD,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAEzB;IACA,IAAIqB,QAAQ,GAAG,GAAG,EAAE;MAAA;MAAAzC,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAClBgC,eAAe,CAACQ,IAAI,CAAC,yEAAyE,CAAC;IACjG,CAAC,MAAM;MAAA;MAAA5D,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA,IAAIqB,QAAQ,GAAG,IAAI,EAAE;QAAA;QAAAzC,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QAC1BgC,eAAe,CAACQ,IAAI,CAAC,kEAAkE,CAAC;MAC1F,CAAC;MAAA;MAAA;QAAA5D,aAAA,GAAAsB,CAAA;MAAA;IAAD;IAEA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IACA;IAAI;IAAA,CAAApB,aAAA,GAAAsB,CAAA,WAAAoB,MAAM,KAAKP,sBAAsB,CAAC2E,OAAO;IAAA;IAAA,CAAA9G,aAAA,GAAAsB,CAAA,WAAImB,QAAQ,IAAI,GAAG;IAAA;IAAA,CAAAzC,aAAA,GAAAsB,CAAA,WAAImB,QAAQ,IAAI,IAAI,GAAE;MAAA;MAAAzC,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACpFgC,eAAe,CAACQ,IAAI,CAAC,iFAAiF,CAAC;IACzG,CAAC;IAAA;IAAA;MAAA5D,aAAA,GAAAsB,CAAA;IAAA;IAED;IAAAtB,aAAA,GAAAoB,CAAA;IACA,IAAI2E,IAAI,CAAC8B,GAAG,CAAC/D,WAAW,CAACK,QAAQ,GAAG,GAAG,CAAC,GAAG,IAAI,EAAE;MAAA;MAAAnE,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAC/CgC,eAAe,CAACQ,IAAI,CAAC,uEAAuE,CAAC;IAC/F,CAAC;IAAA;IAAA;MAAA5D,aAAA,GAAAsB,CAAA;IAAA;EACH;EAEA;;;EAGQ,OAAOuD,qBAAqBA,CAACnC,MAA8B;IAAA;IAAA1C,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACjE,QAAQsB,MAAM;MACZ,KAAKP,sBAAsB,CAAC2E,OAAO;QAAA;QAAA9G,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACjC,OAAO,gBAAgB;MACzB,KAAKe,sBAAsB,CAACmD,YAAY;QAAA;QAAAtF,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACtC,OAAO,gCAAgC;MACzC,KAAKe,sBAAsB,CAACwD,YAAY;QAAA;QAAA3F,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACtC,OAAO,wCAAwC;MACjD,KAAKe,sBAAsB,CAACQ,gBAAgB;QAAA;QAAA3C,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QAC1C,OAAO,oDAAoD;MAC7D,KAAKe,sBAAsB,CAACuD,aAAa;QAAA;QAAA1F,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACvC,OAAO,6CAA6C;MACtD;QAAA;QAAApB,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACE,OAAO,gBAAgB;IAC3B;EACF;EAEA;;;EAGQ,OAAO8D,oBAAoBA,CAACxC,MAA8B;IAAA;IAAA1C,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAChE,QAAQsB,MAAM;MACZ,KAAKP,sBAAsB,CAAC2E,OAAO;QAAA;QAAA9G,aAAA,GAAAsB,CAAA;MACnC,KAAKa,sBAAsB,CAACQ,gBAAgB;QAAA;QAAA3C,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QAC1C,OAAO,iCAAiC;MAC1C,KAAKe,sBAAsB,CAACmD,YAAY;QAAA;QAAAtF,aAAA,GAAAsB,CAAA;MACxC,KAAKa,sBAAsB,CAACwD,YAAY;QAAA;QAAA3F,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACtC,OAAO,iCAAiC;MAC1C,KAAKe,sBAAsB,CAACuD,aAAa;QAAA;QAAA1F,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACvC,OAAO,sCAAsC;MAC/C;QAAA;QAAApB,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACE,OAAO,sBAAsB;IACjC;EACF;;;;AAvcFgB,OAAA,CAAAE,0BAAA,GAAAA,0BAAA;AAwcC;AAAAtC,aAAA,GAAAoB,CAAA;AAvcyBkB,0BAAA,CAAA4F,OAAO,GAAG,OAAO;AAAC;AAAAlI,aAAA,GAAAoB,CAAA;AAClBkB,0BAAA,CAAAuB,oBAAoB,GAAG,KAAK,CAAC,CAAC;AAAA;AAAA7D,aAAA,GAAAoB,CAAA;AAC9BkB,0BAAA,CAAAwD,0BAA0B,GAAG,IAAI,CAAC,CAAC;AAE3D;AAAA;AAAA9F,aAAA,GAAAoB,CAAA;AACwBkB,0BAAA,CAAA+C,eAAe,GAAG;EACxCyB,OAAO,EAAE;IAAEvB,GAAG,EAAE,CAAC;IAAEC,GAAG,EAAE;EAAK,CAAE;EAC/BF,YAAY,EAAE;IAAEC,GAAG,EAAE,GAAG;IAAEC,GAAG,EAAE;EAAI,CAAE;EACrCG,YAAY,EAAE;IAAEJ,GAAG,EAAE,EAAE;IAAEC,GAAG,EAAE;EAAI,CAAE;EACpC7C,gBAAgB,EAAE;IAAE4C,GAAG,EAAE,CAAC;IAAEC,GAAG,EAAE;EAAK,CAAE;EACxCE,aAAa,EAAE;IAAEH,GAAG,EAAE,GAAG;IAAEC,GAAG,EAAE;EAAI;CACrC;AAED;AAAA;AAAAxF,aAAA,GAAAoB,CAAA;AACwBkB,0BAAA,CAAAoC,eAAe,GAAG;EACxC,CAACvC,sBAAsB,CAAC2E,OAAO,GAAG,IAAI;EACtC,CAAC3E,sBAAsB,CAACmD,YAAY,GAAG,IAAI;EAC3C,CAACnD,sBAAsB,CAACwD,YAAY,GAAG,IAAI;EAC3C,CAACxD,sBAAsB,CAACQ,gBAAgB,GAAG,IAAI;EAC/C,CAACR,sBAAsB,CAACuD,aAAa,GAAG;CACzC", "ignoreList": []}