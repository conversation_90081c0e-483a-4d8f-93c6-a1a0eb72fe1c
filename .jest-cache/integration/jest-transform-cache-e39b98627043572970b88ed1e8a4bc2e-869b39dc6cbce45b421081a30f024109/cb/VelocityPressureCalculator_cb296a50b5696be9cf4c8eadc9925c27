50376ba9198f8dd112787a22045485e9
"use strict";

/**
 * Velocity Pressure Calculator
 *
 * Comprehensive velocity pressure calculation service for Phase 3: Advanced Calculation Modules
 * Provides multiple calculation methods, environmental corrections, and performance optimization
 * for HVAC duct system velocity pressure calculations.
 *
 * @version 3.0.0
 * <AUTHOR> Suite Development Team
 */
/* istanbul ignore next */
function cov_l6ts6nd3r() {
  var path = "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\VelocityPressureCalculator.ts";
  var hash = "b7a2371f33da1de30b715fab97b5ec7d5610278d";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\VelocityPressureCalculator.ts",
    statementMap: {
      "0": {
        start: {
          line: 12,
          column: 0
        },
        end: {
          line: 12,
          column: 62
        }
      },
      "1": {
        start: {
          line: 13,
          column: 0
        },
        end: {
          line: 13,
          column: 103
        }
      },
      "2": {
        start: {
          line: 14,
          column: 34
        },
        end: {
          line: 14,
          column: 70
        }
      },
      "3": {
        start: {
          line: 19,
          column: 0
        },
        end: {
          line: 25,
          column: 93
        }
      },
      "4": {
        start: {
          line: 20,
          column: 4
        },
        end: {
          line: 20,
          column: 50
        }
      },
      "5": {
        start: {
          line: 21,
          column: 4
        },
        end: {
          line: 21,
          column: 60
        }
      },
      "6": {
        start: {
          line: 22,
          column: 4
        },
        end: {
          line: 22,
          column: 60
        }
      },
      "7": {
        start: {
          line: 23,
          column: 4
        },
        end: {
          line: 23,
          column: 68
        }
      },
      "8": {
        start: {
          line: 24,
          column: 4
        },
        end: {
          line: 24,
          column: 62
        }
      },
      "9": {
        start: {
          line: 30,
          column: 0
        },
        end: {
          line: 35,
          column: 72
        }
      },
      "10": {
        start: {
          line: 31,
          column: 4
        },
        end: {
          line: 31,
          column: 37
        }
      },
      "11": {
        start: {
          line: 32,
          column: 4
        },
        end: {
          line: 32,
          column: 39
        }
      },
      "12": {
        start: {
          line: 33,
          column: 4
        },
        end: {
          line: 33,
          column: 45
        }
      },
      "13": {
        start: {
          line: 34,
          column: 4
        },
        end: {
          line: 34,
          column: 41
        }
      },
      "14": {
        start: {
          line: 47,
          column: 229
        },
        end: {
          line: 47,
          column: 234
        }
      },
      "15": {
        start: {
          line: 48,
          column: 25
        },
        end: {
          line: 48,
          column: 27
        }
      },
      "16": {
        start: {
          line: 49,
          column: 32
        },
        end: {
          line: 49,
          column: 34
        }
      },
      "17": {
        start: {
          line: 51,
          column: 8
        },
        end: {
          line: 51,
          column: 62
        }
      },
      "18": {
        start: {
          line: 55,
          column: 8
        },
        end: {
          line: 68,
          column: 9
        }
      },
      "19": {
        start: {
          line: 56,
          column: 12
        },
        end: {
          line: 56,
          column: 42
        }
      },
      "20": {
        start: {
          line: 57,
          column: 12
        },
        end: {
          line: 57,
          column: 56
        }
      },
      "21": {
        start: {
          line: 59,
          column: 13
        },
        end: {
          line: 68,
          column: 9
        }
      },
      "22": {
        start: {
          line: 60,
          column: 29
        },
        end: {
          line: 60,
          column: 116
        }
      },
      "23": {
        start: {
          line: 61,
          column: 12
        },
        end: {
          line: 61,
          column: 48
        }
      },
      "24": {
        start: {
          line: 62,
          column: 12
        },
        end: {
          line: 62,
          column: 68
        }
      },
      "25": {
        start: {
          line: 63,
          column: 12
        },
        end: {
          line: 63,
          column: 48
        }
      },
      "26": {
        start: {
          line: 66,
          column: 12
        },
        end: {
          line: 66,
          column: 57
        }
      },
      "27": {
        start: {
          line: 67,
          column: 12
        },
        end: {
          line: 67,
          column: 69
        }
      },
      "28": {
        start: {
          line: 70,
          column: 28
        },
        end: {
          line: 70,
          column: 149
        }
      },
      "29": {
        start: {
          line: 72,
          column: 37
        },
        end: {
          line: 72,
          column: 94
        }
      },
      "30": {
        start: {
          line: 74,
          column: 42
        },
        end: {
          line: 74,
          column: 85
        }
      },
      "31": {
        start: {
          line: 76,
          column: 34
        },
        end: {
          line: 76,
          column: 123
        }
      },
      "32": {
        start: {
          line: 78,
          column: 8
        },
        end: {
          line: 78,
          column: 85
        }
      },
      "33": {
        start: {
          line: 79,
          column: 8
        },
        end: {
          line: 100,
          column: 10
        }
      },
      "34": {
        start: {
          line: 107,
          column: 29
        },
        end: {
          line: 108,
          column: 61
        }
      },
      "35": {
        start: {
          line: 109,
          column: 27
        },
        end: {
          line: 110,
          column: 62
        }
      },
      "36": {
        start: {
          line: 112,
          column: 8
        },
        end: {
          line: 114,
          column: 9
        }
      },
      "37": {
        start: {
          line: 113,
          column: 12
        },
        end: {
          line: 113,
          column: 56
        }
      },
      "38": {
        start: {
          line: 115,
          column: 8
        },
        end: {
          line: 117,
          column: 9
        }
      },
      "39": {
        start: {
          line: 116,
          column: 12
        },
        end: {
          line: 116,
          column: 55
        }
      },
      "40": {
        start: {
          line: 118,
          column: 8
        },
        end: {
          line: 120,
          column: 9
        }
      },
      "41": {
        start: {
          line: 119,
          column: 12
        },
        end: {
          line: 119,
          column: 55
        }
      },
      "42": {
        start: {
          line: 121,
          column: 8
        },
        end: {
          line: 121,
          column: 55
        }
      },
      "43": {
        start: {
          line: 127,
          column: 25
        },
        end: {
          line: 127,
          column: 27
        }
      },
      "44": {
        start: {
          line: 130,
          column: 8
        },
        end: {
          line: 140,
          column: 9
        }
      },
      "45": {
        start: {
          line: 131,
          column: 12
        },
        end: {
          line: 131,
          column: 42
        }
      },
      "46": {
        start: {
          line: 133,
          column: 13
        },
        end: {
          line: 140,
          column: 9
        }
      },
      "47": {
        start: {
          line: 134,
          column: 29
        },
        end: {
          line: 134,
          column: 116
        }
      },
      "48": {
        start: {
          line: 135,
          column: 12
        },
        end: {
          line: 135,
          column: 48
        }
      },
      "49": {
        start: {
          line: 136,
          column: 12
        },
        end: {
          line: 136,
          column: 48
        }
      },
      "50": {
        start: {
          line: 139,
          column: 12
        },
        end: {
          line: 139,
          column: 57
        }
      },
      "51": {
        start: {
          line: 142,
          column: 29
        },
        end: {
          line: 142,
          column: 73
        }
      },
      "52": {
        start: {
          line: 143,
          column: 27
        },
        end: {
          line: 143,
          column: 58
        }
      },
      "53": {
        start: {
          line: 144,
          column: 25
        },
        end: {
          line: 144,
          column: 80
        }
      },
      "54": {
        start: {
          line: 145,
          column: 8
        },
        end: {
          line: 149,
          column: 10
        }
      },
      "55": {
        start: {
          line: 155,
          column: 8
        },
        end: {
          line: 156,
          column: 19
        }
      },
      "56": {
        start: {
          line: 156,
          column: 12
        },
        end: {
          line: 156,
          column: 19
        }
      },
      "57": {
        start: {
          line: 157,
          column: 79
        },
        end: {
          line: 157,
          column: 84
        }
      },
      "58": {
        start: {
          line: 159,
          column: 8
        },
        end: {
          line: 161,
          column: 9
        }
      },
      "59": {
        start: {
          line: 160,
          column: 12
        },
        end: {
          line: 160,
          column: 59
        }
      },
      "60": {
        start: {
          line: 162,
          column: 8
        },
        end: {
          line: 163,
          column: 19
        }
      },
      "61": {
        start: {
          line: 163,
          column: 12
        },
        end: {
          line: 163,
          column: 19
        }
      },
      "62": {
        start: {
          line: 165,
          column: 8
        },
        end: {
          line: 167,
          column: 9
        }
      },
      "63": {
        start: {
          line: 166,
          column: 12
        },
        end: {
          line: 166,
          column: 79
        }
      },
      "64": {
        start: {
          line: 168,
          column: 22
        },
        end: {
          line: 168,
          column: 50
        }
      },
      "65": {
        start: {
          line: 169,
          column: 8
        },
        end: {
          line: 171,
          column: 9
        }
      },
      "66": {
        start: {
          line: 170,
          column: 12
        },
        end: {
          line: 170,
          column: 132
        }
      },
      "67": {
        start: {
          line: 172,
          column: 8
        },
        end: {
          line: 180,
          column: 9
        }
      },
      "68": {
        start: {
          line: 174,
          column: 12
        },
        end: {
          line: 176,
          column: 13
        }
      },
      "69": {
        start: {
          line: 175,
          column: 16
        },
        end: {
          line: 175,
          column: 91
        }
      },
      "70": {
        start: {
          line: 177,
          column: 12
        },
        end: {
          line: 179,
          column: 13
        }
      },
      "71": {
        start: {
          line: 178,
          column: 16
        },
        end: {
          line: 178,
          column: 92
        }
      },
      "72": {
        start: {
          line: 186,
          column: 28
        },
        end: {
          line: 194,
          column: 9
        }
      },
      "73": {
        start: {
          line: 196,
          column: 8
        },
        end: {
          line: 202,
          column: 9
        }
      },
      "74": {
        start: {
          line: 197,
          column: 33
        },
        end: {
          line: 197,
          column: 71
        }
      },
      "75": {
        start: {
          line: 198,
          column: 12
        },
        end: {
          line: 198,
          column: 51
        }
      },
      "76": {
        start: {
          line: 199,
          column: 12
        },
        end: {
          line: 199,
          column: 48
        }
      },
      "77": {
        start: {
          line: 200,
          column: 12
        },
        end: {
          line: 200,
          column: 48
        }
      },
      "78": {
        start: {
          line: 201,
          column: 12
        },
        end: {
          line: 201,
          column: 48
        }
      },
      "79": {
        start: {
          line: 204,
          column: 8
        },
        end: {
          line: 208,
          column: 9
        }
      },
      "80": {
        start: {
          line: 205,
          column: 12
        },
        end: {
          line: 207,
          column: 13
        }
      },
      "81": {
        start: {
          line: 206,
          column: 16
        },
        end: {
          line: 206,
          column: 46
        }
      },
      "82": {
        start: {
          line: 210,
          column: 8
        },
        end: {
          line: 213,
          column: 9
        }
      },
      "83": {
        start: {
          line: 212,
          column: 12
        },
        end: {
          line: 212,
          column: 46
        }
      },
      "84": {
        start: {
          line: 215,
          column: 8
        },
        end: {
          line: 217,
          column: 65
        }
      },
      "85": {
        start: {
          line: 218,
          column: 8
        },
        end: {
          line: 218,
          column: 27
        }
      },
      "86": {
        start: {
          line: 224,
          column: 8
        },
        end: {
          line: 237,
          column: 9
        }
      },
      "87": {
        start: {
          line: 226,
          column: 16
        },
        end: {
          line: 226,
          column: 57
        }
      },
      "88": {
        start: {
          line: 228,
          column: 16
        },
        end: {
          line: 228,
          column: 71
        }
      },
      "89": {
        start: {
          line: 230,
          column: 16
        },
        end: {
          line: 230,
          column: 73
        }
      },
      "90": {
        start: {
          line: 232,
          column: 16
        },
        end: {
          line: 232,
          column: 65
        }
      },
      "91": {
        start: {
          line: 234,
          column: 16
        },
        end: {
          line: 234,
          column: 73
        }
      },
      "92": {
        start: {
          line: 236,
          column: 16
        },
        end: {
          line: 236,
          column: 77
        }
      },
      "93": {
        start: {
          line: 243,
          column: 8
        },
        end: {
          line: 243,
          column: 71
        }
      },
      "94": {
        start: {
          line: 249,
          column: 8
        },
        end: {
          line: 260,
          column: 9
        }
      },
      "95": {
        start: {
          line: 250,
          column: 29
        },
        end: {
          line: 253,
          column: 14
        }
      },
      "96": {
        start: {
          line: 254,
          column: 12
        },
        end: {
          line: 254,
          column: 48
        }
      },
      "97": {
        start: {
          line: 255,
          column: 12
        },
        end: {
          line: 255,
          column: 45
        }
      },
      "98": {
        start: {
          line: 258,
          column: 12
        },
        end: {
          line: 258,
          column: 86
        }
      },
      "99": {
        start: {
          line: 259,
          column: 12
        },
        end: {
          line: 259,
          column: 53
        }
      },
      "100": {
        start: {
          line: 267,
          column: 8
        },
        end: {
          line: 267,
          column: 63
        }
      },
      "101": {
        start: {
          line: 274,
          column: 23
        },
        end: {
          line: 274,
          column: 78
        }
      },
      "102": {
        start: {
          line: 276,
          column: 35
        },
        end: {
          line: 276,
          column: 67
        }
      },
      "103": {
        start: {
          line: 277,
          column: 8
        },
        end: {
          line: 277,
          column: 75
        }
      },
      "104": {
        start: {
          line: 284,
          column: 23
        },
        end: {
          line: 284,
          column: 56
        }
      },
      "105": {
        start: {
          line: 286,
          column: 28
        },
        end: {
          line: 286,
          column: 31
        }
      },
      "106": {
        start: {
          line: 287,
          column: 8
        },
        end: {
          line: 292,
          column: 9
        }
      },
      "107": {
        start: {
          line: 288,
          column: 12
        },
        end: {
          line: 288,
          column: 33
        }
      },
      "108": {
        start: {
          line: 290,
          column: 13
        },
        end: {
          line: 292,
          column: 9
        }
      },
      "109": {
        start: {
          line: 291,
          column: 12
        },
        end: {
          line: 291,
          column: 33
        }
      },
      "110": {
        start: {
          line: 293,
          column: 8
        },
        end: {
          line: 293,
          column: 99
        }
      },
      "111": {
        start: {
          line: 294,
          column: 8
        },
        end: {
          line: 294,
          column: 38
        }
      },
      "112": {
        start: {
          line: 300,
          column: 29
        },
        end: {
          line: 300,
          column: 57
        }
      },
      "113": {
        start: {
          line: 302,
          column: 31
        },
        end: {
          line: 302,
          column: 43
        }
      },
      "114": {
        start: {
          line: 303,
          column: 8
        },
        end: {
          line: 305,
          column: 9
        }
      },
      "115": {
        start: {
          line: 304,
          column: 12
        },
        end: {
          line: 304,
          column: 37
        }
      },
      "116": {
        start: {
          line: 306,
          column: 8
        },
        end: {
          line: 308,
          column: 9
        }
      },
      "117": {
        start: {
          line: 307,
          column: 12
        },
        end: {
          line: 307,
          column: 37
        }
      },
      "118": {
        start: {
          line: 309,
          column: 28
        },
        end: {
          line: 309,
          column: 69
        }
      },
      "119": {
        start: {
          line: 310,
          column: 8
        },
        end: {
          line: 314,
          column: 10
        }
      },
      "120": {
        start: {
          line: 321,
          column: 8
        },
        end: {
          line: 326,
          column: 9
        }
      },
      "121": {
        start: {
          line: 322,
          column: 12
        },
        end: {
          line: 322,
          column: 108
        }
      },
      "122": {
        start: {
          line: 324,
          column: 13
        },
        end: {
          line: 326,
          column: 9
        }
      },
      "123": {
        start: {
          line: 325,
          column: 12
        },
        end: {
          line: 325,
          column: 101
        }
      },
      "124": {
        start: {
          line: 328,
          column: 8
        },
        end: {
          line: 330,
          column: 9
        }
      },
      "125": {
        start: {
          line: 329,
          column: 12
        },
        end: {
          line: 329,
          column: 116
        }
      },
      "126": {
        start: {
          line: 332,
          column: 8
        },
        end: {
          line: 334,
          column: 9
        }
      },
      "127": {
        start: {
          line: 333,
          column: 12
        },
        end: {
          line: 333,
          column: 106
        }
      },
      "128": {
        start: {
          line: 340,
          column: 8
        },
        end: {
          line: 353,
          column: 9
        }
      },
      "129": {
        start: {
          line: 342,
          column: 16
        },
        end: {
          line: 342,
          column: 40
        }
      },
      "130": {
        start: {
          line: 344,
          column: 16
        },
        end: {
          line: 344,
          column: 56
        }
      },
      "131": {
        start: {
          line: 346,
          column: 16
        },
        end: {
          line: 346,
          column: 64
        }
      },
      "132": {
        start: {
          line: 348,
          column: 16
        },
        end: {
          line: 348,
          column: 76
        }
      },
      "133": {
        start: {
          line: 350,
          column: 16
        },
        end: {
          line: 350,
          column: 69
        }
      },
      "134": {
        start: {
          line: 352,
          column: 16
        },
        end: {
          line: 352,
          column: 40
        }
      },
      "135": {
        start: {
          line: 359,
          column: 8
        },
        end: {
          line: 370,
          column: 9
        }
      },
      "136": {
        start: {
          line: 362,
          column: 16
        },
        end: {
          line: 362,
          column: 57
        }
      },
      "137": {
        start: {
          line: 365,
          column: 16
        },
        end: {
          line: 365,
          column: 57
        }
      },
      "138": {
        start: {
          line: 367,
          column: 16
        },
        end: {
          line: 367,
          column: 62
        }
      },
      "139": {
        start: {
          line: 369,
          column: 16
        },
        end: {
          line: 369,
          column: 46
        }
      },
      "140": {
        start: {
          line: 373,
          column: 0
        },
        end: {
          line: 373,
          column: 64
        }
      },
      "141": {
        start: {
          line: 374,
          column: 0
        },
        end: {
          line: 374,
          column: 45
        }
      },
      "142": {
        start: {
          line: 375,
          column: 0
        },
        end: {
          line: 375,
          column: 56
        }
      },
      "143": {
        start: {
          line: 376,
          column: 0
        },
        end: {
          line: 376,
          column: 61
        }
      },
      "144": {
        start: {
          line: 378,
          column: 0
        },
        end: {
          line: 384,
          column: 2
        }
      },
      "145": {
        start: {
          line: 386,
          column: 0
        },
        end: {
          line: 392,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 19,
            column: 1
          },
          end: {
            line: 19,
            column: 2
          }
        },
        loc: {
          start: {
            line: 19,
            column: 35
          },
          end: {
            line: 25,
            column: 1
          }
        },
        line: 19
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 30,
            column: 1
          },
          end: {
            line: 30,
            column: 2
          }
        },
        loc: {
          start: {
            line: 30,
            column: 28
          },
          end: {
            line: 35,
            column: 1
          }
        },
        line: 30
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 46,
            column: 4
          },
          end: {
            line: 46,
            column: 5
          }
        },
        loc: {
          start: {
            line: 46,
            column: 44
          },
          end: {
            line: 101,
            column: 5
          }
        },
        line: 46
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 105,
            column: 4
          },
          end: {
            line: 105,
            column: 5
          }
        },
        loc: {
          start: {
            line: 105,
            column: 76
          },
          end: {
            line: 122,
            column: 5
          }
        },
        line: 105
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 126,
            column: 4
          },
          end: {
            line: 126,
            column: 5
          }
        },
        loc: {
          start: {
            line: 126,
            column: 86
          },
          end: {
            line: 150,
            column: 5
          }
        },
        line: 126
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 154,
            column: 4
          },
          end: {
            line: 154,
            column: 5
          }
        },
        loc: {
          start: {
            line: 154,
            column: 60
          },
          end: {
            line: 181,
            column: 5
          }
        },
        line: 154
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 185,
            column: 4
          },
          end: {
            line: 185,
            column: 5
          }
        },
        loc: {
          start: {
            line: 185,
            column: 122
          },
          end: {
            line: 219,
            column: 5
          }
        },
        line: 185
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 223,
            column: 4
          },
          end: {
            line: 223,
            column: 5
          }
        },
        loc: {
          start: {
            line: 223,
            column: 64
          },
          end: {
            line: 238,
            column: 5
          }
        },
        line: 223
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 242,
            column: 4
          },
          end: {
            line: 242,
            column: 5
          }
        },
        loc: {
          start: {
            line: 242,
            column: 40
          },
          end: {
            line: 244,
            column: 5
          }
        },
        line: 242
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 248,
            column: 4
          },
          end: {
            line: 248,
            column: 5
          }
        },
        loc: {
          start: {
            line: 248,
            column: 54
          },
          end: {
            line: 261,
            column: 5
          }
        },
        line: 248
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 265,
            column: 4
          },
          end: {
            line: 265,
            column: 5
          }
        },
        loc: {
          start: {
            line: 265,
            column: 56
          },
          end: {
            line: 268,
            column: 5
          }
        },
        line: 265
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 272,
            column: 4
          },
          end: {
            line: 272,
            column: 5
          }
        },
        loc: {
          start: {
            line: 272,
            column: 48
          },
          end: {
            line: 278,
            column: 5
          }
        },
        line: 272
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 282,
            column: 4
          },
          end: {
            line: 282,
            column: 5
          }
        },
        loc: {
          start: {
            line: 282,
            column: 56
          },
          end: {
            line: 295,
            column: 5
          }
        },
        line: 282
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 299,
            column: 4
          },
          end: {
            line: 299,
            column: 5
          }
        },
        loc: {
          start: {
            line: 299,
            column: 87
          },
          end: {
            line: 315,
            column: 5
          }
        },
        line: 299
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 319,
            column: 4
          },
          end: {
            line: 319,
            column: 5
          }
        },
        loc: {
          start: {
            line: 319,
            column: 83
          },
          end: {
            line: 335,
            column: 5
          }
        },
        line: 319
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 339,
            column: 4
          },
          end: {
            line: 339,
            column: 5
          }
        },
        loc: {
          start: {
            line: 339,
            column: 41
          },
          end: {
            line: 354,
            column: 5
          }
        },
        line: 339
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 358,
            column: 4
          },
          end: {
            line: 358,
            column: 5
          }
        },
        loc: {
          start: {
            line: 358,
            column: 40
          },
          end: {
            line: 371,
            column: 5
          }
        },
        line: 358
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 25,
            column: 3
          },
          end: {
            line: 25,
            column: 91
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 25,
            column: 3
          },
          end: {
            line: 25,
            column: 25
          }
        }, {
          start: {
            line: 25,
            column: 30
          },
          end: {
            line: 25,
            column: 90
          }
        }],
        line: 25
      },
      "1": {
        loc: {
          start: {
            line: 35,
            column: 3
          },
          end: {
            line: 35,
            column: 70
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 35,
            column: 3
          },
          end: {
            line: 35,
            column: 18
          }
        }, {
          start: {
            line: 35,
            column: 23
          },
          end: {
            line: 35,
            column: 69
          }
        }],
        line: 35
      },
      "2": {
        loc: {
          start: {
            line: 47,
            column: 26
          },
          end: {
            line: 47,
            column: 74
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 47,
            column: 35
          },
          end: {
            line: 47,
            column: 74
          }
        }],
        line: 47
      },
      "3": {
        loc: {
          start: {
            line: 47,
            column: 117
          },
          end: {
            line: 47,
            column: 145
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 47,
            column: 140
          },
          end: {
            line: 47,
            column: 145
          }
        }],
        line: 47
      },
      "4": {
        loc: {
          start: {
            line: 47,
            column: 147
          },
          end: {
            line: 47,
            column: 180
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 47,
            column: 175
          },
          end: {
            line: 47,
            column: 180
          }
        }],
        line: 47
      },
      "5": {
        loc: {
          start: {
            line: 47,
            column: 182
          },
          end: {
            line: 47,
            column: 224
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 47,
            column: 200
          },
          end: {
            line: 47,
            column: 224
          }
        }],
        line: 47
      },
      "6": {
        loc: {
          start: {
            line: 55,
            column: 8
          },
          end: {
            line: 68,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 55,
            column: 8
          },
          end: {
            line: 68,
            column: 9
          }
        }, {
          start: {
            line: 59,
            column: 13
          },
          end: {
            line: 68,
            column: 9
          }
        }],
        line: 55
      },
      "7": {
        loc: {
          start: {
            line: 59,
            column: 13
          },
          end: {
            line: 68,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 59,
            column: 13
          },
          end: {
            line: 68,
            column: 9
          }
        }, {
          start: {
            line: 65,
            column: 13
          },
          end: {
            line: 68,
            column: 9
          }
        }],
        line: 59
      },
      "8": {
        loc: {
          start: {
            line: 105,
            column: 53
          },
          end: {
            line: 105,
            column: 74
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 105,
            column: 64
          },
          end: {
            line: 105,
            column: 74
          }
        }],
        line: 105
      },
      "9": {
        loc: {
          start: {
            line: 107,
            column: 29
          },
          end: {
            line: 108,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 107,
            column: 29
          },
          end: {
            line: 107,
            column: 78
          }
        }, {
          start: {
            line: 108,
            column: 12
          },
          end: {
            line: 108,
            column: 61
          }
        }],
        line: 107
      },
      "10": {
        loc: {
          start: {
            line: 109,
            column: 27
          },
          end: {
            line: 110,
            column: 62
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 109,
            column: 27
          },
          end: {
            line: 109,
            column: 77
          }
        }, {
          start: {
            line: 110,
            column: 12
          },
          end: {
            line: 110,
            column: 62
          }
        }],
        line: 109
      },
      "11": {
        loc: {
          start: {
            line: 112,
            column: 8
          },
          end: {
            line: 114,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 112,
            column: 8
          },
          end: {
            line: 114,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 112
      },
      "12": {
        loc: {
          start: {
            line: 112,
            column: 12
          },
          end: {
            line: 112,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 112,
            column: 12
          },
          end: {
            line: 112,
            column: 34
          }
        }, {
          start: {
            line: 112,
            column: 38
          },
          end: {
            line: 112,
            column: 48
          }
        }],
        line: 112
      },
      "13": {
        loc: {
          start: {
            line: 115,
            column: 8
          },
          end: {
            line: 117,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 115,
            column: 8
          },
          end: {
            line: 117,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 115
      },
      "14": {
        loc: {
          start: {
            line: 115,
            column: 12
          },
          end: {
            line: 115,
            column: 47
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 115,
            column: 12
          },
          end: {
            line: 115,
            column: 31
          }
        }, {
          start: {
            line: 115,
            column: 35
          },
          end: {
            line: 115,
            column: 47
          }
        }],
        line: 115
      },
      "15": {
        loc: {
          start: {
            line: 118,
            column: 8
          },
          end: {
            line: 120,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 118,
            column: 8
          },
          end: {
            line: 120,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 118
      },
      "16": {
        loc: {
          start: {
            line: 118,
            column: 12
          },
          end: {
            line: 118,
            column: 42
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 118,
            column: 12
          },
          end: {
            line: 118,
            column: 24
          }
        }, {
          start: {
            line: 118,
            column: 28
          },
          end: {
            line: 118,
            column: 42
          }
        }],
        line: 118
      },
      "17": {
        loc: {
          start: {
            line: 130,
            column: 8
          },
          end: {
            line: 140,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 130,
            column: 8
          },
          end: {
            line: 140,
            column: 9
          }
        }, {
          start: {
            line: 133,
            column: 13
          },
          end: {
            line: 140,
            column: 9
          }
        }],
        line: 130
      },
      "18": {
        loc: {
          start: {
            line: 133,
            column: 13
          },
          end: {
            line: 140,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 133,
            column: 13
          },
          end: {
            line: 140,
            column: 9
          }
        }, {
          start: {
            line: 138,
            column: 13
          },
          end: {
            line: 140,
            column: 9
          }
        }],
        line: 133
      },
      "19": {
        loc: {
          start: {
            line: 155,
            column: 8
          },
          end: {
            line: 156,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 155,
            column: 8
          },
          end: {
            line: 156,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 155
      },
      "20": {
        loc: {
          start: {
            line: 157,
            column: 26
          },
          end: {
            line: 157,
            column: 74
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 157,
            column: 35
          },
          end: {
            line: 157,
            column: 74
          }
        }],
        line: 157
      },
      "21": {
        loc: {
          start: {
            line: 159,
            column: 8
          },
          end: {
            line: 161,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 159,
            column: 8
          },
          end: {
            line: 161,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 159
      },
      "22": {
        loc: {
          start: {
            line: 162,
            column: 8
          },
          end: {
            line: 163,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 162,
            column: 8
          },
          end: {
            line: 163,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 162
      },
      "23": {
        loc: {
          start: {
            line: 165,
            column: 8
          },
          end: {
            line: 167,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 165,
            column: 8
          },
          end: {
            line: 167,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 165
      },
      "24": {
        loc: {
          start: {
            line: 169,
            column: 8
          },
          end: {
            line: 171,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 169,
            column: 8
          },
          end: {
            line: 171,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 169
      },
      "25": {
        loc: {
          start: {
            line: 169,
            column: 12
          },
          end: {
            line: 169,
            column: 56
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 169,
            column: 12
          },
          end: {
            line: 169,
            column: 32
          }
        }, {
          start: {
            line: 169,
            column: 36
          },
          end: {
            line: 169,
            column: 56
          }
        }],
        line: 169
      },
      "26": {
        loc: {
          start: {
            line: 172,
            column: 8
          },
          end: {
            line: 180,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 172,
            column: 8
          },
          end: {
            line: 180,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 172
      },
      "27": {
        loc: {
          start: {
            line: 174,
            column: 12
          },
          end: {
            line: 176,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 174,
            column: 12
          },
          end: {
            line: 176,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 174
      },
      "28": {
        loc: {
          start: {
            line: 177,
            column: 12
          },
          end: {
            line: 179,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 177,
            column: 12
          },
          end: {
            line: 179,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 177
      },
      "29": {
        loc: {
          start: {
            line: 196,
            column: 8
          },
          end: {
            line: 202,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 196,
            column: 8
          },
          end: {
            line: 202,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 196
      },
      "30": {
        loc: {
          start: {
            line: 204,
            column: 8
          },
          end: {
            line: 208,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 204,
            column: 8
          },
          end: {
            line: 208,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 204
      },
      "31": {
        loc: {
          start: {
            line: 204,
            column: 12
          },
          end: {
            line: 204,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 204,
            column: 12
          },
          end: {
            line: 204,
            column: 32
          }
        }, {
          start: {
            line: 204,
            column: 36
          },
          end: {
            line: 204,
            column: 48
          }
        }],
        line: 204
      },
      "32": {
        loc: {
          start: {
            line: 205,
            column: 12
          },
          end: {
            line: 207,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 205,
            column: 12
          },
          end: {
            line: 207,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 205
      },
      "33": {
        loc: {
          start: {
            line: 205,
            column: 16
          },
          end: {
            line: 205,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 205,
            column: 16
          },
          end: {
            line: 205,
            column: 52
          }
        }, {
          start: {
            line: 205,
            column: 56
          },
          end: {
            line: 205,
            column: 80
          }
        }, {
          start: {
            line: 205,
            column: 84
          },
          end: {
            line: 205,
            column: 112
          }
        }],
        line: 205
      },
      "34": {
        loc: {
          start: {
            line: 210,
            column: 8
          },
          end: {
            line: 213,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 210,
            column: 8
          },
          end: {
            line: 213,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 210
      },
      "35": {
        loc: {
          start: {
            line: 210,
            column: 12
          },
          end: {
            line: 210,
            column: 54
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 210,
            column: 12
          },
          end: {
            line: 210,
            column: 37
          }
        }, {
          start: {
            line: 210,
            column: 41
          },
          end: {
            line: 210,
            column: 54
          }
        }],
        line: 210
      },
      "36": {
        loc: {
          start: {
            line: 224,
            column: 8
          },
          end: {
            line: 237,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 225,
            column: 12
          },
          end: {
            line: 226,
            column: 57
          }
        }, {
          start: {
            line: 227,
            column: 12
          },
          end: {
            line: 228,
            column: 71
          }
        }, {
          start: {
            line: 229,
            column: 12
          },
          end: {
            line: 230,
            column: 73
          }
        }, {
          start: {
            line: 231,
            column: 12
          },
          end: {
            line: 232,
            column: 65
          }
        }, {
          start: {
            line: 233,
            column: 12
          },
          end: {
            line: 234,
            column: 73
          }
        }, {
          start: {
            line: 235,
            column: 12
          },
          end: {
            line: 236,
            column: 77
          }
        }],
        line: 224
      },
      "37": {
        loc: {
          start: {
            line: 287,
            column: 8
          },
          end: {
            line: 292,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 287,
            column: 8
          },
          end: {
            line: 292,
            column: 9
          }
        }, {
          start: {
            line: 290,
            column: 13
          },
          end: {
            line: 292,
            column: 9
          }
        }],
        line: 287
      },
      "38": {
        loc: {
          start: {
            line: 290,
            column: 13
          },
          end: {
            line: 292,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 290,
            column: 13
          },
          end: {
            line: 292,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 290
      },
      "39": {
        loc: {
          start: {
            line: 303,
            column: 8
          },
          end: {
            line: 305,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 303,
            column: 8
          },
          end: {
            line: 305,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 303
      },
      "40": {
        loc: {
          start: {
            line: 303,
            column: 12
          },
          end: {
            line: 303,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 303,
            column: 12
          },
          end: {
            line: 303,
            column: 26
          }
        }, {
          start: {
            line: 303,
            column: 30
          },
          end: {
            line: 303,
            column: 45
          }
        }],
        line: 303
      },
      "41": {
        loc: {
          start: {
            line: 306,
            column: 8
          },
          end: {
            line: 308,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 306,
            column: 8
          },
          end: {
            line: 308,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 306
      },
      "42": {
        loc: {
          start: {
            line: 321,
            column: 8
          },
          end: {
            line: 326,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 321,
            column: 8
          },
          end: {
            line: 326,
            column: 9
          }
        }, {
          start: {
            line: 324,
            column: 13
          },
          end: {
            line: 326,
            column: 9
          }
        }],
        line: 321
      },
      "43": {
        loc: {
          start: {
            line: 324,
            column: 13
          },
          end: {
            line: 326,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 324,
            column: 13
          },
          end: {
            line: 326,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 324
      },
      "44": {
        loc: {
          start: {
            line: 328,
            column: 8
          },
          end: {
            line: 330,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 328,
            column: 8
          },
          end: {
            line: 330,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 328
      },
      "45": {
        loc: {
          start: {
            line: 328,
            column: 12
          },
          end: {
            line: 328,
            column: 92
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 328,
            column: 12
          },
          end: {
            line: 328,
            column: 53
          }
        }, {
          start: {
            line: 328,
            column: 57
          },
          end: {
            line: 328,
            column: 72
          }
        }, {
          start: {
            line: 328,
            column: 76
          },
          end: {
            line: 328,
            column: 92
          }
        }],
        line: 328
      },
      "46": {
        loc: {
          start: {
            line: 332,
            column: 8
          },
          end: {
            line: 334,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 332,
            column: 8
          },
          end: {
            line: 334,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 332
      },
      "47": {
        loc: {
          start: {
            line: 340,
            column: 8
          },
          end: {
            line: 353,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 341,
            column: 12
          },
          end: {
            line: 342,
            column: 40
          }
        }, {
          start: {
            line: 343,
            column: 12
          },
          end: {
            line: 344,
            column: 56
          }
        }, {
          start: {
            line: 345,
            column: 12
          },
          end: {
            line: 346,
            column: 64
          }
        }, {
          start: {
            line: 347,
            column: 12
          },
          end: {
            line: 348,
            column: 76
          }
        }, {
          start: {
            line: 349,
            column: 12
          },
          end: {
            line: 350,
            column: 69
          }
        }, {
          start: {
            line: 351,
            column: 12
          },
          end: {
            line: 352,
            column: 40
          }
        }],
        line: 340
      },
      "48": {
        loc: {
          start: {
            line: 359,
            column: 8
          },
          end: {
            line: 370,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 360,
            column: 12
          },
          end: {
            line: 360,
            column: 48
          }
        }, {
          start: {
            line: 361,
            column: 12
          },
          end: {
            line: 362,
            column: 57
          }
        }, {
          start: {
            line: 363,
            column: 12
          },
          end: {
            line: 363,
            column: 53
          }
        }, {
          start: {
            line: 364,
            column: 12
          },
          end: {
            line: 365,
            column: 57
          }
        }, {
          start: {
            line: 366,
            column: 12
          },
          end: {
            line: 367,
            column: 62
          }
        }, {
          start: {
            line: 368,
            column: 12
          },
          end: {
            line: 369,
            column: 46
          }
        }],
        line: 359
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0],
      "3": [0],
      "4": [0],
      "5": [0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0, 0, 0, 0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0, 0],
      "46": [0, 0],
      "47": [0, 0, 0, 0, 0, 0],
      "48": [0, 0, 0, 0, 0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\VelocityPressureCalculator.ts",
      mappings: ";AAAA;;;;;;;;;GASG;;;AAEH,uEAAmF;AAEnF;;GAEG;AACH,IAAY,sBAMX;AAND,WAAY,sBAAsB;IAChC,6CAAmB,CAAA;IACnB,uDAA6B,CAAA;IAC7B,uDAA6B,CAAA;IAC7B,+DAAqC,CAAA;IACrC,yDAA+B,CAAA;AACjC,CAAC,EANW,sBAAsB,sCAAtB,sBAAsB,QAMjC;AA8BD;;GAEG;AACH,IAAY,eAKX;AALD,WAAY,eAAe;IACzB,gCAAa,CAAA;IACb,kCAAe,CAAA;IACf,wCAAqB,CAAA;IACrB,oCAAiB,CAAA;AACnB,CAAC,EALW,eAAe,+BAAf,eAAe,QAK1B;AAoCD;;;;;GAKG;AACH,MAAa,0BAA0B;IAuBrC;;OAEG;IACI,MAAM,CAAC,yBAAyB,CAAC,KAA4B;QAClE,MAAM,EACJ,QAAQ,EACR,MAAM,GAAG,sBAAsB,CAAC,gBAAgB,EAChD,aAAa,EACb,UAAU,EACV,YAAY,EACZ,oBAAoB,GAAG,KAAK,EAC5B,yBAAyB,GAAG,KAAK,EACjC,eAAe,GAAG,eAAe,CAAC,QAAQ,EAC3C,GAAG,KAAK,CAAC;QAEV,MAAM,QAAQ,GAAa,EAAE,CAAC;QAC9B,MAAM,eAAe,GAAa,EAAE,CAAC;QAErC,kBAAkB;QAClB,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,eAAe,EAAE,QAAQ,CAAC,CAAC;QAEtD,wBAAwB;QACxB,IAAI,gBAAwB,CAAC;QAC7B,IAAI,wBAAgC,CAAC;QAErC,IAAI,UAAU,KAAK,SAAS,EAAE,CAAC;YAC7B,gBAAgB,GAAG,UAAU,CAAC;YAC9B,wBAAwB,GAAG,gBAAgB,CAAC;QAC9C,CAAC;aAAM,IAAI,aAAa,EAAE,CAAC;YACzB,MAAM,QAAQ,GAAG,iDAAuB,CAAC,sBAAsB,CAAC,aAAa,CAAC,CAAC;YAC/E,gBAAgB,GAAG,QAAQ,CAAC,OAAO,CAAC;YACpC,wBAAwB,GAAG,4BAA4B,CAAC;YACxD,QAAQ,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACtC,CAAC;aAAM,CAAC;YACN,gBAAgB,GAAG,IAAI,CAAC,oBAAoB,CAAC;YAC7C,wBAAwB,GAAG,6BAA6B,CAAC;QAC3D,CAAC;QAED,wBAAwB;QACxB,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAC3C,aAAa,EACb,gBAAgB,EAChB,YAAY,EACZ,oBAAoB,EACpB,yBAAyB,CAC1B,CAAC;QAEF,wCAAwC;QACxC,MAAM,oBAAoB,GAAG,IAAI,CAAC,wBAAwB,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAEvF,oBAAoB;QACpB,MAAM,yBAAyB,GAAG,oBAAoB,GAAG,WAAW,CAAC,QAAQ,CAAC;QAE9E,+BAA+B;QAC/B,MAAM,iBAAiB,GAAG,IAAI,CAAC,0BAA0B,CACvD,yBAAyB,EACzB,MAAM,EACN,QAAQ,EACR,WAAW,CACZ,CAAC;QAEF,2BAA2B;QAC3B,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,eAAe,CAAC,CAAC;QAE7E,OAAO;YACL,gBAAgB,EAAE,yBAAyB;YAC3C,MAAM;YACN,QAAQ;YACR,UAAU,EAAE,gBAAgB;YAC5B,YAAY,EAAE,gBAAgB,GAAG,IAAI,CAAC,oBAAoB;YAC1D,WAAW;YACX,QAAQ,EAAE,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;YACtC,iBAAiB;YACjB,QAAQ;YACR,eAAe;YACf,kBAAkB,EAAE;gBAClB,OAAO,EAAE,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC;gBAC3C,kBAAkB,EAAE;oBAClB,oBAAoB;oBACpB,YAAY,EAAE,gBAAgB,GAAG,IAAI,CAAC,oBAAoB;oBAC1D,kBAAkB,EAAE,WAAW,CAAC,QAAQ;iBACzC;gBACD,UAAU,EAAE,wBAAwB;gBACpC,iBAAiB,EAAE,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC;aACrD;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,gBAAgB,CAC5B,QAAgB,EAChB,aAA6B,EAC7B,WAA4C,UAAU;QAEtD,wBAAwB;QACxB,MAAM,YAAY,GAAG,QAAQ,IAAI,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,GAAG;YAClD,QAAQ,IAAI,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,GAAG,CAAC;QACtE,MAAM,UAAU,GAAG,QAAQ,IAAI,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,GAAG;YACnD,QAAQ,IAAI,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,GAAG,CAAC;QAErE,yEAAyE;QACzE,IAAI,QAAQ,KAAK,SAAS,IAAI,UAAU,EAAE,CAAC;YACzC,OAAO,sBAAsB,CAAC,aAAa,CAAC;QAC9C,CAAC;QAED,IAAI,QAAQ,KAAK,MAAM,IAAI,YAAY,EAAE,CAAC;YACxC,OAAO,sBAAsB,CAAC,YAAY,CAAC;QAC7C,CAAC;QAED,IAAI,YAAY,IAAI,CAAC,aAAa,EAAE,CAAC;YACnC,OAAO,sBAAsB,CAAC,YAAY,CAAC;QAC7C,CAAC;QAED,OAAO,sBAAsB,CAAC,gBAAgB,CAAC;IACjD,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,6BAA6B,CACzC,gBAAwB,EACxB,aAA6B,EAC7B,UAAmB;QAEnB,MAAM,QAAQ,GAAa,EAAE,CAAC;QAE9B,wBAAwB;QACxB,IAAI,gBAAwB,CAAC;QAC7B,IAAI,UAAU,KAAK,SAAS,EAAE,CAAC;YAC7B,gBAAgB,GAAG,UAAU,CAAC;QAChC,CAAC;aAAM,IAAI,aAAa,EAAE,CAAC;YACzB,MAAM,QAAQ,GAAG,iDAAuB,CAAC,sBAAsB,CAAC,aAAa,CAAC,CAAC;YAC/E,gBAAgB,GAAG,QAAQ,CAAC,OAAO,CAAC;YACpC,QAAQ,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACtC,CAAC;aAAM,CAAC;YACN,gBAAgB,GAAG,IAAI,CAAC,oBAAoB,CAAC;QAC/C,CAAC;QAED,2CAA2C;QAC3C,MAAM,YAAY,GAAG,gBAAgB,GAAG,IAAI,CAAC,oBAAoB,CAAC;QAClE,MAAM,UAAU,GAAG,gBAAgB,GAAG,YAAY,CAAC;QACnD,MAAM,QAAQ,GAAG,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAEzE,OAAO;YACL,QAAQ;YACR,QAAQ,EAAE,IAAI;YACd,QAAQ;SACT,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,cAAc,CAC3B,KAA4B,EAC5B,eAAgC,EAChC,QAAkB;QAElB,IAAI,eAAe,KAAK,eAAe,CAAC,IAAI;YAAE,OAAO;QAErD,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,sBAAsB,CAAC,gBAAgB,EAAE,GAAG,KAAK,CAAC;QAE7E,mBAAmB;QACnB,IAAI,QAAQ,GAAG,CAAC,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,eAAe,KAAK,eAAe,CAAC,KAAK;YAAE,OAAO;QAEtD,sBAAsB;QACtB,IAAI,QAAQ,GAAG,KAAK,EAAE,CAAC;YACrB,QAAQ,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;QACrE,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QAC3C,IAAI,QAAQ,GAAG,KAAK,CAAC,GAAG,IAAI,QAAQ,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC;YACjD,QAAQ,CAAC,IAAI,CAAC,YAAY,QAAQ,qCAAqC,MAAM,YAAY,KAAK,CAAC,GAAG,IAAI,KAAK,CAAC,GAAG,OAAO,CAAC,CAAC;QAC1H,CAAC;QAED,IAAI,eAAe,KAAK,eAAe,CAAC,MAAM,EAAE,CAAC;YAC/C,oBAAoB;YACpB,IAAI,QAAQ,GAAG,GAAG,EAAE,CAAC;gBACnB,QAAQ,CAAC,IAAI,CAAC,2DAA2D,CAAC,CAAC;YAC7E,CAAC;YAED,IAAI,QAAQ,GAAG,IAAI,EAAE,CAAC;gBACpB,QAAQ,CAAC,IAAI,CAAC,4DAA4D,CAAC,CAAC;YAC9E,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,oBAAoB,CACjC,aAA6B,EAC7B,UAAmB,EACnB,YAA2B,EAC3B,oBAA8B,EAC9B,yBAAmC;QAEnC,MAAM,WAAW,GAAG;YAClB,WAAW,EAAE,GAAG;YAChB,QAAQ,EAAE,GAAG;YACb,QAAQ,EAAE,GAAG;YACb,QAAQ,EAAE,GAAG;YACb,UAAU,EAAE,GAAG;YACf,eAAe,EAAE,GAAG;YACpB,QAAQ,EAAE,GAAG;SACd,CAAC;QAEF,4BAA4B;QAC5B,IAAI,UAAU,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAAC;YAC5D,WAAW,CAAC,WAAW,GAAG,YAAY,CAAC;YACvC,WAAW,CAAC,QAAQ,GAAG,YAAY,CAAC;YACpC,WAAW,CAAC,QAAQ,GAAG,YAAY,CAAC;YACpC,WAAW,CAAC,QAAQ,GAAG,YAAY,CAAC;QACtC,CAAC;QAED,qCAAqC;QACrC,IAAI,oBAAoB,IAAI,YAAY,EAAE,CAAC;YACzC,IAAI,YAAY,CAAC,KAAK,KAAK,aAAa,IAAI,YAAY,CAAC,WAAW,IAAI,YAAY,CAAC,WAAW,GAAG,CAAC,EAAE,CAAC;gBACrG,WAAW,CAAC,UAAU,GAAG,IAAI,CAAC,CAAC,oCAAoC;YACrE,CAAC;QACH,CAAC;QAED,0CAA0C;QAC1C,IAAI,yBAAyB,IAAI,aAAa,EAAE,CAAC;YAC/C,wEAAwE;YACxE,WAAW,CAAC,eAAe,GAAG,GAAG,CAAC;QACpC,CAAC;QAED,gCAAgC;QAChC,WAAW,CAAC,QAAQ,GAAG,WAAW,CAAC,WAAW,GAAG,WAAW,CAAC,QAAQ;YAC/C,WAAW,CAAC,QAAQ,GAAG,WAAW,CAAC,QAAQ;YAC3C,WAAW,CAAC,UAAU,GAAG,WAAW,CAAC,eAAe,CAAC;QAE3E,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,wBAAwB,CACrC,MAA8B,EAC9B,QAAgB,EAChB,QAAkB;QAElB,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,sBAAsB,CAAC,OAAO;gBACjC,OAAO,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;YAE3C,KAAK,sBAAsB,CAAC,YAAY;gBACtC,OAAO,IAAI,CAAC,sBAAsB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAEzD,KAAK,sBAAsB,CAAC,YAAY;gBACtC,OAAO,IAAI,CAAC,wBAAwB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAE3D,KAAK,sBAAsB,CAAC,gBAAgB;gBAC1C,OAAO,IAAI,CAAC,0BAA0B,CAAC,QAAQ,CAAC,CAAC;YAEnD,KAAK,sBAAsB,CAAC,aAAa;gBACvC,OAAO,IAAI,CAAC,wBAAwB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAE3D;gBACE,MAAM,IAAI,KAAK,CAAC,mCAAmC,MAAM,EAAE,CAAC,CAAC;QACjE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,kBAAkB,CAAC,QAAgB;QAChD,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,0BAA0B,EAAE,CAAC,CAAC,CAAC;IACjE,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,sBAAsB,CAAC,QAAgB,EAAE,QAAkB;QACxE,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,iDAAuB,CAAC,yBAAyB,CAAC;gBACjE,QAAQ;gBACR,QAAQ,EAAE,IAAI;aACf,CAAC,CAAC;YACH,QAAQ,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACpC,OAAO,QAAQ,CAAC,gBAAgB,CAAC;QACnC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,QAAQ,CAAC,IAAI,CAAC,0DAA0D,CAAC,CAAC;YAC1E,OAAO,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;QAC3C,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,wBAAwB,CAAC,QAAgB,EAAE,QAAkB;QAC1E,qDAAqD;QACrD,OAAO,IAAI,CAAC,sBAAsB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,0BAA0B,CAAC,QAAgB;QACxD,qEAAqE;QACrE,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,0BAA0B,EAAE,CAAC,CAAC,CAAC;QAEvE,wDAAwD;QACxD,MAAM,kBAAkB,GAAG,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,QAAQ,CAAC,CAAC,wBAAwB;QAErF,OAAO,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,kBAAkB,CAAC,CAAC,CAAC;IACrE,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,wBAAwB,CAAC,QAAgB,EAAE,QAAkB;QAC1E,gDAAgD;QAChD,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;QAEjD,oDAAoD;QACpD,IAAI,aAAa,GAAG,GAAG,CAAC;QAExB,IAAI,QAAQ,GAAG,IAAI,EAAE,CAAC;YACpB,aAAa,GAAG,IAAI,CAAC,CAAC,4CAA4C;QACpE,CAAC;aAAM,IAAI,QAAQ,GAAG,IAAI,EAAE,CAAC;YAC3B,aAAa,GAAG,IAAI,CAAC,CAAC,4CAA4C;QACpE,CAAC;QAED,QAAQ,CAAC,IAAI,CAAC,2EAA2E,CAAC,CAAC;QAE3F,OAAO,MAAM,GAAG,aAAa,CAAC;IAChC,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,0BAA0B,CACvC,gBAAwB,EACxB,MAA8B,EAC9B,QAAgB,EAChB,WAAkD;QAElD,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QAElD,0DAA0D;QAC1D,IAAI,gBAAgB,GAAG,YAAY,CAAC;QAEpC,IAAI,QAAQ,GAAG,GAAG,IAAI,QAAQ,GAAG,IAAI,EAAE,CAAC;YACtC,gBAAgB,IAAI,IAAI,CAAC,CAAC,yCAAyC;QACrE,CAAC;QAED,IAAI,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,QAAQ,GAAG,GAAG,CAAC,GAAG,GAAG,EAAE,CAAC;YAC/C,gBAAgB,IAAI,IAAI,CAAC,CAAC,0CAA0C;QACtE,CAAC;QAED,MAAM,WAAW,GAAG,gBAAgB,GAAG,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC;QAE9D,OAAO;YACL,KAAK,EAAE,gBAAgB,GAAG,WAAW;YACrC,KAAK,EAAE,gBAAgB,GAAG,WAAW;YACrC,eAAe,EAAE,gBAAgB;SAClC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,uBAAuB,CACpC,QAAgB,EAChB,MAA8B,EAC9B,WAAkD,EAClD,eAAyB;QAEzB,iCAAiC;QACjC,IAAI,QAAQ,GAAG,GAAG,EAAE,CAAC;YACnB,eAAe,CAAC,IAAI,CAAC,yEAAyE,CAAC,CAAC;QAClG,CAAC;aAAM,IAAI,QAAQ,GAAG,IAAI,EAAE,CAAC;YAC3B,eAAe,CAAC,IAAI,CAAC,kEAAkE,CAAC,CAAC;QAC3F,CAAC;QAED,+BAA+B;QAC/B,IAAI,MAAM,KAAK,sBAAsB,CAAC,OAAO,IAAI,QAAQ,IAAI,GAAG,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;YACrF,eAAe,CAAC,IAAI,CAAC,iFAAiF,CAAC,CAAC;QAC1G,CAAC;QAED,mCAAmC;QACnC,IAAI,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,QAAQ,GAAG,GAAG,CAAC,GAAG,IAAI,EAAE,CAAC;YAChD,eAAe,CAAC,IAAI,CAAC,uEAAuE,CAAC,CAAC;QAChG,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,qBAAqB,CAAC,MAA8B;QACjE,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,sBAAsB,CAAC,OAAO;gBACjC,OAAO,gBAAgB,CAAC;YAC1B,KAAK,sBAAsB,CAAC,YAAY;gBACtC,OAAO,gCAAgC,CAAC;YAC1C,KAAK,sBAAsB,CAAC,YAAY;gBACtC,OAAO,wCAAwC,CAAC;YAClD,KAAK,sBAAsB,CAAC,gBAAgB;gBAC1C,OAAO,oDAAoD,CAAC;YAC9D,KAAK,sBAAsB,CAAC,aAAa;gBACvC,OAAO,6CAA6C,CAAC;YACvD;gBACE,OAAO,gBAAgB,CAAC;QAC5B,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,oBAAoB,CAAC,MAA8B;QAChE,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,sBAAsB,CAAC,OAAO,CAAC;YACpC,KAAK,sBAAsB,CAAC,gBAAgB;gBAC1C,OAAO,iCAAiC,CAAC;YAC3C,KAAK,sBAAsB,CAAC,YAAY,CAAC;YACzC,KAAK,sBAAsB,CAAC,YAAY;gBACtC,OAAO,iCAAiC,CAAC;YAC3C,KAAK,sBAAsB,CAAC,aAAa;gBACvC,OAAO,sCAAsC,CAAC;YAChD;gBACE,OAAO,sBAAsB,CAAC;QAClC,CAAC;IACH,CAAC;;AAvcH,gEAwcC;AAvcyB,kCAAO,GAAG,OAAO,CAAC;AAClB,+CAAoB,GAAG,KAAK,CAAC,CAAC,SAAS;AACvC,qDAA0B,GAAG,IAAI,CAAC,CAAC,qBAAqB;AAEhF,oDAAoD;AAC5B,0CAAe,GAAG;IACxC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE;IAC/B,YAAY,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE;IACrC,YAAY,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE;IACpC,gBAAgB,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE;IACxC,aAAa,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE;CACvC,CAAC;AAEF,2CAA2C;AACnB,0CAAe,GAAG;IACxC,CAAC,sBAAsB,CAAC,OAAO,CAAC,EAAE,IAAI;IACtC,CAAC,sBAAsB,CAAC,YAAY,CAAC,EAAE,IAAI;IAC3C,CAAC,sBAAsB,CAAC,YAAY,CAAC,EAAE,IAAI;IAC3C,CAAC,sBAAsB,CAAC,gBAAgB,CAAC,EAAE,IAAI;IAC/C,CAAC,sBAAsB,CAAC,aAAa,CAAC,EAAE,IAAI;CAC7C,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\VelocityPressureCalculator.ts"],
      sourcesContent: ["/**\r\n * Velocity Pressure Calculator\r\n * \r\n * Comprehensive velocity pressure calculation service for Phase 3: Advanced Calculation Modules\r\n * Provides multiple calculation methods, environmental corrections, and performance optimization\r\n * for HVAC duct system velocity pressure calculations.\r\n * \r\n * @version 3.0.0\r\n * <AUTHOR> Suite Development Team\r\n */\r\n\r\nimport { AirPropertiesCalculator, AirConditions } from './AirPropertiesCalculator';\r\n\r\n/**\r\n * Velocity pressure calculation method options\r\n */\r\nexport enum VelocityPressureMethod {\r\n  FORMULA = 'formula',\r\n  LOOKUP_TABLE = 'lookup_table',\r\n  INTERPOLATED = 'interpolated',\r\n  ENHANCED_FORMULA = 'enhanced_formula',\r\n  CFD_CORRECTED = 'cfd_corrected'\r\n}\r\n\r\n/**\r\n * Velocity pressure calculation input parameters\r\n */\r\nexport interface VelocityPressureInput {\r\n  velocity: number;                    // FPM\r\n  method?: VelocityPressureMethod;     // Calculation method\r\n  airConditions?: AirConditions;       // Environmental conditions\r\n  airDensity?: number;                 // lb/ft\xB3 (overrides calculated density)\r\n  ductGeometry?: DuctGeometry;         // Duct geometry for advanced corrections\r\n  turbulenceCorrection?: boolean;      // Apply turbulence corrections\r\n  compressibilityCorrection?: boolean; // Apply compressibility corrections\r\n  validationLevel?: ValidationLevel;   // Input validation strictness\r\n}\r\n\r\n/**\r\n * Duct geometry for advanced velocity pressure calculations\r\n */\r\nexport interface DuctGeometry {\r\n  shape: 'round' | 'rectangular' | 'oval';\r\n  diameter?: number;                   // inches (for round ducts)\r\n  width?: number;                      // inches (for rectangular ducts)\r\n  height?: number;                     // inches (for rectangular ducts)\r\n  majorAxis?: number;                  // inches (for oval ducts)\r\n  minorAxis?: number;                  // inches (for oval ducts)\r\n  hydraulicDiameter?: number;          // inches (calculated if not provided)\r\n  aspectRatio?: number;                // width/height (calculated if not provided)\r\n}\r\n\r\n/**\r\n * Validation level for input checking\r\n */\r\nexport enum ValidationLevel {\r\n  NONE = 'none',\r\n  BASIC = 'basic',\r\n  STANDARD = 'standard',\r\n  STRICT = 'strict'\r\n}\r\n\r\n/**\r\n * Velocity pressure calculation result\r\n */\r\nexport interface VelocityPressureResult {\r\n  velocityPressure: number;            // inches w.g.\r\n  method: VelocityPressureMethod;      // Method used\r\n  velocity: number;                    // FPM (input velocity)\r\n  airDensity: number;                  // lb/ft\xB3 (actual density used)\r\n  densityRatio: number;                // Ratio to standard density\r\n  corrections: {\r\n    temperature: number;               // Temperature correction factor\r\n    pressure: number;                  // Pressure correction factor\r\n    altitude: number;                  // Altitude correction factor\r\n    humidity: number;                  // Humidity correction factor\r\n    turbulence: number;                // Turbulence correction factor\r\n    compressibility: number;           // Compressibility correction factor\r\n    combined: number;                  // Combined correction factor\r\n  };\r\n  accuracy: number;                    // Estimated accuracy (0-1)\r\n  uncertaintyBounds?: {\r\n    lower: number;                     // Lower bound (inches w.g.)\r\n    upper: number;                     // Upper bound (inches w.g.)\r\n    confidenceLevel: number;           // Confidence level (0-1)\r\n  };\r\n  warnings: string[];\r\n  recommendations: string[];\r\n  calculationDetails: {\r\n    formula: string;                   // Formula used\r\n    intermediateValues: Record<string, number>;\r\n    dataSource: string;                // Source of calculation data\r\n    standardReference: string;         // Reference standard\r\n  };\r\n}\r\n\r\n/**\r\n * Velocity Pressure Calculator\r\n * \r\n * Comprehensive velocity pressure calculation service providing multiple calculation\r\n * methods, environmental corrections, and advanced features for HVAC applications.\r\n */\r\nexport class VelocityPressureCalculator {\r\n  private static readonly VERSION = '3.0.0';\r\n  private static readonly STANDARD_AIR_DENSITY = 0.075; // lb/ft\xB3\r\n  private static readonly STANDARD_VELOCITY_CONSTANT = 4005; // For VP = (V/4005)\xB2\r\n  \r\n  // Velocity ranges for different calculation methods\r\n  private static readonly VELOCITY_RANGES = {\r\n    FORMULA: { min: 0, max: 10000 },\r\n    LOOKUP_TABLE: { min: 100, max: 5000 },\r\n    INTERPOLATED: { min: 50, max: 6000 },\r\n    ENHANCED_FORMULA: { min: 0, max: 15000 },\r\n    CFD_CORRECTED: { min: 500, max: 8000 }\r\n  };\r\n\r\n  // Accuracy estimates for different methods\r\n  private static readonly METHOD_ACCURACY = {\r\n    [VelocityPressureMethod.FORMULA]: 0.95,\r\n    [VelocityPressureMethod.LOOKUP_TABLE]: 0.98,\r\n    [VelocityPressureMethod.INTERPOLATED]: 0.97,\r\n    [VelocityPressureMethod.ENHANCED_FORMULA]: 0.96,\r\n    [VelocityPressureMethod.CFD_CORRECTED]: 0.99\r\n  };\r\n\r\n  /**\r\n   * Calculate velocity pressure using specified method and conditions\r\n   */\r\n  public static calculateVelocityPressure(input: VelocityPressureInput): VelocityPressureResult {\r\n    const {\r\n      velocity,\r\n      method = VelocityPressureMethod.ENHANCED_FORMULA,\r\n      airConditions,\r\n      airDensity,\r\n      ductGeometry,\r\n      turbulenceCorrection = false,\r\n      compressibilityCorrection = false,\r\n      validationLevel = ValidationLevel.STANDARD\r\n    } = input;\r\n\r\n    const warnings: string[] = [];\r\n    const recommendations: string[] = [];\r\n\r\n    // Validate inputs\r\n    this.validateInputs(input, validationLevel, warnings);\r\n\r\n    // Determine air density\r\n    let actualAirDensity: number;\r\n    let densityCalculationMethod: string;\r\n    \r\n    if (airDensity !== undefined) {\r\n      actualAirDensity = airDensity;\r\n      densityCalculationMethod = 'User specified';\r\n    } else if (airConditions) {\r\n      const airProps = AirPropertiesCalculator.calculateAirProperties(airConditions);\r\n      actualAirDensity = airProps.density;\r\n      densityCalculationMethod = 'Calculated from conditions';\r\n      warnings.push(...airProps.warnings);\r\n    } else {\r\n      actualAirDensity = this.STANDARD_AIR_DENSITY;\r\n      densityCalculationMethod = 'Standard conditions assumed';\r\n    }\r\n\r\n    // Calculate corrections\r\n    const corrections = this.calculateCorrections(\r\n      airConditions,\r\n      actualAirDensity,\r\n      ductGeometry,\r\n      turbulenceCorrection,\r\n      compressibilityCorrection\r\n    );\r\n\r\n    // Select and execute calculation method\r\n    const baseVelocityPressure = this.executeCalculationMethod(method, velocity, warnings);\r\n    \r\n    // Apply corrections\r\n    const correctedVelocityPressure = baseVelocityPressure * corrections.combined;\r\n\r\n    // Calculate uncertainty bounds\r\n    const uncertaintyBounds = this.calculateUncertaintyBounds(\r\n      correctedVelocityPressure,\r\n      method,\r\n      velocity,\r\n      corrections\r\n    );\r\n\r\n    // Generate recommendations\r\n    this.generateRecommendations(velocity, method, corrections, recommendations);\r\n\r\n    return {\r\n      velocityPressure: correctedVelocityPressure,\r\n      method,\r\n      velocity,\r\n      airDensity: actualAirDensity,\r\n      densityRatio: actualAirDensity / this.STANDARD_AIR_DENSITY,\r\n      corrections,\r\n      accuracy: this.METHOD_ACCURACY[method],\r\n      uncertaintyBounds,\r\n      warnings,\r\n      recommendations,\r\n      calculationDetails: {\r\n        formula: this.getFormulaDescription(method),\r\n        intermediateValues: {\r\n          baseVelocityPressure,\r\n          densityRatio: actualAirDensity / this.STANDARD_AIR_DENSITY,\r\n          combinedCorrection: corrections.combined\r\n        },\r\n        dataSource: densityCalculationMethod,\r\n        standardReference: this.getStandardReference(method)\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get optimal calculation method for given conditions\r\n   */\r\n  public static getOptimalMethod(\r\n    velocity: number,\r\n    airConditions?: AirConditions,\r\n    accuracy: 'standard' | 'high' | 'maximum' = 'standard'\r\n  ): VelocityPressureMethod {\r\n    // Check velocity ranges\r\n    const inTableRange = velocity >= this.VELOCITY_RANGES.LOOKUP_TABLE.min && \r\n                        velocity <= this.VELOCITY_RANGES.LOOKUP_TABLE.max;\r\n    const inCFDRange = velocity >= this.VELOCITY_RANGES.CFD_CORRECTED.min && \r\n                      velocity <= this.VELOCITY_RANGES.CFD_CORRECTED.max;\r\n\r\n    // Determine optimal method based on accuracy requirements and conditions\r\n    if (accuracy === 'maximum' && inCFDRange) {\r\n      return VelocityPressureMethod.CFD_CORRECTED;\r\n    }\r\n    \r\n    if (accuracy === 'high' && inTableRange) {\r\n      return VelocityPressureMethod.LOOKUP_TABLE;\r\n    }\r\n    \r\n    if (inTableRange && !airConditions) {\r\n      return VelocityPressureMethod.INTERPOLATED;\r\n    }\r\n    \r\n    return VelocityPressureMethod.ENHANCED_FORMULA;\r\n  }\r\n\r\n  /**\r\n   * Calculate velocity from velocity pressure (inverse calculation)\r\n   */\r\n  public static calculateVelocityFromPressure(\r\n    velocityPressure: number,\r\n    airConditions?: AirConditions,\r\n    airDensity?: number\r\n  ): { velocity: number; accuracy: number; warnings: string[] } {\r\n    const warnings: string[] = [];\r\n    \r\n    // Determine air density\r\n    let actualAirDensity: number;\r\n    if (airDensity !== undefined) {\r\n      actualAirDensity = airDensity;\r\n    } else if (airConditions) {\r\n      const airProps = AirPropertiesCalculator.calculateAirProperties(airConditions);\r\n      actualAirDensity = airProps.density;\r\n      warnings.push(...airProps.warnings);\r\n    } else {\r\n      actualAirDensity = this.STANDARD_AIR_DENSITY;\r\n    }\r\n\r\n    // Calculate velocity using inverse formula\r\n    const densityRatio = actualAirDensity / this.STANDARD_AIR_DENSITY;\r\n    const adjustedVP = velocityPressure / densityRatio;\r\n    const velocity = this.STANDARD_VELOCITY_CONSTANT * Math.sqrt(adjustedVP);\r\n\r\n    return {\r\n      velocity,\r\n      accuracy: 0.95,\r\n      warnings\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Validate input parameters\r\n   */\r\n  private static validateInputs(\r\n    input: VelocityPressureInput,\r\n    validationLevel: ValidationLevel,\r\n    warnings: string[]\r\n  ): void {\r\n    if (validationLevel === ValidationLevel.NONE) return;\r\n\r\n    const { velocity, method = VelocityPressureMethod.ENHANCED_FORMULA } = input;\r\n\r\n    // Basic validation\r\n    if (velocity < 0) {\r\n      throw new Error('Velocity cannot be negative');\r\n    }\r\n\r\n    if (validationLevel === ValidationLevel.BASIC) return;\r\n\r\n    // Standard validation\r\n    if (velocity > 10000) {\r\n      warnings.push('Velocity exceeds typical HVAC range (>10,000 FPM)');\r\n    }\r\n\r\n    const range = this.VELOCITY_RANGES[method];\r\n    if (velocity < range.min || velocity > range.max) {\r\n      warnings.push(`Velocity ${velocity} FPM is outside optimal range for ${method} method (${range.min}-${range.max} FPM)`);\r\n    }\r\n\r\n    if (validationLevel === ValidationLevel.STRICT) {\r\n      // Strict validation\r\n      if (velocity < 100) {\r\n        warnings.push('Very low velocity may indicate measurement or input error');\r\n      }\r\n      \r\n      if (velocity > 6000) {\r\n        warnings.push('High velocity may cause noise and energy efficiency issues');\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Calculate environmental and geometric corrections\r\n   */\r\n  private static calculateCorrections(\r\n    airConditions?: AirConditions,\r\n    airDensity?: number,\r\n    ductGeometry?: DuctGeometry,\r\n    turbulenceCorrection?: boolean,\r\n    compressibilityCorrection?: boolean\r\n  ): VelocityPressureResult['corrections'] {\r\n    const corrections = {\r\n      temperature: 1.0,\r\n      pressure: 1.0,\r\n      altitude: 1.0,\r\n      humidity: 1.0,\r\n      turbulence: 1.0,\r\n      compressibility: 1.0,\r\n      combined: 1.0\r\n    };\r\n\r\n    // Density-based corrections\r\n    if (airDensity) {\r\n      const densityRatio = airDensity / this.STANDARD_AIR_DENSITY;\r\n      corrections.temperature = densityRatio;\r\n      corrections.pressure = densityRatio;\r\n      corrections.altitude = densityRatio;\r\n      corrections.humidity = densityRatio;\r\n    }\r\n\r\n    // Turbulence correction (simplified)\r\n    if (turbulenceCorrection && ductGeometry) {\r\n      if (ductGeometry.shape === 'rectangular' && ductGeometry.aspectRatio && ductGeometry.aspectRatio > 3) {\r\n        corrections.turbulence = 1.05; // 5% increase for high aspect ratio\r\n      }\r\n    }\r\n\r\n    // Compressibility correction (simplified)\r\n    if (compressibilityCorrection && airConditions) {\r\n      // Negligible for typical HVAC velocities, but included for completeness\r\n      corrections.compressibility = 1.0;\r\n    }\r\n\r\n    // Calculate combined correction\r\n    corrections.combined = corrections.temperature * corrections.pressure * \r\n                          corrections.altitude * corrections.humidity * \r\n                          corrections.turbulence * corrections.compressibility;\r\n\r\n    return corrections;\r\n  }\r\n\r\n  /**\r\n   * Execute the specified calculation method\r\n   */\r\n  private static executeCalculationMethod(\r\n    method: VelocityPressureMethod,\r\n    velocity: number,\r\n    warnings: string[]\r\n  ): number {\r\n    switch (method) {\r\n      case VelocityPressureMethod.FORMULA:\r\n        return this.calculateByFormula(velocity);\r\n        \r\n      case VelocityPressureMethod.LOOKUP_TABLE:\r\n        return this.calculateByLookupTable(velocity, warnings);\r\n        \r\n      case VelocityPressureMethod.INTERPOLATED:\r\n        return this.calculateByInterpolation(velocity, warnings);\r\n        \r\n      case VelocityPressureMethod.ENHANCED_FORMULA:\r\n        return this.calculateByEnhancedFormula(velocity);\r\n        \r\n      case VelocityPressureMethod.CFD_CORRECTED:\r\n        return this.calculateByCFDCorrection(velocity, warnings);\r\n        \r\n      default:\r\n        throw new Error(`Unsupported calculation method: ${method}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Calculate velocity pressure using standard formula\r\n   */\r\n  private static calculateByFormula(velocity: number): number {\r\n    return Math.pow(velocity / this.STANDARD_VELOCITY_CONSTANT, 2);\r\n  }\r\n\r\n  /**\r\n   * Calculate velocity pressure using lookup table\r\n   */\r\n  private static calculateByLookupTable(velocity: number, warnings: string[]): number {\r\n    try {\r\n      const vpResult = AirPropertiesCalculator.calculateVelocityPressure({\r\n        velocity,\r\n        useTable: true\r\n      });\r\n      warnings.push(...vpResult.warnings);\r\n      return vpResult.velocityPressure;\r\n    } catch (error) {\r\n      warnings.push('Lookup table unavailable, falling back to formula method');\r\n      return this.calculateByFormula(velocity);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Calculate velocity pressure using interpolation\r\n   */\r\n  private static calculateByInterpolation(velocity: number, warnings: string[]): number {\r\n    // Use AirPropertiesCalculator's interpolation method\r\n    return this.calculateByLookupTable(velocity, warnings);\r\n  }\r\n\r\n  /**\r\n   * Calculate velocity pressure using enhanced formula with corrections\r\n   */\r\n  private static calculateByEnhancedFormula(velocity: number): number {\r\n    // Enhanced formula with slight corrections for real-world conditions\r\n    const baseVP = Math.pow(velocity / this.STANDARD_VELOCITY_CONSTANT, 2);\r\n    \r\n    // Apply minor correction for velocity-dependent effects\r\n    const velocityCorrection = 1 + (velocity - 2000) * 0.000001; // Very small correction\r\n    \r\n    return baseVP * Math.max(0.98, Math.min(1.02, velocityCorrection));\r\n  }\r\n\r\n  /**\r\n   * Calculate velocity pressure using CFD-derived corrections\r\n   */\r\n  private static calculateByCFDCorrection(velocity: number, warnings: string[]): number {\r\n    // CFD-derived corrections for improved accuracy\r\n    const baseVP = this.calculateByFormula(velocity);\r\n    \r\n    // Apply CFD-derived correction factors (simplified)\r\n    let cfdCorrection = 1.0;\r\n    \r\n    if (velocity < 1000) {\r\n      cfdCorrection = 0.98; // Slight under-prediction at low velocities\r\n    } else if (velocity > 4000) {\r\n      cfdCorrection = 1.02; // Slight over-prediction at high velocities\r\n    }\r\n    \r\n    warnings.push('CFD corrections applied - results may vary with actual duct configuration');\r\n    \r\n    return baseVP * cfdCorrection;\r\n  }\r\n\r\n  /**\r\n   * Calculate uncertainty bounds for the result\r\n   */\r\n  private static calculateUncertaintyBounds(\r\n    velocityPressure: number,\r\n    method: VelocityPressureMethod,\r\n    velocity: number,\r\n    corrections: VelocityPressureResult['corrections']\r\n  ): VelocityPressureResult['uncertaintyBounds'] {\r\n    const baseAccuracy = this.METHOD_ACCURACY[method];\r\n    \r\n    // Adjust accuracy based on velocity range and corrections\r\n    let adjustedAccuracy = baseAccuracy;\r\n    \r\n    if (velocity < 500 || velocity > 5000) {\r\n      adjustedAccuracy *= 0.95; // Reduced accuracy outside optimal range\r\n    }\r\n    \r\n    if (Math.abs(corrections.combined - 1.0) > 0.1) {\r\n      adjustedAccuracy *= 0.98; // Reduced accuracy with large corrections\r\n    }\r\n    \r\n    const uncertainty = velocityPressure * (1 - adjustedAccuracy);\r\n    \r\n    return {\r\n      lower: velocityPressure - uncertainty,\r\n      upper: velocityPressure + uncertainty,\r\n      confidenceLevel: adjustedAccuracy\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Generate recommendations based on calculation results\r\n   */\r\n  private static generateRecommendations(\r\n    velocity: number,\r\n    method: VelocityPressureMethod,\r\n    corrections: VelocityPressureResult['corrections'],\r\n    recommendations: string[]\r\n  ): void {\r\n    // Velocity-based recommendations\r\n    if (velocity < 500) {\r\n      recommendations.push('Consider increasing velocity to improve accuracy and system performance');\r\n    } else if (velocity > 4000) {\r\n      recommendations.push('High velocity may cause noise issues - consider larger duct size');\r\n    }\r\n    \r\n    // Method-based recommendations\r\n    if (method === VelocityPressureMethod.FORMULA && velocity >= 100 && velocity <= 5000) {\r\n      recommendations.push('Consider using lookup table method for improved accuracy in this velocity range');\r\n    }\r\n    \r\n    // Correction-based recommendations\r\n    if (Math.abs(corrections.combined - 1.0) > 0.05) {\r\n      recommendations.push('Significant environmental corrections applied - verify air conditions');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get formula description for the method\r\n   */\r\n  private static getFormulaDescription(method: VelocityPressureMethod): string {\r\n    switch (method) {\r\n      case VelocityPressureMethod.FORMULA:\r\n        return 'VP = (V/4005)\xB2';\r\n      case VelocityPressureMethod.LOOKUP_TABLE:\r\n        return 'Table lookup with exact values';\r\n      case VelocityPressureMethod.INTERPOLATED:\r\n        return 'Table lookup with linear interpolation';\r\n      case VelocityPressureMethod.ENHANCED_FORMULA:\r\n        return 'VP = (V/4005)\xB2 with velocity-dependent corrections';\r\n      case VelocityPressureMethod.CFD_CORRECTED:\r\n        return 'VP = (V/4005)\xB2 with CFD-derived corrections';\r\n      default:\r\n        return 'Unknown method';\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get standard reference for the method\r\n   */\r\n  private static getStandardReference(method: VelocityPressureMethod): string {\r\n    switch (method) {\r\n      case VelocityPressureMethod.FORMULA:\r\n      case VelocityPressureMethod.ENHANCED_FORMULA:\r\n        return 'ASHRAE Fundamentals, Chapter 21';\r\n      case VelocityPressureMethod.LOOKUP_TABLE:\r\n      case VelocityPressureMethod.INTERPOLATED:\r\n        return 'ASHRAE Fundamentals, Table 21-1';\r\n      case VelocityPressureMethod.CFD_CORRECTED:\r\n        return 'CFD Analysis and ASHRAE Fundamentals';\r\n      default:\r\n        return 'Internal calculation';\r\n    }\r\n  }\r\n}\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "b7a2371f33da1de30b715fab97b5ec7d5610278d"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_l6ts6nd3r = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_l6ts6nd3r();
cov_l6ts6nd3r().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_l6ts6nd3r().s[1]++;
exports.VelocityPressureCalculator = exports.ValidationLevel = exports.VelocityPressureMethod = void 0;
const AirPropertiesCalculator_1 =
/* istanbul ignore next */
(cov_l6ts6nd3r().s[2]++, require("./AirPropertiesCalculator"));
/**
 * Velocity pressure calculation method options
 */
var VelocityPressureMethod;
/* istanbul ignore next */
cov_l6ts6nd3r().s[3]++;
(function (VelocityPressureMethod) {
  /* istanbul ignore next */
  cov_l6ts6nd3r().f[0]++;
  cov_l6ts6nd3r().s[4]++;
  VelocityPressureMethod["FORMULA"] = "formula";
  /* istanbul ignore next */
  cov_l6ts6nd3r().s[5]++;
  VelocityPressureMethod["LOOKUP_TABLE"] = "lookup_table";
  /* istanbul ignore next */
  cov_l6ts6nd3r().s[6]++;
  VelocityPressureMethod["INTERPOLATED"] = "interpolated";
  /* istanbul ignore next */
  cov_l6ts6nd3r().s[7]++;
  VelocityPressureMethod["ENHANCED_FORMULA"] = "enhanced_formula";
  /* istanbul ignore next */
  cov_l6ts6nd3r().s[8]++;
  VelocityPressureMethod["CFD_CORRECTED"] = "cfd_corrected";
})(
/* istanbul ignore next */
(cov_l6ts6nd3r().b[0][0]++, VelocityPressureMethod) ||
/* istanbul ignore next */
(cov_l6ts6nd3r().b[0][1]++, exports.VelocityPressureMethod = VelocityPressureMethod = {}));
/**
 * Validation level for input checking
 */
var ValidationLevel;
/* istanbul ignore next */
cov_l6ts6nd3r().s[9]++;
(function (ValidationLevel) {
  /* istanbul ignore next */
  cov_l6ts6nd3r().f[1]++;
  cov_l6ts6nd3r().s[10]++;
  ValidationLevel["NONE"] = "none";
  /* istanbul ignore next */
  cov_l6ts6nd3r().s[11]++;
  ValidationLevel["BASIC"] = "basic";
  /* istanbul ignore next */
  cov_l6ts6nd3r().s[12]++;
  ValidationLevel["STANDARD"] = "standard";
  /* istanbul ignore next */
  cov_l6ts6nd3r().s[13]++;
  ValidationLevel["STRICT"] = "strict";
})(
/* istanbul ignore next */
(cov_l6ts6nd3r().b[1][0]++, ValidationLevel) ||
/* istanbul ignore next */
(cov_l6ts6nd3r().b[1][1]++, exports.ValidationLevel = ValidationLevel = {}));
/**
 * Velocity Pressure Calculator
 *
 * Comprehensive velocity pressure calculation service providing multiple calculation
 * methods, environmental corrections, and advanced features for HVAC applications.
 */
class VelocityPressureCalculator {
  /**
   * Calculate velocity pressure using specified method and conditions
   */
  static calculateVelocityPressure(input) {
    /* istanbul ignore next */
    cov_l6ts6nd3r().f[2]++;
    const {
      velocity,
      method =
      /* istanbul ignore next */
      (cov_l6ts6nd3r().b[2][0]++, VelocityPressureMethod.ENHANCED_FORMULA),
      airConditions,
      airDensity,
      ductGeometry,
      turbulenceCorrection =
      /* istanbul ignore next */
      (cov_l6ts6nd3r().b[3][0]++, false),
      compressibilityCorrection =
      /* istanbul ignore next */
      (cov_l6ts6nd3r().b[4][0]++, false),
      validationLevel =
      /* istanbul ignore next */
      (cov_l6ts6nd3r().b[5][0]++, ValidationLevel.STANDARD)
    } =
    /* istanbul ignore next */
    (cov_l6ts6nd3r().s[14]++, input);
    const warnings =
    /* istanbul ignore next */
    (cov_l6ts6nd3r().s[15]++, []);
    const recommendations =
    /* istanbul ignore next */
    (cov_l6ts6nd3r().s[16]++, []);
    // Validate inputs
    /* istanbul ignore next */
    cov_l6ts6nd3r().s[17]++;
    this.validateInputs(input, validationLevel, warnings);
    // Determine air density
    let actualAirDensity;
    let densityCalculationMethod;
    /* istanbul ignore next */
    cov_l6ts6nd3r().s[18]++;
    if (airDensity !== undefined) {
      /* istanbul ignore next */
      cov_l6ts6nd3r().b[6][0]++;
      cov_l6ts6nd3r().s[19]++;
      actualAirDensity = airDensity;
      /* istanbul ignore next */
      cov_l6ts6nd3r().s[20]++;
      densityCalculationMethod = 'User specified';
    } else {
      /* istanbul ignore next */
      cov_l6ts6nd3r().b[6][1]++;
      cov_l6ts6nd3r().s[21]++;
      if (airConditions) {
        /* istanbul ignore next */
        cov_l6ts6nd3r().b[7][0]++;
        const airProps =
        /* istanbul ignore next */
        (cov_l6ts6nd3r().s[22]++, AirPropertiesCalculator_1.AirPropertiesCalculator.calculateAirProperties(airConditions));
        /* istanbul ignore next */
        cov_l6ts6nd3r().s[23]++;
        actualAirDensity = airProps.density;
        /* istanbul ignore next */
        cov_l6ts6nd3r().s[24]++;
        densityCalculationMethod = 'Calculated from conditions';
        /* istanbul ignore next */
        cov_l6ts6nd3r().s[25]++;
        warnings.push(...airProps.warnings);
      } else {
        /* istanbul ignore next */
        cov_l6ts6nd3r().b[7][1]++;
        cov_l6ts6nd3r().s[26]++;
        actualAirDensity = this.STANDARD_AIR_DENSITY;
        /* istanbul ignore next */
        cov_l6ts6nd3r().s[27]++;
        densityCalculationMethod = 'Standard conditions assumed';
      }
    }
    // Calculate corrections
    const corrections =
    /* istanbul ignore next */
    (cov_l6ts6nd3r().s[28]++, this.calculateCorrections(airConditions, actualAirDensity, ductGeometry, turbulenceCorrection, compressibilityCorrection));
    // Select and execute calculation method
    const baseVelocityPressure =
    /* istanbul ignore next */
    (cov_l6ts6nd3r().s[29]++, this.executeCalculationMethod(method, velocity, warnings));
    // Apply corrections
    const correctedVelocityPressure =
    /* istanbul ignore next */
    (cov_l6ts6nd3r().s[30]++, baseVelocityPressure * corrections.combined);
    // Calculate uncertainty bounds
    const uncertaintyBounds =
    /* istanbul ignore next */
    (cov_l6ts6nd3r().s[31]++, this.calculateUncertaintyBounds(correctedVelocityPressure, method, velocity, corrections));
    // Generate recommendations
    /* istanbul ignore next */
    cov_l6ts6nd3r().s[32]++;
    this.generateRecommendations(velocity, method, corrections, recommendations);
    /* istanbul ignore next */
    cov_l6ts6nd3r().s[33]++;
    return {
      velocityPressure: correctedVelocityPressure,
      method,
      velocity,
      airDensity: actualAirDensity,
      densityRatio: actualAirDensity / this.STANDARD_AIR_DENSITY,
      corrections,
      accuracy: this.METHOD_ACCURACY[method],
      uncertaintyBounds,
      warnings,
      recommendations,
      calculationDetails: {
        formula: this.getFormulaDescription(method),
        intermediateValues: {
          baseVelocityPressure,
          densityRatio: actualAirDensity / this.STANDARD_AIR_DENSITY,
          combinedCorrection: corrections.combined
        },
        dataSource: densityCalculationMethod,
        standardReference: this.getStandardReference(method)
      }
    };
  }
  /**
   * Get optimal calculation method for given conditions
   */
  static getOptimalMethod(velocity, airConditions, accuracy =
  /* istanbul ignore next */
  (cov_l6ts6nd3r().b[8][0]++, 'standard')) {
    /* istanbul ignore next */
    cov_l6ts6nd3r().f[3]++;
    // Check velocity ranges
    const inTableRange =
    /* istanbul ignore next */
    (cov_l6ts6nd3r().s[34]++,
    /* istanbul ignore next */
    (cov_l6ts6nd3r().b[9][0]++, velocity >= this.VELOCITY_RANGES.LOOKUP_TABLE.min) &&
    /* istanbul ignore next */
    (cov_l6ts6nd3r().b[9][1]++, velocity <= this.VELOCITY_RANGES.LOOKUP_TABLE.max));
    const inCFDRange =
    /* istanbul ignore next */
    (cov_l6ts6nd3r().s[35]++,
    /* istanbul ignore next */
    (cov_l6ts6nd3r().b[10][0]++, velocity >= this.VELOCITY_RANGES.CFD_CORRECTED.min) &&
    /* istanbul ignore next */
    (cov_l6ts6nd3r().b[10][1]++, velocity <= this.VELOCITY_RANGES.CFD_CORRECTED.max));
    // Determine optimal method based on accuracy requirements and conditions
    /* istanbul ignore next */
    cov_l6ts6nd3r().s[36]++;
    if (
    /* istanbul ignore next */
    (cov_l6ts6nd3r().b[12][0]++, accuracy === 'maximum') &&
    /* istanbul ignore next */
    (cov_l6ts6nd3r().b[12][1]++, inCFDRange)) {
      /* istanbul ignore next */
      cov_l6ts6nd3r().b[11][0]++;
      cov_l6ts6nd3r().s[37]++;
      return VelocityPressureMethod.CFD_CORRECTED;
    } else
    /* istanbul ignore next */
    {
      cov_l6ts6nd3r().b[11][1]++;
    }
    cov_l6ts6nd3r().s[38]++;
    if (
    /* istanbul ignore next */
    (cov_l6ts6nd3r().b[14][0]++, accuracy === 'high') &&
    /* istanbul ignore next */
    (cov_l6ts6nd3r().b[14][1]++, inTableRange)) {
      /* istanbul ignore next */
      cov_l6ts6nd3r().b[13][0]++;
      cov_l6ts6nd3r().s[39]++;
      return VelocityPressureMethod.LOOKUP_TABLE;
    } else
    /* istanbul ignore next */
    {
      cov_l6ts6nd3r().b[13][1]++;
    }
    cov_l6ts6nd3r().s[40]++;
    if (
    /* istanbul ignore next */
    (cov_l6ts6nd3r().b[16][0]++, inTableRange) &&
    /* istanbul ignore next */
    (cov_l6ts6nd3r().b[16][1]++, !airConditions)) {
      /* istanbul ignore next */
      cov_l6ts6nd3r().b[15][0]++;
      cov_l6ts6nd3r().s[41]++;
      return VelocityPressureMethod.INTERPOLATED;
    } else
    /* istanbul ignore next */
    {
      cov_l6ts6nd3r().b[15][1]++;
    }
    cov_l6ts6nd3r().s[42]++;
    return VelocityPressureMethod.ENHANCED_FORMULA;
  }
  /**
   * Calculate velocity from velocity pressure (inverse calculation)
   */
  static calculateVelocityFromPressure(velocityPressure, airConditions, airDensity) {
    /* istanbul ignore next */
    cov_l6ts6nd3r().f[4]++;
    const warnings =
    /* istanbul ignore next */
    (cov_l6ts6nd3r().s[43]++, []);
    // Determine air density
    let actualAirDensity;
    /* istanbul ignore next */
    cov_l6ts6nd3r().s[44]++;
    if (airDensity !== undefined) {
      /* istanbul ignore next */
      cov_l6ts6nd3r().b[17][0]++;
      cov_l6ts6nd3r().s[45]++;
      actualAirDensity = airDensity;
    } else {
      /* istanbul ignore next */
      cov_l6ts6nd3r().b[17][1]++;
      cov_l6ts6nd3r().s[46]++;
      if (airConditions) {
        /* istanbul ignore next */
        cov_l6ts6nd3r().b[18][0]++;
        const airProps =
        /* istanbul ignore next */
        (cov_l6ts6nd3r().s[47]++, AirPropertiesCalculator_1.AirPropertiesCalculator.calculateAirProperties(airConditions));
        /* istanbul ignore next */
        cov_l6ts6nd3r().s[48]++;
        actualAirDensity = airProps.density;
        /* istanbul ignore next */
        cov_l6ts6nd3r().s[49]++;
        warnings.push(...airProps.warnings);
      } else {
        /* istanbul ignore next */
        cov_l6ts6nd3r().b[18][1]++;
        cov_l6ts6nd3r().s[50]++;
        actualAirDensity = this.STANDARD_AIR_DENSITY;
      }
    }
    // Calculate velocity using inverse formula
    const densityRatio =
    /* istanbul ignore next */
    (cov_l6ts6nd3r().s[51]++, actualAirDensity / this.STANDARD_AIR_DENSITY);
    const adjustedVP =
    /* istanbul ignore next */
    (cov_l6ts6nd3r().s[52]++, velocityPressure / densityRatio);
    const velocity =
    /* istanbul ignore next */
    (cov_l6ts6nd3r().s[53]++, this.STANDARD_VELOCITY_CONSTANT * Math.sqrt(adjustedVP));
    /* istanbul ignore next */
    cov_l6ts6nd3r().s[54]++;
    return {
      velocity,
      accuracy: 0.95,
      warnings
    };
  }
  /**
   * Validate input parameters
   */
  static validateInputs(input, validationLevel, warnings) {
    /* istanbul ignore next */
    cov_l6ts6nd3r().f[5]++;
    cov_l6ts6nd3r().s[55]++;
    if (validationLevel === ValidationLevel.NONE) {
      /* istanbul ignore next */
      cov_l6ts6nd3r().b[19][0]++;
      cov_l6ts6nd3r().s[56]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_l6ts6nd3r().b[19][1]++;
    }
    const {
      velocity,
      method =
      /* istanbul ignore next */
      (cov_l6ts6nd3r().b[20][0]++, VelocityPressureMethod.ENHANCED_FORMULA)
    } =
    /* istanbul ignore next */
    (cov_l6ts6nd3r().s[57]++, input);
    // Basic validation
    /* istanbul ignore next */
    cov_l6ts6nd3r().s[58]++;
    if (velocity < 0) {
      /* istanbul ignore next */
      cov_l6ts6nd3r().b[21][0]++;
      cov_l6ts6nd3r().s[59]++;
      throw new Error('Velocity cannot be negative');
    } else
    /* istanbul ignore next */
    {
      cov_l6ts6nd3r().b[21][1]++;
    }
    cov_l6ts6nd3r().s[60]++;
    if (validationLevel === ValidationLevel.BASIC) {
      /* istanbul ignore next */
      cov_l6ts6nd3r().b[22][0]++;
      cov_l6ts6nd3r().s[61]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_l6ts6nd3r().b[22][1]++;
    }
    // Standard validation
    cov_l6ts6nd3r().s[62]++;
    if (velocity > 10000) {
      /* istanbul ignore next */
      cov_l6ts6nd3r().b[23][0]++;
      cov_l6ts6nd3r().s[63]++;
      warnings.push('Velocity exceeds typical HVAC range (>10,000 FPM)');
    } else
    /* istanbul ignore next */
    {
      cov_l6ts6nd3r().b[23][1]++;
    }
    const range =
    /* istanbul ignore next */
    (cov_l6ts6nd3r().s[64]++, this.VELOCITY_RANGES[method]);
    /* istanbul ignore next */
    cov_l6ts6nd3r().s[65]++;
    if (
    /* istanbul ignore next */
    (cov_l6ts6nd3r().b[25][0]++, velocity < range.min) ||
    /* istanbul ignore next */
    (cov_l6ts6nd3r().b[25][1]++, velocity > range.max)) {
      /* istanbul ignore next */
      cov_l6ts6nd3r().b[24][0]++;
      cov_l6ts6nd3r().s[66]++;
      warnings.push(`Velocity ${velocity} FPM is outside optimal range for ${method} method (${range.min}-${range.max} FPM)`);
    } else
    /* istanbul ignore next */
    {
      cov_l6ts6nd3r().b[24][1]++;
    }
    cov_l6ts6nd3r().s[67]++;
    if (validationLevel === ValidationLevel.STRICT) {
      /* istanbul ignore next */
      cov_l6ts6nd3r().b[26][0]++;
      cov_l6ts6nd3r().s[68]++;
      // Strict validation
      if (velocity < 100) {
        /* istanbul ignore next */
        cov_l6ts6nd3r().b[27][0]++;
        cov_l6ts6nd3r().s[69]++;
        warnings.push('Very low velocity may indicate measurement or input error');
      } else
      /* istanbul ignore next */
      {
        cov_l6ts6nd3r().b[27][1]++;
      }
      cov_l6ts6nd3r().s[70]++;
      if (velocity > 6000) {
        /* istanbul ignore next */
        cov_l6ts6nd3r().b[28][0]++;
        cov_l6ts6nd3r().s[71]++;
        warnings.push('High velocity may cause noise and energy efficiency issues');
      } else
      /* istanbul ignore next */
      {
        cov_l6ts6nd3r().b[28][1]++;
      }
    } else
    /* istanbul ignore next */
    {
      cov_l6ts6nd3r().b[26][1]++;
    }
  }
  /**
   * Calculate environmental and geometric corrections
   */
  static calculateCorrections(airConditions, airDensity, ductGeometry, turbulenceCorrection, compressibilityCorrection) {
    /* istanbul ignore next */
    cov_l6ts6nd3r().f[6]++;
    const corrections =
    /* istanbul ignore next */
    (cov_l6ts6nd3r().s[72]++, {
      temperature: 1.0,
      pressure: 1.0,
      altitude: 1.0,
      humidity: 1.0,
      turbulence: 1.0,
      compressibility: 1.0,
      combined: 1.0
    });
    // Density-based corrections
    /* istanbul ignore next */
    cov_l6ts6nd3r().s[73]++;
    if (airDensity) {
      /* istanbul ignore next */
      cov_l6ts6nd3r().b[29][0]++;
      const densityRatio =
      /* istanbul ignore next */
      (cov_l6ts6nd3r().s[74]++, airDensity / this.STANDARD_AIR_DENSITY);
      /* istanbul ignore next */
      cov_l6ts6nd3r().s[75]++;
      corrections.temperature = densityRatio;
      /* istanbul ignore next */
      cov_l6ts6nd3r().s[76]++;
      corrections.pressure = densityRatio;
      /* istanbul ignore next */
      cov_l6ts6nd3r().s[77]++;
      corrections.altitude = densityRatio;
      /* istanbul ignore next */
      cov_l6ts6nd3r().s[78]++;
      corrections.humidity = densityRatio;
    } else
    /* istanbul ignore next */
    {
      cov_l6ts6nd3r().b[29][1]++;
    }
    // Turbulence correction (simplified)
    cov_l6ts6nd3r().s[79]++;
    if (
    /* istanbul ignore next */
    (cov_l6ts6nd3r().b[31][0]++, turbulenceCorrection) &&
    /* istanbul ignore next */
    (cov_l6ts6nd3r().b[31][1]++, ductGeometry)) {
      /* istanbul ignore next */
      cov_l6ts6nd3r().b[30][0]++;
      cov_l6ts6nd3r().s[80]++;
      if (
      /* istanbul ignore next */
      (cov_l6ts6nd3r().b[33][0]++, ductGeometry.shape === 'rectangular') &&
      /* istanbul ignore next */
      (cov_l6ts6nd3r().b[33][1]++, ductGeometry.aspectRatio) &&
      /* istanbul ignore next */
      (cov_l6ts6nd3r().b[33][2]++, ductGeometry.aspectRatio > 3)) {
        /* istanbul ignore next */
        cov_l6ts6nd3r().b[32][0]++;
        cov_l6ts6nd3r().s[81]++;
        corrections.turbulence = 1.05; // 5% increase for high aspect ratio
      } else
      /* istanbul ignore next */
      {
        cov_l6ts6nd3r().b[32][1]++;
      }
    } else
    /* istanbul ignore next */
    {
      cov_l6ts6nd3r().b[30][1]++;
    }
    // Compressibility correction (simplified)
    cov_l6ts6nd3r().s[82]++;
    if (
    /* istanbul ignore next */
    (cov_l6ts6nd3r().b[35][0]++, compressibilityCorrection) &&
    /* istanbul ignore next */
    (cov_l6ts6nd3r().b[35][1]++, airConditions)) {
      /* istanbul ignore next */
      cov_l6ts6nd3r().b[34][0]++;
      cov_l6ts6nd3r().s[83]++;
      // Negligible for typical HVAC velocities, but included for completeness
      corrections.compressibility = 1.0;
    } else
    /* istanbul ignore next */
    {
      cov_l6ts6nd3r().b[34][1]++;
    }
    // Calculate combined correction
    cov_l6ts6nd3r().s[84]++;
    corrections.combined = corrections.temperature * corrections.pressure * corrections.altitude * corrections.humidity * corrections.turbulence * corrections.compressibility;
    /* istanbul ignore next */
    cov_l6ts6nd3r().s[85]++;
    return corrections;
  }
  /**
   * Execute the specified calculation method
   */
  static executeCalculationMethod(method, velocity, warnings) {
    /* istanbul ignore next */
    cov_l6ts6nd3r().f[7]++;
    cov_l6ts6nd3r().s[86]++;
    switch (method) {
      case VelocityPressureMethod.FORMULA:
        /* istanbul ignore next */
        cov_l6ts6nd3r().b[36][0]++;
        cov_l6ts6nd3r().s[87]++;
        return this.calculateByFormula(velocity);
      case VelocityPressureMethod.LOOKUP_TABLE:
        /* istanbul ignore next */
        cov_l6ts6nd3r().b[36][1]++;
        cov_l6ts6nd3r().s[88]++;
        return this.calculateByLookupTable(velocity, warnings);
      case VelocityPressureMethod.INTERPOLATED:
        /* istanbul ignore next */
        cov_l6ts6nd3r().b[36][2]++;
        cov_l6ts6nd3r().s[89]++;
        return this.calculateByInterpolation(velocity, warnings);
      case VelocityPressureMethod.ENHANCED_FORMULA:
        /* istanbul ignore next */
        cov_l6ts6nd3r().b[36][3]++;
        cov_l6ts6nd3r().s[90]++;
        return this.calculateByEnhancedFormula(velocity);
      case VelocityPressureMethod.CFD_CORRECTED:
        /* istanbul ignore next */
        cov_l6ts6nd3r().b[36][4]++;
        cov_l6ts6nd3r().s[91]++;
        return this.calculateByCFDCorrection(velocity, warnings);
      default:
        /* istanbul ignore next */
        cov_l6ts6nd3r().b[36][5]++;
        cov_l6ts6nd3r().s[92]++;
        throw new Error(`Unsupported calculation method: ${method}`);
    }
  }
  /**
   * Calculate velocity pressure using standard formula
   */
  static calculateByFormula(velocity) {
    /* istanbul ignore next */
    cov_l6ts6nd3r().f[8]++;
    cov_l6ts6nd3r().s[93]++;
    return Math.pow(velocity / this.STANDARD_VELOCITY_CONSTANT, 2);
  }
  /**
   * Calculate velocity pressure using lookup table
   */
  static calculateByLookupTable(velocity, warnings) {
    /* istanbul ignore next */
    cov_l6ts6nd3r().f[9]++;
    cov_l6ts6nd3r().s[94]++;
    try {
      const vpResult =
      /* istanbul ignore next */
      (cov_l6ts6nd3r().s[95]++, AirPropertiesCalculator_1.AirPropertiesCalculator.calculateVelocityPressure({
        velocity,
        useTable: true
      }));
      /* istanbul ignore next */
      cov_l6ts6nd3r().s[96]++;
      warnings.push(...vpResult.warnings);
      /* istanbul ignore next */
      cov_l6ts6nd3r().s[97]++;
      return vpResult.velocityPressure;
    } catch (error) {
      /* istanbul ignore next */
      cov_l6ts6nd3r().s[98]++;
      warnings.push('Lookup table unavailable, falling back to formula method');
      /* istanbul ignore next */
      cov_l6ts6nd3r().s[99]++;
      return this.calculateByFormula(velocity);
    }
  }
  /**
   * Calculate velocity pressure using interpolation
   */
  static calculateByInterpolation(velocity, warnings) {
    /* istanbul ignore next */
    cov_l6ts6nd3r().f[10]++;
    cov_l6ts6nd3r().s[100]++;
    // Use AirPropertiesCalculator's interpolation method
    return this.calculateByLookupTable(velocity, warnings);
  }
  /**
   * Calculate velocity pressure using enhanced formula with corrections
   */
  static calculateByEnhancedFormula(velocity) {
    /* istanbul ignore next */
    cov_l6ts6nd3r().f[11]++;
    // Enhanced formula with slight corrections for real-world conditions
    const baseVP =
    /* istanbul ignore next */
    (cov_l6ts6nd3r().s[101]++, Math.pow(velocity / this.STANDARD_VELOCITY_CONSTANT, 2));
    // Apply minor correction for velocity-dependent effects
    const velocityCorrection =
    /* istanbul ignore next */
    (cov_l6ts6nd3r().s[102]++, 1 + (velocity - 2000) * 0.000001); // Very small correction
    /* istanbul ignore next */
    cov_l6ts6nd3r().s[103]++;
    return baseVP * Math.max(0.98, Math.min(1.02, velocityCorrection));
  }
  /**
   * Calculate velocity pressure using CFD-derived corrections
   */
  static calculateByCFDCorrection(velocity, warnings) {
    /* istanbul ignore next */
    cov_l6ts6nd3r().f[12]++;
    // CFD-derived corrections for improved accuracy
    const baseVP =
    /* istanbul ignore next */
    (cov_l6ts6nd3r().s[104]++, this.calculateByFormula(velocity));
    // Apply CFD-derived correction factors (simplified)
    let cfdCorrection =
    /* istanbul ignore next */
    (cov_l6ts6nd3r().s[105]++, 1.0);
    /* istanbul ignore next */
    cov_l6ts6nd3r().s[106]++;
    if (velocity < 1000) {
      /* istanbul ignore next */
      cov_l6ts6nd3r().b[37][0]++;
      cov_l6ts6nd3r().s[107]++;
      cfdCorrection = 0.98; // Slight under-prediction at low velocities
    } else {
      /* istanbul ignore next */
      cov_l6ts6nd3r().b[37][1]++;
      cov_l6ts6nd3r().s[108]++;
      if (velocity > 4000) {
        /* istanbul ignore next */
        cov_l6ts6nd3r().b[38][0]++;
        cov_l6ts6nd3r().s[109]++;
        cfdCorrection = 1.02; // Slight over-prediction at high velocities
      } else
      /* istanbul ignore next */
      {
        cov_l6ts6nd3r().b[38][1]++;
      }
    }
    /* istanbul ignore next */
    cov_l6ts6nd3r().s[110]++;
    warnings.push('CFD corrections applied - results may vary with actual duct configuration');
    /* istanbul ignore next */
    cov_l6ts6nd3r().s[111]++;
    return baseVP * cfdCorrection;
  }
  /**
   * Calculate uncertainty bounds for the result
   */
  static calculateUncertaintyBounds(velocityPressure, method, velocity, corrections) {
    /* istanbul ignore next */
    cov_l6ts6nd3r().f[13]++;
    const baseAccuracy =
    /* istanbul ignore next */
    (cov_l6ts6nd3r().s[112]++, this.METHOD_ACCURACY[method]);
    // Adjust accuracy based on velocity range and corrections
    let adjustedAccuracy =
    /* istanbul ignore next */
    (cov_l6ts6nd3r().s[113]++, baseAccuracy);
    /* istanbul ignore next */
    cov_l6ts6nd3r().s[114]++;
    if (
    /* istanbul ignore next */
    (cov_l6ts6nd3r().b[40][0]++, velocity < 500) ||
    /* istanbul ignore next */
    (cov_l6ts6nd3r().b[40][1]++, velocity > 5000)) {
      /* istanbul ignore next */
      cov_l6ts6nd3r().b[39][0]++;
      cov_l6ts6nd3r().s[115]++;
      adjustedAccuracy *= 0.95; // Reduced accuracy outside optimal range
    } else
    /* istanbul ignore next */
    {
      cov_l6ts6nd3r().b[39][1]++;
    }
    cov_l6ts6nd3r().s[116]++;
    if (Math.abs(corrections.combined - 1.0) > 0.1) {
      /* istanbul ignore next */
      cov_l6ts6nd3r().b[41][0]++;
      cov_l6ts6nd3r().s[117]++;
      adjustedAccuracy *= 0.98; // Reduced accuracy with large corrections
    } else
    /* istanbul ignore next */
    {
      cov_l6ts6nd3r().b[41][1]++;
    }
    const uncertainty =
    /* istanbul ignore next */
    (cov_l6ts6nd3r().s[118]++, velocityPressure * (1 - adjustedAccuracy));
    /* istanbul ignore next */
    cov_l6ts6nd3r().s[119]++;
    return {
      lower: velocityPressure - uncertainty,
      upper: velocityPressure + uncertainty,
      confidenceLevel: adjustedAccuracy
    };
  }
  /**
   * Generate recommendations based on calculation results
   */
  static generateRecommendations(velocity, method, corrections, recommendations) {
    /* istanbul ignore next */
    cov_l6ts6nd3r().f[14]++;
    cov_l6ts6nd3r().s[120]++;
    // Velocity-based recommendations
    if (velocity < 500) {
      /* istanbul ignore next */
      cov_l6ts6nd3r().b[42][0]++;
      cov_l6ts6nd3r().s[121]++;
      recommendations.push('Consider increasing velocity to improve accuracy and system performance');
    } else {
      /* istanbul ignore next */
      cov_l6ts6nd3r().b[42][1]++;
      cov_l6ts6nd3r().s[122]++;
      if (velocity > 4000) {
        /* istanbul ignore next */
        cov_l6ts6nd3r().b[43][0]++;
        cov_l6ts6nd3r().s[123]++;
        recommendations.push('High velocity may cause noise issues - consider larger duct size');
      } else
      /* istanbul ignore next */
      {
        cov_l6ts6nd3r().b[43][1]++;
      }
    }
    // Method-based recommendations
    /* istanbul ignore next */
    cov_l6ts6nd3r().s[124]++;
    if (
    /* istanbul ignore next */
    (cov_l6ts6nd3r().b[45][0]++, method === VelocityPressureMethod.FORMULA) &&
    /* istanbul ignore next */
    (cov_l6ts6nd3r().b[45][1]++, velocity >= 100) &&
    /* istanbul ignore next */
    (cov_l6ts6nd3r().b[45][2]++, velocity <= 5000)) {
      /* istanbul ignore next */
      cov_l6ts6nd3r().b[44][0]++;
      cov_l6ts6nd3r().s[125]++;
      recommendations.push('Consider using lookup table method for improved accuracy in this velocity range');
    } else
    /* istanbul ignore next */
    {
      cov_l6ts6nd3r().b[44][1]++;
    }
    // Correction-based recommendations
    cov_l6ts6nd3r().s[126]++;
    if (Math.abs(corrections.combined - 1.0) > 0.05) {
      /* istanbul ignore next */
      cov_l6ts6nd3r().b[46][0]++;
      cov_l6ts6nd3r().s[127]++;
      recommendations.push('Significant environmental corrections applied - verify air conditions');
    } else
    /* istanbul ignore next */
    {
      cov_l6ts6nd3r().b[46][1]++;
    }
  }
  /**
   * Get formula description for the method
   */
  static getFormulaDescription(method) {
    /* istanbul ignore next */
    cov_l6ts6nd3r().f[15]++;
    cov_l6ts6nd3r().s[128]++;
    switch (method) {
      case VelocityPressureMethod.FORMULA:
        /* istanbul ignore next */
        cov_l6ts6nd3r().b[47][0]++;
        cov_l6ts6nd3r().s[129]++;
        return 'VP = (V/4005)²';
      case VelocityPressureMethod.LOOKUP_TABLE:
        /* istanbul ignore next */
        cov_l6ts6nd3r().b[47][1]++;
        cov_l6ts6nd3r().s[130]++;
        return 'Table lookup with exact values';
      case VelocityPressureMethod.INTERPOLATED:
        /* istanbul ignore next */
        cov_l6ts6nd3r().b[47][2]++;
        cov_l6ts6nd3r().s[131]++;
        return 'Table lookup with linear interpolation';
      case VelocityPressureMethod.ENHANCED_FORMULA:
        /* istanbul ignore next */
        cov_l6ts6nd3r().b[47][3]++;
        cov_l6ts6nd3r().s[132]++;
        return 'VP = (V/4005)² with velocity-dependent corrections';
      case VelocityPressureMethod.CFD_CORRECTED:
        /* istanbul ignore next */
        cov_l6ts6nd3r().b[47][4]++;
        cov_l6ts6nd3r().s[133]++;
        return 'VP = (V/4005)² with CFD-derived corrections';
      default:
        /* istanbul ignore next */
        cov_l6ts6nd3r().b[47][5]++;
        cov_l6ts6nd3r().s[134]++;
        return 'Unknown method';
    }
  }
  /**
   * Get standard reference for the method
   */
  static getStandardReference(method) {
    /* istanbul ignore next */
    cov_l6ts6nd3r().f[16]++;
    cov_l6ts6nd3r().s[135]++;
    switch (method) {
      case VelocityPressureMethod.FORMULA:
        /* istanbul ignore next */
        cov_l6ts6nd3r().b[48][0]++;
      case VelocityPressureMethod.ENHANCED_FORMULA:
        /* istanbul ignore next */
        cov_l6ts6nd3r().b[48][1]++;
        cov_l6ts6nd3r().s[136]++;
        return 'ASHRAE Fundamentals, Chapter 21';
      case VelocityPressureMethod.LOOKUP_TABLE:
        /* istanbul ignore next */
        cov_l6ts6nd3r().b[48][2]++;
      case VelocityPressureMethod.INTERPOLATED:
        /* istanbul ignore next */
        cov_l6ts6nd3r().b[48][3]++;
        cov_l6ts6nd3r().s[137]++;
        return 'ASHRAE Fundamentals, Table 21-1';
      case VelocityPressureMethod.CFD_CORRECTED:
        /* istanbul ignore next */
        cov_l6ts6nd3r().b[48][4]++;
        cov_l6ts6nd3r().s[138]++;
        return 'CFD Analysis and ASHRAE Fundamentals';
      default:
        /* istanbul ignore next */
        cov_l6ts6nd3r().b[48][5]++;
        cov_l6ts6nd3r().s[139]++;
        return 'Internal calculation';
    }
  }
}
/* istanbul ignore next */
cov_l6ts6nd3r().s[140]++;
exports.VelocityPressureCalculator = VelocityPressureCalculator;
/* istanbul ignore next */
cov_l6ts6nd3r().s[141]++;
VelocityPressureCalculator.VERSION = '3.0.0';
/* istanbul ignore next */
cov_l6ts6nd3r().s[142]++;
VelocityPressureCalculator.STANDARD_AIR_DENSITY = 0.075; // lb/ft³
/* istanbul ignore next */
cov_l6ts6nd3r().s[143]++;
VelocityPressureCalculator.STANDARD_VELOCITY_CONSTANT = 4005; // For VP = (V/4005)²
// Velocity ranges for different calculation methods
/* istanbul ignore next */
cov_l6ts6nd3r().s[144]++;
VelocityPressureCalculator.VELOCITY_RANGES = {
  FORMULA: {
    min: 0,
    max: 10000
  },
  LOOKUP_TABLE: {
    min: 100,
    max: 5000
  },
  INTERPOLATED: {
    min: 50,
    max: 6000
  },
  ENHANCED_FORMULA: {
    min: 0,
    max: 15000
  },
  CFD_CORRECTED: {
    min: 500,
    max: 8000
  }
};
// Accuracy estimates for different methods
/* istanbul ignore next */
cov_l6ts6nd3r().s[145]++;
VelocityPressureCalculator.METHOD_ACCURACY = {
  [VelocityPressureMethod.FORMULA]: 0.95,
  [VelocityPressureMethod.LOOKUP_TABLE]: 0.98,
  [VelocityPressureMethod.INTERPOLATED]: 0.97,
  [VelocityPressureMethod.ENHANCED_FORMULA]: 0.96,
  [VelocityPressureMethod.CFD_CORRECTED]: 0.99
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJjb3ZfbDZ0czZuZDNyIiwicGF0aCIsImhhc2giLCJnbG9iYWwiLCJGdW5jdGlvbiIsImdjdiIsImNvdmVyYWdlRGF0YSIsInN0YXRlbWVudE1hcCIsInN0YXJ0IiwibGluZSIsImNvbHVtbiIsImVuZCIsImZuTWFwIiwibmFtZSIsImRlY2wiLCJsb2MiLCJicmFuY2hNYXAiLCJ0eXBlIiwibG9jYXRpb25zIiwidW5kZWZpbmVkIiwicyIsImYiLCJiIiwiaW5wdXRTb3VyY2VNYXAiLCJmaWxlIiwibWFwcGluZ3MiLCJuYW1lcyIsInNvdXJjZXMiLCJzb3VyY2VzQ29udGVudCIsInZlcnNpb24iLCJfY292ZXJhZ2VTY2hlbWEiLCJjb3ZlcmFnZSIsImFjdHVhbENvdmVyYWdlIiwiQWlyUHJvcGVydGllc0NhbGN1bGF0b3JfMSIsInJlcXVpcmUiLCJWZWxvY2l0eVByZXNzdXJlTWV0aG9kIiwiZXhwb3J0cyIsIlZhbGlkYXRpb25MZXZlbCIsIlZlbG9jaXR5UHJlc3N1cmVDYWxjdWxhdG9yIiwiY2FsY3VsYXRlVmVsb2NpdHlQcmVzc3VyZSIsImlucHV0IiwidmVsb2NpdHkiLCJtZXRob2QiLCJFTkhBTkNFRF9GT1JNVUxBIiwiYWlyQ29uZGl0aW9ucyIsImFpckRlbnNpdHkiLCJkdWN0R2VvbWV0cnkiLCJ0dXJidWxlbmNlQ29ycmVjdGlvbiIsImNvbXByZXNzaWJpbGl0eUNvcnJlY3Rpb24iLCJ2YWxpZGF0aW9uTGV2ZWwiLCJTVEFOREFSRCIsIndhcm5pbmdzIiwicmVjb21tZW5kYXRpb25zIiwidmFsaWRhdGVJbnB1dHMiLCJhY3R1YWxBaXJEZW5zaXR5IiwiZGVuc2l0eUNhbGN1bGF0aW9uTWV0aG9kIiwiYWlyUHJvcHMiLCJBaXJQcm9wZXJ0aWVzQ2FsY3VsYXRvciIsImNhbGN1bGF0ZUFpclByb3BlcnRpZXMiLCJkZW5zaXR5IiwicHVzaCIsIlNUQU5EQVJEX0FJUl9ERU5TSVRZIiwiY29ycmVjdGlvbnMiLCJjYWxjdWxhdGVDb3JyZWN0aW9ucyIsImJhc2VWZWxvY2l0eVByZXNzdXJlIiwiZXhlY3V0ZUNhbGN1bGF0aW9uTWV0aG9kIiwiY29ycmVjdGVkVmVsb2NpdHlQcmVzc3VyZSIsImNvbWJpbmVkIiwidW5jZXJ0YWludHlCb3VuZHMiLCJjYWxjdWxhdGVVbmNlcnRhaW50eUJvdW5kcyIsImdlbmVyYXRlUmVjb21tZW5kYXRpb25zIiwidmVsb2NpdHlQcmVzc3VyZSIsImRlbnNpdHlSYXRpbyIsImFjY3VyYWN5IiwiTUVUSE9EX0FDQ1VSQUNZIiwiY2FsY3VsYXRpb25EZXRhaWxzIiwiZm9ybXVsYSIsImdldEZvcm11bGFEZXNjcmlwdGlvbiIsImludGVybWVkaWF0ZVZhbHVlcyIsImNvbWJpbmVkQ29ycmVjdGlvbiIsImRhdGFTb3VyY2UiLCJzdGFuZGFyZFJlZmVyZW5jZSIsImdldFN0YW5kYXJkUmVmZXJlbmNlIiwiZ2V0T3B0aW1hbE1ldGhvZCIsImluVGFibGVSYW5nZSIsIlZFTE9DSVRZX1JBTkdFUyIsIkxPT0tVUF9UQUJMRSIsIm1pbiIsIm1heCIsImluQ0ZEUmFuZ2UiLCJDRkRfQ09SUkVDVEVEIiwiSU5URVJQT0xBVEVEIiwiY2FsY3VsYXRlVmVsb2NpdHlGcm9tUHJlc3N1cmUiLCJhZGp1c3RlZFZQIiwiU1RBTkRBUkRfVkVMT0NJVFlfQ09OU1RBTlQiLCJNYXRoIiwic3FydCIsIk5PTkUiLCJFcnJvciIsIkJBU0lDIiwicmFuZ2UiLCJTVFJJQ1QiLCJ0ZW1wZXJhdHVyZSIsInByZXNzdXJlIiwiYWx0aXR1ZGUiLCJodW1pZGl0eSIsInR1cmJ1bGVuY2UiLCJjb21wcmVzc2liaWxpdHkiLCJzaGFwZSIsImFzcGVjdFJhdGlvIiwiRk9STVVMQSIsImNhbGN1bGF0ZUJ5Rm9ybXVsYSIsImNhbGN1bGF0ZUJ5TG9va3VwVGFibGUiLCJjYWxjdWxhdGVCeUludGVycG9sYXRpb24iLCJjYWxjdWxhdGVCeUVuaGFuY2VkRm9ybXVsYSIsImNhbGN1bGF0ZUJ5Q0ZEQ29ycmVjdGlvbiIsInBvdyIsInZwUmVzdWx0IiwidXNlVGFibGUiLCJlcnJvciIsImJhc2VWUCIsInZlbG9jaXR5Q29ycmVjdGlvbiIsImNmZENvcnJlY3Rpb24iLCJiYXNlQWNjdXJhY3kiLCJhZGp1c3RlZEFjY3VyYWN5IiwiYWJzIiwidW5jZXJ0YWludHkiLCJsb3dlciIsInVwcGVyIiwiY29uZmlkZW5jZUxldmVsIiwiVkVSU0lPTiJdLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcam9obnJcXERvd25sb2Fkc1xcU2l6ZVdpc2VfU3VpdGVfQXBwXFxiYWNrZW5kXFxzZXJ2aWNlc1xcY2FsY3VsYXRpb25zXFxWZWxvY2l0eVByZXNzdXJlQ2FsY3VsYXRvci50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcclxuICogVmVsb2NpdHkgUHJlc3N1cmUgQ2FsY3VsYXRvclxyXG4gKiBcclxuICogQ29tcHJlaGVuc2l2ZSB2ZWxvY2l0eSBwcmVzc3VyZSBjYWxjdWxhdGlvbiBzZXJ2aWNlIGZvciBQaGFzZSAzOiBBZHZhbmNlZCBDYWxjdWxhdGlvbiBNb2R1bGVzXHJcbiAqIFByb3ZpZGVzIG11bHRpcGxlIGNhbGN1bGF0aW9uIG1ldGhvZHMsIGVudmlyb25tZW50YWwgY29ycmVjdGlvbnMsIGFuZCBwZXJmb3JtYW5jZSBvcHRpbWl6YXRpb25cclxuICogZm9yIEhWQUMgZHVjdCBzeXN0ZW0gdmVsb2NpdHkgcHJlc3N1cmUgY2FsY3VsYXRpb25zLlxyXG4gKiBcclxuICogQHZlcnNpb24gMy4wLjBcclxuICogQGF1dGhvciBTaXplV2lzZSBTdWl0ZSBEZXZlbG9wbWVudCBUZWFtXHJcbiAqL1xyXG5cclxuaW1wb3J0IHsgQWlyUHJvcGVydGllc0NhbGN1bGF0b3IsIEFpckNvbmRpdGlvbnMgfSBmcm9tICcuL0FpclByb3BlcnRpZXNDYWxjdWxhdG9yJztcclxuXHJcbi8qKlxyXG4gKiBWZWxvY2l0eSBwcmVzc3VyZSBjYWxjdWxhdGlvbiBtZXRob2Qgb3B0aW9uc1xyXG4gKi9cclxuZXhwb3J0IGVudW0gVmVsb2NpdHlQcmVzc3VyZU1ldGhvZCB7XHJcbiAgRk9STVVMQSA9ICdmb3JtdWxhJyxcclxuICBMT09LVVBfVEFCTEUgPSAnbG9va3VwX3RhYmxlJyxcclxuICBJTlRFUlBPTEFURUQgPSAnaW50ZXJwb2xhdGVkJyxcclxuICBFTkhBTkNFRF9GT1JNVUxBID0gJ2VuaGFuY2VkX2Zvcm11bGEnLFxyXG4gIENGRF9DT1JSRUNURUQgPSAnY2ZkX2NvcnJlY3RlZCdcclxufVxyXG5cclxuLyoqXHJcbiAqIFZlbG9jaXR5IHByZXNzdXJlIGNhbGN1bGF0aW9uIGlucHV0IHBhcmFtZXRlcnNcclxuICovXHJcbmV4cG9ydCBpbnRlcmZhY2UgVmVsb2NpdHlQcmVzc3VyZUlucHV0IHtcclxuICB2ZWxvY2l0eTogbnVtYmVyOyAgICAgICAgICAgICAgICAgICAgLy8gRlBNXHJcbiAgbWV0aG9kPzogVmVsb2NpdHlQcmVzc3VyZU1ldGhvZDsgICAgIC8vIENhbGN1bGF0aW9uIG1ldGhvZFxyXG4gIGFpckNvbmRpdGlvbnM/OiBBaXJDb25kaXRpb25zOyAgICAgICAvLyBFbnZpcm9ubWVudGFsIGNvbmRpdGlvbnNcclxuICBhaXJEZW5zaXR5PzogbnVtYmVyOyAgICAgICAgICAgICAgICAgLy8gbGIvZnTCsyAob3ZlcnJpZGVzIGNhbGN1bGF0ZWQgZGVuc2l0eSlcclxuICBkdWN0R2VvbWV0cnk/OiBEdWN0R2VvbWV0cnk7ICAgICAgICAgLy8gRHVjdCBnZW9tZXRyeSBmb3IgYWR2YW5jZWQgY29ycmVjdGlvbnNcclxuICB0dXJidWxlbmNlQ29ycmVjdGlvbj86IGJvb2xlYW47ICAgICAgLy8gQXBwbHkgdHVyYnVsZW5jZSBjb3JyZWN0aW9uc1xyXG4gIGNvbXByZXNzaWJpbGl0eUNvcnJlY3Rpb24/OiBib29sZWFuOyAvLyBBcHBseSBjb21wcmVzc2liaWxpdHkgY29ycmVjdGlvbnNcclxuICB2YWxpZGF0aW9uTGV2ZWw/OiBWYWxpZGF0aW9uTGV2ZWw7ICAgLy8gSW5wdXQgdmFsaWRhdGlvbiBzdHJpY3RuZXNzXHJcbn1cclxuXHJcbi8qKlxyXG4gKiBEdWN0IGdlb21ldHJ5IGZvciBhZHZhbmNlZCB2ZWxvY2l0eSBwcmVzc3VyZSBjYWxjdWxhdGlvbnNcclxuICovXHJcbmV4cG9ydCBpbnRlcmZhY2UgRHVjdEdlb21ldHJ5IHtcclxuICBzaGFwZTogJ3JvdW5kJyB8ICdyZWN0YW5ndWxhcicgfCAnb3ZhbCc7XHJcbiAgZGlhbWV0ZXI/OiBudW1iZXI7ICAgICAgICAgICAgICAgICAgIC8vIGluY2hlcyAoZm9yIHJvdW5kIGR1Y3RzKVxyXG4gIHdpZHRoPzogbnVtYmVyOyAgICAgICAgICAgICAgICAgICAgICAvLyBpbmNoZXMgKGZvciByZWN0YW5ndWxhciBkdWN0cylcclxuICBoZWlnaHQ/OiBudW1iZXI7ICAgICAgICAgICAgICAgICAgICAgLy8gaW5jaGVzIChmb3IgcmVjdGFuZ3VsYXIgZHVjdHMpXHJcbiAgbWFqb3JBeGlzPzogbnVtYmVyOyAgICAgICAgICAgICAgICAgIC8vIGluY2hlcyAoZm9yIG92YWwgZHVjdHMpXHJcbiAgbWlub3JBeGlzPzogbnVtYmVyOyAgICAgICAgICAgICAgICAgIC8vIGluY2hlcyAoZm9yIG92YWwgZHVjdHMpXHJcbiAgaHlkcmF1bGljRGlhbWV0ZXI/OiBudW1iZXI7ICAgICAgICAgIC8vIGluY2hlcyAoY2FsY3VsYXRlZCBpZiBub3QgcHJvdmlkZWQpXHJcbiAgYXNwZWN0UmF0aW8/OiBudW1iZXI7ICAgICAgICAgICAgICAgIC8vIHdpZHRoL2hlaWdodCAoY2FsY3VsYXRlZCBpZiBub3QgcHJvdmlkZWQpXHJcbn1cclxuXHJcbi8qKlxyXG4gKiBWYWxpZGF0aW9uIGxldmVsIGZvciBpbnB1dCBjaGVja2luZ1xyXG4gKi9cclxuZXhwb3J0IGVudW0gVmFsaWRhdGlvbkxldmVsIHtcclxuICBOT05FID0gJ25vbmUnLFxyXG4gIEJBU0lDID0gJ2Jhc2ljJyxcclxuICBTVEFOREFSRCA9ICdzdGFuZGFyZCcsXHJcbiAgU1RSSUNUID0gJ3N0cmljdCdcclxufVxyXG5cclxuLyoqXHJcbiAqIFZlbG9jaXR5IHByZXNzdXJlIGNhbGN1bGF0aW9uIHJlc3VsdFxyXG4gKi9cclxuZXhwb3J0IGludGVyZmFjZSBWZWxvY2l0eVByZXNzdXJlUmVzdWx0IHtcclxuICB2ZWxvY2l0eVByZXNzdXJlOiBudW1iZXI7ICAgICAgICAgICAgLy8gaW5jaGVzIHcuZy5cclxuICBtZXRob2Q6IFZlbG9jaXR5UHJlc3N1cmVNZXRob2Q7ICAgICAgLy8gTWV0aG9kIHVzZWRcclxuICB2ZWxvY2l0eTogbnVtYmVyOyAgICAgICAgICAgICAgICAgICAgLy8gRlBNIChpbnB1dCB2ZWxvY2l0eSlcclxuICBhaXJEZW5zaXR5OiBudW1iZXI7ICAgICAgICAgICAgICAgICAgLy8gbGIvZnTCsyAoYWN0dWFsIGRlbnNpdHkgdXNlZClcclxuICBkZW5zaXR5UmF0aW86IG51bWJlcjsgICAgICAgICAgICAgICAgLy8gUmF0aW8gdG8gc3RhbmRhcmQgZGVuc2l0eVxyXG4gIGNvcnJlY3Rpb25zOiB7XHJcbiAgICB0ZW1wZXJhdHVyZTogbnVtYmVyOyAgICAgICAgICAgICAgIC8vIFRlbXBlcmF0dXJlIGNvcnJlY3Rpb24gZmFjdG9yXHJcbiAgICBwcmVzc3VyZTogbnVtYmVyOyAgICAgICAgICAgICAgICAgIC8vIFByZXNzdXJlIGNvcnJlY3Rpb24gZmFjdG9yXHJcbiAgICBhbHRpdHVkZTogbnVtYmVyOyAgICAgICAgICAgICAgICAgIC8vIEFsdGl0dWRlIGNvcnJlY3Rpb24gZmFjdG9yXHJcbiAgICBodW1pZGl0eTogbnVtYmVyOyAgICAgICAgICAgICAgICAgIC8vIEh1bWlkaXR5IGNvcnJlY3Rpb24gZmFjdG9yXHJcbiAgICB0dXJidWxlbmNlOiBudW1iZXI7ICAgICAgICAgICAgICAgIC8vIFR1cmJ1bGVuY2UgY29ycmVjdGlvbiBmYWN0b3JcclxuICAgIGNvbXByZXNzaWJpbGl0eTogbnVtYmVyOyAgICAgICAgICAgLy8gQ29tcHJlc3NpYmlsaXR5IGNvcnJlY3Rpb24gZmFjdG9yXHJcbiAgICBjb21iaW5lZDogbnVtYmVyOyAgICAgICAgICAgICAgICAgIC8vIENvbWJpbmVkIGNvcnJlY3Rpb24gZmFjdG9yXHJcbiAgfTtcclxuICBhY2N1cmFjeTogbnVtYmVyOyAgICAgICAgICAgICAgICAgICAgLy8gRXN0aW1hdGVkIGFjY3VyYWN5ICgwLTEpXHJcbiAgdW5jZXJ0YWludHlCb3VuZHM/OiB7XHJcbiAgICBsb3dlcjogbnVtYmVyOyAgICAgICAgICAgICAgICAgICAgIC8vIExvd2VyIGJvdW5kIChpbmNoZXMgdy5nLilcclxuICAgIHVwcGVyOiBudW1iZXI7ICAgICAgICAgICAgICAgICAgICAgLy8gVXBwZXIgYm91bmQgKGluY2hlcyB3LmcuKVxyXG4gICAgY29uZmlkZW5jZUxldmVsOiBudW1iZXI7ICAgICAgICAgICAvLyBDb25maWRlbmNlIGxldmVsICgwLTEpXHJcbiAgfTtcclxuICB3YXJuaW5nczogc3RyaW5nW107XHJcbiAgcmVjb21tZW5kYXRpb25zOiBzdHJpbmdbXTtcclxuICBjYWxjdWxhdGlvbkRldGFpbHM6IHtcclxuICAgIGZvcm11bGE6IHN0cmluZzsgICAgICAgICAgICAgICAgICAgLy8gRm9ybXVsYSB1c2VkXHJcbiAgICBpbnRlcm1lZGlhdGVWYWx1ZXM6IFJlY29yZDxzdHJpbmcsIG51bWJlcj47XHJcbiAgICBkYXRhU291cmNlOiBzdHJpbmc7ICAgICAgICAgICAgICAgIC8vIFNvdXJjZSBvZiBjYWxjdWxhdGlvbiBkYXRhXHJcbiAgICBzdGFuZGFyZFJlZmVyZW5jZTogc3RyaW5nOyAgICAgICAgIC8vIFJlZmVyZW5jZSBzdGFuZGFyZFxyXG4gIH07XHJcbn1cclxuXHJcbi8qKlxyXG4gKiBWZWxvY2l0eSBQcmVzc3VyZSBDYWxjdWxhdG9yXHJcbiAqIFxyXG4gKiBDb21wcmVoZW5zaXZlIHZlbG9jaXR5IHByZXNzdXJlIGNhbGN1bGF0aW9uIHNlcnZpY2UgcHJvdmlkaW5nIG11bHRpcGxlIGNhbGN1bGF0aW9uXHJcbiAqIG1ldGhvZHMsIGVudmlyb25tZW50YWwgY29ycmVjdGlvbnMsIGFuZCBhZHZhbmNlZCBmZWF0dXJlcyBmb3IgSFZBQyBhcHBsaWNhdGlvbnMuXHJcbiAqL1xyXG5leHBvcnQgY2xhc3MgVmVsb2NpdHlQcmVzc3VyZUNhbGN1bGF0b3Ige1xyXG4gIHByaXZhdGUgc3RhdGljIHJlYWRvbmx5IFZFUlNJT04gPSAnMy4wLjAnO1xyXG4gIHByaXZhdGUgc3RhdGljIHJlYWRvbmx5IFNUQU5EQVJEX0FJUl9ERU5TSVRZID0gMC4wNzU7IC8vIGxiL2Z0wrNcclxuICBwcml2YXRlIHN0YXRpYyByZWFkb25seSBTVEFOREFSRF9WRUxPQ0lUWV9DT05TVEFOVCA9IDQwMDU7IC8vIEZvciBWUCA9IChWLzQwMDUpwrJcclxuICBcclxuICAvLyBWZWxvY2l0eSByYW5nZXMgZm9yIGRpZmZlcmVudCBjYWxjdWxhdGlvbiBtZXRob2RzXHJcbiAgcHJpdmF0ZSBzdGF0aWMgcmVhZG9ubHkgVkVMT0NJVFlfUkFOR0VTID0ge1xyXG4gICAgRk9STVVMQTogeyBtaW46IDAsIG1heDogMTAwMDAgfSxcclxuICAgIExPT0tVUF9UQUJMRTogeyBtaW46IDEwMCwgbWF4OiA1MDAwIH0sXHJcbiAgICBJTlRFUlBPTEFURUQ6IHsgbWluOiA1MCwgbWF4OiA2MDAwIH0sXHJcbiAgICBFTkhBTkNFRF9GT1JNVUxBOiB7IG1pbjogMCwgbWF4OiAxNTAwMCB9LFxyXG4gICAgQ0ZEX0NPUlJFQ1RFRDogeyBtaW46IDUwMCwgbWF4OiA4MDAwIH1cclxuICB9O1xyXG5cclxuICAvLyBBY2N1cmFjeSBlc3RpbWF0ZXMgZm9yIGRpZmZlcmVudCBtZXRob2RzXHJcbiAgcHJpdmF0ZSBzdGF0aWMgcmVhZG9ubHkgTUVUSE9EX0FDQ1VSQUNZID0ge1xyXG4gICAgW1ZlbG9jaXR5UHJlc3N1cmVNZXRob2QuRk9STVVMQV06IDAuOTUsXHJcbiAgICBbVmVsb2NpdHlQcmVzc3VyZU1ldGhvZC5MT09LVVBfVEFCTEVdOiAwLjk4LFxyXG4gICAgW1ZlbG9jaXR5UHJlc3N1cmVNZXRob2QuSU5URVJQT0xBVEVEXTogMC45NyxcclxuICAgIFtWZWxvY2l0eVByZXNzdXJlTWV0aG9kLkVOSEFOQ0VEX0ZPUk1VTEFdOiAwLjk2LFxyXG4gICAgW1ZlbG9jaXR5UHJlc3N1cmVNZXRob2QuQ0ZEX0NPUlJFQ1RFRF06IDAuOTlcclxuICB9O1xyXG5cclxuICAvKipcclxuICAgKiBDYWxjdWxhdGUgdmVsb2NpdHkgcHJlc3N1cmUgdXNpbmcgc3BlY2lmaWVkIG1ldGhvZCBhbmQgY29uZGl0aW9uc1xyXG4gICAqL1xyXG4gIHB1YmxpYyBzdGF0aWMgY2FsY3VsYXRlVmVsb2NpdHlQcmVzc3VyZShpbnB1dDogVmVsb2NpdHlQcmVzc3VyZUlucHV0KTogVmVsb2NpdHlQcmVzc3VyZVJlc3VsdCB7XHJcbiAgICBjb25zdCB7XHJcbiAgICAgIHZlbG9jaXR5LFxyXG4gICAgICBtZXRob2QgPSBWZWxvY2l0eVByZXNzdXJlTWV0aG9kLkVOSEFOQ0VEX0ZPUk1VTEEsXHJcbiAgICAgIGFpckNvbmRpdGlvbnMsXHJcbiAgICAgIGFpckRlbnNpdHksXHJcbiAgICAgIGR1Y3RHZW9tZXRyeSxcclxuICAgICAgdHVyYnVsZW5jZUNvcnJlY3Rpb24gPSBmYWxzZSxcclxuICAgICAgY29tcHJlc3NpYmlsaXR5Q29ycmVjdGlvbiA9IGZhbHNlLFxyXG4gICAgICB2YWxpZGF0aW9uTGV2ZWwgPSBWYWxpZGF0aW9uTGV2ZWwuU1RBTkRBUkRcclxuICAgIH0gPSBpbnB1dDtcclxuXHJcbiAgICBjb25zdCB3YXJuaW5nczogc3RyaW5nW10gPSBbXTtcclxuICAgIGNvbnN0IHJlY29tbWVuZGF0aW9uczogc3RyaW5nW10gPSBbXTtcclxuXHJcbiAgICAvLyBWYWxpZGF0ZSBpbnB1dHNcclxuICAgIHRoaXMudmFsaWRhdGVJbnB1dHMoaW5wdXQsIHZhbGlkYXRpb25MZXZlbCwgd2FybmluZ3MpO1xyXG5cclxuICAgIC8vIERldGVybWluZSBhaXIgZGVuc2l0eVxyXG4gICAgbGV0IGFjdHVhbEFpckRlbnNpdHk6IG51bWJlcjtcclxuICAgIGxldCBkZW5zaXR5Q2FsY3VsYXRpb25NZXRob2Q6IHN0cmluZztcclxuICAgIFxyXG4gICAgaWYgKGFpckRlbnNpdHkgIT09IHVuZGVmaW5lZCkge1xyXG4gICAgICBhY3R1YWxBaXJEZW5zaXR5ID0gYWlyRGVuc2l0eTtcclxuICAgICAgZGVuc2l0eUNhbGN1bGF0aW9uTWV0aG9kID0gJ1VzZXIgc3BlY2lmaWVkJztcclxuICAgIH0gZWxzZSBpZiAoYWlyQ29uZGl0aW9ucykge1xyXG4gICAgICBjb25zdCBhaXJQcm9wcyA9IEFpclByb3BlcnRpZXNDYWxjdWxhdG9yLmNhbGN1bGF0ZUFpclByb3BlcnRpZXMoYWlyQ29uZGl0aW9ucyk7XHJcbiAgICAgIGFjdHVhbEFpckRlbnNpdHkgPSBhaXJQcm9wcy5kZW5zaXR5O1xyXG4gICAgICBkZW5zaXR5Q2FsY3VsYXRpb25NZXRob2QgPSAnQ2FsY3VsYXRlZCBmcm9tIGNvbmRpdGlvbnMnO1xyXG4gICAgICB3YXJuaW5ncy5wdXNoKC4uLmFpclByb3BzLndhcm5pbmdzKTtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIGFjdHVhbEFpckRlbnNpdHkgPSB0aGlzLlNUQU5EQVJEX0FJUl9ERU5TSVRZO1xyXG4gICAgICBkZW5zaXR5Q2FsY3VsYXRpb25NZXRob2QgPSAnU3RhbmRhcmQgY29uZGl0aW9ucyBhc3N1bWVkJztcclxuICAgIH1cclxuXHJcbiAgICAvLyBDYWxjdWxhdGUgY29ycmVjdGlvbnNcclxuICAgIGNvbnN0IGNvcnJlY3Rpb25zID0gdGhpcy5jYWxjdWxhdGVDb3JyZWN0aW9ucyhcclxuICAgICAgYWlyQ29uZGl0aW9ucyxcclxuICAgICAgYWN0dWFsQWlyRGVuc2l0eSxcclxuICAgICAgZHVjdEdlb21ldHJ5LFxyXG4gICAgICB0dXJidWxlbmNlQ29ycmVjdGlvbixcclxuICAgICAgY29tcHJlc3NpYmlsaXR5Q29ycmVjdGlvblxyXG4gICAgKTtcclxuXHJcbiAgICAvLyBTZWxlY3QgYW5kIGV4ZWN1dGUgY2FsY3VsYXRpb24gbWV0aG9kXHJcbiAgICBjb25zdCBiYXNlVmVsb2NpdHlQcmVzc3VyZSA9IHRoaXMuZXhlY3V0ZUNhbGN1bGF0aW9uTWV0aG9kKG1ldGhvZCwgdmVsb2NpdHksIHdhcm5pbmdzKTtcclxuICAgIFxyXG4gICAgLy8gQXBwbHkgY29ycmVjdGlvbnNcclxuICAgIGNvbnN0IGNvcnJlY3RlZFZlbG9jaXR5UHJlc3N1cmUgPSBiYXNlVmVsb2NpdHlQcmVzc3VyZSAqIGNvcnJlY3Rpb25zLmNvbWJpbmVkO1xyXG5cclxuICAgIC8vIENhbGN1bGF0ZSB1bmNlcnRhaW50eSBib3VuZHNcclxuICAgIGNvbnN0IHVuY2VydGFpbnR5Qm91bmRzID0gdGhpcy5jYWxjdWxhdGVVbmNlcnRhaW50eUJvdW5kcyhcclxuICAgICAgY29ycmVjdGVkVmVsb2NpdHlQcmVzc3VyZSxcclxuICAgICAgbWV0aG9kLFxyXG4gICAgICB2ZWxvY2l0eSxcclxuICAgICAgY29ycmVjdGlvbnNcclxuICAgICk7XHJcblxyXG4gICAgLy8gR2VuZXJhdGUgcmVjb21tZW5kYXRpb25zXHJcbiAgICB0aGlzLmdlbmVyYXRlUmVjb21tZW5kYXRpb25zKHZlbG9jaXR5LCBtZXRob2QsIGNvcnJlY3Rpb25zLCByZWNvbW1lbmRhdGlvbnMpO1xyXG5cclxuICAgIHJldHVybiB7XHJcbiAgICAgIHZlbG9jaXR5UHJlc3N1cmU6IGNvcnJlY3RlZFZlbG9jaXR5UHJlc3N1cmUsXHJcbiAgICAgIG1ldGhvZCxcclxuICAgICAgdmVsb2NpdHksXHJcbiAgICAgIGFpckRlbnNpdHk6IGFjdHVhbEFpckRlbnNpdHksXHJcbiAgICAgIGRlbnNpdHlSYXRpbzogYWN0dWFsQWlyRGVuc2l0eSAvIHRoaXMuU1RBTkRBUkRfQUlSX0RFTlNJVFksXHJcbiAgICAgIGNvcnJlY3Rpb25zLFxyXG4gICAgICBhY2N1cmFjeTogdGhpcy5NRVRIT0RfQUNDVVJBQ1lbbWV0aG9kXSxcclxuICAgICAgdW5jZXJ0YWludHlCb3VuZHMsXHJcbiAgICAgIHdhcm5pbmdzLFxyXG4gICAgICByZWNvbW1lbmRhdGlvbnMsXHJcbiAgICAgIGNhbGN1bGF0aW9uRGV0YWlsczoge1xyXG4gICAgICAgIGZvcm11bGE6IHRoaXMuZ2V0Rm9ybXVsYURlc2NyaXB0aW9uKG1ldGhvZCksXHJcbiAgICAgICAgaW50ZXJtZWRpYXRlVmFsdWVzOiB7XHJcbiAgICAgICAgICBiYXNlVmVsb2NpdHlQcmVzc3VyZSxcclxuICAgICAgICAgIGRlbnNpdHlSYXRpbzogYWN0dWFsQWlyRGVuc2l0eSAvIHRoaXMuU1RBTkRBUkRfQUlSX0RFTlNJVFksXHJcbiAgICAgICAgICBjb21iaW5lZENvcnJlY3Rpb246IGNvcnJlY3Rpb25zLmNvbWJpbmVkXHJcbiAgICAgICAgfSxcclxuICAgICAgICBkYXRhU291cmNlOiBkZW5zaXR5Q2FsY3VsYXRpb25NZXRob2QsXHJcbiAgICAgICAgc3RhbmRhcmRSZWZlcmVuY2U6IHRoaXMuZ2V0U3RhbmRhcmRSZWZlcmVuY2UobWV0aG9kKVxyXG4gICAgICB9XHJcbiAgICB9O1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogR2V0IG9wdGltYWwgY2FsY3VsYXRpb24gbWV0aG9kIGZvciBnaXZlbiBjb25kaXRpb25zXHJcbiAgICovXHJcbiAgcHVibGljIHN0YXRpYyBnZXRPcHRpbWFsTWV0aG9kKFxyXG4gICAgdmVsb2NpdHk6IG51bWJlcixcclxuICAgIGFpckNvbmRpdGlvbnM/OiBBaXJDb25kaXRpb25zLFxyXG4gICAgYWNjdXJhY3k6ICdzdGFuZGFyZCcgfCAnaGlnaCcgfCAnbWF4aW11bScgPSAnc3RhbmRhcmQnXHJcbiAgKTogVmVsb2NpdHlQcmVzc3VyZU1ldGhvZCB7XHJcbiAgICAvLyBDaGVjayB2ZWxvY2l0eSByYW5nZXNcclxuICAgIGNvbnN0IGluVGFibGVSYW5nZSA9IHZlbG9jaXR5ID49IHRoaXMuVkVMT0NJVFlfUkFOR0VTLkxPT0tVUF9UQUJMRS5taW4gJiYgXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZlbG9jaXR5IDw9IHRoaXMuVkVMT0NJVFlfUkFOR0VTLkxPT0tVUF9UQUJMRS5tYXg7XHJcbiAgICBjb25zdCBpbkNGRFJhbmdlID0gdmVsb2NpdHkgPj0gdGhpcy5WRUxPQ0lUWV9SQU5HRVMuQ0ZEX0NPUlJFQ1RFRC5taW4gJiYgXHJcbiAgICAgICAgICAgICAgICAgICAgICB2ZWxvY2l0eSA8PSB0aGlzLlZFTE9DSVRZX1JBTkdFUy5DRkRfQ09SUkVDVEVELm1heDtcclxuXHJcbiAgICAvLyBEZXRlcm1pbmUgb3B0aW1hbCBtZXRob2QgYmFzZWQgb24gYWNjdXJhY3kgcmVxdWlyZW1lbnRzIGFuZCBjb25kaXRpb25zXHJcbiAgICBpZiAoYWNjdXJhY3kgPT09ICdtYXhpbXVtJyAmJiBpbkNGRFJhbmdlKSB7XHJcbiAgICAgIHJldHVybiBWZWxvY2l0eVByZXNzdXJlTWV0aG9kLkNGRF9DT1JSRUNURUQ7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIGlmIChhY2N1cmFjeSA9PT0gJ2hpZ2gnICYmIGluVGFibGVSYW5nZSkge1xyXG4gICAgICByZXR1cm4gVmVsb2NpdHlQcmVzc3VyZU1ldGhvZC5MT09LVVBfVEFCTEU7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIGlmIChpblRhYmxlUmFuZ2UgJiYgIWFpckNvbmRpdGlvbnMpIHtcclxuICAgICAgcmV0dXJuIFZlbG9jaXR5UHJlc3N1cmVNZXRob2QuSU5URVJQT0xBVEVEO1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICByZXR1cm4gVmVsb2NpdHlQcmVzc3VyZU1ldGhvZC5FTkhBTkNFRF9GT1JNVUxBO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ2FsY3VsYXRlIHZlbG9jaXR5IGZyb20gdmVsb2NpdHkgcHJlc3N1cmUgKGludmVyc2UgY2FsY3VsYXRpb24pXHJcbiAgICovXHJcbiAgcHVibGljIHN0YXRpYyBjYWxjdWxhdGVWZWxvY2l0eUZyb21QcmVzc3VyZShcclxuICAgIHZlbG9jaXR5UHJlc3N1cmU6IG51bWJlcixcclxuICAgIGFpckNvbmRpdGlvbnM/OiBBaXJDb25kaXRpb25zLFxyXG4gICAgYWlyRGVuc2l0eT86IG51bWJlclxyXG4gICk6IHsgdmVsb2NpdHk6IG51bWJlcjsgYWNjdXJhY3k6IG51bWJlcjsgd2FybmluZ3M6IHN0cmluZ1tdIH0ge1xyXG4gICAgY29uc3Qgd2FybmluZ3M6IHN0cmluZ1tdID0gW107XHJcbiAgICBcclxuICAgIC8vIERldGVybWluZSBhaXIgZGVuc2l0eVxyXG4gICAgbGV0IGFjdHVhbEFpckRlbnNpdHk6IG51bWJlcjtcclxuICAgIGlmIChhaXJEZW5zaXR5ICE9PSB1bmRlZmluZWQpIHtcclxuICAgICAgYWN0dWFsQWlyRGVuc2l0eSA9IGFpckRlbnNpdHk7XHJcbiAgICB9IGVsc2UgaWYgKGFpckNvbmRpdGlvbnMpIHtcclxuICAgICAgY29uc3QgYWlyUHJvcHMgPSBBaXJQcm9wZXJ0aWVzQ2FsY3VsYXRvci5jYWxjdWxhdGVBaXJQcm9wZXJ0aWVzKGFpckNvbmRpdGlvbnMpO1xyXG4gICAgICBhY3R1YWxBaXJEZW5zaXR5ID0gYWlyUHJvcHMuZGVuc2l0eTtcclxuICAgICAgd2FybmluZ3MucHVzaCguLi5haXJQcm9wcy53YXJuaW5ncyk7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICBhY3R1YWxBaXJEZW5zaXR5ID0gdGhpcy5TVEFOREFSRF9BSVJfREVOU0lUWTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBDYWxjdWxhdGUgdmVsb2NpdHkgdXNpbmcgaW52ZXJzZSBmb3JtdWxhXHJcbiAgICBjb25zdCBkZW5zaXR5UmF0aW8gPSBhY3R1YWxBaXJEZW5zaXR5IC8gdGhpcy5TVEFOREFSRF9BSVJfREVOU0lUWTtcclxuICAgIGNvbnN0IGFkanVzdGVkVlAgPSB2ZWxvY2l0eVByZXNzdXJlIC8gZGVuc2l0eVJhdGlvO1xyXG4gICAgY29uc3QgdmVsb2NpdHkgPSB0aGlzLlNUQU5EQVJEX1ZFTE9DSVRZX0NPTlNUQU5UICogTWF0aC5zcXJ0KGFkanVzdGVkVlApO1xyXG5cclxuICAgIHJldHVybiB7XHJcbiAgICAgIHZlbG9jaXR5LFxyXG4gICAgICBhY2N1cmFjeTogMC45NSxcclxuICAgICAgd2FybmluZ3NcclxuICAgIH07XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBWYWxpZGF0ZSBpbnB1dCBwYXJhbWV0ZXJzXHJcbiAgICovXHJcbiAgcHJpdmF0ZSBzdGF0aWMgdmFsaWRhdGVJbnB1dHMoXHJcbiAgICBpbnB1dDogVmVsb2NpdHlQcmVzc3VyZUlucHV0LFxyXG4gICAgdmFsaWRhdGlvbkxldmVsOiBWYWxpZGF0aW9uTGV2ZWwsXHJcbiAgICB3YXJuaW5nczogc3RyaW5nW11cclxuICApOiB2b2lkIHtcclxuICAgIGlmICh2YWxpZGF0aW9uTGV2ZWwgPT09IFZhbGlkYXRpb25MZXZlbC5OT05FKSByZXR1cm47XHJcblxyXG4gICAgY29uc3QgeyB2ZWxvY2l0eSwgbWV0aG9kID0gVmVsb2NpdHlQcmVzc3VyZU1ldGhvZC5FTkhBTkNFRF9GT1JNVUxBIH0gPSBpbnB1dDtcclxuXHJcbiAgICAvLyBCYXNpYyB2YWxpZGF0aW9uXHJcbiAgICBpZiAodmVsb2NpdHkgPCAwKSB7XHJcbiAgICAgIHRocm93IG5ldyBFcnJvcignVmVsb2NpdHkgY2Fubm90IGJlIG5lZ2F0aXZlJyk7XHJcbiAgICB9XHJcblxyXG4gICAgaWYgKHZhbGlkYXRpb25MZXZlbCA9PT0gVmFsaWRhdGlvbkxldmVsLkJBU0lDKSByZXR1cm47XHJcblxyXG4gICAgLy8gU3RhbmRhcmQgdmFsaWRhdGlvblxyXG4gICAgaWYgKHZlbG9jaXR5ID4gMTAwMDApIHtcclxuICAgICAgd2FybmluZ3MucHVzaCgnVmVsb2NpdHkgZXhjZWVkcyB0eXBpY2FsIEhWQUMgcmFuZ2UgKD4xMCwwMDAgRlBNKScpO1xyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IHJhbmdlID0gdGhpcy5WRUxPQ0lUWV9SQU5HRVNbbWV0aG9kXTtcclxuICAgIGlmICh2ZWxvY2l0eSA8IHJhbmdlLm1pbiB8fCB2ZWxvY2l0eSA+IHJhbmdlLm1heCkge1xyXG4gICAgICB3YXJuaW5ncy5wdXNoKGBWZWxvY2l0eSAke3ZlbG9jaXR5fSBGUE0gaXMgb3V0c2lkZSBvcHRpbWFsIHJhbmdlIGZvciAke21ldGhvZH0gbWV0aG9kICgke3JhbmdlLm1pbn0tJHtyYW5nZS5tYXh9IEZQTSlgKTtcclxuICAgIH1cclxuXHJcbiAgICBpZiAodmFsaWRhdGlvbkxldmVsID09PSBWYWxpZGF0aW9uTGV2ZWwuU1RSSUNUKSB7XHJcbiAgICAgIC8vIFN0cmljdCB2YWxpZGF0aW9uXHJcbiAgICAgIGlmICh2ZWxvY2l0eSA8IDEwMCkge1xyXG4gICAgICAgIHdhcm5pbmdzLnB1c2goJ1ZlcnkgbG93IHZlbG9jaXR5IG1heSBpbmRpY2F0ZSBtZWFzdXJlbWVudCBvciBpbnB1dCBlcnJvcicpO1xyXG4gICAgICB9XHJcbiAgICAgIFxyXG4gICAgICBpZiAodmVsb2NpdHkgPiA2MDAwKSB7XHJcbiAgICAgICAgd2FybmluZ3MucHVzaCgnSGlnaCB2ZWxvY2l0eSBtYXkgY2F1c2Ugbm9pc2UgYW5kIGVuZXJneSBlZmZpY2llbmN5IGlzc3VlcycpO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBDYWxjdWxhdGUgZW52aXJvbm1lbnRhbCBhbmQgZ2VvbWV0cmljIGNvcnJlY3Rpb25zXHJcbiAgICovXHJcbiAgcHJpdmF0ZSBzdGF0aWMgY2FsY3VsYXRlQ29ycmVjdGlvbnMoXHJcbiAgICBhaXJDb25kaXRpb25zPzogQWlyQ29uZGl0aW9ucyxcclxuICAgIGFpckRlbnNpdHk/OiBudW1iZXIsXHJcbiAgICBkdWN0R2VvbWV0cnk/OiBEdWN0R2VvbWV0cnksXHJcbiAgICB0dXJidWxlbmNlQ29ycmVjdGlvbj86IGJvb2xlYW4sXHJcbiAgICBjb21wcmVzc2liaWxpdHlDb3JyZWN0aW9uPzogYm9vbGVhblxyXG4gICk6IFZlbG9jaXR5UHJlc3N1cmVSZXN1bHRbJ2NvcnJlY3Rpb25zJ10ge1xyXG4gICAgY29uc3QgY29ycmVjdGlvbnMgPSB7XHJcbiAgICAgIHRlbXBlcmF0dXJlOiAxLjAsXHJcbiAgICAgIHByZXNzdXJlOiAxLjAsXHJcbiAgICAgIGFsdGl0dWRlOiAxLjAsXHJcbiAgICAgIGh1bWlkaXR5OiAxLjAsXHJcbiAgICAgIHR1cmJ1bGVuY2U6IDEuMCxcclxuICAgICAgY29tcHJlc3NpYmlsaXR5OiAxLjAsXHJcbiAgICAgIGNvbWJpbmVkOiAxLjBcclxuICAgIH07XHJcblxyXG4gICAgLy8gRGVuc2l0eS1iYXNlZCBjb3JyZWN0aW9uc1xyXG4gICAgaWYgKGFpckRlbnNpdHkpIHtcclxuICAgICAgY29uc3QgZGVuc2l0eVJhdGlvID0gYWlyRGVuc2l0eSAvIHRoaXMuU1RBTkRBUkRfQUlSX0RFTlNJVFk7XHJcbiAgICAgIGNvcnJlY3Rpb25zLnRlbXBlcmF0dXJlID0gZGVuc2l0eVJhdGlvO1xyXG4gICAgICBjb3JyZWN0aW9ucy5wcmVzc3VyZSA9IGRlbnNpdHlSYXRpbztcclxuICAgICAgY29ycmVjdGlvbnMuYWx0aXR1ZGUgPSBkZW5zaXR5UmF0aW87XHJcbiAgICAgIGNvcnJlY3Rpb25zLmh1bWlkaXR5ID0gZGVuc2l0eVJhdGlvO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIFR1cmJ1bGVuY2UgY29ycmVjdGlvbiAoc2ltcGxpZmllZClcclxuICAgIGlmICh0dXJidWxlbmNlQ29ycmVjdGlvbiAmJiBkdWN0R2VvbWV0cnkpIHtcclxuICAgICAgaWYgKGR1Y3RHZW9tZXRyeS5zaGFwZSA9PT0gJ3JlY3Rhbmd1bGFyJyAmJiBkdWN0R2VvbWV0cnkuYXNwZWN0UmF0aW8gJiYgZHVjdEdlb21ldHJ5LmFzcGVjdFJhdGlvID4gMykge1xyXG4gICAgICAgIGNvcnJlY3Rpb25zLnR1cmJ1bGVuY2UgPSAxLjA1OyAvLyA1JSBpbmNyZWFzZSBmb3IgaGlnaCBhc3BlY3QgcmF0aW9cclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC8vIENvbXByZXNzaWJpbGl0eSBjb3JyZWN0aW9uIChzaW1wbGlmaWVkKVxyXG4gICAgaWYgKGNvbXByZXNzaWJpbGl0eUNvcnJlY3Rpb24gJiYgYWlyQ29uZGl0aW9ucykge1xyXG4gICAgICAvLyBOZWdsaWdpYmxlIGZvciB0eXBpY2FsIEhWQUMgdmVsb2NpdGllcywgYnV0IGluY2x1ZGVkIGZvciBjb21wbGV0ZW5lc3NcclxuICAgICAgY29ycmVjdGlvbnMuY29tcHJlc3NpYmlsaXR5ID0gMS4wO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIENhbGN1bGF0ZSBjb21iaW5lZCBjb3JyZWN0aW9uXHJcbiAgICBjb3JyZWN0aW9ucy5jb21iaW5lZCA9IGNvcnJlY3Rpb25zLnRlbXBlcmF0dXJlICogY29ycmVjdGlvbnMucHJlc3N1cmUgKiBcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBjb3JyZWN0aW9ucy5hbHRpdHVkZSAqIGNvcnJlY3Rpb25zLmh1bWlkaXR5ICogXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY29ycmVjdGlvbnMudHVyYnVsZW5jZSAqIGNvcnJlY3Rpb25zLmNvbXByZXNzaWJpbGl0eTtcclxuXHJcbiAgICByZXR1cm4gY29ycmVjdGlvbnM7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBFeGVjdXRlIHRoZSBzcGVjaWZpZWQgY2FsY3VsYXRpb24gbWV0aG9kXHJcbiAgICovXHJcbiAgcHJpdmF0ZSBzdGF0aWMgZXhlY3V0ZUNhbGN1bGF0aW9uTWV0aG9kKFxyXG4gICAgbWV0aG9kOiBWZWxvY2l0eVByZXNzdXJlTWV0aG9kLFxyXG4gICAgdmVsb2NpdHk6IG51bWJlcixcclxuICAgIHdhcm5pbmdzOiBzdHJpbmdbXVxyXG4gICk6IG51bWJlciB7XHJcbiAgICBzd2l0Y2ggKG1ldGhvZCkge1xyXG4gICAgICBjYXNlIFZlbG9jaXR5UHJlc3N1cmVNZXRob2QuRk9STVVMQTpcclxuICAgICAgICByZXR1cm4gdGhpcy5jYWxjdWxhdGVCeUZvcm11bGEodmVsb2NpdHkpO1xyXG4gICAgICAgIFxyXG4gICAgICBjYXNlIFZlbG9jaXR5UHJlc3N1cmVNZXRob2QuTE9PS1VQX1RBQkxFOlxyXG4gICAgICAgIHJldHVybiB0aGlzLmNhbGN1bGF0ZUJ5TG9va3VwVGFibGUodmVsb2NpdHksIHdhcm5pbmdzKTtcclxuICAgICAgICBcclxuICAgICAgY2FzZSBWZWxvY2l0eVByZXNzdXJlTWV0aG9kLklOVEVSUE9MQVRFRDpcclxuICAgICAgICByZXR1cm4gdGhpcy5jYWxjdWxhdGVCeUludGVycG9sYXRpb24odmVsb2NpdHksIHdhcm5pbmdzKTtcclxuICAgICAgICBcclxuICAgICAgY2FzZSBWZWxvY2l0eVByZXNzdXJlTWV0aG9kLkVOSEFOQ0VEX0ZPUk1VTEE6XHJcbiAgICAgICAgcmV0dXJuIHRoaXMuY2FsY3VsYXRlQnlFbmhhbmNlZEZvcm11bGEodmVsb2NpdHkpO1xyXG4gICAgICAgIFxyXG4gICAgICBjYXNlIFZlbG9jaXR5UHJlc3N1cmVNZXRob2QuQ0ZEX0NPUlJFQ1RFRDpcclxuICAgICAgICByZXR1cm4gdGhpcy5jYWxjdWxhdGVCeUNGRENvcnJlY3Rpb24odmVsb2NpdHksIHdhcm5pbmdzKTtcclxuICAgICAgICBcclxuICAgICAgZGVmYXVsdDpcclxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYFVuc3VwcG9ydGVkIGNhbGN1bGF0aW9uIG1ldGhvZDogJHttZXRob2R9YCk7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBDYWxjdWxhdGUgdmVsb2NpdHkgcHJlc3N1cmUgdXNpbmcgc3RhbmRhcmQgZm9ybXVsYVxyXG4gICAqL1xyXG4gIHByaXZhdGUgc3RhdGljIGNhbGN1bGF0ZUJ5Rm9ybXVsYSh2ZWxvY2l0eTogbnVtYmVyKTogbnVtYmVyIHtcclxuICAgIHJldHVybiBNYXRoLnBvdyh2ZWxvY2l0eSAvIHRoaXMuU1RBTkRBUkRfVkVMT0NJVFlfQ09OU1RBTlQsIDIpO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ2FsY3VsYXRlIHZlbG9jaXR5IHByZXNzdXJlIHVzaW5nIGxvb2t1cCB0YWJsZVxyXG4gICAqL1xyXG4gIHByaXZhdGUgc3RhdGljIGNhbGN1bGF0ZUJ5TG9va3VwVGFibGUodmVsb2NpdHk6IG51bWJlciwgd2FybmluZ3M6IHN0cmluZ1tdKTogbnVtYmVyIHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHZwUmVzdWx0ID0gQWlyUHJvcGVydGllc0NhbGN1bGF0b3IuY2FsY3VsYXRlVmVsb2NpdHlQcmVzc3VyZSh7XHJcbiAgICAgICAgdmVsb2NpdHksXHJcbiAgICAgICAgdXNlVGFibGU6IHRydWVcclxuICAgICAgfSk7XHJcbiAgICAgIHdhcm5pbmdzLnB1c2goLi4udnBSZXN1bHQud2FybmluZ3MpO1xyXG4gICAgICByZXR1cm4gdnBSZXN1bHQudmVsb2NpdHlQcmVzc3VyZTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIHdhcm5pbmdzLnB1c2goJ0xvb2t1cCB0YWJsZSB1bmF2YWlsYWJsZSwgZmFsbGluZyBiYWNrIHRvIGZvcm11bGEgbWV0aG9kJyk7XHJcbiAgICAgIHJldHVybiB0aGlzLmNhbGN1bGF0ZUJ5Rm9ybXVsYSh2ZWxvY2l0eSk7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBDYWxjdWxhdGUgdmVsb2NpdHkgcHJlc3N1cmUgdXNpbmcgaW50ZXJwb2xhdGlvblxyXG4gICAqL1xyXG4gIHByaXZhdGUgc3RhdGljIGNhbGN1bGF0ZUJ5SW50ZXJwb2xhdGlvbih2ZWxvY2l0eTogbnVtYmVyLCB3YXJuaW5nczogc3RyaW5nW10pOiBudW1iZXIge1xyXG4gICAgLy8gVXNlIEFpclByb3BlcnRpZXNDYWxjdWxhdG9yJ3MgaW50ZXJwb2xhdGlvbiBtZXRob2RcclxuICAgIHJldHVybiB0aGlzLmNhbGN1bGF0ZUJ5TG9va3VwVGFibGUodmVsb2NpdHksIHdhcm5pbmdzKTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIENhbGN1bGF0ZSB2ZWxvY2l0eSBwcmVzc3VyZSB1c2luZyBlbmhhbmNlZCBmb3JtdWxhIHdpdGggY29ycmVjdGlvbnNcclxuICAgKi9cclxuICBwcml2YXRlIHN0YXRpYyBjYWxjdWxhdGVCeUVuaGFuY2VkRm9ybXVsYSh2ZWxvY2l0eTogbnVtYmVyKTogbnVtYmVyIHtcclxuICAgIC8vIEVuaGFuY2VkIGZvcm11bGEgd2l0aCBzbGlnaHQgY29ycmVjdGlvbnMgZm9yIHJlYWwtd29ybGQgY29uZGl0aW9uc1xyXG4gICAgY29uc3QgYmFzZVZQID0gTWF0aC5wb3codmVsb2NpdHkgLyB0aGlzLlNUQU5EQVJEX1ZFTE9DSVRZX0NPTlNUQU5ULCAyKTtcclxuICAgIFxyXG4gICAgLy8gQXBwbHkgbWlub3IgY29ycmVjdGlvbiBmb3IgdmVsb2NpdHktZGVwZW5kZW50IGVmZmVjdHNcclxuICAgIGNvbnN0IHZlbG9jaXR5Q29ycmVjdGlvbiA9IDEgKyAodmVsb2NpdHkgLSAyMDAwKSAqIDAuMDAwMDAxOyAvLyBWZXJ5IHNtYWxsIGNvcnJlY3Rpb25cclxuICAgIFxyXG4gICAgcmV0dXJuIGJhc2VWUCAqIE1hdGgubWF4KDAuOTgsIE1hdGgubWluKDEuMDIsIHZlbG9jaXR5Q29ycmVjdGlvbikpO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ2FsY3VsYXRlIHZlbG9jaXR5IHByZXNzdXJlIHVzaW5nIENGRC1kZXJpdmVkIGNvcnJlY3Rpb25zXHJcbiAgICovXHJcbiAgcHJpdmF0ZSBzdGF0aWMgY2FsY3VsYXRlQnlDRkRDb3JyZWN0aW9uKHZlbG9jaXR5OiBudW1iZXIsIHdhcm5pbmdzOiBzdHJpbmdbXSk6IG51bWJlciB7XHJcbiAgICAvLyBDRkQtZGVyaXZlZCBjb3JyZWN0aW9ucyBmb3IgaW1wcm92ZWQgYWNjdXJhY3lcclxuICAgIGNvbnN0IGJhc2VWUCA9IHRoaXMuY2FsY3VsYXRlQnlGb3JtdWxhKHZlbG9jaXR5KTtcclxuICAgIFxyXG4gICAgLy8gQXBwbHkgQ0ZELWRlcml2ZWQgY29ycmVjdGlvbiBmYWN0b3JzIChzaW1wbGlmaWVkKVxyXG4gICAgbGV0IGNmZENvcnJlY3Rpb24gPSAxLjA7XHJcbiAgICBcclxuICAgIGlmICh2ZWxvY2l0eSA8IDEwMDApIHtcclxuICAgICAgY2ZkQ29ycmVjdGlvbiA9IDAuOTg7IC8vIFNsaWdodCB1bmRlci1wcmVkaWN0aW9uIGF0IGxvdyB2ZWxvY2l0aWVzXHJcbiAgICB9IGVsc2UgaWYgKHZlbG9jaXR5ID4gNDAwMCkge1xyXG4gICAgICBjZmRDb3JyZWN0aW9uID0gMS4wMjsgLy8gU2xpZ2h0IG92ZXItcHJlZGljdGlvbiBhdCBoaWdoIHZlbG9jaXRpZXNcclxuICAgIH1cclxuICAgIFxyXG4gICAgd2FybmluZ3MucHVzaCgnQ0ZEIGNvcnJlY3Rpb25zIGFwcGxpZWQgLSByZXN1bHRzIG1heSB2YXJ5IHdpdGggYWN0dWFsIGR1Y3QgY29uZmlndXJhdGlvbicpO1xyXG4gICAgXHJcbiAgICByZXR1cm4gYmFzZVZQICogY2ZkQ29ycmVjdGlvbjtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIENhbGN1bGF0ZSB1bmNlcnRhaW50eSBib3VuZHMgZm9yIHRoZSByZXN1bHRcclxuICAgKi9cclxuICBwcml2YXRlIHN0YXRpYyBjYWxjdWxhdGVVbmNlcnRhaW50eUJvdW5kcyhcclxuICAgIHZlbG9jaXR5UHJlc3N1cmU6IG51bWJlcixcclxuICAgIG1ldGhvZDogVmVsb2NpdHlQcmVzc3VyZU1ldGhvZCxcclxuICAgIHZlbG9jaXR5OiBudW1iZXIsXHJcbiAgICBjb3JyZWN0aW9uczogVmVsb2NpdHlQcmVzc3VyZVJlc3VsdFsnY29ycmVjdGlvbnMnXVxyXG4gICk6IFZlbG9jaXR5UHJlc3N1cmVSZXN1bHRbJ3VuY2VydGFpbnR5Qm91bmRzJ10ge1xyXG4gICAgY29uc3QgYmFzZUFjY3VyYWN5ID0gdGhpcy5NRVRIT0RfQUNDVVJBQ1lbbWV0aG9kXTtcclxuICAgIFxyXG4gICAgLy8gQWRqdXN0IGFjY3VyYWN5IGJhc2VkIG9uIHZlbG9jaXR5IHJhbmdlIGFuZCBjb3JyZWN0aW9uc1xyXG4gICAgbGV0IGFkanVzdGVkQWNjdXJhY3kgPSBiYXNlQWNjdXJhY3k7XHJcbiAgICBcclxuICAgIGlmICh2ZWxvY2l0eSA8IDUwMCB8fCB2ZWxvY2l0eSA+IDUwMDApIHtcclxuICAgICAgYWRqdXN0ZWRBY2N1cmFjeSAqPSAwLjk1OyAvLyBSZWR1Y2VkIGFjY3VyYWN5IG91dHNpZGUgb3B0aW1hbCByYW5nZVxyXG4gICAgfVxyXG4gICAgXHJcbiAgICBpZiAoTWF0aC5hYnMoY29ycmVjdGlvbnMuY29tYmluZWQgLSAxLjApID4gMC4xKSB7XHJcbiAgICAgIGFkanVzdGVkQWNjdXJhY3kgKj0gMC45ODsgLy8gUmVkdWNlZCBhY2N1cmFjeSB3aXRoIGxhcmdlIGNvcnJlY3Rpb25zXHJcbiAgICB9XHJcbiAgICBcclxuICAgIGNvbnN0IHVuY2VydGFpbnR5ID0gdmVsb2NpdHlQcmVzc3VyZSAqICgxIC0gYWRqdXN0ZWRBY2N1cmFjeSk7XHJcbiAgICBcclxuICAgIHJldHVybiB7XHJcbiAgICAgIGxvd2VyOiB2ZWxvY2l0eVByZXNzdXJlIC0gdW5jZXJ0YWludHksXHJcbiAgICAgIHVwcGVyOiB2ZWxvY2l0eVByZXNzdXJlICsgdW5jZXJ0YWludHksXHJcbiAgICAgIGNvbmZpZGVuY2VMZXZlbDogYWRqdXN0ZWRBY2N1cmFjeVxyXG4gICAgfTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEdlbmVyYXRlIHJlY29tbWVuZGF0aW9ucyBiYXNlZCBvbiBjYWxjdWxhdGlvbiByZXN1bHRzXHJcbiAgICovXHJcbiAgcHJpdmF0ZSBzdGF0aWMgZ2VuZXJhdGVSZWNvbW1lbmRhdGlvbnMoXHJcbiAgICB2ZWxvY2l0eTogbnVtYmVyLFxyXG4gICAgbWV0aG9kOiBWZWxvY2l0eVByZXNzdXJlTWV0aG9kLFxyXG4gICAgY29ycmVjdGlvbnM6IFZlbG9jaXR5UHJlc3N1cmVSZXN1bHRbJ2NvcnJlY3Rpb25zJ10sXHJcbiAgICByZWNvbW1lbmRhdGlvbnM6IHN0cmluZ1tdXHJcbiAgKTogdm9pZCB7XHJcbiAgICAvLyBWZWxvY2l0eS1iYXNlZCByZWNvbW1lbmRhdGlvbnNcclxuICAgIGlmICh2ZWxvY2l0eSA8IDUwMCkge1xyXG4gICAgICByZWNvbW1lbmRhdGlvbnMucHVzaCgnQ29uc2lkZXIgaW5jcmVhc2luZyB2ZWxvY2l0eSB0byBpbXByb3ZlIGFjY3VyYWN5IGFuZCBzeXN0ZW0gcGVyZm9ybWFuY2UnKTtcclxuICAgIH0gZWxzZSBpZiAodmVsb2NpdHkgPiA0MDAwKSB7XHJcbiAgICAgIHJlY29tbWVuZGF0aW9ucy5wdXNoKCdIaWdoIHZlbG9jaXR5IG1heSBjYXVzZSBub2lzZSBpc3N1ZXMgLSBjb25zaWRlciBsYXJnZXIgZHVjdCBzaXplJyk7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIC8vIE1ldGhvZC1iYXNlZCByZWNvbW1lbmRhdGlvbnNcclxuICAgIGlmIChtZXRob2QgPT09IFZlbG9jaXR5UHJlc3N1cmVNZXRob2QuRk9STVVMQSAmJiB2ZWxvY2l0eSA+PSAxMDAgJiYgdmVsb2NpdHkgPD0gNTAwMCkge1xyXG4gICAgICByZWNvbW1lbmRhdGlvbnMucHVzaCgnQ29uc2lkZXIgdXNpbmcgbG9va3VwIHRhYmxlIG1ldGhvZCBmb3IgaW1wcm92ZWQgYWNjdXJhY3kgaW4gdGhpcyB2ZWxvY2l0eSByYW5nZScpO1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICAvLyBDb3JyZWN0aW9uLWJhc2VkIHJlY29tbWVuZGF0aW9uc1xyXG4gICAgaWYgKE1hdGguYWJzKGNvcnJlY3Rpb25zLmNvbWJpbmVkIC0gMS4wKSA+IDAuMDUpIHtcclxuICAgICAgcmVjb21tZW5kYXRpb25zLnB1c2goJ1NpZ25pZmljYW50IGVudmlyb25tZW50YWwgY29ycmVjdGlvbnMgYXBwbGllZCAtIHZlcmlmeSBhaXIgY29uZGl0aW9ucycpO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogR2V0IGZvcm11bGEgZGVzY3JpcHRpb24gZm9yIHRoZSBtZXRob2RcclxuICAgKi9cclxuICBwcml2YXRlIHN0YXRpYyBnZXRGb3JtdWxhRGVzY3JpcHRpb24obWV0aG9kOiBWZWxvY2l0eVByZXNzdXJlTWV0aG9kKTogc3RyaW5nIHtcclxuICAgIHN3aXRjaCAobWV0aG9kKSB7XHJcbiAgICAgIGNhc2UgVmVsb2NpdHlQcmVzc3VyZU1ldGhvZC5GT1JNVUxBOlxyXG4gICAgICAgIHJldHVybiAnVlAgPSAoVi80MDA1KcKyJztcclxuICAgICAgY2FzZSBWZWxvY2l0eVByZXNzdXJlTWV0aG9kLkxPT0tVUF9UQUJMRTpcclxuICAgICAgICByZXR1cm4gJ1RhYmxlIGxvb2t1cCB3aXRoIGV4YWN0IHZhbHVlcyc7XHJcbiAgICAgIGNhc2UgVmVsb2NpdHlQcmVzc3VyZU1ldGhvZC5JTlRFUlBPTEFURUQ6XHJcbiAgICAgICAgcmV0dXJuICdUYWJsZSBsb29rdXAgd2l0aCBsaW5lYXIgaW50ZXJwb2xhdGlvbic7XHJcbiAgICAgIGNhc2UgVmVsb2NpdHlQcmVzc3VyZU1ldGhvZC5FTkhBTkNFRF9GT1JNVUxBOlxyXG4gICAgICAgIHJldHVybiAnVlAgPSAoVi80MDA1KcKyIHdpdGggdmVsb2NpdHktZGVwZW5kZW50IGNvcnJlY3Rpb25zJztcclxuICAgICAgY2FzZSBWZWxvY2l0eVByZXNzdXJlTWV0aG9kLkNGRF9DT1JSRUNURUQ6XHJcbiAgICAgICAgcmV0dXJuICdWUCA9IChWLzQwMDUpwrIgd2l0aCBDRkQtZGVyaXZlZCBjb3JyZWN0aW9ucyc7XHJcbiAgICAgIGRlZmF1bHQ6XHJcbiAgICAgICAgcmV0dXJuICdVbmtub3duIG1ldGhvZCc7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBHZXQgc3RhbmRhcmQgcmVmZXJlbmNlIGZvciB0aGUgbWV0aG9kXHJcbiAgICovXHJcbiAgcHJpdmF0ZSBzdGF0aWMgZ2V0U3RhbmRhcmRSZWZlcmVuY2UobWV0aG9kOiBWZWxvY2l0eVByZXNzdXJlTWV0aG9kKTogc3RyaW5nIHtcclxuICAgIHN3aXRjaCAobWV0aG9kKSB7XHJcbiAgICAgIGNhc2UgVmVsb2NpdHlQcmVzc3VyZU1ldGhvZC5GT1JNVUxBOlxyXG4gICAgICBjYXNlIFZlbG9jaXR5UHJlc3N1cmVNZXRob2QuRU5IQU5DRURfRk9STVVMQTpcclxuICAgICAgICByZXR1cm4gJ0FTSFJBRSBGdW5kYW1lbnRhbHMsIENoYXB0ZXIgMjEnO1xyXG4gICAgICBjYXNlIFZlbG9jaXR5UHJlc3N1cmVNZXRob2QuTE9PS1VQX1RBQkxFOlxyXG4gICAgICBjYXNlIFZlbG9jaXR5UHJlc3N1cmVNZXRob2QuSU5URVJQT0xBVEVEOlxyXG4gICAgICAgIHJldHVybiAnQVNIUkFFIEZ1bmRhbWVudGFscywgVGFibGUgMjEtMSc7XHJcbiAgICAgIGNhc2UgVmVsb2NpdHlQcmVzc3VyZU1ldGhvZC5DRkRfQ09SUkVDVEVEOlxyXG4gICAgICAgIHJldHVybiAnQ0ZEIEFuYWx5c2lzIGFuZCBBU0hSQUUgRnVuZGFtZW50YWxzJztcclxuICAgICAgZGVmYXVsdDpcclxuICAgICAgICByZXR1cm4gJ0ludGVybmFsIGNhbGN1bGF0aW9uJztcclxuICAgIH1cclxuICB9XHJcbn1cclxuIl0sIm1hcHBpbmdzIjoiOztBQUFBOzs7Ozs7Ozs7O0FBQUE7QUFBQSxTQUFBQSxjQUFBO0VBQUEsSUFBQUMsSUFBQTtFQUFBLElBQUFDLElBQUE7RUFBQSxJQUFBQyxNQUFBLE9BQUFDLFFBQUE7RUFBQSxJQUFBQyxHQUFBO0VBQUEsSUFBQUMsWUFBQTtJQUFBTCxJQUFBO0lBQUFNLFlBQUE7TUFBQTtRQUFBQyxLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO0lBQUE7SUFBQUUsS0FBQTtNQUFBO1FBQUFDLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtJQUFBO0lBQUFPLFNBQUE7TUFBQTtRQUFBRCxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7SUFBQTtJQUFBVyxDQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtJQUFBO0lBQUFDLENBQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO0lBQUE7SUFBQUMsQ0FBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO0lBQUE7SUFBQUMsY0FBQTtNQUFBQyxJQUFBO01BQUFDLFFBQUE7TUFBQUMsS0FBQTtNQUFBQyxPQUFBO01BQUFDLGNBQUE7TUFBQUMsT0FBQTtJQUFBO0lBQUFDLGVBQUE7SUFBQTVCLElBQUE7RUFBQTtFQUFBLElBQUE2QixRQUFBLEdBQUE1QixNQUFBLENBQUFFLEdBQUEsTUFBQUYsTUFBQSxDQUFBRSxHQUFBO0VBQUEsS0FBQTBCLFFBQUEsQ0FBQTlCLElBQUEsS0FBQThCLFFBQUEsQ0FBQTlCLElBQUEsRUFBQUMsSUFBQSxLQUFBQSxJQUFBO0lBQUE2QixRQUFBLENBQUE5QixJQUFBLElBQUFLLFlBQUE7RUFBQTtFQUFBLElBQUEwQixjQUFBLEdBQUFELFFBQUEsQ0FBQTlCLElBQUE7RUFBQTs7Ozs7Ozs7Ozs7Ozs7OztBQVdBLE1BQUFnQyx5QkFBQTtBQUFBO0FBQUEsQ0FBQWpDLGFBQUEsR0FBQW9CLENBQUEsT0FBQWMsT0FBQTtBQUVBOzs7QUFHQSxJQUFZQyxzQkFNWDtBQUFBO0FBQUFuQyxhQUFBLEdBQUFvQixDQUFBO0FBTkQsV0FBWWUsc0JBQXNCO0VBQUE7RUFBQW5DLGFBQUEsR0FBQXFCLENBQUE7RUFBQXJCLGFBQUEsR0FBQW9CLENBQUE7RUFDaENlLHNCQUFBLHVCQUFtQjtFQUFBO0VBQUFuQyxhQUFBLEdBQUFvQixDQUFBO0VBQ25CZSxzQkFBQSxpQ0FBNkI7RUFBQTtFQUFBbkMsYUFBQSxHQUFBb0IsQ0FBQTtFQUM3QmUsc0JBQUEsaUNBQTZCO0VBQUE7RUFBQW5DLGFBQUEsR0FBQW9CLENBQUE7RUFDN0JlLHNCQUFBLHlDQUFxQztFQUFBO0VBQUFuQyxhQUFBLEdBQUFvQixDQUFBO0VBQ3JDZSxzQkFBQSxtQ0FBK0I7QUFDakMsQ0FBQztBQU5XO0FBQUEsQ0FBQW5DLGFBQUEsR0FBQXNCLENBQUEsVUFBQWEsc0JBQXNCO0FBQUE7QUFBQSxDQUFBbkMsYUFBQSxHQUFBc0IsQ0FBQSxVQUFBYyxPQUFBLENBQUFELHNCQUFBLEdBQXRCQSxzQkFBc0I7QUFvQ2xDOzs7QUFHQSxJQUFZRSxlQUtYO0FBQUE7QUFBQXJDLGFBQUEsR0FBQW9CLENBQUE7QUFMRCxXQUFZaUIsZUFBZTtFQUFBO0VBQUFyQyxhQUFBLEdBQUFxQixDQUFBO0VBQUFyQixhQUFBLEdBQUFvQixDQUFBO0VBQ3pCaUIsZUFBQSxpQkFBYTtFQUFBO0VBQUFyQyxhQUFBLEdBQUFvQixDQUFBO0VBQ2JpQixlQUFBLG1CQUFlO0VBQUE7RUFBQXJDLGFBQUEsR0FBQW9CLENBQUE7RUFDZmlCLGVBQUEseUJBQXFCO0VBQUE7RUFBQXJDLGFBQUEsR0FBQW9CLENBQUE7RUFDckJpQixlQUFBLHFCQUFpQjtBQUNuQixDQUFDO0FBTFc7QUFBQSxDQUFBckMsYUFBQSxHQUFBc0IsQ0FBQSxVQUFBZSxlQUFlO0FBQUE7QUFBQSxDQUFBckMsYUFBQSxHQUFBc0IsQ0FBQSxVQUFBYyxPQUFBLENBQUFDLGVBQUEsR0FBZkEsZUFBZTtBQXlDM0I7Ozs7OztBQU1BLE1BQWFDLDBCQUEwQjtFQXVCckM7OztFQUdPLE9BQU9DLHlCQUF5QkEsQ0FBQ0MsS0FBNEI7SUFBQTtJQUFBeEMsYUFBQSxHQUFBcUIsQ0FBQTtJQUNsRSxNQUFNO01BQ0pvQixRQUFRO01BQ1JDLE1BQU07TUFBQTtNQUFBLENBQUExQyxhQUFBLEdBQUFzQixDQUFBLFVBQUdhLHNCQUFzQixDQUFDUSxnQkFBZ0I7TUFDaERDLGFBQWE7TUFDYkMsVUFBVTtNQUNWQyxZQUFZO01BQ1pDLG9CQUFvQjtNQUFBO01BQUEsQ0FBQS9DLGFBQUEsR0FBQXNCLENBQUEsVUFBRyxLQUFLO01BQzVCMEIseUJBQXlCO01BQUE7TUFBQSxDQUFBaEQsYUFBQSxHQUFBc0IsQ0FBQSxVQUFHLEtBQUs7TUFDakMyQixlQUFlO01BQUE7TUFBQSxDQUFBakQsYUFBQSxHQUFBc0IsQ0FBQSxVQUFHZSxlQUFlLENBQUNhLFFBQVE7SUFBQSxDQUMzQztJQUFBO0lBQUEsQ0FBQWxELGFBQUEsR0FBQW9CLENBQUEsUUFBR29CLEtBQUs7SUFFVCxNQUFNVyxRQUFRO0lBQUE7SUFBQSxDQUFBbkQsYUFBQSxHQUFBb0IsQ0FBQSxRQUFhLEVBQUU7SUFDN0IsTUFBTWdDLGVBQWU7SUFBQTtJQUFBLENBQUFwRCxhQUFBLEdBQUFvQixDQUFBLFFBQWEsRUFBRTtJQUVwQztJQUFBO0lBQUFwQixhQUFBLEdBQUFvQixDQUFBO0lBQ0EsSUFBSSxDQUFDaUMsY0FBYyxDQUFDYixLQUFLLEVBQUVTLGVBQWUsRUFBRUUsUUFBUSxDQUFDO0lBRXJEO0lBQ0EsSUFBSUcsZ0JBQXdCO0lBQzVCLElBQUlDLHdCQUFnQztJQUFDO0lBQUF2RCxhQUFBLEdBQUFvQixDQUFBO0lBRXJDLElBQUl5QixVQUFVLEtBQUsxQixTQUFTLEVBQUU7TUFBQTtNQUFBbkIsYUFBQSxHQUFBc0IsQ0FBQTtNQUFBdEIsYUFBQSxHQUFBb0IsQ0FBQTtNQUM1QmtDLGdCQUFnQixHQUFHVCxVQUFVO01BQUM7TUFBQTdDLGFBQUEsR0FBQW9CLENBQUE7TUFDOUJtQyx3QkFBd0IsR0FBRyxnQkFBZ0I7SUFDN0MsQ0FBQyxNQUFNO01BQUE7TUFBQXZELGFBQUEsR0FBQXNCLENBQUE7TUFBQXRCLGFBQUEsR0FBQW9CLENBQUE7TUFBQSxJQUFJd0IsYUFBYSxFQUFFO1FBQUE7UUFBQTVDLGFBQUEsR0FBQXNCLENBQUE7UUFDeEIsTUFBTWtDLFFBQVE7UUFBQTtRQUFBLENBQUF4RCxhQUFBLEdBQUFvQixDQUFBLFFBQUdhLHlCQUFBLENBQUF3Qix1QkFBdUIsQ0FBQ0Msc0JBQXNCLENBQUNkLGFBQWEsQ0FBQztRQUFDO1FBQUE1QyxhQUFBLEdBQUFvQixDQUFBO1FBQy9Fa0MsZ0JBQWdCLEdBQUdFLFFBQVEsQ0FBQ0csT0FBTztRQUFDO1FBQUEzRCxhQUFBLEdBQUFvQixDQUFBO1FBQ3BDbUMsd0JBQXdCLEdBQUcsNEJBQTRCO1FBQUM7UUFBQXZELGFBQUEsR0FBQW9CLENBQUE7UUFDeEQrQixRQUFRLENBQUNTLElBQUksQ0FBQyxHQUFHSixRQUFRLENBQUNMLFFBQVEsQ0FBQztNQUNyQyxDQUFDLE1BQU07UUFBQTtRQUFBbkQsYUFBQSxHQUFBc0IsQ0FBQTtRQUFBdEIsYUFBQSxHQUFBb0IsQ0FBQTtRQUNMa0MsZ0JBQWdCLEdBQUcsSUFBSSxDQUFDTyxvQkFBb0I7UUFBQztRQUFBN0QsYUFBQSxHQUFBb0IsQ0FBQTtRQUM3Q21DLHdCQUF3QixHQUFHLDZCQUE2QjtNQUMxRDtJQUFBO0lBRUE7SUFDQSxNQUFNTyxXQUFXO0lBQUE7SUFBQSxDQUFBOUQsYUFBQSxHQUFBb0IsQ0FBQSxRQUFHLElBQUksQ0FBQzJDLG9CQUFvQixDQUMzQ25CLGFBQWEsRUFDYlUsZ0JBQWdCLEVBQ2hCUixZQUFZLEVBQ1pDLG9CQUFvQixFQUNwQkMseUJBQXlCLENBQzFCO0lBRUQ7SUFDQSxNQUFNZ0Isb0JBQW9CO0lBQUE7SUFBQSxDQUFBaEUsYUFBQSxHQUFBb0IsQ0FBQSxRQUFHLElBQUksQ0FBQzZDLHdCQUF3QixDQUFDdkIsTUFBTSxFQUFFRCxRQUFRLEVBQUVVLFFBQVEsQ0FBQztJQUV0RjtJQUNBLE1BQU1lLHlCQUF5QjtJQUFBO0lBQUEsQ0FBQWxFLGFBQUEsR0FBQW9CLENBQUEsUUFBRzRDLG9CQUFvQixHQUFHRixXQUFXLENBQUNLLFFBQVE7SUFFN0U7SUFDQSxNQUFNQyxpQkFBaUI7SUFBQTtJQUFBLENBQUFwRSxhQUFBLEdBQUFvQixDQUFBLFFBQUcsSUFBSSxDQUFDaUQsMEJBQTBCLENBQ3ZESCx5QkFBeUIsRUFDekJ4QixNQUFNLEVBQ05ELFFBQVEsRUFDUnFCLFdBQVcsQ0FDWjtJQUVEO0lBQUE7SUFBQTlELGFBQUEsR0FBQW9CLENBQUE7SUFDQSxJQUFJLENBQUNrRCx1QkFBdUIsQ0FBQzdCLFFBQVEsRUFBRUMsTUFBTSxFQUFFb0IsV0FBVyxFQUFFVixlQUFlLENBQUM7SUFBQztJQUFBcEQsYUFBQSxHQUFBb0IsQ0FBQTtJQUU3RSxPQUFPO01BQ0xtRCxnQkFBZ0IsRUFBRUwseUJBQXlCO01BQzNDeEIsTUFBTTtNQUNORCxRQUFRO01BQ1JJLFVBQVUsRUFBRVMsZ0JBQWdCO01BQzVCa0IsWUFBWSxFQUFFbEIsZ0JBQWdCLEdBQUcsSUFBSSxDQUFDTyxvQkFBb0I7TUFDMURDLFdBQVc7TUFDWFcsUUFBUSxFQUFFLElBQUksQ0FBQ0MsZUFBZSxDQUFDaEMsTUFBTSxDQUFDO01BQ3RDMEIsaUJBQWlCO01BQ2pCakIsUUFBUTtNQUNSQyxlQUFlO01BQ2Z1QixrQkFBa0IsRUFBRTtRQUNsQkMsT0FBTyxFQUFFLElBQUksQ0FBQ0MscUJBQXFCLENBQUNuQyxNQUFNLENBQUM7UUFDM0NvQyxrQkFBa0IsRUFBRTtVQUNsQmQsb0JBQW9CO1VBQ3BCUSxZQUFZLEVBQUVsQixnQkFBZ0IsR0FBRyxJQUFJLENBQUNPLG9CQUFvQjtVQUMxRGtCLGtCQUFrQixFQUFFakIsV0FBVyxDQUFDSztTQUNqQztRQUNEYSxVQUFVLEVBQUV6Qix3QkFBd0I7UUFDcEMwQixpQkFBaUIsRUFBRSxJQUFJLENBQUNDLG9CQUFvQixDQUFDeEMsTUFBTTs7S0FFdEQ7RUFDSDtFQUVBOzs7RUFHTyxPQUFPeUMsZ0JBQWdCQSxDQUM1QjFDLFFBQWdCLEVBQ2hCRyxhQUE2QixFQUM3QjZCLFFBQUE7RUFBQTtFQUFBLENBQUF6RSxhQUFBLEdBQUFzQixDQUFBLFVBQTRDLFVBQVU7SUFBQTtJQUFBdEIsYUFBQSxHQUFBcUIsQ0FBQTtJQUV0RDtJQUNBLE1BQU0rRCxZQUFZO0lBQUE7SUFBQSxDQUFBcEYsYUFBQSxHQUFBb0IsQ0FBQTtJQUFHO0lBQUEsQ0FBQXBCLGFBQUEsR0FBQXNCLENBQUEsVUFBQW1CLFFBQVEsSUFBSSxJQUFJLENBQUM0QyxlQUFlLENBQUNDLFlBQVksQ0FBQ0MsR0FBRztJQUFBO0lBQUEsQ0FBQXZGLGFBQUEsR0FBQXNCLENBQUEsVUFDbERtQixRQUFRLElBQUksSUFBSSxDQUFDNEMsZUFBZSxDQUFDQyxZQUFZLENBQUNFLEdBQUc7SUFDckUsTUFBTUMsVUFBVTtJQUFBO0lBQUEsQ0FBQXpGLGFBQUEsR0FBQW9CLENBQUE7SUFBRztJQUFBLENBQUFwQixhQUFBLEdBQUFzQixDQUFBLFdBQUFtQixRQUFRLElBQUksSUFBSSxDQUFDNEMsZUFBZSxDQUFDSyxhQUFhLENBQUNILEdBQUc7SUFBQTtJQUFBLENBQUF2RixhQUFBLEdBQUFzQixDQUFBLFdBQ25EbUIsUUFBUSxJQUFJLElBQUksQ0FBQzRDLGVBQWUsQ0FBQ0ssYUFBYSxDQUFDRixHQUFHO0lBRXBFO0lBQUE7SUFBQXhGLGFBQUEsR0FBQW9CLENBQUE7SUFDQTtJQUFJO0lBQUEsQ0FBQXBCLGFBQUEsR0FBQXNCLENBQUEsV0FBQW1ELFFBQVEsS0FBSyxTQUFTO0lBQUE7SUFBQSxDQUFBekUsYUFBQSxHQUFBc0IsQ0FBQSxXQUFJbUUsVUFBVSxHQUFFO01BQUE7TUFBQXpGLGFBQUEsR0FBQXNCLENBQUE7TUFBQXRCLGFBQUEsR0FBQW9CLENBQUE7TUFDeEMsT0FBT2Usc0JBQXNCLENBQUN1RCxhQUFhO0lBQzdDLENBQUM7SUFBQTtJQUFBO01BQUExRixhQUFBLEdBQUFzQixDQUFBO0lBQUE7SUFBQXRCLGFBQUEsR0FBQW9CLENBQUE7SUFFRDtJQUFJO0lBQUEsQ0FBQXBCLGFBQUEsR0FBQXNCLENBQUEsV0FBQW1ELFFBQVEsS0FBSyxNQUFNO0lBQUE7SUFBQSxDQUFBekUsYUFBQSxHQUFBc0IsQ0FBQSxXQUFJOEQsWUFBWSxHQUFFO01BQUE7TUFBQXBGLGFBQUEsR0FBQXNCLENBQUE7TUFBQXRCLGFBQUEsR0FBQW9CLENBQUE7TUFDdkMsT0FBT2Usc0JBQXNCLENBQUNtRCxZQUFZO0lBQzVDLENBQUM7SUFBQTtJQUFBO01BQUF0RixhQUFBLEdBQUFzQixDQUFBO0lBQUE7SUFBQXRCLGFBQUEsR0FBQW9CLENBQUE7SUFFRDtJQUFJO0lBQUEsQ0FBQXBCLGFBQUEsR0FBQXNCLENBQUEsV0FBQThELFlBQVk7SUFBQTtJQUFBLENBQUFwRixhQUFBLEdBQUFzQixDQUFBLFdBQUksQ0FBQ3NCLGFBQWEsR0FBRTtNQUFBO01BQUE1QyxhQUFBLEdBQUFzQixDQUFBO01BQUF0QixhQUFBLEdBQUFvQixDQUFBO01BQ2xDLE9BQU9lLHNCQUFzQixDQUFDd0QsWUFBWTtJQUM1QyxDQUFDO0lBQUE7SUFBQTtNQUFBM0YsYUFBQSxHQUFBc0IsQ0FBQTtJQUFBO0lBQUF0QixhQUFBLEdBQUFvQixDQUFBO0lBRUQsT0FBT2Usc0JBQXNCLENBQUNRLGdCQUFnQjtFQUNoRDtFQUVBOzs7RUFHTyxPQUFPaUQsNkJBQTZCQSxDQUN6Q3JCLGdCQUF3QixFQUN4QjNCLGFBQTZCLEVBQzdCQyxVQUFtQjtJQUFBO0lBQUE3QyxhQUFBLEdBQUFxQixDQUFBO0lBRW5CLE1BQU04QixRQUFRO0lBQUE7SUFBQSxDQUFBbkQsYUFBQSxHQUFBb0IsQ0FBQSxRQUFhLEVBQUU7SUFFN0I7SUFDQSxJQUFJa0MsZ0JBQXdCO0lBQUM7SUFBQXRELGFBQUEsR0FBQW9CLENBQUE7SUFDN0IsSUFBSXlCLFVBQVUsS0FBSzFCLFNBQVMsRUFBRTtNQUFBO01BQUFuQixhQUFBLEdBQUFzQixDQUFBO01BQUF0QixhQUFBLEdBQUFvQixDQUFBO01BQzVCa0MsZ0JBQWdCLEdBQUdULFVBQVU7SUFDL0IsQ0FBQyxNQUFNO01BQUE7TUFBQTdDLGFBQUEsR0FBQXNCLENBQUE7TUFBQXRCLGFBQUEsR0FBQW9CLENBQUE7TUFBQSxJQUFJd0IsYUFBYSxFQUFFO1FBQUE7UUFBQTVDLGFBQUEsR0FBQXNCLENBQUE7UUFDeEIsTUFBTWtDLFFBQVE7UUFBQTtRQUFBLENBQUF4RCxhQUFBLEdBQUFvQixDQUFBLFFBQUdhLHlCQUFBLENBQUF3Qix1QkFBdUIsQ0FBQ0Msc0JBQXNCLENBQUNkLGFBQWEsQ0FBQztRQUFDO1FBQUE1QyxhQUFBLEdBQUFvQixDQUFBO1FBQy9Fa0MsZ0JBQWdCLEdBQUdFLFFBQVEsQ0FBQ0csT0FBTztRQUFDO1FBQUEzRCxhQUFBLEdBQUFvQixDQUFBO1FBQ3BDK0IsUUFBUSxDQUFDUyxJQUFJLENBQUMsR0FBR0osUUFBUSxDQUFDTCxRQUFRLENBQUM7TUFDckMsQ0FBQyxNQUFNO1FBQUE7UUFBQW5ELGFBQUEsR0FBQXNCLENBQUE7UUFBQXRCLGFBQUEsR0FBQW9CLENBQUE7UUFDTGtDLGdCQUFnQixHQUFHLElBQUksQ0FBQ08sb0JBQW9CO01BQzlDO0lBQUE7SUFFQTtJQUNBLE1BQU1XLFlBQVk7SUFBQTtJQUFBLENBQUF4RSxhQUFBLEdBQUFvQixDQUFBLFFBQUdrQyxnQkFBZ0IsR0FBRyxJQUFJLENBQUNPLG9CQUFvQjtJQUNqRSxNQUFNZ0MsVUFBVTtJQUFBO0lBQUEsQ0FBQTdGLGFBQUEsR0FBQW9CLENBQUEsUUFBR21ELGdCQUFnQixHQUFHQyxZQUFZO0lBQ2xELE1BQU0vQixRQUFRO0lBQUE7SUFBQSxDQUFBekMsYUFBQSxHQUFBb0IsQ0FBQSxRQUFHLElBQUksQ0FBQzBFLDBCQUEwQixHQUFHQyxJQUFJLENBQUNDLElBQUksQ0FBQ0gsVUFBVSxDQUFDO0lBQUM7SUFBQTdGLGFBQUEsR0FBQW9CLENBQUE7SUFFekUsT0FBTztNQUNMcUIsUUFBUTtNQUNSZ0MsUUFBUSxFQUFFLElBQUk7TUFDZHRCO0tBQ0Q7RUFDSDtFQUVBOzs7RUFHUSxPQUFPRSxjQUFjQSxDQUMzQmIsS0FBNEIsRUFDNUJTLGVBQWdDLEVBQ2hDRSxRQUFrQjtJQUFBO0lBQUFuRCxhQUFBLEdBQUFxQixDQUFBO0lBQUFyQixhQUFBLEdBQUFvQixDQUFBO0lBRWxCLElBQUk2QixlQUFlLEtBQUtaLGVBQWUsQ0FBQzRELElBQUksRUFBRTtNQUFBO01BQUFqRyxhQUFBLEdBQUFzQixDQUFBO01BQUF0QixhQUFBLEdBQUFvQixDQUFBO01BQUE7SUFBQSxDQUFPO0lBQUE7SUFBQTtNQUFBcEIsYUFBQSxHQUFBc0IsQ0FBQTtJQUFBO0lBRXJELE1BQU07TUFBRW1CLFFBQVE7TUFBRUMsTUFBTTtNQUFBO01BQUEsQ0FBQTFDLGFBQUEsR0FBQXNCLENBQUEsV0FBR2Esc0JBQXNCLENBQUNRLGdCQUFnQjtJQUFBLENBQUU7SUFBQTtJQUFBLENBQUEzQyxhQUFBLEdBQUFvQixDQUFBLFFBQUdvQixLQUFLO0lBRTVFO0lBQUE7SUFBQXhDLGFBQUEsR0FBQW9CLENBQUE7SUFDQSxJQUFJcUIsUUFBUSxHQUFHLENBQUMsRUFBRTtNQUFBO01BQUF6QyxhQUFBLEdBQUFzQixDQUFBO01BQUF0QixhQUFBLEdBQUFvQixDQUFBO01BQ2hCLE1BQU0sSUFBSThFLEtBQUssQ0FBQyw2QkFBNkIsQ0FBQztJQUNoRCxDQUFDO0lBQUE7SUFBQTtNQUFBbEcsYUFBQSxHQUFBc0IsQ0FBQTtJQUFBO0lBQUF0QixhQUFBLEdBQUFvQixDQUFBO0lBRUQsSUFBSTZCLGVBQWUsS0FBS1osZUFBZSxDQUFDOEQsS0FBSyxFQUFFO01BQUE7TUFBQW5HLGFBQUEsR0FBQXNCLENBQUE7TUFBQXRCLGFBQUEsR0FBQW9CLENBQUE7TUFBQTtJQUFBLENBQU87SUFBQTtJQUFBO01BQUFwQixhQUFBLEdBQUFzQixDQUFBO0lBQUE7SUFFdEQ7SUFBQXRCLGFBQUEsR0FBQW9CLENBQUE7SUFDQSxJQUFJcUIsUUFBUSxHQUFHLEtBQUssRUFBRTtNQUFBO01BQUF6QyxhQUFBLEdBQUFzQixDQUFBO01BQUF0QixhQUFBLEdBQUFvQixDQUFBO01BQ3BCK0IsUUFBUSxDQUFDUyxJQUFJLENBQUMsbURBQW1ELENBQUM7SUFDcEUsQ0FBQztJQUFBO0lBQUE7TUFBQTVELGFBQUEsR0FBQXNCLENBQUE7SUFBQTtJQUVELE1BQU04RSxLQUFLO0lBQUE7SUFBQSxDQUFBcEcsYUFBQSxHQUFBb0IsQ0FBQSxRQUFHLElBQUksQ0FBQ2lFLGVBQWUsQ0FBQzNDLE1BQU0sQ0FBQztJQUFDO0lBQUExQyxhQUFBLEdBQUFvQixDQUFBO0lBQzNDO0lBQUk7SUFBQSxDQUFBcEIsYUFBQSxHQUFBc0IsQ0FBQSxXQUFBbUIsUUFBUSxHQUFHMkQsS0FBSyxDQUFDYixHQUFHO0lBQUE7SUFBQSxDQUFBdkYsYUFBQSxHQUFBc0IsQ0FBQSxXQUFJbUIsUUFBUSxHQUFHMkQsS0FBSyxDQUFDWixHQUFHLEdBQUU7TUFBQTtNQUFBeEYsYUFBQSxHQUFBc0IsQ0FBQTtNQUFBdEIsYUFBQSxHQUFBb0IsQ0FBQTtNQUNoRCtCLFFBQVEsQ0FBQ1MsSUFBSSxDQUFDLFlBQVluQixRQUFRLHFDQUFxQ0MsTUFBTSxZQUFZMEQsS0FBSyxDQUFDYixHQUFHLElBQUlhLEtBQUssQ0FBQ1osR0FBRyxPQUFPLENBQUM7SUFDekgsQ0FBQztJQUFBO0lBQUE7TUFBQXhGLGFBQUEsR0FBQXNCLENBQUE7SUFBQTtJQUFBdEIsYUFBQSxHQUFBb0IsQ0FBQTtJQUVELElBQUk2QixlQUFlLEtBQUtaLGVBQWUsQ0FBQ2dFLE1BQU0sRUFBRTtNQUFBO01BQUFyRyxhQUFBLEdBQUFzQixDQUFBO01BQUF0QixhQUFBLEdBQUFvQixDQUFBO01BQzlDO01BQ0EsSUFBSXFCLFFBQVEsR0FBRyxHQUFHLEVBQUU7UUFBQTtRQUFBekMsYUFBQSxHQUFBc0IsQ0FBQTtRQUFBdEIsYUFBQSxHQUFBb0IsQ0FBQTtRQUNsQitCLFFBQVEsQ0FBQ1MsSUFBSSxDQUFDLDJEQUEyRCxDQUFDO01BQzVFLENBQUM7TUFBQTtNQUFBO1FBQUE1RCxhQUFBLEdBQUFzQixDQUFBO01BQUE7TUFBQXRCLGFBQUEsR0FBQW9CLENBQUE7TUFFRCxJQUFJcUIsUUFBUSxHQUFHLElBQUksRUFBRTtRQUFBO1FBQUF6QyxhQUFBLEdBQUFzQixDQUFBO1FBQUF0QixhQUFBLEdBQUFvQixDQUFBO1FBQ25CK0IsUUFBUSxDQUFDUyxJQUFJLENBQUMsNERBQTRELENBQUM7TUFDN0UsQ0FBQztNQUFBO01BQUE7UUFBQTVELGFBQUEsR0FBQXNCLENBQUE7TUFBQTtJQUNILENBQUM7SUFBQTtJQUFBO01BQUF0QixhQUFBLEdBQUFzQixDQUFBO0lBQUE7RUFDSDtFQUVBOzs7RUFHUSxPQUFPeUMsb0JBQW9CQSxDQUNqQ25CLGFBQTZCLEVBQzdCQyxVQUFtQixFQUNuQkMsWUFBMkIsRUFDM0JDLG9CQUE4QixFQUM5QkMseUJBQW1DO0lBQUE7SUFBQWhELGFBQUEsR0FBQXFCLENBQUE7SUFFbkMsTUFBTXlDLFdBQVc7SUFBQTtJQUFBLENBQUE5RCxhQUFBLEdBQUFvQixDQUFBLFFBQUc7TUFDbEJrRixXQUFXLEVBQUUsR0FBRztNQUNoQkMsUUFBUSxFQUFFLEdBQUc7TUFDYkMsUUFBUSxFQUFFLEdBQUc7TUFDYkMsUUFBUSxFQUFFLEdBQUc7TUFDYkMsVUFBVSxFQUFFLEdBQUc7TUFDZkMsZUFBZSxFQUFFLEdBQUc7TUFDcEJ4QyxRQUFRLEVBQUU7S0FDWDtJQUVEO0lBQUE7SUFBQW5FLGFBQUEsR0FBQW9CLENBQUE7SUFDQSxJQUFJeUIsVUFBVSxFQUFFO01BQUE7TUFBQTdDLGFBQUEsR0FBQXNCLENBQUE7TUFDZCxNQUFNa0QsWUFBWTtNQUFBO01BQUEsQ0FBQXhFLGFBQUEsR0FBQW9CLENBQUEsUUFBR3lCLFVBQVUsR0FBRyxJQUFJLENBQUNnQixvQkFBb0I7TUFBQztNQUFBN0QsYUFBQSxHQUFBb0IsQ0FBQTtNQUM1RDBDLFdBQVcsQ0FBQ3dDLFdBQVcsR0FBRzlCLFlBQVk7TUFBQztNQUFBeEUsYUFBQSxHQUFBb0IsQ0FBQTtNQUN2QzBDLFdBQVcsQ0FBQ3lDLFFBQVEsR0FBRy9CLFlBQVk7TUFBQztNQUFBeEUsYUFBQSxHQUFBb0IsQ0FBQTtNQUNwQzBDLFdBQVcsQ0FBQzBDLFFBQVEsR0FBR2hDLFlBQVk7TUFBQztNQUFBeEUsYUFBQSxHQUFBb0IsQ0FBQTtNQUNwQzBDLFdBQVcsQ0FBQzJDLFFBQVEsR0FBR2pDLFlBQVk7SUFDckMsQ0FBQztJQUFBO0lBQUE7TUFBQXhFLGFBQUEsR0FBQXNCLENBQUE7SUFBQTtJQUVEO0lBQUF0QixhQUFBLEdBQUFvQixDQUFBO0lBQ0E7SUFBSTtJQUFBLENBQUFwQixhQUFBLEdBQUFzQixDQUFBLFdBQUF5QixvQkFBb0I7SUFBQTtJQUFBLENBQUEvQyxhQUFBLEdBQUFzQixDQUFBLFdBQUl3QixZQUFZLEdBQUU7TUFBQTtNQUFBOUMsYUFBQSxHQUFBc0IsQ0FBQTtNQUFBdEIsYUFBQSxHQUFBb0IsQ0FBQTtNQUN4QztNQUFJO01BQUEsQ0FBQXBCLGFBQUEsR0FBQXNCLENBQUEsV0FBQXdCLFlBQVksQ0FBQzhELEtBQUssS0FBSyxhQUFhO01BQUE7TUFBQSxDQUFBNUcsYUFBQSxHQUFBc0IsQ0FBQSxXQUFJd0IsWUFBWSxDQUFDK0QsV0FBVztNQUFBO01BQUEsQ0FBQTdHLGFBQUEsR0FBQXNCLENBQUEsV0FBSXdCLFlBQVksQ0FBQytELFdBQVcsR0FBRyxDQUFDLEdBQUU7UUFBQTtRQUFBN0csYUFBQSxHQUFBc0IsQ0FBQTtRQUFBdEIsYUFBQSxHQUFBb0IsQ0FBQTtRQUNwRzBDLFdBQVcsQ0FBQzRDLFVBQVUsR0FBRyxJQUFJLENBQUMsQ0FBQztNQUNqQyxDQUFDO01BQUE7TUFBQTtRQUFBMUcsYUFBQSxHQUFBc0IsQ0FBQTtNQUFBO0lBQ0gsQ0FBQztJQUFBO0lBQUE7TUFBQXRCLGFBQUEsR0FBQXNCLENBQUE7SUFBQTtJQUVEO0lBQUF0QixhQUFBLEdBQUFvQixDQUFBO0lBQ0E7SUFBSTtJQUFBLENBQUFwQixhQUFBLEdBQUFzQixDQUFBLFdBQUEwQix5QkFBeUI7SUFBQTtJQUFBLENBQUFoRCxhQUFBLEdBQUFzQixDQUFBLFdBQUlzQixhQUFhLEdBQUU7TUFBQTtNQUFBNUMsYUFBQSxHQUFBc0IsQ0FBQTtNQUFBdEIsYUFBQSxHQUFBb0IsQ0FBQTtNQUM5QztNQUNBMEMsV0FBVyxDQUFDNkMsZUFBZSxHQUFHLEdBQUc7SUFDbkMsQ0FBQztJQUFBO0lBQUE7TUFBQTNHLGFBQUEsR0FBQXNCLENBQUE7SUFBQTtJQUVEO0lBQUF0QixhQUFBLEdBQUFvQixDQUFBO0lBQ0EwQyxXQUFXLENBQUNLLFFBQVEsR0FBR0wsV0FBVyxDQUFDd0MsV0FBVyxHQUFHeEMsV0FBVyxDQUFDeUMsUUFBUSxHQUMvQ3pDLFdBQVcsQ0FBQzBDLFFBQVEsR0FBRzFDLFdBQVcsQ0FBQzJDLFFBQVEsR0FDM0MzQyxXQUFXLENBQUM0QyxVQUFVLEdBQUc1QyxXQUFXLENBQUM2QyxlQUFlO0lBQUM7SUFBQTNHLGFBQUEsR0FBQW9CLENBQUE7SUFFM0UsT0FBTzBDLFdBQVc7RUFDcEI7RUFFQTs7O0VBR1EsT0FBT0csd0JBQXdCQSxDQUNyQ3ZCLE1BQThCLEVBQzlCRCxRQUFnQixFQUNoQlUsUUFBa0I7SUFBQTtJQUFBbkQsYUFBQSxHQUFBcUIsQ0FBQTtJQUFBckIsYUFBQSxHQUFBb0IsQ0FBQTtJQUVsQixRQUFRc0IsTUFBTTtNQUNaLEtBQUtQLHNCQUFzQixDQUFDMkUsT0FBTztRQUFBO1FBQUE5RyxhQUFBLEdBQUFzQixDQUFBO1FBQUF0QixhQUFBLEdBQUFvQixDQUFBO1FBQ2pDLE9BQU8sSUFBSSxDQUFDMkYsa0JBQWtCLENBQUN0RSxRQUFRLENBQUM7TUFFMUMsS0FBS04sc0JBQXNCLENBQUNtRCxZQUFZO1FBQUE7UUFBQXRGLGFBQUEsR0FBQXNCLENBQUE7UUFBQXRCLGFBQUEsR0FBQW9CLENBQUE7UUFDdEMsT0FBTyxJQUFJLENBQUM0RixzQkFBc0IsQ0FBQ3ZFLFFBQVEsRUFBRVUsUUFBUSxDQUFDO01BRXhELEtBQUtoQixzQkFBc0IsQ0FBQ3dELFlBQVk7UUFBQTtRQUFBM0YsYUFBQSxHQUFBc0IsQ0FBQTtRQUFBdEIsYUFBQSxHQUFBb0IsQ0FBQTtRQUN0QyxPQUFPLElBQUksQ0FBQzZGLHdCQUF3QixDQUFDeEUsUUFBUSxFQUFFVSxRQUFRLENBQUM7TUFFMUQsS0FBS2hCLHNCQUFzQixDQUFDUSxnQkFBZ0I7UUFBQTtRQUFBM0MsYUFBQSxHQUFBc0IsQ0FBQTtRQUFBdEIsYUFBQSxHQUFBb0IsQ0FBQTtRQUMxQyxPQUFPLElBQUksQ0FBQzhGLDBCQUEwQixDQUFDekUsUUFBUSxDQUFDO01BRWxELEtBQUtOLHNCQUFzQixDQUFDdUQsYUFBYTtRQUFBO1FBQUExRixhQUFBLEdBQUFzQixDQUFBO1FBQUF0QixhQUFBLEdBQUFvQixDQUFBO1FBQ3ZDLE9BQU8sSUFBSSxDQUFDK0Ysd0JBQXdCLENBQUMxRSxRQUFRLEVBQUVVLFFBQVEsQ0FBQztNQUUxRDtRQUFBO1FBQUFuRCxhQUFBLEdBQUFzQixDQUFBO1FBQUF0QixhQUFBLEdBQUFvQixDQUFBO1FBQ0UsTUFBTSxJQUFJOEUsS0FBSyxDQUFDLG1DQUFtQ3hELE1BQU0sRUFBRSxDQUFDO0lBQ2hFO0VBQ0Y7RUFFQTs7O0VBR1EsT0FBT3FFLGtCQUFrQkEsQ0FBQ3RFLFFBQWdCO0lBQUE7SUFBQXpDLGFBQUEsR0FBQXFCLENBQUE7SUFBQXJCLGFBQUEsR0FBQW9CLENBQUE7SUFDaEQsT0FBTzJFLElBQUksQ0FBQ3FCLEdBQUcsQ0FBQzNFLFFBQVEsR0FBRyxJQUFJLENBQUNxRCwwQkFBMEIsRUFBRSxDQUFDLENBQUM7RUFDaEU7RUFFQTs7O0VBR1EsT0FBT2tCLHNCQUFzQkEsQ0FBQ3ZFLFFBQWdCLEVBQUVVLFFBQWtCO0lBQUE7SUFBQW5ELGFBQUEsR0FBQXFCLENBQUE7SUFBQXJCLGFBQUEsR0FBQW9CLENBQUE7SUFDeEUsSUFBSTtNQUNGLE1BQU1pRyxRQUFRO01BQUE7TUFBQSxDQUFBckgsYUFBQSxHQUFBb0IsQ0FBQSxRQUFHYSx5QkFBQSxDQUFBd0IsdUJBQXVCLENBQUNsQix5QkFBeUIsQ0FBQztRQUNqRUUsUUFBUTtRQUNSNkUsUUFBUSxFQUFFO09BQ1gsQ0FBQztNQUFDO01BQUF0SCxhQUFBLEdBQUFvQixDQUFBO01BQ0grQixRQUFRLENBQUNTLElBQUksQ0FBQyxHQUFHeUQsUUFBUSxDQUFDbEUsUUFBUSxDQUFDO01BQUM7TUFBQW5ELGFBQUEsR0FBQW9CLENBQUE7TUFDcEMsT0FBT2lHLFFBQVEsQ0FBQzlDLGdCQUFnQjtJQUNsQyxDQUFDLENBQUMsT0FBT2dELEtBQUssRUFBRTtNQUFBO01BQUF2SCxhQUFBLEdBQUFvQixDQUFBO01BQ2QrQixRQUFRLENBQUNTLElBQUksQ0FBQywwREFBMEQsQ0FBQztNQUFDO01BQUE1RCxhQUFBLEdBQUFvQixDQUFBO01BQzFFLE9BQU8sSUFBSSxDQUFDMkYsa0JBQWtCLENBQUN0RSxRQUFRLENBQUM7SUFDMUM7RUFDRjtFQUVBOzs7RUFHUSxPQUFPd0Usd0JBQXdCQSxDQUFDeEUsUUFBZ0IsRUFBRVUsUUFBa0I7SUFBQTtJQUFBbkQsYUFBQSxHQUFBcUIsQ0FBQTtJQUFBckIsYUFBQSxHQUFBb0IsQ0FBQTtJQUMxRTtJQUNBLE9BQU8sSUFBSSxDQUFDNEYsc0JBQXNCLENBQUN2RSxRQUFRLEVBQUVVLFFBQVEsQ0FBQztFQUN4RDtFQUVBOzs7RUFHUSxPQUFPK0QsMEJBQTBCQSxDQUFDekUsUUFBZ0I7SUFBQTtJQUFBekMsYUFBQSxHQUFBcUIsQ0FBQTtJQUN4RDtJQUNBLE1BQU1tRyxNQUFNO0lBQUE7SUFBQSxDQUFBeEgsYUFBQSxHQUFBb0IsQ0FBQSxTQUFHMkUsSUFBSSxDQUFDcUIsR0FBRyxDQUFDM0UsUUFBUSxHQUFHLElBQUksQ0FBQ3FELDBCQUEwQixFQUFFLENBQUMsQ0FBQztJQUV0RTtJQUNBLE1BQU0yQixrQkFBa0I7SUFBQTtJQUFBLENBQUF6SCxhQUFBLEdBQUFvQixDQUFBLFNBQUcsQ0FBQyxHQUFHLENBQUNxQixRQUFRLEdBQUcsSUFBSSxJQUFJLFFBQVEsRUFBQyxDQUFDO0lBQUE7SUFBQXpDLGFBQUEsR0FBQW9CLENBQUE7SUFFN0QsT0FBT29HLE1BQU0sR0FBR3pCLElBQUksQ0FBQ1AsR0FBRyxDQUFDLElBQUksRUFBRU8sSUFBSSxDQUFDUixHQUFHLENBQUMsSUFBSSxFQUFFa0Msa0JBQWtCLENBQUMsQ0FBQztFQUNwRTtFQUVBOzs7RUFHUSxPQUFPTix3QkFBd0JBLENBQUMxRSxRQUFnQixFQUFFVSxRQUFrQjtJQUFBO0lBQUFuRCxhQUFBLEdBQUFxQixDQUFBO0lBQzFFO0lBQ0EsTUFBTW1HLE1BQU07SUFBQTtJQUFBLENBQUF4SCxhQUFBLEdBQUFvQixDQUFBLFNBQUcsSUFBSSxDQUFDMkYsa0JBQWtCLENBQUN0RSxRQUFRLENBQUM7SUFFaEQ7SUFDQSxJQUFJaUYsYUFBYTtJQUFBO0lBQUEsQ0FBQTFILGFBQUEsR0FBQW9CLENBQUEsU0FBRyxHQUFHO0lBQUM7SUFBQXBCLGFBQUEsR0FBQW9CLENBQUE7SUFFeEIsSUFBSXFCLFFBQVEsR0FBRyxJQUFJLEVBQUU7TUFBQTtNQUFBekMsYUFBQSxHQUFBc0IsQ0FBQTtNQUFBdEIsYUFBQSxHQUFBb0IsQ0FBQTtNQUNuQnNHLGFBQWEsR0FBRyxJQUFJLENBQUMsQ0FBQztJQUN4QixDQUFDLE1BQU07TUFBQTtNQUFBMUgsYUFBQSxHQUFBc0IsQ0FBQTtNQUFBdEIsYUFBQSxHQUFBb0IsQ0FBQTtNQUFBLElBQUlxQixRQUFRLEdBQUcsSUFBSSxFQUFFO1FBQUE7UUFBQXpDLGFBQUEsR0FBQXNCLENBQUE7UUFBQXRCLGFBQUEsR0FBQW9CLENBQUE7UUFDMUJzRyxhQUFhLEdBQUcsSUFBSSxDQUFDLENBQUM7TUFDeEIsQ0FBQztNQUFBO01BQUE7UUFBQTFILGFBQUEsR0FBQXNCLENBQUE7TUFBQTtJQUFEO0lBQUM7SUFBQXRCLGFBQUEsR0FBQW9CLENBQUE7SUFFRCtCLFFBQVEsQ0FBQ1MsSUFBSSxDQUFDLDJFQUEyRSxDQUFDO0lBQUM7SUFBQTVELGFBQUEsR0FBQW9CLENBQUE7SUFFM0YsT0FBT29HLE1BQU0sR0FBR0UsYUFBYTtFQUMvQjtFQUVBOzs7RUFHUSxPQUFPckQsMEJBQTBCQSxDQUN2Q0UsZ0JBQXdCLEVBQ3hCN0IsTUFBOEIsRUFDOUJELFFBQWdCLEVBQ2hCcUIsV0FBa0Q7SUFBQTtJQUFBOUQsYUFBQSxHQUFBcUIsQ0FBQTtJQUVsRCxNQUFNc0csWUFBWTtJQUFBO0lBQUEsQ0FBQTNILGFBQUEsR0FBQW9CLENBQUEsU0FBRyxJQUFJLENBQUNzRCxlQUFlLENBQUNoQyxNQUFNLENBQUM7SUFFakQ7SUFDQSxJQUFJa0YsZ0JBQWdCO0lBQUE7SUFBQSxDQUFBNUgsYUFBQSxHQUFBb0IsQ0FBQSxTQUFHdUcsWUFBWTtJQUFDO0lBQUEzSCxhQUFBLEdBQUFvQixDQUFBO0lBRXBDO0lBQUk7SUFBQSxDQUFBcEIsYUFBQSxHQUFBc0IsQ0FBQSxXQUFBbUIsUUFBUSxHQUFHLEdBQUc7SUFBQTtJQUFBLENBQUF6QyxhQUFBLEdBQUFzQixDQUFBLFdBQUltQixRQUFRLEdBQUcsSUFBSSxHQUFFO01BQUE7TUFBQXpDLGFBQUEsR0FBQXNCLENBQUE7TUFBQXRCLGFBQUEsR0FBQW9CLENBQUE7TUFDckN3RyxnQkFBZ0IsSUFBSSxJQUFJLENBQUMsQ0FBQztJQUM1QixDQUFDO0lBQUE7SUFBQTtNQUFBNUgsYUFBQSxHQUFBc0IsQ0FBQTtJQUFBO0lBQUF0QixhQUFBLEdBQUFvQixDQUFBO0lBRUQsSUFBSTJFLElBQUksQ0FBQzhCLEdBQUcsQ0FBQy9ELFdBQVcsQ0FBQ0ssUUFBUSxHQUFHLEdBQUcsQ0FBQyxHQUFHLEdBQUcsRUFBRTtNQUFBO01BQUFuRSxhQUFBLEdBQUFzQixDQUFBO01BQUF0QixhQUFBLEdBQUFvQixDQUFBO01BQzlDd0csZ0JBQWdCLElBQUksSUFBSSxDQUFDLENBQUM7SUFDNUIsQ0FBQztJQUFBO0lBQUE7TUFBQTVILGFBQUEsR0FBQXNCLENBQUE7SUFBQTtJQUVELE1BQU13RyxXQUFXO0lBQUE7SUFBQSxDQUFBOUgsYUFBQSxHQUFBb0IsQ0FBQSxTQUFHbUQsZ0JBQWdCLElBQUksQ0FBQyxHQUFHcUQsZ0JBQWdCLENBQUM7SUFBQztJQUFBNUgsYUFBQSxHQUFBb0IsQ0FBQTtJQUU5RCxPQUFPO01BQ0wyRyxLQUFLLEVBQUV4RCxnQkFBZ0IsR0FBR3VELFdBQVc7TUFDckNFLEtBQUssRUFBRXpELGdCQUFnQixHQUFHdUQsV0FBVztNQUNyQ0csZUFBZSxFQUFFTDtLQUNsQjtFQUNIO0VBRUE7OztFQUdRLE9BQU90RCx1QkFBdUJBLENBQ3BDN0IsUUFBZ0IsRUFDaEJDLE1BQThCLEVBQzlCb0IsV0FBa0QsRUFDbERWLGVBQXlCO0lBQUE7SUFBQXBELGFBQUEsR0FBQXFCLENBQUE7SUFBQXJCLGFBQUEsR0FBQW9CLENBQUE7SUFFekI7SUFDQSxJQUFJcUIsUUFBUSxHQUFHLEdBQUcsRUFBRTtNQUFBO01BQUF6QyxhQUFBLEdBQUFzQixDQUFBO01BQUF0QixhQUFBLEdBQUFvQixDQUFBO01BQ2xCZ0MsZUFBZSxDQUFDUSxJQUFJLENBQUMseUVBQXlFLENBQUM7SUFDakcsQ0FBQyxNQUFNO01BQUE7TUFBQTVELGFBQUEsR0FBQXNCLENBQUE7TUFBQXRCLGFBQUEsR0FBQW9CLENBQUE7TUFBQSxJQUFJcUIsUUFBUSxHQUFHLElBQUksRUFBRTtRQUFBO1FBQUF6QyxhQUFBLEdBQUFzQixDQUFBO1FBQUF0QixhQUFBLEdBQUFvQixDQUFBO1FBQzFCZ0MsZUFBZSxDQUFDUSxJQUFJLENBQUMsa0VBQWtFLENBQUM7TUFDMUYsQ0FBQztNQUFBO01BQUE7UUFBQTVELGFBQUEsR0FBQXNCLENBQUE7TUFBQTtJQUFEO0lBRUE7SUFBQTtJQUFBdEIsYUFBQSxHQUFBb0IsQ0FBQTtJQUNBO0lBQUk7SUFBQSxDQUFBcEIsYUFBQSxHQUFBc0IsQ0FBQSxXQUFBb0IsTUFBTSxLQUFLUCxzQkFBc0IsQ0FBQzJFLE9BQU87SUFBQTtJQUFBLENBQUE5RyxhQUFBLEdBQUFzQixDQUFBLFdBQUltQixRQUFRLElBQUksR0FBRztJQUFBO0lBQUEsQ0FBQXpDLGFBQUEsR0FBQXNCLENBQUEsV0FBSW1CLFFBQVEsSUFBSSxJQUFJLEdBQUU7TUFBQTtNQUFBekMsYUFBQSxHQUFBc0IsQ0FBQTtNQUFBdEIsYUFBQSxHQUFBb0IsQ0FBQTtNQUNwRmdDLGVBQWUsQ0FBQ1EsSUFBSSxDQUFDLGlGQUFpRixDQUFDO0lBQ3pHLENBQUM7SUFBQTtJQUFBO01BQUE1RCxhQUFBLEdBQUFzQixDQUFBO0lBQUE7SUFFRDtJQUFBdEIsYUFBQSxHQUFBb0IsQ0FBQTtJQUNBLElBQUkyRSxJQUFJLENBQUM4QixHQUFHLENBQUMvRCxXQUFXLENBQUNLLFFBQVEsR0FBRyxHQUFHLENBQUMsR0FBRyxJQUFJLEVBQUU7TUFBQTtNQUFBbkUsYUFBQSxHQUFBc0IsQ0FBQTtNQUFBdEIsYUFBQSxHQUFBb0IsQ0FBQTtNQUMvQ2dDLGVBQWUsQ0FBQ1EsSUFBSSxDQUFDLHVFQUF1RSxDQUFDO0lBQy9GLENBQUM7SUFBQTtJQUFBO01BQUE1RCxhQUFBLEdBQUFzQixDQUFBO0lBQUE7RUFDSDtFQUVBOzs7RUFHUSxPQUFPdUQscUJBQXFCQSxDQUFDbkMsTUFBOEI7SUFBQTtJQUFBMUMsYUFBQSxHQUFBcUIsQ0FBQTtJQUFBckIsYUFBQSxHQUFBb0IsQ0FBQTtJQUNqRSxRQUFRc0IsTUFBTTtNQUNaLEtBQUtQLHNCQUFzQixDQUFDMkUsT0FBTztRQUFBO1FBQUE5RyxhQUFBLEdBQUFzQixDQUFBO1FBQUF0QixhQUFBLEdBQUFvQixDQUFBO1FBQ2pDLE9BQU8sZ0JBQWdCO01BQ3pCLEtBQUtlLHNCQUFzQixDQUFDbUQsWUFBWTtRQUFBO1FBQUF0RixhQUFBLEdBQUFzQixDQUFBO1FBQUF0QixhQUFBLEdBQUFvQixDQUFBO1FBQ3RDLE9BQU8sZ0NBQWdDO01BQ3pDLEtBQUtlLHNCQUFzQixDQUFDd0QsWUFBWTtRQUFBO1FBQUEzRixhQUFBLEdBQUFzQixDQUFBO1FBQUF0QixhQUFBLEdBQUFvQixDQUFBO1FBQ3RDLE9BQU8sd0NBQXdDO01BQ2pELEtBQUtlLHNCQUFzQixDQUFDUSxnQkFBZ0I7UUFBQTtRQUFBM0MsYUFBQSxHQUFBc0IsQ0FBQTtRQUFBdEIsYUFBQSxHQUFBb0IsQ0FBQTtRQUMxQyxPQUFPLG9EQUFvRDtNQUM3RCxLQUFLZSxzQkFBc0IsQ0FBQ3VELGFBQWE7UUFBQTtRQUFBMUYsYUFBQSxHQUFBc0IsQ0FBQTtRQUFBdEIsYUFBQSxHQUFBb0IsQ0FBQTtRQUN2QyxPQUFPLDZDQUE2QztNQUN0RDtRQUFBO1FBQUFwQixhQUFBLEdBQUFzQixDQUFBO1FBQUF0QixhQUFBLEdBQUFvQixDQUFBO1FBQ0UsT0FBTyxnQkFBZ0I7SUFDM0I7RUFDRjtFQUVBOzs7RUFHUSxPQUFPOEQsb0JBQW9CQSxDQUFDeEMsTUFBOEI7SUFBQTtJQUFBMUMsYUFBQSxHQUFBcUIsQ0FBQTtJQUFBckIsYUFBQSxHQUFBb0IsQ0FBQTtJQUNoRSxRQUFRc0IsTUFBTTtNQUNaLEtBQUtQLHNCQUFzQixDQUFDMkUsT0FBTztRQUFBO1FBQUE5RyxhQUFBLEdBQUFzQixDQUFBO01BQ25DLEtBQUthLHNCQUFzQixDQUFDUSxnQkFBZ0I7UUFBQTtRQUFBM0MsYUFBQSxHQUFBc0IsQ0FBQTtRQUFBdEIsYUFBQSxHQUFBb0IsQ0FBQTtRQUMxQyxPQUFPLGlDQUFpQztNQUMxQyxLQUFLZSxzQkFBc0IsQ0FBQ21ELFlBQVk7UUFBQTtRQUFBdEYsYUFBQSxHQUFBc0IsQ0FBQTtNQUN4QyxLQUFLYSxzQkFBc0IsQ0FBQ3dELFlBQVk7UUFBQTtRQUFBM0YsYUFBQSxHQUFBc0IsQ0FBQTtRQUFBdEIsYUFBQSxHQUFBb0IsQ0FBQTtRQUN0QyxPQUFPLGlDQUFpQztNQUMxQyxLQUFLZSxzQkFBc0IsQ0FBQ3VELGFBQWE7UUFBQTtRQUFBMUYsYUFBQSxHQUFBc0IsQ0FBQTtRQUFBdEIsYUFBQSxHQUFBb0IsQ0FBQTtRQUN2QyxPQUFPLHNDQUFzQztNQUMvQztRQUFBO1FBQUFwQixhQUFBLEdBQUFzQixDQUFBO1FBQUF0QixhQUFBLEdBQUFvQixDQUFBO1FBQ0UsT0FBTyxzQkFBc0I7SUFDakM7RUFDRjs7OztBQXZjRmdCLE9BQUEsQ0FBQUUsMEJBQUEsR0FBQUEsMEJBQUE7QUF3Y0M7QUFBQXRDLGFBQUEsR0FBQW9CLENBQUE7QUF2Y3lCa0IsMEJBQUEsQ0FBQTRGLE9BQU8sR0FBRyxPQUFPO0FBQUM7QUFBQWxJLGFBQUEsR0FBQW9CLENBQUE7QUFDbEJrQiwwQkFBQSxDQUFBdUIsb0JBQW9CLEdBQUcsS0FBSyxDQUFDLENBQUM7QUFBQTtBQUFBN0QsYUFBQSxHQUFBb0IsQ0FBQTtBQUM5QmtCLDBCQUFBLENBQUF3RCwwQkFBMEIsR0FBRyxJQUFJLENBQUMsQ0FBQztBQUUzRDtBQUFBO0FBQUE5RixhQUFBLEdBQUFvQixDQUFBO0FBQ3dCa0IsMEJBQUEsQ0FBQStDLGVBQWUsR0FBRztFQUN4Q3lCLE9BQU8sRUFBRTtJQUFFdkIsR0FBRyxFQUFFLENBQUM7SUFBRUMsR0FBRyxFQUFFO0VBQUssQ0FBRTtFQUMvQkYsWUFBWSxFQUFFO0lBQUVDLEdBQUcsRUFBRSxHQUFHO0lBQUVDLEdBQUcsRUFBRTtFQUFJLENBQUU7RUFDckNHLFlBQVksRUFBRTtJQUFFSixHQUFHLEVBQUUsRUFBRTtJQUFFQyxHQUFHLEVBQUU7RUFBSSxDQUFFO0VBQ3BDN0MsZ0JBQWdCLEVBQUU7SUFBRTRDLEdBQUcsRUFBRSxDQUFDO0lBQUVDLEdBQUcsRUFBRTtFQUFLLENBQUU7RUFDeENFLGFBQWEsRUFBRTtJQUFFSCxHQUFHLEVBQUUsR0FBRztJQUFFQyxHQUFHLEVBQUU7RUFBSTtDQUNyQztBQUVEO0FBQUE7QUFBQXhGLGFBQUEsR0FBQW9CLENBQUE7QUFDd0JrQiwwQkFBQSxDQUFBb0MsZUFBZSxHQUFHO0VBQ3hDLENBQUN2QyxzQkFBc0IsQ0FBQzJFLE9BQU8sR0FBRyxJQUFJO0VBQ3RDLENBQUMzRSxzQkFBc0IsQ0FBQ21ELFlBQVksR0FBRyxJQUFJO0VBQzNDLENBQUNuRCxzQkFBc0IsQ0FBQ3dELFlBQVksR0FBRyxJQUFJO0VBQzNDLENBQUN4RCxzQkFBc0IsQ0FBQ1EsZ0JBQWdCLEdBQUcsSUFBSTtFQUMvQyxDQUFDUixzQkFBc0IsQ0FBQ3VELGFBQWEsR0FBRztDQUN6QyIsImlnbm9yZUxpc3QiOltdfQ==