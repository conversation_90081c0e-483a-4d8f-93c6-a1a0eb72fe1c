{"version": 3, "names": ["cov_11pqkh4977", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "name", "decl", "loc", "branchMap", "type", "locations", "undefined", "s", "f", "b", "inputSourceMap", "file", "mappings", "names", "sources", "sourcesContent", "version", "_coverageSchema", "coverage", "actualCoverage", "exports", "getAIOptimizationService", "useAIOptimization", "react_1", "require", "ort", "__importStar", "AIOptimizationService", "constructor", "config", "session", "modelCache", "Map", "isInitialized", "enableGPU", "batchSize", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "optimizationGoals", "weight", "initialize", "env", "wasm", "numThreads", "simd", "InferenceSession", "create", "modelPath", "loadSpecializedModels", "console", "log", "error", "optimizeSystem", "input", "Error", "modelInput", "prepareModelInput", "results", "run", "optimizationResult", "processResults", "validatedResult", "validateRecommendations", "predictEnergyConsumption", "hvacSystem", "environmentalData", "timeHorizon", "energyModel", "get", "prepareEnergyInput", "Array", "from", "energy_consumption", "data", "detectAnomalies", "performanceData", "threshold", "anomalyModel", "prepareAnomalyInput", "anomalyScores", "anomaly_scores", "confidenceScores", "confidence", "anomalies", "map", "score", "index", "filter", "recommendEquipmentSizing", "buildingData", "loadRequirements", "sizingModel", "prepareSizingInput", "equipment", "decodeEquipmentTypes", "equipment_types", "sizes", "equipment_sizes", "efficiency_ratings", "cost_estimates", "analyzeEnvironmentalImpact", "energyConsumption", "carbonIntensity", "totalEnergy", "reduce", "sum", "consumption", "carbonFootprint", "renewablePotential", "assessRenewablePotential", "sustainabilityScore", "calculateSustainabilityScore", "recommendations", "generateEnvironmentalRecommendations", "carbon_footprint", "renewable_potential", "sustainability_score", "getOptimizationInsights", "result", "insights", "priorityActions", "potentialIssues", "predicted_savings", "energy", "push", "toFixed", "cost", "toLocaleString", "highPrioritySteps", "implementation_plan", "step", "priority", "sort", "a", "expected_impact", "slice", "description", "highRiskFactors", "risk_assessment", "risk", "probability", "impact", "key_insights", "priority_actions", "potential_issues", "models", "model", "set", "warn", "features", "area", "volume", "occupancy", "insulation", "floors", "zoneCount", "outdoorTemperature", "t", "humidity", "h", "operationalData", "currentLoad", "performanceMetrics", "cop", "eer", "capacity_utilization", "constraints", "budget", "timeline", "inputSize", "paddedFeatures", "length", "Tensor", "Float32Array", "outputData", "output", "extractRecommendations", "savings", "calculateSavings", "implementationPlan", "generateImplementationPlan", "roiAnalysis", "calculateROI", "riskAssessment", "assessRisks", "confidence_score", "Math", "min", "roi_analysis", "id", "implementation_cost", "annual_savings", "payback_period", "impact_areas", "currentEnergyCost", "e", "emissions", "rec", "estimated_cost", "estimated_duration", "dependencies", "plan", "totalInvestment", "annualSavings", "initial_investment", "net_present_value", "calculateNPV", "internal_rate_of_return", "calculateIRR", "risks", "totalCost", "mitigation", "totalDuration", "flatMap", "temperature_variance", "equipmentTypes", "floor", "random", "efficiencyScore", "efficiency", "carbonScore", "max", "renewableScore", "validatedRecommendations", "investment", "discountRate", "years", "npv", "year", "pow", "dispose", "release", "clear", "aiOptimizationService", "default", "service", "setService", "useState", "setIsInitialized", "isLoading", "setIsLoading", "setError", "useEffect", "aiService", "then", "catch", "err", "message", "useCallback", "errorMessage", "predictEnergy"], "sources": ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\frontend\\lib\\services\\AIOptimizationService.ts"], "sourcesContent": ["/**\r\n * AI-Powered HVAC Optimization Service\r\n * \r\n * Integrates ONNX.js machine learning models for intelligent HVAC system\r\n * optimization and energy efficiency recommendations.\r\n * \r\n * Features:\r\n * - Energy efficiency optimization\r\n * - Load prediction and balancing\r\n * - Equipment sizing recommendations\r\n * - Performance anomaly detection\r\n * - Cost optimization analysis\r\n * - Environmental impact assessment\r\n */\r\n\r\nimport { useState, useEffect, useCallback } from 'react';\r\nimport * as ort from 'onnxruntime-web';\r\nimport { CalculationResult } from '../../types/air-duct-sizer';\r\n\r\n// Mock types for AI optimization\r\nexport interface HVACSystem {\r\n  id: string;\r\n  type: string;\r\n  efficiency?: number;\r\n  capacity?: number;\r\n  age?: number;\r\n  maintenance_schedule?: string[];\r\n}\r\n\r\nexport interface OptimizationRecommendation {\r\n  id: string;\r\n  type: 'equipment_upgrade' | 'schedule_optimization' | 'maintenance' | 'control_strategy';\r\n  description: string;\r\n  priority: 'low' | 'medium' | 'high';\r\n  implementation_cost: number;\r\n  annual_savings: number;\r\n  payback_period: number;\r\n  confidence: number;\r\n  impact_areas: string[];\r\n}\r\n\r\nexport interface AIOptimizationConfig {\r\n  modelPath: string;\r\n  enableGPU?: boolean;\r\n  batchSize?: number;\r\n  confidenceThreshold?: number;\r\n  optimizationGoals?: OptimizationGoal[];\r\n}\r\n\r\nexport interface OptimizationGoal {\r\n  type: 'energy_efficiency' | 'cost_reduction' | 'comfort' | 'environmental';\r\n  weight: number; // 0-1\r\n  target?: number;\r\n}\r\n\r\nexport interface OptimizationInput {\r\n  hvacSystem: HVACSystem;\r\n  buildingData: BuildingData;\r\n  environmentalData: EnvironmentalData;\r\n  operationalData: OperationalData;\r\n  constraints: OptimizationConstraints;\r\n}\r\n\r\nexport interface BuildingData {\r\n  area: number;\r\n  volume: number;\r\n  occupancy: number;\r\n  insulation: number; // R-value\r\n  windows: WindowData[];\r\n  orientation: string;\r\n  floors: number;\r\n  zoneCount: number;\r\n}\r\n\r\nexport interface WindowData {\r\n  area: number;\r\n  uValue: number;\r\n  orientation: string;\r\n  shading: number;\r\n}\r\n\r\nexport interface EnvironmentalData {\r\n  outdoorTemperature: number[];\r\n  humidity: number[];\r\n  solarRadiation: number[];\r\n  windSpeed: number[];\r\n  season: 'spring' | 'summer' | 'fall' | 'winter';\r\n  climate: string;\r\n}\r\n\r\nexport interface OperationalData {\r\n  currentLoad: number;\r\n  energyConsumption: number[];\r\n  operatingHours: number;\r\n  maintenanceHistory: MaintenanceRecord[];\r\n  performanceMetrics: PerformanceMetrics;\r\n}\r\n\r\nexport interface MaintenanceRecord {\r\n  date: Date;\r\n  type: string;\r\n  cost: number;\r\n  efficiency_impact: number;\r\n}\r\n\r\nexport interface PerformanceMetrics {\r\n  cop: number; // Coefficient of Performance\r\n  eer: number; // Energy Efficiency Ratio\r\n  capacity_utilization: number;\r\n  temperature_variance: number;\r\n}\r\n\r\nexport interface OptimizationConstraints {\r\n  budget: number;\r\n  timeline: number; // days\r\n  comfort_requirements: ComfortRequirements;\r\n  regulatory_requirements: string[];\r\n  existing_equipment: string[];\r\n}\r\n\r\nexport interface ComfortRequirements {\r\n  temperature_range: [number, number];\r\n  humidity_range: [number, number];\r\n  air_quality_min: number;\r\n  noise_max: number;\r\n}\r\n\r\nexport interface OptimizationResult {\r\n  recommendations: OptimizationRecommendation[];\r\n  predicted_savings: {\r\n    energy: number; // percentage\r\n    cost: number; // annual dollars\r\n    emissions: number; // kg CO2 equivalent\r\n  };\r\n  confidence_score: number;\r\n  implementation_plan: ImplementationStep[];\r\n  roi_analysis: ROIAnalysis;\r\n  risk_assessment: RiskFactor[];\r\n}\r\n\r\nexport interface ImplementationStep {\r\n  id: string;\r\n  description: string;\r\n  priority: 'high' | 'medium' | 'low';\r\n  estimated_cost: number;\r\n  estimated_duration: number; // days\r\n  dependencies: string[];\r\n  expected_impact: number; // percentage improvement\r\n}\r\n\r\nexport interface ROIAnalysis {\r\n  initial_investment: number;\r\n  annual_savings: number;\r\n  payback_period: number; // years\r\n  net_present_value: number;\r\n  internal_rate_of_return: number;\r\n}\r\n\r\nexport interface RiskFactor {\r\n  type: string;\r\n  description: string;\r\n  probability: number; // 0-1\r\n  impact: number; // 0-1\r\n  mitigation: string;\r\n}\r\n\r\nexport class AIOptimizationService {\r\n  private session: ort.InferenceSession | null = null;\r\n  private config: AIOptimizationConfig;\r\n  private modelCache: Map<string, ort.InferenceSession> = new Map();\r\n  private isInitialized = false;\r\n\r\n  constructor(config: AIOptimizationConfig) {\r\n    this.config = {\r\n      enableGPU: false,\r\n      batchSize: 1,\r\n      confidenceThreshold: 0.7,\r\n      optimizationGoals: [\r\n        { type: 'energy_efficiency', weight: 0.4 },\r\n        { type: 'cost_reduction', weight: 0.3 },\r\n        { type: 'comfort', weight: 0.2 },\r\n        { type: 'environmental', weight: 0.1 }\r\n      ],\r\n      ...config\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Initialize the AI optimization service\r\n   */\r\n  async initialize(): Promise<void> {\r\n    try {\r\n      // Configure ONNX runtime\r\n      if (this.config.enableGPU) {\r\n        ort.env.wasm.numThreads = 4;\r\n        ort.env.wasm.simd = true;\r\n      }\r\n\r\n      // Load main optimization model\r\n      this.session = await ort.InferenceSession.create(this.config.modelPath);\r\n      \r\n      // Load specialized models\r\n      await this.loadSpecializedModels();\r\n      \r\n      this.isInitialized = true;\r\n      console.log('AI Optimization Service initialized successfully');\r\n      \r\n    } catch (error) {\r\n      console.error('Failed to initialize AI Optimization Service:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Optimize HVAC system configuration\r\n   */\r\n  async optimizeSystem(input: OptimizationInput): Promise<OptimizationResult> {\r\n    if (!this.isInitialized || !this.session) {\r\n      throw new Error('AI Optimization Service not initialized');\r\n    }\r\n\r\n    try {\r\n      // Prepare input data\r\n      const modelInput = await this.prepareModelInput(input);\r\n      \r\n      // Run inference\r\n      const results = await this.session.run(modelInput);\r\n      \r\n      // Process results\r\n      const optimizationResult = await this.processResults(results, input);\r\n      \r\n      // Validate recommendations\r\n      const validatedResult = await this.validateRecommendations(optimizationResult, input);\r\n      \r\n      return validatedResult;\r\n      \r\n    } catch (error) {\r\n      console.error('Optimization failed:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Predict energy consumption\r\n   */\r\n  async predictEnergyConsumption(\r\n    hvacSystem: HVACSystem,\r\n    environmentalData: EnvironmentalData,\r\n    timeHorizon: number = 24 // hours\r\n  ): Promise<number[]> {\r\n    const energyModel = this.modelCache.get('energy_prediction');\r\n    if (!energyModel) {\r\n      throw new Error('Energy prediction model not loaded');\r\n    }\r\n\r\n    const input = this.prepareEnergyInput(hvacSystem, environmentalData, timeHorizon);\r\n    const results = await energyModel.run(input);\r\n    \r\n    return Array.from(results.energy_consumption.data as Float32Array);\r\n  }\r\n\r\n  /**\r\n   * Detect performance anomalies\r\n   */\r\n  async detectAnomalies(\r\n    performanceData: PerformanceMetrics[],\r\n    threshold: number = 0.8\r\n  ): Promise<{ anomalies: number[]; confidence: number[] }> {\r\n    const anomalyModel = this.modelCache.get('anomaly_detection');\r\n    if (!anomalyModel) {\r\n      throw new Error('Anomaly detection model not loaded');\r\n    }\r\n\r\n    const input = this.prepareAnomalyInput(performanceData);\r\n    const results = await anomalyModel.run(input);\r\n    \r\n    const anomalyScores = Array.from(results.anomaly_scores.data as Float32Array);\r\n    const confidenceScores = Array.from(results.confidence.data as Float32Array);\r\n    \r\n    const anomalies = anomalyScores.map((score, index) => \r\n      score > threshold ? index : -1\r\n    ).filter(index => index !== -1);\r\n    \r\n    return {\r\n      anomalies,\r\n      confidence: anomalies.map(index => confidenceScores[index])\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Generate equipment sizing recommendations\r\n   */\r\n  async recommendEquipmentSizing(\r\n    buildingData: BuildingData,\r\n    loadRequirements: number[]\r\n  ): Promise<{\r\n    equipment: string[];\r\n    sizes: number[];\r\n    efficiency_ratings: number[];\r\n    cost_estimates: number[];\r\n  }> {\r\n    const sizingModel = this.modelCache.get('equipment_sizing');\r\n    if (!sizingModel) {\r\n      throw new Error('Equipment sizing model not loaded');\r\n    }\r\n\r\n    const input = this.prepareSizingInput(buildingData, loadRequirements);\r\n    const results = await sizingModel.run(input);\r\n    \r\n    return {\r\n      equipment: this.decodeEquipmentTypes(results.equipment_types.data as Float32Array),\r\n      sizes: Array.from(results.equipment_sizes.data as Float32Array),\r\n      efficiency_ratings: Array.from(results.efficiency_ratings.data as Float32Array),\r\n      cost_estimates: Array.from(results.cost_estimates.data as Float32Array)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Analyze environmental impact\r\n   */\r\n  async analyzeEnvironmentalImpact(\r\n    hvacSystem: HVACSystem,\r\n    energyConsumption: number[]\r\n  ): Promise<{\r\n    carbon_footprint: number;\r\n    renewable_potential: number;\r\n    sustainability_score: number;\r\n    recommendations: string[];\r\n  }> {\r\n    // Calculate carbon footprint\r\n    const carbonIntensity = 0.4; // kg CO2/kWh (average grid)\r\n    const totalEnergy = energyConsumption.reduce((sum, consumption) => sum + consumption, 0);\r\n    const carbonFootprint = totalEnergy * carbonIntensity;\r\n    \r\n    // Assess renewable potential\r\n    const renewablePotential = await this.assessRenewablePotential(hvacSystem);\r\n    \r\n    // Calculate sustainability score\r\n    const sustainabilityScore = this.calculateSustainabilityScore(\r\n      hvacSystem,\r\n      carbonFootprint,\r\n      renewablePotential\r\n    );\r\n    \r\n    // Generate recommendations\r\n    const recommendations = this.generateEnvironmentalRecommendations(\r\n      sustainabilityScore,\r\n      renewablePotential\r\n    );\r\n    \r\n    return {\r\n      carbon_footprint: carbonFootprint,\r\n      renewable_potential: renewablePotential,\r\n      sustainability_score: sustainabilityScore,\r\n      recommendations\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get optimization insights\r\n   */\r\n  getOptimizationInsights(result: OptimizationResult): {\r\n    key_insights: string[];\r\n    priority_actions: string[];\r\n    potential_issues: string[];\r\n  } {\r\n    const insights: string[] = [];\r\n    const priorityActions: string[] = [];\r\n    const potentialIssues: string[] = [];\r\n    \r\n    // Analyze savings potential\r\n    if (result.predicted_savings.energy > 20) {\r\n      insights.push(`Significant energy savings potential: ${result.predicted_savings.energy.toFixed(1)}%`);\r\n    }\r\n    \r\n    if (result.predicted_savings.cost > 5000) {\r\n      insights.push(`Annual cost savings: $${result.predicted_savings.cost.toLocaleString()}`);\r\n    }\r\n    \r\n    // Identify priority actions\r\n    const highPrioritySteps = result.implementation_plan\r\n      .filter(step => step.priority === 'high')\r\n      .sort((a, b) => b.expected_impact - a.expected_impact);\r\n    \r\n    priorityActions.push(...highPrioritySteps.slice(0, 3).map(step => step.description));\r\n    \r\n    // Identify potential issues\r\n    const highRiskFactors = result.risk_assessment\r\n      .filter(risk => risk.probability * risk.impact > 0.5)\r\n      .sort((a, b) => (b.probability * b.impact) - (a.probability * a.impact));\r\n    \r\n    potentialIssues.push(...highRiskFactors.slice(0, 3).map(risk => risk.description));\r\n    \r\n    return {\r\n      key_insights: insights,\r\n      priority_actions: priorityActions,\r\n      potential_issues: potentialIssues\r\n    };\r\n  }\r\n\r\n  private async loadSpecializedModels(): Promise<void> {\r\n    const models = [\r\n      { name: 'energy_prediction', path: '/models/energy_prediction.onnx' },\r\n      { name: 'anomaly_detection', path: '/models/anomaly_detection.onnx' },\r\n      { name: 'equipment_sizing', path: '/models/equipment_sizing.onnx' },\r\n      { name: 'load_forecasting', path: '/models/load_forecasting.onnx' }\r\n    ];\r\n\r\n    for (const model of models) {\r\n      try {\r\n        const session = await ort.InferenceSession.create(model.path);\r\n        this.modelCache.set(model.name, session);\r\n        console.log(`Loaded ${model.name} model`);\r\n      } catch (error) {\r\n        console.warn(`Failed to load ${model.name} model:`, error);\r\n      }\r\n    }\r\n  }\r\n\r\n  private async prepareModelInput(input: OptimizationInput): Promise<Record<string, ort.Tensor>> {\r\n    // Normalize and prepare input data for the model\r\n    const features = [\r\n      // Building features\r\n      input.buildingData.area / 10000, // Normalize area\r\n      input.buildingData.volume / 100000, // Normalize volume\r\n      input.buildingData.occupancy / 1000, // Normalize occupancy\r\n      input.buildingData.insulation / 50, // Normalize R-value\r\n      input.buildingData.floors / 50, // Normalize floors\r\n      input.buildingData.zoneCount / 20, // Normalize zones\r\n      \r\n      // Environmental features\r\n      ...input.environmentalData.outdoorTemperature.slice(0, 24).map(t => t / 100),\r\n      ...input.environmentalData.humidity.slice(0, 24).map(h => h / 100),\r\n      \r\n      // Operational features\r\n      input.operationalData.currentLoad / 1000, // Normalize load\r\n      input.operationalData.performanceMetrics.cop / 10,\r\n      input.operationalData.performanceMetrics.eer / 30,\r\n      input.operationalData.performanceMetrics.capacity_utilization,\r\n      \r\n      // Constraint features\r\n      input.constraints.budget / 100000, // Normalize budget\r\n      input.constraints.timeline / 365, // Normalize timeline\r\n    ];\r\n\r\n    // Pad or truncate to expected input size\r\n    const inputSize = 100; // Expected model input size\r\n    const paddedFeatures = features.slice(0, inputSize);\r\n    while (paddedFeatures.length < inputSize) {\r\n      paddedFeatures.push(0);\r\n    }\r\n\r\n    return {\r\n      input: new ort.Tensor('float32', new Float32Array(paddedFeatures), [1, inputSize])\r\n    };\r\n  }\r\n\r\n  private async processResults(results: ort.InferenceSession.OnnxValueMapType, input: OptimizationInput): Promise<OptimizationResult> {\r\n    // Process model outputs into structured recommendations\r\n    const outputData = results.output.data as Float32Array;\r\n    \r\n    // Extract different types of recommendations\r\n    const recommendations = this.extractRecommendations(outputData, input);\r\n    const savings = this.calculateSavings(outputData, input);\r\n    const implementationPlan = this.generateImplementationPlan(recommendations, input);\r\n    const roiAnalysis = this.calculateROI(savings, implementationPlan);\r\n    const riskAssessment = this.assessRisks(recommendations, input);\r\n    \r\n    return {\r\n      recommendations,\r\n      predicted_savings: savings,\r\n      confidence_score: Math.min(outputData[0], 1.0),\r\n      implementation_plan: implementationPlan,\r\n      roi_analysis: roiAnalysis,\r\n      risk_assessment: riskAssessment\r\n    };\r\n  }\r\n\r\n  private extractRecommendations(outputData: Float32Array, input: OptimizationInput): OptimizationRecommendation[] {\r\n    // Extract and decode recommendations from model output\r\n    const recommendations: OptimizationRecommendation[] = [];\r\n    \r\n    // This is a simplified example - real implementation would decode model outputs\r\n    if (outputData[1] > 0.5) {\r\n      recommendations.push({\r\n        id: 'upgrade_hvac',\r\n        type: 'equipment_upgrade',\r\n        description: 'Upgrade HVAC Equipment - Replace aging equipment with high-efficiency units',\r\n        priority: 'high',\r\n        implementation_cost: outputData[3] * 10000,\r\n        annual_savings: outputData[2] * 100,\r\n        payback_period: outputData[4] * 10,\r\n        confidence: 0.85,\r\n        impact_areas: ['energy_efficiency', 'cost_reduction']\r\n      });\r\n    }\r\n    \r\n    if (outputData[5] > 0.5) {\r\n      recommendations.push({\r\n        id: 'optimize_controls',\r\n        type: 'control_strategy',\r\n        description: 'Optimize Control Systems - Implement smart controls and scheduling',\r\n        priority: 'medium',\r\n        implementation_cost: outputData[7] * 5000,\r\n        annual_savings: outputData[6] * 100,\r\n        payback_period: outputData[8] * 5,\r\n        confidence: 0.75,\r\n        impact_areas: ['energy_efficiency', 'comfort']\r\n      });\r\n    }\r\n    \r\n    return recommendations;\r\n  }\r\n\r\n  private calculateSavings(outputData: Float32Array, input: OptimizationInput) {\r\n    const currentEnergyCost = input.operationalData.energyConsumption.reduce((sum, e) => sum + e, 0) * 0.12; // $0.12/kWh\r\n    \r\n    return {\r\n      energy: outputData[10] * 100, // Percentage energy savings\r\n      cost: currentEnergyCost * outputData[10], // Annual cost savings\r\n      emissions: currentEnergyCost * outputData[10] * 0.4 * 1000 // kg CO2 equivalent\r\n    };\r\n  }\r\n\r\n  private generateImplementationPlan(recommendations: OptimizationRecommendation[], input: OptimizationInput): ImplementationStep[] {\r\n    return recommendations.map((rec, index) => ({\r\n      id: rec.id,\r\n      description: rec.description,\r\n      priority: rec.priority,\r\n      estimated_cost: rec.implementation_cost,\r\n      estimated_duration: rec.payback_period * 30, // Convert to days\r\n      dependencies: index > 0 ? [recommendations[index - 1].id] : [],\r\n      expected_impact: rec.annual_savings\r\n    }));\r\n  }\r\n\r\n  private calculateROI(savings: any, plan: ImplementationStep[]): ROIAnalysis {\r\n    const totalInvestment = plan.reduce((sum, step) => sum + step.estimated_cost, 0);\r\n    const annualSavings = savings.cost;\r\n    \r\n    return {\r\n      initial_investment: totalInvestment,\r\n      annual_savings: annualSavings,\r\n      payback_period: totalInvestment / annualSavings,\r\n      net_present_value: this.calculateNPV(totalInvestment, annualSavings, 0.05, 10),\r\n      internal_rate_of_return: this.calculateIRR(totalInvestment, annualSavings, 10)\r\n    };\r\n  }\r\n\r\n  private assessRisks(recommendations: OptimizationRecommendation[], input: OptimizationInput): RiskFactor[] {\r\n    const risks: RiskFactor[] = [];\r\n    \r\n    // Budget risk\r\n    const totalCost = recommendations.reduce((sum, rec) => sum + rec.implementation_cost, 0);\r\n    if (totalCost > input.constraints.budget * 0.8) {\r\n      risks.push({\r\n        type: 'budget',\r\n        description: 'Implementation cost approaches budget limit',\r\n        probability: 0.7,\r\n        impact: 0.8,\r\n        mitigation: 'Phase implementation or seek additional funding'\r\n      });\r\n    }\r\n    \r\n    // Timeline risk\r\n    const totalDuration = recommendations.length * 30; // Simplified\r\n    if (totalDuration > input.constraints.timeline * 0.8) {\r\n      risks.push({\r\n        type: 'timeline',\r\n        description: 'Implementation timeline may exceed constraints',\r\n        probability: 0.6,\r\n        impact: 0.6,\r\n        mitigation: 'Parallel implementation or scope reduction'\r\n      });\r\n    }\r\n    \r\n    return risks;\r\n  }\r\n\r\n  private prepareEnergyInput(hvacSystem: HVACSystem, environmentalData: EnvironmentalData, timeHorizon: number): Record<string, ort.Tensor> {\r\n    // Prepare input for energy prediction model\r\n    const features = [\r\n      ...environmentalData.outdoorTemperature.slice(0, timeHorizon),\r\n      ...environmentalData.humidity.slice(0, timeHorizon),\r\n      // Add more features as needed\r\n    ];\r\n    \r\n    return {\r\n      input: new ort.Tensor('float32', new Float32Array(features), [1, features.length])\r\n    };\r\n  }\r\n\r\n  private prepareAnomalyInput(performanceData: PerformanceMetrics[]): Record<string, ort.Tensor> {\r\n    const features = performanceData.flatMap(data => [\r\n      data.cop,\r\n      data.eer,\r\n      data.capacity_utilization,\r\n      data.temperature_variance\r\n    ]);\r\n    \r\n    return {\r\n      input: new ort.Tensor('float32', new Float32Array(features), [1, features.length])\r\n    };\r\n  }\r\n\r\n  private prepareSizingInput(buildingData: BuildingData, loadRequirements: number[]): Record<string, ort.Tensor> {\r\n    const features = [\r\n      buildingData.area,\r\n      buildingData.volume,\r\n      buildingData.occupancy,\r\n      ...loadRequirements.slice(0, 24)\r\n    ];\r\n    \r\n    return {\r\n      input: new ort.Tensor('float32', new Float32Array(features), [1, features.length])\r\n    };\r\n  }\r\n\r\n  private decodeEquipmentTypes(data: Float32Array): string[] {\r\n    // Decode equipment type indices to names\r\n    const equipmentTypes = ['Heat Pump', 'Chiller', 'Boiler', 'Air Handler', 'Fan Coil'];\r\n    return Array.from(data).map(index => equipmentTypes[Math.floor(index)] || 'Unknown');\r\n  }\r\n\r\n  private async assessRenewablePotential(hvacSystem: HVACSystem): Promise<number> {\r\n    // Simplified renewable potential assessment\r\n    return Math.random() * 0.5 + 0.3; // 30-80% potential\r\n  }\r\n\r\n  private calculateSustainabilityScore(hvacSystem: HVACSystem, carbonFootprint: number, renewablePotential: number): number {\r\n    // Simplified sustainability scoring\r\n    const efficiencyScore = Math.min(hvacSystem.efficiency || 0.8, 1.0);\r\n    const carbonScore = Math.max(0, 1 - carbonFootprint / 10000);\r\n    const renewableScore = renewablePotential;\r\n    \r\n    return (efficiencyScore * 0.4 + carbonScore * 0.4 + renewableScore * 0.2) * 100;\r\n  }\r\n\r\n  private generateEnvironmentalRecommendations(sustainabilityScore: number, renewablePotential: number): string[] {\r\n    const recommendations: string[] = [];\r\n    \r\n    if (sustainabilityScore < 60) {\r\n      recommendations.push('Consider upgrading to high-efficiency equipment');\r\n    }\r\n    \r\n    if (renewablePotential > 0.5) {\r\n      recommendations.push('Explore solar panel integration');\r\n      recommendations.push('Consider geothermal heat pump systems');\r\n    }\r\n    \r\n    recommendations.push('Implement smart scheduling to reduce peak demand');\r\n    recommendations.push('Regular maintenance to maintain efficiency');\r\n    \r\n    return recommendations;\r\n  }\r\n\r\n  private async validateRecommendations(result: OptimizationResult, input: OptimizationInput): Promise<OptimizationResult> {\r\n    // Validate recommendations against constraints and feasibility\r\n    const validatedRecommendations = result.recommendations.filter(rec => {\r\n      // Check budget constraints\r\n      if (rec.implementation_cost > input.constraints.budget) {\r\n        return false;\r\n      }\r\n      \r\n      // Check timeline constraints\r\n      if (rec.payback_period * 30 > input.constraints.timeline) {\r\n        return false;\r\n      }\r\n      \r\n      return true;\r\n    });\r\n    \r\n    return {\r\n      ...result,\r\n      recommendations: validatedRecommendations\r\n    };\r\n  }\r\n\r\n  private calculateNPV(investment: number, annualSavings: number, discountRate: number, years: number): number {\r\n    let npv = -investment;\r\n    for (let year = 1; year <= years; year++) {\r\n      npv += annualSavings / Math.pow(1 + discountRate, year);\r\n    }\r\n    return npv;\r\n  }\r\n\r\n  private calculateIRR(investment: number, annualSavings: number, years: number): number {\r\n    // Simplified IRR calculation\r\n    return (annualSavings / investment) * 100;\r\n  }\r\n\r\n  /**\r\n   * Cleanup resources\r\n   */\r\n  async dispose(): Promise<void> {\r\n    if (this.session) {\r\n      await this.session.release();\r\n    }\r\n    \r\n    for (const [name, session] of this.modelCache) {\r\n      await session.release();\r\n    }\r\n    \r\n    this.modelCache.clear();\r\n    this.isInitialized = false;\r\n  }\r\n}\r\n\r\n// Singleton instance\r\nlet aiOptimizationService: AIOptimizationService | null = null;\r\n\r\nexport function getAIOptimizationService(config?: AIOptimizationConfig): AIOptimizationService {\r\n  if (!aiOptimizationService && config) {\r\n    aiOptimizationService = new AIOptimizationService(config);\r\n  }\r\n  return aiOptimizationService!;\r\n}\r\n\r\nexport default AIOptimizationService;\r\n\r\n// React Hook for AI Optimization\r\nexport function useAIOptimization(config?: AIOptimizationConfig) {\r\n  const [service, setService] = useState<AIOptimizationService | null>(null);\r\n  const [isInitialized, setIsInitialized] = useState(false);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  useEffect(() => {\r\n    if (config) {\r\n      const aiService = new AIOptimizationService(config);\r\n      setService(aiService);\r\n\r\n      aiService.initialize()\r\n        .then(() => setIsInitialized(true))\r\n        .catch(err => setError(err.message));\r\n    }\r\n  }, [config]);\r\n\r\n  const optimizeSystem = useCallback(async (input: OptimizationInput) => {\r\n    if (!service || !isInitialized) {\r\n      throw new Error('AI service not initialized');\r\n    }\r\n\r\n    setIsLoading(true);\r\n    setError(null);\r\n\r\n    try {\r\n      const result = await service.optimizeSystem(input);\r\n      return result;\r\n    } catch (err) {\r\n      const errorMessage = err instanceof Error ? err.message : 'Optimization failed';\r\n      setError(errorMessage);\r\n      throw err;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, [service, isInitialized]);\r\n\r\n  const predictEnergy = useCallback(async (\r\n    hvacSystem: HVACSystem,\r\n    environmentalData: EnvironmentalData,\r\n    timeHorizon?: number\r\n  ) => {\r\n    if (!service || !isInitialized) {\r\n      throw new Error('AI service not initialized');\r\n    }\r\n\r\n    return service.predictEnergyConsumption(hvacSystem, environmentalData, timeHorizon);\r\n  }, [service, isInitialized]);\r\n\r\n  const detectAnomalies = useCallback(async (\r\n    performanceData: PerformanceMetrics[],\r\n    threshold?: number\r\n  ) => {\r\n    if (!service || !isInitialized) {\r\n      throw new Error('AI service not initialized');\r\n    }\r\n\r\n    return service.detectAnomalies(performanceData, threshold);\r\n  }, [service, isInitialized]);\r\n\r\n  return {\r\n    isInitialized,\r\n    isLoading,\r\n    error,\r\n    optimizeSystem,\r\n    predictEnergy,\r\n    detectAnomalies,\r\n    service\r\n  };\r\n}\r\n"], "mappings": ";;AAAA;;;;;;;;;;;;;;AAAA;AAAA,SAAAA,eAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;MAAA;QAAAC,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAO,SAAA;MAAA;QAAAD,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAW,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,cAAA;MAAAC,IAAA;MAAAC,QAAA;MAAAC,KAAA;MAAAC,OAAA;MAAAC,cAAA;MAAAC,OAAA;IAAA;IAAAC,eAAA;IAAA5B,IAAA;EAAA;EAAA,IAAA6B,QAAA,GAAA5B,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAA0B,QAAA,CAAA9B,IAAA,KAAA8B,QAAA,CAAA9B,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAA6B,QAAA,CAAA9B,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAA0B,cAAA,GAAAD,QAAA,CAAA9B,IAAA;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAusBAgC,OAAA,CAAAC,wBAAA,GAAAA,wBAAA;AAKC;AAAAlC,cAAA,GAAAoB,CAAA;AAKDa,OAAA,CAAAE,iBAAA,GAAAA,iBAAA;AAlsBA,MAAAC,OAAA;AAAA;AAAA,CAAApC,cAAA,GAAAoB,CAAA,QAAAiB,OAAA;AACA,MAAAC,GAAA;AAAA;AAAA,CAAAtC,cAAA,GAAAoB,CAAA,QAAAmB,YAAA,CAAAF,OAAA;AAsJA,MAAaG,qBAAqB;EAMhCC,YAAYC,MAA4B;IAAA;IAAA1C,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IALhC,KAAAuB,OAAO,GAAgC,IAAI;IAAC;IAAA3C,cAAA,GAAAoB,CAAA;IAE5C,KAAAwB,UAAU,GAAsC,IAAIC,GAAG,EAAE;IAAC;IAAA7C,cAAA,GAAAoB,CAAA;IAC1D,KAAA0B,aAAa,GAAG,KAAK;IAAC;IAAA9C,cAAA,GAAAoB,CAAA;IAG5B,IAAI,CAACsB,MAAM,GAAG;MACZK,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE,CAAC;MACZC,mBAAmB,EAAE,GAAG;MACxBC,iBAAiB,EAAE,CACjB;QAAEjC,IAAI,EAAE,mBAAmB;QAAEkC,MAAM,EAAE;MAAG,CAAE,EAC1C;QAAElC,IAAI,EAAE,gBAAgB;QAAEkC,MAAM,EAAE;MAAG,CAAE,EACvC;QAAElC,IAAI,EAAE,SAAS;QAAEkC,MAAM,EAAE;MAAG,CAAE,EAChC;QAAElC,IAAI,EAAE,eAAe;QAAEkC,MAAM,EAAE;MAAG,CAAE,CACvC;MACD,GAAGT;KACJ;EACH;EAEA;;;EAGA,MAAMU,UAAUA,CAAA;IAAA;IAAApD,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACd,IAAI;MAAA;MAAApB,cAAA,GAAAoB,CAAA;MACF;MACA,IAAI,IAAI,CAACsB,MAAM,CAACK,SAAS,EAAE;QAAA;QAAA/C,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACzBkB,GAAG,CAACe,GAAG,CAACC,IAAI,CAACC,UAAU,GAAG,CAAC;QAAC;QAAAvD,cAAA,GAAAoB,CAAA;QAC5BkB,GAAG,CAACe,GAAG,CAACC,IAAI,CAACE,IAAI,GAAG,IAAI;MAC1B,CAAC;MAAA;MAAA;QAAAxD,cAAA,GAAAsB,CAAA;MAAA;MAED;MAAAtB,cAAA,GAAAoB,CAAA;MACA,IAAI,CAACuB,OAAO,GAAG,MAAML,GAAG,CAACmB,gBAAgB,CAACC,MAAM,CAAC,IAAI,CAAChB,MAAM,CAACiB,SAAS,CAAC;MAEvE;MAAA;MAAA3D,cAAA,GAAAoB,CAAA;MACA,MAAM,IAAI,CAACwC,qBAAqB,EAAE;MAAC;MAAA5D,cAAA,GAAAoB,CAAA;MAEnC,IAAI,CAAC0B,aAAa,GAAG,IAAI;MAAC;MAAA9C,cAAA,GAAAoB,CAAA;MAC1ByC,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;IAEjE,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAA;MAAA/D,cAAA,GAAAoB,CAAA;MACdyC,OAAO,CAACE,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;MAAC;MAAA/D,cAAA,GAAAoB,CAAA;MACtE,MAAM2C,KAAK;IACb;EACF;EAEA;;;EAGA,MAAMC,cAAcA,CAACC,KAAwB;IAAA;IAAAjE,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAC3C;IAAI;IAAA,CAAApB,cAAA,GAAAsB,CAAA,YAAC,IAAI,CAACwB,aAAa;IAAA;IAAA,CAAA9C,cAAA,GAAAsB,CAAA,WAAI,CAAC,IAAI,CAACqB,OAAO,GAAE;MAAA;MAAA3C,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACxC,MAAM,IAAI8C,KAAK,CAAC,yCAAyC,CAAC;IAC5D,CAAC;IAAA;IAAA;MAAAlE,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAED,IAAI;MACF;MACA,MAAM+C,UAAU;MAAA;MAAA,CAAAnE,cAAA,GAAAoB,CAAA,QAAG,MAAM,IAAI,CAACgD,iBAAiB,CAACH,KAAK,CAAC;MAEtD;MACA,MAAMI,OAAO;MAAA;MAAA,CAAArE,cAAA,GAAAoB,CAAA,QAAG,MAAM,IAAI,CAACuB,OAAO,CAAC2B,GAAG,CAACH,UAAU,CAAC;MAElD;MACA,MAAMI,kBAAkB;MAAA;MAAA,CAAAvE,cAAA,GAAAoB,CAAA,QAAG,MAAM,IAAI,CAACoD,cAAc,CAACH,OAAO,EAAEJ,KAAK,CAAC;MAEpE;MACA,MAAMQ,eAAe;MAAA;MAAA,CAAAzE,cAAA,GAAAoB,CAAA,QAAG,MAAM,IAAI,CAACsD,uBAAuB,CAACH,kBAAkB,EAAEN,KAAK,CAAC;MAAC;MAAAjE,cAAA,GAAAoB,CAAA;MAEtF,OAAOqD,eAAe;IAExB,CAAC,CAAC,OAAOV,KAAK,EAAE;MAAA;MAAA/D,cAAA,GAAAoB,CAAA;MACdyC,OAAO,CAACE,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAAC;MAAA/D,cAAA,GAAAoB,CAAA;MAC7C,MAAM2C,KAAK;IACb;EACF;EAEA;;;EAGA,MAAMY,wBAAwBA,CAC5BC,UAAsB,EACtBC,iBAAoC,EACpCC,WAAA;EAAA;EAAA,CAAA9E,cAAA,GAAAsB,CAAA,WAAsB,EAAE,EAAC;EAAA,E;;;IAEzB,MAAMyD,WAAW;IAAA;IAAA,CAAA/E,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACwB,UAAU,CAACoC,GAAG,CAAC,mBAAmB,CAAC;IAAC;IAAAhF,cAAA,GAAAoB,CAAA;IAC7D,IAAI,CAAC2D,WAAW,EAAE;MAAA;MAAA/E,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAChB,MAAM,IAAI8C,KAAK,CAAC,oCAAoC,CAAC;IACvD,CAAC;IAAA;IAAA;MAAAlE,cAAA,GAAAsB,CAAA;IAAA;IAED,MAAM2C,KAAK;IAAA;IAAA,CAAAjE,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAAC6D,kBAAkB,CAACL,UAAU,EAAEC,iBAAiB,EAAEC,WAAW,CAAC;IACjF,MAAMT,OAAO;IAAA;IAAA,CAAArE,cAAA,GAAAoB,CAAA,QAAG,MAAM2D,WAAW,CAACT,GAAG,CAACL,KAAK,CAAC;IAAC;IAAAjE,cAAA,GAAAoB,CAAA;IAE7C,OAAO8D,KAAK,CAACC,IAAI,CAACd,OAAO,CAACe,kBAAkB,CAACC,IAAoB,CAAC;EACpE;EAEA;;;EAGA,MAAMC,eAAeA,CACnBC,eAAqC,EACrCC,SAAA;EAAA;EAAA,CAAAxF,cAAA,GAAAsB,CAAA,WAAoB,GAAG;IAAA;IAAAtB,cAAA,GAAAqB,CAAA;IAEvB,MAAMoE,YAAY;IAAA;IAAA,CAAAzF,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACwB,UAAU,CAACoC,GAAG,CAAC,mBAAmB,CAAC;IAAC;IAAAhF,cAAA,GAAAoB,CAAA;IAC9D,IAAI,CAACqE,YAAY,EAAE;MAAA;MAAAzF,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACjB,MAAM,IAAI8C,KAAK,CAAC,oCAAoC,CAAC;IACvD,CAAC;IAAA;IAAA;MAAAlE,cAAA,GAAAsB,CAAA;IAAA;IAED,MAAM2C,KAAK;IAAA;IAAA,CAAAjE,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACsE,mBAAmB,CAACH,eAAe,CAAC;IACvD,MAAMlB,OAAO;IAAA;IAAA,CAAArE,cAAA,GAAAoB,CAAA,QAAG,MAAMqE,YAAY,CAACnB,GAAG,CAACL,KAAK,CAAC;IAE7C,MAAM0B,aAAa;IAAA;IAAA,CAAA3F,cAAA,GAAAoB,CAAA,QAAG8D,KAAK,CAACC,IAAI,CAACd,OAAO,CAACuB,cAAc,CAACP,IAAoB,CAAC;IAC7E,MAAMQ,gBAAgB;IAAA;IAAA,CAAA7F,cAAA,GAAAoB,CAAA,QAAG8D,KAAK,CAACC,IAAI,CAACd,OAAO,CAACyB,UAAU,CAACT,IAAoB,CAAC;IAE5E,MAAMU,SAAS;IAAA;IAAA,CAAA/F,cAAA,GAAAoB,CAAA,QAAGuE,aAAa,CAACK,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,KAC/C;MAAA;MAAAlG,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAAA,OAAA6E,KAAK,GAAGT,SAAS;MAAA;MAAA,CAAAxF,cAAA,GAAAsB,CAAA,WAAG4E,KAAK;MAAA;MAAA,CAAAlG,cAAA,GAAAsB,CAAA,WAAG,CAAC,CAAC;IAAD,CAAC,CAC/B,CAAC6E,MAAM,CAACD,KAAK,IAAI;MAAA;MAAAlG,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAAA,OAAA8E,KAAK,KAAK,CAAC,CAAC;IAAD,CAAC,CAAC;IAAC;IAAAlG,cAAA,GAAAoB,CAAA;IAEhC,OAAO;MACL2E,SAAS;MACTD,UAAU,EAAEC,SAAS,CAACC,GAAG,CAACE,KAAK,IAAI;QAAA;QAAAlG,cAAA,GAAAqB,CAAA;QAAArB,cAAA,GAAAoB,CAAA;QAAA,OAAAyE,gBAAgB,CAACK,KAAK,CAAC;MAAD,CAAC;KAC3D;EACH;EAEA;;;EAGA,MAAME,wBAAwBA,CAC5BC,YAA0B,EAC1BC,gBAA0B;IAAA;IAAAtG,cAAA,GAAAqB,CAAA;IAO1B,MAAMkF,WAAW;IAAA;IAAA,CAAAvG,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACwB,UAAU,CAACoC,GAAG,CAAC,kBAAkB,CAAC;IAAC;IAAAhF,cAAA,GAAAoB,CAAA;IAC5D,IAAI,CAACmF,WAAW,EAAE;MAAA;MAAAvG,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAChB,MAAM,IAAI8C,KAAK,CAAC,mCAAmC,CAAC;IACtD,CAAC;IAAA;IAAA;MAAAlE,cAAA,GAAAsB,CAAA;IAAA;IAED,MAAM2C,KAAK;IAAA;IAAA,CAAAjE,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACoF,kBAAkB,CAACH,YAAY,EAAEC,gBAAgB,CAAC;IACrE,MAAMjC,OAAO;IAAA;IAAA,CAAArE,cAAA,GAAAoB,CAAA,QAAG,MAAMmF,WAAW,CAACjC,GAAG,CAACL,KAAK,CAAC;IAAC;IAAAjE,cAAA,GAAAoB,CAAA;IAE7C,OAAO;MACLqF,SAAS,EAAE,IAAI,CAACC,oBAAoB,CAACrC,OAAO,CAACsC,eAAe,CAACtB,IAAoB,CAAC;MAClFuB,KAAK,EAAE1B,KAAK,CAACC,IAAI,CAACd,OAAO,CAACwC,eAAe,CAACxB,IAAoB,CAAC;MAC/DyB,kBAAkB,EAAE5B,KAAK,CAACC,IAAI,CAACd,OAAO,CAACyC,kBAAkB,CAACzB,IAAoB,CAAC;MAC/E0B,cAAc,EAAE7B,KAAK,CAACC,IAAI,CAACd,OAAO,CAAC0C,cAAc,CAAC1B,IAAoB;KACvE;EACH;EAEA;;;EAGA,MAAM2B,0BAA0BA,CAC9BpC,UAAsB,EACtBqC,iBAA2B;IAAA;IAAAjH,cAAA,GAAAqB,CAAA;IAO3B;IACA,MAAM6F,eAAe;IAAA;IAAA,CAAAlH,cAAA,GAAAoB,CAAA,QAAG,GAAG,EAAC,CAAC;IAC7B,MAAM+F,WAAW;IAAA;IAAA,CAAAnH,cAAA,GAAAoB,CAAA,QAAG6F,iBAAiB,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEC,WAAW,KAAK;MAAA;MAAAtH,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAAA,OAAAiG,GAAG,GAAGC,WAAW;IAAX,CAAW,EAAE,CAAC,CAAC;IACxF,MAAMC,eAAe;IAAA;IAAA,CAAAvH,cAAA,GAAAoB,CAAA,QAAG+F,WAAW,GAAGD,eAAe;IAErD;IACA,MAAMM,kBAAkB;IAAA;IAAA,CAAAxH,cAAA,GAAAoB,CAAA,QAAG,MAAM,IAAI,CAACqG,wBAAwB,CAAC7C,UAAU,CAAC;IAE1E;IACA,MAAM8C,mBAAmB;IAAA;IAAA,CAAA1H,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACuG,4BAA4B,CAC3D/C,UAAU,EACV2C,eAAe,EACfC,kBAAkB,CACnB;IAED;IACA,MAAMI,eAAe;IAAA;IAAA,CAAA5H,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACyG,oCAAoC,CAC/DH,mBAAmB,EACnBF,kBAAkB,CACnB;IAAC;IAAAxH,cAAA,GAAAoB,CAAA;IAEF,OAAO;MACL0G,gBAAgB,EAAEP,eAAe;MACjCQ,mBAAmB,EAAEP,kBAAkB;MACvCQ,oBAAoB,EAAEN,mBAAmB;MACzCE;KACD;EACH;EAEA;;;EAGAK,uBAAuBA,CAACC,MAA0B;IAAA;IAAAlI,cAAA,GAAAqB,CAAA;IAKhD,MAAM8G,QAAQ;IAAA;IAAA,CAAAnI,cAAA,GAAAoB,CAAA,QAAa,EAAE;IAC7B,MAAMgH,eAAe;IAAA;IAAA,CAAApI,cAAA,GAAAoB,CAAA,QAAa,EAAE;IACpC,MAAMiH,eAAe;IAAA;IAAA,CAAArI,cAAA,GAAAoB,CAAA,QAAa,EAAE;IAEpC;IAAA;IAAApB,cAAA,GAAAoB,CAAA;IACA,IAAI8G,MAAM,CAACI,iBAAiB,CAACC,MAAM,GAAG,EAAE,EAAE;MAAA;MAAAvI,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACxC+G,QAAQ,CAACK,IAAI,CAAC,yCAAyCN,MAAM,CAACI,iBAAiB,CAACC,MAAM,CAACE,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IACvG,CAAC;IAAA;IAAA;MAAAzI,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAED,IAAI8G,MAAM,CAACI,iBAAiB,CAACI,IAAI,GAAG,IAAI,EAAE;MAAA;MAAA1I,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACxC+G,QAAQ,CAACK,IAAI,CAAC,yBAAyBN,MAAM,CAACI,iBAAiB,CAACI,IAAI,CAACC,cAAc,EAAE,EAAE,CAAC;IAC1F,CAAC;IAAA;IAAA;MAAA3I,cAAA,GAAAsB,CAAA;IAAA;IAED;IACA,MAAMsH,iBAAiB;IAAA;IAAA,CAAA5I,cAAA,GAAAoB,CAAA,SAAG8G,MAAM,CAACW,mBAAmB,CACjD1C,MAAM,CAAC2C,IAAI,IAAI;MAAA;MAAA9I,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAAA,OAAA0H,IAAI,CAACC,QAAQ,KAAK,MAAM;IAAN,CAAM,CAAC,CACxCC,IAAI,CAAC,CAACC,CAAC,EAAE3H,CAAC,KAAK;MAAA;MAAAtB,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAAA,OAAAE,CAAC,CAAC4H,eAAe,GAAGD,CAAC,CAACC,eAAe;IAAf,CAAe,CAAC;IAAC;IAAAlJ,cAAA,GAAAoB,CAAA;IAEzDgH,eAAe,CAACI,IAAI,CAAC,GAAGI,iBAAiB,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACnD,GAAG,CAAC8C,IAAI,IAAI;MAAA;MAAA9I,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAAA,OAAA0H,IAAI,CAACM,WAAW;IAAX,CAAW,CAAC,CAAC;IAEpF;IACA,MAAMC,eAAe;IAAA;IAAA,CAAArJ,cAAA,GAAAoB,CAAA,SAAG8G,MAAM,CAACoB,eAAe,CAC3CnD,MAAM,CAACoD,IAAI,IAAI;MAAA;MAAAvJ,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAAA,OAAAmI,IAAI,CAACC,WAAW,GAAGD,IAAI,CAACE,MAAM,GAAG,GAAG;IAAH,CAAG,CAAC,CACpDT,IAAI,CAAC,CAACC,CAAC,EAAE3H,CAAC,KAAK;MAAA;MAAAtB,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAAA,OAACE,CAAC,CAACkI,WAAW,GAAGlI,CAAC,CAACmI,MAAM,GAAKR,CAAC,CAACO,WAAW,GAAGP,CAAC,CAACQ,MAAO;IAAD,CAAC,CAAC;IAAC;IAAAzJ,cAAA,GAAAoB,CAAA;IAE3EiH,eAAe,CAACG,IAAI,CAAC,GAAGa,eAAe,CAACF,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACnD,GAAG,CAACuD,IAAI,IAAI;MAAA;MAAAvJ,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAAA,OAAAmI,IAAI,CAACH,WAAW;IAAX,CAAW,CAAC,CAAC;IAAC;IAAApJ,cAAA,GAAAoB,CAAA;IAEnF,OAAO;MACLsI,YAAY,EAAEvB,QAAQ;MACtBwB,gBAAgB,EAAEvB,eAAe;MACjCwB,gBAAgB,EAAEvB;KACnB;EACH;EAEQ,MAAMzE,qBAAqBA,CAAA;IAAA;IAAA5D,cAAA,GAAAqB,CAAA;IACjC,MAAMwI,MAAM;IAAA;IAAA,CAAA7J,cAAA,GAAAoB,CAAA,SAAG,CACb;MAAEP,IAAI,EAAE,mBAAmB;MAAEZ,IAAI,EAAE;IAAgC,CAAE,EACrE;MAAEY,IAAI,EAAE,mBAAmB;MAAEZ,IAAI,EAAE;IAAgC,CAAE,EACrE;MAAEY,IAAI,EAAE,kBAAkB;MAAEZ,IAAI,EAAE;IAA+B,CAAE,EACnE;MAAEY,IAAI,EAAE,kBAAkB;MAAEZ,IAAI,EAAE;IAA+B,CAAE,CACpE;IAAC;IAAAD,cAAA,GAAAoB,CAAA;IAEF,KAAK,MAAM0I,KAAK,IAAID,MAAM,EAAE;MAAA;MAAA7J,cAAA,GAAAoB,CAAA;MAC1B,IAAI;QACF,MAAMuB,OAAO;QAAA;QAAA,CAAA3C,cAAA,GAAAoB,CAAA,SAAG,MAAMkB,GAAG,CAACmB,gBAAgB,CAACC,MAAM,CAACoG,KAAK,CAAC7J,IAAI,CAAC;QAAC;QAAAD,cAAA,GAAAoB,CAAA;QAC9D,IAAI,CAACwB,UAAU,CAACmH,GAAG,CAACD,KAAK,CAACjJ,IAAI,EAAE8B,OAAO,CAAC;QAAC;QAAA3C,cAAA,GAAAoB,CAAA;QACzCyC,OAAO,CAACC,GAAG,CAAC,UAAUgG,KAAK,CAACjJ,IAAI,QAAQ,CAAC;MAC3C,CAAC,CAAC,OAAOkD,KAAK,EAAE;QAAA;QAAA/D,cAAA,GAAAoB,CAAA;QACdyC,OAAO,CAACmG,IAAI,CAAC,kBAAkBF,KAAK,CAACjJ,IAAI,SAAS,EAAEkD,KAAK,CAAC;MAC5D;IACF;EACF;EAEQ,MAAMK,iBAAiBA,CAACH,KAAwB;IAAA;IAAAjE,cAAA,GAAAqB,CAAA;IACtD;IACA,MAAM4I,QAAQ;IAAA;IAAA,CAAAjK,cAAA,GAAAoB,CAAA,SAAG;IACf;IACA6C,KAAK,CAACoC,YAAY,CAAC6D,IAAI,GAAG,KAAK;IAAE;IACjCjG,KAAK,CAACoC,YAAY,CAAC8D,MAAM,GAAG,MAAM;IAAE;IACpClG,KAAK,CAACoC,YAAY,CAAC+D,SAAS,GAAG,IAAI;IAAE;IACrCnG,KAAK,CAACoC,YAAY,CAACgE,UAAU,GAAG,EAAE;IAAE;IACpCpG,KAAK,CAACoC,YAAY,CAACiE,MAAM,GAAG,EAAE;IAAE;IAChCrG,KAAK,CAACoC,YAAY,CAACkE,SAAS,GAAG,EAAE;IAAE;IAEnC;IACA,GAAGtG,KAAK,CAACY,iBAAiB,CAAC2F,kBAAkB,CAACrB,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAACnD,GAAG,CAACyE,CAAC,IAAI;MAAA;MAAAzK,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAAA,OAAAqJ,CAAC,GAAG,GAAG;IAAH,CAAG,CAAC,EAC5E,GAAGxG,KAAK,CAACY,iBAAiB,CAAC6F,QAAQ,CAACvB,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAACnD,GAAG,CAAC2E,CAAC,IAAI;MAAA;MAAA3K,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAAA,OAAAuJ,CAAC,GAAG,GAAG;IAAH,CAAG,CAAC;IAElE;IACA1G,KAAK,CAAC2G,eAAe,CAACC,WAAW,GAAG,IAAI;IAAE;IAC1C5G,KAAK,CAAC2G,eAAe,CAACE,kBAAkB,CAACC,GAAG,GAAG,EAAE,EACjD9G,KAAK,CAAC2G,eAAe,CAACE,kBAAkB,CAACE,GAAG,GAAG,EAAE,EACjD/G,KAAK,CAAC2G,eAAe,CAACE,kBAAkB,CAACG,oBAAoB;IAE7D;IACAhH,KAAK,CAACiH,WAAW,CAACC,MAAM,GAAG,MAAM;IAAE;IACnClH,KAAK,CAACiH,WAAW,CAACE,QAAQ,GAAG,GAAG,CAAE;IAAA,CACnC;IAED;IACA,MAAMC,SAAS;IAAA;IAAA,CAAArL,cAAA,GAAAoB,CAAA,SAAG,GAAG,EAAC,CAAC;IACvB,MAAMkK,cAAc;IAAA;IAAA,CAAAtL,cAAA,GAAAoB,CAAA,SAAG6I,QAAQ,CAACd,KAAK,CAAC,CAAC,EAAEkC,SAAS,CAAC;IAAC;IAAArL,cAAA,GAAAoB,CAAA;IACpD,OAAOkK,cAAc,CAACC,MAAM,GAAGF,SAAS,EAAE;MAAA;MAAArL,cAAA,GAAAoB,CAAA;MACxCkK,cAAc,CAAC9C,IAAI,CAAC,CAAC,CAAC;IACxB;IAAC;IAAAxI,cAAA,GAAAoB,CAAA;IAED,OAAO;MACL6C,KAAK,EAAE,IAAI3B,GAAG,CAACkJ,MAAM,CAAC,SAAS,EAAE,IAAIC,YAAY,CAACH,cAAc,CAAC,EAAE,CAAC,CAAC,EAAED,SAAS,CAAC;KAClF;EACH;EAEQ,MAAM7G,cAAcA,CAACH,OAA8C,EAAEJ,KAAwB;IAAA;IAAAjE,cAAA,GAAAqB,CAAA;IACnG;IACA,MAAMqK,UAAU;IAAA;IAAA,CAAA1L,cAAA,GAAAoB,CAAA,SAAGiD,OAAO,CAACsH,MAAM,CAACtG,IAAoB;IAEtD;IACA,MAAMuC,eAAe;IAAA;IAAA,CAAA5H,cAAA,GAAAoB,CAAA,SAAG,IAAI,CAACwK,sBAAsB,CAACF,UAAU,EAAEzH,KAAK,CAAC;IACtE,MAAM4H,OAAO;IAAA;IAAA,CAAA7L,cAAA,GAAAoB,CAAA,SAAG,IAAI,CAAC0K,gBAAgB,CAACJ,UAAU,EAAEzH,KAAK,CAAC;IACxD,MAAM8H,kBAAkB;IAAA;IAAA,CAAA/L,cAAA,GAAAoB,CAAA,SAAG,IAAI,CAAC4K,0BAA0B,CAACpE,eAAe,EAAE3D,KAAK,CAAC;IAClF,MAAMgI,WAAW;IAAA;IAAA,CAAAjM,cAAA,GAAAoB,CAAA,SAAG,IAAI,CAAC8K,YAAY,CAACL,OAAO,EAAEE,kBAAkB,CAAC;IAClE,MAAMI,cAAc;IAAA;IAAA,CAAAnM,cAAA,GAAAoB,CAAA,SAAG,IAAI,CAACgL,WAAW,CAACxE,eAAe,EAAE3D,KAAK,CAAC;IAAC;IAAAjE,cAAA,GAAAoB,CAAA;IAEhE,OAAO;MACLwG,eAAe;MACfU,iBAAiB,EAAEuD,OAAO;MAC1BQ,gBAAgB,EAAEC,IAAI,CAACC,GAAG,CAACb,UAAU,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC;MAC9C7C,mBAAmB,EAAEkD,kBAAkB;MACvCS,YAAY,EAAEP,WAAW;MACzB3C,eAAe,EAAE6C;KAClB;EACH;EAEQP,sBAAsBA,CAACF,UAAwB,EAAEzH,KAAwB;IAAA;IAAAjE,cAAA,GAAAqB,CAAA;IAC/E;IACA,MAAMuG,eAAe;IAAA;IAAA,CAAA5H,cAAA,GAAAoB,CAAA,SAAiC,EAAE;IAExD;IAAA;IAAApB,cAAA,GAAAoB,CAAA;IACA,IAAIsK,UAAU,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE;MAAA;MAAA1L,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACvBwG,eAAe,CAACY,IAAI,CAAC;QACnBiE,EAAE,EAAE,cAAc;QAClBxL,IAAI,EAAE,mBAAmB;QACzBmI,WAAW,EAAE,6EAA6E;QAC1FL,QAAQ,EAAE,MAAM;QAChB2D,mBAAmB,EAAEhB,UAAU,CAAC,CAAC,CAAC,GAAG,KAAK;QAC1CiB,cAAc,EAAEjB,UAAU,CAAC,CAAC,CAAC,GAAG,GAAG;QACnCkB,cAAc,EAAElB,UAAU,CAAC,CAAC,CAAC,GAAG,EAAE;QAClC5F,UAAU,EAAE,IAAI;QAChB+G,YAAY,EAAE,CAAC,mBAAmB,EAAE,gBAAgB;OACrD,CAAC;IACJ,CAAC;IAAA;IAAA;MAAA7M,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAED,IAAIsK,UAAU,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE;MAAA;MAAA1L,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACvBwG,eAAe,CAACY,IAAI,CAAC;QACnBiE,EAAE,EAAE,mBAAmB;QACvBxL,IAAI,EAAE,kBAAkB;QACxBmI,WAAW,EAAE,oEAAoE;QACjFL,QAAQ,EAAE,QAAQ;QAClB2D,mBAAmB,EAAEhB,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI;QACzCiB,cAAc,EAAEjB,UAAU,CAAC,CAAC,CAAC,GAAG,GAAG;QACnCkB,cAAc,EAAElB,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;QACjC5F,UAAU,EAAE,IAAI;QAChB+G,YAAY,EAAE,CAAC,mBAAmB,EAAE,SAAS;OAC9C,CAAC;IACJ,CAAC;IAAA;IAAA;MAAA7M,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAED,OAAOwG,eAAe;EACxB;EAEQkE,gBAAgBA,CAACJ,UAAwB,EAAEzH,KAAwB;IAAA;IAAAjE,cAAA,GAAAqB,CAAA;IACzE,MAAMyL,iBAAiB;IAAA;IAAA,CAAA9M,cAAA,GAAAoB,CAAA,SAAG6C,KAAK,CAAC2G,eAAe,CAAC3D,iBAAiB,CAACG,MAAM,CAAC,CAACC,GAAG,EAAE0F,CAAC,KAAK;MAAA;MAAA/M,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAAA,OAAAiG,GAAG,GAAG0F,CAAC;IAAD,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,EAAC,CAAC;IAAA;IAAA/M,cAAA,GAAAoB,CAAA;IAEzG,OAAO;MACLmH,MAAM,EAAEmD,UAAU,CAAC,EAAE,CAAC,GAAG,GAAG;MAAE;MAC9BhD,IAAI,EAAEoE,iBAAiB,GAAGpB,UAAU,CAAC,EAAE,CAAC;MAAE;MAC1CsB,SAAS,EAAEF,iBAAiB,GAAGpB,UAAU,CAAC,EAAE,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC;KAC5D;EACH;EAEQM,0BAA0BA,CAACpE,eAA6C,EAAE3D,KAAwB;IAAA;IAAAjE,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACxG,OAAOwG,eAAe,CAAC5B,GAAG,CAAC,CAACiH,GAAG,EAAE/G,KAAK,KAAM;MAAA;MAAAlG,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAAA;QAC1CqL,EAAE,EAAEQ,GAAG,CAACR,EAAE;QACVrD,WAAW,EAAE6D,GAAG,CAAC7D,WAAW;QAC5BL,QAAQ,EAAEkE,GAAG,CAAClE,QAAQ;QACtBmE,cAAc,EAAED,GAAG,CAACP,mBAAmB;QACvCS,kBAAkB,EAAEF,GAAG,CAACL,cAAc,GAAG,EAAE;QAAE;QAC7CQ,YAAY,EAAElH,KAAK,GAAG,CAAC;QAAA;QAAA,CAAAlG,cAAA,GAAAsB,CAAA,WAAG,CAACsG,eAAe,CAAC1B,KAAK,GAAG,CAAC,CAAC,CAACuG,EAAE,CAAC;QAAA;QAAA,CAAAzM,cAAA,GAAAsB,CAAA,WAAG,EAAE;QAC9D4H,eAAe,EAAE+D,GAAG,CAACN;OACtB;KAAC,CAAC;EACL;EAEQT,YAAYA,CAACL,OAAY,EAAEwB,IAA0B;IAAA;IAAArN,cAAA,GAAAqB,CAAA;IAC3D,MAAMiM,eAAe;IAAA;IAAA,CAAAtN,cAAA,GAAAoB,CAAA,SAAGiM,IAAI,CAACjG,MAAM,CAAC,CAACC,GAAG,EAAEyB,IAAI,KAAK;MAAA;MAAA9I,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAAA,OAAAiG,GAAG,GAAGyB,IAAI,CAACoE,cAAc;IAAd,CAAc,EAAE,CAAC,CAAC;IAChF,MAAMK,aAAa;IAAA;IAAA,CAAAvN,cAAA,GAAAoB,CAAA,SAAGyK,OAAO,CAACnD,IAAI;IAAC;IAAA1I,cAAA,GAAAoB,CAAA;IAEnC,OAAO;MACLoM,kBAAkB,EAAEF,eAAe;MACnCX,cAAc,EAAEY,aAAa;MAC7BX,cAAc,EAAEU,eAAe,GAAGC,aAAa;MAC/CE,iBAAiB,EAAE,IAAI,CAACC,YAAY,CAACJ,eAAe,EAAEC,aAAa,EAAE,IAAI,EAAE,EAAE,CAAC;MAC9EI,uBAAuB,EAAE,IAAI,CAACC,YAAY,CAACN,eAAe,EAAEC,aAAa,EAAE,EAAE;KAC9E;EACH;EAEQnB,WAAWA,CAACxE,eAA6C,EAAE3D,KAAwB;IAAA;IAAAjE,cAAA,GAAAqB,CAAA;IACzF,MAAMwM,KAAK;IAAA;IAAA,CAAA7N,cAAA,GAAAoB,CAAA,SAAiB,EAAE;IAE9B;IACA,MAAM0M,SAAS;IAAA;IAAA,CAAA9N,cAAA,GAAAoB,CAAA,SAAGwG,eAAe,CAACR,MAAM,CAAC,CAACC,GAAG,EAAE4F,GAAG,KAAK;MAAA;MAAAjN,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAAA,OAAAiG,GAAG,GAAG4F,GAAG,CAACP,mBAAmB;IAAnB,CAAmB,EAAE,CAAC,CAAC;IAAC;IAAA1M,cAAA,GAAAoB,CAAA;IACzF,IAAI0M,SAAS,GAAG7J,KAAK,CAACiH,WAAW,CAACC,MAAM,GAAG,GAAG,EAAE;MAAA;MAAAnL,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAC9CyM,KAAK,CAACrF,IAAI,CAAC;QACTvH,IAAI,EAAE,QAAQ;QACdmI,WAAW,EAAE,6CAA6C;QAC1DI,WAAW,EAAE,GAAG;QAChBC,MAAM,EAAE,GAAG;QACXsE,UAAU,EAAE;OACb,CAAC;IACJ,CAAC;IAAA;IAAA;MAAA/N,cAAA,GAAAsB,CAAA;IAAA;IAED;IACA,MAAM0M,aAAa;IAAA;IAAA,CAAAhO,cAAA,GAAAoB,CAAA,SAAGwG,eAAe,CAAC2D,MAAM,GAAG,EAAE,EAAC,CAAC;IAAA;IAAAvL,cAAA,GAAAoB,CAAA;IACnD,IAAI4M,aAAa,GAAG/J,KAAK,CAACiH,WAAW,CAACE,QAAQ,GAAG,GAAG,EAAE;MAAA;MAAApL,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACpDyM,KAAK,CAACrF,IAAI,CAAC;QACTvH,IAAI,EAAE,UAAU;QAChBmI,WAAW,EAAE,gDAAgD;QAC7DI,WAAW,EAAE,GAAG;QAChBC,MAAM,EAAE,GAAG;QACXsE,UAAU,EAAE;OACb,CAAC;IACJ,CAAC;IAAA;IAAA;MAAA/N,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAED,OAAOyM,KAAK;EACd;EAEQ5I,kBAAkBA,CAACL,UAAsB,EAAEC,iBAAoC,EAAEC,WAAmB;IAAA;IAAA9E,cAAA,GAAAqB,CAAA;IAC1G;IACA,MAAM4I,QAAQ;IAAA;IAAA,CAAAjK,cAAA,GAAAoB,CAAA,SAAG,CACf,GAAGyD,iBAAiB,CAAC2F,kBAAkB,CAACrB,KAAK,CAAC,CAAC,EAAErE,WAAW,CAAC,EAC7D,GAAGD,iBAAiB,CAAC6F,QAAQ,CAACvB,KAAK,CAAC,CAAC,EAAErE,WAAW;IAClD;IAAA,CACD;IAAC;IAAA9E,cAAA,GAAAoB,CAAA;IAEF,OAAO;MACL6C,KAAK,EAAE,IAAI3B,GAAG,CAACkJ,MAAM,CAAC,SAAS,EAAE,IAAIC,YAAY,CAACxB,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAEA,QAAQ,CAACsB,MAAM,CAAC;KAClF;EACH;EAEQ7F,mBAAmBA,CAACH,eAAqC;IAAA;IAAAvF,cAAA,GAAAqB,CAAA;IAC/D,MAAM4I,QAAQ;IAAA;IAAA,CAAAjK,cAAA,GAAAoB,CAAA,SAAGmE,eAAe,CAAC0I,OAAO,CAAC5I,IAAI,IAAI;MAAA;MAAArF,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAAA,QAC/CiE,IAAI,CAAC0F,GAAG,EACR1F,IAAI,CAAC2F,GAAG,EACR3F,IAAI,CAAC4F,oBAAoB,EACzB5F,IAAI,CAAC6I,oBAAoB,CAC1B;KAAA,CAAC;IAAC;IAAAlO,cAAA,GAAAoB,CAAA;IAEH,OAAO;MACL6C,KAAK,EAAE,IAAI3B,GAAG,CAACkJ,MAAM,CAAC,SAAS,EAAE,IAAIC,YAAY,CAACxB,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAEA,QAAQ,CAACsB,MAAM,CAAC;KAClF;EACH;EAEQ/E,kBAAkBA,CAACH,YAA0B,EAAEC,gBAA0B;IAAA;IAAAtG,cAAA,GAAAqB,CAAA;IAC/E,MAAM4I,QAAQ;IAAA;IAAA,CAAAjK,cAAA,GAAAoB,CAAA,SAAG,CACfiF,YAAY,CAAC6D,IAAI,EACjB7D,YAAY,CAAC8D,MAAM,EACnB9D,YAAY,CAAC+D,SAAS,EACtB,GAAG9D,gBAAgB,CAAC6C,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CACjC;IAAC;IAAAnJ,cAAA,GAAAoB,CAAA;IAEF,OAAO;MACL6C,KAAK,EAAE,IAAI3B,GAAG,CAACkJ,MAAM,CAAC,SAAS,EAAE,IAAIC,YAAY,CAACxB,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAEA,QAAQ,CAACsB,MAAM,CAAC;KAClF;EACH;EAEQ7E,oBAAoBA,CAACrB,IAAkB;IAAA;IAAArF,cAAA,GAAAqB,CAAA;IAC7C;IACA,MAAM8M,cAAc;IAAA;IAAA,CAAAnO,cAAA,GAAAoB,CAAA,SAAG,CAAC,WAAW,EAAE,SAAS,EAAE,QAAQ,EAAE,aAAa,EAAE,UAAU,CAAC;IAAC;IAAApB,cAAA,GAAAoB,CAAA;IACrF,OAAO8D,KAAK,CAACC,IAAI,CAACE,IAAI,CAAC,CAACW,GAAG,CAACE,KAAK,IAAI;MAAA;MAAAlG,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAAA,kCAAApB,cAAA,GAAAsB,CAAA,WAAA6M,cAAc,CAAC7B,IAAI,CAAC8B,KAAK,CAAClI,KAAK,CAAC,CAAC;MAAA;MAAA,CAAAlG,cAAA,GAAAsB,CAAA,WAAI,SAAS;IAAT,CAAS,CAAC;EACtF;EAEQ,MAAMmG,wBAAwBA,CAAC7C,UAAsB;IAAA;IAAA5E,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAC3D;IACA,OAAOkL,IAAI,CAAC+B,MAAM,EAAE,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;EACpC;EAEQ1G,4BAA4BA,CAAC/C,UAAsB,EAAE2C,eAAuB,EAAEC,kBAA0B;IAAA;IAAAxH,cAAA,GAAAqB,CAAA;IAC9G;IACA,MAAMiN,eAAe;IAAA;IAAA,CAAAtO,cAAA,GAAAoB,CAAA,SAAGkL,IAAI,CAACC,GAAG;IAAC;IAAA,CAAAvM,cAAA,GAAAsB,CAAA,WAAAsD,UAAU,CAAC2J,UAAU;IAAA;IAAA,CAAAvO,cAAA,GAAAsB,CAAA,WAAI,GAAG,GAAE,GAAG,CAAC;IACnE,MAAMkN,WAAW;IAAA;IAAA,CAAAxO,cAAA,GAAAoB,CAAA,SAAGkL,IAAI,CAACmC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAGlH,eAAe,GAAG,KAAK,CAAC;IAC5D,MAAMmH,cAAc;IAAA;IAAA,CAAA1O,cAAA,GAAAoB,CAAA,SAAGoG,kBAAkB;IAAC;IAAAxH,cAAA,GAAAoB,CAAA;IAE1C,OAAO,CAACkN,eAAe,GAAG,GAAG,GAAGE,WAAW,GAAG,GAAG,GAAGE,cAAc,GAAG,GAAG,IAAI,GAAG;EACjF;EAEQ7G,oCAAoCA,CAACH,mBAA2B,EAAEF,kBAA0B;IAAA;IAAAxH,cAAA,GAAAqB,CAAA;IAClG,MAAMuG,eAAe;IAAA;IAAA,CAAA5H,cAAA,GAAAoB,CAAA,SAAa,EAAE;IAAC;IAAApB,cAAA,GAAAoB,CAAA;IAErC,IAAIsG,mBAAmB,GAAG,EAAE,EAAE;MAAA;MAAA1H,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAC5BwG,eAAe,CAACY,IAAI,CAAC,iDAAiD,CAAC;IACzE,CAAC;IAAA;IAAA;MAAAxI,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAED,IAAIoG,kBAAkB,GAAG,GAAG,EAAE;MAAA;MAAAxH,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAC5BwG,eAAe,CAACY,IAAI,CAAC,iCAAiC,CAAC;MAAC;MAAAxI,cAAA,GAAAoB,CAAA;MACxDwG,eAAe,CAACY,IAAI,CAAC,uCAAuC,CAAC;IAC/D,CAAC;IAAA;IAAA;MAAAxI,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAEDwG,eAAe,CAACY,IAAI,CAAC,kDAAkD,CAAC;IAAC;IAAAxI,cAAA,GAAAoB,CAAA;IACzEwG,eAAe,CAACY,IAAI,CAAC,4CAA4C,CAAC;IAAC;IAAAxI,cAAA,GAAAoB,CAAA;IAEnE,OAAOwG,eAAe;EACxB;EAEQ,MAAMlD,uBAAuBA,CAACwD,MAA0B,EAAEjE,KAAwB;IAAA;IAAAjE,cAAA,GAAAqB,CAAA;IACxF;IACA,MAAMsN,wBAAwB;IAAA;IAAA,CAAA3O,cAAA,GAAAoB,CAAA,SAAG8G,MAAM,CAACN,eAAe,CAACzB,MAAM,CAAC8G,GAAG,IAAG;MAAA;MAAAjN,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MACnE;MACA,IAAI6L,GAAG,CAACP,mBAAmB,GAAGzI,KAAK,CAACiH,WAAW,CAACC,MAAM,EAAE;QAAA;QAAAnL,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACtD,OAAO,KAAK;MACd,CAAC;MAAA;MAAA;QAAApB,cAAA,GAAAsB,CAAA;MAAA;MAED;MAAAtB,cAAA,GAAAoB,CAAA;MACA,IAAI6L,GAAG,CAACL,cAAc,GAAG,EAAE,GAAG3I,KAAK,CAACiH,WAAW,CAACE,QAAQ,EAAE;QAAA;QAAApL,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACxD,OAAO,KAAK;MACd,CAAC;MAAA;MAAA;QAAApB,cAAA,GAAAsB,CAAA;MAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAED,OAAO,IAAI;IACb,CAAC,CAAC;IAAC;IAAApB,cAAA,GAAAoB,CAAA;IAEH,OAAO;MACL,GAAG8G,MAAM;MACTN,eAAe,EAAE+G;KAClB;EACH;EAEQjB,YAAYA,CAACkB,UAAkB,EAAErB,aAAqB,EAAEsB,YAAoB,EAAEC,KAAa;IAAA;IAAA9O,cAAA,GAAAqB,CAAA;IACjG,IAAI0N,GAAG;IAAA;IAAA,CAAA/O,cAAA,GAAAoB,CAAA,SAAG,CAACwN,UAAU;IAAC;IAAA5O,cAAA,GAAAoB,CAAA;IACtB,KAAK,IAAI4N,IAAI;IAAA;IAAA,CAAAhP,cAAA,GAAAoB,CAAA,SAAG,CAAC,GAAE4N,IAAI,IAAIF,KAAK,EAAEE,IAAI,EAAE,EAAE;MAAA;MAAAhP,cAAA,GAAAoB,CAAA;MACxC2N,GAAG,IAAIxB,aAAa,GAAGjB,IAAI,CAAC2C,GAAG,CAAC,CAAC,GAAGJ,YAAY,EAAEG,IAAI,CAAC;IACzD;IAAC;IAAAhP,cAAA,GAAAoB,CAAA;IACD,OAAO2N,GAAG;EACZ;EAEQnB,YAAYA,CAACgB,UAAkB,EAAErB,aAAqB,EAAEuB,KAAa;IAAA;IAAA9O,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAC3E;IACA,OAAQmM,aAAa,GAAGqB,UAAU,GAAI,GAAG;EAC3C;EAEA;;;EAGA,MAAMM,OAAOA,CAAA;IAAA;IAAAlP,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACX,IAAI,IAAI,CAACuB,OAAO,EAAE;MAAA;MAAA3C,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAChB,MAAM,IAAI,CAACuB,OAAO,CAACwM,OAAO,EAAE;IAC9B,CAAC;IAAA;IAAA;MAAAnP,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAED,KAAK,MAAM,CAACP,IAAI,EAAE8B,OAAO,CAAC,IAAI,IAAI,CAACC,UAAU,EAAE;MAAA;MAAA5C,cAAA,GAAAoB,CAAA;MAC7C,MAAMuB,OAAO,CAACwM,OAAO,EAAE;IACzB;IAAC;IAAAnP,cAAA,GAAAoB,CAAA;IAED,IAAI,CAACwB,UAAU,CAACwM,KAAK,EAAE;IAAC;IAAApP,cAAA,GAAAoB,CAAA;IACxB,IAAI,CAAC0B,aAAa,GAAG,KAAK;EAC5B;;AACD;AAAA9C,cAAA,GAAAoB,CAAA;AA5hBDa,OAAA,CAAAO,qBAAA,GAAAA,qBAAA;AA8hBA;AACA,IAAI6M,qBAAqB;AAAA;AAAA,CAAArP,cAAA,GAAAoB,CAAA,SAAiC,IAAI;AAE9D,SAAgBc,wBAAwBA,CAACQ,MAA6B;EAAA;EAAA1C,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EACpE;EAAI;EAAA,CAAApB,cAAA,GAAAsB,CAAA,YAAC+N,qBAAqB;EAAA;EAAA,CAAArP,cAAA,GAAAsB,CAAA,WAAIoB,MAAM,GAAE;IAAA;IAAA1C,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAoB,CAAA;IACpCiO,qBAAqB,GAAG,IAAI7M,qBAAqB,CAACE,MAAM,CAAC;EAC3D,CAAC;EAAA;EAAA;IAAA1C,cAAA,GAAAsB,CAAA;EAAA;EAAAtB,cAAA,GAAAoB,CAAA;EACD,OAAOiO,qBAAsB;AAC/B;AAAC;AAAArP,cAAA,GAAAoB,CAAA;AAEDa,OAAA,CAAAqN,OAAA,GAAe9M,qBAAqB;AAEpC;AACA,SAAgBL,iBAAiBA,CAACO,MAA6B;EAAA;EAAA1C,cAAA,GAAAqB,CAAA;EAC7D,MAAM,CAACkO,OAAO,EAAEC,UAAU,CAAC;EAAA;EAAA,CAAAxP,cAAA,GAAAoB,CAAA,SAAG,IAAAgB,OAAA,CAAAqN,QAAQ,EAA+B,IAAI,CAAC;EAC1E,MAAM,CAAC3M,aAAa,EAAE4M,gBAAgB,CAAC;EAAA;EAAA,CAAA1P,cAAA,GAAAoB,CAAA,SAAG,IAAAgB,OAAA,CAAAqN,QAAQ,EAAC,KAAK,CAAC;EACzD,MAAM,CAACE,SAAS,EAAEC,YAAY,CAAC;EAAA;EAAA,CAAA5P,cAAA,GAAAoB,CAAA,SAAG,IAAAgB,OAAA,CAAAqN,QAAQ,EAAC,KAAK,CAAC;EACjD,MAAM,CAAC1L,KAAK,EAAE8L,QAAQ,CAAC;EAAA;EAAA,CAAA7P,cAAA,GAAAoB,CAAA,SAAG,IAAAgB,OAAA,CAAAqN,QAAQ,EAAgB,IAAI,CAAC;EAAC;EAAAzP,cAAA,GAAAoB,CAAA;EAExD,IAAAgB,OAAA,CAAA0N,SAAS,EAAC,MAAK;IAAA;IAAA9P,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACb,IAAIsB,MAAM,EAAE;MAAA;MAAA1C,cAAA,GAAAsB,CAAA;MACV,MAAMyO,SAAS;MAAA;MAAA,CAAA/P,cAAA,GAAAoB,CAAA,SAAG,IAAIoB,qBAAqB,CAACE,MAAM,CAAC;MAAC;MAAA1C,cAAA,GAAAoB,CAAA;MACpDoO,UAAU,CAACO,SAAS,CAAC;MAAC;MAAA/P,cAAA,GAAAoB,CAAA;MAEtB2O,SAAS,CAAC3M,UAAU,EAAE,CACnB4M,IAAI,CAAC,MAAM;QAAA;QAAAhQ,cAAA,GAAAqB,CAAA;QAAArB,cAAA,GAAAoB,CAAA;QAAA,OAAAsO,gBAAgB,CAAC,IAAI,CAAC;MAAD,CAAC,CAAC,CAClCO,KAAK,CAACC,GAAG,IAAI;QAAA;QAAAlQ,cAAA,GAAAqB,CAAA;QAAArB,cAAA,GAAAoB,CAAA;QAAA,OAAAyO,QAAQ,CAACK,GAAG,CAACC,OAAO,CAAC;MAAD,CAAC,CAAC;IACxC,CAAC;IAAA;IAAA;MAAAnQ,cAAA,GAAAsB,CAAA;IAAA;EACH,CAAC,EAAE,CAACoB,MAAM,CAAC,CAAC;EAEZ,MAAMsB,cAAc;EAAA;EAAA,CAAAhE,cAAA,GAAAoB,CAAA,SAAG,IAAAgB,OAAA,CAAAgO,WAAW,EAAC,MAAOnM,KAAwB,IAAI;IAAA;IAAAjE,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACpE;IAAI;IAAA,CAAApB,cAAA,GAAAsB,CAAA,YAACiO,OAAO;IAAA;IAAA,CAAAvP,cAAA,GAAAsB,CAAA,WAAI,CAACwB,aAAa,GAAE;MAAA;MAAA9C,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAC9B,MAAM,IAAI8C,KAAK,CAAC,4BAA4B,CAAC;IAC/C,CAAC;IAAA;IAAA;MAAAlE,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAEDwO,YAAY,CAAC,IAAI,CAAC;IAAC;IAAA5P,cAAA,GAAAoB,CAAA;IACnByO,QAAQ,CAAC,IAAI,CAAC;IAAC;IAAA7P,cAAA,GAAAoB,CAAA;IAEf,IAAI;MACF,MAAM8G,MAAM;MAAA;MAAA,CAAAlI,cAAA,GAAAoB,CAAA,SAAG,MAAMmO,OAAO,CAACvL,cAAc,CAACC,KAAK,CAAC;MAAC;MAAAjE,cAAA,GAAAoB,CAAA;MACnD,OAAO8G,MAAM;IACf,CAAC,CAAC,OAAOgI,GAAG,EAAE;MACZ,MAAMG,YAAY;MAAA;MAAA,CAAArQ,cAAA,GAAAoB,CAAA,SAAG8O,GAAG,YAAYhM,KAAK;MAAA;MAAA,CAAAlE,cAAA,GAAAsB,CAAA,WAAG4O,GAAG,CAACC,OAAO;MAAA;MAAA,CAAAnQ,cAAA,GAAAsB,CAAA,WAAG,qBAAqB;MAAC;MAAAtB,cAAA,GAAAoB,CAAA;MAChFyO,QAAQ,CAACQ,YAAY,CAAC;MAAC;MAAArQ,cAAA,GAAAoB,CAAA;MACvB,MAAM8O,GAAG;IACX,CAAC,SAAS;MAAA;MAAAlQ,cAAA,GAAAoB,CAAA;MACRwO,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,CAACL,OAAO,EAAEzM,aAAa,CAAC,CAAC;EAE5B,MAAMwN,aAAa;EAAA;EAAA,CAAAtQ,cAAA,GAAAoB,CAAA,SAAG,IAAAgB,OAAA,CAAAgO,WAAW,EAAC,OAChCxL,UAAsB,EACtBC,iBAAoC,EACpCC,WAAoB,KAClB;IAAA;IAAA9E,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACF;IAAI;IAAA,CAAApB,cAAA,GAAAsB,CAAA,YAACiO,OAAO;IAAA;IAAA,CAAAvP,cAAA,GAAAsB,CAAA,WAAI,CAACwB,aAAa,GAAE;MAAA;MAAA9C,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAC9B,MAAM,IAAI8C,KAAK,CAAC,4BAA4B,CAAC;IAC/C,CAAC;IAAA;IAAA;MAAAlE,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAED,OAAOmO,OAAO,CAAC5K,wBAAwB,CAACC,UAAU,EAAEC,iBAAiB,EAAEC,WAAW,CAAC;EACrF,CAAC,EAAE,CAACyK,OAAO,EAAEzM,aAAa,CAAC,CAAC;EAE5B,MAAMwC,eAAe;EAAA;EAAA,CAAAtF,cAAA,GAAAoB,CAAA,SAAG,IAAAgB,OAAA,CAAAgO,WAAW,EAAC,OAClC7K,eAAqC,EACrCC,SAAkB,KAChB;IAAA;IAAAxF,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACF;IAAI;IAAA,CAAApB,cAAA,GAAAsB,CAAA,YAACiO,OAAO;IAAA;IAAA,CAAAvP,cAAA,GAAAsB,CAAA,WAAI,CAACwB,aAAa,GAAE;MAAA;MAAA9C,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAC9B,MAAM,IAAI8C,KAAK,CAAC,4BAA4B,CAAC;IAC/C,CAAC;IAAA;IAAA;MAAAlE,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAED,OAAOmO,OAAO,CAACjK,eAAe,CAACC,eAAe,EAAEC,SAAS,CAAC;EAC5D,CAAC,EAAE,CAAC+J,OAAO,EAAEzM,aAAa,CAAC,CAAC;EAAC;EAAA9C,cAAA,GAAAoB,CAAA;EAE7B,OAAO;IACL0B,aAAa;IACb6M,SAAS;IACT5L,KAAK;IACLC,cAAc;IACdsM,aAAa;IACbhL,eAAe;IACfiK;GACD;AACH", "ignoreList": []}