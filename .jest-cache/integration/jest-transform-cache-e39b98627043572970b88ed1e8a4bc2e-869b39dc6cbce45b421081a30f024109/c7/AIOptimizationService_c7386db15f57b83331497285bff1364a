5e8c90218b45e61dec402b6ed58cf6d6
"use strict";

/**
 * AI-Powered HVAC Optimization Service
 *
 * Integrates ONNX.js machine learning models for intelligent HVAC system
 * optimization and energy efficiency recommendations.
 *
 * Features:
 * - Energy efficiency optimization
 * - Load prediction and balancing
 * - Equipment sizing recommendations
 * - Performance anomaly detection
 * - Cost optimization analysis
 * - Environmental impact assessment
 */
/* istanbul ignore next */
function cov_11pqkh4977() {
  var path = "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\frontend\\lib\\services\\AIOptimizationService.ts";
  var hash = "d423b2f4b26b33b47de8b47922c84cbf5ee5faad";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\frontend\\lib\\services\\AIOptimizationService.ts",
    statementMap: {
      "0": {
        start: {
          line: 16,
          column: 22
        },
        end: {
          line: 26,
          column: 3
        }
      },
      "1": {
        start: {
          line: 17,
          column: 4
        },
        end: {
          line: 17,
          column: 33
        }
      },
      "2": {
        start: {
          line: 17,
          column: 26
        },
        end: {
          line: 17,
          column: 33
        }
      },
      "3": {
        start: {
          line: 18,
          column: 15
        },
        end: {
          line: 18,
          column: 52
        }
      },
      "4": {
        start: {
          line: 19,
          column: 4
        },
        end: {
          line: 21,
          column: 5
        }
      },
      "5": {
        start: {
          line: 20,
          column: 6
        },
        end: {
          line: 20,
          column: 68
        }
      },
      "6": {
        start: {
          line: 20,
          column: 51
        },
        end: {
          line: 20,
          column: 63
        }
      },
      "7": {
        start: {
          line: 22,
          column: 4
        },
        end: {
          line: 22,
          column: 39
        }
      },
      "8": {
        start: {
          line: 24,
          column: 4
        },
        end: {
          line: 24,
          column: 33
        }
      },
      "9": {
        start: {
          line: 24,
          column: 26
        },
        end: {
          line: 24,
          column: 33
        }
      },
      "10": {
        start: {
          line: 25,
          column: 4
        },
        end: {
          line: 25,
          column: 17
        }
      },
      "11": {
        start: {
          line: 27,
          column: 25
        },
        end: {
          line: 31,
          column: 2
        }
      },
      "12": {
        start: {
          line: 28,
          column: 4
        },
        end: {
          line: 28,
          column: 72
        }
      },
      "13": {
        start: {
          line: 30,
          column: 4
        },
        end: {
          line: 30,
          column: 21
        }
      },
      "14": {
        start: {
          line: 32,
          column: 19
        },
        end: {
          line: 48,
          column: 4
        }
      },
      "15": {
        start: {
          line: 33,
          column: 18
        },
        end: {
          line: 40,
          column: 5
        }
      },
      "16": {
        start: {
          line: 34,
          column: 8
        },
        end: {
          line: 38,
          column: 10
        }
      },
      "17": {
        start: {
          line: 35,
          column: 21
        },
        end: {
          line: 35,
          column: 23
        }
      },
      "18": {
        start: {
          line: 36,
          column: 12
        },
        end: {
          line: 36,
          column: 95
        }
      },
      "19": {
        start: {
          line: 36,
          column: 29
        },
        end: {
          line: 36,
          column: 95
        }
      },
      "20": {
        start: {
          line: 36,
          column: 77
        },
        end: {
          line: 36,
          column: 95
        }
      },
      "21": {
        start: {
          line: 37,
          column: 12
        },
        end: {
          line: 37,
          column: 22
        }
      },
      "22": {
        start: {
          line: 39,
          column: 8
        },
        end: {
          line: 39,
          column: 26
        }
      },
      "23": {
        start: {
          line: 41,
          column: 4
        },
        end: {
          line: 47,
          column: 6
        }
      },
      "24": {
        start: {
          line: 42,
          column: 8
        },
        end: {
          line: 42,
          column: 46
        }
      },
      "25": {
        start: {
          line: 42,
          column: 35
        },
        end: {
          line: 42,
          column: 46
        }
      },
      "26": {
        start: {
          line: 43,
          column: 21
        },
        end: {
          line: 43,
          column: 23
        }
      },
      "27": {
        start: {
          line: 44,
          column: 8
        },
        end: {
          line: 44,
          column: 137
        }
      },
      "28": {
        start: {
          line: 44,
          column: 25
        },
        end: {
          line: 44,
          column: 137
        }
      },
      "29": {
        start: {
          line: 44,
          column: 38
        },
        end: {
          line: 44,
          column: 50
        }
      },
      "30": {
        start: {
          line: 44,
          column: 56
        },
        end: {
          line: 44,
          column: 57
        }
      },
      "31": {
        start: {
          line: 44,
          column: 78
        },
        end: {
          line: 44,
          column: 137
        }
      },
      "32": {
        start: {
          line: 44,
          column: 102
        },
        end: {
          line: 44,
          column: 137
        }
      },
      "33": {
        start: {
          line: 45,
          column: 8
        },
        end: {
          line: 45,
          column: 40
        }
      },
      "34": {
        start: {
          line: 46,
          column: 8
        },
        end: {
          line: 46,
          column: 22
        }
      },
      "35": {
        start: {
          line: 49,
          column: 0
        },
        end: {
          line: 49,
          column: 62
        }
      },
      "36": {
        start: {
          line: 50,
          column: 0
        },
        end: {
          line: 50,
          column: 39
        }
      },
      "37": {
        start: {
          line: 51,
          column: 0
        },
        end: {
          line: 51,
          column: 60
        }
      },
      "38": {
        start: {
          line: 52,
          column: 0
        },
        end: {
          line: 52,
          column: 46
        }
      },
      "39": {
        start: {
          line: 53,
          column: 16
        },
        end: {
          line: 53,
          column: 32
        }
      },
      "40": {
        start: {
          line: 54,
          column: 12
        },
        end: {
          line: 54,
          column: 52
        }
      },
      "41": {
        start: {
          line: 57,
          column: 8
        },
        end: {
          line: 57,
          column: 28
        }
      },
      "42": {
        start: {
          line: 58,
          column: 8
        },
        end: {
          line: 58,
          column: 36
        }
      },
      "43": {
        start: {
          line: 59,
          column: 8
        },
        end: {
          line: 59,
          column: 35
        }
      },
      "44": {
        start: {
          line: 60,
          column: 8
        },
        end: {
          line: 71,
          column: 10
        }
      },
      "45": {
        start: {
          line: 77,
          column: 8
        },
        end: {
          line: 93,
          column: 9
        }
      },
      "46": {
        start: {
          line: 79,
          column: 12
        },
        end: {
          line: 82,
          column: 13
        }
      },
      "47": {
        start: {
          line: 80,
          column: 16
        },
        end: {
          line: 80,
          column: 44
        }
      },
      "48": {
        start: {
          line: 81,
          column: 16
        },
        end: {
          line: 81,
          column: 41
        }
      },
      "49": {
        start: {
          line: 84,
          column: 12
        },
        end: {
          line: 84,
          column: 84
        }
      },
      "50": {
        start: {
          line: 86,
          column: 12
        },
        end: {
          line: 86,
          column: 47
        }
      },
      "51": {
        start: {
          line: 87,
          column: 12
        },
        end: {
          line: 87,
          column: 38
        }
      },
      "52": {
        start: {
          line: 88,
          column: 12
        },
        end: {
          line: 88,
          column: 76
        }
      },
      "53": {
        start: {
          line: 91,
          column: 12
        },
        end: {
          line: 91,
          column: 82
        }
      },
      "54": {
        start: {
          line: 92,
          column: 12
        },
        end: {
          line: 92,
          column: 24
        }
      },
      "55": {
        start: {
          line: 99,
          column: 8
        },
        end: {
          line: 101,
          column: 9
        }
      },
      "56": {
        start: {
          line: 100,
          column: 12
        },
        end: {
          line: 100,
          column: 71
        }
      },
      "57": {
        start: {
          line: 102,
          column: 8
        },
        end: {
          line: 116,
          column: 9
        }
      },
      "58": {
        start: {
          line: 104,
          column: 31
        },
        end: {
          line: 104,
          column: 66
        }
      },
      "59": {
        start: {
          line: 106,
          column: 28
        },
        end: {
          line: 106,
          column: 62
        }
      },
      "60": {
        start: {
          line: 108,
          column: 39
        },
        end: {
          line: 108,
          column: 80
        }
      },
      "61": {
        start: {
          line: 110,
          column: 36
        },
        end: {
          line: 110,
          column: 97
        }
      },
      "62": {
        start: {
          line: 111,
          column: 12
        },
        end: {
          line: 111,
          column: 35
        }
      },
      "63": {
        start: {
          line: 114,
          column: 12
        },
        end: {
          line: 114,
          column: 57
        }
      },
      "64": {
        start: {
          line: 115,
          column: 12
        },
        end: {
          line: 115,
          column: 24
        }
      },
      "65": {
        start: {
          line: 123,
          column: 28
        },
        end: {
          line: 123,
          column: 68
        }
      },
      "66": {
        start: {
          line: 124,
          column: 8
        },
        end: {
          line: 126,
          column: 9
        }
      },
      "67": {
        start: {
          line: 125,
          column: 12
        },
        end: {
          line: 125,
          column: 66
        }
      },
      "68": {
        start: {
          line: 127,
          column: 22
        },
        end: {
          line: 127,
          column: 89
        }
      },
      "69": {
        start: {
          line: 128,
          column: 24
        },
        end: {
          line: 128,
          column: 52
        }
      },
      "70": {
        start: {
          line: 129,
          column: 8
        },
        end: {
          line: 129,
          column: 59
        }
      },
      "71": {
        start: {
          line: 135,
          column: 29
        },
        end: {
          line: 135,
          column: 69
        }
      },
      "72": {
        start: {
          line: 136,
          column: 8
        },
        end: {
          line: 138,
          column: 9
        }
      },
      "73": {
        start: {
          line: 137,
          column: 12
        },
        end: {
          line: 137,
          column: 66
        }
      },
      "74": {
        start: {
          line: 139,
          column: 22
        },
        end: {
          line: 139,
          column: 63
        }
      },
      "75": {
        start: {
          line: 140,
          column: 24
        },
        end: {
          line: 140,
          column: 53
        }
      },
      "76": {
        start: {
          line: 141,
          column: 30
        },
        end: {
          line: 141,
          column: 69
        }
      },
      "77": {
        start: {
          line: 142,
          column: 33
        },
        end: {
          line: 142,
          column: 68
        }
      },
      "78": {
        start: {
          line: 143,
          column: 26
        },
        end: {
          line: 143,
          column: 123
        }
      },
      "79": {
        start: {
          line: 143,
          column: 62
        },
        end: {
          line: 143,
          column: 92
        }
      },
      "80": {
        start: {
          line: 143,
          column: 110
        },
        end: {
          line: 143,
          column: 122
        }
      },
      "81": {
        start: {
          line: 144,
          column: 8
        },
        end: {
          line: 147,
          column: 10
        }
      },
      "82": {
        start: {
          line: 146,
          column: 47
        },
        end: {
          line: 146,
          column: 70
        }
      },
      "83": {
        start: {
          line: 153,
          column: 28
        },
        end: {
          line: 153,
          column: 67
        }
      },
      "84": {
        start: {
          line: 154,
          column: 8
        },
        end: {
          line: 156,
          column: 9
        }
      },
      "85": {
        start: {
          line: 155,
          column: 12
        },
        end: {
          line: 155,
          column: 65
        }
      },
      "86": {
        start: {
          line: 157,
          column: 22
        },
        end: {
          line: 157,
          column: 77
        }
      },
      "87": {
        start: {
          line: 158,
          column: 24
        },
        end: {
          line: 158,
          column: 52
        }
      },
      "88": {
        start: {
          line: 159,
          column: 8
        },
        end: {
          line: 164,
          column: 10
        }
      },
      "89": {
        start: {
          line: 171,
          column: 32
        },
        end: {
          line: 171,
          column: 35
        }
      },
      "90": {
        start: {
          line: 172,
          column: 28
        },
        end: {
          line: 172,
          column: 96
        }
      },
      "91": {
        start: {
          line: 172,
          column: 75
        },
        end: {
          line: 172,
          column: 92
        }
      },
      "92": {
        start: {
          line: 173,
          column: 32
        },
        end: {
          line: 173,
          column: 61
        }
      },
      "93": {
        start: {
          line: 175,
          column: 35
        },
        end: {
          line: 175,
          column: 82
        }
      },
      "94": {
        start: {
          line: 177,
          column: 36
        },
        end: {
          line: 177,
          column: 118
        }
      },
      "95": {
        start: {
          line: 179,
          column: 32
        },
        end: {
          line: 179,
          column: 114
        }
      },
      "96": {
        start: {
          line: 180,
          column: 8
        },
        end: {
          line: 185,
          column: 10
        }
      },
      "97": {
        start: {
          line: 191,
          column: 25
        },
        end: {
          line: 191,
          column: 27
        }
      },
      "98": {
        start: {
          line: 192,
          column: 32
        },
        end: {
          line: 192,
          column: 34
        }
      },
      "99": {
        start: {
          line: 193,
          column: 32
        },
        end: {
          line: 193,
          column: 34
        }
      },
      "100": {
        start: {
          line: 195,
          column: 8
        },
        end: {
          line: 197,
          column: 9
        }
      },
      "101": {
        start: {
          line: 196,
          column: 12
        },
        end: {
          line: 196,
          column: 114
        }
      },
      "102": {
        start: {
          line: 198,
          column: 8
        },
        end: {
          line: 200,
          column: 9
        }
      },
      "103": {
        start: {
          line: 199,
          column: 12
        },
        end: {
          line: 199,
          column: 101
        }
      },
      "104": {
        start: {
          line: 202,
          column: 34
        },
        end: {
          line: 204,
          column: 66
        }
      },
      "105": {
        start: {
          line: 203,
          column: 28
        },
        end: {
          line: 203,
          column: 52
        }
      },
      "106": {
        start: {
          line: 204,
          column: 28
        },
        end: {
          line: 204,
          column: 65
        }
      },
      "107": {
        start: {
          line: 205,
          column: 8
        },
        end: {
          line: 205,
          column: 93
        }
      },
      "108": {
        start: {
          line: 205,
          column: 74
        },
        end: {
          line: 205,
          column: 90
        }
      },
      "109": {
        start: {
          line: 207,
          column: 32
        },
        end: {
          line: 209,
          column: 84
        }
      },
      "110": {
        start: {
          line: 208,
          column: 28
        },
        end: {
          line: 208,
          column: 64
        }
      },
      "111": {
        start: {
          line: 209,
          column: 28
        },
        end: {
          line: 209,
          column: 83
        }
      },
      "112": {
        start: {
          line: 210,
          column: 8
        },
        end: {
          line: 210,
          column: 91
        }
      },
      "113": {
        start: {
          line: 210,
          column: 72
        },
        end: {
          line: 210,
          column: 88
        }
      },
      "114": {
        start: {
          line: 211,
          column: 8
        },
        end: {
          line: 215,
          column: 10
        }
      },
      "115": {
        start: {
          line: 218,
          column: 23
        },
        end: {
          line: 223,
          column: 9
        }
      },
      "116": {
        start: {
          line: 224,
          column: 8
        },
        end: {
          line: 233,
          column: 9
        }
      },
      "117": {
        start: {
          line: 225,
          column: 12
        },
        end: {
          line: 232,
          column: 13
        }
      },
      "118": {
        start: {
          line: 226,
          column: 32
        },
        end: {
          line: 226,
          column: 77
        }
      },
      "119": {
        start: {
          line: 227,
          column: 16
        },
        end: {
          line: 227,
          column: 57
        }
      },
      "120": {
        start: {
          line: 228,
          column: 16
        },
        end: {
          line: 228,
          column: 58
        }
      },
      "121": {
        start: {
          line: 231,
          column: 16
        },
        end: {
          line: 231,
          column: 75
        }
      },
      "122": {
        start: {
          line: 237,
          column: 25
        },
        end: {
          line: 256,
          column: 9
        }
      },
      "123": {
        start: {
          line: 246,
          column: 80
        },
        end: {
          line: 246,
          column: 87
        }
      },
      "124": {
        start: {
          line: 247,
          column: 70
        },
        end: {
          line: 247,
          column: 77
        }
      },
      "125": {
        start: {
          line: 258,
          column: 26
        },
        end: {
          line: 258,
          column: 29
        }
      },
      "126": {
        start: {
          line: 259,
          column: 31
        },
        end: {
          line: 259,
          column: 59
        }
      },
      "127": {
        start: {
          line: 260,
          column: 8
        },
        end: {
          line: 262,
          column: 9
        }
      },
      "128": {
        start: {
          line: 261,
          column: 12
        },
        end: {
          line: 261,
          column: 35
        }
      },
      "129": {
        start: {
          line: 263,
          column: 8
        },
        end: {
          line: 265,
          column: 10
        }
      },
      "130": {
        start: {
          line: 269,
          column: 27
        },
        end: {
          line: 269,
          column: 46
        }
      },
      "131": {
        start: {
          line: 271,
          column: 32
        },
        end: {
          line: 271,
          column: 78
        }
      },
      "132": {
        start: {
          line: 272,
          column: 24
        },
        end: {
          line: 272,
          column: 64
        }
      },
      "133": {
        start: {
          line: 273,
          column: 35
        },
        end: {
          line: 273,
          column: 90
        }
      },
      "134": {
        start: {
          line: 274,
          column: 28
        },
        end: {
          line: 274,
          column: 74
        }
      },
      "135": {
        start: {
          line: 275,
          column: 31
        },
        end: {
          line: 275,
          column: 71
        }
      },
      "136": {
        start: {
          line: 276,
          column: 8
        },
        end: {
          line: 283,
          column: 10
        }
      },
      "137": {
        start: {
          line: 287,
          column: 32
        },
        end: {
          line: 287,
          column: 34
        }
      },
      "138": {
        start: {
          line: 289,
          column: 8
        },
        end: {
          line: 301,
          column: 9
        }
      },
      "139": {
        start: {
          line: 290,
          column: 12
        },
        end: {
          line: 300,
          column: 15
        }
      },
      "140": {
        start: {
          line: 302,
          column: 8
        },
        end: {
          line: 314,
          column: 9
        }
      },
      "141": {
        start: {
          line: 303,
          column: 12
        },
        end: {
          line: 313,
          column: 15
        }
      },
      "142": {
        start: {
          line: 315,
          column: 8
        },
        end: {
          line: 315,
          column: 31
        }
      },
      "143": {
        start: {
          line: 318,
          column: 34
        },
        end: {
          line: 318,
          column: 111
        }
      },
      "144": {
        start: {
          line: 318,
          column: 93
        },
        end: {
          line: 318,
          column: 100
        }
      },
      "145": {
        start: {
          line: 319,
          column: 8
        },
        end: {
          line: 323,
          column: 10
        }
      },
      "146": {
        start: {
          line: 326,
          column: 8
        },
        end: {
          line: 334,
          column: 12
        }
      },
      "147": {
        start: {
          line: 326,
          column: 52
        },
        end: {
          line: 334,
          column: 9
        }
      },
      "148": {
        start: {
          line: 337,
          column: 32
        },
        end: {
          line: 337,
          column: 88
        }
      },
      "149": {
        start: {
          line: 337,
          column: 59
        },
        end: {
          line: 337,
          column: 84
        }
      },
      "150": {
        start: {
          line: 338,
          column: 30
        },
        end: {
          line: 338,
          column: 42
        }
      },
      "151": {
        start: {
          line: 339,
          column: 8
        },
        end: {
          line: 345,
          column: 10
        }
      },
      "152": {
        start: {
          line: 348,
          column: 22
        },
        end: {
          line: 348,
          column: 24
        }
      },
      "153": {
        start: {
          line: 350,
          column: 26
        },
        end: {
          line: 350,
          column: 96
        }
      },
      "154": {
        start: {
          line: 350,
          column: 63
        },
        end: {
          line: 350,
          column: 92
        }
      },
      "155": {
        start: {
          line: 351,
          column: 8
        },
        end: {
          line: 359,
          column: 9
        }
      },
      "156": {
        start: {
          line: 352,
          column: 12
        },
        end: {
          line: 358,
          column: 15
        }
      },
      "157": {
        start: {
          line: 361,
          column: 30
        },
        end: {
          line: 361,
          column: 57
        }
      },
      "158": {
        start: {
          line: 362,
          column: 8
        },
        end: {
          line: 370,
          column: 9
        }
      },
      "159": {
        start: {
          line: 363,
          column: 12
        },
        end: {
          line: 369,
          column: 15
        }
      },
      "160": {
        start: {
          line: 371,
          column: 8
        },
        end: {
          line: 371,
          column: 21
        }
      },
      "161": {
        start: {
          line: 375,
          column: 25
        },
        end: {
          line: 379,
          column: 9
        }
      },
      "162": {
        start: {
          line: 380,
          column: 8
        },
        end: {
          line: 382,
          column: 10
        }
      },
      "163": {
        start: {
          line: 385,
          column: 25
        },
        end: {
          line: 390,
          column: 10
        }
      },
      "164": {
        start: {
          line: 385,
          column: 57
        },
        end: {
          line: 390,
          column: 9
        }
      },
      "165": {
        start: {
          line: 391,
          column: 8
        },
        end: {
          line: 393,
          column: 10
        }
      },
      "166": {
        start: {
          line: 396,
          column: 25
        },
        end: {
          line: 401,
          column: 9
        }
      },
      "167": {
        start: {
          line: 402,
          column: 8
        },
        end: {
          line: 404,
          column: 10
        }
      },
      "168": {
        start: {
          line: 408,
          column: 31
        },
        end: {
          line: 408,
          column: 92
        }
      },
      "169": {
        start: {
          line: 409,
          column: 8
        },
        end: {
          line: 409,
          column: 93
        }
      },
      "170": {
        start: {
          line: 409,
          column: 45
        },
        end: {
          line: 409,
          column: 91
        }
      },
      "171": {
        start: {
          line: 413,
          column: 8
        },
        end: {
          line: 413,
          column: 41
        }
      },
      "172": {
        start: {
          line: 417,
          column: 32
        },
        end: {
          line: 417,
          column: 75
        }
      },
      "173": {
        start: {
          line: 418,
          column: 28
        },
        end: {
          line: 418,
          column: 68
        }
      },
      "174": {
        start: {
          line: 419,
          column: 31
        },
        end: {
          line: 419,
          column: 49
        }
      },
      "175": {
        start: {
          line: 420,
          column: 8
        },
        end: {
          line: 420,
          column: 88
        }
      },
      "176": {
        start: {
          line: 423,
          column: 32
        },
        end: {
          line: 423,
          column: 34
        }
      },
      "177": {
        start: {
          line: 424,
          column: 8
        },
        end: {
          line: 426,
          column: 9
        }
      },
      "178": {
        start: {
          line: 425,
          column: 12
        },
        end: {
          line: 425,
          column: 84
        }
      },
      "179": {
        start: {
          line: 427,
          column: 8
        },
        end: {
          line: 430,
          column: 9
        }
      },
      "180": {
        start: {
          line: 428,
          column: 12
        },
        end: {
          line: 428,
          column: 68
        }
      },
      "181": {
        start: {
          line: 429,
          column: 12
        },
        end: {
          line: 429,
          column: 74
        }
      },
      "182": {
        start: {
          line: 431,
          column: 8
        },
        end: {
          line: 431,
          column: 81
        }
      },
      "183": {
        start: {
          line: 432,
          column: 8
        },
        end: {
          line: 432,
          column: 75
        }
      },
      "184": {
        start: {
          line: 433,
          column: 8
        },
        end: {
          line: 433,
          column: 31
        }
      },
      "185": {
        start: {
          line: 437,
          column: 41
        },
        end: {
          line: 447,
          column: 10
        }
      },
      "186": {
        start: {
          line: 439,
          column: 12
        },
        end: {
          line: 441,
          column: 13
        }
      },
      "187": {
        start: {
          line: 440,
          column: 16
        },
        end: {
          line: 440,
          column: 29
        }
      },
      "188": {
        start: {
          line: 443,
          column: 12
        },
        end: {
          line: 445,
          column: 13
        }
      },
      "189": {
        start: {
          line: 444,
          column: 16
        },
        end: {
          line: 444,
          column: 29
        }
      },
      "190": {
        start: {
          line: 446,
          column: 12
        },
        end: {
          line: 446,
          column: 24
        }
      },
      "191": {
        start: {
          line: 448,
          column: 8
        },
        end: {
          line: 451,
          column: 10
        }
      },
      "192": {
        start: {
          line: 454,
          column: 18
        },
        end: {
          line: 454,
          column: 29
        }
      },
      "193": {
        start: {
          line: 455,
          column: 8
        },
        end: {
          line: 457,
          column: 9
        }
      },
      "194": {
        start: {
          line: 455,
          column: 24
        },
        end: {
          line: 455,
          column: 25
        }
      },
      "195": {
        start: {
          line: 456,
          column: 12
        },
        end: {
          line: 456,
          column: 68
        }
      },
      "196": {
        start: {
          line: 458,
          column: 8
        },
        end: {
          line: 458,
          column: 19
        }
      },
      "197": {
        start: {
          line: 462,
          column: 8
        },
        end: {
          line: 462,
          column: 50
        }
      },
      "198": {
        start: {
          line: 468,
          column: 8
        },
        end: {
          line: 470,
          column: 9
        }
      },
      "199": {
        start: {
          line: 469,
          column: 12
        },
        end: {
          line: 469,
          column: 41
        }
      },
      "200": {
        start: {
          line: 471,
          column: 8
        },
        end: {
          line: 473,
          column: 9
        }
      },
      "201": {
        start: {
          line: 472,
          column: 12
        },
        end: {
          line: 472,
          column: 36
        }
      },
      "202": {
        start: {
          line: 474,
          column: 8
        },
        end: {
          line: 474,
          column: 32
        }
      },
      "203": {
        start: {
          line: 475,
          column: 8
        },
        end: {
          line: 475,
          column: 35
        }
      },
      "204": {
        start: {
          line: 478,
          column: 0
        },
        end: {
          line: 478,
          column: 54
        }
      },
      "205": {
        start: {
          line: 480,
          column: 28
        },
        end: {
          line: 480,
          column: 32
        }
      },
      "206": {
        start: {
          line: 482,
          column: 4
        },
        end: {
          line: 484,
          column: 5
        }
      },
      "207": {
        start: {
          line: 483,
          column: 8
        },
        end: {
          line: 483,
          column: 66
        }
      },
      "208": {
        start: {
          line: 485,
          column: 4
        },
        end: {
          line: 485,
          column: 33
        }
      },
      "209": {
        start: {
          line: 487,
          column: 0
        },
        end: {
          line: 487,
          column: 40
        }
      },
      "210": {
        start: {
          line: 490,
          column: 34
        },
        end: {
          line: 490,
          column: 61
        }
      },
      "211": {
        start: {
          line: 491,
          column: 46
        },
        end: {
          line: 491,
          column: 74
        }
      },
      "212": {
        start: {
          line: 492,
          column: 38
        },
        end: {
          line: 492,
          column: 66
        }
      },
      "213": {
        start: {
          line: 493,
          column: 30
        },
        end: {
          line: 493,
          column: 57
        }
      },
      "214": {
        start: {
          line: 494,
          column: 4
        },
        end: {
          line: 502,
          column: 17
        }
      },
      "215": {
        start: {
          line: 495,
          column: 8
        },
        end: {
          line: 501,
          column: 9
        }
      },
      "216": {
        start: {
          line: 496,
          column: 30
        },
        end: {
          line: 496,
          column: 63
        }
      },
      "217": {
        start: {
          line: 497,
          column: 12
        },
        end: {
          line: 497,
          column: 34
        }
      },
      "218": {
        start: {
          line: 498,
          column: 12
        },
        end: {
          line: 500,
          column: 53
        }
      },
      "219": {
        start: {
          line: 499,
          column: 28
        },
        end: {
          line: 499,
          column: 50
        }
      },
      "220": {
        start: {
          line: 500,
          column: 30
        },
        end: {
          line: 500,
          column: 51
        }
      },
      "221": {
        start: {
          line: 503,
          column: 27
        },
        end: {
          line: 521,
          column: 32
        }
      },
      "222": {
        start: {
          line: 504,
          column: 8
        },
        end: {
          line: 506,
          column: 9
        }
      },
      "223": {
        start: {
          line: 505,
          column: 12
        },
        end: {
          line: 505,
          column: 58
        }
      },
      "224": {
        start: {
          line: 507,
          column: 8
        },
        end: {
          line: 507,
          column: 27
        }
      },
      "225": {
        start: {
          line: 508,
          column: 8
        },
        end: {
          line: 508,
          column: 23
        }
      },
      "226": {
        start: {
          line: 509,
          column: 8
        },
        end: {
          line: 520,
          column: 9
        }
      },
      "227": {
        start: {
          line: 510,
          column: 27
        },
        end: {
          line: 510,
          column: 62
        }
      },
      "228": {
        start: {
          line: 511,
          column: 12
        },
        end: {
          line: 511,
          column: 26
        }
      },
      "229": {
        start: {
          line: 514,
          column: 33
        },
        end: {
          line: 514,
          column: 91
        }
      },
      "230": {
        start: {
          line: 515,
          column: 12
        },
        end: {
          line: 515,
          column: 35
        }
      },
      "231": {
        start: {
          line: 516,
          column: 12
        },
        end: {
          line: 516,
          column: 22
        }
      },
      "232": {
        start: {
          line: 519,
          column: 12
        },
        end: {
          line: 519,
          column: 32
        }
      },
      "233": {
        start: {
          line: 522,
          column: 26
        },
        end: {
          line: 527,
          column: 32
        }
      },
      "234": {
        start: {
          line: 523,
          column: 8
        },
        end: {
          line: 525,
          column: 9
        }
      },
      "235": {
        start: {
          line: 524,
          column: 12
        },
        end: {
          line: 524,
          column: 58
        }
      },
      "236": {
        start: {
          line: 526,
          column: 8
        },
        end: {
          line: 526,
          column: 92
        }
      },
      "237": {
        start: {
          line: 528,
          column: 28
        },
        end: {
          line: 533,
          column: 32
        }
      },
      "238": {
        start: {
          line: 529,
          column: 8
        },
        end: {
          line: 531,
          column: 9
        }
      },
      "239": {
        start: {
          line: 530,
          column: 12
        },
        end: {
          line: 530,
          column: 58
        }
      },
      "240": {
        start: {
          line: 532,
          column: 8
        },
        end: {
          line: 532,
          column: 67
        }
      },
      "241": {
        start: {
          line: 534,
          column: 4
        },
        end: {
          line: 542,
          column: 6
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 16,
            column: 74
          },
          end: {
            line: 16,
            column: 75
          }
        },
        loc: {
          start: {
            line: 16,
            column: 96
          },
          end: {
            line: 23,
            column: 1
          }
        },
        line: 16
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 20,
            column: 38
          },
          end: {
            line: 20,
            column: 39
          }
        },
        loc: {
          start: {
            line: 20,
            column: 49
          },
          end: {
            line: 20,
            column: 65
          }
        },
        line: 20
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 23,
            column: 6
          },
          end: {
            line: 23,
            column: 7
          }
        },
        loc: {
          start: {
            line: 23,
            column: 28
          },
          end: {
            line: 26,
            column: 1
          }
        },
        line: 23
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 27,
            column: 80
          },
          end: {
            line: 27,
            column: 81
          }
        },
        loc: {
          start: {
            line: 27,
            column: 95
          },
          end: {
            line: 29,
            column: 1
          }
        },
        line: 27
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 29,
            column: 5
          },
          end: {
            line: 29,
            column: 6
          }
        },
        loc: {
          start: {
            line: 29,
            column: 20
          },
          end: {
            line: 31,
            column: 1
          }
        },
        line: 29
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 32,
            column: 51
          },
          end: {
            line: 32,
            column: 52
          }
        },
        loc: {
          start: {
            line: 32,
            column: 63
          },
          end: {
            line: 48,
            column: 1
          }
        },
        line: 32
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 33,
            column: 18
          },
          end: {
            line: 33,
            column: 19
          }
        },
        loc: {
          start: {
            line: 33,
            column: 30
          },
          end: {
            line: 40,
            column: 5
          }
        },
        line: 33
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 34,
            column: 48
          },
          end: {
            line: 34,
            column: 49
          }
        },
        loc: {
          start: {
            line: 34,
            column: 61
          },
          end: {
            line: 38,
            column: 9
          }
        },
        line: 34
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 41,
            column: 11
          },
          end: {
            line: 41,
            column: 12
          }
        },
        loc: {
          start: {
            line: 41,
            column: 26
          },
          end: {
            line: 47,
            column: 5
          }
        },
        line: 41
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 56,
            column: 4
          },
          end: {
            line: 56,
            column: 5
          }
        },
        loc: {
          start: {
            line: 56,
            column: 24
          },
          end: {
            line: 72,
            column: 5
          }
        },
        line: 56
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 76,
            column: 4
          },
          end: {
            line: 76,
            column: 5
          }
        },
        loc: {
          start: {
            line: 76,
            column: 23
          },
          end: {
            line: 94,
            column: 5
          }
        },
        line: 76
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 98,
            column: 4
          },
          end: {
            line: 98,
            column: 5
          }
        },
        loc: {
          start: {
            line: 98,
            column: 32
          },
          end: {
            line: 117,
            column: 5
          }
        },
        line: 98
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 121,
            column: 4
          },
          end: {
            line: 121,
            column: 5
          }
        },
        loc: {
          start: {
            line: 122,
            column: 6
          },
          end: {
            line: 130,
            column: 5
          }
        },
        line: 122
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 134,
            column: 4
          },
          end: {
            line: 134,
            column: 5
          }
        },
        loc: {
          start: {
            line: 134,
            column: 60
          },
          end: {
            line: 148,
            column: 5
          }
        },
        line: 134
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 143,
            column: 44
          },
          end: {
            line: 143,
            column: 45
          }
        },
        loc: {
          start: {
            line: 143,
            column: 62
          },
          end: {
            line: 143,
            column: 92
          }
        },
        line: 143
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 143,
            column: 101
          },
          end: {
            line: 143,
            column: 102
          }
        },
        loc: {
          start: {
            line: 143,
            column: 110
          },
          end: {
            line: 143,
            column: 122
          }
        },
        line: 143
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 146,
            column: 38
          },
          end: {
            line: 146,
            column: 39
          }
        },
        loc: {
          start: {
            line: 146,
            column: 47
          },
          end: {
            line: 146,
            column: 70
          }
        },
        line: 146
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 152,
            column: 4
          },
          end: {
            line: 152,
            column: 5
          }
        },
        loc: {
          start: {
            line: 152,
            column: 67
          },
          end: {
            line: 165,
            column: 5
          }
        },
        line: 152
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 169,
            column: 4
          },
          end: {
            line: 169,
            column: 5
          }
        },
        loc: {
          start: {
            line: 169,
            column: 68
          },
          end: {
            line: 186,
            column: 5
          }
        },
        line: 169
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 172,
            column: 53
          },
          end: {
            line: 172,
            column: 54
          }
        },
        loc: {
          start: {
            line: 172,
            column: 75
          },
          end: {
            line: 172,
            column: 92
          }
        },
        line: 172
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 190,
            column: 4
          },
          end: {
            line: 190,
            column: 5
          }
        },
        loc: {
          start: {
            line: 190,
            column: 36
          },
          end: {
            line: 216,
            column: 5
          }
        },
        line: 190
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 203,
            column: 20
          },
          end: {
            line: 203,
            column: 21
          }
        },
        loc: {
          start: {
            line: 203,
            column: 28
          },
          end: {
            line: 203,
            column: 52
          }
        },
        line: 203
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 204,
            column: 18
          },
          end: {
            line: 204,
            column: 19
          }
        },
        loc: {
          start: {
            line: 204,
            column: 28
          },
          end: {
            line: 204,
            column: 65
          }
        },
        line: 204
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 205,
            column: 66
          },
          end: {
            line: 205,
            column: 67
          }
        },
        loc: {
          start: {
            line: 205,
            column: 74
          },
          end: {
            line: 205,
            column: 90
          }
        },
        line: 205
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 208,
            column: 20
          },
          end: {
            line: 208,
            column: 21
          }
        },
        loc: {
          start: {
            line: 208,
            column: 28
          },
          end: {
            line: 208,
            column: 64
          }
        },
        line: 208
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 209,
            column: 18
          },
          end: {
            line: 209,
            column: 19
          }
        },
        loc: {
          start: {
            line: 209,
            column: 28
          },
          end: {
            line: 209,
            column: 83
          }
        },
        line: 209
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 210,
            column: 64
          },
          end: {
            line: 210,
            column: 65
          }
        },
        loc: {
          start: {
            line: 210,
            column: 72
          },
          end: {
            line: 210,
            column: 88
          }
        },
        line: 210
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 217,
            column: 4
          },
          end: {
            line: 217,
            column: 5
          }
        },
        loc: {
          start: {
            line: 217,
            column: 34
          },
          end: {
            line: 234,
            column: 5
          }
        },
        line: 217
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 235,
            column: 4
          },
          end: {
            line: 235,
            column: 5
          }
        },
        loc: {
          start: {
            line: 235,
            column: 35
          },
          end: {
            line: 266,
            column: 5
          }
        },
        line: 235
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 246,
            column: 75
          },
          end: {
            line: 246,
            column: 76
          }
        },
        loc: {
          start: {
            line: 246,
            column: 80
          },
          end: {
            line: 246,
            column: 87
          }
        },
        line: 246
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 247,
            column: 65
          },
          end: {
            line: 247,
            column: 66
          }
        },
        loc: {
          start: {
            line: 247,
            column: 70
          },
          end: {
            line: 247,
            column: 77
          }
        },
        line: 247
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 267,
            column: 4
          },
          end: {
            line: 267,
            column: 5
          }
        },
        loc: {
          start: {
            line: 267,
            column: 41
          },
          end: {
            line: 284,
            column: 5
          }
        },
        line: 267
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 285,
            column: 4
          },
          end: {
            line: 285,
            column: 5
          }
        },
        loc: {
          start: {
            line: 285,
            column: 46
          },
          end: {
            line: 316,
            column: 5
          }
        },
        line: 285
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 317,
            column: 4
          },
          end: {
            line: 317,
            column: 5
          }
        },
        loc: {
          start: {
            line: 317,
            column: 40
          },
          end: {
            line: 324,
            column: 5
          }
        },
        line: 317
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 318,
            column: 81
          },
          end: {
            line: 318,
            column: 82
          }
        },
        loc: {
          start: {
            line: 318,
            column: 93
          },
          end: {
            line: 318,
            column: 100
          }
        },
        line: 318
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 325,
            column: 4
          },
          end: {
            line: 325,
            column: 5
          }
        },
        loc: {
          start: {
            line: 325,
            column: 55
          },
          end: {
            line: 335,
            column: 5
          }
        },
        line: 325
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 326,
            column: 35
          },
          end: {
            line: 326,
            column: 36
          }
        },
        loc: {
          start: {
            line: 326,
            column: 52
          },
          end: {
            line: 334,
            column: 9
          }
        },
        line: 326
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 336,
            column: 4
          },
          end: {
            line: 336,
            column: 5
          }
        },
        loc: {
          start: {
            line: 336,
            column: 32
          },
          end: {
            line: 346,
            column: 5
          }
        },
        line: 336
      },
      "38": {
        name: "(anonymous_38)",
        decl: {
          start: {
            line: 337,
            column: 44
          },
          end: {
            line: 337,
            column: 45
          }
        },
        loc: {
          start: {
            line: 337,
            column: 59
          },
          end: {
            line: 337,
            column: 84
          }
        },
        line: 337
      },
      "39": {
        name: "(anonymous_39)",
        decl: {
          start: {
            line: 347,
            column: 4
          },
          end: {
            line: 347,
            column: 5
          }
        },
        loc: {
          start: {
            line: 347,
            column: 40
          },
          end: {
            line: 372,
            column: 5
          }
        },
        line: 347
      },
      "40": {
        name: "(anonymous_40)",
        decl: {
          start: {
            line: 350,
            column: 49
          },
          end: {
            line: 350,
            column: 50
          }
        },
        loc: {
          start: {
            line: 350,
            column: 63
          },
          end: {
            line: 350,
            column: 92
          }
        },
        line: 350
      },
      "41": {
        name: "(anonymous_41)",
        decl: {
          start: {
            line: 373,
            column: 4
          },
          end: {
            line: 373,
            column: 5
          }
        },
        loc: {
          start: {
            line: 373,
            column: 67
          },
          end: {
            line: 383,
            column: 5
          }
        },
        line: 373
      },
      "42": {
        name: "(anonymous_42)",
        decl: {
          start: {
            line: 384,
            column: 4
          },
          end: {
            line: 384,
            column: 5
          }
        },
        loc: {
          start: {
            line: 384,
            column: 41
          },
          end: {
            line: 394,
            column: 5
          }
        },
        line: 384
      },
      "43": {
        name: "(anonymous_43)",
        decl: {
          start: {
            line: 385,
            column: 49
          },
          end: {
            line: 385,
            column: 50
          }
        },
        loc: {
          start: {
            line: 385,
            column: 57
          },
          end: {
            line: 390,
            column: 9
          }
        },
        line: 385
      },
      "44": {
        name: "(anonymous_44)",
        decl: {
          start: {
            line: 395,
            column: 4
          },
          end: {
            line: 395,
            column: 5
          }
        },
        loc: {
          start: {
            line: 395,
            column: 55
          },
          end: {
            line: 405,
            column: 5
          }
        },
        line: 395
      },
      "45": {
        name: "(anonymous_45)",
        decl: {
          start: {
            line: 406,
            column: 4
          },
          end: {
            line: 406,
            column: 5
          }
        },
        loc: {
          start: {
            line: 406,
            column: 31
          },
          end: {
            line: 410,
            column: 5
          }
        },
        line: 406
      },
      "46": {
        name: "(anonymous_46)",
        decl: {
          start: {
            line: 409,
            column: 36
          },
          end: {
            line: 409,
            column: 37
          }
        },
        loc: {
          start: {
            line: 409,
            column: 45
          },
          end: {
            line: 409,
            column: 91
          }
        },
        line: 409
      },
      "47": {
        name: "(anonymous_47)",
        decl: {
          start: {
            line: 411,
            column: 4
          },
          end: {
            line: 411,
            column: 5
          }
        },
        loc: {
          start: {
            line: 411,
            column: 47
          },
          end: {
            line: 414,
            column: 5
          }
        },
        line: 411
      },
      "48": {
        name: "(anonymous_48)",
        decl: {
          start: {
            line: 415,
            column: 4
          },
          end: {
            line: 415,
            column: 5
          }
        },
        loc: {
          start: {
            line: 415,
            column: 82
          },
          end: {
            line: 421,
            column: 5
          }
        },
        line: 415
      },
      "49": {
        name: "(anonymous_49)",
        decl: {
          start: {
            line: 422,
            column: 4
          },
          end: {
            line: 422,
            column: 5
          }
        },
        loc: {
          start: {
            line: 422,
            column: 82
          },
          end: {
            line: 434,
            column: 5
          }
        },
        line: 422
      },
      "50": {
        name: "(anonymous_50)",
        decl: {
          start: {
            line: 435,
            column: 4
          },
          end: {
            line: 435,
            column: 5
          }
        },
        loc: {
          start: {
            line: 435,
            column: 49
          },
          end: {
            line: 452,
            column: 5
          }
        },
        line: 435
      },
      "51": {
        name: "(anonymous_51)",
        decl: {
          start: {
            line: 437,
            column: 71
          },
          end: {
            line: 437,
            column: 72
          }
        },
        loc: {
          start: {
            line: 437,
            column: 78
          },
          end: {
            line: 447,
            column: 9
          }
        },
        line: 437
      },
      "52": {
        name: "(anonymous_52)",
        decl: {
          start: {
            line: 453,
            column: 4
          },
          end: {
            line: 453,
            column: 5
          }
        },
        loc: {
          start: {
            line: 453,
            column: 65
          },
          end: {
            line: 459,
            column: 5
          }
        },
        line: 453
      },
      "53": {
        name: "(anonymous_53)",
        decl: {
          start: {
            line: 460,
            column: 4
          },
          end: {
            line: 460,
            column: 5
          }
        },
        loc: {
          start: {
            line: 460,
            column: 51
          },
          end: {
            line: 463,
            column: 5
          }
        },
        line: 460
      },
      "54": {
        name: "(anonymous_54)",
        decl: {
          start: {
            line: 467,
            column: 4
          },
          end: {
            line: 467,
            column: 5
          }
        },
        loc: {
          start: {
            line: 467,
            column: 20
          },
          end: {
            line: 476,
            column: 5
          }
        },
        line: 467
      },
      "55": {
        name: "getAIOptimizationService",
        decl: {
          start: {
            line: 481,
            column: 9
          },
          end: {
            line: 481,
            column: 33
          }
        },
        loc: {
          start: {
            line: 481,
            column: 42
          },
          end: {
            line: 486,
            column: 1
          }
        },
        line: 481
      },
      "56": {
        name: "useAIOptimization",
        decl: {
          start: {
            line: 489,
            column: 9
          },
          end: {
            line: 489,
            column: 26
          }
        },
        loc: {
          start: {
            line: 489,
            column: 35
          },
          end: {
            line: 543,
            column: 1
          }
        },
        line: 489
      },
      "57": {
        name: "(anonymous_57)",
        decl: {
          start: {
            line: 494,
            column: 27
          },
          end: {
            line: 494,
            column: 28
          }
        },
        loc: {
          start: {
            line: 494,
            column: 33
          },
          end: {
            line: 502,
            column: 5
          }
        },
        line: 494
      },
      "58": {
        name: "(anonymous_58)",
        decl: {
          start: {
            line: 499,
            column: 22
          },
          end: {
            line: 499,
            column: 23
          }
        },
        loc: {
          start: {
            line: 499,
            column: 28
          },
          end: {
            line: 499,
            column: 50
          }
        },
        line: 499
      },
      "59": {
        name: "(anonymous_59)",
        decl: {
          start: {
            line: 500,
            column: 23
          },
          end: {
            line: 500,
            column: 24
          }
        },
        loc: {
          start: {
            line: 500,
            column: 30
          },
          end: {
            line: 500,
            column: 51
          }
        },
        line: 500
      },
      "60": {
        name: "(anonymous_60)",
        decl: {
          start: {
            line: 503,
            column: 52
          },
          end: {
            line: 503,
            column: 53
          }
        },
        loc: {
          start: {
            line: 503,
            column: 69
          },
          end: {
            line: 521,
            column: 5
          }
        },
        line: 503
      },
      "61": {
        name: "(anonymous_61)",
        decl: {
          start: {
            line: 522,
            column: 51
          },
          end: {
            line: 522,
            column: 52
          }
        },
        loc: {
          start: {
            line: 522,
            column: 105
          },
          end: {
            line: 527,
            column: 5
          }
        },
        line: 522
      },
      "62": {
        name: "(anonymous_62)",
        decl: {
          start: {
            line: 528,
            column: 53
          },
          end: {
            line: 528,
            column: 54
          }
        },
        loc: {
          start: {
            line: 528,
            column: 91
          },
          end: {
            line: 533,
            column: 5
          }
        },
        line: 528
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 16,
            column: 22
          },
          end: {
            line: 26,
            column: 3
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 16,
            column: 23
          },
          end: {
            line: 16,
            column: 27
          }
        }, {
          start: {
            line: 16,
            column: 31
          },
          end: {
            line: 16,
            column: 51
          }
        }, {
          start: {
            line: 16,
            column: 57
          },
          end: {
            line: 26,
            column: 2
          }
        }],
        line: 16
      },
      "1": {
        loc: {
          start: {
            line: 16,
            column: 57
          },
          end: {
            line: 26,
            column: 2
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 16,
            column: 74
          },
          end: {
            line: 23,
            column: 1
          }
        }, {
          start: {
            line: 23,
            column: 6
          },
          end: {
            line: 26,
            column: 1
          }
        }],
        line: 16
      },
      "2": {
        loc: {
          start: {
            line: 17,
            column: 4
          },
          end: {
            line: 17,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 17,
            column: 4
          },
          end: {
            line: 17,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 17
      },
      "3": {
        loc: {
          start: {
            line: 19,
            column: 4
          },
          end: {
            line: 21,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 19,
            column: 4
          },
          end: {
            line: 21,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 19
      },
      "4": {
        loc: {
          start: {
            line: 19,
            column: 8
          },
          end: {
            line: 19,
            column: 85
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 19,
            column: 8
          },
          end: {
            line: 19,
            column: 13
          }
        }, {
          start: {
            line: 19,
            column: 18
          },
          end: {
            line: 19,
            column: 84
          }
        }],
        line: 19
      },
      "5": {
        loc: {
          start: {
            line: 19,
            column: 18
          },
          end: {
            line: 19,
            column: 84
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 19,
            column: 34
          },
          end: {
            line: 19,
            column: 47
          }
        }, {
          start: {
            line: 19,
            column: 50
          },
          end: {
            line: 19,
            column: 84
          }
        }],
        line: 19
      },
      "6": {
        loc: {
          start: {
            line: 19,
            column: 50
          },
          end: {
            line: 19,
            column: 84
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 19,
            column: 50
          },
          end: {
            line: 19,
            column: 63
          }
        }, {
          start: {
            line: 19,
            column: 67
          },
          end: {
            line: 19,
            column: 84
          }
        }],
        line: 19
      },
      "7": {
        loc: {
          start: {
            line: 24,
            column: 4
          },
          end: {
            line: 24,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 24,
            column: 4
          },
          end: {
            line: 24,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 24
      },
      "8": {
        loc: {
          start: {
            line: 27,
            column: 25
          },
          end: {
            line: 31,
            column: 2
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 27,
            column: 26
          },
          end: {
            line: 27,
            column: 30
          }
        }, {
          start: {
            line: 27,
            column: 34
          },
          end: {
            line: 27,
            column: 57
          }
        }, {
          start: {
            line: 27,
            column: 63
          },
          end: {
            line: 31,
            column: 1
          }
        }],
        line: 27
      },
      "9": {
        loc: {
          start: {
            line: 27,
            column: 63
          },
          end: {
            line: 31,
            column: 1
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 27,
            column: 80
          },
          end: {
            line: 29,
            column: 1
          }
        }, {
          start: {
            line: 29,
            column: 5
          },
          end: {
            line: 31,
            column: 1
          }
        }],
        line: 27
      },
      "10": {
        loc: {
          start: {
            line: 32,
            column: 19
          },
          end: {
            line: 48,
            column: 4
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 32,
            column: 20
          },
          end: {
            line: 32,
            column: 24
          }
        }, {
          start: {
            line: 32,
            column: 28
          },
          end: {
            line: 32,
            column: 45
          }
        }, {
          start: {
            line: 32,
            column: 50
          },
          end: {
            line: 48,
            column: 4
          }
        }],
        line: 32
      },
      "11": {
        loc: {
          start: {
            line: 34,
            column: 18
          },
          end: {
            line: 38,
            column: 9
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 34,
            column: 18
          },
          end: {
            line: 34,
            column: 44
          }
        }, {
          start: {
            line: 34,
            column: 48
          },
          end: {
            line: 38,
            column: 9
          }
        }],
        line: 34
      },
      "12": {
        loc: {
          start: {
            line: 36,
            column: 29
          },
          end: {
            line: 36,
            column: 95
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 36,
            column: 29
          },
          end: {
            line: 36,
            column: 95
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 36
      },
      "13": {
        loc: {
          start: {
            line: 42,
            column: 8
          },
          end: {
            line: 42,
            column: 46
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 42,
            column: 8
          },
          end: {
            line: 42,
            column: 46
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 42
      },
      "14": {
        loc: {
          start: {
            line: 42,
            column: 12
          },
          end: {
            line: 42,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 42,
            column: 12
          },
          end: {
            line: 42,
            column: 15
          }
        }, {
          start: {
            line: 42,
            column: 19
          },
          end: {
            line: 42,
            column: 33
          }
        }],
        line: 42
      },
      "15": {
        loc: {
          start: {
            line: 44,
            column: 8
          },
          end: {
            line: 44,
            column: 137
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 44,
            column: 8
          },
          end: {
            line: 44,
            column: 137
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 44
      },
      "16": {
        loc: {
          start: {
            line: 44,
            column: 78
          },
          end: {
            line: 44,
            column: 137
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 44,
            column: 78
          },
          end: {
            line: 44,
            column: 137
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 44
      },
      "17": {
        loc: {
          start: {
            line: 79,
            column: 12
          },
          end: {
            line: 82,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 79,
            column: 12
          },
          end: {
            line: 82,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 79
      },
      "18": {
        loc: {
          start: {
            line: 99,
            column: 8
          },
          end: {
            line: 101,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 99,
            column: 8
          },
          end: {
            line: 101,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 99
      },
      "19": {
        loc: {
          start: {
            line: 99,
            column: 12
          },
          end: {
            line: 99,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 99,
            column: 12
          },
          end: {
            line: 99,
            column: 31
          }
        }, {
          start: {
            line: 99,
            column: 35
          },
          end: {
            line: 99,
            column: 48
          }
        }],
        line: 99
      },
      "20": {
        loc: {
          start: {
            line: 121,
            column: 66
          },
          end: {
            line: 121,
            column: 82
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 121,
            column: 80
          },
          end: {
            line: 121,
            column: 82
          }
        }],
        line: 121
      },
      "21": {
        loc: {
          start: {
            line: 124,
            column: 8
          },
          end: {
            line: 126,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 124,
            column: 8
          },
          end: {
            line: 126,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 124
      },
      "22": {
        loc: {
          start: {
            line: 134,
            column: 43
          },
          end: {
            line: 134,
            column: 58
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 134,
            column: 55
          },
          end: {
            line: 134,
            column: 58
          }
        }],
        line: 134
      },
      "23": {
        loc: {
          start: {
            line: 136,
            column: 8
          },
          end: {
            line: 138,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 136,
            column: 8
          },
          end: {
            line: 138,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 136
      },
      "24": {
        loc: {
          start: {
            line: 143,
            column: 62
          },
          end: {
            line: 143,
            column: 92
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 143,
            column: 82
          },
          end: {
            line: 143,
            column: 87
          }
        }, {
          start: {
            line: 143,
            column: 90
          },
          end: {
            line: 143,
            column: 92
          }
        }],
        line: 143
      },
      "25": {
        loc: {
          start: {
            line: 154,
            column: 8
          },
          end: {
            line: 156,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 154,
            column: 8
          },
          end: {
            line: 156,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 154
      },
      "26": {
        loc: {
          start: {
            line: 195,
            column: 8
          },
          end: {
            line: 197,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 195,
            column: 8
          },
          end: {
            line: 197,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 195
      },
      "27": {
        loc: {
          start: {
            line: 198,
            column: 8
          },
          end: {
            line: 200,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 198,
            column: 8
          },
          end: {
            line: 200,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 198
      },
      "28": {
        loc: {
          start: {
            line: 289,
            column: 8
          },
          end: {
            line: 301,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 289,
            column: 8
          },
          end: {
            line: 301,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 289
      },
      "29": {
        loc: {
          start: {
            line: 302,
            column: 8
          },
          end: {
            line: 314,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 302,
            column: 8
          },
          end: {
            line: 314,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 302
      },
      "30": {
        loc: {
          start: {
            line: 332,
            column: 26
          },
          end: {
            line: 332,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 332,
            column: 38
          },
          end: {
            line: 332,
            column: 69
          }
        }, {
          start: {
            line: 332,
            column: 72
          },
          end: {
            line: 332,
            column: 74
          }
        }],
        line: 332
      },
      "31": {
        loc: {
          start: {
            line: 351,
            column: 8
          },
          end: {
            line: 359,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 351,
            column: 8
          },
          end: {
            line: 359,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 351
      },
      "32": {
        loc: {
          start: {
            line: 362,
            column: 8
          },
          end: {
            line: 370,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 362,
            column: 8
          },
          end: {
            line: 370,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 362
      },
      "33": {
        loc: {
          start: {
            line: 409,
            column: 45
          },
          end: {
            line: 409,
            column: 91
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 409,
            column: 45
          },
          end: {
            line: 409,
            column: 78
          }
        }, {
          start: {
            line: 409,
            column: 82
          },
          end: {
            line: 409,
            column: 91
          }
        }],
        line: 409
      },
      "34": {
        loc: {
          start: {
            line: 417,
            column: 41
          },
          end: {
            line: 417,
            column: 69
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 417,
            column: 41
          },
          end: {
            line: 417,
            column: 62
          }
        }, {
          start: {
            line: 417,
            column: 66
          },
          end: {
            line: 417,
            column: 69
          }
        }],
        line: 417
      },
      "35": {
        loc: {
          start: {
            line: 424,
            column: 8
          },
          end: {
            line: 426,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 424,
            column: 8
          },
          end: {
            line: 426,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 424
      },
      "36": {
        loc: {
          start: {
            line: 427,
            column: 8
          },
          end: {
            line: 430,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 427,
            column: 8
          },
          end: {
            line: 430,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 427
      },
      "37": {
        loc: {
          start: {
            line: 439,
            column: 12
          },
          end: {
            line: 441,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 439,
            column: 12
          },
          end: {
            line: 441,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 439
      },
      "38": {
        loc: {
          start: {
            line: 443,
            column: 12
          },
          end: {
            line: 445,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 443,
            column: 12
          },
          end: {
            line: 445,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 443
      },
      "39": {
        loc: {
          start: {
            line: 468,
            column: 8
          },
          end: {
            line: 470,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 468,
            column: 8
          },
          end: {
            line: 470,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 468
      },
      "40": {
        loc: {
          start: {
            line: 482,
            column: 4
          },
          end: {
            line: 484,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 482,
            column: 4
          },
          end: {
            line: 484,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 482
      },
      "41": {
        loc: {
          start: {
            line: 482,
            column: 8
          },
          end: {
            line: 482,
            column: 40
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 482,
            column: 8
          },
          end: {
            line: 482,
            column: 30
          }
        }, {
          start: {
            line: 482,
            column: 34
          },
          end: {
            line: 482,
            column: 40
          }
        }],
        line: 482
      },
      "42": {
        loc: {
          start: {
            line: 495,
            column: 8
          },
          end: {
            line: 501,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 495,
            column: 8
          },
          end: {
            line: 501,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 495
      },
      "43": {
        loc: {
          start: {
            line: 504,
            column: 8
          },
          end: {
            line: 506,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 504,
            column: 8
          },
          end: {
            line: 506,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 504
      },
      "44": {
        loc: {
          start: {
            line: 504,
            column: 12
          },
          end: {
            line: 504,
            column: 38
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 504,
            column: 12
          },
          end: {
            line: 504,
            column: 20
          }
        }, {
          start: {
            line: 504,
            column: 24
          },
          end: {
            line: 504,
            column: 38
          }
        }],
        line: 504
      },
      "45": {
        loc: {
          start: {
            line: 514,
            column: 33
          },
          end: {
            line: 514,
            column: 91
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 514,
            column: 56
          },
          end: {
            line: 514,
            column: 67
          }
        }, {
          start: {
            line: 514,
            column: 70
          },
          end: {
            line: 514,
            column: 91
          }
        }],
        line: 514
      },
      "46": {
        loc: {
          start: {
            line: 523,
            column: 8
          },
          end: {
            line: 525,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 523,
            column: 8
          },
          end: {
            line: 525,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 523
      },
      "47": {
        loc: {
          start: {
            line: 523,
            column: 12
          },
          end: {
            line: 523,
            column: 38
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 523,
            column: 12
          },
          end: {
            line: 523,
            column: 20
          }
        }, {
          start: {
            line: 523,
            column: 24
          },
          end: {
            line: 523,
            column: 38
          }
        }],
        line: 523
      },
      "48": {
        loc: {
          start: {
            line: 529,
            column: 8
          },
          end: {
            line: 531,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 529,
            column: 8
          },
          end: {
            line: 531,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 529
      },
      "49": {
        loc: {
          start: {
            line: 529,
            column: 12
          },
          end: {
            line: 529,
            column: 38
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 529,
            column: 12
          },
          end: {
            line: 529,
            column: 20
          }
        }, {
          start: {
            line: 529,
            column: 24
          },
          end: {
            line: 529,
            column: 38
          }
        }],
        line: 529
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0,
      "210": 0,
      "211": 0,
      "212": 0,
      "213": 0,
      "214": 0,
      "215": 0,
      "216": 0,
      "217": 0,
      "218": 0,
      "219": 0,
      "220": 0,
      "221": 0,
      "222": 0,
      "223": 0,
      "224": 0,
      "225": 0,
      "226": 0,
      "227": 0,
      "228": 0,
      "229": 0,
      "230": 0,
      "231": 0,
      "232": 0,
      "233": 0,
      "234": 0,
      "235": 0,
      "236": 0,
      "237": 0,
      "238": 0,
      "239": 0,
      "240": 0,
      "241": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0, 0],
      "9": [0, 0],
      "10": [0, 0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0],
      "21": [0, 0],
      "22": [0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\frontend\\lib\\services\\AIOptimizationService.ts",
      mappings: ";AAAA;;;;;;;;;;;;;GAaG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0rBH,4DAKC;AAKD,8CAqEC;AAvwBD,iCAAyD;AACzD,qDAAuC;AAsJvC,MAAa,qBAAqB;IAMhC,YAAY,MAA4B;QALhC,YAAO,GAAgC,IAAI,CAAC;QAE5C,eAAU,GAAsC,IAAI,GAAG,EAAE,CAAC;QAC1D,kBAAa,GAAG,KAAK,CAAC;QAG5B,IAAI,CAAC,MAAM,GAAG;YACZ,SAAS,EAAE,KAAK;YAChB,SAAS,EAAE,CAAC;YACZ,mBAAmB,EAAE,GAAG;YACxB,iBAAiB,EAAE;gBACjB,EAAE,IAAI,EAAE,mBAAmB,EAAE,MAAM,EAAE,GAAG,EAAE;gBAC1C,EAAE,IAAI,EAAE,gBAAgB,EAAE,MAAM,EAAE,GAAG,EAAE;gBACvC,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,EAAE;gBAChC,EAAE,IAAI,EAAE,eAAe,EAAE,MAAM,EAAE,GAAG,EAAE;aACvC;YACD,GAAG,MAAM;SACV,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACd,IAAI,CAAC;YACH,yBAAyB;YACzB,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;gBAC1B,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;gBAC5B,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;YAC3B,CAAC;YAED,+BAA+B;YAC/B,IAAI,CAAC,OAAO,GAAG,MAAM,GAAG,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAExE,0BAA0B;YAC1B,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAEnC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAC;QAElE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,+CAA+C,EAAE,KAAK,CAAC,CAAC;YACtE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,KAAwB;QAC3C,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YACzC,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,CAAC;YACH,qBAAqB;YACrB,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAEvD,gBAAgB;YAChB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAEnD,kBAAkB;YAClB,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YAErE,2BAA2B;YAC3B,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;YAEtF,OAAO,eAAe,CAAC;QAEzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;YAC7C,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,wBAAwB,CAC5B,UAAsB,EACtB,iBAAoC,EACpC,cAAsB,EAAE,CAAC,QAAQ;;QAEjC,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;QAC7D,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;QACxD,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,UAAU,EAAE,iBAAiB,EAAE,WAAW,CAAC,CAAC;QAClF,MAAM,OAAO,GAAG,MAAM,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAE7C,OAAO,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,IAAoB,CAAC,CAAC;IACrE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CACnB,eAAqC,EACrC,YAAoB,GAAG;QAEvB,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;QAC9D,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;QACxD,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC;QACxD,MAAM,OAAO,GAAG,MAAM,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAE9C,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAoB,CAAC,CAAC;QAC9E,MAAM,gBAAgB,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAoB,CAAC,CAAC;QAE7E,MAAM,SAAS,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,CACnD,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAC/B,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC;QAEhC,OAAO;YACL,SAAS;YACT,UAAU,EAAE,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;SAC5D,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,wBAAwB,CAC5B,YAA0B,EAC1B,gBAA0B;QAO1B,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;QAC5D,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACvD,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,gBAAgB,CAAC,CAAC;QACtE,MAAM,OAAO,GAAG,MAAM,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAE7C,OAAO;YACL,SAAS,EAAE,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,eAAe,CAAC,IAAoB,CAAC;YAClF,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAoB,CAAC;YAC/D,kBAAkB,EAAE,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,IAAoB,CAAC;YAC/E,cAAc,EAAE,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAoB,CAAC;SACxE,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,0BAA0B,CAC9B,UAAsB,EACtB,iBAA2B;QAO3B,6BAA6B;QAC7B,MAAM,eAAe,GAAG,GAAG,CAAC,CAAC,4BAA4B;QACzD,MAAM,WAAW,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,WAAW,EAAE,EAAE,CAAC,GAAG,GAAG,WAAW,EAAE,CAAC,CAAC,CAAC;QACzF,MAAM,eAAe,GAAG,WAAW,GAAG,eAAe,CAAC;QAEtD,6BAA6B;QAC7B,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,UAAU,CAAC,CAAC;QAE3E,iCAAiC;QACjC,MAAM,mBAAmB,GAAG,IAAI,CAAC,4BAA4B,CAC3D,UAAU,EACV,eAAe,EACf,kBAAkB,CACnB,CAAC;QAEF,2BAA2B;QAC3B,MAAM,eAAe,GAAG,IAAI,CAAC,oCAAoC,CAC/D,mBAAmB,EACnB,kBAAkB,CACnB,CAAC;QAEF,OAAO;YACL,gBAAgB,EAAE,eAAe;YACjC,mBAAmB,EAAE,kBAAkB;YACvC,oBAAoB,EAAE,mBAAmB;YACzC,eAAe;SAChB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,uBAAuB,CAAC,MAA0B;QAKhD,MAAM,QAAQ,GAAa,EAAE,CAAC;QAC9B,MAAM,eAAe,GAAa,EAAE,CAAC;QACrC,MAAM,eAAe,GAAa,EAAE,CAAC;QAErC,4BAA4B;QAC5B,IAAI,MAAM,CAAC,iBAAiB,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YACzC,QAAQ,CAAC,IAAI,CAAC,yCAAyC,MAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACxG,CAAC;QAED,IAAI,MAAM,CAAC,iBAAiB,CAAC,IAAI,GAAG,IAAI,EAAE,CAAC;YACzC,QAAQ,CAAC,IAAI,CAAC,yBAAyB,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;QAC3F,CAAC;QAED,4BAA4B;QAC5B,MAAM,iBAAiB,GAAG,MAAM,CAAC,mBAAmB;aACjD,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,KAAK,MAAM,CAAC;aACxC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,eAAe,GAAG,CAAC,CAAC,eAAe,CAAC,CAAC;QAEzD,eAAe,CAAC,IAAI,CAAC,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;QAErF,4BAA4B;QAC5B,MAAM,eAAe,GAAG,MAAM,CAAC,eAAe;aAC3C,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC;aACpD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;QAE3E,eAAe,CAAC,IAAI,CAAC,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;QAEnF,OAAO;YACL,YAAY,EAAE,QAAQ;YACtB,gBAAgB,EAAE,eAAe;YACjC,gBAAgB,EAAE,eAAe;SAClC,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,qBAAqB;QACjC,MAAM,MAAM,GAAG;YACb,EAAE,IAAI,EAAE,mBAAmB,EAAE,IAAI,EAAE,gCAAgC,EAAE;YACrE,EAAE,IAAI,EAAE,mBAAmB,EAAE,IAAI,EAAE,gCAAgC,EAAE;YACrE,EAAE,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE,+BAA+B,EAAE;YACnE,EAAE,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE,+BAA+B,EAAE;SACpE,CAAC;QAEF,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,IAAI,CAAC;gBACH,MAAM,OAAO,GAAG,MAAM,GAAG,CAAC,gBAAgB,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBAC9D,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;gBACzC,OAAO,CAAC,GAAG,CAAC,UAAU,KAAK,CAAC,IAAI,QAAQ,CAAC,CAAC;YAC5C,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,IAAI,CAAC,kBAAkB,KAAK,CAAC,IAAI,SAAS,EAAE,KAAK,CAAC,CAAC;YAC7D,CAAC;QACH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,KAAwB;QACtD,iDAAiD;QACjD,MAAM,QAAQ,GAAG;YACf,oBAAoB;YACpB,KAAK,CAAC,YAAY,CAAC,IAAI,GAAG,KAAK,EAAE,iBAAiB;YAClD,KAAK,CAAC,YAAY,CAAC,MAAM,GAAG,MAAM,EAAE,mBAAmB;YACvD,KAAK,CAAC,YAAY,CAAC,SAAS,GAAG,IAAI,EAAE,sBAAsB;YAC3D,KAAK,CAAC,YAAY,CAAC,UAAU,GAAG,EAAE,EAAE,oBAAoB;YACxD,KAAK,CAAC,YAAY,CAAC,MAAM,GAAG,EAAE,EAAE,mBAAmB;YACnD,KAAK,CAAC,YAAY,CAAC,SAAS,GAAG,EAAE,EAAE,kBAAkB;YAErD,yBAAyB;YACzB,GAAG,KAAK,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC;YAC5E,GAAG,KAAK,CAAC,iBAAiB,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC;YAElE,uBAAuB;YACvB,KAAK,CAAC,eAAe,CAAC,WAAW,GAAG,IAAI,EAAE,iBAAiB;YAC3D,KAAK,CAAC,eAAe,CAAC,kBAAkB,CAAC,GAAG,GAAG,EAAE;YACjD,KAAK,CAAC,eAAe,CAAC,kBAAkB,CAAC,GAAG,GAAG,EAAE;YACjD,KAAK,CAAC,eAAe,CAAC,kBAAkB,CAAC,oBAAoB;YAE7D,sBAAsB;YACtB,KAAK,CAAC,WAAW,CAAC,MAAM,GAAG,MAAM,EAAE,mBAAmB;YACtD,KAAK,CAAC,WAAW,CAAC,QAAQ,GAAG,GAAG,EAAE,qBAAqB;SACxD,CAAC;QAEF,yCAAyC;QACzC,MAAM,SAAS,GAAG,GAAG,CAAC,CAAC,4BAA4B;QACnD,MAAM,cAAc,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;QACpD,OAAO,cAAc,CAAC,MAAM,GAAG,SAAS,EAAE,CAAC;YACzC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC;QAED,OAAO;YACL,KAAK,EAAE,IAAI,GAAG,CAAC,MAAM,CAAC,SAAS,EAAE,IAAI,YAAY,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;SACnF,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,OAA8C,EAAE,KAAwB;QACnG,wDAAwD;QACxD,MAAM,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC,IAAoB,CAAC;QAEvD,6CAA6C;QAC7C,MAAM,eAAe,GAAG,IAAI,CAAC,sBAAsB,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QACvE,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QACzD,MAAM,kBAAkB,GAAG,IAAI,CAAC,0BAA0B,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;QACnF,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC;QACnE,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;QAEhE,OAAO;YACL,eAAe;YACf,iBAAiB,EAAE,OAAO;YAC1B,gBAAgB,EAAE,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC;YAC9C,mBAAmB,EAAE,kBAAkB;YACvC,YAAY,EAAE,WAAW;YACzB,eAAe,EAAE,cAAc;SAChC,CAAC;IACJ,CAAC;IAEO,sBAAsB,CAAC,UAAwB,EAAE,KAAwB;QAC/E,uDAAuD;QACvD,MAAM,eAAe,GAAiC,EAAE,CAAC;QAEzD,gFAAgF;QAChF,IAAI,UAAU,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC;YACxB,eAAe,CAAC,IAAI,CAAC;gBACnB,EAAE,EAAE,cAAc;gBAClB,IAAI,EAAE,mBAAmB;gBACzB,WAAW,EAAE,6EAA6E;gBAC1F,QAAQ,EAAE,MAAM;gBAChB,mBAAmB,EAAE,UAAU,CAAC,CAAC,CAAC,GAAG,KAAK;gBAC1C,cAAc,EAAE,UAAU,CAAC,CAAC,CAAC,GAAG,GAAG;gBACnC,cAAc,EAAE,UAAU,CAAC,CAAC,CAAC,GAAG,EAAE;gBAClC,UAAU,EAAE,IAAI;gBAChB,YAAY,EAAE,CAAC,mBAAmB,EAAE,gBAAgB,CAAC;aACtD,CAAC,CAAC;QACL,CAAC;QAED,IAAI,UAAU,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC;YACxB,eAAe,CAAC,IAAI,CAAC;gBACnB,EAAE,EAAE,mBAAmB;gBACvB,IAAI,EAAE,kBAAkB;gBACxB,WAAW,EAAE,oEAAoE;gBACjF,QAAQ,EAAE,QAAQ;gBAClB,mBAAmB,EAAE,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI;gBACzC,cAAc,EAAE,UAAU,CAAC,CAAC,CAAC,GAAG,GAAG;gBACnC,cAAc,EAAE,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;gBACjC,UAAU,EAAE,IAAI;gBAChB,YAAY,EAAE,CAAC,mBAAmB,EAAE,SAAS,CAAC;aAC/C,CAAC,CAAC;QACL,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAEO,gBAAgB,CAAC,UAAwB,EAAE,KAAwB;QACzE,MAAM,iBAAiB,GAAG,KAAK,CAAC,eAAe,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,YAAY;QAErH,OAAO;YACL,MAAM,EAAE,UAAU,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,4BAA4B;YAC1D,IAAI,EAAE,iBAAiB,GAAG,UAAU,CAAC,EAAE,CAAC,EAAE,sBAAsB;YAChE,SAAS,EAAE,iBAAiB,GAAG,UAAU,CAAC,EAAE,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,oBAAoB;SAChF,CAAC;IACJ,CAAC;IAEO,0BAA0B,CAAC,eAA6C,EAAE,KAAwB;QACxG,OAAO,eAAe,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;YAC1C,EAAE,EAAE,GAAG,CAAC,EAAE;YACV,WAAW,EAAE,GAAG,CAAC,WAAW;YAC5B,QAAQ,EAAE,GAAG,CAAC,QAAQ;YACtB,cAAc,EAAE,GAAG,CAAC,mBAAmB;YACvC,kBAAkB,EAAE,GAAG,CAAC,cAAc,GAAG,EAAE,EAAE,kBAAkB;YAC/D,YAAY,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;YAC9D,eAAe,EAAE,GAAG,CAAC,cAAc;SACpC,CAAC,CAAC,CAAC;IACN,CAAC;IAEO,YAAY,CAAC,OAAY,EAAE,IAA0B;QAC3D,MAAM,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;QACjF,MAAM,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC;QAEnC,OAAO;YACL,kBAAkB,EAAE,eAAe;YACnC,cAAc,EAAE,aAAa;YAC7B,cAAc,EAAE,eAAe,GAAG,aAAa;YAC/C,iBAAiB,EAAE,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE,aAAa,EAAE,IAAI,EAAE,EAAE,CAAC;YAC9E,uBAAuB,EAAE,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE,aAAa,EAAE,EAAE,CAAC;SAC/E,CAAC;IACJ,CAAC;IAEO,WAAW,CAAC,eAA6C,EAAE,KAAwB;QACzF,MAAM,KAAK,GAAiB,EAAE,CAAC;QAE/B,cAAc;QACd,MAAM,SAAS,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAAC;QACzF,IAAI,SAAS,GAAG,KAAK,CAAC,WAAW,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;YAC/C,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,6CAA6C;gBAC1D,WAAW,EAAE,GAAG;gBAChB,MAAM,EAAE,GAAG;gBACX,UAAU,EAAE,iDAAiD;aAC9D,CAAC,CAAC;QACL,CAAC;QAED,gBAAgB;QAChB,MAAM,aAAa,GAAG,eAAe,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,aAAa;QAChE,IAAI,aAAa,GAAG,KAAK,CAAC,WAAW,CAAC,QAAQ,GAAG,GAAG,EAAE,CAAC;YACrD,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,UAAU;gBAChB,WAAW,EAAE,gDAAgD;gBAC7D,WAAW,EAAE,GAAG;gBAChB,MAAM,EAAE,GAAG;gBACX,UAAU,EAAE,4CAA4C;aACzD,CAAC,CAAC;QACL,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,kBAAkB,CAAC,UAAsB,EAAE,iBAAoC,EAAE,WAAmB;QAC1G,4CAA4C;QAC5C,MAAM,QAAQ,GAAG;YACf,GAAG,iBAAiB,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW,CAAC;YAC7D,GAAG,iBAAiB,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW,CAAC;YACnD,8BAA8B;SAC/B,CAAC;QAEF,OAAO;YACL,KAAK,EAAE,IAAI,GAAG,CAAC,MAAM,CAAC,SAAS,EAAE,IAAI,YAAY,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;SACnF,CAAC;IACJ,CAAC;IAEO,mBAAmB,CAAC,eAAqC;QAC/D,MAAM,QAAQ,GAAG,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YAC/C,IAAI,CAAC,GAAG;YACR,IAAI,CAAC,GAAG;YACR,IAAI,CAAC,oBAAoB;YACzB,IAAI,CAAC,oBAAoB;SAC1B,CAAC,CAAC;QAEH,OAAO;YACL,KAAK,EAAE,IAAI,GAAG,CAAC,MAAM,CAAC,SAAS,EAAE,IAAI,YAAY,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;SACnF,CAAC;IACJ,CAAC;IAEO,kBAAkB,CAAC,YAA0B,EAAE,gBAA0B;QAC/E,MAAM,QAAQ,GAAG;YACf,YAAY,CAAC,IAAI;YACjB,YAAY,CAAC,MAAM;YACnB,YAAY,CAAC,SAAS;YACtB,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;SACjC,CAAC;QAEF,OAAO;YACL,KAAK,EAAE,IAAI,GAAG,CAAC,MAAM,CAAC,SAAS,EAAE,IAAI,YAAY,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;SACnF,CAAC;IACJ,CAAC;IAEO,oBAAoB,CAAC,IAAkB;QAC7C,yCAAyC;QACzC,MAAM,cAAc,GAAG,CAAC,WAAW,EAAE,SAAS,EAAE,QAAQ,EAAE,aAAa,EAAE,UAAU,CAAC,CAAC;QACrF,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,SAAS,CAAC,CAAC;IACvF,CAAC;IAEO,KAAK,CAAC,wBAAwB,CAAC,UAAsB;QAC3D,4CAA4C;QAC5C,OAAO,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,mBAAmB;IACvD,CAAC;IAEO,4BAA4B,CAAC,UAAsB,EAAE,eAAuB,EAAE,kBAA0B;QAC9G,oCAAoC;QACpC,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,UAAU,IAAI,GAAG,EAAE,GAAG,CAAC,CAAC;QACpE,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,eAAe,GAAG,KAAK,CAAC,CAAC;QAC7D,MAAM,cAAc,GAAG,kBAAkB,CAAC;QAE1C,OAAO,CAAC,eAAe,GAAG,GAAG,GAAG,WAAW,GAAG,GAAG,GAAG,cAAc,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;IAClF,CAAC;IAEO,oCAAoC,CAAC,mBAA2B,EAAE,kBAA0B;QAClG,MAAM,eAAe,GAAa,EAAE,CAAC;QAErC,IAAI,mBAAmB,GAAG,EAAE,EAAE,CAAC;YAC7B,eAAe,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;QAC1E,CAAC;QAED,IAAI,kBAAkB,GAAG,GAAG,EAAE,CAAC;YAC7B,eAAe,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;YACxD,eAAe,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;QAChE,CAAC;QAED,eAAe,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;QACzE,eAAe,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;QAEnE,OAAO,eAAe,CAAC;IACzB,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,MAA0B,EAAE,KAAwB;QACxF,+DAA+D;QAC/D,MAAM,wBAAwB,GAAG,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;YACnE,2BAA2B;YAC3B,IAAI,GAAG,CAAC,mBAAmB,GAAG,KAAK,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;gBACvD,OAAO,KAAK,CAAC;YACf,CAAC;YAED,6BAA6B;YAC7B,IAAI,GAAG,CAAC,cAAc,GAAG,EAAE,GAAG,KAAK,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;gBACzD,OAAO,KAAK,CAAC;YACf,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,GAAG,MAAM;YACT,eAAe,EAAE,wBAAwB;SAC1C,CAAC;IACJ,CAAC;IAEO,YAAY,CAAC,UAAkB,EAAE,aAAqB,EAAE,YAAoB,EAAE,KAAa;QACjG,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC;QACtB,KAAK,IAAI,IAAI,GAAG,CAAC,EAAE,IAAI,IAAI,KAAK,EAAE,IAAI,EAAE,EAAE,CAAC;YACzC,GAAG,IAAI,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY,EAAE,IAAI,CAAC,CAAC;QAC1D,CAAC;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAEO,YAAY,CAAC,UAAkB,EAAE,aAAqB,EAAE,KAAa;QAC3E,6BAA6B;QAC7B,OAAO,CAAC,aAAa,GAAG,UAAU,CAAC,GAAG,GAAG,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO;QACX,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QAC/B,CAAC;QAED,KAAK,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAC9C,MAAM,OAAO,CAAC,OAAO,EAAE,CAAC;QAC1B,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;QACxB,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;IAC7B,CAAC;CACF;AA5hBD,sDA4hBC;AAED,qBAAqB;AACrB,IAAI,qBAAqB,GAAiC,IAAI,CAAC;AAE/D,SAAgB,wBAAwB,CAAC,MAA6B;IACpE,IAAI,CAAC,qBAAqB,IAAI,MAAM,EAAE,CAAC;QACrC,qBAAqB,GAAG,IAAI,qBAAqB,CAAC,MAAM,CAAC,CAAC;IAC5D,CAAC;IACD,OAAO,qBAAsB,CAAC;AAChC,CAAC;AAED,kBAAe,qBAAqB,CAAC;AAErC,iCAAiC;AACjC,SAAgB,iBAAiB,CAAC,MAA6B;IAC7D,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,GAAG,IAAA,gBAAQ,EAA+B,IAAI,CAAC,CAAC;IAC3E,MAAM,CAAC,aAAa,EAAE,gBAAgB,CAAC,GAAG,IAAA,gBAAQ,EAAC,KAAK,CAAC,CAAC;IAC1D,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,GAAG,IAAA,gBAAQ,EAAC,KAAK,CAAC,CAAC;IAClD,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,IAAA,gBAAQ,EAAgB,IAAI,CAAC,CAAC;IAExD,IAAA,iBAAS,EAAC,GAAG,EAAE;QACb,IAAI,MAAM,EAAE,CAAC;YACX,MAAM,SAAS,GAAG,IAAI,qBAAqB,CAAC,MAAM,CAAC,CAAC;YACpD,UAAU,CAAC,SAAS,CAAC,CAAC;YAEtB,SAAS,CAAC,UAAU,EAAE;iBACnB,IAAI,CAAC,GAAG,EAAE,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;iBAClC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;QACzC,CAAC;IACH,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;IAEb,MAAM,cAAc,GAAG,IAAA,mBAAW,EAAC,KAAK,EAAE,KAAwB,EAAE,EAAE;QACpE,IAAI,CAAC,OAAO,IAAI,CAAC,aAAa,EAAE,CAAC;YAC/B,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;QAChD,CAAC;QAED,YAAY,CAAC,IAAI,CAAC,CAAC;QACnB,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEf,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YACnD,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,MAAM,YAAY,GAAG,GAAG,YAAY,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,qBAAqB,CAAC;YAChF,QAAQ,CAAC,YAAY,CAAC,CAAC;YACvB,MAAM,GAAG,CAAC;QACZ,CAAC;gBAAS,CAAC;YACT,YAAY,CAAC,KAAK,CAAC,CAAC;QACtB,CAAC;IACH,CAAC,EAAE,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC,CAAC;IAE7B,MAAM,aAAa,GAAG,IAAA,mBAAW,EAAC,KAAK,EACrC,UAAsB,EACtB,iBAAoC,EACpC,WAAoB,EACpB,EAAE;QACF,IAAI,CAAC,OAAO,IAAI,CAAC,aAAa,EAAE,CAAC;YAC/B,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;QAChD,CAAC;QAED,OAAO,OAAO,CAAC,wBAAwB,CAAC,UAAU,EAAE,iBAAiB,EAAE,WAAW,CAAC,CAAC;IACtF,CAAC,EAAE,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC,CAAC;IAE7B,MAAM,eAAe,GAAG,IAAA,mBAAW,EAAC,KAAK,EACvC,eAAqC,EACrC,SAAkB,EAClB,EAAE;QACF,IAAI,CAAC,OAAO,IAAI,CAAC,aAAa,EAAE,CAAC;YAC/B,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;QAChD,CAAC;QAED,OAAO,OAAO,CAAC,eAAe,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;IAC7D,CAAC,EAAE,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC,CAAC;IAE7B,OAAO;QACL,aAAa;QACb,SAAS;QACT,KAAK;QACL,cAAc;QACd,aAAa;QACb,eAAe;QACf,OAAO;KACR,CAAC;AACJ,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\frontend\\lib\\services\\AIOptimizationService.ts"],
      sourcesContent: ["/**\r\n * AI-Powered HVAC Optimization Service\r\n * \r\n * Integrates ONNX.js machine learning models for intelligent HVAC system\r\n * optimization and energy efficiency recommendations.\r\n * \r\n * Features:\r\n * - Energy efficiency optimization\r\n * - Load prediction and balancing\r\n * - Equipment sizing recommendations\r\n * - Performance anomaly detection\r\n * - Cost optimization analysis\r\n * - Environmental impact assessment\r\n */\r\n\r\nimport { useState, useEffect, useCallback } from 'react';\r\nimport * as ort from 'onnxruntime-web';\r\nimport { CalculationResult } from '../../types/air-duct-sizer';\r\n\r\n// Mock types for AI optimization\r\nexport interface HVACSystem {\r\n  id: string;\r\n  type: string;\r\n  efficiency?: number;\r\n  capacity?: number;\r\n  age?: number;\r\n  maintenance_schedule?: string[];\r\n}\r\n\r\nexport interface OptimizationRecommendation {\r\n  id: string;\r\n  type: 'equipment_upgrade' | 'schedule_optimization' | 'maintenance' | 'control_strategy';\r\n  description: string;\r\n  priority: 'low' | 'medium' | 'high';\r\n  implementation_cost: number;\r\n  annual_savings: number;\r\n  payback_period: number;\r\n  confidence: number;\r\n  impact_areas: string[];\r\n}\r\n\r\nexport interface AIOptimizationConfig {\r\n  modelPath: string;\r\n  enableGPU?: boolean;\r\n  batchSize?: number;\r\n  confidenceThreshold?: number;\r\n  optimizationGoals?: OptimizationGoal[];\r\n}\r\n\r\nexport interface OptimizationGoal {\r\n  type: 'energy_efficiency' | 'cost_reduction' | 'comfort' | 'environmental';\r\n  weight: number; // 0-1\r\n  target?: number;\r\n}\r\n\r\nexport interface OptimizationInput {\r\n  hvacSystem: HVACSystem;\r\n  buildingData: BuildingData;\r\n  environmentalData: EnvironmentalData;\r\n  operationalData: OperationalData;\r\n  constraints: OptimizationConstraints;\r\n}\r\n\r\nexport interface BuildingData {\r\n  area: number;\r\n  volume: number;\r\n  occupancy: number;\r\n  insulation: number; // R-value\r\n  windows: WindowData[];\r\n  orientation: string;\r\n  floors: number;\r\n  zoneCount: number;\r\n}\r\n\r\nexport interface WindowData {\r\n  area: number;\r\n  uValue: number;\r\n  orientation: string;\r\n  shading: number;\r\n}\r\n\r\nexport interface EnvironmentalData {\r\n  outdoorTemperature: number[];\r\n  humidity: number[];\r\n  solarRadiation: number[];\r\n  windSpeed: number[];\r\n  season: 'spring' | 'summer' | 'fall' | 'winter';\r\n  climate: string;\r\n}\r\n\r\nexport interface OperationalData {\r\n  currentLoad: number;\r\n  energyConsumption: number[];\r\n  operatingHours: number;\r\n  maintenanceHistory: MaintenanceRecord[];\r\n  performanceMetrics: PerformanceMetrics;\r\n}\r\n\r\nexport interface MaintenanceRecord {\r\n  date: Date;\r\n  type: string;\r\n  cost: number;\r\n  efficiency_impact: number;\r\n}\r\n\r\nexport interface PerformanceMetrics {\r\n  cop: number; // Coefficient of Performance\r\n  eer: number; // Energy Efficiency Ratio\r\n  capacity_utilization: number;\r\n  temperature_variance: number;\r\n}\r\n\r\nexport interface OptimizationConstraints {\r\n  budget: number;\r\n  timeline: number; // days\r\n  comfort_requirements: ComfortRequirements;\r\n  regulatory_requirements: string[];\r\n  existing_equipment: string[];\r\n}\r\n\r\nexport interface ComfortRequirements {\r\n  temperature_range: [number, number];\r\n  humidity_range: [number, number];\r\n  air_quality_min: number;\r\n  noise_max: number;\r\n}\r\n\r\nexport interface OptimizationResult {\r\n  recommendations: OptimizationRecommendation[];\r\n  predicted_savings: {\r\n    energy: number; // percentage\r\n    cost: number; // annual dollars\r\n    emissions: number; // kg CO2 equivalent\r\n  };\r\n  confidence_score: number;\r\n  implementation_plan: ImplementationStep[];\r\n  roi_analysis: ROIAnalysis;\r\n  risk_assessment: RiskFactor[];\r\n}\r\n\r\nexport interface ImplementationStep {\r\n  id: string;\r\n  description: string;\r\n  priority: 'high' | 'medium' | 'low';\r\n  estimated_cost: number;\r\n  estimated_duration: number; // days\r\n  dependencies: string[];\r\n  expected_impact: number; // percentage improvement\r\n}\r\n\r\nexport interface ROIAnalysis {\r\n  initial_investment: number;\r\n  annual_savings: number;\r\n  payback_period: number; // years\r\n  net_present_value: number;\r\n  internal_rate_of_return: number;\r\n}\r\n\r\nexport interface RiskFactor {\r\n  type: string;\r\n  description: string;\r\n  probability: number; // 0-1\r\n  impact: number; // 0-1\r\n  mitigation: string;\r\n}\r\n\r\nexport class AIOptimizationService {\r\n  private session: ort.InferenceSession | null = null;\r\n  private config: AIOptimizationConfig;\r\n  private modelCache: Map<string, ort.InferenceSession> = new Map();\r\n  private isInitialized = false;\r\n\r\n  constructor(config: AIOptimizationConfig) {\r\n    this.config = {\r\n      enableGPU: false,\r\n      batchSize: 1,\r\n      confidenceThreshold: 0.7,\r\n      optimizationGoals: [\r\n        { type: 'energy_efficiency', weight: 0.4 },\r\n        { type: 'cost_reduction', weight: 0.3 },\r\n        { type: 'comfort', weight: 0.2 },\r\n        { type: 'environmental', weight: 0.1 }\r\n      ],\r\n      ...config\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Initialize the AI optimization service\r\n   */\r\n  async initialize(): Promise<void> {\r\n    try {\r\n      // Configure ONNX runtime\r\n      if (this.config.enableGPU) {\r\n        ort.env.wasm.numThreads = 4;\r\n        ort.env.wasm.simd = true;\r\n      }\r\n\r\n      // Load main optimization model\r\n      this.session = await ort.InferenceSession.create(this.config.modelPath);\r\n      \r\n      // Load specialized models\r\n      await this.loadSpecializedModels();\r\n      \r\n      this.isInitialized = true;\r\n      console.log('AI Optimization Service initialized successfully');\r\n      \r\n    } catch (error) {\r\n      console.error('Failed to initialize AI Optimization Service:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Optimize HVAC system configuration\r\n   */\r\n  async optimizeSystem(input: OptimizationInput): Promise<OptimizationResult> {\r\n    if (!this.isInitialized || !this.session) {\r\n      throw new Error('AI Optimization Service not initialized');\r\n    }\r\n\r\n    try {\r\n      // Prepare input data\r\n      const modelInput = await this.prepareModelInput(input);\r\n      \r\n      // Run inference\r\n      const results = await this.session.run(modelInput);\r\n      \r\n      // Process results\r\n      const optimizationResult = await this.processResults(results, input);\r\n      \r\n      // Validate recommendations\r\n      const validatedResult = await this.validateRecommendations(optimizationResult, input);\r\n      \r\n      return validatedResult;\r\n      \r\n    } catch (error) {\r\n      console.error('Optimization failed:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Predict energy consumption\r\n   */\r\n  async predictEnergyConsumption(\r\n    hvacSystem: HVACSystem,\r\n    environmentalData: EnvironmentalData,\r\n    timeHorizon: number = 24 // hours\r\n  ): Promise<number[]> {\r\n    const energyModel = this.modelCache.get('energy_prediction');\r\n    if (!energyModel) {\r\n      throw new Error('Energy prediction model not loaded');\r\n    }\r\n\r\n    const input = this.prepareEnergyInput(hvacSystem, environmentalData, timeHorizon);\r\n    const results = await energyModel.run(input);\r\n    \r\n    return Array.from(results.energy_consumption.data as Float32Array);\r\n  }\r\n\r\n  /**\r\n   * Detect performance anomalies\r\n   */\r\n  async detectAnomalies(\r\n    performanceData: PerformanceMetrics[],\r\n    threshold: number = 0.8\r\n  ): Promise<{ anomalies: number[]; confidence: number[] }> {\r\n    const anomalyModel = this.modelCache.get('anomaly_detection');\r\n    if (!anomalyModel) {\r\n      throw new Error('Anomaly detection model not loaded');\r\n    }\r\n\r\n    const input = this.prepareAnomalyInput(performanceData);\r\n    const results = await anomalyModel.run(input);\r\n    \r\n    const anomalyScores = Array.from(results.anomaly_scores.data as Float32Array);\r\n    const confidenceScores = Array.from(results.confidence.data as Float32Array);\r\n    \r\n    const anomalies = anomalyScores.map((score, index) => \r\n      score > threshold ? index : -1\r\n    ).filter(index => index !== -1);\r\n    \r\n    return {\r\n      anomalies,\r\n      confidence: anomalies.map(index => confidenceScores[index])\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Generate equipment sizing recommendations\r\n   */\r\n  async recommendEquipmentSizing(\r\n    buildingData: BuildingData,\r\n    loadRequirements: number[]\r\n  ): Promise<{\r\n    equipment: string[];\r\n    sizes: number[];\r\n    efficiency_ratings: number[];\r\n    cost_estimates: number[];\r\n  }> {\r\n    const sizingModel = this.modelCache.get('equipment_sizing');\r\n    if (!sizingModel) {\r\n      throw new Error('Equipment sizing model not loaded');\r\n    }\r\n\r\n    const input = this.prepareSizingInput(buildingData, loadRequirements);\r\n    const results = await sizingModel.run(input);\r\n    \r\n    return {\r\n      equipment: this.decodeEquipmentTypes(results.equipment_types.data as Float32Array),\r\n      sizes: Array.from(results.equipment_sizes.data as Float32Array),\r\n      efficiency_ratings: Array.from(results.efficiency_ratings.data as Float32Array),\r\n      cost_estimates: Array.from(results.cost_estimates.data as Float32Array)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Analyze environmental impact\r\n   */\r\n  async analyzeEnvironmentalImpact(\r\n    hvacSystem: HVACSystem,\r\n    energyConsumption: number[]\r\n  ): Promise<{\r\n    carbon_footprint: number;\r\n    renewable_potential: number;\r\n    sustainability_score: number;\r\n    recommendations: string[];\r\n  }> {\r\n    // Calculate carbon footprint\r\n    const carbonIntensity = 0.4; // kg CO2/kWh (average grid)\r\n    const totalEnergy = energyConsumption.reduce((sum, consumption) => sum + consumption, 0);\r\n    const carbonFootprint = totalEnergy * carbonIntensity;\r\n    \r\n    // Assess renewable potential\r\n    const renewablePotential = await this.assessRenewablePotential(hvacSystem);\r\n    \r\n    // Calculate sustainability score\r\n    const sustainabilityScore = this.calculateSustainabilityScore(\r\n      hvacSystem,\r\n      carbonFootprint,\r\n      renewablePotential\r\n    );\r\n    \r\n    // Generate recommendations\r\n    const recommendations = this.generateEnvironmentalRecommendations(\r\n      sustainabilityScore,\r\n      renewablePotential\r\n    );\r\n    \r\n    return {\r\n      carbon_footprint: carbonFootprint,\r\n      renewable_potential: renewablePotential,\r\n      sustainability_score: sustainabilityScore,\r\n      recommendations\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get optimization insights\r\n   */\r\n  getOptimizationInsights(result: OptimizationResult): {\r\n    key_insights: string[];\r\n    priority_actions: string[];\r\n    potential_issues: string[];\r\n  } {\r\n    const insights: string[] = [];\r\n    const priorityActions: string[] = [];\r\n    const potentialIssues: string[] = [];\r\n    \r\n    // Analyze savings potential\r\n    if (result.predicted_savings.energy > 20) {\r\n      insights.push(`Significant energy savings potential: ${result.predicted_savings.energy.toFixed(1)}%`);\r\n    }\r\n    \r\n    if (result.predicted_savings.cost > 5000) {\r\n      insights.push(`Annual cost savings: $${result.predicted_savings.cost.toLocaleString()}`);\r\n    }\r\n    \r\n    // Identify priority actions\r\n    const highPrioritySteps = result.implementation_plan\r\n      .filter(step => step.priority === 'high')\r\n      .sort((a, b) => b.expected_impact - a.expected_impact);\r\n    \r\n    priorityActions.push(...highPrioritySteps.slice(0, 3).map(step => step.description));\r\n    \r\n    // Identify potential issues\r\n    const highRiskFactors = result.risk_assessment\r\n      .filter(risk => risk.probability * risk.impact > 0.5)\r\n      .sort((a, b) => (b.probability * b.impact) - (a.probability * a.impact));\r\n    \r\n    potentialIssues.push(...highRiskFactors.slice(0, 3).map(risk => risk.description));\r\n    \r\n    return {\r\n      key_insights: insights,\r\n      priority_actions: priorityActions,\r\n      potential_issues: potentialIssues\r\n    };\r\n  }\r\n\r\n  private async loadSpecializedModels(): Promise<void> {\r\n    const models = [\r\n      { name: 'energy_prediction', path: '/models/energy_prediction.onnx' },\r\n      { name: 'anomaly_detection', path: '/models/anomaly_detection.onnx' },\r\n      { name: 'equipment_sizing', path: '/models/equipment_sizing.onnx' },\r\n      { name: 'load_forecasting', path: '/models/load_forecasting.onnx' }\r\n    ];\r\n\r\n    for (const model of models) {\r\n      try {\r\n        const session = await ort.InferenceSession.create(model.path);\r\n        this.modelCache.set(model.name, session);\r\n        console.log(`Loaded ${model.name} model`);\r\n      } catch (error) {\r\n        console.warn(`Failed to load ${model.name} model:`, error);\r\n      }\r\n    }\r\n  }\r\n\r\n  private async prepareModelInput(input: OptimizationInput): Promise<Record<string, ort.Tensor>> {\r\n    // Normalize and prepare input data for the model\r\n    const features = [\r\n      // Building features\r\n      input.buildingData.area / 10000, // Normalize area\r\n      input.buildingData.volume / 100000, // Normalize volume\r\n      input.buildingData.occupancy / 1000, // Normalize occupancy\r\n      input.buildingData.insulation / 50, // Normalize R-value\r\n      input.buildingData.floors / 50, // Normalize floors\r\n      input.buildingData.zoneCount / 20, // Normalize zones\r\n      \r\n      // Environmental features\r\n      ...input.environmentalData.outdoorTemperature.slice(0, 24).map(t => t / 100),\r\n      ...input.environmentalData.humidity.slice(0, 24).map(h => h / 100),\r\n      \r\n      // Operational features\r\n      input.operationalData.currentLoad / 1000, // Normalize load\r\n      input.operationalData.performanceMetrics.cop / 10,\r\n      input.operationalData.performanceMetrics.eer / 30,\r\n      input.operationalData.performanceMetrics.capacity_utilization,\r\n      \r\n      // Constraint features\r\n      input.constraints.budget / 100000, // Normalize budget\r\n      input.constraints.timeline / 365, // Normalize timeline\r\n    ];\r\n\r\n    // Pad or truncate to expected input size\r\n    const inputSize = 100; // Expected model input size\r\n    const paddedFeatures = features.slice(0, inputSize);\r\n    while (paddedFeatures.length < inputSize) {\r\n      paddedFeatures.push(0);\r\n    }\r\n\r\n    return {\r\n      input: new ort.Tensor('float32', new Float32Array(paddedFeatures), [1, inputSize])\r\n    };\r\n  }\r\n\r\n  private async processResults(results: ort.InferenceSession.OnnxValueMapType, input: OptimizationInput): Promise<OptimizationResult> {\r\n    // Process model outputs into structured recommendations\r\n    const outputData = results.output.data as Float32Array;\r\n    \r\n    // Extract different types of recommendations\r\n    const recommendations = this.extractRecommendations(outputData, input);\r\n    const savings = this.calculateSavings(outputData, input);\r\n    const implementationPlan = this.generateImplementationPlan(recommendations, input);\r\n    const roiAnalysis = this.calculateROI(savings, implementationPlan);\r\n    const riskAssessment = this.assessRisks(recommendations, input);\r\n    \r\n    return {\r\n      recommendations,\r\n      predicted_savings: savings,\r\n      confidence_score: Math.min(outputData[0], 1.0),\r\n      implementation_plan: implementationPlan,\r\n      roi_analysis: roiAnalysis,\r\n      risk_assessment: riskAssessment\r\n    };\r\n  }\r\n\r\n  private extractRecommendations(outputData: Float32Array, input: OptimizationInput): OptimizationRecommendation[] {\r\n    // Extract and decode recommendations from model output\r\n    const recommendations: OptimizationRecommendation[] = [];\r\n    \r\n    // This is a simplified example - real implementation would decode model outputs\r\n    if (outputData[1] > 0.5) {\r\n      recommendations.push({\r\n        id: 'upgrade_hvac',\r\n        type: 'equipment_upgrade',\r\n        description: 'Upgrade HVAC Equipment - Replace aging equipment with high-efficiency units',\r\n        priority: 'high',\r\n        implementation_cost: outputData[3] * 10000,\r\n        annual_savings: outputData[2] * 100,\r\n        payback_period: outputData[4] * 10,\r\n        confidence: 0.85,\r\n        impact_areas: ['energy_efficiency', 'cost_reduction']\r\n      });\r\n    }\r\n    \r\n    if (outputData[5] > 0.5) {\r\n      recommendations.push({\r\n        id: 'optimize_controls',\r\n        type: 'control_strategy',\r\n        description: 'Optimize Control Systems - Implement smart controls and scheduling',\r\n        priority: 'medium',\r\n        implementation_cost: outputData[7] * 5000,\r\n        annual_savings: outputData[6] * 100,\r\n        payback_period: outputData[8] * 5,\r\n        confidence: 0.75,\r\n        impact_areas: ['energy_efficiency', 'comfort']\r\n      });\r\n    }\r\n    \r\n    return recommendations;\r\n  }\r\n\r\n  private calculateSavings(outputData: Float32Array, input: OptimizationInput) {\r\n    const currentEnergyCost = input.operationalData.energyConsumption.reduce((sum, e) => sum + e, 0) * 0.12; // $0.12/kWh\r\n    \r\n    return {\r\n      energy: outputData[10] * 100, // Percentage energy savings\r\n      cost: currentEnergyCost * outputData[10], // Annual cost savings\r\n      emissions: currentEnergyCost * outputData[10] * 0.4 * 1000 // kg CO2 equivalent\r\n    };\r\n  }\r\n\r\n  private generateImplementationPlan(recommendations: OptimizationRecommendation[], input: OptimizationInput): ImplementationStep[] {\r\n    return recommendations.map((rec, index) => ({\r\n      id: rec.id,\r\n      description: rec.description,\r\n      priority: rec.priority,\r\n      estimated_cost: rec.implementation_cost,\r\n      estimated_duration: rec.payback_period * 30, // Convert to days\r\n      dependencies: index > 0 ? [recommendations[index - 1].id] : [],\r\n      expected_impact: rec.annual_savings\r\n    }));\r\n  }\r\n\r\n  private calculateROI(savings: any, plan: ImplementationStep[]): ROIAnalysis {\r\n    const totalInvestment = plan.reduce((sum, step) => sum + step.estimated_cost, 0);\r\n    const annualSavings = savings.cost;\r\n    \r\n    return {\r\n      initial_investment: totalInvestment,\r\n      annual_savings: annualSavings,\r\n      payback_period: totalInvestment / annualSavings,\r\n      net_present_value: this.calculateNPV(totalInvestment, annualSavings, 0.05, 10),\r\n      internal_rate_of_return: this.calculateIRR(totalInvestment, annualSavings, 10)\r\n    };\r\n  }\r\n\r\n  private assessRisks(recommendations: OptimizationRecommendation[], input: OptimizationInput): RiskFactor[] {\r\n    const risks: RiskFactor[] = [];\r\n    \r\n    // Budget risk\r\n    const totalCost = recommendations.reduce((sum, rec) => sum + rec.implementation_cost, 0);\r\n    if (totalCost > input.constraints.budget * 0.8) {\r\n      risks.push({\r\n        type: 'budget',\r\n        description: 'Implementation cost approaches budget limit',\r\n        probability: 0.7,\r\n        impact: 0.8,\r\n        mitigation: 'Phase implementation or seek additional funding'\r\n      });\r\n    }\r\n    \r\n    // Timeline risk\r\n    const totalDuration = recommendations.length * 30; // Simplified\r\n    if (totalDuration > input.constraints.timeline * 0.8) {\r\n      risks.push({\r\n        type: 'timeline',\r\n        description: 'Implementation timeline may exceed constraints',\r\n        probability: 0.6,\r\n        impact: 0.6,\r\n        mitigation: 'Parallel implementation or scope reduction'\r\n      });\r\n    }\r\n    \r\n    return risks;\r\n  }\r\n\r\n  private prepareEnergyInput(hvacSystem: HVACSystem, environmentalData: EnvironmentalData, timeHorizon: number): Record<string, ort.Tensor> {\r\n    // Prepare input for energy prediction model\r\n    const features = [\r\n      ...environmentalData.outdoorTemperature.slice(0, timeHorizon),\r\n      ...environmentalData.humidity.slice(0, timeHorizon),\r\n      // Add more features as needed\r\n    ];\r\n    \r\n    return {\r\n      input: new ort.Tensor('float32', new Float32Array(features), [1, features.length])\r\n    };\r\n  }\r\n\r\n  private prepareAnomalyInput(performanceData: PerformanceMetrics[]): Record<string, ort.Tensor> {\r\n    const features = performanceData.flatMap(data => [\r\n      data.cop,\r\n      data.eer,\r\n      data.capacity_utilization,\r\n      data.temperature_variance\r\n    ]);\r\n    \r\n    return {\r\n      input: new ort.Tensor('float32', new Float32Array(features), [1, features.length])\r\n    };\r\n  }\r\n\r\n  private prepareSizingInput(buildingData: BuildingData, loadRequirements: number[]): Record<string, ort.Tensor> {\r\n    const features = [\r\n      buildingData.area,\r\n      buildingData.volume,\r\n      buildingData.occupancy,\r\n      ...loadRequirements.slice(0, 24)\r\n    ];\r\n    \r\n    return {\r\n      input: new ort.Tensor('float32', new Float32Array(features), [1, features.length])\r\n    };\r\n  }\r\n\r\n  private decodeEquipmentTypes(data: Float32Array): string[] {\r\n    // Decode equipment type indices to names\r\n    const equipmentTypes = ['Heat Pump', 'Chiller', 'Boiler', 'Air Handler', 'Fan Coil'];\r\n    return Array.from(data).map(index => equipmentTypes[Math.floor(index)] || 'Unknown');\r\n  }\r\n\r\n  private async assessRenewablePotential(hvacSystem: HVACSystem): Promise<number> {\r\n    // Simplified renewable potential assessment\r\n    return Math.random() * 0.5 + 0.3; // 30-80% potential\r\n  }\r\n\r\n  private calculateSustainabilityScore(hvacSystem: HVACSystem, carbonFootprint: number, renewablePotential: number): number {\r\n    // Simplified sustainability scoring\r\n    const efficiencyScore = Math.min(hvacSystem.efficiency || 0.8, 1.0);\r\n    const carbonScore = Math.max(0, 1 - carbonFootprint / 10000);\r\n    const renewableScore = renewablePotential;\r\n    \r\n    return (efficiencyScore * 0.4 + carbonScore * 0.4 + renewableScore * 0.2) * 100;\r\n  }\r\n\r\n  private generateEnvironmentalRecommendations(sustainabilityScore: number, renewablePotential: number): string[] {\r\n    const recommendations: string[] = [];\r\n    \r\n    if (sustainabilityScore < 60) {\r\n      recommendations.push('Consider upgrading to high-efficiency equipment');\r\n    }\r\n    \r\n    if (renewablePotential > 0.5) {\r\n      recommendations.push('Explore solar panel integration');\r\n      recommendations.push('Consider geothermal heat pump systems');\r\n    }\r\n    \r\n    recommendations.push('Implement smart scheduling to reduce peak demand');\r\n    recommendations.push('Regular maintenance to maintain efficiency');\r\n    \r\n    return recommendations;\r\n  }\r\n\r\n  private async validateRecommendations(result: OptimizationResult, input: OptimizationInput): Promise<OptimizationResult> {\r\n    // Validate recommendations against constraints and feasibility\r\n    const validatedRecommendations = result.recommendations.filter(rec => {\r\n      // Check budget constraints\r\n      if (rec.implementation_cost > input.constraints.budget) {\r\n        return false;\r\n      }\r\n      \r\n      // Check timeline constraints\r\n      if (rec.payback_period * 30 > input.constraints.timeline) {\r\n        return false;\r\n      }\r\n      \r\n      return true;\r\n    });\r\n    \r\n    return {\r\n      ...result,\r\n      recommendations: validatedRecommendations\r\n    };\r\n  }\r\n\r\n  private calculateNPV(investment: number, annualSavings: number, discountRate: number, years: number): number {\r\n    let npv = -investment;\r\n    for (let year = 1; year <= years; year++) {\r\n      npv += annualSavings / Math.pow(1 + discountRate, year);\r\n    }\r\n    return npv;\r\n  }\r\n\r\n  private calculateIRR(investment: number, annualSavings: number, years: number): number {\r\n    // Simplified IRR calculation\r\n    return (annualSavings / investment) * 100;\r\n  }\r\n\r\n  /**\r\n   * Cleanup resources\r\n   */\r\n  async dispose(): Promise<void> {\r\n    if (this.session) {\r\n      await this.session.release();\r\n    }\r\n    \r\n    for (const [name, session] of this.modelCache) {\r\n      await session.release();\r\n    }\r\n    \r\n    this.modelCache.clear();\r\n    this.isInitialized = false;\r\n  }\r\n}\r\n\r\n// Singleton instance\r\nlet aiOptimizationService: AIOptimizationService | null = null;\r\n\r\nexport function getAIOptimizationService(config?: AIOptimizationConfig): AIOptimizationService {\r\n  if (!aiOptimizationService && config) {\r\n    aiOptimizationService = new AIOptimizationService(config);\r\n  }\r\n  return aiOptimizationService!;\r\n}\r\n\r\nexport default AIOptimizationService;\r\n\r\n// React Hook for AI Optimization\r\nexport function useAIOptimization(config?: AIOptimizationConfig) {\r\n  const [service, setService] = useState<AIOptimizationService | null>(null);\r\n  const [isInitialized, setIsInitialized] = useState(false);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  useEffect(() => {\r\n    if (config) {\r\n      const aiService = new AIOptimizationService(config);\r\n      setService(aiService);\r\n\r\n      aiService.initialize()\r\n        .then(() => setIsInitialized(true))\r\n        .catch(err => setError(err.message));\r\n    }\r\n  }, [config]);\r\n\r\n  const optimizeSystem = useCallback(async (input: OptimizationInput) => {\r\n    if (!service || !isInitialized) {\r\n      throw new Error('AI service not initialized');\r\n    }\r\n\r\n    setIsLoading(true);\r\n    setError(null);\r\n\r\n    try {\r\n      const result = await service.optimizeSystem(input);\r\n      return result;\r\n    } catch (err) {\r\n      const errorMessage = err instanceof Error ? err.message : 'Optimization failed';\r\n      setError(errorMessage);\r\n      throw err;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, [service, isInitialized]);\r\n\r\n  const predictEnergy = useCallback(async (\r\n    hvacSystem: HVACSystem,\r\n    environmentalData: EnvironmentalData,\r\n    timeHorizon?: number\r\n  ) => {\r\n    if (!service || !isInitialized) {\r\n      throw new Error('AI service not initialized');\r\n    }\r\n\r\n    return service.predictEnergyConsumption(hvacSystem, environmentalData, timeHorizon);\r\n  }, [service, isInitialized]);\r\n\r\n  const detectAnomalies = useCallback(async (\r\n    performanceData: PerformanceMetrics[],\r\n    threshold?: number\r\n  ) => {\r\n    if (!service || !isInitialized) {\r\n      throw new Error('AI service not initialized');\r\n    }\r\n\r\n    return service.detectAnomalies(performanceData, threshold);\r\n  }, [service, isInitialized]);\r\n\r\n  return {\r\n    isInitialized,\r\n    isLoading,\r\n    error,\r\n    optimizeSystem,\r\n    predictEnergy,\r\n    detectAnomalies,\r\n    service\r\n  };\r\n}\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "d423b2f4b26b33b47de8b47922c84cbf5ee5faad"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_11pqkh4977 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_11pqkh4977();
var __createBinding =
/* istanbul ignore next */
(cov_11pqkh4977().s[0]++,
/* istanbul ignore next */
(cov_11pqkh4977().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_11pqkh4977().b[0][1]++, this.__createBinding) ||
/* istanbul ignore next */
(cov_11pqkh4977().b[0][2]++, Object.create ?
/* istanbul ignore next */
(cov_11pqkh4977().b[1][0]++, function (o, m, k, k2) {
  /* istanbul ignore next */
  cov_11pqkh4977().f[0]++;
  cov_11pqkh4977().s[1]++;
  if (k2 === undefined) {
    /* istanbul ignore next */
    cov_11pqkh4977().b[2][0]++;
    cov_11pqkh4977().s[2]++;
    k2 = k;
  } else
  /* istanbul ignore next */
  {
    cov_11pqkh4977().b[2][1]++;
  }
  var desc =
  /* istanbul ignore next */
  (cov_11pqkh4977().s[3]++, Object.getOwnPropertyDescriptor(m, k));
  /* istanbul ignore next */
  cov_11pqkh4977().s[4]++;
  if (
  /* istanbul ignore next */
  (cov_11pqkh4977().b[4][0]++, !desc) ||
  /* istanbul ignore next */
  (cov_11pqkh4977().b[4][1]++, "get" in desc ?
  /* istanbul ignore next */
  (cov_11pqkh4977().b[5][0]++, !m.__esModule) :
  /* istanbul ignore next */
  (cov_11pqkh4977().b[5][1]++,
  /* istanbul ignore next */
  (cov_11pqkh4977().b[6][0]++, desc.writable) ||
  /* istanbul ignore next */
  (cov_11pqkh4977().b[6][1]++, desc.configurable)))) {
    /* istanbul ignore next */
    cov_11pqkh4977().b[3][0]++;
    cov_11pqkh4977().s[5]++;
    desc = {
      enumerable: true,
      get: function () {
        /* istanbul ignore next */
        cov_11pqkh4977().f[1]++;
        cov_11pqkh4977().s[6]++;
        return m[k];
      }
    };
  } else
  /* istanbul ignore next */
  {
    cov_11pqkh4977().b[3][1]++;
  }
  cov_11pqkh4977().s[7]++;
  Object.defineProperty(o, k2, desc);
}) :
/* istanbul ignore next */
(cov_11pqkh4977().b[1][1]++, function (o, m, k, k2) {
  /* istanbul ignore next */
  cov_11pqkh4977().f[2]++;
  cov_11pqkh4977().s[8]++;
  if (k2 === undefined) {
    /* istanbul ignore next */
    cov_11pqkh4977().b[7][0]++;
    cov_11pqkh4977().s[9]++;
    k2 = k;
  } else
  /* istanbul ignore next */
  {
    cov_11pqkh4977().b[7][1]++;
  }
  cov_11pqkh4977().s[10]++;
  o[k2] = m[k];
})));
var __setModuleDefault =
/* istanbul ignore next */
(cov_11pqkh4977().s[11]++,
/* istanbul ignore next */
(cov_11pqkh4977().b[8][0]++, this) &&
/* istanbul ignore next */
(cov_11pqkh4977().b[8][1]++, this.__setModuleDefault) ||
/* istanbul ignore next */
(cov_11pqkh4977().b[8][2]++, Object.create ?
/* istanbul ignore next */
(cov_11pqkh4977().b[9][0]++, function (o, v) {
  /* istanbul ignore next */
  cov_11pqkh4977().f[3]++;
  cov_11pqkh4977().s[12]++;
  Object.defineProperty(o, "default", {
    enumerable: true,
    value: v
  });
}) :
/* istanbul ignore next */
(cov_11pqkh4977().b[9][1]++, function (o, v) {
  /* istanbul ignore next */
  cov_11pqkh4977().f[4]++;
  cov_11pqkh4977().s[13]++;
  o["default"] = v;
})));
var __importStar =
/* istanbul ignore next */
(cov_11pqkh4977().s[14]++,
/* istanbul ignore next */
(cov_11pqkh4977().b[10][0]++, this) &&
/* istanbul ignore next */
(cov_11pqkh4977().b[10][1]++, this.__importStar) ||
/* istanbul ignore next */
(cov_11pqkh4977().b[10][2]++, function () {
  /* istanbul ignore next */
  cov_11pqkh4977().f[5]++;
  cov_11pqkh4977().s[15]++;
  var ownKeys = function (o) {
    /* istanbul ignore next */
    cov_11pqkh4977().f[6]++;
    cov_11pqkh4977().s[16]++;
    ownKeys =
    /* istanbul ignore next */
    (cov_11pqkh4977().b[11][0]++, Object.getOwnPropertyNames) ||
    /* istanbul ignore next */
    (cov_11pqkh4977().b[11][1]++, function (o) {
      /* istanbul ignore next */
      cov_11pqkh4977().f[7]++;
      var ar =
      /* istanbul ignore next */
      (cov_11pqkh4977().s[17]++, []);
      /* istanbul ignore next */
      cov_11pqkh4977().s[18]++;
      for (var k in o) {
        /* istanbul ignore next */
        cov_11pqkh4977().s[19]++;
        if (Object.prototype.hasOwnProperty.call(o, k)) {
          /* istanbul ignore next */
          cov_11pqkh4977().b[12][0]++;
          cov_11pqkh4977().s[20]++;
          ar[ar.length] = k;
        } else
        /* istanbul ignore next */
        {
          cov_11pqkh4977().b[12][1]++;
        }
      }
      /* istanbul ignore next */
      cov_11pqkh4977().s[21]++;
      return ar;
    });
    /* istanbul ignore next */
    cov_11pqkh4977().s[22]++;
    return ownKeys(o);
  };
  /* istanbul ignore next */
  cov_11pqkh4977().s[23]++;
  return function (mod) {
    /* istanbul ignore next */
    cov_11pqkh4977().f[8]++;
    cov_11pqkh4977().s[24]++;
    if (
    /* istanbul ignore next */
    (cov_11pqkh4977().b[14][0]++, mod) &&
    /* istanbul ignore next */
    (cov_11pqkh4977().b[14][1]++, mod.__esModule)) {
      /* istanbul ignore next */
      cov_11pqkh4977().b[13][0]++;
      cov_11pqkh4977().s[25]++;
      return mod;
    } else
    /* istanbul ignore next */
    {
      cov_11pqkh4977().b[13][1]++;
    }
    var result =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[26]++, {});
    /* istanbul ignore next */
    cov_11pqkh4977().s[27]++;
    if (mod != null) {
      /* istanbul ignore next */
      cov_11pqkh4977().b[15][0]++;
      cov_11pqkh4977().s[28]++;
      for (var k =
        /* istanbul ignore next */
        (cov_11pqkh4977().s[29]++, ownKeys(mod)), i =
        /* istanbul ignore next */
        (cov_11pqkh4977().s[30]++, 0); i < k.length; i++) {
        /* istanbul ignore next */
        cov_11pqkh4977().s[31]++;
        if (k[i] !== "default") {
          /* istanbul ignore next */
          cov_11pqkh4977().b[16][0]++;
          cov_11pqkh4977().s[32]++;
          __createBinding(result, mod, k[i]);
        } else
        /* istanbul ignore next */
        {
          cov_11pqkh4977().b[16][1]++;
        }
      }
    } else
    /* istanbul ignore next */
    {
      cov_11pqkh4977().b[15][1]++;
    }
    cov_11pqkh4977().s[33]++;
    __setModuleDefault(result, mod);
    /* istanbul ignore next */
    cov_11pqkh4977().s[34]++;
    return result;
  };
}()));
/* istanbul ignore next */
cov_11pqkh4977().s[35]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_11pqkh4977().s[36]++;
exports.AIOptimizationService = void 0;
/* istanbul ignore next */
cov_11pqkh4977().s[37]++;
exports.getAIOptimizationService = getAIOptimizationService;
/* istanbul ignore next */
cov_11pqkh4977().s[38]++;
exports.useAIOptimization = useAIOptimization;
const react_1 =
/* istanbul ignore next */
(cov_11pqkh4977().s[39]++, require("react"));
const ort =
/* istanbul ignore next */
(cov_11pqkh4977().s[40]++, __importStar(require("onnxruntime-web")));
class AIOptimizationService {
  constructor(config) {
    /* istanbul ignore next */
    cov_11pqkh4977().f[9]++;
    cov_11pqkh4977().s[41]++;
    this.session = null;
    /* istanbul ignore next */
    cov_11pqkh4977().s[42]++;
    this.modelCache = new Map();
    /* istanbul ignore next */
    cov_11pqkh4977().s[43]++;
    this.isInitialized = false;
    /* istanbul ignore next */
    cov_11pqkh4977().s[44]++;
    this.config = {
      enableGPU: false,
      batchSize: 1,
      confidenceThreshold: 0.7,
      optimizationGoals: [{
        type: 'energy_efficiency',
        weight: 0.4
      }, {
        type: 'cost_reduction',
        weight: 0.3
      }, {
        type: 'comfort',
        weight: 0.2
      }, {
        type: 'environmental',
        weight: 0.1
      }],
      ...config
    };
  }
  /**
   * Initialize the AI optimization service
   */
  async initialize() {
    /* istanbul ignore next */
    cov_11pqkh4977().f[10]++;
    cov_11pqkh4977().s[45]++;
    try {
      /* istanbul ignore next */
      cov_11pqkh4977().s[46]++;
      // Configure ONNX runtime
      if (this.config.enableGPU) {
        /* istanbul ignore next */
        cov_11pqkh4977().b[17][0]++;
        cov_11pqkh4977().s[47]++;
        ort.env.wasm.numThreads = 4;
        /* istanbul ignore next */
        cov_11pqkh4977().s[48]++;
        ort.env.wasm.simd = true;
      } else
      /* istanbul ignore next */
      {
        cov_11pqkh4977().b[17][1]++;
      }
      // Load main optimization model
      cov_11pqkh4977().s[49]++;
      this.session = await ort.InferenceSession.create(this.config.modelPath);
      // Load specialized models
      /* istanbul ignore next */
      cov_11pqkh4977().s[50]++;
      await this.loadSpecializedModels();
      /* istanbul ignore next */
      cov_11pqkh4977().s[51]++;
      this.isInitialized = true;
      /* istanbul ignore next */
      cov_11pqkh4977().s[52]++;
      console.log('AI Optimization Service initialized successfully');
    } catch (error) {
      /* istanbul ignore next */
      cov_11pqkh4977().s[53]++;
      console.error('Failed to initialize AI Optimization Service:', error);
      /* istanbul ignore next */
      cov_11pqkh4977().s[54]++;
      throw error;
    }
  }
  /**
   * Optimize HVAC system configuration
   */
  async optimizeSystem(input) {
    /* istanbul ignore next */
    cov_11pqkh4977().f[11]++;
    cov_11pqkh4977().s[55]++;
    if (
    /* istanbul ignore next */
    (cov_11pqkh4977().b[19][0]++, !this.isInitialized) ||
    /* istanbul ignore next */
    (cov_11pqkh4977().b[19][1]++, !this.session)) {
      /* istanbul ignore next */
      cov_11pqkh4977().b[18][0]++;
      cov_11pqkh4977().s[56]++;
      throw new Error('AI Optimization Service not initialized');
    } else
    /* istanbul ignore next */
    {
      cov_11pqkh4977().b[18][1]++;
    }
    cov_11pqkh4977().s[57]++;
    try {
      // Prepare input data
      const modelInput =
      /* istanbul ignore next */
      (cov_11pqkh4977().s[58]++, await this.prepareModelInput(input));
      // Run inference
      const results =
      /* istanbul ignore next */
      (cov_11pqkh4977().s[59]++, await this.session.run(modelInput));
      // Process results
      const optimizationResult =
      /* istanbul ignore next */
      (cov_11pqkh4977().s[60]++, await this.processResults(results, input));
      // Validate recommendations
      const validatedResult =
      /* istanbul ignore next */
      (cov_11pqkh4977().s[61]++, await this.validateRecommendations(optimizationResult, input));
      /* istanbul ignore next */
      cov_11pqkh4977().s[62]++;
      return validatedResult;
    } catch (error) {
      /* istanbul ignore next */
      cov_11pqkh4977().s[63]++;
      console.error('Optimization failed:', error);
      /* istanbul ignore next */
      cov_11pqkh4977().s[64]++;
      throw error;
    }
  }
  /**
   * Predict energy consumption
   */
  async predictEnergyConsumption(hvacSystem, environmentalData, timeHorizon =
  /* istanbul ignore next */
  (cov_11pqkh4977().b[20][0]++, 24) // hours
  ) {
    /* istanbul ignore next */
    cov_11pqkh4977().f[12]++;
    const energyModel =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[65]++, this.modelCache.get('energy_prediction'));
    /* istanbul ignore next */
    cov_11pqkh4977().s[66]++;
    if (!energyModel) {
      /* istanbul ignore next */
      cov_11pqkh4977().b[21][0]++;
      cov_11pqkh4977().s[67]++;
      throw new Error('Energy prediction model not loaded');
    } else
    /* istanbul ignore next */
    {
      cov_11pqkh4977().b[21][1]++;
    }
    const input =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[68]++, this.prepareEnergyInput(hvacSystem, environmentalData, timeHorizon));
    const results =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[69]++, await energyModel.run(input));
    /* istanbul ignore next */
    cov_11pqkh4977().s[70]++;
    return Array.from(results.energy_consumption.data);
  }
  /**
   * Detect performance anomalies
   */
  async detectAnomalies(performanceData, threshold =
  /* istanbul ignore next */
  (cov_11pqkh4977().b[22][0]++, 0.8)) {
    /* istanbul ignore next */
    cov_11pqkh4977().f[13]++;
    const anomalyModel =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[71]++, this.modelCache.get('anomaly_detection'));
    /* istanbul ignore next */
    cov_11pqkh4977().s[72]++;
    if (!anomalyModel) {
      /* istanbul ignore next */
      cov_11pqkh4977().b[23][0]++;
      cov_11pqkh4977().s[73]++;
      throw new Error('Anomaly detection model not loaded');
    } else
    /* istanbul ignore next */
    {
      cov_11pqkh4977().b[23][1]++;
    }
    const input =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[74]++, this.prepareAnomalyInput(performanceData));
    const results =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[75]++, await anomalyModel.run(input));
    const anomalyScores =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[76]++, Array.from(results.anomaly_scores.data));
    const confidenceScores =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[77]++, Array.from(results.confidence.data));
    const anomalies =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[78]++, anomalyScores.map((score, index) => {
      /* istanbul ignore next */
      cov_11pqkh4977().f[14]++;
      cov_11pqkh4977().s[79]++;
      return score > threshold ?
      /* istanbul ignore next */
      (cov_11pqkh4977().b[24][0]++, index) :
      /* istanbul ignore next */
      (cov_11pqkh4977().b[24][1]++, -1);
    }).filter(index => {
      /* istanbul ignore next */
      cov_11pqkh4977().f[15]++;
      cov_11pqkh4977().s[80]++;
      return index !== -1;
    }));
    /* istanbul ignore next */
    cov_11pqkh4977().s[81]++;
    return {
      anomalies,
      confidence: anomalies.map(index => {
        /* istanbul ignore next */
        cov_11pqkh4977().f[16]++;
        cov_11pqkh4977().s[82]++;
        return confidenceScores[index];
      })
    };
  }
  /**
   * Generate equipment sizing recommendations
   */
  async recommendEquipmentSizing(buildingData, loadRequirements) {
    /* istanbul ignore next */
    cov_11pqkh4977().f[17]++;
    const sizingModel =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[83]++, this.modelCache.get('equipment_sizing'));
    /* istanbul ignore next */
    cov_11pqkh4977().s[84]++;
    if (!sizingModel) {
      /* istanbul ignore next */
      cov_11pqkh4977().b[25][0]++;
      cov_11pqkh4977().s[85]++;
      throw new Error('Equipment sizing model not loaded');
    } else
    /* istanbul ignore next */
    {
      cov_11pqkh4977().b[25][1]++;
    }
    const input =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[86]++, this.prepareSizingInput(buildingData, loadRequirements));
    const results =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[87]++, await sizingModel.run(input));
    /* istanbul ignore next */
    cov_11pqkh4977().s[88]++;
    return {
      equipment: this.decodeEquipmentTypes(results.equipment_types.data),
      sizes: Array.from(results.equipment_sizes.data),
      efficiency_ratings: Array.from(results.efficiency_ratings.data),
      cost_estimates: Array.from(results.cost_estimates.data)
    };
  }
  /**
   * Analyze environmental impact
   */
  async analyzeEnvironmentalImpact(hvacSystem, energyConsumption) {
    /* istanbul ignore next */
    cov_11pqkh4977().f[18]++;
    // Calculate carbon footprint
    const carbonIntensity =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[89]++, 0.4); // kg CO2/kWh (average grid)
    const totalEnergy =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[90]++, energyConsumption.reduce((sum, consumption) => {
      /* istanbul ignore next */
      cov_11pqkh4977().f[19]++;
      cov_11pqkh4977().s[91]++;
      return sum + consumption;
    }, 0));
    const carbonFootprint =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[92]++, totalEnergy * carbonIntensity);
    // Assess renewable potential
    const renewablePotential =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[93]++, await this.assessRenewablePotential(hvacSystem));
    // Calculate sustainability score
    const sustainabilityScore =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[94]++, this.calculateSustainabilityScore(hvacSystem, carbonFootprint, renewablePotential));
    // Generate recommendations
    const recommendations =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[95]++, this.generateEnvironmentalRecommendations(sustainabilityScore, renewablePotential));
    /* istanbul ignore next */
    cov_11pqkh4977().s[96]++;
    return {
      carbon_footprint: carbonFootprint,
      renewable_potential: renewablePotential,
      sustainability_score: sustainabilityScore,
      recommendations
    };
  }
  /**
   * Get optimization insights
   */
  getOptimizationInsights(result) {
    /* istanbul ignore next */
    cov_11pqkh4977().f[20]++;
    const insights =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[97]++, []);
    const priorityActions =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[98]++, []);
    const potentialIssues =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[99]++, []);
    // Analyze savings potential
    /* istanbul ignore next */
    cov_11pqkh4977().s[100]++;
    if (result.predicted_savings.energy > 20) {
      /* istanbul ignore next */
      cov_11pqkh4977().b[26][0]++;
      cov_11pqkh4977().s[101]++;
      insights.push(`Significant energy savings potential: ${result.predicted_savings.energy.toFixed(1)}%`);
    } else
    /* istanbul ignore next */
    {
      cov_11pqkh4977().b[26][1]++;
    }
    cov_11pqkh4977().s[102]++;
    if (result.predicted_savings.cost > 5000) {
      /* istanbul ignore next */
      cov_11pqkh4977().b[27][0]++;
      cov_11pqkh4977().s[103]++;
      insights.push(`Annual cost savings: $${result.predicted_savings.cost.toLocaleString()}`);
    } else
    /* istanbul ignore next */
    {
      cov_11pqkh4977().b[27][1]++;
    }
    // Identify priority actions
    const highPrioritySteps =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[104]++, result.implementation_plan.filter(step => {
      /* istanbul ignore next */
      cov_11pqkh4977().f[21]++;
      cov_11pqkh4977().s[105]++;
      return step.priority === 'high';
    }).sort((a, b) => {
      /* istanbul ignore next */
      cov_11pqkh4977().f[22]++;
      cov_11pqkh4977().s[106]++;
      return b.expected_impact - a.expected_impact;
    }));
    /* istanbul ignore next */
    cov_11pqkh4977().s[107]++;
    priorityActions.push(...highPrioritySteps.slice(0, 3).map(step => {
      /* istanbul ignore next */
      cov_11pqkh4977().f[23]++;
      cov_11pqkh4977().s[108]++;
      return step.description;
    }));
    // Identify potential issues
    const highRiskFactors =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[109]++, result.risk_assessment.filter(risk => {
      /* istanbul ignore next */
      cov_11pqkh4977().f[24]++;
      cov_11pqkh4977().s[110]++;
      return risk.probability * risk.impact > 0.5;
    }).sort((a, b) => {
      /* istanbul ignore next */
      cov_11pqkh4977().f[25]++;
      cov_11pqkh4977().s[111]++;
      return b.probability * b.impact - a.probability * a.impact;
    }));
    /* istanbul ignore next */
    cov_11pqkh4977().s[112]++;
    potentialIssues.push(...highRiskFactors.slice(0, 3).map(risk => {
      /* istanbul ignore next */
      cov_11pqkh4977().f[26]++;
      cov_11pqkh4977().s[113]++;
      return risk.description;
    }));
    /* istanbul ignore next */
    cov_11pqkh4977().s[114]++;
    return {
      key_insights: insights,
      priority_actions: priorityActions,
      potential_issues: potentialIssues
    };
  }
  async loadSpecializedModels() {
    /* istanbul ignore next */
    cov_11pqkh4977().f[27]++;
    const models =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[115]++, [{
      name: 'energy_prediction',
      path: '/models/energy_prediction.onnx'
    }, {
      name: 'anomaly_detection',
      path: '/models/anomaly_detection.onnx'
    }, {
      name: 'equipment_sizing',
      path: '/models/equipment_sizing.onnx'
    }, {
      name: 'load_forecasting',
      path: '/models/load_forecasting.onnx'
    }]);
    /* istanbul ignore next */
    cov_11pqkh4977().s[116]++;
    for (const model of models) {
      /* istanbul ignore next */
      cov_11pqkh4977().s[117]++;
      try {
        const session =
        /* istanbul ignore next */
        (cov_11pqkh4977().s[118]++, await ort.InferenceSession.create(model.path));
        /* istanbul ignore next */
        cov_11pqkh4977().s[119]++;
        this.modelCache.set(model.name, session);
        /* istanbul ignore next */
        cov_11pqkh4977().s[120]++;
        console.log(`Loaded ${model.name} model`);
      } catch (error) {
        /* istanbul ignore next */
        cov_11pqkh4977().s[121]++;
        console.warn(`Failed to load ${model.name} model:`, error);
      }
    }
  }
  async prepareModelInput(input) {
    /* istanbul ignore next */
    cov_11pqkh4977().f[28]++;
    // Normalize and prepare input data for the model
    const features =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[122]++, [
    // Building features
    input.buildingData.area / 10000,
    // Normalize area
    input.buildingData.volume / 100000,
    // Normalize volume
    input.buildingData.occupancy / 1000,
    // Normalize occupancy
    input.buildingData.insulation / 50,
    // Normalize R-value
    input.buildingData.floors / 50,
    // Normalize floors
    input.buildingData.zoneCount / 20,
    // Normalize zones
    // Environmental features
    ...input.environmentalData.outdoorTemperature.slice(0, 24).map(t => {
      /* istanbul ignore next */
      cov_11pqkh4977().f[29]++;
      cov_11pqkh4977().s[123]++;
      return t / 100;
    }), ...input.environmentalData.humidity.slice(0, 24).map(h => {
      /* istanbul ignore next */
      cov_11pqkh4977().f[30]++;
      cov_11pqkh4977().s[124]++;
      return h / 100;
    }),
    // Operational features
    input.operationalData.currentLoad / 1000,
    // Normalize load
    input.operationalData.performanceMetrics.cop / 10, input.operationalData.performanceMetrics.eer / 30, input.operationalData.performanceMetrics.capacity_utilization,
    // Constraint features
    input.constraints.budget / 100000,
    // Normalize budget
    input.constraints.timeline / 365 // Normalize timeline
    ]);
    // Pad or truncate to expected input size
    const inputSize =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[125]++, 100); // Expected model input size
    const paddedFeatures =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[126]++, features.slice(0, inputSize));
    /* istanbul ignore next */
    cov_11pqkh4977().s[127]++;
    while (paddedFeatures.length < inputSize) {
      /* istanbul ignore next */
      cov_11pqkh4977().s[128]++;
      paddedFeatures.push(0);
    }
    /* istanbul ignore next */
    cov_11pqkh4977().s[129]++;
    return {
      input: new ort.Tensor('float32', new Float32Array(paddedFeatures), [1, inputSize])
    };
  }
  async processResults(results, input) {
    /* istanbul ignore next */
    cov_11pqkh4977().f[31]++;
    // Process model outputs into structured recommendations
    const outputData =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[130]++, results.output.data);
    // Extract different types of recommendations
    const recommendations =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[131]++, this.extractRecommendations(outputData, input));
    const savings =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[132]++, this.calculateSavings(outputData, input));
    const implementationPlan =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[133]++, this.generateImplementationPlan(recommendations, input));
    const roiAnalysis =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[134]++, this.calculateROI(savings, implementationPlan));
    const riskAssessment =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[135]++, this.assessRisks(recommendations, input));
    /* istanbul ignore next */
    cov_11pqkh4977().s[136]++;
    return {
      recommendations,
      predicted_savings: savings,
      confidence_score: Math.min(outputData[0], 1.0),
      implementation_plan: implementationPlan,
      roi_analysis: roiAnalysis,
      risk_assessment: riskAssessment
    };
  }
  extractRecommendations(outputData, input) {
    /* istanbul ignore next */
    cov_11pqkh4977().f[32]++;
    // Extract and decode recommendations from model output
    const recommendations =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[137]++, []);
    // This is a simplified example - real implementation would decode model outputs
    /* istanbul ignore next */
    cov_11pqkh4977().s[138]++;
    if (outputData[1] > 0.5) {
      /* istanbul ignore next */
      cov_11pqkh4977().b[28][0]++;
      cov_11pqkh4977().s[139]++;
      recommendations.push({
        id: 'upgrade_hvac',
        type: 'equipment_upgrade',
        description: 'Upgrade HVAC Equipment - Replace aging equipment with high-efficiency units',
        priority: 'high',
        implementation_cost: outputData[3] * 10000,
        annual_savings: outputData[2] * 100,
        payback_period: outputData[4] * 10,
        confidence: 0.85,
        impact_areas: ['energy_efficiency', 'cost_reduction']
      });
    } else
    /* istanbul ignore next */
    {
      cov_11pqkh4977().b[28][1]++;
    }
    cov_11pqkh4977().s[140]++;
    if (outputData[5] > 0.5) {
      /* istanbul ignore next */
      cov_11pqkh4977().b[29][0]++;
      cov_11pqkh4977().s[141]++;
      recommendations.push({
        id: 'optimize_controls',
        type: 'control_strategy',
        description: 'Optimize Control Systems - Implement smart controls and scheduling',
        priority: 'medium',
        implementation_cost: outputData[7] * 5000,
        annual_savings: outputData[6] * 100,
        payback_period: outputData[8] * 5,
        confidence: 0.75,
        impact_areas: ['energy_efficiency', 'comfort']
      });
    } else
    /* istanbul ignore next */
    {
      cov_11pqkh4977().b[29][1]++;
    }
    cov_11pqkh4977().s[142]++;
    return recommendations;
  }
  calculateSavings(outputData, input) {
    /* istanbul ignore next */
    cov_11pqkh4977().f[33]++;
    const currentEnergyCost =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[143]++, input.operationalData.energyConsumption.reduce((sum, e) => {
      /* istanbul ignore next */
      cov_11pqkh4977().f[34]++;
      cov_11pqkh4977().s[144]++;
      return sum + e;
    }, 0) * 0.12); // $0.12/kWh
    /* istanbul ignore next */
    cov_11pqkh4977().s[145]++;
    return {
      energy: outputData[10] * 100,
      // Percentage energy savings
      cost: currentEnergyCost * outputData[10],
      // Annual cost savings
      emissions: currentEnergyCost * outputData[10] * 0.4 * 1000 // kg CO2 equivalent
    };
  }
  generateImplementationPlan(recommendations, input) {
    /* istanbul ignore next */
    cov_11pqkh4977().f[35]++;
    cov_11pqkh4977().s[146]++;
    return recommendations.map((rec, index) => {
      /* istanbul ignore next */
      cov_11pqkh4977().f[36]++;
      cov_11pqkh4977().s[147]++;
      return {
        id: rec.id,
        description: rec.description,
        priority: rec.priority,
        estimated_cost: rec.implementation_cost,
        estimated_duration: rec.payback_period * 30,
        // Convert to days
        dependencies: index > 0 ?
        /* istanbul ignore next */
        (cov_11pqkh4977().b[30][0]++, [recommendations[index - 1].id]) :
        /* istanbul ignore next */
        (cov_11pqkh4977().b[30][1]++, []),
        expected_impact: rec.annual_savings
      };
    });
  }
  calculateROI(savings, plan) {
    /* istanbul ignore next */
    cov_11pqkh4977().f[37]++;
    const totalInvestment =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[148]++, plan.reduce((sum, step) => {
      /* istanbul ignore next */
      cov_11pqkh4977().f[38]++;
      cov_11pqkh4977().s[149]++;
      return sum + step.estimated_cost;
    }, 0));
    const annualSavings =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[150]++, savings.cost);
    /* istanbul ignore next */
    cov_11pqkh4977().s[151]++;
    return {
      initial_investment: totalInvestment,
      annual_savings: annualSavings,
      payback_period: totalInvestment / annualSavings,
      net_present_value: this.calculateNPV(totalInvestment, annualSavings, 0.05, 10),
      internal_rate_of_return: this.calculateIRR(totalInvestment, annualSavings, 10)
    };
  }
  assessRisks(recommendations, input) {
    /* istanbul ignore next */
    cov_11pqkh4977().f[39]++;
    const risks =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[152]++, []);
    // Budget risk
    const totalCost =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[153]++, recommendations.reduce((sum, rec) => {
      /* istanbul ignore next */
      cov_11pqkh4977().f[40]++;
      cov_11pqkh4977().s[154]++;
      return sum + rec.implementation_cost;
    }, 0));
    /* istanbul ignore next */
    cov_11pqkh4977().s[155]++;
    if (totalCost > input.constraints.budget * 0.8) {
      /* istanbul ignore next */
      cov_11pqkh4977().b[31][0]++;
      cov_11pqkh4977().s[156]++;
      risks.push({
        type: 'budget',
        description: 'Implementation cost approaches budget limit',
        probability: 0.7,
        impact: 0.8,
        mitigation: 'Phase implementation or seek additional funding'
      });
    } else
    /* istanbul ignore next */
    {
      cov_11pqkh4977().b[31][1]++;
    }
    // Timeline risk
    const totalDuration =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[157]++, recommendations.length * 30); // Simplified
    /* istanbul ignore next */
    cov_11pqkh4977().s[158]++;
    if (totalDuration > input.constraints.timeline * 0.8) {
      /* istanbul ignore next */
      cov_11pqkh4977().b[32][0]++;
      cov_11pqkh4977().s[159]++;
      risks.push({
        type: 'timeline',
        description: 'Implementation timeline may exceed constraints',
        probability: 0.6,
        impact: 0.6,
        mitigation: 'Parallel implementation or scope reduction'
      });
    } else
    /* istanbul ignore next */
    {
      cov_11pqkh4977().b[32][1]++;
    }
    cov_11pqkh4977().s[160]++;
    return risks;
  }
  prepareEnergyInput(hvacSystem, environmentalData, timeHorizon) {
    /* istanbul ignore next */
    cov_11pqkh4977().f[41]++;
    // Prepare input for energy prediction model
    const features =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[161]++, [...environmentalData.outdoorTemperature.slice(0, timeHorizon), ...environmentalData.humidity.slice(0, timeHorizon)
    // Add more features as needed
    ]);
    /* istanbul ignore next */
    cov_11pqkh4977().s[162]++;
    return {
      input: new ort.Tensor('float32', new Float32Array(features), [1, features.length])
    };
  }
  prepareAnomalyInput(performanceData) {
    /* istanbul ignore next */
    cov_11pqkh4977().f[42]++;
    const features =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[163]++, performanceData.flatMap(data => {
      /* istanbul ignore next */
      cov_11pqkh4977().f[43]++;
      cov_11pqkh4977().s[164]++;
      return [data.cop, data.eer, data.capacity_utilization, data.temperature_variance];
    }));
    /* istanbul ignore next */
    cov_11pqkh4977().s[165]++;
    return {
      input: new ort.Tensor('float32', new Float32Array(features), [1, features.length])
    };
  }
  prepareSizingInput(buildingData, loadRequirements) {
    /* istanbul ignore next */
    cov_11pqkh4977().f[44]++;
    const features =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[166]++, [buildingData.area, buildingData.volume, buildingData.occupancy, ...loadRequirements.slice(0, 24)]);
    /* istanbul ignore next */
    cov_11pqkh4977().s[167]++;
    return {
      input: new ort.Tensor('float32', new Float32Array(features), [1, features.length])
    };
  }
  decodeEquipmentTypes(data) {
    /* istanbul ignore next */
    cov_11pqkh4977().f[45]++;
    // Decode equipment type indices to names
    const equipmentTypes =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[168]++, ['Heat Pump', 'Chiller', 'Boiler', 'Air Handler', 'Fan Coil']);
    /* istanbul ignore next */
    cov_11pqkh4977().s[169]++;
    return Array.from(data).map(index => {
      /* istanbul ignore next */
      cov_11pqkh4977().f[46]++;
      cov_11pqkh4977().s[170]++;
      return /* istanbul ignore next */(cov_11pqkh4977().b[33][0]++, equipmentTypes[Math.floor(index)]) ||
      /* istanbul ignore next */
      (cov_11pqkh4977().b[33][1]++, 'Unknown');
    });
  }
  async assessRenewablePotential(hvacSystem) {
    /* istanbul ignore next */
    cov_11pqkh4977().f[47]++;
    cov_11pqkh4977().s[171]++;
    // Simplified renewable potential assessment
    return Math.random() * 0.5 + 0.3; // 30-80% potential
  }
  calculateSustainabilityScore(hvacSystem, carbonFootprint, renewablePotential) {
    /* istanbul ignore next */
    cov_11pqkh4977().f[48]++;
    // Simplified sustainability scoring
    const efficiencyScore =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[172]++, Math.min(
    /* istanbul ignore next */
    (cov_11pqkh4977().b[34][0]++, hvacSystem.efficiency) ||
    /* istanbul ignore next */
    (cov_11pqkh4977().b[34][1]++, 0.8), 1.0));
    const carbonScore =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[173]++, Math.max(0, 1 - carbonFootprint / 10000));
    const renewableScore =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[174]++, renewablePotential);
    /* istanbul ignore next */
    cov_11pqkh4977().s[175]++;
    return (efficiencyScore * 0.4 + carbonScore * 0.4 + renewableScore * 0.2) * 100;
  }
  generateEnvironmentalRecommendations(sustainabilityScore, renewablePotential) {
    /* istanbul ignore next */
    cov_11pqkh4977().f[49]++;
    const recommendations =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[176]++, []);
    /* istanbul ignore next */
    cov_11pqkh4977().s[177]++;
    if (sustainabilityScore < 60) {
      /* istanbul ignore next */
      cov_11pqkh4977().b[35][0]++;
      cov_11pqkh4977().s[178]++;
      recommendations.push('Consider upgrading to high-efficiency equipment');
    } else
    /* istanbul ignore next */
    {
      cov_11pqkh4977().b[35][1]++;
    }
    cov_11pqkh4977().s[179]++;
    if (renewablePotential > 0.5) {
      /* istanbul ignore next */
      cov_11pqkh4977().b[36][0]++;
      cov_11pqkh4977().s[180]++;
      recommendations.push('Explore solar panel integration');
      /* istanbul ignore next */
      cov_11pqkh4977().s[181]++;
      recommendations.push('Consider geothermal heat pump systems');
    } else
    /* istanbul ignore next */
    {
      cov_11pqkh4977().b[36][1]++;
    }
    cov_11pqkh4977().s[182]++;
    recommendations.push('Implement smart scheduling to reduce peak demand');
    /* istanbul ignore next */
    cov_11pqkh4977().s[183]++;
    recommendations.push('Regular maintenance to maintain efficiency');
    /* istanbul ignore next */
    cov_11pqkh4977().s[184]++;
    return recommendations;
  }
  async validateRecommendations(result, input) {
    /* istanbul ignore next */
    cov_11pqkh4977().f[50]++;
    // Validate recommendations against constraints and feasibility
    const validatedRecommendations =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[185]++, result.recommendations.filter(rec => {
      /* istanbul ignore next */
      cov_11pqkh4977().f[51]++;
      cov_11pqkh4977().s[186]++;
      // Check budget constraints
      if (rec.implementation_cost > input.constraints.budget) {
        /* istanbul ignore next */
        cov_11pqkh4977().b[37][0]++;
        cov_11pqkh4977().s[187]++;
        return false;
      } else
      /* istanbul ignore next */
      {
        cov_11pqkh4977().b[37][1]++;
      }
      // Check timeline constraints
      cov_11pqkh4977().s[188]++;
      if (rec.payback_period * 30 > input.constraints.timeline) {
        /* istanbul ignore next */
        cov_11pqkh4977().b[38][0]++;
        cov_11pqkh4977().s[189]++;
        return false;
      } else
      /* istanbul ignore next */
      {
        cov_11pqkh4977().b[38][1]++;
      }
      cov_11pqkh4977().s[190]++;
      return true;
    }));
    /* istanbul ignore next */
    cov_11pqkh4977().s[191]++;
    return {
      ...result,
      recommendations: validatedRecommendations
    };
  }
  calculateNPV(investment, annualSavings, discountRate, years) {
    /* istanbul ignore next */
    cov_11pqkh4977().f[52]++;
    let npv =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[192]++, -investment);
    /* istanbul ignore next */
    cov_11pqkh4977().s[193]++;
    for (let year =
    /* istanbul ignore next */
    (cov_11pqkh4977().s[194]++, 1); year <= years; year++) {
      /* istanbul ignore next */
      cov_11pqkh4977().s[195]++;
      npv += annualSavings / Math.pow(1 + discountRate, year);
    }
    /* istanbul ignore next */
    cov_11pqkh4977().s[196]++;
    return npv;
  }
  calculateIRR(investment, annualSavings, years) {
    /* istanbul ignore next */
    cov_11pqkh4977().f[53]++;
    cov_11pqkh4977().s[197]++;
    // Simplified IRR calculation
    return annualSavings / investment * 100;
  }
  /**
   * Cleanup resources
   */
  async dispose() {
    /* istanbul ignore next */
    cov_11pqkh4977().f[54]++;
    cov_11pqkh4977().s[198]++;
    if (this.session) {
      /* istanbul ignore next */
      cov_11pqkh4977().b[39][0]++;
      cov_11pqkh4977().s[199]++;
      await this.session.release();
    } else
    /* istanbul ignore next */
    {
      cov_11pqkh4977().b[39][1]++;
    }
    cov_11pqkh4977().s[200]++;
    for (const [name, session] of this.modelCache) {
      /* istanbul ignore next */
      cov_11pqkh4977().s[201]++;
      await session.release();
    }
    /* istanbul ignore next */
    cov_11pqkh4977().s[202]++;
    this.modelCache.clear();
    /* istanbul ignore next */
    cov_11pqkh4977().s[203]++;
    this.isInitialized = false;
  }
}
/* istanbul ignore next */
cov_11pqkh4977().s[204]++;
exports.AIOptimizationService = AIOptimizationService;
// Singleton instance
let aiOptimizationService =
/* istanbul ignore next */
(cov_11pqkh4977().s[205]++, null);
function getAIOptimizationService(config) {
  /* istanbul ignore next */
  cov_11pqkh4977().f[55]++;
  cov_11pqkh4977().s[206]++;
  if (
  /* istanbul ignore next */
  (cov_11pqkh4977().b[41][0]++, !aiOptimizationService) &&
  /* istanbul ignore next */
  (cov_11pqkh4977().b[41][1]++, config)) {
    /* istanbul ignore next */
    cov_11pqkh4977().b[40][0]++;
    cov_11pqkh4977().s[207]++;
    aiOptimizationService = new AIOptimizationService(config);
  } else
  /* istanbul ignore next */
  {
    cov_11pqkh4977().b[40][1]++;
  }
  cov_11pqkh4977().s[208]++;
  return aiOptimizationService;
}
/* istanbul ignore next */
cov_11pqkh4977().s[209]++;
exports.default = AIOptimizationService;
// React Hook for AI Optimization
function useAIOptimization(config) {
  /* istanbul ignore next */
  cov_11pqkh4977().f[56]++;
  const [service, setService] =
  /* istanbul ignore next */
  (cov_11pqkh4977().s[210]++, (0, react_1.useState)(null));
  const [isInitialized, setIsInitialized] =
  /* istanbul ignore next */
  (cov_11pqkh4977().s[211]++, (0, react_1.useState)(false));
  const [isLoading, setIsLoading] =
  /* istanbul ignore next */
  (cov_11pqkh4977().s[212]++, (0, react_1.useState)(false));
  const [error, setError] =
  /* istanbul ignore next */
  (cov_11pqkh4977().s[213]++, (0, react_1.useState)(null));
  /* istanbul ignore next */
  cov_11pqkh4977().s[214]++;
  (0, react_1.useEffect)(() => {
    /* istanbul ignore next */
    cov_11pqkh4977().f[57]++;
    cov_11pqkh4977().s[215]++;
    if (config) {
      /* istanbul ignore next */
      cov_11pqkh4977().b[42][0]++;
      const aiService =
      /* istanbul ignore next */
      (cov_11pqkh4977().s[216]++, new AIOptimizationService(config));
      /* istanbul ignore next */
      cov_11pqkh4977().s[217]++;
      setService(aiService);
      /* istanbul ignore next */
      cov_11pqkh4977().s[218]++;
      aiService.initialize().then(() => {
        /* istanbul ignore next */
        cov_11pqkh4977().f[58]++;
        cov_11pqkh4977().s[219]++;
        return setIsInitialized(true);
      }).catch(err => {
        /* istanbul ignore next */
        cov_11pqkh4977().f[59]++;
        cov_11pqkh4977().s[220]++;
        return setError(err.message);
      });
    } else
    /* istanbul ignore next */
    {
      cov_11pqkh4977().b[42][1]++;
    }
  }, [config]);
  const optimizeSystem =
  /* istanbul ignore next */
  (cov_11pqkh4977().s[221]++, (0, react_1.useCallback)(async input => {
    /* istanbul ignore next */
    cov_11pqkh4977().f[60]++;
    cov_11pqkh4977().s[222]++;
    if (
    /* istanbul ignore next */
    (cov_11pqkh4977().b[44][0]++, !service) ||
    /* istanbul ignore next */
    (cov_11pqkh4977().b[44][1]++, !isInitialized)) {
      /* istanbul ignore next */
      cov_11pqkh4977().b[43][0]++;
      cov_11pqkh4977().s[223]++;
      throw new Error('AI service not initialized');
    } else
    /* istanbul ignore next */
    {
      cov_11pqkh4977().b[43][1]++;
    }
    cov_11pqkh4977().s[224]++;
    setIsLoading(true);
    /* istanbul ignore next */
    cov_11pqkh4977().s[225]++;
    setError(null);
    /* istanbul ignore next */
    cov_11pqkh4977().s[226]++;
    try {
      const result =
      /* istanbul ignore next */
      (cov_11pqkh4977().s[227]++, await service.optimizeSystem(input));
      /* istanbul ignore next */
      cov_11pqkh4977().s[228]++;
      return result;
    } catch (err) {
      const errorMessage =
      /* istanbul ignore next */
      (cov_11pqkh4977().s[229]++, err instanceof Error ?
      /* istanbul ignore next */
      (cov_11pqkh4977().b[45][0]++, err.message) :
      /* istanbul ignore next */
      (cov_11pqkh4977().b[45][1]++, 'Optimization failed'));
      /* istanbul ignore next */
      cov_11pqkh4977().s[230]++;
      setError(errorMessage);
      /* istanbul ignore next */
      cov_11pqkh4977().s[231]++;
      throw err;
    } finally {
      /* istanbul ignore next */
      cov_11pqkh4977().s[232]++;
      setIsLoading(false);
    }
  }, [service, isInitialized]));
  const predictEnergy =
  /* istanbul ignore next */
  (cov_11pqkh4977().s[233]++, (0, react_1.useCallback)(async (hvacSystem, environmentalData, timeHorizon) => {
    /* istanbul ignore next */
    cov_11pqkh4977().f[61]++;
    cov_11pqkh4977().s[234]++;
    if (
    /* istanbul ignore next */
    (cov_11pqkh4977().b[47][0]++, !service) ||
    /* istanbul ignore next */
    (cov_11pqkh4977().b[47][1]++, !isInitialized)) {
      /* istanbul ignore next */
      cov_11pqkh4977().b[46][0]++;
      cov_11pqkh4977().s[235]++;
      throw new Error('AI service not initialized');
    } else
    /* istanbul ignore next */
    {
      cov_11pqkh4977().b[46][1]++;
    }
    cov_11pqkh4977().s[236]++;
    return service.predictEnergyConsumption(hvacSystem, environmentalData, timeHorizon);
  }, [service, isInitialized]));
  const detectAnomalies =
  /* istanbul ignore next */
  (cov_11pqkh4977().s[237]++, (0, react_1.useCallback)(async (performanceData, threshold) => {
    /* istanbul ignore next */
    cov_11pqkh4977().f[62]++;
    cov_11pqkh4977().s[238]++;
    if (
    /* istanbul ignore next */
    (cov_11pqkh4977().b[49][0]++, !service) ||
    /* istanbul ignore next */
    (cov_11pqkh4977().b[49][1]++, !isInitialized)) {
      /* istanbul ignore next */
      cov_11pqkh4977().b[48][0]++;
      cov_11pqkh4977().s[239]++;
      throw new Error('AI service not initialized');
    } else
    /* istanbul ignore next */
    {
      cov_11pqkh4977().b[48][1]++;
    }
    cov_11pqkh4977().s[240]++;
    return service.detectAnomalies(performanceData, threshold);
  }, [service, isInitialized]));
  /* istanbul ignore next */
  cov_11pqkh4977().s[241]++;
  return {
    isInitialized,
    isLoading,
    error,
    optimizeSystem,
    predictEnergy,
    detectAnomalies,
    service
  };
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJjb3ZfMTFwcWtoNDk3NyIsInBhdGgiLCJoYXNoIiwiZ2xvYmFsIiwiRnVuY3Rpb24iLCJnY3YiLCJjb3ZlcmFnZURhdGEiLCJzdGF0ZW1lbnRNYXAiLCJzdGFydCIsImxpbmUiLCJjb2x1bW4iLCJlbmQiLCJmbk1hcCIsIm5hbWUiLCJkZWNsIiwibG9jIiwiYnJhbmNoTWFwIiwidHlwZSIsImxvY2F0aW9ucyIsInVuZGVmaW5lZCIsInMiLCJmIiwiYiIsImlucHV0U291cmNlTWFwIiwiZmlsZSIsIm1hcHBpbmdzIiwibmFtZXMiLCJzb3VyY2VzIiwic291cmNlc0NvbnRlbnQiLCJ2ZXJzaW9uIiwiX2NvdmVyYWdlU2NoZW1hIiwiY292ZXJhZ2UiLCJhY3R1YWxDb3ZlcmFnZSIsImV4cG9ydHMiLCJnZXRBSU9wdGltaXphdGlvblNlcnZpY2UiLCJ1c2VBSU9wdGltaXphdGlvbiIsInJlYWN0XzEiLCJyZXF1aXJlIiwib3J0IiwiX19pbXBvcnRTdGFyIiwiQUlPcHRpbWl6YXRpb25TZXJ2aWNlIiwiY29uc3RydWN0b3IiLCJjb25maWciLCJzZXNzaW9uIiwibW9kZWxDYWNoZSIsIk1hcCIsImlzSW5pdGlhbGl6ZWQiLCJlbmFibGVHUFUiLCJiYXRjaFNpemUiLCJjb25maWRlbmNlVGhyZXNob2xkIiwib3B0aW1pemF0aW9uR29hbHMiLCJ3ZWlnaHQiLCJpbml0aWFsaXplIiwiZW52Iiwid2FzbSIsIm51bVRocmVhZHMiLCJzaW1kIiwiSW5mZXJlbmNlU2Vzc2lvbiIsImNyZWF0ZSIsIm1vZGVsUGF0aCIsImxvYWRTcGVjaWFsaXplZE1vZGVscyIsImNvbnNvbGUiLCJsb2ciLCJlcnJvciIsIm9wdGltaXplU3lzdGVtIiwiaW5wdXQiLCJFcnJvciIsIm1vZGVsSW5wdXQiLCJwcmVwYXJlTW9kZWxJbnB1dCIsInJlc3VsdHMiLCJydW4iLCJvcHRpbWl6YXRpb25SZXN1bHQiLCJwcm9jZXNzUmVzdWx0cyIsInZhbGlkYXRlZFJlc3VsdCIsInZhbGlkYXRlUmVjb21tZW5kYXRpb25zIiwicHJlZGljdEVuZXJneUNvbnN1bXB0aW9uIiwiaHZhY1N5c3RlbSIsImVudmlyb25tZW50YWxEYXRhIiwidGltZUhvcml6b24iLCJlbmVyZ3lNb2RlbCIsImdldCIsInByZXBhcmVFbmVyZ3lJbnB1dCIsIkFycmF5IiwiZnJvbSIsImVuZXJneV9jb25zdW1wdGlvbiIsImRhdGEiLCJkZXRlY3RBbm9tYWxpZXMiLCJwZXJmb3JtYW5jZURhdGEiLCJ0aHJlc2hvbGQiLCJhbm9tYWx5TW9kZWwiLCJwcmVwYXJlQW5vbWFseUlucHV0IiwiYW5vbWFseVNjb3JlcyIsImFub21hbHlfc2NvcmVzIiwiY29uZmlkZW5jZVNjb3JlcyIsImNvbmZpZGVuY2UiLCJhbm9tYWxpZXMiLCJtYXAiLCJzY29yZSIsImluZGV4IiwiZmlsdGVyIiwicmVjb21tZW5kRXF1aXBtZW50U2l6aW5nIiwiYnVpbGRpbmdEYXRhIiwibG9hZFJlcXVpcmVtZW50cyIsInNpemluZ01vZGVsIiwicHJlcGFyZVNpemluZ0lucHV0IiwiZXF1aXBtZW50IiwiZGVjb2RlRXF1aXBtZW50VHlwZXMiLCJlcXVpcG1lbnRfdHlwZXMiLCJzaXplcyIsImVxdWlwbWVudF9zaXplcyIsImVmZmljaWVuY3lfcmF0aW5ncyIsImNvc3RfZXN0aW1hdGVzIiwiYW5hbHl6ZUVudmlyb25tZW50YWxJbXBhY3QiLCJlbmVyZ3lDb25zdW1wdGlvbiIsImNhcmJvbkludGVuc2l0eSIsInRvdGFsRW5lcmd5IiwicmVkdWNlIiwic3VtIiwiY29uc3VtcHRpb24iLCJjYXJib25Gb290cHJpbnQiLCJyZW5ld2FibGVQb3RlbnRpYWwiLCJhc3Nlc3NSZW5ld2FibGVQb3RlbnRpYWwiLCJzdXN0YWluYWJpbGl0eVNjb3JlIiwiY2FsY3VsYXRlU3VzdGFpbmFiaWxpdHlTY29yZSIsInJlY29tbWVuZGF0aW9ucyIsImdlbmVyYXRlRW52aXJvbm1lbnRhbFJlY29tbWVuZGF0aW9ucyIsImNhcmJvbl9mb290cHJpbnQiLCJyZW5ld2FibGVfcG90ZW50aWFsIiwic3VzdGFpbmFiaWxpdHlfc2NvcmUiLCJnZXRPcHRpbWl6YXRpb25JbnNpZ2h0cyIsInJlc3VsdCIsImluc2lnaHRzIiwicHJpb3JpdHlBY3Rpb25zIiwicG90ZW50aWFsSXNzdWVzIiwicHJlZGljdGVkX3NhdmluZ3MiLCJlbmVyZ3kiLCJwdXNoIiwidG9GaXhlZCIsImNvc3QiLCJ0b0xvY2FsZVN0cmluZyIsImhpZ2hQcmlvcml0eVN0ZXBzIiwiaW1wbGVtZW50YXRpb25fcGxhbiIsInN0ZXAiLCJwcmlvcml0eSIsInNvcnQiLCJhIiwiZXhwZWN0ZWRfaW1wYWN0Iiwic2xpY2UiLCJkZXNjcmlwdGlvbiIsImhpZ2hSaXNrRmFjdG9ycyIsInJpc2tfYXNzZXNzbWVudCIsInJpc2siLCJwcm9iYWJpbGl0eSIsImltcGFjdCIsImtleV9pbnNpZ2h0cyIsInByaW9yaXR5X2FjdGlvbnMiLCJwb3RlbnRpYWxfaXNzdWVzIiwibW9kZWxzIiwibW9kZWwiLCJzZXQiLCJ3YXJuIiwiZmVhdHVyZXMiLCJhcmVhIiwidm9sdW1lIiwib2NjdXBhbmN5IiwiaW5zdWxhdGlvbiIsImZsb29ycyIsInpvbmVDb3VudCIsIm91dGRvb3JUZW1wZXJhdHVyZSIsInQiLCJodW1pZGl0eSIsImgiLCJvcGVyYXRpb25hbERhdGEiLCJjdXJyZW50TG9hZCIsInBlcmZvcm1hbmNlTWV0cmljcyIsImNvcCIsImVlciIsImNhcGFjaXR5X3V0aWxpemF0aW9uIiwiY29uc3RyYWludHMiLCJidWRnZXQiLCJ0aW1lbGluZSIsImlucHV0U2l6ZSIsInBhZGRlZEZlYXR1cmVzIiwibGVuZ3RoIiwiVGVuc29yIiwiRmxvYXQzMkFycmF5Iiwib3V0cHV0RGF0YSIsIm91dHB1dCIsImV4dHJhY3RSZWNvbW1lbmRhdGlvbnMiLCJzYXZpbmdzIiwiY2FsY3VsYXRlU2F2aW5ncyIsImltcGxlbWVudGF0aW9uUGxhbiIsImdlbmVyYXRlSW1wbGVtZW50YXRpb25QbGFuIiwicm9pQW5hbHlzaXMiLCJjYWxjdWxhdGVST0kiLCJyaXNrQXNzZXNzbWVudCIsImFzc2Vzc1Jpc2tzIiwiY29uZmlkZW5jZV9zY29yZSIsIk1hdGgiLCJtaW4iLCJyb2lfYW5hbHlzaXMiLCJpZCIsImltcGxlbWVudGF0aW9uX2Nvc3QiLCJhbm51YWxfc2F2aW5ncyIsInBheWJhY2tfcGVyaW9kIiwiaW1wYWN0X2FyZWFzIiwiY3VycmVudEVuZXJneUNvc3QiLCJlIiwiZW1pc3Npb25zIiwicmVjIiwiZXN0aW1hdGVkX2Nvc3QiLCJlc3RpbWF0ZWRfZHVyYXRpb24iLCJkZXBlbmRlbmNpZXMiLCJwbGFuIiwidG90YWxJbnZlc3RtZW50IiwiYW5udWFsU2F2aW5ncyIsImluaXRpYWxfaW52ZXN0bWVudCIsIm5ldF9wcmVzZW50X3ZhbHVlIiwiY2FsY3VsYXRlTlBWIiwiaW50ZXJuYWxfcmF0ZV9vZl9yZXR1cm4iLCJjYWxjdWxhdGVJUlIiLCJyaXNrcyIsInRvdGFsQ29zdCIsIm1pdGlnYXRpb24iLCJ0b3RhbER1cmF0aW9uIiwiZmxhdE1hcCIsInRlbXBlcmF0dXJlX3ZhcmlhbmNlIiwiZXF1aXBtZW50VHlwZXMiLCJmbG9vciIsInJhbmRvbSIsImVmZmljaWVuY3lTY29yZSIsImVmZmljaWVuY3kiLCJjYXJib25TY29yZSIsIm1heCIsInJlbmV3YWJsZVNjb3JlIiwidmFsaWRhdGVkUmVjb21tZW5kYXRpb25zIiwiaW52ZXN0bWVudCIsImRpc2NvdW50UmF0ZSIsInllYXJzIiwibnB2IiwieWVhciIsInBvdyIsImRpc3Bvc2UiLCJyZWxlYXNlIiwiY2xlYXIiLCJhaU9wdGltaXphdGlvblNlcnZpY2UiLCJkZWZhdWx0Iiwic2VydmljZSIsInNldFNlcnZpY2UiLCJ1c2VTdGF0ZSIsInNldElzSW5pdGlhbGl6ZWQiLCJpc0xvYWRpbmciLCJzZXRJc0xvYWRpbmciLCJzZXRFcnJvciIsInVzZUVmZmVjdCIsImFpU2VydmljZSIsInRoZW4iLCJjYXRjaCIsImVyciIsIm1lc3NhZ2UiLCJ1c2VDYWxsYmFjayIsImVycm9yTWVzc2FnZSIsInByZWRpY3RFbmVyZ3kiXSwic291cmNlcyI6WyJDOlxcVXNlcnNcXGpvaG5yXFxEb3dubG9hZHNcXFNpemVXaXNlX1N1aXRlX0FwcFxcZnJvbnRlbmRcXGxpYlxcc2VydmljZXNcXEFJT3B0aW1pemF0aW9uU2VydmljZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcclxuICogQUktUG93ZXJlZCBIVkFDIE9wdGltaXphdGlvbiBTZXJ2aWNlXHJcbiAqIFxyXG4gKiBJbnRlZ3JhdGVzIE9OTlguanMgbWFjaGluZSBsZWFybmluZyBtb2RlbHMgZm9yIGludGVsbGlnZW50IEhWQUMgc3lzdGVtXHJcbiAqIG9wdGltaXphdGlvbiBhbmQgZW5lcmd5IGVmZmljaWVuY3kgcmVjb21tZW5kYXRpb25zLlxyXG4gKiBcclxuICogRmVhdHVyZXM6XHJcbiAqIC0gRW5lcmd5IGVmZmljaWVuY3kgb3B0aW1pemF0aW9uXHJcbiAqIC0gTG9hZCBwcmVkaWN0aW9uIGFuZCBiYWxhbmNpbmdcclxuICogLSBFcXVpcG1lbnQgc2l6aW5nIHJlY29tbWVuZGF0aW9uc1xyXG4gKiAtIFBlcmZvcm1hbmNlIGFub21hbHkgZGV0ZWN0aW9uXHJcbiAqIC0gQ29zdCBvcHRpbWl6YXRpb24gYW5hbHlzaXNcclxuICogLSBFbnZpcm9ubWVudGFsIGltcGFjdCBhc3Nlc3NtZW50XHJcbiAqL1xyXG5cclxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCwgdXNlQ2FsbGJhY2sgfSBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCAqIGFzIG9ydCBmcm9tICdvbm54cnVudGltZS13ZWInO1xyXG5pbXBvcnQgeyBDYWxjdWxhdGlvblJlc3VsdCB9IGZyb20gJy4uLy4uL3R5cGVzL2Fpci1kdWN0LXNpemVyJztcclxuXHJcbi8vIE1vY2sgdHlwZXMgZm9yIEFJIG9wdGltaXphdGlvblxyXG5leHBvcnQgaW50ZXJmYWNlIEhWQUNTeXN0ZW0ge1xyXG4gIGlkOiBzdHJpbmc7XHJcbiAgdHlwZTogc3RyaW5nO1xyXG4gIGVmZmljaWVuY3k/OiBudW1iZXI7XHJcbiAgY2FwYWNpdHk/OiBudW1iZXI7XHJcbiAgYWdlPzogbnVtYmVyO1xyXG4gIG1haW50ZW5hbmNlX3NjaGVkdWxlPzogc3RyaW5nW107XHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgT3B0aW1pemF0aW9uUmVjb21tZW5kYXRpb24ge1xyXG4gIGlkOiBzdHJpbmc7XHJcbiAgdHlwZTogJ2VxdWlwbWVudF91cGdyYWRlJyB8ICdzY2hlZHVsZV9vcHRpbWl6YXRpb24nIHwgJ21haW50ZW5hbmNlJyB8ICdjb250cm9sX3N0cmF0ZWd5JztcclxuICBkZXNjcmlwdGlvbjogc3RyaW5nO1xyXG4gIHByaW9yaXR5OiAnbG93JyB8ICdtZWRpdW0nIHwgJ2hpZ2gnO1xyXG4gIGltcGxlbWVudGF0aW9uX2Nvc3Q6IG51bWJlcjtcclxuICBhbm51YWxfc2F2aW5nczogbnVtYmVyO1xyXG4gIHBheWJhY2tfcGVyaW9kOiBudW1iZXI7XHJcbiAgY29uZmlkZW5jZTogbnVtYmVyO1xyXG4gIGltcGFjdF9hcmVhczogc3RyaW5nW107XHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgQUlPcHRpbWl6YXRpb25Db25maWcge1xyXG4gIG1vZGVsUGF0aDogc3RyaW5nO1xyXG4gIGVuYWJsZUdQVT86IGJvb2xlYW47XHJcbiAgYmF0Y2hTaXplPzogbnVtYmVyO1xyXG4gIGNvbmZpZGVuY2VUaHJlc2hvbGQ/OiBudW1iZXI7XHJcbiAgb3B0aW1pemF0aW9uR29hbHM/OiBPcHRpbWl6YXRpb25Hb2FsW107XHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgT3B0aW1pemF0aW9uR29hbCB7XHJcbiAgdHlwZTogJ2VuZXJneV9lZmZpY2llbmN5JyB8ICdjb3N0X3JlZHVjdGlvbicgfCAnY29tZm9ydCcgfCAnZW52aXJvbm1lbnRhbCc7XHJcbiAgd2VpZ2h0OiBudW1iZXI7IC8vIDAtMVxyXG4gIHRhcmdldD86IG51bWJlcjtcclxufVxyXG5cclxuZXhwb3J0IGludGVyZmFjZSBPcHRpbWl6YXRpb25JbnB1dCB7XHJcbiAgaHZhY1N5c3RlbTogSFZBQ1N5c3RlbTtcclxuICBidWlsZGluZ0RhdGE6IEJ1aWxkaW5nRGF0YTtcclxuICBlbnZpcm9ubWVudGFsRGF0YTogRW52aXJvbm1lbnRhbERhdGE7XHJcbiAgb3BlcmF0aW9uYWxEYXRhOiBPcGVyYXRpb25hbERhdGE7XHJcbiAgY29uc3RyYWludHM6IE9wdGltaXphdGlvbkNvbnN0cmFpbnRzO1xyXG59XHJcblxyXG5leHBvcnQgaW50ZXJmYWNlIEJ1aWxkaW5nRGF0YSB7XHJcbiAgYXJlYTogbnVtYmVyO1xyXG4gIHZvbHVtZTogbnVtYmVyO1xyXG4gIG9jY3VwYW5jeTogbnVtYmVyO1xyXG4gIGluc3VsYXRpb246IG51bWJlcjsgLy8gUi12YWx1ZVxyXG4gIHdpbmRvd3M6IFdpbmRvd0RhdGFbXTtcclxuICBvcmllbnRhdGlvbjogc3RyaW5nO1xyXG4gIGZsb29yczogbnVtYmVyO1xyXG4gIHpvbmVDb3VudDogbnVtYmVyO1xyXG59XHJcblxyXG5leHBvcnQgaW50ZXJmYWNlIFdpbmRvd0RhdGEge1xyXG4gIGFyZWE6IG51bWJlcjtcclxuICB1VmFsdWU6IG51bWJlcjtcclxuICBvcmllbnRhdGlvbjogc3RyaW5nO1xyXG4gIHNoYWRpbmc6IG51bWJlcjtcclxufVxyXG5cclxuZXhwb3J0IGludGVyZmFjZSBFbnZpcm9ubWVudGFsRGF0YSB7XHJcbiAgb3V0ZG9vclRlbXBlcmF0dXJlOiBudW1iZXJbXTtcclxuICBodW1pZGl0eTogbnVtYmVyW107XHJcbiAgc29sYXJSYWRpYXRpb246IG51bWJlcltdO1xyXG4gIHdpbmRTcGVlZDogbnVtYmVyW107XHJcbiAgc2Vhc29uOiAnc3ByaW5nJyB8ICdzdW1tZXInIHwgJ2ZhbGwnIHwgJ3dpbnRlcic7XHJcbiAgY2xpbWF0ZTogc3RyaW5nO1xyXG59XHJcblxyXG5leHBvcnQgaW50ZXJmYWNlIE9wZXJhdGlvbmFsRGF0YSB7XHJcbiAgY3VycmVudExvYWQ6IG51bWJlcjtcclxuICBlbmVyZ3lDb25zdW1wdGlvbjogbnVtYmVyW107XHJcbiAgb3BlcmF0aW5nSG91cnM6IG51bWJlcjtcclxuICBtYWludGVuYW5jZUhpc3Rvcnk6IE1haW50ZW5hbmNlUmVjb3JkW107XHJcbiAgcGVyZm9ybWFuY2VNZXRyaWNzOiBQZXJmb3JtYW5jZU1ldHJpY3M7XHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgTWFpbnRlbmFuY2VSZWNvcmQge1xyXG4gIGRhdGU6IERhdGU7XHJcbiAgdHlwZTogc3RyaW5nO1xyXG4gIGNvc3Q6IG51bWJlcjtcclxuICBlZmZpY2llbmN5X2ltcGFjdDogbnVtYmVyO1xyXG59XHJcblxyXG5leHBvcnQgaW50ZXJmYWNlIFBlcmZvcm1hbmNlTWV0cmljcyB7XHJcbiAgY29wOiBudW1iZXI7IC8vIENvZWZmaWNpZW50IG9mIFBlcmZvcm1hbmNlXHJcbiAgZWVyOiBudW1iZXI7IC8vIEVuZXJneSBFZmZpY2llbmN5IFJhdGlvXHJcbiAgY2FwYWNpdHlfdXRpbGl6YXRpb246IG51bWJlcjtcclxuICB0ZW1wZXJhdHVyZV92YXJpYW5jZTogbnVtYmVyO1xyXG59XHJcblxyXG5leHBvcnQgaW50ZXJmYWNlIE9wdGltaXphdGlvbkNvbnN0cmFpbnRzIHtcclxuICBidWRnZXQ6IG51bWJlcjtcclxuICB0aW1lbGluZTogbnVtYmVyOyAvLyBkYXlzXHJcbiAgY29tZm9ydF9yZXF1aXJlbWVudHM6IENvbWZvcnRSZXF1aXJlbWVudHM7XHJcbiAgcmVndWxhdG9yeV9yZXF1aXJlbWVudHM6IHN0cmluZ1tdO1xyXG4gIGV4aXN0aW5nX2VxdWlwbWVudDogc3RyaW5nW107XHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgQ29tZm9ydFJlcXVpcmVtZW50cyB7XHJcbiAgdGVtcGVyYXR1cmVfcmFuZ2U6IFtudW1iZXIsIG51bWJlcl07XHJcbiAgaHVtaWRpdHlfcmFuZ2U6IFtudW1iZXIsIG51bWJlcl07XHJcbiAgYWlyX3F1YWxpdHlfbWluOiBudW1iZXI7XHJcbiAgbm9pc2VfbWF4OiBudW1iZXI7XHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgT3B0aW1pemF0aW9uUmVzdWx0IHtcclxuICByZWNvbW1lbmRhdGlvbnM6IE9wdGltaXphdGlvblJlY29tbWVuZGF0aW9uW107XHJcbiAgcHJlZGljdGVkX3NhdmluZ3M6IHtcclxuICAgIGVuZXJneTogbnVtYmVyOyAvLyBwZXJjZW50YWdlXHJcbiAgICBjb3N0OiBudW1iZXI7IC8vIGFubnVhbCBkb2xsYXJzXHJcbiAgICBlbWlzc2lvbnM6IG51bWJlcjsgLy8ga2cgQ08yIGVxdWl2YWxlbnRcclxuICB9O1xyXG4gIGNvbmZpZGVuY2Vfc2NvcmU6IG51bWJlcjtcclxuICBpbXBsZW1lbnRhdGlvbl9wbGFuOiBJbXBsZW1lbnRhdGlvblN0ZXBbXTtcclxuICByb2lfYW5hbHlzaXM6IFJPSUFuYWx5c2lzO1xyXG4gIHJpc2tfYXNzZXNzbWVudDogUmlza0ZhY3RvcltdO1xyXG59XHJcblxyXG5leHBvcnQgaW50ZXJmYWNlIEltcGxlbWVudGF0aW9uU3RlcCB7XHJcbiAgaWQ6IHN0cmluZztcclxuICBkZXNjcmlwdGlvbjogc3RyaW5nO1xyXG4gIHByaW9yaXR5OiAnaGlnaCcgfCAnbWVkaXVtJyB8ICdsb3cnO1xyXG4gIGVzdGltYXRlZF9jb3N0OiBudW1iZXI7XHJcbiAgZXN0aW1hdGVkX2R1cmF0aW9uOiBudW1iZXI7IC8vIGRheXNcclxuICBkZXBlbmRlbmNpZXM6IHN0cmluZ1tdO1xyXG4gIGV4cGVjdGVkX2ltcGFjdDogbnVtYmVyOyAvLyBwZXJjZW50YWdlIGltcHJvdmVtZW50XHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgUk9JQW5hbHlzaXMge1xyXG4gIGluaXRpYWxfaW52ZXN0bWVudDogbnVtYmVyO1xyXG4gIGFubnVhbF9zYXZpbmdzOiBudW1iZXI7XHJcbiAgcGF5YmFja19wZXJpb2Q6IG51bWJlcjsgLy8geWVhcnNcclxuICBuZXRfcHJlc2VudF92YWx1ZTogbnVtYmVyO1xyXG4gIGludGVybmFsX3JhdGVfb2ZfcmV0dXJuOiBudW1iZXI7XHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgUmlza0ZhY3RvciB7XHJcbiAgdHlwZTogc3RyaW5nO1xyXG4gIGRlc2NyaXB0aW9uOiBzdHJpbmc7XHJcbiAgcHJvYmFiaWxpdHk6IG51bWJlcjsgLy8gMC0xXHJcbiAgaW1wYWN0OiBudW1iZXI7IC8vIDAtMVxyXG4gIG1pdGlnYXRpb246IHN0cmluZztcclxufVxyXG5cclxuZXhwb3J0IGNsYXNzIEFJT3B0aW1pemF0aW9uU2VydmljZSB7XHJcbiAgcHJpdmF0ZSBzZXNzaW9uOiBvcnQuSW5mZXJlbmNlU2Vzc2lvbiB8IG51bGwgPSBudWxsO1xyXG4gIHByaXZhdGUgY29uZmlnOiBBSU9wdGltaXphdGlvbkNvbmZpZztcclxuICBwcml2YXRlIG1vZGVsQ2FjaGU6IE1hcDxzdHJpbmcsIG9ydC5JbmZlcmVuY2VTZXNzaW9uPiA9IG5ldyBNYXAoKTtcclxuICBwcml2YXRlIGlzSW5pdGlhbGl6ZWQgPSBmYWxzZTtcclxuXHJcbiAgY29uc3RydWN0b3IoY29uZmlnOiBBSU9wdGltaXphdGlvbkNvbmZpZykge1xyXG4gICAgdGhpcy5jb25maWcgPSB7XHJcbiAgICAgIGVuYWJsZUdQVTogZmFsc2UsXHJcbiAgICAgIGJhdGNoU2l6ZTogMSxcclxuICAgICAgY29uZmlkZW5jZVRocmVzaG9sZDogMC43LFxyXG4gICAgICBvcHRpbWl6YXRpb25Hb2FsczogW1xyXG4gICAgICAgIHsgdHlwZTogJ2VuZXJneV9lZmZpY2llbmN5Jywgd2VpZ2h0OiAwLjQgfSxcclxuICAgICAgICB7IHR5cGU6ICdjb3N0X3JlZHVjdGlvbicsIHdlaWdodDogMC4zIH0sXHJcbiAgICAgICAgeyB0eXBlOiAnY29tZm9ydCcsIHdlaWdodDogMC4yIH0sXHJcbiAgICAgICAgeyB0eXBlOiAnZW52aXJvbm1lbnRhbCcsIHdlaWdodDogMC4xIH1cclxuICAgICAgXSxcclxuICAgICAgLi4uY29uZmlnXHJcbiAgICB9O1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogSW5pdGlhbGl6ZSB0aGUgQUkgb3B0aW1pemF0aW9uIHNlcnZpY2VcclxuICAgKi9cclxuICBhc3luYyBpbml0aWFsaXplKCk6IFByb21pc2U8dm9pZD4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgLy8gQ29uZmlndXJlIE9OTlggcnVudGltZVxyXG4gICAgICBpZiAodGhpcy5jb25maWcuZW5hYmxlR1BVKSB7XHJcbiAgICAgICAgb3J0LmVudi53YXNtLm51bVRocmVhZHMgPSA0O1xyXG4gICAgICAgIG9ydC5lbnYud2FzbS5zaW1kID0gdHJ1ZTtcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gTG9hZCBtYWluIG9wdGltaXphdGlvbiBtb2RlbFxyXG4gICAgICB0aGlzLnNlc3Npb24gPSBhd2FpdCBvcnQuSW5mZXJlbmNlU2Vzc2lvbi5jcmVhdGUodGhpcy5jb25maWcubW9kZWxQYXRoKTtcclxuICAgICAgXHJcbiAgICAgIC8vIExvYWQgc3BlY2lhbGl6ZWQgbW9kZWxzXHJcbiAgICAgIGF3YWl0IHRoaXMubG9hZFNwZWNpYWxpemVkTW9kZWxzKCk7XHJcbiAgICAgIFxyXG4gICAgICB0aGlzLmlzSW5pdGlhbGl6ZWQgPSB0cnVlO1xyXG4gICAgICBjb25zb2xlLmxvZygnQUkgT3B0aW1pemF0aW9uIFNlcnZpY2UgaW5pdGlhbGl6ZWQgc3VjY2Vzc2Z1bGx5Jyk7XHJcbiAgICAgIFxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGluaXRpYWxpemUgQUkgT3B0aW1pemF0aW9uIFNlcnZpY2U6JywgZXJyb3IpO1xyXG4gICAgICB0aHJvdyBlcnJvcjtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIE9wdGltaXplIEhWQUMgc3lzdGVtIGNvbmZpZ3VyYXRpb25cclxuICAgKi9cclxuICBhc3luYyBvcHRpbWl6ZVN5c3RlbShpbnB1dDogT3B0aW1pemF0aW9uSW5wdXQpOiBQcm9taXNlPE9wdGltaXphdGlvblJlc3VsdD4ge1xyXG4gICAgaWYgKCF0aGlzLmlzSW5pdGlhbGl6ZWQgfHwgIXRoaXMuc2Vzc2lvbikge1xyXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ0FJIE9wdGltaXphdGlvbiBTZXJ2aWNlIG5vdCBpbml0aWFsaXplZCcpO1xyXG4gICAgfVxyXG5cclxuICAgIHRyeSB7XHJcbiAgICAgIC8vIFByZXBhcmUgaW5wdXQgZGF0YVxyXG4gICAgICBjb25zdCBtb2RlbElucHV0ID0gYXdhaXQgdGhpcy5wcmVwYXJlTW9kZWxJbnB1dChpbnB1dCk7XHJcbiAgICAgIFxyXG4gICAgICAvLyBSdW4gaW5mZXJlbmNlXHJcbiAgICAgIGNvbnN0IHJlc3VsdHMgPSBhd2FpdCB0aGlzLnNlc3Npb24ucnVuKG1vZGVsSW5wdXQpO1xyXG4gICAgICBcclxuICAgICAgLy8gUHJvY2VzcyByZXN1bHRzXHJcbiAgICAgIGNvbnN0IG9wdGltaXphdGlvblJlc3VsdCA9IGF3YWl0IHRoaXMucHJvY2Vzc1Jlc3VsdHMocmVzdWx0cywgaW5wdXQpO1xyXG4gICAgICBcclxuICAgICAgLy8gVmFsaWRhdGUgcmVjb21tZW5kYXRpb25zXHJcbiAgICAgIGNvbnN0IHZhbGlkYXRlZFJlc3VsdCA9IGF3YWl0IHRoaXMudmFsaWRhdGVSZWNvbW1lbmRhdGlvbnMob3B0aW1pemF0aW9uUmVzdWx0LCBpbnB1dCk7XHJcbiAgICAgIFxyXG4gICAgICByZXR1cm4gdmFsaWRhdGVkUmVzdWx0O1xyXG4gICAgICBcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ09wdGltaXphdGlvbiBmYWlsZWQ6JywgZXJyb3IpO1xyXG4gICAgICB0aHJvdyBlcnJvcjtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIFByZWRpY3QgZW5lcmd5IGNvbnN1bXB0aW9uXHJcbiAgICovXHJcbiAgYXN5bmMgcHJlZGljdEVuZXJneUNvbnN1bXB0aW9uKFxyXG4gICAgaHZhY1N5c3RlbTogSFZBQ1N5c3RlbSxcclxuICAgIGVudmlyb25tZW50YWxEYXRhOiBFbnZpcm9ubWVudGFsRGF0YSxcclxuICAgIHRpbWVIb3Jpem9uOiBudW1iZXIgPSAyNCAvLyBob3Vyc1xyXG4gICk6IFByb21pc2U8bnVtYmVyW10+IHtcclxuICAgIGNvbnN0IGVuZXJneU1vZGVsID0gdGhpcy5tb2RlbENhY2hlLmdldCgnZW5lcmd5X3ByZWRpY3Rpb24nKTtcclxuICAgIGlmICghZW5lcmd5TW9kZWwpIHtcclxuICAgICAgdGhyb3cgbmV3IEVycm9yKCdFbmVyZ3kgcHJlZGljdGlvbiBtb2RlbCBub3QgbG9hZGVkJyk7XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgaW5wdXQgPSB0aGlzLnByZXBhcmVFbmVyZ3lJbnB1dChodmFjU3lzdGVtLCBlbnZpcm9ubWVudGFsRGF0YSwgdGltZUhvcml6b24pO1xyXG4gICAgY29uc3QgcmVzdWx0cyA9IGF3YWl0IGVuZXJneU1vZGVsLnJ1bihpbnB1dCk7XHJcbiAgICBcclxuICAgIHJldHVybiBBcnJheS5mcm9tKHJlc3VsdHMuZW5lcmd5X2NvbnN1bXB0aW9uLmRhdGEgYXMgRmxvYXQzMkFycmF5KTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIERldGVjdCBwZXJmb3JtYW5jZSBhbm9tYWxpZXNcclxuICAgKi9cclxuICBhc3luYyBkZXRlY3RBbm9tYWxpZXMoXHJcbiAgICBwZXJmb3JtYW5jZURhdGE6IFBlcmZvcm1hbmNlTWV0cmljc1tdLFxyXG4gICAgdGhyZXNob2xkOiBudW1iZXIgPSAwLjhcclxuICApOiBQcm9taXNlPHsgYW5vbWFsaWVzOiBudW1iZXJbXTsgY29uZmlkZW5jZTogbnVtYmVyW10gfT4ge1xyXG4gICAgY29uc3QgYW5vbWFseU1vZGVsID0gdGhpcy5tb2RlbENhY2hlLmdldCgnYW5vbWFseV9kZXRlY3Rpb24nKTtcclxuICAgIGlmICghYW5vbWFseU1vZGVsKSB7XHJcbiAgICAgIHRocm93IG5ldyBFcnJvcignQW5vbWFseSBkZXRlY3Rpb24gbW9kZWwgbm90IGxvYWRlZCcpO1xyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IGlucHV0ID0gdGhpcy5wcmVwYXJlQW5vbWFseUlucHV0KHBlcmZvcm1hbmNlRGF0YSk7XHJcbiAgICBjb25zdCByZXN1bHRzID0gYXdhaXQgYW5vbWFseU1vZGVsLnJ1bihpbnB1dCk7XHJcbiAgICBcclxuICAgIGNvbnN0IGFub21hbHlTY29yZXMgPSBBcnJheS5mcm9tKHJlc3VsdHMuYW5vbWFseV9zY29yZXMuZGF0YSBhcyBGbG9hdDMyQXJyYXkpO1xyXG4gICAgY29uc3QgY29uZmlkZW5jZVNjb3JlcyA9IEFycmF5LmZyb20ocmVzdWx0cy5jb25maWRlbmNlLmRhdGEgYXMgRmxvYXQzMkFycmF5KTtcclxuICAgIFxyXG4gICAgY29uc3QgYW5vbWFsaWVzID0gYW5vbWFseVNjb3Jlcy5tYXAoKHNjb3JlLCBpbmRleCkgPT4gXHJcbiAgICAgIHNjb3JlID4gdGhyZXNob2xkID8gaW5kZXggOiAtMVxyXG4gICAgKS5maWx0ZXIoaW5kZXggPT4gaW5kZXggIT09IC0xKTtcclxuICAgIFxyXG4gICAgcmV0dXJuIHtcclxuICAgICAgYW5vbWFsaWVzLFxyXG4gICAgICBjb25maWRlbmNlOiBhbm9tYWxpZXMubWFwKGluZGV4ID0+IGNvbmZpZGVuY2VTY29yZXNbaW5kZXhdKVxyXG4gICAgfTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEdlbmVyYXRlIGVxdWlwbWVudCBzaXppbmcgcmVjb21tZW5kYXRpb25zXHJcbiAgICovXHJcbiAgYXN5bmMgcmVjb21tZW5kRXF1aXBtZW50U2l6aW5nKFxyXG4gICAgYnVpbGRpbmdEYXRhOiBCdWlsZGluZ0RhdGEsXHJcbiAgICBsb2FkUmVxdWlyZW1lbnRzOiBudW1iZXJbXVxyXG4gICk6IFByb21pc2U8e1xyXG4gICAgZXF1aXBtZW50OiBzdHJpbmdbXTtcclxuICAgIHNpemVzOiBudW1iZXJbXTtcclxuICAgIGVmZmljaWVuY3lfcmF0aW5nczogbnVtYmVyW107XHJcbiAgICBjb3N0X2VzdGltYXRlczogbnVtYmVyW107XHJcbiAgfT4ge1xyXG4gICAgY29uc3Qgc2l6aW5nTW9kZWwgPSB0aGlzLm1vZGVsQ2FjaGUuZ2V0KCdlcXVpcG1lbnRfc2l6aW5nJyk7XHJcbiAgICBpZiAoIXNpemluZ01vZGVsKSB7XHJcbiAgICAgIHRocm93IG5ldyBFcnJvcignRXF1aXBtZW50IHNpemluZyBtb2RlbCBub3QgbG9hZGVkJyk7XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgaW5wdXQgPSB0aGlzLnByZXBhcmVTaXppbmdJbnB1dChidWlsZGluZ0RhdGEsIGxvYWRSZXF1aXJlbWVudHMpO1xyXG4gICAgY29uc3QgcmVzdWx0cyA9IGF3YWl0IHNpemluZ01vZGVsLnJ1bihpbnB1dCk7XHJcbiAgICBcclxuICAgIHJldHVybiB7XHJcbiAgICAgIGVxdWlwbWVudDogdGhpcy5kZWNvZGVFcXVpcG1lbnRUeXBlcyhyZXN1bHRzLmVxdWlwbWVudF90eXBlcy5kYXRhIGFzIEZsb2F0MzJBcnJheSksXHJcbiAgICAgIHNpemVzOiBBcnJheS5mcm9tKHJlc3VsdHMuZXF1aXBtZW50X3NpemVzLmRhdGEgYXMgRmxvYXQzMkFycmF5KSxcclxuICAgICAgZWZmaWNpZW5jeV9yYXRpbmdzOiBBcnJheS5mcm9tKHJlc3VsdHMuZWZmaWNpZW5jeV9yYXRpbmdzLmRhdGEgYXMgRmxvYXQzMkFycmF5KSxcclxuICAgICAgY29zdF9lc3RpbWF0ZXM6IEFycmF5LmZyb20ocmVzdWx0cy5jb3N0X2VzdGltYXRlcy5kYXRhIGFzIEZsb2F0MzJBcnJheSlcclxuICAgIH07XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBBbmFseXplIGVudmlyb25tZW50YWwgaW1wYWN0XHJcbiAgICovXHJcbiAgYXN5bmMgYW5hbHl6ZUVudmlyb25tZW50YWxJbXBhY3QoXHJcbiAgICBodmFjU3lzdGVtOiBIVkFDU3lzdGVtLFxyXG4gICAgZW5lcmd5Q29uc3VtcHRpb246IG51bWJlcltdXHJcbiAgKTogUHJvbWlzZTx7XHJcbiAgICBjYXJib25fZm9vdHByaW50OiBudW1iZXI7XHJcbiAgICByZW5ld2FibGVfcG90ZW50aWFsOiBudW1iZXI7XHJcbiAgICBzdXN0YWluYWJpbGl0eV9zY29yZTogbnVtYmVyO1xyXG4gICAgcmVjb21tZW5kYXRpb25zOiBzdHJpbmdbXTtcclxuICB9PiB7XHJcbiAgICAvLyBDYWxjdWxhdGUgY2FyYm9uIGZvb3RwcmludFxyXG4gICAgY29uc3QgY2FyYm9uSW50ZW5zaXR5ID0gMC40OyAvLyBrZyBDTzIva1doIChhdmVyYWdlIGdyaWQpXHJcbiAgICBjb25zdCB0b3RhbEVuZXJneSA9IGVuZXJneUNvbnN1bXB0aW9uLnJlZHVjZSgoc3VtLCBjb25zdW1wdGlvbikgPT4gc3VtICsgY29uc3VtcHRpb24sIDApO1xyXG4gICAgY29uc3QgY2FyYm9uRm9vdHByaW50ID0gdG90YWxFbmVyZ3kgKiBjYXJib25JbnRlbnNpdHk7XHJcbiAgICBcclxuICAgIC8vIEFzc2VzcyByZW5ld2FibGUgcG90ZW50aWFsXHJcbiAgICBjb25zdCByZW5ld2FibGVQb3RlbnRpYWwgPSBhd2FpdCB0aGlzLmFzc2Vzc1JlbmV3YWJsZVBvdGVudGlhbChodmFjU3lzdGVtKTtcclxuICAgIFxyXG4gICAgLy8gQ2FsY3VsYXRlIHN1c3RhaW5hYmlsaXR5IHNjb3JlXHJcbiAgICBjb25zdCBzdXN0YWluYWJpbGl0eVNjb3JlID0gdGhpcy5jYWxjdWxhdGVTdXN0YWluYWJpbGl0eVNjb3JlKFxyXG4gICAgICBodmFjU3lzdGVtLFxyXG4gICAgICBjYXJib25Gb290cHJpbnQsXHJcbiAgICAgIHJlbmV3YWJsZVBvdGVudGlhbFxyXG4gICAgKTtcclxuICAgIFxyXG4gICAgLy8gR2VuZXJhdGUgcmVjb21tZW5kYXRpb25zXHJcbiAgICBjb25zdCByZWNvbW1lbmRhdGlvbnMgPSB0aGlzLmdlbmVyYXRlRW52aXJvbm1lbnRhbFJlY29tbWVuZGF0aW9ucyhcclxuICAgICAgc3VzdGFpbmFiaWxpdHlTY29yZSxcclxuICAgICAgcmVuZXdhYmxlUG90ZW50aWFsXHJcbiAgICApO1xyXG4gICAgXHJcbiAgICByZXR1cm4ge1xyXG4gICAgICBjYXJib25fZm9vdHByaW50OiBjYXJib25Gb290cHJpbnQsXHJcbiAgICAgIHJlbmV3YWJsZV9wb3RlbnRpYWw6IHJlbmV3YWJsZVBvdGVudGlhbCxcclxuICAgICAgc3VzdGFpbmFiaWxpdHlfc2NvcmU6IHN1c3RhaW5hYmlsaXR5U2NvcmUsXHJcbiAgICAgIHJlY29tbWVuZGF0aW9uc1xyXG4gICAgfTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEdldCBvcHRpbWl6YXRpb24gaW5zaWdodHNcclxuICAgKi9cclxuICBnZXRPcHRpbWl6YXRpb25JbnNpZ2h0cyhyZXN1bHQ6IE9wdGltaXphdGlvblJlc3VsdCk6IHtcclxuICAgIGtleV9pbnNpZ2h0czogc3RyaW5nW107XHJcbiAgICBwcmlvcml0eV9hY3Rpb25zOiBzdHJpbmdbXTtcclxuICAgIHBvdGVudGlhbF9pc3N1ZXM6IHN0cmluZ1tdO1xyXG4gIH0ge1xyXG4gICAgY29uc3QgaW5zaWdodHM6IHN0cmluZ1tdID0gW107XHJcbiAgICBjb25zdCBwcmlvcml0eUFjdGlvbnM6IHN0cmluZ1tdID0gW107XHJcbiAgICBjb25zdCBwb3RlbnRpYWxJc3N1ZXM6IHN0cmluZ1tdID0gW107XHJcbiAgICBcclxuICAgIC8vIEFuYWx5emUgc2F2aW5ncyBwb3RlbnRpYWxcclxuICAgIGlmIChyZXN1bHQucHJlZGljdGVkX3NhdmluZ3MuZW5lcmd5ID4gMjApIHtcclxuICAgICAgaW5zaWdodHMucHVzaChgU2lnbmlmaWNhbnQgZW5lcmd5IHNhdmluZ3MgcG90ZW50aWFsOiAke3Jlc3VsdC5wcmVkaWN0ZWRfc2F2aW5ncy5lbmVyZ3kudG9GaXhlZCgxKX0lYCk7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIGlmIChyZXN1bHQucHJlZGljdGVkX3NhdmluZ3MuY29zdCA+IDUwMDApIHtcclxuICAgICAgaW5zaWdodHMucHVzaChgQW5udWFsIGNvc3Qgc2F2aW5nczogJCR7cmVzdWx0LnByZWRpY3RlZF9zYXZpbmdzLmNvc3QudG9Mb2NhbGVTdHJpbmcoKX1gKTtcclxuICAgIH1cclxuICAgIFxyXG4gICAgLy8gSWRlbnRpZnkgcHJpb3JpdHkgYWN0aW9uc1xyXG4gICAgY29uc3QgaGlnaFByaW9yaXR5U3RlcHMgPSByZXN1bHQuaW1wbGVtZW50YXRpb25fcGxhblxyXG4gICAgICAuZmlsdGVyKHN0ZXAgPT4gc3RlcC5wcmlvcml0eSA9PT0gJ2hpZ2gnKVxyXG4gICAgICAuc29ydCgoYSwgYikgPT4gYi5leHBlY3RlZF9pbXBhY3QgLSBhLmV4cGVjdGVkX2ltcGFjdCk7XHJcbiAgICBcclxuICAgIHByaW9yaXR5QWN0aW9ucy5wdXNoKC4uLmhpZ2hQcmlvcml0eVN0ZXBzLnNsaWNlKDAsIDMpLm1hcChzdGVwID0+IHN0ZXAuZGVzY3JpcHRpb24pKTtcclxuICAgIFxyXG4gICAgLy8gSWRlbnRpZnkgcG90ZW50aWFsIGlzc3Vlc1xyXG4gICAgY29uc3QgaGlnaFJpc2tGYWN0b3JzID0gcmVzdWx0LnJpc2tfYXNzZXNzbWVudFxyXG4gICAgICAuZmlsdGVyKHJpc2sgPT4gcmlzay5wcm9iYWJpbGl0eSAqIHJpc2suaW1wYWN0ID4gMC41KVxyXG4gICAgICAuc29ydCgoYSwgYikgPT4gKGIucHJvYmFiaWxpdHkgKiBiLmltcGFjdCkgLSAoYS5wcm9iYWJpbGl0eSAqIGEuaW1wYWN0KSk7XHJcbiAgICBcclxuICAgIHBvdGVudGlhbElzc3Vlcy5wdXNoKC4uLmhpZ2hSaXNrRmFjdG9ycy5zbGljZSgwLCAzKS5tYXAocmlzayA9PiByaXNrLmRlc2NyaXB0aW9uKSk7XHJcbiAgICBcclxuICAgIHJldHVybiB7XHJcbiAgICAgIGtleV9pbnNpZ2h0czogaW5zaWdodHMsXHJcbiAgICAgIHByaW9yaXR5X2FjdGlvbnM6IHByaW9yaXR5QWN0aW9ucyxcclxuICAgICAgcG90ZW50aWFsX2lzc3VlczogcG90ZW50aWFsSXNzdWVzXHJcbiAgICB9O1xyXG4gIH1cclxuXHJcbiAgcHJpdmF0ZSBhc3luYyBsb2FkU3BlY2lhbGl6ZWRNb2RlbHMoKTogUHJvbWlzZTx2b2lkPiB7XHJcbiAgICBjb25zdCBtb2RlbHMgPSBbXHJcbiAgICAgIHsgbmFtZTogJ2VuZXJneV9wcmVkaWN0aW9uJywgcGF0aDogJy9tb2RlbHMvZW5lcmd5X3ByZWRpY3Rpb24ub25ueCcgfSxcclxuICAgICAgeyBuYW1lOiAnYW5vbWFseV9kZXRlY3Rpb24nLCBwYXRoOiAnL21vZGVscy9hbm9tYWx5X2RldGVjdGlvbi5vbm54JyB9LFxyXG4gICAgICB7IG5hbWU6ICdlcXVpcG1lbnRfc2l6aW5nJywgcGF0aDogJy9tb2RlbHMvZXF1aXBtZW50X3NpemluZy5vbm54JyB9LFxyXG4gICAgICB7IG5hbWU6ICdsb2FkX2ZvcmVjYXN0aW5nJywgcGF0aDogJy9tb2RlbHMvbG9hZF9mb3JlY2FzdGluZy5vbm54JyB9XHJcbiAgICBdO1xyXG5cclxuICAgIGZvciAoY29uc3QgbW9kZWwgb2YgbW9kZWxzKSB7XHJcbiAgICAgIHRyeSB7XHJcbiAgICAgICAgY29uc3Qgc2Vzc2lvbiA9IGF3YWl0IG9ydC5JbmZlcmVuY2VTZXNzaW9uLmNyZWF0ZShtb2RlbC5wYXRoKTtcclxuICAgICAgICB0aGlzLm1vZGVsQ2FjaGUuc2V0KG1vZGVsLm5hbWUsIHNlc3Npb24pO1xyXG4gICAgICAgIGNvbnNvbGUubG9nKGBMb2FkZWQgJHttb2RlbC5uYW1lfSBtb2RlbGApO1xyXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgIGNvbnNvbGUud2FybihgRmFpbGVkIHRvIGxvYWQgJHttb2RlbC5uYW1lfSBtb2RlbDpgLCBlcnJvcik7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcblxyXG4gIHByaXZhdGUgYXN5bmMgcHJlcGFyZU1vZGVsSW5wdXQoaW5wdXQ6IE9wdGltaXphdGlvbklucHV0KTogUHJvbWlzZTxSZWNvcmQ8c3RyaW5nLCBvcnQuVGVuc29yPj4ge1xyXG4gICAgLy8gTm9ybWFsaXplIGFuZCBwcmVwYXJlIGlucHV0IGRhdGEgZm9yIHRoZSBtb2RlbFxyXG4gICAgY29uc3QgZmVhdHVyZXMgPSBbXHJcbiAgICAgIC8vIEJ1aWxkaW5nIGZlYXR1cmVzXHJcbiAgICAgIGlucHV0LmJ1aWxkaW5nRGF0YS5hcmVhIC8gMTAwMDAsIC8vIE5vcm1hbGl6ZSBhcmVhXHJcbiAgICAgIGlucHV0LmJ1aWxkaW5nRGF0YS52b2x1bWUgLyAxMDAwMDAsIC8vIE5vcm1hbGl6ZSB2b2x1bWVcclxuICAgICAgaW5wdXQuYnVpbGRpbmdEYXRhLm9jY3VwYW5jeSAvIDEwMDAsIC8vIE5vcm1hbGl6ZSBvY2N1cGFuY3lcclxuICAgICAgaW5wdXQuYnVpbGRpbmdEYXRhLmluc3VsYXRpb24gLyA1MCwgLy8gTm9ybWFsaXplIFItdmFsdWVcclxuICAgICAgaW5wdXQuYnVpbGRpbmdEYXRhLmZsb29ycyAvIDUwLCAvLyBOb3JtYWxpemUgZmxvb3JzXHJcbiAgICAgIGlucHV0LmJ1aWxkaW5nRGF0YS56b25lQ291bnQgLyAyMCwgLy8gTm9ybWFsaXplIHpvbmVzXHJcbiAgICAgIFxyXG4gICAgICAvLyBFbnZpcm9ubWVudGFsIGZlYXR1cmVzXHJcbiAgICAgIC4uLmlucHV0LmVudmlyb25tZW50YWxEYXRhLm91dGRvb3JUZW1wZXJhdHVyZS5zbGljZSgwLCAyNCkubWFwKHQgPT4gdCAvIDEwMCksXHJcbiAgICAgIC4uLmlucHV0LmVudmlyb25tZW50YWxEYXRhLmh1bWlkaXR5LnNsaWNlKDAsIDI0KS5tYXAoaCA9PiBoIC8gMTAwKSxcclxuICAgICAgXHJcbiAgICAgIC8vIE9wZXJhdGlvbmFsIGZlYXR1cmVzXHJcbiAgICAgIGlucHV0Lm9wZXJhdGlvbmFsRGF0YS5jdXJyZW50TG9hZCAvIDEwMDAsIC8vIE5vcm1hbGl6ZSBsb2FkXHJcbiAgICAgIGlucHV0Lm9wZXJhdGlvbmFsRGF0YS5wZXJmb3JtYW5jZU1ldHJpY3MuY29wIC8gMTAsXHJcbiAgICAgIGlucHV0Lm9wZXJhdGlvbmFsRGF0YS5wZXJmb3JtYW5jZU1ldHJpY3MuZWVyIC8gMzAsXHJcbiAgICAgIGlucHV0Lm9wZXJhdGlvbmFsRGF0YS5wZXJmb3JtYW5jZU1ldHJpY3MuY2FwYWNpdHlfdXRpbGl6YXRpb24sXHJcbiAgICAgIFxyXG4gICAgICAvLyBDb25zdHJhaW50IGZlYXR1cmVzXHJcbiAgICAgIGlucHV0LmNvbnN0cmFpbnRzLmJ1ZGdldCAvIDEwMDAwMCwgLy8gTm9ybWFsaXplIGJ1ZGdldFxyXG4gICAgICBpbnB1dC5jb25zdHJhaW50cy50aW1lbGluZSAvIDM2NSwgLy8gTm9ybWFsaXplIHRpbWVsaW5lXHJcbiAgICBdO1xyXG5cclxuICAgIC8vIFBhZCBvciB0cnVuY2F0ZSB0byBleHBlY3RlZCBpbnB1dCBzaXplXHJcbiAgICBjb25zdCBpbnB1dFNpemUgPSAxMDA7IC8vIEV4cGVjdGVkIG1vZGVsIGlucHV0IHNpemVcclxuICAgIGNvbnN0IHBhZGRlZEZlYXR1cmVzID0gZmVhdHVyZXMuc2xpY2UoMCwgaW5wdXRTaXplKTtcclxuICAgIHdoaWxlIChwYWRkZWRGZWF0dXJlcy5sZW5ndGggPCBpbnB1dFNpemUpIHtcclxuICAgICAgcGFkZGVkRmVhdHVyZXMucHVzaCgwKTtcclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4ge1xyXG4gICAgICBpbnB1dDogbmV3IG9ydC5UZW5zb3IoJ2Zsb2F0MzInLCBuZXcgRmxvYXQzMkFycmF5KHBhZGRlZEZlYXR1cmVzKSwgWzEsIGlucHV0U2l6ZV0pXHJcbiAgICB9O1xyXG4gIH1cclxuXHJcbiAgcHJpdmF0ZSBhc3luYyBwcm9jZXNzUmVzdWx0cyhyZXN1bHRzOiBvcnQuSW5mZXJlbmNlU2Vzc2lvbi5Pbm54VmFsdWVNYXBUeXBlLCBpbnB1dDogT3B0aW1pemF0aW9uSW5wdXQpOiBQcm9taXNlPE9wdGltaXphdGlvblJlc3VsdD4ge1xyXG4gICAgLy8gUHJvY2VzcyBtb2RlbCBvdXRwdXRzIGludG8gc3RydWN0dXJlZCByZWNvbW1lbmRhdGlvbnNcclxuICAgIGNvbnN0IG91dHB1dERhdGEgPSByZXN1bHRzLm91dHB1dC5kYXRhIGFzIEZsb2F0MzJBcnJheTtcclxuICAgIFxyXG4gICAgLy8gRXh0cmFjdCBkaWZmZXJlbnQgdHlwZXMgb2YgcmVjb21tZW5kYXRpb25zXHJcbiAgICBjb25zdCByZWNvbW1lbmRhdGlvbnMgPSB0aGlzLmV4dHJhY3RSZWNvbW1lbmRhdGlvbnMob3V0cHV0RGF0YSwgaW5wdXQpO1xyXG4gICAgY29uc3Qgc2F2aW5ncyA9IHRoaXMuY2FsY3VsYXRlU2F2aW5ncyhvdXRwdXREYXRhLCBpbnB1dCk7XHJcbiAgICBjb25zdCBpbXBsZW1lbnRhdGlvblBsYW4gPSB0aGlzLmdlbmVyYXRlSW1wbGVtZW50YXRpb25QbGFuKHJlY29tbWVuZGF0aW9ucywgaW5wdXQpO1xyXG4gICAgY29uc3Qgcm9pQW5hbHlzaXMgPSB0aGlzLmNhbGN1bGF0ZVJPSShzYXZpbmdzLCBpbXBsZW1lbnRhdGlvblBsYW4pO1xyXG4gICAgY29uc3Qgcmlza0Fzc2Vzc21lbnQgPSB0aGlzLmFzc2Vzc1Jpc2tzKHJlY29tbWVuZGF0aW9ucywgaW5wdXQpO1xyXG4gICAgXHJcbiAgICByZXR1cm4ge1xyXG4gICAgICByZWNvbW1lbmRhdGlvbnMsXHJcbiAgICAgIHByZWRpY3RlZF9zYXZpbmdzOiBzYXZpbmdzLFxyXG4gICAgICBjb25maWRlbmNlX3Njb3JlOiBNYXRoLm1pbihvdXRwdXREYXRhWzBdLCAxLjApLFxyXG4gICAgICBpbXBsZW1lbnRhdGlvbl9wbGFuOiBpbXBsZW1lbnRhdGlvblBsYW4sXHJcbiAgICAgIHJvaV9hbmFseXNpczogcm9pQW5hbHlzaXMsXHJcbiAgICAgIHJpc2tfYXNzZXNzbWVudDogcmlza0Fzc2Vzc21lbnRcclxuICAgIH07XHJcbiAgfVxyXG5cclxuICBwcml2YXRlIGV4dHJhY3RSZWNvbW1lbmRhdGlvbnMob3V0cHV0RGF0YTogRmxvYXQzMkFycmF5LCBpbnB1dDogT3B0aW1pemF0aW9uSW5wdXQpOiBPcHRpbWl6YXRpb25SZWNvbW1lbmRhdGlvbltdIHtcclxuICAgIC8vIEV4dHJhY3QgYW5kIGRlY29kZSByZWNvbW1lbmRhdGlvbnMgZnJvbSBtb2RlbCBvdXRwdXRcclxuICAgIGNvbnN0IHJlY29tbWVuZGF0aW9uczogT3B0aW1pemF0aW9uUmVjb21tZW5kYXRpb25bXSA9IFtdO1xyXG4gICAgXHJcbiAgICAvLyBUaGlzIGlzIGEgc2ltcGxpZmllZCBleGFtcGxlIC0gcmVhbCBpbXBsZW1lbnRhdGlvbiB3b3VsZCBkZWNvZGUgbW9kZWwgb3V0cHV0c1xyXG4gICAgaWYgKG91dHB1dERhdGFbMV0gPiAwLjUpIHtcclxuICAgICAgcmVjb21tZW5kYXRpb25zLnB1c2goe1xyXG4gICAgICAgIGlkOiAndXBncmFkZV9odmFjJyxcclxuICAgICAgICB0eXBlOiAnZXF1aXBtZW50X3VwZ3JhZGUnLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnVXBncmFkZSBIVkFDIEVxdWlwbWVudCAtIFJlcGxhY2UgYWdpbmcgZXF1aXBtZW50IHdpdGggaGlnaC1lZmZpY2llbmN5IHVuaXRzJyxcclxuICAgICAgICBwcmlvcml0eTogJ2hpZ2gnLFxyXG4gICAgICAgIGltcGxlbWVudGF0aW9uX2Nvc3Q6IG91dHB1dERhdGFbM10gKiAxMDAwMCxcclxuICAgICAgICBhbm51YWxfc2F2aW5nczogb3V0cHV0RGF0YVsyXSAqIDEwMCxcclxuICAgICAgICBwYXliYWNrX3BlcmlvZDogb3V0cHV0RGF0YVs0XSAqIDEwLFxyXG4gICAgICAgIGNvbmZpZGVuY2U6IDAuODUsXHJcbiAgICAgICAgaW1wYWN0X2FyZWFzOiBbJ2VuZXJneV9lZmZpY2llbmN5JywgJ2Nvc3RfcmVkdWN0aW9uJ11cclxuICAgICAgfSk7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIGlmIChvdXRwdXREYXRhWzVdID4gMC41KSB7XHJcbiAgICAgIHJlY29tbWVuZGF0aW9ucy5wdXNoKHtcclxuICAgICAgICBpZDogJ29wdGltaXplX2NvbnRyb2xzJyxcclxuICAgICAgICB0eXBlOiAnY29udHJvbF9zdHJhdGVneScsXHJcbiAgICAgICAgZGVzY3JpcHRpb246ICdPcHRpbWl6ZSBDb250cm9sIFN5c3RlbXMgLSBJbXBsZW1lbnQgc21hcnQgY29udHJvbHMgYW5kIHNjaGVkdWxpbmcnLFxyXG4gICAgICAgIHByaW9yaXR5OiAnbWVkaXVtJyxcclxuICAgICAgICBpbXBsZW1lbnRhdGlvbl9jb3N0OiBvdXRwdXREYXRhWzddICogNTAwMCxcclxuICAgICAgICBhbm51YWxfc2F2aW5nczogb3V0cHV0RGF0YVs2XSAqIDEwMCxcclxuICAgICAgICBwYXliYWNrX3BlcmlvZDogb3V0cHV0RGF0YVs4XSAqIDUsXHJcbiAgICAgICAgY29uZmlkZW5jZTogMC43NSxcclxuICAgICAgICBpbXBhY3RfYXJlYXM6IFsnZW5lcmd5X2VmZmljaWVuY3knLCAnY29tZm9ydCddXHJcbiAgICAgIH0pO1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICByZXR1cm4gcmVjb21tZW5kYXRpb25zO1xyXG4gIH1cclxuXHJcbiAgcHJpdmF0ZSBjYWxjdWxhdGVTYXZpbmdzKG91dHB1dERhdGE6IEZsb2F0MzJBcnJheSwgaW5wdXQ6IE9wdGltaXphdGlvbklucHV0KSB7XHJcbiAgICBjb25zdCBjdXJyZW50RW5lcmd5Q29zdCA9IGlucHV0Lm9wZXJhdGlvbmFsRGF0YS5lbmVyZ3lDb25zdW1wdGlvbi5yZWR1Y2UoKHN1bSwgZSkgPT4gc3VtICsgZSwgMCkgKiAwLjEyOyAvLyAkMC4xMi9rV2hcclxuICAgIFxyXG4gICAgcmV0dXJuIHtcclxuICAgICAgZW5lcmd5OiBvdXRwdXREYXRhWzEwXSAqIDEwMCwgLy8gUGVyY2VudGFnZSBlbmVyZ3kgc2F2aW5nc1xyXG4gICAgICBjb3N0OiBjdXJyZW50RW5lcmd5Q29zdCAqIG91dHB1dERhdGFbMTBdLCAvLyBBbm51YWwgY29zdCBzYXZpbmdzXHJcbiAgICAgIGVtaXNzaW9uczogY3VycmVudEVuZXJneUNvc3QgKiBvdXRwdXREYXRhWzEwXSAqIDAuNCAqIDEwMDAgLy8ga2cgQ08yIGVxdWl2YWxlbnRcclxuICAgIH07XHJcbiAgfVxyXG5cclxuICBwcml2YXRlIGdlbmVyYXRlSW1wbGVtZW50YXRpb25QbGFuKHJlY29tbWVuZGF0aW9uczogT3B0aW1pemF0aW9uUmVjb21tZW5kYXRpb25bXSwgaW5wdXQ6IE9wdGltaXphdGlvbklucHV0KTogSW1wbGVtZW50YXRpb25TdGVwW10ge1xyXG4gICAgcmV0dXJuIHJlY29tbWVuZGF0aW9ucy5tYXAoKHJlYywgaW5kZXgpID0+ICh7XHJcbiAgICAgIGlkOiByZWMuaWQsXHJcbiAgICAgIGRlc2NyaXB0aW9uOiByZWMuZGVzY3JpcHRpb24sXHJcbiAgICAgIHByaW9yaXR5OiByZWMucHJpb3JpdHksXHJcbiAgICAgIGVzdGltYXRlZF9jb3N0OiByZWMuaW1wbGVtZW50YXRpb25fY29zdCxcclxuICAgICAgZXN0aW1hdGVkX2R1cmF0aW9uOiByZWMucGF5YmFja19wZXJpb2QgKiAzMCwgLy8gQ29udmVydCB0byBkYXlzXHJcbiAgICAgIGRlcGVuZGVuY2llczogaW5kZXggPiAwID8gW3JlY29tbWVuZGF0aW9uc1tpbmRleCAtIDFdLmlkXSA6IFtdLFxyXG4gICAgICBleHBlY3RlZF9pbXBhY3Q6IHJlYy5hbm51YWxfc2F2aW5nc1xyXG4gICAgfSkpO1xyXG4gIH1cclxuXHJcbiAgcHJpdmF0ZSBjYWxjdWxhdGVST0koc2F2aW5nczogYW55LCBwbGFuOiBJbXBsZW1lbnRhdGlvblN0ZXBbXSk6IFJPSUFuYWx5c2lzIHtcclxuICAgIGNvbnN0IHRvdGFsSW52ZXN0bWVudCA9IHBsYW4ucmVkdWNlKChzdW0sIHN0ZXApID0+IHN1bSArIHN0ZXAuZXN0aW1hdGVkX2Nvc3QsIDApO1xyXG4gICAgY29uc3QgYW5udWFsU2F2aW5ncyA9IHNhdmluZ3MuY29zdDtcclxuICAgIFxyXG4gICAgcmV0dXJuIHtcclxuICAgICAgaW5pdGlhbF9pbnZlc3RtZW50OiB0b3RhbEludmVzdG1lbnQsXHJcbiAgICAgIGFubnVhbF9zYXZpbmdzOiBhbm51YWxTYXZpbmdzLFxyXG4gICAgICBwYXliYWNrX3BlcmlvZDogdG90YWxJbnZlc3RtZW50IC8gYW5udWFsU2F2aW5ncyxcclxuICAgICAgbmV0X3ByZXNlbnRfdmFsdWU6IHRoaXMuY2FsY3VsYXRlTlBWKHRvdGFsSW52ZXN0bWVudCwgYW5udWFsU2F2aW5ncywgMC4wNSwgMTApLFxyXG4gICAgICBpbnRlcm5hbF9yYXRlX29mX3JldHVybjogdGhpcy5jYWxjdWxhdGVJUlIodG90YWxJbnZlc3RtZW50LCBhbm51YWxTYXZpbmdzLCAxMClcclxuICAgIH07XHJcbiAgfVxyXG5cclxuICBwcml2YXRlIGFzc2Vzc1Jpc2tzKHJlY29tbWVuZGF0aW9uczogT3B0aW1pemF0aW9uUmVjb21tZW5kYXRpb25bXSwgaW5wdXQ6IE9wdGltaXphdGlvbklucHV0KTogUmlza0ZhY3RvcltdIHtcclxuICAgIGNvbnN0IHJpc2tzOiBSaXNrRmFjdG9yW10gPSBbXTtcclxuICAgIFxyXG4gICAgLy8gQnVkZ2V0IHJpc2tcclxuICAgIGNvbnN0IHRvdGFsQ29zdCA9IHJlY29tbWVuZGF0aW9ucy5yZWR1Y2UoKHN1bSwgcmVjKSA9PiBzdW0gKyByZWMuaW1wbGVtZW50YXRpb25fY29zdCwgMCk7XHJcbiAgICBpZiAodG90YWxDb3N0ID4gaW5wdXQuY29uc3RyYWludHMuYnVkZ2V0ICogMC44KSB7XHJcbiAgICAgIHJpc2tzLnB1c2goe1xyXG4gICAgICAgIHR5cGU6ICdidWRnZXQnLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnSW1wbGVtZW50YXRpb24gY29zdCBhcHByb2FjaGVzIGJ1ZGdldCBsaW1pdCcsXHJcbiAgICAgICAgcHJvYmFiaWxpdHk6IDAuNyxcclxuICAgICAgICBpbXBhY3Q6IDAuOCxcclxuICAgICAgICBtaXRpZ2F0aW9uOiAnUGhhc2UgaW1wbGVtZW50YXRpb24gb3Igc2VlayBhZGRpdGlvbmFsIGZ1bmRpbmcnXHJcbiAgICAgIH0pO1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICAvLyBUaW1lbGluZSByaXNrXHJcbiAgICBjb25zdCB0b3RhbER1cmF0aW9uID0gcmVjb21tZW5kYXRpb25zLmxlbmd0aCAqIDMwOyAvLyBTaW1wbGlmaWVkXHJcbiAgICBpZiAodG90YWxEdXJhdGlvbiA+IGlucHV0LmNvbnN0cmFpbnRzLnRpbWVsaW5lICogMC44KSB7XHJcbiAgICAgIHJpc2tzLnB1c2goe1xyXG4gICAgICAgIHR5cGU6ICd0aW1lbGluZScsXHJcbiAgICAgICAgZGVzY3JpcHRpb246ICdJbXBsZW1lbnRhdGlvbiB0aW1lbGluZSBtYXkgZXhjZWVkIGNvbnN0cmFpbnRzJyxcclxuICAgICAgICBwcm9iYWJpbGl0eTogMC42LFxyXG4gICAgICAgIGltcGFjdDogMC42LFxyXG4gICAgICAgIG1pdGlnYXRpb246ICdQYXJhbGxlbCBpbXBsZW1lbnRhdGlvbiBvciBzY29wZSByZWR1Y3Rpb24nXHJcbiAgICAgIH0pO1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICByZXR1cm4gcmlza3M7XHJcbiAgfVxyXG5cclxuICBwcml2YXRlIHByZXBhcmVFbmVyZ3lJbnB1dChodmFjU3lzdGVtOiBIVkFDU3lzdGVtLCBlbnZpcm9ubWVudGFsRGF0YTogRW52aXJvbm1lbnRhbERhdGEsIHRpbWVIb3Jpem9uOiBudW1iZXIpOiBSZWNvcmQ8c3RyaW5nLCBvcnQuVGVuc29yPiB7XHJcbiAgICAvLyBQcmVwYXJlIGlucHV0IGZvciBlbmVyZ3kgcHJlZGljdGlvbiBtb2RlbFxyXG4gICAgY29uc3QgZmVhdHVyZXMgPSBbXHJcbiAgICAgIC4uLmVudmlyb25tZW50YWxEYXRhLm91dGRvb3JUZW1wZXJhdHVyZS5zbGljZSgwLCB0aW1lSG9yaXpvbiksXHJcbiAgICAgIC4uLmVudmlyb25tZW50YWxEYXRhLmh1bWlkaXR5LnNsaWNlKDAsIHRpbWVIb3Jpem9uKSxcclxuICAgICAgLy8gQWRkIG1vcmUgZmVhdHVyZXMgYXMgbmVlZGVkXHJcbiAgICBdO1xyXG4gICAgXHJcbiAgICByZXR1cm4ge1xyXG4gICAgICBpbnB1dDogbmV3IG9ydC5UZW5zb3IoJ2Zsb2F0MzInLCBuZXcgRmxvYXQzMkFycmF5KGZlYXR1cmVzKSwgWzEsIGZlYXR1cmVzLmxlbmd0aF0pXHJcbiAgICB9O1xyXG4gIH1cclxuXHJcbiAgcHJpdmF0ZSBwcmVwYXJlQW5vbWFseUlucHV0KHBlcmZvcm1hbmNlRGF0YTogUGVyZm9ybWFuY2VNZXRyaWNzW10pOiBSZWNvcmQ8c3RyaW5nLCBvcnQuVGVuc29yPiB7XHJcbiAgICBjb25zdCBmZWF0dXJlcyA9IHBlcmZvcm1hbmNlRGF0YS5mbGF0TWFwKGRhdGEgPT4gW1xyXG4gICAgICBkYXRhLmNvcCxcclxuICAgICAgZGF0YS5lZXIsXHJcbiAgICAgIGRhdGEuY2FwYWNpdHlfdXRpbGl6YXRpb24sXHJcbiAgICAgIGRhdGEudGVtcGVyYXR1cmVfdmFyaWFuY2VcclxuICAgIF0pO1xyXG4gICAgXHJcbiAgICByZXR1cm4ge1xyXG4gICAgICBpbnB1dDogbmV3IG9ydC5UZW5zb3IoJ2Zsb2F0MzInLCBuZXcgRmxvYXQzMkFycmF5KGZlYXR1cmVzKSwgWzEsIGZlYXR1cmVzLmxlbmd0aF0pXHJcbiAgICB9O1xyXG4gIH1cclxuXHJcbiAgcHJpdmF0ZSBwcmVwYXJlU2l6aW5nSW5wdXQoYnVpbGRpbmdEYXRhOiBCdWlsZGluZ0RhdGEsIGxvYWRSZXF1aXJlbWVudHM6IG51bWJlcltdKTogUmVjb3JkPHN0cmluZywgb3J0LlRlbnNvcj4ge1xyXG4gICAgY29uc3QgZmVhdHVyZXMgPSBbXHJcbiAgICAgIGJ1aWxkaW5nRGF0YS5hcmVhLFxyXG4gICAgICBidWlsZGluZ0RhdGEudm9sdW1lLFxyXG4gICAgICBidWlsZGluZ0RhdGEub2NjdXBhbmN5LFxyXG4gICAgICAuLi5sb2FkUmVxdWlyZW1lbnRzLnNsaWNlKDAsIDI0KVxyXG4gICAgXTtcclxuICAgIFxyXG4gICAgcmV0dXJuIHtcclxuICAgICAgaW5wdXQ6IG5ldyBvcnQuVGVuc29yKCdmbG9hdDMyJywgbmV3IEZsb2F0MzJBcnJheShmZWF0dXJlcyksIFsxLCBmZWF0dXJlcy5sZW5ndGhdKVxyXG4gICAgfTtcclxuICB9XHJcblxyXG4gIHByaXZhdGUgZGVjb2RlRXF1aXBtZW50VHlwZXMoZGF0YTogRmxvYXQzMkFycmF5KTogc3RyaW5nW10ge1xyXG4gICAgLy8gRGVjb2RlIGVxdWlwbWVudCB0eXBlIGluZGljZXMgdG8gbmFtZXNcclxuICAgIGNvbnN0IGVxdWlwbWVudFR5cGVzID0gWydIZWF0IFB1bXAnLCAnQ2hpbGxlcicsICdCb2lsZXInLCAnQWlyIEhhbmRsZXInLCAnRmFuIENvaWwnXTtcclxuICAgIHJldHVybiBBcnJheS5mcm9tKGRhdGEpLm1hcChpbmRleCA9PiBlcXVpcG1lbnRUeXBlc1tNYXRoLmZsb29yKGluZGV4KV0gfHwgJ1Vua25vd24nKTtcclxuICB9XHJcblxyXG4gIHByaXZhdGUgYXN5bmMgYXNzZXNzUmVuZXdhYmxlUG90ZW50aWFsKGh2YWNTeXN0ZW06IEhWQUNTeXN0ZW0pOiBQcm9taXNlPG51bWJlcj4ge1xyXG4gICAgLy8gU2ltcGxpZmllZCByZW5ld2FibGUgcG90ZW50aWFsIGFzc2Vzc21lbnRcclxuICAgIHJldHVybiBNYXRoLnJhbmRvbSgpICogMC41ICsgMC4zOyAvLyAzMC04MCUgcG90ZW50aWFsXHJcbiAgfVxyXG5cclxuICBwcml2YXRlIGNhbGN1bGF0ZVN1c3RhaW5hYmlsaXR5U2NvcmUoaHZhY1N5c3RlbTogSFZBQ1N5c3RlbSwgY2FyYm9uRm9vdHByaW50OiBudW1iZXIsIHJlbmV3YWJsZVBvdGVudGlhbDogbnVtYmVyKTogbnVtYmVyIHtcclxuICAgIC8vIFNpbXBsaWZpZWQgc3VzdGFpbmFiaWxpdHkgc2NvcmluZ1xyXG4gICAgY29uc3QgZWZmaWNpZW5jeVNjb3JlID0gTWF0aC5taW4oaHZhY1N5c3RlbS5lZmZpY2llbmN5IHx8IDAuOCwgMS4wKTtcclxuICAgIGNvbnN0IGNhcmJvblNjb3JlID0gTWF0aC5tYXgoMCwgMSAtIGNhcmJvbkZvb3RwcmludCAvIDEwMDAwKTtcclxuICAgIGNvbnN0IHJlbmV3YWJsZVNjb3JlID0gcmVuZXdhYmxlUG90ZW50aWFsO1xyXG4gICAgXHJcbiAgICByZXR1cm4gKGVmZmljaWVuY3lTY29yZSAqIDAuNCArIGNhcmJvblNjb3JlICogMC40ICsgcmVuZXdhYmxlU2NvcmUgKiAwLjIpICogMTAwO1xyXG4gIH1cclxuXHJcbiAgcHJpdmF0ZSBnZW5lcmF0ZUVudmlyb25tZW50YWxSZWNvbW1lbmRhdGlvbnMoc3VzdGFpbmFiaWxpdHlTY29yZTogbnVtYmVyLCByZW5ld2FibGVQb3RlbnRpYWw6IG51bWJlcik6IHN0cmluZ1tdIHtcclxuICAgIGNvbnN0IHJlY29tbWVuZGF0aW9uczogc3RyaW5nW10gPSBbXTtcclxuICAgIFxyXG4gICAgaWYgKHN1c3RhaW5hYmlsaXR5U2NvcmUgPCA2MCkge1xyXG4gICAgICByZWNvbW1lbmRhdGlvbnMucHVzaCgnQ29uc2lkZXIgdXBncmFkaW5nIHRvIGhpZ2gtZWZmaWNpZW5jeSBlcXVpcG1lbnQnKTtcclxuICAgIH1cclxuICAgIFxyXG4gICAgaWYgKHJlbmV3YWJsZVBvdGVudGlhbCA+IDAuNSkge1xyXG4gICAgICByZWNvbW1lbmRhdGlvbnMucHVzaCgnRXhwbG9yZSBzb2xhciBwYW5lbCBpbnRlZ3JhdGlvbicpO1xyXG4gICAgICByZWNvbW1lbmRhdGlvbnMucHVzaCgnQ29uc2lkZXIgZ2VvdGhlcm1hbCBoZWF0IHB1bXAgc3lzdGVtcycpO1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICByZWNvbW1lbmRhdGlvbnMucHVzaCgnSW1wbGVtZW50IHNtYXJ0IHNjaGVkdWxpbmcgdG8gcmVkdWNlIHBlYWsgZGVtYW5kJyk7XHJcbiAgICByZWNvbW1lbmRhdGlvbnMucHVzaCgnUmVndWxhciBtYWludGVuYW5jZSB0byBtYWludGFpbiBlZmZpY2llbmN5Jyk7XHJcbiAgICBcclxuICAgIHJldHVybiByZWNvbW1lbmRhdGlvbnM7XHJcbiAgfVxyXG5cclxuICBwcml2YXRlIGFzeW5jIHZhbGlkYXRlUmVjb21tZW5kYXRpb25zKHJlc3VsdDogT3B0aW1pemF0aW9uUmVzdWx0LCBpbnB1dDogT3B0aW1pemF0aW9uSW5wdXQpOiBQcm9taXNlPE9wdGltaXphdGlvblJlc3VsdD4ge1xyXG4gICAgLy8gVmFsaWRhdGUgcmVjb21tZW5kYXRpb25zIGFnYWluc3QgY29uc3RyYWludHMgYW5kIGZlYXNpYmlsaXR5XHJcbiAgICBjb25zdCB2YWxpZGF0ZWRSZWNvbW1lbmRhdGlvbnMgPSByZXN1bHQucmVjb21tZW5kYXRpb25zLmZpbHRlcihyZWMgPT4ge1xyXG4gICAgICAvLyBDaGVjayBidWRnZXQgY29uc3RyYWludHNcclxuICAgICAgaWYgKHJlYy5pbXBsZW1lbnRhdGlvbl9jb3N0ID4gaW5wdXQuY29uc3RyYWludHMuYnVkZ2V0KSB7XHJcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xyXG4gICAgICB9XHJcbiAgICAgIFxyXG4gICAgICAvLyBDaGVjayB0aW1lbGluZSBjb25zdHJhaW50c1xyXG4gICAgICBpZiAocmVjLnBheWJhY2tfcGVyaW9kICogMzAgPiBpbnB1dC5jb25zdHJhaW50cy50aW1lbGluZSkge1xyXG4gICAgICAgIHJldHVybiBmYWxzZTtcclxuICAgICAgfVxyXG4gICAgICBcclxuICAgICAgcmV0dXJuIHRydWU7XHJcbiAgICB9KTtcclxuICAgIFxyXG4gICAgcmV0dXJuIHtcclxuICAgICAgLi4ucmVzdWx0LFxyXG4gICAgICByZWNvbW1lbmRhdGlvbnM6IHZhbGlkYXRlZFJlY29tbWVuZGF0aW9uc1xyXG4gICAgfTtcclxuICB9XHJcblxyXG4gIHByaXZhdGUgY2FsY3VsYXRlTlBWKGludmVzdG1lbnQ6IG51bWJlciwgYW5udWFsU2F2aW5nczogbnVtYmVyLCBkaXNjb3VudFJhdGU6IG51bWJlciwgeWVhcnM6IG51bWJlcik6IG51bWJlciB7XHJcbiAgICBsZXQgbnB2ID0gLWludmVzdG1lbnQ7XHJcbiAgICBmb3IgKGxldCB5ZWFyID0gMTsgeWVhciA8PSB5ZWFyczsgeWVhcisrKSB7XHJcbiAgICAgIG5wdiArPSBhbm51YWxTYXZpbmdzIC8gTWF0aC5wb3coMSArIGRpc2NvdW50UmF0ZSwgeWVhcik7XHJcbiAgICB9XHJcbiAgICByZXR1cm4gbnB2O1xyXG4gIH1cclxuXHJcbiAgcHJpdmF0ZSBjYWxjdWxhdGVJUlIoaW52ZXN0bWVudDogbnVtYmVyLCBhbm51YWxTYXZpbmdzOiBudW1iZXIsIHllYXJzOiBudW1iZXIpOiBudW1iZXIge1xyXG4gICAgLy8gU2ltcGxpZmllZCBJUlIgY2FsY3VsYXRpb25cclxuICAgIHJldHVybiAoYW5udWFsU2F2aW5ncyAvIGludmVzdG1lbnQpICogMTAwO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ2xlYW51cCByZXNvdXJjZXNcclxuICAgKi9cclxuICBhc3luYyBkaXNwb3NlKCk6IFByb21pc2U8dm9pZD4ge1xyXG4gICAgaWYgKHRoaXMuc2Vzc2lvbikge1xyXG4gICAgICBhd2FpdCB0aGlzLnNlc3Npb24ucmVsZWFzZSgpO1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICBmb3IgKGNvbnN0IFtuYW1lLCBzZXNzaW9uXSBvZiB0aGlzLm1vZGVsQ2FjaGUpIHtcclxuICAgICAgYXdhaXQgc2Vzc2lvbi5yZWxlYXNlKCk7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIHRoaXMubW9kZWxDYWNoZS5jbGVhcigpO1xyXG4gICAgdGhpcy5pc0luaXRpYWxpemVkID0gZmFsc2U7XHJcbiAgfVxyXG59XHJcblxyXG4vLyBTaW5nbGV0b24gaW5zdGFuY2VcclxubGV0IGFpT3B0aW1pemF0aW9uU2VydmljZTogQUlPcHRpbWl6YXRpb25TZXJ2aWNlIHwgbnVsbCA9IG51bGw7XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gZ2V0QUlPcHRpbWl6YXRpb25TZXJ2aWNlKGNvbmZpZz86IEFJT3B0aW1pemF0aW9uQ29uZmlnKTogQUlPcHRpbWl6YXRpb25TZXJ2aWNlIHtcclxuICBpZiAoIWFpT3B0aW1pemF0aW9uU2VydmljZSAmJiBjb25maWcpIHtcclxuICAgIGFpT3B0aW1pemF0aW9uU2VydmljZSA9IG5ldyBBSU9wdGltaXphdGlvblNlcnZpY2UoY29uZmlnKTtcclxuICB9XHJcbiAgcmV0dXJuIGFpT3B0aW1pemF0aW9uU2VydmljZSE7XHJcbn1cclxuXHJcbmV4cG9ydCBkZWZhdWx0IEFJT3B0aW1pemF0aW9uU2VydmljZTtcclxuXHJcbi8vIFJlYWN0IEhvb2sgZm9yIEFJIE9wdGltaXphdGlvblxyXG5leHBvcnQgZnVuY3Rpb24gdXNlQUlPcHRpbWl6YXRpb24oY29uZmlnPzogQUlPcHRpbWl6YXRpb25Db25maWcpIHtcclxuICBjb25zdCBbc2VydmljZSwgc2V0U2VydmljZV0gPSB1c2VTdGF0ZTxBSU9wdGltaXphdGlvblNlcnZpY2UgfCBudWxsPihudWxsKTtcclxuICBjb25zdCBbaXNJbml0aWFsaXplZCwgc2V0SXNJbml0aWFsaXplZF0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2lzTG9hZGluZywgc2V0SXNMb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbZXJyb3IsIHNldEVycm9yXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xyXG5cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgaWYgKGNvbmZpZykge1xyXG4gICAgICBjb25zdCBhaVNlcnZpY2UgPSBuZXcgQUlPcHRpbWl6YXRpb25TZXJ2aWNlKGNvbmZpZyk7XHJcbiAgICAgIHNldFNlcnZpY2UoYWlTZXJ2aWNlKTtcclxuXHJcbiAgICAgIGFpU2VydmljZS5pbml0aWFsaXplKClcclxuICAgICAgICAudGhlbigoKSA9PiBzZXRJc0luaXRpYWxpemVkKHRydWUpKVxyXG4gICAgICAgIC5jYXRjaChlcnIgPT4gc2V0RXJyb3IoZXJyLm1lc3NhZ2UpKTtcclxuICAgIH1cclxuICB9LCBbY29uZmlnXSk7XHJcblxyXG4gIGNvbnN0IG9wdGltaXplU3lzdGVtID0gdXNlQ2FsbGJhY2soYXN5bmMgKGlucHV0OiBPcHRpbWl6YXRpb25JbnB1dCkgPT4ge1xyXG4gICAgaWYgKCFzZXJ2aWNlIHx8ICFpc0luaXRpYWxpemVkKSB7XHJcbiAgICAgIHRocm93IG5ldyBFcnJvcignQUkgc2VydmljZSBub3QgaW5pdGlhbGl6ZWQnKTtcclxuICAgIH1cclxuXHJcbiAgICBzZXRJc0xvYWRpbmcodHJ1ZSk7XHJcbiAgICBzZXRFcnJvcihudWxsKTtcclxuXHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBzZXJ2aWNlLm9wdGltaXplU3lzdGVtKGlucHV0KTtcclxuICAgICAgcmV0dXJuIHJlc3VsdDtcclxuICAgIH0gY2F0Y2ggKGVycikge1xyXG4gICAgICBjb25zdCBlcnJvck1lc3NhZ2UgPSBlcnIgaW5zdGFuY2VvZiBFcnJvciA/IGVyci5tZXNzYWdlIDogJ09wdGltaXphdGlvbiBmYWlsZWQnO1xyXG4gICAgICBzZXRFcnJvcihlcnJvck1lc3NhZ2UpO1xyXG4gICAgICB0aHJvdyBlcnI7XHJcbiAgICB9IGZpbmFsbHkge1xyXG4gICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xyXG4gICAgfVxyXG4gIH0sIFtzZXJ2aWNlLCBpc0luaXRpYWxpemVkXSk7XHJcblxyXG4gIGNvbnN0IHByZWRpY3RFbmVyZ3kgPSB1c2VDYWxsYmFjayhhc3luYyAoXHJcbiAgICBodmFjU3lzdGVtOiBIVkFDU3lzdGVtLFxyXG4gICAgZW52aXJvbm1lbnRhbERhdGE6IEVudmlyb25tZW50YWxEYXRhLFxyXG4gICAgdGltZUhvcml6b24/OiBudW1iZXJcclxuICApID0+IHtcclxuICAgIGlmICghc2VydmljZSB8fCAhaXNJbml0aWFsaXplZCkge1xyXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ0FJIHNlcnZpY2Ugbm90IGluaXRpYWxpemVkJyk7XHJcbiAgICB9XHJcblxyXG4gICAgcmV0dXJuIHNlcnZpY2UucHJlZGljdEVuZXJneUNvbnN1bXB0aW9uKGh2YWNTeXN0ZW0sIGVudmlyb25tZW50YWxEYXRhLCB0aW1lSG9yaXpvbik7XHJcbiAgfSwgW3NlcnZpY2UsIGlzSW5pdGlhbGl6ZWRdKTtcclxuXHJcbiAgY29uc3QgZGV0ZWN0QW5vbWFsaWVzID0gdXNlQ2FsbGJhY2soYXN5bmMgKFxyXG4gICAgcGVyZm9ybWFuY2VEYXRhOiBQZXJmb3JtYW5jZU1ldHJpY3NbXSxcclxuICAgIHRocmVzaG9sZD86IG51bWJlclxyXG4gICkgPT4ge1xyXG4gICAgaWYgKCFzZXJ2aWNlIHx8ICFpc0luaXRpYWxpemVkKSB7XHJcbiAgICAgIHRocm93IG5ldyBFcnJvcignQUkgc2VydmljZSBub3QgaW5pdGlhbGl6ZWQnKTtcclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4gc2VydmljZS5kZXRlY3RBbm9tYWxpZXMocGVyZm9ybWFuY2VEYXRhLCB0aHJlc2hvbGQpO1xyXG4gIH0sIFtzZXJ2aWNlLCBpc0luaXRpYWxpemVkXSk7XHJcblxyXG4gIHJldHVybiB7XHJcbiAgICBpc0luaXRpYWxpemVkLFxyXG4gICAgaXNMb2FkaW5nLFxyXG4gICAgZXJyb3IsXHJcbiAgICBvcHRpbWl6ZVN5c3RlbSxcclxuICAgIHByZWRpY3RFbmVyZ3ksXHJcbiAgICBkZXRlY3RBbm9tYWxpZXMsXHJcbiAgICBzZXJ2aWNlXHJcbiAgfTtcclxufVxyXG4iXSwibWFwcGluZ3MiOiI7O0FBQUE7Ozs7Ozs7Ozs7Ozs7O0FBQUE7QUFBQSxTQUFBQSxlQUFBO0VBQUEsSUFBQUMsSUFBQTtFQUFBLElBQUFDLElBQUE7RUFBQSxJQUFBQyxNQUFBLE9BQUFDLFFBQUE7RUFBQSxJQUFBQyxHQUFBO0VBQUEsSUFBQUMsWUFBQTtJQUFBTCxJQUFBO0lBQUFNLFlBQUE7TUFBQTtRQUFBQyxLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO0lBQUE7SUFBQUUsS0FBQTtNQUFBO1FBQUFDLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtJQUFBO0lBQUFPLFNBQUE7TUFBQTtRQUFBRCxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO0lBQUE7SUFBQVcsQ0FBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7SUFBQTtJQUFBQyxDQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO0lBQUE7SUFBQUMsQ0FBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7SUFBQTtJQUFBQyxjQUFBO01BQUFDLElBQUE7TUFBQUMsUUFBQTtNQUFBQyxLQUFBO01BQUFDLE9BQUE7TUFBQUMsY0FBQTtNQUFBQyxPQUFBO0lBQUE7SUFBQUMsZUFBQTtJQUFBNUIsSUFBQTtFQUFBO0VBQUEsSUFBQTZCLFFBQUEsR0FBQTVCLE1BQUEsQ0FBQUUsR0FBQSxNQUFBRixNQUFBLENBQUFFLEdBQUE7RUFBQSxLQUFBMEIsUUFBQSxDQUFBOUIsSUFBQSxLQUFBOEIsUUFBQSxDQUFBOUIsSUFBQSxFQUFBQyxJQUFBLEtBQUFBLElBQUE7SUFBQTZCLFFBQUEsQ0FBQTlCLElBQUEsSUFBQUssWUFBQTtFQUFBO0VBQUEsSUFBQTBCLGNBQUEsR0FBQUQsUUFBQSxDQUFBOUIsSUFBQTtFQUFBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUF1c0JBZ0MsT0FBQSxDQUFBQyx3QkFBQSxHQUFBQSx3QkFBQTtBQUtDO0FBQUFsQyxjQUFBLEdBQUFvQixDQUFBO0FBS0RhLE9BQUEsQ0FBQUUsaUJBQUEsR0FBQUEsaUJBQUE7QUFsc0JBLE1BQUFDLE9BQUE7QUFBQTtBQUFBLENBQUFwQyxjQUFBLEdBQUFvQixDQUFBLFFBQUFpQixPQUFBO0FBQ0EsTUFBQUMsR0FBQTtBQUFBO0FBQUEsQ0FBQXRDLGNBQUEsR0FBQW9CLENBQUEsUUFBQW1CLFlBQUEsQ0FBQUYsT0FBQTtBQXNKQSxNQUFhRyxxQkFBcUI7RUFNaENDLFlBQVlDLE1BQTRCO0lBQUE7SUFBQTFDLGNBQUEsR0FBQXFCLENBQUE7SUFBQXJCLGNBQUEsR0FBQW9CLENBQUE7SUFMaEMsS0FBQXVCLE9BQU8sR0FBZ0MsSUFBSTtJQUFDO0lBQUEzQyxjQUFBLEdBQUFvQixDQUFBO0lBRTVDLEtBQUF3QixVQUFVLEdBQXNDLElBQUlDLEdBQUcsRUFBRTtJQUFDO0lBQUE3QyxjQUFBLEdBQUFvQixDQUFBO0lBQzFELEtBQUEwQixhQUFhLEdBQUcsS0FBSztJQUFDO0lBQUE5QyxjQUFBLEdBQUFvQixDQUFBO0lBRzVCLElBQUksQ0FBQ3NCLE1BQU0sR0FBRztNQUNaSyxTQUFTLEVBQUUsS0FBSztNQUNoQkMsU0FBUyxFQUFFLENBQUM7TUFDWkMsbUJBQW1CLEVBQUUsR0FBRztNQUN4QkMsaUJBQWlCLEVBQUUsQ0FDakI7UUFBRWpDLElBQUksRUFBRSxtQkFBbUI7UUFBRWtDLE1BQU0sRUFBRTtNQUFHLENBQUUsRUFDMUM7UUFBRWxDLElBQUksRUFBRSxnQkFBZ0I7UUFBRWtDLE1BQU0sRUFBRTtNQUFHLENBQUUsRUFDdkM7UUFBRWxDLElBQUksRUFBRSxTQUFTO1FBQUVrQyxNQUFNLEVBQUU7TUFBRyxDQUFFLEVBQ2hDO1FBQUVsQyxJQUFJLEVBQUUsZUFBZTtRQUFFa0MsTUFBTSxFQUFFO01BQUcsQ0FBRSxDQUN2QztNQUNELEdBQUdUO0tBQ0o7RUFDSDtFQUVBOzs7RUFHQSxNQUFNVSxVQUFVQSxDQUFBO0lBQUE7SUFBQXBELGNBQUEsR0FBQXFCLENBQUE7SUFBQXJCLGNBQUEsR0FBQW9CLENBQUE7SUFDZCxJQUFJO01BQUE7TUFBQXBCLGNBQUEsR0FBQW9CLENBQUE7TUFDRjtNQUNBLElBQUksSUFBSSxDQUFDc0IsTUFBTSxDQUFDSyxTQUFTLEVBQUU7UUFBQTtRQUFBL0MsY0FBQSxHQUFBc0IsQ0FBQTtRQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtRQUN6QmtCLEdBQUcsQ0FBQ2UsR0FBRyxDQUFDQyxJQUFJLENBQUNDLFVBQVUsR0FBRyxDQUFDO1FBQUM7UUFBQXZELGNBQUEsR0FBQW9CLENBQUE7UUFDNUJrQixHQUFHLENBQUNlLEdBQUcsQ0FBQ0MsSUFBSSxDQUFDRSxJQUFJLEdBQUcsSUFBSTtNQUMxQixDQUFDO01BQUE7TUFBQTtRQUFBeEQsY0FBQSxHQUFBc0IsQ0FBQTtNQUFBO01BRUQ7TUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7TUFDQSxJQUFJLENBQUN1QixPQUFPLEdBQUcsTUFBTUwsR0FBRyxDQUFDbUIsZ0JBQWdCLENBQUNDLE1BQU0sQ0FBQyxJQUFJLENBQUNoQixNQUFNLENBQUNpQixTQUFTLENBQUM7TUFFdkU7TUFBQTtNQUFBM0QsY0FBQSxHQUFBb0IsQ0FBQTtNQUNBLE1BQU0sSUFBSSxDQUFDd0MscUJBQXFCLEVBQUU7TUFBQztNQUFBNUQsY0FBQSxHQUFBb0IsQ0FBQTtNQUVuQyxJQUFJLENBQUMwQixhQUFhLEdBQUcsSUFBSTtNQUFDO01BQUE5QyxjQUFBLEdBQUFvQixDQUFBO01BQzFCeUMsT0FBTyxDQUFDQyxHQUFHLENBQUMsa0RBQWtELENBQUM7SUFFakUsQ0FBQyxDQUFDLE9BQU9DLEtBQUssRUFBRTtNQUFBO01BQUEvRCxjQUFBLEdBQUFvQixDQUFBO01BQ2R5QyxPQUFPLENBQUNFLEtBQUssQ0FBQywrQ0FBK0MsRUFBRUEsS0FBSyxDQUFDO01BQUM7TUFBQS9ELGNBQUEsR0FBQW9CLENBQUE7TUFDdEUsTUFBTTJDLEtBQUs7SUFDYjtFQUNGO0VBRUE7OztFQUdBLE1BQU1DLGNBQWNBLENBQUNDLEtBQXdCO0lBQUE7SUFBQWpFLGNBQUEsR0FBQXFCLENBQUE7SUFBQXJCLGNBQUEsR0FBQW9CLENBQUE7SUFDM0M7SUFBSTtJQUFBLENBQUFwQixjQUFBLEdBQUFzQixDQUFBLFlBQUMsSUFBSSxDQUFDd0IsYUFBYTtJQUFBO0lBQUEsQ0FBQTlDLGNBQUEsR0FBQXNCLENBQUEsV0FBSSxDQUFDLElBQUksQ0FBQ3FCLE9BQU8sR0FBRTtNQUFBO01BQUEzQyxjQUFBLEdBQUFzQixDQUFBO01BQUF0QixjQUFBLEdBQUFvQixDQUFBO01BQ3hDLE1BQU0sSUFBSThDLEtBQUssQ0FBQyx5Q0FBeUMsQ0FBQztJQUM1RCxDQUFDO0lBQUE7SUFBQTtNQUFBbEUsY0FBQSxHQUFBc0IsQ0FBQTtJQUFBO0lBQUF0QixjQUFBLEdBQUFvQixDQUFBO0lBRUQsSUFBSTtNQUNGO01BQ0EsTUFBTStDLFVBQVU7TUFBQTtNQUFBLENBQUFuRSxjQUFBLEdBQUFvQixDQUFBLFFBQUcsTUFBTSxJQUFJLENBQUNnRCxpQkFBaUIsQ0FBQ0gsS0FBSyxDQUFDO01BRXREO01BQ0EsTUFBTUksT0FBTztNQUFBO01BQUEsQ0FBQXJFLGNBQUEsR0FBQW9CLENBQUEsUUFBRyxNQUFNLElBQUksQ0FBQ3VCLE9BQU8sQ0FBQzJCLEdBQUcsQ0FBQ0gsVUFBVSxDQUFDO01BRWxEO01BQ0EsTUFBTUksa0JBQWtCO01BQUE7TUFBQSxDQUFBdkUsY0FBQSxHQUFBb0IsQ0FBQSxRQUFHLE1BQU0sSUFBSSxDQUFDb0QsY0FBYyxDQUFDSCxPQUFPLEVBQUVKLEtBQUssQ0FBQztNQUVwRTtNQUNBLE1BQU1RLGVBQWU7TUFBQTtNQUFBLENBQUF6RSxjQUFBLEdBQUFvQixDQUFBLFFBQUcsTUFBTSxJQUFJLENBQUNzRCx1QkFBdUIsQ0FBQ0gsa0JBQWtCLEVBQUVOLEtBQUssQ0FBQztNQUFDO01BQUFqRSxjQUFBLEdBQUFvQixDQUFBO01BRXRGLE9BQU9xRCxlQUFlO0lBRXhCLENBQUMsQ0FBQyxPQUFPVixLQUFLLEVBQUU7TUFBQTtNQUFBL0QsY0FBQSxHQUFBb0IsQ0FBQTtNQUNkeUMsT0FBTyxDQUFDRSxLQUFLLENBQUMsc0JBQXNCLEVBQUVBLEtBQUssQ0FBQztNQUFDO01BQUEvRCxjQUFBLEdBQUFvQixDQUFBO01BQzdDLE1BQU0yQyxLQUFLO0lBQ2I7RUFDRjtFQUVBOzs7RUFHQSxNQUFNWSx3QkFBd0JBLENBQzVCQyxVQUFzQixFQUN0QkMsaUJBQW9DLEVBQ3BDQyxXQUFBO0VBQUE7RUFBQSxDQUFBOUUsY0FBQSxHQUFBc0IsQ0FBQSxXQUFzQixFQUFFLEVBQUM7RUFBQSxFOzs7SUFFekIsTUFBTXlELFdBQVc7SUFBQTtJQUFBLENBQUEvRSxjQUFBLEdBQUFvQixDQUFBLFFBQUcsSUFBSSxDQUFDd0IsVUFBVSxDQUFDb0MsR0FBRyxDQUFDLG1CQUFtQixDQUFDO0lBQUM7SUFBQWhGLGNBQUEsR0FBQW9CLENBQUE7SUFDN0QsSUFBSSxDQUFDMkQsV0FBVyxFQUFFO01BQUE7TUFBQS9FLGNBQUEsR0FBQXNCLENBQUE7TUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7TUFDaEIsTUFBTSxJQUFJOEMsS0FBSyxDQUFDLG9DQUFvQyxDQUFDO0lBQ3ZELENBQUM7SUFBQTtJQUFBO01BQUFsRSxjQUFBLEdBQUFzQixDQUFBO0lBQUE7SUFFRCxNQUFNMkMsS0FBSztJQUFBO0lBQUEsQ0FBQWpFLGNBQUEsR0FBQW9CLENBQUEsUUFBRyxJQUFJLENBQUM2RCxrQkFBa0IsQ0FBQ0wsVUFBVSxFQUFFQyxpQkFBaUIsRUFBRUMsV0FBVyxDQUFDO0lBQ2pGLE1BQU1ULE9BQU87SUFBQTtJQUFBLENBQUFyRSxjQUFBLEdBQUFvQixDQUFBLFFBQUcsTUFBTTJELFdBQVcsQ0FBQ1QsR0FBRyxDQUFDTCxLQUFLLENBQUM7SUFBQztJQUFBakUsY0FBQSxHQUFBb0IsQ0FBQTtJQUU3QyxPQUFPOEQsS0FBSyxDQUFDQyxJQUFJLENBQUNkLE9BQU8sQ0FBQ2Usa0JBQWtCLENBQUNDLElBQW9CLENBQUM7RUFDcEU7RUFFQTs7O0VBR0EsTUFBTUMsZUFBZUEsQ0FDbkJDLGVBQXFDLEVBQ3JDQyxTQUFBO0VBQUE7RUFBQSxDQUFBeEYsY0FBQSxHQUFBc0IsQ0FBQSxXQUFvQixHQUFHO0lBQUE7SUFBQXRCLGNBQUEsR0FBQXFCLENBQUE7SUFFdkIsTUFBTW9FLFlBQVk7SUFBQTtJQUFBLENBQUF6RixjQUFBLEdBQUFvQixDQUFBLFFBQUcsSUFBSSxDQUFDd0IsVUFBVSxDQUFDb0MsR0FBRyxDQUFDLG1CQUFtQixDQUFDO0lBQUM7SUFBQWhGLGNBQUEsR0FBQW9CLENBQUE7SUFDOUQsSUFBSSxDQUFDcUUsWUFBWSxFQUFFO01BQUE7TUFBQXpGLGNBQUEsR0FBQXNCLENBQUE7TUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7TUFDakIsTUFBTSxJQUFJOEMsS0FBSyxDQUFDLG9DQUFvQyxDQUFDO0lBQ3ZELENBQUM7SUFBQTtJQUFBO01BQUFsRSxjQUFBLEdBQUFzQixDQUFBO0lBQUE7SUFFRCxNQUFNMkMsS0FBSztJQUFBO0lBQUEsQ0FBQWpFLGNBQUEsR0FBQW9CLENBQUEsUUFBRyxJQUFJLENBQUNzRSxtQkFBbUIsQ0FBQ0gsZUFBZSxDQUFDO0lBQ3ZELE1BQU1sQixPQUFPO0lBQUE7SUFBQSxDQUFBckUsY0FBQSxHQUFBb0IsQ0FBQSxRQUFHLE1BQU1xRSxZQUFZLENBQUNuQixHQUFHLENBQUNMLEtBQUssQ0FBQztJQUU3QyxNQUFNMEIsYUFBYTtJQUFBO0lBQUEsQ0FBQTNGLGNBQUEsR0FBQW9CLENBQUEsUUFBRzhELEtBQUssQ0FBQ0MsSUFBSSxDQUFDZCxPQUFPLENBQUN1QixjQUFjLENBQUNQLElBQW9CLENBQUM7SUFDN0UsTUFBTVEsZ0JBQWdCO0lBQUE7SUFBQSxDQUFBN0YsY0FBQSxHQUFBb0IsQ0FBQSxRQUFHOEQsS0FBSyxDQUFDQyxJQUFJLENBQUNkLE9BQU8sQ0FBQ3lCLFVBQVUsQ0FBQ1QsSUFBb0IsQ0FBQztJQUU1RSxNQUFNVSxTQUFTO0lBQUE7SUFBQSxDQUFBL0YsY0FBQSxHQUFBb0IsQ0FBQSxRQUFHdUUsYUFBYSxDQUFDSyxHQUFHLENBQUMsQ0FBQ0MsS0FBSyxFQUFFQyxLQUFLLEtBQy9DO01BQUE7TUFBQWxHLGNBQUEsR0FBQXFCLENBQUE7TUFBQXJCLGNBQUEsR0FBQW9CLENBQUE7TUFBQSxPQUFBNkUsS0FBSyxHQUFHVCxTQUFTO01BQUE7TUFBQSxDQUFBeEYsY0FBQSxHQUFBc0IsQ0FBQSxXQUFHNEUsS0FBSztNQUFBO01BQUEsQ0FBQWxHLGNBQUEsR0FBQXNCLENBQUEsV0FBRyxDQUFDLENBQUM7SUFBRCxDQUFDLENBQy9CLENBQUM2RSxNQUFNLENBQUNELEtBQUssSUFBSTtNQUFBO01BQUFsRyxjQUFBLEdBQUFxQixDQUFBO01BQUFyQixjQUFBLEdBQUFvQixDQUFBO01BQUEsT0FBQThFLEtBQUssS0FBSyxDQUFDLENBQUM7SUFBRCxDQUFDLENBQUM7SUFBQztJQUFBbEcsY0FBQSxHQUFBb0IsQ0FBQTtJQUVoQyxPQUFPO01BQ0wyRSxTQUFTO01BQ1RELFVBQVUsRUFBRUMsU0FBUyxDQUFDQyxHQUFHLENBQUNFLEtBQUssSUFBSTtRQUFBO1FBQUFsRyxjQUFBLEdBQUFxQixDQUFBO1FBQUFyQixjQUFBLEdBQUFvQixDQUFBO1FBQUEsT0FBQXlFLGdCQUFnQixDQUFDSyxLQUFLLENBQUM7TUFBRCxDQUFDO0tBQzNEO0VBQ0g7RUFFQTs7O0VBR0EsTUFBTUUsd0JBQXdCQSxDQUM1QkMsWUFBMEIsRUFDMUJDLGdCQUEwQjtJQUFBO0lBQUF0RyxjQUFBLEdBQUFxQixDQUFBO0lBTzFCLE1BQU1rRixXQUFXO0lBQUE7SUFBQSxDQUFBdkcsY0FBQSxHQUFBb0IsQ0FBQSxRQUFHLElBQUksQ0FBQ3dCLFVBQVUsQ0FBQ29DLEdBQUcsQ0FBQyxrQkFBa0IsQ0FBQztJQUFDO0lBQUFoRixjQUFBLEdBQUFvQixDQUFBO0lBQzVELElBQUksQ0FBQ21GLFdBQVcsRUFBRTtNQUFBO01BQUF2RyxjQUFBLEdBQUFzQixDQUFBO01BQUF0QixjQUFBLEdBQUFvQixDQUFBO01BQ2hCLE1BQU0sSUFBSThDLEtBQUssQ0FBQyxtQ0FBbUMsQ0FBQztJQUN0RCxDQUFDO0lBQUE7SUFBQTtNQUFBbEUsY0FBQSxHQUFBc0IsQ0FBQTtJQUFBO0lBRUQsTUFBTTJDLEtBQUs7SUFBQTtJQUFBLENBQUFqRSxjQUFBLEdBQUFvQixDQUFBLFFBQUcsSUFBSSxDQUFDb0Ysa0JBQWtCLENBQUNILFlBQVksRUFBRUMsZ0JBQWdCLENBQUM7SUFDckUsTUFBTWpDLE9BQU87SUFBQTtJQUFBLENBQUFyRSxjQUFBLEdBQUFvQixDQUFBLFFBQUcsTUFBTW1GLFdBQVcsQ0FBQ2pDLEdBQUcsQ0FBQ0wsS0FBSyxDQUFDO0lBQUM7SUFBQWpFLGNBQUEsR0FBQW9CLENBQUE7SUFFN0MsT0FBTztNQUNMcUYsU0FBUyxFQUFFLElBQUksQ0FBQ0Msb0JBQW9CLENBQUNyQyxPQUFPLENBQUNzQyxlQUFlLENBQUN0QixJQUFvQixDQUFDO01BQ2xGdUIsS0FBSyxFQUFFMUIsS0FBSyxDQUFDQyxJQUFJLENBQUNkLE9BQU8sQ0FBQ3dDLGVBQWUsQ0FBQ3hCLElBQW9CLENBQUM7TUFDL0R5QixrQkFBa0IsRUFBRTVCLEtBQUssQ0FBQ0MsSUFBSSxDQUFDZCxPQUFPLENBQUN5QyxrQkFBa0IsQ0FBQ3pCLElBQW9CLENBQUM7TUFDL0UwQixjQUFjLEVBQUU3QixLQUFLLENBQUNDLElBQUksQ0FBQ2QsT0FBTyxDQUFDMEMsY0FBYyxDQUFDMUIsSUFBb0I7S0FDdkU7RUFDSDtFQUVBOzs7RUFHQSxNQUFNMkIsMEJBQTBCQSxDQUM5QnBDLFVBQXNCLEVBQ3RCcUMsaUJBQTJCO0lBQUE7SUFBQWpILGNBQUEsR0FBQXFCLENBQUE7SUFPM0I7SUFDQSxNQUFNNkYsZUFBZTtJQUFBO0lBQUEsQ0FBQWxILGNBQUEsR0FBQW9CLENBQUEsUUFBRyxHQUFHLEVBQUMsQ0FBQztJQUM3QixNQUFNK0YsV0FBVztJQUFBO0lBQUEsQ0FBQW5ILGNBQUEsR0FBQW9CLENBQUEsUUFBRzZGLGlCQUFpQixDQUFDRyxNQUFNLENBQUMsQ0FBQ0MsR0FBRyxFQUFFQyxXQUFXLEtBQUs7TUFBQTtNQUFBdEgsY0FBQSxHQUFBcUIsQ0FBQTtNQUFBckIsY0FBQSxHQUFBb0IsQ0FBQTtNQUFBLE9BQUFpRyxHQUFHLEdBQUdDLFdBQVc7SUFBWCxDQUFXLEVBQUUsQ0FBQyxDQUFDO0lBQ3hGLE1BQU1DLGVBQWU7SUFBQTtJQUFBLENBQUF2SCxjQUFBLEdBQUFvQixDQUFBLFFBQUcrRixXQUFXLEdBQUdELGVBQWU7SUFFckQ7SUFDQSxNQUFNTSxrQkFBa0I7SUFBQTtJQUFBLENBQUF4SCxjQUFBLEdBQUFvQixDQUFBLFFBQUcsTUFBTSxJQUFJLENBQUNxRyx3QkFBd0IsQ0FBQzdDLFVBQVUsQ0FBQztJQUUxRTtJQUNBLE1BQU04QyxtQkFBbUI7SUFBQTtJQUFBLENBQUExSCxjQUFBLEdBQUFvQixDQUFBLFFBQUcsSUFBSSxDQUFDdUcsNEJBQTRCLENBQzNEL0MsVUFBVSxFQUNWMkMsZUFBZSxFQUNmQyxrQkFBa0IsQ0FDbkI7SUFFRDtJQUNBLE1BQU1JLGVBQWU7SUFBQTtJQUFBLENBQUE1SCxjQUFBLEdBQUFvQixDQUFBLFFBQUcsSUFBSSxDQUFDeUcsb0NBQW9DLENBQy9ESCxtQkFBbUIsRUFDbkJGLGtCQUFrQixDQUNuQjtJQUFDO0lBQUF4SCxjQUFBLEdBQUFvQixDQUFBO0lBRUYsT0FBTztNQUNMMEcsZ0JBQWdCLEVBQUVQLGVBQWU7TUFDakNRLG1CQUFtQixFQUFFUCxrQkFBa0I7TUFDdkNRLG9CQUFvQixFQUFFTixtQkFBbUI7TUFDekNFO0tBQ0Q7RUFDSDtFQUVBOzs7RUFHQUssdUJBQXVCQSxDQUFDQyxNQUEwQjtJQUFBO0lBQUFsSSxjQUFBLEdBQUFxQixDQUFBO0lBS2hELE1BQU04RyxRQUFRO0lBQUE7SUFBQSxDQUFBbkksY0FBQSxHQUFBb0IsQ0FBQSxRQUFhLEVBQUU7SUFDN0IsTUFBTWdILGVBQWU7SUFBQTtJQUFBLENBQUFwSSxjQUFBLEdBQUFvQixDQUFBLFFBQWEsRUFBRTtJQUNwQyxNQUFNaUgsZUFBZTtJQUFBO0lBQUEsQ0FBQXJJLGNBQUEsR0FBQW9CLENBQUEsUUFBYSxFQUFFO0lBRXBDO0lBQUE7SUFBQXBCLGNBQUEsR0FBQW9CLENBQUE7SUFDQSxJQUFJOEcsTUFBTSxDQUFDSSxpQkFBaUIsQ0FBQ0MsTUFBTSxHQUFHLEVBQUUsRUFBRTtNQUFBO01BQUF2SSxjQUFBLEdBQUFzQixDQUFBO01BQUF0QixjQUFBLEdBQUFvQixDQUFBO01BQ3hDK0csUUFBUSxDQUFDSyxJQUFJLENBQUMseUNBQXlDTixNQUFNLENBQUNJLGlCQUFpQixDQUFDQyxNQUFNLENBQUNFLE9BQU8sQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDO0lBQ3ZHLENBQUM7SUFBQTtJQUFBO01BQUF6SSxjQUFBLEdBQUFzQixDQUFBO0lBQUE7SUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7SUFFRCxJQUFJOEcsTUFBTSxDQUFDSSxpQkFBaUIsQ0FBQ0ksSUFBSSxHQUFHLElBQUksRUFBRTtNQUFBO01BQUExSSxjQUFBLEdBQUFzQixDQUFBO01BQUF0QixjQUFBLEdBQUFvQixDQUFBO01BQ3hDK0csUUFBUSxDQUFDSyxJQUFJLENBQUMseUJBQXlCTixNQUFNLENBQUNJLGlCQUFpQixDQUFDSSxJQUFJLENBQUNDLGNBQWMsRUFBRSxFQUFFLENBQUM7SUFDMUYsQ0FBQztJQUFBO0lBQUE7TUFBQTNJLGNBQUEsR0FBQXNCLENBQUE7SUFBQTtJQUVEO0lBQ0EsTUFBTXNILGlCQUFpQjtJQUFBO0lBQUEsQ0FBQTVJLGNBQUEsR0FBQW9CLENBQUEsU0FBRzhHLE1BQU0sQ0FBQ1csbUJBQW1CLENBQ2pEMUMsTUFBTSxDQUFDMkMsSUFBSSxJQUFJO01BQUE7TUFBQTlJLGNBQUEsR0FBQXFCLENBQUE7TUFBQXJCLGNBQUEsR0FBQW9CLENBQUE7TUFBQSxPQUFBMEgsSUFBSSxDQUFDQyxRQUFRLEtBQUssTUFBTTtJQUFOLENBQU0sQ0FBQyxDQUN4Q0MsSUFBSSxDQUFDLENBQUNDLENBQUMsRUFBRTNILENBQUMsS0FBSztNQUFBO01BQUF0QixjQUFBLEdBQUFxQixDQUFBO01BQUFyQixjQUFBLEdBQUFvQixDQUFBO01BQUEsT0FBQUUsQ0FBQyxDQUFDNEgsZUFBZSxHQUFHRCxDQUFDLENBQUNDLGVBQWU7SUFBZixDQUFlLENBQUM7SUFBQztJQUFBbEosY0FBQSxHQUFBb0IsQ0FBQTtJQUV6RGdILGVBQWUsQ0FBQ0ksSUFBSSxDQUFDLEdBQUdJLGlCQUFpQixDQUFDTyxLQUFLLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDbkQsR0FBRyxDQUFDOEMsSUFBSSxJQUFJO01BQUE7TUFBQTlJLGNBQUEsR0FBQXFCLENBQUE7TUFBQXJCLGNBQUEsR0FBQW9CLENBQUE7TUFBQSxPQUFBMEgsSUFBSSxDQUFDTSxXQUFXO0lBQVgsQ0FBVyxDQUFDLENBQUM7SUFFcEY7SUFDQSxNQUFNQyxlQUFlO0lBQUE7SUFBQSxDQUFBckosY0FBQSxHQUFBb0IsQ0FBQSxTQUFHOEcsTUFBTSxDQUFDb0IsZUFBZSxDQUMzQ25ELE1BQU0sQ0FBQ29ELElBQUksSUFBSTtNQUFBO01BQUF2SixjQUFBLEdBQUFxQixDQUFBO01BQUFyQixjQUFBLEdBQUFvQixDQUFBO01BQUEsT0FBQW1JLElBQUksQ0FBQ0MsV0FBVyxHQUFHRCxJQUFJLENBQUNFLE1BQU0sR0FBRyxHQUFHO0lBQUgsQ0FBRyxDQUFDLENBQ3BEVCxJQUFJLENBQUMsQ0FBQ0MsQ0FBQyxFQUFFM0gsQ0FBQyxLQUFLO01BQUE7TUFBQXRCLGNBQUEsR0FBQXFCLENBQUE7TUFBQXJCLGNBQUEsR0FBQW9CLENBQUE7TUFBQSxPQUFDRSxDQUFDLENBQUNrSSxXQUFXLEdBQUdsSSxDQUFDLENBQUNtSSxNQUFNLEdBQUtSLENBQUMsQ0FBQ08sV0FBVyxHQUFHUCxDQUFDLENBQUNRLE1BQU87SUFBRCxDQUFDLENBQUM7SUFBQztJQUFBekosY0FBQSxHQUFBb0IsQ0FBQTtJQUUzRWlILGVBQWUsQ0FBQ0csSUFBSSxDQUFDLEdBQUdhLGVBQWUsQ0FBQ0YsS0FBSyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQ25ELEdBQUcsQ0FBQ3VELElBQUksSUFBSTtNQUFBO01BQUF2SixjQUFBLEdBQUFxQixDQUFBO01BQUFyQixjQUFBLEdBQUFvQixDQUFBO01BQUEsT0FBQW1JLElBQUksQ0FBQ0gsV0FBVztJQUFYLENBQVcsQ0FBQyxDQUFDO0lBQUM7SUFBQXBKLGNBQUEsR0FBQW9CLENBQUE7SUFFbkYsT0FBTztNQUNMc0ksWUFBWSxFQUFFdkIsUUFBUTtNQUN0QndCLGdCQUFnQixFQUFFdkIsZUFBZTtNQUNqQ3dCLGdCQUFnQixFQUFFdkI7S0FDbkI7RUFDSDtFQUVRLE1BQU16RSxxQkFBcUJBLENBQUE7SUFBQTtJQUFBNUQsY0FBQSxHQUFBcUIsQ0FBQTtJQUNqQyxNQUFNd0ksTUFBTTtJQUFBO0lBQUEsQ0FBQTdKLGNBQUEsR0FBQW9CLENBQUEsU0FBRyxDQUNiO01BQUVQLElBQUksRUFBRSxtQkFBbUI7TUFBRVosSUFBSSxFQUFFO0lBQWdDLENBQUUsRUFDckU7TUFBRVksSUFBSSxFQUFFLG1CQUFtQjtNQUFFWixJQUFJLEVBQUU7SUFBZ0MsQ0FBRSxFQUNyRTtNQUFFWSxJQUFJLEVBQUUsa0JBQWtCO01BQUVaLElBQUksRUFBRTtJQUErQixDQUFFLEVBQ25FO01BQUVZLElBQUksRUFBRSxrQkFBa0I7TUFBRVosSUFBSSxFQUFFO0lBQStCLENBQUUsQ0FDcEU7SUFBQztJQUFBRCxjQUFBLEdBQUFvQixDQUFBO0lBRUYsS0FBSyxNQUFNMEksS0FBSyxJQUFJRCxNQUFNLEVBQUU7TUFBQTtNQUFBN0osY0FBQSxHQUFBb0IsQ0FBQTtNQUMxQixJQUFJO1FBQ0YsTUFBTXVCLE9BQU87UUFBQTtRQUFBLENBQUEzQyxjQUFBLEdBQUFvQixDQUFBLFNBQUcsTUFBTWtCLEdBQUcsQ0FBQ21CLGdCQUFnQixDQUFDQyxNQUFNLENBQUNvRyxLQUFLLENBQUM3SixJQUFJLENBQUM7UUFBQztRQUFBRCxjQUFBLEdBQUFvQixDQUFBO1FBQzlELElBQUksQ0FBQ3dCLFVBQVUsQ0FBQ21ILEdBQUcsQ0FBQ0QsS0FBSyxDQUFDakosSUFBSSxFQUFFOEIsT0FBTyxDQUFDO1FBQUM7UUFBQTNDLGNBQUEsR0FBQW9CLENBQUE7UUFDekN5QyxPQUFPLENBQUNDLEdBQUcsQ0FBQyxVQUFVZ0csS0FBSyxDQUFDakosSUFBSSxRQUFRLENBQUM7TUFDM0MsQ0FBQyxDQUFDLE9BQU9rRCxLQUFLLEVBQUU7UUFBQTtRQUFBL0QsY0FBQSxHQUFBb0IsQ0FBQTtRQUNkeUMsT0FBTyxDQUFDbUcsSUFBSSxDQUFDLGtCQUFrQkYsS0FBSyxDQUFDakosSUFBSSxTQUFTLEVBQUVrRCxLQUFLLENBQUM7TUFDNUQ7SUFDRjtFQUNGO0VBRVEsTUFBTUssaUJBQWlCQSxDQUFDSCxLQUF3QjtJQUFBO0lBQUFqRSxjQUFBLEdBQUFxQixDQUFBO0lBQ3REO0lBQ0EsTUFBTTRJLFFBQVE7SUFBQTtJQUFBLENBQUFqSyxjQUFBLEdBQUFvQixDQUFBLFNBQUc7SUFDZjtJQUNBNkMsS0FBSyxDQUFDb0MsWUFBWSxDQUFDNkQsSUFBSSxHQUFHLEtBQUs7SUFBRTtJQUNqQ2pHLEtBQUssQ0FBQ29DLFlBQVksQ0FBQzhELE1BQU0sR0FBRyxNQUFNO0lBQUU7SUFDcENsRyxLQUFLLENBQUNvQyxZQUFZLENBQUMrRCxTQUFTLEdBQUcsSUFBSTtJQUFFO0lBQ3JDbkcsS0FBSyxDQUFDb0MsWUFBWSxDQUFDZ0UsVUFBVSxHQUFHLEVBQUU7SUFBRTtJQUNwQ3BHLEtBQUssQ0FBQ29DLFlBQVksQ0FBQ2lFLE1BQU0sR0FBRyxFQUFFO0lBQUU7SUFDaENyRyxLQUFLLENBQUNvQyxZQUFZLENBQUNrRSxTQUFTLEdBQUcsRUFBRTtJQUFFO0lBRW5DO0lBQ0EsR0FBR3RHLEtBQUssQ0FBQ1ksaUJBQWlCLENBQUMyRixrQkFBa0IsQ0FBQ3JCLEtBQUssQ0FBQyxDQUFDLEVBQUUsRUFBRSxDQUFDLENBQUNuRCxHQUFHLENBQUN5RSxDQUFDLElBQUk7TUFBQTtNQUFBekssY0FBQSxHQUFBcUIsQ0FBQTtNQUFBckIsY0FBQSxHQUFBb0IsQ0FBQTtNQUFBLE9BQUFxSixDQUFDLEdBQUcsR0FBRztJQUFILENBQUcsQ0FBQyxFQUM1RSxHQUFHeEcsS0FBSyxDQUFDWSxpQkFBaUIsQ0FBQzZGLFFBQVEsQ0FBQ3ZCLEtBQUssQ0FBQyxDQUFDLEVBQUUsRUFBRSxDQUFDLENBQUNuRCxHQUFHLENBQUMyRSxDQUFDLElBQUk7TUFBQTtNQUFBM0ssY0FBQSxHQUFBcUIsQ0FBQTtNQUFBckIsY0FBQSxHQUFBb0IsQ0FBQTtNQUFBLE9BQUF1SixDQUFDLEdBQUcsR0FBRztJQUFILENBQUcsQ0FBQztJQUVsRTtJQUNBMUcsS0FBSyxDQUFDMkcsZUFBZSxDQUFDQyxXQUFXLEdBQUcsSUFBSTtJQUFFO0lBQzFDNUcsS0FBSyxDQUFDMkcsZUFBZSxDQUFDRSxrQkFBa0IsQ0FBQ0MsR0FBRyxHQUFHLEVBQUUsRUFDakQ5RyxLQUFLLENBQUMyRyxlQUFlLENBQUNFLGtCQUFrQixDQUFDRSxHQUFHLEdBQUcsRUFBRSxFQUNqRC9HLEtBQUssQ0FBQzJHLGVBQWUsQ0FBQ0Usa0JBQWtCLENBQUNHLG9CQUFvQjtJQUU3RDtJQUNBaEgsS0FBSyxDQUFDaUgsV0FBVyxDQUFDQyxNQUFNLEdBQUcsTUFBTTtJQUFFO0lBQ25DbEgsS0FBSyxDQUFDaUgsV0FBVyxDQUFDRSxRQUFRLEdBQUcsR0FBRyxDQUFFO0lBQUEsQ0FDbkM7SUFFRDtJQUNBLE1BQU1DLFNBQVM7SUFBQTtJQUFBLENBQUFyTCxjQUFBLEdBQUFvQixDQUFBLFNBQUcsR0FBRyxFQUFDLENBQUM7SUFDdkIsTUFBTWtLLGNBQWM7SUFBQTtJQUFBLENBQUF0TCxjQUFBLEdBQUFvQixDQUFBLFNBQUc2SSxRQUFRLENBQUNkLEtBQUssQ0FBQyxDQUFDLEVBQUVrQyxTQUFTLENBQUM7SUFBQztJQUFBckwsY0FBQSxHQUFBb0IsQ0FBQTtJQUNwRCxPQUFPa0ssY0FBYyxDQUFDQyxNQUFNLEdBQUdGLFNBQVMsRUFBRTtNQUFBO01BQUFyTCxjQUFBLEdBQUFvQixDQUFBO01BQ3hDa0ssY0FBYyxDQUFDOUMsSUFBSSxDQUFDLENBQUMsQ0FBQztJQUN4QjtJQUFDO0lBQUF4SSxjQUFBLEdBQUFvQixDQUFBO0lBRUQsT0FBTztNQUNMNkMsS0FBSyxFQUFFLElBQUkzQixHQUFHLENBQUNrSixNQUFNLENBQUMsU0FBUyxFQUFFLElBQUlDLFlBQVksQ0FBQ0gsY0FBYyxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUVELFNBQVMsQ0FBQztLQUNsRjtFQUNIO0VBRVEsTUFBTTdHLGNBQWNBLENBQUNILE9BQThDLEVBQUVKLEtBQXdCO0lBQUE7SUFBQWpFLGNBQUEsR0FBQXFCLENBQUE7SUFDbkc7SUFDQSxNQUFNcUssVUFBVTtJQUFBO0lBQUEsQ0FBQTFMLGNBQUEsR0FBQW9CLENBQUEsU0FBR2lELE9BQU8sQ0FBQ3NILE1BQU0sQ0FBQ3RHLElBQW9CO0lBRXREO0lBQ0EsTUFBTXVDLGVBQWU7SUFBQTtJQUFBLENBQUE1SCxjQUFBLEdBQUFvQixDQUFBLFNBQUcsSUFBSSxDQUFDd0ssc0JBQXNCLENBQUNGLFVBQVUsRUFBRXpILEtBQUssQ0FBQztJQUN0RSxNQUFNNEgsT0FBTztJQUFBO0lBQUEsQ0FBQTdMLGNBQUEsR0FBQW9CLENBQUEsU0FBRyxJQUFJLENBQUMwSyxnQkFBZ0IsQ0FBQ0osVUFBVSxFQUFFekgsS0FBSyxDQUFDO0lBQ3hELE1BQU04SCxrQkFBa0I7SUFBQTtJQUFBLENBQUEvTCxjQUFBLEdBQUFvQixDQUFBLFNBQUcsSUFBSSxDQUFDNEssMEJBQTBCLENBQUNwRSxlQUFlLEVBQUUzRCxLQUFLLENBQUM7SUFDbEYsTUFBTWdJLFdBQVc7SUFBQTtJQUFBLENBQUFqTSxjQUFBLEdBQUFvQixDQUFBLFNBQUcsSUFBSSxDQUFDOEssWUFBWSxDQUFDTCxPQUFPLEVBQUVFLGtCQUFrQixDQUFDO0lBQ2xFLE1BQU1JLGNBQWM7SUFBQTtJQUFBLENBQUFuTSxjQUFBLEdBQUFvQixDQUFBLFNBQUcsSUFBSSxDQUFDZ0wsV0FBVyxDQUFDeEUsZUFBZSxFQUFFM0QsS0FBSyxDQUFDO0lBQUM7SUFBQWpFLGNBQUEsR0FBQW9CLENBQUE7SUFFaEUsT0FBTztNQUNMd0csZUFBZTtNQUNmVSxpQkFBaUIsRUFBRXVELE9BQU87TUFDMUJRLGdCQUFnQixFQUFFQyxJQUFJLENBQUNDLEdBQUcsQ0FBQ2IsVUFBVSxDQUFDLENBQUMsQ0FBQyxFQUFFLEdBQUcsQ0FBQztNQUM5QzdDLG1CQUFtQixFQUFFa0Qsa0JBQWtCO01BQ3ZDUyxZQUFZLEVBQUVQLFdBQVc7TUFDekIzQyxlQUFlLEVBQUU2QztLQUNsQjtFQUNIO0VBRVFQLHNCQUFzQkEsQ0FBQ0YsVUFBd0IsRUFBRXpILEtBQXdCO0lBQUE7SUFBQWpFLGNBQUEsR0FBQXFCLENBQUE7SUFDL0U7SUFDQSxNQUFNdUcsZUFBZTtJQUFBO0lBQUEsQ0FBQTVILGNBQUEsR0FBQW9CLENBQUEsU0FBaUMsRUFBRTtJQUV4RDtJQUFBO0lBQUFwQixjQUFBLEdBQUFvQixDQUFBO0lBQ0EsSUFBSXNLLFVBQVUsQ0FBQyxDQUFDLENBQUMsR0FBRyxHQUFHLEVBQUU7TUFBQTtNQUFBMUwsY0FBQSxHQUFBc0IsQ0FBQTtNQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtNQUN2QndHLGVBQWUsQ0FBQ1ksSUFBSSxDQUFDO1FBQ25CaUUsRUFBRSxFQUFFLGNBQWM7UUFDbEJ4TCxJQUFJLEVBQUUsbUJBQW1CO1FBQ3pCbUksV0FBVyxFQUFFLDZFQUE2RTtRQUMxRkwsUUFBUSxFQUFFLE1BQU07UUFDaEIyRCxtQkFBbUIsRUFBRWhCLFVBQVUsQ0FBQyxDQUFDLENBQUMsR0FBRyxLQUFLO1FBQzFDaUIsY0FBYyxFQUFFakIsVUFBVSxDQUFDLENBQUMsQ0FBQyxHQUFHLEdBQUc7UUFDbkNrQixjQUFjLEVBQUVsQixVQUFVLENBQUMsQ0FBQyxDQUFDLEdBQUcsRUFBRTtRQUNsQzVGLFVBQVUsRUFBRSxJQUFJO1FBQ2hCK0csWUFBWSxFQUFFLENBQUMsbUJBQW1CLEVBQUUsZ0JBQWdCO09BQ3JELENBQUM7SUFDSixDQUFDO0lBQUE7SUFBQTtNQUFBN00sY0FBQSxHQUFBc0IsQ0FBQTtJQUFBO0lBQUF0QixjQUFBLEdBQUFvQixDQUFBO0lBRUQsSUFBSXNLLFVBQVUsQ0FBQyxDQUFDLENBQUMsR0FBRyxHQUFHLEVBQUU7TUFBQTtNQUFBMUwsY0FBQSxHQUFBc0IsQ0FBQTtNQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtNQUN2QndHLGVBQWUsQ0FBQ1ksSUFBSSxDQUFDO1FBQ25CaUUsRUFBRSxFQUFFLG1CQUFtQjtRQUN2QnhMLElBQUksRUFBRSxrQkFBa0I7UUFDeEJtSSxXQUFXLEVBQUUsb0VBQW9FO1FBQ2pGTCxRQUFRLEVBQUUsUUFBUTtRQUNsQjJELG1CQUFtQixFQUFFaEIsVUFBVSxDQUFDLENBQUMsQ0FBQyxHQUFHLElBQUk7UUFDekNpQixjQUFjLEVBQUVqQixVQUFVLENBQUMsQ0FBQyxDQUFDLEdBQUcsR0FBRztRQUNuQ2tCLGNBQWMsRUFBRWxCLFVBQVUsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDO1FBQ2pDNUYsVUFBVSxFQUFFLElBQUk7UUFDaEIrRyxZQUFZLEVBQUUsQ0FBQyxtQkFBbUIsRUFBRSxTQUFTO09BQzlDLENBQUM7SUFDSixDQUFDO0lBQUE7SUFBQTtNQUFBN00sY0FBQSxHQUFBc0IsQ0FBQTtJQUFBO0lBQUF0QixjQUFBLEdBQUFvQixDQUFBO0lBRUQsT0FBT3dHLGVBQWU7RUFDeEI7RUFFUWtFLGdCQUFnQkEsQ0FBQ0osVUFBd0IsRUFBRXpILEtBQXdCO0lBQUE7SUFBQWpFLGNBQUEsR0FBQXFCLENBQUE7SUFDekUsTUFBTXlMLGlCQUFpQjtJQUFBO0lBQUEsQ0FBQTlNLGNBQUEsR0FBQW9CLENBQUEsU0FBRzZDLEtBQUssQ0FBQzJHLGVBQWUsQ0FBQzNELGlCQUFpQixDQUFDRyxNQUFNLENBQUMsQ0FBQ0MsR0FBRyxFQUFFMEYsQ0FBQyxLQUFLO01BQUE7TUFBQS9NLGNBQUEsR0FBQXFCLENBQUE7TUFBQXJCLGNBQUEsR0FBQW9CLENBQUE7TUFBQSxPQUFBaUcsR0FBRyxHQUFHMEYsQ0FBQztJQUFELENBQUMsRUFBRSxDQUFDLENBQUMsR0FBRyxJQUFJLEVBQUMsQ0FBQztJQUFBO0lBQUEvTSxjQUFBLEdBQUFvQixDQUFBO0lBRXpHLE9BQU87TUFDTG1ILE1BQU0sRUFBRW1ELFVBQVUsQ0FBQyxFQUFFLENBQUMsR0FBRyxHQUFHO01BQUU7TUFDOUJoRCxJQUFJLEVBQUVvRSxpQkFBaUIsR0FBR3BCLFVBQVUsQ0FBQyxFQUFFLENBQUM7TUFBRTtNQUMxQ3NCLFNBQVMsRUFBRUYsaUJBQWlCLEdBQUdwQixVQUFVLENBQUMsRUFBRSxDQUFDLEdBQUcsR0FBRyxHQUFHLElBQUksQ0FBQztLQUM1RDtFQUNIO0VBRVFNLDBCQUEwQkEsQ0FBQ3BFLGVBQTZDLEVBQUUzRCxLQUF3QjtJQUFBO0lBQUFqRSxjQUFBLEdBQUFxQixDQUFBO0lBQUFyQixjQUFBLEdBQUFvQixDQUFBO0lBQ3hHLE9BQU93RyxlQUFlLENBQUM1QixHQUFHLENBQUMsQ0FBQ2lILEdBQUcsRUFBRS9HLEtBQUssS0FBTTtNQUFBO01BQUFsRyxjQUFBLEdBQUFxQixDQUFBO01BQUFyQixjQUFBLEdBQUFvQixDQUFBO01BQUE7UUFDMUNxTCxFQUFFLEVBQUVRLEdBQUcsQ0FBQ1IsRUFBRTtRQUNWckQsV0FBVyxFQUFFNkQsR0FBRyxDQUFDN0QsV0FBVztRQUM1QkwsUUFBUSxFQUFFa0UsR0FBRyxDQUFDbEUsUUFBUTtRQUN0Qm1FLGNBQWMsRUFBRUQsR0FBRyxDQUFDUCxtQkFBbUI7UUFDdkNTLGtCQUFrQixFQUFFRixHQUFHLENBQUNMLGNBQWMsR0FBRyxFQUFFO1FBQUU7UUFDN0NRLFlBQVksRUFBRWxILEtBQUssR0FBRyxDQUFDO1FBQUE7UUFBQSxDQUFBbEcsY0FBQSxHQUFBc0IsQ0FBQSxXQUFHLENBQUNzRyxlQUFlLENBQUMxQixLQUFLLEdBQUcsQ0FBQyxDQUFDLENBQUN1RyxFQUFFLENBQUM7UUFBQTtRQUFBLENBQUF6TSxjQUFBLEdBQUFzQixDQUFBLFdBQUcsRUFBRTtRQUM5RDRILGVBQWUsRUFBRStELEdBQUcsQ0FBQ047T0FDdEI7S0FBQyxDQUFDO0VBQ0w7RUFFUVQsWUFBWUEsQ0FBQ0wsT0FBWSxFQUFFd0IsSUFBMEI7SUFBQTtJQUFBck4sY0FBQSxHQUFBcUIsQ0FBQTtJQUMzRCxNQUFNaU0sZUFBZTtJQUFBO0lBQUEsQ0FBQXROLGNBQUEsR0FBQW9CLENBQUEsU0FBR2lNLElBQUksQ0FBQ2pHLE1BQU0sQ0FBQyxDQUFDQyxHQUFHLEVBQUV5QixJQUFJLEtBQUs7TUFBQTtNQUFBOUksY0FBQSxHQUFBcUIsQ0FBQTtNQUFBckIsY0FBQSxHQUFBb0IsQ0FBQTtNQUFBLE9BQUFpRyxHQUFHLEdBQUd5QixJQUFJLENBQUNvRSxjQUFjO0lBQWQsQ0FBYyxFQUFFLENBQUMsQ0FBQztJQUNoRixNQUFNSyxhQUFhO0lBQUE7SUFBQSxDQUFBdk4sY0FBQSxHQUFBb0IsQ0FBQSxTQUFHeUssT0FBTyxDQUFDbkQsSUFBSTtJQUFDO0lBQUExSSxjQUFBLEdBQUFvQixDQUFBO0lBRW5DLE9BQU87TUFDTG9NLGtCQUFrQixFQUFFRixlQUFlO01BQ25DWCxjQUFjLEVBQUVZLGFBQWE7TUFDN0JYLGNBQWMsRUFBRVUsZUFBZSxHQUFHQyxhQUFhO01BQy9DRSxpQkFBaUIsRUFBRSxJQUFJLENBQUNDLFlBQVksQ0FBQ0osZUFBZSxFQUFFQyxhQUFhLEVBQUUsSUFBSSxFQUFFLEVBQUUsQ0FBQztNQUM5RUksdUJBQXVCLEVBQUUsSUFBSSxDQUFDQyxZQUFZLENBQUNOLGVBQWUsRUFBRUMsYUFBYSxFQUFFLEVBQUU7S0FDOUU7RUFDSDtFQUVRbkIsV0FBV0EsQ0FBQ3hFLGVBQTZDLEVBQUUzRCxLQUF3QjtJQUFBO0lBQUFqRSxjQUFBLEdBQUFxQixDQUFBO0lBQ3pGLE1BQU13TSxLQUFLO0lBQUE7SUFBQSxDQUFBN04sY0FBQSxHQUFBb0IsQ0FBQSxTQUFpQixFQUFFO0lBRTlCO0lBQ0EsTUFBTTBNLFNBQVM7SUFBQTtJQUFBLENBQUE5TixjQUFBLEdBQUFvQixDQUFBLFNBQUd3RyxlQUFlLENBQUNSLE1BQU0sQ0FBQyxDQUFDQyxHQUFHLEVBQUU0RixHQUFHLEtBQUs7TUFBQTtNQUFBak4sY0FBQSxHQUFBcUIsQ0FBQTtNQUFBckIsY0FBQSxHQUFBb0IsQ0FBQTtNQUFBLE9BQUFpRyxHQUFHLEdBQUc0RixHQUFHLENBQUNQLG1CQUFtQjtJQUFuQixDQUFtQixFQUFFLENBQUMsQ0FBQztJQUFDO0lBQUExTSxjQUFBLEdBQUFvQixDQUFBO0lBQ3pGLElBQUkwTSxTQUFTLEdBQUc3SixLQUFLLENBQUNpSCxXQUFXLENBQUNDLE1BQU0sR0FBRyxHQUFHLEVBQUU7TUFBQTtNQUFBbkwsY0FBQSxHQUFBc0IsQ0FBQTtNQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtNQUM5Q3lNLEtBQUssQ0FBQ3JGLElBQUksQ0FBQztRQUNUdkgsSUFBSSxFQUFFLFFBQVE7UUFDZG1JLFdBQVcsRUFBRSw2Q0FBNkM7UUFDMURJLFdBQVcsRUFBRSxHQUFHO1FBQ2hCQyxNQUFNLEVBQUUsR0FBRztRQUNYc0UsVUFBVSxFQUFFO09BQ2IsQ0FBQztJQUNKLENBQUM7SUFBQTtJQUFBO01BQUEvTixjQUFBLEdBQUFzQixDQUFBO0lBQUE7SUFFRDtJQUNBLE1BQU0wTSxhQUFhO0lBQUE7SUFBQSxDQUFBaE8sY0FBQSxHQUFBb0IsQ0FBQSxTQUFHd0csZUFBZSxDQUFDMkQsTUFBTSxHQUFHLEVBQUUsRUFBQyxDQUFDO0lBQUE7SUFBQXZMLGNBQUEsR0FBQW9CLENBQUE7SUFDbkQsSUFBSTRNLGFBQWEsR0FBRy9KLEtBQUssQ0FBQ2lILFdBQVcsQ0FBQ0UsUUFBUSxHQUFHLEdBQUcsRUFBRTtNQUFBO01BQUFwTCxjQUFBLEdBQUFzQixDQUFBO01BQUF0QixjQUFBLEdBQUFvQixDQUFBO01BQ3BEeU0sS0FBSyxDQUFDckYsSUFBSSxDQUFDO1FBQ1R2SCxJQUFJLEVBQUUsVUFBVTtRQUNoQm1JLFdBQVcsRUFBRSxnREFBZ0Q7UUFDN0RJLFdBQVcsRUFBRSxHQUFHO1FBQ2hCQyxNQUFNLEVBQUUsR0FBRztRQUNYc0UsVUFBVSxFQUFFO09BQ2IsQ0FBQztJQUNKLENBQUM7SUFBQTtJQUFBO01BQUEvTixjQUFBLEdBQUFzQixDQUFBO0lBQUE7SUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7SUFFRCxPQUFPeU0sS0FBSztFQUNkO0VBRVE1SSxrQkFBa0JBLENBQUNMLFVBQXNCLEVBQUVDLGlCQUFvQyxFQUFFQyxXQUFtQjtJQUFBO0lBQUE5RSxjQUFBLEdBQUFxQixDQUFBO0lBQzFHO0lBQ0EsTUFBTTRJLFFBQVE7SUFBQTtJQUFBLENBQUFqSyxjQUFBLEdBQUFvQixDQUFBLFNBQUcsQ0FDZixHQUFHeUQsaUJBQWlCLENBQUMyRixrQkFBa0IsQ0FBQ3JCLEtBQUssQ0FBQyxDQUFDLEVBQUVyRSxXQUFXLENBQUMsRUFDN0QsR0FBR0QsaUJBQWlCLENBQUM2RixRQUFRLENBQUN2QixLQUFLLENBQUMsQ0FBQyxFQUFFckUsV0FBVztJQUNsRDtJQUFBLENBQ0Q7SUFBQztJQUFBOUUsY0FBQSxHQUFBb0IsQ0FBQTtJQUVGLE9BQU87TUFDTDZDLEtBQUssRUFBRSxJQUFJM0IsR0FBRyxDQUFDa0osTUFBTSxDQUFDLFNBQVMsRUFBRSxJQUFJQyxZQUFZLENBQUN4QixRQUFRLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRUEsUUFBUSxDQUFDc0IsTUFBTSxDQUFDO0tBQ2xGO0VBQ0g7RUFFUTdGLG1CQUFtQkEsQ0FBQ0gsZUFBcUM7SUFBQTtJQUFBdkYsY0FBQSxHQUFBcUIsQ0FBQTtJQUMvRCxNQUFNNEksUUFBUTtJQUFBO0lBQUEsQ0FBQWpLLGNBQUEsR0FBQW9CLENBQUEsU0FBR21FLGVBQWUsQ0FBQzBJLE9BQU8sQ0FBQzVJLElBQUksSUFBSTtNQUFBO01BQUFyRixjQUFBLEdBQUFxQixDQUFBO01BQUFyQixjQUFBLEdBQUFvQixDQUFBO01BQUEsUUFDL0NpRSxJQUFJLENBQUMwRixHQUFHLEVBQ1IxRixJQUFJLENBQUMyRixHQUFHLEVBQ1IzRixJQUFJLENBQUM0RixvQkFBb0IsRUFDekI1RixJQUFJLENBQUM2SSxvQkFBb0IsQ0FDMUI7S0FBQSxDQUFDO0lBQUM7SUFBQWxPLGNBQUEsR0FBQW9CLENBQUE7SUFFSCxPQUFPO01BQ0w2QyxLQUFLLEVBQUUsSUFBSTNCLEdBQUcsQ0FBQ2tKLE1BQU0sQ0FBQyxTQUFTLEVBQUUsSUFBSUMsWUFBWSxDQUFDeEIsUUFBUSxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUVBLFFBQVEsQ0FBQ3NCLE1BQU0sQ0FBQztLQUNsRjtFQUNIO0VBRVEvRSxrQkFBa0JBLENBQUNILFlBQTBCLEVBQUVDLGdCQUEwQjtJQUFBO0lBQUF0RyxjQUFBLEdBQUFxQixDQUFBO0lBQy9FLE1BQU00SSxRQUFRO0lBQUE7SUFBQSxDQUFBakssY0FBQSxHQUFBb0IsQ0FBQSxTQUFHLENBQ2ZpRixZQUFZLENBQUM2RCxJQUFJLEVBQ2pCN0QsWUFBWSxDQUFDOEQsTUFBTSxFQUNuQjlELFlBQVksQ0FBQytELFNBQVMsRUFDdEIsR0FBRzlELGdCQUFnQixDQUFDNkMsS0FBSyxDQUFDLENBQUMsRUFBRSxFQUFFLENBQUMsQ0FDakM7SUFBQztJQUFBbkosY0FBQSxHQUFBb0IsQ0FBQTtJQUVGLE9BQU87TUFDTDZDLEtBQUssRUFBRSxJQUFJM0IsR0FBRyxDQUFDa0osTUFBTSxDQUFDLFNBQVMsRUFBRSxJQUFJQyxZQUFZLENBQUN4QixRQUFRLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRUEsUUFBUSxDQUFDc0IsTUFBTSxDQUFDO0tBQ2xGO0VBQ0g7RUFFUTdFLG9CQUFvQkEsQ0FBQ3JCLElBQWtCO0lBQUE7SUFBQXJGLGNBQUEsR0FBQXFCLENBQUE7SUFDN0M7SUFDQSxNQUFNOE0sY0FBYztJQUFBO0lBQUEsQ0FBQW5PLGNBQUEsR0FBQW9CLENBQUEsU0FBRyxDQUFDLFdBQVcsRUFBRSxTQUFTLEVBQUUsUUFBUSxFQUFFLGFBQWEsRUFBRSxVQUFVLENBQUM7SUFBQztJQUFBcEIsY0FBQSxHQUFBb0IsQ0FBQTtJQUNyRixPQUFPOEQsS0FBSyxDQUFDQyxJQUFJLENBQUNFLElBQUksQ0FBQyxDQUFDVyxHQUFHLENBQUNFLEtBQUssSUFBSTtNQUFBO01BQUFsRyxjQUFBLEdBQUFxQixDQUFBO01BQUFyQixjQUFBLEdBQUFvQixDQUFBO01BQUEsa0NBQUFwQixjQUFBLEdBQUFzQixDQUFBLFdBQUE2TSxjQUFjLENBQUM3QixJQUFJLENBQUM4QixLQUFLLENBQUNsSSxLQUFLLENBQUMsQ0FBQztNQUFBO01BQUEsQ0FBQWxHLGNBQUEsR0FBQXNCLENBQUEsV0FBSSxTQUFTO0lBQVQsQ0FBUyxDQUFDO0VBQ3RGO0VBRVEsTUFBTW1HLHdCQUF3QkEsQ0FBQzdDLFVBQXNCO0lBQUE7SUFBQTVFLGNBQUEsR0FBQXFCLENBQUE7SUFBQXJCLGNBQUEsR0FBQW9CLENBQUE7SUFDM0Q7SUFDQSxPQUFPa0wsSUFBSSxDQUFDK0IsTUFBTSxFQUFFLEdBQUcsR0FBRyxHQUFHLEdBQUcsQ0FBQyxDQUFDO0VBQ3BDO0VBRVExRyw0QkFBNEJBLENBQUMvQyxVQUFzQixFQUFFMkMsZUFBdUIsRUFBRUMsa0JBQTBCO0lBQUE7SUFBQXhILGNBQUEsR0FBQXFCLENBQUE7SUFDOUc7SUFDQSxNQUFNaU4sZUFBZTtJQUFBO0lBQUEsQ0FBQXRPLGNBQUEsR0FBQW9CLENBQUEsU0FBR2tMLElBQUksQ0FBQ0MsR0FBRztJQUFDO0lBQUEsQ0FBQXZNLGNBQUEsR0FBQXNCLENBQUEsV0FBQXNELFVBQVUsQ0FBQzJKLFVBQVU7SUFBQTtJQUFBLENBQUF2TyxjQUFBLEdBQUFzQixDQUFBLFdBQUksR0FBRyxHQUFFLEdBQUcsQ0FBQztJQUNuRSxNQUFNa04sV0FBVztJQUFBO0lBQUEsQ0FBQXhPLGNBQUEsR0FBQW9CLENBQUEsU0FBR2tMLElBQUksQ0FBQ21DLEdBQUcsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxHQUFHbEgsZUFBZSxHQUFHLEtBQUssQ0FBQztJQUM1RCxNQUFNbUgsY0FBYztJQUFBO0lBQUEsQ0FBQTFPLGNBQUEsR0FBQW9CLENBQUEsU0FBR29HLGtCQUFrQjtJQUFDO0lBQUF4SCxjQUFBLEdBQUFvQixDQUFBO0lBRTFDLE9BQU8sQ0FBQ2tOLGVBQWUsR0FBRyxHQUFHLEdBQUdFLFdBQVcsR0FBRyxHQUFHLEdBQUdFLGNBQWMsR0FBRyxHQUFHLElBQUksR0FBRztFQUNqRjtFQUVRN0csb0NBQW9DQSxDQUFDSCxtQkFBMkIsRUFBRUYsa0JBQTBCO0lBQUE7SUFBQXhILGNBQUEsR0FBQXFCLENBQUE7SUFDbEcsTUFBTXVHLGVBQWU7SUFBQTtJQUFBLENBQUE1SCxjQUFBLEdBQUFvQixDQUFBLFNBQWEsRUFBRTtJQUFDO0lBQUFwQixjQUFBLEdBQUFvQixDQUFBO0lBRXJDLElBQUlzRyxtQkFBbUIsR0FBRyxFQUFFLEVBQUU7TUFBQTtNQUFBMUgsY0FBQSxHQUFBc0IsQ0FBQTtNQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtNQUM1QndHLGVBQWUsQ0FBQ1ksSUFBSSxDQUFDLGlEQUFpRCxDQUFDO0lBQ3pFLENBQUM7SUFBQTtJQUFBO01BQUF4SSxjQUFBLEdBQUFzQixDQUFBO0lBQUE7SUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7SUFFRCxJQUFJb0csa0JBQWtCLEdBQUcsR0FBRyxFQUFFO01BQUE7TUFBQXhILGNBQUEsR0FBQXNCLENBQUE7TUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7TUFDNUJ3RyxlQUFlLENBQUNZLElBQUksQ0FBQyxpQ0FBaUMsQ0FBQztNQUFDO01BQUF4SSxjQUFBLEdBQUFvQixDQUFBO01BQ3hEd0csZUFBZSxDQUFDWSxJQUFJLENBQUMsdUNBQXVDLENBQUM7SUFDL0QsQ0FBQztJQUFBO0lBQUE7TUFBQXhJLGNBQUEsR0FBQXNCLENBQUE7SUFBQTtJQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtJQUVEd0csZUFBZSxDQUFDWSxJQUFJLENBQUMsa0RBQWtELENBQUM7SUFBQztJQUFBeEksY0FBQSxHQUFBb0IsQ0FBQTtJQUN6RXdHLGVBQWUsQ0FBQ1ksSUFBSSxDQUFDLDRDQUE0QyxDQUFDO0lBQUM7SUFBQXhJLGNBQUEsR0FBQW9CLENBQUE7SUFFbkUsT0FBT3dHLGVBQWU7RUFDeEI7RUFFUSxNQUFNbEQsdUJBQXVCQSxDQUFDd0QsTUFBMEIsRUFBRWpFLEtBQXdCO0lBQUE7SUFBQWpFLGNBQUEsR0FBQXFCLENBQUE7SUFDeEY7SUFDQSxNQUFNc04sd0JBQXdCO0lBQUE7SUFBQSxDQUFBM08sY0FBQSxHQUFBb0IsQ0FBQSxTQUFHOEcsTUFBTSxDQUFDTixlQUFlLENBQUN6QixNQUFNLENBQUM4RyxHQUFHLElBQUc7TUFBQTtNQUFBak4sY0FBQSxHQUFBcUIsQ0FBQTtNQUFBckIsY0FBQSxHQUFBb0IsQ0FBQTtNQUNuRTtNQUNBLElBQUk2TCxHQUFHLENBQUNQLG1CQUFtQixHQUFHekksS0FBSyxDQUFDaUgsV0FBVyxDQUFDQyxNQUFNLEVBQUU7UUFBQTtRQUFBbkwsY0FBQSxHQUFBc0IsQ0FBQTtRQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtRQUN0RCxPQUFPLEtBQUs7TUFDZCxDQUFDO01BQUE7TUFBQTtRQUFBcEIsY0FBQSxHQUFBc0IsQ0FBQTtNQUFBO01BRUQ7TUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7TUFDQSxJQUFJNkwsR0FBRyxDQUFDTCxjQUFjLEdBQUcsRUFBRSxHQUFHM0ksS0FBSyxDQUFDaUgsV0FBVyxDQUFDRSxRQUFRLEVBQUU7UUFBQTtRQUFBcEwsY0FBQSxHQUFBc0IsQ0FBQTtRQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtRQUN4RCxPQUFPLEtBQUs7TUFDZCxDQUFDO01BQUE7TUFBQTtRQUFBcEIsY0FBQSxHQUFBc0IsQ0FBQTtNQUFBO01BQUF0QixjQUFBLEdBQUFvQixDQUFBO01BRUQsT0FBTyxJQUFJO0lBQ2IsQ0FBQyxDQUFDO0lBQUM7SUFBQXBCLGNBQUEsR0FBQW9CLENBQUE7SUFFSCxPQUFPO01BQ0wsR0FBRzhHLE1BQU07TUFDVE4sZUFBZSxFQUFFK0c7S0FDbEI7RUFDSDtFQUVRakIsWUFBWUEsQ0FBQ2tCLFVBQWtCLEVBQUVyQixhQUFxQixFQUFFc0IsWUFBb0IsRUFBRUMsS0FBYTtJQUFBO0lBQUE5TyxjQUFBLEdBQUFxQixDQUFBO0lBQ2pHLElBQUkwTixHQUFHO0lBQUE7SUFBQSxDQUFBL08sY0FBQSxHQUFBb0IsQ0FBQSxTQUFHLENBQUN3TixVQUFVO0lBQUM7SUFBQTVPLGNBQUEsR0FBQW9CLENBQUE7SUFDdEIsS0FBSyxJQUFJNE4sSUFBSTtJQUFBO0lBQUEsQ0FBQWhQLGNBQUEsR0FBQW9CLENBQUEsU0FBRyxDQUFDLEdBQUU0TixJQUFJLElBQUlGLEtBQUssRUFBRUUsSUFBSSxFQUFFLEVBQUU7TUFBQTtNQUFBaFAsY0FBQSxHQUFBb0IsQ0FBQTtNQUN4QzJOLEdBQUcsSUFBSXhCLGFBQWEsR0FBR2pCLElBQUksQ0FBQzJDLEdBQUcsQ0FBQyxDQUFDLEdBQUdKLFlBQVksRUFBRUcsSUFBSSxDQUFDO0lBQ3pEO0lBQUM7SUFBQWhQLGNBQUEsR0FBQW9CLENBQUE7SUFDRCxPQUFPMk4sR0FBRztFQUNaO0VBRVFuQixZQUFZQSxDQUFDZ0IsVUFBa0IsRUFBRXJCLGFBQXFCLEVBQUV1QixLQUFhO0lBQUE7SUFBQTlPLGNBQUEsR0FBQXFCLENBQUE7SUFBQXJCLGNBQUEsR0FBQW9CLENBQUE7SUFDM0U7SUFDQSxPQUFRbU0sYUFBYSxHQUFHcUIsVUFBVSxHQUFJLEdBQUc7RUFDM0M7RUFFQTs7O0VBR0EsTUFBTU0sT0FBT0EsQ0FBQTtJQUFBO0lBQUFsUCxjQUFBLEdBQUFxQixDQUFBO0lBQUFyQixjQUFBLEdBQUFvQixDQUFBO0lBQ1gsSUFBSSxJQUFJLENBQUN1QixPQUFPLEVBQUU7TUFBQTtNQUFBM0MsY0FBQSxHQUFBc0IsQ0FBQTtNQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtNQUNoQixNQUFNLElBQUksQ0FBQ3VCLE9BQU8sQ0FBQ3dNLE9BQU8sRUFBRTtJQUM5QixDQUFDO0lBQUE7SUFBQTtNQUFBblAsY0FBQSxHQUFBc0IsQ0FBQTtJQUFBO0lBQUF0QixjQUFBLEdBQUFvQixDQUFBO0lBRUQsS0FBSyxNQUFNLENBQUNQLElBQUksRUFBRThCLE9BQU8sQ0FBQyxJQUFJLElBQUksQ0FBQ0MsVUFBVSxFQUFFO01BQUE7TUFBQTVDLGNBQUEsR0FBQW9CLENBQUE7TUFDN0MsTUFBTXVCLE9BQU8sQ0FBQ3dNLE9BQU8sRUFBRTtJQUN6QjtJQUFDO0lBQUFuUCxjQUFBLEdBQUFvQixDQUFBO0lBRUQsSUFBSSxDQUFDd0IsVUFBVSxDQUFDd00sS0FBSyxFQUFFO0lBQUM7SUFBQXBQLGNBQUEsR0FBQW9CLENBQUE7SUFDeEIsSUFBSSxDQUFDMEIsYUFBYSxHQUFHLEtBQUs7RUFDNUI7O0FBQ0Q7QUFBQTlDLGNBQUEsR0FBQW9CLENBQUE7QUE1aEJEYSxPQUFBLENBQUFPLHFCQUFBLEdBQUFBLHFCQUFBO0FBOGhCQTtBQUNBLElBQUk2TSxxQkFBcUI7QUFBQTtBQUFBLENBQUFyUCxjQUFBLEdBQUFvQixDQUFBLFNBQWlDLElBQUk7QUFFOUQsU0FBZ0JjLHdCQUF3QkEsQ0FBQ1EsTUFBNkI7RUFBQTtFQUFBMUMsY0FBQSxHQUFBcUIsQ0FBQTtFQUFBckIsY0FBQSxHQUFBb0IsQ0FBQTtFQUNwRTtFQUFJO0VBQUEsQ0FBQXBCLGNBQUEsR0FBQXNCLENBQUEsWUFBQytOLHFCQUFxQjtFQUFBO0VBQUEsQ0FBQXJQLGNBQUEsR0FBQXNCLENBQUEsV0FBSW9CLE1BQU0sR0FBRTtJQUFBO0lBQUExQyxjQUFBLEdBQUFzQixDQUFBO0lBQUF0QixjQUFBLEdBQUFvQixDQUFBO0lBQ3BDaU8scUJBQXFCLEdBQUcsSUFBSTdNLHFCQUFxQixDQUFDRSxNQUFNLENBQUM7RUFDM0QsQ0FBQztFQUFBO0VBQUE7SUFBQTFDLGNBQUEsR0FBQXNCLENBQUE7RUFBQTtFQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtFQUNELE9BQU9pTyxxQkFBc0I7QUFDL0I7QUFBQztBQUFBclAsY0FBQSxHQUFBb0IsQ0FBQTtBQUVEYSxPQUFBLENBQUFxTixPQUFBLEdBQWU5TSxxQkFBcUI7QUFFcEM7QUFDQSxTQUFnQkwsaUJBQWlCQSxDQUFDTyxNQUE2QjtFQUFBO0VBQUExQyxjQUFBLEdBQUFxQixDQUFBO0VBQzdELE1BQU0sQ0FBQ2tPLE9BQU8sRUFBRUMsVUFBVSxDQUFDO0VBQUE7RUFBQSxDQUFBeFAsY0FBQSxHQUFBb0IsQ0FBQSxTQUFHLElBQUFnQixPQUFBLENBQUFxTixRQUFRLEVBQStCLElBQUksQ0FBQztFQUMxRSxNQUFNLENBQUMzTSxhQUFhLEVBQUU0TSxnQkFBZ0IsQ0FBQztFQUFBO0VBQUEsQ0FBQTFQLGNBQUEsR0FBQW9CLENBQUEsU0FBRyxJQUFBZ0IsT0FBQSxDQUFBcU4sUUFBUSxFQUFDLEtBQUssQ0FBQztFQUN6RCxNQUFNLENBQUNFLFNBQVMsRUFBRUMsWUFBWSxDQUFDO0VBQUE7RUFBQSxDQUFBNVAsY0FBQSxHQUFBb0IsQ0FBQSxTQUFHLElBQUFnQixPQUFBLENBQUFxTixRQUFRLEVBQUMsS0FBSyxDQUFDO0VBQ2pELE1BQU0sQ0FBQzFMLEtBQUssRUFBRThMLFFBQVEsQ0FBQztFQUFBO0VBQUEsQ0FBQTdQLGNBQUEsR0FBQW9CLENBQUEsU0FBRyxJQUFBZ0IsT0FBQSxDQUFBcU4sUUFBUSxFQUFnQixJQUFJLENBQUM7RUFBQztFQUFBelAsY0FBQSxHQUFBb0IsQ0FBQTtFQUV4RCxJQUFBZ0IsT0FBQSxDQUFBME4sU0FBUyxFQUFDLE1BQUs7SUFBQTtJQUFBOVAsY0FBQSxHQUFBcUIsQ0FBQTtJQUFBckIsY0FBQSxHQUFBb0IsQ0FBQTtJQUNiLElBQUlzQixNQUFNLEVBQUU7TUFBQTtNQUFBMUMsY0FBQSxHQUFBc0IsQ0FBQTtNQUNWLE1BQU15TyxTQUFTO01BQUE7TUFBQSxDQUFBL1AsY0FBQSxHQUFBb0IsQ0FBQSxTQUFHLElBQUlvQixxQkFBcUIsQ0FBQ0UsTUFBTSxDQUFDO01BQUM7TUFBQTFDLGNBQUEsR0FBQW9CLENBQUE7TUFDcERvTyxVQUFVLENBQUNPLFNBQVMsQ0FBQztNQUFDO01BQUEvUCxjQUFBLEdBQUFvQixDQUFBO01BRXRCMk8sU0FBUyxDQUFDM00sVUFBVSxFQUFFLENBQ25CNE0sSUFBSSxDQUFDLE1BQU07UUFBQTtRQUFBaFEsY0FBQSxHQUFBcUIsQ0FBQTtRQUFBckIsY0FBQSxHQUFBb0IsQ0FBQTtRQUFBLE9BQUFzTyxnQkFBZ0IsQ0FBQyxJQUFJLENBQUM7TUFBRCxDQUFDLENBQUMsQ0FDbENPLEtBQUssQ0FBQ0MsR0FBRyxJQUFJO1FBQUE7UUFBQWxRLGNBQUEsR0FBQXFCLENBQUE7UUFBQXJCLGNBQUEsR0FBQW9CLENBQUE7UUFBQSxPQUFBeU8sUUFBUSxDQUFDSyxHQUFHLENBQUNDLE9BQU8sQ0FBQztNQUFELENBQUMsQ0FBQztJQUN4QyxDQUFDO0lBQUE7SUFBQTtNQUFBblEsY0FBQSxHQUFBc0IsQ0FBQTtJQUFBO0VBQ0gsQ0FBQyxFQUFFLENBQUNvQixNQUFNLENBQUMsQ0FBQztFQUVaLE1BQU1zQixjQUFjO0VBQUE7RUFBQSxDQUFBaEUsY0FBQSxHQUFBb0IsQ0FBQSxTQUFHLElBQUFnQixPQUFBLENBQUFnTyxXQUFXLEVBQUMsTUFBT25NLEtBQXdCLElBQUk7SUFBQTtJQUFBakUsY0FBQSxHQUFBcUIsQ0FBQTtJQUFBckIsY0FBQSxHQUFBb0IsQ0FBQTtJQUNwRTtJQUFJO0lBQUEsQ0FBQXBCLGNBQUEsR0FBQXNCLENBQUEsWUFBQ2lPLE9BQU87SUFBQTtJQUFBLENBQUF2UCxjQUFBLEdBQUFzQixDQUFBLFdBQUksQ0FBQ3dCLGFBQWEsR0FBRTtNQUFBO01BQUE5QyxjQUFBLEdBQUFzQixDQUFBO01BQUF0QixjQUFBLEdBQUFvQixDQUFBO01BQzlCLE1BQU0sSUFBSThDLEtBQUssQ0FBQyw0QkFBNEIsQ0FBQztJQUMvQyxDQUFDO0lBQUE7SUFBQTtNQUFBbEUsY0FBQSxHQUFBc0IsQ0FBQTtJQUFBO0lBQUF0QixjQUFBLEdBQUFvQixDQUFBO0lBRUR3TyxZQUFZLENBQUMsSUFBSSxDQUFDO0lBQUM7SUFBQTVQLGNBQUEsR0FBQW9CLENBQUE7SUFDbkJ5TyxRQUFRLENBQUMsSUFBSSxDQUFDO0lBQUM7SUFBQTdQLGNBQUEsR0FBQW9CLENBQUE7SUFFZixJQUFJO01BQ0YsTUFBTThHLE1BQU07TUFBQTtNQUFBLENBQUFsSSxjQUFBLEdBQUFvQixDQUFBLFNBQUcsTUFBTW1PLE9BQU8sQ0FBQ3ZMLGNBQWMsQ0FBQ0MsS0FBSyxDQUFDO01BQUM7TUFBQWpFLGNBQUEsR0FBQW9CLENBQUE7TUFDbkQsT0FBTzhHLE1BQU07SUFDZixDQUFDLENBQUMsT0FBT2dJLEdBQUcsRUFBRTtNQUNaLE1BQU1HLFlBQVk7TUFBQTtNQUFBLENBQUFyUSxjQUFBLEdBQUFvQixDQUFBLFNBQUc4TyxHQUFHLFlBQVloTSxLQUFLO01BQUE7TUFBQSxDQUFBbEUsY0FBQSxHQUFBc0IsQ0FBQSxXQUFHNE8sR0FBRyxDQUFDQyxPQUFPO01BQUE7TUFBQSxDQUFBblEsY0FBQSxHQUFBc0IsQ0FBQSxXQUFHLHFCQUFxQjtNQUFDO01BQUF0QixjQUFBLEdBQUFvQixDQUFBO01BQ2hGeU8sUUFBUSxDQUFDUSxZQUFZLENBQUM7TUFBQztNQUFBclEsY0FBQSxHQUFBb0IsQ0FBQTtNQUN2QixNQUFNOE8sR0FBRztJQUNYLENBQUMsU0FBUztNQUFBO01BQUFsUSxjQUFBLEdBQUFvQixDQUFBO01BQ1J3TyxZQUFZLENBQUMsS0FBSyxDQUFDO0lBQ3JCO0VBQ0YsQ0FBQyxFQUFFLENBQUNMLE9BQU8sRUFBRXpNLGFBQWEsQ0FBQyxDQUFDO0VBRTVCLE1BQU13TixhQUFhO0VBQUE7RUFBQSxDQUFBdFEsY0FBQSxHQUFBb0IsQ0FBQSxTQUFHLElBQUFnQixPQUFBLENBQUFnTyxXQUFXLEVBQUMsT0FDaEN4TCxVQUFzQixFQUN0QkMsaUJBQW9DLEVBQ3BDQyxXQUFvQixLQUNsQjtJQUFBO0lBQUE5RSxjQUFBLEdBQUFxQixDQUFBO0lBQUFyQixjQUFBLEdBQUFvQixDQUFBO0lBQ0Y7SUFBSTtJQUFBLENBQUFwQixjQUFBLEdBQUFzQixDQUFBLFlBQUNpTyxPQUFPO0lBQUE7SUFBQSxDQUFBdlAsY0FBQSxHQUFBc0IsQ0FBQSxXQUFJLENBQUN3QixhQUFhLEdBQUU7TUFBQTtNQUFBOUMsY0FBQSxHQUFBc0IsQ0FBQTtNQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtNQUM5QixNQUFNLElBQUk4QyxLQUFLLENBQUMsNEJBQTRCLENBQUM7SUFDL0MsQ0FBQztJQUFBO0lBQUE7TUFBQWxFLGNBQUEsR0FBQXNCLENBQUE7SUFBQTtJQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtJQUVELE9BQU9tTyxPQUFPLENBQUM1Syx3QkFBd0IsQ0FBQ0MsVUFBVSxFQUFFQyxpQkFBaUIsRUFBRUMsV0FBVyxDQUFDO0VBQ3JGLENBQUMsRUFBRSxDQUFDeUssT0FBTyxFQUFFek0sYUFBYSxDQUFDLENBQUM7RUFFNUIsTUFBTXdDLGVBQWU7RUFBQTtFQUFBLENBQUF0RixjQUFBLEdBQUFvQixDQUFBLFNBQUcsSUFBQWdCLE9BQUEsQ0FBQWdPLFdBQVcsRUFBQyxPQUNsQzdLLGVBQXFDLEVBQ3JDQyxTQUFrQixLQUNoQjtJQUFBO0lBQUF4RixjQUFBLEdBQUFxQixDQUFBO0lBQUFyQixjQUFBLEdBQUFvQixDQUFBO0lBQ0Y7SUFBSTtJQUFBLENBQUFwQixjQUFBLEdBQUFzQixDQUFBLFlBQUNpTyxPQUFPO0lBQUE7SUFBQSxDQUFBdlAsY0FBQSxHQUFBc0IsQ0FBQSxXQUFJLENBQUN3QixhQUFhLEdBQUU7TUFBQTtNQUFBOUMsY0FBQSxHQUFBc0IsQ0FBQTtNQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtNQUM5QixNQUFNLElBQUk4QyxLQUFLLENBQUMsNEJBQTRCLENBQUM7SUFDL0MsQ0FBQztJQUFBO0lBQUE7TUFBQWxFLGNBQUEsR0FBQXNCLENBQUE7SUFBQTtJQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtJQUVELE9BQU9tTyxPQUFPLENBQUNqSyxlQUFlLENBQUNDLGVBQWUsRUFBRUMsU0FBUyxDQUFDO0VBQzVELENBQUMsRUFBRSxDQUFDK0osT0FBTyxFQUFFek0sYUFBYSxDQUFDLENBQUM7RUFBQztFQUFBOUMsY0FBQSxHQUFBb0IsQ0FBQTtFQUU3QixPQUFPO0lBQ0wwQixhQUFhO0lBQ2I2TSxTQUFTO0lBQ1Q1TCxLQUFLO0lBQ0xDLGNBQWM7SUFDZHNNLGFBQWE7SUFDYmhMLGVBQWU7SUFDZmlLO0dBQ0Q7QUFDSCIsImlnbm9yZUxpc3QiOltdfQ==