{"version": 3, "names": ["global", "fetch", "jest", "fn", "describe", "baseUrl", "sampleHVACDesign", "system_type", "airflow_cfm", "fan_power_watts", "duct_insulation_r_value", "duct_leakage_cfm", "climate_zone", "building_type", "conditioned_area_sqft", "equipment_efficiency", "air_conditioner", "seer", "eer", "controls", "automatic_shutoff", "demand_control_ventilation", "economizer_required", "compliantHVACDesign", "iecc2024Design", "smart_controls", "zone_control", "renewable_percentage", "beforeEach", "mockClear", "test", "mockResponse", "input", "compliance", "standard", "is_compliant", "compliance_percentage", "violations", "recommendations", "critical_issues", "warnings", "energy_savings_potential", "cost_impact", "mockResolvedValueOnce", "ok", "json", "response", "method", "headers", "body", "JSON", "stringify", "result", "expect", "toBe", "toBeGreaterThan", "toHave<PERSON>ength", "length", "incompleteDesign", "status", "error", "missing_fields", "compliance_results", "ASHRAE_90_2", "IECC_2024", "summary", "total_standards", "compliant_standards", "average_compliance", "total_critical_issues", "total_warnings", "toBeDefined", "supported_standards", "name", "description", "endpoint", "version", "focus_areas", "combined_endpoint", "backward_compatibility", "existing_endpoints_preserved", "existing_api_unchanged", "note", "existingEndpoints", "validation", "compliant", "checks", "errors"], "sources": ["advanced-compliance-integration.test.js"], "sourcesContent": ["/**\n * Advanced HVAC Compliance Integration Tests\n *\n * Tests for ASHRAE 90.2 and IECC 2024 compliance checking\n * Part of Phase 1 bridging plan for comprehensive HVAC standards support\n *\n * @see docs/post-implementation-bridging-plan.md Task 1.2\n */\n\n// Mock fetch for API calls\nglobal.fetch = jest.fn();\n\ndescribe('Advanced HVAC Compliance Integration Tests', () => {\n  const baseUrl = 'http://localhost:5000';\n  \n  // Sample HVAC design data for testing\n  const sampleHVACDesign = {\n    system_type: 'variable_volume',\n    airflow_cfm: 5000,\n    fan_power_watts: 4000,\n    duct_insulation_r_value: 6.0,\n    duct_leakage_cfm: 150,\n    climate_zone: '4',\n    building_type: 'office',\n    conditioned_area_sqft: 10000,\n    equipment_efficiency: {\n      air_conditioner: {\n        seer: 14.5,\n        eer: 11.2\n      }\n    },\n    controls: {\n      automatic_shutoff: true,\n      demand_control_ventilation: true,\n      economizer_required: true\n    }\n  };\n\n  const compliantHVACDesign = {\n    ...sampleHVACDesign,\n    fan_power_watts: 3500, // Lower fan power for compliance\n    duct_insulation_r_value: 8.0, // Higher insulation\n    duct_leakage_cfm: 100, // Lower leakage\n    equipment_efficiency: {\n      air_conditioner: {\n        seer: 15.0,\n        eer: 12.0\n      }\n    }\n  };\n\n  const iecc2024Design = {\n    ...compliantHVACDesign,\n    fan_power_watts: 3000, // Even lower for IECC 2024\n    duct_insulation_r_value: 10.0, // Enhanced insulation\n    duct_leakage_cfm: 80, // Stricter leakage\n    controls: {\n      ...compliantHVACDesign.controls,\n      smart_controls: true,\n      zone_control: true,\n      renewable_percentage: 15.0\n    }\n  };\n\n  beforeEach(() => {\n    fetch.mockClear();\n  });\n\n  describe('ASHRAE 90.2 Compliance Testing', () => {\n    test('should validate compliant ASHRAE 90.2 design', async () => {\n      // Mock successful API response\n      const mockResponse = {\n        input: compliantHVACDesign,\n        compliance: {\n          standard: 'ASHRAE 90.2',\n          is_compliant: true,\n          compliance_percentage: 95.0,\n          violations: [],\n          recommendations: [],\n          critical_issues: 0,\n          warnings: 0,\n          energy_savings_potential: 0.0,\n          cost_impact: 'Low'\n        }\n      };\n\n      fetch.mockResolvedValueOnce({\n        ok: true,\n        json: async () => mockResponse\n      });\n\n      const response = await fetch(`${baseUrl}/api/compliance/ashrae-902`, {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(compliantHVACDesign)\n      });\n\n      const result = await response.json();\n\n      expect(response.ok).toBe(true);\n      expect(result.compliance.standard).toBe('ASHRAE 90.2');\n      expect(result.compliance.is_compliant).toBe(true);\n      expect(result.compliance.compliance_percentage).toBeGreaterThan(90);\n      expect(result.compliance.violations).toHaveLength(0);\n      expect(result.compliance.critical_issues).toBe(0);\n    });\n\n    test('should identify ASHRAE 90.2 violations in non-compliant design', async () => {\n      // Mock response with violations\n      const mockResponse = {\n        input: sampleHVACDesign,\n        compliance: {\n          standard: 'ASHRAE 90.2',\n          is_compliant: false,\n          compliance_percentage: 65.0,\n          violations: [\n            'Fan power 0.80 W/CFM exceeds ASHRAE 90.2 limit of 1.0 W/CFM',\n            'Duct leakage rate 1.5 CFM/100 sq ft exceeds ASHRAE 90.2 limit of 4.0 CFM/100 sq ft'\n          ],\n          recommendations: [\n            'Consider variable speed drives or more efficient fan selection',\n            'Improve duct sealing and testing procedures'\n          ],\n          critical_issues: 2,\n          warnings: 0,\n          energy_savings_potential: 25.0,\n          cost_impact: 'Medium'\n        }\n      };\n\n      fetch.mockResolvedValueOnce({\n        ok: true,\n        json: async () => mockResponse\n      });\n\n      const response = await fetch(`${baseUrl}/api/compliance/ashrae-902`, {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(sampleHVACDesign)\n      });\n\n      const result = await response.json();\n\n      expect(response.ok).toBe(true);\n      expect(result.compliance.is_compliant).toBe(false);\n      expect(result.compliance.violations.length).toBeGreaterThan(0);\n      expect(result.compliance.recommendations.length).toBeGreaterThan(0);\n      expect(result.compliance.energy_savings_potential).toBeGreaterThan(0);\n    });\n\n    test('should handle missing required fields for ASHRAE 90.2', async () => {\n      const incompleteDesign = {\n        system_type: 'variable_volume',\n        airflow_cfm: 5000\n        // Missing other required fields\n      };\n\n      fetch.mockResolvedValueOnce({\n        ok: false,\n        status: 400,\n        json: async () => ({\n          error: 'Missing required fields',\n          missing_fields: ['fan_power_watts', 'duct_insulation_r_value', 'duct_leakage_cfm']\n        })\n      });\n\n      const response = await fetch(`${baseUrl}/api/compliance/ashrae-902`, {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(incompleteDesign)\n      });\n\n      expect(response.ok).toBe(false);\n      expect(response.status).toBe(400);\n    });\n  });\n\n  describe('IECC 2024 Compliance Testing', () => {\n    test('should validate compliant IECC 2024 design', async () => {\n      // Mock successful API response\n      const mockResponse = {\n        input: iecc2024Design,\n        compliance: {\n          standard: 'IECC 2024',\n          is_compliant: true,\n          compliance_percentage: 98.0,\n          violations: [],\n          recommendations: [],\n          critical_issues: 0,\n          warnings: 0,\n          energy_savings_potential: 0.0,\n          cost_impact: 'Low'\n        }\n      };\n\n      fetch.mockResolvedValueOnce({\n        ok: true,\n        json: async () => mockResponse\n      });\n\n      const response = await fetch(`${baseUrl}/api/compliance/iecc-2024`, {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(iecc2024Design)\n      });\n\n      const result = await response.json();\n\n      expect(response.ok).toBe(true);\n      expect(result.compliance.standard).toBe('IECC 2024');\n      expect(result.compliance.is_compliant).toBe(true);\n      expect(result.compliance.compliance_percentage).toBeGreaterThan(95);\n      expect(result.compliance.violations).toHaveLength(0);\n    });\n\n    test('should identify IECC 2024 violations with stricter requirements', async () => {\n      // Mock response with IECC 2024 specific violations\n      const mockResponse = {\n        input: sampleHVACDesign,\n        compliance: {\n          standard: 'IECC 2024',\n          is_compliant: false,\n          compliance_percentage: 55.0,\n          violations: [\n            'Fan power 0.80 W/CFM exceeds IECC 2024 limit of 0.8 W/CFM',\n            'Duct insulation R-6.0 below IECC 2024 minimum R-8.0 for climate zone 4',\n            'Missing required IECC 2024 control: smart_controls',\n            'Renewable energy percentage below IECC 2024 minimum 10%'\n          ],\n          recommendations: [\n            'Consider high-efficiency fans with variable speed drives',\n            'Increase duct insulation to minimum R-8.0',\n            'Install smart controls system',\n            'Consider solar panels or other renewable energy systems'\n          ],\n          critical_issues: 2,\n          warnings: 2,\n          energy_savings_potential: 35.0,\n          cost_impact: 'High'\n        }\n      };\n\n      fetch.mockResolvedValueOnce({\n        ok: true,\n        json: async () => mockResponse\n      });\n\n      const response = await fetch(`${baseUrl}/api/compliance/iecc-2024`, {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(sampleHVACDesign)\n      });\n\n      const result = await response.json();\n\n      expect(response.ok).toBe(true);\n      expect(result.compliance.is_compliant).toBe(false);\n      expect(result.compliance.violations.length).toBeGreaterThan(3);\n      expect(result.compliance.energy_savings_potential).toBeGreaterThan(30);\n      expect(result.compliance.cost_impact).toBe('High');\n    });\n  });\n\n  describe('All Advanced Standards Testing', () => {\n    test('should validate design against all advanced standards', async () => {\n      // Mock response for combined standards check\n      const mockResponse = {\n        input: compliantHVACDesign,\n        compliance_results: {\n          ASHRAE_90_2: {\n            standard: 'ASHRAE 90.2',\n            is_compliant: true,\n            compliance_percentage: 95.0,\n            violations: [],\n            recommendations: [],\n            critical_issues: 0,\n            warnings: 0,\n            energy_savings_potential: 0.0,\n            cost_impact: 'Low'\n          },\n          IECC_2024: {\n            standard: 'IECC 2024',\n            is_compliant: false,\n            compliance_percentage: 85.0,\n            violations: ['Missing required IECC 2024 control: smart_controls'],\n            recommendations: ['Install smart controls system'],\n            critical_issues: 0,\n            warnings: 1,\n            energy_savings_potential: 10.0,\n            cost_impact: 'Medium'\n          }\n        },\n        summary: {\n          total_standards: 2,\n          compliant_standards: 1,\n          average_compliance: 90.0,\n          total_critical_issues: 0,\n          total_warnings: 1\n        }\n      };\n\n      fetch.mockResolvedValueOnce({\n        ok: true,\n        json: async () => mockResponse\n      });\n\n      const response = await fetch(`${baseUrl}/api/compliance/all-advanced`, {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(compliantHVACDesign)\n      });\n\n      const result = await response.json();\n\n      expect(response.ok).toBe(true);\n      expect(result.summary.total_standards).toBe(2);\n      expect(result.summary.compliant_standards).toBe(1);\n      expect(result.summary.average_compliance).toBe(90.0);\n      expect(result.compliance_results.ASHRAE_90_2).toBeDefined();\n      expect(result.compliance_results.IECC_2024).toBeDefined();\n    });\n  });\n\n  describe('Standards Information Testing', () => {\n    test('should retrieve information about supported standards', async () => {\n      // Mock standards info response\n      const mockResponse = {\n        supported_standards: [\n          {\n            name: 'ASHRAE 90.2',\n            description: 'Energy-Efficient Design of Low-Rise Residential Buildings',\n            endpoint: '/api/compliance/ashrae-902',\n            version: '2018',\n            focus_areas: [\n              'Fan power limits',\n              'Duct insulation requirements',\n              'Duct leakage limits',\n              'Equipment efficiency',\n              'Control systems'\n            ]\n          },\n          {\n            name: 'IECC 2024',\n            description: 'International Energy Conservation Code 2024',\n            endpoint: '/api/compliance/iecc-2024',\n            version: '2024',\n            focus_areas: [\n              'Enhanced fan power limits',\n              'Improved insulation requirements',\n              'Stricter duct leakage limits',\n              'High-efficiency equipment',\n              'Smart control systems',\n              'Renewable energy integration'\n            ]\n          }\n        ],\n        combined_endpoint: '/api/compliance/all-advanced',\n        backward_compatibility: {\n          existing_endpoints_preserved: true,\n          existing_api_unchanged: true,\n          note: 'All existing compliance endpoints remain fully functional'\n        }\n      };\n\n      fetch.mockResolvedValueOnce({\n        ok: true,\n        json: async () => mockResponse\n      });\n\n      const response = await fetch(`${baseUrl}/api/compliance/standards-info`, {\n        method: 'GET'\n      });\n\n      const result = await response.json();\n\n      expect(response.ok).toBe(true);\n      expect(result.supported_standards).toHaveLength(2);\n      expect(result.supported_standards[0].name).toBe('ASHRAE 90.2');\n      expect(result.supported_standards[1].name).toBe('IECC 2024');\n      expect(result.backward_compatibility.existing_endpoints_preserved).toBe(true);\n    });\n  });\n\n  describe('Backward Compatibility Testing', () => {\n    test('should preserve existing compliance API functionality', async () => {\n      // Test that existing endpoints still work\n      const existingEndpoints = [\n        '/api/compliance/check',\n        '/api/compliance/smacna',\n        '/api/compliance/ashrae'\n      ];\n\n      for (const endpoint of existingEndpoints) {\n        // Mock existing endpoint response\n        fetch.mockResolvedValueOnce({\n          ok: true,\n          json: async () => ({\n            validation: {\n              compliant: true,\n              standard: 'Existing Standard',\n              checks: [],\n              warnings: [],\n              errors: []\n            }\n          })\n        });\n\n        const response = await fetch(`${baseUrl}${endpoint}`, {\n          method: 'POST',\n          headers: { 'Content-Type': 'application/json' },\n          body: JSON.stringify(sampleHVACDesign)\n        });\n\n        expect(response.ok).toBe(true);\n      }\n    });\n  });\n});\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACAA,MAAM,CAACC,KAAK,GAAGC,IAAI,CAACC,EAAE,CAAC,CAAC;AAExBC,QAAQ,CAAC,4CAA4C,EAAE,MAAM;EAC3D,MAAMC,OAAO,GAAG,uBAAuB;;EAEvC;EACA,MAAMC,gBAAgB,GAAG;IACvBC,WAAW,EAAE,iBAAiB;IAC9BC,WAAW,EAAE,IAAI;IACjBC,eAAe,EAAE,IAAI;IACrBC,uBAAuB,EAAE,GAAG;IAC5BC,gBAAgB,EAAE,GAAG;IACrBC,YAAY,EAAE,GAAG;IACjBC,aAAa,EAAE,QAAQ;IACvBC,qBAAqB,EAAE,KAAK;IAC5BC,oBAAoB,EAAE;MACpBC,eAAe,EAAE;QACfC,IAAI,EAAE,IAAI;QACVC,GAAG,EAAE;MACP;IACF,CAAC;IACDC,QAAQ,EAAE;MACRC,iBAAiB,EAAE,IAAI;MACvBC,0BAA0B,EAAE,IAAI;MAChCC,mBAAmB,EAAE;IACvB;EACF,CAAC;EAED,MAAMC,mBAAmB,GAAG;IAC1B,GAAGjB,gBAAgB;IACnBG,eAAe,EAAE,IAAI;IAAE;IACvBC,uBAAuB,EAAE,GAAG;IAAE;IAC9BC,gBAAgB,EAAE,GAAG;IAAE;IACvBI,oBAAoB,EAAE;MACpBC,eAAe,EAAE;QACfC,IAAI,EAAE,IAAI;QACVC,GAAG,EAAE;MACP;IACF;EACF,CAAC;EAED,MAAMM,cAAc,GAAG;IACrB,GAAGD,mBAAmB;IACtBd,eAAe,EAAE,IAAI;IAAE;IACvBC,uBAAuB,EAAE,IAAI;IAAE;IAC/BC,gBAAgB,EAAE,EAAE;IAAE;IACtBQ,QAAQ,EAAE;MACR,GAAGI,mBAAmB,CAACJ,QAAQ;MAC/BM,cAAc,EAAE,IAAI;MACpBC,YAAY,EAAE,IAAI;MAClBC,oBAAoB,EAAE;IACxB;EACF,CAAC;EAEDC,UAAU,CAAC,MAAM;IACf3B,KAAK,CAAC4B,SAAS,CAAC,CAAC;EACnB,CAAC,CAAC;EAEFzB,QAAQ,CAAC,gCAAgC,EAAE,MAAM;IAC/C0B,IAAI,CAAC,8CAA8C,EAAE,YAAY;MAC/D;MACA,MAAMC,YAAY,GAAG;QACnBC,KAAK,EAAET,mBAAmB;QAC1BU,UAAU,EAAE;UACVC,QAAQ,EAAE,aAAa;UACvBC,YAAY,EAAE,IAAI;UAClBC,qBAAqB,EAAE,IAAI;UAC3BC,UAAU,EAAE,EAAE;UACdC,eAAe,EAAE,EAAE;UACnBC,eAAe,EAAE,CAAC;UAClBC,QAAQ,EAAE,CAAC;UACXC,wBAAwB,EAAE,GAAG;UAC7BC,WAAW,EAAE;QACf;MACF,CAAC;MAEDzC,KAAK,CAAC0C,qBAAqB,CAAC;QAC1BC,EAAE,EAAE,IAAI;QACRC,IAAI,EAAE,MAAAA,CAAA,KAAYd;MACpB,CAAC,CAAC;MAEF,MAAMe,QAAQ,GAAG,MAAM7C,KAAK,CAAC,GAAGI,OAAO,4BAA4B,EAAE;QACnE0C,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC5B,mBAAmB;MAC1C,CAAC,CAAC;MAEF,MAAM6B,MAAM,GAAG,MAAMN,QAAQ,CAACD,IAAI,CAAC,CAAC;MAEpCQ,MAAM,CAACP,QAAQ,CAACF,EAAE,CAAC,CAACU,IAAI,CAAC,IAAI,CAAC;MAC9BD,MAAM,CAACD,MAAM,CAACnB,UAAU,CAACC,QAAQ,CAAC,CAACoB,IAAI,CAAC,aAAa,CAAC;MACtDD,MAAM,CAACD,MAAM,CAACnB,UAAU,CAACE,YAAY,CAAC,CAACmB,IAAI,CAAC,IAAI,CAAC;MACjDD,MAAM,CAACD,MAAM,CAACnB,UAAU,CAACG,qBAAqB,CAAC,CAACmB,eAAe,CAAC,EAAE,CAAC;MACnEF,MAAM,CAACD,MAAM,CAACnB,UAAU,CAACI,UAAU,CAAC,CAACmB,YAAY,CAAC,CAAC,CAAC;MACpDH,MAAM,CAACD,MAAM,CAACnB,UAAU,CAACM,eAAe,CAAC,CAACe,IAAI,CAAC,CAAC,CAAC;IACnD,CAAC,CAAC;IAEFxB,IAAI,CAAC,gEAAgE,EAAE,YAAY;MACjF;MACA,MAAMC,YAAY,GAAG;QACnBC,KAAK,EAAE1B,gBAAgB;QACvB2B,UAAU,EAAE;UACVC,QAAQ,EAAE,aAAa;UACvBC,YAAY,EAAE,KAAK;UACnBC,qBAAqB,EAAE,IAAI;UAC3BC,UAAU,EAAE,CACV,6DAA6D,EAC7D,oFAAoF,CACrF;UACDC,eAAe,EAAE,CACf,gEAAgE,EAChE,6CAA6C,CAC9C;UACDC,eAAe,EAAE,CAAC;UAClBC,QAAQ,EAAE,CAAC;UACXC,wBAAwB,EAAE,IAAI;UAC9BC,WAAW,EAAE;QACf;MACF,CAAC;MAEDzC,KAAK,CAAC0C,qBAAqB,CAAC;QAC1BC,EAAE,EAAE,IAAI;QACRC,IAAI,EAAE,MAAAA,CAAA,KAAYd;MACpB,CAAC,CAAC;MAEF,MAAMe,QAAQ,GAAG,MAAM7C,KAAK,CAAC,GAAGI,OAAO,4BAA4B,EAAE;QACnE0C,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC7C,gBAAgB;MACvC,CAAC,CAAC;MAEF,MAAM8C,MAAM,GAAG,MAAMN,QAAQ,CAACD,IAAI,CAAC,CAAC;MAEpCQ,MAAM,CAACP,QAAQ,CAACF,EAAE,CAAC,CAACU,IAAI,CAAC,IAAI,CAAC;MAC9BD,MAAM,CAACD,MAAM,CAACnB,UAAU,CAACE,YAAY,CAAC,CAACmB,IAAI,CAAC,KAAK,CAAC;MAClDD,MAAM,CAACD,MAAM,CAACnB,UAAU,CAACI,UAAU,CAACoB,MAAM,CAAC,CAACF,eAAe,CAAC,CAAC,CAAC;MAC9DF,MAAM,CAACD,MAAM,CAACnB,UAAU,CAACK,eAAe,CAACmB,MAAM,CAAC,CAACF,eAAe,CAAC,CAAC,CAAC;MACnEF,MAAM,CAACD,MAAM,CAACnB,UAAU,CAACQ,wBAAwB,CAAC,CAACc,eAAe,CAAC,CAAC,CAAC;IACvE,CAAC,CAAC;IAEFzB,IAAI,CAAC,uDAAuD,EAAE,YAAY;MACxE,MAAM4B,gBAAgB,GAAG;QACvBnD,WAAW,EAAE,iBAAiB;QAC9BC,WAAW,EAAE;QACb;MACF,CAAC;MAEDP,KAAK,CAAC0C,qBAAqB,CAAC;QAC1BC,EAAE,EAAE,KAAK;QACTe,MAAM,EAAE,GAAG;QACXd,IAAI,EAAE,MAAAA,CAAA,MAAa;UACjBe,KAAK,EAAE,yBAAyB;UAChCC,cAAc,EAAE,CAAC,iBAAiB,EAAE,yBAAyB,EAAE,kBAAkB;QACnF,CAAC;MACH,CAAC,CAAC;MAEF,MAAMf,QAAQ,GAAG,MAAM7C,KAAK,CAAC,GAAGI,OAAO,4BAA4B,EAAE;QACnE0C,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACO,gBAAgB;MACvC,CAAC,CAAC;MAEFL,MAAM,CAACP,QAAQ,CAACF,EAAE,CAAC,CAACU,IAAI,CAAC,KAAK,CAAC;MAC/BD,MAAM,CAACP,QAAQ,CAACa,MAAM,CAAC,CAACL,IAAI,CAAC,GAAG,CAAC;IACnC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFlD,QAAQ,CAAC,8BAA8B,EAAE,MAAM;IAC7C0B,IAAI,CAAC,4CAA4C,EAAE,YAAY;MAC7D;MACA,MAAMC,YAAY,GAAG;QACnBC,KAAK,EAAER,cAAc;QACrBS,UAAU,EAAE;UACVC,QAAQ,EAAE,WAAW;UACrBC,YAAY,EAAE,IAAI;UAClBC,qBAAqB,EAAE,IAAI;UAC3BC,UAAU,EAAE,EAAE;UACdC,eAAe,EAAE,EAAE;UACnBC,eAAe,EAAE,CAAC;UAClBC,QAAQ,EAAE,CAAC;UACXC,wBAAwB,EAAE,GAAG;UAC7BC,WAAW,EAAE;QACf;MACF,CAAC;MAEDzC,KAAK,CAAC0C,qBAAqB,CAAC;QAC1BC,EAAE,EAAE,IAAI;QACRC,IAAI,EAAE,MAAAA,CAAA,KAAYd;MACpB,CAAC,CAAC;MAEF,MAAMe,QAAQ,GAAG,MAAM7C,KAAK,CAAC,GAAGI,OAAO,2BAA2B,EAAE;QAClE0C,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC3B,cAAc;MACrC,CAAC,CAAC;MAEF,MAAM4B,MAAM,GAAG,MAAMN,QAAQ,CAACD,IAAI,CAAC,CAAC;MAEpCQ,MAAM,CAACP,QAAQ,CAACF,EAAE,CAAC,CAACU,IAAI,CAAC,IAAI,CAAC;MAC9BD,MAAM,CAACD,MAAM,CAACnB,UAAU,CAACC,QAAQ,CAAC,CAACoB,IAAI,CAAC,WAAW,CAAC;MACpDD,MAAM,CAACD,MAAM,CAACnB,UAAU,CAACE,YAAY,CAAC,CAACmB,IAAI,CAAC,IAAI,CAAC;MACjDD,MAAM,CAACD,MAAM,CAACnB,UAAU,CAACG,qBAAqB,CAAC,CAACmB,eAAe,CAAC,EAAE,CAAC;MACnEF,MAAM,CAACD,MAAM,CAACnB,UAAU,CAACI,UAAU,CAAC,CAACmB,YAAY,CAAC,CAAC,CAAC;IACtD,CAAC,CAAC;IAEF1B,IAAI,CAAC,iEAAiE,EAAE,YAAY;MAClF;MACA,MAAMC,YAAY,GAAG;QACnBC,KAAK,EAAE1B,gBAAgB;QACvB2B,UAAU,EAAE;UACVC,QAAQ,EAAE,WAAW;UACrBC,YAAY,EAAE,KAAK;UACnBC,qBAAqB,EAAE,IAAI;UAC3BC,UAAU,EAAE,CACV,2DAA2D,EAC3D,wEAAwE,EACxE,oDAAoD,EACpD,yDAAyD,CAC1D;UACDC,eAAe,EAAE,CACf,0DAA0D,EAC1D,2CAA2C,EAC3C,+BAA+B,EAC/B,yDAAyD,CAC1D;UACDC,eAAe,EAAE,CAAC;UAClBC,QAAQ,EAAE,CAAC;UACXC,wBAAwB,EAAE,IAAI;UAC9BC,WAAW,EAAE;QACf;MACF,CAAC;MAEDzC,KAAK,CAAC0C,qBAAqB,CAAC;QAC1BC,EAAE,EAAE,IAAI;QACRC,IAAI,EAAE,MAAAA,CAAA,KAAYd;MACpB,CAAC,CAAC;MAEF,MAAMe,QAAQ,GAAG,MAAM7C,KAAK,CAAC,GAAGI,OAAO,2BAA2B,EAAE;QAClE0C,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC7C,gBAAgB;MACvC,CAAC,CAAC;MAEF,MAAM8C,MAAM,GAAG,MAAMN,QAAQ,CAACD,IAAI,CAAC,CAAC;MAEpCQ,MAAM,CAACP,QAAQ,CAACF,EAAE,CAAC,CAACU,IAAI,CAAC,IAAI,CAAC;MAC9BD,MAAM,CAACD,MAAM,CAACnB,UAAU,CAACE,YAAY,CAAC,CAACmB,IAAI,CAAC,KAAK,CAAC;MAClDD,MAAM,CAACD,MAAM,CAACnB,UAAU,CAACI,UAAU,CAACoB,MAAM,CAAC,CAACF,eAAe,CAAC,CAAC,CAAC;MAC9DF,MAAM,CAACD,MAAM,CAACnB,UAAU,CAACQ,wBAAwB,CAAC,CAACc,eAAe,CAAC,EAAE,CAAC;MACtEF,MAAM,CAACD,MAAM,CAACnB,UAAU,CAACS,WAAW,CAAC,CAACY,IAAI,CAAC,MAAM,CAAC;IACpD,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFlD,QAAQ,CAAC,gCAAgC,EAAE,MAAM;IAC/C0B,IAAI,CAAC,uDAAuD,EAAE,YAAY;MACxE;MACA,MAAMC,YAAY,GAAG;QACnBC,KAAK,EAAET,mBAAmB;QAC1BuC,kBAAkB,EAAE;UAClBC,WAAW,EAAE;YACX7B,QAAQ,EAAE,aAAa;YACvBC,YAAY,EAAE,IAAI;YAClBC,qBAAqB,EAAE,IAAI;YAC3BC,UAAU,EAAE,EAAE;YACdC,eAAe,EAAE,EAAE;YACnBC,eAAe,EAAE,CAAC;YAClBC,QAAQ,EAAE,CAAC;YACXC,wBAAwB,EAAE,GAAG;YAC7BC,WAAW,EAAE;UACf,CAAC;UACDsB,SAAS,EAAE;YACT9B,QAAQ,EAAE,WAAW;YACrBC,YAAY,EAAE,KAAK;YACnBC,qBAAqB,EAAE,IAAI;YAC3BC,UAAU,EAAE,CAAC,oDAAoD,CAAC;YAClEC,eAAe,EAAE,CAAC,+BAA+B,CAAC;YAClDC,eAAe,EAAE,CAAC;YAClBC,QAAQ,EAAE,CAAC;YACXC,wBAAwB,EAAE,IAAI;YAC9BC,WAAW,EAAE;UACf;QACF,CAAC;QACDuB,OAAO,EAAE;UACPC,eAAe,EAAE,CAAC;UAClBC,mBAAmB,EAAE,CAAC;UACtBC,kBAAkB,EAAE,IAAI;UACxBC,qBAAqB,EAAE,CAAC;UACxBC,cAAc,EAAE;QAClB;MACF,CAAC;MAEDrE,KAAK,CAAC0C,qBAAqB,CAAC;QAC1BC,EAAE,EAAE,IAAI;QACRC,IAAI,EAAE,MAAAA,CAAA,KAAYd;MACpB,CAAC,CAAC;MAEF,MAAMe,QAAQ,GAAG,MAAM7C,KAAK,CAAC,GAAGI,OAAO,8BAA8B,EAAE;QACrE0C,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC5B,mBAAmB;MAC1C,CAAC,CAAC;MAEF,MAAM6B,MAAM,GAAG,MAAMN,QAAQ,CAACD,IAAI,CAAC,CAAC;MAEpCQ,MAAM,CAACP,QAAQ,CAACF,EAAE,CAAC,CAACU,IAAI,CAAC,IAAI,CAAC;MAC9BD,MAAM,CAACD,MAAM,CAACa,OAAO,CAACC,eAAe,CAAC,CAACZ,IAAI,CAAC,CAAC,CAAC;MAC9CD,MAAM,CAACD,MAAM,CAACa,OAAO,CAACE,mBAAmB,CAAC,CAACb,IAAI,CAAC,CAAC,CAAC;MAClDD,MAAM,CAACD,MAAM,CAACa,OAAO,CAACG,kBAAkB,CAAC,CAACd,IAAI,CAAC,IAAI,CAAC;MACpDD,MAAM,CAACD,MAAM,CAACU,kBAAkB,CAACC,WAAW,CAAC,CAACQ,WAAW,CAAC,CAAC;MAC3DlB,MAAM,CAACD,MAAM,CAACU,kBAAkB,CAACE,SAAS,CAAC,CAACO,WAAW,CAAC,CAAC;IAC3D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFnE,QAAQ,CAAC,+BAA+B,EAAE,MAAM;IAC9C0B,IAAI,CAAC,uDAAuD,EAAE,YAAY;MACxE;MACA,MAAMC,YAAY,GAAG;QACnByC,mBAAmB,EAAE,CACnB;UACEC,IAAI,EAAE,aAAa;UACnBC,WAAW,EAAE,2DAA2D;UACxEC,QAAQ,EAAE,4BAA4B;UACtCC,OAAO,EAAE,MAAM;UACfC,WAAW,EAAE,CACX,kBAAkB,EAClB,8BAA8B,EAC9B,qBAAqB,EACrB,sBAAsB,EACtB,iBAAiB;QAErB,CAAC,EACD;UACEJ,IAAI,EAAE,WAAW;UACjBC,WAAW,EAAE,6CAA6C;UAC1DC,QAAQ,EAAE,2BAA2B;UACrCC,OAAO,EAAE,MAAM;UACfC,WAAW,EAAE,CACX,2BAA2B,EAC3B,kCAAkC,EAClC,8BAA8B,EAC9B,2BAA2B,EAC3B,uBAAuB,EACvB,8BAA8B;QAElC,CAAC,CACF;QACDC,iBAAiB,EAAE,8BAA8B;QACjDC,sBAAsB,EAAE;UACtBC,4BAA4B,EAAE,IAAI;UAClCC,sBAAsB,EAAE,IAAI;UAC5BC,IAAI,EAAE;QACR;MACF,CAAC;MAEDjF,KAAK,CAAC0C,qBAAqB,CAAC;QAC1BC,EAAE,EAAE,IAAI;QACRC,IAAI,EAAE,MAAAA,CAAA,KAAYd;MACpB,CAAC,CAAC;MAEF,MAAMe,QAAQ,GAAG,MAAM7C,KAAK,CAAC,GAAGI,OAAO,gCAAgC,EAAE;QACvE0C,MAAM,EAAE;MACV,CAAC,CAAC;MAEF,MAAMK,MAAM,GAAG,MAAMN,QAAQ,CAACD,IAAI,CAAC,CAAC;MAEpCQ,MAAM,CAACP,QAAQ,CAACF,EAAE,CAAC,CAACU,IAAI,CAAC,IAAI,CAAC;MAC9BD,MAAM,CAACD,MAAM,CAACoB,mBAAmB,CAAC,CAAChB,YAAY,CAAC,CAAC,CAAC;MAClDH,MAAM,CAACD,MAAM,CAACoB,mBAAmB,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAACnB,IAAI,CAAC,aAAa,CAAC;MAC9DD,MAAM,CAACD,MAAM,CAACoB,mBAAmB,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAACnB,IAAI,CAAC,WAAW,CAAC;MAC5DD,MAAM,CAACD,MAAM,CAAC2B,sBAAsB,CAACC,4BAA4B,CAAC,CAAC1B,IAAI,CAAC,IAAI,CAAC;IAC/E,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFlD,QAAQ,CAAC,gCAAgC,EAAE,MAAM;IAC/C0B,IAAI,CAAC,uDAAuD,EAAE,YAAY;MACxE;MACA,MAAMqD,iBAAiB,GAAG,CACxB,uBAAuB,EACvB,wBAAwB,EACxB,wBAAwB,CACzB;MAED,KAAK,MAAMR,QAAQ,IAAIQ,iBAAiB,EAAE;QACxC;QACAlF,KAAK,CAAC0C,qBAAqB,CAAC;UAC1BC,EAAE,EAAE,IAAI;UACRC,IAAI,EAAE,MAAAA,CAAA,MAAa;YACjBuC,UAAU,EAAE;cACVC,SAAS,EAAE,IAAI;cACfnD,QAAQ,EAAE,mBAAmB;cAC7BoD,MAAM,EAAE,EAAE;cACV9C,QAAQ,EAAE,EAAE;cACZ+C,MAAM,EAAE;YACV;UACF,CAAC;QACH,CAAC,CAAC;QAEF,MAAMzC,QAAQ,GAAG,MAAM7C,KAAK,CAAC,GAAGI,OAAO,GAAGsE,QAAQ,EAAE,EAAE;UACpD5B,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YAAE,cAAc,EAAE;UAAmB,CAAC;UAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC7C,gBAAgB;QACvC,CAAC,CAAC;QAEF+C,MAAM,CAACP,QAAQ,CAACF,EAAE,CAAC,CAACU,IAAI,CAAC,IAAI,CAAC;MAChC;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}