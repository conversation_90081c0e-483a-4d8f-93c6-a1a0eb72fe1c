ec2337c97c36b1977d63b777e3993126
"use strict";

/**
 * System Analysis Examples
 *
 * Practical examples demonstrating the use of Phase 3 Priority 3: Advanced System Analysis Tools
 * Shows real-world usage patterns and integration with existing SizeWise Suite components.
 *
 * @version 3.0.0
 * <AUTHOR> Suite Development Team
 */
/* istanbul ignore next */
function cov_1zsdp0zatz() {
  var path = "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\examples\\SystemAnalysisExamples.ts";
  var hash = "e876884ab3410cbdbd498dfd60f3c271a8dde445";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\examples\\SystemAnalysisExamples.ts",
    statementMap: {
      "0": {
        start: {
          line: 11,
          column: 0
        },
        end: {
          line: 11,
          column: 62
        }
      },
      "1": {
        start: {
          line: 12,
          column: 0
        },
        end: {
          line: 12,
          column: 40
        }
      },
      "2": {
        start: {
          line: 13,
          column: 0
        },
        end: {
          line: 13,
          column: 70
        }
      },
      "3": {
        start: {
          line: 14,
          column: 0
        },
        end: {
          line: 14,
          column: 80
        }
      },
      "4": {
        start: {
          line: 15,
          column: 0
        },
        end: {
          line: 15,
          column: 58
        }
      },
      "5": {
        start: {
          line: 16,
          column: 42
        },
        end: {
          line: 16,
          column: 87
        }
      },
      "6": {
        start: {
          line: 17,
          column: 41
        },
        end: {
          line: 17,
          column: 85
        }
      },
      "7": {
        start: {
          line: 18,
          column: 38
        },
        end: {
          line: 18,
          column: 79
        }
      },
      "8": {
        start: {
          line: 19,
          column: 46
        },
        end: {
          line: 19,
          column: 95
        }
      },
      "9": {
        start: {
          line: 20,
          column: 35
        },
        end: {
          line: 20,
          column: 73
        }
      },
      "10": {
        start: {
          line: 21,
          column: 35
        },
        end: {
          line: 21,
          column: 73
        }
      },
      "11": {
        start: {
          line: 22,
          column: 30
        },
        end: {
          line: 22,
          column: 69
        }
      },
      "12": {
        start: {
          line: 30,
          column: 4
        },
        end: {
          line: 30,
          column: 76
        }
      },
      "13": {
        start: {
          line: 32,
          column: 25
        },
        end: {
          line: 65,
          column: 5
        }
      },
      "14": {
        start: {
          line: 66,
          column: 4
        },
        end: {
          line: 66,
          column: 58
        }
      },
      "15": {
        start: {
          line: 67,
          column: 4
        },
        end: {
          line: 67,
          column: 103
        }
      },
      "16": {
        start: {
          line: 68,
          column: 4
        },
        end: {
          line: 68,
          column: 95
        }
      },
      "17": {
        start: {
          line: 70,
          column: 33
        },
        end: {
          line: 109,
          column: 6
        }
      },
      "18": {
        start: {
          line: 110,
          column: 4
        },
        end: {
          line: 110,
          column: 56
        }
      },
      "19": {
        start: {
          line: 111,
          column: 4
        },
        end: {
          line: 111,
          column: 102
        }
      },
      "20": {
        start: {
          line: 112,
          column: 4
        },
        end: {
          line: 112,
          column: 95
        }
      },
      "21": {
        start: {
          line: 113,
          column: 4
        },
        end: {
          line: 113,
          column: 95
        }
      },
      "22": {
        start: {
          line: 115,
          column: 31
        },
        end: {
          line: 158,
          column: 5
        }
      },
      "23": {
        start: {
          line: 160,
          column: 32
        },
        end: {
          line: 160,
          column: 162
        }
      },
      "24": {
        start: {
          line: 161,
          column: 4
        },
        end: {
          line: 161,
          column: 56
        }
      },
      "25": {
        start: {
          line: 162,
          column: 4
        },
        end: {
          line: 162,
          column: 58
        }
      },
      "26": {
        start: {
          line: 163,
          column: 4
        },
        end: {
          line: 163,
          column: 112
        }
      },
      "27": {
        start: {
          line: 164,
          column: 4
        },
        end: {
          line: 164,
          column: 90
        }
      },
      "28": {
        start: {
          line: 165,
          column: 4
        },
        end: {
          line: 165,
          column: 108
        }
      },
      "29": {
        start: {
          line: 166,
          column: 4
        },
        end: {
          line: 166,
          column: 93
        }
      },
      "30": {
        start: {
          line: 167,
          column: 4
        },
        end: {
          line: 167,
          column: 100
        }
      },
      "31": {
        start: {
          line: 169,
          column: 27
        },
        end: {
          line: 169,
          column: 154
        }
      },
      "32": {
        start: {
          line: 170,
          column: 4
        },
        end: {
          line: 170,
          column: 55
        }
      },
      "33": {
        start: {
          line: 171,
          column: 4
        },
        end: {
          line: 171,
          column: 126
        }
      },
      "34": {
        start: {
          line: 172,
          column: 4
        },
        end: {
          line: 172,
          column: 109
        }
      },
      "35": {
        start: {
          line: 173,
          column: 4
        },
        end: {
          line: 173,
          column: 110
        }
      },
      "36": {
        start: {
          line: 174,
          column: 4
        },
        end: {
          line: 174,
          column: 122
        }
      },
      "37": {
        start: {
          line: 175,
          column: 4
        },
        end: {
          line: 175,
          column: 155
        }
      },
      "38": {
        start: {
          line: 177,
          column: 25
        },
        end: {
          line: 185,
          column: 6
        }
      },
      "39": {
        start: {
          line: 186,
          column: 4
        },
        end: {
          line: 186,
          column: 52
        }
      },
      "40": {
        start: {
          line: 187,
          column: 4
        },
        end: {
          line: 187,
          column: 97
        }
      },
      "41": {
        start: {
          line: 188,
          column: 4
        },
        end: {
          line: 188,
          column: 116
        }
      },
      "42": {
        start: {
          line: 189,
          column: 4
        },
        end: {
          line: 189,
          column: 120
        }
      },
      "43": {
        start: {
          line: 190,
          column: 4
        },
        end: {
          line: 190,
          column: 122
        }
      },
      "44": {
        start: {
          line: 191,
          column: 4
        },
        end: {
          line: 191,
          column: 93
        }
      },
      "45": {
        start: {
          line: 192,
          column: 4
        },
        end: {
          line: 192,
          column: 110
        }
      },
      "46": {
        start: {
          line: 193,
          column: 4
        },
        end: {
          line: 193,
          column: 111
        }
      },
      "47": {
        start: {
          line: 195,
          column: 34
        },
        end: {
          line: 211,
          column: 6
        }
      },
      "48": {
        start: {
          line: 212,
          column: 4
        },
        end: {
          line: 212,
          column: 60
        }
      },
      "49": {
        start: {
          line: 213,
          column: 4
        },
        end: {
          line: 213,
          column: 135
        }
      },
      "50": {
        start: {
          line: 214,
          column: 4
        },
        end: {
          line: 214,
          column: 140
        }
      },
      "51": {
        start: {
          line: 215,
          column: 4
        },
        end: {
          line: 215,
          column: 134
        }
      },
      "52": {
        start: {
          line: 216,
          column: 4
        },
        end: {
          line: 216,
          column: 134
        }
      },
      "53": {
        start: {
          line: 217,
          column: 4
        },
        end: {
          line: 217,
          column: 119
        }
      },
      "54": {
        start: {
          line: 218,
          column: 4
        },
        end: {
          line: 218,
          column: 132
        }
      },
      "55": {
        start: {
          line: 220,
          column: 31
        },
        end: {
          line: 226,
          column: 6
        }
      },
      "56": {
        start: {
          line: 227,
          column: 4
        },
        end: {
          line: 227,
          column: 48
        }
      },
      "57": {
        start: {
          line: 228,
          column: 4
        },
        end: {
          line: 228,
          column: 112
        }
      },
      "58": {
        start: {
          line: 229,
          column: 4
        },
        end: {
          line: 229,
          column: 96
        }
      },
      "59": {
        start: {
          line: 230,
          column: 4
        },
        end: {
          line: 230,
          column: 91
        }
      },
      "60": {
        start: {
          line: 231,
          column: 4
        },
        end: {
          line: 231,
          column: 100
        }
      },
      "61": {
        start: {
          line: 232,
          column: 4
        },
        end: {
          line: 232,
          column: 91
        }
      },
      "62": {
        start: {
          line: 233,
          column: 4
        },
        end: {
          line: 233,
          column: 78
        }
      },
      "63": {
        start: {
          line: 234,
          column: 4
        },
        end: {
          line: 234,
          column: 110
        }
      },
      "64": {
        start: {
          line: 236,
          column: 4
        },
        end: {
          line: 236,
          column: 44
        }
      },
      "65": {
        start: {
          line: 237,
          column: 4
        },
        end: {
          line: 237,
          column: 48
        }
      },
      "66": {
        start: {
          line: 238,
          column: 4
        },
        end: {
          line: 238,
          column: 104
        }
      },
      "67": {
        start: {
          line: 239,
          column: 4
        },
        end: {
          line: 239,
          column: 108
        }
      },
      "68": {
        start: {
          line: 240,
          column: 4
        },
        end: {
          line: 240,
          column: 112
        }
      },
      "69": {
        start: {
          line: 241,
          column: 4
        },
        end: {
          line: 241,
          column: 134
        }
      },
      "70": {
        start: {
          line: 242,
          column: 4
        },
        end: {
          line: 242,
          column: 104
        }
      },
      "71": {
        start: {
          line: 243,
          column: 33
        },
        end: {
          line: 245,
          column: 49
        }
      },
      "72": {
        start: {
          line: 246,
          column: 4
        },
        end: {
          line: 246,
          column: 66
        }
      },
      "73": {
        start: {
          line: 247,
          column: 4
        },
        end: {
          line: 247,
          column: 42
        }
      },
      "74": {
        start: {
          line: 256,
          column: 4
        },
        end: {
          line: 256,
          column: 74
        }
      },
      "75": {
        start: {
          line: 257,
          column: 34
        },
        end: {
          line: 290,
          column: 5
        }
      },
      "76": {
        start: {
          line: 292,
          column: 35
        },
        end: {
          line: 335,
          column: 5
        }
      },
      "77": {
        start: {
          line: 337,
          column: 32
        },
        end: {
          line: 337,
          column: 175
        }
      },
      "78": {
        start: {
          line: 338,
          column: 27
        },
        end: {
          line: 338,
          column: 167
        }
      },
      "79": {
        start: {
          line: 339,
          column: 34
        },
        end: {
          line: 339,
          column: 178
        }
      },
      "80": {
        start: {
          line: 340,
          column: 4
        },
        end: {
          line: 340,
          column: 52
        }
      },
      "81": {
        start: {
          line: 341,
          column: 4
        },
        end: {
          line: 341,
          column: 104
        }
      },
      "82": {
        start: {
          line: 342,
          column: 4
        },
        end: {
          line: 342,
          column: 109
        }
      },
      "83": {
        start: {
          line: 343,
          column: 4
        },
        end: {
          line: 343,
          column: 134
        }
      },
      "84": {
        start: {
          line: 344,
          column: 4
        },
        end: {
          line: 344,
          column: 119
        }
      },
      "85": {
        start: {
          line: 345,
          column: 4
        },
        end: {
          line: 345,
          column: 134
        }
      },
      "86": {
        start: {
          line: 347,
          column: 4
        },
        end: {
          line: 347,
          column: 63
        }
      },
      "87": {
        start: {
          line: 348,
          column: 4
        },
        end: {
          line: 348,
          column: 69
        }
      },
      "88": {
        start: {
          line: 349,
          column: 4
        },
        end: {
          line: 349,
          column: 125
        }
      },
      "89": {
        start: {
          line: 350,
          column: 4
        },
        end: {
          line: 350,
          column: 84
        }
      },
      "90": {
        start: {
          line: 351,
          column: 4
        },
        end: {
          line: 351,
          column: 54
        }
      },
      "91": {
        start: {
          line: 360,
          column: 4
        },
        end: {
          line: 360,
          column: 70
        }
      },
      "92": {
        start: {
          line: 362,
          column: 27
        },
        end: {
          line: 390,
          column: 5
        }
      },
      "93": {
        start: {
          line: 392,
          column: 28
        },
        end: {
          line: 409,
          column: 5
        }
      },
      "94": {
        start: {
          line: 410,
          column: 28
        },
        end: {
          line: 434,
          column: 5
        }
      },
      "95": {
        start: {
          line: 435,
          column: 4
        },
        end: {
          line: 435,
          column: 51
        }
      },
      "96": {
        start: {
          line: 437,
          column: 20
        },
        end: {
          line: 441,
          column: 5
        }
      },
      "97": {
        start: {
          line: 442,
          column: 4
        },
        end: {
          line: 452,
          column: 5
        }
      },
      "98": {
        start: {
          line: 443,
          column: 8
        },
        end: {
          line: 443,
          column: 46
        }
      },
      "99": {
        start: {
          line: 445,
          column: 20
        },
        end: {
          line: 445,
          column: 54
        }
      },
      "100": {
        start: {
          line: 446,
          column: 27
        },
        end: {
          line: 446,
          column: 79
        }
      },
      "101": {
        start: {
          line: 447,
          column: 8
        },
        end: {
          line: 447,
          column: 62
        }
      },
      "102": {
        start: {
          line: 448,
          column: 8
        },
        end: {
          line: 448,
          column: 71
        }
      },
      "103": {
        start: {
          line: 449,
          column: 8
        },
        end: {
          line: 449,
          column: 102
        }
      },
      "104": {
        start: {
          line: 450,
          column: 8
        },
        end: {
          line: 450,
          column: 96
        }
      },
      "105": {
        start: {
          line: 451,
          column: 8
        },
        end: {
          line: 451,
          column: 24
        }
      },
      "106": {
        start: {
          line: 453,
          column: 4
        },
        end: {
          line: 453,
          column: 48
        }
      },
      "107": {
        start: {
          line: 454,
          column: 4
        },
        end: {
          line: 454,
          column: 74
        }
      },
      "108": {
        start: {
          line: 455,
          column: 4
        },
        end: {
          line: 455,
          column: 81
        }
      },
      "109": {
        start: {
          line: 456,
          column: 4
        },
        end: {
          line: 456,
          column: 81
        }
      },
      "110": {
        start: {
          line: 457,
          column: 4
        },
        end: {
          line: 457,
          column: 50
        }
      },
      "111": {
        start: {
          line: 460,
          column: 0
        },
        end: {
          line: 464,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "completeOfficeAnalysisExample",
        decl: {
          start: {
            line: 29,
            column: 15
          },
          end: {
            line: 29,
            column: 44
          }
        },
        loc: {
          start: {
            line: 29,
            column: 47
          },
          end: {
            line: 248,
            column: 1
          }
        },
        line: 29
      },
      "1": {
        name: "highPerformanceOptimizationExample",
        decl: {
          start: {
            line: 255,
            column: 15
          },
          end: {
            line: 255,
            column: 49
          }
        },
        loc: {
          start: {
            line: 255,
            column: 52
          },
          end: {
            line: 352,
            column: 1
          }
        },
        line: 255
      },
      "2": {
        name: "retrofitAnalysisExample",
        decl: {
          start: {
            line: 359,
            column: 15
          },
          end: {
            line: 359,
            column: 38
          }
        },
        loc: {
          start: {
            line: 359,
            column: 41
          },
          end: {
            line: 458,
            column: 1
          }
        },
        line: 359
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 449,
            column: 40
          },
          end: {
            line: 449,
            column: 98
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 449,
            column: 40
          },
          end: {
            line: 449,
            column: 89
          }
        }, {
          start: {
            line: 449,
            column: 93
          },
          end: {
            line: 449,
            column: 98
          }
        }],
        line: 449
      },
      "1": {
        loc: {
          start: {
            line: 450,
            column: 38
          },
          end: {
            line: 450,
            column: 92
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 450,
            column: 38
          },
          end: {
            line: 450,
            column: 83
          }
        }, {
          start: {
            line: 450,
            column: 87
          },
          end: {
            line: 450,
            column: 92
          }
        }],
        line: 450
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\examples\\SystemAnalysisExamples.ts",
      mappings: ";AAAA;;;;;;;;GAQG;;;AAuBH,sEAkQC;AAQD,gFAiHC;AAQD,0DA4GC;AApgBD,wFAAqF;AACrF,sFAAmF;AACnF,gFAA6E;AAC7E,gGAA6F;AAC7F,0EAAuE;AACvE,0EAAuE;AAGvE,sEAKsC;AAEtC;;;;;GAKG;AACI,KAAK,UAAU,6BAA6B;IACjD,OAAO,CAAC,GAAG,CAAC,0DAA0D,CAAC,CAAC;IAExE,0CAA0C;IAC1C,MAAM,YAAY,GAAwB;QACxC,EAAE,EAAE,qBAAqB;QACzB,IAAI,EAAE,0CAA0C;QAChD,UAAU,EAAE,YAAY;QACxB,gBAAgB,EAAE;YAChB,aAAa,EAAE,KAAK,EAAE,MAAM;YAC5B,cAAc,EAAE,GAAG,EAAE,WAAW;YAChC,iBAAiB,EAAE,EAAE,EAAE,KAAK;YAC5B,cAAc,EAAE,EAAE,EAAE,OAAO;YAC3B,SAAS,EAAE,GAAG,EAAE,qBAAqB;YACrC,UAAU,EAAE,KAAK,CAAC,SAAS;SAC5B;QACD,iBAAiB,EAAE;YACjB,KAAK,EAAE,aAAa;YACpB,QAAQ,EAAE,kBAAkB;YAC5B,UAAU,EAAE;gBACV,IAAI,EAAE,YAAY;gBAClB,SAAS,EAAE,CAAC;gBACZ,MAAM,EAAE,GAAG;aACZ;YACD,YAAY,EAAE,SAAS;SACxB;QACD,mBAAmB,EAAE;YACnB,cAAc,EAAE,IAAI,EAAE,aAAa;YACnC,UAAU,EAAE,GAAG;YACf,iBAAiB,EAAE,IAAI;YACvB,mBAAmB,EAAE,UAAU;SAChC;QACD,QAAQ,EAAE;YACR,WAAW,EAAE,IAAI;YACjB,MAAM,EAAE,OAAO;YACf,YAAY,EAAE,SAAS;SACxB;KACF,CAAC;IAEF,OAAO,CAAC,GAAG,CAAC,qBAAqB,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC;IACtD,OAAO,CAAC,GAAG,CAAC,mBAAmB,YAAY,CAAC,gBAAgB,CAAC,aAAa,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;IACnG,OAAO,CAAC,GAAG,CAAC,oBAAoB,YAAY,CAAC,gBAAgB,CAAC,cAAc,aAAa,CAAC,CAAC;IAE3F,sEAAsE;IACtE,MAAM,oBAAoB,GAAG,MAAM,mDAAwB,CAAC,uBAAuB,CAAC;QAClF,OAAO,EAAE,YAAY,CAAC,gBAAgB,CAAC,aAAa;QACpD,YAAY,EAAE;YACZ;gBACE,EAAE,EAAE,mBAAmB;gBACvB,MAAM,EAAE,GAAG;gBACX,iBAAiB,EAAE,GAAG;gBACtB,SAAS,EAAE,MAAM;gBACjB,IAAI,EAAE,GAAG;gBACT,QAAQ,EAAE,YAAY,CAAC,gBAAgB,CAAC,aAAa,GAAG,GAAG,GAAG,EAAE;aACjE;YACD;gBACE,EAAE,EAAE,eAAe;gBACnB,MAAM,EAAE,EAAE;gBACV,iBAAiB,EAAE,GAAG;gBACtB,SAAS,EAAE,MAAM;gBACjB,IAAI,EAAE,IAAI;gBACV,QAAQ,EAAE,IAAI,GAAG,IAAI,GAAG,EAAE;aAC3B;SACF;QACD,QAAQ,EAAE;YACR;gBACE,EAAE,EAAE,YAAY;gBAChB,IAAI,EAAE,sBAAsB;gBAC5B,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,YAAY,CAAC,gBAAgB,CAAC,aAAa;aACrD;YACD;gBACE,EAAE,EAAE,YAAY;gBAChB,IAAI,EAAE,YAAY;gBAClB,OAAO,EAAE,GAAG;gBACZ,OAAO,EAAE,IAAI;aACd;SACF;QACD,aAAa,EAAE;YACb,OAAO,EAAE,YAAY,CAAC,gBAAgB,CAAC,UAAU;YACjD,SAAS,EAAE,MAAM;YACjB,WAAW,EAAE,YAAY,CAAC,gBAAgB,CAAC,iBAAiB;SAC7D;KACF,CAAC,CAAC;IAEH,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;IACpD,OAAO,CAAC,GAAG,CAAC,wBAAwB,oBAAoB,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;IAClG,OAAO,CAAC,GAAG,CAAC,oBAAoB,oBAAoB,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;IAC3F,OAAO,CAAC,GAAG,CAAC,mBAAmB,oBAAoB,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC;IAE3F,6DAA6D;IAC7D,MAAM,kBAAkB,GAAuB;QAC7C,mBAAmB,EAAE;YACnB,KAAK,EAAE,oBAAoB,CAAC,iBAAiB;YAC7C,KAAK,EAAE,OAAO;YACd,QAAQ,EAAE,IAAI;YACd,iBAAiB,EAAE,YAAY;YAC/B,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB;QACD,iBAAiB,EAAE;YACjB,KAAK,EAAE,EAAE;YACT,KAAK,EAAE,SAAS;YAChB,QAAQ,EAAE,GAAG;YACb,iBAAiB,EAAE,YAAY;YAC/B,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB;QACD,cAAc,EAAE;YACd,KAAK,EAAE,EAAE;YACT,KAAK,EAAE,SAAS;YAChB,QAAQ,EAAE,IAAI;YACd,iBAAiB,EAAE,WAAW;YAC9B,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB;QACD,gBAAgB,EAAE;YAChB,KAAK,EAAE,EAAE;YACT,KAAK,EAAE,SAAS;YAChB,QAAQ,EAAE,GAAG;YACb,iBAAiB,EAAE,YAAY;YAC/B,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB;QACD,oBAAoB,EAAE;YACpB,KAAK,EAAE,EAAE;YACT,KAAK,EAAE,OAAO;YACd,QAAQ,EAAE,GAAG;YACb,iBAAiB,EAAE,YAAY;YAC/B,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB;QACD,aAAa,EAAE;YACb,KAAK,EAAE,EAAE;YACT,KAAK,EAAE,SAAS;YAChB,QAAQ,EAAE,IAAI;YACd,iBAAiB,EAAE,YAAY;YAC/B,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB;KACF,CAAC;IAEF,8CAA8C;IAC9C,MAAM,mBAAmB,GAAG,MAAM,iEAA+B,CAAC,wBAAwB,CACxF,YAAY,EACZ,kBAAkB,CACnB,CAAC;IAEF,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;IACpD,OAAO,CAAC,GAAG,CAAC,gBAAgB,mBAAmB,CAAC,EAAE,EAAE,CAAC,CAAC;IACtD,OAAO,CAAC,GAAG,CAAC,8BAA8B,mBAAmB,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,KAAK,GAAG,CAAC,CAAC;IAC5G,OAAO,CAAC,GAAG,CAAC,sBAAsB,mBAAmB,CAAC,aAAa,CAAC,cAAc,EAAE,CAAC,CAAC;IACtF,OAAO,CAAC,GAAG,CAAC,yBAAyB,mBAAmB,CAAC,mBAAmB,CAAC,UAAU,eAAe,CAAC,CAAC;IACxG,OAAO,CAAC,GAAG,CAAC,uBAAuB,mBAAmB,CAAC,MAAM,CAAC,MAAM,mBAAmB,CAAC,CAAC;IACzF,OAAO,CAAC,GAAG,CAAC,oBAAoB,mBAAmB,CAAC,eAAe,CAAC,MAAM,oBAAoB,CAAC,CAAC;IAEhG,6CAA6C;IAC7C,MAAM,cAAc,GAAG,MAAM,+DAA8B,CAAC,uBAAuB,CACjF,YAAY,EACZ,kBAAkB,CACnB,CAAC;IAEF,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;IACnD,OAAO,CAAC,GAAG,CAAC,8BAA8B,cAAc,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,KAAK,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;IAC1H,OAAO,CAAC,GAAG,CAAC,uBAAuB,cAAc,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;IACzG,OAAO,CAAC,GAAG,CAAC,wBAAwB,cAAc,CAAC,WAAW,CAAC,YAAY,CAAC,SAAS,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;IAC1G,OAAO,CAAC,GAAG,CAAC,qBAAqB,cAAc,CAAC,eAAe,CAAC,cAAc,CAAC,KAAK,CAAC,cAAc,EAAE,eAAe,CAAC,CAAC;IACtH,OAAO,CAAC,GAAG,CAAC,qBAAqB,cAAc,CAAC,mBAAmB,CAAC,aAAa,MAAM,cAAc,CAAC,mBAAmB,CAAC,UAAU,iBAAiB,CAAC,CAAC;IAEvJ,0CAA0C;IAC1C,MAAM,YAAY,GAAG,MAAM,yDAA2B,CAAC,qBAAqB,CAC1E,YAAY,EACZ,cAAc,EACd;QACE,eAAe,EAAE,EAAE;QACnB,YAAY,EAAE,IAAI;QAClB,aAAa,EAAE,KAAK;QACpB,oBAAoB,EAAE,IAAI;QAC1B,QAAQ,EAAE,KAAK;QACf,cAAc,EAAE,wCAAkB,CAAC,iBAAiB;QACpD,gBAAgB,EAAE,sCAAgB,CAAC,MAAM;KAC1C,CACF,CAAC;IAEF,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;IAChD,OAAO,CAAC,GAAG,CAAC,kBAAkB,YAAY,CAAC,YAAY,CAAC,gBAAgB,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;IAC7F,OAAO,CAAC,GAAG,CAAC,kCAAkC,YAAY,CAAC,cAAc,CAAC,iBAAiB,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;IAChH,OAAO,CAAC,GAAG,CAAC,oCAAoC,YAAY,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;IACpH,OAAO,CAAC,GAAG,CAAC,kCAAkC,YAAY,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;IACtH,OAAO,CAAC,GAAG,CAAC,kBAAkB,YAAY,CAAC,oBAAoB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACzF,OAAO,CAAC,GAAG,CAAC,0BAA0B,YAAY,CAAC,oBAAoB,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;IAC1G,OAAO,CAAC,GAAG,CAAC,sCAAsC,YAAY,CAAC,eAAe,CAAC,MAAM,oBAAoB,CAAC,CAAC;IAE3G,kDAAkD;IAClD,MAAM,qBAAqB,GAAG,MAAM,yEAAmC,CAAC,yBAAyB,CAC/F,YAAY,EACZ,cAAc,EACd;QACE,oBAAoB,EAAE,IAAI;QAC1B,WAAW,EAAE,UAAU;QACvB,iBAAiB,EAAE,EAAE;QACrB,YAAY,EAAE,GAAG;KAClB,EACD;QACE,MAAM,EAAE,OAAO;QACf,WAAW,EAAE,IAAI;QACjB,OAAO,EAAE;YACP,SAAS,EAAE,EAAE;YACb,OAAO,EAAE,EAAE;YACX,UAAU,EAAE,EAAE;YACd,IAAI,EAAE,CAAC;YACP,KAAK,EAAE,CAAC;SACT;QACD,gBAAgB,EAAE,CAAC,qBAAqB,EAAE,sCAAsC,CAAC;KAClF,CACF,CAAC;IAEF,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;IACxD,OAAO,CAAC,GAAG,CAAC,2BAA2B,qBAAqB,CAAC,eAAe,CAAC,cAAc,CAAC,KAAK,CAAC,cAAc,EAAE,eAAe,CAAC,CAAC;IACnI,OAAO,CAAC,GAAG,CAAC,0BAA0B,qBAAqB,CAAC,eAAe,CAAC,oBAAoB,CAAC,KAAK,CAAC,cAAc,EAAE,eAAe,CAAC,CAAC;IACxI,OAAO,CAAC,GAAG,CAAC,uBAAuB,qBAAqB,CAAC,eAAe,CAAC,iBAAiB,CAAC,KAAK,CAAC,cAAc,EAAE,eAAe,CAAC,CAAC;IAClI,OAAO,CAAC,GAAG,CAAC,wBAAwB,qBAAqB,CAAC,qBAAqB,CAAC,kBAAkB,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;IAClI,OAAO,CAAC,GAAG,CAAC,mBAAmB,qBAAqB,CAAC,qBAAqB,CAAC,sBAAsB,CAAC,aAAa,EAAE,CAAC,CAAC;IACnH,OAAO,CAAC,GAAG,CAAC,gCAAgC,qBAAqB,CAAC,eAAe,CAAC,mBAAmB,CAAC,MAAM,kBAAkB,CAAC,CAAC;IAEhI,sCAAsC;IACtC,MAAM,kBAAkB,GAAG,MAAM,mDAAwB,CAAC,yBAAyB,CACjF,YAAY,EACZ,kBAAkB,EAClB,cAAc,EACd,qBAAqB,EACrB,CAAC,aAAa,EAAE,QAAQ,EAAE,WAAW,CAAC,EACtC;QACE,OAAO,EAAE,IAAI;QACb,KAAK,EAAE,IAAI;QACX,IAAI,EAAE,SAAS;QACf,WAAW,EAAE,IAAI;QACjB,YAAY,EAAE,SAAS;KACxB,CACF,CAAC;IAEF,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;IAC5C,OAAO,CAAC,GAAG,CAAC,uBAAuB,kBAAkB,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAC5G,OAAO,CAAC,GAAG,CAAC,2BAA2B,kBAAkB,CAAC,gBAAgB,CAAC,aAAa,EAAE,CAAC,CAAC;IAC5F,OAAO,CAAC,GAAG,CAAC,sBAAsB,kBAAkB,CAAC,gBAAgB,CAAC,aAAa,EAAE,CAAC,CAAC;IACvF,OAAO,CAAC,GAAG,CAAC,2BAA2B,kBAAkB,CAAC,oBAAoB,CAAC,aAAa,EAAE,CAAC,CAAC;IAChG,OAAO,CAAC,GAAG,CAAC,oBAAoB,kBAAkB,CAAC,iBAAiB,CAAC,cAAc,EAAE,CAAC,CAAC;IACvF,OAAO,CAAC,GAAG,CAAC,aAAa,kBAAkB,CAAC,iBAAiB,CAAC,QAAQ,EAAE,CAAC,CAAC;IAC1E,OAAO,CAAC,GAAG,CAAC,+BAA+B,kBAAkB,CAAC,eAAe,CAAC,MAAM,oBAAoB,CAAC,CAAC;IAE1G,kCAAkC;IAClC,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;IACxC,OAAO,CAAC,GAAG,CAAC,WAAW,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC;IAC5C,OAAO,CAAC,GAAG,CAAC,sBAAsB,mBAAmB,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,KAAK,GAAG,CAAC,CAAC;IACpG,OAAO,CAAC,GAAG,CAAC,sBAAsB,cAAc,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;IACxG,OAAO,CAAC,GAAG,CAAC,wBAAwB,YAAY,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;IAC5G,OAAO,CAAC,GAAG,CAAC,wBAAwB,qBAAqB,CAAC,qBAAqB,CAAC,kBAAkB,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;IAClI,OAAO,CAAC,GAAG,CAAC,eAAe,kBAAkB,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAEpG,MAAM,oBAAoB,GAAG,mBAAmB,CAAC,eAAe,CAAC,MAAM;QAC3C,YAAY,CAAC,eAAe,CAAC,MAAM;QACnC,kBAAkB,CAAC,eAAe,CAAC,MAAM,CAAC;IACtE,OAAO,CAAC,GAAG,CAAC,0BAA0B,oBAAoB,EAAE,CAAC,CAAC;IAC9D,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;AACxC,CAAC;AAED;;;;;GAKG;AACI,KAAK,UAAU,kCAAkC;IACtD,OAAO,CAAC,GAAG,CAAC,wDAAwD,CAAC,CAAC;IAEtE,MAAM,qBAAqB,GAAwB;QACjD,EAAE,EAAE,sBAAsB;QAC1B,IAAI,EAAE,sCAAsC;QAC5C,UAAU,EAAE,YAAY;QACxB,gBAAgB,EAAE;YAChB,aAAa,EAAE,KAAK;YACpB,cAAc,EAAE,GAAG,EAAE,gCAAgC;YACrD,iBAAiB,EAAE,EAAE;YACrB,cAAc,EAAE,EAAE;YAClB,SAAS,EAAE,IAAI;YACf,UAAU,EAAE,KAAK;SAClB;QACD,iBAAiB,EAAE;YACjB,KAAK,EAAE,OAAO,EAAE,uBAAuB;YACvC,QAAQ,EAAE,kBAAkB;YAC5B,UAAU,EAAE;gBACV,IAAI,EAAE,uBAAuB;gBAC7B,SAAS,EAAE,CAAC;gBACZ,MAAM,EAAE,IAAI,CAAC,iBAAiB;aAC/B;YACD,YAAY,EAAE,cAAc,CAAC,mBAAmB;SACjD;QACD,mBAAmB,EAAE;YACnB,cAAc,EAAE,IAAI,EAAE,qBAAqB;YAC3C,UAAU,EAAE,GAAG,EAAE,0BAA0B;YAC3C,iBAAiB,EAAE,GAAG;YACtB,mBAAmB,EAAE,UAAU;SAChC;QACD,QAAQ,EAAE;YACR,WAAW,EAAE,IAAI;YACjB,MAAM,EAAE,OAAO;YACf,YAAY,EAAE,QAAQ;SACvB;KACF,CAAC;IAEF,oCAAoC;IACpC,MAAM,sBAAsB,GAAuB;QACjD,mBAAmB,EAAE;YACnB,KAAK,EAAE,GAAG;YACV,KAAK,EAAE,OAAO;YACd,QAAQ,EAAE,IAAI;YACd,iBAAiB,EAAE,UAAU;YAC7B,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB;QACD,iBAAiB,EAAE;YACjB,KAAK,EAAE,EAAE;YACT,KAAK,EAAE,SAAS;YAChB,QAAQ,EAAE,IAAI;YACd,iBAAiB,EAAE,UAAU;YAC7B,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB;QACD,cAAc,EAAE;YACd,KAAK,EAAE,EAAE;YACT,KAAK,EAAE,SAAS;YAChB,QAAQ,EAAE,GAAG;YACb,iBAAiB,EAAE,UAAU;YAC7B,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB;QACD,gBAAgB,EAAE;YAChB,KAAK,EAAE,EAAE;YACT,KAAK,EAAE,SAAS;YAChB,QAAQ,EAAE,IAAI;YACd,iBAAiB,EAAE,UAAU;YAC7B,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB;QACD,oBAAoB,EAAE;YACpB,KAAK,EAAE,EAAE;YACT,KAAK,EAAE,OAAO;YACd,QAAQ,EAAE,GAAG;YACb,iBAAiB,EAAE,YAAY;YAC/B,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB;QACD,aAAa,EAAE;YACb,KAAK,EAAE,EAAE;YACT,KAAK,EAAE,SAAS;YAChB,QAAQ,EAAE,GAAG;YACb,iBAAiB,EAAE,UAAU;YAC7B,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB;KACF,CAAC;IAEF,iCAAiC;IACjC,MAAM,mBAAmB,GAAG,MAAM,iEAA+B,CAAC,wBAAwB,CACxF,qBAAqB,EACrB,sBAAsB,CACvB,CAAC;IAEF,MAAM,cAAc,GAAG,MAAM,+DAA8B,CAAC,uBAAuB,CACjF,qBAAqB,EACrB,sBAAsB,CACvB,CAAC;IAEF,MAAM,qBAAqB,GAAG,MAAM,yEAAmC,CAAC,yBAAyB,CAC/F,qBAAqB,EACrB,cAAc,CACf,CAAC;IAEF,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;IAChD,OAAO,CAAC,GAAG,CAAC,sBAAsB,mBAAmB,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,KAAK,GAAG,CAAC,CAAC;IACpG,OAAO,CAAC,GAAG,CAAC,uBAAuB,cAAc,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;IACzG,OAAO,CAAC,GAAG,CAAC,wBAAwB,qBAAqB,CAAC,qBAAqB,CAAC,kBAAkB,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;IAClI,OAAO,CAAC,GAAG,CAAC,mBAAmB,qBAAqB,CAAC,qBAAqB,CAAC,sBAAsB,CAAC,aAAa,EAAE,CAAC,CAAC;IACnH,OAAO,CAAC,GAAG,CAAC,0BAA0B,qBAAqB,CAAC,qBAAqB,CAAC,sBAAsB,CAAC,mBAAmB,IAAI,CAAC,CAAC;IAElI,+BAA+B;IAC/B,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;IAC3D,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACjE,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC,IAAI,GAAG,cAAc,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACzH,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;IAChF,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;AACpD,CAAC;AAED;;;;;GAKG;AACI,KAAK,UAAU,uBAAuB;IAC3C,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAC;IAElE,yBAAyB;IACzB,MAAM,cAAc,GAAwB;QAC1C,EAAE,EAAE,qBAAqB;QACzB,IAAI,EAAE,uCAAuC;QAC7C,UAAU,EAAE,YAAY;QACxB,gBAAgB,EAAE;YAChB,aAAa,EAAE,KAAK;YACpB,cAAc,EAAE,GAAG,EAAE,8BAA8B;YACnD,iBAAiB,EAAE,EAAE;YACrB,cAAc,EAAE,EAAE;YAClB,SAAS,EAAE,GAAG;YACd,UAAU,EAAE,KAAK;SAClB;QACD,iBAAiB,EAAE;YACjB,KAAK,EAAE,aAAa;YACpB,QAAQ,EAAE,kBAAkB;YAC5B,UAAU,EAAE;gBACV,IAAI,EAAE,YAAY;gBAClB,SAAS,EAAE,CAAC,EAAE,qBAAqB;gBACnC,MAAM,EAAE,GAAG;aACZ;YACD,YAAY,EAAE,SAAS,CAAC,eAAe;SACxC;QACD,mBAAmB,EAAE;YACnB,cAAc,EAAE,IAAI,EAAE,wBAAwB;YAC9C,UAAU,EAAE,GAAG,EAAE,qBAAqB;YACtC,iBAAiB,EAAE,GAAG;YACtB,mBAAmB,EAAE,SAAS;SAC/B;KACF,CAAC;IAEF,0BAA0B;IAC1B,MAAM,eAAe,GAAwB;QAC3C,GAAG,cAAc;QACjB,EAAE,EAAE,mBAAmB;QACvB,IAAI,EAAE,4CAA4C;QAClD,gBAAgB,EAAE;YAChB,GAAG,cAAc,CAAC,gBAAgB;YAClC,cAAc,EAAE,GAAG,CAAC,wBAAwB;SAC7C;QACD,iBAAiB,EAAE;YACjB,GAAG,cAAc,CAAC,iBAAiB;YACnC,YAAY,EAAE,SAAS,CAAC,mBAAmB;SAC5C;QACD,mBAAmB,EAAE;YACnB,GAAG,cAAc,CAAC,mBAAmB;YACrC,cAAc,EAAE,IAAI,EAAE,kBAAkB;YACxC,UAAU,EAAE,IAAI;SACjB;KACF,CAAC;IAEF,MAAM,eAAe,GAAwB;QAC3C,GAAG,cAAc;QACjB,EAAE,EAAE,mBAAmB;QACvB,IAAI,EAAE,4CAA4C;QAClD,gBAAgB,EAAE;YAChB,GAAG,cAAc,CAAC,gBAAgB;YAClC,cAAc,EAAE,GAAG,CAAC,sBAAsB;SAC3C;QACD,iBAAiB,EAAE;YACjB,KAAK,EAAE,OAAO,EAAE,iBAAiB;YACjC,QAAQ,EAAE,kBAAkB;YAC5B,UAAU,EAAE;gBACV,IAAI,EAAE,uBAAuB;gBAC7B,SAAS,EAAE,GAAG;gBACd,MAAM,EAAE,IAAI;aACb;YACD,YAAY,EAAE,cAAc;SAC7B;QACD,mBAAmB,EAAE;YACnB,cAAc,EAAE,IAAI,EAAE,4BAA4B;YAClD,UAAU,EAAE,IAAI;YAChB,iBAAiB,EAAE,IAAI;YACvB,mBAAmB,EAAE,UAAU;SAChC;KACF,CAAC;IAEF,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;IAE/C,+CAA+C;IAC/C,MAAM,OAAO,GAAG;QACd,EAAE,MAAM,EAAE,cAAc,EAAE,IAAI,EAAE,iBAAiB,EAAE;QACnD,EAAE,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,mBAAmB,EAAE;QACtD,EAAE,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,mBAAmB,EAAE;KACvD,CAAC;IAEF,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;QAC7B,OAAO,CAAC,GAAG,CAAC,OAAO,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC;QAEtC,+DAA+D;QAC/D,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,cAAc,GAAG,GAAG,CAAC,CAAC,6BAA6B;QAC7E,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC;QAExE,OAAO,CAAC,GAAG,CAAC,kBAAkB,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QACtD,OAAO,CAAC,GAAG,CAAC,yBAAyB,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC/D,OAAO,CAAC,GAAG,CAAC,oBAAoB,MAAM,CAAC,MAAM,CAAC,mBAAmB,EAAE,cAAc,IAAI,KAAK,EAAE,CAAC,CAAC;QAC9F,OAAO,CAAC,GAAG,CAAC,kBAAkB,MAAM,CAAC,MAAM,CAAC,iBAAiB,EAAE,YAAY,IAAI,KAAK,EAAE,CAAC,CAAC;QACxF,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAClB,CAAC;IAED,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;IAC5C,OAAO,CAAC,GAAG,CAAC,wDAAwD,CAAC,CAAC;IACtE,OAAO,CAAC,GAAG,CAAC,+DAA+D,CAAC,CAAC;IAC7E,OAAO,CAAC,GAAG,CAAC,+DAA+D,CAAC,CAAC;IAC7E,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;AAChD,CAAC;AAED,yCAAyC;AAC5B,QAAA,sBAAsB,GAAG;IACpC,6BAA6B;IAC7B,kCAAkC;IAClC,uBAAuB;CACxB,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\examples\\SystemAnalysisExamples.ts"],
      sourcesContent: ["/**\r\n * System Analysis Examples\r\n * \r\n * Practical examples demonstrating the use of Phase 3 Priority 3: Advanced System Analysis Tools\r\n * Shows real-world usage patterns and integration with existing SizeWise Suite components.\r\n * \r\n * @version 3.0.0\r\n * <AUTHOR> Suite Development Team\r\n */\r\n\r\nimport { SystemPerformanceAnalysisEngine } from '../SystemPerformanceAnalysisEngine';\r\nimport { EnergyEfficiencyAnalysisEngine } from '../EnergyEfficiencyAnalysisEngine';\r\nimport { LifecycleCostAnalysisEngine } from '../LifecycleCostAnalysisEngine';\r\nimport { EnvironmentalImpactAssessmentEngine } from '../EnvironmentalImpactAssessmentEngine';\r\nimport { ComplianceCheckingEngine } from '../ComplianceCheckingEngine';\r\nimport { SystemPressureCalculator } from '../SystemPressureCalculator';\r\nimport { FittingLossCalculator } from '../FittingLossCalculator';\r\nimport { AirPropertiesCalculator } from '../AirPropertiesCalculator';\r\nimport {\r\n  SystemConfiguration,\r\n  PerformanceMetrics,\r\n  CostAnalysisMethod,\r\n  UncertaintyLevel\r\n} from '../types/SystemAnalysisTypes';\r\n\r\n/**\r\n * Example 1: Complete Office Building HVAC Analysis\r\n * \r\n * This example demonstrates a complete analysis workflow for a typical\r\n * office building HVAC system, from initial design through compliance checking.\r\n */\r\nexport async function completeOfficeAnalysisExample(): Promise<void> {\r\n  console.log('=== Complete Office Building HVAC Analysis Example ===\\n');\r\n\r\n  // Step 1: Define the system configuration\r\n  const officeSystem: SystemConfiguration = {\r\n    id: 'office_building_001',\r\n    name: '50,000 sq ft Office Building HVAC System',\r\n    systemType: 'supply_air',\r\n    designParameters: {\r\n      designAirflow: 25000, // CFM\r\n      designPressure: 4.2, // in. w.g.\r\n      designTemperature: 75, // \xB0F\r\n      designHumidity: 50, // % RH\r\n      elevation: 500, // ft above sea level\r\n      airDensity: 0.075 // lb/ft\xB3\r\n    },\r\n    ductConfiguration: {\r\n      shape: 'rectangular',\r\n      material: 'galvanized_steel',\r\n      insulation: {\r\n        type: 'fiberglass',\r\n        thickness: 2,\r\n        rValue: 6.0\r\n      },\r\n      sealingClass: 'class_a'\r\n    },\r\n    operatingConditions: {\r\n      operatingHours: 2800, // hours/year\r\n      loadFactor: 0.8,\r\n      seasonalVariation: 0.25,\r\n      maintenanceSchedule: 'standard'\r\n    },\r\n    location: {\r\n      climateZone: '4A',\r\n      region: 'US-IL',\r\n      jurisdiction: 'Chicago'\r\n    }\r\n  };\r\n\r\n  console.log(`Analyzing system: ${officeSystem.name}`);\r\n  console.log(`Design airflow: ${officeSystem.designParameters.designAirflow.toLocaleString()} CFM`);\r\n  console.log(`Design pressure: ${officeSystem.designParameters.designPressure} in. w.g.\\n`);\r\n\r\n  // Step 2: Calculate system pressure using existing Phase 1 components\r\n  const systemPressureResult = await SystemPressureCalculator.calculateSystemPressure({\r\n    airflow: officeSystem.designParameters.designAirflow,\r\n    ductSections: [\r\n      {\r\n        id: 'main_supply_trunk',\r\n        length: 200,\r\n        hydraulicDiameter: 2.0,\r\n        roughness: 0.0003,\r\n        area: 4.0,\r\n        velocity: officeSystem.designParameters.designAirflow / 4.0 / 60\r\n      },\r\n      {\r\n        id: 'branch_duct_1',\r\n        length: 80,\r\n        hydraulicDiameter: 1.2,\r\n        roughness: 0.0003,\r\n        area: 1.44,\r\n        velocity: 8000 / 1.44 / 60\r\n      }\r\n    ],\r\n    fittings: [\r\n      {\r\n        id: 'main_elbow',\r\n        type: 'elbow_90_rectangular',\r\n        kFactor: 0.25,\r\n        airflow: officeSystem.designParameters.designAirflow\r\n      },\r\n      {\r\n        id: 'branch_tee',\r\n        type: 'tee_branch',\r\n        kFactor: 0.4,\r\n        airflow: 8000\r\n      }\r\n    ],\r\n    airProperties: {\r\n      density: officeSystem.designParameters.airDensity,\r\n      viscosity: 1.2e-5,\r\n      temperature: officeSystem.designParameters.designTemperature\r\n    }\r\n  });\r\n\r\n  console.log('System Pressure Calculation Results:');\r\n  console.log(`Total pressure loss: ${systemPressureResult.totalPressureLoss.toFixed(2)} in. w.g.`);\r\n  console.log(`Friction losses: ${systemPressureResult.frictionLosses.toFixed(2)} in. w.g.`);\r\n  console.log(`Fitting losses: ${systemPressureResult.fittingLosses.toFixed(2)} in. w.g.\\n`);\r\n\r\n  // Step 3: Create performance metrics from calculated results\r\n  const performanceMetrics: PerformanceMetrics = {\r\n    totalSystemPressure: {\r\n      value: systemPressureResult.totalPressureLoss,\r\n      units: 'in_wg',\r\n      accuracy: 0.95,\r\n      measurementSource: 'calculated',\r\n      timestamp: new Date()\r\n    },\r\n    airflowEfficiency: {\r\n      value: 87,\r\n      units: 'percent',\r\n      accuracy: 0.9,\r\n      measurementSource: 'calculated',\r\n      timestamp: new Date()\r\n    },\r\n    fanPerformance: {\r\n      value: 82,\r\n      units: 'percent',\r\n      accuracy: 0.85,\r\n      measurementSource: 'estimated',\r\n      timestamp: new Date()\r\n    },\r\n    systemEfficiency: {\r\n      value: 84,\r\n      units: 'percent',\r\n      accuracy: 0.9,\r\n      measurementSource: 'calculated',\r\n      timestamp: new Date()\r\n    },\r\n    environmentalMetrics: {\r\n      value: 78,\r\n      units: 'score',\r\n      accuracy: 0.8,\r\n      measurementSource: 'calculated',\r\n      timestamp: new Date()\r\n    },\r\n    systemBalance: {\r\n      value: 92,\r\n      units: 'percent',\r\n      accuracy: 0.85,\r\n      measurementSource: 'calculated',\r\n      timestamp: new Date()\r\n    }\r\n  };\r\n\r\n  // Step 4: Perform system performance analysis\r\n  const performanceAnalysis = await SystemPerformanceAnalysisEngine.analyzeSystemPerformance(\r\n    officeSystem,\r\n    performanceMetrics\r\n  );\r\n\r\n  console.log('System Performance Analysis Results:');\r\n  console.log(`Analysis ID: ${performanceAnalysis.id}`);\r\n  console.log(`Overall system efficiency: ${performanceAnalysis.performanceMetrics.systemEfficiency.value}%`);\r\n  console.log(`Performance trend: ${performanceAnalysis.trendAnalysis.trendDirection}`);\r\n  console.log(`Benchmark percentile: ${performanceAnalysis.benchmarkComparison.percentile}th percentile`);\r\n  console.log(`Performance alerts: ${performanceAnalysis.alerts.length} alerts generated`);\r\n  console.log(`Recommendations: ${performanceAnalysis.recommendations.length} recommendations\\n`);\r\n\r\n  // Step 5: Perform energy efficiency analysis\r\n  const energyAnalysis = await EnergyEfficiencyAnalysisEngine.analyzeEnergyEfficiency(\r\n    officeSystem,\r\n    performanceMetrics\r\n  );\r\n\r\n  console.log('Energy Efficiency Analysis Results:');\r\n  console.log(`Annual energy consumption: ${energyAnalysis.energyConsumption.totalConsumption.value.toLocaleString()} kWh`);\r\n  console.log(`Specific fan power: ${energyAnalysis.efficiencyMetrics.specificFanPower.toFixed(2)} W/CFM`);\r\n  console.log(`Annual energy cost: $${energyAnalysis.energyCosts.currentCosts.totalCost.toLocaleString()}`);\r\n  console.log(`Carbon footprint: ${energyAnalysis.carbonFootprint.totalEmissions.value.toLocaleString()} kg CO2e/year`);\r\n  console.log(`Energy benchmark: ${energyAnalysis.benchmarkComparison.benchmarkType} - ${energyAnalysis.benchmarkComparison.percentile}th percentile\\n`);\r\n\r\n  // Step 6: Perform lifecycle cost analysis\r\n  const costAnalysis = await LifecycleCostAnalysisEngine.analyzeLifecycleCosts(\r\n    officeSystem,\r\n    energyAnalysis,\r\n    {\r\n      analysisHorizon: 20,\r\n      discountRate: 0.06,\r\n      inflationRate: 0.025,\r\n      energyEscalationRate: 0.03,\r\n      currency: 'USD',\r\n      analysisMethod: CostAnalysisMethod.NET_PRESENT_VALUE,\r\n      uncertaintyLevel: UncertaintyLevel.MEDIUM\r\n    }\r\n  );\r\n\r\n  console.log('Lifecycle Cost Analysis Results:');\r\n  console.log(`Initial cost: $${costAnalysis.initialCosts.totalInitialCost.toLocaleString()}`);\r\n  console.log(`20-year operating costs (PV): $${costAnalysis.operatingCosts.totalPresentValue.toLocaleString()}`);\r\n  console.log(`20-year maintenance costs (PV): $${costAnalysis.maintenanceCosts.totalPresentValue.toLocaleString()}`);\r\n  console.log(`Total cost of ownership (PV): $${costAnalysis.totalCostOfOwnership.totalPresentValue.toLocaleString()}`);\r\n  console.log(`Cost per CFM: $${costAnalysis.totalCostOfOwnership.costPerCFM.toFixed(2)}`);\r\n  console.log(`Simple payback period: ${costAnalysis.totalCostOfOwnership.paybackPeriod.toFixed(1)} years`);\r\n  console.log(`Cost optimization recommendations: ${costAnalysis.recommendations.length} recommendations\\n`);\r\n\r\n  // Step 7: Perform environmental impact assessment\r\n  const environmentalAnalysis = await EnvironmentalImpactAssessmentEngine.assessEnvironmentalImpact(\r\n    officeSystem,\r\n    energyAnalysis,\r\n    {\r\n      annualOperatingHours: 2800,\r\n      loadProfile: 'variable',\r\n      seasonalVariation: 25,\r\n      futureGrowth: 1.5\r\n    },\r\n    {\r\n      region: 'US-IL',\r\n      climateZone: '4A',\r\n      gridMix: {\r\n        renewable: 25,\r\n        nuclear: 35,\r\n        naturalGas: 30,\r\n        coal: 8,\r\n        other: 2\r\n      },\r\n      localRegulations: ['Chicago Energy Code', 'Illinois Energy Efficiency Standards']\r\n    }\r\n  );\r\n\r\n  console.log('Environmental Impact Assessment Results:');\r\n  console.log(`Total carbon emissions: ${environmentalAnalysis.carbonFootprint.totalEmissions.value.toLocaleString()} kg CO2e/year`);\r\n  console.log(`Operational emissions: ${environmentalAnalysis.carbonFootprint.operationalEmissions.value.toLocaleString()} kg CO2e/year`);\r\n  console.log(`Embodied emissions: ${environmentalAnalysis.carbonFootprint.embodiedEmissions.value.toLocaleString()} kg CO2e/year`);\r\n  console.log(`Environmental score: ${environmentalAnalysis.sustainabilityMetrics.environmentalScore.overallScore.toFixed(1)}/100`);\r\n  console.log(`LEED readiness: ${environmentalAnalysis.sustainabilityMetrics.certificationReadiness.leedReadiness}`);\r\n  console.log(`Carbon offset opportunities: ${environmentalAnalysis.carbonFootprint.offsetOpportunities.length} opportunities\\n`);\r\n\r\n  // Step 8: Perform compliance checking\r\n  const complianceAnalysis = await ComplianceCheckingEngine.performComplianceAnalysis(\r\n    officeSystem,\r\n    performanceMetrics,\r\n    energyAnalysis,\r\n    environmentalAnalysis,\r\n    ['ASHRAE 90.1', 'SMACNA', 'IECC 2021'],\r\n    {\r\n      country: 'US',\r\n      state: 'IL',\r\n      city: 'Chicago',\r\n      climateZone: '4A',\r\n      jurisdiction: 'Chicago'\r\n    }\r\n  );\r\n\r\n  console.log('Compliance Analysis Results:');\r\n  console.log(`Overall compliance: ${complianceAnalysis.overallCompliance.compliancePercentage.toFixed(1)}%`);\r\n  console.log(`ASHRAE 90.1 compliance: ${complianceAnalysis.ashraeCompliance.overallStatus}`);\r\n  console.log(`SMACNA compliance: ${complianceAnalysis.smacnaCompliance.overallStatus}`);\r\n  console.log(`Energy code compliance: ${complianceAnalysis.energyCodeCompliance.overallStatus}`);\r\n  console.log(`Critical issues: ${complianceAnalysis.overallCompliance.criticalIssues}`);\r\n  console.log(`Warnings: ${complianceAnalysis.overallCompliance.warnings}`);\r\n  console.log(`Compliance recommendations: ${complianceAnalysis.recommendations.length} recommendations\\n`);\r\n\r\n  // Step 9: Generate summary report\r\n  console.log('=== ANALYSIS SUMMARY ===');\r\n  console.log(`System: ${officeSystem.name}`);\r\n  console.log(`Performance Score: ${performanceAnalysis.performanceMetrics.systemEfficiency.value}%`);\r\n  console.log(`Energy Efficiency: ${energyAnalysis.efficiencyMetrics.specificFanPower.toFixed(2)} W/CFM`);\r\n  console.log(`20-Year Total Cost: $${costAnalysis.totalCostOfOwnership.totalPresentValue.toLocaleString()}`);\r\n  console.log(`Environmental Score: ${environmentalAnalysis.sustainabilityMetrics.environmentalScore.overallScore.toFixed(1)}/100`);\r\n  console.log(`Compliance: ${complianceAnalysis.overallCompliance.compliancePercentage.toFixed(1)}%`);\r\n  \r\n  const totalRecommendations = performanceAnalysis.recommendations.length + \r\n                              costAnalysis.recommendations.length + \r\n                              complianceAnalysis.recommendations.length;\r\n  console.log(`Total Recommendations: ${totalRecommendations}`);\r\n  console.log('=== END ANALYSIS ===\\n');\r\n}\r\n\r\n/**\r\n * Example 2: High-Performance System Optimization\r\n * \r\n * This example shows how to use the analysis tools to evaluate and optimize\r\n * a high-performance HVAC system for maximum efficiency and compliance.\r\n */\r\nexport async function highPerformanceOptimizationExample(): Promise<void> {\r\n  console.log('=== High-Performance System Optimization Example ===\\n');\r\n\r\n  const highPerformanceSystem: SystemConfiguration = {\r\n    id: 'high_performance_001',\r\n    name: 'LEED Platinum Office Building System',\r\n    systemType: 'supply_air',\r\n    designParameters: {\r\n      designAirflow: 15000,\r\n      designPressure: 2.8, // Lower pressure for efficiency\r\n      designTemperature: 75,\r\n      designHumidity: 50,\r\n      elevation: 1200,\r\n      airDensity: 0.074\r\n    },\r\n    ductConfiguration: {\r\n      shape: 'round', // More efficient shape\r\n      material: 'galvanized_steel',\r\n      insulation: {\r\n        type: 'high_performance_foam',\r\n        thickness: 3,\r\n        rValue: 12.0 // Higher R-value\r\n      },\r\n      sealingClass: 'class_a_plus' // Enhanced sealing\r\n    },\r\n    operatingConditions: {\r\n      operatingHours: 2200, // Optimized schedule\r\n      loadFactor: 0.7, // Variable load operation\r\n      seasonalVariation: 0.3,\r\n      maintenanceSchedule: 'enhanced'\r\n    },\r\n    location: {\r\n      climateZone: '5A',\r\n      region: 'US-CO',\r\n      jurisdiction: 'Denver'\r\n    }\r\n  };\r\n\r\n  // Simulate high-performance metrics\r\n  const highPerformanceMetrics: PerformanceMetrics = {\r\n    totalSystemPressure: {\r\n      value: 2.6,\r\n      units: 'in_wg',\r\n      accuracy: 0.98,\r\n      measurementSource: 'measured',\r\n      timestamp: new Date()\r\n    },\r\n    airflowEfficiency: {\r\n      value: 94,\r\n      units: 'percent',\r\n      accuracy: 0.95,\r\n      measurementSource: 'measured',\r\n      timestamp: new Date()\r\n    },\r\n    fanPerformance: {\r\n      value: 89,\r\n      units: 'percent',\r\n      accuracy: 0.9,\r\n      measurementSource: 'measured',\r\n      timestamp: new Date()\r\n    },\r\n    systemEfficiency: {\r\n      value: 91,\r\n      units: 'percent',\r\n      accuracy: 0.95,\r\n      measurementSource: 'measured',\r\n      timestamp: new Date()\r\n    },\r\n    environmentalMetrics: {\r\n      value: 88,\r\n      units: 'score',\r\n      accuracy: 0.9,\r\n      measurementSource: 'calculated',\r\n      timestamp: new Date()\r\n    },\r\n    systemBalance: {\r\n      value: 97,\r\n      units: 'percent',\r\n      accuracy: 0.9,\r\n      measurementSource: 'measured',\r\n      timestamp: new Date()\r\n    }\r\n  };\r\n\r\n  // Perform comprehensive analysis\r\n  const performanceAnalysis = await SystemPerformanceAnalysisEngine.analyzeSystemPerformance(\r\n    highPerformanceSystem,\r\n    highPerformanceMetrics\r\n  );\r\n\r\n  const energyAnalysis = await EnergyEfficiencyAnalysisEngine.analyzeEnergyEfficiency(\r\n    highPerformanceSystem,\r\n    highPerformanceMetrics\r\n  );\r\n\r\n  const environmentalAnalysis = await EnvironmentalImpactAssessmentEngine.assessEnvironmentalImpact(\r\n    highPerformanceSystem,\r\n    energyAnalysis\r\n  );\r\n\r\n  console.log('High-Performance System Results:');\r\n  console.log(`System efficiency: ${performanceAnalysis.performanceMetrics.systemEfficiency.value}%`);\r\n  console.log(`Specific fan power: ${energyAnalysis.efficiencyMetrics.specificFanPower.toFixed(2)} W/CFM`);\r\n  console.log(`Environmental score: ${environmentalAnalysis.sustainabilityMetrics.environmentalScore.overallScore.toFixed(1)}/100`);\r\n  console.log(`LEED readiness: ${environmentalAnalysis.sustainabilityMetrics.certificationReadiness.leedReadiness}`);\r\n  console.log(`Energy Star readiness: ${environmentalAnalysis.sustainabilityMetrics.certificationReadiness.energyStarReadiness}\\n`);\r\n\r\n  // Compare with baseline system\r\n  console.log('Performance Comparison vs. Standard System:');\r\n  console.log(`Efficiency improvement: +${(91 - 84).toFixed(1)}%`);\r\n  console.log(`Energy savings: ~${((1.25 - energyAnalysis.efficiencyMetrics.specificFanPower) / 1.25 * 100).toFixed(1)}%`);\r\n  console.log(`Environmental score improvement: +${(88 - 78).toFixed(1)} points`);\r\n  console.log('=== END OPTIMIZATION EXAMPLE ===\\n');\r\n}\r\n\r\n/**\r\n * Example 3: Retrofit Analysis and Comparison\r\n * \r\n * This example demonstrates how to analyze and compare different retrofit\r\n * options for an existing HVAC system.\r\n */\r\nexport async function retrofitAnalysisExample(): Promise<void> {\r\n  console.log('=== Retrofit Analysis and Comparison Example ===\\n');\r\n\r\n  // Define existing system\r\n  const existingSystem: SystemConfiguration = {\r\n    id: 'existing_system_001',\r\n    name: 'Existing 1990s Office Building System',\r\n    systemType: 'supply_air',\r\n    designParameters: {\r\n      designAirflow: 20000,\r\n      designPressure: 5.5, // High pressure - inefficient\r\n      designTemperature: 75,\r\n      designHumidity: 50,\r\n      elevation: 800,\r\n      airDensity: 0.075\r\n    },\r\n    ductConfiguration: {\r\n      shape: 'rectangular',\r\n      material: 'galvanized_steel',\r\n      insulation: {\r\n        type: 'fiberglass',\r\n        thickness: 1, // Minimal insulation\r\n        rValue: 3.0\r\n      },\r\n      sealingClass: 'class_c' // Poor sealing\r\n    },\r\n    operatingConditions: {\r\n      operatingHours: 3200, // Inefficient operation\r\n      loadFactor: 0.9, // Constant high load\r\n      seasonalVariation: 0.1,\r\n      maintenanceSchedule: 'minimal'\r\n    }\r\n  };\r\n\r\n  // Define retrofit options\r\n  const retrofitOption1: SystemConfiguration = {\r\n    ...existingSystem,\r\n    id: 'retrofit_option_1',\r\n    name: 'Retrofit Option 1: Duct Sealing + Controls',\r\n    designParameters: {\r\n      ...existingSystem.designParameters,\r\n      designPressure: 4.8 // Improved with sealing\r\n    },\r\n    ductConfiguration: {\r\n      ...existingSystem.ductConfiguration,\r\n      sealingClass: 'class_a' // Improved sealing\r\n    },\r\n    operatingConditions: {\r\n      ...existingSystem.operatingConditions,\r\n      operatingHours: 2800, // Better controls\r\n      loadFactor: 0.75\r\n    }\r\n  };\r\n\r\n  const retrofitOption2: SystemConfiguration = {\r\n    ...existingSystem,\r\n    id: 'retrofit_option_2',\r\n    name: 'Retrofit Option 2: Complete System Upgrade',\r\n    designParameters: {\r\n      ...existingSystem.designParameters,\r\n      designPressure: 3.2 // New ductwork design\r\n    },\r\n    ductConfiguration: {\r\n      shape: 'round', // More efficient\r\n      material: 'galvanized_steel',\r\n      insulation: {\r\n        type: 'high_performance_foam',\r\n        thickness: 2.5,\r\n        rValue: 10.0\r\n      },\r\n      sealingClass: 'class_a_plus'\r\n    },\r\n    operatingConditions: {\r\n      operatingHours: 2400, // VFD and advanced controls\r\n      loadFactor: 0.65,\r\n      seasonalVariation: 0.25,\r\n      maintenanceSchedule: 'standard'\r\n    }\r\n  };\r\n\r\n  console.log('Analyzing retrofit options...\\n');\r\n\r\n  // Analyze each option (simplified for example)\r\n  const options = [\r\n    { config: existingSystem, name: 'Existing System' },\r\n    { config: retrofitOption1, name: 'Retrofit Option 1' },\r\n    { config: retrofitOption2, name: 'Retrofit Option 2' }\r\n  ];\r\n\r\n  for (const option of options) {\r\n    console.log(`--- ${option.name} ---`);\r\n    \r\n    // Simulate performance metrics based on system characteristics\r\n    const sfp = option.config.designPressure * 0.3; // Simplified SFP calculation\r\n    const efficiency = Math.max(60, 100 - option.config.designPressure * 8);\r\n    \r\n    console.log(`Estimated SFP: ${sfp.toFixed(2)} W/CFM`);\r\n    console.log(`Estimated efficiency: ${efficiency.toFixed(1)}%`);\r\n    console.log(`Operating hours: ${option.config.operatingConditions?.operatingHours || 'N/A'}`);\r\n    console.log(`Sealing class: ${option.config.ductConfiguration?.sealingClass || 'N/A'}`);\r\n    console.log('');\r\n  }\r\n\r\n  console.log('Retrofit Comparison Summary:');\r\n  console.log('Option 1 (Sealing + Controls): Moderate cost, good ROI');\r\n  console.log('Option 2 (Complete Upgrade): High cost, excellent performance');\r\n  console.log('Recommendation: Start with Option 1, plan Option 2 for future');\r\n  console.log('=== END RETROFIT EXAMPLE ===\\n');\r\n}\r\n\r\n// Export all examples for easy execution\r\nexport const SystemAnalysisExamples = {\r\n  completeOfficeAnalysisExample,\r\n  highPerformanceOptimizationExample,\r\n  retrofitAnalysisExample\r\n};\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "e876884ab3410cbdbd498dfd60f3c271a8dde445"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1zsdp0zatz = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1zsdp0zatz();
cov_1zsdp0zatz().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1zsdp0zatz().s[1]++;
exports.SystemAnalysisExamples = void 0;
/* istanbul ignore next */
cov_1zsdp0zatz().s[2]++;
exports.completeOfficeAnalysisExample = completeOfficeAnalysisExample;
/* istanbul ignore next */
cov_1zsdp0zatz().s[3]++;
exports.highPerformanceOptimizationExample = highPerformanceOptimizationExample;
/* istanbul ignore next */
cov_1zsdp0zatz().s[4]++;
exports.retrofitAnalysisExample = retrofitAnalysisExample;
const SystemPerformanceAnalysisEngine_1 =
/* istanbul ignore next */
(cov_1zsdp0zatz().s[5]++, require("../SystemPerformanceAnalysisEngine"));
const EnergyEfficiencyAnalysisEngine_1 =
/* istanbul ignore next */
(cov_1zsdp0zatz().s[6]++, require("../EnergyEfficiencyAnalysisEngine"));
const LifecycleCostAnalysisEngine_1 =
/* istanbul ignore next */
(cov_1zsdp0zatz().s[7]++, require("../LifecycleCostAnalysisEngine"));
const EnvironmentalImpactAssessmentEngine_1 =
/* istanbul ignore next */
(cov_1zsdp0zatz().s[8]++, require("../EnvironmentalImpactAssessmentEngine"));
const ComplianceCheckingEngine_1 =
/* istanbul ignore next */
(cov_1zsdp0zatz().s[9]++, require("../ComplianceCheckingEngine"));
const SystemPressureCalculator_1 =
/* istanbul ignore next */
(cov_1zsdp0zatz().s[10]++, require("../SystemPressureCalculator"));
const SystemAnalysisTypes_1 =
/* istanbul ignore next */
(cov_1zsdp0zatz().s[11]++, require("../types/SystemAnalysisTypes"));
/**
 * Example 1: Complete Office Building HVAC Analysis
 *
 * This example demonstrates a complete analysis workflow for a typical
 * office building HVAC system, from initial design through compliance checking.
 */
async function completeOfficeAnalysisExample() {
  /* istanbul ignore next */
  cov_1zsdp0zatz().f[0]++;
  cov_1zsdp0zatz().s[12]++;
  console.log('=== Complete Office Building HVAC Analysis Example ===\n');
  // Step 1: Define the system configuration
  const officeSystem =
  /* istanbul ignore next */
  (cov_1zsdp0zatz().s[13]++, {
    id: 'office_building_001',
    name: '50,000 sq ft Office Building HVAC System',
    systemType: 'supply_air',
    designParameters: {
      designAirflow: 25000,
      // CFM
      designPressure: 4.2,
      // in. w.g.
      designTemperature: 75,
      // °F
      designHumidity: 50,
      // % RH
      elevation: 500,
      // ft above sea level
      airDensity: 0.075 // lb/ft³
    },
    ductConfiguration: {
      shape: 'rectangular',
      material: 'galvanized_steel',
      insulation: {
        type: 'fiberglass',
        thickness: 2,
        rValue: 6.0
      },
      sealingClass: 'class_a'
    },
    operatingConditions: {
      operatingHours: 2800,
      // hours/year
      loadFactor: 0.8,
      seasonalVariation: 0.25,
      maintenanceSchedule: 'standard'
    },
    location: {
      climateZone: '4A',
      region: 'US-IL',
      jurisdiction: 'Chicago'
    }
  });
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[14]++;
  console.log(`Analyzing system: ${officeSystem.name}`);
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[15]++;
  console.log(`Design airflow: ${officeSystem.designParameters.designAirflow.toLocaleString()} CFM`);
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[16]++;
  console.log(`Design pressure: ${officeSystem.designParameters.designPressure} in. w.g.\n`);
  // Step 2: Calculate system pressure using existing Phase 1 components
  const systemPressureResult =
  /* istanbul ignore next */
  (cov_1zsdp0zatz().s[17]++, await SystemPressureCalculator_1.SystemPressureCalculator.calculateSystemPressure({
    airflow: officeSystem.designParameters.designAirflow,
    ductSections: [{
      id: 'main_supply_trunk',
      length: 200,
      hydraulicDiameter: 2.0,
      roughness: 0.0003,
      area: 4.0,
      velocity: officeSystem.designParameters.designAirflow / 4.0 / 60
    }, {
      id: 'branch_duct_1',
      length: 80,
      hydraulicDiameter: 1.2,
      roughness: 0.0003,
      area: 1.44,
      velocity: 8000 / 1.44 / 60
    }],
    fittings: [{
      id: 'main_elbow',
      type: 'elbow_90_rectangular',
      kFactor: 0.25,
      airflow: officeSystem.designParameters.designAirflow
    }, {
      id: 'branch_tee',
      type: 'tee_branch',
      kFactor: 0.4,
      airflow: 8000
    }],
    airProperties: {
      density: officeSystem.designParameters.airDensity,
      viscosity: 1.2e-5,
      temperature: officeSystem.designParameters.designTemperature
    }
  }));
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[18]++;
  console.log('System Pressure Calculation Results:');
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[19]++;
  console.log(`Total pressure loss: ${systemPressureResult.totalPressureLoss.toFixed(2)} in. w.g.`);
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[20]++;
  console.log(`Friction losses: ${systemPressureResult.frictionLosses.toFixed(2)} in. w.g.`);
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[21]++;
  console.log(`Fitting losses: ${systemPressureResult.fittingLosses.toFixed(2)} in. w.g.\n`);
  // Step 3: Create performance metrics from calculated results
  const performanceMetrics =
  /* istanbul ignore next */
  (cov_1zsdp0zatz().s[22]++, {
    totalSystemPressure: {
      value: systemPressureResult.totalPressureLoss,
      units: 'in_wg',
      accuracy: 0.95,
      measurementSource: 'calculated',
      timestamp: new Date()
    },
    airflowEfficiency: {
      value: 87,
      units: 'percent',
      accuracy: 0.9,
      measurementSource: 'calculated',
      timestamp: new Date()
    },
    fanPerformance: {
      value: 82,
      units: 'percent',
      accuracy: 0.85,
      measurementSource: 'estimated',
      timestamp: new Date()
    },
    systemEfficiency: {
      value: 84,
      units: 'percent',
      accuracy: 0.9,
      measurementSource: 'calculated',
      timestamp: new Date()
    },
    environmentalMetrics: {
      value: 78,
      units: 'score',
      accuracy: 0.8,
      measurementSource: 'calculated',
      timestamp: new Date()
    },
    systemBalance: {
      value: 92,
      units: 'percent',
      accuracy: 0.85,
      measurementSource: 'calculated',
      timestamp: new Date()
    }
  });
  // Step 4: Perform system performance analysis
  const performanceAnalysis =
  /* istanbul ignore next */
  (cov_1zsdp0zatz().s[23]++, await SystemPerformanceAnalysisEngine_1.SystemPerformanceAnalysisEngine.analyzeSystemPerformance(officeSystem, performanceMetrics));
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[24]++;
  console.log('System Performance Analysis Results:');
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[25]++;
  console.log(`Analysis ID: ${performanceAnalysis.id}`);
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[26]++;
  console.log(`Overall system efficiency: ${performanceAnalysis.performanceMetrics.systemEfficiency.value}%`);
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[27]++;
  console.log(`Performance trend: ${performanceAnalysis.trendAnalysis.trendDirection}`);
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[28]++;
  console.log(`Benchmark percentile: ${performanceAnalysis.benchmarkComparison.percentile}th percentile`);
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[29]++;
  console.log(`Performance alerts: ${performanceAnalysis.alerts.length} alerts generated`);
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[30]++;
  console.log(`Recommendations: ${performanceAnalysis.recommendations.length} recommendations\n`);
  // Step 5: Perform energy efficiency analysis
  const energyAnalysis =
  /* istanbul ignore next */
  (cov_1zsdp0zatz().s[31]++, await EnergyEfficiencyAnalysisEngine_1.EnergyEfficiencyAnalysisEngine.analyzeEnergyEfficiency(officeSystem, performanceMetrics));
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[32]++;
  console.log('Energy Efficiency Analysis Results:');
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[33]++;
  console.log(`Annual energy consumption: ${energyAnalysis.energyConsumption.totalConsumption.value.toLocaleString()} kWh`);
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[34]++;
  console.log(`Specific fan power: ${energyAnalysis.efficiencyMetrics.specificFanPower.toFixed(2)} W/CFM`);
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[35]++;
  console.log(`Annual energy cost: $${energyAnalysis.energyCosts.currentCosts.totalCost.toLocaleString()}`);
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[36]++;
  console.log(`Carbon footprint: ${energyAnalysis.carbonFootprint.totalEmissions.value.toLocaleString()} kg CO2e/year`);
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[37]++;
  console.log(`Energy benchmark: ${energyAnalysis.benchmarkComparison.benchmarkType} - ${energyAnalysis.benchmarkComparison.percentile}th percentile\n`);
  // Step 6: Perform lifecycle cost analysis
  const costAnalysis =
  /* istanbul ignore next */
  (cov_1zsdp0zatz().s[38]++, await LifecycleCostAnalysisEngine_1.LifecycleCostAnalysisEngine.analyzeLifecycleCosts(officeSystem, energyAnalysis, {
    analysisHorizon: 20,
    discountRate: 0.06,
    inflationRate: 0.025,
    energyEscalationRate: 0.03,
    currency: 'USD',
    analysisMethod: SystemAnalysisTypes_1.CostAnalysisMethod.NET_PRESENT_VALUE,
    uncertaintyLevel: SystemAnalysisTypes_1.UncertaintyLevel.MEDIUM
  }));
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[39]++;
  console.log('Lifecycle Cost Analysis Results:');
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[40]++;
  console.log(`Initial cost: $${costAnalysis.initialCosts.totalInitialCost.toLocaleString()}`);
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[41]++;
  console.log(`20-year operating costs (PV): $${costAnalysis.operatingCosts.totalPresentValue.toLocaleString()}`);
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[42]++;
  console.log(`20-year maintenance costs (PV): $${costAnalysis.maintenanceCosts.totalPresentValue.toLocaleString()}`);
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[43]++;
  console.log(`Total cost of ownership (PV): $${costAnalysis.totalCostOfOwnership.totalPresentValue.toLocaleString()}`);
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[44]++;
  console.log(`Cost per CFM: $${costAnalysis.totalCostOfOwnership.costPerCFM.toFixed(2)}`);
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[45]++;
  console.log(`Simple payback period: ${costAnalysis.totalCostOfOwnership.paybackPeriod.toFixed(1)} years`);
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[46]++;
  console.log(`Cost optimization recommendations: ${costAnalysis.recommendations.length} recommendations\n`);
  // Step 7: Perform environmental impact assessment
  const environmentalAnalysis =
  /* istanbul ignore next */
  (cov_1zsdp0zatz().s[47]++, await EnvironmentalImpactAssessmentEngine_1.EnvironmentalImpactAssessmentEngine.assessEnvironmentalImpact(officeSystem, energyAnalysis, {
    annualOperatingHours: 2800,
    loadProfile: 'variable',
    seasonalVariation: 25,
    futureGrowth: 1.5
  }, {
    region: 'US-IL',
    climateZone: '4A',
    gridMix: {
      renewable: 25,
      nuclear: 35,
      naturalGas: 30,
      coal: 8,
      other: 2
    },
    localRegulations: ['Chicago Energy Code', 'Illinois Energy Efficiency Standards']
  }));
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[48]++;
  console.log('Environmental Impact Assessment Results:');
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[49]++;
  console.log(`Total carbon emissions: ${environmentalAnalysis.carbonFootprint.totalEmissions.value.toLocaleString()} kg CO2e/year`);
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[50]++;
  console.log(`Operational emissions: ${environmentalAnalysis.carbonFootprint.operationalEmissions.value.toLocaleString()} kg CO2e/year`);
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[51]++;
  console.log(`Embodied emissions: ${environmentalAnalysis.carbonFootprint.embodiedEmissions.value.toLocaleString()} kg CO2e/year`);
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[52]++;
  console.log(`Environmental score: ${environmentalAnalysis.sustainabilityMetrics.environmentalScore.overallScore.toFixed(1)}/100`);
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[53]++;
  console.log(`LEED readiness: ${environmentalAnalysis.sustainabilityMetrics.certificationReadiness.leedReadiness}`);
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[54]++;
  console.log(`Carbon offset opportunities: ${environmentalAnalysis.carbonFootprint.offsetOpportunities.length} opportunities\n`);
  // Step 8: Perform compliance checking
  const complianceAnalysis =
  /* istanbul ignore next */
  (cov_1zsdp0zatz().s[55]++, await ComplianceCheckingEngine_1.ComplianceCheckingEngine.performComplianceAnalysis(officeSystem, performanceMetrics, energyAnalysis, environmentalAnalysis, ['ASHRAE 90.1', 'SMACNA', 'IECC 2021'], {
    country: 'US',
    state: 'IL',
    city: 'Chicago',
    climateZone: '4A',
    jurisdiction: 'Chicago'
  }));
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[56]++;
  console.log('Compliance Analysis Results:');
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[57]++;
  console.log(`Overall compliance: ${complianceAnalysis.overallCompliance.compliancePercentage.toFixed(1)}%`);
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[58]++;
  console.log(`ASHRAE 90.1 compliance: ${complianceAnalysis.ashraeCompliance.overallStatus}`);
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[59]++;
  console.log(`SMACNA compliance: ${complianceAnalysis.smacnaCompliance.overallStatus}`);
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[60]++;
  console.log(`Energy code compliance: ${complianceAnalysis.energyCodeCompliance.overallStatus}`);
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[61]++;
  console.log(`Critical issues: ${complianceAnalysis.overallCompliance.criticalIssues}`);
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[62]++;
  console.log(`Warnings: ${complianceAnalysis.overallCompliance.warnings}`);
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[63]++;
  console.log(`Compliance recommendations: ${complianceAnalysis.recommendations.length} recommendations\n`);
  // Step 9: Generate summary report
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[64]++;
  console.log('=== ANALYSIS SUMMARY ===');
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[65]++;
  console.log(`System: ${officeSystem.name}`);
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[66]++;
  console.log(`Performance Score: ${performanceAnalysis.performanceMetrics.systemEfficiency.value}%`);
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[67]++;
  console.log(`Energy Efficiency: ${energyAnalysis.efficiencyMetrics.specificFanPower.toFixed(2)} W/CFM`);
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[68]++;
  console.log(`20-Year Total Cost: $${costAnalysis.totalCostOfOwnership.totalPresentValue.toLocaleString()}`);
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[69]++;
  console.log(`Environmental Score: ${environmentalAnalysis.sustainabilityMetrics.environmentalScore.overallScore.toFixed(1)}/100`);
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[70]++;
  console.log(`Compliance: ${complianceAnalysis.overallCompliance.compliancePercentage.toFixed(1)}%`);
  const totalRecommendations =
  /* istanbul ignore next */
  (cov_1zsdp0zatz().s[71]++, performanceAnalysis.recommendations.length + costAnalysis.recommendations.length + complianceAnalysis.recommendations.length);
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[72]++;
  console.log(`Total Recommendations: ${totalRecommendations}`);
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[73]++;
  console.log('=== END ANALYSIS ===\n');
}
/**
 * Example 2: High-Performance System Optimization
 *
 * This example shows how to use the analysis tools to evaluate and optimize
 * a high-performance HVAC system for maximum efficiency and compliance.
 */
async function highPerformanceOptimizationExample() {
  /* istanbul ignore next */
  cov_1zsdp0zatz().f[1]++;
  cov_1zsdp0zatz().s[74]++;
  console.log('=== High-Performance System Optimization Example ===\n');
  const highPerformanceSystem =
  /* istanbul ignore next */
  (cov_1zsdp0zatz().s[75]++, {
    id: 'high_performance_001',
    name: 'LEED Platinum Office Building System',
    systemType: 'supply_air',
    designParameters: {
      designAirflow: 15000,
      designPressure: 2.8,
      // Lower pressure for efficiency
      designTemperature: 75,
      designHumidity: 50,
      elevation: 1200,
      airDensity: 0.074
    },
    ductConfiguration: {
      shape: 'round',
      // More efficient shape
      material: 'galvanized_steel',
      insulation: {
        type: 'high_performance_foam',
        thickness: 3,
        rValue: 12.0 // Higher R-value
      },
      sealingClass: 'class_a_plus' // Enhanced sealing
    },
    operatingConditions: {
      operatingHours: 2200,
      // Optimized schedule
      loadFactor: 0.7,
      // Variable load operation
      seasonalVariation: 0.3,
      maintenanceSchedule: 'enhanced'
    },
    location: {
      climateZone: '5A',
      region: 'US-CO',
      jurisdiction: 'Denver'
    }
  });
  // Simulate high-performance metrics
  const highPerformanceMetrics =
  /* istanbul ignore next */
  (cov_1zsdp0zatz().s[76]++, {
    totalSystemPressure: {
      value: 2.6,
      units: 'in_wg',
      accuracy: 0.98,
      measurementSource: 'measured',
      timestamp: new Date()
    },
    airflowEfficiency: {
      value: 94,
      units: 'percent',
      accuracy: 0.95,
      measurementSource: 'measured',
      timestamp: new Date()
    },
    fanPerformance: {
      value: 89,
      units: 'percent',
      accuracy: 0.9,
      measurementSource: 'measured',
      timestamp: new Date()
    },
    systemEfficiency: {
      value: 91,
      units: 'percent',
      accuracy: 0.95,
      measurementSource: 'measured',
      timestamp: new Date()
    },
    environmentalMetrics: {
      value: 88,
      units: 'score',
      accuracy: 0.9,
      measurementSource: 'calculated',
      timestamp: new Date()
    },
    systemBalance: {
      value: 97,
      units: 'percent',
      accuracy: 0.9,
      measurementSource: 'measured',
      timestamp: new Date()
    }
  });
  // Perform comprehensive analysis
  const performanceAnalysis =
  /* istanbul ignore next */
  (cov_1zsdp0zatz().s[77]++, await SystemPerformanceAnalysisEngine_1.SystemPerformanceAnalysisEngine.analyzeSystemPerformance(highPerformanceSystem, highPerformanceMetrics));
  const energyAnalysis =
  /* istanbul ignore next */
  (cov_1zsdp0zatz().s[78]++, await EnergyEfficiencyAnalysisEngine_1.EnergyEfficiencyAnalysisEngine.analyzeEnergyEfficiency(highPerformanceSystem, highPerformanceMetrics));
  const environmentalAnalysis =
  /* istanbul ignore next */
  (cov_1zsdp0zatz().s[79]++, await EnvironmentalImpactAssessmentEngine_1.EnvironmentalImpactAssessmentEngine.assessEnvironmentalImpact(highPerformanceSystem, energyAnalysis));
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[80]++;
  console.log('High-Performance System Results:');
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[81]++;
  console.log(`System efficiency: ${performanceAnalysis.performanceMetrics.systemEfficiency.value}%`);
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[82]++;
  console.log(`Specific fan power: ${energyAnalysis.efficiencyMetrics.specificFanPower.toFixed(2)} W/CFM`);
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[83]++;
  console.log(`Environmental score: ${environmentalAnalysis.sustainabilityMetrics.environmentalScore.overallScore.toFixed(1)}/100`);
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[84]++;
  console.log(`LEED readiness: ${environmentalAnalysis.sustainabilityMetrics.certificationReadiness.leedReadiness}`);
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[85]++;
  console.log(`Energy Star readiness: ${environmentalAnalysis.sustainabilityMetrics.certificationReadiness.energyStarReadiness}\n`);
  // Compare with baseline system
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[86]++;
  console.log('Performance Comparison vs. Standard System:');
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[87]++;
  console.log(`Efficiency improvement: +${(91 - 84).toFixed(1)}%`);
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[88]++;
  console.log(`Energy savings: ~${((1.25 - energyAnalysis.efficiencyMetrics.specificFanPower) / 1.25 * 100).toFixed(1)}%`);
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[89]++;
  console.log(`Environmental score improvement: +${(88 - 78).toFixed(1)} points`);
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[90]++;
  console.log('=== END OPTIMIZATION EXAMPLE ===\n');
}
/**
 * Example 3: Retrofit Analysis and Comparison
 *
 * This example demonstrates how to analyze and compare different retrofit
 * options for an existing HVAC system.
 */
async function retrofitAnalysisExample() {
  /* istanbul ignore next */
  cov_1zsdp0zatz().f[2]++;
  cov_1zsdp0zatz().s[91]++;
  console.log('=== Retrofit Analysis and Comparison Example ===\n');
  // Define existing system
  const existingSystem =
  /* istanbul ignore next */
  (cov_1zsdp0zatz().s[92]++, {
    id: 'existing_system_001',
    name: 'Existing 1990s Office Building System',
    systemType: 'supply_air',
    designParameters: {
      designAirflow: 20000,
      designPressure: 5.5,
      // High pressure - inefficient
      designTemperature: 75,
      designHumidity: 50,
      elevation: 800,
      airDensity: 0.075
    },
    ductConfiguration: {
      shape: 'rectangular',
      material: 'galvanized_steel',
      insulation: {
        type: 'fiberglass',
        thickness: 1,
        // Minimal insulation
        rValue: 3.0
      },
      sealingClass: 'class_c' // Poor sealing
    },
    operatingConditions: {
      operatingHours: 3200,
      // Inefficient operation
      loadFactor: 0.9,
      // Constant high load
      seasonalVariation: 0.1,
      maintenanceSchedule: 'minimal'
    }
  });
  // Define retrofit options
  const retrofitOption1 =
  /* istanbul ignore next */
  (cov_1zsdp0zatz().s[93]++, {
    ...existingSystem,
    id: 'retrofit_option_1',
    name: 'Retrofit Option 1: Duct Sealing + Controls',
    designParameters: {
      ...existingSystem.designParameters,
      designPressure: 4.8 // Improved with sealing
    },
    ductConfiguration: {
      ...existingSystem.ductConfiguration,
      sealingClass: 'class_a' // Improved sealing
    },
    operatingConditions: {
      ...existingSystem.operatingConditions,
      operatingHours: 2800,
      // Better controls
      loadFactor: 0.75
    }
  });
  const retrofitOption2 =
  /* istanbul ignore next */
  (cov_1zsdp0zatz().s[94]++, {
    ...existingSystem,
    id: 'retrofit_option_2',
    name: 'Retrofit Option 2: Complete System Upgrade',
    designParameters: {
      ...existingSystem.designParameters,
      designPressure: 3.2 // New ductwork design
    },
    ductConfiguration: {
      shape: 'round',
      // More efficient
      material: 'galvanized_steel',
      insulation: {
        type: 'high_performance_foam',
        thickness: 2.5,
        rValue: 10.0
      },
      sealingClass: 'class_a_plus'
    },
    operatingConditions: {
      operatingHours: 2400,
      // VFD and advanced controls
      loadFactor: 0.65,
      seasonalVariation: 0.25,
      maintenanceSchedule: 'standard'
    }
  });
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[95]++;
  console.log('Analyzing retrofit options...\n');
  // Analyze each option (simplified for example)
  const options =
  /* istanbul ignore next */
  (cov_1zsdp0zatz().s[96]++, [{
    config: existingSystem,
    name: 'Existing System'
  }, {
    config: retrofitOption1,
    name: 'Retrofit Option 1'
  }, {
    config: retrofitOption2,
    name: 'Retrofit Option 2'
  }]);
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[97]++;
  for (const option of options) {
    /* istanbul ignore next */
    cov_1zsdp0zatz().s[98]++;
    console.log(`--- ${option.name} ---`);
    // Simulate performance metrics based on system characteristics
    const sfp =
    /* istanbul ignore next */
    (cov_1zsdp0zatz().s[99]++, option.config.designPressure * 0.3); // Simplified SFP calculation
    const efficiency =
    /* istanbul ignore next */
    (cov_1zsdp0zatz().s[100]++, Math.max(60, 100 - option.config.designPressure * 8));
    /* istanbul ignore next */
    cov_1zsdp0zatz().s[101]++;
    console.log(`Estimated SFP: ${sfp.toFixed(2)} W/CFM`);
    /* istanbul ignore next */
    cov_1zsdp0zatz().s[102]++;
    console.log(`Estimated efficiency: ${efficiency.toFixed(1)}%`);
    /* istanbul ignore next */
    cov_1zsdp0zatz().s[103]++;
    console.log(`Operating hours: ${
    /* istanbul ignore next */
    (cov_1zsdp0zatz().b[0][0]++, option.config.operatingConditions?.operatingHours) ||
    /* istanbul ignore next */
    (cov_1zsdp0zatz().b[0][1]++, 'N/A')}`);
    /* istanbul ignore next */
    cov_1zsdp0zatz().s[104]++;
    console.log(`Sealing class: ${
    /* istanbul ignore next */
    (cov_1zsdp0zatz().b[1][0]++, option.config.ductConfiguration?.sealingClass) ||
    /* istanbul ignore next */
    (cov_1zsdp0zatz().b[1][1]++, 'N/A')}`);
    /* istanbul ignore next */
    cov_1zsdp0zatz().s[105]++;
    console.log('');
  }
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[106]++;
  console.log('Retrofit Comparison Summary:');
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[107]++;
  console.log('Option 1 (Sealing + Controls): Moderate cost, good ROI');
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[108]++;
  console.log('Option 2 (Complete Upgrade): High cost, excellent performance');
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[109]++;
  console.log('Recommendation: Start with Option 1, plan Option 2 for future');
  /* istanbul ignore next */
  cov_1zsdp0zatz().s[110]++;
  console.log('=== END RETROFIT EXAMPLE ===\n');
}
// Export all examples for easy execution
/* istanbul ignore next */
cov_1zsdp0zatz().s[111]++;
exports.SystemAnalysisExamples = {
  completeOfficeAnalysisExample,
  highPerformanceOptimizationExample,
  retrofitAnalysisExample
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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