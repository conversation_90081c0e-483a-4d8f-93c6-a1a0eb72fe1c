{"version": 3, "names": ["cov_1zsdp0zatz", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "name", "decl", "loc", "branchMap", "type", "locations", "s", "f", "b", "inputSourceMap", "file", "mappings", "names", "sources", "sourcesContent", "version", "_coverageSchema", "coverage", "actualCoverage", "exports", "completeOfficeAnalysisExample", "highPerformanceOptimizationExample", "retrofitAnalysisExample", "SystemPerformanceAnalysisEngine_1", "require", "EnergyEfficiencyAnalysisEngine_1", "LifecycleCostAnalysisEngine_1", "EnvironmentalImpactAssessmentEngine_1", "ComplianceCheckingEngine_1", "SystemPressureCalculator_1", "SystemAnalysisTypes_1", "console", "log", "officeSystem", "id", "systemType", "designParameters", "designAirflow", "designPressure", "designTemperature", "designHumidity", "elevation", "airDensity", "ductConfiguration", "shape", "material", "insulation", "thickness", "rValue", "sealingClass", "operatingConditions", "operatingHours", "loadFactor", "seasonalVariation", "maintenanceSchedule", "location", "climateZone", "region", "jurisdiction", "toLocaleString", "systemPressureResult", "SystemPressureCalculator", "calculateSystemPressure", "airflow", "ductSections", "length", "hydraulicDiameter", "roughness", "area", "velocity", "fittings", "kFactor", "airProperties", "density", "viscosity", "temperature", "totalPressureLoss", "toFixed", "frictionLosses", "fittingLosses", "performanceMetrics", "totalSystemPressure", "value", "units", "accuracy", "measurementSource", "timestamp", "Date", "airflowEfficiency", "fanPerformance", "systemEfficiency", "environmentalMetrics", "systemBalance", "performanceAnalysis", "SystemPerformanceAnalysisEngine", "analyzeSystemPerformance", "trendAnalysis", "trendDirection", "benchmarkComparison", "percentile", "alerts", "recommendations", "energyAnalysis", "EnergyEfficiencyAnalysisEngine", "analyzeEnergyEfficiency", "energyConsumption", "totalConsumption", "efficiencyMetrics", "specificFan<PERSON>ower", "energyCosts", "currentCosts", "totalCost", "carbonFootprint", "totalEmissions", "benchmarkType", "costAnalysis", "LifecycleCostAnalysisEngine", "analyzeLifecycleCosts", "analysisHorizon", "discountRate", "inflationRate", "energyEscalationRate", "currency", "analysisMethod", "CostAnalysisMethod", "NET_PRESENT_VALUE", "uncertaintyLevel", "UncertaintyLevel", "MEDIUM", "initialCosts", "totalInitialCost", "operatingCosts", "totalPresentValue", "maintenanceCosts", "totalCostOfOwnership", "costPerCFM", "paybackPeriod", "environmentalAnalysis", "EnvironmentalImpactAssessmentEngine", "assessEnvironmentalImpact", "annualOperatingHours", "loadProfile", "<PERSON><PERSON><PERSON><PERSON>", "gridMix", "renewable", "nuclear", "naturalGas", "coal", "other", "localRegulations", "operationalEmissions", "embodiedEmissions", "sustainabilityMetrics", "environmentalScore", "overallScore", "certificationReadiness", "leedReadiness", "offsetOpportunities", "complianceAnalysis", "ComplianceCheckingEngine", "performComplianceAnalysis", "country", "state", "city", "overallCompliance", "compliancePercentage", "ashraeCompliance", "overallStatus", "smacnaCompliance", "energyCodeCompliance", "criticalIssues", "warnings", "totalRecommendations", "highPerformanceSystem", "highPerformanceMetrics", "energyStarReadiness", "existingSystem", "retrofitOption1", "retrofitOption2", "options", "config", "option", "sfp", "efficiency", "Math", "max", "SystemAnalysisExamples"], "sources": ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\examples\\SystemAnalysisExamples.ts"], "sourcesContent": ["/**\r\n * System Analysis Examples\r\n * \r\n * Practical examples demonstrating the use of Phase 3 Priority 3: Advanced System Analysis Tools\r\n * Shows real-world usage patterns and integration with existing SizeWise Suite components.\r\n * \r\n * @version 3.0.0\r\n * <AUTHOR> Suite Development Team\r\n */\r\n\r\nimport { SystemPerformanceAnalysisEngine } from '../SystemPerformanceAnalysisEngine';\r\nimport { EnergyEfficiencyAnalysisEngine } from '../EnergyEfficiencyAnalysisEngine';\r\nimport { LifecycleCostAnalysisEngine } from '../LifecycleCostAnalysisEngine';\r\nimport { EnvironmentalImpactAssessmentEngine } from '../EnvironmentalImpactAssessmentEngine';\r\nimport { ComplianceCheckingEngine } from '../ComplianceCheckingEngine';\r\nimport { SystemPressureCalculator } from '../SystemPressureCalculator';\r\nimport { FittingLossCalculator } from '../FittingLossCalculator';\r\nimport { AirPropertiesCalculator } from '../AirPropertiesCalculator';\r\nimport {\r\n  SystemConfiguration,\r\n  PerformanceMetrics,\r\n  CostAnalysisMethod,\r\n  UncertaintyLevel\r\n} from '../types/SystemAnalysisTypes';\r\n\r\n/**\r\n * Example 1: Complete Office Building HVAC Analysis\r\n * \r\n * This example demonstrates a complete analysis workflow for a typical\r\n * office building HVAC system, from initial design through compliance checking.\r\n */\r\nexport async function completeOfficeAnalysisExample(): Promise<void> {\r\n  console.log('=== Complete Office Building HVAC Analysis Example ===\\n');\r\n\r\n  // Step 1: Define the system configuration\r\n  const officeSystem: SystemConfiguration = {\r\n    id: 'office_building_001',\r\n    name: '50,000 sq ft Office Building HVAC System',\r\n    systemType: 'supply_air',\r\n    designParameters: {\r\n      designAirflow: 25000, // CFM\r\n      designPressure: 4.2, // in. w.g.\r\n      designTemperature: 75, // °F\r\n      designHumidity: 50, // % RH\r\n      elevation: 500, // ft above sea level\r\n      airDensity: 0.075 // lb/ft³\r\n    },\r\n    ductConfiguration: {\r\n      shape: 'rectangular',\r\n      material: 'galvanized_steel',\r\n      insulation: {\r\n        type: 'fiberglass',\r\n        thickness: 2,\r\n        rValue: 6.0\r\n      },\r\n      sealingClass: 'class_a'\r\n    },\r\n    operatingConditions: {\r\n      operatingHours: 2800, // hours/year\r\n      loadFactor: 0.8,\r\n      seasonalVariation: 0.25,\r\n      maintenanceSchedule: 'standard'\r\n    },\r\n    location: {\r\n      climateZone: '4A',\r\n      region: 'US-IL',\r\n      jurisdiction: 'Chicago'\r\n    }\r\n  };\r\n\r\n  console.log(`Analyzing system: ${officeSystem.name}`);\r\n  console.log(`Design airflow: ${officeSystem.designParameters.designAirflow.toLocaleString()} CFM`);\r\n  console.log(`Design pressure: ${officeSystem.designParameters.designPressure} in. w.g.\\n`);\r\n\r\n  // Step 2: Calculate system pressure using existing Phase 1 components\r\n  const systemPressureResult = await SystemPressureCalculator.calculateSystemPressure({\r\n    airflow: officeSystem.designParameters.designAirflow,\r\n    ductSections: [\r\n      {\r\n        id: 'main_supply_trunk',\r\n        length: 200,\r\n        hydraulicDiameter: 2.0,\r\n        roughness: 0.0003,\r\n        area: 4.0,\r\n        velocity: officeSystem.designParameters.designAirflow / 4.0 / 60\r\n      },\r\n      {\r\n        id: 'branch_duct_1',\r\n        length: 80,\r\n        hydraulicDiameter: 1.2,\r\n        roughness: 0.0003,\r\n        area: 1.44,\r\n        velocity: 8000 / 1.44 / 60\r\n      }\r\n    ],\r\n    fittings: [\r\n      {\r\n        id: 'main_elbow',\r\n        type: 'elbow_90_rectangular',\r\n        kFactor: 0.25,\r\n        airflow: officeSystem.designParameters.designAirflow\r\n      },\r\n      {\r\n        id: 'branch_tee',\r\n        type: 'tee_branch',\r\n        kFactor: 0.4,\r\n        airflow: 8000\r\n      }\r\n    ],\r\n    airProperties: {\r\n      density: officeSystem.designParameters.airDensity,\r\n      viscosity: 1.2e-5,\r\n      temperature: officeSystem.designParameters.designTemperature\r\n    }\r\n  });\r\n\r\n  console.log('System Pressure Calculation Results:');\r\n  console.log(`Total pressure loss: ${systemPressureResult.totalPressureLoss.toFixed(2)} in. w.g.`);\r\n  console.log(`Friction losses: ${systemPressureResult.frictionLosses.toFixed(2)} in. w.g.`);\r\n  console.log(`Fitting losses: ${systemPressureResult.fittingLosses.toFixed(2)} in. w.g.\\n`);\r\n\r\n  // Step 3: Create performance metrics from calculated results\r\n  const performanceMetrics: PerformanceMetrics = {\r\n    totalSystemPressure: {\r\n      value: systemPressureResult.totalPressureLoss,\r\n      units: 'in_wg',\r\n      accuracy: 0.95,\r\n      measurementSource: 'calculated',\r\n      timestamp: new Date()\r\n    },\r\n    airflowEfficiency: {\r\n      value: 87,\r\n      units: 'percent',\r\n      accuracy: 0.9,\r\n      measurementSource: 'calculated',\r\n      timestamp: new Date()\r\n    },\r\n    fanPerformance: {\r\n      value: 82,\r\n      units: 'percent',\r\n      accuracy: 0.85,\r\n      measurementSource: 'estimated',\r\n      timestamp: new Date()\r\n    },\r\n    systemEfficiency: {\r\n      value: 84,\r\n      units: 'percent',\r\n      accuracy: 0.9,\r\n      measurementSource: 'calculated',\r\n      timestamp: new Date()\r\n    },\r\n    environmentalMetrics: {\r\n      value: 78,\r\n      units: 'score',\r\n      accuracy: 0.8,\r\n      measurementSource: 'calculated',\r\n      timestamp: new Date()\r\n    },\r\n    systemBalance: {\r\n      value: 92,\r\n      units: 'percent',\r\n      accuracy: 0.85,\r\n      measurementSource: 'calculated',\r\n      timestamp: new Date()\r\n    }\r\n  };\r\n\r\n  // Step 4: Perform system performance analysis\r\n  const performanceAnalysis = await SystemPerformanceAnalysisEngine.analyzeSystemPerformance(\r\n    officeSystem,\r\n    performanceMetrics\r\n  );\r\n\r\n  console.log('System Performance Analysis Results:');\r\n  console.log(`Analysis ID: ${performanceAnalysis.id}`);\r\n  console.log(`Overall system efficiency: ${performanceAnalysis.performanceMetrics.systemEfficiency.value}%`);\r\n  console.log(`Performance trend: ${performanceAnalysis.trendAnalysis.trendDirection}`);\r\n  console.log(`Benchmark percentile: ${performanceAnalysis.benchmarkComparison.percentile}th percentile`);\r\n  console.log(`Performance alerts: ${performanceAnalysis.alerts.length} alerts generated`);\r\n  console.log(`Recommendations: ${performanceAnalysis.recommendations.length} recommendations\\n`);\r\n\r\n  // Step 5: Perform energy efficiency analysis\r\n  const energyAnalysis = await EnergyEfficiencyAnalysisEngine.analyzeEnergyEfficiency(\r\n    officeSystem,\r\n    performanceMetrics\r\n  );\r\n\r\n  console.log('Energy Efficiency Analysis Results:');\r\n  console.log(`Annual energy consumption: ${energyAnalysis.energyConsumption.totalConsumption.value.toLocaleString()} kWh`);\r\n  console.log(`Specific fan power: ${energyAnalysis.efficiencyMetrics.specificFanPower.toFixed(2)} W/CFM`);\r\n  console.log(`Annual energy cost: $${energyAnalysis.energyCosts.currentCosts.totalCost.toLocaleString()}`);\r\n  console.log(`Carbon footprint: ${energyAnalysis.carbonFootprint.totalEmissions.value.toLocaleString()} kg CO2e/year`);\r\n  console.log(`Energy benchmark: ${energyAnalysis.benchmarkComparison.benchmarkType} - ${energyAnalysis.benchmarkComparison.percentile}th percentile\\n`);\r\n\r\n  // Step 6: Perform lifecycle cost analysis\r\n  const costAnalysis = await LifecycleCostAnalysisEngine.analyzeLifecycleCosts(\r\n    officeSystem,\r\n    energyAnalysis,\r\n    {\r\n      analysisHorizon: 20,\r\n      discountRate: 0.06,\r\n      inflationRate: 0.025,\r\n      energyEscalationRate: 0.03,\r\n      currency: 'USD',\r\n      analysisMethod: CostAnalysisMethod.NET_PRESENT_VALUE,\r\n      uncertaintyLevel: UncertaintyLevel.MEDIUM\r\n    }\r\n  );\r\n\r\n  console.log('Lifecycle Cost Analysis Results:');\r\n  console.log(`Initial cost: $${costAnalysis.initialCosts.totalInitialCost.toLocaleString()}`);\r\n  console.log(`20-year operating costs (PV): $${costAnalysis.operatingCosts.totalPresentValue.toLocaleString()}`);\r\n  console.log(`20-year maintenance costs (PV): $${costAnalysis.maintenanceCosts.totalPresentValue.toLocaleString()}`);\r\n  console.log(`Total cost of ownership (PV): $${costAnalysis.totalCostOfOwnership.totalPresentValue.toLocaleString()}`);\r\n  console.log(`Cost per CFM: $${costAnalysis.totalCostOfOwnership.costPerCFM.toFixed(2)}`);\r\n  console.log(`Simple payback period: ${costAnalysis.totalCostOfOwnership.paybackPeriod.toFixed(1)} years`);\r\n  console.log(`Cost optimization recommendations: ${costAnalysis.recommendations.length} recommendations\\n`);\r\n\r\n  // Step 7: Perform environmental impact assessment\r\n  const environmentalAnalysis = await EnvironmentalImpactAssessmentEngine.assessEnvironmentalImpact(\r\n    officeSystem,\r\n    energyAnalysis,\r\n    {\r\n      annualOperatingHours: 2800,\r\n      loadProfile: 'variable',\r\n      seasonalVariation: 25,\r\n      futureGrowth: 1.5\r\n    },\r\n    {\r\n      region: 'US-IL',\r\n      climateZone: '4A',\r\n      gridMix: {\r\n        renewable: 25,\r\n        nuclear: 35,\r\n        naturalGas: 30,\r\n        coal: 8,\r\n        other: 2\r\n      },\r\n      localRegulations: ['Chicago Energy Code', 'Illinois Energy Efficiency Standards']\r\n    }\r\n  );\r\n\r\n  console.log('Environmental Impact Assessment Results:');\r\n  console.log(`Total carbon emissions: ${environmentalAnalysis.carbonFootprint.totalEmissions.value.toLocaleString()} kg CO2e/year`);\r\n  console.log(`Operational emissions: ${environmentalAnalysis.carbonFootprint.operationalEmissions.value.toLocaleString()} kg CO2e/year`);\r\n  console.log(`Embodied emissions: ${environmentalAnalysis.carbonFootprint.embodiedEmissions.value.toLocaleString()} kg CO2e/year`);\r\n  console.log(`Environmental score: ${environmentalAnalysis.sustainabilityMetrics.environmentalScore.overallScore.toFixed(1)}/100`);\r\n  console.log(`LEED readiness: ${environmentalAnalysis.sustainabilityMetrics.certificationReadiness.leedReadiness}`);\r\n  console.log(`Carbon offset opportunities: ${environmentalAnalysis.carbonFootprint.offsetOpportunities.length} opportunities\\n`);\r\n\r\n  // Step 8: Perform compliance checking\r\n  const complianceAnalysis = await ComplianceCheckingEngine.performComplianceAnalysis(\r\n    officeSystem,\r\n    performanceMetrics,\r\n    energyAnalysis,\r\n    environmentalAnalysis,\r\n    ['ASHRAE 90.1', 'SMACNA', 'IECC 2021'],\r\n    {\r\n      country: 'US',\r\n      state: 'IL',\r\n      city: 'Chicago',\r\n      climateZone: '4A',\r\n      jurisdiction: 'Chicago'\r\n    }\r\n  );\r\n\r\n  console.log('Compliance Analysis Results:');\r\n  console.log(`Overall compliance: ${complianceAnalysis.overallCompliance.compliancePercentage.toFixed(1)}%`);\r\n  console.log(`ASHRAE 90.1 compliance: ${complianceAnalysis.ashraeCompliance.overallStatus}`);\r\n  console.log(`SMACNA compliance: ${complianceAnalysis.smacnaCompliance.overallStatus}`);\r\n  console.log(`Energy code compliance: ${complianceAnalysis.energyCodeCompliance.overallStatus}`);\r\n  console.log(`Critical issues: ${complianceAnalysis.overallCompliance.criticalIssues}`);\r\n  console.log(`Warnings: ${complianceAnalysis.overallCompliance.warnings}`);\r\n  console.log(`Compliance recommendations: ${complianceAnalysis.recommendations.length} recommendations\\n`);\r\n\r\n  // Step 9: Generate summary report\r\n  console.log('=== ANALYSIS SUMMARY ===');\r\n  console.log(`System: ${officeSystem.name}`);\r\n  console.log(`Performance Score: ${performanceAnalysis.performanceMetrics.systemEfficiency.value}%`);\r\n  console.log(`Energy Efficiency: ${energyAnalysis.efficiencyMetrics.specificFanPower.toFixed(2)} W/CFM`);\r\n  console.log(`20-Year Total Cost: $${costAnalysis.totalCostOfOwnership.totalPresentValue.toLocaleString()}`);\r\n  console.log(`Environmental Score: ${environmentalAnalysis.sustainabilityMetrics.environmentalScore.overallScore.toFixed(1)}/100`);\r\n  console.log(`Compliance: ${complianceAnalysis.overallCompliance.compliancePercentage.toFixed(1)}%`);\r\n  \r\n  const totalRecommendations = performanceAnalysis.recommendations.length + \r\n                              costAnalysis.recommendations.length + \r\n                              complianceAnalysis.recommendations.length;\r\n  console.log(`Total Recommendations: ${totalRecommendations}`);\r\n  console.log('=== END ANALYSIS ===\\n');\r\n}\r\n\r\n/**\r\n * Example 2: High-Performance System Optimization\r\n * \r\n * This example shows how to use the analysis tools to evaluate and optimize\r\n * a high-performance HVAC system for maximum efficiency and compliance.\r\n */\r\nexport async function highPerformanceOptimizationExample(): Promise<void> {\r\n  console.log('=== High-Performance System Optimization Example ===\\n');\r\n\r\n  const highPerformanceSystem: SystemConfiguration = {\r\n    id: 'high_performance_001',\r\n    name: 'LEED Platinum Office Building System',\r\n    systemType: 'supply_air',\r\n    designParameters: {\r\n      designAirflow: 15000,\r\n      designPressure: 2.8, // Lower pressure for efficiency\r\n      designTemperature: 75,\r\n      designHumidity: 50,\r\n      elevation: 1200,\r\n      airDensity: 0.074\r\n    },\r\n    ductConfiguration: {\r\n      shape: 'round', // More efficient shape\r\n      material: 'galvanized_steel',\r\n      insulation: {\r\n        type: 'high_performance_foam',\r\n        thickness: 3,\r\n        rValue: 12.0 // Higher R-value\r\n      },\r\n      sealingClass: 'class_a_plus' // Enhanced sealing\r\n    },\r\n    operatingConditions: {\r\n      operatingHours: 2200, // Optimized schedule\r\n      loadFactor: 0.7, // Variable load operation\r\n      seasonalVariation: 0.3,\r\n      maintenanceSchedule: 'enhanced'\r\n    },\r\n    location: {\r\n      climateZone: '5A',\r\n      region: 'US-CO',\r\n      jurisdiction: 'Denver'\r\n    }\r\n  };\r\n\r\n  // Simulate high-performance metrics\r\n  const highPerformanceMetrics: PerformanceMetrics = {\r\n    totalSystemPressure: {\r\n      value: 2.6,\r\n      units: 'in_wg',\r\n      accuracy: 0.98,\r\n      measurementSource: 'measured',\r\n      timestamp: new Date()\r\n    },\r\n    airflowEfficiency: {\r\n      value: 94,\r\n      units: 'percent',\r\n      accuracy: 0.95,\r\n      measurementSource: 'measured',\r\n      timestamp: new Date()\r\n    },\r\n    fanPerformance: {\r\n      value: 89,\r\n      units: 'percent',\r\n      accuracy: 0.9,\r\n      measurementSource: 'measured',\r\n      timestamp: new Date()\r\n    },\r\n    systemEfficiency: {\r\n      value: 91,\r\n      units: 'percent',\r\n      accuracy: 0.95,\r\n      measurementSource: 'measured',\r\n      timestamp: new Date()\r\n    },\r\n    environmentalMetrics: {\r\n      value: 88,\r\n      units: 'score',\r\n      accuracy: 0.9,\r\n      measurementSource: 'calculated',\r\n      timestamp: new Date()\r\n    },\r\n    systemBalance: {\r\n      value: 97,\r\n      units: 'percent',\r\n      accuracy: 0.9,\r\n      measurementSource: 'measured',\r\n      timestamp: new Date()\r\n    }\r\n  };\r\n\r\n  // Perform comprehensive analysis\r\n  const performanceAnalysis = await SystemPerformanceAnalysisEngine.analyzeSystemPerformance(\r\n    highPerformanceSystem,\r\n    highPerformanceMetrics\r\n  );\r\n\r\n  const energyAnalysis = await EnergyEfficiencyAnalysisEngine.analyzeEnergyEfficiency(\r\n    highPerformanceSystem,\r\n    highPerformanceMetrics\r\n  );\r\n\r\n  const environmentalAnalysis = await EnvironmentalImpactAssessmentEngine.assessEnvironmentalImpact(\r\n    highPerformanceSystem,\r\n    energyAnalysis\r\n  );\r\n\r\n  console.log('High-Performance System Results:');\r\n  console.log(`System efficiency: ${performanceAnalysis.performanceMetrics.systemEfficiency.value}%`);\r\n  console.log(`Specific fan power: ${energyAnalysis.efficiencyMetrics.specificFanPower.toFixed(2)} W/CFM`);\r\n  console.log(`Environmental score: ${environmentalAnalysis.sustainabilityMetrics.environmentalScore.overallScore.toFixed(1)}/100`);\r\n  console.log(`LEED readiness: ${environmentalAnalysis.sustainabilityMetrics.certificationReadiness.leedReadiness}`);\r\n  console.log(`Energy Star readiness: ${environmentalAnalysis.sustainabilityMetrics.certificationReadiness.energyStarReadiness}\\n`);\r\n\r\n  // Compare with baseline system\r\n  console.log('Performance Comparison vs. Standard System:');\r\n  console.log(`Efficiency improvement: +${(91 - 84).toFixed(1)}%`);\r\n  console.log(`Energy savings: ~${((1.25 - energyAnalysis.efficiencyMetrics.specificFanPower) / 1.25 * 100).toFixed(1)}%`);\r\n  console.log(`Environmental score improvement: +${(88 - 78).toFixed(1)} points`);\r\n  console.log('=== END OPTIMIZATION EXAMPLE ===\\n');\r\n}\r\n\r\n/**\r\n * Example 3: Retrofit Analysis and Comparison\r\n * \r\n * This example demonstrates how to analyze and compare different retrofit\r\n * options for an existing HVAC system.\r\n */\r\nexport async function retrofitAnalysisExample(): Promise<void> {\r\n  console.log('=== Retrofit Analysis and Comparison Example ===\\n');\r\n\r\n  // Define existing system\r\n  const existingSystem: SystemConfiguration = {\r\n    id: 'existing_system_001',\r\n    name: 'Existing 1990s Office Building System',\r\n    systemType: 'supply_air',\r\n    designParameters: {\r\n      designAirflow: 20000,\r\n      designPressure: 5.5, // High pressure - inefficient\r\n      designTemperature: 75,\r\n      designHumidity: 50,\r\n      elevation: 800,\r\n      airDensity: 0.075\r\n    },\r\n    ductConfiguration: {\r\n      shape: 'rectangular',\r\n      material: 'galvanized_steel',\r\n      insulation: {\r\n        type: 'fiberglass',\r\n        thickness: 1, // Minimal insulation\r\n        rValue: 3.0\r\n      },\r\n      sealingClass: 'class_c' // Poor sealing\r\n    },\r\n    operatingConditions: {\r\n      operatingHours: 3200, // Inefficient operation\r\n      loadFactor: 0.9, // Constant high load\r\n      seasonalVariation: 0.1,\r\n      maintenanceSchedule: 'minimal'\r\n    }\r\n  };\r\n\r\n  // Define retrofit options\r\n  const retrofitOption1: SystemConfiguration = {\r\n    ...existingSystem,\r\n    id: 'retrofit_option_1',\r\n    name: 'Retrofit Option 1: Duct Sealing + Controls',\r\n    designParameters: {\r\n      ...existingSystem.designParameters,\r\n      designPressure: 4.8 // Improved with sealing\r\n    },\r\n    ductConfiguration: {\r\n      ...existingSystem.ductConfiguration,\r\n      sealingClass: 'class_a' // Improved sealing\r\n    },\r\n    operatingConditions: {\r\n      ...existingSystem.operatingConditions,\r\n      operatingHours: 2800, // Better controls\r\n      loadFactor: 0.75\r\n    }\r\n  };\r\n\r\n  const retrofitOption2: SystemConfiguration = {\r\n    ...existingSystem,\r\n    id: 'retrofit_option_2',\r\n    name: 'Retrofit Option 2: Complete System Upgrade',\r\n    designParameters: {\r\n      ...existingSystem.designParameters,\r\n      designPressure: 3.2 // New ductwork design\r\n    },\r\n    ductConfiguration: {\r\n      shape: 'round', // More efficient\r\n      material: 'galvanized_steel',\r\n      insulation: {\r\n        type: 'high_performance_foam',\r\n        thickness: 2.5,\r\n        rValue: 10.0\r\n      },\r\n      sealingClass: 'class_a_plus'\r\n    },\r\n    operatingConditions: {\r\n      operatingHours: 2400, // VFD and advanced controls\r\n      loadFactor: 0.65,\r\n      seasonalVariation: 0.25,\r\n      maintenanceSchedule: 'standard'\r\n    }\r\n  };\r\n\r\n  console.log('Analyzing retrofit options...\\n');\r\n\r\n  // Analyze each option (simplified for example)\r\n  const options = [\r\n    { config: existingSystem, name: 'Existing System' },\r\n    { config: retrofitOption1, name: 'Retrofit Option 1' },\r\n    { config: retrofitOption2, name: 'Retrofit Option 2' }\r\n  ];\r\n\r\n  for (const option of options) {\r\n    console.log(`--- ${option.name} ---`);\r\n    \r\n    // Simulate performance metrics based on system characteristics\r\n    const sfp = option.config.designPressure * 0.3; // Simplified SFP calculation\r\n    const efficiency = Math.max(60, 100 - option.config.designPressure * 8);\r\n    \r\n    console.log(`Estimated SFP: ${sfp.toFixed(2)} W/CFM`);\r\n    console.log(`Estimated efficiency: ${efficiency.toFixed(1)}%`);\r\n    console.log(`Operating hours: ${option.config.operatingConditions?.operatingHours || 'N/A'}`);\r\n    console.log(`Sealing class: ${option.config.ductConfiguration?.sealingClass || 'N/A'}`);\r\n    console.log('');\r\n  }\r\n\r\n  console.log('Retrofit Comparison Summary:');\r\n  console.log('Option 1 (Sealing + Controls): Moderate cost, good ROI');\r\n  console.log('Option 2 (Complete Upgrade): High cost, excellent performance');\r\n  console.log('Recommendation: Start with Option 1, plan Option 2 for future');\r\n  console.log('=== END RETROFIT EXAMPLE ===\\n');\r\n}\r\n\r\n// Export all examples for easy execution\r\nexport const SystemAnalysisExamples = {\r\n  completeOfficeAnalysisExample,\r\n  highPerformanceOptimizationExample,\r\n  retrofitAnalysisExample\r\n};\r\n"], "mappings": ";;AAAA;;;;;;;;;AAAA;AAAA,SAAAA,eAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;MAAA;QAAAC,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAO,SAAA;MAAA;QAAAD,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAU,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;IAAA;IAAAC,cAAA;MAAAC,IAAA;MAAAC,QAAA;MAAAC,KAAA;MAAAC,OAAA;MAAAC,cAAA;MAAAC,OAAA;IAAA;IAAAC,eAAA;IAAA3B,IAAA;EAAA;EAAA,IAAA4B,QAAA,GAAA3B,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAAyB,QAAA,CAAA7B,IAAA,KAAA6B,QAAA,CAAA7B,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAA4B,QAAA,CAAA7B,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAAyB,cAAA,GAAAD,QAAA,CAAA7B,IAAA;EAAA;IAUA;IAAAD,cAAA,YAAAA,CAAA;MAAA,OAAA+B,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAA/B,cAAA;AAAAA,cAAA,GAAAmB,CAAA;;;;;;;;;AAqBAa,OAAA,CAAAC,6BAAA,GAAAA,6BAAA;AAkQC;AAAAjC,cAAA,GAAAmB,CAAA;AAQDa,OAAA,CAAAE,kCAAA,GAAAA,kCAAA;AAiHC;AAAAlC,cAAA,GAAAmB,CAAA;AAQDa,OAAA,CAAAG,uBAAA,GAAAA,uBAAA;AAxZA,MAAAC,iCAAA;AAAA;AAAA,CAAApC,cAAA,GAAAmB,CAAA,OAAAkB,OAAA;AACA,MAAAC,gCAAA;AAAA;AAAA,CAAAtC,cAAA,GAAAmB,CAAA,OAAAkB,OAAA;AACA,MAAAE,6BAAA;AAAA;AAAA,CAAAvC,cAAA,GAAAmB,CAAA,OAAAkB,OAAA;AACA,MAAAG,qCAAA;AAAA;AAAA,CAAAxC,cAAA,GAAAmB,CAAA,OAAAkB,OAAA;AACA,MAAAI,0BAAA;AAAA;AAAA,CAAAzC,cAAA,GAAAmB,CAAA,OAAAkB,OAAA;AACA,MAAAK,0BAAA;AAAA;AAAA,CAAA1C,cAAA,GAAAmB,CAAA,QAAAkB,OAAA;AAGA,MAAAM,qBAAA;AAAA;AAAA,CAAA3C,cAAA,GAAAmB,CAAA,QAAAkB,OAAA;AAOA;;;;;;AAMO,eAAeJ,6BAA6BA,CAAA;EAAA;EAAAjC,cAAA,GAAAoB,CAAA;EAAApB,cAAA,GAAAmB,CAAA;EACjDyB,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;EAEvE;EACA,MAAMC,YAAY;EAAA;EAAA,CAAA9C,cAAA,GAAAmB,CAAA,QAAwB;IACxC4B,EAAE,EAAE,qBAAqB;IACzBlC,IAAI,EAAE,0CAA0C;IAChDmC,UAAU,EAAE,YAAY;IACxBC,gBAAgB,EAAE;MAChBC,aAAa,EAAE,KAAK;MAAE;MACtBC,cAAc,EAAE,GAAG;MAAE;MACrBC,iBAAiB,EAAE,EAAE;MAAE;MACvBC,cAAc,EAAE,EAAE;MAAE;MACpBC,SAAS,EAAE,GAAG;MAAE;MAChBC,UAAU,EAAE,KAAK,CAAC;KACnB;IACDC,iBAAiB,EAAE;MACjBC,KAAK,EAAE,aAAa;MACpBC,QAAQ,EAAE,kBAAkB;MAC5BC,UAAU,EAAE;QACV1C,IAAI,EAAE,YAAY;QAClB2C,SAAS,EAAE,CAAC;QACZC,MAAM,EAAE;OACT;MACDC,YAAY,EAAE;KACf;IACDC,mBAAmB,EAAE;MACnBC,cAAc,EAAE,IAAI;MAAE;MACtBC,UAAU,EAAE,GAAG;MACfC,iBAAiB,EAAE,IAAI;MACvBC,mBAAmB,EAAE;KACtB;IACDC,QAAQ,EAAE;MACRC,WAAW,EAAE,IAAI;MACjBC,MAAM,EAAE,OAAO;MACfC,YAAY,EAAE;;GAEjB;EAAC;EAAAvE,cAAA,GAAAmB,CAAA;EAEFyB,OAAO,CAACC,GAAG,CAAC,qBAAqBC,YAAY,CAACjC,IAAI,EAAE,CAAC;EAAC;EAAAb,cAAA,GAAAmB,CAAA;EACtDyB,OAAO,CAACC,GAAG,CAAC,mBAAmBC,YAAY,CAACG,gBAAgB,CAACC,aAAa,CAACsB,cAAc,EAAE,MAAM,CAAC;EAAC;EAAAxE,cAAA,GAAAmB,CAAA;EACnGyB,OAAO,CAACC,GAAG,CAAC,oBAAoBC,YAAY,CAACG,gBAAgB,CAACE,cAAc,aAAa,CAAC;EAE1F;EACA,MAAMsB,oBAAoB;EAAA;EAAA,CAAAzE,cAAA,GAAAmB,CAAA,QAAG,MAAMuB,0BAAA,CAAAgC,wBAAwB,CAACC,uBAAuB,CAAC;IAClFC,OAAO,EAAE9B,YAAY,CAACG,gBAAgB,CAACC,aAAa;IACpD2B,YAAY,EAAE,CACZ;MACE9B,EAAE,EAAE,mBAAmB;MACvB+B,MAAM,EAAE,GAAG;MACXC,iBAAiB,EAAE,GAAG;MACtBC,SAAS,EAAE,MAAM;MACjBC,IAAI,EAAE,GAAG;MACTC,QAAQ,EAAEpC,YAAY,CAACG,gBAAgB,CAACC,aAAa,GAAG,GAAG,GAAG;KAC/D,EACD;MACEH,EAAE,EAAE,eAAe;MACnB+B,MAAM,EAAE,EAAE;MACVC,iBAAiB,EAAE,GAAG;MACtBC,SAAS,EAAE,MAAM;MACjBC,IAAI,EAAE,IAAI;MACVC,QAAQ,EAAE,IAAI,GAAG,IAAI,GAAG;KACzB,CACF;IACDC,QAAQ,EAAE,CACR;MACEpC,EAAE,EAAE,YAAY;MAChB9B,IAAI,EAAE,sBAAsB;MAC5BmE,OAAO,EAAE,IAAI;MACbR,OAAO,EAAE9B,YAAY,CAACG,gBAAgB,CAACC;KACxC,EACD;MACEH,EAAE,EAAE,YAAY;MAChB9B,IAAI,EAAE,YAAY;MAClBmE,OAAO,EAAE,GAAG;MACZR,OAAO,EAAE;KACV,CACF;IACDS,aAAa,EAAE;MACbC,OAAO,EAAExC,YAAY,CAACG,gBAAgB,CAACM,UAAU;MACjDgC,SAAS,EAAE,MAAM;MACjBC,WAAW,EAAE1C,YAAY,CAACG,gBAAgB,CAACG;;GAE9C,CAAC;EAAC;EAAApD,cAAA,GAAAmB,CAAA;EAEHyB,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;EAAC;EAAA7C,cAAA,GAAAmB,CAAA;EACpDyB,OAAO,CAACC,GAAG,CAAC,wBAAwB4B,oBAAoB,CAACgB,iBAAiB,CAACC,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC;EAAC;EAAA1F,cAAA,GAAAmB,CAAA;EAClGyB,OAAO,CAACC,GAAG,CAAC,oBAAoB4B,oBAAoB,CAACkB,cAAc,CAACD,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC;EAAC;EAAA1F,cAAA,GAAAmB,CAAA;EAC3FyB,OAAO,CAACC,GAAG,CAAC,mBAAmB4B,oBAAoB,CAACmB,aAAa,CAACF,OAAO,CAAC,CAAC,CAAC,aAAa,CAAC;EAE1F;EACA,MAAMG,kBAAkB;EAAA;EAAA,CAAA7F,cAAA,GAAAmB,CAAA,QAAuB;IAC7C2E,mBAAmB,EAAE;MACnBC,KAAK,EAAEtB,oBAAoB,CAACgB,iBAAiB;MAC7CO,KAAK,EAAE,OAAO;MACdC,QAAQ,EAAE,IAAI;MACdC,iBAAiB,EAAE,YAAY;MAC/BC,SAAS,EAAE,IAAIC,IAAI;KACpB;IACDC,iBAAiB,EAAE;MACjBN,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,SAAS;MAChBC,QAAQ,EAAE,GAAG;MACbC,iBAAiB,EAAE,YAAY;MAC/BC,SAAS,EAAE,IAAIC,IAAI;KACpB;IACDE,cAAc,EAAE;MACdP,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,SAAS;MAChBC,QAAQ,EAAE,IAAI;MACdC,iBAAiB,EAAE,WAAW;MAC9BC,SAAS,EAAE,IAAIC,IAAI;KACpB;IACDG,gBAAgB,EAAE;MAChBR,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,SAAS;MAChBC,QAAQ,EAAE,GAAG;MACbC,iBAAiB,EAAE,YAAY;MAC/BC,SAAS,EAAE,IAAIC,IAAI;KACpB;IACDI,oBAAoB,EAAE;MACpBT,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,OAAO;MACdC,QAAQ,EAAE,GAAG;MACbC,iBAAiB,EAAE,YAAY;MAC/BC,SAAS,EAAE,IAAIC,IAAI;KACpB;IACDK,aAAa,EAAE;MACbV,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,SAAS;MAChBC,QAAQ,EAAE,IAAI;MACdC,iBAAiB,EAAE,YAAY;MAC/BC,SAAS,EAAE,IAAIC,IAAI;;GAEtB;EAED;EACA,MAAMM,mBAAmB;EAAA;EAAA,CAAA1G,cAAA,GAAAmB,CAAA,QAAG,MAAMiB,iCAAA,CAAAuE,+BAA+B,CAACC,wBAAwB,CACxF9D,YAAY,EACZ+C,kBAAkB,CACnB;EAAC;EAAA7F,cAAA,GAAAmB,CAAA;EAEFyB,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;EAAC;EAAA7C,cAAA,GAAAmB,CAAA;EACpDyB,OAAO,CAACC,GAAG,CAAC,gBAAgB6D,mBAAmB,CAAC3D,EAAE,EAAE,CAAC;EAAC;EAAA/C,cAAA,GAAAmB,CAAA;EACtDyB,OAAO,CAACC,GAAG,CAAC,8BAA8B6D,mBAAmB,CAACb,kBAAkB,CAACU,gBAAgB,CAACR,KAAK,GAAG,CAAC;EAAC;EAAA/F,cAAA,GAAAmB,CAAA;EAC5GyB,OAAO,CAACC,GAAG,CAAC,sBAAsB6D,mBAAmB,CAACG,aAAa,CAACC,cAAc,EAAE,CAAC;EAAC;EAAA9G,cAAA,GAAAmB,CAAA;EACtFyB,OAAO,CAACC,GAAG,CAAC,yBAAyB6D,mBAAmB,CAACK,mBAAmB,CAACC,UAAU,eAAe,CAAC;EAAC;EAAAhH,cAAA,GAAAmB,CAAA;EACxGyB,OAAO,CAACC,GAAG,CAAC,uBAAuB6D,mBAAmB,CAACO,MAAM,CAACnC,MAAM,mBAAmB,CAAC;EAAC;EAAA9E,cAAA,GAAAmB,CAAA;EACzFyB,OAAO,CAACC,GAAG,CAAC,oBAAoB6D,mBAAmB,CAACQ,eAAe,CAACpC,MAAM,oBAAoB,CAAC;EAE/F;EACA,MAAMqC,cAAc;EAAA;EAAA,CAAAnH,cAAA,GAAAmB,CAAA,QAAG,MAAMmB,gCAAA,CAAA8E,8BAA8B,CAACC,uBAAuB,CACjFvE,YAAY,EACZ+C,kBAAkB,CACnB;EAAC;EAAA7F,cAAA,GAAAmB,CAAA;EAEFyB,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;EAAC;EAAA7C,cAAA,GAAAmB,CAAA;EACnDyB,OAAO,CAACC,GAAG,CAAC,8BAA8BsE,cAAc,CAACG,iBAAiB,CAACC,gBAAgB,CAACxB,KAAK,CAACvB,cAAc,EAAE,MAAM,CAAC;EAAC;EAAAxE,cAAA,GAAAmB,CAAA;EAC1HyB,OAAO,CAACC,GAAG,CAAC,uBAAuBsE,cAAc,CAACK,iBAAiB,CAACC,gBAAgB,CAAC/B,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC;EAAC;EAAA1F,cAAA,GAAAmB,CAAA;EACzGyB,OAAO,CAACC,GAAG,CAAC,wBAAwBsE,cAAc,CAACO,WAAW,CAACC,YAAY,CAACC,SAAS,CAACpD,cAAc,EAAE,EAAE,CAAC;EAAC;EAAAxE,cAAA,GAAAmB,CAAA;EAC1GyB,OAAO,CAACC,GAAG,CAAC,qBAAqBsE,cAAc,CAACU,eAAe,CAACC,cAAc,CAAC/B,KAAK,CAACvB,cAAc,EAAE,eAAe,CAAC;EAAC;EAAAxE,cAAA,GAAAmB,CAAA;EACtHyB,OAAO,CAACC,GAAG,CAAC,qBAAqBsE,cAAc,CAACJ,mBAAmB,CAACgB,aAAa,MAAMZ,cAAc,CAACJ,mBAAmB,CAACC,UAAU,iBAAiB,CAAC;EAEtJ;EACA,MAAMgB,YAAY;EAAA;EAAA,CAAAhI,cAAA,GAAAmB,CAAA,QAAG,MAAMoB,6BAAA,CAAA0F,2BAA2B,CAACC,qBAAqB,CAC1EpF,YAAY,EACZqE,cAAc,EACd;IACEgB,eAAe,EAAE,EAAE;IACnBC,YAAY,EAAE,IAAI;IAClBC,aAAa,EAAE,KAAK;IACpBC,oBAAoB,EAAE,IAAI;IAC1BC,QAAQ,EAAE,KAAK;IACfC,cAAc,EAAE7F,qBAAA,CAAA8F,kBAAkB,CAACC,iBAAiB;IACpDC,gBAAgB,EAAEhG,qBAAA,CAAAiG,gBAAgB,CAACC;GACpC,CACF;EAAC;EAAA7I,cAAA,GAAAmB,CAAA;EAEFyB,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;EAAC;EAAA7C,cAAA,GAAAmB,CAAA;EAChDyB,OAAO,CAACC,GAAG,CAAC,kBAAkBmF,YAAY,CAACc,YAAY,CAACC,gBAAgB,CAACvE,cAAc,EAAE,EAAE,CAAC;EAAC;EAAAxE,cAAA,GAAAmB,CAAA;EAC7FyB,OAAO,CAACC,GAAG,CAAC,kCAAkCmF,YAAY,CAACgB,cAAc,CAACC,iBAAiB,CAACzE,cAAc,EAAE,EAAE,CAAC;EAAC;EAAAxE,cAAA,GAAAmB,CAAA;EAChHyB,OAAO,CAACC,GAAG,CAAC,oCAAoCmF,YAAY,CAACkB,gBAAgB,CAACD,iBAAiB,CAACzE,cAAc,EAAE,EAAE,CAAC;EAAC;EAAAxE,cAAA,GAAAmB,CAAA;EACpHyB,OAAO,CAACC,GAAG,CAAC,kCAAkCmF,YAAY,CAACmB,oBAAoB,CAACF,iBAAiB,CAACzE,cAAc,EAAE,EAAE,CAAC;EAAC;EAAAxE,cAAA,GAAAmB,CAAA;EACtHyB,OAAO,CAACC,GAAG,CAAC,kBAAkBmF,YAAY,CAACmB,oBAAoB,CAACC,UAAU,CAAC1D,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;EAAC;EAAA1F,cAAA,GAAAmB,CAAA;EACzFyB,OAAO,CAACC,GAAG,CAAC,0BAA0BmF,YAAY,CAACmB,oBAAoB,CAACE,aAAa,CAAC3D,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC;EAAC;EAAA1F,cAAA,GAAAmB,CAAA;EAC1GyB,OAAO,CAACC,GAAG,CAAC,sCAAsCmF,YAAY,CAACd,eAAe,CAACpC,MAAM,oBAAoB,CAAC;EAE1G;EACA,MAAMwE,qBAAqB;EAAA;EAAA,CAAAtJ,cAAA,GAAAmB,CAAA,QAAG,MAAMqB,qCAAA,CAAA+G,mCAAmC,CAACC,yBAAyB,CAC/F1G,YAAY,EACZqE,cAAc,EACd;IACEsC,oBAAoB,EAAE,IAAI;IAC1BC,WAAW,EAAE,UAAU;IACvBxF,iBAAiB,EAAE,EAAE;IACrByF,YAAY,EAAE;GACf,EACD;IACErF,MAAM,EAAE,OAAO;IACfD,WAAW,EAAE,IAAI;IACjBuF,OAAO,EAAE;MACPC,SAAS,EAAE,EAAE;MACbC,OAAO,EAAE,EAAE;MACXC,UAAU,EAAE,EAAE;MACdC,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE;KACR;IACDC,gBAAgB,EAAE,CAAC,qBAAqB,EAAE,sCAAsC;GACjF,CACF;EAAC;EAAAlK,cAAA,GAAAmB,CAAA;EAEFyB,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;EAAC;EAAA7C,cAAA,GAAAmB,CAAA;EACxDyB,OAAO,CAACC,GAAG,CAAC,2BAA2ByG,qBAAqB,CAACzB,eAAe,CAACC,cAAc,CAAC/B,KAAK,CAACvB,cAAc,EAAE,eAAe,CAAC;EAAC;EAAAxE,cAAA,GAAAmB,CAAA;EACnIyB,OAAO,CAACC,GAAG,CAAC,0BAA0ByG,qBAAqB,CAACzB,eAAe,CAACsC,oBAAoB,CAACpE,KAAK,CAACvB,cAAc,EAAE,eAAe,CAAC;EAAC;EAAAxE,cAAA,GAAAmB,CAAA;EACxIyB,OAAO,CAACC,GAAG,CAAC,uBAAuByG,qBAAqB,CAACzB,eAAe,CAACuC,iBAAiB,CAACrE,KAAK,CAACvB,cAAc,EAAE,eAAe,CAAC;EAAC;EAAAxE,cAAA,GAAAmB,CAAA;EAClIyB,OAAO,CAACC,GAAG,CAAC,wBAAwByG,qBAAqB,CAACe,qBAAqB,CAACC,kBAAkB,CAACC,YAAY,CAAC7E,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC;EAAC;EAAA1F,cAAA,GAAAmB,CAAA;EAClIyB,OAAO,CAACC,GAAG,CAAC,mBAAmByG,qBAAqB,CAACe,qBAAqB,CAACG,sBAAsB,CAACC,aAAa,EAAE,CAAC;EAAC;EAAAzK,cAAA,GAAAmB,CAAA;EACnHyB,OAAO,CAACC,GAAG,CAAC,gCAAgCyG,qBAAqB,CAACzB,eAAe,CAAC6C,mBAAmB,CAAC5F,MAAM,kBAAkB,CAAC;EAE/H;EACA,MAAM6F,kBAAkB;EAAA;EAAA,CAAA3K,cAAA,GAAAmB,CAAA,QAAG,MAAMsB,0BAAA,CAAAmI,wBAAwB,CAACC,yBAAyB,CACjF/H,YAAY,EACZ+C,kBAAkB,EAClBsB,cAAc,EACdmC,qBAAqB,EACrB,CAAC,aAAa,EAAE,QAAQ,EAAE,WAAW,CAAC,EACtC;IACEwB,OAAO,EAAE,IAAI;IACbC,KAAK,EAAE,IAAI;IACXC,IAAI,EAAE,SAAS;IACf3G,WAAW,EAAE,IAAI;IACjBE,YAAY,EAAE;GACf,CACF;EAAC;EAAAvE,cAAA,GAAAmB,CAAA;EAEFyB,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;EAAC;EAAA7C,cAAA,GAAAmB,CAAA;EAC5CyB,OAAO,CAACC,GAAG,CAAC,uBAAuB8H,kBAAkB,CAACM,iBAAiB,CAACC,oBAAoB,CAACxF,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;EAAC;EAAA1F,cAAA,GAAAmB,CAAA;EAC5GyB,OAAO,CAACC,GAAG,CAAC,2BAA2B8H,kBAAkB,CAACQ,gBAAgB,CAACC,aAAa,EAAE,CAAC;EAAC;EAAApL,cAAA,GAAAmB,CAAA;EAC5FyB,OAAO,CAACC,GAAG,CAAC,sBAAsB8H,kBAAkB,CAACU,gBAAgB,CAACD,aAAa,EAAE,CAAC;EAAC;EAAApL,cAAA,GAAAmB,CAAA;EACvFyB,OAAO,CAACC,GAAG,CAAC,2BAA2B8H,kBAAkB,CAACW,oBAAoB,CAACF,aAAa,EAAE,CAAC;EAAC;EAAApL,cAAA,GAAAmB,CAAA;EAChGyB,OAAO,CAACC,GAAG,CAAC,oBAAoB8H,kBAAkB,CAACM,iBAAiB,CAACM,cAAc,EAAE,CAAC;EAAC;EAAAvL,cAAA,GAAAmB,CAAA;EACvFyB,OAAO,CAACC,GAAG,CAAC,aAAa8H,kBAAkB,CAACM,iBAAiB,CAACO,QAAQ,EAAE,CAAC;EAAC;EAAAxL,cAAA,GAAAmB,CAAA;EAC1EyB,OAAO,CAACC,GAAG,CAAC,+BAA+B8H,kBAAkB,CAACzD,eAAe,CAACpC,MAAM,oBAAoB,CAAC;EAEzG;EAAA;EAAA9E,cAAA,GAAAmB,CAAA;EACAyB,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;EAAC;EAAA7C,cAAA,GAAAmB,CAAA;EACxCyB,OAAO,CAACC,GAAG,CAAC,WAAWC,YAAY,CAACjC,IAAI,EAAE,CAAC;EAAC;EAAAb,cAAA,GAAAmB,CAAA;EAC5CyB,OAAO,CAACC,GAAG,CAAC,sBAAsB6D,mBAAmB,CAACb,kBAAkB,CAACU,gBAAgB,CAACR,KAAK,GAAG,CAAC;EAAC;EAAA/F,cAAA,GAAAmB,CAAA;EACpGyB,OAAO,CAACC,GAAG,CAAC,sBAAsBsE,cAAc,CAACK,iBAAiB,CAACC,gBAAgB,CAAC/B,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC;EAAC;EAAA1F,cAAA,GAAAmB,CAAA;EACxGyB,OAAO,CAACC,GAAG,CAAC,wBAAwBmF,YAAY,CAACmB,oBAAoB,CAACF,iBAAiB,CAACzE,cAAc,EAAE,EAAE,CAAC;EAAC;EAAAxE,cAAA,GAAAmB,CAAA;EAC5GyB,OAAO,CAACC,GAAG,CAAC,wBAAwByG,qBAAqB,CAACe,qBAAqB,CAACC,kBAAkB,CAACC,YAAY,CAAC7E,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC;EAAC;EAAA1F,cAAA,GAAAmB,CAAA;EAClIyB,OAAO,CAACC,GAAG,CAAC,eAAe8H,kBAAkB,CAACM,iBAAiB,CAACC,oBAAoB,CAACxF,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;EAEnG,MAAM+F,oBAAoB;EAAA;EAAA,CAAAzL,cAAA,GAAAmB,CAAA,QAAGuF,mBAAmB,CAACQ,eAAe,CAACpC,MAAM,GAC3CkD,YAAY,CAACd,eAAe,CAACpC,MAAM,GACnC6F,kBAAkB,CAACzD,eAAe,CAACpC,MAAM;EAAC;EAAA9E,cAAA,GAAAmB,CAAA;EACtEyB,OAAO,CAACC,GAAG,CAAC,0BAA0B4I,oBAAoB,EAAE,CAAC;EAAC;EAAAzL,cAAA,GAAAmB,CAAA;EAC9DyB,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;AACvC;AAEA;;;;;;AAMO,eAAeX,kCAAkCA,CAAA;EAAA;EAAAlC,cAAA,GAAAoB,CAAA;EAAApB,cAAA,GAAAmB,CAAA;EACtDyB,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;EAErE,MAAM6I,qBAAqB;EAAA;EAAA,CAAA1L,cAAA,GAAAmB,CAAA,QAAwB;IACjD4B,EAAE,EAAE,sBAAsB;IAC1BlC,IAAI,EAAE,sCAAsC;IAC5CmC,UAAU,EAAE,YAAY;IACxBC,gBAAgB,EAAE;MAChBC,aAAa,EAAE,KAAK;MACpBC,cAAc,EAAE,GAAG;MAAE;MACrBC,iBAAiB,EAAE,EAAE;MACrBC,cAAc,EAAE,EAAE;MAClBC,SAAS,EAAE,IAAI;MACfC,UAAU,EAAE;KACb;IACDC,iBAAiB,EAAE;MACjBC,KAAK,EAAE,OAAO;MAAE;MAChBC,QAAQ,EAAE,kBAAkB;MAC5BC,UAAU,EAAE;QACV1C,IAAI,EAAE,uBAAuB;QAC7B2C,SAAS,EAAE,CAAC;QACZC,MAAM,EAAE,IAAI,CAAC;OACd;MACDC,YAAY,EAAE,cAAc,CAAC;KAC9B;IACDC,mBAAmB,EAAE;MACnBC,cAAc,EAAE,IAAI;MAAE;MACtBC,UAAU,EAAE,GAAG;MAAE;MACjBC,iBAAiB,EAAE,GAAG;MACtBC,mBAAmB,EAAE;KACtB;IACDC,QAAQ,EAAE;MACRC,WAAW,EAAE,IAAI;MACjBC,MAAM,EAAE,OAAO;MACfC,YAAY,EAAE;;GAEjB;EAED;EACA,MAAMoH,sBAAsB;EAAA;EAAA,CAAA3L,cAAA,GAAAmB,CAAA,QAAuB;IACjD2E,mBAAmB,EAAE;MACnBC,KAAK,EAAE,GAAG;MACVC,KAAK,EAAE,OAAO;MACdC,QAAQ,EAAE,IAAI;MACdC,iBAAiB,EAAE,UAAU;MAC7BC,SAAS,EAAE,IAAIC,IAAI;KACpB;IACDC,iBAAiB,EAAE;MACjBN,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,SAAS;MAChBC,QAAQ,EAAE,IAAI;MACdC,iBAAiB,EAAE,UAAU;MAC7BC,SAAS,EAAE,IAAIC,IAAI;KACpB;IACDE,cAAc,EAAE;MACdP,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,SAAS;MAChBC,QAAQ,EAAE,GAAG;MACbC,iBAAiB,EAAE,UAAU;MAC7BC,SAAS,EAAE,IAAIC,IAAI;KACpB;IACDG,gBAAgB,EAAE;MAChBR,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,SAAS;MAChBC,QAAQ,EAAE,IAAI;MACdC,iBAAiB,EAAE,UAAU;MAC7BC,SAAS,EAAE,IAAIC,IAAI;KACpB;IACDI,oBAAoB,EAAE;MACpBT,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,OAAO;MACdC,QAAQ,EAAE,GAAG;MACbC,iBAAiB,EAAE,YAAY;MAC/BC,SAAS,EAAE,IAAIC,IAAI;KACpB;IACDK,aAAa,EAAE;MACbV,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,SAAS;MAChBC,QAAQ,EAAE,GAAG;MACbC,iBAAiB,EAAE,UAAU;MAC7BC,SAAS,EAAE,IAAIC,IAAI;;GAEtB;EAED;EACA,MAAMM,mBAAmB;EAAA;EAAA,CAAA1G,cAAA,GAAAmB,CAAA,QAAG,MAAMiB,iCAAA,CAAAuE,+BAA+B,CAACC,wBAAwB,CACxF8E,qBAAqB,EACrBC,sBAAsB,CACvB;EAED,MAAMxE,cAAc;EAAA;EAAA,CAAAnH,cAAA,GAAAmB,CAAA,QAAG,MAAMmB,gCAAA,CAAA8E,8BAA8B,CAACC,uBAAuB,CACjFqE,qBAAqB,EACrBC,sBAAsB,CACvB;EAED,MAAMrC,qBAAqB;EAAA;EAAA,CAAAtJ,cAAA,GAAAmB,CAAA,QAAG,MAAMqB,qCAAA,CAAA+G,mCAAmC,CAACC,yBAAyB,CAC/FkC,qBAAqB,EACrBvE,cAAc,CACf;EAAC;EAAAnH,cAAA,GAAAmB,CAAA;EAEFyB,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;EAAC;EAAA7C,cAAA,GAAAmB,CAAA;EAChDyB,OAAO,CAACC,GAAG,CAAC,sBAAsB6D,mBAAmB,CAACb,kBAAkB,CAACU,gBAAgB,CAACR,KAAK,GAAG,CAAC;EAAC;EAAA/F,cAAA,GAAAmB,CAAA;EACpGyB,OAAO,CAACC,GAAG,CAAC,uBAAuBsE,cAAc,CAACK,iBAAiB,CAACC,gBAAgB,CAAC/B,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC;EAAC;EAAA1F,cAAA,GAAAmB,CAAA;EACzGyB,OAAO,CAACC,GAAG,CAAC,wBAAwByG,qBAAqB,CAACe,qBAAqB,CAACC,kBAAkB,CAACC,YAAY,CAAC7E,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC;EAAC;EAAA1F,cAAA,GAAAmB,CAAA;EAClIyB,OAAO,CAACC,GAAG,CAAC,mBAAmByG,qBAAqB,CAACe,qBAAqB,CAACG,sBAAsB,CAACC,aAAa,EAAE,CAAC;EAAC;EAAAzK,cAAA,GAAAmB,CAAA;EACnHyB,OAAO,CAACC,GAAG,CAAC,0BAA0ByG,qBAAqB,CAACe,qBAAqB,CAACG,sBAAsB,CAACoB,mBAAmB,IAAI,CAAC;EAEjI;EAAA;EAAA5L,cAAA,GAAAmB,CAAA;EACAyB,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;EAAC;EAAA7C,cAAA,GAAAmB,CAAA;EAC3DyB,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC,EAAE,GAAG,EAAE,EAAE6C,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;EAAC;EAAA1F,cAAA,GAAAmB,CAAA;EACjEyB,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC,CAAC,IAAI,GAAGsE,cAAc,CAACK,iBAAiB,CAACC,gBAAgB,IAAI,IAAI,GAAG,GAAG,EAAE/B,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;EAAC;EAAA1F,cAAA,GAAAmB,CAAA;EACzHyB,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC,EAAE,GAAG,EAAE,EAAE6C,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC;EAAC;EAAA1F,cAAA,GAAAmB,CAAA;EAChFyB,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;AACnD;AAEA;;;;;;AAMO,eAAeV,uBAAuBA,CAAA;EAAA;EAAAnC,cAAA,GAAAoB,CAAA;EAAApB,cAAA,GAAAmB,CAAA;EAC3CyB,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;EAEjE;EACA,MAAMgJ,cAAc;EAAA;EAAA,CAAA7L,cAAA,GAAAmB,CAAA,QAAwB;IAC1C4B,EAAE,EAAE,qBAAqB;IACzBlC,IAAI,EAAE,uCAAuC;IAC7CmC,UAAU,EAAE,YAAY;IACxBC,gBAAgB,EAAE;MAChBC,aAAa,EAAE,KAAK;MACpBC,cAAc,EAAE,GAAG;MAAE;MACrBC,iBAAiB,EAAE,EAAE;MACrBC,cAAc,EAAE,EAAE;MAClBC,SAAS,EAAE,GAAG;MACdC,UAAU,EAAE;KACb;IACDC,iBAAiB,EAAE;MACjBC,KAAK,EAAE,aAAa;MACpBC,QAAQ,EAAE,kBAAkB;MAC5BC,UAAU,EAAE;QACV1C,IAAI,EAAE,YAAY;QAClB2C,SAAS,EAAE,CAAC;QAAE;QACdC,MAAM,EAAE;OACT;MACDC,YAAY,EAAE,SAAS,CAAC;KACzB;IACDC,mBAAmB,EAAE;MACnBC,cAAc,EAAE,IAAI;MAAE;MACtBC,UAAU,EAAE,GAAG;MAAE;MACjBC,iBAAiB,EAAE,GAAG;MACtBC,mBAAmB,EAAE;;GAExB;EAED;EACA,MAAM2H,eAAe;EAAA;EAAA,CAAA9L,cAAA,GAAAmB,CAAA,QAAwB;IAC3C,GAAG0K,cAAc;IACjB9I,EAAE,EAAE,mBAAmB;IACvBlC,IAAI,EAAE,4CAA4C;IAClDoC,gBAAgB,EAAE;MAChB,GAAG4I,cAAc,CAAC5I,gBAAgB;MAClCE,cAAc,EAAE,GAAG,CAAC;KACrB;IACDK,iBAAiB,EAAE;MACjB,GAAGqI,cAAc,CAACrI,iBAAiB;MACnCM,YAAY,EAAE,SAAS,CAAC;KACzB;IACDC,mBAAmB,EAAE;MACnB,GAAG8H,cAAc,CAAC9H,mBAAmB;MACrCC,cAAc,EAAE,IAAI;MAAE;MACtBC,UAAU,EAAE;;GAEf;EAED,MAAM8H,eAAe;EAAA;EAAA,CAAA/L,cAAA,GAAAmB,CAAA,QAAwB;IAC3C,GAAG0K,cAAc;IACjB9I,EAAE,EAAE,mBAAmB;IACvBlC,IAAI,EAAE,4CAA4C;IAClDoC,gBAAgB,EAAE;MAChB,GAAG4I,cAAc,CAAC5I,gBAAgB;MAClCE,cAAc,EAAE,GAAG,CAAC;KACrB;IACDK,iBAAiB,EAAE;MACjBC,KAAK,EAAE,OAAO;MAAE;MAChBC,QAAQ,EAAE,kBAAkB;MAC5BC,UAAU,EAAE;QACV1C,IAAI,EAAE,uBAAuB;QAC7B2C,SAAS,EAAE,GAAG;QACdC,MAAM,EAAE;OACT;MACDC,YAAY,EAAE;KACf;IACDC,mBAAmB,EAAE;MACnBC,cAAc,EAAE,IAAI;MAAE;MACtBC,UAAU,EAAE,IAAI;MAChBC,iBAAiB,EAAE,IAAI;MACvBC,mBAAmB,EAAE;;GAExB;EAAC;EAAAnE,cAAA,GAAAmB,CAAA;EAEFyB,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;EAE9C;EACA,MAAMmJ,OAAO;EAAA;EAAA,CAAAhM,cAAA,GAAAmB,CAAA,QAAG,CACd;IAAE8K,MAAM,EAAEJ,cAAc;IAAEhL,IAAI,EAAE;EAAiB,CAAE,EACnD;IAAEoL,MAAM,EAAEH,eAAe;IAAEjL,IAAI,EAAE;EAAmB,CAAE,EACtD;IAAEoL,MAAM,EAAEF,eAAe;IAAElL,IAAI,EAAE;EAAmB,CAAE,CACvD;EAAC;EAAAb,cAAA,GAAAmB,CAAA;EAEF,KAAK,MAAM+K,MAAM,IAAIF,OAAO,EAAE;IAAA;IAAAhM,cAAA,GAAAmB,CAAA;IAC5ByB,OAAO,CAACC,GAAG,CAAC,OAAOqJ,MAAM,CAACrL,IAAI,MAAM,CAAC;IAErC;IACA,MAAMsL,GAAG;IAAA;IAAA,CAAAnM,cAAA,GAAAmB,CAAA,QAAG+K,MAAM,CAACD,MAAM,CAAC9I,cAAc,GAAG,GAAG,EAAC,CAAC;IAChD,MAAMiJ,UAAU;IAAA;IAAA,CAAApM,cAAA,GAAAmB,CAAA,SAAGkL,IAAI,CAACC,GAAG,CAAC,EAAE,EAAE,GAAG,GAAGJ,MAAM,CAACD,MAAM,CAAC9I,cAAc,GAAG,CAAC,CAAC;IAAC;IAAAnD,cAAA,GAAAmB,CAAA;IAExEyB,OAAO,CAACC,GAAG,CAAC,kBAAkBsJ,GAAG,CAACzG,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC;IAAC;IAAA1F,cAAA,GAAAmB,CAAA;IACtDyB,OAAO,CAACC,GAAG,CAAC,yBAAyBuJ,UAAU,CAAC1G,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IAAC;IAAA1F,cAAA,GAAAmB,CAAA;IAC/DyB,OAAO,CAACC,GAAG,CAAC;IAAoB;IAAA,CAAA7C,cAAA,GAAAqB,CAAA,UAAA6K,MAAM,CAACD,MAAM,CAAClI,mBAAmB,EAAEC,cAAc;IAAA;IAAA,CAAAhE,cAAA,GAAAqB,CAAA,UAAI,KAAK,GAAE,CAAC;IAAC;IAAArB,cAAA,GAAAmB,CAAA;IAC9FyB,OAAO,CAACC,GAAG,CAAC;IAAkB;IAAA,CAAA7C,cAAA,GAAAqB,CAAA,UAAA6K,MAAM,CAACD,MAAM,CAACzI,iBAAiB,EAAEM,YAAY;IAAA;IAAA,CAAA9D,cAAA,GAAAqB,CAAA,UAAI,KAAK,GAAE,CAAC;IAAC;IAAArB,cAAA,GAAAmB,CAAA;IACxFyB,OAAO,CAACC,GAAG,CAAC,EAAE,CAAC;EACjB;EAAC;EAAA7C,cAAA,GAAAmB,CAAA;EAEDyB,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;EAAC;EAAA7C,cAAA,GAAAmB,CAAA;EAC5CyB,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;EAAC;EAAA7C,cAAA,GAAAmB,CAAA;EACtEyB,OAAO,CAACC,GAAG,CAAC,+DAA+D,CAAC;EAAC;EAAA7C,cAAA,GAAAmB,CAAA;EAC7EyB,OAAO,CAACC,GAAG,CAAC,+DAA+D,CAAC;EAAC;EAAA7C,cAAA,GAAAmB,CAAA;EAC7EyB,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;AAC/C;AAEA;AAAA;AAAA7C,cAAA,GAAAmB,CAAA;AACaa,OAAA,CAAAuK,sBAAsB,GAAG;EACpCtK,6BAA6B;EAC7BC,kCAAkC;EAClCC;CACD", "ignoreList": []}