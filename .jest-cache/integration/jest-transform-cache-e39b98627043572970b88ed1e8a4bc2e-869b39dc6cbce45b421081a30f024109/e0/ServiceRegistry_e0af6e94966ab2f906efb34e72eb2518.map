{"version": 3, "names": ["cov_lld5u1oeb", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "name", "decl", "loc", "branchMap", "type", "locations", "undefined", "s", "f", "b", "inputSourceMap", "file", "mappings", "names", "sources", "sourcesContent", "version", "_coverageSchema", "coverage", "actualCoverage", "CircuitBreaker", "constructor", "threshold", "timeout", "monitoringPeriod", "state", "failureCount", "lastFailureTime", "successCount", "nextAttempt", "execute", "operation", "Date", "now", "Error", "result", "onSuccess", "error", "onFailure", "getState", "getMetrics", "exports", "ServiceRegistry", "config", "services", "Map", "circuitBreakers", "metrics", "totalRequests", "successfulRequests", "failedRequests", "averageResponseTime", "circuitBreakerTrips", "healthCheckInterval", "heartbeatTimeout", "maxRetries", "circuitBreakerThreshold", "loadBalancingStrategy", "enableMetrics", "enableTracing", "startHealthChecking", "registerCoreServices", "registerService", "service", "endpoint", "registeredAt", "lastHeartbeat", "set", "id", "console", "log", "unregisterService", "serviceId", "delete", "discoverServices", "serviceName", "tags", "allServices", "Array", "from", "values", "filtered", "filter", "length", "every", "tag", "metadata", "includes", "health", "status", "getService", "get", "selectService", "availableServices", "roundRobinSelection", "leastConnectionsSelection", "weightedSelection", "randomSelection", "index", "reduce", "best", "current", "responseTime", "weights", "map", "weight", "totalWeight", "sum", "random", "Math", "i", "floor", "callService", "circuitBreaker", "startTime", "updateResponseTime", "updateServiceHealth", "setInterval", "performHealthCheck", "response", "pingService", "duration", "healthCheck", "message", "timestamp", "updateServiceHealthStatus", "url", "protocol", "fetch", "method", "ok", "success", "<PERSON><PERSON><PERSON><PERSON>", "errorRate", "max", "min", "checks", "slice", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "check", "port", "uptime", "environment", "capabilities", "dependencies", "resources", "cpu", "memory", "storage", "registeredServices", "size", "healthyServices", "entries", "cb", "getAllServices", "getServiceHealth", "destroy", "clearInterval", "APIGateway", "serviceRegistry", "routes", "rateLimiters", "averageLatency", "rateLimitedRequests", "timeouts", "setupDefaultRoutes", "addRoute", "route", "key", "removeRoute", "routeRequest", "body", "headers", "findRoute", "rateLimit", "checkRateLimit", "executeRequest", "updateLatency", "exactKey", "exactMatch", "startsWith", "routePath", "substring", "matchPath", "requestPath", "routeSegments", "split", "requestSegments", "routeSegment", "requestSegment", "limiter", "resetTime", "count", "window", "requests", "targetPath", "requestOptions", "JSON", "stringify", "controller", "AbortController", "timeoutId", "setTimeout", "abort", "signal", "clearTimeout", "statusText", "json", "latency", "authentication", "getRoutes", "getServiceRegistry"], "sources": ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\frontend\\lib\\services\\ServiceRegistry.ts"], "sourcesContent": ["/**\r\n * Service Registry for SizeWise Suite\r\n * \r\n * Implements microservices preparation with:\r\n * - Service discovery and registration\r\n * - Health monitoring and status tracking\r\n * - Load balancing and failover\r\n * - Circuit breaker patterns\r\n * - API gateway preparation\r\n * - Service mesh readiness\r\n */\r\n\r\n// =============================================================================\r\n// Service Registry Types and Interfaces\r\n// =============================================================================\r\n\r\nexport interface ServiceEndpoint {\r\n  id: string;\r\n  name: string;\r\n  version: string;\r\n  url: string;\r\n  protocol: 'http' | 'https' | 'ws' | 'wss';\r\n  port?: number;\r\n  path?: string;\r\n  health: ServiceHealth;\r\n  metadata: ServiceMetadata;\r\n  lastHeartbeat: Date;\r\n  registeredAt: Date;\r\n}\r\n\r\nexport interface ServiceHealth {\r\n  status: 'healthy' | 'degraded' | 'unhealthy' | 'unknown';\r\n  lastCheck: Date;\r\n  responseTime: number;\r\n  errorRate: number;\r\n  uptime: number;\r\n  checks: HealthCheck[];\r\n}\r\n\r\nexport interface HealthCheck {\r\n  name: string;\r\n  status: 'pass' | 'fail' | 'warn';\r\n  message?: string;\r\n  timestamp: Date;\r\n  duration: number;\r\n}\r\n\r\nexport interface ServiceMetadata {\r\n  tags: string[];\r\n  environment: 'development' | 'staging' | 'production';\r\n  region?: string;\r\n  datacenter?: string;\r\n  capabilities: string[];\r\n  dependencies: string[];\r\n  resources: {\r\n    cpu: number;\r\n    memory: number;\r\n    storage: number;\r\n  };\r\n}\r\n\r\nexport interface ServiceRegistryConfig {\r\n  healthCheckInterval: number;\r\n  heartbeatTimeout: number;\r\n  maxRetries: number;\r\n  circuitBreakerThreshold: number;\r\n  loadBalancingStrategy: 'round-robin' | 'least-connections' | 'weighted' | 'random';\r\n  enableMetrics: boolean;\r\n  enableTracing: boolean;\r\n}\r\n\r\n// =============================================================================\r\n// Circuit Breaker Implementation\r\n// =============================================================================\r\n\r\nexport class CircuitBreaker {\r\n  private state: 'closed' | 'open' | 'half-open' = 'closed';\r\n  private failureCount = 0;\r\n  private lastFailureTime = 0;\r\n  private successCount = 0;\r\n  private nextAttempt = 0;\r\n\r\n  constructor(\r\n    private threshold: number = 5,\r\n    private timeout: number = 60000, // 1 minute\r\n    private monitoringPeriod: number = 10000 // 10 seconds\r\n  ) {}\r\n\r\n  async execute<T>(operation: () => Promise<T>): Promise<T> {\r\n    if (this.state === 'open') {\r\n      if (Date.now() < this.nextAttempt) {\r\n        throw new Error('Circuit breaker is OPEN');\r\n      }\r\n      this.state = 'half-open';\r\n    }\r\n\r\n    try {\r\n      const result = await operation();\r\n      this.onSuccess();\r\n      return result;\r\n    } catch (error) {\r\n      this.onFailure();\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  private onSuccess(): void {\r\n    this.failureCount = 0;\r\n    if (this.state === 'half-open') {\r\n      this.state = 'closed';\r\n    }\r\n    this.successCount++;\r\n  }\r\n\r\n  private onFailure(): void {\r\n    this.failureCount++;\r\n    this.lastFailureTime = Date.now();\r\n\r\n    if (this.failureCount >= this.threshold) {\r\n      this.state = 'open';\r\n      this.nextAttempt = Date.now() + this.timeout;\r\n    }\r\n  }\r\n\r\n  getState(): string {\r\n    return this.state;\r\n  }\r\n\r\n  getMetrics() {\r\n    return {\r\n      state: this.state,\r\n      failureCount: this.failureCount,\r\n      successCount: this.successCount,\r\n      lastFailureTime: this.lastFailureTime\r\n    };\r\n  }\r\n}\r\n\r\n// =============================================================================\r\n// Service Registry Implementation\r\n// =============================================================================\r\n\r\nexport class ServiceRegistry {\r\n  private services = new Map<string, ServiceEndpoint>();\r\n  private circuitBreakers = new Map<string, CircuitBreaker>();\r\n  private healthCheckInterval?: NodeJS.Timeout;\r\n  private config: ServiceRegistryConfig;\r\n  private metrics = {\r\n    totalRequests: 0,\r\n    successfulRequests: 0,\r\n    failedRequests: 0,\r\n    averageResponseTime: 0,\r\n    circuitBreakerTrips: 0\r\n  };\r\n\r\n  constructor(config: Partial<ServiceRegistryConfig> = {}) {\r\n    this.config = {\r\n      healthCheckInterval: 30000, // 30 seconds\r\n      heartbeatTimeout: 60000, // 1 minute\r\n      maxRetries: 3,\r\n      circuitBreakerThreshold: 5,\r\n      loadBalancingStrategy: 'round-robin',\r\n      enableMetrics: true,\r\n      enableTracing: false,\r\n      ...config\r\n    };\r\n\r\n    this.startHealthChecking();\r\n    this.registerCoreServices();\r\n  }\r\n\r\n  // =============================================================================\r\n  // Service Registration and Discovery\r\n  // =============================================================================\r\n\r\n  registerService(service: Omit<ServiceEndpoint, 'registeredAt' | 'lastHeartbeat'>): void {\r\n    const endpoint: ServiceEndpoint = {\r\n      ...service,\r\n      registeredAt: new Date(),\r\n      lastHeartbeat: new Date()\r\n    };\r\n\r\n    this.services.set(service.id, endpoint);\r\n    this.circuitBreakers.set(service.id, new CircuitBreaker(\r\n      this.config.circuitBreakerThreshold,\r\n      60000, // 1 minute timeout\r\n      10000  // 10 second monitoring\r\n    ));\r\n\r\n    console.log(`Service registered: ${service.name} (${service.id})`);\r\n  }\r\n\r\n  unregisterService(serviceId: string): void {\r\n    this.services.delete(serviceId);\r\n    this.circuitBreakers.delete(serviceId);\r\n    console.log(`Service unregistered: ${serviceId}`);\r\n  }\r\n\r\n  discoverServices(serviceName?: string, tags?: string[]): ServiceEndpoint[] {\r\n    const allServices = Array.from(this.services.values());\r\n    \r\n    let filtered = allServices;\r\n    \r\n    if (serviceName) {\r\n      filtered = filtered.filter(service => service.name === serviceName);\r\n    }\r\n    \r\n    if (tags && tags.length > 0) {\r\n      filtered = filtered.filter(service => \r\n        tags.every(tag => service.metadata.tags.includes(tag))\r\n      );\r\n    }\r\n    \r\n    // Filter out unhealthy services\r\n    return filtered.filter(service => \r\n      service.health.status === 'healthy' || service.health.status === 'degraded'\r\n    );\r\n  }\r\n\r\n  getService(serviceId: string): ServiceEndpoint | undefined {\r\n    return this.services.get(serviceId);\r\n  }\r\n\r\n  // =============================================================================\r\n  // Load Balancing and Service Selection\r\n  // =============================================================================\r\n\r\n  selectService(serviceName: string, tags?: string[]): ServiceEndpoint | null {\r\n    const availableServices = this.discoverServices(serviceName, tags);\r\n    \r\n    if (availableServices.length === 0) {\r\n      return null;\r\n    }\r\n\r\n    switch (this.config.loadBalancingStrategy) {\r\n      case 'round-robin':\r\n        return this.roundRobinSelection(availableServices);\r\n      case 'least-connections':\r\n        return this.leastConnectionsSelection(availableServices);\r\n      case 'weighted':\r\n        return this.weightedSelection(availableServices);\r\n      case 'random':\r\n        return this.randomSelection(availableServices);\r\n      default:\r\n        return availableServices[0];\r\n    }\r\n  }\r\n\r\n  private roundRobinSelection(services: ServiceEndpoint[]): ServiceEndpoint {\r\n    // Simple round-robin implementation\r\n    const index = this.metrics.totalRequests % services.length;\r\n    return services[index];\r\n  }\r\n\r\n  private leastConnectionsSelection(services: ServiceEndpoint[]): ServiceEndpoint {\r\n    // For now, select based on response time as a proxy for connections\r\n    return services.reduce((best, current) => \r\n      current.health.responseTime < best.health.responseTime ? current : best\r\n    );\r\n  }\r\n\r\n  private weightedSelection(services: ServiceEndpoint[]): ServiceEndpoint {\r\n    // Weight based on health status and response time\r\n    const weights = services.map(service => {\r\n      let weight = 1;\r\n      if (service.health.status === 'healthy') weight *= 2;\r\n      if (service.health.responseTime < 100) weight *= 1.5;\r\n      return weight;\r\n    });\r\n\r\n    const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);\r\n    let random = Math.random() * totalWeight;\r\n\r\n    for (let i = 0; i < services.length; i++) {\r\n      random -= weights[i];\r\n      if (random <= 0) {\r\n        return services[i];\r\n      }\r\n    }\r\n\r\n    return services[0];\r\n  }\r\n\r\n  private randomSelection(services: ServiceEndpoint[]): ServiceEndpoint {\r\n    const index = Math.floor(Math.random() * services.length);\r\n    return services[index];\r\n  }\r\n\r\n  // =============================================================================\r\n  // Circuit Breaker Integration\r\n  // =============================================================================\r\n\r\n  async callService<T>(\r\n    serviceId: string,\r\n    operation: (endpoint: ServiceEndpoint) => Promise<T>\r\n  ): Promise<T> {\r\n    const service = this.services.get(serviceId);\r\n    if (!service) {\r\n      throw new Error(`Service not found: ${serviceId}`);\r\n    }\r\n\r\n    const circuitBreaker = this.circuitBreakers.get(serviceId);\r\n    if (!circuitBreaker) {\r\n      throw new Error(`Circuit breaker not found for service: ${serviceId}`);\r\n    }\r\n\r\n    this.metrics.totalRequests++;\r\n    const startTime = Date.now();\r\n\r\n    try {\r\n      const result = await circuitBreaker.execute(() => operation(service));\r\n      \r\n      this.metrics.successfulRequests++;\r\n      this.updateResponseTime(Date.now() - startTime);\r\n      this.updateServiceHealth(serviceId, true, Date.now() - startTime);\r\n      \r\n      return result;\r\n    } catch (error) {\r\n      this.metrics.failedRequests++;\r\n      this.updateServiceHealth(serviceId, false, Date.now() - startTime);\r\n      \r\n      if (circuitBreaker.getState() === 'open') {\r\n        this.metrics.circuitBreakerTrips++;\r\n      }\r\n      \r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // =============================================================================\r\n  // Health Monitoring\r\n  // =============================================================================\r\n\r\n  private startHealthChecking(): void {\r\n    this.healthCheckInterval = setInterval(async () => {\r\n      for (const [serviceId, service] of this.services) {\r\n        await this.performHealthCheck(serviceId, service);\r\n      }\r\n    }, this.config.healthCheckInterval);\r\n  }\r\n\r\n  private async performHealthCheck(serviceId: string, service: ServiceEndpoint): Promise<void> {\r\n    const startTime = Date.now();\r\n    \r\n    try {\r\n      // Perform actual health check (simplified for demo)\r\n      const response = await this.pingService(service);\r\n      const duration = Date.now() - startTime;\r\n      \r\n      const healthCheck: HealthCheck = {\r\n        name: 'ping',\r\n        status: response ? 'pass' : 'fail',\r\n        message: response ? 'Service responding' : 'Service not responding',\r\n        timestamp: new Date(),\r\n        duration\r\n      };\r\n\r\n      this.updateServiceHealthStatus(serviceId, healthCheck);\r\n      \r\n    } catch (error) {\r\n      const healthCheck: HealthCheck = {\r\n        name: 'ping',\r\n        status: 'fail',\r\n        message: error instanceof Error ? error.message : 'Unknown error',\r\n        timestamp: new Date(),\r\n        duration: Date.now() - startTime\r\n      };\r\n\r\n      this.updateServiceHealthStatus(serviceId, healthCheck);\r\n    }\r\n  }\r\n\r\n  private async pingService(service: ServiceEndpoint): Promise<boolean> {\r\n    // Simplified ping implementation\r\n    // In a real implementation, this would make an actual HTTP request\r\n    try {\r\n      const url = `${service.protocol}://${service.url}${service.path || '/health'}`;\r\n      const response = await fetch(url, { \r\n        method: 'GET',\r\n        timeout: 5000 \r\n      } as any);\r\n      return response.ok;\r\n    } catch {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  private updateServiceHealth(serviceId: string, success: boolean, responseTime: number): void {\r\n    const service = this.services.get(serviceId);\r\n    if (!service) return;\r\n\r\n    service.health.responseTime = responseTime;\r\n    service.health.lastCheck = new Date();\r\n    service.lastHeartbeat = new Date();\r\n\r\n    if (success) {\r\n      service.health.errorRate = Math.max(0, service.health.errorRate - 0.1);\r\n    } else {\r\n      service.health.errorRate = Math.min(1, service.health.errorRate + 0.1);\r\n    }\r\n\r\n    // Update health status based on error rate\r\n    if (service.health.errorRate < 0.1) {\r\n      service.health.status = 'healthy';\r\n    } else if (service.health.errorRate < 0.5) {\r\n      service.health.status = 'degraded';\r\n    } else {\r\n      service.health.status = 'unhealthy';\r\n    }\r\n  }\r\n\r\n  private updateServiceHealthStatus(serviceId: string, healthCheck: HealthCheck): void {\r\n    const service = this.services.get(serviceId);\r\n    if (!service) return;\r\n\r\n    service.health.checks = [healthCheck, ...service.health.checks.slice(0, 9)]; // Keep last 10 checks\r\n    service.health.lastCheck = healthCheck.timestamp;\r\n    service.lastHeartbeat = new Date();\r\n\r\n    // Update overall health status\r\n    const recentChecks = service.health.checks.slice(0, 5);\r\n    const failedChecks = recentChecks.filter(check => check.status === 'fail').length;\r\n    \r\n    if (failedChecks === 0) {\r\n      service.health.status = 'healthy';\r\n    } else if (failedChecks < 3) {\r\n      service.health.status = 'degraded';\r\n    } else {\r\n      service.health.status = 'unhealthy';\r\n    }\r\n  }\r\n\r\n  private updateResponseTime(responseTime: number): void {\r\n    this.metrics.averageResponseTime = \r\n      (this.metrics.averageResponseTime + responseTime) / 2;\r\n  }\r\n\r\n  // =============================================================================\r\n  // Core Services Registration\r\n  // =============================================================================\r\n\r\n  private registerCoreServices(): void {\r\n    // Register core SizeWise services for microservices preparation\r\n    \r\n    // HVAC Calculation Service\r\n    this.registerService({\r\n      id: 'hvac-calc-service',\r\n      name: 'hvac-calculation',\r\n      version: '1.0.0',\r\n      url: 'localhost',\r\n      protocol: 'http',\r\n      port: 3001,\r\n      path: '/api/calculations',\r\n      health: {\r\n        status: 'healthy',\r\n        lastCheck: new Date(),\r\n        responseTime: 50,\r\n        errorRate: 0,\r\n        uptime: 100,\r\n        checks: []\r\n      },\r\n      metadata: {\r\n        tags: ['calculation', 'hvac', 'core'],\r\n        environment: 'development',\r\n        capabilities: ['air-duct-sizing', 'load-calculation', 'energy-analysis'],\r\n        dependencies: ['database-service'],\r\n        resources: {\r\n          cpu: 0.5,\r\n          memory: 512,\r\n          storage: 100\r\n        }\r\n      }\r\n    });\r\n\r\n    // Project Management Service\r\n    this.registerService({\r\n      id: 'project-mgmt-service',\r\n      name: 'project-management',\r\n      version: '1.0.0',\r\n      url: 'localhost',\r\n      protocol: 'http',\r\n      port: 3002,\r\n      path: '/api/projects',\r\n      health: {\r\n        status: 'healthy',\r\n        lastCheck: new Date(),\r\n        responseTime: 30,\r\n        errorRate: 0,\r\n        uptime: 100,\r\n        checks: []\r\n      },\r\n      metadata: {\r\n        tags: ['project', 'management', 'core'],\r\n        environment: 'development',\r\n        capabilities: ['project-crud', 'collaboration', 'version-control'],\r\n        dependencies: ['database-service', 'auth-service'],\r\n        resources: {\r\n          cpu: 0.3,\r\n          memory: 256,\r\n          storage: 500\r\n        }\r\n      }\r\n    });\r\n\r\n    // Database Service\r\n    this.registerService({\r\n      id: 'database-service',\r\n      name: 'database',\r\n      version: '1.0.0',\r\n      url: 'localhost',\r\n      protocol: 'http',\r\n      port: 3003,\r\n      path: '/api/data',\r\n      health: {\r\n        status: 'healthy',\r\n        lastCheck: new Date(),\r\n        responseTime: 20,\r\n        errorRate: 0,\r\n        uptime: 100,\r\n        checks: []\r\n      },\r\n      metadata: {\r\n        tags: ['database', 'storage', 'infrastructure'],\r\n        environment: 'development',\r\n        capabilities: ['postgresql', 'mongodb', 'caching'],\r\n        dependencies: [],\r\n        resources: {\r\n          cpu: 1.0,\r\n          memory: 1024,\r\n          storage: 2000\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  // =============================================================================\r\n  // Public API\r\n  // =============================================================================\r\n\r\n  getMetrics() {\r\n    return {\r\n      ...this.metrics,\r\n      registeredServices: this.services.size,\r\n      healthyServices: Array.from(this.services.values())\r\n        .filter(s => s.health.status === 'healthy').length,\r\n      circuitBreakers: Array.from(this.circuitBreakers.entries())\r\n        .map(([id, cb]) => ({ serviceId: id, ...cb.getMetrics() }))\r\n    };\r\n  }\r\n\r\n  getAllServices(): ServiceEndpoint[] {\r\n    return Array.from(this.services.values());\r\n  }\r\n\r\n  getServiceHealth(serviceId: string): ServiceHealth | undefined {\r\n    return this.services.get(serviceId)?.health;\r\n  }\r\n\r\n  destroy(): void {\r\n    if (this.healthCheckInterval) {\r\n      clearInterval(this.healthCheckInterval);\r\n    }\r\n  }\r\n}\r\n\r\n// =============================================================================\r\n// API Gateway for Microservices\r\n// =============================================================================\r\n\r\nexport interface RouteConfig {\r\n  path: string;\r\n  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';\r\n  serviceName: string;\r\n  targetPath?: string;\r\n  middleware?: string[];\r\n  rateLimit?: {\r\n    requests: number;\r\n    window: number; // in milliseconds\r\n  };\r\n  authentication?: boolean;\r\n  timeout?: number;\r\n}\r\n\r\nexport interface GatewayMetrics {\r\n  totalRequests: number;\r\n  successfulRequests: number;\r\n  failedRequests: number;\r\n  averageLatency: number;\r\n  rateLimitedRequests: number;\r\n  timeouts: number;\r\n}\r\n\r\nexport class APIGateway {\r\n  private routes = new Map<string, RouteConfig>();\r\n  private serviceRegistry: ServiceRegistry;\r\n  private rateLimiters = new Map<string, { count: number; resetTime: number }>();\r\n  private metrics: GatewayMetrics = {\r\n    totalRequests: 0,\r\n    successfulRequests: 0,\r\n    failedRequests: 0,\r\n    averageLatency: 0,\r\n    rateLimitedRequests: 0,\r\n    timeouts: 0\r\n  };\r\n\r\n  constructor(serviceRegistry: ServiceRegistry) {\r\n    this.serviceRegistry = serviceRegistry;\r\n    this.setupDefaultRoutes();\r\n  }\r\n\r\n  // =============================================================================\r\n  // Route Management\r\n  // =============================================================================\r\n\r\n  addRoute(route: RouteConfig): void {\r\n    const key = `${route.method}:${route.path}`;\r\n    this.routes.set(key, route);\r\n  }\r\n\r\n  removeRoute(method: string, path: string): void {\r\n    const key = `${method}:${path}`;\r\n    this.routes.delete(key);\r\n  }\r\n\r\n  // =============================================================================\r\n  // Request Routing and Processing\r\n  // =============================================================================\r\n\r\n  async routeRequest(\r\n    method: string,\r\n    path: string,\r\n    body?: any,\r\n    headers?: Record<string, string>\r\n  ): Promise<any> {\r\n    const startTime = Date.now();\r\n    this.metrics.totalRequests++;\r\n\r\n    try {\r\n      // Find matching route\r\n      const route = this.findRoute(method, path);\r\n      if (!route) {\r\n        throw new Error(`Route not found: ${method} ${path}`);\r\n      }\r\n\r\n      // Apply rate limiting\r\n      if (route.rateLimit && !this.checkRateLimit(route)) {\r\n        this.metrics.rateLimitedRequests++;\r\n        throw new Error('Rate limit exceeded');\r\n      }\r\n\r\n      // Select service instance\r\n      const service = this.serviceRegistry.selectService(route.serviceName);\r\n      if (!service) {\r\n        throw new Error(`No healthy service found: ${route.serviceName}`);\r\n      }\r\n\r\n      // Execute request with circuit breaker\r\n      const result = await this.serviceRegistry.callService(\r\n        service.id,\r\n        async (endpoint) => {\r\n          return await this.executeRequest(endpoint, route, body, headers);\r\n        }\r\n      );\r\n\r\n      this.metrics.successfulRequests++;\r\n      this.updateLatency(Date.now() - startTime);\r\n\r\n      return result;\r\n\r\n    } catch (error) {\r\n      this.metrics.failedRequests++;\r\n      this.updateLatency(Date.now() - startTime);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  private findRoute(method: string, path: string): RouteConfig | undefined {\r\n    const exactKey = `${method}:${path}`;\r\n    const exactMatch = this.routes.get(exactKey);\r\n\r\n    if (exactMatch) {\r\n      return exactMatch;\r\n    }\r\n\r\n    // Pattern matching for dynamic routes\r\n    for (const [key, route] of this.routes) {\r\n      if (key.startsWith(`${method}:`)) {\r\n        const routePath = key.substring(method.length + 1);\r\n        if (this.matchPath(routePath, path)) {\r\n          return route;\r\n        }\r\n      }\r\n    }\r\n\r\n    return undefined;\r\n  }\r\n\r\n  private matchPath(routePath: string, requestPath: string): boolean {\r\n    // Simple pattern matching - can be enhanced with more sophisticated routing\r\n    const routeSegments = routePath.split('/');\r\n    const requestSegments = requestPath.split('/');\r\n\r\n    if (routeSegments.length !== requestSegments.length) {\r\n      return false;\r\n    }\r\n\r\n    for (let i = 0; i < routeSegments.length; i++) {\r\n      const routeSegment = routeSegments[i];\r\n      const requestSegment = requestSegments[i];\r\n\r\n      if (routeSegment.startsWith(':')) {\r\n        // Dynamic segment - matches any value\r\n        continue;\r\n      }\r\n\r\n      if (routeSegment !== requestSegment) {\r\n        return false;\r\n      }\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  private checkRateLimit(route: RouteConfig): boolean {\r\n    if (!route.rateLimit) return true;\r\n\r\n    const key = `${route.method}:${route.path}`;\r\n    const now = Date.now();\r\n    const limiter = this.rateLimiters.get(key);\r\n\r\n    if (!limiter || now > limiter.resetTime) {\r\n      this.rateLimiters.set(key, {\r\n        count: 1,\r\n        resetTime: now + route.rateLimit.window\r\n      });\r\n      return true;\r\n    }\r\n\r\n    if (limiter.count >= route.rateLimit.requests) {\r\n      return false;\r\n    }\r\n\r\n    limiter.count++;\r\n    return true;\r\n  }\r\n\r\n  private async executeRequest(\r\n    endpoint: ServiceEndpoint,\r\n    route: RouteConfig,\r\n    body?: any,\r\n    headers?: Record<string, string>\r\n  ): Promise<any> {\r\n    const targetPath = route.targetPath || route.path;\r\n    const url = `${endpoint.protocol}://${endpoint.url}:${endpoint.port}${targetPath}`;\r\n\r\n    const requestOptions: RequestInit = {\r\n      method: route.method,\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        ...headers\r\n      }\r\n    };\r\n\r\n    if (body && route.method !== 'GET') {\r\n      requestOptions.body = JSON.stringify(body);\r\n    }\r\n\r\n    // Add timeout\r\n    const timeout = route.timeout || 30000; // 30 seconds default\r\n    const controller = new AbortController();\r\n    const timeoutId = setTimeout(() => {\r\n      controller.abort();\r\n      this.metrics.timeouts++;\r\n    }, timeout);\r\n\r\n    requestOptions.signal = controller.signal;\r\n\r\n    try {\r\n      const response = await fetch(url, requestOptions);\r\n      clearTimeout(timeoutId);\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`Service error: ${response.status} ${response.statusText}`);\r\n      }\r\n\r\n      return await response.json();\r\n    } catch (error) {\r\n      clearTimeout(timeoutId);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  private updateLatency(latency: number): void {\r\n    this.metrics.averageLatency =\r\n      (this.metrics.averageLatency + latency) / 2;\r\n  }\r\n\r\n  // =============================================================================\r\n  // Default Routes Setup\r\n  // =============================================================================\r\n\r\n  private setupDefaultRoutes(): void {\r\n    // HVAC Calculation routes\r\n    this.addRoute({\r\n      path: '/api/calculations/air-duct',\r\n      method: 'POST',\r\n      serviceName: 'hvac-calculation',\r\n      targetPath: '/api/calculations/air-duct',\r\n      authentication: true,\r\n      timeout: 10000,\r\n      rateLimit: { requests: 100, window: 60000 } // 100 requests per minute\r\n    });\r\n\r\n    this.addRoute({\r\n      path: '/api/calculations/load',\r\n      method: 'POST',\r\n      serviceName: 'hvac-calculation',\r\n      targetPath: '/api/calculations/load',\r\n      authentication: true,\r\n      timeout: 15000,\r\n      rateLimit: { requests: 50, window: 60000 }\r\n    });\r\n\r\n    // Project Management routes\r\n    this.addRoute({\r\n      path: '/api/projects',\r\n      method: 'GET',\r\n      serviceName: 'project-management',\r\n      authentication: true,\r\n      rateLimit: { requests: 200, window: 60000 }\r\n    });\r\n\r\n    this.addRoute({\r\n      path: '/api/projects',\r\n      method: 'POST',\r\n      serviceName: 'project-management',\r\n      authentication: true,\r\n      rateLimit: { requests: 20, window: 60000 }\r\n    });\r\n\r\n    this.addRoute({\r\n      path: '/api/projects/:id',\r\n      method: 'GET',\r\n      serviceName: 'project-management',\r\n      authentication: true,\r\n      rateLimit: { requests: 500, window: 60000 }\r\n    });\r\n\r\n    this.addRoute({\r\n      path: '/api/projects/:id',\r\n      method: 'PUT',\r\n      serviceName: 'project-management',\r\n      authentication: true,\r\n      rateLimit: { requests: 50, window: 60000 }\r\n    });\r\n\r\n    // Database routes\r\n    this.addRoute({\r\n      path: '/api/data/sync',\r\n      method: 'POST',\r\n      serviceName: 'database',\r\n      authentication: true,\r\n      timeout: 30000,\r\n      rateLimit: { requests: 10, window: 60000 }\r\n    });\r\n  }\r\n\r\n  // =============================================================================\r\n  // Public API\r\n  // =============================================================================\r\n\r\n  getMetrics(): GatewayMetrics {\r\n    return { ...this.metrics };\r\n  }\r\n\r\n  getRoutes(): RouteConfig[] {\r\n    return Array.from(this.routes.values());\r\n  }\r\n\r\n  getServiceRegistry(): ServiceRegistry {\r\n    return this.serviceRegistry;\r\n  }\r\n}\r\n"], "mappings": ";;AAAA;;;;;;;;;;;AAAA;AAAA,SAAAA,cAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;MAAA;QAAAC,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAO,SAAA;MAAA;QAAAD,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;IAAA;IAAAW,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,cAAA;MAAAC,IAAA;MAAAC,QAAA;MAAAC,KAAA;MAAAC,OAAA;MAAAC,cAAA;MAAAC,OAAA;IAAA;IAAAC,eAAA;IAAA5B,IAAA;EAAA;EAAA,IAAA6B,QAAA,GAAA5B,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAA0B,QAAA,CAAA9B,IAAA,KAAA8B,QAAA,CAAA9B,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAA6B,QAAA,CAAA9B,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAA0B,cAAA,GAAAD,QAAA,CAAA9B,IAAA;EAAA;IAwEA;IAAAD,aAAA,YAAAA,CAAA;MAAA,OAAAgC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAhC,aAAA;AAAAA,aAAA,GAAAoB,CAAA;;;;;;;AADA;AACA;AACA;AAEA,MAAaa,cAAc;EAOzBC,YACUC,SAAA;EAAA;EAAA,CAAAnC,aAAA,GAAAsB,CAAA,UAAoB,CAAC,GACrBc,OAAA;EAAA;EAAA,CAAApC,aAAA,GAAAsB,CAAA,UAAkB,KAAK;EAAE;EACzBe,gBAAA;EAAA;EAAA,CAAArC,aAAA,GAAAsB,CAAA,UAA2B,KAAK,EAAC;EAAA,E;;;;IAFjC,KAAAa,SAAS,GAATA,SAAS;IAAY;IAAAnC,aAAA,GAAAoB,CAAA;IACrB,KAAAgB,OAAO,GAAPA,OAAO;IAAgB;IAAApC,aAAA,GAAAoB,CAAA;IACvB,KAAAiB,gBAAgB,GAAhBA,gBAAgB;IAAgB;IAAArC,aAAA,GAAAoB,CAAA;IATlC,KAAAkB,KAAK,GAAoC,QAAQ;IAAC;IAAAtC,aAAA,GAAAoB,CAAA;IAClD,KAAAmB,YAAY,GAAG,CAAC;IAAC;IAAAvC,aAAA,GAAAoB,CAAA;IACjB,KAAAoB,eAAe,GAAG,CAAC;IAAC;IAAAxC,aAAA,GAAAoB,CAAA;IACpB,KAAAqB,YAAY,GAAG,CAAC;IAAC;IAAAzC,aAAA,GAAAoB,CAAA;IACjB,KAAAsB,WAAW,GAAG,CAAC;EAMpB;EAEH,MAAMC,OAAOA,CAAIC,SAA2B;IAAA;IAAA5C,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAC1C,IAAI,IAAI,CAACkB,KAAK,KAAK,MAAM,EAAE;MAAA;MAAAtC,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACzB,IAAIyB,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,CAACJ,WAAW,EAAE;QAAA;QAAA1C,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACjC,MAAM,IAAI2B,KAAK,CAAC,yBAAyB,CAAC;MAC5C,CAAC;MAAA;MAAA;QAAA/C,aAAA,GAAAsB,CAAA;MAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACD,IAAI,CAACkB,KAAK,GAAG,WAAW;IAC1B,CAAC;IAAA;IAAA;MAAAtC,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAED,IAAI;MACF,MAAM4B,MAAM;MAAA;MAAA,CAAAhD,aAAA,GAAAoB,CAAA,QAAG,MAAMwB,SAAS,EAAE;MAAC;MAAA5C,aAAA,GAAAoB,CAAA;MACjC,IAAI,CAAC6B,SAAS,EAAE;MAAC;MAAAjD,aAAA,GAAAoB,CAAA;MACjB,OAAO4B,MAAM;IACf,CAAC,CAAC,OAAOE,KAAK,EAAE;MAAA;MAAAlD,aAAA,GAAAoB,CAAA;MACd,IAAI,CAAC+B,SAAS,EAAE;MAAC;MAAAnD,aAAA,GAAAoB,CAAA;MACjB,MAAM8B,KAAK;IACb;EACF;EAEQD,SAASA,CAAA;IAAA;IAAAjD,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACf,IAAI,CAACmB,YAAY,GAAG,CAAC;IAAC;IAAAvC,aAAA,GAAAoB,CAAA;IACtB,IAAI,IAAI,CAACkB,KAAK,KAAK,WAAW,EAAE;MAAA;MAAAtC,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAC9B,IAAI,CAACkB,KAAK,GAAG,QAAQ;IACvB,CAAC;IAAA;IAAA;MAAAtC,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IACD,IAAI,CAACqB,YAAY,EAAE;EACrB;EAEQU,SAASA,CAAA;IAAA;IAAAnD,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACf,IAAI,CAACmB,YAAY,EAAE;IAAC;IAAAvC,aAAA,GAAAoB,CAAA;IACpB,IAAI,CAACoB,eAAe,GAAGK,IAAI,CAACC,GAAG,EAAE;IAAC;IAAA9C,aAAA,GAAAoB,CAAA;IAElC,IAAI,IAAI,CAACmB,YAAY,IAAI,IAAI,CAACJ,SAAS,EAAE;MAAA;MAAAnC,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACvC,IAAI,CAACkB,KAAK,GAAG,MAAM;MAAC;MAAAtC,aAAA,GAAAoB,CAAA;MACpB,IAAI,CAACsB,WAAW,GAAGG,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,CAACV,OAAO;IAC9C,CAAC;IAAA;IAAA;MAAApC,aAAA,GAAAsB,CAAA;IAAA;EACH;EAEA8B,QAAQA,CAAA;IAAA;IAAApD,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACN,OAAO,IAAI,CAACkB,KAAK;EACnB;EAEAe,UAAUA,CAAA;IAAA;IAAArD,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACR,OAAO;MACLkB,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBC,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BE,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BD,eAAe,EAAE,IAAI,CAACA;KACvB;EACH;;AACD;AAAAxC,aAAA,GAAAoB,CAAA;AA7DDkC,OAAA,CAAArB,cAAA,GAAAA,cAAA;AA+DA;AACA;AACA;AAEA,MAAasB,eAAe;EAa1BrB,YAAYsB,MAAA;EAAA;EAAA,CAAAxD,aAAA,GAAAsB,CAAA,UAAyC,EAAE;IAAA;IAAAtB,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAZ/C,KAAAqC,QAAQ,GAAG,IAAIC,GAAG,EAA2B;IAAC;IAAA1D,aAAA,GAAAoB,CAAA;IAC9C,KAAAuC,eAAe,GAAG,IAAID,GAAG,EAA0B;IAAC;IAAA1D,aAAA,GAAAoB,CAAA;IAGpD,KAAAwC,OAAO,GAAG;MAChBC,aAAa,EAAE,CAAC;MAChBC,kBAAkB,EAAE,CAAC;MACrBC,cAAc,EAAE,CAAC;MACjBC,mBAAmB,EAAE,CAAC;MACtBC,mBAAmB,EAAE;KACtB;IAAC;IAAAjE,aAAA,GAAAoB,CAAA;IAGA,IAAI,CAACoC,MAAM,GAAG;MACZU,mBAAmB,EAAE,KAAK;MAAE;MAC5BC,gBAAgB,EAAE,KAAK;MAAE;MACzBC,UAAU,EAAE,CAAC;MACbC,uBAAuB,EAAE,CAAC;MAC1BC,qBAAqB,EAAE,aAAa;MACpCC,aAAa,EAAE,IAAI;MACnBC,aAAa,EAAE,KAAK;MACpB,GAAGhB;KACJ;IAAC;IAAAxD,aAAA,GAAAoB,CAAA;IAEF,IAAI,CAACqD,mBAAmB,EAAE;IAAC;IAAAzE,aAAA,GAAAoB,CAAA;IAC3B,IAAI,CAACsD,oBAAoB,EAAE;EAC7B;EAEA;EACA;EACA;EAEAC,eAAeA,CAACC,OAAgE;IAAA;IAAA5E,aAAA,GAAAqB,CAAA;IAC9E,MAAMwD,QAAQ;IAAA;IAAA,CAAA7E,aAAA,GAAAoB,CAAA,QAAoB;MAChC,GAAGwD,OAAO;MACVE,YAAY,EAAE,IAAIjC,IAAI,EAAE;MACxBkC,aAAa,EAAE,IAAIlC,IAAI;KACxB;IAAC;IAAA7C,aAAA,GAAAoB,CAAA;IAEF,IAAI,CAACqC,QAAQ,CAACuB,GAAG,CAACJ,OAAO,CAACK,EAAE,EAAEJ,QAAQ,CAAC;IAAC;IAAA7E,aAAA,GAAAoB,CAAA;IACxC,IAAI,CAACuC,eAAe,CAACqB,GAAG,CAACJ,OAAO,CAACK,EAAE,EAAE,IAAIhD,cAAc,CACrD,IAAI,CAACuB,MAAM,CAACa,uBAAuB,EACnC,KAAK;IAAE;IACP,KAAK,CAAE;KACR,CAAC;IAAC;IAAArE,aAAA,GAAAoB,CAAA;IAEH8D,OAAO,CAACC,GAAG,CAAC,uBAAuBP,OAAO,CAAC/D,IAAI,KAAK+D,OAAO,CAACK,EAAE,GAAG,CAAC;EACpE;EAEAG,iBAAiBA,CAACC,SAAiB;IAAA;IAAArF,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACjC,IAAI,CAACqC,QAAQ,CAAC6B,MAAM,CAACD,SAAS,CAAC;IAAC;IAAArF,aAAA,GAAAoB,CAAA;IAChC,IAAI,CAACuC,eAAe,CAAC2B,MAAM,CAACD,SAAS,CAAC;IAAC;IAAArF,aAAA,GAAAoB,CAAA;IACvC8D,OAAO,CAACC,GAAG,CAAC,yBAAyBE,SAAS,EAAE,CAAC;EACnD;EAEAE,gBAAgBA,CAACC,WAAoB,EAAEC,IAAe;IAAA;IAAAzF,aAAA,GAAAqB,CAAA;IACpD,MAAMqE,WAAW;IAAA;IAAA,CAAA1F,aAAA,GAAAoB,CAAA,QAAGuE,KAAK,CAACC,IAAI,CAAC,IAAI,CAACnC,QAAQ,CAACoC,MAAM,EAAE,CAAC;IAEtD,IAAIC,QAAQ;IAAA;IAAA,CAAA9F,aAAA,GAAAoB,CAAA,QAAGsE,WAAW;IAAC;IAAA1F,aAAA,GAAAoB,CAAA;IAE3B,IAAIoE,WAAW,EAAE;MAAA;MAAAxF,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACf0E,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACnB,OAAO,IAAI;QAAA;QAAA5E,aAAA,GAAAqB,CAAA;QAAArB,aAAA,GAAAoB,CAAA;QAAA,OAAAwD,OAAO,CAAC/D,IAAI,KAAK2E,WAAW;MAAX,CAAW,CAAC;IACrE,CAAC;IAAA;IAAA;MAAAxF,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAED;IAAI;IAAA,CAAApB,aAAA,GAAAsB,CAAA,WAAAmE,IAAI;IAAA;IAAA,CAAAzF,aAAA,GAAAsB,CAAA,WAAImE,IAAI,CAACO,MAAM,GAAG,CAAC,GAAE;MAAA;MAAAhG,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAC3B0E,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACnB,OAAO,IAChC;QAAA;QAAA5E,aAAA,GAAAqB,CAAA;QAAArB,aAAA,GAAAoB,CAAA;QAAA,OAAAqE,IAAI,CAACQ,KAAK,CAACC,GAAG,IAAI;UAAA;UAAAlG,aAAA,GAAAqB,CAAA;UAAArB,aAAA,GAAAoB,CAAA;UAAA,OAAAwD,OAAO,CAACuB,QAAQ,CAACV,IAAI,CAACW,QAAQ,CAACF,GAAG,CAAC;QAAD,CAAC,CAAC;MAAD,CAAC,CACvD;IACH,CAAC;IAAA;IAAA;MAAAlG,aAAA,GAAAsB,CAAA;IAAA;IAED;IAAAtB,aAAA,GAAAoB,CAAA;IACA,OAAO0E,QAAQ,CAACC,MAAM,CAACnB,OAAO,IAC5B;MAAA;MAAA5E,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MAAA,kCAAApB,aAAA,GAAAsB,CAAA,WAAAsD,OAAO,CAACyB,MAAM,CAACC,MAAM,KAAK,SAAS;MAAA;MAAA,CAAAtG,aAAA,GAAAsB,CAAA,WAAIsD,OAAO,CAACyB,MAAM,CAACC,MAAM,KAAK,UAAU;IAAV,CAAU,CAC5E;EACH;EAEAC,UAAUA,CAAClB,SAAiB;IAAA;IAAArF,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAC1B,OAAO,IAAI,CAACqC,QAAQ,CAAC+C,GAAG,CAACnB,SAAS,CAAC;EACrC;EAEA;EACA;EACA;EAEAoB,aAAaA,CAACjB,WAAmB,EAAEC,IAAe;IAAA;IAAAzF,aAAA,GAAAqB,CAAA;IAChD,MAAMqF,iBAAiB;IAAA;IAAA,CAAA1G,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAACmE,gBAAgB,CAACC,WAAW,EAAEC,IAAI,CAAC;IAAC;IAAAzF,aAAA,GAAAoB,CAAA;IAEnE,IAAIsF,iBAAiB,CAACV,MAAM,KAAK,CAAC,EAAE;MAAA;MAAAhG,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAClC,OAAO,IAAI;IACb,CAAC;IAAA;IAAA;MAAApB,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAED,QAAQ,IAAI,CAACoC,MAAM,CAACc,qBAAqB;MACvC,KAAK,aAAa;QAAA;QAAAtE,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QAChB,OAAO,IAAI,CAACuF,mBAAmB,CAACD,iBAAiB,CAAC;MACpD,KAAK,mBAAmB;QAAA;QAAA1G,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACtB,OAAO,IAAI,CAACwF,yBAAyB,CAACF,iBAAiB,CAAC;MAC1D,KAAK,UAAU;QAAA;QAAA1G,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACb,OAAO,IAAI,CAACyF,iBAAiB,CAACH,iBAAiB,CAAC;MAClD,KAAK,QAAQ;QAAA;QAAA1G,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACX,OAAO,IAAI,CAAC0F,eAAe,CAACJ,iBAAiB,CAAC;MAChD;QAAA;QAAA1G,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACE,OAAOsF,iBAAiB,CAAC,CAAC,CAAC;IAC/B;EACF;EAEQC,mBAAmBA,CAAClD,QAA2B;IAAA;IAAAzD,aAAA,GAAAqB,CAAA;IACrD;IACA,MAAM0F,KAAK;IAAA;IAAA,CAAA/G,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAACwC,OAAO,CAACC,aAAa,GAAGJ,QAAQ,CAACuC,MAAM;IAAC;IAAAhG,aAAA,GAAAoB,CAAA;IAC3D,OAAOqC,QAAQ,CAACsD,KAAK,CAAC;EACxB;EAEQH,yBAAyBA,CAACnD,QAA2B;IAAA;IAAAzD,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAC3D;IACA,OAAOqC,QAAQ,CAACuD,MAAM,CAAC,CAACC,IAAI,EAAEC,OAAO,KACnC;MAAA;MAAAlH,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MAAA,OAAA8F,OAAO,CAACb,MAAM,CAACc,YAAY,GAAGF,IAAI,CAACZ,MAAM,CAACc,YAAY;MAAA;MAAA,CAAAnH,aAAA,GAAAsB,CAAA,WAAG4F,OAAO;MAAA;MAAA,CAAAlH,aAAA,GAAAsB,CAAA,WAAG2F,IAAI;IAAJ,CAAI,CACxE;EACH;EAEQJ,iBAAiBA,CAACpD,QAA2B;IAAA;IAAAzD,aAAA,GAAAqB,CAAA;IACnD;IACA,MAAM+F,OAAO;IAAA;IAAA,CAAApH,aAAA,GAAAoB,CAAA,QAAGqC,QAAQ,CAAC4D,GAAG,CAACzC,OAAO,IAAG;MAAA;MAAA5E,aAAA,GAAAqB,CAAA;MACrC,IAAIiG,MAAM;MAAA;MAAA,CAAAtH,aAAA,GAAAoB,CAAA,QAAG,CAAC;MAAC;MAAApB,aAAA,GAAAoB,CAAA;MACf,IAAIwD,OAAO,CAACyB,MAAM,CAACC,MAAM,KAAK,SAAS,EAAE;QAAA;QAAAtG,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QAAAkG,MAAM,IAAI,CAAC;MAAA,CAAC;MAAA;MAAA;QAAAtH,aAAA,GAAAsB,CAAA;MAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACrD,IAAIwD,OAAO,CAACyB,MAAM,CAACc,YAAY,GAAG,GAAG,EAAE;QAAA;QAAAnH,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QAAAkG,MAAM,IAAI,GAAG;MAAA,CAAC;MAAA;MAAA;QAAAtH,aAAA,GAAAsB,CAAA;MAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACrD,OAAOkG,MAAM;IACf,CAAC,CAAC;IAEF,MAAMC,WAAW;IAAA;IAAA,CAAAvH,aAAA,GAAAoB,CAAA,QAAGgG,OAAO,CAACJ,MAAM,CAAC,CAACQ,GAAG,EAAEF,MAAM,KAAK;MAAA;MAAAtH,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MAAA,OAAAoG,GAAG,GAAGF,MAAM;IAAN,CAAM,EAAE,CAAC,CAAC;IACpE,IAAIG,MAAM;IAAA;IAAA,CAAAzH,aAAA,GAAAoB,CAAA,QAAGsG,IAAI,CAACD,MAAM,EAAE,GAAGF,WAAW;IAAC;IAAAvH,aAAA,GAAAoB,CAAA;IAEzC,KAAK,IAAIuG,CAAC;IAAA;IAAA,CAAA3H,aAAA,GAAAoB,CAAA,QAAG,CAAC,GAAEuG,CAAC,GAAGlE,QAAQ,CAACuC,MAAM,EAAE2B,CAAC,EAAE,EAAE;MAAA;MAAA3H,aAAA,GAAAoB,CAAA;MACxCqG,MAAM,IAAIL,OAAO,CAACO,CAAC,CAAC;MAAC;MAAA3H,aAAA,GAAAoB,CAAA;MACrB,IAAIqG,MAAM,IAAI,CAAC,EAAE;QAAA;QAAAzH,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACf,OAAOqC,QAAQ,CAACkE,CAAC,CAAC;MACpB,CAAC;MAAA;MAAA;QAAA3H,aAAA,GAAAsB,CAAA;MAAA;IACH;IAAC;IAAAtB,aAAA,GAAAoB,CAAA;IAED,OAAOqC,QAAQ,CAAC,CAAC,CAAC;EACpB;EAEQqD,eAAeA,CAACrD,QAA2B;IAAA;IAAAzD,aAAA,GAAAqB,CAAA;IACjD,MAAM0F,KAAK;IAAA;IAAA,CAAA/G,aAAA,GAAAoB,CAAA,QAAGsG,IAAI,CAACE,KAAK,CAACF,IAAI,CAACD,MAAM,EAAE,GAAGhE,QAAQ,CAACuC,MAAM,CAAC;IAAC;IAAAhG,aAAA,GAAAoB,CAAA;IAC1D,OAAOqC,QAAQ,CAACsD,KAAK,CAAC;EACxB;EAEA;EACA;EACA;EAEA,MAAMc,WAAWA,CACfxC,SAAiB,EACjBzC,SAAoD;IAAA;IAAA5C,aAAA,GAAAqB,CAAA;IAEpD,MAAMuD,OAAO;IAAA;IAAA,CAAA5E,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAACqC,QAAQ,CAAC+C,GAAG,CAACnB,SAAS,CAAC;IAAC;IAAArF,aAAA,GAAAoB,CAAA;IAC7C,IAAI,CAACwD,OAAO,EAAE;MAAA;MAAA5E,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACZ,MAAM,IAAI2B,KAAK,CAAC,sBAAsBsC,SAAS,EAAE,CAAC;IACpD,CAAC;IAAA;IAAA;MAAArF,aAAA,GAAAsB,CAAA;IAAA;IAED,MAAMwG,cAAc;IAAA;IAAA,CAAA9H,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAACuC,eAAe,CAAC6C,GAAG,CAACnB,SAAS,CAAC;IAAC;IAAArF,aAAA,GAAAoB,CAAA;IAC3D,IAAI,CAAC0G,cAAc,EAAE;MAAA;MAAA9H,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACnB,MAAM,IAAI2B,KAAK,CAAC,0CAA0CsC,SAAS,EAAE,CAAC;IACxE,CAAC;IAAA;IAAA;MAAArF,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAED,IAAI,CAACwC,OAAO,CAACC,aAAa,EAAE;IAC5B,MAAMkE,SAAS;IAAA;IAAA,CAAA/H,aAAA,GAAAoB,CAAA,QAAGyB,IAAI,CAACC,GAAG,EAAE;IAAC;IAAA9C,aAAA,GAAAoB,CAAA;IAE7B,IAAI;MACF,MAAM4B,MAAM;MAAA;MAAA,CAAAhD,aAAA,GAAAoB,CAAA,QAAG,MAAM0G,cAAc,CAACnF,OAAO,CAAC,MAAM;QAAA;QAAA3C,aAAA,GAAAqB,CAAA;QAAArB,aAAA,GAAAoB,CAAA;QAAA,OAAAwB,SAAS,CAACgC,OAAO,CAAC;MAAD,CAAC,CAAC;MAAC;MAAA5E,aAAA,GAAAoB,CAAA;MAEtE,IAAI,CAACwC,OAAO,CAACE,kBAAkB,EAAE;MAAC;MAAA9D,aAAA,GAAAoB,CAAA;MAClC,IAAI,CAAC4G,kBAAkB,CAACnF,IAAI,CAACC,GAAG,EAAE,GAAGiF,SAAS,CAAC;MAAC;MAAA/H,aAAA,GAAAoB,CAAA;MAChD,IAAI,CAAC6G,mBAAmB,CAAC5C,SAAS,EAAE,IAAI,EAAExC,IAAI,CAACC,GAAG,EAAE,GAAGiF,SAAS,CAAC;MAAC;MAAA/H,aAAA,GAAAoB,CAAA;MAElE,OAAO4B,MAAM;IACf,CAAC,CAAC,OAAOE,KAAK,EAAE;MAAA;MAAAlD,aAAA,GAAAoB,CAAA;MACd,IAAI,CAACwC,OAAO,CAACG,cAAc,EAAE;MAAC;MAAA/D,aAAA,GAAAoB,CAAA;MAC9B,IAAI,CAAC6G,mBAAmB,CAAC5C,SAAS,EAAE,KAAK,EAAExC,IAAI,CAACC,GAAG,EAAE,GAAGiF,SAAS,CAAC;MAAC;MAAA/H,aAAA,GAAAoB,CAAA;MAEnE,IAAI0G,cAAc,CAAC1E,QAAQ,EAAE,KAAK,MAAM,EAAE;QAAA;QAAApD,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACxC,IAAI,CAACwC,OAAO,CAACK,mBAAmB,EAAE;MACpC,CAAC;MAAA;MAAA;QAAAjE,aAAA,GAAAsB,CAAA;MAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAED,MAAM8B,KAAK;IACb;EACF;EAEA;EACA;EACA;EAEQuB,mBAAmBA,CAAA;IAAA;IAAAzE,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACzB,IAAI,CAAC8C,mBAAmB,GAAGgE,WAAW,CAAC,YAAW;MAAA;MAAAlI,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MAChD,KAAK,MAAM,CAACiE,SAAS,EAAET,OAAO,CAAC,IAAI,IAAI,CAACnB,QAAQ,EAAE;QAAA;QAAAzD,aAAA,GAAAoB,CAAA;QAChD,MAAM,IAAI,CAAC+G,kBAAkB,CAAC9C,SAAS,EAAET,OAAO,CAAC;MACnD;IACF,CAAC,EAAE,IAAI,CAACpB,MAAM,CAACU,mBAAmB,CAAC;EACrC;EAEQ,MAAMiE,kBAAkBA,CAAC9C,SAAiB,EAAET,OAAwB;IAAA;IAAA5E,aAAA,GAAAqB,CAAA;IAC1E,MAAM0G,SAAS;IAAA;IAAA,CAAA/H,aAAA,GAAAoB,CAAA,SAAGyB,IAAI,CAACC,GAAG,EAAE;IAAC;IAAA9C,aAAA,GAAAoB,CAAA;IAE7B,IAAI;MACF;MACA,MAAMgH,QAAQ;MAAA;MAAA,CAAApI,aAAA,GAAAoB,CAAA,SAAG,MAAM,IAAI,CAACiH,WAAW,CAACzD,OAAO,CAAC;MAChD,MAAM0D,QAAQ;MAAA;MAAA,CAAAtI,aAAA,GAAAoB,CAAA,SAAGyB,IAAI,CAACC,GAAG,EAAE,GAAGiF,SAAS;MAEvC,MAAMQ,WAAW;MAAA;MAAA,CAAAvI,aAAA,GAAAoB,CAAA,SAAgB;QAC/BP,IAAI,EAAE,MAAM;QACZyF,MAAM,EAAE8B,QAAQ;QAAA;QAAA,CAAApI,aAAA,GAAAsB,CAAA,WAAG,MAAM;QAAA;QAAA,CAAAtB,aAAA,GAAAsB,CAAA,WAAG,MAAM;QAClCkH,OAAO,EAAEJ,QAAQ;QAAA;QAAA,CAAApI,aAAA,GAAAsB,CAAA,WAAG,oBAAoB;QAAA;QAAA,CAAAtB,aAAA,GAAAsB,CAAA,WAAG,wBAAwB;QACnEmH,SAAS,EAAE,IAAI5F,IAAI,EAAE;QACrByF;OACD;MAAC;MAAAtI,aAAA,GAAAoB,CAAA;MAEF,IAAI,CAACsH,yBAAyB,CAACrD,SAAS,EAAEkD,WAAW,CAAC;IAExD,CAAC,CAAC,OAAOrF,KAAK,EAAE;MACd,MAAMqF,WAAW;MAAA;MAAA,CAAAvI,aAAA,GAAAoB,CAAA,SAAgB;QAC/BP,IAAI,EAAE,MAAM;QACZyF,MAAM,EAAE,MAAM;QACdkC,OAAO,EAAEtF,KAAK,YAAYH,KAAK;QAAA;QAAA,CAAA/C,aAAA,GAAAsB,CAAA,WAAG4B,KAAK,CAACsF,OAAO;QAAA;QAAA,CAAAxI,aAAA,GAAAsB,CAAA,WAAG,eAAe;QACjEmH,SAAS,EAAE,IAAI5F,IAAI,EAAE;QACrByF,QAAQ,EAAEzF,IAAI,CAACC,GAAG,EAAE,GAAGiF;OACxB;MAAC;MAAA/H,aAAA,GAAAoB,CAAA;MAEF,IAAI,CAACsH,yBAAyB,CAACrD,SAAS,EAAEkD,WAAW,CAAC;IACxD;EACF;EAEQ,MAAMF,WAAWA,CAACzD,OAAwB;IAAA;IAAA5E,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAChD;IACA;IACA,IAAI;MACF,MAAMuH,GAAG;MAAA;MAAA,CAAA3I,aAAA,GAAAoB,CAAA,SAAG,GAAGwD,OAAO,CAACgE,QAAQ,MAAMhE,OAAO,CAAC+D,GAAG;MAAG;MAAA,CAAA3I,aAAA,GAAAsB,CAAA,WAAAsD,OAAO,CAAC3E,IAAI;MAAA;MAAA,CAAAD,aAAA,GAAAsB,CAAA,WAAI,SAAS,GAAE;MAC9E,MAAM8G,QAAQ;MAAA;MAAA,CAAApI,aAAA,GAAAoB,CAAA,SAAG,MAAMyH,KAAK,CAACF,GAAG,EAAE;QAChCG,MAAM,EAAE,KAAK;QACb1G,OAAO,EAAE;OACH,CAAC;MAAC;MAAApC,aAAA,GAAAoB,CAAA;MACV,OAAOgH,QAAQ,CAACW,EAAE;IACpB,CAAC,CAAC,MAAM;MAAA;MAAA/I,aAAA,GAAAoB,CAAA;MACN,OAAO,KAAK;IACd;EACF;EAEQ6G,mBAAmBA,CAAC5C,SAAiB,EAAE2D,OAAgB,EAAE7B,YAAoB;IAAA;IAAAnH,aAAA,GAAAqB,CAAA;IACnF,MAAMuD,OAAO;IAAA;IAAA,CAAA5E,aAAA,GAAAoB,CAAA,SAAG,IAAI,CAACqC,QAAQ,CAAC+C,GAAG,CAACnB,SAAS,CAAC;IAAC;IAAArF,aAAA,GAAAoB,CAAA;IAC7C,IAAI,CAACwD,OAAO,EAAE;MAAA;MAAA5E,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA;IAAA,CAAO;IAAA;IAAA;MAAApB,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAErBwD,OAAO,CAACyB,MAAM,CAACc,YAAY,GAAGA,YAAY;IAAC;IAAAnH,aAAA,GAAAoB,CAAA;IAC3CwD,OAAO,CAACyB,MAAM,CAAC4C,SAAS,GAAG,IAAIpG,IAAI,EAAE;IAAC;IAAA7C,aAAA,GAAAoB,CAAA;IACtCwD,OAAO,CAACG,aAAa,GAAG,IAAIlC,IAAI,EAAE;IAAC;IAAA7C,aAAA,GAAAoB,CAAA;IAEnC,IAAI4H,OAAO,EAAE;MAAA;MAAAhJ,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACXwD,OAAO,CAACyB,MAAM,CAAC6C,SAAS,GAAGxB,IAAI,CAACyB,GAAG,CAAC,CAAC,EAAEvE,OAAO,CAACyB,MAAM,CAAC6C,SAAS,GAAG,GAAG,CAAC;IACxE,CAAC,MAAM;MAAA;MAAAlJ,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACLwD,OAAO,CAACyB,MAAM,CAAC6C,SAAS,GAAGxB,IAAI,CAAC0B,GAAG,CAAC,CAAC,EAAExE,OAAO,CAACyB,MAAM,CAAC6C,SAAS,GAAG,GAAG,CAAC;IACxE;IAEA;IAAA;IAAAlJ,aAAA,GAAAoB,CAAA;IACA,IAAIwD,OAAO,CAACyB,MAAM,CAAC6C,SAAS,GAAG,GAAG,EAAE;MAAA;MAAAlJ,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAClCwD,OAAO,CAACyB,MAAM,CAACC,MAAM,GAAG,SAAS;IACnC,CAAC,MAAM;MAAA;MAAAtG,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA,IAAIwD,OAAO,CAACyB,MAAM,CAAC6C,SAAS,GAAG,GAAG,EAAE;QAAA;QAAAlJ,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACzCwD,OAAO,CAACyB,MAAM,CAACC,MAAM,GAAG,UAAU;MACpC,CAAC,MAAM;QAAA;QAAAtG,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACLwD,OAAO,CAACyB,MAAM,CAACC,MAAM,GAAG,WAAW;MACrC;IAAA;EACF;EAEQoC,yBAAyBA,CAACrD,SAAiB,EAAEkD,WAAwB;IAAA;IAAAvI,aAAA,GAAAqB,CAAA;IAC3E,MAAMuD,OAAO;IAAA;IAAA,CAAA5E,aAAA,GAAAoB,CAAA,SAAG,IAAI,CAACqC,QAAQ,CAAC+C,GAAG,CAACnB,SAAS,CAAC;IAAC;IAAArF,aAAA,GAAAoB,CAAA;IAC7C,IAAI,CAACwD,OAAO,EAAE;MAAA;MAAA5E,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA;IAAA,CAAO;IAAA;IAAA;MAAApB,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAErBwD,OAAO,CAACyB,MAAM,CAACgD,MAAM,GAAG,CAACd,WAAW,EAAE,GAAG3D,OAAO,CAACyB,MAAM,CAACgD,MAAM,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAAA;IAAAtJ,aAAA,GAAAoB,CAAA;IAC7EwD,OAAO,CAACyB,MAAM,CAAC4C,SAAS,GAAGV,WAAW,CAACE,SAAS;IAAC;IAAAzI,aAAA,GAAAoB,CAAA;IACjDwD,OAAO,CAACG,aAAa,GAAG,IAAIlC,IAAI,EAAE;IAElC;IACA,MAAM0G,YAAY;IAAA;IAAA,CAAAvJ,aAAA,GAAAoB,CAAA,SAAGwD,OAAO,CAACyB,MAAM,CAACgD,MAAM,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IACtD,MAAME,YAAY;IAAA;IAAA,CAAAxJ,aAAA,GAAAoB,CAAA,SAAGmI,YAAY,CAACxD,MAAM,CAAC0D,KAAK,IAAI;MAAA;MAAAzJ,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MAAA,OAAAqI,KAAK,CAACnD,MAAM,KAAK,MAAM;IAAN,CAAM,CAAC,CAACN,MAAM;IAAC;IAAAhG,aAAA,GAAAoB,CAAA;IAElF,IAAIoI,YAAY,KAAK,CAAC,EAAE;MAAA;MAAAxJ,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACtBwD,OAAO,CAACyB,MAAM,CAACC,MAAM,GAAG,SAAS;IACnC,CAAC,MAAM;MAAA;MAAAtG,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA,IAAIoI,YAAY,GAAG,CAAC,EAAE;QAAA;QAAAxJ,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QAC3BwD,OAAO,CAACyB,MAAM,CAACC,MAAM,GAAG,UAAU;MACpC,CAAC,MAAM;QAAA;QAAAtG,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACLwD,OAAO,CAACyB,MAAM,CAACC,MAAM,GAAG,WAAW;MACrC;IAAA;EACF;EAEQ0B,kBAAkBA,CAACb,YAAoB;IAAA;IAAAnH,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAC7C,IAAI,CAACwC,OAAO,CAACI,mBAAmB,GAC9B,CAAC,IAAI,CAACJ,OAAO,CAACI,mBAAmB,GAAGmD,YAAY,IAAI,CAAC;EACzD;EAEA;EACA;EACA;EAEQzC,oBAAoBA,CAAA;IAAA;IAAA1E,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAC1B;IAEA;IACA,IAAI,CAACuD,eAAe,CAAC;MACnBM,EAAE,EAAE,mBAAmB;MACvBpE,IAAI,EAAE,kBAAkB;MACxBgB,OAAO,EAAE,OAAO;MAChB8G,GAAG,EAAE,WAAW;MAChBC,QAAQ,EAAE,MAAM;MAChBc,IAAI,EAAE,IAAI;MACVzJ,IAAI,EAAE,mBAAmB;MACzBoG,MAAM,EAAE;QACNC,MAAM,EAAE,SAAS;QACjB2C,SAAS,EAAE,IAAIpG,IAAI,EAAE;QACrBsE,YAAY,EAAE,EAAE;QAChB+B,SAAS,EAAE,CAAC;QACZS,MAAM,EAAE,GAAG;QACXN,MAAM,EAAE;OACT;MACDlD,QAAQ,EAAE;QACRV,IAAI,EAAE,CAAC,aAAa,EAAE,MAAM,EAAE,MAAM,CAAC;QACrCmE,WAAW,EAAE,aAAa;QAC1BC,YAAY,EAAE,CAAC,iBAAiB,EAAE,kBAAkB,EAAE,iBAAiB,CAAC;QACxEC,YAAY,EAAE,CAAC,kBAAkB,CAAC;QAClCC,SAAS,EAAE;UACTC,GAAG,EAAE,GAAG;UACRC,MAAM,EAAE,GAAG;UACXC,OAAO,EAAE;;;KAGd,CAAC;IAEF;IAAA;IAAAlK,aAAA,GAAAoB,CAAA;IACA,IAAI,CAACuD,eAAe,CAAC;MACnBM,EAAE,EAAE,sBAAsB;MAC1BpE,IAAI,EAAE,oBAAoB;MAC1BgB,OAAO,EAAE,OAAO;MAChB8G,GAAG,EAAE,WAAW;MAChBC,QAAQ,EAAE,MAAM;MAChBc,IAAI,EAAE,IAAI;MACVzJ,IAAI,EAAE,eAAe;MACrBoG,MAAM,EAAE;QACNC,MAAM,EAAE,SAAS;QACjB2C,SAAS,EAAE,IAAIpG,IAAI,EAAE;QACrBsE,YAAY,EAAE,EAAE;QAChB+B,SAAS,EAAE,CAAC;QACZS,MAAM,EAAE,GAAG;QACXN,MAAM,EAAE;OACT;MACDlD,QAAQ,EAAE;QACRV,IAAI,EAAE,CAAC,SAAS,EAAE,YAAY,EAAE,MAAM,CAAC;QACvCmE,WAAW,EAAE,aAAa;QAC1BC,YAAY,EAAE,CAAC,cAAc,EAAE,eAAe,EAAE,iBAAiB,CAAC;QAClEC,YAAY,EAAE,CAAC,kBAAkB,EAAE,cAAc,CAAC;QAClDC,SAAS,EAAE;UACTC,GAAG,EAAE,GAAG;UACRC,MAAM,EAAE,GAAG;UACXC,OAAO,EAAE;;;KAGd,CAAC;IAEF;IAAA;IAAAlK,aAAA,GAAAoB,CAAA;IACA,IAAI,CAACuD,eAAe,CAAC;MACnBM,EAAE,EAAE,kBAAkB;MACtBpE,IAAI,EAAE,UAAU;MAChBgB,OAAO,EAAE,OAAO;MAChB8G,GAAG,EAAE,WAAW;MAChBC,QAAQ,EAAE,MAAM;MAChBc,IAAI,EAAE,IAAI;MACVzJ,IAAI,EAAE,WAAW;MACjBoG,MAAM,EAAE;QACNC,MAAM,EAAE,SAAS;QACjB2C,SAAS,EAAE,IAAIpG,IAAI,EAAE;QACrBsE,YAAY,EAAE,EAAE;QAChB+B,SAAS,EAAE,CAAC;QACZS,MAAM,EAAE,GAAG;QACXN,MAAM,EAAE;OACT;MACDlD,QAAQ,EAAE;QACRV,IAAI,EAAE,CAAC,UAAU,EAAE,SAAS,EAAE,gBAAgB,CAAC;QAC/CmE,WAAW,EAAE,aAAa;QAC1BC,YAAY,EAAE,CAAC,YAAY,EAAE,SAAS,EAAE,SAAS,CAAC;QAClDC,YAAY,EAAE,EAAE;QAChBC,SAAS,EAAE;UACTC,GAAG,EAAE,GAAG;UACRC,MAAM,EAAE,IAAI;UACZC,OAAO,EAAE;;;KAGd,CAAC;EACJ;EAEA;EACA;EACA;EAEA7G,UAAUA,CAAA;IAAA;IAAArD,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACR,OAAO;MACL,GAAG,IAAI,CAACwC,OAAO;MACfuG,kBAAkB,EAAE,IAAI,CAAC1G,QAAQ,CAAC2G,IAAI;MACtCC,eAAe,EAAE1E,KAAK,CAACC,IAAI,CAAC,IAAI,CAACnC,QAAQ,CAACoC,MAAM,EAAE,CAAC,CAChDE,MAAM,CAAC3E,CAAC,IAAI;QAAA;QAAApB,aAAA,GAAAqB,CAAA;QAAArB,aAAA,GAAAoB,CAAA;QAAA,OAAAA,CAAC,CAACiF,MAAM,CAACC,MAAM,KAAK,SAAS;MAAT,CAAS,CAAC,CAACN,MAAM;MACpDrC,eAAe,EAAEgC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACjC,eAAe,CAAC2G,OAAO,EAAE,CAAC,CACxDjD,GAAG,CAAC,CAAC,CAACpC,EAAE,EAAEsF,EAAE,CAAC,KAAM;QAAA;QAAAvK,aAAA,GAAAqB,CAAA;QAAArB,aAAA,GAAAoB,CAAA;QAAA;UAAEiE,SAAS,EAAEJ,EAAE;UAAE,GAAGsF,EAAE,CAAClH,UAAU;QAAE,CAAE;MAAF,CAAG;KAC7D;EACH;EAEAmH,cAAcA,CAAA;IAAA;IAAAxK,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACZ,OAAOuE,KAAK,CAACC,IAAI,CAAC,IAAI,CAACnC,QAAQ,CAACoC,MAAM,EAAE,CAAC;EAC3C;EAEA4E,gBAAgBA,CAACpF,SAAiB;IAAA;IAAArF,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAChC,OAAO,IAAI,CAACqC,QAAQ,CAAC+C,GAAG,CAACnB,SAAS,CAAC,EAAEgB,MAAM;EAC7C;EAEAqE,OAAOA,CAAA;IAAA;IAAA1K,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACL,IAAI,IAAI,CAAC8C,mBAAmB,EAAE;MAAA;MAAAlE,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAC5BuJ,aAAa,CAAC,IAAI,CAACzG,mBAAmB,CAAC;IACzC,CAAC;IAAA;IAAA;MAAAlE,aAAA,GAAAsB,CAAA;IAAA;EACH;;AACD;AAAAtB,aAAA,GAAAoB,CAAA;AAraDkC,OAAA,CAAAC,eAAA,GAAAA,eAAA;AAkcA,MAAaqH,UAAU;EAarB1I,YAAY2I,eAAgC;IAAA;IAAA7K,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAZpC,KAAA0J,MAAM,GAAG,IAAIpH,GAAG,EAAuB;IAAC;IAAA1D,aAAA,GAAAoB,CAAA;IAExC,KAAA2J,YAAY,GAAG,IAAIrH,GAAG,EAAgD;IAAC;IAAA1D,aAAA,GAAAoB,CAAA;IACvE,KAAAwC,OAAO,GAAmB;MAChCC,aAAa,EAAE,CAAC;MAChBC,kBAAkB,EAAE,CAAC;MACrBC,cAAc,EAAE,CAAC;MACjBiH,cAAc,EAAE,CAAC;MACjBC,mBAAmB,EAAE,CAAC;MACtBC,QAAQ,EAAE;KACX;IAAC;IAAAlL,aAAA,GAAAoB,CAAA;IAGA,IAAI,CAACyJ,eAAe,GAAGA,eAAe;IAAC;IAAA7K,aAAA,GAAAoB,CAAA;IACvC,IAAI,CAAC+J,kBAAkB,EAAE;EAC3B;EAEA;EACA;EACA;EAEAC,QAAQA,CAACC,KAAkB;IAAA;IAAArL,aAAA,GAAAqB,CAAA;IACzB,MAAMiK,GAAG;IAAA;IAAA,CAAAtL,aAAA,GAAAoB,CAAA,SAAG,GAAGiK,KAAK,CAACvC,MAAM,IAAIuC,KAAK,CAACpL,IAAI,EAAE;IAAC;IAAAD,aAAA,GAAAoB,CAAA;IAC5C,IAAI,CAAC0J,MAAM,CAAC9F,GAAG,CAACsG,GAAG,EAAED,KAAK,CAAC;EAC7B;EAEAE,WAAWA,CAACzC,MAAc,EAAE7I,IAAY;IAAA;IAAAD,aAAA,GAAAqB,CAAA;IACtC,MAAMiK,GAAG;IAAA;IAAA,CAAAtL,aAAA,GAAAoB,CAAA,SAAG,GAAG0H,MAAM,IAAI7I,IAAI,EAAE;IAAC;IAAAD,aAAA,GAAAoB,CAAA;IAChC,IAAI,CAAC0J,MAAM,CAACxF,MAAM,CAACgG,GAAG,CAAC;EACzB;EAEA;EACA;EACA;EAEA,MAAME,YAAYA,CAChB1C,MAAc,EACd7I,IAAY,EACZwL,IAAU,EACVC,OAAgC;IAAA;IAAA1L,aAAA,GAAAqB,CAAA;IAEhC,MAAM0G,SAAS;IAAA;IAAA,CAAA/H,aAAA,GAAAoB,CAAA,SAAGyB,IAAI,CAACC,GAAG,EAAE;IAAC;IAAA9C,aAAA,GAAAoB,CAAA;IAC7B,IAAI,CAACwC,OAAO,CAACC,aAAa,EAAE;IAAC;IAAA7D,aAAA,GAAAoB,CAAA;IAE7B,IAAI;MACF;MACA,MAAMiK,KAAK;MAAA;MAAA,CAAArL,aAAA,GAAAoB,CAAA,SAAG,IAAI,CAACuK,SAAS,CAAC7C,MAAM,EAAE7I,IAAI,CAAC;MAAC;MAAAD,aAAA,GAAAoB,CAAA;MAC3C,IAAI,CAACiK,KAAK,EAAE;QAAA;QAAArL,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACV,MAAM,IAAI2B,KAAK,CAAC,oBAAoB+F,MAAM,IAAI7I,IAAI,EAAE,CAAC;MACvD,CAAC;MAAA;MAAA;QAAAD,aAAA,GAAAsB,CAAA;MAAA;MAED;MAAAtB,aAAA,GAAAoB,CAAA;MACA;MAAI;MAAA,CAAApB,aAAA,GAAAsB,CAAA,WAAA+J,KAAK,CAACO,SAAS;MAAA;MAAA,CAAA5L,aAAA,GAAAsB,CAAA,WAAI,CAAC,IAAI,CAACuK,cAAc,CAACR,KAAK,CAAC,GAAE;QAAA;QAAArL,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QAClD,IAAI,CAACwC,OAAO,CAACqH,mBAAmB,EAAE;QAAC;QAAAjL,aAAA,GAAAoB,CAAA;QACnC,MAAM,IAAI2B,KAAK,CAAC,qBAAqB,CAAC;MACxC,CAAC;MAAA;MAAA;QAAA/C,aAAA,GAAAsB,CAAA;MAAA;MAED;MACA,MAAMsD,OAAO;MAAA;MAAA,CAAA5E,aAAA,GAAAoB,CAAA,SAAG,IAAI,CAACyJ,eAAe,CAACpE,aAAa,CAAC4E,KAAK,CAAC7F,WAAW,CAAC;MAAC;MAAAxF,aAAA,GAAAoB,CAAA;MACtE,IAAI,CAACwD,OAAO,EAAE;QAAA;QAAA5E,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACZ,MAAM,IAAI2B,KAAK,CAAC,6BAA6BsI,KAAK,CAAC7F,WAAW,EAAE,CAAC;MACnE,CAAC;MAAA;MAAA;QAAAxF,aAAA,GAAAsB,CAAA;MAAA;MAED;MACA,MAAM0B,MAAM;MAAA;MAAA,CAAAhD,aAAA,GAAAoB,CAAA,SAAG,MAAM,IAAI,CAACyJ,eAAe,CAAChD,WAAW,CACnDjD,OAAO,CAACK,EAAE,EACV,MAAOJ,QAAQ,IAAI;QAAA;QAAA7E,aAAA,GAAAqB,CAAA;QAAArB,aAAA,GAAAoB,CAAA;QACjB,OAAO,MAAM,IAAI,CAAC0K,cAAc,CAACjH,QAAQ,EAAEwG,KAAK,EAAEI,IAAI,EAAEC,OAAO,CAAC;MAClE,CAAC,CACF;MAAC;MAAA1L,aAAA,GAAAoB,CAAA;MAEF,IAAI,CAACwC,OAAO,CAACE,kBAAkB,EAAE;MAAC;MAAA9D,aAAA,GAAAoB,CAAA;MAClC,IAAI,CAAC2K,aAAa,CAAClJ,IAAI,CAACC,GAAG,EAAE,GAAGiF,SAAS,CAAC;MAAC;MAAA/H,aAAA,GAAAoB,CAAA;MAE3C,OAAO4B,MAAM;IAEf,CAAC,CAAC,OAAOE,KAAK,EAAE;MAAA;MAAAlD,aAAA,GAAAoB,CAAA;MACd,IAAI,CAACwC,OAAO,CAACG,cAAc,EAAE;MAAC;MAAA/D,aAAA,GAAAoB,CAAA;MAC9B,IAAI,CAAC2K,aAAa,CAAClJ,IAAI,CAACC,GAAG,EAAE,GAAGiF,SAAS,CAAC;MAAC;MAAA/H,aAAA,GAAAoB,CAAA;MAC3C,MAAM8B,KAAK;IACb;EACF;EAEQyI,SAASA,CAAC7C,MAAc,EAAE7I,IAAY;IAAA;IAAAD,aAAA,GAAAqB,CAAA;IAC5C,MAAM2K,QAAQ;IAAA;IAAA,CAAAhM,aAAA,GAAAoB,CAAA,SAAG,GAAG0H,MAAM,IAAI7I,IAAI,EAAE;IACpC,MAAMgM,UAAU;IAAA;IAAA,CAAAjM,aAAA,GAAAoB,CAAA,SAAG,IAAI,CAAC0J,MAAM,CAACtE,GAAG,CAACwF,QAAQ,CAAC;IAAC;IAAAhM,aAAA,GAAAoB,CAAA;IAE7C,IAAI6K,UAAU,EAAE;MAAA;MAAAjM,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACd,OAAO6K,UAAU;IACnB,CAAC;IAAA;IAAA;MAAAjM,aAAA,GAAAsB,CAAA;IAAA;IAED;IAAAtB,aAAA,GAAAoB,CAAA;IACA,KAAK,MAAM,CAACkK,GAAG,EAAED,KAAK,CAAC,IAAI,IAAI,CAACP,MAAM,EAAE;MAAA;MAAA9K,aAAA,GAAAoB,CAAA;MACtC,IAAIkK,GAAG,CAACY,UAAU,CAAC,GAAGpD,MAAM,GAAG,CAAC,EAAE;QAAA;QAAA9I,aAAA,GAAAsB,CAAA;QAChC,MAAM6K,SAAS;QAAA;QAAA,CAAAnM,aAAA,GAAAoB,CAAA,SAAGkK,GAAG,CAACc,SAAS,CAACtD,MAAM,CAAC9C,MAAM,GAAG,CAAC,CAAC;QAAC;QAAAhG,aAAA,GAAAoB,CAAA;QACnD,IAAI,IAAI,CAACiL,SAAS,CAACF,SAAS,EAAElM,IAAI,CAAC,EAAE;UAAA;UAAAD,aAAA,GAAAsB,CAAA;UAAAtB,aAAA,GAAAoB,CAAA;UACnC,OAAOiK,KAAK;QACd,CAAC;QAAA;QAAA;UAAArL,aAAA,GAAAsB,CAAA;QAAA;MACH,CAAC;MAAA;MAAA;QAAAtB,aAAA,GAAAsB,CAAA;MAAA;IACH;IAAC;IAAAtB,aAAA,GAAAoB,CAAA;IAED,OAAOD,SAAS;EAClB;EAEQkL,SAASA,CAACF,SAAiB,EAAEG,WAAmB;IAAA;IAAAtM,aAAA,GAAAqB,CAAA;IACtD;IACA,MAAMkL,aAAa;IAAA;IAAA,CAAAvM,aAAA,GAAAoB,CAAA,SAAG+K,SAAS,CAACK,KAAK,CAAC,GAAG,CAAC;IAC1C,MAAMC,eAAe;IAAA;IAAA,CAAAzM,aAAA,GAAAoB,CAAA,SAAGkL,WAAW,CAACE,KAAK,CAAC,GAAG,CAAC;IAAC;IAAAxM,aAAA,GAAAoB,CAAA;IAE/C,IAAImL,aAAa,CAACvG,MAAM,KAAKyG,eAAe,CAACzG,MAAM,EAAE;MAAA;MAAAhG,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACnD,OAAO,KAAK;IACd,CAAC;IAAA;IAAA;MAAApB,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAED,KAAK,IAAIuG,CAAC;IAAA;IAAA,CAAA3H,aAAA,GAAAoB,CAAA,SAAG,CAAC,GAAEuG,CAAC,GAAG4E,aAAa,CAACvG,MAAM,EAAE2B,CAAC,EAAE,EAAE;MAC7C,MAAM+E,YAAY;MAAA;MAAA,CAAA1M,aAAA,GAAAoB,CAAA,SAAGmL,aAAa,CAAC5E,CAAC,CAAC;MACrC,MAAMgF,cAAc;MAAA;MAAA,CAAA3M,aAAA,GAAAoB,CAAA,SAAGqL,eAAe,CAAC9E,CAAC,CAAC;MAAC;MAAA3H,aAAA,GAAAoB,CAAA;MAE1C,IAAIsL,YAAY,CAACR,UAAU,CAAC,GAAG,CAAC,EAAE;QAAA;QAAAlM,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QAChC;QACA;MACF,CAAC;MAAA;MAAA;QAAApB,aAAA,GAAAsB,CAAA;MAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAED,IAAIsL,YAAY,KAAKC,cAAc,EAAE;QAAA;QAAA3M,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACnC,OAAO,KAAK;MACd,CAAC;MAAA;MAAA;QAAApB,aAAA,GAAAsB,CAAA;MAAA;IACH;IAAC;IAAAtB,aAAA,GAAAoB,CAAA;IAED,OAAO,IAAI;EACb;EAEQyK,cAAcA,CAACR,KAAkB;IAAA;IAAArL,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACvC,IAAI,CAACiK,KAAK,CAACO,SAAS,EAAE;MAAA;MAAA5L,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA,OAAO,IAAI;IAAA,CAAC;IAAA;IAAA;MAAApB,aAAA,GAAAsB,CAAA;IAAA;IAElC,MAAMgK,GAAG;IAAA;IAAA,CAAAtL,aAAA,GAAAoB,CAAA,SAAG,GAAGiK,KAAK,CAACvC,MAAM,IAAIuC,KAAK,CAACpL,IAAI,EAAE;IAC3C,MAAM6C,GAAG;IAAA;IAAA,CAAA9C,aAAA,GAAAoB,CAAA,SAAGyB,IAAI,CAACC,GAAG,EAAE;IACtB,MAAM8J,OAAO;IAAA;IAAA,CAAA5M,aAAA,GAAAoB,CAAA,SAAG,IAAI,CAAC2J,YAAY,CAACvE,GAAG,CAAC8E,GAAG,CAAC;IAAC;IAAAtL,aAAA,GAAAoB,CAAA;IAE3C;IAAI;IAAA,CAAApB,aAAA,GAAAsB,CAAA,YAACsL,OAAO;IAAA;IAAA,CAAA5M,aAAA,GAAAsB,CAAA,WAAIwB,GAAG,GAAG8J,OAAO,CAACC,SAAS,GAAE;MAAA;MAAA7M,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACvC,IAAI,CAAC2J,YAAY,CAAC/F,GAAG,CAACsG,GAAG,EAAE;QACzBwB,KAAK,EAAE,CAAC;QACRD,SAAS,EAAE/J,GAAG,GAAGuI,KAAK,CAACO,SAAS,CAACmB;OAClC,CAAC;MAAC;MAAA/M,aAAA,GAAAoB,CAAA;MACH,OAAO,IAAI;IACb,CAAC;IAAA;IAAA;MAAApB,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAED,IAAIwL,OAAO,CAACE,KAAK,IAAIzB,KAAK,CAACO,SAAS,CAACoB,QAAQ,EAAE;MAAA;MAAAhN,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAC7C,OAAO,KAAK;IACd,CAAC;IAAA;IAAA;MAAApB,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAEDwL,OAAO,CAACE,KAAK,EAAE;IAAC;IAAA9M,aAAA,GAAAoB,CAAA;IAChB,OAAO,IAAI;EACb;EAEQ,MAAM0K,cAAcA,CAC1BjH,QAAyB,EACzBwG,KAAkB,EAClBI,IAAU,EACVC,OAAgC;IAAA;IAAA1L,aAAA,GAAAqB,CAAA;IAEhC,MAAM4L,UAAU;IAAA;IAAA,CAAAjN,aAAA,GAAAoB,CAAA;IAAG;IAAA,CAAApB,aAAA,GAAAsB,CAAA,WAAA+J,KAAK,CAAC4B,UAAU;IAAA;IAAA,CAAAjN,aAAA,GAAAsB,CAAA,WAAI+J,KAAK,CAACpL,IAAI;IACjD,MAAM0I,GAAG;IAAA;IAAA,CAAA3I,aAAA,GAAAoB,CAAA,SAAG,GAAGyD,QAAQ,CAAC+D,QAAQ,MAAM/D,QAAQ,CAAC8D,GAAG,IAAI9D,QAAQ,CAAC6E,IAAI,GAAGuD,UAAU,EAAE;IAElF,MAAMC,cAAc;IAAA;IAAA,CAAAlN,aAAA,GAAAoB,CAAA,SAAgB;MAClC0H,MAAM,EAAEuC,KAAK,CAACvC,MAAM;MACpB4C,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClC,GAAGA;;KAEN;IAAC;IAAA1L,aAAA,GAAAoB,CAAA;IAEF;IAAI;IAAA,CAAApB,aAAA,GAAAsB,CAAA,WAAAmK,IAAI;IAAA;IAAA,CAAAzL,aAAA,GAAAsB,CAAA,WAAI+J,KAAK,CAACvC,MAAM,KAAK,KAAK,GAAE;MAAA;MAAA9I,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAClC8L,cAAc,CAACzB,IAAI,GAAG0B,IAAI,CAACC,SAAS,CAAC3B,IAAI,CAAC;IAC5C,CAAC;IAAA;IAAA;MAAAzL,aAAA,GAAAsB,CAAA;IAAA;IAED;IACA,MAAMc,OAAO;IAAA;IAAA,CAAApC,aAAA,GAAAoB,CAAA;IAAG;IAAA,CAAApB,aAAA,GAAAsB,CAAA,WAAA+J,KAAK,CAACjJ,OAAO;IAAA;IAAA,CAAApC,aAAA,GAAAsB,CAAA,WAAI,KAAK,GAAC,CAAC;IACxC,MAAM+L,UAAU;IAAA;IAAA,CAAArN,aAAA,GAAAoB,CAAA,SAAG,IAAIkM,eAAe,EAAE;IACxC,MAAMC,SAAS;IAAA;IAAA,CAAAvN,aAAA,GAAAoB,CAAA,SAAGoM,UAAU,CAAC,MAAK;MAAA;MAAAxN,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MAChCiM,UAAU,CAACI,KAAK,EAAE;MAAC;MAAAzN,aAAA,GAAAoB,CAAA;MACnB,IAAI,CAACwC,OAAO,CAACsH,QAAQ,EAAE;IACzB,CAAC,EAAE9I,OAAO,CAAC;IAAC;IAAApC,aAAA,GAAAoB,CAAA;IAEZ8L,cAAc,CAACQ,MAAM,GAAGL,UAAU,CAACK,MAAM;IAAC;IAAA1N,aAAA,GAAAoB,CAAA;IAE1C,IAAI;MACF,MAAMgH,QAAQ;MAAA;MAAA,CAAApI,aAAA,GAAAoB,CAAA,SAAG,MAAMyH,KAAK,CAACF,GAAG,EAAEuE,cAAc,CAAC;MAAC;MAAAlN,aAAA,GAAAoB,CAAA;MAClDuM,YAAY,CAACJ,SAAS,CAAC;MAAC;MAAAvN,aAAA,GAAAoB,CAAA;MAExB,IAAI,CAACgH,QAAQ,CAACW,EAAE,EAAE;QAAA;QAAA/I,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QAChB,MAAM,IAAI2B,KAAK,CAAC,kBAAkBqF,QAAQ,CAAC9B,MAAM,IAAI8B,QAAQ,CAACwF,UAAU,EAAE,CAAC;MAC7E,CAAC;MAAA;MAAA;QAAA5N,aAAA,GAAAsB,CAAA;MAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAED,OAAO,MAAMgH,QAAQ,CAACyF,IAAI,EAAE;IAC9B,CAAC,CAAC,OAAO3K,KAAK,EAAE;MAAA;MAAAlD,aAAA,GAAAoB,CAAA;MACduM,YAAY,CAACJ,SAAS,CAAC;MAAC;MAAAvN,aAAA,GAAAoB,CAAA;MACxB,MAAM8B,KAAK;IACb;EACF;EAEQ6I,aAAaA,CAAC+B,OAAe;IAAA;IAAA9N,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACnC,IAAI,CAACwC,OAAO,CAACoH,cAAc,GACzB,CAAC,IAAI,CAACpH,OAAO,CAACoH,cAAc,GAAG8C,OAAO,IAAI,CAAC;EAC/C;EAEA;EACA;EACA;EAEQ3C,kBAAkBA,CAAA;IAAA;IAAAnL,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACxB;IACA,IAAI,CAACgK,QAAQ,CAAC;MACZnL,IAAI,EAAE,4BAA4B;MAClC6I,MAAM,EAAE,MAAM;MACdtD,WAAW,EAAE,kBAAkB;MAC/ByH,UAAU,EAAE,4BAA4B;MACxCc,cAAc,EAAE,IAAI;MACpB3L,OAAO,EAAE,KAAK;MACdwJ,SAAS,EAAE;QAAEoB,QAAQ,EAAE,GAAG;QAAED,MAAM,EAAE;MAAK,CAAE,CAAC;KAC7C,CAAC;IAAC;IAAA/M,aAAA,GAAAoB,CAAA;IAEH,IAAI,CAACgK,QAAQ,CAAC;MACZnL,IAAI,EAAE,wBAAwB;MAC9B6I,MAAM,EAAE,MAAM;MACdtD,WAAW,EAAE,kBAAkB;MAC/ByH,UAAU,EAAE,wBAAwB;MACpCc,cAAc,EAAE,IAAI;MACpB3L,OAAO,EAAE,KAAK;MACdwJ,SAAS,EAAE;QAAEoB,QAAQ,EAAE,EAAE;QAAED,MAAM,EAAE;MAAK;KACzC,CAAC;IAEF;IAAA;IAAA/M,aAAA,GAAAoB,CAAA;IACA,IAAI,CAACgK,QAAQ,CAAC;MACZnL,IAAI,EAAE,eAAe;MACrB6I,MAAM,EAAE,KAAK;MACbtD,WAAW,EAAE,oBAAoB;MACjCuI,cAAc,EAAE,IAAI;MACpBnC,SAAS,EAAE;QAAEoB,QAAQ,EAAE,GAAG;QAAED,MAAM,EAAE;MAAK;KAC1C,CAAC;IAAC;IAAA/M,aAAA,GAAAoB,CAAA;IAEH,IAAI,CAACgK,QAAQ,CAAC;MACZnL,IAAI,EAAE,eAAe;MACrB6I,MAAM,EAAE,MAAM;MACdtD,WAAW,EAAE,oBAAoB;MACjCuI,cAAc,EAAE,IAAI;MACpBnC,SAAS,EAAE;QAAEoB,QAAQ,EAAE,EAAE;QAAED,MAAM,EAAE;MAAK;KACzC,CAAC;IAAC;IAAA/M,aAAA,GAAAoB,CAAA;IAEH,IAAI,CAACgK,QAAQ,CAAC;MACZnL,IAAI,EAAE,mBAAmB;MACzB6I,MAAM,EAAE,KAAK;MACbtD,WAAW,EAAE,oBAAoB;MACjCuI,cAAc,EAAE,IAAI;MACpBnC,SAAS,EAAE;QAAEoB,QAAQ,EAAE,GAAG;QAAED,MAAM,EAAE;MAAK;KAC1C,CAAC;IAAC;IAAA/M,aAAA,GAAAoB,CAAA;IAEH,IAAI,CAACgK,QAAQ,CAAC;MACZnL,IAAI,EAAE,mBAAmB;MACzB6I,MAAM,EAAE,KAAK;MACbtD,WAAW,EAAE,oBAAoB;MACjCuI,cAAc,EAAE,IAAI;MACpBnC,SAAS,EAAE;QAAEoB,QAAQ,EAAE,EAAE;QAAED,MAAM,EAAE;MAAK;KACzC,CAAC;IAEF;IAAA;IAAA/M,aAAA,GAAAoB,CAAA;IACA,IAAI,CAACgK,QAAQ,CAAC;MACZnL,IAAI,EAAE,gBAAgB;MACtB6I,MAAM,EAAE,MAAM;MACdtD,WAAW,EAAE,UAAU;MACvBuI,cAAc,EAAE,IAAI;MACpB3L,OAAO,EAAE,KAAK;MACdwJ,SAAS,EAAE;QAAEoB,QAAQ,EAAE,EAAE;QAAED,MAAM,EAAE;MAAK;KACzC,CAAC;EACJ;EAEA;EACA;EACA;EAEA1J,UAAUA,CAAA;IAAA;IAAArD,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACR,OAAO;MAAE,GAAG,IAAI,CAACwC;IAAO,CAAE;EAC5B;EAEAoK,SAASA,CAAA;IAAA;IAAAhO,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACP,OAAOuE,KAAK,CAACC,IAAI,CAAC,IAAI,CAACkF,MAAM,CAACjF,MAAM,EAAE,CAAC;EACzC;EAEAoI,kBAAkBA,CAAA;IAAA;IAAAjO,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAChB,OAAO,IAAI,CAACyJ,eAAe;EAC7B;;AACD;AAAA7K,aAAA,GAAAoB,CAAA;AAlSDkC,OAAA,CAAAsH,UAAA,GAAAA,UAAA", "ignoreList": []}