7491db8cb954e66d2675f7035fed3fa9
"use strict";

/**
 * Service Registry for SizeWise Suite
 *
 * Implements microservices preparation with:
 * - Service discovery and registration
 * - Health monitoring and status tracking
 * - Load balancing and failover
 * - Circuit breaker patterns
 * - API gateway preparation
 * - Service mesh readiness
 */
/* istanbul ignore next */
function cov_lld5u1oeb() {
  var path = "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\frontend\\lib\\services\\ServiceRegistry.ts";
  var hash = "73bf91fab749dbedcfbc2654d933352995535bfb";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\frontend\\lib\\services\\ServiceRegistry.ts",
    statementMap: {
      "0": {
        start: {
          line: 13,
          column: 0
        },
        end: {
          line: 13,
          column: 62
        }
      },
      "1": {
        start: {
          line: 14,
          column: 0
        },
        end: {
          line: 14,
          column: 79
        }
      },
      "2": {
        start: {
          line: 22,
          column: 8
        },
        end: {
          line: 22,
          column: 35
        }
      },
      "3": {
        start: {
          line: 23,
          column: 8
        },
        end: {
          line: 23,
          column: 31
        }
      },
      "4": {
        start: {
          line: 24,
          column: 8
        },
        end: {
          line: 24,
          column: 49
        }
      },
      "5": {
        start: {
          line: 25,
          column: 8
        },
        end: {
          line: 25,
          column: 30
        }
      },
      "6": {
        start: {
          line: 26,
          column: 8
        },
        end: {
          line: 26,
          column: 30
        }
      },
      "7": {
        start: {
          line: 27,
          column: 8
        },
        end: {
          line: 27,
          column: 33
        }
      },
      "8": {
        start: {
          line: 28,
          column: 8
        },
        end: {
          line: 28,
          column: 30
        }
      },
      "9": {
        start: {
          line: 29,
          column: 8
        },
        end: {
          line: 29,
          column: 29
        }
      },
      "10": {
        start: {
          line: 32,
          column: 8
        },
        end: {
          line: 37,
          column: 9
        }
      },
      "11": {
        start: {
          line: 33,
          column: 12
        },
        end: {
          line: 35,
          column: 13
        }
      },
      "12": {
        start: {
          line: 34,
          column: 16
        },
        end: {
          line: 34,
          column: 59
        }
      },
      "13": {
        start: {
          line: 36,
          column: 12
        },
        end: {
          line: 36,
          column: 37
        }
      },
      "14": {
        start: {
          line: 38,
          column: 8
        },
        end: {
          line: 46,
          column: 9
        }
      },
      "15": {
        start: {
          line: 39,
          column: 27
        },
        end: {
          line: 39,
          column: 44
        }
      },
      "16": {
        start: {
          line: 40,
          column: 12
        },
        end: {
          line: 40,
          column: 29
        }
      },
      "17": {
        start: {
          line: 41,
          column: 12
        },
        end: {
          line: 41,
          column: 26
        }
      },
      "18": {
        start: {
          line: 44,
          column: 12
        },
        end: {
          line: 44,
          column: 29
        }
      },
      "19": {
        start: {
          line: 45,
          column: 12
        },
        end: {
          line: 45,
          column: 24
        }
      },
      "20": {
        start: {
          line: 49,
          column: 8
        },
        end: {
          line: 49,
          column: 30
        }
      },
      "21": {
        start: {
          line: 50,
          column: 8
        },
        end: {
          line: 52,
          column: 9
        }
      },
      "22": {
        start: {
          line: 51,
          column: 12
        },
        end: {
          line: 51,
          column: 34
        }
      },
      "23": {
        start: {
          line: 53,
          column: 8
        },
        end: {
          line: 53,
          column: 28
        }
      },
      "24": {
        start: {
          line: 56,
          column: 8
        },
        end: {
          line: 56,
          column: 28
        }
      },
      "25": {
        start: {
          line: 57,
          column: 8
        },
        end: {
          line: 57,
          column: 42
        }
      },
      "26": {
        start: {
          line: 58,
          column: 8
        },
        end: {
          line: 61,
          column: 9
        }
      },
      "27": {
        start: {
          line: 59,
          column: 12
        },
        end: {
          line: 59,
          column: 32
        }
      },
      "28": {
        start: {
          line: 60,
          column: 12
        },
        end: {
          line: 60,
          column: 57
        }
      },
      "29": {
        start: {
          line: 64,
          column: 8
        },
        end: {
          line: 64,
          column: 26
        }
      },
      "30": {
        start: {
          line: 67,
          column: 8
        },
        end: {
          line: 72,
          column: 10
        }
      },
      "31": {
        start: {
          line: 75,
          column: 0
        },
        end: {
          line: 75,
          column: 40
        }
      },
      "32": {
        start: {
          line: 81,
          column: 8
        },
        end: {
          line: 81,
          column: 34
        }
      },
      "33": {
        start: {
          line: 82,
          column: 8
        },
        end: {
          line: 82,
          column: 41
        }
      },
      "34": {
        start: {
          line: 83,
          column: 8
        },
        end: {
          line: 89,
          column: 10
        }
      },
      "35": {
        start: {
          line: 90,
          column: 8
        },
        end: {
          line: 99,
          column: 10
        }
      },
      "36": {
        start: {
          line: 100,
          column: 8
        },
        end: {
          line: 100,
          column: 35
        }
      },
      "37": {
        start: {
          line: 101,
          column: 8
        },
        end: {
          line: 101,
          column: 36
        }
      },
      "38": {
        start: {
          line: 107,
          column: 25
        },
        end: {
          line: 111,
          column: 9
        }
      },
      "39": {
        start: {
          line: 112,
          column: 8
        },
        end: {
          line: 112,
          column: 48
        }
      },
      "40": {
        start: {
          line: 113,
          column: 8
        },
        end: {
          line: 115,
          column: 11
        }
      },
      "41": {
        start: {
          line: 116,
          column: 8
        },
        end: {
          line: 116,
          column: 75
        }
      },
      "42": {
        start: {
          line: 119,
          column: 8
        },
        end: {
          line: 119,
          column: 40
        }
      },
      "43": {
        start: {
          line: 120,
          column: 8
        },
        end: {
          line: 120,
          column: 47
        }
      },
      "44": {
        start: {
          line: 121,
          column: 8
        },
        end: {
          line: 121,
          column: 58
        }
      },
      "45": {
        start: {
          line: 124,
          column: 28
        },
        end: {
          line: 124,
          column: 62
        }
      },
      "46": {
        start: {
          line: 125,
          column: 23
        },
        end: {
          line: 125,
          column: 34
        }
      },
      "47": {
        start: {
          line: 126,
          column: 8
        },
        end: {
          line: 128,
          column: 9
        }
      },
      "48": {
        start: {
          line: 127,
          column: 12
        },
        end: {
          line: 127,
          column: 80
        }
      },
      "49": {
        start: {
          line: 127,
          column: 50
        },
        end: {
          line: 127,
          column: 78
        }
      },
      "50": {
        start: {
          line: 129,
          column: 8
        },
        end: {
          line: 131,
          column: 9
        }
      },
      "51": {
        start: {
          line: 130,
          column: 12
        },
        end: {
          line: 130,
          column: 106
        }
      },
      "52": {
        start: {
          line: 130,
          column: 50
        },
        end: {
          line: 130,
          column: 104
        }
      },
      "53": {
        start: {
          line: 130,
          column: 68
        },
        end: {
          line: 130,
          column: 103
        }
      },
      "54": {
        start: {
          line: 133,
          column: 8
        },
        end: {
          line: 133,
          column: 119
        }
      },
      "55": {
        start: {
          line: 133,
          column: 42
        },
        end: {
          line: 133,
          column: 117
        }
      },
      "56": {
        start: {
          line: 136,
          column: 8
        },
        end: {
          line: 136,
          column: 44
        }
      },
      "57": {
        start: {
          line: 142,
          column: 34
        },
        end: {
          line: 142,
          column: 74
        }
      },
      "58": {
        start: {
          line: 143,
          column: 8
        },
        end: {
          line: 145,
          column: 9
        }
      },
      "59": {
        start: {
          line: 144,
          column: 12
        },
        end: {
          line: 144,
          column: 24
        }
      },
      "60": {
        start: {
          line: 146,
          column: 8
        },
        end: {
          line: 157,
          column: 9
        }
      },
      "61": {
        start: {
          line: 148,
          column: 16
        },
        end: {
          line: 148,
          column: 67
        }
      },
      "62": {
        start: {
          line: 150,
          column: 16
        },
        end: {
          line: 150,
          column: 73
        }
      },
      "63": {
        start: {
          line: 152,
          column: 16
        },
        end: {
          line: 152,
          column: 65
        }
      },
      "64": {
        start: {
          line: 154,
          column: 16
        },
        end: {
          line: 154,
          column: 63
        }
      },
      "65": {
        start: {
          line: 156,
          column: 16
        },
        end: {
          line: 156,
          column: 44
        }
      },
      "66": {
        start: {
          line: 161,
          column: 22
        },
        end: {
          line: 161,
          column: 66
        }
      },
      "67": {
        start: {
          line: 162,
          column: 8
        },
        end: {
          line: 162,
          column: 31
        }
      },
      "68": {
        start: {
          line: 166,
          column: 8
        },
        end: {
          line: 166,
          column: 123
        }
      },
      "69": {
        start: {
          line: 166,
          column: 50
        },
        end: {
          line: 166,
          column: 121
        }
      },
      "70": {
        start: {
          line: 170,
          column: 24
        },
        end: {
          line: 177,
          column: 10
        }
      },
      "71": {
        start: {
          line: 171,
          column: 25
        },
        end: {
          line: 171,
          column: 26
        }
      },
      "72": {
        start: {
          line: 172,
          column: 12
        },
        end: {
          line: 173,
          column: 28
        }
      },
      "73": {
        start: {
          line: 173,
          column: 16
        },
        end: {
          line: 173,
          column: 28
        }
      },
      "74": {
        start: {
          line: 174,
          column: 12
        },
        end: {
          line: 175,
          column: 30
        }
      },
      "75": {
        start: {
          line: 175,
          column: 16
        },
        end: {
          line: 175,
          column: 30
        }
      },
      "76": {
        start: {
          line: 176,
          column: 12
        },
        end: {
          line: 176,
          column: 26
        }
      },
      "77": {
        start: {
          line: 178,
          column: 28
        },
        end: {
          line: 178,
          column: 76
        }
      },
      "78": {
        start: {
          line: 178,
          column: 60
        },
        end: {
          line: 178,
          column: 72
        }
      },
      "79": {
        start: {
          line: 179,
          column: 21
        },
        end: {
          line: 179,
          column: 48
        }
      },
      "80": {
        start: {
          line: 180,
          column: 8
        },
        end: {
          line: 185,
          column: 9
        }
      },
      "81": {
        start: {
          line: 180,
          column: 21
        },
        end: {
          line: 180,
          column: 22
        }
      },
      "82": {
        start: {
          line: 181,
          column: 12
        },
        end: {
          line: 181,
          column: 33
        }
      },
      "83": {
        start: {
          line: 182,
          column: 12
        },
        end: {
          line: 184,
          column: 13
        }
      },
      "84": {
        start: {
          line: 183,
          column: 16
        },
        end: {
          line: 183,
          column: 35
        }
      },
      "85": {
        start: {
          line: 186,
          column: 8
        },
        end: {
          line: 186,
          column: 27
        }
      },
      "86": {
        start: {
          line: 189,
          column: 22
        },
        end: {
          line: 189,
          column: 65
        }
      },
      "87": {
        start: {
          line: 190,
          column: 8
        },
        end: {
          line: 190,
          column: 31
        }
      },
      "88": {
        start: {
          line: 196,
          column: 24
        },
        end: {
          line: 196,
          column: 52
        }
      },
      "89": {
        start: {
          line: 197,
          column: 8
        },
        end: {
          line: 199,
          column: 9
        }
      },
      "90": {
        start: {
          line: 198,
          column: 12
        },
        end: {
          line: 198,
          column: 63
        }
      },
      "91": {
        start: {
          line: 200,
          column: 31
        },
        end: {
          line: 200,
          column: 66
        }
      },
      "92": {
        start: {
          line: 201,
          column: 8
        },
        end: {
          line: 203,
          column: 9
        }
      },
      "93": {
        start: {
          line: 202,
          column: 12
        },
        end: {
          line: 202,
          column: 83
        }
      },
      "94": {
        start: {
          line: 204,
          column: 8
        },
        end: {
          line: 204,
          column: 37
        }
      },
      "95": {
        start: {
          line: 205,
          column: 26
        },
        end: {
          line: 205,
          column: 36
        }
      },
      "96": {
        start: {
          line: 206,
          column: 8
        },
        end: {
          line: 220,
          column: 9
        }
      },
      "97": {
        start: {
          line: 207,
          column: 27
        },
        end: {
          line: 207,
          column: 81
        }
      },
      "98": {
        start: {
          line: 207,
          column: 62
        },
        end: {
          line: 207,
          column: 80
        }
      },
      "99": {
        start: {
          line: 208,
          column: 12
        },
        end: {
          line: 208,
          column: 46
        }
      },
      "100": {
        start: {
          line: 209,
          column: 12
        },
        end: {
          line: 209,
          column: 60
        }
      },
      "101": {
        start: {
          line: 210,
          column: 12
        },
        end: {
          line: 210,
          column: 78
        }
      },
      "102": {
        start: {
          line: 211,
          column: 12
        },
        end: {
          line: 211,
          column: 26
        }
      },
      "103": {
        start: {
          line: 214,
          column: 12
        },
        end: {
          line: 214,
          column: 42
        }
      },
      "104": {
        start: {
          line: 215,
          column: 12
        },
        end: {
          line: 215,
          column: 79
        }
      },
      "105": {
        start: {
          line: 216,
          column: 12
        },
        end: {
          line: 218,
          column: 13
        }
      },
      "106": {
        start: {
          line: 217,
          column: 16
        },
        end: {
          line: 217,
          column: 51
        }
      },
      "107": {
        start: {
          line: 219,
          column: 12
        },
        end: {
          line: 219,
          column: 24
        }
      },
      "108": {
        start: {
          line: 226,
          column: 8
        },
        end: {
          line: 230,
          column: 44
        }
      },
      "109": {
        start: {
          line: 227,
          column: 12
        },
        end: {
          line: 229,
          column: 13
        }
      },
      "110": {
        start: {
          line: 228,
          column: 16
        },
        end: {
          line: 228,
          column: 66
        }
      },
      "111": {
        start: {
          line: 233,
          column: 26
        },
        end: {
          line: 233,
          column: 36
        }
      },
      "112": {
        start: {
          line: 234,
          column: 8
        },
        end: {
          line: 256,
          column: 9
        }
      },
      "113": {
        start: {
          line: 236,
          column: 29
        },
        end: {
          line: 236,
          column: 60
        }
      },
      "114": {
        start: {
          line: 237,
          column: 29
        },
        end: {
          line: 237,
          column: 51
        }
      },
      "115": {
        start: {
          line: 238,
          column: 32
        },
        end: {
          line: 244,
          column: 13
        }
      },
      "116": {
        start: {
          line: 245,
          column: 12
        },
        end: {
          line: 245,
          column: 67
        }
      },
      "117": {
        start: {
          line: 248,
          column: 32
        },
        end: {
          line: 254,
          column: 13
        }
      },
      "118": {
        start: {
          line: 255,
          column: 12
        },
        end: {
          line: 255,
          column: 67
        }
      },
      "119": {
        start: {
          line: 261,
          column: 8
        },
        end: {
          line: 271,
          column: 9
        }
      },
      "120": {
        start: {
          line: 262,
          column: 24
        },
        end: {
          line: 262,
          column: 90
        }
      },
      "121": {
        start: {
          line: 263,
          column: 29
        },
        end: {
          line: 266,
          column: 14
        }
      },
      "122": {
        start: {
          line: 267,
          column: 12
        },
        end: {
          line: 267,
          column: 31
        }
      },
      "123": {
        start: {
          line: 270,
          column: 12
        },
        end: {
          line: 270,
          column: 25
        }
      },
      "124": {
        start: {
          line: 274,
          column: 24
        },
        end: {
          line: 274,
          column: 52
        }
      },
      "125": {
        start: {
          line: 275,
          column: 8
        },
        end: {
          line: 276,
          column: 19
        }
      },
      "126": {
        start: {
          line: 276,
          column: 12
        },
        end: {
          line: 276,
          column: 19
        }
      },
      "127": {
        start: {
          line: 277,
          column: 8
        },
        end: {
          line: 277,
          column: 51
        }
      },
      "128": {
        start: {
          line: 278,
          column: 8
        },
        end: {
          line: 278,
          column: 46
        }
      },
      "129": {
        start: {
          line: 279,
          column: 8
        },
        end: {
          line: 279,
          column: 43
        }
      },
      "130": {
        start: {
          line: 280,
          column: 8
        },
        end: {
          line: 285,
          column: 9
        }
      },
      "131": {
        start: {
          line: 281,
          column: 12
        },
        end: {
          line: 281,
          column: 83
        }
      },
      "132": {
        start: {
          line: 284,
          column: 12
        },
        end: {
          line: 284,
          column: 83
        }
      },
      "133": {
        start: {
          line: 287,
          column: 8
        },
        end: {
          line: 295,
          column: 9
        }
      },
      "134": {
        start: {
          line: 288,
          column: 12
        },
        end: {
          line: 288,
          column: 46
        }
      },
      "135": {
        start: {
          line: 290,
          column: 13
        },
        end: {
          line: 295,
          column: 9
        }
      },
      "136": {
        start: {
          line: 291,
          column: 12
        },
        end: {
          line: 291,
          column: 47
        }
      },
      "137": {
        start: {
          line: 294,
          column: 12
        },
        end: {
          line: 294,
          column: 48
        }
      },
      "138": {
        start: {
          line: 298,
          column: 24
        },
        end: {
          line: 298,
          column: 52
        }
      },
      "139": {
        start: {
          line: 299,
          column: 8
        },
        end: {
          line: 300,
          column: 19
        }
      },
      "140": {
        start: {
          line: 300,
          column: 12
        },
        end: {
          line: 300,
          column: 19
        }
      },
      "141": {
        start: {
          line: 301,
          column: 8
        },
        end: {
          line: 301,
          column: 84
        }
      },
      "142": {
        start: {
          line: 302,
          column: 8
        },
        end: {
          line: 302,
          column: 57
        }
      },
      "143": {
        start: {
          line: 303,
          column: 8
        },
        end: {
          line: 303,
          column: 43
        }
      },
      "144": {
        start: {
          line: 305,
          column: 29
        },
        end: {
          line: 305,
          column: 62
        }
      },
      "145": {
        start: {
          line: 306,
          column: 29
        },
        end: {
          line: 306,
          column: 89
        }
      },
      "146": {
        start: {
          line: 306,
          column: 58
        },
        end: {
          line: 306,
          column: 81
        }
      },
      "147": {
        start: {
          line: 307,
          column: 8
        },
        end: {
          line: 315,
          column: 9
        }
      },
      "148": {
        start: {
          line: 308,
          column: 12
        },
        end: {
          line: 308,
          column: 46
        }
      },
      "149": {
        start: {
          line: 310,
          column: 13
        },
        end: {
          line: 315,
          column: 9
        }
      },
      "150": {
        start: {
          line: 311,
          column: 12
        },
        end: {
          line: 311,
          column: 47
        }
      },
      "151": {
        start: {
          line: 314,
          column: 12
        },
        end: {
          line: 314,
          column: 48
        }
      },
      "152": {
        start: {
          line: 318,
          column: 8
        },
        end: {
          line: 319,
          column: 66
        }
      },
      "153": {
        start: {
          line: 327,
          column: 8
        },
        end: {
          line: 354,
          column: 11
        }
      },
      "154": {
        start: {
          line: 356,
          column: 8
        },
        end: {
          line: 383,
          column: 11
        }
      },
      "155": {
        start: {
          line: 385,
          column: 8
        },
        end: {
          line: 412,
          column: 11
        }
      },
      "156": {
        start: {
          line: 418,
          column: 8
        },
        end: {
          line: 425,
          column: 10
        }
      },
      "157": {
        start: {
          line: 422,
          column: 29
        },
        end: {
          line: 422,
          column: 58
        }
      },
      "158": {
        start: {
          line: 424,
          column: 36
        },
        end: {
          line: 424,
          column: 73
        }
      },
      "159": {
        start: {
          line: 428,
          column: 8
        },
        end: {
          line: 428,
          column: 50
        }
      },
      "160": {
        start: {
          line: 431,
          column: 8
        },
        end: {
          line: 431,
          column: 52
        }
      },
      "161": {
        start: {
          line: 434,
          column: 8
        },
        end: {
          line: 436,
          column: 9
        }
      },
      "162": {
        start: {
          line: 435,
          column: 12
        },
        end: {
          line: 435,
          column: 52
        }
      },
      "163": {
        start: {
          line: 439,
          column: 0
        },
        end: {
          line: 439,
          column: 42
        }
      },
      "164": {
        start: {
          line: 442,
          column: 8
        },
        end: {
          line: 442,
          column: 32
        }
      },
      "165": {
        start: {
          line: 443,
          column: 8
        },
        end: {
          line: 443,
          column: 38
        }
      },
      "166": {
        start: {
          line: 444,
          column: 8
        },
        end: {
          line: 451,
          column: 10
        }
      },
      "167": {
        start: {
          line: 452,
          column: 8
        },
        end: {
          line: 452,
          column: 47
        }
      },
      "168": {
        start: {
          line: 453,
          column: 8
        },
        end: {
          line: 453,
          column: 34
        }
      },
      "169": {
        start: {
          line: 459,
          column: 20
        },
        end: {
          line: 459,
          column: 51
        }
      },
      "170": {
        start: {
          line: 460,
          column: 8
        },
        end: {
          line: 460,
          column: 36
        }
      },
      "171": {
        start: {
          line: 463,
          column: 20
        },
        end: {
          line: 463,
          column: 39
        }
      },
      "172": {
        start: {
          line: 464,
          column: 8
        },
        end: {
          line: 464,
          column: 32
        }
      },
      "173": {
        start: {
          line: 470,
          column: 26
        },
        end: {
          line: 470,
          column: 36
        }
      },
      "174": {
        start: {
          line: 471,
          column: 8
        },
        end: {
          line: 471,
          column: 37
        }
      },
      "175": {
        start: {
          line: 472,
          column: 8
        },
        end: {
          line: 500,
          column: 9
        }
      },
      "176": {
        start: {
          line: 474,
          column: 26
        },
        end: {
          line: 474,
          column: 54
        }
      },
      "177": {
        start: {
          line: 475,
          column: 12
        },
        end: {
          line: 477,
          column: 13
        }
      },
      "178": {
        start: {
          line: 476,
          column: 16
        },
        end: {
          line: 476,
          column: 70
        }
      },
      "179": {
        start: {
          line: 479,
          column: 12
        },
        end: {
          line: 482,
          column: 13
        }
      },
      "180": {
        start: {
          line: 480,
          column: 16
        },
        end: {
          line: 480,
          column: 51
        }
      },
      "181": {
        start: {
          line: 481,
          column: 16
        },
        end: {
          line: 481,
          column: 55
        }
      },
      "182": {
        start: {
          line: 484,
          column: 28
        },
        end: {
          line: 484,
          column: 81
        }
      },
      "183": {
        start: {
          line: 485,
          column: 12
        },
        end: {
          line: 487,
          column: 13
        }
      },
      "184": {
        start: {
          line: 486,
          column: 16
        },
        end: {
          line: 486,
          column: 82
        }
      },
      "185": {
        start: {
          line: 489,
          column: 27
        },
        end: {
          line: 491,
          column: 14
        }
      },
      "186": {
        start: {
          line: 490,
          column: 16
        },
        end: {
          line: 490,
          column: 81
        }
      },
      "187": {
        start: {
          line: 492,
          column: 12
        },
        end: {
          line: 492,
          column: 46
        }
      },
      "188": {
        start: {
          line: 493,
          column: 12
        },
        end: {
          line: 493,
          column: 55
        }
      },
      "189": {
        start: {
          line: 494,
          column: 12
        },
        end: {
          line: 494,
          column: 26
        }
      },
      "190": {
        start: {
          line: 497,
          column: 12
        },
        end: {
          line: 497,
          column: 42
        }
      },
      "191": {
        start: {
          line: 498,
          column: 12
        },
        end: {
          line: 498,
          column: 55
        }
      },
      "192": {
        start: {
          line: 499,
          column: 12
        },
        end: {
          line: 499,
          column: 24
        }
      },
      "193": {
        start: {
          line: 503,
          column: 25
        },
        end: {
          line: 503,
          column: 44
        }
      },
      "194": {
        start: {
          line: 504,
          column: 27
        },
        end: {
          line: 504,
          column: 52
        }
      },
      "195": {
        start: {
          line: 505,
          column: 8
        },
        end: {
          line: 507,
          column: 9
        }
      },
      "196": {
        start: {
          line: 506,
          column: 12
        },
        end: {
          line: 506,
          column: 30
        }
      },
      "197": {
        start: {
          line: 509,
          column: 8
        },
        end: {
          line: 516,
          column: 9
        }
      },
      "198": {
        start: {
          line: 510,
          column: 12
        },
        end: {
          line: 515,
          column: 13
        }
      },
      "199": {
        start: {
          line: 511,
          column: 34
        },
        end: {
          line: 511,
          column: 66
        }
      },
      "200": {
        start: {
          line: 512,
          column: 16
        },
        end: {
          line: 514,
          column: 17
        }
      },
      "201": {
        start: {
          line: 513,
          column: 20
        },
        end: {
          line: 513,
          column: 33
        }
      },
      "202": {
        start: {
          line: 517,
          column: 8
        },
        end: {
          line: 517,
          column: 25
        }
      },
      "203": {
        start: {
          line: 521,
          column: 30
        },
        end: {
          line: 521,
          column: 50
        }
      },
      "204": {
        start: {
          line: 522,
          column: 32
        },
        end: {
          line: 522,
          column: 54
        }
      },
      "205": {
        start: {
          line: 523,
          column: 8
        },
        end: {
          line: 525,
          column: 9
        }
      },
      "206": {
        start: {
          line: 524,
          column: 12
        },
        end: {
          line: 524,
          column: 25
        }
      },
      "207": {
        start: {
          line: 526,
          column: 8
        },
        end: {
          line: 536,
          column: 9
        }
      },
      "208": {
        start: {
          line: 526,
          column: 21
        },
        end: {
          line: 526,
          column: 22
        }
      },
      "209": {
        start: {
          line: 527,
          column: 33
        },
        end: {
          line: 527,
          column: 49
        }
      },
      "210": {
        start: {
          line: 528,
          column: 35
        },
        end: {
          line: 528,
          column: 53
        }
      },
      "211": {
        start: {
          line: 529,
          column: 12
        },
        end: {
          line: 532,
          column: 13
        }
      },
      "212": {
        start: {
          line: 531,
          column: 16
        },
        end: {
          line: 531,
          column: 25
        }
      },
      "213": {
        start: {
          line: 533,
          column: 12
        },
        end: {
          line: 535,
          column: 13
        }
      },
      "214": {
        start: {
          line: 534,
          column: 16
        },
        end: {
          line: 534,
          column: 29
        }
      },
      "215": {
        start: {
          line: 537,
          column: 8
        },
        end: {
          line: 537,
          column: 20
        }
      },
      "216": {
        start: {
          line: 540,
          column: 8
        },
        end: {
          line: 541,
          column: 24
        }
      },
      "217": {
        start: {
          line: 541,
          column: 12
        },
        end: {
          line: 541,
          column: 24
        }
      },
      "218": {
        start: {
          line: 542,
          column: 20
        },
        end: {
          line: 542,
          column: 51
        }
      },
      "219": {
        start: {
          line: 543,
          column: 20
        },
        end: {
          line: 543,
          column: 30
        }
      },
      "220": {
        start: {
          line: 544,
          column: 24
        },
        end: {
          line: 544,
          column: 50
        }
      },
      "221": {
        start: {
          line: 545,
          column: 8
        },
        end: {
          line: 551,
          column: 9
        }
      },
      "222": {
        start: {
          line: 546,
          column: 12
        },
        end: {
          line: 549,
          column: 15
        }
      },
      "223": {
        start: {
          line: 550,
          column: 12
        },
        end: {
          line: 550,
          column: 24
        }
      },
      "224": {
        start: {
          line: 552,
          column: 8
        },
        end: {
          line: 554,
          column: 9
        }
      },
      "225": {
        start: {
          line: 553,
          column: 12
        },
        end: {
          line: 553,
          column: 25
        }
      },
      "226": {
        start: {
          line: 555,
          column: 8
        },
        end: {
          line: 555,
          column: 24
        }
      },
      "227": {
        start: {
          line: 556,
          column: 8
        },
        end: {
          line: 556,
          column: 20
        }
      },
      "228": {
        start: {
          line: 559,
          column: 27
        },
        end: {
          line: 559,
          column: 57
        }
      },
      "229": {
        start: {
          line: 560,
          column: 20
        },
        end: {
          line: 560,
          column: 90
        }
      },
      "230": {
        start: {
          line: 561,
          column: 31
        },
        end: {
          line: 567,
          column: 9
        }
      },
      "231": {
        start: {
          line: 568,
          column: 8
        },
        end: {
          line: 570,
          column: 9
        }
      },
      "232": {
        start: {
          line: 569,
          column: 12
        },
        end: {
          line: 569,
          column: 55
        }
      },
      "233": {
        start: {
          line: 572,
          column: 24
        },
        end: {
          line: 572,
          column: 46
        }
      },
      "234": {
        start: {
          line: 573,
          column: 27
        },
        end: {
          line: 573,
          column: 48
        }
      },
      "235": {
        start: {
          line: 574,
          column: 26
        },
        end: {
          line: 577,
          column: 19
        }
      },
      "236": {
        start: {
          line: 575,
          column: 12
        },
        end: {
          line: 575,
          column: 31
        }
      },
      "237": {
        start: {
          line: 576,
          column: 12
        },
        end: {
          line: 576,
          column: 36
        }
      },
      "238": {
        start: {
          line: 578,
          column: 8
        },
        end: {
          line: 578,
          column: 50
        }
      },
      "239": {
        start: {
          line: 579,
          column: 8
        },
        end: {
          line: 590,
          column: 9
        }
      },
      "240": {
        start: {
          line: 580,
          column: 29
        },
        end: {
          line: 580,
          column: 61
        }
      },
      "241": {
        start: {
          line: 581,
          column: 12
        },
        end: {
          line: 581,
          column: 36
        }
      },
      "242": {
        start: {
          line: 582,
          column: 12
        },
        end: {
          line: 584,
          column: 13
        }
      },
      "243": {
        start: {
          line: 583,
          column: 16
        },
        end: {
          line: 583,
          column: 92
        }
      },
      "244": {
        start: {
          line: 585,
          column: 12
        },
        end: {
          line: 585,
          column: 41
        }
      },
      "245": {
        start: {
          line: 588,
          column: 12
        },
        end: {
          line: 588,
          column: 36
        }
      },
      "246": {
        start: {
          line: 589,
          column: 12
        },
        end: {
          line: 589,
          column: 24
        }
      },
      "247": {
        start: {
          line: 593,
          column: 8
        },
        end: {
          line: 594,
          column: 56
        }
      },
      "248": {
        start: {
          line: 601,
          column: 8
        },
        end: {
          line: 609,
          column: 11
        }
      },
      "249": {
        start: {
          line: 610,
          column: 8
        },
        end: {
          line: 618,
          column: 11
        }
      },
      "250": {
        start: {
          line: 620,
          column: 8
        },
        end: {
          line: 626,
          column: 11
        }
      },
      "251": {
        start: {
          line: 627,
          column: 8
        },
        end: {
          line: 633,
          column: 11
        }
      },
      "252": {
        start: {
          line: 634,
          column: 8
        },
        end: {
          line: 640,
          column: 11
        }
      },
      "253": {
        start: {
          line: 641,
          column: 8
        },
        end: {
          line: 647,
          column: 11
        }
      },
      "254": {
        start: {
          line: 649,
          column: 8
        },
        end: {
          line: 656,
          column: 11
        }
      },
      "255": {
        start: {
          line: 662,
          column: 8
        },
        end: {
          line: 662,
          column: 35
        }
      },
      "256": {
        start: {
          line: 665,
          column: 8
        },
        end: {
          line: 665,
          column: 48
        }
      },
      "257": {
        start: {
          line: 668,
          column: 8
        },
        end: {
          line: 668,
          column: 36
        }
      },
      "258": {
        start: {
          line: 671,
          column: 0
        },
        end: {
          line: 671,
          column: 32
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 19,
            column: 4
          },
          end: {
            line: 19,
            column: 5
          }
        },
        loc: {
          start: {
            line: 21,
            column: 6
          },
          end: {
            line: 30,
            column: 5
          }
        },
        line: 21
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 31,
            column: 4
          },
          end: {
            line: 31,
            column: 5
          }
        },
        loc: {
          start: {
            line: 31,
            column: 29
          },
          end: {
            line: 47,
            column: 5
          }
        },
        line: 31
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 48,
            column: 4
          },
          end: {
            line: 48,
            column: 5
          }
        },
        loc: {
          start: {
            line: 48,
            column: 16
          },
          end: {
            line: 54,
            column: 5
          }
        },
        line: 48
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 55,
            column: 4
          },
          end: {
            line: 55,
            column: 5
          }
        },
        loc: {
          start: {
            line: 55,
            column: 16
          },
          end: {
            line: 62,
            column: 5
          }
        },
        line: 55
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 63,
            column: 4
          },
          end: {
            line: 63,
            column: 5
          }
        },
        loc: {
          start: {
            line: 63,
            column: 15
          },
          end: {
            line: 65,
            column: 5
          }
        },
        line: 63
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 66,
            column: 4
          },
          end: {
            line: 66,
            column: 5
          }
        },
        loc: {
          start: {
            line: 66,
            column: 17
          },
          end: {
            line: 73,
            column: 5
          }
        },
        line: 66
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 80,
            column: 4
          },
          end: {
            line: 80,
            column: 5
          }
        },
        loc: {
          start: {
            line: 80,
            column: 29
          },
          end: {
            line: 102,
            column: 5
          }
        },
        line: 80
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 106,
            column: 4
          },
          end: {
            line: 106,
            column: 5
          }
        },
        loc: {
          start: {
            line: 106,
            column: 29
          },
          end: {
            line: 117,
            column: 5
          }
        },
        line: 106
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 118,
            column: 4
          },
          end: {
            line: 118,
            column: 5
          }
        },
        loc: {
          start: {
            line: 118,
            column: 33
          },
          end: {
            line: 122,
            column: 5
          }
        },
        line: 118
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 123,
            column: 4
          },
          end: {
            line: 123,
            column: 5
          }
        },
        loc: {
          start: {
            line: 123,
            column: 40
          },
          end: {
            line: 134,
            column: 5
          }
        },
        line: 123
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 127,
            column: 39
          },
          end: {
            line: 127,
            column: 40
          }
        },
        loc: {
          start: {
            line: 127,
            column: 50
          },
          end: {
            line: 127,
            column: 78
          }
        },
        line: 127
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 130,
            column: 39
          },
          end: {
            line: 130,
            column: 40
          }
        },
        loc: {
          start: {
            line: 130,
            column: 50
          },
          end: {
            line: 130,
            column: 104
          }
        },
        line: 130
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 130,
            column: 61
          },
          end: {
            line: 130,
            column: 62
          }
        },
        loc: {
          start: {
            line: 130,
            column: 68
          },
          end: {
            line: 130,
            column: 103
          }
        },
        line: 130
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 133,
            column: 31
          },
          end: {
            line: 133,
            column: 32
          }
        },
        loc: {
          start: {
            line: 133,
            column: 42
          },
          end: {
            line: 133,
            column: 117
          }
        },
        line: 133
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 135,
            column: 4
          },
          end: {
            line: 135,
            column: 5
          }
        },
        loc: {
          start: {
            line: 135,
            column: 26
          },
          end: {
            line: 137,
            column: 5
          }
        },
        line: 135
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 141,
            column: 4
          },
          end: {
            line: 141,
            column: 5
          }
        },
        loc: {
          start: {
            line: 141,
            column: 37
          },
          end: {
            line: 158,
            column: 5
          }
        },
        line: 141
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 159,
            column: 4
          },
          end: {
            line: 159,
            column: 5
          }
        },
        loc: {
          start: {
            line: 159,
            column: 34
          },
          end: {
            line: 163,
            column: 5
          }
        },
        line: 159
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 164,
            column: 4
          },
          end: {
            line: 164,
            column: 5
          }
        },
        loc: {
          start: {
            line: 164,
            column: 40
          },
          end: {
            line: 167,
            column: 5
          }
        },
        line: 164
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 166,
            column: 31
          },
          end: {
            line: 166,
            column: 32
          }
        },
        loc: {
          start: {
            line: 166,
            column: 50
          },
          end: {
            line: 166,
            column: 121
          }
        },
        line: 166
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 168,
            column: 4
          },
          end: {
            line: 168,
            column: 5
          }
        },
        loc: {
          start: {
            line: 168,
            column: 32
          },
          end: {
            line: 187,
            column: 5
          }
        },
        line: 168
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 170,
            column: 37
          },
          end: {
            line: 170,
            column: 38
          }
        },
        loc: {
          start: {
            line: 170,
            column: 48
          },
          end: {
            line: 177,
            column: 9
          }
        },
        line: 170
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 178,
            column: 43
          },
          end: {
            line: 178,
            column: 44
          }
        },
        loc: {
          start: {
            line: 178,
            column: 60
          },
          end: {
            line: 178,
            column: 72
          }
        },
        line: 178
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 188,
            column: 4
          },
          end: {
            line: 188,
            column: 5
          }
        },
        loc: {
          start: {
            line: 188,
            column: 30
          },
          end: {
            line: 191,
            column: 5
          }
        },
        line: 188
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 195,
            column: 4
          },
          end: {
            line: 195,
            column: 5
          }
        },
        loc: {
          start: {
            line: 195,
            column: 44
          },
          end: {
            line: 221,
            column: 5
          }
        },
        line: 195
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 207,
            column: 56
          },
          end: {
            line: 207,
            column: 57
          }
        },
        loc: {
          start: {
            line: 207,
            column: 62
          },
          end: {
            line: 207,
            column: 80
          }
        },
        line: 207
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 225,
            column: 4
          },
          end: {
            line: 225,
            column: 5
          }
        },
        loc: {
          start: {
            line: 225,
            column: 26
          },
          end: {
            line: 231,
            column: 5
          }
        },
        line: 225
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 226,
            column: 47
          },
          end: {
            line: 226,
            column: 48
          }
        },
        loc: {
          start: {
            line: 226,
            column: 59
          },
          end: {
            line: 230,
            column: 9
          }
        },
        line: 226
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 232,
            column: 4
          },
          end: {
            line: 232,
            column: 5
          }
        },
        loc: {
          start: {
            line: 232,
            column: 49
          },
          end: {
            line: 257,
            column: 5
          }
        },
        line: 232
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 258,
            column: 4
          },
          end: {
            line: 258,
            column: 5
          }
        },
        loc: {
          start: {
            line: 258,
            column: 31
          },
          end: {
            line: 272,
            column: 5
          }
        },
        line: 258
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 273,
            column: 4
          },
          end: {
            line: 273,
            column: 5
          }
        },
        loc: {
          start: {
            line: 273,
            column: 58
          },
          end: {
            line: 296,
            column: 5
          }
        },
        line: 273
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 297,
            column: 4
          },
          end: {
            line: 297,
            column: 5
          }
        },
        loc: {
          start: {
            line: 297,
            column: 54
          },
          end: {
            line: 316,
            column: 5
          }
        },
        line: 297
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 306,
            column: 49
          },
          end: {
            line: 306,
            column: 50
          }
        },
        loc: {
          start: {
            line: 306,
            column: 58
          },
          end: {
            line: 306,
            column: 81
          }
        },
        line: 306
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 317,
            column: 4
          },
          end: {
            line: 317,
            column: 5
          }
        },
        loc: {
          start: {
            line: 317,
            column: 37
          },
          end: {
            line: 320,
            column: 5
          }
        },
        line: 317
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 324,
            column: 4
          },
          end: {
            line: 324,
            column: 5
          }
        },
        loc: {
          start: {
            line: 324,
            column: 27
          },
          end: {
            line: 413,
            column: 5
          }
        },
        line: 324
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 417,
            column: 4
          },
          end: {
            line: 417,
            column: 5
          }
        },
        loc: {
          start: {
            line: 417,
            column: 17
          },
          end: {
            line: 426,
            column: 5
          }
        },
        line: 417
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 422,
            column: 24
          },
          end: {
            line: 422,
            column: 25
          }
        },
        loc: {
          start: {
            line: 422,
            column: 29
          },
          end: {
            line: 422,
            column: 58
          }
        },
        line: 422
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 424,
            column: 21
          },
          end: {
            line: 424,
            column: 22
          }
        },
        loc: {
          start: {
            line: 424,
            column: 36
          },
          end: {
            line: 424,
            column: 73
          }
        },
        line: 424
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 427,
            column: 4
          },
          end: {
            line: 427,
            column: 5
          }
        },
        loc: {
          start: {
            line: 427,
            column: 21
          },
          end: {
            line: 429,
            column: 5
          }
        },
        line: 427
      },
      "38": {
        name: "(anonymous_38)",
        decl: {
          start: {
            line: 430,
            column: 4
          },
          end: {
            line: 430,
            column: 5
          }
        },
        loc: {
          start: {
            line: 430,
            column: 32
          },
          end: {
            line: 432,
            column: 5
          }
        },
        line: 430
      },
      "39": {
        name: "(anonymous_39)",
        decl: {
          start: {
            line: 433,
            column: 4
          },
          end: {
            line: 433,
            column: 5
          }
        },
        loc: {
          start: {
            line: 433,
            column: 14
          },
          end: {
            line: 437,
            column: 5
          }
        },
        line: 433
      },
      "40": {
        name: "(anonymous_40)",
        decl: {
          start: {
            line: 441,
            column: 4
          },
          end: {
            line: 441,
            column: 5
          }
        },
        loc: {
          start: {
            line: 441,
            column: 33
          },
          end: {
            line: 454,
            column: 5
          }
        },
        line: 441
      },
      "41": {
        name: "(anonymous_41)",
        decl: {
          start: {
            line: 458,
            column: 4
          },
          end: {
            line: 458,
            column: 5
          }
        },
        loc: {
          start: {
            line: 458,
            column: 20
          },
          end: {
            line: 461,
            column: 5
          }
        },
        line: 458
      },
      "42": {
        name: "(anonymous_42)",
        decl: {
          start: {
            line: 462,
            column: 4
          },
          end: {
            line: 462,
            column: 5
          }
        },
        loc: {
          start: {
            line: 462,
            column: 30
          },
          end: {
            line: 465,
            column: 5
          }
        },
        line: 462
      },
      "43": {
        name: "(anonymous_43)",
        decl: {
          start: {
            line: 469,
            column: 4
          },
          end: {
            line: 469,
            column: 5
          }
        },
        loc: {
          start: {
            line: 469,
            column: 52
          },
          end: {
            line: 501,
            column: 5
          }
        },
        line: 469
      },
      "44": {
        name: "(anonymous_44)",
        decl: {
          start: {
            line: 489,
            column: 78
          },
          end: {
            line: 489,
            column: 79
          }
        },
        loc: {
          start: {
            line: 489,
            column: 98
          },
          end: {
            line: 491,
            column: 13
          }
        },
        line: 489
      },
      "45": {
        name: "(anonymous_45)",
        decl: {
          start: {
            line: 502,
            column: 4
          },
          end: {
            line: 502,
            column: 5
          }
        },
        loc: {
          start: {
            line: 502,
            column: 28
          },
          end: {
            line: 518,
            column: 5
          }
        },
        line: 502
      },
      "46": {
        name: "(anonymous_46)",
        decl: {
          start: {
            line: 519,
            column: 4
          },
          end: {
            line: 519,
            column: 5
          }
        },
        loc: {
          start: {
            line: 519,
            column: 38
          },
          end: {
            line: 538,
            column: 5
          }
        },
        line: 519
      },
      "47": {
        name: "(anonymous_47)",
        decl: {
          start: {
            line: 539,
            column: 4
          },
          end: {
            line: 539,
            column: 5
          }
        },
        loc: {
          start: {
            line: 539,
            column: 26
          },
          end: {
            line: 557,
            column: 5
          }
        },
        line: 539
      },
      "48": {
        name: "(anonymous_48)",
        decl: {
          start: {
            line: 558,
            column: 4
          },
          end: {
            line: 558,
            column: 5
          }
        },
        loc: {
          start: {
            line: 558,
            column: 57
          },
          end: {
            line: 591,
            column: 5
          }
        },
        line: 558
      },
      "49": {
        name: "(anonymous_49)",
        decl: {
          start: {
            line: 574,
            column: 37
          },
          end: {
            line: 574,
            column: 38
          }
        },
        loc: {
          start: {
            line: 574,
            column: 43
          },
          end: {
            line: 577,
            column: 9
          }
        },
        line: 574
      },
      "50": {
        name: "(anonymous_50)",
        decl: {
          start: {
            line: 592,
            column: 4
          },
          end: {
            line: 592,
            column: 5
          }
        },
        loc: {
          start: {
            line: 592,
            column: 27
          },
          end: {
            line: 595,
            column: 5
          }
        },
        line: 592
      },
      "51": {
        name: "(anonymous_51)",
        decl: {
          start: {
            line: 599,
            column: 4
          },
          end: {
            line: 599,
            column: 5
          }
        },
        loc: {
          start: {
            line: 599,
            column: 25
          },
          end: {
            line: 657,
            column: 5
          }
        },
        line: 599
      },
      "52": {
        name: "(anonymous_52)",
        decl: {
          start: {
            line: 661,
            column: 4
          },
          end: {
            line: 661,
            column: 5
          }
        },
        loc: {
          start: {
            line: 661,
            column: 17
          },
          end: {
            line: 663,
            column: 5
          }
        },
        line: 661
      },
      "53": {
        name: "(anonymous_53)",
        decl: {
          start: {
            line: 664,
            column: 4
          },
          end: {
            line: 664,
            column: 5
          }
        },
        loc: {
          start: {
            line: 664,
            column: 16
          },
          end: {
            line: 666,
            column: 5
          }
        },
        line: 664
      },
      "54": {
        name: "(anonymous_54)",
        decl: {
          start: {
            line: 667,
            column: 4
          },
          end: {
            line: 667,
            column: 5
          }
        },
        loc: {
          start: {
            line: 667,
            column: 25
          },
          end: {
            line: 669,
            column: 5
          }
        },
        line: 667
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 19,
            column: 16
          },
          end: {
            line: 19,
            column: 29
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 19,
            column: 28
          },
          end: {
            line: 19,
            column: 29
          }
        }],
        line: 19
      },
      "1": {
        loc: {
          start: {
            line: 19,
            column: 31
          },
          end: {
            line: 19,
            column: 46
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 19,
            column: 41
          },
          end: {
            line: 19,
            column: 46
          }
        }],
        line: 19
      },
      "2": {
        loc: {
          start: {
            line: 20,
            column: 4
          },
          end: {
            line: 20,
            column: 28
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 20,
            column: 23
          },
          end: {
            line: 20,
            column: 28
          }
        }],
        line: 20
      },
      "3": {
        loc: {
          start: {
            line: 32,
            column: 8
          },
          end: {
            line: 37,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 32,
            column: 8
          },
          end: {
            line: 37,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 32
      },
      "4": {
        loc: {
          start: {
            line: 33,
            column: 12
          },
          end: {
            line: 35,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 33,
            column: 12
          },
          end: {
            line: 35,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 33
      },
      "5": {
        loc: {
          start: {
            line: 50,
            column: 8
          },
          end: {
            line: 52,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 50,
            column: 8
          },
          end: {
            line: 52,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 50
      },
      "6": {
        loc: {
          start: {
            line: 58,
            column: 8
          },
          end: {
            line: 61,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 58,
            column: 8
          },
          end: {
            line: 61,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 58
      },
      "7": {
        loc: {
          start: {
            line: 80,
            column: 16
          },
          end: {
            line: 80,
            column: 27
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 80,
            column: 25
          },
          end: {
            line: 80,
            column: 27
          }
        }],
        line: 80
      },
      "8": {
        loc: {
          start: {
            line: 126,
            column: 8
          },
          end: {
            line: 128,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 126,
            column: 8
          },
          end: {
            line: 128,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 126
      },
      "9": {
        loc: {
          start: {
            line: 129,
            column: 8
          },
          end: {
            line: 131,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 129,
            column: 8
          },
          end: {
            line: 131,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 129
      },
      "10": {
        loc: {
          start: {
            line: 129,
            column: 12
          },
          end: {
            line: 129,
            column: 35
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 129,
            column: 12
          },
          end: {
            line: 129,
            column: 16
          }
        }, {
          start: {
            line: 129,
            column: 20
          },
          end: {
            line: 129,
            column: 35
          }
        }],
        line: 129
      },
      "11": {
        loc: {
          start: {
            line: 133,
            column: 42
          },
          end: {
            line: 133,
            column: 117
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 133,
            column: 42
          },
          end: {
            line: 133,
            column: 77
          }
        }, {
          start: {
            line: 133,
            column: 81
          },
          end: {
            line: 133,
            column: 117
          }
        }],
        line: 133
      },
      "12": {
        loc: {
          start: {
            line: 143,
            column: 8
          },
          end: {
            line: 145,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 143,
            column: 8
          },
          end: {
            line: 145,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 143
      },
      "13": {
        loc: {
          start: {
            line: 146,
            column: 8
          },
          end: {
            line: 157,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 147,
            column: 12
          },
          end: {
            line: 148,
            column: 67
          }
        }, {
          start: {
            line: 149,
            column: 12
          },
          end: {
            line: 150,
            column: 73
          }
        }, {
          start: {
            line: 151,
            column: 12
          },
          end: {
            line: 152,
            column: 65
          }
        }, {
          start: {
            line: 153,
            column: 12
          },
          end: {
            line: 154,
            column: 63
          }
        }, {
          start: {
            line: 155,
            column: 12
          },
          end: {
            line: 156,
            column: 44
          }
        }],
        line: 146
      },
      "14": {
        loc: {
          start: {
            line: 166,
            column: 50
          },
          end: {
            line: 166,
            column: 121
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 166,
            column: 107
          },
          end: {
            line: 166,
            column: 114
          }
        }, {
          start: {
            line: 166,
            column: 117
          },
          end: {
            line: 166,
            column: 121
          }
        }],
        line: 166
      },
      "15": {
        loc: {
          start: {
            line: 172,
            column: 12
          },
          end: {
            line: 173,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 172,
            column: 12
          },
          end: {
            line: 173,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 172
      },
      "16": {
        loc: {
          start: {
            line: 174,
            column: 12
          },
          end: {
            line: 175,
            column: 30
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 174,
            column: 12
          },
          end: {
            line: 175,
            column: 30
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 174
      },
      "17": {
        loc: {
          start: {
            line: 182,
            column: 12
          },
          end: {
            line: 184,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 182,
            column: 12
          },
          end: {
            line: 184,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 182
      },
      "18": {
        loc: {
          start: {
            line: 197,
            column: 8
          },
          end: {
            line: 199,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 197,
            column: 8
          },
          end: {
            line: 199,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 197
      },
      "19": {
        loc: {
          start: {
            line: 201,
            column: 8
          },
          end: {
            line: 203,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 201,
            column: 8
          },
          end: {
            line: 203,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 201
      },
      "20": {
        loc: {
          start: {
            line: 216,
            column: 12
          },
          end: {
            line: 218,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 216,
            column: 12
          },
          end: {
            line: 218,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 216
      },
      "21": {
        loc: {
          start: {
            line: 240,
            column: 24
          },
          end: {
            line: 240,
            column: 50
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 240,
            column: 35
          },
          end: {
            line: 240,
            column: 41
          }
        }, {
          start: {
            line: 240,
            column: 44
          },
          end: {
            line: 240,
            column: 50
          }
        }],
        line: 240
      },
      "22": {
        loc: {
          start: {
            line: 241,
            column: 25
          },
          end: {
            line: 241,
            column: 83
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 241,
            column: 36
          },
          end: {
            line: 241,
            column: 56
          }
        }, {
          start: {
            line: 241,
            column: 59
          },
          end: {
            line: 241,
            column: 83
          }
        }],
        line: 241
      },
      "23": {
        loc: {
          start: {
            line: 251,
            column: 25
          },
          end: {
            line: 251,
            column: 81
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 251,
            column: 50
          },
          end: {
            line: 251,
            column: 63
          }
        }, {
          start: {
            line: 251,
            column: 66
          },
          end: {
            line: 251,
            column: 81
          }
        }],
        line: 251
      },
      "24": {
        loc: {
          start: {
            line: 262,
            column: 63
          },
          end: {
            line: 262,
            column: 88
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 262,
            column: 63
          },
          end: {
            line: 262,
            column: 75
          }
        }, {
          start: {
            line: 262,
            column: 79
          },
          end: {
            line: 262,
            column: 88
          }
        }],
        line: 262
      },
      "25": {
        loc: {
          start: {
            line: 275,
            column: 8
          },
          end: {
            line: 276,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 275,
            column: 8
          },
          end: {
            line: 276,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 275
      },
      "26": {
        loc: {
          start: {
            line: 280,
            column: 8
          },
          end: {
            line: 285,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 280,
            column: 8
          },
          end: {
            line: 285,
            column: 9
          }
        }, {
          start: {
            line: 283,
            column: 13
          },
          end: {
            line: 285,
            column: 9
          }
        }],
        line: 280
      },
      "27": {
        loc: {
          start: {
            line: 287,
            column: 8
          },
          end: {
            line: 295,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 287,
            column: 8
          },
          end: {
            line: 295,
            column: 9
          }
        }, {
          start: {
            line: 290,
            column: 13
          },
          end: {
            line: 295,
            column: 9
          }
        }],
        line: 287
      },
      "28": {
        loc: {
          start: {
            line: 290,
            column: 13
          },
          end: {
            line: 295,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 290,
            column: 13
          },
          end: {
            line: 295,
            column: 9
          }
        }, {
          start: {
            line: 293,
            column: 13
          },
          end: {
            line: 295,
            column: 9
          }
        }],
        line: 290
      },
      "29": {
        loc: {
          start: {
            line: 299,
            column: 8
          },
          end: {
            line: 300,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 299,
            column: 8
          },
          end: {
            line: 300,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 299
      },
      "30": {
        loc: {
          start: {
            line: 307,
            column: 8
          },
          end: {
            line: 315,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 307,
            column: 8
          },
          end: {
            line: 315,
            column: 9
          }
        }, {
          start: {
            line: 310,
            column: 13
          },
          end: {
            line: 315,
            column: 9
          }
        }],
        line: 307
      },
      "31": {
        loc: {
          start: {
            line: 310,
            column: 13
          },
          end: {
            line: 315,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 310,
            column: 13
          },
          end: {
            line: 315,
            column: 9
          }
        }, {
          start: {
            line: 313,
            column: 13
          },
          end: {
            line: 315,
            column: 9
          }
        }],
        line: 310
      },
      "32": {
        loc: {
          start: {
            line: 434,
            column: 8
          },
          end: {
            line: 436,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 434,
            column: 8
          },
          end: {
            line: 436,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 434
      },
      "33": {
        loc: {
          start: {
            line: 475,
            column: 12
          },
          end: {
            line: 477,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 475,
            column: 12
          },
          end: {
            line: 477,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 475
      },
      "34": {
        loc: {
          start: {
            line: 479,
            column: 12
          },
          end: {
            line: 482,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 479,
            column: 12
          },
          end: {
            line: 482,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 479
      },
      "35": {
        loc: {
          start: {
            line: 479,
            column: 16
          },
          end: {
            line: 479,
            column: 62
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 479,
            column: 16
          },
          end: {
            line: 479,
            column: 31
          }
        }, {
          start: {
            line: 479,
            column: 35
          },
          end: {
            line: 479,
            column: 62
          }
        }],
        line: 479
      },
      "36": {
        loc: {
          start: {
            line: 485,
            column: 12
          },
          end: {
            line: 487,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 485,
            column: 12
          },
          end: {
            line: 487,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 485
      },
      "37": {
        loc: {
          start: {
            line: 505,
            column: 8
          },
          end: {
            line: 507,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 505,
            column: 8
          },
          end: {
            line: 507,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 505
      },
      "38": {
        loc: {
          start: {
            line: 510,
            column: 12
          },
          end: {
            line: 515,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 510,
            column: 12
          },
          end: {
            line: 515,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 510
      },
      "39": {
        loc: {
          start: {
            line: 512,
            column: 16
          },
          end: {
            line: 514,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 512,
            column: 16
          },
          end: {
            line: 514,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 512
      },
      "40": {
        loc: {
          start: {
            line: 523,
            column: 8
          },
          end: {
            line: 525,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 523,
            column: 8
          },
          end: {
            line: 525,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 523
      },
      "41": {
        loc: {
          start: {
            line: 529,
            column: 12
          },
          end: {
            line: 532,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 529,
            column: 12
          },
          end: {
            line: 532,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 529
      },
      "42": {
        loc: {
          start: {
            line: 533,
            column: 12
          },
          end: {
            line: 535,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 533,
            column: 12
          },
          end: {
            line: 535,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 533
      },
      "43": {
        loc: {
          start: {
            line: 540,
            column: 8
          },
          end: {
            line: 541,
            column: 24
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 540,
            column: 8
          },
          end: {
            line: 541,
            column: 24
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 540
      },
      "44": {
        loc: {
          start: {
            line: 545,
            column: 8
          },
          end: {
            line: 551,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 545,
            column: 8
          },
          end: {
            line: 551,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 545
      },
      "45": {
        loc: {
          start: {
            line: 545,
            column: 12
          },
          end: {
            line: 545,
            column: 47
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 545,
            column: 12
          },
          end: {
            line: 545,
            column: 20
          }
        }, {
          start: {
            line: 545,
            column: 24
          },
          end: {
            line: 545,
            column: 47
          }
        }],
        line: 545
      },
      "46": {
        loc: {
          start: {
            line: 552,
            column: 8
          },
          end: {
            line: 554,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 552,
            column: 8
          },
          end: {
            line: 554,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 552
      },
      "47": {
        loc: {
          start: {
            line: 559,
            column: 27
          },
          end: {
            line: 559,
            column: 57
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 559,
            column: 27
          },
          end: {
            line: 559,
            column: 43
          }
        }, {
          start: {
            line: 559,
            column: 47
          },
          end: {
            line: 559,
            column: 57
          }
        }],
        line: 559
      },
      "48": {
        loc: {
          start: {
            line: 568,
            column: 8
          },
          end: {
            line: 570,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 568,
            column: 8
          },
          end: {
            line: 570,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 568
      },
      "49": {
        loc: {
          start: {
            line: 568,
            column: 12
          },
          end: {
            line: 568,
            column: 42
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 568,
            column: 12
          },
          end: {
            line: 568,
            column: 16
          }
        }, {
          start: {
            line: 568,
            column: 20
          },
          end: {
            line: 568,
            column: 42
          }
        }],
        line: 568
      },
      "50": {
        loc: {
          start: {
            line: 572,
            column: 24
          },
          end: {
            line: 572,
            column: 46
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 572,
            column: 24
          },
          end: {
            line: 572,
            column: 37
          }
        }, {
          start: {
            line: 572,
            column: 41
          },
          end: {
            line: 572,
            column: 46
          }
        }],
        line: 572
      },
      "51": {
        loc: {
          start: {
            line: 582,
            column: 12
          },
          end: {
            line: 584,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 582,
            column: 12
          },
          end: {
            line: 584,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 582
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0,
      "210": 0,
      "211": 0,
      "212": 0,
      "213": 0,
      "214": 0,
      "215": 0,
      "216": 0,
      "217": 0,
      "218": 0,
      "219": 0,
      "220": 0,
      "221": 0,
      "222": 0,
      "223": 0,
      "224": 0,
      "225": 0,
      "226": 0,
      "227": 0,
      "228": 0,
      "229": 0,
      "230": 0,
      "231": 0,
      "232": 0,
      "233": 0,
      "234": 0,
      "235": 0,
      "236": 0,
      "237": 0,
      "238": 0,
      "239": 0,
      "240": 0,
      "241": 0,
      "242": 0,
      "243": 0,
      "244": 0,
      "245": 0,
      "246": 0,
      "247": 0,
      "248": 0,
      "249": 0,
      "250": 0,
      "251": 0,
      "252": 0,
      "253": 0,
      "254": 0,
      "255": 0,
      "256": 0,
      "257": 0,
      "258": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0
    },
    b: {
      "0": [0],
      "1": [0],
      "2": [0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0, 0, 0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\frontend\\lib\\services\\ServiceRegistry.ts",
      mappings: ";AAAA;;;;;;;;;;GAUG;;;AA6DH,gFAAgF;AAChF,iCAAiC;AACjC,gFAAgF;AAEhF,MAAa,cAAc;IAOzB,YACU,YAAoB,CAAC,EACrB,UAAkB,KAAK,EAAE,WAAW;IACpC,mBAA2B,KAAK,CAAC,aAAa;;QAF9C,cAAS,GAAT,SAAS,CAAY;QACrB,YAAO,GAAP,OAAO,CAAgB;QACvB,qBAAgB,GAAhB,gBAAgB,CAAgB;QATlC,UAAK,GAAoC,QAAQ,CAAC;QAClD,iBAAY,GAAG,CAAC,CAAC;QACjB,oBAAe,GAAG,CAAC,CAAC;QACpB,iBAAY,GAAG,CAAC,CAAC;QACjB,gBAAW,GAAG,CAAC,CAAC;IAMrB,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAI,SAA2B;QAC1C,IAAI,IAAI,CAAC,KAAK,KAAK,MAAM,EAAE,CAAC;YAC1B,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;gBAClC,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;YAC7C,CAAC;YACD,IAAI,CAAC,KAAK,GAAG,WAAW,CAAC;QAC3B,CAAC;QAED,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE,CAAC;YACjC,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,SAAS;QACf,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;QACtB,IAAI,IAAI,CAAC,KAAK,KAAK,WAAW,EAAE,CAAC;YAC/B,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;QACxB,CAAC;QACD,IAAI,CAAC,YAAY,EAAE,CAAC;IACtB,CAAC;IAEO,SAAS;QACf,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAElC,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACxC,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC;YACpB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC;QAC/C,CAAC;IACH,CAAC;IAED,QAAQ;QACN,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAED,UAAU;QACR,OAAO;YACL,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,eAAe,EAAE,IAAI,CAAC,eAAe;SACtC,CAAC;IACJ,CAAC;CACF;AA7DD,wCA6DC;AAED,gFAAgF;AAChF,kCAAkC;AAClC,gFAAgF;AAEhF,MAAa,eAAe;IAa1B,YAAY,SAAyC,EAAE;QAZ/C,aAAQ,GAAG,IAAI,GAAG,EAA2B,CAAC;QAC9C,oBAAe,GAAG,IAAI,GAAG,EAA0B,CAAC;QAGpD,YAAO,GAAG;YAChB,aAAa,EAAE,CAAC;YAChB,kBAAkB,EAAE,CAAC;YACrB,cAAc,EAAE,CAAC;YACjB,mBAAmB,EAAE,CAAC;YACtB,mBAAmB,EAAE,CAAC;SACvB,CAAC;QAGA,IAAI,CAAC,MAAM,GAAG;YACZ,mBAAmB,EAAE,KAAK,EAAE,aAAa;YACzC,gBAAgB,EAAE,KAAK,EAAE,WAAW;YACpC,UAAU,EAAE,CAAC;YACb,uBAAuB,EAAE,CAAC;YAC1B,qBAAqB,EAAE,aAAa;YACpC,aAAa,EAAE,IAAI;YACnB,aAAa,EAAE,KAAK;YACpB,GAAG,MAAM;SACV,CAAC;QAEF,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC3B,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAC9B,CAAC;IAED,gFAAgF;IAChF,qCAAqC;IACrC,gFAAgF;IAEhF,eAAe,CAAC,OAAgE;QAC9E,MAAM,QAAQ,GAAoB;YAChC,GAAG,OAAO;YACV,YAAY,EAAE,IAAI,IAAI,EAAE;YACxB,aAAa,EAAE,IAAI,IAAI,EAAE;SAC1B,CAAC;QAEF,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;QACxC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,EAAE,IAAI,cAAc,CACrD,IAAI,CAAC,MAAM,CAAC,uBAAuB,EACnC,KAAK,EAAE,mBAAmB;QAC1B,KAAK,CAAE,uBAAuB;SAC/B,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,uBAAuB,OAAO,CAAC,IAAI,KAAK,OAAO,CAAC,EAAE,GAAG,CAAC,CAAC;IACrE,CAAC;IAED,iBAAiB,CAAC,SAAiB;QACjC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAChC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACvC,OAAO,CAAC,GAAG,CAAC,yBAAyB,SAAS,EAAE,CAAC,CAAC;IACpD,CAAC;IAED,gBAAgB,CAAC,WAAoB,EAAE,IAAe;QACpD,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;QAEvD,IAAI,QAAQ,GAAG,WAAW,CAAC;QAE3B,IAAI,WAAW,EAAE,CAAC;YAChB,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,KAAK,WAAW,CAAC,CAAC;QACtE,CAAC;QAED,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5B,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CACnC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CACvD,CAAC;QACJ,CAAC;QAED,gCAAgC;QAChC,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAC/B,OAAO,CAAC,MAAM,CAAC,MAAM,KAAK,SAAS,IAAI,OAAO,CAAC,MAAM,CAAC,MAAM,KAAK,UAAU,CAC5E,CAAC;IACJ,CAAC;IAED,UAAU,CAAC,SAAiB;QAC1B,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;IACtC,CAAC;IAED,gFAAgF;IAChF,uCAAuC;IACvC,gFAAgF;IAEhF,aAAa,CAAC,WAAmB,EAAE,IAAe;QAChD,MAAM,iBAAiB,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;QAEnE,IAAI,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACnC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,QAAQ,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE,CAAC;YAC1C,KAAK,aAAa;gBAChB,OAAO,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,CAAC;YACrD,KAAK,mBAAmB;gBACtB,OAAO,IAAI,CAAC,yBAAyB,CAAC,iBAAiB,CAAC,CAAC;YAC3D,KAAK,UAAU;gBACb,OAAO,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;YACnD,KAAK,QAAQ;gBACX,OAAO,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC;YACjD;gBACE,OAAO,iBAAiB,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC;IACH,CAAC;IAEO,mBAAmB,CAAC,QAA2B;QACrD,oCAAoC;QACpC,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC;QAC3D,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC;IACzB,CAAC;IAEO,yBAAyB,CAAC,QAA2B;QAC3D,oEAAoE;QACpE,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,OAAO,EAAE,EAAE,CACvC,OAAO,CAAC,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CACxE,CAAC;IACJ,CAAC;IAEO,iBAAiB,CAAC,QAA2B;QACnD,kDAAkD;QAClD,MAAM,OAAO,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;YACrC,IAAI,MAAM,GAAG,CAAC,CAAC;YACf,IAAI,OAAO,CAAC,MAAM,CAAC,MAAM,KAAK,SAAS;gBAAE,MAAM,IAAI,CAAC,CAAC;YACrD,IAAI,OAAO,CAAC,MAAM,CAAC,YAAY,GAAG,GAAG;gBAAE,MAAM,IAAI,GAAG,CAAC;YACrD,OAAO,MAAM,CAAC;QAChB,CAAC,CAAC,CAAC;QAEH,MAAM,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,MAAM,EAAE,CAAC,CAAC,CAAC;QACrE,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,WAAW,CAAC;QAEzC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACzC,MAAM,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;YACrB,IAAI,MAAM,IAAI,CAAC,EAAE,CAAC;gBAChB,OAAO,QAAQ,CAAC,CAAC,CAAC,CAAC;YACrB,CAAC;QACH,CAAC;QAED,OAAO,QAAQ,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC;IAEO,eAAe,CAAC,QAA2B;QACjD,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;QAC1D,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC;IACzB,CAAC;IAED,gFAAgF;IAChF,8BAA8B;IAC9B,gFAAgF;IAEhF,KAAK,CAAC,WAAW,CACf,SAAiB,EACjB,SAAoD;QAEpD,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC7C,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,sBAAsB,SAAS,EAAE,CAAC,CAAC;QACrD,CAAC;QAED,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC3D,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,0CAA0C,SAAS,EAAE,CAAC,CAAC;QACzE,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;QAC7B,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,cAAc,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;YAEtE,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;YAClC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC;YAChD,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC;YAElE,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;YAC9B,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC;YAEnE,IAAI,cAAc,CAAC,QAAQ,EAAE,KAAK,MAAM,EAAE,CAAC;gBACzC,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE,CAAC;YACrC,CAAC;YAED,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,gFAAgF;IAChF,oBAAoB;IACpB,gFAAgF;IAExE,mBAAmB;QACzB,IAAI,CAAC,mBAAmB,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;YAChD,KAAK,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACjD,MAAM,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YACpD,CAAC;QACH,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;IACtC,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,SAAiB,EAAE,OAAwB;QAC1E,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,oDAAoD;YACpD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YACjD,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAExC,MAAM,WAAW,GAAgB;gBAC/B,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM;gBAClC,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,wBAAwB;gBACnE,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,QAAQ;aACT,CAAC;YAEF,IAAI,CAAC,yBAAyB,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;QAEzD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,WAAW,GAAgB;gBAC/B,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;gBACjE,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;aACjC,CAAC;YAEF,IAAI,CAAC,yBAAyB,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,WAAW,CAAC,OAAwB;QAChD,iCAAiC;QACjC,mEAAmE;QACnE,IAAI,CAAC;YACH,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC,QAAQ,MAAM,OAAO,CAAC,GAAG,GAAG,OAAO,CAAC,IAAI,IAAI,SAAS,EAAE,CAAC;YAC/E,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,EAAE;gBAChC,MAAM,EAAE,KAAK;gBACb,OAAO,EAAE,IAAI;aACP,CAAC,CAAC;YACV,OAAO,QAAQ,CAAC,EAAE,CAAC;QACrB,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAEO,mBAAmB,CAAC,SAAiB,EAAE,OAAgB,EAAE,YAAoB;QACnF,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC7C,IAAI,CAAC,OAAO;YAAE,OAAO;QAErB,OAAO,CAAC,MAAM,CAAC,YAAY,GAAG,YAAY,CAAC;QAC3C,OAAO,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QACtC,OAAO,CAAC,aAAa,GAAG,IAAI,IAAI,EAAE,CAAC;QAEnC,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,MAAM,CAAC,SAAS,GAAG,GAAG,CAAC,CAAC;QACzE,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,MAAM,CAAC,SAAS,GAAG,GAAG,CAAC,CAAC;QACzE,CAAC;QAED,2CAA2C;QAC3C,IAAI,OAAO,CAAC,MAAM,CAAC,SAAS,GAAG,GAAG,EAAE,CAAC;YACnC,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,SAAS,CAAC;QACpC,CAAC;aAAM,IAAI,OAAO,CAAC,MAAM,CAAC,SAAS,GAAG,GAAG,EAAE,CAAC;YAC1C,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,UAAU,CAAC;QACrC,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,WAAW,CAAC;QACtC,CAAC;IACH,CAAC;IAEO,yBAAyB,CAAC,SAAiB,EAAE,WAAwB;QAC3E,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC7C,IAAI,CAAC,OAAO;YAAE,OAAO;QAErB,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,WAAW,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,sBAAsB;QACnG,OAAO,CAAC,MAAM,CAAC,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC;QACjD,OAAO,CAAC,aAAa,GAAG,IAAI,IAAI,EAAE,CAAC;QAEnC,+BAA+B;QAC/B,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACvD,MAAM,YAAY,GAAG,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC,MAAM,CAAC;QAElF,IAAI,YAAY,KAAK,CAAC,EAAE,CAAC;YACvB,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,SAAS,CAAC;QACpC,CAAC;aAAM,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;YAC5B,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,UAAU,CAAC;QACrC,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,WAAW,CAAC;QACtC,CAAC;IACH,CAAC;IAEO,kBAAkB,CAAC,YAAoB;QAC7C,IAAI,CAAC,OAAO,CAAC,mBAAmB;YAC9B,CAAC,IAAI,CAAC,OAAO,CAAC,mBAAmB,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;IAC1D,CAAC;IAED,gFAAgF;IAChF,6BAA6B;IAC7B,gFAAgF;IAExE,oBAAoB;QAC1B,gEAAgE;QAEhE,2BAA2B;QAC3B,IAAI,CAAC,eAAe,CAAC;YACnB,EAAE,EAAE,mBAAmB;YACvB,IAAI,EAAE,kBAAkB;YACxB,OAAO,EAAE,OAAO;YAChB,GAAG,EAAE,WAAW;YAChB,QAAQ,EAAE,MAAM;YAChB,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,mBAAmB;YACzB,MAAM,EAAE;gBACN,MAAM,EAAE,SAAS;gBACjB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,YAAY,EAAE,EAAE;gBAChB,SAAS,EAAE,CAAC;gBACZ,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;aACX;YACD,QAAQ,EAAE;gBACR,IAAI,EAAE,CAAC,aAAa,EAAE,MAAM,EAAE,MAAM,CAAC;gBACrC,WAAW,EAAE,aAAa;gBAC1B,YAAY,EAAE,CAAC,iBAAiB,EAAE,kBAAkB,EAAE,iBAAiB,CAAC;gBACxE,YAAY,EAAE,CAAC,kBAAkB,CAAC;gBAClC,SAAS,EAAE;oBACT,GAAG,EAAE,GAAG;oBACR,MAAM,EAAE,GAAG;oBACX,OAAO,EAAE,GAAG;iBACb;aACF;SACF,CAAC,CAAC;QAEH,6BAA6B;QAC7B,IAAI,CAAC,eAAe,CAAC;YACnB,EAAE,EAAE,sBAAsB;YAC1B,IAAI,EAAE,oBAAoB;YAC1B,OAAO,EAAE,OAAO;YAChB,GAAG,EAAE,WAAW;YAChB,QAAQ,EAAE,MAAM;YAChB,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,eAAe;YACrB,MAAM,EAAE;gBACN,MAAM,EAAE,SAAS;gBACjB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,YAAY,EAAE,EAAE;gBAChB,SAAS,EAAE,CAAC;gBACZ,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;aACX;YACD,QAAQ,EAAE;gBACR,IAAI,EAAE,CAAC,SAAS,EAAE,YAAY,EAAE,MAAM,CAAC;gBACvC,WAAW,EAAE,aAAa;gBAC1B,YAAY,EAAE,CAAC,cAAc,EAAE,eAAe,EAAE,iBAAiB,CAAC;gBAClE,YAAY,EAAE,CAAC,kBAAkB,EAAE,cAAc,CAAC;gBAClD,SAAS,EAAE;oBACT,GAAG,EAAE,GAAG;oBACR,MAAM,EAAE,GAAG;oBACX,OAAO,EAAE,GAAG;iBACb;aACF;SACF,CAAC,CAAC;QAEH,mBAAmB;QACnB,IAAI,CAAC,eAAe,CAAC;YACnB,EAAE,EAAE,kBAAkB;YACtB,IAAI,EAAE,UAAU;YAChB,OAAO,EAAE,OAAO;YAChB,GAAG,EAAE,WAAW;YAChB,QAAQ,EAAE,MAAM;YAChB,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,WAAW;YACjB,MAAM,EAAE;gBACN,MAAM,EAAE,SAAS;gBACjB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,YAAY,EAAE,EAAE;gBAChB,SAAS,EAAE,CAAC;gBACZ,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,EAAE;aACX;YACD,QAAQ,EAAE;gBACR,IAAI,EAAE,CAAC,UAAU,EAAE,SAAS,EAAE,gBAAgB,CAAC;gBAC/C,WAAW,EAAE,aAAa;gBAC1B,YAAY,EAAE,CAAC,YAAY,EAAE,SAAS,EAAE,SAAS,CAAC;gBAClD,YAAY,EAAE,EAAE;gBAChB,SAAS,EAAE;oBACT,GAAG,EAAE,GAAG;oBACR,MAAM,EAAE,IAAI;oBACZ,OAAO,EAAE,IAAI;iBACd;aACF;SACF,CAAC,CAAC;IACL,CAAC;IAED,gFAAgF;IAChF,aAAa;IACb,gFAAgF;IAEhF,UAAU;QACR,OAAO;YACL,GAAG,IAAI,CAAC,OAAO;YACf,kBAAkB,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI;YACtC,eAAe,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;iBAChD,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,MAAM;YACpD,eAAe,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;iBACxD,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;SAC9D,CAAC;IACJ,CAAC;IAED,cAAc;QACZ,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;IAC5C,CAAC;IAED,gBAAgB,CAAC,SAAiB;QAChC,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,MAAM,CAAC;IAC9C,CAAC;IAED,OAAO;QACL,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC7B,aAAa,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC;CACF;AAraD,0CAqaC;AA6BD,MAAa,UAAU;IAarB,YAAY,eAAgC;QAZpC,WAAM,GAAG,IAAI,GAAG,EAAuB,CAAC;QAExC,iBAAY,GAAG,IAAI,GAAG,EAAgD,CAAC;QACvE,YAAO,GAAmB;YAChC,aAAa,EAAE,CAAC;YAChB,kBAAkB,EAAE,CAAC;YACrB,cAAc,EAAE,CAAC;YACjB,cAAc,EAAE,CAAC;YACjB,mBAAmB,EAAE,CAAC;YACtB,QAAQ,EAAE,CAAC;SACZ,CAAC;QAGA,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAED,gFAAgF;IAChF,mBAAmB;IACnB,gFAAgF;IAEhF,QAAQ,CAAC,KAAkB;QACzB,MAAM,GAAG,GAAG,GAAG,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC;QAC5C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IAC9B,CAAC;IAED,WAAW,CAAC,MAAc,EAAE,IAAY;QACtC,MAAM,GAAG,GAAG,GAAG,MAAM,IAAI,IAAI,EAAE,CAAC;QAChC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IAC1B,CAAC;IAED,gFAAgF;IAChF,iCAAiC;IACjC,gFAAgF;IAEhF,KAAK,CAAC,YAAY,CAChB,MAAc,EACd,IAAY,EACZ,IAAU,EACV,OAAgC;QAEhC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,sBAAsB;YACtB,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YAC3C,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,MAAM,IAAI,KAAK,CAAC,oBAAoB,MAAM,IAAI,IAAI,EAAE,CAAC,CAAC;YACxD,CAAC;YAED,sBAAsB;YACtB,IAAI,KAAK,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,CAAC;gBACnD,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE,CAAC;gBACnC,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;YACzC,CAAC;YAED,0BAA0B;YAC1B,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;YACtE,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,IAAI,KAAK,CAAC,6BAA6B,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC;YACpE,CAAC;YAED,uCAAuC;YACvC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,WAAW,CACnD,OAAO,CAAC,EAAE,EACV,KAAK,EAAE,QAAQ,EAAE,EAAE;gBACjB,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;YACnE,CAAC,CACF,CAAC;YAEF,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;YAClC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC;YAE3C,OAAO,MAAM,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;YAC9B,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC;YAC3C,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,SAAS,CAAC,MAAc,EAAE,IAAY;QAC5C,MAAM,QAAQ,GAAG,GAAG,MAAM,IAAI,IAAI,EAAE,CAAC;QACrC,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAE7C,IAAI,UAAU,EAAE,CAAC;YACf,OAAO,UAAU,CAAC;QACpB,CAAC;QAED,sCAAsC;QACtC,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACvC,IAAI,GAAG,CAAC,UAAU,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE,CAAC;gBACjC,MAAM,SAAS,GAAG,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACnD,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,CAAC,EAAE,CAAC;oBACpC,OAAO,KAAK,CAAC;gBACf,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,SAAS,CAAC,SAAiB,EAAE,WAAmB;QACtD,4EAA4E;QAC5E,MAAM,aAAa,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC3C,MAAM,eAAe,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAE/C,IAAI,aAAa,CAAC,MAAM,KAAK,eAAe,CAAC,MAAM,EAAE,CAAC;YACpD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC9C,MAAM,YAAY,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,cAAc,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;YAE1C,IAAI,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;gBACjC,sCAAsC;gBACtC,SAAS;YACX,CAAC;YAED,IAAI,YAAY,KAAK,cAAc,EAAE,CAAC;gBACpC,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,cAAc,CAAC,KAAkB;QACvC,IAAI,CAAC,KAAK,CAAC,SAAS;YAAE,OAAO,IAAI,CAAC;QAElC,MAAM,GAAG,GAAG,GAAG,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC;QAC5C,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAE3C,IAAI,CAAC,OAAO,IAAI,GAAG,GAAG,OAAO,CAAC,SAAS,EAAE,CAAC;YACxC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,EAAE;gBACzB,KAAK,EAAE,CAAC;gBACR,SAAS,EAAE,GAAG,GAAG,KAAK,CAAC,SAAS,CAAC,MAAM;aACxC,CAAC,CAAC;YACH,OAAO,IAAI,CAAC;QACd,CAAC;QAED,IAAI,OAAO,CAAC,KAAK,IAAI,KAAK,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;YAC9C,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,CAAC,KAAK,EAAE,CAAC;QAChB,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,KAAK,CAAC,cAAc,CAC1B,QAAyB,EACzB,KAAkB,EAClB,IAAU,EACV,OAAgC;QAEhC,MAAM,UAAU,GAAG,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC,IAAI,CAAC;QAClD,MAAM,GAAG,GAAG,GAAG,QAAQ,CAAC,QAAQ,MAAM,QAAQ,CAAC,GAAG,IAAI,QAAQ,CAAC,IAAI,GAAG,UAAU,EAAE,CAAC;QAEnF,MAAM,cAAc,GAAgB;YAClC,MAAM,EAAE,KAAK,CAAC,MAAM;YACpB,OAAO,EAAE;gBACP,cAAc,EAAE,kBAAkB;gBAClC,GAAG,OAAO;aACX;SACF,CAAC;QAEF,IAAI,IAAI,IAAI,KAAK,CAAC,MAAM,KAAK,KAAK,EAAE,CAAC;YACnC,cAAc,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAC7C,CAAC;QAED,cAAc;QACd,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,CAAC,qBAAqB;QAC7D,MAAM,UAAU,GAAG,IAAI,eAAe,EAAE,CAAC;QACzC,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE;YAChC,UAAU,CAAC,KAAK,EAAE,CAAC;YACnB,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;QAC1B,CAAC,EAAE,OAAO,CAAC,CAAC;QAEZ,cAAc,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;QAE1C,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;YAClD,YAAY,CAAC,SAAS,CAAC,CAAC;YAExB,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACjB,MAAM,IAAI,KAAK,CAAC,kBAAkB,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC;YAC9E,CAAC;YAED,OAAO,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;QAC/B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,YAAY,CAAC,SAAS,CAAC,CAAC;YACxB,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,aAAa,CAAC,OAAe;QACnC,IAAI,CAAC,OAAO,CAAC,cAAc;YACzB,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;IAChD,CAAC;IAED,gFAAgF;IAChF,uBAAuB;IACvB,gFAAgF;IAExE,kBAAkB;QACxB,0BAA0B;QAC1B,IAAI,CAAC,QAAQ,CAAC;YACZ,IAAI,EAAE,4BAA4B;YAClC,MAAM,EAAE,MAAM;YACd,WAAW,EAAE,kBAAkB;YAC/B,UAAU,EAAE,4BAA4B;YACxC,cAAc,EAAE,IAAI;YACpB,OAAO,EAAE,KAAK;YACd,SAAS,EAAE,EAAE,QAAQ,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,0BAA0B;SACvE,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,CAAC;YACZ,IAAI,EAAE,wBAAwB;YAC9B,MAAM,EAAE,MAAM;YACd,WAAW,EAAE,kBAAkB;YAC/B,UAAU,EAAE,wBAAwB;YACpC,cAAc,EAAE,IAAI;YACpB,OAAO,EAAE,KAAK;YACd,SAAS,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE;SAC3C,CAAC,CAAC;QAEH,4BAA4B;QAC5B,IAAI,CAAC,QAAQ,CAAC;YACZ,IAAI,EAAE,eAAe;YACrB,MAAM,EAAE,KAAK;YACb,WAAW,EAAE,oBAAoB;YACjC,cAAc,EAAE,IAAI;YACpB,SAAS,EAAE,EAAE,QAAQ,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE;SAC5C,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,CAAC;YACZ,IAAI,EAAE,eAAe;YACrB,MAAM,EAAE,MAAM;YACd,WAAW,EAAE,oBAAoB;YACjC,cAAc,EAAE,IAAI;YACpB,SAAS,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE;SAC3C,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,CAAC;YACZ,IAAI,EAAE,mBAAmB;YACzB,MAAM,EAAE,KAAK;YACb,WAAW,EAAE,oBAAoB;YACjC,cAAc,EAAE,IAAI;YACpB,SAAS,EAAE,EAAE,QAAQ,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE;SAC5C,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,CAAC;YACZ,IAAI,EAAE,mBAAmB;YACzB,MAAM,EAAE,KAAK;YACb,WAAW,EAAE,oBAAoB;YACjC,cAAc,EAAE,IAAI;YACpB,SAAS,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE;SAC3C,CAAC,CAAC;QAEH,kBAAkB;QAClB,IAAI,CAAC,QAAQ,CAAC;YACZ,IAAI,EAAE,gBAAgB;YACtB,MAAM,EAAE,MAAM;YACd,WAAW,EAAE,UAAU;YACvB,cAAc,EAAE,IAAI;YACpB,OAAO,EAAE,KAAK;YACd,SAAS,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE;SAC3C,CAAC,CAAC;IACL,CAAC;IAED,gFAAgF;IAChF,aAAa;IACb,gFAAgF;IAEhF,UAAU;QACR,OAAO,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;IAC7B,CAAC;IAED,SAAS;QACP,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;IAC1C,CAAC;IAED,kBAAkB;QAChB,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B,CAAC;CACF;AAlSD,gCAkSC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\frontend\\lib\\services\\ServiceRegistry.ts"],
      sourcesContent: ["/**\r\n * Service Registry for SizeWise Suite\r\n * \r\n * Implements microservices preparation with:\r\n * - Service discovery and registration\r\n * - Health monitoring and status tracking\r\n * - Load balancing and failover\r\n * - Circuit breaker patterns\r\n * - API gateway preparation\r\n * - Service mesh readiness\r\n */\r\n\r\n// =============================================================================\r\n// Service Registry Types and Interfaces\r\n// =============================================================================\r\n\r\nexport interface ServiceEndpoint {\r\n  id: string;\r\n  name: string;\r\n  version: string;\r\n  url: string;\r\n  protocol: 'http' | 'https' | 'ws' | 'wss';\r\n  port?: number;\r\n  path?: string;\r\n  health: ServiceHealth;\r\n  metadata: ServiceMetadata;\r\n  lastHeartbeat: Date;\r\n  registeredAt: Date;\r\n}\r\n\r\nexport interface ServiceHealth {\r\n  status: 'healthy' | 'degraded' | 'unhealthy' | 'unknown';\r\n  lastCheck: Date;\r\n  responseTime: number;\r\n  errorRate: number;\r\n  uptime: number;\r\n  checks: HealthCheck[];\r\n}\r\n\r\nexport interface HealthCheck {\r\n  name: string;\r\n  status: 'pass' | 'fail' | 'warn';\r\n  message?: string;\r\n  timestamp: Date;\r\n  duration: number;\r\n}\r\n\r\nexport interface ServiceMetadata {\r\n  tags: string[];\r\n  environment: 'development' | 'staging' | 'production';\r\n  region?: string;\r\n  datacenter?: string;\r\n  capabilities: string[];\r\n  dependencies: string[];\r\n  resources: {\r\n    cpu: number;\r\n    memory: number;\r\n    storage: number;\r\n  };\r\n}\r\n\r\nexport interface ServiceRegistryConfig {\r\n  healthCheckInterval: number;\r\n  heartbeatTimeout: number;\r\n  maxRetries: number;\r\n  circuitBreakerThreshold: number;\r\n  loadBalancingStrategy: 'round-robin' | 'least-connections' | 'weighted' | 'random';\r\n  enableMetrics: boolean;\r\n  enableTracing: boolean;\r\n}\r\n\r\n// =============================================================================\r\n// Circuit Breaker Implementation\r\n// =============================================================================\r\n\r\nexport class CircuitBreaker {\r\n  private state: 'closed' | 'open' | 'half-open' = 'closed';\r\n  private failureCount = 0;\r\n  private lastFailureTime = 0;\r\n  private successCount = 0;\r\n  private nextAttempt = 0;\r\n\r\n  constructor(\r\n    private threshold: number = 5,\r\n    private timeout: number = 60000, // 1 minute\r\n    private monitoringPeriod: number = 10000 // 10 seconds\r\n  ) {}\r\n\r\n  async execute<T>(operation: () => Promise<T>): Promise<T> {\r\n    if (this.state === 'open') {\r\n      if (Date.now() < this.nextAttempt) {\r\n        throw new Error('Circuit breaker is OPEN');\r\n      }\r\n      this.state = 'half-open';\r\n    }\r\n\r\n    try {\r\n      const result = await operation();\r\n      this.onSuccess();\r\n      return result;\r\n    } catch (error) {\r\n      this.onFailure();\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  private onSuccess(): void {\r\n    this.failureCount = 0;\r\n    if (this.state === 'half-open') {\r\n      this.state = 'closed';\r\n    }\r\n    this.successCount++;\r\n  }\r\n\r\n  private onFailure(): void {\r\n    this.failureCount++;\r\n    this.lastFailureTime = Date.now();\r\n\r\n    if (this.failureCount >= this.threshold) {\r\n      this.state = 'open';\r\n      this.nextAttempt = Date.now() + this.timeout;\r\n    }\r\n  }\r\n\r\n  getState(): string {\r\n    return this.state;\r\n  }\r\n\r\n  getMetrics() {\r\n    return {\r\n      state: this.state,\r\n      failureCount: this.failureCount,\r\n      successCount: this.successCount,\r\n      lastFailureTime: this.lastFailureTime\r\n    };\r\n  }\r\n}\r\n\r\n// =============================================================================\r\n// Service Registry Implementation\r\n// =============================================================================\r\n\r\nexport class ServiceRegistry {\r\n  private services = new Map<string, ServiceEndpoint>();\r\n  private circuitBreakers = new Map<string, CircuitBreaker>();\r\n  private healthCheckInterval?: NodeJS.Timeout;\r\n  private config: ServiceRegistryConfig;\r\n  private metrics = {\r\n    totalRequests: 0,\r\n    successfulRequests: 0,\r\n    failedRequests: 0,\r\n    averageResponseTime: 0,\r\n    circuitBreakerTrips: 0\r\n  };\r\n\r\n  constructor(config: Partial<ServiceRegistryConfig> = {}) {\r\n    this.config = {\r\n      healthCheckInterval: 30000, // 30 seconds\r\n      heartbeatTimeout: 60000, // 1 minute\r\n      maxRetries: 3,\r\n      circuitBreakerThreshold: 5,\r\n      loadBalancingStrategy: 'round-robin',\r\n      enableMetrics: true,\r\n      enableTracing: false,\r\n      ...config\r\n    };\r\n\r\n    this.startHealthChecking();\r\n    this.registerCoreServices();\r\n  }\r\n\r\n  // =============================================================================\r\n  // Service Registration and Discovery\r\n  // =============================================================================\r\n\r\n  registerService(service: Omit<ServiceEndpoint, 'registeredAt' | 'lastHeartbeat'>): void {\r\n    const endpoint: ServiceEndpoint = {\r\n      ...service,\r\n      registeredAt: new Date(),\r\n      lastHeartbeat: new Date()\r\n    };\r\n\r\n    this.services.set(service.id, endpoint);\r\n    this.circuitBreakers.set(service.id, new CircuitBreaker(\r\n      this.config.circuitBreakerThreshold,\r\n      60000, // 1 minute timeout\r\n      10000  // 10 second monitoring\r\n    ));\r\n\r\n    console.log(`Service registered: ${service.name} (${service.id})`);\r\n  }\r\n\r\n  unregisterService(serviceId: string): void {\r\n    this.services.delete(serviceId);\r\n    this.circuitBreakers.delete(serviceId);\r\n    console.log(`Service unregistered: ${serviceId}`);\r\n  }\r\n\r\n  discoverServices(serviceName?: string, tags?: string[]): ServiceEndpoint[] {\r\n    const allServices = Array.from(this.services.values());\r\n    \r\n    let filtered = allServices;\r\n    \r\n    if (serviceName) {\r\n      filtered = filtered.filter(service => service.name === serviceName);\r\n    }\r\n    \r\n    if (tags && tags.length > 0) {\r\n      filtered = filtered.filter(service => \r\n        tags.every(tag => service.metadata.tags.includes(tag))\r\n      );\r\n    }\r\n    \r\n    // Filter out unhealthy services\r\n    return filtered.filter(service => \r\n      service.health.status === 'healthy' || service.health.status === 'degraded'\r\n    );\r\n  }\r\n\r\n  getService(serviceId: string): ServiceEndpoint | undefined {\r\n    return this.services.get(serviceId);\r\n  }\r\n\r\n  // =============================================================================\r\n  // Load Balancing and Service Selection\r\n  // =============================================================================\r\n\r\n  selectService(serviceName: string, tags?: string[]): ServiceEndpoint | null {\r\n    const availableServices = this.discoverServices(serviceName, tags);\r\n    \r\n    if (availableServices.length === 0) {\r\n      return null;\r\n    }\r\n\r\n    switch (this.config.loadBalancingStrategy) {\r\n      case 'round-robin':\r\n        return this.roundRobinSelection(availableServices);\r\n      case 'least-connections':\r\n        return this.leastConnectionsSelection(availableServices);\r\n      case 'weighted':\r\n        return this.weightedSelection(availableServices);\r\n      case 'random':\r\n        return this.randomSelection(availableServices);\r\n      default:\r\n        return availableServices[0];\r\n    }\r\n  }\r\n\r\n  private roundRobinSelection(services: ServiceEndpoint[]): ServiceEndpoint {\r\n    // Simple round-robin implementation\r\n    const index = this.metrics.totalRequests % services.length;\r\n    return services[index];\r\n  }\r\n\r\n  private leastConnectionsSelection(services: ServiceEndpoint[]): ServiceEndpoint {\r\n    // For now, select based on response time as a proxy for connections\r\n    return services.reduce((best, current) => \r\n      current.health.responseTime < best.health.responseTime ? current : best\r\n    );\r\n  }\r\n\r\n  private weightedSelection(services: ServiceEndpoint[]): ServiceEndpoint {\r\n    // Weight based on health status and response time\r\n    const weights = services.map(service => {\r\n      let weight = 1;\r\n      if (service.health.status === 'healthy') weight *= 2;\r\n      if (service.health.responseTime < 100) weight *= 1.5;\r\n      return weight;\r\n    });\r\n\r\n    const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);\r\n    let random = Math.random() * totalWeight;\r\n\r\n    for (let i = 0; i < services.length; i++) {\r\n      random -= weights[i];\r\n      if (random <= 0) {\r\n        return services[i];\r\n      }\r\n    }\r\n\r\n    return services[0];\r\n  }\r\n\r\n  private randomSelection(services: ServiceEndpoint[]): ServiceEndpoint {\r\n    const index = Math.floor(Math.random() * services.length);\r\n    return services[index];\r\n  }\r\n\r\n  // =============================================================================\r\n  // Circuit Breaker Integration\r\n  // =============================================================================\r\n\r\n  async callService<T>(\r\n    serviceId: string,\r\n    operation: (endpoint: ServiceEndpoint) => Promise<T>\r\n  ): Promise<T> {\r\n    const service = this.services.get(serviceId);\r\n    if (!service) {\r\n      throw new Error(`Service not found: ${serviceId}`);\r\n    }\r\n\r\n    const circuitBreaker = this.circuitBreakers.get(serviceId);\r\n    if (!circuitBreaker) {\r\n      throw new Error(`Circuit breaker not found for service: ${serviceId}`);\r\n    }\r\n\r\n    this.metrics.totalRequests++;\r\n    const startTime = Date.now();\r\n\r\n    try {\r\n      const result = await circuitBreaker.execute(() => operation(service));\r\n      \r\n      this.metrics.successfulRequests++;\r\n      this.updateResponseTime(Date.now() - startTime);\r\n      this.updateServiceHealth(serviceId, true, Date.now() - startTime);\r\n      \r\n      return result;\r\n    } catch (error) {\r\n      this.metrics.failedRequests++;\r\n      this.updateServiceHealth(serviceId, false, Date.now() - startTime);\r\n      \r\n      if (circuitBreaker.getState() === 'open') {\r\n        this.metrics.circuitBreakerTrips++;\r\n      }\r\n      \r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // =============================================================================\r\n  // Health Monitoring\r\n  // =============================================================================\r\n\r\n  private startHealthChecking(): void {\r\n    this.healthCheckInterval = setInterval(async () => {\r\n      for (const [serviceId, service] of this.services) {\r\n        await this.performHealthCheck(serviceId, service);\r\n      }\r\n    }, this.config.healthCheckInterval);\r\n  }\r\n\r\n  private async performHealthCheck(serviceId: string, service: ServiceEndpoint): Promise<void> {\r\n    const startTime = Date.now();\r\n    \r\n    try {\r\n      // Perform actual health check (simplified for demo)\r\n      const response = await this.pingService(service);\r\n      const duration = Date.now() - startTime;\r\n      \r\n      const healthCheck: HealthCheck = {\r\n        name: 'ping',\r\n        status: response ? 'pass' : 'fail',\r\n        message: response ? 'Service responding' : 'Service not responding',\r\n        timestamp: new Date(),\r\n        duration\r\n      };\r\n\r\n      this.updateServiceHealthStatus(serviceId, healthCheck);\r\n      \r\n    } catch (error) {\r\n      const healthCheck: HealthCheck = {\r\n        name: 'ping',\r\n        status: 'fail',\r\n        message: error instanceof Error ? error.message : 'Unknown error',\r\n        timestamp: new Date(),\r\n        duration: Date.now() - startTime\r\n      };\r\n\r\n      this.updateServiceHealthStatus(serviceId, healthCheck);\r\n    }\r\n  }\r\n\r\n  private async pingService(service: ServiceEndpoint): Promise<boolean> {\r\n    // Simplified ping implementation\r\n    // In a real implementation, this would make an actual HTTP request\r\n    try {\r\n      const url = `${service.protocol}://${service.url}${service.path || '/health'}`;\r\n      const response = await fetch(url, { \r\n        method: 'GET',\r\n        timeout: 5000 \r\n      } as any);\r\n      return response.ok;\r\n    } catch {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  private updateServiceHealth(serviceId: string, success: boolean, responseTime: number): void {\r\n    const service = this.services.get(serviceId);\r\n    if (!service) return;\r\n\r\n    service.health.responseTime = responseTime;\r\n    service.health.lastCheck = new Date();\r\n    service.lastHeartbeat = new Date();\r\n\r\n    if (success) {\r\n      service.health.errorRate = Math.max(0, service.health.errorRate - 0.1);\r\n    } else {\r\n      service.health.errorRate = Math.min(1, service.health.errorRate + 0.1);\r\n    }\r\n\r\n    // Update health status based on error rate\r\n    if (service.health.errorRate < 0.1) {\r\n      service.health.status = 'healthy';\r\n    } else if (service.health.errorRate < 0.5) {\r\n      service.health.status = 'degraded';\r\n    } else {\r\n      service.health.status = 'unhealthy';\r\n    }\r\n  }\r\n\r\n  private updateServiceHealthStatus(serviceId: string, healthCheck: HealthCheck): void {\r\n    const service = this.services.get(serviceId);\r\n    if (!service) return;\r\n\r\n    service.health.checks = [healthCheck, ...service.health.checks.slice(0, 9)]; // Keep last 10 checks\r\n    service.health.lastCheck = healthCheck.timestamp;\r\n    service.lastHeartbeat = new Date();\r\n\r\n    // Update overall health status\r\n    const recentChecks = service.health.checks.slice(0, 5);\r\n    const failedChecks = recentChecks.filter(check => check.status === 'fail').length;\r\n    \r\n    if (failedChecks === 0) {\r\n      service.health.status = 'healthy';\r\n    } else if (failedChecks < 3) {\r\n      service.health.status = 'degraded';\r\n    } else {\r\n      service.health.status = 'unhealthy';\r\n    }\r\n  }\r\n\r\n  private updateResponseTime(responseTime: number): void {\r\n    this.metrics.averageResponseTime = \r\n      (this.metrics.averageResponseTime + responseTime) / 2;\r\n  }\r\n\r\n  // =============================================================================\r\n  // Core Services Registration\r\n  // =============================================================================\r\n\r\n  private registerCoreServices(): void {\r\n    // Register core SizeWise services for microservices preparation\r\n    \r\n    // HVAC Calculation Service\r\n    this.registerService({\r\n      id: 'hvac-calc-service',\r\n      name: 'hvac-calculation',\r\n      version: '1.0.0',\r\n      url: 'localhost',\r\n      protocol: 'http',\r\n      port: 3001,\r\n      path: '/api/calculations',\r\n      health: {\r\n        status: 'healthy',\r\n        lastCheck: new Date(),\r\n        responseTime: 50,\r\n        errorRate: 0,\r\n        uptime: 100,\r\n        checks: []\r\n      },\r\n      metadata: {\r\n        tags: ['calculation', 'hvac', 'core'],\r\n        environment: 'development',\r\n        capabilities: ['air-duct-sizing', 'load-calculation', 'energy-analysis'],\r\n        dependencies: ['database-service'],\r\n        resources: {\r\n          cpu: 0.5,\r\n          memory: 512,\r\n          storage: 100\r\n        }\r\n      }\r\n    });\r\n\r\n    // Project Management Service\r\n    this.registerService({\r\n      id: 'project-mgmt-service',\r\n      name: 'project-management',\r\n      version: '1.0.0',\r\n      url: 'localhost',\r\n      protocol: 'http',\r\n      port: 3002,\r\n      path: '/api/projects',\r\n      health: {\r\n        status: 'healthy',\r\n        lastCheck: new Date(),\r\n        responseTime: 30,\r\n        errorRate: 0,\r\n        uptime: 100,\r\n        checks: []\r\n      },\r\n      metadata: {\r\n        tags: ['project', 'management', 'core'],\r\n        environment: 'development',\r\n        capabilities: ['project-crud', 'collaboration', 'version-control'],\r\n        dependencies: ['database-service', 'auth-service'],\r\n        resources: {\r\n          cpu: 0.3,\r\n          memory: 256,\r\n          storage: 500\r\n        }\r\n      }\r\n    });\r\n\r\n    // Database Service\r\n    this.registerService({\r\n      id: 'database-service',\r\n      name: 'database',\r\n      version: '1.0.0',\r\n      url: 'localhost',\r\n      protocol: 'http',\r\n      port: 3003,\r\n      path: '/api/data',\r\n      health: {\r\n        status: 'healthy',\r\n        lastCheck: new Date(),\r\n        responseTime: 20,\r\n        errorRate: 0,\r\n        uptime: 100,\r\n        checks: []\r\n      },\r\n      metadata: {\r\n        tags: ['database', 'storage', 'infrastructure'],\r\n        environment: 'development',\r\n        capabilities: ['postgresql', 'mongodb', 'caching'],\r\n        dependencies: [],\r\n        resources: {\r\n          cpu: 1.0,\r\n          memory: 1024,\r\n          storage: 2000\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  // =============================================================================\r\n  // Public API\r\n  // =============================================================================\r\n\r\n  getMetrics() {\r\n    return {\r\n      ...this.metrics,\r\n      registeredServices: this.services.size,\r\n      healthyServices: Array.from(this.services.values())\r\n        .filter(s => s.health.status === 'healthy').length,\r\n      circuitBreakers: Array.from(this.circuitBreakers.entries())\r\n        .map(([id, cb]) => ({ serviceId: id, ...cb.getMetrics() }))\r\n    };\r\n  }\r\n\r\n  getAllServices(): ServiceEndpoint[] {\r\n    return Array.from(this.services.values());\r\n  }\r\n\r\n  getServiceHealth(serviceId: string): ServiceHealth | undefined {\r\n    return this.services.get(serviceId)?.health;\r\n  }\r\n\r\n  destroy(): void {\r\n    if (this.healthCheckInterval) {\r\n      clearInterval(this.healthCheckInterval);\r\n    }\r\n  }\r\n}\r\n\r\n// =============================================================================\r\n// API Gateway for Microservices\r\n// =============================================================================\r\n\r\nexport interface RouteConfig {\r\n  path: string;\r\n  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';\r\n  serviceName: string;\r\n  targetPath?: string;\r\n  middleware?: string[];\r\n  rateLimit?: {\r\n    requests: number;\r\n    window: number; // in milliseconds\r\n  };\r\n  authentication?: boolean;\r\n  timeout?: number;\r\n}\r\n\r\nexport interface GatewayMetrics {\r\n  totalRequests: number;\r\n  successfulRequests: number;\r\n  failedRequests: number;\r\n  averageLatency: number;\r\n  rateLimitedRequests: number;\r\n  timeouts: number;\r\n}\r\n\r\nexport class APIGateway {\r\n  private routes = new Map<string, RouteConfig>();\r\n  private serviceRegistry: ServiceRegistry;\r\n  private rateLimiters = new Map<string, { count: number; resetTime: number }>();\r\n  private metrics: GatewayMetrics = {\r\n    totalRequests: 0,\r\n    successfulRequests: 0,\r\n    failedRequests: 0,\r\n    averageLatency: 0,\r\n    rateLimitedRequests: 0,\r\n    timeouts: 0\r\n  };\r\n\r\n  constructor(serviceRegistry: ServiceRegistry) {\r\n    this.serviceRegistry = serviceRegistry;\r\n    this.setupDefaultRoutes();\r\n  }\r\n\r\n  // =============================================================================\r\n  // Route Management\r\n  // =============================================================================\r\n\r\n  addRoute(route: RouteConfig): void {\r\n    const key = `${route.method}:${route.path}`;\r\n    this.routes.set(key, route);\r\n  }\r\n\r\n  removeRoute(method: string, path: string): void {\r\n    const key = `${method}:${path}`;\r\n    this.routes.delete(key);\r\n  }\r\n\r\n  // =============================================================================\r\n  // Request Routing and Processing\r\n  // =============================================================================\r\n\r\n  async routeRequest(\r\n    method: string,\r\n    path: string,\r\n    body?: any,\r\n    headers?: Record<string, string>\r\n  ): Promise<any> {\r\n    const startTime = Date.now();\r\n    this.metrics.totalRequests++;\r\n\r\n    try {\r\n      // Find matching route\r\n      const route = this.findRoute(method, path);\r\n      if (!route) {\r\n        throw new Error(`Route not found: ${method} ${path}`);\r\n      }\r\n\r\n      // Apply rate limiting\r\n      if (route.rateLimit && !this.checkRateLimit(route)) {\r\n        this.metrics.rateLimitedRequests++;\r\n        throw new Error('Rate limit exceeded');\r\n      }\r\n\r\n      // Select service instance\r\n      const service = this.serviceRegistry.selectService(route.serviceName);\r\n      if (!service) {\r\n        throw new Error(`No healthy service found: ${route.serviceName}`);\r\n      }\r\n\r\n      // Execute request with circuit breaker\r\n      const result = await this.serviceRegistry.callService(\r\n        service.id,\r\n        async (endpoint) => {\r\n          return await this.executeRequest(endpoint, route, body, headers);\r\n        }\r\n      );\r\n\r\n      this.metrics.successfulRequests++;\r\n      this.updateLatency(Date.now() - startTime);\r\n\r\n      return result;\r\n\r\n    } catch (error) {\r\n      this.metrics.failedRequests++;\r\n      this.updateLatency(Date.now() - startTime);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  private findRoute(method: string, path: string): RouteConfig | undefined {\r\n    const exactKey = `${method}:${path}`;\r\n    const exactMatch = this.routes.get(exactKey);\r\n\r\n    if (exactMatch) {\r\n      return exactMatch;\r\n    }\r\n\r\n    // Pattern matching for dynamic routes\r\n    for (const [key, route] of this.routes) {\r\n      if (key.startsWith(`${method}:`)) {\r\n        const routePath = key.substring(method.length + 1);\r\n        if (this.matchPath(routePath, path)) {\r\n          return route;\r\n        }\r\n      }\r\n    }\r\n\r\n    return undefined;\r\n  }\r\n\r\n  private matchPath(routePath: string, requestPath: string): boolean {\r\n    // Simple pattern matching - can be enhanced with more sophisticated routing\r\n    const routeSegments = routePath.split('/');\r\n    const requestSegments = requestPath.split('/');\r\n\r\n    if (routeSegments.length !== requestSegments.length) {\r\n      return false;\r\n    }\r\n\r\n    for (let i = 0; i < routeSegments.length; i++) {\r\n      const routeSegment = routeSegments[i];\r\n      const requestSegment = requestSegments[i];\r\n\r\n      if (routeSegment.startsWith(':')) {\r\n        // Dynamic segment - matches any value\r\n        continue;\r\n      }\r\n\r\n      if (routeSegment !== requestSegment) {\r\n        return false;\r\n      }\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  private checkRateLimit(route: RouteConfig): boolean {\r\n    if (!route.rateLimit) return true;\r\n\r\n    const key = `${route.method}:${route.path}`;\r\n    const now = Date.now();\r\n    const limiter = this.rateLimiters.get(key);\r\n\r\n    if (!limiter || now > limiter.resetTime) {\r\n      this.rateLimiters.set(key, {\r\n        count: 1,\r\n        resetTime: now + route.rateLimit.window\r\n      });\r\n      return true;\r\n    }\r\n\r\n    if (limiter.count >= route.rateLimit.requests) {\r\n      return false;\r\n    }\r\n\r\n    limiter.count++;\r\n    return true;\r\n  }\r\n\r\n  private async executeRequest(\r\n    endpoint: ServiceEndpoint,\r\n    route: RouteConfig,\r\n    body?: any,\r\n    headers?: Record<string, string>\r\n  ): Promise<any> {\r\n    const targetPath = route.targetPath || route.path;\r\n    const url = `${endpoint.protocol}://${endpoint.url}:${endpoint.port}${targetPath}`;\r\n\r\n    const requestOptions: RequestInit = {\r\n      method: route.method,\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        ...headers\r\n      }\r\n    };\r\n\r\n    if (body && route.method !== 'GET') {\r\n      requestOptions.body = JSON.stringify(body);\r\n    }\r\n\r\n    // Add timeout\r\n    const timeout = route.timeout || 30000; // 30 seconds default\r\n    const controller = new AbortController();\r\n    const timeoutId = setTimeout(() => {\r\n      controller.abort();\r\n      this.metrics.timeouts++;\r\n    }, timeout);\r\n\r\n    requestOptions.signal = controller.signal;\r\n\r\n    try {\r\n      const response = await fetch(url, requestOptions);\r\n      clearTimeout(timeoutId);\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`Service error: ${response.status} ${response.statusText}`);\r\n      }\r\n\r\n      return await response.json();\r\n    } catch (error) {\r\n      clearTimeout(timeoutId);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  private updateLatency(latency: number): void {\r\n    this.metrics.averageLatency =\r\n      (this.metrics.averageLatency + latency) / 2;\r\n  }\r\n\r\n  // =============================================================================\r\n  // Default Routes Setup\r\n  // =============================================================================\r\n\r\n  private setupDefaultRoutes(): void {\r\n    // HVAC Calculation routes\r\n    this.addRoute({\r\n      path: '/api/calculations/air-duct',\r\n      method: 'POST',\r\n      serviceName: 'hvac-calculation',\r\n      targetPath: '/api/calculations/air-duct',\r\n      authentication: true,\r\n      timeout: 10000,\r\n      rateLimit: { requests: 100, window: 60000 } // 100 requests per minute\r\n    });\r\n\r\n    this.addRoute({\r\n      path: '/api/calculations/load',\r\n      method: 'POST',\r\n      serviceName: 'hvac-calculation',\r\n      targetPath: '/api/calculations/load',\r\n      authentication: true,\r\n      timeout: 15000,\r\n      rateLimit: { requests: 50, window: 60000 }\r\n    });\r\n\r\n    // Project Management routes\r\n    this.addRoute({\r\n      path: '/api/projects',\r\n      method: 'GET',\r\n      serviceName: 'project-management',\r\n      authentication: true,\r\n      rateLimit: { requests: 200, window: 60000 }\r\n    });\r\n\r\n    this.addRoute({\r\n      path: '/api/projects',\r\n      method: 'POST',\r\n      serviceName: 'project-management',\r\n      authentication: true,\r\n      rateLimit: { requests: 20, window: 60000 }\r\n    });\r\n\r\n    this.addRoute({\r\n      path: '/api/projects/:id',\r\n      method: 'GET',\r\n      serviceName: 'project-management',\r\n      authentication: true,\r\n      rateLimit: { requests: 500, window: 60000 }\r\n    });\r\n\r\n    this.addRoute({\r\n      path: '/api/projects/:id',\r\n      method: 'PUT',\r\n      serviceName: 'project-management',\r\n      authentication: true,\r\n      rateLimit: { requests: 50, window: 60000 }\r\n    });\r\n\r\n    // Database routes\r\n    this.addRoute({\r\n      path: '/api/data/sync',\r\n      method: 'POST',\r\n      serviceName: 'database',\r\n      authentication: true,\r\n      timeout: 30000,\r\n      rateLimit: { requests: 10, window: 60000 }\r\n    });\r\n  }\r\n\r\n  // =============================================================================\r\n  // Public API\r\n  // =============================================================================\r\n\r\n  getMetrics(): GatewayMetrics {\r\n    return { ...this.metrics };\r\n  }\r\n\r\n  getRoutes(): RouteConfig[] {\r\n    return Array.from(this.routes.values());\r\n  }\r\n\r\n  getServiceRegistry(): ServiceRegistry {\r\n    return this.serviceRegistry;\r\n  }\r\n}\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "73bf91fab749dbedcfbc2654d933352995535bfb"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_lld5u1oeb = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_lld5u1oeb();
cov_lld5u1oeb().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_lld5u1oeb().s[1]++;
exports.APIGateway = exports.ServiceRegistry = exports.CircuitBreaker = void 0;
// =============================================================================
// Circuit Breaker Implementation
// =============================================================================
class CircuitBreaker {
  constructor(threshold =
  /* istanbul ignore next */
  (cov_lld5u1oeb().b[0][0]++, 5), timeout =
  /* istanbul ignore next */
  (cov_lld5u1oeb().b[1][0]++, 60000),
  // 1 minute
  monitoringPeriod =
  /* istanbul ignore next */
  (cov_lld5u1oeb().b[2][0]++, 10000) // 10 seconds
  ) {
    /* istanbul ignore next */
    cov_lld5u1oeb().f[0]++;
    cov_lld5u1oeb().s[2]++;
    this.threshold = threshold;
    /* istanbul ignore next */
    cov_lld5u1oeb().s[3]++;
    this.timeout = timeout;
    /* istanbul ignore next */
    cov_lld5u1oeb().s[4]++;
    this.monitoringPeriod = monitoringPeriod;
    /* istanbul ignore next */
    cov_lld5u1oeb().s[5]++;
    this.state = 'closed';
    /* istanbul ignore next */
    cov_lld5u1oeb().s[6]++;
    this.failureCount = 0;
    /* istanbul ignore next */
    cov_lld5u1oeb().s[7]++;
    this.lastFailureTime = 0;
    /* istanbul ignore next */
    cov_lld5u1oeb().s[8]++;
    this.successCount = 0;
    /* istanbul ignore next */
    cov_lld5u1oeb().s[9]++;
    this.nextAttempt = 0;
  }
  async execute(operation) {
    /* istanbul ignore next */
    cov_lld5u1oeb().f[1]++;
    cov_lld5u1oeb().s[10]++;
    if (this.state === 'open') {
      /* istanbul ignore next */
      cov_lld5u1oeb().b[3][0]++;
      cov_lld5u1oeb().s[11]++;
      if (Date.now() < this.nextAttempt) {
        /* istanbul ignore next */
        cov_lld5u1oeb().b[4][0]++;
        cov_lld5u1oeb().s[12]++;
        throw new Error('Circuit breaker is OPEN');
      } else
      /* istanbul ignore next */
      {
        cov_lld5u1oeb().b[4][1]++;
      }
      cov_lld5u1oeb().s[13]++;
      this.state = 'half-open';
    } else
    /* istanbul ignore next */
    {
      cov_lld5u1oeb().b[3][1]++;
    }
    cov_lld5u1oeb().s[14]++;
    try {
      const result =
      /* istanbul ignore next */
      (cov_lld5u1oeb().s[15]++, await operation());
      /* istanbul ignore next */
      cov_lld5u1oeb().s[16]++;
      this.onSuccess();
      /* istanbul ignore next */
      cov_lld5u1oeb().s[17]++;
      return result;
    } catch (error) {
      /* istanbul ignore next */
      cov_lld5u1oeb().s[18]++;
      this.onFailure();
      /* istanbul ignore next */
      cov_lld5u1oeb().s[19]++;
      throw error;
    }
  }
  onSuccess() {
    /* istanbul ignore next */
    cov_lld5u1oeb().f[2]++;
    cov_lld5u1oeb().s[20]++;
    this.failureCount = 0;
    /* istanbul ignore next */
    cov_lld5u1oeb().s[21]++;
    if (this.state === 'half-open') {
      /* istanbul ignore next */
      cov_lld5u1oeb().b[5][0]++;
      cov_lld5u1oeb().s[22]++;
      this.state = 'closed';
    } else
    /* istanbul ignore next */
    {
      cov_lld5u1oeb().b[5][1]++;
    }
    cov_lld5u1oeb().s[23]++;
    this.successCount++;
  }
  onFailure() {
    /* istanbul ignore next */
    cov_lld5u1oeb().f[3]++;
    cov_lld5u1oeb().s[24]++;
    this.failureCount++;
    /* istanbul ignore next */
    cov_lld5u1oeb().s[25]++;
    this.lastFailureTime = Date.now();
    /* istanbul ignore next */
    cov_lld5u1oeb().s[26]++;
    if (this.failureCount >= this.threshold) {
      /* istanbul ignore next */
      cov_lld5u1oeb().b[6][0]++;
      cov_lld5u1oeb().s[27]++;
      this.state = 'open';
      /* istanbul ignore next */
      cov_lld5u1oeb().s[28]++;
      this.nextAttempt = Date.now() + this.timeout;
    } else
    /* istanbul ignore next */
    {
      cov_lld5u1oeb().b[6][1]++;
    }
  }
  getState() {
    /* istanbul ignore next */
    cov_lld5u1oeb().f[4]++;
    cov_lld5u1oeb().s[29]++;
    return this.state;
  }
  getMetrics() {
    /* istanbul ignore next */
    cov_lld5u1oeb().f[5]++;
    cov_lld5u1oeb().s[30]++;
    return {
      state: this.state,
      failureCount: this.failureCount,
      successCount: this.successCount,
      lastFailureTime: this.lastFailureTime
    };
  }
}
/* istanbul ignore next */
cov_lld5u1oeb().s[31]++;
exports.CircuitBreaker = CircuitBreaker;
// =============================================================================
// Service Registry Implementation
// =============================================================================
class ServiceRegistry {
  constructor(config =
  /* istanbul ignore next */
  (cov_lld5u1oeb().b[7][0]++, {})) {
    /* istanbul ignore next */
    cov_lld5u1oeb().f[6]++;
    cov_lld5u1oeb().s[32]++;
    this.services = new Map();
    /* istanbul ignore next */
    cov_lld5u1oeb().s[33]++;
    this.circuitBreakers = new Map();
    /* istanbul ignore next */
    cov_lld5u1oeb().s[34]++;
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      circuitBreakerTrips: 0
    };
    /* istanbul ignore next */
    cov_lld5u1oeb().s[35]++;
    this.config = {
      healthCheckInterval: 30000,
      // 30 seconds
      heartbeatTimeout: 60000,
      // 1 minute
      maxRetries: 3,
      circuitBreakerThreshold: 5,
      loadBalancingStrategy: 'round-robin',
      enableMetrics: true,
      enableTracing: false,
      ...config
    };
    /* istanbul ignore next */
    cov_lld5u1oeb().s[36]++;
    this.startHealthChecking();
    /* istanbul ignore next */
    cov_lld5u1oeb().s[37]++;
    this.registerCoreServices();
  }
  // =============================================================================
  // Service Registration and Discovery
  // =============================================================================
  registerService(service) {
    /* istanbul ignore next */
    cov_lld5u1oeb().f[7]++;
    const endpoint =
    /* istanbul ignore next */
    (cov_lld5u1oeb().s[38]++, {
      ...service,
      registeredAt: new Date(),
      lastHeartbeat: new Date()
    });
    /* istanbul ignore next */
    cov_lld5u1oeb().s[39]++;
    this.services.set(service.id, endpoint);
    /* istanbul ignore next */
    cov_lld5u1oeb().s[40]++;
    this.circuitBreakers.set(service.id, new CircuitBreaker(this.config.circuitBreakerThreshold, 60000,
    // 1 minute timeout
    10000 // 10 second monitoring
    ));
    /* istanbul ignore next */
    cov_lld5u1oeb().s[41]++;
    console.log(`Service registered: ${service.name} (${service.id})`);
  }
  unregisterService(serviceId) {
    /* istanbul ignore next */
    cov_lld5u1oeb().f[8]++;
    cov_lld5u1oeb().s[42]++;
    this.services.delete(serviceId);
    /* istanbul ignore next */
    cov_lld5u1oeb().s[43]++;
    this.circuitBreakers.delete(serviceId);
    /* istanbul ignore next */
    cov_lld5u1oeb().s[44]++;
    console.log(`Service unregistered: ${serviceId}`);
  }
  discoverServices(serviceName, tags) {
    /* istanbul ignore next */
    cov_lld5u1oeb().f[9]++;
    const allServices =
    /* istanbul ignore next */
    (cov_lld5u1oeb().s[45]++, Array.from(this.services.values()));
    let filtered =
    /* istanbul ignore next */
    (cov_lld5u1oeb().s[46]++, allServices);
    /* istanbul ignore next */
    cov_lld5u1oeb().s[47]++;
    if (serviceName) {
      /* istanbul ignore next */
      cov_lld5u1oeb().b[8][0]++;
      cov_lld5u1oeb().s[48]++;
      filtered = filtered.filter(service => {
        /* istanbul ignore next */
        cov_lld5u1oeb().f[10]++;
        cov_lld5u1oeb().s[49]++;
        return service.name === serviceName;
      });
    } else
    /* istanbul ignore next */
    {
      cov_lld5u1oeb().b[8][1]++;
    }
    cov_lld5u1oeb().s[50]++;
    if (
    /* istanbul ignore next */
    (cov_lld5u1oeb().b[10][0]++, tags) &&
    /* istanbul ignore next */
    (cov_lld5u1oeb().b[10][1]++, tags.length > 0)) {
      /* istanbul ignore next */
      cov_lld5u1oeb().b[9][0]++;
      cov_lld5u1oeb().s[51]++;
      filtered = filtered.filter(service => {
        /* istanbul ignore next */
        cov_lld5u1oeb().f[11]++;
        cov_lld5u1oeb().s[52]++;
        return tags.every(tag => {
          /* istanbul ignore next */
          cov_lld5u1oeb().f[12]++;
          cov_lld5u1oeb().s[53]++;
          return service.metadata.tags.includes(tag);
        });
      });
    } else
    /* istanbul ignore next */
    {
      cov_lld5u1oeb().b[9][1]++;
    }
    // Filter out unhealthy services
    cov_lld5u1oeb().s[54]++;
    return filtered.filter(service => {
      /* istanbul ignore next */
      cov_lld5u1oeb().f[13]++;
      cov_lld5u1oeb().s[55]++;
      return /* istanbul ignore next */(cov_lld5u1oeb().b[11][0]++, service.health.status === 'healthy') ||
      /* istanbul ignore next */
      (cov_lld5u1oeb().b[11][1]++, service.health.status === 'degraded');
    });
  }
  getService(serviceId) {
    /* istanbul ignore next */
    cov_lld5u1oeb().f[14]++;
    cov_lld5u1oeb().s[56]++;
    return this.services.get(serviceId);
  }
  // =============================================================================
  // Load Balancing and Service Selection
  // =============================================================================
  selectService(serviceName, tags) {
    /* istanbul ignore next */
    cov_lld5u1oeb().f[15]++;
    const availableServices =
    /* istanbul ignore next */
    (cov_lld5u1oeb().s[57]++, this.discoverServices(serviceName, tags));
    /* istanbul ignore next */
    cov_lld5u1oeb().s[58]++;
    if (availableServices.length === 0) {
      /* istanbul ignore next */
      cov_lld5u1oeb().b[12][0]++;
      cov_lld5u1oeb().s[59]++;
      return null;
    } else
    /* istanbul ignore next */
    {
      cov_lld5u1oeb().b[12][1]++;
    }
    cov_lld5u1oeb().s[60]++;
    switch (this.config.loadBalancingStrategy) {
      case 'round-robin':
        /* istanbul ignore next */
        cov_lld5u1oeb().b[13][0]++;
        cov_lld5u1oeb().s[61]++;
        return this.roundRobinSelection(availableServices);
      case 'least-connections':
        /* istanbul ignore next */
        cov_lld5u1oeb().b[13][1]++;
        cov_lld5u1oeb().s[62]++;
        return this.leastConnectionsSelection(availableServices);
      case 'weighted':
        /* istanbul ignore next */
        cov_lld5u1oeb().b[13][2]++;
        cov_lld5u1oeb().s[63]++;
        return this.weightedSelection(availableServices);
      case 'random':
        /* istanbul ignore next */
        cov_lld5u1oeb().b[13][3]++;
        cov_lld5u1oeb().s[64]++;
        return this.randomSelection(availableServices);
      default:
        /* istanbul ignore next */
        cov_lld5u1oeb().b[13][4]++;
        cov_lld5u1oeb().s[65]++;
        return availableServices[0];
    }
  }
  roundRobinSelection(services) {
    /* istanbul ignore next */
    cov_lld5u1oeb().f[16]++;
    // Simple round-robin implementation
    const index =
    /* istanbul ignore next */
    (cov_lld5u1oeb().s[66]++, this.metrics.totalRequests % services.length);
    /* istanbul ignore next */
    cov_lld5u1oeb().s[67]++;
    return services[index];
  }
  leastConnectionsSelection(services) {
    /* istanbul ignore next */
    cov_lld5u1oeb().f[17]++;
    cov_lld5u1oeb().s[68]++;
    // For now, select based on response time as a proxy for connections
    return services.reduce((best, current) => {
      /* istanbul ignore next */
      cov_lld5u1oeb().f[18]++;
      cov_lld5u1oeb().s[69]++;
      return current.health.responseTime < best.health.responseTime ?
      /* istanbul ignore next */
      (cov_lld5u1oeb().b[14][0]++, current) :
      /* istanbul ignore next */
      (cov_lld5u1oeb().b[14][1]++, best);
    });
  }
  weightedSelection(services) {
    /* istanbul ignore next */
    cov_lld5u1oeb().f[19]++;
    // Weight based on health status and response time
    const weights =
    /* istanbul ignore next */
    (cov_lld5u1oeb().s[70]++, services.map(service => {
      /* istanbul ignore next */
      cov_lld5u1oeb().f[20]++;
      let weight =
      /* istanbul ignore next */
      (cov_lld5u1oeb().s[71]++, 1);
      /* istanbul ignore next */
      cov_lld5u1oeb().s[72]++;
      if (service.health.status === 'healthy') {
        /* istanbul ignore next */
        cov_lld5u1oeb().b[15][0]++;
        cov_lld5u1oeb().s[73]++;
        weight *= 2;
      } else
      /* istanbul ignore next */
      {
        cov_lld5u1oeb().b[15][1]++;
      }
      cov_lld5u1oeb().s[74]++;
      if (service.health.responseTime < 100) {
        /* istanbul ignore next */
        cov_lld5u1oeb().b[16][0]++;
        cov_lld5u1oeb().s[75]++;
        weight *= 1.5;
      } else
      /* istanbul ignore next */
      {
        cov_lld5u1oeb().b[16][1]++;
      }
      cov_lld5u1oeb().s[76]++;
      return weight;
    }));
    const totalWeight =
    /* istanbul ignore next */
    (cov_lld5u1oeb().s[77]++, weights.reduce((sum, weight) => {
      /* istanbul ignore next */
      cov_lld5u1oeb().f[21]++;
      cov_lld5u1oeb().s[78]++;
      return sum + weight;
    }, 0));
    let random =
    /* istanbul ignore next */
    (cov_lld5u1oeb().s[79]++, Math.random() * totalWeight);
    /* istanbul ignore next */
    cov_lld5u1oeb().s[80]++;
    for (let i =
    /* istanbul ignore next */
    (cov_lld5u1oeb().s[81]++, 0); i < services.length; i++) {
      /* istanbul ignore next */
      cov_lld5u1oeb().s[82]++;
      random -= weights[i];
      /* istanbul ignore next */
      cov_lld5u1oeb().s[83]++;
      if (random <= 0) {
        /* istanbul ignore next */
        cov_lld5u1oeb().b[17][0]++;
        cov_lld5u1oeb().s[84]++;
        return services[i];
      } else
      /* istanbul ignore next */
      {
        cov_lld5u1oeb().b[17][1]++;
      }
    }
    /* istanbul ignore next */
    cov_lld5u1oeb().s[85]++;
    return services[0];
  }
  randomSelection(services) {
    /* istanbul ignore next */
    cov_lld5u1oeb().f[22]++;
    const index =
    /* istanbul ignore next */
    (cov_lld5u1oeb().s[86]++, Math.floor(Math.random() * services.length));
    /* istanbul ignore next */
    cov_lld5u1oeb().s[87]++;
    return services[index];
  }
  // =============================================================================
  // Circuit Breaker Integration
  // =============================================================================
  async callService(serviceId, operation) {
    /* istanbul ignore next */
    cov_lld5u1oeb().f[23]++;
    const service =
    /* istanbul ignore next */
    (cov_lld5u1oeb().s[88]++, this.services.get(serviceId));
    /* istanbul ignore next */
    cov_lld5u1oeb().s[89]++;
    if (!service) {
      /* istanbul ignore next */
      cov_lld5u1oeb().b[18][0]++;
      cov_lld5u1oeb().s[90]++;
      throw new Error(`Service not found: ${serviceId}`);
    } else
    /* istanbul ignore next */
    {
      cov_lld5u1oeb().b[18][1]++;
    }
    const circuitBreaker =
    /* istanbul ignore next */
    (cov_lld5u1oeb().s[91]++, this.circuitBreakers.get(serviceId));
    /* istanbul ignore next */
    cov_lld5u1oeb().s[92]++;
    if (!circuitBreaker) {
      /* istanbul ignore next */
      cov_lld5u1oeb().b[19][0]++;
      cov_lld5u1oeb().s[93]++;
      throw new Error(`Circuit breaker not found for service: ${serviceId}`);
    } else
    /* istanbul ignore next */
    {
      cov_lld5u1oeb().b[19][1]++;
    }
    cov_lld5u1oeb().s[94]++;
    this.metrics.totalRequests++;
    const startTime =
    /* istanbul ignore next */
    (cov_lld5u1oeb().s[95]++, Date.now());
    /* istanbul ignore next */
    cov_lld5u1oeb().s[96]++;
    try {
      const result =
      /* istanbul ignore next */
      (cov_lld5u1oeb().s[97]++, await circuitBreaker.execute(() => {
        /* istanbul ignore next */
        cov_lld5u1oeb().f[24]++;
        cov_lld5u1oeb().s[98]++;
        return operation(service);
      }));
      /* istanbul ignore next */
      cov_lld5u1oeb().s[99]++;
      this.metrics.successfulRequests++;
      /* istanbul ignore next */
      cov_lld5u1oeb().s[100]++;
      this.updateResponseTime(Date.now() - startTime);
      /* istanbul ignore next */
      cov_lld5u1oeb().s[101]++;
      this.updateServiceHealth(serviceId, true, Date.now() - startTime);
      /* istanbul ignore next */
      cov_lld5u1oeb().s[102]++;
      return result;
    } catch (error) {
      /* istanbul ignore next */
      cov_lld5u1oeb().s[103]++;
      this.metrics.failedRequests++;
      /* istanbul ignore next */
      cov_lld5u1oeb().s[104]++;
      this.updateServiceHealth(serviceId, false, Date.now() - startTime);
      /* istanbul ignore next */
      cov_lld5u1oeb().s[105]++;
      if (circuitBreaker.getState() === 'open') {
        /* istanbul ignore next */
        cov_lld5u1oeb().b[20][0]++;
        cov_lld5u1oeb().s[106]++;
        this.metrics.circuitBreakerTrips++;
      } else
      /* istanbul ignore next */
      {
        cov_lld5u1oeb().b[20][1]++;
      }
      cov_lld5u1oeb().s[107]++;
      throw error;
    }
  }
  // =============================================================================
  // Health Monitoring
  // =============================================================================
  startHealthChecking() {
    /* istanbul ignore next */
    cov_lld5u1oeb().f[25]++;
    cov_lld5u1oeb().s[108]++;
    this.healthCheckInterval = setInterval(async () => {
      /* istanbul ignore next */
      cov_lld5u1oeb().f[26]++;
      cov_lld5u1oeb().s[109]++;
      for (const [serviceId, service] of this.services) {
        /* istanbul ignore next */
        cov_lld5u1oeb().s[110]++;
        await this.performHealthCheck(serviceId, service);
      }
    }, this.config.healthCheckInterval);
  }
  async performHealthCheck(serviceId, service) {
    /* istanbul ignore next */
    cov_lld5u1oeb().f[27]++;
    const startTime =
    /* istanbul ignore next */
    (cov_lld5u1oeb().s[111]++, Date.now());
    /* istanbul ignore next */
    cov_lld5u1oeb().s[112]++;
    try {
      // Perform actual health check (simplified for demo)
      const response =
      /* istanbul ignore next */
      (cov_lld5u1oeb().s[113]++, await this.pingService(service));
      const duration =
      /* istanbul ignore next */
      (cov_lld5u1oeb().s[114]++, Date.now() - startTime);
      const healthCheck =
      /* istanbul ignore next */
      (cov_lld5u1oeb().s[115]++, {
        name: 'ping',
        status: response ?
        /* istanbul ignore next */
        (cov_lld5u1oeb().b[21][0]++, 'pass') :
        /* istanbul ignore next */
        (cov_lld5u1oeb().b[21][1]++, 'fail'),
        message: response ?
        /* istanbul ignore next */
        (cov_lld5u1oeb().b[22][0]++, 'Service responding') :
        /* istanbul ignore next */
        (cov_lld5u1oeb().b[22][1]++, 'Service not responding'),
        timestamp: new Date(),
        duration
      });
      /* istanbul ignore next */
      cov_lld5u1oeb().s[116]++;
      this.updateServiceHealthStatus(serviceId, healthCheck);
    } catch (error) {
      const healthCheck =
      /* istanbul ignore next */
      (cov_lld5u1oeb().s[117]++, {
        name: 'ping',
        status: 'fail',
        message: error instanceof Error ?
        /* istanbul ignore next */
        (cov_lld5u1oeb().b[23][0]++, error.message) :
        /* istanbul ignore next */
        (cov_lld5u1oeb().b[23][1]++, 'Unknown error'),
        timestamp: new Date(),
        duration: Date.now() - startTime
      });
      /* istanbul ignore next */
      cov_lld5u1oeb().s[118]++;
      this.updateServiceHealthStatus(serviceId, healthCheck);
    }
  }
  async pingService(service) {
    /* istanbul ignore next */
    cov_lld5u1oeb().f[28]++;
    cov_lld5u1oeb().s[119]++;
    // Simplified ping implementation
    // In a real implementation, this would make an actual HTTP request
    try {
      const url =
      /* istanbul ignore next */
      (cov_lld5u1oeb().s[120]++, `${service.protocol}://${service.url}${
      /* istanbul ignore next */
      (cov_lld5u1oeb().b[24][0]++, service.path) ||
      /* istanbul ignore next */
      (cov_lld5u1oeb().b[24][1]++, '/health')}`);
      const response =
      /* istanbul ignore next */
      (cov_lld5u1oeb().s[121]++, await fetch(url, {
        method: 'GET',
        timeout: 5000
      }));
      /* istanbul ignore next */
      cov_lld5u1oeb().s[122]++;
      return response.ok;
    } catch {
      /* istanbul ignore next */
      cov_lld5u1oeb().s[123]++;
      return false;
    }
  }
  updateServiceHealth(serviceId, success, responseTime) {
    /* istanbul ignore next */
    cov_lld5u1oeb().f[29]++;
    const service =
    /* istanbul ignore next */
    (cov_lld5u1oeb().s[124]++, this.services.get(serviceId));
    /* istanbul ignore next */
    cov_lld5u1oeb().s[125]++;
    if (!service) {
      /* istanbul ignore next */
      cov_lld5u1oeb().b[25][0]++;
      cov_lld5u1oeb().s[126]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_lld5u1oeb().b[25][1]++;
    }
    cov_lld5u1oeb().s[127]++;
    service.health.responseTime = responseTime;
    /* istanbul ignore next */
    cov_lld5u1oeb().s[128]++;
    service.health.lastCheck = new Date();
    /* istanbul ignore next */
    cov_lld5u1oeb().s[129]++;
    service.lastHeartbeat = new Date();
    /* istanbul ignore next */
    cov_lld5u1oeb().s[130]++;
    if (success) {
      /* istanbul ignore next */
      cov_lld5u1oeb().b[26][0]++;
      cov_lld5u1oeb().s[131]++;
      service.health.errorRate = Math.max(0, service.health.errorRate - 0.1);
    } else {
      /* istanbul ignore next */
      cov_lld5u1oeb().b[26][1]++;
      cov_lld5u1oeb().s[132]++;
      service.health.errorRate = Math.min(1, service.health.errorRate + 0.1);
    }
    // Update health status based on error rate
    /* istanbul ignore next */
    cov_lld5u1oeb().s[133]++;
    if (service.health.errorRate < 0.1) {
      /* istanbul ignore next */
      cov_lld5u1oeb().b[27][0]++;
      cov_lld5u1oeb().s[134]++;
      service.health.status = 'healthy';
    } else {
      /* istanbul ignore next */
      cov_lld5u1oeb().b[27][1]++;
      cov_lld5u1oeb().s[135]++;
      if (service.health.errorRate < 0.5) {
        /* istanbul ignore next */
        cov_lld5u1oeb().b[28][0]++;
        cov_lld5u1oeb().s[136]++;
        service.health.status = 'degraded';
      } else {
        /* istanbul ignore next */
        cov_lld5u1oeb().b[28][1]++;
        cov_lld5u1oeb().s[137]++;
        service.health.status = 'unhealthy';
      }
    }
  }
  updateServiceHealthStatus(serviceId, healthCheck) {
    /* istanbul ignore next */
    cov_lld5u1oeb().f[30]++;
    const service =
    /* istanbul ignore next */
    (cov_lld5u1oeb().s[138]++, this.services.get(serviceId));
    /* istanbul ignore next */
    cov_lld5u1oeb().s[139]++;
    if (!service) {
      /* istanbul ignore next */
      cov_lld5u1oeb().b[29][0]++;
      cov_lld5u1oeb().s[140]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_lld5u1oeb().b[29][1]++;
    }
    cov_lld5u1oeb().s[141]++;
    service.health.checks = [healthCheck, ...service.health.checks.slice(0, 9)]; // Keep last 10 checks
    /* istanbul ignore next */
    cov_lld5u1oeb().s[142]++;
    service.health.lastCheck = healthCheck.timestamp;
    /* istanbul ignore next */
    cov_lld5u1oeb().s[143]++;
    service.lastHeartbeat = new Date();
    // Update overall health status
    const recentChecks =
    /* istanbul ignore next */
    (cov_lld5u1oeb().s[144]++, service.health.checks.slice(0, 5));
    const failedChecks =
    /* istanbul ignore next */
    (cov_lld5u1oeb().s[145]++, recentChecks.filter(check => {
      /* istanbul ignore next */
      cov_lld5u1oeb().f[31]++;
      cov_lld5u1oeb().s[146]++;
      return check.status === 'fail';
    }).length);
    /* istanbul ignore next */
    cov_lld5u1oeb().s[147]++;
    if (failedChecks === 0) {
      /* istanbul ignore next */
      cov_lld5u1oeb().b[30][0]++;
      cov_lld5u1oeb().s[148]++;
      service.health.status = 'healthy';
    } else {
      /* istanbul ignore next */
      cov_lld5u1oeb().b[30][1]++;
      cov_lld5u1oeb().s[149]++;
      if (failedChecks < 3) {
        /* istanbul ignore next */
        cov_lld5u1oeb().b[31][0]++;
        cov_lld5u1oeb().s[150]++;
        service.health.status = 'degraded';
      } else {
        /* istanbul ignore next */
        cov_lld5u1oeb().b[31][1]++;
        cov_lld5u1oeb().s[151]++;
        service.health.status = 'unhealthy';
      }
    }
  }
  updateResponseTime(responseTime) {
    /* istanbul ignore next */
    cov_lld5u1oeb().f[32]++;
    cov_lld5u1oeb().s[152]++;
    this.metrics.averageResponseTime = (this.metrics.averageResponseTime + responseTime) / 2;
  }
  // =============================================================================
  // Core Services Registration
  // =============================================================================
  registerCoreServices() {
    /* istanbul ignore next */
    cov_lld5u1oeb().f[33]++;
    cov_lld5u1oeb().s[153]++;
    // Register core SizeWise services for microservices preparation
    // HVAC Calculation Service
    this.registerService({
      id: 'hvac-calc-service',
      name: 'hvac-calculation',
      version: '1.0.0',
      url: 'localhost',
      protocol: 'http',
      port: 3001,
      path: '/api/calculations',
      health: {
        status: 'healthy',
        lastCheck: new Date(),
        responseTime: 50,
        errorRate: 0,
        uptime: 100,
        checks: []
      },
      metadata: {
        tags: ['calculation', 'hvac', 'core'],
        environment: 'development',
        capabilities: ['air-duct-sizing', 'load-calculation', 'energy-analysis'],
        dependencies: ['database-service'],
        resources: {
          cpu: 0.5,
          memory: 512,
          storage: 100
        }
      }
    });
    // Project Management Service
    /* istanbul ignore next */
    cov_lld5u1oeb().s[154]++;
    this.registerService({
      id: 'project-mgmt-service',
      name: 'project-management',
      version: '1.0.0',
      url: 'localhost',
      protocol: 'http',
      port: 3002,
      path: '/api/projects',
      health: {
        status: 'healthy',
        lastCheck: new Date(),
        responseTime: 30,
        errorRate: 0,
        uptime: 100,
        checks: []
      },
      metadata: {
        tags: ['project', 'management', 'core'],
        environment: 'development',
        capabilities: ['project-crud', 'collaboration', 'version-control'],
        dependencies: ['database-service', 'auth-service'],
        resources: {
          cpu: 0.3,
          memory: 256,
          storage: 500
        }
      }
    });
    // Database Service
    /* istanbul ignore next */
    cov_lld5u1oeb().s[155]++;
    this.registerService({
      id: 'database-service',
      name: 'database',
      version: '1.0.0',
      url: 'localhost',
      protocol: 'http',
      port: 3003,
      path: '/api/data',
      health: {
        status: 'healthy',
        lastCheck: new Date(),
        responseTime: 20,
        errorRate: 0,
        uptime: 100,
        checks: []
      },
      metadata: {
        tags: ['database', 'storage', 'infrastructure'],
        environment: 'development',
        capabilities: ['postgresql', 'mongodb', 'caching'],
        dependencies: [],
        resources: {
          cpu: 1.0,
          memory: 1024,
          storage: 2000
        }
      }
    });
  }
  // =============================================================================
  // Public API
  // =============================================================================
  getMetrics() {
    /* istanbul ignore next */
    cov_lld5u1oeb().f[34]++;
    cov_lld5u1oeb().s[156]++;
    return {
      ...this.metrics,
      registeredServices: this.services.size,
      healthyServices: Array.from(this.services.values()).filter(s => {
        /* istanbul ignore next */
        cov_lld5u1oeb().f[35]++;
        cov_lld5u1oeb().s[157]++;
        return s.health.status === 'healthy';
      }).length,
      circuitBreakers: Array.from(this.circuitBreakers.entries()).map(([id, cb]) => {
        /* istanbul ignore next */
        cov_lld5u1oeb().f[36]++;
        cov_lld5u1oeb().s[158]++;
        return {
          serviceId: id,
          ...cb.getMetrics()
        };
      })
    };
  }
  getAllServices() {
    /* istanbul ignore next */
    cov_lld5u1oeb().f[37]++;
    cov_lld5u1oeb().s[159]++;
    return Array.from(this.services.values());
  }
  getServiceHealth(serviceId) {
    /* istanbul ignore next */
    cov_lld5u1oeb().f[38]++;
    cov_lld5u1oeb().s[160]++;
    return this.services.get(serviceId)?.health;
  }
  destroy() {
    /* istanbul ignore next */
    cov_lld5u1oeb().f[39]++;
    cov_lld5u1oeb().s[161]++;
    if (this.healthCheckInterval) {
      /* istanbul ignore next */
      cov_lld5u1oeb().b[32][0]++;
      cov_lld5u1oeb().s[162]++;
      clearInterval(this.healthCheckInterval);
    } else
    /* istanbul ignore next */
    {
      cov_lld5u1oeb().b[32][1]++;
    }
  }
}
/* istanbul ignore next */
cov_lld5u1oeb().s[163]++;
exports.ServiceRegistry = ServiceRegistry;
class APIGateway {
  constructor(serviceRegistry) {
    /* istanbul ignore next */
    cov_lld5u1oeb().f[40]++;
    cov_lld5u1oeb().s[164]++;
    this.routes = new Map();
    /* istanbul ignore next */
    cov_lld5u1oeb().s[165]++;
    this.rateLimiters = new Map();
    /* istanbul ignore next */
    cov_lld5u1oeb().s[166]++;
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageLatency: 0,
      rateLimitedRequests: 0,
      timeouts: 0
    };
    /* istanbul ignore next */
    cov_lld5u1oeb().s[167]++;
    this.serviceRegistry = serviceRegistry;
    /* istanbul ignore next */
    cov_lld5u1oeb().s[168]++;
    this.setupDefaultRoutes();
  }
  // =============================================================================
  // Route Management
  // =============================================================================
  addRoute(route) {
    /* istanbul ignore next */
    cov_lld5u1oeb().f[41]++;
    const key =
    /* istanbul ignore next */
    (cov_lld5u1oeb().s[169]++, `${route.method}:${route.path}`);
    /* istanbul ignore next */
    cov_lld5u1oeb().s[170]++;
    this.routes.set(key, route);
  }
  removeRoute(method, path) {
    /* istanbul ignore next */
    cov_lld5u1oeb().f[42]++;
    const key =
    /* istanbul ignore next */
    (cov_lld5u1oeb().s[171]++, `${method}:${path}`);
    /* istanbul ignore next */
    cov_lld5u1oeb().s[172]++;
    this.routes.delete(key);
  }
  // =============================================================================
  // Request Routing and Processing
  // =============================================================================
  async routeRequest(method, path, body, headers) {
    /* istanbul ignore next */
    cov_lld5u1oeb().f[43]++;
    const startTime =
    /* istanbul ignore next */
    (cov_lld5u1oeb().s[173]++, Date.now());
    /* istanbul ignore next */
    cov_lld5u1oeb().s[174]++;
    this.metrics.totalRequests++;
    /* istanbul ignore next */
    cov_lld5u1oeb().s[175]++;
    try {
      // Find matching route
      const route =
      /* istanbul ignore next */
      (cov_lld5u1oeb().s[176]++, this.findRoute(method, path));
      /* istanbul ignore next */
      cov_lld5u1oeb().s[177]++;
      if (!route) {
        /* istanbul ignore next */
        cov_lld5u1oeb().b[33][0]++;
        cov_lld5u1oeb().s[178]++;
        throw new Error(`Route not found: ${method} ${path}`);
      } else
      /* istanbul ignore next */
      {
        cov_lld5u1oeb().b[33][1]++;
      }
      // Apply rate limiting
      cov_lld5u1oeb().s[179]++;
      if (
      /* istanbul ignore next */
      (cov_lld5u1oeb().b[35][0]++, route.rateLimit) &&
      /* istanbul ignore next */
      (cov_lld5u1oeb().b[35][1]++, !this.checkRateLimit(route))) {
        /* istanbul ignore next */
        cov_lld5u1oeb().b[34][0]++;
        cov_lld5u1oeb().s[180]++;
        this.metrics.rateLimitedRequests++;
        /* istanbul ignore next */
        cov_lld5u1oeb().s[181]++;
        throw new Error('Rate limit exceeded');
      } else
      /* istanbul ignore next */
      {
        cov_lld5u1oeb().b[34][1]++;
      }
      // Select service instance
      const service =
      /* istanbul ignore next */
      (cov_lld5u1oeb().s[182]++, this.serviceRegistry.selectService(route.serviceName));
      /* istanbul ignore next */
      cov_lld5u1oeb().s[183]++;
      if (!service) {
        /* istanbul ignore next */
        cov_lld5u1oeb().b[36][0]++;
        cov_lld5u1oeb().s[184]++;
        throw new Error(`No healthy service found: ${route.serviceName}`);
      } else
      /* istanbul ignore next */
      {
        cov_lld5u1oeb().b[36][1]++;
      }
      // Execute request with circuit breaker
      const result =
      /* istanbul ignore next */
      (cov_lld5u1oeb().s[185]++, await this.serviceRegistry.callService(service.id, async endpoint => {
        /* istanbul ignore next */
        cov_lld5u1oeb().f[44]++;
        cov_lld5u1oeb().s[186]++;
        return await this.executeRequest(endpoint, route, body, headers);
      }));
      /* istanbul ignore next */
      cov_lld5u1oeb().s[187]++;
      this.metrics.successfulRequests++;
      /* istanbul ignore next */
      cov_lld5u1oeb().s[188]++;
      this.updateLatency(Date.now() - startTime);
      /* istanbul ignore next */
      cov_lld5u1oeb().s[189]++;
      return result;
    } catch (error) {
      /* istanbul ignore next */
      cov_lld5u1oeb().s[190]++;
      this.metrics.failedRequests++;
      /* istanbul ignore next */
      cov_lld5u1oeb().s[191]++;
      this.updateLatency(Date.now() - startTime);
      /* istanbul ignore next */
      cov_lld5u1oeb().s[192]++;
      throw error;
    }
  }
  findRoute(method, path) {
    /* istanbul ignore next */
    cov_lld5u1oeb().f[45]++;
    const exactKey =
    /* istanbul ignore next */
    (cov_lld5u1oeb().s[193]++, `${method}:${path}`);
    const exactMatch =
    /* istanbul ignore next */
    (cov_lld5u1oeb().s[194]++, this.routes.get(exactKey));
    /* istanbul ignore next */
    cov_lld5u1oeb().s[195]++;
    if (exactMatch) {
      /* istanbul ignore next */
      cov_lld5u1oeb().b[37][0]++;
      cov_lld5u1oeb().s[196]++;
      return exactMatch;
    } else
    /* istanbul ignore next */
    {
      cov_lld5u1oeb().b[37][1]++;
    }
    // Pattern matching for dynamic routes
    cov_lld5u1oeb().s[197]++;
    for (const [key, route] of this.routes) {
      /* istanbul ignore next */
      cov_lld5u1oeb().s[198]++;
      if (key.startsWith(`${method}:`)) {
        /* istanbul ignore next */
        cov_lld5u1oeb().b[38][0]++;
        const routePath =
        /* istanbul ignore next */
        (cov_lld5u1oeb().s[199]++, key.substring(method.length + 1));
        /* istanbul ignore next */
        cov_lld5u1oeb().s[200]++;
        if (this.matchPath(routePath, path)) {
          /* istanbul ignore next */
          cov_lld5u1oeb().b[39][0]++;
          cov_lld5u1oeb().s[201]++;
          return route;
        } else
        /* istanbul ignore next */
        {
          cov_lld5u1oeb().b[39][1]++;
        }
      } else
      /* istanbul ignore next */
      {
        cov_lld5u1oeb().b[38][1]++;
      }
    }
    /* istanbul ignore next */
    cov_lld5u1oeb().s[202]++;
    return undefined;
  }
  matchPath(routePath, requestPath) {
    /* istanbul ignore next */
    cov_lld5u1oeb().f[46]++;
    // Simple pattern matching - can be enhanced with more sophisticated routing
    const routeSegments =
    /* istanbul ignore next */
    (cov_lld5u1oeb().s[203]++, routePath.split('/'));
    const requestSegments =
    /* istanbul ignore next */
    (cov_lld5u1oeb().s[204]++, requestPath.split('/'));
    /* istanbul ignore next */
    cov_lld5u1oeb().s[205]++;
    if (routeSegments.length !== requestSegments.length) {
      /* istanbul ignore next */
      cov_lld5u1oeb().b[40][0]++;
      cov_lld5u1oeb().s[206]++;
      return false;
    } else
    /* istanbul ignore next */
    {
      cov_lld5u1oeb().b[40][1]++;
    }
    cov_lld5u1oeb().s[207]++;
    for (let i =
    /* istanbul ignore next */
    (cov_lld5u1oeb().s[208]++, 0); i < routeSegments.length; i++) {
      const routeSegment =
      /* istanbul ignore next */
      (cov_lld5u1oeb().s[209]++, routeSegments[i]);
      const requestSegment =
      /* istanbul ignore next */
      (cov_lld5u1oeb().s[210]++, requestSegments[i]);
      /* istanbul ignore next */
      cov_lld5u1oeb().s[211]++;
      if (routeSegment.startsWith(':')) {
        /* istanbul ignore next */
        cov_lld5u1oeb().b[41][0]++;
        cov_lld5u1oeb().s[212]++;
        // Dynamic segment - matches any value
        continue;
      } else
      /* istanbul ignore next */
      {
        cov_lld5u1oeb().b[41][1]++;
      }
      cov_lld5u1oeb().s[213]++;
      if (routeSegment !== requestSegment) {
        /* istanbul ignore next */
        cov_lld5u1oeb().b[42][0]++;
        cov_lld5u1oeb().s[214]++;
        return false;
      } else
      /* istanbul ignore next */
      {
        cov_lld5u1oeb().b[42][1]++;
      }
    }
    /* istanbul ignore next */
    cov_lld5u1oeb().s[215]++;
    return true;
  }
  checkRateLimit(route) {
    /* istanbul ignore next */
    cov_lld5u1oeb().f[47]++;
    cov_lld5u1oeb().s[216]++;
    if (!route.rateLimit) {
      /* istanbul ignore next */
      cov_lld5u1oeb().b[43][0]++;
      cov_lld5u1oeb().s[217]++;
      return true;
    } else
    /* istanbul ignore next */
    {
      cov_lld5u1oeb().b[43][1]++;
    }
    const key =
    /* istanbul ignore next */
    (cov_lld5u1oeb().s[218]++, `${route.method}:${route.path}`);
    const now =
    /* istanbul ignore next */
    (cov_lld5u1oeb().s[219]++, Date.now());
    const limiter =
    /* istanbul ignore next */
    (cov_lld5u1oeb().s[220]++, this.rateLimiters.get(key));
    /* istanbul ignore next */
    cov_lld5u1oeb().s[221]++;
    if (
    /* istanbul ignore next */
    (cov_lld5u1oeb().b[45][0]++, !limiter) ||
    /* istanbul ignore next */
    (cov_lld5u1oeb().b[45][1]++, now > limiter.resetTime)) {
      /* istanbul ignore next */
      cov_lld5u1oeb().b[44][0]++;
      cov_lld5u1oeb().s[222]++;
      this.rateLimiters.set(key, {
        count: 1,
        resetTime: now + route.rateLimit.window
      });
      /* istanbul ignore next */
      cov_lld5u1oeb().s[223]++;
      return true;
    } else
    /* istanbul ignore next */
    {
      cov_lld5u1oeb().b[44][1]++;
    }
    cov_lld5u1oeb().s[224]++;
    if (limiter.count >= route.rateLimit.requests) {
      /* istanbul ignore next */
      cov_lld5u1oeb().b[46][0]++;
      cov_lld5u1oeb().s[225]++;
      return false;
    } else
    /* istanbul ignore next */
    {
      cov_lld5u1oeb().b[46][1]++;
    }
    cov_lld5u1oeb().s[226]++;
    limiter.count++;
    /* istanbul ignore next */
    cov_lld5u1oeb().s[227]++;
    return true;
  }
  async executeRequest(endpoint, route, body, headers) {
    /* istanbul ignore next */
    cov_lld5u1oeb().f[48]++;
    const targetPath =
    /* istanbul ignore next */
    (cov_lld5u1oeb().s[228]++,
    /* istanbul ignore next */
    (cov_lld5u1oeb().b[47][0]++, route.targetPath) ||
    /* istanbul ignore next */
    (cov_lld5u1oeb().b[47][1]++, route.path));
    const url =
    /* istanbul ignore next */
    (cov_lld5u1oeb().s[229]++, `${endpoint.protocol}://${endpoint.url}:${endpoint.port}${targetPath}`);
    const requestOptions =
    /* istanbul ignore next */
    (cov_lld5u1oeb().s[230]++, {
      method: route.method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    });
    /* istanbul ignore next */
    cov_lld5u1oeb().s[231]++;
    if (
    /* istanbul ignore next */
    (cov_lld5u1oeb().b[49][0]++, body) &&
    /* istanbul ignore next */
    (cov_lld5u1oeb().b[49][1]++, route.method !== 'GET')) {
      /* istanbul ignore next */
      cov_lld5u1oeb().b[48][0]++;
      cov_lld5u1oeb().s[232]++;
      requestOptions.body = JSON.stringify(body);
    } else
    /* istanbul ignore next */
    {
      cov_lld5u1oeb().b[48][1]++;
    }
    // Add timeout
    const timeout =
    /* istanbul ignore next */
    (cov_lld5u1oeb().s[233]++,
    /* istanbul ignore next */
    (cov_lld5u1oeb().b[50][0]++, route.timeout) ||
    /* istanbul ignore next */
    (cov_lld5u1oeb().b[50][1]++, 30000)); // 30 seconds default
    const controller =
    /* istanbul ignore next */
    (cov_lld5u1oeb().s[234]++, new AbortController());
    const timeoutId =
    /* istanbul ignore next */
    (cov_lld5u1oeb().s[235]++, setTimeout(() => {
      /* istanbul ignore next */
      cov_lld5u1oeb().f[49]++;
      cov_lld5u1oeb().s[236]++;
      controller.abort();
      /* istanbul ignore next */
      cov_lld5u1oeb().s[237]++;
      this.metrics.timeouts++;
    }, timeout));
    /* istanbul ignore next */
    cov_lld5u1oeb().s[238]++;
    requestOptions.signal = controller.signal;
    /* istanbul ignore next */
    cov_lld5u1oeb().s[239]++;
    try {
      const response =
      /* istanbul ignore next */
      (cov_lld5u1oeb().s[240]++, await fetch(url, requestOptions));
      /* istanbul ignore next */
      cov_lld5u1oeb().s[241]++;
      clearTimeout(timeoutId);
      /* istanbul ignore next */
      cov_lld5u1oeb().s[242]++;
      if (!response.ok) {
        /* istanbul ignore next */
        cov_lld5u1oeb().b[51][0]++;
        cov_lld5u1oeb().s[243]++;
        throw new Error(`Service error: ${response.status} ${response.statusText}`);
      } else
      /* istanbul ignore next */
      {
        cov_lld5u1oeb().b[51][1]++;
      }
      cov_lld5u1oeb().s[244]++;
      return await response.json();
    } catch (error) {
      /* istanbul ignore next */
      cov_lld5u1oeb().s[245]++;
      clearTimeout(timeoutId);
      /* istanbul ignore next */
      cov_lld5u1oeb().s[246]++;
      throw error;
    }
  }
  updateLatency(latency) {
    /* istanbul ignore next */
    cov_lld5u1oeb().f[50]++;
    cov_lld5u1oeb().s[247]++;
    this.metrics.averageLatency = (this.metrics.averageLatency + latency) / 2;
  }
  // =============================================================================
  // Default Routes Setup
  // =============================================================================
  setupDefaultRoutes() {
    /* istanbul ignore next */
    cov_lld5u1oeb().f[51]++;
    cov_lld5u1oeb().s[248]++;
    // HVAC Calculation routes
    this.addRoute({
      path: '/api/calculations/air-duct',
      method: 'POST',
      serviceName: 'hvac-calculation',
      targetPath: '/api/calculations/air-duct',
      authentication: true,
      timeout: 10000,
      rateLimit: {
        requests: 100,
        window: 60000
      } // 100 requests per minute
    });
    /* istanbul ignore next */
    cov_lld5u1oeb().s[249]++;
    this.addRoute({
      path: '/api/calculations/load',
      method: 'POST',
      serviceName: 'hvac-calculation',
      targetPath: '/api/calculations/load',
      authentication: true,
      timeout: 15000,
      rateLimit: {
        requests: 50,
        window: 60000
      }
    });
    // Project Management routes
    /* istanbul ignore next */
    cov_lld5u1oeb().s[250]++;
    this.addRoute({
      path: '/api/projects',
      method: 'GET',
      serviceName: 'project-management',
      authentication: true,
      rateLimit: {
        requests: 200,
        window: 60000
      }
    });
    /* istanbul ignore next */
    cov_lld5u1oeb().s[251]++;
    this.addRoute({
      path: '/api/projects',
      method: 'POST',
      serviceName: 'project-management',
      authentication: true,
      rateLimit: {
        requests: 20,
        window: 60000
      }
    });
    /* istanbul ignore next */
    cov_lld5u1oeb().s[252]++;
    this.addRoute({
      path: '/api/projects/:id',
      method: 'GET',
      serviceName: 'project-management',
      authentication: true,
      rateLimit: {
        requests: 500,
        window: 60000
      }
    });
    /* istanbul ignore next */
    cov_lld5u1oeb().s[253]++;
    this.addRoute({
      path: '/api/projects/:id',
      method: 'PUT',
      serviceName: 'project-management',
      authentication: true,
      rateLimit: {
        requests: 50,
        window: 60000
      }
    });
    // Database routes
    /* istanbul ignore next */
    cov_lld5u1oeb().s[254]++;
    this.addRoute({
      path: '/api/data/sync',
      method: 'POST',
      serviceName: 'database',
      authentication: true,
      timeout: 30000,
      rateLimit: {
        requests: 10,
        window: 60000
      }
    });
  }
  // =============================================================================
  // Public API
  // =============================================================================
  getMetrics() {
    /* istanbul ignore next */
    cov_lld5u1oeb().f[52]++;
    cov_lld5u1oeb().s[255]++;
    return {
      ...this.metrics
    };
  }
  getRoutes() {
    /* istanbul ignore next */
    cov_lld5u1oeb().f[53]++;
    cov_lld5u1oeb().s[256]++;
    return Array.from(this.routes.values());
  }
  getServiceRegistry() {
    /* istanbul ignore next */
    cov_lld5u1oeb().f[54]++;
    cov_lld5u1oeb().s[257]++;
    return this.serviceRegistry;
  }
}
/* istanbul ignore next */
cov_lld5u1oeb().s[258]++;
exports.APIGateway = APIGateway;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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