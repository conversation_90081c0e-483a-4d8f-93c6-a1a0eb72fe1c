{"version": 3, "names": ["cov_1y81f61fhf", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "name", "decl", "loc", "branchMap", "type", "locations", "undefined", "s", "f", "b", "inputSourceMap", "file", "mappings", "names", "sources", "sourcesContent", "version", "_coverageSchema", "coverage", "actualCoverage", "SystemPressureCalculator_1", "require", "VelocityPressureCalculator_1", "EnhancedFrictionCalculator_1", "Canvas3DIntegrationHelper", "prepareSystemVisualization", "systemInput", "layoutPoints", "systemResult", "SystemPressureCalculator", "calculateSystemPressure", "vpResult", "VelocityPressureCalculator", "calculateVelocityPressure", "velocity", "method", "VelocityPressureMethod", "ENHANCED_FORMULA", "airConditions", "frictionResult", "EnhancedFrictionCalculator", "calculateFrictionLoss", "hydraulicDiameter", "length", "material", "FrictionMethod", "ENHANCED_DARCY", "segments", "generateDuctSegments", "fittings", "generateFittings3D", "airflow", "generateAirflowVisualization", "performance", "totalPressureLoss", "systemEfficiency", "calculateSystemEfficiency", "energyConsumption", "estimateEnergyConsumption", "colorMaps", "generateColorMaps", "metadata", "calculationTime", "Date", "toISOString", "accuracy", "Math", "min", "VERSION", "units", "pressure", "temperature", "points", "generateDefaultLayout", "i", "segment", "id", "startPoint", "endPoint", "diameter", "crossSection", "dimensions", "pressureLoss", "frictionLoss", "frictionRate", "reynoldsNumber", "flowRegime", "visualProperties", "color", "getSegmentColor", "opacity", "highlighted", "push", "fittings3D", "for<PERSON>ach", "fitting", "index", "segmentIndex", "position", "x", "y", "z", "fitting3D", "orientation", "rotation", "direction", "connections", "filter", "Boolean", "kFactor", "getFittingColor", "model", "getFittingModel", "velocityArray", "dx", "dy", "dz", "sqrt", "pressureValues", "map", "velocityValues", "createColorMap", "efficiency", "max", "floor", "colorMap", "default", "modelMap", "values", "colorSchemes", "colors", "value", "idealPressure", "velocityPressure", "actualPressure", "fanEfficiency", "motorEfficiency", "exports"], "sources": ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\Canvas3DIntegrationHelper.ts"], "sourcesContent": ["/**\r\n * 3D Canvas Integration Helper for SizeWise Suite\r\n * \r\n * Provides data transformation and formatting services to prepare calculation\r\n * results for 3D canvas visualization and interaction.\r\n * \r\n * @version 5.0.0\r\n * <AUTHOR> Suite Development Team\r\n */\r\n\r\nimport { SystemPressureCalculator } from './SystemPressureCalculator';\r\nimport { VelocityPressureCalculator, VelocityPressureMethod } from './VelocityPressureCalculator';\r\nimport { EnhancedFrictionCalculator, FrictionMethod } from './EnhancedFrictionCalculator';\r\nimport { AirPropertiesCalculator } from './AirPropertiesCalculator';\r\n\r\nimport type {\r\n  SystemPressureInput,\r\n  SystemPressureResult,\r\n  VelocityPressureInput,\r\n  FrictionCalculationInput,\r\n  AirConditions\r\n} from './types';\r\n\r\n/**\r\n * 3D Visualization Data Structures\r\n */\r\nexport interface Point3D {\r\n  x: number;\r\n  y: number;\r\n  z: number;\r\n}\r\n\r\nexport interface DuctSegment3D {\r\n  id: string;\r\n  startPoint: Point3D;\r\n  endPoint: Point3D;\r\n  diameter: number;\r\n  crossSection: 'circular' | 'rectangular';\r\n  dimensions?: {\r\n    width?: number;\r\n    height?: number;\r\n    diameter?: number;\r\n  };\r\n  material: string;\r\n  velocity: number;\r\n  pressureLoss: number;\r\n  frictionRate: number;\r\n  reynoldsNumber: number;\r\n  flowRegime: string;\r\n  visualProperties: {\r\n    color: string;\r\n    opacity: number;\r\n    highlighted: boolean;\r\n  };\r\n}\r\n\r\nexport interface Fitting3D {\r\n  id: string;\r\n  type: string;\r\n  position: Point3D;\r\n  orientation: {\r\n    rotation: Point3D;\r\n    direction: Point3D;\r\n  };\r\n  connections: string[]; // Connected segment IDs\r\n  pressureLoss: number;\r\n  kFactor: number;\r\n  visualProperties: {\r\n    color: string;\r\n    opacity: number;\r\n    highlighted: boolean;\r\n    model: string; // 3D model reference\r\n  };\r\n}\r\n\r\nexport interface SystemVisualization3D {\r\n  segments: DuctSegment3D[];\r\n  fittings: Fitting3D[];\r\n  airflow: {\r\n    direction: Point3D[];\r\n    velocity: number[];\r\n    pressure: number[];\r\n  };\r\n  performance: {\r\n    totalPressureLoss: number;\r\n    systemEfficiency: number;\r\n    energyConsumption: number;\r\n  };\r\n  colorMaps: {\r\n    pressure: ColorMap;\r\n    velocity: ColorMap;\r\n    efficiency: ColorMap;\r\n    temperature: ColorMap;\r\n  };\r\n  metadata: {\r\n    calculationTime: string;\r\n    accuracy: number;\r\n    version: string;\r\n    units: {\r\n      length: string;\r\n      pressure: string;\r\n      velocity: string;\r\n      temperature: string;\r\n    };\r\n  };\r\n}\r\n\r\nexport interface ColorMap {\r\n  min: number;\r\n  max: number;\r\n  colors: {\r\n    value: number;\r\n    color: string;\r\n  }[];\r\n}\r\n\r\n/**\r\n * Canvas 3D Integration Helper Service\r\n */\r\nexport class Canvas3DIntegrationHelper {\r\n  private static readonly VERSION = '5.0.0';\r\n\r\n  /**\r\n   * Convert system calculation results to 3D visualization format\r\n   */\r\n  static async prepareSystemVisualization(\r\n    systemInput: SystemPressureInput,\r\n    layoutPoints?: Point3D[]\r\n  ): Promise<SystemVisualization3D> {\r\n    // Calculate system pressure and performance\r\n    const systemResult = SystemPressureCalculator.calculateSystemPressure(systemInput);\r\n    \r\n    // Calculate detailed component results\r\n    const vpResult = VelocityPressureCalculator.calculateVelocityPressure({\r\n      velocity: systemInput.velocity,\r\n      method: VelocityPressureMethod.ENHANCED_FORMULA,\r\n      airConditions: systemInput.airConditions\r\n    });\r\n\r\n    const frictionResult = EnhancedFrictionCalculator.calculateFrictionLoss({\r\n      velocity: systemInput.velocity,\r\n      hydraulicDiameter: systemInput.hydraulicDiameter,\r\n      length: systemInput.length,\r\n      material: systemInput.material,\r\n      method: FrictionMethod.ENHANCED_DARCY,\r\n      airConditions: systemInput.airConditions\r\n    });\r\n\r\n    // Generate 3D segments\r\n    const segments = this.generateDuctSegments(systemInput, frictionResult, layoutPoints);\r\n    \r\n    // Generate 3D fittings\r\n    const fittings = this.generateFittings3D(systemInput.fittings || [], segments);\r\n    \r\n    // Generate airflow visualization data\r\n    const airflow = this.generateAirflowVisualization(segments, systemInput.velocity);\r\n    \r\n    // Generate performance data\r\n    const performance = {\r\n      totalPressureLoss: systemResult.totalPressureLoss,\r\n      systemEfficiency: this.calculateSystemEfficiency(systemResult),\r\n      energyConsumption: this.estimateEnergyConsumption(systemResult, systemInput.airflow)\r\n    };\r\n\r\n    // Generate color maps\r\n    const colorMaps = this.generateColorMaps(segments, fittings);\r\n\r\n    return {\r\n      segments,\r\n      fittings,\r\n      airflow,\r\n      performance,\r\n      colorMaps,\r\n      metadata: {\r\n        calculationTime: new Date().toISOString(),\r\n        accuracy: Math.min(vpResult.accuracy, frictionResult.accuracy),\r\n        version: this.VERSION,\r\n        units: {\r\n          length: 'inches',\r\n          pressure: 'in. w.g.',\r\n          velocity: 'FPM',\r\n          temperature: '°F'\r\n        }\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Generate 3D duct segments from system input\r\n   */\r\n  private static generateDuctSegments(\r\n    systemInput: SystemPressureInput,\r\n    frictionResult: any,\r\n    layoutPoints?: Point3D[]\r\n  ): DuctSegment3D[] {\r\n    const segments: DuctSegment3D[] = [];\r\n    \r\n    // Default linear layout if no points provided\r\n    const points = layoutPoints || this.generateDefaultLayout(systemInput.length);\r\n    \r\n    // Create segments between consecutive points\r\n    for (let i = 0; i < points.length - 1; i++) {\r\n      const segment: DuctSegment3D = {\r\n        id: `segment_${i}`,\r\n        startPoint: points[i],\r\n        endPoint: points[i + 1],\r\n        diameter: systemInput.hydraulicDiameter,\r\n        crossSection: 'circular',\r\n        dimensions: {\r\n          diameter: systemInput.hydraulicDiameter\r\n        },\r\n        material: systemInput.material,\r\n        velocity: systemInput.velocity,\r\n        pressureLoss: frictionResult.frictionLoss / points.length,\r\n        frictionRate: frictionResult.frictionRate,\r\n        reynoldsNumber: frictionResult.reynoldsNumber,\r\n        flowRegime: frictionResult.flowRegime,\r\n        visualProperties: {\r\n          color: this.getSegmentColor(systemInput.velocity),\r\n          opacity: 0.8,\r\n          highlighted: false\r\n        }\r\n      };\r\n      segments.push(segment);\r\n    }\r\n\r\n    return segments;\r\n  }\r\n\r\n  /**\r\n   * Generate 3D fittings from fitting input\r\n   */\r\n  private static generateFittings3D(\r\n    fittings: any[],\r\n    segments: DuctSegment3D[]\r\n  ): Fitting3D[] {\r\n    const fittings3D: Fitting3D[] = [];\r\n    \r\n    fittings.forEach((fitting, index) => {\r\n      // Place fitting at segment connection points\r\n      const segmentIndex = Math.min(index, segments.length - 1);\r\n      const position = segments[segmentIndex]?.endPoint || { x: 0, y: 0, z: 0 };\r\n      \r\n      const fitting3D: Fitting3D = {\r\n        id: `fitting_${index}`,\r\n        type: fitting.type,\r\n        position,\r\n        orientation: {\r\n          rotation: { x: 0, y: 0, z: 0 },\r\n          direction: { x: 1, y: 0, z: 0 }\r\n        },\r\n        connections: [`segment_${segmentIndex}`, `segment_${segmentIndex + 1}`].filter(Boolean),\r\n        pressureLoss: fitting.pressureLoss || 0,\r\n        kFactor: fitting.kFactor || 0,\r\n        visualProperties: {\r\n          color: this.getFittingColor(fitting.type),\r\n          opacity: 0.9,\r\n          highlighted: false,\r\n          model: this.getFittingModel(fitting.type)\r\n        }\r\n      };\r\n      fittings3D.push(fitting3D);\r\n    });\r\n\r\n    return fittings3D;\r\n  }\r\n\r\n  /**\r\n   * Generate airflow visualization data\r\n   */\r\n  private static generateAirflowVisualization(\r\n    segments: DuctSegment3D[],\r\n    velocity: number\r\n  ): SystemVisualization3D['airflow'] {\r\n    const direction: Point3D[] = [];\r\n    const velocityArray: number[] = [];\r\n    const pressure: number[] = [];\r\n\r\n    segments.forEach(segment => {\r\n      // Calculate flow direction vector\r\n      const dx = segment.endPoint.x - segment.startPoint.x;\r\n      const dy = segment.endPoint.y - segment.startPoint.y;\r\n      const dz = segment.endPoint.z - segment.startPoint.z;\r\n      const length = Math.sqrt(dx * dx + dy * dy + dz * dz);\r\n      \r\n      direction.push({\r\n        x: dx / length,\r\n        y: dy / length,\r\n        z: dz / length\r\n      });\r\n      \r\n      velocityArray.push(velocity);\r\n      pressure.push(segment.pressureLoss);\r\n    });\r\n\r\n    return {\r\n      direction,\r\n      velocity: velocityArray,\r\n      pressure\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Generate color maps for visualization\r\n   */\r\n  private static generateColorMaps(\r\n    segments: DuctSegment3D[],\r\n    fittings: Fitting3D[]\r\n  ): SystemVisualization3D['colorMaps'] {\r\n    const pressureValues = [...segments.map(s => s.pressureLoss), ...fittings.map(f => f.pressureLoss)];\r\n    const velocityValues = segments.map(s => s.velocity);\r\n    \r\n    return {\r\n      pressure: this.createColorMap(pressureValues, 'pressure'),\r\n      velocity: this.createColorMap(velocityValues, 'velocity'),\r\n      efficiency: this.createColorMap([0.7, 0.8, 0.9, 1.0], 'efficiency'),\r\n      temperature: this.createColorMap([60, 70, 80, 90], 'temperature')\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Helper methods\r\n   */\r\n  private static generateDefaultLayout(length: number): Point3D[] {\r\n    const segments = Math.max(2, Math.floor(length / 10));\r\n    const points: Point3D[] = [];\r\n    \r\n    for (let i = 0; i <= segments; i++) {\r\n      points.push({\r\n        x: (i * length) / segments,\r\n        y: 0,\r\n        z: 0\r\n      });\r\n    }\r\n    \r\n    return points;\r\n  }\r\n\r\n  private static getSegmentColor(velocity: number): string {\r\n    if (velocity < 1000) return '#4CAF50'; // Green - low velocity\r\n    if (velocity < 2500) return '#FFC107'; // Yellow - medium velocity\r\n    if (velocity < 4000) return '#FF9800'; // Orange - high velocity\r\n    return '#F44336'; // Red - very high velocity\r\n  }\r\n\r\n  private static getFittingColor(type: string): string {\r\n    const colorMap: Record<string, string> = {\r\n      'elbow_90_smooth': '#2196F3',\r\n      'elbow_90_mitered': '#3F51B5',\r\n      'tee_branch': '#9C27B0',\r\n      'tee_straight': '#673AB7',\r\n      'damper_butterfly': '#FF5722',\r\n      'damper_blade': '#795548',\r\n      'transition': '#607D8B',\r\n      'diffuser': '#009688',\r\n      'default': '#757575'\r\n    };\r\n    return colorMap[type] || colorMap.default;\r\n  }\r\n\r\n  private static getFittingModel(type: string): string {\r\n    const modelMap: Record<string, string> = {\r\n      'elbow_90_smooth': 'elbow_90_smooth.obj',\r\n      'elbow_90_mitered': 'elbow_90_mitered.obj',\r\n      'tee_branch': 'tee_branch.obj',\r\n      'tee_straight': 'tee_straight.obj',\r\n      'damper_butterfly': 'damper_butterfly.obj',\r\n      'damper_blade': 'damper_blade.obj',\r\n      'transition': 'transition.obj',\r\n      'diffuser': 'diffuser.obj',\r\n      'default': 'generic_fitting.obj'\r\n    };\r\n    return modelMap[type] || modelMap.default;\r\n  }\r\n\r\n  private static createColorMap(values: number[], type: string): ColorMap {\r\n    const min = Math.min(...values);\r\n    const max = Math.max(...values);\r\n    \r\n    const colorSchemes: Record<string, string[]> = {\r\n      pressure: ['#4CAF50', '#FFC107', '#FF9800', '#F44336'],\r\n      velocity: ['#2196F3', '#00BCD4', '#4CAF50', '#FFC107'],\r\n      efficiency: ['#F44336', '#FF9800', '#FFC107', '#4CAF50'],\r\n      temperature: ['#2196F3', '#00BCD4', '#FFC107', '#FF5722']\r\n    };\r\n    \r\n    const colors = colorSchemes[type] || colorSchemes.pressure;\r\n    \r\n    return {\r\n      min,\r\n      max,\r\n      colors: colors.map((color, index) => ({\r\n        value: min + (max - min) * (index / (colors.length - 1)),\r\n        color\r\n      }))\r\n    };\r\n  }\r\n\r\n  private static calculateSystemEfficiency(systemResult: SystemPressureResult): number {\r\n    // Simple efficiency calculation based on pressure losses\r\n    const idealPressure = systemResult.velocityPressure;\r\n    const actualPressure = systemResult.totalPressureLoss;\r\n    return Math.max(0.1, Math.min(1.0, idealPressure / actualPressure));\r\n  }\r\n\r\n  private static estimateEnergyConsumption(\r\n    systemResult: SystemPressureResult,\r\n    airflow: number\r\n  ): number {\r\n    // Estimate fan power in watts\r\n    const fanEfficiency = 0.7; // Typical fan efficiency\r\n    const motorEfficiency = 0.9; // Typical motor efficiency\r\n    \r\n    // Power = (CFM × Total Pressure) / (6356 × Fan Efficiency × Motor Efficiency)\r\n    return (airflow * systemResult.totalPressureLoss) / (6356 * fanEfficiency * motorEfficiency);\r\n  }\r\n}\r\n"], "mappings": ";;AAAA;;;;;;;;;AAAA;AAAA,SAAAA,eAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;MAAA;QAAAC,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAO,SAAA;MAAA;QAAAD,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAW,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,cAAA;MAAAC,IAAA;MAAAC,QAAA;MAAAC,KAAA;MAAAC,OAAA;MAAAC,cAAA;MAAAC,OAAA;IAAA;IAAAC,eAAA;IAAA5B,IAAA;EAAA;EAAA,IAAA6B,QAAA,GAAA5B,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAA0B,QAAA,CAAA9B,IAAA,KAAA8B,QAAA,CAAA9B,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAA6B,QAAA,CAAA9B,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAA0B,cAAA,GAAAD,QAAA,CAAA9B,IAAA;EAAA;IAoHA;IAAAD,cAAA,YAAAA,CAAA;MAAA,OAAAgC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAhC,cAAA;AAAAA,cAAA,GAAAoB,CAAA;;;;;;;AA1GA,MAAAa,0BAAA;AAAA;AAAA,CAAAjC,cAAA,GAAAoB,CAAA,OAAAc,OAAA;AACA,MAAAC,4BAAA;AAAA;AAAA,CAAAnC,cAAA,GAAAoB,CAAA,OAAAc,OAAA;AACA,MAAAE,4BAAA;AAAA;AAAA,CAAApC,cAAA,GAAAoB,CAAA,OAAAc,OAAA;AAwGA;;;AAGA,MAAaG,yBAAyB;EAGpC;;;EAGA,aAAaC,0BAA0BA,CACrCC,WAAgC,EAChCC,YAAwB;IAAA;IAAAxC,cAAA,GAAAqB,CAAA;IAExB;IACA,MAAMoB,YAAY;IAAA;IAAA,CAAAzC,cAAA,GAAAoB,CAAA,OAAGa,0BAAA,CAAAS,wBAAwB,CAACC,uBAAuB,CAACJ,WAAW,CAAC;IAElF;IACA,MAAMK,QAAQ;IAAA;IAAA,CAAA5C,cAAA,GAAAoB,CAAA,OAAGe,4BAAA,CAAAU,0BAA0B,CAACC,yBAAyB,CAAC;MACpEC,QAAQ,EAAER,WAAW,CAACQ,QAAQ;MAC9BC,MAAM,EAAEb,4BAAA,CAAAc,sBAAsB,CAACC,gBAAgB;MAC/CC,aAAa,EAAEZ,WAAW,CAACY;KAC5B,CAAC;IAEF,MAAMC,cAAc;IAAA;IAAA,CAAApD,cAAA,GAAAoB,CAAA,OAAGgB,4BAAA,CAAAiB,0BAA0B,CAACC,qBAAqB,CAAC;MACtEP,QAAQ,EAAER,WAAW,CAACQ,QAAQ;MAC9BQ,iBAAiB,EAAEhB,WAAW,CAACgB,iBAAiB;MAChDC,MAAM,EAAEjB,WAAW,CAACiB,MAAM;MAC1BC,QAAQ,EAAElB,WAAW,CAACkB,QAAQ;MAC9BT,MAAM,EAAEZ,4BAAA,CAAAsB,cAAc,CAACC,cAAc;MACrCR,aAAa,EAAEZ,WAAW,CAACY;KAC5B,CAAC;IAEF;IACA,MAAMS,QAAQ;IAAA;IAAA,CAAA5D,cAAA,GAAAoB,CAAA,OAAG,IAAI,CAACyC,oBAAoB,CAACtB,WAAW,EAAEa,cAAc,EAAEZ,YAAY,CAAC;IAErF;IACA,MAAMsB,QAAQ;IAAA;IAAA,CAAA9D,cAAA,GAAAoB,CAAA,OAAG,IAAI,CAAC2C,kBAAkB;IAAC;IAAA,CAAA/D,cAAA,GAAAsB,CAAA,UAAAiB,WAAW,CAACuB,QAAQ;IAAA;IAAA,CAAA9D,cAAA,GAAAsB,CAAA,UAAI,EAAE,GAAEsC,QAAQ,CAAC;IAE9E;IACA,MAAMI,OAAO;IAAA;IAAA,CAAAhE,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAAC6C,4BAA4B,CAACL,QAAQ,EAAErB,WAAW,CAACQ,QAAQ,CAAC;IAEjF;IACA,MAAMmB,WAAW;IAAA;IAAA,CAAAlE,cAAA,GAAAoB,CAAA,QAAG;MAClB+C,iBAAiB,EAAE1B,YAAY,CAAC0B,iBAAiB;MACjDC,gBAAgB,EAAE,IAAI,CAACC,yBAAyB,CAAC5B,YAAY,CAAC;MAC9D6B,iBAAiB,EAAE,IAAI,CAACC,yBAAyB,CAAC9B,YAAY,EAAEF,WAAW,CAACyB,OAAO;KACpF;IAED;IACA,MAAMQ,SAAS;IAAA;IAAA,CAAAxE,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACqD,iBAAiB,CAACb,QAAQ,EAAEE,QAAQ,CAAC;IAAC;IAAA9D,cAAA,GAAAoB,CAAA;IAE7D,OAAO;MACLwC,QAAQ;MACRE,QAAQ;MACRE,OAAO;MACPE,WAAW;MACXM,SAAS;MACTE,QAAQ,EAAE;QACRC,eAAe,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;QACzCC,QAAQ,EAAEC,IAAI,CAACC,GAAG,CAACpC,QAAQ,CAACkC,QAAQ,EAAE1B,cAAc,CAAC0B,QAAQ,CAAC;QAC9DjD,OAAO,EAAE,IAAI,CAACoD,OAAO;QACrBC,KAAK,EAAE;UACL1B,MAAM,EAAE,QAAQ;UAChB2B,QAAQ,EAAE,UAAU;UACpBpC,QAAQ,EAAE,KAAK;UACfqC,WAAW,EAAE;;;KAGlB;EACH;EAEA;;;EAGQ,OAAOvB,oBAAoBA,CACjCtB,WAAgC,EAChCa,cAAmB,EACnBZ,YAAwB;IAAA;IAAAxC,cAAA,GAAAqB,CAAA;IAExB,MAAMuC,QAAQ;IAAA;IAAA,CAAA5D,cAAA,GAAAoB,CAAA,QAAoB,EAAE;IAEpC;IACA,MAAMiE,MAAM;IAAA;IAAA,CAAArF,cAAA,GAAAoB,CAAA;IAAG;IAAA,CAAApB,cAAA,GAAAsB,CAAA,UAAAkB,YAAY;IAAA;IAAA,CAAAxC,cAAA,GAAAsB,CAAA,UAAI,IAAI,CAACgE,qBAAqB,CAAC/C,WAAW,CAACiB,MAAM,CAAC;IAE7E;IAAA;IAAAxD,cAAA,GAAAoB,CAAA;IACA,KAAK,IAAImE,CAAC;IAAA;IAAA,CAAAvF,cAAA,GAAAoB,CAAA,QAAG,CAAC,GAAEmE,CAAC,GAAGF,MAAM,CAAC7B,MAAM,GAAG,CAAC,EAAE+B,CAAC,EAAE,EAAE;MAC1C,MAAMC,OAAO;MAAA;MAAA,CAAAxF,cAAA,GAAAoB,CAAA,QAAkB;QAC7BqE,EAAE,EAAE,WAAWF,CAAC,EAAE;QAClBG,UAAU,EAAEL,MAAM,CAACE,CAAC,CAAC;QACrBI,QAAQ,EAAEN,MAAM,CAACE,CAAC,GAAG,CAAC,CAAC;QACvBK,QAAQ,EAAErD,WAAW,CAACgB,iBAAiB;QACvCsC,YAAY,EAAE,UAAU;QACxBC,UAAU,EAAE;UACVF,QAAQ,EAAErD,WAAW,CAACgB;SACvB;QACDE,QAAQ,EAAElB,WAAW,CAACkB,QAAQ;QAC9BV,QAAQ,EAAER,WAAW,CAACQ,QAAQ;QAC9BgD,YAAY,EAAE3C,cAAc,CAAC4C,YAAY,GAAGX,MAAM,CAAC7B,MAAM;QACzDyC,YAAY,EAAE7C,cAAc,CAAC6C,YAAY;QACzCC,cAAc,EAAE9C,cAAc,CAAC8C,cAAc;QAC7CC,UAAU,EAAE/C,cAAc,CAAC+C,UAAU;QACrCC,gBAAgB,EAAE;UAChBC,KAAK,EAAE,IAAI,CAACC,eAAe,CAAC/D,WAAW,CAACQ,QAAQ,CAAC;UACjDwD,OAAO,EAAE,GAAG;UACZC,WAAW,EAAE;;OAEhB;MAAC;MAAAxG,cAAA,GAAAoB,CAAA;MACFwC,QAAQ,CAAC6C,IAAI,CAACjB,OAAO,CAAC;IACxB;IAAC;IAAAxF,cAAA,GAAAoB,CAAA;IAED,OAAOwC,QAAQ;EACjB;EAEA;;;EAGQ,OAAOG,kBAAkBA,CAC/BD,QAAe,EACfF,QAAyB;IAAA;IAAA5D,cAAA,GAAAqB,CAAA;IAEzB,MAAMqF,UAAU;IAAA;IAAA,CAAA1G,cAAA,GAAAoB,CAAA,QAAgB,EAAE;IAAC;IAAApB,cAAA,GAAAoB,CAAA;IAEnC0C,QAAQ,CAAC6C,OAAO,CAAC,CAACC,OAAO,EAAEC,KAAK,KAAI;MAAA;MAAA7G,cAAA,GAAAqB,CAAA;MAClC;MACA,MAAMyF,YAAY;MAAA;MAAA,CAAA9G,cAAA,GAAAoB,CAAA,QAAG2D,IAAI,CAACC,GAAG,CAAC6B,KAAK,EAAEjD,QAAQ,CAACJ,MAAM,GAAG,CAAC,CAAC;MACzD,MAAMuD,QAAQ;MAAA;MAAA,CAAA/G,cAAA,GAAAoB,CAAA;MAAG;MAAA,CAAApB,cAAA,GAAAsB,CAAA,UAAAsC,QAAQ,CAACkD,YAAY,CAAC,EAAEnB,QAAQ;MAAA;MAAA,CAAA3F,cAAA,GAAAsB,CAAA,UAAI;QAAE0F,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAC,CAAE;MAEzE,MAAMC,SAAS;MAAA;MAAA,CAAAnH,cAAA,GAAAoB,CAAA,QAAc;QAC3BqE,EAAE,EAAE,WAAWoB,KAAK,EAAE;QACtB5F,IAAI,EAAE2F,OAAO,CAAC3F,IAAI;QAClB8F,QAAQ;QACRK,WAAW,EAAE;UACXC,QAAQ,EAAE;YAAEL,CAAC,EAAE,CAAC;YAAEC,CAAC,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAC,CAAE;UAC9BI,SAAS,EAAE;YAAEN,CAAC,EAAE,CAAC;YAAEC,CAAC,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAC;SAC9B;QACDK,WAAW,EAAE,CAAC,WAAWT,YAAY,EAAE,EAAE,WAAWA,YAAY,GAAG,CAAC,EAAE,CAAC,CAACU,MAAM,CAACC,OAAO,CAAC;QACvF1B,YAAY;QAAE;QAAA,CAAA/F,cAAA,GAAAsB,CAAA,UAAAsF,OAAO,CAACb,YAAY;QAAA;QAAA,CAAA/F,cAAA,GAAAsB,CAAA,UAAI,CAAC;QACvCoG,OAAO;QAAE;QAAA,CAAA1H,cAAA,GAAAsB,CAAA,UAAAsF,OAAO,CAACc,OAAO;QAAA;QAAA,CAAA1H,cAAA,GAAAsB,CAAA,UAAI,CAAC;QAC7B8E,gBAAgB,EAAE;UAChBC,KAAK,EAAE,IAAI,CAACsB,eAAe,CAACf,OAAO,CAAC3F,IAAI,CAAC;UACzCsF,OAAO,EAAE,GAAG;UACZC,WAAW,EAAE,KAAK;UAClBoB,KAAK,EAAE,IAAI,CAACC,eAAe,CAACjB,OAAO,CAAC3F,IAAI;;OAE3C;MAAC;MAAAjB,cAAA,GAAAoB,CAAA;MACFsF,UAAU,CAACD,IAAI,CAACU,SAAS,CAAC;IAC5B,CAAC,CAAC;IAAC;IAAAnH,cAAA,GAAAoB,CAAA;IAEH,OAAOsF,UAAU;EACnB;EAEA;;;EAGQ,OAAOzC,4BAA4BA,CACzCL,QAAyB,EACzBb,QAAgB;IAAA;IAAA/C,cAAA,GAAAqB,CAAA;IAEhB,MAAMiG,SAAS;IAAA;IAAA,CAAAtH,cAAA,GAAAoB,CAAA,QAAc,EAAE;IAC/B,MAAM0G,aAAa;IAAA;IAAA,CAAA9H,cAAA,GAAAoB,CAAA,QAAa,EAAE;IAClC,MAAM+D,QAAQ;IAAA;IAAA,CAAAnF,cAAA,GAAAoB,CAAA,QAAa,EAAE;IAAC;IAAApB,cAAA,GAAAoB,CAAA;IAE9BwC,QAAQ,CAAC+C,OAAO,CAACnB,OAAO,IAAG;MAAA;MAAAxF,cAAA,GAAAqB,CAAA;MACzB;MACA,MAAM0G,EAAE;MAAA;MAAA,CAAA/H,cAAA,GAAAoB,CAAA,QAAGoE,OAAO,CAACG,QAAQ,CAACqB,CAAC,GAAGxB,OAAO,CAACE,UAAU,CAACsB,CAAC;MACpD,MAAMgB,EAAE;MAAA;MAAA,CAAAhI,cAAA,GAAAoB,CAAA,QAAGoE,OAAO,CAACG,QAAQ,CAACsB,CAAC,GAAGzB,OAAO,CAACE,UAAU,CAACuB,CAAC;MACpD,MAAMgB,EAAE;MAAA;MAAA,CAAAjI,cAAA,GAAAoB,CAAA,QAAGoE,OAAO,CAACG,QAAQ,CAACuB,CAAC,GAAG1B,OAAO,CAACE,UAAU,CAACwB,CAAC;MACpD,MAAM1D,MAAM;MAAA;MAAA,CAAAxD,cAAA,GAAAoB,CAAA,QAAG2D,IAAI,CAACmD,IAAI,CAACH,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC;MAAC;MAAAjI,cAAA,GAAAoB,CAAA;MAEtDkG,SAAS,CAACb,IAAI,CAAC;QACbO,CAAC,EAAEe,EAAE,GAAGvE,MAAM;QACdyD,CAAC,EAAEe,EAAE,GAAGxE,MAAM;QACd0D,CAAC,EAAEe,EAAE,GAAGzE;OACT,CAAC;MAAC;MAAAxD,cAAA,GAAAoB,CAAA;MAEH0G,aAAa,CAACrB,IAAI,CAAC1D,QAAQ,CAAC;MAAC;MAAA/C,cAAA,GAAAoB,CAAA;MAC7B+D,QAAQ,CAACsB,IAAI,CAACjB,OAAO,CAACO,YAAY,CAAC;IACrC,CAAC,CAAC;IAAC;IAAA/F,cAAA,GAAAoB,CAAA;IAEH,OAAO;MACLkG,SAAS;MACTvE,QAAQ,EAAE+E,aAAa;MACvB3C;KACD;EACH;EAEA;;;EAGQ,OAAOV,iBAAiBA,CAC9Bb,QAAyB,EACzBE,QAAqB;IAAA;IAAA9D,cAAA,GAAAqB,CAAA;IAErB,MAAM8G,cAAc;IAAA;IAAA,CAAAnI,cAAA,GAAAoB,CAAA,QAAG,CAAC,GAAGwC,QAAQ,CAACwE,GAAG,CAAChH,CAAC,IAAI;MAAA;MAAApB,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAAA,OAAAA,CAAC,CAAC2E,YAAY;IAAZ,CAAY,CAAC,EAAE,GAAGjC,QAAQ,CAACsE,GAAG,CAAC/G,CAAC,IAAI;MAAA;MAAArB,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAAA,OAAAC,CAAC,CAAC0E,YAAY;IAAZ,CAAY,CAAC,CAAC;IACnG,MAAMsC,cAAc;IAAA;IAAA,CAAArI,cAAA,GAAAoB,CAAA,QAAGwC,QAAQ,CAACwE,GAAG,CAAChH,CAAC,IAAI;MAAA;MAAApB,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAAA,OAAAA,CAAC,CAAC2B,QAAQ;IAAR,CAAQ,CAAC;IAAC;IAAA/C,cAAA,GAAAoB,CAAA;IAErD,OAAO;MACL+D,QAAQ,EAAE,IAAI,CAACmD,cAAc,CAACH,cAAc,EAAE,UAAU,CAAC;MACzDpF,QAAQ,EAAE,IAAI,CAACuF,cAAc,CAACD,cAAc,EAAE,UAAU,CAAC;MACzDE,UAAU,EAAE,IAAI,CAACD,cAAc,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,YAAY,CAAC;MACnElD,WAAW,EAAE,IAAI,CAACkD,cAAc,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,aAAa;KACjE;EACH;EAEA;;;EAGQ,OAAOhD,qBAAqBA,CAAC9B,MAAc;IAAA;IAAAxD,cAAA,GAAAqB,CAAA;IACjD,MAAMuC,QAAQ;IAAA;IAAA,CAAA5D,cAAA,GAAAoB,CAAA,QAAG2D,IAAI,CAACyD,GAAG,CAAC,CAAC,EAAEzD,IAAI,CAAC0D,KAAK,CAACjF,MAAM,GAAG,EAAE,CAAC,CAAC;IACrD,MAAM6B,MAAM;IAAA;IAAA,CAAArF,cAAA,GAAAoB,CAAA,QAAc,EAAE;IAAC;IAAApB,cAAA,GAAAoB,CAAA;IAE7B,KAAK,IAAImE,CAAC;IAAA;IAAA,CAAAvF,cAAA,GAAAoB,CAAA,QAAG,CAAC,GAAEmE,CAAC,IAAI3B,QAAQ,EAAE2B,CAAC,EAAE,EAAE;MAAA;MAAAvF,cAAA,GAAAoB,CAAA;MAClCiE,MAAM,CAACoB,IAAI,CAAC;QACVO,CAAC,EAAGzB,CAAC,GAAG/B,MAAM,GAAII,QAAQ;QAC1BqD,CAAC,EAAE,CAAC;QACJC,CAAC,EAAE;OACJ,CAAC;IACJ;IAAC;IAAAlH,cAAA,GAAAoB,CAAA;IAED,OAAOiE,MAAM;EACf;EAEQ,OAAOiB,eAAeA,CAACvD,QAAgB;IAAA;IAAA/C,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAC7C,IAAI2B,QAAQ,GAAG,IAAI,EAAE;MAAA;MAAA/C,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,OAAO,SAAS;IAAA,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA,EAAC;IAAAtB,cAAA,GAAAoB,CAAA;IACvC,IAAI2B,QAAQ,GAAG,IAAI,EAAE;MAAA;MAAA/C,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,OAAO,SAAS;IAAA,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA,EAAC;IAAAtB,cAAA,GAAAoB,CAAA;IACvC,IAAI2B,QAAQ,GAAG,IAAI,EAAE;MAAA;MAAA/C,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,OAAO,SAAS;IAAA,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA,EAAC;IAAAtB,cAAA,GAAAoB,CAAA;IACvC,OAAO,SAAS,CAAC,CAAC;EACpB;EAEQ,OAAOuG,eAAeA,CAAC1G,IAAY;IAAA;IAAAjB,cAAA,GAAAqB,CAAA;IACzC,MAAMqH,QAAQ;IAAA;IAAA,CAAA1I,cAAA,GAAAoB,CAAA,QAA2B;MACvC,iBAAiB,EAAE,SAAS;MAC5B,kBAAkB,EAAE,SAAS;MAC7B,YAAY,EAAE,SAAS;MACvB,cAAc,EAAE,SAAS;MACzB,kBAAkB,EAAE,SAAS;MAC7B,cAAc,EAAE,SAAS;MACzB,YAAY,EAAE,SAAS;MACvB,UAAU,EAAE,SAAS;MACrB,SAAS,EAAE;KACZ;IAAC;IAAApB,cAAA,GAAAoB,CAAA;IACF,OAAO,2BAAApB,cAAA,GAAAsB,CAAA,UAAAoH,QAAQ,CAACzH,IAAI,CAAC;IAAA;IAAA,CAAAjB,cAAA,GAAAsB,CAAA,UAAIoH,QAAQ,CAACC,OAAO;EAC3C;EAEQ,OAAOd,eAAeA,CAAC5G,IAAY;IAAA;IAAAjB,cAAA,GAAAqB,CAAA;IACzC,MAAMuH,QAAQ;IAAA;IAAA,CAAA5I,cAAA,GAAAoB,CAAA,QAA2B;MACvC,iBAAiB,EAAE,qBAAqB;MACxC,kBAAkB,EAAE,sBAAsB;MAC1C,YAAY,EAAE,gBAAgB;MAC9B,cAAc,EAAE,kBAAkB;MAClC,kBAAkB,EAAE,sBAAsB;MAC1C,cAAc,EAAE,kBAAkB;MAClC,YAAY,EAAE,gBAAgB;MAC9B,UAAU,EAAE,cAAc;MAC1B,SAAS,EAAE;KACZ;IAAC;IAAApB,cAAA,GAAAoB,CAAA;IACF,OAAO,2BAAApB,cAAA,GAAAsB,CAAA,UAAAsH,QAAQ,CAAC3H,IAAI,CAAC;IAAA;IAAA,CAAAjB,cAAA,GAAAsB,CAAA,UAAIsH,QAAQ,CAACD,OAAO;EAC3C;EAEQ,OAAOL,cAAcA,CAACO,MAAgB,EAAE5H,IAAY;IAAA;IAAAjB,cAAA,GAAAqB,CAAA;IAC1D,MAAM2D,GAAG;IAAA;IAAA,CAAAhF,cAAA,GAAAoB,CAAA,QAAG2D,IAAI,CAACC,GAAG,CAAC,GAAG6D,MAAM,CAAC;IAC/B,MAAML,GAAG;IAAA;IAAA,CAAAxI,cAAA,GAAAoB,CAAA,QAAG2D,IAAI,CAACyD,GAAG,CAAC,GAAGK,MAAM,CAAC;IAE/B,MAAMC,YAAY;IAAA;IAAA,CAAA9I,cAAA,GAAAoB,CAAA,QAA6B;MAC7C+D,QAAQ,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;MACtDpC,QAAQ,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;MACtDwF,UAAU,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;MACxDnD,WAAW,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;KACzD;IAED,MAAM2D,MAAM;IAAA;IAAA,CAAA/I,cAAA,GAAAoB,CAAA;IAAG;IAAA,CAAApB,cAAA,GAAAsB,CAAA,WAAAwH,YAAY,CAAC7H,IAAI,CAAC;IAAA;IAAA,CAAAjB,cAAA,GAAAsB,CAAA,WAAIwH,YAAY,CAAC3D,QAAQ;IAAC;IAAAnF,cAAA,GAAAoB,CAAA;IAE3D,OAAO;MACL4D,GAAG;MACHwD,GAAG;MACHO,MAAM,EAAEA,MAAM,CAACX,GAAG,CAAC,CAAC/B,KAAK,EAAEQ,KAAK,KAAM;QAAA;QAAA7G,cAAA,GAAAqB,CAAA;QAAArB,cAAA,GAAAoB,CAAA;QAAA;UACpC4H,KAAK,EAAEhE,GAAG,GAAG,CAACwD,GAAG,GAAGxD,GAAG,KAAK6B,KAAK,IAAIkC,MAAM,CAACvF,MAAM,GAAG,CAAC,CAAC,CAAC;UACxD6C;SACD;OAAC;KACH;EACH;EAEQ,OAAOhC,yBAAyBA,CAAC5B,YAAkC;IAAA;IAAAzC,cAAA,GAAAqB,CAAA;IACzE;IACA,MAAM4H,aAAa;IAAA;IAAA,CAAAjJ,cAAA,GAAAoB,CAAA,QAAGqB,YAAY,CAACyG,gBAAgB;IACnD,MAAMC,cAAc;IAAA;IAAA,CAAAnJ,cAAA,GAAAoB,CAAA,QAAGqB,YAAY,CAAC0B,iBAAiB;IAAC;IAAAnE,cAAA,GAAAoB,CAAA;IACtD,OAAO2D,IAAI,CAACyD,GAAG,CAAC,GAAG,EAAEzD,IAAI,CAACC,GAAG,CAAC,GAAG,EAAEiE,aAAa,GAAGE,cAAc,CAAC,CAAC;EACrE;EAEQ,OAAO5E,yBAAyBA,CACtC9B,YAAkC,EAClCuB,OAAe;IAAA;IAAAhE,cAAA,GAAAqB,CAAA;IAEf;IACA,MAAM+H,aAAa;IAAA;IAAA,CAAApJ,cAAA,GAAAoB,CAAA,QAAG,GAAG,EAAC,CAAC;IAC3B,MAAMiI,eAAe;IAAA;IAAA,CAAArJ,cAAA,GAAAoB,CAAA,QAAG,GAAG,EAAC,CAAC;IAE7B;IAAA;IAAApB,cAAA,GAAAoB,CAAA;IACA,OAAQ4C,OAAO,GAAGvB,YAAY,CAAC0B,iBAAiB,IAAK,IAAI,GAAGiF,aAAa,GAAGC,eAAe,CAAC;EAC9F;;;;AAxSFC,OAAA,CAAAjH,yBAAA,GAAAA,yBAAA;AAySC;AAAArC,cAAA,GAAAoB,CAAA;AAxSyBiB,yBAAA,CAAA4C,OAAO,GAAG,OAAO", "ignoreList": []}