e44d4dc4006cfe41976bb22184673bf9
"use strict";

/**
 * Enhanced Offline Service with Dexie.js Integration
 *
 * Provides high-performance offline-first data operations with:
 * - Dexie.js for optimized IndexedDB operations
 * - Automatic sync queue management
 * - Conflict resolution strategies
 * - Performance monitoring
 * - Data integrity validation
 */
/* istanbul ignore next */
function cov_2mp44saf55() {
  var path = "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\frontend\\lib\\services\\EnhancedOfflineService.ts";
  var hash = "495937bde9aed42e63bb09b827c5797268149dfc";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\frontend\\lib\\services\\EnhancedOfflineService.ts",
    statementMap: {
      "0": {
        start: {
          line: 12,
          column: 0
        },
        end: {
          line: 12,
          column: 62
        }
      },
      "1": {
        start: {
          line: 13,
          column: 0
        },
        end: {
          line: 13,
          column: 40
        }
      },
      "2": {
        start: {
          line: 14,
          column: 0
        },
        end: {
          line: 14,
          column: 68
        }
      },
      "3": {
        start: {
          line: 15,
          column: 24
        },
        end: {
          line: 15,
          column: 60
        }
      },
      "4": {
        start: {
          line: 16,
          column: 17
        },
        end: {
          line: 16,
          column: 34
        }
      },
      "5": {
        start: {
          line: 22,
          column: 8
        },
        end: {
          line: 22,
          column: 16
        }
      },
      "6": {
        start: {
          line: 23,
          column: 8
        },
        end: {
          line: 23,
          column: 30
        }
      },
      "7": {
        start: {
          line: 24,
          column: 8
        },
        end: {
          line: 24,
          column: 61
        }
      },
      "8": {
        start: {
          line: 25,
          column: 8
        },
        end: {
          line: 32,
          column: 10
        }
      },
      "9": {
        start: {
          line: 33,
          column: 8
        },
        end: {
          line: 40,
          column: 10
        }
      },
      "10": {
        start: {
          line: 41,
          column: 8
        },
        end: {
          line: 47,
          column: 10
        }
      },
      "11": {
        start: {
          line: 48,
          column: 8
        },
        end: {
          line: 48,
          column: 36
        }
      },
      "12": {
        start: {
          line: 49,
          column: 8
        },
        end: {
          line: 49,
          column: 26
        }
      },
      "13": {
        start: {
          line: 55,
          column: 8
        },
        end: {
          line: 67,
          column: 9
        }
      },
      "14": {
        start: {
          line: 56,
          column: 12
        },
        end: {
          line: 56,
          column: 33
        }
      },
      "15": {
        start: {
          line: 57,
          column: 12
        },
        end: {
          line: 57,
          column: 42
        }
      },
      "16": {
        start: {
          line: 58,
          column: 12
        },
        end: {
          line: 60,
          column: 13
        }
      },
      "17": {
        start: {
          line: 59,
          column: 16
        },
        end: {
          line: 59,
          column: 37
        }
      },
      "18": {
        start: {
          line: 61,
          column: 12
        },
        end: {
          line: 61,
          column: 37
        }
      },
      "19": {
        start: {
          line: 62,
          column: 12
        },
        end: {
          line: 62,
          column: 66
        }
      },
      "20": {
        start: {
          line: 65,
          column: 12
        },
        end: {
          line: 65,
          column: 85
        }
      },
      "21": {
        start: {
          line: 66,
          column: 12
        },
        end: {
          line: 66,
          column: 38
        }
      },
      "22": {
        start: {
          line: 73,
          column: 26
        },
        end: {
          line: 73,
          column: 43
        }
      },
      "23": {
        start: {
          line: 74,
          column: 8
        },
        end: {
          line: 92,
          column: 9
        }
      },
      "24": {
        start: {
          line: 75,
          column: 25
        },
        end: {
          line: 75,
          column: 91
        }
      },
      "25": {
        start: {
          line: 76,
          column: 36
        },
        end: {
          line: 82,
          column: 13
        }
      },
      "26": {
        start: {
          line: 83,
          column: 12
        },
        end: {
          line: 83,
          column: 57
        }
      },
      "27": {
        start: {
          line: 84,
          column: 12
        },
        end: {
          line: 84,
          column: 52
        }
      },
      "28": {
        start: {
          line: 85,
          column: 12
        },
        end: {
          line: 85,
          column: 45
        }
      },
      "29": {
        start: {
          line: 86,
          column: 12
        },
        end: {
          line: 86,
          column: 77
        }
      },
      "30": {
        start: {
          line: 87,
          column: 12
        },
        end: {
          line: 87,
          column: 24
        }
      },
      "31": {
        start: {
          line: 90,
          column: 12
        },
        end: {
          line: 90,
          column: 62
        }
      },
      "32": {
        start: {
          line: 91,
          column: 12
        },
        end: {
          line: 91,
          column: 24
        }
      },
      "33": {
        start: {
          line: 95,
          column: 26
        },
        end: {
          line: 95,
          column: 43
        }
      },
      "34": {
        start: {
          line: 96,
          column: 8
        },
        end: {
          line: 106,
          column: 9
        }
      },
      "35": {
        start: {
          line: 97,
          column: 12
        },
        end: {
          line: 97,
          column: 55
        }
      },
      "36": {
        start: {
          line: 98,
          column: 12
        },
        end: {
          line: 98,
          column: 52
        }
      },
      "37": {
        start: {
          line: 99,
          column: 12
        },
        end: {
          line: 99,
          column: 45
        }
      },
      "38": {
        start: {
          line: 100,
          column: 12
        },
        end: {
          line: 100,
          column: 52
        }
      },
      "39": {
        start: {
          line: 101,
          column: 12
        },
        end: {
          line: 101,
          column: 60
        }
      },
      "40": {
        start: {
          line: 104,
          column: 12
        },
        end: {
          line: 104,
          column: 62
        }
      },
      "41": {
        start: {
          line: 105,
          column: 12
        },
        end: {
          line: 105,
          column: 24
        }
      },
      "42": {
        start: {
          line: 109,
          column: 26
        },
        end: {
          line: 109,
          column: 43
        }
      },
      "43": {
        start: {
          line: 110,
          column: 25
        },
        end: {
          line: 110,
          column: 42
        }
      },
      "44": {
        start: {
          line: 111,
          column: 8
        },
        end: {
          line: 132,
          column: 9
        }
      },
      "45": {
        start: {
          line: 113,
          column: 27
        },
        end: {
          line: 113,
          column: 55
        }
      },
      "46": {
        start: {
          line: 114,
          column: 12
        },
        end: {
          line: 117,
          column: 13
        }
      },
      "47": {
        start: {
          line: 115,
          column: 16
        },
        end: {
          line: 115,
          column: 62
        }
      },
      "48": {
        start: {
          line: 116,
          column: 16
        },
        end: {
          line: 116,
          column: 30
        }
      },
      "49": {
        start: {
          line: 118,
          column: 36
        },
        end: {
          line: 118,
          column: 66
        }
      },
      "50": {
        start: {
          line: 119,
          column: 12
        },
        end: {
          line: 122,
          column: 13
        }
      },
      "51": {
        start: {
          line: 120,
          column: 16
        },
        end: {
          line: 120,
          column: 56
        }
      },
      "52": {
        start: {
          line: 121,
          column: 16
        },
        end: {
          line: 121,
          column: 28
        }
      },
      "53": {
        start: {
          line: 123,
          column: 28
        },
        end: {
          line: 123,
          column: 76
        }
      },
      "54": {
        start: {
          line: 125,
          column: 12
        },
        end: {
          line: 125,
          column: 58
        }
      },
      "55": {
        start: {
          line: 126,
          column: 12
        },
        end: {
          line: 126,
          column: 52
        }
      },
      "56": {
        start: {
          line: 127,
          column: 12
        },
        end: {
          line: 127,
          column: 27
        }
      },
      "57": {
        start: {
          line: 130,
          column: 12
        },
        end: {
          line: 130,
          column: 59
        }
      },
      "58": {
        start: {
          line: 131,
          column: 12
        },
        end: {
          line: 131,
          column: 24
        }
      },
      "59": {
        start: {
          line: 135,
          column: 26
        },
        end: {
          line: 135,
          column: 43
        }
      },
      "60": {
        start: {
          line: 136,
          column: 25
        },
        end: {
          line: 136,
          column: 35
        }
      },
      "61": {
        start: {
          line: 137,
          column: 8
        },
        end: {
          line: 154,
          column: 9
        }
      },
      "62": {
        start: {
          line: 139,
          column: 27
        },
        end: {
          line: 139,
          column: 55
        }
      },
      "63": {
        start: {
          line: 140,
          column: 12
        },
        end: {
          line: 143,
          column: 13
        }
      },
      "64": {
        start: {
          line: 141,
          column: 16
        },
        end: {
          line: 141,
          column: 62
        }
      },
      "65": {
        start: {
          line: 142,
          column: 16
        },
        end: {
          line: 142,
          column: 30
        }
      },
      "66": {
        start: {
          line: 144,
          column: 37
        },
        end: {
          line: 144,
          column: 67
        }
      },
      "67": {
        start: {
          line: 145,
          column: 29
        },
        end: {
          line: 145,
          column: 90
        }
      },
      "68": {
        start: {
          line: 145,
          column: 55
        },
        end: {
          line: 145,
          column: 89
        }
      },
      "69": {
        start: {
          line: 147,
          column: 12
        },
        end: {
          line: 147,
          column: 58
        }
      },
      "70": {
        start: {
          line: 148,
          column: 12
        },
        end: {
          line: 148,
          column: 52
        }
      },
      "71": {
        start: {
          line: 149,
          column: 12
        },
        end: {
          line: 149,
          column: 28
        }
      },
      "72": {
        start: {
          line: 152,
          column: 12
        },
        end: {
          line: 152,
          column: 64
        }
      },
      "73": {
        start: {
          line: 153,
          column: 12
        },
        end: {
          line: 153,
          column: 24
        }
      },
      "74": {
        start: {
          line: 157,
          column: 26
        },
        end: {
          line: 157,
          column: 43
        }
      },
      "75": {
        start: {
          line: 158,
          column: 8
        },
        end: {
          line: 168,
          column: 9
        }
      },
      "76": {
        start: {
          line: 159,
          column: 12
        },
        end: {
          line: 159,
          column: 46
        }
      },
      "77": {
        start: {
          line: 160,
          column: 12
        },
        end: {
          line: 160,
          column: 52
        }
      },
      "78": {
        start: {
          line: 161,
          column: 12
        },
        end: {
          line: 161,
          column: 45
        }
      },
      "79": {
        start: {
          line: 162,
          column: 12
        },
        end: {
          line: 162,
          column: 52
        }
      },
      "80": {
        start: {
          line: 163,
          column: 12
        },
        end: {
          line: 163,
          column: 51
        }
      },
      "81": {
        start: {
          line: 166,
          column: 12
        },
        end: {
          line: 166,
          column: 62
        }
      },
      "82": {
        start: {
          line: 167,
          column: 12
        },
        end: {
          line: 167,
          column: 24
        }
      },
      "83": {
        start: {
          line: 174,
          column: 26
        },
        end: {
          line: 174,
          column: 43
        }
      },
      "84": {
        start: {
          line: 175,
          column: 8
        },
        end: {
          line: 195,
          column: 9
        }
      },
      "85": {
        start: {
          line: 176,
          column: 25
        },
        end: {
          line: 176,
          column: 88
        }
      },
      "86": {
        start: {
          line: 177,
          column: 32
        },
        end: {
          line: 185,
          column: 13
        }
      },
      "87": {
        start: {
          line: 186,
          column: 12
        },
        end: {
          line: 186,
          column: 55
        }
      },
      "88": {
        start: {
          line: 187,
          column: 12
        },
        end: {
          line: 187,
          column: 52
        }
      },
      "89": {
        start: {
          line: 188,
          column: 12
        },
        end: {
          line: 188,
          column: 64
        }
      },
      "90": {
        start: {
          line: 189,
          column: 12
        },
        end: {
          line: 189,
          column: 66
        }
      },
      "91": {
        start: {
          line: 190,
          column: 12
        },
        end: {
          line: 190,
          column: 24
        }
      },
      "92": {
        start: {
          line: 193,
          column: 12
        },
        end: {
          line: 193,
          column: 64
        }
      },
      "93": {
        start: {
          line: 194,
          column: 12
        },
        end: {
          line: 194,
          column: 24
        }
      },
      "94": {
        start: {
          line: 198,
          column: 26
        },
        end: {
          line: 198,
          column: 43
        }
      },
      "95": {
        start: {
          line: 199,
          column: 25
        },
        end: {
          line: 199,
          column: 54
        }
      },
      "96": {
        start: {
          line: 200,
          column: 8
        },
        end: {
          line: 216,
          column: 9
        }
      },
      "97": {
        start: {
          line: 202,
          column: 27
        },
        end: {
          line: 202,
          column: 55
        }
      },
      "98": {
        start: {
          line: 203,
          column: 12
        },
        end: {
          line: 206,
          column: 13
        }
      },
      "99": {
        start: {
          line: 204,
          column: 16
        },
        end: {
          line: 204,
          column: 62
        }
      },
      "100": {
        start: {
          line: 205,
          column: 16
        },
        end: {
          line: 205,
          column: 30
        }
      },
      "101": {
        start: {
          line: 207,
          column: 33
        },
        end: {
          line: 207,
          column: 84
        }
      },
      "102": {
        start: {
          line: 209,
          column: 12
        },
        end: {
          line: 209,
          column: 63
        }
      },
      "103": {
        start: {
          line: 210,
          column: 12
        },
        end: {
          line: 210,
          column: 52
        }
      },
      "104": {
        start: {
          line: 211,
          column: 12
        },
        end: {
          line: 211,
          column: 32
        }
      },
      "105": {
        start: {
          line: 214,
          column: 12
        },
        end: {
          line: 214,
          column: 64
        }
      },
      "106": {
        start: {
          line: 215,
          column: 12
        },
        end: {
          line: 215,
          column: 24
        }
      },
      "107": {
        start: {
          line: 222,
          column: 26
        },
        end: {
          line: 222,
          column: 43
        }
      },
      "108": {
        start: {
          line: 223,
          column: 8
        },
        end: {
          line: 243,
          column: 9
        }
      },
      "109": {
        start: {
          line: 224,
          column: 25
        },
        end: {
          line: 224,
          column: 91
        }
      },
      "110": {
        start: {
          line: 225,
          column: 26
        },
        end: {
          line: 233,
          column: 13
        }
      },
      "111": {
        start: {
          line: 234,
          column: 12
        },
        end: {
          line: 234,
          column: 50
        }
      },
      "112": {
        start: {
          line: 235,
          column: 12
        },
        end: {
          line: 235,
          column: 52
        }
      },
      "113": {
        start: {
          line: 236,
          column: 12
        },
        end: {
          line: 236,
          column: 59
        }
      },
      "114": {
        start: {
          line: 237,
          column: 12
        },
        end: {
          line: 237,
          column: 56
        }
      },
      "115": {
        start: {
          line: 238,
          column: 12
        },
        end: {
          line: 238,
          column: 24
        }
      },
      "116": {
        start: {
          line: 241,
          column: 12
        },
        end: {
          line: 241,
          column: 66
        }
      },
      "117": {
        start: {
          line: 242,
          column: 12
        },
        end: {
          line: 242,
          column: 24
        }
      },
      "118": {
        start: {
          line: 246,
          column: 26
        },
        end: {
          line: 246,
          column: 43
        }
      },
      "119": {
        start: {
          line: 247,
          column: 25
        },
        end: {
          line: 247,
          column: 49
        }
      },
      "120": {
        start: {
          line: 248,
          column: 8
        },
        end: {
          line: 264,
          column: 9
        }
      },
      "121": {
        start: {
          line: 250,
          column: 27
        },
        end: {
          line: 250,
          column: 55
        }
      },
      "122": {
        start: {
          line: 251,
          column: 12
        },
        end: {
          line: 254,
          column: 13
        }
      },
      "123": {
        start: {
          line: 252,
          column: 16
        },
        end: {
          line: 252,
          column: 62
        }
      },
      "124": {
        start: {
          line: 253,
          column: 16
        },
        end: {
          line: 253,
          column: 30
        }
      },
      "125": {
        start: {
          line: 255,
          column: 27
        },
        end: {
          line: 255,
          column: 79
        }
      },
      "126": {
        start: {
          line: 257,
          column: 12
        },
        end: {
          line: 257,
          column: 57
        }
      },
      "127": {
        start: {
          line: 258,
          column: 12
        },
        end: {
          line: 258,
          column: 52
        }
      },
      "128": {
        start: {
          line: 259,
          column: 12
        },
        end: {
          line: 259,
          column: 26
        }
      },
      "129": {
        start: {
          line: 262,
          column: 12
        },
        end: {
          line: 262,
          column: 66
        }
      },
      "130": {
        start: {
          line: 263,
          column: 12
        },
        end: {
          line: 263,
          column: 24
        }
      },
      "131": {
        start: {
          line: 270,
          column: 8
        },
        end: {
          line: 270,
          column: 38
        }
      },
      "132": {
        start: {
          line: 271,
          column: 8
        },
        end: {
          line: 271,
          column: 38
        }
      },
      "133": {
        start: {
          line: 274,
          column: 8
        },
        end: {
          line: 281,
          column: 9
        }
      },
      "134": {
        start: {
          line: 275,
          column: 31
        },
        end: {
          line: 275,
          column: 71
        }
      },
      "135": {
        start: {
          line: 276,
          column: 12
        },
        end: {
          line: 276,
          column: 66
        }
      },
      "136": {
        start: {
          line: 277,
          column: 12
        },
        end: {
          line: 277,
          column: 102
        }
      },
      "137": {
        start: {
          line: 277,
          column: 71
        },
        end: {
          line: 277,
          column: 93
        }
      },
      "138": {
        start: {
          line: 280,
          column: 12
        },
        end: {
          line: 280,
          column: 66
        }
      },
      "139": {
        start: {
          line: 285,
          column: 8
        },
        end: {
          line: 285,
          column: 72
        }
      },
      "140": {
        start: {
          line: 286,
          column: 8
        },
        end: {
          line: 286,
          column: 33
        }
      },
      "141": {
        start: {
          line: 292,
          column: 8
        },
        end: {
          line: 293,
          column: 19
        }
      },
      "142": {
        start: {
          line: 293,
          column: 12
        },
        end: {
          line: 293,
          column: 19
        }
      },
      "143": {
        start: {
          line: 294,
          column: 26
        },
        end: {
          line: 294,
          column: 55
        }
      },
      "144": {
        start: {
          line: 295,
          column: 8
        },
        end: {
          line: 295,
          column: 47
        }
      },
      "145": {
        start: {
          line: 296,
          column: 8
        },
        end: {
          line: 305,
          column: 9
        }
      },
      "146": {
        start: {
          line: 297,
          column: 12
        },
        end: {
          line: 299,
          column: 57
        }
      },
      "147": {
        start: {
          line: 302,
          column: 12
        },
        end: {
          line: 304,
          column: 57
        }
      },
      "148": {
        start: {
          line: 306,
          column: 8
        },
        end: {
          line: 306,
          column: 61
        }
      },
      "149": {
        start: {
          line: 309,
          column: 22
        },
        end: {
          line: 309,
          column: 53
        }
      },
      "150": {
        start: {
          line: 310,
          column: 8
        },
        end: {
          line: 310,
          column: 63
        }
      },
      "151": {
        start: {
          line: 311,
          column: 8
        },
        end: {
          line: 311,
          column: 46
        }
      },
      "152": {
        start: {
          line: 317,
          column: 23
        },
        end: {
          line: 317,
          column: 47
        }
      },
      "153": {
        start: {
          line: 318,
          column: 8
        },
        end: {
          line: 319,
          column: 24
        }
      },
      "154": {
        start: {
          line: 319,
          column: 12
        },
        end: {
          line: 319,
          column: 24
        }
      },
      "155": {
        start: {
          line: 320,
          column: 20
        },
        end: {
          line: 320,
          column: 30
        }
      },
      "156": {
        start: {
          line: 321,
          column: 8
        },
        end: {
          line: 324,
          column: 9
        }
      },
      "157": {
        start: {
          line: 322,
          column: 12
        },
        end: {
          line: 322,
          column: 40
        }
      },
      "158": {
        start: {
          line: 323,
          column: 12
        },
        end: {
          line: 323,
          column: 24
        }
      },
      "159": {
        start: {
          line: 325,
          column: 8
        },
        end: {
          line: 325,
          column: 27
        }
      },
      "160": {
        start: {
          line: 328,
          column: 8
        },
        end: {
          line: 332,
          column: 11
        }
      },
      "161": {
        start: {
          line: 335,
          column: 8
        },
        end: {
          line: 339,
          column: 9
        }
      },
      "162": {
        start: {
          line: 336,
          column: 12
        },
        end: {
          line: 338,
          column: 13
        }
      },
      "163": {
        start: {
          line: 337,
          column: 16
        },
        end: {
          line: 337,
          column: 44
        }
      },
      "164": {
        start: {
          line: 345,
          column: 80
        },
        end: {
          line: 345,
          column: 95
        }
      },
      "165": {
        start: {
          line: 346,
          column: 8
        },
        end: {
          line: 349,
          column: 10
        }
      },
      "166": {
        start: {
          line: 352,
          column: 8
        },
        end: {
          line: 353,
          column: 19
        }
      },
      "167": {
        start: {
          line: 353,
          column: 12
        },
        end: {
          line: 353,
          column: 19
        }
      },
      "168": {
        start: {
          line: 354,
          column: 8
        },
        end: {
          line: 358,
          column: 39
        }
      },
      "169": {
        start: {
          line: 355,
          column: 12
        },
        end: {
          line: 357,
          column: 13
        }
      },
      "170": {
        start: {
          line: 356,
          column: 16
        },
        end: {
          line: 356,
          column: 42
        }
      },
      "171": {
        start: {
          line: 361,
          column: 8
        },
        end: {
          line: 364,
          column: 9
        }
      },
      "172": {
        start: {
          line: 362,
          column: 12
        },
        end: {
          line: 362,
          column: 42
        }
      },
      "173": {
        start: {
          line: 363,
          column: 12
        },
        end: {
          line: 363,
          column: 34
        }
      },
      "174": {
        start: {
          line: 370,
          column: 8
        },
        end: {
          line: 370,
          column: 28
        }
      },
      "175": {
        start: {
          line: 371,
          column: 8
        },
        end: {
          line: 371,
          column: 32
        }
      },
      "176": {
        start: {
          line: 372,
          column: 8
        },
        end: {
          line: 372,
          column: 47
        }
      },
      "177": {
        start: {
          line: 373,
          column: 8
        },
        end: {
          line: 373,
          column: 39
        }
      },
      "178": {
        start: {
          line: 376,
          column: 8
        },
        end: {
          line: 376,
          column: 29
        }
      },
      "179": {
        start: {
          line: 377,
          column: 8
        },
        end: {
          line: 377,
          column: 30
        }
      },
      "180": {
        start: {
          line: 378,
          column: 8
        },
        end: {
          line: 378,
          column: 28
        }
      },
      "181": {
        start: {
          line: 381,
          column: 0
        },
        end: {
          line: 381,
          column: 56
        }
      },
      "182": {
        start: {
          line: 386,
          column: 20
        },
        end: {
          line: 386,
          column: 54
        }
      },
      "183": {
        start: {
          line: 388,
          column: 4
        },
        end: {
          line: 391,
          column: 7
        }
      },
      "184": {
        start: {
          line: 389,
          column: 8
        },
        end: {
          line: 389,
          column: 60
        }
      },
      "185": {
        start: {
          line: 389,
          column: 42
        },
        end: {
          line: 389,
          column: 58
        }
      },
      "186": {
        start: {
          line: 390,
          column: 8
        },
        end: {
          line: 390,
          column: 38
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 21,
            column: 4
          },
          end: {
            line: 21,
            column: 5
          }
        },
        loc: {
          start: {
            line: 21,
            column: 29
          },
          end: {
            line: 50,
            column: 5
          }
        },
        line: 21
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 54,
            column: 4
          },
          end: {
            line: 54,
            column: 5
          }
        },
        loc: {
          start: {
            line: 54,
            column: 23
          },
          end: {
            line: 68,
            column: 5
          }
        },
        line: 54
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 72,
            column: 4
          },
          end: {
            line: 72,
            column: 5
          }
        },
        loc: {
          start: {
            line: 72,
            column: 37
          },
          end: {
            line: 93,
            column: 5
          }
        },
        line: 72
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 94,
            column: 4
          },
          end: {
            line: 94,
            column: 5
          }
        },
        loc: {
          start: {
            line: 94,
            column: 39
          },
          end: {
            line: 107,
            column: 5
          }
        },
        line: 94
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 108,
            column: 4
          },
          end: {
            line: 108,
            column: 5
          }
        },
        loc: {
          start: {
            line: 108,
            column: 27
          },
          end: {
            line: 133,
            column: 5
          }
        },
        line: 108
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 134,
            column: 4
          },
          end: {
            line: 134,
            column: 5
          }
        },
        loc: {
          start: {
            line: 134,
            column: 27
          },
          end: {
            line: 155,
            column: 5
          }
        },
        line: 134
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 145,
            column: 50
          },
          end: {
            line: 145,
            column: 51
          }
        },
        loc: {
          start: {
            line: 145,
            column: 55
          },
          end: {
            line: 145,
            column: 89
          }
        },
        line: 145
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 156,
            column: 4
          },
          end: {
            line: 156,
            column: 5
          }
        },
        loc: {
          start: {
            line: 156,
            column: 30
          },
          end: {
            line: 169,
            column: 5
          }
        },
        line: 156
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 173,
            column: 4
          },
          end: {
            line: 173,
            column: 5
          }
        },
        loc: {
          start: {
            line: 173,
            column: 77
          },
          end: {
            line: 196,
            column: 5
          }
        },
        line: 173
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 197,
            column: 4
          },
          end: {
            line: 197,
            column: 5
          }
        },
        loc: {
          start: {
            line: 197,
            column: 48
          },
          end: {
            line: 217,
            column: 5
          }
        },
        line: 197
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 221,
            column: 4
          },
          end: {
            line: 221,
            column: 5
          }
        },
        loc: {
          start: {
            line: 221,
            column: 78
          },
          end: {
            line: 244,
            column: 5
          }
        },
        line: 221
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 245,
            column: 4
          },
          end: {
            line: 245,
            column: 5
          }
        },
        loc: {
          start: {
            line: 245,
            column: 49
          },
          end: {
            line: 265,
            column: 5
          }
        },
        line: 245
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 269,
            column: 4
          },
          end: {
            line: 269,
            column: 5
          }
        },
        loc: {
          start: {
            line: 269,
            column: 26
          },
          end: {
            line: 272,
            column: 5
          }
        },
        line: 269
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 273,
            column: 4
          },
          end: {
            line: 273,
            column: 5
          }
        },
        loc: {
          start: {
            line: 273,
            column: 29
          },
          end: {
            line: 282,
            column: 5
          }
        },
        line: 273
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 277,
            column: 65
          },
          end: {
            line: 277,
            column: 66
          }
        },
        loc: {
          start: {
            line: 277,
            column: 71
          },
          end: {
            line: 277,
            column: 93
          }
        },
        line: 277
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 283,
            column: 4
          },
          end: {
            line: 283,
            column: 5
          }
        },
        loc: {
          start: {
            line: 283,
            column: 25
          },
          end: {
            line: 287,
            column: 5
          }
        },
        line: 283
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 291,
            column: 4
          },
          end: {
            line: 291,
            column: 5
          }
        },
        loc: {
          start: {
            line: 291,
            column: 57
          },
          end: {
            line: 307,
            column: 5
          }
        },
        line: 291
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 308,
            column: 4
          },
          end: {
            line: 308,
            column: 5
          }
        },
        loc: {
          start: {
            line: 308,
            column: 34
          },
          end: {
            line: 312,
            column: 5
          }
        },
        line: 308
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 316,
            column: 4
          },
          end: {
            line: 316,
            column: 5
          }
        },
        loc: {
          start: {
            line: 316,
            column: 23
          },
          end: {
            line: 326,
            column: 5
          }
        },
        line: 316
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 327,
            column: 4
          },
          end: {
            line: 327,
            column: 5
          }
        },
        loc: {
          start: {
            line: 327,
            column: 34
          },
          end: {
            line: 333,
            column: 5
          }
        },
        line: 327
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 334,
            column: 4
          },
          end: {
            line: 334,
            column: 5
          }
        },
        loc: {
          start: {
            line: 334,
            column: 29
          },
          end: {
            line: 340,
            column: 5
          }
        },
        line: 334
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 344,
            column: 4
          },
          end: {
            line: 344,
            column: 5
          }
        },
        loc: {
          start: {
            line: 344,
            column: 48
          },
          end: {
            line: 350,
            column: 5
          }
        },
        line: 344
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 351,
            column: 4
          },
          end: {
            line: 351,
            column: 5
          }
        },
        loc: {
          start: {
            line: 351,
            column: 20
          },
          end: {
            line: 359,
            column: 5
          }
        },
        line: 351
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 354,
            column: 37
          },
          end: {
            line: 354,
            column: 38
          }
        },
        loc: {
          start: {
            line: 354,
            column: 49
          },
          end: {
            line: 358,
            column: 9
          }
        },
        line: 354
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 360,
            column: 4
          },
          end: {
            line: 360,
            column: 5
          }
        },
        loc: {
          start: {
            line: 360,
            column: 19
          },
          end: {
            line: 365,
            column: 5
          }
        },
        line: 360
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 369,
            column: 4
          },
          end: {
            line: 369,
            column: 5
          }
        },
        loc: {
          start: {
            line: 369,
            column: 20
          },
          end: {
            line: 374,
            column: 5
          }
        },
        line: 369
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 375,
            column: 4
          },
          end: {
            line: 375,
            column: 5
          }
        },
        loc: {
          start: {
            line: 375,
            column: 18
          },
          end: {
            line: 379,
            column: 5
          }
        },
        line: 375
      },
      "27": {
        name: "createEnhancedOfflineService",
        decl: {
          start: {
            line: 385,
            column: 15
          },
          end: {
            line: 385,
            column: 43
          }
        },
        loc: {
          start: {
            line: 385,
            column: 52
          },
          end: {
            line: 392,
            column: 1
          }
        },
        line: 385
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 388,
            column: 23
          },
          end: {
            line: 388,
            column: 24
          }
        },
        loc: {
          start: {
            line: 388,
            column: 44
          },
          end: {
            line: 391,
            column: 5
          }
        },
        line: 388
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 389,
            column: 36
          },
          end: {
            line: 389,
            column: 37
          }
        },
        loc: {
          start: {
            line: 389,
            column: 42
          },
          end: {
            line: 389,
            column: 58
          }
        },
        line: 389
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 21,
            column: 16
          },
          end: {
            line: 21,
            column: 27
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 21,
            column: 25
          },
          end: {
            line: 21,
            column: 27
          }
        }],
        line: 21
      },
      "1": {
        loc: {
          start: {
            line: 58,
            column: 12
          },
          end: {
            line: 60,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 58,
            column: 12
          },
          end: {
            line: 60,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 58
      },
      "2": {
        loc: {
          start: {
            line: 79,
            column: 23
          },
          end: {
            line: 79,
            column: 46
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 79,
            column: 23
          },
          end: {
            line: 79,
            column: 40
          }
        }, {
          start: {
            line: 79,
            column: 44
          },
          end: {
            line: 79,
            column: 46
          }
        }],
        line: 79
      },
      "3": {
        loc: {
          start: {
            line: 80,
            column: 26
          },
          end: {
            line: 80,
            column: 52
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 80,
            column: 26
          },
          end: {
            line: 80,
            column: 46
          }
        }, {
          start: {
            line: 80,
            column: 50
          },
          end: {
            line: 80,
            column: 52
          }
        }],
        line: 80
      },
      "4": {
        loc: {
          start: {
            line: 81,
            column: 27
          },
          end: {
            line: 81,
            column: 54
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 81,
            column: 27
          },
          end: {
            line: 81,
            column: 48
          }
        }, {
          start: {
            line: 81,
            column: 52
          },
          end: {
            line: 81,
            column: 54
          }
        }],
        line: 81
      },
      "5": {
        loc: {
          start: {
            line: 114,
            column: 12
          },
          end: {
            line: 117,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 114,
            column: 12
          },
          end: {
            line: 117,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 114
      },
      "6": {
        loc: {
          start: {
            line: 119,
            column: 12
          },
          end: {
            line: 122,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 119,
            column: 12
          },
          end: {
            line: 122,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 119
      },
      "7": {
        loc: {
          start: {
            line: 140,
            column: 12
          },
          end: {
            line: 143,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 140,
            column: 12
          },
          end: {
            line: 143,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 140
      },
      "8": {
        loc: {
          start: {
            line: 182,
            column: 33
          },
          end: {
            line: 182,
            column: 65
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 182,
            column: 33
          },
          end: {
            line: 182,
            column: 48
          }
        }, {
          start: {
            line: 182,
            column: 52
          },
          end: {
            line: 182,
            column: 65
          }
        }],
        line: 182
      },
      "9": {
        loc: {
          start: {
            line: 203,
            column: 12
          },
          end: {
            line: 206,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 203,
            column: 12
          },
          end: {
            line: 206,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 203
      },
      "10": {
        loc: {
          start: {
            line: 221,
            column: 61
          },
          end: {
            line: 221,
            column: 76
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 221,
            column: 74
          },
          end: {
            line: 221,
            column: 76
          }
        }],
        line: 221
      },
      "11": {
        loc: {
          start: {
            line: 251,
            column: 12
          },
          end: {
            line: 254,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 251,
            column: 12
          },
          end: {
            line: 254,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 251
      },
      "12": {
        loc: {
          start: {
            line: 291,
            column: 39
          },
          end: {
            line: 291,
            column: 55
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 291,
            column: 50
          },
          end: {
            line: 291,
            column: 55
          }
        }],
        line: 291
      },
      "13": {
        loc: {
          start: {
            line: 292,
            column: 8
          },
          end: {
            line: 293,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 292,
            column: 8
          },
          end: {
            line: 293,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 292
      },
      "14": {
        loc: {
          start: {
            line: 296,
            column: 8
          },
          end: {
            line: 305,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 296,
            column: 8
          },
          end: {
            line: 305,
            column: 9
          }
        }, {
          start: {
            line: 301,
            column: 13
          },
          end: {
            line: 305,
            column: 9
          }
        }],
        line: 296
      },
      "15": {
        loc: {
          start: {
            line: 318,
            column: 8
          },
          end: {
            line: 319,
            column: 24
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 318,
            column: 8
          },
          end: {
            line: 319,
            column: 24
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 318
      },
      "16": {
        loc: {
          start: {
            line: 321,
            column: 8
          },
          end: {
            line: 324,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 321,
            column: 8
          },
          end: {
            line: 324,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 321
      },
      "17": {
        loc: {
          start: {
            line: 336,
            column: 12
          },
          end: {
            line: 338,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 336,
            column: 12
          },
          end: {
            line: 338,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 336
      },
      "18": {
        loc: {
          start: {
            line: 352,
            column: 8
          },
          end: {
            line: 353,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 352,
            column: 8
          },
          end: {
            line: 353,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 352
      },
      "19": {
        loc: {
          start: {
            line: 355,
            column: 12
          },
          end: {
            line: 357,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 355,
            column: 12
          },
          end: {
            line: 357,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 355
      },
      "20": {
        loc: {
          start: {
            line: 355,
            column: 16
          },
          end: {
            line: 355,
            column: 75
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 355,
            column: 16
          },
          end: {
            line: 355,
            column: 47
          }
        }, {
          start: {
            line: 355,
            column: 51
          },
          end: {
            line: 355,
            column: 75
          }
        }],
        line: 355
      },
      "21": {
        loc: {
          start: {
            line: 361,
            column: 8
          },
          end: {
            line: 364,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 361,
            column: 8
          },
          end: {
            line: 364,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 361
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0
    },
    b: {
      "0": [0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0],
      "11": [0, 0],
      "12": [0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\frontend\\lib\\services\\EnhancedOfflineService.ts",
      mappings: ";AAAA;;;;;;;;;GASG;;;AAweH,oEAQC;AA9eD,6DAA0I;AAE1I,mCAAsC;AA+BtC,gFAAgF;AAChF,2BAA2B;AAC3B,gFAAgF;AAEhF,MAAa,sBAAuB,SAAQ,qBAAY;IAQtD,YAAY,SAAwC,EAAE;QACpD,KAAK,EAAE,CAAC;QAHF,cAAS,GAA0B,IAAI,CAAC;QAK9C,IAAI,CAAC,EAAE,GAAG,IAAA,mCAAmB,GAAE,CAAC;QAChC,IAAI,CAAC,MAAM,GAAG;YACZ,cAAc,EAAE,KAAK,EAAE,kCAAkC;YACzD,cAAc,EAAE,KAAK,EAAE,aAAa;YACpC,gBAAgB,EAAE,CAAC;YACnB,2BAA2B,EAAE,IAAI;YACjC,oBAAoB,EAAE,IAAI;YAC1B,GAAG,MAAM;SACV,CAAC;QAEF,IAAI,CAAC,UAAU,GAAG;YAChB,QAAQ,EAAE,KAAK;YACf,eAAe,EAAE,IAAI;YACrB,kBAAkB,EAAE,IAAI;YACxB,iBAAiB,EAAE,CAAC;YACpB,gBAAgB,EAAE,CAAC;YACnB,cAAc,EAAE,KAAK;SACtB,CAAC;QAEF,IAAI,CAAC,kBAAkB,GAAG;YACxB,gBAAgB,EAAE,CAAC;YACnB,YAAY,EAAE,CAAC;YACf,YAAY,EAAE,CAAC;YACf,YAAY,EAAE,CAAC;YACf,eAAe,EAAE,IAAI,IAAI,EAAE;SAC5B,CAAC;QAEF,IAAI,CAAC,UAAU,GAAG,IAAI,GAAG,EAAE,CAAC;QAE5B,IAAI,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IAED,gFAAgF;IAChF,iBAAiB;IACjB,gFAAgF;IAExE,KAAK,CAAC,UAAU;QACtB,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC;YACrB,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAE9B,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;gBAC/B,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACzB,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;QACxD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,kDAAkD,EAAE,KAAK,CAAC,CAAC;YACzE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAC5B,CAAC;IACH,CAAC;IAED,gFAAgF;IAChF,qBAAqB;IACrB,gFAAgF;IAEhF,KAAK,CAAC,aAAa,CAAC,WAAgC;QAClD,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QAEpC,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,WAAW,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;YAEhF,MAAM,eAAe,GAA4E;gBAC/F,GAAG,WAAW;gBACd,IAAI;gBACJ,KAAK,EAAE,WAAW,CAAC,KAAK,IAAI,EAAE;gBAC9B,QAAQ,EAAE,WAAW,CAAC,QAAQ,IAAI,EAAE;gBACpC,SAAS,EAAE,WAAW,CAAC,SAAS,IAAI,EAAE;aACvC,CAAC;YAEF,MAAM,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC;YAE7C,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;YACxC,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;YACjC,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,eAAe,EAAE,CAAC,CAAC;YAEjE,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAClD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,IAAY,EAAE,OAAyB;QACzD,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QAEpC,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,IAAI,EAAE,OAAc,CAAC,CAAC;YAElD,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;YACxC,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;YACjC,IAAI,CAAC,eAAe,CAAC,WAAW,IAAI,EAAE,CAAC,CAAC;YACxC,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC;QAClD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAClD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,IAAY;QAC3B,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QACpC,MAAM,QAAQ,GAAG,WAAW,IAAI,EAAE,CAAC;QAEnC,IAAI,CAAC;YACH,oBAAoB;YACpB,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YAC5C,IAAI,MAAM,EAAE,CAAC;gBACX,IAAI,CAAC,uBAAuB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;gBAC9C,OAAO,MAAM,CAAC;YAChB,CAAC;YAED,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YACvD,IAAI,CAAC,eAAe,EAAE,CAAC;gBACrB,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;gBACxC,OAAO,IAAI,CAAC;YACd,CAAC;YAED,MAAM,OAAO,GAAY,IAAI,CAAC,0BAA0B,CAAC,eAAe,CAAC,CAAC;YAE1E,mBAAmB;YACnB,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,gBAAgB;YAE/D,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;YACxC,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YAC/C,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,cAAc;QAClB,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QACpC,MAAM,QAAQ,GAAG,UAAU,CAAC;QAE5B,IAAI,CAAC;YACH,oBAAoB;YACpB,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YAC5C,IAAI,MAAM,EAAE,CAAC;gBACX,IAAI,CAAC,uBAAuB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;gBAC9C,OAAO,MAAM,CAAC;YAChB,CAAC;YAED,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,cAAc,EAAE,CAAC;YACxD,MAAM,QAAQ,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC,CAAC,CAAC,CAAC;YAE/E,mBAAmB;YACnB,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC,eAAe;YAE9D,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;YACxC,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACpD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,IAAY;QAC9B,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QAEpC,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YAElC,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;YACxC,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;YACjC,IAAI,CAAC,eAAe,CAAC,WAAW,IAAI,EAAE,CAAC,CAAC;YACxC,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC;QACzC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAClD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,gFAAgF;IAChF,yBAAyB;IACzB,gFAAgF;IAEhF,KAAK,CAAC,eAAe,CACnB,WAAmB,EACnB,KAAuB,EACvB,MAAyB,EACzB,WAAoB,EACpB,QAAiB;QAEjB,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QAEpC,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,QAAQ,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;YAE7E,MAAM,WAAW,GAAiE;gBAChF,IAAI;gBACJ,WAAW;gBACX,WAAW;gBACX,QAAQ;gBACR,eAAe,EAAE,KAAK,CAAC,SAAS,IAAI,aAAa;gBACjD,SAAS,EAAE,KAAK;gBAChB,MAAM;aACP,CAAC;YAEF,MAAM,IAAI,CAAC,EAAE,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;YAE3C,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;YACxC,IAAI,CAAC,eAAe,CAAC,gBAAgB,WAAW,EAAE,CAAC,CAAC;YACpD,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;YAEtD,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACpD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,wBAAwB,CAAC,WAAmB;QAChD,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QACpC,MAAM,QAAQ,GAAG,gBAAgB,WAAW,EAAE,CAAC;QAE/C,IAAI,CAAC;YACH,oBAAoB;YACpB,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YAC5C,IAAI,MAAM,EAAE,CAAC;gBACX,IAAI,CAAC,uBAAuB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;gBAC9C,OAAO,MAAM,CAAC;YAChB,CAAC;YAED,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,wBAAwB,CAAC,WAAW,CAAC,CAAC;YAEzE,mBAAmB;YACnB,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,YAAY,EAAE,MAAM,CAAC,CAAC,CAAC,gBAAgB;YAEpE,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;YACxC,OAAO,YAAY,CAAC;QACtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACpD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,gFAAgF;IAChF,0BAA0B;IAC1B,gFAAgF;IAEhF,KAAK,CAAC,gBAAgB,CACpB,WAAmB,EACnB,SAAwC,EACxC,QAAa,EACb,aAAkC,EAAE;QAEpC,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QAEpC,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,WAAW,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;YAEhF,MAAM,KAAK,GAAiE;gBAC1E,IAAI;gBACJ,WAAW;gBACX,SAAS;gBACT,QAAQ;gBACR,UAAU;gBACV,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,KAAK;aACd,CAAC;YAEF,MAAM,IAAI,CAAC,EAAE,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;YAEtC,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;YACxC,IAAI,CAAC,eAAe,CAAC,WAAW,WAAW,EAAE,CAAC,CAAC;YAC/C,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;YAE5C,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,yBAAyB,CAAC,WAAmB;QACjD,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QACpC,MAAM,QAAQ,GAAG,WAAW,WAAW,EAAE,CAAC;QAE1C,IAAI,CAAC;YACH,oBAAoB;YACpB,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YAC5C,IAAI,MAAM,EAAE,CAAC;gBACX,IAAI,CAAC,uBAAuB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;gBAC9C,OAAO,MAAM,CAAC;YAChB,CAAC;YAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,yBAAyB,CAAC,WAAW,CAAC,CAAC;YAEpE,mBAAmB;YACnB,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,gBAAgB;YAE9D,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;YACxC,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,gFAAgF;IAChF,kBAAkB;IAClB,gFAAgF;IAEhF,KAAK,CAAC,aAAa;QACjB,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC9B,OAAO,EAAE,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;IAChC,CAAC;IAEO,KAAK,CAAC,gBAAgB;QAC5B,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,wBAAwB,EAAE,CAAC;YAC5D,IAAI,CAAC,UAAU,CAAC,iBAAiB,GAAG,UAAU,CAAC,MAAM,CAAC;YACtD,IAAI,CAAC,UAAU,CAAC,gBAAgB,GAAG,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,KAAK,QAAQ,CAAC,CAAC,MAAM,CAAC;QAC5F,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;QACxD,CAAC;IACH,CAAC;IAED,KAAK,CAAC,YAAY;QAChB,iEAAiE;QACjE,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAC;QAChE,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAC3B,CAAC;IAED,gFAAgF;IAChF,yBAAyB;IACzB,gFAAgF;IAExE,uBAAuB,CAAC,SAAiB,EAAE,WAAoB,KAAK;QAC1E,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,2BAA2B;YAAE,OAAO;QAErD,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;QAChD,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;QAEvC,IAAI,QAAQ,EAAE,CAAC;YACb,IAAI,CAAC,kBAAkB,CAAC,YAAY;gBAClC,CAAC,IAAI,CAAC,kBAAkB,CAAC,YAAY,GAAG,CAAC,IAAI,CAAC,kBAAkB,CAAC,YAAY,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;oBACvF,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC;QACzC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,kBAAkB,CAAC,gBAAgB;gBACtC,CAAC,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,GAAG,CAAC,IAAI,CAAC,kBAAkB,CAAC,YAAY,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC;oBACnG,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC;QACzC,CAAC;QAED,IAAI,CAAC,kBAAkB,CAAC,eAAe,GAAG,IAAI,IAAI,EAAE,CAAC;IACvD,CAAC;IAED,KAAK,CAAC,qBAAqB;QACzB,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,eAAe,EAAE,CAAC;QAC9C,IAAI,CAAC,kBAAkB,CAAC,YAAY,GAAG,KAAK,CAAC,SAAS,CAAC;QACvD,OAAO,EAAE,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;IACxC,CAAC;IAED,gFAAgF;IAChF,mBAAmB;IACnB,gFAAgF;IAExE,aAAa,CAAC,GAAW;QAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACxC,IAAI,CAAC,MAAM;YAAE,OAAO,IAAI,CAAC;QAEzB,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,IAAI,GAAG,CAAC,OAAO,EAAE,GAAG,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,CAAC;YAC5D,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YAC5B,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,MAAM,CAAC,IAAI,CAAC;IACrB,CAAC;IAEO,aAAa,CAAC,GAAW,EAAE,IAAS,EAAE,GAAW;QACvD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,EAAE;YACvB,IAAI;YACJ,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,GAAG;SACJ,CAAC,CAAC;IACL,CAAC;IAEO,eAAe,CAAC,OAAe;QACrC,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,EAAE,CAAC;YACzC,IAAI,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC1B,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YAC9B,CAAC;QACH,CAAC;IACH,CAAC;IAED,gFAAgF;IAChF,kBAAkB;IAClB,gFAAgF;IAExE,0BAA0B,CAAC,eAAgC;QACjE,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,YAAY,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,WAAW,EAAE,GAAG,eAAe,CAAC;QACxF,OAAO;YACL,EAAE,EAAE,IAAI;YACR,GAAG,WAAW;SACf,CAAC;IACJ,CAAC;IAEO,aAAa;QACnB,IAAI,IAAI,CAAC,SAAS;YAAE,OAAO;QAE3B,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;YACtC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;gBAChE,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;YAC5B,CAAC;QACH,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;IACjC,CAAC;IAEO,YAAY;QAClB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC9B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACxB,CAAC;IACH,CAAC;IAED,gFAAgF;IAChF,UAAU;IACV,gFAAgF;IAEhF,KAAK,CAAC,OAAO;QACX,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;QACxB,MAAM,IAAI,CAAC,EAAE,CAAC,sBAAsB,EAAE,CAAC;QACvC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;IACjC,CAAC;IAED,KAAK,CAAC,KAAK;QACT,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;QACrB,MAAM,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC;QACtB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACtB,CAAC;CACF;AA3bD,wDA2bC;AAED,gFAAgF;AAChF,mBAAmB;AACnB,gFAAgF;AAEzE,KAAK,UAAU,4BAA4B,CAAC,MAAsC;IACvF,MAAM,OAAO,GAAG,IAAI,sBAAsB,CAAC,MAAM,CAAC,CAAC;IAEnD,0BAA0B;IAC1B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACrC,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;QACpD,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IAChC,CAAC,CAAC,CAAC;AACL,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\frontend\\lib\\services\\EnhancedOfflineService.ts"],
      sourcesContent: ["/**\r\n * Enhanced Offline Service with Dexie.js Integration\r\n * \r\n * Provides high-performance offline-first data operations with:\r\n * - Dexie.js for optimized IndexedDB operations\r\n * - Automatic sync queue management\r\n * - Conflict resolution strategies\r\n * - Performance monitoring\r\n * - Data integrity validation\r\n */\r\n\r\nimport { getSizeWiseDatabase, SizeWiseDatabase, SizeWiseProject, SizeWiseCalculation, SpatialDataLayer } from '../database/DexieDatabase';\r\nimport { Project, CalculationResult, CalculationInput } from '@/types/air-duct-sizer';\r\nimport { EventEmitter } from 'events';\r\n\r\n// =============================================================================\r\n// Service Interfaces\r\n// =============================================================================\r\n\r\nexport interface OfflineServiceConfig {\r\n  enableAutoSync: boolean;\r\n  syncIntervalMs: number;\r\n  maxRetryAttempts: number;\r\n  enablePerformanceMonitoring: boolean;\r\n  enableDataValidation: boolean;\r\n}\r\n\r\nexport interface SyncStatus {\r\n  isOnline: boolean;\r\n  lastSyncAttempt: Date | null;\r\n  lastSuccessfulSync: Date | null;\r\n  pendingOperations: number;\r\n  failedOperations: number;\r\n  syncInProgress: boolean;\r\n}\r\n\r\nexport interface PerformanceMetrics {\r\n  averageQueryTime: number;\r\n  totalQueries: number;\r\n  cacheHitRate: number;\r\n  storageUsage: number;\r\n  lastMeasurement: Date;\r\n}\r\n\r\n// =============================================================================\r\n// Enhanced Offline Service\r\n// =============================================================================\r\n\r\nexport class EnhancedOfflineService extends EventEmitter {\r\n  private db: SizeWiseDatabase;\r\n  private config: OfflineServiceConfig;\r\n  private syncStatus: SyncStatus;\r\n  private performanceMetrics: PerformanceMetrics;\r\n  private queryCache: Map<string, { data: any; timestamp: Date; ttl: number }>;\r\n  private syncTimer: NodeJS.Timeout | null = null;\r\n\r\n  constructor(config: Partial<OfflineServiceConfig> = {}) {\r\n    super();\r\n    \r\n    this.db = getSizeWiseDatabase();\r\n    this.config = {\r\n      enableAutoSync: false, // Disabled for offline-first mode\r\n      syncIntervalMs: 30000, // 30 seconds\r\n      maxRetryAttempts: 3,\r\n      enablePerformanceMonitoring: true,\r\n      enableDataValidation: true,\r\n      ...config\r\n    };\r\n\r\n    this.syncStatus = {\r\n      isOnline: false,\r\n      lastSyncAttempt: null,\r\n      lastSuccessfulSync: null,\r\n      pendingOperations: 0,\r\n      failedOperations: 0,\r\n      syncInProgress: false\r\n    };\r\n\r\n    this.performanceMetrics = {\r\n      averageQueryTime: 0,\r\n      totalQueries: 0,\r\n      cacheHitRate: 0,\r\n      storageUsage: 0,\r\n      lastMeasurement: new Date()\r\n    };\r\n\r\n    this.queryCache = new Map();\r\n\r\n    this.initialize();\r\n  }\r\n\r\n  // =============================================================================\r\n  // Initialization\r\n  // =============================================================================\r\n\r\n  private async initialize(): Promise<void> {\r\n    try {\r\n      await this.db.open();\r\n      await this.updateSyncStatus();\r\n      \r\n      if (this.config.enableAutoSync) {\r\n        this.startAutoSync();\r\n      }\r\n\r\n      this.emit('initialized');\r\n      console.log('\u2705 Enhanced Offline Service initialized');\r\n    } catch (error) {\r\n      console.error('\u274C Failed to initialize Enhanced Offline Service:', error);\r\n      this.emit('error', error);\r\n    }\r\n  }\r\n\r\n  // =============================================================================\r\n  // Project Operations\r\n  // =============================================================================\r\n\r\n  async createProject(projectData: Omit<Project, 'id'>): Promise<string> {\r\n    const startTime = performance.now();\r\n    \r\n    try {\r\n      const uuid = `project-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\r\n      \r\n      const sizeWiseProject: Omit<SizeWiseProject, 'id' | 'lastModified' | 'syncStatus' | 'version'> = {\r\n        ...projectData,\r\n        uuid,\r\n        rooms: projectData.rooms || [],\r\n        segments: projectData.segments || [],\r\n        equipment: projectData.equipment || []\r\n      };\r\n\r\n      await this.db.createProject(sizeWiseProject);\r\n      \r\n      this.recordPerformanceMetric(startTime);\r\n      this.invalidateCache('projects');\r\n      this.emit('project:created', { uuid, project: sizeWiseProject });\r\n      \r\n      return uuid;\r\n    } catch (error) {\r\n      console.error('Failed to create project:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async updateProject(uuid: string, updates: Partial<Project>): Promise<void> {\r\n    const startTime = performance.now();\r\n    \r\n    try {\r\n      await this.db.updateProject(uuid, updates as any);\r\n      \r\n      this.recordPerformanceMetric(startTime);\r\n      this.invalidateCache('projects');\r\n      this.invalidateCache(`project:${uuid}`);\r\n      this.emit('project:updated', { uuid, updates });\r\n    } catch (error) {\r\n      console.error('Failed to update project:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async getProject(uuid: string): Promise<Project | null> {\r\n    const startTime = performance.now();\r\n    const cacheKey = `project:${uuid}`;\r\n    \r\n    try {\r\n      // Check cache first\r\n      const cached = this.getCachedData(cacheKey);\r\n      if (cached) {\r\n        this.recordPerformanceMetric(startTime, true);\r\n        return cached;\r\n      }\r\n\r\n      const sizeWiseProject = await this.db.getProject(uuid);\r\n      if (!sizeWiseProject) {\r\n        this.recordPerformanceMetric(startTime);\r\n        return null;\r\n      }\r\n\r\n      const project: Project = this.convertFromSizeWiseProject(sizeWiseProject);\r\n      \r\n      // Cache the result\r\n      this.setCachedData(cacheKey, project, 300000); // 5 minutes TTL\r\n      \r\n      this.recordPerformanceMetric(startTime);\r\n      return project;\r\n    } catch (error) {\r\n      console.error('Failed to get project:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async getAllProjects(): Promise<Project[]> {\r\n    const startTime = performance.now();\r\n    const cacheKey = 'projects';\r\n    \r\n    try {\r\n      // Check cache first\r\n      const cached = this.getCachedData(cacheKey);\r\n      if (cached) {\r\n        this.recordPerformanceMetric(startTime, true);\r\n        return cached;\r\n      }\r\n\r\n      const sizeWiseProjects = await this.db.getAllProjects();\r\n      const projects = sizeWiseProjects.map(p => this.convertFromSizeWiseProject(p));\r\n      \r\n      // Cache the result\r\n      this.setCachedData(cacheKey, projects, 60000); // 1 minute TTL\r\n      \r\n      this.recordPerformanceMetric(startTime);\r\n      return projects;\r\n    } catch (error) {\r\n      console.error('Failed to get all projects:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async deleteProject(uuid: string): Promise<void> {\r\n    const startTime = performance.now();\r\n    \r\n    try {\r\n      await this.db.deleteProject(uuid);\r\n      \r\n      this.recordPerformanceMetric(startTime);\r\n      this.invalidateCache('projects');\r\n      this.invalidateCache(`project:${uuid}`);\r\n      this.emit('project:deleted', { uuid });\r\n    } catch (error) {\r\n      console.error('Failed to delete project:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // =============================================================================\r\n  // Calculation Operations\r\n  // =============================================================================\r\n\r\n  async saveCalculation(\r\n    projectUuid: string,\r\n    input: CalculationInput,\r\n    result: CalculationResult,\r\n    segmentUuid?: string,\r\n    roomUuid?: string\r\n  ): Promise<string> {\r\n    const startTime = performance.now();\r\n    \r\n    try {\r\n      const uuid = `calc-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\r\n      \r\n      const calculation: Omit<SizeWiseCalculation, 'id' | 'timestamp' | 'syncStatus'> = {\r\n        uuid,\r\n        projectUuid,\r\n        segmentUuid,\r\n        roomUuid,\r\n        calculationType: input.duct_type || 'rectangular',\r\n        inputData: input,\r\n        result\r\n      };\r\n\r\n      await this.db.saveCalculation(calculation);\r\n      \r\n      this.recordPerformanceMetric(startTime);\r\n      this.invalidateCache(`calculations:${projectUuid}`);\r\n      this.emit('calculation:saved', { uuid, calculation });\r\n      \r\n      return uuid;\r\n    } catch (error) {\r\n      console.error('Failed to save calculation:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async getCalculationsByProject(projectUuid: string): Promise<SizeWiseCalculation[]> {\r\n    const startTime = performance.now();\r\n    const cacheKey = `calculations:${projectUuid}`;\r\n    \r\n    try {\r\n      // Check cache first\r\n      const cached = this.getCachedData(cacheKey);\r\n      if (cached) {\r\n        this.recordPerformanceMetric(startTime, true);\r\n        return cached;\r\n      }\r\n\r\n      const calculations = await this.db.getCalculationsByProject(projectUuid);\r\n      \r\n      // Cache the result\r\n      this.setCachedData(cacheKey, calculations, 120000); // 2 minutes TTL\r\n      \r\n      this.recordPerformanceMetric(startTime);\r\n      return calculations;\r\n    } catch (error) {\r\n      console.error('Failed to get calculations:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // =============================================================================\r\n  // Spatial Data Operations\r\n  // =============================================================================\r\n\r\n  async saveSpatialLayer(\r\n    projectUuid: string,\r\n    layerType: SpatialDataLayer['layerType'],\r\n    geometry: any,\r\n    properties: Record<string, any> = {}\r\n  ): Promise<string> {\r\n    const startTime = performance.now();\r\n    \r\n    try {\r\n      const uuid = `spatial-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\r\n      \r\n      const layer: Omit<SpatialDataLayer, 'id' | 'lastModified' | 'syncStatus'> = {\r\n        uuid,\r\n        projectUuid,\r\n        layerType,\r\n        geometry,\r\n        properties,\r\n        visible: true,\r\n        locked: false\r\n      };\r\n\r\n      await this.db.saveSpatialLayer(layer);\r\n      \r\n      this.recordPerformanceMetric(startTime);\r\n      this.invalidateCache(`spatial:${projectUuid}`);\r\n      this.emit('spatial:saved', { uuid, layer });\r\n      \r\n      return uuid;\r\n    } catch (error) {\r\n      console.error('Failed to save spatial layer:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async getSpatialLayersByProject(projectUuid: string): Promise<SpatialDataLayer[]> {\r\n    const startTime = performance.now();\r\n    const cacheKey = `spatial:${projectUuid}`;\r\n    \r\n    try {\r\n      // Check cache first\r\n      const cached = this.getCachedData(cacheKey);\r\n      if (cached) {\r\n        this.recordPerformanceMetric(startTime, true);\r\n        return cached;\r\n      }\r\n\r\n      const layers = await this.db.getSpatialLayersByProject(projectUuid);\r\n      \r\n      // Cache the result\r\n      this.setCachedData(cacheKey, layers, 180000); // 3 minutes TTL\r\n      \r\n      this.recordPerformanceMetric(startTime);\r\n      return layers;\r\n    } catch (error) {\r\n      console.error('Failed to get spatial layers:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // =============================================================================\r\n  // Sync Operations\r\n  // =============================================================================\r\n\r\n  async getSyncStatus(): Promise<SyncStatus> {\r\n    await this.updateSyncStatus();\r\n    return { ...this.syncStatus };\r\n  }\r\n\r\n  private async updateSyncStatus(): Promise<void> {\r\n    try {\r\n      const pendingOps = await this.db.getPendingSyncOperations();\r\n      this.syncStatus.pendingOperations = pendingOps.length;\r\n      this.syncStatus.failedOperations = pendingOps.filter(op => op.status === 'failed').length;\r\n    } catch (error) {\r\n      console.error('Failed to update sync status:', error);\r\n    }\r\n  }\r\n\r\n  async forceSyncAll(): Promise<void> {\r\n    // In offline-first mode, this would prepare data for future sync\r\n    console.log('\uD83D\uDCDD Sync operations queued for future online mode');\r\n    this.emit('sync:queued');\r\n  }\r\n\r\n  // =============================================================================\r\n  // Performance Monitoring\r\n  // =============================================================================\r\n\r\n  private recordPerformanceMetric(startTime: number, cacheHit: boolean = false): void {\r\n    if (!this.config.enablePerformanceMonitoring) return;\r\n\r\n    const queryTime = performance.now() - startTime;\r\n    this.performanceMetrics.totalQueries++;\r\n    \r\n    if (cacheHit) {\r\n      this.performanceMetrics.cacheHitRate = \r\n        (this.performanceMetrics.cacheHitRate * (this.performanceMetrics.totalQueries - 1) + 1) / \r\n        this.performanceMetrics.totalQueries;\r\n    } else {\r\n      this.performanceMetrics.averageQueryTime = \r\n        (this.performanceMetrics.averageQueryTime * (this.performanceMetrics.totalQueries - 1) + queryTime) / \r\n        this.performanceMetrics.totalQueries;\r\n    }\r\n\r\n    this.performanceMetrics.lastMeasurement = new Date();\r\n  }\r\n\r\n  async getPerformanceMetrics(): Promise<PerformanceMetrics> {\r\n    const stats = await this.db.getStorageStats();\r\n    this.performanceMetrics.storageUsage = stats.totalSize;\r\n    return { ...this.performanceMetrics };\r\n  }\r\n\r\n  // =============================================================================\r\n  // Cache Management\r\n  // =============================================================================\r\n\r\n  private getCachedData(key: string): any | null {\r\n    const cached = this.queryCache.get(key);\r\n    if (!cached) return null;\r\n\r\n    const now = new Date();\r\n    if (now.getTime() - cached.timestamp.getTime() > cached.ttl) {\r\n      this.queryCache.delete(key);\r\n      return null;\r\n    }\r\n\r\n    return cached.data;\r\n  }\r\n\r\n  private setCachedData(key: string, data: any, ttl: number): void {\r\n    this.queryCache.set(key, {\r\n      data,\r\n      timestamp: new Date(),\r\n      ttl\r\n    });\r\n  }\r\n\r\n  private invalidateCache(pattern: string): void {\r\n    for (const key of this.queryCache.keys()) {\r\n      if (key.includes(pattern)) {\r\n        this.queryCache.delete(key);\r\n      }\r\n    }\r\n  }\r\n\r\n  // =============================================================================\r\n  // Utility Methods\r\n  // =============================================================================\r\n\r\n  private convertFromSizeWiseProject(sizeWiseProject: SizeWiseProject): Project {\r\n    const { id, uuid, lastModified, syncStatus, version, ...projectData } = sizeWiseProject;\r\n    return {\r\n      id: uuid,\r\n      ...projectData\r\n    };\r\n  }\r\n\r\n  private startAutoSync(): void {\r\n    if (this.syncTimer) return;\r\n\r\n    this.syncTimer = setInterval(async () => {\r\n      if (!this.syncStatus.syncInProgress && this.syncStatus.isOnline) {\r\n        await this.forceSyncAll();\r\n      }\r\n    }, this.config.syncIntervalMs);\r\n  }\r\n\r\n  private stopAutoSync(): void {\r\n    if (this.syncTimer) {\r\n      clearInterval(this.syncTimer);\r\n      this.syncTimer = null;\r\n    }\r\n  }\r\n\r\n  // =============================================================================\r\n  // Cleanup\r\n  // =============================================================================\r\n\r\n  async cleanup(): Promise<void> {\r\n    this.stopAutoSync();\r\n    this.queryCache.clear();\r\n    await this.db.clearOldSyncOperations();\r\n    this.emit('cleanup:completed');\r\n  }\r\n\r\n  async close(): Promise<void> {\r\n    await this.cleanup();\r\n    await this.db.close();\r\n    this.emit('closed');\r\n  }\r\n}\r\n\r\n// =============================================================================\r\n// Factory Function\r\n// =============================================================================\r\n\r\nexport async function createEnhancedOfflineService(config?: Partial<OfflineServiceConfig>): Promise<EnhancedOfflineService> {\r\n  const service = new EnhancedOfflineService(config);\r\n  \r\n  // Wait for initialization\r\n  return new Promise((resolve, reject) => {\r\n    service.once('initialized', () => resolve(service));\r\n    service.once('error', reject);\r\n  });\r\n}\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "495937bde9aed42e63bb09b827c5797268149dfc"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2mp44saf55 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2mp44saf55();
cov_2mp44saf55().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_2mp44saf55().s[1]++;
exports.EnhancedOfflineService = void 0;
/* istanbul ignore next */
cov_2mp44saf55().s[2]++;
exports.createEnhancedOfflineService = createEnhancedOfflineService;
const DexieDatabase_1 =
/* istanbul ignore next */
(cov_2mp44saf55().s[3]++, require("../database/DexieDatabase"));
const events_1 =
/* istanbul ignore next */
(cov_2mp44saf55().s[4]++, require("events"));
// =============================================================================
// Enhanced Offline Service
// =============================================================================
class EnhancedOfflineService extends
/* istanbul ignore next */
(events_1.EventEmitter) {
  constructor(config =
  /* istanbul ignore next */
  (cov_2mp44saf55().b[0][0]++, {})) {
    /* istanbul ignore next */
    cov_2mp44saf55().f[0]++;
    cov_2mp44saf55().s[5]++;
    super();
    /* istanbul ignore next */
    cov_2mp44saf55().s[6]++;
    this.syncTimer = null;
    /* istanbul ignore next */
    cov_2mp44saf55().s[7]++;
    this.db = (0, DexieDatabase_1.getSizeWiseDatabase)();
    /* istanbul ignore next */
    cov_2mp44saf55().s[8]++;
    this.config = {
      enableAutoSync: false,
      // Disabled for offline-first mode
      syncIntervalMs: 30000,
      // 30 seconds
      maxRetryAttempts: 3,
      enablePerformanceMonitoring: true,
      enableDataValidation: true,
      ...config
    };
    /* istanbul ignore next */
    cov_2mp44saf55().s[9]++;
    this.syncStatus = {
      isOnline: false,
      lastSyncAttempt: null,
      lastSuccessfulSync: null,
      pendingOperations: 0,
      failedOperations: 0,
      syncInProgress: false
    };
    /* istanbul ignore next */
    cov_2mp44saf55().s[10]++;
    this.performanceMetrics = {
      averageQueryTime: 0,
      totalQueries: 0,
      cacheHitRate: 0,
      storageUsage: 0,
      lastMeasurement: new Date()
    };
    /* istanbul ignore next */
    cov_2mp44saf55().s[11]++;
    this.queryCache = new Map();
    /* istanbul ignore next */
    cov_2mp44saf55().s[12]++;
    this.initialize();
  }
  // =============================================================================
  // Initialization
  // =============================================================================
  async initialize() {
    /* istanbul ignore next */
    cov_2mp44saf55().f[1]++;
    cov_2mp44saf55().s[13]++;
    try {
      /* istanbul ignore next */
      cov_2mp44saf55().s[14]++;
      await this.db.open();
      /* istanbul ignore next */
      cov_2mp44saf55().s[15]++;
      await this.updateSyncStatus();
      /* istanbul ignore next */
      cov_2mp44saf55().s[16]++;
      if (this.config.enableAutoSync) {
        /* istanbul ignore next */
        cov_2mp44saf55().b[1][0]++;
        cov_2mp44saf55().s[17]++;
        this.startAutoSync();
      } else
      /* istanbul ignore next */
      {
        cov_2mp44saf55().b[1][1]++;
      }
      cov_2mp44saf55().s[18]++;
      this.emit('initialized');
      /* istanbul ignore next */
      cov_2mp44saf55().s[19]++;
      console.log('✅ Enhanced Offline Service initialized');
    } catch (error) {
      /* istanbul ignore next */
      cov_2mp44saf55().s[20]++;
      console.error('❌ Failed to initialize Enhanced Offline Service:', error);
      /* istanbul ignore next */
      cov_2mp44saf55().s[21]++;
      this.emit('error', error);
    }
  }
  // =============================================================================
  // Project Operations
  // =============================================================================
  async createProject(projectData) {
    /* istanbul ignore next */
    cov_2mp44saf55().f[2]++;
    const startTime =
    /* istanbul ignore next */
    (cov_2mp44saf55().s[22]++, performance.now());
    /* istanbul ignore next */
    cov_2mp44saf55().s[23]++;
    try {
      const uuid =
      /* istanbul ignore next */
      (cov_2mp44saf55().s[24]++, `project-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`);
      const sizeWiseProject =
      /* istanbul ignore next */
      (cov_2mp44saf55().s[25]++, {
        ...projectData,
        uuid,
        rooms:
        /* istanbul ignore next */
        (cov_2mp44saf55().b[2][0]++, projectData.rooms) ||
        /* istanbul ignore next */
        (cov_2mp44saf55().b[2][1]++, []),
        segments:
        /* istanbul ignore next */
        (cov_2mp44saf55().b[3][0]++, projectData.segments) ||
        /* istanbul ignore next */
        (cov_2mp44saf55().b[3][1]++, []),
        equipment:
        /* istanbul ignore next */
        (cov_2mp44saf55().b[4][0]++, projectData.equipment) ||
        /* istanbul ignore next */
        (cov_2mp44saf55().b[4][1]++, [])
      });
      /* istanbul ignore next */
      cov_2mp44saf55().s[26]++;
      await this.db.createProject(sizeWiseProject);
      /* istanbul ignore next */
      cov_2mp44saf55().s[27]++;
      this.recordPerformanceMetric(startTime);
      /* istanbul ignore next */
      cov_2mp44saf55().s[28]++;
      this.invalidateCache('projects');
      /* istanbul ignore next */
      cov_2mp44saf55().s[29]++;
      this.emit('project:created', {
        uuid,
        project: sizeWiseProject
      });
      /* istanbul ignore next */
      cov_2mp44saf55().s[30]++;
      return uuid;
    } catch (error) {
      /* istanbul ignore next */
      cov_2mp44saf55().s[31]++;
      console.error('Failed to create project:', error);
      /* istanbul ignore next */
      cov_2mp44saf55().s[32]++;
      throw error;
    }
  }
  async updateProject(uuid, updates) {
    /* istanbul ignore next */
    cov_2mp44saf55().f[3]++;
    const startTime =
    /* istanbul ignore next */
    (cov_2mp44saf55().s[33]++, performance.now());
    /* istanbul ignore next */
    cov_2mp44saf55().s[34]++;
    try {
      /* istanbul ignore next */
      cov_2mp44saf55().s[35]++;
      await this.db.updateProject(uuid, updates);
      /* istanbul ignore next */
      cov_2mp44saf55().s[36]++;
      this.recordPerformanceMetric(startTime);
      /* istanbul ignore next */
      cov_2mp44saf55().s[37]++;
      this.invalidateCache('projects');
      /* istanbul ignore next */
      cov_2mp44saf55().s[38]++;
      this.invalidateCache(`project:${uuid}`);
      /* istanbul ignore next */
      cov_2mp44saf55().s[39]++;
      this.emit('project:updated', {
        uuid,
        updates
      });
    } catch (error) {
      /* istanbul ignore next */
      cov_2mp44saf55().s[40]++;
      console.error('Failed to update project:', error);
      /* istanbul ignore next */
      cov_2mp44saf55().s[41]++;
      throw error;
    }
  }
  async getProject(uuid) {
    /* istanbul ignore next */
    cov_2mp44saf55().f[4]++;
    const startTime =
    /* istanbul ignore next */
    (cov_2mp44saf55().s[42]++, performance.now());
    const cacheKey =
    /* istanbul ignore next */
    (cov_2mp44saf55().s[43]++, `project:${uuid}`);
    /* istanbul ignore next */
    cov_2mp44saf55().s[44]++;
    try {
      // Check cache first
      const cached =
      /* istanbul ignore next */
      (cov_2mp44saf55().s[45]++, this.getCachedData(cacheKey));
      /* istanbul ignore next */
      cov_2mp44saf55().s[46]++;
      if (cached) {
        /* istanbul ignore next */
        cov_2mp44saf55().b[5][0]++;
        cov_2mp44saf55().s[47]++;
        this.recordPerformanceMetric(startTime, true);
        /* istanbul ignore next */
        cov_2mp44saf55().s[48]++;
        return cached;
      } else
      /* istanbul ignore next */
      {
        cov_2mp44saf55().b[5][1]++;
      }
      const sizeWiseProject =
      /* istanbul ignore next */
      (cov_2mp44saf55().s[49]++, await this.db.getProject(uuid));
      /* istanbul ignore next */
      cov_2mp44saf55().s[50]++;
      if (!sizeWiseProject) {
        /* istanbul ignore next */
        cov_2mp44saf55().b[6][0]++;
        cov_2mp44saf55().s[51]++;
        this.recordPerformanceMetric(startTime);
        /* istanbul ignore next */
        cov_2mp44saf55().s[52]++;
        return null;
      } else
      /* istanbul ignore next */
      {
        cov_2mp44saf55().b[6][1]++;
      }
      const project =
      /* istanbul ignore next */
      (cov_2mp44saf55().s[53]++, this.convertFromSizeWiseProject(sizeWiseProject));
      // Cache the result
      /* istanbul ignore next */
      cov_2mp44saf55().s[54]++;
      this.setCachedData(cacheKey, project, 300000); // 5 minutes TTL
      /* istanbul ignore next */
      cov_2mp44saf55().s[55]++;
      this.recordPerformanceMetric(startTime);
      /* istanbul ignore next */
      cov_2mp44saf55().s[56]++;
      return project;
    } catch (error) {
      /* istanbul ignore next */
      cov_2mp44saf55().s[57]++;
      console.error('Failed to get project:', error);
      /* istanbul ignore next */
      cov_2mp44saf55().s[58]++;
      throw error;
    }
  }
  async getAllProjects() {
    /* istanbul ignore next */
    cov_2mp44saf55().f[5]++;
    const startTime =
    /* istanbul ignore next */
    (cov_2mp44saf55().s[59]++, performance.now());
    const cacheKey =
    /* istanbul ignore next */
    (cov_2mp44saf55().s[60]++, 'projects');
    /* istanbul ignore next */
    cov_2mp44saf55().s[61]++;
    try {
      // Check cache first
      const cached =
      /* istanbul ignore next */
      (cov_2mp44saf55().s[62]++, this.getCachedData(cacheKey));
      /* istanbul ignore next */
      cov_2mp44saf55().s[63]++;
      if (cached) {
        /* istanbul ignore next */
        cov_2mp44saf55().b[7][0]++;
        cov_2mp44saf55().s[64]++;
        this.recordPerformanceMetric(startTime, true);
        /* istanbul ignore next */
        cov_2mp44saf55().s[65]++;
        return cached;
      } else
      /* istanbul ignore next */
      {
        cov_2mp44saf55().b[7][1]++;
      }
      const sizeWiseProjects =
      /* istanbul ignore next */
      (cov_2mp44saf55().s[66]++, await this.db.getAllProjects());
      const projects =
      /* istanbul ignore next */
      (cov_2mp44saf55().s[67]++, sizeWiseProjects.map(p => {
        /* istanbul ignore next */
        cov_2mp44saf55().f[6]++;
        cov_2mp44saf55().s[68]++;
        return this.convertFromSizeWiseProject(p);
      }));
      // Cache the result
      /* istanbul ignore next */
      cov_2mp44saf55().s[69]++;
      this.setCachedData(cacheKey, projects, 60000); // 1 minute TTL
      /* istanbul ignore next */
      cov_2mp44saf55().s[70]++;
      this.recordPerformanceMetric(startTime);
      /* istanbul ignore next */
      cov_2mp44saf55().s[71]++;
      return projects;
    } catch (error) {
      /* istanbul ignore next */
      cov_2mp44saf55().s[72]++;
      console.error('Failed to get all projects:', error);
      /* istanbul ignore next */
      cov_2mp44saf55().s[73]++;
      throw error;
    }
  }
  async deleteProject(uuid) {
    /* istanbul ignore next */
    cov_2mp44saf55().f[7]++;
    const startTime =
    /* istanbul ignore next */
    (cov_2mp44saf55().s[74]++, performance.now());
    /* istanbul ignore next */
    cov_2mp44saf55().s[75]++;
    try {
      /* istanbul ignore next */
      cov_2mp44saf55().s[76]++;
      await this.db.deleteProject(uuid);
      /* istanbul ignore next */
      cov_2mp44saf55().s[77]++;
      this.recordPerformanceMetric(startTime);
      /* istanbul ignore next */
      cov_2mp44saf55().s[78]++;
      this.invalidateCache('projects');
      /* istanbul ignore next */
      cov_2mp44saf55().s[79]++;
      this.invalidateCache(`project:${uuid}`);
      /* istanbul ignore next */
      cov_2mp44saf55().s[80]++;
      this.emit('project:deleted', {
        uuid
      });
    } catch (error) {
      /* istanbul ignore next */
      cov_2mp44saf55().s[81]++;
      console.error('Failed to delete project:', error);
      /* istanbul ignore next */
      cov_2mp44saf55().s[82]++;
      throw error;
    }
  }
  // =============================================================================
  // Calculation Operations
  // =============================================================================
  async saveCalculation(projectUuid, input, result, segmentUuid, roomUuid) {
    /* istanbul ignore next */
    cov_2mp44saf55().f[8]++;
    const startTime =
    /* istanbul ignore next */
    (cov_2mp44saf55().s[83]++, performance.now());
    /* istanbul ignore next */
    cov_2mp44saf55().s[84]++;
    try {
      const uuid =
      /* istanbul ignore next */
      (cov_2mp44saf55().s[85]++, `calc-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`);
      const calculation =
      /* istanbul ignore next */
      (cov_2mp44saf55().s[86]++, {
        uuid,
        projectUuid,
        segmentUuid,
        roomUuid,
        calculationType:
        /* istanbul ignore next */
        (cov_2mp44saf55().b[8][0]++, input.duct_type) ||
        /* istanbul ignore next */
        (cov_2mp44saf55().b[8][1]++, 'rectangular'),
        inputData: input,
        result
      });
      /* istanbul ignore next */
      cov_2mp44saf55().s[87]++;
      await this.db.saveCalculation(calculation);
      /* istanbul ignore next */
      cov_2mp44saf55().s[88]++;
      this.recordPerformanceMetric(startTime);
      /* istanbul ignore next */
      cov_2mp44saf55().s[89]++;
      this.invalidateCache(`calculations:${projectUuid}`);
      /* istanbul ignore next */
      cov_2mp44saf55().s[90]++;
      this.emit('calculation:saved', {
        uuid,
        calculation
      });
      /* istanbul ignore next */
      cov_2mp44saf55().s[91]++;
      return uuid;
    } catch (error) {
      /* istanbul ignore next */
      cov_2mp44saf55().s[92]++;
      console.error('Failed to save calculation:', error);
      /* istanbul ignore next */
      cov_2mp44saf55().s[93]++;
      throw error;
    }
  }
  async getCalculationsByProject(projectUuid) {
    /* istanbul ignore next */
    cov_2mp44saf55().f[9]++;
    const startTime =
    /* istanbul ignore next */
    (cov_2mp44saf55().s[94]++, performance.now());
    const cacheKey =
    /* istanbul ignore next */
    (cov_2mp44saf55().s[95]++, `calculations:${projectUuid}`);
    /* istanbul ignore next */
    cov_2mp44saf55().s[96]++;
    try {
      // Check cache first
      const cached =
      /* istanbul ignore next */
      (cov_2mp44saf55().s[97]++, this.getCachedData(cacheKey));
      /* istanbul ignore next */
      cov_2mp44saf55().s[98]++;
      if (cached) {
        /* istanbul ignore next */
        cov_2mp44saf55().b[9][0]++;
        cov_2mp44saf55().s[99]++;
        this.recordPerformanceMetric(startTime, true);
        /* istanbul ignore next */
        cov_2mp44saf55().s[100]++;
        return cached;
      } else
      /* istanbul ignore next */
      {
        cov_2mp44saf55().b[9][1]++;
      }
      const calculations =
      /* istanbul ignore next */
      (cov_2mp44saf55().s[101]++, await this.db.getCalculationsByProject(projectUuid));
      // Cache the result
      /* istanbul ignore next */
      cov_2mp44saf55().s[102]++;
      this.setCachedData(cacheKey, calculations, 120000); // 2 minutes TTL
      /* istanbul ignore next */
      cov_2mp44saf55().s[103]++;
      this.recordPerformanceMetric(startTime);
      /* istanbul ignore next */
      cov_2mp44saf55().s[104]++;
      return calculations;
    } catch (error) {
      /* istanbul ignore next */
      cov_2mp44saf55().s[105]++;
      console.error('Failed to get calculations:', error);
      /* istanbul ignore next */
      cov_2mp44saf55().s[106]++;
      throw error;
    }
  }
  // =============================================================================
  // Spatial Data Operations
  // =============================================================================
  async saveSpatialLayer(projectUuid, layerType, geometry, properties =
  /* istanbul ignore next */
  (cov_2mp44saf55().b[10][0]++, {})) {
    /* istanbul ignore next */
    cov_2mp44saf55().f[10]++;
    const startTime =
    /* istanbul ignore next */
    (cov_2mp44saf55().s[107]++, performance.now());
    /* istanbul ignore next */
    cov_2mp44saf55().s[108]++;
    try {
      const uuid =
      /* istanbul ignore next */
      (cov_2mp44saf55().s[109]++, `spatial-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`);
      const layer =
      /* istanbul ignore next */
      (cov_2mp44saf55().s[110]++, {
        uuid,
        projectUuid,
        layerType,
        geometry,
        properties,
        visible: true,
        locked: false
      });
      /* istanbul ignore next */
      cov_2mp44saf55().s[111]++;
      await this.db.saveSpatialLayer(layer);
      /* istanbul ignore next */
      cov_2mp44saf55().s[112]++;
      this.recordPerformanceMetric(startTime);
      /* istanbul ignore next */
      cov_2mp44saf55().s[113]++;
      this.invalidateCache(`spatial:${projectUuid}`);
      /* istanbul ignore next */
      cov_2mp44saf55().s[114]++;
      this.emit('spatial:saved', {
        uuid,
        layer
      });
      /* istanbul ignore next */
      cov_2mp44saf55().s[115]++;
      return uuid;
    } catch (error) {
      /* istanbul ignore next */
      cov_2mp44saf55().s[116]++;
      console.error('Failed to save spatial layer:', error);
      /* istanbul ignore next */
      cov_2mp44saf55().s[117]++;
      throw error;
    }
  }
  async getSpatialLayersByProject(projectUuid) {
    /* istanbul ignore next */
    cov_2mp44saf55().f[11]++;
    const startTime =
    /* istanbul ignore next */
    (cov_2mp44saf55().s[118]++, performance.now());
    const cacheKey =
    /* istanbul ignore next */
    (cov_2mp44saf55().s[119]++, `spatial:${projectUuid}`);
    /* istanbul ignore next */
    cov_2mp44saf55().s[120]++;
    try {
      // Check cache first
      const cached =
      /* istanbul ignore next */
      (cov_2mp44saf55().s[121]++, this.getCachedData(cacheKey));
      /* istanbul ignore next */
      cov_2mp44saf55().s[122]++;
      if (cached) {
        /* istanbul ignore next */
        cov_2mp44saf55().b[11][0]++;
        cov_2mp44saf55().s[123]++;
        this.recordPerformanceMetric(startTime, true);
        /* istanbul ignore next */
        cov_2mp44saf55().s[124]++;
        return cached;
      } else
      /* istanbul ignore next */
      {
        cov_2mp44saf55().b[11][1]++;
      }
      const layers =
      /* istanbul ignore next */
      (cov_2mp44saf55().s[125]++, await this.db.getSpatialLayersByProject(projectUuid));
      // Cache the result
      /* istanbul ignore next */
      cov_2mp44saf55().s[126]++;
      this.setCachedData(cacheKey, layers, 180000); // 3 minutes TTL
      /* istanbul ignore next */
      cov_2mp44saf55().s[127]++;
      this.recordPerformanceMetric(startTime);
      /* istanbul ignore next */
      cov_2mp44saf55().s[128]++;
      return layers;
    } catch (error) {
      /* istanbul ignore next */
      cov_2mp44saf55().s[129]++;
      console.error('Failed to get spatial layers:', error);
      /* istanbul ignore next */
      cov_2mp44saf55().s[130]++;
      throw error;
    }
  }
  // =============================================================================
  // Sync Operations
  // =============================================================================
  async getSyncStatus() {
    /* istanbul ignore next */
    cov_2mp44saf55().f[12]++;
    cov_2mp44saf55().s[131]++;
    await this.updateSyncStatus();
    /* istanbul ignore next */
    cov_2mp44saf55().s[132]++;
    return {
      ...this.syncStatus
    };
  }
  async updateSyncStatus() {
    /* istanbul ignore next */
    cov_2mp44saf55().f[13]++;
    cov_2mp44saf55().s[133]++;
    try {
      const pendingOps =
      /* istanbul ignore next */
      (cov_2mp44saf55().s[134]++, await this.db.getPendingSyncOperations());
      /* istanbul ignore next */
      cov_2mp44saf55().s[135]++;
      this.syncStatus.pendingOperations = pendingOps.length;
      /* istanbul ignore next */
      cov_2mp44saf55().s[136]++;
      this.syncStatus.failedOperations = pendingOps.filter(op => {
        /* istanbul ignore next */
        cov_2mp44saf55().f[14]++;
        cov_2mp44saf55().s[137]++;
        return op.status === 'failed';
      }).length;
    } catch (error) {
      /* istanbul ignore next */
      cov_2mp44saf55().s[138]++;
      console.error('Failed to update sync status:', error);
    }
  }
  async forceSyncAll() {
    /* istanbul ignore next */
    cov_2mp44saf55().f[15]++;
    cov_2mp44saf55().s[139]++;
    // In offline-first mode, this would prepare data for future sync
    console.log('📝 Sync operations queued for future online mode');
    /* istanbul ignore next */
    cov_2mp44saf55().s[140]++;
    this.emit('sync:queued');
  }
  // =============================================================================
  // Performance Monitoring
  // =============================================================================
  recordPerformanceMetric(startTime, cacheHit =
  /* istanbul ignore next */
  (cov_2mp44saf55().b[12][0]++, false)) {
    /* istanbul ignore next */
    cov_2mp44saf55().f[16]++;
    cov_2mp44saf55().s[141]++;
    if (!this.config.enablePerformanceMonitoring) {
      /* istanbul ignore next */
      cov_2mp44saf55().b[13][0]++;
      cov_2mp44saf55().s[142]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_2mp44saf55().b[13][1]++;
    }
    const queryTime =
    /* istanbul ignore next */
    (cov_2mp44saf55().s[143]++, performance.now() - startTime);
    /* istanbul ignore next */
    cov_2mp44saf55().s[144]++;
    this.performanceMetrics.totalQueries++;
    /* istanbul ignore next */
    cov_2mp44saf55().s[145]++;
    if (cacheHit) {
      /* istanbul ignore next */
      cov_2mp44saf55().b[14][0]++;
      cov_2mp44saf55().s[146]++;
      this.performanceMetrics.cacheHitRate = (this.performanceMetrics.cacheHitRate * (this.performanceMetrics.totalQueries - 1) + 1) / this.performanceMetrics.totalQueries;
    } else {
      /* istanbul ignore next */
      cov_2mp44saf55().b[14][1]++;
      cov_2mp44saf55().s[147]++;
      this.performanceMetrics.averageQueryTime = (this.performanceMetrics.averageQueryTime * (this.performanceMetrics.totalQueries - 1) + queryTime) / this.performanceMetrics.totalQueries;
    }
    /* istanbul ignore next */
    cov_2mp44saf55().s[148]++;
    this.performanceMetrics.lastMeasurement = new Date();
  }
  async getPerformanceMetrics() {
    /* istanbul ignore next */
    cov_2mp44saf55().f[17]++;
    const stats =
    /* istanbul ignore next */
    (cov_2mp44saf55().s[149]++, await this.db.getStorageStats());
    /* istanbul ignore next */
    cov_2mp44saf55().s[150]++;
    this.performanceMetrics.storageUsage = stats.totalSize;
    /* istanbul ignore next */
    cov_2mp44saf55().s[151]++;
    return {
      ...this.performanceMetrics
    };
  }
  // =============================================================================
  // Cache Management
  // =============================================================================
  getCachedData(key) {
    /* istanbul ignore next */
    cov_2mp44saf55().f[18]++;
    const cached =
    /* istanbul ignore next */
    (cov_2mp44saf55().s[152]++, this.queryCache.get(key));
    /* istanbul ignore next */
    cov_2mp44saf55().s[153]++;
    if (!cached) {
      /* istanbul ignore next */
      cov_2mp44saf55().b[15][0]++;
      cov_2mp44saf55().s[154]++;
      return null;
    } else
    /* istanbul ignore next */
    {
      cov_2mp44saf55().b[15][1]++;
    }
    const now =
    /* istanbul ignore next */
    (cov_2mp44saf55().s[155]++, new Date());
    /* istanbul ignore next */
    cov_2mp44saf55().s[156]++;
    if (now.getTime() - cached.timestamp.getTime() > cached.ttl) {
      /* istanbul ignore next */
      cov_2mp44saf55().b[16][0]++;
      cov_2mp44saf55().s[157]++;
      this.queryCache.delete(key);
      /* istanbul ignore next */
      cov_2mp44saf55().s[158]++;
      return null;
    } else
    /* istanbul ignore next */
    {
      cov_2mp44saf55().b[16][1]++;
    }
    cov_2mp44saf55().s[159]++;
    return cached.data;
  }
  setCachedData(key, data, ttl) {
    /* istanbul ignore next */
    cov_2mp44saf55().f[19]++;
    cov_2mp44saf55().s[160]++;
    this.queryCache.set(key, {
      data,
      timestamp: new Date(),
      ttl
    });
  }
  invalidateCache(pattern) {
    /* istanbul ignore next */
    cov_2mp44saf55().f[20]++;
    cov_2mp44saf55().s[161]++;
    for (const key of this.queryCache.keys()) {
      /* istanbul ignore next */
      cov_2mp44saf55().s[162]++;
      if (key.includes(pattern)) {
        /* istanbul ignore next */
        cov_2mp44saf55().b[17][0]++;
        cov_2mp44saf55().s[163]++;
        this.queryCache.delete(key);
      } else
      /* istanbul ignore next */
      {
        cov_2mp44saf55().b[17][1]++;
      }
    }
  }
  // =============================================================================
  // Utility Methods
  // =============================================================================
  convertFromSizeWiseProject(sizeWiseProject) {
    /* istanbul ignore next */
    cov_2mp44saf55().f[21]++;
    const {
      id,
      uuid,
      lastModified,
      syncStatus,
      version,
      ...projectData
    } =
    /* istanbul ignore next */
    (cov_2mp44saf55().s[164]++, sizeWiseProject);
    /* istanbul ignore next */
    cov_2mp44saf55().s[165]++;
    return {
      id: uuid,
      ...projectData
    };
  }
  startAutoSync() {
    /* istanbul ignore next */
    cov_2mp44saf55().f[22]++;
    cov_2mp44saf55().s[166]++;
    if (this.syncTimer) {
      /* istanbul ignore next */
      cov_2mp44saf55().b[18][0]++;
      cov_2mp44saf55().s[167]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_2mp44saf55().b[18][1]++;
    }
    cov_2mp44saf55().s[168]++;
    this.syncTimer = setInterval(async () => {
      /* istanbul ignore next */
      cov_2mp44saf55().f[23]++;
      cov_2mp44saf55().s[169]++;
      if (
      /* istanbul ignore next */
      (cov_2mp44saf55().b[20][0]++, !this.syncStatus.syncInProgress) &&
      /* istanbul ignore next */
      (cov_2mp44saf55().b[20][1]++, this.syncStatus.isOnline)) {
        /* istanbul ignore next */
        cov_2mp44saf55().b[19][0]++;
        cov_2mp44saf55().s[170]++;
        await this.forceSyncAll();
      } else
      /* istanbul ignore next */
      {
        cov_2mp44saf55().b[19][1]++;
      }
    }, this.config.syncIntervalMs);
  }
  stopAutoSync() {
    /* istanbul ignore next */
    cov_2mp44saf55().f[24]++;
    cov_2mp44saf55().s[171]++;
    if (this.syncTimer) {
      /* istanbul ignore next */
      cov_2mp44saf55().b[21][0]++;
      cov_2mp44saf55().s[172]++;
      clearInterval(this.syncTimer);
      /* istanbul ignore next */
      cov_2mp44saf55().s[173]++;
      this.syncTimer = null;
    } else
    /* istanbul ignore next */
    {
      cov_2mp44saf55().b[21][1]++;
    }
  }
  // =============================================================================
  // Cleanup
  // =============================================================================
  async cleanup() {
    /* istanbul ignore next */
    cov_2mp44saf55().f[25]++;
    cov_2mp44saf55().s[174]++;
    this.stopAutoSync();
    /* istanbul ignore next */
    cov_2mp44saf55().s[175]++;
    this.queryCache.clear();
    /* istanbul ignore next */
    cov_2mp44saf55().s[176]++;
    await this.db.clearOldSyncOperations();
    /* istanbul ignore next */
    cov_2mp44saf55().s[177]++;
    this.emit('cleanup:completed');
  }
  async close() {
    /* istanbul ignore next */
    cov_2mp44saf55().f[26]++;
    cov_2mp44saf55().s[178]++;
    await this.cleanup();
    /* istanbul ignore next */
    cov_2mp44saf55().s[179]++;
    await this.db.close();
    /* istanbul ignore next */
    cov_2mp44saf55().s[180]++;
    this.emit('closed');
  }
}
/* istanbul ignore next */
cov_2mp44saf55().s[181]++;
exports.EnhancedOfflineService = EnhancedOfflineService;
// =============================================================================
// Factory Function
// =============================================================================
async function createEnhancedOfflineService(config) {
  /* istanbul ignore next */
  cov_2mp44saf55().f[27]++;
  const service =
  /* istanbul ignore next */
  (cov_2mp44saf55().s[182]++, new EnhancedOfflineService(config));
  // Wait for initialization
  /* istanbul ignore next */
  cov_2mp44saf55().s[183]++;
  return new Promise((resolve, reject) => {
    /* istanbul ignore next */
    cov_2mp44saf55().f[28]++;
    cov_2mp44saf55().s[184]++;
    service.once('initialized', () => {
      /* istanbul ignore next */
      cov_2mp44saf55().f[29]++;
      cov_2mp44saf55().s[185]++;
      return resolve(service);
    });
    /* istanbul ignore next */
    cov_2mp44saf55().s[186]++;
    service.once('error', reject);
  });
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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