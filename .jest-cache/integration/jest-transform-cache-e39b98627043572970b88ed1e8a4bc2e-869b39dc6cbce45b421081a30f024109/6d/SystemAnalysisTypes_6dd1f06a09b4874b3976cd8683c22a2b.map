{"version": 3, "names": ["cov_12k33bco6g", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "name", "decl", "loc", "branchMap", "type", "locations", "undefined", "s", "f", "b", "inputSourceMap", "file", "mappings", "names", "sources", "sourcesContent", "version", "_coverageSchema", "coverage", "actualCoverage", "AnalysisType", "exports", "TimeHorizon", "AnalysisDepth", "MeasurementSource", "QualityIndicator", "BalanceGrade", "TrendDirection", "Season", "AlertType", "Alert<PERSON>everity", "EnergyUnits", "TimeFrame", "CostAnalysisMethod", "UncertaintyLevel", "EmissionUnits", "EmissionScope", "StandardType", "ComplianceStatus", "TimeUnits", "DistributionType", "SystemType", "RecommendationType", "RecommendationPriority", "ImplementationComplexity", "BenchmarkType", "SystemSize", "BuildingType", "EfficiencyCalculationMethod", "ComponentType", "MaintenanceStatus", "ModelType", "AnomalyType", "AnomalySeverity", "CauseCategory", "__exportStar", "require"], "sources": ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\types\\SystemAnalysisTypes.ts"], "sourcesContent": ["/**\r\n * System Analysis Type Definitions\r\n * \r\n * Comprehensive TypeScript interfaces for Phase 3 Priority 3: Advanced System Analysis Tools\r\n * Includes system performance analysis, energy efficiency metrics, lifecycle cost analysis,\r\n * environmental impact assessment, and compliance checking frameworks.\r\n * \r\n * @version 3.0.0\r\n * <AUTHOR> Suite Development Team\r\n */\r\n\r\n// ============================================================================\r\n// CORE SYSTEM ANALYSIS INTERFACES\r\n// ============================================================================\r\n\r\n/**\r\n * Main system analysis configuration and results interface\r\n */\r\nexport interface SystemAnalysis {\r\n  id: string;\r\n  name: string;\r\n  description: string;\r\n  systemConfiguration: SystemConfiguration;\r\n  analysisType: AnalysisType;\r\n  analysisScope: AnalysisScope;\r\n  performanceAnalysis?: PerformanceAnalysis;\r\n  energyAnalysis?: EnergyAnalysis;\r\n  costAnalysis?: LifecycleCostAnalysis;\r\n  environmentalAnalysis?: EnvironmentalImpactAnalysis;\r\n  complianceAnalysis?: ComplianceAnalysis;\r\n  timestamp: Date;\r\n  analysisVersion: string;\r\n}\r\n\r\n/**\r\n * Types of system analysis that can be performed\r\n */\r\nexport enum AnalysisType {\r\n  PERFORMANCE_ONLY = 'performance_only',\r\n  ENERGY_ONLY = 'energy_only',\r\n  COST_ONLY = 'cost_only',\r\n  ENVIRONMENTAL_ONLY = 'environmental_only',\r\n  COMPLIANCE_ONLY = 'compliance_only',\r\n  COMPREHENSIVE = 'comprehensive',\r\n  CUSTOM = 'custom'\r\n}\r\n\r\n/**\r\n * Scope of analysis - what parts of the system to analyze\r\n */\r\nexport interface AnalysisScope {\r\n  includePerformance: boolean;\r\n  includeEnergy: boolean;\r\n  includeCost: boolean;\r\n  includeEnvironmental: boolean;\r\n  includeCompliance: boolean;\r\n  timeHorizon: TimeHorizon;\r\n  analysisDepth: AnalysisDepth;\r\n  customParameters?: CustomAnalysisParameters;\r\n}\r\n\r\nexport enum TimeHorizon {\r\n  INSTANTANEOUS = 'instantaneous',\r\n  DAILY = 'daily',\r\n  MONTHLY = 'monthly',\r\n  ANNUAL = 'annual',\r\n  LIFECYCLE = 'lifecycle',\r\n  CUSTOM = 'custom'\r\n}\r\n\r\nexport enum AnalysisDepth {\r\n  BASIC = 'basic',\r\n  DETAILED = 'detailed',\r\n  COMPREHENSIVE = 'comprehensive',\r\n  RESEARCH_GRADE = 'research_grade'\r\n}\r\n\r\n// ============================================================================\r\n// SYSTEM PERFORMANCE ANALYSIS\r\n// ============================================================================\r\n\r\n/**\r\n * Comprehensive system performance analysis results\r\n */\r\nexport interface PerformanceAnalysis {\r\n  id: string;\r\n  systemId: string;\r\n  analysisTimestamp: Date;\r\n  performanceMetrics: PerformanceMetrics;\r\n  trendAnalysis: TrendAnalysis;\r\n  benchmarkComparison: BenchmarkComparison;\r\n  efficiencyAnalysis: EfficiencyAnalysis;\r\n  alertsAndWarnings: PerformanceAlert[];\r\n  recommendations: PerformanceRecommendation[];\r\n  uncertaintyAnalysis: UncertaintyAnalysis;\r\n}\r\n\r\n/**\r\n * Core performance metrics for HVAC systems\r\n */\r\nexport interface PerformanceMetrics {\r\n  // Pressure and Flow Metrics\r\n  totalSystemPressure: Measurement;\r\n  staticPressure: Measurement;\r\n  velocityPressure: Measurement;\r\n  totalAirflow: Measurement;\r\n  designAirflow: Measurement;\r\n  airflowEfficiency: Measurement;\r\n  \r\n  // Fan Performance\r\n  fanPower: Measurement;\r\n  fanEfficiency: Measurement;\r\n  fanSpeed: Measurement;\r\n  fanCurvePosition: FanCurvePosition;\r\n  \r\n  // System Efficiency\r\n  systemEfficiency: Measurement;\r\n  transportEfficiency: Measurement;\r\n  distributionEfficiency: Measurement;\r\n  \r\n  // Environmental Metrics\r\n  noiseLevel: Measurement;\r\n  vibrationLevel: Measurement;\r\n  temperatureRise: Measurement;\r\n  \r\n  // Filter and Component Performance\r\n  filterPressureDrop: Measurement;\r\n  coilPressureDrop: Measurement;\r\n  dampersPosition: DamperPosition[];\r\n  \r\n  // System Balance\r\n  balanceQuality: BalanceQuality;\r\n  flowDistribution: FlowDistribution;\r\n}\r\n\r\n/**\r\n * Measurement interface with value, units, and quality indicators\r\n */\r\nexport interface Measurement {\r\n  value: number;\r\n  units: string;\r\n  accuracy: number;\r\n  timestamp: Date;\r\n  source: MeasurementSource;\r\n  qualityIndicator: QualityIndicator;\r\n  uncertaintyBounds?: UncertaintyBounds;\r\n}\r\n\r\nexport enum MeasurementSource {\r\n  CALCULATED = 'calculated',\r\n  MEASURED = 'measured',\r\n  ESTIMATED = 'estimated',\r\n  MANUFACTURER_DATA = 'manufacturer_data',\r\n  SIMULATION = 'simulation'\r\n}\r\n\r\nexport enum QualityIndicator {\r\n  HIGH = 'high',\r\n  MEDIUM = 'medium',\r\n  LOW = 'low',\r\n  UNCERTAIN = 'uncertain'\r\n}\r\n\r\n/**\r\n * Fan curve position and operating point analysis\r\n */\r\nexport interface FanCurvePosition {\r\n  operatingPoint: OperatingPoint;\r\n  designPoint: OperatingPoint;\r\n  efficiencyAtOperating: number;\r\n  efficiencyAtDesign: number;\r\n  surgeMargin: number;\r\n  stallMargin: number;\r\n  recommendedOperatingRange: OperatingRange;\r\n}\r\n\r\nexport interface OperatingPoint {\r\n  airflow: number;\r\n  pressure: number;\r\n  power: number;\r\n  efficiency: number;\r\n  speed: number;\r\n}\r\n\r\n/**\r\n * System balance quality assessment\r\n */\r\nexport interface BalanceQuality {\r\n  overallScore: number; // 0-100\r\n  flowVariation: number; // Coefficient of variation\r\n  pressureVariation: number;\r\n  balanceGrade: BalanceGrade;\r\n  criticalZones: string[];\r\n  balanceRecommendations: string[];\r\n}\r\n\r\nexport enum BalanceGrade {\r\n  EXCELLENT = 'excellent',\r\n  GOOD = 'good',\r\n  ACCEPTABLE = 'acceptable',\r\n  POOR = 'poor',\r\n  CRITICAL = 'critical'\r\n}\r\n\r\n/**\r\n * Trend analysis for performance monitoring\r\n */\r\nexport interface TrendAnalysis {\r\n  timeRange: TimeRange;\r\n  trendDirection: TrendDirection;\r\n  trendMagnitude: number;\r\n  seasonalPatterns: SeasonalPattern[];\r\n  anomalies: PerformanceAnomaly[];\r\n  predictiveAnalysis: PredictiveAnalysis;\r\n  degradationRate: DegradationRate;\r\n}\r\n\r\nexport enum TrendDirection {\r\n  IMPROVING = 'improving',\r\n  STABLE = 'stable',\r\n  DEGRADING = 'degrading',\r\n  FLUCTUATING = 'fluctuating',\r\n  UNKNOWN = 'unknown'\r\n}\r\n\r\nexport interface SeasonalPattern {\r\n  season: Season;\r\n  averagePerformance: number;\r\n  performanceVariation: number;\r\n  typicalIssues: string[];\r\n}\r\n\r\nexport enum Season {\r\n  SPRING = 'spring',\r\n  SUMMER = 'summer',\r\n  FALL = 'fall',\r\n  WINTER = 'winter'\r\n}\r\n\r\n/**\r\n * Performance alerts and warnings system\r\n */\r\nexport interface PerformanceAlert {\r\n  id: string;\r\n  alertType: AlertType;\r\n  severity: AlertSeverity;\r\n  metric: string;\r\n  currentValue: number;\r\n  thresholdValue: number;\r\n  message: string;\r\n  timestamp: Date;\r\n  acknowledged: boolean;\r\n  recommendedActions: string[];\r\n}\r\n\r\nexport enum AlertType {\r\n  THRESHOLD_EXCEEDED = 'threshold_exceeded',\r\n  TREND_DEGRADATION = 'trend_degradation',\r\n  ANOMALY_DETECTED = 'anomaly_detected',\r\n  EFFICIENCY_DROP = 'efficiency_drop',\r\n  BALANCE_ISSUE = 'balance_issue',\r\n  MAINTENANCE_DUE = 'maintenance_due'\r\n}\r\n\r\nexport enum AlertSeverity {\r\n  CRITICAL = 'critical',\r\n  HIGH = 'high',\r\n  MEDIUM = 'medium',\r\n  LOW = 'low',\r\n  INFO = 'info'\r\n}\r\n\r\n// ============================================================================\r\n// ENERGY EFFICIENCY ANALYSIS\r\n// ============================================================================\r\n\r\n/**\r\n * Comprehensive energy efficiency analysis\r\n */\r\nexport interface EnergyAnalysis {\r\n  id: string;\r\n  systemId: string;\r\n  analysisTimestamp: Date;\r\n  energyConsumption: EnergyConsumption;\r\n  efficiencyMetrics: EnergyEfficiencyMetrics;\r\n  energyCosts: EnergyCosts;\r\n  carbonFootprint: CarbonFootprint;\r\n  benchmarkComparison: EnergyBenchmark;\r\n  optimizationOpportunities: EnergyOptimizationOpportunity[];\r\n  seasonalAnalysis: SeasonalEnergyAnalysis;\r\n}\r\n\r\n/**\r\n * Energy consumption breakdown and analysis\r\n */\r\nexport interface EnergyConsumption {\r\n  totalConsumption: EnergyMeasurement;\r\n  fanConsumption: EnergyMeasurement;\r\n  auxiliaryConsumption: EnergyMeasurement;\r\n  heatingConsumption?: EnergyMeasurement;\r\n  coolingConsumption?: EnergyMeasurement;\r\n  consumptionByTimeOfDay: TimeOfDayConsumption[];\r\n  loadProfile: LoadProfile;\r\n  peakDemand: PeakDemand;\r\n}\r\n\r\nexport interface EnergyMeasurement {\r\n  value: number;\r\n  units: EnergyUnits;\r\n  timeFrame: TimeFrame;\r\n  accuracy: number;\r\n  source: MeasurementSource;\r\n}\r\n\r\nexport enum EnergyUnits {\r\n  KWH = 'kWh',\r\n  BTU = 'BTU',\r\n  THERMS = 'therms',\r\n  JOULES = 'joules',\r\n  KW = 'kW'\r\n}\r\n\r\nexport enum TimeFrame {\r\n  HOURLY = 'hourly',\r\n  DAILY = 'daily',\r\n  WEEKLY = 'weekly',\r\n  MONTHLY = 'monthly',\r\n  ANNUALLY = 'annually'\r\n}\r\n\r\n/**\r\n * Energy efficiency metrics and calculations\r\n */\r\nexport interface EnergyEfficiencyMetrics {\r\n  overallEfficiency: number; // %\r\n  fanEfficiency: number; // %\r\n  systemEfficiency: number; // %\r\n  transportEfficiency: number; // %\r\n  specificFanPower: number; // W/CFM\r\n  energyUtilizationIndex: number; // EUI\r\n  powerDensity: number; // W/sq ft\r\n  efficiencyTrend: EfficiencyTrend;\r\n  benchmarkComparison: EfficiencyBenchmark;\r\n}\r\n\r\n/**\r\n * Energy cost analysis and projections\r\n */\r\nexport interface EnergyCosts {\r\n  currentCosts: CostBreakdown;\r\n  projectedCosts: CostProjection[];\r\n  costSavingOpportunities: CostSavingOpportunity[];\r\n  utilityRateStructure: UtilityRateStructure;\r\n  demandCharges: DemandCharges;\r\n  timeOfUsePricing: TimeOfUsePricing;\r\n}\r\n\r\nexport interface CostBreakdown {\r\n  totalCost: number;\r\n  energyCost: number;\r\n  demandCost: number;\r\n  fixedCost: number;\r\n  currency: string;\r\n  timeFrame: TimeFrame;\r\n}\r\n\r\n// ============================================================================\r\n// LIFECYCLE COST ANALYSIS\r\n// ============================================================================\r\n\r\n/**\r\n * Comprehensive lifecycle cost analysis\r\n */\r\nexport interface LifecycleCostAnalysis {\r\n  id: string;\r\n  systemId: string;\r\n  analysisTimestamp: Date;\r\n  analysisParameters: CostAnalysisParameters;\r\n  initialCosts: InitialCosts;\r\n  operatingCosts: OperatingCosts;\r\n  maintenanceCosts: MaintenanceCosts;\r\n  replacementCosts: ReplacementCosts;\r\n  totalCostOfOwnership: TotalCostOfOwnership;\r\n  costComparison: CostComparison;\r\n  sensitivityAnalysis: CostSensitivityAnalysis;\r\n  recommendations: CostRecommendation[];\r\n}\r\n\r\n/**\r\n * Parameters for lifecycle cost analysis\r\n */\r\nexport interface CostAnalysisParameters {\r\n  analysisHorizon: number; // years\r\n  discountRate: number; // %\r\n  inflationRate: number; // %\r\n  energyEscalationRate: number; // %\r\n  currency: string;\r\n  analysisMethod: CostAnalysisMethod;\r\n  uncertaintyLevel: UncertaintyLevel;\r\n}\r\n\r\nexport enum CostAnalysisMethod {\r\n  NET_PRESENT_VALUE = 'net_present_value',\r\n  LIFE_CYCLE_COST = 'life_cycle_cost',\r\n  PAYBACK_PERIOD = 'payback_period',\r\n  INTERNAL_RATE_OF_RETURN = 'internal_rate_of_return'\r\n}\r\n\r\nexport enum UncertaintyLevel {\r\n  LOW = 'low',\r\n  MEDIUM = 'medium',\r\n  HIGH = 'high'\r\n}\r\n\r\n/**\r\n * Initial system costs breakdown\r\n */\r\nexport interface InitialCosts {\r\n  equipmentCosts: EquipmentCosts;\r\n  installationCosts: InstallationCosts;\r\n  designCosts: DesignCosts;\r\n  permitsCosts: PermitsCosts;\r\n  totalInitialCost: number;\r\n  costPerCFM: number;\r\n  costPerSquareFoot: number;\r\n}\r\n\r\nexport interface EquipmentCosts {\r\n  fans: number;\r\n  ductwork: number;\r\n  fittings: number;\r\n  dampers: number;\r\n  controls: number;\r\n  accessories: number;\r\n  total: number;\r\n}\r\n\r\n// ============================================================================\r\n// ENVIRONMENTAL IMPACT ANALYSIS\r\n// ============================================================================\r\n\r\n/**\r\n * Environmental impact assessment\r\n */\r\nexport interface EnvironmentalImpactAnalysis {\r\n  id: string;\r\n  systemId: string;\r\n  analysisTimestamp: Date;\r\n  carbonFootprint: CarbonFootprint;\r\n  sustainabilityMetrics: SustainabilityMetrics;\r\n  greenBuildingCompliance: GreenBuildingCompliance;\r\n  environmentalCertifications: EnvironmentalCertification[];\r\n  lifecycleAssessment: LifecycleAssessment;\r\n  recommendations: EnvironmentalRecommendation[];\r\n}\r\n\r\n/**\r\n * Carbon footprint calculation and analysis\r\n */\r\nexport interface CarbonFootprint {\r\n  totalEmissions: EmissionMeasurement;\r\n  operationalEmissions: EmissionMeasurement;\r\n  embodiedEmissions: EmissionMeasurement;\r\n  emissionsBySource: EmissionSource[];\r\n  emissionsTrend: EmissionsTrend;\r\n  offsetOpportunities: OffsetOpportunity[];\r\n  benchmarkComparison: EmissionsBenchmark;\r\n}\r\n\r\nexport interface EmissionMeasurement {\r\n  value: number;\r\n  units: EmissionUnits;\r\n  timeFrame: TimeFrame;\r\n  scope: EmissionScope;\r\n  accuracy: number;\r\n}\r\n\r\nexport enum EmissionUnits {\r\n  KG_CO2E = 'kg_CO2e',\r\n  TONS_CO2E = 'tons_CO2e',\r\n  LBS_CO2E = 'lbs_CO2e'\r\n}\r\n\r\nexport enum EmissionScope {\r\n  SCOPE_1 = 'scope_1', // Direct emissions\r\n  SCOPE_2 = 'scope_2', // Indirect emissions from electricity\r\n  SCOPE_3 = 'scope_3'  // Other indirect emissions\r\n}\r\n\r\n// ============================================================================\r\n// COMPLIANCE ANALYSIS\r\n// ============================================================================\r\n\r\n/**\r\n * Compliance checking and validation\r\n */\r\nexport interface ComplianceAnalysis {\r\n  id: string;\r\n  systemId: string;\r\n  analysisTimestamp: Date;\r\n  complianceStandards: ComplianceStandard[];\r\n  complianceResults: ComplianceResult[];\r\n  overallComplianceStatus: ComplianceStatus;\r\n  nonComplianceIssues: NonComplianceIssue[];\r\n  recommendations: ComplianceRecommendation[];\r\n  certificationStatus: CertificationStatus;\r\n}\r\n\r\n/**\r\n * Compliance standards and codes\r\n */\r\nexport interface ComplianceStandard {\r\n  id: string;\r\n  name: string;\r\n  version: string;\r\n  type: StandardType;\r\n  jurisdiction: string;\r\n  applicability: StandardApplicability;\r\n  requirements: ComplianceRequirement[];\r\n}\r\n\r\nexport enum StandardType {\r\n  SMACNA = 'smacna',\r\n  ASHRAE = 'ashrae',\r\n  LOCAL_CODE = 'local_code',\r\n  INTERNATIONAL_CODE = 'international_code',\r\n  INDUSTRY_STANDARD = 'industry_standard',\r\n  GREEN_BUILDING = 'green_building'\r\n}\r\n\r\nexport interface ComplianceResult {\r\n  standardId: string;\r\n  requirementId: string;\r\n  status: ComplianceStatus;\r\n  actualValue: number;\r\n  requiredValue: number;\r\n  units: string;\r\n  margin: number;\r\n  notes: string;\r\n}\r\n\r\nexport enum ComplianceStatus {\r\n  COMPLIANT = 'compliant',\r\n  NON_COMPLIANT = 'non_compliant',\r\n  MARGINAL = 'marginal',\r\n  NOT_APPLICABLE = 'not_applicable',\r\n  REQUIRES_REVIEW = 'requires_review'\r\n}\r\n\r\n// ============================================================================\r\n// UTILITY INTERFACES\r\n// ============================================================================\r\n\r\nexport interface TimeRange {\r\n  startDate: Date;\r\n  endDate: Date;\r\n  duration: number;\r\n  units: TimeUnits;\r\n}\r\n\r\nexport enum TimeUnits {\r\n  SECONDS = 'seconds',\r\n  MINUTES = 'minutes',\r\n  HOURS = 'hours',\r\n  DAYS = 'days',\r\n  WEEKS = 'weeks',\r\n  MONTHS = 'months',\r\n  YEARS = 'years'\r\n}\r\n\r\nexport interface UncertaintyBounds {\r\n  lowerBound: number;\r\n  upperBound: number;\r\n  confidenceLevel: number; // %\r\n  distributionType: DistributionType;\r\n}\r\n\r\nexport enum DistributionType {\r\n  NORMAL = 'normal',\r\n  UNIFORM = 'uniform',\r\n  TRIANGULAR = 'triangular',\r\n  LOGNORMAL = 'lognormal'\r\n}\r\n\r\nexport interface CustomAnalysisParameters {\r\n  [key: string]: any;\r\n}\r\n\r\n// ============================================================================\r\n// SYSTEM CONFIGURATION (EXTENDED)\r\n// ============================================================================\r\n\r\nexport interface SystemConfiguration {\r\n  id: string;\r\n  name: string;\r\n  description: string;\r\n  systemType: SystemType;\r\n  designParameters: DesignParameters;\r\n  operatingConditions: OperatingConditions;\r\n  components: SystemComponent[];\r\n  controlStrategy: ControlStrategy;\r\n  maintenanceSchedule: MaintenanceSchedule;\r\n}\r\n\r\nexport enum SystemType {\r\n  SUPPLY_AIR = 'supply_air',\r\n  RETURN_AIR = 'return_air',\r\n  EXHAUST_AIR = 'exhaust_air',\r\n  MIXED_AIR = 'mixed_air',\r\n  DEDICATED_OUTDOOR_AIR = 'dedicated_outdoor_air'\r\n}\r\n\r\nexport interface DesignParameters {\r\n  designAirflow: number; // CFM\r\n  designPressure: number; // in wg\r\n  designTemperature: number; // °F\r\n  designHumidity: number; // %RH\r\n  designElevation: number; // ft\r\n  safetyFactors: SafetyFactors;\r\n}\r\n\r\nexport interface SafetyFactors {\r\n  pressureSafetyFactor: number;\r\n  airflowSafetyFactor: number;\r\n  powerSafetyFactor: number;\r\n}\r\n\r\n// ============================================================================\r\n// ADDITIONAL INTERFACES FOR COMPREHENSIVE ANALYSIS\r\n// ============================================================================\r\n\r\n/**\r\n * Performance recommendations with priority and impact assessment\r\n */\r\nexport interface PerformanceRecommendation {\r\n  id: string;\r\n  type: RecommendationType;\r\n  priority: RecommendationPriority;\r\n  title: string;\r\n  description: string;\r\n  expectedImpact: ExpectedImpact;\r\n  implementationCost: number;\r\n  paybackPeriod: number; // months\r\n  implementationComplexity: ImplementationComplexity;\r\n  requiredActions: string[];\r\n  timeline: string;\r\n}\r\n\r\nexport enum RecommendationType {\r\n  OPTIMIZATION = 'optimization',\r\n  MAINTENANCE = 'maintenance',\r\n  UPGRADE = 'upgrade',\r\n  OPERATIONAL_CHANGE = 'operational_change',\r\n  DESIGN_MODIFICATION = 'design_modification'\r\n}\r\n\r\nexport enum RecommendationPriority {\r\n  CRITICAL = 'critical',\r\n  HIGH = 'high',\r\n  MEDIUM = 'medium',\r\n  LOW = 'low'\r\n}\r\n\r\nexport interface ExpectedImpact {\r\n  energySavings: number; // %\r\n  costSavings: number; // $/year\r\n  performanceImprovement: number; // %\r\n  emissionReduction: number; // kg CO2e/year\r\n  reliabilityImprovement: number; // %\r\n}\r\n\r\nexport enum ImplementationComplexity {\r\n  SIMPLE = 'simple',\r\n  MODERATE = 'moderate',\r\n  COMPLEX = 'complex',\r\n  VERY_COMPLEX = 'very_complex'\r\n}\r\n\r\n/**\r\n * Benchmark comparison for performance analysis\r\n */\r\nexport interface BenchmarkComparison {\r\n  benchmarkType: BenchmarkType;\r\n  benchmarkSource: string;\r\n  systemPerformance: number;\r\n  benchmarkValue: number;\r\n  percentile: number;\r\n  performanceGap: number;\r\n  improvementPotential: number;\r\n  similarSystems: SimilarSystemComparison[];\r\n}\r\n\r\nexport enum BenchmarkType {\r\n  INDUSTRY_AVERAGE = 'industry_average',\r\n  BEST_IN_CLASS = 'best_in_class',\r\n  REGULATORY_MINIMUM = 'regulatory_minimum',\r\n  DESIGN_TARGET = 'design_target',\r\n  HISTORICAL_PERFORMANCE = 'historical_performance'\r\n}\r\n\r\nexport interface SimilarSystemComparison {\r\n  systemId: string;\r\n  systemName: string;\r\n  performanceMetric: number;\r\n  systemCharacteristics: SystemCharacteristics;\r\n  performanceDifference: number;\r\n}\r\n\r\nexport interface SystemCharacteristics {\r\n  size: SystemSize;\r\n  age: number; // years\r\n  buildingType: BuildingType;\r\n  climateZone: string;\r\n  operatingHours: number; // hours/year\r\n}\r\n\r\nexport enum SystemSize {\r\n  SMALL = 'small',\r\n  MEDIUM = 'medium',\r\n  LARGE = 'large',\r\n  EXTRA_LARGE = 'extra_large'\r\n}\r\n\r\nexport enum BuildingType {\r\n  OFFICE = 'office',\r\n  RETAIL = 'retail',\r\n  HEALTHCARE = 'healthcare',\r\n  EDUCATION = 'education',\r\n  INDUSTRIAL = 'industrial',\r\n  RESIDENTIAL = 'residential',\r\n  HOSPITALITY = 'hospitality',\r\n  OTHER = 'other'\r\n}\r\n\r\n/**\r\n * Efficiency analysis with detailed breakdown\r\n */\r\nexport interface EfficiencyAnalysis {\r\n  overallEfficiency: EfficiencyMetric;\r\n  componentEfficiencies: ComponentEfficiency[];\r\n  efficiencyTrends: EfficiencyTrend[];\r\n  efficiencyLosses: EfficiencyLoss[];\r\n  improvementOpportunities: EfficiencyImprovement[];\r\n  benchmarkComparison: EfficiencyBenchmark;\r\n}\r\n\r\nexport interface EfficiencyMetric {\r\n  value: number;\r\n  units: string;\r\n  calculationMethod: EfficiencyCalculationMethod;\r\n  accuracy: number;\r\n  timestamp: Date;\r\n}\r\n\r\nexport enum EfficiencyCalculationMethod {\r\n  MEASURED = 'measured',\r\n  CALCULATED = 'calculated',\r\n  ESTIMATED = 'estimated',\r\n  MANUFACTURER_RATED = 'manufacturer_rated'\r\n}\r\n\r\nexport interface ComponentEfficiency {\r\n  componentId: string;\r\n  componentType: ComponentType;\r\n  efficiency: number;\r\n  ratedEfficiency: number;\r\n  degradationFactor: number;\r\n  maintenanceStatus: MaintenanceStatus;\r\n}\r\n\r\nexport enum ComponentType {\r\n  FAN = 'fan',\r\n  MOTOR = 'motor',\r\n  VFD = 'vfd',\r\n  DAMPER = 'damper',\r\n  FILTER = 'filter',\r\n  COIL = 'coil',\r\n  DUCTWORK = 'ductwork'\r\n}\r\n\r\nexport enum MaintenanceStatus {\r\n  EXCELLENT = 'excellent',\r\n  GOOD = 'good',\r\n  FAIR = 'fair',\r\n  POOR = 'poor',\r\n  CRITICAL = 'critical'\r\n}\r\n\r\n/**\r\n * Predictive analysis for performance forecasting\r\n */\r\nexport interface PredictiveAnalysis {\r\n  forecastHorizon: number; // months\r\n  predictedPerformance: PredictedMetric[];\r\n  confidenceInterval: ConfidenceInterval;\r\n  predictionModel: PredictionModel;\r\n  keyFactors: PredictionFactor[];\r\n  scenarios: PredictionScenario[];\r\n}\r\n\r\nexport interface PredictedMetric {\r\n  metric: string;\r\n  currentValue: number;\r\n  predictedValue: number;\r\n  changePercent: number;\r\n  timeToTarget: number; // months\r\n}\r\n\r\nexport interface ConfidenceInterval {\r\n  lowerBound: number;\r\n  upperBound: number;\r\n  confidenceLevel: number; // %\r\n}\r\n\r\nexport interface PredictionModel {\r\n  modelType: ModelType;\r\n  accuracy: number; // %\r\n  lastTrainingDate: Date;\r\n  dataPoints: number;\r\n  validationScore: number;\r\n}\r\n\r\nexport enum ModelType {\r\n  LINEAR_REGRESSION = 'linear_regression',\r\n  POLYNOMIAL_REGRESSION = 'polynomial_regression',\r\n  TIME_SERIES = 'time_series',\r\n  MACHINE_LEARNING = 'machine_learning',\r\n  PHYSICS_BASED = 'physics_based'\r\n}\r\n\r\n/**\r\n * Performance anomaly detection and analysis\r\n */\r\nexport interface PerformanceAnomaly {\r\n  id: string;\r\n  detectionTimestamp: Date;\r\n  anomalyType: AnomalyType;\r\n  severity: AnomalySeverity;\r\n  affectedMetrics: string[];\r\n  deviationMagnitude: number;\r\n  duration: number; // hours\r\n  possibleCauses: PossibleCause[];\r\n  recommendedActions: string[];\r\n  resolved: boolean;\r\n  resolutionDate?: Date;\r\n}\r\n\r\nexport enum AnomalyType {\r\n  SUDDEN_CHANGE = 'sudden_change',\r\n  GRADUAL_DRIFT = 'gradual_drift',\r\n  CYCLIC_ANOMALY = 'cyclic_anomaly',\r\n  OUTLIER = 'outlier',\r\n  PATTERN_BREAK = 'pattern_break'\r\n}\r\n\r\nexport enum AnomalySeverity {\r\n  CRITICAL = 'critical',\r\n  HIGH = 'high',\r\n  MEDIUM = 'medium',\r\n  LOW = 'low'\r\n}\r\n\r\nexport interface PossibleCause {\r\n  cause: string;\r\n  probability: number; // %\r\n  category: CauseCategory;\r\n  diagnosticSteps: string[];\r\n}\r\n\r\nexport enum CauseCategory {\r\n  EQUIPMENT_FAILURE = 'equipment_failure',\r\n  MAINTENANCE_ISSUE = 'maintenance_issue',\r\n  OPERATIONAL_CHANGE = 'operational_change',\r\n  ENVIRONMENTAL_FACTOR = 'environmental_factor',\r\n  CONTROL_SYSTEM = 'control_system',\r\n  UNKNOWN = 'unknown'\r\n}\r\n\r\n/**\r\n * Degradation rate analysis\r\n */\r\nexport interface DegradationRate {\r\n  overallDegradationRate: number; // %/year\r\n  componentDegradation: ComponentDegradation[];\r\n  degradationFactors: DegradationFactor[];\r\n  maintenanceImpact: MaintenanceImpact;\r\n  projectedLifespan: ProjectedLifespan;\r\n}\r\n\r\nexport interface ComponentDegradation {\r\n  componentId: string;\r\n  componentType: ComponentType;\r\n  degradationRate: number; // %/year\r\n  currentCondition: number; // % of original performance\r\n  estimatedRemainingLife: number; // years\r\n  replacementThreshold: number; // % performance\r\n}\r\n\r\nexport interface DegradationFactor {\r\n  factor: string;\r\n  impact: number; // % contribution to degradation\r\n  controllable: boolean;\r\n  mitigationStrategies: string[];\r\n}\r\n\r\nexport interface MaintenanceImpact {\r\n  preventiveMaintenance: MaintenanceEffect;\r\n  correctiveMaintenance: MaintenanceEffect;\r\n  deferredMaintenance: MaintenanceEffect;\r\n}\r\n\r\nexport interface MaintenanceEffect {\r\n  performanceImpact: number; // %\r\n  lifespanImpact: number; // years\r\n  costImpact: number; // $/year\r\n}\r\n\r\nexport interface ProjectedLifespan {\r\n  currentAge: number; // years\r\n  designLife: number; // years\r\n  projectedLife: number; // years\r\n  confidenceLevel: number; // %\r\n  keyAssumptions: string[];\r\n}\r\n\r\n// Export all types for external use\r\nexport * from './SystemOptimizationTypes';\r\n"], "mappings": ";;AAAA;;;;;;;;;;AAAA;AAAA,SAAAA,eAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;MAAA;QAAAC,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAO,SAAA;MAAA;QAAAD,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAW,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,cAAA;MAAAC,IAAA;MAAAC,QAAA;MAAAC,KAAA;MAAAC,OAAA;MAAAC,cAAA;MAAAC,OAAA;IAAA;IAAAC,eAAA;IAAA5B,IAAA;EAAA;EAAA,IAAA6B,QAAA,GAAA5B,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAA0B,QAAA,CAAA9B,IAAA,KAAA8B,QAAA,CAAA9B,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAA6B,QAAA,CAAA9B,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAA0B,cAAA,GAAAD,QAAA,CAAA9B,IAAA;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA;;;AAGA,IAAYgC,YAQX;AAAA;AAAAjC,cAAA,GAAAoB,CAAA;AARD,WAAYa,YAAY;EAAA;EAAAjC,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EACtBa,YAAA,yCAAqC;EAAA;EAAAjC,cAAA,GAAAoB,CAAA;EACrCa,YAAA,+BAA2B;EAAA;EAAAjC,cAAA,GAAAoB,CAAA;EAC3Ba,YAAA,2BAAuB;EAAA;EAAAjC,cAAA,GAAAoB,CAAA;EACvBa,YAAA,6CAAyC;EAAA;EAAAjC,cAAA,GAAAoB,CAAA;EACzCa,YAAA,uCAAmC;EAAA;EAAAjC,cAAA,GAAAoB,CAAA;EACnCa,YAAA,mCAA+B;EAAA;EAAAjC,cAAA,GAAAoB,CAAA;EAC/Ba,YAAA,qBAAiB;AACnB,CAAC;AARW;AAAA,CAAAjC,cAAA,GAAAsB,CAAA,WAAAW,YAAY;AAAA;AAAA,CAAAjC,cAAA,GAAAsB,CAAA,WAAAY,OAAA,CAAAD,YAAA,GAAZA,YAAY;AAwBxB,IAAYE,WAOX;AAAA;AAAAnC,cAAA,GAAAoB,CAAA;AAPD,WAAYe,WAAW;EAAA;EAAAnC,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EACrBe,WAAA,mCAA+B;EAAA;EAAAnC,cAAA,GAAAoB,CAAA;EAC/Be,WAAA,mBAAe;EAAA;EAAAnC,cAAA,GAAAoB,CAAA;EACfe,WAAA,uBAAmB;EAAA;EAAAnC,cAAA,GAAAoB,CAAA;EACnBe,WAAA,qBAAiB;EAAA;EAAAnC,cAAA,GAAAoB,CAAA;EACjBe,WAAA,2BAAuB;EAAA;EAAAnC,cAAA,GAAAoB,CAAA;EACvBe,WAAA,qBAAiB;AACnB,CAAC;AAPW;AAAA,CAAAnC,cAAA,GAAAsB,CAAA,WAAAa,WAAW;AAAA;AAAA,CAAAnC,cAAA,GAAAsB,CAAA,WAAAY,OAAA,CAAAC,WAAA,GAAXA,WAAW;AASvB,IAAYC,aAKX;AAAA;AAAApC,cAAA,GAAAoB,CAAA;AALD,WAAYgB,aAAa;EAAA;EAAApC,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EACvBgB,aAAA,mBAAe;EAAA;EAAApC,cAAA,GAAAoB,CAAA;EACfgB,aAAA,yBAAqB;EAAA;EAAApC,cAAA,GAAAoB,CAAA;EACrBgB,aAAA,mCAA+B;EAAA;EAAApC,cAAA,GAAAoB,CAAA;EAC/BgB,aAAA,qCAAiC;AACnC,CAAC;AALW;AAAA,CAAApC,cAAA,GAAAsB,CAAA,WAAAc,aAAa;AAAA;AAAA,CAAApC,cAAA,GAAAsB,CAAA,WAAAY,OAAA,CAAAE,aAAA,GAAbA,aAAa;AA8EzB,IAAYC,iBAMX;AAAA;AAAArC,cAAA,GAAAoB,CAAA;AAND,WAAYiB,iBAAiB;EAAA;EAAArC,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EAC3BiB,iBAAA,6BAAyB;EAAA;EAAArC,cAAA,GAAAoB,CAAA;EACzBiB,iBAAA,yBAAqB;EAAA;EAAArC,cAAA,GAAAoB,CAAA;EACrBiB,iBAAA,2BAAuB;EAAA;EAAArC,cAAA,GAAAoB,CAAA;EACvBiB,iBAAA,2CAAuC;EAAA;EAAArC,cAAA,GAAAoB,CAAA;EACvCiB,iBAAA,6BAAyB;AAC3B,CAAC;AANW;AAAA,CAAArC,cAAA,GAAAsB,CAAA,WAAAe,iBAAiB;AAAA;AAAA,CAAArC,cAAA,GAAAsB,CAAA,WAAAY,OAAA,CAAAG,iBAAA,GAAjBA,iBAAiB;AAQ7B,IAAYC,gBAKX;AAAA;AAAAtC,cAAA,GAAAoB,CAAA;AALD,WAAYkB,gBAAgB;EAAA;EAAAtC,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EAC1BkB,gBAAA,iBAAa;EAAA;EAAAtC,cAAA,GAAAoB,CAAA;EACbkB,gBAAA,qBAAiB;EAAA;EAAAtC,cAAA,GAAAoB,CAAA;EACjBkB,gBAAA,eAAW;EAAA;EAAAtC,cAAA,GAAAoB,CAAA;EACXkB,gBAAA,2BAAuB;AACzB,CAAC;AALW;AAAA,CAAAtC,cAAA,GAAAsB,CAAA,WAAAgB,gBAAgB;AAAA;AAAA,CAAAtC,cAAA,GAAAsB,CAAA,WAAAY,OAAA,CAAAI,gBAAA,GAAhBA,gBAAgB;AAwC5B,IAAYC,YAMX;AAAA;AAAAvC,cAAA,GAAAoB,CAAA;AAND,WAAYmB,YAAY;EAAA;EAAAvC,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EACtBmB,YAAA,2BAAuB;EAAA;EAAAvC,cAAA,GAAAoB,CAAA;EACvBmB,YAAA,iBAAa;EAAA;EAAAvC,cAAA,GAAAoB,CAAA;EACbmB,YAAA,6BAAyB;EAAA;EAAAvC,cAAA,GAAAoB,CAAA;EACzBmB,YAAA,iBAAa;EAAA;EAAAvC,cAAA,GAAAoB,CAAA;EACbmB,YAAA,yBAAqB;AACvB,CAAC;AANW;AAAA,CAAAvC,cAAA,GAAAsB,CAAA,WAAAiB,YAAY;AAAA;AAAA,CAAAvC,cAAA,GAAAsB,CAAA,WAAAY,OAAA,CAAAK,YAAA,GAAZA,YAAY;AAqBxB,IAAYC,cAMX;AAAA;AAAAxC,cAAA,GAAAoB,CAAA;AAND,WAAYoB,cAAc;EAAA;EAAAxC,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EACxBoB,cAAA,2BAAuB;EAAA;EAAAxC,cAAA,GAAAoB,CAAA;EACvBoB,cAAA,qBAAiB;EAAA;EAAAxC,cAAA,GAAAoB,CAAA;EACjBoB,cAAA,2BAAuB;EAAA;EAAAxC,cAAA,GAAAoB,CAAA;EACvBoB,cAAA,+BAA2B;EAAA;EAAAxC,cAAA,GAAAoB,CAAA;EAC3BoB,cAAA,uBAAmB;AACrB,CAAC;AANW;AAAA,CAAAxC,cAAA,GAAAsB,CAAA,WAAAkB,cAAc;AAAA;AAAA,CAAAxC,cAAA,GAAAsB,CAAA,WAAAY,OAAA,CAAAM,cAAA,GAAdA,cAAc;AAe1B,IAAYC,MAKX;AAAA;AAAAzC,cAAA,GAAAoB,CAAA;AALD,WAAYqB,MAAM;EAAA;EAAAzC,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EAChBqB,MAAA,qBAAiB;EAAA;EAAAzC,cAAA,GAAAoB,CAAA;EACjBqB,MAAA,qBAAiB;EAAA;EAAAzC,cAAA,GAAAoB,CAAA;EACjBqB,MAAA,iBAAa;EAAA;EAAAzC,cAAA,GAAAoB,CAAA;EACbqB,MAAA,qBAAiB;AACnB,CAAC;AALW;AAAA,CAAAzC,cAAA,GAAAsB,CAAA,WAAAmB,MAAM;AAAA;AAAA,CAAAzC,cAAA,GAAAsB,CAAA,WAAAY,OAAA,CAAAO,MAAA,GAANA,MAAM;AAuBlB,IAAYC,SAOX;AAAA;AAAA1C,cAAA,GAAAoB,CAAA;AAPD,WAAYsB,SAAS;EAAA;EAAA1C,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EACnBsB,SAAA,6CAAyC;EAAA;EAAA1C,cAAA,GAAAoB,CAAA;EACzCsB,SAAA,2CAAuC;EAAA;EAAA1C,cAAA,GAAAoB,CAAA;EACvCsB,SAAA,yCAAqC;EAAA;EAAA1C,cAAA,GAAAoB,CAAA;EACrCsB,SAAA,uCAAmC;EAAA;EAAA1C,cAAA,GAAAoB,CAAA;EACnCsB,SAAA,mCAA+B;EAAA;EAAA1C,cAAA,GAAAoB,CAAA;EAC/BsB,SAAA,uCAAmC;AACrC,CAAC;AAPW;AAAA,CAAA1C,cAAA,GAAAsB,CAAA,WAAAoB,SAAS;AAAA;AAAA,CAAA1C,cAAA,GAAAsB,CAAA,WAAAY,OAAA,CAAAQ,SAAA,GAATA,SAAS;AASrB,IAAYC,aAMX;AAAA;AAAA3C,cAAA,GAAAoB,CAAA;AAND,WAAYuB,aAAa;EAAA;EAAA3C,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EACvBuB,aAAA,yBAAqB;EAAA;EAAA3C,cAAA,GAAAoB,CAAA;EACrBuB,aAAA,iBAAa;EAAA;EAAA3C,cAAA,GAAAoB,CAAA;EACbuB,aAAA,qBAAiB;EAAA;EAAA3C,cAAA,GAAAoB,CAAA;EACjBuB,aAAA,eAAW;EAAA;EAAA3C,cAAA,GAAAoB,CAAA;EACXuB,aAAA,iBAAa;AACf,CAAC;AANW;AAAA,CAAA3C,cAAA,GAAAsB,CAAA,WAAAqB,aAAa;AAAA;AAAA,CAAA3C,cAAA,GAAAsB,CAAA,WAAAY,OAAA,CAAAS,aAAA,GAAbA,aAAa;AAkDzB,IAAYC,WAMX;AAAA;AAAA5C,cAAA,GAAAoB,CAAA;AAND,WAAYwB,WAAW;EAAA;EAAA5C,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EACrBwB,WAAA,eAAW;EAAA;EAAA5C,cAAA,GAAAoB,CAAA;EACXwB,WAAA,eAAW;EAAA;EAAA5C,cAAA,GAAAoB,CAAA;EACXwB,WAAA,qBAAiB;EAAA;EAAA5C,cAAA,GAAAoB,CAAA;EACjBwB,WAAA,qBAAiB;EAAA;EAAA5C,cAAA,GAAAoB,CAAA;EACjBwB,WAAA,aAAS;AACX,CAAC;AANW;AAAA,CAAA5C,cAAA,GAAAsB,CAAA,WAAAsB,WAAW;AAAA;AAAA,CAAA5C,cAAA,GAAAsB,CAAA,WAAAY,OAAA,CAAAU,WAAA,GAAXA,WAAW;AAQvB,IAAYC,SAMX;AAAA;AAAA7C,cAAA,GAAAoB,CAAA;AAND,WAAYyB,SAAS;EAAA;EAAA7C,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EACnByB,SAAA,qBAAiB;EAAA;EAAA7C,cAAA,GAAAoB,CAAA;EACjByB,SAAA,mBAAe;EAAA;EAAA7C,cAAA,GAAAoB,CAAA;EACfyB,SAAA,qBAAiB;EAAA;EAAA7C,cAAA,GAAAoB,CAAA;EACjByB,SAAA,uBAAmB;EAAA;EAAA7C,cAAA,GAAAoB,CAAA;EACnByB,SAAA,yBAAqB;AACvB,CAAC;AANW;AAAA,CAAA7C,cAAA,GAAAsB,CAAA,WAAAuB,SAAS;AAAA;AAAA,CAAA7C,cAAA,GAAAsB,CAAA,WAAAY,OAAA,CAAAW,SAAA,GAATA,SAAS;AA+ErB,IAAYC,kBAKX;AAAA;AAAA9C,cAAA,GAAAoB,CAAA;AALD,WAAY0B,kBAAkB;EAAA;EAAA9C,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EAC5B0B,kBAAA,2CAAuC;EAAA;EAAA9C,cAAA,GAAAoB,CAAA;EACvC0B,kBAAA,uCAAmC;EAAA;EAAA9C,cAAA,GAAAoB,CAAA;EACnC0B,kBAAA,qCAAiC;EAAA;EAAA9C,cAAA,GAAAoB,CAAA;EACjC0B,kBAAA,uDAAmD;AACrD,CAAC;AALW;AAAA,CAAA9C,cAAA,GAAAsB,CAAA,WAAAwB,kBAAkB;AAAA;AAAA,CAAA9C,cAAA,GAAAsB,CAAA,WAAAY,OAAA,CAAAY,kBAAA,GAAlBA,kBAAkB;AAO9B,IAAYC,gBAIX;AAAA;AAAA/C,cAAA,GAAAoB,CAAA;AAJD,WAAY2B,gBAAgB;EAAA;EAAA/C,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EAC1B2B,gBAAA,eAAW;EAAA;EAAA/C,cAAA,GAAAoB,CAAA;EACX2B,gBAAA,qBAAiB;EAAA;EAAA/C,cAAA,GAAAoB,CAAA;EACjB2B,gBAAA,iBAAa;AACf,CAAC;AAJW;AAAA,CAAA/C,cAAA,GAAAsB,CAAA,WAAAyB,gBAAgB;AAAA;AAAA,CAAA/C,cAAA,GAAAsB,CAAA,WAAAY,OAAA,CAAAa,gBAAA,GAAhBA,gBAAgB;AAqE5B,IAAYC,aAIX;AAAA;AAAAhD,cAAA,GAAAoB,CAAA;AAJD,WAAY4B,aAAa;EAAA;EAAAhD,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EACvB4B,aAAA,uBAAmB;EAAA;EAAAhD,cAAA,GAAAoB,CAAA;EACnB4B,aAAA,2BAAuB;EAAA;EAAAhD,cAAA,GAAAoB,CAAA;EACvB4B,aAAA,yBAAqB;AACvB,CAAC;AAJW;AAAA,CAAAhD,cAAA,GAAAsB,CAAA,WAAA0B,aAAa;AAAA;AAAA,CAAAhD,cAAA,GAAAsB,CAAA,WAAAY,OAAA,CAAAc,aAAA,GAAbA,aAAa;AAMzB,IAAYC,aAIX;AAAA;AAAAjD,cAAA,GAAAoB,CAAA;AAJD,WAAY6B,aAAa;EAAA;EAAAjD,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EACvB6B,aAAA,uBAAmB;EAAA;EAAAjD,cAAA,GAAAoB,CAAA;EACnB6B,aAAA,uBAAmB;EAAA;EAAAjD,cAAA,GAAAoB,CAAA;EACnB6B,aAAA,uBAAmB,EAAE;AACvB,CAAC;AAJW;AAAA,CAAAjD,cAAA,GAAAsB,CAAA,WAAA2B,aAAa;AAAA;AAAA,CAAAjD,cAAA,GAAAsB,CAAA,WAAAY,OAAA,CAAAe,aAAA,GAAbA,aAAa;AAsCzB,IAAYC,YAOX;AAAA;AAAAlD,cAAA,GAAAoB,CAAA;AAPD,WAAY8B,YAAY;EAAA;EAAAlD,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EACtB8B,YAAA,qBAAiB;EAAA;EAAAlD,cAAA,GAAAoB,CAAA;EACjB8B,YAAA,qBAAiB;EAAA;EAAAlD,cAAA,GAAAoB,CAAA;EACjB8B,YAAA,6BAAyB;EAAA;EAAAlD,cAAA,GAAAoB,CAAA;EACzB8B,YAAA,6CAAyC;EAAA;EAAAlD,cAAA,GAAAoB,CAAA;EACzC8B,YAAA,2CAAuC;EAAA;EAAAlD,cAAA,GAAAoB,CAAA;EACvC8B,YAAA,qCAAiC;AACnC,CAAC;AAPW;AAAA,CAAAlD,cAAA,GAAAsB,CAAA,WAAA4B,YAAY;AAAA;AAAA,CAAAlD,cAAA,GAAAsB,CAAA,WAAAY,OAAA,CAAAgB,YAAA,GAAZA,YAAY;AAoBxB,IAAYC,gBAMX;AAAA;AAAAnD,cAAA,GAAAoB,CAAA;AAND,WAAY+B,gBAAgB;EAAA;EAAAnD,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EAC1B+B,gBAAA,2BAAuB;EAAA;EAAAnD,cAAA,GAAAoB,CAAA;EACvB+B,gBAAA,mCAA+B;EAAA;EAAAnD,cAAA,GAAAoB,CAAA;EAC/B+B,gBAAA,yBAAqB;EAAA;EAAAnD,cAAA,GAAAoB,CAAA;EACrB+B,gBAAA,qCAAiC;EAAA;EAAAnD,cAAA,GAAAoB,CAAA;EACjC+B,gBAAA,uCAAmC;AACrC,CAAC;AANW;AAAA,CAAAnD,cAAA,GAAAsB,CAAA,WAAA6B,gBAAgB;AAAA;AAAA,CAAAnD,cAAA,GAAAsB,CAAA,WAAAY,OAAA,CAAAiB,gBAAA,GAAhBA,gBAAgB;AAmB5B,IAAYC,SAQX;AAAA;AAAApD,cAAA,GAAAoB,CAAA;AARD,WAAYgC,SAAS;EAAA;EAAApD,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EACnBgC,SAAA,uBAAmB;EAAA;EAAApD,cAAA,GAAAoB,CAAA;EACnBgC,SAAA,uBAAmB;EAAA;EAAApD,cAAA,GAAAoB,CAAA;EACnBgC,SAAA,mBAAe;EAAA;EAAApD,cAAA,GAAAoB,CAAA;EACfgC,SAAA,iBAAa;EAAA;EAAApD,cAAA,GAAAoB,CAAA;EACbgC,SAAA,mBAAe;EAAA;EAAApD,cAAA,GAAAoB,CAAA;EACfgC,SAAA,qBAAiB;EAAA;EAAApD,cAAA,GAAAoB,CAAA;EACjBgC,SAAA,mBAAe;AACjB,CAAC;AARW;AAAA,CAAApD,cAAA,GAAAsB,CAAA,WAAA8B,SAAS;AAAA;AAAA,CAAApD,cAAA,GAAAsB,CAAA,WAAAY,OAAA,CAAAkB,SAAA,GAATA,SAAS;AAiBrB,IAAYC,gBAKX;AAAA;AAAArD,cAAA,GAAAoB,CAAA;AALD,WAAYiC,gBAAgB;EAAA;EAAArD,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EAC1BiC,gBAAA,qBAAiB;EAAA;EAAArD,cAAA,GAAAoB,CAAA;EACjBiC,gBAAA,uBAAmB;EAAA;EAAArD,cAAA,GAAAoB,CAAA;EACnBiC,gBAAA,6BAAyB;EAAA;EAAArD,cAAA,GAAAoB,CAAA;EACzBiC,gBAAA,2BAAuB;AACzB,CAAC;AALW;AAAA,CAAArD,cAAA,GAAAsB,CAAA,WAAA+B,gBAAgB;AAAA;AAAA,CAAArD,cAAA,GAAAsB,CAAA,WAAAY,OAAA,CAAAmB,gBAAA,GAAhBA,gBAAgB;AA2B5B,IAAYC,UAMX;AAAA;AAAAtD,cAAA,GAAAoB,CAAA;AAND,WAAYkC,UAAU;EAAA;EAAAtD,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EACpBkC,UAAA,6BAAyB;EAAA;EAAAtD,cAAA,GAAAoB,CAAA;EACzBkC,UAAA,6BAAyB;EAAA;EAAAtD,cAAA,GAAAoB,CAAA;EACzBkC,UAAA,+BAA2B;EAAA;EAAAtD,cAAA,GAAAoB,CAAA;EAC3BkC,UAAA,2BAAuB;EAAA;EAAAtD,cAAA,GAAAoB,CAAA;EACvBkC,UAAA,mDAA+C;AACjD,CAAC;AANW;AAAA,CAAAtD,cAAA,GAAAsB,CAAA,WAAAgC,UAAU;AAAA;AAAA,CAAAtD,cAAA,GAAAsB,CAAA,WAAAY,OAAA,CAAAoB,UAAA,GAAVA,UAAU;AA4CtB,IAAYC,kBAMX;AAAA;AAAAvD,cAAA,GAAAoB,CAAA;AAND,WAAYmC,kBAAkB;EAAA;EAAAvD,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EAC5BmC,kBAAA,iCAA6B;EAAA;EAAAvD,cAAA,GAAAoB,CAAA;EAC7BmC,kBAAA,+BAA2B;EAAA;EAAAvD,cAAA,GAAAoB,CAAA;EAC3BmC,kBAAA,uBAAmB;EAAA;EAAAvD,cAAA,GAAAoB,CAAA;EACnBmC,kBAAA,6CAAyC;EAAA;EAAAvD,cAAA,GAAAoB,CAAA;EACzCmC,kBAAA,+CAA2C;AAC7C,CAAC;AANW;AAAA,CAAAvD,cAAA,GAAAsB,CAAA,WAAAiC,kBAAkB;AAAA;AAAA,CAAAvD,cAAA,GAAAsB,CAAA,WAAAY,OAAA,CAAAqB,kBAAA,GAAlBA,kBAAkB;AAQ9B,IAAYC,sBAKX;AAAA;AAAAxD,cAAA,GAAAoB,CAAA;AALD,WAAYoC,sBAAsB;EAAA;EAAAxD,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EAChCoC,sBAAA,yBAAqB;EAAA;EAAAxD,cAAA,GAAAoB,CAAA;EACrBoC,sBAAA,iBAAa;EAAA;EAAAxD,cAAA,GAAAoB,CAAA;EACboC,sBAAA,qBAAiB;EAAA;EAAAxD,cAAA,GAAAoB,CAAA;EACjBoC,sBAAA,eAAW;AACb,CAAC;AALW;AAAA,CAAAxD,cAAA,GAAAsB,CAAA,WAAAkC,sBAAsB;AAAA;AAAA,CAAAxD,cAAA,GAAAsB,CAAA,WAAAY,OAAA,CAAAsB,sBAAA,GAAtBA,sBAAsB;AAelC,IAAYC,wBAKX;AAAA;AAAAzD,cAAA,GAAAoB,CAAA;AALD,WAAYqC,wBAAwB;EAAA;EAAAzD,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EAClCqC,wBAAA,qBAAiB;EAAA;EAAAzD,cAAA,GAAAoB,CAAA;EACjBqC,wBAAA,yBAAqB;EAAA;EAAAzD,cAAA,GAAAoB,CAAA;EACrBqC,wBAAA,uBAAmB;EAAA;EAAAzD,cAAA,GAAAoB,CAAA;EACnBqC,wBAAA,iCAA6B;AAC/B,CAAC;AALW;AAAA,CAAAzD,cAAA,GAAAsB,CAAA,WAAAmC,wBAAwB;AAAA;AAAA,CAAAzD,cAAA,GAAAsB,CAAA,WAAAY,OAAA,CAAAuB,wBAAA,GAAxBA,wBAAwB;AAqBpC,IAAYC,aAMX;AAAA;AAAA1D,cAAA,GAAAoB,CAAA;AAND,WAAYsC,aAAa;EAAA;EAAA1D,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EACvBsC,aAAA,yCAAqC;EAAA;EAAA1D,cAAA,GAAAoB,CAAA;EACrCsC,aAAA,mCAA+B;EAAA;EAAA1D,cAAA,GAAAoB,CAAA;EAC/BsC,aAAA,6CAAyC;EAAA;EAAA1D,cAAA,GAAAoB,CAAA;EACzCsC,aAAA,mCAA+B;EAAA;EAAA1D,cAAA,GAAAoB,CAAA;EAC/BsC,aAAA,qDAAiD;AACnD,CAAC;AANW;AAAA,CAAA1D,cAAA,GAAAsB,CAAA,WAAAoC,aAAa;AAAA;AAAA,CAAA1D,cAAA,GAAAsB,CAAA,WAAAY,OAAA,CAAAwB,aAAA,GAAbA,aAAa;AAwBzB,IAAYC,UAKX;AAAA;AAAA3D,cAAA,GAAAoB,CAAA;AALD,WAAYuC,UAAU;EAAA;EAAA3D,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EACpBuC,UAAA,mBAAe;EAAA;EAAA3D,cAAA,GAAAoB,CAAA;EACfuC,UAAA,qBAAiB;EAAA;EAAA3D,cAAA,GAAAoB,CAAA;EACjBuC,UAAA,mBAAe;EAAA;EAAA3D,cAAA,GAAAoB,CAAA;EACfuC,UAAA,+BAA2B;AAC7B,CAAC;AALW;AAAA,CAAA3D,cAAA,GAAAsB,CAAA,WAAAqC,UAAU;AAAA;AAAA,CAAA3D,cAAA,GAAAsB,CAAA,WAAAY,OAAA,CAAAyB,UAAA,GAAVA,UAAU;AAOtB,IAAYC,YASX;AAAA;AAAA5D,cAAA,GAAAoB,CAAA;AATD,WAAYwC,YAAY;EAAA;EAAA5D,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EACtBwC,YAAA,qBAAiB;EAAA;EAAA5D,cAAA,GAAAoB,CAAA;EACjBwC,YAAA,qBAAiB;EAAA;EAAA5D,cAAA,GAAAoB,CAAA;EACjBwC,YAAA,6BAAyB;EAAA;EAAA5D,cAAA,GAAAoB,CAAA;EACzBwC,YAAA,2BAAuB;EAAA;EAAA5D,cAAA,GAAAoB,CAAA;EACvBwC,YAAA,6BAAyB;EAAA;EAAA5D,cAAA,GAAAoB,CAAA;EACzBwC,YAAA,+BAA2B;EAAA;EAAA5D,cAAA,GAAAoB,CAAA;EAC3BwC,YAAA,+BAA2B;EAAA;EAAA5D,cAAA,GAAAoB,CAAA;EAC3BwC,YAAA,mBAAe;AACjB,CAAC;AATW;AAAA,CAAA5D,cAAA,GAAAsB,CAAA,WAAAsC,YAAY;AAAA;AAAA,CAAA5D,cAAA,GAAAsB,CAAA,WAAAY,OAAA,CAAA0B,YAAA,GAAZA,YAAY;AA+BxB,IAAYC,2BAKX;AAAA;AAAA7D,cAAA,GAAAoB,CAAA;AALD,WAAYyC,2BAA2B;EAAA;EAAA7D,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EACrCyC,2BAAA,yBAAqB;EAAA;EAAA7D,cAAA,GAAAoB,CAAA;EACrByC,2BAAA,6BAAyB;EAAA;EAAA7D,cAAA,GAAAoB,CAAA;EACzByC,2BAAA,2BAAuB;EAAA;EAAA7D,cAAA,GAAAoB,CAAA;EACvByC,2BAAA,6CAAyC;AAC3C,CAAC;AALW;AAAA,CAAA7D,cAAA,GAAAsB,CAAA,WAAAuC,2BAA2B;AAAA;AAAA,CAAA7D,cAAA,GAAAsB,CAAA,WAAAY,OAAA,CAAA2B,2BAAA,GAA3BA,2BAA2B;AAgBvC,IAAYC,aAQX;AAAA;AAAA9D,cAAA,GAAAoB,CAAA;AARD,WAAY0C,aAAa;EAAA;EAAA9D,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EACvB0C,aAAA,eAAW;EAAA;EAAA9D,cAAA,GAAAoB,CAAA;EACX0C,aAAA,mBAAe;EAAA;EAAA9D,cAAA,GAAAoB,CAAA;EACf0C,aAAA,eAAW;EAAA;EAAA9D,cAAA,GAAAoB,CAAA;EACX0C,aAAA,qBAAiB;EAAA;EAAA9D,cAAA,GAAAoB,CAAA;EACjB0C,aAAA,qBAAiB;EAAA;EAAA9D,cAAA,GAAAoB,CAAA;EACjB0C,aAAA,iBAAa;EAAA;EAAA9D,cAAA,GAAAoB,CAAA;EACb0C,aAAA,yBAAqB;AACvB,CAAC;AARW;AAAA,CAAA9D,cAAA,GAAAsB,CAAA,WAAAwC,aAAa;AAAA;AAAA,CAAA9D,cAAA,GAAAsB,CAAA,WAAAY,OAAA,CAAA4B,aAAA,GAAbA,aAAa;AAUzB,IAAYC,iBAMX;AAAA;AAAA/D,cAAA,GAAAoB,CAAA;AAND,WAAY2C,iBAAiB;EAAA;EAAA/D,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EAC3B2C,iBAAA,2BAAuB;EAAA;EAAA/D,cAAA,GAAAoB,CAAA;EACvB2C,iBAAA,iBAAa;EAAA;EAAA/D,cAAA,GAAAoB,CAAA;EACb2C,iBAAA,iBAAa;EAAA;EAAA/D,cAAA,GAAAoB,CAAA;EACb2C,iBAAA,iBAAa;EAAA;EAAA/D,cAAA,GAAAoB,CAAA;EACb2C,iBAAA,yBAAqB;AACvB,CAAC;AANW;AAAA,CAAA/D,cAAA,GAAAsB,CAAA,WAAAyC,iBAAiB;AAAA;AAAA,CAAA/D,cAAA,GAAAsB,CAAA,WAAAY,OAAA,CAAA6B,iBAAA,GAAjBA,iBAAiB;AA0C7B,IAAYC,SAMX;AAAA;AAAAhE,cAAA,GAAAoB,CAAA;AAND,WAAY4C,SAAS;EAAA;EAAAhE,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EACnB4C,SAAA,2CAAuC;EAAA;EAAAhE,cAAA,GAAAoB,CAAA;EACvC4C,SAAA,mDAA+C;EAAA;EAAAhE,cAAA,GAAAoB,CAAA;EAC/C4C,SAAA,+BAA2B;EAAA;EAAAhE,cAAA,GAAAoB,CAAA;EAC3B4C,SAAA,yCAAqC;EAAA;EAAAhE,cAAA,GAAAoB,CAAA;EACrC4C,SAAA,mCAA+B;AACjC,CAAC;AANW;AAAA,CAAAhE,cAAA,GAAAsB,CAAA,WAAA0C,SAAS;AAAA;AAAA,CAAAhE,cAAA,GAAAsB,CAAA,WAAAY,OAAA,CAAA8B,SAAA,GAATA,SAAS;AAyBrB,IAAYC,WAMX;AAAA;AAAAjE,cAAA,GAAAoB,CAAA;AAND,WAAY6C,WAAW;EAAA;EAAAjE,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EACrB6C,WAAA,mCAA+B;EAAA;EAAAjE,cAAA,GAAAoB,CAAA;EAC/B6C,WAAA,mCAA+B;EAAA;EAAAjE,cAAA,GAAAoB,CAAA;EAC/B6C,WAAA,qCAAiC;EAAA;EAAAjE,cAAA,GAAAoB,CAAA;EACjC6C,WAAA,uBAAmB;EAAA;EAAAjE,cAAA,GAAAoB,CAAA;EACnB6C,WAAA,mCAA+B;AACjC,CAAC;AANW;AAAA,CAAAjE,cAAA,GAAAsB,CAAA,WAAA2C,WAAW;AAAA;AAAA,CAAAjE,cAAA,GAAAsB,CAAA,WAAAY,OAAA,CAAA+B,WAAA,GAAXA,WAAW;AAQvB,IAAYC,eAKX;AAAA;AAAAlE,cAAA,GAAAoB,CAAA;AALD,WAAY8C,eAAe;EAAA;EAAAlE,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EACzB8C,eAAA,yBAAqB;EAAA;EAAAlE,cAAA,GAAAoB,CAAA;EACrB8C,eAAA,iBAAa;EAAA;EAAAlE,cAAA,GAAAoB,CAAA;EACb8C,eAAA,qBAAiB;EAAA;EAAAlE,cAAA,GAAAoB,CAAA;EACjB8C,eAAA,eAAW;AACb,CAAC;AALW;AAAA,CAAAlE,cAAA,GAAAsB,CAAA,WAAA4C,eAAe;AAAA;AAAA,CAAAlE,cAAA,GAAAsB,CAAA,WAAAY,OAAA,CAAAgC,eAAA,GAAfA,eAAe;AAc3B,IAAYC,aAOX;AAAA;AAAAnE,cAAA,GAAAoB,CAAA;AAPD,WAAY+C,aAAa;EAAA;EAAAnE,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EACvB+C,aAAA,2CAAuC;EAAA;EAAAnE,cAAA,GAAAoB,CAAA;EACvC+C,aAAA,2CAAuC;EAAA;EAAAnE,cAAA,GAAAoB,CAAA;EACvC+C,aAAA,6CAAyC;EAAA;EAAAnE,cAAA,GAAAoB,CAAA;EACzC+C,aAAA,iDAA6C;EAAA;EAAAnE,cAAA,GAAAoB,CAAA;EAC7C+C,aAAA,qCAAiC;EAAA;EAAAnE,cAAA,GAAAoB,CAAA;EACjC+C,aAAA,uBAAmB;AACrB,CAAC;AAPW;AAAA,CAAAnE,cAAA,GAAAsB,CAAA,WAAA6C,aAAa;AAAA;AAAA,CAAAnE,cAAA,GAAAsB,CAAA,WAAAY,OAAA,CAAAiC,aAAA,GAAbA,aAAa;AAwDzB;AAAA;AAAAnE,cAAA,GAAAoB,CAAA;AACAgD,YAAA,CAAAC,OAAA,+BAAAnC,OAAA", "ignoreList": []}