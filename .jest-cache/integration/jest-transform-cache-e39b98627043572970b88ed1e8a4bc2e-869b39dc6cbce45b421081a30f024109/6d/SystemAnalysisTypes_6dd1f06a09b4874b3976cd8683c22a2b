1293c9cf93021d198fe4d0648c1b2741
"use strict";

/**
 * System Analysis Type Definitions
 *
 * Comprehensive TypeScript interfaces for Phase 3 Priority 3: Advanced System Analysis Tools
 * Includes system performance analysis, energy efficiency metrics, lifecycle cost analysis,
 * environmental impact assessment, and compliance checking frameworks.
 *
 * @version 3.0.0
 * <AUTHOR> Suite Development Team
 */
/* istanbul ignore next */
function cov_12k33bco6g() {
  var path = "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\types\\SystemAnalysisTypes.ts";
  var hash = "d685e5bbcacfdd0df1ebfe89daf27e6f10aae0b7";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\types\\SystemAnalysisTypes.ts",
    statementMap: {
      "0": {
        start: {
          line: 12,
          column: 22
        },
        end: {
          line: 22,
          column: 3
        }
      },
      "1": {
        start: {
          line: 13,
          column: 4
        },
        end: {
          line: 13,
          column: 33
        }
      },
      "2": {
        start: {
          line: 13,
          column: 26
        },
        end: {
          line: 13,
          column: 33
        }
      },
      "3": {
        start: {
          line: 14,
          column: 15
        },
        end: {
          line: 14,
          column: 52
        }
      },
      "4": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 17,
          column: 5
        }
      },
      "5": {
        start: {
          line: 16,
          column: 6
        },
        end: {
          line: 16,
          column: 68
        }
      },
      "6": {
        start: {
          line: 16,
          column: 51
        },
        end: {
          line: 16,
          column: 63
        }
      },
      "7": {
        start: {
          line: 18,
          column: 4
        },
        end: {
          line: 18,
          column: 39
        }
      },
      "8": {
        start: {
          line: 20,
          column: 4
        },
        end: {
          line: 20,
          column: 33
        }
      },
      "9": {
        start: {
          line: 20,
          column: 26
        },
        end: {
          line: 20,
          column: 33
        }
      },
      "10": {
        start: {
          line: 21,
          column: 4
        },
        end: {
          line: 21,
          column: 17
        }
      },
      "11": {
        start: {
          line: 23,
          column: 19
        },
        end: {
          line: 25,
          column: 1
        }
      },
      "12": {
        start: {
          line: 24,
          column: 4
        },
        end: {
          line: 24,
          column: 126
        }
      },
      "13": {
        start: {
          line: 24,
          column: 21
        },
        end: {
          line: 24,
          column: 126
        }
      },
      "14": {
        start: {
          line: 24,
          column: 95
        },
        end: {
          line: 24,
          column: 126
        }
      },
      "15": {
        start: {
          line: 26,
          column: 0
        },
        end: {
          line: 26,
          column: 62
        }
      },
      "16": {
        start: {
          line: 27,
          column: 0
        },
        end: {
          line: 27,
          column: 851
        }
      },
      "17": {
        start: {
          line: 32,
          column: 0
        },
        end: {
          line: 40,
          column: 63
        }
      },
      "18": {
        start: {
          line: 33,
          column: 4
        },
        end: {
          line: 33,
          column: 58
        }
      },
      "19": {
        start: {
          line: 34,
          column: 4
        },
        end: {
          line: 34,
          column: 48
        }
      },
      "20": {
        start: {
          line: 35,
          column: 4
        },
        end: {
          line: 35,
          column: 44
        }
      },
      "21": {
        start: {
          line: 36,
          column: 4
        },
        end: {
          line: 36,
          column: 62
        }
      },
      "22": {
        start: {
          line: 37,
          column: 4
        },
        end: {
          line: 37,
          column: 56
        }
      },
      "23": {
        start: {
          line: 38,
          column: 4
        },
        end: {
          line: 38,
          column: 52
        }
      },
      "24": {
        start: {
          line: 39,
          column: 4
        },
        end: {
          line: 39,
          column: 38
        }
      },
      "25": {
        start: {
          line: 42,
          column: 0
        },
        end: {
          line: 49,
          column: 60
        }
      },
      "26": {
        start: {
          line: 43,
          column: 4
        },
        end: {
          line: 43,
          column: 51
        }
      },
      "27": {
        start: {
          line: 44,
          column: 4
        },
        end: {
          line: 44,
          column: 35
        }
      },
      "28": {
        start: {
          line: 45,
          column: 4
        },
        end: {
          line: 45,
          column: 39
        }
      },
      "29": {
        start: {
          line: 46,
          column: 4
        },
        end: {
          line: 46,
          column: 37
        }
      },
      "30": {
        start: {
          line: 47,
          column: 4
        },
        end: {
          line: 47,
          column: 43
        }
      },
      "31": {
        start: {
          line: 48,
          column: 4
        },
        end: {
          line: 48,
          column: 37
        }
      },
      "32": {
        start: {
          line: 51,
          column: 0
        },
        end: {
          line: 56,
          column: 66
        }
      },
      "33": {
        start: {
          line: 52,
          column: 4
        },
        end: {
          line: 52,
          column: 37
        }
      },
      "34": {
        start: {
          line: 53,
          column: 4
        },
        end: {
          line: 53,
          column: 43
        }
      },
      "35": {
        start: {
          line: 54,
          column: 4
        },
        end: {
          line: 54,
          column: 53
        }
      },
      "36": {
        start: {
          line: 55,
          column: 4
        },
        end: {
          line: 55,
          column: 55
        }
      },
      "37": {
        start: {
          line: 58,
          column: 0
        },
        end: {
          line: 64,
          column: 78
        }
      },
      "38": {
        start: {
          line: 59,
          column: 4
        },
        end: {
          line: 59,
          column: 51
        }
      },
      "39": {
        start: {
          line: 60,
          column: 4
        },
        end: {
          line: 60,
          column: 47
        }
      },
      "40": {
        start: {
          line: 61,
          column: 4
        },
        end: {
          line: 61,
          column: 49
        }
      },
      "41": {
        start: {
          line: 62,
          column: 4
        },
        end: {
          line: 62,
          column: 65
        }
      },
      "42": {
        start: {
          line: 63,
          column: 4
        },
        end: {
          line: 63,
          column: 51
        }
      },
      "43": {
        start: {
          line: 66,
          column: 0
        },
        end: {
          line: 71,
          column: 75
        }
      },
      "44": {
        start: {
          line: 67,
          column: 4
        },
        end: {
          line: 67,
          column: 38
        }
      },
      "45": {
        start: {
          line: 68,
          column: 4
        },
        end: {
          line: 68,
          column: 42
        }
      },
      "46": {
        start: {
          line: 69,
          column: 4
        },
        end: {
          line: 69,
          column: 36
        }
      },
      "47": {
        start: {
          line: 70,
          column: 4
        },
        end: {
          line: 70,
          column: 48
        }
      },
      "48": {
        start: {
          line: 73,
          column: 0
        },
        end: {
          line: 79,
          column: 63
        }
      },
      "49": {
        start: {
          line: 74,
          column: 4
        },
        end: {
          line: 74,
          column: 44
        }
      },
      "50": {
        start: {
          line: 75,
          column: 4
        },
        end: {
          line: 75,
          column: 34
        }
      },
      "51": {
        start: {
          line: 76,
          column: 4
        },
        end: {
          line: 76,
          column: 46
        }
      },
      "52": {
        start: {
          line: 77,
          column: 4
        },
        end: {
          line: 77,
          column: 34
        }
      },
      "53": {
        start: {
          line: 78,
          column: 4
        },
        end: {
          line: 78,
          column: 42
        }
      },
      "54": {
        start: {
          line: 81,
          column: 0
        },
        end: {
          line: 87,
          column: 69
        }
      },
      "55": {
        start: {
          line: 82,
          column: 4
        },
        end: {
          line: 82,
          column: 46
        }
      },
      "56": {
        start: {
          line: 83,
          column: 4
        },
        end: {
          line: 83,
          column: 40
        }
      },
      "57": {
        start: {
          line: 84,
          column: 4
        },
        end: {
          line: 84,
          column: 46
        }
      },
      "58": {
        start: {
          line: 85,
          column: 4
        },
        end: {
          line: 85,
          column: 50
        }
      },
      "59": {
        start: {
          line: 86,
          column: 4
        },
        end: {
          line: 86,
          column: 42
        }
      },
      "60": {
        start: {
          line: 89,
          column: 0
        },
        end: {
          line: 94,
          column: 45
        }
      },
      "61": {
        start: {
          line: 90,
          column: 4
        },
        end: {
          line: 90,
          column: 32
        }
      },
      "62": {
        start: {
          line: 91,
          column: 4
        },
        end: {
          line: 91,
          column: 32
        }
      },
      "63": {
        start: {
          line: 92,
          column: 4
        },
        end: {
          line: 92,
          column: 28
        }
      },
      "64": {
        start: {
          line: 93,
          column: 4
        },
        end: {
          line: 93,
          column: 32
        }
      },
      "65": {
        start: {
          line: 96,
          column: 0
        },
        end: {
          line: 103,
          column: 54
        }
      },
      "66": {
        start: {
          line: 97,
          column: 4
        },
        end: {
          line: 97,
          column: 59
        }
      },
      "67": {
        start: {
          line: 98,
          column: 4
        },
        end: {
          line: 98,
          column: 57
        }
      },
      "68": {
        start: {
          line: 99,
          column: 4
        },
        end: {
          line: 99,
          column: 55
        }
      },
      "69": {
        start: {
          line: 100,
          column: 4
        },
        end: {
          line: 100,
          column: 53
        }
      },
      "70": {
        start: {
          line: 101,
          column: 4
        },
        end: {
          line: 101,
          column: 49
        }
      },
      "71": {
        start: {
          line: 102,
          column: 4
        },
        end: {
          line: 102,
          column: 53
        }
      },
      "72": {
        start: {
          line: 105,
          column: 0
        },
        end: {
          line: 111,
          column: 66
        }
      },
      "73": {
        start: {
          line: 106,
          column: 4
        },
        end: {
          line: 106,
          column: 43
        }
      },
      "74": {
        start: {
          line: 107,
          column: 4
        },
        end: {
          line: 107,
          column: 35
        }
      },
      "75": {
        start: {
          line: 108,
          column: 4
        },
        end: {
          line: 108,
          column: 39
        }
      },
      "76": {
        start: {
          line: 109,
          column: 4
        },
        end: {
          line: 109,
          column: 33
        }
      },
      "77": {
        start: {
          line: 110,
          column: 4
        },
        end: {
          line: 110,
          column: 35
        }
      },
      "78": {
        start: {
          line: 113,
          column: 0
        },
        end: {
          line: 119,
          column: 60
        }
      },
      "79": {
        start: {
          line: 114,
          column: 4
        },
        end: {
          line: 114,
          column: 31
        }
      },
      "80": {
        start: {
          line: 115,
          column: 4
        },
        end: {
          line: 115,
          column: 31
        }
      },
      "81": {
        start: {
          line: 116,
          column: 4
        },
        end: {
          line: 116,
          column: 37
        }
      },
      "82": {
        start: {
          line: 117,
          column: 4
        },
        end: {
          line: 117,
          column: 37
        }
      },
      "83": {
        start: {
          line: 118,
          column: 4
        },
        end: {
          line: 118,
          column: 29
        }
      },
      "84": {
        start: {
          line: 121,
          column: 0
        },
        end: {
          line: 127,
          column: 54
        }
      },
      "85": {
        start: {
          line: 122,
          column: 4
        },
        end: {
          line: 122,
          column: 35
        }
      },
      "86": {
        start: {
          line: 123,
          column: 4
        },
        end: {
          line: 123,
          column: 33
        }
      },
      "87": {
        start: {
          line: 124,
          column: 4
        },
        end: {
          line: 124,
          column: 35
        }
      },
      "88": {
        start: {
          line: 125,
          column: 4
        },
        end: {
          line: 125,
          column: 37
        }
      },
      "89": {
        start: {
          line: 126,
          column: 4
        },
        end: {
          line: 126,
          column: 39
        }
      },
      "90": {
        start: {
          line: 129,
          column: 0
        },
        end: {
          line: 134,
          column: 81
        }
      },
      "91": {
        start: {
          line: 130,
          column: 4
        },
        end: {
          line: 130,
          column: 66
        }
      },
      "92": {
        start: {
          line: 131,
          column: 4
        },
        end: {
          line: 131,
          column: 62
        }
      },
      "93": {
        start: {
          line: 132,
          column: 4
        },
        end: {
          line: 132,
          column: 60
        }
      },
      "94": {
        start: {
          line: 133,
          column: 4
        },
        end: {
          line: 133,
          column: 78
        }
      },
      "95": {
        start: {
          line: 136,
          column: 0
        },
        end: {
          line: 140,
          column: 75
        }
      },
      "96": {
        start: {
          line: 137,
          column: 4
        },
        end: {
          line: 137,
          column: 36
        }
      },
      "97": {
        start: {
          line: 138,
          column: 4
        },
        end: {
          line: 138,
          column: 42
        }
      },
      "98": {
        start: {
          line: 139,
          column: 4
        },
        end: {
          line: 139,
          column: 38
        }
      },
      "99": {
        start: {
          line: 142,
          column: 0
        },
        end: {
          line: 146,
          column: 66
        }
      },
      "100": {
        start: {
          line: 143,
          column: 4
        },
        end: {
          line: 143,
          column: 41
        }
      },
      "101": {
        start: {
          line: 144,
          column: 4
        },
        end: {
          line: 144,
          column: 45
        }
      },
      "102": {
        start: {
          line: 145,
          column: 4
        },
        end: {
          line: 145,
          column: 43
        }
      },
      "103": {
        start: {
          line: 148,
          column: 0
        },
        end: {
          line: 152,
          column: 66
        }
      },
      "104": {
        start: {
          line: 149,
          column: 4
        },
        end: {
          line: 149,
          column: 41
        }
      },
      "105": {
        start: {
          line: 150,
          column: 4
        },
        end: {
          line: 150,
          column: 41
        }
      },
      "106": {
        start: {
          line: 151,
          column: 4
        },
        end: {
          line: 151,
          column: 41
        }
      },
      "107": {
        start: {
          line: 154,
          column: 0
        },
        end: {
          line: 161,
          column: 63
        }
      },
      "108": {
        start: {
          line: 155,
          column: 4
        },
        end: {
          line: 155,
          column: 38
        }
      },
      "109": {
        start: {
          line: 156,
          column: 4
        },
        end: {
          line: 156,
          column: 38
        }
      },
      "110": {
        start: {
          line: 157,
          column: 4
        },
        end: {
          line: 157,
          column: 46
        }
      },
      "111": {
        start: {
          line: 158,
          column: 4
        },
        end: {
          line: 158,
          column: 62
        }
      },
      "112": {
        start: {
          line: 159,
          column: 4
        },
        end: {
          line: 159,
          column: 60
        }
      },
      "113": {
        start: {
          line: 160,
          column: 4
        },
        end: {
          line: 160,
          column: 54
        }
      },
      "114": {
        start: {
          line: 163,
          column: 0
        },
        end: {
          line: 169,
          column: 75
        }
      },
      "115": {
        start: {
          line: 164,
          column: 4
        },
        end: {
          line: 164,
          column: 48
        }
      },
      "116": {
        start: {
          line: 165,
          column: 4
        },
        end: {
          line: 165,
          column: 56
        }
      },
      "117": {
        start: {
          line: 166,
          column: 4
        },
        end: {
          line: 166,
          column: 46
        }
      },
      "118": {
        start: {
          line: 167,
          column: 4
        },
        end: {
          line: 167,
          column: 58
        }
      },
      "119": {
        start: {
          line: 168,
          column: 4
        },
        end: {
          line: 168,
          column: 60
        }
      },
      "120": {
        start: {
          line: 171,
          column: 0
        },
        end: {
          line: 179,
          column: 54
        }
      },
      "121": {
        start: {
          line: 172,
          column: 4
        },
        end: {
          line: 172,
          column: 37
        }
      },
      "122": {
        start: {
          line: 173,
          column: 4
        },
        end: {
          line: 173,
          column: 37
        }
      },
      "123": {
        start: {
          line: 174,
          column: 4
        },
        end: {
          line: 174,
          column: 33
        }
      },
      "124": {
        start: {
          line: 175,
          column: 4
        },
        end: {
          line: 175,
          column: 31
        }
      },
      "125": {
        start: {
          line: 176,
          column: 4
        },
        end: {
          line: 176,
          column: 33
        }
      },
      "126": {
        start: {
          line: 177,
          column: 4
        },
        end: {
          line: 177,
          column: 35
        }
      },
      "127": {
        start: {
          line: 178,
          column: 4
        },
        end: {
          line: 178,
          column: 33
        }
      },
      "128": {
        start: {
          line: 181,
          column: 0
        },
        end: {
          line: 186,
          column: 75
        }
      },
      "129": {
        start: {
          line: 182,
          column: 4
        },
        end: {
          line: 182,
          column: 42
        }
      },
      "130": {
        start: {
          line: 183,
          column: 4
        },
        end: {
          line: 183,
          column: 44
        }
      },
      "131": {
        start: {
          line: 184,
          column: 4
        },
        end: {
          line: 184,
          column: 50
        }
      },
      "132": {
        start: {
          line: 185,
          column: 4
        },
        end: {
          line: 185,
          column: 48
        }
      },
      "133": {
        start: {
          line: 188,
          column: 0
        },
        end: {
          line: 194,
          column: 57
        }
      },
      "134": {
        start: {
          line: 189,
          column: 4
        },
        end: {
          line: 189,
          column: 44
        }
      },
      "135": {
        start: {
          line: 190,
          column: 4
        },
        end: {
          line: 190,
          column: 44
        }
      },
      "136": {
        start: {
          line: 191,
          column: 4
        },
        end: {
          line: 191,
          column: 46
        }
      },
      "137": {
        start: {
          line: 192,
          column: 4
        },
        end: {
          line: 192,
          column: 42
        }
      },
      "138": {
        start: {
          line: 193,
          column: 4
        },
        end: {
          line: 193,
          column: 66
        }
      },
      "139": {
        start: {
          line: 196,
          column: 0
        },
        end: {
          line: 202,
          column: 81
        }
      },
      "140": {
        start: {
          line: 197,
          column: 4
        },
        end: {
          line: 197,
          column: 56
        }
      },
      "141": {
        start: {
          line: 198,
          column: 4
        },
        end: {
          line: 198,
          column: 54
        }
      },
      "142": {
        start: {
          line: 199,
          column: 4
        },
        end: {
          line: 199,
          column: 46
        }
      },
      "143": {
        start: {
          line: 200,
          column: 4
        },
        end: {
          line: 200,
          column: 68
        }
      },
      "144": {
        start: {
          line: 201,
          column: 4
        },
        end: {
          line: 201,
          column: 70
        }
      },
      "145": {
        start: {
          line: 204,
          column: 0
        },
        end: {
          line: 209,
          column: 93
        }
      },
      "146": {
        start: {
          line: 205,
          column: 4
        },
        end: {
          line: 205,
          column: 52
        }
      },
      "147": {
        start: {
          line: 206,
          column: 4
        },
        end: {
          line: 206,
          column: 44
        }
      },
      "148": {
        start: {
          line: 207,
          column: 4
        },
        end: {
          line: 207,
          column: 48
        }
      },
      "149": {
        start: {
          line: 208,
          column: 4
        },
        end: {
          line: 208,
          column: 42
        }
      },
      "150": {
        start: {
          line: 211,
          column: 0
        },
        end: {
          line: 216,
          column: 99
        }
      },
      "151": {
        start: {
          line: 212,
          column: 4
        },
        end: {
          line: 212,
          column: 50
        }
      },
      "152": {
        start: {
          line: 213,
          column: 4
        },
        end: {
          line: 213,
          column: 54
        }
      },
      "153": {
        start: {
          line: 214,
          column: 4
        },
        end: {
          line: 214,
          column: 52
        }
      },
      "154": {
        start: {
          line: 215,
          column: 4
        },
        end: {
          line: 215,
          column: 62
        }
      },
      "155": {
        start: {
          line: 218,
          column: 0
        },
        end: {
          line: 224,
          column: 66
        }
      },
      "156": {
        start: {
          line: 219,
          column: 4
        },
        end: {
          line: 219,
          column: 59
        }
      },
      "157": {
        start: {
          line: 220,
          column: 4
        },
        end: {
          line: 220,
          column: 53
        }
      },
      "158": {
        start: {
          line: 221,
          column: 4
        },
        end: {
          line: 221,
          column: 63
        }
      },
      "159": {
        start: {
          line: 222,
          column: 4
        },
        end: {
          line: 222,
          column: 53
        }
      },
      "160": {
        start: {
          line: 223,
          column: 4
        },
        end: {
          line: 223,
          column: 71
        }
      },
      "161": {
        start: {
          line: 226,
          column: 0
        },
        end: {
          line: 231,
          column: 57
        }
      },
      "162": {
        start: {
          line: 227,
          column: 4
        },
        end: {
          line: 227,
          column: 34
        }
      },
      "163": {
        start: {
          line: 228,
          column: 4
        },
        end: {
          line: 228,
          column: 36
        }
      },
      "164": {
        start: {
          line: 229,
          column: 4
        },
        end: {
          line: 229,
          column: 34
        }
      },
      "165": {
        start: {
          line: 230,
          column: 4
        },
        end: {
          line: 230,
          column: 46
        }
      },
      "166": {
        start: {
          line: 233,
          column: 0
        },
        end: {
          line: 242,
          column: 63
        }
      },
      "167": {
        start: {
          line: 234,
          column: 4
        },
        end: {
          line: 234,
          column: 38
        }
      },
      "168": {
        start: {
          line: 235,
          column: 4
        },
        end: {
          line: 235,
          column: 38
        }
      },
      "169": {
        start: {
          line: 236,
          column: 4
        },
        end: {
          line: 236,
          column: 46
        }
      },
      "170": {
        start: {
          line: 237,
          column: 4
        },
        end: {
          line: 237,
          column: 44
        }
      },
      "171": {
        start: {
          line: 238,
          column: 4
        },
        end: {
          line: 238,
          column: 46
        }
      },
      "172": {
        start: {
          line: 239,
          column: 4
        },
        end: {
          line: 239,
          column: 48
        }
      },
      "173": {
        start: {
          line: 240,
          column: 4
        },
        end: {
          line: 240,
          column: 48
        }
      },
      "174": {
        start: {
          line: 241,
          column: 4
        },
        end: {
          line: 241,
          column: 36
        }
      },
      "175": {
        start: {
          line: 244,
          column: 0
        },
        end: {
          line: 249,
          column: 108
        }
      },
      "176": {
        start: {
          line: 245,
          column: 4
        },
        end: {
          line: 245,
          column: 57
        }
      },
      "177": {
        start: {
          line: 246,
          column: 4
        },
        end: {
          line: 246,
          column: 61
        }
      },
      "178": {
        start: {
          line: 247,
          column: 4
        },
        end: {
          line: 247,
          column: 59
        }
      },
      "179": {
        start: {
          line: 248,
          column: 4
        },
        end: {
          line: 248,
          column: 77
        }
      },
      "180": {
        start: {
          line: 251,
          column: 0
        },
        end: {
          line: 259,
          column: 66
        }
      },
      "181": {
        start: {
          line: 252,
          column: 4
        },
        end: {
          line: 252,
          column: 33
        }
      },
      "182": {
        start: {
          line: 253,
          column: 4
        },
        end: {
          line: 253,
          column: 37
        }
      },
      "183": {
        start: {
          line: 254,
          column: 4
        },
        end: {
          line: 254,
          column: 33
        }
      },
      "184": {
        start: {
          line: 255,
          column: 4
        },
        end: {
          line: 255,
          column: 39
        }
      },
      "185": {
        start: {
          line: 256,
          column: 4
        },
        end: {
          line: 256,
          column: 39
        }
      },
      "186": {
        start: {
          line: 257,
          column: 4
        },
        end: {
          line: 257,
          column: 35
        }
      },
      "187": {
        start: {
          line: 258,
          column: 4
        },
        end: {
          line: 258,
          column: 43
        }
      },
      "188": {
        start: {
          line: 261,
          column: 0
        },
        end: {
          line: 267,
          column: 78
        }
      },
      "189": {
        start: {
          line: 262,
          column: 4
        },
        end: {
          line: 262,
          column: 49
        }
      },
      "190": {
        start: {
          line: 263,
          column: 4
        },
        end: {
          line: 263,
          column: 39
        }
      },
      "191": {
        start: {
          line: 264,
          column: 4
        },
        end: {
          line: 264,
          column: 39
        }
      },
      "192": {
        start: {
          line: 265,
          column: 4
        },
        end: {
          line: 265,
          column: 39
        }
      },
      "193": {
        start: {
          line: 266,
          column: 4
        },
        end: {
          line: 266,
          column: 47
        }
      },
      "194": {
        start: {
          line: 269,
          column: 0
        },
        end: {
          line: 275,
          column: 54
        }
      },
      "195": {
        start: {
          line: 270,
          column: 4
        },
        end: {
          line: 270,
          column: 57
        }
      },
      "196": {
        start: {
          line: 271,
          column: 4
        },
        end: {
          line: 271,
          column: 65
        }
      },
      "197": {
        start: {
          line: 272,
          column: 4
        },
        end: {
          line: 272,
          column: 45
        }
      },
      "198": {
        start: {
          line: 273,
          column: 4
        },
        end: {
          line: 273,
          column: 55
        }
      },
      "199": {
        start: {
          line: 274,
          column: 4
        },
        end: {
          line: 274,
          column: 49
        }
      },
      "200": {
        start: {
          line: 277,
          column: 0
        },
        end: {
          line: 283,
          column: 60
        }
      },
      "201": {
        start: {
          line: 278,
          column: 4
        },
        end: {
          line: 278,
          column: 51
        }
      },
      "202": {
        start: {
          line: 279,
          column: 4
        },
        end: {
          line: 279,
          column: 51
        }
      },
      "203": {
        start: {
          line: 280,
          column: 4
        },
        end: {
          line: 280,
          column: 53
        }
      },
      "204": {
        start: {
          line: 281,
          column: 4
        },
        end: {
          line: 281,
          column: 39
        }
      },
      "205": {
        start: {
          line: 282,
          column: 4
        },
        end: {
          line: 282,
          column: 51
        }
      },
      "206": {
        start: {
          line: 285,
          column: 0
        },
        end: {
          line: 290,
          column: 72
        }
      },
      "207": {
        start: {
          line: 286,
          column: 4
        },
        end: {
          line: 286,
          column: 45
        }
      },
      "208": {
        start: {
          line: 287,
          column: 4
        },
        end: {
          line: 287,
          column: 37
        }
      },
      "209": {
        start: {
          line: 288,
          column: 4
        },
        end: {
          line: 288,
          column: 41
        }
      },
      "210": {
        start: {
          line: 289,
          column: 4
        },
        end: {
          line: 289,
          column: 35
        }
      },
      "211": {
        start: {
          line: 292,
          column: 0
        },
        end: {
          line: 299,
          column: 66
        }
      },
      "212": {
        start: {
          line: 293,
          column: 4
        },
        end: {
          line: 293,
          column: 61
        }
      },
      "213": {
        start: {
          line: 294,
          column: 4
        },
        end: {
          line: 294,
          column: 61
        }
      },
      "214": {
        start: {
          line: 295,
          column: 4
        },
        end: {
          line: 295,
          column: 63
        }
      },
      "215": {
        start: {
          line: 296,
          column: 4
        },
        end: {
          line: 296,
          column: 67
        }
      },
      "216": {
        start: {
          line: 297,
          column: 4
        },
        end: {
          line: 297,
          column: 55
        }
      },
      "217": {
        start: {
          line: 298,
          column: 4
        },
        end: {
          line: 298,
          column: 41
        }
      },
      "218": {
        start: {
          line: 301,
          column: 0
        },
        end: {
          line: 301,
          column: 60
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 12,
            column: 74
          },
          end: {
            line: 12,
            column: 75
          }
        },
        loc: {
          start: {
            line: 12,
            column: 96
          },
          end: {
            line: 19,
            column: 1
          }
        },
        line: 12
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 16,
            column: 38
          },
          end: {
            line: 16,
            column: 39
          }
        },
        loc: {
          start: {
            line: 16,
            column: 49
          },
          end: {
            line: 16,
            column: 65
          }
        },
        line: 16
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 19,
            column: 6
          },
          end: {
            line: 19,
            column: 7
          }
        },
        loc: {
          start: {
            line: 19,
            column: 28
          },
          end: {
            line: 22,
            column: 1
          }
        },
        line: 19
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 23,
            column: 50
          },
          end: {
            line: 23,
            column: 51
          }
        },
        loc: {
          start: {
            line: 23,
            column: 71
          },
          end: {
            line: 25,
            column: 1
          }
        },
        line: 23
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 32,
            column: 1
          },
          end: {
            line: 32,
            column: 2
          }
        },
        loc: {
          start: {
            line: 32,
            column: 25
          },
          end: {
            line: 40,
            column: 1
          }
        },
        line: 32
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 42,
            column: 1
          },
          end: {
            line: 42,
            column: 2
          }
        },
        loc: {
          start: {
            line: 42,
            column: 24
          },
          end: {
            line: 49,
            column: 1
          }
        },
        line: 42
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 51,
            column: 1
          },
          end: {
            line: 51,
            column: 2
          }
        },
        loc: {
          start: {
            line: 51,
            column: 26
          },
          end: {
            line: 56,
            column: 1
          }
        },
        line: 51
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 58,
            column: 1
          },
          end: {
            line: 58,
            column: 2
          }
        },
        loc: {
          start: {
            line: 58,
            column: 30
          },
          end: {
            line: 64,
            column: 1
          }
        },
        line: 58
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 66,
            column: 1
          },
          end: {
            line: 66,
            column: 2
          }
        },
        loc: {
          start: {
            line: 66,
            column: 29
          },
          end: {
            line: 71,
            column: 1
          }
        },
        line: 66
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 73,
            column: 1
          },
          end: {
            line: 73,
            column: 2
          }
        },
        loc: {
          start: {
            line: 73,
            column: 25
          },
          end: {
            line: 79,
            column: 1
          }
        },
        line: 73
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 81,
            column: 1
          },
          end: {
            line: 81,
            column: 2
          }
        },
        loc: {
          start: {
            line: 81,
            column: 27
          },
          end: {
            line: 87,
            column: 1
          }
        },
        line: 81
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 89,
            column: 1
          },
          end: {
            line: 89,
            column: 2
          }
        },
        loc: {
          start: {
            line: 89,
            column: 19
          },
          end: {
            line: 94,
            column: 1
          }
        },
        line: 89
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 96,
            column: 1
          },
          end: {
            line: 96,
            column: 2
          }
        },
        loc: {
          start: {
            line: 96,
            column: 22
          },
          end: {
            line: 103,
            column: 1
          }
        },
        line: 96
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 105,
            column: 1
          },
          end: {
            line: 105,
            column: 2
          }
        },
        loc: {
          start: {
            line: 105,
            column: 26
          },
          end: {
            line: 111,
            column: 1
          }
        },
        line: 105
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 113,
            column: 1
          },
          end: {
            line: 113,
            column: 2
          }
        },
        loc: {
          start: {
            line: 113,
            column: 24
          },
          end: {
            line: 119,
            column: 1
          }
        },
        line: 113
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 121,
            column: 1
          },
          end: {
            line: 121,
            column: 2
          }
        },
        loc: {
          start: {
            line: 121,
            column: 22
          },
          end: {
            line: 127,
            column: 1
          }
        },
        line: 121
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 129,
            column: 1
          },
          end: {
            line: 129,
            column: 2
          }
        },
        loc: {
          start: {
            line: 129,
            column: 31
          },
          end: {
            line: 134,
            column: 1
          }
        },
        line: 129
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 136,
            column: 1
          },
          end: {
            line: 136,
            column: 2
          }
        },
        loc: {
          start: {
            line: 136,
            column: 29
          },
          end: {
            line: 140,
            column: 1
          }
        },
        line: 136
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 142,
            column: 1
          },
          end: {
            line: 142,
            column: 2
          }
        },
        loc: {
          start: {
            line: 142,
            column: 26
          },
          end: {
            line: 146,
            column: 1
          }
        },
        line: 142
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 148,
            column: 1
          },
          end: {
            line: 148,
            column: 2
          }
        },
        loc: {
          start: {
            line: 148,
            column: 26
          },
          end: {
            line: 152,
            column: 1
          }
        },
        line: 148
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 154,
            column: 1
          },
          end: {
            line: 154,
            column: 2
          }
        },
        loc: {
          start: {
            line: 154,
            column: 25
          },
          end: {
            line: 161,
            column: 1
          }
        },
        line: 154
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 163,
            column: 1
          },
          end: {
            line: 163,
            column: 2
          }
        },
        loc: {
          start: {
            line: 163,
            column: 29
          },
          end: {
            line: 169,
            column: 1
          }
        },
        line: 163
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 171,
            column: 1
          },
          end: {
            line: 171,
            column: 2
          }
        },
        loc: {
          start: {
            line: 171,
            column: 22
          },
          end: {
            line: 179,
            column: 1
          }
        },
        line: 171
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 181,
            column: 1
          },
          end: {
            line: 181,
            column: 2
          }
        },
        loc: {
          start: {
            line: 181,
            column: 29
          },
          end: {
            line: 186,
            column: 1
          }
        },
        line: 181
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 188,
            column: 1
          },
          end: {
            line: 188,
            column: 2
          }
        },
        loc: {
          start: {
            line: 188,
            column: 23
          },
          end: {
            line: 194,
            column: 1
          }
        },
        line: 188
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 196,
            column: 1
          },
          end: {
            line: 196,
            column: 2
          }
        },
        loc: {
          start: {
            line: 196,
            column: 31
          },
          end: {
            line: 202,
            column: 1
          }
        },
        line: 196
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 204,
            column: 1
          },
          end: {
            line: 204,
            column: 2
          }
        },
        loc: {
          start: {
            line: 204,
            column: 35
          },
          end: {
            line: 209,
            column: 1
          }
        },
        line: 204
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 211,
            column: 1
          },
          end: {
            line: 211,
            column: 2
          }
        },
        loc: {
          start: {
            line: 211,
            column: 37
          },
          end: {
            line: 216,
            column: 1
          }
        },
        line: 211
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 218,
            column: 1
          },
          end: {
            line: 218,
            column: 2
          }
        },
        loc: {
          start: {
            line: 218,
            column: 26
          },
          end: {
            line: 224,
            column: 1
          }
        },
        line: 218
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 226,
            column: 1
          },
          end: {
            line: 226,
            column: 2
          }
        },
        loc: {
          start: {
            line: 226,
            column: 23
          },
          end: {
            line: 231,
            column: 1
          }
        },
        line: 226
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 233,
            column: 1
          },
          end: {
            line: 233,
            column: 2
          }
        },
        loc: {
          start: {
            line: 233,
            column: 25
          },
          end: {
            line: 242,
            column: 1
          }
        },
        line: 233
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 244,
            column: 1
          },
          end: {
            line: 244,
            column: 2
          }
        },
        loc: {
          start: {
            line: 244,
            column: 40
          },
          end: {
            line: 249,
            column: 1
          }
        },
        line: 244
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 251,
            column: 1
          },
          end: {
            line: 251,
            column: 2
          }
        },
        loc: {
          start: {
            line: 251,
            column: 26
          },
          end: {
            line: 259,
            column: 1
          }
        },
        line: 251
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 261,
            column: 1
          },
          end: {
            line: 261,
            column: 2
          }
        },
        loc: {
          start: {
            line: 261,
            column: 30
          },
          end: {
            line: 267,
            column: 1
          }
        },
        line: 261
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 269,
            column: 1
          },
          end: {
            line: 269,
            column: 2
          }
        },
        loc: {
          start: {
            line: 269,
            column: 22
          },
          end: {
            line: 275,
            column: 1
          }
        },
        line: 269
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 277,
            column: 1
          },
          end: {
            line: 277,
            column: 2
          }
        },
        loc: {
          start: {
            line: 277,
            column: 24
          },
          end: {
            line: 283,
            column: 1
          }
        },
        line: 277
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 285,
            column: 1
          },
          end: {
            line: 285,
            column: 2
          }
        },
        loc: {
          start: {
            line: 285,
            column: 28
          },
          end: {
            line: 290,
            column: 1
          }
        },
        line: 285
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 292,
            column: 1
          },
          end: {
            line: 292,
            column: 2
          }
        },
        loc: {
          start: {
            line: 292,
            column: 26
          },
          end: {
            line: 299,
            column: 1
          }
        },
        line: 292
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 12,
            column: 22
          },
          end: {
            line: 22,
            column: 3
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 12,
            column: 23
          },
          end: {
            line: 12,
            column: 27
          }
        }, {
          start: {
            line: 12,
            column: 31
          },
          end: {
            line: 12,
            column: 51
          }
        }, {
          start: {
            line: 12,
            column: 57
          },
          end: {
            line: 22,
            column: 2
          }
        }],
        line: 12
      },
      "1": {
        loc: {
          start: {
            line: 12,
            column: 57
          },
          end: {
            line: 22,
            column: 2
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 12,
            column: 74
          },
          end: {
            line: 19,
            column: 1
          }
        }, {
          start: {
            line: 19,
            column: 6
          },
          end: {
            line: 22,
            column: 1
          }
        }],
        line: 12
      },
      "2": {
        loc: {
          start: {
            line: 13,
            column: 4
          },
          end: {
            line: 13,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 13,
            column: 4
          },
          end: {
            line: 13,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 13
      },
      "3": {
        loc: {
          start: {
            line: 15,
            column: 4
          },
          end: {
            line: 17,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 15,
            column: 4
          },
          end: {
            line: 17,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 15
      },
      "4": {
        loc: {
          start: {
            line: 15,
            column: 8
          },
          end: {
            line: 15,
            column: 85
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 15,
            column: 8
          },
          end: {
            line: 15,
            column: 13
          }
        }, {
          start: {
            line: 15,
            column: 18
          },
          end: {
            line: 15,
            column: 84
          }
        }],
        line: 15
      },
      "5": {
        loc: {
          start: {
            line: 15,
            column: 18
          },
          end: {
            line: 15,
            column: 84
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 15,
            column: 34
          },
          end: {
            line: 15,
            column: 47
          }
        }, {
          start: {
            line: 15,
            column: 50
          },
          end: {
            line: 15,
            column: 84
          }
        }],
        line: 15
      },
      "6": {
        loc: {
          start: {
            line: 15,
            column: 50
          },
          end: {
            line: 15,
            column: 84
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 15,
            column: 50
          },
          end: {
            line: 15,
            column: 63
          }
        }, {
          start: {
            line: 15,
            column: 67
          },
          end: {
            line: 15,
            column: 84
          }
        }],
        line: 15
      },
      "7": {
        loc: {
          start: {
            line: 20,
            column: 4
          },
          end: {
            line: 20,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 20,
            column: 4
          },
          end: {
            line: 20,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 20
      },
      "8": {
        loc: {
          start: {
            line: 23,
            column: 19
          },
          end: {
            line: 25,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 23,
            column: 20
          },
          end: {
            line: 23,
            column: 24
          }
        }, {
          start: {
            line: 23,
            column: 28
          },
          end: {
            line: 23,
            column: 45
          }
        }, {
          start: {
            line: 23,
            column: 50
          },
          end: {
            line: 25,
            column: 1
          }
        }],
        line: 23
      },
      "9": {
        loc: {
          start: {
            line: 24,
            column: 21
          },
          end: {
            line: 24,
            column: 126
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 24,
            column: 21
          },
          end: {
            line: 24,
            column: 126
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 24
      },
      "10": {
        loc: {
          start: {
            line: 24,
            column: 25
          },
          end: {
            line: 24,
            column: 93
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 24,
            column: 25
          },
          end: {
            line: 24,
            column: 40
          }
        }, {
          start: {
            line: 24,
            column: 44
          },
          end: {
            line: 24,
            column: 93
          }
        }],
        line: 24
      },
      "11": {
        loc: {
          start: {
            line: 40,
            column: 3
          },
          end: {
            line: 40,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 40,
            column: 3
          },
          end: {
            line: 40,
            column: 15
          }
        }, {
          start: {
            line: 40,
            column: 20
          },
          end: {
            line: 40,
            column: 60
          }
        }],
        line: 40
      },
      "12": {
        loc: {
          start: {
            line: 49,
            column: 3
          },
          end: {
            line: 49,
            column: 58
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 49,
            column: 3
          },
          end: {
            line: 49,
            column: 14
          }
        }, {
          start: {
            line: 49,
            column: 19
          },
          end: {
            line: 49,
            column: 57
          }
        }],
        line: 49
      },
      "13": {
        loc: {
          start: {
            line: 56,
            column: 3
          },
          end: {
            line: 56,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 56,
            column: 3
          },
          end: {
            line: 56,
            column: 16
          }
        }, {
          start: {
            line: 56,
            column: 21
          },
          end: {
            line: 56,
            column: 63
          }
        }],
        line: 56
      },
      "14": {
        loc: {
          start: {
            line: 64,
            column: 3
          },
          end: {
            line: 64,
            column: 76
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 64,
            column: 3
          },
          end: {
            line: 64,
            column: 20
          }
        }, {
          start: {
            line: 64,
            column: 25
          },
          end: {
            line: 64,
            column: 75
          }
        }],
        line: 64
      },
      "15": {
        loc: {
          start: {
            line: 71,
            column: 3
          },
          end: {
            line: 71,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 71,
            column: 3
          },
          end: {
            line: 71,
            column: 19
          }
        }, {
          start: {
            line: 71,
            column: 24
          },
          end: {
            line: 71,
            column: 72
          }
        }],
        line: 71
      },
      "16": {
        loc: {
          start: {
            line: 79,
            column: 3
          },
          end: {
            line: 79,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 79,
            column: 3
          },
          end: {
            line: 79,
            column: 15
          }
        }, {
          start: {
            line: 79,
            column: 20
          },
          end: {
            line: 79,
            column: 60
          }
        }],
        line: 79
      },
      "17": {
        loc: {
          start: {
            line: 87,
            column: 3
          },
          end: {
            line: 87,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 87,
            column: 3
          },
          end: {
            line: 87,
            column: 17
          }
        }, {
          start: {
            line: 87,
            column: 22
          },
          end: {
            line: 87,
            column: 66
          }
        }],
        line: 87
      },
      "18": {
        loc: {
          start: {
            line: 94,
            column: 3
          },
          end: {
            line: 94,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 94,
            column: 3
          },
          end: {
            line: 94,
            column: 9
          }
        }, {
          start: {
            line: 94,
            column: 14
          },
          end: {
            line: 94,
            column: 42
          }
        }],
        line: 94
      },
      "19": {
        loc: {
          start: {
            line: 103,
            column: 3
          },
          end: {
            line: 103,
            column: 52
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 103,
            column: 3
          },
          end: {
            line: 103,
            column: 12
          }
        }, {
          start: {
            line: 103,
            column: 17
          },
          end: {
            line: 103,
            column: 51
          }
        }],
        line: 103
      },
      "20": {
        loc: {
          start: {
            line: 111,
            column: 3
          },
          end: {
            line: 111,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 111,
            column: 3
          },
          end: {
            line: 111,
            column: 16
          }
        }, {
          start: {
            line: 111,
            column: 21
          },
          end: {
            line: 111,
            column: 63
          }
        }],
        line: 111
      },
      "21": {
        loc: {
          start: {
            line: 119,
            column: 3
          },
          end: {
            line: 119,
            column: 58
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 119,
            column: 3
          },
          end: {
            line: 119,
            column: 14
          }
        }, {
          start: {
            line: 119,
            column: 19
          },
          end: {
            line: 119,
            column: 57
          }
        }],
        line: 119
      },
      "22": {
        loc: {
          start: {
            line: 127,
            column: 3
          },
          end: {
            line: 127,
            column: 52
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 127,
            column: 3
          },
          end: {
            line: 127,
            column: 12
          }
        }, {
          start: {
            line: 127,
            column: 17
          },
          end: {
            line: 127,
            column: 51
          }
        }],
        line: 127
      },
      "23": {
        loc: {
          start: {
            line: 134,
            column: 3
          },
          end: {
            line: 134,
            column: 79
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 134,
            column: 3
          },
          end: {
            line: 134,
            column: 21
          }
        }, {
          start: {
            line: 134,
            column: 26
          },
          end: {
            line: 134,
            column: 78
          }
        }],
        line: 134
      },
      "24": {
        loc: {
          start: {
            line: 140,
            column: 3
          },
          end: {
            line: 140,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 140,
            column: 3
          },
          end: {
            line: 140,
            column: 19
          }
        }, {
          start: {
            line: 140,
            column: 24
          },
          end: {
            line: 140,
            column: 72
          }
        }],
        line: 140
      },
      "25": {
        loc: {
          start: {
            line: 146,
            column: 3
          },
          end: {
            line: 146,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 146,
            column: 3
          },
          end: {
            line: 146,
            column: 16
          }
        }, {
          start: {
            line: 146,
            column: 21
          },
          end: {
            line: 146,
            column: 63
          }
        }],
        line: 146
      },
      "26": {
        loc: {
          start: {
            line: 152,
            column: 3
          },
          end: {
            line: 152,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 152,
            column: 3
          },
          end: {
            line: 152,
            column: 16
          }
        }, {
          start: {
            line: 152,
            column: 21
          },
          end: {
            line: 152,
            column: 63
          }
        }],
        line: 152
      },
      "27": {
        loc: {
          start: {
            line: 161,
            column: 3
          },
          end: {
            line: 161,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 161,
            column: 3
          },
          end: {
            line: 161,
            column: 15
          }
        }, {
          start: {
            line: 161,
            column: 20
          },
          end: {
            line: 161,
            column: 60
          }
        }],
        line: 161
      },
      "28": {
        loc: {
          start: {
            line: 169,
            column: 3
          },
          end: {
            line: 169,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 169,
            column: 3
          },
          end: {
            line: 169,
            column: 19
          }
        }, {
          start: {
            line: 169,
            column: 24
          },
          end: {
            line: 169,
            column: 72
          }
        }],
        line: 169
      },
      "29": {
        loc: {
          start: {
            line: 179,
            column: 3
          },
          end: {
            line: 179,
            column: 52
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 179,
            column: 3
          },
          end: {
            line: 179,
            column: 12
          }
        }, {
          start: {
            line: 179,
            column: 17
          },
          end: {
            line: 179,
            column: 51
          }
        }],
        line: 179
      },
      "30": {
        loc: {
          start: {
            line: 186,
            column: 3
          },
          end: {
            line: 186,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 186,
            column: 3
          },
          end: {
            line: 186,
            column: 19
          }
        }, {
          start: {
            line: 186,
            column: 24
          },
          end: {
            line: 186,
            column: 72
          }
        }],
        line: 186
      },
      "31": {
        loc: {
          start: {
            line: 194,
            column: 3
          },
          end: {
            line: 194,
            column: 55
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 194,
            column: 3
          },
          end: {
            line: 194,
            column: 13
          }
        }, {
          start: {
            line: 194,
            column: 18
          },
          end: {
            line: 194,
            column: 54
          }
        }],
        line: 194
      },
      "32": {
        loc: {
          start: {
            line: 202,
            column: 3
          },
          end: {
            line: 202,
            column: 79
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 202,
            column: 3
          },
          end: {
            line: 202,
            column: 21
          }
        }, {
          start: {
            line: 202,
            column: 26
          },
          end: {
            line: 202,
            column: 78
          }
        }],
        line: 202
      },
      "33": {
        loc: {
          start: {
            line: 209,
            column: 3
          },
          end: {
            line: 209,
            column: 91
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 209,
            column: 3
          },
          end: {
            line: 209,
            column: 25
          }
        }, {
          start: {
            line: 209,
            column: 30
          },
          end: {
            line: 209,
            column: 90
          }
        }],
        line: 209
      },
      "34": {
        loc: {
          start: {
            line: 216,
            column: 3
          },
          end: {
            line: 216,
            column: 97
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 216,
            column: 3
          },
          end: {
            line: 216,
            column: 27
          }
        }, {
          start: {
            line: 216,
            column: 32
          },
          end: {
            line: 216,
            column: 96
          }
        }],
        line: 216
      },
      "35": {
        loc: {
          start: {
            line: 224,
            column: 3
          },
          end: {
            line: 224,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 224,
            column: 3
          },
          end: {
            line: 224,
            column: 16
          }
        }, {
          start: {
            line: 224,
            column: 21
          },
          end: {
            line: 224,
            column: 63
          }
        }],
        line: 224
      },
      "36": {
        loc: {
          start: {
            line: 231,
            column: 3
          },
          end: {
            line: 231,
            column: 55
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 231,
            column: 3
          },
          end: {
            line: 231,
            column: 13
          }
        }, {
          start: {
            line: 231,
            column: 18
          },
          end: {
            line: 231,
            column: 54
          }
        }],
        line: 231
      },
      "37": {
        loc: {
          start: {
            line: 242,
            column: 3
          },
          end: {
            line: 242,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 242,
            column: 3
          },
          end: {
            line: 242,
            column: 15
          }
        }, {
          start: {
            line: 242,
            column: 20
          },
          end: {
            line: 242,
            column: 60
          }
        }],
        line: 242
      },
      "38": {
        loc: {
          start: {
            line: 249,
            column: 3
          },
          end: {
            line: 249,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 249,
            column: 3
          },
          end: {
            line: 249,
            column: 30
          }
        }, {
          start: {
            line: 249,
            column: 35
          },
          end: {
            line: 249,
            column: 105
          }
        }],
        line: 249
      },
      "39": {
        loc: {
          start: {
            line: 259,
            column: 3
          },
          end: {
            line: 259,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 259,
            column: 3
          },
          end: {
            line: 259,
            column: 16
          }
        }, {
          start: {
            line: 259,
            column: 21
          },
          end: {
            line: 259,
            column: 63
          }
        }],
        line: 259
      },
      "40": {
        loc: {
          start: {
            line: 267,
            column: 3
          },
          end: {
            line: 267,
            column: 76
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 267,
            column: 3
          },
          end: {
            line: 267,
            column: 20
          }
        }, {
          start: {
            line: 267,
            column: 25
          },
          end: {
            line: 267,
            column: 75
          }
        }],
        line: 267
      },
      "41": {
        loc: {
          start: {
            line: 275,
            column: 3
          },
          end: {
            line: 275,
            column: 52
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 275,
            column: 3
          },
          end: {
            line: 275,
            column: 12
          }
        }, {
          start: {
            line: 275,
            column: 17
          },
          end: {
            line: 275,
            column: 51
          }
        }],
        line: 275
      },
      "42": {
        loc: {
          start: {
            line: 283,
            column: 3
          },
          end: {
            line: 283,
            column: 58
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 283,
            column: 3
          },
          end: {
            line: 283,
            column: 14
          }
        }, {
          start: {
            line: 283,
            column: 19
          },
          end: {
            line: 283,
            column: 57
          }
        }],
        line: 283
      },
      "43": {
        loc: {
          start: {
            line: 290,
            column: 3
          },
          end: {
            line: 290,
            column: 70
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 290,
            column: 3
          },
          end: {
            line: 290,
            column: 18
          }
        }, {
          start: {
            line: 290,
            column: 23
          },
          end: {
            line: 290,
            column: 69
          }
        }],
        line: 290
      },
      "44": {
        loc: {
          start: {
            line: 299,
            column: 3
          },
          end: {
            line: 299,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 299,
            column: 3
          },
          end: {
            line: 299,
            column: 16
          }
        }, {
          start: {
            line: 299,
            column: 21
          },
          end: {
            line: 299,
            column: 63
          }
        }],
        line: 299
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0,
      "210": 0,
      "211": 0,
      "212": 0,
      "213": 0,
      "214": 0,
      "215": 0,
      "216": 0,
      "217": 0,
      "218": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\types\\SystemAnalysisTypes.ts",
      mappings: ";AAAA;;;;;;;;;GASG;;;;;;;;;;;;;;;;;AAyBH;;GAEG;AACH,IAAY,YAQX;AARD,WAAY,YAAY;IACtB,qDAAqC,CAAA;IACrC,2CAA2B,CAAA;IAC3B,uCAAuB,CAAA;IACvB,yDAAyC,CAAA;IACzC,mDAAmC,CAAA;IACnC,+CAA+B,CAAA;IAC/B,iCAAiB,CAAA;AACnB,CAAC,EARW,YAAY,4BAAZ,YAAY,QAQvB;AAgBD,IAAY,WAOX;AAPD,WAAY,WAAW;IACrB,8CAA+B,CAAA;IAC/B,8BAAe,CAAA;IACf,kCAAmB,CAAA;IACnB,gCAAiB,CAAA;IACjB,sCAAuB,CAAA;IACvB,gCAAiB,CAAA;AACnB,CAAC,EAPW,WAAW,2BAAX,WAAW,QAOtB;AAED,IAAY,aAKX;AALD,WAAY,aAAa;IACvB,gCAAe,CAAA;IACf,sCAAqB,CAAA;IACrB,gDAA+B,CAAA;IAC/B,kDAAiC,CAAA;AACnC,CAAC,EALW,aAAa,6BAAb,aAAa,QAKxB;AAyED,IAAY,iBAMX;AAND,WAAY,iBAAiB;IAC3B,8CAAyB,CAAA;IACzB,0CAAqB,CAAA;IACrB,4CAAuB,CAAA;IACvB,4DAAuC,CAAA;IACvC,8CAAyB,CAAA;AAC3B,CAAC,EANW,iBAAiB,iCAAjB,iBAAiB,QAM5B;AAED,IAAY,gBAKX;AALD,WAAY,gBAAgB;IAC1B,iCAAa,CAAA;IACb,qCAAiB,CAAA;IACjB,+BAAW,CAAA;IACX,2CAAuB,CAAA;AACzB,CAAC,EALW,gBAAgB,gCAAhB,gBAAgB,QAK3B;AAmCD,IAAY,YAMX;AAND,WAAY,YAAY;IACtB,uCAAuB,CAAA;IACvB,6BAAa,CAAA;IACb,yCAAyB,CAAA;IACzB,6BAAa,CAAA;IACb,qCAAqB,CAAA;AACvB,CAAC,EANW,YAAY,4BAAZ,YAAY,QAMvB;AAeD,IAAY,cAMX;AAND,WAAY,cAAc;IACxB,yCAAuB,CAAA;IACvB,mCAAiB,CAAA;IACjB,yCAAuB,CAAA;IACvB,6CAA2B,CAAA;IAC3B,qCAAmB,CAAA;AACrB,CAAC,EANW,cAAc,8BAAd,cAAc,QAMzB;AASD,IAAY,MAKX;AALD,WAAY,MAAM;IAChB,2BAAiB,CAAA;IACjB,2BAAiB,CAAA;IACjB,uBAAa,CAAA;IACb,2BAAiB,CAAA;AACnB,CAAC,EALW,MAAM,sBAAN,MAAM,QAKjB;AAkBD,IAAY,SAOX;AAPD,WAAY,SAAS;IACnB,sDAAyC,CAAA;IACzC,oDAAuC,CAAA;IACvC,kDAAqC,CAAA;IACrC,gDAAmC,CAAA;IACnC,4CAA+B,CAAA;IAC/B,gDAAmC,CAAA;AACrC,CAAC,EAPW,SAAS,yBAAT,SAAS,QAOpB;AAED,IAAY,aAMX;AAND,WAAY,aAAa;IACvB,sCAAqB,CAAA;IACrB,8BAAa,CAAA;IACb,kCAAiB,CAAA;IACjB,4BAAW,CAAA;IACX,8BAAa,CAAA;AACf,CAAC,EANW,aAAa,6BAAb,aAAa,QAMxB;AA4CD,IAAY,WAMX;AAND,WAAY,WAAW;IACrB,0BAAW,CAAA;IACX,0BAAW,CAAA;IACX,gCAAiB,CAAA;IACjB,gCAAiB,CAAA;IACjB,wBAAS,CAAA;AACX,CAAC,EANW,WAAW,2BAAX,WAAW,QAMtB;AAED,IAAY,SAMX;AAND,WAAY,SAAS;IACnB,8BAAiB,CAAA;IACjB,4BAAe,CAAA;IACf,8BAAiB,CAAA;IACjB,gCAAmB,CAAA;IACnB,kCAAqB,CAAA;AACvB,CAAC,EANW,SAAS,yBAAT,SAAS,QAMpB;AAyED,IAAY,kBAKX;AALD,WAAY,kBAAkB;IAC5B,6DAAuC,CAAA;IACvC,yDAAmC,CAAA;IACnC,uDAAiC,CAAA;IACjC,yEAAmD,CAAA;AACrD,CAAC,EALW,kBAAkB,kCAAlB,kBAAkB,QAK7B;AAED,IAAY,gBAIX;AAJD,WAAY,gBAAgB;IAC1B,+BAAW,CAAA;IACX,qCAAiB,CAAA;IACjB,iCAAa,CAAA;AACf,CAAC,EAJW,gBAAgB,gCAAhB,gBAAgB,QAI3B;AAiED,IAAY,aAIX;AAJD,WAAY,aAAa;IACvB,oCAAmB,CAAA;IACnB,wCAAuB,CAAA;IACvB,sCAAqB,CAAA;AACvB,CAAC,EAJW,aAAa,6BAAb,aAAa,QAIxB;AAED,IAAY,aAIX;AAJD,WAAY,aAAa;IACvB,oCAAmB,CAAA;IACnB,oCAAmB,CAAA;IACnB,oCAAmB,CAAA,CAAE,2BAA2B;AAClD,CAAC,EAJW,aAAa,6BAAb,aAAa,QAIxB;AAkCD,IAAY,YAOX;AAPD,WAAY,YAAY;IACtB,iCAAiB,CAAA;IACjB,iCAAiB,CAAA;IACjB,yCAAyB,CAAA;IACzB,yDAAyC,CAAA;IACzC,uDAAuC,CAAA;IACvC,iDAAiC,CAAA;AACnC,CAAC,EAPW,YAAY,4BAAZ,YAAY,QAOvB;AAaD,IAAY,gBAMX;AAND,WAAY,gBAAgB;IAC1B,2CAAuB,CAAA;IACvB,mDAA+B,CAAA;IAC/B,yCAAqB,CAAA;IACrB,qDAAiC,CAAA;IACjC,uDAAmC,CAAA;AACrC,CAAC,EANW,gBAAgB,gCAAhB,gBAAgB,QAM3B;AAaD,IAAY,SAQX;AARD,WAAY,SAAS;IACnB,gCAAmB,CAAA;IACnB,gCAAmB,CAAA;IACnB,4BAAe,CAAA;IACf,0BAAa,CAAA;IACb,4BAAe,CAAA;IACf,8BAAiB,CAAA;IACjB,4BAAe,CAAA;AACjB,CAAC,EARW,SAAS,yBAAT,SAAS,QAQpB;AASD,IAAY,gBAKX;AALD,WAAY,gBAAgB;IAC1B,qCAAiB,CAAA;IACjB,uCAAmB,CAAA;IACnB,6CAAyB,CAAA;IACzB,2CAAuB,CAAA;AACzB,CAAC,EALW,gBAAgB,gCAAhB,gBAAgB,QAK3B;AAsBD,IAAY,UAMX;AAND,WAAY,UAAU;IACpB,uCAAyB,CAAA;IACzB,uCAAyB,CAAA;IACzB,yCAA2B,CAAA;IAC3B,qCAAuB,CAAA;IACvB,6DAA+C,CAAA;AACjD,CAAC,EANW,UAAU,0BAAV,UAAU,QAMrB;AAsCD,IAAY,kBAMX;AAND,WAAY,kBAAkB;IAC5B,mDAA6B,CAAA;IAC7B,iDAA2B,CAAA;IAC3B,yCAAmB,CAAA;IACnB,+DAAyC,CAAA;IACzC,iEAA2C,CAAA;AAC7C,CAAC,EANW,kBAAkB,kCAAlB,kBAAkB,QAM7B;AAED,IAAY,sBAKX;AALD,WAAY,sBAAsB;IAChC,+CAAqB,CAAA;IACrB,uCAAa,CAAA;IACb,2CAAiB,CAAA;IACjB,qCAAW,CAAA;AACb,CAAC,EALW,sBAAsB,sCAAtB,sBAAsB,QAKjC;AAUD,IAAY,wBAKX;AALD,WAAY,wBAAwB;IAClC,6CAAiB,CAAA;IACjB,iDAAqB,CAAA;IACrB,+CAAmB,CAAA;IACnB,yDAA6B,CAAA;AAC/B,CAAC,EALW,wBAAwB,wCAAxB,wBAAwB,QAKnC;AAgBD,IAAY,aAMX;AAND,WAAY,aAAa;IACvB,sDAAqC,CAAA;IACrC,gDAA+B,CAAA;IAC/B,0DAAyC,CAAA;IACzC,gDAA+B,CAAA;IAC/B,kEAAiD,CAAA;AACnD,CAAC,EANW,aAAa,6BAAb,aAAa,QAMxB;AAkBD,IAAY,UAKX;AALD,WAAY,UAAU;IACpB,6BAAe,CAAA;IACf,+BAAiB,CAAA;IACjB,6BAAe,CAAA;IACf,yCAA2B,CAAA;AAC7B,CAAC,EALW,UAAU,0BAAV,UAAU,QAKrB;AAED,IAAY,YASX;AATD,WAAY,YAAY;IACtB,iCAAiB,CAAA;IACjB,iCAAiB,CAAA;IACjB,yCAAyB,CAAA;IACzB,uCAAuB,CAAA;IACvB,yCAAyB,CAAA;IACzB,2CAA2B,CAAA;IAC3B,2CAA2B,CAAA;IAC3B,+BAAe,CAAA;AACjB,CAAC,EATW,YAAY,4BAAZ,YAAY,QASvB;AAsBD,IAAY,2BAKX;AALD,WAAY,2BAA2B;IACrC,oDAAqB,CAAA;IACrB,wDAAyB,CAAA;IACzB,sDAAuB,CAAA;IACvB,wEAAyC,CAAA;AAC3C,CAAC,EALW,2BAA2B,2CAA3B,2BAA2B,QAKtC;AAWD,IAAY,aAQX;AARD,WAAY,aAAa;IACvB,4BAAW,CAAA;IACX,gCAAe,CAAA;IACf,4BAAW,CAAA;IACX,kCAAiB,CAAA;IACjB,kCAAiB,CAAA;IACjB,8BAAa,CAAA;IACb,sCAAqB,CAAA;AACvB,CAAC,EARW,aAAa,6BAAb,aAAa,QAQxB;AAED,IAAY,iBAMX;AAND,WAAY,iBAAiB;IAC3B,4CAAuB,CAAA;IACvB,kCAAa,CAAA;IACb,kCAAa,CAAA;IACb,kCAAa,CAAA;IACb,0CAAqB,CAAA;AACvB,CAAC,EANW,iBAAiB,iCAAjB,iBAAiB,QAM5B;AAoCD,IAAY,SAMX;AAND,WAAY,SAAS;IACnB,oDAAuC,CAAA;IACvC,4DAA+C,CAAA;IAC/C,wCAA2B,CAAA;IAC3B,kDAAqC,CAAA;IACrC,4CAA+B,CAAA;AACjC,CAAC,EANW,SAAS,yBAAT,SAAS,QAMpB;AAmBD,IAAY,WAMX;AAND,WAAY,WAAW;IACrB,8CAA+B,CAAA;IAC/B,8CAA+B,CAAA;IAC/B,gDAAiC,CAAA;IACjC,kCAAmB,CAAA;IACnB,8CAA+B,CAAA;AACjC,CAAC,EANW,WAAW,2BAAX,WAAW,QAMtB;AAED,IAAY,eAKX;AALD,WAAY,eAAe;IACzB,wCAAqB,CAAA;IACrB,gCAAa,CAAA;IACb,oCAAiB,CAAA;IACjB,8BAAW,CAAA;AACb,CAAC,EALW,eAAe,+BAAf,eAAe,QAK1B;AASD,IAAY,aAOX;AAPD,WAAY,aAAa;IACvB,wDAAuC,CAAA;IACvC,wDAAuC,CAAA;IACvC,0DAAyC,CAAA;IACzC,8DAA6C,CAAA;IAC7C,kDAAiC,CAAA;IACjC,oCAAmB,CAAA;AACrB,CAAC,EAPW,aAAa,6BAAb,aAAa,QAOxB;AAiDD,oCAAoC;AACpC,4DAA0C",
      names: [],
      sources: ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\types\\SystemAnalysisTypes.ts"],
      sourcesContent: ["/**\r\n * System Analysis Type Definitions\r\n * \r\n * Comprehensive TypeScript interfaces for Phase 3 Priority 3: Advanced System Analysis Tools\r\n * Includes system performance analysis, energy efficiency metrics, lifecycle cost analysis,\r\n * environmental impact assessment, and compliance checking frameworks.\r\n * \r\n * @version 3.0.0\r\n * <AUTHOR> Suite Development Team\r\n */\r\n\r\n// ============================================================================\r\n// CORE SYSTEM ANALYSIS INTERFACES\r\n// ============================================================================\r\n\r\n/**\r\n * Main system analysis configuration and results interface\r\n */\r\nexport interface SystemAnalysis {\r\n  id: string;\r\n  name: string;\r\n  description: string;\r\n  systemConfiguration: SystemConfiguration;\r\n  analysisType: AnalysisType;\r\n  analysisScope: AnalysisScope;\r\n  performanceAnalysis?: PerformanceAnalysis;\r\n  energyAnalysis?: EnergyAnalysis;\r\n  costAnalysis?: LifecycleCostAnalysis;\r\n  environmentalAnalysis?: EnvironmentalImpactAnalysis;\r\n  complianceAnalysis?: ComplianceAnalysis;\r\n  timestamp: Date;\r\n  analysisVersion: string;\r\n}\r\n\r\n/**\r\n * Types of system analysis that can be performed\r\n */\r\nexport enum AnalysisType {\r\n  PERFORMANCE_ONLY = 'performance_only',\r\n  ENERGY_ONLY = 'energy_only',\r\n  COST_ONLY = 'cost_only',\r\n  ENVIRONMENTAL_ONLY = 'environmental_only',\r\n  COMPLIANCE_ONLY = 'compliance_only',\r\n  COMPREHENSIVE = 'comprehensive',\r\n  CUSTOM = 'custom'\r\n}\r\n\r\n/**\r\n * Scope of analysis - what parts of the system to analyze\r\n */\r\nexport interface AnalysisScope {\r\n  includePerformance: boolean;\r\n  includeEnergy: boolean;\r\n  includeCost: boolean;\r\n  includeEnvironmental: boolean;\r\n  includeCompliance: boolean;\r\n  timeHorizon: TimeHorizon;\r\n  analysisDepth: AnalysisDepth;\r\n  customParameters?: CustomAnalysisParameters;\r\n}\r\n\r\nexport enum TimeHorizon {\r\n  INSTANTANEOUS = 'instantaneous',\r\n  DAILY = 'daily',\r\n  MONTHLY = 'monthly',\r\n  ANNUAL = 'annual',\r\n  LIFECYCLE = 'lifecycle',\r\n  CUSTOM = 'custom'\r\n}\r\n\r\nexport enum AnalysisDepth {\r\n  BASIC = 'basic',\r\n  DETAILED = 'detailed',\r\n  COMPREHENSIVE = 'comprehensive',\r\n  RESEARCH_GRADE = 'research_grade'\r\n}\r\n\r\n// ============================================================================\r\n// SYSTEM PERFORMANCE ANALYSIS\r\n// ============================================================================\r\n\r\n/**\r\n * Comprehensive system performance analysis results\r\n */\r\nexport interface PerformanceAnalysis {\r\n  id: string;\r\n  systemId: string;\r\n  analysisTimestamp: Date;\r\n  performanceMetrics: PerformanceMetrics;\r\n  trendAnalysis: TrendAnalysis;\r\n  benchmarkComparison: BenchmarkComparison;\r\n  efficiencyAnalysis: EfficiencyAnalysis;\r\n  alertsAndWarnings: PerformanceAlert[];\r\n  recommendations: PerformanceRecommendation[];\r\n  uncertaintyAnalysis: UncertaintyAnalysis;\r\n}\r\n\r\n/**\r\n * Core performance metrics for HVAC systems\r\n */\r\nexport interface PerformanceMetrics {\r\n  // Pressure and Flow Metrics\r\n  totalSystemPressure: Measurement;\r\n  staticPressure: Measurement;\r\n  velocityPressure: Measurement;\r\n  totalAirflow: Measurement;\r\n  designAirflow: Measurement;\r\n  airflowEfficiency: Measurement;\r\n  \r\n  // Fan Performance\r\n  fanPower: Measurement;\r\n  fanEfficiency: Measurement;\r\n  fanSpeed: Measurement;\r\n  fanCurvePosition: FanCurvePosition;\r\n  \r\n  // System Efficiency\r\n  systemEfficiency: Measurement;\r\n  transportEfficiency: Measurement;\r\n  distributionEfficiency: Measurement;\r\n  \r\n  // Environmental Metrics\r\n  noiseLevel: Measurement;\r\n  vibrationLevel: Measurement;\r\n  temperatureRise: Measurement;\r\n  \r\n  // Filter and Component Performance\r\n  filterPressureDrop: Measurement;\r\n  coilPressureDrop: Measurement;\r\n  dampersPosition: DamperPosition[];\r\n  \r\n  // System Balance\r\n  balanceQuality: BalanceQuality;\r\n  flowDistribution: FlowDistribution;\r\n}\r\n\r\n/**\r\n * Measurement interface with value, units, and quality indicators\r\n */\r\nexport interface Measurement {\r\n  value: number;\r\n  units: string;\r\n  accuracy: number;\r\n  timestamp: Date;\r\n  source: MeasurementSource;\r\n  qualityIndicator: QualityIndicator;\r\n  uncertaintyBounds?: UncertaintyBounds;\r\n}\r\n\r\nexport enum MeasurementSource {\r\n  CALCULATED = 'calculated',\r\n  MEASURED = 'measured',\r\n  ESTIMATED = 'estimated',\r\n  MANUFACTURER_DATA = 'manufacturer_data',\r\n  SIMULATION = 'simulation'\r\n}\r\n\r\nexport enum QualityIndicator {\r\n  HIGH = 'high',\r\n  MEDIUM = 'medium',\r\n  LOW = 'low',\r\n  UNCERTAIN = 'uncertain'\r\n}\r\n\r\n/**\r\n * Fan curve position and operating point analysis\r\n */\r\nexport interface FanCurvePosition {\r\n  operatingPoint: OperatingPoint;\r\n  designPoint: OperatingPoint;\r\n  efficiencyAtOperating: number;\r\n  efficiencyAtDesign: number;\r\n  surgeMargin: number;\r\n  stallMargin: number;\r\n  recommendedOperatingRange: OperatingRange;\r\n}\r\n\r\nexport interface OperatingPoint {\r\n  airflow: number;\r\n  pressure: number;\r\n  power: number;\r\n  efficiency: number;\r\n  speed: number;\r\n}\r\n\r\n/**\r\n * System balance quality assessment\r\n */\r\nexport interface BalanceQuality {\r\n  overallScore: number; // 0-100\r\n  flowVariation: number; // Coefficient of variation\r\n  pressureVariation: number;\r\n  balanceGrade: BalanceGrade;\r\n  criticalZones: string[];\r\n  balanceRecommendations: string[];\r\n}\r\n\r\nexport enum BalanceGrade {\r\n  EXCELLENT = 'excellent',\r\n  GOOD = 'good',\r\n  ACCEPTABLE = 'acceptable',\r\n  POOR = 'poor',\r\n  CRITICAL = 'critical'\r\n}\r\n\r\n/**\r\n * Trend analysis for performance monitoring\r\n */\r\nexport interface TrendAnalysis {\r\n  timeRange: TimeRange;\r\n  trendDirection: TrendDirection;\r\n  trendMagnitude: number;\r\n  seasonalPatterns: SeasonalPattern[];\r\n  anomalies: PerformanceAnomaly[];\r\n  predictiveAnalysis: PredictiveAnalysis;\r\n  degradationRate: DegradationRate;\r\n}\r\n\r\nexport enum TrendDirection {\r\n  IMPROVING = 'improving',\r\n  STABLE = 'stable',\r\n  DEGRADING = 'degrading',\r\n  FLUCTUATING = 'fluctuating',\r\n  UNKNOWN = 'unknown'\r\n}\r\n\r\nexport interface SeasonalPattern {\r\n  season: Season;\r\n  averagePerformance: number;\r\n  performanceVariation: number;\r\n  typicalIssues: string[];\r\n}\r\n\r\nexport enum Season {\r\n  SPRING = 'spring',\r\n  SUMMER = 'summer',\r\n  FALL = 'fall',\r\n  WINTER = 'winter'\r\n}\r\n\r\n/**\r\n * Performance alerts and warnings system\r\n */\r\nexport interface PerformanceAlert {\r\n  id: string;\r\n  alertType: AlertType;\r\n  severity: AlertSeverity;\r\n  metric: string;\r\n  currentValue: number;\r\n  thresholdValue: number;\r\n  message: string;\r\n  timestamp: Date;\r\n  acknowledged: boolean;\r\n  recommendedActions: string[];\r\n}\r\n\r\nexport enum AlertType {\r\n  THRESHOLD_EXCEEDED = 'threshold_exceeded',\r\n  TREND_DEGRADATION = 'trend_degradation',\r\n  ANOMALY_DETECTED = 'anomaly_detected',\r\n  EFFICIENCY_DROP = 'efficiency_drop',\r\n  BALANCE_ISSUE = 'balance_issue',\r\n  MAINTENANCE_DUE = 'maintenance_due'\r\n}\r\n\r\nexport enum AlertSeverity {\r\n  CRITICAL = 'critical',\r\n  HIGH = 'high',\r\n  MEDIUM = 'medium',\r\n  LOW = 'low',\r\n  INFO = 'info'\r\n}\r\n\r\n// ============================================================================\r\n// ENERGY EFFICIENCY ANALYSIS\r\n// ============================================================================\r\n\r\n/**\r\n * Comprehensive energy efficiency analysis\r\n */\r\nexport interface EnergyAnalysis {\r\n  id: string;\r\n  systemId: string;\r\n  analysisTimestamp: Date;\r\n  energyConsumption: EnergyConsumption;\r\n  efficiencyMetrics: EnergyEfficiencyMetrics;\r\n  energyCosts: EnergyCosts;\r\n  carbonFootprint: CarbonFootprint;\r\n  benchmarkComparison: EnergyBenchmark;\r\n  optimizationOpportunities: EnergyOptimizationOpportunity[];\r\n  seasonalAnalysis: SeasonalEnergyAnalysis;\r\n}\r\n\r\n/**\r\n * Energy consumption breakdown and analysis\r\n */\r\nexport interface EnergyConsumption {\r\n  totalConsumption: EnergyMeasurement;\r\n  fanConsumption: EnergyMeasurement;\r\n  auxiliaryConsumption: EnergyMeasurement;\r\n  heatingConsumption?: EnergyMeasurement;\r\n  coolingConsumption?: EnergyMeasurement;\r\n  consumptionByTimeOfDay: TimeOfDayConsumption[];\r\n  loadProfile: LoadProfile;\r\n  peakDemand: PeakDemand;\r\n}\r\n\r\nexport interface EnergyMeasurement {\r\n  value: number;\r\n  units: EnergyUnits;\r\n  timeFrame: TimeFrame;\r\n  accuracy: number;\r\n  source: MeasurementSource;\r\n}\r\n\r\nexport enum EnergyUnits {\r\n  KWH = 'kWh',\r\n  BTU = 'BTU',\r\n  THERMS = 'therms',\r\n  JOULES = 'joules',\r\n  KW = 'kW'\r\n}\r\n\r\nexport enum TimeFrame {\r\n  HOURLY = 'hourly',\r\n  DAILY = 'daily',\r\n  WEEKLY = 'weekly',\r\n  MONTHLY = 'monthly',\r\n  ANNUALLY = 'annually'\r\n}\r\n\r\n/**\r\n * Energy efficiency metrics and calculations\r\n */\r\nexport interface EnergyEfficiencyMetrics {\r\n  overallEfficiency: number; // %\r\n  fanEfficiency: number; // %\r\n  systemEfficiency: number; // %\r\n  transportEfficiency: number; // %\r\n  specificFanPower: number; // W/CFM\r\n  energyUtilizationIndex: number; // EUI\r\n  powerDensity: number; // W/sq ft\r\n  efficiencyTrend: EfficiencyTrend;\r\n  benchmarkComparison: EfficiencyBenchmark;\r\n}\r\n\r\n/**\r\n * Energy cost analysis and projections\r\n */\r\nexport interface EnergyCosts {\r\n  currentCosts: CostBreakdown;\r\n  projectedCosts: CostProjection[];\r\n  costSavingOpportunities: CostSavingOpportunity[];\r\n  utilityRateStructure: UtilityRateStructure;\r\n  demandCharges: DemandCharges;\r\n  timeOfUsePricing: TimeOfUsePricing;\r\n}\r\n\r\nexport interface CostBreakdown {\r\n  totalCost: number;\r\n  energyCost: number;\r\n  demandCost: number;\r\n  fixedCost: number;\r\n  currency: string;\r\n  timeFrame: TimeFrame;\r\n}\r\n\r\n// ============================================================================\r\n// LIFECYCLE COST ANALYSIS\r\n// ============================================================================\r\n\r\n/**\r\n * Comprehensive lifecycle cost analysis\r\n */\r\nexport interface LifecycleCostAnalysis {\r\n  id: string;\r\n  systemId: string;\r\n  analysisTimestamp: Date;\r\n  analysisParameters: CostAnalysisParameters;\r\n  initialCosts: InitialCosts;\r\n  operatingCosts: OperatingCosts;\r\n  maintenanceCosts: MaintenanceCosts;\r\n  replacementCosts: ReplacementCosts;\r\n  totalCostOfOwnership: TotalCostOfOwnership;\r\n  costComparison: CostComparison;\r\n  sensitivityAnalysis: CostSensitivityAnalysis;\r\n  recommendations: CostRecommendation[];\r\n}\r\n\r\n/**\r\n * Parameters for lifecycle cost analysis\r\n */\r\nexport interface CostAnalysisParameters {\r\n  analysisHorizon: number; // years\r\n  discountRate: number; // %\r\n  inflationRate: number; // %\r\n  energyEscalationRate: number; // %\r\n  currency: string;\r\n  analysisMethod: CostAnalysisMethod;\r\n  uncertaintyLevel: UncertaintyLevel;\r\n}\r\n\r\nexport enum CostAnalysisMethod {\r\n  NET_PRESENT_VALUE = 'net_present_value',\r\n  LIFE_CYCLE_COST = 'life_cycle_cost',\r\n  PAYBACK_PERIOD = 'payback_period',\r\n  INTERNAL_RATE_OF_RETURN = 'internal_rate_of_return'\r\n}\r\n\r\nexport enum UncertaintyLevel {\r\n  LOW = 'low',\r\n  MEDIUM = 'medium',\r\n  HIGH = 'high'\r\n}\r\n\r\n/**\r\n * Initial system costs breakdown\r\n */\r\nexport interface InitialCosts {\r\n  equipmentCosts: EquipmentCosts;\r\n  installationCosts: InstallationCosts;\r\n  designCosts: DesignCosts;\r\n  permitsCosts: PermitsCosts;\r\n  totalInitialCost: number;\r\n  costPerCFM: number;\r\n  costPerSquareFoot: number;\r\n}\r\n\r\nexport interface EquipmentCosts {\r\n  fans: number;\r\n  ductwork: number;\r\n  fittings: number;\r\n  dampers: number;\r\n  controls: number;\r\n  accessories: number;\r\n  total: number;\r\n}\r\n\r\n// ============================================================================\r\n// ENVIRONMENTAL IMPACT ANALYSIS\r\n// ============================================================================\r\n\r\n/**\r\n * Environmental impact assessment\r\n */\r\nexport interface EnvironmentalImpactAnalysis {\r\n  id: string;\r\n  systemId: string;\r\n  analysisTimestamp: Date;\r\n  carbonFootprint: CarbonFootprint;\r\n  sustainabilityMetrics: SustainabilityMetrics;\r\n  greenBuildingCompliance: GreenBuildingCompliance;\r\n  environmentalCertifications: EnvironmentalCertification[];\r\n  lifecycleAssessment: LifecycleAssessment;\r\n  recommendations: EnvironmentalRecommendation[];\r\n}\r\n\r\n/**\r\n * Carbon footprint calculation and analysis\r\n */\r\nexport interface CarbonFootprint {\r\n  totalEmissions: EmissionMeasurement;\r\n  operationalEmissions: EmissionMeasurement;\r\n  embodiedEmissions: EmissionMeasurement;\r\n  emissionsBySource: EmissionSource[];\r\n  emissionsTrend: EmissionsTrend;\r\n  offsetOpportunities: OffsetOpportunity[];\r\n  benchmarkComparison: EmissionsBenchmark;\r\n}\r\n\r\nexport interface EmissionMeasurement {\r\n  value: number;\r\n  units: EmissionUnits;\r\n  timeFrame: TimeFrame;\r\n  scope: EmissionScope;\r\n  accuracy: number;\r\n}\r\n\r\nexport enum EmissionUnits {\r\n  KG_CO2E = 'kg_CO2e',\r\n  TONS_CO2E = 'tons_CO2e',\r\n  LBS_CO2E = 'lbs_CO2e'\r\n}\r\n\r\nexport enum EmissionScope {\r\n  SCOPE_1 = 'scope_1', // Direct emissions\r\n  SCOPE_2 = 'scope_2', // Indirect emissions from electricity\r\n  SCOPE_3 = 'scope_3'  // Other indirect emissions\r\n}\r\n\r\n// ============================================================================\r\n// COMPLIANCE ANALYSIS\r\n// ============================================================================\r\n\r\n/**\r\n * Compliance checking and validation\r\n */\r\nexport interface ComplianceAnalysis {\r\n  id: string;\r\n  systemId: string;\r\n  analysisTimestamp: Date;\r\n  complianceStandards: ComplianceStandard[];\r\n  complianceResults: ComplianceResult[];\r\n  overallComplianceStatus: ComplianceStatus;\r\n  nonComplianceIssues: NonComplianceIssue[];\r\n  recommendations: ComplianceRecommendation[];\r\n  certificationStatus: CertificationStatus;\r\n}\r\n\r\n/**\r\n * Compliance standards and codes\r\n */\r\nexport interface ComplianceStandard {\r\n  id: string;\r\n  name: string;\r\n  version: string;\r\n  type: StandardType;\r\n  jurisdiction: string;\r\n  applicability: StandardApplicability;\r\n  requirements: ComplianceRequirement[];\r\n}\r\n\r\nexport enum StandardType {\r\n  SMACNA = 'smacna',\r\n  ASHRAE = 'ashrae',\r\n  LOCAL_CODE = 'local_code',\r\n  INTERNATIONAL_CODE = 'international_code',\r\n  INDUSTRY_STANDARD = 'industry_standard',\r\n  GREEN_BUILDING = 'green_building'\r\n}\r\n\r\nexport interface ComplianceResult {\r\n  standardId: string;\r\n  requirementId: string;\r\n  status: ComplianceStatus;\r\n  actualValue: number;\r\n  requiredValue: number;\r\n  units: string;\r\n  margin: number;\r\n  notes: string;\r\n}\r\n\r\nexport enum ComplianceStatus {\r\n  COMPLIANT = 'compliant',\r\n  NON_COMPLIANT = 'non_compliant',\r\n  MARGINAL = 'marginal',\r\n  NOT_APPLICABLE = 'not_applicable',\r\n  REQUIRES_REVIEW = 'requires_review'\r\n}\r\n\r\n// ============================================================================\r\n// UTILITY INTERFACES\r\n// ============================================================================\r\n\r\nexport interface TimeRange {\r\n  startDate: Date;\r\n  endDate: Date;\r\n  duration: number;\r\n  units: TimeUnits;\r\n}\r\n\r\nexport enum TimeUnits {\r\n  SECONDS = 'seconds',\r\n  MINUTES = 'minutes',\r\n  HOURS = 'hours',\r\n  DAYS = 'days',\r\n  WEEKS = 'weeks',\r\n  MONTHS = 'months',\r\n  YEARS = 'years'\r\n}\r\n\r\nexport interface UncertaintyBounds {\r\n  lowerBound: number;\r\n  upperBound: number;\r\n  confidenceLevel: number; // %\r\n  distributionType: DistributionType;\r\n}\r\n\r\nexport enum DistributionType {\r\n  NORMAL = 'normal',\r\n  UNIFORM = 'uniform',\r\n  TRIANGULAR = 'triangular',\r\n  LOGNORMAL = 'lognormal'\r\n}\r\n\r\nexport interface CustomAnalysisParameters {\r\n  [key: string]: any;\r\n}\r\n\r\n// ============================================================================\r\n// SYSTEM CONFIGURATION (EXTENDED)\r\n// ============================================================================\r\n\r\nexport interface SystemConfiguration {\r\n  id: string;\r\n  name: string;\r\n  description: string;\r\n  systemType: SystemType;\r\n  designParameters: DesignParameters;\r\n  operatingConditions: OperatingConditions;\r\n  components: SystemComponent[];\r\n  controlStrategy: ControlStrategy;\r\n  maintenanceSchedule: MaintenanceSchedule;\r\n}\r\n\r\nexport enum SystemType {\r\n  SUPPLY_AIR = 'supply_air',\r\n  RETURN_AIR = 'return_air',\r\n  EXHAUST_AIR = 'exhaust_air',\r\n  MIXED_AIR = 'mixed_air',\r\n  DEDICATED_OUTDOOR_AIR = 'dedicated_outdoor_air'\r\n}\r\n\r\nexport interface DesignParameters {\r\n  designAirflow: number; // CFM\r\n  designPressure: number; // in wg\r\n  designTemperature: number; // \xB0F\r\n  designHumidity: number; // %RH\r\n  designElevation: number; // ft\r\n  safetyFactors: SafetyFactors;\r\n}\r\n\r\nexport interface SafetyFactors {\r\n  pressureSafetyFactor: number;\r\n  airflowSafetyFactor: number;\r\n  powerSafetyFactor: number;\r\n}\r\n\r\n// ============================================================================\r\n// ADDITIONAL INTERFACES FOR COMPREHENSIVE ANALYSIS\r\n// ============================================================================\r\n\r\n/**\r\n * Performance recommendations with priority and impact assessment\r\n */\r\nexport interface PerformanceRecommendation {\r\n  id: string;\r\n  type: RecommendationType;\r\n  priority: RecommendationPriority;\r\n  title: string;\r\n  description: string;\r\n  expectedImpact: ExpectedImpact;\r\n  implementationCost: number;\r\n  paybackPeriod: number; // months\r\n  implementationComplexity: ImplementationComplexity;\r\n  requiredActions: string[];\r\n  timeline: string;\r\n}\r\n\r\nexport enum RecommendationType {\r\n  OPTIMIZATION = 'optimization',\r\n  MAINTENANCE = 'maintenance',\r\n  UPGRADE = 'upgrade',\r\n  OPERATIONAL_CHANGE = 'operational_change',\r\n  DESIGN_MODIFICATION = 'design_modification'\r\n}\r\n\r\nexport enum RecommendationPriority {\r\n  CRITICAL = 'critical',\r\n  HIGH = 'high',\r\n  MEDIUM = 'medium',\r\n  LOW = 'low'\r\n}\r\n\r\nexport interface ExpectedImpact {\r\n  energySavings: number; // %\r\n  costSavings: number; // $/year\r\n  performanceImprovement: number; // %\r\n  emissionReduction: number; // kg CO2e/year\r\n  reliabilityImprovement: number; // %\r\n}\r\n\r\nexport enum ImplementationComplexity {\r\n  SIMPLE = 'simple',\r\n  MODERATE = 'moderate',\r\n  COMPLEX = 'complex',\r\n  VERY_COMPLEX = 'very_complex'\r\n}\r\n\r\n/**\r\n * Benchmark comparison for performance analysis\r\n */\r\nexport interface BenchmarkComparison {\r\n  benchmarkType: BenchmarkType;\r\n  benchmarkSource: string;\r\n  systemPerformance: number;\r\n  benchmarkValue: number;\r\n  percentile: number;\r\n  performanceGap: number;\r\n  improvementPotential: number;\r\n  similarSystems: SimilarSystemComparison[];\r\n}\r\n\r\nexport enum BenchmarkType {\r\n  INDUSTRY_AVERAGE = 'industry_average',\r\n  BEST_IN_CLASS = 'best_in_class',\r\n  REGULATORY_MINIMUM = 'regulatory_minimum',\r\n  DESIGN_TARGET = 'design_target',\r\n  HISTORICAL_PERFORMANCE = 'historical_performance'\r\n}\r\n\r\nexport interface SimilarSystemComparison {\r\n  systemId: string;\r\n  systemName: string;\r\n  performanceMetric: number;\r\n  systemCharacteristics: SystemCharacteristics;\r\n  performanceDifference: number;\r\n}\r\n\r\nexport interface SystemCharacteristics {\r\n  size: SystemSize;\r\n  age: number; // years\r\n  buildingType: BuildingType;\r\n  climateZone: string;\r\n  operatingHours: number; // hours/year\r\n}\r\n\r\nexport enum SystemSize {\r\n  SMALL = 'small',\r\n  MEDIUM = 'medium',\r\n  LARGE = 'large',\r\n  EXTRA_LARGE = 'extra_large'\r\n}\r\n\r\nexport enum BuildingType {\r\n  OFFICE = 'office',\r\n  RETAIL = 'retail',\r\n  HEALTHCARE = 'healthcare',\r\n  EDUCATION = 'education',\r\n  INDUSTRIAL = 'industrial',\r\n  RESIDENTIAL = 'residential',\r\n  HOSPITALITY = 'hospitality',\r\n  OTHER = 'other'\r\n}\r\n\r\n/**\r\n * Efficiency analysis with detailed breakdown\r\n */\r\nexport interface EfficiencyAnalysis {\r\n  overallEfficiency: EfficiencyMetric;\r\n  componentEfficiencies: ComponentEfficiency[];\r\n  efficiencyTrends: EfficiencyTrend[];\r\n  efficiencyLosses: EfficiencyLoss[];\r\n  improvementOpportunities: EfficiencyImprovement[];\r\n  benchmarkComparison: EfficiencyBenchmark;\r\n}\r\n\r\nexport interface EfficiencyMetric {\r\n  value: number;\r\n  units: string;\r\n  calculationMethod: EfficiencyCalculationMethod;\r\n  accuracy: number;\r\n  timestamp: Date;\r\n}\r\n\r\nexport enum EfficiencyCalculationMethod {\r\n  MEASURED = 'measured',\r\n  CALCULATED = 'calculated',\r\n  ESTIMATED = 'estimated',\r\n  MANUFACTURER_RATED = 'manufacturer_rated'\r\n}\r\n\r\nexport interface ComponentEfficiency {\r\n  componentId: string;\r\n  componentType: ComponentType;\r\n  efficiency: number;\r\n  ratedEfficiency: number;\r\n  degradationFactor: number;\r\n  maintenanceStatus: MaintenanceStatus;\r\n}\r\n\r\nexport enum ComponentType {\r\n  FAN = 'fan',\r\n  MOTOR = 'motor',\r\n  VFD = 'vfd',\r\n  DAMPER = 'damper',\r\n  FILTER = 'filter',\r\n  COIL = 'coil',\r\n  DUCTWORK = 'ductwork'\r\n}\r\n\r\nexport enum MaintenanceStatus {\r\n  EXCELLENT = 'excellent',\r\n  GOOD = 'good',\r\n  FAIR = 'fair',\r\n  POOR = 'poor',\r\n  CRITICAL = 'critical'\r\n}\r\n\r\n/**\r\n * Predictive analysis for performance forecasting\r\n */\r\nexport interface PredictiveAnalysis {\r\n  forecastHorizon: number; // months\r\n  predictedPerformance: PredictedMetric[];\r\n  confidenceInterval: ConfidenceInterval;\r\n  predictionModel: PredictionModel;\r\n  keyFactors: PredictionFactor[];\r\n  scenarios: PredictionScenario[];\r\n}\r\n\r\nexport interface PredictedMetric {\r\n  metric: string;\r\n  currentValue: number;\r\n  predictedValue: number;\r\n  changePercent: number;\r\n  timeToTarget: number; // months\r\n}\r\n\r\nexport interface ConfidenceInterval {\r\n  lowerBound: number;\r\n  upperBound: number;\r\n  confidenceLevel: number; // %\r\n}\r\n\r\nexport interface PredictionModel {\r\n  modelType: ModelType;\r\n  accuracy: number; // %\r\n  lastTrainingDate: Date;\r\n  dataPoints: number;\r\n  validationScore: number;\r\n}\r\n\r\nexport enum ModelType {\r\n  LINEAR_REGRESSION = 'linear_regression',\r\n  POLYNOMIAL_REGRESSION = 'polynomial_regression',\r\n  TIME_SERIES = 'time_series',\r\n  MACHINE_LEARNING = 'machine_learning',\r\n  PHYSICS_BASED = 'physics_based'\r\n}\r\n\r\n/**\r\n * Performance anomaly detection and analysis\r\n */\r\nexport interface PerformanceAnomaly {\r\n  id: string;\r\n  detectionTimestamp: Date;\r\n  anomalyType: AnomalyType;\r\n  severity: AnomalySeverity;\r\n  affectedMetrics: string[];\r\n  deviationMagnitude: number;\r\n  duration: number; // hours\r\n  possibleCauses: PossibleCause[];\r\n  recommendedActions: string[];\r\n  resolved: boolean;\r\n  resolutionDate?: Date;\r\n}\r\n\r\nexport enum AnomalyType {\r\n  SUDDEN_CHANGE = 'sudden_change',\r\n  GRADUAL_DRIFT = 'gradual_drift',\r\n  CYCLIC_ANOMALY = 'cyclic_anomaly',\r\n  OUTLIER = 'outlier',\r\n  PATTERN_BREAK = 'pattern_break'\r\n}\r\n\r\nexport enum AnomalySeverity {\r\n  CRITICAL = 'critical',\r\n  HIGH = 'high',\r\n  MEDIUM = 'medium',\r\n  LOW = 'low'\r\n}\r\n\r\nexport interface PossibleCause {\r\n  cause: string;\r\n  probability: number; // %\r\n  category: CauseCategory;\r\n  diagnosticSteps: string[];\r\n}\r\n\r\nexport enum CauseCategory {\r\n  EQUIPMENT_FAILURE = 'equipment_failure',\r\n  MAINTENANCE_ISSUE = 'maintenance_issue',\r\n  OPERATIONAL_CHANGE = 'operational_change',\r\n  ENVIRONMENTAL_FACTOR = 'environmental_factor',\r\n  CONTROL_SYSTEM = 'control_system',\r\n  UNKNOWN = 'unknown'\r\n}\r\n\r\n/**\r\n * Degradation rate analysis\r\n */\r\nexport interface DegradationRate {\r\n  overallDegradationRate: number; // %/year\r\n  componentDegradation: ComponentDegradation[];\r\n  degradationFactors: DegradationFactor[];\r\n  maintenanceImpact: MaintenanceImpact;\r\n  projectedLifespan: ProjectedLifespan;\r\n}\r\n\r\nexport interface ComponentDegradation {\r\n  componentId: string;\r\n  componentType: ComponentType;\r\n  degradationRate: number; // %/year\r\n  currentCondition: number; // % of original performance\r\n  estimatedRemainingLife: number; // years\r\n  replacementThreshold: number; // % performance\r\n}\r\n\r\nexport interface DegradationFactor {\r\n  factor: string;\r\n  impact: number; // % contribution to degradation\r\n  controllable: boolean;\r\n  mitigationStrategies: string[];\r\n}\r\n\r\nexport interface MaintenanceImpact {\r\n  preventiveMaintenance: MaintenanceEffect;\r\n  correctiveMaintenance: MaintenanceEffect;\r\n  deferredMaintenance: MaintenanceEffect;\r\n}\r\n\r\nexport interface MaintenanceEffect {\r\n  performanceImpact: number; // %\r\n  lifespanImpact: number; // years\r\n  costImpact: number; // $/year\r\n}\r\n\r\nexport interface ProjectedLifespan {\r\n  currentAge: number; // years\r\n  designLife: number; // years\r\n  projectedLife: number; // years\r\n  confidenceLevel: number; // %\r\n  keyAssumptions: string[];\r\n}\r\n\r\n// Export all types for external use\r\nexport * from './SystemOptimizationTypes';\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "d685e5bbcacfdd0df1ebfe89daf27e6f10aae0b7"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_12k33bco6g = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_12k33bco6g();
var __createBinding =
/* istanbul ignore next */
(cov_12k33bco6g().s[0]++,
/* istanbul ignore next */
(cov_12k33bco6g().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_12k33bco6g().b[0][1]++, this.__createBinding) ||
/* istanbul ignore next */
(cov_12k33bco6g().b[0][2]++, Object.create ?
/* istanbul ignore next */
(cov_12k33bco6g().b[1][0]++, function (o, m, k, k2) {
  /* istanbul ignore next */
  cov_12k33bco6g().f[0]++;
  cov_12k33bco6g().s[1]++;
  if (k2 === undefined) {
    /* istanbul ignore next */
    cov_12k33bco6g().b[2][0]++;
    cov_12k33bco6g().s[2]++;
    k2 = k;
  } else
  /* istanbul ignore next */
  {
    cov_12k33bco6g().b[2][1]++;
  }
  var desc =
  /* istanbul ignore next */
  (cov_12k33bco6g().s[3]++, Object.getOwnPropertyDescriptor(m, k));
  /* istanbul ignore next */
  cov_12k33bco6g().s[4]++;
  if (
  /* istanbul ignore next */
  (cov_12k33bco6g().b[4][0]++, !desc) ||
  /* istanbul ignore next */
  (cov_12k33bco6g().b[4][1]++, "get" in desc ?
  /* istanbul ignore next */
  (cov_12k33bco6g().b[5][0]++, !m.__esModule) :
  /* istanbul ignore next */
  (cov_12k33bco6g().b[5][1]++,
  /* istanbul ignore next */
  (cov_12k33bco6g().b[6][0]++, desc.writable) ||
  /* istanbul ignore next */
  (cov_12k33bco6g().b[6][1]++, desc.configurable)))) {
    /* istanbul ignore next */
    cov_12k33bco6g().b[3][0]++;
    cov_12k33bco6g().s[5]++;
    desc = {
      enumerable: true,
      get: function () {
        /* istanbul ignore next */
        cov_12k33bco6g().f[1]++;
        cov_12k33bco6g().s[6]++;
        return m[k];
      }
    };
  } else
  /* istanbul ignore next */
  {
    cov_12k33bco6g().b[3][1]++;
  }
  cov_12k33bco6g().s[7]++;
  Object.defineProperty(o, k2, desc);
}) :
/* istanbul ignore next */
(cov_12k33bco6g().b[1][1]++, function (o, m, k, k2) {
  /* istanbul ignore next */
  cov_12k33bco6g().f[2]++;
  cov_12k33bco6g().s[8]++;
  if (k2 === undefined) {
    /* istanbul ignore next */
    cov_12k33bco6g().b[7][0]++;
    cov_12k33bco6g().s[9]++;
    k2 = k;
  } else
  /* istanbul ignore next */
  {
    cov_12k33bco6g().b[7][1]++;
  }
  cov_12k33bco6g().s[10]++;
  o[k2] = m[k];
})));
var __exportStar =
/* istanbul ignore next */
(cov_12k33bco6g().s[11]++,
/* istanbul ignore next */
(cov_12k33bco6g().b[8][0]++, this) &&
/* istanbul ignore next */
(cov_12k33bco6g().b[8][1]++, this.__exportStar) ||
/* istanbul ignore next */
(cov_12k33bco6g().b[8][2]++, function (m, exports) {
  /* istanbul ignore next */
  cov_12k33bco6g().f[3]++;
  cov_12k33bco6g().s[12]++;
  for (var p in m) {
    /* istanbul ignore next */
    cov_12k33bco6g().s[13]++;
    if (
    /* istanbul ignore next */
    (cov_12k33bco6g().b[10][0]++, p !== "default") &&
    /* istanbul ignore next */
    (cov_12k33bco6g().b[10][1]++, !Object.prototype.hasOwnProperty.call(exports, p))) {
      /* istanbul ignore next */
      cov_12k33bco6g().b[9][0]++;
      cov_12k33bco6g().s[14]++;
      __createBinding(exports, m, p);
    } else
    /* istanbul ignore next */
    {
      cov_12k33bco6g().b[9][1]++;
    }
  }
}));
/* istanbul ignore next */
cov_12k33bco6g().s[15]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_12k33bco6g().s[16]++;
exports.CauseCategory = exports.AnomalySeverity = exports.AnomalyType = exports.ModelType = exports.MaintenanceStatus = exports.ComponentType = exports.EfficiencyCalculationMethod = exports.BuildingType = exports.SystemSize = exports.BenchmarkType = exports.ImplementationComplexity = exports.RecommendationPriority = exports.RecommendationType = exports.SystemType = exports.DistributionType = exports.TimeUnits = exports.ComplianceStatus = exports.StandardType = exports.EmissionScope = exports.EmissionUnits = exports.UncertaintyLevel = exports.CostAnalysisMethod = exports.TimeFrame = exports.EnergyUnits = exports.AlertSeverity = exports.AlertType = exports.Season = exports.TrendDirection = exports.BalanceGrade = exports.QualityIndicator = exports.MeasurementSource = exports.AnalysisDepth = exports.TimeHorizon = exports.AnalysisType = void 0;
/**
 * Types of system analysis that can be performed
 */
var AnalysisType;
/* istanbul ignore next */
cov_12k33bco6g().s[17]++;
(function (AnalysisType) {
  /* istanbul ignore next */
  cov_12k33bco6g().f[4]++;
  cov_12k33bco6g().s[18]++;
  AnalysisType["PERFORMANCE_ONLY"] = "performance_only";
  /* istanbul ignore next */
  cov_12k33bco6g().s[19]++;
  AnalysisType["ENERGY_ONLY"] = "energy_only";
  /* istanbul ignore next */
  cov_12k33bco6g().s[20]++;
  AnalysisType["COST_ONLY"] = "cost_only";
  /* istanbul ignore next */
  cov_12k33bco6g().s[21]++;
  AnalysisType["ENVIRONMENTAL_ONLY"] = "environmental_only";
  /* istanbul ignore next */
  cov_12k33bco6g().s[22]++;
  AnalysisType["COMPLIANCE_ONLY"] = "compliance_only";
  /* istanbul ignore next */
  cov_12k33bco6g().s[23]++;
  AnalysisType["COMPREHENSIVE"] = "comprehensive";
  /* istanbul ignore next */
  cov_12k33bco6g().s[24]++;
  AnalysisType["CUSTOM"] = "custom";
})(
/* istanbul ignore next */
(cov_12k33bco6g().b[11][0]++, AnalysisType) ||
/* istanbul ignore next */
(cov_12k33bco6g().b[11][1]++, exports.AnalysisType = AnalysisType = {}));
var TimeHorizon;
/* istanbul ignore next */
cov_12k33bco6g().s[25]++;
(function (TimeHorizon) {
  /* istanbul ignore next */
  cov_12k33bco6g().f[5]++;
  cov_12k33bco6g().s[26]++;
  TimeHorizon["INSTANTANEOUS"] = "instantaneous";
  /* istanbul ignore next */
  cov_12k33bco6g().s[27]++;
  TimeHorizon["DAILY"] = "daily";
  /* istanbul ignore next */
  cov_12k33bco6g().s[28]++;
  TimeHorizon["MONTHLY"] = "monthly";
  /* istanbul ignore next */
  cov_12k33bco6g().s[29]++;
  TimeHorizon["ANNUAL"] = "annual";
  /* istanbul ignore next */
  cov_12k33bco6g().s[30]++;
  TimeHorizon["LIFECYCLE"] = "lifecycle";
  /* istanbul ignore next */
  cov_12k33bco6g().s[31]++;
  TimeHorizon["CUSTOM"] = "custom";
})(
/* istanbul ignore next */
(cov_12k33bco6g().b[12][0]++, TimeHorizon) ||
/* istanbul ignore next */
(cov_12k33bco6g().b[12][1]++, exports.TimeHorizon = TimeHorizon = {}));
var AnalysisDepth;
/* istanbul ignore next */
cov_12k33bco6g().s[32]++;
(function (AnalysisDepth) {
  /* istanbul ignore next */
  cov_12k33bco6g().f[6]++;
  cov_12k33bco6g().s[33]++;
  AnalysisDepth["BASIC"] = "basic";
  /* istanbul ignore next */
  cov_12k33bco6g().s[34]++;
  AnalysisDepth["DETAILED"] = "detailed";
  /* istanbul ignore next */
  cov_12k33bco6g().s[35]++;
  AnalysisDepth["COMPREHENSIVE"] = "comprehensive";
  /* istanbul ignore next */
  cov_12k33bco6g().s[36]++;
  AnalysisDepth["RESEARCH_GRADE"] = "research_grade";
})(
/* istanbul ignore next */
(cov_12k33bco6g().b[13][0]++, AnalysisDepth) ||
/* istanbul ignore next */
(cov_12k33bco6g().b[13][1]++, exports.AnalysisDepth = AnalysisDepth = {}));
var MeasurementSource;
/* istanbul ignore next */
cov_12k33bco6g().s[37]++;
(function (MeasurementSource) {
  /* istanbul ignore next */
  cov_12k33bco6g().f[7]++;
  cov_12k33bco6g().s[38]++;
  MeasurementSource["CALCULATED"] = "calculated";
  /* istanbul ignore next */
  cov_12k33bco6g().s[39]++;
  MeasurementSource["MEASURED"] = "measured";
  /* istanbul ignore next */
  cov_12k33bco6g().s[40]++;
  MeasurementSource["ESTIMATED"] = "estimated";
  /* istanbul ignore next */
  cov_12k33bco6g().s[41]++;
  MeasurementSource["MANUFACTURER_DATA"] = "manufacturer_data";
  /* istanbul ignore next */
  cov_12k33bco6g().s[42]++;
  MeasurementSource["SIMULATION"] = "simulation";
})(
/* istanbul ignore next */
(cov_12k33bco6g().b[14][0]++, MeasurementSource) ||
/* istanbul ignore next */
(cov_12k33bco6g().b[14][1]++, exports.MeasurementSource = MeasurementSource = {}));
var QualityIndicator;
/* istanbul ignore next */
cov_12k33bco6g().s[43]++;
(function (QualityIndicator) {
  /* istanbul ignore next */
  cov_12k33bco6g().f[8]++;
  cov_12k33bco6g().s[44]++;
  QualityIndicator["HIGH"] = "high";
  /* istanbul ignore next */
  cov_12k33bco6g().s[45]++;
  QualityIndicator["MEDIUM"] = "medium";
  /* istanbul ignore next */
  cov_12k33bco6g().s[46]++;
  QualityIndicator["LOW"] = "low";
  /* istanbul ignore next */
  cov_12k33bco6g().s[47]++;
  QualityIndicator["UNCERTAIN"] = "uncertain";
})(
/* istanbul ignore next */
(cov_12k33bco6g().b[15][0]++, QualityIndicator) ||
/* istanbul ignore next */
(cov_12k33bco6g().b[15][1]++, exports.QualityIndicator = QualityIndicator = {}));
var BalanceGrade;
/* istanbul ignore next */
cov_12k33bco6g().s[48]++;
(function (BalanceGrade) {
  /* istanbul ignore next */
  cov_12k33bco6g().f[9]++;
  cov_12k33bco6g().s[49]++;
  BalanceGrade["EXCELLENT"] = "excellent";
  /* istanbul ignore next */
  cov_12k33bco6g().s[50]++;
  BalanceGrade["GOOD"] = "good";
  /* istanbul ignore next */
  cov_12k33bco6g().s[51]++;
  BalanceGrade["ACCEPTABLE"] = "acceptable";
  /* istanbul ignore next */
  cov_12k33bco6g().s[52]++;
  BalanceGrade["POOR"] = "poor";
  /* istanbul ignore next */
  cov_12k33bco6g().s[53]++;
  BalanceGrade["CRITICAL"] = "critical";
})(
/* istanbul ignore next */
(cov_12k33bco6g().b[16][0]++, BalanceGrade) ||
/* istanbul ignore next */
(cov_12k33bco6g().b[16][1]++, exports.BalanceGrade = BalanceGrade = {}));
var TrendDirection;
/* istanbul ignore next */
cov_12k33bco6g().s[54]++;
(function (TrendDirection) {
  /* istanbul ignore next */
  cov_12k33bco6g().f[10]++;
  cov_12k33bco6g().s[55]++;
  TrendDirection["IMPROVING"] = "improving";
  /* istanbul ignore next */
  cov_12k33bco6g().s[56]++;
  TrendDirection["STABLE"] = "stable";
  /* istanbul ignore next */
  cov_12k33bco6g().s[57]++;
  TrendDirection["DEGRADING"] = "degrading";
  /* istanbul ignore next */
  cov_12k33bco6g().s[58]++;
  TrendDirection["FLUCTUATING"] = "fluctuating";
  /* istanbul ignore next */
  cov_12k33bco6g().s[59]++;
  TrendDirection["UNKNOWN"] = "unknown";
})(
/* istanbul ignore next */
(cov_12k33bco6g().b[17][0]++, TrendDirection) ||
/* istanbul ignore next */
(cov_12k33bco6g().b[17][1]++, exports.TrendDirection = TrendDirection = {}));
var Season;
/* istanbul ignore next */
cov_12k33bco6g().s[60]++;
(function (Season) {
  /* istanbul ignore next */
  cov_12k33bco6g().f[11]++;
  cov_12k33bco6g().s[61]++;
  Season["SPRING"] = "spring";
  /* istanbul ignore next */
  cov_12k33bco6g().s[62]++;
  Season["SUMMER"] = "summer";
  /* istanbul ignore next */
  cov_12k33bco6g().s[63]++;
  Season["FALL"] = "fall";
  /* istanbul ignore next */
  cov_12k33bco6g().s[64]++;
  Season["WINTER"] = "winter";
})(
/* istanbul ignore next */
(cov_12k33bco6g().b[18][0]++, Season) ||
/* istanbul ignore next */
(cov_12k33bco6g().b[18][1]++, exports.Season = Season = {}));
var AlertType;
/* istanbul ignore next */
cov_12k33bco6g().s[65]++;
(function (AlertType) {
  /* istanbul ignore next */
  cov_12k33bco6g().f[12]++;
  cov_12k33bco6g().s[66]++;
  AlertType["THRESHOLD_EXCEEDED"] = "threshold_exceeded";
  /* istanbul ignore next */
  cov_12k33bco6g().s[67]++;
  AlertType["TREND_DEGRADATION"] = "trend_degradation";
  /* istanbul ignore next */
  cov_12k33bco6g().s[68]++;
  AlertType["ANOMALY_DETECTED"] = "anomaly_detected";
  /* istanbul ignore next */
  cov_12k33bco6g().s[69]++;
  AlertType["EFFICIENCY_DROP"] = "efficiency_drop";
  /* istanbul ignore next */
  cov_12k33bco6g().s[70]++;
  AlertType["BALANCE_ISSUE"] = "balance_issue";
  /* istanbul ignore next */
  cov_12k33bco6g().s[71]++;
  AlertType["MAINTENANCE_DUE"] = "maintenance_due";
})(
/* istanbul ignore next */
(cov_12k33bco6g().b[19][0]++, AlertType) ||
/* istanbul ignore next */
(cov_12k33bco6g().b[19][1]++, exports.AlertType = AlertType = {}));
var AlertSeverity;
/* istanbul ignore next */
cov_12k33bco6g().s[72]++;
(function (AlertSeverity) {
  /* istanbul ignore next */
  cov_12k33bco6g().f[13]++;
  cov_12k33bco6g().s[73]++;
  AlertSeverity["CRITICAL"] = "critical";
  /* istanbul ignore next */
  cov_12k33bco6g().s[74]++;
  AlertSeverity["HIGH"] = "high";
  /* istanbul ignore next */
  cov_12k33bco6g().s[75]++;
  AlertSeverity["MEDIUM"] = "medium";
  /* istanbul ignore next */
  cov_12k33bco6g().s[76]++;
  AlertSeverity["LOW"] = "low";
  /* istanbul ignore next */
  cov_12k33bco6g().s[77]++;
  AlertSeverity["INFO"] = "info";
})(
/* istanbul ignore next */
(cov_12k33bco6g().b[20][0]++, AlertSeverity) ||
/* istanbul ignore next */
(cov_12k33bco6g().b[20][1]++, exports.AlertSeverity = AlertSeverity = {}));
var EnergyUnits;
/* istanbul ignore next */
cov_12k33bco6g().s[78]++;
(function (EnergyUnits) {
  /* istanbul ignore next */
  cov_12k33bco6g().f[14]++;
  cov_12k33bco6g().s[79]++;
  EnergyUnits["KWH"] = "kWh";
  /* istanbul ignore next */
  cov_12k33bco6g().s[80]++;
  EnergyUnits["BTU"] = "BTU";
  /* istanbul ignore next */
  cov_12k33bco6g().s[81]++;
  EnergyUnits["THERMS"] = "therms";
  /* istanbul ignore next */
  cov_12k33bco6g().s[82]++;
  EnergyUnits["JOULES"] = "joules";
  /* istanbul ignore next */
  cov_12k33bco6g().s[83]++;
  EnergyUnits["KW"] = "kW";
})(
/* istanbul ignore next */
(cov_12k33bco6g().b[21][0]++, EnergyUnits) ||
/* istanbul ignore next */
(cov_12k33bco6g().b[21][1]++, exports.EnergyUnits = EnergyUnits = {}));
var TimeFrame;
/* istanbul ignore next */
cov_12k33bco6g().s[84]++;
(function (TimeFrame) {
  /* istanbul ignore next */
  cov_12k33bco6g().f[15]++;
  cov_12k33bco6g().s[85]++;
  TimeFrame["HOURLY"] = "hourly";
  /* istanbul ignore next */
  cov_12k33bco6g().s[86]++;
  TimeFrame["DAILY"] = "daily";
  /* istanbul ignore next */
  cov_12k33bco6g().s[87]++;
  TimeFrame["WEEKLY"] = "weekly";
  /* istanbul ignore next */
  cov_12k33bco6g().s[88]++;
  TimeFrame["MONTHLY"] = "monthly";
  /* istanbul ignore next */
  cov_12k33bco6g().s[89]++;
  TimeFrame["ANNUALLY"] = "annually";
})(
/* istanbul ignore next */
(cov_12k33bco6g().b[22][0]++, TimeFrame) ||
/* istanbul ignore next */
(cov_12k33bco6g().b[22][1]++, exports.TimeFrame = TimeFrame = {}));
var CostAnalysisMethod;
/* istanbul ignore next */
cov_12k33bco6g().s[90]++;
(function (CostAnalysisMethod) {
  /* istanbul ignore next */
  cov_12k33bco6g().f[16]++;
  cov_12k33bco6g().s[91]++;
  CostAnalysisMethod["NET_PRESENT_VALUE"] = "net_present_value";
  /* istanbul ignore next */
  cov_12k33bco6g().s[92]++;
  CostAnalysisMethod["LIFE_CYCLE_COST"] = "life_cycle_cost";
  /* istanbul ignore next */
  cov_12k33bco6g().s[93]++;
  CostAnalysisMethod["PAYBACK_PERIOD"] = "payback_period";
  /* istanbul ignore next */
  cov_12k33bco6g().s[94]++;
  CostAnalysisMethod["INTERNAL_RATE_OF_RETURN"] = "internal_rate_of_return";
})(
/* istanbul ignore next */
(cov_12k33bco6g().b[23][0]++, CostAnalysisMethod) ||
/* istanbul ignore next */
(cov_12k33bco6g().b[23][1]++, exports.CostAnalysisMethod = CostAnalysisMethod = {}));
var UncertaintyLevel;
/* istanbul ignore next */
cov_12k33bco6g().s[95]++;
(function (UncertaintyLevel) {
  /* istanbul ignore next */
  cov_12k33bco6g().f[17]++;
  cov_12k33bco6g().s[96]++;
  UncertaintyLevel["LOW"] = "low";
  /* istanbul ignore next */
  cov_12k33bco6g().s[97]++;
  UncertaintyLevel["MEDIUM"] = "medium";
  /* istanbul ignore next */
  cov_12k33bco6g().s[98]++;
  UncertaintyLevel["HIGH"] = "high";
})(
/* istanbul ignore next */
(cov_12k33bco6g().b[24][0]++, UncertaintyLevel) ||
/* istanbul ignore next */
(cov_12k33bco6g().b[24][1]++, exports.UncertaintyLevel = UncertaintyLevel = {}));
var EmissionUnits;
/* istanbul ignore next */
cov_12k33bco6g().s[99]++;
(function (EmissionUnits) {
  /* istanbul ignore next */
  cov_12k33bco6g().f[18]++;
  cov_12k33bco6g().s[100]++;
  EmissionUnits["KG_CO2E"] = "kg_CO2e";
  /* istanbul ignore next */
  cov_12k33bco6g().s[101]++;
  EmissionUnits["TONS_CO2E"] = "tons_CO2e";
  /* istanbul ignore next */
  cov_12k33bco6g().s[102]++;
  EmissionUnits["LBS_CO2E"] = "lbs_CO2e";
})(
/* istanbul ignore next */
(cov_12k33bco6g().b[25][0]++, EmissionUnits) ||
/* istanbul ignore next */
(cov_12k33bco6g().b[25][1]++, exports.EmissionUnits = EmissionUnits = {}));
var EmissionScope;
/* istanbul ignore next */
cov_12k33bco6g().s[103]++;
(function (EmissionScope) {
  /* istanbul ignore next */
  cov_12k33bco6g().f[19]++;
  cov_12k33bco6g().s[104]++;
  EmissionScope["SCOPE_1"] = "scope_1";
  /* istanbul ignore next */
  cov_12k33bco6g().s[105]++;
  EmissionScope["SCOPE_2"] = "scope_2";
  /* istanbul ignore next */
  cov_12k33bco6g().s[106]++;
  EmissionScope["SCOPE_3"] = "scope_3"; // Other indirect emissions
})(
/* istanbul ignore next */
(cov_12k33bco6g().b[26][0]++, EmissionScope) ||
/* istanbul ignore next */
(cov_12k33bco6g().b[26][1]++, exports.EmissionScope = EmissionScope = {}));
var StandardType;
/* istanbul ignore next */
cov_12k33bco6g().s[107]++;
(function (StandardType) {
  /* istanbul ignore next */
  cov_12k33bco6g().f[20]++;
  cov_12k33bco6g().s[108]++;
  StandardType["SMACNA"] = "smacna";
  /* istanbul ignore next */
  cov_12k33bco6g().s[109]++;
  StandardType["ASHRAE"] = "ashrae";
  /* istanbul ignore next */
  cov_12k33bco6g().s[110]++;
  StandardType["LOCAL_CODE"] = "local_code";
  /* istanbul ignore next */
  cov_12k33bco6g().s[111]++;
  StandardType["INTERNATIONAL_CODE"] = "international_code";
  /* istanbul ignore next */
  cov_12k33bco6g().s[112]++;
  StandardType["INDUSTRY_STANDARD"] = "industry_standard";
  /* istanbul ignore next */
  cov_12k33bco6g().s[113]++;
  StandardType["GREEN_BUILDING"] = "green_building";
})(
/* istanbul ignore next */
(cov_12k33bco6g().b[27][0]++, StandardType) ||
/* istanbul ignore next */
(cov_12k33bco6g().b[27][1]++, exports.StandardType = StandardType = {}));
var ComplianceStatus;
/* istanbul ignore next */
cov_12k33bco6g().s[114]++;
(function (ComplianceStatus) {
  /* istanbul ignore next */
  cov_12k33bco6g().f[21]++;
  cov_12k33bco6g().s[115]++;
  ComplianceStatus["COMPLIANT"] = "compliant";
  /* istanbul ignore next */
  cov_12k33bco6g().s[116]++;
  ComplianceStatus["NON_COMPLIANT"] = "non_compliant";
  /* istanbul ignore next */
  cov_12k33bco6g().s[117]++;
  ComplianceStatus["MARGINAL"] = "marginal";
  /* istanbul ignore next */
  cov_12k33bco6g().s[118]++;
  ComplianceStatus["NOT_APPLICABLE"] = "not_applicable";
  /* istanbul ignore next */
  cov_12k33bco6g().s[119]++;
  ComplianceStatus["REQUIRES_REVIEW"] = "requires_review";
})(
/* istanbul ignore next */
(cov_12k33bco6g().b[28][0]++, ComplianceStatus) ||
/* istanbul ignore next */
(cov_12k33bco6g().b[28][1]++, exports.ComplianceStatus = ComplianceStatus = {}));
var TimeUnits;
/* istanbul ignore next */
cov_12k33bco6g().s[120]++;
(function (TimeUnits) {
  /* istanbul ignore next */
  cov_12k33bco6g().f[22]++;
  cov_12k33bco6g().s[121]++;
  TimeUnits["SECONDS"] = "seconds";
  /* istanbul ignore next */
  cov_12k33bco6g().s[122]++;
  TimeUnits["MINUTES"] = "minutes";
  /* istanbul ignore next */
  cov_12k33bco6g().s[123]++;
  TimeUnits["HOURS"] = "hours";
  /* istanbul ignore next */
  cov_12k33bco6g().s[124]++;
  TimeUnits["DAYS"] = "days";
  /* istanbul ignore next */
  cov_12k33bco6g().s[125]++;
  TimeUnits["WEEKS"] = "weeks";
  /* istanbul ignore next */
  cov_12k33bco6g().s[126]++;
  TimeUnits["MONTHS"] = "months";
  /* istanbul ignore next */
  cov_12k33bco6g().s[127]++;
  TimeUnits["YEARS"] = "years";
})(
/* istanbul ignore next */
(cov_12k33bco6g().b[29][0]++, TimeUnits) ||
/* istanbul ignore next */
(cov_12k33bco6g().b[29][1]++, exports.TimeUnits = TimeUnits = {}));
var DistributionType;
/* istanbul ignore next */
cov_12k33bco6g().s[128]++;
(function (DistributionType) {
  /* istanbul ignore next */
  cov_12k33bco6g().f[23]++;
  cov_12k33bco6g().s[129]++;
  DistributionType["NORMAL"] = "normal";
  /* istanbul ignore next */
  cov_12k33bco6g().s[130]++;
  DistributionType["UNIFORM"] = "uniform";
  /* istanbul ignore next */
  cov_12k33bco6g().s[131]++;
  DistributionType["TRIANGULAR"] = "triangular";
  /* istanbul ignore next */
  cov_12k33bco6g().s[132]++;
  DistributionType["LOGNORMAL"] = "lognormal";
})(
/* istanbul ignore next */
(cov_12k33bco6g().b[30][0]++, DistributionType) ||
/* istanbul ignore next */
(cov_12k33bco6g().b[30][1]++, exports.DistributionType = DistributionType = {}));
var SystemType;
/* istanbul ignore next */
cov_12k33bco6g().s[133]++;
(function (SystemType) {
  /* istanbul ignore next */
  cov_12k33bco6g().f[24]++;
  cov_12k33bco6g().s[134]++;
  SystemType["SUPPLY_AIR"] = "supply_air";
  /* istanbul ignore next */
  cov_12k33bco6g().s[135]++;
  SystemType["RETURN_AIR"] = "return_air";
  /* istanbul ignore next */
  cov_12k33bco6g().s[136]++;
  SystemType["EXHAUST_AIR"] = "exhaust_air";
  /* istanbul ignore next */
  cov_12k33bco6g().s[137]++;
  SystemType["MIXED_AIR"] = "mixed_air";
  /* istanbul ignore next */
  cov_12k33bco6g().s[138]++;
  SystemType["DEDICATED_OUTDOOR_AIR"] = "dedicated_outdoor_air";
})(
/* istanbul ignore next */
(cov_12k33bco6g().b[31][0]++, SystemType) ||
/* istanbul ignore next */
(cov_12k33bco6g().b[31][1]++, exports.SystemType = SystemType = {}));
var RecommendationType;
/* istanbul ignore next */
cov_12k33bco6g().s[139]++;
(function (RecommendationType) {
  /* istanbul ignore next */
  cov_12k33bco6g().f[25]++;
  cov_12k33bco6g().s[140]++;
  RecommendationType["OPTIMIZATION"] = "optimization";
  /* istanbul ignore next */
  cov_12k33bco6g().s[141]++;
  RecommendationType["MAINTENANCE"] = "maintenance";
  /* istanbul ignore next */
  cov_12k33bco6g().s[142]++;
  RecommendationType["UPGRADE"] = "upgrade";
  /* istanbul ignore next */
  cov_12k33bco6g().s[143]++;
  RecommendationType["OPERATIONAL_CHANGE"] = "operational_change";
  /* istanbul ignore next */
  cov_12k33bco6g().s[144]++;
  RecommendationType["DESIGN_MODIFICATION"] = "design_modification";
})(
/* istanbul ignore next */
(cov_12k33bco6g().b[32][0]++, RecommendationType) ||
/* istanbul ignore next */
(cov_12k33bco6g().b[32][1]++, exports.RecommendationType = RecommendationType = {}));
var RecommendationPriority;
/* istanbul ignore next */
cov_12k33bco6g().s[145]++;
(function (RecommendationPriority) {
  /* istanbul ignore next */
  cov_12k33bco6g().f[26]++;
  cov_12k33bco6g().s[146]++;
  RecommendationPriority["CRITICAL"] = "critical";
  /* istanbul ignore next */
  cov_12k33bco6g().s[147]++;
  RecommendationPriority["HIGH"] = "high";
  /* istanbul ignore next */
  cov_12k33bco6g().s[148]++;
  RecommendationPriority["MEDIUM"] = "medium";
  /* istanbul ignore next */
  cov_12k33bco6g().s[149]++;
  RecommendationPriority["LOW"] = "low";
})(
/* istanbul ignore next */
(cov_12k33bco6g().b[33][0]++, RecommendationPriority) ||
/* istanbul ignore next */
(cov_12k33bco6g().b[33][1]++, exports.RecommendationPriority = RecommendationPriority = {}));
var ImplementationComplexity;
/* istanbul ignore next */
cov_12k33bco6g().s[150]++;
(function (ImplementationComplexity) {
  /* istanbul ignore next */
  cov_12k33bco6g().f[27]++;
  cov_12k33bco6g().s[151]++;
  ImplementationComplexity["SIMPLE"] = "simple";
  /* istanbul ignore next */
  cov_12k33bco6g().s[152]++;
  ImplementationComplexity["MODERATE"] = "moderate";
  /* istanbul ignore next */
  cov_12k33bco6g().s[153]++;
  ImplementationComplexity["COMPLEX"] = "complex";
  /* istanbul ignore next */
  cov_12k33bco6g().s[154]++;
  ImplementationComplexity["VERY_COMPLEX"] = "very_complex";
})(
/* istanbul ignore next */
(cov_12k33bco6g().b[34][0]++, ImplementationComplexity) ||
/* istanbul ignore next */
(cov_12k33bco6g().b[34][1]++, exports.ImplementationComplexity = ImplementationComplexity = {}));
var BenchmarkType;
/* istanbul ignore next */
cov_12k33bco6g().s[155]++;
(function (BenchmarkType) {
  /* istanbul ignore next */
  cov_12k33bco6g().f[28]++;
  cov_12k33bco6g().s[156]++;
  BenchmarkType["INDUSTRY_AVERAGE"] = "industry_average";
  /* istanbul ignore next */
  cov_12k33bco6g().s[157]++;
  BenchmarkType["BEST_IN_CLASS"] = "best_in_class";
  /* istanbul ignore next */
  cov_12k33bco6g().s[158]++;
  BenchmarkType["REGULATORY_MINIMUM"] = "regulatory_minimum";
  /* istanbul ignore next */
  cov_12k33bco6g().s[159]++;
  BenchmarkType["DESIGN_TARGET"] = "design_target";
  /* istanbul ignore next */
  cov_12k33bco6g().s[160]++;
  BenchmarkType["HISTORICAL_PERFORMANCE"] = "historical_performance";
})(
/* istanbul ignore next */
(cov_12k33bco6g().b[35][0]++, BenchmarkType) ||
/* istanbul ignore next */
(cov_12k33bco6g().b[35][1]++, exports.BenchmarkType = BenchmarkType = {}));
var SystemSize;
/* istanbul ignore next */
cov_12k33bco6g().s[161]++;
(function (SystemSize) {
  /* istanbul ignore next */
  cov_12k33bco6g().f[29]++;
  cov_12k33bco6g().s[162]++;
  SystemSize["SMALL"] = "small";
  /* istanbul ignore next */
  cov_12k33bco6g().s[163]++;
  SystemSize["MEDIUM"] = "medium";
  /* istanbul ignore next */
  cov_12k33bco6g().s[164]++;
  SystemSize["LARGE"] = "large";
  /* istanbul ignore next */
  cov_12k33bco6g().s[165]++;
  SystemSize["EXTRA_LARGE"] = "extra_large";
})(
/* istanbul ignore next */
(cov_12k33bco6g().b[36][0]++, SystemSize) ||
/* istanbul ignore next */
(cov_12k33bco6g().b[36][1]++, exports.SystemSize = SystemSize = {}));
var BuildingType;
/* istanbul ignore next */
cov_12k33bco6g().s[166]++;
(function (BuildingType) {
  /* istanbul ignore next */
  cov_12k33bco6g().f[30]++;
  cov_12k33bco6g().s[167]++;
  BuildingType["OFFICE"] = "office";
  /* istanbul ignore next */
  cov_12k33bco6g().s[168]++;
  BuildingType["RETAIL"] = "retail";
  /* istanbul ignore next */
  cov_12k33bco6g().s[169]++;
  BuildingType["HEALTHCARE"] = "healthcare";
  /* istanbul ignore next */
  cov_12k33bco6g().s[170]++;
  BuildingType["EDUCATION"] = "education";
  /* istanbul ignore next */
  cov_12k33bco6g().s[171]++;
  BuildingType["INDUSTRIAL"] = "industrial";
  /* istanbul ignore next */
  cov_12k33bco6g().s[172]++;
  BuildingType["RESIDENTIAL"] = "residential";
  /* istanbul ignore next */
  cov_12k33bco6g().s[173]++;
  BuildingType["HOSPITALITY"] = "hospitality";
  /* istanbul ignore next */
  cov_12k33bco6g().s[174]++;
  BuildingType["OTHER"] = "other";
})(
/* istanbul ignore next */
(cov_12k33bco6g().b[37][0]++, BuildingType) ||
/* istanbul ignore next */
(cov_12k33bco6g().b[37][1]++, exports.BuildingType = BuildingType = {}));
var EfficiencyCalculationMethod;
/* istanbul ignore next */
cov_12k33bco6g().s[175]++;
(function (EfficiencyCalculationMethod) {
  /* istanbul ignore next */
  cov_12k33bco6g().f[31]++;
  cov_12k33bco6g().s[176]++;
  EfficiencyCalculationMethod["MEASURED"] = "measured";
  /* istanbul ignore next */
  cov_12k33bco6g().s[177]++;
  EfficiencyCalculationMethod["CALCULATED"] = "calculated";
  /* istanbul ignore next */
  cov_12k33bco6g().s[178]++;
  EfficiencyCalculationMethod["ESTIMATED"] = "estimated";
  /* istanbul ignore next */
  cov_12k33bco6g().s[179]++;
  EfficiencyCalculationMethod["MANUFACTURER_RATED"] = "manufacturer_rated";
})(
/* istanbul ignore next */
(cov_12k33bco6g().b[38][0]++, EfficiencyCalculationMethod) ||
/* istanbul ignore next */
(cov_12k33bco6g().b[38][1]++, exports.EfficiencyCalculationMethod = EfficiencyCalculationMethod = {}));
var ComponentType;
/* istanbul ignore next */
cov_12k33bco6g().s[180]++;
(function (ComponentType) {
  /* istanbul ignore next */
  cov_12k33bco6g().f[32]++;
  cov_12k33bco6g().s[181]++;
  ComponentType["FAN"] = "fan";
  /* istanbul ignore next */
  cov_12k33bco6g().s[182]++;
  ComponentType["MOTOR"] = "motor";
  /* istanbul ignore next */
  cov_12k33bco6g().s[183]++;
  ComponentType["VFD"] = "vfd";
  /* istanbul ignore next */
  cov_12k33bco6g().s[184]++;
  ComponentType["DAMPER"] = "damper";
  /* istanbul ignore next */
  cov_12k33bco6g().s[185]++;
  ComponentType["FILTER"] = "filter";
  /* istanbul ignore next */
  cov_12k33bco6g().s[186]++;
  ComponentType["COIL"] = "coil";
  /* istanbul ignore next */
  cov_12k33bco6g().s[187]++;
  ComponentType["DUCTWORK"] = "ductwork";
})(
/* istanbul ignore next */
(cov_12k33bco6g().b[39][0]++, ComponentType) ||
/* istanbul ignore next */
(cov_12k33bco6g().b[39][1]++, exports.ComponentType = ComponentType = {}));
var MaintenanceStatus;
/* istanbul ignore next */
cov_12k33bco6g().s[188]++;
(function (MaintenanceStatus) {
  /* istanbul ignore next */
  cov_12k33bco6g().f[33]++;
  cov_12k33bco6g().s[189]++;
  MaintenanceStatus["EXCELLENT"] = "excellent";
  /* istanbul ignore next */
  cov_12k33bco6g().s[190]++;
  MaintenanceStatus["GOOD"] = "good";
  /* istanbul ignore next */
  cov_12k33bco6g().s[191]++;
  MaintenanceStatus["FAIR"] = "fair";
  /* istanbul ignore next */
  cov_12k33bco6g().s[192]++;
  MaintenanceStatus["POOR"] = "poor";
  /* istanbul ignore next */
  cov_12k33bco6g().s[193]++;
  MaintenanceStatus["CRITICAL"] = "critical";
})(
/* istanbul ignore next */
(cov_12k33bco6g().b[40][0]++, MaintenanceStatus) ||
/* istanbul ignore next */
(cov_12k33bco6g().b[40][1]++, exports.MaintenanceStatus = MaintenanceStatus = {}));
var ModelType;
/* istanbul ignore next */
cov_12k33bco6g().s[194]++;
(function (ModelType) {
  /* istanbul ignore next */
  cov_12k33bco6g().f[34]++;
  cov_12k33bco6g().s[195]++;
  ModelType["LINEAR_REGRESSION"] = "linear_regression";
  /* istanbul ignore next */
  cov_12k33bco6g().s[196]++;
  ModelType["POLYNOMIAL_REGRESSION"] = "polynomial_regression";
  /* istanbul ignore next */
  cov_12k33bco6g().s[197]++;
  ModelType["TIME_SERIES"] = "time_series";
  /* istanbul ignore next */
  cov_12k33bco6g().s[198]++;
  ModelType["MACHINE_LEARNING"] = "machine_learning";
  /* istanbul ignore next */
  cov_12k33bco6g().s[199]++;
  ModelType["PHYSICS_BASED"] = "physics_based";
})(
/* istanbul ignore next */
(cov_12k33bco6g().b[41][0]++, ModelType) ||
/* istanbul ignore next */
(cov_12k33bco6g().b[41][1]++, exports.ModelType = ModelType = {}));
var AnomalyType;
/* istanbul ignore next */
cov_12k33bco6g().s[200]++;
(function (AnomalyType) {
  /* istanbul ignore next */
  cov_12k33bco6g().f[35]++;
  cov_12k33bco6g().s[201]++;
  AnomalyType["SUDDEN_CHANGE"] = "sudden_change";
  /* istanbul ignore next */
  cov_12k33bco6g().s[202]++;
  AnomalyType["GRADUAL_DRIFT"] = "gradual_drift";
  /* istanbul ignore next */
  cov_12k33bco6g().s[203]++;
  AnomalyType["CYCLIC_ANOMALY"] = "cyclic_anomaly";
  /* istanbul ignore next */
  cov_12k33bco6g().s[204]++;
  AnomalyType["OUTLIER"] = "outlier";
  /* istanbul ignore next */
  cov_12k33bco6g().s[205]++;
  AnomalyType["PATTERN_BREAK"] = "pattern_break";
})(
/* istanbul ignore next */
(cov_12k33bco6g().b[42][0]++, AnomalyType) ||
/* istanbul ignore next */
(cov_12k33bco6g().b[42][1]++, exports.AnomalyType = AnomalyType = {}));
var AnomalySeverity;
/* istanbul ignore next */
cov_12k33bco6g().s[206]++;
(function (AnomalySeverity) {
  /* istanbul ignore next */
  cov_12k33bco6g().f[36]++;
  cov_12k33bco6g().s[207]++;
  AnomalySeverity["CRITICAL"] = "critical";
  /* istanbul ignore next */
  cov_12k33bco6g().s[208]++;
  AnomalySeverity["HIGH"] = "high";
  /* istanbul ignore next */
  cov_12k33bco6g().s[209]++;
  AnomalySeverity["MEDIUM"] = "medium";
  /* istanbul ignore next */
  cov_12k33bco6g().s[210]++;
  AnomalySeverity["LOW"] = "low";
})(
/* istanbul ignore next */
(cov_12k33bco6g().b[43][0]++, AnomalySeverity) ||
/* istanbul ignore next */
(cov_12k33bco6g().b[43][1]++, exports.AnomalySeverity = AnomalySeverity = {}));
var CauseCategory;
/* istanbul ignore next */
cov_12k33bco6g().s[211]++;
(function (CauseCategory) {
  /* istanbul ignore next */
  cov_12k33bco6g().f[37]++;
  cov_12k33bco6g().s[212]++;
  CauseCategory["EQUIPMENT_FAILURE"] = "equipment_failure";
  /* istanbul ignore next */
  cov_12k33bco6g().s[213]++;
  CauseCategory["MAINTENANCE_ISSUE"] = "maintenance_issue";
  /* istanbul ignore next */
  cov_12k33bco6g().s[214]++;
  CauseCategory["OPERATIONAL_CHANGE"] = "operational_change";
  /* istanbul ignore next */
  cov_12k33bco6g().s[215]++;
  CauseCategory["ENVIRONMENTAL_FACTOR"] = "environmental_factor";
  /* istanbul ignore next */
  cov_12k33bco6g().s[216]++;
  CauseCategory["CONTROL_SYSTEM"] = "control_system";
  /* istanbul ignore next */
  cov_12k33bco6g().s[217]++;
  CauseCategory["UNKNOWN"] = "unknown";
})(
/* istanbul ignore next */
(cov_12k33bco6g().b[44][0]++, CauseCategory) ||
/* istanbul ignore next */
(cov_12k33bco6g().b[44][1]++, exports.CauseCategory = CauseCategory = {}));
// Export all types for external use
/* istanbul ignore next */
cov_12k33bco6g().s[218]++;
__exportStar(require("./SystemOptimizationTypes"), exports);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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