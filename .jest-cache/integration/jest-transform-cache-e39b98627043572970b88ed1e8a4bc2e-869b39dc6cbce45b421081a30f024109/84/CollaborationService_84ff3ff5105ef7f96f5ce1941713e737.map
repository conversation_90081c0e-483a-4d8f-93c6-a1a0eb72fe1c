{"version": 3, "names": ["cov_4djw3z3h9", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "name", "decl", "loc", "branchMap", "type", "locations", "undefined", "s", "f", "b", "inputSourceMap", "file", "mappings", "names", "sources", "sourcesContent", "version", "_coverageSchema", "coverage", "actualCoverage", "exports", "getCollaborationService", "socket_io_client_1", "require", "uuid_1", "CollaborationService", "constructor", "socket", "currentUser", "operationQueue", "eventListeners", "Map", "reconnectAttempts", "maxReconnectAttempts", "heartbeatInterval", "state", "isConnected", "activeUsers", "pendingOperations", "localOperations", "isReconnecting", "lastSyncTime", "Date", "transformationEngine", "OperationalTransform", "setupEventHandlers", "initialize", "user", "serverUrl", "io", "process", "env", "NEXT_PUBLIC_COLLABORATION_SERVER", "auth", "userId", "id", "token", "getAuthToken", "transports", "timeout", "reconnection", "reconnectionAttempts", "reconnectionDelay", "setupSocketEventHandlers", "startHeartbeat", "error", "console", "joinDocument", "documentId", "projectId", "Error", "Promise", "resolve", "reject", "emit", "response", "success", "document", "currentDocument", "participants", "filter", "u", "isOnline", "leaveDocument", "syncPendingOperations", "applyOperation", "operation", "fullOperation", "v4", "timestamp", "push", "sendOperation", "updateCursor", "position", "cursor", "lockDocument", "lock", "isLocked", "locked<PERSON>y", "locked", "getCollaborationStats", "length", "totalOperations", "on", "event", "callback", "has", "set", "get", "off", "listeners", "index", "indexOf", "splice", "data", "for<PERSON>ach", "handleRemoteOperation", "find", "findIndex", "op", "transformedOperation", "transform", "operations", "addEventListener", "hidden", "updateUserStatus", "window", "isActive", "setInterval", "localStorage", "getItem", "destroy", "clearInterval", "disconnect", "clear", "transformedOp", "localOp", "transformAgainst", "op1", "op2", "elementId", "transformSameElement", "x", "y", "collaborationService", "default"], "sources": ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\frontend\\lib\\services\\CollaborationService.ts"], "sourcesContent": ["/**\r\n * Real-time Collaboration Service for SizeWise Suite\r\n * \r\n * Implements WebSocket-based collaboration with operational transformation\r\n * and conflict resolution for multi-user HVAC design workflows.\r\n * \r\n * Features:\r\n * - Real-time document synchronization\r\n * - Operational transformation for conflict resolution\r\n * - User presence and cursor tracking\r\n * - Collaborative editing with undo/redo\r\n * - Permission-based access control\r\n * - Offline-first with sync on reconnection\r\n */\r\n\r\nimport { io, Socket } from 'socket.io-client';\r\nimport { v4 as uuidv4 } from 'uuid';\r\n\r\n// Types for collaboration\r\nexport interface CollaborationUser {\r\n  id: string;\r\n  name: string;\r\n  email: string;\r\n  avatar?: string;\r\n  color: string;\r\n  cursor?: {\r\n    x: number;\r\n    y: number;\r\n    elementId?: string;\r\n  };\r\n  lastSeen: Date;\r\n  isOnline: boolean;\r\n}\r\n\r\nexport interface Operation {\r\n  id: string;\r\n  type: 'insert' | 'delete' | 'update' | 'move' | 'style';\r\n  userId: string;\r\n  timestamp: Date;\r\n  elementId: string;\r\n  path: string[];\r\n  oldValue?: any;\r\n  newValue?: any;\r\n  position?: { x: number; y: number };\r\n  metadata?: Record<string, any>;\r\n}\r\n\r\nexport interface CollaborationDocument {\r\n  id: string;\r\n  projectId: string;\r\n  type: 'hvac_design' | 'calculation' | 'report';\r\n  version: number;\r\n  operations: Operation[];\r\n  participants: CollaborationUser[];\r\n  permissions: Record<string, 'read' | 'write' | 'admin'>;\r\n  lastModified: Date;\r\n  isLocked?: boolean;\r\n  lockedBy?: string;\r\n}\r\n\r\nexport interface CollaborationState {\r\n  isConnected: boolean;\r\n  currentDocument?: CollaborationDocument;\r\n  activeUsers: CollaborationUser[];\r\n  pendingOperations: Operation[];\r\n  localOperations: Operation[];\r\n  isReconnecting: boolean;\r\n  lastSyncTime: Date;\r\n}\r\n\r\nexport class CollaborationService {\r\n  private socket: Socket | null = null;\r\n  private currentUser: CollaborationUser | null = null;\r\n  private state: CollaborationState;\r\n  private operationQueue: Operation[] = [];\r\n  private transformationEngine: OperationalTransform;\r\n  private eventListeners: Map<string, Function[]> = new Map();\r\n  private reconnectAttempts = 0;\r\n  private maxReconnectAttempts = 5;\r\n  private heartbeatInterval: NodeJS.Timeout | null = null;\r\n\r\n  constructor() {\r\n    this.state = {\r\n      isConnected: false,\r\n      activeUsers: [],\r\n      pendingOperations: [],\r\n      localOperations: [],\r\n      isReconnecting: false,\r\n      lastSyncTime: new Date()\r\n    };\r\n    \r\n    this.transformationEngine = new OperationalTransform();\r\n    this.setupEventHandlers();\r\n  }\r\n\r\n  /**\r\n   * Initialize collaboration service with user authentication\r\n   */\r\n  async initialize(user: CollaborationUser, serverUrl?: string): Promise<void> {\r\n    try {\r\n      this.currentUser = user;\r\n      \r\n      // Initialize WebSocket connection\r\n      this.socket = io(serverUrl || process.env.NEXT_PUBLIC_COLLABORATION_SERVER || 'ws://localhost:3001', {\r\n        auth: {\r\n          userId: user.id,\r\n          token: await this.getAuthToken()\r\n        },\r\n        transports: ['websocket', 'polling'],\r\n        timeout: 10000,\r\n        reconnection: true,\r\n        reconnectionAttempts: this.maxReconnectAttempts,\r\n        reconnectionDelay: 1000\r\n      });\r\n\r\n      this.setupSocketEventHandlers();\r\n      \r\n      // Start heartbeat\r\n      this.startHeartbeat();\r\n      \r\n    } catch (error) {\r\n      console.error('Failed to initialize collaboration service:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Join a collaborative document session\r\n   */\r\n  async joinDocument(documentId: string, projectId: string): Promise<CollaborationDocument> {\r\n    if (!this.socket || !this.currentUser) {\r\n      throw new Error('Collaboration service not initialized');\r\n    }\r\n\r\n    return new Promise((resolve, reject) => {\r\n      this.socket!.emit('join_document', {\r\n        documentId,\r\n        projectId,\r\n        user: this.currentUser\r\n      }, (response: { success: boolean; document?: CollaborationDocument; error?: string }) => {\r\n        if (response.success && response.document) {\r\n          this.state.currentDocument = response.document;\r\n          this.state.activeUsers = response.document.participants.filter(u => u.isOnline);\r\n          this.emit('document_joined', response.document);\r\n          resolve(response.document);\r\n        } else {\r\n          reject(new Error(response.error || 'Failed to join document'));\r\n        }\r\n      });\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Leave current document session\r\n   */\r\n  async leaveDocument(): Promise<void> {\r\n    if (!this.socket || !this.state.currentDocument) {\r\n      return;\r\n    }\r\n\r\n    // Sync any pending operations before leaving\r\n    await this.syncPendingOperations();\r\n\r\n    this.socket.emit('leave_document', {\r\n      documentId: this.state.currentDocument.id,\r\n      userId: this.currentUser?.id\r\n    });\r\n\r\n    this.state.currentDocument = undefined;\r\n    this.state.activeUsers = [];\r\n    this.operationQueue = [];\r\n    this.emit('document_left');\r\n  }\r\n\r\n  /**\r\n   * Apply a local operation and broadcast to other users\r\n   */\r\n  async applyOperation(operation: Omit<Operation, 'id' | 'userId' | 'timestamp'>): Promise<void> {\r\n    if (!this.currentUser || !this.state.currentDocument) {\r\n      throw new Error('No active collaboration session');\r\n    }\r\n\r\n    const fullOperation: Operation = {\r\n      ...operation,\r\n      id: uuidv4(),\r\n      userId: this.currentUser.id,\r\n      timestamp: new Date()\r\n    };\r\n\r\n    // Apply operation locally first\r\n    this.state.localOperations.push(fullOperation);\r\n    this.emit('operation_applied', fullOperation);\r\n\r\n    // Queue for transmission\r\n    this.operationQueue.push(fullOperation);\r\n\r\n    // Send immediately if connected, otherwise queue for later\r\n    if (this.state.isConnected) {\r\n      await this.sendOperation(fullOperation);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update user cursor position\r\n   */\r\n  updateCursor(position: { x: number; y: number; elementId?: string }): void {\r\n    if (!this.currentUser || !this.socket) {\r\n      return;\r\n    }\r\n\r\n    this.currentUser.cursor = position;\r\n    \r\n    this.socket.emit('cursor_update', {\r\n      documentId: this.state.currentDocument?.id,\r\n      userId: this.currentUser.id,\r\n      cursor: position\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Lock/unlock document for exclusive editing\r\n   */\r\n  async lockDocument(lock: boolean): Promise<boolean> {\r\n    if (!this.socket || !this.state.currentDocument || !this.currentUser) {\r\n      return false;\r\n    }\r\n\r\n    return new Promise((resolve) => {\r\n      this.socket!.emit('document_lock', {\r\n        documentId: this.state.currentDocument!.id,\r\n        userId: this.currentUser!.id,\r\n        lock\r\n      }, (response: { success: boolean }) => {\r\n        if (response.success) {\r\n          this.state.currentDocument!.isLocked = lock;\r\n          this.state.currentDocument!.lockedBy = lock ? this.currentUser!.id : undefined;\r\n          this.emit('document_lock_changed', { locked: lock, userId: this.currentUser!.id });\r\n        }\r\n        resolve(response.success);\r\n      });\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Get collaboration statistics\r\n   */\r\n  getCollaborationStats(): {\r\n    activeUsers: number;\r\n    totalOperations: number;\r\n    pendingOperations: number;\r\n    lastSyncTime: Date;\r\n    isConnected: boolean;\r\n  } {\r\n    return {\r\n      activeUsers: this.state.activeUsers.length,\r\n      totalOperations: this.state.localOperations.length,\r\n      pendingOperations: this.operationQueue.length,\r\n      lastSyncTime: this.state.lastSyncTime,\r\n      isConnected: this.state.isConnected\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Event listener management\r\n   */\r\n  on(event: string, callback: Function): void {\r\n    if (!this.eventListeners.has(event)) {\r\n      this.eventListeners.set(event, []);\r\n    }\r\n    this.eventListeners.get(event)!.push(callback);\r\n  }\r\n\r\n  off(event: string, callback: Function): void {\r\n    const listeners = this.eventListeners.get(event);\r\n    if (listeners) {\r\n      const index = listeners.indexOf(callback);\r\n      if (index > -1) {\r\n        listeners.splice(index, 1);\r\n      }\r\n    }\r\n  }\r\n\r\n  private emit(event: string, data?: any): void {\r\n    const listeners = this.eventListeners.get(event);\r\n    if (listeners) {\r\n      listeners.forEach(callback => callback(data));\r\n    }\r\n  }\r\n\r\n  private setupSocketEventHandlers(): void {\r\n    if (!this.socket) return;\r\n\r\n    this.socket.on('connect', () => {\r\n      this.state.isConnected = true;\r\n      this.state.isReconnecting = false;\r\n      this.reconnectAttempts = 0;\r\n      this.emit('connected');\r\n      \r\n      // Sync pending operations\r\n      this.syncPendingOperations();\r\n    });\r\n\r\n    this.socket.on('disconnect', () => {\r\n      this.state.isConnected = false;\r\n      this.emit('disconnected');\r\n    });\r\n\r\n    this.socket.on('reconnect_attempt', () => {\r\n      this.state.isReconnecting = true;\r\n      this.reconnectAttempts++;\r\n      this.emit('reconnecting', this.reconnectAttempts);\r\n    });\r\n\r\n    this.socket.on('operation_received', (operation: Operation) => {\r\n      this.handleRemoteOperation(operation);\r\n    });\r\n\r\n    this.socket.on('user_joined', (user: CollaborationUser) => {\r\n      this.state.activeUsers.push(user);\r\n      this.emit('user_joined', user);\r\n    });\r\n\r\n    this.socket.on('user_left', (userId: string) => {\r\n      this.state.activeUsers = this.state.activeUsers.filter(u => u.id !== userId);\r\n      this.emit('user_left', userId);\r\n    });\r\n\r\n    this.socket.on('cursor_updated', (data: { userId: string; cursor: any }) => {\r\n      const user = this.state.activeUsers.find(u => u.id === data.userId);\r\n      if (user) {\r\n        user.cursor = data.cursor;\r\n        this.emit('cursor_updated', data);\r\n      }\r\n    });\r\n\r\n    this.socket.on('document_locked', (data: { locked: boolean; userId: string }) => {\r\n      if (this.state.currentDocument) {\r\n        this.state.currentDocument.isLocked = data.locked;\r\n        this.state.currentDocument.lockedBy = data.locked ? data.userId : undefined;\r\n        this.emit('document_lock_changed', data);\r\n      }\r\n    });\r\n  }\r\n\r\n  private async sendOperation(operation: Operation): Promise<void> {\r\n    if (!this.socket || !this.state.currentDocument) {\r\n      return;\r\n    }\r\n\r\n    this.socket.emit('operation', {\r\n      documentId: this.state.currentDocument.id,\r\n      operation\r\n    }, (response: { success: boolean; error?: string }) => {\r\n      if (response.success) {\r\n        // Remove from queue\r\n        const index = this.operationQueue.findIndex(op => op.id === operation.id);\r\n        if (index > -1) {\r\n          this.operationQueue.splice(index, 1);\r\n        }\r\n        this.state.lastSyncTime = new Date();\r\n      } else {\r\n        console.error('Failed to send operation:', response.error);\r\n      }\r\n    });\r\n  }\r\n\r\n  private handleRemoteOperation(operation: Operation): void {\r\n    // Transform operation against local operations\r\n    const transformedOperation = this.transformationEngine.transform(\r\n      operation,\r\n      this.state.localOperations\r\n    );\r\n\r\n    // Apply transformed operation\r\n    this.emit('remote_operation', transformedOperation);\r\n    \r\n    // Update document version\r\n    if (this.state.currentDocument) {\r\n      this.state.currentDocument.version++;\r\n      this.state.currentDocument.operations.push(transformedOperation);\r\n    }\r\n  }\r\n\r\n  private async syncPendingOperations(): Promise<void> {\r\n    const operations = [...this.operationQueue];\r\n    for (const operation of operations) {\r\n      await this.sendOperation(operation);\r\n    }\r\n  }\r\n\r\n  private setupEventHandlers(): void {\r\n    // Handle page visibility changes\r\n    document.addEventListener('visibilitychange', () => {\r\n      if (document.hidden) {\r\n        this.updateUserStatus(false);\r\n      } else {\r\n        this.updateUserStatus(true);\r\n      }\r\n    });\r\n\r\n    // Handle beforeunload\r\n    window.addEventListener('beforeunload', () => {\r\n      this.leaveDocument();\r\n    });\r\n  }\r\n\r\n  private updateUserStatus(isActive: boolean): void {\r\n    if (this.socket && this.currentUser) {\r\n      this.socket.emit('user_status', {\r\n        userId: this.currentUser.id,\r\n        isActive\r\n      });\r\n    }\r\n  }\r\n\r\n  private startHeartbeat(): void {\r\n    this.heartbeatInterval = setInterval(() => {\r\n      if (this.socket && this.state.isConnected) {\r\n        this.socket.emit('heartbeat', {\r\n          userId: this.currentUser?.id,\r\n          timestamp: new Date()\r\n        });\r\n      }\r\n    }, 30000); // 30 seconds\r\n  }\r\n\r\n  private async getAuthToken(): Promise<string> {\r\n    // Implementation depends on your auth system\r\n    return localStorage.getItem('auth_token') || '';\r\n  }\r\n\r\n  /**\r\n   * Cleanup resources\r\n   */\r\n  destroy(): void {\r\n    if (this.heartbeatInterval) {\r\n      clearInterval(this.heartbeatInterval);\r\n    }\r\n    \r\n    if (this.socket) {\r\n      this.socket.disconnect();\r\n    }\r\n    \r\n    this.eventListeners.clear();\r\n  }\r\n}\r\n\r\n/**\r\n * Operational Transformation Engine\r\n * Handles conflict resolution for concurrent operations\r\n */\r\nclass OperationalTransform {\r\n  transform(operation: Operation, localOperations: Operation[]): Operation {\r\n    let transformedOp = { ...operation };\r\n    \r\n    // Apply transformation rules based on operation types\r\n    for (const localOp of localOperations) {\r\n      if (localOp.timestamp > operation.timestamp) {\r\n        transformedOp = this.transformAgainst(transformedOp, localOp);\r\n      }\r\n    }\r\n    \r\n    return transformedOp;\r\n  }\r\n\r\n  private transformAgainst(op1: Operation, op2: Operation): Operation {\r\n    // Implement transformation rules based on operation types\r\n    if (op1.elementId === op2.elementId) {\r\n      // Same element - need careful transformation\r\n      return this.transformSameElement(op1, op2);\r\n    }\r\n    \r\n    // Different elements - operations are independent\r\n    return op1;\r\n  }\r\n\r\n  private transformSameElement(op1: Operation, op2: Operation): Operation {\r\n    // Implement specific transformation logic\r\n    // This is a simplified version - real implementation would be more complex\r\n    \r\n    if (op1.type === 'update' && op2.type === 'update') {\r\n      // Last writer wins for updates\r\n      return op1.timestamp > op2.timestamp ? op1 : op2;\r\n    }\r\n    \r\n    if (op1.type === 'move' && op2.type === 'move') {\r\n      // Combine position changes\r\n      return {\r\n        ...op1,\r\n        position: {\r\n          x: (op1.position?.x || 0) + (op2.position?.x || 0),\r\n          y: (op1.position?.y || 0) + (op2.position?.y || 0)\r\n        }\r\n      };\r\n    }\r\n    \r\n    return op1;\r\n  }\r\n}\r\n\r\n// Singleton instance\r\nlet collaborationService: CollaborationService | null = null;\r\n\r\nexport function getCollaborationService(): CollaborationService {\r\n  if (!collaborationService) {\r\n    collaborationService = new CollaborationService();\r\n  }\r\n  return collaborationService;\r\n}\r\n\r\nexport default CollaborationService;\r\n"], "mappings": ";;AAAA;;;;;;;;;;;;;;AAAA;AAAA,SAAAA,cAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;MAAA;QAAAC,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAO,SAAA;MAAA;QAAAD,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;IAAA;IAAAW,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,cAAA;MAAAC,IAAA;MAAAC,QAAA;MAAAC,KAAA;MAAAC,OAAA;MAAAC,cAAA;MAAAC,OAAA;IAAA;IAAAC,eAAA;IAAA5B,IAAA;EAAA;EAAA,IAAA6B,QAAA,GAAA5B,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAA0B,QAAA,CAAA9B,IAAA,KAAA8B,QAAA,CAAA9B,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAA6B,QAAA,CAAA9B,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAA0B,cAAA,GAAAD,QAAA,CAAA9B,IAAA;EAAA;;;;;;;;;;;;;;;;;;AAufAgC,OAAA,CAAAC,uBAAA,GAAAA,uBAAA;AAxeA,MAAAC,kBAAA;AAAA;AAAA,CAAAnC,aAAA,GAAAoB,CAAA,OAAAgB,OAAA;AACA,MAAAC,MAAA;AAAA;AAAA,CAAArC,aAAA,GAAAoB,CAAA,OAAAgB,OAAA;AAsDA,MAAaE,oBAAoB;EAW/BC,YAAA;IAAA;IAAAvC,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAVQ,KAAAoB,MAAM,GAAkB,IAAI;IAAC;IAAAxC,aAAA,GAAAoB,CAAA;IAC7B,KAAAqB,WAAW,GAA6B,IAAI;IAAC;IAAAzC,aAAA,GAAAoB,CAAA;IAE7C,KAAAsB,cAAc,GAAgB,EAAE;IAAC;IAAA1C,aAAA,GAAAoB,CAAA;IAEjC,KAAAuB,cAAc,GAA4B,IAAIC,GAAG,EAAE;IAAC;IAAA5C,aAAA,GAAAoB,CAAA;IACpD,KAAAyB,iBAAiB,GAAG,CAAC;IAAC;IAAA7C,aAAA,GAAAoB,CAAA;IACtB,KAAA0B,oBAAoB,GAAG,CAAC;IAAC;IAAA9C,aAAA,GAAAoB,CAAA;IACzB,KAAA2B,iBAAiB,GAA0B,IAAI;IAAC;IAAA/C,aAAA,GAAAoB,CAAA;IAGtD,IAAI,CAAC4B,KAAK,GAAG;MACXC,WAAW,EAAE,KAAK;MAClBC,WAAW,EAAE,EAAE;MACfC,iBAAiB,EAAE,EAAE;MACrBC,eAAe,EAAE,EAAE;MACnBC,cAAc,EAAE,KAAK;MACrBC,YAAY,EAAE,IAAIC,IAAI;KACvB;IAAC;IAAAvD,aAAA,GAAAoB,CAAA;IAEF,IAAI,CAACoC,oBAAoB,GAAG,IAAIC,oBAAoB,EAAE;IAAC;IAAAzD,aAAA,GAAAoB,CAAA;IACvD,IAAI,CAACsC,kBAAkB,EAAE;EAC3B;EAEA;;;EAGA,MAAMC,UAAUA,CAACC,IAAuB,EAAEC,SAAkB;IAAA;IAAA7D,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAC1D,IAAI;MAAA;MAAApB,aAAA,GAAAoB,CAAA;MACF,IAAI,CAACqB,WAAW,GAAGmB,IAAI;MAEvB;MAAA;MAAA5D,aAAA,GAAAoB,CAAA;MACA,IAAI,CAACoB,MAAM,GAAG,IAAAL,kBAAA,CAAA2B,EAAE;MAAC;MAAA,CAAA9D,aAAA,GAAAsB,CAAA,UAAAuC,SAAS;MAAA;MAAA,CAAA7D,aAAA,GAAAsB,CAAA,UAAIyC,OAAO,CAACC,GAAG,CAACC,gCAAgC;MAAA;MAAA,CAAAjE,aAAA,GAAAsB,CAAA,UAAI,qBAAqB,GAAE;QACnG4C,IAAI,EAAE;UACJC,MAAM,EAAEP,IAAI,CAACQ,EAAE;UACfC,KAAK,EAAE,MAAM,IAAI,CAACC,YAAY;SAC/B;QACDC,UAAU,EAAE,CAAC,WAAW,EAAE,SAAS,CAAC;QACpCC,OAAO,EAAE,KAAK;QACdC,YAAY,EAAE,IAAI;QAClBC,oBAAoB,EAAE,IAAI,CAAC5B,oBAAoB;QAC/C6B,iBAAiB,EAAE;OACpB,CAAC;MAAC;MAAA3E,aAAA,GAAAoB,CAAA;MAEH,IAAI,CAACwD,wBAAwB,EAAE;MAE/B;MAAA;MAAA5E,aAAA,GAAAoB,CAAA;MACA,IAAI,CAACyD,cAAc,EAAE;IAEvB,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAA;MAAA9E,aAAA,GAAAoB,CAAA;MACd2D,OAAO,CAACD,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;MAAC;MAAA9E,aAAA,GAAAoB,CAAA;MACpE,MAAM0D,KAAK;IACb;EACF;EAEA;;;EAGA,MAAME,YAAYA,CAACC,UAAkB,EAAEC,SAAiB;IAAA;IAAAlF,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACtD;IAAI;IAAA,CAAApB,aAAA,GAAAsB,CAAA,WAAC,IAAI,CAACkB,MAAM;IAAA;IAAA,CAAAxC,aAAA,GAAAsB,CAAA,UAAI,CAAC,IAAI,CAACmB,WAAW,GAAE;MAAA;MAAAzC,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACrC,MAAM,IAAI+D,KAAK,CAAC,uCAAuC,CAAC;IAC1D,CAAC;IAAA;IAAA;MAAAnF,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAED,OAAO,IAAIgE,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MAAA;MAAAtF,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MACrC,IAAI,CAACoB,MAAO,CAAC+C,IAAI,CAAC,eAAe,EAAE;QACjCN,UAAU;QACVC,SAAS;QACTtB,IAAI,EAAE,IAAI,CAACnB;OACZ,EAAG+C,QAAgF,IAAI;QAAA;QAAAxF,aAAA,GAAAqB,CAAA;QAAArB,aAAA,GAAAoB,CAAA;QACtF;QAAI;QAAA,CAAApB,aAAA,GAAAsB,CAAA,UAAAkE,QAAQ,CAACC,OAAO;QAAA;QAAA,CAAAzF,aAAA,GAAAsB,CAAA,UAAIkE,QAAQ,CAACE,QAAQ,GAAE;UAAA;UAAA1F,aAAA,GAAAsB,CAAA;UAAAtB,aAAA,GAAAoB,CAAA;UACzC,IAAI,CAAC4B,KAAK,CAAC2C,eAAe,GAAGH,QAAQ,CAACE,QAAQ;UAAC;UAAA1F,aAAA,GAAAoB,CAAA;UAC/C,IAAI,CAAC4B,KAAK,CAACE,WAAW,GAAGsC,QAAQ,CAACE,QAAQ,CAACE,YAAY,CAACC,MAAM,CAACC,CAAC,IAAI;YAAA;YAAA9F,aAAA,GAAAqB,CAAA;YAAArB,aAAA,GAAAoB,CAAA;YAAA,OAAA0E,CAAC,CAACC,QAAQ;UAAR,CAAQ,CAAC;UAAC;UAAA/F,aAAA,GAAAoB,CAAA;UAChF,IAAI,CAACmE,IAAI,CAAC,iBAAiB,EAAEC,QAAQ,CAACE,QAAQ,CAAC;UAAC;UAAA1F,aAAA,GAAAoB,CAAA;UAChDiE,OAAO,CAACG,QAAQ,CAACE,QAAQ,CAAC;QAC5B,CAAC,MAAM;UAAA;UAAA1F,aAAA,GAAAsB,CAAA;UAAAtB,aAAA,GAAAoB,CAAA;UACLkE,MAAM,CAAC,IAAIH,KAAK;UAAC;UAAA,CAAAnF,aAAA,GAAAsB,CAAA,UAAAkE,QAAQ,CAACV,KAAK;UAAA;UAAA,CAAA9E,aAAA,GAAAsB,CAAA,UAAI,yBAAyB,EAAC,CAAC;QAChE;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEA;;;EAGA,MAAM0E,aAAaA,CAAA;IAAA;IAAAhG,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACjB;IAAI;IAAA,CAAApB,aAAA,GAAAsB,CAAA,WAAC,IAAI,CAACkB,MAAM;IAAA;IAAA,CAAAxC,aAAA,GAAAsB,CAAA,UAAI,CAAC,IAAI,CAAC0B,KAAK,CAAC2C,eAAe,GAAE;MAAA;MAAA3F,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAC/C;IACF,CAAC;IAAA;IAAA;MAAApB,aAAA,GAAAsB,CAAA;IAAA;IAED;IAAAtB,aAAA,GAAAoB,CAAA;IACA,MAAM,IAAI,CAAC6E,qBAAqB,EAAE;IAAC;IAAAjG,aAAA,GAAAoB,CAAA;IAEnC,IAAI,CAACoB,MAAM,CAAC+C,IAAI,CAAC,gBAAgB,EAAE;MACjCN,UAAU,EAAE,IAAI,CAACjC,KAAK,CAAC2C,eAAe,CAACvB,EAAE;MACzCD,MAAM,EAAE,IAAI,CAAC1B,WAAW,EAAE2B;KAC3B,CAAC;IAAC;IAAApE,aAAA,GAAAoB,CAAA;IAEH,IAAI,CAAC4B,KAAK,CAAC2C,eAAe,GAAGxE,SAAS;IAAC;IAAAnB,aAAA,GAAAoB,CAAA;IACvC,IAAI,CAAC4B,KAAK,CAACE,WAAW,GAAG,EAAE;IAAC;IAAAlD,aAAA,GAAAoB,CAAA;IAC5B,IAAI,CAACsB,cAAc,GAAG,EAAE;IAAC;IAAA1C,aAAA,GAAAoB,CAAA;IACzB,IAAI,CAACmE,IAAI,CAAC,eAAe,CAAC;EAC5B;EAEA;;;EAGA,MAAMW,cAAcA,CAACC,SAAyD;IAAA;IAAAnG,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAC5E;IAAI;IAAA,CAAApB,aAAA,GAAAsB,CAAA,WAAC,IAAI,CAACmB,WAAW;IAAA;IAAA,CAAAzC,aAAA,GAAAsB,CAAA,UAAI,CAAC,IAAI,CAAC0B,KAAK,CAAC2C,eAAe,GAAE;MAAA;MAAA3F,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACpD,MAAM,IAAI+D,KAAK,CAAC,iCAAiC,CAAC;IACpD,CAAC;IAAA;IAAA;MAAAnF,aAAA,GAAAsB,CAAA;IAAA;IAED,MAAM8E,aAAa;IAAA;IAAA,CAAApG,aAAA,GAAAoB,CAAA,QAAc;MAC/B,GAAG+E,SAAS;MACZ/B,EAAE,EAAE,IAAA/B,MAAA,CAAAgE,EAAM,GAAE;MACZlC,MAAM,EAAE,IAAI,CAAC1B,WAAW,CAAC2B,EAAE;MAC3BkC,SAAS,EAAE,IAAI/C,IAAI;KACpB;IAED;IAAA;IAAAvD,aAAA,GAAAoB,CAAA;IACA,IAAI,CAAC4B,KAAK,CAACI,eAAe,CAACmD,IAAI,CAACH,aAAa,CAAC;IAAC;IAAApG,aAAA,GAAAoB,CAAA;IAC/C,IAAI,CAACmE,IAAI,CAAC,mBAAmB,EAAEa,aAAa,CAAC;IAE7C;IAAA;IAAApG,aAAA,GAAAoB,CAAA;IACA,IAAI,CAACsB,cAAc,CAAC6D,IAAI,CAACH,aAAa,CAAC;IAEvC;IAAA;IAAApG,aAAA,GAAAoB,CAAA;IACA,IAAI,IAAI,CAAC4B,KAAK,CAACC,WAAW,EAAE;MAAA;MAAAjD,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAC1B,MAAM,IAAI,CAACoF,aAAa,CAACJ,aAAa,CAAC;IACzC,CAAC;IAAA;IAAA;MAAApG,aAAA,GAAAsB,CAAA;IAAA;EACH;EAEA;;;EAGAmF,YAAYA,CAACC,QAAsD;IAAA;IAAA1G,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACjE;IAAI;IAAA,CAAApB,aAAA,GAAAsB,CAAA,YAAC,IAAI,CAACmB,WAAW;IAAA;IAAA,CAAAzC,aAAA,GAAAsB,CAAA,WAAI,CAAC,IAAI,CAACkB,MAAM,GAAE;MAAA;MAAAxC,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACrC;IACF,CAAC;IAAA;IAAA;MAAApB,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAED,IAAI,CAACqB,WAAW,CAACkE,MAAM,GAAGD,QAAQ;IAAC;IAAA1G,aAAA,GAAAoB,CAAA;IAEnC,IAAI,CAACoB,MAAM,CAAC+C,IAAI,CAAC,eAAe,EAAE;MAChCN,UAAU,EAAE,IAAI,CAACjC,KAAK,CAAC2C,eAAe,EAAEvB,EAAE;MAC1CD,MAAM,EAAE,IAAI,CAAC1B,WAAW,CAAC2B,EAAE;MAC3BuC,MAAM,EAAED;KACT,CAAC;EACJ;EAEA;;;EAGA,MAAME,YAAYA,CAACC,IAAa;IAAA;IAAA7G,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAC9B;IAAI;IAAA,CAAApB,aAAA,GAAAsB,CAAA,YAAC,IAAI,CAACkB,MAAM;IAAA;IAAA,CAAAxC,aAAA,GAAAsB,CAAA,WAAI,CAAC,IAAI,CAAC0B,KAAK,CAAC2C,eAAe;IAAA;IAAA,CAAA3F,aAAA,GAAAsB,CAAA,WAAI,CAAC,IAAI,CAACmB,WAAW,GAAE;MAAA;MAAAzC,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACpE,OAAO,KAAK;IACd,CAAC;IAAA;IAAA;MAAApB,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAED,OAAO,IAAIgE,OAAO,CAAEC,OAAO,IAAI;MAAA;MAAArF,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MAC7B,IAAI,CAACoB,MAAO,CAAC+C,IAAI,CAAC,eAAe,EAAE;QACjCN,UAAU,EAAE,IAAI,CAACjC,KAAK,CAAC2C,eAAgB,CAACvB,EAAE;QAC1CD,MAAM,EAAE,IAAI,CAAC1B,WAAY,CAAC2B,EAAE;QAC5ByC;OACD,EAAGrB,QAA8B,IAAI;QAAA;QAAAxF,aAAA,GAAAqB,CAAA;QAAArB,aAAA,GAAAoB,CAAA;QACpC,IAAIoE,QAAQ,CAACC,OAAO,EAAE;UAAA;UAAAzF,aAAA,GAAAsB,CAAA;UAAAtB,aAAA,GAAAoB,CAAA;UACpB,IAAI,CAAC4B,KAAK,CAAC2C,eAAgB,CAACmB,QAAQ,GAAGD,IAAI;UAAC;UAAA7G,aAAA,GAAAoB,CAAA;UAC5C,IAAI,CAAC4B,KAAK,CAAC2C,eAAgB,CAACoB,QAAQ,GAAGF,IAAI;UAAA;UAAA,CAAA7G,aAAA,GAAAsB,CAAA,WAAG,IAAI,CAACmB,WAAY,CAAC2B,EAAE;UAAA;UAAA,CAAApE,aAAA,GAAAsB,CAAA,WAAGH,SAAS;UAAC;UAAAnB,aAAA,GAAAoB,CAAA;UAC/E,IAAI,CAACmE,IAAI,CAAC,uBAAuB,EAAE;YAAEyB,MAAM,EAAEH,IAAI;YAAE1C,MAAM,EAAE,IAAI,CAAC1B,WAAY,CAAC2B;UAAE,CAAE,CAAC;QACpF,CAAC;QAAA;QAAA;UAAApE,aAAA,GAAAsB,CAAA;QAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACDiE,OAAO,CAACG,QAAQ,CAACC,OAAO,CAAC;MAC3B,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEA;;;EAGAwB,qBAAqBA,CAAA;IAAA;IAAAjH,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAOnB,OAAO;MACL8B,WAAW,EAAE,IAAI,CAACF,KAAK,CAACE,WAAW,CAACgE,MAAM;MAC1CC,eAAe,EAAE,IAAI,CAACnE,KAAK,CAACI,eAAe,CAAC8D,MAAM;MAClD/D,iBAAiB,EAAE,IAAI,CAACT,cAAc,CAACwE,MAAM;MAC7C5D,YAAY,EAAE,IAAI,CAACN,KAAK,CAACM,YAAY;MACrCL,WAAW,EAAE,IAAI,CAACD,KAAK,CAACC;KACzB;EACH;EAEA;;;EAGAmE,EAAEA,CAACC,KAAa,EAAEC,QAAkB;IAAA;IAAAtH,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAClC,IAAI,CAAC,IAAI,CAACuB,cAAc,CAAC4E,GAAG,CAACF,KAAK,CAAC,EAAE;MAAA;MAAArH,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACnC,IAAI,CAACuB,cAAc,CAAC6E,GAAG,CAACH,KAAK,EAAE,EAAE,CAAC;IACpC,CAAC;IAAA;IAAA;MAAArH,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IACD,IAAI,CAACuB,cAAc,CAAC8E,GAAG,CAACJ,KAAK,CAAE,CAACd,IAAI,CAACe,QAAQ,CAAC;EAChD;EAEAI,GAAGA,CAACL,KAAa,EAAEC,QAAkB;IAAA;IAAAtH,aAAA,GAAAqB,CAAA;IACnC,MAAMsG,SAAS;IAAA;IAAA,CAAA3H,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAACuB,cAAc,CAAC8E,GAAG,CAACJ,KAAK,CAAC;IAAC;IAAArH,aAAA,GAAAoB,CAAA;IACjD,IAAIuG,SAAS,EAAE;MAAA;MAAA3H,aAAA,GAAAsB,CAAA;MACb,MAAMsG,KAAK;MAAA;MAAA,CAAA5H,aAAA,GAAAoB,CAAA,QAAGuG,SAAS,CAACE,OAAO,CAACP,QAAQ,CAAC;MAAC;MAAAtH,aAAA,GAAAoB,CAAA;MAC1C,IAAIwG,KAAK,GAAG,CAAC,CAAC,EAAE;QAAA;QAAA5H,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACduG,SAAS,CAACG,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MAC5B,CAAC;MAAA;MAAA;QAAA5H,aAAA,GAAAsB,CAAA;MAAA;IACH,CAAC;IAAA;IAAA;MAAAtB,aAAA,GAAAsB,CAAA;IAAA;EACH;EAEQiE,IAAIA,CAAC8B,KAAa,EAAEU,IAAU;IAAA;IAAA/H,aAAA,GAAAqB,CAAA;IACpC,MAAMsG,SAAS;IAAA;IAAA,CAAA3H,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAACuB,cAAc,CAAC8E,GAAG,CAACJ,KAAK,CAAC;IAAC;IAAArH,aAAA,GAAAoB,CAAA;IACjD,IAAIuG,SAAS,EAAE;MAAA;MAAA3H,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACbuG,SAAS,CAACK,OAAO,CAACV,QAAQ,IAAI;QAAA;QAAAtH,aAAA,GAAAqB,CAAA;QAAArB,aAAA,GAAAoB,CAAA;QAAA,OAAAkG,QAAQ,CAACS,IAAI,CAAC;MAAD,CAAC,CAAC;IAC/C,CAAC;IAAA;IAAA;MAAA/H,aAAA,GAAAsB,CAAA;IAAA;EACH;EAEQsD,wBAAwBA,CAAA;IAAA;IAAA5E,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAC9B,IAAI,CAAC,IAAI,CAACoB,MAAM,EAAE;MAAA;MAAAxC,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA;IAAA,CAAO;IAAA;IAAA;MAAApB,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAEzB,IAAI,CAACoB,MAAM,CAAC4E,EAAE,CAAC,SAAS,EAAE,MAAK;MAAA;MAAApH,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MAC7B,IAAI,CAAC4B,KAAK,CAACC,WAAW,GAAG,IAAI;MAAC;MAAAjD,aAAA,GAAAoB,CAAA;MAC9B,IAAI,CAAC4B,KAAK,CAACK,cAAc,GAAG,KAAK;MAAC;MAAArD,aAAA,GAAAoB,CAAA;MAClC,IAAI,CAACyB,iBAAiB,GAAG,CAAC;MAAC;MAAA7C,aAAA,GAAAoB,CAAA;MAC3B,IAAI,CAACmE,IAAI,CAAC,WAAW,CAAC;MAEtB;MAAA;MAAAvF,aAAA,GAAAoB,CAAA;MACA,IAAI,CAAC6E,qBAAqB,EAAE;IAC9B,CAAC,CAAC;IAAC;IAAAjG,aAAA,GAAAoB,CAAA;IAEH,IAAI,CAACoB,MAAM,CAAC4E,EAAE,CAAC,YAAY,EAAE,MAAK;MAAA;MAAApH,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MAChC,IAAI,CAAC4B,KAAK,CAACC,WAAW,GAAG,KAAK;MAAC;MAAAjD,aAAA,GAAAoB,CAAA;MAC/B,IAAI,CAACmE,IAAI,CAAC,cAAc,CAAC;IAC3B,CAAC,CAAC;IAAC;IAAAvF,aAAA,GAAAoB,CAAA;IAEH,IAAI,CAACoB,MAAM,CAAC4E,EAAE,CAAC,mBAAmB,EAAE,MAAK;MAAA;MAAApH,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MACvC,IAAI,CAAC4B,KAAK,CAACK,cAAc,GAAG,IAAI;MAAC;MAAArD,aAAA,GAAAoB,CAAA;MACjC,IAAI,CAACyB,iBAAiB,EAAE;MAAC;MAAA7C,aAAA,GAAAoB,CAAA;MACzB,IAAI,CAACmE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC1C,iBAAiB,CAAC;IACnD,CAAC,CAAC;IAAC;IAAA7C,aAAA,GAAAoB,CAAA;IAEH,IAAI,CAACoB,MAAM,CAAC4E,EAAE,CAAC,oBAAoB,EAAGjB,SAAoB,IAAI;MAAA;MAAAnG,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MAC5D,IAAI,CAAC6G,qBAAqB,CAAC9B,SAAS,CAAC;IACvC,CAAC,CAAC;IAAC;IAAAnG,aAAA,GAAAoB,CAAA;IAEH,IAAI,CAACoB,MAAM,CAAC4E,EAAE,CAAC,aAAa,EAAGxD,IAAuB,IAAI;MAAA;MAAA5D,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MACxD,IAAI,CAAC4B,KAAK,CAACE,WAAW,CAACqD,IAAI,CAAC3C,IAAI,CAAC;MAAC;MAAA5D,aAAA,GAAAoB,CAAA;MAClC,IAAI,CAACmE,IAAI,CAAC,aAAa,EAAE3B,IAAI,CAAC;IAChC,CAAC,CAAC;IAAC;IAAA5D,aAAA,GAAAoB,CAAA;IAEH,IAAI,CAACoB,MAAM,CAAC4E,EAAE,CAAC,WAAW,EAAGjD,MAAc,IAAI;MAAA;MAAAnE,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MAC7C,IAAI,CAAC4B,KAAK,CAACE,WAAW,GAAG,IAAI,CAACF,KAAK,CAACE,WAAW,CAAC2C,MAAM,CAACC,CAAC,IAAI;QAAA;QAAA9F,aAAA,GAAAqB,CAAA;QAAArB,aAAA,GAAAoB,CAAA;QAAA,OAAA0E,CAAC,CAAC1B,EAAE,KAAKD,MAAM;MAAN,CAAM,CAAC;MAAC;MAAAnE,aAAA,GAAAoB,CAAA;MAC7E,IAAI,CAACmE,IAAI,CAAC,WAAW,EAAEpB,MAAM,CAAC;IAChC,CAAC,CAAC;IAAC;IAAAnE,aAAA,GAAAoB,CAAA;IAEH,IAAI,CAACoB,MAAM,CAAC4E,EAAE,CAAC,gBAAgB,EAAGW,IAAqC,IAAI;MAAA;MAAA/H,aAAA,GAAAqB,CAAA;MACzE,MAAMuC,IAAI;MAAA;MAAA,CAAA5D,aAAA,GAAAoB,CAAA,SAAG,IAAI,CAAC4B,KAAK,CAACE,WAAW,CAACgF,IAAI,CAACpC,CAAC,IAAI;QAAA;QAAA9F,aAAA,GAAAqB,CAAA;QAAArB,aAAA,GAAAoB,CAAA;QAAA,OAAA0E,CAAC,CAAC1B,EAAE,KAAK2D,IAAI,CAAC5D,MAAM;MAAN,CAAM,CAAC;MAAC;MAAAnE,aAAA,GAAAoB,CAAA;MACpE,IAAIwC,IAAI,EAAE;QAAA;QAAA5D,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACRwC,IAAI,CAAC+C,MAAM,GAAGoB,IAAI,CAACpB,MAAM;QAAC;QAAA3G,aAAA,GAAAoB,CAAA;QAC1B,IAAI,CAACmE,IAAI,CAAC,gBAAgB,EAAEwC,IAAI,CAAC;MACnC,CAAC;MAAA;MAAA;QAAA/H,aAAA,GAAAsB,CAAA;MAAA;IACH,CAAC,CAAC;IAAC;IAAAtB,aAAA,GAAAoB,CAAA;IAEH,IAAI,CAACoB,MAAM,CAAC4E,EAAE,CAAC,iBAAiB,EAAGW,IAAyC,IAAI;MAAA;MAAA/H,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MAC9E,IAAI,IAAI,CAAC4B,KAAK,CAAC2C,eAAe,EAAE;QAAA;QAAA3F,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QAC9B,IAAI,CAAC4B,KAAK,CAAC2C,eAAe,CAACmB,QAAQ,GAAGiB,IAAI,CAACf,MAAM;QAAC;QAAAhH,aAAA,GAAAoB,CAAA;QAClD,IAAI,CAAC4B,KAAK,CAAC2C,eAAe,CAACoB,QAAQ,GAAGgB,IAAI,CAACf,MAAM;QAAA;QAAA,CAAAhH,aAAA,GAAAsB,CAAA,WAAGyG,IAAI,CAAC5D,MAAM;QAAA;QAAA,CAAAnE,aAAA,GAAAsB,CAAA,WAAGH,SAAS;QAAC;QAAAnB,aAAA,GAAAoB,CAAA;QAC5E,IAAI,CAACmE,IAAI,CAAC,uBAAuB,EAAEwC,IAAI,CAAC;MAC1C,CAAC;MAAA;MAAA;QAAA/H,aAAA,GAAAsB,CAAA;MAAA;IACH,CAAC,CAAC;EACJ;EAEQ,MAAMkF,aAAaA,CAACL,SAAoB;IAAA;IAAAnG,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAC9C;IAAI;IAAA,CAAApB,aAAA,GAAAsB,CAAA,YAAC,IAAI,CAACkB,MAAM;IAAA;IAAA,CAAAxC,aAAA,GAAAsB,CAAA,WAAI,CAAC,IAAI,CAAC0B,KAAK,CAAC2C,eAAe,GAAE;MAAA;MAAA3F,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAC/C;IACF,CAAC;IAAA;IAAA;MAAApB,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAED,IAAI,CAACoB,MAAM,CAAC+C,IAAI,CAAC,WAAW,EAAE;MAC5BN,UAAU,EAAE,IAAI,CAACjC,KAAK,CAAC2C,eAAe,CAACvB,EAAE;MACzC+B;KACD,EAAGX,QAA8C,IAAI;MAAA;MAAAxF,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MACpD,IAAIoE,QAAQ,CAACC,OAAO,EAAE;QAAA;QAAAzF,aAAA,GAAAsB,CAAA;QACpB;QACA,MAAMsG,KAAK;QAAA;QAAA,CAAA5H,aAAA,GAAAoB,CAAA,SAAG,IAAI,CAACsB,cAAc,CAACyF,SAAS,CAACC,EAAE,IAAI;UAAA;UAAApI,aAAA,GAAAqB,CAAA;UAAArB,aAAA,GAAAoB,CAAA;UAAA,OAAAgH,EAAE,CAAChE,EAAE,KAAK+B,SAAS,CAAC/B,EAAE;QAAF,CAAE,CAAC;QAAC;QAAApE,aAAA,GAAAoB,CAAA;QAC1E,IAAIwG,KAAK,GAAG,CAAC,CAAC,EAAE;UAAA;UAAA5H,aAAA,GAAAsB,CAAA;UAAAtB,aAAA,GAAAoB,CAAA;UACd,IAAI,CAACsB,cAAc,CAACoF,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;QACtC,CAAC;QAAA;QAAA;UAAA5H,aAAA,GAAAsB,CAAA;QAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACD,IAAI,CAAC4B,KAAK,CAACM,YAAY,GAAG,IAAIC,IAAI,EAAE;MACtC,CAAC,MAAM;QAAA;QAAAvD,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACL2D,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEU,QAAQ,CAACV,KAAK,CAAC;MAC5D;IACF,CAAC,CAAC;EACJ;EAEQmD,qBAAqBA,CAAC9B,SAAoB;IAAA;IAAAnG,aAAA,GAAAqB,CAAA;IAChD;IACA,MAAMgH,oBAAoB;IAAA;IAAA,CAAArI,aAAA,GAAAoB,CAAA,SAAG,IAAI,CAACoC,oBAAoB,CAAC8E,SAAS,CAC9DnC,SAAS,EACT,IAAI,CAACnD,KAAK,CAACI,eAAe,CAC3B;IAED;IAAA;IAAApD,aAAA,GAAAoB,CAAA;IACA,IAAI,CAACmE,IAAI,CAAC,kBAAkB,EAAE8C,oBAAoB,CAAC;IAEnD;IAAA;IAAArI,aAAA,GAAAoB,CAAA;IACA,IAAI,IAAI,CAAC4B,KAAK,CAAC2C,eAAe,EAAE;MAAA;MAAA3F,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAC9B,IAAI,CAAC4B,KAAK,CAAC2C,eAAe,CAAC9D,OAAO,EAAE;MAAC;MAAA7B,aAAA,GAAAoB,CAAA;MACrC,IAAI,CAAC4B,KAAK,CAAC2C,eAAe,CAAC4C,UAAU,CAAChC,IAAI,CAAC8B,oBAAoB,CAAC;IAClE,CAAC;IAAA;IAAA;MAAArI,aAAA,GAAAsB,CAAA;IAAA;EACH;EAEQ,MAAM2E,qBAAqBA,CAAA;IAAA;IAAAjG,aAAA,GAAAqB,CAAA;IACjC,MAAMkH,UAAU;IAAA;IAAA,CAAAvI,aAAA,GAAAoB,CAAA,SAAG,CAAC,GAAG,IAAI,CAACsB,cAAc,CAAC;IAAC;IAAA1C,aAAA,GAAAoB,CAAA;IAC5C,KAAK,MAAM+E,SAAS,IAAIoC,UAAU,EAAE;MAAA;MAAAvI,aAAA,GAAAoB,CAAA;MAClC,MAAM,IAAI,CAACoF,aAAa,CAACL,SAAS,CAAC;IACrC;EACF;EAEQzC,kBAAkBA,CAAA;IAAA;IAAA1D,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACxB;IACAsE,QAAQ,CAAC8C,gBAAgB,CAAC,kBAAkB,EAAE,MAAK;MAAA;MAAAxI,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MACjD,IAAIsE,QAAQ,CAAC+C,MAAM,EAAE;QAAA;QAAAzI,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACnB,IAAI,CAACsH,gBAAgB,CAAC,KAAK,CAAC;MAC9B,CAAC,MAAM;QAAA;QAAA1I,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACL,IAAI,CAACsH,gBAAgB,CAAC,IAAI,CAAC;MAC7B;IACF,CAAC,CAAC;IAEF;IAAA;IAAA1I,aAAA,GAAAoB,CAAA;IACAuH,MAAM,CAACH,gBAAgB,CAAC,cAAc,EAAE,MAAK;MAAA;MAAAxI,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MAC3C,IAAI,CAAC4E,aAAa,EAAE;IACtB,CAAC,CAAC;EACJ;EAEQ0C,gBAAgBA,CAACE,QAAiB;IAAA;IAAA5I,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACxC;IAAI;IAAA,CAAApB,aAAA,GAAAsB,CAAA,eAAI,CAACkB,MAAM;IAAA;IAAA,CAAAxC,aAAA,GAAAsB,CAAA,WAAI,IAAI,CAACmB,WAAW,GAAE;MAAA;MAAAzC,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACnC,IAAI,CAACoB,MAAM,CAAC+C,IAAI,CAAC,aAAa,EAAE;QAC9BpB,MAAM,EAAE,IAAI,CAAC1B,WAAW,CAAC2B,EAAE;QAC3BwE;OACD,CAAC;IACJ,CAAC;IAAA;IAAA;MAAA5I,aAAA,GAAAsB,CAAA;IAAA;EACH;EAEQuD,cAAcA,CAAA;IAAA;IAAA7E,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACpB,IAAI,CAAC2B,iBAAiB,GAAG8F,WAAW,CAAC,MAAK;MAAA;MAAA7I,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MACxC;MAAI;MAAA,CAAApB,aAAA,GAAAsB,CAAA,eAAI,CAACkB,MAAM;MAAA;MAAA,CAAAxC,aAAA,GAAAsB,CAAA,WAAI,IAAI,CAAC0B,KAAK,CAACC,WAAW,GAAE;QAAA;QAAAjD,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACzC,IAAI,CAACoB,MAAM,CAAC+C,IAAI,CAAC,WAAW,EAAE;UAC5BpB,MAAM,EAAE,IAAI,CAAC1B,WAAW,EAAE2B,EAAE;UAC5BkC,SAAS,EAAE,IAAI/C,IAAI;SACpB,CAAC;MACJ,CAAC;MAAA;MAAA;QAAAvD,aAAA,GAAAsB,CAAA;MAAA;IACH,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;EACb;EAEQ,MAAMgD,YAAYA,CAAA;IAAA;IAAAtE,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACxB;IACA,OAAO,2BAAApB,aAAA,GAAAsB,CAAA,WAAAwH,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAAA;IAAA,CAAA/I,aAAA,GAAAsB,CAAA,WAAI,EAAE;EACjD;EAEA;;;EAGA0H,OAAOA,CAAA;IAAA;IAAAhJ,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACL,IAAI,IAAI,CAAC2B,iBAAiB,EAAE;MAAA;MAAA/C,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAC1B6H,aAAa,CAAC,IAAI,CAAClG,iBAAiB,CAAC;IACvC,CAAC;IAAA;IAAA;MAAA/C,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAED,IAAI,IAAI,CAACoB,MAAM,EAAE;MAAA;MAAAxC,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACf,IAAI,CAACoB,MAAM,CAAC0G,UAAU,EAAE;IAC1B,CAAC;IAAA;IAAA;MAAAlJ,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAED,IAAI,CAACuB,cAAc,CAACwG,KAAK,EAAE;EAC7B;;AACD;AAAAnJ,aAAA,GAAAoB,CAAA;AAvXDa,OAAA,CAAAK,oBAAA,GAAAA,oBAAA;AAyXA;;;;AAIA,MAAMmB,oBAAoB;EACxB6E,SAASA,CAACnC,SAAoB,EAAE/C,eAA4B;IAAA;IAAApD,aAAA,GAAAqB,CAAA;IAC1D,IAAI+H,aAAa;IAAA;IAAA,CAAApJ,aAAA,GAAAoB,CAAA,SAAG;MAAE,GAAG+E;IAAS,CAAE;IAEpC;IAAA;IAAAnG,aAAA,GAAAoB,CAAA;IACA,KAAK,MAAMiI,OAAO,IAAIjG,eAAe,EAAE;MAAA;MAAApD,aAAA,GAAAoB,CAAA;MACrC,IAAIiI,OAAO,CAAC/C,SAAS,GAAGH,SAAS,CAACG,SAAS,EAAE;QAAA;QAAAtG,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QAC3CgI,aAAa,GAAG,IAAI,CAACE,gBAAgB,CAACF,aAAa,EAAEC,OAAO,CAAC;MAC/D,CAAC;MAAA;MAAA;QAAArJ,aAAA,GAAAsB,CAAA;MAAA;IACH;IAAC;IAAAtB,aAAA,GAAAoB,CAAA;IAED,OAAOgI,aAAa;EACtB;EAEQE,gBAAgBA,CAACC,GAAc,EAAEC,GAAc;IAAA;IAAAxJ,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACrD;IACA,IAAImI,GAAG,CAACE,SAAS,KAAKD,GAAG,CAACC,SAAS,EAAE;MAAA;MAAAzJ,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACnC;MACA,OAAO,IAAI,CAACsI,oBAAoB,CAACH,GAAG,EAAEC,GAAG,CAAC;IAC5C,CAAC;IAAA;IAAA;MAAAxJ,aAAA,GAAAsB,CAAA;IAAA;IAED;IAAAtB,aAAA,GAAAoB,CAAA;IACA,OAAOmI,GAAG;EACZ;EAEQG,oBAAoBA,CAACH,GAAc,EAAEC,GAAc;IAAA;IAAAxJ,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACzD;IACA;IAEA;IAAI;IAAA,CAAApB,aAAA,GAAAsB,CAAA,WAAAiI,GAAG,CAACtI,IAAI,KAAK,QAAQ;IAAA;IAAA,CAAAjB,aAAA,GAAAsB,CAAA,WAAIkI,GAAG,CAACvI,IAAI,KAAK,QAAQ,GAAE;MAAA;MAAAjB,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAClD;MACA,OAAOmI,GAAG,CAACjD,SAAS,GAAGkD,GAAG,CAAClD,SAAS;MAAA;MAAA,CAAAtG,aAAA,GAAAsB,CAAA,WAAGiI,GAAG;MAAA;MAAA,CAAAvJ,aAAA,GAAAsB,CAAA,WAAGkI,GAAG;IAClD,CAAC;IAAA;IAAA;MAAAxJ,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAED;IAAI;IAAA,CAAApB,aAAA,GAAAsB,CAAA,WAAAiI,GAAG,CAACtI,IAAI,KAAK,MAAM;IAAA;IAAA,CAAAjB,aAAA,GAAAsB,CAAA,WAAIkI,GAAG,CAACvI,IAAI,KAAK,MAAM,GAAE;MAAA;MAAAjB,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAC9C;MACA,OAAO;QACL,GAAGmI,GAAG;QACN7C,QAAQ,EAAE;UACRiD,CAAC,EAAE;UAAC;UAAA,CAAA3J,aAAA,GAAAsB,CAAA,WAAAiI,GAAG,CAAC7C,QAAQ,EAAEiD,CAAC;UAAA;UAAA,CAAA3J,aAAA,GAAAsB,CAAA,WAAI,CAAC;UAAK;UAAA,CAAAtB,aAAA,GAAAsB,CAAA,WAAAkI,GAAG,CAAC9C,QAAQ,EAAEiD,CAAC;UAAA;UAAA,CAAA3J,aAAA,GAAAsB,CAAA,WAAI,CAAC,EAAC;UAClDsI,CAAC,EAAE;UAAC;UAAA,CAAA5J,aAAA,GAAAsB,CAAA,WAAAiI,GAAG,CAAC7C,QAAQ,EAAEkD,CAAC;UAAA;UAAA,CAAA5J,aAAA,GAAAsB,CAAA,WAAI,CAAC;UAAK;UAAA,CAAAtB,aAAA,GAAAsB,CAAA,WAAAkI,GAAG,CAAC9C,QAAQ,EAAEkD,CAAC;UAAA;UAAA,CAAA5J,aAAA,GAAAsB,CAAA,WAAI,CAAC;;OAEpD;IACH,CAAC;IAAA;IAAA;MAAAtB,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAED,OAAOmI,GAAG;EACZ;;AAGF;AACA,IAAIM,oBAAoB;AAAA;AAAA,CAAA7J,aAAA,GAAAoB,CAAA,SAAgC,IAAI;AAE5D,SAAgBc,uBAAuBA,CAAA;EAAA;EAAAlC,aAAA,GAAAqB,CAAA;EAAArB,aAAA,GAAAoB,CAAA;EACrC,IAAI,CAACyI,oBAAoB,EAAE;IAAA;IAAA7J,aAAA,GAAAsB,CAAA;IAAAtB,aAAA,GAAAoB,CAAA;IACzByI,oBAAoB,GAAG,IAAIvH,oBAAoB,EAAE;EACnD,CAAC;EAAA;EAAA;IAAAtC,aAAA,GAAAsB,CAAA;EAAA;EAAAtB,aAAA,GAAAoB,CAAA;EACD,OAAOyI,oBAAoB;AAC7B;AAAC;AAAA7J,aAAA,GAAAoB,CAAA;AAEDa,OAAA,CAAA6H,OAAA,GAAexH,oBAAoB", "ignoreList": []}