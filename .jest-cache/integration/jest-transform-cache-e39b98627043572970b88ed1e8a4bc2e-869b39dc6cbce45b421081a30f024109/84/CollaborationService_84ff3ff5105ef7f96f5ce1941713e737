da65f6df4c20b83772264b86e212fef7
"use strict";

/**
 * Real-time Collaboration Service for SizeWise Suite
 *
 * Implements WebSocket-based collaboration with operational transformation
 * and conflict resolution for multi-user HVAC design workflows.
 *
 * Features:
 * - Real-time document synchronization
 * - Operational transformation for conflict resolution
 * - User presence and cursor tracking
 * - Collaborative editing with undo/redo
 * - Permission-based access control
 * - Offline-first with sync on reconnection
 */
/* istanbul ignore next */
function cov_4djw3z3h9() {
  var path = "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\frontend\\lib\\services\\CollaborationService.ts";
  var hash = "75a0823fdbdf4b474330abf988e1e389d5ab0404";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\frontend\\lib\\services\\CollaborationService.ts",
    statementMap: {
      "0": {
        start: {
          line: 16,
          column: 0
        },
        end: {
          line: 16,
          column: 62
        }
      },
      "1": {
        start: {
          line: 17,
          column: 0
        },
        end: {
          line: 17,
          column: 38
        }
      },
      "2": {
        start: {
          line: 18,
          column: 0
        },
        end: {
          line: 18,
          column: 58
        }
      },
      "3": {
        start: {
          line: 19,
          column: 27
        },
        end: {
          line: 19,
          column: 54
        }
      },
      "4": {
        start: {
          line: 20,
          column: 15
        },
        end: {
          line: 20,
          column: 30
        }
      },
      "5": {
        start: {
          line: 23,
          column: 8
        },
        end: {
          line: 23,
          column: 27
        }
      },
      "6": {
        start: {
          line: 24,
          column: 8
        },
        end: {
          line: 24,
          column: 32
        }
      },
      "7": {
        start: {
          line: 25,
          column: 8
        },
        end: {
          line: 25,
          column: 33
        }
      },
      "8": {
        start: {
          line: 26,
          column: 8
        },
        end: {
          line: 26,
          column: 40
        }
      },
      "9": {
        start: {
          line: 27,
          column: 8
        },
        end: {
          line: 27,
          column: 35
        }
      },
      "10": {
        start: {
          line: 28,
          column: 8
        },
        end: {
          line: 28,
          column: 38
        }
      },
      "11": {
        start: {
          line: 29,
          column: 8
        },
        end: {
          line: 29,
          column: 38
        }
      },
      "12": {
        start: {
          line: 30,
          column: 8
        },
        end: {
          line: 37,
          column: 10
        }
      },
      "13": {
        start: {
          line: 38,
          column: 8
        },
        end: {
          line: 38,
          column: 63
        }
      },
      "14": {
        start: {
          line: 39,
          column: 8
        },
        end: {
          line: 39,
          column: 34
        }
      },
      "15": {
        start: {
          line: 45,
          column: 8
        },
        end: {
          line: 66,
          column: 9
        }
      },
      "16": {
        start: {
          line: 46,
          column: 12
        },
        end: {
          line: 46,
          column: 36
        }
      },
      "17": {
        start: {
          line: 48,
          column: 12
        },
        end: {
          line: 58,
          column: 15
        }
      },
      "18": {
        start: {
          line: 59,
          column: 12
        },
        end: {
          line: 59,
          column: 44
        }
      },
      "19": {
        start: {
          line: 61,
          column: 12
        },
        end: {
          line: 61,
          column: 34
        }
      },
      "20": {
        start: {
          line: 64,
          column: 12
        },
        end: {
          line: 64,
          column: 80
        }
      },
      "21": {
        start: {
          line: 65,
          column: 12
        },
        end: {
          line: 65,
          column: 24
        }
      },
      "22": {
        start: {
          line: 72,
          column: 8
        },
        end: {
          line: 74,
          column: 9
        }
      },
      "23": {
        start: {
          line: 73,
          column: 12
        },
        end: {
          line: 73,
          column: 69
        }
      },
      "24": {
        start: {
          line: 75,
          column: 8
        },
        end: {
          line: 91,
          column: 11
        }
      },
      "25": {
        start: {
          line: 76,
          column: 12
        },
        end: {
          line: 90,
          column: 15
        }
      },
      "26": {
        start: {
          line: 81,
          column: 16
        },
        end: {
          line: 89,
          column: 17
        }
      },
      "27": {
        start: {
          line: 82,
          column: 20
        },
        end: {
          line: 82,
          column: 67
        }
      },
      "28": {
        start: {
          line: 83,
          column: 20
        },
        end: {
          line: 83,
          column: 100
        }
      },
      "29": {
        start: {
          line: 83,
          column: 88
        },
        end: {
          line: 83,
          column: 98
        }
      },
      "30": {
        start: {
          line: 84,
          column: 20
        },
        end: {
          line: 84,
          column: 68
        }
      },
      "31": {
        start: {
          line: 85,
          column: 20
        },
        end: {
          line: 85,
          column: 47
        }
      },
      "32": {
        start: {
          line: 88,
          column: 20
        },
        end: {
          line: 88,
          column: 83
        }
      },
      "33": {
        start: {
          line: 97,
          column: 8
        },
        end: {
          line: 99,
          column: 9
        }
      },
      "34": {
        start: {
          line: 98,
          column: 12
        },
        end: {
          line: 98,
          column: 19
        }
      },
      "35": {
        start: {
          line: 101,
          column: 8
        },
        end: {
          line: 101,
          column: 43
        }
      },
      "36": {
        start: {
          line: 102,
          column: 8
        },
        end: {
          line: 105,
          column: 11
        }
      },
      "37": {
        start: {
          line: 106,
          column: 8
        },
        end: {
          line: 106,
          column: 47
        }
      },
      "38": {
        start: {
          line: 107,
          column: 8
        },
        end: {
          line: 107,
          column: 36
        }
      },
      "39": {
        start: {
          line: 108,
          column: 8
        },
        end: {
          line: 108,
          column: 33
        }
      },
      "40": {
        start: {
          line: 109,
          column: 8
        },
        end: {
          line: 109,
          column: 35
        }
      },
      "41": {
        start: {
          line: 115,
          column: 8
        },
        end: {
          line: 117,
          column: 9
        }
      },
      "42": {
        start: {
          line: 116,
          column: 12
        },
        end: {
          line: 116,
          column: 63
        }
      },
      "43": {
        start: {
          line: 118,
          column: 30
        },
        end: {
          line: 123,
          column: 9
        }
      },
      "44": {
        start: {
          line: 125,
          column: 8
        },
        end: {
          line: 125,
          column: 55
        }
      },
      "45": {
        start: {
          line: 126,
          column: 8
        },
        end: {
          line: 126,
          column: 54
        }
      },
      "46": {
        start: {
          line: 128,
          column: 8
        },
        end: {
          line: 128,
          column: 48
        }
      },
      "47": {
        start: {
          line: 130,
          column: 8
        },
        end: {
          line: 132,
          column: 9
        }
      },
      "48": {
        start: {
          line: 131,
          column: 12
        },
        end: {
          line: 131,
          column: 52
        }
      },
      "49": {
        start: {
          line: 138,
          column: 8
        },
        end: {
          line: 140,
          column: 9
        }
      },
      "50": {
        start: {
          line: 139,
          column: 12
        },
        end: {
          line: 139,
          column: 19
        }
      },
      "51": {
        start: {
          line: 141,
          column: 8
        },
        end: {
          line: 141,
          column: 43
        }
      },
      "52": {
        start: {
          line: 142,
          column: 8
        },
        end: {
          line: 146,
          column: 11
        }
      },
      "53": {
        start: {
          line: 152,
          column: 8
        },
        end: {
          line: 154,
          column: 9
        }
      },
      "54": {
        start: {
          line: 153,
          column: 12
        },
        end: {
          line: 153,
          column: 25
        }
      },
      "55": {
        start: {
          line: 155,
          column: 8
        },
        end: {
          line: 168,
          column: 11
        }
      },
      "56": {
        start: {
          line: 156,
          column: 12
        },
        end: {
          line: 167,
          column: 15
        }
      },
      "57": {
        start: {
          line: 161,
          column: 16
        },
        end: {
          line: 165,
          column: 17
        }
      },
      "58": {
        start: {
          line: 162,
          column: 20
        },
        end: {
          line: 162,
          column: 63
        }
      },
      "59": {
        start: {
          line: 163,
          column: 20
        },
        end: {
          line: 163,
          column: 97
        }
      },
      "60": {
        start: {
          line: 164,
          column: 20
        },
        end: {
          line: 164,
          column: 102
        }
      },
      "61": {
        start: {
          line: 166,
          column: 16
        },
        end: {
          line: 166,
          column: 42
        }
      },
      "62": {
        start: {
          line: 174,
          column: 8
        },
        end: {
          line: 180,
          column: 10
        }
      },
      "63": {
        start: {
          line: 186,
          column: 8
        },
        end: {
          line: 188,
          column: 9
        }
      },
      "64": {
        start: {
          line: 187,
          column: 12
        },
        end: {
          line: 187,
          column: 47
        }
      },
      "65": {
        start: {
          line: 189,
          column: 8
        },
        end: {
          line: 189,
          column: 54
        }
      },
      "66": {
        start: {
          line: 192,
          column: 26
        },
        end: {
          line: 192,
          column: 56
        }
      },
      "67": {
        start: {
          line: 193,
          column: 8
        },
        end: {
          line: 198,
          column: 9
        }
      },
      "68": {
        start: {
          line: 194,
          column: 26
        },
        end: {
          line: 194,
          column: 53
        }
      },
      "69": {
        start: {
          line: 195,
          column: 12
        },
        end: {
          line: 197,
          column: 13
        }
      },
      "70": {
        start: {
          line: 196,
          column: 16
        },
        end: {
          line: 196,
          column: 43
        }
      },
      "71": {
        start: {
          line: 201,
          column: 26
        },
        end: {
          line: 201,
          column: 56
        }
      },
      "72": {
        start: {
          line: 202,
          column: 8
        },
        end: {
          line: 204,
          column: 9
        }
      },
      "73": {
        start: {
          line: 203,
          column: 12
        },
        end: {
          line: 203,
          column: 58
        }
      },
      "74": {
        start: {
          line: 203,
          column: 42
        },
        end: {
          line: 203,
          column: 56
        }
      },
      "75": {
        start: {
          line: 207,
          column: 8
        },
        end: {
          line: 208,
          column: 19
        }
      },
      "76": {
        start: {
          line: 208,
          column: 12
        },
        end: {
          line: 208,
          column: 19
        }
      },
      "77": {
        start: {
          line: 209,
          column: 8
        },
        end: {
          line: 216,
          column: 11
        }
      },
      "78": {
        start: {
          line: 210,
          column: 12
        },
        end: {
          line: 210,
          column: 42
        }
      },
      "79": {
        start: {
          line: 211,
          column: 12
        },
        end: {
          line: 211,
          column: 46
        }
      },
      "80": {
        start: {
          line: 212,
          column: 12
        },
        end: {
          line: 212,
          column: 39
        }
      },
      "81": {
        start: {
          line: 213,
          column: 12
        },
        end: {
          line: 213,
          column: 35
        }
      },
      "82": {
        start: {
          line: 215,
          column: 12
        },
        end: {
          line: 215,
          column: 41
        }
      },
      "83": {
        start: {
          line: 217,
          column: 8
        },
        end: {
          line: 220,
          column: 11
        }
      },
      "84": {
        start: {
          line: 218,
          column: 12
        },
        end: {
          line: 218,
          column: 43
        }
      },
      "85": {
        start: {
          line: 219,
          column: 12
        },
        end: {
          line: 219,
          column: 38
        }
      },
      "86": {
        start: {
          line: 221,
          column: 8
        },
        end: {
          line: 225,
          column: 11
        }
      },
      "87": {
        start: {
          line: 222,
          column: 12
        },
        end: {
          line: 222,
          column: 45
        }
      },
      "88": {
        start: {
          line: 223,
          column: 12
        },
        end: {
          line: 223,
          column: 37
        }
      },
      "89": {
        start: {
          line: 224,
          column: 12
        },
        end: {
          line: 224,
          column: 62
        }
      },
      "90": {
        start: {
          line: 226,
          column: 8
        },
        end: {
          line: 228,
          column: 11
        }
      },
      "91": {
        start: {
          line: 227,
          column: 12
        },
        end: {
          line: 227,
          column: 50
        }
      },
      "92": {
        start: {
          line: 229,
          column: 8
        },
        end: {
          line: 232,
          column: 11
        }
      },
      "93": {
        start: {
          line: 230,
          column: 12
        },
        end: {
          line: 230,
          column: 46
        }
      },
      "94": {
        start: {
          line: 231,
          column: 12
        },
        end: {
          line: 231,
          column: 43
        }
      },
      "95": {
        start: {
          line: 233,
          column: 8
        },
        end: {
          line: 236,
          column: 11
        }
      },
      "96": {
        start: {
          line: 234,
          column: 12
        },
        end: {
          line: 234,
          column: 89
        }
      },
      "97": {
        start: {
          line: 234,
          column: 72
        },
        end: {
          line: 234,
          column: 87
        }
      },
      "98": {
        start: {
          line: 235,
          column: 12
        },
        end: {
          line: 235,
          column: 43
        }
      },
      "99": {
        start: {
          line: 237,
          column: 8
        },
        end: {
          line: 243,
          column: 11
        }
      },
      "100": {
        start: {
          line: 238,
          column: 25
        },
        end: {
          line: 238,
          column: 79
        }
      },
      "101": {
        start: {
          line: 238,
          column: 58
        },
        end: {
          line: 238,
          column: 78
        }
      },
      "102": {
        start: {
          line: 239,
          column: 12
        },
        end: {
          line: 242,
          column: 13
        }
      },
      "103": {
        start: {
          line: 240,
          column: 16
        },
        end: {
          line: 240,
          column: 42
        }
      },
      "104": {
        start: {
          line: 241,
          column: 16
        },
        end: {
          line: 241,
          column: 50
        }
      },
      "105": {
        start: {
          line: 244,
          column: 8
        },
        end: {
          line: 250,
          column: 11
        }
      },
      "106": {
        start: {
          line: 245,
          column: 12
        },
        end: {
          line: 249,
          column: 13
        }
      },
      "107": {
        start: {
          line: 246,
          column: 16
        },
        end: {
          line: 246,
          column: 66
        }
      },
      "108": {
        start: {
          line: 247,
          column: 16
        },
        end: {
          line: 247,
          column: 92
        }
      },
      "109": {
        start: {
          line: 248,
          column: 16
        },
        end: {
          line: 248,
          column: 57
        }
      },
      "110": {
        start: {
          line: 253,
          column: 8
        },
        end: {
          line: 255,
          column: 9
        }
      },
      "111": {
        start: {
          line: 254,
          column: 12
        },
        end: {
          line: 254,
          column: 19
        }
      },
      "112": {
        start: {
          line: 256,
          column: 8
        },
        end: {
          line: 271,
          column: 11
        }
      },
      "113": {
        start: {
          line: 260,
          column: 12
        },
        end: {
          line: 270,
          column: 13
        }
      },
      "114": {
        start: {
          line: 262,
          column: 30
        },
        end: {
          line: 262,
          column: 89
        }
      },
      "115": {
        start: {
          line: 262,
          column: 66
        },
        end: {
          line: 262,
          column: 88
        }
      },
      "116": {
        start: {
          line: 263,
          column: 16
        },
        end: {
          line: 265,
          column: 17
        }
      },
      "117": {
        start: {
          line: 264,
          column: 20
        },
        end: {
          line: 264,
          column: 57
        }
      },
      "118": {
        start: {
          line: 266,
          column: 16
        },
        end: {
          line: 266,
          column: 53
        }
      },
      "119": {
        start: {
          line: 269,
          column: 16
        },
        end: {
          line: 269,
          column: 75
        }
      },
      "120": {
        start: {
          line: 275,
          column: 37
        },
        end: {
          line: 275,
          column: 111
        }
      },
      "121": {
        start: {
          line: 277,
          column: 8
        },
        end: {
          line: 277,
          column: 60
        }
      },
      "122": {
        start: {
          line: 279,
          column: 8
        },
        end: {
          line: 282,
          column: 9
        }
      },
      "123": {
        start: {
          line: 280,
          column: 12
        },
        end: {
          line: 280,
          column: 49
        }
      },
      "124": {
        start: {
          line: 281,
          column: 12
        },
        end: {
          line: 281,
          column: 77
        }
      },
      "125": {
        start: {
          line: 285,
          column: 27
        },
        end: {
          line: 285,
          column: 51
        }
      },
      "126": {
        start: {
          line: 286,
          column: 8
        },
        end: {
          line: 288,
          column: 9
        }
      },
      "127": {
        start: {
          line: 287,
          column: 12
        },
        end: {
          line: 287,
          column: 48
        }
      },
      "128": {
        start: {
          line: 292,
          column: 8
        },
        end: {
          line: 299,
          column: 11
        }
      },
      "129": {
        start: {
          line: 293,
          column: 12
        },
        end: {
          line: 298,
          column: 13
        }
      },
      "130": {
        start: {
          line: 294,
          column: 16
        },
        end: {
          line: 294,
          column: 45
        }
      },
      "131": {
        start: {
          line: 297,
          column: 16
        },
        end: {
          line: 297,
          column: 44
        }
      },
      "132": {
        start: {
          line: 301,
          column: 8
        },
        end: {
          line: 303,
          column: 11
        }
      },
      "133": {
        start: {
          line: 302,
          column: 12
        },
        end: {
          line: 302,
          column: 33
        }
      },
      "134": {
        start: {
          line: 306,
          column: 8
        },
        end: {
          line: 311,
          column: 9
        }
      },
      "135": {
        start: {
          line: 307,
          column: 12
        },
        end: {
          line: 310,
          column: 15
        }
      },
      "136": {
        start: {
          line: 314,
          column: 8
        },
        end: {
          line: 321,
          column: 18
        }
      },
      "137": {
        start: {
          line: 315,
          column: 12
        },
        end: {
          line: 320,
          column: 13
        }
      },
      "138": {
        start: {
          line: 316,
          column: 16
        },
        end: {
          line: 319,
          column: 19
        }
      },
      "139": {
        start: {
          line: 325,
          column: 8
        },
        end: {
          line: 325,
          column: 56
        }
      },
      "140": {
        start: {
          line: 331,
          column: 8
        },
        end: {
          line: 333,
          column: 9
        }
      },
      "141": {
        start: {
          line: 332,
          column: 12
        },
        end: {
          line: 332,
          column: 50
        }
      },
      "142": {
        start: {
          line: 334,
          column: 8
        },
        end: {
          line: 336,
          column: 9
        }
      },
      "143": {
        start: {
          line: 335,
          column: 12
        },
        end: {
          line: 335,
          column: 37
        }
      },
      "144": {
        start: {
          line: 337,
          column: 8
        },
        end: {
          line: 337,
          column: 36
        }
      },
      "145": {
        start: {
          line: 340,
          column: 0
        },
        end: {
          line: 340,
          column: 52
        }
      },
      "146": {
        start: {
          line: 347,
          column: 28
        },
        end: {
          line: 347,
          column: 44
        }
      },
      "147": {
        start: {
          line: 349,
          column: 8
        },
        end: {
          line: 353,
          column: 9
        }
      },
      "148": {
        start: {
          line: 350,
          column: 12
        },
        end: {
          line: 352,
          column: 13
        }
      },
      "149": {
        start: {
          line: 351,
          column: 16
        },
        end: {
          line: 351,
          column: 78
        }
      },
      "150": {
        start: {
          line: 354,
          column: 8
        },
        end: {
          line: 354,
          column: 29
        }
      },
      "151": {
        start: {
          line: 358,
          column: 8
        },
        end: {
          line: 361,
          column: 9
        }
      },
      "152": {
        start: {
          line: 360,
          column: 12
        },
        end: {
          line: 360,
          column: 55
        }
      },
      "153": {
        start: {
          line: 363,
          column: 8
        },
        end: {
          line: 363,
          column: 19
        }
      },
      "154": {
        start: {
          line: 368,
          column: 8
        },
        end: {
          line: 371,
          column: 9
        }
      },
      "155": {
        start: {
          line: 370,
          column: 12
        },
        end: {
          line: 370,
          column: 61
        }
      },
      "156": {
        start: {
          line: 372,
          column: 8
        },
        end: {
          line: 381,
          column: 9
        }
      },
      "157": {
        start: {
          line: 374,
          column: 12
        },
        end: {
          line: 380,
          column: 14
        }
      },
      "158": {
        start: {
          line: 382,
          column: 8
        },
        end: {
          line: 382,
          column: 19
        }
      },
      "159": {
        start: {
          line: 386,
          column: 27
        },
        end: {
          line: 386,
          column: 31
        }
      },
      "160": {
        start: {
          line: 388,
          column: 4
        },
        end: {
          line: 390,
          column: 5
        }
      },
      "161": {
        start: {
          line: 389,
          column: 8
        },
        end: {
          line: 389,
          column: 58
        }
      },
      "162": {
        start: {
          line: 391,
          column: 4
        },
        end: {
          line: 391,
          column: 32
        }
      },
      "163": {
        start: {
          line: 393,
          column: 0
        },
        end: {
          line: 393,
          column: 39
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 22,
            column: 4
          },
          end: {
            line: 22,
            column: 5
          }
        },
        loc: {
          start: {
            line: 22,
            column: 18
          },
          end: {
            line: 40,
            column: 5
          }
        },
        line: 22
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 44,
            column: 4
          },
          end: {
            line: 44,
            column: 5
          }
        },
        loc: {
          start: {
            line: 44,
            column: 38
          },
          end: {
            line: 67,
            column: 5
          }
        },
        line: 44
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 71,
            column: 4
          },
          end: {
            line: 71,
            column: 5
          }
        },
        loc: {
          start: {
            line: 71,
            column: 46
          },
          end: {
            line: 92,
            column: 5
          }
        },
        line: 71
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 75,
            column: 27
          },
          end: {
            line: 75,
            column: 28
          }
        },
        loc: {
          start: {
            line: 75,
            column: 48
          },
          end: {
            line: 91,
            column: 9
          }
        },
        line: 75
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 80,
            column: 15
          },
          end: {
            line: 80,
            column: 16
          }
        },
        loc: {
          start: {
            line: 80,
            column: 29
          },
          end: {
            line: 90,
            column: 13
          }
        },
        line: 80
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 83,
            column: 83
          },
          end: {
            line: 83,
            column: 84
          }
        },
        loc: {
          start: {
            line: 83,
            column: 88
          },
          end: {
            line: 83,
            column: 98
          }
        },
        line: 83
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 96,
            column: 4
          },
          end: {
            line: 96,
            column: 5
          }
        },
        loc: {
          start: {
            line: 96,
            column: 26
          },
          end: {
            line: 110,
            column: 5
          }
        },
        line: 96
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 114,
            column: 4
          },
          end: {
            line: 114,
            column: 5
          }
        },
        loc: {
          start: {
            line: 114,
            column: 36
          },
          end: {
            line: 133,
            column: 5
          }
        },
        line: 114
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 137,
            column: 4
          },
          end: {
            line: 137,
            column: 5
          }
        },
        loc: {
          start: {
            line: 137,
            column: 27
          },
          end: {
            line: 147,
            column: 5
          }
        },
        line: 137
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 151,
            column: 4
          },
          end: {
            line: 151,
            column: 5
          }
        },
        loc: {
          start: {
            line: 151,
            column: 29
          },
          end: {
            line: 169,
            column: 5
          }
        },
        line: 151
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 155,
            column: 27
          },
          end: {
            line: 155,
            column: 28
          }
        },
        loc: {
          start: {
            line: 155,
            column: 40
          },
          end: {
            line: 168,
            column: 9
          }
        },
        line: 155
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 160,
            column: 15
          },
          end: {
            line: 160,
            column: 16
          }
        },
        loc: {
          start: {
            line: 160,
            column: 29
          },
          end: {
            line: 167,
            column: 13
          }
        },
        line: 160
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 173,
            column: 4
          },
          end: {
            line: 173,
            column: 5
          }
        },
        loc: {
          start: {
            line: 173,
            column: 28
          },
          end: {
            line: 181,
            column: 5
          }
        },
        line: 173
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 185,
            column: 4
          },
          end: {
            line: 185,
            column: 5
          }
        },
        loc: {
          start: {
            line: 185,
            column: 24
          },
          end: {
            line: 190,
            column: 5
          }
        },
        line: 185
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 191,
            column: 4
          },
          end: {
            line: 191,
            column: 5
          }
        },
        loc: {
          start: {
            line: 191,
            column: 25
          },
          end: {
            line: 199,
            column: 5
          }
        },
        line: 191
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 200,
            column: 4
          },
          end: {
            line: 200,
            column: 5
          }
        },
        loc: {
          start: {
            line: 200,
            column: 22
          },
          end: {
            line: 205,
            column: 5
          }
        },
        line: 200
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 203,
            column: 30
          },
          end: {
            line: 203,
            column: 31
          }
        },
        loc: {
          start: {
            line: 203,
            column: 42
          },
          end: {
            line: 203,
            column: 56
          }
        },
        line: 203
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 206,
            column: 4
          },
          end: {
            line: 206,
            column: 5
          }
        },
        loc: {
          start: {
            line: 206,
            column: 31
          },
          end: {
            line: 251,
            column: 5
          }
        },
        line: 206
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 209,
            column: 34
          },
          end: {
            line: 209,
            column: 35
          }
        },
        loc: {
          start: {
            line: 209,
            column: 40
          },
          end: {
            line: 216,
            column: 9
          }
        },
        line: 209
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 217,
            column: 37
          },
          end: {
            line: 217,
            column: 38
          }
        },
        loc: {
          start: {
            line: 217,
            column: 43
          },
          end: {
            line: 220,
            column: 9
          }
        },
        line: 217
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 221,
            column: 44
          },
          end: {
            line: 221,
            column: 45
          }
        },
        loc: {
          start: {
            line: 221,
            column: 50
          },
          end: {
            line: 225,
            column: 9
          }
        },
        line: 221
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 226,
            column: 45
          },
          end: {
            line: 226,
            column: 46
          }
        },
        loc: {
          start: {
            line: 226,
            column: 60
          },
          end: {
            line: 228,
            column: 9
          }
        },
        line: 226
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 229,
            column: 38
          },
          end: {
            line: 229,
            column: 39
          }
        },
        loc: {
          start: {
            line: 229,
            column: 48
          },
          end: {
            line: 232,
            column: 9
          }
        },
        line: 229
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 233,
            column: 36
          },
          end: {
            line: 233,
            column: 37
          }
        },
        loc: {
          start: {
            line: 233,
            column: 48
          },
          end: {
            line: 236,
            column: 9
          }
        },
        line: 233
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 234,
            column: 67
          },
          end: {
            line: 234,
            column: 68
          }
        },
        loc: {
          start: {
            line: 234,
            column: 72
          },
          end: {
            line: 234,
            column: 87
          }
        },
        line: 234
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 237,
            column: 41
          },
          end: {
            line: 237,
            column: 42
          }
        },
        loc: {
          start: {
            line: 237,
            column: 51
          },
          end: {
            line: 243,
            column: 9
          }
        },
        line: 237
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 238,
            column: 53
          },
          end: {
            line: 238,
            column: 54
          }
        },
        loc: {
          start: {
            line: 238,
            column: 58
          },
          end: {
            line: 238,
            column: 78
          }
        },
        line: 238
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 244,
            column: 42
          },
          end: {
            line: 244,
            column: 43
          }
        },
        loc: {
          start: {
            line: 244,
            column: 52
          },
          end: {
            line: 250,
            column: 9
          }
        },
        line: 244
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 252,
            column: 4
          },
          end: {
            line: 252,
            column: 5
          }
        },
        loc: {
          start: {
            line: 252,
            column: 35
          },
          end: {
            line: 272,
            column: 5
          }
        },
        line: 252
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 259,
            column: 11
          },
          end: {
            line: 259,
            column: 12
          }
        },
        loc: {
          start: {
            line: 259,
            column: 25
          },
          end: {
            line: 271,
            column: 9
          }
        },
        line: 259
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 262,
            column: 60
          },
          end: {
            line: 262,
            column: 61
          }
        },
        loc: {
          start: {
            line: 262,
            column: 66
          },
          end: {
            line: 262,
            column: 88
          }
        },
        line: 262
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 273,
            column: 4
          },
          end: {
            line: 273,
            column: 5
          }
        },
        loc: {
          start: {
            line: 273,
            column: 37
          },
          end: {
            line: 283,
            column: 5
          }
        },
        line: 273
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 284,
            column: 4
          },
          end: {
            line: 284,
            column: 5
          }
        },
        loc: {
          start: {
            line: 284,
            column: 34
          },
          end: {
            line: 289,
            column: 5
          }
        },
        line: 284
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 290,
            column: 4
          },
          end: {
            line: 290,
            column: 5
          }
        },
        loc: {
          start: {
            line: 290,
            column: 25
          },
          end: {
            line: 304,
            column: 5
          }
        },
        line: 290
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 292,
            column: 54
          },
          end: {
            line: 292,
            column: 55
          }
        },
        loc: {
          start: {
            line: 292,
            column: 60
          },
          end: {
            line: 299,
            column: 9
          }
        },
        line: 292
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 301,
            column: 48
          },
          end: {
            line: 301,
            column: 49
          }
        },
        loc: {
          start: {
            line: 301,
            column: 54
          },
          end: {
            line: 303,
            column: 9
          }
        },
        line: 301
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 305,
            column: 4
          },
          end: {
            line: 305,
            column: 5
          }
        },
        loc: {
          start: {
            line: 305,
            column: 31
          },
          end: {
            line: 312,
            column: 5
          }
        },
        line: 305
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 313,
            column: 4
          },
          end: {
            line: 313,
            column: 5
          }
        },
        loc: {
          start: {
            line: 313,
            column: 21
          },
          end: {
            line: 322,
            column: 5
          }
        },
        line: 313
      },
      "38": {
        name: "(anonymous_38)",
        decl: {
          start: {
            line: 314,
            column: 45
          },
          end: {
            line: 314,
            column: 46
          }
        },
        loc: {
          start: {
            line: 314,
            column: 51
          },
          end: {
            line: 321,
            column: 9
          }
        },
        line: 314
      },
      "39": {
        name: "(anonymous_39)",
        decl: {
          start: {
            line: 323,
            column: 4
          },
          end: {
            line: 323,
            column: 5
          }
        },
        loc: {
          start: {
            line: 323,
            column: 25
          },
          end: {
            line: 326,
            column: 5
          }
        },
        line: 323
      },
      "40": {
        name: "(anonymous_40)",
        decl: {
          start: {
            line: 330,
            column: 4
          },
          end: {
            line: 330,
            column: 5
          }
        },
        loc: {
          start: {
            line: 330,
            column: 14
          },
          end: {
            line: 338,
            column: 5
          }
        },
        line: 330
      },
      "41": {
        name: "(anonymous_41)",
        decl: {
          start: {
            line: 346,
            column: 4
          },
          end: {
            line: 346,
            column: 5
          }
        },
        loc: {
          start: {
            line: 346,
            column: 42
          },
          end: {
            line: 355,
            column: 5
          }
        },
        line: 346
      },
      "42": {
        name: "(anonymous_42)",
        decl: {
          start: {
            line: 356,
            column: 4
          },
          end: {
            line: 356,
            column: 5
          }
        },
        loc: {
          start: {
            line: 356,
            column: 31
          },
          end: {
            line: 364,
            column: 5
          }
        },
        line: 356
      },
      "43": {
        name: "(anonymous_43)",
        decl: {
          start: {
            line: 365,
            column: 4
          },
          end: {
            line: 365,
            column: 5
          }
        },
        loc: {
          start: {
            line: 365,
            column: 35
          },
          end: {
            line: 383,
            column: 5
          }
        },
        line: 365
      },
      "44": {
        name: "getCollaborationService",
        decl: {
          start: {
            line: 387,
            column: 9
          },
          end: {
            line: 387,
            column: 32
          }
        },
        loc: {
          start: {
            line: 387,
            column: 35
          },
          end: {
            line: 392,
            column: 1
          }
        },
        line: 387
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 48,
            column: 53
          },
          end: {
            line: 48,
            column: 135
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 48,
            column: 53
          },
          end: {
            line: 48,
            column: 62
          }
        }, {
          start: {
            line: 48,
            column: 66
          },
          end: {
            line: 48,
            column: 110
          }
        }, {
          start: {
            line: 48,
            column: 114
          },
          end: {
            line: 48,
            column: 135
          }
        }],
        line: 48
      },
      "1": {
        loc: {
          start: {
            line: 72,
            column: 8
          },
          end: {
            line: 74,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 72,
            column: 8
          },
          end: {
            line: 74,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 72
      },
      "2": {
        loc: {
          start: {
            line: 72,
            column: 12
          },
          end: {
            line: 72,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 72,
            column: 12
          },
          end: {
            line: 72,
            column: 24
          }
        }, {
          start: {
            line: 72,
            column: 28
          },
          end: {
            line: 72,
            column: 45
          }
        }],
        line: 72
      },
      "3": {
        loc: {
          start: {
            line: 81,
            column: 16
          },
          end: {
            line: 89,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 81,
            column: 16
          },
          end: {
            line: 89,
            column: 17
          }
        }, {
          start: {
            line: 87,
            column: 21
          },
          end: {
            line: 89,
            column: 17
          }
        }],
        line: 81
      },
      "4": {
        loc: {
          start: {
            line: 81,
            column: 20
          },
          end: {
            line: 81,
            column: 57
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 81,
            column: 20
          },
          end: {
            line: 81,
            column: 36
          }
        }, {
          start: {
            line: 81,
            column: 40
          },
          end: {
            line: 81,
            column: 57
          }
        }],
        line: 81
      },
      "5": {
        loc: {
          start: {
            line: 88,
            column: 37
          },
          end: {
            line: 88,
            column: 80
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 88,
            column: 37
          },
          end: {
            line: 88,
            column: 51
          }
        }, {
          start: {
            line: 88,
            column: 55
          },
          end: {
            line: 88,
            column: 80
          }
        }],
        line: 88
      },
      "6": {
        loc: {
          start: {
            line: 97,
            column: 8
          },
          end: {
            line: 99,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 97,
            column: 8
          },
          end: {
            line: 99,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 97
      },
      "7": {
        loc: {
          start: {
            line: 97,
            column: 12
          },
          end: {
            line: 97,
            column: 55
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 97,
            column: 12
          },
          end: {
            line: 97,
            column: 24
          }
        }, {
          start: {
            line: 97,
            column: 28
          },
          end: {
            line: 97,
            column: 55
          }
        }],
        line: 97
      },
      "8": {
        loc: {
          start: {
            line: 115,
            column: 8
          },
          end: {
            line: 117,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 115,
            column: 8
          },
          end: {
            line: 117,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 115
      },
      "9": {
        loc: {
          start: {
            line: 115,
            column: 12
          },
          end: {
            line: 115,
            column: 60
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 115,
            column: 12
          },
          end: {
            line: 115,
            column: 29
          }
        }, {
          start: {
            line: 115,
            column: 33
          },
          end: {
            line: 115,
            column: 60
          }
        }],
        line: 115
      },
      "10": {
        loc: {
          start: {
            line: 130,
            column: 8
          },
          end: {
            line: 132,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 130,
            column: 8
          },
          end: {
            line: 132,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 130
      },
      "11": {
        loc: {
          start: {
            line: 138,
            column: 8
          },
          end: {
            line: 140,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 138,
            column: 8
          },
          end: {
            line: 140,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 138
      },
      "12": {
        loc: {
          start: {
            line: 138,
            column: 12
          },
          end: {
            line: 138,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 138,
            column: 12
          },
          end: {
            line: 138,
            column: 29
          }
        }, {
          start: {
            line: 138,
            column: 33
          },
          end: {
            line: 138,
            column: 45
          }
        }],
        line: 138
      },
      "13": {
        loc: {
          start: {
            line: 152,
            column: 8
          },
          end: {
            line: 154,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 152,
            column: 8
          },
          end: {
            line: 154,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 152
      },
      "14": {
        loc: {
          start: {
            line: 152,
            column: 12
          },
          end: {
            line: 152,
            column: 76
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 152,
            column: 12
          },
          end: {
            line: 152,
            column: 24
          }
        }, {
          start: {
            line: 152,
            column: 28
          },
          end: {
            line: 152,
            column: 55
          }
        }, {
          start: {
            line: 152,
            column: 59
          },
          end: {
            line: 152,
            column: 76
          }
        }],
        line: 152
      },
      "15": {
        loc: {
          start: {
            line: 161,
            column: 16
          },
          end: {
            line: 165,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 161,
            column: 16
          },
          end: {
            line: 165,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 161
      },
      "16": {
        loc: {
          start: {
            line: 163,
            column: 58
          },
          end: {
            line: 163,
            column: 96
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 163,
            column: 65
          },
          end: {
            line: 163,
            column: 84
          }
        }, {
          start: {
            line: 163,
            column: 87
          },
          end: {
            line: 163,
            column: 96
          }
        }],
        line: 163
      },
      "17": {
        loc: {
          start: {
            line: 186,
            column: 8
          },
          end: {
            line: 188,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 186,
            column: 8
          },
          end: {
            line: 188,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 186
      },
      "18": {
        loc: {
          start: {
            line: 193,
            column: 8
          },
          end: {
            line: 198,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 193,
            column: 8
          },
          end: {
            line: 198,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 193
      },
      "19": {
        loc: {
          start: {
            line: 195,
            column: 12
          },
          end: {
            line: 197,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 195,
            column: 12
          },
          end: {
            line: 197,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 195
      },
      "20": {
        loc: {
          start: {
            line: 202,
            column: 8
          },
          end: {
            line: 204,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 202,
            column: 8
          },
          end: {
            line: 204,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 202
      },
      "21": {
        loc: {
          start: {
            line: 207,
            column: 8
          },
          end: {
            line: 208,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 207,
            column: 8
          },
          end: {
            line: 208,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 207
      },
      "22": {
        loc: {
          start: {
            line: 239,
            column: 12
          },
          end: {
            line: 242,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 239,
            column: 12
          },
          end: {
            line: 242,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 239
      },
      "23": {
        loc: {
          start: {
            line: 245,
            column: 12
          },
          end: {
            line: 249,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 245,
            column: 12
          },
          end: {
            line: 249,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 245
      },
      "24": {
        loc: {
          start: {
            line: 247,
            column: 54
          },
          end: {
            line: 247,
            column: 91
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 247,
            column: 68
          },
          end: {
            line: 247,
            column: 79
          }
        }, {
          start: {
            line: 247,
            column: 82
          },
          end: {
            line: 247,
            column: 91
          }
        }],
        line: 247
      },
      "25": {
        loc: {
          start: {
            line: 253,
            column: 8
          },
          end: {
            line: 255,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 253,
            column: 8
          },
          end: {
            line: 255,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 253
      },
      "26": {
        loc: {
          start: {
            line: 253,
            column: 12
          },
          end: {
            line: 253,
            column: 55
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 253,
            column: 12
          },
          end: {
            line: 253,
            column: 24
          }
        }, {
          start: {
            line: 253,
            column: 28
          },
          end: {
            line: 253,
            column: 55
          }
        }],
        line: 253
      },
      "27": {
        loc: {
          start: {
            line: 260,
            column: 12
          },
          end: {
            line: 270,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 260,
            column: 12
          },
          end: {
            line: 270,
            column: 13
          }
        }, {
          start: {
            line: 268,
            column: 17
          },
          end: {
            line: 270,
            column: 13
          }
        }],
        line: 260
      },
      "28": {
        loc: {
          start: {
            line: 263,
            column: 16
          },
          end: {
            line: 265,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 263,
            column: 16
          },
          end: {
            line: 265,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 263
      },
      "29": {
        loc: {
          start: {
            line: 279,
            column: 8
          },
          end: {
            line: 282,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 279,
            column: 8
          },
          end: {
            line: 282,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 279
      },
      "30": {
        loc: {
          start: {
            line: 293,
            column: 12
          },
          end: {
            line: 298,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 293,
            column: 12
          },
          end: {
            line: 298,
            column: 13
          }
        }, {
          start: {
            line: 296,
            column: 17
          },
          end: {
            line: 298,
            column: 13
          }
        }],
        line: 293
      },
      "31": {
        loc: {
          start: {
            line: 306,
            column: 8
          },
          end: {
            line: 311,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 306,
            column: 8
          },
          end: {
            line: 311,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 306
      },
      "32": {
        loc: {
          start: {
            line: 306,
            column: 12
          },
          end: {
            line: 306,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 306,
            column: 12
          },
          end: {
            line: 306,
            column: 23
          }
        }, {
          start: {
            line: 306,
            column: 27
          },
          end: {
            line: 306,
            column: 43
          }
        }],
        line: 306
      },
      "33": {
        loc: {
          start: {
            line: 315,
            column: 12
          },
          end: {
            line: 320,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 315,
            column: 12
          },
          end: {
            line: 320,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 315
      },
      "34": {
        loc: {
          start: {
            line: 315,
            column: 16
          },
          end: {
            line: 315,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 315,
            column: 16
          },
          end: {
            line: 315,
            column: 27
          }
        }, {
          start: {
            line: 315,
            column: 31
          },
          end: {
            line: 315,
            column: 53
          }
        }],
        line: 315
      },
      "35": {
        loc: {
          start: {
            line: 325,
            column: 15
          },
          end: {
            line: 325,
            column: 55
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 325,
            column: 15
          },
          end: {
            line: 325,
            column: 49
          }
        }, {
          start: {
            line: 325,
            column: 53
          },
          end: {
            line: 325,
            column: 55
          }
        }],
        line: 325
      },
      "36": {
        loc: {
          start: {
            line: 331,
            column: 8
          },
          end: {
            line: 333,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 331,
            column: 8
          },
          end: {
            line: 333,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 331
      },
      "37": {
        loc: {
          start: {
            line: 334,
            column: 8
          },
          end: {
            line: 336,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 334,
            column: 8
          },
          end: {
            line: 336,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 334
      },
      "38": {
        loc: {
          start: {
            line: 350,
            column: 12
          },
          end: {
            line: 352,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 350,
            column: 12
          },
          end: {
            line: 352,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 350
      },
      "39": {
        loc: {
          start: {
            line: 358,
            column: 8
          },
          end: {
            line: 361,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 358,
            column: 8
          },
          end: {
            line: 361,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 358
      },
      "40": {
        loc: {
          start: {
            line: 368,
            column: 8
          },
          end: {
            line: 371,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 368,
            column: 8
          },
          end: {
            line: 371,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 368
      },
      "41": {
        loc: {
          start: {
            line: 368,
            column: 12
          },
          end: {
            line: 368,
            column: 58
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 368,
            column: 12
          },
          end: {
            line: 368,
            column: 33
          }
        }, {
          start: {
            line: 368,
            column: 37
          },
          end: {
            line: 368,
            column: 58
          }
        }],
        line: 368
      },
      "42": {
        loc: {
          start: {
            line: 370,
            column: 19
          },
          end: {
            line: 370,
            column: 60
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 370,
            column: 51
          },
          end: {
            line: 370,
            column: 54
          }
        }, {
          start: {
            line: 370,
            column: 57
          },
          end: {
            line: 370,
            column: 60
          }
        }],
        line: 370
      },
      "43": {
        loc: {
          start: {
            line: 372,
            column: 8
          },
          end: {
            line: 381,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 372,
            column: 8
          },
          end: {
            line: 381,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 372
      },
      "44": {
        loc: {
          start: {
            line: 372,
            column: 12
          },
          end: {
            line: 372,
            column: 54
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 372,
            column: 12
          },
          end: {
            line: 372,
            column: 31
          }
        }, {
          start: {
            line: 372,
            column: 35
          },
          end: {
            line: 372,
            column: 54
          }
        }],
        line: 372
      },
      "45": {
        loc: {
          start: {
            line: 377,
            column: 24
          },
          end: {
            line: 377,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 377,
            column: 24
          },
          end: {
            line: 377,
            column: 39
          }
        }, {
          start: {
            line: 377,
            column: 43
          },
          end: {
            line: 377,
            column: 44
          }
        }],
        line: 377
      },
      "46": {
        loc: {
          start: {
            line: 377,
            column: 49
          },
          end: {
            line: 377,
            column: 69
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 377,
            column: 49
          },
          end: {
            line: 377,
            column: 64
          }
        }, {
          start: {
            line: 377,
            column: 68
          },
          end: {
            line: 377,
            column: 69
          }
        }],
        line: 377
      },
      "47": {
        loc: {
          start: {
            line: 378,
            column: 24
          },
          end: {
            line: 378,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 378,
            column: 24
          },
          end: {
            line: 378,
            column: 39
          }
        }, {
          start: {
            line: 378,
            column: 43
          },
          end: {
            line: 378,
            column: 44
          }
        }],
        line: 378
      },
      "48": {
        loc: {
          start: {
            line: 378,
            column: 49
          },
          end: {
            line: 378,
            column: 69
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 378,
            column: 49
          },
          end: {
            line: 378,
            column: 64
          }
        }, {
          start: {
            line: 378,
            column: 68
          },
          end: {
            line: 378,
            column: 69
          }
        }],
        line: 378
      },
      "49": {
        loc: {
          start: {
            line: 388,
            column: 4
          },
          end: {
            line: 390,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 388,
            column: 4
          },
          end: {
            line: 390,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 388
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\frontend\\lib\\services\\CollaborationService.ts",
      mappings: ";AAAA;;;;;;;;;;;;;GAaG;;;AA0eH,0DAKC;AA7eD,uDAA8C;AAC9C,+BAAoC;AAsDpC,MAAa,oBAAoB;IAW/B;QAVQ,WAAM,GAAkB,IAAI,CAAC;QAC7B,gBAAW,GAA6B,IAAI,CAAC;QAE7C,mBAAc,GAAgB,EAAE,CAAC;QAEjC,mBAAc,GAA4B,IAAI,GAAG,EAAE,CAAC;QACpD,sBAAiB,GAAG,CAAC,CAAC;QACtB,yBAAoB,GAAG,CAAC,CAAC;QACzB,sBAAiB,GAA0B,IAAI,CAAC;QAGtD,IAAI,CAAC,KAAK,GAAG;YACX,WAAW,EAAE,KAAK;YAClB,WAAW,EAAE,EAAE;YACf,iBAAiB,EAAE,EAAE;YACrB,eAAe,EAAE,EAAE;YACnB,cAAc,EAAE,KAAK;YACrB,YAAY,EAAE,IAAI,IAAI,EAAE;SACzB,CAAC;QAEF,IAAI,CAAC,oBAAoB,GAAG,IAAI,oBAAoB,EAAE,CAAC;QACvD,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CAAC,IAAuB,EAAE,SAAkB;QAC1D,IAAI,CAAC;YACH,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;YAExB,kCAAkC;YAClC,IAAI,CAAC,MAAM,GAAG,IAAA,qBAAE,EAAC,SAAS,IAAI,OAAO,CAAC,GAAG,CAAC,gCAAgC,IAAI,qBAAqB,EAAE;gBACnG,IAAI,EAAE;oBACJ,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,KAAK,EAAE,MAAM,IAAI,CAAC,YAAY,EAAE;iBACjC;gBACD,UAAU,EAAE,CAAC,WAAW,EAAE,SAAS,CAAC;gBACpC,OAAO,EAAE,KAAK;gBACd,YAAY,EAAE,IAAI;gBAClB,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;gBAC/C,iBAAiB,EAAE,IAAI;aACxB,CAAC,CAAC;YAEH,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAEhC,kBAAkB;YAClB,IAAI,CAAC,cAAc,EAAE,CAAC;QAExB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,6CAA6C,EAAE,KAAK,CAAC,CAAC;YACpE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,UAAkB,EAAE,SAAiB;QACtD,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtC,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;QAC3D,CAAC;QAED,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,IAAI,CAAC,MAAO,CAAC,IAAI,CAAC,eAAe,EAAE;gBACjC,UAAU;gBACV,SAAS;gBACT,IAAI,EAAE,IAAI,CAAC,WAAW;aACvB,EAAE,CAAC,QAAgF,EAAE,EAAE;gBACtF,IAAI,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC;oBAC1C,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,QAAQ,CAAC,QAAQ,CAAC;oBAC/C,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;oBAChF,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC;oBAChD,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBAC7B,CAAC;qBAAM,CAAC;oBACN,MAAM,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,IAAI,yBAAyB,CAAC,CAAC,CAAC;gBACjE,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa;QACjB,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;YAChD,OAAO;QACT,CAAC;QAED,6CAA6C;QAC7C,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAEnC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACjC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,EAAE;YACzC,MAAM,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE;SAC7B,CAAC,CAAC;QAEH,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,SAAS,CAAC;QACvC,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,EAAE,CAAC;QAC5B,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;QACzB,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,SAAyD;QAC5E,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;YACrD,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;QACrD,CAAC;QAED,MAAM,aAAa,GAAc;YAC/B,GAAG,SAAS;YACZ,EAAE,EAAE,IAAA,SAAM,GAAE;YACZ,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,EAAE;YAC3B,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,gCAAgC;QAChC,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC/C,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,aAAa,CAAC,CAAC;QAE9C,yBAAyB;QACzB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAExC,2DAA2D;QAC3D,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;YAC3B,MAAM,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,QAAsD;QACjE,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACtC,OAAO;QACT,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,QAAQ,CAAC;QAEnC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,EAAE;YAChC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,EAAE;YAC1C,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,EAAE;YAC3B,MAAM,EAAE,QAAQ;SACjB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,IAAa;QAC9B,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACrE,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,IAAI,CAAC,MAAO,CAAC,IAAI,CAAC,eAAe,EAAE;gBACjC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,eAAgB,CAAC,EAAE;gBAC1C,MAAM,EAAE,IAAI,CAAC,WAAY,CAAC,EAAE;gBAC5B,IAAI;aACL,EAAE,CAAC,QAA8B,EAAE,EAAE;gBACpC,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;oBACrB,IAAI,CAAC,KAAK,CAAC,eAAgB,CAAC,QAAQ,GAAG,IAAI,CAAC;oBAC5C,IAAI,CAAC,KAAK,CAAC,eAAgB,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,WAAY,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;oBAC/E,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,WAAY,CAAC,EAAE,EAAE,CAAC,CAAC;gBACrF,CAAC;gBACD,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YAC5B,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,qBAAqB;QAOnB,OAAO;YACL,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM;YAC1C,eAAe,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,MAAM;YAClD,iBAAiB,EAAE,IAAI,CAAC,cAAc,CAAC,MAAM;YAC7C,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY;YACrC,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW;SACpC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,EAAE,CAAC,KAAa,EAAE,QAAkB;QAClC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;YACpC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QACrC,CAAC;QACD,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACjD,CAAC;IAED,GAAG,CAAC,KAAa,EAAE,QAAkB;QACnC,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACjD,IAAI,SAAS,EAAE,CAAC;YACd,MAAM,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAC1C,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;gBACf,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAC7B,CAAC;QACH,CAAC;IACH,CAAC;IAEO,IAAI,CAAC,KAAa,EAAE,IAAU;QACpC,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACjD,IAAI,SAAS,EAAE,CAAC;YACd,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;QAChD,CAAC;IACH,CAAC;IAEO,wBAAwB;QAC9B,IAAI,CAAC,IAAI,CAAC,MAAM;YAAE,OAAO;QAEzB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,EAAE,GAAG,EAAE;YAC7B,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;YAC9B,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,KAAK,CAAC;YAClC,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;YAC3B,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAEvB,0BAA0B;YAC1B,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,YAAY,EAAE,GAAG,EAAE;YAChC,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC;YAC/B,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,mBAAmB,EAAE,GAAG,EAAE;YACvC,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,IAAI,CAAC;YACjC,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,oBAAoB,EAAE,CAAC,SAAoB,EAAE,EAAE;YAC5D,IAAI,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC,IAAuB,EAAE,EAAE;YACxD,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,WAAW,EAAE,CAAC,MAAc,EAAE,EAAE;YAC7C,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC,CAAC;YAC7E,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,gBAAgB,EAAE,CAAC,IAAqC,EAAE,EAAE;YACzE,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,MAAM,CAAC,CAAC;YACpE,IAAI,IAAI,EAAE,CAAC;gBACT,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;gBAC1B,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;YACpC,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,iBAAiB,EAAE,CAAC,IAAyC,EAAE,EAAE;YAC9E,IAAI,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;gBAC/B,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC;gBAClD,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC;gBAC5E,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,IAAI,CAAC,CAAC;YAC3C,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,SAAoB;QAC9C,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;YAChD,OAAO;QACT,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE;YAC5B,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,EAAE;YACzC,SAAS;SACV,EAAE,CAAC,QAA8C,EAAE,EAAE;YACpD,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACrB,oBAAoB;gBACpB,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,SAAS,CAAC,EAAE,CAAC,CAAC;gBAC1E,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;oBACf,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;gBACvC,CAAC;gBACD,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC;YACvC,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;YAC7D,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,qBAAqB,CAAC,SAAoB;QAChD,+CAA+C;QAC/C,MAAM,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAC9D,SAAS,EACT,IAAI,CAAC,KAAK,CAAC,eAAe,CAC3B,CAAC;QAEF,8BAA8B;QAC9B,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,oBAAoB,CAAC,CAAC;QAEpD,0BAA0B;QAC1B,IAAI,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;YAC/B,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;YACrC,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QACnE,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,qBAAqB;QACjC,MAAM,UAAU,GAAG,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC;QAC5C,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;YACnC,MAAM,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;QACtC,CAAC;IACH,CAAC;IAEO,kBAAkB;QACxB,iCAAiC;QACjC,QAAQ,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,GAAG,EAAE;YACjD,IAAI,QAAQ,CAAC,MAAM,EAAE,CAAC;gBACpB,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;YAC/B,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;YAC9B,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,sBAAsB;QACtB,MAAM,CAAC,gBAAgB,CAAC,cAAc,EAAE,GAAG,EAAE;YAC3C,IAAI,CAAC,aAAa,EAAE,CAAC;QACvB,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,gBAAgB,CAAC,QAAiB;QACxC,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACpC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE;gBAC9B,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,EAAE;gBAC3B,QAAQ;aACT,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAEO,cAAc;QACpB,IAAI,CAAC,iBAAiB,GAAG,WAAW,CAAC,GAAG,EAAE;YACxC,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;gBAC1C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE;oBAC5B,MAAM,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE;oBAC5B,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC,CAAC;YACL,CAAC;QACH,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,aAAa;IAC1B,CAAC;IAEO,KAAK,CAAC,YAAY;QACxB,6CAA6C;QAC7C,OAAO,YAAY,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;IAClD,CAAC;IAED;;OAEG;IACH,OAAO;QACL,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC3B,aAAa,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACxC,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;QAC3B,CAAC;QAED,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;IAC9B,CAAC;CACF;AAvXD,oDAuXC;AAED;;;GAGG;AACH,MAAM,oBAAoB;IACxB,SAAS,CAAC,SAAoB,EAAE,eAA4B;QAC1D,IAAI,aAAa,GAAG,EAAE,GAAG,SAAS,EAAE,CAAC;QAErC,sDAAsD;QACtD,KAAK,MAAM,OAAO,IAAI,eAAe,EAAE,CAAC;YACtC,IAAI,OAAO,CAAC,SAAS,GAAG,SAAS,CAAC,SAAS,EAAE,CAAC;gBAC5C,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;YAChE,CAAC;QACH,CAAC;QAED,OAAO,aAAa,CAAC;IACvB,CAAC;IAEO,gBAAgB,CAAC,GAAc,EAAE,GAAc;QACrD,0DAA0D;QAC1D,IAAI,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC,SAAS,EAAE,CAAC;YACpC,6CAA6C;YAC7C,OAAO,IAAI,CAAC,oBAAoB,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;QAC7C,CAAC;QAED,kDAAkD;QAClD,OAAO,GAAG,CAAC;IACb,CAAC;IAEO,oBAAoB,CAAC,GAAc,EAAE,GAAc;QACzD,0CAA0C;QAC1C,2EAA2E;QAE3E,IAAI,GAAG,CAAC,IAAI,KAAK,QAAQ,IAAI,GAAG,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;YACnD,+BAA+B;YAC/B,OAAO,GAAG,CAAC,SAAS,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QACnD,CAAC;QAED,IAAI,GAAG,CAAC,IAAI,KAAK,MAAM,IAAI,GAAG,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YAC/C,2BAA2B;YAC3B,OAAO;gBACL,GAAG,GAAG;gBACN,QAAQ,EAAE;oBACR,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,CAAC;oBAClD,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,CAAC;iBACnD;aACF,CAAC;QACJ,CAAC;QAED,OAAO,GAAG,CAAC;IACb,CAAC;CACF;AAED,qBAAqB;AACrB,IAAI,oBAAoB,GAAgC,IAAI,CAAC;AAE7D,SAAgB,uBAAuB;IACrC,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC1B,oBAAoB,GAAG,IAAI,oBAAoB,EAAE,CAAC;IACpD,CAAC;IACD,OAAO,oBAAoB,CAAC;AAC9B,CAAC;AAED,kBAAe,oBAAoB,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\frontend\\lib\\services\\CollaborationService.ts"],
      sourcesContent: ["/**\r\n * Real-time Collaboration Service for SizeWise Suite\r\n * \r\n * Implements WebSocket-based collaboration with operational transformation\r\n * and conflict resolution for multi-user HVAC design workflows.\r\n * \r\n * Features:\r\n * - Real-time document synchronization\r\n * - Operational transformation for conflict resolution\r\n * - User presence and cursor tracking\r\n * - Collaborative editing with undo/redo\r\n * - Permission-based access control\r\n * - Offline-first with sync on reconnection\r\n */\r\n\r\nimport { io, Socket } from 'socket.io-client';\r\nimport { v4 as uuidv4 } from 'uuid';\r\n\r\n// Types for collaboration\r\nexport interface CollaborationUser {\r\n  id: string;\r\n  name: string;\r\n  email: string;\r\n  avatar?: string;\r\n  color: string;\r\n  cursor?: {\r\n    x: number;\r\n    y: number;\r\n    elementId?: string;\r\n  };\r\n  lastSeen: Date;\r\n  isOnline: boolean;\r\n}\r\n\r\nexport interface Operation {\r\n  id: string;\r\n  type: 'insert' | 'delete' | 'update' | 'move' | 'style';\r\n  userId: string;\r\n  timestamp: Date;\r\n  elementId: string;\r\n  path: string[];\r\n  oldValue?: any;\r\n  newValue?: any;\r\n  position?: { x: number; y: number };\r\n  metadata?: Record<string, any>;\r\n}\r\n\r\nexport interface CollaborationDocument {\r\n  id: string;\r\n  projectId: string;\r\n  type: 'hvac_design' | 'calculation' | 'report';\r\n  version: number;\r\n  operations: Operation[];\r\n  participants: CollaborationUser[];\r\n  permissions: Record<string, 'read' | 'write' | 'admin'>;\r\n  lastModified: Date;\r\n  isLocked?: boolean;\r\n  lockedBy?: string;\r\n}\r\n\r\nexport interface CollaborationState {\r\n  isConnected: boolean;\r\n  currentDocument?: CollaborationDocument;\r\n  activeUsers: CollaborationUser[];\r\n  pendingOperations: Operation[];\r\n  localOperations: Operation[];\r\n  isReconnecting: boolean;\r\n  lastSyncTime: Date;\r\n}\r\n\r\nexport class CollaborationService {\r\n  private socket: Socket | null = null;\r\n  private currentUser: CollaborationUser | null = null;\r\n  private state: CollaborationState;\r\n  private operationQueue: Operation[] = [];\r\n  private transformationEngine: OperationalTransform;\r\n  private eventListeners: Map<string, Function[]> = new Map();\r\n  private reconnectAttempts = 0;\r\n  private maxReconnectAttempts = 5;\r\n  private heartbeatInterval: NodeJS.Timeout | null = null;\r\n\r\n  constructor() {\r\n    this.state = {\r\n      isConnected: false,\r\n      activeUsers: [],\r\n      pendingOperations: [],\r\n      localOperations: [],\r\n      isReconnecting: false,\r\n      lastSyncTime: new Date()\r\n    };\r\n    \r\n    this.transformationEngine = new OperationalTransform();\r\n    this.setupEventHandlers();\r\n  }\r\n\r\n  /**\r\n   * Initialize collaboration service with user authentication\r\n   */\r\n  async initialize(user: CollaborationUser, serverUrl?: string): Promise<void> {\r\n    try {\r\n      this.currentUser = user;\r\n      \r\n      // Initialize WebSocket connection\r\n      this.socket = io(serverUrl || process.env.NEXT_PUBLIC_COLLABORATION_SERVER || 'ws://localhost:3001', {\r\n        auth: {\r\n          userId: user.id,\r\n          token: await this.getAuthToken()\r\n        },\r\n        transports: ['websocket', 'polling'],\r\n        timeout: 10000,\r\n        reconnection: true,\r\n        reconnectionAttempts: this.maxReconnectAttempts,\r\n        reconnectionDelay: 1000\r\n      });\r\n\r\n      this.setupSocketEventHandlers();\r\n      \r\n      // Start heartbeat\r\n      this.startHeartbeat();\r\n      \r\n    } catch (error) {\r\n      console.error('Failed to initialize collaboration service:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Join a collaborative document session\r\n   */\r\n  async joinDocument(documentId: string, projectId: string): Promise<CollaborationDocument> {\r\n    if (!this.socket || !this.currentUser) {\r\n      throw new Error('Collaboration service not initialized');\r\n    }\r\n\r\n    return new Promise((resolve, reject) => {\r\n      this.socket!.emit('join_document', {\r\n        documentId,\r\n        projectId,\r\n        user: this.currentUser\r\n      }, (response: { success: boolean; document?: CollaborationDocument; error?: string }) => {\r\n        if (response.success && response.document) {\r\n          this.state.currentDocument = response.document;\r\n          this.state.activeUsers = response.document.participants.filter(u => u.isOnline);\r\n          this.emit('document_joined', response.document);\r\n          resolve(response.document);\r\n        } else {\r\n          reject(new Error(response.error || 'Failed to join document'));\r\n        }\r\n      });\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Leave current document session\r\n   */\r\n  async leaveDocument(): Promise<void> {\r\n    if (!this.socket || !this.state.currentDocument) {\r\n      return;\r\n    }\r\n\r\n    // Sync any pending operations before leaving\r\n    await this.syncPendingOperations();\r\n\r\n    this.socket.emit('leave_document', {\r\n      documentId: this.state.currentDocument.id,\r\n      userId: this.currentUser?.id\r\n    });\r\n\r\n    this.state.currentDocument = undefined;\r\n    this.state.activeUsers = [];\r\n    this.operationQueue = [];\r\n    this.emit('document_left');\r\n  }\r\n\r\n  /**\r\n   * Apply a local operation and broadcast to other users\r\n   */\r\n  async applyOperation(operation: Omit<Operation, 'id' | 'userId' | 'timestamp'>): Promise<void> {\r\n    if (!this.currentUser || !this.state.currentDocument) {\r\n      throw new Error('No active collaboration session');\r\n    }\r\n\r\n    const fullOperation: Operation = {\r\n      ...operation,\r\n      id: uuidv4(),\r\n      userId: this.currentUser.id,\r\n      timestamp: new Date()\r\n    };\r\n\r\n    // Apply operation locally first\r\n    this.state.localOperations.push(fullOperation);\r\n    this.emit('operation_applied', fullOperation);\r\n\r\n    // Queue for transmission\r\n    this.operationQueue.push(fullOperation);\r\n\r\n    // Send immediately if connected, otherwise queue for later\r\n    if (this.state.isConnected) {\r\n      await this.sendOperation(fullOperation);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update user cursor position\r\n   */\r\n  updateCursor(position: { x: number; y: number; elementId?: string }): void {\r\n    if (!this.currentUser || !this.socket) {\r\n      return;\r\n    }\r\n\r\n    this.currentUser.cursor = position;\r\n    \r\n    this.socket.emit('cursor_update', {\r\n      documentId: this.state.currentDocument?.id,\r\n      userId: this.currentUser.id,\r\n      cursor: position\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Lock/unlock document for exclusive editing\r\n   */\r\n  async lockDocument(lock: boolean): Promise<boolean> {\r\n    if (!this.socket || !this.state.currentDocument || !this.currentUser) {\r\n      return false;\r\n    }\r\n\r\n    return new Promise((resolve) => {\r\n      this.socket!.emit('document_lock', {\r\n        documentId: this.state.currentDocument!.id,\r\n        userId: this.currentUser!.id,\r\n        lock\r\n      }, (response: { success: boolean }) => {\r\n        if (response.success) {\r\n          this.state.currentDocument!.isLocked = lock;\r\n          this.state.currentDocument!.lockedBy = lock ? this.currentUser!.id : undefined;\r\n          this.emit('document_lock_changed', { locked: lock, userId: this.currentUser!.id });\r\n        }\r\n        resolve(response.success);\r\n      });\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Get collaboration statistics\r\n   */\r\n  getCollaborationStats(): {\r\n    activeUsers: number;\r\n    totalOperations: number;\r\n    pendingOperations: number;\r\n    lastSyncTime: Date;\r\n    isConnected: boolean;\r\n  } {\r\n    return {\r\n      activeUsers: this.state.activeUsers.length,\r\n      totalOperations: this.state.localOperations.length,\r\n      pendingOperations: this.operationQueue.length,\r\n      lastSyncTime: this.state.lastSyncTime,\r\n      isConnected: this.state.isConnected\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Event listener management\r\n   */\r\n  on(event: string, callback: Function): void {\r\n    if (!this.eventListeners.has(event)) {\r\n      this.eventListeners.set(event, []);\r\n    }\r\n    this.eventListeners.get(event)!.push(callback);\r\n  }\r\n\r\n  off(event: string, callback: Function): void {\r\n    const listeners = this.eventListeners.get(event);\r\n    if (listeners) {\r\n      const index = listeners.indexOf(callback);\r\n      if (index > -1) {\r\n        listeners.splice(index, 1);\r\n      }\r\n    }\r\n  }\r\n\r\n  private emit(event: string, data?: any): void {\r\n    const listeners = this.eventListeners.get(event);\r\n    if (listeners) {\r\n      listeners.forEach(callback => callback(data));\r\n    }\r\n  }\r\n\r\n  private setupSocketEventHandlers(): void {\r\n    if (!this.socket) return;\r\n\r\n    this.socket.on('connect', () => {\r\n      this.state.isConnected = true;\r\n      this.state.isReconnecting = false;\r\n      this.reconnectAttempts = 0;\r\n      this.emit('connected');\r\n      \r\n      // Sync pending operations\r\n      this.syncPendingOperations();\r\n    });\r\n\r\n    this.socket.on('disconnect', () => {\r\n      this.state.isConnected = false;\r\n      this.emit('disconnected');\r\n    });\r\n\r\n    this.socket.on('reconnect_attempt', () => {\r\n      this.state.isReconnecting = true;\r\n      this.reconnectAttempts++;\r\n      this.emit('reconnecting', this.reconnectAttempts);\r\n    });\r\n\r\n    this.socket.on('operation_received', (operation: Operation) => {\r\n      this.handleRemoteOperation(operation);\r\n    });\r\n\r\n    this.socket.on('user_joined', (user: CollaborationUser) => {\r\n      this.state.activeUsers.push(user);\r\n      this.emit('user_joined', user);\r\n    });\r\n\r\n    this.socket.on('user_left', (userId: string) => {\r\n      this.state.activeUsers = this.state.activeUsers.filter(u => u.id !== userId);\r\n      this.emit('user_left', userId);\r\n    });\r\n\r\n    this.socket.on('cursor_updated', (data: { userId: string; cursor: any }) => {\r\n      const user = this.state.activeUsers.find(u => u.id === data.userId);\r\n      if (user) {\r\n        user.cursor = data.cursor;\r\n        this.emit('cursor_updated', data);\r\n      }\r\n    });\r\n\r\n    this.socket.on('document_locked', (data: { locked: boolean; userId: string }) => {\r\n      if (this.state.currentDocument) {\r\n        this.state.currentDocument.isLocked = data.locked;\r\n        this.state.currentDocument.lockedBy = data.locked ? data.userId : undefined;\r\n        this.emit('document_lock_changed', data);\r\n      }\r\n    });\r\n  }\r\n\r\n  private async sendOperation(operation: Operation): Promise<void> {\r\n    if (!this.socket || !this.state.currentDocument) {\r\n      return;\r\n    }\r\n\r\n    this.socket.emit('operation', {\r\n      documentId: this.state.currentDocument.id,\r\n      operation\r\n    }, (response: { success: boolean; error?: string }) => {\r\n      if (response.success) {\r\n        // Remove from queue\r\n        const index = this.operationQueue.findIndex(op => op.id === operation.id);\r\n        if (index > -1) {\r\n          this.operationQueue.splice(index, 1);\r\n        }\r\n        this.state.lastSyncTime = new Date();\r\n      } else {\r\n        console.error('Failed to send operation:', response.error);\r\n      }\r\n    });\r\n  }\r\n\r\n  private handleRemoteOperation(operation: Operation): void {\r\n    // Transform operation against local operations\r\n    const transformedOperation = this.transformationEngine.transform(\r\n      operation,\r\n      this.state.localOperations\r\n    );\r\n\r\n    // Apply transformed operation\r\n    this.emit('remote_operation', transformedOperation);\r\n    \r\n    // Update document version\r\n    if (this.state.currentDocument) {\r\n      this.state.currentDocument.version++;\r\n      this.state.currentDocument.operations.push(transformedOperation);\r\n    }\r\n  }\r\n\r\n  private async syncPendingOperations(): Promise<void> {\r\n    const operations = [...this.operationQueue];\r\n    for (const operation of operations) {\r\n      await this.sendOperation(operation);\r\n    }\r\n  }\r\n\r\n  private setupEventHandlers(): void {\r\n    // Handle page visibility changes\r\n    document.addEventListener('visibilitychange', () => {\r\n      if (document.hidden) {\r\n        this.updateUserStatus(false);\r\n      } else {\r\n        this.updateUserStatus(true);\r\n      }\r\n    });\r\n\r\n    // Handle beforeunload\r\n    window.addEventListener('beforeunload', () => {\r\n      this.leaveDocument();\r\n    });\r\n  }\r\n\r\n  private updateUserStatus(isActive: boolean): void {\r\n    if (this.socket && this.currentUser) {\r\n      this.socket.emit('user_status', {\r\n        userId: this.currentUser.id,\r\n        isActive\r\n      });\r\n    }\r\n  }\r\n\r\n  private startHeartbeat(): void {\r\n    this.heartbeatInterval = setInterval(() => {\r\n      if (this.socket && this.state.isConnected) {\r\n        this.socket.emit('heartbeat', {\r\n          userId: this.currentUser?.id,\r\n          timestamp: new Date()\r\n        });\r\n      }\r\n    }, 30000); // 30 seconds\r\n  }\r\n\r\n  private async getAuthToken(): Promise<string> {\r\n    // Implementation depends on your auth system\r\n    return localStorage.getItem('auth_token') || '';\r\n  }\r\n\r\n  /**\r\n   * Cleanup resources\r\n   */\r\n  destroy(): void {\r\n    if (this.heartbeatInterval) {\r\n      clearInterval(this.heartbeatInterval);\r\n    }\r\n    \r\n    if (this.socket) {\r\n      this.socket.disconnect();\r\n    }\r\n    \r\n    this.eventListeners.clear();\r\n  }\r\n}\r\n\r\n/**\r\n * Operational Transformation Engine\r\n * Handles conflict resolution for concurrent operations\r\n */\r\nclass OperationalTransform {\r\n  transform(operation: Operation, localOperations: Operation[]): Operation {\r\n    let transformedOp = { ...operation };\r\n    \r\n    // Apply transformation rules based on operation types\r\n    for (const localOp of localOperations) {\r\n      if (localOp.timestamp > operation.timestamp) {\r\n        transformedOp = this.transformAgainst(transformedOp, localOp);\r\n      }\r\n    }\r\n    \r\n    return transformedOp;\r\n  }\r\n\r\n  private transformAgainst(op1: Operation, op2: Operation): Operation {\r\n    // Implement transformation rules based on operation types\r\n    if (op1.elementId === op2.elementId) {\r\n      // Same element - need careful transformation\r\n      return this.transformSameElement(op1, op2);\r\n    }\r\n    \r\n    // Different elements - operations are independent\r\n    return op1;\r\n  }\r\n\r\n  private transformSameElement(op1: Operation, op2: Operation): Operation {\r\n    // Implement specific transformation logic\r\n    // This is a simplified version - real implementation would be more complex\r\n    \r\n    if (op1.type === 'update' && op2.type === 'update') {\r\n      // Last writer wins for updates\r\n      return op1.timestamp > op2.timestamp ? op1 : op2;\r\n    }\r\n    \r\n    if (op1.type === 'move' && op2.type === 'move') {\r\n      // Combine position changes\r\n      return {\r\n        ...op1,\r\n        position: {\r\n          x: (op1.position?.x || 0) + (op2.position?.x || 0),\r\n          y: (op1.position?.y || 0) + (op2.position?.y || 0)\r\n        }\r\n      };\r\n    }\r\n    \r\n    return op1;\r\n  }\r\n}\r\n\r\n// Singleton instance\r\nlet collaborationService: CollaborationService | null = null;\r\n\r\nexport function getCollaborationService(): CollaborationService {\r\n  if (!collaborationService) {\r\n    collaborationService = new CollaborationService();\r\n  }\r\n  return collaborationService;\r\n}\r\n\r\nexport default CollaborationService;\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "75a0823fdbdf4b474330abf988e1e389d5ab0404"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_4djw3z3h9 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_4djw3z3h9();
cov_4djw3z3h9().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_4djw3z3h9().s[1]++;
exports.CollaborationService = void 0;
/* istanbul ignore next */
cov_4djw3z3h9().s[2]++;
exports.getCollaborationService = getCollaborationService;
const socket_io_client_1 =
/* istanbul ignore next */
(cov_4djw3z3h9().s[3]++, require("socket.io-client"));
const uuid_1 =
/* istanbul ignore next */
(cov_4djw3z3h9().s[4]++, require("uuid"));
class CollaborationService {
  constructor() {
    /* istanbul ignore next */
    cov_4djw3z3h9().f[0]++;
    cov_4djw3z3h9().s[5]++;
    this.socket = null;
    /* istanbul ignore next */
    cov_4djw3z3h9().s[6]++;
    this.currentUser = null;
    /* istanbul ignore next */
    cov_4djw3z3h9().s[7]++;
    this.operationQueue = [];
    /* istanbul ignore next */
    cov_4djw3z3h9().s[8]++;
    this.eventListeners = new Map();
    /* istanbul ignore next */
    cov_4djw3z3h9().s[9]++;
    this.reconnectAttempts = 0;
    /* istanbul ignore next */
    cov_4djw3z3h9().s[10]++;
    this.maxReconnectAttempts = 5;
    /* istanbul ignore next */
    cov_4djw3z3h9().s[11]++;
    this.heartbeatInterval = null;
    /* istanbul ignore next */
    cov_4djw3z3h9().s[12]++;
    this.state = {
      isConnected: false,
      activeUsers: [],
      pendingOperations: [],
      localOperations: [],
      isReconnecting: false,
      lastSyncTime: new Date()
    };
    /* istanbul ignore next */
    cov_4djw3z3h9().s[13]++;
    this.transformationEngine = new OperationalTransform();
    /* istanbul ignore next */
    cov_4djw3z3h9().s[14]++;
    this.setupEventHandlers();
  }
  /**
   * Initialize collaboration service with user authentication
   */
  async initialize(user, serverUrl) {
    /* istanbul ignore next */
    cov_4djw3z3h9().f[1]++;
    cov_4djw3z3h9().s[15]++;
    try {
      /* istanbul ignore next */
      cov_4djw3z3h9().s[16]++;
      this.currentUser = user;
      // Initialize WebSocket connection
      /* istanbul ignore next */
      cov_4djw3z3h9().s[17]++;
      this.socket = (0, socket_io_client_1.io)(
      /* istanbul ignore next */
      (cov_4djw3z3h9().b[0][0]++, serverUrl) ||
      /* istanbul ignore next */
      (cov_4djw3z3h9().b[0][1]++, process.env.NEXT_PUBLIC_COLLABORATION_SERVER) ||
      /* istanbul ignore next */
      (cov_4djw3z3h9().b[0][2]++, 'ws://localhost:3001'), {
        auth: {
          userId: user.id,
          token: await this.getAuthToken()
        },
        transports: ['websocket', 'polling'],
        timeout: 10000,
        reconnection: true,
        reconnectionAttempts: this.maxReconnectAttempts,
        reconnectionDelay: 1000
      });
      /* istanbul ignore next */
      cov_4djw3z3h9().s[18]++;
      this.setupSocketEventHandlers();
      // Start heartbeat
      /* istanbul ignore next */
      cov_4djw3z3h9().s[19]++;
      this.startHeartbeat();
    } catch (error) {
      /* istanbul ignore next */
      cov_4djw3z3h9().s[20]++;
      console.error('Failed to initialize collaboration service:', error);
      /* istanbul ignore next */
      cov_4djw3z3h9().s[21]++;
      throw error;
    }
  }
  /**
   * Join a collaborative document session
   */
  async joinDocument(documentId, projectId) {
    /* istanbul ignore next */
    cov_4djw3z3h9().f[2]++;
    cov_4djw3z3h9().s[22]++;
    if (
    /* istanbul ignore next */
    (cov_4djw3z3h9().b[2][0]++, !this.socket) ||
    /* istanbul ignore next */
    (cov_4djw3z3h9().b[2][1]++, !this.currentUser)) {
      /* istanbul ignore next */
      cov_4djw3z3h9().b[1][0]++;
      cov_4djw3z3h9().s[23]++;
      throw new Error('Collaboration service not initialized');
    } else
    /* istanbul ignore next */
    {
      cov_4djw3z3h9().b[1][1]++;
    }
    cov_4djw3z3h9().s[24]++;
    return new Promise((resolve, reject) => {
      /* istanbul ignore next */
      cov_4djw3z3h9().f[3]++;
      cov_4djw3z3h9().s[25]++;
      this.socket.emit('join_document', {
        documentId,
        projectId,
        user: this.currentUser
      }, response => {
        /* istanbul ignore next */
        cov_4djw3z3h9().f[4]++;
        cov_4djw3z3h9().s[26]++;
        if (
        /* istanbul ignore next */
        (cov_4djw3z3h9().b[4][0]++, response.success) &&
        /* istanbul ignore next */
        (cov_4djw3z3h9().b[4][1]++, response.document)) {
          /* istanbul ignore next */
          cov_4djw3z3h9().b[3][0]++;
          cov_4djw3z3h9().s[27]++;
          this.state.currentDocument = response.document;
          /* istanbul ignore next */
          cov_4djw3z3h9().s[28]++;
          this.state.activeUsers = response.document.participants.filter(u => {
            /* istanbul ignore next */
            cov_4djw3z3h9().f[5]++;
            cov_4djw3z3h9().s[29]++;
            return u.isOnline;
          });
          /* istanbul ignore next */
          cov_4djw3z3h9().s[30]++;
          this.emit('document_joined', response.document);
          /* istanbul ignore next */
          cov_4djw3z3h9().s[31]++;
          resolve(response.document);
        } else {
          /* istanbul ignore next */
          cov_4djw3z3h9().b[3][1]++;
          cov_4djw3z3h9().s[32]++;
          reject(new Error(
          /* istanbul ignore next */
          (cov_4djw3z3h9().b[5][0]++, response.error) ||
          /* istanbul ignore next */
          (cov_4djw3z3h9().b[5][1]++, 'Failed to join document')));
        }
      });
    });
  }
  /**
   * Leave current document session
   */
  async leaveDocument() {
    /* istanbul ignore next */
    cov_4djw3z3h9().f[6]++;
    cov_4djw3z3h9().s[33]++;
    if (
    /* istanbul ignore next */
    (cov_4djw3z3h9().b[7][0]++, !this.socket) ||
    /* istanbul ignore next */
    (cov_4djw3z3h9().b[7][1]++, !this.state.currentDocument)) {
      /* istanbul ignore next */
      cov_4djw3z3h9().b[6][0]++;
      cov_4djw3z3h9().s[34]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_4djw3z3h9().b[6][1]++;
    }
    // Sync any pending operations before leaving
    cov_4djw3z3h9().s[35]++;
    await this.syncPendingOperations();
    /* istanbul ignore next */
    cov_4djw3z3h9().s[36]++;
    this.socket.emit('leave_document', {
      documentId: this.state.currentDocument.id,
      userId: this.currentUser?.id
    });
    /* istanbul ignore next */
    cov_4djw3z3h9().s[37]++;
    this.state.currentDocument = undefined;
    /* istanbul ignore next */
    cov_4djw3z3h9().s[38]++;
    this.state.activeUsers = [];
    /* istanbul ignore next */
    cov_4djw3z3h9().s[39]++;
    this.operationQueue = [];
    /* istanbul ignore next */
    cov_4djw3z3h9().s[40]++;
    this.emit('document_left');
  }
  /**
   * Apply a local operation and broadcast to other users
   */
  async applyOperation(operation) {
    /* istanbul ignore next */
    cov_4djw3z3h9().f[7]++;
    cov_4djw3z3h9().s[41]++;
    if (
    /* istanbul ignore next */
    (cov_4djw3z3h9().b[9][0]++, !this.currentUser) ||
    /* istanbul ignore next */
    (cov_4djw3z3h9().b[9][1]++, !this.state.currentDocument)) {
      /* istanbul ignore next */
      cov_4djw3z3h9().b[8][0]++;
      cov_4djw3z3h9().s[42]++;
      throw new Error('No active collaboration session');
    } else
    /* istanbul ignore next */
    {
      cov_4djw3z3h9().b[8][1]++;
    }
    const fullOperation =
    /* istanbul ignore next */
    (cov_4djw3z3h9().s[43]++, {
      ...operation,
      id: (0, uuid_1.v4)(),
      userId: this.currentUser.id,
      timestamp: new Date()
    });
    // Apply operation locally first
    /* istanbul ignore next */
    cov_4djw3z3h9().s[44]++;
    this.state.localOperations.push(fullOperation);
    /* istanbul ignore next */
    cov_4djw3z3h9().s[45]++;
    this.emit('operation_applied', fullOperation);
    // Queue for transmission
    /* istanbul ignore next */
    cov_4djw3z3h9().s[46]++;
    this.operationQueue.push(fullOperation);
    // Send immediately if connected, otherwise queue for later
    /* istanbul ignore next */
    cov_4djw3z3h9().s[47]++;
    if (this.state.isConnected) {
      /* istanbul ignore next */
      cov_4djw3z3h9().b[10][0]++;
      cov_4djw3z3h9().s[48]++;
      await this.sendOperation(fullOperation);
    } else
    /* istanbul ignore next */
    {
      cov_4djw3z3h9().b[10][1]++;
    }
  }
  /**
   * Update user cursor position
   */
  updateCursor(position) {
    /* istanbul ignore next */
    cov_4djw3z3h9().f[8]++;
    cov_4djw3z3h9().s[49]++;
    if (
    /* istanbul ignore next */
    (cov_4djw3z3h9().b[12][0]++, !this.currentUser) ||
    /* istanbul ignore next */
    (cov_4djw3z3h9().b[12][1]++, !this.socket)) {
      /* istanbul ignore next */
      cov_4djw3z3h9().b[11][0]++;
      cov_4djw3z3h9().s[50]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_4djw3z3h9().b[11][1]++;
    }
    cov_4djw3z3h9().s[51]++;
    this.currentUser.cursor = position;
    /* istanbul ignore next */
    cov_4djw3z3h9().s[52]++;
    this.socket.emit('cursor_update', {
      documentId: this.state.currentDocument?.id,
      userId: this.currentUser.id,
      cursor: position
    });
  }
  /**
   * Lock/unlock document for exclusive editing
   */
  async lockDocument(lock) {
    /* istanbul ignore next */
    cov_4djw3z3h9().f[9]++;
    cov_4djw3z3h9().s[53]++;
    if (
    /* istanbul ignore next */
    (cov_4djw3z3h9().b[14][0]++, !this.socket) ||
    /* istanbul ignore next */
    (cov_4djw3z3h9().b[14][1]++, !this.state.currentDocument) ||
    /* istanbul ignore next */
    (cov_4djw3z3h9().b[14][2]++, !this.currentUser)) {
      /* istanbul ignore next */
      cov_4djw3z3h9().b[13][0]++;
      cov_4djw3z3h9().s[54]++;
      return false;
    } else
    /* istanbul ignore next */
    {
      cov_4djw3z3h9().b[13][1]++;
    }
    cov_4djw3z3h9().s[55]++;
    return new Promise(resolve => {
      /* istanbul ignore next */
      cov_4djw3z3h9().f[10]++;
      cov_4djw3z3h9().s[56]++;
      this.socket.emit('document_lock', {
        documentId: this.state.currentDocument.id,
        userId: this.currentUser.id,
        lock
      }, response => {
        /* istanbul ignore next */
        cov_4djw3z3h9().f[11]++;
        cov_4djw3z3h9().s[57]++;
        if (response.success) {
          /* istanbul ignore next */
          cov_4djw3z3h9().b[15][0]++;
          cov_4djw3z3h9().s[58]++;
          this.state.currentDocument.isLocked = lock;
          /* istanbul ignore next */
          cov_4djw3z3h9().s[59]++;
          this.state.currentDocument.lockedBy = lock ?
          /* istanbul ignore next */
          (cov_4djw3z3h9().b[16][0]++, this.currentUser.id) :
          /* istanbul ignore next */
          (cov_4djw3z3h9().b[16][1]++, undefined);
          /* istanbul ignore next */
          cov_4djw3z3h9().s[60]++;
          this.emit('document_lock_changed', {
            locked: lock,
            userId: this.currentUser.id
          });
        } else
        /* istanbul ignore next */
        {
          cov_4djw3z3h9().b[15][1]++;
        }
        cov_4djw3z3h9().s[61]++;
        resolve(response.success);
      });
    });
  }
  /**
   * Get collaboration statistics
   */
  getCollaborationStats() {
    /* istanbul ignore next */
    cov_4djw3z3h9().f[12]++;
    cov_4djw3z3h9().s[62]++;
    return {
      activeUsers: this.state.activeUsers.length,
      totalOperations: this.state.localOperations.length,
      pendingOperations: this.operationQueue.length,
      lastSyncTime: this.state.lastSyncTime,
      isConnected: this.state.isConnected
    };
  }
  /**
   * Event listener management
   */
  on(event, callback) {
    /* istanbul ignore next */
    cov_4djw3z3h9().f[13]++;
    cov_4djw3z3h9().s[63]++;
    if (!this.eventListeners.has(event)) {
      /* istanbul ignore next */
      cov_4djw3z3h9().b[17][0]++;
      cov_4djw3z3h9().s[64]++;
      this.eventListeners.set(event, []);
    } else
    /* istanbul ignore next */
    {
      cov_4djw3z3h9().b[17][1]++;
    }
    cov_4djw3z3h9().s[65]++;
    this.eventListeners.get(event).push(callback);
  }
  off(event, callback) {
    /* istanbul ignore next */
    cov_4djw3z3h9().f[14]++;
    const listeners =
    /* istanbul ignore next */
    (cov_4djw3z3h9().s[66]++, this.eventListeners.get(event));
    /* istanbul ignore next */
    cov_4djw3z3h9().s[67]++;
    if (listeners) {
      /* istanbul ignore next */
      cov_4djw3z3h9().b[18][0]++;
      const index =
      /* istanbul ignore next */
      (cov_4djw3z3h9().s[68]++, listeners.indexOf(callback));
      /* istanbul ignore next */
      cov_4djw3z3h9().s[69]++;
      if (index > -1) {
        /* istanbul ignore next */
        cov_4djw3z3h9().b[19][0]++;
        cov_4djw3z3h9().s[70]++;
        listeners.splice(index, 1);
      } else
      /* istanbul ignore next */
      {
        cov_4djw3z3h9().b[19][1]++;
      }
    } else
    /* istanbul ignore next */
    {
      cov_4djw3z3h9().b[18][1]++;
    }
  }
  emit(event, data) {
    /* istanbul ignore next */
    cov_4djw3z3h9().f[15]++;
    const listeners =
    /* istanbul ignore next */
    (cov_4djw3z3h9().s[71]++, this.eventListeners.get(event));
    /* istanbul ignore next */
    cov_4djw3z3h9().s[72]++;
    if (listeners) {
      /* istanbul ignore next */
      cov_4djw3z3h9().b[20][0]++;
      cov_4djw3z3h9().s[73]++;
      listeners.forEach(callback => {
        /* istanbul ignore next */
        cov_4djw3z3h9().f[16]++;
        cov_4djw3z3h9().s[74]++;
        return callback(data);
      });
    } else
    /* istanbul ignore next */
    {
      cov_4djw3z3h9().b[20][1]++;
    }
  }
  setupSocketEventHandlers() {
    /* istanbul ignore next */
    cov_4djw3z3h9().f[17]++;
    cov_4djw3z3h9().s[75]++;
    if (!this.socket) {
      /* istanbul ignore next */
      cov_4djw3z3h9().b[21][0]++;
      cov_4djw3z3h9().s[76]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_4djw3z3h9().b[21][1]++;
    }
    cov_4djw3z3h9().s[77]++;
    this.socket.on('connect', () => {
      /* istanbul ignore next */
      cov_4djw3z3h9().f[18]++;
      cov_4djw3z3h9().s[78]++;
      this.state.isConnected = true;
      /* istanbul ignore next */
      cov_4djw3z3h9().s[79]++;
      this.state.isReconnecting = false;
      /* istanbul ignore next */
      cov_4djw3z3h9().s[80]++;
      this.reconnectAttempts = 0;
      /* istanbul ignore next */
      cov_4djw3z3h9().s[81]++;
      this.emit('connected');
      // Sync pending operations
      /* istanbul ignore next */
      cov_4djw3z3h9().s[82]++;
      this.syncPendingOperations();
    });
    /* istanbul ignore next */
    cov_4djw3z3h9().s[83]++;
    this.socket.on('disconnect', () => {
      /* istanbul ignore next */
      cov_4djw3z3h9().f[19]++;
      cov_4djw3z3h9().s[84]++;
      this.state.isConnected = false;
      /* istanbul ignore next */
      cov_4djw3z3h9().s[85]++;
      this.emit('disconnected');
    });
    /* istanbul ignore next */
    cov_4djw3z3h9().s[86]++;
    this.socket.on('reconnect_attempt', () => {
      /* istanbul ignore next */
      cov_4djw3z3h9().f[20]++;
      cov_4djw3z3h9().s[87]++;
      this.state.isReconnecting = true;
      /* istanbul ignore next */
      cov_4djw3z3h9().s[88]++;
      this.reconnectAttempts++;
      /* istanbul ignore next */
      cov_4djw3z3h9().s[89]++;
      this.emit('reconnecting', this.reconnectAttempts);
    });
    /* istanbul ignore next */
    cov_4djw3z3h9().s[90]++;
    this.socket.on('operation_received', operation => {
      /* istanbul ignore next */
      cov_4djw3z3h9().f[21]++;
      cov_4djw3z3h9().s[91]++;
      this.handleRemoteOperation(operation);
    });
    /* istanbul ignore next */
    cov_4djw3z3h9().s[92]++;
    this.socket.on('user_joined', user => {
      /* istanbul ignore next */
      cov_4djw3z3h9().f[22]++;
      cov_4djw3z3h9().s[93]++;
      this.state.activeUsers.push(user);
      /* istanbul ignore next */
      cov_4djw3z3h9().s[94]++;
      this.emit('user_joined', user);
    });
    /* istanbul ignore next */
    cov_4djw3z3h9().s[95]++;
    this.socket.on('user_left', userId => {
      /* istanbul ignore next */
      cov_4djw3z3h9().f[23]++;
      cov_4djw3z3h9().s[96]++;
      this.state.activeUsers = this.state.activeUsers.filter(u => {
        /* istanbul ignore next */
        cov_4djw3z3h9().f[24]++;
        cov_4djw3z3h9().s[97]++;
        return u.id !== userId;
      });
      /* istanbul ignore next */
      cov_4djw3z3h9().s[98]++;
      this.emit('user_left', userId);
    });
    /* istanbul ignore next */
    cov_4djw3z3h9().s[99]++;
    this.socket.on('cursor_updated', data => {
      /* istanbul ignore next */
      cov_4djw3z3h9().f[25]++;
      const user =
      /* istanbul ignore next */
      (cov_4djw3z3h9().s[100]++, this.state.activeUsers.find(u => {
        /* istanbul ignore next */
        cov_4djw3z3h9().f[26]++;
        cov_4djw3z3h9().s[101]++;
        return u.id === data.userId;
      }));
      /* istanbul ignore next */
      cov_4djw3z3h9().s[102]++;
      if (user) {
        /* istanbul ignore next */
        cov_4djw3z3h9().b[22][0]++;
        cov_4djw3z3h9().s[103]++;
        user.cursor = data.cursor;
        /* istanbul ignore next */
        cov_4djw3z3h9().s[104]++;
        this.emit('cursor_updated', data);
      } else
      /* istanbul ignore next */
      {
        cov_4djw3z3h9().b[22][1]++;
      }
    });
    /* istanbul ignore next */
    cov_4djw3z3h9().s[105]++;
    this.socket.on('document_locked', data => {
      /* istanbul ignore next */
      cov_4djw3z3h9().f[27]++;
      cov_4djw3z3h9().s[106]++;
      if (this.state.currentDocument) {
        /* istanbul ignore next */
        cov_4djw3z3h9().b[23][0]++;
        cov_4djw3z3h9().s[107]++;
        this.state.currentDocument.isLocked = data.locked;
        /* istanbul ignore next */
        cov_4djw3z3h9().s[108]++;
        this.state.currentDocument.lockedBy = data.locked ?
        /* istanbul ignore next */
        (cov_4djw3z3h9().b[24][0]++, data.userId) :
        /* istanbul ignore next */
        (cov_4djw3z3h9().b[24][1]++, undefined);
        /* istanbul ignore next */
        cov_4djw3z3h9().s[109]++;
        this.emit('document_lock_changed', data);
      } else
      /* istanbul ignore next */
      {
        cov_4djw3z3h9().b[23][1]++;
      }
    });
  }
  async sendOperation(operation) {
    /* istanbul ignore next */
    cov_4djw3z3h9().f[28]++;
    cov_4djw3z3h9().s[110]++;
    if (
    /* istanbul ignore next */
    (cov_4djw3z3h9().b[26][0]++, !this.socket) ||
    /* istanbul ignore next */
    (cov_4djw3z3h9().b[26][1]++, !this.state.currentDocument)) {
      /* istanbul ignore next */
      cov_4djw3z3h9().b[25][0]++;
      cov_4djw3z3h9().s[111]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_4djw3z3h9().b[25][1]++;
    }
    cov_4djw3z3h9().s[112]++;
    this.socket.emit('operation', {
      documentId: this.state.currentDocument.id,
      operation
    }, response => {
      /* istanbul ignore next */
      cov_4djw3z3h9().f[29]++;
      cov_4djw3z3h9().s[113]++;
      if (response.success) {
        /* istanbul ignore next */
        cov_4djw3z3h9().b[27][0]++;
        // Remove from queue
        const index =
        /* istanbul ignore next */
        (cov_4djw3z3h9().s[114]++, this.operationQueue.findIndex(op => {
          /* istanbul ignore next */
          cov_4djw3z3h9().f[30]++;
          cov_4djw3z3h9().s[115]++;
          return op.id === operation.id;
        }));
        /* istanbul ignore next */
        cov_4djw3z3h9().s[116]++;
        if (index > -1) {
          /* istanbul ignore next */
          cov_4djw3z3h9().b[28][0]++;
          cov_4djw3z3h9().s[117]++;
          this.operationQueue.splice(index, 1);
        } else
        /* istanbul ignore next */
        {
          cov_4djw3z3h9().b[28][1]++;
        }
        cov_4djw3z3h9().s[118]++;
        this.state.lastSyncTime = new Date();
      } else {
        /* istanbul ignore next */
        cov_4djw3z3h9().b[27][1]++;
        cov_4djw3z3h9().s[119]++;
        console.error('Failed to send operation:', response.error);
      }
    });
  }
  handleRemoteOperation(operation) {
    /* istanbul ignore next */
    cov_4djw3z3h9().f[31]++;
    // Transform operation against local operations
    const transformedOperation =
    /* istanbul ignore next */
    (cov_4djw3z3h9().s[120]++, this.transformationEngine.transform(operation, this.state.localOperations));
    // Apply transformed operation
    /* istanbul ignore next */
    cov_4djw3z3h9().s[121]++;
    this.emit('remote_operation', transformedOperation);
    // Update document version
    /* istanbul ignore next */
    cov_4djw3z3h9().s[122]++;
    if (this.state.currentDocument) {
      /* istanbul ignore next */
      cov_4djw3z3h9().b[29][0]++;
      cov_4djw3z3h9().s[123]++;
      this.state.currentDocument.version++;
      /* istanbul ignore next */
      cov_4djw3z3h9().s[124]++;
      this.state.currentDocument.operations.push(transformedOperation);
    } else
    /* istanbul ignore next */
    {
      cov_4djw3z3h9().b[29][1]++;
    }
  }
  async syncPendingOperations() {
    /* istanbul ignore next */
    cov_4djw3z3h9().f[32]++;
    const operations =
    /* istanbul ignore next */
    (cov_4djw3z3h9().s[125]++, [...this.operationQueue]);
    /* istanbul ignore next */
    cov_4djw3z3h9().s[126]++;
    for (const operation of operations) {
      /* istanbul ignore next */
      cov_4djw3z3h9().s[127]++;
      await this.sendOperation(operation);
    }
  }
  setupEventHandlers() {
    /* istanbul ignore next */
    cov_4djw3z3h9().f[33]++;
    cov_4djw3z3h9().s[128]++;
    // Handle page visibility changes
    document.addEventListener('visibilitychange', () => {
      /* istanbul ignore next */
      cov_4djw3z3h9().f[34]++;
      cov_4djw3z3h9().s[129]++;
      if (document.hidden) {
        /* istanbul ignore next */
        cov_4djw3z3h9().b[30][0]++;
        cov_4djw3z3h9().s[130]++;
        this.updateUserStatus(false);
      } else {
        /* istanbul ignore next */
        cov_4djw3z3h9().b[30][1]++;
        cov_4djw3z3h9().s[131]++;
        this.updateUserStatus(true);
      }
    });
    // Handle beforeunload
    /* istanbul ignore next */
    cov_4djw3z3h9().s[132]++;
    window.addEventListener('beforeunload', () => {
      /* istanbul ignore next */
      cov_4djw3z3h9().f[35]++;
      cov_4djw3z3h9().s[133]++;
      this.leaveDocument();
    });
  }
  updateUserStatus(isActive) {
    /* istanbul ignore next */
    cov_4djw3z3h9().f[36]++;
    cov_4djw3z3h9().s[134]++;
    if (
    /* istanbul ignore next */
    (cov_4djw3z3h9().b[32][0]++, this.socket) &&
    /* istanbul ignore next */
    (cov_4djw3z3h9().b[32][1]++, this.currentUser)) {
      /* istanbul ignore next */
      cov_4djw3z3h9().b[31][0]++;
      cov_4djw3z3h9().s[135]++;
      this.socket.emit('user_status', {
        userId: this.currentUser.id,
        isActive
      });
    } else
    /* istanbul ignore next */
    {
      cov_4djw3z3h9().b[31][1]++;
    }
  }
  startHeartbeat() {
    /* istanbul ignore next */
    cov_4djw3z3h9().f[37]++;
    cov_4djw3z3h9().s[136]++;
    this.heartbeatInterval = setInterval(() => {
      /* istanbul ignore next */
      cov_4djw3z3h9().f[38]++;
      cov_4djw3z3h9().s[137]++;
      if (
      /* istanbul ignore next */
      (cov_4djw3z3h9().b[34][0]++, this.socket) &&
      /* istanbul ignore next */
      (cov_4djw3z3h9().b[34][1]++, this.state.isConnected)) {
        /* istanbul ignore next */
        cov_4djw3z3h9().b[33][0]++;
        cov_4djw3z3h9().s[138]++;
        this.socket.emit('heartbeat', {
          userId: this.currentUser?.id,
          timestamp: new Date()
        });
      } else
      /* istanbul ignore next */
      {
        cov_4djw3z3h9().b[33][1]++;
      }
    }, 30000); // 30 seconds
  }
  async getAuthToken() {
    /* istanbul ignore next */
    cov_4djw3z3h9().f[39]++;
    cov_4djw3z3h9().s[139]++;
    // Implementation depends on your auth system
    return /* istanbul ignore next */(cov_4djw3z3h9().b[35][0]++, localStorage.getItem('auth_token')) ||
    /* istanbul ignore next */
    (cov_4djw3z3h9().b[35][1]++, '');
  }
  /**
   * Cleanup resources
   */
  destroy() {
    /* istanbul ignore next */
    cov_4djw3z3h9().f[40]++;
    cov_4djw3z3h9().s[140]++;
    if (this.heartbeatInterval) {
      /* istanbul ignore next */
      cov_4djw3z3h9().b[36][0]++;
      cov_4djw3z3h9().s[141]++;
      clearInterval(this.heartbeatInterval);
    } else
    /* istanbul ignore next */
    {
      cov_4djw3z3h9().b[36][1]++;
    }
    cov_4djw3z3h9().s[142]++;
    if (this.socket) {
      /* istanbul ignore next */
      cov_4djw3z3h9().b[37][0]++;
      cov_4djw3z3h9().s[143]++;
      this.socket.disconnect();
    } else
    /* istanbul ignore next */
    {
      cov_4djw3z3h9().b[37][1]++;
    }
    cov_4djw3z3h9().s[144]++;
    this.eventListeners.clear();
  }
}
/* istanbul ignore next */
cov_4djw3z3h9().s[145]++;
exports.CollaborationService = CollaborationService;
/**
 * Operational Transformation Engine
 * Handles conflict resolution for concurrent operations
 */
class OperationalTransform {
  transform(operation, localOperations) {
    /* istanbul ignore next */
    cov_4djw3z3h9().f[41]++;
    let transformedOp =
    /* istanbul ignore next */
    (cov_4djw3z3h9().s[146]++, {
      ...operation
    });
    // Apply transformation rules based on operation types
    /* istanbul ignore next */
    cov_4djw3z3h9().s[147]++;
    for (const localOp of localOperations) {
      /* istanbul ignore next */
      cov_4djw3z3h9().s[148]++;
      if (localOp.timestamp > operation.timestamp) {
        /* istanbul ignore next */
        cov_4djw3z3h9().b[38][0]++;
        cov_4djw3z3h9().s[149]++;
        transformedOp = this.transformAgainst(transformedOp, localOp);
      } else
      /* istanbul ignore next */
      {
        cov_4djw3z3h9().b[38][1]++;
      }
    }
    /* istanbul ignore next */
    cov_4djw3z3h9().s[150]++;
    return transformedOp;
  }
  transformAgainst(op1, op2) {
    /* istanbul ignore next */
    cov_4djw3z3h9().f[42]++;
    cov_4djw3z3h9().s[151]++;
    // Implement transformation rules based on operation types
    if (op1.elementId === op2.elementId) {
      /* istanbul ignore next */
      cov_4djw3z3h9().b[39][0]++;
      cov_4djw3z3h9().s[152]++;
      // Same element - need careful transformation
      return this.transformSameElement(op1, op2);
    } else
    /* istanbul ignore next */
    {
      cov_4djw3z3h9().b[39][1]++;
    }
    // Different elements - operations are independent
    cov_4djw3z3h9().s[153]++;
    return op1;
  }
  transformSameElement(op1, op2) {
    /* istanbul ignore next */
    cov_4djw3z3h9().f[43]++;
    cov_4djw3z3h9().s[154]++;
    // Implement specific transformation logic
    // This is a simplified version - real implementation would be more complex
    if (
    /* istanbul ignore next */
    (cov_4djw3z3h9().b[41][0]++, op1.type === 'update') &&
    /* istanbul ignore next */
    (cov_4djw3z3h9().b[41][1]++, op2.type === 'update')) {
      /* istanbul ignore next */
      cov_4djw3z3h9().b[40][0]++;
      cov_4djw3z3h9().s[155]++;
      // Last writer wins for updates
      return op1.timestamp > op2.timestamp ?
      /* istanbul ignore next */
      (cov_4djw3z3h9().b[42][0]++, op1) :
      /* istanbul ignore next */
      (cov_4djw3z3h9().b[42][1]++, op2);
    } else
    /* istanbul ignore next */
    {
      cov_4djw3z3h9().b[40][1]++;
    }
    cov_4djw3z3h9().s[156]++;
    if (
    /* istanbul ignore next */
    (cov_4djw3z3h9().b[44][0]++, op1.type === 'move') &&
    /* istanbul ignore next */
    (cov_4djw3z3h9().b[44][1]++, op2.type === 'move')) {
      /* istanbul ignore next */
      cov_4djw3z3h9().b[43][0]++;
      cov_4djw3z3h9().s[157]++;
      // Combine position changes
      return {
        ...op1,
        position: {
          x: (
          /* istanbul ignore next */
          (cov_4djw3z3h9().b[45][0]++, op1.position?.x) ||
          /* istanbul ignore next */
          (cov_4djw3z3h9().b[45][1]++, 0)) + (
          /* istanbul ignore next */
          (cov_4djw3z3h9().b[46][0]++, op2.position?.x) ||
          /* istanbul ignore next */
          (cov_4djw3z3h9().b[46][1]++, 0)),
          y: (
          /* istanbul ignore next */
          (cov_4djw3z3h9().b[47][0]++, op1.position?.y) ||
          /* istanbul ignore next */
          (cov_4djw3z3h9().b[47][1]++, 0)) + (
          /* istanbul ignore next */
          (cov_4djw3z3h9().b[48][0]++, op2.position?.y) ||
          /* istanbul ignore next */
          (cov_4djw3z3h9().b[48][1]++, 0))
        }
      };
    } else
    /* istanbul ignore next */
    {
      cov_4djw3z3h9().b[43][1]++;
    }
    cov_4djw3z3h9().s[158]++;
    return op1;
  }
}
// Singleton instance
let collaborationService =
/* istanbul ignore next */
(cov_4djw3z3h9().s[159]++, null);
function getCollaborationService() {
  /* istanbul ignore next */
  cov_4djw3z3h9().f[44]++;
  cov_4djw3z3h9().s[160]++;
  if (!collaborationService) {
    /* istanbul ignore next */
    cov_4djw3z3h9().b[49][0]++;
    cov_4djw3z3h9().s[161]++;
    collaborationService = new CollaborationService();
  } else
  /* istanbul ignore next */
  {
    cov_4djw3z3h9().b[49][1]++;
  }
  cov_4djw3z3h9().s[162]++;
  return collaborationService;
}
/* istanbul ignore next */
cov_4djw3z3h9().s[163]++;
exports.default = CollaborationService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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