{"version": 3, "names": ["cov_317kd64f2", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "name", "decl", "loc", "branchMap", "type", "locations", "undefined", "s", "f", "b", "inputSourceMap", "file", "mappings", "names", "sources", "sourcesContent", "version", "_coverageSchema", "coverage", "actualCoverage", "exports", "basicVelocityPressureExample", "inverseVelocityPressureExample", "enhancedFrictionExample", "materialAgingExample", "environmentalCorrectionsExample", "completeSystemAnalysisExample", "methodComparisonExample", "runAllAdvancedCalculationExamples", "VelocityPressureCalculator_1", "require", "EnhancedFrictionCalculator_1", "console", "log", "velocity", "formulaResult", "VelocityPressureCalculator", "calculateVelocityPressure", "method", "VelocityPressureMethod", "FORMULA", "velocityPressure", "toFixed", "accuracy", "calculationDetails", "formula", "enhancedResult", "ENHANCED_FORMULA", "airConditions", "temperature", "altitude", "humidity", "airDensity", "densityRatio", "corrections", "combined", "uncertaintyBounds", "upper", "lower", "warnings", "length", "join", "recommendations", "optimalMethod", "getOptimalMethod", "optimalResult", "targetVelocityPressure", "inverseResult", "calculateVelocityFromPressure", "inverseWithConditions", "baseInput", "hydraulicDiameter", "material", "colebrookResult", "EnhancedFrictionCalculator", "calculateFrictionLoss", "FrictionMethod", "COLEBROOK_WHITE", "frictionLoss", "frictionRate", "frictionFactor", "reynoldsNumber", "flowRegime", "enhancedDarcyResult", "ENHANCED_DARCY", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SWAMEE_JAIN", "newDuctResult", "materialAge", "MaterialAge", "NEW", "surfaceCondition", "SurfaceCondition", "EXCELLENT", "materialProperties", "baseRoughness", "agingFactor", "surfaceFactor", "combinedFactor", "averageAgedResult", "AVERAGE", "poorConditionResult", "POOR", "standardResult", "environmentalCorrections", "extremeConditionsResult", "systemParameters", "GOOD", "vpResult", "frictionResult", "totalPressureLoss", "frictionPercentage", "allRecommendations", "allWarnings", "testConditions", "methods", "HAALAND", "CHEN", "for<PERSON>ach", "result", "methodName", "replace", "toLowerCase", "l", "toUpperCase", "padEnd", "padStart", "formulaType", "includes"], "sources": ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\examples\\AdvancedCalculationModulesExamples.ts"], "sourcesContent": ["/**\r\n * Advanced Calculation Modules Examples\r\n * \r\n * Comprehensive examples demonstrating the usage of VelocityPressureCalculator\r\n * and EnhancedFrictionCalculator for Phase 3: Advanced Calculation Modules\r\n * \r\n * @version 3.0.0\r\n */\r\n\r\nimport { \r\n  VelocityPressureCalculator, \r\n  VelocityPressureMethod, \r\n  ValidationLevel \r\n} from '../VelocityPressureCalculator';\r\n\r\nimport { \r\n  EnhancedFrictionCalculator, \r\n  FrictionMethod, \r\n  MaterialAge,\r\n  SurfaceCondition \r\n} from '../EnhancedFrictionCalculator';\r\n\r\n/**\r\n * Example 1: Basic Velocity Pressure Calculations\r\n * Demonstrates different calculation methods and their applications\r\n */\r\nexport function basicVelocityPressureExample() {\r\n  console.log('=== BASIC VELOCITY PRESSURE CALCULATIONS ===\\n');\r\n\r\n  const velocity = 2000; // FPM\r\n\r\n  // Method 1: Standard formula calculation\r\n  const formulaResult = VelocityPressureCalculator.calculateVelocityPressure({\r\n    velocity,\r\n    method: VelocityPressureMethod.FORMULA\r\n  });\r\n\r\n  console.log('Formula Method:');\r\n  console.log(`  Velocity: ${formulaResult.velocity} FPM`);\r\n  console.log(`  Velocity Pressure: ${formulaResult.velocityPressure.toFixed(4)} in. w.g.`);\r\n  console.log(`  Method: ${formulaResult.method}`);\r\n  console.log(`  Accuracy: ${(formulaResult.accuracy * 100).toFixed(1)}%`);\r\n  console.log(`  Formula: ${formulaResult.calculationDetails.formula}\\n`);\r\n\r\n  // Method 2: Enhanced formula with corrections\r\n  const enhancedResult = VelocityPressureCalculator.calculateVelocityPressure({\r\n    velocity,\r\n    method: VelocityPressureMethod.ENHANCED_FORMULA,\r\n    airConditions: {\r\n      temperature: 85,  // Higher temperature\r\n      altitude: 3000,   // Higher altitude\r\n      humidity: 60      // Moderate humidity\r\n    }\r\n  });\r\n\r\n  console.log('Enhanced Formula Method with Environmental Conditions:');\r\n  console.log(`  Velocity: ${enhancedResult.velocity} FPM`);\r\n  console.log(`  Velocity Pressure: ${enhancedResult.velocityPressure.toFixed(4)} in. w.g.`);\r\n  console.log(`  Air Density: ${enhancedResult.airDensity.toFixed(4)} lb/ft³`);\r\n  console.log(`  Density Ratio: ${enhancedResult.densityRatio.toFixed(3)}`);\r\n  console.log(`  Combined Correction: ${enhancedResult.corrections.combined.toFixed(3)}`);\r\n  console.log(`  Uncertainty: ±${((enhancedResult.uncertaintyBounds!.upper - enhancedResult.uncertaintyBounds!.lower) / 2).toFixed(4)} in. w.g.`);\r\n  \r\n  if (enhancedResult.warnings.length > 0) {\r\n    console.log(`  Warnings: ${enhancedResult.warnings.join(', ')}`);\r\n  }\r\n  \r\n  if (enhancedResult.recommendations.length > 0) {\r\n    console.log(`  Recommendations: ${enhancedResult.recommendations.join(', ')}`);\r\n  }\r\n  console.log();\r\n\r\n  // Method 3: Optimal method selection\r\n  const optimalMethod = VelocityPressureCalculator.getOptimalMethod(velocity, undefined, 'high');\r\n  const optimalResult = VelocityPressureCalculator.calculateVelocityPressure({\r\n    velocity,\r\n    method: optimalMethod\r\n  });\r\n\r\n  console.log('Optimal Method Selection:');\r\n  console.log(`  Recommended Method: ${optimalMethod}`);\r\n  console.log(`  Velocity Pressure: ${optimalResult.velocityPressure.toFixed(4)} in. w.g.`);\r\n  console.log(`  Accuracy: ${(optimalResult.accuracy * 100).toFixed(1)}%\\n`);\r\n}\r\n\r\n/**\r\n * Example 2: Inverse Velocity Pressure Calculations\r\n * Demonstrates calculating velocity from known velocity pressure\r\n */\r\nexport function inverseVelocityPressureExample() {\r\n  console.log('=== INVERSE VELOCITY PRESSURE CALCULATIONS ===\\n');\r\n\r\n  const targetVelocityPressure = 0.25; // in. w.g.\r\n\r\n  // Calculate velocity from velocity pressure\r\n  const inverseResult = VelocityPressureCalculator.calculateVelocityFromPressure(\r\n    targetVelocityPressure\r\n  );\r\n\r\n  console.log('Standard Conditions:');\r\n  console.log(`  Target Velocity Pressure: ${targetVelocityPressure} in. w.g.`);\r\n  console.log(`  Calculated Velocity: ${inverseResult.velocity.toFixed(0)} FPM`);\r\n  console.log(`  Accuracy: ${(inverseResult.accuracy * 100).toFixed(1)}%\\n`);\r\n\r\n  // Calculate velocity with environmental conditions\r\n  const inverseWithConditions = VelocityPressureCalculator.calculateVelocityFromPressure(\r\n    targetVelocityPressure,\r\n    {\r\n      temperature: 90,\r\n      altitude: 5000,\r\n      humidity: 70\r\n    }\r\n  );\r\n\r\n  console.log('With Environmental Conditions:');\r\n  console.log(`  Target Velocity Pressure: ${targetVelocityPressure} in. w.g.`);\r\n  console.log(`  Calculated Velocity: ${inverseWithConditions.velocity.toFixed(0)} FPM`);\r\n  console.log(`  Accuracy: ${(inverseWithConditions.accuracy * 100).toFixed(1)}%`);\r\n  \r\n  if (inverseWithConditions.warnings.length > 0) {\r\n    console.log(`  Warnings: ${inverseWithConditions.warnings.join(', ')}`);\r\n  }\r\n  console.log();\r\n}\r\n\r\n/**\r\n * Example 3: Enhanced Friction Calculations\r\n * Demonstrates different friction calculation methods and material effects\r\n */\r\nexport function enhancedFrictionExample() {\r\n  console.log('=== ENHANCED FRICTION CALCULATIONS ===\\n');\r\n\r\n  const baseInput = {\r\n    velocity: 2000,           // FPM\r\n    hydraulicDiameter: 12,    // inches\r\n    length: 100,              // feet\r\n    material: 'galvanized_steel'\r\n  };\r\n\r\n  // Method 1: Colebrook-White (most accurate)\r\n  const colebrookResult = EnhancedFrictionCalculator.calculateFrictionLoss({\r\n    ...baseInput,\r\n    method: FrictionMethod.COLEBROOK_WHITE\r\n  });\r\n\r\n  console.log('Colebrook-White Method (Most Accurate):');\r\n  console.log(`  Friction Loss: ${colebrookResult.frictionLoss.toFixed(4)} in. w.g.`);\r\n  console.log(`  Friction Rate: ${colebrookResult.frictionRate.toFixed(4)} in. w.g./100 ft`);\r\n  console.log(`  Friction Factor: ${colebrookResult.frictionFactor.toFixed(6)}`);\r\n  console.log(`  Reynolds Number: ${colebrookResult.reynoldsNumber.toFixed(0)}`);\r\n  console.log(`  Flow Regime: ${colebrookResult.flowRegime}`);\r\n  console.log(`  Accuracy: ${(colebrookResult.accuracy * 100).toFixed(1)}%`);\r\n  console.log(`  Formula: ${colebrookResult.calculationDetails.formula}\\n`);\r\n\r\n  // Method 2: Enhanced Darcy (optimized for flow regime)\r\n  const enhancedDarcyResult = EnhancedFrictionCalculator.calculateFrictionLoss({\r\n    ...baseInput,\r\n    method: FrictionMethod.ENHANCED_DARCY\r\n  });\r\n\r\n  console.log('Enhanced Darcy Method (Flow Regime Optimized):');\r\n  console.log(`  Friction Loss: ${enhancedDarcyResult.frictionLoss.toFixed(4)} in. w.g.`);\r\n  console.log(`  Friction Rate: ${enhancedDarcyResult.frictionRate.toFixed(4)} in. w.g./100 ft`);\r\n  console.log(`  Flow Regime: ${enhancedDarcyResult.flowRegime}`);\r\n  console.log(`  Accuracy: ${(enhancedDarcyResult.accuracy * 100).toFixed(1)}%\\n`);\r\n\r\n  // Method 3: Swamee-Jain (explicit approximation)\r\n  const swameeJainResult = EnhancedFrictionCalculator.calculateFrictionLoss({\r\n    ...baseInput,\r\n    method: FrictionMethod.SWAMEE_JAIN\r\n  });\r\n\r\n  console.log('Swamee-Jain Method (Explicit Approximation):');\r\n  console.log(`  Friction Loss: ${swameeJainResult.frictionLoss.toFixed(4)} in. w.g.`);\r\n  console.log(`  Friction Rate: ${swameeJainResult.frictionRate.toFixed(4)} in. w.g./100 ft`);\r\n  console.log(`  Accuracy: ${(swameeJainResult.accuracy * 100).toFixed(1)}%\\n`);\r\n}\r\n\r\n/**\r\n * Example 4: Material Aging and Surface Condition Effects\r\n * Demonstrates how material aging and surface conditions affect friction\r\n */\r\nexport function materialAgingExample() {\r\n  console.log('=== MATERIAL AGING AND SURFACE CONDITION EFFECTS ===\\n');\r\n\r\n  const baseInput = {\r\n    velocity: 2000,\r\n    hydraulicDiameter: 12,\r\n    length: 100,\r\n    material: 'galvanized_steel',\r\n    method: FrictionMethod.ENHANCED_DARCY\r\n  };\r\n\r\n  // New duct in excellent condition\r\n  const newDuctResult = EnhancedFrictionCalculator.calculateFrictionLoss({\r\n    ...baseInput,\r\n    materialAge: MaterialAge.NEW,\r\n    surfaceCondition: SurfaceCondition.EXCELLENT\r\n  });\r\n\r\n  console.log('New Duct - Excellent Condition:');\r\n  console.log(`  Friction Loss: ${newDuctResult.frictionLoss.toFixed(4)} in. w.g.`);\r\n  console.log(`  Base Roughness: ${newDuctResult.materialProperties.baseRoughness.toFixed(6)} ft`);\r\n  console.log(`  Aging Factor: ${newDuctResult.materialProperties.agingFactor.toFixed(2)}`);\r\n  console.log(`  Surface Factor: ${newDuctResult.materialProperties.surfaceFactor.toFixed(2)}`);\r\n  console.log(`  Combined Factor: ${newDuctResult.materialProperties.combinedFactor.toFixed(2)}\\n`);\r\n\r\n  // Average aged duct\r\n  const averageAgedResult = EnhancedFrictionCalculator.calculateFrictionLoss({\r\n    ...baseInput,\r\n    materialAge: MaterialAge.AVERAGE,\r\n    surfaceCondition: SurfaceCondition.AVERAGE\r\n  });\r\n\r\n  console.log('Average Aged Duct - Average Condition:');\r\n  console.log(`  Friction Loss: ${averageAgedResult.frictionLoss.toFixed(4)} in. w.g.`);\r\n  console.log(`  Aging Factor: ${averageAgedResult.materialProperties.agingFactor.toFixed(2)}`);\r\n  console.log(`  Surface Factor: ${averageAgedResult.materialProperties.surfaceFactor.toFixed(2)}`);\r\n  console.log(`  Combined Factor: ${averageAgedResult.materialProperties.combinedFactor.toFixed(2)}`);\r\n  console.log(`  Increase vs New: ${((averageAgedResult.frictionLoss / newDuctResult.frictionLoss - 1) * 100).toFixed(1)}%\\n`);\r\n\r\n  // Poor condition duct\r\n  const poorConditionResult = EnhancedFrictionCalculator.calculateFrictionLoss({\r\n    ...baseInput,\r\n    materialAge: MaterialAge.POOR,\r\n    surfaceCondition: SurfaceCondition.POOR\r\n  });\r\n\r\n  console.log('Poor Condition Duct:');\r\n  console.log(`  Friction Loss: ${poorConditionResult.frictionLoss.toFixed(4)} in. w.g.`);\r\n  console.log(`  Aging Factor: ${poorConditionResult.materialProperties.agingFactor.toFixed(2)}`);\r\n  console.log(`  Surface Factor: ${poorConditionResult.materialProperties.surfaceFactor.toFixed(2)}`);\r\n  console.log(`  Combined Factor: ${poorConditionResult.materialProperties.combinedFactor.toFixed(2)}`);\r\n  console.log(`  Increase vs New: ${((poorConditionResult.frictionLoss / newDuctResult.frictionLoss - 1) * 100).toFixed(1)}%`);\r\n  \r\n  if (poorConditionResult.recommendations.length > 0) {\r\n    console.log(`  Recommendations: ${poorConditionResult.recommendations.join(', ')}`);\r\n  }\r\n  console.log();\r\n}\r\n\r\n/**\r\n * Example 5: Environmental Corrections\r\n * Demonstrates how environmental conditions affect friction calculations\r\n */\r\nexport function environmentalCorrectionsExample() {\r\n  console.log('=== ENVIRONMENTAL CORRECTIONS ===\\n');\r\n\r\n  const baseInput = {\r\n    velocity: 2000,\r\n    hydraulicDiameter: 12,\r\n    length: 100,\r\n    material: 'galvanized_steel',\r\n    method: FrictionMethod.ENHANCED_DARCY\r\n  };\r\n\r\n  // Standard conditions\r\n  const standardResult = EnhancedFrictionCalculator.calculateFrictionLoss(baseInput);\r\n\r\n  console.log('Standard Conditions (70°F, Sea Level):');\r\n  console.log(`  Friction Loss: ${standardResult.frictionLoss.toFixed(4)} in. w.g.`);\r\n  console.log(`  Environmental Correction: ${standardResult.environmentalCorrections.combined.toFixed(3)}\\n`);\r\n\r\n  // High temperature, high altitude conditions\r\n  const extremeConditionsResult = EnhancedFrictionCalculator.calculateFrictionLoss({\r\n    ...baseInput,\r\n    airConditions: {\r\n      temperature: 120,  // High temperature\r\n      altitude: 8000,    // High altitude\r\n      humidity: 90       // High humidity\r\n    }\r\n  });\r\n\r\n  console.log('Extreme Conditions (120°F, 8000 ft altitude, 90% RH):');\r\n  console.log(`  Friction Loss: ${extremeConditionsResult.frictionLoss.toFixed(4)} in. w.g.`);\r\n  console.log(`  Environmental Correction: ${extremeConditionsResult.environmentalCorrections.combined.toFixed(3)}`);\r\n  console.log(`  Change vs Standard: ${((extremeConditionsResult.frictionLoss / standardResult.frictionLoss - 1) * 100).toFixed(1)}%`);\r\n  \r\n  if (extremeConditionsResult.warnings.length > 0) {\r\n    console.log(`  Warnings: ${extremeConditionsResult.warnings.join(', ')}`);\r\n  }\r\n  console.log();\r\n}\r\n\r\n/**\r\n * Example 6: Complete Duct System Analysis\r\n * Demonstrates integrated use of both calculators for complete system analysis\r\n */\r\nexport function completeSystemAnalysisExample() {\r\n  console.log('=== COMPLETE DUCT SYSTEM ANALYSIS ===\\n');\r\n\r\n  const systemParameters = {\r\n    velocity: 2500,           // FPM\r\n    hydraulicDiameter: 14,    // inches\r\n    length: 150,              // feet\r\n    material: 'galvanized_steel',\r\n    materialAge: MaterialAge.GOOD,\r\n    surfaceCondition: SurfaceCondition.GOOD,\r\n    airConditions: {\r\n      temperature: 75,\r\n      altitude: 2000,\r\n      humidity: 55\r\n    }\r\n  };\r\n\r\n  console.log('System Parameters:');\r\n  console.log(`  Velocity: ${systemParameters.velocity} FPM`);\r\n  console.log(`  Hydraulic Diameter: ${systemParameters.hydraulicDiameter} inches`);\r\n  console.log(`  Length: ${systemParameters.length} feet`);\r\n  console.log(`  Material: ${systemParameters.material}`);\r\n  console.log(`  Conditions: ${systemParameters.airConditions.temperature}°F, ${systemParameters.airConditions.altitude} ft, ${systemParameters.airConditions.humidity}% RH\\n`);\r\n\r\n  // Calculate velocity pressure\r\n  const vpResult = VelocityPressureCalculator.calculateVelocityPressure({\r\n    velocity: systemParameters.velocity,\r\n    method: VelocityPressureMethod.ENHANCED_FORMULA,\r\n    airConditions: systemParameters.airConditions\r\n  });\r\n\r\n  console.log('Velocity Pressure Analysis:');\r\n  console.log(`  Velocity Pressure: ${vpResult.velocityPressure.toFixed(4)} in. w.g.`);\r\n  console.log(`  Air Density: ${vpResult.airDensity.toFixed(4)} lb/ft³`);\r\n  console.log(`  Method: ${vpResult.method}`);\r\n  console.log(`  Accuracy: ${(vpResult.accuracy * 100).toFixed(1)}%\\n`);\r\n\r\n  // Calculate friction loss\r\n  const frictionResult = EnhancedFrictionCalculator.calculateFrictionLoss({\r\n    velocity: systemParameters.velocity,\r\n    hydraulicDiameter: systemParameters.hydraulicDiameter,\r\n    length: systemParameters.length,\r\n    material: systemParameters.material,\r\n    materialAge: systemParameters.materialAge,\r\n    surfaceCondition: systemParameters.surfaceCondition,\r\n    airConditions: systemParameters.airConditions,\r\n    method: FrictionMethod.ENHANCED_DARCY\r\n  });\r\n\r\n  console.log('Friction Loss Analysis:');\r\n  console.log(`  Friction Loss: ${frictionResult.frictionLoss.toFixed(4)} in. w.g.`);\r\n  console.log(`  Friction Rate: ${frictionResult.frictionRate.toFixed(4)} in. w.g./100 ft`);\r\n  console.log(`  Friction Factor: ${frictionResult.frictionFactor.toFixed(6)}`);\r\n  console.log(`  Reynolds Number: ${frictionResult.reynoldsNumber.toFixed(0)}`);\r\n  console.log(`  Flow Regime: ${frictionResult.flowRegime}`);\r\n  console.log(`  Method: ${frictionResult.method}`);\r\n  console.log(`  Accuracy: ${(frictionResult.accuracy * 100).toFixed(1)}%\\n`);\r\n\r\n  // Calculate total system pressure loss\r\n  const totalPressureLoss = vpResult.velocityPressure + frictionResult.frictionLoss;\r\n  const frictionPercentage = (frictionResult.frictionLoss / totalPressureLoss) * 100;\r\n\r\n  console.log('System Summary:');\r\n  console.log(`  Velocity Pressure: ${vpResult.velocityPressure.toFixed(4)} in. w.g.`);\r\n  console.log(`  Friction Loss: ${frictionResult.frictionLoss.toFixed(4)} in. w.g.`);\r\n  console.log(`  Total Pressure Loss: ${totalPressureLoss.toFixed(4)} in. w.g.`);\r\n  console.log(`  Friction Percentage: ${frictionPercentage.toFixed(1)}%`);\r\n\r\n  // Combined recommendations\r\n  const allRecommendations = [...vpResult.recommendations, ...frictionResult.recommendations];\r\n  if (allRecommendations.length > 0) {\r\n    console.log(`  Recommendations: ${allRecommendations.join('; ')}`);\r\n  }\r\n\r\n  // Combined warnings\r\n  const allWarnings = [...vpResult.warnings, ...frictionResult.warnings];\r\n  if (allWarnings.length > 0) {\r\n    console.log(`  Warnings: ${allWarnings.join('; ')}`);\r\n  }\r\n  console.log();\r\n}\r\n\r\n/**\r\n * Example 7: Method Comparison and Optimization\r\n * Demonstrates comparing different calculation methods for optimization\r\n */\r\nexport function methodComparisonExample() {\r\n  console.log('=== METHOD COMPARISON AND OPTIMIZATION ===\\n');\r\n\r\n  const testConditions = {\r\n    velocity: 2000,\r\n    hydraulicDiameter: 12,\r\n    length: 100,\r\n    material: 'galvanized_steel'\r\n  };\r\n\r\n  console.log('Friction Method Comparison:');\r\n  console.log('Method                    | Friction Loss | Accuracy | Formula Type');\r\n  console.log('--------------------------|---------------|----------|-------------');\r\n\r\n  const methods = [\r\n    FrictionMethod.COLEBROOK_WHITE,\r\n    FrictionMethod.SWAMEE_JAIN,\r\n    FrictionMethod.HAALAND,\r\n    FrictionMethod.CHEN,\r\n    FrictionMethod.ENHANCED_DARCY\r\n  ];\r\n\r\n  methods.forEach(method => {\r\n    const result = EnhancedFrictionCalculator.calculateFrictionLoss({\r\n      ...testConditions,\r\n      method\r\n    });\r\n\r\n    const methodName = method.replace(/_/g, ' ').toLowerCase()\r\n      .replace(/\\b\\w/g, l => l.toUpperCase()).padEnd(25);\r\n    const frictionLoss = result.frictionLoss.toFixed(4).padStart(13);\r\n    const accuracy = `${(result.accuracy * 100).toFixed(1)}%`.padStart(8);\r\n    const formulaType = result.calculationDetails.formula.includes('iterative') ? 'Iterative' : 'Explicit';\r\n\r\n    console.log(`${methodName}| ${frictionLoss} | ${accuracy} | ${formulaType}`);\r\n  });\r\n\r\n  console.log();\r\n\r\n  // Optimal method recommendation\r\n  const optimalMethod = EnhancedFrictionCalculator.getOptimalMethod(50000, 0.001, 'high');\r\n  console.log(`Optimal Method Recommendation: ${optimalMethod}`);\r\n  console.log('(Based on Reynolds number: 50,000, Relative roughness: 0.001, High accuracy requirement)\\n');\r\n}\r\n\r\n/**\r\n * Run all examples\r\n */\r\nexport function runAllAdvancedCalculationExamples() {\r\n  console.log('ADVANCED CALCULATION MODULES - COMPREHENSIVE EXAMPLES\\n');\r\n  console.log('=====================================================\\n');\r\n\r\n  basicVelocityPressureExample();\r\n  inverseVelocityPressureExample();\r\n  enhancedFrictionExample();\r\n  materialAgingExample();\r\n  environmentalCorrectionsExample();\r\n  completeSystemAnalysisExample();\r\n  methodComparisonExample();\r\n\r\n  console.log('All examples completed successfully!');\r\n  console.log('These examples demonstrate the comprehensive capabilities of the Advanced Calculation Modules.');\r\n}\r\n\r\n// Export individual examples for selective usage\r\nexport {\r\n  basicVelocityPressureExample,\r\n  inverseVelocityPressureExample,\r\n  enhancedFrictionExample,\r\n  materialAgingExample,\r\n  environmentalCorrectionsExample,\r\n  completeSystemAnalysisExample,\r\n  methodComparisonExample\r\n};\r\n"], "mappings": ";;AAAA;;;;;;;;AAAA;AAAA,SAAAA,cAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;MAAA;QAAAC,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAO,SAAA;MAAA;QAAAD,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAW,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,cAAA;MAAAC,IAAA;MAAAC,QAAA;MAAAC,KAAA;MAAAC,OAAA;MAAAC,cAAA;MAAAC,OAAA;IAAA;IAAAC,eAAA;IAAA5B,IAAA;EAAA;EAAA,IAAA6B,QAAA,GAAA5B,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAA0B,QAAA,CAAA9B,IAAA,KAAA8B,QAAA,CAAA9B,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAA6B,QAAA,CAAA9B,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAA0B,cAAA,GAAAD,QAAA,CAAA9B,IAAA;EAAA;IAgSA;IAAAD,aAAA,YAAAA,CAAA;MAAA,OAAAgC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAhC,aAAA;AAAAA,aAAA,GAAAoB,CAAA;;;;;;AAtQAa,OAAA,CAAAC,4BAAA,GAAAA,4BAAA;AAyDC;AAAAlC,aAAA,GAAAoB,CAAA;AAMDa,OAAA,CAAAE,8BAAA,GAAAA,8BAAA;AAkCC;AAAAnC,aAAA,GAAAoB,CAAA;AAMDa,OAAA,CAAAG,uBAAA,GAAAA,uBAAA;AA+CC;AAAApC,aAAA,GAAAoB,CAAA;AAMDa,OAAA,CAAAI,oBAAA,GAAAA,oBAAA;AAyDC;AAAArC,aAAA,GAAAoB,CAAA;AAMDa,OAAA,CAAAK,+BAAA,GAAAA,+BAAA;AAqCC;AAAAtC,aAAA,GAAAoB,CAAA;AAMDa,OAAA,CAAAM,6BAAA,GAAAA,6BAAA;AAgFC;AAAAvC,aAAA,GAAAoB,CAAA;AAMDa,OAAA,CAAAO,uBAAA,GAAAA,uBAAA;AA2CC;AAAAxC,aAAA,GAAAoB,CAAA;AAKDa,OAAA,CAAAQ,iCAAA,GAAAA,iCAAA;AA7ZA,MAAAC,4BAAA;AAAA;AAAA,CAAA1C,aAAA,GAAAoB,CAAA,OAAAuB,OAAA;AAMA,MAAAC,4BAAA;AAAA;AAAA,CAAA5C,aAAA,GAAAoB,CAAA,QAAAuB,OAAA;AAOA;;;;AAIA,SAAgBT,4BAA4BA,CAAA;EAAA;EAAAlC,aAAA,GAAAqB,CAAA;EAAArB,aAAA,GAAAoB,CAAA;EAC1CyB,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;EAE7D,MAAMC,QAAQ;EAAA;EAAA,CAAA/C,aAAA,GAAAoB,CAAA,QAAG,IAAI,EAAC,CAAC;EAEvB;EACA,MAAM4B,aAAa;EAAA;EAAA,CAAAhD,aAAA,GAAAoB,CAAA,QAAGsB,4BAAA,CAAAO,0BAA0B,CAACC,yBAAyB,CAAC;IACzEH,QAAQ;IACRI,MAAM,EAAET,4BAAA,CAAAU,sBAAsB,CAACC;GAChC,CAAC;EAAC;EAAArD,aAAA,GAAAoB,CAAA;EAEHyB,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;EAAC;EAAA9C,aAAA,GAAAoB,CAAA;EAC/ByB,OAAO,CAACC,GAAG,CAAC,eAAeE,aAAa,CAACD,QAAQ,MAAM,CAAC;EAAC;EAAA/C,aAAA,GAAAoB,CAAA;EACzDyB,OAAO,CAACC,GAAG,CAAC,wBAAwBE,aAAa,CAACM,gBAAgB,CAACC,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC;EAAC;EAAAvD,aAAA,GAAAoB,CAAA;EAC1FyB,OAAO,CAACC,GAAG,CAAC,aAAaE,aAAa,CAACG,MAAM,EAAE,CAAC;EAAC;EAAAnD,aAAA,GAAAoB,CAAA;EACjDyB,OAAO,CAACC,GAAG,CAAC,eAAe,CAACE,aAAa,CAACQ,QAAQ,GAAG,GAAG,EAAED,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;EAAC;EAAAvD,aAAA,GAAAoB,CAAA;EACzEyB,OAAO,CAACC,GAAG,CAAC,cAAcE,aAAa,CAACS,kBAAkB,CAACC,OAAO,IAAI,CAAC;EAEvE;EACA,MAAMC,cAAc;EAAA;EAAA,CAAA3D,aAAA,GAAAoB,CAAA,QAAGsB,4BAAA,CAAAO,0BAA0B,CAACC,yBAAyB,CAAC;IAC1EH,QAAQ;IACRI,MAAM,EAAET,4BAAA,CAAAU,sBAAsB,CAACQ,gBAAgB;IAC/CC,aAAa,EAAE;MACbC,WAAW,EAAE,EAAE;MAAG;MAClBC,QAAQ,EAAE,IAAI;MAAI;MAClBC,QAAQ,EAAE,EAAE,CAAM;;GAErB,CAAC;EAAC;EAAAhE,aAAA,GAAAoB,CAAA;EAEHyB,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;EAAC;EAAA9C,aAAA,GAAAoB,CAAA;EACtEyB,OAAO,CAACC,GAAG,CAAC,eAAea,cAAc,CAACZ,QAAQ,MAAM,CAAC;EAAC;EAAA/C,aAAA,GAAAoB,CAAA;EAC1DyB,OAAO,CAACC,GAAG,CAAC,wBAAwBa,cAAc,CAACL,gBAAgB,CAACC,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC;EAAC;EAAAvD,aAAA,GAAAoB,CAAA;EAC3FyB,OAAO,CAACC,GAAG,CAAC,kBAAkBa,cAAc,CAACM,UAAU,CAACV,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC;EAAC;EAAAvD,aAAA,GAAAoB,CAAA;EAC7EyB,OAAO,CAACC,GAAG,CAAC,oBAAoBa,cAAc,CAACO,YAAY,CAACX,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;EAAC;EAAAvD,aAAA,GAAAoB,CAAA;EAC1EyB,OAAO,CAACC,GAAG,CAAC,0BAA0Ba,cAAc,CAACQ,WAAW,CAACC,QAAQ,CAACb,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;EAAC;EAAAvD,aAAA,GAAAoB,CAAA;EACxFyB,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC,CAACa,cAAc,CAACU,iBAAkB,CAACC,KAAK,GAAGX,cAAc,CAACU,iBAAkB,CAACE,KAAK,IAAI,CAAC,EAAEhB,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC;EAAC;EAAAvD,aAAA,GAAAoB,CAAA;EAEhJ,IAAIuC,cAAc,CAACa,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;IAAA;IAAAzE,aAAA,GAAAsB,CAAA;IAAAtB,aAAA,GAAAoB,CAAA;IACtCyB,OAAO,CAACC,GAAG,CAAC,eAAea,cAAc,CAACa,QAAQ,CAACE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;EAClE,CAAC;EAAA;EAAA;IAAA1E,aAAA,GAAAsB,CAAA;EAAA;EAAAtB,aAAA,GAAAoB,CAAA;EAED,IAAIuC,cAAc,CAACgB,eAAe,CAACF,MAAM,GAAG,CAAC,EAAE;IAAA;IAAAzE,aAAA,GAAAsB,CAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAC7CyB,OAAO,CAACC,GAAG,CAAC,sBAAsBa,cAAc,CAACgB,eAAe,CAACD,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;EAChF,CAAC;EAAA;EAAA;IAAA1E,aAAA,GAAAsB,CAAA;EAAA;EAAAtB,aAAA,GAAAoB,CAAA;EACDyB,OAAO,CAACC,GAAG,EAAE;EAEb;EACA,MAAM8B,aAAa;EAAA;EAAA,CAAA5E,aAAA,GAAAoB,CAAA,QAAGsB,4BAAA,CAAAO,0BAA0B,CAAC4B,gBAAgB,CAAC9B,QAAQ,EAAE5B,SAAS,EAAE,MAAM,CAAC;EAC9F,MAAM2D,aAAa;EAAA;EAAA,CAAA9E,aAAA,GAAAoB,CAAA,QAAGsB,4BAAA,CAAAO,0BAA0B,CAACC,yBAAyB,CAAC;IACzEH,QAAQ;IACRI,MAAM,EAAEyB;GACT,CAAC;EAAC;EAAA5E,aAAA,GAAAoB,CAAA;EAEHyB,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;EAAC;EAAA9C,aAAA,GAAAoB,CAAA;EACzCyB,OAAO,CAACC,GAAG,CAAC,yBAAyB8B,aAAa,EAAE,CAAC;EAAC;EAAA5E,aAAA,GAAAoB,CAAA;EACtDyB,OAAO,CAACC,GAAG,CAAC,wBAAwBgC,aAAa,CAACxB,gBAAgB,CAACC,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC;EAAC;EAAAvD,aAAA,GAAAoB,CAAA;EAC1FyB,OAAO,CAACC,GAAG,CAAC,eAAe,CAACgC,aAAa,CAACtB,QAAQ,GAAG,GAAG,EAAED,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;AAC5E;AAEA;;;;AAIA,SAAgBpB,8BAA8BA,CAAA;EAAA;EAAAnC,aAAA,GAAAqB,CAAA;EAAArB,aAAA,GAAAoB,CAAA;EAC5CyB,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;EAE/D,MAAMiC,sBAAsB;EAAA;EAAA,CAAA/E,aAAA,GAAAoB,CAAA,QAAG,IAAI,EAAC,CAAC;EAErC;EACA,MAAM4D,aAAa;EAAA;EAAA,CAAAhF,aAAA,GAAAoB,CAAA,QAAGsB,4BAAA,CAAAO,0BAA0B,CAACgC,6BAA6B,CAC5EF,sBAAsB,CACvB;EAAC;EAAA/E,aAAA,GAAAoB,CAAA;EAEFyB,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;EAAC;EAAA9C,aAAA,GAAAoB,CAAA;EACpCyB,OAAO,CAACC,GAAG,CAAC,+BAA+BiC,sBAAsB,WAAW,CAAC;EAAC;EAAA/E,aAAA,GAAAoB,CAAA;EAC9EyB,OAAO,CAACC,GAAG,CAAC,0BAA0BkC,aAAa,CAACjC,QAAQ,CAACQ,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC;EAAC;EAAAvD,aAAA,GAAAoB,CAAA;EAC/EyB,OAAO,CAACC,GAAG,CAAC,eAAe,CAACkC,aAAa,CAACxB,QAAQ,GAAG,GAAG,EAAED,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;EAE1E;EACA,MAAM2B,qBAAqB;EAAA;EAAA,CAAAlF,aAAA,GAAAoB,CAAA,QAAGsB,4BAAA,CAAAO,0BAA0B,CAACgC,6BAA6B,CACpFF,sBAAsB,EACtB;IACEjB,WAAW,EAAE,EAAE;IACfC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE;GACX,CACF;EAAC;EAAAhE,aAAA,GAAAoB,CAAA;EAEFyB,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;EAAC;EAAA9C,aAAA,GAAAoB,CAAA;EAC9CyB,OAAO,CAACC,GAAG,CAAC,+BAA+BiC,sBAAsB,WAAW,CAAC;EAAC;EAAA/E,aAAA,GAAAoB,CAAA;EAC9EyB,OAAO,CAACC,GAAG,CAAC,0BAA0BoC,qBAAqB,CAACnC,QAAQ,CAACQ,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC;EAAC;EAAAvD,aAAA,GAAAoB,CAAA;EACvFyB,OAAO,CAACC,GAAG,CAAC,eAAe,CAACoC,qBAAqB,CAAC1B,QAAQ,GAAG,GAAG,EAAED,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;EAAC;EAAAvD,aAAA,GAAAoB,CAAA;EAEjF,IAAI8D,qBAAqB,CAACV,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;IAAA;IAAAzE,aAAA,GAAAsB,CAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAC7CyB,OAAO,CAACC,GAAG,CAAC,eAAeoC,qBAAqB,CAACV,QAAQ,CAACE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;EACzE,CAAC;EAAA;EAAA;IAAA1E,aAAA,GAAAsB,CAAA;EAAA;EAAAtB,aAAA,GAAAoB,CAAA;EACDyB,OAAO,CAACC,GAAG,EAAE;AACf;AAEA;;;;AAIA,SAAgBV,uBAAuBA,CAAA;EAAA;EAAApC,aAAA,GAAAqB,CAAA;EAAArB,aAAA,GAAAoB,CAAA;EACrCyB,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;EAEvD,MAAMqC,SAAS;EAAA;EAAA,CAAAnF,aAAA,GAAAoB,CAAA,QAAG;IAChB2B,QAAQ,EAAE,IAAI;IAAY;IAC1BqC,iBAAiB,EAAE,EAAE;IAAK;IAC1BX,MAAM,EAAE,GAAG;IAAe;IAC1BY,QAAQ,EAAE;GACX;EAED;EACA,MAAMC,eAAe;EAAA;EAAA,CAAAtF,aAAA,GAAAoB,CAAA,QAAGwB,4BAAA,CAAA2C,0BAA0B,CAACC,qBAAqB,CAAC;IACvE,GAAGL,SAAS;IACZhC,MAAM,EAAEP,4BAAA,CAAA6C,cAAc,CAACC;GACxB,CAAC;EAAC;EAAA1F,aAAA,GAAAoB,CAAA;EAEHyB,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;EAAC;EAAA9C,aAAA,GAAAoB,CAAA;EACvDyB,OAAO,CAACC,GAAG,CAAC,oBAAoBwC,eAAe,CAACK,YAAY,CAACpC,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC;EAAC;EAAAvD,aAAA,GAAAoB,CAAA;EACpFyB,OAAO,CAACC,GAAG,CAAC,oBAAoBwC,eAAe,CAACM,YAAY,CAACrC,OAAO,CAAC,CAAC,CAAC,kBAAkB,CAAC;EAAC;EAAAvD,aAAA,GAAAoB,CAAA;EAC3FyB,OAAO,CAACC,GAAG,CAAC,sBAAsBwC,eAAe,CAACO,cAAc,CAACtC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;EAAC;EAAAvD,aAAA,GAAAoB,CAAA;EAC/EyB,OAAO,CAACC,GAAG,CAAC,sBAAsBwC,eAAe,CAACQ,cAAc,CAACvC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;EAAC;EAAAvD,aAAA,GAAAoB,CAAA;EAC/EyB,OAAO,CAACC,GAAG,CAAC,kBAAkBwC,eAAe,CAACS,UAAU,EAAE,CAAC;EAAC;EAAA/F,aAAA,GAAAoB,CAAA;EAC5DyB,OAAO,CAACC,GAAG,CAAC,eAAe,CAACwC,eAAe,CAAC9B,QAAQ,GAAG,GAAG,EAAED,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;EAAC;EAAAvD,aAAA,GAAAoB,CAAA;EAC3EyB,OAAO,CAACC,GAAG,CAAC,cAAcwC,eAAe,CAAC7B,kBAAkB,CAACC,OAAO,IAAI,CAAC;EAEzE;EACA,MAAMsC,mBAAmB;EAAA;EAAA,CAAAhG,aAAA,GAAAoB,CAAA,QAAGwB,4BAAA,CAAA2C,0BAA0B,CAACC,qBAAqB,CAAC;IAC3E,GAAGL,SAAS;IACZhC,MAAM,EAAEP,4BAAA,CAAA6C,cAAc,CAACQ;GACxB,CAAC;EAAC;EAAAjG,aAAA,GAAAoB,CAAA;EAEHyB,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;EAAC;EAAA9C,aAAA,GAAAoB,CAAA;EAC9DyB,OAAO,CAACC,GAAG,CAAC,oBAAoBkD,mBAAmB,CAACL,YAAY,CAACpC,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC;EAAC;EAAAvD,aAAA,GAAAoB,CAAA;EACxFyB,OAAO,CAACC,GAAG,CAAC,oBAAoBkD,mBAAmB,CAACJ,YAAY,CAACrC,OAAO,CAAC,CAAC,CAAC,kBAAkB,CAAC;EAAC;EAAAvD,aAAA,GAAAoB,CAAA;EAC/FyB,OAAO,CAACC,GAAG,CAAC,kBAAkBkD,mBAAmB,CAACD,UAAU,EAAE,CAAC;EAAC;EAAA/F,aAAA,GAAAoB,CAAA;EAChEyB,OAAO,CAACC,GAAG,CAAC,eAAe,CAACkD,mBAAmB,CAACxC,QAAQ,GAAG,GAAG,EAAED,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;EAEhF;EACA,MAAM2C,gBAAgB;EAAA;EAAA,CAAAlG,aAAA,GAAAoB,CAAA,QAAGwB,4BAAA,CAAA2C,0BAA0B,CAACC,qBAAqB,CAAC;IACxE,GAAGL,SAAS;IACZhC,MAAM,EAAEP,4BAAA,CAAA6C,cAAc,CAACU;GACxB,CAAC;EAAC;EAAAnG,aAAA,GAAAoB,CAAA;EAEHyB,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;EAAC;EAAA9C,aAAA,GAAAoB,CAAA;EAC5DyB,OAAO,CAACC,GAAG,CAAC,oBAAoBoD,gBAAgB,CAACP,YAAY,CAACpC,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC;EAAC;EAAAvD,aAAA,GAAAoB,CAAA;EACrFyB,OAAO,CAACC,GAAG,CAAC,oBAAoBoD,gBAAgB,CAACN,YAAY,CAACrC,OAAO,CAAC,CAAC,CAAC,kBAAkB,CAAC;EAAC;EAAAvD,aAAA,GAAAoB,CAAA;EAC5FyB,OAAO,CAACC,GAAG,CAAC,eAAe,CAACoD,gBAAgB,CAAC1C,QAAQ,GAAG,GAAG,EAAED,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;AAC/E;AAEA;;;;AAIA,SAAgBlB,oBAAoBA,CAAA;EAAA;EAAArC,aAAA,GAAAqB,CAAA;EAAArB,aAAA,GAAAoB,CAAA;EAClCyB,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;EAErE,MAAMqC,SAAS;EAAA;EAAA,CAAAnF,aAAA,GAAAoB,CAAA,QAAG;IAChB2B,QAAQ,EAAE,IAAI;IACdqC,iBAAiB,EAAE,EAAE;IACrBX,MAAM,EAAE,GAAG;IACXY,QAAQ,EAAE,kBAAkB;IAC5BlC,MAAM,EAAEP,4BAAA,CAAA6C,cAAc,CAACQ;GACxB;EAED;EACA,MAAMG,aAAa;EAAA;EAAA,CAAApG,aAAA,GAAAoB,CAAA,QAAGwB,4BAAA,CAAA2C,0BAA0B,CAACC,qBAAqB,CAAC;IACrE,GAAGL,SAAS;IACZkB,WAAW,EAAEzD,4BAAA,CAAA0D,WAAW,CAACC,GAAG;IAC5BC,gBAAgB,EAAE5D,4BAAA,CAAA6D,gBAAgB,CAACC;GACpC,CAAC;EAAC;EAAA1G,aAAA,GAAAoB,CAAA;EAEHyB,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;EAAC;EAAA9C,aAAA,GAAAoB,CAAA;EAC/CyB,OAAO,CAACC,GAAG,CAAC,oBAAoBsD,aAAa,CAACT,YAAY,CAACpC,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC;EAAC;EAAAvD,aAAA,GAAAoB,CAAA;EAClFyB,OAAO,CAACC,GAAG,CAAC,qBAAqBsD,aAAa,CAACO,kBAAkB,CAACC,aAAa,CAACrD,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;EAAC;EAAAvD,aAAA,GAAAoB,CAAA;EACjGyB,OAAO,CAACC,GAAG,CAAC,mBAAmBsD,aAAa,CAACO,kBAAkB,CAACE,WAAW,CAACtD,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;EAAC;EAAAvD,aAAA,GAAAoB,CAAA;EAC1FyB,OAAO,CAACC,GAAG,CAAC,qBAAqBsD,aAAa,CAACO,kBAAkB,CAACG,aAAa,CAACvD,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;EAAC;EAAAvD,aAAA,GAAAoB,CAAA;EAC9FyB,OAAO,CAACC,GAAG,CAAC,sBAAsBsD,aAAa,CAACO,kBAAkB,CAACI,cAAc,CAACxD,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;EAEjG;EACA,MAAMyD,iBAAiB;EAAA;EAAA,CAAAhH,aAAA,GAAAoB,CAAA,QAAGwB,4BAAA,CAAA2C,0BAA0B,CAACC,qBAAqB,CAAC;IACzE,GAAGL,SAAS;IACZkB,WAAW,EAAEzD,4BAAA,CAAA0D,WAAW,CAACW,OAAO;IAChCT,gBAAgB,EAAE5D,4BAAA,CAAA6D,gBAAgB,CAACQ;GACpC,CAAC;EAAC;EAAAjH,aAAA,GAAAoB,CAAA;EAEHyB,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;EAAC;EAAA9C,aAAA,GAAAoB,CAAA;EACtDyB,OAAO,CAACC,GAAG,CAAC,oBAAoBkE,iBAAiB,CAACrB,YAAY,CAACpC,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC;EAAC;EAAAvD,aAAA,GAAAoB,CAAA;EACtFyB,OAAO,CAACC,GAAG,CAAC,mBAAmBkE,iBAAiB,CAACL,kBAAkB,CAACE,WAAW,CAACtD,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;EAAC;EAAAvD,aAAA,GAAAoB,CAAA;EAC9FyB,OAAO,CAACC,GAAG,CAAC,qBAAqBkE,iBAAiB,CAACL,kBAAkB,CAACG,aAAa,CAACvD,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;EAAC;EAAAvD,aAAA,GAAAoB,CAAA;EAClGyB,OAAO,CAACC,GAAG,CAAC,sBAAsBkE,iBAAiB,CAACL,kBAAkB,CAACI,cAAc,CAACxD,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;EAAC;EAAAvD,aAAA,GAAAoB,CAAA;EACpGyB,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC,CAACkE,iBAAiB,CAACrB,YAAY,GAAGS,aAAa,CAACT,YAAY,GAAG,CAAC,IAAI,GAAG,EAAEpC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;EAE5H;EACA,MAAM2D,mBAAmB;EAAA;EAAA,CAAAlH,aAAA,GAAAoB,CAAA,QAAGwB,4BAAA,CAAA2C,0BAA0B,CAACC,qBAAqB,CAAC;IAC3E,GAAGL,SAAS;IACZkB,WAAW,EAAEzD,4BAAA,CAAA0D,WAAW,CAACa,IAAI;IAC7BX,gBAAgB,EAAE5D,4BAAA,CAAA6D,gBAAgB,CAACU;GACpC,CAAC;EAAC;EAAAnH,aAAA,GAAAoB,CAAA;EAEHyB,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;EAAC;EAAA9C,aAAA,GAAAoB,CAAA;EACpCyB,OAAO,CAACC,GAAG,CAAC,oBAAoBoE,mBAAmB,CAACvB,YAAY,CAACpC,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC;EAAC;EAAAvD,aAAA,GAAAoB,CAAA;EACxFyB,OAAO,CAACC,GAAG,CAAC,mBAAmBoE,mBAAmB,CAACP,kBAAkB,CAACE,WAAW,CAACtD,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;EAAC;EAAAvD,aAAA,GAAAoB,CAAA;EAChGyB,OAAO,CAACC,GAAG,CAAC,qBAAqBoE,mBAAmB,CAACP,kBAAkB,CAACG,aAAa,CAACvD,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;EAAC;EAAAvD,aAAA,GAAAoB,CAAA;EACpGyB,OAAO,CAACC,GAAG,CAAC,sBAAsBoE,mBAAmB,CAACP,kBAAkB,CAACI,cAAc,CAACxD,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;EAAC;EAAAvD,aAAA,GAAAoB,CAAA;EACtGyB,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC,CAACoE,mBAAmB,CAACvB,YAAY,GAAGS,aAAa,CAACT,YAAY,GAAG,CAAC,IAAI,GAAG,EAAEpC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;EAAC;EAAAvD,aAAA,GAAAoB,CAAA;EAE7H,IAAI8F,mBAAmB,CAACvC,eAAe,CAACF,MAAM,GAAG,CAAC,EAAE;IAAA;IAAAzE,aAAA,GAAAsB,CAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAClDyB,OAAO,CAACC,GAAG,CAAC,sBAAsBoE,mBAAmB,CAACvC,eAAe,CAACD,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;EACrF,CAAC;EAAA;EAAA;IAAA1E,aAAA,GAAAsB,CAAA;EAAA;EAAAtB,aAAA,GAAAoB,CAAA;EACDyB,OAAO,CAACC,GAAG,EAAE;AACf;AAEA;;;;AAIA,SAAgBR,+BAA+BA,CAAA;EAAA;EAAAtC,aAAA,GAAAqB,CAAA;EAAArB,aAAA,GAAAoB,CAAA;EAC7CyB,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;EAElD,MAAMqC,SAAS;EAAA;EAAA,CAAAnF,aAAA,GAAAoB,CAAA,SAAG;IAChB2B,QAAQ,EAAE,IAAI;IACdqC,iBAAiB,EAAE,EAAE;IACrBX,MAAM,EAAE,GAAG;IACXY,QAAQ,EAAE,kBAAkB;IAC5BlC,MAAM,EAAEP,4BAAA,CAAA6C,cAAc,CAACQ;GACxB;EAED;EACA,MAAMmB,cAAc;EAAA;EAAA,CAAApH,aAAA,GAAAoB,CAAA,SAAGwB,4BAAA,CAAA2C,0BAA0B,CAACC,qBAAqB,CAACL,SAAS,CAAC;EAAC;EAAAnF,aAAA,GAAAoB,CAAA;EAEnFyB,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;EAAC;EAAA9C,aAAA,GAAAoB,CAAA;EACtDyB,OAAO,CAACC,GAAG,CAAC,oBAAoBsE,cAAc,CAACzB,YAAY,CAACpC,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC;EAAC;EAAAvD,aAAA,GAAAoB,CAAA;EACnFyB,OAAO,CAACC,GAAG,CAAC,+BAA+BsE,cAAc,CAACC,wBAAwB,CAACjD,QAAQ,CAACb,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;EAE3G;EACA,MAAM+D,uBAAuB;EAAA;EAAA,CAAAtH,aAAA,GAAAoB,CAAA,SAAGwB,4BAAA,CAAA2C,0BAA0B,CAACC,qBAAqB,CAAC;IAC/E,GAAGL,SAAS;IACZtB,aAAa,EAAE;MACbC,WAAW,EAAE,GAAG;MAAG;MACnBC,QAAQ,EAAE,IAAI;MAAK;MACnBC,QAAQ,EAAE,EAAE,CAAO;;GAEtB,CAAC;EAAC;EAAAhE,aAAA,GAAAoB,CAAA;EAEHyB,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;EAAC;EAAA9C,aAAA,GAAAoB,CAAA;EACrEyB,OAAO,CAACC,GAAG,CAAC,oBAAoBwE,uBAAuB,CAAC3B,YAAY,CAACpC,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC;EAAC;EAAAvD,aAAA,GAAAoB,CAAA;EAC5FyB,OAAO,CAACC,GAAG,CAAC,+BAA+BwE,uBAAuB,CAACD,wBAAwB,CAACjD,QAAQ,CAACb,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;EAAC;EAAAvD,aAAA,GAAAoB,CAAA;EACnHyB,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC,CAACwE,uBAAuB,CAAC3B,YAAY,GAAGyB,cAAc,CAACzB,YAAY,GAAG,CAAC,IAAI,GAAG,EAAEpC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;EAAC;EAAAvD,aAAA,GAAAoB,CAAA;EAErI,IAAIkG,uBAAuB,CAAC9C,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;IAAA;IAAAzE,aAAA,GAAAsB,CAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAC/CyB,OAAO,CAACC,GAAG,CAAC,eAAewE,uBAAuB,CAAC9C,QAAQ,CAACE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;EAC3E,CAAC;EAAA;EAAA;IAAA1E,aAAA,GAAAsB,CAAA;EAAA;EAAAtB,aAAA,GAAAoB,CAAA;EACDyB,OAAO,CAACC,GAAG,EAAE;AACf;AAEA;;;;AAIA,SAAgBP,6BAA6BA,CAAA;EAAA;EAAAvC,aAAA,GAAAqB,CAAA;EAAArB,aAAA,GAAAoB,CAAA;EAC3CyB,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;EAEtD,MAAMyE,gBAAgB;EAAA;EAAA,CAAAvH,aAAA,GAAAoB,CAAA,SAAG;IACvB2B,QAAQ,EAAE,IAAI;IAAY;IAC1BqC,iBAAiB,EAAE,EAAE;IAAK;IAC1BX,MAAM,EAAE,GAAG;IAAe;IAC1BY,QAAQ,EAAE,kBAAkB;IAC5BgB,WAAW,EAAEzD,4BAAA,CAAA0D,WAAW,CAACkB,IAAI;IAC7BhB,gBAAgB,EAAE5D,4BAAA,CAAA6D,gBAAgB,CAACe,IAAI;IACvC3D,aAAa,EAAE;MACbC,WAAW,EAAE,EAAE;MACfC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE;;GAEb;EAAC;EAAAhE,aAAA,GAAAoB,CAAA;EAEFyB,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;EAAC;EAAA9C,aAAA,GAAAoB,CAAA;EAClCyB,OAAO,CAACC,GAAG,CAAC,eAAeyE,gBAAgB,CAACxE,QAAQ,MAAM,CAAC;EAAC;EAAA/C,aAAA,GAAAoB,CAAA;EAC5DyB,OAAO,CAACC,GAAG,CAAC,yBAAyByE,gBAAgB,CAACnC,iBAAiB,SAAS,CAAC;EAAC;EAAApF,aAAA,GAAAoB,CAAA;EAClFyB,OAAO,CAACC,GAAG,CAAC,aAAayE,gBAAgB,CAAC9C,MAAM,OAAO,CAAC;EAAC;EAAAzE,aAAA,GAAAoB,CAAA;EACzDyB,OAAO,CAACC,GAAG,CAAC,eAAeyE,gBAAgB,CAAClC,QAAQ,EAAE,CAAC;EAAC;EAAArF,aAAA,GAAAoB,CAAA;EACxDyB,OAAO,CAACC,GAAG,CAAC,iBAAiByE,gBAAgB,CAAC1D,aAAa,CAACC,WAAW,OAAOyD,gBAAgB,CAAC1D,aAAa,CAACE,QAAQ,QAAQwD,gBAAgB,CAAC1D,aAAa,CAACG,QAAQ,QAAQ,CAAC;EAE7K;EACA,MAAMyD,QAAQ;EAAA;EAAA,CAAAzH,aAAA,GAAAoB,CAAA,SAAGsB,4BAAA,CAAAO,0BAA0B,CAACC,yBAAyB,CAAC;IACpEH,QAAQ,EAAEwE,gBAAgB,CAACxE,QAAQ;IACnCI,MAAM,EAAET,4BAAA,CAAAU,sBAAsB,CAACQ,gBAAgB;IAC/CC,aAAa,EAAE0D,gBAAgB,CAAC1D;GACjC,CAAC;EAAC;EAAA7D,aAAA,GAAAoB,CAAA;EAEHyB,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;EAAC;EAAA9C,aAAA,GAAAoB,CAAA;EAC3CyB,OAAO,CAACC,GAAG,CAAC,wBAAwB2E,QAAQ,CAACnE,gBAAgB,CAACC,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC;EAAC;EAAAvD,aAAA,GAAAoB,CAAA;EACrFyB,OAAO,CAACC,GAAG,CAAC,kBAAkB2E,QAAQ,CAACxD,UAAU,CAACV,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC;EAAC;EAAAvD,aAAA,GAAAoB,CAAA;EACvEyB,OAAO,CAACC,GAAG,CAAC,aAAa2E,QAAQ,CAACtE,MAAM,EAAE,CAAC;EAAC;EAAAnD,aAAA,GAAAoB,CAAA;EAC5CyB,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC2E,QAAQ,CAACjE,QAAQ,GAAG,GAAG,EAAED,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;EAErE;EACA,MAAMmE,cAAc;EAAA;EAAA,CAAA1H,aAAA,GAAAoB,CAAA,SAAGwB,4BAAA,CAAA2C,0BAA0B,CAACC,qBAAqB,CAAC;IACtEzC,QAAQ,EAAEwE,gBAAgB,CAACxE,QAAQ;IACnCqC,iBAAiB,EAAEmC,gBAAgB,CAACnC,iBAAiB;IACrDX,MAAM,EAAE8C,gBAAgB,CAAC9C,MAAM;IAC/BY,QAAQ,EAAEkC,gBAAgB,CAAClC,QAAQ;IACnCgB,WAAW,EAAEkB,gBAAgB,CAAClB,WAAW;IACzCG,gBAAgB,EAAEe,gBAAgB,CAACf,gBAAgB;IACnD3C,aAAa,EAAE0D,gBAAgB,CAAC1D,aAAa;IAC7CV,MAAM,EAAEP,4BAAA,CAAA6C,cAAc,CAACQ;GACxB,CAAC;EAAC;EAAAjG,aAAA,GAAAoB,CAAA;EAEHyB,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;EAAC;EAAA9C,aAAA,GAAAoB,CAAA;EACvCyB,OAAO,CAACC,GAAG,CAAC,oBAAoB4E,cAAc,CAAC/B,YAAY,CAACpC,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC;EAAC;EAAAvD,aAAA,GAAAoB,CAAA;EACnFyB,OAAO,CAACC,GAAG,CAAC,oBAAoB4E,cAAc,CAAC9B,YAAY,CAACrC,OAAO,CAAC,CAAC,CAAC,kBAAkB,CAAC;EAAC;EAAAvD,aAAA,GAAAoB,CAAA;EAC1FyB,OAAO,CAACC,GAAG,CAAC,sBAAsB4E,cAAc,CAAC7B,cAAc,CAACtC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;EAAC;EAAAvD,aAAA,GAAAoB,CAAA;EAC9EyB,OAAO,CAACC,GAAG,CAAC,sBAAsB4E,cAAc,CAAC5B,cAAc,CAACvC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;EAAC;EAAAvD,aAAA,GAAAoB,CAAA;EAC9EyB,OAAO,CAACC,GAAG,CAAC,kBAAkB4E,cAAc,CAAC3B,UAAU,EAAE,CAAC;EAAC;EAAA/F,aAAA,GAAAoB,CAAA;EAC3DyB,OAAO,CAACC,GAAG,CAAC,aAAa4E,cAAc,CAACvE,MAAM,EAAE,CAAC;EAAC;EAAAnD,aAAA,GAAAoB,CAAA;EAClDyB,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC4E,cAAc,CAAClE,QAAQ,GAAG,GAAG,EAAED,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;EAE3E;EACA,MAAMoE,iBAAiB;EAAA;EAAA,CAAA3H,aAAA,GAAAoB,CAAA,SAAGqG,QAAQ,CAACnE,gBAAgB,GAAGoE,cAAc,CAAC/B,YAAY;EACjF,MAAMiC,kBAAkB;EAAA;EAAA,CAAA5H,aAAA,GAAAoB,CAAA,SAAIsG,cAAc,CAAC/B,YAAY,GAAGgC,iBAAiB,GAAI,GAAG;EAAC;EAAA3H,aAAA,GAAAoB,CAAA;EAEnFyB,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;EAAC;EAAA9C,aAAA,GAAAoB,CAAA;EAC/ByB,OAAO,CAACC,GAAG,CAAC,wBAAwB2E,QAAQ,CAACnE,gBAAgB,CAACC,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC;EAAC;EAAAvD,aAAA,GAAAoB,CAAA;EACrFyB,OAAO,CAACC,GAAG,CAAC,oBAAoB4E,cAAc,CAAC/B,YAAY,CAACpC,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC;EAAC;EAAAvD,aAAA,GAAAoB,CAAA;EACnFyB,OAAO,CAACC,GAAG,CAAC,0BAA0B6E,iBAAiB,CAACpE,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC;EAAC;EAAAvD,aAAA,GAAAoB,CAAA;EAC/EyB,OAAO,CAACC,GAAG,CAAC,0BAA0B8E,kBAAkB,CAACrE,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;EAEvE;EACA,MAAMsE,kBAAkB;EAAA;EAAA,CAAA7H,aAAA,GAAAoB,CAAA,SAAG,CAAC,GAAGqG,QAAQ,CAAC9C,eAAe,EAAE,GAAG+C,cAAc,CAAC/C,eAAe,CAAC;EAAC;EAAA3E,aAAA,GAAAoB,CAAA;EAC5F,IAAIyG,kBAAkB,CAACpD,MAAM,GAAG,CAAC,EAAE;IAAA;IAAAzE,aAAA,GAAAsB,CAAA;IAAAtB,aAAA,GAAAoB,CAAA;IACjCyB,OAAO,CAACC,GAAG,CAAC,sBAAsB+E,kBAAkB,CAACnD,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;EACpE,CAAC;EAAA;EAAA;IAAA1E,aAAA,GAAAsB,CAAA;EAAA;EAED;EACA,MAAMwG,WAAW;EAAA;EAAA,CAAA9H,aAAA,GAAAoB,CAAA,SAAG,CAAC,GAAGqG,QAAQ,CAACjD,QAAQ,EAAE,GAAGkD,cAAc,CAAClD,QAAQ,CAAC;EAAC;EAAAxE,aAAA,GAAAoB,CAAA;EACvE,IAAI0G,WAAW,CAACrD,MAAM,GAAG,CAAC,EAAE;IAAA;IAAAzE,aAAA,GAAAsB,CAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAC1ByB,OAAO,CAACC,GAAG,CAAC,eAAegF,WAAW,CAACpD,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;EACtD,CAAC;EAAA;EAAA;IAAA1E,aAAA,GAAAsB,CAAA;EAAA;EAAAtB,aAAA,GAAAoB,CAAA;EACDyB,OAAO,CAACC,GAAG,EAAE;AACf;AAEA;;;;AAIA,SAAgBN,uBAAuBA,CAAA;EAAA;EAAAxC,aAAA,GAAAqB,CAAA;EAAArB,aAAA,GAAAoB,CAAA;EACrCyB,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;EAE3D,MAAMiF,cAAc;EAAA;EAAA,CAAA/H,aAAA,GAAAoB,CAAA,SAAG;IACrB2B,QAAQ,EAAE,IAAI;IACdqC,iBAAiB,EAAE,EAAE;IACrBX,MAAM,EAAE,GAAG;IACXY,QAAQ,EAAE;GACX;EAAC;EAAArF,aAAA,GAAAoB,CAAA;EAEFyB,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;EAAC;EAAA9C,aAAA,GAAAoB,CAAA;EAC3CyB,OAAO,CAACC,GAAG,CAAC,qEAAqE,CAAC;EAAC;EAAA9C,aAAA,GAAAoB,CAAA;EACnFyB,OAAO,CAACC,GAAG,CAAC,qEAAqE,CAAC;EAElF,MAAMkF,OAAO;EAAA;EAAA,CAAAhI,aAAA,GAAAoB,CAAA,SAAG,CACdwB,4BAAA,CAAA6C,cAAc,CAACC,eAAe,EAC9B9C,4BAAA,CAAA6C,cAAc,CAACU,WAAW,EAC1BvD,4BAAA,CAAA6C,cAAc,CAACwC,OAAO,EACtBrF,4BAAA,CAAA6C,cAAc,CAACyC,IAAI,EACnBtF,4BAAA,CAAA6C,cAAc,CAACQ,cAAc,CAC9B;EAAC;EAAAjG,aAAA,GAAAoB,CAAA;EAEF4G,OAAO,CAACG,OAAO,CAAChF,MAAM,IAAG;IAAA;IAAAnD,aAAA,GAAAqB,CAAA;IACvB,MAAM+G,MAAM;IAAA;IAAA,CAAApI,aAAA,GAAAoB,CAAA,SAAGwB,4BAAA,CAAA2C,0BAA0B,CAACC,qBAAqB,CAAC;MAC9D,GAAGuC,cAAc;MACjB5E;KACD,CAAC;IAEF,MAAMkF,UAAU;IAAA;IAAA,CAAArI,aAAA,GAAAoB,CAAA,SAAG+B,MAAM,CAACmF,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACC,WAAW,EAAE,CACvDD,OAAO,CAAC,OAAO,EAAEE,CAAC,IAAI;MAAA;MAAAxI,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MAAA,OAAAoH,CAAC,CAACC,WAAW,EAAE;IAAF,CAAE,CAAC,CAACC,MAAM,CAAC,EAAE,CAAC;IACpD,MAAM/C,YAAY;IAAA;IAAA,CAAA3F,aAAA,GAAAoB,CAAA,SAAGgH,MAAM,CAACzC,YAAY,CAACpC,OAAO,CAAC,CAAC,CAAC,CAACoF,QAAQ,CAAC,EAAE,CAAC;IAChE,MAAMnF,QAAQ;IAAA;IAAA,CAAAxD,aAAA,GAAAoB,CAAA,SAAG,GAAG,CAACgH,MAAM,CAAC5E,QAAQ,GAAG,GAAG,EAAED,OAAO,CAAC,CAAC,CAAC,GAAG,CAACoF,QAAQ,CAAC,CAAC,CAAC;IACrE,MAAMC,WAAW;IAAA;IAAA,CAAA5I,aAAA,GAAAoB,CAAA,SAAGgH,MAAM,CAAC3E,kBAAkB,CAACC,OAAO,CAACmF,QAAQ,CAAC,WAAW,CAAC;IAAA;IAAA,CAAA7I,aAAA,GAAAsB,CAAA,UAAG,WAAW;IAAA;IAAA,CAAAtB,aAAA,GAAAsB,CAAA,UAAG,UAAU;IAAC;IAAAtB,aAAA,GAAAoB,CAAA;IAEvGyB,OAAO,CAACC,GAAG,CAAC,GAAGuF,UAAU,KAAK1C,YAAY,MAAMnC,QAAQ,MAAMoF,WAAW,EAAE,CAAC;EAC9E,CAAC,CAAC;EAAC;EAAA5I,aAAA,GAAAoB,CAAA;EAEHyB,OAAO,CAACC,GAAG,EAAE;EAEb;EACA,MAAM8B,aAAa;EAAA;EAAA,CAAA5E,aAAA,GAAAoB,CAAA,SAAGwB,4BAAA,CAAA2C,0BAA0B,CAACV,gBAAgB,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC;EAAC;EAAA7E,aAAA,GAAAoB,CAAA;EACxFyB,OAAO,CAACC,GAAG,CAAC,kCAAkC8B,aAAa,EAAE,CAAC;EAAC;EAAA5E,aAAA,GAAAoB,CAAA;EAC/DyB,OAAO,CAACC,GAAG,CAAC,4FAA4F,CAAC;AAC3G;AAEA;;;AAGA,SAAgBL,iCAAiCA,CAAA;EAAA;EAAAzC,aAAA,GAAAqB,CAAA;EAAArB,aAAA,GAAAoB,CAAA;EAC/CyB,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;EAAC;EAAA9C,aAAA,GAAAoB,CAAA;EACvEyB,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;EAAC;EAAA9C,aAAA,GAAAoB,CAAA;EAEvEc,4BAA4B,EAAE;EAAC;EAAAlC,aAAA,GAAAoB,CAAA;EAC/Be,8BAA8B,EAAE;EAAC;EAAAnC,aAAA,GAAAoB,CAAA;EACjCgB,uBAAuB,EAAE;EAAC;EAAApC,aAAA,GAAAoB,CAAA;EAC1BiB,oBAAoB,EAAE;EAAC;EAAArC,aAAA,GAAAoB,CAAA;EACvBkB,+BAA+B,EAAE;EAAC;EAAAtC,aAAA,GAAAoB,CAAA;EAClCmB,6BAA6B,EAAE;EAAC;EAAAvC,aAAA,GAAAoB,CAAA;EAChCoB,uBAAuB,EAAE;EAAC;EAAAxC,aAAA,GAAAoB,CAAA;EAE1ByB,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;EAAC;EAAA9C,aAAA,GAAAoB,CAAA;EACpDyB,OAAO,CAACC,GAAG,CAAC,gGAAgG,CAAC;AAC/G", "ignoreList": []}