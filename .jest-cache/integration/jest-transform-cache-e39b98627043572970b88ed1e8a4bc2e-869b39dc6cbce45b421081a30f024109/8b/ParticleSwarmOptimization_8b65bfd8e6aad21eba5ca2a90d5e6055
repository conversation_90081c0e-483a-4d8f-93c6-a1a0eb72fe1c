b6fcad674d8ab727ad4e335bfa875cba
"use strict";

/**
 * Particle Swarm Optimization Algorithm Implementation for System Optimization
 *
 * Implements particle swarm optimization with:
 * - Configurable swarm topology (global, local, ring, star)
 * - Adaptive inertia weight and acceleration coefficients
 * - Constraint handling with penalty methods
 * - Multi-objective optimization support
 * - Velocity clamping and boundary handling
 *
 * @version 3.0.0
 * <AUTHOR> Suite Development Team
 */
/* istanbul ignore next */
function cov_161daubuf() {
  var path = "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\algorithms\\ParticleSwarmOptimization.ts";
  var hash = "f49aa23fb815c4d2d72eda98db3a54f25a162e44";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\algorithms\\ParticleSwarmOptimization.ts",
    statementMap: {
      "0": {
        start: {
          line: 15,
          column: 0
        },
        end: {
          line: 15,
          column: 62
        }
      },
      "1": {
        start: {
          line: 16,
          column: 0
        },
        end: {
          line: 16,
          column: 43
        }
      },
      "2": {
        start: {
          line: 17,
          column: 34
        },
        end: {
          line: 17,
          column: 77
        }
      },
      "3": {
        start: {
          line: 23,
          column: 8
        },
        end: {
          line: 23,
          column: 31
        }
      },
      "4": {
        start: {
          line: 24,
          column: 8
        },
        end: {
          line: 24,
          column: 33
        }
      },
      "5": {
        start: {
          line: 25,
          column: 8
        },
        end: {
          line: 25,
          column: 26
        }
      },
      "6": {
        start: {
          line: 26,
          column: 8
        },
        end: {
          line: 26,
          column: 36
        }
      },
      "7": {
        start: {
          line: 27,
          column: 8
        },
        end: {
          line: 27,
          column: 33
        }
      },
      "8": {
        start: {
          line: 28,
          column: 8
        },
        end: {
          line: 46,
          column: 10
        }
      },
      "9": {
        start: {
          line: 48,
          column: 8
        },
        end: {
          line: 53,
          column: 9
        }
      },
      "10": {
        start: {
          line: 49,
          column: 12
        },
        end: {
          line: 49,
          column: 77
        }
      },
      "11": {
        start: {
          line: 52,
          column: 12
        },
        end: {
          line: 52,
          column: 38
        }
      },
      "12": {
        start: {
          line: 59,
          column: 26
        },
        end: {
          line: 59,
          column: 43
        }
      },
      "13": {
        start: {
          line: 60,
          column: 8
        },
        end: {
          line: 82,
          column: 9
        }
      },
      "14": {
        start: {
          line: 62,
          column: 12
        },
        end: {
          line: 62,
          column: 46
        }
      },
      "15": {
        start: {
          line: 64,
          column: 12
        },
        end: {
          line: 64,
          column: 91
        }
      },
      "16": {
        start: {
          line: 66,
          column: 12
        },
        end: {
          line: 75,
          column: 13
        }
      },
      "17": {
        start: {
          line: 67,
          column: 16
        },
        end: {
          line: 67,
          column: 88
        }
      },
      "18": {
        start: {
          line: 68,
          column: 16
        },
        end: {
          line: 68,
          column: 37
        }
      },
      "19": {
        start: {
          line: 69,
          column: 16
        },
        end: {
          line: 71,
          column: 17
        }
      },
      "20": {
        start: {
          line: 70,
          column: 20
        },
        end: {
          line: 70,
          column: 43
        }
      },
      "21": {
        start: {
          line: 72,
          column: 16
        },
        end: {
          line: 74,
          column: 17
        }
      },
      "22": {
        start: {
          line: 73,
          column: 20
        },
        end: {
          line: 73,
          column: 52
        }
      },
      "23": {
        start: {
          line: 77,
          column: 12
        },
        end: {
          line: 77,
          column: 69
        }
      },
      "24": {
        start: {
          line: 80,
          column: 12
        },
        end: {
          line: 80,
          column: 72
        }
      },
      "25": {
        start: {
          line: 81,
          column: 12
        },
        end: {
          line: 81,
          column: 24
        }
      },
      "26": {
        start: {
          line: 88,
          column: 8
        },
        end: {
          line: 88,
          column: 31
        }
      },
      "27": {
        start: {
          line: 89,
          column: 8
        },
        end: {
          line: 89,
          column: 33
        }
      },
      "28": {
        start: {
          line: 90,
          column: 8
        },
        end: {
          line: 90,
          column: 26
        }
      },
      "29": {
        start: {
          line: 91,
          column: 8
        },
        end: {
          line: 91,
          column: 36
        }
      },
      "30": {
        start: {
          line: 92,
          column: 8
        },
        end: {
          line: 92,
          column: 33
        }
      },
      "31": {
        start: {
          line: 93,
          column: 8
        },
        end: {
          line: 93,
          column: 110
        }
      },
      "32": {
        start: {
          line: 99,
          column: 26
        },
        end: {
          line: 99,
          column: 28
        }
      },
      "33": {
        start: {
          line: 101,
          column: 8
        },
        end: {
          line: 105,
          column: 9
        }
      },
      "34": {
        start: {
          line: 101,
          column: 21
        },
        end: {
          line: 101,
          column: 22
        }
      },
      "35": {
        start: {
          line: 102,
          column: 29
        },
        end: {
          line: 102,
          column: 63
        }
      },
      "36": {
        start: {
          line: 103,
          column: 12
        },
        end: {
          line: 103,
          column: 99
        }
      },
      "37": {
        start: {
          line: 104,
          column: 12
        },
        end: {
          line: 104,
          column: 37
        }
      },
      "38": {
        start: {
          line: 107,
          column: 8
        },
        end: {
          line: 114,
          column: 10
        }
      },
      "39": {
        start: {
          line: 116,
          column: 8
        },
        end: {
          line: 116,
          column: 29
        }
      },
      "40": {
        start: {
          line: 118,
          column: 8
        },
        end: {
          line: 118,
          column: 41
        }
      },
      "41": {
        start: {
          line: 124,
          column: 25
        },
        end: {
          line: 124,
          column: 27
        }
      },
      "42": {
        start: {
          line: 125,
          column: 25
        },
        end: {
          line: 125,
          column: 27
        }
      },
      "43": {
        start: {
          line: 126,
          column: 8
        },
        end: {
          line: 142,
          column: 9
        }
      },
      "44": {
        start: {
          line: 127,
          column: 12
        },
        end: {
          line: 141,
          column: 13
        }
      },
      "45": {
        start: {
          line: 129,
          column: 36
        },
        end: {
          line: 129,
          column: 94
        }
      },
      "46": {
        start: {
          line: 130,
          column: 16
        },
        end: {
          line: 130,
          column: 68
        }
      },
      "47": {
        start: {
          line: 131,
          column: 16
        },
        end: {
          line: 131,
          column: 33
        }
      },
      "48": {
        start: {
          line: 135,
          column: 28
        },
        end: {
          line: 135,
          column: 101
        }
      },
      "49": {
        start: {
          line: 136,
          column: 28
        },
        end: {
          line: 136,
          column: 101
        }
      },
      "50": {
        start: {
          line: 137,
          column: 28
        },
        end: {
          line: 137,
          column: 61
        }
      },
      "51": {
        start: {
          line: 138,
          column: 28
        },
        end: {
          line: 138,
          column: 93
        }
      },
      "52": {
        start: {
          line: 139,
          column: 16
        },
        end: {
          line: 139,
          column: 35
        }
      },
      "53": {
        start: {
          line: 140,
          column: 16
        },
        end: {
          line: 140,
          column: 35
        }
      },
      "54": {
        start: {
          line: 143,
          column: 25
        },
        end: {
          line: 155,
          column: 9
        }
      },
      "55": {
        start: {
          line: 156,
          column: 8
        },
        end: {
          line: 156,
          column: 24
        }
      },
      "56": {
        start: {
          line: 162,
          column: 8
        },
        end: {
          line: 195,
          column: 9
        }
      },
      "57": {
        start: {
          line: 164,
          column: 30
        },
        end: {
          line: 164,
          column: 83
        }
      },
      "58": {
        start: {
          line: 166,
          column: 35
        },
        end: {
          line: 166,
          column: 63
        }
      },
      "59": {
        start: {
          line: 167,
          column: 12
        },
        end: {
          line: 167,
          column: 46
        }
      },
      "60": {
        start: {
          line: 169,
          column: 41
        },
        end: {
          line: 169,
          column: 43
        }
      },
      "61": {
        start: {
          line: 170,
          column: 12
        },
        end: {
          line: 173,
          column: 13
        }
      },
      "62": {
        start: {
          line: 171,
          column: 34
        },
        end: {
          line: 171,
          column: 63
        }
      },
      "63": {
        start: {
          line: 172,
          column: 16
        },
        end: {
          line: 172,
          column: 53
        }
      },
      "64": {
        start: {
          line: 175,
          column: 12
        },
        end: {
          line: 180,
          column: 13
        }
      },
      "65": {
        start: {
          line: 176,
          column: 37
        },
        end: {
          line: 178,
          column: 88
        }
      },
      "66": {
        start: {
          line: 177,
          column: 33
        },
        end: {
          line: 177,
          column: 38
        }
      },
      "67": {
        start: {
          line: 178,
          column: 40
        },
        end: {
          line: 178,
          column: 47
        }
      },
      "68": {
        start: {
          line: 179,
          column: 16
        },
        end: {
          line: 179,
          column: 49
        }
      },
      "69": {
        start: {
          line: 182,
          column: 12
        },
        end: {
          line: 189,
          column: 13
        }
      },
      "70": {
        start: {
          line: 183,
          column: 16
        },
        end: {
          line: 183,
          column: 72
        }
      },
      "71": {
        start: {
          line: 184,
          column: 16
        },
        end: {
          line: 184,
          column: 65
        }
      },
      "72": {
        start: {
          line: 185,
          column: 16
        },
        end: {
          line: 185,
          column: 45
        }
      },
      "73": {
        start: {
          line: 188,
          column: 16
        },
        end: {
          line: 188,
          column: 43
        }
      },
      "74": {
        start: {
          line: 190,
          column: 12
        },
        end: {
          line: 190,
          column: 35
        }
      },
      "75": {
        start: {
          line: 193,
          column: 12
        },
        end: {
          line: 193,
          column: 63
        }
      },
      "76": {
        start: {
          line: 194,
          column: 12
        },
        end: {
          line: 194,
          column: 48
        }
      },
      "77": {
        start: {
          line: 201,
          column: 8
        },
        end: {
          line: 204,
          column: 12
        }
      },
      "78": {
        start: {
          line: 201,
          column: 59
        },
        end: {
          line: 204,
          column: 9
        }
      },
      "79": {
        start: {
          line: 210,
          column: 8
        },
        end: {
          line: 211,
          column: 19
        }
      },
      "80": {
        start: {
          line: 211,
          column: 12
        },
        end: {
          line: 211,
          column: 19
        }
      },
      "81": {
        start: {
          line: 213,
          column: 8
        },
        end: {
          line: 222,
          column: 9
        }
      },
      "82": {
        start: {
          line: 215,
          column: 12
        },
        end: {
          line: 215,
          column: 59
        }
      },
      "83": {
        start: {
          line: 217,
          column: 12
        },
        end: {
          line: 217,
          column: 59
        }
      },
      "84": {
        start: {
          line: 219,
          column: 12
        },
        end: {
          line: 219,
          column: 99
        }
      },
      "85": {
        start: {
          line: 221,
          column: 12
        },
        end: {
          line: 221,
          column: 27
        }
      },
      "86": {
        start: {
          line: 224,
          column: 30
        },
        end: {
          line: 224,
          column: 76
        }
      },
      "87": {
        start: {
          line: 225,
          column: 8
        },
        end: {
          line: 228,
          column: 9
        }
      },
      "88": {
        start: {
          line: 226,
          column: 12
        },
        end: {
          line: 226,
          column: 55
        }
      },
      "89": {
        start: {
          line: 227,
          column: 12
        },
        end: {
          line: 227,
          column: 45
        }
      },
      "90": {
        start: {
          line: 230,
          column: 8
        },
        end: {
          line: 230,
          column: 97
        }
      },
      "91": {
        start: {
          line: 231,
          column: 8
        },
        end: {
          line: 231,
          column: 97
        }
      },
      "92": {
        start: {
          line: 232,
          column: 8
        },
        end: {
          line: 232,
          column: 36
        }
      },
      "93": {
        start: {
          line: 238,
          column: 25
        },
        end: {
          line: 238,
          column: 65
        }
      },
      "94": {
        start: {
          line: 239,
          column: 18
        },
        end: {
          line: 239,
          column: 48
        }
      },
      "95": {
        start: {
          line: 241,
          column: 33
        },
        end: {
          line: 241,
          column: 68
        }
      },
      "96": {
        start: {
          line: 242,
          column: 8
        },
        end: {
          line: 258,
          column: 9
        }
      },
      "97": {
        start: {
          line: 242,
          column: 21
        },
        end: {
          line: 242,
          column: 22
        }
      },
      "98": {
        start: {
          line: 243,
          column: 12
        },
        end: {
          line: 257,
          column: 13
        }
      },
      "99": {
        start: {
          line: 244,
          column: 35
        },
        end: {
          line: 244,
          column: 55
        }
      },
      "100": {
        start: {
          line: 245,
          column: 40
        },
        end: {
          line: 245,
          column: 73
        }
      },
      "101": {
        start: {
          line: 246,
          column: 44
        },
        end: {
          line: 246,
          column: 72
        }
      },
      "102": {
        start: {
          line: 248,
          column: 34
        },
        end: {
          line: 248,
          column: 85
        }
      },
      "103": {
        start: {
          line: 249,
          column: 31
        },
        end: {
          line: 249,
          column: 86
        }
      },
      "104": {
        start: {
          line: 250,
          column: 16
        },
        end: {
          line: 250,
          column: 85
        }
      },
      "105": {
        start: {
          line: 252,
          column: 33
        },
        end: {
          line: 252,
          column: 53
        }
      },
      "106": {
        start: {
          line: 253,
          column: 28
        },
        end: {
          line: 253,
          column: 101
        }
      },
      "107": {
        start: {
          line: 254,
          column: 28
        },
        end: {
          line: 254,
          column: 101
        }
      },
      "108": {
        start: {
          line: 255,
          column: 31
        },
        end: {
          line: 255,
          column: 72
        }
      },
      "109": {
        start: {
          line: 256,
          column: 16
        },
        end: {
          line: 256,
          column: 97
        }
      },
      "110": {
        start: {
          line: 264,
          column: 8
        },
        end: {
          line: 281,
          column: 9
        }
      },
      "111": {
        start: {
          line: 264,
          column: 21
        },
        end: {
          line: 264,
          column: 22
        }
      },
      "112": {
        start: {
          line: 265,
          column: 29
        },
        end: {
          line: 265,
          column: 49
        }
      },
      "113": {
        start: {
          line: 266,
          column: 12
        },
        end: {
          line: 280,
          column: 13
        }
      },
      "114": {
        start: {
          line: 268,
          column: 16
        },
        end: {
          line: 271,
          column: 17
        }
      },
      "115": {
        start: {
          line: 269,
          column: 40
        },
        end: {
          line: 269,
          column: 98
        }
      },
      "116": {
        start: {
          line: 270,
          column: 20
        },
        end: {
          line: 270,
          column: 80
        }
      },
      "117": {
        start: {
          line: 273,
          column: 17
        },
        end: {
          line: 280,
          column: 13
        }
      },
      "118": {
        start: {
          line: 275,
          column: 31
        },
        end: {
          line: 275,
          column: 74
        }
      },
      "119": {
        start: {
          line: 277,
          column: 28
        },
        end: {
          line: 277,
          column: 101
        }
      },
      "120": {
        start: {
          line: 278,
          column: 28
        },
        end: {
          line: 278,
          column: 101
        }
      },
      "121": {
        start: {
          line: 279,
          column: 16
        },
        end: {
          line: 279,
          column: 102
        }
      },
      "122": {
        start: {
          line: 287,
          column: 8
        },
        end: {
          line: 311,
          column: 9
        }
      },
      "123": {
        start: {
          line: 289,
          column: 16
        },
        end: {
          line: 294,
          column: 17
        }
      },
      "124": {
        start: {
          line: 290,
          column: 20
        },
        end: {
          line: 290,
          column: 50
        }
      },
      "125": {
        start: {
          line: 292,
          column: 21
        },
        end: {
          line: 294,
          column: 17
        }
      },
      "126": {
        start: {
          line: 293,
          column: 20
        },
        end: {
          line: 293,
          column: 50
        }
      },
      "127": {
        start: {
          line: 295,
          column: 16
        },
        end: {
          line: 295,
          column: 32
        }
      },
      "128": {
        start: {
          line: 297,
          column: 16
        },
        end: {
          line: 297,
          column: 62
        }
      },
      "129": {
        start: {
          line: 300,
          column: 16
        },
        end: {
          line: 302,
          column: 17
        }
      },
      "130": {
        start: {
          line: 301,
          column: 20
        },
        end: {
          line: 301,
          column: 66
        }
      },
      "131": {
        start: {
          line: 303,
          column: 16
        },
        end: {
          line: 303,
          column: 32
        }
      },
      "132": {
        start: {
          line: 305,
          column: 16
        },
        end: {
          line: 307,
          column: 17
        }
      },
      "133": {
        start: {
          line: 306,
          column: 20
        },
        end: {
          line: 306,
          column: 61
        }
      },
      "134": {
        start: {
          line: 308,
          column: 16
        },
        end: {
          line: 308,
          column: 32
        }
      },
      "135": {
        start: {
          line: 310,
          column: 16
        },
        end: {
          line: 310,
          column: 62
        }
      },
      "136": {
        start: {
          line: 317,
          column: 21
        },
        end: {
          line: 317,
          column: 105
        }
      },
      "137": {
        start: {
          line: 317,
          column: 57
        },
        end: {
          line: 317,
          column: 104
        }
      },
      "138": {
        start: {
          line: 318,
          column: 8
        },
        end: {
          line: 322,
          column: 10
        }
      },
      "139": {
        start: {
          line: 328,
          column: 8
        },
        end: {
          line: 329,
          column: 78
        }
      },
      "140": {
        start: {
          line: 329,
          column: 12
        },
        end: {
          line: 329,
          column: 78
        }
      },
      "141": {
        start: {
          line: 330,
          column: 8
        },
        end: {
          line: 346,
          column: 9
        }
      },
      "142": {
        start: {
          line: 332,
          column: 16
        },
        end: {
          line: 332,
          column: 50
        }
      },
      "143": {
        start: {
          line: 336,
          column: 27
        },
        end: {
          line: 336,
          column: 48
        }
      },
      "144": {
        start: {
          line: 337,
          column: 16
        },
        end: {
          line: 342,
          column: 17
        }
      },
      "145": {
        start: {
          line: 338,
          column: 37
        },
        end: {
          line: 338,
          column: 93
        }
      },
      "146": {
        start: {
          line: 338,
          column: 73
        },
        end: {
          line: 338,
          column: 92
        }
      },
      "147": {
        start: {
          line: 339,
          column: 20
        },
        end: {
          line: 341,
          column: 21
        }
      },
      "148": {
        start: {
          line: 340,
          column: 24
        },
        end: {
          line: 340,
          column: 53
        }
      },
      "149": {
        start: {
          line: 343,
          column: 16
        },
        end: {
          line: 343,
          column: 28
        }
      },
      "150": {
        start: {
          line: 345,
          column: 16
        },
        end: {
          line: 345,
          column: 50
        }
      },
      "151": {
        start: {
          line: 352,
          column: 8
        },
        end: {
          line: 353,
          column: 19
        }
      },
      "152": {
        start: {
          line: 353,
          column: 12
        },
        end: {
          line: 353,
          column: 19
        }
      },
      "153": {
        start: {
          line: 354,
          column: 26
        },
        end: {
          line: 354,
          column: 51
        }
      },
      "154": {
        start: {
          line: 355,
          column: 8
        },
        end: {
          line: 400,
          column: 9
        }
      },
      "155": {
        start: {
          line: 357,
          column: 16
        },
        end: {
          line: 361,
          column: 17
        }
      },
      "156": {
        start: {
          line: 357,
          column: 29
        },
        end: {
          line: 357,
          column: 30
        }
      },
      "157": {
        start: {
          line: 358,
          column: 33
        },
        end: {
          line: 358,
          column: 78
        }
      },
      "158": {
        start: {
          line: 359,
          column: 33
        },
        end: {
          line: 359,
          column: 59
        }
      },
      "159": {
        start: {
          line: 360,
          column: 20
        },
        end: {
          line: 360,
          column: 86
        }
      },
      "160": {
        start: {
          line: 362,
          column: 16
        },
        end: {
          line: 362,
          column: 22
        }
      },
      "161": {
        start: {
          line: 364,
          column: 16
        },
        end: {
          line: 373,
          column: 17
        }
      },
      "162": {
        start: {
          line: 364,
          column: 29
        },
        end: {
          line: 364,
          column: 30
        }
      },
      "163": {
        start: {
          line: 365,
          column: 38
        },
        end: {
          line: 365,
          column: 40
        }
      },
      "164": {
        start: {
          line: 366,
          column: 20
        },
        end: {
          line: 371,
          column: 21
        }
      },
      "165": {
        start: {
          line: 366,
          column: 33
        },
        end: {
          line: 366,
          column: 34
        }
      },
      "166": {
        start: {
          line: 367,
          column: 46
        },
        end: {
          line: 367,
          column: 76
        }
      },
      "167": {
        start: {
          line: 368,
          column: 24
        },
        end: {
          line: 370,
          column: 25
        }
      },
      "168": {
        start: {
          line: 369,
          column: 28
        },
        end: {
          line: 369,
          column: 72
        }
      },
      "169": {
        start: {
          line: 372,
          column: 20
        },
        end: {
          line: 372,
          column: 55
        }
      },
      "170": {
        start: {
          line: 374,
          column: 16
        },
        end: {
          line: 374,
          column: 22
        }
      },
      "171": {
        start: {
          line: 377,
          column: 37
        },
        end: {
          line: 377,
          column: 67
        }
      },
      "172": {
        start: {
          line: 378,
          column: 16
        },
        end: {
          line: 382,
          column: 17
        }
      },
      "173": {
        start: {
          line: 379,
          column: 20
        },
        end: {
          line: 381,
          column: 21
        }
      },
      "174": {
        start: {
          line: 380,
          column: 24
        },
        end: {
          line: 380,
          column: 71
        }
      },
      "175": {
        start: {
          line: 383,
          column: 16
        },
        end: {
          line: 383,
          column: 22
        }
      },
      "176": {
        start: {
          line: 385,
          column: 16
        },
        end: {
          line: 395,
          column: 17
        }
      },
      "177": {
        start: {
          line: 386,
          column: 38
        },
        end: {
          line: 386,
          column: 40
        }
      },
      "178": {
        start: {
          line: 387,
          column: 20
        },
        end: {
          line: 393,
          column: 21
        }
      },
      "179": {
        start: {
          line: 387,
          column: 33
        },
        end: {
          line: 387,
          column: 34
        }
      },
      "180": {
        start: {
          line: 388,
          column: 44
        },
        end: {
          line: 388,
          column: 88
        }
      },
      "181": {
        start: {
          line: 389,
          column: 24
        },
        end: {
          line: 392,
          column: 25
        }
      },
      "182": {
        start: {
          line: 391,
          column: 28
        },
        end: {
          line: 391,
          column: 70
        }
      },
      "183": {
        start: {
          line: 394,
          column: 20
        },
        end: {
          line: 394,
          column: 51
        }
      },
      "184": {
        start: {
          line: 396,
          column: 16
        },
        end: {
          line: 396,
          column: 22
        }
      },
      "185": {
        start: {
          line: 399,
          column: 16
        },
        end: {
          line: 399,
          column: 22
        }
      },
      "186": {
        start: {
          line: 406,
          column: 8
        },
        end: {
          line: 408,
          column: 9
        }
      },
      "187": {
        start: {
          line: 407,
          column: 12
        },
        end: {
          line: 407,
          column: 49
        }
      },
      "188": {
        start: {
          line: 410,
          column: 25
        },
        end: {
          line: 410,
          column: 82
        }
      },
      "189": {
        start: {
          line: 411,
          column: 8
        },
        end: {
          line: 412,
          column: 93
        }
      },
      "190": {
        start: {
          line: 418,
          column: 8
        },
        end: {
          line: 419,
          column: 19
        }
      },
      "191": {
        start: {
          line: 419,
          column: 12
        },
        end: {
          line: 419,
          column: 19
        }
      },
      "192": {
        start: {
          line: 421,
          column: 8
        },
        end: {
          line: 430,
          column: 9
        }
      },
      "193": {
        start: {
          line: 423,
          column: 12
        },
        end: {
          line: 423,
          column: 95
        }
      },
      "194": {
        start: {
          line: 424,
          column: 12
        },
        end: {
          line: 424,
          column: 91
        }
      },
      "195": {
        start: {
          line: 426,
          column: 13
        },
        end: {
          line: 430,
          column: 9
        }
      },
      "196": {
        start: {
          line: 428,
          column: 12
        },
        end: {
          line: 428,
          column: 95
        }
      },
      "197": {
        start: {
          line: 429,
          column: 12
        },
        end: {
          line: 429,
          column: 92
        }
      },
      "198": {
        start: {
          line: 436,
          column: 8
        },
        end: {
          line: 437,
          column: 19
        }
      },
      "199": {
        start: {
          line: 437,
          column: 12
        },
        end: {
          line: 437,
          column: 19
        }
      },
      "200": {
        start: {
          line: 439,
          column: 8
        },
        end: {
          line: 445,
          column: 9
        }
      },
      "201": {
        start: {
          line: 440,
          column: 12
        },
        end: {
          line: 444,
          column: 13
        }
      },
      "202": {
        start: {
          line: 442,
          column: 36
        },
        end: {
          line: 442,
          column: 70
        }
      },
      "203": {
        start: {
          line: 443,
          column: 16
        },
        end: {
          line: 443,
          column: 53
        }
      },
      "204": {
        start: {
          line: 451,
          column: 31
        },
        end: {
          line: 451,
          column: 96
        }
      },
      "205": {
        start: {
          line: 451,
          column: 50
        },
        end: {
          line: 451,
          column: 59
        }
      },
      "206": {
        start: {
          line: 451,
          column: 73
        },
        end: {
          line: 451,
          column: 95
        }
      },
      "207": {
        start: {
          line: 452,
          column: 8
        },
        end: {
          line: 454,
          column: 29
        }
      },
      "208": {
        start: {
          line: 453,
          column: 46
        },
        end: {
          line: 453,
          column: 53
        }
      },
      "209": {
        start: {
          line: 460,
          column: 8
        },
        end: {
          line: 461,
          column: 21
        }
      },
      "210": {
        start: {
          line: 461,
          column: 12
        },
        end: {
          line: 461,
          column: 21
        }
      },
      "211": {
        start: {
          line: 462,
          column: 28
        },
        end: {
          line: 462,
          column: 29
        }
      },
      "212": {
        start: {
          line: 463,
          column: 24
        },
        end: {
          line: 463,
          column: 25
        }
      },
      "213": {
        start: {
          line: 464,
          column: 8
        },
        end: {
          line: 470,
          column: 9
        }
      },
      "214": {
        start: {
          line: 464,
          column: 21
        },
        end: {
          line: 464,
          column: 22
        }
      },
      "215": {
        start: {
          line: 465,
          column: 12
        },
        end: {
          line: 469,
          column: 13
        }
      },
      "216": {
        start: {
          line: 465,
          column: 25
        },
        end: {
          line: 465,
          column: 30
        }
      },
      "217": {
        start: {
          line: 466,
          column: 33
        },
        end: {
          line: 466,
          column: 83
        }
      },
      "218": {
        start: {
          line: 467,
          column: 16
        },
        end: {
          line: 467,
          column: 42
        }
      },
      "219": {
        start: {
          line: 468,
          column: 16
        },
        end: {
          line: 468,
          column: 28
        }
      },
      "220": {
        start: {
          line: 471,
          column: 8
        },
        end: {
          line: 471,
          column: 61
        }
      },
      "221": {
        start: {
          line: 477,
          column: 23
        },
        end: {
          line: 477,
          column: 24
        }
      },
      "222": {
        start: {
          line: 478,
          column: 8
        },
        end: {
          line: 483,
          column: 9
        }
      },
      "223": {
        start: {
          line: 478,
          column: 21
        },
        end: {
          line: 478,
          column: 22
        }
      },
      "224": {
        start: {
          line: 479,
          column: 12
        },
        end: {
          line: 482,
          column: 13
        }
      },
      "225": {
        start: {
          line: 480,
          column: 29
        },
        end: {
          line: 480,
          column: 74
        }
      },
      "226": {
        start: {
          line: 481,
          column: 16
        },
        end: {
          line: 481,
          column: 40
        }
      },
      "227": {
        start: {
          line: 484,
          column: 8
        },
        end: {
          line: 484,
          column: 35
        }
      },
      "228": {
        start: {
          line: 490,
          column: 8
        },
        end: {
          line: 491,
          column: 19
        }
      },
      "229": {
        start: {
          line: 491,
          column: 12
        },
        end: {
          line: 491,
          column: 19
        }
      },
      "230": {
        start: {
          line: 492,
          column: 29
        },
        end: {
          line: 492,
          column: 112
        }
      },
      "231": {
        start: {
          line: 492,
          column: 65
        },
        end: {
          line: 492,
          column: 111
        }
      },
      "232": {
        start: {
          line: 493,
          column: 8
        },
        end: {
          line: 494,
          column: 19
        }
      },
      "233": {
        start: {
          line: 494,
          column: 12
        },
        end: {
          line: 494,
          column: 19
        }
      },
      "234": {
        start: {
          line: 495,
          column: 26
        },
        end: {
          line: 495,
          column: 28
        }
      },
      "235": {
        start: {
          line: 496,
          column: 8
        },
        end: {
          line: 498,
          column: 11
        }
      },
      "236": {
        start: {
          line: 497,
          column: 12
        },
        end: {
          line: 497,
          column: 66
        }
      },
      "237": {
        start: {
          line: 499,
          column: 8
        },
        end: {
          line: 508,
          column: 10
        }
      },
      "238": {
        start: {
          line: 514,
          column: 8
        },
        end: {
          line: 515,
          column: 24
        }
      },
      "239": {
        start: {
          line: 515,
          column: 12
        },
        end: {
          line: 515,
          column: 24
        }
      },
      "240": {
        start: {
          line: 517,
          column: 8
        },
        end: {
          line: 519,
          column: 9
        }
      },
      "241": {
        start: {
          line: 518,
          column: 12
        },
        end: {
          line: 518,
          column: 24
        }
      },
      "242": {
        start: {
          line: 521,
          column: 8
        },
        end: {
          line: 527,
          column: 9
        }
      },
      "243": {
        start: {
          line: 522,
          column: 34
        },
        end: {
          line: 522,
          column: 57
        }
      },
      "244": {
        start: {
          line: 523,
          column: 39
        },
        end: {
          line: 523,
          column: 121
        }
      },
      "245": {
        start: {
          line: 524,
          column: 12
        },
        end: {
          line: 526,
          column: 13
        }
      },
      "246": {
        start: {
          line: 525,
          column: 16
        },
        end: {
          line: 525,
          column: 28
        }
      },
      "247": {
        start: {
          line: 528,
          column: 8
        },
        end: {
          line: 528,
          column: 21
        }
      },
      "248": {
        start: {
          line: 534,
          column: 8
        },
        end: {
          line: 535,
          column: 19
        }
      },
      "249": {
        start: {
          line: 535,
          column: 12
        },
        end: {
          line: 535,
          column: 19
        }
      },
      "250": {
        start: {
          line: 536,
          column: 34
        },
        end: {
          line: 536,
          column: 103
        }
      },
      "251": {
        start: {
          line: 536,
          column: 72
        },
        end: {
          line: 536,
          column: 102
        }
      },
      "252": {
        start: {
          line: 537,
          column: 26
        },
        end: {
          line: 537,
          column: 63
        }
      },
      "253": {
        start: {
          line: 537,
          column: 53
        },
        end: {
          line: 537,
          column: 62
        }
      },
      "254": {
        start: {
          line: 538,
          column: 8
        },
        end: {
          line: 540,
          column: 9
        }
      },
      "255": {
        start: {
          line: 539,
          column: 12
        },
        end: {
          line: 539,
          column: 45
        }
      },
      "256": {
        start: {
          line: 541,
          column: 24
        },
        end: {
          line: 549,
          column: 9
        }
      },
      "257": {
        start: {
          line: 547,
          column: 72
        },
        end: {
          line: 547,
          column: 102
        }
      },
      "258": {
        start: {
          line: 550,
          column: 8
        },
        end: {
          line: 550,
          column: 35
        }
      },
      "259": {
        start: {
          line: 556,
          column: 30
        },
        end: {
          line: 556,
          column: 59
        }
      },
      "260": {
        start: {
          line: 557,
          column: 27
        },
        end: {
          line: 571,
          column: 9
        }
      },
      "261": {
        start: {
          line: 562,
          column: 54
        },
        end: {
          line: 562,
          column: 67
        }
      },
      "262": {
        start: {
          line: 563,
          column: 57
        },
        end: {
          line: 563,
          column: 73
        }
      },
      "263": {
        start: {
          line: 564,
          column: 52
        },
        end: {
          line: 564,
          column: 63
        }
      },
      "264": {
        start: {
          line: 565,
          column: 62
        },
        end: {
          line: 565,
          column: 84
        }
      },
      "265": {
        start: {
          line: 572,
          column: 36
        },
        end: {
          line: 577,
          column: 9
        }
      },
      "266": {
        start: {
          line: 578,
          column: 8
        },
        end: {
          line: 588,
          column: 10
        }
      },
      "267": {
        start: {
          line: 592,
          column: 8
        },
        end: {
          line: 592,
          column: 87
        }
      },
      "268": {
        start: {
          line: 595,
          column: 20
        },
        end: {
          line: 595,
          column: 24
        }
      },
      "269": {
        start: {
          line: 596,
          column: 8
        },
        end: {
          line: 599,
          column: 10
        }
      },
      "270": {
        start: {
          line: 597,
          column: 12
        },
        end: {
          line: 597,
          column: 52
        }
      },
      "271": {
        start: {
          line: 598,
          column: 12
        },
        end: {
          line: 598,
          column: 34
        }
      },
      "272": {
        start: {
          line: 602,
          column: 0
        },
        end: {
          line: 602,
          column: 62
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 22,
            column: 4
          },
          end: {
            line: 22,
            column: 5
          }
        },
        loc: {
          start: {
            line: 22,
            column: 28
          },
          end: {
            line: 54,
            column: 5
          }
        },
        line: 22
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 58,
            column: 4
          },
          end: {
            line: 58,
            column: 5
          }
        },
        loc: {
          start: {
            line: 58,
            column: 68
          },
          end: {
            line: 83,
            column: 5
          }
        },
        line: 58
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 87,
            column: 4
          },
          end: {
            line: 87,
            column: 5
          }
        },
        loc: {
          start: {
            line: 87,
            column: 33
          },
          end: {
            line: 94,
            column: 5
          }
        },
        line: 87
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 98,
            column: 4
          },
          end: {
            line: 98,
            column: 5
          }
        },
        loc: {
          start: {
            line: 98,
            column: 78
          },
          end: {
            line: 119,
            column: 5
          }
        },
        line: 98
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 123,
            column: 4
          },
          end: {
            line: 123,
            column: 5
          }
        },
        loc: {
          start: {
            line: 123,
            column: 34
          },
          end: {
            line: 157,
            column: 5
          }
        },
        line: 123
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 161,
            column: 4
          },
          end: {
            line: 161,
            column: 5
          }
        },
        loc: {
          start: {
            line: 161,
            column: 86
          },
          end: {
            line: 196,
            column: 5
          }
        },
        line: 161
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 177,
            column: 28
          },
          end: {
            line: 177,
            column: 29
          }
        },
        loc: {
          start: {
            line: 177,
            column: 33
          },
          end: {
            line: 177,
            column: 38
          }
        },
        line: 177
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 178,
            column: 28
          },
          end: {
            line: 178,
            column: 29
          }
        },
        loc: {
          start: {
            line: 178,
            column: 40
          },
          end: {
            line: 178,
            column: 47
          }
        },
        line: 178
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 200,
            column: 4
          },
          end: {
            line: 200,
            column: 5
          }
        },
        loc: {
          start: {
            line: 200,
            column: 53
          },
          end: {
            line: 205,
            column: 5
          }
        },
        line: 200
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 201,
            column: 37
          },
          end: {
            line: 201,
            column: 38
          }
        },
        loc: {
          start: {
            line: 201,
            column: 59
          },
          end: {
            line: 204,
            column: 9
          }
        },
        line: 201
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 209,
            column: 4
          },
          end: {
            line: 209,
            column: 5
          }
        },
        loc: {
          start: {
            line: 209,
            column: 71
          },
          end: {
            line: 233,
            column: 5
          }
        },
        line: 209
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 237,
            column: 4
          },
          end: {
            line: 237,
            column: 5
          }
        },
        loc: {
          start: {
            line: 237,
            column: 46
          },
          end: {
            line: 259,
            column: 5
          }
        },
        line: 237
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 263,
            column: 4
          },
          end: {
            line: 263,
            column: 5
          }
        },
        loc: {
          start: {
            line: 263,
            column: 46
          },
          end: {
            line: 282,
            column: 5
          }
        },
        line: 263
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 286,
            column: 4
          },
          end: {
            line: 286,
            column: 5
          }
        },
        loc: {
          start: {
            line: 286,
            column: 56
          },
          end: {
            line: 312,
            column: 5
          }
        },
        line: 286
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 316,
            column: 4
          },
          end: {
            line: 316,
            column: 5
          }
        },
        loc: {
          start: {
            line: 316,
            column: 30
          },
          end: {
            line: 323,
            column: 5
          }
        },
        line: 316
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 317,
            column: 38
          },
          end: {
            line: 317,
            column: 39
          }
        },
        loc: {
          start: {
            line: 317,
            column: 57
          },
          end: {
            line: 317,
            column: 104
          }
        },
        line: 317
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 327,
            column: 4
          },
          end: {
            line: 327,
            column: 5
          }
        },
        loc: {
          start: {
            line: 327,
            column: 35
          },
          end: {
            line: 347,
            column: 5
          }
        },
        line: 327
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 338,
            column: 68
          },
          end: {
            line: 338,
            column: 69
          }
        },
        loc: {
          start: {
            line: 338,
            column: 73
          },
          end: {
            line: 338,
            column: 92
          }
        },
        line: 338
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 351,
            column: 4
          },
          end: {
            line: 351,
            column: 5
          }
        },
        loc: {
          start: {
            line: 351,
            column: 20
          },
          end: {
            line: 401,
            column: 5
          }
        },
        line: 351
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 405,
            column: 4
          },
          end: {
            line: 405,
            column: 5
          }
        },
        loc: {
          start: {
            line: 405,
            column: 30
          },
          end: {
            line: 413,
            column: 5
          }
        },
        line: 405
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 417,
            column: 4
          },
          end: {
            line: 417,
            column: 5
          }
        },
        loc: {
          start: {
            line: 417,
            column: 22
          },
          end: {
            line: 431,
            column: 5
          }
        },
        line: 417
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 435,
            column: 4
          },
          end: {
            line: 435,
            column: 5
          }
        },
        loc: {
          start: {
            line: 435,
            column: 31
          },
          end: {
            line: 446,
            column: 5
          }
        },
        line: 435
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 450,
            column: 4
          },
          end: {
            line: 450,
            column: 5
          }
        },
        loc: {
          start: {
            line: 450,
            column: 39
          },
          end: {
            line: 455,
            column: 5
          }
        },
        line: 450
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 451,
            column: 45
          },
          end: {
            line: 451,
            column: 46
          }
        },
        loc: {
          start: {
            line: 451,
            column: 50
          },
          end: {
            line: 451,
            column: 59
          }
        },
        line: 451
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 451,
            column: 68
          },
          end: {
            line: 451,
            column: 69
          }
        },
        loc: {
          start: {
            line: 451,
            column: 73
          },
          end: {
            line: 451,
            column: 95
          }
        },
        line: 451
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 453,
            column: 34
          },
          end: {
            line: 453,
            column: 35
          }
        },
        loc: {
          start: {
            line: 453,
            column: 46
          },
          end: {
            line: 453,
            column: 53
          }
        },
        line: 453
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 459,
            column: 4
          },
          end: {
            line: 459,
            column: 5
          }
        },
        loc: {
          start: {
            line: 459,
            column: 39
          },
          end: {
            line: 472,
            column: 5
          }
        },
        line: 459
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 476,
            column: 4
          },
          end: {
            line: 476,
            column: 5
          }
        },
        loc: {
          start: {
            line: 476,
            column: 44
          },
          end: {
            line: 485,
            column: 5
          }
        },
        line: 476
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 489,
            column: 4
          },
          end: {
            line: 489,
            column: 5
          }
        },
        loc: {
          start: {
            line: 489,
            column: 32
          },
          end: {
            line: 509,
            column: 5
          }
        },
        line: 489
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 492,
            column: 60
          },
          end: {
            line: 492,
            column: 61
          }
        },
        loc: {
          start: {
            line: 492,
            column: 65
          },
          end: {
            line: 492,
            column: 111
          }
        },
        line: 492
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 496,
            column: 34
          },
          end: {
            line: 496,
            column: 35
          }
        },
        loc: {
          start: {
            line: 496,
            column: 55
          },
          end: {
            line: 498,
            column: 9
          }
        },
        line: 496
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 513,
            column: 4
          },
          end: {
            line: 513,
            column: 5
          }
        },
        loc: {
          start: {
            line: 513,
            column: 29
          },
          end: {
            line: 529,
            column: 5
          }
        },
        line: 513
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 533,
            column: 4
          },
          end: {
            line: 533,
            column: 5
          }
        },
        loc: {
          start: {
            line: 533,
            column: 20
          },
          end: {
            line: 551,
            column: 5
          }
        },
        line: 533
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 536,
            column: 67
          },
          end: {
            line: 536,
            column: 68
          }
        },
        loc: {
          start: {
            line: 536,
            column: 72
          },
          end: {
            line: 536,
            column: 102
          }
        },
        line: 536
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 537,
            column: 48
          },
          end: {
            line: 537,
            column: 49
          }
        },
        loc: {
          start: {
            line: 537,
            column: 53
          },
          end: {
            line: 537,
            column: 62
          }
        },
        line: 537
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 547,
            column: 67
          },
          end: {
            line: 547,
            column: 68
          }
        },
        loc: {
          start: {
            line: 547,
            column: 72
          },
          end: {
            line: 547,
            column: 102
          }
        },
        line: 547
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 555,
            column: 4
          },
          end: {
            line: 555,
            column: 5
          }
        },
        loc: {
          start: {
            line: 555,
            column: 49
          },
          end: {
            line: 589,
            column: 5
          }
        },
        line: 555
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 562,
            column: 49
          },
          end: {
            line: 562,
            column: 50
          }
        },
        loc: {
          start: {
            line: 562,
            column: 54
          },
          end: {
            line: 562,
            column: 67
          }
        },
        line: 562
      },
      "38": {
        name: "(anonymous_38)",
        decl: {
          start: {
            line: 563,
            column: 52
          },
          end: {
            line: 563,
            column: 53
          }
        },
        loc: {
          start: {
            line: 563,
            column: 57
          },
          end: {
            line: 563,
            column: 73
          }
        },
        line: 563
      },
      "39": {
        name: "(anonymous_39)",
        decl: {
          start: {
            line: 564,
            column: 47
          },
          end: {
            line: 564,
            column: 48
          }
        },
        loc: {
          start: {
            line: 564,
            column: 52
          },
          end: {
            line: 564,
            column: 63
          }
        },
        line: 564
      },
      "40": {
        name: "(anonymous_40)",
        decl: {
          start: {
            line: 565,
            column: 57
          },
          end: {
            line: 565,
            column: 58
          }
        },
        loc: {
          start: {
            line: 565,
            column: 62
          },
          end: {
            line: 565,
            column: 84
          }
        },
        line: 565
      },
      "41": {
        name: "(anonymous_41)",
        decl: {
          start: {
            line: 591,
            column: 4
          },
          end: {
            line: 591,
            column: 5
          }
        },
        loc: {
          start: {
            line: 591,
            column: 25
          },
          end: {
            line: 593,
            column: 5
          }
        },
        line: 591
      },
      "42": {
        name: "(anonymous_42)",
        decl: {
          start: {
            line: 594,
            column: 4
          },
          end: {
            line: 594,
            column: 5
          }
        },
        loc: {
          start: {
            line: 594,
            column: 29
          },
          end: {
            line: 600,
            column: 5
          }
        },
        line: 594
      },
      "43": {
        name: "(anonymous_43)",
        decl: {
          start: {
            line: 596,
            column: 15
          },
          end: {
            line: 596,
            column: 16
          }
        },
        loc: {
          start: {
            line: 596,
            column: 21
          },
          end: {
            line: 599,
            column: 9
          }
        },
        line: 596
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 48,
            column: 8
          },
          end: {
            line: 53,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 48,
            column: 8
          },
          end: {
            line: 53,
            column: 9
          }
        }, {
          start: {
            line: 51,
            column: 13
          },
          end: {
            line: 53,
            column: 9
          }
        }],
        line: 48
      },
      "1": {
        loc: {
          start: {
            line: 69,
            column: 16
          },
          end: {
            line: 71,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 69,
            column: 16
          },
          end: {
            line: 71,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 69
      },
      "2": {
        loc: {
          start: {
            line: 72,
            column: 16
          },
          end: {
            line: 74,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 72,
            column: 16
          },
          end: {
            line: 74,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 72
      },
      "3": {
        loc: {
          start: {
            line: 127,
            column: 12
          },
          end: {
            line: 141,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 127,
            column: 12
          },
          end: {
            line: 141,
            column: 13
          }
        }, {
          start: {
            line: 133,
            column: 17
          },
          end: {
            line: 141,
            column: 13
          }
        }],
        line: 127
      },
      "4": {
        loc: {
          start: {
            line: 127,
            column: 16
          },
          end: {
            line: 127,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 127,
            column: 16
          },
          end: {
            line: 127,
            column: 39
          }
        }, {
          start: {
            line: 127,
            column: 43
          },
          end: {
            line: 127,
            column: 77
          }
        }],
        line: 127
      },
      "5": {
        loc: {
          start: {
            line: 135,
            column: 28
          },
          end: {
            line: 135,
            column: 101
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 135,
            column: 74
          },
          end: {
            line: 135,
            column: 97
          }
        }, {
          start: {
            line: 135,
            column: 100
          },
          end: {
            line: 135,
            column: 101
          }
        }],
        line: 135
      },
      "6": {
        loc: {
          start: {
            line: 136,
            column: 28
          },
          end: {
            line: 136,
            column: 101
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 136,
            column: 74
          },
          end: {
            line: 136,
            column: 97
          }
        }, {
          start: {
            line: 136,
            column: 100
          },
          end: {
            line: 136,
            column: 101
          }
        }],
        line: 136
      },
      "7": {
        loc: {
          start: {
            line: 175,
            column: 12
          },
          end: {
            line: 180,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 175,
            column: 12
          },
          end: {
            line: 180,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 175
      },
      "8": {
        loc: {
          start: {
            line: 182,
            column: 12
          },
          end: {
            line: 189,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 182,
            column: 12
          },
          end: {
            line: 189,
            column: 13
          }
        }, {
          start: {
            line: 187,
            column: 17
          },
          end: {
            line: 189,
            column: 13
          }
        }],
        line: 182
      },
      "9": {
        loc: {
          start: {
            line: 210,
            column: 8
          },
          end: {
            line: 211,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 210,
            column: 8
          },
          end: {
            line: 211,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 210
      },
      "10": {
        loc: {
          start: {
            line: 225,
            column: 8
          },
          end: {
            line: 228,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 225,
            column: 8
          },
          end: {
            line: 228,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 225
      },
      "11": {
        loc: {
          start: {
            line: 243,
            column: 12
          },
          end: {
            line: 257,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 243,
            column: 12
          },
          end: {
            line: 257,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 243
      },
      "12": {
        loc: {
          start: {
            line: 253,
            column: 28
          },
          end: {
            line: 253,
            column: 101
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 253,
            column: 74
          },
          end: {
            line: 253,
            column: 97
          }
        }, {
          start: {
            line: 253,
            column: 100
          },
          end: {
            line: 253,
            column: 101
          }
        }],
        line: 253
      },
      "13": {
        loc: {
          start: {
            line: 254,
            column: 28
          },
          end: {
            line: 254,
            column: 101
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 254,
            column: 74
          },
          end: {
            line: 254,
            column: 97
          }
        }, {
          start: {
            line: 254,
            column: 100
          },
          end: {
            line: 254,
            column: 101
          }
        }],
        line: 254
      },
      "14": {
        loc: {
          start: {
            line: 266,
            column: 12
          },
          end: {
            line: 280,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 266,
            column: 12
          },
          end: {
            line: 280,
            column: 13
          }
        }, {
          start: {
            line: 273,
            column: 17
          },
          end: {
            line: 280,
            column: 13
          }
        }],
        line: 266
      },
      "15": {
        loc: {
          start: {
            line: 266,
            column: 16
          },
          end: {
            line: 266,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 266,
            column: 16
          },
          end: {
            line: 266,
            column: 39
          }
        }, {
          start: {
            line: 266,
            column: 43
          },
          end: {
            line: 266,
            column: 77
          }
        }],
        line: 266
      },
      "16": {
        loc: {
          start: {
            line: 268,
            column: 16
          },
          end: {
            line: 271,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 268,
            column: 16
          },
          end: {
            line: 271,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 268
      },
      "17": {
        loc: {
          start: {
            line: 273,
            column: 17
          },
          end: {
            line: 280,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 273,
            column: 17
          },
          end: {
            line: 280,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 273
      },
      "18": {
        loc: {
          start: {
            line: 277,
            column: 28
          },
          end: {
            line: 277,
            column: 101
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 277,
            column: 74
          },
          end: {
            line: 277,
            column: 97
          }
        }, {
          start: {
            line: 277,
            column: 100
          },
          end: {
            line: 277,
            column: 101
          }
        }],
        line: 277
      },
      "19": {
        loc: {
          start: {
            line: 278,
            column: 28
          },
          end: {
            line: 278,
            column: 101
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 278,
            column: 74
          },
          end: {
            line: 278,
            column: 97
          }
        }, {
          start: {
            line: 278,
            column: 100
          },
          end: {
            line: 278,
            column: 101
          }
        }],
        line: 278
      },
      "20": {
        loc: {
          start: {
            line: 287,
            column: 8
          },
          end: {
            line: 311,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 288,
            column: 12
          },
          end: {
            line: 295,
            column: 32
          }
        }, {
          start: {
            line: 296,
            column: 12
          },
          end: {
            line: 297,
            column: 62
          }
        }, {
          start: {
            line: 298,
            column: 12
          },
          end: {
            line: 303,
            column: 32
          }
        }, {
          start: {
            line: 304,
            column: 12
          },
          end: {
            line: 308,
            column: 32
          }
        }, {
          start: {
            line: 309,
            column: 12
          },
          end: {
            line: 310,
            column: 62
          }
        }],
        line: 287
      },
      "21": {
        loc: {
          start: {
            line: 289,
            column: 16
          },
          end: {
            line: 294,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 289,
            column: 16
          },
          end: {
            line: 294,
            column: 17
          }
        }, {
          start: {
            line: 292,
            column: 21
          },
          end: {
            line: 294,
            column: 17
          }
        }],
        line: 289
      },
      "22": {
        loc: {
          start: {
            line: 292,
            column: 21
          },
          end: {
            line: 294,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 292,
            column: 21
          },
          end: {
            line: 294,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 292
      },
      "23": {
        loc: {
          start: {
            line: 300,
            column: 16
          },
          end: {
            line: 302,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 300,
            column: 16
          },
          end: {
            line: 302,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 300
      },
      "24": {
        loc: {
          start: {
            line: 300,
            column: 20
          },
          end: {
            line: 300,
            column: 52
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 300,
            column: 20
          },
          end: {
            line: 300,
            column: 34
          }
        }, {
          start: {
            line: 300,
            column: 38
          },
          end: {
            line: 300,
            column: 52
          }
        }],
        line: 300
      },
      "25": {
        loc: {
          start: {
            line: 305,
            column: 16
          },
          end: {
            line: 307,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 305,
            column: 16
          },
          end: {
            line: 307,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 305
      },
      "26": {
        loc: {
          start: {
            line: 305,
            column: 20
          },
          end: {
            line: 305,
            column: 52
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 305,
            column: 20
          },
          end: {
            line: 305,
            column: 34
          }
        }, {
          start: {
            line: 305,
            column: 38
          },
          end: {
            line: 305,
            column: 52
          }
        }],
        line: 305
      },
      "27": {
        loc: {
          start: {
            line: 317,
            column: 57
          },
          end: {
            line: 317,
            column: 104
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 317,
            column: 90
          },
          end: {
            line: 317,
            column: 97
          }
        }, {
          start: {
            line: 317,
            column: 100
          },
          end: {
            line: 317,
            column: 104
          }
        }],
        line: 317
      },
      "28": {
        loc: {
          start: {
            line: 328,
            column: 8
          },
          end: {
            line: 329,
            column: 78
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 328,
            column: 8
          },
          end: {
            line: 329,
            column: 78
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 328
      },
      "29": {
        loc: {
          start: {
            line: 330,
            column: 8
          },
          end: {
            line: 346,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 331,
            column: 12
          },
          end: {
            line: 332,
            column: 50
          }
        }, {
          start: {
            line: 333,
            column: 12
          },
          end: {
            line: 333,
            column: 25
          }
        }, {
          start: {
            line: 334,
            column: 12
          },
          end: {
            line: 343,
            column: 28
          }
        }, {
          start: {
            line: 344,
            column: 12
          },
          end: {
            line: 345,
            column: 50
          }
        }],
        line: 330
      },
      "30": {
        loc: {
          start: {
            line: 339,
            column: 20
          },
          end: {
            line: 341,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 339,
            column: 20
          },
          end: {
            line: 341,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 339
      },
      "31": {
        loc: {
          start: {
            line: 339,
            column: 24
          },
          end: {
            line: 339,
            column: 80
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 339,
            column: 24
          },
          end: {
            line: 339,
            column: 32
          }
        }, {
          start: {
            line: 339,
            column: 36
          },
          end: {
            line: 339,
            column: 80
          }
        }],
        line: 339
      },
      "32": {
        loc: {
          start: {
            line: 352,
            column: 8
          },
          end: {
            line: 353,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 352,
            column: 8
          },
          end: {
            line: 353,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 352
      },
      "33": {
        loc: {
          start: {
            line: 355,
            column: 8
          },
          end: {
            line: 400,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 356,
            column: 12
          },
          end: {
            line: 362,
            column: 22
          }
        }, {
          start: {
            line: 363,
            column: 12
          },
          end: {
            line: 374,
            column: 22
          }
        }, {
          start: {
            line: 375,
            column: 12
          },
          end: {
            line: 383,
            column: 22
          }
        }, {
          start: {
            line: 384,
            column: 12
          },
          end: {
            line: 396,
            column: 22
          }
        }, {
          start: {
            line: 397,
            column: 12
          },
          end: {
            line: 399,
            column: 22
          }
        }],
        line: 355
      },
      "34": {
        loc: {
          start: {
            line: 366,
            column: 36
          },
          end: {
            line: 366,
            column: 96
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 366,
            column: 36
          },
          end: {
            line: 366,
            column: 72
          }
        }, {
          start: {
            line: 366,
            column: 76
          },
          end: {
            line: 366,
            column: 96
          }
        }],
        line: 366
      },
      "35": {
        loc: {
          start: {
            line: 368,
            column: 24
          },
          end: {
            line: 370,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 368,
            column: 24
          },
          end: {
            line: 370,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 368
      },
      "36": {
        loc: {
          start: {
            line: 379,
            column: 20
          },
          end: {
            line: 381,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 379,
            column: 20
          },
          end: {
            line: 381,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 379
      },
      "37": {
        loc: {
          start: {
            line: 389,
            column: 24
          },
          end: {
            line: 392,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 389,
            column: 24
          },
          end: {
            line: 392,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 389
      },
      "38": {
        loc: {
          start: {
            line: 389,
            column: 28
          },
          end: {
            line: 390,
            column: 74
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 389,
            column: 28
          },
          end: {
            line: 389,
            column: 69
          }
        }, {
          start: {
            line: 390,
            column: 28
          },
          end: {
            line: 390,
            column: 74
          }
        }],
        line: 389
      },
      "39": {
        loc: {
          start: {
            line: 406,
            column: 8
          },
          end: {
            line: 408,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 406,
            column: 8
          },
          end: {
            line: 408,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 406
      },
      "40": {
        loc: {
          start: {
            line: 406,
            column: 12
          },
          end: {
            line: 406,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 406,
            column: 12
          },
          end: {
            line: 406,
            column: 47
          }
        }, {
          start: {
            line: 406,
            column: 51
          },
          end: {
            line: 406,
            column: 67
          }
        }],
        line: 406
      },
      "41": {
        loc: {
          start: {
            line: 418,
            column: 8
          },
          end: {
            line: 419,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 418,
            column: 8
          },
          end: {
            line: 419,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 418
      },
      "42": {
        loc: {
          start: {
            line: 421,
            column: 8
          },
          end: {
            line: 430,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 421,
            column: 8
          },
          end: {
            line: 430,
            column: 9
          }
        }, {
          start: {
            line: 426,
            column: 13
          },
          end: {
            line: 430,
            column: 9
          }
        }],
        line: 421
      },
      "43": {
        loc: {
          start: {
            line: 426,
            column: 13
          },
          end: {
            line: 430,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 426,
            column: 13
          },
          end: {
            line: 430,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 426
      },
      "44": {
        loc: {
          start: {
            line: 436,
            column: 8
          },
          end: {
            line: 437,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 436,
            column: 8
          },
          end: {
            line: 437,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 436
      },
      "45": {
        loc: {
          start: {
            line: 440,
            column: 12
          },
          end: {
            line: 444,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 440,
            column: 12
          },
          end: {
            line: 444,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 440
      },
      "46": {
        loc: {
          start: {
            line: 452,
            column: 15
          },
          end: {
            line: 454,
            column: 28
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 453,
            column: 12
          },
          end: {
            line: 453,
            column: 81
          }
        }, {
          start: {
            line: 454,
            column: 12
          },
          end: {
            line: 454,
            column: 28
          }
        }],
        line: 452
      },
      "47": {
        loc: {
          start: {
            line: 460,
            column: 8
          },
          end: {
            line: 461,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 460,
            column: 8
          },
          end: {
            line: 461,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 460
      },
      "48": {
        loc: {
          start: {
            line: 471,
            column: 15
          },
          end: {
            line: 471,
            column: 60
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 471,
            column: 31
          },
          end: {
            line: 471,
            column: 56
          }
        }, {
          start: {
            line: 471,
            column: 59
          },
          end: {
            line: 471,
            column: 60
          }
        }],
        line: 471
      },
      "49": {
        loc: {
          start: {
            line: 479,
            column: 12
          },
          end: {
            line: 482,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 479,
            column: 12
          },
          end: {
            line: 482,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 479
      },
      "50": {
        loc: {
          start: {
            line: 479,
            column: 16
          },
          end: {
            line: 479,
            column: 102
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 479,
            column: 16
          },
          end: {
            line: 479,
            column: 57
          }
        }, {
          start: {
            line: 479,
            column: 61
          },
          end: {
            line: 479,
            column: 102
          }
        }],
        line: 479
      },
      "51": {
        loc: {
          start: {
            line: 490,
            column: 8
          },
          end: {
            line: 491,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 490,
            column: 8
          },
          end: {
            line: 491,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 490
      },
      "52": {
        loc: {
          start: {
            line: 493,
            column: 8
          },
          end: {
            line: 494,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 493,
            column: 8
          },
          end: {
            line: 494,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 493
      },
      "53": {
        loc: {
          start: {
            line: 514,
            column: 8
          },
          end: {
            line: 515,
            column: 24
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 514,
            column: 8
          },
          end: {
            line: 515,
            column: 24
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 514
      },
      "54": {
        loc: {
          start: {
            line: 517,
            column: 8
          },
          end: {
            line: 519,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 517,
            column: 8
          },
          end: {
            line: 519,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 517
      },
      "55": {
        loc: {
          start: {
            line: 521,
            column: 8
          },
          end: {
            line: 527,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 521,
            column: 8
          },
          end: {
            line: 527,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 521
      },
      "56": {
        loc: {
          start: {
            line: 524,
            column: 12
          },
          end: {
            line: 526,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 524,
            column: 12
          },
          end: {
            line: 526,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 524
      },
      "57": {
        loc: {
          start: {
            line: 534,
            column: 8
          },
          end: {
            line: 535,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 534,
            column: 8
          },
          end: {
            line: 535,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 534
      },
      "58": {
        loc: {
          start: {
            line: 538,
            column: 8
          },
          end: {
            line: 540,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 538,
            column: 8
          },
          end: {
            line: 540,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 538
      },
      "59": {
        loc: {
          start: {
            line: 558,
            column: 29
          },
          end: {
            line: 558,
            column: 60
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 558,
            column: 29
          },
          end: {
            line: 558,
            column: 55
          }
        }, {
          start: {
            line: 558,
            column: 59
          },
          end: {
            line: 558,
            column: 60
          }
        }],
        line: 558
      },
      "60": {
        loc: {
          start: {
            line: 560,
            column: 34
          },
          end: {
            line: 560,
            column: 65
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 560,
            column: 34
          },
          end: {
            line: 560,
            column: 60
          }
        }, {
          start: {
            line: 560,
            column: 64
          },
          end: {
            line: 560,
            column: 65
          }
        }],
        line: 560
      },
      "61": {
        loc: {
          start: {
            line: 569,
            column: 37
          },
          end: {
            line: 569,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 569,
            column: 37
          },
          end: {
            line: 569,
            column: 68
          }
        }, {
          start: {
            line: 569,
            column: 72
          },
          end: {
            line: 569,
            column: 73
          }
        }],
        line: 569
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0,
      "210": 0,
      "211": 0,
      "212": 0,
      "213": 0,
      "214": 0,
      "215": 0,
      "216": 0,
      "217": 0,
      "218": 0,
      "219": 0,
      "220": 0,
      "221": 0,
      "222": 0,
      "223": 0,
      "224": 0,
      "225": 0,
      "226": 0,
      "227": 0,
      "228": 0,
      "229": 0,
      "230": 0,
      "231": 0,
      "232": 0,
      "233": 0,
      "234": 0,
      "235": 0,
      "236": 0,
      "237": 0,
      "238": 0,
      "239": 0,
      "240": 0,
      "241": 0,
      "242": 0,
      "243": 0,
      "244": 0,
      "245": 0,
      "246": 0,
      "247": 0,
      "248": 0,
      "249": 0,
      "250": 0,
      "251": 0,
      "252": 0,
      "253": 0,
      "254": 0,
      "255": 0,
      "256": 0,
      "257": 0,
      "258": 0,
      "259": 0,
      "260": 0,
      "261": 0,
      "262": 0,
      "263": 0,
      "264": 0,
      "265": 0,
      "266": 0,
      "267": 0,
      "268": 0,
      "269": 0,
      "270": 0,
      "271": 0,
      "272": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0, 0, 0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0, 0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0, 0, 0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0],
      "61": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\algorithms\\ParticleSwarmOptimization.ts",
      mappings: ";AAAA;;;;;;;;;;;;GAYG;;;AAEH,8EAa0C;AAiD1C;;GAEG;AACH,MAAa,yBAAyB;IASpC,YAAY,UAA6C;QAPjD,eAAU,GAAsB,IAAI,CAAC;QACrC,iBAAY,GAAgC,IAAI,CAAC;QACjD,YAAO,GAAuB,EAAE,CAAC;QACjC,sBAAiB,GAAyB,EAAE,CAAC;QAE7C,oBAAe,GAAW,CAAC,CAAC;QAGlC,IAAI,CAAC,UAAU,GAAG;YAChB,SAAS,EAAE,EAAE;YACb,aAAa,EAAE,GAAG;YAClB,aAAa,EAAE,GAAG;YAClB,gBAAgB,EAAE,GAAG;YACrB,gBAAgB,EAAE,GAAG;YACrB,wBAAwB,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;YACpC,WAAW,EAAE,GAAG;YAChB,sBAAsB,EAAE,GAAG;YAC3B,QAAQ,EAAE,QAAQ;YAClB,gBAAgB,EAAE,CAAC;YACnB,gBAAgB,EAAE,SAAS;YAC3B,kBAAkB,EAAE,SAAS;YAC7B,kBAAkB,EAAE,IAAI;YACxB,kBAAkB,EAAE,IAAI;YACxB,oBAAoB,EAAE,IAAI;YAC1B,SAAS,EAAE,CAAC;YACZ,GAAG,UAAU;SACd,CAAC;QAEF,qCAAqC;QACrC,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;YAC5C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;QACnE,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC5B,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,QAAQ,CACnB,OAA4B,EAC5B,iBAAwC,EACxC,mBAA6C;QAE7C,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QAEpC,IAAI,CAAC;YACH,uBAAuB;YACvB,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;YAElC,uBAAuB;YACvB,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,iBAAiB,EAAE,mBAAmB,CAAC,CAAC;YAE/E,yBAAyB;YACzB,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,CAAC;gBACtC,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,iBAAiB,EAAE,mBAAmB,CAAC,CAAC;gBACxE,IAAI,CAAC,aAAa,EAAE,CAAC;gBAErB,IAAI,IAAI,CAAC,UAAU,CAAC,kBAAkB,EAAE,CAAC;oBACvC,IAAI,CAAC,eAAe,EAAE,CAAC;gBACzB,CAAC;gBAED,IAAI,IAAI,CAAC,UAAU,CAAC,oBAAoB,EAAE,CAAC;oBACzC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;gBAClC,CAAC;YACH,CAAC;YAED,sBAAsB;YACtB,OAAO,IAAI,CAAC,wBAAwB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QAE3D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;YAC5D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,OAA4B;QACtD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;QAClB,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;QAC5B,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;QAEzB,OAAO,CAAC,GAAG,CAAC,6DAA6D,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC,CAAC;IACxG,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAC9B,OAA4B,EAC5B,iBAAwC,EACxC,mBAA6C;QAE7C,MAAM,SAAS,GAAe,EAAE,CAAC;QAEjC,mBAAmB;QACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC;YACnD,MAAM,QAAQ,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;YACpD,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,OAAO,EAAE,iBAAiB,EAAE,mBAAmB,CAAC,CAAC;YACvF,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC3B,CAAC;QAED,yBAAyB;QACzB,IAAI,CAAC,UAAU,GAAG;YAChB,SAAS;YACT,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC;YAC1C,SAAS,EAAE,CAAC;YACZ,cAAc,EAAE,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC;YACvD,cAAc,EAAE,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC;YACvD,eAAe,EAAE,CAAC;SACnB,CAAC;QAEF,+BAA+B;QAC/B,IAAI,CAAC,aAAa,EAAE,CAAC;QAErB,iDAAiD;QACjD,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;IACnC,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,OAA4B;QACvD,MAAM,QAAQ,GAAwB,EAAE,CAAC;QACzC,MAAM,QAAQ,GAAa,EAAE,CAAC;QAE9B,KAAK,MAAM,QAAQ,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACzC,IAAI,QAAQ,CAAC,cAAc,IAAI,QAAQ,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAClE,oBAAoB;gBACpB,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;gBAC/E,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC,CAAC;gBACpD,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,qCAAqC;YACzD,CAAC;iBAAM,CAAC;gBACN,sBAAsB;gBACtB,MAAM,GAAG,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtF,MAAM,GAAG,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtF,MAAM,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;gBAC9C,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;gBAE9E,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACnB,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACrB,CAAC;QACH,CAAC;QAED,MAAM,QAAQ,GAAa;YACzB,EAAE,EAAE,IAAI,CAAC,kBAAkB,EAAE;YAC7B,QAAQ;YACR,QAAQ;YACR,OAAO,EAAE,MAAM,CAAC,SAAS;YACzB,YAAY,EAAE;gBACZ,QAAQ,EAAE,CAAC,GAAG,QAAQ,CAAC;gBACvB,OAAO,EAAE,MAAM,CAAC,SAAS;aAC1B;YACD,SAAS,EAAE,EAAE;YACb,GAAG,EAAE,CAAC;YACN,eAAe,EAAE,CAAC;SACnB,CAAC;QAEF,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAC5B,QAAkB,EAClB,OAA4B,EAC5B,iBAAwC,EACxC,mBAA6C;QAE7C,IAAI,CAAC;YACH,sDAAsD;YACtD,MAAM,SAAS,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;YAExE,8BAA8B;YAC9B,MAAM,cAAc,GAAG,iBAAiB,CAAC,SAAS,CAAC,CAAC;YACpD,QAAQ,CAAC,OAAO,GAAG,cAAc,CAAC;YAElC,uBAAuB;YACvB,MAAM,oBAAoB,GAAa,EAAE,CAAC;YAC1C,KAAK,MAAM,kBAAkB,IAAI,mBAAmB,EAAE,CAAC;gBACrD,MAAM,SAAS,GAAG,kBAAkB,CAAC,SAAS,CAAC,CAAC;gBAChD,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACvC,CAAC;YAED,4BAA4B;YAC5B,IAAI,IAAI,CAAC,UAAU,CAAC,kBAAkB,KAAK,SAAS,EAAE,CAAC;gBACrD,MAAM,YAAY,GAAG,oBAAoB;qBACtC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;qBAClB,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC;gBACvE,QAAQ,CAAC,OAAO,IAAI,YAAY,CAAC;YACnC,CAAC;YAED,uBAAuB;YACvB,IAAI,QAAQ,CAAC,OAAO,GAAG,QAAQ,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;gBACrD,QAAQ,CAAC,YAAY,CAAC,QAAQ,GAAG,CAAC,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBACxD,QAAQ,CAAC,YAAY,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;gBACjD,QAAQ,CAAC,eAAe,GAAG,CAAC,CAAC;YAC/B,CAAC;iBAAM,CAAC;gBACN,QAAQ,CAAC,eAAe,EAAE,CAAC;YAC7B,CAAC;YAED,IAAI,CAAC,eAAe,EAAE,CAAC;QAEzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACnD,QAAQ,CAAC,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC;QACtC,CAAC;IACH,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,QAAkB,EAAE,iBAAyC;QACvF,OAAO,iBAAiB,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;YACjD,GAAG,QAAQ;YACX,YAAY,EAAE,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC;SACvC,CAAC,CAAC,CAAC;IACN,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,WAAW,CACvB,OAA4B,EAC5B,iBAAwC,EACxC,mBAA6C;QAE7C,IAAI,CAAC,IAAI,CAAC,UAAU;YAAE,OAAO;QAE7B,uBAAuB;QACvB,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;YACjD,kBAAkB;YAClB,IAAI,CAAC,sBAAsB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YAE/C,kBAAkB;YAClB,IAAI,CAAC,sBAAsB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YAE/C,wBAAwB;YACxB,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,OAAO,EAAE,iBAAiB,EAAE,mBAAmB,CAAC,CAAC;YAEvF,aAAa;YACb,QAAQ,CAAC,GAAG,EAAE,CAAC;QACjB,CAAC;QAED,qBAAqB;QACrB,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;QACrE,IAAI,aAAa,CAAC,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;YAC/D,IAAI,CAAC,UAAU,CAAC,UAAU,GAAG,aAAa,CAAC;YAC3C,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;QACnC,CAAC;QAED,0BAA0B;QAC1B,IAAI,CAAC,UAAU,CAAC,cAAc,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;QACzF,IAAI,CAAC,UAAU,CAAC,cAAc,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;QACzF,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;IAC9B,CAAC;IAED;;OAEG;IACK,sBAAsB,CAAC,QAAkB,EAAE,OAA4B;QAC7E,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,wBAAwB,CAAC;QAC1D,MAAM,CAAC,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAEzC,yBAAyB;QACzB,MAAM,gBAAgB,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;QAE7D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAClD,IAAI,OAAO,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE,CAAC;gBAC7C,MAAM,UAAU,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAW,CAAC;gBAClD,MAAM,eAAe,GAAG,QAAQ,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAW,CAAC;gBACpE,MAAM,mBAAmB,GAAG,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAW,CAAC;gBAEnE,+BAA+B;gBAC/B,MAAM,SAAS,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,eAAe,GAAG,UAAU,CAAC,CAAC;gBACtE,MAAM,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,mBAAmB,GAAG,UAAU,CAAC,CAAC;gBAEvE,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,SAAS,GAAG,MAAM,CAAC;gBAErE,oBAAoB;gBACpB,MAAM,QAAQ,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;gBACtC,MAAM,GAAG,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtF,MAAM,GAAG,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtF,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;gBAEzD,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnF,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,sBAAsB,CAAC,QAAkB,EAAE,OAA4B;QAC7E,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAClD,MAAM,QAAQ,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAEtC,IAAI,QAAQ,CAAC,cAAc,IAAI,QAAQ,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAClE,2CAA2C;gBAC3C,IAAI,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,EAAE,CAAC,CAAC,uBAAuB;oBAChD,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;oBAC/E,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;gBAC9D,CAAC;YACH,CAAC;iBAAM,IAAI,OAAO,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE,CAAC;gBACpD,sBAAsB;gBACtB,MAAM,MAAM,GAAI,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAY,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBAEvE,oBAAoB;gBACpB,MAAM,GAAG,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtF,MAAM,GAAG,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;gBAEtF,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACxF,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,QAAgB,EAAE,GAAW,EAAE,GAAW,EAAE,QAAgB,EAAE,KAAa;QAChG,QAAQ,IAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC;YACzC,KAAK,SAAS;gBACZ,IAAI,QAAQ,GAAG,GAAG,EAAE,CAAC;oBACnB,OAAO,GAAG,GAAG,CAAC,GAAG,GAAG,QAAQ,CAAC,CAAC;gBAChC,CAAC;qBAAM,IAAI,QAAQ,GAAG,GAAG,EAAE,CAAC;oBAC1B,OAAO,GAAG,GAAG,CAAC,QAAQ,GAAG,GAAG,CAAC,CAAC;gBAChC,CAAC;gBACD,OAAO,QAAQ,CAAC;YAElB,KAAK,QAAQ;gBACX,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC;YAEhD,KAAK,WAAW;gBACd,8DAA8D;gBAC9D,IAAI,QAAQ,GAAG,GAAG,IAAI,QAAQ,GAAG,GAAG,EAAE,CAAC;oBACrC,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC;gBAChD,CAAC;gBACD,OAAO,QAAQ,CAAC;YAElB,KAAK,QAAQ;gBACX,IAAI,QAAQ,GAAG,GAAG,IAAI,QAAQ,GAAG,GAAG,EAAE,CAAC;oBACrC,OAAO,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;gBAC3C,CAAC;gBACD,OAAO,QAAQ,CAAC;YAElB;gBACE,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC;QAClD,CAAC;IACH,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,SAAqB;QAC1C,MAAM,IAAI,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,OAAO,EAAE,EAAE,CAC9C,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAChD,CAAC;QAEF,OAAO;YACL,QAAQ,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;YAC5B,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,UAAU,EAAE,IAAI,CAAC,EAAE;SACpB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,QAAkB;QAC7C,IAAI,CAAC,IAAI,CAAC,UAAU;YAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,CAAC,OAAO,EAAE,CAAC;QAExF,QAAQ,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;YACjC,KAAK,QAAQ;gBACX,OAAO,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC;YAEpC,KAAK,OAAO,CAAC;YACb,KAAK,MAAM;gBACT,4BAA4B;gBAC5B,IAAI,IAAI,GAAG,QAAQ,CAAC,YAAY,CAAC;gBACjC,KAAK,MAAM,UAAU,IAAI,QAAQ,CAAC,SAAS,EAAE,CAAC;oBAC5C,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,UAAU,CAAC,CAAC;oBAC1E,IAAI,QAAQ,IAAI,QAAQ,CAAC,YAAY,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;wBAC7D,IAAI,GAAG,QAAQ,CAAC,YAAY,CAAC;oBAC/B,CAAC;gBACH,CAAC;gBACD,OAAO,IAAI,CAAC;YAEd;gBACE,OAAO,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC;QACtC,CAAC;IACH,CAAC;IAED;;OAEG;IACK,aAAa;QACnB,IAAI,CAAC,IAAI,CAAC,UAAU;YAAE,OAAO;QAE7B,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC;QAE5C,QAAQ,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;YACjC,KAAK,MAAM;gBACT,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;oBAC1C,MAAM,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC;oBAC3D,MAAM,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC;oBACxC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;gBACpE,CAAC;gBACD,MAAM;YAER,KAAK,OAAO;gBACV,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;oBAC1C,MAAM,SAAS,GAAa,EAAE,CAAC;oBAC/B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,gBAAgB,IAAI,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;wBAClF,MAAM,aAAa,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC;wBACrD,IAAI,aAAa,KAAK,CAAC,EAAE,CAAC;4BACxB,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC;wBAC9C,CAAC;oBACH,CAAC;oBACD,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,SAAS,CAAC;gBACrC,CAAC;gBACD,MAAM;YAER,KAAK,MAAM;gBACT,2CAA2C;gBAC3C,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;gBACpD,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;oBACjC,IAAI,QAAQ,CAAC,EAAE,KAAK,YAAY,CAAC,UAAU,EAAE,CAAC;wBAC5C,QAAQ,CAAC,SAAS,GAAG,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;oBACjD,CAAC;gBACH,CAAC;gBACD,MAAM;YAER,KAAK,QAAQ;gBACX,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;oBACjC,MAAM,SAAS,GAAa,EAAE,CAAC;oBAC/B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC,EAAE,EAAE,CAAC;wBAC1D,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;wBACjE,IAAI,SAAS,CAAC,WAAW,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC,EAAE;4BACzC,CAAC,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;4BACnD,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC;wBAC5C,CAAC;oBACH,CAAC;oBACD,QAAQ,CAAC,SAAS,GAAG,SAAS,CAAC;gBACjC,CAAC;gBACD,MAAM;YAER,SAAS,SAAS;gBAChB,wDAAwD;gBACxD,MAAM;QACV,CAAC;IACH,CAAC;IAED;;OAEG;IACK,uBAAuB;QAC7B,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,kBAAkB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YAC5D,OAAO,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC;QACvC,CAAC;QAED,kCAAkC;QAClC,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC;QAC3E,OAAO,IAAI,CAAC,UAAU,CAAC,gBAAgB;YAChC,QAAQ,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,gBAAgB,GAAG,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC;IAC1F,CAAC;IAED;;OAEG;IACK,eAAe;QACrB,IAAI,CAAC,IAAI,CAAC,UAAU;YAAE,OAAO;QAE7B,2BAA2B;QAC3B,IAAI,IAAI,CAAC,UAAU,CAAC,cAAc,GAAG,GAAG,EAAE,CAAC;YACzC,uCAAuC;YACvC,IAAI,CAAC,UAAU,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,UAAU,CAAC,aAAa,GAAG,GAAG,CAAC,CAAC;YACnF,IAAI,CAAC,UAAU,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,UAAU,CAAC,WAAW,GAAG,GAAG,CAAC,CAAC;QACjF,CAAC;aAAM,IAAI,IAAI,CAAC,UAAU,CAAC,cAAc,GAAG,GAAG,EAAE,CAAC;YAChD,yCAAyC;YACzC,IAAI,CAAC,UAAU,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,UAAU,CAAC,aAAa,GAAG,GAAG,CAAC,CAAC;YACnF,IAAI,CAAC,UAAU,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,WAAW,GAAG,GAAG,CAAC,CAAC;QAClF,CAAC;IACH,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,OAA4B;QACpD,IAAI,CAAC,IAAI,CAAC,UAAU;YAAE,OAAO;QAE7B,6BAA6B;QAC7B,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;YACjD,IAAI,QAAQ,CAAC,eAAe,GAAG,EAAE,EAAE,CAAC;gBAClC,wBAAwB;gBACxB,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;gBACvD,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;YACvC,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,uBAAuB,CAAC,SAAqB;QACnD,MAAM,cAAc,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,CAAC,SAAS,CAAC,CAAC;QACzF,OAAO,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAC3B,cAAc,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC;YACvE,MAAM,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED;;OAEG;IACK,uBAAuB,CAAC,SAAqB;QACnD,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC;YAAE,OAAO,CAAC,CAAC;QAEnC,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,IAAI,SAAS,GAAG,CAAC,CAAC;QAElB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC1C,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC9C,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpE,aAAa,IAAI,QAAQ,CAAC;gBAC1B,SAAS,EAAE,CAAC;YACd,CAAC;QACH,CAAC;QAED,OAAO,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;IACvD,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,SAAmB,EAAE,SAAmB;QAChE,IAAI,QAAQ,GAAG,CAAC,CAAC;QAEjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACnD,IAAI,OAAO,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAI,OAAO,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE,CAAC;gBAC3F,MAAM,IAAI,GAAI,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAY,GAAI,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAY,CAAC;gBACnF,QAAQ,IAAI,IAAI,GAAG,IAAI,CAAC;YAC1B,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,OAA4B;QACrD,IAAI,CAAC,IAAI,CAAC,UAAU;YAAE,OAAO;QAE7B,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,UAAW,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;QAC1G,IAAI,CAAC,YAAY;YAAE,OAAO;QAE1B,MAAM,SAAS,GAA8C,EAAE,CAAC;QAChE,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE;YAC5C,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,GAAG;YAClB,EAAE,EAAE,YAAY,IAAI,CAAC,GAAG,EAAE,EAAE;YAC5B,SAAS;YACT,eAAe,EAAE,EAAE;YACnB,oBAAoB,EAAE,EAAE;YACxB,QAAQ,EAAE,IAAI;YACd,OAAO,EAAE,YAAY,CAAC,OAAO;YAC7B,mBAAmB,EAAE,OAAO,CAAC,mBAAmB;YAChD,kBAAkB,EAAE,EAAgC;SACrD,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,OAA4B;QAClD,IAAI,CAAC,IAAI,CAAC,UAAU;YAAE,OAAO,IAAI,CAAC;QAElC,qBAAqB;QACrB,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC;YAC/D,OAAO,IAAI,CAAC;QACd,CAAC;QAED,oBAAoB;QACpB,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,EAAE,EAAE,CAAC;YAC9B,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;YAC9C,MAAM,kBAAkB,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,aAAa,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,WAAW,CAAC;YAE9G,IAAI,IAAI,CAAC,GAAG,CAAC,kBAAkB,CAAC,GAAG,OAAO,CAAC,mBAAmB,CAAC,cAAc,EAAE,CAAC;gBAC9E,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACK,aAAa;QACnB,IAAI,CAAC,IAAI,CAAC,UAAU;YAAE,OAAO;QAE7B,MAAM,iBAAiB,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,KAAK,MAAM,CAAC,SAAS,CAAC,CAAC;QAChG,MAAM,SAAS,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QAExD,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC3B,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACnC,CAAC;QAED,MAAM,OAAO,GAAqB;YAChC,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,SAAS;YACpC,WAAW,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC;YACnC,cAAc,EAAE,IAAI,CAAC,UAAU,CAAC,cAAc;YAC9C,YAAY,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC;YACpC,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,cAAc;YACzC,oBAAoB,EAAE,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,KAAK,MAAM,CAAC,SAAS,CAAC,CAAC,MAAM;YAClG,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACK,wBAAwB,CAAC,OAA4B,EAAE,SAAiB;QAC9E,MAAM,aAAa,GAAG,WAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;QAEpD,MAAM,UAAU,GAA2B;YACzC,eAAe,EAAE,IAAI,CAAC,UAAU,EAAE,SAAS,IAAI,CAAC;YAChD,gBAAgB,EAAE,IAAI,CAAC,eAAe;YACtC,oBAAoB,EAAE,IAAI,CAAC,UAAU,EAAE,SAAS,IAAI,CAAC;YACrD,aAAa;YACb,kBAAkB,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC;YACxD,qBAAqB,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,cAAc,CAAC;YAC9D,gBAAgB,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;YACpD,0BAA0B,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,oBAAoB,CAAC;YACzE,sBAAsB,EAAE;gBACtB,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,SAAS;gBACpC,kBAAkB,EAAE,IAAI,CAAC,uBAAuB,EAAE;gBAClD,mBAAmB,EAAE,IAAI,CAAC,UAAU,EAAE,cAAc,IAAI,CAAC;aAC1D;SACF,CAAC;QAEF,MAAM,mBAAmB,GAAwB;YAC/C,UAAU,EAAE,IAAI,CAAC,OAAO;YACxB,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;YACzC,gBAAgB,EAAE,EAAE;YACpB,kBAAkB,EAAE,EAAE;SACvB,CAAC;QAEF,OAAO;YACL,SAAS,EAAE,OAAO,CAAC,EAAE;YACrB,MAAM,EAAE,4CAAkB,CAAC,SAAS;YACpC,YAAY,EAAE,IAAI,CAAC,YAAa;YAChC,UAAU;YACV,OAAO,EAAE,mBAAmB;YAC5B,QAAQ,EAAE,EAAE;YACZ,eAAe,EAAE,EAAE;YACnB,QAAQ,EAAE,EAAE;YACZ,MAAM,EAAE,EAAE;SACX,CAAC;IACJ,CAAC;IAED,kBAAkB;IACV,kBAAkB;QACxB,OAAO,gBAAgB,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IACjF,CAAC;IAEO,kBAAkB,CAAC,IAAY;QACrC,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,OAAO,GAAG,EAAE;YACV,KAAK,GAAG,CAAC,KAAK,GAAG,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC;YACxC,OAAO,KAAK,GAAG,MAAM,CAAC;QACxB,CAAC,CAAC;IACJ,CAAC;CACF;AA3qBD,8DA2qBC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\algorithms\\ParticleSwarmOptimization.ts"],
      sourcesContent: ["/**\r\n * Particle Swarm Optimization Algorithm Implementation for System Optimization\r\n * \r\n * Implements particle swarm optimization with:\r\n * - Configurable swarm topology (global, local, ring, star)\r\n * - Adaptive inertia weight and acceleration coefficients\r\n * - Constraint handling with penalty methods\r\n * - Multi-objective optimization support\r\n * - Velocity clamping and boundary handling\r\n * \r\n * @version 3.0.0\r\n * <AUTHOR> Suite Development Team\r\n */\r\n\r\nimport {\r\n  OptimizationSolution,\r\n  OptimizationVariable,\r\n  OptimizationProblem,\r\n  OptimizationResult,\r\n  OptimizationStatus,\r\n  OptimizationStatistics,\r\n  OptimizationHistory,\r\n  IterationHistory,\r\n  PopulationSnapshot,\r\n  SolutionPerformanceMetrics,\r\n  ObjectiveFunctionType,\r\n  ConstraintFunctionType\r\n} from '../types/SystemOptimizationTypes';\r\n\r\nexport interface ParticleSwarmParameters {\r\n  swarmSize: number;\r\n  maxIterations: number;\r\n  inertiaWeight: number;\r\n  inertiaWeightMin: number;\r\n  inertiaWeightMax: number;\r\n  accelerationCoefficients: [number, number]; // [c1, c2] - cognitive and social\r\n  maxVelocity: number;\r\n  velocityClampingFactor: number;\r\n  topology: 'global' | 'local' | 'ring' | 'star' | 'random';\r\n  neighborhoodSize: number;\r\n  boundaryHandling: 'reflect' | 'absorb' | 'invisible' | 'random';\r\n  constraintHandling: 'penalty' | 'repair' | 'death_penalty';\r\n  penaltyCoefficient: number;\r\n  adaptiveParameters: boolean;\r\n  diversityMaintenance: boolean;\r\n  eliteSize: number;\r\n  seedValue?: number;\r\n}\r\n\r\nexport interface Particle {\r\n  id: string;\r\n  position: (number | string)[];\r\n  velocity: number[];\r\n  fitness: number;\r\n  personalBest: {\r\n    position: (number | string)[];\r\n    fitness: number;\r\n  };\r\n  neighbors: string[];\r\n  age: number;\r\n  stagnationCount: number;\r\n}\r\n\r\nexport interface SwarmState {\r\n  particles: Particle[];\r\n  globalBest: {\r\n    position: (number | string)[];\r\n    fitness: number;\r\n    particleId: string;\r\n  };\r\n  iteration: number;\r\n  averageFitness: number;\r\n  diversityIndex: number;\r\n  convergenceRate: number;\r\n}\r\n\r\n/**\r\n * Particle Swarm Optimization algorithm for single and multi-objective optimization\r\n */\r\nexport class ParticleSwarmOptimization {\r\n  private parameters: ParticleSwarmParameters;\r\n  private swarmState: SwarmState | null = null;\r\n  private bestSolution: OptimizationSolution | null = null;\r\n  private history: IterationHistory[] = [];\r\n  private populationHistory: PopulationSnapshot[] = [];\r\n  private random: () => number;\r\n  private evaluationCount: number = 0;\r\n\r\n  constructor(parameters?: Partial<ParticleSwarmParameters>) {\r\n    this.parameters = {\r\n      swarmSize: 30,\r\n      maxIterations: 100,\r\n      inertiaWeight: 0.9,\r\n      inertiaWeightMin: 0.1,\r\n      inertiaWeightMax: 0.9,\r\n      accelerationCoefficients: [2.0, 2.0],\r\n      maxVelocity: 0.2,\r\n      velocityClampingFactor: 0.5,\r\n      topology: 'global',\r\n      neighborhoodSize: 3,\r\n      boundaryHandling: 'reflect',\r\n      constraintHandling: 'penalty',\r\n      penaltyCoefficient: 1000,\r\n      adaptiveParameters: true,\r\n      diversityMaintenance: true,\r\n      eliteSize: 2,\r\n      ...parameters\r\n    };\r\n\r\n    // Initialize random number generator\r\n    if (this.parameters.seedValue !== undefined) {\r\n      this.random = this.createSeededRandom(this.parameters.seedValue);\r\n    } else {\r\n      this.random = Math.random;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Main optimization method\r\n   */\r\n  public async optimize(\r\n    problem: OptimizationProblem,\r\n    objectiveFunction: ObjectiveFunctionType,\r\n    constraintFunctions: ConstraintFunctionType[]\r\n  ): Promise<OptimizationResult> {\r\n    const startTime = performance.now();\r\n    \r\n    try {\r\n      // Initialize algorithm\r\n      this.initializeAlgorithm(problem);\r\n      \r\n      // Create initial swarm\r\n      await this.createInitialSwarm(problem, objectiveFunction, constraintFunctions);\r\n      \r\n      // Main optimization loop\r\n      while (!this.shouldTerminate(problem)) {\r\n        await this.updateSwarm(problem, objectiveFunction, constraintFunctions);\r\n        this.updateHistory();\r\n        \r\n        if (this.parameters.adaptiveParameters) {\r\n          this.adaptParameters();\r\n        }\r\n        \r\n        if (this.parameters.diversityMaintenance) {\r\n          this.maintainDiversity(problem);\r\n        }\r\n      }\r\n      \r\n      // Create final result\r\n      return this.createOptimizationResult(problem, startTime);\r\n      \r\n    } catch (error) {\r\n      console.error('Particle swarm optimization failed:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Initialize algorithm state\r\n   */\r\n  private initializeAlgorithm(problem: OptimizationProblem): void {\r\n    this.swarmState = null;\r\n    this.bestSolution = null;\r\n    this.history = [];\r\n    this.populationHistory = [];\r\n    this.evaluationCount = 0;\r\n    \r\n    console.log(`Initializing Particle Swarm Optimization with swarm size: ${this.parameters.swarmSize}`);\r\n  }\r\n\r\n  /**\r\n   * Create initial swarm\r\n   */\r\n  private async createInitialSwarm(\r\n    problem: OptimizationProblem,\r\n    objectiveFunction: ObjectiveFunctionType,\r\n    constraintFunctions: ConstraintFunctionType[]\r\n  ): Promise<void> {\r\n    const particles: Particle[] = [];\r\n    \r\n    // Create particles\r\n    for (let i = 0; i < this.parameters.swarmSize; i++) {\r\n      const particle = this.createRandomParticle(problem);\r\n      await this.evaluateParticle(particle, problem, objectiveFunction, constraintFunctions);\r\n      particles.push(particle);\r\n    }\r\n    \r\n    // Initialize swarm state\r\n    this.swarmState = {\r\n      particles,\r\n      globalBest: this.findGlobalBest(particles),\r\n      iteration: 0,\r\n      averageFitness: this.calculateAverageFitness(particles),\r\n      diversityIndex: this.calculateDiversityIndex(particles),\r\n      convergenceRate: 0\r\n    };\r\n    \r\n    // Set up neighborhood topology\r\n    this.setupTopology();\r\n    \r\n    // Convert best particle to optimization solution\r\n    this.updateBestSolution(problem);\r\n  }\r\n\r\n  /**\r\n   * Create a random particle\r\n   */\r\n  private createRandomParticle(problem: OptimizationProblem): Particle {\r\n    const position: (number | string)[] = [];\r\n    const velocity: number[] = [];\r\n    \r\n    for (const variable of problem.variables) {\r\n      if (variable.discreteValues && variable.discreteValues.length > 0) {\r\n        // Discrete variable\r\n        const randomIndex = Math.floor(this.random() * variable.discreteValues.length);\r\n        position.push(variable.discreteValues[randomIndex]);\r\n        velocity.push(0); // No velocity for discrete variables\r\n      } else {\r\n        // Continuous variable\r\n        const min = typeof variable.bounds.minimum === 'number' ? variable.bounds.minimum : 0;\r\n        const max = typeof variable.bounds.maximum === 'number' ? variable.bounds.maximum : 1;\r\n        const pos = min + this.random() * (max - min);\r\n        const vel = (this.random() - 0.5) * this.parameters.maxVelocity * (max - min);\r\n        \r\n        position.push(pos);\r\n        velocity.push(vel);\r\n      }\r\n    }\r\n    \r\n    const particle: Particle = {\r\n      id: this.generateParticleId(),\r\n      position,\r\n      velocity,\r\n      fitness: Number.MAX_VALUE,\r\n      personalBest: {\r\n        position: [...position],\r\n        fitness: Number.MAX_VALUE\r\n      },\r\n      neighbors: [],\r\n      age: 0,\r\n      stagnationCount: 0\r\n    };\r\n    \r\n    return particle;\r\n  }\r\n\r\n  /**\r\n   * Evaluate particle fitness\r\n   */\r\n  private async evaluateParticle(\r\n    particle: Particle,\r\n    problem: OptimizationProblem,\r\n    objectiveFunction: ObjectiveFunctionType,\r\n    constraintFunctions: ConstraintFunctionType[]\r\n  ): Promise<void> {\r\n    try {\r\n      // Convert particle position to optimization variables\r\n      const variables = this.particleToVariables(particle, problem.variables);\r\n      \r\n      // Evaluate objective function\r\n      const objectiveValue = objectiveFunction(variables);\r\n      particle.fitness = objectiveValue;\r\n      \r\n      // Evaluate constraints\r\n      const constraintViolations: number[] = [];\r\n      for (const constraintFunction of constraintFunctions) {\r\n        const violation = constraintFunction(variables);\r\n        constraintViolations.push(violation);\r\n      }\r\n      \r\n      // Apply constraint handling\r\n      if (this.parameters.constraintHandling === 'penalty') {\r\n        const totalPenalty = constraintViolations\r\n          .filter(v => v > 0)\r\n          .reduce((sum, v) => sum + v, 0) * this.parameters.penaltyCoefficient;\r\n        particle.fitness += totalPenalty;\r\n      }\r\n      \r\n      // Update personal best\r\n      if (particle.fitness < particle.personalBest.fitness) {\r\n        particle.personalBest.position = [...particle.position];\r\n        particle.personalBest.fitness = particle.fitness;\r\n        particle.stagnationCount = 0;\r\n      } else {\r\n        particle.stagnationCount++;\r\n      }\r\n      \r\n      this.evaluationCount++;\r\n      \r\n    } catch (error) {\r\n      console.error('Error evaluating particle:', error);\r\n      particle.fitness = Number.MAX_VALUE;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Convert particle to optimization variables\r\n   */\r\n  private particleToVariables(particle: Particle, variableTemplates: OptimizationVariable[]): OptimizationVariable[] {\r\n    return variableTemplates.map((template, index) => ({\r\n      ...template,\r\n      currentValue: particle.position[index]\r\n    }));\r\n  }\r\n\r\n  /**\r\n   * Update swarm for one iteration\r\n   */\r\n  private async updateSwarm(\r\n    problem: OptimizationProblem,\r\n    objectiveFunction: ObjectiveFunctionType,\r\n    constraintFunctions: ConstraintFunctionType[]\r\n  ): Promise<void> {\r\n    if (!this.swarmState) return;\r\n    \r\n    // Update each particle\r\n    for (const particle of this.swarmState.particles) {\r\n      // Update velocity\r\n      this.updateParticleVelocity(particle, problem);\r\n      \r\n      // Update position\r\n      this.updateParticlePosition(particle, problem);\r\n      \r\n      // Evaluate new position\r\n      await this.evaluateParticle(particle, problem, objectiveFunction, constraintFunctions);\r\n      \r\n      // Update age\r\n      particle.age++;\r\n    }\r\n    \r\n    // Update global best\r\n    const newGlobalBest = this.findGlobalBest(this.swarmState.particles);\r\n    if (newGlobalBest.fitness < this.swarmState.globalBest.fitness) {\r\n      this.swarmState.globalBest = newGlobalBest;\r\n      this.updateBestSolution(problem);\r\n    }\r\n    \r\n    // Update swarm statistics\r\n    this.swarmState.averageFitness = this.calculateAverageFitness(this.swarmState.particles);\r\n    this.swarmState.diversityIndex = this.calculateDiversityIndex(this.swarmState.particles);\r\n    this.swarmState.iteration++;\r\n  }\r\n\r\n  /**\r\n   * Update particle velocity\r\n   */\r\n  private updateParticleVelocity(particle: Particle, problem: OptimizationProblem): void {\r\n    const [c1, c2] = this.parameters.accelerationCoefficients;\r\n    const w = this.getCurrentInertiaWeight();\r\n    \r\n    // Find neighborhood best\r\n    const neighborhoodBest = this.findNeighborhoodBest(particle);\r\n    \r\n    for (let i = 0; i < particle.velocity.length; i++) {\r\n      if (typeof particle.position[i] === 'number') {\r\n        const currentPos = particle.position[i] as number;\r\n        const personalBestPos = particle.personalBest.position[i] as number;\r\n        const neighborhoodBestPos = neighborhoodBest.position[i] as number;\r\n        \r\n        // PSO velocity update equation\r\n        const cognitive = c1 * this.random() * (personalBestPos - currentPos);\r\n        const social = c2 * this.random() * (neighborhoodBestPos - currentPos);\r\n        \r\n        particle.velocity[i] = w * particle.velocity[i] + cognitive + social;\r\n        \r\n        // Velocity clamping\r\n        const variable = problem.variables[i];\r\n        const min = typeof variable.bounds.minimum === 'number' ? variable.bounds.minimum : 0;\r\n        const max = typeof variable.bounds.maximum === 'number' ? variable.bounds.maximum : 1;\r\n        const maxVel = this.parameters.maxVelocity * (max - min);\r\n        \r\n        particle.velocity[i] = Math.max(-maxVel, Math.min(maxVel, particle.velocity[i]));\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update particle position\r\n   */\r\n  private updateParticlePosition(particle: Particle, problem: OptimizationProblem): void {\r\n    for (let i = 0; i < particle.position.length; i++) {\r\n      const variable = problem.variables[i];\r\n      \r\n      if (variable.discreteValues && variable.discreteValues.length > 0) {\r\n        // Discrete variable - probabilistic update\r\n        if (this.random() < 0.1) { // 10% chance to change\r\n          const randomIndex = Math.floor(this.random() * variable.discreteValues.length);\r\n          particle.position[i] = variable.discreteValues[randomIndex];\r\n        }\r\n      } else if (typeof particle.position[i] === 'number') {\r\n        // Continuous variable\r\n        const newPos = (particle.position[i] as number) + particle.velocity[i];\r\n        \r\n        // Boundary handling\r\n        const min = typeof variable.bounds.minimum === 'number' ? variable.bounds.minimum : 0;\r\n        const max = typeof variable.bounds.maximum === 'number' ? variable.bounds.maximum : 1;\r\n        \r\n        particle.position[i] = this.handleBoundary(newPos, min, max, particle.velocity[i], i);\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Handle boundary violations\r\n   */\r\n  private handleBoundary(position: number, min: number, max: number, velocity: number, index: number): number {\r\n    switch (this.parameters.boundaryHandling) {\r\n      case 'reflect':\r\n        if (position < min) {\r\n          return min + (min - position);\r\n        } else if (position > max) {\r\n          return max - (position - max);\r\n        }\r\n        return position;\r\n        \r\n      case 'absorb':\r\n        return Math.max(min, Math.min(max, position));\r\n        \r\n      case 'invisible':\r\n        // Keep position but set velocity to zero if boundary violated\r\n        if (position < min || position > max) {\r\n          return Math.max(min, Math.min(max, position));\r\n        }\r\n        return position;\r\n        \r\n      case 'random':\r\n        if (position < min || position > max) {\r\n          return min + this.random() * (max - min);\r\n        }\r\n        return position;\r\n        \r\n      default:\r\n        return Math.max(min, Math.min(max, position));\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Find global best particle\r\n   */\r\n  private findGlobalBest(particles: Particle[]): { position: (number | string)[]; fitness: number; particleId: string } {\r\n    const best = particles.reduce((best, current) => \r\n      current.fitness < best.fitness ? current : best\r\n    );\r\n    \r\n    return {\r\n      position: [...best.position],\r\n      fitness: best.fitness,\r\n      particleId: best.id\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Find neighborhood best for a particle\r\n   */\r\n  private findNeighborhoodBest(particle: Particle): { position: (number | string)[]; fitness: number } {\r\n    if (!this.swarmState) return { position: particle.position, fitness: particle.fitness };\r\n    \r\n    switch (this.parameters.topology) {\r\n      case 'global':\r\n        return this.swarmState.globalBest;\r\n        \r\n      case 'local':\r\n      case 'ring':\r\n        // Find best among neighbors\r\n        let best = particle.personalBest;\r\n        for (const neighborId of particle.neighbors) {\r\n          const neighbor = this.swarmState.particles.find(p => p.id === neighborId);\r\n          if (neighbor && neighbor.personalBest.fitness < best.fitness) {\r\n            best = neighbor.personalBest;\r\n          }\r\n        }\r\n        return best;\r\n        \r\n      default:\r\n        return this.swarmState.globalBest;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Setup neighborhood topology\r\n   */\r\n  private setupTopology(): void {\r\n    if (!this.swarmState) return;\r\n    \r\n    const particles = this.swarmState.particles;\r\n    \r\n    switch (this.parameters.topology) {\r\n      case 'ring':\r\n        for (let i = 0; i < particles.length; i++) {\r\n          const prev = (i - 1 + particles.length) % particles.length;\r\n          const next = (i + 1) % particles.length;\r\n          particles[i].neighbors = [particles[prev].id, particles[next].id];\r\n        }\r\n        break;\r\n        \r\n      case 'local':\r\n        for (let i = 0; i < particles.length; i++) {\r\n          const neighbors: string[] = [];\r\n          for (let j = 0; j < this.parameters.neighborhoodSize && j < particles.length; j++) {\r\n            const neighborIndex = (i + j + 1) % particles.length;\r\n            if (neighborIndex !== i) {\r\n              neighbors.push(particles[neighborIndex].id);\r\n            }\r\n          }\r\n          particles[i].neighbors = neighbors;\r\n        }\r\n        break;\r\n        \r\n      case 'star':\r\n        // All particles connected to best particle\r\n        const bestParticle = this.findGlobalBest(particles);\r\n        for (const particle of particles) {\r\n          if (particle.id !== bestParticle.particleId) {\r\n            particle.neighbors = [bestParticle.particleId];\r\n          }\r\n        }\r\n        break;\r\n        \r\n      case 'random':\r\n        for (const particle of particles) {\r\n          const neighbors: string[] = [];\r\n          for (let j = 0; j < this.parameters.neighborhoodSize; j++) {\r\n            const randomIndex = Math.floor(this.random() * particles.length);\r\n            if (particles[randomIndex].id !== particle.id && \r\n                !neighbors.includes(particles[randomIndex].id)) {\r\n              neighbors.push(particles[randomIndex].id);\r\n            }\r\n          }\r\n          particle.neighbors = neighbors;\r\n        }\r\n        break;\r\n        \r\n      default: // global\r\n        // No explicit neighbors - all particles use global best\r\n        break;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get current inertia weight (adaptive)\r\n   */\r\n  private getCurrentInertiaWeight(): number {\r\n    if (!this.parameters.adaptiveParameters || !this.swarmState) {\r\n      return this.parameters.inertiaWeight;\r\n    }\r\n    \r\n    // Linear decrease from max to min\r\n    const progress = this.swarmState.iteration / this.parameters.maxIterations;\r\n    return this.parameters.inertiaWeightMax - \r\n           progress * (this.parameters.inertiaWeightMax - this.parameters.inertiaWeightMin);\r\n  }\r\n\r\n  /**\r\n   * Adapt algorithm parameters\r\n   */\r\n  private adaptParameters(): void {\r\n    if (!this.swarmState) return;\r\n    \r\n    // Adapt based on diversity\r\n    if (this.swarmState.diversityIndex < 0.1) {\r\n      // Low diversity - increase exploration\r\n      this.parameters.inertiaWeight = Math.min(0.9, this.parameters.inertiaWeight * 1.1);\r\n      this.parameters.maxVelocity = Math.min(0.5, this.parameters.maxVelocity * 1.1);\r\n    } else if (this.swarmState.diversityIndex > 0.8) {\r\n      // High diversity - increase exploitation\r\n      this.parameters.inertiaWeight = Math.max(0.1, this.parameters.inertiaWeight * 0.9);\r\n      this.parameters.maxVelocity = Math.max(0.05, this.parameters.maxVelocity * 0.9);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Maintain swarm diversity\r\n   */\r\n  private maintainDiversity(problem: OptimizationProblem): void {\r\n    if (!this.swarmState) return;\r\n    \r\n    // Replace stagnant particles\r\n    for (const particle of this.swarmState.particles) {\r\n      if (particle.stagnationCount > 20) {\r\n        // Reinitialize particle\r\n        const newParticle = this.createRandomParticle(problem);\r\n        Object.assign(particle, newParticle);\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Calculate average fitness\r\n   */\r\n  private calculateAverageFitness(particles: Particle[]): number {\r\n    const validFitnesses = particles.map(p => p.fitness).filter(f => f !== Number.MAX_VALUE);\r\n    return validFitnesses.length > 0 ? \r\n           validFitnesses.reduce((sum, f) => sum + f, 0) / validFitnesses.length : \r\n           Number.MAX_VALUE;\r\n  }\r\n\r\n  /**\r\n   * Calculate diversity index\r\n   */\r\n  private calculateDiversityIndex(particles: Particle[]): number {\r\n    if (particles.length < 2) return 0;\r\n    \r\n    let totalDistance = 0;\r\n    let pairCount = 0;\r\n    \r\n    for (let i = 0; i < particles.length; i++) {\r\n      for (let j = i + 1; j < particles.length; j++) {\r\n        const distance = this.calculateDistance(particles[i], particles[j]);\r\n        totalDistance += distance;\r\n        pairCount++;\r\n      }\r\n    }\r\n    \r\n    return pairCount > 0 ? totalDistance / pairCount : 0;\r\n  }\r\n\r\n  /**\r\n   * Calculate distance between particles\r\n   */\r\n  private calculateDistance(particle1: Particle, particle2: Particle): number {\r\n    let distance = 0;\r\n    \r\n    for (let i = 0; i < particle1.position.length; i++) {\r\n      if (typeof particle1.position[i] === 'number' && typeof particle2.position[i] === 'number') {\r\n        const diff = (particle1.position[i] as number) - (particle2.position[i] as number);\r\n        distance += diff * diff;\r\n      }\r\n    }\r\n    \r\n    return Math.sqrt(distance);\r\n  }\r\n\r\n  /**\r\n   * Update best solution\r\n   */\r\n  private updateBestSolution(problem: OptimizationProblem): void {\r\n    if (!this.swarmState) return;\r\n    \r\n    const bestParticle = this.swarmState.particles.find(p => p.id === this.swarmState!.globalBest.particleId);\r\n    if (!bestParticle) return;\r\n    \r\n    const variables: { [variableId: string]: number | string } = {};\r\n    problem.variables.forEach((variable, index) => {\r\n      variables[variable.id] = bestParticle.position[index];\r\n    });\r\n    \r\n    this.bestSolution = {\r\n      id: `pso_best_${Date.now()}`,\r\n      variables,\r\n      objectiveValues: {},\r\n      constraintViolations: [],\r\n      feasible: true,\r\n      fitness: bestParticle.fitness,\r\n      systemConfiguration: problem.systemConfiguration,\r\n      performanceMetrics: {} as SolutionPerformanceMetrics\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Check termination criteria\r\n   */\r\n  private shouldTerminate(problem: OptimizationProblem): boolean {\r\n    if (!this.swarmState) return true;\r\n    \r\n    // Maximum iterations\r\n    if (this.swarmState.iteration >= this.parameters.maxIterations) {\r\n      return true;\r\n    }\r\n    \r\n    // Convergence check\r\n    if (this.history.length >= 20) {\r\n      const recentHistory = this.history.slice(-20);\r\n      const fitnessImprovement = recentHistory[0].bestFitness - recentHistory[recentHistory.length - 1].bestFitness;\r\n      \r\n      if (Math.abs(fitnessImprovement) < problem.convergenceCriteria.toleranceValue) {\r\n        return true;\r\n      }\r\n    }\r\n    \r\n    return false;\r\n  }\r\n\r\n  /**\r\n   * Update optimization history\r\n   */\r\n  private updateHistory(): void {\r\n    if (!this.swarmState) return;\r\n    \r\n    const feasibleParticles = this.swarmState.particles.filter(p => p.fitness !== Number.MAX_VALUE);\r\n    const fitnesses = feasibleParticles.map(p => p.fitness);\r\n    \r\n    if (fitnesses.length === 0) {\r\n      fitnesses.push(Number.MAX_VALUE);\r\n    }\r\n    \r\n    const history: IterationHistory = {\r\n      iteration: this.swarmState.iteration,\r\n      bestFitness: Math.min(...fitnesses),\r\n      averageFitness: this.swarmState.averageFitness,\r\n      worstFitness: Math.max(...fitnesses),\r\n      diversity: this.swarmState.diversityIndex,\r\n      constraintViolations: this.swarmState.particles.filter(p => p.fitness === Number.MAX_VALUE).length,\r\n      timestamp: new Date()\r\n    };\r\n    \r\n    this.history.push(history);\r\n  }\r\n\r\n  /**\r\n   * Create optimization result\r\n   */\r\n  private createOptimizationResult(problem: OptimizationProblem, startTime: number): OptimizationResult {\r\n    const executionTime = performance.now() - startTime;\r\n    \r\n    const statistics: OptimizationStatistics = {\r\n      totalIterations: this.swarmState?.iteration || 0,\r\n      totalEvaluations: this.evaluationCount,\r\n      convergenceIteration: this.swarmState?.iteration || 0,\r\n      executionTime,\r\n      bestFitnessHistory: this.history.map(h => h.bestFitness),\r\n      averageFitnessHistory: this.history.map(h => h.averageFitness),\r\n      diversityHistory: this.history.map(h => h.diversity),\r\n      constraintViolationHistory: this.history.map(h => h.constraintViolations),\r\n      algorithmSpecificStats: {\r\n        swarmSize: this.parameters.swarmSize,\r\n        finalInertiaWeight: this.getCurrentInertiaWeight(),\r\n        finalDiversityIndex: this.swarmState?.diversityIndex || 0\r\n      }\r\n    };\r\n    \r\n    const optimizationHistory: OptimizationHistory = {\r\n      iterations: this.history,\r\n      populationHistory: this.populationHistory,\r\n      parameterHistory: [],\r\n      convergenceMetrics: []\r\n    };\r\n    \r\n    return {\r\n      problemId: problem.id,\r\n      status: OptimizationStatus.CONVERGED,\r\n      bestSolution: this.bestSolution!,\r\n      statistics,\r\n      history: optimizationHistory,\r\n      analysis: {},\r\n      recommendations: [],\r\n      warnings: [],\r\n      errors: []\r\n    };\r\n  }\r\n\r\n  // Utility methods\r\n  private generateParticleId(): string {\r\n    return `pso_particle_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\r\n  }\r\n\r\n  private createSeededRandom(seed: number): () => number {\r\n    let state = seed;\r\n    return () => {\r\n      state = (state * 9301 + 49297) % 233280;\r\n      return state / 233280;\r\n    };\r\n  }\r\n}\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "f49aa23fb815c4d2d72eda98db3a54f25a162e44"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_161daubuf = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_161daubuf();
cov_161daubuf().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_161daubuf().s[1]++;
exports.ParticleSwarmOptimization = void 0;
const SystemOptimizationTypes_1 =
/* istanbul ignore next */
(cov_161daubuf().s[2]++, require("../types/SystemOptimizationTypes"));
/**
 * Particle Swarm Optimization algorithm for single and multi-objective optimization
 */
class ParticleSwarmOptimization {
  constructor(parameters) {
    /* istanbul ignore next */
    cov_161daubuf().f[0]++;
    cov_161daubuf().s[3]++;
    this.swarmState = null;
    /* istanbul ignore next */
    cov_161daubuf().s[4]++;
    this.bestSolution = null;
    /* istanbul ignore next */
    cov_161daubuf().s[5]++;
    this.history = [];
    /* istanbul ignore next */
    cov_161daubuf().s[6]++;
    this.populationHistory = [];
    /* istanbul ignore next */
    cov_161daubuf().s[7]++;
    this.evaluationCount = 0;
    /* istanbul ignore next */
    cov_161daubuf().s[8]++;
    this.parameters = {
      swarmSize: 30,
      maxIterations: 100,
      inertiaWeight: 0.9,
      inertiaWeightMin: 0.1,
      inertiaWeightMax: 0.9,
      accelerationCoefficients: [2.0, 2.0],
      maxVelocity: 0.2,
      velocityClampingFactor: 0.5,
      topology: 'global',
      neighborhoodSize: 3,
      boundaryHandling: 'reflect',
      constraintHandling: 'penalty',
      penaltyCoefficient: 1000,
      adaptiveParameters: true,
      diversityMaintenance: true,
      eliteSize: 2,
      ...parameters
    };
    // Initialize random number generator
    /* istanbul ignore next */
    cov_161daubuf().s[9]++;
    if (this.parameters.seedValue !== undefined) {
      /* istanbul ignore next */
      cov_161daubuf().b[0][0]++;
      cov_161daubuf().s[10]++;
      this.random = this.createSeededRandom(this.parameters.seedValue);
    } else {
      /* istanbul ignore next */
      cov_161daubuf().b[0][1]++;
      cov_161daubuf().s[11]++;
      this.random = Math.random;
    }
  }
  /**
   * Main optimization method
   */
  async optimize(problem, objectiveFunction, constraintFunctions) {
    /* istanbul ignore next */
    cov_161daubuf().f[1]++;
    const startTime =
    /* istanbul ignore next */
    (cov_161daubuf().s[12]++, performance.now());
    /* istanbul ignore next */
    cov_161daubuf().s[13]++;
    try {
      /* istanbul ignore next */
      cov_161daubuf().s[14]++;
      // Initialize algorithm
      this.initializeAlgorithm(problem);
      // Create initial swarm
      /* istanbul ignore next */
      cov_161daubuf().s[15]++;
      await this.createInitialSwarm(problem, objectiveFunction, constraintFunctions);
      // Main optimization loop
      /* istanbul ignore next */
      cov_161daubuf().s[16]++;
      while (!this.shouldTerminate(problem)) {
        /* istanbul ignore next */
        cov_161daubuf().s[17]++;
        await this.updateSwarm(problem, objectiveFunction, constraintFunctions);
        /* istanbul ignore next */
        cov_161daubuf().s[18]++;
        this.updateHistory();
        /* istanbul ignore next */
        cov_161daubuf().s[19]++;
        if (this.parameters.adaptiveParameters) {
          /* istanbul ignore next */
          cov_161daubuf().b[1][0]++;
          cov_161daubuf().s[20]++;
          this.adaptParameters();
        } else
        /* istanbul ignore next */
        {
          cov_161daubuf().b[1][1]++;
        }
        cov_161daubuf().s[21]++;
        if (this.parameters.diversityMaintenance) {
          /* istanbul ignore next */
          cov_161daubuf().b[2][0]++;
          cov_161daubuf().s[22]++;
          this.maintainDiversity(problem);
        } else
        /* istanbul ignore next */
        {
          cov_161daubuf().b[2][1]++;
        }
      }
      // Create final result
      /* istanbul ignore next */
      cov_161daubuf().s[23]++;
      return this.createOptimizationResult(problem, startTime);
    } catch (error) {
      /* istanbul ignore next */
      cov_161daubuf().s[24]++;
      console.error('Particle swarm optimization failed:', error);
      /* istanbul ignore next */
      cov_161daubuf().s[25]++;
      throw error;
    }
  }
  /**
   * Initialize algorithm state
   */
  initializeAlgorithm(problem) {
    /* istanbul ignore next */
    cov_161daubuf().f[2]++;
    cov_161daubuf().s[26]++;
    this.swarmState = null;
    /* istanbul ignore next */
    cov_161daubuf().s[27]++;
    this.bestSolution = null;
    /* istanbul ignore next */
    cov_161daubuf().s[28]++;
    this.history = [];
    /* istanbul ignore next */
    cov_161daubuf().s[29]++;
    this.populationHistory = [];
    /* istanbul ignore next */
    cov_161daubuf().s[30]++;
    this.evaluationCount = 0;
    /* istanbul ignore next */
    cov_161daubuf().s[31]++;
    console.log(`Initializing Particle Swarm Optimization with swarm size: ${this.parameters.swarmSize}`);
  }
  /**
   * Create initial swarm
   */
  async createInitialSwarm(problem, objectiveFunction, constraintFunctions) {
    /* istanbul ignore next */
    cov_161daubuf().f[3]++;
    const particles =
    /* istanbul ignore next */
    (cov_161daubuf().s[32]++, []);
    // Create particles
    /* istanbul ignore next */
    cov_161daubuf().s[33]++;
    for (let i =
    /* istanbul ignore next */
    (cov_161daubuf().s[34]++, 0); i < this.parameters.swarmSize; i++) {
      const particle =
      /* istanbul ignore next */
      (cov_161daubuf().s[35]++, this.createRandomParticle(problem));
      /* istanbul ignore next */
      cov_161daubuf().s[36]++;
      await this.evaluateParticle(particle, problem, objectiveFunction, constraintFunctions);
      /* istanbul ignore next */
      cov_161daubuf().s[37]++;
      particles.push(particle);
    }
    // Initialize swarm state
    /* istanbul ignore next */
    cov_161daubuf().s[38]++;
    this.swarmState = {
      particles,
      globalBest: this.findGlobalBest(particles),
      iteration: 0,
      averageFitness: this.calculateAverageFitness(particles),
      diversityIndex: this.calculateDiversityIndex(particles),
      convergenceRate: 0
    };
    // Set up neighborhood topology
    /* istanbul ignore next */
    cov_161daubuf().s[39]++;
    this.setupTopology();
    // Convert best particle to optimization solution
    /* istanbul ignore next */
    cov_161daubuf().s[40]++;
    this.updateBestSolution(problem);
  }
  /**
   * Create a random particle
   */
  createRandomParticle(problem) {
    /* istanbul ignore next */
    cov_161daubuf().f[4]++;
    const position =
    /* istanbul ignore next */
    (cov_161daubuf().s[41]++, []);
    const velocity =
    /* istanbul ignore next */
    (cov_161daubuf().s[42]++, []);
    /* istanbul ignore next */
    cov_161daubuf().s[43]++;
    for (const variable of problem.variables) {
      /* istanbul ignore next */
      cov_161daubuf().s[44]++;
      if (
      /* istanbul ignore next */
      (cov_161daubuf().b[4][0]++, variable.discreteValues) &&
      /* istanbul ignore next */
      (cov_161daubuf().b[4][1]++, variable.discreteValues.length > 0)) {
        /* istanbul ignore next */
        cov_161daubuf().b[3][0]++;
        // Discrete variable
        const randomIndex =
        /* istanbul ignore next */
        (cov_161daubuf().s[45]++, Math.floor(this.random() * variable.discreteValues.length));
        /* istanbul ignore next */
        cov_161daubuf().s[46]++;
        position.push(variable.discreteValues[randomIndex]);
        /* istanbul ignore next */
        cov_161daubuf().s[47]++;
        velocity.push(0); // No velocity for discrete variables
      } else {
        /* istanbul ignore next */
        cov_161daubuf().b[3][1]++;
        // Continuous variable
        const min =
        /* istanbul ignore next */
        (cov_161daubuf().s[48]++, typeof variable.bounds.minimum === 'number' ?
        /* istanbul ignore next */
        (cov_161daubuf().b[5][0]++, variable.bounds.minimum) :
        /* istanbul ignore next */
        (cov_161daubuf().b[5][1]++, 0));
        const max =
        /* istanbul ignore next */
        (cov_161daubuf().s[49]++, typeof variable.bounds.maximum === 'number' ?
        /* istanbul ignore next */
        (cov_161daubuf().b[6][0]++, variable.bounds.maximum) :
        /* istanbul ignore next */
        (cov_161daubuf().b[6][1]++, 1));
        const pos =
        /* istanbul ignore next */
        (cov_161daubuf().s[50]++, min + this.random() * (max - min));
        const vel =
        /* istanbul ignore next */
        (cov_161daubuf().s[51]++, (this.random() - 0.5) * this.parameters.maxVelocity * (max - min));
        /* istanbul ignore next */
        cov_161daubuf().s[52]++;
        position.push(pos);
        /* istanbul ignore next */
        cov_161daubuf().s[53]++;
        velocity.push(vel);
      }
    }
    const particle =
    /* istanbul ignore next */
    (cov_161daubuf().s[54]++, {
      id: this.generateParticleId(),
      position,
      velocity,
      fitness: Number.MAX_VALUE,
      personalBest: {
        position: [...position],
        fitness: Number.MAX_VALUE
      },
      neighbors: [],
      age: 0,
      stagnationCount: 0
    });
    /* istanbul ignore next */
    cov_161daubuf().s[55]++;
    return particle;
  }
  /**
   * Evaluate particle fitness
   */
  async evaluateParticle(particle, problem, objectiveFunction, constraintFunctions) {
    /* istanbul ignore next */
    cov_161daubuf().f[5]++;
    cov_161daubuf().s[56]++;
    try {
      // Convert particle position to optimization variables
      const variables =
      /* istanbul ignore next */
      (cov_161daubuf().s[57]++, this.particleToVariables(particle, problem.variables));
      // Evaluate objective function
      const objectiveValue =
      /* istanbul ignore next */
      (cov_161daubuf().s[58]++, objectiveFunction(variables));
      /* istanbul ignore next */
      cov_161daubuf().s[59]++;
      particle.fitness = objectiveValue;
      // Evaluate constraints
      const constraintViolations =
      /* istanbul ignore next */
      (cov_161daubuf().s[60]++, []);
      /* istanbul ignore next */
      cov_161daubuf().s[61]++;
      for (const constraintFunction of constraintFunctions) {
        const violation =
        /* istanbul ignore next */
        (cov_161daubuf().s[62]++, constraintFunction(variables));
        /* istanbul ignore next */
        cov_161daubuf().s[63]++;
        constraintViolations.push(violation);
      }
      // Apply constraint handling
      /* istanbul ignore next */
      cov_161daubuf().s[64]++;
      if (this.parameters.constraintHandling === 'penalty') {
        /* istanbul ignore next */
        cov_161daubuf().b[7][0]++;
        const totalPenalty =
        /* istanbul ignore next */
        (cov_161daubuf().s[65]++, constraintViolations.filter(v => {
          /* istanbul ignore next */
          cov_161daubuf().f[6]++;
          cov_161daubuf().s[66]++;
          return v > 0;
        }).reduce((sum, v) => {
          /* istanbul ignore next */
          cov_161daubuf().f[7]++;
          cov_161daubuf().s[67]++;
          return sum + v;
        }, 0) * this.parameters.penaltyCoefficient);
        /* istanbul ignore next */
        cov_161daubuf().s[68]++;
        particle.fitness += totalPenalty;
      } else
      /* istanbul ignore next */
      {
        cov_161daubuf().b[7][1]++;
      }
      // Update personal best
      cov_161daubuf().s[69]++;
      if (particle.fitness < particle.personalBest.fitness) {
        /* istanbul ignore next */
        cov_161daubuf().b[8][0]++;
        cov_161daubuf().s[70]++;
        particle.personalBest.position = [...particle.position];
        /* istanbul ignore next */
        cov_161daubuf().s[71]++;
        particle.personalBest.fitness = particle.fitness;
        /* istanbul ignore next */
        cov_161daubuf().s[72]++;
        particle.stagnationCount = 0;
      } else {
        /* istanbul ignore next */
        cov_161daubuf().b[8][1]++;
        cov_161daubuf().s[73]++;
        particle.stagnationCount++;
      }
      /* istanbul ignore next */
      cov_161daubuf().s[74]++;
      this.evaluationCount++;
    } catch (error) {
      /* istanbul ignore next */
      cov_161daubuf().s[75]++;
      console.error('Error evaluating particle:', error);
      /* istanbul ignore next */
      cov_161daubuf().s[76]++;
      particle.fitness = Number.MAX_VALUE;
    }
  }
  /**
   * Convert particle to optimization variables
   */
  particleToVariables(particle, variableTemplates) {
    /* istanbul ignore next */
    cov_161daubuf().f[8]++;
    cov_161daubuf().s[77]++;
    return variableTemplates.map((template, index) => {
      /* istanbul ignore next */
      cov_161daubuf().f[9]++;
      cov_161daubuf().s[78]++;
      return {
        ...template,
        currentValue: particle.position[index]
      };
    });
  }
  /**
   * Update swarm for one iteration
   */
  async updateSwarm(problem, objectiveFunction, constraintFunctions) {
    /* istanbul ignore next */
    cov_161daubuf().f[10]++;
    cov_161daubuf().s[79]++;
    if (!this.swarmState) {
      /* istanbul ignore next */
      cov_161daubuf().b[9][0]++;
      cov_161daubuf().s[80]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_161daubuf().b[9][1]++;
    }
    // Update each particle
    cov_161daubuf().s[81]++;
    for (const particle of this.swarmState.particles) {
      /* istanbul ignore next */
      cov_161daubuf().s[82]++;
      // Update velocity
      this.updateParticleVelocity(particle, problem);
      // Update position
      /* istanbul ignore next */
      cov_161daubuf().s[83]++;
      this.updateParticlePosition(particle, problem);
      // Evaluate new position
      /* istanbul ignore next */
      cov_161daubuf().s[84]++;
      await this.evaluateParticle(particle, problem, objectiveFunction, constraintFunctions);
      // Update age
      /* istanbul ignore next */
      cov_161daubuf().s[85]++;
      particle.age++;
    }
    // Update global best
    const newGlobalBest =
    /* istanbul ignore next */
    (cov_161daubuf().s[86]++, this.findGlobalBest(this.swarmState.particles));
    /* istanbul ignore next */
    cov_161daubuf().s[87]++;
    if (newGlobalBest.fitness < this.swarmState.globalBest.fitness) {
      /* istanbul ignore next */
      cov_161daubuf().b[10][0]++;
      cov_161daubuf().s[88]++;
      this.swarmState.globalBest = newGlobalBest;
      /* istanbul ignore next */
      cov_161daubuf().s[89]++;
      this.updateBestSolution(problem);
    } else
    /* istanbul ignore next */
    {
      cov_161daubuf().b[10][1]++;
    }
    // Update swarm statistics
    cov_161daubuf().s[90]++;
    this.swarmState.averageFitness = this.calculateAverageFitness(this.swarmState.particles);
    /* istanbul ignore next */
    cov_161daubuf().s[91]++;
    this.swarmState.diversityIndex = this.calculateDiversityIndex(this.swarmState.particles);
    /* istanbul ignore next */
    cov_161daubuf().s[92]++;
    this.swarmState.iteration++;
  }
  /**
   * Update particle velocity
   */
  updateParticleVelocity(particle, problem) {
    /* istanbul ignore next */
    cov_161daubuf().f[11]++;
    const [c1, c2] =
    /* istanbul ignore next */
    (cov_161daubuf().s[93]++, this.parameters.accelerationCoefficients);
    const w =
    /* istanbul ignore next */
    (cov_161daubuf().s[94]++, this.getCurrentInertiaWeight());
    // Find neighborhood best
    const neighborhoodBest =
    /* istanbul ignore next */
    (cov_161daubuf().s[95]++, this.findNeighborhoodBest(particle));
    /* istanbul ignore next */
    cov_161daubuf().s[96]++;
    for (let i =
    /* istanbul ignore next */
    (cov_161daubuf().s[97]++, 0); i < particle.velocity.length; i++) {
      /* istanbul ignore next */
      cov_161daubuf().s[98]++;
      if (typeof particle.position[i] === 'number') {
        /* istanbul ignore next */
        cov_161daubuf().b[11][0]++;
        const currentPos =
        /* istanbul ignore next */
        (cov_161daubuf().s[99]++, particle.position[i]);
        const personalBestPos =
        /* istanbul ignore next */
        (cov_161daubuf().s[100]++, particle.personalBest.position[i]);
        const neighborhoodBestPos =
        /* istanbul ignore next */
        (cov_161daubuf().s[101]++, neighborhoodBest.position[i]);
        // PSO velocity update equation
        const cognitive =
        /* istanbul ignore next */
        (cov_161daubuf().s[102]++, c1 * this.random() * (personalBestPos - currentPos));
        const social =
        /* istanbul ignore next */
        (cov_161daubuf().s[103]++, c2 * this.random() * (neighborhoodBestPos - currentPos));
        /* istanbul ignore next */
        cov_161daubuf().s[104]++;
        particle.velocity[i] = w * particle.velocity[i] + cognitive + social;
        // Velocity clamping
        const variable =
        /* istanbul ignore next */
        (cov_161daubuf().s[105]++, problem.variables[i]);
        const min =
        /* istanbul ignore next */
        (cov_161daubuf().s[106]++, typeof variable.bounds.minimum === 'number' ?
        /* istanbul ignore next */
        (cov_161daubuf().b[12][0]++, variable.bounds.minimum) :
        /* istanbul ignore next */
        (cov_161daubuf().b[12][1]++, 0));
        const max =
        /* istanbul ignore next */
        (cov_161daubuf().s[107]++, typeof variable.bounds.maximum === 'number' ?
        /* istanbul ignore next */
        (cov_161daubuf().b[13][0]++, variable.bounds.maximum) :
        /* istanbul ignore next */
        (cov_161daubuf().b[13][1]++, 1));
        const maxVel =
        /* istanbul ignore next */
        (cov_161daubuf().s[108]++, this.parameters.maxVelocity * (max - min));
        /* istanbul ignore next */
        cov_161daubuf().s[109]++;
        particle.velocity[i] = Math.max(-maxVel, Math.min(maxVel, particle.velocity[i]));
      } else
      /* istanbul ignore next */
      {
        cov_161daubuf().b[11][1]++;
      }
    }
  }
  /**
   * Update particle position
   */
  updateParticlePosition(particle, problem) {
    /* istanbul ignore next */
    cov_161daubuf().f[12]++;
    cov_161daubuf().s[110]++;
    for (let i =
    /* istanbul ignore next */
    (cov_161daubuf().s[111]++, 0); i < particle.position.length; i++) {
      const variable =
      /* istanbul ignore next */
      (cov_161daubuf().s[112]++, problem.variables[i]);
      /* istanbul ignore next */
      cov_161daubuf().s[113]++;
      if (
      /* istanbul ignore next */
      (cov_161daubuf().b[15][0]++, variable.discreteValues) &&
      /* istanbul ignore next */
      (cov_161daubuf().b[15][1]++, variable.discreteValues.length > 0)) {
        /* istanbul ignore next */
        cov_161daubuf().b[14][0]++;
        cov_161daubuf().s[114]++;
        // Discrete variable - probabilistic update
        if (this.random() < 0.1) {
          /* istanbul ignore next */
          cov_161daubuf().b[16][0]++;
          // 10% chance to change
          const randomIndex =
          /* istanbul ignore next */
          (cov_161daubuf().s[115]++, Math.floor(this.random() * variable.discreteValues.length));
          /* istanbul ignore next */
          cov_161daubuf().s[116]++;
          particle.position[i] = variable.discreteValues[randomIndex];
        } else
        /* istanbul ignore next */
        {
          cov_161daubuf().b[16][1]++;
        }
      } else {
        /* istanbul ignore next */
        cov_161daubuf().b[14][1]++;
        cov_161daubuf().s[117]++;
        if (typeof particle.position[i] === 'number') {
          /* istanbul ignore next */
          cov_161daubuf().b[17][0]++;
          // Continuous variable
          const newPos =
          /* istanbul ignore next */
          (cov_161daubuf().s[118]++, particle.position[i] + particle.velocity[i]);
          // Boundary handling
          const min =
          /* istanbul ignore next */
          (cov_161daubuf().s[119]++, typeof variable.bounds.minimum === 'number' ?
          /* istanbul ignore next */
          (cov_161daubuf().b[18][0]++, variable.bounds.minimum) :
          /* istanbul ignore next */
          (cov_161daubuf().b[18][1]++, 0));
          const max =
          /* istanbul ignore next */
          (cov_161daubuf().s[120]++, typeof variable.bounds.maximum === 'number' ?
          /* istanbul ignore next */
          (cov_161daubuf().b[19][0]++, variable.bounds.maximum) :
          /* istanbul ignore next */
          (cov_161daubuf().b[19][1]++, 1));
          /* istanbul ignore next */
          cov_161daubuf().s[121]++;
          particle.position[i] = this.handleBoundary(newPos, min, max, particle.velocity[i], i);
        } else
        /* istanbul ignore next */
        {
          cov_161daubuf().b[17][1]++;
        }
      }
    }
  }
  /**
   * Handle boundary violations
   */
  handleBoundary(position, min, max, velocity, index) {
    /* istanbul ignore next */
    cov_161daubuf().f[13]++;
    cov_161daubuf().s[122]++;
    switch (this.parameters.boundaryHandling) {
      case 'reflect':
        /* istanbul ignore next */
        cov_161daubuf().b[20][0]++;
        cov_161daubuf().s[123]++;
        if (position < min) {
          /* istanbul ignore next */
          cov_161daubuf().b[21][0]++;
          cov_161daubuf().s[124]++;
          return min + (min - position);
        } else {
          /* istanbul ignore next */
          cov_161daubuf().b[21][1]++;
          cov_161daubuf().s[125]++;
          if (position > max) {
            /* istanbul ignore next */
            cov_161daubuf().b[22][0]++;
            cov_161daubuf().s[126]++;
            return max - (position - max);
          } else
          /* istanbul ignore next */
          {
            cov_161daubuf().b[22][1]++;
          }
        }
        /* istanbul ignore next */
        cov_161daubuf().s[127]++;
        return position;
      case 'absorb':
        /* istanbul ignore next */
        cov_161daubuf().b[20][1]++;
        cov_161daubuf().s[128]++;
        return Math.max(min, Math.min(max, position));
      case 'invisible':
        /* istanbul ignore next */
        cov_161daubuf().b[20][2]++;
        cov_161daubuf().s[129]++;
        // Keep position but set velocity to zero if boundary violated
        if (
        /* istanbul ignore next */
        (cov_161daubuf().b[24][0]++, position < min) ||
        /* istanbul ignore next */
        (cov_161daubuf().b[24][1]++, position > max)) {
          /* istanbul ignore next */
          cov_161daubuf().b[23][0]++;
          cov_161daubuf().s[130]++;
          return Math.max(min, Math.min(max, position));
        } else
        /* istanbul ignore next */
        {
          cov_161daubuf().b[23][1]++;
        }
        cov_161daubuf().s[131]++;
        return position;
      case 'random':
        /* istanbul ignore next */
        cov_161daubuf().b[20][3]++;
        cov_161daubuf().s[132]++;
        if (
        /* istanbul ignore next */
        (cov_161daubuf().b[26][0]++, position < min) ||
        /* istanbul ignore next */
        (cov_161daubuf().b[26][1]++, position > max)) {
          /* istanbul ignore next */
          cov_161daubuf().b[25][0]++;
          cov_161daubuf().s[133]++;
          return min + this.random() * (max - min);
        } else
        /* istanbul ignore next */
        {
          cov_161daubuf().b[25][1]++;
        }
        cov_161daubuf().s[134]++;
        return position;
      default:
        /* istanbul ignore next */
        cov_161daubuf().b[20][4]++;
        cov_161daubuf().s[135]++;
        return Math.max(min, Math.min(max, position));
    }
  }
  /**
   * Find global best particle
   */
  findGlobalBest(particles) {
    /* istanbul ignore next */
    cov_161daubuf().f[14]++;
    const best =
    /* istanbul ignore next */
    (cov_161daubuf().s[136]++, particles.reduce((best, current) => {
      /* istanbul ignore next */
      cov_161daubuf().f[15]++;
      cov_161daubuf().s[137]++;
      return current.fitness < best.fitness ?
      /* istanbul ignore next */
      (cov_161daubuf().b[27][0]++, current) :
      /* istanbul ignore next */
      (cov_161daubuf().b[27][1]++, best);
    }));
    /* istanbul ignore next */
    cov_161daubuf().s[138]++;
    return {
      position: [...best.position],
      fitness: best.fitness,
      particleId: best.id
    };
  }
  /**
   * Find neighborhood best for a particle
   */
  findNeighborhoodBest(particle) {
    /* istanbul ignore next */
    cov_161daubuf().f[16]++;
    cov_161daubuf().s[139]++;
    if (!this.swarmState) {
      /* istanbul ignore next */
      cov_161daubuf().b[28][0]++;
      cov_161daubuf().s[140]++;
      return {
        position: particle.position,
        fitness: particle.fitness
      };
    } else
    /* istanbul ignore next */
    {
      cov_161daubuf().b[28][1]++;
    }
    cov_161daubuf().s[141]++;
    switch (this.parameters.topology) {
      case 'global':
        /* istanbul ignore next */
        cov_161daubuf().b[29][0]++;
        cov_161daubuf().s[142]++;
        return this.swarmState.globalBest;
      case 'local':
        /* istanbul ignore next */
        cov_161daubuf().b[29][1]++;
      case 'ring':
        /* istanbul ignore next */
        cov_161daubuf().b[29][2]++;
        // Find best among neighbors
        let best =
        /* istanbul ignore next */
        (cov_161daubuf().s[143]++, particle.personalBest);
        /* istanbul ignore next */
        cov_161daubuf().s[144]++;
        for (const neighborId of particle.neighbors) {
          const neighbor =
          /* istanbul ignore next */
          (cov_161daubuf().s[145]++, this.swarmState.particles.find(p => {
            /* istanbul ignore next */
            cov_161daubuf().f[17]++;
            cov_161daubuf().s[146]++;
            return p.id === neighborId;
          }));
          /* istanbul ignore next */
          cov_161daubuf().s[147]++;
          if (
          /* istanbul ignore next */
          (cov_161daubuf().b[31][0]++, neighbor) &&
          /* istanbul ignore next */
          (cov_161daubuf().b[31][1]++, neighbor.personalBest.fitness < best.fitness)) {
            /* istanbul ignore next */
            cov_161daubuf().b[30][0]++;
            cov_161daubuf().s[148]++;
            best = neighbor.personalBest;
          } else
          /* istanbul ignore next */
          {
            cov_161daubuf().b[30][1]++;
          }
        }
        /* istanbul ignore next */
        cov_161daubuf().s[149]++;
        return best;
      default:
        /* istanbul ignore next */
        cov_161daubuf().b[29][3]++;
        cov_161daubuf().s[150]++;
        return this.swarmState.globalBest;
    }
  }
  /**
   * Setup neighborhood topology
   */
  setupTopology() {
    /* istanbul ignore next */
    cov_161daubuf().f[18]++;
    cov_161daubuf().s[151]++;
    if (!this.swarmState) {
      /* istanbul ignore next */
      cov_161daubuf().b[32][0]++;
      cov_161daubuf().s[152]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_161daubuf().b[32][1]++;
    }
    const particles =
    /* istanbul ignore next */
    (cov_161daubuf().s[153]++, this.swarmState.particles);
    /* istanbul ignore next */
    cov_161daubuf().s[154]++;
    switch (this.parameters.topology) {
      case 'ring':
        /* istanbul ignore next */
        cov_161daubuf().b[33][0]++;
        cov_161daubuf().s[155]++;
        for (let i =
        /* istanbul ignore next */
        (cov_161daubuf().s[156]++, 0); i < particles.length; i++) {
          const prev =
          /* istanbul ignore next */
          (cov_161daubuf().s[157]++, (i - 1 + particles.length) % particles.length);
          const next =
          /* istanbul ignore next */
          (cov_161daubuf().s[158]++, (i + 1) % particles.length);
          /* istanbul ignore next */
          cov_161daubuf().s[159]++;
          particles[i].neighbors = [particles[prev].id, particles[next].id];
        }
        /* istanbul ignore next */
        cov_161daubuf().s[160]++;
        break;
      case 'local':
        /* istanbul ignore next */
        cov_161daubuf().b[33][1]++;
        cov_161daubuf().s[161]++;
        for (let i =
        /* istanbul ignore next */
        (cov_161daubuf().s[162]++, 0); i < particles.length; i++) {
          const neighbors =
          /* istanbul ignore next */
          (cov_161daubuf().s[163]++, []);
          /* istanbul ignore next */
          cov_161daubuf().s[164]++;
          for (let j =
          /* istanbul ignore next */
          (cov_161daubuf().s[165]++, 0);
          /* istanbul ignore next */
          (cov_161daubuf().b[34][0]++, j < this.parameters.neighborhoodSize) &&
          /* istanbul ignore next */
          (cov_161daubuf().b[34][1]++, j < particles.length); j++) {
            const neighborIndex =
            /* istanbul ignore next */
            (cov_161daubuf().s[166]++, (i + j + 1) % particles.length);
            /* istanbul ignore next */
            cov_161daubuf().s[167]++;
            if (neighborIndex !== i) {
              /* istanbul ignore next */
              cov_161daubuf().b[35][0]++;
              cov_161daubuf().s[168]++;
              neighbors.push(particles[neighborIndex].id);
            } else
            /* istanbul ignore next */
            {
              cov_161daubuf().b[35][1]++;
            }
          }
          /* istanbul ignore next */
          cov_161daubuf().s[169]++;
          particles[i].neighbors = neighbors;
        }
        /* istanbul ignore next */
        cov_161daubuf().s[170]++;
        break;
      case 'star':
        /* istanbul ignore next */
        cov_161daubuf().b[33][2]++;
        // All particles connected to best particle
        const bestParticle =
        /* istanbul ignore next */
        (cov_161daubuf().s[171]++, this.findGlobalBest(particles));
        /* istanbul ignore next */
        cov_161daubuf().s[172]++;
        for (const particle of particles) {
          /* istanbul ignore next */
          cov_161daubuf().s[173]++;
          if (particle.id !== bestParticle.particleId) {
            /* istanbul ignore next */
            cov_161daubuf().b[36][0]++;
            cov_161daubuf().s[174]++;
            particle.neighbors = [bestParticle.particleId];
          } else
          /* istanbul ignore next */
          {
            cov_161daubuf().b[36][1]++;
          }
        }
        /* istanbul ignore next */
        cov_161daubuf().s[175]++;
        break;
      case 'random':
        /* istanbul ignore next */
        cov_161daubuf().b[33][3]++;
        cov_161daubuf().s[176]++;
        for (const particle of particles) {
          const neighbors =
          /* istanbul ignore next */
          (cov_161daubuf().s[177]++, []);
          /* istanbul ignore next */
          cov_161daubuf().s[178]++;
          for (let j =
          /* istanbul ignore next */
          (cov_161daubuf().s[179]++, 0); j < this.parameters.neighborhoodSize; j++) {
            const randomIndex =
            /* istanbul ignore next */
            (cov_161daubuf().s[180]++, Math.floor(this.random() * particles.length));
            /* istanbul ignore next */
            cov_161daubuf().s[181]++;
            if (
            /* istanbul ignore next */
            (cov_161daubuf().b[38][0]++, particles[randomIndex].id !== particle.id) &&
            /* istanbul ignore next */
            (cov_161daubuf().b[38][1]++, !neighbors.includes(particles[randomIndex].id))) {
              /* istanbul ignore next */
              cov_161daubuf().b[37][0]++;
              cov_161daubuf().s[182]++;
              neighbors.push(particles[randomIndex].id);
            } else
            /* istanbul ignore next */
            {
              cov_161daubuf().b[37][1]++;
            }
          }
          /* istanbul ignore next */
          cov_161daubuf().s[183]++;
          particle.neighbors = neighbors;
        }
        /* istanbul ignore next */
        cov_161daubuf().s[184]++;
        break;
      default:
        /* istanbul ignore next */
        cov_161daubuf().b[33][4]++;
        cov_161daubuf().s[185]++;
        // global
        // No explicit neighbors - all particles use global best
        break;
    }
  }
  /**
   * Get current inertia weight (adaptive)
   */
  getCurrentInertiaWeight() {
    /* istanbul ignore next */
    cov_161daubuf().f[19]++;
    cov_161daubuf().s[186]++;
    if (
    /* istanbul ignore next */
    (cov_161daubuf().b[40][0]++, !this.parameters.adaptiveParameters) ||
    /* istanbul ignore next */
    (cov_161daubuf().b[40][1]++, !this.swarmState)) {
      /* istanbul ignore next */
      cov_161daubuf().b[39][0]++;
      cov_161daubuf().s[187]++;
      return this.parameters.inertiaWeight;
    } else
    /* istanbul ignore next */
    {
      cov_161daubuf().b[39][1]++;
    }
    // Linear decrease from max to min
    const progress =
    /* istanbul ignore next */
    (cov_161daubuf().s[188]++, this.swarmState.iteration / this.parameters.maxIterations);
    /* istanbul ignore next */
    cov_161daubuf().s[189]++;
    return this.parameters.inertiaWeightMax - progress * (this.parameters.inertiaWeightMax - this.parameters.inertiaWeightMin);
  }
  /**
   * Adapt algorithm parameters
   */
  adaptParameters() {
    /* istanbul ignore next */
    cov_161daubuf().f[20]++;
    cov_161daubuf().s[190]++;
    if (!this.swarmState) {
      /* istanbul ignore next */
      cov_161daubuf().b[41][0]++;
      cov_161daubuf().s[191]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_161daubuf().b[41][1]++;
    }
    // Adapt based on diversity
    cov_161daubuf().s[192]++;
    if (this.swarmState.diversityIndex < 0.1) {
      /* istanbul ignore next */
      cov_161daubuf().b[42][0]++;
      cov_161daubuf().s[193]++;
      // Low diversity - increase exploration
      this.parameters.inertiaWeight = Math.min(0.9, this.parameters.inertiaWeight * 1.1);
      /* istanbul ignore next */
      cov_161daubuf().s[194]++;
      this.parameters.maxVelocity = Math.min(0.5, this.parameters.maxVelocity * 1.1);
    } else {
      /* istanbul ignore next */
      cov_161daubuf().b[42][1]++;
      cov_161daubuf().s[195]++;
      if (this.swarmState.diversityIndex > 0.8) {
        /* istanbul ignore next */
        cov_161daubuf().b[43][0]++;
        cov_161daubuf().s[196]++;
        // High diversity - increase exploitation
        this.parameters.inertiaWeight = Math.max(0.1, this.parameters.inertiaWeight * 0.9);
        /* istanbul ignore next */
        cov_161daubuf().s[197]++;
        this.parameters.maxVelocity = Math.max(0.05, this.parameters.maxVelocity * 0.9);
      } else
      /* istanbul ignore next */
      {
        cov_161daubuf().b[43][1]++;
      }
    }
  }
  /**
   * Maintain swarm diversity
   */
  maintainDiversity(problem) {
    /* istanbul ignore next */
    cov_161daubuf().f[21]++;
    cov_161daubuf().s[198]++;
    if (!this.swarmState) {
      /* istanbul ignore next */
      cov_161daubuf().b[44][0]++;
      cov_161daubuf().s[199]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_161daubuf().b[44][1]++;
    }
    // Replace stagnant particles
    cov_161daubuf().s[200]++;
    for (const particle of this.swarmState.particles) {
      /* istanbul ignore next */
      cov_161daubuf().s[201]++;
      if (particle.stagnationCount > 20) {
        /* istanbul ignore next */
        cov_161daubuf().b[45][0]++;
        // Reinitialize particle
        const newParticle =
        /* istanbul ignore next */
        (cov_161daubuf().s[202]++, this.createRandomParticle(problem));
        /* istanbul ignore next */
        cov_161daubuf().s[203]++;
        Object.assign(particle, newParticle);
      } else
      /* istanbul ignore next */
      {
        cov_161daubuf().b[45][1]++;
      }
    }
  }
  /**
   * Calculate average fitness
   */
  calculateAverageFitness(particles) {
    /* istanbul ignore next */
    cov_161daubuf().f[22]++;
    const validFitnesses =
    /* istanbul ignore next */
    (cov_161daubuf().s[204]++, particles.map(p => {
      /* istanbul ignore next */
      cov_161daubuf().f[23]++;
      cov_161daubuf().s[205]++;
      return p.fitness;
    }).filter(f => {
      /* istanbul ignore next */
      cov_161daubuf().f[24]++;
      cov_161daubuf().s[206]++;
      return f !== Number.MAX_VALUE;
    }));
    /* istanbul ignore next */
    cov_161daubuf().s[207]++;
    return validFitnesses.length > 0 ?
    /* istanbul ignore next */
    (cov_161daubuf().b[46][0]++, validFitnesses.reduce((sum, f) => {
      /* istanbul ignore next */
      cov_161daubuf().f[25]++;
      cov_161daubuf().s[208]++;
      return sum + f;
    }, 0) / validFitnesses.length) :
    /* istanbul ignore next */
    (cov_161daubuf().b[46][1]++, Number.MAX_VALUE);
  }
  /**
   * Calculate diversity index
   */
  calculateDiversityIndex(particles) {
    /* istanbul ignore next */
    cov_161daubuf().f[26]++;
    cov_161daubuf().s[209]++;
    if (particles.length < 2) {
      /* istanbul ignore next */
      cov_161daubuf().b[47][0]++;
      cov_161daubuf().s[210]++;
      return 0;
    } else
    /* istanbul ignore next */
    {
      cov_161daubuf().b[47][1]++;
    }
    let totalDistance =
    /* istanbul ignore next */
    (cov_161daubuf().s[211]++, 0);
    let pairCount =
    /* istanbul ignore next */
    (cov_161daubuf().s[212]++, 0);
    /* istanbul ignore next */
    cov_161daubuf().s[213]++;
    for (let i =
    /* istanbul ignore next */
    (cov_161daubuf().s[214]++, 0); i < particles.length; i++) {
      /* istanbul ignore next */
      cov_161daubuf().s[215]++;
      for (let j =
      /* istanbul ignore next */
      (cov_161daubuf().s[216]++, i + 1); j < particles.length; j++) {
        const distance =
        /* istanbul ignore next */
        (cov_161daubuf().s[217]++, this.calculateDistance(particles[i], particles[j]));
        /* istanbul ignore next */
        cov_161daubuf().s[218]++;
        totalDistance += distance;
        /* istanbul ignore next */
        cov_161daubuf().s[219]++;
        pairCount++;
      }
    }
    /* istanbul ignore next */
    cov_161daubuf().s[220]++;
    return pairCount > 0 ?
    /* istanbul ignore next */
    (cov_161daubuf().b[48][0]++, totalDistance / pairCount) :
    /* istanbul ignore next */
    (cov_161daubuf().b[48][1]++, 0);
  }
  /**
   * Calculate distance between particles
   */
  calculateDistance(particle1, particle2) {
    /* istanbul ignore next */
    cov_161daubuf().f[27]++;
    let distance =
    /* istanbul ignore next */
    (cov_161daubuf().s[221]++, 0);
    /* istanbul ignore next */
    cov_161daubuf().s[222]++;
    for (let i =
    /* istanbul ignore next */
    (cov_161daubuf().s[223]++, 0); i < particle1.position.length; i++) {
      /* istanbul ignore next */
      cov_161daubuf().s[224]++;
      if (
      /* istanbul ignore next */
      (cov_161daubuf().b[50][0]++, typeof particle1.position[i] === 'number') &&
      /* istanbul ignore next */
      (cov_161daubuf().b[50][1]++, typeof particle2.position[i] === 'number')) {
        /* istanbul ignore next */
        cov_161daubuf().b[49][0]++;
        const diff =
        /* istanbul ignore next */
        (cov_161daubuf().s[225]++, particle1.position[i] - particle2.position[i]);
        /* istanbul ignore next */
        cov_161daubuf().s[226]++;
        distance += diff * diff;
      } else
      /* istanbul ignore next */
      {
        cov_161daubuf().b[49][1]++;
      }
    }
    /* istanbul ignore next */
    cov_161daubuf().s[227]++;
    return Math.sqrt(distance);
  }
  /**
   * Update best solution
   */
  updateBestSolution(problem) {
    /* istanbul ignore next */
    cov_161daubuf().f[28]++;
    cov_161daubuf().s[228]++;
    if (!this.swarmState) {
      /* istanbul ignore next */
      cov_161daubuf().b[51][0]++;
      cov_161daubuf().s[229]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_161daubuf().b[51][1]++;
    }
    const bestParticle =
    /* istanbul ignore next */
    (cov_161daubuf().s[230]++, this.swarmState.particles.find(p => {
      /* istanbul ignore next */
      cov_161daubuf().f[29]++;
      cov_161daubuf().s[231]++;
      return p.id === this.swarmState.globalBest.particleId;
    }));
    /* istanbul ignore next */
    cov_161daubuf().s[232]++;
    if (!bestParticle) {
      /* istanbul ignore next */
      cov_161daubuf().b[52][0]++;
      cov_161daubuf().s[233]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_161daubuf().b[52][1]++;
    }
    const variables =
    /* istanbul ignore next */
    (cov_161daubuf().s[234]++, {});
    /* istanbul ignore next */
    cov_161daubuf().s[235]++;
    problem.variables.forEach((variable, index) => {
      /* istanbul ignore next */
      cov_161daubuf().f[30]++;
      cov_161daubuf().s[236]++;
      variables[variable.id] = bestParticle.position[index];
    });
    /* istanbul ignore next */
    cov_161daubuf().s[237]++;
    this.bestSolution = {
      id: `pso_best_${Date.now()}`,
      variables,
      objectiveValues: {},
      constraintViolations: [],
      feasible: true,
      fitness: bestParticle.fitness,
      systemConfiguration: problem.systemConfiguration,
      performanceMetrics: {}
    };
  }
  /**
   * Check termination criteria
   */
  shouldTerminate(problem) {
    /* istanbul ignore next */
    cov_161daubuf().f[31]++;
    cov_161daubuf().s[238]++;
    if (!this.swarmState) {
      /* istanbul ignore next */
      cov_161daubuf().b[53][0]++;
      cov_161daubuf().s[239]++;
      return true;
    } else
    /* istanbul ignore next */
    {
      cov_161daubuf().b[53][1]++;
    }
    // Maximum iterations
    cov_161daubuf().s[240]++;
    if (this.swarmState.iteration >= this.parameters.maxIterations) {
      /* istanbul ignore next */
      cov_161daubuf().b[54][0]++;
      cov_161daubuf().s[241]++;
      return true;
    } else
    /* istanbul ignore next */
    {
      cov_161daubuf().b[54][1]++;
    }
    // Convergence check
    cov_161daubuf().s[242]++;
    if (this.history.length >= 20) {
      /* istanbul ignore next */
      cov_161daubuf().b[55][0]++;
      const recentHistory =
      /* istanbul ignore next */
      (cov_161daubuf().s[243]++, this.history.slice(-20));
      const fitnessImprovement =
      /* istanbul ignore next */
      (cov_161daubuf().s[244]++, recentHistory[0].bestFitness - recentHistory[recentHistory.length - 1].bestFitness);
      /* istanbul ignore next */
      cov_161daubuf().s[245]++;
      if (Math.abs(fitnessImprovement) < problem.convergenceCriteria.toleranceValue) {
        /* istanbul ignore next */
        cov_161daubuf().b[56][0]++;
        cov_161daubuf().s[246]++;
        return true;
      } else
      /* istanbul ignore next */
      {
        cov_161daubuf().b[56][1]++;
      }
    } else
    /* istanbul ignore next */
    {
      cov_161daubuf().b[55][1]++;
    }
    cov_161daubuf().s[247]++;
    return false;
  }
  /**
   * Update optimization history
   */
  updateHistory() {
    /* istanbul ignore next */
    cov_161daubuf().f[32]++;
    cov_161daubuf().s[248]++;
    if (!this.swarmState) {
      /* istanbul ignore next */
      cov_161daubuf().b[57][0]++;
      cov_161daubuf().s[249]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_161daubuf().b[57][1]++;
    }
    const feasibleParticles =
    /* istanbul ignore next */
    (cov_161daubuf().s[250]++, this.swarmState.particles.filter(p => {
      /* istanbul ignore next */
      cov_161daubuf().f[33]++;
      cov_161daubuf().s[251]++;
      return p.fitness !== Number.MAX_VALUE;
    }));
    const fitnesses =
    /* istanbul ignore next */
    (cov_161daubuf().s[252]++, feasibleParticles.map(p => {
      /* istanbul ignore next */
      cov_161daubuf().f[34]++;
      cov_161daubuf().s[253]++;
      return p.fitness;
    }));
    /* istanbul ignore next */
    cov_161daubuf().s[254]++;
    if (fitnesses.length === 0) {
      /* istanbul ignore next */
      cov_161daubuf().b[58][0]++;
      cov_161daubuf().s[255]++;
      fitnesses.push(Number.MAX_VALUE);
    } else
    /* istanbul ignore next */
    {
      cov_161daubuf().b[58][1]++;
    }
    const history =
    /* istanbul ignore next */
    (cov_161daubuf().s[256]++, {
      iteration: this.swarmState.iteration,
      bestFitness: Math.min(...fitnesses),
      averageFitness: this.swarmState.averageFitness,
      worstFitness: Math.max(...fitnesses),
      diversity: this.swarmState.diversityIndex,
      constraintViolations: this.swarmState.particles.filter(p => {
        /* istanbul ignore next */
        cov_161daubuf().f[35]++;
        cov_161daubuf().s[257]++;
        return p.fitness === Number.MAX_VALUE;
      }).length,
      timestamp: new Date()
    });
    /* istanbul ignore next */
    cov_161daubuf().s[258]++;
    this.history.push(history);
  }
  /**
   * Create optimization result
   */
  createOptimizationResult(problem, startTime) {
    /* istanbul ignore next */
    cov_161daubuf().f[36]++;
    const executionTime =
    /* istanbul ignore next */
    (cov_161daubuf().s[259]++, performance.now() - startTime);
    const statistics =
    /* istanbul ignore next */
    (cov_161daubuf().s[260]++, {
      totalIterations:
      /* istanbul ignore next */
      (cov_161daubuf().b[59][0]++, this.swarmState?.iteration) ||
      /* istanbul ignore next */
      (cov_161daubuf().b[59][1]++, 0),
      totalEvaluations: this.evaluationCount,
      convergenceIteration:
      /* istanbul ignore next */
      (cov_161daubuf().b[60][0]++, this.swarmState?.iteration) ||
      /* istanbul ignore next */
      (cov_161daubuf().b[60][1]++, 0),
      executionTime,
      bestFitnessHistory: this.history.map(h => {
        /* istanbul ignore next */
        cov_161daubuf().f[37]++;
        cov_161daubuf().s[261]++;
        return h.bestFitness;
      }),
      averageFitnessHistory: this.history.map(h => {
        /* istanbul ignore next */
        cov_161daubuf().f[38]++;
        cov_161daubuf().s[262]++;
        return h.averageFitness;
      }),
      diversityHistory: this.history.map(h => {
        /* istanbul ignore next */
        cov_161daubuf().f[39]++;
        cov_161daubuf().s[263]++;
        return h.diversity;
      }),
      constraintViolationHistory: this.history.map(h => {
        /* istanbul ignore next */
        cov_161daubuf().f[40]++;
        cov_161daubuf().s[264]++;
        return h.constraintViolations;
      }),
      algorithmSpecificStats: {
        swarmSize: this.parameters.swarmSize,
        finalInertiaWeight: this.getCurrentInertiaWeight(),
        finalDiversityIndex:
        /* istanbul ignore next */
        (cov_161daubuf().b[61][0]++, this.swarmState?.diversityIndex) ||
        /* istanbul ignore next */
        (cov_161daubuf().b[61][1]++, 0)
      }
    });
    const optimizationHistory =
    /* istanbul ignore next */
    (cov_161daubuf().s[265]++, {
      iterations: this.history,
      populationHistory: this.populationHistory,
      parameterHistory: [],
      convergenceMetrics: []
    });
    /* istanbul ignore next */
    cov_161daubuf().s[266]++;
    return {
      problemId: problem.id,
      status: SystemOptimizationTypes_1.OptimizationStatus.CONVERGED,
      bestSolution: this.bestSolution,
      statistics,
      history: optimizationHistory,
      analysis: {},
      recommendations: [],
      warnings: [],
      errors: []
    };
  }
  // Utility methods
  generateParticleId() {
    /* istanbul ignore next */
    cov_161daubuf().f[41]++;
    cov_161daubuf().s[267]++;
    return `pso_particle_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  createSeededRandom(seed) {
    /* istanbul ignore next */
    cov_161daubuf().f[42]++;
    let state =
    /* istanbul ignore next */
    (cov_161daubuf().s[268]++, seed);
    /* istanbul ignore next */
    cov_161daubuf().s[269]++;
    return () => {
      /* istanbul ignore next */
      cov_161daubuf().f[43]++;
      cov_161daubuf().s[270]++;
      state = (state * 9301 + 49297) % 233280;
      /* istanbul ignore next */
      cov_161daubuf().s[271]++;
      return state / 233280;
    };
  }
}
/* istanbul ignore next */
cov_161daubuf().s[272]++;
exports.ParticleSwarmOptimization = ParticleSwarmOptimization;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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