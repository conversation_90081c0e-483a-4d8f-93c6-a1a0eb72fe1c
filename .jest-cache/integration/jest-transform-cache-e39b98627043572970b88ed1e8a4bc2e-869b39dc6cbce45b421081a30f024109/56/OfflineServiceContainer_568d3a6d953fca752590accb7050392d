b13067284975184625880a12b5ca4763
"use strict";

/**
 * Offline Service Container
 *
 * Real service implementations for Phase 1 offline desktop mode.
 * Connects to local SQLite database and provides business logic services.
 *
 * @see docs/refactoring/component-architecture-specification.md
 */
/* istanbul ignore next */
function cov_p8luzfsbt() {
  var path = "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\frontend\\lib\\services\\OfflineServiceContainer.ts";
  var hash = "291f28f295ce597a174de1b2cf3f3246b180133a";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\frontend\\lib\\services\\OfflineServiceContainer.ts",
    statementMap: {
      "0": {
        start: {
          line: 10,
          column: 0
        },
        end: {
          line: 10,
          column: 62
        }
      },
      "1": {
        start: {
          line: 11,
          column: 0
        },
        end: {
          line: 11,
          column: 199
        }
      },
      "2": {
        start: {
          line: 12,
          column: 0
        },
        end: {
          line: 12,
          column: 70
        }
      },
      "3": {
        start: {
          line: 13,
          column: 25
        },
        end: {
          line: 13,
          column: 62
        }
      },
      "4": {
        start: {
          line: 14,
          column: 30
        },
        end: {
          line: 14,
          column: 72
        }
      },
      "5": {
        start: {
          line: 20,
          column: 8
        },
        end: {
          line: 20,
          column: 41
        }
      },
      "6": {
        start: {
          line: 23,
          column: 8
        },
        end: {
          line: 29,
          column: 9
        }
      },
      "7": {
        start: {
          line: 24,
          column: 12
        },
        end: {
          line: 24,
          column: 76
        }
      },
      "8": {
        start: {
          line: 27,
          column: 12
        },
        end: {
          line: 27,
          column: 59
        }
      },
      "9": {
        start: {
          line: 28,
          column: 12
        },
        end: {
          line: 28,
          column: 24
        }
      },
      "10": {
        start: {
          line: 32,
          column: 8
        },
        end: {
          line: 38,
          column: 9
        }
      },
      "11": {
        start: {
          line: 33,
          column: 12
        },
        end: {
          line: 33,
          column: 75
        }
      },
      "12": {
        start: {
          line: 36,
          column: 12
        },
        end: {
          line: 36,
          column: 60
        }
      },
      "13": {
        start: {
          line: 37,
          column: 12
        },
        end: {
          line: 37,
          column: 24
        }
      },
      "14": {
        start: {
          line: 41,
          column: 8
        },
        end: {
          line: 83,
          column: 9
        }
      },
      "15": {
        start: {
          line: 42,
          column: 31
        },
        end: {
          line: 76,
          column: 13
        }
      },
      "16": {
        start: {
          line: 77,
          column: 12
        },
        end: {
          line: 77,
          column: 78
        }
      },
      "17": {
        start: {
          line: 78,
          column: 12
        },
        end: {
          line: 78,
          column: 30
        }
      },
      "18": {
        start: {
          line: 81,
          column: 12
        },
        end: {
          line: 81,
          column: 62
        }
      },
      "19": {
        start: {
          line: 82,
          column: 12
        },
        end: {
          line: 82,
          column: 24
        }
      },
      "20": {
        start: {
          line: 86,
          column: 8
        },
        end: {
          line: 92,
          column: 9
        }
      },
      "21": {
        start: {
          line: 87,
          column: 12
        },
        end: {
          line: 87,
          column: 72
        }
      },
      "22": {
        start: {
          line: 90,
          column: 12
        },
        end: {
          line: 90,
          column: 62
        }
      },
      "23": {
        start: {
          line: 91,
          column: 12
        },
        end: {
          line: 91,
          column: 24
        }
      },
      "24": {
        start: {
          line: 95,
          column: 8
        },
        end: {
          line: 101,
          column: 9
        }
      },
      "25": {
        start: {
          line: 96,
          column: 12
        },
        end: {
          line: 96,
          column: 87
        }
      },
      "26": {
        start: {
          line: 99,
          column: 12
        },
        end: {
          line: 99,
          column: 61
        }
      },
      "27": {
        start: {
          line: 100,
          column: 12
        },
        end: {
          line: 100,
          column: 24
        }
      },
      "28": {
        start: {
          line: 104,
          column: 8
        },
        end: {
          line: 108,
          column: 11
        }
      },
      "29": {
        start: {
          line: 105,
          column: 22
        },
        end: {
          line: 105,
          column: 44
        }
      },
      "30": {
        start: {
          line: 106,
          column: 22
        },
        end: {
          line: 106,
          column: 52
        }
      },
      "31": {
        start: {
          line: 107,
          column: 12
        },
        end: {
          line: 107,
          column: 34
        }
      },
      "32": {
        start: {
          line: 111,
          column: 26
        },
        end: {
          line: 111,
          column: 57
        }
      },
      "33": {
        start: {
          line: 112,
          column: 8
        },
        end: {
          line: 112,
          column: 34
        }
      },
      "34": {
        start: {
          line: 115,
          column: 0
        },
        end: {
          line: 115,
          column: 54
        }
      },
      "35": {
        start: {
          line: 121,
          column: 8
        },
        end: {
          line: 121,
          column: 41
        }
      },
      "36": {
        start: {
          line: 124,
          column: 8
        },
        end: {
          line: 130,
          column: 9
        }
      },
      "37": {
        start: {
          line: 125,
          column: 12
        },
        end: {
          line: 125,
          column: 75
        }
      },
      "38": {
        start: {
          line: 128,
          column: 12
        },
        end: {
          line: 128,
          column: 64
        }
      },
      "39": {
        start: {
          line: 129,
          column: 12
        },
        end: {
          line: 129,
          column: 24
        }
      },
      "40": {
        start: {
          line: 133,
          column: 8
        },
        end: {
          line: 139,
          column: 9
        }
      },
      "41": {
        start: {
          line: 134,
          column: 12
        },
        end: {
          line: 134,
          column: 70
        }
      },
      "42": {
        start: {
          line: 137,
          column: 12
        },
        end: {
          line: 137,
          column: 56
        }
      },
      "43": {
        start: {
          line: 138,
          column: 12
        },
        end: {
          line: 138,
          column: 24
        }
      },
      "44": {
        start: {
          line: 142,
          column: 8
        },
        end: {
          line: 148,
          column: 9
        }
      },
      "45": {
        start: {
          line: 143,
          column: 12
        },
        end: {
          line: 143,
          column: 66
        }
      },
      "46": {
        start: {
          line: 146,
          column: 12
        },
        end: {
          line: 146,
          column: 59
        }
      },
      "47": {
        start: {
          line: 147,
          column: 12
        },
        end: {
          line: 147,
          column: 24
        }
      },
      "48": {
        start: {
          line: 151,
          column: 0
        },
        end: {
          line: 151,
          column: 48
        }
      },
      "49": {
        start: {
          line: 156,
          column: 8
        },
        end: {
          line: 156,
          column: 77
        }
      },
      "50": {
        start: {
          line: 160,
          column: 8
        },
        end: {
          line: 160,
          column: 77
        }
      },
      "51": {
        start: {
          line: 166,
          column: 8
        },
        end: {
          line: 166,
          column: 20
        }
      },
      "52": {
        start: {
          line: 174,
          column: 8
        },
        end: {
          line: 174,
          column: 50
        }
      },
      "53": {
        start: {
          line: 177,
          column: 8
        },
        end: {
          line: 183,
          column: 9
        }
      },
      "54": {
        start: {
          line: 178,
          column: 12
        },
        end: {
          line: 178,
          column: 69
        }
      },
      "55": {
        start: {
          line: 181,
          column: 12
        },
        end: {
          line: 181,
          column: 69
        }
      },
      "56": {
        start: {
          line: 182,
          column: 12
        },
        end: {
          line: 182,
          column: 24
        }
      },
      "57": {
        start: {
          line: 186,
          column: 8
        },
        end: {
          line: 197,
          column: 9
        }
      },
      "58": {
        start: {
          line: 188,
          column: 12
        },
        end: {
          line: 192,
          column: 14
        }
      },
      "59": {
        start: {
          line: 195,
          column: 12
        },
        end: {
          line: 195,
          column: 64
        }
      },
      "60": {
        start: {
          line: 196,
          column: 12
        },
        end: {
          line: 196,
          column: 24
        }
      },
      "61": {
        start: {
          line: 200,
          column: 8
        },
        end: {
          line: 208,
          column: 9
        }
      },
      "62": {
        start: {
          line: 203,
          column: 12
        },
        end: {
          line: 203,
          column: 22
        }
      },
      "63": {
        start: {
          line: 206,
          column: 12
        },
        end: {
          line: 206,
          column: 71
        }
      },
      "64": {
        start: {
          line: 207,
          column: 12
        },
        end: {
          line: 207,
          column: 24
        }
      },
      "65": {
        start: {
          line: 211,
          column: 0
        },
        end: {
          line: 211,
          column: 62
        }
      },
      "66": {
        start: {
          line: 217,
          column: 8
        },
        end: {
          line: 217,
          column: 53
        }
      },
      "67": {
        start: {
          line: 220,
          column: 8
        },
        end: {
          line: 242,
          column: 9
        }
      },
      "68": {
        start: {
          line: 222,
          column: 27
        },
        end: {
          line: 222,
          column: 29
        }
      },
      "69": {
        start: {
          line: 223,
          column: 29
        },
        end: {
          line: 223,
          column: 31
        }
      },
      "70": {
        start: {
          line: 224,
          column: 12
        },
        end: {
          line: 226,
          column: 13
        }
      },
      "71": {
        start: {
          line: 225,
          column: 16
        },
        end: {
          line: 225,
          column: 56
        }
      },
      "72": {
        start: {
          line: 227,
          column: 12
        },
        end: {
          line: 229,
          column: 13
        }
      },
      "73": {
        start: {
          line: 228,
          column: 16
        },
        end: {
          line: 228,
          column: 62
        }
      },
      "74": {
        start: {
          line: 230,
          column: 12
        },
        end: {
          line: 232,
          column: 13
        }
      },
      "75": {
        start: {
          line: 231,
          column: 16
        },
        end: {
          line: 231,
          column: 70
        }
      },
      "76": {
        start: {
          line: 233,
          column: 12
        },
        end: {
          line: 237,
          column: 14
        }
      },
      "77": {
        start: {
          line: 240,
          column: 12
        },
        end: {
          line: 240,
          column: 64
        }
      },
      "78": {
        start: {
          line: 241,
          column: 12
        },
        end: {
          line: 241,
          column: 24
        }
      },
      "79": {
        start: {
          line: 245,
          column: 0
        },
        end: {
          line: 245,
          column: 60
        }
      },
      "80": {
        start: {
          line: 251,
          column: 8
        },
        end: {
          line: 262,
          column: 9
        }
      },
      "81": {
        start: {
          line: 253,
          column: 12
        },
        end: {
          line: 257,
          column: 14
        }
      },
      "82": {
        start: {
          line: 260,
          column: 12
        },
        end: {
          line: 260,
          column: 62
        }
      },
      "83": {
        start: {
          line: 261,
          column: 12
        },
        end: {
          line: 261,
          column: 24
        }
      },
      "84": {
        start: {
          line: 265,
          column: 8
        },
        end: {
          line: 276,
          column: 9
        }
      },
      "85": {
        start: {
          line: 267,
          column: 12
        },
        end: {
          line: 271,
          column: 14
        }
      },
      "86": {
        start: {
          line: 274,
          column: 12
        },
        end: {
          line: 274,
          column: 65
        }
      },
      "87": {
        start: {
          line: 275,
          column: 12
        },
        end: {
          line: 275,
          column: 24
        }
      },
      "88": {
        start: {
          line: 279,
          column: 8
        },
        end: {
          line: 286,
          column: 9
        }
      },
      "89": {
        start: {
          line: 281,
          column: 12
        },
        end: {
          line: 281,
          column: 82
        }
      },
      "90": {
        start: {
          line: 284,
          column: 12
        },
        end: {
          line: 284,
          column: 63
        }
      },
      "91": {
        start: {
          line: 285,
          column: 12
        },
        end: {
          line: 285,
          column: 24
        }
      },
      "92": {
        start: {
          line: 289,
          column: 0
        },
        end: {
          line: 289,
          column: 52
        }
      },
      "93": {
        start: {
          line: 295,
          column: 8
        },
        end: {
          line: 295,
          column: 41
        }
      },
      "94": {
        start: {
          line: 296,
          column: 8
        },
        end: {
          line: 296,
          column: 45
        }
      },
      "95": {
        start: {
          line: 299,
          column: 8
        },
        end: {
          line: 306,
          column: 9
        }
      },
      "96": {
        start: {
          line: 300,
          column: 25
        },
        end: {
          line: 300,
          column: 80
        }
      },
      "97": {
        start: {
          line: 301,
          column: 12
        },
        end: {
          line: 301,
          column: 40
        }
      },
      "98": {
        start: {
          line: 304,
          column: 12
        },
        end: {
          line: 304,
          column: 64
        }
      },
      "99": {
        start: {
          line: 305,
          column: 12
        },
        end: {
          line: 305,
          column: 26
        }
      },
      "100": {
        start: {
          line: 309,
          column: 8
        },
        end: {
          line: 320,
          column: 9
        }
      },
      "101": {
        start: {
          line: 310,
          column: 25
        },
        end: {
          line: 310,
          column: 80
        }
      },
      "102": {
        start: {
          line: 311,
          column: 12
        },
        end: {
          line: 313,
          column: 13
        }
      },
      "103": {
        start: {
          line: 312,
          column: 16
        },
        end: {
          line: 312,
          column: 29
        }
      },
      "104": {
        start: {
          line: 314,
          column: 27
        },
        end: {
          line: 314,
          column: 80
        }
      },
      "105": {
        start: {
          line: 315,
          column: 12
        },
        end: {
          line: 315,
          column: 34
        }
      },
      "106": {
        start: {
          line: 318,
          column: 12
        },
        end: {
          line: 318,
          column: 68
        }
      },
      "107": {
        start: {
          line: 319,
          column: 12
        },
        end: {
          line: 319,
          column: 25
        }
      },
      "108": {
        start: {
          line: 323,
          column: 8
        },
        end: {
          line: 377,
          column: 9
        }
      },
      "109": {
        start: {
          line: 324,
          column: 25
        },
        end: {
          line: 324,
          column: 52
        }
      },
      "110": {
        start: {
          line: 326,
          column: 27
        },
        end: {
          line: 363,
          column: 13
        }
      },
      "111": {
        start: {
          line: 364,
          column: 12
        },
        end: {
          line: 364,
          column: 32
        }
      },
      "112": {
        start: {
          line: 367,
          column: 12
        },
        end: {
          line: 367,
          column: 63
        }
      },
      "113": {
        start: {
          line: 368,
          column: 12
        },
        end: {
          line: 376,
          column: 14
        }
      },
      "114": {
        start: {
          line: 380,
          column: 8
        },
        end: {
          line: 391,
          column: 9
        }
      },
      "115": {
        start: {
          line: 381,
          column: 25
        },
        end: {
          line: 381,
          column: 80
        }
      },
      "116": {
        start: {
          line: 382,
          column: 12
        },
        end: {
          line: 386,
          column: 13
        }
      },
      "117": {
        start: {
          line: 383,
          column: 16
        },
        end: {
          line: 383,
          column: 36
        }
      },
      "118": {
        start: {
          line: 384,
          column: 16
        },
        end: {
          line: 384,
          column: 44
        }
      },
      "119": {
        start: {
          line: 385,
          column: 16
        },
        end: {
          line: 385,
          column: 70
        }
      },
      "120": {
        start: {
          line: 389,
          column: 12
        },
        end: {
          line: 389,
          column: 60
        }
      },
      "121": {
        start: {
          line: 390,
          column: 12
        },
        end: {
          line: 390,
          column: 24
        }
      },
      "122": {
        start: {
          line: 394,
          column: 0
        },
        end: {
          line: 394,
          column: 48
        }
      },
      "123": {
        start: {
          line: 400,
          column: 26
        },
        end: {
          line: 400,
          column: 81
        }
      },
      "124": {
        start: {
          line: 401,
          column: 21
        },
        end: {
          line: 401,
          column: 53
        }
      },
      "125": {
        start: {
          line: 402,
          column: 4
        },
        end: {
          line: 404,
          column: 5
        }
      },
      "126": {
        start: {
          line: 403,
          column: 8
        },
        end: {
          line: 403,
          column: 76
        }
      },
      "127": {
        start: {
          line: 405,
          column: 25
        },
        end: {
          line: 405,
          column: 46
        }
      },
      "128": {
        start: {
          line: 407,
          column: 27
        },
        end: {
          line: 407,
          column: 82
        }
      },
      "129": {
        start: {
          line: 409,
          column: 27
        },
        end: {
          line: 409,
          column: 66
        }
      },
      "130": {
        start: {
          line: 410,
          column: 24
        },
        end: {
          line: 410,
          column: 60
        }
      },
      "131": {
        start: {
          line: 411,
          column: 31
        },
        end: {
          line: 411,
          column: 62
        }
      },
      "132": {
        start: {
          line: 412,
          column: 30
        },
        end: {
          line: 412,
          column: 60
        }
      },
      "133": {
        start: {
          line: 413,
          column: 26
        },
        end: {
          line: 413,
          column: 52
        }
      },
      "134": {
        start: {
          line: 414,
          column: 24
        },
        end: {
          line: 414,
          column: 76
        }
      },
      "135": {
        start: {
          line: 415,
          column: 4
        },
        end: {
          line: 423,
          column: 6
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 19,
            column: 4
          },
          end: {
            line: 19,
            column: 5
          }
        },
        loc: {
          start: {
            line: 19,
            column: 30
          },
          end: {
            line: 21,
            column: 5
          }
        },
        line: 19
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 22,
            column: 4
          },
          end: {
            line: 22,
            column: 5
          }
        },
        loc: {
          start: {
            line: 22,
            column: 25
          },
          end: {
            line: 30,
            column: 5
          }
        },
        line: 22
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 31,
            column: 4
          },
          end: {
            line: 31,
            column: 5
          }
        },
        loc: {
          start: {
            line: 31,
            column: 31
          },
          end: {
            line: 39,
            column: 5
          }
        },
        line: 31
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 40,
            column: 4
          },
          end: {
            line: 40,
            column: 5
          }
        },
        loc: {
          start: {
            line: 40,
            column: 30
          },
          end: {
            line: 84,
            column: 5
          }
        },
        line: 40
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 85,
            column: 4
          },
          end: {
            line: 85,
            column: 5
          }
        },
        loc: {
          start: {
            line: 85,
            column: 28
          },
          end: {
            line: 93,
            column: 5
          }
        },
        line: 85
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 94,
            column: 4
          },
          end: {
            line: 94,
            column: 5
          }
        },
        loc: {
          start: {
            line: 94,
            column: 31
          },
          end: {
            line: 102,
            column: 5
          }
        },
        line: 94
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 103,
            column: 4
          },
          end: {
            line: 103,
            column: 5
          }
        },
        loc: {
          start: {
            line: 103,
            column: 19
          },
          end: {
            line: 109,
            column: 5
          }
        },
        line: 103
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 104,
            column: 71
          },
          end: {
            line: 104,
            column: 72
          }
        },
        loc: {
          start: {
            line: 104,
            column: 84
          },
          end: {
            line: 108,
            column: 9
          }
        },
        line: 104
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 110,
            column: 4
          },
          end: {
            line: 110,
            column: 5
          }
        },
        loc: {
          start: {
            line: 110,
            column: 28
          },
          end: {
            line: 113,
            column: 5
          }
        },
        line: 110
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 120,
            column: 4
          },
          end: {
            line: 120,
            column: 5
          }
        },
        loc: {
          start: {
            line: 120,
            column: 30
          },
          end: {
            line: 122,
            column: 5
          }
        },
        line: 120
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 123,
            column: 4
          },
          end: {
            line: 123,
            column: 5
          }
        },
        loc: {
          start: {
            line: 123,
            column: 27
          },
          end: {
            line: 131,
            column: 5
          }
        },
        line: 123
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 132,
            column: 4
          },
          end: {
            line: 132,
            column: 5
          }
        },
        loc: {
          start: {
            line: 132,
            column: 26
          },
          end: {
            line: 140,
            column: 5
          }
        },
        line: 132
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 141,
            column: 4
          },
          end: {
            line: 141,
            column: 5
          }
        },
        loc: {
          start: {
            line: 141,
            column: 27
          },
          end: {
            line: 149,
            column: 5
          }
        },
        line: 141
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 154,
            column: 4
          },
          end: {
            line: 154,
            column: 5
          }
        },
        loc: {
          start: {
            line: 154,
            column: 27
          },
          end: {
            line: 157,
            column: 5
          }
        },
        line: 154
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 158,
            column: 4
          },
          end: {
            line: 158,
            column: 5
          }
        },
        loc: {
          start: {
            line: 158,
            column: 37
          },
          end: {
            line: 161,
            column: 5
          }
        },
        line: 158
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 164,
            column: 4
          },
          end: {
            line: 164,
            column: 5
          }
        },
        loc: {
          start: {
            line: 164,
            column: 26
          },
          end: {
            line: 167,
            column: 5
          }
        },
        line: 164
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 173,
            column: 4
          },
          end: {
            line: 173,
            column: 5
          }
        },
        loc: {
          start: {
            line: 173,
            column: 18
          },
          end: {
            line: 175,
            column: 5
          }
        },
        line: 173
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 176,
            column: 4
          },
          end: {
            line: 176,
            column: 5
          }
        },
        loc: {
          start: {
            line: 176,
            column: 38
          },
          end: {
            line: 184,
            column: 5
          }
        },
        line: 176
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 185,
            column: 4
          },
          end: {
            line: 185,
            column: 5
          }
        },
        loc: {
          start: {
            line: 185,
            column: 35
          },
          end: {
            line: 198,
            column: 5
          }
        },
        line: 185
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 199,
            column: 4
          },
          end: {
            line: 199,
            column: 5
          }
        },
        loc: {
          start: {
            line: 199,
            column: 43
          },
          end: {
            line: 209,
            column: 5
          }
        },
        line: 199
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 216,
            column: 4
          },
          end: {
            line: 216,
            column: 5
          }
        },
        loc: {
          start: {
            line: 216,
            column: 18
          },
          end: {
            line: 218,
            column: 5
          }
        },
        line: 216
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 219,
            column: 4
          },
          end: {
            line: 219,
            column: 5
          }
        },
        loc: {
          start: {
            line: 219,
            column: 35
          },
          end: {
            line: 243,
            column: 5
          }
        },
        line: 219
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 250,
            column: 4
          },
          end: {
            line: 250,
            column: 5
          }
        },
        loc: {
          start: {
            line: 250,
            column: 44
          },
          end: {
            line: 263,
            column: 5
          }
        },
        line: 250
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 264,
            column: 4
          },
          end: {
            line: 264,
            column: 5
          }
        },
        loc: {
          start: {
            line: 264,
            column: 36
          },
          end: {
            line: 277,
            column: 5
          }
        },
        line: 264
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 278,
            column: 4
          },
          end: {
            line: 278,
            column: 5
          }
        },
        loc: {
          start: {
            line: 278,
            column: 35
          },
          end: {
            line: 287,
            column: 5
          }
        },
        line: 278
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 294,
            column: 4
          },
          end: {
            line: 294,
            column: 5
          }
        },
        loc: {
          start: {
            line: 294,
            column: 46
          },
          end: {
            line: 297,
            column: 5
          }
        },
        line: 294
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 298,
            column: 4
          },
          end: {
            line: 298,
            column: 5
          }
        },
        loc: {
          start: {
            line: 298,
            column: 27
          },
          end: {
            line: 307,
            column: 5
          }
        },
        line: 298
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 308,
            column: 4
          },
          end: {
            line: 308,
            column: 5
          }
        },
        loc: {
          start: {
            line: 308,
            column: 36
          },
          end: {
            line: 321,
            column: 5
          }
        },
        line: 308
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 322,
            column: 4
          },
          end: {
            line: 322,
            column: 5
          }
        },
        loc: {
          start: {
            line: 322,
            column: 26
          },
          end: {
            line: 378,
            column: 5
          }
        },
        line: 322
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 379,
            column: 4
          },
          end: {
            line: 379,
            column: 5
          }
        },
        loc: {
          start: {
            line: 379,
            column: 31
          },
          end: {
            line: 392,
            column: 5
          }
        },
        line: 379
      },
      "30": {
        name: "createOfflineServiceContainer",
        decl: {
          start: {
            line: 398,
            column: 15
          },
          end: {
            line: 398,
            column: 44
          }
        },
        loc: {
          start: {
            line: 398,
            column: 47
          },
          end: {
            line: 424,
            column: 1
          }
        },
        line: 398
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 43,
            column: 20
          },
          end: {
            line: 43,
            column: 50
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 43,
            column: 20
          },
          end: {
            line: 43,
            column: 27
          }
        }, {
          start: {
            line: 43,
            column: 31
          },
          end: {
            line: 43,
            column: 50
          }
        }],
        line: 43
      },
      "1": {
        loc: {
          start: {
            line: 44,
            column: 24
          },
          end: {
            line: 44,
            column: 57
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 44,
            column: 24
          },
          end: {
            line: 44,
            column: 35
          }
        }, {
          start: {
            line: 44,
            column: 39
          },
          end: {
            line: 44,
            column: 57
          }
        }],
        line: 44
      },
      "2": {
        loc: {
          start: {
            line: 45,
            column: 30
          },
          end: {
            line: 45,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 45,
            column: 30
          },
          end: {
            line: 45,
            column: 47
          }
        }, {
          start: {
            line: 45,
            column: 51
          },
          end: {
            line: 45,
            column: 64
          }
        }],
        line: 45
      },
      "3": {
        loc: {
          start: {
            line: 46,
            column: 32
          },
          end: {
            line: 46,
            column: 83
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 46,
            column: 32
          },
          end: {
            line: 46,
            column: 51
          }
        }, {
          start: {
            line: 46,
            column: 55
          },
          end: {
            line: 46,
            column: 83
          }
        }],
        line: 46
      },
      "4": {
        loc: {
          start: {
            line: 47,
            column: 37
          },
          end: {
            line: 47,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 47,
            column: 37
          },
          end: {
            line: 47,
            column: 61
          }
        }, {
          start: {
            line: 47,
            column: 65
          },
          end: {
            line: 47,
            column: 67
          }
        }],
        line: 47
      },
      "5": {
        loc: {
          start: {
            line: 48,
            column: 34
          },
          end: {
            line: 48,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 48,
            column: 34
          },
          end: {
            line: 48,
            column: 55
          }
        }, {
          start: {
            line: 48,
            column: 59
          },
          end: {
            line: 48,
            column: 61
          }
        }],
        line: 48
      },
      "6": {
        loc: {
          start: {
            line: 49,
            column: 29
          },
          end: {
            line: 49,
            column: 51
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 49,
            column: 29
          },
          end: {
            line: 49,
            column: 45
          }
        }, {
          start: {
            line: 49,
            column: 49
          },
          end: {
            line: 49,
            column: 51
          }
        }],
        line: 49
      },
      "7": {
        loc: {
          start: {
            line: 50,
            column: 32
          },
          end: {
            line: 50,
            column: 57
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 50,
            column: 32
          },
          end: {
            line: 50,
            column: 51
          }
        }, {
          start: {
            line: 50,
            column: 55
          },
          end: {
            line: 50,
            column: 57
          }
        }],
        line: 50
      },
      "8": {
        loc: {
          start: {
            line: 51,
            column: 30
          },
          end: {
            line: 51,
            column: 75
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 51,
            column: 30
          },
          end: {
            line: 51,
            column: 47
          }
        }, {
          start: {
            line: 51,
            column: 51
          },
          end: {
            line: 51,
            column: 75
          }
        }],
        line: 51
      },
      "9": {
        loc: {
          start: {
            line: 53,
            column: 25
          },
          end: {
            line: 53,
            column: 46
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 53,
            column: 25
          },
          end: {
            line: 53,
            column: 37
          }
        }, {
          start: {
            line: 53,
            column: 41
          },
          end: {
            line: 53,
            column: 46
          }
        }],
        line: 53
      },
      "10": {
        loc: {
          start: {
            line: 54,
            column: 23
          },
          end: {
            line: 54,
            column: 39
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 54,
            column: 23
          },
          end: {
            line: 54,
            column: 33
          }
        }, {
          start: {
            line: 54,
            column: 37
          },
          end: {
            line: 54,
            column: 39
          }
        }],
        line: 54
      },
      "11": {
        loc: {
          start: {
            line: 55,
            column: 26
          },
          end: {
            line: 55,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 55,
            column: 26
          },
          end: {
            line: 55,
            column: 39
          }
        }, {
          start: {
            line: 55,
            column: 43
          },
          end: {
            line: 55,
            column: 45
          }
        }],
        line: 55
      },
      "12": {
        loc: {
          start: {
            line: 56,
            column: 27
          },
          end: {
            line: 56,
            column: 47
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 56,
            column: 27
          },
          end: {
            line: 56,
            column: 41
          }
        }, {
          start: {
            line: 56,
            column: 45
          },
          end: {
            line: 56,
            column: 47
          }
        }],
        line: 56
      },
      "13": {
        loc: {
          start: {
            line: 57,
            column: 42
          },
          end: {
            line: 68,
            column: 17
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 57,
            column: 42
          },
          end: {
            line: 57,
            column: 71
          }
        }, {
          start: {
            line: 57,
            column: 75
          },
          end: {
            line: 68,
            column: 17
          }
        }],
        line: 57
      },
      "14": {
        loc: {
          start: {
            line: 69,
            column: 32
          },
          end: {
            line: 75,
            column: 17
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 69,
            column: 32
          },
          end: {
            line: 69,
            column: 51
          }
        }, {
          start: {
            line: 69,
            column: 55
          },
          end: {
            line: 75,
            column: 17
          }
        }],
        line: 69
      },
      "15": {
        loc: {
          start: {
            line: 106,
            column: 22
          },
          end: {
            line: 106,
            column: 52
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 106,
            column: 33
          },
          end: {
            line: 106,
            column: 34
          }
        }, {
          start: {
            line: 106,
            column: 38
          },
          end: {
            line: 106,
            column: 51
          }
        }],
        line: 106
      },
      "16": {
        loc: {
          start: {
            line: 190,
            column: 26
          },
          end: {
            line: 190,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 190,
            column: 26
          },
          end: {
            line: 190,
            column: 42
          }
        }, {
          start: {
            line: 190,
            column: 46
          },
          end: {
            line: 190,
            column: 48
          }
        }],
        line: 190
      },
      "17": {
        loc: {
          start: {
            line: 191,
            column: 24
          },
          end: {
            line: 191,
            column: 69
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 191,
            column: 42
          },
          end: {
            line: 191,
            column: 44
          }
        }, {
          start: {
            line: 191,
            column: 47
          },
          end: {
            line: 191,
            column: 69
          }
        }],
        line: 191
      },
      "18": {
        loc: {
          start: {
            line: 224,
            column: 12
          },
          end: {
            line: 226,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 224,
            column: 12
          },
          end: {
            line: 226,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 224
      },
      "19": {
        loc: {
          start: {
            line: 227,
            column: 12
          },
          end: {
            line: 229,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 227,
            column: 12
          },
          end: {
            line: 229,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 227
      },
      "20": {
        loc: {
          start: {
            line: 230,
            column: 12
          },
          end: {
            line: 232,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 230,
            column: 12
          },
          end: {
            line: 232,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 230
      },
      "21": {
        loc: {
          start: {
            line: 301,
            column: 19
          },
          end: {
            line: 301,
            column: 39
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 301,
            column: 19
          },
          end: {
            line: 301,
            column: 29
          }
        }, {
          start: {
            line: 301,
            column: 33
          },
          end: {
            line: 301,
            column: 39
          }
        }],
        line: 301
      },
      "22": {
        loc: {
          start: {
            line: 311,
            column: 12
          },
          end: {
            line: 313,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 311,
            column: 12
          },
          end: {
            line: 313,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 311
      },
      "23": {
        loc: {
          start: {
            line: 382,
            column: 12
          },
          end: {
            line: 386,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 382,
            column: 12
          },
          end: {
            line: 386,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 382
      },
      "24": {
        loc: {
          start: {
            line: 402,
            column: 4
          },
          end: {
            line: 404,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 402,
            column: 4
          },
          end: {
            line: 404,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 402
      },
      "25": {
        loc: {
          start: {
            line: 402,
            column: 8
          },
          end: {
            line: 402,
            column: 51
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 402,
            column: 8
          },
          end: {
            line: 402,
            column: 25
          }
        }, {
          start: {
            line: 402,
            column: 29
          },
          end: {
            line: 402,
            column: 51
          }
        }],
        line: 402
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\frontend\\lib\\services\\OfflineServiceContainer.ts",
      mappings: ";AAAA;;;;;;;GAOG;;;AAyaH,sEA+BC;AArcD,+DAA4D;AAC5D,yEAA2F;AA2B3F;;GAEG;AACH,MAAa,qBAAqB;IAChC,YAAoB,YAAiC;QAAjC,iBAAY,GAAZ,YAAY,CAAqB;IAAG,CAAC;IAEzD,KAAK,CAAC,UAAU,CAAC,EAAU;QACzB,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;QAClE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YAC/C,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,OAAgB;QAChC,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QACjE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YAChD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,IAAsB;QACxC,IAAI,CAAC;YACH,MAAM,UAAU,GAAY;gBAC1B,EAAE,EAAE,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,YAAY,EAAE;gBAClC,MAAM,EAAE,IAAI,CAAC,MAAM,IAAI,kBAAkB;gBACzC,YAAY,EAAE,IAAI,CAAC,YAAY,IAAI,aAAa;gBAChD,cAAc,EAAE,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,qBAAqB,EAAE;gBACnE,mBAAmB,EAAE,IAAI,CAAC,mBAAmB,IAAI,EAAE;gBACnD,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,IAAI,EAAE;gBAC7C,WAAW,EAAE,IAAI,CAAC,WAAW,IAAI,EAAE;gBACnC,cAAc,EAAE,IAAI,CAAC,cAAc,IAAI,EAAE;gBACzC,YAAY,EAAE,IAAI,CAAC,YAAY,IAAI,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBAC3D,aAAa,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACvC,OAAO,EAAE,IAAI,CAAC,OAAO,IAAI,KAAK;gBAC9B,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,EAAE;gBACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI,EAAE;gBAC7B,SAAS,EAAE,IAAI,CAAC,SAAS,IAAI,EAAE;gBAC/B,wBAAwB,EAAE,IAAI,CAAC,wBAAwB,IAAI;oBACzD,KAAK,EAAE,UAAU;oBACjB,iBAAiB,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE;oBAC3C,gBAAgB,EAAE,kBAAkB;oBACpC,kBAAkB,EAAE,MAAM;oBAC1B,eAAe,EAAE,UAAU;oBAC3B,gBAAgB,EAAE,MAAM;oBACxB,gBAAgB,EAAE,IAAI;oBACtB,cAAc,EAAE,GAAG;oBACnB,QAAQ,EAAE,CAAC;oBACX,aAAa,EAAE,GAAG;iBACnB;gBACD,cAAc,EAAE,IAAI,CAAC,cAAc,IAAI;oBACrC,MAAM,EAAE,IAAI;oBACZ,MAAM,EAAE,IAAI;oBACZ,EAAE,EAAE,KAAK;oBACT,GAAG,EAAE,KAAK;oBACV,IAAI,EAAE,KAAK;iBACZ;aACF,CAAC;YAEF,MAAM,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;YAClE,OAAO,UAAU,CAAC;QACpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAClD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,EAAU;QAC5B,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;QAC9D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAClD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,MAAc;QAC/B,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QAC7E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YACjD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,YAAY;QAClB,OAAO,sCAAsC,CAAC,OAAO,CAAC,OAAO,EAAE,UAAS,CAAC;YACvE,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YACjC,MAAM,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;YACzC,OAAO,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,qBAAqB;QAC3B,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAClD,OAAO,OAAO,SAAS,EAAE,CAAC;IAC5B,CAAC;CACF;AAjGD,sDAiGC;AAED;;GAEG;AACH,MAAa,kBAAkB;IAC7B,YAAoB,YAAiC;QAAjC,iBAAY,GAAZ,YAAY,CAAqB;IAAG,CAAC;IAEzD,KAAK,CAAC,cAAc;QAClB,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,cAAc,EAAE,CAAC;QACjE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACpD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,EAAU;QAC1B,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAC5D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;YAC5C,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,IAAU;QACzB,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACxD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YAC/C,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF;AA7BD,gDA6BC;AAED,oDAAoD;AACpD,MAAM,iBAAiB;IACrB,KAAK,CAAC,SAAS,CAAC,KAAuB;QACrC,wEAAwE;QACxE,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;IACvE,CAAC;IAED,KAAK,CAAC,mBAAmB,CAAC,KAAuB;QAC/C,wEAAwE;QACxE,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;IACvE,CAAC;CACF;AAED,MAAM,eAAe;IACnB,KAAK,CAAC,QAAQ,CAAC,KAAuB;QACpC,uEAAuE;QACvE,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AAED;;GAEG;AACH,MAAa,yBAAyB;IAGpC;QACE,IAAI,CAAC,UAAU,GAAG,IAAI,iBAAiB,EAAE,CAAC;IAC5C,CAAC;IAED,KAAK,CAAC,mBAAmB,CAAC,MAAwB;QAChD,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;QAC3D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YACzD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,OAA0B;QAC9C,IAAI,CAAC;YACH,oCAAoC;YACpC,OAAO;gBACL,KAAK,EAAE,OAAO,CAAC,OAAO;gBACtB,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,EAAE;gBAChC,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC;aACtD,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACpD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,SAAiB;QAC3C,IAAI,CAAC;YACH,+CAA+C;YAC/C,6DAA6D;YAC7D,OAAO,EAAE,CAAC;QACZ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YAC3D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF;AAxCD,8DAwCC;AAED;;GAEG;AACH,MAAa,wBAAwB;IAGnC;QACE,IAAI,CAAC,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;IAC/C,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,OAAgB;QACpC,IAAI,CAAC;YACH,2BAA2B;YAC3B,MAAM,MAAM,GAAG,EAAE,CAAC;YAClB,MAAM,QAAQ,GAAG,EAAE,CAAC;YAEpB,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,IAAI,EAAE,EAAE,CAAC;gBAClC,MAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;YAC1C,CAAC;YAED,IAAI,OAAO,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC/B,QAAQ,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;YAChD,CAAC;YAED,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAClC,QAAQ,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;YACxD,CAAC;YAED,OAAO;gBACL,KAAK,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;gBAC1B,MAAM;gBACN,QAAQ;aACT,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACpD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF;AAnCD,4DAmCC;AAED;;GAEG;AACH,MAAa,oBAAoB;IAC/B,KAAK,CAAC,aAAa,CAAC,SAAiB,EAAE,OAAsB;QAC3D,IAAI,CAAC;YACH,2CAA2C;YAC3C,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,QAAQ,EAAE,UAAU,IAAI,CAAC,GAAG,EAAE,EAAE;gBAChC,WAAW,EAAE,YAAY,SAAS,IAAI,OAAO,CAAC,MAAM,EAAE;aACvD,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAClD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,QAAgB;QACpC,IAAI,CAAC;YACH,sCAAsC;YACtC,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,QAAQ,EAAE,QAAQ;gBAClB,WAAW,EAAE,gBAAgB,QAAQ,WAAW;aACjD,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACrD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,QAAgB;QACnC,IAAI,CAAC;YACH,qCAAqC;YACrC,OAAO,IAAI,IAAI,CAAC,CAAC,qBAAqB,CAAC,EAAE,EAAE,IAAI,EAAE,iBAAiB,EAAE,CAAC,CAAC;QACxE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACnD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF;AAtCD,oDAsCC;AAED;;GAEG;AACH,MAAa,kBAAkB;IAC7B,YACU,YAAiC,EACjC,cAA8B;QAD9B,iBAAY,GAAZ,YAAY,CAAqB;QACjC,mBAAc,GAAd,cAAc,CAAgB;IACrC,CAAC;IAEJ,KAAK,CAAC,cAAc;QAClB,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,cAAc,EAAE,CAAC;YACrE,OAAO,IAAI,EAAE,IAAI,IAAI,MAAM,CAAC;QAC9B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACpD,OAAO,MAAM,CAAC;QAChB,CAAC;IACH,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,OAAe;QACpC,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,cAAc,EAAE,CAAC;YACrE,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC;gBACd,OAAO,KAAK,CAAC;YACf,CAAC;YACD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;YACrE,OAAO,MAAM,CAAC,OAAO,CAAC;QACxB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YACxD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,KAAK,CAAC,aAAa;QACjB,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;YAEzC,sCAAsC;YACtC,MAAM,MAAM,GAAiC;gBAC3C,IAAI,EAAE;oBACJ,QAAQ,EAAE,CAAC;oBACX,WAAW,EAAE,EAAE;oBACf,WAAW,EAAE,CAAC;oBACd,8BAA8B,EAAE,KAAK;oBACrC,yBAAyB,EAAE,KAAK;oBAChC,gBAAgB,EAAE,KAAK;oBACvB,aAAa,EAAE,KAAK;iBACrB;gBACD,GAAG,EAAE;oBACH,QAAQ,EAAE,EAAE;oBACZ,WAAW,EAAE,GAAG;oBAChB,WAAW,EAAE,CAAC,CAAC,EAAE,YAAY;oBAC7B,8BAA8B,EAAE,IAAI;oBACpC,yBAAyB,EAAE,IAAI;oBAC/B,gBAAgB,EAAE,IAAI;oBACtB,aAAa,EAAE,IAAI;iBACpB;gBACD,UAAU,EAAE;oBACV,QAAQ,EAAE,CAAC,CAAC,EAAE,YAAY;oBAC1B,WAAW,EAAE,CAAC,CAAC,EAAE,YAAY;oBAC7B,WAAW,EAAE,CAAC,CAAC,EAAE,YAAY;oBAC7B,8BAA8B,EAAE,IAAI;oBACpC,yBAAyB,EAAE,IAAI;oBAC/B,gBAAgB,EAAE,IAAI;oBACtB,aAAa,EAAE,IAAI;iBACpB;gBACD,WAAW,EAAE;oBACX,QAAQ,EAAE,CAAC,CAAC,EAAE,YAAY;oBAC1B,WAAW,EAAE,CAAC,CAAC,EAAE,YAAY;oBAC7B,WAAW,EAAE,CAAC,CAAC,EAAE,YAAY;oBAC7B,8BAA8B,EAAE,IAAI;oBACpC,yBAAyB,EAAE,IAAI;oBAC/B,gBAAgB,EAAE,IAAI;oBACtB,aAAa,EAAE,IAAI;iBACpB;aACF,CAAC;YAEF,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC;QACtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACnD,OAAO;gBACL,QAAQ,EAAE,CAAC;gBACX,WAAW,EAAE,EAAE;gBACf,WAAW,EAAE,CAAC;gBACd,8BAA8B,EAAE,KAAK;gBACrC,yBAAyB,EAAE,KAAK;gBAChC,gBAAgB,EAAE,KAAK;gBACvB,aAAa,EAAE,KAAK;aACrB,CAAC;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,OAAiB;QACjC,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,cAAc,EAAE,CAAC;YACrE,IAAI,IAAI,EAAE,CAAC;gBACT,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC;gBACpB,IAAI,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;gBAC5B,MAAM,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YACxD,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YAChD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF;AAtGD,gDAsGC;AAED;;GAEG;AACI,KAAK,UAAU,6BAA6B;IACjD,sBAAsB;IACtB,MAAM,aAAa,GAAG,yCAAmB,CAAC,WAAW,EAAE,CAAC;IACxD,MAAM,QAAQ,GAAG,MAAM,aAAa,CAAC,UAAU,EAAE,CAAC;IAElD,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC;QAChD,MAAM,IAAI,KAAK,CAAC,kCAAkC,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC;IACtE,CAAC;IAED,MAAM,YAAY,GAAG,QAAQ,CAAC,YAAY,CAAC;IAE3C,yBAAyB;IACzB,MAAM,cAAc,GAAG,IAAI,+BAAc,CAAC,QAAQ,CAAC,SAAU,CAAC,CAAC;IAE/D,kBAAkB;IAClB,MAAM,cAAc,GAAG,IAAI,qBAAqB,CAAC,YAAY,CAAC,CAAC;IAC/D,MAAM,WAAW,GAAG,IAAI,kBAAkB,CAAC,YAAY,CAAC,CAAC;IACzD,MAAM,kBAAkB,GAAG,IAAI,yBAAyB,EAAE,CAAC;IAC3D,MAAM,iBAAiB,GAAG,IAAI,wBAAwB,EAAE,CAAC;IACzD,MAAM,aAAa,GAAG,IAAI,oBAAoB,EAAE,CAAC;IACjD,MAAM,WAAW,GAAG,IAAI,kBAAkB,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;IAEzE,OAAO;QACL,cAAc;QACd,kBAAkB;QAClB,iBAAiB;QACjB,aAAa;QACb,WAAW;QACX,cAAc;QACd,WAAW;KACZ,CAAC;AACJ,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\frontend\\lib\\services\\OfflineServiceContainer.ts"],
      sourcesContent: ["/**\r\n * Offline Service Container\r\n * \r\n * Real service implementations for Phase 1 offline desktop mode.\r\n * Connects to local SQLite database and provides business logic services.\r\n * \r\n * @see docs/refactoring/component-architecture-specification.md\r\n */\r\n\r\nimport { createDataService, DataService } from '../data/DataService';\r\nimport { FeatureManager } from '../features/FeatureManager';\r\nimport { RepositoryContainer, DatabaseInitializer } from '../database/DatabaseInitializer';\r\n// import { TierEnforcer } from '../../../backend/services/enforcement/TierEnforcer';\r\n// import { AirDuctCalculator } from '../../../backend/services/calculations/AirDuctCalculator';\r\n// import { SMACNAValidator } from '../../../backend/services/calculations/SMACNAValidator';\r\nimport {\r\n  CalculationInput,\r\n  CalculationResult,\r\n  ExportOptions,\r\n  ExportResult,\r\n  TierLimits\r\n} from '../../types/air-duct-sizer';\r\nimport { Project } from '../repositories/interfaces/ProjectRepository';\r\nimport { User, UserTier } from '../repositories/interfaces/UserRepository';\r\n\r\n/**\r\n * Service container interface for dependency injection\r\n */\r\nexport interface OfflineServiceContainer {\r\n  projectService: OfflineProjectService;\r\n  calculationService: OfflineCalculationService;\r\n  validationService: OfflineValidationService;\r\n  exportService: OfflineExportService;\r\n  tierService: OfflineTierService;\r\n  featureManager: FeatureManager;\r\n  userService: OfflineUserService;\r\n}\r\n\r\n/**\r\n * Project service for offline mode\r\n */\r\nexport class OfflineProjectService {\r\n  constructor(private repositories: RepositoryContainer) {}\r\n\r\n  async getProject(id: string): Promise<Project | null> {\r\n    try {\r\n      return await this.repositories.projectRepository.getProject(id);\r\n    } catch (error) {\r\n      console.error('Failed to get project:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async saveProject(project: Project): Promise<void> {\r\n    try {\r\n      await this.repositories.projectRepository.saveProject(project);\r\n    } catch (error) {\r\n      console.error('Failed to save project:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async createProject(data: Partial<Project>): Promise<Project> {\r\n    try {\r\n      const newProject: Project = {\r\n        id: data.id || this.generateUUID(),\r\n        userId: data.userId || 'offline-user-001',\r\n        project_name: data.project_name || 'New Project',\r\n        project_number: data.project_number || this.generateProjectNumber(),\r\n        project_description: data.project_description || '',\r\n        project_location: data.project_location || '',\r\n        client_name: data.client_name || '',\r\n        estimator_name: data.estimator_name || '',\r\n        date_created: data.date_created || new Date().toISOString(),\r\n        last_modified: new Date().toISOString(),\r\n        version: data.version || '1.0',\r\n        rooms: data.rooms || [],\r\n        segments: data.segments || [],\r\n        equipment: data.equipment || [],\r\n        computational_properties: data.computational_properties || {\r\n          units: 'Imperial',\r\n          default_duct_size: { width: 12, height: 8 },\r\n          default_material: 'Galvanized Steel',\r\n          default_insulation: 'None',\r\n          default_fitting: 'Standard',\r\n          calibration_mode: 'Auto',\r\n          default_velocity: 1000,\r\n          pressure_class: \"2\",\r\n          altitude: 0,\r\n          friction_rate: 0.1\r\n        },\r\n        code_standards: data.code_standards || {\r\n          smacna: true,\r\n          ashrae: true,\r\n          ul: false,\r\n          imc: false,\r\n          nfpa: false\r\n        }\r\n      };\r\n\r\n      await this.repositories.projectRepository.saveProject(newProject);\r\n      return newProject;\r\n    } catch (error) {\r\n      console.error('Failed to create project:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async deleteProject(id: string): Promise<void> {\r\n    try {\r\n      await this.repositories.projectRepository.deleteProject(id);\r\n    } catch (error) {\r\n      console.error('Failed to delete project:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async listProjects(userId: string): Promise<Project[]> {\r\n    try {\r\n      return await this.repositories.projectRepository.getProjectsByUser(userId);\r\n    } catch (error) {\r\n      console.error('Failed to list projects:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  private generateUUID(): string {\r\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\r\n      const r = Math.random() * 16 | 0;\r\n      const v = c == 'x' ? r : (r & 0x3 | 0x8);\r\n      return v.toString(16);\r\n    });\r\n  }\r\n\r\n  private generateProjectNumber(): string {\r\n    const timestamp = Date.now().toString().slice(-6);\r\n    return `PRJ-${timestamp}`;\r\n  }\r\n}\r\n\r\n/**\r\n * User service for offline mode\r\n */\r\nexport class OfflineUserService {\r\n  constructor(private repositories: RepositoryContainer) {}\r\n\r\n  async getCurrentUser(): Promise<User | null> {\r\n    try {\r\n      return await this.repositories.userRepository.getCurrentUser();\r\n    } catch (error) {\r\n      console.error('Failed to get current user:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async getUserById(id: string): Promise<User | null> {\r\n    try {\r\n      return await this.repositories.userRepository.getUser(id);\r\n    } catch (error) {\r\n      console.error('Failed to get user:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async updateUser(user: User): Promise<void> {\r\n    try {\r\n      await this.repositories.userRepository.saveUser(user);\r\n    } catch (error) {\r\n      console.error('Failed to update user:', error);\r\n      throw error;\r\n    }\r\n  }\r\n}\r\n\r\n// Stub implementations for missing backend services\r\nclass AirDuctCalculator {\r\n  async calculate(input: CalculationInput): Promise<CalculationResult> {\r\n    // Stub implementation - would be replaced with actual calculation logic\r\n    throw new Error('AirDuctCalculator not implemented in offline mode');\r\n  }\r\n\r\n  async calculateDuctSizing(input: CalculationInput): Promise<CalculationResult> {\r\n    // Stub implementation - would be replaced with actual calculation logic\r\n    throw new Error('AirDuctCalculator not implemented in offline mode');\r\n  }\r\n}\r\n\r\nclass SMACNAValidator {\r\n  async validate(input: CalculationInput): Promise<boolean> {\r\n    // Stub implementation - would be replaced with actual validation logic\r\n    return true;\r\n  }\r\n}\r\n\r\n/**\r\n * Calculation service for offline mode\r\n */\r\nexport class OfflineCalculationService {\r\n  private calculator: AirDuctCalculator;\r\n\r\n  constructor() {\r\n    this.calculator = new AirDuctCalculator();\r\n  }\r\n\r\n  async calculateDuctSizing(inputs: CalculationInput): Promise<CalculationResult> {\r\n    try {\r\n      return await this.calculator.calculateDuctSizing(inputs);\r\n    } catch (error) {\r\n      console.error('Failed to calculate duct sizing:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async validateResults(results: CalculationResult): Promise<any> {\r\n    try {\r\n      // Basic validation for offline mode\r\n      return {\r\n        valid: results.success,\r\n        warnings: results.warnings || [],\r\n        errors: results.success ? [] : ['Calculation failed']\r\n      };\r\n    } catch (error) {\r\n      console.error('Failed to validate results:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async getCalculationHistory(projectId: string): Promise<CalculationResult[]> {\r\n    try {\r\n      // For offline mode, return empty array for now\r\n      // Could be enhanced to store calculation history in database\r\n      return [];\r\n    } catch (error) {\r\n      console.error('Failed to get calculation history:', error);\r\n      throw error;\r\n    }\r\n  }\r\n}\r\n\r\n/**\r\n * Validation service for offline mode\r\n */\r\nexport class OfflineValidationService {\r\n  private smacnaValidator: SMACNAValidator;\r\n\r\n  constructor() {\r\n    this.smacnaValidator = new SMACNAValidator();\r\n  }\r\n\r\n  async validateProject(project: Project): Promise<any> {\r\n    try {\r\n      // Basic project validation\r\n      const errors = [];\r\n      const warnings = [];\r\n\r\n      if (!project.project_name?.trim()) {\r\n        errors.push('Project name is required');\r\n      }\r\n\r\n      if (project.rooms.length === 0) {\r\n        warnings.push('Project has no rooms defined');\r\n      }\r\n\r\n      if (project.segments.length === 0) {\r\n        warnings.push('Project has no duct segments defined');\r\n      }\r\n\r\n      return {\r\n        valid: errors.length === 0,\r\n        errors,\r\n        warnings\r\n      };\r\n    } catch (error) {\r\n      console.error('Failed to validate project:', error);\r\n      throw error;\r\n    }\r\n  }\r\n}\r\n\r\n/**\r\n * Export service for offline mode\r\n */\r\nexport class OfflineExportService {\r\n  async exportProject(projectId: string, options: ExportOptions): Promise<ExportResult> {\r\n    try {\r\n      // Simulate export process for offline mode\r\n      return {\r\n        success: true,\r\n        exportId: `export-${Date.now()}`,\r\n        downloadUrl: `/exports/${projectId}.${options.format}`\r\n      };\r\n    } catch (error) {\r\n      console.error('Failed to export project:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async getExportStatus(exportId: string): Promise<ExportResult> {\r\n    try {\r\n      // Return mock status for offline mode\r\n      return {\r\n        success: true,\r\n        exportId: exportId,\r\n        downloadUrl: `/api/exports/${exportId}/download`\r\n      };\r\n    } catch (error) {\r\n      console.error('Failed to get export status:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async downloadExport(exportId: string): Promise<Blob> {\r\n    try {\r\n      // Return empty blob for offline mode\r\n      return new Blob(['Mock export content'], { type: 'application/pdf' });\r\n    } catch (error) {\r\n      console.error('Failed to download export:', error);\r\n      throw error;\r\n    }\r\n  }\r\n}\r\n\r\n/**\r\n * Tier service for offline mode\r\n */\r\nexport class OfflineTierService {\r\n  constructor(\r\n    private repositories: RepositoryContainer,\r\n    private featureManager: FeatureManager\r\n  ) {}\r\n\r\n  async getCurrentTier(): Promise<UserTier> {\r\n    try {\r\n      const user = await this.repositories.userRepository.getCurrentUser();\r\n      return user?.tier || 'free';\r\n    } catch (error) {\r\n      console.error('Failed to get current tier:', error);\r\n      return 'free';\r\n    }\r\n  }\r\n\r\n  async hasFeatureAccess(feature: string): Promise<boolean> {\r\n    try {\r\n      const user = await this.repositories.userRepository.getCurrentUser();\r\n      if (!user?.id) {\r\n        return false;\r\n      }\r\n      const result = await this.featureManager.isEnabled(feature, user.id);\r\n      return result.enabled;\r\n    } catch (error) {\r\n      console.error('Failed to check feature access:', error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  async getTierLimits(): Promise<TierLimits> {\r\n    try {\r\n      const tier = await this.getCurrentTier();\r\n      \r\n      // Define tier limits for offline mode\r\n      const limits: Record<UserTier, TierLimits> = {\r\n        free: {\r\n          maxRooms: 5,\r\n          maxSegments: 10,\r\n          maxProjects: 3,\r\n          canEditComputationalProperties: false,\r\n          canExportWithoutWatermark: false,\r\n          canUseSimulation: false,\r\n          canUseCatalog: false\r\n        },\r\n        pro: {\r\n          maxRooms: 50,\r\n          maxSegments: 100,\r\n          maxProjects: -1, // Unlimited\r\n          canEditComputationalProperties: true,\r\n          canExportWithoutWatermark: true,\r\n          canUseSimulation: true,\r\n          canUseCatalog: true\r\n        },\r\n        enterprise: {\r\n          maxRooms: -1, // Unlimited\r\n          maxSegments: -1, // Unlimited\r\n          maxProjects: -1, // Unlimited\r\n          canEditComputationalProperties: true,\r\n          canExportWithoutWatermark: true,\r\n          canUseSimulation: true,\r\n          canUseCatalog: true\r\n        },\r\n        super_admin: {\r\n          maxRooms: -1, // Unlimited\r\n          maxSegments: -1, // Unlimited\r\n          maxProjects: -1, // Unlimited\r\n          canEditComputationalProperties: true,\r\n          canExportWithoutWatermark: true,\r\n          canUseSimulation: true,\r\n          canUseCatalog: true\r\n        }\r\n      };\r\n\r\n      return limits[tier];\r\n    } catch (error) {\r\n      console.error('Failed to get tier limits:', error);\r\n      return {\r\n        maxRooms: 5,\r\n        maxSegments: 10,\r\n        maxProjects: 3,\r\n        canEditComputationalProperties: false,\r\n        canExportWithoutWatermark: false,\r\n        canUseSimulation: false,\r\n        canUseCatalog: false\r\n      };\r\n    }\r\n  }\r\n\r\n  async upgradeTier(newTier: UserTier): Promise<void> {\r\n    try {\r\n      const user = await this.repositories.userRepository.getCurrentUser();\r\n      if (user) {\r\n        user.tier = newTier;\r\n        user.updatedAt = new Date();\r\n        await this.repositories.userRepository.saveUser(user);\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to upgrade tier:', error);\r\n      throw error;\r\n    }\r\n  }\r\n}\r\n\r\n/**\r\n * Create offline service container\r\n */\r\nexport async function createOfflineServiceContainer(): Promise<OfflineServiceContainer> {\r\n  // Initialize database\r\n  const dbInitializer = DatabaseInitializer.getInstance();\r\n  const dbResult = await dbInitializer.initialize();\r\n  \r\n  if (!dbResult.success || !dbResult.repositories) {\r\n    throw new Error(`Failed to initialize database: ${dbResult.error}`);\r\n  }\r\n\r\n  const repositories = dbResult.repositories;\r\n\r\n  // Create feature manager\r\n  const featureManager = new FeatureManager(dbResult.dbManager!);\r\n\r\n  // Create services\r\n  const projectService = new OfflineProjectService(repositories);\r\n  const userService = new OfflineUserService(repositories);\r\n  const calculationService = new OfflineCalculationService();\r\n  const validationService = new OfflineValidationService();\r\n  const exportService = new OfflineExportService();\r\n  const tierService = new OfflineTierService(repositories, featureManager);\r\n\r\n  return {\r\n    projectService,\r\n    calculationService,\r\n    validationService,\r\n    exportService,\r\n    tierService,\r\n    featureManager,\r\n    userService\r\n  };\r\n}\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "291f28f295ce597a174de1b2cf3f3246b180133a"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_p8luzfsbt = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_p8luzfsbt();
cov_p8luzfsbt().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_p8luzfsbt().s[1]++;
exports.OfflineTierService = exports.OfflineExportService = exports.OfflineValidationService = exports.OfflineCalculationService = exports.OfflineUserService = exports.OfflineProjectService = void 0;
/* istanbul ignore next */
cov_p8luzfsbt().s[2]++;
exports.createOfflineServiceContainer = createOfflineServiceContainer;
const FeatureManager_1 =
/* istanbul ignore next */
(cov_p8luzfsbt().s[3]++, require("../features/FeatureManager"));
const DatabaseInitializer_1 =
/* istanbul ignore next */
(cov_p8luzfsbt().s[4]++, require("../database/DatabaseInitializer"));
/**
 * Project service for offline mode
 */
class OfflineProjectService {
  constructor(repositories) {
    /* istanbul ignore next */
    cov_p8luzfsbt().f[0]++;
    cov_p8luzfsbt().s[5]++;
    this.repositories = repositories;
  }
  async getProject(id) {
    /* istanbul ignore next */
    cov_p8luzfsbt().f[1]++;
    cov_p8luzfsbt().s[6]++;
    try {
      /* istanbul ignore next */
      cov_p8luzfsbt().s[7]++;
      return await this.repositories.projectRepository.getProject(id);
    } catch (error) {
      /* istanbul ignore next */
      cov_p8luzfsbt().s[8]++;
      console.error('Failed to get project:', error);
      /* istanbul ignore next */
      cov_p8luzfsbt().s[9]++;
      throw error;
    }
  }
  async saveProject(project) {
    /* istanbul ignore next */
    cov_p8luzfsbt().f[2]++;
    cov_p8luzfsbt().s[10]++;
    try {
      /* istanbul ignore next */
      cov_p8luzfsbt().s[11]++;
      await this.repositories.projectRepository.saveProject(project);
    } catch (error) {
      /* istanbul ignore next */
      cov_p8luzfsbt().s[12]++;
      console.error('Failed to save project:', error);
      /* istanbul ignore next */
      cov_p8luzfsbt().s[13]++;
      throw error;
    }
  }
  async createProject(data) {
    /* istanbul ignore next */
    cov_p8luzfsbt().f[3]++;
    cov_p8luzfsbt().s[14]++;
    try {
      const newProject =
      /* istanbul ignore next */
      (cov_p8luzfsbt().s[15]++, {
        id:
        /* istanbul ignore next */
        (cov_p8luzfsbt().b[0][0]++, data.id) ||
        /* istanbul ignore next */
        (cov_p8luzfsbt().b[0][1]++, this.generateUUID()),
        userId:
        /* istanbul ignore next */
        (cov_p8luzfsbt().b[1][0]++, data.userId) ||
        /* istanbul ignore next */
        (cov_p8luzfsbt().b[1][1]++, 'offline-user-001'),
        project_name:
        /* istanbul ignore next */
        (cov_p8luzfsbt().b[2][0]++, data.project_name) ||
        /* istanbul ignore next */
        (cov_p8luzfsbt().b[2][1]++, 'New Project'),
        project_number:
        /* istanbul ignore next */
        (cov_p8luzfsbt().b[3][0]++, data.project_number) ||
        /* istanbul ignore next */
        (cov_p8luzfsbt().b[3][1]++, this.generateProjectNumber()),
        project_description:
        /* istanbul ignore next */
        (cov_p8luzfsbt().b[4][0]++, data.project_description) ||
        /* istanbul ignore next */
        (cov_p8luzfsbt().b[4][1]++, ''),
        project_location:
        /* istanbul ignore next */
        (cov_p8luzfsbt().b[5][0]++, data.project_location) ||
        /* istanbul ignore next */
        (cov_p8luzfsbt().b[5][1]++, ''),
        client_name:
        /* istanbul ignore next */
        (cov_p8luzfsbt().b[6][0]++, data.client_name) ||
        /* istanbul ignore next */
        (cov_p8luzfsbt().b[6][1]++, ''),
        estimator_name:
        /* istanbul ignore next */
        (cov_p8luzfsbt().b[7][0]++, data.estimator_name) ||
        /* istanbul ignore next */
        (cov_p8luzfsbt().b[7][1]++, ''),
        date_created:
        /* istanbul ignore next */
        (cov_p8luzfsbt().b[8][0]++, data.date_created) ||
        /* istanbul ignore next */
        (cov_p8luzfsbt().b[8][1]++, new Date().toISOString()),
        last_modified: new Date().toISOString(),
        version:
        /* istanbul ignore next */
        (cov_p8luzfsbt().b[9][0]++, data.version) ||
        /* istanbul ignore next */
        (cov_p8luzfsbt().b[9][1]++, '1.0'),
        rooms:
        /* istanbul ignore next */
        (cov_p8luzfsbt().b[10][0]++, data.rooms) ||
        /* istanbul ignore next */
        (cov_p8luzfsbt().b[10][1]++, []),
        segments:
        /* istanbul ignore next */
        (cov_p8luzfsbt().b[11][0]++, data.segments) ||
        /* istanbul ignore next */
        (cov_p8luzfsbt().b[11][1]++, []),
        equipment:
        /* istanbul ignore next */
        (cov_p8luzfsbt().b[12][0]++, data.equipment) ||
        /* istanbul ignore next */
        (cov_p8luzfsbt().b[12][1]++, []),
        computational_properties:
        /* istanbul ignore next */
        (cov_p8luzfsbt().b[13][0]++, data.computational_properties) ||
        /* istanbul ignore next */
        (cov_p8luzfsbt().b[13][1]++, {
          units: 'Imperial',
          default_duct_size: {
            width: 12,
            height: 8
          },
          default_material: 'Galvanized Steel',
          default_insulation: 'None',
          default_fitting: 'Standard',
          calibration_mode: 'Auto',
          default_velocity: 1000,
          pressure_class: "2",
          altitude: 0,
          friction_rate: 0.1
        }),
        code_standards:
        /* istanbul ignore next */
        (cov_p8luzfsbt().b[14][0]++, data.code_standards) ||
        /* istanbul ignore next */
        (cov_p8luzfsbt().b[14][1]++, {
          smacna: true,
          ashrae: true,
          ul: false,
          imc: false,
          nfpa: false
        })
      });
      /* istanbul ignore next */
      cov_p8luzfsbt().s[16]++;
      await this.repositories.projectRepository.saveProject(newProject);
      /* istanbul ignore next */
      cov_p8luzfsbt().s[17]++;
      return newProject;
    } catch (error) {
      /* istanbul ignore next */
      cov_p8luzfsbt().s[18]++;
      console.error('Failed to create project:', error);
      /* istanbul ignore next */
      cov_p8luzfsbt().s[19]++;
      throw error;
    }
  }
  async deleteProject(id) {
    /* istanbul ignore next */
    cov_p8luzfsbt().f[4]++;
    cov_p8luzfsbt().s[20]++;
    try {
      /* istanbul ignore next */
      cov_p8luzfsbt().s[21]++;
      await this.repositories.projectRepository.deleteProject(id);
    } catch (error) {
      /* istanbul ignore next */
      cov_p8luzfsbt().s[22]++;
      console.error('Failed to delete project:', error);
      /* istanbul ignore next */
      cov_p8luzfsbt().s[23]++;
      throw error;
    }
  }
  async listProjects(userId) {
    /* istanbul ignore next */
    cov_p8luzfsbt().f[5]++;
    cov_p8luzfsbt().s[24]++;
    try {
      /* istanbul ignore next */
      cov_p8luzfsbt().s[25]++;
      return await this.repositories.projectRepository.getProjectsByUser(userId);
    } catch (error) {
      /* istanbul ignore next */
      cov_p8luzfsbt().s[26]++;
      console.error('Failed to list projects:', error);
      /* istanbul ignore next */
      cov_p8luzfsbt().s[27]++;
      throw error;
    }
  }
  generateUUID() {
    /* istanbul ignore next */
    cov_p8luzfsbt().f[6]++;
    cov_p8luzfsbt().s[28]++;
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
      /* istanbul ignore next */
      cov_p8luzfsbt().f[7]++;
      const r =
      /* istanbul ignore next */
      (cov_p8luzfsbt().s[29]++, Math.random() * 16 | 0);
      const v =
      /* istanbul ignore next */
      (cov_p8luzfsbt().s[30]++, c == 'x' ?
      /* istanbul ignore next */
      (cov_p8luzfsbt().b[15][0]++, r) :
      /* istanbul ignore next */
      (cov_p8luzfsbt().b[15][1]++, r & 0x3 | 0x8));
      /* istanbul ignore next */
      cov_p8luzfsbt().s[31]++;
      return v.toString(16);
    });
  }
  generateProjectNumber() {
    /* istanbul ignore next */
    cov_p8luzfsbt().f[8]++;
    const timestamp =
    /* istanbul ignore next */
    (cov_p8luzfsbt().s[32]++, Date.now().toString().slice(-6));
    /* istanbul ignore next */
    cov_p8luzfsbt().s[33]++;
    return `PRJ-${timestamp}`;
  }
}
/* istanbul ignore next */
cov_p8luzfsbt().s[34]++;
exports.OfflineProjectService = OfflineProjectService;
/**
 * User service for offline mode
 */
class OfflineUserService {
  constructor(repositories) {
    /* istanbul ignore next */
    cov_p8luzfsbt().f[9]++;
    cov_p8luzfsbt().s[35]++;
    this.repositories = repositories;
  }
  async getCurrentUser() {
    /* istanbul ignore next */
    cov_p8luzfsbt().f[10]++;
    cov_p8luzfsbt().s[36]++;
    try {
      /* istanbul ignore next */
      cov_p8luzfsbt().s[37]++;
      return await this.repositories.userRepository.getCurrentUser();
    } catch (error) {
      /* istanbul ignore next */
      cov_p8luzfsbt().s[38]++;
      console.error('Failed to get current user:', error);
      /* istanbul ignore next */
      cov_p8luzfsbt().s[39]++;
      throw error;
    }
  }
  async getUserById(id) {
    /* istanbul ignore next */
    cov_p8luzfsbt().f[11]++;
    cov_p8luzfsbt().s[40]++;
    try {
      /* istanbul ignore next */
      cov_p8luzfsbt().s[41]++;
      return await this.repositories.userRepository.getUser(id);
    } catch (error) {
      /* istanbul ignore next */
      cov_p8luzfsbt().s[42]++;
      console.error('Failed to get user:', error);
      /* istanbul ignore next */
      cov_p8luzfsbt().s[43]++;
      throw error;
    }
  }
  async updateUser(user) {
    /* istanbul ignore next */
    cov_p8luzfsbt().f[12]++;
    cov_p8luzfsbt().s[44]++;
    try {
      /* istanbul ignore next */
      cov_p8luzfsbt().s[45]++;
      await this.repositories.userRepository.saveUser(user);
    } catch (error) {
      /* istanbul ignore next */
      cov_p8luzfsbt().s[46]++;
      console.error('Failed to update user:', error);
      /* istanbul ignore next */
      cov_p8luzfsbt().s[47]++;
      throw error;
    }
  }
}
/* istanbul ignore next */
cov_p8luzfsbt().s[48]++;
exports.OfflineUserService = OfflineUserService;
// Stub implementations for missing backend services
class AirDuctCalculator {
  async calculate(input) {
    /* istanbul ignore next */
    cov_p8luzfsbt().f[13]++;
    cov_p8luzfsbt().s[49]++;
    // Stub implementation - would be replaced with actual calculation logic
    throw new Error('AirDuctCalculator not implemented in offline mode');
  }
  async calculateDuctSizing(input) {
    /* istanbul ignore next */
    cov_p8luzfsbt().f[14]++;
    cov_p8luzfsbt().s[50]++;
    // Stub implementation - would be replaced with actual calculation logic
    throw new Error('AirDuctCalculator not implemented in offline mode');
  }
}
class SMACNAValidator {
  async validate(input) {
    /* istanbul ignore next */
    cov_p8luzfsbt().f[15]++;
    cov_p8luzfsbt().s[51]++;
    // Stub implementation - would be replaced with actual validation logic
    return true;
  }
}
/**
 * Calculation service for offline mode
 */
class OfflineCalculationService {
  constructor() {
    /* istanbul ignore next */
    cov_p8luzfsbt().f[16]++;
    cov_p8luzfsbt().s[52]++;
    this.calculator = new AirDuctCalculator();
  }
  async calculateDuctSizing(inputs) {
    /* istanbul ignore next */
    cov_p8luzfsbt().f[17]++;
    cov_p8luzfsbt().s[53]++;
    try {
      /* istanbul ignore next */
      cov_p8luzfsbt().s[54]++;
      return await this.calculator.calculateDuctSizing(inputs);
    } catch (error) {
      /* istanbul ignore next */
      cov_p8luzfsbt().s[55]++;
      console.error('Failed to calculate duct sizing:', error);
      /* istanbul ignore next */
      cov_p8luzfsbt().s[56]++;
      throw error;
    }
  }
  async validateResults(results) {
    /* istanbul ignore next */
    cov_p8luzfsbt().f[18]++;
    cov_p8luzfsbt().s[57]++;
    try {
      /* istanbul ignore next */
      cov_p8luzfsbt().s[58]++;
      // Basic validation for offline mode
      return {
        valid: results.success,
        warnings:
        /* istanbul ignore next */
        (cov_p8luzfsbt().b[16][0]++, results.warnings) ||
        /* istanbul ignore next */
        (cov_p8luzfsbt().b[16][1]++, []),
        errors: results.success ?
        /* istanbul ignore next */
        (cov_p8luzfsbt().b[17][0]++, []) :
        /* istanbul ignore next */
        (cov_p8luzfsbt().b[17][1]++, ['Calculation failed'])
      };
    } catch (error) {
      /* istanbul ignore next */
      cov_p8luzfsbt().s[59]++;
      console.error('Failed to validate results:', error);
      /* istanbul ignore next */
      cov_p8luzfsbt().s[60]++;
      throw error;
    }
  }
  async getCalculationHistory(projectId) {
    /* istanbul ignore next */
    cov_p8luzfsbt().f[19]++;
    cov_p8luzfsbt().s[61]++;
    try {
      /* istanbul ignore next */
      cov_p8luzfsbt().s[62]++;
      // For offline mode, return empty array for now
      // Could be enhanced to store calculation history in database
      return [];
    } catch (error) {
      /* istanbul ignore next */
      cov_p8luzfsbt().s[63]++;
      console.error('Failed to get calculation history:', error);
      /* istanbul ignore next */
      cov_p8luzfsbt().s[64]++;
      throw error;
    }
  }
}
/* istanbul ignore next */
cov_p8luzfsbt().s[65]++;
exports.OfflineCalculationService = OfflineCalculationService;
/**
 * Validation service for offline mode
 */
class OfflineValidationService {
  constructor() {
    /* istanbul ignore next */
    cov_p8luzfsbt().f[20]++;
    cov_p8luzfsbt().s[66]++;
    this.smacnaValidator = new SMACNAValidator();
  }
  async validateProject(project) {
    /* istanbul ignore next */
    cov_p8luzfsbt().f[21]++;
    cov_p8luzfsbt().s[67]++;
    try {
      // Basic project validation
      const errors =
      /* istanbul ignore next */
      (cov_p8luzfsbt().s[68]++, []);
      const warnings =
      /* istanbul ignore next */
      (cov_p8luzfsbt().s[69]++, []);
      /* istanbul ignore next */
      cov_p8luzfsbt().s[70]++;
      if (!project.project_name?.trim()) {
        /* istanbul ignore next */
        cov_p8luzfsbt().b[18][0]++;
        cov_p8luzfsbt().s[71]++;
        errors.push('Project name is required');
      } else
      /* istanbul ignore next */
      {
        cov_p8luzfsbt().b[18][1]++;
      }
      cov_p8luzfsbt().s[72]++;
      if (project.rooms.length === 0) {
        /* istanbul ignore next */
        cov_p8luzfsbt().b[19][0]++;
        cov_p8luzfsbt().s[73]++;
        warnings.push('Project has no rooms defined');
      } else
      /* istanbul ignore next */
      {
        cov_p8luzfsbt().b[19][1]++;
      }
      cov_p8luzfsbt().s[74]++;
      if (project.segments.length === 0) {
        /* istanbul ignore next */
        cov_p8luzfsbt().b[20][0]++;
        cov_p8luzfsbt().s[75]++;
        warnings.push('Project has no duct segments defined');
      } else
      /* istanbul ignore next */
      {
        cov_p8luzfsbt().b[20][1]++;
      }
      cov_p8luzfsbt().s[76]++;
      return {
        valid: errors.length === 0,
        errors,
        warnings
      };
    } catch (error) {
      /* istanbul ignore next */
      cov_p8luzfsbt().s[77]++;
      console.error('Failed to validate project:', error);
      /* istanbul ignore next */
      cov_p8luzfsbt().s[78]++;
      throw error;
    }
  }
}
/* istanbul ignore next */
cov_p8luzfsbt().s[79]++;
exports.OfflineValidationService = OfflineValidationService;
/**
 * Export service for offline mode
 */
class OfflineExportService {
  async exportProject(projectId, options) {
    /* istanbul ignore next */
    cov_p8luzfsbt().f[22]++;
    cov_p8luzfsbt().s[80]++;
    try {
      /* istanbul ignore next */
      cov_p8luzfsbt().s[81]++;
      // Simulate export process for offline mode
      return {
        success: true,
        exportId: `export-${Date.now()}`,
        downloadUrl: `/exports/${projectId}.${options.format}`
      };
    } catch (error) {
      /* istanbul ignore next */
      cov_p8luzfsbt().s[82]++;
      console.error('Failed to export project:', error);
      /* istanbul ignore next */
      cov_p8luzfsbt().s[83]++;
      throw error;
    }
  }
  async getExportStatus(exportId) {
    /* istanbul ignore next */
    cov_p8luzfsbt().f[23]++;
    cov_p8luzfsbt().s[84]++;
    try {
      /* istanbul ignore next */
      cov_p8luzfsbt().s[85]++;
      // Return mock status for offline mode
      return {
        success: true,
        exportId: exportId,
        downloadUrl: `/api/exports/${exportId}/download`
      };
    } catch (error) {
      /* istanbul ignore next */
      cov_p8luzfsbt().s[86]++;
      console.error('Failed to get export status:', error);
      /* istanbul ignore next */
      cov_p8luzfsbt().s[87]++;
      throw error;
    }
  }
  async downloadExport(exportId) {
    /* istanbul ignore next */
    cov_p8luzfsbt().f[24]++;
    cov_p8luzfsbt().s[88]++;
    try {
      /* istanbul ignore next */
      cov_p8luzfsbt().s[89]++;
      // Return empty blob for offline mode
      return new Blob(['Mock export content'], {
        type: 'application/pdf'
      });
    } catch (error) {
      /* istanbul ignore next */
      cov_p8luzfsbt().s[90]++;
      console.error('Failed to download export:', error);
      /* istanbul ignore next */
      cov_p8luzfsbt().s[91]++;
      throw error;
    }
  }
}
/* istanbul ignore next */
cov_p8luzfsbt().s[92]++;
exports.OfflineExportService = OfflineExportService;
/**
 * Tier service for offline mode
 */
class OfflineTierService {
  constructor(repositories, featureManager) {
    /* istanbul ignore next */
    cov_p8luzfsbt().f[25]++;
    cov_p8luzfsbt().s[93]++;
    this.repositories = repositories;
    /* istanbul ignore next */
    cov_p8luzfsbt().s[94]++;
    this.featureManager = featureManager;
  }
  async getCurrentTier() {
    /* istanbul ignore next */
    cov_p8luzfsbt().f[26]++;
    cov_p8luzfsbt().s[95]++;
    try {
      const user =
      /* istanbul ignore next */
      (cov_p8luzfsbt().s[96]++, await this.repositories.userRepository.getCurrentUser());
      /* istanbul ignore next */
      cov_p8luzfsbt().s[97]++;
      return /* istanbul ignore next */(cov_p8luzfsbt().b[21][0]++, user?.tier) ||
      /* istanbul ignore next */
      (cov_p8luzfsbt().b[21][1]++, 'free');
    } catch (error) {
      /* istanbul ignore next */
      cov_p8luzfsbt().s[98]++;
      console.error('Failed to get current tier:', error);
      /* istanbul ignore next */
      cov_p8luzfsbt().s[99]++;
      return 'free';
    }
  }
  async hasFeatureAccess(feature) {
    /* istanbul ignore next */
    cov_p8luzfsbt().f[27]++;
    cov_p8luzfsbt().s[100]++;
    try {
      const user =
      /* istanbul ignore next */
      (cov_p8luzfsbt().s[101]++, await this.repositories.userRepository.getCurrentUser());
      /* istanbul ignore next */
      cov_p8luzfsbt().s[102]++;
      if (!user?.id) {
        /* istanbul ignore next */
        cov_p8luzfsbt().b[22][0]++;
        cov_p8luzfsbt().s[103]++;
        return false;
      } else
      /* istanbul ignore next */
      {
        cov_p8luzfsbt().b[22][1]++;
      }
      const result =
      /* istanbul ignore next */
      (cov_p8luzfsbt().s[104]++, await this.featureManager.isEnabled(feature, user.id));
      /* istanbul ignore next */
      cov_p8luzfsbt().s[105]++;
      return result.enabled;
    } catch (error) {
      /* istanbul ignore next */
      cov_p8luzfsbt().s[106]++;
      console.error('Failed to check feature access:', error);
      /* istanbul ignore next */
      cov_p8luzfsbt().s[107]++;
      return false;
    }
  }
  async getTierLimits() {
    /* istanbul ignore next */
    cov_p8luzfsbt().f[28]++;
    cov_p8luzfsbt().s[108]++;
    try {
      const tier =
      /* istanbul ignore next */
      (cov_p8luzfsbt().s[109]++, await this.getCurrentTier());
      // Define tier limits for offline mode
      const limits =
      /* istanbul ignore next */
      (cov_p8luzfsbt().s[110]++, {
        free: {
          maxRooms: 5,
          maxSegments: 10,
          maxProjects: 3,
          canEditComputationalProperties: false,
          canExportWithoutWatermark: false,
          canUseSimulation: false,
          canUseCatalog: false
        },
        pro: {
          maxRooms: 50,
          maxSegments: 100,
          maxProjects: -1,
          // Unlimited
          canEditComputationalProperties: true,
          canExportWithoutWatermark: true,
          canUseSimulation: true,
          canUseCatalog: true
        },
        enterprise: {
          maxRooms: -1,
          // Unlimited
          maxSegments: -1,
          // Unlimited
          maxProjects: -1,
          // Unlimited
          canEditComputationalProperties: true,
          canExportWithoutWatermark: true,
          canUseSimulation: true,
          canUseCatalog: true
        },
        super_admin: {
          maxRooms: -1,
          // Unlimited
          maxSegments: -1,
          // Unlimited
          maxProjects: -1,
          // Unlimited
          canEditComputationalProperties: true,
          canExportWithoutWatermark: true,
          canUseSimulation: true,
          canUseCatalog: true
        }
      });
      /* istanbul ignore next */
      cov_p8luzfsbt().s[111]++;
      return limits[tier];
    } catch (error) {
      /* istanbul ignore next */
      cov_p8luzfsbt().s[112]++;
      console.error('Failed to get tier limits:', error);
      /* istanbul ignore next */
      cov_p8luzfsbt().s[113]++;
      return {
        maxRooms: 5,
        maxSegments: 10,
        maxProjects: 3,
        canEditComputationalProperties: false,
        canExportWithoutWatermark: false,
        canUseSimulation: false,
        canUseCatalog: false
      };
    }
  }
  async upgradeTier(newTier) {
    /* istanbul ignore next */
    cov_p8luzfsbt().f[29]++;
    cov_p8luzfsbt().s[114]++;
    try {
      const user =
      /* istanbul ignore next */
      (cov_p8luzfsbt().s[115]++, await this.repositories.userRepository.getCurrentUser());
      /* istanbul ignore next */
      cov_p8luzfsbt().s[116]++;
      if (user) {
        /* istanbul ignore next */
        cov_p8luzfsbt().b[23][0]++;
        cov_p8luzfsbt().s[117]++;
        user.tier = newTier;
        /* istanbul ignore next */
        cov_p8luzfsbt().s[118]++;
        user.updatedAt = new Date();
        /* istanbul ignore next */
        cov_p8luzfsbt().s[119]++;
        await this.repositories.userRepository.saveUser(user);
      } else
      /* istanbul ignore next */
      {
        cov_p8luzfsbt().b[23][1]++;
      }
    } catch (error) {
      /* istanbul ignore next */
      cov_p8luzfsbt().s[120]++;
      console.error('Failed to upgrade tier:', error);
      /* istanbul ignore next */
      cov_p8luzfsbt().s[121]++;
      throw error;
    }
  }
}
/* istanbul ignore next */
cov_p8luzfsbt().s[122]++;
exports.OfflineTierService = OfflineTierService;
/**
 * Create offline service container
 */
async function createOfflineServiceContainer() {
  /* istanbul ignore next */
  cov_p8luzfsbt().f[30]++;
  // Initialize database
  const dbInitializer =
  /* istanbul ignore next */
  (cov_p8luzfsbt().s[123]++, DatabaseInitializer_1.DatabaseInitializer.getInstance());
  const dbResult =
  /* istanbul ignore next */
  (cov_p8luzfsbt().s[124]++, await dbInitializer.initialize());
  /* istanbul ignore next */
  cov_p8luzfsbt().s[125]++;
  if (
  /* istanbul ignore next */
  (cov_p8luzfsbt().b[25][0]++, !dbResult.success) ||
  /* istanbul ignore next */
  (cov_p8luzfsbt().b[25][1]++, !dbResult.repositories)) {
    /* istanbul ignore next */
    cov_p8luzfsbt().b[24][0]++;
    cov_p8luzfsbt().s[126]++;
    throw new Error(`Failed to initialize database: ${dbResult.error}`);
  } else
  /* istanbul ignore next */
  {
    cov_p8luzfsbt().b[24][1]++;
  }
  const repositories =
  /* istanbul ignore next */
  (cov_p8luzfsbt().s[127]++, dbResult.repositories);
  // Create feature manager
  const featureManager =
  /* istanbul ignore next */
  (cov_p8luzfsbt().s[128]++, new FeatureManager_1.FeatureManager(dbResult.dbManager));
  // Create services
  const projectService =
  /* istanbul ignore next */
  (cov_p8luzfsbt().s[129]++, new OfflineProjectService(repositories));
  const userService =
  /* istanbul ignore next */
  (cov_p8luzfsbt().s[130]++, new OfflineUserService(repositories));
  const calculationService =
  /* istanbul ignore next */
  (cov_p8luzfsbt().s[131]++, new OfflineCalculationService());
  const validationService =
  /* istanbul ignore next */
  (cov_p8luzfsbt().s[132]++, new OfflineValidationService());
  const exportService =
  /* istanbul ignore next */
  (cov_p8luzfsbt().s[133]++, new OfflineExportService());
  const tierService =
  /* istanbul ignore next */
  (cov_p8luzfsbt().s[134]++, new OfflineTierService(repositories, featureManager));
  /* istanbul ignore next */
  cov_p8luzfsbt().s[135]++;
  return {
    projectService,
    calculationService,
    validationService,
    exportService,
    tierService,
    featureManager,
    userService
  };
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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