{"version": 3, "names": ["cov_p8luzfsbt", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "name", "decl", "loc", "branchMap", "type", "locations", "undefined", "s", "f", "b", "inputSourceMap", "file", "mappings", "names", "sources", "sourcesContent", "version", "_coverageSchema", "coverage", "actualCoverage", "exports", "createOfflineServiceContainer", "FeatureManager_1", "require", "DatabaseInitializer_1", "OfflineProjectService", "constructor", "repositories", "getProject", "id", "projectRepository", "error", "console", "saveProject", "project", "createProject", "data", "newProject", "generateUUID", "userId", "project_name", "project_number", "generateProjectNumber", "project_description", "project_location", "client_name", "estimator_name", "date_created", "Date", "toISOString", "last_modified", "rooms", "segments", "equipment", "computational_properties", "units", "default_duct_size", "width", "height", "default_material", "default_insulation", "default_fitting", "calibration_mode", "default_velocity", "pressure_class", "altitude", "friction_rate", "code_standards", "smacna", "ashrae", "ul", "imc", "nfpa", "deleteProject", "listProjects", "getProjectsByUser", "replace", "c", "r", "Math", "random", "v", "toString", "timestamp", "now", "slice", "OfflineUserService", "getCurrentUser", "userRepository", "getUserById", "getUser", "updateUser", "user", "saveUser", "AirDuctCalculator", "calculate", "input", "Error", "calculateDuctSizing", "SMACNAValidator", "validate", "OfflineCalculationService", "calculator", "inputs", "validateResults", "results", "valid", "success", "warnings", "errors", "getCalculationHistory", "projectId", "OfflineValidationService", "smacnaValidator", "validateProject", "trim", "push", "length", "OfflineExportService", "exportProject", "options", "exportId", "downloadUrl", "format", "getExportStatus", "downloadExport", "Blob", "OfflineTierService", "featureManager", "getCurrentTier", "tier", "hasFeatureAccess", "feature", "result", "isEnabled", "enabled", "getTierLimits", "limits", "free", "maxRooms", "maxSegments", "maxProjects", "canEditComputationalProperties", "canExportWithoutWatermark", "canUseSimulation", "canUseCatalog", "pro", "enterprise", "super_admin", "upgradeTier", "newTier", "updatedAt", "dbInitializer", "DatabaseInitializer", "getInstance", "dbR<PERSON>ult", "initialize", "FeatureManager", "db<PERSON><PERSON><PERSON>", "projectService", "userService", "calculationService", "validationService", "exportService", "tierService"], "sources": ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\frontend\\lib\\services\\OfflineServiceContainer.ts"], "sourcesContent": ["/**\r\n * Offline Service Container\r\n * \r\n * Real service implementations for Phase 1 offline desktop mode.\r\n * Connects to local SQLite database and provides business logic services.\r\n * \r\n * @see docs/refactoring/component-architecture-specification.md\r\n */\r\n\r\nimport { createDataService, DataService } from '../data/DataService';\r\nimport { FeatureManager } from '../features/FeatureManager';\r\nimport { RepositoryContainer, DatabaseInitializer } from '../database/DatabaseInitializer';\r\n// import { TierEnforcer } from '../../../backend/services/enforcement/TierEnforcer';\r\n// import { AirDuctCalculator } from '../../../backend/services/calculations/AirDuctCalculator';\r\n// import { SMACNAValidator } from '../../../backend/services/calculations/SMACNAValidator';\r\nimport {\r\n  CalculationInput,\r\n  CalculationResult,\r\n  ExportOptions,\r\n  ExportResult,\r\n  TierLimits\r\n} from '../../types/air-duct-sizer';\r\nimport { Project } from '../repositories/interfaces/ProjectRepository';\r\nimport { User, UserTier } from '../repositories/interfaces/UserRepository';\r\n\r\n/**\r\n * Service container interface for dependency injection\r\n */\r\nexport interface OfflineServiceContainer {\r\n  projectService: OfflineProjectService;\r\n  calculationService: OfflineCalculationService;\r\n  validationService: OfflineValidationService;\r\n  exportService: OfflineExportService;\r\n  tierService: OfflineTierService;\r\n  featureManager: FeatureManager;\r\n  userService: OfflineUserService;\r\n}\r\n\r\n/**\r\n * Project service for offline mode\r\n */\r\nexport class OfflineProjectService {\r\n  constructor(private repositories: RepositoryContainer) {}\r\n\r\n  async getProject(id: string): Promise<Project | null> {\r\n    try {\r\n      return await this.repositories.projectRepository.getProject(id);\r\n    } catch (error) {\r\n      console.error('Failed to get project:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async saveProject(project: Project): Promise<void> {\r\n    try {\r\n      await this.repositories.projectRepository.saveProject(project);\r\n    } catch (error) {\r\n      console.error('Failed to save project:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async createProject(data: Partial<Project>): Promise<Project> {\r\n    try {\r\n      const newProject: Project = {\r\n        id: data.id || this.generateUUID(),\r\n        userId: data.userId || 'offline-user-001',\r\n        project_name: data.project_name || 'New Project',\r\n        project_number: data.project_number || this.generateProjectNumber(),\r\n        project_description: data.project_description || '',\r\n        project_location: data.project_location || '',\r\n        client_name: data.client_name || '',\r\n        estimator_name: data.estimator_name || '',\r\n        date_created: data.date_created || new Date().toISOString(),\r\n        last_modified: new Date().toISOString(),\r\n        version: data.version || '1.0',\r\n        rooms: data.rooms || [],\r\n        segments: data.segments || [],\r\n        equipment: data.equipment || [],\r\n        computational_properties: data.computational_properties || {\r\n          units: 'Imperial',\r\n          default_duct_size: { width: 12, height: 8 },\r\n          default_material: 'Galvanized Steel',\r\n          default_insulation: 'None',\r\n          default_fitting: 'Standard',\r\n          calibration_mode: 'Auto',\r\n          default_velocity: 1000,\r\n          pressure_class: \"2\",\r\n          altitude: 0,\r\n          friction_rate: 0.1\r\n        },\r\n        code_standards: data.code_standards || {\r\n          smacna: true,\r\n          ashrae: true,\r\n          ul: false,\r\n          imc: false,\r\n          nfpa: false\r\n        }\r\n      };\r\n\r\n      await this.repositories.projectRepository.saveProject(newProject);\r\n      return newProject;\r\n    } catch (error) {\r\n      console.error('Failed to create project:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async deleteProject(id: string): Promise<void> {\r\n    try {\r\n      await this.repositories.projectRepository.deleteProject(id);\r\n    } catch (error) {\r\n      console.error('Failed to delete project:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async listProjects(userId: string): Promise<Project[]> {\r\n    try {\r\n      return await this.repositories.projectRepository.getProjectsByUser(userId);\r\n    } catch (error) {\r\n      console.error('Failed to list projects:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  private generateUUID(): string {\r\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\r\n      const r = Math.random() * 16 | 0;\r\n      const v = c == 'x' ? r : (r & 0x3 | 0x8);\r\n      return v.toString(16);\r\n    });\r\n  }\r\n\r\n  private generateProjectNumber(): string {\r\n    const timestamp = Date.now().toString().slice(-6);\r\n    return `PRJ-${timestamp}`;\r\n  }\r\n}\r\n\r\n/**\r\n * User service for offline mode\r\n */\r\nexport class OfflineUserService {\r\n  constructor(private repositories: RepositoryContainer) {}\r\n\r\n  async getCurrentUser(): Promise<User | null> {\r\n    try {\r\n      return await this.repositories.userRepository.getCurrentUser();\r\n    } catch (error) {\r\n      console.error('Failed to get current user:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async getUserById(id: string): Promise<User | null> {\r\n    try {\r\n      return await this.repositories.userRepository.getUser(id);\r\n    } catch (error) {\r\n      console.error('Failed to get user:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async updateUser(user: User): Promise<void> {\r\n    try {\r\n      await this.repositories.userRepository.saveUser(user);\r\n    } catch (error) {\r\n      console.error('Failed to update user:', error);\r\n      throw error;\r\n    }\r\n  }\r\n}\r\n\r\n// Stub implementations for missing backend services\r\nclass AirDuctCalculator {\r\n  async calculate(input: CalculationInput): Promise<CalculationResult> {\r\n    // Stub implementation - would be replaced with actual calculation logic\r\n    throw new Error('AirDuctCalculator not implemented in offline mode');\r\n  }\r\n\r\n  async calculateDuctSizing(input: CalculationInput): Promise<CalculationResult> {\r\n    // Stub implementation - would be replaced with actual calculation logic\r\n    throw new Error('AirDuctCalculator not implemented in offline mode');\r\n  }\r\n}\r\n\r\nclass SMACNAValidator {\r\n  async validate(input: CalculationInput): Promise<boolean> {\r\n    // Stub implementation - would be replaced with actual validation logic\r\n    return true;\r\n  }\r\n}\r\n\r\n/**\r\n * Calculation service for offline mode\r\n */\r\nexport class OfflineCalculationService {\r\n  private calculator: AirDuctCalculator;\r\n\r\n  constructor() {\r\n    this.calculator = new AirDuctCalculator();\r\n  }\r\n\r\n  async calculateDuctSizing(inputs: CalculationInput): Promise<CalculationResult> {\r\n    try {\r\n      return await this.calculator.calculateDuctSizing(inputs);\r\n    } catch (error) {\r\n      console.error('Failed to calculate duct sizing:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async validateResults(results: CalculationResult): Promise<any> {\r\n    try {\r\n      // Basic validation for offline mode\r\n      return {\r\n        valid: results.success,\r\n        warnings: results.warnings || [],\r\n        errors: results.success ? [] : ['Calculation failed']\r\n      };\r\n    } catch (error) {\r\n      console.error('Failed to validate results:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async getCalculationHistory(projectId: string): Promise<CalculationResult[]> {\r\n    try {\r\n      // For offline mode, return empty array for now\r\n      // Could be enhanced to store calculation history in database\r\n      return [];\r\n    } catch (error) {\r\n      console.error('Failed to get calculation history:', error);\r\n      throw error;\r\n    }\r\n  }\r\n}\r\n\r\n/**\r\n * Validation service for offline mode\r\n */\r\nexport class OfflineValidationService {\r\n  private smacnaValidator: SMACNAValidator;\r\n\r\n  constructor() {\r\n    this.smacnaValidator = new SMACNAValidator();\r\n  }\r\n\r\n  async validateProject(project: Project): Promise<any> {\r\n    try {\r\n      // Basic project validation\r\n      const errors = [];\r\n      const warnings = [];\r\n\r\n      if (!project.project_name?.trim()) {\r\n        errors.push('Project name is required');\r\n      }\r\n\r\n      if (project.rooms.length === 0) {\r\n        warnings.push('Project has no rooms defined');\r\n      }\r\n\r\n      if (project.segments.length === 0) {\r\n        warnings.push('Project has no duct segments defined');\r\n      }\r\n\r\n      return {\r\n        valid: errors.length === 0,\r\n        errors,\r\n        warnings\r\n      };\r\n    } catch (error) {\r\n      console.error('Failed to validate project:', error);\r\n      throw error;\r\n    }\r\n  }\r\n}\r\n\r\n/**\r\n * Export service for offline mode\r\n */\r\nexport class OfflineExportService {\r\n  async exportProject(projectId: string, options: ExportOptions): Promise<ExportResult> {\r\n    try {\r\n      // Simulate export process for offline mode\r\n      return {\r\n        success: true,\r\n        exportId: `export-${Date.now()}`,\r\n        downloadUrl: `/exports/${projectId}.${options.format}`\r\n      };\r\n    } catch (error) {\r\n      console.error('Failed to export project:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async getExportStatus(exportId: string): Promise<ExportResult> {\r\n    try {\r\n      // Return mock status for offline mode\r\n      return {\r\n        success: true,\r\n        exportId: exportId,\r\n        downloadUrl: `/api/exports/${exportId}/download`\r\n      };\r\n    } catch (error) {\r\n      console.error('Failed to get export status:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async downloadExport(exportId: string): Promise<Blob> {\r\n    try {\r\n      // Return empty blob for offline mode\r\n      return new Blob(['Mock export content'], { type: 'application/pdf' });\r\n    } catch (error) {\r\n      console.error('Failed to download export:', error);\r\n      throw error;\r\n    }\r\n  }\r\n}\r\n\r\n/**\r\n * Tier service for offline mode\r\n */\r\nexport class OfflineTierService {\r\n  constructor(\r\n    private repositories: RepositoryContainer,\r\n    private featureManager: FeatureManager\r\n  ) {}\r\n\r\n  async getCurrentTier(): Promise<UserTier> {\r\n    try {\r\n      const user = await this.repositories.userRepository.getCurrentUser();\r\n      return user?.tier || 'free';\r\n    } catch (error) {\r\n      console.error('Failed to get current tier:', error);\r\n      return 'free';\r\n    }\r\n  }\r\n\r\n  async hasFeatureAccess(feature: string): Promise<boolean> {\r\n    try {\r\n      const user = await this.repositories.userRepository.getCurrentUser();\r\n      if (!user?.id) {\r\n        return false;\r\n      }\r\n      const result = await this.featureManager.isEnabled(feature, user.id);\r\n      return result.enabled;\r\n    } catch (error) {\r\n      console.error('Failed to check feature access:', error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  async getTierLimits(): Promise<TierLimits> {\r\n    try {\r\n      const tier = await this.getCurrentTier();\r\n      \r\n      // Define tier limits for offline mode\r\n      const limits: Record<UserTier, TierLimits> = {\r\n        free: {\r\n          maxRooms: 5,\r\n          maxSegments: 10,\r\n          maxProjects: 3,\r\n          canEditComputationalProperties: false,\r\n          canExportWithoutWatermark: false,\r\n          canUseSimulation: false,\r\n          canUseCatalog: false\r\n        },\r\n        pro: {\r\n          maxRooms: 50,\r\n          maxSegments: 100,\r\n          maxProjects: -1, // Unlimited\r\n          canEditComputationalProperties: true,\r\n          canExportWithoutWatermark: true,\r\n          canUseSimulation: true,\r\n          canUseCatalog: true\r\n        },\r\n        enterprise: {\r\n          maxRooms: -1, // Unlimited\r\n          maxSegments: -1, // Unlimited\r\n          maxProjects: -1, // Unlimited\r\n          canEditComputationalProperties: true,\r\n          canExportWithoutWatermark: true,\r\n          canUseSimulation: true,\r\n          canUseCatalog: true\r\n        },\r\n        super_admin: {\r\n          maxRooms: -1, // Unlimited\r\n          maxSegments: -1, // Unlimited\r\n          maxProjects: -1, // Unlimited\r\n          canEditComputationalProperties: true,\r\n          canExportWithoutWatermark: true,\r\n          canUseSimulation: true,\r\n          canUseCatalog: true\r\n        }\r\n      };\r\n\r\n      return limits[tier];\r\n    } catch (error) {\r\n      console.error('Failed to get tier limits:', error);\r\n      return {\r\n        maxRooms: 5,\r\n        maxSegments: 10,\r\n        maxProjects: 3,\r\n        canEditComputationalProperties: false,\r\n        canExportWithoutWatermark: false,\r\n        canUseSimulation: false,\r\n        canUseCatalog: false\r\n      };\r\n    }\r\n  }\r\n\r\n  async upgradeTier(newTier: UserTier): Promise<void> {\r\n    try {\r\n      const user = await this.repositories.userRepository.getCurrentUser();\r\n      if (user) {\r\n        user.tier = newTier;\r\n        user.updatedAt = new Date();\r\n        await this.repositories.userRepository.saveUser(user);\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to upgrade tier:', error);\r\n      throw error;\r\n    }\r\n  }\r\n}\r\n\r\n/**\r\n * Create offline service container\r\n */\r\nexport async function createOfflineServiceContainer(): Promise<OfflineServiceContainer> {\r\n  // Initialize database\r\n  const dbInitializer = DatabaseInitializer.getInstance();\r\n  const dbResult = await dbInitializer.initialize();\r\n  \r\n  if (!dbResult.success || !dbResult.repositories) {\r\n    throw new Error(`Failed to initialize database: ${dbResult.error}`);\r\n  }\r\n\r\n  const repositories = dbResult.repositories;\r\n\r\n  // Create feature manager\r\n  const featureManager = new FeatureManager(dbResult.dbManager!);\r\n\r\n  // Create services\r\n  const projectService = new OfflineProjectService(repositories);\r\n  const userService = new OfflineUserService(repositories);\r\n  const calculationService = new OfflineCalculationService();\r\n  const validationService = new OfflineValidationService();\r\n  const exportService = new OfflineExportService();\r\n  const tierService = new OfflineTierService(repositories, featureManager);\r\n\r\n  return {\r\n    projectService,\r\n    calculationService,\r\n    validationService,\r\n    exportService,\r\n    tierService,\r\n    featureManager,\r\n    userService\r\n  };\r\n}\r\n"], "mappings": ";;AAAA;;;;;;;;AAAA;AAAA,SAAAA,cAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;MAAA;QAAAC,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAO,SAAA;MAAA;QAAAD,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAW,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,cAAA;MAAAC,IAAA;MAAAC,QAAA;MAAAC,KAAA;MAAAC,OAAA;MAAAC,cAAA;MAAAC,OAAA;IAAA;IAAAC,eAAA;IAAA5B,IAAA;EAAA;EAAA,IAAA6B,QAAA,GAAA5B,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAA0B,QAAA,CAAA9B,IAAA,KAAA8B,QAAA,CAAA9B,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAA6B,QAAA,CAAA9B,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAA0B,cAAA,GAAAD,QAAA,CAAA9B,IAAA;EAAA;;;;;;;;;;;;;;;;;;AAgbAgC,OAAA,CAAAC,6BAAA,GAAAA,6BAAA;AAtaA,MAAAC,gBAAA;AAAA;AAAA,CAAAnC,aAAA,GAAAoB,CAAA,OAAAgB,OAAA;AACA,MAAAC,qBAAA;AAAA;AAAA,CAAArC,aAAA,GAAAoB,CAAA,OAAAgB,OAAA;AA2BA;;;AAGA,MAAaE,qBAAqB;EAChCC,YAAoBC,YAAiC;IAAA;IAAAxC,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAAjC,KAAAoB,YAAY,GAAZA,YAAY;EAAwB;EAExD,MAAMC,UAAUA,CAACC,EAAU;IAAA;IAAA1C,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACzB,IAAI;MAAA;MAAApB,aAAA,GAAAoB,CAAA;MACF,OAAO,MAAM,IAAI,CAACoB,YAAY,CAACG,iBAAiB,CAACF,UAAU,CAACC,EAAE,CAAC;IACjE,CAAC,CAAC,OAAOE,KAAK,EAAE;MAAA;MAAA5C,aAAA,GAAAoB,CAAA;MACdyB,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAAC;MAAA5C,aAAA,GAAAoB,CAAA;MAC/C,MAAMwB,KAAK;IACb;EACF;EAEA,MAAME,WAAWA,CAACC,OAAgB;IAAA;IAAA/C,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAChC,IAAI;MAAA;MAAApB,aAAA,GAAAoB,CAAA;MACF,MAAM,IAAI,CAACoB,YAAY,CAACG,iBAAiB,CAACG,WAAW,CAACC,OAAO,CAAC;IAChE,CAAC,CAAC,OAAOH,KAAK,EAAE;MAAA;MAAA5C,aAAA,GAAAoB,CAAA;MACdyB,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAAC;MAAA5C,aAAA,GAAAoB,CAAA;MAChD,MAAMwB,KAAK;IACb;EACF;EAEA,MAAMI,aAAaA,CAACC,IAAsB;IAAA;IAAAjD,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACxC,IAAI;MACF,MAAM8B,UAAU;MAAA;MAAA,CAAAlD,aAAA,GAAAoB,CAAA,QAAY;QAC1BsB,EAAE;QAAE;QAAA,CAAA1C,aAAA,GAAAsB,CAAA,UAAA2B,IAAI,CAACP,EAAE;QAAA;QAAA,CAAA1C,aAAA,GAAAsB,CAAA,UAAI,IAAI,CAAC6B,YAAY,EAAE;QAClCC,MAAM;QAAE;QAAA,CAAApD,aAAA,GAAAsB,CAAA,UAAA2B,IAAI,CAACG,MAAM;QAAA;QAAA,CAAApD,aAAA,GAAAsB,CAAA,UAAI,kBAAkB;QACzC+B,YAAY;QAAE;QAAA,CAAArD,aAAA,GAAAsB,CAAA,UAAA2B,IAAI,CAACI,YAAY;QAAA;QAAA,CAAArD,aAAA,GAAAsB,CAAA,UAAI,aAAa;QAChDgC,cAAc;QAAE;QAAA,CAAAtD,aAAA,GAAAsB,CAAA,UAAA2B,IAAI,CAACK,cAAc;QAAA;QAAA,CAAAtD,aAAA,GAAAsB,CAAA,UAAI,IAAI,CAACiC,qBAAqB,EAAE;QACnEC,mBAAmB;QAAE;QAAA,CAAAxD,aAAA,GAAAsB,CAAA,UAAA2B,IAAI,CAACO,mBAAmB;QAAA;QAAA,CAAAxD,aAAA,GAAAsB,CAAA,UAAI,EAAE;QACnDmC,gBAAgB;QAAE;QAAA,CAAAzD,aAAA,GAAAsB,CAAA,UAAA2B,IAAI,CAACQ,gBAAgB;QAAA;QAAA,CAAAzD,aAAA,GAAAsB,CAAA,UAAI,EAAE;QAC7CoC,WAAW;QAAE;QAAA,CAAA1D,aAAA,GAAAsB,CAAA,UAAA2B,IAAI,CAACS,WAAW;QAAA;QAAA,CAAA1D,aAAA,GAAAsB,CAAA,UAAI,EAAE;QACnCqC,cAAc;QAAE;QAAA,CAAA3D,aAAA,GAAAsB,CAAA,UAAA2B,IAAI,CAACU,cAAc;QAAA;QAAA,CAAA3D,aAAA,GAAAsB,CAAA,UAAI,EAAE;QACzCsC,YAAY;QAAE;QAAA,CAAA5D,aAAA,GAAAsB,CAAA,UAAA2B,IAAI,CAACW,YAAY;QAAA;QAAA,CAAA5D,aAAA,GAAAsB,CAAA,UAAI,IAAIuC,IAAI,EAAE,CAACC,WAAW,EAAE;QAC3DC,aAAa,EAAE,IAAIF,IAAI,EAAE,CAACC,WAAW,EAAE;QACvCjC,OAAO;QAAE;QAAA,CAAA7B,aAAA,GAAAsB,CAAA,UAAA2B,IAAI,CAACpB,OAAO;QAAA;QAAA,CAAA7B,aAAA,GAAAsB,CAAA,UAAI,KAAK;QAC9B0C,KAAK;QAAE;QAAA,CAAAhE,aAAA,GAAAsB,CAAA,WAAA2B,IAAI,CAACe,KAAK;QAAA;QAAA,CAAAhE,aAAA,GAAAsB,CAAA,WAAI,EAAE;QACvB2C,QAAQ;QAAE;QAAA,CAAAjE,aAAA,GAAAsB,CAAA,WAAA2B,IAAI,CAACgB,QAAQ;QAAA;QAAA,CAAAjE,aAAA,GAAAsB,CAAA,WAAI,EAAE;QAC7B4C,SAAS;QAAE;QAAA,CAAAlE,aAAA,GAAAsB,CAAA,WAAA2B,IAAI,CAACiB,SAAS;QAAA;QAAA,CAAAlE,aAAA,GAAAsB,CAAA,WAAI,EAAE;QAC/B6C,wBAAwB;QAAE;QAAA,CAAAnE,aAAA,GAAAsB,CAAA,WAAA2B,IAAI,CAACkB,wBAAwB;QAAA;QAAA,CAAAnE,aAAA,GAAAsB,CAAA,WAAI;UACzD8C,KAAK,EAAE,UAAU;UACjBC,iBAAiB,EAAE;YAAEC,KAAK,EAAE,EAAE;YAAEC,MAAM,EAAE;UAAC,CAAE;UAC3CC,gBAAgB,EAAE,kBAAkB;UACpCC,kBAAkB,EAAE,MAAM;UAC1BC,eAAe,EAAE,UAAU;UAC3BC,gBAAgB,EAAE,MAAM;UACxBC,gBAAgB,EAAE,IAAI;UACtBC,cAAc,EAAE,GAAG;UACnBC,QAAQ,EAAE,CAAC;UACXC,aAAa,EAAE;SAChB;QACDC,cAAc;QAAE;QAAA,CAAAhF,aAAA,GAAAsB,CAAA,WAAA2B,IAAI,CAAC+B,cAAc;QAAA;QAAA,CAAAhF,aAAA,GAAAsB,CAAA,WAAI;UACrC2D,MAAM,EAAE,IAAI;UACZC,MAAM,EAAE,IAAI;UACZC,EAAE,EAAE,KAAK;UACTC,GAAG,EAAE,KAAK;UACVC,IAAI,EAAE;SACP;OACF;MAAC;MAAArF,aAAA,GAAAoB,CAAA;MAEF,MAAM,IAAI,CAACoB,YAAY,CAACG,iBAAiB,CAACG,WAAW,CAACI,UAAU,CAAC;MAAC;MAAAlD,aAAA,GAAAoB,CAAA;MAClE,OAAO8B,UAAU;IACnB,CAAC,CAAC,OAAON,KAAK,EAAE;MAAA;MAAA5C,aAAA,GAAAoB,CAAA;MACdyB,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MAAC;MAAA5C,aAAA,GAAAoB,CAAA;MAClD,MAAMwB,KAAK;IACb;EACF;EAEA,MAAM0C,aAAaA,CAAC5C,EAAU;IAAA;IAAA1C,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAC5B,IAAI;MAAA;MAAApB,aAAA,GAAAoB,CAAA;MACF,MAAM,IAAI,CAACoB,YAAY,CAACG,iBAAiB,CAAC2C,aAAa,CAAC5C,EAAE,CAAC;IAC7D,CAAC,CAAC,OAAOE,KAAK,EAAE;MAAA;MAAA5C,aAAA,GAAAoB,CAAA;MACdyB,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MAAC;MAAA5C,aAAA,GAAAoB,CAAA;MAClD,MAAMwB,KAAK;IACb;EACF;EAEA,MAAM2C,YAAYA,CAACnC,MAAc;IAAA;IAAApD,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAC/B,IAAI;MAAA;MAAApB,aAAA,GAAAoB,CAAA;MACF,OAAO,MAAM,IAAI,CAACoB,YAAY,CAACG,iBAAiB,CAAC6C,iBAAiB,CAACpC,MAAM,CAAC;IAC5E,CAAC,CAAC,OAAOR,KAAK,EAAE;MAAA;MAAA5C,aAAA,GAAAoB,CAAA;MACdyB,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAAC;MAAA5C,aAAA,GAAAoB,CAAA;MACjD,MAAMwB,KAAK;IACb;EACF;EAEQO,YAAYA,CAAA;IAAA;IAAAnD,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAClB,OAAO,sCAAsC,CAACqE,OAAO,CAAC,OAAO,EAAE,UAASC,CAAC;MAAA;MAAA1F,aAAA,GAAAqB,CAAA;MACvE,MAAMsE,CAAC;MAAA;MAAA,CAAA3F,aAAA,GAAAoB,CAAA,QAAGwE,IAAI,CAACC,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC;MAChC,MAAMC,CAAC;MAAA;MAAA,CAAA9F,aAAA,GAAAoB,CAAA,QAAGsE,CAAC,IAAI,GAAG;MAAA;MAAA,CAAA1F,aAAA,GAAAsB,CAAA,WAAGqE,CAAC;MAAA;MAAA,CAAA3F,aAAA,GAAAsB,CAAA,WAAIqE,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;MAAC;MAAA3F,aAAA,GAAAoB,CAAA;MACzC,OAAO0E,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC;IACvB,CAAC,CAAC;EACJ;EAEQxC,qBAAqBA,CAAA;IAAA;IAAAvD,aAAA,GAAAqB,CAAA;IAC3B,MAAM2E,SAAS;IAAA;IAAA,CAAAhG,aAAA,GAAAoB,CAAA,QAAGyC,IAAI,CAACoC,GAAG,EAAE,CAACF,QAAQ,EAAE,CAACG,KAAK,CAAC,CAAC,CAAC,CAAC;IAAC;IAAAlG,aAAA,GAAAoB,CAAA;IAClD,OAAO,OAAO4E,SAAS,EAAE;EAC3B;;AACD;AAAAhG,aAAA,GAAAoB,CAAA;AAjGDa,OAAA,CAAAK,qBAAA,GAAAA,qBAAA;AAmGA;;;AAGA,MAAa6D,kBAAkB;EAC7B5D,YAAoBC,YAAiC;IAAA;IAAAxC,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAAjC,KAAAoB,YAAY,GAAZA,YAAY;EAAwB;EAExD,MAAM4D,cAAcA,CAAA;IAAA;IAAApG,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAClB,IAAI;MAAA;MAAApB,aAAA,GAAAoB,CAAA;MACF,OAAO,MAAM,IAAI,CAACoB,YAAY,CAAC6D,cAAc,CAACD,cAAc,EAAE;IAChE,CAAC,CAAC,OAAOxD,KAAK,EAAE;MAAA;MAAA5C,aAAA,GAAAoB,CAAA;MACdyB,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MAAC;MAAA5C,aAAA,GAAAoB,CAAA;MACpD,MAAMwB,KAAK;IACb;EACF;EAEA,MAAM0D,WAAWA,CAAC5D,EAAU;IAAA;IAAA1C,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAC1B,IAAI;MAAA;MAAApB,aAAA,GAAAoB,CAAA;MACF,OAAO,MAAM,IAAI,CAACoB,YAAY,CAAC6D,cAAc,CAACE,OAAO,CAAC7D,EAAE,CAAC;IAC3D,CAAC,CAAC,OAAOE,KAAK,EAAE;MAAA;MAAA5C,aAAA,GAAAoB,CAAA;MACdyB,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAAC;MAAA5C,aAAA,GAAAoB,CAAA;MAC5C,MAAMwB,KAAK;IACb;EACF;EAEA,MAAM4D,UAAUA,CAACC,IAAU;IAAA;IAAAzG,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACzB,IAAI;MAAA;MAAApB,aAAA,GAAAoB,CAAA;MACF,MAAM,IAAI,CAACoB,YAAY,CAAC6D,cAAc,CAACK,QAAQ,CAACD,IAAI,CAAC;IACvD,CAAC,CAAC,OAAO7D,KAAK,EAAE;MAAA;MAAA5C,aAAA,GAAAoB,CAAA;MACdyB,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAAC;MAAA5C,aAAA,GAAAoB,CAAA;MAC/C,MAAMwB,KAAK;IACb;EACF;;AACD;AAAA5C,aAAA,GAAAoB,CAAA;AA7BDa,OAAA,CAAAkE,kBAAA,GAAAA,kBAAA;AA+BA;AACA,MAAMQ,iBAAiB;EACrB,MAAMC,SAASA,CAACC,KAAuB;IAAA;IAAA7G,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACrC;IACA,MAAM,IAAI0F,KAAK,CAAC,mDAAmD,CAAC;EACtE;EAEA,MAAMC,mBAAmBA,CAACF,KAAuB;IAAA;IAAA7G,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAC/C;IACA,MAAM,IAAI0F,KAAK,CAAC,mDAAmD,CAAC;EACtE;;AAGF,MAAME,eAAe;EACnB,MAAMC,QAAQA,CAACJ,KAAuB;IAAA;IAAA7G,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACpC;IACA,OAAO,IAAI;EACb;;AAGF;;;AAGA,MAAa8F,yBAAyB;EAGpC3E,YAAA;IAAA;IAAAvC,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACE,IAAI,CAAC+F,UAAU,GAAG,IAAIR,iBAAiB,EAAE;EAC3C;EAEA,MAAMI,mBAAmBA,CAACK,MAAwB;IAAA;IAAApH,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAChD,IAAI;MAAA;MAAApB,aAAA,GAAAoB,CAAA;MACF,OAAO,MAAM,IAAI,CAAC+F,UAAU,CAACJ,mBAAmB,CAACK,MAAM,CAAC;IAC1D,CAAC,CAAC,OAAOxE,KAAK,EAAE;MAAA;MAAA5C,aAAA,GAAAoB,CAAA;MACdyB,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MAAC;MAAA5C,aAAA,GAAAoB,CAAA;MACzD,MAAMwB,KAAK;IACb;EACF;EAEA,MAAMyE,eAAeA,CAACC,OAA0B;IAAA;IAAAtH,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAC9C,IAAI;MAAA;MAAApB,aAAA,GAAAoB,CAAA;MACF;MACA,OAAO;QACLmG,KAAK,EAAED,OAAO,CAACE,OAAO;QACtBC,QAAQ;QAAE;QAAA,CAAAzH,aAAA,GAAAsB,CAAA,WAAAgG,OAAO,CAACG,QAAQ;QAAA;QAAA,CAAAzH,aAAA,GAAAsB,CAAA,WAAI,EAAE;QAChCoG,MAAM,EAAEJ,OAAO,CAACE,OAAO;QAAA;QAAA,CAAAxH,aAAA,GAAAsB,CAAA,WAAG,EAAE;QAAA;QAAA,CAAAtB,aAAA,GAAAsB,CAAA,WAAG,CAAC,oBAAoB,CAAC;OACtD;IACH,CAAC,CAAC,OAAOsB,KAAK,EAAE;MAAA;MAAA5C,aAAA,GAAAoB,CAAA;MACdyB,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MAAC;MAAA5C,aAAA,GAAAoB,CAAA;MACpD,MAAMwB,KAAK;IACb;EACF;EAEA,MAAM+E,qBAAqBA,CAACC,SAAiB;IAAA;IAAA5H,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAC3C,IAAI;MAAA;MAAApB,aAAA,GAAAoB,CAAA;MACF;MACA;MACA,OAAO,EAAE;IACX,CAAC,CAAC,OAAOwB,KAAK,EAAE;MAAA;MAAA5C,aAAA,GAAAoB,CAAA;MACdyB,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAAC;MAAA5C,aAAA,GAAAoB,CAAA;MAC3D,MAAMwB,KAAK;IACb;EACF;;AACD;AAAA5C,aAAA,GAAAoB,CAAA;AAxCDa,OAAA,CAAAiF,yBAAA,GAAAA,yBAAA;AA0CA;;;AAGA,MAAaW,wBAAwB;EAGnCtF,YAAA;IAAA;IAAAvC,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACE,IAAI,CAAC0G,eAAe,GAAG,IAAId,eAAe,EAAE;EAC9C;EAEA,MAAMe,eAAeA,CAAChF,OAAgB;IAAA;IAAA/C,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACpC,IAAI;MACF;MACA,MAAMsG,MAAM;MAAA;MAAA,CAAA1H,aAAA,GAAAoB,CAAA,QAAG,EAAE;MACjB,MAAMqG,QAAQ;MAAA;MAAA,CAAAzH,aAAA,GAAAoB,CAAA,QAAG,EAAE;MAAC;MAAApB,aAAA,GAAAoB,CAAA;MAEpB,IAAI,CAAC2B,OAAO,CAACM,YAAY,EAAE2E,IAAI,EAAE,EAAE;QAAA;QAAAhI,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACjCsG,MAAM,CAACO,IAAI,CAAC,0BAA0B,CAAC;MACzC,CAAC;MAAA;MAAA;QAAAjI,aAAA,GAAAsB,CAAA;MAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAED,IAAI2B,OAAO,CAACiB,KAAK,CAACkE,MAAM,KAAK,CAAC,EAAE;QAAA;QAAAlI,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QAC9BqG,QAAQ,CAACQ,IAAI,CAAC,8BAA8B,CAAC;MAC/C,CAAC;MAAA;MAAA;QAAAjI,aAAA,GAAAsB,CAAA;MAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAED,IAAI2B,OAAO,CAACkB,QAAQ,CAACiE,MAAM,KAAK,CAAC,EAAE;QAAA;QAAAlI,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACjCqG,QAAQ,CAACQ,IAAI,CAAC,sCAAsC,CAAC;MACvD,CAAC;MAAA;MAAA;QAAAjI,aAAA,GAAAsB,CAAA;MAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAED,OAAO;QACLmG,KAAK,EAAEG,MAAM,CAACQ,MAAM,KAAK,CAAC;QAC1BR,MAAM;QACND;OACD;IACH,CAAC,CAAC,OAAO7E,KAAK,EAAE;MAAA;MAAA5C,aAAA,GAAAoB,CAAA;MACdyB,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MAAC;MAAA5C,aAAA,GAAAoB,CAAA;MACpD,MAAMwB,KAAK;IACb;EACF;;AACD;AAAA5C,aAAA,GAAAoB,CAAA;AAnCDa,OAAA,CAAA4F,wBAAA,GAAAA,wBAAA;AAqCA;;;AAGA,MAAaM,oBAAoB;EAC/B,MAAMC,aAAaA,CAACR,SAAiB,EAAES,OAAsB;IAAA;IAAArI,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAC3D,IAAI;MAAA;MAAApB,aAAA,GAAAoB,CAAA;MACF;MACA,OAAO;QACLoG,OAAO,EAAE,IAAI;QACbc,QAAQ,EAAE,UAAUzE,IAAI,CAACoC,GAAG,EAAE,EAAE;QAChCsC,WAAW,EAAE,YAAYX,SAAS,IAAIS,OAAO,CAACG,MAAM;OACrD;IACH,CAAC,CAAC,OAAO5F,KAAK,EAAE;MAAA;MAAA5C,aAAA,GAAAoB,CAAA;MACdyB,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MAAC;MAAA5C,aAAA,GAAAoB,CAAA;MAClD,MAAMwB,KAAK;IACb;EACF;EAEA,MAAM6F,eAAeA,CAACH,QAAgB;IAAA;IAAAtI,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACpC,IAAI;MAAA;MAAApB,aAAA,GAAAoB,CAAA;MACF;MACA,OAAO;QACLoG,OAAO,EAAE,IAAI;QACbc,QAAQ,EAAEA,QAAQ;QAClBC,WAAW,EAAE,gBAAgBD,QAAQ;OACtC;IACH,CAAC,CAAC,OAAO1F,KAAK,EAAE;MAAA;MAAA5C,aAAA,GAAAoB,CAAA;MACdyB,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MAAC;MAAA5C,aAAA,GAAAoB,CAAA;MACrD,MAAMwB,KAAK;IACb;EACF;EAEA,MAAM8F,cAAcA,CAACJ,QAAgB;IAAA;IAAAtI,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACnC,IAAI;MAAA;MAAApB,aAAA,GAAAoB,CAAA;MACF;MACA,OAAO,IAAIuH,IAAI,CAAC,CAAC,qBAAqB,CAAC,EAAE;QAAE1H,IAAI,EAAE;MAAiB,CAAE,CAAC;IACvE,CAAC,CAAC,OAAO2B,KAAK,EAAE;MAAA;MAAA5C,aAAA,GAAAoB,CAAA;MACdyB,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAAC;MAAA5C,aAAA,GAAAoB,CAAA;MACnD,MAAMwB,KAAK;IACb;EACF;;AACD;AAAA5C,aAAA,GAAAoB,CAAA;AAtCDa,OAAA,CAAAkG,oBAAA,GAAAA,oBAAA;AAwCA;;;AAGA,MAAaS,kBAAkB;EAC7BrG,YACUC,YAAiC,EACjCqG,cAA8B;IAAA;IAAA7I,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAD9B,KAAAoB,YAAY,GAAZA,YAAY;IAAqB;IAAAxC,aAAA,GAAAoB,CAAA;IACjC,KAAAyH,cAAc,GAAdA,cAAc;EACrB;EAEH,MAAMC,cAAcA,CAAA;IAAA;IAAA9I,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAClB,IAAI;MACF,MAAMqF,IAAI;MAAA;MAAA,CAAAzG,aAAA,GAAAoB,CAAA,QAAG,MAAM,IAAI,CAACoB,YAAY,CAAC6D,cAAc,CAACD,cAAc,EAAE;MAAC;MAAApG,aAAA,GAAAoB,CAAA;MACrE,OAAO,2BAAApB,aAAA,GAAAsB,CAAA,WAAAmF,IAAI,EAAEsC,IAAI;MAAA;MAAA,CAAA/I,aAAA,GAAAsB,CAAA,WAAI,MAAM;IAC7B,CAAC,CAAC,OAAOsB,KAAK,EAAE;MAAA;MAAA5C,aAAA,GAAAoB,CAAA;MACdyB,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MAAC;MAAA5C,aAAA,GAAAoB,CAAA;MACpD,OAAO,MAAM;IACf;EACF;EAEA,MAAM4H,gBAAgBA,CAACC,OAAe;IAAA;IAAAjJ,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACpC,IAAI;MACF,MAAMqF,IAAI;MAAA;MAAA,CAAAzG,aAAA,GAAAoB,CAAA,SAAG,MAAM,IAAI,CAACoB,YAAY,CAAC6D,cAAc,CAACD,cAAc,EAAE;MAAC;MAAApG,aAAA,GAAAoB,CAAA;MACrE,IAAI,CAACqF,IAAI,EAAE/D,EAAE,EAAE;QAAA;QAAA1C,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACb,OAAO,KAAK;MACd,CAAC;MAAA;MAAA;QAAApB,aAAA,GAAAsB,CAAA;MAAA;MACD,MAAM4H,MAAM;MAAA;MAAA,CAAAlJ,aAAA,GAAAoB,CAAA,SAAG,MAAM,IAAI,CAACyH,cAAc,CAACM,SAAS,CAACF,OAAO,EAAExC,IAAI,CAAC/D,EAAE,CAAC;MAAC;MAAA1C,aAAA,GAAAoB,CAAA;MACrE,OAAO8H,MAAM,CAACE,OAAO;IACvB,CAAC,CAAC,OAAOxG,KAAK,EAAE;MAAA;MAAA5C,aAAA,GAAAoB,CAAA;MACdyB,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MAAC;MAAA5C,aAAA,GAAAoB,CAAA;MACxD,OAAO,KAAK;IACd;EACF;EAEA,MAAMiI,aAAaA,CAAA;IAAA;IAAArJ,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACjB,IAAI;MACF,MAAM2H,IAAI;MAAA;MAAA,CAAA/I,aAAA,GAAAoB,CAAA,SAAG,MAAM,IAAI,CAAC0H,cAAc,EAAE;MAExC;MACA,MAAMQ,MAAM;MAAA;MAAA,CAAAtJ,aAAA,GAAAoB,CAAA,SAAiC;QAC3CmI,IAAI,EAAE;UACJC,QAAQ,EAAE,CAAC;UACXC,WAAW,EAAE,EAAE;UACfC,WAAW,EAAE,CAAC;UACdC,8BAA8B,EAAE,KAAK;UACrCC,yBAAyB,EAAE,KAAK;UAChCC,gBAAgB,EAAE,KAAK;UACvBC,aAAa,EAAE;SAChB;QACDC,GAAG,EAAE;UACHP,QAAQ,EAAE,EAAE;UACZC,WAAW,EAAE,GAAG;UAChBC,WAAW,EAAE,CAAC,CAAC;UAAE;UACjBC,8BAA8B,EAAE,IAAI;UACpCC,yBAAyB,EAAE,IAAI;UAC/BC,gBAAgB,EAAE,IAAI;UACtBC,aAAa,EAAE;SAChB;QACDE,UAAU,EAAE;UACVR,QAAQ,EAAE,CAAC,CAAC;UAAE;UACdC,WAAW,EAAE,CAAC,CAAC;UAAE;UACjBC,WAAW,EAAE,CAAC,CAAC;UAAE;UACjBC,8BAA8B,EAAE,IAAI;UACpCC,yBAAyB,EAAE,IAAI;UAC/BC,gBAAgB,EAAE,IAAI;UACtBC,aAAa,EAAE;SAChB;QACDG,WAAW,EAAE;UACXT,QAAQ,EAAE,CAAC,CAAC;UAAE;UACdC,WAAW,EAAE,CAAC,CAAC;UAAE;UACjBC,WAAW,EAAE,CAAC,CAAC;UAAE;UACjBC,8BAA8B,EAAE,IAAI;UACpCC,yBAAyB,EAAE,IAAI;UAC/BC,gBAAgB,EAAE,IAAI;UACtBC,aAAa,EAAE;;OAElB;MAAC;MAAA9J,aAAA,GAAAoB,CAAA;MAEF,OAAOkI,MAAM,CAACP,IAAI,CAAC;IACrB,CAAC,CAAC,OAAOnG,KAAK,EAAE;MAAA;MAAA5C,aAAA,GAAAoB,CAAA;MACdyB,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAAC;MAAA5C,aAAA,GAAAoB,CAAA;MACnD,OAAO;QACLoI,QAAQ,EAAE,CAAC;QACXC,WAAW,EAAE,EAAE;QACfC,WAAW,EAAE,CAAC;QACdC,8BAA8B,EAAE,KAAK;QACrCC,yBAAyB,EAAE,KAAK;QAChCC,gBAAgB,EAAE,KAAK;QACvBC,aAAa,EAAE;OAChB;IACH;EACF;EAEA,MAAMI,WAAWA,CAACC,OAAiB;IAAA;IAAAnK,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACjC,IAAI;MACF,MAAMqF,IAAI;MAAA;MAAA,CAAAzG,aAAA,GAAAoB,CAAA,SAAG,MAAM,IAAI,CAACoB,YAAY,CAAC6D,cAAc,CAACD,cAAc,EAAE;MAAC;MAAApG,aAAA,GAAAoB,CAAA;MACrE,IAAIqF,IAAI,EAAE;QAAA;QAAAzG,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACRqF,IAAI,CAACsC,IAAI,GAAGoB,OAAO;QAAC;QAAAnK,aAAA,GAAAoB,CAAA;QACpBqF,IAAI,CAAC2D,SAAS,GAAG,IAAIvG,IAAI,EAAE;QAAC;QAAA7D,aAAA,GAAAoB,CAAA;QAC5B,MAAM,IAAI,CAACoB,YAAY,CAAC6D,cAAc,CAACK,QAAQ,CAACD,IAAI,CAAC;MACvD,CAAC;MAAA;MAAA;QAAAzG,aAAA,GAAAsB,CAAA;MAAA;IACH,CAAC,CAAC,OAAOsB,KAAK,EAAE;MAAA;MAAA5C,aAAA,GAAAoB,CAAA;MACdyB,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAAC;MAAA5C,aAAA,GAAAoB,CAAA;MAChD,MAAMwB,KAAK;IACb;EACF;;AACD;AAAA5C,aAAA,GAAAoB,CAAA;AAtGDa,OAAA,CAAA2G,kBAAA,GAAAA,kBAAA;AAwGA;;;AAGO,eAAe1G,6BAA6BA,CAAA;EAAA;EAAAlC,aAAA,GAAAqB,CAAA;EACjD;EACA,MAAMgJ,aAAa;EAAA;EAAA,CAAArK,aAAA,GAAAoB,CAAA,SAAGiB,qBAAA,CAAAiI,mBAAmB,CAACC,WAAW,EAAE;EACvD,MAAMC,QAAQ;EAAA;EAAA,CAAAxK,aAAA,GAAAoB,CAAA,SAAG,MAAMiJ,aAAa,CAACI,UAAU,EAAE;EAAC;EAAAzK,aAAA,GAAAoB,CAAA;EAElD;EAAI;EAAA,CAAApB,aAAA,GAAAsB,CAAA,YAACkJ,QAAQ,CAAChD,OAAO;EAAA;EAAA,CAAAxH,aAAA,GAAAsB,CAAA,WAAI,CAACkJ,QAAQ,CAAChI,YAAY,GAAE;IAAA;IAAAxC,aAAA,GAAAsB,CAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAC/C,MAAM,IAAI0F,KAAK,CAAC,kCAAkC0D,QAAQ,CAAC5H,KAAK,EAAE,CAAC;EACrE,CAAC;EAAA;EAAA;IAAA5C,aAAA,GAAAsB,CAAA;EAAA;EAED,MAAMkB,YAAY;EAAA;EAAA,CAAAxC,aAAA,GAAAoB,CAAA,SAAGoJ,QAAQ,CAAChI,YAAY;EAE1C;EACA,MAAMqG,cAAc;EAAA;EAAA,CAAA7I,aAAA,GAAAoB,CAAA,SAAG,IAAIe,gBAAA,CAAAuI,cAAc,CAACF,QAAQ,CAACG,SAAU,CAAC;EAE9D;EACA,MAAMC,cAAc;EAAA;EAAA,CAAA5K,aAAA,GAAAoB,CAAA,SAAG,IAAIkB,qBAAqB,CAACE,YAAY,CAAC;EAC9D,MAAMqI,WAAW;EAAA;EAAA,CAAA7K,aAAA,GAAAoB,CAAA,SAAG,IAAI+E,kBAAkB,CAAC3D,YAAY,CAAC;EACxD,MAAMsI,kBAAkB;EAAA;EAAA,CAAA9K,aAAA,GAAAoB,CAAA,SAAG,IAAI8F,yBAAyB,EAAE;EAC1D,MAAM6D,iBAAiB;EAAA;EAAA,CAAA/K,aAAA,GAAAoB,CAAA,SAAG,IAAIyG,wBAAwB,EAAE;EACxD,MAAMmD,aAAa;EAAA;EAAA,CAAAhL,aAAA,GAAAoB,CAAA,SAAG,IAAI+G,oBAAoB,EAAE;EAChD,MAAM8C,WAAW;EAAA;EAAA,CAAAjL,aAAA,GAAAoB,CAAA,SAAG,IAAIwH,kBAAkB,CAACpG,YAAY,EAAEqG,cAAc,CAAC;EAAC;EAAA7I,aAAA,GAAAoB,CAAA;EAEzE,OAAO;IACLwJ,cAAc;IACdE,kBAAkB;IAClBC,iBAAiB;IACjBC,aAAa;IACbC,WAAW;IACXpC,cAAc;IACdgC;GACD;AACH", "ignoreList": []}