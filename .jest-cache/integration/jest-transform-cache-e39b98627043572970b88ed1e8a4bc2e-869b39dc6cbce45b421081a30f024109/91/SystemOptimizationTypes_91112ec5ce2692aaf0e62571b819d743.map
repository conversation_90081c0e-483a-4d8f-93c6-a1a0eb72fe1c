{"version": 3, "names": ["cov_168sp8uxfy", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "name", "decl", "loc", "branchMap", "type", "locations", "s", "f", "b", "inputSourceMap", "file", "mappings", "names", "sources", "sourcesContent", "version", "_coverageSchema", "coverage", "actualCoverage", "OptimizationObjective", "exports", "OptimizationAlgorithm", "ConstraintType", "VariableType", "OptimizationStatus"], "sources": ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\types\\SystemOptimizationTypes.ts"], "sourcesContent": ["/**\r\n * System Optimization Type Definitions for Phase 3 Priority 2\r\n * \r\n * Comprehensive TypeScript interfaces for dynamic system optimization including:\r\n * - Multi-objective optimization problems\r\n * - Constraint handling and validation\r\n * - Optimization algorithms and parameters\r\n * - System configuration and variables\r\n * - Performance metrics and results\r\n * \r\n * @version 3.0.0\r\n * <AUTHOR> Suite Development Team\r\n */\r\n\r\n// ============================================================================\r\n// Core Optimization Enums\r\n// ============================================================================\r\n\r\nexport enum OptimizationObjective {\r\n  MINIMIZE_PRESSURE_LOSS = 'minimize_pressure_loss',\r\n  MINIMIZE_ENERGY_CONSUMPTION = 'minimize_energy_consumption',\r\n  MINIMIZE_TOTAL_COST = 'minimize_total_cost',\r\n  MINIMIZE_NOISE_LEVEL = 'minimize_noise_level',\r\n  MAXIMIZE_EFFICIENCY = 'maximize_efficiency',\r\n  MINIMIZE_SPACE_USAGE = 'minimize_space_usage',\r\n  MINIMIZE_MATERIAL_COST = 'minimize_material_cost',\r\n  MINIMIZE_INSTALLATION_TIME = 'minimize_installation_time'\r\n}\r\n\r\nexport enum OptimizationAlgorithm {\r\n  GENETIC_ALGORITHM = 'genetic_algorithm',\r\n  SIMULATED_ANNEALING = 'simulated_annealing',\r\n  PARTICLE_SWARM = 'particle_swarm',\r\n  GRADIENT_DESCENT = 'gradient_descent',\r\n  DIFFERENTIAL_EVOLUTION = 'differential_evolution',\r\n  MULTI_OBJECTIVE_GA = 'multi_objective_ga',\r\n  NSGA_II = 'nsga_ii',\r\n  HYBRID_OPTIMIZATION = 'hybrid_optimization'\r\n}\r\n\r\nexport enum ConstraintType {\r\n  VELOCITY_LIMIT = 'velocity_limit',\r\n  PRESSURE_LIMIT = 'pressure_limit',\r\n  NOISE_LIMIT = 'noise_limit',\r\n  SPACE_CONSTRAINT = 'space_constraint',\r\n  COST_CONSTRAINT = 'cost_constraint',\r\n  CODE_COMPLIANCE = 'code_compliance',\r\n  MATERIAL_AVAILABILITY = 'material_availability',\r\n  INSTALLATION_CONSTRAINT = 'installation_constraint'\r\n}\r\n\r\nexport enum VariableType {\r\n  DUCT_SIZE = 'duct_size',\r\n  FITTING_TYPE = 'fitting_type',\r\n  MATERIAL_TYPE = 'material_type',\r\n  DAMPER_POSITION = 'damper_position',\r\n  FAN_SPEED = 'fan_speed',\r\n  SYSTEM_CONFIGURATION = 'system_configuration',\r\n  ROUTING_PATH = 'routing_path',\r\n  INSULATION_THICKNESS = 'insulation_thickness'\r\n}\r\n\r\nexport enum OptimizationStatus {\r\n  NOT_STARTED = 'not_started',\r\n  INITIALIZING = 'initializing',\r\n  RUNNING = 'running',\r\n  CONVERGED = 'converged',\r\n  MAX_ITERATIONS = 'max_iterations',\r\n  CONSTRAINT_VIOLATION = 'constraint_violation',\r\n  ERROR = 'error',\r\n  CANCELLED = 'cancelled'\r\n}\r\n\r\n// ============================================================================\r\n// Optimization Variables and Bounds\r\n// ============================================================================\r\n\r\nexport interface OptimizationVariable {\r\n  id: string;\r\n  name: string;\r\n  type: VariableType;\r\n  description: string;\r\n  currentValue: number | string;\r\n  bounds: VariableBounds;\r\n  discreteValues?: (number | string)[];\r\n  units?: string;\r\n  precision?: number;\r\n  dependencies?: VariableDependency[];\r\n}\r\n\r\nexport interface VariableBounds {\r\n  minimum: number | string;\r\n  maximum: number | string;\r\n  step?: number;\r\n  allowedValues?: (number | string)[];\r\n  constraints?: BoundConstraint[];\r\n}\r\n\r\nexport interface BoundConstraint {\r\n  condition: string;\r\n  minValue?: number | string;\r\n  maxValue?: number | string;\r\n  description: string;\r\n}\r\n\r\nexport interface VariableDependency {\r\n  dependentVariableId: string;\r\n  relationship: 'linear' | 'inverse' | 'custom';\r\n  coefficient?: number;\r\n  customFunction?: string;\r\n  description: string;\r\n}\r\n\r\n// ============================================================================\r\n// Optimization Constraints\r\n// ============================================================================\r\n\r\nexport interface OptimizationConstraint {\r\n  id: string;\r\n  name: string;\r\n  type: ConstraintType;\r\n  description: string;\r\n  constraintFunction: ConstraintFunction;\r\n  tolerance: number;\r\n  priority: 'low' | 'medium' | 'high' | 'critical';\r\n  penalty?: number;\r\n  active: boolean;\r\n}\r\n\r\nexport interface ConstraintFunction {\r\n  expression: string;\r\n  variables: string[];\r\n  parameters?: { [key: string]: number };\r\n  evaluationMethod: 'analytical' | 'numerical' | 'simulation';\r\n}\r\n\r\nexport interface ConstraintViolation {\r\n  constraintId: string;\r\n  violationType: 'boundary' | 'equality' | 'inequality';\r\n  currentValue: number;\r\n  requiredValue: number;\r\n  severity: 'minor' | 'major' | 'critical';\r\n  penalty: number;\r\n}\r\n\r\n// ============================================================================\r\n// Objective Functions\r\n// ============================================================================\r\n\r\nexport interface ObjectiveFunction {\r\n  id: string;\r\n  objective: OptimizationObjective;\r\n  weight: number;\r\n  description: string;\r\n  evaluationFunction: (variables: OptimizationVariable[]) => number;\r\n  units: string;\r\n  targetValue?: number;\r\n  tolerance?: number;\r\n}\r\n\r\nexport interface MultiObjectiveFunction {\r\n  objectives: ObjectiveFunction[];\r\n  aggregationMethod: 'weighted_sum' | 'pareto_optimal' | 'lexicographic' | 'goal_programming';\r\n  weights?: number[];\r\n  paretoSettings?: ParetoSettings;\r\n}\r\n\r\nexport interface ParetoSettings {\r\n  populationSize: number;\r\n  maxGenerations: number;\r\n  crossoverRate: number;\r\n  mutationRate: number;\r\n  eliteSize: number;\r\n  diversityMaintenance: boolean;\r\n}\r\n\r\n// ============================================================================\r\n// System Configuration\r\n// ============================================================================\r\n\r\nexport interface SystemConfiguration {\r\n  id: string;\r\n  name: string;\r\n  description: string;\r\n  systemType: 'supply' | 'return' | 'exhaust' | 'mixed';\r\n  segments: SystemSegment[];\r\n  designConditions: DesignConditions;\r\n  performanceRequirements: PerformanceRequirements;\r\n  constraints: SystemConstraints;\r\n}\r\n\r\nexport interface SystemSegment {\r\n  id: string;\r\n  segmentType: 'duct' | 'fitting' | 'equipment' | 'terminal';\r\n  geometry: SegmentGeometry;\r\n  material: MaterialProperties;\r\n  flowConditions: FlowConditions;\r\n  optimizationVariables: string[];\r\n  fixedParameters: { [key: string]: any };\r\n}\r\n\r\nexport interface SegmentGeometry {\r\n  shape: 'rectangular' | 'round' | 'oval' | 'custom';\r\n  dimensions: DimensionSet;\r\n  length?: number;\r\n  orientation?: number;\r\n  position?: Position3D;\r\n}\r\n\r\nexport interface DimensionSet {\r\n  width?: number;\r\n  height?: number;\r\n  diameter?: number;\r\n  majorAxis?: number;\r\n  minorAxis?: number;\r\n  customDimensions?: { [key: string]: number };\r\n}\r\n\r\nexport interface Position3D {\r\n  x: number;\r\n  y: number;\r\n  z: number;\r\n}\r\n\r\nexport interface MaterialProperties {\r\n  type: string;\r\n  roughness: number;\r\n  density: number;\r\n  thermalConductivity?: number;\r\n  cost?: number;\r\n  availability?: 'standard' | 'special_order' | 'custom';\r\n}\r\n\r\nexport interface FlowConditions {\r\n  volumeFlow: number;\r\n  velocity: number;\r\n  pressure: number;\r\n  temperature: number;\r\n  density: number;\r\n  viscosity: number;\r\n}\r\n\r\nexport interface DesignConditions {\r\n  temperature: number;\r\n  pressure: number;\r\n  humidity: number;\r\n  elevation: number;\r\n  seasonalVariation?: boolean;\r\n  operatingSchedule?: OperatingSchedule;\r\n}\r\n\r\nexport interface OperatingSchedule {\r\n  dailyProfile: HourlyProfile[];\r\n  weeklyPattern: DailyPattern[];\r\n  seasonalAdjustments: SeasonalAdjustment[];\r\n}\r\n\r\nexport interface HourlyProfile {\r\n  hour: number;\r\n  loadFactor: number;\r\n  temperatureAdjustment: number;\r\n}\r\n\r\nexport interface DailyPattern {\r\n  dayOfWeek: number;\r\n  profileId: string;\r\n  specialConditions?: string[];\r\n}\r\n\r\nexport interface SeasonalAdjustment {\r\n  season: 'spring' | 'summer' | 'fall' | 'winter';\r\n  temperatureRange: [number, number];\r\n  humidityRange: [number, number];\r\n  loadAdjustment: number;\r\n}\r\n\r\nexport interface PerformanceRequirements {\r\n  maxPressureLoss: number;\r\n  maxVelocity: number;\r\n  maxNoiseLevel: number;\r\n  minEfficiency: number;\r\n  targetFlowRates: { [zoneId: string]: number };\r\n  balancingTolerance: number;\r\n}\r\n\r\nexport interface SystemConstraints {\r\n  spaceConstraints: SpaceConstraint[];\r\n  codeRequirements: CodeRequirement[];\r\n  budgetConstraints: BudgetConstraint[];\r\n  installationConstraints: InstallationConstraint[];\r\n}\r\n\r\nexport interface SpaceConstraint {\r\n  area: BoundingBox;\r\n  maxDuctSize: DimensionSet;\r\n  clearanceRequirements: number;\r\n  accessRequirements: AccessRequirement[];\r\n}\r\n\r\nexport interface BoundingBox {\r\n  minX: number;\r\n  maxX: number;\r\n  minY: number;\r\n  maxY: number;\r\n  minZ: number;\r\n  maxZ: number;\r\n}\r\n\r\nexport interface AccessRequirement {\r\n  purpose: 'maintenance' | 'inspection' | 'installation';\r\n  minimumClearance: number;\r\n  frequency: 'daily' | 'weekly' | 'monthly' | 'annual';\r\n}\r\n\r\nexport interface CodeRequirement {\r\n  code: 'SMACNA' | 'ASHRAE' | 'IMC' | 'UMC' | 'local';\r\n  section: string;\r\n  requirement: string;\r\n  complianceCheck: (system: SystemConfiguration) => boolean;\r\n}\r\n\r\nexport interface BudgetConstraint {\r\n  category: 'material' | 'labor' | 'equipment' | 'total';\r\n  maxCost: number;\r\n  currency: string;\r\n  contingency: number;\r\n}\r\n\r\nexport interface InstallationConstraint {\r\n  sequenceRequirements: string[];\r\n  equipmentRequirements: string[];\r\n  timeConstraints: TimeConstraint[];\r\n  skillRequirements: SkillRequirement[];\r\n}\r\n\r\nexport interface TimeConstraint {\r\n  phase: string;\r\n  maxDuration: number;\r\n  dependencies: string[];\r\n  criticalPath: boolean;\r\n}\r\n\r\nexport interface SkillRequirement {\r\n  skill: string;\r\n  level: 'apprentice' | 'journeyman' | 'master';\r\n  certification?: string;\r\n  availability: number;\r\n}\r\n\r\n// ============================================================================\r\n// Optimization Problem Definition\r\n// ============================================================================\r\n\r\nexport interface OptimizationProblem {\r\n  id: string;\r\n  name: string;\r\n  description: string;\r\n  systemConfiguration: SystemConfiguration;\r\n  variables: OptimizationVariable[];\r\n  objectives: MultiObjectiveFunction;\r\n  constraints: OptimizationConstraint[];\r\n  algorithmSettings: AlgorithmSettings;\r\n  convergenceCriteria: ConvergenceCriteria;\r\n  outputRequirements: OutputRequirements;\r\n}\r\n\r\nexport interface AlgorithmSettings {\r\n  algorithm: OptimizationAlgorithm;\r\n  parameters: AlgorithmParameters;\r\n  parallelization: ParallelizationSettings;\r\n  seedValue?: number;\r\n  restartStrategy?: RestartStrategy;\r\n}\r\n\r\nexport interface AlgorithmParameters {\r\n  populationSize?: number;\r\n  maxIterations?: number;\r\n  maxEvaluations?: number;\r\n  crossoverRate?: number;\r\n  mutationRate?: number;\r\n  selectionMethod?: string;\r\n  coolingSchedule?: CoolingSchedule;\r\n  inertiaWeight?: number;\r\n  accelerationCoefficients?: number[];\r\n  stepSize?: number;\r\n  gradientTolerance?: number;\r\n  customParameters?: { [key: string]: any };\r\n}\r\n\r\nexport interface CoolingSchedule {\r\n  initialTemperature: number;\r\n  finalTemperature: number;\r\n  coolingRate: number;\r\n  method: 'linear' | 'exponential' | 'logarithmic' | 'adaptive';\r\n}\r\n\r\nexport interface ParallelizationSettings {\r\n  enabled: boolean;\r\n  maxWorkers?: number;\r\n  chunkSize?: number;\r\n  loadBalancing?: 'static' | 'dynamic';\r\n}\r\n\r\nexport interface RestartStrategy {\r\n  enabled: boolean;\r\n  maxRestarts: number;\r\n  restartCondition: 'stagnation' | 'diversity_loss' | 'time_limit';\r\n  restartParameters: { [key: string]: any };\r\n}\r\n\r\nexport interface ConvergenceCriteria {\r\n  maxIterations: number;\r\n  toleranceValue: number;\r\n  stagnationLimit: number;\r\n  improvementThreshold: number;\r\n  timeLimit?: number;\r\n  customCriteria?: ConvergenceFunction[];\r\n}\r\n\r\nexport interface ConvergenceFunction {\r\n  name: string;\r\n  function: (history: OptimizationHistory) => boolean;\r\n  description: string;\r\n}\r\n\r\nexport interface OutputRequirements {\r\n  includeHistory: boolean;\r\n  detailedAnalysis: boolean;\r\n  sensitivityAnalysis: boolean;\r\n  uncertaintyAnalysis: boolean;\r\n  visualizations: VisualizationRequest[];\r\n  reportFormat: 'json' | 'pdf' | 'excel' | 'html';\r\n}\r\n\r\nexport interface VisualizationRequest {\r\n  type: 'convergence' | 'pareto_front' | 'variable_history' | 'constraint_violations' | 'sensitivity';\r\n  parameters: { [key: string]: any };\r\n}\r\n\r\n// ============================================================================\r\n// Optimization Results\r\n// ============================================================================\r\n\r\nexport interface OptimizationResult {\r\n  problemId: string;\r\n  status: OptimizationStatus;\r\n  bestSolution: OptimizationSolution;\r\n  paretoFront?: OptimizationSolution[];\r\n  statistics: OptimizationStatistics;\r\n  history: OptimizationHistory;\r\n  analysis: ResultAnalysis;\r\n  recommendations: OptimizationRecommendation[];\r\n  warnings: string[];\r\n  errors: string[];\r\n}\r\n\r\nexport interface OptimizationSolution {\r\n  id: string;\r\n  variables: { [variableId: string]: number | string };\r\n  objectiveValues: { [objectiveId: string]: number };\r\n  constraintViolations: ConstraintViolation[];\r\n  feasible: boolean;\r\n  dominationRank?: number;\r\n  crowdingDistance?: number;\r\n  fitness: number;\r\n  systemConfiguration: SystemConfiguration;\r\n  performanceMetrics: SolutionPerformanceMetrics;\r\n}\r\n\r\nexport interface SolutionPerformanceMetrics {\r\n  totalPressureLoss: number;\r\n  energyConsumption: number;\r\n  totalCost: number;\r\n  noiseLevel: number;\r\n  efficiency: number;\r\n  spaceUtilization: number;\r\n  installationComplexity: number;\r\n  maintenanceRequirements: number;\r\n}\r\n\r\nexport interface OptimizationStatistics {\r\n  totalIterations: number;\r\n  totalEvaluations: number;\r\n  convergenceIteration: number;\r\n  executionTime: number;\r\n  bestFitnessHistory: number[];\r\n  averageFitnessHistory: number[];\r\n  diversityHistory: number[];\r\n  constraintViolationHistory: number[];\r\n  algorithmSpecificStats: { [key: string]: any };\r\n}\r\n\r\nexport interface OptimizationHistory {\r\n  iterations: IterationHistory[];\r\n  populationHistory?: PopulationSnapshot[];\r\n  parameterHistory: ParameterHistory[];\r\n  convergenceMetrics: ConvergenceMetrics[];\r\n}\r\n\r\nexport interface IterationHistory {\r\n  iteration: number;\r\n  bestFitness: number;\r\n  averageFitness: number;\r\n  worstFitness: number;\r\n  diversity: number;\r\n  constraintViolations: number;\r\n  timestamp: Date;\r\n}\r\n\r\nexport interface PopulationSnapshot {\r\n  iteration: number;\r\n  population: OptimizationSolution[];\r\n  statistics: PopulationStatistics;\r\n}\r\n\r\nexport interface PopulationStatistics {\r\n  size: number;\r\n  feasibleSolutions: number;\r\n  averageFitness: number;\r\n  fitnessStandardDeviation: number;\r\n  diversityIndex: number;\r\n}\r\n\r\nexport interface ParameterHistory {\r\n  iteration: number;\r\n  parameters: { [parameterName: string]: any };\r\n  adaptiveChanges: string[];\r\n}\r\n\r\nexport interface ConvergenceMetrics {\r\n  iteration: number;\r\n  improvement: number;\r\n  stagnationCount: number;\r\n  convergenceIndicator: number;\r\n  estimatedRemainingIterations?: number;\r\n}\r\n\r\nexport interface ResultAnalysis {\r\n  sensitivityAnalysis?: SensitivityAnalysis;\r\n  uncertaintyAnalysis?: UncertaintyAnalysis;\r\n  tradeoffAnalysis?: TradeoffAnalysis;\r\n  robustnessAnalysis?: RobustnessAnalysis;\r\n}\r\n\r\nexport interface SensitivityAnalysis {\r\n  variableSensitivities: VariableSensitivity[];\r\n  parameterSensitivities: ParameterSensitivity[];\r\n  globalSensitivityIndices: { [variableId: string]: number };\r\n}\r\n\r\nexport interface VariableSensitivity {\r\n  variableId: string;\r\n  sensitivityIndex: number;\r\n  localSensitivity: number;\r\n  interactionEffects: InteractionEffect[];\r\n}\r\n\r\nexport interface InteractionEffect {\r\n  variableIds: string[];\r\n  interactionStrength: number;\r\n  description: string;\r\n}\r\n\r\nexport interface ParameterSensitivity {\r\n  parameterId: string;\r\n  sensitivityIndex: number;\r\n  recommendedRange: [number, number];\r\n}\r\n\r\nexport interface UncertaintyAnalysis {\r\n  inputUncertainties: InputUncertainty[];\r\n  outputUncertainties: OutputUncertainty[];\r\n  confidenceIntervals: ConfidenceInterval[];\r\n  robustnessMeasures: RobustnessMeasure[];\r\n}\r\n\r\nexport interface InputUncertainty {\r\n  variableId: string;\r\n  uncertaintyType: 'normal' | 'uniform' | 'triangular' | 'custom';\r\n  parameters: { [key: string]: number };\r\n  correlations?: { [variableId: string]: number };\r\n}\r\n\r\nexport interface OutputUncertainty {\r\n  objectiveId: string;\r\n  mean: number;\r\n  standardDeviation: number;\r\n  distribution: 'normal' | 'lognormal' | 'custom';\r\n  percentiles: { [percentile: string]: number };\r\n}\r\n\r\nexport interface ConfidenceInterval {\r\n  objectiveId: string;\r\n  confidenceLevel: number;\r\n  lowerBound: number;\r\n  upperBound: number;\r\n}\r\n\r\nexport interface RobustnessMeasure {\r\n  name: string;\r\n  value: number;\r\n  interpretation: string;\r\n}\r\n\r\nexport interface TradeoffAnalysis {\r\n  objectivePairs: ObjectivePair[];\r\n  paretoEfficiency: number;\r\n  kneePoints: OptimizationSolution[];\r\n  tradeoffRecommendations: TradeoffRecommendation[];\r\n}\r\n\r\nexport interface ObjectivePair {\r\n  objective1Id: string;\r\n  objective2Id: string;\r\n  correlationCoefficient: number;\r\n  tradeoffStrength: 'weak' | 'moderate' | 'strong';\r\n  conflictLevel: number;\r\n}\r\n\r\nexport interface TradeoffRecommendation {\r\n  description: string;\r\n  recommendedSolution: OptimizationSolution;\r\n  tradeoffJustification: string;\r\n  alternativeSolutions: OptimizationSolution[];\r\n}\r\n\r\nexport interface RobustnessAnalysis {\r\n  robustSolutions: OptimizationSolution[];\r\n  robustnessMetrics: RobustnessMetric[];\r\n  scenarioAnalysis: ScenarioAnalysis[];\r\n}\r\n\r\nexport interface RobustnessMetric {\r\n  name: string;\r\n  value: number;\r\n  threshold: number;\r\n  passed: boolean;\r\n}\r\n\r\nexport interface ScenarioAnalysis {\r\n  scenarioName: string;\r\n  scenarioParameters: { [key: string]: any };\r\n  solutionPerformance: SolutionPerformanceMetrics;\r\n  performanceDegradation: number;\r\n}\r\n\r\nexport interface OptimizationRecommendation {\r\n  type: 'improvement' | 'alternative' | 'caution' | 'validation';\r\n  priority: 'low' | 'medium' | 'high' | 'critical';\r\n  title: string;\r\n  description: string;\r\n  expectedBenefit: string;\r\n  implementationEffort: 'low' | 'medium' | 'high';\r\n  riskLevel: 'low' | 'medium' | 'high';\r\n  actionItems: string[];\r\n  relatedSolutions?: OptimizationSolution[];\r\n}\r\n\r\n// ============================================================================\r\n// Utility Types\r\n// ============================================================================\r\n\r\nexport type ObjectiveFunctionType = (variables: OptimizationVariable[]) => number;\r\nexport type ConstraintFunctionType = (variables: OptimizationVariable[]) => number;\r\nexport type FitnessFunction = (solution: OptimizationSolution) => number;\r\nexport type SelectionFunction = (population: OptimizationSolution[], count: number) => OptimizationSolution[];\r\nexport type CrossoverFunction = (parent1: OptimizationSolution, parent2: OptimizationSolution) => OptimizationSolution[];\r\nexport type MutationFunction = (solution: OptimizationSolution, rate: number) => OptimizationSolution;\r\n"], "mappings": ";;AAAA;;;;;;;;;;;;;AAAA;AAAA,SAAAA,eAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;MAAA;QAAAC,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAO,SAAA;MAAA;QAAAD,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAU,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,cAAA;MAAAC,IAAA;MAAAC,QAAA;MAAAC,KAAA;MAAAC,OAAA;MAAAC,cAAA;MAAAC,OAAA;IAAA;IAAAC,eAAA;IAAA3B,IAAA;EAAA;EAAA,IAAA4B,QAAA,GAAA3B,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAAyB,QAAA,CAAA7B,IAAA,KAAA6B,QAAA,CAAA7B,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAA4B,QAAA,CAAA7B,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAAyB,cAAA,GAAAD,QAAA,CAAA7B,IAAA;EAAA;;;;;;;;;;;;;;;;AAcA;AACA;AACA;AAEA,IAAY+B,qBASX;AAAA;AAAAhC,cAAA,GAAAmB,CAAA;AATD,WAAYa,qBAAqB;EAAA;EAAAhC,cAAA,GAAAoB,CAAA;EAAApB,cAAA,GAAAmB,CAAA;EAC/Ba,qBAAA,qDAAiD;EAAA;EAAAhC,cAAA,GAAAmB,CAAA;EACjDa,qBAAA,+DAA2D;EAAA;EAAAhC,cAAA,GAAAmB,CAAA;EAC3Da,qBAAA,+CAA2C;EAAA;EAAAhC,cAAA,GAAAmB,CAAA;EAC3Ca,qBAAA,iDAA6C;EAAA;EAAAhC,cAAA,GAAAmB,CAAA;EAC7Ca,qBAAA,+CAA2C;EAAA;EAAAhC,cAAA,GAAAmB,CAAA;EAC3Ca,qBAAA,iDAA6C;EAAA;EAAAhC,cAAA,GAAAmB,CAAA;EAC7Ca,qBAAA,qDAAiD;EAAA;EAAAhC,cAAA,GAAAmB,CAAA;EACjDa,qBAAA,6DAAyD;AAC3D,CAAC;AATW;AAAA,CAAAhC,cAAA,GAAAqB,CAAA,UAAAW,qBAAqB;AAAA;AAAA,CAAAhC,cAAA,GAAAqB,CAAA,UAAAY,OAAA,CAAAD,qBAAA,GAArBA,qBAAqB;AAWjC,IAAYE,qBASX;AAAA;AAAAlC,cAAA,GAAAmB,CAAA;AATD,WAAYe,qBAAqB;EAAA;EAAAlC,cAAA,GAAAoB,CAAA;EAAApB,cAAA,GAAAmB,CAAA;EAC/Be,qBAAA,2CAAuC;EAAA;EAAAlC,cAAA,GAAAmB,CAAA;EACvCe,qBAAA,+CAA2C;EAAA;EAAAlC,cAAA,GAAAmB,CAAA;EAC3Ce,qBAAA,qCAAiC;EAAA;EAAAlC,cAAA,GAAAmB,CAAA;EACjCe,qBAAA,yCAAqC;EAAA;EAAAlC,cAAA,GAAAmB,CAAA;EACrCe,qBAAA,qDAAiD;EAAA;EAAAlC,cAAA,GAAAmB,CAAA;EACjDe,qBAAA,6CAAyC;EAAA;EAAAlC,cAAA,GAAAmB,CAAA;EACzCe,qBAAA,uBAAmB;EAAA;EAAAlC,cAAA,GAAAmB,CAAA;EACnBe,qBAAA,+CAA2C;AAC7C,CAAC;AATW;AAAA,CAAAlC,cAAA,GAAAqB,CAAA,UAAAa,qBAAqB;AAAA;AAAA,CAAAlC,cAAA,GAAAqB,CAAA,UAAAY,OAAA,CAAAC,qBAAA,GAArBA,qBAAqB;AAWjC,IAAYC,cASX;AAAA;AAAAnC,cAAA,GAAAmB,CAAA;AATD,WAAYgB,cAAc;EAAA;EAAAnC,cAAA,GAAAoB,CAAA;EAAApB,cAAA,GAAAmB,CAAA;EACxBgB,cAAA,qCAAiC;EAAA;EAAAnC,cAAA,GAAAmB,CAAA;EACjCgB,cAAA,qCAAiC;EAAA;EAAAnC,cAAA,GAAAmB,CAAA;EACjCgB,cAAA,+BAA2B;EAAA;EAAAnC,cAAA,GAAAmB,CAAA;EAC3BgB,cAAA,yCAAqC;EAAA;EAAAnC,cAAA,GAAAmB,CAAA;EACrCgB,cAAA,uCAAmC;EAAA;EAAAnC,cAAA,GAAAmB,CAAA;EACnCgB,cAAA,uCAAmC;EAAA;EAAAnC,cAAA,GAAAmB,CAAA;EACnCgB,cAAA,mDAA+C;EAAA;EAAAnC,cAAA,GAAAmB,CAAA;EAC/CgB,cAAA,uDAAmD;AACrD,CAAC;AATW;AAAA,CAAAnC,cAAA,GAAAqB,CAAA,UAAAc,cAAc;AAAA;AAAA,CAAAnC,cAAA,GAAAqB,CAAA,UAAAY,OAAA,CAAAE,cAAA,GAAdA,cAAc;AAW1B,IAAYC,YASX;AAAA;AAAApC,cAAA,GAAAmB,CAAA;AATD,WAAYiB,YAAY;EAAA;EAAApC,cAAA,GAAAoB,CAAA;EAAApB,cAAA,GAAAmB,CAAA;EACtBiB,YAAA,2BAAuB;EAAA;EAAApC,cAAA,GAAAmB,CAAA;EACvBiB,YAAA,iCAA6B;EAAA;EAAApC,cAAA,GAAAmB,CAAA;EAC7BiB,YAAA,mCAA+B;EAAA;EAAApC,cAAA,GAAAmB,CAAA;EAC/BiB,YAAA,uCAAmC;EAAA;EAAApC,cAAA,GAAAmB,CAAA;EACnCiB,YAAA,2BAAuB;EAAA;EAAApC,cAAA,GAAAmB,CAAA;EACvBiB,YAAA,iDAA6C;EAAA;EAAApC,cAAA,GAAAmB,CAAA;EAC7CiB,YAAA,iCAA6B;EAAA;EAAApC,cAAA,GAAAmB,CAAA;EAC7BiB,YAAA,iDAA6C;AAC/C,CAAC;AATW;AAAA,CAAApC,cAAA,GAAAqB,CAAA,UAAAe,YAAY;AAAA;AAAA,CAAApC,cAAA,GAAAqB,CAAA,UAAAY,OAAA,CAAAG,YAAA,GAAZA,YAAY;AAWxB,IAAYC,kBASX;AAAA;AAAArC,cAAA,GAAAmB,CAAA;AATD,WAAYkB,kBAAkB;EAAA;EAAArC,cAAA,GAAAoB,CAAA;EAAApB,cAAA,GAAAmB,CAAA;EAC5BkB,kBAAA,+BAA2B;EAAA;EAAArC,cAAA,GAAAmB,CAAA;EAC3BkB,kBAAA,iCAA6B;EAAA;EAAArC,cAAA,GAAAmB,CAAA;EAC7BkB,kBAAA,uBAAmB;EAAA;EAAArC,cAAA,GAAAmB,CAAA;EACnBkB,kBAAA,2BAAuB;EAAA;EAAArC,cAAA,GAAAmB,CAAA;EACvBkB,kBAAA,qCAAiC;EAAA;EAAArC,cAAA,GAAAmB,CAAA;EACjCkB,kBAAA,iDAA6C;EAAA;EAAArC,cAAA,GAAAmB,CAAA;EAC7CkB,kBAAA,mBAAe;EAAA;EAAArC,cAAA,GAAAmB,CAAA;EACfkB,kBAAA,2BAAuB;AACzB,CAAC;AATW;AAAA,CAAArC,cAAA,GAAAqB,CAAA,UAAAgB,kBAAkB;AAAA;AAAA,CAAArC,cAAA,GAAAqB,CAAA,UAAAY,OAAA,CAAAI,kBAAA,GAAlBA,kBAAkB", "ignoreList": []}