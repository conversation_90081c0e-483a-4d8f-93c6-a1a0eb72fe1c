6d7b2ee823c5b8f594755e5d0ec2e616
"use strict";

/**
 * System Optimization Type Definitions for Phase 3 Priority 2
 *
 * Comprehensive TypeScript interfaces for dynamic system optimization including:
 * - Multi-objective optimization problems
 * - Constraint handling and validation
 * - Optimization algorithms and parameters
 * - System configuration and variables
 * - Performance metrics and results
 *
 * @version 3.0.0
 * <AUTHOR> Suite Development Team
 */
/* istanbul ignore next */
function cov_168sp8uxfy() {
  var path = "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\types\\SystemOptimizationTypes.ts";
  var hash = "eaadd53deb1d3f5720a809711a063dc2d226fd38";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\types\\SystemOptimizationTypes.ts",
    statementMap: {
      "0": {
        start: {
          line: 15,
          column: 0
        },
        end: {
          line: 15,
          column: 62
        }
      },
      "1": {
        start: {
          line: 16,
          column: 0
        },
        end: {
          line: 16,
          column: 148
        }
      },
      "2": {
        start: {
          line: 21,
          column: 0
        },
        end: {
          line: 30,
          column: 90
        }
      },
      "3": {
        start: {
          line: 22,
          column: 4
        },
        end: {
          line: 22,
          column: 79
        }
      },
      "4": {
        start: {
          line: 23,
          column: 4
        },
        end: {
          line: 23,
          column: 89
        }
      },
      "5": {
        start: {
          line: 24,
          column: 4
        },
        end: {
          line: 24,
          column: 73
        }
      },
      "6": {
        start: {
          line: 25,
          column: 4
        },
        end: {
          line: 25,
          column: 75
        }
      },
      "7": {
        start: {
          line: 26,
          column: 4
        },
        end: {
          line: 26,
          column: 73
        }
      },
      "8": {
        start: {
          line: 27,
          column: 4
        },
        end: {
          line: 27,
          column: 75
        }
      },
      "9": {
        start: {
          line: 28,
          column: 4
        },
        end: {
          line: 28,
          column: 79
        }
      },
      "10": {
        start: {
          line: 29,
          column: 4
        },
        end: {
          line: 29,
          column: 87
        }
      },
      "11": {
        start: {
          line: 32,
          column: 0
        },
        end: {
          line: 41,
          column: 90
        }
      },
      "12": {
        start: {
          line: 33,
          column: 4
        },
        end: {
          line: 33,
          column: 69
        }
      },
      "13": {
        start: {
          line: 34,
          column: 4
        },
        end: {
          line: 34,
          column: 73
        }
      },
      "14": {
        start: {
          line: 35,
          column: 4
        },
        end: {
          line: 35,
          column: 63
        }
      },
      "15": {
        start: {
          line: 36,
          column: 4
        },
        end: {
          line: 36,
          column: 67
        }
      },
      "16": {
        start: {
          line: 37,
          column: 4
        },
        end: {
          line: 37,
          column: 79
        }
      },
      "17": {
        start: {
          line: 38,
          column: 4
        },
        end: {
          line: 38,
          column: 71
        }
      },
      "18": {
        start: {
          line: 39,
          column: 4
        },
        end: {
          line: 39,
          column: 49
        }
      },
      "19": {
        start: {
          line: 40,
          column: 4
        },
        end: {
          line: 40,
          column: 73
        }
      },
      "20": {
        start: {
          line: 43,
          column: 0
        },
        end: {
          line: 52,
          column: 69
        }
      },
      "21": {
        start: {
          line: 44,
          column: 4
        },
        end: {
          line: 44,
          column: 56
        }
      },
      "22": {
        start: {
          line: 45,
          column: 4
        },
        end: {
          line: 45,
          column: 56
        }
      },
      "23": {
        start: {
          line: 46,
          column: 4
        },
        end: {
          line: 46,
          column: 50
        }
      },
      "24": {
        start: {
          line: 47,
          column: 4
        },
        end: {
          line: 47,
          column: 60
        }
      },
      "25": {
        start: {
          line: 48,
          column: 4
        },
        end: {
          line: 48,
          column: 58
        }
      },
      "26": {
        start: {
          line: 49,
          column: 4
        },
        end: {
          line: 49,
          column: 58
        }
      },
      "27": {
        start: {
          line: 50,
          column: 4
        },
        end: {
          line: 50,
          column: 70
        }
      },
      "28": {
        start: {
          line: 51,
          column: 4
        },
        end: {
          line: 51,
          column: 74
        }
      },
      "29": {
        start: {
          line: 54,
          column: 0
        },
        end: {
          line: 63,
          column: 63
        }
      },
      "30": {
        start: {
          line: 55,
          column: 4
        },
        end: {
          line: 55,
          column: 44
        }
      },
      "31": {
        start: {
          line: 56,
          column: 4
        },
        end: {
          line: 56,
          column: 50
        }
      },
      "32": {
        start: {
          line: 57,
          column: 4
        },
        end: {
          line: 57,
          column: 52
        }
      },
      "33": {
        start: {
          line: 58,
          column: 4
        },
        end: {
          line: 58,
          column: 56
        }
      },
      "34": {
        start: {
          line: 59,
          column: 4
        },
        end: {
          line: 59,
          column: 44
        }
      },
      "35": {
        start: {
          line: 60,
          column: 4
        },
        end: {
          line: 60,
          column: 66
        }
      },
      "36": {
        start: {
          line: 61,
          column: 4
        },
        end: {
          line: 61,
          column: 50
        }
      },
      "37": {
        start: {
          line: 62,
          column: 4
        },
        end: {
          line: 62,
          column: 66
        }
      },
      "38": {
        start: {
          line: 65,
          column: 0
        },
        end: {
          line: 74,
          column: 81
        }
      },
      "39": {
        start: {
          line: 66,
          column: 4
        },
        end: {
          line: 66,
          column: 54
        }
      },
      "40": {
        start: {
          line: 67,
          column: 4
        },
        end: {
          line: 67,
          column: 56
        }
      },
      "41": {
        start: {
          line: 68,
          column: 4
        },
        end: {
          line: 68,
          column: 46
        }
      },
      "42": {
        start: {
          line: 69,
          column: 4
        },
        end: {
          line: 69,
          column: 50
        }
      },
      "43": {
        start: {
          line: 70,
          column: 4
        },
        end: {
          line: 70,
          column: 60
        }
      },
      "44": {
        start: {
          line: 71,
          column: 4
        },
        end: {
          line: 71,
          column: 72
        }
      },
      "45": {
        start: {
          line: 72,
          column: 4
        },
        end: {
          line: 72,
          column: 42
        }
      },
      "46": {
        start: {
          line: 73,
          column: 4
        },
        end: {
          line: 73,
          column: 50
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 21,
            column: 1
          },
          end: {
            line: 21,
            column: 2
          }
        },
        loc: {
          start: {
            line: 21,
            column: 34
          },
          end: {
            line: 30,
            column: 1
          }
        },
        line: 21
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 32,
            column: 1
          },
          end: {
            line: 32,
            column: 2
          }
        },
        loc: {
          start: {
            line: 32,
            column: 34
          },
          end: {
            line: 41,
            column: 1
          }
        },
        line: 32
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 43,
            column: 1
          },
          end: {
            line: 43,
            column: 2
          }
        },
        loc: {
          start: {
            line: 43,
            column: 27
          },
          end: {
            line: 52,
            column: 1
          }
        },
        line: 43
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 54,
            column: 1
          },
          end: {
            line: 54,
            column: 2
          }
        },
        loc: {
          start: {
            line: 54,
            column: 25
          },
          end: {
            line: 63,
            column: 1
          }
        },
        line: 54
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 65,
            column: 1
          },
          end: {
            line: 65,
            column: 2
          }
        },
        loc: {
          start: {
            line: 65,
            column: 31
          },
          end: {
            line: 74,
            column: 1
          }
        },
        line: 65
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 30,
            column: 3
          },
          end: {
            line: 30,
            column: 88
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 30,
            column: 3
          },
          end: {
            line: 30,
            column: 24
          }
        }, {
          start: {
            line: 30,
            column: 29
          },
          end: {
            line: 30,
            column: 87
          }
        }],
        line: 30
      },
      "1": {
        loc: {
          start: {
            line: 41,
            column: 3
          },
          end: {
            line: 41,
            column: 88
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 41,
            column: 3
          },
          end: {
            line: 41,
            column: 24
          }
        }, {
          start: {
            line: 41,
            column: 29
          },
          end: {
            line: 41,
            column: 87
          }
        }],
        line: 41
      },
      "2": {
        loc: {
          start: {
            line: 52,
            column: 3
          },
          end: {
            line: 52,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 52,
            column: 3
          },
          end: {
            line: 52,
            column: 17
          }
        }, {
          start: {
            line: 52,
            column: 22
          },
          end: {
            line: 52,
            column: 66
          }
        }],
        line: 52
      },
      "3": {
        loc: {
          start: {
            line: 63,
            column: 3
          },
          end: {
            line: 63,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 63,
            column: 3
          },
          end: {
            line: 63,
            column: 15
          }
        }, {
          start: {
            line: 63,
            column: 20
          },
          end: {
            line: 63,
            column: 60
          }
        }],
        line: 63
      },
      "4": {
        loc: {
          start: {
            line: 74,
            column: 3
          },
          end: {
            line: 74,
            column: 79
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 74,
            column: 3
          },
          end: {
            line: 74,
            column: 21
          }
        }, {
          start: {
            line: 74,
            column: 26
          },
          end: {
            line: 74,
            column: 78
          }
        }],
        line: 74
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\types\\SystemOptimizationTypes.ts",
      mappings: ";AAAA;;;;;;;;;;;;GAYG;;;AAEH,+EAA+E;AAC/E,0BAA0B;AAC1B,+EAA+E;AAE/E,IAAY,qBASX;AATD,WAAY,qBAAqB;IAC/B,0EAAiD,CAAA;IACjD,oFAA2D,CAAA;IAC3D,oEAA2C,CAAA;IAC3C,sEAA6C,CAAA;IAC7C,oEAA2C,CAAA;IAC3C,sEAA6C,CAAA;IAC7C,0EAAiD,CAAA;IACjD,kFAAyD,CAAA;AAC3D,CAAC,EATW,qBAAqB,qCAArB,qBAAqB,QAShC;AAED,IAAY,qBASX;AATD,WAAY,qBAAqB;IAC/B,gEAAuC,CAAA;IACvC,oEAA2C,CAAA;IAC3C,0DAAiC,CAAA;IACjC,8DAAqC,CAAA;IACrC,0EAAiD,CAAA;IACjD,kEAAyC,CAAA;IACzC,4CAAmB,CAAA;IACnB,oEAA2C,CAAA;AAC7C,CAAC,EATW,qBAAqB,qCAArB,qBAAqB,QAShC;AAED,IAAY,cASX;AATD,WAAY,cAAc;IACxB,mDAAiC,CAAA;IACjC,mDAAiC,CAAA;IACjC,6CAA2B,CAAA;IAC3B,uDAAqC,CAAA;IACrC,qDAAmC,CAAA;IACnC,qDAAmC,CAAA;IACnC,iEAA+C,CAAA;IAC/C,qEAAmD,CAAA;AACrD,CAAC,EATW,cAAc,8BAAd,cAAc,QASzB;AAED,IAAY,YASX;AATD,WAAY,YAAY;IACtB,uCAAuB,CAAA;IACvB,6CAA6B,CAAA;IAC7B,+CAA+B,CAAA;IAC/B,mDAAmC,CAAA;IACnC,uCAAuB,CAAA;IACvB,6DAA6C,CAAA;IAC7C,6CAA6B,CAAA;IAC7B,6DAA6C,CAAA;AAC/C,CAAC,EATW,YAAY,4BAAZ,YAAY,QASvB;AAED,IAAY,kBASX;AATD,WAAY,kBAAkB;IAC5B,iDAA2B,CAAA;IAC3B,mDAA6B,CAAA;IAC7B,yCAAmB,CAAA;IACnB,6CAAuB,CAAA;IACvB,uDAAiC,CAAA;IACjC,mEAA6C,CAAA;IAC7C,qCAAe,CAAA;IACf,6CAAuB,CAAA;AACzB,CAAC,EATW,kBAAkB,kCAAlB,kBAAkB,QAS7B",
      names: [],
      sources: ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\types\\SystemOptimizationTypes.ts"],
      sourcesContent: ["/**\r\n * System Optimization Type Definitions for Phase 3 Priority 2\r\n * \r\n * Comprehensive TypeScript interfaces for dynamic system optimization including:\r\n * - Multi-objective optimization problems\r\n * - Constraint handling and validation\r\n * - Optimization algorithms and parameters\r\n * - System configuration and variables\r\n * - Performance metrics and results\r\n * \r\n * @version 3.0.0\r\n * <AUTHOR> Suite Development Team\r\n */\r\n\r\n// ============================================================================\r\n// Core Optimization Enums\r\n// ============================================================================\r\n\r\nexport enum OptimizationObjective {\r\n  MINIMIZE_PRESSURE_LOSS = 'minimize_pressure_loss',\r\n  MINIMIZE_ENERGY_CONSUMPTION = 'minimize_energy_consumption',\r\n  MINIMIZE_TOTAL_COST = 'minimize_total_cost',\r\n  MINIMIZE_NOISE_LEVEL = 'minimize_noise_level',\r\n  MAXIMIZE_EFFICIENCY = 'maximize_efficiency',\r\n  MINIMIZE_SPACE_USAGE = 'minimize_space_usage',\r\n  MINIMIZE_MATERIAL_COST = 'minimize_material_cost',\r\n  MINIMIZE_INSTALLATION_TIME = 'minimize_installation_time'\r\n}\r\n\r\nexport enum OptimizationAlgorithm {\r\n  GENETIC_ALGORITHM = 'genetic_algorithm',\r\n  SIMULATED_ANNEALING = 'simulated_annealing',\r\n  PARTICLE_SWARM = 'particle_swarm',\r\n  GRADIENT_DESCENT = 'gradient_descent',\r\n  DIFFERENTIAL_EVOLUTION = 'differential_evolution',\r\n  MULTI_OBJECTIVE_GA = 'multi_objective_ga',\r\n  NSGA_II = 'nsga_ii',\r\n  HYBRID_OPTIMIZATION = 'hybrid_optimization'\r\n}\r\n\r\nexport enum ConstraintType {\r\n  VELOCITY_LIMIT = 'velocity_limit',\r\n  PRESSURE_LIMIT = 'pressure_limit',\r\n  NOISE_LIMIT = 'noise_limit',\r\n  SPACE_CONSTRAINT = 'space_constraint',\r\n  COST_CONSTRAINT = 'cost_constraint',\r\n  CODE_COMPLIANCE = 'code_compliance',\r\n  MATERIAL_AVAILABILITY = 'material_availability',\r\n  INSTALLATION_CONSTRAINT = 'installation_constraint'\r\n}\r\n\r\nexport enum VariableType {\r\n  DUCT_SIZE = 'duct_size',\r\n  FITTING_TYPE = 'fitting_type',\r\n  MATERIAL_TYPE = 'material_type',\r\n  DAMPER_POSITION = 'damper_position',\r\n  FAN_SPEED = 'fan_speed',\r\n  SYSTEM_CONFIGURATION = 'system_configuration',\r\n  ROUTING_PATH = 'routing_path',\r\n  INSULATION_THICKNESS = 'insulation_thickness'\r\n}\r\n\r\nexport enum OptimizationStatus {\r\n  NOT_STARTED = 'not_started',\r\n  INITIALIZING = 'initializing',\r\n  RUNNING = 'running',\r\n  CONVERGED = 'converged',\r\n  MAX_ITERATIONS = 'max_iterations',\r\n  CONSTRAINT_VIOLATION = 'constraint_violation',\r\n  ERROR = 'error',\r\n  CANCELLED = 'cancelled'\r\n}\r\n\r\n// ============================================================================\r\n// Optimization Variables and Bounds\r\n// ============================================================================\r\n\r\nexport interface OptimizationVariable {\r\n  id: string;\r\n  name: string;\r\n  type: VariableType;\r\n  description: string;\r\n  currentValue: number | string;\r\n  bounds: VariableBounds;\r\n  discreteValues?: (number | string)[];\r\n  units?: string;\r\n  precision?: number;\r\n  dependencies?: VariableDependency[];\r\n}\r\n\r\nexport interface VariableBounds {\r\n  minimum: number | string;\r\n  maximum: number | string;\r\n  step?: number;\r\n  allowedValues?: (number | string)[];\r\n  constraints?: BoundConstraint[];\r\n}\r\n\r\nexport interface BoundConstraint {\r\n  condition: string;\r\n  minValue?: number | string;\r\n  maxValue?: number | string;\r\n  description: string;\r\n}\r\n\r\nexport interface VariableDependency {\r\n  dependentVariableId: string;\r\n  relationship: 'linear' | 'inverse' | 'custom';\r\n  coefficient?: number;\r\n  customFunction?: string;\r\n  description: string;\r\n}\r\n\r\n// ============================================================================\r\n// Optimization Constraints\r\n// ============================================================================\r\n\r\nexport interface OptimizationConstraint {\r\n  id: string;\r\n  name: string;\r\n  type: ConstraintType;\r\n  description: string;\r\n  constraintFunction: ConstraintFunction;\r\n  tolerance: number;\r\n  priority: 'low' | 'medium' | 'high' | 'critical';\r\n  penalty?: number;\r\n  active: boolean;\r\n}\r\n\r\nexport interface ConstraintFunction {\r\n  expression: string;\r\n  variables: string[];\r\n  parameters?: { [key: string]: number };\r\n  evaluationMethod: 'analytical' | 'numerical' | 'simulation';\r\n}\r\n\r\nexport interface ConstraintViolation {\r\n  constraintId: string;\r\n  violationType: 'boundary' | 'equality' | 'inequality';\r\n  currentValue: number;\r\n  requiredValue: number;\r\n  severity: 'minor' | 'major' | 'critical';\r\n  penalty: number;\r\n}\r\n\r\n// ============================================================================\r\n// Objective Functions\r\n// ============================================================================\r\n\r\nexport interface ObjectiveFunction {\r\n  id: string;\r\n  objective: OptimizationObjective;\r\n  weight: number;\r\n  description: string;\r\n  evaluationFunction: (variables: OptimizationVariable[]) => number;\r\n  units: string;\r\n  targetValue?: number;\r\n  tolerance?: number;\r\n}\r\n\r\nexport interface MultiObjectiveFunction {\r\n  objectives: ObjectiveFunction[];\r\n  aggregationMethod: 'weighted_sum' | 'pareto_optimal' | 'lexicographic' | 'goal_programming';\r\n  weights?: number[];\r\n  paretoSettings?: ParetoSettings;\r\n}\r\n\r\nexport interface ParetoSettings {\r\n  populationSize: number;\r\n  maxGenerations: number;\r\n  crossoverRate: number;\r\n  mutationRate: number;\r\n  eliteSize: number;\r\n  diversityMaintenance: boolean;\r\n}\r\n\r\n// ============================================================================\r\n// System Configuration\r\n// ============================================================================\r\n\r\nexport interface SystemConfiguration {\r\n  id: string;\r\n  name: string;\r\n  description: string;\r\n  systemType: 'supply' | 'return' | 'exhaust' | 'mixed';\r\n  segments: SystemSegment[];\r\n  designConditions: DesignConditions;\r\n  performanceRequirements: PerformanceRequirements;\r\n  constraints: SystemConstraints;\r\n}\r\n\r\nexport interface SystemSegment {\r\n  id: string;\r\n  segmentType: 'duct' | 'fitting' | 'equipment' | 'terminal';\r\n  geometry: SegmentGeometry;\r\n  material: MaterialProperties;\r\n  flowConditions: FlowConditions;\r\n  optimizationVariables: string[];\r\n  fixedParameters: { [key: string]: any };\r\n}\r\n\r\nexport interface SegmentGeometry {\r\n  shape: 'rectangular' | 'round' | 'oval' | 'custom';\r\n  dimensions: DimensionSet;\r\n  length?: number;\r\n  orientation?: number;\r\n  position?: Position3D;\r\n}\r\n\r\nexport interface DimensionSet {\r\n  width?: number;\r\n  height?: number;\r\n  diameter?: number;\r\n  majorAxis?: number;\r\n  minorAxis?: number;\r\n  customDimensions?: { [key: string]: number };\r\n}\r\n\r\nexport interface Position3D {\r\n  x: number;\r\n  y: number;\r\n  z: number;\r\n}\r\n\r\nexport interface MaterialProperties {\r\n  type: string;\r\n  roughness: number;\r\n  density: number;\r\n  thermalConductivity?: number;\r\n  cost?: number;\r\n  availability?: 'standard' | 'special_order' | 'custom';\r\n}\r\n\r\nexport interface FlowConditions {\r\n  volumeFlow: number;\r\n  velocity: number;\r\n  pressure: number;\r\n  temperature: number;\r\n  density: number;\r\n  viscosity: number;\r\n}\r\n\r\nexport interface DesignConditions {\r\n  temperature: number;\r\n  pressure: number;\r\n  humidity: number;\r\n  elevation: number;\r\n  seasonalVariation?: boolean;\r\n  operatingSchedule?: OperatingSchedule;\r\n}\r\n\r\nexport interface OperatingSchedule {\r\n  dailyProfile: HourlyProfile[];\r\n  weeklyPattern: DailyPattern[];\r\n  seasonalAdjustments: SeasonalAdjustment[];\r\n}\r\n\r\nexport interface HourlyProfile {\r\n  hour: number;\r\n  loadFactor: number;\r\n  temperatureAdjustment: number;\r\n}\r\n\r\nexport interface DailyPattern {\r\n  dayOfWeek: number;\r\n  profileId: string;\r\n  specialConditions?: string[];\r\n}\r\n\r\nexport interface SeasonalAdjustment {\r\n  season: 'spring' | 'summer' | 'fall' | 'winter';\r\n  temperatureRange: [number, number];\r\n  humidityRange: [number, number];\r\n  loadAdjustment: number;\r\n}\r\n\r\nexport interface PerformanceRequirements {\r\n  maxPressureLoss: number;\r\n  maxVelocity: number;\r\n  maxNoiseLevel: number;\r\n  minEfficiency: number;\r\n  targetFlowRates: { [zoneId: string]: number };\r\n  balancingTolerance: number;\r\n}\r\n\r\nexport interface SystemConstraints {\r\n  spaceConstraints: SpaceConstraint[];\r\n  codeRequirements: CodeRequirement[];\r\n  budgetConstraints: BudgetConstraint[];\r\n  installationConstraints: InstallationConstraint[];\r\n}\r\n\r\nexport interface SpaceConstraint {\r\n  area: BoundingBox;\r\n  maxDuctSize: DimensionSet;\r\n  clearanceRequirements: number;\r\n  accessRequirements: AccessRequirement[];\r\n}\r\n\r\nexport interface BoundingBox {\r\n  minX: number;\r\n  maxX: number;\r\n  minY: number;\r\n  maxY: number;\r\n  minZ: number;\r\n  maxZ: number;\r\n}\r\n\r\nexport interface AccessRequirement {\r\n  purpose: 'maintenance' | 'inspection' | 'installation';\r\n  minimumClearance: number;\r\n  frequency: 'daily' | 'weekly' | 'monthly' | 'annual';\r\n}\r\n\r\nexport interface CodeRequirement {\r\n  code: 'SMACNA' | 'ASHRAE' | 'IMC' | 'UMC' | 'local';\r\n  section: string;\r\n  requirement: string;\r\n  complianceCheck: (system: SystemConfiguration) => boolean;\r\n}\r\n\r\nexport interface BudgetConstraint {\r\n  category: 'material' | 'labor' | 'equipment' | 'total';\r\n  maxCost: number;\r\n  currency: string;\r\n  contingency: number;\r\n}\r\n\r\nexport interface InstallationConstraint {\r\n  sequenceRequirements: string[];\r\n  equipmentRequirements: string[];\r\n  timeConstraints: TimeConstraint[];\r\n  skillRequirements: SkillRequirement[];\r\n}\r\n\r\nexport interface TimeConstraint {\r\n  phase: string;\r\n  maxDuration: number;\r\n  dependencies: string[];\r\n  criticalPath: boolean;\r\n}\r\n\r\nexport interface SkillRequirement {\r\n  skill: string;\r\n  level: 'apprentice' | 'journeyman' | 'master';\r\n  certification?: string;\r\n  availability: number;\r\n}\r\n\r\n// ============================================================================\r\n// Optimization Problem Definition\r\n// ============================================================================\r\n\r\nexport interface OptimizationProblem {\r\n  id: string;\r\n  name: string;\r\n  description: string;\r\n  systemConfiguration: SystemConfiguration;\r\n  variables: OptimizationVariable[];\r\n  objectives: MultiObjectiveFunction;\r\n  constraints: OptimizationConstraint[];\r\n  algorithmSettings: AlgorithmSettings;\r\n  convergenceCriteria: ConvergenceCriteria;\r\n  outputRequirements: OutputRequirements;\r\n}\r\n\r\nexport interface AlgorithmSettings {\r\n  algorithm: OptimizationAlgorithm;\r\n  parameters: AlgorithmParameters;\r\n  parallelization: ParallelizationSettings;\r\n  seedValue?: number;\r\n  restartStrategy?: RestartStrategy;\r\n}\r\n\r\nexport interface AlgorithmParameters {\r\n  populationSize?: number;\r\n  maxIterations?: number;\r\n  maxEvaluations?: number;\r\n  crossoverRate?: number;\r\n  mutationRate?: number;\r\n  selectionMethod?: string;\r\n  coolingSchedule?: CoolingSchedule;\r\n  inertiaWeight?: number;\r\n  accelerationCoefficients?: number[];\r\n  stepSize?: number;\r\n  gradientTolerance?: number;\r\n  customParameters?: { [key: string]: any };\r\n}\r\n\r\nexport interface CoolingSchedule {\r\n  initialTemperature: number;\r\n  finalTemperature: number;\r\n  coolingRate: number;\r\n  method: 'linear' | 'exponential' | 'logarithmic' | 'adaptive';\r\n}\r\n\r\nexport interface ParallelizationSettings {\r\n  enabled: boolean;\r\n  maxWorkers?: number;\r\n  chunkSize?: number;\r\n  loadBalancing?: 'static' | 'dynamic';\r\n}\r\n\r\nexport interface RestartStrategy {\r\n  enabled: boolean;\r\n  maxRestarts: number;\r\n  restartCondition: 'stagnation' | 'diversity_loss' | 'time_limit';\r\n  restartParameters: { [key: string]: any };\r\n}\r\n\r\nexport interface ConvergenceCriteria {\r\n  maxIterations: number;\r\n  toleranceValue: number;\r\n  stagnationLimit: number;\r\n  improvementThreshold: number;\r\n  timeLimit?: number;\r\n  customCriteria?: ConvergenceFunction[];\r\n}\r\n\r\nexport interface ConvergenceFunction {\r\n  name: string;\r\n  function: (history: OptimizationHistory) => boolean;\r\n  description: string;\r\n}\r\n\r\nexport interface OutputRequirements {\r\n  includeHistory: boolean;\r\n  detailedAnalysis: boolean;\r\n  sensitivityAnalysis: boolean;\r\n  uncertaintyAnalysis: boolean;\r\n  visualizations: VisualizationRequest[];\r\n  reportFormat: 'json' | 'pdf' | 'excel' | 'html';\r\n}\r\n\r\nexport interface VisualizationRequest {\r\n  type: 'convergence' | 'pareto_front' | 'variable_history' | 'constraint_violations' | 'sensitivity';\r\n  parameters: { [key: string]: any };\r\n}\r\n\r\n// ============================================================================\r\n// Optimization Results\r\n// ============================================================================\r\n\r\nexport interface OptimizationResult {\r\n  problemId: string;\r\n  status: OptimizationStatus;\r\n  bestSolution: OptimizationSolution;\r\n  paretoFront?: OptimizationSolution[];\r\n  statistics: OptimizationStatistics;\r\n  history: OptimizationHistory;\r\n  analysis: ResultAnalysis;\r\n  recommendations: OptimizationRecommendation[];\r\n  warnings: string[];\r\n  errors: string[];\r\n}\r\n\r\nexport interface OptimizationSolution {\r\n  id: string;\r\n  variables: { [variableId: string]: number | string };\r\n  objectiveValues: { [objectiveId: string]: number };\r\n  constraintViolations: ConstraintViolation[];\r\n  feasible: boolean;\r\n  dominationRank?: number;\r\n  crowdingDistance?: number;\r\n  fitness: number;\r\n  systemConfiguration: SystemConfiguration;\r\n  performanceMetrics: SolutionPerformanceMetrics;\r\n}\r\n\r\nexport interface SolutionPerformanceMetrics {\r\n  totalPressureLoss: number;\r\n  energyConsumption: number;\r\n  totalCost: number;\r\n  noiseLevel: number;\r\n  efficiency: number;\r\n  spaceUtilization: number;\r\n  installationComplexity: number;\r\n  maintenanceRequirements: number;\r\n}\r\n\r\nexport interface OptimizationStatistics {\r\n  totalIterations: number;\r\n  totalEvaluations: number;\r\n  convergenceIteration: number;\r\n  executionTime: number;\r\n  bestFitnessHistory: number[];\r\n  averageFitnessHistory: number[];\r\n  diversityHistory: number[];\r\n  constraintViolationHistory: number[];\r\n  algorithmSpecificStats: { [key: string]: any };\r\n}\r\n\r\nexport interface OptimizationHistory {\r\n  iterations: IterationHistory[];\r\n  populationHistory?: PopulationSnapshot[];\r\n  parameterHistory: ParameterHistory[];\r\n  convergenceMetrics: ConvergenceMetrics[];\r\n}\r\n\r\nexport interface IterationHistory {\r\n  iteration: number;\r\n  bestFitness: number;\r\n  averageFitness: number;\r\n  worstFitness: number;\r\n  diversity: number;\r\n  constraintViolations: number;\r\n  timestamp: Date;\r\n}\r\n\r\nexport interface PopulationSnapshot {\r\n  iteration: number;\r\n  population: OptimizationSolution[];\r\n  statistics: PopulationStatistics;\r\n}\r\n\r\nexport interface PopulationStatistics {\r\n  size: number;\r\n  feasibleSolutions: number;\r\n  averageFitness: number;\r\n  fitnessStandardDeviation: number;\r\n  diversityIndex: number;\r\n}\r\n\r\nexport interface ParameterHistory {\r\n  iteration: number;\r\n  parameters: { [parameterName: string]: any };\r\n  adaptiveChanges: string[];\r\n}\r\n\r\nexport interface ConvergenceMetrics {\r\n  iteration: number;\r\n  improvement: number;\r\n  stagnationCount: number;\r\n  convergenceIndicator: number;\r\n  estimatedRemainingIterations?: number;\r\n}\r\n\r\nexport interface ResultAnalysis {\r\n  sensitivityAnalysis?: SensitivityAnalysis;\r\n  uncertaintyAnalysis?: UncertaintyAnalysis;\r\n  tradeoffAnalysis?: TradeoffAnalysis;\r\n  robustnessAnalysis?: RobustnessAnalysis;\r\n}\r\n\r\nexport interface SensitivityAnalysis {\r\n  variableSensitivities: VariableSensitivity[];\r\n  parameterSensitivities: ParameterSensitivity[];\r\n  globalSensitivityIndices: { [variableId: string]: number };\r\n}\r\n\r\nexport interface VariableSensitivity {\r\n  variableId: string;\r\n  sensitivityIndex: number;\r\n  localSensitivity: number;\r\n  interactionEffects: InteractionEffect[];\r\n}\r\n\r\nexport interface InteractionEffect {\r\n  variableIds: string[];\r\n  interactionStrength: number;\r\n  description: string;\r\n}\r\n\r\nexport interface ParameterSensitivity {\r\n  parameterId: string;\r\n  sensitivityIndex: number;\r\n  recommendedRange: [number, number];\r\n}\r\n\r\nexport interface UncertaintyAnalysis {\r\n  inputUncertainties: InputUncertainty[];\r\n  outputUncertainties: OutputUncertainty[];\r\n  confidenceIntervals: ConfidenceInterval[];\r\n  robustnessMeasures: RobustnessMeasure[];\r\n}\r\n\r\nexport interface InputUncertainty {\r\n  variableId: string;\r\n  uncertaintyType: 'normal' | 'uniform' | 'triangular' | 'custom';\r\n  parameters: { [key: string]: number };\r\n  correlations?: { [variableId: string]: number };\r\n}\r\n\r\nexport interface OutputUncertainty {\r\n  objectiveId: string;\r\n  mean: number;\r\n  standardDeviation: number;\r\n  distribution: 'normal' | 'lognormal' | 'custom';\r\n  percentiles: { [percentile: string]: number };\r\n}\r\n\r\nexport interface ConfidenceInterval {\r\n  objectiveId: string;\r\n  confidenceLevel: number;\r\n  lowerBound: number;\r\n  upperBound: number;\r\n}\r\n\r\nexport interface RobustnessMeasure {\r\n  name: string;\r\n  value: number;\r\n  interpretation: string;\r\n}\r\n\r\nexport interface TradeoffAnalysis {\r\n  objectivePairs: ObjectivePair[];\r\n  paretoEfficiency: number;\r\n  kneePoints: OptimizationSolution[];\r\n  tradeoffRecommendations: TradeoffRecommendation[];\r\n}\r\n\r\nexport interface ObjectivePair {\r\n  objective1Id: string;\r\n  objective2Id: string;\r\n  correlationCoefficient: number;\r\n  tradeoffStrength: 'weak' | 'moderate' | 'strong';\r\n  conflictLevel: number;\r\n}\r\n\r\nexport interface TradeoffRecommendation {\r\n  description: string;\r\n  recommendedSolution: OptimizationSolution;\r\n  tradeoffJustification: string;\r\n  alternativeSolutions: OptimizationSolution[];\r\n}\r\n\r\nexport interface RobustnessAnalysis {\r\n  robustSolutions: OptimizationSolution[];\r\n  robustnessMetrics: RobustnessMetric[];\r\n  scenarioAnalysis: ScenarioAnalysis[];\r\n}\r\n\r\nexport interface RobustnessMetric {\r\n  name: string;\r\n  value: number;\r\n  threshold: number;\r\n  passed: boolean;\r\n}\r\n\r\nexport interface ScenarioAnalysis {\r\n  scenarioName: string;\r\n  scenarioParameters: { [key: string]: any };\r\n  solutionPerformance: SolutionPerformanceMetrics;\r\n  performanceDegradation: number;\r\n}\r\n\r\nexport interface OptimizationRecommendation {\r\n  type: 'improvement' | 'alternative' | 'caution' | 'validation';\r\n  priority: 'low' | 'medium' | 'high' | 'critical';\r\n  title: string;\r\n  description: string;\r\n  expectedBenefit: string;\r\n  implementationEffort: 'low' | 'medium' | 'high';\r\n  riskLevel: 'low' | 'medium' | 'high';\r\n  actionItems: string[];\r\n  relatedSolutions?: OptimizationSolution[];\r\n}\r\n\r\n// ============================================================================\r\n// Utility Types\r\n// ============================================================================\r\n\r\nexport type ObjectiveFunctionType = (variables: OptimizationVariable[]) => number;\r\nexport type ConstraintFunctionType = (variables: OptimizationVariable[]) => number;\r\nexport type FitnessFunction = (solution: OptimizationSolution) => number;\r\nexport type SelectionFunction = (population: OptimizationSolution[], count: number) => OptimizationSolution[];\r\nexport type CrossoverFunction = (parent1: OptimizationSolution, parent2: OptimizationSolution) => OptimizationSolution[];\r\nexport type MutationFunction = (solution: OptimizationSolution, rate: number) => OptimizationSolution;\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "eaadd53deb1d3f5720a809711a063dc2d226fd38"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_168sp8uxfy = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_168sp8uxfy();
cov_168sp8uxfy().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_168sp8uxfy().s[1]++;
exports.OptimizationStatus = exports.VariableType = exports.ConstraintType = exports.OptimizationAlgorithm = exports.OptimizationObjective = void 0;
// ============================================================================
// Core Optimization Enums
// ============================================================================
var OptimizationObjective;
/* istanbul ignore next */
cov_168sp8uxfy().s[2]++;
(function (OptimizationObjective) {
  /* istanbul ignore next */
  cov_168sp8uxfy().f[0]++;
  cov_168sp8uxfy().s[3]++;
  OptimizationObjective["MINIMIZE_PRESSURE_LOSS"] = "minimize_pressure_loss";
  /* istanbul ignore next */
  cov_168sp8uxfy().s[4]++;
  OptimizationObjective["MINIMIZE_ENERGY_CONSUMPTION"] = "minimize_energy_consumption";
  /* istanbul ignore next */
  cov_168sp8uxfy().s[5]++;
  OptimizationObjective["MINIMIZE_TOTAL_COST"] = "minimize_total_cost";
  /* istanbul ignore next */
  cov_168sp8uxfy().s[6]++;
  OptimizationObjective["MINIMIZE_NOISE_LEVEL"] = "minimize_noise_level";
  /* istanbul ignore next */
  cov_168sp8uxfy().s[7]++;
  OptimizationObjective["MAXIMIZE_EFFICIENCY"] = "maximize_efficiency";
  /* istanbul ignore next */
  cov_168sp8uxfy().s[8]++;
  OptimizationObjective["MINIMIZE_SPACE_USAGE"] = "minimize_space_usage";
  /* istanbul ignore next */
  cov_168sp8uxfy().s[9]++;
  OptimizationObjective["MINIMIZE_MATERIAL_COST"] = "minimize_material_cost";
  /* istanbul ignore next */
  cov_168sp8uxfy().s[10]++;
  OptimizationObjective["MINIMIZE_INSTALLATION_TIME"] = "minimize_installation_time";
})(
/* istanbul ignore next */
(cov_168sp8uxfy().b[0][0]++, OptimizationObjective) ||
/* istanbul ignore next */
(cov_168sp8uxfy().b[0][1]++, exports.OptimizationObjective = OptimizationObjective = {}));
var OptimizationAlgorithm;
/* istanbul ignore next */
cov_168sp8uxfy().s[11]++;
(function (OptimizationAlgorithm) {
  /* istanbul ignore next */
  cov_168sp8uxfy().f[1]++;
  cov_168sp8uxfy().s[12]++;
  OptimizationAlgorithm["GENETIC_ALGORITHM"] = "genetic_algorithm";
  /* istanbul ignore next */
  cov_168sp8uxfy().s[13]++;
  OptimizationAlgorithm["SIMULATED_ANNEALING"] = "simulated_annealing";
  /* istanbul ignore next */
  cov_168sp8uxfy().s[14]++;
  OptimizationAlgorithm["PARTICLE_SWARM"] = "particle_swarm";
  /* istanbul ignore next */
  cov_168sp8uxfy().s[15]++;
  OptimizationAlgorithm["GRADIENT_DESCENT"] = "gradient_descent";
  /* istanbul ignore next */
  cov_168sp8uxfy().s[16]++;
  OptimizationAlgorithm["DIFFERENTIAL_EVOLUTION"] = "differential_evolution";
  /* istanbul ignore next */
  cov_168sp8uxfy().s[17]++;
  OptimizationAlgorithm["MULTI_OBJECTIVE_GA"] = "multi_objective_ga";
  /* istanbul ignore next */
  cov_168sp8uxfy().s[18]++;
  OptimizationAlgorithm["NSGA_II"] = "nsga_ii";
  /* istanbul ignore next */
  cov_168sp8uxfy().s[19]++;
  OptimizationAlgorithm["HYBRID_OPTIMIZATION"] = "hybrid_optimization";
})(
/* istanbul ignore next */
(cov_168sp8uxfy().b[1][0]++, OptimizationAlgorithm) ||
/* istanbul ignore next */
(cov_168sp8uxfy().b[1][1]++, exports.OptimizationAlgorithm = OptimizationAlgorithm = {}));
var ConstraintType;
/* istanbul ignore next */
cov_168sp8uxfy().s[20]++;
(function (ConstraintType) {
  /* istanbul ignore next */
  cov_168sp8uxfy().f[2]++;
  cov_168sp8uxfy().s[21]++;
  ConstraintType["VELOCITY_LIMIT"] = "velocity_limit";
  /* istanbul ignore next */
  cov_168sp8uxfy().s[22]++;
  ConstraintType["PRESSURE_LIMIT"] = "pressure_limit";
  /* istanbul ignore next */
  cov_168sp8uxfy().s[23]++;
  ConstraintType["NOISE_LIMIT"] = "noise_limit";
  /* istanbul ignore next */
  cov_168sp8uxfy().s[24]++;
  ConstraintType["SPACE_CONSTRAINT"] = "space_constraint";
  /* istanbul ignore next */
  cov_168sp8uxfy().s[25]++;
  ConstraintType["COST_CONSTRAINT"] = "cost_constraint";
  /* istanbul ignore next */
  cov_168sp8uxfy().s[26]++;
  ConstraintType["CODE_COMPLIANCE"] = "code_compliance";
  /* istanbul ignore next */
  cov_168sp8uxfy().s[27]++;
  ConstraintType["MATERIAL_AVAILABILITY"] = "material_availability";
  /* istanbul ignore next */
  cov_168sp8uxfy().s[28]++;
  ConstraintType["INSTALLATION_CONSTRAINT"] = "installation_constraint";
})(
/* istanbul ignore next */
(cov_168sp8uxfy().b[2][0]++, ConstraintType) ||
/* istanbul ignore next */
(cov_168sp8uxfy().b[2][1]++, exports.ConstraintType = ConstraintType = {}));
var VariableType;
/* istanbul ignore next */
cov_168sp8uxfy().s[29]++;
(function (VariableType) {
  /* istanbul ignore next */
  cov_168sp8uxfy().f[3]++;
  cov_168sp8uxfy().s[30]++;
  VariableType["DUCT_SIZE"] = "duct_size";
  /* istanbul ignore next */
  cov_168sp8uxfy().s[31]++;
  VariableType["FITTING_TYPE"] = "fitting_type";
  /* istanbul ignore next */
  cov_168sp8uxfy().s[32]++;
  VariableType["MATERIAL_TYPE"] = "material_type";
  /* istanbul ignore next */
  cov_168sp8uxfy().s[33]++;
  VariableType["DAMPER_POSITION"] = "damper_position";
  /* istanbul ignore next */
  cov_168sp8uxfy().s[34]++;
  VariableType["FAN_SPEED"] = "fan_speed";
  /* istanbul ignore next */
  cov_168sp8uxfy().s[35]++;
  VariableType["SYSTEM_CONFIGURATION"] = "system_configuration";
  /* istanbul ignore next */
  cov_168sp8uxfy().s[36]++;
  VariableType["ROUTING_PATH"] = "routing_path";
  /* istanbul ignore next */
  cov_168sp8uxfy().s[37]++;
  VariableType["INSULATION_THICKNESS"] = "insulation_thickness";
})(
/* istanbul ignore next */
(cov_168sp8uxfy().b[3][0]++, VariableType) ||
/* istanbul ignore next */
(cov_168sp8uxfy().b[3][1]++, exports.VariableType = VariableType = {}));
var OptimizationStatus;
/* istanbul ignore next */
cov_168sp8uxfy().s[38]++;
(function (OptimizationStatus) {
  /* istanbul ignore next */
  cov_168sp8uxfy().f[4]++;
  cov_168sp8uxfy().s[39]++;
  OptimizationStatus["NOT_STARTED"] = "not_started";
  /* istanbul ignore next */
  cov_168sp8uxfy().s[40]++;
  OptimizationStatus["INITIALIZING"] = "initializing";
  /* istanbul ignore next */
  cov_168sp8uxfy().s[41]++;
  OptimizationStatus["RUNNING"] = "running";
  /* istanbul ignore next */
  cov_168sp8uxfy().s[42]++;
  OptimizationStatus["CONVERGED"] = "converged";
  /* istanbul ignore next */
  cov_168sp8uxfy().s[43]++;
  OptimizationStatus["MAX_ITERATIONS"] = "max_iterations";
  /* istanbul ignore next */
  cov_168sp8uxfy().s[44]++;
  OptimizationStatus["CONSTRAINT_VIOLATION"] = "constraint_violation";
  /* istanbul ignore next */
  cov_168sp8uxfy().s[45]++;
  OptimizationStatus["ERROR"] = "error";
  /* istanbul ignore next */
  cov_168sp8uxfy().s[46]++;
  OptimizationStatus["CANCELLED"] = "cancelled";
})(
/* istanbul ignore next */
(cov_168sp8uxfy().b[4][0]++, OptimizationStatus) ||
/* istanbul ignore next */
(cov_168sp8uxfy().b[4][1]++, exports.OptimizationStatus = OptimizationStatus = {}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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