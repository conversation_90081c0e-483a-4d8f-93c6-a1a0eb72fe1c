5d475c00043c4e98e6b3d0a046b77c5e
"use strict";

/**
 * AirDuctCalculator - Pure Calculation Functions for Air Duct Sizing
 *
 * MISSION-CRITICAL: Pure TypeScript functions for SMACNA-compliant air duct calculations
 * Extracted from UI components for reusability and tier enforcement integration
 *
 * @see docs/implementation/tier-system/tier-boundaries-specification.md
 * @see docs/developer-guide/tier-implementation-checklist.md section 2.4
 */
/* istanbul ignore next */
function cov_2g903bnq0k() {
  var path = "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\AirDuctCalculator.ts";
  var hash = "6cf0a2b9468aa41112e11f78724b3655d6711d58";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\AirDuctCalculator.ts",
    statementMap: {
      "0": {
        start: {
          line: 11,
          column: 0
        },
        end: {
          line: 11,
          column: 62
        }
      },
      "1": {
        start: {
          line: 12,
          column: 0
        },
        end: {
          line: 12,
          column: 35
        }
      },
      "2": {
        start: {
          line: 24,
          column: 8
        },
        end: {
          line: 24,
          column: 36
        }
      },
      "3": {
        start: {
          line: 26,
          column: 31
        },
        end: {
          line: 28,
          column: 20
        }
      },
      "4": {
        start: {
          line: 30,
          column: 8
        },
        end: {
          line: 35,
          column: 9
        }
      },
      "5": {
        start: {
          line: 31,
          column: 12
        },
        end: {
          line: 31,
          column: 59
        }
      },
      "6": {
        start: {
          line: 34,
          column: 12
        },
        end: {
          line: 34,
          column: 65
        }
      },
      "7": {
        start: {
          line: 41,
          column: 73
        },
        end: {
          line: 41,
          column: 79
        }
      },
      "8": {
        start: {
          line: 43,
          column: 25
        },
        end: {
          line: 43,
          column: 77
        }
      },
      "9": {
        start: {
          line: 45,
          column: 21
        },
        end: {
          line: 45,
          column: 61
        }
      },
      "10": {
        start: {
          line: 46,
          column: 25
        },
        end: {
          line: 46,
          column: 39
        }
      },
      "11": {
        start: {
          line: 48,
          column: 29
        },
        end: {
          line: 48,
          column: 90
        }
      },
      "12": {
        start: {
          line: 50,
          column: 31
        },
        end: {
          line: 50,
          column: 79
        }
      },
      "13": {
        start: {
          line: 51,
          column: 31
        },
        end: {
          line: 51,
          column: 95
        }
      },
      "14": {
        start: {
          line: 53,
          column: 27
        },
        end: {
          line: 53,
          column: 77
        }
      },
      "15": {
        start: {
          line: 54,
          column: 32
        },
        end: {
          line: 54,
          column: 98
        }
      },
      "16": {
        start: {
          line: 55,
          column: 8
        },
        end: {
          line: 70,
          column: 10
        }
      },
      "17": {
        start: {
          line: 76,
          column: 73
        },
        end: {
          line: 76,
          column: 79
        }
      },
      "18": {
        start: {
          line: 78,
          column: 34
        },
        end: {
          line: 78,
          column: 94
        }
      },
      "19": {
        start: {
          line: 80,
          column: 21
        },
        end: {
          line: 80,
          column: 43
        }
      },
      "20": {
        start: {
          line: 81,
          column: 25
        },
        end: {
          line: 81,
          column: 39
        }
      },
      "21": {
        start: {
          line: 83,
          column: 35
        },
        end: {
          line: 83,
          column: 82
        }
      },
      "22": {
        start: {
          line: 84,
          column: 34
        },
        end: {
          line: 84,
          column: 80
        }
      },
      "23": {
        start: {
          line: 85,
          column: 28
        },
        end: {
          line: 85,
          column: 68
        }
      },
      "24": {
        start: {
          line: 87,
          column: 29
        },
        end: {
          line: 87,
          column: 100
        }
      },
      "25": {
        start: {
          line: 89,
          column: 31
        },
        end: {
          line: 89,
          column: 88
        }
      },
      "26": {
        start: {
          line: 90,
          column: 31
        },
        end: {
          line: 90,
          column: 104
        }
      },
      "27": {
        start: {
          line: 92,
          column: 27
        },
        end: {
          line: 92,
          column: 95
        }
      },
      "28": {
        start: {
          line: 93,
          column: 32
        },
        end: {
          line: 93,
          column: 116
        }
      },
      "29": {
        start: {
          line: 94,
          column: 8
        },
        end: {
          line: 113,
          column: 10
        }
      },
      "30": {
        start: {
          line: 119,
          column: 27
        },
        end: {
          line: 119,
          column: 55
        }
      },
      "31": {
        start: {
          line: 120,
          column: 24
        },
        end: {
          line: 120,
          column: 32
        }
      },
      "32": {
        start: {
          line: 121,
          column: 8
        },
        end: {
          line: 138,
          column: 9
        }
      },
      "33": {
        start: {
          line: 122,
          column: 25
        },
        end: {
          line: 122,
          column: 65
        }
      },
      "34": {
        start: {
          line: 123,
          column: 29
        },
        end: {
          line: 123,
          column: 43
        }
      },
      "35": {
        start: {
          line: 125,
          column: 12
        },
        end: {
          line: 127,
          column: 13
        }
      },
      "36": {
        start: {
          line: 126,
          column: 16
        },
        end: {
          line: 126,
          column: 25
        }
      },
      "37": {
        start: {
          line: 129,
          column: 35
        },
        end: {
          line: 129,
          column: 106
        }
      },
      "38": {
        start: {
          line: 131,
          column: 34
        },
        end: {
          line: 131,
          column: 92
        }
      },
      "39": {
        start: {
          line: 132,
          column: 34
        },
        end: {
          line: 132,
          column: 128
        }
      },
      "40": {
        start: {
          line: 133,
          column: 31
        },
        end: {
          line: 133,
          column: 66
        }
      },
      "41": {
        start: {
          line: 134,
          column: 12
        },
        end: {
          line: 137,
          column: 13
        }
      },
      "42": {
        start: {
          line: 135,
          column: 16
        },
        end: {
          line: 135,
          column: 39
        }
      },
      "43": {
        start: {
          line: 136,
          column: 16
        },
        end: {
          line: 136,
          column: 40
        }
      },
      "44": {
        start: {
          line: 139,
          column: 8
        },
        end: {
          line: 139,
          column: 28
        }
      },
      "45": {
        start: {
          line: 145,
          column: 24
        },
        end: {
          line: 145,
          column: 58
        }
      },
      "46": {
        start: {
          line: 146,
          column: 25
        },
        end: {
          line: 146,
          column: 59
        }
      },
      "47": {
        start: {
          line: 147,
          column: 24
        },
        end: {
          line: 147,
          column: 32
        }
      },
      "48": {
        start: {
          line: 149,
          column: 29
        },
        end: {
          line: 149,
          column: 64
        }
      },
      "49": {
        start: {
          line: 150,
          column: 8
        },
        end: {
          line: 178,
          column: 9
        }
      },
      "50": {
        start: {
          line: 152,
          column: 34
        },
        end: {
          line: 152,
          column: 79
        }
      },
      "51": {
        start: {
          line: 153,
          column: 27
        },
        end: {
          line: 153,
          column: 70
        }
      },
      "52": {
        start: {
          line: 154,
          column: 26
        },
        end: {
          line: 154,
          column: 46
        }
      },
      "53": {
        start: {
          line: 156,
          column: 30
        },
        end: {
          line: 156,
          column: 99
        }
      },
      "54": {
        start: {
          line: 157,
          column: 29
        },
        end: {
          line: 157,
          column: 97
        }
      },
      "55": {
        start: {
          line: 159,
          column: 25
        },
        end: {
          line: 159,
          column: 53
        }
      },
      "56": {
        start: {
          line: 160,
          column: 29
        },
        end: {
          line: 160,
          column: 43
        }
      },
      "57": {
        start: {
          line: 162,
          column: 12
        },
        end: {
          line: 164,
          column: 13
        }
      },
      "58": {
        start: {
          line: 163,
          column: 16
        },
        end: {
          line: 163,
          column: 25
        }
      },
      "59": {
        start: {
          line: 166,
          column: 39
        },
        end: {
          line: 166,
          column: 92
        }
      },
      "60": {
        start: {
          line: 167,
          column: 35
        },
        end: {
          line: 167,
          column: 116
        }
      },
      "61": {
        start: {
          line: 169,
          column: 34
        },
        end: {
          line: 169,
          column: 92
        }
      },
      "62": {
        start: {
          line: 170,
          column: 34
        },
        end: {
          line: 170,
          column: 128
        }
      },
      "63": {
        start: {
          line: 171,
          column: 32
        },
        end: {
          line: 171,
          column: 81
        }
      },
      "64": {
        start: {
          line: 172,
          column: 31
        },
        end: {
          line: 172,
          column: 80
        }
      },
      "65": {
        start: {
          line: 173,
          column: 12
        },
        end: {
          line: 177,
          column: 13
        }
      },
      "66": {
        start: {
          line: 174,
          column: 16
        },
        end: {
          line: 174,
          column: 39
        }
      },
      "67": {
        start: {
          line: 175,
          column: 16
        },
        end: {
          line: 175,
          column: 37
        }
      },
      "68": {
        start: {
          line: 176,
          column: 16
        },
        end: {
          line: 176,
          column: 39
        }
      },
      "69": {
        start: {
          line: 179,
          column: 8
        },
        end: {
          line: 179,
          column: 56
        }
      },
      "70": {
        start: {
          line: 185,
          column: 8
        },
        end: {
          line: 185,
          column: 86
        }
      },
      "71": {
        start: {
          line: 191,
          column: 8
        },
        end: {
          line: 191,
          column: 61
        }
      },
      "72": {
        start: {
          line: 197,
          column: 8
        },
        end: {
          line: 197,
          column: 65
        }
      },
      "73": {
        start: {
          line: 203,
          column: 30
        },
        end: {
          line: 203,
          column: 109
        }
      },
      "74": {
        start: {
          line: 204,
          column: 26
        },
        end: {
          line: 204,
          column: 55
        }
      },
      "75": {
        start: {
          line: 206,
          column: 28
        },
        end: {
          line: 206,
          column: 41
        }
      },
      "76": {
        start: {
          line: 207,
          column: 27
        },
        end: {
          line: 207,
          column: 40
        }
      },
      "77": {
        start: {
          line: 209,
          column: 27
        },
        end: {
          line: 209,
          column: 32
        }
      },
      "78": {
        start: {
          line: 210,
          column: 35
        },
        end: {
          line: 210,
          column: 42
        }
      },
      "79": {
        start: {
          line: 212,
          column: 25
        },
        end: {
          line: 212,
          column: 72
        }
      },
      "80": {
        start: {
          line: 214,
          column: 31
        },
        end: {
          line: 214,
          column: 89
        }
      },
      "81": {
        start: {
          line: 217,
          column: 32
        },
        end: {
          line: 217,
          column: 127
        }
      },
      "82": {
        start: {
          line: 218,
          column: 33
        },
        end: {
          line: 218,
          column: 54
        }
      },
      "83": {
        start: {
          line: 219,
          column: 8
        },
        end: {
          line: 219,
          column: 32
        }
      },
      "84": {
        start: {
          line: 225,
          column: 28
        },
        end: {
          line: 225,
          column: 41
        }
      },
      "85": {
        start: {
          line: 226,
          column: 27
        },
        end: {
          line: 226,
          column: 40
        }
      },
      "86": {
        start: {
          line: 227,
          column: 35
        },
        end: {
          line: 227,
          column: 42
        }
      },
      "87": {
        start: {
          line: 228,
          column: 8
        },
        end: {
          line: 228,
          column: 63
        }
      },
      "88": {
        start: {
          line: 234,
          column: 30
        },
        end: {
          line: 234,
          column: 109
        }
      },
      "89": {
        start: {
          line: 235,
          column: 26
        },
        end: {
          line: 235,
          column: 55
        }
      },
      "90": {
        start: {
          line: 236,
          column: 27
        },
        end: {
          line: 236,
          column: 40
        }
      },
      "91": {
        start: {
          line: 237,
          column: 34
        },
        end: {
          line: 237,
          column: 56
        }
      },
      "92": {
        start: {
          line: 239,
          column: 8
        },
        end: {
          line: 241,
          column: 9
        }
      },
      "93": {
        start: {
          line: 240,
          column: 12
        },
        end: {
          line: 240,
          column: 33
        }
      },
      "94": {
        start: {
          line: 243,
          column: 16
        },
        end: {
          line: 243,
          column: 20
        }
      },
      "95": {
        start: {
          line: 244,
          column: 8
        },
        end: {
          line: 249,
          column: 9
        }
      },
      "96": {
        start: {
          line: 244,
          column: 21
        },
        end: {
          line: 244,
          column: 22
        }
      },
      "97": {
        start: {
          line: 245,
          column: 25
        },
        end: {
          line: 245,
          column: 117
        }
      },
      "98": {
        start: {
          line: 246,
          column: 12
        },
        end: {
          line: 247,
          column: 22
        }
      },
      "99": {
        start: {
          line: 247,
          column: 16
        },
        end: {
          line: 247,
          column: 22
        }
      },
      "100": {
        start: {
          line: 248,
          column: 12
        },
        end: {
          line: 248,
          column: 21
        }
      },
      "101": {
        start: {
          line: 250,
          column: 8
        },
        end: {
          line: 250,
          column: 17
        }
      },
      "102": {
        start: {
          line: 256,
          column: 25
        },
        end: {
          line: 256,
          column: 27
        }
      },
      "103": {
        start: {
          line: 257,
          column: 30
        },
        end: {
          line: 257,
          column: 34
        }
      },
      "104": {
        start: {
          line: 258,
          column: 30
        },
        end: {
          line: 258,
          column: 34
        }
      },
      "105": {
        start: {
          line: 259,
          column: 32
        },
        end: {
          line: 259,
          column: 36
        }
      },
      "106": {
        start: {
          line: 261,
          column: 29
        },
        end: {
          line: 261,
          column: 36
        }
      },
      "107": {
        start: {
          line: 262,
          column: 8
        },
        end: {
          line: 270,
          column: 9
        }
      },
      "108": {
        start: {
          line: 263,
          column: 12
        },
        end: {
          line: 263,
          column: 135
        }
      },
      "109": {
        start: {
          line: 264,
          column: 12
        },
        end: {
          line: 264,
          column: 38
        }
      },
      "110": {
        start: {
          line: 266,
          column: 13
        },
        end: {
          line: 270,
          column: 9
        }
      },
      "111": {
        start: {
          line: 267,
          column: 12
        },
        end: {
          line: 267,
          column: 134
        }
      },
      "112": {
        start: {
          line: 268,
          column: 12
        },
        end: {
          line: 268,
          column: 38
        }
      },
      "113": {
        start: {
          line: 269,
          column: 12
        },
        end: {
          line: 269,
          column: 36
        }
      },
      "114": {
        start: {
          line: 272,
          column: 8
        },
        end: {
          line: 280,
          column: 9
        }
      },
      "115": {
        start: {
          line: 273,
          column: 12
        },
        end: {
          line: 279,
          column: 13
        }
      },
      "116": {
        start: {
          line: 274,
          column: 16
        },
        end: {
          line: 274,
          column: 112
        }
      },
      "117": {
        start: {
          line: 275,
          column: 16
        },
        end: {
          line: 275,
          column: 40
        }
      },
      "118": {
        start: {
          line: 277,
          column: 17
        },
        end: {
          line: 279,
          column: 13
        }
      },
      "119": {
        start: {
          line: 278,
          column: 16
        },
        end: {
          line: 278,
          column: 114
        }
      },
      "120": {
        start: {
          line: 282,
          column: 8
        },
        end: {
          line: 284,
          column: 9
        }
      },
      "121": {
        start: {
          line: 283,
          column: 12
        },
        end: {
          line: 283,
          column: 92
        }
      },
      "122": {
        start: {
          line: 285,
          column: 26
        },
        end: {
          line: 287,
          column: 65
        }
      },
      "123": {
        start: {
          line: 288,
          column: 8
        },
        end: {
          line: 294,
          column: 10
        }
      },
      "124": {
        start: {
          line: 300,
          column: 32
        },
        end: {
          line: 300,
          column: 34
        }
      },
      "125": {
        start: {
          line: 302,
          column: 8
        },
        end: {
          line: 307,
          column: 9
        }
      },
      "126": {
        start: {
          line: 303,
          column: 12
        },
        end: {
          line: 303,
          column: 108
        }
      },
      "127": {
        start: {
          line: 305,
          column: 13
        },
        end: {
          line: 307,
          column: 9
        }
      },
      "128": {
        start: {
          line: 306,
          column: 12
        },
        end: {
          line: 306,
          column: 95
        }
      },
      "129": {
        start: {
          line: 309,
          column: 8
        },
        end: {
          line: 311,
          column: 9
        }
      },
      "130": {
        start: {
          line: 310,
          column: 12
        },
        end: {
          line: 310,
          column: 110
        }
      },
      "131": {
        start: {
          line: 313,
          column: 8
        },
        end: {
          line: 315,
          column: 9
        }
      },
      "132": {
        start: {
          line: 314,
          column: 12
        },
        end: {
          line: 314,
          column: 115
        }
      },
      "133": {
        start: {
          line: 316,
          column: 8
        },
        end: {
          line: 316,
          column: 31
        }
      },
      "134": {
        start: {
          line: 322,
          column: 8
        },
        end: {
          line: 322,
          column: 117
        }
      },
      "135": {
        start: {
          line: 322,
          column: 52
        },
        end: {
          line: 322,
          column: 115
        }
      },
      "136": {
        start: {
          line: 328,
          column: 8
        },
        end: {
          line: 333,
          column: 10
        }
      },
      "137": {
        start: {
          line: 339,
          column: 8
        },
        end: {
          line: 341,
          column: 9
        }
      },
      "138": {
        start: {
          line: 340,
          column: 12
        },
        end: {
          line: 340,
          column: 62
        }
      },
      "139": {
        start: {
          line: 342,
          column: 8
        },
        end: {
          line: 344,
          column: 9
        }
      },
      "140": {
        start: {
          line: 343,
          column: 12
        },
        end: {
          line: 343,
          column: 68
        }
      },
      "141": {
        start: {
          line: 345,
          column: 8
        },
        end: {
          line: 347,
          column: 9
        }
      },
      "142": {
        start: {
          line: 346,
          column: 12
        },
        end: {
          line: 346,
          column: 74
        }
      },
      "143": {
        start: {
          line: 348,
          column: 8
        },
        end: {
          line: 350,
          column: 9
        }
      },
      "144": {
        start: {
          line: 349,
          column: 12
        },
        end: {
          line: 349,
          column: 68
        }
      },
      "145": {
        start: {
          line: 356,
          column: 8
        },
        end: {
          line: 356,
          column: 47
        }
      },
      "146": {
        start: {
          line: 362,
          column: 8
        },
        end: {
          line: 362,
          column: 43
        }
      },
      "147": {
        start: {
          line: 368,
          column: 8
        },
        end: {
          line: 371,
          column: 10
        }
      },
      "148": {
        start: {
          line: 374,
          column: 0
        },
        end: {
          line: 374,
          column: 46
        }
      },
      "149": {
        start: {
          line: 376,
          column: 0
        },
        end: {
          line: 379,
          column: 2
        }
      },
      "150": {
        start: {
          line: 381,
          column: 0
        },
        end: {
          line: 384,
          column: 2
        }
      },
      "151": {
        start: {
          line: 386,
          column: 0
        },
        end: {
          line: 394,
          column: 2
        }
      },
      "152": {
        start: {
          line: 396,
          column: 0
        },
        end: {
          line: 400,
          column: 2
        }
      },
      "153": {
        start: {
          line: 401,
          column: 0
        },
        end: {
          line: 401,
          column: 36
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 22,
            column: 4
          },
          end: {
            line: 22,
            column: 5
          }
        },
        loc: {
          start: {
            line: 22,
            column: 39
          },
          end: {
            line: 36,
            column: 5
          }
        },
        line: 22
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 40,
            column: 4
          },
          end: {
            line: 40,
            column: 5
          }
        },
        loc: {
          start: {
            line: 40,
            column: 38
          },
          end: {
            line: 71,
            column: 5
          }
        },
        line: 40
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 75,
            column: 4
          },
          end: {
            line: 75,
            column: 5
          }
        },
        loc: {
          start: {
            line: 75,
            column: 44
          },
          end: {
            line: 114,
            column: 5
          }
        },
        line: 75
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 118,
            column: 4
          },
          end: {
            line: 118,
            column: 5
          }
        },
        loc: {
          start: {
            line: 118,
            column: 61
          },
          end: {
            line: 140,
            column: 5
          }
        },
        line: 118
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 144,
            column: 4
          },
          end: {
            line: 144,
            column: 5
          }
        },
        loc: {
          start: {
            line: 144,
            column: 69
          },
          end: {
            line: 180,
            column: 5
          }
        },
        line: 144
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 184,
            column: 4
          },
          end: {
            line: 184,
            column: 5
          }
        },
        loc: {
          start: {
            line: 184,
            column: 54
          },
          end: {
            line: 186,
            column: 5
          }
        },
        line: 184
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 190,
            column: 4
          },
          end: {
            line: 190,
            column: 5
          }
        },
        loc: {
          start: {
            line: 190,
            column: 53
          },
          end: {
            line: 192,
            column: 5
          }
        },
        line: 190
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 196,
            column: 4
          },
          end: {
            line: 196,
            column: 5
          }
        },
        loc: {
          start: {
            line: 196,
            column: 47
          },
          end: {
            line: 198,
            column: 5
          }
        },
        line: 196
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 202,
            column: 4
          },
          end: {
            line: 202,
            column: 5
          }
        },
        loc: {
          start: {
            line: 202,
            column: 71
          },
          end: {
            line: 220,
            column: 5
          }
        },
        line: 202
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 224,
            column: 4
          },
          end: {
            line: 224,
            column: 5
          }
        },
        loc: {
          start: {
            line: 224,
            column: 55
          },
          end: {
            line: 229,
            column: 5
          }
        },
        line: 224
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 233,
            column: 4
          },
          end: {
            line: 233,
            column: 5
          }
        },
        loc: {
          start: {
            line: 233,
            column: 65
          },
          end: {
            line: 251,
            column: 5
          }
        },
        line: 233
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 255,
            column: 4
          },
          end: {
            line: 255,
            column: 5
          }
        },
        loc: {
          start: {
            line: 255,
            column: 36
          },
          end: {
            line: 295,
            column: 5
          }
        },
        line: 255
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 299,
            column: 4
          },
          end: {
            line: 299,
            column: 5
          }
        },
        loc: {
          start: {
            line: 299,
            column: 52
          },
          end: {
            line: 317,
            column: 5
          }
        },
        line: 299
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 321,
            column: 4
          },
          end: {
            line: 321,
            column: 5
          }
        },
        loc: {
          start: {
            line: 321,
            column: 58
          },
          end: {
            line: 323,
            column: 5
          }
        },
        line: 321
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 322,
            column: 36
          },
          end: {
            line: 322,
            column: 37
          }
        },
        loc: {
          start: {
            line: 322,
            column: 52
          },
          end: {
            line: 322,
            column: 115
          }
        },
        line: 322
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 327,
            column: 4
          },
          end: {
            line: 327,
            column: 5
          }
        },
        loc: {
          start: {
            line: 327,
            column: 37
          },
          end: {
            line: 334,
            column: 5
          }
        },
        line: 327
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 338,
            column: 4
          },
          end: {
            line: 338,
            column: 5
          }
        },
        loc: {
          start: {
            line: 338,
            column: 34
          },
          end: {
            line: 351,
            column: 5
          }
        },
        line: 338
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 355,
            column: 4
          },
          end: {
            line: 355,
            column: 5
          }
        },
        loc: {
          start: {
            line: 355,
            column: 26
          },
          end: {
            line: 357,
            column: 5
          }
        },
        line: 355
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 361,
            column: 4
          },
          end: {
            line: 361,
            column: 5
          }
        },
        loc: {
          start: {
            line: 361,
            column: 31
          },
          end: {
            line: 363,
            column: 5
          }
        },
        line: 361
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 367,
            column: 4
          },
          end: {
            line: 367,
            column: 5
          }
        },
        loc: {
          start: {
            line: 367,
            column: 30
          },
          end: {
            line: 372,
            column: 5
          }
        },
        line: 367
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 26,
            column: 31
          },
          end: {
            line: 28,
            column: 20
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 27,
            column: 14
          },
          end: {
            line: 27,
            column: 44
          }
        }, {
          start: {
            line: 28,
            column: 14
          },
          end: {
            line: 28,
            column: 20
          }
        }],
        line: 26
      },
      "1": {
        loc: {
          start: {
            line: 30,
            column: 8
          },
          end: {
            line: 35,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 8
          },
          end: {
            line: 35,
            column: 9
          }
        }, {
          start: {
            line: 33,
            column: 13
          },
          end: {
            line: 35,
            column: 9
          }
        }],
        line: 30
      },
      "2": {
        loc: {
          start: {
            line: 41,
            column: 39
          },
          end: {
            line: 41,
            column: 68
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 41,
            column: 50
          },
          end: {
            line: 41,
            column: 68
          }
        }],
        line: 41
      },
      "3": {
        loc: {
          start: {
            line: 76,
            column: 39
          },
          end: {
            line: 76,
            column: 68
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 76,
            column: 50
          },
          end: {
            line: 76,
            column: 68
          }
        }],
        line: 76
      },
      "4": {
        loc: {
          start: {
            line: 125,
            column: 12
          },
          end: {
            line: 127,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 125,
            column: 12
          },
          end: {
            line: 127,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 125
      },
      "5": {
        loc: {
          start: {
            line: 125,
            column: 16
          },
          end: {
            line: 125,
            column: 104
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 125,
            column: 16
          },
          end: {
            line: 125,
            column: 58
          }
        }, {
          start: {
            line: 125,
            column: 62
          },
          end: {
            line: 125,
            column: 104
          }
        }],
        line: 125
      },
      "6": {
        loc: {
          start: {
            line: 134,
            column: 12
          },
          end: {
            line: 137,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 134,
            column: 12
          },
          end: {
            line: 137,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 134
      },
      "7": {
        loc: {
          start: {
            line: 162,
            column: 12
          },
          end: {
            line: 164,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 162,
            column: 12
          },
          end: {
            line: 164,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 162
      },
      "8": {
        loc: {
          start: {
            line: 162,
            column: 16
          },
          end: {
            line: 162,
            column: 104
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 162,
            column: 16
          },
          end: {
            line: 162,
            column: 58
          }
        }, {
          start: {
            line: 162,
            column: 62
          },
          end: {
            line: 162,
            column: 104
          }
        }],
        line: 162
      },
      "9": {
        loc: {
          start: {
            line: 171,
            column: 32
          },
          end: {
            line: 171,
            column: 81
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 171,
            column: 52
          },
          end: {
            line: 171,
            column: 77
          }
        }, {
          start: {
            line: 171,
            column: 80
          },
          end: {
            line: 171,
            column: 81
          }
        }],
        line: 171
      },
      "10": {
        loc: {
          start: {
            line: 173,
            column: 12
          },
          end: {
            line: 177,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 173,
            column: 12
          },
          end: {
            line: 177,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 173
      },
      "11": {
        loc: {
          start: {
            line: 203,
            column: 30
          },
          end: {
            line: 203,
            column: 109
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 203,
            column: 30
          },
          end: {
            line: 203,
            column: 64
          }
        }, {
          start: {
            line: 203,
            column: 68
          },
          end: {
            line: 203,
            column: 109
          }
        }],
        line: 203
      },
      "12": {
        loc: {
          start: {
            line: 234,
            column: 30
          },
          end: {
            line: 234,
            column: 109
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 234,
            column: 30
          },
          end: {
            line: 234,
            column: 64
          }
        }, {
          start: {
            line: 234,
            column: 68
          },
          end: {
            line: 234,
            column: 109
          }
        }],
        line: 234
      },
      "13": {
        loc: {
          start: {
            line: 239,
            column: 8
          },
          end: {
            line: 241,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 239,
            column: 8
          },
          end: {
            line: 241,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 239
      },
      "14": {
        loc: {
          start: {
            line: 246,
            column: 12
          },
          end: {
            line: 247,
            column: 22
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 246,
            column: 12
          },
          end: {
            line: 247,
            column: 22
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 246
      },
      "15": {
        loc: {
          start: {
            line: 262,
            column: 8
          },
          end: {
            line: 270,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 262,
            column: 8
          },
          end: {
            line: 270,
            column: 9
          }
        }, {
          start: {
            line: 266,
            column: 13
          },
          end: {
            line: 270,
            column: 9
          }
        }],
        line: 262
      },
      "16": {
        loc: {
          start: {
            line: 266,
            column: 13
          },
          end: {
            line: 270,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 266,
            column: 13
          },
          end: {
            line: 270,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 266
      },
      "17": {
        loc: {
          start: {
            line: 272,
            column: 8
          },
          end: {
            line: 280,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 272,
            column: 8
          },
          end: {
            line: 280,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 272
      },
      "18": {
        loc: {
          start: {
            line: 273,
            column: 12
          },
          end: {
            line: 279,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 273,
            column: 12
          },
          end: {
            line: 279,
            column: 13
          }
        }, {
          start: {
            line: 277,
            column: 17
          },
          end: {
            line: 279,
            column: 13
          }
        }],
        line: 273
      },
      "19": {
        loc: {
          start: {
            line: 277,
            column: 17
          },
          end: {
            line: 279,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 277,
            column: 17
          },
          end: {
            line: 279,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 277
      },
      "20": {
        loc: {
          start: {
            line: 282,
            column: 8
          },
          end: {
            line: 284,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 282,
            column: 8
          },
          end: {
            line: 284,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 282
      },
      "21": {
        loc: {
          start: {
            line: 285,
            column: 26
          },
          end: {
            line: 287,
            column: 65
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 285,
            column: 26
          },
          end: {
            line: 285,
            column: 47
          }
        }, {
          start: {
            line: 286,
            column: 12
          },
          end: {
            line: 286,
            column: 65
          }
        }, {
          start: {
            line: 287,
            column: 12
          },
          end: {
            line: 287,
            column: 65
          }
        }],
        line: 285
      },
      "22": {
        loc: {
          start: {
            line: 302,
            column: 8
          },
          end: {
            line: 307,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 302,
            column: 8
          },
          end: {
            line: 307,
            column: 9
          }
        }, {
          start: {
            line: 305,
            column: 13
          },
          end: {
            line: 307,
            column: 9
          }
        }],
        line: 302
      },
      "23": {
        loc: {
          start: {
            line: 305,
            column: 13
          },
          end: {
            line: 307,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 305,
            column: 13
          },
          end: {
            line: 307,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 305
      },
      "24": {
        loc: {
          start: {
            line: 309,
            column: 8
          },
          end: {
            line: 311,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 309,
            column: 8
          },
          end: {
            line: 311,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 309
      },
      "25": {
        loc: {
          start: {
            line: 309,
            column: 12
          },
          end: {
            line: 309,
            column: 60
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 309,
            column: 12
          },
          end: {
            line: 309,
            column: 31
          }
        }, {
          start: {
            line: 309,
            column: 35
          },
          end: {
            line: 309,
            column: 60
          }
        }],
        line: 309
      },
      "26": {
        loc: {
          start: {
            line: 313,
            column: 8
          },
          end: {
            line: 315,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 313,
            column: 8
          },
          end: {
            line: 315,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 313
      },
      "27": {
        loc: {
          start: {
            line: 313,
            column: 12
          },
          end: {
            line: 313,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 313,
            column: 12
          },
          end: {
            line: 313,
            column: 50
          }
        }, {
          start: {
            line: 313,
            column: 54
          },
          end: {
            line: 313,
            column: 77
          }
        }],
        line: 313
      },
      "28": {
        loc: {
          start: {
            line: 322,
            column: 52
          },
          end: {
            line: 322,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 322,
            column: 104
          },
          end: {
            line: 322,
            column: 108
          }
        }, {
          start: {
            line: 322,
            column: 111
          },
          end: {
            line: 322,
            column: 115
          }
        }],
        line: 322
      },
      "29": {
        loc: {
          start: {
            line: 339,
            column: 8
          },
          end: {
            line: 341,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 339,
            column: 8
          },
          end: {
            line: 341,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 339
      },
      "30": {
        loc: {
          start: {
            line: 342,
            column: 8
          },
          end: {
            line: 344,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 342,
            column: 8
          },
          end: {
            line: 344,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 342
      },
      "31": {
        loc: {
          start: {
            line: 345,
            column: 8
          },
          end: {
            line: 347,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 345,
            column: 8
          },
          end: {
            line: 347,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 345
      },
      "32": {
        loc: {
          start: {
            line: 348,
            column: 8
          },
          end: {
            line: 350,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 348,
            column: 8
          },
          end: {
            line: 350,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 348
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0],
      "3": [0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\AirDuctCalculator.ts",
      mappings: ";AAAA;;;;;;;;GAQG;;;AAyDH;;;GAGG;AACH,MAAa,iBAAiB;IA+B5B;;;OAGG;IACI,MAAM,CAAC,mBAAmB,CAAC,MAAwB;QACxD,kBAAkB;QAClB,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAE5B,sCAAsC;QACtC,MAAM,cAAc,GAAG,MAAM,CAAC,KAAK,KAAK,QAAQ;YAC9C,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC;YAChC,CAAC,CAAC,MAAM,CAAC;QAEX,yCAAyC;QACzC,IAAI,cAAc,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;YACxC,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;QACjD,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,CAAC,wBAAwB,CAAC,cAAc,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,kBAAkB,CAAC,MAAwB;QACxD,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,QAAQ,GAAG,kBAAkB,EAAE,GAAG,MAAM,CAAC;QAExE,2DAA2D;QAC3D,MAAM,QAAQ,GAAG,IAAI,CAAC,wBAAwB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;QAEtE,8BAA8B;QAC9B,MAAM,IAAI,GAAG,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ;QAC/D,MAAM,QAAQ,GAAG,OAAO,GAAG,IAAI,CAAC,CAAC,MAAM;QAEvC,0BAA0B;QAC1B,MAAM,YAAY,GAAG,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,GAAG,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAEnF,gDAAgD;QAChD,MAAM,cAAc,GAAG,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QACxE,MAAM,cAAc,GAAG,IAAI,CAAC,uBAAuB,CAAC,cAAc,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAExF,wCAAwC;QACxC,MAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;QACtE,MAAM,eAAe,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;QAE3F,OAAO;YACL,QAAQ;YACR,IAAI;YACJ,QAAQ;YACR,YAAY;YACZ,cAAc;YACd,cAAc;YACd,SAAS,EAAE,UAAU,CAAC,SAAS;YAC/B,QAAQ,EAAE,UAAU,CAAC,QAAQ;YAC7B,eAAe;YACf,mBAAmB,EAAE;gBACnB,MAAM,EAAE,UAAU,CAAC,eAAe;gBAClC,MAAM,EAAE,UAAU,CAAC,eAAe;gBAClC,iBAAiB,EAAE,UAAU,CAAC,iBAAiB;aAChD;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,wBAAwB,CAAC,MAAwB;QAC9D,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,QAAQ,GAAG,kBAAkB,EAAE,GAAG,MAAM,CAAC;QAExE,0BAA0B;QAC1B,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,gCAAgC,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;QAEvF,8BAA8B;QAC9B,MAAM,IAAI,GAAG,CAAC,KAAK,GAAG,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,QAAQ;QAC7C,MAAM,QAAQ,GAAG,OAAO,GAAG,IAAI,CAAC,CAAC,MAAM;QAEvC,+CAA+C;QAC/C,MAAM,kBAAkB,GAAG,IAAI,CAAC,2BAA2B,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QAC3E,MAAM,iBAAiB,GAAG,IAAI,CAAC,0BAA0B,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QACzE,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QAE7D,oDAAoD;QACpD,MAAM,YAAY,GAAG,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,GAAG,EAAE,kBAAkB,EAAE,QAAQ,CAAC,CAAC;QAE7F,gDAAgD;QAChD,MAAM,cAAc,GAAG,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE,iBAAiB,CAAC,CAAC;QACjF,MAAM,cAAc,GAAG,IAAI,CAAC,uBAAuB,CAAC,cAAc,EAAE,QAAQ,EAAE,iBAAiB,CAAC,CAAC;QAEjG,wCAAwC;QACxC,MAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC;QACxF,MAAM,eAAe,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;QAE7G,OAAO;YACL,KAAK;YACL,MAAM;YACN,IAAI;YACJ,QAAQ;YACR,YAAY;YACZ,cAAc;YACd,cAAc;YACd,kBAAkB;YAClB,iBAAiB;YACjB,WAAW;YACX,SAAS,EAAE,UAAU,CAAC,SAAS;YAC/B,QAAQ,EAAE,UAAU,CAAC,QAAQ;YAC7B,eAAe;YACf,mBAAmB,EAAE;gBACnB,MAAM,EAAE,UAAU,CAAC,eAAe;gBAClC,MAAM,EAAE,UAAU,CAAC,eAAe;gBAClC,iBAAiB,EAAE,UAAU,CAAC,iBAAiB;aAChD;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,wBAAwB,CAAC,OAAe,EAAE,cAAsB;QAC7E,IAAI,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC;QAChD,IAAI,SAAS,GAAG,QAAQ,CAAC;QAEzB,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YACjD,MAAM,IAAI,GAAG,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ;YAC/D,MAAM,QAAQ,GAAG,OAAO,GAAG,IAAI,CAAC,CAAC,MAAM;YAEvC,wBAAwB;YACxB,IAAI,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,GAAG,IAAI,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;gBAC7F,SAAS;YACX,CAAC;YAED,iCAAiC;YACjC,MAAM,cAAc,GAAG,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,GAAG,EAAE,QAAQ,EAAE,kBAAkB,CAAC,CAAC;YAE/F,mEAAmE;YACnE,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,GAAG,cAAc,CAAC,GAAG,cAAc,CAAC;YACjF,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC;YACrH,MAAM,UAAU,GAAG,aAAa,GAAG,aAAa,GAAG,GAAG,CAAC,CAAC,+BAA+B;YAEvF,IAAI,UAAU,GAAG,SAAS,EAAE,CAAC;gBAC3B,SAAS,GAAG,UAAU,CAAC;gBACvB,YAAY,GAAG,QAAQ,CAAC;YAC1B,CAAC;QACH,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,gCAAgC,CAAC,OAAe,EAAE,cAAsB;QACrF,IAAI,SAAS,GAAG,IAAI,CAAC,0BAA0B,CAAC,CAAC,CAAC,CAAC;QACnD,IAAI,UAAU,GAAG,IAAI,CAAC,0BAA0B,CAAC,CAAC,CAAC,CAAC;QACpD,IAAI,SAAS,GAAG,QAAQ,CAAC;QAEzB,6DAA6D;QAC7D,MAAM,YAAY,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAEzD,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE,CAAC;YACvC,iCAAiC;YACjC,MAAM,aAAa,GAAG,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,0BAA0B;YAC/F,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC,CAAC,SAAS;YACrE,MAAM,KAAK,GAAG,WAAW,GAAG,MAAM,CAAC;YAEnC,kCAAkC;YAClC,MAAM,SAAS,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,IAAI,CAAC,0BAA0B,CAAC,CAAC;YACxF,MAAM,QAAQ,GAAG,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,IAAI,CAAC,0BAA0B,CAAC,CAAC;YAEtF,8BAA8B;YAC9B,MAAM,IAAI,GAAG,CAAC,QAAQ,GAAG,SAAS,CAAC,GAAG,GAAG,CAAC,CAAC,QAAQ;YACnD,MAAM,QAAQ,GAAG,OAAO,GAAG,IAAI,CAAC,CAAC,MAAM;YAEvC,wBAAwB;YACxB,IAAI,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,GAAG,IAAI,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;gBAC7F,SAAS;YACX,CAAC;YAED,6CAA6C;YAC7C,MAAM,kBAAkB,GAAG,IAAI,CAAC,2BAA2B,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;YACjF,MAAM,cAAc,GAAG,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,GAAG,EAAE,kBAAkB,EAAE,kBAAkB,CAAC,CAAC;YAEzG,0EAA0E;YAC1E,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,GAAG,cAAc,CAAC,GAAG,cAAc,CAAC;YACjF,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC;YACrH,MAAM,WAAW,GAAG,WAAW,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iCAAiC;YACxG,MAAM,UAAU,GAAG,aAAa,GAAG,aAAa,GAAG,GAAG,GAAG,WAAW,CAAC;YAErE,IAAI,UAAU,GAAG,SAAS,EAAE,CAAC;gBAC3B,SAAS,GAAG,UAAU,CAAC;gBACvB,SAAS,GAAG,QAAQ,CAAC;gBACrB,UAAU,GAAG,SAAS,CAAC;YACzB,CAAC;QACH,CAAC;QAED,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC;IAClD,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,2BAA2B,CAAC,KAAa,EAAE,MAAc;QACrE,OAAO,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,MAAM,EAAE,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,MAAM,EAAE,IAAI,CAAC,CAAC;IAChF,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,0BAA0B,CAAC,KAAa,EAAE,MAAc;QACpE,OAAO,CAAC,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC;IACvD,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,oBAAoB,CAAC,KAAa,EAAE,MAAc;QAC9D,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IAC3D,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,qBAAqB,CAAC,QAAgB,EAAE,MAAc,EAAE,QAAgB,EAAE,QAAgB;QACtG,MAAM,aAAa,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,CAAC;QACtG,MAAM,SAAS,GAAG,aAAa,CAAC,eAAe,CAAC;QAEhD,gBAAgB;QAChB,MAAM,WAAW,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAC,aAAa;QAChD,MAAM,UAAU,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAC,iBAAiB;QAEnD,0DAA0D;QAC1D,MAAM,UAAU,GAAG,KAAK,CAAC,CAAC,SAAS;QACnC,MAAM,kBAAkB,GAAG,OAAO,CAAC,CAAC,QAAQ;QAE5C,4BAA4B;QAC5B,MAAM,QAAQ,GAAG,CAAC,WAAW,GAAG,UAAU,CAAC,GAAG,kBAAkB,CAAC;QAEjE,4BAA4B;QAC5B,MAAM,cAAc,GAAG,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAElF,gEAAgE;QAChE,0CAA0C;QAC1C,MAAM,eAAe,GAAG,cAAc,GAAG,CAAC,MAAM,GAAG,UAAU,CAAC,GAAG,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;QACxH,MAAM,gBAAgB,GAAG,eAAe,GAAG,GAAG,CAAC,CAAC,6BAA6B;QAE7E,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,uBAAuB,CAAC,QAAgB,EAAE,QAAgB;QACtE,MAAM,WAAW,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAC,aAAa;QAChD,MAAM,UAAU,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAC,iBAAiB;QACnD,MAAM,kBAAkB,GAAG,OAAO,CAAC,CAAC,uCAAuC;QAE3E,OAAO,CAAC,WAAW,GAAG,UAAU,CAAC,GAAG,kBAAkB,CAAC;IACzD,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,uBAAuB,CAAC,QAAgB,EAAE,QAAgB,EAAE,QAAgB;QACxF,MAAM,aAAa,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,CAAC;QACtG,MAAM,SAAS,GAAG,aAAa,CAAC,eAAe,CAAC;QAChD,MAAM,UAAU,GAAG,QAAQ,GAAG,EAAE,CAAC;QACjC,MAAM,iBAAiB,GAAG,SAAS,GAAG,UAAU,CAAC;QAEjD,+BAA+B;QAC/B,IAAI,QAAQ,GAAG,IAAI,EAAE,CAAC;YACpB,OAAO,EAAE,GAAG,QAAQ,CAAC;QACvB,CAAC;QAED,wEAAwE;QACxE,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,gBAAgB;QAC9B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;YAC5B,MAAM,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,GAAG,GAAG,IAAI,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC1G,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,MAAM;gBAAE,MAAM;YACvC,CAAC,GAAG,IAAI,CAAC;QACX,CAAC;QAED,OAAO,CAAC,CAAC;IACX,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,eAAe,CAAC,OAAY;QAOzC,MAAM,QAAQ,GAAa,EAAE,CAAC;QAC9B,IAAI,eAAe,GAAG,IAAI,CAAC;QAC3B,IAAI,eAAe,GAAG,IAAI,CAAC;QAC3B,IAAI,iBAAiB,GAAG,IAAI,CAAC;QAE7B,sBAAsB;QACtB,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC;QAC7B,IAAI,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;YAC/C,QAAQ,CAAC,IAAI,CAAC,YAAY,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,sCAAsC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,CAAC;YAC3H,iBAAiB,GAAG,KAAK,CAAC;QAC5B,CAAC;aAAM,IAAI,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;YACtD,QAAQ,CAAC,IAAI,CAAC,YAAY,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,qCAAqC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,CAAC;YAC1H,iBAAiB,GAAG,KAAK,CAAC;YAC1B,eAAe,GAAG,KAAK,CAAC;QAC1B,CAAC;QAED,kDAAkD;QAClD,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;YACxB,IAAI,OAAO,CAAC,WAAW,GAAG,GAAG,EAAE,CAAC;gBAC9B,QAAQ,CAAC,IAAI,CAAC,gBAAgB,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,kCAAkC,CAAC,CAAC;gBAChG,eAAe,GAAG,KAAK,CAAC;YAC1B,CAAC;iBAAM,IAAI,OAAO,CAAC,WAAW,GAAG,GAAG,EAAE,CAAC;gBACrC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,oCAAoC,CAAC,CAAC;YACpG,CAAC;QACH,CAAC;QAED,kBAAkB;QAClB,IAAI,OAAO,CAAC,IAAI,GAAG,GAAG,EAAE,CAAC;YACvB,QAAQ,CAAC,IAAI,CAAC,gEAAgE,CAAC,CAAC;QAClF,CAAC;QAED,MAAM,SAAS,GAAG,QAAQ,CAAC,MAAM,KAAK,CAAC;YACrC,QAAQ,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,GAAG,GAAG;YACrD,QAAQ,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,GAAG,GAAG,CAAC;QAExD,OAAO;YACL,SAAS;YACT,QAAQ;YACR,eAAe;YACf,eAAe;YACf,iBAAiB;SAClB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,uBAAuB,CAAC,MAAwB,EAAE,OAAY;QAC3E,MAAM,eAAe,GAAa,EAAE,CAAC;QAErC,2BAA2B;QAC3B,IAAI,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC;YACjE,eAAe,CAAC,IAAI,CAAC,yEAAyE,CAAC,CAAC;QAClG,CAAC;aAAM,IAAI,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC;YACxE,eAAe,CAAC,IAAI,CAAC,4DAA4D,CAAC,CAAC;QACrF,CAAC;QAED,uDAAuD;QACvD,IAAI,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,WAAW,GAAG,GAAG,EAAE,CAAC;YACrD,eAAe,CAAC,IAAI,CAAC,2EAA2E,CAAC,CAAC;QACpG,CAAC;QAED,2BAA2B;QAC3B,IAAI,MAAM,CAAC,QAAQ,KAAK,kBAAkB,IAAI,OAAO,CAAC,QAAQ,GAAG,IAAI,EAAE,CAAC;YACtE,eAAe,CAAC,IAAI,CAAC,gFAAgF,CAAC,CAAC;QACzG,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,uBAAuB,CAAC,MAAc,EAAE,aAAuB;QAC5E,OAAO,aAAa,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CACzC,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAChE,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,iBAAiB,CAAC,MAAwB;QACvD,OAAO;YACL,GAAG,MAAM;YACT,OAAO,EAAE,MAAM,CAAC,OAAO,GAAG,KAAK,EAAE,cAAc;YAC/C,YAAY,EAAE,MAAM,CAAC,YAAY,GAAG,KAAK,EAAE,mCAAmC;YAC9E,KAAK,EAAE,UAAU;SAClB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,cAAc,CAAC,MAAwB;QACpD,IAAI,MAAM,CAAC,OAAO,IAAI,CAAC,EAAE,CAAC;YACxB,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACpD,CAAC;QACD,IAAI,MAAM,CAAC,YAAY,IAAI,CAAC,EAAE,CAAC;YAC7B,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAC1D,CAAC;QACD,IAAI,CAAC,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;YACxD,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;QAChE,CAAC;QACD,IAAI,CAAC,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;YACnD,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,YAAY;QACxB,OAAO,EAAE,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;IACzC,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,iBAAiB;QAC7B,OAAO,EAAE,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;IACrC,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,gBAAgB;QAC5B,OAAO;YACL,KAAK,EAAE,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC;YACrC,WAAW,EAAE,CAAC,GAAG,IAAI,CAAC,0BAA0B,CAAC;SAClD,CAAC;IACJ,CAAC;;AAvcH,8CAwcC;AAvcC,4CAA4C;AACpB,sCAAoB,GAAG;IAC7C,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;IACvD,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;CACvC,CAAC;AAEF,kDAAkD;AAC1B,4CAA0B,GAAG;IACnD,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;IAChD,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;CAC3C,CAAC;AAEF,oCAAoC;AACZ,qCAAmB,GAAuC;IAChF,gBAAgB,EAAE,EAAE,eAAe,EAAE,MAAM,EAAE,IAAI,EAAE,kBAAkB,EAAE,WAAW,EAAE,wBAAwB,EAAE;IAC9G,QAAQ,EAAE,EAAE,eAAe,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,WAAW,EAAE,sBAAsB,EAAE;IAC5F,eAAe,EAAE,EAAE,eAAe,EAAE,MAAM,EAAE,IAAI,EAAE,iBAAiB,EAAE,WAAW,EAAE,qBAAqB,EAAE;IACzG,GAAG,EAAE,EAAE,eAAe,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,WAAW,EAAE,kBAAkB,EAAE;IAC9E,UAAU,EAAE,EAAE,eAAe,EAAE,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,WAAW,EAAE,oBAAoB,EAAE;IAC9F,QAAQ,EAAE,EAAE,eAAe,EAAE,KAAK,EAAE,IAAI,EAAE,UAAU,EAAE,WAAW,EAAE,mBAAmB,EAAE;IACxF,KAAK,EAAE,EAAE,eAAe,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,eAAe,EAAE;CAC9E,CAAC;AAEF,+BAA+B;AACP,iCAAe,GAAG;IACxC,MAAM,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE;IAC9C,MAAM,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE;IAC9C,OAAO,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE;CAChD,CAAC;AA6aJ,kBAAe,iBAAiB,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\AirDuctCalculator.ts"],
      sourcesContent: ["/**\r\n * AirDuctCalculator - Pure Calculation Functions for Air Duct Sizing\r\n * \r\n * MISSION-CRITICAL: Pure TypeScript functions for SMACNA-compliant air duct calculations\r\n * Extracted from UI components for reusability and tier enforcement integration\r\n * \r\n * @see docs/implementation/tier-system/tier-boundaries-specification.md\r\n * @see docs/developer-guide/tier-implementation-checklist.md section 2.4\r\n */\r\n\r\n/**\r\n * Input parameters for duct sizing calculations\r\n */\r\nexport interface DuctSizingInputs {\r\n  airflow: number; // CFM\r\n  ductType: 'round' | 'rectangular';\r\n  frictionRate: number; // inches w.g. per 100 feet\r\n  units: 'imperial' | 'metric';\r\n  material?: string;\r\n  targetVelocity?: number; // FPM\r\n  maxVelocity?: number; // FPM\r\n  minVelocity?: number; // FPM\r\n}\r\n\r\n/**\r\n * Results from duct sizing calculations\r\n */\r\nexport interface DuctSizingResults {\r\n  // Common properties\r\n  area: number; // sq ft\r\n  velocity: number; // FPM\r\n  pressureLoss: number; // inches w.g. per 100 feet\r\n  reynoldsNumber: number;\r\n  frictionFactor: number;\r\n  \r\n  // Round duct specific\r\n  diameter?: number; // inches\r\n  \r\n  // Rectangular duct specific\r\n  width?: number; // inches\r\n  height?: number; // inches\r\n  equivalentDiameter?: number; // inches\r\n  hydraulicDiameter?: number; // inches\r\n  aspectRatio?: number;\r\n  \r\n  // Validation and recommendations\r\n  isOptimal: boolean;\r\n  warnings: string[];\r\n  recommendations: string[];\r\n  standardsCompliance: {\r\n    smacna: boolean;\r\n    ashrae: boolean;\r\n    velocityCompliant: boolean;\r\n  };\r\n}\r\n\r\n/**\r\n * Material properties for pressure loss calculations\r\n */\r\nexport interface MaterialProperties {\r\n  roughnessFactor: number; // feet\r\n  name: string;\r\n  description: string;\r\n}\r\n\r\n/**\r\n * AirDuctCalculator - Pure calculation functions for air duct sizing\r\n * CRITICAL: No dependencies on UI, storage, or external services\r\n */\r\nexport class AirDuctCalculator {\r\n  // SMACNA standard round duct sizes (inches)\r\n  private static readonly ROUND_STANDARD_SIZES = [\r\n    3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24,\r\n    26, 28, 30, 32, 36, 40, 42, 48, 54, 60\r\n  ];\r\n\r\n  // SMACNA standard rectangular duct sizes (inches)\r\n  private static readonly RECTANGULAR_STANDARD_SIZES = [\r\n    4, 5, 6, 7, 8, 9, 10, 12, 14, 16, 18, 20, 22, 24,\r\n    26, 28, 30, 32, 36, 40, 42, 48, 54, 60, 72\r\n  ];\r\n\r\n  // Material roughness factors (feet)\r\n  private static readonly MATERIAL_PROPERTIES: Record<string, MaterialProperties> = {\r\n    galvanized_steel: { roughnessFactor: 0.0003, name: 'Galvanized Steel', description: 'Standard HVAC ductwork' },\r\n    aluminum: { roughnessFactor: 0.0002, name: 'Aluminum', description: 'Lightweight ductwork' },\r\n    stainless_steel: { roughnessFactor: 0.0002, name: 'Stainless Steel', description: 'Corrosion resistant' },\r\n    pvc: { roughnessFactor: 0.0001, name: 'PVC', description: 'Plastic ductwork' },\r\n    fiberglass: { roughnessFactor: 0.0005, name: 'Fiberglass', description: 'Insulated ductwork' },\r\n    concrete: { roughnessFactor: 0.003, name: 'Concrete', description: 'Underground ducts' },\r\n    brick: { roughnessFactor: 0.01, name: 'Brick', description: 'Masonry ducts' }\r\n  };\r\n\r\n  // SMACNA velocity limits (FPM)\r\n  private static readonly VELOCITY_LIMITS = {\r\n    supply: { min: 400, max: 2500, optimal: 1500 },\r\n    return: { min: 300, max: 2000, optimal: 1200 },\r\n    exhaust: { min: 500, max: 3000, optimal: 1800 }\r\n  };\r\n\r\n  /**\r\n   * Calculate air duct sizing based on SMACNA standards\r\n   * CRITICAL: Pure function with no side effects\r\n   */\r\n  public static calculateDuctSizing(inputs: DuctSizingInputs): DuctSizingResults {\r\n    // Validate inputs\r\n    this.validateInputs(inputs);\r\n\r\n    // Convert to imperial units if needed\r\n    const imperialInputs = inputs.units === 'metric' \r\n      ? this.convertToImperial(inputs) \r\n      : inputs;\r\n\r\n    // Perform calculation based on duct type\r\n    if (imperialInputs.ductType === 'round') {\r\n      return this.calculateRoundDuct(imperialInputs);\r\n    } else {\r\n      return this.calculateRectangularDuct(imperialInputs);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Calculate round duct sizing\r\n   */\r\n  private static calculateRoundDuct(inputs: DuctSizingInputs): DuctSizingResults {\r\n    const { airflow, frictionRate, material = 'galvanized_steel' } = inputs;\r\n\r\n    // Find optimal diameter using SMACNA friction chart method\r\n    const diameter = this.findOptimalRoundDiameter(airflow, frictionRate);\r\n\r\n    // Calculate area and velocity\r\n    const area = Math.PI * Math.pow(diameter / 12, 2) / 4; // sq ft\r\n    const velocity = airflow / area; // FPM\r\n\r\n    // Calculate pressure loss\r\n    const pressureLoss = this.calculatePressureLoss(velocity, 100, diameter, material);\r\n\r\n    // Calculate Reynolds number and friction factor\r\n    const reynoldsNumber = this.calculateReynoldsNumber(velocity, diameter);\r\n    const frictionFactor = this.calculateFrictionFactor(reynoldsNumber, material, diameter);\r\n\r\n    // Validate and generate recommendations\r\n    const validation = this.validateResults({ velocity, diameter, area });\r\n    const recommendations = this.generateRecommendations(inputs, { diameter, velocity, area });\r\n\r\n    return {\r\n      diameter,\r\n      area,\r\n      velocity,\r\n      pressureLoss,\r\n      reynoldsNumber,\r\n      frictionFactor,\r\n      isOptimal: validation.isOptimal,\r\n      warnings: validation.warnings,\r\n      recommendations,\r\n      standardsCompliance: {\r\n        smacna: validation.smacnaCompliant,\r\n        ashrae: validation.ashraeCompliant,\r\n        velocityCompliant: validation.velocityCompliant\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate rectangular duct sizing\r\n   */\r\n  private static calculateRectangularDuct(inputs: DuctSizingInputs): DuctSizingResults {\r\n    const { airflow, frictionRate, material = 'galvanized_steel' } = inputs;\r\n\r\n    // Find optimal dimensions\r\n    const { width, height } = this.findOptimalRectangularDimensions(airflow, frictionRate);\r\n\r\n    // Calculate area and velocity\r\n    const area = (width * height) / 144; // sq ft\r\n    const velocity = airflow / area; // FPM\r\n\r\n    // Calculate equivalent and hydraulic diameters\r\n    const equivalentDiameter = this.calculateEquivalentDiameter(width, height);\r\n    const hydraulicDiameter = this.calculateHydraulicDiameter(width, height);\r\n    const aspectRatio = this.calculateAspectRatio(width, height);\r\n\r\n    // Calculate pressure loss using equivalent diameter\r\n    const pressureLoss = this.calculatePressureLoss(velocity, 100, equivalentDiameter, material);\r\n\r\n    // Calculate Reynolds number and friction factor\r\n    const reynoldsNumber = this.calculateReynoldsNumber(velocity, hydraulicDiameter);\r\n    const frictionFactor = this.calculateFrictionFactor(reynoldsNumber, material, hydraulicDiameter);\r\n\r\n    // Validate and generate recommendations\r\n    const validation = this.validateResults({ velocity, width, height, aspectRatio, area });\r\n    const recommendations = this.generateRecommendations(inputs, { width, height, velocity, area, aspectRatio });\r\n\r\n    return {\r\n      width,\r\n      height,\r\n      area,\r\n      velocity,\r\n      pressureLoss,\r\n      reynoldsNumber,\r\n      frictionFactor,\r\n      equivalentDiameter,\r\n      hydraulicDiameter,\r\n      aspectRatio,\r\n      isOptimal: validation.isOptimal,\r\n      warnings: validation.warnings,\r\n      recommendations,\r\n      standardsCompliance: {\r\n        smacna: validation.smacnaCompliant,\r\n        ashrae: validation.ashraeCompliant,\r\n        velocityCompliant: validation.velocityCompliant\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Find optimal round duct diameter using SMACNA friction chart method\r\n   */\r\n  private static findOptimalRoundDiameter(airflow: number, targetFriction: number): number {\r\n    let bestDiameter = this.ROUND_STANDARD_SIZES[0];\r\n    let bestScore = Infinity;\r\n\r\n    for (const diameter of this.ROUND_STANDARD_SIZES) {\r\n      const area = Math.PI * Math.pow(diameter / 12, 2) / 4; // sq ft\r\n      const velocity = airflow / area; // FPM\r\n\r\n      // Check velocity limits\r\n      if (velocity < this.VELOCITY_LIMITS.supply.min || velocity > this.VELOCITY_LIMITS.supply.max) {\r\n        continue;\r\n      }\r\n\r\n      // Calculate actual friction rate\r\n      const actualFriction = this.calculatePressureLoss(velocity, 100, diameter, 'galvanized_steel');\r\n      \r\n      // Score based on how close to target friction and optimal velocity\r\n      const frictionScore = Math.abs(actualFriction - targetFriction) / targetFriction;\r\n      const velocityScore = Math.abs(velocity - this.VELOCITY_LIMITS.supply.optimal) / this.VELOCITY_LIMITS.supply.optimal;\r\n      const totalScore = frictionScore + velocityScore * 0.5; // Weight friction more heavily\r\n\r\n      if (totalScore < bestScore) {\r\n        bestScore = totalScore;\r\n        bestDiameter = diameter;\r\n      }\r\n    }\r\n\r\n    return bestDiameter;\r\n  }\r\n\r\n  /**\r\n   * Find optimal rectangular duct dimensions\r\n   */\r\n  private static findOptimalRectangularDimensions(airflow: number, targetFriction: number): { width: number; height: number } {\r\n    let bestWidth = this.RECTANGULAR_STANDARD_SIZES[0];\r\n    let bestHeight = this.RECTANGULAR_STANDARD_SIZES[0];\r\n    let bestScore = Infinity;\r\n\r\n    // Try different aspect ratios (SMACNA recommends 1:1 to 4:1)\r\n    const aspectRatios = [1.0, 1.5, 2.0, 2.5, 3.0, 3.5, 4.0];\r\n\r\n    for (const aspectRatio of aspectRatios) {\r\n      // Calculate estimated dimensions\r\n      const estimatedArea = airflow / this.VELOCITY_LIMITS.supply.optimal; // Target optimal velocity\r\n      const height = Math.sqrt(estimatedArea / aspectRatio) * 12; // inches\r\n      const width = aspectRatio * height;\r\n\r\n      // Round to nearest standard sizes\r\n      const heightStd = this.findNearestStandardSize(height, this.RECTANGULAR_STANDARD_SIZES);\r\n      const widthStd = this.findNearestStandardSize(width, this.RECTANGULAR_STANDARD_SIZES);\r\n\r\n      // Calculate actual properties\r\n      const area = (widthStd * heightStd) / 144; // sq ft\r\n      const velocity = airflow / area; // FPM\r\n\r\n      // Check velocity limits\r\n      if (velocity < this.VELOCITY_LIMITS.supply.min || velocity > this.VELOCITY_LIMITS.supply.max) {\r\n        continue;\r\n      }\r\n\r\n      // Calculate equivalent diameter and friction\r\n      const equivalentDiameter = this.calculateEquivalentDiameter(widthStd, heightStd);\r\n      const actualFriction = this.calculatePressureLoss(velocity, 100, equivalentDiameter, 'galvanized_steel');\r\n\r\n      // Score based on friction accuracy, velocity optimality, and aspect ratio\r\n      const frictionScore = Math.abs(actualFriction - targetFriction) / targetFriction;\r\n      const velocityScore = Math.abs(velocity - this.VELOCITY_LIMITS.supply.optimal) / this.VELOCITY_LIMITS.supply.optimal;\r\n      const aspectScore = aspectRatio > 3.0 ? (aspectRatio - 3.0) * 0.2 : 0; // Penalty for high aspect ratios\r\n      const totalScore = frictionScore + velocityScore * 0.5 + aspectScore;\r\n\r\n      if (totalScore < bestScore) {\r\n        bestScore = totalScore;\r\n        bestWidth = widthStd;\r\n        bestHeight = heightStd;\r\n      }\r\n    }\r\n\r\n    return { width: bestWidth, height: bestHeight };\r\n  }\r\n\r\n  /**\r\n   * Calculate equivalent diameter for rectangular ducts (SMACNA formula)\r\n   */\r\n  public static calculateEquivalentDiameter(width: number, height: number): number {\r\n    return 1.3 * Math.pow(width * height, 0.625) / Math.pow(width + height, 0.25);\r\n  }\r\n\r\n  /**\r\n   * Calculate hydraulic diameter for rectangular ducts\r\n   */\r\n  public static calculateHydraulicDiameter(width: number, height: number): number {\r\n    return (4 * width * height) / (2 * (width + height));\r\n  }\r\n\r\n  /**\r\n   * Calculate aspect ratio for rectangular ducts\r\n   */\r\n  public static calculateAspectRatio(width: number, height: number): number {\r\n    return Math.max(width, height) / Math.min(width, height);\r\n  }\r\n\r\n  /**\r\n   * Calculate pressure loss using Darcy-Weisbach equation\r\n   */\r\n  public static calculatePressureLoss(velocity: number, length: number, diameter: number, material: string): number {\r\n    const materialProps = this.MATERIAL_PROPERTIES[material] || this.MATERIAL_PROPERTIES.galvanized_steel;\r\n    const roughness = materialProps.roughnessFactor;\r\n\r\n    // Convert units\r\n    const velocityFps = velocity / 60; // FPM to FPS\r\n    const diameterFt = diameter / 12; // inches to feet\r\n\r\n    // Air properties at standard conditions (70\xB0F, 14.7 psia)\r\n    const airDensity = 0.075; // lb/ft\xB3\r\n    const kinematicViscosity = 1.57e-4; // ft\xB2/s\r\n\r\n    // Calculate Reynolds number\r\n    const reynolds = (velocityFps * diameterFt) / kinematicViscosity;\r\n\r\n    // Calculate friction factor\r\n    const frictionFactor = this.calculateFrictionFactor(reynolds, material, diameter);\r\n\r\n    // Darcy-Weisbach equation: \u0394P = f * (L/D) * (\u03C1 * V\xB2) / (2 * gc)\r\n    // Convert to inches of water per 100 feet\r\n    const pressureLossPsf = frictionFactor * (length / diameterFt) * (airDensity * Math.pow(velocityFps, 2)) / (2 * 32.174);\r\n    const pressureLossInWg = pressureLossPsf / 5.2; // Convert psf to inches w.g.\r\n\r\n    return pressureLossInWg;\r\n  }\r\n\r\n  /**\r\n   * Calculate Reynolds number\r\n   */\r\n  public static calculateReynoldsNumber(velocity: number, diameter: number): number {\r\n    const velocityFps = velocity / 60; // FPM to FPS\r\n    const diameterFt = diameter / 12; // inches to feet\r\n    const kinematicViscosity = 1.57e-4; // ft\xB2/s for air at standard conditions\r\n\r\n    return (velocityFps * diameterFt) / kinematicViscosity;\r\n  }\r\n\r\n  /**\r\n   * Calculate friction factor using Colebrook-White equation\r\n   */\r\n  public static calculateFrictionFactor(reynolds: number, material: string, diameter: number): number {\r\n    const materialProps = this.MATERIAL_PROPERTIES[material] || this.MATERIAL_PROPERTIES.galvanized_steel;\r\n    const roughness = materialProps.roughnessFactor;\r\n    const diameterFt = diameter / 12;\r\n    const relativeRoughness = roughness / diameterFt;\r\n\r\n    // For laminar flow (Re < 2300)\r\n    if (reynolds < 2300) {\r\n      return 64 / reynolds;\r\n    }\r\n\r\n    // For turbulent flow, use Colebrook-White equation (iterative solution)\r\n    let f = 0.02; // Initial guess\r\n    for (let i = 0; i < 10; i++) {\r\n      const fNew = 1 / Math.pow(-2 * Math.log10(relativeRoughness / 3.7 + 2.51 / (reynolds * Math.sqrt(f))), 2);\r\n      if (Math.abs(fNew - f) < 0.0001) break;\r\n      f = fNew;\r\n    }\r\n\r\n    return f;\r\n  }\r\n\r\n  /**\r\n   * Validate calculation results\r\n   */\r\n  private static validateResults(results: any): {\r\n    isOptimal: boolean;\r\n    warnings: string[];\r\n    smacnaCompliant: boolean;\r\n    ashraeCompliant: boolean;\r\n    velocityCompliant: boolean;\r\n  } {\r\n    const warnings: string[] = [];\r\n    let smacnaCompliant = true;\r\n    let ashraeCompliant = true;\r\n    let velocityCompliant = true;\r\n\r\n    // Velocity validation\r\n    const { velocity } = results;\r\n    if (velocity < this.VELOCITY_LIMITS.supply.min) {\r\n      warnings.push(`Velocity ${velocity.toFixed(0)} FPM is below minimum recommended (${this.VELOCITY_LIMITS.supply.min} FPM)`);\r\n      velocityCompliant = false;\r\n    } else if (velocity > this.VELOCITY_LIMITS.supply.max) {\r\n      warnings.push(`Velocity ${velocity.toFixed(0)} FPM exceeds maximum recommended (${this.VELOCITY_LIMITS.supply.max} FPM)`);\r\n      velocityCompliant = false;\r\n      smacnaCompliant = false;\r\n    }\r\n\r\n    // Aspect ratio validation (for rectangular ducts)\r\n    if (results.aspectRatio) {\r\n      if (results.aspectRatio > 4.0) {\r\n        warnings.push(`Aspect ratio ${results.aspectRatio.toFixed(1)}:1 exceeds SMACNA maximum of 4:1`);\r\n        smacnaCompliant = false;\r\n      } else if (results.aspectRatio > 3.0) {\r\n        warnings.push(`Aspect ratio ${results.aspectRatio.toFixed(1)}:1 is high - consider optimization`);\r\n      }\r\n    }\r\n\r\n    // Area validation\r\n    if (results.area < 0.1) {\r\n      warnings.push('Very small duct area. Consider minimum duct size requirements.');\r\n    }\r\n\r\n    const isOptimal = warnings.length === 0 && \r\n      velocity >= this.VELOCITY_LIMITS.supply.optimal * 0.8 && \r\n      velocity <= this.VELOCITY_LIMITS.supply.optimal * 1.2;\r\n\r\n    return {\r\n      isOptimal,\r\n      warnings,\r\n      smacnaCompliant,\r\n      ashraeCompliant,\r\n      velocityCompliant\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Generate recommendations based on calculation results\r\n   */\r\n  private static generateRecommendations(inputs: DuctSizingInputs, results: any): string[] {\r\n    const recommendations: string[] = [];\r\n\r\n    // Velocity recommendations\r\n    if (results.velocity < this.VELOCITY_LIMITS.supply.optimal * 0.8) {\r\n      recommendations.push('Consider reducing duct size to increase velocity for better performance');\r\n    } else if (results.velocity > this.VELOCITY_LIMITS.supply.optimal * 1.2) {\r\n      recommendations.push('Consider increasing duct size to reduce velocity and noise');\r\n    }\r\n\r\n    // Aspect ratio recommendations (for rectangular ducts)\r\n    if (results.aspectRatio && results.aspectRatio > 3.0) {\r\n      recommendations.push('Consider using round duct or reducing aspect ratio for better performance');\r\n    }\r\n\r\n    // Material recommendations\r\n    if (inputs.material === 'galvanized_steel' && results.velocity > 2000) {\r\n      recommendations.push('Consider using smoother materials like aluminum for high-velocity applications');\r\n    }\r\n\r\n    return recommendations;\r\n  }\r\n\r\n  /**\r\n   * Find nearest standard size\r\n   */\r\n  private static findNearestStandardSize(target: number, standardSizes: number[]): number {\r\n    return standardSizes.reduce((prev, curr) => \r\n      Math.abs(curr - target) < Math.abs(prev - target) ? curr : prev\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Convert metric inputs to imperial\r\n   */\r\n  private static convertToImperial(inputs: DuctSizingInputs): DuctSizingInputs {\r\n    return {\r\n      ...inputs,\r\n      airflow: inputs.airflow * 2.119, // m\xB3/h to CFM\r\n      frictionRate: inputs.frictionRate * 0.249, // Pa/m to inches w.g. per 100 feet\r\n      units: 'imperial'\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Validate input parameters\r\n   */\r\n  private static validateInputs(inputs: DuctSizingInputs): void {\r\n    if (inputs.airflow <= 0) {\r\n      throw new Error('Airflow must be greater than 0');\r\n    }\r\n    if (inputs.frictionRate <= 0) {\r\n      throw new Error('Friction rate must be greater than 0');\r\n    }\r\n    if (!['round', 'rectangular'].includes(inputs.ductType)) {\r\n      throw new Error('Duct type must be \"round\" or \"rectangular\"');\r\n    }\r\n    if (!['imperial', 'metric'].includes(inputs.units)) {\r\n      throw new Error('Units must be \"imperial\" or \"metric\"');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get available materials\r\n   */\r\n  public static getMaterials(): Record<string, MaterialProperties> {\r\n    return { ...this.MATERIAL_PROPERTIES };\r\n  }\r\n\r\n  /**\r\n   * Get velocity limits\r\n   */\r\n  public static getVelocityLimits(): typeof AirDuctCalculator.VELOCITY_LIMITS {\r\n    return { ...this.VELOCITY_LIMITS };\r\n  }\r\n\r\n  /**\r\n   * Get standard sizes\r\n   */\r\n  public static getStandardSizes(): { round: number[]; rectangular: number[] } {\r\n    return {\r\n      round: [...this.ROUND_STANDARD_SIZES],\r\n      rectangular: [...this.RECTANGULAR_STANDARD_SIZES]\r\n    };\r\n  }\r\n}\r\n\r\nexport default AirDuctCalculator;\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "6cf0a2b9468aa41112e11f78724b3655d6711d58"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2g903bnq0k = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2g903bnq0k();
cov_2g903bnq0k().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_2g903bnq0k().s[1]++;
exports.AirDuctCalculator = void 0;
/**
 * AirDuctCalculator - Pure calculation functions for air duct sizing
 * CRITICAL: No dependencies on UI, storage, or external services
 */
class AirDuctCalculator {
  /**
   * Calculate air duct sizing based on SMACNA standards
   * CRITICAL: Pure function with no side effects
   */
  static calculateDuctSizing(inputs) {
    /* istanbul ignore next */
    cov_2g903bnq0k().f[0]++;
    cov_2g903bnq0k().s[2]++;
    // Validate inputs
    this.validateInputs(inputs);
    // Convert to imperial units if needed
    const imperialInputs =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[3]++, inputs.units === 'metric' ?
    /* istanbul ignore next */
    (cov_2g903bnq0k().b[0][0]++, this.convertToImperial(inputs)) :
    /* istanbul ignore next */
    (cov_2g903bnq0k().b[0][1]++, inputs));
    // Perform calculation based on duct type
    /* istanbul ignore next */
    cov_2g903bnq0k().s[4]++;
    if (imperialInputs.ductType === 'round') {
      /* istanbul ignore next */
      cov_2g903bnq0k().b[1][0]++;
      cov_2g903bnq0k().s[5]++;
      return this.calculateRoundDuct(imperialInputs);
    } else {
      /* istanbul ignore next */
      cov_2g903bnq0k().b[1][1]++;
      cov_2g903bnq0k().s[6]++;
      return this.calculateRectangularDuct(imperialInputs);
    }
  }
  /**
   * Calculate round duct sizing
   */
  static calculateRoundDuct(inputs) {
    /* istanbul ignore next */
    cov_2g903bnq0k().f[1]++;
    const {
      airflow,
      frictionRate,
      material =
      /* istanbul ignore next */
      (cov_2g903bnq0k().b[2][0]++, 'galvanized_steel')
    } =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[7]++, inputs);
    // Find optimal diameter using SMACNA friction chart method
    const diameter =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[8]++, this.findOptimalRoundDiameter(airflow, frictionRate));
    // Calculate area and velocity
    const area =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[9]++, Math.PI * Math.pow(diameter / 12, 2) / 4); // sq ft
    const velocity =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[10]++, airflow / area); // FPM
    // Calculate pressure loss
    const pressureLoss =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[11]++, this.calculatePressureLoss(velocity, 100, diameter, material));
    // Calculate Reynolds number and friction factor
    const reynoldsNumber =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[12]++, this.calculateReynoldsNumber(velocity, diameter));
    const frictionFactor =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[13]++, this.calculateFrictionFactor(reynoldsNumber, material, diameter));
    // Validate and generate recommendations
    const validation =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[14]++, this.validateResults({
      velocity,
      diameter,
      area
    }));
    const recommendations =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[15]++, this.generateRecommendations(inputs, {
      diameter,
      velocity,
      area
    }));
    /* istanbul ignore next */
    cov_2g903bnq0k().s[16]++;
    return {
      diameter,
      area,
      velocity,
      pressureLoss,
      reynoldsNumber,
      frictionFactor,
      isOptimal: validation.isOptimal,
      warnings: validation.warnings,
      recommendations,
      standardsCompliance: {
        smacna: validation.smacnaCompliant,
        ashrae: validation.ashraeCompliant,
        velocityCompliant: validation.velocityCompliant
      }
    };
  }
  /**
   * Calculate rectangular duct sizing
   */
  static calculateRectangularDuct(inputs) {
    /* istanbul ignore next */
    cov_2g903bnq0k().f[2]++;
    const {
      airflow,
      frictionRate,
      material =
      /* istanbul ignore next */
      (cov_2g903bnq0k().b[3][0]++, 'galvanized_steel')
    } =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[17]++, inputs);
    // Find optimal dimensions
    const {
      width,
      height
    } =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[18]++, this.findOptimalRectangularDimensions(airflow, frictionRate));
    // Calculate area and velocity
    const area =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[19]++, width * height / 144); // sq ft
    const velocity =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[20]++, airflow / area); // FPM
    // Calculate equivalent and hydraulic diameters
    const equivalentDiameter =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[21]++, this.calculateEquivalentDiameter(width, height));
    const hydraulicDiameter =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[22]++, this.calculateHydraulicDiameter(width, height));
    const aspectRatio =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[23]++, this.calculateAspectRatio(width, height));
    // Calculate pressure loss using equivalent diameter
    const pressureLoss =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[24]++, this.calculatePressureLoss(velocity, 100, equivalentDiameter, material));
    // Calculate Reynolds number and friction factor
    const reynoldsNumber =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[25]++, this.calculateReynoldsNumber(velocity, hydraulicDiameter));
    const frictionFactor =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[26]++, this.calculateFrictionFactor(reynoldsNumber, material, hydraulicDiameter));
    // Validate and generate recommendations
    const validation =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[27]++, this.validateResults({
      velocity,
      width,
      height,
      aspectRatio,
      area
    }));
    const recommendations =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[28]++, this.generateRecommendations(inputs, {
      width,
      height,
      velocity,
      area,
      aspectRatio
    }));
    /* istanbul ignore next */
    cov_2g903bnq0k().s[29]++;
    return {
      width,
      height,
      area,
      velocity,
      pressureLoss,
      reynoldsNumber,
      frictionFactor,
      equivalentDiameter,
      hydraulicDiameter,
      aspectRatio,
      isOptimal: validation.isOptimal,
      warnings: validation.warnings,
      recommendations,
      standardsCompliance: {
        smacna: validation.smacnaCompliant,
        ashrae: validation.ashraeCompliant,
        velocityCompliant: validation.velocityCompliant
      }
    };
  }
  /**
   * Find optimal round duct diameter using SMACNA friction chart method
   */
  static findOptimalRoundDiameter(airflow, targetFriction) {
    /* istanbul ignore next */
    cov_2g903bnq0k().f[3]++;
    let bestDiameter =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[30]++, this.ROUND_STANDARD_SIZES[0]);
    let bestScore =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[31]++, Infinity);
    /* istanbul ignore next */
    cov_2g903bnq0k().s[32]++;
    for (const diameter of this.ROUND_STANDARD_SIZES) {
      const area =
      /* istanbul ignore next */
      (cov_2g903bnq0k().s[33]++, Math.PI * Math.pow(diameter / 12, 2) / 4); // sq ft
      const velocity =
      /* istanbul ignore next */
      (cov_2g903bnq0k().s[34]++, airflow / area); // FPM
      // Check velocity limits
      /* istanbul ignore next */
      cov_2g903bnq0k().s[35]++;
      if (
      /* istanbul ignore next */
      (cov_2g903bnq0k().b[5][0]++, velocity < this.VELOCITY_LIMITS.supply.min) ||
      /* istanbul ignore next */
      (cov_2g903bnq0k().b[5][1]++, velocity > this.VELOCITY_LIMITS.supply.max)) {
        /* istanbul ignore next */
        cov_2g903bnq0k().b[4][0]++;
        cov_2g903bnq0k().s[36]++;
        continue;
      } else
      /* istanbul ignore next */
      {
        cov_2g903bnq0k().b[4][1]++;
      }
      // Calculate actual friction rate
      const actualFriction =
      /* istanbul ignore next */
      (cov_2g903bnq0k().s[37]++, this.calculatePressureLoss(velocity, 100, diameter, 'galvanized_steel'));
      // Score based on how close to target friction and optimal velocity
      const frictionScore =
      /* istanbul ignore next */
      (cov_2g903bnq0k().s[38]++, Math.abs(actualFriction - targetFriction) / targetFriction);
      const velocityScore =
      /* istanbul ignore next */
      (cov_2g903bnq0k().s[39]++, Math.abs(velocity - this.VELOCITY_LIMITS.supply.optimal) / this.VELOCITY_LIMITS.supply.optimal);
      const totalScore =
      /* istanbul ignore next */
      (cov_2g903bnq0k().s[40]++, frictionScore + velocityScore * 0.5); // Weight friction more heavily
      /* istanbul ignore next */
      cov_2g903bnq0k().s[41]++;
      if (totalScore < bestScore) {
        /* istanbul ignore next */
        cov_2g903bnq0k().b[6][0]++;
        cov_2g903bnq0k().s[42]++;
        bestScore = totalScore;
        /* istanbul ignore next */
        cov_2g903bnq0k().s[43]++;
        bestDiameter = diameter;
      } else
      /* istanbul ignore next */
      {
        cov_2g903bnq0k().b[6][1]++;
      }
    }
    /* istanbul ignore next */
    cov_2g903bnq0k().s[44]++;
    return bestDiameter;
  }
  /**
   * Find optimal rectangular duct dimensions
   */
  static findOptimalRectangularDimensions(airflow, targetFriction) {
    /* istanbul ignore next */
    cov_2g903bnq0k().f[4]++;
    let bestWidth =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[45]++, this.RECTANGULAR_STANDARD_SIZES[0]);
    let bestHeight =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[46]++, this.RECTANGULAR_STANDARD_SIZES[0]);
    let bestScore =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[47]++, Infinity);
    // Try different aspect ratios (SMACNA recommends 1:1 to 4:1)
    const aspectRatios =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[48]++, [1.0, 1.5, 2.0, 2.5, 3.0, 3.5, 4.0]);
    /* istanbul ignore next */
    cov_2g903bnq0k().s[49]++;
    for (const aspectRatio of aspectRatios) {
      // Calculate estimated dimensions
      const estimatedArea =
      /* istanbul ignore next */
      (cov_2g903bnq0k().s[50]++, airflow / this.VELOCITY_LIMITS.supply.optimal); // Target optimal velocity
      const height =
      /* istanbul ignore next */
      (cov_2g903bnq0k().s[51]++, Math.sqrt(estimatedArea / aspectRatio) * 12); // inches
      const width =
      /* istanbul ignore next */
      (cov_2g903bnq0k().s[52]++, aspectRatio * height);
      // Round to nearest standard sizes
      const heightStd =
      /* istanbul ignore next */
      (cov_2g903bnq0k().s[53]++, this.findNearestStandardSize(height, this.RECTANGULAR_STANDARD_SIZES));
      const widthStd =
      /* istanbul ignore next */
      (cov_2g903bnq0k().s[54]++, this.findNearestStandardSize(width, this.RECTANGULAR_STANDARD_SIZES));
      // Calculate actual properties
      const area =
      /* istanbul ignore next */
      (cov_2g903bnq0k().s[55]++, widthStd * heightStd / 144); // sq ft
      const velocity =
      /* istanbul ignore next */
      (cov_2g903bnq0k().s[56]++, airflow / area); // FPM
      // Check velocity limits
      /* istanbul ignore next */
      cov_2g903bnq0k().s[57]++;
      if (
      /* istanbul ignore next */
      (cov_2g903bnq0k().b[8][0]++, velocity < this.VELOCITY_LIMITS.supply.min) ||
      /* istanbul ignore next */
      (cov_2g903bnq0k().b[8][1]++, velocity > this.VELOCITY_LIMITS.supply.max)) {
        /* istanbul ignore next */
        cov_2g903bnq0k().b[7][0]++;
        cov_2g903bnq0k().s[58]++;
        continue;
      } else
      /* istanbul ignore next */
      {
        cov_2g903bnq0k().b[7][1]++;
      }
      // Calculate equivalent diameter and friction
      const equivalentDiameter =
      /* istanbul ignore next */
      (cov_2g903bnq0k().s[59]++, this.calculateEquivalentDiameter(widthStd, heightStd));
      const actualFriction =
      /* istanbul ignore next */
      (cov_2g903bnq0k().s[60]++, this.calculatePressureLoss(velocity, 100, equivalentDiameter, 'galvanized_steel'));
      // Score based on friction accuracy, velocity optimality, and aspect ratio
      const frictionScore =
      /* istanbul ignore next */
      (cov_2g903bnq0k().s[61]++, Math.abs(actualFriction - targetFriction) / targetFriction);
      const velocityScore =
      /* istanbul ignore next */
      (cov_2g903bnq0k().s[62]++, Math.abs(velocity - this.VELOCITY_LIMITS.supply.optimal) / this.VELOCITY_LIMITS.supply.optimal);
      const aspectScore =
      /* istanbul ignore next */
      (cov_2g903bnq0k().s[63]++, aspectRatio > 3.0 ?
      /* istanbul ignore next */
      (cov_2g903bnq0k().b[9][0]++, (aspectRatio - 3.0) * 0.2) :
      /* istanbul ignore next */
      (cov_2g903bnq0k().b[9][1]++, 0)); // Penalty for high aspect ratios
      const totalScore =
      /* istanbul ignore next */
      (cov_2g903bnq0k().s[64]++, frictionScore + velocityScore * 0.5 + aspectScore);
      /* istanbul ignore next */
      cov_2g903bnq0k().s[65]++;
      if (totalScore < bestScore) {
        /* istanbul ignore next */
        cov_2g903bnq0k().b[10][0]++;
        cov_2g903bnq0k().s[66]++;
        bestScore = totalScore;
        /* istanbul ignore next */
        cov_2g903bnq0k().s[67]++;
        bestWidth = widthStd;
        /* istanbul ignore next */
        cov_2g903bnq0k().s[68]++;
        bestHeight = heightStd;
      } else
      /* istanbul ignore next */
      {
        cov_2g903bnq0k().b[10][1]++;
      }
    }
    /* istanbul ignore next */
    cov_2g903bnq0k().s[69]++;
    return {
      width: bestWidth,
      height: bestHeight
    };
  }
  /**
   * Calculate equivalent diameter for rectangular ducts (SMACNA formula)
   */
  static calculateEquivalentDiameter(width, height) {
    /* istanbul ignore next */
    cov_2g903bnq0k().f[5]++;
    cov_2g903bnq0k().s[70]++;
    return 1.3 * Math.pow(width * height, 0.625) / Math.pow(width + height, 0.25);
  }
  /**
   * Calculate hydraulic diameter for rectangular ducts
   */
  static calculateHydraulicDiameter(width, height) {
    /* istanbul ignore next */
    cov_2g903bnq0k().f[6]++;
    cov_2g903bnq0k().s[71]++;
    return 4 * width * height / (2 * (width + height));
  }
  /**
   * Calculate aspect ratio for rectangular ducts
   */
  static calculateAspectRatio(width, height) {
    /* istanbul ignore next */
    cov_2g903bnq0k().f[7]++;
    cov_2g903bnq0k().s[72]++;
    return Math.max(width, height) / Math.min(width, height);
  }
  /**
   * Calculate pressure loss using Darcy-Weisbach equation
   */
  static calculatePressureLoss(velocity, length, diameter, material) {
    /* istanbul ignore next */
    cov_2g903bnq0k().f[8]++;
    const materialProps =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[73]++,
    /* istanbul ignore next */
    (cov_2g903bnq0k().b[11][0]++, this.MATERIAL_PROPERTIES[material]) ||
    /* istanbul ignore next */
    (cov_2g903bnq0k().b[11][1]++, this.MATERIAL_PROPERTIES.galvanized_steel));
    const roughness =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[74]++, materialProps.roughnessFactor);
    // Convert units
    const velocityFps =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[75]++, velocity / 60); // FPM to FPS
    const diameterFt =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[76]++, diameter / 12); // inches to feet
    // Air properties at standard conditions (70°F, 14.7 psia)
    const airDensity =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[77]++, 0.075); // lb/ft³
    const kinematicViscosity =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[78]++, 1.57e-4); // ft²/s
    // Calculate Reynolds number
    const reynolds =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[79]++, velocityFps * diameterFt / kinematicViscosity);
    // Calculate friction factor
    const frictionFactor =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[80]++, this.calculateFrictionFactor(reynolds, material, diameter));
    // Darcy-Weisbach equation: ΔP = f * (L/D) * (ρ * V²) / (2 * gc)
    // Convert to inches of water per 100 feet
    const pressureLossPsf =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[81]++, frictionFactor * (length / diameterFt) * (airDensity * Math.pow(velocityFps, 2)) / (2 * 32.174));
    const pressureLossInWg =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[82]++, pressureLossPsf / 5.2); // Convert psf to inches w.g.
    /* istanbul ignore next */
    cov_2g903bnq0k().s[83]++;
    return pressureLossInWg;
  }
  /**
   * Calculate Reynolds number
   */
  static calculateReynoldsNumber(velocity, diameter) {
    /* istanbul ignore next */
    cov_2g903bnq0k().f[9]++;
    const velocityFps =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[84]++, velocity / 60); // FPM to FPS
    const diameterFt =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[85]++, diameter / 12); // inches to feet
    const kinematicViscosity =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[86]++, 1.57e-4); // ft²/s for air at standard conditions
    /* istanbul ignore next */
    cov_2g903bnq0k().s[87]++;
    return velocityFps * diameterFt / kinematicViscosity;
  }
  /**
   * Calculate friction factor using Colebrook-White equation
   */
  static calculateFrictionFactor(reynolds, material, diameter) {
    /* istanbul ignore next */
    cov_2g903bnq0k().f[10]++;
    const materialProps =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[88]++,
    /* istanbul ignore next */
    (cov_2g903bnq0k().b[12][0]++, this.MATERIAL_PROPERTIES[material]) ||
    /* istanbul ignore next */
    (cov_2g903bnq0k().b[12][1]++, this.MATERIAL_PROPERTIES.galvanized_steel));
    const roughness =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[89]++, materialProps.roughnessFactor);
    const diameterFt =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[90]++, diameter / 12);
    const relativeRoughness =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[91]++, roughness / diameterFt);
    // For laminar flow (Re < 2300)
    /* istanbul ignore next */
    cov_2g903bnq0k().s[92]++;
    if (reynolds < 2300) {
      /* istanbul ignore next */
      cov_2g903bnq0k().b[13][0]++;
      cov_2g903bnq0k().s[93]++;
      return 64 / reynolds;
    } else
    /* istanbul ignore next */
    {
      cov_2g903bnq0k().b[13][1]++;
    }
    // For turbulent flow, use Colebrook-White equation (iterative solution)
    let f =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[94]++, 0.02); // Initial guess
    /* istanbul ignore next */
    cov_2g903bnq0k().s[95]++;
    for (let i =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[96]++, 0); i < 10; i++) {
      const fNew =
      /* istanbul ignore next */
      (cov_2g903bnq0k().s[97]++, 1 / Math.pow(-2 * Math.log10(relativeRoughness / 3.7 + 2.51 / (reynolds * Math.sqrt(f))), 2));
      /* istanbul ignore next */
      cov_2g903bnq0k().s[98]++;
      if (Math.abs(fNew - f) < 0.0001) {
        /* istanbul ignore next */
        cov_2g903bnq0k().b[14][0]++;
        cov_2g903bnq0k().s[99]++;
        break;
      } else
      /* istanbul ignore next */
      {
        cov_2g903bnq0k().b[14][1]++;
      }
      cov_2g903bnq0k().s[100]++;
      f = fNew;
    }
    /* istanbul ignore next */
    cov_2g903bnq0k().s[101]++;
    return f;
  }
  /**
   * Validate calculation results
   */
  static validateResults(results) {
    /* istanbul ignore next */
    cov_2g903bnq0k().f[11]++;
    const warnings =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[102]++, []);
    let smacnaCompliant =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[103]++, true);
    let ashraeCompliant =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[104]++, true);
    let velocityCompliant =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[105]++, true);
    // Velocity validation
    const {
      velocity
    } =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[106]++, results);
    /* istanbul ignore next */
    cov_2g903bnq0k().s[107]++;
    if (velocity < this.VELOCITY_LIMITS.supply.min) {
      /* istanbul ignore next */
      cov_2g903bnq0k().b[15][0]++;
      cov_2g903bnq0k().s[108]++;
      warnings.push(`Velocity ${velocity.toFixed(0)} FPM is below minimum recommended (${this.VELOCITY_LIMITS.supply.min} FPM)`);
      /* istanbul ignore next */
      cov_2g903bnq0k().s[109]++;
      velocityCompliant = false;
    } else {
      /* istanbul ignore next */
      cov_2g903bnq0k().b[15][1]++;
      cov_2g903bnq0k().s[110]++;
      if (velocity > this.VELOCITY_LIMITS.supply.max) {
        /* istanbul ignore next */
        cov_2g903bnq0k().b[16][0]++;
        cov_2g903bnq0k().s[111]++;
        warnings.push(`Velocity ${velocity.toFixed(0)} FPM exceeds maximum recommended (${this.VELOCITY_LIMITS.supply.max} FPM)`);
        /* istanbul ignore next */
        cov_2g903bnq0k().s[112]++;
        velocityCompliant = false;
        /* istanbul ignore next */
        cov_2g903bnq0k().s[113]++;
        smacnaCompliant = false;
      } else
      /* istanbul ignore next */
      {
        cov_2g903bnq0k().b[16][1]++;
      }
    }
    // Aspect ratio validation (for rectangular ducts)
    /* istanbul ignore next */
    cov_2g903bnq0k().s[114]++;
    if (results.aspectRatio) {
      /* istanbul ignore next */
      cov_2g903bnq0k().b[17][0]++;
      cov_2g903bnq0k().s[115]++;
      if (results.aspectRatio > 4.0) {
        /* istanbul ignore next */
        cov_2g903bnq0k().b[18][0]++;
        cov_2g903bnq0k().s[116]++;
        warnings.push(`Aspect ratio ${results.aspectRatio.toFixed(1)}:1 exceeds SMACNA maximum of 4:1`);
        /* istanbul ignore next */
        cov_2g903bnq0k().s[117]++;
        smacnaCompliant = false;
      } else {
        /* istanbul ignore next */
        cov_2g903bnq0k().b[18][1]++;
        cov_2g903bnq0k().s[118]++;
        if (results.aspectRatio > 3.0) {
          /* istanbul ignore next */
          cov_2g903bnq0k().b[19][0]++;
          cov_2g903bnq0k().s[119]++;
          warnings.push(`Aspect ratio ${results.aspectRatio.toFixed(1)}:1 is high - consider optimization`);
        } else
        /* istanbul ignore next */
        {
          cov_2g903bnq0k().b[19][1]++;
        }
      }
    } else
    /* istanbul ignore next */
    {
      cov_2g903bnq0k().b[17][1]++;
    }
    // Area validation
    cov_2g903bnq0k().s[120]++;
    if (results.area < 0.1) {
      /* istanbul ignore next */
      cov_2g903bnq0k().b[20][0]++;
      cov_2g903bnq0k().s[121]++;
      warnings.push('Very small duct area. Consider minimum duct size requirements.');
    } else
    /* istanbul ignore next */
    {
      cov_2g903bnq0k().b[20][1]++;
    }
    const isOptimal =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[122]++,
    /* istanbul ignore next */
    (cov_2g903bnq0k().b[21][0]++, warnings.length === 0) &&
    /* istanbul ignore next */
    (cov_2g903bnq0k().b[21][1]++, velocity >= this.VELOCITY_LIMITS.supply.optimal * 0.8) &&
    /* istanbul ignore next */
    (cov_2g903bnq0k().b[21][2]++, velocity <= this.VELOCITY_LIMITS.supply.optimal * 1.2));
    /* istanbul ignore next */
    cov_2g903bnq0k().s[123]++;
    return {
      isOptimal,
      warnings,
      smacnaCompliant,
      ashraeCompliant,
      velocityCompliant
    };
  }
  /**
   * Generate recommendations based on calculation results
   */
  static generateRecommendations(inputs, results) {
    /* istanbul ignore next */
    cov_2g903bnq0k().f[12]++;
    const recommendations =
    /* istanbul ignore next */
    (cov_2g903bnq0k().s[124]++, []);
    // Velocity recommendations
    /* istanbul ignore next */
    cov_2g903bnq0k().s[125]++;
    if (results.velocity < this.VELOCITY_LIMITS.supply.optimal * 0.8) {
      /* istanbul ignore next */
      cov_2g903bnq0k().b[22][0]++;
      cov_2g903bnq0k().s[126]++;
      recommendations.push('Consider reducing duct size to increase velocity for better performance');
    } else {
      /* istanbul ignore next */
      cov_2g903bnq0k().b[22][1]++;
      cov_2g903bnq0k().s[127]++;
      if (results.velocity > this.VELOCITY_LIMITS.supply.optimal * 1.2) {
        /* istanbul ignore next */
        cov_2g903bnq0k().b[23][0]++;
        cov_2g903bnq0k().s[128]++;
        recommendations.push('Consider increasing duct size to reduce velocity and noise');
      } else
      /* istanbul ignore next */
      {
        cov_2g903bnq0k().b[23][1]++;
      }
    }
    // Aspect ratio recommendations (for rectangular ducts)
    /* istanbul ignore next */
    cov_2g903bnq0k().s[129]++;
    if (
    /* istanbul ignore next */
    (cov_2g903bnq0k().b[25][0]++, results.aspectRatio) &&
    /* istanbul ignore next */
    (cov_2g903bnq0k().b[25][1]++, results.aspectRatio > 3.0)) {
      /* istanbul ignore next */
      cov_2g903bnq0k().b[24][0]++;
      cov_2g903bnq0k().s[130]++;
      recommendations.push('Consider using round duct or reducing aspect ratio for better performance');
    } else
    /* istanbul ignore next */
    {
      cov_2g903bnq0k().b[24][1]++;
    }
    // Material recommendations
    cov_2g903bnq0k().s[131]++;
    if (
    /* istanbul ignore next */
    (cov_2g903bnq0k().b[27][0]++, inputs.material === 'galvanized_steel') &&
    /* istanbul ignore next */
    (cov_2g903bnq0k().b[27][1]++, results.velocity > 2000)) {
      /* istanbul ignore next */
      cov_2g903bnq0k().b[26][0]++;
      cov_2g903bnq0k().s[132]++;
      recommendations.push('Consider using smoother materials like aluminum for high-velocity applications');
    } else
    /* istanbul ignore next */
    {
      cov_2g903bnq0k().b[26][1]++;
    }
    cov_2g903bnq0k().s[133]++;
    return recommendations;
  }
  /**
   * Find nearest standard size
   */
  static findNearestStandardSize(target, standardSizes) {
    /* istanbul ignore next */
    cov_2g903bnq0k().f[13]++;
    cov_2g903bnq0k().s[134]++;
    return standardSizes.reduce((prev, curr) => {
      /* istanbul ignore next */
      cov_2g903bnq0k().f[14]++;
      cov_2g903bnq0k().s[135]++;
      return Math.abs(curr - target) < Math.abs(prev - target) ?
      /* istanbul ignore next */
      (cov_2g903bnq0k().b[28][0]++, curr) :
      /* istanbul ignore next */
      (cov_2g903bnq0k().b[28][1]++, prev);
    });
  }
  /**
   * Convert metric inputs to imperial
   */
  static convertToImperial(inputs) {
    /* istanbul ignore next */
    cov_2g903bnq0k().f[15]++;
    cov_2g903bnq0k().s[136]++;
    return {
      ...inputs,
      airflow: inputs.airflow * 2.119,
      // m³/h to CFM
      frictionRate: inputs.frictionRate * 0.249,
      // Pa/m to inches w.g. per 100 feet
      units: 'imperial'
    };
  }
  /**
   * Validate input parameters
   */
  static validateInputs(inputs) {
    /* istanbul ignore next */
    cov_2g903bnq0k().f[16]++;
    cov_2g903bnq0k().s[137]++;
    if (inputs.airflow <= 0) {
      /* istanbul ignore next */
      cov_2g903bnq0k().b[29][0]++;
      cov_2g903bnq0k().s[138]++;
      throw new Error('Airflow must be greater than 0');
    } else
    /* istanbul ignore next */
    {
      cov_2g903bnq0k().b[29][1]++;
    }
    cov_2g903bnq0k().s[139]++;
    if (inputs.frictionRate <= 0) {
      /* istanbul ignore next */
      cov_2g903bnq0k().b[30][0]++;
      cov_2g903bnq0k().s[140]++;
      throw new Error('Friction rate must be greater than 0');
    } else
    /* istanbul ignore next */
    {
      cov_2g903bnq0k().b[30][1]++;
    }
    cov_2g903bnq0k().s[141]++;
    if (!['round', 'rectangular'].includes(inputs.ductType)) {
      /* istanbul ignore next */
      cov_2g903bnq0k().b[31][0]++;
      cov_2g903bnq0k().s[142]++;
      throw new Error('Duct type must be "round" or "rectangular"');
    } else
    /* istanbul ignore next */
    {
      cov_2g903bnq0k().b[31][1]++;
    }
    cov_2g903bnq0k().s[143]++;
    if (!['imperial', 'metric'].includes(inputs.units)) {
      /* istanbul ignore next */
      cov_2g903bnq0k().b[32][0]++;
      cov_2g903bnq0k().s[144]++;
      throw new Error('Units must be "imperial" or "metric"');
    } else
    /* istanbul ignore next */
    {
      cov_2g903bnq0k().b[32][1]++;
    }
  }
  /**
   * Get available materials
   */
  static getMaterials() {
    /* istanbul ignore next */
    cov_2g903bnq0k().f[17]++;
    cov_2g903bnq0k().s[145]++;
    return {
      ...this.MATERIAL_PROPERTIES
    };
  }
  /**
   * Get velocity limits
   */
  static getVelocityLimits() {
    /* istanbul ignore next */
    cov_2g903bnq0k().f[18]++;
    cov_2g903bnq0k().s[146]++;
    return {
      ...this.VELOCITY_LIMITS
    };
  }
  /**
   * Get standard sizes
   */
  static getStandardSizes() {
    /* istanbul ignore next */
    cov_2g903bnq0k().f[19]++;
    cov_2g903bnq0k().s[147]++;
    return {
      round: [...this.ROUND_STANDARD_SIZES],
      rectangular: [...this.RECTANGULAR_STANDARD_SIZES]
    };
  }
}
/* istanbul ignore next */
cov_2g903bnq0k().s[148]++;
exports.AirDuctCalculator = AirDuctCalculator;
// SMACNA standard round duct sizes (inches)
/* istanbul ignore next */
cov_2g903bnq0k().s[149]++;
AirDuctCalculator.ROUND_STANDARD_SIZES = [3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 36, 40, 42, 48, 54, 60];
// SMACNA standard rectangular duct sizes (inches)
/* istanbul ignore next */
cov_2g903bnq0k().s[150]++;
AirDuctCalculator.RECTANGULAR_STANDARD_SIZES = [4, 5, 6, 7, 8, 9, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 36, 40, 42, 48, 54, 60, 72];
// Material roughness factors (feet)
/* istanbul ignore next */
cov_2g903bnq0k().s[151]++;
AirDuctCalculator.MATERIAL_PROPERTIES = {
  galvanized_steel: {
    roughnessFactor: 0.0003,
    name: 'Galvanized Steel',
    description: 'Standard HVAC ductwork'
  },
  aluminum: {
    roughnessFactor: 0.0002,
    name: 'Aluminum',
    description: 'Lightweight ductwork'
  },
  stainless_steel: {
    roughnessFactor: 0.0002,
    name: 'Stainless Steel',
    description: 'Corrosion resistant'
  },
  pvc: {
    roughnessFactor: 0.0001,
    name: 'PVC',
    description: 'Plastic ductwork'
  },
  fiberglass: {
    roughnessFactor: 0.0005,
    name: 'Fiberglass',
    description: 'Insulated ductwork'
  },
  concrete: {
    roughnessFactor: 0.003,
    name: 'Concrete',
    description: 'Underground ducts'
  },
  brick: {
    roughnessFactor: 0.01,
    name: 'Brick',
    description: 'Masonry ducts'
  }
};
// SMACNA velocity limits (FPM)
/* istanbul ignore next */
cov_2g903bnq0k().s[152]++;
AirDuctCalculator.VELOCITY_LIMITS = {
  supply: {
    min: 400,
    max: 2500,
    optimal: 1500
  },
  return: {
    min: 300,
    max: 2000,
    optimal: 1200
  },
  exhaust: {
    min: 500,
    max: 3000,
    optimal: 1800
  }
};
/* istanbul ignore next */
cov_2g903bnq0k().s[153]++;
exports.default = AirDuctCalculator;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJjb3ZfMmc5MDNibnEwayIsInBhdGgiLCJoYXNoIiwiZ2xvYmFsIiwiRnVuY3Rpb24iLCJnY3YiLCJjb3ZlcmFnZURhdGEiLCJzdGF0ZW1lbnRNYXAiLCJzdGFydCIsImxpbmUiLCJjb2x1bW4iLCJlbmQiLCJmbk1hcCIsIm5hbWUiLCJkZWNsIiwibG9jIiwiYnJhbmNoTWFwIiwidHlwZSIsImxvY2F0aW9ucyIsInVuZGVmaW5lZCIsInMiLCJmIiwiYiIsImlucHV0U291cmNlTWFwIiwiZmlsZSIsIm1hcHBpbmdzIiwibmFtZXMiLCJzb3VyY2VzIiwic291cmNlc0NvbnRlbnQiLCJ2ZXJzaW9uIiwiX2NvdmVyYWdlU2NoZW1hIiwiY292ZXJhZ2UiLCJhY3R1YWxDb3ZlcmFnZSIsIkFpckR1Y3RDYWxjdWxhdG9yIiwiY2FsY3VsYXRlRHVjdFNpemluZyIsImlucHV0cyIsInZhbGlkYXRlSW5wdXRzIiwiaW1wZXJpYWxJbnB1dHMiLCJ1bml0cyIsImNvbnZlcnRUb0ltcGVyaWFsIiwiZHVjdFR5cGUiLCJjYWxjdWxhdGVSb3VuZER1Y3QiLCJjYWxjdWxhdGVSZWN0YW5ndWxhckR1Y3QiLCJhaXJmbG93IiwiZnJpY3Rpb25SYXRlIiwibWF0ZXJpYWwiLCJkaWFtZXRlciIsImZpbmRPcHRpbWFsUm91bmREaWFtZXRlciIsImFyZWEiLCJNYXRoIiwiUEkiLCJwb3ciLCJ2ZWxvY2l0eSIsInByZXNzdXJlTG9zcyIsImNhbGN1bGF0ZVByZXNzdXJlTG9zcyIsInJleW5vbGRzTnVtYmVyIiwiY2FsY3VsYXRlUmV5bm9sZHNOdW1iZXIiLCJmcmljdGlvbkZhY3RvciIsImNhbGN1bGF0ZUZyaWN0aW9uRmFjdG9yIiwidmFsaWRhdGlvbiIsInZhbGlkYXRlUmVzdWx0cyIsInJlY29tbWVuZGF0aW9ucyIsImdlbmVyYXRlUmVjb21tZW5kYXRpb25zIiwiaXNPcHRpbWFsIiwid2FybmluZ3MiLCJzdGFuZGFyZHNDb21wbGlhbmNlIiwic21hY25hIiwic21hY25hQ29tcGxpYW50IiwiYXNocmFlIiwiYXNocmFlQ29tcGxpYW50IiwidmVsb2NpdHlDb21wbGlhbnQiLCJ3aWR0aCIsImhlaWdodCIsImZpbmRPcHRpbWFsUmVjdGFuZ3VsYXJEaW1lbnNpb25zIiwiZXF1aXZhbGVudERpYW1ldGVyIiwiY2FsY3VsYXRlRXF1aXZhbGVudERpYW1ldGVyIiwiaHlkcmF1bGljRGlhbWV0ZXIiLCJjYWxjdWxhdGVIeWRyYXVsaWNEaWFtZXRlciIsImFzcGVjdFJhdGlvIiwiY2FsY3VsYXRlQXNwZWN0UmF0aW8iLCJ0YXJnZXRGcmljdGlvbiIsImJlc3REaWFtZXRlciIsIlJPVU5EX1NUQU5EQVJEX1NJWkVTIiwiYmVzdFNjb3JlIiwiSW5maW5pdHkiLCJWRUxPQ0lUWV9MSU1JVFMiLCJzdXBwbHkiLCJtaW4iLCJtYXgiLCJhY3R1YWxGcmljdGlvbiIsImZyaWN0aW9uU2NvcmUiLCJhYnMiLCJ2ZWxvY2l0eVNjb3JlIiwib3B0aW1hbCIsInRvdGFsU2NvcmUiLCJiZXN0V2lkdGgiLCJSRUNUQU5HVUxBUl9TVEFOREFSRF9TSVpFUyIsImJlc3RIZWlnaHQiLCJhc3BlY3RSYXRpb3MiLCJlc3RpbWF0ZWRBcmVhIiwic3FydCIsImhlaWdodFN0ZCIsImZpbmROZWFyZXN0U3RhbmRhcmRTaXplIiwid2lkdGhTdGQiLCJhc3BlY3RTY29yZSIsImxlbmd0aCIsIm1hdGVyaWFsUHJvcHMiLCJNQVRFUklBTF9QUk9QRVJUSUVTIiwiZ2FsdmFuaXplZF9zdGVlbCIsInJvdWdobmVzcyIsInJvdWdobmVzc0ZhY3RvciIsInZlbG9jaXR5RnBzIiwiZGlhbWV0ZXJGdCIsImFpckRlbnNpdHkiLCJraW5lbWF0aWNWaXNjb3NpdHkiLCJyZXlub2xkcyIsInByZXNzdXJlTG9zc1BzZiIsInByZXNzdXJlTG9zc0luV2ciLCJyZWxhdGl2ZVJvdWdobmVzcyIsImkiLCJmTmV3IiwibG9nMTAiLCJyZXN1bHRzIiwicHVzaCIsInRvRml4ZWQiLCJ0YXJnZXQiLCJzdGFuZGFyZFNpemVzIiwicmVkdWNlIiwicHJldiIsImN1cnIiLCJFcnJvciIsImluY2x1ZGVzIiwiZ2V0TWF0ZXJpYWxzIiwiZ2V0VmVsb2NpdHlMaW1pdHMiLCJnZXRTdGFuZGFyZFNpemVzIiwicm91bmQiLCJyZWN0YW5ndWxhciIsImV4cG9ydHMiLCJkZXNjcmlwdGlvbiIsImFsdW1pbnVtIiwic3RhaW5sZXNzX3N0ZWVsIiwicHZjIiwiZmliZXJnbGFzcyIsImNvbmNyZXRlIiwiYnJpY2siLCJyZXR1cm4iLCJleGhhdXN0IiwiZGVmYXVsdCJdLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcam9obnJcXERvd25sb2Fkc1xcU2l6ZVdpc2VfU3VpdGVfQXBwXFxiYWNrZW5kXFxzZXJ2aWNlc1xcY2FsY3VsYXRpb25zXFxBaXJEdWN0Q2FsY3VsYXRvci50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcclxuICogQWlyRHVjdENhbGN1bGF0b3IgLSBQdXJlIENhbGN1bGF0aW9uIEZ1bmN0aW9ucyBmb3IgQWlyIER1Y3QgU2l6aW5nXHJcbiAqIFxyXG4gKiBNSVNTSU9OLUNSSVRJQ0FMOiBQdXJlIFR5cGVTY3JpcHQgZnVuY3Rpb25zIGZvciBTTUFDTkEtY29tcGxpYW50IGFpciBkdWN0IGNhbGN1bGF0aW9uc1xyXG4gKiBFeHRyYWN0ZWQgZnJvbSBVSSBjb21wb25lbnRzIGZvciByZXVzYWJpbGl0eSBhbmQgdGllciBlbmZvcmNlbWVudCBpbnRlZ3JhdGlvblxyXG4gKiBcclxuICogQHNlZSBkb2NzL2ltcGxlbWVudGF0aW9uL3RpZXItc3lzdGVtL3RpZXItYm91bmRhcmllcy1zcGVjaWZpY2F0aW9uLm1kXHJcbiAqIEBzZWUgZG9jcy9kZXZlbG9wZXItZ3VpZGUvdGllci1pbXBsZW1lbnRhdGlvbi1jaGVja2xpc3QubWQgc2VjdGlvbiAyLjRcclxuICovXHJcblxyXG4vKipcclxuICogSW5wdXQgcGFyYW1ldGVycyBmb3IgZHVjdCBzaXppbmcgY2FsY3VsYXRpb25zXHJcbiAqL1xyXG5leHBvcnQgaW50ZXJmYWNlIER1Y3RTaXppbmdJbnB1dHMge1xyXG4gIGFpcmZsb3c6IG51bWJlcjsgLy8gQ0ZNXHJcbiAgZHVjdFR5cGU6ICdyb3VuZCcgfCAncmVjdGFuZ3VsYXInO1xyXG4gIGZyaWN0aW9uUmF0ZTogbnVtYmVyOyAvLyBpbmNoZXMgdy5nLiBwZXIgMTAwIGZlZXRcclxuICB1bml0czogJ2ltcGVyaWFsJyB8ICdtZXRyaWMnO1xyXG4gIG1hdGVyaWFsPzogc3RyaW5nO1xyXG4gIHRhcmdldFZlbG9jaXR5PzogbnVtYmVyOyAvLyBGUE1cclxuICBtYXhWZWxvY2l0eT86IG51bWJlcjsgLy8gRlBNXHJcbiAgbWluVmVsb2NpdHk/OiBudW1iZXI7IC8vIEZQTVxyXG59XHJcblxyXG4vKipcclxuICogUmVzdWx0cyBmcm9tIGR1Y3Qgc2l6aW5nIGNhbGN1bGF0aW9uc1xyXG4gKi9cclxuZXhwb3J0IGludGVyZmFjZSBEdWN0U2l6aW5nUmVzdWx0cyB7XHJcbiAgLy8gQ29tbW9uIHByb3BlcnRpZXNcclxuICBhcmVhOiBudW1iZXI7IC8vIHNxIGZ0XHJcbiAgdmVsb2NpdHk6IG51bWJlcjsgLy8gRlBNXHJcbiAgcHJlc3N1cmVMb3NzOiBudW1iZXI7IC8vIGluY2hlcyB3LmcuIHBlciAxMDAgZmVldFxyXG4gIHJleW5vbGRzTnVtYmVyOiBudW1iZXI7XHJcbiAgZnJpY3Rpb25GYWN0b3I6IG51bWJlcjtcclxuICBcclxuICAvLyBSb3VuZCBkdWN0IHNwZWNpZmljXHJcbiAgZGlhbWV0ZXI/OiBudW1iZXI7IC8vIGluY2hlc1xyXG4gIFxyXG4gIC8vIFJlY3Rhbmd1bGFyIGR1Y3Qgc3BlY2lmaWNcclxuICB3aWR0aD86IG51bWJlcjsgLy8gaW5jaGVzXHJcbiAgaGVpZ2h0PzogbnVtYmVyOyAvLyBpbmNoZXNcclxuICBlcXVpdmFsZW50RGlhbWV0ZXI/OiBudW1iZXI7IC8vIGluY2hlc1xyXG4gIGh5ZHJhdWxpY0RpYW1ldGVyPzogbnVtYmVyOyAvLyBpbmNoZXNcclxuICBhc3BlY3RSYXRpbz86IG51bWJlcjtcclxuICBcclxuICAvLyBWYWxpZGF0aW9uIGFuZCByZWNvbW1lbmRhdGlvbnNcclxuICBpc09wdGltYWw6IGJvb2xlYW47XHJcbiAgd2FybmluZ3M6IHN0cmluZ1tdO1xyXG4gIHJlY29tbWVuZGF0aW9uczogc3RyaW5nW107XHJcbiAgc3RhbmRhcmRzQ29tcGxpYW5jZToge1xyXG4gICAgc21hY25hOiBib29sZWFuO1xyXG4gICAgYXNocmFlOiBib29sZWFuO1xyXG4gICAgdmVsb2NpdHlDb21wbGlhbnQ6IGJvb2xlYW47XHJcbiAgfTtcclxufVxyXG5cclxuLyoqXHJcbiAqIE1hdGVyaWFsIHByb3BlcnRpZXMgZm9yIHByZXNzdXJlIGxvc3MgY2FsY3VsYXRpb25zXHJcbiAqL1xyXG5leHBvcnQgaW50ZXJmYWNlIE1hdGVyaWFsUHJvcGVydGllcyB7XHJcbiAgcm91Z2huZXNzRmFjdG9yOiBudW1iZXI7IC8vIGZlZXRcclxuICBuYW1lOiBzdHJpbmc7XHJcbiAgZGVzY3JpcHRpb246IHN0cmluZztcclxufVxyXG5cclxuLyoqXHJcbiAqIEFpckR1Y3RDYWxjdWxhdG9yIC0gUHVyZSBjYWxjdWxhdGlvbiBmdW5jdGlvbnMgZm9yIGFpciBkdWN0IHNpemluZ1xyXG4gKiBDUklUSUNBTDogTm8gZGVwZW5kZW5jaWVzIG9uIFVJLCBzdG9yYWdlLCBvciBleHRlcm5hbCBzZXJ2aWNlc1xyXG4gKi9cclxuZXhwb3J0IGNsYXNzIEFpckR1Y3RDYWxjdWxhdG9yIHtcclxuICAvLyBTTUFDTkEgc3RhbmRhcmQgcm91bmQgZHVjdCBzaXplcyAoaW5jaGVzKVxyXG4gIHByaXZhdGUgc3RhdGljIHJlYWRvbmx5IFJPVU5EX1NUQU5EQVJEX1NJWkVTID0gW1xyXG4gICAgMywgNCwgNSwgNiwgNywgOCwgOSwgMTAsIDExLCAxMiwgMTQsIDE2LCAxOCwgMjAsIDIyLCAyNCxcclxuICAgIDI2LCAyOCwgMzAsIDMyLCAzNiwgNDAsIDQyLCA0OCwgNTQsIDYwXHJcbiAgXTtcclxuXHJcbiAgLy8gU01BQ05BIHN0YW5kYXJkIHJlY3Rhbmd1bGFyIGR1Y3Qgc2l6ZXMgKGluY2hlcylcclxuICBwcml2YXRlIHN0YXRpYyByZWFkb25seSBSRUNUQU5HVUxBUl9TVEFOREFSRF9TSVpFUyA9IFtcclxuICAgIDQsIDUsIDYsIDcsIDgsIDksIDEwLCAxMiwgMTQsIDE2LCAxOCwgMjAsIDIyLCAyNCxcclxuICAgIDI2LCAyOCwgMzAsIDMyLCAzNiwgNDAsIDQyLCA0OCwgNTQsIDYwLCA3MlxyXG4gIF07XHJcblxyXG4gIC8vIE1hdGVyaWFsIHJvdWdobmVzcyBmYWN0b3JzIChmZWV0KVxyXG4gIHByaXZhdGUgc3RhdGljIHJlYWRvbmx5IE1BVEVSSUFMX1BST1BFUlRJRVM6IFJlY29yZDxzdHJpbmcsIE1hdGVyaWFsUHJvcGVydGllcz4gPSB7XHJcbiAgICBnYWx2YW5pemVkX3N0ZWVsOiB7IHJvdWdobmVzc0ZhY3RvcjogMC4wMDAzLCBuYW1lOiAnR2FsdmFuaXplZCBTdGVlbCcsIGRlc2NyaXB0aW9uOiAnU3RhbmRhcmQgSFZBQyBkdWN0d29yaycgfSxcclxuICAgIGFsdW1pbnVtOiB7IHJvdWdobmVzc0ZhY3RvcjogMC4wMDAyLCBuYW1lOiAnQWx1bWludW0nLCBkZXNjcmlwdGlvbjogJ0xpZ2h0d2VpZ2h0IGR1Y3R3b3JrJyB9LFxyXG4gICAgc3RhaW5sZXNzX3N0ZWVsOiB7IHJvdWdobmVzc0ZhY3RvcjogMC4wMDAyLCBuYW1lOiAnU3RhaW5sZXNzIFN0ZWVsJywgZGVzY3JpcHRpb246ICdDb3Jyb3Npb24gcmVzaXN0YW50JyB9LFxyXG4gICAgcHZjOiB7IHJvdWdobmVzc0ZhY3RvcjogMC4wMDAxLCBuYW1lOiAnUFZDJywgZGVzY3JpcHRpb246ICdQbGFzdGljIGR1Y3R3b3JrJyB9LFxyXG4gICAgZmliZXJnbGFzczogeyByb3VnaG5lc3NGYWN0b3I6IDAuMDAwNSwgbmFtZTogJ0ZpYmVyZ2xhc3MnLCBkZXNjcmlwdGlvbjogJ0luc3VsYXRlZCBkdWN0d29yaycgfSxcclxuICAgIGNvbmNyZXRlOiB7IHJvdWdobmVzc0ZhY3RvcjogMC4wMDMsIG5hbWU6ICdDb25jcmV0ZScsIGRlc2NyaXB0aW9uOiAnVW5kZXJncm91bmQgZHVjdHMnIH0sXHJcbiAgICBicmljazogeyByb3VnaG5lc3NGYWN0b3I6IDAuMDEsIG5hbWU6ICdCcmljaycsIGRlc2NyaXB0aW9uOiAnTWFzb25yeSBkdWN0cycgfVxyXG4gIH07XHJcblxyXG4gIC8vIFNNQUNOQSB2ZWxvY2l0eSBsaW1pdHMgKEZQTSlcclxuICBwcml2YXRlIHN0YXRpYyByZWFkb25seSBWRUxPQ0lUWV9MSU1JVFMgPSB7XHJcbiAgICBzdXBwbHk6IHsgbWluOiA0MDAsIG1heDogMjUwMCwgb3B0aW1hbDogMTUwMCB9LFxyXG4gICAgcmV0dXJuOiB7IG1pbjogMzAwLCBtYXg6IDIwMDAsIG9wdGltYWw6IDEyMDAgfSxcclxuICAgIGV4aGF1c3Q6IHsgbWluOiA1MDAsIG1heDogMzAwMCwgb3B0aW1hbDogMTgwMCB9XHJcbiAgfTtcclxuXHJcbiAgLyoqXHJcbiAgICogQ2FsY3VsYXRlIGFpciBkdWN0IHNpemluZyBiYXNlZCBvbiBTTUFDTkEgc3RhbmRhcmRzXHJcbiAgICogQ1JJVElDQUw6IFB1cmUgZnVuY3Rpb24gd2l0aCBubyBzaWRlIGVmZmVjdHNcclxuICAgKi9cclxuICBwdWJsaWMgc3RhdGljIGNhbGN1bGF0ZUR1Y3RTaXppbmcoaW5wdXRzOiBEdWN0U2l6aW5nSW5wdXRzKTogRHVjdFNpemluZ1Jlc3VsdHMge1xyXG4gICAgLy8gVmFsaWRhdGUgaW5wdXRzXHJcbiAgICB0aGlzLnZhbGlkYXRlSW5wdXRzKGlucHV0cyk7XHJcblxyXG4gICAgLy8gQ29udmVydCB0byBpbXBlcmlhbCB1bml0cyBpZiBuZWVkZWRcclxuICAgIGNvbnN0IGltcGVyaWFsSW5wdXRzID0gaW5wdXRzLnVuaXRzID09PSAnbWV0cmljJyBcclxuICAgICAgPyB0aGlzLmNvbnZlcnRUb0ltcGVyaWFsKGlucHV0cykgXHJcbiAgICAgIDogaW5wdXRzO1xyXG5cclxuICAgIC8vIFBlcmZvcm0gY2FsY3VsYXRpb24gYmFzZWQgb24gZHVjdCB0eXBlXHJcbiAgICBpZiAoaW1wZXJpYWxJbnB1dHMuZHVjdFR5cGUgPT09ICdyb3VuZCcpIHtcclxuICAgICAgcmV0dXJuIHRoaXMuY2FsY3VsYXRlUm91bmREdWN0KGltcGVyaWFsSW5wdXRzKTtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIHJldHVybiB0aGlzLmNhbGN1bGF0ZVJlY3Rhbmd1bGFyRHVjdChpbXBlcmlhbElucHV0cyk7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBDYWxjdWxhdGUgcm91bmQgZHVjdCBzaXppbmdcclxuICAgKi9cclxuICBwcml2YXRlIHN0YXRpYyBjYWxjdWxhdGVSb3VuZER1Y3QoaW5wdXRzOiBEdWN0U2l6aW5nSW5wdXRzKTogRHVjdFNpemluZ1Jlc3VsdHMge1xyXG4gICAgY29uc3QgeyBhaXJmbG93LCBmcmljdGlvblJhdGUsIG1hdGVyaWFsID0gJ2dhbHZhbml6ZWRfc3RlZWwnIH0gPSBpbnB1dHM7XHJcblxyXG4gICAgLy8gRmluZCBvcHRpbWFsIGRpYW1ldGVyIHVzaW5nIFNNQUNOQSBmcmljdGlvbiBjaGFydCBtZXRob2RcclxuICAgIGNvbnN0IGRpYW1ldGVyID0gdGhpcy5maW5kT3B0aW1hbFJvdW5kRGlhbWV0ZXIoYWlyZmxvdywgZnJpY3Rpb25SYXRlKTtcclxuXHJcbiAgICAvLyBDYWxjdWxhdGUgYXJlYSBhbmQgdmVsb2NpdHlcclxuICAgIGNvbnN0IGFyZWEgPSBNYXRoLlBJICogTWF0aC5wb3coZGlhbWV0ZXIgLyAxMiwgMikgLyA0OyAvLyBzcSBmdFxyXG4gICAgY29uc3QgdmVsb2NpdHkgPSBhaXJmbG93IC8gYXJlYTsgLy8gRlBNXHJcblxyXG4gICAgLy8gQ2FsY3VsYXRlIHByZXNzdXJlIGxvc3NcclxuICAgIGNvbnN0IHByZXNzdXJlTG9zcyA9IHRoaXMuY2FsY3VsYXRlUHJlc3N1cmVMb3NzKHZlbG9jaXR5LCAxMDAsIGRpYW1ldGVyLCBtYXRlcmlhbCk7XHJcblxyXG4gICAgLy8gQ2FsY3VsYXRlIFJleW5vbGRzIG51bWJlciBhbmQgZnJpY3Rpb24gZmFjdG9yXHJcbiAgICBjb25zdCByZXlub2xkc051bWJlciA9IHRoaXMuY2FsY3VsYXRlUmV5bm9sZHNOdW1iZXIodmVsb2NpdHksIGRpYW1ldGVyKTtcclxuICAgIGNvbnN0IGZyaWN0aW9uRmFjdG9yID0gdGhpcy5jYWxjdWxhdGVGcmljdGlvbkZhY3RvcihyZXlub2xkc051bWJlciwgbWF0ZXJpYWwsIGRpYW1ldGVyKTtcclxuXHJcbiAgICAvLyBWYWxpZGF0ZSBhbmQgZ2VuZXJhdGUgcmVjb21tZW5kYXRpb25zXHJcbiAgICBjb25zdCB2YWxpZGF0aW9uID0gdGhpcy52YWxpZGF0ZVJlc3VsdHMoeyB2ZWxvY2l0eSwgZGlhbWV0ZXIsIGFyZWEgfSk7XHJcbiAgICBjb25zdCByZWNvbW1lbmRhdGlvbnMgPSB0aGlzLmdlbmVyYXRlUmVjb21tZW5kYXRpb25zKGlucHV0cywgeyBkaWFtZXRlciwgdmVsb2NpdHksIGFyZWEgfSk7XHJcblxyXG4gICAgcmV0dXJuIHtcclxuICAgICAgZGlhbWV0ZXIsXHJcbiAgICAgIGFyZWEsXHJcbiAgICAgIHZlbG9jaXR5LFxyXG4gICAgICBwcmVzc3VyZUxvc3MsXHJcbiAgICAgIHJleW5vbGRzTnVtYmVyLFxyXG4gICAgICBmcmljdGlvbkZhY3RvcixcclxuICAgICAgaXNPcHRpbWFsOiB2YWxpZGF0aW9uLmlzT3B0aW1hbCxcclxuICAgICAgd2FybmluZ3M6IHZhbGlkYXRpb24ud2FybmluZ3MsXHJcbiAgICAgIHJlY29tbWVuZGF0aW9ucyxcclxuICAgICAgc3RhbmRhcmRzQ29tcGxpYW5jZToge1xyXG4gICAgICAgIHNtYWNuYTogdmFsaWRhdGlvbi5zbWFjbmFDb21wbGlhbnQsXHJcbiAgICAgICAgYXNocmFlOiB2YWxpZGF0aW9uLmFzaHJhZUNvbXBsaWFudCxcclxuICAgICAgICB2ZWxvY2l0eUNvbXBsaWFudDogdmFsaWRhdGlvbi52ZWxvY2l0eUNvbXBsaWFudFxyXG4gICAgICB9XHJcbiAgICB9O1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ2FsY3VsYXRlIHJlY3Rhbmd1bGFyIGR1Y3Qgc2l6aW5nXHJcbiAgICovXHJcbiAgcHJpdmF0ZSBzdGF0aWMgY2FsY3VsYXRlUmVjdGFuZ3VsYXJEdWN0KGlucHV0czogRHVjdFNpemluZ0lucHV0cyk6IER1Y3RTaXppbmdSZXN1bHRzIHtcclxuICAgIGNvbnN0IHsgYWlyZmxvdywgZnJpY3Rpb25SYXRlLCBtYXRlcmlhbCA9ICdnYWx2YW5pemVkX3N0ZWVsJyB9ID0gaW5wdXRzO1xyXG5cclxuICAgIC8vIEZpbmQgb3B0aW1hbCBkaW1lbnNpb25zXHJcbiAgICBjb25zdCB7IHdpZHRoLCBoZWlnaHQgfSA9IHRoaXMuZmluZE9wdGltYWxSZWN0YW5ndWxhckRpbWVuc2lvbnMoYWlyZmxvdywgZnJpY3Rpb25SYXRlKTtcclxuXHJcbiAgICAvLyBDYWxjdWxhdGUgYXJlYSBhbmQgdmVsb2NpdHlcclxuICAgIGNvbnN0IGFyZWEgPSAod2lkdGggKiBoZWlnaHQpIC8gMTQ0OyAvLyBzcSBmdFxyXG4gICAgY29uc3QgdmVsb2NpdHkgPSBhaXJmbG93IC8gYXJlYTsgLy8gRlBNXHJcblxyXG4gICAgLy8gQ2FsY3VsYXRlIGVxdWl2YWxlbnQgYW5kIGh5ZHJhdWxpYyBkaWFtZXRlcnNcclxuICAgIGNvbnN0IGVxdWl2YWxlbnREaWFtZXRlciA9IHRoaXMuY2FsY3VsYXRlRXF1aXZhbGVudERpYW1ldGVyKHdpZHRoLCBoZWlnaHQpO1xyXG4gICAgY29uc3QgaHlkcmF1bGljRGlhbWV0ZXIgPSB0aGlzLmNhbGN1bGF0ZUh5ZHJhdWxpY0RpYW1ldGVyKHdpZHRoLCBoZWlnaHQpO1xyXG4gICAgY29uc3QgYXNwZWN0UmF0aW8gPSB0aGlzLmNhbGN1bGF0ZUFzcGVjdFJhdGlvKHdpZHRoLCBoZWlnaHQpO1xyXG5cclxuICAgIC8vIENhbGN1bGF0ZSBwcmVzc3VyZSBsb3NzIHVzaW5nIGVxdWl2YWxlbnQgZGlhbWV0ZXJcclxuICAgIGNvbnN0IHByZXNzdXJlTG9zcyA9IHRoaXMuY2FsY3VsYXRlUHJlc3N1cmVMb3NzKHZlbG9jaXR5LCAxMDAsIGVxdWl2YWxlbnREaWFtZXRlciwgbWF0ZXJpYWwpO1xyXG5cclxuICAgIC8vIENhbGN1bGF0ZSBSZXlub2xkcyBudW1iZXIgYW5kIGZyaWN0aW9uIGZhY3RvclxyXG4gICAgY29uc3QgcmV5bm9sZHNOdW1iZXIgPSB0aGlzLmNhbGN1bGF0ZVJleW5vbGRzTnVtYmVyKHZlbG9jaXR5LCBoeWRyYXVsaWNEaWFtZXRlcik7XHJcbiAgICBjb25zdCBmcmljdGlvbkZhY3RvciA9IHRoaXMuY2FsY3VsYXRlRnJpY3Rpb25GYWN0b3IocmV5bm9sZHNOdW1iZXIsIG1hdGVyaWFsLCBoeWRyYXVsaWNEaWFtZXRlcik7XHJcblxyXG4gICAgLy8gVmFsaWRhdGUgYW5kIGdlbmVyYXRlIHJlY29tbWVuZGF0aW9uc1xyXG4gICAgY29uc3QgdmFsaWRhdGlvbiA9IHRoaXMudmFsaWRhdGVSZXN1bHRzKHsgdmVsb2NpdHksIHdpZHRoLCBoZWlnaHQsIGFzcGVjdFJhdGlvLCBhcmVhIH0pO1xyXG4gICAgY29uc3QgcmVjb21tZW5kYXRpb25zID0gdGhpcy5nZW5lcmF0ZVJlY29tbWVuZGF0aW9ucyhpbnB1dHMsIHsgd2lkdGgsIGhlaWdodCwgdmVsb2NpdHksIGFyZWEsIGFzcGVjdFJhdGlvIH0pO1xyXG5cclxuICAgIHJldHVybiB7XHJcbiAgICAgIHdpZHRoLFxyXG4gICAgICBoZWlnaHQsXHJcbiAgICAgIGFyZWEsXHJcbiAgICAgIHZlbG9jaXR5LFxyXG4gICAgICBwcmVzc3VyZUxvc3MsXHJcbiAgICAgIHJleW5vbGRzTnVtYmVyLFxyXG4gICAgICBmcmljdGlvbkZhY3RvcixcclxuICAgICAgZXF1aXZhbGVudERpYW1ldGVyLFxyXG4gICAgICBoeWRyYXVsaWNEaWFtZXRlcixcclxuICAgICAgYXNwZWN0UmF0aW8sXHJcbiAgICAgIGlzT3B0aW1hbDogdmFsaWRhdGlvbi5pc09wdGltYWwsXHJcbiAgICAgIHdhcm5pbmdzOiB2YWxpZGF0aW9uLndhcm5pbmdzLFxyXG4gICAgICByZWNvbW1lbmRhdGlvbnMsXHJcbiAgICAgIHN0YW5kYXJkc0NvbXBsaWFuY2U6IHtcclxuICAgICAgICBzbWFjbmE6IHZhbGlkYXRpb24uc21hY25hQ29tcGxpYW50LFxyXG4gICAgICAgIGFzaHJhZTogdmFsaWRhdGlvbi5hc2hyYWVDb21wbGlhbnQsXHJcbiAgICAgICAgdmVsb2NpdHlDb21wbGlhbnQ6IHZhbGlkYXRpb24udmVsb2NpdHlDb21wbGlhbnRcclxuICAgICAgfVxyXG4gICAgfTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEZpbmQgb3B0aW1hbCByb3VuZCBkdWN0IGRpYW1ldGVyIHVzaW5nIFNNQUNOQSBmcmljdGlvbiBjaGFydCBtZXRob2RcclxuICAgKi9cclxuICBwcml2YXRlIHN0YXRpYyBmaW5kT3B0aW1hbFJvdW5kRGlhbWV0ZXIoYWlyZmxvdzogbnVtYmVyLCB0YXJnZXRGcmljdGlvbjogbnVtYmVyKTogbnVtYmVyIHtcclxuICAgIGxldCBiZXN0RGlhbWV0ZXIgPSB0aGlzLlJPVU5EX1NUQU5EQVJEX1NJWkVTWzBdO1xyXG4gICAgbGV0IGJlc3RTY29yZSA9IEluZmluaXR5O1xyXG5cclxuICAgIGZvciAoY29uc3QgZGlhbWV0ZXIgb2YgdGhpcy5ST1VORF9TVEFOREFSRF9TSVpFUykge1xyXG4gICAgICBjb25zdCBhcmVhID0gTWF0aC5QSSAqIE1hdGgucG93KGRpYW1ldGVyIC8gMTIsIDIpIC8gNDsgLy8gc3EgZnRcclxuICAgICAgY29uc3QgdmVsb2NpdHkgPSBhaXJmbG93IC8gYXJlYTsgLy8gRlBNXHJcblxyXG4gICAgICAvLyBDaGVjayB2ZWxvY2l0eSBsaW1pdHNcclxuICAgICAgaWYgKHZlbG9jaXR5IDwgdGhpcy5WRUxPQ0lUWV9MSU1JVFMuc3VwcGx5Lm1pbiB8fCB2ZWxvY2l0eSA+IHRoaXMuVkVMT0NJVFlfTElNSVRTLnN1cHBseS5tYXgpIHtcclxuICAgICAgICBjb250aW51ZTtcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gQ2FsY3VsYXRlIGFjdHVhbCBmcmljdGlvbiByYXRlXHJcbiAgICAgIGNvbnN0IGFjdHVhbEZyaWN0aW9uID0gdGhpcy5jYWxjdWxhdGVQcmVzc3VyZUxvc3ModmVsb2NpdHksIDEwMCwgZGlhbWV0ZXIsICdnYWx2YW5pemVkX3N0ZWVsJyk7XHJcbiAgICAgIFxyXG4gICAgICAvLyBTY29yZSBiYXNlZCBvbiBob3cgY2xvc2UgdG8gdGFyZ2V0IGZyaWN0aW9uIGFuZCBvcHRpbWFsIHZlbG9jaXR5XHJcbiAgICAgIGNvbnN0IGZyaWN0aW9uU2NvcmUgPSBNYXRoLmFicyhhY3R1YWxGcmljdGlvbiAtIHRhcmdldEZyaWN0aW9uKSAvIHRhcmdldEZyaWN0aW9uO1xyXG4gICAgICBjb25zdCB2ZWxvY2l0eVNjb3JlID0gTWF0aC5hYnModmVsb2NpdHkgLSB0aGlzLlZFTE9DSVRZX0xJTUlUUy5zdXBwbHkub3B0aW1hbCkgLyB0aGlzLlZFTE9DSVRZX0xJTUlUUy5zdXBwbHkub3B0aW1hbDtcclxuICAgICAgY29uc3QgdG90YWxTY29yZSA9IGZyaWN0aW9uU2NvcmUgKyB2ZWxvY2l0eVNjb3JlICogMC41OyAvLyBXZWlnaHQgZnJpY3Rpb24gbW9yZSBoZWF2aWx5XHJcblxyXG4gICAgICBpZiAodG90YWxTY29yZSA8IGJlc3RTY29yZSkge1xyXG4gICAgICAgIGJlc3RTY29yZSA9IHRvdGFsU2NvcmU7XHJcbiAgICAgICAgYmVzdERpYW1ldGVyID0gZGlhbWV0ZXI7XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4gYmVzdERpYW1ldGVyO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogRmluZCBvcHRpbWFsIHJlY3Rhbmd1bGFyIGR1Y3QgZGltZW5zaW9uc1xyXG4gICAqL1xyXG4gIHByaXZhdGUgc3RhdGljIGZpbmRPcHRpbWFsUmVjdGFuZ3VsYXJEaW1lbnNpb25zKGFpcmZsb3c6IG51bWJlciwgdGFyZ2V0RnJpY3Rpb246IG51bWJlcik6IHsgd2lkdGg6IG51bWJlcjsgaGVpZ2h0OiBudW1iZXIgfSB7XHJcbiAgICBsZXQgYmVzdFdpZHRoID0gdGhpcy5SRUNUQU5HVUxBUl9TVEFOREFSRF9TSVpFU1swXTtcclxuICAgIGxldCBiZXN0SGVpZ2h0ID0gdGhpcy5SRUNUQU5HVUxBUl9TVEFOREFSRF9TSVpFU1swXTtcclxuICAgIGxldCBiZXN0U2NvcmUgPSBJbmZpbml0eTtcclxuXHJcbiAgICAvLyBUcnkgZGlmZmVyZW50IGFzcGVjdCByYXRpb3MgKFNNQUNOQSByZWNvbW1lbmRzIDE6MSB0byA0OjEpXHJcbiAgICBjb25zdCBhc3BlY3RSYXRpb3MgPSBbMS4wLCAxLjUsIDIuMCwgMi41LCAzLjAsIDMuNSwgNC4wXTtcclxuXHJcbiAgICBmb3IgKGNvbnN0IGFzcGVjdFJhdGlvIG9mIGFzcGVjdFJhdGlvcykge1xyXG4gICAgICAvLyBDYWxjdWxhdGUgZXN0aW1hdGVkIGRpbWVuc2lvbnNcclxuICAgICAgY29uc3QgZXN0aW1hdGVkQXJlYSA9IGFpcmZsb3cgLyB0aGlzLlZFTE9DSVRZX0xJTUlUUy5zdXBwbHkub3B0aW1hbDsgLy8gVGFyZ2V0IG9wdGltYWwgdmVsb2NpdHlcclxuICAgICAgY29uc3QgaGVpZ2h0ID0gTWF0aC5zcXJ0KGVzdGltYXRlZEFyZWEgLyBhc3BlY3RSYXRpbykgKiAxMjsgLy8gaW5jaGVzXHJcbiAgICAgIGNvbnN0IHdpZHRoID0gYXNwZWN0UmF0aW8gKiBoZWlnaHQ7XHJcblxyXG4gICAgICAvLyBSb3VuZCB0byBuZWFyZXN0IHN0YW5kYXJkIHNpemVzXHJcbiAgICAgIGNvbnN0IGhlaWdodFN0ZCA9IHRoaXMuZmluZE5lYXJlc3RTdGFuZGFyZFNpemUoaGVpZ2h0LCB0aGlzLlJFQ1RBTkdVTEFSX1NUQU5EQVJEX1NJWkVTKTtcclxuICAgICAgY29uc3Qgd2lkdGhTdGQgPSB0aGlzLmZpbmROZWFyZXN0U3RhbmRhcmRTaXplKHdpZHRoLCB0aGlzLlJFQ1RBTkdVTEFSX1NUQU5EQVJEX1NJWkVTKTtcclxuXHJcbiAgICAgIC8vIENhbGN1bGF0ZSBhY3R1YWwgcHJvcGVydGllc1xyXG4gICAgICBjb25zdCBhcmVhID0gKHdpZHRoU3RkICogaGVpZ2h0U3RkKSAvIDE0NDsgLy8gc3EgZnRcclxuICAgICAgY29uc3QgdmVsb2NpdHkgPSBhaXJmbG93IC8gYXJlYTsgLy8gRlBNXHJcblxyXG4gICAgICAvLyBDaGVjayB2ZWxvY2l0eSBsaW1pdHNcclxuICAgICAgaWYgKHZlbG9jaXR5IDwgdGhpcy5WRUxPQ0lUWV9MSU1JVFMuc3VwcGx5Lm1pbiB8fCB2ZWxvY2l0eSA+IHRoaXMuVkVMT0NJVFlfTElNSVRTLnN1cHBseS5tYXgpIHtcclxuICAgICAgICBjb250aW51ZTtcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gQ2FsY3VsYXRlIGVxdWl2YWxlbnQgZGlhbWV0ZXIgYW5kIGZyaWN0aW9uXHJcbiAgICAgIGNvbnN0IGVxdWl2YWxlbnREaWFtZXRlciA9IHRoaXMuY2FsY3VsYXRlRXF1aXZhbGVudERpYW1ldGVyKHdpZHRoU3RkLCBoZWlnaHRTdGQpO1xyXG4gICAgICBjb25zdCBhY3R1YWxGcmljdGlvbiA9IHRoaXMuY2FsY3VsYXRlUHJlc3N1cmVMb3NzKHZlbG9jaXR5LCAxMDAsIGVxdWl2YWxlbnREaWFtZXRlciwgJ2dhbHZhbml6ZWRfc3RlZWwnKTtcclxuXHJcbiAgICAgIC8vIFNjb3JlIGJhc2VkIG9uIGZyaWN0aW9uIGFjY3VyYWN5LCB2ZWxvY2l0eSBvcHRpbWFsaXR5LCBhbmQgYXNwZWN0IHJhdGlvXHJcbiAgICAgIGNvbnN0IGZyaWN0aW9uU2NvcmUgPSBNYXRoLmFicyhhY3R1YWxGcmljdGlvbiAtIHRhcmdldEZyaWN0aW9uKSAvIHRhcmdldEZyaWN0aW9uO1xyXG4gICAgICBjb25zdCB2ZWxvY2l0eVNjb3JlID0gTWF0aC5hYnModmVsb2NpdHkgLSB0aGlzLlZFTE9DSVRZX0xJTUlUUy5zdXBwbHkub3B0aW1hbCkgLyB0aGlzLlZFTE9DSVRZX0xJTUlUUy5zdXBwbHkub3B0aW1hbDtcclxuICAgICAgY29uc3QgYXNwZWN0U2NvcmUgPSBhc3BlY3RSYXRpbyA+IDMuMCA/IChhc3BlY3RSYXRpbyAtIDMuMCkgKiAwLjIgOiAwOyAvLyBQZW5hbHR5IGZvciBoaWdoIGFzcGVjdCByYXRpb3NcclxuICAgICAgY29uc3QgdG90YWxTY29yZSA9IGZyaWN0aW9uU2NvcmUgKyB2ZWxvY2l0eVNjb3JlICogMC41ICsgYXNwZWN0U2NvcmU7XHJcblxyXG4gICAgICBpZiAodG90YWxTY29yZSA8IGJlc3RTY29yZSkge1xyXG4gICAgICAgIGJlc3RTY29yZSA9IHRvdGFsU2NvcmU7XHJcbiAgICAgICAgYmVzdFdpZHRoID0gd2lkdGhTdGQ7XHJcbiAgICAgICAgYmVzdEhlaWdodCA9IGhlaWdodFN0ZDtcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIHJldHVybiB7IHdpZHRoOiBiZXN0V2lkdGgsIGhlaWdodDogYmVzdEhlaWdodCB9O1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ2FsY3VsYXRlIGVxdWl2YWxlbnQgZGlhbWV0ZXIgZm9yIHJlY3Rhbmd1bGFyIGR1Y3RzIChTTUFDTkEgZm9ybXVsYSlcclxuICAgKi9cclxuICBwdWJsaWMgc3RhdGljIGNhbGN1bGF0ZUVxdWl2YWxlbnREaWFtZXRlcih3aWR0aDogbnVtYmVyLCBoZWlnaHQ6IG51bWJlcik6IG51bWJlciB7XHJcbiAgICByZXR1cm4gMS4zICogTWF0aC5wb3cod2lkdGggKiBoZWlnaHQsIDAuNjI1KSAvIE1hdGgucG93KHdpZHRoICsgaGVpZ2h0LCAwLjI1KTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIENhbGN1bGF0ZSBoeWRyYXVsaWMgZGlhbWV0ZXIgZm9yIHJlY3Rhbmd1bGFyIGR1Y3RzXHJcbiAgICovXHJcbiAgcHVibGljIHN0YXRpYyBjYWxjdWxhdGVIeWRyYXVsaWNEaWFtZXRlcih3aWR0aDogbnVtYmVyLCBoZWlnaHQ6IG51bWJlcik6IG51bWJlciB7XHJcbiAgICByZXR1cm4gKDQgKiB3aWR0aCAqIGhlaWdodCkgLyAoMiAqICh3aWR0aCArIGhlaWdodCkpO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ2FsY3VsYXRlIGFzcGVjdCByYXRpbyBmb3IgcmVjdGFuZ3VsYXIgZHVjdHNcclxuICAgKi9cclxuICBwdWJsaWMgc3RhdGljIGNhbGN1bGF0ZUFzcGVjdFJhdGlvKHdpZHRoOiBudW1iZXIsIGhlaWdodDogbnVtYmVyKTogbnVtYmVyIHtcclxuICAgIHJldHVybiBNYXRoLm1heCh3aWR0aCwgaGVpZ2h0KSAvIE1hdGgubWluKHdpZHRoLCBoZWlnaHQpO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ2FsY3VsYXRlIHByZXNzdXJlIGxvc3MgdXNpbmcgRGFyY3ktV2Vpc2JhY2ggZXF1YXRpb25cclxuICAgKi9cclxuICBwdWJsaWMgc3RhdGljIGNhbGN1bGF0ZVByZXNzdXJlTG9zcyh2ZWxvY2l0eTogbnVtYmVyLCBsZW5ndGg6IG51bWJlciwgZGlhbWV0ZXI6IG51bWJlciwgbWF0ZXJpYWw6IHN0cmluZyk6IG51bWJlciB7XHJcbiAgICBjb25zdCBtYXRlcmlhbFByb3BzID0gdGhpcy5NQVRFUklBTF9QUk9QRVJUSUVTW21hdGVyaWFsXSB8fCB0aGlzLk1BVEVSSUFMX1BST1BFUlRJRVMuZ2FsdmFuaXplZF9zdGVlbDtcclxuICAgIGNvbnN0IHJvdWdobmVzcyA9IG1hdGVyaWFsUHJvcHMucm91Z2huZXNzRmFjdG9yO1xyXG5cclxuICAgIC8vIENvbnZlcnQgdW5pdHNcclxuICAgIGNvbnN0IHZlbG9jaXR5RnBzID0gdmVsb2NpdHkgLyA2MDsgLy8gRlBNIHRvIEZQU1xyXG4gICAgY29uc3QgZGlhbWV0ZXJGdCA9IGRpYW1ldGVyIC8gMTI7IC8vIGluY2hlcyB0byBmZWV0XHJcblxyXG4gICAgLy8gQWlyIHByb3BlcnRpZXMgYXQgc3RhbmRhcmQgY29uZGl0aW9ucyAoNzDCsEYsIDE0LjcgcHNpYSlcclxuICAgIGNvbnN0IGFpckRlbnNpdHkgPSAwLjA3NTsgLy8gbGIvZnTCs1xyXG4gICAgY29uc3Qga2luZW1hdGljVmlzY29zaXR5ID0gMS41N2UtNDsgLy8gZnTCsi9zXHJcblxyXG4gICAgLy8gQ2FsY3VsYXRlIFJleW5vbGRzIG51bWJlclxyXG4gICAgY29uc3QgcmV5bm9sZHMgPSAodmVsb2NpdHlGcHMgKiBkaWFtZXRlckZ0KSAvIGtpbmVtYXRpY1Zpc2Nvc2l0eTtcclxuXHJcbiAgICAvLyBDYWxjdWxhdGUgZnJpY3Rpb24gZmFjdG9yXHJcbiAgICBjb25zdCBmcmljdGlvbkZhY3RvciA9IHRoaXMuY2FsY3VsYXRlRnJpY3Rpb25GYWN0b3IocmV5bm9sZHMsIG1hdGVyaWFsLCBkaWFtZXRlcik7XHJcblxyXG4gICAgLy8gRGFyY3ktV2Vpc2JhY2ggZXF1YXRpb246IM6UUCA9IGYgKiAoTC9EKSAqICjPgSAqIFbCsikgLyAoMiAqIGdjKVxyXG4gICAgLy8gQ29udmVydCB0byBpbmNoZXMgb2Ygd2F0ZXIgcGVyIDEwMCBmZWV0XHJcbiAgICBjb25zdCBwcmVzc3VyZUxvc3NQc2YgPSBmcmljdGlvbkZhY3RvciAqIChsZW5ndGggLyBkaWFtZXRlckZ0KSAqIChhaXJEZW5zaXR5ICogTWF0aC5wb3codmVsb2NpdHlGcHMsIDIpKSAvICgyICogMzIuMTc0KTtcclxuICAgIGNvbnN0IHByZXNzdXJlTG9zc0luV2cgPSBwcmVzc3VyZUxvc3NQc2YgLyA1LjI7IC8vIENvbnZlcnQgcHNmIHRvIGluY2hlcyB3LmcuXHJcblxyXG4gICAgcmV0dXJuIHByZXNzdXJlTG9zc0luV2c7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBDYWxjdWxhdGUgUmV5bm9sZHMgbnVtYmVyXHJcbiAgICovXHJcbiAgcHVibGljIHN0YXRpYyBjYWxjdWxhdGVSZXlub2xkc051bWJlcih2ZWxvY2l0eTogbnVtYmVyLCBkaWFtZXRlcjogbnVtYmVyKTogbnVtYmVyIHtcclxuICAgIGNvbnN0IHZlbG9jaXR5RnBzID0gdmVsb2NpdHkgLyA2MDsgLy8gRlBNIHRvIEZQU1xyXG4gICAgY29uc3QgZGlhbWV0ZXJGdCA9IGRpYW1ldGVyIC8gMTI7IC8vIGluY2hlcyB0byBmZWV0XHJcbiAgICBjb25zdCBraW5lbWF0aWNWaXNjb3NpdHkgPSAxLjU3ZS00OyAvLyBmdMKyL3MgZm9yIGFpciBhdCBzdGFuZGFyZCBjb25kaXRpb25zXHJcblxyXG4gICAgcmV0dXJuICh2ZWxvY2l0eUZwcyAqIGRpYW1ldGVyRnQpIC8ga2luZW1hdGljVmlzY29zaXR5O1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ2FsY3VsYXRlIGZyaWN0aW9uIGZhY3RvciB1c2luZyBDb2xlYnJvb2stV2hpdGUgZXF1YXRpb25cclxuICAgKi9cclxuICBwdWJsaWMgc3RhdGljIGNhbGN1bGF0ZUZyaWN0aW9uRmFjdG9yKHJleW5vbGRzOiBudW1iZXIsIG1hdGVyaWFsOiBzdHJpbmcsIGRpYW1ldGVyOiBudW1iZXIpOiBudW1iZXIge1xyXG4gICAgY29uc3QgbWF0ZXJpYWxQcm9wcyA9IHRoaXMuTUFURVJJQUxfUFJPUEVSVElFU1ttYXRlcmlhbF0gfHwgdGhpcy5NQVRFUklBTF9QUk9QRVJUSUVTLmdhbHZhbml6ZWRfc3RlZWw7XHJcbiAgICBjb25zdCByb3VnaG5lc3MgPSBtYXRlcmlhbFByb3BzLnJvdWdobmVzc0ZhY3RvcjtcclxuICAgIGNvbnN0IGRpYW1ldGVyRnQgPSBkaWFtZXRlciAvIDEyO1xyXG4gICAgY29uc3QgcmVsYXRpdmVSb3VnaG5lc3MgPSByb3VnaG5lc3MgLyBkaWFtZXRlckZ0O1xyXG5cclxuICAgIC8vIEZvciBsYW1pbmFyIGZsb3cgKFJlIDwgMjMwMClcclxuICAgIGlmIChyZXlub2xkcyA8IDIzMDApIHtcclxuICAgICAgcmV0dXJuIDY0IC8gcmV5bm9sZHM7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gRm9yIHR1cmJ1bGVudCBmbG93LCB1c2UgQ29sZWJyb29rLVdoaXRlIGVxdWF0aW9uIChpdGVyYXRpdmUgc29sdXRpb24pXHJcbiAgICBsZXQgZiA9IDAuMDI7IC8vIEluaXRpYWwgZ3Vlc3NcclxuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgMTA7IGkrKykge1xyXG4gICAgICBjb25zdCBmTmV3ID0gMSAvIE1hdGgucG93KC0yICogTWF0aC5sb2cxMChyZWxhdGl2ZVJvdWdobmVzcyAvIDMuNyArIDIuNTEgLyAocmV5bm9sZHMgKiBNYXRoLnNxcnQoZikpKSwgMik7XHJcbiAgICAgIGlmIChNYXRoLmFicyhmTmV3IC0gZikgPCAwLjAwMDEpIGJyZWFrO1xyXG4gICAgICBmID0gZk5ldztcclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4gZjtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIFZhbGlkYXRlIGNhbGN1bGF0aW9uIHJlc3VsdHNcclxuICAgKi9cclxuICBwcml2YXRlIHN0YXRpYyB2YWxpZGF0ZVJlc3VsdHMocmVzdWx0czogYW55KToge1xyXG4gICAgaXNPcHRpbWFsOiBib29sZWFuO1xyXG4gICAgd2FybmluZ3M6IHN0cmluZ1tdO1xyXG4gICAgc21hY25hQ29tcGxpYW50OiBib29sZWFuO1xyXG4gICAgYXNocmFlQ29tcGxpYW50OiBib29sZWFuO1xyXG4gICAgdmVsb2NpdHlDb21wbGlhbnQ6IGJvb2xlYW47XHJcbiAgfSB7XHJcbiAgICBjb25zdCB3YXJuaW5nczogc3RyaW5nW10gPSBbXTtcclxuICAgIGxldCBzbWFjbmFDb21wbGlhbnQgPSB0cnVlO1xyXG4gICAgbGV0IGFzaHJhZUNvbXBsaWFudCA9IHRydWU7XHJcbiAgICBsZXQgdmVsb2NpdHlDb21wbGlhbnQgPSB0cnVlO1xyXG5cclxuICAgIC8vIFZlbG9jaXR5IHZhbGlkYXRpb25cclxuICAgIGNvbnN0IHsgdmVsb2NpdHkgfSA9IHJlc3VsdHM7XHJcbiAgICBpZiAodmVsb2NpdHkgPCB0aGlzLlZFTE9DSVRZX0xJTUlUUy5zdXBwbHkubWluKSB7XHJcbiAgICAgIHdhcm5pbmdzLnB1c2goYFZlbG9jaXR5ICR7dmVsb2NpdHkudG9GaXhlZCgwKX0gRlBNIGlzIGJlbG93IG1pbmltdW0gcmVjb21tZW5kZWQgKCR7dGhpcy5WRUxPQ0lUWV9MSU1JVFMuc3VwcGx5Lm1pbn0gRlBNKWApO1xyXG4gICAgICB2ZWxvY2l0eUNvbXBsaWFudCA9IGZhbHNlO1xyXG4gICAgfSBlbHNlIGlmICh2ZWxvY2l0eSA+IHRoaXMuVkVMT0NJVFlfTElNSVRTLnN1cHBseS5tYXgpIHtcclxuICAgICAgd2FybmluZ3MucHVzaChgVmVsb2NpdHkgJHt2ZWxvY2l0eS50b0ZpeGVkKDApfSBGUE0gZXhjZWVkcyBtYXhpbXVtIHJlY29tbWVuZGVkICgke3RoaXMuVkVMT0NJVFlfTElNSVRTLnN1cHBseS5tYXh9IEZQTSlgKTtcclxuICAgICAgdmVsb2NpdHlDb21wbGlhbnQgPSBmYWxzZTtcclxuICAgICAgc21hY25hQ29tcGxpYW50ID0gZmFsc2U7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gQXNwZWN0IHJhdGlvIHZhbGlkYXRpb24gKGZvciByZWN0YW5ndWxhciBkdWN0cylcclxuICAgIGlmIChyZXN1bHRzLmFzcGVjdFJhdGlvKSB7XHJcbiAgICAgIGlmIChyZXN1bHRzLmFzcGVjdFJhdGlvID4gNC4wKSB7XHJcbiAgICAgICAgd2FybmluZ3MucHVzaChgQXNwZWN0IHJhdGlvICR7cmVzdWx0cy5hc3BlY3RSYXRpby50b0ZpeGVkKDEpfToxIGV4Y2VlZHMgU01BQ05BIG1heGltdW0gb2YgNDoxYCk7XHJcbiAgICAgICAgc21hY25hQ29tcGxpYW50ID0gZmFsc2U7XHJcbiAgICAgIH0gZWxzZSBpZiAocmVzdWx0cy5hc3BlY3RSYXRpbyA+IDMuMCkge1xyXG4gICAgICAgIHdhcm5pbmdzLnB1c2goYEFzcGVjdCByYXRpbyAke3Jlc3VsdHMuYXNwZWN0UmF0aW8udG9GaXhlZCgxKX06MSBpcyBoaWdoIC0gY29uc2lkZXIgb3B0aW1pemF0aW9uYCk7XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAvLyBBcmVhIHZhbGlkYXRpb25cclxuICAgIGlmIChyZXN1bHRzLmFyZWEgPCAwLjEpIHtcclxuICAgICAgd2FybmluZ3MucHVzaCgnVmVyeSBzbWFsbCBkdWN0IGFyZWEuIENvbnNpZGVyIG1pbmltdW0gZHVjdCBzaXplIHJlcXVpcmVtZW50cy4nKTtcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBpc09wdGltYWwgPSB3YXJuaW5ncy5sZW5ndGggPT09IDAgJiYgXHJcbiAgICAgIHZlbG9jaXR5ID49IHRoaXMuVkVMT0NJVFlfTElNSVRTLnN1cHBseS5vcHRpbWFsICogMC44ICYmIFxyXG4gICAgICB2ZWxvY2l0eSA8PSB0aGlzLlZFTE9DSVRZX0xJTUlUUy5zdXBwbHkub3B0aW1hbCAqIDEuMjtcclxuXHJcbiAgICByZXR1cm4ge1xyXG4gICAgICBpc09wdGltYWwsXHJcbiAgICAgIHdhcm5pbmdzLFxyXG4gICAgICBzbWFjbmFDb21wbGlhbnQsXHJcbiAgICAgIGFzaHJhZUNvbXBsaWFudCxcclxuICAgICAgdmVsb2NpdHlDb21wbGlhbnRcclxuICAgIH07XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBHZW5lcmF0ZSByZWNvbW1lbmRhdGlvbnMgYmFzZWQgb24gY2FsY3VsYXRpb24gcmVzdWx0c1xyXG4gICAqL1xyXG4gIHByaXZhdGUgc3RhdGljIGdlbmVyYXRlUmVjb21tZW5kYXRpb25zKGlucHV0czogRHVjdFNpemluZ0lucHV0cywgcmVzdWx0czogYW55KTogc3RyaW5nW10ge1xyXG4gICAgY29uc3QgcmVjb21tZW5kYXRpb25zOiBzdHJpbmdbXSA9IFtdO1xyXG5cclxuICAgIC8vIFZlbG9jaXR5IHJlY29tbWVuZGF0aW9uc1xyXG4gICAgaWYgKHJlc3VsdHMudmVsb2NpdHkgPCB0aGlzLlZFTE9DSVRZX0xJTUlUUy5zdXBwbHkub3B0aW1hbCAqIDAuOCkge1xyXG4gICAgICByZWNvbW1lbmRhdGlvbnMucHVzaCgnQ29uc2lkZXIgcmVkdWNpbmcgZHVjdCBzaXplIHRvIGluY3JlYXNlIHZlbG9jaXR5IGZvciBiZXR0ZXIgcGVyZm9ybWFuY2UnKTtcclxuICAgIH0gZWxzZSBpZiAocmVzdWx0cy52ZWxvY2l0eSA+IHRoaXMuVkVMT0NJVFlfTElNSVRTLnN1cHBseS5vcHRpbWFsICogMS4yKSB7XHJcbiAgICAgIHJlY29tbWVuZGF0aW9ucy5wdXNoKCdDb25zaWRlciBpbmNyZWFzaW5nIGR1Y3Qgc2l6ZSB0byByZWR1Y2UgdmVsb2NpdHkgYW5kIG5vaXNlJyk7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gQXNwZWN0IHJhdGlvIHJlY29tbWVuZGF0aW9ucyAoZm9yIHJlY3Rhbmd1bGFyIGR1Y3RzKVxyXG4gICAgaWYgKHJlc3VsdHMuYXNwZWN0UmF0aW8gJiYgcmVzdWx0cy5hc3BlY3RSYXRpbyA+IDMuMCkge1xyXG4gICAgICByZWNvbW1lbmRhdGlvbnMucHVzaCgnQ29uc2lkZXIgdXNpbmcgcm91bmQgZHVjdCBvciByZWR1Y2luZyBhc3BlY3QgcmF0aW8gZm9yIGJldHRlciBwZXJmb3JtYW5jZScpO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIE1hdGVyaWFsIHJlY29tbWVuZGF0aW9uc1xyXG4gICAgaWYgKGlucHV0cy5tYXRlcmlhbCA9PT0gJ2dhbHZhbml6ZWRfc3RlZWwnICYmIHJlc3VsdHMudmVsb2NpdHkgPiAyMDAwKSB7XHJcbiAgICAgIHJlY29tbWVuZGF0aW9ucy5wdXNoKCdDb25zaWRlciB1c2luZyBzbW9vdGhlciBtYXRlcmlhbHMgbGlrZSBhbHVtaW51bSBmb3IgaGlnaC12ZWxvY2l0eSBhcHBsaWNhdGlvbnMnKTtcclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4gcmVjb21tZW5kYXRpb25zO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogRmluZCBuZWFyZXN0IHN0YW5kYXJkIHNpemVcclxuICAgKi9cclxuICBwcml2YXRlIHN0YXRpYyBmaW5kTmVhcmVzdFN0YW5kYXJkU2l6ZSh0YXJnZXQ6IG51bWJlciwgc3RhbmRhcmRTaXplczogbnVtYmVyW10pOiBudW1iZXIge1xyXG4gICAgcmV0dXJuIHN0YW5kYXJkU2l6ZXMucmVkdWNlKChwcmV2LCBjdXJyKSA9PiBcclxuICAgICAgTWF0aC5hYnMoY3VyciAtIHRhcmdldCkgPCBNYXRoLmFicyhwcmV2IC0gdGFyZ2V0KSA/IGN1cnIgOiBwcmV2XHJcbiAgICApO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ29udmVydCBtZXRyaWMgaW5wdXRzIHRvIGltcGVyaWFsXHJcbiAgICovXHJcbiAgcHJpdmF0ZSBzdGF0aWMgY29udmVydFRvSW1wZXJpYWwoaW5wdXRzOiBEdWN0U2l6aW5nSW5wdXRzKTogRHVjdFNpemluZ0lucHV0cyB7XHJcbiAgICByZXR1cm4ge1xyXG4gICAgICAuLi5pbnB1dHMsXHJcbiAgICAgIGFpcmZsb3c6IGlucHV0cy5haXJmbG93ICogMi4xMTksIC8vIG3Csy9oIHRvIENGTVxyXG4gICAgICBmcmljdGlvblJhdGU6IGlucHV0cy5mcmljdGlvblJhdGUgKiAwLjI0OSwgLy8gUGEvbSB0byBpbmNoZXMgdy5nLiBwZXIgMTAwIGZlZXRcclxuICAgICAgdW5pdHM6ICdpbXBlcmlhbCdcclxuICAgIH07XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBWYWxpZGF0ZSBpbnB1dCBwYXJhbWV0ZXJzXHJcbiAgICovXHJcbiAgcHJpdmF0ZSBzdGF0aWMgdmFsaWRhdGVJbnB1dHMoaW5wdXRzOiBEdWN0U2l6aW5nSW5wdXRzKTogdm9pZCB7XHJcbiAgICBpZiAoaW5wdXRzLmFpcmZsb3cgPD0gMCkge1xyXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ0FpcmZsb3cgbXVzdCBiZSBncmVhdGVyIHRoYW4gMCcpO1xyXG4gICAgfVxyXG4gICAgaWYgKGlucHV0cy5mcmljdGlvblJhdGUgPD0gMCkge1xyXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ0ZyaWN0aW9uIHJhdGUgbXVzdCBiZSBncmVhdGVyIHRoYW4gMCcpO1xyXG4gICAgfVxyXG4gICAgaWYgKCFbJ3JvdW5kJywgJ3JlY3Rhbmd1bGFyJ10uaW5jbHVkZXMoaW5wdXRzLmR1Y3RUeXBlKSkge1xyXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ0R1Y3QgdHlwZSBtdXN0IGJlIFwicm91bmRcIiBvciBcInJlY3Rhbmd1bGFyXCInKTtcclxuICAgIH1cclxuICAgIGlmICghWydpbXBlcmlhbCcsICdtZXRyaWMnXS5pbmNsdWRlcyhpbnB1dHMudW5pdHMpKSB7XHJcbiAgICAgIHRocm93IG5ldyBFcnJvcignVW5pdHMgbXVzdCBiZSBcImltcGVyaWFsXCIgb3IgXCJtZXRyaWNcIicpO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogR2V0IGF2YWlsYWJsZSBtYXRlcmlhbHNcclxuICAgKi9cclxuICBwdWJsaWMgc3RhdGljIGdldE1hdGVyaWFscygpOiBSZWNvcmQ8c3RyaW5nLCBNYXRlcmlhbFByb3BlcnRpZXM+IHtcclxuICAgIHJldHVybiB7IC4uLnRoaXMuTUFURVJJQUxfUFJPUEVSVElFUyB9O1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogR2V0IHZlbG9jaXR5IGxpbWl0c1xyXG4gICAqL1xyXG4gIHB1YmxpYyBzdGF0aWMgZ2V0VmVsb2NpdHlMaW1pdHMoKTogdHlwZW9mIEFpckR1Y3RDYWxjdWxhdG9yLlZFTE9DSVRZX0xJTUlUUyB7XHJcbiAgICByZXR1cm4geyAuLi50aGlzLlZFTE9DSVRZX0xJTUlUUyB9O1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogR2V0IHN0YW5kYXJkIHNpemVzXHJcbiAgICovXHJcbiAgcHVibGljIHN0YXRpYyBnZXRTdGFuZGFyZFNpemVzKCk6IHsgcm91bmQ6IG51bWJlcltdOyByZWN0YW5ndWxhcjogbnVtYmVyW10gfSB7XHJcbiAgICByZXR1cm4ge1xyXG4gICAgICByb3VuZDogWy4uLnRoaXMuUk9VTkRfU1RBTkRBUkRfU0laRVNdLFxyXG4gICAgICByZWN0YW5ndWxhcjogWy4uLnRoaXMuUkVDVEFOR1VMQVJfU1RBTkRBUkRfU0laRVNdXHJcbiAgICB9O1xyXG4gIH1cclxufVxyXG5cclxuZXhwb3J0IGRlZmF1bHQgQWlyRHVjdENhbGN1bGF0b3I7XHJcbiJdLCJtYXBwaW5ncyI6Ijs7QUFBQTs7Ozs7Ozs7O0FBQUE7QUFBQSxTQUFBQSxlQUFBO0VBQUEsSUFBQUMsSUFBQTtFQUFBLElBQUFDLElBQUE7RUFBQSxJQUFBQyxNQUFBLE9BQUFDLFFBQUE7RUFBQSxJQUFBQyxHQUFBO0VBQUEsSUFBQUMsWUFBQTtJQUFBTCxJQUFBO0lBQUFNLFlBQUE7TUFBQTtRQUFBQyxLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtJQUFBO0lBQUFFLEtBQUE7TUFBQTtRQUFBQyxJQUFBO1FBQUFDLElBQUE7VUFBQU4sS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUssR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBSSxJQUFBO1FBQUFDLElBQUE7VUFBQU4sS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUssR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBSSxJQUFBO1FBQUFDLElBQUE7VUFBQU4sS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUssR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBSSxJQUFBO1FBQUFDLElBQUE7VUFBQU4sS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUssR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBSSxJQUFBO1FBQUFDLElBQUE7VUFBQU4sS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUssR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBSSxJQUFBO1FBQUFDLElBQUE7VUFBQU4sS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUssR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBSSxJQUFBO1FBQUFDLElBQUE7VUFBQU4sS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUssR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBSSxJQUFBO1FBQUFDLElBQUE7VUFBQU4sS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUssR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBSSxJQUFBO1FBQUFDLElBQUE7VUFBQU4sS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUssR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBSSxJQUFBO1FBQUFDLElBQUE7VUFBQU4sS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUssR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBSSxJQUFBO1FBQUFDLElBQUE7VUFBQU4sS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUssR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBSSxJQUFBO1FBQUFDLElBQUE7VUFBQU4sS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUssR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBSSxJQUFBO1FBQUFDLElBQUE7VUFBQU4sS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUssR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBSSxJQUFBO1FBQUFDLElBQUE7VUFBQU4sS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUssR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBSSxJQUFBO1FBQUFDLElBQUE7VUFBQU4sS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUssR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBSSxJQUFBO1FBQUFDLElBQUE7VUFBQU4sS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUssR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBSSxJQUFBO1FBQUFDLElBQUE7VUFBQU4sS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUssR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBSSxJQUFBO1FBQUFDLElBQUE7VUFBQU4sS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUssR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBSSxJQUFBO1FBQUFDLElBQUE7VUFBQU4sS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUssR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBSSxJQUFBO1FBQUFDLElBQUE7VUFBQU4sS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUssR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7SUFBQTtJQUFBTyxTQUFBO01BQUE7UUFBQUQsR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7SUFBQTtJQUFBVyxDQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7SUFBQTtJQUFBQyxDQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtJQUFBO0lBQUFDLENBQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7SUFBQTtJQUFBQyxjQUFBO01BQUFDLElBQUE7TUFBQUMsUUFBQTtNQUFBQyxLQUFBO01BQUFDLE9BQUE7TUFBQUMsY0FBQTtNQUFBQyxPQUFBO0lBQUE7SUFBQUMsZUFBQTtJQUFBNUIsSUFBQTtFQUFBO0VBQUEsSUFBQTZCLFFBQUEsR0FBQTVCLE1BQUEsQ0FBQUUsR0FBQSxNQUFBRixNQUFBLENBQUFFLEdBQUE7RUFBQSxLQUFBMEIsUUFBQSxDQUFBOUIsSUFBQSxLQUFBOEIsUUFBQSxDQUFBOUIsSUFBQSxFQUFBQyxJQUFBLEtBQUFBLElBQUE7SUFBQTZCLFFBQUEsQ0FBQTlCLElBQUEsSUFBQUssWUFBQTtFQUFBO0VBQUEsSUFBQTBCLGNBQUEsR0FBQUQsUUFBQSxDQUFBOUIsSUFBQTtFQUFBO0lBb0VHO0lBQUFELGNBQUEsWUFBQUEsQ0FBQTtNQUFBLE9BQUFnQyxjQUFBO0lBQUE7RUFBQTtFQUFBLE9BQUFBLGNBQUE7QUFBQTtBQUFBaEMsY0FBQTtBQUFBQSxjQUFBLEdBQUFvQixDQUFBOzs7Ozs7O0FBSEg7Ozs7QUFJQSxNQUFhYSxpQkFBaUI7RUErQjVCOzs7O0VBSU8sT0FBT0MsbUJBQW1CQSxDQUFDQyxNQUF3QjtJQUFBO0lBQUFuQyxjQUFBLEdBQUFxQixDQUFBO0lBQUFyQixjQUFBLEdBQUFvQixDQUFBO0lBQ3hEO0lBQ0EsSUFBSSxDQUFDZ0IsY0FBYyxDQUFDRCxNQUFNLENBQUM7SUFFM0I7SUFDQSxNQUFNRSxjQUFjO0lBQUE7SUFBQSxDQUFBckMsY0FBQSxHQUFBb0IsQ0FBQSxPQUFHZSxNQUFNLENBQUNHLEtBQUssS0FBSyxRQUFRO0lBQUE7SUFBQSxDQUFBdEMsY0FBQSxHQUFBc0IsQ0FBQSxVQUM1QyxJQUFJLENBQUNpQixpQkFBaUIsQ0FBQ0osTUFBTSxDQUFDO0lBQUE7SUFBQSxDQUFBbkMsY0FBQSxHQUFBc0IsQ0FBQSxVQUM5QmEsTUFBTTtJQUVWO0lBQUE7SUFBQW5DLGNBQUEsR0FBQW9CLENBQUE7SUFDQSxJQUFJaUIsY0FBYyxDQUFDRyxRQUFRLEtBQUssT0FBTyxFQUFFO01BQUE7TUFBQXhDLGNBQUEsR0FBQXNCLENBQUE7TUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7TUFDdkMsT0FBTyxJQUFJLENBQUNxQixrQkFBa0IsQ0FBQ0osY0FBYyxDQUFDO0lBQ2hELENBQUMsTUFBTTtNQUFBO01BQUFyQyxjQUFBLEdBQUFzQixDQUFBO01BQUF0QixjQUFBLEdBQUFvQixDQUFBO01BQ0wsT0FBTyxJQUFJLENBQUNzQix3QkFBd0IsQ0FBQ0wsY0FBYyxDQUFDO0lBQ3REO0VBQ0Y7RUFFQTs7O0VBR1EsT0FBT0ksa0JBQWtCQSxDQUFDTixNQUF3QjtJQUFBO0lBQUFuQyxjQUFBLEdBQUFxQixDQUFBO0lBQ3hELE1BQU07TUFBRXNCLE9BQU87TUFBRUMsWUFBWTtNQUFFQyxRQUFRO01BQUE7TUFBQSxDQUFBN0MsY0FBQSxHQUFBc0IsQ0FBQSxVQUFHLGtCQUFrQjtJQUFBLENBQUU7SUFBQTtJQUFBLENBQUF0QixjQUFBLEdBQUFvQixDQUFBLE9BQUdlLE1BQU07SUFFdkU7SUFDQSxNQUFNVyxRQUFRO0lBQUE7SUFBQSxDQUFBOUMsY0FBQSxHQUFBb0IsQ0FBQSxPQUFHLElBQUksQ0FBQzJCLHdCQUF3QixDQUFDSixPQUFPLEVBQUVDLFlBQVksQ0FBQztJQUVyRTtJQUNBLE1BQU1JLElBQUk7SUFBQTtJQUFBLENBQUFoRCxjQUFBLEdBQUFvQixDQUFBLE9BQUc2QixJQUFJLENBQUNDLEVBQUUsR0FBR0QsSUFBSSxDQUFDRSxHQUFHLENBQUNMLFFBQVEsR0FBRyxFQUFFLEVBQUUsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxFQUFDLENBQUM7SUFDdkQsTUFBTU0sUUFBUTtJQUFBO0lBQUEsQ0FBQXBELGNBQUEsR0FBQW9CLENBQUEsUUFBR3VCLE9BQU8sR0FBR0ssSUFBSSxFQUFDLENBQUM7SUFFakM7SUFDQSxNQUFNSyxZQUFZO0lBQUE7SUFBQSxDQUFBckQsY0FBQSxHQUFBb0IsQ0FBQSxRQUFHLElBQUksQ0FBQ2tDLHFCQUFxQixDQUFDRixRQUFRLEVBQUUsR0FBRyxFQUFFTixRQUFRLEVBQUVELFFBQVEsQ0FBQztJQUVsRjtJQUNBLE1BQU1VLGNBQWM7SUFBQTtJQUFBLENBQUF2RCxjQUFBLEdBQUFvQixDQUFBLFFBQUcsSUFBSSxDQUFDb0MsdUJBQXVCLENBQUNKLFFBQVEsRUFBRU4sUUFBUSxDQUFDO0lBQ3ZFLE1BQU1XLGNBQWM7SUFBQTtJQUFBLENBQUF6RCxjQUFBLEdBQUFvQixDQUFBLFFBQUcsSUFBSSxDQUFDc0MsdUJBQXVCLENBQUNILGNBQWMsRUFBRVYsUUFBUSxFQUFFQyxRQUFRLENBQUM7SUFFdkY7SUFDQSxNQUFNYSxVQUFVO0lBQUE7SUFBQSxDQUFBM0QsY0FBQSxHQUFBb0IsQ0FBQSxRQUFHLElBQUksQ0FBQ3dDLGVBQWUsQ0FBQztNQUFFUixRQUFRO01BQUVOLFFBQVE7TUFBRUU7SUFBSSxDQUFFLENBQUM7SUFDckUsTUFBTWEsZUFBZTtJQUFBO0lBQUEsQ0FBQTdELGNBQUEsR0FBQW9CLENBQUEsUUFBRyxJQUFJLENBQUMwQyx1QkFBdUIsQ0FBQzNCLE1BQU0sRUFBRTtNQUFFVyxRQUFRO01BQUVNLFFBQVE7TUFBRUo7SUFBSSxDQUFFLENBQUM7SUFBQztJQUFBaEQsY0FBQSxHQUFBb0IsQ0FBQTtJQUUzRixPQUFPO01BQ0wwQixRQUFRO01BQ1JFLElBQUk7TUFDSkksUUFBUTtNQUNSQyxZQUFZO01BQ1pFLGNBQWM7TUFDZEUsY0FBYztNQUNkTSxTQUFTLEVBQUVKLFVBQVUsQ0FBQ0ksU0FBUztNQUMvQkMsUUFBUSxFQUFFTCxVQUFVLENBQUNLLFFBQVE7TUFDN0JILGVBQWU7TUFDZkksbUJBQW1CLEVBQUU7UUFDbkJDLE1BQU0sRUFBRVAsVUFBVSxDQUFDUSxlQUFlO1FBQ2xDQyxNQUFNLEVBQUVULFVBQVUsQ0FBQ1UsZUFBZTtRQUNsQ0MsaUJBQWlCLEVBQUVYLFVBQVUsQ0FBQ1c7O0tBRWpDO0VBQ0g7RUFFQTs7O0VBR1EsT0FBTzVCLHdCQUF3QkEsQ0FBQ1AsTUFBd0I7SUFBQTtJQUFBbkMsY0FBQSxHQUFBcUIsQ0FBQTtJQUM5RCxNQUFNO01BQUVzQixPQUFPO01BQUVDLFlBQVk7TUFBRUMsUUFBUTtNQUFBO01BQUEsQ0FBQTdDLGNBQUEsR0FBQXNCLENBQUEsVUFBRyxrQkFBa0I7SUFBQSxDQUFFO0lBQUE7SUFBQSxDQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQSxRQUFHZSxNQUFNO0lBRXZFO0lBQ0EsTUFBTTtNQUFFb0MsS0FBSztNQUFFQztJQUFNLENBQUU7SUFBQTtJQUFBLENBQUF4RSxjQUFBLEdBQUFvQixDQUFBLFFBQUcsSUFBSSxDQUFDcUQsZ0NBQWdDLENBQUM5QixPQUFPLEVBQUVDLFlBQVksQ0FBQztJQUV0RjtJQUNBLE1BQU1JLElBQUk7SUFBQTtJQUFBLENBQUFoRCxjQUFBLEdBQUFvQixDQUFBLFFBQUltRCxLQUFLLEdBQUdDLE1BQU0sR0FBSSxHQUFHLEVBQUMsQ0FBQztJQUNyQyxNQUFNcEIsUUFBUTtJQUFBO0lBQUEsQ0FBQXBELGNBQUEsR0FBQW9CLENBQUEsUUFBR3VCLE9BQU8sR0FBR0ssSUFBSSxFQUFDLENBQUM7SUFFakM7SUFDQSxNQUFNMEIsa0JBQWtCO0lBQUE7SUFBQSxDQUFBMUUsY0FBQSxHQUFBb0IsQ0FBQSxRQUFHLElBQUksQ0FBQ3VELDJCQUEyQixDQUFDSixLQUFLLEVBQUVDLE1BQU0sQ0FBQztJQUMxRSxNQUFNSSxpQkFBaUI7SUFBQTtJQUFBLENBQUE1RSxjQUFBLEdBQUFvQixDQUFBLFFBQUcsSUFBSSxDQUFDeUQsMEJBQTBCLENBQUNOLEtBQUssRUFBRUMsTUFBTSxDQUFDO0lBQ3hFLE1BQU1NLFdBQVc7SUFBQTtJQUFBLENBQUE5RSxjQUFBLEdBQUFvQixDQUFBLFFBQUcsSUFBSSxDQUFDMkQsb0JBQW9CLENBQUNSLEtBQUssRUFBRUMsTUFBTSxDQUFDO0lBRTVEO0lBQ0EsTUFBTW5CLFlBQVk7SUFBQTtJQUFBLENBQUFyRCxjQUFBLEdBQUFvQixDQUFBLFFBQUcsSUFBSSxDQUFDa0MscUJBQXFCLENBQUNGLFFBQVEsRUFBRSxHQUFHLEVBQUVzQixrQkFBa0IsRUFBRTdCLFFBQVEsQ0FBQztJQUU1RjtJQUNBLE1BQU1VLGNBQWM7SUFBQTtJQUFBLENBQUF2RCxjQUFBLEdBQUFvQixDQUFBLFFBQUcsSUFBSSxDQUFDb0MsdUJBQXVCLENBQUNKLFFBQVEsRUFBRXdCLGlCQUFpQixDQUFDO0lBQ2hGLE1BQU1uQixjQUFjO0lBQUE7SUFBQSxDQUFBekQsY0FBQSxHQUFBb0IsQ0FBQSxRQUFHLElBQUksQ0FBQ3NDLHVCQUF1QixDQUFDSCxjQUFjLEVBQUVWLFFBQVEsRUFBRStCLGlCQUFpQixDQUFDO0lBRWhHO0lBQ0EsTUFBTWpCLFVBQVU7SUFBQTtJQUFBLENBQUEzRCxjQUFBLEdBQUFvQixDQUFBLFFBQUcsSUFBSSxDQUFDd0MsZUFBZSxDQUFDO01BQUVSLFFBQVE7TUFBRW1CLEtBQUs7TUFBRUMsTUFBTTtNQUFFTSxXQUFXO01BQUU5QjtJQUFJLENBQUUsQ0FBQztJQUN2RixNQUFNYSxlQUFlO0lBQUE7SUFBQSxDQUFBN0QsY0FBQSxHQUFBb0IsQ0FBQSxRQUFHLElBQUksQ0FBQzBDLHVCQUF1QixDQUFDM0IsTUFBTSxFQUFFO01BQUVvQyxLQUFLO01BQUVDLE1BQU07TUFBRXBCLFFBQVE7TUFBRUosSUFBSTtNQUFFOEI7SUFBVyxDQUFFLENBQUM7SUFBQztJQUFBOUUsY0FBQSxHQUFBb0IsQ0FBQTtJQUU3RyxPQUFPO01BQ0xtRCxLQUFLO01BQ0xDLE1BQU07TUFDTnhCLElBQUk7TUFDSkksUUFBUTtNQUNSQyxZQUFZO01BQ1pFLGNBQWM7TUFDZEUsY0FBYztNQUNkaUIsa0JBQWtCO01BQ2xCRSxpQkFBaUI7TUFDakJFLFdBQVc7TUFDWGYsU0FBUyxFQUFFSixVQUFVLENBQUNJLFNBQVM7TUFDL0JDLFFBQVEsRUFBRUwsVUFBVSxDQUFDSyxRQUFRO01BQzdCSCxlQUFlO01BQ2ZJLG1CQUFtQixFQUFFO1FBQ25CQyxNQUFNLEVBQUVQLFVBQVUsQ0FBQ1EsZUFBZTtRQUNsQ0MsTUFBTSxFQUFFVCxVQUFVLENBQUNVLGVBQWU7UUFDbENDLGlCQUFpQixFQUFFWCxVQUFVLENBQUNXOztLQUVqQztFQUNIO0VBRUE7OztFQUdRLE9BQU92Qix3QkFBd0JBLENBQUNKLE9BQWUsRUFBRXFDLGNBQXNCO0lBQUE7SUFBQWhGLGNBQUEsR0FBQXFCLENBQUE7SUFDN0UsSUFBSTRELFlBQVk7SUFBQTtJQUFBLENBQUFqRixjQUFBLEdBQUFvQixDQUFBLFFBQUcsSUFBSSxDQUFDOEQsb0JBQW9CLENBQUMsQ0FBQyxDQUFDO0lBQy9DLElBQUlDLFNBQVM7SUFBQTtJQUFBLENBQUFuRixjQUFBLEdBQUFvQixDQUFBLFFBQUdnRSxRQUFRO0lBQUM7SUFBQXBGLGNBQUEsR0FBQW9CLENBQUE7SUFFekIsS0FBSyxNQUFNMEIsUUFBUSxJQUFJLElBQUksQ0FBQ29DLG9CQUFvQixFQUFFO01BQ2hELE1BQU1sQyxJQUFJO01BQUE7TUFBQSxDQUFBaEQsY0FBQSxHQUFBb0IsQ0FBQSxRQUFHNkIsSUFBSSxDQUFDQyxFQUFFLEdBQUdELElBQUksQ0FBQ0UsR0FBRyxDQUFDTCxRQUFRLEdBQUcsRUFBRSxFQUFFLENBQUMsQ0FBQyxHQUFHLENBQUMsRUFBQyxDQUFDO01BQ3ZELE1BQU1NLFFBQVE7TUFBQTtNQUFBLENBQUFwRCxjQUFBLEdBQUFvQixDQUFBLFFBQUd1QixPQUFPLEdBQUdLLElBQUksRUFBQyxDQUFDO01BRWpDO01BQUE7TUFBQWhELGNBQUEsR0FBQW9CLENBQUE7TUFDQTtNQUFJO01BQUEsQ0FBQXBCLGNBQUEsR0FBQXNCLENBQUEsVUFBQThCLFFBQVEsR0FBRyxJQUFJLENBQUNpQyxlQUFlLENBQUNDLE1BQU0sQ0FBQ0MsR0FBRztNQUFBO01BQUEsQ0FBQXZGLGNBQUEsR0FBQXNCLENBQUEsVUFBSThCLFFBQVEsR0FBRyxJQUFJLENBQUNpQyxlQUFlLENBQUNDLE1BQU0sQ0FBQ0UsR0FBRyxHQUFFO1FBQUE7UUFBQXhGLGNBQUEsR0FBQXNCLENBQUE7UUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7UUFDNUY7TUFDRixDQUFDO01BQUE7TUFBQTtRQUFBcEIsY0FBQSxHQUFBc0IsQ0FBQTtNQUFBO01BRUQ7TUFDQSxNQUFNbUUsY0FBYztNQUFBO01BQUEsQ0FBQXpGLGNBQUEsR0FBQW9CLENBQUEsUUFBRyxJQUFJLENBQUNrQyxxQkFBcUIsQ0FBQ0YsUUFBUSxFQUFFLEdBQUcsRUFBRU4sUUFBUSxFQUFFLGtCQUFrQixDQUFDO01BRTlGO01BQ0EsTUFBTTRDLGFBQWE7TUFBQTtNQUFBLENBQUExRixjQUFBLEdBQUFvQixDQUFBLFFBQUc2QixJQUFJLENBQUMwQyxHQUFHLENBQUNGLGNBQWMsR0FBR1QsY0FBYyxDQUFDLEdBQUdBLGNBQWM7TUFDaEYsTUFBTVksYUFBYTtNQUFBO01BQUEsQ0FBQTVGLGNBQUEsR0FBQW9CLENBQUEsUUFBRzZCLElBQUksQ0FBQzBDLEdBQUcsQ0FBQ3ZDLFFBQVEsR0FBRyxJQUFJLENBQUNpQyxlQUFlLENBQUNDLE1BQU0sQ0FBQ08sT0FBTyxDQUFDLEdBQUcsSUFBSSxDQUFDUixlQUFlLENBQUNDLE1BQU0sQ0FBQ08sT0FBTztNQUNwSCxNQUFNQyxVQUFVO01BQUE7TUFBQSxDQUFBOUYsY0FBQSxHQUFBb0IsQ0FBQSxRQUFHc0UsYUFBYSxHQUFHRSxhQUFhLEdBQUcsR0FBRyxFQUFDLENBQUM7TUFBQTtNQUFBNUYsY0FBQSxHQUFBb0IsQ0FBQTtNQUV4RCxJQUFJMEUsVUFBVSxHQUFHWCxTQUFTLEVBQUU7UUFBQTtRQUFBbkYsY0FBQSxHQUFBc0IsQ0FBQTtRQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtRQUMxQitELFNBQVMsR0FBR1csVUFBVTtRQUFDO1FBQUE5RixjQUFBLEdBQUFvQixDQUFBO1FBQ3ZCNkQsWUFBWSxHQUFHbkMsUUFBUTtNQUN6QixDQUFDO01BQUE7TUFBQTtRQUFBOUMsY0FBQSxHQUFBc0IsQ0FBQTtNQUFBO0lBQ0g7SUFBQztJQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtJQUVELE9BQU82RCxZQUFZO0VBQ3JCO0VBRUE7OztFQUdRLE9BQU9SLGdDQUFnQ0EsQ0FBQzlCLE9BQWUsRUFBRXFDLGNBQXNCO0lBQUE7SUFBQWhGLGNBQUEsR0FBQXFCLENBQUE7SUFDckYsSUFBSTBFLFNBQVM7SUFBQTtJQUFBLENBQUEvRixjQUFBLEdBQUFvQixDQUFBLFFBQUcsSUFBSSxDQUFDNEUsMEJBQTBCLENBQUMsQ0FBQyxDQUFDO0lBQ2xELElBQUlDLFVBQVU7SUFBQTtJQUFBLENBQUFqRyxjQUFBLEdBQUFvQixDQUFBLFFBQUcsSUFBSSxDQUFDNEUsMEJBQTBCLENBQUMsQ0FBQyxDQUFDO0lBQ25ELElBQUliLFNBQVM7SUFBQTtJQUFBLENBQUFuRixjQUFBLEdBQUFvQixDQUFBLFFBQUdnRSxRQUFRO0lBRXhCO0lBQ0EsTUFBTWMsWUFBWTtJQUFBO0lBQUEsQ0FBQWxHLGNBQUEsR0FBQW9CLENBQUEsUUFBRyxDQUFDLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsQ0FBQztJQUFDO0lBQUFwQixjQUFBLEdBQUFvQixDQUFBO0lBRXpELEtBQUssTUFBTTBELFdBQVcsSUFBSW9CLFlBQVksRUFBRTtNQUN0QztNQUNBLE1BQU1DLGFBQWE7TUFBQTtNQUFBLENBQUFuRyxjQUFBLEdBQUFvQixDQUFBLFFBQUd1QixPQUFPLEdBQUcsSUFBSSxDQUFDMEMsZUFBZSxDQUFDQyxNQUFNLENBQUNPLE9BQU8sRUFBQyxDQUFDO01BQ3JFLE1BQU1yQixNQUFNO01BQUE7TUFBQSxDQUFBeEUsY0FBQSxHQUFBb0IsQ0FBQSxRQUFHNkIsSUFBSSxDQUFDbUQsSUFBSSxDQUFDRCxhQUFhLEdBQUdyQixXQUFXLENBQUMsR0FBRyxFQUFFLEVBQUMsQ0FBQztNQUM1RCxNQUFNUCxLQUFLO01BQUE7TUFBQSxDQUFBdkUsY0FBQSxHQUFBb0IsQ0FBQSxRQUFHMEQsV0FBVyxHQUFHTixNQUFNO01BRWxDO01BQ0EsTUFBTTZCLFNBQVM7TUFBQTtNQUFBLENBQUFyRyxjQUFBLEdBQUFvQixDQUFBLFFBQUcsSUFBSSxDQUFDa0YsdUJBQXVCLENBQUM5QixNQUFNLEVBQUUsSUFBSSxDQUFDd0IsMEJBQTBCLENBQUM7TUFDdkYsTUFBTU8sUUFBUTtNQUFBO01BQUEsQ0FBQXZHLGNBQUEsR0FBQW9CLENBQUEsUUFBRyxJQUFJLENBQUNrRix1QkFBdUIsQ0FBQy9CLEtBQUssRUFBRSxJQUFJLENBQUN5QiwwQkFBMEIsQ0FBQztNQUVyRjtNQUNBLE1BQU1oRCxJQUFJO01BQUE7TUFBQSxDQUFBaEQsY0FBQSxHQUFBb0IsQ0FBQSxRQUFJbUYsUUFBUSxHQUFHRixTQUFTLEdBQUksR0FBRyxFQUFDLENBQUM7TUFDM0MsTUFBTWpELFFBQVE7TUFBQTtNQUFBLENBQUFwRCxjQUFBLEdBQUFvQixDQUFBLFFBQUd1QixPQUFPLEdBQUdLLElBQUksRUFBQyxDQUFDO01BRWpDO01BQUE7TUFBQWhELGNBQUEsR0FBQW9CLENBQUE7TUFDQTtNQUFJO01BQUEsQ0FBQXBCLGNBQUEsR0FBQXNCLENBQUEsVUFBQThCLFFBQVEsR0FBRyxJQUFJLENBQUNpQyxlQUFlLENBQUNDLE1BQU0sQ0FBQ0MsR0FBRztNQUFBO01BQUEsQ0FBQXZGLGNBQUEsR0FBQXNCLENBQUEsVUFBSThCLFFBQVEsR0FBRyxJQUFJLENBQUNpQyxlQUFlLENBQUNDLE1BQU0sQ0FBQ0UsR0FBRyxHQUFFO1FBQUE7UUFBQXhGLGNBQUEsR0FBQXNCLENBQUE7UUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7UUFDNUY7TUFDRixDQUFDO01BQUE7TUFBQTtRQUFBcEIsY0FBQSxHQUFBc0IsQ0FBQTtNQUFBO01BRUQ7TUFDQSxNQUFNb0Qsa0JBQWtCO01BQUE7TUFBQSxDQUFBMUUsY0FBQSxHQUFBb0IsQ0FBQSxRQUFHLElBQUksQ0FBQ3VELDJCQUEyQixDQUFDNEIsUUFBUSxFQUFFRixTQUFTLENBQUM7TUFDaEYsTUFBTVosY0FBYztNQUFBO01BQUEsQ0FBQXpGLGNBQUEsR0FBQW9CLENBQUEsUUFBRyxJQUFJLENBQUNrQyxxQkFBcUIsQ0FBQ0YsUUFBUSxFQUFFLEdBQUcsRUFBRXNCLGtCQUFrQixFQUFFLGtCQUFrQixDQUFDO01BRXhHO01BQ0EsTUFBTWdCLGFBQWE7TUFBQTtNQUFBLENBQUExRixjQUFBLEdBQUFvQixDQUFBLFFBQUc2QixJQUFJLENBQUMwQyxHQUFHLENBQUNGLGNBQWMsR0FBR1QsY0FBYyxDQUFDLEdBQUdBLGNBQWM7TUFDaEYsTUFBTVksYUFBYTtNQUFBO01BQUEsQ0FBQTVGLGNBQUEsR0FBQW9CLENBQUEsUUFBRzZCLElBQUksQ0FBQzBDLEdBQUcsQ0FBQ3ZDLFFBQVEsR0FBRyxJQUFJLENBQUNpQyxlQUFlLENBQUNDLE1BQU0sQ0FBQ08sT0FBTyxDQUFDLEdBQUcsSUFBSSxDQUFDUixlQUFlLENBQUNDLE1BQU0sQ0FBQ08sT0FBTztNQUNwSCxNQUFNVyxXQUFXO01BQUE7TUFBQSxDQUFBeEcsY0FBQSxHQUFBb0IsQ0FBQSxRQUFHMEQsV0FBVyxHQUFHLEdBQUc7TUFBQTtNQUFBLENBQUE5RSxjQUFBLEdBQUFzQixDQUFBLFVBQUcsQ0FBQ3dELFdBQVcsR0FBRyxHQUFHLElBQUksR0FBRztNQUFBO01BQUEsQ0FBQTlFLGNBQUEsR0FBQXNCLENBQUEsVUFBRyxDQUFDLEdBQUMsQ0FBQztNQUN2RSxNQUFNd0UsVUFBVTtNQUFBO01BQUEsQ0FBQTlGLGNBQUEsR0FBQW9CLENBQUEsUUFBR3NFLGFBQWEsR0FBR0UsYUFBYSxHQUFHLEdBQUcsR0FBR1ksV0FBVztNQUFDO01BQUF4RyxjQUFBLEdBQUFvQixDQUFBO01BRXJFLElBQUkwRSxVQUFVLEdBQUdYLFNBQVMsRUFBRTtRQUFBO1FBQUFuRixjQUFBLEdBQUFzQixDQUFBO1FBQUF0QixjQUFBLEdBQUFvQixDQUFBO1FBQzFCK0QsU0FBUyxHQUFHVyxVQUFVO1FBQUM7UUFBQTlGLGNBQUEsR0FBQW9CLENBQUE7UUFDdkIyRSxTQUFTLEdBQUdRLFFBQVE7UUFBQztRQUFBdkcsY0FBQSxHQUFBb0IsQ0FBQTtRQUNyQjZFLFVBQVUsR0FBR0ksU0FBUztNQUN4QixDQUFDO01BQUE7TUFBQTtRQUFBckcsY0FBQSxHQUFBc0IsQ0FBQTtNQUFBO0lBQ0g7SUFBQztJQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtJQUVELE9BQU87TUFBRW1ELEtBQUssRUFBRXdCLFNBQVM7TUFBRXZCLE1BQU0sRUFBRXlCO0lBQVUsQ0FBRTtFQUNqRDtFQUVBOzs7RUFHTyxPQUFPdEIsMkJBQTJCQSxDQUFDSixLQUFhLEVBQUVDLE1BQWM7SUFBQTtJQUFBeEUsY0FBQSxHQUFBcUIsQ0FBQTtJQUFBckIsY0FBQSxHQUFBb0IsQ0FBQTtJQUNyRSxPQUFPLEdBQUcsR0FBRzZCLElBQUksQ0FBQ0UsR0FBRyxDQUFDb0IsS0FBSyxHQUFHQyxNQUFNLEVBQUUsS0FBSyxDQUFDLEdBQUd2QixJQUFJLENBQUNFLEdBQUcsQ0FBQ29CLEtBQUssR0FBR0MsTUFBTSxFQUFFLElBQUksQ0FBQztFQUMvRTtFQUVBOzs7RUFHTyxPQUFPSywwQkFBMEJBLENBQUNOLEtBQWEsRUFBRUMsTUFBYztJQUFBO0lBQUF4RSxjQUFBLEdBQUFxQixDQUFBO0lBQUFyQixjQUFBLEdBQUFvQixDQUFBO0lBQ3BFLE9BQVEsQ0FBQyxHQUFHbUQsS0FBSyxHQUFHQyxNQUFNLElBQUssQ0FBQyxJQUFJRCxLQUFLLEdBQUdDLE1BQU0sQ0FBQyxDQUFDO0VBQ3REO0VBRUE7OztFQUdPLE9BQU9PLG9CQUFvQkEsQ0FBQ1IsS0FBYSxFQUFFQyxNQUFjO0lBQUE7SUFBQXhFLGNBQUEsR0FBQXFCLENBQUE7SUFBQXJCLGNBQUEsR0FBQW9CLENBQUE7SUFDOUQsT0FBTzZCLElBQUksQ0FBQ3VDLEdBQUcsQ0FBQ2pCLEtBQUssRUFBRUMsTUFBTSxDQUFDLEdBQUd2QixJQUFJLENBQUNzQyxHQUFHLENBQUNoQixLQUFLLEVBQUVDLE1BQU0sQ0FBQztFQUMxRDtFQUVBOzs7RUFHTyxPQUFPbEIscUJBQXFCQSxDQUFDRixRQUFnQixFQUFFcUQsTUFBYyxFQUFFM0QsUUFBZ0IsRUFBRUQsUUFBZ0I7SUFBQTtJQUFBN0MsY0FBQSxHQUFBcUIsQ0FBQTtJQUN0RyxNQUFNcUYsYUFBYTtJQUFBO0lBQUEsQ0FBQTFHLGNBQUEsR0FBQW9CLENBQUE7SUFBRztJQUFBLENBQUFwQixjQUFBLEdBQUFzQixDQUFBLGVBQUksQ0FBQ3FGLG1CQUFtQixDQUFDOUQsUUFBUSxDQUFDO0lBQUE7SUFBQSxDQUFBN0MsY0FBQSxHQUFBc0IsQ0FBQSxXQUFJLElBQUksQ0FBQ3FGLG1CQUFtQixDQUFDQyxnQkFBZ0I7SUFDckcsTUFBTUMsU0FBUztJQUFBO0lBQUEsQ0FBQTdHLGNBQUEsR0FBQW9CLENBQUEsUUFBR3NGLGFBQWEsQ0FBQ0ksZUFBZTtJQUUvQztJQUNBLE1BQU1DLFdBQVc7SUFBQTtJQUFBLENBQUEvRyxjQUFBLEdBQUFvQixDQUFBLFFBQUdnQyxRQUFRLEdBQUcsRUFBRSxFQUFDLENBQUM7SUFDbkMsTUFBTTRELFVBQVU7SUFBQTtJQUFBLENBQUFoSCxjQUFBLEdBQUFvQixDQUFBLFFBQUcwQixRQUFRLEdBQUcsRUFBRSxFQUFDLENBQUM7SUFFbEM7SUFDQSxNQUFNbUUsVUFBVTtJQUFBO0lBQUEsQ0FBQWpILGNBQUEsR0FBQW9CLENBQUEsUUFBRyxLQUFLLEVBQUMsQ0FBQztJQUMxQixNQUFNOEYsa0JBQWtCO0lBQUE7SUFBQSxDQUFBbEgsY0FBQSxHQUFBb0IsQ0FBQSxRQUFHLE9BQU8sRUFBQyxDQUFDO0lBRXBDO0lBQ0EsTUFBTStGLFFBQVE7SUFBQTtJQUFBLENBQUFuSCxjQUFBLEdBQUFvQixDQUFBLFFBQUkyRixXQUFXLEdBQUdDLFVBQVUsR0FBSUUsa0JBQWtCO0lBRWhFO0lBQ0EsTUFBTXpELGNBQWM7SUFBQTtJQUFBLENBQUF6RCxjQUFBLEdBQUFvQixDQUFBLFFBQUcsSUFBSSxDQUFDc0MsdUJBQXVCLENBQUN5RCxRQUFRLEVBQUV0RSxRQUFRLEVBQUVDLFFBQVEsQ0FBQztJQUVqRjtJQUNBO0lBQ0EsTUFBTXNFLGVBQWU7SUFBQTtJQUFBLENBQUFwSCxjQUFBLEdBQUFvQixDQUFBLFFBQUdxQyxjQUFjLElBQUlnRCxNQUFNLEdBQUdPLFVBQVUsQ0FBQyxJQUFJQyxVQUFVLEdBQUdoRSxJQUFJLENBQUNFLEdBQUcsQ0FBQzRELFdBQVcsRUFBRSxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsR0FBRyxNQUFNLENBQUM7SUFDdkgsTUFBTU0sZ0JBQWdCO0lBQUE7SUFBQSxDQUFBckgsY0FBQSxHQUFBb0IsQ0FBQSxRQUFHZ0csZUFBZSxHQUFHLEdBQUcsRUFBQyxDQUFDO0lBQUE7SUFBQXBILGNBQUEsR0FBQW9CLENBQUE7SUFFaEQsT0FBT2lHLGdCQUFnQjtFQUN6QjtFQUVBOzs7RUFHTyxPQUFPN0QsdUJBQXVCQSxDQUFDSixRQUFnQixFQUFFTixRQUFnQjtJQUFBO0lBQUE5QyxjQUFBLEdBQUFxQixDQUFBO0lBQ3RFLE1BQU0wRixXQUFXO0lBQUE7SUFBQSxDQUFBL0csY0FBQSxHQUFBb0IsQ0FBQSxRQUFHZ0MsUUFBUSxHQUFHLEVBQUUsRUFBQyxDQUFDO0lBQ25DLE1BQU00RCxVQUFVO0lBQUE7SUFBQSxDQUFBaEgsY0FBQSxHQUFBb0IsQ0FBQSxRQUFHMEIsUUFBUSxHQUFHLEVBQUUsRUFBQyxDQUFDO0lBQ2xDLE1BQU1vRSxrQkFBa0I7SUFBQTtJQUFBLENBQUFsSCxjQUFBLEdBQUFvQixDQUFBLFFBQUcsT0FBTyxFQUFDLENBQUM7SUFBQTtJQUFBcEIsY0FBQSxHQUFBb0IsQ0FBQTtJQUVwQyxPQUFRMkYsV0FBVyxHQUFHQyxVQUFVLEdBQUlFLGtCQUFrQjtFQUN4RDtFQUVBOzs7RUFHTyxPQUFPeEQsdUJBQXVCQSxDQUFDeUQsUUFBZ0IsRUFBRXRFLFFBQWdCLEVBQUVDLFFBQWdCO0lBQUE7SUFBQTlDLGNBQUEsR0FBQXFCLENBQUE7SUFDeEYsTUFBTXFGLGFBQWE7SUFBQTtJQUFBLENBQUExRyxjQUFBLEdBQUFvQixDQUFBO0lBQUc7SUFBQSxDQUFBcEIsY0FBQSxHQUFBc0IsQ0FBQSxlQUFJLENBQUNxRixtQkFBbUIsQ0FBQzlELFFBQVEsQ0FBQztJQUFBO0lBQUEsQ0FBQTdDLGNBQUEsR0FBQXNCLENBQUEsV0FBSSxJQUFJLENBQUNxRixtQkFBbUIsQ0FBQ0MsZ0JBQWdCO0lBQ3JHLE1BQU1DLFNBQVM7SUFBQTtJQUFBLENBQUE3RyxjQUFBLEdBQUFvQixDQUFBLFFBQUdzRixhQUFhLENBQUNJLGVBQWU7SUFDL0MsTUFBTUUsVUFBVTtJQUFBO0lBQUEsQ0FBQWhILGNBQUEsR0FBQW9CLENBQUEsUUFBRzBCLFFBQVEsR0FBRyxFQUFFO0lBQ2hDLE1BQU13RSxpQkFBaUI7SUFBQTtJQUFBLENBQUF0SCxjQUFBLEdBQUFvQixDQUFBLFFBQUd5RixTQUFTLEdBQUdHLFVBQVU7SUFFaEQ7SUFBQTtJQUFBaEgsY0FBQSxHQUFBb0IsQ0FBQTtJQUNBLElBQUkrRixRQUFRLEdBQUcsSUFBSSxFQUFFO01BQUE7TUFBQW5ILGNBQUEsR0FBQXNCLENBQUE7TUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7TUFDbkIsT0FBTyxFQUFFLEdBQUcrRixRQUFRO0lBQ3RCLENBQUM7SUFBQTtJQUFBO01BQUFuSCxjQUFBLEdBQUFzQixDQUFBO0lBQUE7SUFFRDtJQUNBLElBQUlELENBQUM7SUFBQTtJQUFBLENBQUFyQixjQUFBLEdBQUFvQixDQUFBLFFBQUcsSUFBSSxFQUFDLENBQUM7SUFBQTtJQUFBcEIsY0FBQSxHQUFBb0IsQ0FBQTtJQUNkLEtBQUssSUFBSW1HLENBQUM7SUFBQTtJQUFBLENBQUF2SCxjQUFBLEdBQUFvQixDQUFBLFFBQUcsQ0FBQyxHQUFFbUcsQ0FBQyxHQUFHLEVBQUUsRUFBRUEsQ0FBQyxFQUFFLEVBQUU7TUFDM0IsTUFBTUMsSUFBSTtNQUFBO01BQUEsQ0FBQXhILGNBQUEsR0FBQW9CLENBQUEsUUFBRyxDQUFDLEdBQUc2QixJQUFJLENBQUNFLEdBQUcsQ0FBQyxDQUFDLENBQUMsR0FBR0YsSUFBSSxDQUFDd0UsS0FBSyxDQUFDSCxpQkFBaUIsR0FBRyxHQUFHLEdBQUcsSUFBSSxJQUFJSCxRQUFRLEdBQUdsRSxJQUFJLENBQUNtRCxJQUFJLENBQUMvRSxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDO01BQUM7TUFBQXJCLGNBQUEsR0FBQW9CLENBQUE7TUFDMUcsSUFBSTZCLElBQUksQ0FBQzBDLEdBQUcsQ0FBQzZCLElBQUksR0FBR25HLENBQUMsQ0FBQyxHQUFHLE1BQU0sRUFBRTtRQUFBO1FBQUFyQixjQUFBLEdBQUFzQixDQUFBO1FBQUF0QixjQUFBLEdBQUFvQixDQUFBO1FBQUE7TUFBQSxDQUFNO01BQUE7TUFBQTtRQUFBcEIsY0FBQSxHQUFBc0IsQ0FBQTtNQUFBO01BQUF0QixjQUFBLEdBQUFvQixDQUFBO01BQ3ZDQyxDQUFDLEdBQUdtRyxJQUFJO0lBQ1Y7SUFBQztJQUFBeEgsY0FBQSxHQUFBb0IsQ0FBQTtJQUVELE9BQU9DLENBQUM7RUFDVjtFQUVBOzs7RUFHUSxPQUFPdUMsZUFBZUEsQ0FBQzhELE9BQVk7SUFBQTtJQUFBMUgsY0FBQSxHQUFBcUIsQ0FBQTtJQU96QyxNQUFNMkMsUUFBUTtJQUFBO0lBQUEsQ0FBQWhFLGNBQUEsR0FBQW9CLENBQUEsU0FBYSxFQUFFO0lBQzdCLElBQUkrQyxlQUFlO0lBQUE7SUFBQSxDQUFBbkUsY0FBQSxHQUFBb0IsQ0FBQSxTQUFHLElBQUk7SUFDMUIsSUFBSWlELGVBQWU7SUFBQTtJQUFBLENBQUFyRSxjQUFBLEdBQUFvQixDQUFBLFNBQUcsSUFBSTtJQUMxQixJQUFJa0QsaUJBQWlCO0lBQUE7SUFBQSxDQUFBdEUsY0FBQSxHQUFBb0IsQ0FBQSxTQUFHLElBQUk7SUFFNUI7SUFDQSxNQUFNO01BQUVnQztJQUFRLENBQUU7SUFBQTtJQUFBLENBQUFwRCxjQUFBLEdBQUFvQixDQUFBLFNBQUdzRyxPQUFPO0lBQUM7SUFBQTFILGNBQUEsR0FBQW9CLENBQUE7SUFDN0IsSUFBSWdDLFFBQVEsR0FBRyxJQUFJLENBQUNpQyxlQUFlLENBQUNDLE1BQU0sQ0FBQ0MsR0FBRyxFQUFFO01BQUE7TUFBQXZGLGNBQUEsR0FBQXNCLENBQUE7TUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7TUFDOUM0QyxRQUFRLENBQUMyRCxJQUFJLENBQUMsWUFBWXZFLFFBQVEsQ0FBQ3dFLE9BQU8sQ0FBQyxDQUFDLENBQUMsc0NBQXNDLElBQUksQ0FBQ3ZDLGVBQWUsQ0FBQ0MsTUFBTSxDQUFDQyxHQUFHLE9BQU8sQ0FBQztNQUFDO01BQUF2RixjQUFBLEdBQUFvQixDQUFBO01BQzNIa0QsaUJBQWlCLEdBQUcsS0FBSztJQUMzQixDQUFDLE1BQU07TUFBQTtNQUFBdEUsY0FBQSxHQUFBc0IsQ0FBQTtNQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtNQUFBLElBQUlnQyxRQUFRLEdBQUcsSUFBSSxDQUFDaUMsZUFBZSxDQUFDQyxNQUFNLENBQUNFLEdBQUcsRUFBRTtRQUFBO1FBQUF4RixjQUFBLEdBQUFzQixDQUFBO1FBQUF0QixjQUFBLEdBQUFvQixDQUFBO1FBQ3JENEMsUUFBUSxDQUFDMkQsSUFBSSxDQUFDLFlBQVl2RSxRQUFRLENBQUN3RSxPQUFPLENBQUMsQ0FBQyxDQUFDLHFDQUFxQyxJQUFJLENBQUN2QyxlQUFlLENBQUNDLE1BQU0sQ0FBQ0UsR0FBRyxPQUFPLENBQUM7UUFBQztRQUFBeEYsY0FBQSxHQUFBb0IsQ0FBQTtRQUMxSGtELGlCQUFpQixHQUFHLEtBQUs7UUFBQztRQUFBdEUsY0FBQSxHQUFBb0IsQ0FBQTtRQUMxQitDLGVBQWUsR0FBRyxLQUFLO01BQ3pCLENBQUM7TUFBQTtNQUFBO1FBQUFuRSxjQUFBLEdBQUFzQixDQUFBO01BQUE7SUFBRDtJQUVBO0lBQUE7SUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7SUFDQSxJQUFJc0csT0FBTyxDQUFDNUMsV0FBVyxFQUFFO01BQUE7TUFBQTlFLGNBQUEsR0FBQXNCLENBQUE7TUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7TUFDdkIsSUFBSXNHLE9BQU8sQ0FBQzVDLFdBQVcsR0FBRyxHQUFHLEVBQUU7UUFBQTtRQUFBOUUsY0FBQSxHQUFBc0IsQ0FBQTtRQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtRQUM3QjRDLFFBQVEsQ0FBQzJELElBQUksQ0FBQyxnQkFBZ0JELE9BQU8sQ0FBQzVDLFdBQVcsQ0FBQzhDLE9BQU8sQ0FBQyxDQUFDLENBQUMsa0NBQWtDLENBQUM7UUFBQztRQUFBNUgsY0FBQSxHQUFBb0IsQ0FBQTtRQUNoRytDLGVBQWUsR0FBRyxLQUFLO01BQ3pCLENBQUMsTUFBTTtRQUFBO1FBQUFuRSxjQUFBLEdBQUFzQixDQUFBO1FBQUF0QixjQUFBLEdBQUFvQixDQUFBO1FBQUEsSUFBSXNHLE9BQU8sQ0FBQzVDLFdBQVcsR0FBRyxHQUFHLEVBQUU7VUFBQTtVQUFBOUUsY0FBQSxHQUFBc0IsQ0FBQTtVQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtVQUNwQzRDLFFBQVEsQ0FBQzJELElBQUksQ0FBQyxnQkFBZ0JELE9BQU8sQ0FBQzVDLFdBQVcsQ0FBQzhDLE9BQU8sQ0FBQyxDQUFDLENBQUMsb0NBQW9DLENBQUM7UUFDbkcsQ0FBQztRQUFBO1FBQUE7VUFBQTVILGNBQUEsR0FBQXNCLENBQUE7UUFBQTtNQUFEO0lBQ0YsQ0FBQztJQUFBO0lBQUE7TUFBQXRCLGNBQUEsR0FBQXNCLENBQUE7SUFBQTtJQUVEO0lBQUF0QixjQUFBLEdBQUFvQixDQUFBO0lBQ0EsSUFBSXNHLE9BQU8sQ0FBQzFFLElBQUksR0FBRyxHQUFHLEVBQUU7TUFBQTtNQUFBaEQsY0FBQSxHQUFBc0IsQ0FBQTtNQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtNQUN0QjRDLFFBQVEsQ0FBQzJELElBQUksQ0FBQyxnRUFBZ0UsQ0FBQztJQUNqRixDQUFDO0lBQUE7SUFBQTtNQUFBM0gsY0FBQSxHQUFBc0IsQ0FBQTtJQUFBO0lBRUQsTUFBTXlDLFNBQVM7SUFBQTtJQUFBLENBQUEvRCxjQUFBLEdBQUFvQixDQUFBO0lBQUc7SUFBQSxDQUFBcEIsY0FBQSxHQUFBc0IsQ0FBQSxXQUFBMEMsUUFBUSxDQUFDeUMsTUFBTSxLQUFLLENBQUM7SUFBQTtJQUFBLENBQUF6RyxjQUFBLEdBQUFzQixDQUFBLFdBQ3JDOEIsUUFBUSxJQUFJLElBQUksQ0FBQ2lDLGVBQWUsQ0FBQ0MsTUFBTSxDQUFDTyxPQUFPLEdBQUcsR0FBRztJQUFBO0lBQUEsQ0FBQTdGLGNBQUEsR0FBQXNCLENBQUEsV0FDckQ4QixRQUFRLElBQUksSUFBSSxDQUFDaUMsZUFBZSxDQUFDQyxNQUFNLENBQUNPLE9BQU8sR0FBRyxHQUFHO0lBQUM7SUFBQTdGLGNBQUEsR0FBQW9CLENBQUE7SUFFeEQsT0FBTztNQUNMMkMsU0FBUztNQUNUQyxRQUFRO01BQ1JHLGVBQWU7TUFDZkUsZUFBZTtNQUNmQztLQUNEO0VBQ0g7RUFFQTs7O0VBR1EsT0FBT1IsdUJBQXVCQSxDQUFDM0IsTUFBd0IsRUFBRXVGLE9BQVk7SUFBQTtJQUFBMUgsY0FBQSxHQUFBcUIsQ0FBQTtJQUMzRSxNQUFNd0MsZUFBZTtJQUFBO0lBQUEsQ0FBQTdELGNBQUEsR0FBQW9CLENBQUEsU0FBYSxFQUFFO0lBRXBDO0lBQUE7SUFBQXBCLGNBQUEsR0FBQW9CLENBQUE7SUFDQSxJQUFJc0csT0FBTyxDQUFDdEUsUUFBUSxHQUFHLElBQUksQ0FBQ2lDLGVBQWUsQ0FBQ0MsTUFBTSxDQUFDTyxPQUFPLEdBQUcsR0FBRyxFQUFFO01BQUE7TUFBQTdGLGNBQUEsR0FBQXNCLENBQUE7TUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7TUFDaEV5QyxlQUFlLENBQUM4RCxJQUFJLENBQUMseUVBQXlFLENBQUM7SUFDakcsQ0FBQyxNQUFNO01BQUE7TUFBQTNILGNBQUEsR0FBQXNCLENBQUE7TUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7TUFBQSxJQUFJc0csT0FBTyxDQUFDdEUsUUFBUSxHQUFHLElBQUksQ0FBQ2lDLGVBQWUsQ0FBQ0MsTUFBTSxDQUFDTyxPQUFPLEdBQUcsR0FBRyxFQUFFO1FBQUE7UUFBQTdGLGNBQUEsR0FBQXNCLENBQUE7UUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7UUFDdkV5QyxlQUFlLENBQUM4RCxJQUFJLENBQUMsNERBQTRELENBQUM7TUFDcEYsQ0FBQztNQUFBO01BQUE7UUFBQTNILGNBQUEsR0FBQXNCLENBQUE7TUFBQTtJQUFEO0lBRUE7SUFBQTtJQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtJQUNBO0lBQUk7SUFBQSxDQUFBcEIsY0FBQSxHQUFBc0IsQ0FBQSxXQUFBb0csT0FBTyxDQUFDNUMsV0FBVztJQUFBO0lBQUEsQ0FBQTlFLGNBQUEsR0FBQXNCLENBQUEsV0FBSW9HLE9BQU8sQ0FBQzVDLFdBQVcsR0FBRyxHQUFHLEdBQUU7TUFBQTtNQUFBOUUsY0FBQSxHQUFBc0IsQ0FBQTtNQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtNQUNwRHlDLGVBQWUsQ0FBQzhELElBQUksQ0FBQywyRUFBMkUsQ0FBQztJQUNuRyxDQUFDO0lBQUE7SUFBQTtNQUFBM0gsY0FBQSxHQUFBc0IsQ0FBQTtJQUFBO0lBRUQ7SUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7SUFDQTtJQUFJO0lBQUEsQ0FBQXBCLGNBQUEsR0FBQXNCLENBQUEsV0FBQWEsTUFBTSxDQUFDVSxRQUFRLEtBQUssa0JBQWtCO0lBQUE7SUFBQSxDQUFBN0MsY0FBQSxHQUFBc0IsQ0FBQSxXQUFJb0csT0FBTyxDQUFDdEUsUUFBUSxHQUFHLElBQUksR0FBRTtNQUFBO01BQUFwRCxjQUFBLEdBQUFzQixDQUFBO01BQUF0QixjQUFBLEdBQUFvQixDQUFBO01BQ3JFeUMsZUFBZSxDQUFDOEQsSUFBSSxDQUFDLGdGQUFnRixDQUFDO0lBQ3hHLENBQUM7SUFBQTtJQUFBO01BQUEzSCxjQUFBLEdBQUFzQixDQUFBO0lBQUE7SUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7SUFFRCxPQUFPeUMsZUFBZTtFQUN4QjtFQUVBOzs7RUFHUSxPQUFPeUMsdUJBQXVCQSxDQUFDdUIsTUFBYyxFQUFFQyxhQUF1QjtJQUFBO0lBQUE5SCxjQUFBLEdBQUFxQixDQUFBO0lBQUFyQixjQUFBLEdBQUFvQixDQUFBO0lBQzVFLE9BQU8wRyxhQUFhLENBQUNDLE1BQU0sQ0FBQyxDQUFDQyxJQUFJLEVBQUVDLElBQUksS0FDckM7TUFBQTtNQUFBakksY0FBQSxHQUFBcUIsQ0FBQTtNQUFBckIsY0FBQSxHQUFBb0IsQ0FBQTtNQUFBLE9BQUE2QixJQUFJLENBQUMwQyxHQUFHLENBQUNzQyxJQUFJLEdBQUdKLE1BQU0sQ0FBQyxHQUFHNUUsSUFBSSxDQUFDMEMsR0FBRyxDQUFDcUMsSUFBSSxHQUFHSCxNQUFNLENBQUM7TUFBQTtNQUFBLENBQUE3SCxjQUFBLEdBQUFzQixDQUFBLFdBQUcyRyxJQUFJO01BQUE7TUFBQSxDQUFBakksY0FBQSxHQUFBc0IsQ0FBQSxXQUFHMEcsSUFBSTtJQUFKLENBQUksQ0FDaEU7RUFDSDtFQUVBOzs7RUFHUSxPQUFPekYsaUJBQWlCQSxDQUFDSixNQUF3QjtJQUFBO0lBQUFuQyxjQUFBLEdBQUFxQixDQUFBO0lBQUFyQixjQUFBLEdBQUFvQixDQUFBO0lBQ3ZELE9BQU87TUFDTCxHQUFHZSxNQUFNO01BQ1RRLE9BQU8sRUFBRVIsTUFBTSxDQUFDUSxPQUFPLEdBQUcsS0FBSztNQUFFO01BQ2pDQyxZQUFZLEVBQUVULE1BQU0sQ0FBQ1MsWUFBWSxHQUFHLEtBQUs7TUFBRTtNQUMzQ04sS0FBSyxFQUFFO0tBQ1I7RUFDSDtFQUVBOzs7RUFHUSxPQUFPRixjQUFjQSxDQUFDRCxNQUF3QjtJQUFBO0lBQUFuQyxjQUFBLEdBQUFxQixDQUFBO0lBQUFyQixjQUFBLEdBQUFvQixDQUFBO0lBQ3BELElBQUllLE1BQU0sQ0FBQ1EsT0FBTyxJQUFJLENBQUMsRUFBRTtNQUFBO01BQUEzQyxjQUFBLEdBQUFzQixDQUFBO01BQUF0QixjQUFBLEdBQUFvQixDQUFBO01BQ3ZCLE1BQU0sSUFBSThHLEtBQUssQ0FBQyxnQ0FBZ0MsQ0FBQztJQUNuRCxDQUFDO0lBQUE7SUFBQTtNQUFBbEksY0FBQSxHQUFBc0IsQ0FBQTtJQUFBO0lBQUF0QixjQUFBLEdBQUFvQixDQUFBO0lBQ0QsSUFBSWUsTUFBTSxDQUFDUyxZQUFZLElBQUksQ0FBQyxFQUFFO01BQUE7TUFBQTVDLGNBQUEsR0FBQXNCLENBQUE7TUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7TUFDNUIsTUFBTSxJQUFJOEcsS0FBSyxDQUFDLHNDQUFzQyxDQUFDO0lBQ3pELENBQUM7SUFBQTtJQUFBO01BQUFsSSxjQUFBLEdBQUFzQixDQUFBO0lBQUE7SUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7SUFDRCxJQUFJLENBQUMsQ0FBQyxPQUFPLEVBQUUsYUFBYSxDQUFDLENBQUMrRyxRQUFRLENBQUNoRyxNQUFNLENBQUNLLFFBQVEsQ0FBQyxFQUFFO01BQUE7TUFBQXhDLGNBQUEsR0FBQXNCLENBQUE7TUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7TUFDdkQsTUFBTSxJQUFJOEcsS0FBSyxDQUFDLDRDQUE0QyxDQUFDO0lBQy9ELENBQUM7SUFBQTtJQUFBO01BQUFsSSxjQUFBLEdBQUFzQixDQUFBO0lBQUE7SUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7SUFDRCxJQUFJLENBQUMsQ0FBQyxVQUFVLEVBQUUsUUFBUSxDQUFDLENBQUMrRyxRQUFRLENBQUNoRyxNQUFNLENBQUNHLEtBQUssQ0FBQyxFQUFFO01BQUE7TUFBQXRDLGNBQUEsR0FBQXNCLENBQUE7TUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7TUFDbEQsTUFBTSxJQUFJOEcsS0FBSyxDQUFDLHNDQUFzQyxDQUFDO0lBQ3pELENBQUM7SUFBQTtJQUFBO01BQUFsSSxjQUFBLEdBQUFzQixDQUFBO0lBQUE7RUFDSDtFQUVBOzs7RUFHTyxPQUFPOEcsWUFBWUEsQ0FBQTtJQUFBO0lBQUFwSSxjQUFBLEdBQUFxQixDQUFBO0lBQUFyQixjQUFBLEdBQUFvQixDQUFBO0lBQ3hCLE9BQU87TUFBRSxHQUFHLElBQUksQ0FBQ3VGO0lBQW1CLENBQUU7RUFDeEM7RUFFQTs7O0VBR08sT0FBTzBCLGlCQUFpQkEsQ0FBQTtJQUFBO0lBQUFySSxjQUFBLEdBQUFxQixDQUFBO0lBQUFyQixjQUFBLEdBQUFvQixDQUFBO0lBQzdCLE9BQU87TUFBRSxHQUFHLElBQUksQ0FBQ2lFO0lBQWUsQ0FBRTtFQUNwQztFQUVBOzs7RUFHTyxPQUFPaUQsZ0JBQWdCQSxDQUFBO0lBQUE7SUFBQXRJLGNBQUEsR0FBQXFCLENBQUE7SUFBQXJCLGNBQUEsR0FBQW9CLENBQUE7SUFDNUIsT0FBTztNQUNMbUgsS0FBSyxFQUFFLENBQUMsR0FBRyxJQUFJLENBQUNyRCxvQkFBb0IsQ0FBQztNQUNyQ3NELFdBQVcsRUFBRSxDQUFDLEdBQUcsSUFBSSxDQUFDeEMsMEJBQTBCO0tBQ2pEO0VBQ0g7Ozs7QUF2Y0Z5QyxPQUFBLENBQUF4RyxpQkFBQSxHQUFBQSxpQkFBQTtBQUNFO0FBQUE7QUFBQWpDLGNBQUEsR0FBQW9CLENBQUE7QUFDd0JhLGlCQUFBLENBQUFpRCxvQkFBb0IsR0FBRyxDQUM3QyxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQ3ZELEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsQ0FDdkM7QUFFRDtBQUFBO0FBQUFsRixjQUFBLEdBQUFvQixDQUFBO0FBQ3dCYSxpQkFBQSxDQUFBK0QsMEJBQTBCLEdBQUcsQ0FDbkQsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFDaEQsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsQ0FDM0M7QUFFRDtBQUFBO0FBQUFoRyxjQUFBLEdBQUFvQixDQUFBO0FBQ3dCYSxpQkFBQSxDQUFBMEUsbUJBQW1CLEdBQXVDO0VBQ2hGQyxnQkFBZ0IsRUFBRTtJQUFFRSxlQUFlLEVBQUUsTUFBTTtJQUFFakcsSUFBSSxFQUFFLGtCQUFrQjtJQUFFNkgsV0FBVyxFQUFFO0VBQXdCLENBQUU7RUFDOUdDLFFBQVEsRUFBRTtJQUFFN0IsZUFBZSxFQUFFLE1BQU07SUFBRWpHLElBQUksRUFBRSxVQUFVO0lBQUU2SCxXQUFXLEVBQUU7RUFBc0IsQ0FBRTtFQUM1RkUsZUFBZSxFQUFFO0lBQUU5QixlQUFlLEVBQUUsTUFBTTtJQUFFakcsSUFBSSxFQUFFLGlCQUFpQjtJQUFFNkgsV0FBVyxFQUFFO0VBQXFCLENBQUU7RUFDekdHLEdBQUcsRUFBRTtJQUFFL0IsZUFBZSxFQUFFLE1BQU07SUFBRWpHLElBQUksRUFBRSxLQUFLO0lBQUU2SCxXQUFXLEVBQUU7RUFBa0IsQ0FBRTtFQUM5RUksVUFBVSxFQUFFO0lBQUVoQyxlQUFlLEVBQUUsTUFBTTtJQUFFakcsSUFBSSxFQUFFLFlBQVk7SUFBRTZILFdBQVcsRUFBRTtFQUFvQixDQUFFO0VBQzlGSyxRQUFRLEVBQUU7SUFBRWpDLGVBQWUsRUFBRSxLQUFLO0lBQUVqRyxJQUFJLEVBQUUsVUFBVTtJQUFFNkgsV0FBVyxFQUFFO0VBQW1CLENBQUU7RUFDeEZNLEtBQUssRUFBRTtJQUFFbEMsZUFBZSxFQUFFLElBQUk7SUFBRWpHLElBQUksRUFBRSxPQUFPO0lBQUU2SCxXQUFXLEVBQUU7RUFBZTtDQUM1RTtBQUVEO0FBQUE7QUFBQTFJLGNBQUEsR0FBQW9CLENBQUE7QUFDd0JhLGlCQUFBLENBQUFvRCxlQUFlLEdBQUc7RUFDeENDLE1BQU0sRUFBRTtJQUFFQyxHQUFHLEVBQUUsR0FBRztJQUFFQyxHQUFHLEVBQUUsSUFBSTtJQUFFSyxPQUFPLEVBQUU7RUFBSSxDQUFFO0VBQzlDb0QsTUFBTSxFQUFFO0lBQUUxRCxHQUFHLEVBQUUsR0FBRztJQUFFQyxHQUFHLEVBQUUsSUFBSTtJQUFFSyxPQUFPLEVBQUU7RUFBSSxDQUFFO0VBQzlDcUQsT0FBTyxFQUFFO0lBQUUzRCxHQUFHLEVBQUUsR0FBRztJQUFFQyxHQUFHLEVBQUUsSUFBSTtJQUFFSyxPQUFPLEVBQUU7RUFBSTtDQUM5QztBQUFDO0FBQUE3RixjQUFBLEdBQUFvQixDQUFBO0FBNmFKcUgsT0FBQSxDQUFBVSxPQUFBLEdBQWVsSCxpQkFBaUIiLCJpZ25vcmVMaXN0IjpbXX0=