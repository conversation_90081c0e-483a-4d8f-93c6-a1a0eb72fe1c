{"version": 3, "names": ["cov_2g903bnq0k", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "name", "decl", "loc", "branchMap", "type", "locations", "undefined", "s", "f", "b", "inputSourceMap", "file", "mappings", "names", "sources", "sourcesContent", "version", "_coverageSchema", "coverage", "actualCoverage", "AirDuctCalculator", "calculateDuctSizing", "inputs", "validateInputs", "imperialInputs", "units", "convertToImperial", "ductType", "calculateRoundDuct", "calculateRectangularDuct", "airflow", "frictionRate", "material", "diameter", "findOptimalRoundDiameter", "area", "Math", "PI", "pow", "velocity", "pressureLoss", "calculatePressureLoss", "reynoldsNumber", "calculateReynoldsNumber", "frictionFactor", "calculateFrictionFactor", "validation", "validateResults", "recommendations", "generateRecommendations", "isOptimal", "warnings", "standardsCompliance", "smacna", "smacnaCompliant", "ashrae", "ashraeCompliant", "velocityCompliant", "width", "height", "findOptimalRectangularDimensions", "equivalentDiameter", "calculateEquivalentDiameter", "hydraulicDiameter", "calculateHydraulicDiameter", "aspectRatio", "calculateAspectRatio", "targetFriction", "bestDiameter", "ROUND_STANDARD_SIZES", "bestScore", "Infinity", "VELOCITY_LIMITS", "supply", "min", "max", "actualFriction", "frictionScore", "abs", "velocityScore", "optimal", "totalScore", "bestWidth", "RECTANGULAR_STANDARD_SIZES", "bestHeight", "aspectRatios", "estimatedArea", "sqrt", "heightStd", "findNearestStandardSize", "widthStd", "aspectScore", "length", "materialProps", "MATERIAL_PROPERTIES", "galvanized_steel", "roughness", "roughnessFactor", "velocityFps", "diameterFt", "airDensity", "kinematicViscosity", "reynolds", "pressureLossPsf", "pressureLossInWg", "relativeRoughness", "i", "fNew", "log10", "results", "push", "toFixed", "target", "standardSizes", "reduce", "prev", "curr", "Error", "includes", "getMaterials", "getVelocityLimits", "getStandardSizes", "round", "rectangular", "exports", "description", "aluminum", "stainless_steel", "pvc", "fiberglass", "concrete", "brick", "return", "exhaust", "default"], "sources": ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\AirDuctCalculator.ts"], "sourcesContent": ["/**\r\n * AirDuctCalculator - Pure Calculation Functions for Air Duct Sizing\r\n * \r\n * MISSION-CRITICAL: Pure TypeScript functions for SMACNA-compliant air duct calculations\r\n * Extracted from UI components for reusability and tier enforcement integration\r\n * \r\n * @see docs/implementation/tier-system/tier-boundaries-specification.md\r\n * @see docs/developer-guide/tier-implementation-checklist.md section 2.4\r\n */\r\n\r\n/**\r\n * Input parameters for duct sizing calculations\r\n */\r\nexport interface DuctSizingInputs {\r\n  airflow: number; // CFM\r\n  ductType: 'round' | 'rectangular';\r\n  frictionRate: number; // inches w.g. per 100 feet\r\n  units: 'imperial' | 'metric';\r\n  material?: string;\r\n  targetVelocity?: number; // FPM\r\n  maxVelocity?: number; // FPM\r\n  minVelocity?: number; // FPM\r\n}\r\n\r\n/**\r\n * Results from duct sizing calculations\r\n */\r\nexport interface DuctSizingResults {\r\n  // Common properties\r\n  area: number; // sq ft\r\n  velocity: number; // FPM\r\n  pressureLoss: number; // inches w.g. per 100 feet\r\n  reynoldsNumber: number;\r\n  frictionFactor: number;\r\n  \r\n  // Round duct specific\r\n  diameter?: number; // inches\r\n  \r\n  // Rectangular duct specific\r\n  width?: number; // inches\r\n  height?: number; // inches\r\n  equivalentDiameter?: number; // inches\r\n  hydraulicDiameter?: number; // inches\r\n  aspectRatio?: number;\r\n  \r\n  // Validation and recommendations\r\n  isOptimal: boolean;\r\n  warnings: string[];\r\n  recommendations: string[];\r\n  standardsCompliance: {\r\n    smacna: boolean;\r\n    ashrae: boolean;\r\n    velocityCompliant: boolean;\r\n  };\r\n}\r\n\r\n/**\r\n * Material properties for pressure loss calculations\r\n */\r\nexport interface MaterialProperties {\r\n  roughnessFactor: number; // feet\r\n  name: string;\r\n  description: string;\r\n}\r\n\r\n/**\r\n * AirDuctCalculator - Pure calculation functions for air duct sizing\r\n * CRITICAL: No dependencies on UI, storage, or external services\r\n */\r\nexport class AirDuctCalculator {\r\n  // SMACNA standard round duct sizes (inches)\r\n  private static readonly ROUND_STANDARD_SIZES = [\r\n    3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24,\r\n    26, 28, 30, 32, 36, 40, 42, 48, 54, 60\r\n  ];\r\n\r\n  // SMACNA standard rectangular duct sizes (inches)\r\n  private static readonly RECTANGULAR_STANDARD_SIZES = [\r\n    4, 5, 6, 7, 8, 9, 10, 12, 14, 16, 18, 20, 22, 24,\r\n    26, 28, 30, 32, 36, 40, 42, 48, 54, 60, 72\r\n  ];\r\n\r\n  // Material roughness factors (feet)\r\n  private static readonly MATERIAL_PROPERTIES: Record<string, MaterialProperties> = {\r\n    galvanized_steel: { roughnessFactor: 0.0003, name: 'Galvanized Steel', description: 'Standard HVAC ductwork' },\r\n    aluminum: { roughnessFactor: 0.0002, name: 'Aluminum', description: 'Lightweight ductwork' },\r\n    stainless_steel: { roughnessFactor: 0.0002, name: 'Stainless Steel', description: 'Corrosion resistant' },\r\n    pvc: { roughnessFactor: 0.0001, name: 'PVC', description: 'Plastic ductwork' },\r\n    fiberglass: { roughnessFactor: 0.0005, name: 'Fiberglass', description: 'Insulated ductwork' },\r\n    concrete: { roughnessFactor: 0.003, name: 'Concrete', description: 'Underground ducts' },\r\n    brick: { roughnessFactor: 0.01, name: 'Brick', description: 'Masonry ducts' }\r\n  };\r\n\r\n  // SMACNA velocity limits (FPM)\r\n  private static readonly VELOCITY_LIMITS = {\r\n    supply: { min: 400, max: 2500, optimal: 1500 },\r\n    return: { min: 300, max: 2000, optimal: 1200 },\r\n    exhaust: { min: 500, max: 3000, optimal: 1800 }\r\n  };\r\n\r\n  /**\r\n   * Calculate air duct sizing based on SMACNA standards\r\n   * CRITICAL: Pure function with no side effects\r\n   */\r\n  public static calculateDuctSizing(inputs: DuctSizingInputs): DuctSizingResults {\r\n    // Validate inputs\r\n    this.validateInputs(inputs);\r\n\r\n    // Convert to imperial units if needed\r\n    const imperialInputs = inputs.units === 'metric' \r\n      ? this.convertToImperial(inputs) \r\n      : inputs;\r\n\r\n    // Perform calculation based on duct type\r\n    if (imperialInputs.ductType === 'round') {\r\n      return this.calculateRoundDuct(imperialInputs);\r\n    } else {\r\n      return this.calculateRectangularDuct(imperialInputs);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Calculate round duct sizing\r\n   */\r\n  private static calculateRoundDuct(inputs: DuctSizingInputs): DuctSizingResults {\r\n    const { airflow, frictionRate, material = 'galvanized_steel' } = inputs;\r\n\r\n    // Find optimal diameter using SMACNA friction chart method\r\n    const diameter = this.findOptimalRoundDiameter(airflow, frictionRate);\r\n\r\n    // Calculate area and velocity\r\n    const area = Math.PI * Math.pow(diameter / 12, 2) / 4; // sq ft\r\n    const velocity = airflow / area; // FPM\r\n\r\n    // Calculate pressure loss\r\n    const pressureLoss = this.calculatePressureLoss(velocity, 100, diameter, material);\r\n\r\n    // Calculate Reynolds number and friction factor\r\n    const reynoldsNumber = this.calculateReynoldsNumber(velocity, diameter);\r\n    const frictionFactor = this.calculateFrictionFactor(reynoldsNumber, material, diameter);\r\n\r\n    // Validate and generate recommendations\r\n    const validation = this.validateResults({ velocity, diameter, area });\r\n    const recommendations = this.generateRecommendations(inputs, { diameter, velocity, area });\r\n\r\n    return {\r\n      diameter,\r\n      area,\r\n      velocity,\r\n      pressureLoss,\r\n      reynoldsNumber,\r\n      frictionFactor,\r\n      isOptimal: validation.isOptimal,\r\n      warnings: validation.warnings,\r\n      recommendations,\r\n      standardsCompliance: {\r\n        smacna: validation.smacnaCompliant,\r\n        ashrae: validation.ashraeCompliant,\r\n        velocityCompliant: validation.velocityCompliant\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate rectangular duct sizing\r\n   */\r\n  private static calculateRectangularDuct(inputs: DuctSizingInputs): DuctSizingResults {\r\n    const { airflow, frictionRate, material = 'galvanized_steel' } = inputs;\r\n\r\n    // Find optimal dimensions\r\n    const { width, height } = this.findOptimalRectangularDimensions(airflow, frictionRate);\r\n\r\n    // Calculate area and velocity\r\n    const area = (width * height) / 144; // sq ft\r\n    const velocity = airflow / area; // FPM\r\n\r\n    // Calculate equivalent and hydraulic diameters\r\n    const equivalentDiameter = this.calculateEquivalentDiameter(width, height);\r\n    const hydraulicDiameter = this.calculateHydraulicDiameter(width, height);\r\n    const aspectRatio = this.calculateAspectRatio(width, height);\r\n\r\n    // Calculate pressure loss using equivalent diameter\r\n    const pressureLoss = this.calculatePressureLoss(velocity, 100, equivalentDiameter, material);\r\n\r\n    // Calculate Reynolds number and friction factor\r\n    const reynoldsNumber = this.calculateReynoldsNumber(velocity, hydraulicDiameter);\r\n    const frictionFactor = this.calculateFrictionFactor(reynoldsNumber, material, hydraulicDiameter);\r\n\r\n    // Validate and generate recommendations\r\n    const validation = this.validateResults({ velocity, width, height, aspectRatio, area });\r\n    const recommendations = this.generateRecommendations(inputs, { width, height, velocity, area, aspectRatio });\r\n\r\n    return {\r\n      width,\r\n      height,\r\n      area,\r\n      velocity,\r\n      pressureLoss,\r\n      reynoldsNumber,\r\n      frictionFactor,\r\n      equivalentDiameter,\r\n      hydraulicDiameter,\r\n      aspectRatio,\r\n      isOptimal: validation.isOptimal,\r\n      warnings: validation.warnings,\r\n      recommendations,\r\n      standardsCompliance: {\r\n        smacna: validation.smacnaCompliant,\r\n        ashrae: validation.ashraeCompliant,\r\n        velocityCompliant: validation.velocityCompliant\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Find optimal round duct diameter using SMACNA friction chart method\r\n   */\r\n  private static findOptimalRoundDiameter(airflow: number, targetFriction: number): number {\r\n    let bestDiameter = this.ROUND_STANDARD_SIZES[0];\r\n    let bestScore = Infinity;\r\n\r\n    for (const diameter of this.ROUND_STANDARD_SIZES) {\r\n      const area = Math.PI * Math.pow(diameter / 12, 2) / 4; // sq ft\r\n      const velocity = airflow / area; // FPM\r\n\r\n      // Check velocity limits\r\n      if (velocity < this.VELOCITY_LIMITS.supply.min || velocity > this.VELOCITY_LIMITS.supply.max) {\r\n        continue;\r\n      }\r\n\r\n      // Calculate actual friction rate\r\n      const actualFriction = this.calculatePressureLoss(velocity, 100, diameter, 'galvanized_steel');\r\n      \r\n      // Score based on how close to target friction and optimal velocity\r\n      const frictionScore = Math.abs(actualFriction - targetFriction) / targetFriction;\r\n      const velocityScore = Math.abs(velocity - this.VELOCITY_LIMITS.supply.optimal) / this.VELOCITY_LIMITS.supply.optimal;\r\n      const totalScore = frictionScore + velocityScore * 0.5; // Weight friction more heavily\r\n\r\n      if (totalScore < bestScore) {\r\n        bestScore = totalScore;\r\n        bestDiameter = diameter;\r\n      }\r\n    }\r\n\r\n    return bestDiameter;\r\n  }\r\n\r\n  /**\r\n   * Find optimal rectangular duct dimensions\r\n   */\r\n  private static findOptimalRectangularDimensions(airflow: number, targetFriction: number): { width: number; height: number } {\r\n    let bestWidth = this.RECTANGULAR_STANDARD_SIZES[0];\r\n    let bestHeight = this.RECTANGULAR_STANDARD_SIZES[0];\r\n    let bestScore = Infinity;\r\n\r\n    // Try different aspect ratios (SMACNA recommends 1:1 to 4:1)\r\n    const aspectRatios = [1.0, 1.5, 2.0, 2.5, 3.0, 3.5, 4.0];\r\n\r\n    for (const aspectRatio of aspectRatios) {\r\n      // Calculate estimated dimensions\r\n      const estimatedArea = airflow / this.VELOCITY_LIMITS.supply.optimal; // Target optimal velocity\r\n      const height = Math.sqrt(estimatedArea / aspectRatio) * 12; // inches\r\n      const width = aspectRatio * height;\r\n\r\n      // Round to nearest standard sizes\r\n      const heightStd = this.findNearestStandardSize(height, this.RECTANGULAR_STANDARD_SIZES);\r\n      const widthStd = this.findNearestStandardSize(width, this.RECTANGULAR_STANDARD_SIZES);\r\n\r\n      // Calculate actual properties\r\n      const area = (widthStd * heightStd) / 144; // sq ft\r\n      const velocity = airflow / area; // FPM\r\n\r\n      // Check velocity limits\r\n      if (velocity < this.VELOCITY_LIMITS.supply.min || velocity > this.VELOCITY_LIMITS.supply.max) {\r\n        continue;\r\n      }\r\n\r\n      // Calculate equivalent diameter and friction\r\n      const equivalentDiameter = this.calculateEquivalentDiameter(widthStd, heightStd);\r\n      const actualFriction = this.calculatePressureLoss(velocity, 100, equivalentDiameter, 'galvanized_steel');\r\n\r\n      // Score based on friction accuracy, velocity optimality, and aspect ratio\r\n      const frictionScore = Math.abs(actualFriction - targetFriction) / targetFriction;\r\n      const velocityScore = Math.abs(velocity - this.VELOCITY_LIMITS.supply.optimal) / this.VELOCITY_LIMITS.supply.optimal;\r\n      const aspectScore = aspectRatio > 3.0 ? (aspectRatio - 3.0) * 0.2 : 0; // Penalty for high aspect ratios\r\n      const totalScore = frictionScore + velocityScore * 0.5 + aspectScore;\r\n\r\n      if (totalScore < bestScore) {\r\n        bestScore = totalScore;\r\n        bestWidth = widthStd;\r\n        bestHeight = heightStd;\r\n      }\r\n    }\r\n\r\n    return { width: bestWidth, height: bestHeight };\r\n  }\r\n\r\n  /**\r\n   * Calculate equivalent diameter for rectangular ducts (SMACNA formula)\r\n   */\r\n  public static calculateEquivalentDiameter(width: number, height: number): number {\r\n    return 1.3 * Math.pow(width * height, 0.625) / Math.pow(width + height, 0.25);\r\n  }\r\n\r\n  /**\r\n   * Calculate hydraulic diameter for rectangular ducts\r\n   */\r\n  public static calculateHydraulicDiameter(width: number, height: number): number {\r\n    return (4 * width * height) / (2 * (width + height));\r\n  }\r\n\r\n  /**\r\n   * Calculate aspect ratio for rectangular ducts\r\n   */\r\n  public static calculateAspectRatio(width: number, height: number): number {\r\n    return Math.max(width, height) / Math.min(width, height);\r\n  }\r\n\r\n  /**\r\n   * Calculate pressure loss using Darcy-Weisbach equation\r\n   */\r\n  public static calculatePressureLoss(velocity: number, length: number, diameter: number, material: string): number {\r\n    const materialProps = this.MATERIAL_PROPERTIES[material] || this.MATERIAL_PROPERTIES.galvanized_steel;\r\n    const roughness = materialProps.roughnessFactor;\r\n\r\n    // Convert units\r\n    const velocityFps = velocity / 60; // FPM to FPS\r\n    const diameterFt = diameter / 12; // inches to feet\r\n\r\n    // Air properties at standard conditions (70°F, 14.7 psia)\r\n    const airDensity = 0.075; // lb/ft³\r\n    const kinematicViscosity = 1.57e-4; // ft²/s\r\n\r\n    // Calculate Reynolds number\r\n    const reynolds = (velocityFps * diameterFt) / kinematicViscosity;\r\n\r\n    // Calculate friction factor\r\n    const frictionFactor = this.calculateFrictionFactor(reynolds, material, diameter);\r\n\r\n    // Darcy-Weisbach equation: ΔP = f * (L/D) * (ρ * V²) / (2 * gc)\r\n    // Convert to inches of water per 100 feet\r\n    const pressureLossPsf = frictionFactor * (length / diameterFt) * (airDensity * Math.pow(velocityFps, 2)) / (2 * 32.174);\r\n    const pressureLossInWg = pressureLossPsf / 5.2; // Convert psf to inches w.g.\r\n\r\n    return pressureLossInWg;\r\n  }\r\n\r\n  /**\r\n   * Calculate Reynolds number\r\n   */\r\n  public static calculateReynoldsNumber(velocity: number, diameter: number): number {\r\n    const velocityFps = velocity / 60; // FPM to FPS\r\n    const diameterFt = diameter / 12; // inches to feet\r\n    const kinematicViscosity = 1.57e-4; // ft²/s for air at standard conditions\r\n\r\n    return (velocityFps * diameterFt) / kinematicViscosity;\r\n  }\r\n\r\n  /**\r\n   * Calculate friction factor using Colebrook-White equation\r\n   */\r\n  public static calculateFrictionFactor(reynolds: number, material: string, diameter: number): number {\r\n    const materialProps = this.MATERIAL_PROPERTIES[material] || this.MATERIAL_PROPERTIES.galvanized_steel;\r\n    const roughness = materialProps.roughnessFactor;\r\n    const diameterFt = diameter / 12;\r\n    const relativeRoughness = roughness / diameterFt;\r\n\r\n    // For laminar flow (Re < 2300)\r\n    if (reynolds < 2300) {\r\n      return 64 / reynolds;\r\n    }\r\n\r\n    // For turbulent flow, use Colebrook-White equation (iterative solution)\r\n    let f = 0.02; // Initial guess\r\n    for (let i = 0; i < 10; i++) {\r\n      const fNew = 1 / Math.pow(-2 * Math.log10(relativeRoughness / 3.7 + 2.51 / (reynolds * Math.sqrt(f))), 2);\r\n      if (Math.abs(fNew - f) < 0.0001) break;\r\n      f = fNew;\r\n    }\r\n\r\n    return f;\r\n  }\r\n\r\n  /**\r\n   * Validate calculation results\r\n   */\r\n  private static validateResults(results: any): {\r\n    isOptimal: boolean;\r\n    warnings: string[];\r\n    smacnaCompliant: boolean;\r\n    ashraeCompliant: boolean;\r\n    velocityCompliant: boolean;\r\n  } {\r\n    const warnings: string[] = [];\r\n    let smacnaCompliant = true;\r\n    let ashraeCompliant = true;\r\n    let velocityCompliant = true;\r\n\r\n    // Velocity validation\r\n    const { velocity } = results;\r\n    if (velocity < this.VELOCITY_LIMITS.supply.min) {\r\n      warnings.push(`Velocity ${velocity.toFixed(0)} FPM is below minimum recommended (${this.VELOCITY_LIMITS.supply.min} FPM)`);\r\n      velocityCompliant = false;\r\n    } else if (velocity > this.VELOCITY_LIMITS.supply.max) {\r\n      warnings.push(`Velocity ${velocity.toFixed(0)} FPM exceeds maximum recommended (${this.VELOCITY_LIMITS.supply.max} FPM)`);\r\n      velocityCompliant = false;\r\n      smacnaCompliant = false;\r\n    }\r\n\r\n    // Aspect ratio validation (for rectangular ducts)\r\n    if (results.aspectRatio) {\r\n      if (results.aspectRatio > 4.0) {\r\n        warnings.push(`Aspect ratio ${results.aspectRatio.toFixed(1)}:1 exceeds SMACNA maximum of 4:1`);\r\n        smacnaCompliant = false;\r\n      } else if (results.aspectRatio > 3.0) {\r\n        warnings.push(`Aspect ratio ${results.aspectRatio.toFixed(1)}:1 is high - consider optimization`);\r\n      }\r\n    }\r\n\r\n    // Area validation\r\n    if (results.area < 0.1) {\r\n      warnings.push('Very small duct area. Consider minimum duct size requirements.');\r\n    }\r\n\r\n    const isOptimal = warnings.length === 0 && \r\n      velocity >= this.VELOCITY_LIMITS.supply.optimal * 0.8 && \r\n      velocity <= this.VELOCITY_LIMITS.supply.optimal * 1.2;\r\n\r\n    return {\r\n      isOptimal,\r\n      warnings,\r\n      smacnaCompliant,\r\n      ashraeCompliant,\r\n      velocityCompliant\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Generate recommendations based on calculation results\r\n   */\r\n  private static generateRecommendations(inputs: DuctSizingInputs, results: any): string[] {\r\n    const recommendations: string[] = [];\r\n\r\n    // Velocity recommendations\r\n    if (results.velocity < this.VELOCITY_LIMITS.supply.optimal * 0.8) {\r\n      recommendations.push('Consider reducing duct size to increase velocity for better performance');\r\n    } else if (results.velocity > this.VELOCITY_LIMITS.supply.optimal * 1.2) {\r\n      recommendations.push('Consider increasing duct size to reduce velocity and noise');\r\n    }\r\n\r\n    // Aspect ratio recommendations (for rectangular ducts)\r\n    if (results.aspectRatio && results.aspectRatio > 3.0) {\r\n      recommendations.push('Consider using round duct or reducing aspect ratio for better performance');\r\n    }\r\n\r\n    // Material recommendations\r\n    if (inputs.material === 'galvanized_steel' && results.velocity > 2000) {\r\n      recommendations.push('Consider using smoother materials like aluminum for high-velocity applications');\r\n    }\r\n\r\n    return recommendations;\r\n  }\r\n\r\n  /**\r\n   * Find nearest standard size\r\n   */\r\n  private static findNearestStandardSize(target: number, standardSizes: number[]): number {\r\n    return standardSizes.reduce((prev, curr) => \r\n      Math.abs(curr - target) < Math.abs(prev - target) ? curr : prev\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Convert metric inputs to imperial\r\n   */\r\n  private static convertToImperial(inputs: DuctSizingInputs): DuctSizingInputs {\r\n    return {\r\n      ...inputs,\r\n      airflow: inputs.airflow * 2.119, // m³/h to CFM\r\n      frictionRate: inputs.frictionRate * 0.249, // Pa/m to inches w.g. per 100 feet\r\n      units: 'imperial'\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Validate input parameters\r\n   */\r\n  private static validateInputs(inputs: DuctSizingInputs): void {\r\n    if (inputs.airflow <= 0) {\r\n      throw new Error('Airflow must be greater than 0');\r\n    }\r\n    if (inputs.frictionRate <= 0) {\r\n      throw new Error('Friction rate must be greater than 0');\r\n    }\r\n    if (!['round', 'rectangular'].includes(inputs.ductType)) {\r\n      throw new Error('Duct type must be \"round\" or \"rectangular\"');\r\n    }\r\n    if (!['imperial', 'metric'].includes(inputs.units)) {\r\n      throw new Error('Units must be \"imperial\" or \"metric\"');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get available materials\r\n   */\r\n  public static getMaterials(): Record<string, MaterialProperties> {\r\n    return { ...this.MATERIAL_PROPERTIES };\r\n  }\r\n\r\n  /**\r\n   * Get velocity limits\r\n   */\r\n  public static getVelocityLimits(): typeof AirDuctCalculator.VELOCITY_LIMITS {\r\n    return { ...this.VELOCITY_LIMITS };\r\n  }\r\n\r\n  /**\r\n   * Get standard sizes\r\n   */\r\n  public static getStandardSizes(): { round: number[]; rectangular: number[] } {\r\n    return {\r\n      round: [...this.ROUND_STANDARD_SIZES],\r\n      rectangular: [...this.RECTANGULAR_STANDARD_SIZES]\r\n    };\r\n  }\r\n}\r\n\r\nexport default AirDuctCalculator;\r\n"], "mappings": ";;AAAA;;;;;;;;;AAAA;AAAA,SAAAA,eAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;MAAA;QAAAC,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAO,SAAA;MAAA;QAAAD,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;IAAA;IAAAW,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,cAAA;MAAAC,IAAA;MAAAC,QAAA;MAAAC,KAAA;MAAAC,OAAA;MAAAC,cAAA;MAAAC,OAAA;IAAA;IAAAC,eAAA;IAAA5B,IAAA;EAAA;EAAA,IAAA6B,QAAA,GAAA5B,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAA0B,QAAA,CAAA9B,IAAA,KAAA8B,QAAA,CAAA9B,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAA6B,QAAA,CAAA9B,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAA0B,cAAA,GAAAD,QAAA,CAAA9B,IAAA;EAAA;IAoEG;IAAAD,cAAA,YAAAA,CAAA;MAAA,OAAAgC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAhC,cAAA;AAAAA,cAAA,GAAAoB,CAAA;;;;;;;AAHH;;;;AAIA,MAAaa,iBAAiB;EA+B5B;;;;EAIO,OAAOC,mBAAmBA,CAACC,MAAwB;IAAA;IAAAnC,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACxD;IACA,IAAI,CAACgB,cAAc,CAACD,MAAM,CAAC;IAE3B;IACA,MAAME,cAAc;IAAA;IAAA,CAAArC,cAAA,GAAAoB,CAAA,OAAGe,MAAM,CAACG,KAAK,KAAK,QAAQ;IAAA;IAAA,CAAAtC,cAAA,GAAAsB,CAAA,UAC5C,IAAI,CAACiB,iBAAiB,CAACJ,MAAM,CAAC;IAAA;IAAA,CAAAnC,cAAA,GAAAsB,CAAA,UAC9Ba,MAAM;IAEV;IAAA;IAAAnC,cAAA,GAAAoB,CAAA;IACA,IAAIiB,cAAc,CAACG,QAAQ,KAAK,OAAO,EAAE;MAAA;MAAAxC,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACvC,OAAO,IAAI,CAACqB,kBAAkB,CAACJ,cAAc,CAAC;IAChD,CAAC,MAAM;MAAA;MAAArC,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACL,OAAO,IAAI,CAACsB,wBAAwB,CAACL,cAAc,CAAC;IACtD;EACF;EAEA;;;EAGQ,OAAOI,kBAAkBA,CAACN,MAAwB;IAAA;IAAAnC,cAAA,GAAAqB,CAAA;IACxD,MAAM;MAAEsB,OAAO;MAAEC,YAAY;MAAEC,QAAQ;MAAA;MAAA,CAAA7C,cAAA,GAAAsB,CAAA,UAAG,kBAAkB;IAAA,CAAE;IAAA;IAAA,CAAAtB,cAAA,GAAAoB,CAAA,OAAGe,MAAM;IAEvE;IACA,MAAMW,QAAQ;IAAA;IAAA,CAAA9C,cAAA,GAAAoB,CAAA,OAAG,IAAI,CAAC2B,wBAAwB,CAACJ,OAAO,EAAEC,YAAY,CAAC;IAErE;IACA,MAAMI,IAAI;IAAA;IAAA,CAAAhD,cAAA,GAAAoB,CAAA,OAAG6B,IAAI,CAACC,EAAE,GAAGD,IAAI,CAACE,GAAG,CAACL,QAAQ,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAC,CAAC;IACvD,MAAMM,QAAQ;IAAA;IAAA,CAAApD,cAAA,GAAAoB,CAAA,QAAGuB,OAAO,GAAGK,IAAI,EAAC,CAAC;IAEjC;IACA,MAAMK,YAAY;IAAA;IAAA,CAAArD,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACkC,qBAAqB,CAACF,QAAQ,EAAE,GAAG,EAAEN,QAAQ,EAAED,QAAQ,CAAC;IAElF;IACA,MAAMU,cAAc;IAAA;IAAA,CAAAvD,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACoC,uBAAuB,CAACJ,QAAQ,EAAEN,QAAQ,CAAC;IACvE,MAAMW,cAAc;IAAA;IAAA,CAAAzD,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACsC,uBAAuB,CAACH,cAAc,EAAEV,QAAQ,EAAEC,QAAQ,CAAC;IAEvF;IACA,MAAMa,UAAU;IAAA;IAAA,CAAA3D,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACwC,eAAe,CAAC;MAAER,QAAQ;MAAEN,QAAQ;MAAEE;IAAI,CAAE,CAAC;IACrE,MAAMa,eAAe;IAAA;IAAA,CAAA7D,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAAC0C,uBAAuB,CAAC3B,MAAM,EAAE;MAAEW,QAAQ;MAAEM,QAAQ;MAAEJ;IAAI,CAAE,CAAC;IAAC;IAAAhD,cAAA,GAAAoB,CAAA;IAE3F,OAAO;MACL0B,QAAQ;MACRE,IAAI;MACJI,QAAQ;MACRC,YAAY;MACZE,cAAc;MACdE,cAAc;MACdM,SAAS,EAAEJ,UAAU,CAACI,SAAS;MAC/BC,QAAQ,EAAEL,UAAU,CAACK,QAAQ;MAC7BH,eAAe;MACfI,mBAAmB,EAAE;QACnBC,MAAM,EAAEP,UAAU,CAACQ,eAAe;QAClCC,MAAM,EAAET,UAAU,CAACU,eAAe;QAClCC,iBAAiB,EAAEX,UAAU,CAACW;;KAEjC;EACH;EAEA;;;EAGQ,OAAO5B,wBAAwBA,CAACP,MAAwB;IAAA;IAAAnC,cAAA,GAAAqB,CAAA;IAC9D,MAAM;MAAEsB,OAAO;MAAEC,YAAY;MAAEC,QAAQ;MAAA;MAAA,CAAA7C,cAAA,GAAAsB,CAAA,UAAG,kBAAkB;IAAA,CAAE;IAAA;IAAA,CAAAtB,cAAA,GAAAoB,CAAA,QAAGe,MAAM;IAEvE;IACA,MAAM;MAAEoC,KAAK;MAAEC;IAAM,CAAE;IAAA;IAAA,CAAAxE,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACqD,gCAAgC,CAAC9B,OAAO,EAAEC,YAAY,CAAC;IAEtF;IACA,MAAMI,IAAI;IAAA;IAAA,CAAAhD,cAAA,GAAAoB,CAAA,QAAImD,KAAK,GAAGC,MAAM,GAAI,GAAG,EAAC,CAAC;IACrC,MAAMpB,QAAQ;IAAA;IAAA,CAAApD,cAAA,GAAAoB,CAAA,QAAGuB,OAAO,GAAGK,IAAI,EAAC,CAAC;IAEjC;IACA,MAAM0B,kBAAkB;IAAA;IAAA,CAAA1E,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACuD,2BAA2B,CAACJ,KAAK,EAAEC,MAAM,CAAC;IAC1E,MAAMI,iBAAiB;IAAA;IAAA,CAAA5E,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACyD,0BAA0B,CAACN,KAAK,EAAEC,MAAM,CAAC;IACxE,MAAMM,WAAW;IAAA;IAAA,CAAA9E,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAAC2D,oBAAoB,CAACR,KAAK,EAAEC,MAAM,CAAC;IAE5D;IACA,MAAMnB,YAAY;IAAA;IAAA,CAAArD,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACkC,qBAAqB,CAACF,QAAQ,EAAE,GAAG,EAAEsB,kBAAkB,EAAE7B,QAAQ,CAAC;IAE5F;IACA,MAAMU,cAAc;IAAA;IAAA,CAAAvD,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACoC,uBAAuB,CAACJ,QAAQ,EAAEwB,iBAAiB,CAAC;IAChF,MAAMnB,cAAc;IAAA;IAAA,CAAAzD,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACsC,uBAAuB,CAACH,cAAc,EAAEV,QAAQ,EAAE+B,iBAAiB,CAAC;IAEhG;IACA,MAAMjB,UAAU;IAAA;IAAA,CAAA3D,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACwC,eAAe,CAAC;MAAER,QAAQ;MAAEmB,KAAK;MAAEC,MAAM;MAAEM,WAAW;MAAE9B;IAAI,CAAE,CAAC;IACvF,MAAMa,eAAe;IAAA;IAAA,CAAA7D,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAAC0C,uBAAuB,CAAC3B,MAAM,EAAE;MAAEoC,KAAK;MAAEC,MAAM;MAAEpB,QAAQ;MAAEJ,IAAI;MAAE8B;IAAW,CAAE,CAAC;IAAC;IAAA9E,cAAA,GAAAoB,CAAA;IAE7G,OAAO;MACLmD,KAAK;MACLC,MAAM;MACNxB,IAAI;MACJI,QAAQ;MACRC,YAAY;MACZE,cAAc;MACdE,cAAc;MACdiB,kBAAkB;MAClBE,iBAAiB;MACjBE,WAAW;MACXf,SAAS,EAAEJ,UAAU,CAACI,SAAS;MAC/BC,QAAQ,EAAEL,UAAU,CAACK,QAAQ;MAC7BH,eAAe;MACfI,mBAAmB,EAAE;QACnBC,MAAM,EAAEP,UAAU,CAACQ,eAAe;QAClCC,MAAM,EAAET,UAAU,CAACU,eAAe;QAClCC,iBAAiB,EAAEX,UAAU,CAACW;;KAEjC;EACH;EAEA;;;EAGQ,OAAOvB,wBAAwBA,CAACJ,OAAe,EAAEqC,cAAsB;IAAA;IAAAhF,cAAA,GAAAqB,CAAA;IAC7E,IAAI4D,YAAY;IAAA;IAAA,CAAAjF,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAAC8D,oBAAoB,CAAC,CAAC,CAAC;IAC/C,IAAIC,SAAS;IAAA;IAAA,CAAAnF,cAAA,GAAAoB,CAAA,QAAGgE,QAAQ;IAAC;IAAApF,cAAA,GAAAoB,CAAA;IAEzB,KAAK,MAAM0B,QAAQ,IAAI,IAAI,CAACoC,oBAAoB,EAAE;MAChD,MAAMlC,IAAI;MAAA;MAAA,CAAAhD,cAAA,GAAAoB,CAAA,QAAG6B,IAAI,CAACC,EAAE,GAAGD,IAAI,CAACE,GAAG,CAACL,QAAQ,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAC,CAAC;MACvD,MAAMM,QAAQ;MAAA;MAAA,CAAApD,cAAA,GAAAoB,CAAA,QAAGuB,OAAO,GAAGK,IAAI,EAAC,CAAC;MAEjC;MAAA;MAAAhD,cAAA,GAAAoB,CAAA;MACA;MAAI;MAAA,CAAApB,cAAA,GAAAsB,CAAA,UAAA8B,QAAQ,GAAG,IAAI,CAACiC,eAAe,CAACC,MAAM,CAACC,GAAG;MAAA;MAAA,CAAAvF,cAAA,GAAAsB,CAAA,UAAI8B,QAAQ,GAAG,IAAI,CAACiC,eAAe,CAACC,MAAM,CAACE,GAAG,GAAE;QAAA;QAAAxF,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAC5F;MACF,CAAC;MAAA;MAAA;QAAApB,cAAA,GAAAsB,CAAA;MAAA;MAED;MACA,MAAMmE,cAAc;MAAA;MAAA,CAAAzF,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACkC,qBAAqB,CAACF,QAAQ,EAAE,GAAG,EAAEN,QAAQ,EAAE,kBAAkB,CAAC;MAE9F;MACA,MAAM4C,aAAa;MAAA;MAAA,CAAA1F,cAAA,GAAAoB,CAAA,QAAG6B,IAAI,CAAC0C,GAAG,CAACF,cAAc,GAAGT,cAAc,CAAC,GAAGA,cAAc;MAChF,MAAMY,aAAa;MAAA;MAAA,CAAA5F,cAAA,GAAAoB,CAAA,QAAG6B,IAAI,CAAC0C,GAAG,CAACvC,QAAQ,GAAG,IAAI,CAACiC,eAAe,CAACC,MAAM,CAACO,OAAO,CAAC,GAAG,IAAI,CAACR,eAAe,CAACC,MAAM,CAACO,OAAO;MACpH,MAAMC,UAAU;MAAA;MAAA,CAAA9F,cAAA,GAAAoB,CAAA,QAAGsE,aAAa,GAAGE,aAAa,GAAG,GAAG,EAAC,CAAC;MAAA;MAAA5F,cAAA,GAAAoB,CAAA;MAExD,IAAI0E,UAAU,GAAGX,SAAS,EAAE;QAAA;QAAAnF,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAC1B+D,SAAS,GAAGW,UAAU;QAAC;QAAA9F,cAAA,GAAAoB,CAAA;QACvB6D,YAAY,GAAGnC,QAAQ;MACzB,CAAC;MAAA;MAAA;QAAA9C,cAAA,GAAAsB,CAAA;MAAA;IACH;IAAC;IAAAtB,cAAA,GAAAoB,CAAA;IAED,OAAO6D,YAAY;EACrB;EAEA;;;EAGQ,OAAOR,gCAAgCA,CAAC9B,OAAe,EAAEqC,cAAsB;IAAA;IAAAhF,cAAA,GAAAqB,CAAA;IACrF,IAAI0E,SAAS;IAAA;IAAA,CAAA/F,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAAC4E,0BAA0B,CAAC,CAAC,CAAC;IAClD,IAAIC,UAAU;IAAA;IAAA,CAAAjG,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAAC4E,0BAA0B,CAAC,CAAC,CAAC;IACnD,IAAIb,SAAS;IAAA;IAAA,CAAAnF,cAAA,GAAAoB,CAAA,QAAGgE,QAAQ;IAExB;IACA,MAAMc,YAAY;IAAA;IAAA,CAAAlG,cAAA,GAAAoB,CAAA,QAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IAAC;IAAApB,cAAA,GAAAoB,CAAA;IAEzD,KAAK,MAAM0D,WAAW,IAAIoB,YAAY,EAAE;MACtC;MACA,MAAMC,aAAa;MAAA;MAAA,CAAAnG,cAAA,GAAAoB,CAAA,QAAGuB,OAAO,GAAG,IAAI,CAAC0C,eAAe,CAACC,MAAM,CAACO,OAAO,EAAC,CAAC;MACrE,MAAMrB,MAAM;MAAA;MAAA,CAAAxE,cAAA,GAAAoB,CAAA,QAAG6B,IAAI,CAACmD,IAAI,CAACD,aAAa,GAAGrB,WAAW,CAAC,GAAG,EAAE,EAAC,CAAC;MAC5D,MAAMP,KAAK;MAAA;MAAA,CAAAvE,cAAA,GAAAoB,CAAA,QAAG0D,WAAW,GAAGN,MAAM;MAElC;MACA,MAAM6B,SAAS;MAAA;MAAA,CAAArG,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACkF,uBAAuB,CAAC9B,MAAM,EAAE,IAAI,CAACwB,0BAA0B,CAAC;MACvF,MAAMO,QAAQ;MAAA;MAAA,CAAAvG,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACkF,uBAAuB,CAAC/B,KAAK,EAAE,IAAI,CAACyB,0BAA0B,CAAC;MAErF;MACA,MAAMhD,IAAI;MAAA;MAAA,CAAAhD,cAAA,GAAAoB,CAAA,QAAImF,QAAQ,GAAGF,SAAS,GAAI,GAAG,EAAC,CAAC;MAC3C,MAAMjD,QAAQ;MAAA;MAAA,CAAApD,cAAA,GAAAoB,CAAA,QAAGuB,OAAO,GAAGK,IAAI,EAAC,CAAC;MAEjC;MAAA;MAAAhD,cAAA,GAAAoB,CAAA;MACA;MAAI;MAAA,CAAApB,cAAA,GAAAsB,CAAA,UAAA8B,QAAQ,GAAG,IAAI,CAACiC,eAAe,CAACC,MAAM,CAACC,GAAG;MAAA;MAAA,CAAAvF,cAAA,GAAAsB,CAAA,UAAI8B,QAAQ,GAAG,IAAI,CAACiC,eAAe,CAACC,MAAM,CAACE,GAAG,GAAE;QAAA;QAAAxF,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAC5F;MACF,CAAC;MAAA;MAAA;QAAApB,cAAA,GAAAsB,CAAA;MAAA;MAED;MACA,MAAMoD,kBAAkB;MAAA;MAAA,CAAA1E,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACuD,2BAA2B,CAAC4B,QAAQ,EAAEF,SAAS,CAAC;MAChF,MAAMZ,cAAc;MAAA;MAAA,CAAAzF,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACkC,qBAAqB,CAACF,QAAQ,EAAE,GAAG,EAAEsB,kBAAkB,EAAE,kBAAkB,CAAC;MAExG;MACA,MAAMgB,aAAa;MAAA;MAAA,CAAA1F,cAAA,GAAAoB,CAAA,QAAG6B,IAAI,CAAC0C,GAAG,CAACF,cAAc,GAAGT,cAAc,CAAC,GAAGA,cAAc;MAChF,MAAMY,aAAa;MAAA;MAAA,CAAA5F,cAAA,GAAAoB,CAAA,QAAG6B,IAAI,CAAC0C,GAAG,CAACvC,QAAQ,GAAG,IAAI,CAACiC,eAAe,CAACC,MAAM,CAACO,OAAO,CAAC,GAAG,IAAI,CAACR,eAAe,CAACC,MAAM,CAACO,OAAO;MACpH,MAAMW,WAAW;MAAA;MAAA,CAAAxG,cAAA,GAAAoB,CAAA,QAAG0D,WAAW,GAAG,GAAG;MAAA;MAAA,CAAA9E,cAAA,GAAAsB,CAAA,UAAG,CAACwD,WAAW,GAAG,GAAG,IAAI,GAAG;MAAA;MAAA,CAAA9E,cAAA,GAAAsB,CAAA,UAAG,CAAC,GAAC,CAAC;MACvE,MAAMwE,UAAU;MAAA;MAAA,CAAA9F,cAAA,GAAAoB,CAAA,QAAGsE,aAAa,GAAGE,aAAa,GAAG,GAAG,GAAGY,WAAW;MAAC;MAAAxG,cAAA,GAAAoB,CAAA;MAErE,IAAI0E,UAAU,GAAGX,SAAS,EAAE;QAAA;QAAAnF,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAC1B+D,SAAS,GAAGW,UAAU;QAAC;QAAA9F,cAAA,GAAAoB,CAAA;QACvB2E,SAAS,GAAGQ,QAAQ;QAAC;QAAAvG,cAAA,GAAAoB,CAAA;QACrB6E,UAAU,GAAGI,SAAS;MACxB,CAAC;MAAA;MAAA;QAAArG,cAAA,GAAAsB,CAAA;MAAA;IACH;IAAC;IAAAtB,cAAA,GAAAoB,CAAA;IAED,OAAO;MAAEmD,KAAK,EAAEwB,SAAS;MAAEvB,MAAM,EAAEyB;IAAU,CAAE;EACjD;EAEA;;;EAGO,OAAOtB,2BAA2BA,CAACJ,KAAa,EAAEC,MAAc;IAAA;IAAAxE,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACrE,OAAO,GAAG,GAAG6B,IAAI,CAACE,GAAG,CAACoB,KAAK,GAAGC,MAAM,EAAE,KAAK,CAAC,GAAGvB,IAAI,CAACE,GAAG,CAACoB,KAAK,GAAGC,MAAM,EAAE,IAAI,CAAC;EAC/E;EAEA;;;EAGO,OAAOK,0BAA0BA,CAACN,KAAa,EAAEC,MAAc;IAAA;IAAAxE,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACpE,OAAQ,CAAC,GAAGmD,KAAK,GAAGC,MAAM,IAAK,CAAC,IAAID,KAAK,GAAGC,MAAM,CAAC,CAAC;EACtD;EAEA;;;EAGO,OAAOO,oBAAoBA,CAACR,KAAa,EAAEC,MAAc;IAAA;IAAAxE,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAC9D,OAAO6B,IAAI,CAACuC,GAAG,CAACjB,KAAK,EAAEC,MAAM,CAAC,GAAGvB,IAAI,CAACsC,GAAG,CAAChB,KAAK,EAAEC,MAAM,CAAC;EAC1D;EAEA;;;EAGO,OAAOlB,qBAAqBA,CAACF,QAAgB,EAAEqD,MAAc,EAAE3D,QAAgB,EAAED,QAAgB;IAAA;IAAA7C,cAAA,GAAAqB,CAAA;IACtG,MAAMqF,aAAa;IAAA;IAAA,CAAA1G,cAAA,GAAAoB,CAAA;IAAG;IAAA,CAAApB,cAAA,GAAAsB,CAAA,eAAI,CAACqF,mBAAmB,CAAC9D,QAAQ,CAAC;IAAA;IAAA,CAAA7C,cAAA,GAAAsB,CAAA,WAAI,IAAI,CAACqF,mBAAmB,CAACC,gBAAgB;IACrG,MAAMC,SAAS;IAAA;IAAA,CAAA7G,cAAA,GAAAoB,CAAA,QAAGsF,aAAa,CAACI,eAAe;IAE/C;IACA,MAAMC,WAAW;IAAA;IAAA,CAAA/G,cAAA,GAAAoB,CAAA,QAAGgC,QAAQ,GAAG,EAAE,EAAC,CAAC;IACnC,MAAM4D,UAAU;IAAA;IAAA,CAAAhH,cAAA,GAAAoB,CAAA,QAAG0B,QAAQ,GAAG,EAAE,EAAC,CAAC;IAElC;IACA,MAAMmE,UAAU;IAAA;IAAA,CAAAjH,cAAA,GAAAoB,CAAA,QAAG,KAAK,EAAC,CAAC;IAC1B,MAAM8F,kBAAkB;IAAA;IAAA,CAAAlH,cAAA,GAAAoB,CAAA,QAAG,OAAO,EAAC,CAAC;IAEpC;IACA,MAAM+F,QAAQ;IAAA;IAAA,CAAAnH,cAAA,GAAAoB,CAAA,QAAI2F,WAAW,GAAGC,UAAU,GAAIE,kBAAkB;IAEhE;IACA,MAAMzD,cAAc;IAAA;IAAA,CAAAzD,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACsC,uBAAuB,CAACyD,QAAQ,EAAEtE,QAAQ,EAAEC,QAAQ,CAAC;IAEjF;IACA;IACA,MAAMsE,eAAe;IAAA;IAAA,CAAApH,cAAA,GAAAoB,CAAA,QAAGqC,cAAc,IAAIgD,MAAM,GAAGO,UAAU,CAAC,IAAIC,UAAU,GAAGhE,IAAI,CAACE,GAAG,CAAC4D,WAAW,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;IACvH,MAAMM,gBAAgB;IAAA;IAAA,CAAArH,cAAA,GAAAoB,CAAA,QAAGgG,eAAe,GAAG,GAAG,EAAC,CAAC;IAAA;IAAApH,cAAA,GAAAoB,CAAA;IAEhD,OAAOiG,gBAAgB;EACzB;EAEA;;;EAGO,OAAO7D,uBAAuBA,CAACJ,QAAgB,EAAEN,QAAgB;IAAA;IAAA9C,cAAA,GAAAqB,CAAA;IACtE,MAAM0F,WAAW;IAAA;IAAA,CAAA/G,cAAA,GAAAoB,CAAA,QAAGgC,QAAQ,GAAG,EAAE,EAAC,CAAC;IACnC,MAAM4D,UAAU;IAAA;IAAA,CAAAhH,cAAA,GAAAoB,CAAA,QAAG0B,QAAQ,GAAG,EAAE,EAAC,CAAC;IAClC,MAAMoE,kBAAkB;IAAA;IAAA,CAAAlH,cAAA,GAAAoB,CAAA,QAAG,OAAO,EAAC,CAAC;IAAA;IAAApB,cAAA,GAAAoB,CAAA;IAEpC,OAAQ2F,WAAW,GAAGC,UAAU,GAAIE,kBAAkB;EACxD;EAEA;;;EAGO,OAAOxD,uBAAuBA,CAACyD,QAAgB,EAAEtE,QAAgB,EAAEC,QAAgB;IAAA;IAAA9C,cAAA,GAAAqB,CAAA;IACxF,MAAMqF,aAAa;IAAA;IAAA,CAAA1G,cAAA,GAAAoB,CAAA;IAAG;IAAA,CAAApB,cAAA,GAAAsB,CAAA,eAAI,CAACqF,mBAAmB,CAAC9D,QAAQ,CAAC;IAAA;IAAA,CAAA7C,cAAA,GAAAsB,CAAA,WAAI,IAAI,CAACqF,mBAAmB,CAACC,gBAAgB;IACrG,MAAMC,SAAS;IAAA;IAAA,CAAA7G,cAAA,GAAAoB,CAAA,QAAGsF,aAAa,CAACI,eAAe;IAC/C,MAAME,UAAU;IAAA;IAAA,CAAAhH,cAAA,GAAAoB,CAAA,QAAG0B,QAAQ,GAAG,EAAE;IAChC,MAAMwE,iBAAiB;IAAA;IAAA,CAAAtH,cAAA,GAAAoB,CAAA,QAAGyF,SAAS,GAAGG,UAAU;IAEhD;IAAA;IAAAhH,cAAA,GAAAoB,CAAA;IACA,IAAI+F,QAAQ,GAAG,IAAI,EAAE;MAAA;MAAAnH,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACnB,OAAO,EAAE,GAAG+F,QAAQ;IACtB,CAAC;IAAA;IAAA;MAAAnH,cAAA,GAAAsB,CAAA;IAAA;IAED;IACA,IAAID,CAAC;IAAA;IAAA,CAAArB,cAAA,GAAAoB,CAAA,QAAG,IAAI,EAAC,CAAC;IAAA;IAAApB,cAAA,GAAAoB,CAAA;IACd,KAAK,IAAImG,CAAC;IAAA;IAAA,CAAAvH,cAAA,GAAAoB,CAAA,QAAG,CAAC,GAAEmG,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC3B,MAAMC,IAAI;MAAA;MAAA,CAAAxH,cAAA,GAAAoB,CAAA,QAAG,CAAC,GAAG6B,IAAI,CAACE,GAAG,CAAC,CAAC,CAAC,GAAGF,IAAI,CAACwE,KAAK,CAACH,iBAAiB,GAAG,GAAG,GAAG,IAAI,IAAIH,QAAQ,GAAGlE,IAAI,CAACmD,IAAI,CAAC/E,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MAAC;MAAArB,cAAA,GAAAoB,CAAA;MAC1G,IAAI6B,IAAI,CAAC0C,GAAG,CAAC6B,IAAI,GAAGnG,CAAC,CAAC,GAAG,MAAM,EAAE;QAAA;QAAArB,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAAA;MAAA,CAAM;MAAA;MAAA;QAAApB,cAAA,GAAAsB,CAAA;MAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACvCC,CAAC,GAAGmG,IAAI;IACV;IAAC;IAAAxH,cAAA,GAAAoB,CAAA;IAED,OAAOC,CAAC;EACV;EAEA;;;EAGQ,OAAOuC,eAAeA,CAAC8D,OAAY;IAAA;IAAA1H,cAAA,GAAAqB,CAAA;IAOzC,MAAM2C,QAAQ;IAAA;IAAA,CAAAhE,cAAA,GAAAoB,CAAA,SAAa,EAAE;IAC7B,IAAI+C,eAAe;IAAA;IAAA,CAAAnE,cAAA,GAAAoB,CAAA,SAAG,IAAI;IAC1B,IAAIiD,eAAe;IAAA;IAAA,CAAArE,cAAA,GAAAoB,CAAA,SAAG,IAAI;IAC1B,IAAIkD,iBAAiB;IAAA;IAAA,CAAAtE,cAAA,GAAAoB,CAAA,SAAG,IAAI;IAE5B;IACA,MAAM;MAAEgC;IAAQ,CAAE;IAAA;IAAA,CAAApD,cAAA,GAAAoB,CAAA,SAAGsG,OAAO;IAAC;IAAA1H,cAAA,GAAAoB,CAAA;IAC7B,IAAIgC,QAAQ,GAAG,IAAI,CAACiC,eAAe,CAACC,MAAM,CAACC,GAAG,EAAE;MAAA;MAAAvF,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAC9C4C,QAAQ,CAAC2D,IAAI,CAAC,YAAYvE,QAAQ,CAACwE,OAAO,CAAC,CAAC,CAAC,sCAAsC,IAAI,CAACvC,eAAe,CAACC,MAAM,CAACC,GAAG,OAAO,CAAC;MAAC;MAAAvF,cAAA,GAAAoB,CAAA;MAC3HkD,iBAAiB,GAAG,KAAK;IAC3B,CAAC,MAAM;MAAA;MAAAtE,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,IAAIgC,QAAQ,GAAG,IAAI,CAACiC,eAAe,CAACC,MAAM,CAACE,GAAG,EAAE;QAAA;QAAAxF,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACrD4C,QAAQ,CAAC2D,IAAI,CAAC,YAAYvE,QAAQ,CAACwE,OAAO,CAAC,CAAC,CAAC,qCAAqC,IAAI,CAACvC,eAAe,CAACC,MAAM,CAACE,GAAG,OAAO,CAAC;QAAC;QAAAxF,cAAA,GAAAoB,CAAA;QAC1HkD,iBAAiB,GAAG,KAAK;QAAC;QAAAtE,cAAA,GAAAoB,CAAA;QAC1B+C,eAAe,GAAG,KAAK;MACzB,CAAC;MAAA;MAAA;QAAAnE,cAAA,GAAAsB,CAAA;MAAA;IAAD;IAEA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IACA,IAAIsG,OAAO,CAAC5C,WAAW,EAAE;MAAA;MAAA9E,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACvB,IAAIsG,OAAO,CAAC5C,WAAW,GAAG,GAAG,EAAE;QAAA;QAAA9E,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAC7B4C,QAAQ,CAAC2D,IAAI,CAAC,gBAAgBD,OAAO,CAAC5C,WAAW,CAAC8C,OAAO,CAAC,CAAC,CAAC,kCAAkC,CAAC;QAAC;QAAA5H,cAAA,GAAAoB,CAAA;QAChG+C,eAAe,GAAG,KAAK;MACzB,CAAC,MAAM;QAAA;QAAAnE,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAAA,IAAIsG,OAAO,CAAC5C,WAAW,GAAG,GAAG,EAAE;UAAA;UAAA9E,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAoB,CAAA;UACpC4C,QAAQ,CAAC2D,IAAI,CAAC,gBAAgBD,OAAO,CAAC5C,WAAW,CAAC8C,OAAO,CAAC,CAAC,CAAC,oCAAoC,CAAC;QACnG,CAAC;QAAA;QAAA;UAAA5H,cAAA,GAAAsB,CAAA;QAAA;MAAD;IACF,CAAC;IAAA;IAAA;MAAAtB,cAAA,GAAAsB,CAAA;IAAA;IAED;IAAAtB,cAAA,GAAAoB,CAAA;IACA,IAAIsG,OAAO,CAAC1E,IAAI,GAAG,GAAG,EAAE;MAAA;MAAAhD,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACtB4C,QAAQ,CAAC2D,IAAI,CAAC,gEAAgE,CAAC;IACjF,CAAC;IAAA;IAAA;MAAA3H,cAAA,GAAAsB,CAAA;IAAA;IAED,MAAMyC,SAAS;IAAA;IAAA,CAAA/D,cAAA,GAAAoB,CAAA;IAAG;IAAA,CAAApB,cAAA,GAAAsB,CAAA,WAAA0C,QAAQ,CAACyC,MAAM,KAAK,CAAC;IAAA;IAAA,CAAAzG,cAAA,GAAAsB,CAAA,WACrC8B,QAAQ,IAAI,IAAI,CAACiC,eAAe,CAACC,MAAM,CAACO,OAAO,GAAG,GAAG;IAAA;IAAA,CAAA7F,cAAA,GAAAsB,CAAA,WACrD8B,QAAQ,IAAI,IAAI,CAACiC,eAAe,CAACC,MAAM,CAACO,OAAO,GAAG,GAAG;IAAC;IAAA7F,cAAA,GAAAoB,CAAA;IAExD,OAAO;MACL2C,SAAS;MACTC,QAAQ;MACRG,eAAe;MACfE,eAAe;MACfC;KACD;EACH;EAEA;;;EAGQ,OAAOR,uBAAuBA,CAAC3B,MAAwB,EAAEuF,OAAY;IAAA;IAAA1H,cAAA,GAAAqB,CAAA;IAC3E,MAAMwC,eAAe;IAAA;IAAA,CAAA7D,cAAA,GAAAoB,CAAA,SAAa,EAAE;IAEpC;IAAA;IAAApB,cAAA,GAAAoB,CAAA;IACA,IAAIsG,OAAO,CAACtE,QAAQ,GAAG,IAAI,CAACiC,eAAe,CAACC,MAAM,CAACO,OAAO,GAAG,GAAG,EAAE;MAAA;MAAA7F,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAChEyC,eAAe,CAAC8D,IAAI,CAAC,yEAAyE,CAAC;IACjG,CAAC,MAAM;MAAA;MAAA3H,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,IAAIsG,OAAO,CAACtE,QAAQ,GAAG,IAAI,CAACiC,eAAe,CAACC,MAAM,CAACO,OAAO,GAAG,GAAG,EAAE;QAAA;QAAA7F,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACvEyC,eAAe,CAAC8D,IAAI,CAAC,4DAA4D,CAAC;MACpF,CAAC;MAAA;MAAA;QAAA3H,cAAA,GAAAsB,CAAA;MAAA;IAAD;IAEA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IACA;IAAI;IAAA,CAAApB,cAAA,GAAAsB,CAAA,WAAAoG,OAAO,CAAC5C,WAAW;IAAA;IAAA,CAAA9E,cAAA,GAAAsB,CAAA,WAAIoG,OAAO,CAAC5C,WAAW,GAAG,GAAG,GAAE;MAAA;MAAA9E,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACpDyC,eAAe,CAAC8D,IAAI,CAAC,2EAA2E,CAAC;IACnG,CAAC;IAAA;IAAA;MAAA3H,cAAA,GAAAsB,CAAA;IAAA;IAED;IAAAtB,cAAA,GAAAoB,CAAA;IACA;IAAI;IAAA,CAAApB,cAAA,GAAAsB,CAAA,WAAAa,MAAM,CAACU,QAAQ,KAAK,kBAAkB;IAAA;IAAA,CAAA7C,cAAA,GAAAsB,CAAA,WAAIoG,OAAO,CAACtE,QAAQ,GAAG,IAAI,GAAE;MAAA;MAAApD,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACrEyC,eAAe,CAAC8D,IAAI,CAAC,gFAAgF,CAAC;IACxG,CAAC;IAAA;IAAA;MAAA3H,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAED,OAAOyC,eAAe;EACxB;EAEA;;;EAGQ,OAAOyC,uBAAuBA,CAACuB,MAAc,EAAEC,aAAuB;IAAA;IAAA9H,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAC5E,OAAO0G,aAAa,CAACC,MAAM,CAAC,CAACC,IAAI,EAAEC,IAAI,KACrC;MAAA;MAAAjI,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAAA,OAAA6B,IAAI,CAAC0C,GAAG,CAACsC,IAAI,GAAGJ,MAAM,CAAC,GAAG5E,IAAI,CAAC0C,GAAG,CAACqC,IAAI,GAAGH,MAAM,CAAC;MAAA;MAAA,CAAA7H,cAAA,GAAAsB,CAAA,WAAG2G,IAAI;MAAA;MAAA,CAAAjI,cAAA,GAAAsB,CAAA,WAAG0G,IAAI;IAAJ,CAAI,CAChE;EACH;EAEA;;;EAGQ,OAAOzF,iBAAiBA,CAACJ,MAAwB;IAAA;IAAAnC,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACvD,OAAO;MACL,GAAGe,MAAM;MACTQ,OAAO,EAAER,MAAM,CAACQ,OAAO,GAAG,KAAK;MAAE;MACjCC,YAAY,EAAET,MAAM,CAACS,YAAY,GAAG,KAAK;MAAE;MAC3CN,KAAK,EAAE;KACR;EACH;EAEA;;;EAGQ,OAAOF,cAAcA,CAACD,MAAwB;IAAA;IAAAnC,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACpD,IAAIe,MAAM,CAACQ,OAAO,IAAI,CAAC,EAAE;MAAA;MAAA3C,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACvB,MAAM,IAAI8G,KAAK,CAAC,gCAAgC,CAAC;IACnD,CAAC;IAAA;IAAA;MAAAlI,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IACD,IAAIe,MAAM,CAACS,YAAY,IAAI,CAAC,EAAE;MAAA;MAAA5C,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAC5B,MAAM,IAAI8G,KAAK,CAAC,sCAAsC,CAAC;IACzD,CAAC;IAAA;IAAA;MAAAlI,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IACD,IAAI,CAAC,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC+G,QAAQ,CAAChG,MAAM,CAACK,QAAQ,CAAC,EAAE;MAAA;MAAAxC,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACvD,MAAM,IAAI8G,KAAK,CAAC,4CAA4C,CAAC;IAC/D,CAAC;IAAA;IAAA;MAAAlI,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IACD,IAAI,CAAC,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC+G,QAAQ,CAAChG,MAAM,CAACG,KAAK,CAAC,EAAE;MAAA;MAAAtC,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAClD,MAAM,IAAI8G,KAAK,CAAC,sCAAsC,CAAC;IACzD,CAAC;IAAA;IAAA;MAAAlI,cAAA,GAAAsB,CAAA;IAAA;EACH;EAEA;;;EAGO,OAAO8G,YAAYA,CAAA;IAAA;IAAApI,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACxB,OAAO;MAAE,GAAG,IAAI,CAACuF;IAAmB,CAAE;EACxC;EAEA;;;EAGO,OAAO0B,iBAAiBA,CAAA;IAAA;IAAArI,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAC7B,OAAO;MAAE,GAAG,IAAI,CAACiE;IAAe,CAAE;EACpC;EAEA;;;EAGO,OAAOiD,gBAAgBA,CAAA;IAAA;IAAAtI,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAC5B,OAAO;MACLmH,KAAK,EAAE,CAAC,GAAG,IAAI,CAACrD,oBAAoB,CAAC;MACrCsD,WAAW,EAAE,CAAC,GAAG,IAAI,CAACxC,0BAA0B;KACjD;EACH;;;;AAvcFyC,OAAA,CAAAxG,iBAAA,GAAAA,iBAAA;AACE;AAAA;AAAAjC,cAAA,GAAAoB,CAAA;AACwBa,iBAAA,CAAAiD,oBAAoB,GAAG,CAC7C,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EACvD,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CACvC;AAED;AAAA;AAAAlF,cAAA,GAAAoB,CAAA;AACwBa,iBAAA,CAAA+D,0BAA0B,GAAG,CACnD,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAChD,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAC3C;AAED;AAAA;AAAAhG,cAAA,GAAAoB,CAAA;AACwBa,iBAAA,CAAA0E,mBAAmB,GAAuC;EAChFC,gBAAgB,EAAE;IAAEE,eAAe,EAAE,MAAM;IAAEjG,IAAI,EAAE,kBAAkB;IAAE6H,WAAW,EAAE;EAAwB,CAAE;EAC9GC,QAAQ,EAAE;IAAE7B,eAAe,EAAE,MAAM;IAAEjG,IAAI,EAAE,UAAU;IAAE6H,WAAW,EAAE;EAAsB,CAAE;EAC5FE,eAAe,EAAE;IAAE9B,eAAe,EAAE,MAAM;IAAEjG,IAAI,EAAE,iBAAiB;IAAE6H,WAAW,EAAE;EAAqB,CAAE;EACzGG,GAAG,EAAE;IAAE/B,eAAe,EAAE,MAAM;IAAEjG,IAAI,EAAE,KAAK;IAAE6H,WAAW,EAAE;EAAkB,CAAE;EAC9EI,UAAU,EAAE;IAAEhC,eAAe,EAAE,MAAM;IAAEjG,IAAI,EAAE,YAAY;IAAE6H,WAAW,EAAE;EAAoB,CAAE;EAC9FK,QAAQ,EAAE;IAAEjC,eAAe,EAAE,KAAK;IAAEjG,IAAI,EAAE,UAAU;IAAE6H,WAAW,EAAE;EAAmB,CAAE;EACxFM,KAAK,EAAE;IAAElC,eAAe,EAAE,IAAI;IAAEjG,IAAI,EAAE,OAAO;IAAE6H,WAAW,EAAE;EAAe;CAC5E;AAED;AAAA;AAAA1I,cAAA,GAAAoB,CAAA;AACwBa,iBAAA,CAAAoD,eAAe,GAAG;EACxCC,MAAM,EAAE;IAAEC,GAAG,EAAE,GAAG;IAAEC,GAAG,EAAE,IAAI;IAAEK,OAAO,EAAE;EAAI,CAAE;EAC9CoD,MAAM,EAAE;IAAE1D,GAAG,EAAE,GAAG;IAAEC,GAAG,EAAE,IAAI;IAAEK,OAAO,EAAE;EAAI,CAAE;EAC9CqD,OAAO,EAAE;IAAE3D,GAAG,EAAE,GAAG;IAAEC,GAAG,EAAE,IAAI;IAAEK,OAAO,EAAE;EAAI;CAC9C;AAAC;AAAA7F,cAAA,GAAAoB,CAAA;AA6aJqH,OAAA,CAAAU,OAAA,GAAelH,iBAAiB", "ignoreList": []}