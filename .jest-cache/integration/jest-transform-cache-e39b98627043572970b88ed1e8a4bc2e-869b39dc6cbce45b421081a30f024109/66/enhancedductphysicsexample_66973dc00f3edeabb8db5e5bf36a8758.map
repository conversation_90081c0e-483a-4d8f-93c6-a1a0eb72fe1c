{"version": 3, "names": ["cov_23gtoqs14d", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "name", "decl", "loc", "branchMap", "type", "locations", "undefined", "s", "f", "b", "inputSourceMap", "file", "mappings", "names", "sources", "sourcesContent", "version", "_coverageSchema", "coverage", "actualCoverage", "exports", "compareStandardVsEnhanced", "demonstrateAirProperties", "demonstrateMaterialAging", "demonstrateVelocityPressureOptimization", "runEnhancedDuctPhysicsDemo", "SystemPressureCalculator_1", "require", "AirPropertiesCalculator_1", "console", "log", "segments", "id", "ductShape", "length", "diameter", "airflow", "material", "fittingConfig", "fittingType", "subType", "parameters", "radius_to_diameter_ratio", "standardInputs", "systemType", "designConditions", "temperature", "barometricPressure", "humidity", "calculationOptions", "includeElevation", "includeFittings", "roundResults", "standardResult", "SystemPressureCalculator", "calculateSystemPressure", "totalPressureLoss", "frictionLoss", "fitting<PERSON>oss", "enhancedSegments", "map", "segment", "elevation", "materialAge", "surfaceCondition", "enhancedInputs", "altitude", "enhancedResult", "calculateEnhancedSystemPressure", "elevationLoss", "pressureDifference", "percentIncrease", "toFixed", "systemMetrics", "environmentalImpact", "warnings", "for<PERSON>ach", "warning", "conditions", "condition", "index", "airProps", "AirPropertiesCalculator", "calculateAirProperties", "density", "correctionFactors", "combined", "join", "materials", "ages", "toUpperCase", "age", "roughness", "getEnhancedMaterialRoughness", "<PERSON><PERSON><PERSON><PERSON>", "effectiveRoughness", "agingFactor", "velocities", "velocity", "tableResult", "calculateVelocityPressure", "airConditions", "useTable", "formulaResult", "difference", "Math", "abs", "velocityPressure", "toString", "padStart", "error", "main", "module"], "sources": ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\examples\\enhanced-duct-physics-example.ts"], "sourcesContent": ["/**\r\n * Enhanced Duct Physics Example - Phase 2\r\n * \r\n * This example demonstrates the enhanced capabilities of the duct physics system\r\n * including environmental corrections, material aging effects, and advanced calculations.\r\n */\r\n\r\nimport { SystemPressureCalculator, DuctSegment } from '../SystemPressureCalculator';\r\nimport { AirPropertiesCalculator, AirConditions } from '../AirPropertiesCalculator';\r\nimport { FittingLossCalculator, FittingConfiguration } from '../FittingLossCalculator';\r\n\r\n/**\r\n * Example 1: Standard vs Enhanced System Calculation\r\n * Demonstrates the difference between standard and enhanced calculations\r\n */\r\nexport function compareStandardVsEnhanced() {\r\n  console.log('=== STANDARD vs ENHANCED CALCULATION COMPARISON ===\\n');\r\n\r\n  // Define system segments\r\n  const segments: DuctSegment[] = [\r\n    {\r\n      id: 'segment-1',\r\n      type: 'straight',\r\n      ductShape: 'round',\r\n      length: 10,\r\n      diameter: 10,\r\n      airflow: 1000,\r\n      material: 'galvanized_steel'\r\n    },\r\n    {\r\n      id: 'segment-2',\r\n      type: 'fitting',\r\n      ductShape: 'round',\r\n      diameter: 10,\r\n      airflow: 1000,\r\n      material: 'galvanized_steel',\r\n      fittingConfig: {\r\n        ductShape: 'round',\r\n        fittingType: 'elbow',\r\n        subType: '90deg_round_smooth',\r\n        parameters: { radius_to_diameter_ratio: 1.5 }\r\n      }\r\n    },\r\n    {\r\n      id: 'segment-3',\r\n      type: 'straight',\r\n      ductShape: 'round',\r\n      length: 10,\r\n      diameter: 10,\r\n      airflow: 1000,\r\n      material: 'galvanized_steel'\r\n    }\r\n  ];\r\n\r\n  // Standard calculation (sea level, 70°F, new duct)\r\n  const standardInputs = {\r\n    segments,\r\n    systemType: 'supply' as const,\r\n    designConditions: {\r\n      temperature: 70,\r\n      barometricPressure: 29.92,\r\n      humidity: 50\r\n    },\r\n    calculationOptions: {\r\n      includeElevation: false,\r\n      includeFittings: true,\r\n      roundResults: true\r\n    }\r\n  };\r\n\r\n  const standardResult = SystemPressureCalculator.calculateSystemPressure(standardInputs);\r\n\r\n  console.log('STANDARD CALCULATION (Sea Level, 70°F, New Duct):');\r\n  console.log(`  Total Pressure Loss: ${standardResult.totalPressureLoss} in wg`);\r\n  console.log(`  Friction Loss: ${standardResult.frictionLoss} in wg`);\r\n  console.log(`  Fitting Loss: ${standardResult.fittingLoss} in wg`);\r\n\r\n  // Enhanced calculation (Denver altitude, 100°F, 10-year-old duct)\r\n  const enhancedSegments: DuctSegment[] = segments.map(segment => ({\r\n    ...segment,\r\n    temperature: 100,\r\n    elevation: 5000,\r\n    humidity: 30,\r\n    materialAge: 10,\r\n    surfaceCondition: 'good' as const\r\n  }));\r\n\r\n  const enhancedInputs = {\r\n    segments: enhancedSegments,\r\n    systemType: 'supply' as const,\r\n    designConditions: {\r\n      temperature: 100,\r\n      barometricPressure: 24.89, // Denver pressure\r\n      altitude: 5000,\r\n      humidity: 30\r\n    },\r\n    calculationOptions: {\r\n      includeElevation: true,\r\n      includeFittings: true,\r\n      roundResults: true\r\n    }\r\n  };\r\n\r\n  const enhancedResult = SystemPressureCalculator.calculateEnhancedSystemPressure(enhancedInputs);\r\n\r\n  console.log('\\nENHANCED CALCULATION (Denver, 100°F, 10-year-old duct):');\r\n  console.log(`  Total Pressure Loss: ${enhancedResult.totalPressureLoss} in wg`);\r\n  console.log(`  Friction Loss: ${enhancedResult.frictionLoss} in wg`);\r\n  console.log(`  Fitting Loss: ${enhancedResult.fittingLoss} in wg`);\r\n  console.log(`  Elevation Loss: ${enhancedResult.elevationLoss} in wg`);\r\n\r\n  // Calculate the difference\r\n  const pressureDifference = enhancedResult.totalPressureLoss - standardResult.totalPressureLoss;\r\n  const percentIncrease = (pressureDifference / standardResult.totalPressureLoss * 100);\r\n\r\n  console.log('\\nCOMPARISON:');\r\n  console.log(`  Pressure Difference: ${pressureDifference.toFixed(4)} in wg`);\r\n  console.log(`  Percent Increase: ${percentIncrease.toFixed(1)}%`);\r\n  console.log(`  Environmental Impact: ${enhancedResult.systemMetrics.environmentalImpact}%`);\r\n\r\n  if (enhancedResult.warnings.length > 0) {\r\n    console.log('\\nWARNINGS:');\r\n    enhancedResult.warnings.forEach(warning => console.log(`  - ${warning}`));\r\n  }\r\n\r\n  return { standardResult, enhancedResult };\r\n}\r\n\r\n/**\r\n * Example 2: Air Properties Calculation\r\n * Demonstrates environmental corrections for different conditions\r\n */\r\nexport function demonstrateAirProperties() {\r\n  console.log('\\n=== AIR PROPERTIES DEMONSTRATION ===\\n');\r\n\r\n  const conditions: AirConditions[] = [\r\n    { temperature: 70, altitude: 0, humidity: 50 },      // Standard conditions\r\n    { temperature: 100, altitude: 0, humidity: 50 },     // High temperature\r\n    { temperature: 70, altitude: 5000, humidity: 50 },   // High altitude\r\n    { temperature: 70, altitude: 0, humidity: 90 },      // High humidity\r\n    { temperature: 150, altitude: 8000, humidity: 20 }   // Extreme conditions\r\n  ];\r\n\r\n  conditions.forEach((condition, index) => {\r\n    const airProps = AirPropertiesCalculator.calculateAirProperties(condition);\r\n    \r\n    console.log(`CONDITION ${index + 1}: ${condition.temperature}°F, ${condition.altitude} ft, ${condition.humidity}% RH`);\r\n    console.log(`  Air Density: ${airProps.density.toFixed(4)} lb/ft³`);\r\n    console.log(`  Temperature Correction: ${airProps.correctionFactors.temperature.toFixed(3)}`);\r\n    console.log(`  Altitude Correction: ${airProps.correctionFactors.altitude.toFixed(3)}`);\r\n    console.log(`  Humidity Correction: ${airProps.correctionFactors.humidity.toFixed(3)}`);\r\n    console.log(`  Combined Correction: ${airProps.correctionFactors.combined.toFixed(3)}`);\r\n    \r\n    if (airProps.warnings.length > 0) {\r\n      console.log(`  Warnings: ${airProps.warnings.join(', ')}`);\r\n    }\r\n    console.log('');\r\n  });\r\n}\r\n\r\n/**\r\n * Example 3: Material Aging Effects\r\n * Shows how material aging affects pressure calculations\r\n */\r\nexport function demonstrateMaterialAging() {\r\n  console.log('\\n=== MATERIAL AGING EFFECTS ===\\n');\r\n\r\n  const materials = ['galvanized_steel', 'aluminum', 'stainless_steel', 'flexible_duct'];\r\n  const ages = [0, 5, 10, 15, 20];\r\n\r\n  materials.forEach(material => {\r\n    console.log(`MATERIAL: ${material.toUpperCase()}`);\r\n    \r\n    ages.forEach(age => {\r\n      const roughness = AirPropertiesCalculator.getEnhancedMaterialRoughness(\r\n        material, \r\n        age, \r\n        'good'\r\n      );\r\n      \r\n      const ageLabel = age === 0 ? 'New' : `${age} years`;\r\n      console.log(`  ${ageLabel}: ${roughness.effectiveRoughness.toFixed(6)} ft (factor: ${roughness.agingFactor.toFixed(2)})`);\r\n    });\r\n    console.log('');\r\n  });\r\n}\r\n\r\n/**\r\n * Example 4: Velocity Pressure Optimization\r\n * Compares table lookup vs formula calculation\r\n */\r\nexport function demonstrateVelocityPressureOptimization() {\r\n  console.log('\\n=== VELOCITY PRESSURE OPTIMIZATION ===\\n');\r\n\r\n  const velocities = [500, 1000, 1500, 2000, 2500, 3000];\r\n  const conditions: AirConditions = {\r\n    temperature: 85,\r\n    altitude: 2500,\r\n    humidity: 40\r\n  };\r\n\r\n  console.log('VELOCITY PRESSURE COMPARISON (Table vs Formula):');\r\n  console.log('Velocity (FPM) | Table (in wg) | Formula (in wg) | Difference');\r\n  console.log('---------------|---------------|-----------------|----------');\r\n\r\n  velocities.forEach(velocity => {\r\n    // Table lookup method\r\n    const tableResult = AirPropertiesCalculator.calculateVelocityPressure({\r\n      velocity,\r\n      airConditions: conditions,\r\n      useTable: true\r\n    });\r\n\r\n    // Formula calculation method\r\n    const formulaResult = AirPropertiesCalculator.calculateVelocityPressure({\r\n      velocity,\r\n      airConditions: conditions,\r\n      useTable: false\r\n    });\r\n\r\n    const difference = Math.abs(tableResult.velocityPressure - formulaResult.velocityPressure);\r\n    \r\n    console.log(\r\n      `${velocity.toString().padStart(14)} | ` +\r\n      `${tableResult.velocityPressure.toFixed(4).padStart(13)} | ` +\r\n      `${formulaResult.velocityPressure.toFixed(4).padStart(15)} | ` +\r\n      `${difference.toFixed(6)}`\r\n    );\r\n  });\r\n\r\n  console.log('\\nPerformance benefits of table lookup:');\r\n  console.log('- Faster calculation (pre-computed values)');\r\n  console.log('- Consistent precision across velocity range');\r\n  console.log('- Reduced computational overhead for large systems');\r\n}\r\n\r\n/**\r\n * Main demonstration function\r\n */\r\nexport function runEnhancedDuctPhysicsDemo() {\r\n  console.log('ENHANCED DUCT PHYSICS DEMONSTRATION - PHASE 2');\r\n  console.log('==============================================\\n');\r\n\r\n  try {\r\n    // Run all demonstrations\r\n    compareStandardVsEnhanced();\r\n    demonstrateAirProperties();\r\n    demonstrateMaterialAging();\r\n    demonstrateVelocityPressureOptimization();\r\n\r\n    console.log('\\n==============================================');\r\n    console.log('DEMONSTRATION COMPLETED SUCCESSFULLY');\r\n    console.log('==============================================');\r\n\r\n  } catch (error) {\r\n    console.error('Error in demonstration:', error);\r\n  }\r\n}\r\n\r\n// Run demonstration if this file is executed directly\r\nif (require.main === module) {\r\n  runEnhancedDuctPhysicsDemo();\r\n}\r\n"], "mappings": ";;AAAA;;;;;;AAAA;AAAA,SAAAA,eAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;MAAA;QAAAC,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAO,SAAA;MAAA;QAAAD,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;IAAA;IAAAW,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,cAAA;MAAAC,IAAA;MAAAC,QAAA;MAAAC,KAAA;MAAAC,OAAA;MAAAC,cAAA;MAAAC,OAAA;IAAA;IAAAC,eAAA;IAAA5B,IAAA;EAAA;EAAA,IAAA6B,QAAA,GAAA5B,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAA0B,QAAA,CAAA9B,IAAA,KAAA8B,QAAA,CAAA9B,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAA6B,QAAA,CAAA9B,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAA0B,cAAA,GAAAD,QAAA,CAAA9B,IAAA;EAAA;IAWA;IAAAD,cAAA,YAAAA,CAAA;MAAA,OAAAgC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAhC,cAAA;AAAAA,cAAA,GAAAoB,CAAA;;;;;;AAIAa,OAAA,CAAAC,yBAAA,GAAAA,yBAAA;AA+GC;AAAAlC,cAAA,GAAAoB,CAAA;AAMDa,OAAA,CAAAE,wBAAA,GAAAA,wBAAA;AA0BC;AAAAnC,cAAA,GAAAoB,CAAA;AAMDa,OAAA,CAAAG,wBAAA,GAAAA,wBAAA;AAqBC;AAAApC,cAAA,GAAAoB,CAAA;AAMDa,OAAA,CAAAI,uCAAA,GAAAA,uCAAA;AA2CC;AAAArC,cAAA,GAAAoB,CAAA;AAKDa,OAAA,CAAAK,0BAAA,GAAAA,0BAAA;AAxOA,MAAAC,0BAAA;AAAA;AAAA,CAAAvC,cAAA,GAAAoB,CAAA,OAAAoB,OAAA;AACA,MAAAC,yBAAA;AAAA;AAAA,CAAAzC,cAAA,GAAAoB,CAAA,OAAAoB,OAAA;AAGA;;;;AAIA,SAAgBN,yBAAyBA,CAAA;EAAA;EAAAlC,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EACvCsB,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;EAEpE;EACA,MAAMC,QAAQ;EAAA;EAAA,CAAA5C,cAAA,GAAAoB,CAAA,OAAkB,CAC9B;IACEyB,EAAE,EAAE,WAAW;IACf5B,IAAI,EAAE,UAAU;IAChB6B,SAAS,EAAE,OAAO;IAClBC,MAAM,EAAE,EAAE;IACVC,QAAQ,EAAE,EAAE;IACZC,OAAO,EAAE,IAAI;IACbC,QAAQ,EAAE;GACX,EACD;IACEL,EAAE,EAAE,WAAW;IACf5B,IAAI,EAAE,SAAS;IACf6B,SAAS,EAAE,OAAO;IAClBE,QAAQ,EAAE,EAAE;IACZC,OAAO,EAAE,IAAI;IACbC,QAAQ,EAAE,kBAAkB;IAC5BC,aAAa,EAAE;MACbL,SAAS,EAAE,OAAO;MAClBM,WAAW,EAAE,OAAO;MACpBC,OAAO,EAAE,oBAAoB;MAC7BC,UAAU,EAAE;QAAEC,wBAAwB,EAAE;MAAG;;GAE9C,EACD;IACEV,EAAE,EAAE,WAAW;IACf5B,IAAI,EAAE,UAAU;IAChB6B,SAAS,EAAE,OAAO;IAClBC,MAAM,EAAE,EAAE;IACVC,QAAQ,EAAE,EAAE;IACZC,OAAO,EAAE,IAAI;IACbC,QAAQ,EAAE;GACX,CACF;EAED;EACA,MAAMM,cAAc;EAAA;EAAA,CAAAxD,cAAA,GAAAoB,CAAA,QAAG;IACrBwB,QAAQ;IACRa,UAAU,EAAE,QAAiB;IAC7BC,gBAAgB,EAAE;MAChBC,WAAW,EAAE,EAAE;MACfC,kBAAkB,EAAE,KAAK;MACzBC,QAAQ,EAAE;KACX;IACDC,kBAAkB,EAAE;MAClBC,gBAAgB,EAAE,KAAK;MACvBC,eAAe,EAAE,IAAI;MACrBC,YAAY,EAAE;;GAEjB;EAED,MAAMC,cAAc;EAAA;EAAA,CAAAlE,cAAA,GAAAoB,CAAA,QAAGmB,0BAAA,CAAA4B,wBAAwB,CAACC,uBAAuB,CAACZ,cAAc,CAAC;EAAC;EAAAxD,cAAA,GAAAoB,CAAA;EAExFsB,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;EAAC;EAAA3C,cAAA,GAAAoB,CAAA;EACjEsB,OAAO,CAACC,GAAG,CAAC,0BAA0BuB,cAAc,CAACG,iBAAiB,QAAQ,CAAC;EAAC;EAAArE,cAAA,GAAAoB,CAAA;EAChFsB,OAAO,CAACC,GAAG,CAAC,oBAAoBuB,cAAc,CAACI,YAAY,QAAQ,CAAC;EAAC;EAAAtE,cAAA,GAAAoB,CAAA;EACrEsB,OAAO,CAACC,GAAG,CAAC,mBAAmBuB,cAAc,CAACK,WAAW,QAAQ,CAAC;EAElE;EACA,MAAMC,gBAAgB;EAAA;EAAA,CAAAxE,cAAA,GAAAoB,CAAA,QAAkBwB,QAAQ,CAAC6B,GAAG,CAACC,OAAO,IAAK;IAAA;IAAA1E,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAAA;MAC/D,GAAGsD,OAAO;MACVf,WAAW,EAAE,GAAG;MAChBgB,SAAS,EAAE,IAAI;MACfd,QAAQ,EAAE,EAAE;MACZe,WAAW,EAAE,EAAE;MACfC,gBAAgB,EAAE;KACnB;GAAC,CAAC;EAEH,MAAMC,cAAc;EAAA;EAAA,CAAA9E,cAAA,GAAAoB,CAAA,QAAG;IACrBwB,QAAQ,EAAE4B,gBAAgB;IAC1Bf,UAAU,EAAE,QAAiB;IAC7BC,gBAAgB,EAAE;MAChBC,WAAW,EAAE,GAAG;MAChBC,kBAAkB,EAAE,KAAK;MAAE;MAC3BmB,QAAQ,EAAE,IAAI;MACdlB,QAAQ,EAAE;KACX;IACDC,kBAAkB,EAAE;MAClBC,gBAAgB,EAAE,IAAI;MACtBC,eAAe,EAAE,IAAI;MACrBC,YAAY,EAAE;;GAEjB;EAED,MAAMe,cAAc;EAAA;EAAA,CAAAhF,cAAA,GAAAoB,CAAA,QAAGmB,0BAAA,CAAA4B,wBAAwB,CAACc,+BAA+B,CAACH,cAAc,CAAC;EAAC;EAAA9E,cAAA,GAAAoB,CAAA;EAEhGsB,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;EAAC;EAAA3C,cAAA,GAAAoB,CAAA;EACzEsB,OAAO,CAACC,GAAG,CAAC,0BAA0BqC,cAAc,CAACX,iBAAiB,QAAQ,CAAC;EAAC;EAAArE,cAAA,GAAAoB,CAAA;EAChFsB,OAAO,CAACC,GAAG,CAAC,oBAAoBqC,cAAc,CAACV,YAAY,QAAQ,CAAC;EAAC;EAAAtE,cAAA,GAAAoB,CAAA;EACrEsB,OAAO,CAACC,GAAG,CAAC,mBAAmBqC,cAAc,CAACT,WAAW,QAAQ,CAAC;EAAC;EAAAvE,cAAA,GAAAoB,CAAA;EACnEsB,OAAO,CAACC,GAAG,CAAC,qBAAqBqC,cAAc,CAACE,aAAa,QAAQ,CAAC;EAEtE;EACA,MAAMC,kBAAkB;EAAA;EAAA,CAAAnF,cAAA,GAAAoB,CAAA,QAAG4D,cAAc,CAACX,iBAAiB,GAAGH,cAAc,CAACG,iBAAiB;EAC9F,MAAMe,eAAe;EAAA;EAAA,CAAApF,cAAA,GAAAoB,CAAA,QAAI+D,kBAAkB,GAAGjB,cAAc,CAACG,iBAAiB,GAAG,GAAG,CAAC;EAAC;EAAArE,cAAA,GAAAoB,CAAA;EAEtFsB,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;EAAC;EAAA3C,cAAA,GAAAoB,CAAA;EAC7BsB,OAAO,CAACC,GAAG,CAAC,0BAA0BwC,kBAAkB,CAACE,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC;EAAC;EAAArF,cAAA,GAAAoB,CAAA;EAC7EsB,OAAO,CAACC,GAAG,CAAC,uBAAuByC,eAAe,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;EAAC;EAAArF,cAAA,GAAAoB,CAAA;EAClEsB,OAAO,CAACC,GAAG,CAAC,2BAA2BqC,cAAc,CAACM,aAAa,CAACC,mBAAmB,GAAG,CAAC;EAAC;EAAAvF,cAAA,GAAAoB,CAAA;EAE5F,IAAI4D,cAAc,CAACQ,QAAQ,CAACzC,MAAM,GAAG,CAAC,EAAE;IAAA;IAAA/C,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAoB,CAAA;IACtCsB,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;IAAC;IAAA3C,cAAA,GAAAoB,CAAA;IAC3B4D,cAAc,CAACQ,QAAQ,CAACC,OAAO,CAACC,OAAO,IAAI;MAAA;MAAA1F,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAAA,OAAAsB,OAAO,CAACC,GAAG,CAAC,OAAO+C,OAAO,EAAE,CAAC;IAAD,CAAC,CAAC;EAC3E,CAAC;EAAA;EAAA;IAAA1F,cAAA,GAAAsB,CAAA;EAAA;EAAAtB,cAAA,GAAAoB,CAAA;EAED,OAAO;IAAE8C,cAAc;IAAEc;EAAc,CAAE;AAC3C;AAEA;;;;AAIA,SAAgB7C,wBAAwBA,CAAA;EAAA;EAAAnC,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EACtCsB,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;EAEvD,MAAMgD,UAAU;EAAA;EAAA,CAAA3F,cAAA,GAAAoB,CAAA,QAAoB,CAClC;IAAEuC,WAAW,EAAE,EAAE;IAAEoB,QAAQ,EAAE,CAAC;IAAElB,QAAQ,EAAE;EAAE,CAAE;EAAO;EACrD;IAAEF,WAAW,EAAE,GAAG;IAAEoB,QAAQ,EAAE,CAAC;IAAElB,QAAQ,EAAE;EAAE,CAAE;EAAM;EACrD;IAAEF,WAAW,EAAE,EAAE;IAAEoB,QAAQ,EAAE,IAAI;IAAElB,QAAQ,EAAE;EAAE,CAAE;EAAI;EACrD;IAAEF,WAAW,EAAE,EAAE;IAAEoB,QAAQ,EAAE,CAAC;IAAElB,QAAQ,EAAE;EAAE,CAAE;EAAO;EACrD;IAAEF,WAAW,EAAE,GAAG;IAAEoB,QAAQ,EAAE,IAAI;IAAElB,QAAQ,EAAE;EAAE,CAAE,CAAG;EAAA,CACtD;EAAC;EAAA7D,cAAA,GAAAoB,CAAA;EAEFuE,UAAU,CAACF,OAAO,CAAC,CAACG,SAAS,EAAEC,KAAK,KAAI;IAAA;IAAA7F,cAAA,GAAAqB,CAAA;IACtC,MAAMyE,QAAQ;IAAA;IAAA,CAAA9F,cAAA,GAAAoB,CAAA,QAAGqB,yBAAA,CAAAsD,uBAAuB,CAACC,sBAAsB,CAACJ,SAAS,CAAC;IAAC;IAAA5F,cAAA,GAAAoB,CAAA;IAE3EsB,OAAO,CAACC,GAAG,CAAC,aAAakD,KAAK,GAAG,CAAC,KAAKD,SAAS,CAACjC,WAAW,OAAOiC,SAAS,CAACb,QAAQ,QAAQa,SAAS,CAAC/B,QAAQ,MAAM,CAAC;IAAC;IAAA7D,cAAA,GAAAoB,CAAA;IACvHsB,OAAO,CAACC,GAAG,CAAC,kBAAkBmD,QAAQ,CAACG,OAAO,CAACZ,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC;IAAC;IAAArF,cAAA,GAAAoB,CAAA;IACpEsB,OAAO,CAACC,GAAG,CAAC,6BAA6BmD,QAAQ,CAACI,iBAAiB,CAACvC,WAAW,CAAC0B,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;IAAC;IAAArF,cAAA,GAAAoB,CAAA;IAC9FsB,OAAO,CAACC,GAAG,CAAC,0BAA0BmD,QAAQ,CAACI,iBAAiB,CAACnB,QAAQ,CAACM,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;IAAC;IAAArF,cAAA,GAAAoB,CAAA;IACxFsB,OAAO,CAACC,GAAG,CAAC,0BAA0BmD,QAAQ,CAACI,iBAAiB,CAACrC,QAAQ,CAACwB,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;IAAC;IAAArF,cAAA,GAAAoB,CAAA;IACxFsB,OAAO,CAACC,GAAG,CAAC,0BAA0BmD,QAAQ,CAACI,iBAAiB,CAACC,QAAQ,CAACd,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;IAAC;IAAArF,cAAA,GAAAoB,CAAA;IAExF,IAAI0E,QAAQ,CAACN,QAAQ,CAACzC,MAAM,GAAG,CAAC,EAAE;MAAA;MAAA/C,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAChCsB,OAAO,CAACC,GAAG,CAAC,eAAemD,QAAQ,CAACN,QAAQ,CAACY,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IAC5D,CAAC;IAAA;IAAA;MAAApG,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IACDsB,OAAO,CAACC,GAAG,CAAC,EAAE,CAAC;EACjB,CAAC,CAAC;AACJ;AAEA;;;;AAIA,SAAgBP,wBAAwBA,CAAA;EAAA;EAAApC,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EACtCsB,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;EAEjD,MAAM0D,SAAS;EAAA;EAAA,CAAArG,cAAA,GAAAoB,CAAA,QAAG,CAAC,kBAAkB,EAAE,UAAU,EAAE,iBAAiB,EAAE,eAAe,CAAC;EACtF,MAAMkF,IAAI;EAAA;EAAA,CAAAtG,cAAA,GAAAoB,CAAA,QAAG,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EAAC;EAAApB,cAAA,GAAAoB,CAAA;EAEhCiF,SAAS,CAACZ,OAAO,CAACvC,QAAQ,IAAG;IAAA;IAAAlD,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAC3BsB,OAAO,CAACC,GAAG,CAAC,aAAaO,QAAQ,CAACqD,WAAW,EAAE,EAAE,CAAC;IAAC;IAAAvG,cAAA,GAAAoB,CAAA;IAEnDkF,IAAI,CAACb,OAAO,CAACe,GAAG,IAAG;MAAA;MAAAxG,cAAA,GAAAqB,CAAA;MACjB,MAAMoF,SAAS;MAAA;MAAA,CAAAzG,cAAA,GAAAoB,CAAA,QAAGqB,yBAAA,CAAAsD,uBAAuB,CAACW,4BAA4B,CACpExD,QAAQ,EACRsD,GAAG,EACH,MAAM,CACP;MAED,MAAMG,QAAQ;MAAA;MAAA,CAAA3G,cAAA,GAAAoB,CAAA,QAAGoF,GAAG,KAAK,CAAC;MAAA;MAAA,CAAAxG,cAAA,GAAAsB,CAAA,UAAG,KAAK;MAAA;MAAA,CAAAtB,cAAA,GAAAsB,CAAA,UAAG,GAAGkF,GAAG,QAAQ;MAAC;MAAAxG,cAAA,GAAAoB,CAAA;MACpDsB,OAAO,CAACC,GAAG,CAAC,KAAKgE,QAAQ,KAAKF,SAAS,CAACG,kBAAkB,CAACvB,OAAO,CAAC,CAAC,CAAC,gBAAgBoB,SAAS,CAACI,WAAW,CAACxB,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IAC3H,CAAC,CAAC;IAAC;IAAArF,cAAA,GAAAoB,CAAA;IACHsB,OAAO,CAACC,GAAG,CAAC,EAAE,CAAC;EACjB,CAAC,CAAC;AACJ;AAEA;;;;AAIA,SAAgBN,uCAAuCA,CAAA;EAAA;EAAArC,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EACrDsB,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;EAEzD,MAAMmE,UAAU;EAAA;EAAA,CAAA9G,cAAA,GAAAoB,CAAA,QAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACtD,MAAMuE,UAAU;EAAA;EAAA,CAAA3F,cAAA,GAAAoB,CAAA,QAAkB;IAChCuC,WAAW,EAAE,EAAE;IACfoB,QAAQ,EAAE,IAAI;IACdlB,QAAQ,EAAE;GACX;EAAC;EAAA7D,cAAA,GAAAoB,CAAA;EAEFsB,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;EAAC;EAAA3C,cAAA,GAAAoB,CAAA;EAChEsB,OAAO,CAACC,GAAG,CAAC,+DAA+D,CAAC;EAAC;EAAA3C,cAAA,GAAAoB,CAAA;EAC7EsB,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC;EAAC;EAAA3C,cAAA,GAAAoB,CAAA;EAE5E0F,UAAU,CAACrB,OAAO,CAACsB,QAAQ,IAAG;IAAA;IAAA/G,cAAA,GAAAqB,CAAA;IAC5B;IACA,MAAM2F,WAAW;IAAA;IAAA,CAAAhH,cAAA,GAAAoB,CAAA,QAAGqB,yBAAA,CAAAsD,uBAAuB,CAACkB,yBAAyB,CAAC;MACpEF,QAAQ;MACRG,aAAa,EAAEvB,UAAU;MACzBwB,QAAQ,EAAE;KACX,CAAC;IAEF;IACA,MAAMC,aAAa;IAAA;IAAA,CAAApH,cAAA,GAAAoB,CAAA,QAAGqB,yBAAA,CAAAsD,uBAAuB,CAACkB,yBAAyB,CAAC;MACtEF,QAAQ;MACRG,aAAa,EAAEvB,UAAU;MACzBwB,QAAQ,EAAE;KACX,CAAC;IAEF,MAAME,UAAU;IAAA;IAAA,CAAArH,cAAA,GAAAoB,CAAA,QAAGkG,IAAI,CAACC,GAAG,CAACP,WAAW,CAACQ,gBAAgB,GAAGJ,aAAa,CAACI,gBAAgB,CAAC;IAAC;IAAAxH,cAAA,GAAAoB,CAAA;IAE3FsB,OAAO,CAACC,GAAG,CACT,GAAGoE,QAAQ,CAACU,QAAQ,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,KAAK,GACxC,GAAGV,WAAW,CAACQ,gBAAgB,CAACnC,OAAO,CAAC,CAAC,CAAC,CAACqC,QAAQ,CAAC,EAAE,CAAC,KAAK,GAC5D,GAAGN,aAAa,CAACI,gBAAgB,CAACnC,OAAO,CAAC,CAAC,CAAC,CAACqC,QAAQ,CAAC,EAAE,CAAC,KAAK,GAC9D,GAAGL,UAAU,CAAChC,OAAO,CAAC,CAAC,CAAC,EAAE,CAC3B;EACH,CAAC,CAAC;EAAC;EAAArF,cAAA,GAAAoB,CAAA;EAEHsB,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;EAAC;EAAA3C,cAAA,GAAAoB,CAAA;EACvDsB,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;EAAC;EAAA3C,cAAA,GAAAoB,CAAA;EAC1DsB,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;EAAC;EAAA3C,cAAA,GAAAoB,CAAA;EAC5DsB,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;AACnE;AAEA;;;AAGA,SAAgBL,0BAA0BA,CAAA;EAAA;EAAAtC,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EACxCsB,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;EAAC;EAAA3C,cAAA,GAAAoB,CAAA;EAC7DsB,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;EAAC;EAAA3C,cAAA,GAAAoB,CAAA;EAEhE,IAAI;IAAA;IAAApB,cAAA,GAAAoB,CAAA;IACF;IACAc,yBAAyB,EAAE;IAAC;IAAAlC,cAAA,GAAAoB,CAAA;IAC5Be,wBAAwB,EAAE;IAAC;IAAAnC,cAAA,GAAAoB,CAAA;IAC3BgB,wBAAwB,EAAE;IAAC;IAAApC,cAAA,GAAAoB,CAAA;IAC3BiB,uCAAuC,EAAE;IAAC;IAAArC,cAAA,GAAAoB,CAAA;IAE1CsB,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;IAAC;IAAA3C,cAAA,GAAAoB,CAAA;IAChEsB,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;IAAC;IAAA3C,cAAA,GAAAoB,CAAA;IACpDsB,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;EAE/D,CAAC,CAAC,OAAOgF,KAAK,EAAE;IAAA;IAAA3H,cAAA,GAAAoB,CAAA;IACdsB,OAAO,CAACiF,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;EACjD;AACF;AAEA;AAAA;AAAA3H,cAAA,GAAAoB,CAAA;AACA,IAAIoB,OAAO,CAACoF,IAAI,KAAKC,MAAM,EAAE;EAAA;EAAA7H,cAAA,GAAAsB,CAAA;EAAAtB,cAAA,GAAAoB,CAAA;EAC3BkB,0BAA0B,EAAE;AAC9B,CAAC;AAAA;AAAA;EAAAtC,cAAA,GAAAsB,CAAA;AAAA", "ignoreList": []}