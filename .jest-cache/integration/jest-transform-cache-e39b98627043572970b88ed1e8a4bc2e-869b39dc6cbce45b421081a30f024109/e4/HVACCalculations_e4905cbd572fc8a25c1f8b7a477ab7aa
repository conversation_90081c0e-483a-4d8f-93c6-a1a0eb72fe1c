9a9c33aaa1975a715437ae10ac9928aa
"use strict";

/**
 * HVAC Calculations Utility
 *
 * Consolidates HVAC calculation logic that was duplicated across
 * frontend utilities, backend services, and validation modules.
 */
/* istanbul ignore next */
function cov_1suuup29tp() {
  var path = "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\shared\\calculations\\HVACCalculations.ts";
  var hash = "cbdc8ed5e2a7a032f6b9f96351249abc6b936283";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\shared\\calculations\\HVACCalculations.ts",
    statementMap: {
      "0": {
        start: {
          line: 8,
          column: 0
        },
        end: {
          line: 8,
          column: 62
        }
      },
      "1": {
        start: {
          line: 9,
          column: 0
        },
        end: {
          line: 9,
          column: 34
        }
      },
      "2": {
        start: {
          line: 20,
          column: 8
        },
        end: {
          line: 21,
          column: 21
        }
      },
      "3": {
        start: {
          line: 21,
          column: 12
        },
        end: {
          line: 21,
          column: 21
        }
      },
      "4": {
        start: {
          line: 24,
          column: 27
        },
        end: {
          line: 24,
          column: 82
        }
      },
      "5": {
        start: {
          line: 26,
          column: 29
        },
        end: {
          line: 26,
          column: 67
        }
      },
      "6": {
        start: {
          line: 27,
          column: 8
        },
        end: {
          line: 27,
          column: 41
        }
      },
      "7": {
        start: {
          line: 34,
          column: 8
        },
        end: {
          line: 36,
          column: 9
        }
      },
      "8": {
        start: {
          line: 35,
          column: 12
        },
        end: {
          line: 35,
          column: 44
        }
      },
      "9": {
        start: {
          line: 37,
          column: 8
        },
        end: {
          line: 41,
          column: 9
        }
      },
      "10": {
        start: {
          line: 38,
          column: 38
        },
        end: {
          line: 38,
          column: 48
        }
      },
      "11": {
        start: {
          line: 40,
          column: 12
        },
        end: {
          line: 40,
          column: 90
        }
      },
      "12": {
        start: {
          line: 42,
          column: 8
        },
        end: {
          line: 46,
          column: 9
        }
      },
      "13": {
        start: {
          line: 44,
          column: 25
        },
        end: {
          line: 44,
          column: 83
        }
      },
      "14": {
        start: {
          line: 45,
          column: 12
        },
        end: {
          line: 45,
          column: 49
        }
      },
      "15": {
        start: {
          line: 47,
          column: 8
        },
        end: {
          line: 47,
          column: 17
        }
      },
      "16": {
        start: {
          line: 54,
          column: 102
        },
        end: {
          line: 54,
          column: 108
        }
      },
      "17": {
        start: {
          line: 55,
          column: 8
        },
        end: {
          line: 56,
          column: 21
        }
      },
      "18": {
        start: {
          line: 56,
          column: 12
        },
        end: {
          line: 56,
          column: 21
        }
      },
      "19": {
        start: {
          line: 58,
          column: 35
        },
        end: {
          line: 58,
          column: 42
        }
      },
      "20": {
        start: {
          line: 59,
          column: 31
        },
        end: {
          line: 59,
          column: 80
        }
      },
      "21": {
        start: {
          line: 61,
          column: 34
        },
        end: {
          line: 61,
          column: 61
        }
      },
      "22": {
        start: {
          line: 62,
          column: 31
        },
        end: {
          line: 62,
          column: 94
        }
      },
      "23": {
        start: {
          line: 64,
          column: 33
        },
        end: {
          line: 64,
          column: 85
        }
      },
      "24": {
        start: {
          line: 65,
          column: 8
        },
        end: {
          line: 65,
          column: 82
        }
      },
      "25": {
        start: {
          line: 71,
          column: 8
        },
        end: {
          line: 79,
          column: 9
        }
      },
      "26": {
        start: {
          line: 73,
          column: 12
        },
        end: {
          line: 73,
          column: 39
        }
      },
      "27": {
        start: {
          line: 77,
          column: 26
        },
        end: {
          line: 77,
          column: 100
        }
      },
      "28": {
        start: {
          line: 78,
          column: 12
        },
        end: {
          line: 78,
          column: 45
        }
      },
      "29": {
        start: {
          line: 89,
          column: 28
        },
        end: {
          line: 89,
          column: 48
        }
      },
      "30": {
        start: {
          line: 91,
          column: 28
        },
        end: {
          line: 91,
          column: 33
        }
      },
      "31": {
        start: {
          line: 92,
          column: 30
        },
        end: {
          line: 92,
          column: 76
        }
      },
      "32": {
        start: {
          line: 94,
          column: 35
        },
        end: {
          line: 94,
          column: 110
        }
      },
      "33": {
        start: {
          line: 95,
          column: 8
        },
        end: {
          line: 95,
          column: 50
        }
      },
      "34": {
        start: {
          line: 102,
          column: 28
        },
        end: {
          line: 102,
          column: 54
        }
      },
      "35": {
        start: {
          line: 103,
          column: 8
        },
        end: {
          line: 103,
          column: 67
        }
      },
      "36": {
        start: {
          line: 109,
          column: 8
        },
        end: {
          line: 111,
          column: 9
        }
      },
      "37": {
        start: {
          line: 110,
          column: 12
        },
        end: {
          line: 110,
          column: 63
        }
      },
      "38": {
        start: {
          line: 112,
          column: 21
        },
        end: {
          line: 112,
          column: 39
        }
      },
      "39": {
        start: {
          line: 113,
          column: 8
        },
        end: {
          line: 126,
          column: 9
        }
      },
      "40": {
        start: {
          line: 114,
          column: 29
        },
        end: {
          line: 114,
          column: 63
        }
      },
      "41": {
        start: {
          line: 115,
          column: 12
        },
        end: {
          line: 115,
          column: 61
        }
      },
      "42": {
        start: {
          line: 119,
          column: 27
        },
        end: {
          line: 119,
          column: 51
        }
      },
      "43": {
        start: {
          line: 120,
          column: 26
        },
        end: {
          line: 120,
          column: 36
        }
      },
      "44": {
        start: {
          line: 121,
          column: 12
        },
        end: {
          line: 125,
          column: 14
        }
      },
      "45": {
        start: {
          line: 132,
          column: 25
        },
        end: {
          line: 132,
          column: 27
        }
      },
      "46": {
        start: {
          line: 133,
          column: 23
        },
        end: {
          line: 133,
          column: 25
        }
      },
      "47": {
        start: {
          line: 134,
          column: 32
        },
        end: {
          line: 134,
          column: 34
        }
      },
      "48": {
        start: {
          line: 136,
          column: 8
        },
        end: {
          line: 141,
          column: 9
        }
      },
      "49": {
        start: {
          line: 137,
          column: 12
        },
        end: {
          line: 137,
          column: 58
        }
      },
      "50": {
        start: {
          line: 139,
          column: 13
        },
        end: {
          line: 141,
          column: 9
        }
      },
      "51": {
        start: {
          line: 140,
          column: 12
        },
        end: {
          line: 140,
          column: 70
        }
      },
      "52": {
        start: {
          line: 143,
          column: 8
        },
        end: {
          line: 156,
          column: 9
        }
      },
      "53": {
        start: {
          line: 144,
          column: 27
        },
        end: {
          line: 144,
          column: 60
        }
      },
      "54": {
        start: {
          line: 145,
          column: 12
        },
        end: {
          line: 155,
          column: 13
        }
      },
      "55": {
        start: {
          line: 146,
          column: 16
        },
        end: {
          line: 146,
          column: 137
        }
      },
      "56": {
        start: {
          line: 148,
          column: 17
        },
        end: {
          line: 155,
          column: 13
        }
      },
      "57": {
        start: {
          line: 149,
          column: 16
        },
        end: {
          line: 154,
          column: 17
        }
      },
      "58": {
        start: {
          line: 150,
          column: 20
        },
        end: {
          line: 150,
          column: 140
        }
      },
      "59": {
        start: {
          line: 153,
          column: 20
        },
        end: {
          line: 153,
          column: 140
        }
      },
      "60": {
        start: {
          line: 158,
          column: 8
        },
        end: {
          line: 162,
          column: 9
        }
      },
      "61": {
        start: {
          line: 159,
          column: 12
        },
        end: {
          line: 161,
          column: 13
        }
      },
      "62": {
        start: {
          line: 160,
          column: 16
        },
        end: {
          line: 160,
          column: 92
        }
      },
      "63": {
        start: {
          line: 164,
          column: 8
        },
        end: {
          line: 168,
          column: 9
        }
      },
      "64": {
        start: {
          line: 165,
          column: 12
        },
        end: {
          line: 167,
          column: 13
        }
      },
      "65": {
        start: {
          line: 166,
          column: 16
        },
        end: {
          line: 166,
          column: 92
        }
      },
      "66": {
        start: {
          line: 170,
          column: 8
        },
        end: {
          line: 172,
          column: 9
        }
      },
      "67": {
        start: {
          line: 171,
          column: 12
        },
        end: {
          line: 171,
          column: 95
        }
      },
      "68": {
        start: {
          line: 173,
          column: 8
        },
        end: {
          line: 175,
          column: 9
        }
      },
      "69": {
        start: {
          line: 174,
          column: 12
        },
        end: {
          line: 174,
          column: 108
        }
      },
      "70": {
        start: {
          line: 176,
          column: 8
        },
        end: {
          line: 181,
          column: 10
        }
      },
      "71": {
        start: {
          line: 187,
          column: 40
        },
        end: {
          line: 187,
          column: 79
        }
      },
      "72": {
        start: {
          line: 188,
          column: 41
        },
        end: {
          line: 188,
          column: 81
        }
      },
      "73": {
        start: {
          line: 189,
          column: 31
        },
        end: {
          line: 189,
          column: 91
        }
      },
      "74": {
        start: {
          line: 190,
          column: 8
        },
        end: {
          line: 190,
          column: 65
        }
      },
      "75": {
        start: {
          line: 196,
          column: 8
        },
        end: {
          line: 196,
          column: 55
        }
      },
      "76": {
        start: {
          line: 203,
          column: 8
        },
        end: {
          line: 204,
          column: 22
        }
      },
      "77": {
        start: {
          line: 204,
          column: 12
        },
        end: {
          line: 204,
          column: 22
        }
      },
      "78": {
        start: {
          line: 205,
          column: 8
        },
        end: {
          line: 206,
          column: 22
        }
      },
      "79": {
        start: {
          line: 206,
          column: 12
        },
        end: {
          line: 206,
          column: 22
        }
      },
      "80": {
        start: {
          line: 207,
          column: 8
        },
        end: {
          line: 208,
          column: 22
        }
      },
      "81": {
        start: {
          line: 208,
          column: 12
        },
        end: {
          line: 208,
          column: 22
        }
      },
      "82": {
        start: {
          line: 209,
          column: 8
        },
        end: {
          line: 210,
          column: 22
        }
      },
      "83": {
        start: {
          line: 210,
          column: 12
        },
        end: {
          line: 210,
          column: 22
        }
      },
      "84": {
        start: {
          line: 211,
          column: 8
        },
        end: {
          line: 212,
          column: 22
        }
      },
      "85": {
        start: {
          line: 212,
          column: 12
        },
        end: {
          line: 212,
          column: 22
        }
      },
      "86": {
        start: {
          line: 213,
          column: 8
        },
        end: {
          line: 214,
          column: 22
        }
      },
      "87": {
        start: {
          line: 214,
          column: 12
        },
        end: {
          line: 214,
          column: 22
        }
      },
      "88": {
        start: {
          line: 215,
          column: 8
        },
        end: {
          line: 215,
          column: 18
        }
      },
      "89": {
        start: {
          line: 218,
          column: 0
        },
        end: {
          line: 218,
          column: 44
        }
      },
      "90": {
        start: {
          line: 220,
          column: 0
        },
        end: {
          line: 220,
          column: 46
        }
      },
      "91": {
        start: {
          line: 221,
          column: 0
        },
        end: {
          line: 221,
          column: 43
        }
      },
      "92": {
        start: {
          line: 222,
          column: 0
        },
        end: {
          line: 222,
          column: 42
        }
      },
      "93": {
        start: {
          line: 223,
          column: 0
        },
        end: {
          line: 223,
          column: 51
        }
      },
      "94": {
        start: {
          line: 225,
          column: 0
        },
        end: {
          line: 241,
          column: 2
        }
      },
      "95": {
        start: {
          line: 245,
          column: 0
        },
        end: {
          line: 258,
          column: 2
        }
      },
      "96": {
        start: {
          line: 247,
          column: 23
        },
        end: {
          line: 247,
          column: 36
        }
      },
      "97": {
        start: {
          line: 248,
          column: 23
        },
        end: {
          line: 248,
          column: 36
        }
      },
      "98": {
        start: {
          line: 250,
          column: 23
        },
        end: {
          line: 250,
          column: 36
        }
      },
      "99": {
        start: {
          line: 251,
          column: 23
        },
        end: {
          line: 251,
          column: 36
        }
      },
      "100": {
        start: {
          line: 253,
          column: 24
        },
        end: {
          line: 253,
          column: 37
        }
      },
      "101": {
        start: {
          line: 254,
          column: 22
        },
        end: {
          line: 254,
          column: 33
        }
      },
      "102": {
        start: {
          line: 256,
          column: 28
        },
        end: {
          line: 256,
          column: 41
        }
      },
      "103": {
        start: {
          line: 257,
          column: 24
        },
        end: {
          line: 257,
          column: 33
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 19,
            column: 4
          },
          end: {
            line: 19,
            column: 5
          }
        },
        loc: {
          start: {
            line: 19,
            column: 87
          },
          end: {
            line: 28,
            column: 5
          }
        },
        line: 19
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 33,
            column: 4
          },
          end: {
            line: 33,
            column: 5
          }
        },
        loc: {
          start: {
            line: 33,
            column: 51
          },
          end: {
            line: 48,
            column: 5
          }
        },
        line: 33
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 53,
            column: 4
          },
          end: {
            line: 53,
            column: 5
          }
        },
        loc: {
          start: {
            line: 53,
            column: 41
          },
          end: {
            line: 66,
            column: 5
          }
        },
        line: 53
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 70,
            column: 4
          },
          end: {
            line: 70,
            column: 5
          }
        },
        loc: {
          start: {
            line: 70,
            column: 70
          },
          end: {
            line: 80,
            column: 5
          }
        },
        line: 70
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 84,
            column: 4
          },
          end: {
            line: 84,
            column: 5
          }
        },
        loc: {
          start: {
            line: 87,
            column: 6
          },
          end: {
            line: 96,
            column: 5
          }
        },
        line: 87
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 100,
            column: 4
          },
          end: {
            line: 100,
            column: 5
          }
        },
        loc: {
          start: {
            line: 100,
            column: 46
          },
          end: {
            line: 104,
            column: 5
          }
        },
        line: 100
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 108,
            column: 4
          },
          end: {
            line: 108,
            column: 5
          }
        },
        loc: {
          start: {
            line: 108,
            column: 65
          },
          end: {
            line: 127,
            column: 5
          }
        },
        line: 108
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 131,
            column: 4
          },
          end: {
            line: 131,
            column: 5
          }
        },
        loc: {
          start: {
            line: 131,
            column: 63
          },
          end: {
            line: 182,
            column: 5
          }
        },
        line: 131
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 186,
            column: 4
          },
          end: {
            line: 186,
            column: 5
          }
        },
        loc: {
          start: {
            line: 186,
            column: 52
          },
          end: {
            line: 191,
            column: 5
          }
        },
        line: 186
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 195,
            column: 4
          },
          end: {
            line: 195,
            column: 5
          }
        },
        loc: {
          start: {
            line: 195,
            column: 42
          },
          end: {
            line: 197,
            column: 5
          }
        },
        line: 195
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 201,
            column: 4
          },
          end: {
            line: 201,
            column: 5
          }
        },
        loc: {
          start: {
            line: 201,
            column: 72
          },
          end: {
            line: 216,
            column: 5
          }
        },
        line: 201
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 247,
            column: 14
          },
          end: {
            line: 247,
            column: 15
          }
        },
        loc: {
          start: {
            line: 247,
            column: 23
          },
          end: {
            line: 247,
            column: 36
          }
        },
        line: 247
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 248,
            column: 14
          },
          end: {
            line: 248,
            column: 15
          }
        },
        loc: {
          start: {
            line: 248,
            column: 23
          },
          end: {
            line: 248,
            column: 36
          }
        },
        line: 248
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 250,
            column: 14
          },
          end: {
            line: 250,
            column: 15
          }
        },
        loc: {
          start: {
            line: 250,
            column: 23
          },
          end: {
            line: 250,
            column: 36
          }
        },
        line: 250
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 251,
            column: 14
          },
          end: {
            line: 251,
            column: 15
          }
        },
        loc: {
          start: {
            line: 251,
            column: 23
          },
          end: {
            line: 251,
            column: 36
          }
        },
        line: 251
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 253,
            column: 14
          },
          end: {
            line: 253,
            column: 15
          }
        },
        loc: {
          start: {
            line: 253,
            column: 24
          },
          end: {
            line: 253,
            column: 37
          }
        },
        line: 253
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 254,
            column: 14
          },
          end: {
            line: 254,
            column: 15
          }
        },
        loc: {
          start: {
            line: 254,
            column: 22
          },
          end: {
            line: 254,
            column: 33
          }
        },
        line: 254
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 256,
            column: 16
          },
          end: {
            line: 256,
            column: 17
          }
        },
        loc: {
          start: {
            line: 256,
            column: 28
          },
          end: {
            line: 256,
            column: 41
          }
        },
        line: 256
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 257,
            column: 16
          },
          end: {
            line: 257,
            column: 17
          }
        },
        loc: {
          start: {
            line: 257,
            column: 24
          },
          end: {
            line: 257,
            column: 33
          }
        },
        line: 257
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 19,
            column: 47
          },
          end: {
            line: 19,
            column: 85
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 19,
            column: 60
          },
          end: {
            line: 19,
            column: 85
          }
        }],
        line: 19
      },
      "1": {
        loc: {
          start: {
            line: 20,
            column: 8
          },
          end: {
            line: 21,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 20,
            column: 8
          },
          end: {
            line: 21,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 20
      },
      "2": {
        loc: {
          start: {
            line: 34,
            column: 8
          },
          end: {
            line: 36,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 34,
            column: 8
          },
          end: {
            line: 36,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 34
      },
      "3": {
        loc: {
          start: {
            line: 35,
            column: 19
          },
          end: {
            line: 35,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 35,
            column: 19
          },
          end: {
            line: 35,
            column: 38
          }
        }, {
          start: {
            line: 35,
            column: 42
          },
          end: {
            line: 35,
            column: 43
          }
        }],
        line: 35
      },
      "4": {
        loc: {
          start: {
            line: 37,
            column: 8
          },
          end: {
            line: 41,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 37,
            column: 8
          },
          end: {
            line: 41,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 37
      },
      "5": {
        loc: {
          start: {
            line: 37,
            column: 12
          },
          end: {
            line: 37,
            column: 87
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 12
          },
          end: {
            line: 37,
            column: 46
          }
        }, {
          start: {
            line: 37,
            column: 50
          },
          end: {
            line: 37,
            column: 66
          }
        }, {
          start: {
            line: 37,
            column: 70
          },
          end: {
            line: 37,
            column: 87
          }
        }],
        line: 37
      },
      "6": {
        loc: {
          start: {
            line: 42,
            column: 8
          },
          end: {
            line: 46,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 42,
            column: 8
          },
          end: {
            line: 46,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 42
      },
      "7": {
        loc: {
          start: {
            line: 42,
            column: 12
          },
          end: {
            line: 42,
            column: 80
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 42,
            column: 12
          },
          end: {
            line: 42,
            column: 39
          }
        }, {
          start: {
            line: 42,
            column: 43
          },
          end: {
            line: 42,
            column: 59
          }
        }, {
          start: {
            line: 42,
            column: 63
          },
          end: {
            line: 42,
            column: 80
          }
        }],
        line: 42
      },
      "8": {
        loc: {
          start: {
            line: 54,
            column: 59
          },
          end: {
            line: 54,
            column: 97
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 54,
            column: 72
          },
          end: {
            line: 54,
            column: 97
          }
        }],
        line: 54
      },
      "9": {
        loc: {
          start: {
            line: 55,
            column: 8
          },
          end: {
            line: 56,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 55,
            column: 8
          },
          end: {
            line: 56,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 55
      },
      "10": {
        loc: {
          start: {
            line: 55,
            column: 12
          },
          end: {
            line: 55,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 55,
            column: 12
          },
          end: {
            line: 55,
            column: 25
          }
        }, {
          start: {
            line: 55,
            column: 29
          },
          end: {
            line: 55,
            column: 42
          }
        }, {
          start: {
            line: 55,
            column: 46
          },
          end: {
            line: 55,
            column: 61
          }
        }],
        line: 55
      },
      "11": {
        loc: {
          start: {
            line: 71,
            column: 8
          },
          end: {
            line: 79,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 71,
            column: 8
          },
          end: {
            line: 79,
            column: 9
          }
        }, {
          start: {
            line: 75,
            column: 13
          },
          end: {
            line: 79,
            column: 9
          }
        }],
        line: 71
      },
      "12": {
        loc: {
          start: {
            line: 84,
            column: 31
          },
          end: {
            line: 84,
            column: 70
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 84,
            column: 45
          },
          end: {
            line: 84,
            column: 70
          }
        }],
        line: 84
      },
      "13": {
        loc: {
          start: {
            line: 85,
            column: 4
          },
          end: {
            line: 85,
            column: 37
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 85,
            column: 15
          },
          end: {
            line: 85,
            column: 37
          }
        }],
        line: 85
      },
      "14": {
        loc: {
          start: {
            line: 86,
            column: 4
          },
          end: {
            line: 86,
            column: 16
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 86,
            column: 15
          },
          end: {
            line: 86,
            column: 16
          }
        }],
        line: 86
      },
      "15": {
        loc: {
          start: {
            line: 108,
            column: 48
          },
          end: {
            line: 108,
            column: 63
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 108,
            column: 56
          },
          end: {
            line: 108,
            column: 63
          }
        }],
        line: 108
      },
      "16": {
        loc: {
          start: {
            line: 109,
            column: 8
          },
          end: {
            line: 111,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 109,
            column: 8
          },
          end: {
            line: 111,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 109
      },
      "17": {
        loc: {
          start: {
            line: 109,
            column: 12
          },
          end: {
            line: 109,
            column: 41
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 109,
            column: 12
          },
          end: {
            line: 109,
            column: 24
          }
        }, {
          start: {
            line: 109,
            column: 28
          },
          end: {
            line: 109,
            column: 41
          }
        }],
        line: 109
      },
      "18": {
        loc: {
          start: {
            line: 113,
            column: 8
          },
          end: {
            line: 126,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 113,
            column: 8
          },
          end: {
            line: 126,
            column: 9
          }
        }, {
          start: {
            line: 117,
            column: 13
          },
          end: {
            line: 126,
            column: 9
          }
        }],
        line: 113
      },
      "19": {
        loc: {
          start: {
            line: 131,
            column: 42
          },
          end: {
            line: 131,
            column: 61
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 131,
            column: 53
          },
          end: {
            line: 131,
            column: 61
          }
        }],
        line: 131
      },
      "20": {
        loc: {
          start: {
            line: 136,
            column: 8
          },
          end: {
            line: 141,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 136,
            column: 8
          },
          end: {
            line: 141,
            column: 9
          }
        }, {
          start: {
            line: 139,
            column: 13
          },
          end: {
            line: 141,
            column: 9
          }
        }],
        line: 136
      },
      "21": {
        loc: {
          start: {
            line: 139,
            column: 13
          },
          end: {
            line: 141,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 139,
            column: 13
          },
          end: {
            line: 141,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 139
      },
      "22": {
        loc: {
          start: {
            line: 143,
            column: 8
          },
          end: {
            line: 156,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 143,
            column: 8
          },
          end: {
            line: 156,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 143
      },
      "23": {
        loc: {
          start: {
            line: 145,
            column: 12
          },
          end: {
            line: 155,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 145,
            column: 12
          },
          end: {
            line: 155,
            column: 13
          }
        }, {
          start: {
            line: 148,
            column: 17
          },
          end: {
            line: 155,
            column: 13
          }
        }],
        line: 145
      },
      "24": {
        loc: {
          start: {
            line: 148,
            column: 17
          },
          end: {
            line: 155,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 148,
            column: 17
          },
          end: {
            line: 155,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 148
      },
      "25": {
        loc: {
          start: {
            line: 149,
            column: 16
          },
          end: {
            line: 154,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 149,
            column: 16
          },
          end: {
            line: 154,
            column: 17
          }
        }, {
          start: {
            line: 152,
            column: 21
          },
          end: {
            line: 154,
            column: 17
          }
        }],
        line: 149
      },
      "26": {
        loc: {
          start: {
            line: 158,
            column: 8
          },
          end: {
            line: 162,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 158,
            column: 8
          },
          end: {
            line: 162,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 158
      },
      "27": {
        loc: {
          start: {
            line: 159,
            column: 12
          },
          end: {
            line: 161,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 159,
            column: 12
          },
          end: {
            line: 161,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 159
      },
      "28": {
        loc: {
          start: {
            line: 159,
            column: 16
          },
          end: {
            line: 159,
            column: 68
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 159,
            column: 16
          },
          end: {
            line: 159,
            column: 40
          }
        }, {
          start: {
            line: 159,
            column: 44
          },
          end: {
            line: 159,
            column: 68
          }
        }],
        line: 159
      },
      "29": {
        loc: {
          start: {
            line: 164,
            column: 8
          },
          end: {
            line: 168,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 164,
            column: 8
          },
          end: {
            line: 168,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 164
      },
      "30": {
        loc: {
          start: {
            line: 165,
            column: 12
          },
          end: {
            line: 167,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 165,
            column: 12
          },
          end: {
            line: 167,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 165
      },
      "31": {
        loc: {
          start: {
            line: 165,
            column: 16
          },
          end: {
            line: 165,
            column: 60
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 165,
            column: 16
          },
          end: {
            line: 165,
            column: 36
          }
        }, {
          start: {
            line: 165,
            column: 40
          },
          end: {
            line: 165,
            column: 60
          }
        }],
        line: 165
      },
      "32": {
        loc: {
          start: {
            line: 170,
            column: 8
          },
          end: {
            line: 172,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 170,
            column: 8
          },
          end: {
            line: 172,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 170
      },
      "33": {
        loc: {
          start: {
            line: 170,
            column: 12
          },
          end: {
            line: 170,
            column: 92
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 170,
            column: 12
          },
          end: {
            line: 170,
            column: 27
          }
        }, {
          start: {
            line: 170,
            column: 31
          },
          end: {
            line: 170,
            column: 92
          }
        }],
        line: 170
      },
      "34": {
        loc: {
          start: {
            line: 173,
            column: 8
          },
          end: {
            line: 175,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 173,
            column: 8
          },
          end: {
            line: 175,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 173
      },
      "35": {
        loc: {
          start: {
            line: 173,
            column: 12
          },
          end: {
            line: 173,
            column: 54
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 173,
            column: 12
          },
          end: {
            line: 173,
            column: 34
          }
        }, {
          start: {
            line: 173,
            column: 38
          },
          end: {
            line: 173,
            column: 54
          }
        }],
        line: 173
      },
      "36": {
        loc: {
          start: {
            line: 201,
            column: 41
          },
          end: {
            line: 201,
            column: 70
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 201,
            column: 52
          },
          end: {
            line: 201,
            column: 70
          }
        }],
        line: 201
      },
      "37": {
        loc: {
          start: {
            line: 203,
            column: 8
          },
          end: {
            line: 204,
            column: 22
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 203,
            column: 8
          },
          end: {
            line: 204,
            column: 22
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 203
      },
      "38": {
        loc: {
          start: {
            line: 205,
            column: 8
          },
          end: {
            line: 206,
            column: 22
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 205,
            column: 8
          },
          end: {
            line: 206,
            column: 22
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 205
      },
      "39": {
        loc: {
          start: {
            line: 207,
            column: 8
          },
          end: {
            line: 208,
            column: 22
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 207,
            column: 8
          },
          end: {
            line: 208,
            column: 22
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 207
      },
      "40": {
        loc: {
          start: {
            line: 209,
            column: 8
          },
          end: {
            line: 210,
            column: 22
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 209,
            column: 8
          },
          end: {
            line: 210,
            column: 22
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 209
      },
      "41": {
        loc: {
          start: {
            line: 211,
            column: 8
          },
          end: {
            line: 212,
            column: 22
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 211,
            column: 8
          },
          end: {
            line: 212,
            column: 22
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 211
      },
      "42": {
        loc: {
          start: {
            line: 213,
            column: 8
          },
          end: {
            line: 214,
            column: 22
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 213,
            column: 8
          },
          end: {
            line: 214,
            column: 22
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 213
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0
    },
    b: {
      "0": [0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0, 0],
      "6": [0, 0],
      "7": [0, 0, 0],
      "8": [0],
      "9": [0, 0],
      "10": [0, 0, 0],
      "11": [0, 0],
      "12": [0],
      "13": [0],
      "14": [0],
      "15": [0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\shared\\calculations\\HVACCalculations.ts",
      mappings: ";AAAA;;;;;GAKG;;;AA+CH;;;GAGG;AACH,MAAa,gBAAgB;IA2B3B;;;OAGG;IACH,MAAM,CAAC,yBAAyB,CAAC,QAAgB,EAAE,aAAqB,IAAI,CAAC,oBAAoB;QAC/F,IAAI,QAAQ,IAAI,CAAC;YAAE,OAAO,CAAC,CAAC;QAE5B,4DAA4D;QAC5D,qDAAqD;QACrD,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,0BAA0B,EAAE,CAAC,CAAC,CAAC;QAE3E,sCAAsC;QACtC,MAAM,YAAY,GAAG,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAAC;QAC5D,OAAO,UAAU,GAAG,YAAY,CAAC;IACnC,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,2BAA2B,CAAC,UAA0B;QAC3D,IAAI,UAAU,CAAC,KAAK,KAAK,OAAO,EAAE,CAAC;YACjC,OAAO,UAAU,CAAC,QAAQ,IAAI,CAAC,CAAC;QAClC,CAAC;QAED,IAAI,UAAU,CAAC,KAAK,KAAK,aAAa,IAAI,UAAU,CAAC,KAAK,IAAI,UAAU,CAAC,MAAM,EAAE,CAAC;YAChF,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,UAAU,CAAC;YACrC,qCAAqC;YACrC,OAAO,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,MAAM,EAAE,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,MAAM,EAAE,IAAI,CAAC,CAAC;QAChF,CAAC;QAED,IAAI,UAAU,CAAC,KAAK,KAAK,MAAM,IAAI,UAAU,CAAC,KAAK,IAAI,UAAU,CAAC,MAAM,EAAE,CAAC;YACzE,4CAA4C;YAC5C,MAAM,IAAI,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACxE,OAAO,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC;QACvC,CAAC;QAED,OAAO,CAAC,CAAC;IACX,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,qBAAqB,CAAC,MAA0B;QACrD,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,GAAG,IAAI,CAAC,oBAAoB,EAAE,GAAG,MAAM,CAAC;QAErG,IAAI,QAAQ,IAAI,CAAC,IAAI,QAAQ,IAAI,CAAC,IAAI,UAAU,IAAI,CAAC;YAAE,OAAO,CAAC,CAAC;QAEhE,4BAA4B;QAC5B,MAAM,kBAAkB,GAAG,OAAO,CAAC,CAAC,uCAAuC;QAC3E,MAAM,cAAc,GAAG,CAAC,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,kBAAkB,GAAG,EAAE,CAAC,CAAC,CAAC,2BAA2B;QAErG,2EAA2E;QAC3E,MAAM,iBAAiB,GAAG,SAAS,GAAG,CAAC,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC,kBAAkB;QACzE,MAAM,cAAc,GAAG,IAAI,CAAC,uBAAuB,CAAC,cAAc,EAAE,iBAAiB,CAAC,CAAC;QAEvF,yCAAyC;QACzC,MAAM,gBAAgB,GAAG,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QAC9E,OAAO,cAAc,GAAG,CAAC,UAAU,GAAG,CAAC,QAAQ,GAAG,EAAE,CAAC,CAAC,GAAG,gBAAgB,CAAC;IAC5E,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,uBAAuB,CAAC,cAAsB,EAAE,iBAAyB;QACtF,IAAI,cAAc,GAAG,IAAI,EAAE,CAAC;YAC1B,eAAe;YACf,OAAO,EAAE,GAAG,cAAc,CAAC;QAC7B,CAAC;aAAM,CAAC;YACN,6CAA6C;YAC7C,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,GAAG,CAAC,CAAC,CAAC;YACzF,OAAO,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACnC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,mBAAmB,CACxB,cAAsB,IAAI,CAAC,oBAAoB,EAAE,KAAK;IACtD,WAAmB,IAAI,CAAC,iBAAiB,EAAE,OAAO;IAClD,WAAmB,CAAC,CAAC,0BAA0B;;QAE/C,iCAAiC;QACjC,MAAM,WAAW,GAAG,WAAW,GAAG,MAAM,CAAC;QAEzC,4BAA4B;QAC5B,MAAM,WAAW,GAAG,KAAK,CAAC,CAAC,8BAA8B;QACzD,MAAM,aAAa,GAAG,CAAC,QAAQ,GAAG,GAAG,CAAC,GAAG,CAAC,WAAW,GAAG,WAAW,CAAC,CAAC,CAAC,sBAAsB;QAE5F,mCAAmC;QACnC,MAAM,kBAAkB,GAAG,CAAC,GAAG,CAAC,KAAK,GAAG,QAAQ,GAAG,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,GAAG,QAAQ,CAAC,CAAC;QAEvG,OAAO,aAAa,GAAG,kBAAkB,CAAC;IAC5C,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,qBAAqB,CAAC,WAAmB;QACtD,iEAAiE;QACjE,MAAM,WAAW,GAAG,CAAC,WAAW,GAAG,EAAE,CAAC,GAAG,CAAC,GAAC,CAAC,CAAC;QAC7C,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,GAAG,CAAC,WAAW,GAAG,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,0BAA0B;IACxF,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,iBAAiB,CAAC,OAAe,EAAE,QAAgB,EAAE,QAAiC,OAAO;QAClG,IAAI,OAAO,IAAI,CAAC,IAAI,QAAQ,IAAI,CAAC,EAAE,CAAC;YAClC,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;QACrD,CAAC;QAED,MAAM,IAAI,GAAG,OAAO,GAAG,QAAQ,CAAC,CAAC,MAAM;QAEvC,IAAI,KAAK,KAAK,OAAO,EAAE,CAAC;YACtB,MAAM,QAAQ,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,oBAAoB;YACzE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;QACnD,CAAC;aAAM,CAAC;YACN,6DAA6D;YAC7D,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,oBAAoB;YAC7D,MAAM,KAAK,GAAG,MAAM,GAAG,CAAC,CAAC;YACzB,OAAO;gBACL,KAAK;gBACL,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;gBACxB,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;aAC3B,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,sBAAsB,CAAC,MAAkB,EAAE,WAA4C,QAAQ;QACpG,MAAM,QAAQ,GAAa,EAAE,CAAC;QAC9B,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,MAAM,eAAe,GAAa,EAAE,CAAC;QAErC,mBAAmB;QACnB,IAAI,MAAM,CAAC,OAAO,IAAI,CAAC,EAAE,CAAC;YACxB,MAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;QAChD,CAAC;aAAM,IAAI,MAAM,CAAC,OAAO,GAAG,KAAK,EAAE,CAAC;YAClC,QAAQ,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;QAC5D,CAAC;QAED,gCAAgC;QAChC,IAAI,MAAM,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YAClC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;YAEjD,IAAI,MAAM,CAAC,QAAQ,GAAG,MAAM,CAAC,GAAG,EAAE,CAAC;gBACjC,QAAQ,CAAC,IAAI,CAAC,YAAY,MAAM,CAAC,QAAQ,wCAAwC,MAAM,CAAC,GAAG,YAAY,QAAQ,QAAQ,CAAC,CAAC;YAC3H,CAAC;iBAAM,IAAI,MAAM,CAAC,QAAQ,GAAG,MAAM,CAAC,GAAG,EAAE,CAAC;gBACxC,IAAI,MAAM,CAAC,QAAQ,GAAG,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC;oBACvC,MAAM,CAAC,IAAI,CAAC,YAAY,MAAM,CAAC,QAAQ,yCAAyC,MAAM,CAAC,GAAG,YAAY,QAAQ,QAAQ,CAAC,CAAC;gBAC1H,CAAC;qBAAM,CAAC;oBACN,QAAQ,CAAC,IAAI,CAAC,YAAY,MAAM,CAAC,QAAQ,uCAAuC,MAAM,CAAC,GAAG,YAAY,QAAQ,QAAQ,CAAC,CAAC;gBAC1H,CAAC;YACH,CAAC;QACH,CAAC;QAED,uBAAuB;QACvB,IAAI,MAAM,CAAC,WAAW,KAAK,SAAS,EAAE,CAAC;YACrC,IAAI,MAAM,CAAC,WAAW,GAAG,CAAC,EAAE,IAAI,MAAM,CAAC,WAAW,GAAG,GAAG,EAAE,CAAC;gBACzD,QAAQ,CAAC,IAAI,CAAC,4DAA4D,CAAC,CAAC;YAC9E,CAAC;QACH,CAAC;QAED,oBAAoB;QACpB,IAAI,MAAM,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YAClC,IAAI,MAAM,CAAC,QAAQ,GAAG,EAAE,IAAI,MAAM,CAAC,QAAQ,GAAG,EAAE,EAAE,CAAC;gBACjD,QAAQ,CAAC,IAAI,CAAC,4DAA4D,CAAC,CAAC;YAC9E,CAAC;QACH,CAAC;QAED,2BAA2B;QAC3B,IAAI,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC;YACrF,eAAe,CAAC,IAAI,CAAC,4DAA4D,CAAC,CAAC;QACrF,CAAC;QAED,IAAI,MAAM,CAAC,OAAO,GAAG,KAAK,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;YAC/C,eAAe,CAAC,IAAI,CAAC,yEAAyE,CAAC,CAAC;QAClG,CAAC;QAED,OAAO;YACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC5B,QAAQ;YACR,MAAM;YACN,eAAe;SAChB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,yBAAyB,CAAC,KAAqB,EAAE,MAAsB;QAC5E,MAAM,uBAAuB,GAAG,IAAI,CAAC,2BAA2B,CAAC,KAAK,CAAC,CAAC;QACxE,MAAM,wBAAwB,GAAG,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,CAAC;QAC1E,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,uBAAuB,GAAG,wBAAwB,CAAC,CAAC;QAEpF,OAAO,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC;IAC3D,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,oBAAoB,CAAC,QAAgB;QAC1C,OAAO,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC;IACjD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,mBAAmB,CAAC,QAAgB,EAAE,WAAmB,kBAAkB;QAChF,4CAA4C;QAC5C,IAAI,QAAQ,IAAI,EAAE;YAAE,OAAO,EAAE,CAAC;QAC9B,IAAI,QAAQ,IAAI,EAAE;YAAE,OAAO,EAAE,CAAC;QAC9B,IAAI,QAAQ,IAAI,EAAE;YAAE,OAAO,EAAE,CAAC;QAC9B,IAAI,QAAQ,IAAI,EAAE;YAAE,OAAO,EAAE,CAAC;QAC9B,IAAI,QAAQ,IAAI,EAAE;YAAE,OAAO,EAAE,CAAC;QAC9B,IAAI,QAAQ,IAAI,EAAE;YAAE,OAAO,EAAE,CAAC;QAC9B,OAAO,EAAE,CAAC,CAAC,mBAAmB;IAChC,CAAC;;AAzPH,4CA+QC;AA7QC,qBAAqB;AACL,qCAAoB,GAAG,KAAK,CAAC,CAAC,gCAAgC;AAC9D,qCAAoB,GAAG,EAAE,CAAC,CAAC,KAAK;AAChC,kCAAiB,GAAG,IAAI,CAAC,CAAC,OAAO;AACjC,2CAA0B,GAAG,IAAI,CAAC,CAAC,qBAAqB;AAExE,mBAAmB;AACH,uBAAM,GAAoB;IACxC,WAAW,EAAE;QACX,MAAM,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE;QAC/B,MAAM,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE;QAC/B,OAAO,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE;KAClC;IACD,oBAAoB,EAAE,GAAG;IACzB,gBAAgB,EAAE,GAAG;IACrB,cAAc,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IACpD,iBAAiB,EAAE;QACjB,gBAAgB,EAAE,MAAM;QACxB,eAAe,EAAE,MAAM;QACvB,QAAQ,EAAE,MAAM;QAChB,GAAG,EAAE,MAAM;QACX,UAAU,EAAE,KAAK;KAClB;CACF,CAAC;AAkOF;;GAEG;AACI,6BAAY,GAAG;IACpB,sBAAsB;IACtB,QAAQ,EAAE,CAAC,GAAW,EAAU,EAAE,CAAC,GAAG,GAAG,OAAO;IAChD,QAAQ,EAAE,CAAC,GAAW,EAAU,EAAE,CAAC,GAAG,GAAG,OAAO;IAEhD,uBAAuB;IACvB,QAAQ,EAAE,CAAC,GAAW,EAAU,EAAE,CAAC,GAAG,GAAG,OAAO;IAChD,QAAQ,EAAE,CAAC,GAAW,EAAU,EAAE,CAAC,GAAG,GAAG,OAAO;IAEhD,uBAAuB;IACvB,QAAQ,EAAE,CAAC,IAAY,EAAU,EAAE,CAAC,IAAI,GAAG,MAAM;IACjD,QAAQ,EAAE,CAAC,EAAU,EAAU,EAAE,CAAC,EAAE,GAAG,MAAM;IAE7C,qBAAqB;IACrB,UAAU,EAAE,CAAC,MAAc,EAAU,EAAE,CAAC,MAAM,GAAG,IAAI;IACrD,UAAU,EAAE,CAAC,EAAU,EAAU,EAAE,CAAC,EAAE,GAAG,IAAI;CAC9C,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\shared\\calculations\\HVACCalculations.ts"],
      sourcesContent: ["/**\n * HVAC Calculations Utility\n * \n * Consolidates HVAC calculation logic that was duplicated across\n * frontend utilities, backend services, and validation modules.\n */\n\nexport interface DuctDimensions {\n  width?: number;\n  height?: number;\n  diameter?: number;\n  shape: 'rectangular' | 'round' | 'oval';\n}\n\nexport interface PressureLossParams {\n  velocity: number;\n  ductLength: number;\n  roughness: number;\n  diameter: number;\n  airDensity?: number;\n}\n\nexport interface HVACParams {\n  airflow: number;\n  velocity?: number;\n  temperature?: number;\n  pressure?: number;\n  humidity?: number;\n  elevation?: number;\n}\n\nexport interface ValidationResult {\n  isValid: boolean;\n  warnings: string[];\n  errors: string[];\n  recommendations: string[];\n}\n\nexport interface VelocityLimits {\n  supply: { min: number; max: number };\n  return: { min: number; max: number };\n  exhaust: { min: number; max: number };\n}\n\nexport interface SMACNAStandards {\n  maxVelocity: VelocityLimits;\n  transitionSlopeRatio: number;\n  elbowRadiusRatio: number;\n  standardGauges: number[];\n  materialRoughness: Record<string, number>;\n}\n\n/**\n * Comprehensive HVAC Calculations Class\n * Consolidates calculation logic from multiple sources\n */\nexport class HVACCalculations {\n  \n  // Standard constants\n  static readonly STANDARD_AIR_DENSITY = 0.075; // lb/ft\xB3 at standard conditions\n  static readonly STANDARD_TEMPERATURE = 70; // \xB0F\n  static readonly STANDARD_PRESSURE = 14.7; // psia\n  static readonly VELOCITY_PRESSURE_CONSTANT = 4005; // for imperial units\n\n  // SMACNA Standards\n  static readonly SMACNA: SMACNAStandards = {\n    maxVelocity: {\n      supply: { min: 500, max: 2500 },\n      return: { min: 300, max: 2000 },\n      exhaust: { min: 1000, max: 4000 }\n    },\n    transitionSlopeRatio: 2.5,\n    elbowRadiusRatio: 1.5,\n    standardGauges: [30, 28, 26, 24, 22, 20, 18, 16, 14],\n    materialRoughness: {\n      galvanized_steel: 0.0005,\n      stainless_steel: 0.0002,\n      aluminum: 0.0003,\n      pvc: 0.0001,\n      fiberglass: 0.003\n    }\n  };\n\n  /**\n   * Calculate velocity pressure from air velocity\n   * Formula: VP = (V/4005)\xB2 for standard air density\n   */\n  static calculateVelocityPressure(velocity: number, airDensity: number = this.STANDARD_AIR_DENSITY): number {\n    if (velocity <= 0) return 0;\n    \n    // Standard formula: VP = \u03C1V\xB2/(2gc) converted to inches w.g.\n    // Simplified for standard conditions: VP = (V/4005)\xB2\n    const standardVP = Math.pow(velocity / this.VELOCITY_PRESSURE_CONSTANT, 2);\n    \n    // Adjust for non-standard air density\n    const densityRatio = airDensity / this.STANDARD_AIR_DENSITY;\n    return standardVP * densityRatio;\n  }\n\n  /**\n   * Calculate equivalent diameter for rectangular ducts\n   * Formula: De = 1.3 * (a*b)^0.625 / (a+b)^0.25\n   */\n  static calculateEquivalentDiameter(dimensions: DuctDimensions): number {\n    if (dimensions.shape === 'round') {\n      return dimensions.diameter || 0;\n    }\n\n    if (dimensions.shape === 'rectangular' && dimensions.width && dimensions.height) {\n      const { width, height } = dimensions;\n      // ASHRAE equivalent diameter formula\n      return 1.3 * Math.pow(width * height, 0.625) / Math.pow(width + height, 0.25);\n    }\n\n    if (dimensions.shape === 'oval' && dimensions.width && dimensions.height) {\n      // Approximate oval as equivalent round duct\n      const area = Math.PI * (dimensions.width / 2) * (dimensions.height / 2);\n      return 2 * Math.sqrt(area / Math.PI);\n    }\n\n    return 0;\n  }\n\n  /**\n   * Calculate pressure loss using Darcy-Weisbach equation\n   * Formula: \u0394P = f * (L/D) * (\u03C1V\xB2/2)\n   */\n  static calculatePressureLoss(params: PressureLossParams): number {\n    const { velocity, ductLength, roughness, diameter, airDensity = this.STANDARD_AIR_DENSITY } = params;\n    \n    if (velocity <= 0 || diameter <= 0 || ductLength <= 0) return 0;\n\n    // Calculate Reynolds number\n    const kinematicViscosity = 1.57e-4; // ft\xB2/s for air at standard conditions\n    const reynoldsNumber = (velocity * diameter) / (kinematicViscosity * 12); // Convert diameter to feet\n\n    // Calculate friction factor using Colebrook-White equation (approximation)\n    const relativeRoughness = roughness / (diameter / 12); // Convert to feet\n    const frictionFactor = this.calculateFrictionFactor(reynoldsNumber, relativeRoughness);\n\n    // Calculate pressure loss in inches w.g.\n    const velocityPressure = this.calculateVelocityPressure(velocity, airDensity);\n    return frictionFactor * (ductLength / (diameter / 12)) * velocityPressure;\n  }\n\n  /**\n   * Calculate friction factor using Swamee-Jain approximation\n   */\n  private static calculateFrictionFactor(reynoldsNumber: number, relativeRoughness: number): number {\n    if (reynoldsNumber < 2300) {\n      // Laminar flow\n      return 64 / reynoldsNumber;\n    } else {\n      // Turbulent flow - Swamee-Jain approximation\n      const term1 = Math.log10(relativeRoughness / 3.7 + 5.74 / Math.pow(reynoldsNumber, 0.9));\n      return 0.25 / Math.pow(term1, 2);\n    }\n  }\n\n  /**\n   * Calculate air density based on temperature, pressure, and humidity\n   */\n  static calculateAirDensity(\n    temperature: number = this.STANDARD_TEMPERATURE, // \xB0F\n    pressure: number = this.STANDARD_PRESSURE, // psia\n    humidity: number = 0 // relative humidity (0-1)\n  ): number {\n    // Convert temperature to Rankine\n    const tempRankine = temperature + 459.67;\n    \n    // Calculate dry air density\n    const gasConstant = 53.35; // ft\xB7lbf/(lbm\xB7\xB0R) for dry air\n    const dryAirDensity = (pressure * 144) / (gasConstant * tempRankine); // Convert psia to psf\n    \n    // Adjust for humidity (simplified)\n    const humidityCorrection = 1 - (0.378 * humidity * this.getSaturationPressure(temperature) / pressure);\n    \n    return dryAirDensity * humidityCorrection;\n  }\n\n  /**\n   * Get saturation pressure for humidity calculations\n   */\n  private static getSaturationPressure(temperature: number): number {\n    // Antoine equation approximation for water vapor pressure (psia)\n    const tempCelsius = (temperature - 32) * 5/9;\n    return Math.exp(16.7 - 4060 / (tempCelsius + 235)) * 0.145; // Convert from Pa to psia\n  }\n\n  /**\n   * Calculate duct sizing based on airflow and velocity\n   */\n  static calculateDuctSize(airflow: number, velocity: number, shape: 'rectangular' | 'round' = 'round'): DuctDimensions {\n    if (airflow <= 0 || velocity <= 0) {\n      return { shape, diameter: 0, width: 0, height: 0 };\n    }\n\n    const area = airflow / velocity; // ft\xB2\n\n    if (shape === 'round') {\n      const diameter = 2 * Math.sqrt(area / Math.PI) * 12; // Convert to inches\n      return { shape, diameter: Math.round(diameter) };\n    } else {\n      // For rectangular, assume aspect ratio of 2:1 (width:height)\n      const height = Math.sqrt(area / 2) * 12; // Convert to inches\n      const width = height * 2;\n      return { \n        shape, \n        width: Math.round(width), \n        height: Math.round(height) \n      };\n    }\n  }\n\n  /**\n   * Validate HVAC parameters against standards\n   */\n  static validateHVACParameters(params: HVACParams, ductType: 'supply' | 'return' | 'exhaust' = 'supply'): ValidationResult {\n    const warnings: string[] = [];\n    const errors: string[] = [];\n    const recommendations: string[] = [];\n\n    // Validate airflow\n    if (params.airflow <= 0) {\n      errors.push('Airflow must be greater than 0');\n    } else if (params.airflow > 50000) {\n      warnings.push('Airflow is unusually high, please verify');\n    }\n\n    // Validate velocity if provided\n    if (params.velocity !== undefined) {\n      const limits = this.SMACNA.maxVelocity[ductType];\n      \n      if (params.velocity < limits.min) {\n        warnings.push(`Velocity ${params.velocity} FPM is below recommended minimum of ${limits.min} FPM for ${ductType} ducts`);\n      } else if (params.velocity > limits.max) {\n        if (params.velocity > limits.max * 1.2) {\n          errors.push(`Velocity ${params.velocity} FPM significantly exceeds maximum of ${limits.max} FPM for ${ductType} ducts`);\n        } else {\n          warnings.push(`Velocity ${params.velocity} FPM exceeds recommended maximum of ${limits.max} FPM for ${ductType} ducts`);\n        }\n      }\n    }\n\n    // Validate temperature\n    if (params.temperature !== undefined) {\n      if (params.temperature < -20 || params.temperature > 200) {\n        warnings.push('Temperature is outside typical HVAC range (-20\xB0F to 200\xB0F)');\n      }\n    }\n\n    // Validate pressure\n    if (params.pressure !== undefined) {\n      if (params.pressure < 10 || params.pressure > 20) {\n        warnings.push('Pressure is outside typical atmospheric range (10-20 psia)');\n      }\n    }\n\n    // Generate recommendations\n    if (params.velocity && params.velocity > this.SMACNA.maxVelocity[ductType].max * 0.8) {\n      recommendations.push('Consider increasing duct size to reduce velocity and noise');\n    }\n\n    if (params.airflow > 10000 && !params.velocity) {\n      recommendations.push('High airflow detected - specify target velocity for optimal duct sizing');\n    }\n\n    return {\n      isValid: errors.length === 0,\n      warnings,\n      errors,\n      recommendations\n    };\n  }\n\n  /**\n   * Calculate transition length for duct size changes\n   */\n  static calculateTransitionLength(inlet: DuctDimensions, outlet: DuctDimensions): number {\n    const inletEquivalentDiameter = this.calculateEquivalentDiameter(inlet);\n    const outletEquivalentDiameter = this.calculateEquivalentDiameter(outlet);\n    const sizeDifference = Math.abs(inletEquivalentDiameter - outletEquivalentDiameter);\n    \n    return sizeDifference * this.SMACNA.transitionSlopeRatio;\n  }\n\n  /**\n   * Calculate elbow radius for round ducts\n   */\n  static calculateElbowRadius(diameter: number): number {\n    return diameter * this.SMACNA.elbowRadiusRatio;\n  }\n\n  /**\n   * Get recommended gauge for duct material and size\n   */\n  static getRecommendedGauge(diameter: number, material: string = 'galvanized_steel'): number {\n    // SMACNA gauge selection based on duct size\n    if (diameter <= 12) return 30;\n    if (diameter <= 18) return 28;\n    if (diameter <= 24) return 26;\n    if (diameter <= 30) return 24;\n    if (diameter <= 42) return 22;\n    if (diameter <= 60) return 20;\n    return 18; // For larger ducts\n  }\n\n  /**\n   * Convert between imperial and metric units\n   */\n  static convertUnits = {\n    // Airflow conversions\n    cfmToLps: (cfm: number): number => cfm * 0.47195,\n    lpsToCfm: (lps: number): number => lps / 0.47195,\n    \n    // Velocity conversions\n    fpmToMps: (fpm: number): number => fpm * 0.00508,\n    mpsToFpm: (mps: number): number => mps / 0.00508,\n    \n    // Pressure conversions\n    inWgToPa: (inWg: number): number => inWg * 248.84,\n    paToInWg: (pa: number): number => pa / 248.84,\n    \n    // Length conversions\n    inchesToMm: (inches: number): number => inches * 25.4,\n    mmToInches: (mm: number): number => mm / 25.4\n  };\n}\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "cbdc8ed5e2a7a032f6b9f96351249abc6b936283"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1suuup29tp = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1suuup29tp();
cov_1suuup29tp().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1suuup29tp().s[1]++;
exports.HVACCalculations = void 0;
/**
 * Comprehensive HVAC Calculations Class
 * Consolidates calculation logic from multiple sources
 */
class HVACCalculations {
  /**
   * Calculate velocity pressure from air velocity
   * Formula: VP = (V/4005)² for standard air density
   */
  static calculateVelocityPressure(velocity, airDensity =
  /* istanbul ignore next */
  (cov_1suuup29tp().b[0][0]++, this.STANDARD_AIR_DENSITY)) {
    /* istanbul ignore next */
    cov_1suuup29tp().f[0]++;
    cov_1suuup29tp().s[2]++;
    if (velocity <= 0) {
      /* istanbul ignore next */
      cov_1suuup29tp().b[1][0]++;
      cov_1suuup29tp().s[3]++;
      return 0;
    } else
    /* istanbul ignore next */
    {
      cov_1suuup29tp().b[1][1]++;
    }
    // Standard formula: VP = ρV²/(2gc) converted to inches w.g.
    // Simplified for standard conditions: VP = (V/4005)²
    const standardVP =
    /* istanbul ignore next */
    (cov_1suuup29tp().s[4]++, Math.pow(velocity / this.VELOCITY_PRESSURE_CONSTANT, 2));
    // Adjust for non-standard air density
    const densityRatio =
    /* istanbul ignore next */
    (cov_1suuup29tp().s[5]++, airDensity / this.STANDARD_AIR_DENSITY);
    /* istanbul ignore next */
    cov_1suuup29tp().s[6]++;
    return standardVP * densityRatio;
  }
  /**
   * Calculate equivalent diameter for rectangular ducts
   * Formula: De = 1.3 * (a*b)^0.625 / (a+b)^0.25
   */
  static calculateEquivalentDiameter(dimensions) {
    /* istanbul ignore next */
    cov_1suuup29tp().f[1]++;
    cov_1suuup29tp().s[7]++;
    if (dimensions.shape === 'round') {
      /* istanbul ignore next */
      cov_1suuup29tp().b[2][0]++;
      cov_1suuup29tp().s[8]++;
      return /* istanbul ignore next */(cov_1suuup29tp().b[3][0]++, dimensions.diameter) ||
      /* istanbul ignore next */
      (cov_1suuup29tp().b[3][1]++, 0);
    } else
    /* istanbul ignore next */
    {
      cov_1suuup29tp().b[2][1]++;
    }
    cov_1suuup29tp().s[9]++;
    if (
    /* istanbul ignore next */
    (cov_1suuup29tp().b[5][0]++, dimensions.shape === 'rectangular') &&
    /* istanbul ignore next */
    (cov_1suuup29tp().b[5][1]++, dimensions.width) &&
    /* istanbul ignore next */
    (cov_1suuup29tp().b[5][2]++, dimensions.height)) {
      /* istanbul ignore next */
      cov_1suuup29tp().b[4][0]++;
      const {
        width,
        height
      } =
      /* istanbul ignore next */
      (cov_1suuup29tp().s[10]++, dimensions);
      // ASHRAE equivalent diameter formula
      /* istanbul ignore next */
      cov_1suuup29tp().s[11]++;
      return 1.3 * Math.pow(width * height, 0.625) / Math.pow(width + height, 0.25);
    } else
    /* istanbul ignore next */
    {
      cov_1suuup29tp().b[4][1]++;
    }
    cov_1suuup29tp().s[12]++;
    if (
    /* istanbul ignore next */
    (cov_1suuup29tp().b[7][0]++, dimensions.shape === 'oval') &&
    /* istanbul ignore next */
    (cov_1suuup29tp().b[7][1]++, dimensions.width) &&
    /* istanbul ignore next */
    (cov_1suuup29tp().b[7][2]++, dimensions.height)) {
      /* istanbul ignore next */
      cov_1suuup29tp().b[6][0]++;
      // Approximate oval as equivalent round duct
      const area =
      /* istanbul ignore next */
      (cov_1suuup29tp().s[13]++, Math.PI * (dimensions.width / 2) * (dimensions.height / 2));
      /* istanbul ignore next */
      cov_1suuup29tp().s[14]++;
      return 2 * Math.sqrt(area / Math.PI);
    } else
    /* istanbul ignore next */
    {
      cov_1suuup29tp().b[6][1]++;
    }
    cov_1suuup29tp().s[15]++;
    return 0;
  }
  /**
   * Calculate pressure loss using Darcy-Weisbach equation
   * Formula: ΔP = f * (L/D) * (ρV²/2)
   */
  static calculatePressureLoss(params) {
    /* istanbul ignore next */
    cov_1suuup29tp().f[2]++;
    const {
      velocity,
      ductLength,
      roughness,
      diameter,
      airDensity =
      /* istanbul ignore next */
      (cov_1suuup29tp().b[8][0]++, this.STANDARD_AIR_DENSITY)
    } =
    /* istanbul ignore next */
    (cov_1suuup29tp().s[16]++, params);
    /* istanbul ignore next */
    cov_1suuup29tp().s[17]++;
    if (
    /* istanbul ignore next */
    (cov_1suuup29tp().b[10][0]++, velocity <= 0) ||
    /* istanbul ignore next */
    (cov_1suuup29tp().b[10][1]++, diameter <= 0) ||
    /* istanbul ignore next */
    (cov_1suuup29tp().b[10][2]++, ductLength <= 0)) {
      /* istanbul ignore next */
      cov_1suuup29tp().b[9][0]++;
      cov_1suuup29tp().s[18]++;
      return 0;
    } else
    /* istanbul ignore next */
    {
      cov_1suuup29tp().b[9][1]++;
    }
    // Calculate Reynolds number
    const kinematicViscosity =
    /* istanbul ignore next */
    (cov_1suuup29tp().s[19]++, 1.57e-4); // ft²/s for air at standard conditions
    const reynoldsNumber =
    /* istanbul ignore next */
    (cov_1suuup29tp().s[20]++, velocity * diameter / (kinematicViscosity * 12)); // Convert diameter to feet
    // Calculate friction factor using Colebrook-White equation (approximation)
    const relativeRoughness =
    /* istanbul ignore next */
    (cov_1suuup29tp().s[21]++, roughness / (diameter / 12)); // Convert to feet
    const frictionFactor =
    /* istanbul ignore next */
    (cov_1suuup29tp().s[22]++, this.calculateFrictionFactor(reynoldsNumber, relativeRoughness));
    // Calculate pressure loss in inches w.g.
    const velocityPressure =
    /* istanbul ignore next */
    (cov_1suuup29tp().s[23]++, this.calculateVelocityPressure(velocity, airDensity));
    /* istanbul ignore next */
    cov_1suuup29tp().s[24]++;
    return frictionFactor * (ductLength / (diameter / 12)) * velocityPressure;
  }
  /**
   * Calculate friction factor using Swamee-Jain approximation
   */
  static calculateFrictionFactor(reynoldsNumber, relativeRoughness) {
    /* istanbul ignore next */
    cov_1suuup29tp().f[3]++;
    cov_1suuup29tp().s[25]++;
    if (reynoldsNumber < 2300) {
      /* istanbul ignore next */
      cov_1suuup29tp().b[11][0]++;
      cov_1suuup29tp().s[26]++;
      // Laminar flow
      return 64 / reynoldsNumber;
    } else {
      /* istanbul ignore next */
      cov_1suuup29tp().b[11][1]++;
      // Turbulent flow - Swamee-Jain approximation
      const term1 =
      /* istanbul ignore next */
      (cov_1suuup29tp().s[27]++, Math.log10(relativeRoughness / 3.7 + 5.74 / Math.pow(reynoldsNumber, 0.9)));
      /* istanbul ignore next */
      cov_1suuup29tp().s[28]++;
      return 0.25 / Math.pow(term1, 2);
    }
  }
  /**
   * Calculate air density based on temperature, pressure, and humidity
   */
  static calculateAirDensity(temperature =
  /* istanbul ignore next */
  (cov_1suuup29tp().b[12][0]++, this.STANDARD_TEMPERATURE),
  // °F
  pressure =
  /* istanbul ignore next */
  (cov_1suuup29tp().b[13][0]++, this.STANDARD_PRESSURE),
  // psia
  humidity =
  /* istanbul ignore next */
  (cov_1suuup29tp().b[14][0]++, 0) // relative humidity (0-1)
  ) {
    /* istanbul ignore next */
    cov_1suuup29tp().f[4]++;
    // Convert temperature to Rankine
    const tempRankine =
    /* istanbul ignore next */
    (cov_1suuup29tp().s[29]++, temperature + 459.67);
    // Calculate dry air density
    const gasConstant =
    /* istanbul ignore next */
    (cov_1suuup29tp().s[30]++, 53.35); // ft·lbf/(lbm·°R) for dry air
    const dryAirDensity =
    /* istanbul ignore next */
    (cov_1suuup29tp().s[31]++, pressure * 144 / (gasConstant * tempRankine)); // Convert psia to psf
    // Adjust for humidity (simplified)
    const humidityCorrection =
    /* istanbul ignore next */
    (cov_1suuup29tp().s[32]++, 1 - 0.378 * humidity * this.getSaturationPressure(temperature) / pressure);
    /* istanbul ignore next */
    cov_1suuup29tp().s[33]++;
    return dryAirDensity * humidityCorrection;
  }
  /**
   * Get saturation pressure for humidity calculations
   */
  static getSaturationPressure(temperature) {
    /* istanbul ignore next */
    cov_1suuup29tp().f[5]++;
    // Antoine equation approximation for water vapor pressure (psia)
    const tempCelsius =
    /* istanbul ignore next */
    (cov_1suuup29tp().s[34]++, (temperature - 32) * 5 / 9);
    /* istanbul ignore next */
    cov_1suuup29tp().s[35]++;
    return Math.exp(16.7 - 4060 / (tempCelsius + 235)) * 0.145; // Convert from Pa to psia
  }
  /**
   * Calculate duct sizing based on airflow and velocity
   */
  static calculateDuctSize(airflow, velocity, shape =
  /* istanbul ignore next */
  (cov_1suuup29tp().b[15][0]++, 'round')) {
    /* istanbul ignore next */
    cov_1suuup29tp().f[6]++;
    cov_1suuup29tp().s[36]++;
    if (
    /* istanbul ignore next */
    (cov_1suuup29tp().b[17][0]++, airflow <= 0) ||
    /* istanbul ignore next */
    (cov_1suuup29tp().b[17][1]++, velocity <= 0)) {
      /* istanbul ignore next */
      cov_1suuup29tp().b[16][0]++;
      cov_1suuup29tp().s[37]++;
      return {
        shape,
        diameter: 0,
        width: 0,
        height: 0
      };
    } else
    /* istanbul ignore next */
    {
      cov_1suuup29tp().b[16][1]++;
    }
    const area =
    /* istanbul ignore next */
    (cov_1suuup29tp().s[38]++, airflow / velocity); // ft²
    /* istanbul ignore next */
    cov_1suuup29tp().s[39]++;
    if (shape === 'round') {
      /* istanbul ignore next */
      cov_1suuup29tp().b[18][0]++;
      const diameter =
      /* istanbul ignore next */
      (cov_1suuup29tp().s[40]++, 2 * Math.sqrt(area / Math.PI) * 12); // Convert to inches
      /* istanbul ignore next */
      cov_1suuup29tp().s[41]++;
      return {
        shape,
        diameter: Math.round(diameter)
      };
    } else {
      /* istanbul ignore next */
      cov_1suuup29tp().b[18][1]++;
      // For rectangular, assume aspect ratio of 2:1 (width:height)
      const height =
      /* istanbul ignore next */
      (cov_1suuup29tp().s[42]++, Math.sqrt(area / 2) * 12); // Convert to inches
      const width =
      /* istanbul ignore next */
      (cov_1suuup29tp().s[43]++, height * 2);
      /* istanbul ignore next */
      cov_1suuup29tp().s[44]++;
      return {
        shape,
        width: Math.round(width),
        height: Math.round(height)
      };
    }
  }
  /**
   * Validate HVAC parameters against standards
   */
  static validateHVACParameters(params, ductType =
  /* istanbul ignore next */
  (cov_1suuup29tp().b[19][0]++, 'supply')) {
    /* istanbul ignore next */
    cov_1suuup29tp().f[7]++;
    const warnings =
    /* istanbul ignore next */
    (cov_1suuup29tp().s[45]++, []);
    const errors =
    /* istanbul ignore next */
    (cov_1suuup29tp().s[46]++, []);
    const recommendations =
    /* istanbul ignore next */
    (cov_1suuup29tp().s[47]++, []);
    // Validate airflow
    /* istanbul ignore next */
    cov_1suuup29tp().s[48]++;
    if (params.airflow <= 0) {
      /* istanbul ignore next */
      cov_1suuup29tp().b[20][0]++;
      cov_1suuup29tp().s[49]++;
      errors.push('Airflow must be greater than 0');
    } else {
      /* istanbul ignore next */
      cov_1suuup29tp().b[20][1]++;
      cov_1suuup29tp().s[50]++;
      if (params.airflow > 50000) {
        /* istanbul ignore next */
        cov_1suuup29tp().b[21][0]++;
        cov_1suuup29tp().s[51]++;
        warnings.push('Airflow is unusually high, please verify');
      } else
      /* istanbul ignore next */
      {
        cov_1suuup29tp().b[21][1]++;
      }
    }
    // Validate velocity if provided
    /* istanbul ignore next */
    cov_1suuup29tp().s[52]++;
    if (params.velocity !== undefined) {
      /* istanbul ignore next */
      cov_1suuup29tp().b[22][0]++;
      const limits =
      /* istanbul ignore next */
      (cov_1suuup29tp().s[53]++, this.SMACNA.maxVelocity[ductType]);
      /* istanbul ignore next */
      cov_1suuup29tp().s[54]++;
      if (params.velocity < limits.min) {
        /* istanbul ignore next */
        cov_1suuup29tp().b[23][0]++;
        cov_1suuup29tp().s[55]++;
        warnings.push(`Velocity ${params.velocity} FPM is below recommended minimum of ${limits.min} FPM for ${ductType} ducts`);
      } else {
        /* istanbul ignore next */
        cov_1suuup29tp().b[23][1]++;
        cov_1suuup29tp().s[56]++;
        if (params.velocity > limits.max) {
          /* istanbul ignore next */
          cov_1suuup29tp().b[24][0]++;
          cov_1suuup29tp().s[57]++;
          if (params.velocity > limits.max * 1.2) {
            /* istanbul ignore next */
            cov_1suuup29tp().b[25][0]++;
            cov_1suuup29tp().s[58]++;
            errors.push(`Velocity ${params.velocity} FPM significantly exceeds maximum of ${limits.max} FPM for ${ductType} ducts`);
          } else {
            /* istanbul ignore next */
            cov_1suuup29tp().b[25][1]++;
            cov_1suuup29tp().s[59]++;
            warnings.push(`Velocity ${params.velocity} FPM exceeds recommended maximum of ${limits.max} FPM for ${ductType} ducts`);
          }
        } else
        /* istanbul ignore next */
        {
          cov_1suuup29tp().b[24][1]++;
        }
      }
    } else
    /* istanbul ignore next */
    {
      cov_1suuup29tp().b[22][1]++;
    }
    // Validate temperature
    cov_1suuup29tp().s[60]++;
    if (params.temperature !== undefined) {
      /* istanbul ignore next */
      cov_1suuup29tp().b[26][0]++;
      cov_1suuup29tp().s[61]++;
      if (
      /* istanbul ignore next */
      (cov_1suuup29tp().b[28][0]++, params.temperature < -20) ||
      /* istanbul ignore next */
      (cov_1suuup29tp().b[28][1]++, params.temperature > 200)) {
        /* istanbul ignore next */
        cov_1suuup29tp().b[27][0]++;
        cov_1suuup29tp().s[62]++;
        warnings.push('Temperature is outside typical HVAC range (-20°F to 200°F)');
      } else
      /* istanbul ignore next */
      {
        cov_1suuup29tp().b[27][1]++;
      }
    } else
    /* istanbul ignore next */
    {
      cov_1suuup29tp().b[26][1]++;
    }
    // Validate pressure
    cov_1suuup29tp().s[63]++;
    if (params.pressure !== undefined) {
      /* istanbul ignore next */
      cov_1suuup29tp().b[29][0]++;
      cov_1suuup29tp().s[64]++;
      if (
      /* istanbul ignore next */
      (cov_1suuup29tp().b[31][0]++, params.pressure < 10) ||
      /* istanbul ignore next */
      (cov_1suuup29tp().b[31][1]++, params.pressure > 20)) {
        /* istanbul ignore next */
        cov_1suuup29tp().b[30][0]++;
        cov_1suuup29tp().s[65]++;
        warnings.push('Pressure is outside typical atmospheric range (10-20 psia)');
      } else
      /* istanbul ignore next */
      {
        cov_1suuup29tp().b[30][1]++;
      }
    } else
    /* istanbul ignore next */
    {
      cov_1suuup29tp().b[29][1]++;
    }
    // Generate recommendations
    cov_1suuup29tp().s[66]++;
    if (
    /* istanbul ignore next */
    (cov_1suuup29tp().b[33][0]++, params.velocity) &&
    /* istanbul ignore next */
    (cov_1suuup29tp().b[33][1]++, params.velocity > this.SMACNA.maxVelocity[ductType].max * 0.8)) {
      /* istanbul ignore next */
      cov_1suuup29tp().b[32][0]++;
      cov_1suuup29tp().s[67]++;
      recommendations.push('Consider increasing duct size to reduce velocity and noise');
    } else
    /* istanbul ignore next */
    {
      cov_1suuup29tp().b[32][1]++;
    }
    cov_1suuup29tp().s[68]++;
    if (
    /* istanbul ignore next */
    (cov_1suuup29tp().b[35][0]++, params.airflow > 10000) &&
    /* istanbul ignore next */
    (cov_1suuup29tp().b[35][1]++, !params.velocity)) {
      /* istanbul ignore next */
      cov_1suuup29tp().b[34][0]++;
      cov_1suuup29tp().s[69]++;
      recommendations.push('High airflow detected - specify target velocity for optimal duct sizing');
    } else
    /* istanbul ignore next */
    {
      cov_1suuup29tp().b[34][1]++;
    }
    cov_1suuup29tp().s[70]++;
    return {
      isValid: errors.length === 0,
      warnings,
      errors,
      recommendations
    };
  }
  /**
   * Calculate transition length for duct size changes
   */
  static calculateTransitionLength(inlet, outlet) {
    /* istanbul ignore next */
    cov_1suuup29tp().f[8]++;
    const inletEquivalentDiameter =
    /* istanbul ignore next */
    (cov_1suuup29tp().s[71]++, this.calculateEquivalentDiameter(inlet));
    const outletEquivalentDiameter =
    /* istanbul ignore next */
    (cov_1suuup29tp().s[72]++, this.calculateEquivalentDiameter(outlet));
    const sizeDifference =
    /* istanbul ignore next */
    (cov_1suuup29tp().s[73]++, Math.abs(inletEquivalentDiameter - outletEquivalentDiameter));
    /* istanbul ignore next */
    cov_1suuup29tp().s[74]++;
    return sizeDifference * this.SMACNA.transitionSlopeRatio;
  }
  /**
   * Calculate elbow radius for round ducts
   */
  static calculateElbowRadius(diameter) {
    /* istanbul ignore next */
    cov_1suuup29tp().f[9]++;
    cov_1suuup29tp().s[75]++;
    return diameter * this.SMACNA.elbowRadiusRatio;
  }
  /**
   * Get recommended gauge for duct material and size
   */
  static getRecommendedGauge(diameter, material =
  /* istanbul ignore next */
  (cov_1suuup29tp().b[36][0]++, 'galvanized_steel')) {
    /* istanbul ignore next */
    cov_1suuup29tp().f[10]++;
    cov_1suuup29tp().s[76]++;
    // SMACNA gauge selection based on duct size
    if (diameter <= 12) {
      /* istanbul ignore next */
      cov_1suuup29tp().b[37][0]++;
      cov_1suuup29tp().s[77]++;
      return 30;
    } else
    /* istanbul ignore next */
    {
      cov_1suuup29tp().b[37][1]++;
    }
    cov_1suuup29tp().s[78]++;
    if (diameter <= 18) {
      /* istanbul ignore next */
      cov_1suuup29tp().b[38][0]++;
      cov_1suuup29tp().s[79]++;
      return 28;
    } else
    /* istanbul ignore next */
    {
      cov_1suuup29tp().b[38][1]++;
    }
    cov_1suuup29tp().s[80]++;
    if (diameter <= 24) {
      /* istanbul ignore next */
      cov_1suuup29tp().b[39][0]++;
      cov_1suuup29tp().s[81]++;
      return 26;
    } else
    /* istanbul ignore next */
    {
      cov_1suuup29tp().b[39][1]++;
    }
    cov_1suuup29tp().s[82]++;
    if (diameter <= 30) {
      /* istanbul ignore next */
      cov_1suuup29tp().b[40][0]++;
      cov_1suuup29tp().s[83]++;
      return 24;
    } else
    /* istanbul ignore next */
    {
      cov_1suuup29tp().b[40][1]++;
    }
    cov_1suuup29tp().s[84]++;
    if (diameter <= 42) {
      /* istanbul ignore next */
      cov_1suuup29tp().b[41][0]++;
      cov_1suuup29tp().s[85]++;
      return 22;
    } else
    /* istanbul ignore next */
    {
      cov_1suuup29tp().b[41][1]++;
    }
    cov_1suuup29tp().s[86]++;
    if (diameter <= 60) {
      /* istanbul ignore next */
      cov_1suuup29tp().b[42][0]++;
      cov_1suuup29tp().s[87]++;
      return 20;
    } else
    /* istanbul ignore next */
    {
      cov_1suuup29tp().b[42][1]++;
    }
    cov_1suuup29tp().s[88]++;
    return 18; // For larger ducts
  }
}
/* istanbul ignore next */
cov_1suuup29tp().s[89]++;
exports.HVACCalculations = HVACCalculations;
// Standard constants
/* istanbul ignore next */
cov_1suuup29tp().s[90]++;
HVACCalculations.STANDARD_AIR_DENSITY = 0.075; // lb/ft³ at standard conditions
/* istanbul ignore next */
cov_1suuup29tp().s[91]++;
HVACCalculations.STANDARD_TEMPERATURE = 70; // °F
/* istanbul ignore next */
cov_1suuup29tp().s[92]++;
HVACCalculations.STANDARD_PRESSURE = 14.7; // psia
/* istanbul ignore next */
cov_1suuup29tp().s[93]++;
HVACCalculations.VELOCITY_PRESSURE_CONSTANT = 4005; // for imperial units
// SMACNA Standards
/* istanbul ignore next */
cov_1suuup29tp().s[94]++;
HVACCalculations.SMACNA = {
  maxVelocity: {
    supply: {
      min: 500,
      max: 2500
    },
    return: {
      min: 300,
      max: 2000
    },
    exhaust: {
      min: 1000,
      max: 4000
    }
  },
  transitionSlopeRatio: 2.5,
  elbowRadiusRatio: 1.5,
  standardGauges: [30, 28, 26, 24, 22, 20, 18, 16, 14],
  materialRoughness: {
    galvanized_steel: 0.0005,
    stainless_steel: 0.0002,
    aluminum: 0.0003,
    pvc: 0.0001,
    fiberglass: 0.003
  }
};
/**
 * Convert between imperial and metric units
 */
/* istanbul ignore next */
cov_1suuup29tp().s[95]++;
HVACCalculations.convertUnits = {
  // Airflow conversions
  cfmToLps: cfm => {
    /* istanbul ignore next */
    cov_1suuup29tp().f[11]++;
    cov_1suuup29tp().s[96]++;
    return cfm * 0.47195;
  },
  lpsToCfm: lps => {
    /* istanbul ignore next */
    cov_1suuup29tp().f[12]++;
    cov_1suuup29tp().s[97]++;
    return lps / 0.47195;
  },
  // Velocity conversions
  fpmToMps: fpm => {
    /* istanbul ignore next */
    cov_1suuup29tp().f[13]++;
    cov_1suuup29tp().s[98]++;
    return fpm * 0.00508;
  },
  mpsToFpm: mps => {
    /* istanbul ignore next */
    cov_1suuup29tp().f[14]++;
    cov_1suuup29tp().s[99]++;
    return mps / 0.00508;
  },
  // Pressure conversions
  inWgToPa: inWg => {
    /* istanbul ignore next */
    cov_1suuup29tp().f[15]++;
    cov_1suuup29tp().s[100]++;
    return inWg * 248.84;
  },
  paToInWg: pa => {
    /* istanbul ignore next */
    cov_1suuup29tp().f[16]++;
    cov_1suuup29tp().s[101]++;
    return pa / 248.84;
  },
  // Length conversions
  inchesToMm: inches => {
    /* istanbul ignore next */
    cov_1suuup29tp().f[17]++;
    cov_1suuup29tp().s[102]++;
    return inches * 25.4;
  },
  mmToInches: mm => {
    /* istanbul ignore next */
    cov_1suuup29tp().f[18]++;
    cov_1suuup29tp().s[103]++;
    return mm / 25.4;
  }
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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