{"version": 3, "names": ["cov_1suuup29tp", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "name", "decl", "loc", "branchMap", "type", "locations", "undefined", "s", "f", "b", "inputSourceMap", "file", "mappings", "names", "sources", "sourcesContent", "version", "_coverageSchema", "coverage", "actualCoverage", "HVACCalculations", "calculateVelocityPressure", "velocity", "airDensity", "STANDARD_AIR_DENSITY", "standardVP", "Math", "pow", "VELOCITY_PRESSURE_CONSTANT", "densityRatio", "calculateEquivalentDiameter", "dimensions", "shape", "diameter", "width", "height", "area", "PI", "sqrt", "calculatePressureLoss", "params", "ductLength", "roughness", "kinematicViscosity", "reynoldsNumber", "relativeRoughness", "frictionFactor", "calculateFrictionFactor", "velocityPressure", "term1", "log10", "calculateAirDensity", "temperature", "STANDARD_TEMPERATURE", "pressure", "STANDARD_PRESSURE", "humidity", "tempRankine", "gasConstant", "dryAirDensity", "humidityCorrection", "getSaturationPressure", "tempCelsius", "exp", "calculateDuctSize", "airflow", "round", "validateHVACParameters", "ductType", "warnings", "errors", "recommendations", "push", "limits", "SMACNA", "maxVelocity", "min", "max", "<PERSON><PERSON><PERSON><PERSON>", "length", "calculateTransitionLength", "inlet", "outlet", "inletEquivalentDiameter", "outletEquivalentDiameter", "sizeDifference", "abs", "transitionSlopeRatio", "calculateElbowRadius", "elbowRadiusRatio", "getRecommendedGauge", "material", "exports", "supply", "return", "exhaust", "standardGauges", "materialRoughness", "galvanized_steel", "stainless_steel", "aluminum", "pvc", "fiberglass", "convertUnits", "cfmToLps", "cfm", "lpsToCfm", "lps", "fpmToMps", "fpm", "mpsToFpm", "mps", "inWgToPa", "inWg", "paToInWg", "pa", "inchesToMm", "inches", "mmToInches", "mm"], "sources": ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\shared\\calculations\\HVACCalculations.ts"], "sourcesContent": ["/**\n * HVAC Calculations Utility\n * \n * Consolidates HVAC calculation logic that was duplicated across\n * frontend utilities, backend services, and validation modules.\n */\n\nexport interface DuctDimensions {\n  width?: number;\n  height?: number;\n  diameter?: number;\n  shape: 'rectangular' | 'round' | 'oval';\n}\n\nexport interface PressureLossParams {\n  velocity: number;\n  ductLength: number;\n  roughness: number;\n  diameter: number;\n  airDensity?: number;\n}\n\nexport interface HVACParams {\n  airflow: number;\n  velocity?: number;\n  temperature?: number;\n  pressure?: number;\n  humidity?: number;\n  elevation?: number;\n}\n\nexport interface ValidationResult {\n  isValid: boolean;\n  warnings: string[];\n  errors: string[];\n  recommendations: string[];\n}\n\nexport interface VelocityLimits {\n  supply: { min: number; max: number };\n  return: { min: number; max: number };\n  exhaust: { min: number; max: number };\n}\n\nexport interface SMACNAStandards {\n  maxVelocity: VelocityLimits;\n  transitionSlopeRatio: number;\n  elbowRadiusRatio: number;\n  standardGauges: number[];\n  materialRoughness: Record<string, number>;\n}\n\n/**\n * Comprehensive HVAC Calculations Class\n * Consolidates calculation logic from multiple sources\n */\nexport class HVACCalculations {\n  \n  // Standard constants\n  static readonly STANDARD_AIR_DENSITY = 0.075; // lb/ft³ at standard conditions\n  static readonly STANDARD_TEMPERATURE = 70; // °F\n  static readonly STANDARD_PRESSURE = 14.7; // psia\n  static readonly VELOCITY_PRESSURE_CONSTANT = 4005; // for imperial units\n\n  // SMACNA Standards\n  static readonly SMACNA: SMACNAStandards = {\n    maxVelocity: {\n      supply: { min: 500, max: 2500 },\n      return: { min: 300, max: 2000 },\n      exhaust: { min: 1000, max: 4000 }\n    },\n    transitionSlopeRatio: 2.5,\n    elbowRadiusRatio: 1.5,\n    standardGauges: [30, 28, 26, 24, 22, 20, 18, 16, 14],\n    materialRoughness: {\n      galvanized_steel: 0.0005,\n      stainless_steel: 0.0002,\n      aluminum: 0.0003,\n      pvc: 0.0001,\n      fiberglass: 0.003\n    }\n  };\n\n  /**\n   * Calculate velocity pressure from air velocity\n   * Formula: VP = (V/4005)² for standard air density\n   */\n  static calculateVelocityPressure(velocity: number, airDensity: number = this.STANDARD_AIR_DENSITY): number {\n    if (velocity <= 0) return 0;\n    \n    // Standard formula: VP = ρV²/(2gc) converted to inches w.g.\n    // Simplified for standard conditions: VP = (V/4005)²\n    const standardVP = Math.pow(velocity / this.VELOCITY_PRESSURE_CONSTANT, 2);\n    \n    // Adjust for non-standard air density\n    const densityRatio = airDensity / this.STANDARD_AIR_DENSITY;\n    return standardVP * densityRatio;\n  }\n\n  /**\n   * Calculate equivalent diameter for rectangular ducts\n   * Formula: De = 1.3 * (a*b)^0.625 / (a+b)^0.25\n   */\n  static calculateEquivalentDiameter(dimensions: DuctDimensions): number {\n    if (dimensions.shape === 'round') {\n      return dimensions.diameter || 0;\n    }\n\n    if (dimensions.shape === 'rectangular' && dimensions.width && dimensions.height) {\n      const { width, height } = dimensions;\n      // ASHRAE equivalent diameter formula\n      return 1.3 * Math.pow(width * height, 0.625) / Math.pow(width + height, 0.25);\n    }\n\n    if (dimensions.shape === 'oval' && dimensions.width && dimensions.height) {\n      // Approximate oval as equivalent round duct\n      const area = Math.PI * (dimensions.width / 2) * (dimensions.height / 2);\n      return 2 * Math.sqrt(area / Math.PI);\n    }\n\n    return 0;\n  }\n\n  /**\n   * Calculate pressure loss using Darcy-Weisbach equation\n   * Formula: ΔP = f * (L/D) * (ρV²/2)\n   */\n  static calculatePressureLoss(params: PressureLossParams): number {\n    const { velocity, ductLength, roughness, diameter, airDensity = this.STANDARD_AIR_DENSITY } = params;\n    \n    if (velocity <= 0 || diameter <= 0 || ductLength <= 0) return 0;\n\n    // Calculate Reynolds number\n    const kinematicViscosity = 1.57e-4; // ft²/s for air at standard conditions\n    const reynoldsNumber = (velocity * diameter) / (kinematicViscosity * 12); // Convert diameter to feet\n\n    // Calculate friction factor using Colebrook-White equation (approximation)\n    const relativeRoughness = roughness / (diameter / 12); // Convert to feet\n    const frictionFactor = this.calculateFrictionFactor(reynoldsNumber, relativeRoughness);\n\n    // Calculate pressure loss in inches w.g.\n    const velocityPressure = this.calculateVelocityPressure(velocity, airDensity);\n    return frictionFactor * (ductLength / (diameter / 12)) * velocityPressure;\n  }\n\n  /**\n   * Calculate friction factor using Swamee-Jain approximation\n   */\n  private static calculateFrictionFactor(reynoldsNumber: number, relativeRoughness: number): number {\n    if (reynoldsNumber < 2300) {\n      // Laminar flow\n      return 64 / reynoldsNumber;\n    } else {\n      // Turbulent flow - Swamee-Jain approximation\n      const term1 = Math.log10(relativeRoughness / 3.7 + 5.74 / Math.pow(reynoldsNumber, 0.9));\n      return 0.25 / Math.pow(term1, 2);\n    }\n  }\n\n  /**\n   * Calculate air density based on temperature, pressure, and humidity\n   */\n  static calculateAirDensity(\n    temperature: number = this.STANDARD_TEMPERATURE, // °F\n    pressure: number = this.STANDARD_PRESSURE, // psia\n    humidity: number = 0 // relative humidity (0-1)\n  ): number {\n    // Convert temperature to Rankine\n    const tempRankine = temperature + 459.67;\n    \n    // Calculate dry air density\n    const gasConstant = 53.35; // ft·lbf/(lbm·°R) for dry air\n    const dryAirDensity = (pressure * 144) / (gasConstant * tempRankine); // Convert psia to psf\n    \n    // Adjust for humidity (simplified)\n    const humidityCorrection = 1 - (0.378 * humidity * this.getSaturationPressure(temperature) / pressure);\n    \n    return dryAirDensity * humidityCorrection;\n  }\n\n  /**\n   * Get saturation pressure for humidity calculations\n   */\n  private static getSaturationPressure(temperature: number): number {\n    // Antoine equation approximation for water vapor pressure (psia)\n    const tempCelsius = (temperature - 32) * 5/9;\n    return Math.exp(16.7 - 4060 / (tempCelsius + 235)) * 0.145; // Convert from Pa to psia\n  }\n\n  /**\n   * Calculate duct sizing based on airflow and velocity\n   */\n  static calculateDuctSize(airflow: number, velocity: number, shape: 'rectangular' | 'round' = 'round'): DuctDimensions {\n    if (airflow <= 0 || velocity <= 0) {\n      return { shape, diameter: 0, width: 0, height: 0 };\n    }\n\n    const area = airflow / velocity; // ft²\n\n    if (shape === 'round') {\n      const diameter = 2 * Math.sqrt(area / Math.PI) * 12; // Convert to inches\n      return { shape, diameter: Math.round(diameter) };\n    } else {\n      // For rectangular, assume aspect ratio of 2:1 (width:height)\n      const height = Math.sqrt(area / 2) * 12; // Convert to inches\n      const width = height * 2;\n      return { \n        shape, \n        width: Math.round(width), \n        height: Math.round(height) \n      };\n    }\n  }\n\n  /**\n   * Validate HVAC parameters against standards\n   */\n  static validateHVACParameters(params: HVACParams, ductType: 'supply' | 'return' | 'exhaust' = 'supply'): ValidationResult {\n    const warnings: string[] = [];\n    const errors: string[] = [];\n    const recommendations: string[] = [];\n\n    // Validate airflow\n    if (params.airflow <= 0) {\n      errors.push('Airflow must be greater than 0');\n    } else if (params.airflow > 50000) {\n      warnings.push('Airflow is unusually high, please verify');\n    }\n\n    // Validate velocity if provided\n    if (params.velocity !== undefined) {\n      const limits = this.SMACNA.maxVelocity[ductType];\n      \n      if (params.velocity < limits.min) {\n        warnings.push(`Velocity ${params.velocity} FPM is below recommended minimum of ${limits.min} FPM for ${ductType} ducts`);\n      } else if (params.velocity > limits.max) {\n        if (params.velocity > limits.max * 1.2) {\n          errors.push(`Velocity ${params.velocity} FPM significantly exceeds maximum of ${limits.max} FPM for ${ductType} ducts`);\n        } else {\n          warnings.push(`Velocity ${params.velocity} FPM exceeds recommended maximum of ${limits.max} FPM for ${ductType} ducts`);\n        }\n      }\n    }\n\n    // Validate temperature\n    if (params.temperature !== undefined) {\n      if (params.temperature < -20 || params.temperature > 200) {\n        warnings.push('Temperature is outside typical HVAC range (-20°F to 200°F)');\n      }\n    }\n\n    // Validate pressure\n    if (params.pressure !== undefined) {\n      if (params.pressure < 10 || params.pressure > 20) {\n        warnings.push('Pressure is outside typical atmospheric range (10-20 psia)');\n      }\n    }\n\n    // Generate recommendations\n    if (params.velocity && params.velocity > this.SMACNA.maxVelocity[ductType].max * 0.8) {\n      recommendations.push('Consider increasing duct size to reduce velocity and noise');\n    }\n\n    if (params.airflow > 10000 && !params.velocity) {\n      recommendations.push('High airflow detected - specify target velocity for optimal duct sizing');\n    }\n\n    return {\n      isValid: errors.length === 0,\n      warnings,\n      errors,\n      recommendations\n    };\n  }\n\n  /**\n   * Calculate transition length for duct size changes\n   */\n  static calculateTransitionLength(inlet: DuctDimensions, outlet: DuctDimensions): number {\n    const inletEquivalentDiameter = this.calculateEquivalentDiameter(inlet);\n    const outletEquivalentDiameter = this.calculateEquivalentDiameter(outlet);\n    const sizeDifference = Math.abs(inletEquivalentDiameter - outletEquivalentDiameter);\n    \n    return sizeDifference * this.SMACNA.transitionSlopeRatio;\n  }\n\n  /**\n   * Calculate elbow radius for round ducts\n   */\n  static calculateElbowRadius(diameter: number): number {\n    return diameter * this.SMACNA.elbowRadiusRatio;\n  }\n\n  /**\n   * Get recommended gauge for duct material and size\n   */\n  static getRecommendedGauge(diameter: number, material: string = 'galvanized_steel'): number {\n    // SMACNA gauge selection based on duct size\n    if (diameter <= 12) return 30;\n    if (diameter <= 18) return 28;\n    if (diameter <= 24) return 26;\n    if (diameter <= 30) return 24;\n    if (diameter <= 42) return 22;\n    if (diameter <= 60) return 20;\n    return 18; // For larger ducts\n  }\n\n  /**\n   * Convert between imperial and metric units\n   */\n  static convertUnits = {\n    // Airflow conversions\n    cfmToLps: (cfm: number): number => cfm * 0.47195,\n    lpsToCfm: (lps: number): number => lps / 0.47195,\n    \n    // Velocity conversions\n    fpmToMps: (fpm: number): number => fpm * 0.00508,\n    mpsToFpm: (mps: number): number => mps / 0.00508,\n    \n    // Pressure conversions\n    inWgToPa: (inWg: number): number => inWg * 248.84,\n    paToInWg: (pa: number): number => pa / 248.84,\n    \n    // Length conversions\n    inchesToMm: (inches: number): number => inches * 25.4,\n    mmToInches: (mm: number): number => mm / 25.4\n  };\n}\n"], "mappings": ";;AAAA;;;;;;AAAA;AAAA,SAAAA,eAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;MAAA;QAAAC,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAO,SAAA;MAAA;QAAAD,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;IAAA;IAAAW,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,cAAA;MAAAC,IAAA;MAAAC,QAAA;MAAAC,KAAA;MAAAC,OAAA;MAAAC,cAAA;MAAAC,OAAA;IAAA;IAAAC,eAAA;IAAA5B,IAAA;EAAA;EAAA,IAAA6B,QAAA,GAAA5B,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAA0B,QAAA,CAAA9B,IAAA,KAAA8B,QAAA,CAAA9B,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAA6B,QAAA,CAAA9B,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAA0B,cAAA,GAAAD,QAAA,CAAA9B,IAAA;EAAA;;;;;;;;;;;;;;;;AAoDA;;;;AAIA,MAAagC,gBAAgB;EA2B3B;;;;EAIA,OAAOC,yBAAyBA,CAACC,QAAgB,EAAEC,UAAA;EAAA;EAAA,CAAApC,cAAA,GAAAsB,CAAA,UAAqB,IAAI,CAACe,oBAAoB;IAAA;IAAArC,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAC/F,IAAIe,QAAQ,IAAI,CAAC,EAAE;MAAA;MAAAnC,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,OAAO,CAAC;IAAA,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAE5B;IACA;IACA,MAAMgB,UAAU;IAAA;IAAA,CAAAtC,cAAA,GAAAoB,CAAA,OAAGmB,IAAI,CAACC,GAAG,CAACL,QAAQ,GAAG,IAAI,CAACM,0BAA0B,EAAE,CAAC,CAAC;IAE1E;IACA,MAAMC,YAAY;IAAA;IAAA,CAAA1C,cAAA,GAAAoB,CAAA,OAAGgB,UAAU,GAAG,IAAI,CAACC,oBAAoB;IAAC;IAAArC,cAAA,GAAAoB,CAAA;IAC5D,OAAOkB,UAAU,GAAGI,YAAY;EAClC;EAEA;;;;EAIA,OAAOC,2BAA2BA,CAACC,UAA0B;IAAA;IAAA5C,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAC3D,IAAIwB,UAAU,CAACC,KAAK,KAAK,OAAO,EAAE;MAAA;MAAA7C,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAChC,OAAO,2BAAApB,cAAA,GAAAsB,CAAA,UAAAsB,UAAU,CAACE,QAAQ;MAAA;MAAA,CAAA9C,cAAA,GAAAsB,CAAA,UAAI,CAAC;IACjC,CAAC;IAAA;IAAA;MAAAtB,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAED;IAAI;IAAA,CAAApB,cAAA,GAAAsB,CAAA,UAAAsB,UAAU,CAACC,KAAK,KAAK,aAAa;IAAA;IAAA,CAAA7C,cAAA,GAAAsB,CAAA,UAAIsB,UAAU,CAACG,KAAK;IAAA;IAAA,CAAA/C,cAAA,GAAAsB,CAAA,UAAIsB,UAAU,CAACI,MAAM,GAAE;MAAA;MAAAhD,cAAA,GAAAsB,CAAA;MAC/E,MAAM;QAAEyB,KAAK;QAAEC;MAAM,CAAE;MAAA;MAAA,CAAAhD,cAAA,GAAAoB,CAAA,QAAGwB,UAAU;MACpC;MAAA;MAAA5C,cAAA,GAAAoB,CAAA;MACA,OAAO,GAAG,GAAGmB,IAAI,CAACC,GAAG,CAACO,KAAK,GAAGC,MAAM,EAAE,KAAK,CAAC,GAAGT,IAAI,CAACC,GAAG,CAACO,KAAK,GAAGC,MAAM,EAAE,IAAI,CAAC;IAC/E,CAAC;IAAA;IAAA;MAAAhD,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAED;IAAI;IAAA,CAAApB,cAAA,GAAAsB,CAAA,UAAAsB,UAAU,CAACC,KAAK,KAAK,MAAM;IAAA;IAAA,CAAA7C,cAAA,GAAAsB,CAAA,UAAIsB,UAAU,CAACG,KAAK;IAAA;IAAA,CAAA/C,cAAA,GAAAsB,CAAA,UAAIsB,UAAU,CAACI,MAAM,GAAE;MAAA;MAAAhD,cAAA,GAAAsB,CAAA;MACxE;MACA,MAAM2B,IAAI;MAAA;MAAA,CAAAjD,cAAA,GAAAoB,CAAA,QAAGmB,IAAI,CAACW,EAAE,IAAIN,UAAU,CAACG,KAAK,GAAG,CAAC,CAAC,IAAIH,UAAU,CAACI,MAAM,GAAG,CAAC,CAAC;MAAC;MAAAhD,cAAA,GAAAoB,CAAA;MACxE,OAAO,CAAC,GAAGmB,IAAI,CAACY,IAAI,CAACF,IAAI,GAAGV,IAAI,CAACW,EAAE,CAAC;IACtC,CAAC;IAAA;IAAA;MAAAlD,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAED,OAAO,CAAC;EACV;EAEA;;;;EAIA,OAAOgC,qBAAqBA,CAACC,MAA0B;IAAA;IAAArD,cAAA,GAAAqB,CAAA;IACrD,MAAM;MAAEc,QAAQ;MAAEmB,UAAU;MAAEC,SAAS;MAAET,QAAQ;MAAEV,UAAU;MAAA;MAAA,CAAApC,cAAA,GAAAsB,CAAA,UAAG,IAAI,CAACe,oBAAoB;IAAA,CAAE;IAAA;IAAA,CAAArC,cAAA,GAAAoB,CAAA,QAAGiC,MAAM;IAAC;IAAArD,cAAA,GAAAoB,CAAA;IAErG;IAAI;IAAA,CAAApB,cAAA,GAAAsB,CAAA,WAAAa,QAAQ,IAAI,CAAC;IAAA;IAAA,CAAAnC,cAAA,GAAAsB,CAAA,WAAIwB,QAAQ,IAAI,CAAC;IAAA;IAAA,CAAA9C,cAAA,GAAAsB,CAAA,WAAIgC,UAAU,IAAI,CAAC,GAAE;MAAA;MAAAtD,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,OAAO,CAAC;IAAA,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAEhE;IACA,MAAMkC,kBAAkB;IAAA;IAAA,CAAAxD,cAAA,GAAAoB,CAAA,QAAG,OAAO,EAAC,CAAC;IACpC,MAAMqC,cAAc;IAAA;IAAA,CAAAzD,cAAA,GAAAoB,CAAA,QAAIe,QAAQ,GAAGW,QAAQ,IAAKU,kBAAkB,GAAG,EAAE,CAAC,EAAC,CAAC;IAE1E;IACA,MAAME,iBAAiB;IAAA;IAAA,CAAA1D,cAAA,GAAAoB,CAAA,QAAGmC,SAAS,IAAIT,QAAQ,GAAG,EAAE,CAAC,EAAC,CAAC;IACvD,MAAMa,cAAc;IAAA;IAAA,CAAA3D,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACwC,uBAAuB,CAACH,cAAc,EAAEC,iBAAiB,CAAC;IAEtF;IACA,MAAMG,gBAAgB;IAAA;IAAA,CAAA7D,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACc,yBAAyB,CAACC,QAAQ,EAAEC,UAAU,CAAC;IAAC;IAAApC,cAAA,GAAAoB,CAAA;IAC9E,OAAOuC,cAAc,IAAIL,UAAU,IAAIR,QAAQ,GAAG,EAAE,CAAC,CAAC,GAAGe,gBAAgB;EAC3E;EAEA;;;EAGQ,OAAOD,uBAAuBA,CAACH,cAAsB,EAAEC,iBAAyB;IAAA;IAAA1D,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACtF,IAAIqC,cAAc,GAAG,IAAI,EAAE;MAAA;MAAAzD,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACzB;MACA,OAAO,EAAE,GAAGqC,cAAc;IAC5B,CAAC,MAAM;MAAA;MAAAzD,cAAA,GAAAsB,CAAA;MACL;MACA,MAAMwC,KAAK;MAAA;MAAA,CAAA9D,cAAA,GAAAoB,CAAA,QAAGmB,IAAI,CAACwB,KAAK,CAACL,iBAAiB,GAAG,GAAG,GAAG,IAAI,GAAGnB,IAAI,CAACC,GAAG,CAACiB,cAAc,EAAE,GAAG,CAAC,CAAC;MAAC;MAAAzD,cAAA,GAAAoB,CAAA;MACzF,OAAO,IAAI,GAAGmB,IAAI,CAACC,GAAG,CAACsB,KAAK,EAAE,CAAC,CAAC;IAClC;EACF;EAEA;;;EAGA,OAAOE,mBAAmBA,CACxBC,WAAA;EAAA;EAAA,CAAAjE,cAAA,GAAAsB,CAAA,WAAsB,IAAI,CAAC4C,oBAAoB;EAAE;EACjDC,QAAA;EAAA;EAAA,CAAAnE,cAAA,GAAAsB,CAAA,WAAmB,IAAI,CAAC8C,iBAAiB;EAAE;EAC3CC,QAAA;EAAA;EAAA,CAAArE,cAAA,GAAAsB,CAAA,WAAmB,CAAC,EAAC;EAAA,E;;;IAErB;IACA,MAAMgD,WAAW;IAAA;IAAA,CAAAtE,cAAA,GAAAoB,CAAA,QAAG6C,WAAW,GAAG,MAAM;IAExC;IACA,MAAMM,WAAW;IAAA;IAAA,CAAAvE,cAAA,GAAAoB,CAAA,QAAG,KAAK,EAAC,CAAC;IAC3B,MAAMoD,aAAa;IAAA;IAAA,CAAAxE,cAAA,GAAAoB,CAAA,QAAI+C,QAAQ,GAAG,GAAG,IAAKI,WAAW,GAAGD,WAAW,CAAC,EAAC,CAAC;IAEtE;IACA,MAAMG,kBAAkB;IAAA;IAAA,CAAAzE,cAAA,GAAAoB,CAAA,QAAG,CAAC,GAAI,KAAK,GAAGiD,QAAQ,GAAG,IAAI,CAACK,qBAAqB,CAACT,WAAW,CAAC,GAAGE,QAAS;IAAC;IAAAnE,cAAA,GAAAoB,CAAA;IAEvG,OAAOoD,aAAa,GAAGC,kBAAkB;EAC3C;EAEA;;;EAGQ,OAAOC,qBAAqBA,CAACT,WAAmB;IAAA;IAAAjE,cAAA,GAAAqB,CAAA;IACtD;IACA,MAAMsD,WAAW;IAAA;IAAA,CAAA3E,cAAA,GAAAoB,CAAA,QAAG,CAAC6C,WAAW,GAAG,EAAE,IAAI,CAAC,GAAC,CAAC;IAAC;IAAAjE,cAAA,GAAAoB,CAAA;IAC7C,OAAOmB,IAAI,CAACqC,GAAG,CAAC,IAAI,GAAG,IAAI,IAAID,WAAW,GAAG,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;EAC9D;EAEA;;;EAGA,OAAOE,iBAAiBA,CAACC,OAAe,EAAE3C,QAAgB,EAAEU,KAAA;EAAA;EAAA,CAAA7C,cAAA,GAAAsB,CAAA,WAAiC,OAAO;IAAA;IAAAtB,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAClG;IAAI;IAAA,CAAApB,cAAA,GAAAsB,CAAA,WAAAwD,OAAO,IAAI,CAAC;IAAA;IAAA,CAAA9E,cAAA,GAAAsB,CAAA,WAAIa,QAAQ,IAAI,CAAC,GAAE;MAAA;MAAAnC,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACjC,OAAO;QAAEyB,KAAK;QAAEC,QAAQ,EAAE,CAAC;QAAEC,KAAK,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAC,CAAE;IACpD,CAAC;IAAA;IAAA;MAAAhD,cAAA,GAAAsB,CAAA;IAAA;IAED,MAAM2B,IAAI;IAAA;IAAA,CAAAjD,cAAA,GAAAoB,CAAA,QAAG0D,OAAO,GAAG3C,QAAQ,EAAC,CAAC;IAAA;IAAAnC,cAAA,GAAAoB,CAAA;IAEjC,IAAIyB,KAAK,KAAK,OAAO,EAAE;MAAA;MAAA7C,cAAA,GAAAsB,CAAA;MACrB,MAAMwB,QAAQ;MAAA;MAAA,CAAA9C,cAAA,GAAAoB,CAAA,QAAG,CAAC,GAAGmB,IAAI,CAACY,IAAI,CAACF,IAAI,GAAGV,IAAI,CAACW,EAAE,CAAC,GAAG,EAAE,EAAC,CAAC;MAAA;MAAAlD,cAAA,GAAAoB,CAAA;MACrD,OAAO;QAAEyB,KAAK;QAAEC,QAAQ,EAAEP,IAAI,CAACwC,KAAK,CAACjC,QAAQ;MAAC,CAAE;IAClD,CAAC,MAAM;MAAA;MAAA9C,cAAA,GAAAsB,CAAA;MACL;MACA,MAAM0B,MAAM;MAAA;MAAA,CAAAhD,cAAA,GAAAoB,CAAA,QAAGmB,IAAI,CAACY,IAAI,CAACF,IAAI,GAAG,CAAC,CAAC,GAAG,EAAE,EAAC,CAAC;MACzC,MAAMF,KAAK;MAAA;MAAA,CAAA/C,cAAA,GAAAoB,CAAA,QAAG4B,MAAM,GAAG,CAAC;MAAC;MAAAhD,cAAA,GAAAoB,CAAA;MACzB,OAAO;QACLyB,KAAK;QACLE,KAAK,EAAER,IAAI,CAACwC,KAAK,CAAChC,KAAK,CAAC;QACxBC,MAAM,EAAET,IAAI,CAACwC,KAAK,CAAC/B,MAAM;OAC1B;IACH;EACF;EAEA;;;EAGA,OAAOgC,sBAAsBA,CAAC3B,MAAkB,EAAE4B,QAAA;EAAA;EAAA,CAAAjF,cAAA,GAAAsB,CAAA,WAA4C,QAAQ;IAAA;IAAAtB,cAAA,GAAAqB,CAAA;IACpG,MAAM6D,QAAQ;IAAA;IAAA,CAAAlF,cAAA,GAAAoB,CAAA,QAAa,EAAE;IAC7B,MAAM+D,MAAM;IAAA;IAAA,CAAAnF,cAAA,GAAAoB,CAAA,QAAa,EAAE;IAC3B,MAAMgE,eAAe;IAAA;IAAA,CAAApF,cAAA,GAAAoB,CAAA,QAAa,EAAE;IAEpC;IAAA;IAAApB,cAAA,GAAAoB,CAAA;IACA,IAAIiC,MAAM,CAACyB,OAAO,IAAI,CAAC,EAAE;MAAA;MAAA9E,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACvB+D,MAAM,CAACE,IAAI,CAAC,gCAAgC,CAAC;IAC/C,CAAC,MAAM;MAAA;MAAArF,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,IAAIiC,MAAM,CAACyB,OAAO,GAAG,KAAK,EAAE;QAAA;QAAA9E,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACjC8D,QAAQ,CAACG,IAAI,CAAC,0CAA0C,CAAC;MAC3D,CAAC;MAAA;MAAA;QAAArF,cAAA,GAAAsB,CAAA;MAAA;IAAD;IAEA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IACA,IAAIiC,MAAM,CAAClB,QAAQ,KAAKhB,SAAS,EAAE;MAAA;MAAAnB,cAAA,GAAAsB,CAAA;MACjC,MAAMgE,MAAM;MAAA;MAAA,CAAAtF,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACmE,MAAM,CAACC,WAAW,CAACP,QAAQ,CAAC;MAAC;MAAAjF,cAAA,GAAAoB,CAAA;MAEjD,IAAIiC,MAAM,CAAClB,QAAQ,GAAGmD,MAAM,CAACG,GAAG,EAAE;QAAA;QAAAzF,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAChC8D,QAAQ,CAACG,IAAI,CAAC,YAAYhC,MAAM,CAAClB,QAAQ,wCAAwCmD,MAAM,CAACG,GAAG,YAAYR,QAAQ,QAAQ,CAAC;MAC1H,CAAC,MAAM;QAAA;QAAAjF,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAAA,IAAIiC,MAAM,CAAClB,QAAQ,GAAGmD,MAAM,CAACI,GAAG,EAAE;UAAA;UAAA1F,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAoB,CAAA;UACvC,IAAIiC,MAAM,CAAClB,QAAQ,GAAGmD,MAAM,CAACI,GAAG,GAAG,GAAG,EAAE;YAAA;YAAA1F,cAAA,GAAAsB,CAAA;YAAAtB,cAAA,GAAAoB,CAAA;YACtC+D,MAAM,CAACE,IAAI,CAAC,YAAYhC,MAAM,CAAClB,QAAQ,yCAAyCmD,MAAM,CAACI,GAAG,YAAYT,QAAQ,QAAQ,CAAC;UACzH,CAAC,MAAM;YAAA;YAAAjF,cAAA,GAAAsB,CAAA;YAAAtB,cAAA,GAAAoB,CAAA;YACL8D,QAAQ,CAACG,IAAI,CAAC,YAAYhC,MAAM,CAAClB,QAAQ,uCAAuCmD,MAAM,CAACI,GAAG,YAAYT,QAAQ,QAAQ,CAAC;UACzH;QACF,CAAC;QAAA;QAAA;UAAAjF,cAAA,GAAAsB,CAAA;QAAA;MAAD;IACF,CAAC;IAAA;IAAA;MAAAtB,cAAA,GAAAsB,CAAA;IAAA;IAED;IAAAtB,cAAA,GAAAoB,CAAA;IACA,IAAIiC,MAAM,CAACY,WAAW,KAAK9C,SAAS,EAAE;MAAA;MAAAnB,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACpC;MAAI;MAAA,CAAApB,cAAA,GAAAsB,CAAA,WAAA+B,MAAM,CAACY,WAAW,GAAG,CAAC,EAAE;MAAA;MAAA,CAAAjE,cAAA,GAAAsB,CAAA,WAAI+B,MAAM,CAACY,WAAW,GAAG,GAAG,GAAE;QAAA;QAAAjE,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACxD8D,QAAQ,CAACG,IAAI,CAAC,4DAA4D,CAAC;MAC7E,CAAC;MAAA;MAAA;QAAArF,cAAA,GAAAsB,CAAA;MAAA;IACH,CAAC;IAAA;IAAA;MAAAtB,cAAA,GAAAsB,CAAA;IAAA;IAED;IAAAtB,cAAA,GAAAoB,CAAA;IACA,IAAIiC,MAAM,CAACc,QAAQ,KAAKhD,SAAS,EAAE;MAAA;MAAAnB,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACjC;MAAI;MAAA,CAAApB,cAAA,GAAAsB,CAAA,WAAA+B,MAAM,CAACc,QAAQ,GAAG,EAAE;MAAA;MAAA,CAAAnE,cAAA,GAAAsB,CAAA,WAAI+B,MAAM,CAACc,QAAQ,GAAG,EAAE,GAAE;QAAA;QAAAnE,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAChD8D,QAAQ,CAACG,IAAI,CAAC,4DAA4D,CAAC;MAC7E,CAAC;MAAA;MAAA;QAAArF,cAAA,GAAAsB,CAAA;MAAA;IACH,CAAC;IAAA;IAAA;MAAAtB,cAAA,GAAAsB,CAAA;IAAA;IAED;IAAAtB,cAAA,GAAAoB,CAAA;IACA;IAAI;IAAA,CAAApB,cAAA,GAAAsB,CAAA,WAAA+B,MAAM,CAAClB,QAAQ;IAAA;IAAA,CAAAnC,cAAA,GAAAsB,CAAA,WAAI+B,MAAM,CAAClB,QAAQ,GAAG,IAAI,CAACoD,MAAM,CAACC,WAAW,CAACP,QAAQ,CAAC,CAACS,GAAG,GAAG,GAAG,GAAE;MAAA;MAAA1F,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACpFgE,eAAe,CAACC,IAAI,CAAC,4DAA4D,CAAC;IACpF,CAAC;IAAA;IAAA;MAAArF,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAED;IAAI;IAAA,CAAApB,cAAA,GAAAsB,CAAA,WAAA+B,MAAM,CAACyB,OAAO,GAAG,KAAK;IAAA;IAAA,CAAA9E,cAAA,GAAAsB,CAAA,WAAI,CAAC+B,MAAM,CAAClB,QAAQ,GAAE;MAAA;MAAAnC,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAC9CgE,eAAe,CAACC,IAAI,CAAC,yEAAyE,CAAC;IACjG,CAAC;IAAA;IAAA;MAAArF,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAED,OAAO;MACLuE,OAAO,EAAER,MAAM,CAACS,MAAM,KAAK,CAAC;MAC5BV,QAAQ;MACRC,MAAM;MACNC;KACD;EACH;EAEA;;;EAGA,OAAOS,yBAAyBA,CAACC,KAAqB,EAAEC,MAAsB;IAAA;IAAA/F,cAAA,GAAAqB,CAAA;IAC5E,MAAM2E,uBAAuB;IAAA;IAAA,CAAAhG,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACuB,2BAA2B,CAACmD,KAAK,CAAC;IACvE,MAAMG,wBAAwB;IAAA;IAAA,CAAAjG,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACuB,2BAA2B,CAACoD,MAAM,CAAC;IACzE,MAAMG,cAAc;IAAA;IAAA,CAAAlG,cAAA,GAAAoB,CAAA,QAAGmB,IAAI,CAAC4D,GAAG,CAACH,uBAAuB,GAAGC,wBAAwB,CAAC;IAAC;IAAAjG,cAAA,GAAAoB,CAAA;IAEpF,OAAO8E,cAAc,GAAG,IAAI,CAACX,MAAM,CAACa,oBAAoB;EAC1D;EAEA;;;EAGA,OAAOC,oBAAoBA,CAACvD,QAAgB;IAAA;IAAA9C,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAC1C,OAAO0B,QAAQ,GAAG,IAAI,CAACyC,MAAM,CAACe,gBAAgB;EAChD;EAEA;;;EAGA,OAAOC,mBAAmBA,CAACzD,QAAgB,EAAE0D,QAAA;EAAA;EAAA,CAAAxG,cAAA,GAAAsB,CAAA,WAAmB,kBAAkB;IAAA;IAAAtB,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAChF;IACA,IAAI0B,QAAQ,IAAI,EAAE,EAAE;MAAA;MAAA9C,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,OAAO,EAAE;IAAA,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAC9B,IAAI0B,QAAQ,IAAI,EAAE,EAAE;MAAA;MAAA9C,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,OAAO,EAAE;IAAA,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAC9B,IAAI0B,QAAQ,IAAI,EAAE,EAAE;MAAA;MAAA9C,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,OAAO,EAAE;IAAA,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAC9B,IAAI0B,QAAQ,IAAI,EAAE,EAAE;MAAA;MAAA9C,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,OAAO,EAAE;IAAA,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAC9B,IAAI0B,QAAQ,IAAI,EAAE,EAAE;MAAA;MAAA9C,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,OAAO,EAAE;IAAA,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAC9B,IAAI0B,QAAQ,IAAI,EAAE,EAAE;MAAA;MAAA9C,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,OAAO,EAAE;IAAA,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAC9B,OAAO,EAAE,CAAC,CAAC;EACb;;;;AAzPFqF,OAAA,CAAAxE,gBAAA,GAAAA,gBAAA;AAEE;AAAA;AAAAjC,cAAA,GAAAoB,CAAA;AACgBa,gBAAA,CAAAI,oBAAoB,GAAG,KAAK,CAAC,CAAC;AAAA;AAAArC,cAAA,GAAAoB,CAAA;AAC9Ba,gBAAA,CAAAiC,oBAAoB,GAAG,EAAE,CAAC,CAAC;AAAA;AAAAlE,cAAA,GAAAoB,CAAA;AAC3Ba,gBAAA,CAAAmC,iBAAiB,GAAG,IAAI,CAAC,CAAC;AAAA;AAAApE,cAAA,GAAAoB,CAAA;AAC1Ba,gBAAA,CAAAQ,0BAA0B,GAAG,IAAI,CAAC,CAAC;AAEnD;AAAA;AAAAzC,cAAA,GAAAoB,CAAA;AACgBa,gBAAA,CAAAsD,MAAM,GAAoB;EACxCC,WAAW,EAAE;IACXkB,MAAM,EAAE;MAAEjB,GAAG,EAAE,GAAG;MAAEC,GAAG,EAAE;IAAI,CAAE;IAC/BiB,MAAM,EAAE;MAAElB,GAAG,EAAE,GAAG;MAAEC,GAAG,EAAE;IAAI,CAAE;IAC/BkB,OAAO,EAAE;MAAEnB,GAAG,EAAE,IAAI;MAAEC,GAAG,EAAE;IAAI;GAChC;EACDU,oBAAoB,EAAE,GAAG;EACzBE,gBAAgB,EAAE,GAAG;EACrBO,cAAc,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EACpDC,iBAAiB,EAAE;IACjBC,gBAAgB,EAAE,MAAM;IACxBC,eAAe,EAAE,MAAM;IACvBC,QAAQ,EAAE,MAAM;IAChBC,GAAG,EAAE,MAAM;IACXC,UAAU,EAAE;;CAEf;AAkOD;;;AAAA;AAAAnH,cAAA,GAAAoB,CAAA;AAGOa,gBAAA,CAAAmF,YAAY,GAAG;EACpB;EACAC,QAAQ,EAAGC,GAAW,IAAa;IAAA;IAAAtH,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAAA,OAAAkG,GAAG,GAAG,OAAO;EAAP,CAAO;EAChDC,QAAQ,EAAGC,GAAW,IAAa;IAAA;IAAAxH,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAAA,OAAAoG,GAAG,GAAG,OAAO;EAAP,CAAO;EAEhD;EACAC,QAAQ,EAAGC,GAAW,IAAa;IAAA;IAAA1H,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAAA,OAAAsG,GAAG,GAAG,OAAO;EAAP,CAAO;EAChDC,QAAQ,EAAGC,GAAW,IAAa;IAAA;IAAA5H,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAAA,OAAAwG,GAAG,GAAG,OAAO;EAAP,CAAO;EAEhD;EACAC,QAAQ,EAAGC,IAAY,IAAa;IAAA;IAAA9H,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAAA,OAAA0G,IAAI,GAAG,MAAM;EAAN,CAAM;EACjDC,QAAQ,EAAGC,EAAU,IAAa;IAAA;IAAAhI,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAAA,OAAA4G,EAAE,GAAG,MAAM;EAAN,CAAM;EAE7C;EACAC,UAAU,EAAGC,MAAc,IAAa;IAAA;IAAAlI,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAAA,OAAA8G,MAAM,GAAG,IAAI;EAAJ,CAAI;EACrDC,UAAU,EAAGC,EAAU,IAAa;IAAA;IAAApI,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAAA,OAAAgH,EAAE,GAAG,IAAI;EAAJ;CAC1C", "ignoreList": []}