{"version": 3, "names": ["cov_ocw6unnfw", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "name", "decl", "loc", "branchMap", "type", "locations", "undefined", "s", "f", "b", "inputSourceMap", "file", "mappings", "names", "sources", "sourcesContent", "version", "_coverageSchema", "coverage", "actualCoverage", "SystemAnalysisTypes_1", "require", "LifecycleCostAnalysisEngine", "analyzeLifecycleCosts", "systemConfiguration", "energyAnalysis", "analysisParameters", "analysisId", "generateAnalysisId", "id", "timestamp", "Date", "parameters", "setDefaultParameters", "initialCosts", "calculateInitialCosts", "operatingCosts", "calculateOperatingCosts", "maintenanceCosts", "calculateMaintenanceCosts", "replacementCosts", "calculateReplacementCosts", "totalCostOfOwnership", "calculateTotalCostOfOwnership", "costComparison", "performCostComparison", "sensitivityAnalysis", "performSensitivityAnalysis", "recommendations", "generateCostRecommendations", "analysis", "systemId", "analysisTimestamp", "COST_CACHE", "set", "error", "Error", "message", "provided", "analysisHorizon", "discountRate", "inflationRate", "energyEscalationRate", "DEFAULT_ESCALATION_RATES", "ENERGY", "currency", "analysisMethod", "CostAnalysisMethod", "NET_PRESENT_VALUE", "uncertaintyLevel", "UncertaintyLevel", "MEDIUM", "designAirflow", "designParameters", "systemType", "equipmentCosts", "calculateEquipmentCosts", "installationCosts", "calculateInstallationCosts", "designCosts", "calculateDesignCosts", "permitsCosts", "calculatePermitsCosts", "totalInitialCost", "total", "costPerCFM", "costPerSquareFoot", "designPressure", "fanCostFactor", "getFanCostFactor", "ductworkCostFactor", "fittingsCostFactor", "dampersCostFactor", "controlsCostFactor", "accessoriesCostFactor", "fans", "ductwork", "fittings", "dampers", "controls", "accessories", "airflow", "pressure", "costFactor", "factors", "laborCosts", "materialsCosts", "equipmentRental", "testing", "projectCost", "engineeringDesign", "drawings", "calculations", "projectManagement", "buildingPermits", "inspectionFees", "utilityConnections", "environmentalFees", "annualEnergyCosts", "energyCosts", "currentCosts", "totalCost", "totalEnergyPV", "yearlyEnergyCosts", "year", "yearlyEnergyCost", "Math", "pow", "presentValue", "push", "annualInsurance", "annualUtilities", "annualCompliance", "insurancePV", "calculatePresentValue", "utilitiesPV", "compliancePV", "totalOperatingPV", "annual", "yearlyProjection", "escalationRate", "MAINTENANCE", "insuranceCosts", "utilityCosts", "complianceCosts", "totalAnnual", "totalPresentValue", "annualCost", "years", "yearlyValue", "maintenanceEscalationRate", "preventiveMaintenanceFactor", "correctiveMaintenanceFactor", "filterReplacementFactor", "annualPreventive", "annualCorrective", "annualFilters", "preventivePV", "correctivePV", "filtersPV", "majorOverhaulCost", "overhaulYears", "filter", "overhaulsPV", "for<PERSON>ach", "overhaulCostInflated", "preventiveMaintenance", "description", "correctiveMaintenance", "filterReplacement", "majorOverhauls", "cost", "schedule", "materialEscalationRate", "MATERIALS", "equipmentReplacements", "totalReplacementPV", "equipmentTypes", "life", "EQUIPMENT_LIFE", "FANS", "MOTORS", "VFD", "DAMPERS", "CONTROLS", "equipment", "<PERSON><PERSON><PERSON><PERSON>", "length", "replacementCost", "equipmentPV", "inflatedCost", "equipmentType", "unitCost", "salvageValue", "calculateSalvageValue", "salvagePV", "value", "totalReplacementCost", "netReplacementCost", "ductworkRemainingLife", "max", "DUCTWORK", "fansRemainingLife", "dampersRemainingLife", "ductworkSalvage", "fansSalvage", "dampersSalvage", "totalAnnualizedCost", "calculateAnnualizedCost", "costBreakdown", "amount", "percentage", "financialMetrics", "calculateFinancialMetrics", "paybackPeriod", "calculateSimplePaybackPeriod", "crf", "initialCost", "annualSavings", "npv", "calculateNPV", "irr", "calculateIRR", "discountedPayback", "calculateDiscountedPayback", "profitabilityIndex", "netPresentValue", "internalRateOfReturn", "discountedPaybackPeriod", "returnOnInvestment", "annualCashFlow", "totalCashFlow", "Infinity", "cumulativePV", "alternatives", "initialCostMultiplier", "operatingCostMultiplier", "comparisonResults", "map", "alt", "alternativeName", "costDifference", "percentageDifference", "baselineSystem", "recommendedAlternative", "reduce", "min", "current", "costRankings", "sort", "a", "baselineCost", "sensitivityFactors", "parameter", "baseValue", "variations", "sensitivityResults", "factor", "impacts", "variation", "adjustedCost", "newRate", "adjustedValue", "resultingCost", "costChange", "percentageChange", "sensitivity", "i", "abs", "v", "rankedSensitivities", "result", "index", "impact", "rank", "keyFindings", "flatMap", "r", "toLocaleString", "category", "priority", "title", "potentialSavings", "implementationCost", "actions", "riskLevel", "bestAlternative", "now", "random", "toString", "substring", "exports", "VERSION", "Map", "LABOR", "FILTERS"], "sources": ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\LifecycleCostAnalysisEngine.ts"], "sourcesContent": ["/**\r\n * Lifecycle Cost Analysis Engine\r\n * \r\n * Comprehensive lifecycle cost analysis service for Phase 3 Priority 3: Advanced System Analysis Tools\r\n * Provides initial costs, operating costs, maintenance costs, energy costs, and total cost of ownership\r\n * calculations for HVAC duct systems.\r\n * \r\n * @version 3.0.0\r\n * <AUTHOR> Suite Development Team\r\n */\r\n\r\nimport {\r\n  LifecycleCostAnalysis,\r\n  CostAnalysisParameters,\r\n  InitialCosts,\r\n  OperatingCosts,\r\n  MaintenanceCosts,\r\n  ReplacementCosts,\r\n  TotalCostOfOwnership,\r\n  CostComparison,\r\n  CostSensitivityAnalysis,\r\n  CostRecommendation,\r\n  CostAnalysisMethod,\r\n  UncertaintyLevel,\r\n  EquipmentCosts,\r\n  SystemConfiguration,\r\n  EnergyAnalysis,\r\n  TimeFrame\r\n} from './types/SystemAnalysisTypes';\r\n\r\nimport { EnergyEfficiencyAnalysisEngine } from './EnergyEfficiencyAnalysisEngine';\r\n\r\n/**\r\n * Lifecycle Cost Analysis Engine\r\n * \r\n * Provides comprehensive lifecycle cost analysis capabilities including:\r\n * - Initial capital cost analysis\r\n * - Operating cost projections\r\n * - Maintenance cost modeling\r\n * - Replacement cost planning\r\n * - Total cost of ownership calculations\r\n * - Cost comparison and sensitivity analysis\r\n * - Financial metrics (NPV, IRR, Payback)\r\n */\r\nexport class LifecycleCostAnalysisEngine {\r\n  private static readonly VERSION = '3.0.0';\r\n  private static readonly COST_CACHE = new Map<string, LifecycleCostAnalysis>();\r\n  \r\n  // Cost escalation rates (annual)\r\n  private static readonly DEFAULT_ESCALATION_RATES = {\r\n    ENERGY: 0.03, // 3% annual energy cost escalation\r\n    MAINTENANCE: 0.025, // 2.5% annual maintenance cost escalation\r\n    LABOR: 0.035, // 3.5% annual labor cost escalation\r\n    MATERIALS: 0.028 // 2.8% annual materials cost escalation\r\n  };\r\n\r\n  // Equipment life expectancy (years)\r\n  private static readonly EQUIPMENT_LIFE = {\r\n    FANS: 20,\r\n    MOTORS: 15,\r\n    VFD: 12,\r\n    DUCTWORK: 30,\r\n    DAMPERS: 20,\r\n    CONTROLS: 10,\r\n    FILTERS: 0.25 // 3 months\r\n  };\r\n\r\n  /**\r\n   * Perform comprehensive lifecycle cost analysis\r\n   */\r\n  public static async analyzeLifecycleCosts(\r\n    systemConfiguration: SystemConfiguration,\r\n    energyAnalysis: EnergyAnalysis,\r\n    analysisParameters?: Partial<CostAnalysisParameters>\r\n  ): Promise<LifecycleCostAnalysis> {\r\n    try {\r\n      const analysisId = this.generateAnalysisId(systemConfiguration.id);\r\n      const timestamp = new Date();\r\n\r\n      // Set default analysis parameters\r\n      const parameters = this.setDefaultParameters(analysisParameters);\r\n\r\n      // Calculate initial costs\r\n      const initialCosts = await this.calculateInitialCosts(systemConfiguration);\r\n\r\n      // Calculate operating costs\r\n      const operatingCosts = await this.calculateOperatingCosts(\r\n        systemConfiguration,\r\n        energyAnalysis,\r\n        parameters\r\n      );\r\n\r\n      // Calculate maintenance costs\r\n      const maintenanceCosts = await this.calculateMaintenanceCosts(\r\n        systemConfiguration,\r\n        parameters\r\n      );\r\n\r\n      // Calculate replacement costs\r\n      const replacementCosts = await this.calculateReplacementCosts(\r\n        systemConfiguration,\r\n        parameters\r\n      );\r\n\r\n      // Calculate total cost of ownership\r\n      const totalCostOfOwnership = await this.calculateTotalCostOfOwnership(\r\n        initialCosts,\r\n        operatingCosts,\r\n        maintenanceCosts,\r\n        replacementCosts,\r\n        parameters\r\n      );\r\n\r\n      // Perform cost comparison\r\n      const costComparison = await this.performCostComparison(\r\n        systemConfiguration,\r\n        totalCostOfOwnership\r\n      );\r\n\r\n      // Perform sensitivity analysis\r\n      const sensitivityAnalysis = await this.performSensitivityAnalysis(\r\n        systemConfiguration,\r\n        parameters,\r\n        totalCostOfOwnership\r\n      );\r\n\r\n      // Generate cost recommendations\r\n      const recommendations = await this.generateCostRecommendations(\r\n        totalCostOfOwnership,\r\n        sensitivityAnalysis,\r\n        costComparison\r\n      );\r\n\r\n      const analysis: LifecycleCostAnalysis = {\r\n        id: analysisId,\r\n        systemId: systemConfiguration.id,\r\n        analysisTimestamp: timestamp,\r\n        analysisParameters: parameters,\r\n        initialCosts,\r\n        operatingCosts,\r\n        maintenanceCosts,\r\n        replacementCosts,\r\n        totalCostOfOwnership,\r\n        costComparison,\r\n        sensitivityAnalysis,\r\n        recommendations\r\n      };\r\n\r\n      // Cache the analysis\r\n      this.COST_CACHE.set(analysisId, analysis);\r\n\r\n      return analysis;\r\n\r\n    } catch (error) {\r\n      throw new Error(`Lifecycle cost analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Set default analysis parameters\r\n   */\r\n  private static setDefaultParameters(\r\n    provided?: Partial<CostAnalysisParameters>\r\n  ): CostAnalysisParameters {\r\n    return {\r\n      analysisHorizon: provided?.analysisHorizon || 20, // 20 years\r\n      discountRate: provided?.discountRate || 0.06, // 6% discount rate\r\n      inflationRate: provided?.inflationRate || 0.025, // 2.5% inflation\r\n      energyEscalationRate: provided?.energyEscalationRate || this.DEFAULT_ESCALATION_RATES.ENERGY,\r\n      currency: provided?.currency || 'USD',\r\n      analysisMethod: provided?.analysisMethod || CostAnalysisMethod.NET_PRESENT_VALUE,\r\n      uncertaintyLevel: provided?.uncertaintyLevel || UncertaintyLevel.MEDIUM\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate initial system costs\r\n   */\r\n  private static async calculateInitialCosts(\r\n    systemConfiguration: SystemConfiguration\r\n  ): Promise<InitialCosts> {\r\n    const designAirflow = systemConfiguration.designParameters.designAirflow;\r\n    const systemType = systemConfiguration.systemType;\r\n\r\n    // Equipment costs based on system size and type\r\n    const equipmentCosts = this.calculateEquipmentCosts(systemConfiguration);\r\n\r\n    // Installation costs (typically 40-60% of equipment costs)\r\n    const installationCosts = this.calculateInstallationCosts(equipmentCosts);\r\n\r\n    // Design costs (typically 8-12% of total project cost)\r\n    const designCosts = this.calculateDesignCosts(equipmentCosts, installationCosts);\r\n\r\n    // Permits and fees (typically 2-5% of total project cost)\r\n    const permitsCosts = this.calculatePermitsCosts(equipmentCosts, installationCosts);\r\n\r\n    const totalInitialCost = equipmentCosts.total + installationCosts.total + \r\n                           designCosts.total + permitsCosts.total;\r\n\r\n    return {\r\n      equipmentCosts,\r\n      installationCosts,\r\n      designCosts,\r\n      permitsCosts,\r\n      totalInitialCost,\r\n      costPerCFM: totalInitialCost / designAirflow,\r\n      costPerSquareFoot: totalInitialCost / 10000 // Assumed building area\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate equipment costs breakdown\r\n   */\r\n  private static calculateEquipmentCosts(systemConfiguration: SystemConfiguration): EquipmentCosts {\r\n    const designAirflow = systemConfiguration.designParameters.designAirflow;\r\n    const designPressure = systemConfiguration.designParameters.designPressure;\r\n\r\n    // Cost factors based on system size ($/CFM)\r\n    const fanCostFactor = this.getFanCostFactor(designAirflow, designPressure);\r\n    const ductworkCostFactor = 8.5; // $/CFM for ductwork\r\n    const fittingsCostFactor = 2.2; // $/CFM for fittings\r\n    const dampersCostFactor = 1.8; // $/CFM for dampers\r\n    const controlsCostFactor = 3.5; // $/CFM for controls\r\n    const accessoriesCostFactor = 1.2; // $/CFM for accessories\r\n\r\n    const fans = designAirflow * fanCostFactor;\r\n    const ductwork = designAirflow * ductworkCostFactor;\r\n    const fittings = designAirflow * fittingsCostFactor;\r\n    const dampers = designAirflow * dampersCostFactor;\r\n    const controls = designAirflow * controlsCostFactor;\r\n    const accessories = designAirflow * accessoriesCostFactor;\r\n\r\n    return {\r\n      fans,\r\n      ductwork,\r\n      fittings,\r\n      dampers,\r\n      controls,\r\n      accessories,\r\n      total: fans + ductwork + fittings + dampers + controls + accessories\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get fan cost factor based on size and pressure\r\n   */\r\n  private static getFanCostFactor(airflow: number, pressure: number): number {\r\n    // Base cost factor\r\n    let costFactor = 12.0; // $/CFM base\r\n\r\n    // Size adjustments\r\n    if (airflow > 50000) costFactor *= 0.85; // Economies of scale\r\n    else if (airflow < 5000) costFactor *= 1.25; // Small system premium\r\n\r\n    // Pressure adjustments\r\n    if (pressure > 4.0) costFactor *= 1.3; // High pressure premium\r\n    else if (pressure < 2.0) costFactor *= 0.9; // Low pressure discount\r\n\r\n    return costFactor;\r\n  }\r\n\r\n  /**\r\n   * Calculate installation costs\r\n   */\r\n  private static calculateInstallationCosts(equipmentCosts: EquipmentCosts): InstallationCosts {\r\n    // Installation cost factors (as percentage of equipment cost)\r\n    const factors = {\r\n      fans: 0.4, // 40% of fan cost\r\n      ductwork: 0.6, // 60% of ductwork cost\r\n      fittings: 0.5, // 50% of fittings cost\r\n      dampers: 0.3, // 30% of dampers cost\r\n      controls: 0.8, // 80% of controls cost (complex installation)\r\n      accessories: 0.4 // 40% of accessories cost\r\n    };\r\n\r\n    const fans = equipmentCosts.fans * factors.fans;\r\n    const ductwork = equipmentCosts.ductwork * factors.ductwork;\r\n    const fittings = equipmentCosts.fittings * factors.fittings;\r\n    const dampers = equipmentCosts.dampers * factors.dampers;\r\n    const controls = equipmentCosts.controls * factors.controls;\r\n    const accessories = equipmentCosts.accessories * factors.accessories;\r\n\r\n    // Additional installation costs\r\n    const laborCosts = (fans + ductwork + fittings + dampers + controls + accessories) * 0.6;\r\n    const materialsCosts = equipmentCosts.total * 0.15; // 15% for installation materials\r\n    const equipmentRental = equipmentCosts.total * 0.05; // 5% for equipment rental\r\n    const testing = equipmentCosts.total * 0.03; // 3% for testing and commissioning\r\n\r\n    const total = fans + ductwork + fittings + dampers + controls + accessories + \r\n                 laborCosts + materialsCosts + equipmentRental + testing;\r\n\r\n    return {\r\n      fans,\r\n      ductwork,\r\n      fittings,\r\n      dampers,\r\n      controls,\r\n      accessories,\r\n      laborCosts,\r\n      materialsCosts,\r\n      equipmentRental,\r\n      testing,\r\n      total\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate design costs\r\n   */\r\n  private static calculateDesignCosts(\r\n    equipmentCosts: EquipmentCosts,\r\n    installationCosts: InstallationCosts\r\n  ): DesignCosts {\r\n    const projectCost = equipmentCosts.total + installationCosts.total;\r\n\r\n    const engineeringDesign = projectCost * 0.08; // 8% for engineering design\r\n    const drawings = projectCost * 0.02; // 2% for drawings and specifications\r\n    const calculations = projectCost * 0.015; // 1.5% for calculations\r\n    const projectManagement = projectCost * 0.025; // 2.5% for project management\r\n\r\n    return {\r\n      engineeringDesign,\r\n      drawings,\r\n      calculations,\r\n      projectManagement,\r\n      total: engineeringDesign + drawings + calculations + projectManagement\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate permits and fees\r\n   */\r\n  private static calculatePermitsCosts(\r\n    equipmentCosts: EquipmentCosts,\r\n    installationCosts: InstallationCosts\r\n  ): PermitsCosts {\r\n    const projectCost = equipmentCosts.total + installationCosts.total;\r\n\r\n    const buildingPermits = projectCost * 0.015; // 1.5% for building permits\r\n    const inspectionFees = projectCost * 0.008; // 0.8% for inspection fees\r\n    const utilityConnections = 2500; // Fixed cost for utility connections\r\n    const environmentalFees = projectCost * 0.005; // 0.5% for environmental fees\r\n\r\n    return {\r\n      buildingPermits,\r\n      inspectionFees,\r\n      utilityConnections,\r\n      environmentalFees,\r\n      total: buildingPermits + inspectionFees + utilityConnections + environmentalFees\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate operating costs over analysis horizon\r\n   */\r\n  private static async calculateOperatingCosts(\r\n    systemConfiguration: SystemConfiguration,\r\n    energyAnalysis: EnergyAnalysis,\r\n    parameters: CostAnalysisParameters\r\n  ): Promise<OperatingCosts> {\r\n    const analysisHorizon = parameters.analysisHorizon;\r\n    const energyEscalationRate = parameters.energyEscalationRate;\r\n    const discountRate = parameters.discountRate;\r\n\r\n    // Annual energy costs\r\n    const annualEnergyCosts = energyAnalysis.energyCosts.currentCosts.totalCost;\r\n\r\n    // Calculate present value of energy costs over analysis horizon\r\n    let totalEnergyPV = 0;\r\n    const yearlyEnergyCosts: number[] = [];\r\n\r\n    for (let year = 1; year <= analysisHorizon; year++) {\r\n      const yearlyEnergyCost = annualEnergyCosts * Math.pow(1 + energyEscalationRate, year - 1);\r\n      const presentValue = yearlyEnergyCost / Math.pow(1 + discountRate, year);\r\n      totalEnergyPV += presentValue;\r\n      yearlyEnergyCosts.push(yearlyEnergyCost);\r\n    }\r\n\r\n    // Other operating costs\r\n    const annualInsurance = annualEnergyCosts * 0.02; // 2% of energy costs\r\n    const annualUtilities = 1200; // Fixed annual utilities\r\n    const annualCompliance = 800; // Annual compliance and reporting costs\r\n\r\n    // Calculate present values for other operating costs\r\n    const insurancePV = this.calculatePresentValue(annualInsurance, parameters.inflationRate, discountRate, analysisHorizon);\r\n    const utilitiesPV = this.calculatePresentValue(annualUtilities, parameters.inflationRate, discountRate, analysisHorizon);\r\n    const compliancePV = this.calculatePresentValue(annualCompliance, parameters.inflationRate, discountRate, analysisHorizon);\r\n\r\n    const totalOperatingPV = totalEnergyPV + insurancePV + utilitiesPV + compliancePV;\r\n\r\n    return {\r\n      energyCosts: {\r\n        annual: annualEnergyCosts,\r\n        presentValue: totalEnergyPV,\r\n        yearlyProjection: yearlyEnergyCosts,\r\n        escalationRate: energyEscalationRate\r\n      },\r\n      maintenanceCosts: {\r\n        annual: 0, // Calculated separately\r\n        presentValue: 0,\r\n        yearlyProjection: [],\r\n        escalationRate: this.DEFAULT_ESCALATION_RATES.MAINTENANCE\r\n      },\r\n      insuranceCosts: {\r\n        annual: annualInsurance,\r\n        presentValue: insurancePV,\r\n        escalationRate: parameters.inflationRate\r\n      },\r\n      utilityCosts: {\r\n        annual: annualUtilities,\r\n        presentValue: utilitiesPV,\r\n        escalationRate: parameters.inflationRate\r\n      },\r\n      complianceCosts: {\r\n        annual: annualCompliance,\r\n        presentValue: compliancePV,\r\n        escalationRate: parameters.inflationRate\r\n      },\r\n      totalAnnual: annualEnergyCosts + annualInsurance + annualUtilities + annualCompliance,\r\n      totalPresentValue: totalOperatingPV\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate present value of annual costs\r\n   */\r\n  private static calculatePresentValue(\r\n    annualCost: number,\r\n    escalationRate: number,\r\n    discountRate: number,\r\n    years: number\r\n  ): number {\r\n    let presentValue = 0;\r\n    \r\n    for (let year = 1; year <= years; year++) {\r\n      const yearlyValue = annualCost * Math.pow(1 + escalationRate, year - 1);\r\n      presentValue += yearlyValue / Math.pow(1 + discountRate, year);\r\n    }\r\n    \r\n    return presentValue;\r\n  }\r\n\r\n  /**\r\n   * Calculate maintenance costs over analysis horizon\r\n   */\r\n  private static async calculateMaintenanceCosts(\r\n    systemConfiguration: SystemConfiguration,\r\n    parameters: CostAnalysisParameters\r\n  ): Promise<MaintenanceCosts> {\r\n    const designAirflow = systemConfiguration.designParameters.designAirflow;\r\n    const analysisHorizon = parameters.analysisHorizon;\r\n    const discountRate = parameters.discountRate;\r\n    const maintenanceEscalationRate = this.DEFAULT_ESCALATION_RATES.MAINTENANCE;\r\n\r\n    // Annual maintenance costs ($/CFM/year)\r\n    const preventiveMaintenanceFactor = 0.8; // $/CFM/year\r\n    const correctiveMaintenanceFactor = 0.4; // $/CFM/year\r\n    const filterReplacementFactor = 0.6; // $/CFM/year\r\n\r\n    const annualPreventive = designAirflow * preventiveMaintenanceFactor;\r\n    const annualCorrective = designAirflow * correctiveMaintenanceFactor;\r\n    const annualFilters = designAirflow * filterReplacementFactor;\r\n\r\n    // Calculate present values\r\n    const preventivePV = this.calculatePresentValue(annualPreventive, maintenanceEscalationRate, discountRate, analysisHorizon);\r\n    const correctivePV = this.calculatePresentValue(annualCorrective, maintenanceEscalationRate, discountRate, analysisHorizon);\r\n    const filtersPV = this.calculatePresentValue(annualFilters, maintenanceEscalationRate, discountRate, analysisHorizon);\r\n\r\n    // Major overhauls (every 10 years)\r\n    const majorOverhaulCost = designAirflow * 5.0; // $5/CFM\r\n    const overhaulYears = [10, 20].filter(year => year <= analysisHorizon);\r\n    let overhaulsPV = 0;\r\n    \r\n    overhaulYears.forEach(year => {\r\n      const overhaulCostInflated = majorOverhaulCost * Math.pow(1 + maintenanceEscalationRate, year - 1);\r\n      overhaulsPV += overhaulCostInflated / Math.pow(1 + discountRate, year);\r\n    });\r\n\r\n    const totalAnnual = annualPreventive + annualCorrective + annualFilters;\r\n    const totalPresentValue = preventivePV + correctivePV + filtersPV + overhaulsPV;\r\n\r\n    return {\r\n      preventiveMaintenance: {\r\n        annual: annualPreventive,\r\n        presentValue: preventivePV,\r\n        description: 'Regular inspections, cleaning, and adjustments'\r\n      },\r\n      correctiveMaintenance: {\r\n        annual: annualCorrective,\r\n        presentValue: correctivePV,\r\n        description: 'Repairs and component replacements'\r\n      },\r\n      filterReplacement: {\r\n        annual: annualFilters,\r\n        presentValue: filtersPV,\r\n        description: 'Regular filter replacements'\r\n      },\r\n      majorOverhauls: {\r\n        cost: majorOverhaulCost,\r\n        presentValue: overhaulsPV,\r\n        schedule: overhaulYears,\r\n        description: 'Major system overhauls and upgrades'\r\n      },\r\n      totalAnnual,\r\n      totalPresentValue,\r\n      escalationRate: maintenanceEscalationRate\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate replacement costs over analysis horizon\r\n   */\r\n  private static async calculateReplacementCosts(\r\n    systemConfiguration: SystemConfiguration,\r\n    parameters: CostAnalysisParameters\r\n  ): Promise<ReplacementCosts> {\r\n    const analysisHorizon = parameters.analysisHorizon;\r\n    const discountRate = parameters.discountRate;\r\n    const materialEscalationRate = this.DEFAULT_ESCALATION_RATES.MATERIALS;\r\n\r\n    const equipmentReplacements: EquipmentReplacement[] = [];\r\n    let totalReplacementPV = 0;\r\n\r\n    // Calculate replacement costs for each equipment type\r\n    const equipmentTypes = [\r\n      { type: 'fans', life: this.EQUIPMENT_LIFE.FANS, costFactor: 12.0 },\r\n      { type: 'motors', life: this.EQUIPMENT_LIFE.MOTORS, costFactor: 3.5 },\r\n      { type: 'vfd', life: this.EQUIPMENT_LIFE.VFD, costFactor: 2.8 },\r\n      { type: 'dampers', life: this.EQUIPMENT_LIFE.DAMPERS, costFactor: 1.8 },\r\n      { type: 'controls', life: this.EQUIPMENT_LIFE.CONTROLS, costFactor: 3.5 }\r\n    ];\r\n\r\n    const designAirflow = systemConfiguration.designParameters.designAirflow;\r\n\r\n    equipmentTypes.forEach(equipment => {\r\n      const replacementYears = [];\r\n      for (let year = equipment.life; year <= analysisHorizon; year += equipment.life) {\r\n        replacementYears.push(year);\r\n      }\r\n\r\n      if (replacementYears.length > 0) {\r\n        const replacementCost = designAirflow * equipment.costFactor;\r\n        let equipmentPV = 0;\r\n\r\n        replacementYears.forEach(year => {\r\n          const inflatedCost = replacementCost * Math.pow(1 + materialEscalationRate, year - 1);\r\n          const presentValue = inflatedCost / Math.pow(1 + discountRate, year);\r\n          equipmentPV += presentValue;\r\n        });\r\n\r\n        equipmentReplacements.push({\r\n          equipmentType: equipment.type,\r\n          replacementYears,\r\n          unitCost: replacementCost,\r\n          totalPresentValue: equipmentPV,\r\n          description: `${equipment.type} replacement based on ${equipment.life}-year life expectancy`\r\n        });\r\n\r\n        totalReplacementPV += equipmentPV;\r\n      }\r\n    });\r\n\r\n    // Salvage value at end of analysis period\r\n    const salvageValue = this.calculateSalvageValue(systemConfiguration, parameters);\r\n    const salvagePV = salvageValue / Math.pow(1 + discountRate, analysisHorizon);\r\n\r\n    return {\r\n      equipmentReplacements,\r\n      salvageValue: {\r\n        value: salvageValue,\r\n        presentValue: salvagePV,\r\n        description: 'Estimated salvage value at end of analysis period'\r\n      },\r\n      totalReplacementCost: totalReplacementPV,\r\n      netReplacementCost: totalReplacementPV - salvagePV\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate salvage value at end of analysis period\r\n   */\r\n  private static calculateSalvageValue(\r\n    systemConfiguration: SystemConfiguration,\r\n    parameters: CostAnalysisParameters\r\n  ): number {\r\n    const designAirflow = systemConfiguration.designParameters.designAirflow;\r\n    const analysisHorizon = parameters.analysisHorizon;\r\n\r\n    // Calculate remaining life for each equipment type\r\n    const ductworkRemainingLife = Math.max(0, this.EQUIPMENT_LIFE.DUCTWORK - analysisHorizon);\r\n    const fansRemainingLife = Math.max(0, this.EQUIPMENT_LIFE.FANS - (analysisHorizon % this.EQUIPMENT_LIFE.FANS));\r\n    const dampersRemainingLife = Math.max(0, this.EQUIPMENT_LIFE.DAMPERS - (analysisHorizon % this.EQUIPMENT_LIFE.DAMPERS));\r\n\r\n    // Calculate salvage value based on remaining useful life\r\n    const ductworkSalvage = (designAirflow * 8.5) * (ductworkRemainingLife / this.EQUIPMENT_LIFE.DUCTWORK);\r\n    const fansSalvage = (designAirflow * 12.0) * (fansRemainingLife / this.EQUIPMENT_LIFE.FANS);\r\n    const dampersSalvage = (designAirflow * 1.8) * (dampersRemainingLife / this.EQUIPMENT_LIFE.DAMPERS);\r\n\r\n    return ductworkSalvage + fansSalvage + dampersSalvage;\r\n  }\r\n\r\n  /**\r\n   * Calculate total cost of ownership\r\n   */\r\n  private static async calculateTotalCostOfOwnership(\r\n    initialCosts: InitialCosts,\r\n    operatingCosts: OperatingCosts,\r\n    maintenanceCosts: MaintenanceCosts,\r\n    replacementCosts: ReplacementCosts,\r\n    parameters: CostAnalysisParameters\r\n  ): Promise<TotalCostOfOwnership> {\r\n    const totalPresentValue = initialCosts.totalInitialCost +\r\n                             operatingCosts.totalPresentValue +\r\n                             maintenanceCosts.totalPresentValue +\r\n                             replacementCosts.netReplacementCost;\r\n\r\n    const totalAnnualizedCost = this.calculateAnnualizedCost(totalPresentValue, parameters);\r\n\r\n    // Cost breakdown by category\r\n    const costBreakdown = {\r\n      initialCosts: {\r\n        amount: initialCosts.totalInitialCost,\r\n        percentage: (initialCosts.totalInitialCost / totalPresentValue) * 100\r\n      },\r\n      operatingCosts: {\r\n        amount: operatingCosts.totalPresentValue,\r\n        percentage: (operatingCosts.totalPresentValue / totalPresentValue) * 100\r\n      },\r\n      maintenanceCosts: {\r\n        amount: maintenanceCosts.totalPresentValue,\r\n        percentage: (maintenanceCosts.totalPresentValue / totalPresentValue) * 100\r\n      },\r\n      replacementCosts: {\r\n        amount: replacementCosts.netReplacementCost,\r\n        percentage: (replacementCosts.netReplacementCost / totalPresentValue) * 100\r\n      }\r\n    };\r\n\r\n    // Financial metrics\r\n    const financialMetrics = this.calculateFinancialMetrics(\r\n      initialCosts.totalInitialCost,\r\n      operatingCosts.energyCosts.annual + maintenanceCosts.totalAnnual,\r\n      parameters\r\n    );\r\n\r\n    return {\r\n      totalPresentValue,\r\n      totalAnnualizedCost,\r\n      costBreakdown,\r\n      financialMetrics,\r\n      analysisParameters: parameters,\r\n      costPerCFM: totalPresentValue / 10000, // Assuming 10,000 CFM system\r\n      costPerSquareFoot: totalPresentValue / 10000, // Assuming 10,000 sq ft building\r\n      paybackPeriod: this.calculateSimplePaybackPeriod(initialCosts.totalInitialCost, operatingCosts.energyCosts.annual)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate annualized cost\r\n   */\r\n  private static calculateAnnualizedCost(\r\n    presentValue: number,\r\n    parameters: CostAnalysisParameters\r\n  ): number {\r\n    const discountRate = parameters.discountRate;\r\n    const analysisHorizon = parameters.analysisHorizon;\r\n\r\n    // Capital recovery factor\r\n    const crf = (discountRate * Math.pow(1 + discountRate, analysisHorizon)) /\r\n                (Math.pow(1 + discountRate, analysisHorizon) - 1);\r\n\r\n    return presentValue * crf;\r\n  }\r\n\r\n  /**\r\n   * Calculate financial metrics\r\n   */\r\n  private static calculateFinancialMetrics(\r\n    initialCost: number,\r\n    annualSavings: number,\r\n    parameters: CostAnalysisParameters\r\n  ): FinancialMetrics {\r\n    const discountRate = parameters.discountRate;\r\n    const analysisHorizon = parameters.analysisHorizon;\r\n\r\n    // Net Present Value (assuming savings compared to baseline)\r\n    const npv = this.calculateNPV(initialCost, annualSavings, discountRate, analysisHorizon);\r\n\r\n    // Internal Rate of Return (simplified calculation)\r\n    const irr = this.calculateIRR(initialCost, annualSavings, analysisHorizon);\r\n\r\n    // Discounted Payback Period\r\n    const discountedPayback = this.calculateDiscountedPayback(initialCost, annualSavings, discountRate);\r\n\r\n    // Profitability Index\r\n    const profitabilityIndex = (npv + initialCost) / initialCost;\r\n\r\n    return {\r\n      netPresentValue: npv,\r\n      internalRateOfReturn: irr,\r\n      paybackPeriod: this.calculateSimplePaybackPeriod(initialCost, annualSavings),\r\n      discountedPaybackPeriod: discountedPayback,\r\n      profitabilityIndex,\r\n      returnOnInvestment: (annualSavings * analysisHorizon - initialCost) / initialCost * 100\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate Net Present Value\r\n   */\r\n  private static calculateNPV(\r\n    initialCost: number,\r\n    annualCashFlow: number,\r\n    discountRate: number,\r\n    years: number\r\n  ): number {\r\n    let npv = -initialCost;\r\n\r\n    for (let year = 1; year <= years; year++) {\r\n      npv += annualCashFlow / Math.pow(1 + discountRate, year);\r\n    }\r\n\r\n    return npv;\r\n  }\r\n\r\n  /**\r\n   * Calculate Internal Rate of Return (simplified)\r\n   */\r\n  private static calculateIRR(\r\n    initialCost: number,\r\n    annualCashFlow: number,\r\n    years: number\r\n  ): number {\r\n    // Simplified IRR calculation using approximation\r\n    if (annualCashFlow <= 0) return 0;\r\n\r\n    const totalCashFlow = annualCashFlow * years;\r\n    if (totalCashFlow <= initialCost) return 0;\r\n\r\n    // Approximation: IRR ≈ (Total Cash Flow / Initial Cost)^(1/years) - 1\r\n    return Math.pow(totalCashFlow / initialCost, 1 / years) - 1;\r\n  }\r\n\r\n  /**\r\n   * Calculate simple payback period\r\n   */\r\n  private static calculateSimplePaybackPeriod(\r\n    initialCost: number,\r\n    annualSavings: number\r\n  ): number {\r\n    if (annualSavings <= 0) return Infinity;\r\n    return initialCost / annualSavings;\r\n  }\r\n\r\n  /**\r\n   * Calculate discounted payback period\r\n   */\r\n  private static calculateDiscountedPayback(\r\n    initialCost: number,\r\n    annualCashFlow: number,\r\n    discountRate: number\r\n  ): number {\r\n    let cumulativePV = 0;\r\n    let year = 0;\r\n\r\n    while (cumulativePV < initialCost && year < 50) { // Max 50 years\r\n      year++;\r\n      cumulativePV += annualCashFlow / Math.pow(1 + discountRate, year);\r\n    }\r\n\r\n    return year;\r\n  }\r\n\r\n  /**\r\n   * Perform cost comparison with alternatives\r\n   */\r\n  private static async performCostComparison(\r\n    systemConfiguration: SystemConfiguration,\r\n    totalCostOfOwnership: TotalCostOfOwnership\r\n  ): Promise<CostComparison> {\r\n    // Simplified cost comparison with typical alternatives\r\n    const alternatives = [\r\n      {\r\n        name: 'High Efficiency System',\r\n        initialCostMultiplier: 1.25,\r\n        operatingCostMultiplier: 0.8,\r\n        description: 'Premium efficiency equipment with advanced controls'\r\n      },\r\n      {\r\n        name: 'Standard System',\r\n        initialCostMultiplier: 1.0,\r\n        operatingCostMultiplier: 1.0,\r\n        description: 'Standard efficiency equipment (baseline)'\r\n      },\r\n      {\r\n        name: 'Budget System',\r\n        initialCostMultiplier: 0.8,\r\n        operatingCostMultiplier: 1.2,\r\n        description: 'Lower cost equipment with higher operating costs'\r\n      }\r\n    ];\r\n\r\n    const comparisonResults = alternatives.map(alt => ({\r\n      alternativeName: alt.name,\r\n      description: alt.description,\r\n      totalCost: totalCostOfOwnership.totalPresentValue *\r\n                 ((alt.initialCostMultiplier * 0.3) + (alt.operatingCostMultiplier * 0.7)),\r\n      costDifference: (totalCostOfOwnership.totalPresentValue *\r\n                      ((alt.initialCostMultiplier * 0.3) + (alt.operatingCostMultiplier * 0.7))) -\r\n                     totalCostOfOwnership.totalPresentValue,\r\n      percentageDifference: (((alt.initialCostMultiplier * 0.3) + (alt.operatingCostMultiplier * 0.7)) - 1) * 100\r\n    }));\r\n\r\n    return {\r\n      baselineSystem: {\r\n        name: 'Current System Configuration',\r\n        totalCost: totalCostOfOwnership.totalPresentValue,\r\n        description: 'System as currently configured'\r\n      },\r\n      alternatives: comparisonResults,\r\n      recommendedAlternative: comparisonResults.reduce((min, current) =>\r\n        current.totalCost < min.totalCost ? current : min\r\n      ),\r\n      costRankings: comparisonResults.sort((a, b) => a.totalCost - b.totalCost)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Perform sensitivity analysis\r\n   */\r\n  private static async performSensitivityAnalysis(\r\n    systemConfiguration: SystemConfiguration,\r\n    parameters: CostAnalysisParameters,\r\n    totalCostOfOwnership: TotalCostOfOwnership\r\n  ): Promise<CostSensitivityAnalysis> {\r\n    const baselineCost = totalCostOfOwnership.totalPresentValue;\r\n    const sensitivityFactors = [\r\n      { parameter: 'discountRate', baseValue: parameters.discountRate, variations: [-0.02, -0.01, 0.01, 0.02] },\r\n      { parameter: 'energyEscalationRate', baseValue: parameters.energyEscalationRate, variations: [-0.01, -0.005, 0.005, 0.01] },\r\n      { parameter: 'analysisHorizon', baseValue: parameters.analysisHorizon, variations: [-5, -2, 2, 5] },\r\n      { parameter: 'initialCost', baseValue: 1.0, variations: [-0.2, -0.1, 0.1, 0.2] }\r\n    ];\r\n\r\n    const sensitivityResults = sensitivityFactors.map(factor => {\r\n      const impacts = factor.variations.map(variation => {\r\n        let adjustedCost = baselineCost;\r\n\r\n        // Simplified sensitivity calculation\r\n        if (factor.parameter === 'discountRate') {\r\n          const newRate = factor.baseValue + variation;\r\n          adjustedCost = baselineCost * (parameters.discountRate / newRate);\r\n        } else if (factor.parameter === 'energyEscalationRate') {\r\n          adjustedCost = baselineCost * (1 + variation * 2); // Simplified impact\r\n        } else if (factor.parameter === 'analysisHorizon') {\r\n          adjustedCost = baselineCost * (1 + variation * 0.02); // Simplified impact\r\n        } else if (factor.parameter === 'initialCost') {\r\n          adjustedCost = baselineCost * (1 + variation * 0.3); // 30% of total is initial cost\r\n        }\r\n\r\n        return {\r\n          variation,\r\n          adjustedValue: factor.baseValue + variation,\r\n          resultingCost: adjustedCost,\r\n          costChange: adjustedCost - baselineCost,\r\n          percentageChange: ((adjustedCost - baselineCost) / baselineCost) * 100\r\n        };\r\n      });\r\n\r\n      return {\r\n        parameter: factor.parameter,\r\n        baseValue: factor.baseValue,\r\n        impacts,\r\n        sensitivity: Math.max(...impacts.map(i => Math.abs(i.percentageChange))) /\r\n                    Math.max(...factor.variations.map(v => Math.abs(v)))\r\n      };\r\n    });\r\n\r\n    // Identify most sensitive parameters\r\n    const rankedSensitivities = sensitivityResults\r\n      .sort((a, b) => b.sensitivity - a.sensitivity)\r\n      .map((result, index) => {\r\n        let impact: string;\r\n        if (result.sensitivity > 2) {\r\n          impact = 'High';\r\n        } else if (result.sensitivity > 1) {\r\n          impact = 'Medium';\r\n        } else {\r\n          impact = 'Low';\r\n        }\r\n\r\n        return {\r\n          rank: index + 1,\r\n          parameter: result.parameter,\r\n          sensitivity: result.sensitivity,\r\n          impact\r\n        };\r\n      });\r\n\r\n    return {\r\n      baselineCost,\r\n      sensitivityResults,\r\n      rankedSensitivities,\r\n      keyFindings: [\r\n        `Most sensitive parameter: ${rankedSensitivities[0].parameter}`,\r\n        `Cost range: $${Math.min(...sensitivityResults.flatMap(r => r.impacts.map(i => i.resultingCost))).toLocaleString()} - $${Math.max(...sensitivityResults.flatMap(r => r.impacts.map(i => i.resultingCost))).toLocaleString()}`,\r\n        `Sensitivity analysis shows ${rankedSensitivities.filter(r => r.impact === 'High').length} high-impact parameters`\r\n      ]\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Generate cost recommendations\r\n   */\r\n  private static async generateCostRecommendations(\r\n    totalCostOfOwnership: TotalCostOfOwnership,\r\n    sensitivityAnalysis: CostSensitivityAnalysis,\r\n    costComparison: CostComparison\r\n  ): Promise<CostRecommendation[]> {\r\n    const recommendations: CostRecommendation[] = [];\r\n\r\n    // Operating cost optimization\r\n    if (totalCostOfOwnership.costBreakdown.operatingCosts.percentage > 60) {\r\n      recommendations.push({\r\n        id: 'operating_cost_optimization',\r\n        category: 'Operating Cost Reduction',\r\n        priority: 'High',\r\n        title: 'Focus on Operating Cost Reduction',\r\n        description: 'Operating costs represent a significant portion of total lifecycle costs. Focus on energy efficiency improvements.',\r\n        potentialSavings: totalCostOfOwnership.totalPresentValue * 0.15,\r\n        implementationCost: totalCostOfOwnership.costBreakdown.initialCosts.amount * 0.1,\r\n        paybackPeriod: 3.5,\r\n        actions: [\r\n          'Implement advanced control strategies',\r\n          'Upgrade to high-efficiency equipment',\r\n          'Optimize system operation schedules',\r\n          'Consider demand response programs'\r\n        ],\r\n        riskLevel: 'Low'\r\n      });\r\n    }\r\n\r\n    // Maintenance cost optimization\r\n    if (totalCostOfOwnership.costBreakdown.maintenanceCosts.percentage > 25) {\r\n      recommendations.push({\r\n        id: 'maintenance_optimization',\r\n        category: 'Maintenance Cost Reduction',\r\n        priority: 'Medium',\r\n        title: 'Optimize Maintenance Strategy',\r\n        description: 'Maintenance costs are higher than typical. Consider predictive maintenance and equipment upgrades.',\r\n        potentialSavings: totalCostOfOwnership.totalPresentValue * 0.08,\r\n        implementationCost: 15000,\r\n        paybackPeriod: 4.2,\r\n        actions: [\r\n          'Implement predictive maintenance program',\r\n          'Upgrade to more reliable equipment',\r\n          'Train maintenance staff',\r\n          'Establish preventive maintenance schedules'\r\n        ],\r\n        riskLevel: 'Medium'\r\n      });\r\n    }\r\n\r\n    // Alternative system recommendation\r\n    const bestAlternative = costComparison.recommendedAlternative;\r\n    if (bestAlternative.costDifference < -10000) {\r\n      recommendations.push({\r\n        id: 'alternative_system',\r\n        category: 'System Alternative',\r\n        priority: 'High',\r\n        title: `Consider ${bestAlternative.alternativeName}`,\r\n        description: bestAlternative.description,\r\n        potentialSavings: Math.abs(bestAlternative.costDifference),\r\n        implementationCost: 0, // Alternative, not additional cost\r\n        paybackPeriod: 0,\r\n        actions: [\r\n          'Evaluate alternative system configuration',\r\n          'Perform detailed feasibility study',\r\n          'Consider phased implementation'\r\n        ],\r\n        riskLevel: 'Medium'\r\n      });\r\n    }\r\n\r\n    return recommendations;\r\n  }\r\n\r\n  /**\r\n   * Generate unique analysis ID\r\n   */\r\n  private static generateAnalysisId(systemId: string): string {\r\n    const timestamp = Date.now();\r\n    const random = Math.random().toString(36).substring(2, 8);\r\n    return `cost_analysis_${systemId}_${timestamp}_${random}`;\r\n  }\r\n}\r\n\r\n// Supporting interfaces\r\ninterface EquipmentReplacement {\r\n  equipmentType: string;\r\n  replacementYears: number[];\r\n  unitCost: number;\r\n  totalPresentValue: number;\r\n  description: string;\r\n}\r\n\r\ninterface InstallationCosts {\r\n  fans: number;\r\n  ductwork: number;\r\n  fittings: number;\r\n  dampers: number;\r\n  controls: number;\r\n  accessories: number;\r\n  laborCosts: number;\r\n  materialsCosts: number;\r\n  equipmentRental: number;\r\n  testing: number;\r\n  total: number;\r\n}\r\n\r\ninterface DesignCosts {\r\n  engineeringDesign: number;\r\n  drawings: number;\r\n  calculations: number;\r\n  projectManagement: number;\r\n  total: number;\r\n}\r\n\r\ninterface PermitsCosts {\r\n  buildingPermits: number;\r\n  inspectionFees: number;\r\n  utilityConnections: number;\r\n  environmentalFees: number;\r\n  total: number;\r\n}\r\n\r\ninterface FinancialMetrics {\r\n  netPresentValue: number;\r\n  internalRateOfReturn: number;\r\n  paybackPeriod: number;\r\n  discountedPaybackPeriod: number;\r\n  profitabilityIndex: number;\r\n  returnOnInvestment: number;\r\n}\r\n"], "mappings": ";;AAAA;;;;;;;;;;AAAA;AAAA,SAAAA,cAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;MAAA;QAAAC,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAO,SAAA;MAAA;QAAAD,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;IAAA;IAAAW,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,cAAA;MAAAC,IAAA;MAAAC,QAAA;MAAAC,KAAA;MAAAC,OAAA;MAAAC,cAAA;MAAAC,OAAA;IAAA;IAAAC,eAAA;IAAA5B,IAAA;EAAA;EAAA,IAAA6B,QAAA,GAAA5B,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAA0B,QAAA,CAAA9B,IAAA,KAAA8B,QAAA,CAAA9B,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAA6B,QAAA,CAAA9B,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAA0B,cAAA,GAAAD,QAAA,CAAA9B,IAAA;EAAA;;;;;;;;;;;;;;;;AAWA,MAAAgC,qBAAA;AAAA;AAAA,CAAAjC,aAAA,GAAAoB,CAAA,OAAAc,OAAA;AAqBA;;;;;;;;;;;;AAYA,MAAaC,2BAA2B;EAuBtC;;;EAGO,aAAaC,qBAAqBA,CACvCC,mBAAwC,EACxCC,cAA8B,EAC9BC,kBAAoD;IAAA;IAAAvC,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAEpD,IAAI;MACF,MAAMoB,UAAU;MAAA;MAAA,CAAAxC,aAAA,GAAAoB,CAAA,OAAG,IAAI,CAACqB,kBAAkB,CAACJ,mBAAmB,CAACK,EAAE,CAAC;MAClE,MAAMC,SAAS;MAAA;MAAA,CAAA3C,aAAA,GAAAoB,CAAA,OAAG,IAAIwB,IAAI,EAAE;MAE5B;MACA,MAAMC,UAAU;MAAA;MAAA,CAAA7C,aAAA,GAAAoB,CAAA,OAAG,IAAI,CAAC0B,oBAAoB,CAACP,kBAAkB,CAAC;MAEhE;MACA,MAAMQ,YAAY;MAAA;MAAA,CAAA/C,aAAA,GAAAoB,CAAA,OAAG,MAAM,IAAI,CAAC4B,qBAAqB,CAACX,mBAAmB,CAAC;MAE1E;MACA,MAAMY,cAAc;MAAA;MAAA,CAAAjD,aAAA,GAAAoB,CAAA,OAAG,MAAM,IAAI,CAAC8B,uBAAuB,CACvDb,mBAAmB,EACnBC,cAAc,EACdO,UAAU,CACX;MAED;MACA,MAAMM,gBAAgB;MAAA;MAAA,CAAAnD,aAAA,GAAAoB,CAAA,OAAG,MAAM,IAAI,CAACgC,yBAAyB,CAC3Df,mBAAmB,EACnBQ,UAAU,CACX;MAED;MACA,MAAMQ,gBAAgB;MAAA;MAAA,CAAArD,aAAA,GAAAoB,CAAA,QAAG,MAAM,IAAI,CAACkC,yBAAyB,CAC3DjB,mBAAmB,EACnBQ,UAAU,CACX;MAED;MACA,MAAMU,oBAAoB;MAAA;MAAA,CAAAvD,aAAA,GAAAoB,CAAA,QAAG,MAAM,IAAI,CAACoC,6BAA6B,CACnET,YAAY,EACZE,cAAc,EACdE,gBAAgB,EAChBE,gBAAgB,EAChBR,UAAU,CACX;MAED;MACA,MAAMY,cAAc;MAAA;MAAA,CAAAzD,aAAA,GAAAoB,CAAA,QAAG,MAAM,IAAI,CAACsC,qBAAqB,CACrDrB,mBAAmB,EACnBkB,oBAAoB,CACrB;MAED;MACA,MAAMI,mBAAmB;MAAA;MAAA,CAAA3D,aAAA,GAAAoB,CAAA,QAAG,MAAM,IAAI,CAACwC,0BAA0B,CAC/DvB,mBAAmB,EACnBQ,UAAU,EACVU,oBAAoB,CACrB;MAED;MACA,MAAMM,eAAe;MAAA;MAAA,CAAA7D,aAAA,GAAAoB,CAAA,QAAG,MAAM,IAAI,CAAC0C,2BAA2B,CAC5DP,oBAAoB,EACpBI,mBAAmB,EACnBF,cAAc,CACf;MAED,MAAMM,QAAQ;MAAA;MAAA,CAAA/D,aAAA,GAAAoB,CAAA,QAA0B;QACtCsB,EAAE,EAAEF,UAAU;QACdwB,QAAQ,EAAE3B,mBAAmB,CAACK,EAAE;QAChCuB,iBAAiB,EAAEtB,SAAS;QAC5BJ,kBAAkB,EAAEM,UAAU;QAC9BE,YAAY;QACZE,cAAc;QACdE,gBAAgB;QAChBE,gBAAgB;QAChBE,oBAAoB;QACpBE,cAAc;QACdE,mBAAmB;QACnBE;OACD;MAED;MAAA;MAAA7D,aAAA,GAAAoB,CAAA;MACA,IAAI,CAAC8C,UAAU,CAACC,GAAG,CAAC3B,UAAU,EAAEuB,QAAQ,CAAC;MAAC;MAAA/D,aAAA,GAAAoB,CAAA;MAE1C,OAAO2C,QAAQ;IAEjB,CAAC,CAAC,OAAOK,KAAK,EAAE;MAAA;MAAApE,aAAA,GAAAoB,CAAA;MACd,MAAM,IAAIiD,KAAK,CAAC,mCAAmCD,KAAK,YAAYC,KAAK;MAAA;MAAA,CAAArE,aAAA,GAAAsB,CAAA,UAAG8C,KAAK,CAACE,OAAO;MAAA;MAAA,CAAAtE,aAAA,GAAAsB,CAAA,UAAG,eAAe,GAAE,CAAC;IAChH;EACF;EAEA;;;EAGQ,OAAOwB,oBAAoBA,CACjCyB,QAA0C;IAAA;IAAAvE,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAE1C,OAAO;MACLoD,eAAe;MAAE;MAAA,CAAAxE,aAAA,GAAAsB,CAAA,UAAAiD,QAAQ,EAAEC,eAAe;MAAA;MAAA,CAAAxE,aAAA,GAAAsB,CAAA,UAAI,EAAE;MAAE;MAClDmD,YAAY;MAAE;MAAA,CAAAzE,aAAA,GAAAsB,CAAA,UAAAiD,QAAQ,EAAEE,YAAY;MAAA;MAAA,CAAAzE,aAAA,GAAAsB,CAAA,UAAI,IAAI;MAAE;MAC9CoD,aAAa;MAAE;MAAA,CAAA1E,aAAA,GAAAsB,CAAA,UAAAiD,QAAQ,EAAEG,aAAa;MAAA;MAAA,CAAA1E,aAAA,GAAAsB,CAAA,UAAI,KAAK;MAAE;MACjDqD,oBAAoB;MAAE;MAAA,CAAA3E,aAAA,GAAAsB,CAAA,UAAAiD,QAAQ,EAAEI,oBAAoB;MAAA;MAAA,CAAA3E,aAAA,GAAAsB,CAAA,UAAI,IAAI,CAACsD,wBAAwB,CAACC,MAAM;MAC5FC,QAAQ;MAAE;MAAA,CAAA9E,aAAA,GAAAsB,CAAA,UAAAiD,QAAQ,EAAEO,QAAQ;MAAA;MAAA,CAAA9E,aAAA,GAAAsB,CAAA,UAAI,KAAK;MACrCyD,cAAc;MAAE;MAAA,CAAA/E,aAAA,GAAAsB,CAAA,UAAAiD,QAAQ,EAAEQ,cAAc;MAAA;MAAA,CAAA/E,aAAA,GAAAsB,CAAA,UAAIW,qBAAA,CAAA+C,kBAAkB,CAACC,iBAAiB;MAChFC,gBAAgB;MAAE;MAAA,CAAAlF,aAAA,GAAAsB,CAAA,UAAAiD,QAAQ,EAAEW,gBAAgB;MAAA;MAAA,CAAAlF,aAAA,GAAAsB,CAAA,UAAIW,qBAAA,CAAAkD,gBAAgB,CAACC,MAAM;KACxE;EACH;EAEA;;;EAGQ,aAAapC,qBAAqBA,CACxCX,mBAAwC;IAAA;IAAArC,aAAA,GAAAqB,CAAA;IAExC,MAAMgE,aAAa;IAAA;IAAA,CAAArF,aAAA,GAAAoB,CAAA,QAAGiB,mBAAmB,CAACiD,gBAAgB,CAACD,aAAa;IACxE,MAAME,UAAU;IAAA;IAAA,CAAAvF,aAAA,GAAAoB,CAAA,QAAGiB,mBAAmB,CAACkD,UAAU;IAEjD;IACA,MAAMC,cAAc;IAAA;IAAA,CAAAxF,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAACqE,uBAAuB,CAACpD,mBAAmB,CAAC;IAExE;IACA,MAAMqD,iBAAiB;IAAA;IAAA,CAAA1F,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAACuE,0BAA0B,CAACH,cAAc,CAAC;IAEzE;IACA,MAAMI,WAAW;IAAA;IAAA,CAAA5F,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAACyE,oBAAoB,CAACL,cAAc,EAAEE,iBAAiB,CAAC;IAEhF;IACA,MAAMI,YAAY;IAAA;IAAA,CAAA9F,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAAC2E,qBAAqB,CAACP,cAAc,EAAEE,iBAAiB,CAAC;IAElF,MAAMM,gBAAgB;IAAA;IAAA,CAAAhG,aAAA,GAAAoB,CAAA,QAAGoE,cAAc,CAACS,KAAK,GAAGP,iBAAiB,CAACO,KAAK,GAChDL,WAAW,CAACK,KAAK,GAAGH,YAAY,CAACG,KAAK;IAAC;IAAAjG,aAAA,GAAAoB,CAAA;IAE9D,OAAO;MACLoE,cAAc;MACdE,iBAAiB;MACjBE,WAAW;MACXE,YAAY;MACZE,gBAAgB;MAChBE,UAAU,EAAEF,gBAAgB,GAAGX,aAAa;MAC5Cc,iBAAiB,EAAEH,gBAAgB,GAAG,KAAK,CAAC;KAC7C;EACH;EAEA;;;EAGQ,OAAOP,uBAAuBA,CAACpD,mBAAwC;IAAA;IAAArC,aAAA,GAAAqB,CAAA;IAC7E,MAAMgE,aAAa;IAAA;IAAA,CAAArF,aAAA,GAAAoB,CAAA,QAAGiB,mBAAmB,CAACiD,gBAAgB,CAACD,aAAa;IACxE,MAAMe,cAAc;IAAA;IAAA,CAAApG,aAAA,GAAAoB,CAAA,QAAGiB,mBAAmB,CAACiD,gBAAgB,CAACc,cAAc;IAE1E;IACA,MAAMC,aAAa;IAAA;IAAA,CAAArG,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAACkF,gBAAgB,CAACjB,aAAa,EAAEe,cAAc,CAAC;IAC1E,MAAMG,kBAAkB;IAAA;IAAA,CAAAvG,aAAA,GAAAoB,CAAA,QAAG,GAAG,EAAC,CAAC;IAChC,MAAMoF,kBAAkB;IAAA;IAAA,CAAAxG,aAAA,GAAAoB,CAAA,QAAG,GAAG,EAAC,CAAC;IAChC,MAAMqF,iBAAiB;IAAA;IAAA,CAAAzG,aAAA,GAAAoB,CAAA,QAAG,GAAG,EAAC,CAAC;IAC/B,MAAMsF,kBAAkB;IAAA;IAAA,CAAA1G,aAAA,GAAAoB,CAAA,QAAG,GAAG,EAAC,CAAC;IAChC,MAAMuF,qBAAqB;IAAA;IAAA,CAAA3G,aAAA,GAAAoB,CAAA,QAAG,GAAG,EAAC,CAAC;IAEnC,MAAMwF,IAAI;IAAA;IAAA,CAAA5G,aAAA,GAAAoB,CAAA,QAAGiE,aAAa,GAAGgB,aAAa;IAC1C,MAAMQ,QAAQ;IAAA;IAAA,CAAA7G,aAAA,GAAAoB,CAAA,QAAGiE,aAAa,GAAGkB,kBAAkB;IACnD,MAAMO,QAAQ;IAAA;IAAA,CAAA9G,aAAA,GAAAoB,CAAA,QAAGiE,aAAa,GAAGmB,kBAAkB;IACnD,MAAMO,OAAO;IAAA;IAAA,CAAA/G,aAAA,GAAAoB,CAAA,QAAGiE,aAAa,GAAGoB,iBAAiB;IACjD,MAAMO,QAAQ;IAAA;IAAA,CAAAhH,aAAA,GAAAoB,CAAA,QAAGiE,aAAa,GAAGqB,kBAAkB;IACnD,MAAMO,WAAW;IAAA;IAAA,CAAAjH,aAAA,GAAAoB,CAAA,QAAGiE,aAAa,GAAGsB,qBAAqB;IAAC;IAAA3G,aAAA,GAAAoB,CAAA;IAE1D,OAAO;MACLwF,IAAI;MACJC,QAAQ;MACRC,QAAQ;MACRC,OAAO;MACPC,QAAQ;MACRC,WAAW;MACXhB,KAAK,EAAEW,IAAI,GAAGC,QAAQ,GAAGC,QAAQ,GAAGC,OAAO,GAAGC,QAAQ,GAAGC;KAC1D;EACH;EAEA;;;EAGQ,OAAOX,gBAAgBA,CAACY,OAAe,EAAEC,QAAgB;IAAA;IAAAnH,aAAA,GAAAqB,CAAA;IAC/D;IACA,IAAI+F,UAAU;IAAA;IAAA,CAAApH,aAAA,GAAAoB,CAAA,QAAG,IAAI,EAAC,CAAC;IAEvB;IAAA;IAAApB,aAAA,GAAAoB,CAAA;IACA,IAAI8F,OAAO,GAAG,KAAK,EAAE;MAAA;MAAAlH,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAAgG,UAAU,IAAI,IAAI;IAAA,CAAC,CAAC;IAAA,KACpC;MAAA;MAAApH,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA,IAAI8F,OAAO,GAAG,IAAI,EAAE;QAAA;QAAAlH,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QAAAgG,UAAU,IAAI,IAAI;MAAA,CAAC;MAAA;MAAA;QAAApH,aAAA,GAAAsB,CAAA;MAAA;IAAD,CAAC,CAAC;IAE7C;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IACA,IAAI+F,QAAQ,GAAG,GAAG,EAAE;MAAA;MAAAnH,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAAgG,UAAU,IAAI,GAAG;IAAA,CAAC,CAAC;IAAA,KAClC;MAAA;MAAApH,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA,IAAI+F,QAAQ,GAAG,GAAG,EAAE;QAAA;QAAAnH,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QAAAgG,UAAU,IAAI,GAAG;MAAA,CAAC;MAAA;MAAA;QAAApH,aAAA,GAAAsB,CAAA;MAAA;IAAD,CAAC,CAAC;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAE5C,OAAOgG,UAAU;EACnB;EAEA;;;EAGQ,OAAOzB,0BAA0BA,CAACH,cAA8B;IAAA;IAAAxF,aAAA,GAAAqB,CAAA;IACtE;IACA,MAAMgG,OAAO;IAAA;IAAA,CAAArH,aAAA,GAAAoB,CAAA,QAAG;MACdwF,IAAI,EAAE,GAAG;MAAE;MACXC,QAAQ,EAAE,GAAG;MAAE;MACfC,QAAQ,EAAE,GAAG;MAAE;MACfC,OAAO,EAAE,GAAG;MAAE;MACdC,QAAQ,EAAE,GAAG;MAAE;MACfC,WAAW,EAAE,GAAG,CAAC;KAClB;IAED,MAAML,IAAI;IAAA;IAAA,CAAA5G,aAAA,GAAAoB,CAAA,QAAGoE,cAAc,CAACoB,IAAI,GAAGS,OAAO,CAACT,IAAI;IAC/C,MAAMC,QAAQ;IAAA;IAAA,CAAA7G,aAAA,GAAAoB,CAAA,QAAGoE,cAAc,CAACqB,QAAQ,GAAGQ,OAAO,CAACR,QAAQ;IAC3D,MAAMC,QAAQ;IAAA;IAAA,CAAA9G,aAAA,GAAAoB,CAAA,QAAGoE,cAAc,CAACsB,QAAQ,GAAGO,OAAO,CAACP,QAAQ;IAC3D,MAAMC,OAAO;IAAA;IAAA,CAAA/G,aAAA,GAAAoB,CAAA,QAAGoE,cAAc,CAACuB,OAAO,GAAGM,OAAO,CAACN,OAAO;IACxD,MAAMC,QAAQ;IAAA;IAAA,CAAAhH,aAAA,GAAAoB,CAAA,QAAGoE,cAAc,CAACwB,QAAQ,GAAGK,OAAO,CAACL,QAAQ;IAC3D,MAAMC,WAAW;IAAA;IAAA,CAAAjH,aAAA,GAAAoB,CAAA,QAAGoE,cAAc,CAACyB,WAAW,GAAGI,OAAO,CAACJ,WAAW;IAEpE;IACA,MAAMK,UAAU;IAAA;IAAA,CAAAtH,aAAA,GAAAoB,CAAA,QAAG,CAACwF,IAAI,GAAGC,QAAQ,GAAGC,QAAQ,GAAGC,OAAO,GAAGC,QAAQ,GAAGC,WAAW,IAAI,GAAG;IACxF,MAAMM,cAAc;IAAA;IAAA,CAAAvH,aAAA,GAAAoB,CAAA,QAAGoE,cAAc,CAACS,KAAK,GAAG,IAAI,EAAC,CAAC;IACpD,MAAMuB,eAAe;IAAA;IAAA,CAAAxH,aAAA,GAAAoB,CAAA,QAAGoE,cAAc,CAACS,KAAK,GAAG,IAAI,EAAC,CAAC;IACrD,MAAMwB,OAAO;IAAA;IAAA,CAAAzH,aAAA,GAAAoB,CAAA,QAAGoE,cAAc,CAACS,KAAK,GAAG,IAAI,EAAC,CAAC;IAE7C,MAAMA,KAAK;IAAA;IAAA,CAAAjG,aAAA,GAAAoB,CAAA,QAAGwF,IAAI,GAAGC,QAAQ,GAAGC,QAAQ,GAAGC,OAAO,GAAGC,QAAQ,GAAGC,WAAW,GAC9DK,UAAU,GAAGC,cAAc,GAAGC,eAAe,GAAGC,OAAO;IAAC;IAAAzH,aAAA,GAAAoB,CAAA;IAErE,OAAO;MACLwF,IAAI;MACJC,QAAQ;MACRC,QAAQ;MACRC,OAAO;MACPC,QAAQ;MACRC,WAAW;MACXK,UAAU;MACVC,cAAc;MACdC,eAAe;MACfC,OAAO;MACPxB;KACD;EACH;EAEA;;;EAGQ,OAAOJ,oBAAoBA,CACjCL,cAA8B,EAC9BE,iBAAoC;IAAA;IAAA1F,aAAA,GAAAqB,CAAA;IAEpC,MAAMqG,WAAW;IAAA;IAAA,CAAA1H,aAAA,GAAAoB,CAAA,QAAGoE,cAAc,CAACS,KAAK,GAAGP,iBAAiB,CAACO,KAAK;IAElE,MAAM0B,iBAAiB;IAAA;IAAA,CAAA3H,aAAA,GAAAoB,CAAA,QAAGsG,WAAW,GAAG,IAAI,EAAC,CAAC;IAC9C,MAAME,QAAQ;IAAA;IAAA,CAAA5H,aAAA,GAAAoB,CAAA,QAAGsG,WAAW,GAAG,IAAI,EAAC,CAAC;IACrC,MAAMG,YAAY;IAAA;IAAA,CAAA7H,aAAA,GAAAoB,CAAA,QAAGsG,WAAW,GAAG,KAAK,EAAC,CAAC;IAC1C,MAAMI,iBAAiB;IAAA;IAAA,CAAA9H,aAAA,GAAAoB,CAAA,QAAGsG,WAAW,GAAG,KAAK,EAAC,CAAC;IAAA;IAAA1H,aAAA,GAAAoB,CAAA;IAE/C,OAAO;MACLuG,iBAAiB;MACjBC,QAAQ;MACRC,YAAY;MACZC,iBAAiB;MACjB7B,KAAK,EAAE0B,iBAAiB,GAAGC,QAAQ,GAAGC,YAAY,GAAGC;KACtD;EACH;EAEA;;;EAGQ,OAAO/B,qBAAqBA,CAClCP,cAA8B,EAC9BE,iBAAoC;IAAA;IAAA1F,aAAA,GAAAqB,CAAA;IAEpC,MAAMqG,WAAW;IAAA;IAAA,CAAA1H,aAAA,GAAAoB,CAAA,QAAGoE,cAAc,CAACS,KAAK,GAAGP,iBAAiB,CAACO,KAAK;IAElE,MAAM8B,eAAe;IAAA;IAAA,CAAA/H,aAAA,GAAAoB,CAAA,QAAGsG,WAAW,GAAG,KAAK,EAAC,CAAC;IAC7C,MAAMM,cAAc;IAAA;IAAA,CAAAhI,aAAA,GAAAoB,CAAA,QAAGsG,WAAW,GAAG,KAAK,EAAC,CAAC;IAC5C,MAAMO,kBAAkB;IAAA;IAAA,CAAAjI,aAAA,GAAAoB,CAAA,QAAG,IAAI,EAAC,CAAC;IACjC,MAAM8G,iBAAiB;IAAA;IAAA,CAAAlI,aAAA,GAAAoB,CAAA,QAAGsG,WAAW,GAAG,KAAK,EAAC,CAAC;IAAA;IAAA1H,aAAA,GAAAoB,CAAA;IAE/C,OAAO;MACL2G,eAAe;MACfC,cAAc;MACdC,kBAAkB;MAClBC,iBAAiB;MACjBjC,KAAK,EAAE8B,eAAe,GAAGC,cAAc,GAAGC,kBAAkB,GAAGC;KAChE;EACH;EAEA;;;EAGQ,aAAahF,uBAAuBA,CAC1Cb,mBAAwC,EACxCC,cAA8B,EAC9BO,UAAkC;IAAA;IAAA7C,aAAA,GAAAqB,CAAA;IAElC,MAAMmD,eAAe;IAAA;IAAA,CAAAxE,aAAA,GAAAoB,CAAA,QAAGyB,UAAU,CAAC2B,eAAe;IAClD,MAAMG,oBAAoB;IAAA;IAAA,CAAA3E,aAAA,GAAAoB,CAAA,QAAGyB,UAAU,CAAC8B,oBAAoB;IAC5D,MAAMF,YAAY;IAAA;IAAA,CAAAzE,aAAA,GAAAoB,CAAA,QAAGyB,UAAU,CAAC4B,YAAY;IAE5C;IACA,MAAM0D,iBAAiB;IAAA;IAAA,CAAAnI,aAAA,GAAAoB,CAAA,QAAGkB,cAAc,CAAC8F,WAAW,CAACC,YAAY,CAACC,SAAS;IAE3E;IACA,IAAIC,aAAa;IAAA;IAAA,CAAAvI,aAAA,GAAAoB,CAAA,QAAG,CAAC;IACrB,MAAMoH,iBAAiB;IAAA;IAAA,CAAAxI,aAAA,GAAAoB,CAAA,QAAa,EAAE;IAAC;IAAApB,aAAA,GAAAoB,CAAA;IAEvC,KAAK,IAAIqH,IAAI;IAAA;IAAA,CAAAzI,aAAA,GAAAoB,CAAA,QAAG,CAAC,GAAEqH,IAAI,IAAIjE,eAAe,EAAEiE,IAAI,EAAE,EAAE;MAClD,MAAMC,gBAAgB;MAAA;MAAA,CAAA1I,aAAA,GAAAoB,CAAA,QAAG+G,iBAAiB,GAAGQ,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGjE,oBAAoB,EAAE8D,IAAI,GAAG,CAAC,CAAC;MACzF,MAAMI,YAAY;MAAA;MAAA,CAAA7I,aAAA,GAAAoB,CAAA,QAAGsH,gBAAgB,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGnE,YAAY,EAAEgE,IAAI,CAAC;MAAC;MAAAzI,aAAA,GAAAoB,CAAA;MACzEmH,aAAa,IAAIM,YAAY;MAAC;MAAA7I,aAAA,GAAAoB,CAAA;MAC9BoH,iBAAiB,CAACM,IAAI,CAACJ,gBAAgB,CAAC;IAC1C;IAEA;IACA,MAAMK,eAAe;IAAA;IAAA,CAAA/I,aAAA,GAAAoB,CAAA,QAAG+G,iBAAiB,GAAG,IAAI,EAAC,CAAC;IAClD,MAAMa,eAAe;IAAA;IAAA,CAAAhJ,aAAA,GAAAoB,CAAA,QAAG,IAAI,EAAC,CAAC;IAC9B,MAAM6H,gBAAgB;IAAA;IAAA,CAAAjJ,aAAA,GAAAoB,CAAA,QAAG,GAAG,EAAC,CAAC;IAE9B;IACA,MAAM8H,WAAW;IAAA;IAAA,CAAAlJ,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAAC+H,qBAAqB,CAACJ,eAAe,EAAElG,UAAU,CAAC6B,aAAa,EAAED,YAAY,EAAED,eAAe,CAAC;IACxH,MAAM4E,WAAW;IAAA;IAAA,CAAApJ,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAAC+H,qBAAqB,CAACH,eAAe,EAAEnG,UAAU,CAAC6B,aAAa,EAAED,YAAY,EAAED,eAAe,CAAC;IACxH,MAAM6E,YAAY;IAAA;IAAA,CAAArJ,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAAC+H,qBAAqB,CAACF,gBAAgB,EAAEpG,UAAU,CAAC6B,aAAa,EAAED,YAAY,EAAED,eAAe,CAAC;IAE1H,MAAM8E,gBAAgB;IAAA;IAAA,CAAAtJ,aAAA,GAAAoB,CAAA,QAAGmH,aAAa,GAAGW,WAAW,GAAGE,WAAW,GAAGC,YAAY;IAAC;IAAArJ,aAAA,GAAAoB,CAAA;IAElF,OAAO;MACLgH,WAAW,EAAE;QACXmB,MAAM,EAAEpB,iBAAiB;QACzBU,YAAY,EAAEN,aAAa;QAC3BiB,gBAAgB,EAAEhB,iBAAiB;QACnCiB,cAAc,EAAE9E;OACjB;MACDxB,gBAAgB,EAAE;QAChBoG,MAAM,EAAE,CAAC;QAAE;QACXV,YAAY,EAAE,CAAC;QACfW,gBAAgB,EAAE,EAAE;QACpBC,cAAc,EAAE,IAAI,CAAC7E,wBAAwB,CAAC8E;OAC/C;MACDC,cAAc,EAAE;QACdJ,MAAM,EAAER,eAAe;QACvBF,YAAY,EAAEK,WAAW;QACzBO,cAAc,EAAE5G,UAAU,CAAC6B;OAC5B;MACDkF,YAAY,EAAE;QACZL,MAAM,EAAEP,eAAe;QACvBH,YAAY,EAAEO,WAAW;QACzBK,cAAc,EAAE5G,UAAU,CAAC6B;OAC5B;MACDmF,eAAe,EAAE;QACfN,MAAM,EAAEN,gBAAgB;QACxBJ,YAAY,EAAEQ,YAAY;QAC1BI,cAAc,EAAE5G,UAAU,CAAC6B;OAC5B;MACDoF,WAAW,EAAE3B,iBAAiB,GAAGY,eAAe,GAAGC,eAAe,GAAGC,gBAAgB;MACrFc,iBAAiB,EAAET;KACpB;EACH;EAEA;;;EAGQ,OAAOH,qBAAqBA,CAClCa,UAAkB,EAClBP,cAAsB,EACtBhF,YAAoB,EACpBwF,KAAa;IAAA;IAAAjK,aAAA,GAAAqB,CAAA;IAEb,IAAIwH,YAAY;IAAA;IAAA,CAAA7I,aAAA,GAAAoB,CAAA,QAAG,CAAC;IAAC;IAAApB,aAAA,GAAAoB,CAAA;IAErB,KAAK,IAAIqH,IAAI;IAAA;IAAA,CAAAzI,aAAA,GAAAoB,CAAA,SAAG,CAAC,GAAEqH,IAAI,IAAIwB,KAAK,EAAExB,IAAI,EAAE,EAAE;MACxC,MAAMyB,WAAW;MAAA;MAAA,CAAAlK,aAAA,GAAAoB,CAAA,SAAG4I,UAAU,GAAGrB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGa,cAAc,EAAEhB,IAAI,GAAG,CAAC,CAAC;MAAC;MAAAzI,aAAA,GAAAoB,CAAA;MACxEyH,YAAY,IAAIqB,WAAW,GAAGvB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGnE,YAAY,EAAEgE,IAAI,CAAC;IAChE;IAAC;IAAAzI,aAAA,GAAAoB,CAAA;IAED,OAAOyH,YAAY;EACrB;EAEA;;;EAGQ,aAAazF,yBAAyBA,CAC5Cf,mBAAwC,EACxCQ,UAAkC;IAAA;IAAA7C,aAAA,GAAAqB,CAAA;IAElC,MAAMgE,aAAa;IAAA;IAAA,CAAArF,aAAA,GAAAoB,CAAA,SAAGiB,mBAAmB,CAACiD,gBAAgB,CAACD,aAAa;IACxE,MAAMb,eAAe;IAAA;IAAA,CAAAxE,aAAA,GAAAoB,CAAA,SAAGyB,UAAU,CAAC2B,eAAe;IAClD,MAAMC,YAAY;IAAA;IAAA,CAAAzE,aAAA,GAAAoB,CAAA,SAAGyB,UAAU,CAAC4B,YAAY;IAC5C,MAAM0F,yBAAyB;IAAA;IAAA,CAAAnK,aAAA,GAAAoB,CAAA,SAAG,IAAI,CAACwD,wBAAwB,CAAC8E,WAAW;IAE3E;IACA,MAAMU,2BAA2B;IAAA;IAAA,CAAApK,aAAA,GAAAoB,CAAA,SAAG,GAAG,EAAC,CAAC;IACzC,MAAMiJ,2BAA2B;IAAA;IAAA,CAAArK,aAAA,GAAAoB,CAAA,SAAG,GAAG,EAAC,CAAC;IACzC,MAAMkJ,uBAAuB;IAAA;IAAA,CAAAtK,aAAA,GAAAoB,CAAA,SAAG,GAAG,EAAC,CAAC;IAErC,MAAMmJ,gBAAgB;IAAA;IAAA,CAAAvK,aAAA,GAAAoB,CAAA,SAAGiE,aAAa,GAAG+E,2BAA2B;IACpE,MAAMI,gBAAgB;IAAA;IAAA,CAAAxK,aAAA,GAAAoB,CAAA,SAAGiE,aAAa,GAAGgF,2BAA2B;IACpE,MAAMI,aAAa;IAAA;IAAA,CAAAzK,aAAA,GAAAoB,CAAA,SAAGiE,aAAa,GAAGiF,uBAAuB;IAE7D;IACA,MAAMI,YAAY;IAAA;IAAA,CAAA1K,aAAA,GAAAoB,CAAA,SAAG,IAAI,CAAC+H,qBAAqB,CAACoB,gBAAgB,EAAEJ,yBAAyB,EAAE1F,YAAY,EAAED,eAAe,CAAC;IAC3H,MAAMmG,YAAY;IAAA;IAAA,CAAA3K,aAAA,GAAAoB,CAAA,SAAG,IAAI,CAAC+H,qBAAqB,CAACqB,gBAAgB,EAAEL,yBAAyB,EAAE1F,YAAY,EAAED,eAAe,CAAC;IAC3H,MAAMoG,SAAS;IAAA;IAAA,CAAA5K,aAAA,GAAAoB,CAAA,SAAG,IAAI,CAAC+H,qBAAqB,CAACsB,aAAa,EAAEN,yBAAyB,EAAE1F,YAAY,EAAED,eAAe,CAAC;IAErH;IACA,MAAMqG,iBAAiB;IAAA;IAAA,CAAA7K,aAAA,GAAAoB,CAAA,SAAGiE,aAAa,GAAG,GAAG,EAAC,CAAC;IAC/C,MAAMyF,aAAa;IAAA;IAAA,CAAA9K,aAAA,GAAAoB,CAAA,SAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC2J,MAAM,CAACtC,IAAI,IAAI;MAAA;MAAAzI,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MAAA,OAAAqH,IAAI,IAAIjE,eAAe;IAAf,CAAe,CAAC;IACtE,IAAIwG,WAAW;IAAA;IAAA,CAAAhL,aAAA,GAAAoB,CAAA,SAAG,CAAC;IAAC;IAAApB,aAAA,GAAAoB,CAAA;IAEpB0J,aAAa,CAACG,OAAO,CAACxC,IAAI,IAAG;MAAA;MAAAzI,aAAA,GAAAqB,CAAA;MAC3B,MAAM6J,oBAAoB;MAAA;MAAA,CAAAlL,aAAA,GAAAoB,CAAA,SAAGyJ,iBAAiB,GAAGlC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGuB,yBAAyB,EAAE1B,IAAI,GAAG,CAAC,CAAC;MAAC;MAAAzI,aAAA,GAAAoB,CAAA;MACnG4J,WAAW,IAAIE,oBAAoB,GAAGvC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGnE,YAAY,EAAEgE,IAAI,CAAC;IACxE,CAAC,CAAC;IAEF,MAAMqB,WAAW;IAAA;IAAA,CAAA9J,aAAA,GAAAoB,CAAA,SAAGmJ,gBAAgB,GAAGC,gBAAgB,GAAGC,aAAa;IACvE,MAAMV,iBAAiB;IAAA;IAAA,CAAA/J,aAAA,GAAAoB,CAAA,SAAGsJ,YAAY,GAAGC,YAAY,GAAGC,SAAS,GAAGI,WAAW;IAAC;IAAAhL,aAAA,GAAAoB,CAAA;IAEhF,OAAO;MACL+J,qBAAqB,EAAE;QACrB5B,MAAM,EAAEgB,gBAAgB;QACxB1B,YAAY,EAAE6B,YAAY;QAC1BU,WAAW,EAAE;OACd;MACDC,qBAAqB,EAAE;QACrB9B,MAAM,EAAEiB,gBAAgB;QACxB3B,YAAY,EAAE8B,YAAY;QAC1BS,WAAW,EAAE;OACd;MACDE,iBAAiB,EAAE;QACjB/B,MAAM,EAAEkB,aAAa;QACrB5B,YAAY,EAAE+B,SAAS;QACvBQ,WAAW,EAAE;OACd;MACDG,cAAc,EAAE;QACdC,IAAI,EAAEX,iBAAiB;QACvBhC,YAAY,EAAEmC,WAAW;QACzBS,QAAQ,EAAEX,aAAa;QACvBM,WAAW,EAAE;OACd;MACDtB,WAAW;MACXC,iBAAiB;MACjBN,cAAc,EAAEU;KACjB;EACH;EAEA;;;EAGQ,aAAa7G,yBAAyBA,CAC5CjB,mBAAwC,EACxCQ,UAAkC;IAAA;IAAA7C,aAAA,GAAAqB,CAAA;IAElC,MAAMmD,eAAe;IAAA;IAAA,CAAAxE,aAAA,GAAAoB,CAAA,SAAGyB,UAAU,CAAC2B,eAAe;IAClD,MAAMC,YAAY;IAAA;IAAA,CAAAzE,aAAA,GAAAoB,CAAA,SAAGyB,UAAU,CAAC4B,YAAY;IAC5C,MAAMiH,sBAAsB;IAAA;IAAA,CAAA1L,aAAA,GAAAoB,CAAA,SAAG,IAAI,CAACwD,wBAAwB,CAAC+G,SAAS;IAEtE,MAAMC,qBAAqB;IAAA;IAAA,CAAA5L,aAAA,GAAAoB,CAAA,SAA2B,EAAE;IACxD,IAAIyK,kBAAkB;IAAA;IAAA,CAAA7L,aAAA,GAAAoB,CAAA,SAAG,CAAC;IAE1B;IACA,MAAM0K,cAAc;IAAA;IAAA,CAAA9L,aAAA,GAAAoB,CAAA,SAAG,CACrB;MAAEH,IAAI,EAAE,MAAM;MAAE8K,IAAI,EAAE,IAAI,CAACC,cAAc,CAACC,IAAI;MAAE7E,UAAU,EAAE;IAAI,CAAE,EAClE;MAAEnG,IAAI,EAAE,QAAQ;MAAE8K,IAAI,EAAE,IAAI,CAACC,cAAc,CAACE,MAAM;MAAE9E,UAAU,EAAE;IAAG,CAAE,EACrE;MAAEnG,IAAI,EAAE,KAAK;MAAE8K,IAAI,EAAE,IAAI,CAACC,cAAc,CAACG,GAAG;MAAE/E,UAAU,EAAE;IAAG,CAAE,EAC/D;MAAEnG,IAAI,EAAE,SAAS;MAAE8K,IAAI,EAAE,IAAI,CAACC,cAAc,CAACI,OAAO;MAAEhF,UAAU,EAAE;IAAG,CAAE,EACvE;MAAEnG,IAAI,EAAE,UAAU;MAAE8K,IAAI,EAAE,IAAI,CAACC,cAAc,CAACK,QAAQ;MAAEjF,UAAU,EAAE;IAAG,CAAE,CAC1E;IAED,MAAM/B,aAAa;IAAA;IAAA,CAAArF,aAAA,GAAAoB,CAAA,SAAGiB,mBAAmB,CAACiD,gBAAgB,CAACD,aAAa;IAAC;IAAArF,aAAA,GAAAoB,CAAA;IAEzE0K,cAAc,CAACb,OAAO,CAACqB,SAAS,IAAG;MAAA;MAAAtM,aAAA,GAAAqB,CAAA;MACjC,MAAMkL,gBAAgB;MAAA;MAAA,CAAAvM,aAAA,GAAAoB,CAAA,SAAG,EAAE;MAAC;MAAApB,aAAA,GAAAoB,CAAA;MAC5B,KAAK,IAAIqH,IAAI;MAAA;MAAA,CAAAzI,aAAA,GAAAoB,CAAA,SAAGkL,SAAS,CAACP,IAAI,GAAEtD,IAAI,IAAIjE,eAAe,EAAEiE,IAAI,IAAI6D,SAAS,CAACP,IAAI,EAAE;QAAA;QAAA/L,aAAA,GAAAoB,CAAA;QAC/EmL,gBAAgB,CAACzD,IAAI,CAACL,IAAI,CAAC;MAC7B;MAAC;MAAAzI,aAAA,GAAAoB,CAAA;MAED,IAAImL,gBAAgB,CAACC,MAAM,GAAG,CAAC,EAAE;QAAA;QAAAxM,aAAA,GAAAsB,CAAA;QAC/B,MAAMmL,eAAe;QAAA;QAAA,CAAAzM,aAAA,GAAAoB,CAAA,SAAGiE,aAAa,GAAGiH,SAAS,CAAClF,UAAU;QAC5D,IAAIsF,WAAW;QAAA;QAAA,CAAA1M,aAAA,GAAAoB,CAAA,SAAG,CAAC;QAAC;QAAApB,aAAA,GAAAoB,CAAA;QAEpBmL,gBAAgB,CAACtB,OAAO,CAACxC,IAAI,IAAG;UAAA;UAAAzI,aAAA,GAAAqB,CAAA;UAC9B,MAAMsL,YAAY;UAAA;UAAA,CAAA3M,aAAA,GAAAoB,CAAA,SAAGqL,eAAe,GAAG9D,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG8C,sBAAsB,EAAEjD,IAAI,GAAG,CAAC,CAAC;UACrF,MAAMI,YAAY;UAAA;UAAA,CAAA7I,aAAA,GAAAoB,CAAA,SAAGuL,YAAY,GAAGhE,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGnE,YAAY,EAAEgE,IAAI,CAAC;UAAC;UAAAzI,aAAA,GAAAoB,CAAA;UACrEsL,WAAW,IAAI7D,YAAY;QAC7B,CAAC,CAAC;QAAC;QAAA7I,aAAA,GAAAoB,CAAA;QAEHwK,qBAAqB,CAAC9C,IAAI,CAAC;UACzB8D,aAAa,EAAEN,SAAS,CAACrL,IAAI;UAC7BsL,gBAAgB;UAChBM,QAAQ,EAAEJ,eAAe;UACzB1C,iBAAiB,EAAE2C,WAAW;UAC9BtB,WAAW,EAAE,GAAGkB,SAAS,CAACrL,IAAI,yBAAyBqL,SAAS,CAACP,IAAI;SACtE,CAAC;QAAC;QAAA/L,aAAA,GAAAoB,CAAA;QAEHyK,kBAAkB,IAAIa,WAAW;MACnC,CAAC;MAAA;MAAA;QAAA1M,aAAA,GAAAsB,CAAA;MAAA;IACH,CAAC,CAAC;IAEF;IACA,MAAMwL,YAAY;IAAA;IAAA,CAAA9M,aAAA,GAAAoB,CAAA,SAAG,IAAI,CAAC2L,qBAAqB,CAAC1K,mBAAmB,EAAEQ,UAAU,CAAC;IAChF,MAAMmK,SAAS;IAAA;IAAA,CAAAhN,aAAA,GAAAoB,CAAA,SAAG0L,YAAY,GAAGnE,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGnE,YAAY,EAAED,eAAe,CAAC;IAAC;IAAAxE,aAAA,GAAAoB,CAAA;IAE7E,OAAO;MACLwK,qBAAqB;MACrBkB,YAAY,EAAE;QACZG,KAAK,EAAEH,YAAY;QACnBjE,YAAY,EAAEmE,SAAS;QACvB5B,WAAW,EAAE;OACd;MACD8B,oBAAoB,EAAErB,kBAAkB;MACxCsB,kBAAkB,EAAEtB,kBAAkB,GAAGmB;KAC1C;EACH;EAEA;;;EAGQ,OAAOD,qBAAqBA,CAClC1K,mBAAwC,EACxCQ,UAAkC;IAAA;IAAA7C,aAAA,GAAAqB,CAAA;IAElC,MAAMgE,aAAa;IAAA;IAAA,CAAArF,aAAA,GAAAoB,CAAA,SAAGiB,mBAAmB,CAACiD,gBAAgB,CAACD,aAAa;IACxE,MAAMb,eAAe;IAAA;IAAA,CAAAxE,aAAA,GAAAoB,CAAA,SAAGyB,UAAU,CAAC2B,eAAe;IAElD;IACA,MAAM4I,qBAAqB;IAAA;IAAA,CAAApN,aAAA,GAAAoB,CAAA,SAAGuH,IAAI,CAAC0E,GAAG,CAAC,CAAC,EAAE,IAAI,CAACrB,cAAc,CAACsB,QAAQ,GAAG9I,eAAe,CAAC;IACzF,MAAM+I,iBAAiB;IAAA;IAAA,CAAAvN,aAAA,GAAAoB,CAAA,SAAGuH,IAAI,CAAC0E,GAAG,CAAC,CAAC,EAAE,IAAI,CAACrB,cAAc,CAACC,IAAI,GAAIzH,eAAe,GAAG,IAAI,CAACwH,cAAc,CAACC,IAAK,CAAC;IAC9G,MAAMuB,oBAAoB;IAAA;IAAA,CAAAxN,aAAA,GAAAoB,CAAA,SAAGuH,IAAI,CAAC0E,GAAG,CAAC,CAAC,EAAE,IAAI,CAACrB,cAAc,CAACI,OAAO,GAAI5H,eAAe,GAAG,IAAI,CAACwH,cAAc,CAACI,OAAQ,CAAC;IAEvH;IACA,MAAMqB,eAAe;IAAA;IAAA,CAAAzN,aAAA,GAAAoB,CAAA,SAAIiE,aAAa,GAAG,GAAG,IAAK+H,qBAAqB,GAAG,IAAI,CAACpB,cAAc,CAACsB,QAAQ,CAAC;IACtG,MAAMI,WAAW;IAAA;IAAA,CAAA1N,aAAA,GAAAoB,CAAA,SAAIiE,aAAa,GAAG,IAAI,IAAKkI,iBAAiB,GAAG,IAAI,CAACvB,cAAc,CAACC,IAAI,CAAC;IAC3F,MAAM0B,cAAc;IAAA;IAAA,CAAA3N,aAAA,GAAAoB,CAAA,SAAIiE,aAAa,GAAG,GAAG,IAAKmI,oBAAoB,GAAG,IAAI,CAACxB,cAAc,CAACI,OAAO,CAAC;IAAC;IAAApM,aAAA,GAAAoB,CAAA;IAEpG,OAAOqM,eAAe,GAAGC,WAAW,GAAGC,cAAc;EACvD;EAEA;;;EAGQ,aAAanK,6BAA6BA,CAChDT,YAA0B,EAC1BE,cAA8B,EAC9BE,gBAAkC,EAClCE,gBAAkC,EAClCR,UAAkC;IAAA;IAAA7C,aAAA,GAAAqB,CAAA;IAElC,MAAM0I,iBAAiB;IAAA;IAAA,CAAA/J,aAAA,GAAAoB,CAAA,SAAG2B,YAAY,CAACiD,gBAAgB,GAC9B/C,cAAc,CAAC8G,iBAAiB,GAChC5G,gBAAgB,CAAC4G,iBAAiB,GAClC1G,gBAAgB,CAAC8J,kBAAkB;IAE5D,MAAMS,mBAAmB;IAAA;IAAA,CAAA5N,aAAA,GAAAoB,CAAA,SAAG,IAAI,CAACyM,uBAAuB,CAAC9D,iBAAiB,EAAElH,UAAU,CAAC;IAEvF;IACA,MAAMiL,aAAa;IAAA;IAAA,CAAA9N,aAAA,GAAAoB,CAAA,SAAG;MACpB2B,YAAY,EAAE;QACZgL,MAAM,EAAEhL,YAAY,CAACiD,gBAAgB;QACrCgI,UAAU,EAAGjL,YAAY,CAACiD,gBAAgB,GAAG+D,iBAAiB,GAAI;OACnE;MACD9G,cAAc,EAAE;QACd8K,MAAM,EAAE9K,cAAc,CAAC8G,iBAAiB;QACxCiE,UAAU,EAAG/K,cAAc,CAAC8G,iBAAiB,GAAGA,iBAAiB,GAAI;OACtE;MACD5G,gBAAgB,EAAE;QAChB4K,MAAM,EAAE5K,gBAAgB,CAAC4G,iBAAiB;QAC1CiE,UAAU,EAAG7K,gBAAgB,CAAC4G,iBAAiB,GAAGA,iBAAiB,GAAI;OACxE;MACD1G,gBAAgB,EAAE;QAChB0K,MAAM,EAAE1K,gBAAgB,CAAC8J,kBAAkB;QAC3Ca,UAAU,EAAG3K,gBAAgB,CAAC8J,kBAAkB,GAAGpD,iBAAiB,GAAI;;KAE3E;IAED;IACA,MAAMkE,gBAAgB;IAAA;IAAA,CAAAjO,aAAA,GAAAoB,CAAA,SAAG,IAAI,CAAC8M,yBAAyB,CACrDnL,YAAY,CAACiD,gBAAgB,EAC7B/C,cAAc,CAACmF,WAAW,CAACmB,MAAM,GAAGpG,gBAAgB,CAAC2G,WAAW,EAChEjH,UAAU,CACX;IAAC;IAAA7C,aAAA,GAAAoB,CAAA;IAEF,OAAO;MACL2I,iBAAiB;MACjB6D,mBAAmB;MACnBE,aAAa;MACbG,gBAAgB;MAChB1L,kBAAkB,EAAEM,UAAU;MAC9BqD,UAAU,EAAE6D,iBAAiB,GAAG,KAAK;MAAE;MACvC5D,iBAAiB,EAAE4D,iBAAiB,GAAG,KAAK;MAAE;MAC9CoE,aAAa,EAAE,IAAI,CAACC,4BAA4B,CAACrL,YAAY,CAACiD,gBAAgB,EAAE/C,cAAc,CAACmF,WAAW,CAACmB,MAAM;KAClH;EACH;EAEA;;;EAGQ,OAAOsE,uBAAuBA,CACpChF,YAAoB,EACpBhG,UAAkC;IAAA;IAAA7C,aAAA,GAAAqB,CAAA;IAElC,MAAMoD,YAAY;IAAA;IAAA,CAAAzE,aAAA,GAAAoB,CAAA,SAAGyB,UAAU,CAAC4B,YAAY;IAC5C,MAAMD,eAAe;IAAA;IAAA,CAAAxE,aAAA,GAAAoB,CAAA,SAAGyB,UAAU,CAAC2B,eAAe;IAElD;IACA,MAAM6J,GAAG;IAAA;IAAA,CAAArO,aAAA,GAAAoB,CAAA,SAAIqD,YAAY,GAAGkE,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGnE,YAAY,EAAED,eAAe,CAAC,IAC1DmE,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGnE,YAAY,EAAED,eAAe,CAAC,GAAG,CAAC,CAAC;IAAC;IAAAxE,aAAA,GAAAoB,CAAA;IAE9D,OAAOyH,YAAY,GAAGwF,GAAG;EAC3B;EAEA;;;EAGQ,OAAOH,yBAAyBA,CACtCI,WAAmB,EACnBC,aAAqB,EACrB1L,UAAkC;IAAA;IAAA7C,aAAA,GAAAqB,CAAA;IAElC,MAAMoD,YAAY;IAAA;IAAA,CAAAzE,aAAA,GAAAoB,CAAA,SAAGyB,UAAU,CAAC4B,YAAY;IAC5C,MAAMD,eAAe;IAAA;IAAA,CAAAxE,aAAA,GAAAoB,CAAA,SAAGyB,UAAU,CAAC2B,eAAe;IAElD;IACA,MAAMgK,GAAG;IAAA;IAAA,CAAAxO,aAAA,GAAAoB,CAAA,SAAG,IAAI,CAACqN,YAAY,CAACH,WAAW,EAAEC,aAAa,EAAE9J,YAAY,EAAED,eAAe,CAAC;IAExF;IACA,MAAMkK,GAAG;IAAA;IAAA,CAAA1O,aAAA,GAAAoB,CAAA,SAAG,IAAI,CAACuN,YAAY,CAACL,WAAW,EAAEC,aAAa,EAAE/J,eAAe,CAAC;IAE1E;IACA,MAAMoK,iBAAiB;IAAA;IAAA,CAAA5O,aAAA,GAAAoB,CAAA,SAAG,IAAI,CAACyN,0BAA0B,CAACP,WAAW,EAAEC,aAAa,EAAE9J,YAAY,CAAC;IAEnG;IACA,MAAMqK,kBAAkB;IAAA;IAAA,CAAA9O,aAAA,GAAAoB,CAAA,SAAG,CAACoN,GAAG,GAAGF,WAAW,IAAIA,WAAW;IAAC;IAAAtO,aAAA,GAAAoB,CAAA;IAE7D,OAAO;MACL2N,eAAe,EAAEP,GAAG;MACpBQ,oBAAoB,EAAEN,GAAG;MACzBP,aAAa,EAAE,IAAI,CAACC,4BAA4B,CAACE,WAAW,EAAEC,aAAa,CAAC;MAC5EU,uBAAuB,EAAEL,iBAAiB;MAC1CE,kBAAkB;MAClBI,kBAAkB,EAAE,CAACX,aAAa,GAAG/J,eAAe,GAAG8J,WAAW,IAAIA,WAAW,GAAG;KACrF;EACH;EAEA;;;EAGQ,OAAOG,YAAYA,CACzBH,WAAmB,EACnBa,cAAsB,EACtB1K,YAAoB,EACpBwF,KAAa;IAAA;IAAAjK,aAAA,GAAAqB,CAAA;IAEb,IAAImN,GAAG;IAAA;IAAA,CAAAxO,aAAA,GAAAoB,CAAA,SAAG,CAACkN,WAAW;IAAC;IAAAtO,aAAA,GAAAoB,CAAA;IAEvB,KAAK,IAAIqH,IAAI;IAAA;IAAA,CAAAzI,aAAA,GAAAoB,CAAA,SAAG,CAAC,GAAEqH,IAAI,IAAIwB,KAAK,EAAExB,IAAI,EAAE,EAAE;MAAA;MAAAzI,aAAA,GAAAoB,CAAA;MACxCoN,GAAG,IAAIW,cAAc,GAAGxG,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGnE,YAAY,EAAEgE,IAAI,CAAC;IAC1D;IAAC;IAAAzI,aAAA,GAAAoB,CAAA;IAED,OAAOoN,GAAG;EACZ;EAEA;;;EAGQ,OAAOG,YAAYA,CACzBL,WAAmB,EACnBa,cAAsB,EACtBlF,KAAa;IAAA;IAAAjK,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAEb;IACA,IAAI+N,cAAc,IAAI,CAAC,EAAE;MAAA;MAAAnP,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA,OAAO,CAAC;IAAA,CAAC;IAAA;IAAA;MAAApB,aAAA,GAAAsB,CAAA;IAAA;IAElC,MAAM8N,aAAa;IAAA;IAAA,CAAApP,aAAA,GAAAoB,CAAA,SAAG+N,cAAc,GAAGlF,KAAK;IAAC;IAAAjK,aAAA,GAAAoB,CAAA;IAC7C,IAAIgO,aAAa,IAAId,WAAW,EAAE;MAAA;MAAAtO,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA,OAAO,CAAC;IAAA,CAAC;IAAA;IAAA;MAAApB,aAAA,GAAAsB,CAAA;IAAA;IAE3C;IAAAtB,aAAA,GAAAoB,CAAA;IACA,OAAOuH,IAAI,CAACC,GAAG,CAACwG,aAAa,GAAGd,WAAW,EAAE,CAAC,GAAGrE,KAAK,CAAC,GAAG,CAAC;EAC7D;EAEA;;;EAGQ,OAAOmE,4BAA4BA,CACzCE,WAAmB,EACnBC,aAAqB;IAAA;IAAAvO,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAErB,IAAImN,aAAa,IAAI,CAAC,EAAE;MAAA;MAAAvO,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA,OAAOiO,QAAQ;IAAA,CAAC;IAAA;IAAA;MAAArP,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IACxC,OAAOkN,WAAW,GAAGC,aAAa;EACpC;EAEA;;;EAGQ,OAAOM,0BAA0BA,CACvCP,WAAmB,EACnBa,cAAsB,EACtB1K,YAAoB;IAAA;IAAAzE,aAAA,GAAAqB,CAAA;IAEpB,IAAIiO,YAAY;IAAA;IAAA,CAAAtP,aAAA,GAAAoB,CAAA,SAAG,CAAC;IACpB,IAAIqH,IAAI;IAAA;IAAA,CAAAzI,aAAA,GAAAoB,CAAA,SAAG,CAAC;IAAC;IAAApB,aAAA,GAAAoB,CAAA;IAEb;IAAO;IAAA,CAAApB,aAAA,GAAAsB,CAAA,WAAAgO,YAAY,GAAGhB,WAAW;IAAA;IAAA,CAAAtO,aAAA,GAAAsB,CAAA,WAAImH,IAAI,GAAG,EAAE,GAAE;MAAA;MAAAzI,aAAA,GAAAoB,CAAA;MAAE;MAChDqH,IAAI,EAAE;MAAC;MAAAzI,aAAA,GAAAoB,CAAA;MACPkO,YAAY,IAAIH,cAAc,GAAGxG,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGnE,YAAY,EAAEgE,IAAI,CAAC;IACnE;IAAC;IAAAzI,aAAA,GAAAoB,CAAA;IAED,OAAOqH,IAAI;EACb;EAEA;;;EAGQ,aAAa/E,qBAAqBA,CACxCrB,mBAAwC,EACxCkB,oBAA0C;IAAA;IAAAvD,aAAA,GAAAqB,CAAA;IAE1C;IACA,MAAMkO,YAAY;IAAA;IAAA,CAAAvP,aAAA,GAAAoB,CAAA,SAAG,CACnB;MACEP,IAAI,EAAE,wBAAwB;MAC9B2O,qBAAqB,EAAE,IAAI;MAC3BC,uBAAuB,EAAE,GAAG;MAC5BrE,WAAW,EAAE;KACd,EACD;MACEvK,IAAI,EAAE,iBAAiB;MACvB2O,qBAAqB,EAAE,GAAG;MAC1BC,uBAAuB,EAAE,GAAG;MAC5BrE,WAAW,EAAE;KACd,EACD;MACEvK,IAAI,EAAE,eAAe;MACrB2O,qBAAqB,EAAE,GAAG;MAC1BC,uBAAuB,EAAE,GAAG;MAC5BrE,WAAW,EAAE;KACd,CACF;IAED,MAAMsE,iBAAiB;IAAA;IAAA,CAAA1P,aAAA,GAAAoB,CAAA,SAAGmO,YAAY,CAACI,GAAG,CAACC,GAAG,IAAK;MAAA;MAAA5P,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MAAA;QACjDyO,eAAe,EAAED,GAAG,CAAC/O,IAAI;QACzBuK,WAAW,EAAEwE,GAAG,CAACxE,WAAW;QAC5B9C,SAAS,EAAE/E,oBAAoB,CAACwG,iBAAiB,IACpC6F,GAAG,CAACJ,qBAAqB,GAAG,GAAG,GAAKI,GAAG,CAACH,uBAAuB,GAAG,GAAI,CAAC;QACpFK,cAAc,EAAGvM,oBAAoB,CAACwG,iBAAiB,IACrC6F,GAAG,CAACJ,qBAAqB,GAAG,GAAG,GAAKI,GAAG,CAACH,uBAAuB,GAAG,GAAI,CAAC,GAC1ElM,oBAAoB,CAACwG,iBAAiB;QACrDgG,oBAAoB,EAAE,CAAGH,GAAG,CAACJ,qBAAqB,GAAG,GAAG,GAAKI,GAAG,CAACH,uBAAuB,GAAG,GAAI,GAAI,CAAC,IAAI;OACzG;KAAC,CAAC;IAAC;IAAAzP,aAAA,GAAAoB,CAAA;IAEJ,OAAO;MACL4O,cAAc,EAAE;QACdnP,IAAI,EAAE,8BAA8B;QACpCyH,SAAS,EAAE/E,oBAAoB,CAACwG,iBAAiB;QACjDqB,WAAW,EAAE;OACd;MACDmE,YAAY,EAAEG,iBAAiB;MAC/BO,sBAAsB,EAAEP,iBAAiB,CAACQ,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,KAC5D;QAAA;QAAApQ,aAAA,GAAAqB,CAAA;QAAArB,aAAA,GAAAoB,CAAA;QAAA,OAAAgP,OAAO,CAAC9H,SAAS,GAAG6H,GAAG,CAAC7H,SAAS;QAAA;QAAA,CAAAtI,aAAA,GAAAsB,CAAA,WAAG8O,OAAO;QAAA;QAAA,CAAApQ,aAAA,GAAAsB,CAAA,WAAG6O,GAAG;MAAH,CAAG,CAClD;MACDE,YAAY,EAAEX,iBAAiB,CAACY,IAAI,CAAC,CAACC,CAAC,EAAEjP,CAAC,KAAK;QAAA;QAAAtB,aAAA,GAAAqB,CAAA;QAAArB,aAAA,GAAAoB,CAAA;QAAA,OAAAmP,CAAC,CAACjI,SAAS,GAAGhH,CAAC,CAACgH,SAAS;MAAT,CAAS;KACzE;EACH;EAEA;;;EAGQ,aAAa1E,0BAA0BA,CAC7CvB,mBAAwC,EACxCQ,UAAkC,EAClCU,oBAA0C;IAAA;IAAAvD,aAAA,GAAAqB,CAAA;IAE1C,MAAMmP,YAAY;IAAA;IAAA,CAAAxQ,aAAA,GAAAoB,CAAA,SAAGmC,oBAAoB,CAACwG,iBAAiB;IAC3D,MAAM0G,kBAAkB;IAAA;IAAA,CAAAzQ,aAAA,GAAAoB,CAAA,SAAG,CACzB;MAAEsP,SAAS,EAAE,cAAc;MAAEC,SAAS,EAAE9N,UAAU,CAAC4B,YAAY;MAAEmM,UAAU,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI;IAAC,CAAE,EACzG;MAAEF,SAAS,EAAE,sBAAsB;MAAEC,SAAS,EAAE9N,UAAU,CAAC8B,oBAAoB;MAAEiM,UAAU,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI;IAAC,CAAE,EAC3H;MAAEF,SAAS,EAAE,iBAAiB;MAAEC,SAAS,EAAE9N,UAAU,CAAC2B,eAAe;MAAEoM,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;IAAC,CAAE,EACnG;MAAEF,SAAS,EAAE,aAAa;MAAEC,SAAS,EAAE,GAAG;MAAEC,UAAU,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG;IAAC,CAAE,CACjF;IAED,MAAMC,kBAAkB;IAAA;IAAA,CAAA7Q,aAAA,GAAAoB,CAAA,SAAGqP,kBAAkB,CAACd,GAAG,CAACmB,MAAM,IAAG;MAAA;MAAA9Q,aAAA,GAAAqB,CAAA;MACzD,MAAM0P,OAAO;MAAA;MAAA,CAAA/Q,aAAA,GAAAoB,CAAA,SAAG0P,MAAM,CAACF,UAAU,CAACjB,GAAG,CAACqB,SAAS,IAAG;QAAA;QAAAhR,aAAA,GAAAqB,CAAA;QAChD,IAAI4P,YAAY;QAAA;QAAA,CAAAjR,aAAA,GAAAoB,CAAA,SAAGoP,YAAY;QAE/B;QAAA;QAAAxQ,aAAA,GAAAoB,CAAA;QACA,IAAI0P,MAAM,CAACJ,SAAS,KAAK,cAAc,EAAE;UAAA;UAAA1Q,aAAA,GAAAsB,CAAA;UACvC,MAAM4P,OAAO;UAAA;UAAA,CAAAlR,aAAA,GAAAoB,CAAA,SAAG0P,MAAM,CAACH,SAAS,GAAGK,SAAS;UAAC;UAAAhR,aAAA,GAAAoB,CAAA;UAC7C6P,YAAY,GAAGT,YAAY,IAAI3N,UAAU,CAAC4B,YAAY,GAAGyM,OAAO,CAAC;QACnE,CAAC,MAAM;UAAA;UAAAlR,aAAA,GAAAsB,CAAA;UAAAtB,aAAA,GAAAoB,CAAA;UAAA,IAAI0P,MAAM,CAACJ,SAAS,KAAK,sBAAsB,EAAE;YAAA;YAAA1Q,aAAA,GAAAsB,CAAA;YAAAtB,aAAA,GAAAoB,CAAA;YACtD6P,YAAY,GAAGT,YAAY,IAAI,CAAC,GAAGQ,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;UACrD,CAAC,MAAM;YAAA;YAAAhR,aAAA,GAAAsB,CAAA;YAAAtB,aAAA,GAAAoB,CAAA;YAAA,IAAI0P,MAAM,CAACJ,SAAS,KAAK,iBAAiB,EAAE;cAAA;cAAA1Q,aAAA,GAAAsB,CAAA;cAAAtB,aAAA,GAAAoB,CAAA;cACjD6P,YAAY,GAAGT,YAAY,IAAI,CAAC,GAAGQ,SAAS,GAAG,IAAI,CAAC,CAAC,CAAC;YACxD,CAAC,MAAM;cAAA;cAAAhR,aAAA,GAAAsB,CAAA;cAAAtB,aAAA,GAAAoB,CAAA;cAAA,IAAI0P,MAAM,CAACJ,SAAS,KAAK,aAAa,EAAE;gBAAA;gBAAA1Q,aAAA,GAAAsB,CAAA;gBAAAtB,aAAA,GAAAoB,CAAA;gBAC7C6P,YAAY,GAAGT,YAAY,IAAI,CAAC,GAAGQ,SAAS,GAAG,GAAG,CAAC,CAAC,CAAC;cACvD,CAAC;cAAA;cAAA;gBAAAhR,aAAA,GAAAsB,CAAA;cAAA;YAAD;UAAA;QAAA;QAAC;QAAAtB,aAAA,GAAAoB,CAAA;QAED,OAAO;UACL4P,SAAS;UACTG,aAAa,EAAEL,MAAM,CAACH,SAAS,GAAGK,SAAS;UAC3CI,aAAa,EAAEH,YAAY;UAC3BI,UAAU,EAAEJ,YAAY,GAAGT,YAAY;UACvCc,gBAAgB,EAAG,CAACL,YAAY,GAAGT,YAAY,IAAIA,YAAY,GAAI;SACpE;MACH,CAAC,CAAC;MAAC;MAAAxQ,aAAA,GAAAoB,CAAA;MAEH,OAAO;QACLsP,SAAS,EAAEI,MAAM,CAACJ,SAAS;QAC3BC,SAAS,EAAEG,MAAM,CAACH,SAAS;QAC3BI,OAAO;QACPQ,WAAW,EAAE5I,IAAI,CAAC0E,GAAG,CAAC,GAAG0D,OAAO,CAACpB,GAAG,CAAC6B,CAAC,IAAI;UAAA;UAAAxR,aAAA,GAAAqB,CAAA;UAAArB,aAAA,GAAAoB,CAAA;UAAA,OAAAuH,IAAI,CAAC8I,GAAG,CAACD,CAAC,CAACF,gBAAgB,CAAC;QAAD,CAAC,CAAC,CAAC,GAC5D3I,IAAI,CAAC0E,GAAG,CAAC,GAAGyD,MAAM,CAACF,UAAU,CAACjB,GAAG,CAAC+B,CAAC,IAAI;UAAA;UAAA1R,aAAA,GAAAqB,CAAA;UAAArB,aAAA,GAAAoB,CAAA;UAAA,OAAAuH,IAAI,CAAC8I,GAAG,CAACC,CAAC,CAAC;QAAD,CAAC,CAAC;OAChE;IACH,CAAC,CAAC;IAEF;IACA,MAAMC,mBAAmB;IAAA;IAAA,CAAA3R,aAAA,GAAAoB,CAAA,SAAGyP,kBAAkB,CAC3CP,IAAI,CAAC,CAACC,CAAC,EAAEjP,CAAC,KAAK;MAAA;MAAAtB,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MAAA,OAAAE,CAAC,CAACiQ,WAAW,GAAGhB,CAAC,CAACgB,WAAW;IAAX,CAAW,CAAC,CAC7C5B,GAAG,CAAC,CAACiC,MAAM,EAAEC,KAAK,KAAI;MAAA;MAAA7R,aAAA,GAAAqB,CAAA;MACrB,IAAIyQ,MAAc;MAAC;MAAA9R,aAAA,GAAAoB,CAAA;MACnB,IAAIwQ,MAAM,CAACL,WAAW,GAAG,CAAC,EAAE;QAAA;QAAAvR,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QAC1B0Q,MAAM,GAAG,MAAM;MACjB,CAAC,MAAM;QAAA;QAAA9R,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QAAA,IAAIwQ,MAAM,CAACL,WAAW,GAAG,CAAC,EAAE;UAAA;UAAAvR,aAAA,GAAAsB,CAAA;UAAAtB,aAAA,GAAAoB,CAAA;UACjC0Q,MAAM,GAAG,QAAQ;QACnB,CAAC,MAAM;UAAA;UAAA9R,aAAA,GAAAsB,CAAA;UAAAtB,aAAA,GAAAoB,CAAA;UACL0Q,MAAM,GAAG,KAAK;QAChB;MAAA;MAAC;MAAA9R,aAAA,GAAAoB,CAAA;MAED,OAAO;QACL2Q,IAAI,EAAEF,KAAK,GAAG,CAAC;QACfnB,SAAS,EAAEkB,MAAM,CAAClB,SAAS;QAC3Ba,WAAW,EAAEK,MAAM,CAACL,WAAW;QAC/BO;OACD;IACH,CAAC,CAAC;IAAC;IAAA9R,aAAA,GAAAoB,CAAA;IAEL,OAAO;MACLoP,YAAY;MACZK,kBAAkB;MAClBc,mBAAmB;MACnBK,WAAW,EAAE,CACX,6BAA6BL,mBAAmB,CAAC,CAAC,CAAC,CAACjB,SAAS,EAAE,EAC/D,gBAAgB/H,IAAI,CAACwH,GAAG,CAAC,GAAGU,kBAAkB,CAACoB,OAAO,CAACC,CAAC,IAAI;QAAA;QAAAlS,aAAA,GAAAqB,CAAA;QAAArB,aAAA,GAAAoB,CAAA;QAAA,OAAA8Q,CAAC,CAACnB,OAAO,CAACpB,GAAG,CAAC6B,CAAC,IAAI;UAAA;UAAAxR,aAAA,GAAAqB,CAAA;UAAArB,aAAA,GAAAoB,CAAA;UAAA,OAAAoQ,CAAC,CAACJ,aAAa;QAAb,CAAa,CAAC;MAAD,CAAC,CAAC,CAAC,CAACe,cAAc,EAAE,OAAOxJ,IAAI,CAAC0E,GAAG,CAAC,GAAGwD,kBAAkB,CAACoB,OAAO,CAACC,CAAC,IAAI;QAAA;QAAAlS,aAAA,GAAAqB,CAAA;QAAArB,aAAA,GAAAoB,CAAA;QAAA,OAAA8Q,CAAC,CAACnB,OAAO,CAACpB,GAAG,CAAC6B,CAAC,IAAI;UAAA;UAAAxR,aAAA,GAAAqB,CAAA;UAAArB,aAAA,GAAAoB,CAAA;UAAA,OAAAoQ,CAAC,CAACJ,aAAa;QAAb,CAAa,CAAC;MAAD,CAAC,CAAC,CAAC,CAACe,cAAc,EAAE,EAAE,EAC7N,8BAA8BR,mBAAmB,CAAC5G,MAAM,CAACmH,CAAC,IAAI;QAAA;QAAAlS,aAAA,GAAAqB,CAAA;QAAArB,aAAA,GAAAoB,CAAA;QAAA,OAAA8Q,CAAC,CAACJ,MAAM,KAAK,MAAM;MAAN,CAAM,CAAC,CAACtF,MAAM,yBAAyB;KAErH;EACH;EAEA;;;EAGQ,aAAa1I,2BAA2BA,CAC9CP,oBAA0C,EAC1CI,mBAA4C,EAC5CF,cAA8B;IAAA;IAAAzD,aAAA,GAAAqB,CAAA;IAE9B,MAAMwC,eAAe;IAAA;IAAA,CAAA7D,aAAA,GAAAoB,CAAA,SAAyB,EAAE;IAEhD;IAAA;IAAApB,aAAA,GAAAoB,CAAA;IACA,IAAImC,oBAAoB,CAACuK,aAAa,CAAC7K,cAAc,CAAC+K,UAAU,GAAG,EAAE,EAAE;MAAA;MAAAhO,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACrEyC,eAAe,CAACiF,IAAI,CAAC;QACnBpG,EAAE,EAAE,6BAA6B;QACjC0P,QAAQ,EAAE,0BAA0B;QACpCC,QAAQ,EAAE,MAAM;QAChBC,KAAK,EAAE,mCAAmC;QAC1ClH,WAAW,EAAE,oHAAoH;QACjImH,gBAAgB,EAAEhP,oBAAoB,CAACwG,iBAAiB,GAAG,IAAI;QAC/DyI,kBAAkB,EAAEjP,oBAAoB,CAACuK,aAAa,CAAC/K,YAAY,CAACgL,MAAM,GAAG,GAAG;QAChFI,aAAa,EAAE,GAAG;QAClBsE,OAAO,EAAE,CACP,uCAAuC,EACvC,sCAAsC,EACtC,qCAAqC,EACrC,mCAAmC,CACpC;QACDC,SAAS,EAAE;OACZ,CAAC;IACJ,CAAC;IAAA;IAAA;MAAA1S,aAAA,GAAAsB,CAAA;IAAA;IAED;IAAAtB,aAAA,GAAAoB,CAAA;IACA,IAAImC,oBAAoB,CAACuK,aAAa,CAAC3K,gBAAgB,CAAC6K,UAAU,GAAG,EAAE,EAAE;MAAA;MAAAhO,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACvEyC,eAAe,CAACiF,IAAI,CAAC;QACnBpG,EAAE,EAAE,0BAA0B;QAC9B0P,QAAQ,EAAE,4BAA4B;QACtCC,QAAQ,EAAE,QAAQ;QAClBC,KAAK,EAAE,+BAA+B;QACtClH,WAAW,EAAE,oGAAoG;QACjHmH,gBAAgB,EAAEhP,oBAAoB,CAACwG,iBAAiB,GAAG,IAAI;QAC/DyI,kBAAkB,EAAE,KAAK;QACzBrE,aAAa,EAAE,GAAG;QAClBsE,OAAO,EAAE,CACP,0CAA0C,EAC1C,oCAAoC,EACpC,yBAAyB,EACzB,4CAA4C,CAC7C;QACDC,SAAS,EAAE;OACZ,CAAC;IACJ,CAAC;IAAA;IAAA;MAAA1S,aAAA,GAAAsB,CAAA;IAAA;IAED;IACA,MAAMqR,eAAe;IAAA;IAAA,CAAA3S,aAAA,GAAAoB,CAAA,SAAGqC,cAAc,CAACwM,sBAAsB;IAAC;IAAAjQ,aAAA,GAAAoB,CAAA;IAC9D,IAAIuR,eAAe,CAAC7C,cAAc,GAAG,CAAC,KAAK,EAAE;MAAA;MAAA9P,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAC3CyC,eAAe,CAACiF,IAAI,CAAC;QACnBpG,EAAE,EAAE,oBAAoB;QACxB0P,QAAQ,EAAE,oBAAoB;QAC9BC,QAAQ,EAAE,MAAM;QAChBC,KAAK,EAAE,YAAYK,eAAe,CAAC9C,eAAe,EAAE;QACpDzE,WAAW,EAAEuH,eAAe,CAACvH,WAAW;QACxCmH,gBAAgB,EAAE5J,IAAI,CAAC8I,GAAG,CAACkB,eAAe,CAAC7C,cAAc,CAAC;QAC1D0C,kBAAkB,EAAE,CAAC;QAAE;QACvBrE,aAAa,EAAE,CAAC;QAChBsE,OAAO,EAAE,CACP,2CAA2C,EAC3C,oCAAoC,EACpC,gCAAgC,CACjC;QACDC,SAAS,EAAE;OACZ,CAAC;IACJ,CAAC;IAAA;IAAA;MAAA1S,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAED,OAAOyC,eAAe;EACxB;EAEA;;;EAGQ,OAAOpB,kBAAkBA,CAACuB,QAAgB;IAAA;IAAAhE,aAAA,GAAAqB,CAAA;IAChD,MAAMsB,SAAS;IAAA;IAAA,CAAA3C,aAAA,GAAAoB,CAAA,SAAGwB,IAAI,CAACgQ,GAAG,EAAE;IAC5B,MAAMC,MAAM;IAAA;IAAA,CAAA7S,aAAA,GAAAoB,CAAA,SAAGuH,IAAI,CAACkK,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;IAAC;IAAA/S,aAAA,GAAAoB,CAAA;IAC1D,OAAO,iBAAiB4C,QAAQ,IAAIrB,SAAS,IAAIkQ,MAAM,EAAE;EAC3D;;;;AAr7BFG,OAAA,CAAA7Q,2BAAA,GAAAA,2BAAA;AAs7BC;AAAAnC,aAAA,GAAAoB,CAAA;AAr7ByBe,2BAAA,CAAA8Q,OAAO,GAAG,OAAO;AAAC;AAAAjT,aAAA,GAAAoB,CAAA;AAClBe,2BAAA,CAAA+B,UAAU,GAAG,IAAIgP,GAAG,EAAiC;AAE7E;AAAA;AAAAlT,aAAA,GAAAoB,CAAA;AACwBe,2BAAA,CAAAyC,wBAAwB,GAAG;EACjDC,MAAM,EAAE,IAAI;EAAE;EACd6E,WAAW,EAAE,KAAK;EAAE;EACpByJ,KAAK,EAAE,KAAK;EAAE;EACdxH,SAAS,EAAE,KAAK,CAAC;CAClB;AAED;AAAA;AAAA3L,aAAA,GAAAoB,CAAA;AACwBe,2BAAA,CAAA6J,cAAc,GAAG;EACvCC,IAAI,EAAE,EAAE;EACRC,MAAM,EAAE,EAAE;EACVC,GAAG,EAAE,EAAE;EACPmB,QAAQ,EAAE,EAAE;EACZlB,OAAO,EAAE,EAAE;EACXC,QAAQ,EAAE,EAAE;EACZ+G,OAAO,EAAE,IAAI,CAAC;CACf", "ignoreList": []}