daa74b82115a45b7045890252824e8a1
"use strict";

/**
 * Lifecycle Cost Analysis Engine
 *
 * Comprehensive lifecycle cost analysis service for Phase 3 Priority 3: Advanced System Analysis Tools
 * Provides initial costs, operating costs, maintenance costs, energy costs, and total cost of ownership
 * calculations for HVAC duct systems.
 *
 * @version 3.0.0
 * <AUTHOR> Suite Development Team
 */
/* istanbul ignore next */
function cov_ocw6unnfw() {
  var path = "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\LifecycleCostAnalysisEngine.ts";
  var hash = "3ba42157d0696b3cb633fc65a330e5b2244352bb";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\LifecycleCostAnalysisEngine.ts",
    statementMap: {
      "0": {
        start: {
          line: 12,
          column: 0
        },
        end: {
          line: 12,
          column: 62
        }
      },
      "1": {
        start: {
          line: 13,
          column: 0
        },
        end: {
          line: 13,
          column: 45
        }
      },
      "2": {
        start: {
          line: 14,
          column: 30
        },
        end: {
          line: 14,
          column: 68
        }
      },
      "3": {
        start: {
          line: 32,
          column: 8
        },
        end: {
          line: 73,
          column: 9
        }
      },
      "4": {
        start: {
          line: 33,
          column: 31
        },
        end: {
          line: 33,
          column: 78
        }
      },
      "5": {
        start: {
          line: 34,
          column: 30
        },
        end: {
          line: 34,
          column: 40
        }
      },
      "6": {
        start: {
          line: 36,
          column: 31
        },
        end: {
          line: 36,
          column: 76
        }
      },
      "7": {
        start: {
          line: 38,
          column: 33
        },
        end: {
          line: 38,
          column: 86
        }
      },
      "8": {
        start: {
          line: 40,
          column: 35
        },
        end: {
          line: 40,
          column: 118
        }
      },
      "9": {
        start: {
          line: 42,
          column: 37
        },
        end: {
          line: 42,
          column: 106
        }
      },
      "10": {
        start: {
          line: 44,
          column: 37
        },
        end: {
          line: 44,
          column: 106
        }
      },
      "11": {
        start: {
          line: 46,
          column: 41
        },
        end: {
          line: 46,
          column: 159
        }
      },
      "12": {
        start: {
          line: 48,
          column: 35
        },
        end: {
          line: 48,
          column: 110
        }
      },
      "13": {
        start: {
          line: 50,
          column: 40
        },
        end: {
          line: 50,
          column: 132
        }
      },
      "14": {
        start: {
          line: 52,
          column: 36
        },
        end: {
          line: 52,
          column: 133
        }
      },
      "15": {
        start: {
          line: 53,
          column: 29
        },
        end: {
          line: 66,
          column: 13
        }
      },
      "16": {
        start: {
          line: 68,
          column: 12
        },
        end: {
          line: 68,
          column: 54
        }
      },
      "17": {
        start: {
          line: 69,
          column: 12
        },
        end: {
          line: 69,
          column: 28
        }
      },
      "18": {
        start: {
          line: 72,
          column: 12
        },
        end: {
          line: 72,
          column: 123
        }
      },
      "19": {
        start: {
          line: 79,
          column: 8
        },
        end: {
          line: 87,
          column: 10
        }
      },
      "20": {
        start: {
          line: 93,
          column: 30
        },
        end: {
          line: 93,
          column: 80
        }
      },
      "21": {
        start: {
          line: 94,
          column: 27
        },
        end: {
          line: 94,
          column: 57
        }
      },
      "22": {
        start: {
          line: 96,
          column: 31
        },
        end: {
          line: 96,
          column: 80
        }
      },
      "23": {
        start: {
          line: 98,
          column: 34
        },
        end: {
          line: 98,
          column: 81
        }
      },
      "24": {
        start: {
          line: 100,
          column: 28
        },
        end: {
          line: 100,
          column: 88
        }
      },
      "25": {
        start: {
          line: 102,
          column: 29
        },
        end: {
          line: 102,
          column: 90
        }
      },
      "26": {
        start: {
          line: 103,
          column: 33
        },
        end: {
          line: 104,
          column: 50
        }
      },
      "27": {
        start: {
          line: 105,
          column: 8
        },
        end: {
          line: 113,
          column: 10
        }
      },
      "28": {
        start: {
          line: 119,
          column: 30
        },
        end: {
          line: 119,
          column: 80
        }
      },
      "29": {
        start: {
          line: 120,
          column: 31
        },
        end: {
          line: 120,
          column: 82
        }
      },
      "30": {
        start: {
          line: 122,
          column: 30
        },
        end: {
          line: 122,
          column: 82
        }
      },
      "31": {
        start: {
          line: 123,
          column: 35
        },
        end: {
          line: 123,
          column: 38
        }
      },
      "32": {
        start: {
          line: 124,
          column: 35
        },
        end: {
          line: 124,
          column: 38
        }
      },
      "33": {
        start: {
          line: 125,
          column: 34
        },
        end: {
          line: 125,
          column: 37
        }
      },
      "34": {
        start: {
          line: 126,
          column: 35
        },
        end: {
          line: 126,
          column: 38
        }
      },
      "35": {
        start: {
          line: 127,
          column: 38
        },
        end: {
          line: 127,
          column: 41
        }
      },
      "36": {
        start: {
          line: 128,
          column: 21
        },
        end: {
          line: 128,
          column: 50
        }
      },
      "37": {
        start: {
          line: 129,
          column: 25
        },
        end: {
          line: 129,
          column: 59
        }
      },
      "38": {
        start: {
          line: 130,
          column: 25
        },
        end: {
          line: 130,
          column: 59
        }
      },
      "39": {
        start: {
          line: 131,
          column: 24
        },
        end: {
          line: 131,
          column: 57
        }
      },
      "40": {
        start: {
          line: 132,
          column: 25
        },
        end: {
          line: 132,
          column: 59
        }
      },
      "41": {
        start: {
          line: 133,
          column: 28
        },
        end: {
          line: 133,
          column: 65
        }
      },
      "42": {
        start: {
          line: 134,
          column: 8
        },
        end: {
          line: 142,
          column: 10
        }
      },
      "43": {
        start: {
          line: 149,
          column: 25
        },
        end: {
          line: 149,
          column: 29
        }
      },
      "44": {
        start: {
          line: 151,
          column: 8
        },
        end: {
          line: 154,
          column: 31
        }
      },
      "45": {
        start: {
          line: 152,
          column: 12
        },
        end: {
          line: 152,
          column: 31
        }
      },
      "46": {
        start: {
          line: 153,
          column: 13
        },
        end: {
          line: 154,
          column: 31
        }
      },
      "47": {
        start: {
          line: 154,
          column: 12
        },
        end: {
          line: 154,
          column: 31
        }
      },
      "48": {
        start: {
          line: 156,
          column: 8
        },
        end: {
          line: 159,
          column: 30
        }
      },
      "49": {
        start: {
          line: 157,
          column: 12
        },
        end: {
          line: 157,
          column: 30
        }
      },
      "50": {
        start: {
          line: 158,
          column: 13
        },
        end: {
          line: 159,
          column: 30
        }
      },
      "51": {
        start: {
          line: 159,
          column: 12
        },
        end: {
          line: 159,
          column: 30
        }
      },
      "52": {
        start: {
          line: 160,
          column: 8
        },
        end: {
          line: 160,
          column: 26
        }
      },
      "53": {
        start: {
          line: 167,
          column: 24
        },
        end: {
          line: 174,
          column: 9
        }
      },
      "54": {
        start: {
          line: 175,
          column: 21
        },
        end: {
          line: 175,
          column: 55
        }
      },
      "55": {
        start: {
          line: 176,
          column: 25
        },
        end: {
          line: 176,
          column: 67
        }
      },
      "56": {
        start: {
          line: 177,
          column: 25
        },
        end: {
          line: 177,
          column: 67
        }
      },
      "57": {
        start: {
          line: 178,
          column: 24
        },
        end: {
          line: 178,
          column: 64
        }
      },
      "58": {
        start: {
          line: 179,
          column: 25
        },
        end: {
          line: 179,
          column: 67
        }
      },
      "59": {
        start: {
          line: 180,
          column: 28
        },
        end: {
          line: 180,
          column: 76
        }
      },
      "60": {
        start: {
          line: 182,
          column: 27
        },
        end: {
          line: 182,
          column: 96
        }
      },
      "61": {
        start: {
          line: 183,
          column: 31
        },
        end: {
          line: 183,
          column: 58
        }
      },
      "62": {
        start: {
          line: 184,
          column: 32
        },
        end: {
          line: 184,
          column: 59
        }
      },
      "63": {
        start: {
          line: 185,
          column: 24
        },
        end: {
          line: 185,
          column: 51
        }
      },
      "64": {
        start: {
          line: 186,
          column: 22
        },
        end: {
          line: 187,
          column: 67
        }
      },
      "65": {
        start: {
          line: 188,
          column: 8
        },
        end: {
          line: 200,
          column: 10
        }
      },
      "66": {
        start: {
          line: 206,
          column: 28
        },
        end: {
          line: 206,
          column: 74
        }
      },
      "67": {
        start: {
          line: 207,
          column: 34
        },
        end: {
          line: 207,
          column: 52
        }
      },
      "68": {
        start: {
          line: 208,
          column: 25
        },
        end: {
          line: 208,
          column: 43
        }
      },
      "69": {
        start: {
          line: 209,
          column: 29
        },
        end: {
          line: 209,
          column: 48
        }
      },
      "70": {
        start: {
          line: 210,
          column: 34
        },
        end: {
          line: 210,
          column: 53
        }
      },
      "71": {
        start: {
          line: 211,
          column: 8
        },
        end: {
          line: 217,
          column: 10
        }
      },
      "72": {
        start: {
          line: 223,
          column: 28
        },
        end: {
          line: 223,
          column: 74
        }
      },
      "73": {
        start: {
          line: 224,
          column: 32
        },
        end: {
          line: 224,
          column: 51
        }
      },
      "74": {
        start: {
          line: 225,
          column: 31
        },
        end: {
          line: 225,
          column: 50
        }
      },
      "75": {
        start: {
          line: 226,
          column: 35
        },
        end: {
          line: 226,
          column: 39
        }
      },
      "76": {
        start: {
          line: 227,
          column: 34
        },
        end: {
          line: 227,
          column: 53
        }
      },
      "77": {
        start: {
          line: 228,
          column: 8
        },
        end: {
          line: 234,
          column: 10
        }
      },
      "78": {
        start: {
          line: 240,
          column: 32
        },
        end: {
          line: 240,
          column: 58
        }
      },
      "79": {
        start: {
          line: 241,
          column: 37
        },
        end: {
          line: 241,
          column: 68
        }
      },
      "80": {
        start: {
          line: 242,
          column: 29
        },
        end: {
          line: 242,
          column: 52
        }
      },
      "81": {
        start: {
          line: 244,
          column: 34
        },
        end: {
          line: 244,
          column: 83
        }
      },
      "82": {
        start: {
          line: 246,
          column: 28
        },
        end: {
          line: 246,
          column: 29
        }
      },
      "83": {
        start: {
          line: 247,
          column: 34
        },
        end: {
          line: 247,
          column: 36
        }
      },
      "84": {
        start: {
          line: 248,
          column: 8
        },
        end: {
          line: 253,
          column: 9
        }
      },
      "85": {
        start: {
          line: 248,
          column: 24
        },
        end: {
          line: 248,
          column: 25
        }
      },
      "86": {
        start: {
          line: 249,
          column: 37
        },
        end: {
          line: 249,
          column: 101
        }
      },
      "87": {
        start: {
          line: 250,
          column: 33
        },
        end: {
          line: 250,
          column: 84
        }
      },
      "88": {
        start: {
          line: 251,
          column: 12
        },
        end: {
          line: 251,
          column: 42
        }
      },
      "89": {
        start: {
          line: 252,
          column: 12
        },
        end: {
          line: 252,
          column: 53
        }
      },
      "90": {
        start: {
          line: 255,
          column: 32
        },
        end: {
          line: 255,
          column: 56
        }
      },
      "91": {
        start: {
          line: 256,
          column: 32
        },
        end: {
          line: 256,
          column: 36
        }
      },
      "92": {
        start: {
          line: 257,
          column: 33
        },
        end: {
          line: 257,
          column: 36
        }
      },
      "93": {
        start: {
          line: 259,
          column: 28
        },
        end: {
          line: 259,
          column: 128
        }
      },
      "94": {
        start: {
          line: 260,
          column: 28
        },
        end: {
          line: 260,
          column: 128
        }
      },
      "95": {
        start: {
          line: 261,
          column: 29
        },
        end: {
          line: 261,
          column: 130
        }
      },
      "96": {
        start: {
          line: 262,
          column: 33
        },
        end: {
          line: 262,
          column: 89
        }
      },
      "97": {
        start: {
          line: 263,
          column: 8
        },
        end: {
          line: 293,
          column: 10
        }
      },
      "98": {
        start: {
          line: 299,
          column: 27
        },
        end: {
          line: 299,
          column: 28
        }
      },
      "99": {
        start: {
          line: 300,
          column: 8
        },
        end: {
          line: 303,
          column: 9
        }
      },
      "100": {
        start: {
          line: 300,
          column: 24
        },
        end: {
          line: 300,
          column: 25
        }
      },
      "101": {
        start: {
          line: 301,
          column: 32
        },
        end: {
          line: 301,
          column: 83
        }
      },
      "102": {
        start: {
          line: 302,
          column: 12
        },
        end: {
          line: 302,
          column: 75
        }
      },
      "103": {
        start: {
          line: 304,
          column: 8
        },
        end: {
          line: 304,
          column: 28
        }
      },
      "104": {
        start: {
          line: 310,
          column: 30
        },
        end: {
          line: 310,
          column: 80
        }
      },
      "105": {
        start: {
          line: 311,
          column: 32
        },
        end: {
          line: 311,
          column: 58
        }
      },
      "106": {
        start: {
          line: 312,
          column: 29
        },
        end: {
          line: 312,
          column: 52
        }
      },
      "107": {
        start: {
          line: 313,
          column: 42
        },
        end: {
          line: 313,
          column: 83
        }
      },
      "108": {
        start: {
          line: 315,
          column: 44
        },
        end: {
          line: 315,
          column: 47
        }
      },
      "109": {
        start: {
          line: 316,
          column: 44
        },
        end: {
          line: 316,
          column: 47
        }
      },
      "110": {
        start: {
          line: 317,
          column: 40
        },
        end: {
          line: 317,
          column: 43
        }
      },
      "111": {
        start: {
          line: 318,
          column: 33
        },
        end: {
          line: 318,
          column: 76
        }
      },
      "112": {
        start: {
          line: 319,
          column: 33
        },
        end: {
          line: 319,
          column: 76
        }
      },
      "113": {
        start: {
          line: 320,
          column: 30
        },
        end: {
          line: 320,
          column: 69
        }
      },
      "114": {
        start: {
          line: 322,
          column: 29
        },
        end: {
          line: 322,
          column: 131
        }
      },
      "115": {
        start: {
          line: 323,
          column: 29
        },
        end: {
          line: 323,
          column: 131
        }
      },
      "116": {
        start: {
          line: 324,
          column: 26
        },
        end: {
          line: 324,
          column: 125
        }
      },
      "117": {
        start: {
          line: 326,
          column: 34
        },
        end: {
          line: 326,
          column: 53
        }
      },
      "118": {
        start: {
          line: 327,
          column: 30
        },
        end: {
          line: 327,
          column: 78
        }
      },
      "119": {
        start: {
          line: 327,
          column: 54
        },
        end: {
          line: 327,
          column: 77
        }
      },
      "120": {
        start: {
          line: 328,
          column: 26
        },
        end: {
          line: 328,
          column: 27
        }
      },
      "121": {
        start: {
          line: 329,
          column: 8
        },
        end: {
          line: 332,
          column: 11
        }
      },
      "122": {
        start: {
          line: 330,
          column: 41
        },
        end: {
          line: 330,
          column: 110
        }
      },
      "123": {
        start: {
          line: 331,
          column: 12
        },
        end: {
          line: 331,
          column: 83
        }
      },
      "124": {
        start: {
          line: 333,
          column: 28
        },
        end: {
          line: 333,
          column: 79
        }
      },
      "125": {
        start: {
          line: 334,
          column: 34
        },
        end: {
          line: 334,
          column: 87
        }
      },
      "126": {
        start: {
          line: 335,
          column: 8
        },
        end: {
          line: 360,
          column: 10
        }
      },
      "127": {
        start: {
          line: 366,
          column: 32
        },
        end: {
          line: 366,
          column: 58
        }
      },
      "128": {
        start: {
          line: 367,
          column: 29
        },
        end: {
          line: 367,
          column: 52
        }
      },
      "129": {
        start: {
          line: 368,
          column: 39
        },
        end: {
          line: 368,
          column: 78
        }
      },
      "130": {
        start: {
          line: 369,
          column: 38
        },
        end: {
          line: 369,
          column: 40
        }
      },
      "131": {
        start: {
          line: 370,
          column: 33
        },
        end: {
          line: 370,
          column: 34
        }
      },
      "132": {
        start: {
          line: 372,
          column: 31
        },
        end: {
          line: 378,
          column: 9
        }
      },
      "133": {
        start: {
          line: 379,
          column: 30
        },
        end: {
          line: 379,
          column: 80
        }
      },
      "134": {
        start: {
          line: 380,
          column: 8
        },
        end: {
          line: 402,
          column: 11
        }
      },
      "135": {
        start: {
          line: 381,
          column: 37
        },
        end: {
          line: 381,
          column: 39
        }
      },
      "136": {
        start: {
          line: 382,
          column: 12
        },
        end: {
          line: 384,
          column: 13
        }
      },
      "137": {
        start: {
          line: 382,
          column: 28
        },
        end: {
          line: 382,
          column: 42
        }
      },
      "138": {
        start: {
          line: 383,
          column: 16
        },
        end: {
          line: 383,
          column: 44
        }
      },
      "139": {
        start: {
          line: 385,
          column: 12
        },
        end: {
          line: 401,
          column: 13
        }
      },
      "140": {
        start: {
          line: 386,
          column: 40
        },
        end: {
          line: 386,
          column: 76
        }
      },
      "141": {
        start: {
          line: 387,
          column: 34
        },
        end: {
          line: 387,
          column: 35
        }
      },
      "142": {
        start: {
          line: 388,
          column: 16
        },
        end: {
          line: 392,
          column: 19
        }
      },
      "143": {
        start: {
          line: 389,
          column: 41
        },
        end: {
          line: 389,
          column: 105
        }
      },
      "144": {
        start: {
          line: 390,
          column: 41
        },
        end: {
          line: 390,
          column: 88
        }
      },
      "145": {
        start: {
          line: 391,
          column: 20
        },
        end: {
          line: 391,
          column: 48
        }
      },
      "146": {
        start: {
          line: 393,
          column: 16
        },
        end: {
          line: 399,
          column: 19
        }
      },
      "147": {
        start: {
          line: 400,
          column: 16
        },
        end: {
          line: 400,
          column: 50
        }
      },
      "148": {
        start: {
          line: 404,
          column: 29
        },
        end: {
          line: 404,
          column: 88
        }
      },
      "149": {
        start: {
          line: 405,
          column: 26
        },
        end: {
          line: 405,
          column: 84
        }
      },
      "150": {
        start: {
          line: 406,
          column: 8
        },
        end: {
          line: 415,
          column: 10
        }
      },
      "151": {
        start: {
          line: 421,
          column: 30
        },
        end: {
          line: 421,
          column: 80
        }
      },
      "152": {
        start: {
          line: 422,
          column: 32
        },
        end: {
          line: 422,
          column: 58
        }
      },
      "153": {
        start: {
          line: 424,
          column: 38
        },
        end: {
          line: 424,
          column: 97
        }
      },
      "154": {
        start: {
          line: 425,
          column: 34
        },
        end: {
          line: 425,
          column: 118
        }
      },
      "155": {
        start: {
          line: 426,
          column: 37
        },
        end: {
          line: 426,
          column: 127
        }
      },
      "156": {
        start: {
          line: 428,
          column: 32
        },
        end: {
          line: 428,
          column: 110
        }
      },
      "157": {
        start: {
          line: 429,
          column: 28
        },
        end: {
          line: 429,
          column: 99
        }
      },
      "158": {
        start: {
          line: 430,
          column: 31
        },
        end: {
          line: 430,
          column: 107
        }
      },
      "159": {
        start: {
          line: 431,
          column: 8
        },
        end: {
          line: 431,
          column: 62
        }
      },
      "160": {
        start: {
          line: 437,
          column: 34
        },
        end: {
          line: 440,
          column: 47
        }
      },
      "161": {
        start: {
          line: 441,
          column: 36
        },
        end: {
          line: 441,
          column: 95
        }
      },
      "162": {
        start: {
          line: 443,
          column: 30
        },
        end: {
          line: 460,
          column: 9
        }
      },
      "163": {
        start: {
          line: 462,
          column: 33
        },
        end: {
          line: 462,
          column: 172
        }
      },
      "164": {
        start: {
          line: 463,
          column: 8
        },
        end: {
          line: 472,
          column: 10
        }
      },
      "165": {
        start: {
          line: 478,
          column: 29
        },
        end: {
          line: 478,
          column: 52
        }
      },
      "166": {
        start: {
          line: 479,
          column: 32
        },
        end: {
          line: 479,
          column: 58
        }
      },
      "167": {
        start: {
          line: 481,
          column: 20
        },
        end: {
          line: 482,
          column: 61
        }
      },
      "168": {
        start: {
          line: 483,
          column: 8
        },
        end: {
          line: 483,
          column: 34
        }
      },
      "169": {
        start: {
          line: 489,
          column: 29
        },
        end: {
          line: 489,
          column: 52
        }
      },
      "170": {
        start: {
          line: 490,
          column: 32
        },
        end: {
          line: 490,
          column: 58
        }
      },
      "171": {
        start: {
          line: 492,
          column: 20
        },
        end: {
          line: 492,
          column: 96
        }
      },
      "172": {
        start: {
          line: 494,
          column: 20
        },
        end: {
          line: 494,
          column: 82
        }
      },
      "173": {
        start: {
          line: 496,
          column: 34
        },
        end: {
          line: 496,
          column: 107
        }
      },
      "174": {
        start: {
          line: 498,
          column: 35
        },
        end: {
          line: 498,
          column: 68
        }
      },
      "175": {
        start: {
          line: 499,
          column: 8
        },
        end: {
          line: 506,
          column: 10
        }
      },
      "176": {
        start: {
          line: 512,
          column: 18
        },
        end: {
          line: 512,
          column: 30
        }
      },
      "177": {
        start: {
          line: 513,
          column: 8
        },
        end: {
          line: 515,
          column: 9
        }
      },
      "178": {
        start: {
          line: 513,
          column: 24
        },
        end: {
          line: 513,
          column: 25
        }
      },
      "179": {
        start: {
          line: 514,
          column: 12
        },
        end: {
          line: 514,
          column: 69
        }
      },
      "180": {
        start: {
          line: 516,
          column: 8
        },
        end: {
          line: 516,
          column: 19
        }
      },
      "181": {
        start: {
          line: 523,
          column: 8
        },
        end: {
          line: 524,
          column: 21
        }
      },
      "182": {
        start: {
          line: 524,
          column: 12
        },
        end: {
          line: 524,
          column: 21
        }
      },
      "183": {
        start: {
          line: 525,
          column: 30
        },
        end: {
          line: 525,
          column: 52
        }
      },
      "184": {
        start: {
          line: 526,
          column: 8
        },
        end: {
          line: 527,
          column: 21
        }
      },
      "185": {
        start: {
          line: 527,
          column: 12
        },
        end: {
          line: 527,
          column: 21
        }
      },
      "186": {
        start: {
          line: 529,
          column: 8
        },
        end: {
          line: 529,
          column: 68
        }
      },
      "187": {
        start: {
          line: 535,
          column: 8
        },
        end: {
          line: 536,
          column: 28
        }
      },
      "188": {
        start: {
          line: 536,
          column: 12
        },
        end: {
          line: 536,
          column: 28
        }
      },
      "189": {
        start: {
          line: 537,
          column: 8
        },
        end: {
          line: 537,
          column: 43
        }
      },
      "190": {
        start: {
          line: 543,
          column: 27
        },
        end: {
          line: 543,
          column: 28
        }
      },
      "191": {
        start: {
          line: 544,
          column: 19
        },
        end: {
          line: 544,
          column: 20
        }
      },
      "192": {
        start: {
          line: 545,
          column: 8
        },
        end: {
          line: 548,
          column: 9
        }
      },
      "193": {
        start: {
          line: 546,
          column: 12
        },
        end: {
          line: 546,
          column: 19
        }
      },
      "194": {
        start: {
          line: 547,
          column: 12
        },
        end: {
          line: 547,
          column: 78
        }
      },
      "195": {
        start: {
          line: 549,
          column: 8
        },
        end: {
          line: 549,
          column: 20
        }
      },
      "196": {
        start: {
          line: 556,
          column: 29
        },
        end: {
          line: 575,
          column: 9
        }
      },
      "197": {
        start: {
          line: 576,
          column: 34
        },
        end: {
          line: 585,
          column: 11
        }
      },
      "198": {
        start: {
          line: 576,
          column: 59
        },
        end: {
          line: 585,
          column: 9
        }
      },
      "199": {
        start: {
          line: 586,
          column: 8
        },
        end: {
          line: 595,
          column: 10
        }
      },
      "200": {
        start: {
          line: 593,
          column: 79
        },
        end: {
          line: 593,
          column: 128
        }
      },
      "201": {
        start: {
          line: 594,
          column: 59
        },
        end: {
          line: 594,
          column: 84
        }
      },
      "202": {
        start: {
          line: 601,
          column: 29
        },
        end: {
          line: 601,
          column: 67
        }
      },
      "203": {
        start: {
          line: 602,
          column: 35
        },
        end: {
          line: 607,
          column: 9
        }
      },
      "204": {
        start: {
          line: 608,
          column: 35
        },
        end: {
          line: 640,
          column: 10
        }
      },
      "205": {
        start: {
          line: 609,
          column: 28
        },
        end: {
          line: 632,
          column: 14
        }
      },
      "206": {
        start: {
          line: 610,
          column: 35
        },
        end: {
          line: 610,
          column: 47
        }
      },
      "207": {
        start: {
          line: 612,
          column: 16
        },
        end: {
          line: 624,
          column: 17
        }
      },
      "208": {
        start: {
          line: 613,
          column: 36
        },
        end: {
          line: 613,
          column: 64
        }
      },
      "209": {
        start: {
          line: 614,
          column: 20
        },
        end: {
          line: 614,
          column: 86
        }
      },
      "210": {
        start: {
          line: 616,
          column: 21
        },
        end: {
          line: 624,
          column: 17
        }
      },
      "211": {
        start: {
          line: 617,
          column: 20
        },
        end: {
          line: 617,
          column: 70
        }
      },
      "212": {
        start: {
          line: 619,
          column: 21
        },
        end: {
          line: 624,
          column: 17
        }
      },
      "213": {
        start: {
          line: 620,
          column: 20
        },
        end: {
          line: 620,
          column: 73
        }
      },
      "214": {
        start: {
          line: 622,
          column: 21
        },
        end: {
          line: 624,
          column: 17
        }
      },
      "215": {
        start: {
          line: 623,
          column: 20
        },
        end: {
          line: 623,
          column: 72
        }
      },
      "216": {
        start: {
          line: 625,
          column: 16
        },
        end: {
          line: 631,
          column: 18
        }
      },
      "217": {
        start: {
          line: 633,
          column: 12
        },
        end: {
          line: 639,
          column: 14
        }
      },
      "218": {
        start: {
          line: 637,
          column: 58
        },
        end: {
          line: 637,
          column: 86
        }
      },
      "219": {
        start: {
          line: 638,
          column: 59
        },
        end: {
          line: 638,
          column: 70
        }
      },
      "220": {
        start: {
          line: 642,
          column: 36
        },
        end: {
          line: 661,
          column: 10
        }
      },
      "221": {
        start: {
          line: 643,
          column: 28
        },
        end: {
          line: 643,
          column: 57
        }
      },
      "222": {
        start: {
          line: 646,
          column: 12
        },
        end: {
          line: 654,
          column: 13
        }
      },
      "223": {
        start: {
          line: 647,
          column: 16
        },
        end: {
          line: 647,
          column: 32
        }
      },
      "224": {
        start: {
          line: 649,
          column: 17
        },
        end: {
          line: 654,
          column: 13
        }
      },
      "225": {
        start: {
          line: 650,
          column: 16
        },
        end: {
          line: 650,
          column: 34
        }
      },
      "226": {
        start: {
          line: 653,
          column: 16
        },
        end: {
          line: 653,
          column: 31
        }
      },
      "227": {
        start: {
          line: 655,
          column: 12
        },
        end: {
          line: 660,
          column: 14
        }
      },
      "228": {
        start: {
          line: 662,
          column: 8
        },
        end: {
          line: 671,
          column: 10
        }
      },
      "229": {
        start: {
          line: 668,
          column: 76
        },
        end: {
          line: 668,
          column: 111
        }
      },
      "230": {
        start: {
          line: 668,
          column: 95
        },
        end: {
          line: 668,
          column: 110
        }
      },
      "231": {
        start: {
          line: 668,
          column: 181
        },
        end: {
          line: 668,
          column: 216
        }
      },
      "232": {
        start: {
          line: 668,
          column: 200
        },
        end: {
          line: 668,
          column: 215
        }
      },
      "233": {
        start: {
          line: 669,
          column: 78
        },
        end: {
          line: 669,
          column: 97
        }
      },
      "234": {
        start: {
          line: 677,
          column: 32
        },
        end: {
          line: 677,
          column: 34
        }
      },
      "235": {
        start: {
          line: 679,
          column: 8
        },
        end: {
          line: 697,
          column: 9
        }
      },
      "236": {
        start: {
          line: 680,
          column: 12
        },
        end: {
          line: 696,
          column: 15
        }
      },
      "237": {
        start: {
          line: 699,
          column: 8
        },
        end: {
          line: 717,
          column: 9
        }
      },
      "238": {
        start: {
          line: 700,
          column: 12
        },
        end: {
          line: 716,
          column: 15
        }
      },
      "239": {
        start: {
          line: 719,
          column: 32
        },
        end: {
          line: 719,
          column: 69
        }
      },
      "240": {
        start: {
          line: 720,
          column: 8
        },
        end: {
          line: 737,
          column: 9
        }
      },
      "241": {
        start: {
          line: 721,
          column: 12
        },
        end: {
          line: 736,
          column: 15
        }
      },
      "242": {
        start: {
          line: 738,
          column: 8
        },
        end: {
          line: 738,
          column: 31
        }
      },
      "243": {
        start: {
          line: 744,
          column: 26
        },
        end: {
          line: 744,
          column: 36
        }
      },
      "244": {
        start: {
          line: 745,
          column: 23
        },
        end: {
          line: 745,
          column: 65
        }
      },
      "245": {
        start: {
          line: 746,
          column: 8
        },
        end: {
          line: 746,
          column: 66
        }
      },
      "246": {
        start: {
          line: 749,
          column: 0
        },
        end: {
          line: 749,
          column: 66
        }
      },
      "247": {
        start: {
          line: 750,
          column: 0
        },
        end: {
          line: 750,
          column: 46
        }
      },
      "248": {
        start: {
          line: 751,
          column: 0
        },
        end: {
          line: 751,
          column: 51
        }
      },
      "249": {
        start: {
          line: 753,
          column: 0
        },
        end: {
          line: 758,
          column: 2
        }
      },
      "250": {
        start: {
          line: 760,
          column: 0
        },
        end: {
          line: 768,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 31,
            column: 4
          },
          end: {
            line: 31,
            column: 5
          }
        },
        loc: {
          start: {
            line: 31,
            column: 96
          },
          end: {
            line: 74,
            column: 5
          }
        },
        line: 31
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 78,
            column: 4
          },
          end: {
            line: 78,
            column: 5
          }
        },
        loc: {
          start: {
            line: 78,
            column: 42
          },
          end: {
            line: 88,
            column: 5
          }
        },
        line: 78
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 92,
            column: 4
          },
          end: {
            line: 92,
            column: 5
          }
        },
        loc: {
          start: {
            line: 92,
            column: 60
          },
          end: {
            line: 114,
            column: 5
          }
        },
        line: 92
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 118,
            column: 4
          },
          end: {
            line: 118,
            column: 5
          }
        },
        loc: {
          start: {
            line: 118,
            column: 56
          },
          end: {
            line: 143,
            column: 5
          }
        },
        line: 118
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 147,
            column: 4
          },
          end: {
            line: 147,
            column: 5
          }
        },
        loc: {
          start: {
            line: 147,
            column: 47
          },
          end: {
            line: 161,
            column: 5
          }
        },
        line: 147
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 165,
            column: 4
          },
          end: {
            line: 165,
            column: 5
          }
        },
        loc: {
          start: {
            line: 165,
            column: 54
          },
          end: {
            line: 201,
            column: 5
          }
        },
        line: 165
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 205,
            column: 4
          },
          end: {
            line: 205,
            column: 5
          }
        },
        loc: {
          start: {
            line: 205,
            column: 67
          },
          end: {
            line: 218,
            column: 5
          }
        },
        line: 205
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 222,
            column: 4
          },
          end: {
            line: 222,
            column: 5
          }
        },
        loc: {
          start: {
            line: 222,
            column: 68
          },
          end: {
            line: 235,
            column: 5
          }
        },
        line: 222
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 239,
            column: 4
          },
          end: {
            line: 239,
            column: 5
          }
        },
        loc: {
          start: {
            line: 239,
            column: 90
          },
          end: {
            line: 294,
            column: 5
          }
        },
        line: 239
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 298,
            column: 4
          },
          end: {
            line: 298,
            column: 5
          }
        },
        loc: {
          start: {
            line: 298,
            column: 82
          },
          end: {
            line: 305,
            column: 5
          }
        },
        line: 298
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 309,
            column: 4
          },
          end: {
            line: 309,
            column: 5
          }
        },
        loc: {
          start: {
            line: 309,
            column: 76
          },
          end: {
            line: 361,
            column: 5
          }
        },
        line: 309
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 327,
            column: 46
          },
          end: {
            line: 327,
            column: 47
          }
        },
        loc: {
          start: {
            line: 327,
            column: 54
          },
          end: {
            line: 327,
            column: 77
          }
        },
        line: 327
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 329,
            column: 30
          },
          end: {
            line: 329,
            column: 31
          }
        },
        loc: {
          start: {
            line: 329,
            column: 38
          },
          end: {
            line: 332,
            column: 9
          }
        },
        line: 329
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 365,
            column: 4
          },
          end: {
            line: 365,
            column: 5
          }
        },
        loc: {
          start: {
            line: 365,
            column: 76
          },
          end: {
            line: 416,
            column: 5
          }
        },
        line: 365
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 380,
            column: 31
          },
          end: {
            line: 380,
            column: 32
          }
        },
        loc: {
          start: {
            line: 380,
            column: 44
          },
          end: {
            line: 402,
            column: 9
          }
        },
        line: 380
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 388,
            column: 41
          },
          end: {
            line: 388,
            column: 42
          }
        },
        loc: {
          start: {
            line: 388,
            column: 49
          },
          end: {
            line: 392,
            column: 17
          }
        },
        line: 388
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 420,
            column: 4
          },
          end: {
            line: 420,
            column: 5
          }
        },
        loc: {
          start: {
            line: 420,
            column: 66
          },
          end: {
            line: 432,
            column: 5
          }
        },
        line: 420
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 436,
            column: 4
          },
          end: {
            line: 436,
            column: 5
          }
        },
        loc: {
          start: {
            line: 436,
            column: 125
          },
          end: {
            line: 473,
            column: 5
          }
        },
        line: 436
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 477,
            column: 4
          },
          end: {
            line: 477,
            column: 5
          }
        },
        loc: {
          start: {
            line: 477,
            column: 61
          },
          end: {
            line: 484,
            column: 5
          }
        },
        line: 477
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 488,
            column: 4
          },
          end: {
            line: 488,
            column: 5
          }
        },
        loc: {
          start: {
            line: 488,
            column: 77
          },
          end: {
            line: 507,
            column: 5
          }
        },
        line: 488
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 511,
            column: 4
          },
          end: {
            line: 511,
            column: 5
          }
        },
        loc: {
          start: {
            line: 511,
            column: 74
          },
          end: {
            line: 517,
            column: 5
          }
        },
        line: 511
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 521,
            column: 4
          },
          end: {
            line: 521,
            column: 5
          }
        },
        loc: {
          start: {
            line: 521,
            column: 60
          },
          end: {
            line: 530,
            column: 5
          }
        },
        line: 521
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 534,
            column: 4
          },
          end: {
            line: 534,
            column: 5
          }
        },
        loc: {
          start: {
            line: 534,
            column: 68
          },
          end: {
            line: 538,
            column: 5
          }
        },
        line: 534
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 542,
            column: 4
          },
          end: {
            line: 542,
            column: 5
          }
        },
        loc: {
          start: {
            line: 542,
            column: 81
          },
          end: {
            line: 550,
            column: 5
          }
        },
        line: 542
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 554,
            column: 4
          },
          end: {
            line: 554,
            column: 5
          }
        },
        loc: {
          start: {
            line: 554,
            column: 82
          },
          end: {
            line: 596,
            column: 5
          }
        },
        line: 554
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 576,
            column: 51
          },
          end: {
            line: 576,
            column: 52
          }
        },
        loc: {
          start: {
            line: 576,
            column: 59
          },
          end: {
            line: 585,
            column: 9
          }
        },
        line: 576
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 593,
            column: 61
          },
          end: {
            line: 593,
            column: 62
          }
        },
        loc: {
          start: {
            line: 593,
            column: 79
          },
          end: {
            line: 593,
            column: 128
          }
        },
        line: 593
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 594,
            column: 49
          },
          end: {
            line: 594,
            column: 50
          }
        },
        loc: {
          start: {
            line: 594,
            column: 59
          },
          end: {
            line: 594,
            column: 84
          }
        },
        line: 594
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 600,
            column: 4
          },
          end: {
            line: 600,
            column: 5
          }
        },
        loc: {
          start: {
            line: 600,
            column: 99
          },
          end: {
            line: 672,
            column: 5
          }
        },
        line: 600
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 608,
            column: 58
          },
          end: {
            line: 608,
            column: 59
          }
        },
        loc: {
          start: {
            line: 608,
            column: 68
          },
          end: {
            line: 640,
            column: 9
          }
        },
        line: 608
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 609,
            column: 50
          },
          end: {
            line: 609,
            column: 51
          }
        },
        loc: {
          start: {
            line: 609,
            column: 63
          },
          end: {
            line: 632,
            column: 13
          }
        },
        line: 609
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 637,
            column: 53
          },
          end: {
            line: 637,
            column: 54
          }
        },
        loc: {
          start: {
            line: 637,
            column: 58
          },
          end: {
            line: 637,
            column: 86
          }
        },
        line: 637
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 638,
            column: 54
          },
          end: {
            line: 638,
            column: 55
          }
        },
        loc: {
          start: {
            line: 638,
            column: 59
          },
          end: {
            line: 638,
            column: 70
          }
        },
        line: 638
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 643,
            column: 18
          },
          end: {
            line: 643,
            column: 19
          }
        },
        loc: {
          start: {
            line: 643,
            column: 28
          },
          end: {
            line: 643,
            column: 57
          }
        },
        line: 643
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 644,
            column: 17
          },
          end: {
            line: 644,
            column: 18
          }
        },
        loc: {
          start: {
            line: 644,
            column: 36
          },
          end: {
            line: 661,
            column: 9
          }
        },
        line: 644
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 668,
            column: 71
          },
          end: {
            line: 668,
            column: 72
          }
        },
        loc: {
          start: {
            line: 668,
            column: 76
          },
          end: {
            line: 668,
            column: 111
          }
        },
        line: 668
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 668,
            column: 90
          },
          end: {
            line: 668,
            column: 91
          }
        },
        loc: {
          start: {
            line: 668,
            column: 95
          },
          end: {
            line: 668,
            column: 110
          }
        },
        line: 668
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 668,
            column: 176
          },
          end: {
            line: 668,
            column: 177
          }
        },
        loc: {
          start: {
            line: 668,
            column: 181
          },
          end: {
            line: 668,
            column: 216
          }
        },
        line: 668
      },
      "38": {
        name: "(anonymous_38)",
        decl: {
          start: {
            line: 668,
            column: 195
          },
          end: {
            line: 668,
            column: 196
          }
        },
        loc: {
          start: {
            line: 668,
            column: 200
          },
          end: {
            line: 668,
            column: 215
          }
        },
        line: 668
      },
      "39": {
        name: "(anonymous_39)",
        decl: {
          start: {
            line: 669,
            column: 73
          },
          end: {
            line: 669,
            column: 74
          }
        },
        loc: {
          start: {
            line: 669,
            column: 78
          },
          end: {
            line: 669,
            column: 97
          }
        },
        line: 669
      },
      "40": {
        name: "(anonymous_40)",
        decl: {
          start: {
            line: 676,
            column: 4
          },
          end: {
            line: 676,
            column: 5
          }
        },
        loc: {
          start: {
            line: 676,
            column: 104
          },
          end: {
            line: 739,
            column: 5
          }
        },
        line: 676
      },
      "41": {
        name: "(anonymous_41)",
        decl: {
          start: {
            line: 743,
            column: 4
          },
          end: {
            line: 743,
            column: 5
          }
        },
        loc: {
          start: {
            line: 743,
            column: 40
          },
          end: {
            line: 747,
            column: 5
          }
        },
        line: 743
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 72,
            column: 63
          },
          end: {
            line: 72,
            column: 119
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 72,
            column: 88
          },
          end: {
            line: 72,
            column: 101
          }
        }, {
          start: {
            line: 72,
            column: 104
          },
          end: {
            line: 72,
            column: 119
          }
        }],
        line: 72
      },
      "1": {
        loc: {
          start: {
            line: 80,
            column: 29
          },
          end: {
            line: 80,
            column: 60
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 80,
            column: 29
          },
          end: {
            line: 80,
            column: 54
          }
        }, {
          start: {
            line: 80,
            column: 58
          },
          end: {
            line: 80,
            column: 60
          }
        }],
        line: 80
      },
      "2": {
        loc: {
          start: {
            line: 81,
            column: 26
          },
          end: {
            line: 81,
            column: 56
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 81,
            column: 26
          },
          end: {
            line: 81,
            column: 48
          }
        }, {
          start: {
            line: 81,
            column: 52
          },
          end: {
            line: 81,
            column: 56
          }
        }],
        line: 81
      },
      "3": {
        loc: {
          start: {
            line: 82,
            column: 27
          },
          end: {
            line: 82,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 82,
            column: 27
          },
          end: {
            line: 82,
            column: 50
          }
        }, {
          start: {
            line: 82,
            column: 54
          },
          end: {
            line: 82,
            column: 59
          }
        }],
        line: 82
      },
      "4": {
        loc: {
          start: {
            line: 83,
            column: 34
          },
          end: {
            line: 83,
            column: 104
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 83,
            column: 34
          },
          end: {
            line: 83,
            column: 64
          }
        }, {
          start: {
            line: 83,
            column: 68
          },
          end: {
            line: 83,
            column: 104
          }
        }],
        line: 83
      },
      "5": {
        loc: {
          start: {
            line: 84,
            column: 22
          },
          end: {
            line: 84,
            column: 49
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 84,
            column: 22
          },
          end: {
            line: 84,
            column: 40
          }
        }, {
          start: {
            line: 84,
            column: 44
          },
          end: {
            line: 84,
            column: 49
          }
        }],
        line: 84
      },
      "6": {
        loc: {
          start: {
            line: 85,
            column: 28
          },
          end: {
            line: 85,
            column: 114
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 85,
            column: 28
          },
          end: {
            line: 85,
            column: 52
          }
        }, {
          start: {
            line: 85,
            column: 56
          },
          end: {
            line: 85,
            column: 114
          }
        }],
        line: 85
      },
      "7": {
        loc: {
          start: {
            line: 86,
            column: 30
          },
          end: {
            line: 86,
            column: 105
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 86,
            column: 30
          },
          end: {
            line: 86,
            column: 56
          }
        }, {
          start: {
            line: 86,
            column: 60
          },
          end: {
            line: 86,
            column: 105
          }
        }],
        line: 86
      },
      "8": {
        loc: {
          start: {
            line: 151,
            column: 8
          },
          end: {
            line: 154,
            column: 31
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 151,
            column: 8
          },
          end: {
            line: 154,
            column: 31
          }
        }, {
          start: {
            line: 153,
            column: 13
          },
          end: {
            line: 154,
            column: 31
          }
        }],
        line: 151
      },
      "9": {
        loc: {
          start: {
            line: 153,
            column: 13
          },
          end: {
            line: 154,
            column: 31
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 153,
            column: 13
          },
          end: {
            line: 154,
            column: 31
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 153
      },
      "10": {
        loc: {
          start: {
            line: 156,
            column: 8
          },
          end: {
            line: 159,
            column: 30
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 156,
            column: 8
          },
          end: {
            line: 159,
            column: 30
          }
        }, {
          start: {
            line: 158,
            column: 13
          },
          end: {
            line: 159,
            column: 30
          }
        }],
        line: 156
      },
      "11": {
        loc: {
          start: {
            line: 158,
            column: 13
          },
          end: {
            line: 159,
            column: 30
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 158,
            column: 13
          },
          end: {
            line: 159,
            column: 30
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 158
      },
      "12": {
        loc: {
          start: {
            line: 385,
            column: 12
          },
          end: {
            line: 401,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 385,
            column: 12
          },
          end: {
            line: 401,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 385
      },
      "13": {
        loc: {
          start: {
            line: 523,
            column: 8
          },
          end: {
            line: 524,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 523,
            column: 8
          },
          end: {
            line: 524,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 523
      },
      "14": {
        loc: {
          start: {
            line: 526,
            column: 8
          },
          end: {
            line: 527,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 526,
            column: 8
          },
          end: {
            line: 527,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 526
      },
      "15": {
        loc: {
          start: {
            line: 535,
            column: 8
          },
          end: {
            line: 536,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 535,
            column: 8
          },
          end: {
            line: 536,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 535
      },
      "16": {
        loc: {
          start: {
            line: 545,
            column: 15
          },
          end: {
            line: 545,
            column: 54
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 545,
            column: 15
          },
          end: {
            line: 545,
            column: 41
          }
        }, {
          start: {
            line: 545,
            column: 45
          },
          end: {
            line: 545,
            column: 54
          }
        }],
        line: 545
      },
      "17": {
        loc: {
          start: {
            line: 593,
            column: 79
          },
          end: {
            line: 593,
            column: 128
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 593,
            column: 115
          },
          end: {
            line: 593,
            column: 122
          }
        }, {
          start: {
            line: 593,
            column: 125
          },
          end: {
            line: 593,
            column: 128
          }
        }],
        line: 593
      },
      "18": {
        loc: {
          start: {
            line: 612,
            column: 16
          },
          end: {
            line: 624,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 612,
            column: 16
          },
          end: {
            line: 624,
            column: 17
          }
        }, {
          start: {
            line: 616,
            column: 21
          },
          end: {
            line: 624,
            column: 17
          }
        }],
        line: 612
      },
      "19": {
        loc: {
          start: {
            line: 616,
            column: 21
          },
          end: {
            line: 624,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 616,
            column: 21
          },
          end: {
            line: 624,
            column: 17
          }
        }, {
          start: {
            line: 619,
            column: 21
          },
          end: {
            line: 624,
            column: 17
          }
        }],
        line: 616
      },
      "20": {
        loc: {
          start: {
            line: 619,
            column: 21
          },
          end: {
            line: 624,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 619,
            column: 21
          },
          end: {
            line: 624,
            column: 17
          }
        }, {
          start: {
            line: 622,
            column: 21
          },
          end: {
            line: 624,
            column: 17
          }
        }],
        line: 619
      },
      "21": {
        loc: {
          start: {
            line: 622,
            column: 21
          },
          end: {
            line: 624,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 622,
            column: 21
          },
          end: {
            line: 624,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 622
      },
      "22": {
        loc: {
          start: {
            line: 646,
            column: 12
          },
          end: {
            line: 654,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 646,
            column: 12
          },
          end: {
            line: 654,
            column: 13
          }
        }, {
          start: {
            line: 649,
            column: 17
          },
          end: {
            line: 654,
            column: 13
          }
        }],
        line: 646
      },
      "23": {
        loc: {
          start: {
            line: 649,
            column: 17
          },
          end: {
            line: 654,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 649,
            column: 17
          },
          end: {
            line: 654,
            column: 13
          }
        }, {
          start: {
            line: 652,
            column: 17
          },
          end: {
            line: 654,
            column: 13
          }
        }],
        line: 649
      },
      "24": {
        loc: {
          start: {
            line: 679,
            column: 8
          },
          end: {
            line: 697,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 679,
            column: 8
          },
          end: {
            line: 697,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 679
      },
      "25": {
        loc: {
          start: {
            line: 699,
            column: 8
          },
          end: {
            line: 717,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 699,
            column: 8
          },
          end: {
            line: 717,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 699
      },
      "26": {
        loc: {
          start: {
            line: 720,
            column: 8
          },
          end: {
            line: 737,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 720,
            column: 8
          },
          end: {
            line: 737,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 720
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0,
      "210": 0,
      "211": 0,
      "212": 0,
      "213": 0,
      "214": 0,
      "215": 0,
      "216": 0,
      "217": 0,
      "218": 0,
      "219": 0,
      "220": 0,
      "221": 0,
      "222": 0,
      "223": 0,
      "224": 0,
      "225": 0,
      "226": 0,
      "227": 0,
      "228": 0,
      "229": 0,
      "230": 0,
      "231": 0,
      "232": 0,
      "233": 0,
      "234": 0,
      "235": 0,
      "236": 0,
      "237": 0,
      "238": 0,
      "239": 0,
      "240": 0,
      "241": 0,
      "242": 0,
      "243": 0,
      "244": 0,
      "245": 0,
      "246": 0,
      "247": 0,
      "248": 0,
      "249": 0,
      "250": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\LifecycleCostAnalysisEngine.ts",
      mappings: ";AAAA;;;;;;;;;GASG;;;AAEH,qEAiBqC;AAIrC;;;;;;;;;;;GAWG;AACH,MAAa,2BAA2B;IAuBtC;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,qBAAqB,CACvC,mBAAwC,EACxC,cAA8B,EAC9B,kBAAoD;QAEpD,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC;YACnE,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;YAE7B,kCAAkC;YAClC,MAAM,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,CAAC;YAEjE,0BAA0B;YAC1B,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,mBAAmB,CAAC,CAAC;YAE3E,4BAA4B;YAC5B,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,uBAAuB,CACvD,mBAAmB,EACnB,cAAc,EACd,UAAU,CACX,CAAC;YAEF,8BAA8B;YAC9B,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAC3D,mBAAmB,EACnB,UAAU,CACX,CAAC;YAEF,8BAA8B;YAC9B,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAC3D,mBAAmB,EACnB,UAAU,CACX,CAAC;YAEF,oCAAoC;YACpC,MAAM,oBAAoB,GAAG,MAAM,IAAI,CAAC,6BAA6B,CACnE,YAAY,EACZ,cAAc,EACd,gBAAgB,EAChB,gBAAgB,EAChB,UAAU,CACX,CAAC;YAEF,0BAA0B;YAC1B,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,qBAAqB,CACrD,mBAAmB,EACnB,oBAAoB,CACrB,CAAC;YAEF,+BAA+B;YAC/B,MAAM,mBAAmB,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAC/D,mBAAmB,EACnB,UAAU,EACV,oBAAoB,CACrB,CAAC;YAEF,gCAAgC;YAChC,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAC5D,oBAAoB,EACpB,mBAAmB,EACnB,cAAc,CACf,CAAC;YAEF,MAAM,QAAQ,GAA0B;gBACtC,EAAE,EAAE,UAAU;gBACd,QAAQ,EAAE,mBAAmB,CAAC,EAAE;gBAChC,iBAAiB,EAAE,SAAS;gBAC5B,kBAAkB,EAAE,UAAU;gBAC9B,YAAY;gBACZ,cAAc;gBACd,gBAAgB;gBAChB,gBAAgB;gBAChB,oBAAoB;gBACpB,cAAc;gBACd,mBAAmB;gBACnB,eAAe;aAChB,CAAC;YAEF,qBAAqB;YACrB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YAE1C,OAAO,QAAQ,CAAC;QAElB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,mCAAmC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;QACjH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,oBAAoB,CACjC,QAA0C;QAE1C,OAAO;YACL,eAAe,EAAE,QAAQ,EAAE,eAAe,IAAI,EAAE,EAAE,WAAW;YAC7D,YAAY,EAAE,QAAQ,EAAE,YAAY,IAAI,IAAI,EAAE,mBAAmB;YACjE,aAAa,EAAE,QAAQ,EAAE,aAAa,IAAI,KAAK,EAAE,iBAAiB;YAClE,oBAAoB,EAAE,QAAQ,EAAE,oBAAoB,IAAI,IAAI,CAAC,wBAAwB,CAAC,MAAM;YAC5F,QAAQ,EAAE,QAAQ,EAAE,QAAQ,IAAI,KAAK;YACrC,cAAc,EAAE,QAAQ,EAAE,cAAc,IAAI,wCAAkB,CAAC,iBAAiB;YAChF,gBAAgB,EAAE,QAAQ,EAAE,gBAAgB,IAAI,sCAAgB,CAAC,MAAM;SACxE,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,KAAK,CAAC,qBAAqB,CACxC,mBAAwC;QAExC,MAAM,aAAa,GAAG,mBAAmB,CAAC,gBAAgB,CAAC,aAAa,CAAC;QACzE,MAAM,UAAU,GAAG,mBAAmB,CAAC,UAAU,CAAC;QAElD,gDAAgD;QAChD,MAAM,cAAc,GAAG,IAAI,CAAC,uBAAuB,CAAC,mBAAmB,CAAC,CAAC;QAEzE,2DAA2D;QAC3D,MAAM,iBAAiB,GAAG,IAAI,CAAC,0BAA0B,CAAC,cAAc,CAAC,CAAC;QAE1E,uDAAuD;QACvD,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,cAAc,EAAE,iBAAiB,CAAC,CAAC;QAEjF,0DAA0D;QAC1D,MAAM,YAAY,GAAG,IAAI,CAAC,qBAAqB,CAAC,cAAc,EAAE,iBAAiB,CAAC,CAAC;QAEnF,MAAM,gBAAgB,GAAG,cAAc,CAAC,KAAK,GAAG,iBAAiB,CAAC,KAAK;YAChD,WAAW,CAAC,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC;QAE9D,OAAO;YACL,cAAc;YACd,iBAAiB;YACjB,WAAW;YACX,YAAY;YACZ,gBAAgB;YAChB,UAAU,EAAE,gBAAgB,GAAG,aAAa;YAC5C,iBAAiB,EAAE,gBAAgB,GAAG,KAAK,CAAC,wBAAwB;SACrE,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,uBAAuB,CAAC,mBAAwC;QAC7E,MAAM,aAAa,GAAG,mBAAmB,CAAC,gBAAgB,CAAC,aAAa,CAAC;QACzE,MAAM,cAAc,GAAG,mBAAmB,CAAC,gBAAgB,CAAC,cAAc,CAAC;QAE3E,4CAA4C;QAC5C,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC;QAC3E,MAAM,kBAAkB,GAAG,GAAG,CAAC,CAAC,qBAAqB;QACrD,MAAM,kBAAkB,GAAG,GAAG,CAAC,CAAC,qBAAqB;QACrD,MAAM,iBAAiB,GAAG,GAAG,CAAC,CAAC,oBAAoB;QACnD,MAAM,kBAAkB,GAAG,GAAG,CAAC,CAAC,qBAAqB;QACrD,MAAM,qBAAqB,GAAG,GAAG,CAAC,CAAC,wBAAwB;QAE3D,MAAM,IAAI,GAAG,aAAa,GAAG,aAAa,CAAC;QAC3C,MAAM,QAAQ,GAAG,aAAa,GAAG,kBAAkB,CAAC;QACpD,MAAM,QAAQ,GAAG,aAAa,GAAG,kBAAkB,CAAC;QACpD,MAAM,OAAO,GAAG,aAAa,GAAG,iBAAiB,CAAC;QAClD,MAAM,QAAQ,GAAG,aAAa,GAAG,kBAAkB,CAAC;QACpD,MAAM,WAAW,GAAG,aAAa,GAAG,qBAAqB,CAAC;QAE1D,OAAO;YACL,IAAI;YACJ,QAAQ;YACR,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,WAAW;YACX,KAAK,EAAE,IAAI,GAAG,QAAQ,GAAG,QAAQ,GAAG,OAAO,GAAG,QAAQ,GAAG,WAAW;SACrE,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,gBAAgB,CAAC,OAAe,EAAE,QAAgB;QAC/D,mBAAmB;QACnB,IAAI,UAAU,GAAG,IAAI,CAAC,CAAC,aAAa;QAEpC,mBAAmB;QACnB,IAAI,OAAO,GAAG,KAAK;YAAE,UAAU,IAAI,IAAI,CAAC,CAAC,qBAAqB;aACzD,IAAI,OAAO,GAAG,IAAI;YAAE,UAAU,IAAI,IAAI,CAAC,CAAC,uBAAuB;QAEpE,uBAAuB;QACvB,IAAI,QAAQ,GAAG,GAAG;YAAE,UAAU,IAAI,GAAG,CAAC,CAAC,wBAAwB;aAC1D,IAAI,QAAQ,GAAG,GAAG;YAAE,UAAU,IAAI,GAAG,CAAC,CAAC,wBAAwB;QAEpE,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,0BAA0B,CAAC,cAA8B;QACtE,8DAA8D;QAC9D,MAAM,OAAO,GAAG;YACd,IAAI,EAAE,GAAG,EAAE,kBAAkB;YAC7B,QAAQ,EAAE,GAAG,EAAE,uBAAuB;YACtC,QAAQ,EAAE,GAAG,EAAE,uBAAuB;YACtC,OAAO,EAAE,GAAG,EAAE,sBAAsB;YACpC,QAAQ,EAAE,GAAG,EAAE,8CAA8C;YAC7D,WAAW,EAAE,GAAG,CAAC,0BAA0B;SAC5C,CAAC;QAEF,MAAM,IAAI,GAAG,cAAc,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QAChD,MAAM,QAAQ,GAAG,cAAc,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QAC5D,MAAM,QAAQ,GAAG,cAAc,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QAC5D,MAAM,OAAO,GAAG,cAAc,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QACzD,MAAM,QAAQ,GAAG,cAAc,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QAC5D,MAAM,WAAW,GAAG,cAAc,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QAErE,gCAAgC;QAChC,MAAM,UAAU,GAAG,CAAC,IAAI,GAAG,QAAQ,GAAG,QAAQ,GAAG,OAAO,GAAG,QAAQ,GAAG,WAAW,CAAC,GAAG,GAAG,CAAC;QACzF,MAAM,cAAc,GAAG,cAAc,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC,iCAAiC;QACrF,MAAM,eAAe,GAAG,cAAc,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC,0BAA0B;QAC/E,MAAM,OAAO,GAAG,cAAc,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC,mCAAmC;QAEhF,MAAM,KAAK,GAAG,IAAI,GAAG,QAAQ,GAAG,QAAQ,GAAG,OAAO,GAAG,QAAQ,GAAG,WAAW;YAC9D,UAAU,GAAG,cAAc,GAAG,eAAe,GAAG,OAAO,CAAC;QAErE,OAAO;YACL,IAAI;YACJ,QAAQ;YACR,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,WAAW;YACX,UAAU;YACV,cAAc;YACd,eAAe;YACf,OAAO;YACP,KAAK;SACN,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,oBAAoB,CACjC,cAA8B,EAC9B,iBAAoC;QAEpC,MAAM,WAAW,GAAG,cAAc,CAAC,KAAK,GAAG,iBAAiB,CAAC,KAAK,CAAC;QAEnE,MAAM,iBAAiB,GAAG,WAAW,GAAG,IAAI,CAAC,CAAC,4BAA4B;QAC1E,MAAM,QAAQ,GAAG,WAAW,GAAG,IAAI,CAAC,CAAC,qCAAqC;QAC1E,MAAM,YAAY,GAAG,WAAW,GAAG,KAAK,CAAC,CAAC,wBAAwB;QAClE,MAAM,iBAAiB,GAAG,WAAW,GAAG,KAAK,CAAC,CAAC,8BAA8B;QAE7E,OAAO;YACL,iBAAiB;YACjB,QAAQ;YACR,YAAY;YACZ,iBAAiB;YACjB,KAAK,EAAE,iBAAiB,GAAG,QAAQ,GAAG,YAAY,GAAG,iBAAiB;SACvE,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,qBAAqB,CAClC,cAA8B,EAC9B,iBAAoC;QAEpC,MAAM,WAAW,GAAG,cAAc,CAAC,KAAK,GAAG,iBAAiB,CAAC,KAAK,CAAC;QAEnE,MAAM,eAAe,GAAG,WAAW,GAAG,KAAK,CAAC,CAAC,4BAA4B;QACzE,MAAM,cAAc,GAAG,WAAW,GAAG,KAAK,CAAC,CAAC,2BAA2B;QACvE,MAAM,kBAAkB,GAAG,IAAI,CAAC,CAAC,qCAAqC;QACtE,MAAM,iBAAiB,GAAG,WAAW,GAAG,KAAK,CAAC,CAAC,8BAA8B;QAE7E,OAAO;YACL,eAAe;YACf,cAAc;YACd,kBAAkB;YAClB,iBAAiB;YACjB,KAAK,EAAE,eAAe,GAAG,cAAc,GAAG,kBAAkB,GAAG,iBAAiB;SACjF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,KAAK,CAAC,uBAAuB,CAC1C,mBAAwC,EACxC,cAA8B,EAC9B,UAAkC;QAElC,MAAM,eAAe,GAAG,UAAU,CAAC,eAAe,CAAC;QACnD,MAAM,oBAAoB,GAAG,UAAU,CAAC,oBAAoB,CAAC;QAC7D,MAAM,YAAY,GAAG,UAAU,CAAC,YAAY,CAAC;QAE7C,sBAAsB;QACtB,MAAM,iBAAiB,GAAG,cAAc,CAAC,WAAW,CAAC,YAAY,CAAC,SAAS,CAAC;QAE5E,gEAAgE;QAChE,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,MAAM,iBAAiB,GAAa,EAAE,CAAC;QAEvC,KAAK,IAAI,IAAI,GAAG,CAAC,EAAE,IAAI,IAAI,eAAe,EAAE,IAAI,EAAE,EAAE,CAAC;YACnD,MAAM,gBAAgB,GAAG,iBAAiB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,oBAAoB,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC;YAC1F,MAAM,YAAY,GAAG,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY,EAAE,IAAI,CAAC,CAAC;YACzE,aAAa,IAAI,YAAY,CAAC;YAC9B,iBAAiB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC3C,CAAC;QAED,wBAAwB;QACxB,MAAM,eAAe,GAAG,iBAAiB,GAAG,IAAI,CAAC,CAAC,qBAAqB;QACvE,MAAM,eAAe,GAAG,IAAI,CAAC,CAAC,yBAAyB;QACvD,MAAM,gBAAgB,GAAG,GAAG,CAAC,CAAC,wCAAwC;QAEtE,qDAAqD;QACrD,MAAM,WAAW,GAAG,IAAI,CAAC,qBAAqB,CAAC,eAAe,EAAE,UAAU,CAAC,aAAa,EAAE,YAAY,EAAE,eAAe,CAAC,CAAC;QACzH,MAAM,WAAW,GAAG,IAAI,CAAC,qBAAqB,CAAC,eAAe,EAAE,UAAU,CAAC,aAAa,EAAE,YAAY,EAAE,eAAe,CAAC,CAAC;QACzH,MAAM,YAAY,GAAG,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,EAAE,UAAU,CAAC,aAAa,EAAE,YAAY,EAAE,eAAe,CAAC,CAAC;QAE3H,MAAM,gBAAgB,GAAG,aAAa,GAAG,WAAW,GAAG,WAAW,GAAG,YAAY,CAAC;QAElF,OAAO;YACL,WAAW,EAAE;gBACX,MAAM,EAAE,iBAAiB;gBACzB,YAAY,EAAE,aAAa;gBAC3B,gBAAgB,EAAE,iBAAiB;gBACnC,cAAc,EAAE,oBAAoB;aACrC;YACD,gBAAgB,EAAE;gBAChB,MAAM,EAAE,CAAC,EAAE,wBAAwB;gBACnC,YAAY,EAAE,CAAC;gBACf,gBAAgB,EAAE,EAAE;gBACpB,cAAc,EAAE,IAAI,CAAC,wBAAwB,CAAC,WAAW;aAC1D;YACD,cAAc,EAAE;gBACd,MAAM,EAAE,eAAe;gBACvB,YAAY,EAAE,WAAW;gBACzB,cAAc,EAAE,UAAU,CAAC,aAAa;aACzC;YACD,YAAY,EAAE;gBACZ,MAAM,EAAE,eAAe;gBACvB,YAAY,EAAE,WAAW;gBACzB,cAAc,EAAE,UAAU,CAAC,aAAa;aACzC;YACD,eAAe,EAAE;gBACf,MAAM,EAAE,gBAAgB;gBACxB,YAAY,EAAE,YAAY;gBAC1B,cAAc,EAAE,UAAU,CAAC,aAAa;aACzC;YACD,WAAW,EAAE,iBAAiB,GAAG,eAAe,GAAG,eAAe,GAAG,gBAAgB;YACrF,iBAAiB,EAAE,gBAAgB;SACpC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,qBAAqB,CAClC,UAAkB,EAClB,cAAsB,EACtB,YAAoB,EACpB,KAAa;QAEb,IAAI,YAAY,GAAG,CAAC,CAAC;QAErB,KAAK,IAAI,IAAI,GAAG,CAAC,EAAE,IAAI,IAAI,KAAK,EAAE,IAAI,EAAE,EAAE,CAAC;YACzC,MAAM,WAAW,GAAG,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,cAAc,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC;YACxE,YAAY,IAAI,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY,EAAE,IAAI,CAAC,CAAC;QACjE,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,KAAK,CAAC,yBAAyB,CAC5C,mBAAwC,EACxC,UAAkC;QAElC,MAAM,aAAa,GAAG,mBAAmB,CAAC,gBAAgB,CAAC,aAAa,CAAC;QACzE,MAAM,eAAe,GAAG,UAAU,CAAC,eAAe,CAAC;QACnD,MAAM,YAAY,GAAG,UAAU,CAAC,YAAY,CAAC;QAC7C,MAAM,yBAAyB,GAAG,IAAI,CAAC,wBAAwB,CAAC,WAAW,CAAC;QAE5E,wCAAwC;QACxC,MAAM,2BAA2B,GAAG,GAAG,CAAC,CAAC,aAAa;QACtD,MAAM,2BAA2B,GAAG,GAAG,CAAC,CAAC,aAAa;QACtD,MAAM,uBAAuB,GAAG,GAAG,CAAC,CAAC,aAAa;QAElD,MAAM,gBAAgB,GAAG,aAAa,GAAG,2BAA2B,CAAC;QACrE,MAAM,gBAAgB,GAAG,aAAa,GAAG,2BAA2B,CAAC;QACrE,MAAM,aAAa,GAAG,aAAa,GAAG,uBAAuB,CAAC;QAE9D,2BAA2B;QAC3B,MAAM,YAAY,GAAG,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,EAAE,yBAAyB,EAAE,YAAY,EAAE,eAAe,CAAC,CAAC;QAC5H,MAAM,YAAY,GAAG,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,EAAE,yBAAyB,EAAE,YAAY,EAAE,eAAe,CAAC,CAAC;QAC5H,MAAM,SAAS,GAAG,IAAI,CAAC,qBAAqB,CAAC,aAAa,EAAE,yBAAyB,EAAE,YAAY,EAAE,eAAe,CAAC,CAAC;QAEtH,mCAAmC;QACnC,MAAM,iBAAiB,GAAG,aAAa,GAAG,GAAG,CAAC,CAAC,SAAS;QACxD,MAAM,aAAa,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,IAAI,eAAe,CAAC,CAAC;QACvE,IAAI,WAAW,GAAG,CAAC,CAAC;QAEpB,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YAC3B,MAAM,oBAAoB,GAAG,iBAAiB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,yBAAyB,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC;YACnG,WAAW,IAAI,oBAAoB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY,EAAE,IAAI,CAAC,CAAC;QACzE,CAAC,CAAC,CAAC;QAEH,MAAM,WAAW,GAAG,gBAAgB,GAAG,gBAAgB,GAAG,aAAa,CAAC;QACxE,MAAM,iBAAiB,GAAG,YAAY,GAAG,YAAY,GAAG,SAAS,GAAG,WAAW,CAAC;QAEhF,OAAO;YACL,qBAAqB,EAAE;gBACrB,MAAM,EAAE,gBAAgB;gBACxB,YAAY,EAAE,YAAY;gBAC1B,WAAW,EAAE,gDAAgD;aAC9D;YACD,qBAAqB,EAAE;gBACrB,MAAM,EAAE,gBAAgB;gBACxB,YAAY,EAAE,YAAY;gBAC1B,WAAW,EAAE,oCAAoC;aAClD;YACD,iBAAiB,EAAE;gBACjB,MAAM,EAAE,aAAa;gBACrB,YAAY,EAAE,SAAS;gBACvB,WAAW,EAAE,6BAA6B;aAC3C;YACD,cAAc,EAAE;gBACd,IAAI,EAAE,iBAAiB;gBACvB,YAAY,EAAE,WAAW;gBACzB,QAAQ,EAAE,aAAa;gBACvB,WAAW,EAAE,qCAAqC;aACnD;YACD,WAAW;YACX,iBAAiB;YACjB,cAAc,EAAE,yBAAyB;SAC1C,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,KAAK,CAAC,yBAAyB,CAC5C,mBAAwC,EACxC,UAAkC;QAElC,MAAM,eAAe,GAAG,UAAU,CAAC,eAAe,CAAC;QACnD,MAAM,YAAY,GAAG,UAAU,CAAC,YAAY,CAAC;QAC7C,MAAM,sBAAsB,GAAG,IAAI,CAAC,wBAAwB,CAAC,SAAS,CAAC;QAEvE,MAAM,qBAAqB,GAA2B,EAAE,CAAC;QACzD,IAAI,kBAAkB,GAAG,CAAC,CAAC;QAE3B,sDAAsD;QACtD,MAAM,cAAc,GAAG;YACrB,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE;YAClE,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,UAAU,EAAE,GAAG,EAAE;YACrE,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,UAAU,EAAE,GAAG,EAAE;YAC/D,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,UAAU,EAAE,GAAG,EAAE;YACvE,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,UAAU,EAAE,GAAG,EAAE;SAC1E,CAAC;QAEF,MAAM,aAAa,GAAG,mBAAmB,CAAC,gBAAgB,CAAC,aAAa,CAAC;QAEzE,cAAc,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;YACjC,MAAM,gBAAgB,GAAG,EAAE,CAAC;YAC5B,KAAK,IAAI,IAAI,GAAG,SAAS,CAAC,IAAI,EAAE,IAAI,IAAI,eAAe,EAAE,IAAI,IAAI,SAAS,CAAC,IAAI,EAAE,CAAC;gBAChF,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9B,CAAC;YAED,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChC,MAAM,eAAe,GAAG,aAAa,GAAG,SAAS,CAAC,UAAU,CAAC;gBAC7D,IAAI,WAAW,GAAG,CAAC,CAAC;gBAEpB,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;oBAC9B,MAAM,YAAY,GAAG,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,sBAAsB,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC;oBACtF,MAAM,YAAY,GAAG,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY,EAAE,IAAI,CAAC,CAAC;oBACrE,WAAW,IAAI,YAAY,CAAC;gBAC9B,CAAC,CAAC,CAAC;gBAEH,qBAAqB,CAAC,IAAI,CAAC;oBACzB,aAAa,EAAE,SAAS,CAAC,IAAI;oBAC7B,gBAAgB;oBAChB,QAAQ,EAAE,eAAe;oBACzB,iBAAiB,EAAE,WAAW;oBAC9B,WAAW,EAAE,GAAG,SAAS,CAAC,IAAI,yBAAyB,SAAS,CAAC,IAAI,uBAAuB;iBAC7F,CAAC,CAAC;gBAEH,kBAAkB,IAAI,WAAW,CAAC;YACpC,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,0CAA0C;QAC1C,MAAM,YAAY,GAAG,IAAI,CAAC,qBAAqB,CAAC,mBAAmB,EAAE,UAAU,CAAC,CAAC;QACjF,MAAM,SAAS,GAAG,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY,EAAE,eAAe,CAAC,CAAC;QAE7E,OAAO;YACL,qBAAqB;YACrB,YAAY,EAAE;gBACZ,KAAK,EAAE,YAAY;gBACnB,YAAY,EAAE,SAAS;gBACvB,WAAW,EAAE,mDAAmD;aACjE;YACD,oBAAoB,EAAE,kBAAkB;YACxC,kBAAkB,EAAE,kBAAkB,GAAG,SAAS;SACnD,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,qBAAqB,CAClC,mBAAwC,EACxC,UAAkC;QAElC,MAAM,aAAa,GAAG,mBAAmB,CAAC,gBAAgB,CAAC,aAAa,CAAC;QACzE,MAAM,eAAe,GAAG,UAAU,CAAC,eAAe,CAAC;QAEnD,mDAAmD;QACnD,MAAM,qBAAqB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,QAAQ,GAAG,eAAe,CAAC,CAAC;QAC1F,MAAM,iBAAiB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,GAAG,CAAC,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;QAC/G,MAAM,oBAAoB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG,CAAC,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC;QAExH,yDAAyD;QACzD,MAAM,eAAe,GAAG,CAAC,aAAa,GAAG,GAAG,CAAC,GAAG,CAAC,qBAAqB,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QACvG,MAAM,WAAW,GAAG,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,iBAAiB,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAC5F,MAAM,cAAc,GAAG,CAAC,aAAa,GAAG,GAAG,CAAC,GAAG,CAAC,oBAAoB,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QAEpG,OAAO,eAAe,GAAG,WAAW,GAAG,cAAc,CAAC;IACxD,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,KAAK,CAAC,6BAA6B,CAChD,YAA0B,EAC1B,cAA8B,EAC9B,gBAAkC,EAClC,gBAAkC,EAClC,UAAkC;QAElC,MAAM,iBAAiB,GAAG,YAAY,CAAC,gBAAgB;YAC9B,cAAc,CAAC,iBAAiB;YAChC,gBAAgB,CAAC,iBAAiB;YAClC,gBAAgB,CAAC,kBAAkB,CAAC;QAE7D,MAAM,mBAAmB,GAAG,IAAI,CAAC,uBAAuB,CAAC,iBAAiB,EAAE,UAAU,CAAC,CAAC;QAExF,6BAA6B;QAC7B,MAAM,aAAa,GAAG;YACpB,YAAY,EAAE;gBACZ,MAAM,EAAE,YAAY,CAAC,gBAAgB;gBACrC,UAAU,EAAE,CAAC,YAAY,CAAC,gBAAgB,GAAG,iBAAiB,CAAC,GAAG,GAAG;aACtE;YACD,cAAc,EAAE;gBACd,MAAM,EAAE,cAAc,CAAC,iBAAiB;gBACxC,UAAU,EAAE,CAAC,cAAc,CAAC,iBAAiB,GAAG,iBAAiB,CAAC,GAAG,GAAG;aACzE;YACD,gBAAgB,EAAE;gBAChB,MAAM,EAAE,gBAAgB,CAAC,iBAAiB;gBAC1C,UAAU,EAAE,CAAC,gBAAgB,CAAC,iBAAiB,GAAG,iBAAiB,CAAC,GAAG,GAAG;aAC3E;YACD,gBAAgB,EAAE;gBAChB,MAAM,EAAE,gBAAgB,CAAC,kBAAkB;gBAC3C,UAAU,EAAE,CAAC,gBAAgB,CAAC,kBAAkB,GAAG,iBAAiB,CAAC,GAAG,GAAG;aAC5E;SACF,CAAC;QAEF,oBAAoB;QACpB,MAAM,gBAAgB,GAAG,IAAI,CAAC,yBAAyB,CACrD,YAAY,CAAC,gBAAgB,EAC7B,cAAc,CAAC,WAAW,CAAC,MAAM,GAAG,gBAAgB,CAAC,WAAW,EAChE,UAAU,CACX,CAAC;QAEF,OAAO;YACL,iBAAiB;YACjB,mBAAmB;YACnB,aAAa;YACb,gBAAgB;YAChB,kBAAkB,EAAE,UAAU;YAC9B,UAAU,EAAE,iBAAiB,GAAG,KAAK,EAAE,6BAA6B;YACpE,iBAAiB,EAAE,iBAAiB,GAAG,KAAK,EAAE,iCAAiC;YAC/E,aAAa,EAAE,IAAI,CAAC,4BAA4B,CAAC,YAAY,CAAC,gBAAgB,EAAE,cAAc,CAAC,WAAW,CAAC,MAAM,CAAC;SACnH,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,uBAAuB,CACpC,YAAoB,EACpB,UAAkC;QAElC,MAAM,YAAY,GAAG,UAAU,CAAC,YAAY,CAAC;QAC7C,MAAM,eAAe,GAAG,UAAU,CAAC,eAAe,CAAC;QAEnD,0BAA0B;QAC1B,MAAM,GAAG,GAAG,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY,EAAE,eAAe,CAAC,CAAC;YAC5D,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY,EAAE,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC;QAE9D,OAAO,YAAY,GAAG,GAAG,CAAC;IAC5B,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,yBAAyB,CACtC,WAAmB,EACnB,aAAqB,EACrB,UAAkC;QAElC,MAAM,YAAY,GAAG,UAAU,CAAC,YAAY,CAAC;QAC7C,MAAM,eAAe,GAAG,UAAU,CAAC,eAAe,CAAC;QAEnD,4DAA4D;QAC5D,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,aAAa,EAAE,YAAY,EAAE,eAAe,CAAC,CAAC;QAEzF,mDAAmD;QACnD,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,aAAa,EAAE,eAAe,CAAC,CAAC;QAE3E,4BAA4B;QAC5B,MAAM,iBAAiB,GAAG,IAAI,CAAC,0BAA0B,CAAC,WAAW,EAAE,aAAa,EAAE,YAAY,CAAC,CAAC;QAEpG,sBAAsB;QACtB,MAAM,kBAAkB,GAAG,CAAC,GAAG,GAAG,WAAW,CAAC,GAAG,WAAW,CAAC;QAE7D,OAAO;YACL,eAAe,EAAE,GAAG;YACpB,oBAAoB,EAAE,GAAG;YACzB,aAAa,EAAE,IAAI,CAAC,4BAA4B,CAAC,WAAW,EAAE,aAAa,CAAC;YAC5E,uBAAuB,EAAE,iBAAiB;YAC1C,kBAAkB;YAClB,kBAAkB,EAAE,CAAC,aAAa,GAAG,eAAe,GAAG,WAAW,CAAC,GAAG,WAAW,GAAG,GAAG;SACxF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,YAAY,CACzB,WAAmB,EACnB,cAAsB,EACtB,YAAoB,EACpB,KAAa;QAEb,IAAI,GAAG,GAAG,CAAC,WAAW,CAAC;QAEvB,KAAK,IAAI,IAAI,GAAG,CAAC,EAAE,IAAI,IAAI,KAAK,EAAE,IAAI,EAAE,EAAE,CAAC;YACzC,GAAG,IAAI,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY,EAAE,IAAI,CAAC,CAAC;QAC3D,CAAC;QAED,OAAO,GAAG,CAAC;IACb,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,YAAY,CACzB,WAAmB,EACnB,cAAsB,EACtB,KAAa;QAEb,iDAAiD;QACjD,IAAI,cAAc,IAAI,CAAC;YAAE,OAAO,CAAC,CAAC;QAElC,MAAM,aAAa,GAAG,cAAc,GAAG,KAAK,CAAC;QAC7C,IAAI,aAAa,IAAI,WAAW;YAAE,OAAO,CAAC,CAAC;QAE3C,sEAAsE;QACtE,OAAO,IAAI,CAAC,GAAG,CAAC,aAAa,GAAG,WAAW,EAAE,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;IAC9D,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,4BAA4B,CACzC,WAAmB,EACnB,aAAqB;QAErB,IAAI,aAAa,IAAI,CAAC;YAAE,OAAO,QAAQ,CAAC;QACxC,OAAO,WAAW,GAAG,aAAa,CAAC;IACrC,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,0BAA0B,CACvC,WAAmB,EACnB,cAAsB,EACtB,YAAoB;QAEpB,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,IAAI,IAAI,GAAG,CAAC,CAAC;QAEb,OAAO,YAAY,GAAG,WAAW,IAAI,IAAI,GAAG,EAAE,EAAE,CAAC,CAAC,eAAe;YAC/D,IAAI,EAAE,CAAC;YACP,YAAY,IAAI,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY,EAAE,IAAI,CAAC,CAAC;QACpE,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,KAAK,CAAC,qBAAqB,CACxC,mBAAwC,EACxC,oBAA0C;QAE1C,uDAAuD;QACvD,MAAM,YAAY,GAAG;YACnB;gBACE,IAAI,EAAE,wBAAwB;gBAC9B,qBAAqB,EAAE,IAAI;gBAC3B,uBAAuB,EAAE,GAAG;gBAC5B,WAAW,EAAE,qDAAqD;aACnE;YACD;gBACE,IAAI,EAAE,iBAAiB;gBACvB,qBAAqB,EAAE,GAAG;gBAC1B,uBAAuB,EAAE,GAAG;gBAC5B,WAAW,EAAE,0CAA0C;aACxD;YACD;gBACE,IAAI,EAAE,eAAe;gBACrB,qBAAqB,EAAE,GAAG;gBAC1B,uBAAuB,EAAE,GAAG;gBAC5B,WAAW,EAAE,kDAAkD;aAChE;SACF,CAAC;QAEF,MAAM,iBAAiB,GAAG,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YACjD,eAAe,EAAE,GAAG,CAAC,IAAI;YACzB,WAAW,EAAE,GAAG,CAAC,WAAW;YAC5B,SAAS,EAAE,oBAAoB,CAAC,iBAAiB;gBACtC,CAAC,CAAC,GAAG,CAAC,qBAAqB,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,uBAAuB,GAAG,GAAG,CAAC,CAAC;YACpF,cAAc,EAAE,CAAC,oBAAoB,CAAC,iBAAiB;gBACvC,CAAC,CAAC,GAAG,CAAC,qBAAqB,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,uBAAuB,GAAG,GAAG,CAAC,CAAC,CAAC;gBAC3E,oBAAoB,CAAC,iBAAiB;YACrD,oBAAoB,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,qBAAqB,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,uBAAuB,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG;SAC5G,CAAC,CAAC,CAAC;QAEJ,OAAO;YACL,cAAc,EAAE;gBACd,IAAI,EAAE,8BAA8B;gBACpC,SAAS,EAAE,oBAAoB,CAAC,iBAAiB;gBACjD,WAAW,EAAE,gCAAgC;aAC9C;YACD,YAAY,EAAE,iBAAiB;YAC/B,sBAAsB,EAAE,iBAAiB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE,CAChE,OAAO,CAAC,SAAS,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAClD;YACD,YAAY,EAAE,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,CAAC;SAC1E,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,KAAK,CAAC,0BAA0B,CAC7C,mBAAwC,EACxC,UAAkC,EAClC,oBAA0C;QAE1C,MAAM,YAAY,GAAG,oBAAoB,CAAC,iBAAiB,CAAC;QAC5D,MAAM,kBAAkB,GAAG;YACzB,EAAE,SAAS,EAAE,cAAc,EAAE,SAAS,EAAE,UAAU,CAAC,YAAY,EAAE,UAAU,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE;YACzG,EAAE,SAAS,EAAE,sBAAsB,EAAE,SAAS,EAAE,UAAU,CAAC,oBAAoB,EAAE,UAAU,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE;YAC3H,EAAE,SAAS,EAAE,iBAAiB,EAAE,SAAS,EAAE,UAAU,CAAC,eAAe,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;YACnG,EAAE,SAAS,EAAE,aAAa,EAAE,SAAS,EAAE,GAAG,EAAE,UAAU,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE;SACjF,CAAC;QAEF,MAAM,kBAAkB,GAAG,kBAAkB,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;YACzD,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;gBAChD,IAAI,YAAY,GAAG,YAAY,CAAC;gBAEhC,qCAAqC;gBACrC,IAAI,MAAM,CAAC,SAAS,KAAK,cAAc,EAAE,CAAC;oBACxC,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,GAAG,SAAS,CAAC;oBAC7C,YAAY,GAAG,YAAY,GAAG,CAAC,UAAU,CAAC,YAAY,GAAG,OAAO,CAAC,CAAC;gBACpE,CAAC;qBAAM,IAAI,MAAM,CAAC,SAAS,KAAK,sBAAsB,EAAE,CAAC;oBACvD,YAAY,GAAG,YAAY,GAAG,CAAC,CAAC,GAAG,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC,oBAAoB;gBACzE,CAAC;qBAAM,IAAI,MAAM,CAAC,SAAS,KAAK,iBAAiB,EAAE,CAAC;oBAClD,YAAY,GAAG,YAAY,GAAG,CAAC,CAAC,GAAG,SAAS,GAAG,IAAI,CAAC,CAAC,CAAC,oBAAoB;gBAC5E,CAAC;qBAAM,IAAI,MAAM,CAAC,SAAS,KAAK,aAAa,EAAE,CAAC;oBAC9C,YAAY,GAAG,YAAY,GAAG,CAAC,CAAC,GAAG,SAAS,GAAG,GAAG,CAAC,CAAC,CAAC,+BAA+B;gBACtF,CAAC;gBAED,OAAO;oBACL,SAAS;oBACT,aAAa,EAAE,MAAM,CAAC,SAAS,GAAG,SAAS;oBAC3C,aAAa,EAAE,YAAY;oBAC3B,UAAU,EAAE,YAAY,GAAG,YAAY;oBACvC,gBAAgB,EAAE,CAAC,CAAC,YAAY,GAAG,YAAY,CAAC,GAAG,YAAY,CAAC,GAAG,GAAG;iBACvE,CAAC;YACJ,CAAC,CAAC,CAAC;YAEH,OAAO;gBACL,SAAS,EAAE,MAAM,CAAC,SAAS;gBAC3B,SAAS,EAAE,MAAM,CAAC,SAAS;gBAC3B,OAAO;gBACP,WAAW,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC;oBAC5D,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;aACjE,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,qCAAqC;QACrC,MAAM,mBAAmB,GAAG,kBAAkB;aAC3C,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,WAAW,CAAC;aAC7C,GAAG,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACrB,IAAI,MAAc,CAAC;YACnB,IAAI,MAAM,CAAC,WAAW,GAAG,CAAC,EAAE,CAAC;gBAC3B,MAAM,GAAG,MAAM,CAAC;YAClB,CAAC;iBAAM,IAAI,MAAM,CAAC,WAAW,GAAG,CAAC,EAAE,CAAC;gBAClC,MAAM,GAAG,QAAQ,CAAC;YACpB,CAAC;iBAAM,CAAC;gBACN,MAAM,GAAG,KAAK,CAAC;YACjB,CAAC;YAED,OAAO;gBACL,IAAI,EAAE,KAAK,GAAG,CAAC;gBACf,SAAS,EAAE,MAAM,CAAC,SAAS;gBAC3B,WAAW,EAAE,MAAM,CAAC,WAAW;gBAC/B,MAAM;aACP,CAAC;QACJ,CAAC,CAAC,CAAC;QAEL,OAAO;YACL,YAAY;YACZ,kBAAkB;YAClB,mBAAmB;YACnB,WAAW,EAAE;gBACX,6BAA6B,mBAAmB,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE;gBAC/D,gBAAgB,IAAI,CAAC,GAAG,CAAC,GAAG,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,cAAc,EAAE,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,cAAc,EAAE,EAAE;gBAC7N,8BAA8B,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC,MAAM,yBAAyB;aACnH;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,KAAK,CAAC,2BAA2B,CAC9C,oBAA0C,EAC1C,mBAA4C,EAC5C,cAA8B;QAE9B,MAAM,eAAe,GAAyB,EAAE,CAAC;QAEjD,8BAA8B;QAC9B,IAAI,oBAAoB,CAAC,aAAa,CAAC,cAAc,CAAC,UAAU,GAAG,EAAE,EAAE,CAAC;YACtE,eAAe,CAAC,IAAI,CAAC;gBACnB,EAAE,EAAE,6BAA6B;gBACjC,QAAQ,EAAE,0BAA0B;gBACpC,QAAQ,EAAE,MAAM;gBAChB,KAAK,EAAE,mCAAmC;gBAC1C,WAAW,EAAE,oHAAoH;gBACjI,gBAAgB,EAAE,oBAAoB,CAAC,iBAAiB,GAAG,IAAI;gBAC/D,kBAAkB,EAAE,oBAAoB,CAAC,aAAa,CAAC,YAAY,CAAC,MAAM,GAAG,GAAG;gBAChF,aAAa,EAAE,GAAG;gBAClB,OAAO,EAAE;oBACP,uCAAuC;oBACvC,sCAAsC;oBACtC,qCAAqC;oBACrC,mCAAmC;iBACpC;gBACD,SAAS,EAAE,KAAK;aACjB,CAAC,CAAC;QACL,CAAC;QAED,gCAAgC;QAChC,IAAI,oBAAoB,CAAC,aAAa,CAAC,gBAAgB,CAAC,UAAU,GAAG,EAAE,EAAE,CAAC;YACxE,eAAe,CAAC,IAAI,CAAC;gBACnB,EAAE,EAAE,0BAA0B;gBAC9B,QAAQ,EAAE,4BAA4B;gBACtC,QAAQ,EAAE,QAAQ;gBAClB,KAAK,EAAE,+BAA+B;gBACtC,WAAW,EAAE,oGAAoG;gBACjH,gBAAgB,EAAE,oBAAoB,CAAC,iBAAiB,GAAG,IAAI;gBAC/D,kBAAkB,EAAE,KAAK;gBACzB,aAAa,EAAE,GAAG;gBAClB,OAAO,EAAE;oBACP,0CAA0C;oBAC1C,oCAAoC;oBACpC,yBAAyB;oBACzB,4CAA4C;iBAC7C;gBACD,SAAS,EAAE,QAAQ;aACpB,CAAC,CAAC;QACL,CAAC;QAED,oCAAoC;QACpC,MAAM,eAAe,GAAG,cAAc,CAAC,sBAAsB,CAAC;QAC9D,IAAI,eAAe,CAAC,cAAc,GAAG,CAAC,KAAK,EAAE,CAAC;YAC5C,eAAe,CAAC,IAAI,CAAC;gBACnB,EAAE,EAAE,oBAAoB;gBACxB,QAAQ,EAAE,oBAAoB;gBAC9B,QAAQ,EAAE,MAAM;gBAChB,KAAK,EAAE,YAAY,eAAe,CAAC,eAAe,EAAE;gBACpD,WAAW,EAAE,eAAe,CAAC,WAAW;gBACxC,gBAAgB,EAAE,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,cAAc,CAAC;gBAC1D,kBAAkB,EAAE,CAAC,EAAE,mCAAmC;gBAC1D,aAAa,EAAE,CAAC;gBAChB,OAAO,EAAE;oBACP,2CAA2C;oBAC3C,oCAAoC;oBACpC,gCAAgC;iBACjC;gBACD,SAAS,EAAE,QAAQ;aACpB,CAAC,CAAC;QACL,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,kBAAkB,CAAC,QAAgB;QAChD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC1D,OAAO,iBAAiB,QAAQ,IAAI,SAAS,IAAI,MAAM,EAAE,CAAC;IAC5D,CAAC;;AAr7BH,kEAs7BC;AAr7ByB,mCAAO,GAAG,OAAO,CAAC;AAClB,sCAAU,GAAG,IAAI,GAAG,EAAiC,CAAC;AAE9E,iCAAiC;AACT,oDAAwB,GAAG;IACjD,MAAM,EAAE,IAAI,EAAE,mCAAmC;IACjD,WAAW,EAAE,KAAK,EAAE,0CAA0C;IAC9D,KAAK,EAAE,KAAK,EAAE,oCAAoC;IAClD,SAAS,EAAE,KAAK,CAAC,wCAAwC;CAC1D,CAAC;AAEF,oCAAoC;AACZ,0CAAc,GAAG;IACvC,IAAI,EAAE,EAAE;IACR,MAAM,EAAE,EAAE;IACV,GAAG,EAAE,EAAE;IACP,QAAQ,EAAE,EAAE;IACZ,OAAO,EAAE,EAAE;IACX,QAAQ,EAAE,EAAE;IACZ,OAAO,EAAE,IAAI,CAAC,WAAW;CAC1B,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\LifecycleCostAnalysisEngine.ts"],
      sourcesContent: ["/**\r\n * Lifecycle Cost Analysis Engine\r\n * \r\n * Comprehensive lifecycle cost analysis service for Phase 3 Priority 3: Advanced System Analysis Tools\r\n * Provides initial costs, operating costs, maintenance costs, energy costs, and total cost of ownership\r\n * calculations for HVAC duct systems.\r\n * \r\n * @version 3.0.0\r\n * <AUTHOR> Suite Development Team\r\n */\r\n\r\nimport {\r\n  LifecycleCostAnalysis,\r\n  CostAnalysisParameters,\r\n  InitialCosts,\r\n  OperatingCosts,\r\n  MaintenanceCosts,\r\n  ReplacementCosts,\r\n  TotalCostOfOwnership,\r\n  CostComparison,\r\n  CostSensitivityAnalysis,\r\n  CostRecommendation,\r\n  CostAnalysisMethod,\r\n  UncertaintyLevel,\r\n  EquipmentCosts,\r\n  SystemConfiguration,\r\n  EnergyAnalysis,\r\n  TimeFrame\r\n} from './types/SystemAnalysisTypes';\r\n\r\nimport { EnergyEfficiencyAnalysisEngine } from './EnergyEfficiencyAnalysisEngine';\r\n\r\n/**\r\n * Lifecycle Cost Analysis Engine\r\n * \r\n * Provides comprehensive lifecycle cost analysis capabilities including:\r\n * - Initial capital cost analysis\r\n * - Operating cost projections\r\n * - Maintenance cost modeling\r\n * - Replacement cost planning\r\n * - Total cost of ownership calculations\r\n * - Cost comparison and sensitivity analysis\r\n * - Financial metrics (NPV, IRR, Payback)\r\n */\r\nexport class LifecycleCostAnalysisEngine {\r\n  private static readonly VERSION = '3.0.0';\r\n  private static readonly COST_CACHE = new Map<string, LifecycleCostAnalysis>();\r\n  \r\n  // Cost escalation rates (annual)\r\n  private static readonly DEFAULT_ESCALATION_RATES = {\r\n    ENERGY: 0.03, // 3% annual energy cost escalation\r\n    MAINTENANCE: 0.025, // 2.5% annual maintenance cost escalation\r\n    LABOR: 0.035, // 3.5% annual labor cost escalation\r\n    MATERIALS: 0.028 // 2.8% annual materials cost escalation\r\n  };\r\n\r\n  // Equipment life expectancy (years)\r\n  private static readonly EQUIPMENT_LIFE = {\r\n    FANS: 20,\r\n    MOTORS: 15,\r\n    VFD: 12,\r\n    DUCTWORK: 30,\r\n    DAMPERS: 20,\r\n    CONTROLS: 10,\r\n    FILTERS: 0.25 // 3 months\r\n  };\r\n\r\n  /**\r\n   * Perform comprehensive lifecycle cost analysis\r\n   */\r\n  public static async analyzeLifecycleCosts(\r\n    systemConfiguration: SystemConfiguration,\r\n    energyAnalysis: EnergyAnalysis,\r\n    analysisParameters?: Partial<CostAnalysisParameters>\r\n  ): Promise<LifecycleCostAnalysis> {\r\n    try {\r\n      const analysisId = this.generateAnalysisId(systemConfiguration.id);\r\n      const timestamp = new Date();\r\n\r\n      // Set default analysis parameters\r\n      const parameters = this.setDefaultParameters(analysisParameters);\r\n\r\n      // Calculate initial costs\r\n      const initialCosts = await this.calculateInitialCosts(systemConfiguration);\r\n\r\n      // Calculate operating costs\r\n      const operatingCosts = await this.calculateOperatingCosts(\r\n        systemConfiguration,\r\n        energyAnalysis,\r\n        parameters\r\n      );\r\n\r\n      // Calculate maintenance costs\r\n      const maintenanceCosts = await this.calculateMaintenanceCosts(\r\n        systemConfiguration,\r\n        parameters\r\n      );\r\n\r\n      // Calculate replacement costs\r\n      const replacementCosts = await this.calculateReplacementCosts(\r\n        systemConfiguration,\r\n        parameters\r\n      );\r\n\r\n      // Calculate total cost of ownership\r\n      const totalCostOfOwnership = await this.calculateTotalCostOfOwnership(\r\n        initialCosts,\r\n        operatingCosts,\r\n        maintenanceCosts,\r\n        replacementCosts,\r\n        parameters\r\n      );\r\n\r\n      // Perform cost comparison\r\n      const costComparison = await this.performCostComparison(\r\n        systemConfiguration,\r\n        totalCostOfOwnership\r\n      );\r\n\r\n      // Perform sensitivity analysis\r\n      const sensitivityAnalysis = await this.performSensitivityAnalysis(\r\n        systemConfiguration,\r\n        parameters,\r\n        totalCostOfOwnership\r\n      );\r\n\r\n      // Generate cost recommendations\r\n      const recommendations = await this.generateCostRecommendations(\r\n        totalCostOfOwnership,\r\n        sensitivityAnalysis,\r\n        costComparison\r\n      );\r\n\r\n      const analysis: LifecycleCostAnalysis = {\r\n        id: analysisId,\r\n        systemId: systemConfiguration.id,\r\n        analysisTimestamp: timestamp,\r\n        analysisParameters: parameters,\r\n        initialCosts,\r\n        operatingCosts,\r\n        maintenanceCosts,\r\n        replacementCosts,\r\n        totalCostOfOwnership,\r\n        costComparison,\r\n        sensitivityAnalysis,\r\n        recommendations\r\n      };\r\n\r\n      // Cache the analysis\r\n      this.COST_CACHE.set(analysisId, analysis);\r\n\r\n      return analysis;\r\n\r\n    } catch (error) {\r\n      throw new Error(`Lifecycle cost analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Set default analysis parameters\r\n   */\r\n  private static setDefaultParameters(\r\n    provided?: Partial<CostAnalysisParameters>\r\n  ): CostAnalysisParameters {\r\n    return {\r\n      analysisHorizon: provided?.analysisHorizon || 20, // 20 years\r\n      discountRate: provided?.discountRate || 0.06, // 6% discount rate\r\n      inflationRate: provided?.inflationRate || 0.025, // 2.5% inflation\r\n      energyEscalationRate: provided?.energyEscalationRate || this.DEFAULT_ESCALATION_RATES.ENERGY,\r\n      currency: provided?.currency || 'USD',\r\n      analysisMethod: provided?.analysisMethod || CostAnalysisMethod.NET_PRESENT_VALUE,\r\n      uncertaintyLevel: provided?.uncertaintyLevel || UncertaintyLevel.MEDIUM\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate initial system costs\r\n   */\r\n  private static async calculateInitialCosts(\r\n    systemConfiguration: SystemConfiguration\r\n  ): Promise<InitialCosts> {\r\n    const designAirflow = systemConfiguration.designParameters.designAirflow;\r\n    const systemType = systemConfiguration.systemType;\r\n\r\n    // Equipment costs based on system size and type\r\n    const equipmentCosts = this.calculateEquipmentCosts(systemConfiguration);\r\n\r\n    // Installation costs (typically 40-60% of equipment costs)\r\n    const installationCosts = this.calculateInstallationCosts(equipmentCosts);\r\n\r\n    // Design costs (typically 8-12% of total project cost)\r\n    const designCosts = this.calculateDesignCosts(equipmentCosts, installationCosts);\r\n\r\n    // Permits and fees (typically 2-5% of total project cost)\r\n    const permitsCosts = this.calculatePermitsCosts(equipmentCosts, installationCosts);\r\n\r\n    const totalInitialCost = equipmentCosts.total + installationCosts.total + \r\n                           designCosts.total + permitsCosts.total;\r\n\r\n    return {\r\n      equipmentCosts,\r\n      installationCosts,\r\n      designCosts,\r\n      permitsCosts,\r\n      totalInitialCost,\r\n      costPerCFM: totalInitialCost / designAirflow,\r\n      costPerSquareFoot: totalInitialCost / 10000 // Assumed building area\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate equipment costs breakdown\r\n   */\r\n  private static calculateEquipmentCosts(systemConfiguration: SystemConfiguration): EquipmentCosts {\r\n    const designAirflow = systemConfiguration.designParameters.designAirflow;\r\n    const designPressure = systemConfiguration.designParameters.designPressure;\r\n\r\n    // Cost factors based on system size ($/CFM)\r\n    const fanCostFactor = this.getFanCostFactor(designAirflow, designPressure);\r\n    const ductworkCostFactor = 8.5; // $/CFM for ductwork\r\n    const fittingsCostFactor = 2.2; // $/CFM for fittings\r\n    const dampersCostFactor = 1.8; // $/CFM for dampers\r\n    const controlsCostFactor = 3.5; // $/CFM for controls\r\n    const accessoriesCostFactor = 1.2; // $/CFM for accessories\r\n\r\n    const fans = designAirflow * fanCostFactor;\r\n    const ductwork = designAirflow * ductworkCostFactor;\r\n    const fittings = designAirflow * fittingsCostFactor;\r\n    const dampers = designAirflow * dampersCostFactor;\r\n    const controls = designAirflow * controlsCostFactor;\r\n    const accessories = designAirflow * accessoriesCostFactor;\r\n\r\n    return {\r\n      fans,\r\n      ductwork,\r\n      fittings,\r\n      dampers,\r\n      controls,\r\n      accessories,\r\n      total: fans + ductwork + fittings + dampers + controls + accessories\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get fan cost factor based on size and pressure\r\n   */\r\n  private static getFanCostFactor(airflow: number, pressure: number): number {\r\n    // Base cost factor\r\n    let costFactor = 12.0; // $/CFM base\r\n\r\n    // Size adjustments\r\n    if (airflow > 50000) costFactor *= 0.85; // Economies of scale\r\n    else if (airflow < 5000) costFactor *= 1.25; // Small system premium\r\n\r\n    // Pressure adjustments\r\n    if (pressure > 4.0) costFactor *= 1.3; // High pressure premium\r\n    else if (pressure < 2.0) costFactor *= 0.9; // Low pressure discount\r\n\r\n    return costFactor;\r\n  }\r\n\r\n  /**\r\n   * Calculate installation costs\r\n   */\r\n  private static calculateInstallationCosts(equipmentCosts: EquipmentCosts): InstallationCosts {\r\n    // Installation cost factors (as percentage of equipment cost)\r\n    const factors = {\r\n      fans: 0.4, // 40% of fan cost\r\n      ductwork: 0.6, // 60% of ductwork cost\r\n      fittings: 0.5, // 50% of fittings cost\r\n      dampers: 0.3, // 30% of dampers cost\r\n      controls: 0.8, // 80% of controls cost (complex installation)\r\n      accessories: 0.4 // 40% of accessories cost\r\n    };\r\n\r\n    const fans = equipmentCosts.fans * factors.fans;\r\n    const ductwork = equipmentCosts.ductwork * factors.ductwork;\r\n    const fittings = equipmentCosts.fittings * factors.fittings;\r\n    const dampers = equipmentCosts.dampers * factors.dampers;\r\n    const controls = equipmentCosts.controls * factors.controls;\r\n    const accessories = equipmentCosts.accessories * factors.accessories;\r\n\r\n    // Additional installation costs\r\n    const laborCosts = (fans + ductwork + fittings + dampers + controls + accessories) * 0.6;\r\n    const materialsCosts = equipmentCosts.total * 0.15; // 15% for installation materials\r\n    const equipmentRental = equipmentCosts.total * 0.05; // 5% for equipment rental\r\n    const testing = equipmentCosts.total * 0.03; // 3% for testing and commissioning\r\n\r\n    const total = fans + ductwork + fittings + dampers + controls + accessories + \r\n                 laborCosts + materialsCosts + equipmentRental + testing;\r\n\r\n    return {\r\n      fans,\r\n      ductwork,\r\n      fittings,\r\n      dampers,\r\n      controls,\r\n      accessories,\r\n      laborCosts,\r\n      materialsCosts,\r\n      equipmentRental,\r\n      testing,\r\n      total\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate design costs\r\n   */\r\n  private static calculateDesignCosts(\r\n    equipmentCosts: EquipmentCosts,\r\n    installationCosts: InstallationCosts\r\n  ): DesignCosts {\r\n    const projectCost = equipmentCosts.total + installationCosts.total;\r\n\r\n    const engineeringDesign = projectCost * 0.08; // 8% for engineering design\r\n    const drawings = projectCost * 0.02; // 2% for drawings and specifications\r\n    const calculations = projectCost * 0.015; // 1.5% for calculations\r\n    const projectManagement = projectCost * 0.025; // 2.5% for project management\r\n\r\n    return {\r\n      engineeringDesign,\r\n      drawings,\r\n      calculations,\r\n      projectManagement,\r\n      total: engineeringDesign + drawings + calculations + projectManagement\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate permits and fees\r\n   */\r\n  private static calculatePermitsCosts(\r\n    equipmentCosts: EquipmentCosts,\r\n    installationCosts: InstallationCosts\r\n  ): PermitsCosts {\r\n    const projectCost = equipmentCosts.total + installationCosts.total;\r\n\r\n    const buildingPermits = projectCost * 0.015; // 1.5% for building permits\r\n    const inspectionFees = projectCost * 0.008; // 0.8% for inspection fees\r\n    const utilityConnections = 2500; // Fixed cost for utility connections\r\n    const environmentalFees = projectCost * 0.005; // 0.5% for environmental fees\r\n\r\n    return {\r\n      buildingPermits,\r\n      inspectionFees,\r\n      utilityConnections,\r\n      environmentalFees,\r\n      total: buildingPermits + inspectionFees + utilityConnections + environmentalFees\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate operating costs over analysis horizon\r\n   */\r\n  private static async calculateOperatingCosts(\r\n    systemConfiguration: SystemConfiguration,\r\n    energyAnalysis: EnergyAnalysis,\r\n    parameters: CostAnalysisParameters\r\n  ): Promise<OperatingCosts> {\r\n    const analysisHorizon = parameters.analysisHorizon;\r\n    const energyEscalationRate = parameters.energyEscalationRate;\r\n    const discountRate = parameters.discountRate;\r\n\r\n    // Annual energy costs\r\n    const annualEnergyCosts = energyAnalysis.energyCosts.currentCosts.totalCost;\r\n\r\n    // Calculate present value of energy costs over analysis horizon\r\n    let totalEnergyPV = 0;\r\n    const yearlyEnergyCosts: number[] = [];\r\n\r\n    for (let year = 1; year <= analysisHorizon; year++) {\r\n      const yearlyEnergyCost = annualEnergyCosts * Math.pow(1 + energyEscalationRate, year - 1);\r\n      const presentValue = yearlyEnergyCost / Math.pow(1 + discountRate, year);\r\n      totalEnergyPV += presentValue;\r\n      yearlyEnergyCosts.push(yearlyEnergyCost);\r\n    }\r\n\r\n    // Other operating costs\r\n    const annualInsurance = annualEnergyCosts * 0.02; // 2% of energy costs\r\n    const annualUtilities = 1200; // Fixed annual utilities\r\n    const annualCompliance = 800; // Annual compliance and reporting costs\r\n\r\n    // Calculate present values for other operating costs\r\n    const insurancePV = this.calculatePresentValue(annualInsurance, parameters.inflationRate, discountRate, analysisHorizon);\r\n    const utilitiesPV = this.calculatePresentValue(annualUtilities, parameters.inflationRate, discountRate, analysisHorizon);\r\n    const compliancePV = this.calculatePresentValue(annualCompliance, parameters.inflationRate, discountRate, analysisHorizon);\r\n\r\n    const totalOperatingPV = totalEnergyPV + insurancePV + utilitiesPV + compliancePV;\r\n\r\n    return {\r\n      energyCosts: {\r\n        annual: annualEnergyCosts,\r\n        presentValue: totalEnergyPV,\r\n        yearlyProjection: yearlyEnergyCosts,\r\n        escalationRate: energyEscalationRate\r\n      },\r\n      maintenanceCosts: {\r\n        annual: 0, // Calculated separately\r\n        presentValue: 0,\r\n        yearlyProjection: [],\r\n        escalationRate: this.DEFAULT_ESCALATION_RATES.MAINTENANCE\r\n      },\r\n      insuranceCosts: {\r\n        annual: annualInsurance,\r\n        presentValue: insurancePV,\r\n        escalationRate: parameters.inflationRate\r\n      },\r\n      utilityCosts: {\r\n        annual: annualUtilities,\r\n        presentValue: utilitiesPV,\r\n        escalationRate: parameters.inflationRate\r\n      },\r\n      complianceCosts: {\r\n        annual: annualCompliance,\r\n        presentValue: compliancePV,\r\n        escalationRate: parameters.inflationRate\r\n      },\r\n      totalAnnual: annualEnergyCosts + annualInsurance + annualUtilities + annualCompliance,\r\n      totalPresentValue: totalOperatingPV\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate present value of annual costs\r\n   */\r\n  private static calculatePresentValue(\r\n    annualCost: number,\r\n    escalationRate: number,\r\n    discountRate: number,\r\n    years: number\r\n  ): number {\r\n    let presentValue = 0;\r\n    \r\n    for (let year = 1; year <= years; year++) {\r\n      const yearlyValue = annualCost * Math.pow(1 + escalationRate, year - 1);\r\n      presentValue += yearlyValue / Math.pow(1 + discountRate, year);\r\n    }\r\n    \r\n    return presentValue;\r\n  }\r\n\r\n  /**\r\n   * Calculate maintenance costs over analysis horizon\r\n   */\r\n  private static async calculateMaintenanceCosts(\r\n    systemConfiguration: SystemConfiguration,\r\n    parameters: CostAnalysisParameters\r\n  ): Promise<MaintenanceCosts> {\r\n    const designAirflow = systemConfiguration.designParameters.designAirflow;\r\n    const analysisHorizon = parameters.analysisHorizon;\r\n    const discountRate = parameters.discountRate;\r\n    const maintenanceEscalationRate = this.DEFAULT_ESCALATION_RATES.MAINTENANCE;\r\n\r\n    // Annual maintenance costs ($/CFM/year)\r\n    const preventiveMaintenanceFactor = 0.8; // $/CFM/year\r\n    const correctiveMaintenanceFactor = 0.4; // $/CFM/year\r\n    const filterReplacementFactor = 0.6; // $/CFM/year\r\n\r\n    const annualPreventive = designAirflow * preventiveMaintenanceFactor;\r\n    const annualCorrective = designAirflow * correctiveMaintenanceFactor;\r\n    const annualFilters = designAirflow * filterReplacementFactor;\r\n\r\n    // Calculate present values\r\n    const preventivePV = this.calculatePresentValue(annualPreventive, maintenanceEscalationRate, discountRate, analysisHorizon);\r\n    const correctivePV = this.calculatePresentValue(annualCorrective, maintenanceEscalationRate, discountRate, analysisHorizon);\r\n    const filtersPV = this.calculatePresentValue(annualFilters, maintenanceEscalationRate, discountRate, analysisHorizon);\r\n\r\n    // Major overhauls (every 10 years)\r\n    const majorOverhaulCost = designAirflow * 5.0; // $5/CFM\r\n    const overhaulYears = [10, 20].filter(year => year <= analysisHorizon);\r\n    let overhaulsPV = 0;\r\n    \r\n    overhaulYears.forEach(year => {\r\n      const overhaulCostInflated = majorOverhaulCost * Math.pow(1 + maintenanceEscalationRate, year - 1);\r\n      overhaulsPV += overhaulCostInflated / Math.pow(1 + discountRate, year);\r\n    });\r\n\r\n    const totalAnnual = annualPreventive + annualCorrective + annualFilters;\r\n    const totalPresentValue = preventivePV + correctivePV + filtersPV + overhaulsPV;\r\n\r\n    return {\r\n      preventiveMaintenance: {\r\n        annual: annualPreventive,\r\n        presentValue: preventivePV,\r\n        description: 'Regular inspections, cleaning, and adjustments'\r\n      },\r\n      correctiveMaintenance: {\r\n        annual: annualCorrective,\r\n        presentValue: correctivePV,\r\n        description: 'Repairs and component replacements'\r\n      },\r\n      filterReplacement: {\r\n        annual: annualFilters,\r\n        presentValue: filtersPV,\r\n        description: 'Regular filter replacements'\r\n      },\r\n      majorOverhauls: {\r\n        cost: majorOverhaulCost,\r\n        presentValue: overhaulsPV,\r\n        schedule: overhaulYears,\r\n        description: 'Major system overhauls and upgrades'\r\n      },\r\n      totalAnnual,\r\n      totalPresentValue,\r\n      escalationRate: maintenanceEscalationRate\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate replacement costs over analysis horizon\r\n   */\r\n  private static async calculateReplacementCosts(\r\n    systemConfiguration: SystemConfiguration,\r\n    parameters: CostAnalysisParameters\r\n  ): Promise<ReplacementCosts> {\r\n    const analysisHorizon = parameters.analysisHorizon;\r\n    const discountRate = parameters.discountRate;\r\n    const materialEscalationRate = this.DEFAULT_ESCALATION_RATES.MATERIALS;\r\n\r\n    const equipmentReplacements: EquipmentReplacement[] = [];\r\n    let totalReplacementPV = 0;\r\n\r\n    // Calculate replacement costs for each equipment type\r\n    const equipmentTypes = [\r\n      { type: 'fans', life: this.EQUIPMENT_LIFE.FANS, costFactor: 12.0 },\r\n      { type: 'motors', life: this.EQUIPMENT_LIFE.MOTORS, costFactor: 3.5 },\r\n      { type: 'vfd', life: this.EQUIPMENT_LIFE.VFD, costFactor: 2.8 },\r\n      { type: 'dampers', life: this.EQUIPMENT_LIFE.DAMPERS, costFactor: 1.8 },\r\n      { type: 'controls', life: this.EQUIPMENT_LIFE.CONTROLS, costFactor: 3.5 }\r\n    ];\r\n\r\n    const designAirflow = systemConfiguration.designParameters.designAirflow;\r\n\r\n    equipmentTypes.forEach(equipment => {\r\n      const replacementYears = [];\r\n      for (let year = equipment.life; year <= analysisHorizon; year += equipment.life) {\r\n        replacementYears.push(year);\r\n      }\r\n\r\n      if (replacementYears.length > 0) {\r\n        const replacementCost = designAirflow * equipment.costFactor;\r\n        let equipmentPV = 0;\r\n\r\n        replacementYears.forEach(year => {\r\n          const inflatedCost = replacementCost * Math.pow(1 + materialEscalationRate, year - 1);\r\n          const presentValue = inflatedCost / Math.pow(1 + discountRate, year);\r\n          equipmentPV += presentValue;\r\n        });\r\n\r\n        equipmentReplacements.push({\r\n          equipmentType: equipment.type,\r\n          replacementYears,\r\n          unitCost: replacementCost,\r\n          totalPresentValue: equipmentPV,\r\n          description: `${equipment.type} replacement based on ${equipment.life}-year life expectancy`\r\n        });\r\n\r\n        totalReplacementPV += equipmentPV;\r\n      }\r\n    });\r\n\r\n    // Salvage value at end of analysis period\r\n    const salvageValue = this.calculateSalvageValue(systemConfiguration, parameters);\r\n    const salvagePV = salvageValue / Math.pow(1 + discountRate, analysisHorizon);\r\n\r\n    return {\r\n      equipmentReplacements,\r\n      salvageValue: {\r\n        value: salvageValue,\r\n        presentValue: salvagePV,\r\n        description: 'Estimated salvage value at end of analysis period'\r\n      },\r\n      totalReplacementCost: totalReplacementPV,\r\n      netReplacementCost: totalReplacementPV - salvagePV\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate salvage value at end of analysis period\r\n   */\r\n  private static calculateSalvageValue(\r\n    systemConfiguration: SystemConfiguration,\r\n    parameters: CostAnalysisParameters\r\n  ): number {\r\n    const designAirflow = systemConfiguration.designParameters.designAirflow;\r\n    const analysisHorizon = parameters.analysisHorizon;\r\n\r\n    // Calculate remaining life for each equipment type\r\n    const ductworkRemainingLife = Math.max(0, this.EQUIPMENT_LIFE.DUCTWORK - analysisHorizon);\r\n    const fansRemainingLife = Math.max(0, this.EQUIPMENT_LIFE.FANS - (analysisHorizon % this.EQUIPMENT_LIFE.FANS));\r\n    const dampersRemainingLife = Math.max(0, this.EQUIPMENT_LIFE.DAMPERS - (analysisHorizon % this.EQUIPMENT_LIFE.DAMPERS));\r\n\r\n    // Calculate salvage value based on remaining useful life\r\n    const ductworkSalvage = (designAirflow * 8.5) * (ductworkRemainingLife / this.EQUIPMENT_LIFE.DUCTWORK);\r\n    const fansSalvage = (designAirflow * 12.0) * (fansRemainingLife / this.EQUIPMENT_LIFE.FANS);\r\n    const dampersSalvage = (designAirflow * 1.8) * (dampersRemainingLife / this.EQUIPMENT_LIFE.DAMPERS);\r\n\r\n    return ductworkSalvage + fansSalvage + dampersSalvage;\r\n  }\r\n\r\n  /**\r\n   * Calculate total cost of ownership\r\n   */\r\n  private static async calculateTotalCostOfOwnership(\r\n    initialCosts: InitialCosts,\r\n    operatingCosts: OperatingCosts,\r\n    maintenanceCosts: MaintenanceCosts,\r\n    replacementCosts: ReplacementCosts,\r\n    parameters: CostAnalysisParameters\r\n  ): Promise<TotalCostOfOwnership> {\r\n    const totalPresentValue = initialCosts.totalInitialCost +\r\n                             operatingCosts.totalPresentValue +\r\n                             maintenanceCosts.totalPresentValue +\r\n                             replacementCosts.netReplacementCost;\r\n\r\n    const totalAnnualizedCost = this.calculateAnnualizedCost(totalPresentValue, parameters);\r\n\r\n    // Cost breakdown by category\r\n    const costBreakdown = {\r\n      initialCosts: {\r\n        amount: initialCosts.totalInitialCost,\r\n        percentage: (initialCosts.totalInitialCost / totalPresentValue) * 100\r\n      },\r\n      operatingCosts: {\r\n        amount: operatingCosts.totalPresentValue,\r\n        percentage: (operatingCosts.totalPresentValue / totalPresentValue) * 100\r\n      },\r\n      maintenanceCosts: {\r\n        amount: maintenanceCosts.totalPresentValue,\r\n        percentage: (maintenanceCosts.totalPresentValue / totalPresentValue) * 100\r\n      },\r\n      replacementCosts: {\r\n        amount: replacementCosts.netReplacementCost,\r\n        percentage: (replacementCosts.netReplacementCost / totalPresentValue) * 100\r\n      }\r\n    };\r\n\r\n    // Financial metrics\r\n    const financialMetrics = this.calculateFinancialMetrics(\r\n      initialCosts.totalInitialCost,\r\n      operatingCosts.energyCosts.annual + maintenanceCosts.totalAnnual,\r\n      parameters\r\n    );\r\n\r\n    return {\r\n      totalPresentValue,\r\n      totalAnnualizedCost,\r\n      costBreakdown,\r\n      financialMetrics,\r\n      analysisParameters: parameters,\r\n      costPerCFM: totalPresentValue / 10000, // Assuming 10,000 CFM system\r\n      costPerSquareFoot: totalPresentValue / 10000, // Assuming 10,000 sq ft building\r\n      paybackPeriod: this.calculateSimplePaybackPeriod(initialCosts.totalInitialCost, operatingCosts.energyCosts.annual)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate annualized cost\r\n   */\r\n  private static calculateAnnualizedCost(\r\n    presentValue: number,\r\n    parameters: CostAnalysisParameters\r\n  ): number {\r\n    const discountRate = parameters.discountRate;\r\n    const analysisHorizon = parameters.analysisHorizon;\r\n\r\n    // Capital recovery factor\r\n    const crf = (discountRate * Math.pow(1 + discountRate, analysisHorizon)) /\r\n                (Math.pow(1 + discountRate, analysisHorizon) - 1);\r\n\r\n    return presentValue * crf;\r\n  }\r\n\r\n  /**\r\n   * Calculate financial metrics\r\n   */\r\n  private static calculateFinancialMetrics(\r\n    initialCost: number,\r\n    annualSavings: number,\r\n    parameters: CostAnalysisParameters\r\n  ): FinancialMetrics {\r\n    const discountRate = parameters.discountRate;\r\n    const analysisHorizon = parameters.analysisHorizon;\r\n\r\n    // Net Present Value (assuming savings compared to baseline)\r\n    const npv = this.calculateNPV(initialCost, annualSavings, discountRate, analysisHorizon);\r\n\r\n    // Internal Rate of Return (simplified calculation)\r\n    const irr = this.calculateIRR(initialCost, annualSavings, analysisHorizon);\r\n\r\n    // Discounted Payback Period\r\n    const discountedPayback = this.calculateDiscountedPayback(initialCost, annualSavings, discountRate);\r\n\r\n    // Profitability Index\r\n    const profitabilityIndex = (npv + initialCost) / initialCost;\r\n\r\n    return {\r\n      netPresentValue: npv,\r\n      internalRateOfReturn: irr,\r\n      paybackPeriod: this.calculateSimplePaybackPeriod(initialCost, annualSavings),\r\n      discountedPaybackPeriod: discountedPayback,\r\n      profitabilityIndex,\r\n      returnOnInvestment: (annualSavings * analysisHorizon - initialCost) / initialCost * 100\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate Net Present Value\r\n   */\r\n  private static calculateNPV(\r\n    initialCost: number,\r\n    annualCashFlow: number,\r\n    discountRate: number,\r\n    years: number\r\n  ): number {\r\n    let npv = -initialCost;\r\n\r\n    for (let year = 1; year <= years; year++) {\r\n      npv += annualCashFlow / Math.pow(1 + discountRate, year);\r\n    }\r\n\r\n    return npv;\r\n  }\r\n\r\n  /**\r\n   * Calculate Internal Rate of Return (simplified)\r\n   */\r\n  private static calculateIRR(\r\n    initialCost: number,\r\n    annualCashFlow: number,\r\n    years: number\r\n  ): number {\r\n    // Simplified IRR calculation using approximation\r\n    if (annualCashFlow <= 0) return 0;\r\n\r\n    const totalCashFlow = annualCashFlow * years;\r\n    if (totalCashFlow <= initialCost) return 0;\r\n\r\n    // Approximation: IRR \u2248 (Total Cash Flow / Initial Cost)^(1/years) - 1\r\n    return Math.pow(totalCashFlow / initialCost, 1 / years) - 1;\r\n  }\r\n\r\n  /**\r\n   * Calculate simple payback period\r\n   */\r\n  private static calculateSimplePaybackPeriod(\r\n    initialCost: number,\r\n    annualSavings: number\r\n  ): number {\r\n    if (annualSavings <= 0) return Infinity;\r\n    return initialCost / annualSavings;\r\n  }\r\n\r\n  /**\r\n   * Calculate discounted payback period\r\n   */\r\n  private static calculateDiscountedPayback(\r\n    initialCost: number,\r\n    annualCashFlow: number,\r\n    discountRate: number\r\n  ): number {\r\n    let cumulativePV = 0;\r\n    let year = 0;\r\n\r\n    while (cumulativePV < initialCost && year < 50) { // Max 50 years\r\n      year++;\r\n      cumulativePV += annualCashFlow / Math.pow(1 + discountRate, year);\r\n    }\r\n\r\n    return year;\r\n  }\r\n\r\n  /**\r\n   * Perform cost comparison with alternatives\r\n   */\r\n  private static async performCostComparison(\r\n    systemConfiguration: SystemConfiguration,\r\n    totalCostOfOwnership: TotalCostOfOwnership\r\n  ): Promise<CostComparison> {\r\n    // Simplified cost comparison with typical alternatives\r\n    const alternatives = [\r\n      {\r\n        name: 'High Efficiency System',\r\n        initialCostMultiplier: 1.25,\r\n        operatingCostMultiplier: 0.8,\r\n        description: 'Premium efficiency equipment with advanced controls'\r\n      },\r\n      {\r\n        name: 'Standard System',\r\n        initialCostMultiplier: 1.0,\r\n        operatingCostMultiplier: 1.0,\r\n        description: 'Standard efficiency equipment (baseline)'\r\n      },\r\n      {\r\n        name: 'Budget System',\r\n        initialCostMultiplier: 0.8,\r\n        operatingCostMultiplier: 1.2,\r\n        description: 'Lower cost equipment with higher operating costs'\r\n      }\r\n    ];\r\n\r\n    const comparisonResults = alternatives.map(alt => ({\r\n      alternativeName: alt.name,\r\n      description: alt.description,\r\n      totalCost: totalCostOfOwnership.totalPresentValue *\r\n                 ((alt.initialCostMultiplier * 0.3) + (alt.operatingCostMultiplier * 0.7)),\r\n      costDifference: (totalCostOfOwnership.totalPresentValue *\r\n                      ((alt.initialCostMultiplier * 0.3) + (alt.operatingCostMultiplier * 0.7))) -\r\n                     totalCostOfOwnership.totalPresentValue,\r\n      percentageDifference: (((alt.initialCostMultiplier * 0.3) + (alt.operatingCostMultiplier * 0.7)) - 1) * 100\r\n    }));\r\n\r\n    return {\r\n      baselineSystem: {\r\n        name: 'Current System Configuration',\r\n        totalCost: totalCostOfOwnership.totalPresentValue,\r\n        description: 'System as currently configured'\r\n      },\r\n      alternatives: comparisonResults,\r\n      recommendedAlternative: comparisonResults.reduce((min, current) =>\r\n        current.totalCost < min.totalCost ? current : min\r\n      ),\r\n      costRankings: comparisonResults.sort((a, b) => a.totalCost - b.totalCost)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Perform sensitivity analysis\r\n   */\r\n  private static async performSensitivityAnalysis(\r\n    systemConfiguration: SystemConfiguration,\r\n    parameters: CostAnalysisParameters,\r\n    totalCostOfOwnership: TotalCostOfOwnership\r\n  ): Promise<CostSensitivityAnalysis> {\r\n    const baselineCost = totalCostOfOwnership.totalPresentValue;\r\n    const sensitivityFactors = [\r\n      { parameter: 'discountRate', baseValue: parameters.discountRate, variations: [-0.02, -0.01, 0.01, 0.02] },\r\n      { parameter: 'energyEscalationRate', baseValue: parameters.energyEscalationRate, variations: [-0.01, -0.005, 0.005, 0.01] },\r\n      { parameter: 'analysisHorizon', baseValue: parameters.analysisHorizon, variations: [-5, -2, 2, 5] },\r\n      { parameter: 'initialCost', baseValue: 1.0, variations: [-0.2, -0.1, 0.1, 0.2] }\r\n    ];\r\n\r\n    const sensitivityResults = sensitivityFactors.map(factor => {\r\n      const impacts = factor.variations.map(variation => {\r\n        let adjustedCost = baselineCost;\r\n\r\n        // Simplified sensitivity calculation\r\n        if (factor.parameter === 'discountRate') {\r\n          const newRate = factor.baseValue + variation;\r\n          adjustedCost = baselineCost * (parameters.discountRate / newRate);\r\n        } else if (factor.parameter === 'energyEscalationRate') {\r\n          adjustedCost = baselineCost * (1 + variation * 2); // Simplified impact\r\n        } else if (factor.parameter === 'analysisHorizon') {\r\n          adjustedCost = baselineCost * (1 + variation * 0.02); // Simplified impact\r\n        } else if (factor.parameter === 'initialCost') {\r\n          adjustedCost = baselineCost * (1 + variation * 0.3); // 30% of total is initial cost\r\n        }\r\n\r\n        return {\r\n          variation,\r\n          adjustedValue: factor.baseValue + variation,\r\n          resultingCost: adjustedCost,\r\n          costChange: adjustedCost - baselineCost,\r\n          percentageChange: ((adjustedCost - baselineCost) / baselineCost) * 100\r\n        };\r\n      });\r\n\r\n      return {\r\n        parameter: factor.parameter,\r\n        baseValue: factor.baseValue,\r\n        impacts,\r\n        sensitivity: Math.max(...impacts.map(i => Math.abs(i.percentageChange))) /\r\n                    Math.max(...factor.variations.map(v => Math.abs(v)))\r\n      };\r\n    });\r\n\r\n    // Identify most sensitive parameters\r\n    const rankedSensitivities = sensitivityResults\r\n      .sort((a, b) => b.sensitivity - a.sensitivity)\r\n      .map((result, index) => {\r\n        let impact: string;\r\n        if (result.sensitivity > 2) {\r\n          impact = 'High';\r\n        } else if (result.sensitivity > 1) {\r\n          impact = 'Medium';\r\n        } else {\r\n          impact = 'Low';\r\n        }\r\n\r\n        return {\r\n          rank: index + 1,\r\n          parameter: result.parameter,\r\n          sensitivity: result.sensitivity,\r\n          impact\r\n        };\r\n      });\r\n\r\n    return {\r\n      baselineCost,\r\n      sensitivityResults,\r\n      rankedSensitivities,\r\n      keyFindings: [\r\n        `Most sensitive parameter: ${rankedSensitivities[0].parameter}`,\r\n        `Cost range: $${Math.min(...sensitivityResults.flatMap(r => r.impacts.map(i => i.resultingCost))).toLocaleString()} - $${Math.max(...sensitivityResults.flatMap(r => r.impacts.map(i => i.resultingCost))).toLocaleString()}`,\r\n        `Sensitivity analysis shows ${rankedSensitivities.filter(r => r.impact === 'High').length} high-impact parameters`\r\n      ]\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Generate cost recommendations\r\n   */\r\n  private static async generateCostRecommendations(\r\n    totalCostOfOwnership: TotalCostOfOwnership,\r\n    sensitivityAnalysis: CostSensitivityAnalysis,\r\n    costComparison: CostComparison\r\n  ): Promise<CostRecommendation[]> {\r\n    const recommendations: CostRecommendation[] = [];\r\n\r\n    // Operating cost optimization\r\n    if (totalCostOfOwnership.costBreakdown.operatingCosts.percentage > 60) {\r\n      recommendations.push({\r\n        id: 'operating_cost_optimization',\r\n        category: 'Operating Cost Reduction',\r\n        priority: 'High',\r\n        title: 'Focus on Operating Cost Reduction',\r\n        description: 'Operating costs represent a significant portion of total lifecycle costs. Focus on energy efficiency improvements.',\r\n        potentialSavings: totalCostOfOwnership.totalPresentValue * 0.15,\r\n        implementationCost: totalCostOfOwnership.costBreakdown.initialCosts.amount * 0.1,\r\n        paybackPeriod: 3.5,\r\n        actions: [\r\n          'Implement advanced control strategies',\r\n          'Upgrade to high-efficiency equipment',\r\n          'Optimize system operation schedules',\r\n          'Consider demand response programs'\r\n        ],\r\n        riskLevel: 'Low'\r\n      });\r\n    }\r\n\r\n    // Maintenance cost optimization\r\n    if (totalCostOfOwnership.costBreakdown.maintenanceCosts.percentage > 25) {\r\n      recommendations.push({\r\n        id: 'maintenance_optimization',\r\n        category: 'Maintenance Cost Reduction',\r\n        priority: 'Medium',\r\n        title: 'Optimize Maintenance Strategy',\r\n        description: 'Maintenance costs are higher than typical. Consider predictive maintenance and equipment upgrades.',\r\n        potentialSavings: totalCostOfOwnership.totalPresentValue * 0.08,\r\n        implementationCost: 15000,\r\n        paybackPeriod: 4.2,\r\n        actions: [\r\n          'Implement predictive maintenance program',\r\n          'Upgrade to more reliable equipment',\r\n          'Train maintenance staff',\r\n          'Establish preventive maintenance schedules'\r\n        ],\r\n        riskLevel: 'Medium'\r\n      });\r\n    }\r\n\r\n    // Alternative system recommendation\r\n    const bestAlternative = costComparison.recommendedAlternative;\r\n    if (bestAlternative.costDifference < -10000) {\r\n      recommendations.push({\r\n        id: 'alternative_system',\r\n        category: 'System Alternative',\r\n        priority: 'High',\r\n        title: `Consider ${bestAlternative.alternativeName}`,\r\n        description: bestAlternative.description,\r\n        potentialSavings: Math.abs(bestAlternative.costDifference),\r\n        implementationCost: 0, // Alternative, not additional cost\r\n        paybackPeriod: 0,\r\n        actions: [\r\n          'Evaluate alternative system configuration',\r\n          'Perform detailed feasibility study',\r\n          'Consider phased implementation'\r\n        ],\r\n        riskLevel: 'Medium'\r\n      });\r\n    }\r\n\r\n    return recommendations;\r\n  }\r\n\r\n  /**\r\n   * Generate unique analysis ID\r\n   */\r\n  private static generateAnalysisId(systemId: string): string {\r\n    const timestamp = Date.now();\r\n    const random = Math.random().toString(36).substring(2, 8);\r\n    return `cost_analysis_${systemId}_${timestamp}_${random}`;\r\n  }\r\n}\r\n\r\n// Supporting interfaces\r\ninterface EquipmentReplacement {\r\n  equipmentType: string;\r\n  replacementYears: number[];\r\n  unitCost: number;\r\n  totalPresentValue: number;\r\n  description: string;\r\n}\r\n\r\ninterface InstallationCosts {\r\n  fans: number;\r\n  ductwork: number;\r\n  fittings: number;\r\n  dampers: number;\r\n  controls: number;\r\n  accessories: number;\r\n  laborCosts: number;\r\n  materialsCosts: number;\r\n  equipmentRental: number;\r\n  testing: number;\r\n  total: number;\r\n}\r\n\r\ninterface DesignCosts {\r\n  engineeringDesign: number;\r\n  drawings: number;\r\n  calculations: number;\r\n  projectManagement: number;\r\n  total: number;\r\n}\r\n\r\ninterface PermitsCosts {\r\n  buildingPermits: number;\r\n  inspectionFees: number;\r\n  utilityConnections: number;\r\n  environmentalFees: number;\r\n  total: number;\r\n}\r\n\r\ninterface FinancialMetrics {\r\n  netPresentValue: number;\r\n  internalRateOfReturn: number;\r\n  paybackPeriod: number;\r\n  discountedPaybackPeriod: number;\r\n  profitabilityIndex: number;\r\n  returnOnInvestment: number;\r\n}\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "3ba42157d0696b3cb633fc65a330e5b2244352bb"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_ocw6unnfw = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_ocw6unnfw();
cov_ocw6unnfw().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_ocw6unnfw().s[1]++;
exports.LifecycleCostAnalysisEngine = void 0;
const SystemAnalysisTypes_1 =
/* istanbul ignore next */
(cov_ocw6unnfw().s[2]++, require("./types/SystemAnalysisTypes"));
/**
 * Lifecycle Cost Analysis Engine
 *
 * Provides comprehensive lifecycle cost analysis capabilities including:
 * - Initial capital cost analysis
 * - Operating cost projections
 * - Maintenance cost modeling
 * - Replacement cost planning
 * - Total cost of ownership calculations
 * - Cost comparison and sensitivity analysis
 * - Financial metrics (NPV, IRR, Payback)
 */
class LifecycleCostAnalysisEngine {
  /**
   * Perform comprehensive lifecycle cost analysis
   */
  static async analyzeLifecycleCosts(systemConfiguration, energyAnalysis, analysisParameters) {
    /* istanbul ignore next */
    cov_ocw6unnfw().f[0]++;
    cov_ocw6unnfw().s[3]++;
    try {
      const analysisId =
      /* istanbul ignore next */
      (cov_ocw6unnfw().s[4]++, this.generateAnalysisId(systemConfiguration.id));
      const timestamp =
      /* istanbul ignore next */
      (cov_ocw6unnfw().s[5]++, new Date());
      // Set default analysis parameters
      const parameters =
      /* istanbul ignore next */
      (cov_ocw6unnfw().s[6]++, this.setDefaultParameters(analysisParameters));
      // Calculate initial costs
      const initialCosts =
      /* istanbul ignore next */
      (cov_ocw6unnfw().s[7]++, await this.calculateInitialCosts(systemConfiguration));
      // Calculate operating costs
      const operatingCosts =
      /* istanbul ignore next */
      (cov_ocw6unnfw().s[8]++, await this.calculateOperatingCosts(systemConfiguration, energyAnalysis, parameters));
      // Calculate maintenance costs
      const maintenanceCosts =
      /* istanbul ignore next */
      (cov_ocw6unnfw().s[9]++, await this.calculateMaintenanceCosts(systemConfiguration, parameters));
      // Calculate replacement costs
      const replacementCosts =
      /* istanbul ignore next */
      (cov_ocw6unnfw().s[10]++, await this.calculateReplacementCosts(systemConfiguration, parameters));
      // Calculate total cost of ownership
      const totalCostOfOwnership =
      /* istanbul ignore next */
      (cov_ocw6unnfw().s[11]++, await this.calculateTotalCostOfOwnership(initialCosts, operatingCosts, maintenanceCosts, replacementCosts, parameters));
      // Perform cost comparison
      const costComparison =
      /* istanbul ignore next */
      (cov_ocw6unnfw().s[12]++, await this.performCostComparison(systemConfiguration, totalCostOfOwnership));
      // Perform sensitivity analysis
      const sensitivityAnalysis =
      /* istanbul ignore next */
      (cov_ocw6unnfw().s[13]++, await this.performSensitivityAnalysis(systemConfiguration, parameters, totalCostOfOwnership));
      // Generate cost recommendations
      const recommendations =
      /* istanbul ignore next */
      (cov_ocw6unnfw().s[14]++, await this.generateCostRecommendations(totalCostOfOwnership, sensitivityAnalysis, costComparison));
      const analysis =
      /* istanbul ignore next */
      (cov_ocw6unnfw().s[15]++, {
        id: analysisId,
        systemId: systemConfiguration.id,
        analysisTimestamp: timestamp,
        analysisParameters: parameters,
        initialCosts,
        operatingCosts,
        maintenanceCosts,
        replacementCosts,
        totalCostOfOwnership,
        costComparison,
        sensitivityAnalysis,
        recommendations
      });
      // Cache the analysis
      /* istanbul ignore next */
      cov_ocw6unnfw().s[16]++;
      this.COST_CACHE.set(analysisId, analysis);
      /* istanbul ignore next */
      cov_ocw6unnfw().s[17]++;
      return analysis;
    } catch (error) {
      /* istanbul ignore next */
      cov_ocw6unnfw().s[18]++;
      throw new Error(`Lifecycle cost analysis failed: ${error instanceof Error ?
      /* istanbul ignore next */
      (cov_ocw6unnfw().b[0][0]++, error.message) :
      /* istanbul ignore next */
      (cov_ocw6unnfw().b[0][1]++, 'Unknown error')}`);
    }
  }
  /**
   * Set default analysis parameters
   */
  static setDefaultParameters(provided) {
    /* istanbul ignore next */
    cov_ocw6unnfw().f[1]++;
    cov_ocw6unnfw().s[19]++;
    return {
      analysisHorizon:
      /* istanbul ignore next */
      (cov_ocw6unnfw().b[1][0]++, provided?.analysisHorizon) ||
      /* istanbul ignore next */
      (cov_ocw6unnfw().b[1][1]++, 20),
      // 20 years
      discountRate:
      /* istanbul ignore next */
      (cov_ocw6unnfw().b[2][0]++, provided?.discountRate) ||
      /* istanbul ignore next */
      (cov_ocw6unnfw().b[2][1]++, 0.06),
      // 6% discount rate
      inflationRate:
      /* istanbul ignore next */
      (cov_ocw6unnfw().b[3][0]++, provided?.inflationRate) ||
      /* istanbul ignore next */
      (cov_ocw6unnfw().b[3][1]++, 0.025),
      // 2.5% inflation
      energyEscalationRate:
      /* istanbul ignore next */
      (cov_ocw6unnfw().b[4][0]++, provided?.energyEscalationRate) ||
      /* istanbul ignore next */
      (cov_ocw6unnfw().b[4][1]++, this.DEFAULT_ESCALATION_RATES.ENERGY),
      currency:
      /* istanbul ignore next */
      (cov_ocw6unnfw().b[5][0]++, provided?.currency) ||
      /* istanbul ignore next */
      (cov_ocw6unnfw().b[5][1]++, 'USD'),
      analysisMethod:
      /* istanbul ignore next */
      (cov_ocw6unnfw().b[6][0]++, provided?.analysisMethod) ||
      /* istanbul ignore next */
      (cov_ocw6unnfw().b[6][1]++, SystemAnalysisTypes_1.CostAnalysisMethod.NET_PRESENT_VALUE),
      uncertaintyLevel:
      /* istanbul ignore next */
      (cov_ocw6unnfw().b[7][0]++, provided?.uncertaintyLevel) ||
      /* istanbul ignore next */
      (cov_ocw6unnfw().b[7][1]++, SystemAnalysisTypes_1.UncertaintyLevel.MEDIUM)
    };
  }
  /**
   * Calculate initial system costs
   */
  static async calculateInitialCosts(systemConfiguration) {
    /* istanbul ignore next */
    cov_ocw6unnfw().f[2]++;
    const designAirflow =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[20]++, systemConfiguration.designParameters.designAirflow);
    const systemType =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[21]++, systemConfiguration.systemType);
    // Equipment costs based on system size and type
    const equipmentCosts =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[22]++, this.calculateEquipmentCosts(systemConfiguration));
    // Installation costs (typically 40-60% of equipment costs)
    const installationCosts =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[23]++, this.calculateInstallationCosts(equipmentCosts));
    // Design costs (typically 8-12% of total project cost)
    const designCosts =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[24]++, this.calculateDesignCosts(equipmentCosts, installationCosts));
    // Permits and fees (typically 2-5% of total project cost)
    const permitsCosts =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[25]++, this.calculatePermitsCosts(equipmentCosts, installationCosts));
    const totalInitialCost =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[26]++, equipmentCosts.total + installationCosts.total + designCosts.total + permitsCosts.total);
    /* istanbul ignore next */
    cov_ocw6unnfw().s[27]++;
    return {
      equipmentCosts,
      installationCosts,
      designCosts,
      permitsCosts,
      totalInitialCost,
      costPerCFM: totalInitialCost / designAirflow,
      costPerSquareFoot: totalInitialCost / 10000 // Assumed building area
    };
  }
  /**
   * Calculate equipment costs breakdown
   */
  static calculateEquipmentCosts(systemConfiguration) {
    /* istanbul ignore next */
    cov_ocw6unnfw().f[3]++;
    const designAirflow =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[28]++, systemConfiguration.designParameters.designAirflow);
    const designPressure =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[29]++, systemConfiguration.designParameters.designPressure);
    // Cost factors based on system size ($/CFM)
    const fanCostFactor =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[30]++, this.getFanCostFactor(designAirflow, designPressure));
    const ductworkCostFactor =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[31]++, 8.5); // $/CFM for ductwork
    const fittingsCostFactor =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[32]++, 2.2); // $/CFM for fittings
    const dampersCostFactor =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[33]++, 1.8); // $/CFM for dampers
    const controlsCostFactor =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[34]++, 3.5); // $/CFM for controls
    const accessoriesCostFactor =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[35]++, 1.2); // $/CFM for accessories
    const fans =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[36]++, designAirflow * fanCostFactor);
    const ductwork =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[37]++, designAirflow * ductworkCostFactor);
    const fittings =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[38]++, designAirflow * fittingsCostFactor);
    const dampers =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[39]++, designAirflow * dampersCostFactor);
    const controls =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[40]++, designAirflow * controlsCostFactor);
    const accessories =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[41]++, designAirflow * accessoriesCostFactor);
    /* istanbul ignore next */
    cov_ocw6unnfw().s[42]++;
    return {
      fans,
      ductwork,
      fittings,
      dampers,
      controls,
      accessories,
      total: fans + ductwork + fittings + dampers + controls + accessories
    };
  }
  /**
   * Get fan cost factor based on size and pressure
   */
  static getFanCostFactor(airflow, pressure) {
    /* istanbul ignore next */
    cov_ocw6unnfw().f[4]++;
    // Base cost factor
    let costFactor =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[43]++, 12.0); // $/CFM base
    // Size adjustments
    /* istanbul ignore next */
    cov_ocw6unnfw().s[44]++;
    if (airflow > 50000) {
      /* istanbul ignore next */
      cov_ocw6unnfw().b[8][0]++;
      cov_ocw6unnfw().s[45]++;
      costFactor *= 0.85;
    } // Economies of scale
    else {
      /* istanbul ignore next */
      cov_ocw6unnfw().b[8][1]++;
      cov_ocw6unnfw().s[46]++;
      if (airflow < 5000) {
        /* istanbul ignore next */
        cov_ocw6unnfw().b[9][0]++;
        cov_ocw6unnfw().s[47]++;
        costFactor *= 1.25;
      } else
      /* istanbul ignore next */
      {
        cov_ocw6unnfw().b[9][1]++;
      }
    } // Small system premium
    // Pressure adjustments
    /* istanbul ignore next */
    cov_ocw6unnfw().s[48]++;
    if (pressure > 4.0) {
      /* istanbul ignore next */
      cov_ocw6unnfw().b[10][0]++;
      cov_ocw6unnfw().s[49]++;
      costFactor *= 1.3;
    } // High pressure premium
    else {
      /* istanbul ignore next */
      cov_ocw6unnfw().b[10][1]++;
      cov_ocw6unnfw().s[50]++;
      if (pressure < 2.0) {
        /* istanbul ignore next */
        cov_ocw6unnfw().b[11][0]++;
        cov_ocw6unnfw().s[51]++;
        costFactor *= 0.9;
      } else
      /* istanbul ignore next */
      {
        cov_ocw6unnfw().b[11][1]++;
      }
    } // Low pressure discount
    /* istanbul ignore next */
    cov_ocw6unnfw().s[52]++;
    return costFactor;
  }
  /**
   * Calculate installation costs
   */
  static calculateInstallationCosts(equipmentCosts) {
    /* istanbul ignore next */
    cov_ocw6unnfw().f[5]++;
    // Installation cost factors (as percentage of equipment cost)
    const factors =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[53]++, {
      fans: 0.4,
      // 40% of fan cost
      ductwork: 0.6,
      // 60% of ductwork cost
      fittings: 0.5,
      // 50% of fittings cost
      dampers: 0.3,
      // 30% of dampers cost
      controls: 0.8,
      // 80% of controls cost (complex installation)
      accessories: 0.4 // 40% of accessories cost
    });
    const fans =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[54]++, equipmentCosts.fans * factors.fans);
    const ductwork =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[55]++, equipmentCosts.ductwork * factors.ductwork);
    const fittings =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[56]++, equipmentCosts.fittings * factors.fittings);
    const dampers =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[57]++, equipmentCosts.dampers * factors.dampers);
    const controls =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[58]++, equipmentCosts.controls * factors.controls);
    const accessories =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[59]++, equipmentCosts.accessories * factors.accessories);
    // Additional installation costs
    const laborCosts =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[60]++, (fans + ductwork + fittings + dampers + controls + accessories) * 0.6);
    const materialsCosts =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[61]++, equipmentCosts.total * 0.15); // 15% for installation materials
    const equipmentRental =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[62]++, equipmentCosts.total * 0.05); // 5% for equipment rental
    const testing =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[63]++, equipmentCosts.total * 0.03); // 3% for testing and commissioning
    const total =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[64]++, fans + ductwork + fittings + dampers + controls + accessories + laborCosts + materialsCosts + equipmentRental + testing);
    /* istanbul ignore next */
    cov_ocw6unnfw().s[65]++;
    return {
      fans,
      ductwork,
      fittings,
      dampers,
      controls,
      accessories,
      laborCosts,
      materialsCosts,
      equipmentRental,
      testing,
      total
    };
  }
  /**
   * Calculate design costs
   */
  static calculateDesignCosts(equipmentCosts, installationCosts) {
    /* istanbul ignore next */
    cov_ocw6unnfw().f[6]++;
    const projectCost =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[66]++, equipmentCosts.total + installationCosts.total);
    const engineeringDesign =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[67]++, projectCost * 0.08); // 8% for engineering design
    const drawings =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[68]++, projectCost * 0.02); // 2% for drawings and specifications
    const calculations =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[69]++, projectCost * 0.015); // 1.5% for calculations
    const projectManagement =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[70]++, projectCost * 0.025); // 2.5% for project management
    /* istanbul ignore next */
    cov_ocw6unnfw().s[71]++;
    return {
      engineeringDesign,
      drawings,
      calculations,
      projectManagement,
      total: engineeringDesign + drawings + calculations + projectManagement
    };
  }
  /**
   * Calculate permits and fees
   */
  static calculatePermitsCosts(equipmentCosts, installationCosts) {
    /* istanbul ignore next */
    cov_ocw6unnfw().f[7]++;
    const projectCost =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[72]++, equipmentCosts.total + installationCosts.total);
    const buildingPermits =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[73]++, projectCost * 0.015); // 1.5% for building permits
    const inspectionFees =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[74]++, projectCost * 0.008); // 0.8% for inspection fees
    const utilityConnections =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[75]++, 2500); // Fixed cost for utility connections
    const environmentalFees =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[76]++, projectCost * 0.005); // 0.5% for environmental fees
    /* istanbul ignore next */
    cov_ocw6unnfw().s[77]++;
    return {
      buildingPermits,
      inspectionFees,
      utilityConnections,
      environmentalFees,
      total: buildingPermits + inspectionFees + utilityConnections + environmentalFees
    };
  }
  /**
   * Calculate operating costs over analysis horizon
   */
  static async calculateOperatingCosts(systemConfiguration, energyAnalysis, parameters) {
    /* istanbul ignore next */
    cov_ocw6unnfw().f[8]++;
    const analysisHorizon =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[78]++, parameters.analysisHorizon);
    const energyEscalationRate =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[79]++, parameters.energyEscalationRate);
    const discountRate =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[80]++, parameters.discountRate);
    // Annual energy costs
    const annualEnergyCosts =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[81]++, energyAnalysis.energyCosts.currentCosts.totalCost);
    // Calculate present value of energy costs over analysis horizon
    let totalEnergyPV =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[82]++, 0);
    const yearlyEnergyCosts =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[83]++, []);
    /* istanbul ignore next */
    cov_ocw6unnfw().s[84]++;
    for (let year =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[85]++, 1); year <= analysisHorizon; year++) {
      const yearlyEnergyCost =
      /* istanbul ignore next */
      (cov_ocw6unnfw().s[86]++, annualEnergyCosts * Math.pow(1 + energyEscalationRate, year - 1));
      const presentValue =
      /* istanbul ignore next */
      (cov_ocw6unnfw().s[87]++, yearlyEnergyCost / Math.pow(1 + discountRate, year));
      /* istanbul ignore next */
      cov_ocw6unnfw().s[88]++;
      totalEnergyPV += presentValue;
      /* istanbul ignore next */
      cov_ocw6unnfw().s[89]++;
      yearlyEnergyCosts.push(yearlyEnergyCost);
    }
    // Other operating costs
    const annualInsurance =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[90]++, annualEnergyCosts * 0.02); // 2% of energy costs
    const annualUtilities =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[91]++, 1200); // Fixed annual utilities
    const annualCompliance =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[92]++, 800); // Annual compliance and reporting costs
    // Calculate present values for other operating costs
    const insurancePV =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[93]++, this.calculatePresentValue(annualInsurance, parameters.inflationRate, discountRate, analysisHorizon));
    const utilitiesPV =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[94]++, this.calculatePresentValue(annualUtilities, parameters.inflationRate, discountRate, analysisHorizon));
    const compliancePV =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[95]++, this.calculatePresentValue(annualCompliance, parameters.inflationRate, discountRate, analysisHorizon));
    const totalOperatingPV =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[96]++, totalEnergyPV + insurancePV + utilitiesPV + compliancePV);
    /* istanbul ignore next */
    cov_ocw6unnfw().s[97]++;
    return {
      energyCosts: {
        annual: annualEnergyCosts,
        presentValue: totalEnergyPV,
        yearlyProjection: yearlyEnergyCosts,
        escalationRate: energyEscalationRate
      },
      maintenanceCosts: {
        annual: 0,
        // Calculated separately
        presentValue: 0,
        yearlyProjection: [],
        escalationRate: this.DEFAULT_ESCALATION_RATES.MAINTENANCE
      },
      insuranceCosts: {
        annual: annualInsurance,
        presentValue: insurancePV,
        escalationRate: parameters.inflationRate
      },
      utilityCosts: {
        annual: annualUtilities,
        presentValue: utilitiesPV,
        escalationRate: parameters.inflationRate
      },
      complianceCosts: {
        annual: annualCompliance,
        presentValue: compliancePV,
        escalationRate: parameters.inflationRate
      },
      totalAnnual: annualEnergyCosts + annualInsurance + annualUtilities + annualCompliance,
      totalPresentValue: totalOperatingPV
    };
  }
  /**
   * Calculate present value of annual costs
   */
  static calculatePresentValue(annualCost, escalationRate, discountRate, years) {
    /* istanbul ignore next */
    cov_ocw6unnfw().f[9]++;
    let presentValue =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[98]++, 0);
    /* istanbul ignore next */
    cov_ocw6unnfw().s[99]++;
    for (let year =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[100]++, 1); year <= years; year++) {
      const yearlyValue =
      /* istanbul ignore next */
      (cov_ocw6unnfw().s[101]++, annualCost * Math.pow(1 + escalationRate, year - 1));
      /* istanbul ignore next */
      cov_ocw6unnfw().s[102]++;
      presentValue += yearlyValue / Math.pow(1 + discountRate, year);
    }
    /* istanbul ignore next */
    cov_ocw6unnfw().s[103]++;
    return presentValue;
  }
  /**
   * Calculate maintenance costs over analysis horizon
   */
  static async calculateMaintenanceCosts(systemConfiguration, parameters) {
    /* istanbul ignore next */
    cov_ocw6unnfw().f[10]++;
    const designAirflow =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[104]++, systemConfiguration.designParameters.designAirflow);
    const analysisHorizon =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[105]++, parameters.analysisHorizon);
    const discountRate =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[106]++, parameters.discountRate);
    const maintenanceEscalationRate =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[107]++, this.DEFAULT_ESCALATION_RATES.MAINTENANCE);
    // Annual maintenance costs ($/CFM/year)
    const preventiveMaintenanceFactor =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[108]++, 0.8); // $/CFM/year
    const correctiveMaintenanceFactor =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[109]++, 0.4); // $/CFM/year
    const filterReplacementFactor =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[110]++, 0.6); // $/CFM/year
    const annualPreventive =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[111]++, designAirflow * preventiveMaintenanceFactor);
    const annualCorrective =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[112]++, designAirflow * correctiveMaintenanceFactor);
    const annualFilters =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[113]++, designAirflow * filterReplacementFactor);
    // Calculate present values
    const preventivePV =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[114]++, this.calculatePresentValue(annualPreventive, maintenanceEscalationRate, discountRate, analysisHorizon));
    const correctivePV =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[115]++, this.calculatePresentValue(annualCorrective, maintenanceEscalationRate, discountRate, analysisHorizon));
    const filtersPV =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[116]++, this.calculatePresentValue(annualFilters, maintenanceEscalationRate, discountRate, analysisHorizon));
    // Major overhauls (every 10 years)
    const majorOverhaulCost =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[117]++, designAirflow * 5.0); // $5/CFM
    const overhaulYears =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[118]++, [10, 20].filter(year => {
      /* istanbul ignore next */
      cov_ocw6unnfw().f[11]++;
      cov_ocw6unnfw().s[119]++;
      return year <= analysisHorizon;
    }));
    let overhaulsPV =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[120]++, 0);
    /* istanbul ignore next */
    cov_ocw6unnfw().s[121]++;
    overhaulYears.forEach(year => {
      /* istanbul ignore next */
      cov_ocw6unnfw().f[12]++;
      const overhaulCostInflated =
      /* istanbul ignore next */
      (cov_ocw6unnfw().s[122]++, majorOverhaulCost * Math.pow(1 + maintenanceEscalationRate, year - 1));
      /* istanbul ignore next */
      cov_ocw6unnfw().s[123]++;
      overhaulsPV += overhaulCostInflated / Math.pow(1 + discountRate, year);
    });
    const totalAnnual =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[124]++, annualPreventive + annualCorrective + annualFilters);
    const totalPresentValue =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[125]++, preventivePV + correctivePV + filtersPV + overhaulsPV);
    /* istanbul ignore next */
    cov_ocw6unnfw().s[126]++;
    return {
      preventiveMaintenance: {
        annual: annualPreventive,
        presentValue: preventivePV,
        description: 'Regular inspections, cleaning, and adjustments'
      },
      correctiveMaintenance: {
        annual: annualCorrective,
        presentValue: correctivePV,
        description: 'Repairs and component replacements'
      },
      filterReplacement: {
        annual: annualFilters,
        presentValue: filtersPV,
        description: 'Regular filter replacements'
      },
      majorOverhauls: {
        cost: majorOverhaulCost,
        presentValue: overhaulsPV,
        schedule: overhaulYears,
        description: 'Major system overhauls and upgrades'
      },
      totalAnnual,
      totalPresentValue,
      escalationRate: maintenanceEscalationRate
    };
  }
  /**
   * Calculate replacement costs over analysis horizon
   */
  static async calculateReplacementCosts(systemConfiguration, parameters) {
    /* istanbul ignore next */
    cov_ocw6unnfw().f[13]++;
    const analysisHorizon =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[127]++, parameters.analysisHorizon);
    const discountRate =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[128]++, parameters.discountRate);
    const materialEscalationRate =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[129]++, this.DEFAULT_ESCALATION_RATES.MATERIALS);
    const equipmentReplacements =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[130]++, []);
    let totalReplacementPV =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[131]++, 0);
    // Calculate replacement costs for each equipment type
    const equipmentTypes =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[132]++, [{
      type: 'fans',
      life: this.EQUIPMENT_LIFE.FANS,
      costFactor: 12.0
    }, {
      type: 'motors',
      life: this.EQUIPMENT_LIFE.MOTORS,
      costFactor: 3.5
    }, {
      type: 'vfd',
      life: this.EQUIPMENT_LIFE.VFD,
      costFactor: 2.8
    }, {
      type: 'dampers',
      life: this.EQUIPMENT_LIFE.DAMPERS,
      costFactor: 1.8
    }, {
      type: 'controls',
      life: this.EQUIPMENT_LIFE.CONTROLS,
      costFactor: 3.5
    }]);
    const designAirflow =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[133]++, systemConfiguration.designParameters.designAirflow);
    /* istanbul ignore next */
    cov_ocw6unnfw().s[134]++;
    equipmentTypes.forEach(equipment => {
      /* istanbul ignore next */
      cov_ocw6unnfw().f[14]++;
      const replacementYears =
      /* istanbul ignore next */
      (cov_ocw6unnfw().s[135]++, []);
      /* istanbul ignore next */
      cov_ocw6unnfw().s[136]++;
      for (let year =
      /* istanbul ignore next */
      (cov_ocw6unnfw().s[137]++, equipment.life); year <= analysisHorizon; year += equipment.life) {
        /* istanbul ignore next */
        cov_ocw6unnfw().s[138]++;
        replacementYears.push(year);
      }
      /* istanbul ignore next */
      cov_ocw6unnfw().s[139]++;
      if (replacementYears.length > 0) {
        /* istanbul ignore next */
        cov_ocw6unnfw().b[12][0]++;
        const replacementCost =
        /* istanbul ignore next */
        (cov_ocw6unnfw().s[140]++, designAirflow * equipment.costFactor);
        let equipmentPV =
        /* istanbul ignore next */
        (cov_ocw6unnfw().s[141]++, 0);
        /* istanbul ignore next */
        cov_ocw6unnfw().s[142]++;
        replacementYears.forEach(year => {
          /* istanbul ignore next */
          cov_ocw6unnfw().f[15]++;
          const inflatedCost =
          /* istanbul ignore next */
          (cov_ocw6unnfw().s[143]++, replacementCost * Math.pow(1 + materialEscalationRate, year - 1));
          const presentValue =
          /* istanbul ignore next */
          (cov_ocw6unnfw().s[144]++, inflatedCost / Math.pow(1 + discountRate, year));
          /* istanbul ignore next */
          cov_ocw6unnfw().s[145]++;
          equipmentPV += presentValue;
        });
        /* istanbul ignore next */
        cov_ocw6unnfw().s[146]++;
        equipmentReplacements.push({
          equipmentType: equipment.type,
          replacementYears,
          unitCost: replacementCost,
          totalPresentValue: equipmentPV,
          description: `${equipment.type} replacement based on ${equipment.life}-year life expectancy`
        });
        /* istanbul ignore next */
        cov_ocw6unnfw().s[147]++;
        totalReplacementPV += equipmentPV;
      } else
      /* istanbul ignore next */
      {
        cov_ocw6unnfw().b[12][1]++;
      }
    });
    // Salvage value at end of analysis period
    const salvageValue =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[148]++, this.calculateSalvageValue(systemConfiguration, parameters));
    const salvagePV =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[149]++, salvageValue / Math.pow(1 + discountRate, analysisHorizon));
    /* istanbul ignore next */
    cov_ocw6unnfw().s[150]++;
    return {
      equipmentReplacements,
      salvageValue: {
        value: salvageValue,
        presentValue: salvagePV,
        description: 'Estimated salvage value at end of analysis period'
      },
      totalReplacementCost: totalReplacementPV,
      netReplacementCost: totalReplacementPV - salvagePV
    };
  }
  /**
   * Calculate salvage value at end of analysis period
   */
  static calculateSalvageValue(systemConfiguration, parameters) {
    /* istanbul ignore next */
    cov_ocw6unnfw().f[16]++;
    const designAirflow =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[151]++, systemConfiguration.designParameters.designAirflow);
    const analysisHorizon =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[152]++, parameters.analysisHorizon);
    // Calculate remaining life for each equipment type
    const ductworkRemainingLife =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[153]++, Math.max(0, this.EQUIPMENT_LIFE.DUCTWORK - analysisHorizon));
    const fansRemainingLife =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[154]++, Math.max(0, this.EQUIPMENT_LIFE.FANS - analysisHorizon % this.EQUIPMENT_LIFE.FANS));
    const dampersRemainingLife =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[155]++, Math.max(0, this.EQUIPMENT_LIFE.DAMPERS - analysisHorizon % this.EQUIPMENT_LIFE.DAMPERS));
    // Calculate salvage value based on remaining useful life
    const ductworkSalvage =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[156]++, designAirflow * 8.5 * (ductworkRemainingLife / this.EQUIPMENT_LIFE.DUCTWORK));
    const fansSalvage =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[157]++, designAirflow * 12.0 * (fansRemainingLife / this.EQUIPMENT_LIFE.FANS));
    const dampersSalvage =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[158]++, designAirflow * 1.8 * (dampersRemainingLife / this.EQUIPMENT_LIFE.DAMPERS));
    /* istanbul ignore next */
    cov_ocw6unnfw().s[159]++;
    return ductworkSalvage + fansSalvage + dampersSalvage;
  }
  /**
   * Calculate total cost of ownership
   */
  static async calculateTotalCostOfOwnership(initialCosts, operatingCosts, maintenanceCosts, replacementCosts, parameters) {
    /* istanbul ignore next */
    cov_ocw6unnfw().f[17]++;
    const totalPresentValue =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[160]++, initialCosts.totalInitialCost + operatingCosts.totalPresentValue + maintenanceCosts.totalPresentValue + replacementCosts.netReplacementCost);
    const totalAnnualizedCost =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[161]++, this.calculateAnnualizedCost(totalPresentValue, parameters));
    // Cost breakdown by category
    const costBreakdown =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[162]++, {
      initialCosts: {
        amount: initialCosts.totalInitialCost,
        percentage: initialCosts.totalInitialCost / totalPresentValue * 100
      },
      operatingCosts: {
        amount: operatingCosts.totalPresentValue,
        percentage: operatingCosts.totalPresentValue / totalPresentValue * 100
      },
      maintenanceCosts: {
        amount: maintenanceCosts.totalPresentValue,
        percentage: maintenanceCosts.totalPresentValue / totalPresentValue * 100
      },
      replacementCosts: {
        amount: replacementCosts.netReplacementCost,
        percentage: replacementCosts.netReplacementCost / totalPresentValue * 100
      }
    });
    // Financial metrics
    const financialMetrics =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[163]++, this.calculateFinancialMetrics(initialCosts.totalInitialCost, operatingCosts.energyCosts.annual + maintenanceCosts.totalAnnual, parameters));
    /* istanbul ignore next */
    cov_ocw6unnfw().s[164]++;
    return {
      totalPresentValue,
      totalAnnualizedCost,
      costBreakdown,
      financialMetrics,
      analysisParameters: parameters,
      costPerCFM: totalPresentValue / 10000,
      // Assuming 10,000 CFM system
      costPerSquareFoot: totalPresentValue / 10000,
      // Assuming 10,000 sq ft building
      paybackPeriod: this.calculateSimplePaybackPeriod(initialCosts.totalInitialCost, operatingCosts.energyCosts.annual)
    };
  }
  /**
   * Calculate annualized cost
   */
  static calculateAnnualizedCost(presentValue, parameters) {
    /* istanbul ignore next */
    cov_ocw6unnfw().f[18]++;
    const discountRate =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[165]++, parameters.discountRate);
    const analysisHorizon =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[166]++, parameters.analysisHorizon);
    // Capital recovery factor
    const crf =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[167]++, discountRate * Math.pow(1 + discountRate, analysisHorizon) / (Math.pow(1 + discountRate, analysisHorizon) - 1));
    /* istanbul ignore next */
    cov_ocw6unnfw().s[168]++;
    return presentValue * crf;
  }
  /**
   * Calculate financial metrics
   */
  static calculateFinancialMetrics(initialCost, annualSavings, parameters) {
    /* istanbul ignore next */
    cov_ocw6unnfw().f[19]++;
    const discountRate =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[169]++, parameters.discountRate);
    const analysisHorizon =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[170]++, parameters.analysisHorizon);
    // Net Present Value (assuming savings compared to baseline)
    const npv =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[171]++, this.calculateNPV(initialCost, annualSavings, discountRate, analysisHorizon));
    // Internal Rate of Return (simplified calculation)
    const irr =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[172]++, this.calculateIRR(initialCost, annualSavings, analysisHorizon));
    // Discounted Payback Period
    const discountedPayback =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[173]++, this.calculateDiscountedPayback(initialCost, annualSavings, discountRate));
    // Profitability Index
    const profitabilityIndex =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[174]++, (npv + initialCost) / initialCost);
    /* istanbul ignore next */
    cov_ocw6unnfw().s[175]++;
    return {
      netPresentValue: npv,
      internalRateOfReturn: irr,
      paybackPeriod: this.calculateSimplePaybackPeriod(initialCost, annualSavings),
      discountedPaybackPeriod: discountedPayback,
      profitabilityIndex,
      returnOnInvestment: (annualSavings * analysisHorizon - initialCost) / initialCost * 100
    };
  }
  /**
   * Calculate Net Present Value
   */
  static calculateNPV(initialCost, annualCashFlow, discountRate, years) {
    /* istanbul ignore next */
    cov_ocw6unnfw().f[20]++;
    let npv =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[176]++, -initialCost);
    /* istanbul ignore next */
    cov_ocw6unnfw().s[177]++;
    for (let year =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[178]++, 1); year <= years; year++) {
      /* istanbul ignore next */
      cov_ocw6unnfw().s[179]++;
      npv += annualCashFlow / Math.pow(1 + discountRate, year);
    }
    /* istanbul ignore next */
    cov_ocw6unnfw().s[180]++;
    return npv;
  }
  /**
   * Calculate Internal Rate of Return (simplified)
   */
  static calculateIRR(initialCost, annualCashFlow, years) {
    /* istanbul ignore next */
    cov_ocw6unnfw().f[21]++;
    cov_ocw6unnfw().s[181]++;
    // Simplified IRR calculation using approximation
    if (annualCashFlow <= 0) {
      /* istanbul ignore next */
      cov_ocw6unnfw().b[13][0]++;
      cov_ocw6unnfw().s[182]++;
      return 0;
    } else
    /* istanbul ignore next */
    {
      cov_ocw6unnfw().b[13][1]++;
    }
    const totalCashFlow =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[183]++, annualCashFlow * years);
    /* istanbul ignore next */
    cov_ocw6unnfw().s[184]++;
    if (totalCashFlow <= initialCost) {
      /* istanbul ignore next */
      cov_ocw6unnfw().b[14][0]++;
      cov_ocw6unnfw().s[185]++;
      return 0;
    } else
    /* istanbul ignore next */
    {
      cov_ocw6unnfw().b[14][1]++;
    }
    // Approximation: IRR ≈ (Total Cash Flow / Initial Cost)^(1/years) - 1
    cov_ocw6unnfw().s[186]++;
    return Math.pow(totalCashFlow / initialCost, 1 / years) - 1;
  }
  /**
   * Calculate simple payback period
   */
  static calculateSimplePaybackPeriod(initialCost, annualSavings) {
    /* istanbul ignore next */
    cov_ocw6unnfw().f[22]++;
    cov_ocw6unnfw().s[187]++;
    if (annualSavings <= 0) {
      /* istanbul ignore next */
      cov_ocw6unnfw().b[15][0]++;
      cov_ocw6unnfw().s[188]++;
      return Infinity;
    } else
    /* istanbul ignore next */
    {
      cov_ocw6unnfw().b[15][1]++;
    }
    cov_ocw6unnfw().s[189]++;
    return initialCost / annualSavings;
  }
  /**
   * Calculate discounted payback period
   */
  static calculateDiscountedPayback(initialCost, annualCashFlow, discountRate) {
    /* istanbul ignore next */
    cov_ocw6unnfw().f[23]++;
    let cumulativePV =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[190]++, 0);
    let year =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[191]++, 0);
    /* istanbul ignore next */
    cov_ocw6unnfw().s[192]++;
    while (
    /* istanbul ignore next */
    (cov_ocw6unnfw().b[16][0]++, cumulativePV < initialCost) &&
    /* istanbul ignore next */
    (cov_ocw6unnfw().b[16][1]++, year < 50)) {
      /* istanbul ignore next */
      cov_ocw6unnfw().s[193]++;
      // Max 50 years
      year++;
      /* istanbul ignore next */
      cov_ocw6unnfw().s[194]++;
      cumulativePV += annualCashFlow / Math.pow(1 + discountRate, year);
    }
    /* istanbul ignore next */
    cov_ocw6unnfw().s[195]++;
    return year;
  }
  /**
   * Perform cost comparison with alternatives
   */
  static async performCostComparison(systemConfiguration, totalCostOfOwnership) {
    /* istanbul ignore next */
    cov_ocw6unnfw().f[24]++;
    // Simplified cost comparison with typical alternatives
    const alternatives =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[196]++, [{
      name: 'High Efficiency System',
      initialCostMultiplier: 1.25,
      operatingCostMultiplier: 0.8,
      description: 'Premium efficiency equipment with advanced controls'
    }, {
      name: 'Standard System',
      initialCostMultiplier: 1.0,
      operatingCostMultiplier: 1.0,
      description: 'Standard efficiency equipment (baseline)'
    }, {
      name: 'Budget System',
      initialCostMultiplier: 0.8,
      operatingCostMultiplier: 1.2,
      description: 'Lower cost equipment with higher operating costs'
    }]);
    const comparisonResults =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[197]++, alternatives.map(alt => {
      /* istanbul ignore next */
      cov_ocw6unnfw().f[25]++;
      cov_ocw6unnfw().s[198]++;
      return {
        alternativeName: alt.name,
        description: alt.description,
        totalCost: totalCostOfOwnership.totalPresentValue * (alt.initialCostMultiplier * 0.3 + alt.operatingCostMultiplier * 0.7),
        costDifference: totalCostOfOwnership.totalPresentValue * (alt.initialCostMultiplier * 0.3 + alt.operatingCostMultiplier * 0.7) - totalCostOfOwnership.totalPresentValue,
        percentageDifference: (alt.initialCostMultiplier * 0.3 + alt.operatingCostMultiplier * 0.7 - 1) * 100
      };
    }));
    /* istanbul ignore next */
    cov_ocw6unnfw().s[199]++;
    return {
      baselineSystem: {
        name: 'Current System Configuration',
        totalCost: totalCostOfOwnership.totalPresentValue,
        description: 'System as currently configured'
      },
      alternatives: comparisonResults,
      recommendedAlternative: comparisonResults.reduce((min, current) => {
        /* istanbul ignore next */
        cov_ocw6unnfw().f[26]++;
        cov_ocw6unnfw().s[200]++;
        return current.totalCost < min.totalCost ?
        /* istanbul ignore next */
        (cov_ocw6unnfw().b[17][0]++, current) :
        /* istanbul ignore next */
        (cov_ocw6unnfw().b[17][1]++, min);
      }),
      costRankings: comparisonResults.sort((a, b) => {
        /* istanbul ignore next */
        cov_ocw6unnfw().f[27]++;
        cov_ocw6unnfw().s[201]++;
        return a.totalCost - b.totalCost;
      })
    };
  }
  /**
   * Perform sensitivity analysis
   */
  static async performSensitivityAnalysis(systemConfiguration, parameters, totalCostOfOwnership) {
    /* istanbul ignore next */
    cov_ocw6unnfw().f[28]++;
    const baselineCost =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[202]++, totalCostOfOwnership.totalPresentValue);
    const sensitivityFactors =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[203]++, [{
      parameter: 'discountRate',
      baseValue: parameters.discountRate,
      variations: [-0.02, -0.01, 0.01, 0.02]
    }, {
      parameter: 'energyEscalationRate',
      baseValue: parameters.energyEscalationRate,
      variations: [-0.01, -0.005, 0.005, 0.01]
    }, {
      parameter: 'analysisHorizon',
      baseValue: parameters.analysisHorizon,
      variations: [-5, -2, 2, 5]
    }, {
      parameter: 'initialCost',
      baseValue: 1.0,
      variations: [-0.2, -0.1, 0.1, 0.2]
    }]);
    const sensitivityResults =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[204]++, sensitivityFactors.map(factor => {
      /* istanbul ignore next */
      cov_ocw6unnfw().f[29]++;
      const impacts =
      /* istanbul ignore next */
      (cov_ocw6unnfw().s[205]++, factor.variations.map(variation => {
        /* istanbul ignore next */
        cov_ocw6unnfw().f[30]++;
        let adjustedCost =
        /* istanbul ignore next */
        (cov_ocw6unnfw().s[206]++, baselineCost);
        // Simplified sensitivity calculation
        /* istanbul ignore next */
        cov_ocw6unnfw().s[207]++;
        if (factor.parameter === 'discountRate') {
          /* istanbul ignore next */
          cov_ocw6unnfw().b[18][0]++;
          const newRate =
          /* istanbul ignore next */
          (cov_ocw6unnfw().s[208]++, factor.baseValue + variation);
          /* istanbul ignore next */
          cov_ocw6unnfw().s[209]++;
          adjustedCost = baselineCost * (parameters.discountRate / newRate);
        } else {
          /* istanbul ignore next */
          cov_ocw6unnfw().b[18][1]++;
          cov_ocw6unnfw().s[210]++;
          if (factor.parameter === 'energyEscalationRate') {
            /* istanbul ignore next */
            cov_ocw6unnfw().b[19][0]++;
            cov_ocw6unnfw().s[211]++;
            adjustedCost = baselineCost * (1 + variation * 2); // Simplified impact
          } else {
            /* istanbul ignore next */
            cov_ocw6unnfw().b[19][1]++;
            cov_ocw6unnfw().s[212]++;
            if (factor.parameter === 'analysisHorizon') {
              /* istanbul ignore next */
              cov_ocw6unnfw().b[20][0]++;
              cov_ocw6unnfw().s[213]++;
              adjustedCost = baselineCost * (1 + variation * 0.02); // Simplified impact
            } else {
              /* istanbul ignore next */
              cov_ocw6unnfw().b[20][1]++;
              cov_ocw6unnfw().s[214]++;
              if (factor.parameter === 'initialCost') {
                /* istanbul ignore next */
                cov_ocw6unnfw().b[21][0]++;
                cov_ocw6unnfw().s[215]++;
                adjustedCost = baselineCost * (1 + variation * 0.3); // 30% of total is initial cost
              } else
              /* istanbul ignore next */
              {
                cov_ocw6unnfw().b[21][1]++;
              }
            }
          }
        }
        /* istanbul ignore next */
        cov_ocw6unnfw().s[216]++;
        return {
          variation,
          adjustedValue: factor.baseValue + variation,
          resultingCost: adjustedCost,
          costChange: adjustedCost - baselineCost,
          percentageChange: (adjustedCost - baselineCost) / baselineCost * 100
        };
      }));
      /* istanbul ignore next */
      cov_ocw6unnfw().s[217]++;
      return {
        parameter: factor.parameter,
        baseValue: factor.baseValue,
        impacts,
        sensitivity: Math.max(...impacts.map(i => {
          /* istanbul ignore next */
          cov_ocw6unnfw().f[31]++;
          cov_ocw6unnfw().s[218]++;
          return Math.abs(i.percentageChange);
        })) / Math.max(...factor.variations.map(v => {
          /* istanbul ignore next */
          cov_ocw6unnfw().f[32]++;
          cov_ocw6unnfw().s[219]++;
          return Math.abs(v);
        }))
      };
    }));
    // Identify most sensitive parameters
    const rankedSensitivities =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[220]++, sensitivityResults.sort((a, b) => {
      /* istanbul ignore next */
      cov_ocw6unnfw().f[33]++;
      cov_ocw6unnfw().s[221]++;
      return b.sensitivity - a.sensitivity;
    }).map((result, index) => {
      /* istanbul ignore next */
      cov_ocw6unnfw().f[34]++;
      let impact;
      /* istanbul ignore next */
      cov_ocw6unnfw().s[222]++;
      if (result.sensitivity > 2) {
        /* istanbul ignore next */
        cov_ocw6unnfw().b[22][0]++;
        cov_ocw6unnfw().s[223]++;
        impact = 'High';
      } else {
        /* istanbul ignore next */
        cov_ocw6unnfw().b[22][1]++;
        cov_ocw6unnfw().s[224]++;
        if (result.sensitivity > 1) {
          /* istanbul ignore next */
          cov_ocw6unnfw().b[23][0]++;
          cov_ocw6unnfw().s[225]++;
          impact = 'Medium';
        } else {
          /* istanbul ignore next */
          cov_ocw6unnfw().b[23][1]++;
          cov_ocw6unnfw().s[226]++;
          impact = 'Low';
        }
      }
      /* istanbul ignore next */
      cov_ocw6unnfw().s[227]++;
      return {
        rank: index + 1,
        parameter: result.parameter,
        sensitivity: result.sensitivity,
        impact
      };
    }));
    /* istanbul ignore next */
    cov_ocw6unnfw().s[228]++;
    return {
      baselineCost,
      sensitivityResults,
      rankedSensitivities,
      keyFindings: [`Most sensitive parameter: ${rankedSensitivities[0].parameter}`, `Cost range: $${Math.min(...sensitivityResults.flatMap(r => {
        /* istanbul ignore next */
        cov_ocw6unnfw().f[35]++;
        cov_ocw6unnfw().s[229]++;
        return r.impacts.map(i => {
          /* istanbul ignore next */
          cov_ocw6unnfw().f[36]++;
          cov_ocw6unnfw().s[230]++;
          return i.resultingCost;
        });
      })).toLocaleString()} - $${Math.max(...sensitivityResults.flatMap(r => {
        /* istanbul ignore next */
        cov_ocw6unnfw().f[37]++;
        cov_ocw6unnfw().s[231]++;
        return r.impacts.map(i => {
          /* istanbul ignore next */
          cov_ocw6unnfw().f[38]++;
          cov_ocw6unnfw().s[232]++;
          return i.resultingCost;
        });
      })).toLocaleString()}`, `Sensitivity analysis shows ${rankedSensitivities.filter(r => {
        /* istanbul ignore next */
        cov_ocw6unnfw().f[39]++;
        cov_ocw6unnfw().s[233]++;
        return r.impact === 'High';
      }).length} high-impact parameters`]
    };
  }
  /**
   * Generate cost recommendations
   */
  static async generateCostRecommendations(totalCostOfOwnership, sensitivityAnalysis, costComparison) {
    /* istanbul ignore next */
    cov_ocw6unnfw().f[40]++;
    const recommendations =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[234]++, []);
    // Operating cost optimization
    /* istanbul ignore next */
    cov_ocw6unnfw().s[235]++;
    if (totalCostOfOwnership.costBreakdown.operatingCosts.percentage > 60) {
      /* istanbul ignore next */
      cov_ocw6unnfw().b[24][0]++;
      cov_ocw6unnfw().s[236]++;
      recommendations.push({
        id: 'operating_cost_optimization',
        category: 'Operating Cost Reduction',
        priority: 'High',
        title: 'Focus on Operating Cost Reduction',
        description: 'Operating costs represent a significant portion of total lifecycle costs. Focus on energy efficiency improvements.',
        potentialSavings: totalCostOfOwnership.totalPresentValue * 0.15,
        implementationCost: totalCostOfOwnership.costBreakdown.initialCosts.amount * 0.1,
        paybackPeriod: 3.5,
        actions: ['Implement advanced control strategies', 'Upgrade to high-efficiency equipment', 'Optimize system operation schedules', 'Consider demand response programs'],
        riskLevel: 'Low'
      });
    } else
    /* istanbul ignore next */
    {
      cov_ocw6unnfw().b[24][1]++;
    }
    // Maintenance cost optimization
    cov_ocw6unnfw().s[237]++;
    if (totalCostOfOwnership.costBreakdown.maintenanceCosts.percentage > 25) {
      /* istanbul ignore next */
      cov_ocw6unnfw().b[25][0]++;
      cov_ocw6unnfw().s[238]++;
      recommendations.push({
        id: 'maintenance_optimization',
        category: 'Maintenance Cost Reduction',
        priority: 'Medium',
        title: 'Optimize Maintenance Strategy',
        description: 'Maintenance costs are higher than typical. Consider predictive maintenance and equipment upgrades.',
        potentialSavings: totalCostOfOwnership.totalPresentValue * 0.08,
        implementationCost: 15000,
        paybackPeriod: 4.2,
        actions: ['Implement predictive maintenance program', 'Upgrade to more reliable equipment', 'Train maintenance staff', 'Establish preventive maintenance schedules'],
        riskLevel: 'Medium'
      });
    } else
    /* istanbul ignore next */
    {
      cov_ocw6unnfw().b[25][1]++;
    }
    // Alternative system recommendation
    const bestAlternative =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[239]++, costComparison.recommendedAlternative);
    /* istanbul ignore next */
    cov_ocw6unnfw().s[240]++;
    if (bestAlternative.costDifference < -10000) {
      /* istanbul ignore next */
      cov_ocw6unnfw().b[26][0]++;
      cov_ocw6unnfw().s[241]++;
      recommendations.push({
        id: 'alternative_system',
        category: 'System Alternative',
        priority: 'High',
        title: `Consider ${bestAlternative.alternativeName}`,
        description: bestAlternative.description,
        potentialSavings: Math.abs(bestAlternative.costDifference),
        implementationCost: 0,
        // Alternative, not additional cost
        paybackPeriod: 0,
        actions: ['Evaluate alternative system configuration', 'Perform detailed feasibility study', 'Consider phased implementation'],
        riskLevel: 'Medium'
      });
    } else
    /* istanbul ignore next */
    {
      cov_ocw6unnfw().b[26][1]++;
    }
    cov_ocw6unnfw().s[242]++;
    return recommendations;
  }
  /**
   * Generate unique analysis ID
   */
  static generateAnalysisId(systemId) {
    /* istanbul ignore next */
    cov_ocw6unnfw().f[41]++;
    const timestamp =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[243]++, Date.now());
    const random =
    /* istanbul ignore next */
    (cov_ocw6unnfw().s[244]++, Math.random().toString(36).substring(2, 8));
    /* istanbul ignore next */
    cov_ocw6unnfw().s[245]++;
    return `cost_analysis_${systemId}_${timestamp}_${random}`;
  }
}
/* istanbul ignore next */
cov_ocw6unnfw().s[246]++;
exports.LifecycleCostAnalysisEngine = LifecycleCostAnalysisEngine;
/* istanbul ignore next */
cov_ocw6unnfw().s[247]++;
LifecycleCostAnalysisEngine.VERSION = '3.0.0';
/* istanbul ignore next */
cov_ocw6unnfw().s[248]++;
LifecycleCostAnalysisEngine.COST_CACHE = new Map();
// Cost escalation rates (annual)
/* istanbul ignore next */
cov_ocw6unnfw().s[249]++;
LifecycleCostAnalysisEngine.DEFAULT_ESCALATION_RATES = {
  ENERGY: 0.03,
  // 3% annual energy cost escalation
  MAINTENANCE: 0.025,
  // 2.5% annual maintenance cost escalation
  LABOR: 0.035,
  // 3.5% annual labor cost escalation
  MATERIALS: 0.028 // 2.8% annual materials cost escalation
};
// Equipment life expectancy (years)
/* istanbul ignore next */
cov_ocw6unnfw().s[250]++;
LifecycleCostAnalysisEngine.EQUIPMENT_LIFE = {
  FANS: 20,
  MOTORS: 15,
  VFD: 12,
  DUCTWORK: 30,
  DAMPERS: 20,
  CONTROLS: 10,
  FILTERS: 0.25 // 3 months
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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