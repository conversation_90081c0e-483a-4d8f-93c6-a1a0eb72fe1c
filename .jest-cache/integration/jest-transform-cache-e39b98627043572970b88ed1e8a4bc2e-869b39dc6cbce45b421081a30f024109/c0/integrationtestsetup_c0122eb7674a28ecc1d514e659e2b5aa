efcfcb67f0826e6ad76feb4101bd87de
"use strict";
/**
 * Integration Test Setup
 *
 * Global setup for HVAC component integration tests
 * Configures mocks, test utilities, and environment for integration testing
 *
 * Part of Phase 1 bridging plan for comprehensive integration test coverage
 *
 * @see docs/post-implementation-bridging-plan.md Task 1.1
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.mockNetworkConditions = exports.setupIntegrationTestEnvironment = exports.createMockCalculationResults = exports.createMockCalculationData = exports.mockApiResponse = void 0;
require('@testing-library/jest-dom');
// =============================================================================
// Global Mocks
// =============================================================================
// Mock fetch globally
global.fetch = jest.fn();
// Mock localStorage
const mockLocalStorage = {
    store: {},
    getItem: jest.fn((key) => mockLocalStorage.store[key] || null),
    setItem: jest.fn((key, value) => {
        mockLocalStorage.store[key] = value;
    }),
    removeItem: jest.fn((key) => {
        delete mockLocalStorage.store[key];
    }),
    clear: jest.fn(() => {
        mockLocalStorage.store = {};
    }),
    length: 0,
    key: jest.fn()
};
Object.defineProperty(window, 'localStorage', {
    value: mockLocalStorage
});
// Mock sessionStorage
Object.defineProperty(window, 'sessionStorage', {
    value: mockLocalStorage
});
// Mock IndexedDB for offline storage testing
const mockIndexedDB = {
    open: jest.fn(() => Promise.resolve({
        result: {
            createObjectStore: jest.fn(),
            transaction: jest.fn(() => ({
                objectStore: jest.fn(() => ({
                    add: jest.fn(() => Promise.resolve()),
                    get: jest.fn(() => Promise.resolve()),
                    put: jest.fn(() => Promise.resolve()),
                    delete: jest.fn(() => Promise.resolve())
                }))
            }))
        }
    })),
    deleteDatabase: jest.fn(() => Promise.resolve())
};
Object.defineProperty(window, 'indexedDB', {
    value: mockIndexedDB
});
// Mock Service Worker
Object.defineProperty(navigator, 'serviceWorker', {
    value: {
        register: jest.fn(() => Promise.resolve({
            installing: null,
            waiting: null,
            active: {
                postMessage: jest.fn()
            },
            addEventListener: jest.fn(),
            removeEventListener: jest.fn()
        })),
        ready: Promise.resolve({
            active: {
                postMessage: jest.fn()
            }
        }),
        controller: {
            postMessage: jest.fn()
        }
    },
    writable: true
});
// Mock Web Workers for WASM calculations
Object.defineProperty(window, 'Worker', {
    value: class MockWorker {
        constructor(url) {
            this.url = url;
            this.postMessage = jest.fn();
            this.terminate = jest.fn();
            this.addEventListener = jest.fn();
            this.removeEventListener = jest.fn();
        }
    }
});
// Mock WebAssembly for WASM testing
Object.defineProperty(window, 'WebAssembly', {
    value: {
        instantiate: jest.fn(() => Promise.resolve({
            instance: {
                exports: {
                    calculate_air_duct_size: jest.fn(() => 14.0),
                    calculate_pressure_drop: jest.fn(() => 0.8),
                    calculate_heat_transfer: jest.fn(() => 1200.0),
                    optimize_hvac_system: jest.fn(() => ({ efficiency: 0.95 }))
                }
            }
        })),
        compile: jest.fn(() => Promise.resolve({}))
    }
});
// =============================================================================
// Test Utilities
// =============================================================================
/**
 * Mock API response helper
 */
const mockApiResponse = (data, options = {}) => {
    const { ok = true, status = 200 } = options;
    return {
        ok,
        status,
        json: async () => data,
        text: async () => JSON.stringify(data),
        headers: new Headers(),
        redirected: false,
        statusText: ok ? 'OK' : 'Error',
        type: 'basic',
        url: '',
        clone: jest.fn(),
        body: null,
        bodyUsed: false,
        arrayBuffer: async () => new ArrayBuffer(0),
        blob: async () => new Blob(),
        formData: async () => new FormData()
    };
};
exports.mockApiResponse = mockApiResponse;
/**
 * Mock HVAC calculation data factory
 */
exports.createMockCalculationData = {
    airDuct: (overrides = {}) => ({
        airflow: 1500,
        duct_type: 'round',
        friction_rate: 0.08,
        units: 'imperial',
        material: 'galvanized_steel',
        ...overrides
    }),
    pressureDrop: (overrides = {}) => ({
        airflow: 2000,
        duct_length: 100,
        duct_diameter: 16,
        fittings: [
            { type: 'elbow_90', quantity: 2 },
            { type: 'tee_branch', quantity: 1 }
        ],
        ...overrides
    }),
    compliance: (overrides = {}) => ({
        system_type: 'supply_air',
        velocity: 1800,
        duct_type: 'rectangular',
        width: 20,
        height: 8,
        standards: ['SMACNA', 'ASHRAE'],
        ...overrides
    })
};
/**
 * Mock calculation results factory
 */
exports.createMockCalculationResults = {
    airDuct: (overrides = {}) => ({
        success: true,
        results: {
            diameter: { value: 14.0, unit: 'in' },
            velocity: { value: 1400.0, unit: 'fpm' },
            area: { value: 1.07, unit: 'sq_ft' },
            pressure_loss: { value: 0.8, unit: 'in_wg_per_100ft' }
        },
        compliance: {
            smacna: {
                velocity: { passed: true, value: 1400.0, limit: 2500 }
            }
        },
        ...overrides
    }),
    compliance: (overrides = {}) => ({
        success: true,
        overall_compliance: 'compliant',
        validation_results: {
            smacna: { status: 'compliant', score: 95 },
            ashrae: { status: 'compliant', score: 88 }
        },
        ...overrides
    })
};
/**
 * Integration test environment setup
 */
const setupIntegrationTestEnvironment = () => {
    // Set test environment variables
    process.env.NODE_ENV = 'test';
    process.env.TESTING = 'true';
    process.env.API_BASE_URL = 'http://127.0.0.1:5000/api';
    // Configure console for test environment
    const originalConsoleError = console.error;
    console.error = (...args) => {
        // Suppress expected React warnings in tests
        if (typeof args[0] === 'string' &&
            args[0].includes('Warning: ReactDOM.render is no longer supported')) {
            return;
        }
        originalConsoleError.call(console, ...args);
    };
    return {
        cleanup: () => {
            console.error = originalConsoleError;
        }
    };
};
exports.setupIntegrationTestEnvironment = setupIntegrationTestEnvironment;
/**
 * Mock network conditions for offline testing
 */
exports.mockNetworkConditions = {
    online: () => {
        Object.defineProperty(navigator, 'onLine', {
            writable: true,
            value: true
        });
    },
    offline: () => {
        Object.defineProperty(navigator, 'onLine', {
            writable: true,
            value: false
        });
    },
    slowConnection: () => {
        // Mock slow network by adding delays to fetch
        const originalFetch = global.fetch;
        global.fetch = jest.fn((...args) => {
            return new Promise(resolve => {
                setTimeout(() => {
                    resolve(originalFetch(...args));
                }, 2000); // 2 second delay
            });
        });
    }
};
// =============================================================================
// Global Test Setup
// =============================================================================
beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
    // Reset localStorage
    mockLocalStorage.clear();
    // Reset fetch mock
    global.fetch.mockClear();
    // Reset network to online
    exports.mockNetworkConditions.online();
});
afterEach(() => {
    // Clean up any timers
    jest.clearAllTimers();
    // Reset modules
    jest.resetModules();
});
// Setup integration test environment
const testEnv = (0, exports.setupIntegrationTestEnvironment)();
// Cleanup after all tests
afterAll(() => {
    testEnv.cleanup();
});
// Custom matcher for HVAC calculation validation
expect.extend({
    toBeValidHVACCalculation(received) {
        const pass = received &&
            received.success === true &&
            received.results &&
            typeof received.results === 'object';
        return {
            message: () => pass
                ? `Expected ${received} not to be a valid HVAC calculation`
                : `Expected ${received} to be a valid HVAC calculation with success=true and results object`,
            pass
        };
    },
    toBeCompliantWithStandards(received, standards) {
        const pass = received &&
            received.compliance &&
            standards.every((standard) => received.compliance[standard.toLowerCase()] &&
                received.compliance[standard.toLowerCase()].status === 'compliant');
        return {
            message: () => pass
                ? `Expected ${received} not to be compliant with standards ${standards.join(', ')}`
                : `Expected ${received} to be compliant with standards ${standards.join(', ')}`,
            pass
        };
    },
    toHaveOfflineCapability(received) {
        const pass = received &&
            (received.calculated_offline === true || received.offline_capable === true);
        return {
            message: () => pass
                ? `Expected ${received} not to have offline capability`
                : `Expected ${received} to have offline capability`,
            pass
        };
    }
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiQzpcXFVzZXJzXFxqb2huclxcRG93bmxvYWRzXFxTaXplV2lzZV9TdWl0ZV9BcHBcXHRlc3RzXFxpbnRlZ3JhdGlvblxcc2V0dXBcXGludGVncmF0aW9uLXRlc3Qtc2V0dXAudHMiLCJtYXBwaW5ncyI6IjtBQUFBOzs7Ozs7Ozs7R0FTRzs7O0FBRUgsT0FBTyxDQUFDLDJCQUEyQixDQUFDLENBQUM7QUFFckMsZ0ZBQWdGO0FBQ2hGLGVBQWU7QUFDZixnRkFBZ0Y7QUFFaEYsc0JBQXNCO0FBQ3RCLE1BQU0sQ0FBQyxLQUFLLEdBQUcsSUFBSSxDQUFDLEVBQUUsRUFBRSxDQUFDO0FBRXpCLG9CQUFvQjtBQUNwQixNQUFNLGdCQUFnQixHQUFHO0lBQ3ZCLEtBQUssRUFBRSxFQUE0QjtJQUNuQyxPQUFPLEVBQUUsSUFBSSxDQUFDLEVBQUUsQ0FBQyxDQUFDLEdBQVcsRUFBRSxFQUFFLENBQUMsZ0JBQWdCLENBQUMsS0FBSyxDQUFDLEdBQUcsQ0FBQyxJQUFJLElBQUksQ0FBQztJQUN0RSxPQUFPLEVBQUUsSUFBSSxDQUFDLEVBQUUsQ0FBQyxDQUFDLEdBQVcsRUFBRSxLQUFhLEVBQUUsRUFBRTtRQUM5QyxnQkFBZ0IsQ0FBQyxLQUFLLENBQUMsR0FBRyxDQUFDLEdBQUcsS0FBSyxDQUFDO0lBQ3RDLENBQUMsQ0FBQztJQUNGLFVBQVUsRUFBRSxJQUFJLENBQUMsRUFBRSxDQUFDLENBQUMsR0FBVyxFQUFFLEVBQUU7UUFDbEMsT0FBTyxnQkFBZ0IsQ0FBQyxLQUFLLENBQUMsR0FBRyxDQUFDLENBQUM7SUFDckMsQ0FBQyxDQUFDO0lBQ0YsS0FBSyxFQUFFLElBQUksQ0FBQyxFQUFFLENBQUMsR0FBRyxFQUFFO1FBQ2xCLGdCQUFnQixDQUFDLEtBQUssR0FBRyxFQUFFLENBQUM7SUFDOUIsQ0FBQyxDQUFDO0lBQ0YsTUFBTSxFQUFFLENBQUM7SUFDVCxHQUFHLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtDQUNmLENBQUM7QUFFRixNQUFNLENBQUMsY0FBYyxDQUFDLE1BQU0sRUFBRSxjQUFjLEVBQUU7SUFDNUMsS0FBSyxFQUFFLGdCQUFnQjtDQUN4QixDQUFDLENBQUM7QUFFSCxzQkFBc0I7QUFDdEIsTUFBTSxDQUFDLGNBQWMsQ0FBQyxNQUFNLEVBQUUsZ0JBQWdCLEVBQUU7SUFDOUMsS0FBSyxFQUFFLGdCQUFnQjtDQUN4QixDQUFDLENBQUM7QUFFSCw2Q0FBNkM7QUFDN0MsTUFBTSxhQUFhLEdBQUc7SUFDcEIsSUFBSSxFQUFFLElBQUksQ0FBQyxFQUFFLENBQUMsR0FBRyxFQUFFLENBQUMsT0FBTyxDQUFDLE9BQU8sQ0FBQztRQUNsQyxNQUFNLEVBQUU7WUFDTixpQkFBaUIsRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFO1lBQzVCLFdBQVcsRUFBRSxJQUFJLENBQUMsRUFBRSxDQUFDLEdBQUcsRUFBRSxDQUFDLENBQUM7Z0JBQzFCLFdBQVcsRUFBRSxJQUFJLENBQUMsRUFBRSxDQUFDLEdBQUcsRUFBRSxDQUFDLENBQUM7b0JBQzFCLEdBQUcsRUFBRSxJQUFJLENBQUMsRUFBRSxDQUFDLEdBQUcsRUFBRSxDQUFDLE9BQU8sQ0FBQyxPQUFPLEVBQUUsQ0FBQztvQkFDckMsR0FBRyxFQUFFLElBQUksQ0FBQyxFQUFFLENBQUMsR0FBRyxFQUFFLENBQUMsT0FBTyxDQUFDLE9BQU8sRUFBRSxDQUFDO29CQUNyQyxHQUFHLEVBQUUsSUFBSSxDQUFDLEVBQUUsQ0FBQyxHQUFHLEVBQUUsQ0FBQyxPQUFPLENBQUMsT0FBTyxFQUFFLENBQUM7b0JBQ3JDLE1BQU0sRUFBRSxJQUFJLENBQUMsRUFBRSxDQUFDLEdBQUcsRUFBRSxDQUFDLE9BQU8sQ0FBQyxPQUFPLEVBQUUsQ0FBQztpQkFDekMsQ0FBQyxDQUFDO2FBQ0osQ0FBQyxDQUFDO1NBQ0o7S0FDRixDQUFDLENBQUM7SUFDSCxjQUFjLEVBQUUsSUFBSSxDQUFDLEVBQUUsQ0FBQyxHQUFHLEVBQUUsQ0FBQyxPQUFPLENBQUMsT0FBTyxFQUFFLENBQUM7Q0FDakQsQ0FBQztBQUVGLE1BQU0sQ0FBQyxjQUFjLENBQUMsTUFBTSxFQUFFLFdBQVcsRUFBRTtJQUN6QyxLQUFLLEVBQUUsYUFBYTtDQUNyQixDQUFDLENBQUM7QUFFSCxzQkFBc0I7QUFDdEIsTUFBTSxDQUFDLGNBQWMsQ0FBQyxTQUFTLEVBQUUsZUFBZSxFQUFFO0lBQ2hELEtBQUssRUFBRTtRQUNMLFFBQVEsRUFBRSxJQUFJLENBQUMsRUFBRSxDQUFDLEdBQUcsRUFBRSxDQUFDLE9BQU8sQ0FBQyxPQUFPLENBQUM7WUFDdEMsVUFBVSxFQUFFLElBQUk7WUFDaEIsT0FBTyxFQUFFLElBQUk7WUFDYixNQUFNLEVBQUU7Z0JBQ04sV0FBVyxFQUFFLElBQUksQ0FBQyxFQUFFLEVBQUU7YUFDdkI7WUFDRCxnQkFBZ0IsRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFO1lBQzNCLG1CQUFtQixFQUFFLElBQUksQ0FBQyxFQUFFLEVBQUU7U0FDL0IsQ0FBQyxDQUFDO1FBQ0gsS0FBSyxFQUFFLE9BQU8sQ0FBQyxPQUFPLENBQUM7WUFDckIsTUFBTSxFQUFFO2dCQUNOLFdBQVcsRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFO2FBQ3ZCO1NBQ0YsQ0FBQztRQUNGLFVBQVUsRUFBRTtZQUNWLFdBQVcsRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFO1NBQ3ZCO0tBQ0Y7SUFDRCxRQUFRLEVBQUUsSUFBSTtDQUNmLENBQUMsQ0FBQztBQUVILHlDQUF5QztBQUN6QyxNQUFNLENBQUMsY0FBYyxDQUFDLE1BQU0sRUFBRSxRQUFRLEVBQUU7SUFDdEMsS0FBSyxFQUFFLE1BQU0sVUFBVTtRQUNyQixZQUFtQixHQUFXO1lBQVgsUUFBRyxHQUFILEdBQUcsQ0FBUTtZQUM5QixnQkFBVyxHQUFHLElBQUksQ0FBQyxFQUFFLEVBQUUsQ0FBQztZQUN4QixjQUFTLEdBQUcsSUFBSSxDQUFDLEVBQUUsRUFBRSxDQUFDO1lBQ3RCLHFCQUFnQixHQUFHLElBQUksQ0FBQyxFQUFFLEVBQUUsQ0FBQztZQUM3Qix3QkFBbUIsR0FBRyxJQUFJLENBQUMsRUFBRSxFQUFFLENBQUM7UUFKQyxDQUFDO0tBS25DO0NBQ0YsQ0FBQyxDQUFDO0FBRUgsb0NBQW9DO0FBQ3BDLE1BQU0sQ0FBQyxjQUFjLENBQUMsTUFBTSxFQUFFLGFBQWEsRUFBRTtJQUMzQyxLQUFLLEVBQUU7UUFDTCxXQUFXLEVBQUUsSUFBSSxDQUFDLEVBQUUsQ0FBQyxHQUFHLEVBQUUsQ0FBQyxPQUFPLENBQUMsT0FBTyxDQUFDO1lBQ3pDLFFBQVEsRUFBRTtnQkFDUixPQUFPLEVBQUU7b0JBQ1AsdUJBQXVCLEVBQUUsSUFBSSxDQUFDLEVBQUUsQ0FBQyxHQUFHLEVBQUUsQ0FBQyxJQUFJLENBQUM7b0JBQzVDLHVCQUF1QixFQUFFLElBQUksQ0FBQyxFQUFFLENBQUMsR0FBRyxFQUFFLENBQUMsR0FBRyxDQUFDO29CQUMzQyx1QkFBdUIsRUFBRSxJQUFJLENBQUMsRUFBRSxDQUFDLEdBQUcsRUFBRSxDQUFDLE1BQU0sQ0FBQztvQkFDOUMsb0JBQW9CLEVBQUUsSUFBSSxDQUFDLEVBQUUsQ0FBQyxHQUFHLEVBQUUsQ0FBQyxDQUFDLEVBQUUsVUFBVSxFQUFFLElBQUksRUFBRSxDQUFDLENBQUM7aUJBQzVEO2FBQ0Y7U0FDRixDQUFDLENBQUM7UUFDSCxPQUFPLEVBQUUsSUFBSSxDQUFDLEVBQUUsQ0FBQyxHQUFHLEVBQUUsQ0FBQyxPQUFPLENBQUMsT0FBTyxDQUFDLEVBQUUsQ0FBQyxDQUFDO0tBQzVDO0NBQ0YsQ0FBQyxDQUFDO0FBRUgsZ0ZBQWdGO0FBQ2hGLGlCQUFpQjtBQUNqQixnRkFBZ0Y7QUFFaEY7O0dBRUc7QUFDSSxNQUFNLGVBQWUsR0FBRyxDQUFDLElBQVMsRUFBRSxVQUE2QyxFQUFFLEVBQUUsRUFBRTtJQUM1RixNQUFNLEVBQUUsRUFBRSxHQUFHLElBQUksRUFBRSxNQUFNLEdBQUcsR0FBRyxFQUFFLEdBQUcsT0FBTyxDQUFDO0lBRTVDLE9BQU87UUFDTCxFQUFFO1FBQ0YsTUFBTTtRQUNOLElBQUksRUFBRSxLQUFLLElBQUksRUFBRSxDQUFDLElBQUk7UUFDdEIsSUFBSSxFQUFFLEtBQUssSUFBSSxFQUFFLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxJQUFJLENBQUM7UUFDdEMsT0FBTyxFQUFFLElBQUksT0FBTyxFQUFFO1FBQ3RCLFVBQVUsRUFBRSxLQUFLO1FBQ2pCLFVBQVUsRUFBRSxFQUFFLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsT0FBTztRQUMvQixJQUFJLEVBQUUsT0FBdUI7UUFDN0IsR0FBRyxFQUFFLEVBQUU7UUFDUCxLQUFLLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRTtRQUNoQixJQUFJLEVBQUUsSUFBSTtRQUNWLFFBQVEsRUFBRSxLQUFLO1FBQ2YsV0FBVyxFQUFFLEtBQUssSUFBSSxFQUFFLENBQUMsSUFBSSxXQUFXLENBQUMsQ0FBQyxDQUFDO1FBQzNDLElBQUksRUFBRSxLQUFLLElBQUksRUFBRSxDQUFDLElBQUksSUFBSSxFQUFFO1FBQzVCLFFBQVEsRUFBRSxLQUFLLElBQUksRUFBRSxDQUFDLElBQUksUUFBUSxFQUFFO0tBQ3pCLENBQUM7QUFDaEIsQ0FBQyxDQUFDO0FBcEJXLFFBQUEsZUFBZSxtQkFvQjFCO0FBRUY7O0dBRUc7QUFDVSxRQUFBLHlCQUF5QixHQUFHO0lBQ3ZDLE9BQU8sRUFBRSxDQUFDLFlBQTBCLEVBQUUsRUFBRSxFQUFFLENBQUMsQ0FBQztRQUMxQyxPQUFPLEVBQUUsSUFBSTtRQUNiLFNBQVMsRUFBRSxPQUFPO1FBQ2xCLGFBQWEsRUFBRSxJQUFJO1FBQ25CLEtBQUssRUFBRSxVQUFVO1FBQ2pCLFFBQVEsRUFBRSxrQkFBa0I7UUFDNUIsR0FBRyxTQUFTO0tBQ2IsQ0FBQztJQUVGLFlBQVksRUFBRSxDQUFDLFlBQTBCLEVBQUUsRUFBRSxFQUFFLENBQUMsQ0FBQztRQUMvQyxPQUFPLEVBQUUsSUFBSTtRQUNiLFdBQVcsRUFBRSxHQUFHO1FBQ2hCLGFBQWEsRUFBRSxFQUFFO1FBQ2pCLFFBQVEsRUFBRTtZQUNSLEVBQUUsSUFBSSxFQUFFLFVBQVUsRUFBRSxRQUFRLEVBQUUsQ0FBQyxFQUFFO1lBQ2pDLEVBQUUsSUFBSSxFQUFFLFlBQVksRUFBRSxRQUFRLEVBQUUsQ0FBQyxFQUFFO1NBQ3BDO1FBQ0QsR0FBRyxTQUFTO0tBQ2IsQ0FBQztJQUVGLFVBQVUsRUFBRSxDQUFDLFlBQTBCLEVBQUUsRUFBRSxFQUFFLENBQUMsQ0FBQztRQUM3QyxXQUFXLEVBQUUsWUFBWTtRQUN6QixRQUFRLEVBQUUsSUFBSTtRQUNkLFNBQVMsRUFBRSxhQUFhO1FBQ3hCLEtBQUssRUFBRSxFQUFFO1FBQ1QsTUFBTSxFQUFFLENBQUM7UUFDVCxTQUFTLEVBQUUsQ0FBQyxRQUFRLEVBQUUsUUFBUSxDQUFDO1FBQy9CLEdBQUcsU0FBUztLQUNiLENBQUM7Q0FDSCxDQUFDO0FBRUY7O0dBRUc7QUFDVSxRQUFBLDRCQUE0QixHQUFHO0lBQzFDLE9BQU8sRUFBRSxDQUFDLFlBQTBCLEVBQUUsRUFBRSxFQUFFLENBQUMsQ0FBQztRQUMxQyxPQUFPLEVBQUUsSUFBSTtRQUNiLE9BQU8sRUFBRTtZQUNQLFFBQVEsRUFBRSxFQUFFLEtBQUssRUFBRSxJQUFJLEVBQUUsSUFBSSxFQUFFLElBQUksRUFBRTtZQUNyQyxRQUFRLEVBQUUsRUFBRSxLQUFLLEVBQUUsTUFBTSxFQUFFLElBQUksRUFBRSxLQUFLLEVBQUU7WUFDeEMsSUFBSSxFQUFFLEVBQUUsS0FBSyxFQUFFLElBQUksRUFBRSxJQUFJLEVBQUUsT0FBTyxFQUFFO1lBQ3BDLGFBQWEsRUFBRSxFQUFFLEtBQUssRUFBRSxHQUFHLEVBQUUsSUFBSSxFQUFFLGlCQUFpQixFQUFFO1NBQ3ZEO1FBQ0QsVUFBVSxFQUFFO1lBQ1YsTUFBTSxFQUFFO2dCQUNOLFFBQVEsRUFBRSxFQUFFLE1BQU0sRUFBRSxJQUFJLEVBQUUsS0FBSyxFQUFFLE1BQU0sRUFBRSxLQUFLLEVBQUUsSUFBSSxFQUFFO2FBQ3ZEO1NBQ0Y7UUFDRCxHQUFHLFNBQVM7S0FDYixDQUFDO0lBRUYsVUFBVSxFQUFFLENBQUMsWUFBMEIsRUFBRSxFQUFFLEVBQUUsQ0FBQyxDQUFDO1FBQzdDLE9BQU8sRUFBRSxJQUFJO1FBQ2Isa0JBQWtCLEVBQUUsV0FBVztRQUMvQixrQkFBa0IsRUFBRTtZQUNsQixNQUFNLEVBQUUsRUFBRSxNQUFNLEVBQUUsV0FBVyxFQUFFLEtBQUssRUFBRSxFQUFFLEVBQUU7WUFDMUMsTUFBTSxFQUFFLEVBQUUsTUFBTSxFQUFFLFdBQVcsRUFBRSxLQUFLLEVBQUUsRUFBRSxFQUFFO1NBQzNDO1FBQ0QsR0FBRyxTQUFTO0tBQ2IsQ0FBQztDQUNILENBQUM7QUFFRjs7R0FFRztBQUNJLE1BQU0sK0JBQStCLEdBQUcsR0FBRyxFQUFFO0lBQ2xELGlDQUFpQztJQUNqQyxPQUFPLENBQUMsR0FBRyxDQUFDLFFBQVEsR0FBRyxNQUFNLENBQUM7SUFDOUIsT0FBTyxDQUFDLEdBQUcsQ0FBQyxPQUFPLEdBQUcsTUFBTSxDQUFDO0lBQzdCLE9BQU8sQ0FBQyxHQUFHLENBQUMsWUFBWSxHQUFHLDJCQUEyQixDQUFDO0lBRXZELHlDQUF5QztJQUN6QyxNQUFNLG9CQUFvQixHQUFHLE9BQU8sQ0FBQyxLQUFLLENBQUM7SUFDM0MsT0FBTyxDQUFDLEtBQUssR0FBRyxDQUFDLEdBQUcsSUFBVyxFQUFFLEVBQUU7UUFDakMsNENBQTRDO1FBQzVDLElBQ0UsT0FBTyxJQUFJLENBQUMsQ0FBQyxDQUFDLEtBQUssUUFBUTtZQUMzQixJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUMsUUFBUSxDQUFDLGlEQUFpRCxDQUFDLEVBQ25FLENBQUM7WUFDRCxPQUFPO1FBQ1QsQ0FBQztRQUNELG9CQUFvQixDQUFDLElBQUksQ0FBQyxPQUFPLEVBQUUsR0FBRyxJQUFJLENBQUMsQ0FBQztJQUM5QyxDQUFDLENBQUM7SUFFRixPQUFPO1FBQ0wsT0FBTyxFQUFFLEdBQUcsRUFBRTtZQUNaLE9BQU8sQ0FBQyxLQUFLLEdBQUcsb0JBQW9CLENBQUM7UUFDdkMsQ0FBQztLQUNGLENBQUM7QUFDSixDQUFDLENBQUM7QUF4QlcsUUFBQSwrQkFBK0IsbUNBd0IxQztBQUVGOztHQUVHO0FBQ1UsUUFBQSxxQkFBcUIsR0FBRztJQUNuQyxNQUFNLEVBQUUsR0FBRyxFQUFFO1FBQ1gsTUFBTSxDQUFDLGNBQWMsQ0FBQyxTQUFTLEVBQUUsUUFBUSxFQUFFO1lBQ3pDLFFBQVEsRUFBRSxJQUFJO1lBQ2QsS0FBSyxFQUFFLElBQUk7U0FDWixDQUFDLENBQUM7SUFDTCxDQUFDO0lBRUQsT0FBTyxFQUFFLEdBQUcsRUFBRTtRQUNaLE1BQU0sQ0FBQyxjQUFjLENBQUMsU0FBUyxFQUFFLFFBQVEsRUFBRTtZQUN6QyxRQUFRLEVBQUUsSUFBSTtZQUNkLEtBQUssRUFBRSxLQUFLO1NBQ2IsQ0FBQyxDQUFDO0lBQ0wsQ0FBQztJQUVELGNBQWMsRUFBRSxHQUFHLEVBQUU7UUFDbkIsOENBQThDO1FBQzlDLE1BQU0sYUFBYSxHQUFHLE1BQU0sQ0FBQyxLQUFLLENBQUM7UUFDbkMsTUFBTSxDQUFDLEtBQUssR0FBRyxJQUFJLENBQUMsRUFBRSxDQUFDLENBQUMsR0FBRyxJQUFJLEVBQUUsRUFBRTtZQUNqQyxPQUFPLElBQUksT0FBTyxDQUFDLE9BQU8sQ0FBQyxFQUFFO2dCQUMzQixVQUFVLENBQUMsR0FBRyxFQUFFO29CQUNkLE9BQU8sQ0FBQyxhQUFhLENBQUMsR0FBRyxJQUFJLENBQUMsQ0FBQyxDQUFDO2dCQUNsQyxDQUFDLEVBQUUsSUFBSSxDQUFDLENBQUMsQ0FBQyxpQkFBaUI7WUFDN0IsQ0FBQyxDQUFDLENBQUM7UUFDTCxDQUFDLENBQUMsQ0FBQztJQUNMLENBQUM7Q0FDRixDQUFDO0FBRUYsZ0ZBQWdGO0FBQ2hGLG9CQUFvQjtBQUNwQixnRkFBZ0Y7QUFFaEYsVUFBVSxDQUFDLEdBQUcsRUFBRTtJQUNkLG1DQUFtQztJQUNuQyxJQUFJLENBQUMsYUFBYSxFQUFFLENBQUM7SUFFckIscUJBQXFCO0lBQ3JCLGdCQUFnQixDQUFDLEtBQUssRUFBRSxDQUFDO0lBRXpCLG1CQUFtQjtJQUNsQixNQUFNLENBQUMsS0FBMkMsQ0FBQyxTQUFTLEVBQUUsQ0FBQztJQUVoRSwwQkFBMEI7SUFDMUIsNkJBQXFCLENBQUMsTUFBTSxFQUFFLENBQUM7QUFDakMsQ0FBQyxDQUFDLENBQUM7QUFFSCxTQUFTLENBQUMsR0FBRyxFQUFFO0lBQ2Isc0JBQXNCO0lBQ3RCLElBQUksQ0FBQyxjQUFjLEVBQUUsQ0FBQztJQUV0QixnQkFBZ0I7SUFDaEIsSUFBSSxDQUFDLFlBQVksRUFBRSxDQUFDO0FBQ3RCLENBQUMsQ0FBQyxDQUFDO0FBRUgscUNBQXFDO0FBQ3JDLE1BQU0sT0FBTyxHQUFHLElBQUEsdUNBQStCLEdBQUUsQ0FBQztBQUVsRCwwQkFBMEI7QUFDMUIsUUFBUSxDQUFDLEdBQUcsRUFBRTtJQUNaLE9BQU8sQ0FBQyxPQUFPLEVBQUUsQ0FBQztBQUNwQixDQUFDLENBQUMsQ0FBQztBQWdCSCxpREFBaUQ7QUFDakQsTUFBTSxDQUFDLE1BQU0sQ0FBQztJQUNaLHdCQUF3QixDQUFDLFFBQVE7UUFDL0IsTUFBTSxJQUFJLEdBQUcsUUFBUTtZQUNuQixRQUFRLENBQUMsT0FBTyxLQUFLLElBQUk7WUFDekIsUUFBUSxDQUFDLE9BQU87WUFDaEIsT0FBTyxRQUFRLENBQUMsT0FBTyxLQUFLLFFBQVEsQ0FBQztRQUV2QyxPQUFPO1lBQ0wsT0FBTyxFQUFFLEdBQUcsRUFBRSxDQUFDLElBQUk7Z0JBQ2pCLENBQUMsQ0FBQyxZQUFZLFFBQVEscUNBQXFDO2dCQUMzRCxDQUFDLENBQUMsWUFBWSxRQUFRLHNFQUFzRTtZQUM5RixJQUFJO1NBQ0wsQ0FBQztJQUNKLENBQUM7SUFFRCwwQkFBMEIsQ0FBQyxRQUFRLEVBQUUsU0FBUztRQUM1QyxNQUFNLElBQUksR0FBRyxRQUFRO1lBQ25CLFFBQVEsQ0FBQyxVQUFVO1lBQ25CLFNBQVMsQ0FBQyxLQUFLLENBQUMsQ0FBQyxRQUFnQixFQUFFLEVBQUUsQ0FDbkMsUUFBUSxDQUFDLFVBQVUsQ0FBQyxRQUFRLENBQUMsV0FBVyxFQUFFLENBQUM7Z0JBQzNDLFFBQVEsQ0FBQyxVQUFVLENBQUMsUUFBUSxDQUFDLFdBQVcsRUFBRSxDQUFDLENBQUMsTUFBTSxLQUFLLFdBQVcsQ0FDbkUsQ0FBQztRQUVKLE9BQU87WUFDTCxPQUFPLEVBQUUsR0FBRyxFQUFFLENBQUMsSUFBSTtnQkFDakIsQ0FBQyxDQUFDLFlBQVksUUFBUSx1Q0FBdUMsU0FBUyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsRUFBRTtnQkFDbkYsQ0FBQyxDQUFDLFlBQVksUUFBUSxtQ0FBbUMsU0FBUyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsRUFBRTtZQUNqRixJQUFJO1NBQ0wsQ0FBQztJQUNKLENBQUM7SUFFRCx1QkFBdUIsQ0FBQyxRQUFRO1FBQzlCLE1BQU0sSUFBSSxHQUFHLFFBQVE7WUFDbkIsQ0FBQyxRQUFRLENBQUMsa0JBQWtCLEtBQUssSUFBSSxJQUFJLFFBQVEsQ0FBQyxlQUFlLEtBQUssSUFBSSxDQUFDLENBQUM7UUFFOUUsT0FBTztZQUNMLE9BQU8sRUFBRSxHQUFHLEVBQUUsQ0FBQyxJQUFJO2dCQUNqQixDQUFDLENBQUMsWUFBWSxRQUFRLGlDQUFpQztnQkFDdkQsQ0FBQyxDQUFDLFlBQVksUUFBUSw2QkFBNkI7WUFDckQsSUFBSTtTQUNMLENBQUM7SUFDSixDQUFDO0NBQ0YsQ0FBQyxDQUFDIiwibmFtZXMiOltdLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcam9obnJcXERvd25sb2Fkc1xcU2l6ZVdpc2VfU3VpdGVfQXBwXFx0ZXN0c1xcaW50ZWdyYXRpb25cXHNldHVwXFxpbnRlZ3JhdGlvbi10ZXN0LXNldHVwLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogSW50ZWdyYXRpb24gVGVzdCBTZXR1cFxuICogXG4gKiBHbG9iYWwgc2V0dXAgZm9yIEhWQUMgY29tcG9uZW50IGludGVncmF0aW9uIHRlc3RzXG4gKiBDb25maWd1cmVzIG1vY2tzLCB0ZXN0IHV0aWxpdGllcywgYW5kIGVudmlyb25tZW50IGZvciBpbnRlZ3JhdGlvbiB0ZXN0aW5nXG4gKiBcbiAqIFBhcnQgb2YgUGhhc2UgMSBicmlkZ2luZyBwbGFuIGZvciBjb21wcmVoZW5zaXZlIGludGVncmF0aW9uIHRlc3QgY292ZXJhZ2VcbiAqIFxuICogQHNlZSBkb2NzL3Bvc3QtaW1wbGVtZW50YXRpb24tYnJpZGdpbmctcGxhbi5tZCBUYXNrIDEuMVxuICovXG5cbnJlcXVpcmUoJ0B0ZXN0aW5nLWxpYnJhcnkvamVzdC1kb20nKTtcblxuLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cbi8vIEdsb2JhbCBNb2Nrc1xuLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cblxuLy8gTW9jayBmZXRjaCBnbG9iYWxseVxuZ2xvYmFsLmZldGNoID0gamVzdC5mbigpO1xuXG4vLyBNb2NrIGxvY2FsU3RvcmFnZVxuY29uc3QgbW9ja0xvY2FsU3RvcmFnZSA9IHtcbiAgc3RvcmU6IHt9IGFzIFJlY29yZDxzdHJpbmcsIHN0cmluZz4sXG4gIGdldEl0ZW06IGplc3QuZm4oKGtleTogc3RyaW5nKSA9PiBtb2NrTG9jYWxTdG9yYWdlLnN0b3JlW2tleV0gfHwgbnVsbCksXG4gIHNldEl0ZW06IGplc3QuZm4oKGtleTogc3RyaW5nLCB2YWx1ZTogc3RyaW5nKSA9PiB7XG4gICAgbW9ja0xvY2FsU3RvcmFnZS5zdG9yZVtrZXldID0gdmFsdWU7XG4gIH0pLFxuICByZW1vdmVJdGVtOiBqZXN0LmZuKChrZXk6IHN0cmluZykgPT4ge1xuICAgIGRlbGV0ZSBtb2NrTG9jYWxTdG9yYWdlLnN0b3JlW2tleV07XG4gIH0pLFxuICBjbGVhcjogamVzdC5mbigoKSA9PiB7XG4gICAgbW9ja0xvY2FsU3RvcmFnZS5zdG9yZSA9IHt9O1xuICB9KSxcbiAgbGVuZ3RoOiAwLFxuICBrZXk6IGplc3QuZm4oKVxufTtcblxuT2JqZWN0LmRlZmluZVByb3BlcnR5KHdpbmRvdywgJ2xvY2FsU3RvcmFnZScsIHtcbiAgdmFsdWU6IG1vY2tMb2NhbFN0b3JhZ2Vcbn0pO1xuXG4vLyBNb2NrIHNlc3Npb25TdG9yYWdlXG5PYmplY3QuZGVmaW5lUHJvcGVydHkod2luZG93LCAnc2Vzc2lvblN0b3JhZ2UnLCB7XG4gIHZhbHVlOiBtb2NrTG9jYWxTdG9yYWdlXG59KTtcblxuLy8gTW9jayBJbmRleGVkREIgZm9yIG9mZmxpbmUgc3RvcmFnZSB0ZXN0aW5nXG5jb25zdCBtb2NrSW5kZXhlZERCID0ge1xuICBvcGVuOiBqZXN0LmZuKCgpID0+IFByb21pc2UucmVzb2x2ZSh7XG4gICAgcmVzdWx0OiB7XG4gICAgICBjcmVhdGVPYmplY3RTdG9yZTogamVzdC5mbigpLFxuICAgICAgdHJhbnNhY3Rpb246IGplc3QuZm4oKCkgPT4gKHtcbiAgICAgICAgb2JqZWN0U3RvcmU6IGplc3QuZm4oKCkgPT4gKHtcbiAgICAgICAgICBhZGQ6IGplc3QuZm4oKCkgPT4gUHJvbWlzZS5yZXNvbHZlKCkpLFxuICAgICAgICAgIGdldDogamVzdC5mbigoKSA9PiBQcm9taXNlLnJlc29sdmUoKSksXG4gICAgICAgICAgcHV0OiBqZXN0LmZuKCgpID0+IFByb21pc2UucmVzb2x2ZSgpKSxcbiAgICAgICAgICBkZWxldGU6IGplc3QuZm4oKCkgPT4gUHJvbWlzZS5yZXNvbHZlKCkpXG4gICAgICAgIH0pKVxuICAgICAgfSkpXG4gICAgfVxuICB9KSksXG4gIGRlbGV0ZURhdGFiYXNlOiBqZXN0LmZuKCgpID0+IFByb21pc2UucmVzb2x2ZSgpKVxufTtcblxuT2JqZWN0LmRlZmluZVByb3BlcnR5KHdpbmRvdywgJ2luZGV4ZWREQicsIHtcbiAgdmFsdWU6IG1vY2tJbmRleGVkREJcbn0pO1xuXG4vLyBNb2NrIFNlcnZpY2UgV29ya2VyXG5PYmplY3QuZGVmaW5lUHJvcGVydHkobmF2aWdhdG9yLCAnc2VydmljZVdvcmtlcicsIHtcbiAgdmFsdWU6IHtcbiAgICByZWdpc3RlcjogamVzdC5mbigoKSA9PiBQcm9taXNlLnJlc29sdmUoe1xuICAgICAgaW5zdGFsbGluZzogbnVsbCxcbiAgICAgIHdhaXRpbmc6IG51bGwsXG4gICAgICBhY3RpdmU6IHtcbiAgICAgICAgcG9zdE1lc3NhZ2U6IGplc3QuZm4oKVxuICAgICAgfSxcbiAgICAgIGFkZEV2ZW50TGlzdGVuZXI6IGplc3QuZm4oKSxcbiAgICAgIHJlbW92ZUV2ZW50TGlzdGVuZXI6IGplc3QuZm4oKVxuICAgIH0pKSxcbiAgICByZWFkeTogUHJvbWlzZS5yZXNvbHZlKHtcbiAgICAgIGFjdGl2ZToge1xuICAgICAgICBwb3N0TWVzc2FnZTogamVzdC5mbigpXG4gICAgICB9XG4gICAgfSksXG4gICAgY29udHJvbGxlcjoge1xuICAgICAgcG9zdE1lc3NhZ2U6IGplc3QuZm4oKVxuICAgIH1cbiAgfSxcbiAgd3JpdGFibGU6IHRydWVcbn0pO1xuXG4vLyBNb2NrIFdlYiBXb3JrZXJzIGZvciBXQVNNIGNhbGN1bGF0aW9uc1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KHdpbmRvdywgJ1dvcmtlcicsIHtcbiAgdmFsdWU6IGNsYXNzIE1vY2tXb3JrZXIge1xuICAgIGNvbnN0cnVjdG9yKHB1YmxpYyB1cmw6IHN0cmluZykge31cbiAgICBwb3N0TWVzc2FnZSA9IGplc3QuZm4oKTtcbiAgICB0ZXJtaW5hdGUgPSBqZXN0LmZuKCk7XG4gICAgYWRkRXZlbnRMaXN0ZW5lciA9IGplc3QuZm4oKTtcbiAgICByZW1vdmVFdmVudExpc3RlbmVyID0gamVzdC5mbigpO1xuICB9XG59KTtcblxuLy8gTW9jayBXZWJBc3NlbWJseSBmb3IgV0FTTSB0ZXN0aW5nXG5PYmplY3QuZGVmaW5lUHJvcGVydHkod2luZG93LCAnV2ViQXNzZW1ibHknLCB7XG4gIHZhbHVlOiB7XG4gICAgaW5zdGFudGlhdGU6IGplc3QuZm4oKCkgPT4gUHJvbWlzZS5yZXNvbHZlKHtcbiAgICAgIGluc3RhbmNlOiB7XG4gICAgICAgIGV4cG9ydHM6IHtcbiAgICAgICAgICBjYWxjdWxhdGVfYWlyX2R1Y3Rfc2l6ZTogamVzdC5mbigoKSA9PiAxNC4wKSxcbiAgICAgICAgICBjYWxjdWxhdGVfcHJlc3N1cmVfZHJvcDogamVzdC5mbigoKSA9PiAwLjgpLFxuICAgICAgICAgIGNhbGN1bGF0ZV9oZWF0X3RyYW5zZmVyOiBqZXN0LmZuKCgpID0+IDEyMDAuMCksXG4gICAgICAgICAgb3B0aW1pemVfaHZhY19zeXN0ZW06IGplc3QuZm4oKCkgPT4gKHsgZWZmaWNpZW5jeTogMC45NSB9KSlcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0pKSxcbiAgICBjb21waWxlOiBqZXN0LmZuKCgpID0+IFByb21pc2UucmVzb2x2ZSh7fSkpXG4gIH1cbn0pO1xuXG4vLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuLy8gVGVzdCBVdGlsaXRpZXNcbi8vID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG5cbi8qKlxuICogTW9jayBBUEkgcmVzcG9uc2UgaGVscGVyXG4gKi9cbmV4cG9ydCBjb25zdCBtb2NrQXBpUmVzcG9uc2UgPSAoZGF0YTogYW55LCBvcHRpb25zOiB7IG9rPzogYm9vbGVhbjsgc3RhdHVzPzogbnVtYmVyIH0gPSB7fSkgPT4ge1xuICBjb25zdCB7IG9rID0gdHJ1ZSwgc3RhdHVzID0gMjAwIH0gPSBvcHRpb25zO1xuICBcbiAgcmV0dXJuIHtcbiAgICBvayxcbiAgICBzdGF0dXMsXG4gICAganNvbjogYXN5bmMgKCkgPT4gZGF0YSxcbiAgICB0ZXh0OiBhc3luYyAoKSA9PiBKU09OLnN0cmluZ2lmeShkYXRhKSxcbiAgICBoZWFkZXJzOiBuZXcgSGVhZGVycygpLFxuICAgIHJlZGlyZWN0ZWQ6IGZhbHNlLFxuICAgIHN0YXR1c1RleHQ6IG9rID8gJ09LJyA6ICdFcnJvcicsXG4gICAgdHlwZTogJ2Jhc2ljJyBhcyBSZXNwb25zZVR5cGUsXG4gICAgdXJsOiAnJyxcbiAgICBjbG9uZTogamVzdC5mbigpLFxuICAgIGJvZHk6IG51bGwsXG4gICAgYm9keVVzZWQ6IGZhbHNlLFxuICAgIGFycmF5QnVmZmVyOiBhc3luYyAoKSA9PiBuZXcgQXJyYXlCdWZmZXIoMCksXG4gICAgYmxvYjogYXN5bmMgKCkgPT4gbmV3IEJsb2IoKSxcbiAgICBmb3JtRGF0YTogYXN5bmMgKCkgPT4gbmV3IEZvcm1EYXRhKClcbiAgfSBhcyBSZXNwb25zZTtcbn07XG5cbi8qKlxuICogTW9jayBIVkFDIGNhbGN1bGF0aW9uIGRhdGEgZmFjdG9yeVxuICovXG5leHBvcnQgY29uc3QgY3JlYXRlTW9ja0NhbGN1bGF0aW9uRGF0YSA9IHtcbiAgYWlyRHVjdDogKG92ZXJyaWRlczogUGFydGlhbDxhbnk+ID0ge30pID0+ICh7XG4gICAgYWlyZmxvdzogMTUwMCxcbiAgICBkdWN0X3R5cGU6ICdyb3VuZCcsXG4gICAgZnJpY3Rpb25fcmF0ZTogMC4wOCxcbiAgICB1bml0czogJ2ltcGVyaWFsJyxcbiAgICBtYXRlcmlhbDogJ2dhbHZhbml6ZWRfc3RlZWwnLFxuICAgIC4uLm92ZXJyaWRlc1xuICB9KSxcbiAgXG4gIHByZXNzdXJlRHJvcDogKG92ZXJyaWRlczogUGFydGlhbDxhbnk+ID0ge30pID0+ICh7XG4gICAgYWlyZmxvdzogMjAwMCxcbiAgICBkdWN0X2xlbmd0aDogMTAwLFxuICAgIGR1Y3RfZGlhbWV0ZXI6IDE2LFxuICAgIGZpdHRpbmdzOiBbXG4gICAgICB7IHR5cGU6ICdlbGJvd185MCcsIHF1YW50aXR5OiAyIH0sXG4gICAgICB7IHR5cGU6ICd0ZWVfYnJhbmNoJywgcXVhbnRpdHk6IDEgfVxuICAgIF0sXG4gICAgLi4ub3ZlcnJpZGVzXG4gIH0pLFxuICBcbiAgY29tcGxpYW5jZTogKG92ZXJyaWRlczogUGFydGlhbDxhbnk+ID0ge30pID0+ICh7XG4gICAgc3lzdGVtX3R5cGU6ICdzdXBwbHlfYWlyJyxcbiAgICB2ZWxvY2l0eTogMTgwMCxcbiAgICBkdWN0X3R5cGU6ICdyZWN0YW5ndWxhcicsXG4gICAgd2lkdGg6IDIwLFxuICAgIGhlaWdodDogOCxcbiAgICBzdGFuZGFyZHM6IFsnU01BQ05BJywgJ0FTSFJBRSddLFxuICAgIC4uLm92ZXJyaWRlc1xuICB9KVxufTtcblxuLyoqXG4gKiBNb2NrIGNhbGN1bGF0aW9uIHJlc3VsdHMgZmFjdG9yeVxuICovXG5leHBvcnQgY29uc3QgY3JlYXRlTW9ja0NhbGN1bGF0aW9uUmVzdWx0cyA9IHtcbiAgYWlyRHVjdDogKG92ZXJyaWRlczogUGFydGlhbDxhbnk+ID0ge30pID0+ICh7XG4gICAgc3VjY2VzczogdHJ1ZSxcbiAgICByZXN1bHRzOiB7XG4gICAgICBkaWFtZXRlcjogeyB2YWx1ZTogMTQuMCwgdW5pdDogJ2luJyB9LFxuICAgICAgdmVsb2NpdHk6IHsgdmFsdWU6IDE0MDAuMCwgdW5pdDogJ2ZwbScgfSxcbiAgICAgIGFyZWE6IHsgdmFsdWU6IDEuMDcsIHVuaXQ6ICdzcV9mdCcgfSxcbiAgICAgIHByZXNzdXJlX2xvc3M6IHsgdmFsdWU6IDAuOCwgdW5pdDogJ2luX3dnX3Blcl8xMDBmdCcgfVxuICAgIH0sXG4gICAgY29tcGxpYW5jZToge1xuICAgICAgc21hY25hOiB7XG4gICAgICAgIHZlbG9jaXR5OiB7IHBhc3NlZDogdHJ1ZSwgdmFsdWU6IDE0MDAuMCwgbGltaXQ6IDI1MDAgfVxuICAgICAgfVxuICAgIH0sXG4gICAgLi4ub3ZlcnJpZGVzXG4gIH0pLFxuICBcbiAgY29tcGxpYW5jZTogKG92ZXJyaWRlczogUGFydGlhbDxhbnk+ID0ge30pID0+ICh7XG4gICAgc3VjY2VzczogdHJ1ZSxcbiAgICBvdmVyYWxsX2NvbXBsaWFuY2U6ICdjb21wbGlhbnQnLFxuICAgIHZhbGlkYXRpb25fcmVzdWx0czoge1xuICAgICAgc21hY25hOiB7IHN0YXR1czogJ2NvbXBsaWFudCcsIHNjb3JlOiA5NSB9LFxuICAgICAgYXNocmFlOiB7IHN0YXR1czogJ2NvbXBsaWFudCcsIHNjb3JlOiA4OCB9XG4gICAgfSxcbiAgICAuLi5vdmVycmlkZXNcbiAgfSlcbn07XG5cbi8qKlxuICogSW50ZWdyYXRpb24gdGVzdCBlbnZpcm9ubWVudCBzZXR1cFxuICovXG5leHBvcnQgY29uc3Qgc2V0dXBJbnRlZ3JhdGlvblRlc3RFbnZpcm9ubWVudCA9ICgpID0+IHtcbiAgLy8gU2V0IHRlc3QgZW52aXJvbm1lbnQgdmFyaWFibGVzXG4gIHByb2Nlc3MuZW52Lk5PREVfRU5WID0gJ3Rlc3QnO1xuICBwcm9jZXNzLmVudi5URVNUSU5HID0gJ3RydWUnO1xuICBwcm9jZXNzLmVudi5BUElfQkFTRV9VUkwgPSAnaHR0cDovLzEyNy4wLjAuMTo1MDAwL2FwaSc7XG4gIFxuICAvLyBDb25maWd1cmUgY29uc29sZSBmb3IgdGVzdCBlbnZpcm9ubWVudFxuICBjb25zdCBvcmlnaW5hbENvbnNvbGVFcnJvciA9IGNvbnNvbGUuZXJyb3I7XG4gIGNvbnNvbGUuZXJyb3IgPSAoLi4uYXJnczogYW55W10pID0+IHtcbiAgICAvLyBTdXBwcmVzcyBleHBlY3RlZCBSZWFjdCB3YXJuaW5ncyBpbiB0ZXN0c1xuICAgIGlmIChcbiAgICAgIHR5cGVvZiBhcmdzWzBdID09PSAnc3RyaW5nJyAmJlxuICAgICAgYXJnc1swXS5pbmNsdWRlcygnV2FybmluZzogUmVhY3RET00ucmVuZGVyIGlzIG5vIGxvbmdlciBzdXBwb3J0ZWQnKVxuICAgICkge1xuICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICBvcmlnaW5hbENvbnNvbGVFcnJvci5jYWxsKGNvbnNvbGUsIC4uLmFyZ3MpO1xuICB9O1xuICBcbiAgcmV0dXJuIHtcbiAgICBjbGVhbnVwOiAoKSA9PiB7XG4gICAgICBjb25zb2xlLmVycm9yID0gb3JpZ2luYWxDb25zb2xlRXJyb3I7XG4gICAgfVxuICB9O1xufTtcblxuLyoqXG4gKiBNb2NrIG5ldHdvcmsgY29uZGl0aW9ucyBmb3Igb2ZmbGluZSB0ZXN0aW5nXG4gKi9cbmV4cG9ydCBjb25zdCBtb2NrTmV0d29ya0NvbmRpdGlvbnMgPSB7XG4gIG9ubGluZTogKCkgPT4ge1xuICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShuYXZpZ2F0b3IsICdvbkxpbmUnLCB7XG4gICAgICB3cml0YWJsZTogdHJ1ZSxcbiAgICAgIHZhbHVlOiB0cnVlXG4gICAgfSk7XG4gIH0sXG4gIFxuICBvZmZsaW5lOiAoKSA9PiB7XG4gICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KG5hdmlnYXRvciwgJ29uTGluZScsIHtcbiAgICAgIHdyaXRhYmxlOiB0cnVlLFxuICAgICAgdmFsdWU6IGZhbHNlXG4gICAgfSk7XG4gIH0sXG4gIFxuICBzbG93Q29ubmVjdGlvbjogKCkgPT4ge1xuICAgIC8vIE1vY2sgc2xvdyBuZXR3b3JrIGJ5IGFkZGluZyBkZWxheXMgdG8gZmV0Y2hcbiAgICBjb25zdCBvcmlnaW5hbEZldGNoID0gZ2xvYmFsLmZldGNoO1xuICAgIGdsb2JhbC5mZXRjaCA9IGplc3QuZm4oKC4uLmFyZ3MpID0+IHtcbiAgICAgIHJldHVybiBuZXcgUHJvbWlzZShyZXNvbHZlID0+IHtcbiAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgICAgcmVzb2x2ZShvcmlnaW5hbEZldGNoKC4uLmFyZ3MpKTtcbiAgICAgICAgfSwgMjAwMCk7IC8vIDIgc2Vjb25kIGRlbGF5XG4gICAgICB9KTtcbiAgICB9KTtcbiAgfVxufTtcblxuLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cbi8vIEdsb2JhbCBUZXN0IFNldHVwXG4vLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuXG5iZWZvcmVFYWNoKCgpID0+IHtcbiAgLy8gQ2xlYXIgYWxsIG1vY2tzIGJlZm9yZSBlYWNoIHRlc3RcbiAgamVzdC5jbGVhckFsbE1vY2tzKCk7XG4gIFxuICAvLyBSZXNldCBsb2NhbFN0b3JhZ2VcbiAgbW9ja0xvY2FsU3RvcmFnZS5jbGVhcigpO1xuICBcbiAgLy8gUmVzZXQgZmV0Y2ggbW9ja1xuICAoZ2xvYmFsLmZldGNoIGFzIGplc3QuTW9ja2VkRnVuY3Rpb248dHlwZW9mIGZldGNoPikubW9ja0NsZWFyKCk7XG4gIFxuICAvLyBSZXNldCBuZXR3b3JrIHRvIG9ubGluZVxuICBtb2NrTmV0d29ya0NvbmRpdGlvbnMub25saW5lKCk7XG59KTtcblxuYWZ0ZXJFYWNoKCgpID0+IHtcbiAgLy8gQ2xlYW4gdXAgYW55IHRpbWVyc1xuICBqZXN0LmNsZWFyQWxsVGltZXJzKCk7XG4gIFxuICAvLyBSZXNldCBtb2R1bGVzXG4gIGplc3QucmVzZXRNb2R1bGVzKCk7XG59KTtcblxuLy8gU2V0dXAgaW50ZWdyYXRpb24gdGVzdCBlbnZpcm9ubWVudFxuY29uc3QgdGVzdEVudiA9IHNldHVwSW50ZWdyYXRpb25UZXN0RW52aXJvbm1lbnQoKTtcblxuLy8gQ2xlYW51cCBhZnRlciBhbGwgdGVzdHNcbmFmdGVyQWxsKCgpID0+IHtcbiAgdGVzdEVudi5jbGVhbnVwKCk7XG59KTtcblxuLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cbi8vIEN1c3RvbSBNYXRjaGVyc1xuLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cblxuZGVjbGFyZSBnbG9iYWwge1xuICBuYW1lc3BhY2UgamVzdCB7XG4gICAgaW50ZXJmYWNlIE1hdGNoZXJzPFI+IHtcbiAgICAgIHRvQmVWYWxpZEhWQUNDYWxjdWxhdGlvbigpOiBSO1xuICAgICAgdG9CZUNvbXBsaWFudFdpdGhTdGFuZGFyZHMoc3RhbmRhcmRzOiBzdHJpbmdbXSk6IFI7XG4gICAgICB0b0hhdmVPZmZsaW5lQ2FwYWJpbGl0eSgpOiBSO1xuICAgIH1cbiAgfVxufVxuXG4vLyBDdXN0b20gbWF0Y2hlciBmb3IgSFZBQyBjYWxjdWxhdGlvbiB2YWxpZGF0aW9uXG5leHBlY3QuZXh0ZW5kKHtcbiAgdG9CZVZhbGlkSFZBQ0NhbGN1bGF0aW9uKHJlY2VpdmVkKSB7XG4gICAgY29uc3QgcGFzcyA9IHJlY2VpdmVkICYmXG4gICAgICByZWNlaXZlZC5zdWNjZXNzID09PSB0cnVlICYmXG4gICAgICByZWNlaXZlZC5yZXN1bHRzICYmXG4gICAgICB0eXBlb2YgcmVjZWl2ZWQucmVzdWx0cyA9PT0gJ29iamVjdCc7XG4gICAgXG4gICAgcmV0dXJuIHtcbiAgICAgIG1lc3NhZ2U6ICgpID0+IHBhc3NcbiAgICAgICAgPyBgRXhwZWN0ZWQgJHtyZWNlaXZlZH0gbm90IHRvIGJlIGEgdmFsaWQgSFZBQyBjYWxjdWxhdGlvbmBcbiAgICAgICAgOiBgRXhwZWN0ZWQgJHtyZWNlaXZlZH0gdG8gYmUgYSB2YWxpZCBIVkFDIGNhbGN1bGF0aW9uIHdpdGggc3VjY2Vzcz10cnVlIGFuZCByZXN1bHRzIG9iamVjdGAsXG4gICAgICBwYXNzXG4gICAgfTtcbiAgfSxcbiAgXG4gIHRvQmVDb21wbGlhbnRXaXRoU3RhbmRhcmRzKHJlY2VpdmVkLCBzdGFuZGFyZHMpIHtcbiAgICBjb25zdCBwYXNzID0gcmVjZWl2ZWQgJiZcbiAgICAgIHJlY2VpdmVkLmNvbXBsaWFuY2UgJiZcbiAgICAgIHN0YW5kYXJkcy5ldmVyeSgoc3RhbmRhcmQ6IHN0cmluZykgPT4gXG4gICAgICAgIHJlY2VpdmVkLmNvbXBsaWFuY2Vbc3RhbmRhcmQudG9Mb3dlckNhc2UoKV0gJiZcbiAgICAgICAgcmVjZWl2ZWQuY29tcGxpYW5jZVtzdGFuZGFyZC50b0xvd2VyQ2FzZSgpXS5zdGF0dXMgPT09ICdjb21wbGlhbnQnXG4gICAgICApO1xuICAgIFxuICAgIHJldHVybiB7XG4gICAgICBtZXNzYWdlOiAoKSA9PiBwYXNzXG4gICAgICAgID8gYEV4cGVjdGVkICR7cmVjZWl2ZWR9IG5vdCB0byBiZSBjb21wbGlhbnQgd2l0aCBzdGFuZGFyZHMgJHtzdGFuZGFyZHMuam9pbignLCAnKX1gXG4gICAgICAgIDogYEV4cGVjdGVkICR7cmVjZWl2ZWR9IHRvIGJlIGNvbXBsaWFudCB3aXRoIHN0YW5kYXJkcyAke3N0YW5kYXJkcy5qb2luKCcsICcpfWAsXG4gICAgICBwYXNzXG4gICAgfTtcbiAgfSxcbiAgXG4gIHRvSGF2ZU9mZmxpbmVDYXBhYmlsaXR5KHJlY2VpdmVkKSB7XG4gICAgY29uc3QgcGFzcyA9IHJlY2VpdmVkICYmXG4gICAgICAocmVjZWl2ZWQuY2FsY3VsYXRlZF9vZmZsaW5lID09PSB0cnVlIHx8IHJlY2VpdmVkLm9mZmxpbmVfY2FwYWJsZSA9PT0gdHJ1ZSk7XG4gICAgXG4gICAgcmV0dXJuIHtcbiAgICAgIG1lc3NhZ2U6ICgpID0+IHBhc3NcbiAgICAgICAgPyBgRXhwZWN0ZWQgJHtyZWNlaXZlZH0gbm90IHRvIGhhdmUgb2ZmbGluZSBjYXBhYmlsaXR5YFxuICAgICAgICA6IGBFeHBlY3RlZCAke3JlY2VpdmVkfSB0byBoYXZlIG9mZmxpbmUgY2FwYWJpbGl0eWAsXG4gICAgICBwYXNzXG4gICAgfTtcbiAgfVxufSk7XG4iXSwidmVyc2lvbiI6M30=