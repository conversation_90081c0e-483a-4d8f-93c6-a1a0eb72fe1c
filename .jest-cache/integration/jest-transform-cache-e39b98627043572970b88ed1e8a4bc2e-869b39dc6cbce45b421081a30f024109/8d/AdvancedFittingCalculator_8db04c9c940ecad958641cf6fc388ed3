a9a1c8937a228b8949ef3e60e8aae25b
"use strict";

/**
 * Advanced Fitting Calculator for Phase 3 Duct Physics Implementation
 *
 * This service extends the basic FittingLossCalculator with advanced capabilities including:
 * - Multi-parameter K-factor calculations
 * - Performance curve interpolation
 * - Interaction effects between adjacent fittings
 * - Method selection algorithms
 * - Complex fitting configurations
 *
 * @version 3.0.0
 * <AUTHOR> Suite Development Team
 */
/* istanbul ignore next */
function cov_2f6r1jzz3n() {
  var path = "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\AdvancedFittingCalculator.ts";
  var hash = "033ecd21eb8c2abd4539ea7b1d7e9846a4117eef";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\AdvancedFittingCalculator.ts",
    statementMap: {
      "0": {
        start: {
          line: 15,
          column: 22
        },
        end: {
          line: 25,
          column: 3
        }
      },
      "1": {
        start: {
          line: 16,
          column: 4
        },
        end: {
          line: 16,
          column: 33
        }
      },
      "2": {
        start: {
          line: 16,
          column: 26
        },
        end: {
          line: 16,
          column: 33
        }
      },
      "3": {
        start: {
          line: 17,
          column: 15
        },
        end: {
          line: 17,
          column: 52
        }
      },
      "4": {
        start: {
          line: 18,
          column: 4
        },
        end: {
          line: 20,
          column: 5
        }
      },
      "5": {
        start: {
          line: 19,
          column: 6
        },
        end: {
          line: 19,
          column: 68
        }
      },
      "6": {
        start: {
          line: 19,
          column: 51
        },
        end: {
          line: 19,
          column: 63
        }
      },
      "7": {
        start: {
          line: 21,
          column: 4
        },
        end: {
          line: 21,
          column: 39
        }
      },
      "8": {
        start: {
          line: 23,
          column: 4
        },
        end: {
          line: 23,
          column: 33
        }
      },
      "9": {
        start: {
          line: 23,
          column: 26
        },
        end: {
          line: 23,
          column: 33
        }
      },
      "10": {
        start: {
          line: 24,
          column: 4
        },
        end: {
          line: 24,
          column: 17
        }
      },
      "11": {
        start: {
          line: 26,
          column: 25
        },
        end: {
          line: 30,
          column: 2
        }
      },
      "12": {
        start: {
          line: 27,
          column: 4
        },
        end: {
          line: 27,
          column: 72
        }
      },
      "13": {
        start: {
          line: 29,
          column: 4
        },
        end: {
          line: 29,
          column: 21
        }
      },
      "14": {
        start: {
          line: 31,
          column: 19
        },
        end: {
          line: 47,
          column: 4
        }
      },
      "15": {
        start: {
          line: 32,
          column: 18
        },
        end: {
          line: 39,
          column: 5
        }
      },
      "16": {
        start: {
          line: 33,
          column: 8
        },
        end: {
          line: 37,
          column: 10
        }
      },
      "17": {
        start: {
          line: 34,
          column: 21
        },
        end: {
          line: 34,
          column: 23
        }
      },
      "18": {
        start: {
          line: 35,
          column: 12
        },
        end: {
          line: 35,
          column: 95
        }
      },
      "19": {
        start: {
          line: 35,
          column: 29
        },
        end: {
          line: 35,
          column: 95
        }
      },
      "20": {
        start: {
          line: 35,
          column: 77
        },
        end: {
          line: 35,
          column: 95
        }
      },
      "21": {
        start: {
          line: 36,
          column: 12
        },
        end: {
          line: 36,
          column: 22
        }
      },
      "22": {
        start: {
          line: 38,
          column: 8
        },
        end: {
          line: 38,
          column: 26
        }
      },
      "23": {
        start: {
          line: 40,
          column: 4
        },
        end: {
          line: 46,
          column: 6
        }
      },
      "24": {
        start: {
          line: 41,
          column: 8
        },
        end: {
          line: 41,
          column: 46
        }
      },
      "25": {
        start: {
          line: 41,
          column: 35
        },
        end: {
          line: 41,
          column: 46
        }
      },
      "26": {
        start: {
          line: 42,
          column: 21
        },
        end: {
          line: 42,
          column: 23
        }
      },
      "27": {
        start: {
          line: 43,
          column: 8
        },
        end: {
          line: 43,
          column: 137
        }
      },
      "28": {
        start: {
          line: 43,
          column: 25
        },
        end: {
          line: 43,
          column: 137
        }
      },
      "29": {
        start: {
          line: 43,
          column: 38
        },
        end: {
          line: 43,
          column: 50
        }
      },
      "30": {
        start: {
          line: 43,
          column: 56
        },
        end: {
          line: 43,
          column: 57
        }
      },
      "31": {
        start: {
          line: 43,
          column: 78
        },
        end: {
          line: 43,
          column: 137
        }
      },
      "32": {
        start: {
          line: 43,
          column: 102
        },
        end: {
          line: 43,
          column: 137
        }
      },
      "33": {
        start: {
          line: 44,
          column: 8
        },
        end: {
          line: 44,
          column: 40
        }
      },
      "34": {
        start: {
          line: 45,
          column: 8
        },
        end: {
          line: 45,
          column: 22
        }
      },
      "35": {
        start: {
          line: 48,
          column: 0
        },
        end: {
          line: 48,
          column: 62
        }
      },
      "36": {
        start: {
          line: 49,
          column: 0
        },
        end: {
          line: 49,
          column: 43
        }
      },
      "37": {
        start: {
          line: 50,
          column: 11
        },
        end: {
          line: 50,
          column: 38
        }
      },
      "38": {
        start: {
          line: 51,
          column: 13
        },
        end: {
          line: 51,
          column: 42
        }
      },
      "39": {
        start: {
          line: 52,
          column: 32
        },
        end: {
          line: 52,
          column: 66
        }
      },
      "40": {
        start: {
          line: 53,
          column: 31
        },
        end: {
          line: 53,
          column: 70
        }
      },
      "41": {
        start: {
          line: 59,
          column: 8
        },
        end: {
          line: 68,
          column: 9
        }
      },
      "42": {
        start: {
          line: 60,
          column: 12
        },
        end: {
          line: 67,
          column: 13
        }
      },
      "43": {
        start: {
          line: 61,
          column: 36
        },
        end: {
          line: 61,
          column: 80
        }
      },
      "44": {
        start: {
          line: 62,
          column: 16
        },
        end: {
          line: 62,
          column: 68
        }
      },
      "45": {
        start: {
          line: 65,
          column: 16
        },
        end: {
          line: 65,
          column: 78
        }
      },
      "46": {
        start: {
          line: 66,
          column: 16
        },
        end: {
          line: 66,
          column: 77
        }
      },
      "47": {
        start: {
          line: 69,
          column: 8
        },
        end: {
          line: 69,
          column: 41
        }
      },
      "48": {
        start: {
          line: 76,
          column: 8
        },
        end: {
          line: 76,
          column: 67
        }
      },
      "49": {
        start: {
          line: 78,
          column: 23
        },
        end: {
          line: 78,
          column: 75
        }
      },
      "50": {
        start: {
          line: 80,
          column: 25
        },
        end: {
          line: 80,
          column: 87
        }
      },
      "51": {
        start: {
          line: 82,
          column: 30
        },
        end: {
          line: 82,
          column: 91
        }
      },
      "52": {
        start: {
          line: 84,
          column: 35
        },
        end: {
          line: 86,
          column: 49
        }
      },
      "53": {
        start: {
          line: 88,
          column: 34
        },
        end: {
          line: 88,
          column: 91
        }
      },
      "54": {
        start: {
          line: 90,
          column: 35
        },
        end: {
          line: 90,
          column: 110
        }
      },
      "55": {
        start: {
          line: 92,
          column: 34
        },
        end: {
          line: 92,
          column: 97
        }
      },
      "56": {
        start: {
          line: 94,
          column: 32
        },
        end: {
          line: 94,
          column: 123
        }
      },
      "57": {
        start: {
          line: 96,
          column: 8
        },
        end: {
          line: 106,
          column: 10
        }
      },
      "58": {
        start: {
          line: 100,
          column: 58
        },
        end: {
          line: 100,
          column: 67
        }
      },
      "59": {
        start: {
          line: 113,
          column: 8
        },
        end: {
          line: 116,
          column: 9
        }
      },
      "60": {
        start: {
          line: 115,
          column: 12
        },
        end: {
          line: 115,
          column: 72
        }
      },
      "61": {
        start: {
          line: 118,
          column: 8
        },
        end: {
          line: 121,
          column: 9
        }
      },
      "62": {
        start: {
          line: 120,
          column: 12
        },
        end: {
          line: 120,
          column: 76
        }
      },
      "63": {
        start: {
          line: 123,
          column: 8
        },
        end: {
          line: 127,
          column: 9
        }
      },
      "64": {
        start: {
          line: 126,
          column: 12
        },
        end: {
          line: 126,
          column: 78
        }
      },
      "65": {
        start: {
          line: 129,
          column: 8
        },
        end: {
          line: 129,
          column: 72
        }
      },
      "66": {
        start: {
          line: 135,
          column: 33
        },
        end: {
          line: 135,
          column: 115
        }
      },
      "67": {
        start: {
          line: 136,
          column: 8
        },
        end: {
          line: 147,
          column: 9
        }
      },
      "68": {
        start: {
          line: 138,
          column: 16
        },
        end: {
          line: 138,
          column: 81
        }
      },
      "69": {
        start: {
          line: 140,
          column: 16
        },
        end: {
          line: 140,
          column: 98
        }
      },
      "70": {
        start: {
          line: 142,
          column: 16
        },
        end: {
          line: 142,
          column: 100
        }
      },
      "71": {
        start: {
          line: 144,
          column: 16
        },
        end: {
          line: 144,
          column: 94
        }
      },
      "72": {
        start: {
          line: 146,
          column: 16
        },
        end: {
          line: 146,
          column: 77
        }
      },
      "73": {
        start: {
          line: 153,
          column: 8
        },
        end: {
          line: 153,
          column: 85
        }
      },
      "74": {
        start: {
          line: 159,
          column: 22
        },
        end: {
          line: 159,
          column: 72
        }
      },
      "75": {
        start: {
          line: 161,
          column: 8
        },
        end: {
          line: 165,
          column: 9
        }
      },
      "76": {
        start: {
          line: 162,
          column: 35
        },
        end: {
          line: 162,
          column: 103
        }
      },
      "77": {
        start: {
          line: 163,
          column: 31
        },
        end: {
          line: 163,
          column: 92
        }
      },
      "78": {
        start: {
          line: 164,
          column: 12
        },
        end: {
          line: 164,
          column: 34
        }
      },
      "79": {
        start: {
          line: 167,
          column: 8
        },
        end: {
          line: 170,
          column: 9
        }
      },
      "80": {
        start: {
          line: 168,
          column: 39
        },
        end: {
          line: 168,
          column: 161
        }
      },
      "81": {
        start: {
          line: 169,
          column: 12
        },
        end: {
          line: 169,
          column: 42
        }
      },
      "82": {
        start: {
          line: 172,
          column: 8
        },
        end: {
          line: 174,
          column: 9
        }
      },
      "83": {
        start: {
          line: 173,
          column: 12
        },
        end: {
          line: 173,
          column: 51
        }
      },
      "84": {
        start: {
          line: 175,
          column: 8
        },
        end: {
          line: 175,
          column: 42
        }
      },
      "85": {
        start: {
          line: 181,
          column: 8
        },
        end: {
          line: 183,
          column: 9
        }
      },
      "86": {
        start: {
          line: 182,
          column: 12
        },
        end: {
          line: 182,
          column: 75
        }
      },
      "87": {
        start: {
          line: 185,
          column: 22
        },
        end: {
          line: 185,
          column: 111
        }
      },
      "88": {
        start: {
          line: 187,
          column: 31
        },
        end: {
          line: 187,
          column: 94
        }
      },
      "89": {
        start: {
          line: 189,
          column: 29
        },
        end: {
          line: 189,
          column: 84
        }
      },
      "90": {
        start: {
          line: 190,
          column: 8
        },
        end: {
          line: 190,
          column: 28
        }
      },
      "91": {
        start: {
          line: 197,
          column: 8
        },
        end: {
          line: 197,
          column: 90
        }
      },
      "92": {
        start: {
          line: 204,
          column: 8
        },
        end: {
          line: 206,
          column: 9
        }
      },
      "93": {
        start: {
          line: 205,
          column: 12
        },
        end: {
          line: 205,
          column: 165
        }
      },
      "94": {
        start: {
          line: 207,
          column: 8
        },
        end: {
          line: 225,
          column: 9
        }
      },
      "95": {
        start: {
          line: 209,
          column: 16
        },
        end: {
          line: 209,
          column: 96
        }
      },
      "96": {
        start: {
          line: 211,
          column: 29
        },
        end: {
          line: 211,
          column: 30
        }
      },
      "97": {
        start: {
          line: 212,
          column: 16
        },
        end: {
          line: 214,
          column: 17
        }
      },
      "98": {
        start: {
          line: 212,
          column: 29
        },
        end: {
          line: 212,
          column: 30
        }
      },
      "99": {
        start: {
          line: 213,
          column: 20
        },
        end: {
          line: 213,
          column: 87
        }
      },
      "100": {
        start: {
          line: 215,
          column: 16
        },
        end: {
          line: 215,
          column: 30
        }
      },
      "101": {
        start: {
          line: 218,
          column: 16
        },
        end: {
          line: 218,
          column: 106
        }
      },
      "102": {
        start: {
          line: 222,
          column: 16
        },
        end: {
          line: 222,
          column: 27
        }
      },
      "103": {
        start: {
          line: 224,
          column: 16
        },
        end: {
          line: 224,
          column: 93
        }
      },
      "104": {
        start: {
          line: 231,
          column: 8
        },
        end: {
          line: 254,
          column: 9
        }
      },
      "105": {
        start: {
          line: 233,
          column: 16
        },
        end: {
          line: 233,
          column: 47
        }
      },
      "106": {
        start: {
          line: 235,
          column: 16
        },
        end: {
          line: 235,
          column: 53
        }
      },
      "107": {
        start: {
          line: 237,
          column: 16
        },
        end: {
          line: 237,
          column: 49
        }
      },
      "108": {
        start: {
          line: 239,
          column: 16
        },
        end: {
          line: 239,
          column: 50
        }
      },
      "109": {
        start: {
          line: 241,
          column: 16
        },
        end: {
          line: 241,
          column: 47
        }
      },
      "110": {
        start: {
          line: 243,
          column: 16
        },
        end: {
          line: 243,
          column: 128
        }
      },
      "111": {
        start: {
          line: 245,
          column: 34
        },
        end: {
          line: 245,
          column: 119
        }
      },
      "112": {
        start: {
          line: 246,
          column: 35
        },
        end: {
          line: 246,
          column: 121
        }
      },
      "113": {
        start: {
          line: 247,
          column: 16
        },
        end: {
          line: 247,
          column: 46
        }
      },
      "114": {
        start: {
          line: 250,
          column: 16
        },
        end: {
          line: 250,
          column: 112
        }
      },
      "115": {
        start: {
          line: 252,
          column: 16
        },
        end: {
          line: 252,
          column: 89
        }
      },
      "116": {
        start: {
          line: 253,
          column: 16
        },
        end: {
          line: 253,
          column: 27
        }
      },
      "117": {
        start: {
          line: 260,
          column: 8
        },
        end: {
          line: 262,
          column: 9
        }
      },
      "118": {
        start: {
          line: 261,
          column: 12
        },
        end: {
          line: 261,
          column: 23
        }
      },
      "119": {
        start: {
          line: 263,
          column: 8
        },
        end: {
          line: 280,
          column: 9
        }
      },
      "120": {
        start: {
          line: 266,
          column: 16
        },
        end: {
          line: 266,
          column: 114
        }
      },
      "121": {
        start: {
          line: 269,
          column: 16
        },
        end: {
          line: 269,
          column: 105
        }
      },
      "122": {
        start: {
          line: 272,
          column: 29
        },
        end: {
          line: 272,
          column: 30
        }
      },
      "123": {
        start: {
          line: 273,
          column: 16
        },
        end: {
          line: 275,
          column: 17
        }
      },
      "124": {
        start: {
          line: 273,
          column: 29
        },
        end: {
          line: 273,
          column: 30
        }
      },
      "125": {
        start: {
          line: 274,
          column: 20
        },
        end: {
          line: 274,
          column: 87
        }
      },
      "126": {
        start: {
          line: 276,
          column: 16
        },
        end: {
          line: 276,
          column: 30
        }
      },
      "127": {
        start: {
          line: 279,
          column: 16
        },
        end: {
          line: 279,
          column: 27
        }
      },
      "128": {
        start: {
          line: 288,
          column: 8
        },
        end: {
          line: 288,
          column: 25
        }
      },
      "129": {
        start: {
          line: 294,
          column: 23
        },
        end: {
          line: 294,
          column: 65
        }
      },
      "130": {
        start: {
          line: 294,
          column: 55
        },
        end: {
          line: 294,
          column: 64
        }
      },
      "131": {
        start: {
          line: 296,
          column: 8
        },
        end: {
          line: 298,
          column: 9
        }
      },
      "132": {
        start: {
          line: 297,
          column: 12
        },
        end: {
          line: 297,
          column: 31
        }
      },
      "133": {
        start: {
          line: 299,
          column: 8
        },
        end: {
          line: 309,
          column: 9
        }
      },
      "134": {
        start: {
          line: 300,
          column: 12
        },
        end: {
          line: 308,
          column: 13
        }
      },
      "135": {
        start: {
          line: 302,
          column: 32
        },
        end: {
          line: 302,
          column: 48
        }
      },
      "136": {
        start: {
          line: 303,
          column: 30
        },
        end: {
          line: 303,
          column: 91
        }
      },
      "137": {
        start: {
          line: 304,
          column: 16
        },
        end: {
          line: 304,
          column: 78
        }
      },
      "138": {
        start: {
          line: 307,
          column: 16
        },
        end: {
          line: 307,
          column: 51
        }
      },
      "139": {
        start: {
          line: 311,
          column: 8
        },
        end: {
          line: 317,
          column: 9
        }
      },
      "140": {
        start: {
          line: 311,
          column: 21
        },
        end: {
          line: 311,
          column: 22
        }
      },
      "141": {
        start: {
          line: 312,
          column: 12
        },
        end: {
          line: 316,
          column: 13
        }
      },
      "142": {
        start: {
          line: 314,
          column: 30
        },
        end: {
          line: 314,
          column: 94
        }
      },
      "143": {
        start: {
          line: 315,
          column: 16
        },
        end: {
          line: 315,
          column: 77
        }
      },
      "144": {
        start: {
          line: 318,
          column: 8
        },
        end: {
          line: 318,
          column: 27
        }
      },
      "145": {
        start: {
          line: 324,
          column: 28
        },
        end: {
          line: 324,
          column: 36
        }
      },
      "146": {
        start: {
          line: 325,
          column: 28
        },
        end: {
          line: 325,
          column: 72
        }
      },
      "147": {
        start: {
          line: 327,
          column: 8
        },
        end: {
          line: 330,
          column: 9
        }
      },
      "148": {
        start: {
          line: 328,
          column: 35
        },
        end: {
          line: 328,
          column: 98
        }
      },
      "149": {
        start: {
          line: 329,
          column: 12
        },
        end: {
          line: 329,
          column: 44
        }
      },
      "150": {
        start: {
          line: 332,
          column: 8
        },
        end: {
          line: 335,
          column: 9
        }
      },
      "151": {
        start: {
          line: 333,
          column: 38
        },
        end: {
          line: 333,
          column: 71
        }
      },
      "152": {
        start: {
          line: 334,
          column: 12
        },
        end: {
          line: 334,
          column: 47
        }
      },
      "153": {
        start: {
          line: 337,
          column: 8
        },
        end: {
          line: 339,
          column: 9
        }
      },
      "154": {
        start: {
          line: 338,
          column: 12
        },
        end: {
          line: 338,
          column: 33
        }
      },
      "155": {
        start: {
          line: 340,
          column: 8
        },
        end: {
          line: 340,
          column: 29
        }
      },
      "156": {
        start: {
          line: 347,
          column: 29
        },
        end: {
          line: 347,
          column: 31
        }
      },
      "157": {
        start: {
          line: 348,
          column: 26
        },
        end: {
          line: 348,
          column: 74
        }
      },
      "158": {
        start: {
          line: 349,
          column: 8
        },
        end: {
          line: 349,
          column: 36
        }
      },
      "159": {
        start: {
          line: 355,
          column: 33
        },
        end: {
          line: 355,
          column: 81
        }
      },
      "160": {
        start: {
          line: 356,
          column: 35
        },
        end: {
          line: 356,
          column: 85
        }
      },
      "161": {
        start: {
          line: 357,
          column: 32
        },
        end: {
          line: 357,
          column: 35
        }
      },
      "162": {
        start: {
          line: 358,
          column: 29
        },
        end: {
          line: 358,
          column: 31
        }
      },
      "163": {
        start: {
          line: 360,
          column: 8
        },
        end: {
          line: 364,
          column: 9
        }
      },
      "164": {
        start: {
          line: 361,
          column: 32
        },
        end: {
          line: 361,
          column: 83
        }
      },
      "165": {
        start: {
          line: 362,
          column: 12
        },
        end: {
          line: 362,
          column: 52
        }
      },
      "166": {
        start: {
          line: 363,
          column: 12
        },
        end: {
          line: 363,
          column: 43
        }
      },
      "167": {
        start: {
          line: 366,
          column: 8
        },
        end: {
          line: 370,
          column: 9
        }
      },
      "168": {
        start: {
          line: 367,
          column: 32
        },
        end: {
          line: 367,
          column: 87
        }
      },
      "169": {
        start: {
          line: 368,
          column: 12
        },
        end: {
          line: 368,
          column: 52
        }
      },
      "170": {
        start: {
          line: 369,
          column: 12
        },
        end: {
          line: 369,
          column: 43
        }
      },
      "171": {
        start: {
          line: 371,
          column: 8
        },
        end: {
          line: 375,
          column: 10
        }
      },
      "172": {
        start: {
          line: 374,
          column: 62
        },
        end: {
          line: 374,
          column: 93
        }
      },
      "173": {
        start: {
          line: 381,
          column: 8
        },
        end: {
          line: 385,
          column: 10
        }
      },
      "174": {
        start: {
          line: 393,
          column: 21
        },
        end: {
          line: 393,
          column: 24
        }
      },
      "175": {
        start: {
          line: 394,
          column: 27
        },
        end: {
          line: 394,
          column: 32
        }
      },
      "176": {
        start: {
          line: 396,
          column: 8
        },
        end: {
          line: 410,
          column: 9
        }
      },
      "177": {
        start: {
          line: 397,
          column: 12
        },
        end: {
          line: 409,
          column: 13
        }
      },
      "178": {
        start: {
          line: 398,
          column: 16
        },
        end: {
          line: 398,
          column: 42
        }
      },
      "179": {
        start: {
          line: 399,
          column: 16
        },
        end: {
          line: 407,
          column: 17
        }
      },
      "180": {
        start: {
          line: 400,
          column: 20
        },
        end: {
          line: 400,
          column: 42
        }
      },
      "181": {
        start: {
          line: 402,
          column: 21
        },
        end: {
          line: 407,
          column: 17
        }
      },
      "182": {
        start: {
          line: 403,
          column: 20
        },
        end: {
          line: 403,
          column: 44
        }
      },
      "183": {
        start: {
          line: 406,
          column: 20
        },
        end: {
          line: 406,
          column: 41
        }
      },
      "184": {
        start: {
          line: 408,
          column: 16
        },
        end: {
          line: 408,
          column: 22
        }
      },
      "185": {
        start: {
          line: 411,
          column: 8
        },
        end: {
          line: 417,
          column: 10
        }
      },
      "186": {
        start: {
          line: 424,
          column: 8
        },
        end: {
          line: 430,
          column: 10
        }
      },
      "187": {
        start: {
          line: 436,
          column: 33
        },
        end: {
          line: 436,
          column: 115
        }
      },
      "188": {
        start: {
          line: 438,
          column: 27
        },
        end: {
          line: 438,
          column: 104
        }
      },
      "189": {
        start: {
          line: 440,
          column: 32
        },
        end: {
          line: 440,
          column: 85
        }
      },
      "190": {
        start: {
          line: 442,
          column: 27
        },
        end: {
          line: 442,
          column: 92
        }
      },
      "191": {
        start: {
          line: 444,
          column: 31
        },
        end: {
          line: 444,
          column: 95
        }
      },
      "192": {
        start: {
          line: 446,
          column: 33
        },
        end: {
          line: 446,
          column: 106
        }
      },
      "193": {
        start: {
          line: 447,
          column: 8
        },
        end: {
          line: 453,
          column: 10
        }
      },
      "194": {
        start: {
          line: 460,
          column: 30
        },
        end: {
          line: 460,
          column: 77
        }
      },
      "195": {
        start: {
          line: 461,
          column: 32
        },
        end: {
          line: 461,
          column: 102
        }
      },
      "196": {
        start: {
          line: 462,
          column: 27
        },
        end: {
          line: 462,
          column: 28
        }
      },
      "197": {
        start: {
          line: 463,
          column: 8
        },
        end: {
          line: 468,
          column: 9
        }
      },
      "198": {
        start: {
          line: 464,
          column: 12
        },
        end: {
          line: 464,
          column: 29
        }
      },
      "199": {
        start: {
          line: 466,
          column: 13
        },
        end: {
          line: 468,
          column: 9
        }
      },
      "200": {
        start: {
          line: 467,
          column: 12
        },
        end: {
          line: 467,
          column: 29
        }
      },
      "201": {
        start: {
          line: 469,
          column: 8
        },
        end: {
          line: 469,
          column: 75
        }
      },
      "202": {
        start: {
          line: 477,
          column: 30
        },
        end: {
          line: 477,
          column: 34
        }
      },
      "203": {
        start: {
          line: 478,
          column: 8
        },
        end: {
          line: 478,
          column: 65
        }
      },
      "204": {
        start: {
          line: 485,
          column: 31
        },
        end: {
          line: 485,
          column: 72
        }
      },
      "205": {
        start: {
          line: 486,
          column: 8
        },
        end: {
          line: 489,
          column: 9
        }
      },
      "206": {
        start: {
          line: 488,
          column: 12
        },
        end: {
          line: 488,
          column: 160
        }
      },
      "207": {
        start: {
          line: 491,
          column: 8
        },
        end: {
          line: 493,
          column: 9
        }
      },
      "208": {
        start: {
          line: 492,
          column: 12
        },
        end: {
          line: 492,
          column: 112
        }
      },
      "209": {
        start: {
          line: 495,
          column: 8
        },
        end: {
          line: 497,
          column: 9
        }
      },
      "210": {
        start: {
          line: 496,
          column: 12
        },
        end: {
          line: 496,
          column: 120
        }
      },
      "211": {
        start: {
          line: 503,
          column: 23
        },
        end: {
          line: 503,
          column: 25
        }
      },
      "212": {
        start: {
          line: 504,
          column: 25
        },
        end: {
          line: 504,
          column: 27
        }
      },
      "213": {
        start: {
          line: 506,
          column: 33
        },
        end: {
          line: 506,
          column: 115
        }
      },
      "214": {
        start: {
          line: 507,
          column: 24
        },
        end: {
          line: 507,
          column: 55
        }
      },
      "215": {
        start: {
          line: 508,
          column: 8
        },
        end: {
          line: 515,
          column: 9
        }
      },
      "216": {
        start: {
          line: 509,
          column: 12
        },
        end: {
          line: 514,
          column: 15
        }
      },
      "217": {
        start: {
          line: 516,
          column: 8
        },
        end: {
          line: 523,
          column: 9
        }
      },
      "218": {
        start: {
          line: 517,
          column: 12
        },
        end: {
          line: 522,
          column: 15
        }
      },
      "219": {
        start: {
          line: 525,
          column: 8
        },
        end: {
          line: 546,
          column: 9
        }
      },
      "220": {
        start: {
          line: 526,
          column: 35
        },
        end: {
          line: 526,
          column: 107
        }
      },
      "221": {
        start: {
          line: 527,
          column: 31
        },
        end: {
          line: 527,
          column: 92
        }
      },
      "222": {
        start: {
          line: 528,
          column: 12
        },
        end: {
          line: 545,
          column: 13
        }
      },
      "223": {
        start: {
          line: 529,
          column: 16
        },
        end: {
          line: 544,
          column: 17
        }
      },
      "224": {
        start: {
          line: 530,
          column: 20
        },
        end: {
          line: 535,
          column: 23
        }
      },
      "225": {
        start: {
          line: 537,
          column: 21
        },
        end: {
          line: 544,
          column: 17
        }
      },
      "226": {
        start: {
          line: 538,
          column: 20
        },
        end: {
          line: 543,
          column: 23
        }
      },
      "227": {
        start: {
          line: 548,
          column: 34
        },
        end: {
          line: 548,
          column: 78
        }
      },
      "228": {
        start: {
          line: 549,
          column: 33
        },
        end: {
          line: 549,
          column: 115
        }
      },
      "229": {
        start: {
          line: 550,
          column: 8
        },
        end: {
          line: 557,
          column: 9
        }
      },
      "230": {
        start: {
          line: 551,
          column: 12
        },
        end: {
          line: 556,
          column: 15
        }
      },
      "231": {
        start: {
          line: 558,
          column: 33
        },
        end: {
          line: 563,
          column: 9
        }
      },
      "232": {
        start: {
          line: 564,
          column: 8
        },
        end: {
          line: 569,
          column: 10
        }
      },
      "233": {
        start: {
          line: 575,
          column: 8
        },
        end: {
          line: 594,
          column: 9
        }
      },
      "234": {
        start: {
          line: 577,
          column: 16
        },
        end: {
          line: 577,
          column: 63
        }
      },
      "235": {
        start: {
          line: 579,
          column: 16
        },
        end: {
          line: 579,
          column: 63
        }
      },
      "236": {
        start: {
          line: 581,
          column: 16
        },
        end: {
          line: 581,
          column: 64
        }
      },
      "237": {
        start: {
          line: 583,
          column: 16
        },
        end: {
          line: 583,
          column: 64
        }
      },
      "238": {
        start: {
          line: 585,
          column: 16
        },
        end: {
          line: 585,
          column: 49
        }
      },
      "239": {
        start: {
          line: 587,
          column: 16
        },
        end: {
          line: 587,
          column: 49
        }
      },
      "240": {
        start: {
          line: 589,
          column: 16
        },
        end: {
          line: 589,
          column: 89
        }
      },
      "241": {
        start: {
          line: 591,
          column: 16
        },
        end: {
          line: 591,
          column: 90
        }
      },
      "242": {
        start: {
          line: 593,
          column: 16
        },
        end: {
          line: 593,
          column: 29
        }
      },
      "243": {
        start: {
          line: 600,
          column: 32
        },
        end: {
          line: 600,
          column: 34
        }
      },
      "244": {
        start: {
          line: 602,
          column: 8
        },
        end: {
          line: 610,
          column: 9
        }
      },
      "245": {
        start: {
          line: 603,
          column: 12
        },
        end: {
          line: 609,
          column: 15
        }
      },
      "246": {
        start: {
          line: 611,
          column: 8
        },
        end: {
          line: 619,
          column: 9
        }
      },
      "247": {
        start: {
          line: 612,
          column: 12
        },
        end: {
          line: 618,
          column: 15
        }
      },
      "248": {
        start: {
          line: 621,
          column: 31
        },
        end: {
          line: 621,
          column: 72
        }
      },
      "249": {
        start: {
          line: 622,
          column: 26
        },
        end: {
          line: 622,
          column: 76
        }
      },
      "250": {
        start: {
          line: 623,
          column: 8
        },
        end: {
          line: 631,
          column: 9
        }
      },
      "251": {
        start: {
          line: 624,
          column: 12
        },
        end: {
          line: 630,
          column: 15
        }
      },
      "252": {
        start: {
          line: 632,
          column: 8
        },
        end: {
          line: 640,
          column: 9
        }
      },
      "253": {
        start: {
          line: 633,
          column: 12
        },
        end: {
          line: 639,
          column: 15
        }
      },
      "254": {
        start: {
          line: 642,
          column: 8
        },
        end: {
          line: 650,
          column: 9
        }
      },
      "255": {
        start: {
          line: 643,
          column: 12
        },
        end: {
          line: 649,
          column: 15
        }
      },
      "256": {
        start: {
          line: 651,
          column: 8
        },
        end: {
          line: 651,
          column: 31
        }
      },
      "257": {
        start: {
          line: 657,
          column: 21
        },
        end: {
          line: 657,
          column: 52
        }
      },
      "258": {
        start: {
          line: 659,
          column: 8
        },
        end: {
          line: 670,
          column: 9
        }
      },
      "259": {
        start: {
          line: 660,
          column: 29
        },
        end: {
          line: 660,
          column: 58
        }
      },
      "260": {
        start: {
          line: 661,
          column: 12
        },
        end: {
          line: 669,
          column: 13
        }
      },
      "261": {
        start: {
          line: 662,
          column: 29
        },
        end: {
          line: 662,
          column: 47
        }
      },
      "262": {
        start: {
          line: 663,
          column: 16
        },
        end: {
          line: 668,
          column: 17
        }
      },
      "263": {
        start: {
          line: 664,
          column: 35
        },
        end: {
          line: 664,
          column: 51
        }
      },
      "264": {
        start: {
          line: 665,
          column: 20
        },
        end: {
          line: 667,
          column: 21
        }
      },
      "265": {
        start: {
          line: 666,
          column: 24
        },
        end: {
          line: 666,
          column: 38
        }
      },
      "266": {
        start: {
          line: 671,
          column: 8
        },
        end: {
          line: 671,
          column: 20
        }
      },
      "267": {
        start: {
          line: 677,
          column: 21
        },
        end: {
          line: 677,
          column: 52
        }
      },
      "268": {
        start: {
          line: 678,
          column: 25
        },
        end: {
          line: 678,
          column: 27
        }
      },
      "269": {
        start: {
          line: 679,
          column: 8
        },
        end: {
          line: 692,
          column: 9
        }
      },
      "270": {
        start: {
          line: 680,
          column: 29
        },
        end: {
          line: 680,
          column: 58
        }
      },
      "271": {
        start: {
          line: 681,
          column: 12
        },
        end: {
          line: 691,
          column: 13
        }
      },
      "272": {
        start: {
          line: 682,
          column: 29
        },
        end: {
          line: 682,
          column: 47
        }
      },
      "273": {
        start: {
          line: 683,
          column: 16
        },
        end: {
          line: 690,
          column: 17
        }
      },
      "274": {
        start: {
          line: 684,
          column: 35
        },
        end: {
          line: 684,
          column: 51
        }
      },
      "275": {
        start: {
          line: 685,
          column: 20
        },
        end: {
          line: 689,
          column: 23
        }
      },
      "276": {
        start: {
          line: 693,
          column: 8
        },
        end: {
          line: 693,
          column: 123
        }
      },
      "277": {
        start: {
          line: 693,
          column: 39
        },
        end: {
          line: 693,
          column: 121
        }
      },
      "278": {
        start: {
          line: 696,
          column: 0
        },
        end: {
          line: 696,
          column: 62
        }
      },
      "279": {
        start: {
          line: 697,
          column: 0
        },
        end: {
          line: 697,
          column: 54
        }
      },
      "280": {
        start: {
          line: 698,
          column: 0
        },
        end: {
          line: 698,
          column: 101
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 15,
            column: 74
          },
          end: {
            line: 15,
            column: 75
          }
        },
        loc: {
          start: {
            line: 15,
            column: 96
          },
          end: {
            line: 22,
            column: 1
          }
        },
        line: 15
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 19,
            column: 38
          },
          end: {
            line: 19,
            column: 39
          }
        },
        loc: {
          start: {
            line: 19,
            column: 49
          },
          end: {
            line: 19,
            column: 65
          }
        },
        line: 19
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 22,
            column: 6
          },
          end: {
            line: 22,
            column: 7
          }
        },
        loc: {
          start: {
            line: 22,
            column: 28
          },
          end: {
            line: 25,
            column: 1
          }
        },
        line: 22
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 26,
            column: 80
          },
          end: {
            line: 26,
            column: 81
          }
        },
        loc: {
          start: {
            line: 26,
            column: 95
          },
          end: {
            line: 28,
            column: 1
          }
        },
        line: 26
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 28,
            column: 5
          },
          end: {
            line: 28,
            column: 6
          }
        },
        loc: {
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 30,
            column: 1
          }
        },
        line: 28
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 31,
            column: 51
          },
          end: {
            line: 31,
            column: 52
          }
        },
        loc: {
          start: {
            line: 31,
            column: 63
          },
          end: {
            line: 47,
            column: 1
          }
        },
        line: 31
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 32,
            column: 18
          },
          end: {
            line: 32,
            column: 19
          }
        },
        loc: {
          start: {
            line: 32,
            column: 30
          },
          end: {
            line: 39,
            column: 5
          }
        },
        line: 32
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 33,
            column: 48
          },
          end: {
            line: 33,
            column: 49
          }
        },
        loc: {
          start: {
            line: 33,
            column: 61
          },
          end: {
            line: 37,
            column: 9
          }
        },
        line: 33
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 40,
            column: 11
          },
          end: {
            line: 40,
            column: 12
          }
        },
        loc: {
          start: {
            line: 40,
            column: 26
          },
          end: {
            line: 46,
            column: 5
          }
        },
        line: 40
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 58,
            column: 4
          },
          end: {
            line: 58,
            column: 5
          }
        },
        loc: {
          start: {
            line: 58,
            column: 38
          },
          end: {
            line: 70,
            column: 5
          }
        },
        line: 58
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 74,
            column: 4
          },
          end: {
            line: 74,
            column: 5
          }
        },
        loc: {
          start: {
            line: 74,
            column: 79
          },
          end: {
            line: 107,
            column: 5
          }
        },
        line: 74
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 100,
            column: 53
          },
          end: {
            line: 100,
            column: 54
          }
        },
        loc: {
          start: {
            line: 100,
            column: 58
          },
          end: {
            line: 100,
            column: 67
          }
        },
        line: 100
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 111,
            column: 4
          },
          end: {
            line: 111,
            column: 5
          }
        },
        loc: {
          start: {
            line: 111,
            column: 59
          },
          end: {
            line: 130,
            column: 5
          }
        },
        line: 111
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 134,
            column: 4
          },
          end: {
            line: 134,
            column: 5
          }
        },
        loc: {
          start: {
            line: 134,
            column: 69
          },
          end: {
            line: 148,
            column: 5
          }
        },
        line: 134
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 152,
            column: 4
          },
          end: {
            line: 152,
            column: 5
          }
        },
        loc: {
          start: {
            line: 152,
            column: 64
          },
          end: {
            line: 154,
            column: 5
          }
        },
        line: 152
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 158,
            column: 4
          },
          end: {
            line: 158,
            column: 5
          }
        },
        loc: {
          start: {
            line: 158,
            column: 81
          },
          end: {
            line: 176,
            column: 5
          }
        },
        line: 158
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 180,
            column: 4
          },
          end: {
            line: 180,
            column: 5
          }
        },
        loc: {
          start: {
            line: 180,
            column: 83
          },
          end: {
            line: 191,
            column: 5
          }
        },
        line: 180
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 195,
            column: 4
          },
          end: {
            line: 195,
            column: 5
          }
        },
        loc: {
          start: {
            line: 195,
            column: 77
          },
          end: {
            line: 198,
            column: 5
          }
        },
        line: 195
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 202,
            column: 4
          },
          end: {
            line: 202,
            column: 5
          }
        },
        loc: {
          start: {
            line: 202,
            column: 68
          },
          end: {
            line: 226,
            column: 5
          }
        },
        line: 202
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 230,
            column: 4
          },
          end: {
            line: 230,
            column: 5
          }
        },
        loc: {
          start: {
            line: 230,
            column: 64
          },
          end: {
            line: 255,
            column: 5
          }
        },
        line: 230
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 259,
            column: 4
          },
          end: {
            line: 259,
            column: 5
          }
        },
        loc: {
          start: {
            line: 259,
            column: 67
          },
          end: {
            line: 281,
            column: 5
          }
        },
        line: 259
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 285,
            column: 4
          },
          end: {
            line: 285,
            column: 5
          }
        },
        loc: {
          start: {
            line: 285,
            column: 58
          },
          end: {
            line: 289,
            column: 5
          }
        },
        line: 285
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 293,
            column: 4
          },
          end: {
            line: 293,
            column: 5
          }
        },
        loc: {
          start: {
            line: 293,
            column: 62
          },
          end: {
            line: 319,
            column: 5
          }
        },
        line: 293
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 294,
            column: 45
          },
          end: {
            line: 294,
            column: 46
          }
        },
        loc: {
          start: {
            line: 294,
            column: 55
          },
          end: {
            line: 294,
            column: 64
          }
        },
        line: 294
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 323,
            column: 4
          },
          end: {
            line: 323,
            column: 5
          }
        },
        loc: {
          start: {
            line: 323,
            column: 68
          },
          end: {
            line: 341,
            column: 5
          }
        },
        line: 323
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 345,
            column: 4
          },
          end: {
            line: 345,
            column: 5
          }
        },
        loc: {
          start: {
            line: 345,
            column: 55
          },
          end: {
            line: 350,
            column: 5
          }
        },
        line: 345
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 354,
            column: 4
          },
          end: {
            line: 354,
            column: 5
          }
        },
        loc: {
          start: {
            line: 354,
            column: 62
          },
          end: {
            line: 376,
            column: 5
          }
        },
        line: 354
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 374,
            column: 57
          },
          end: {
            line: 374,
            column: 58
          }
        },
        loc: {
          start: {
            line: 374,
            column: 62
          },
          end: {
            line: 374,
            column: 93
          }
        },
        line: 374
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 380,
            column: 4
          },
          end: {
            line: 380,
            column: 5
          }
        },
        loc: {
          start: {
            line: 380,
            column: 42
          },
          end: {
            line: 386,
            column: 5
          }
        },
        line: 380
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 390,
            column: 4
          },
          end: {
            line: 390,
            column: 5
          }
        },
        loc: {
          start: {
            line: 390,
            column: 73
          },
          end: {
            line: 418,
            column: 5
          }
        },
        line: 390
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 422,
            column: 4
          },
          end: {
            line: 422,
            column: 5
          }
        },
        loc: {
          start: {
            line: 422,
            column: 77
          },
          end: {
            line: 431,
            column: 5
          }
        },
        line: 422
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 435,
            column: 4
          },
          end: {
            line: 435,
            column: 5
          }
        },
        loc: {
          start: {
            line: 435,
            column: 77
          },
          end: {
            line: 454,
            column: 5
          }
        },
        line: 435
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 458,
            column: 4
          },
          end: {
            line: 458,
            column: 5
          }
        },
        loc: {
          start: {
            line: 458,
            column: 60
          },
          end: {
            line: 470,
            column: 5
          }
        },
        line: 458
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 474,
            column: 4
          },
          end: {
            line: 474,
            column: 5
          }
        },
        loc: {
          start: {
            line: 474,
            column: 57
          },
          end: {
            line: 479,
            column: 5
          }
        },
        line: 474
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 483,
            column: 4
          },
          end: {
            line: 483,
            column: 5
          }
        },
        loc: {
          start: {
            line: 483,
            column: 65
          },
          end: {
            line: 498,
            column: 5
          }
        },
        line: 483
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 502,
            column: 4
          },
          end: {
            line: 502,
            column: 5
          }
        },
        loc: {
          start: {
            line: 502,
            column: 65
          },
          end: {
            line: 570,
            column: 5
          }
        },
        line: 502
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 574,
            column: 4
          },
          end: {
            line: 574,
            column: 5
          }
        },
        loc: {
          start: {
            line: 574,
            column: 54
          },
          end: {
            line: 595,
            column: 5
          }
        },
        line: 574
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 599,
            column: 4
          },
          end: {
            line: 599,
            column: 5
          }
        },
        loc: {
          start: {
            line: 599,
            column: 98
          },
          end: {
            line: 652,
            column: 5
          }
        },
        line: 599
      },
      "38": {
        name: "(anonymous_38)",
        decl: {
          start: {
            line: 656,
            column: 4
          },
          end: {
            line: 656,
            column: 5
          }
        },
        loc: {
          start: {
            line: 656,
            column: 46
          },
          end: {
            line: 672,
            column: 5
          }
        },
        line: 656
      },
      "39": {
        name: "(anonymous_39)",
        decl: {
          start: {
            line: 676,
            column: 4
          },
          end: {
            line: 676,
            column: 5
          }
        },
        loc: {
          start: {
            line: 676,
            column: 35
          },
          end: {
            line: 694,
            column: 5
          }
        },
        line: 676
      },
      "40": {
        name: "(anonymous_40)",
        decl: {
          start: {
            line: 693,
            column: 29
          },
          end: {
            line: 693,
            column: 30
          }
        },
        loc: {
          start: {
            line: 693,
            column: 39
          },
          end: {
            line: 693,
            column: 121
          }
        },
        line: 693
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 15,
            column: 22
          },
          end: {
            line: 25,
            column: 3
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 15,
            column: 23
          },
          end: {
            line: 15,
            column: 27
          }
        }, {
          start: {
            line: 15,
            column: 31
          },
          end: {
            line: 15,
            column: 51
          }
        }, {
          start: {
            line: 15,
            column: 57
          },
          end: {
            line: 25,
            column: 2
          }
        }],
        line: 15
      },
      "1": {
        loc: {
          start: {
            line: 15,
            column: 57
          },
          end: {
            line: 25,
            column: 2
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 15,
            column: 74
          },
          end: {
            line: 22,
            column: 1
          }
        }, {
          start: {
            line: 22,
            column: 6
          },
          end: {
            line: 25,
            column: 1
          }
        }],
        line: 15
      },
      "2": {
        loc: {
          start: {
            line: 16,
            column: 4
          },
          end: {
            line: 16,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 16,
            column: 4
          },
          end: {
            line: 16,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 16
      },
      "3": {
        loc: {
          start: {
            line: 18,
            column: 4
          },
          end: {
            line: 20,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 18,
            column: 4
          },
          end: {
            line: 20,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 18
      },
      "4": {
        loc: {
          start: {
            line: 18,
            column: 8
          },
          end: {
            line: 18,
            column: 85
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 8
          },
          end: {
            line: 18,
            column: 13
          }
        }, {
          start: {
            line: 18,
            column: 18
          },
          end: {
            line: 18,
            column: 84
          }
        }],
        line: 18
      },
      "5": {
        loc: {
          start: {
            line: 18,
            column: 18
          },
          end: {
            line: 18,
            column: 84
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 34
          },
          end: {
            line: 18,
            column: 47
          }
        }, {
          start: {
            line: 18,
            column: 50
          },
          end: {
            line: 18,
            column: 84
          }
        }],
        line: 18
      },
      "6": {
        loc: {
          start: {
            line: 18,
            column: 50
          },
          end: {
            line: 18,
            column: 84
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 50
          },
          end: {
            line: 18,
            column: 63
          }
        }, {
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 84
          }
        }],
        line: 18
      },
      "7": {
        loc: {
          start: {
            line: 23,
            column: 4
          },
          end: {
            line: 23,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 23,
            column: 4
          },
          end: {
            line: 23,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 23
      },
      "8": {
        loc: {
          start: {
            line: 26,
            column: 25
          },
          end: {
            line: 30,
            column: 2
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 26
          },
          end: {
            line: 26,
            column: 30
          }
        }, {
          start: {
            line: 26,
            column: 34
          },
          end: {
            line: 26,
            column: 57
          }
        }, {
          start: {
            line: 26,
            column: 63
          },
          end: {
            line: 30,
            column: 1
          }
        }],
        line: 26
      },
      "9": {
        loc: {
          start: {
            line: 26,
            column: 63
          },
          end: {
            line: 30,
            column: 1
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 26,
            column: 80
          },
          end: {
            line: 28,
            column: 1
          }
        }, {
          start: {
            line: 28,
            column: 5
          },
          end: {
            line: 30,
            column: 1
          }
        }],
        line: 26
      },
      "10": {
        loc: {
          start: {
            line: 31,
            column: 19
          },
          end: {
            line: 47,
            column: 4
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 31,
            column: 20
          },
          end: {
            line: 31,
            column: 24
          }
        }, {
          start: {
            line: 31,
            column: 28
          },
          end: {
            line: 31,
            column: 45
          }
        }, {
          start: {
            line: 31,
            column: 50
          },
          end: {
            line: 47,
            column: 4
          }
        }],
        line: 31
      },
      "11": {
        loc: {
          start: {
            line: 33,
            column: 18
          },
          end: {
            line: 37,
            column: 9
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 33,
            column: 18
          },
          end: {
            line: 33,
            column: 44
          }
        }, {
          start: {
            line: 33,
            column: 48
          },
          end: {
            line: 37,
            column: 9
          }
        }],
        line: 33
      },
      "12": {
        loc: {
          start: {
            line: 35,
            column: 29
          },
          end: {
            line: 35,
            column: 95
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 35,
            column: 29
          },
          end: {
            line: 35,
            column: 95
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 35
      },
      "13": {
        loc: {
          start: {
            line: 41,
            column: 8
          },
          end: {
            line: 41,
            column: 46
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 41,
            column: 8
          },
          end: {
            line: 41,
            column: 46
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 41
      },
      "14": {
        loc: {
          start: {
            line: 41,
            column: 12
          },
          end: {
            line: 41,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 41,
            column: 12
          },
          end: {
            line: 41,
            column: 15
          }
        }, {
          start: {
            line: 41,
            column: 19
          },
          end: {
            line: 41,
            column: 33
          }
        }],
        line: 41
      },
      "15": {
        loc: {
          start: {
            line: 43,
            column: 8
          },
          end: {
            line: 43,
            column: 137
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 43,
            column: 8
          },
          end: {
            line: 43,
            column: 137
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 43
      },
      "16": {
        loc: {
          start: {
            line: 43,
            column: 78
          },
          end: {
            line: 43,
            column: 137
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 43,
            column: 78
          },
          end: {
            line: 43,
            column: 137
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 43
      },
      "17": {
        loc: {
          start: {
            line: 59,
            column: 8
          },
          end: {
            line: 68,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 59,
            column: 8
          },
          end: {
            line: 68,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 59
      },
      "18": {
        loc: {
          start: {
            line: 84,
            column: 35
          },
          end: {
            line: 86,
            column: 49
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 85,
            column: 14
          },
          end: {
            line: 85,
            column: 69
          }
        }, {
          start: {
            line: 86,
            column: 14
          },
          end: {
            line: 86,
            column: 49
          }
        }],
        line: 84
      },
      "19": {
        loc: {
          start: {
            line: 113,
            column: 8
          },
          end: {
            line: 116,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 113,
            column: 8
          },
          end: {
            line: 116,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 113
      },
      "20": {
        loc: {
          start: {
            line: 113,
            column: 12
          },
          end: {
            line: 114,
            column: 113
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 113,
            column: 12
          },
          end: {
            line: 113,
            column: 50
          }
        }, {
          start: {
            line: 114,
            column: 12
          },
          end: {
            line: 114,
            column: 113
          }
        }],
        line: 113
      },
      "21": {
        loc: {
          start: {
            line: 118,
            column: 8
          },
          end: {
            line: 121,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 118,
            column: 8
          },
          end: {
            line: 121,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 118
      },
      "22": {
        loc: {
          start: {
            line: 118,
            column: 12
          },
          end: {
            line: 119,
            column: 83
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 118,
            column: 12
          },
          end: {
            line: 118,
            column: 43
          }
        }, {
          start: {
            line: 119,
            column: 12
          },
          end: {
            line: 119,
            column: 83
          }
        }],
        line: 118
      },
      "23": {
        loc: {
          start: {
            line: 123,
            column: 8
          },
          end: {
            line: 127,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 123,
            column: 8
          },
          end: {
            line: 127,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 123
      },
      "24": {
        loc: {
          start: {
            line: 123,
            column: 12
          },
          end: {
            line: 125,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 123,
            column: 12
          },
          end: {
            line: 123,
            column: 44
          }
        }, {
          start: {
            line: 124,
            column: 12
          },
          end: {
            line: 124,
            column: 56
          }
        }, {
          start: {
            line: 125,
            column: 12
          },
          end: {
            line: 125,
            column: 67
          }
        }],
        line: 123
      },
      "25": {
        loc: {
          start: {
            line: 136,
            column: 8
          },
          end: {
            line: 147,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 137,
            column: 12
          },
          end: {
            line: 138,
            column: 81
          }
        }, {
          start: {
            line: 139,
            column: 12
          },
          end: {
            line: 140,
            column: 98
          }
        }, {
          start: {
            line: 141,
            column: 12
          },
          end: {
            line: 142,
            column: 100
          }
        }, {
          start: {
            line: 143,
            column: 12
          },
          end: {
            line: 144,
            column: 94
          }
        }, {
          start: {
            line: 145,
            column: 12
          },
          end: {
            line: 146,
            column: 77
          }
        }],
        line: 136
      },
      "26": {
        loc: {
          start: {
            line: 167,
            column: 8
          },
          end: {
            line: 170,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 167,
            column: 8
          },
          end: {
            line: 170,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 167
      },
      "27": {
        loc: {
          start: {
            line: 181,
            column: 8
          },
          end: {
            line: 183,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 181,
            column: 8
          },
          end: {
            line: 183,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 181
      },
      "28": {
        loc: {
          start: {
            line: 181,
            column: 12
          },
          end: {
            line: 181,
            column: 118
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 181,
            column: 12
          },
          end: {
            line: 181,
            column: 57
          }
        }, {
          start: {
            line: 181,
            column: 61
          },
          end: {
            line: 181,
            column: 118
          }
        }],
        line: 181
      },
      "29": {
        loc: {
          start: {
            line: 204,
            column: 8
          },
          end: {
            line: 206,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 204,
            column: 8
          },
          end: {
            line: 206,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 204
      },
      "30": {
        loc: {
          start: {
            line: 204,
            column: 12
          },
          end: {
            line: 204,
            column: 98
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 204,
            column: 12
          },
          end: {
            line: 204,
            column: 53
          }
        }, {
          start: {
            line: 204,
            column: 57
          },
          end: {
            line: 204,
            column: 98
          }
        }],
        line: 204
      },
      "31": {
        loc: {
          start: {
            line: 207,
            column: 8
          },
          end: {
            line: 225,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 208,
            column: 12
          },
          end: {
            line: 209,
            column: 96
          }
        }, {
          start: {
            line: 210,
            column: 12
          },
          end: {
            line: 216,
            column: 13
          }
        }, {
          start: {
            line: 217,
            column: 12
          },
          end: {
            line: 218,
            column: 106
          }
        }, {
          start: {
            line: 219,
            column: 12
          },
          end: {
            line: 222,
            column: 27
          }
        }, {
          start: {
            line: 223,
            column: 12
          },
          end: {
            line: 224,
            column: 93
          }
        }],
        line: 207
      },
      "32": {
        loc: {
          start: {
            line: 231,
            column: 8
          },
          end: {
            line: 254,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 232,
            column: 12
          },
          end: {
            line: 233,
            column: 47
          }
        }, {
          start: {
            line: 234,
            column: 12
          },
          end: {
            line: 235,
            column: 53
          }
        }, {
          start: {
            line: 236,
            column: 12
          },
          end: {
            line: 237,
            column: 49
          }
        }, {
          start: {
            line: 238,
            column: 12
          },
          end: {
            line: 239,
            column: 50
          }
        }, {
          start: {
            line: 240,
            column: 12
          },
          end: {
            line: 241,
            column: 47
          }
        }, {
          start: {
            line: 242,
            column: 12
          },
          end: {
            line: 243,
            column: 128
          }
        }, {
          start: {
            line: 244,
            column: 12
          },
          end: {
            line: 248,
            column: 13
          }
        }, {
          start: {
            line: 249,
            column: 12
          },
          end: {
            line: 250,
            column: 112
          }
        }, {
          start: {
            line: 251,
            column: 12
          },
          end: {
            line: 253,
            column: 27
          }
        }],
        line: 231
      },
      "33": {
        loc: {
          start: {
            line: 243,
            column: 70
          },
          end: {
            line: 243,
            column: 126
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 243,
            column: 70
          },
          end: {
            line: 243,
            column: 120
          }
        }, {
          start: {
            line: 243,
            column: 124
          },
          end: {
            line: 243,
            column: 126
          }
        }],
        line: 243
      },
      "34": {
        loc: {
          start: {
            line: 245,
            column: 54
          },
          end: {
            line: 245,
            column: 110
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 245,
            column: 54
          },
          end: {
            line: 245,
            column: 104
          }
        }, {
          start: {
            line: 245,
            column: 108
          },
          end: {
            line: 245,
            column: 110
          }
        }],
        line: 245
      },
      "35": {
        loc: {
          start: {
            line: 246,
            column: 55
          },
          end: {
            line: 246,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 246,
            column: 55
          },
          end: {
            line: 246,
            column: 106
          }
        }, {
          start: {
            line: 246,
            column: 110
          },
          end: {
            line: 246,
            column: 112
          }
        }],
        line: 246
      },
      "36": {
        loc: {
          start: {
            line: 260,
            column: 8
          },
          end: {
            line: 262,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 260,
            column: 8
          },
          end: {
            line: 262,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 260
      },
      "37": {
        loc: {
          start: {
            line: 260,
            column: 12
          },
          end: {
            line: 260,
            column: 98
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 260,
            column: 12
          },
          end: {
            line: 260,
            column: 53
          }
        }, {
          start: {
            line: 260,
            column: 57
          },
          end: {
            line: 260,
            column: 98
          }
        }],
        line: 260
      },
      "38": {
        loc: {
          start: {
            line: 263,
            column: 8
          },
          end: {
            line: 280,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 264,
            column: 12
          },
          end: {
            line: 266,
            column: 114
          }
        }, {
          start: {
            line: 267,
            column: 12
          },
          end: {
            line: 269,
            column: 105
          }
        }, {
          start: {
            line: 270,
            column: 12
          },
          end: {
            line: 277,
            column: 13
          }
        }, {
          start: {
            line: 278,
            column: 12
          },
          end: {
            line: 279,
            column: 27
          }
        }],
        line: 263
      },
      "39": {
        loc: {
          start: {
            line: 296,
            column: 8
          },
          end: {
            line: 298,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 296,
            column: 8
          },
          end: {
            line: 298,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 296
      },
      "40": {
        loc: {
          start: {
            line: 299,
            column: 8
          },
          end: {
            line: 309,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 299,
            column: 8
          },
          end: {
            line: 309,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 299
      },
      "41": {
        loc: {
          start: {
            line: 300,
            column: 12
          },
          end: {
            line: 308,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 300,
            column: 12
          },
          end: {
            line: 308,
            column: 13
          }
        }, {
          start: {
            line: 306,
            column: 17
          },
          end: {
            line: 308,
            column: 13
          }
        }],
        line: 300
      },
      "42": {
        loc: {
          start: {
            line: 312,
            column: 12
          },
          end: {
            line: 316,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 312,
            column: 12
          },
          end: {
            line: 316,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 312
      },
      "43": {
        loc: {
          start: {
            line: 312,
            column: 16
          },
          end: {
            line: 312,
            column: 82
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 312,
            column: 16
          },
          end: {
            line: 312,
            column: 45
          }
        }, {
          start: {
            line: 312,
            column: 49
          },
          end: {
            line: 312,
            column: 82
          }
        }],
        line: 312
      },
      "44": {
        loc: {
          start: {
            line: 327,
            column: 8
          },
          end: {
            line: 330,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 327,
            column: 8
          },
          end: {
            line: 330,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 327
      },
      "45": {
        loc: {
          start: {
            line: 332,
            column: 8
          },
          end: {
            line: 335,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 332,
            column: 8
          },
          end: {
            line: 335,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 332
      },
      "46": {
        loc: {
          start: {
            line: 337,
            column: 8
          },
          end: {
            line: 339,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 337,
            column: 8
          },
          end: {
            line: 339,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 337
      },
      "47": {
        loc: {
          start: {
            line: 397,
            column: 12
          },
          end: {
            line: 409,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 397,
            column: 12
          },
          end: {
            line: 409,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 397
      },
      "48": {
        loc: {
          start: {
            line: 399,
            column: 16
          },
          end: {
            line: 407,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 399,
            column: 16
          },
          end: {
            line: 407,
            column: 17
          }
        }, {
          start: {
            line: 402,
            column: 21
          },
          end: {
            line: 407,
            column: 17
          }
        }],
        line: 399
      },
      "49": {
        loc: {
          start: {
            line: 402,
            column: 21
          },
          end: {
            line: 407,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 402,
            column: 21
          },
          end: {
            line: 407,
            column: 17
          }
        }, {
          start: {
            line: 405,
            column: 21
          },
          end: {
            line: 407,
            column: 17
          }
        }],
        line: 402
      },
      "50": {
        loc: {
          start: {
            line: 463,
            column: 8
          },
          end: {
            line: 468,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 463,
            column: 8
          },
          end: {
            line: 468,
            column: 9
          }
        }, {
          start: {
            line: 466,
            column: 13
          },
          end: {
            line: 468,
            column: 9
          }
        }],
        line: 463
      },
      "51": {
        loc: {
          start: {
            line: 466,
            column: 13
          },
          end: {
            line: 468,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 466,
            column: 13
          },
          end: {
            line: 468,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 466
      },
      "52": {
        loc: {
          start: {
            line: 486,
            column: 8
          },
          end: {
            line: 489,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 486,
            column: 8
          },
          end: {
            line: 489,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 486
      },
      "53": {
        loc: {
          start: {
            line: 486,
            column: 12
          },
          end: {
            line: 487,
            column: 62
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 486,
            column: 12
          },
          end: {
            line: 486,
            column: 62
          }
        }, {
          start: {
            line: 487,
            column: 12
          },
          end: {
            line: 487,
            column: 62
          }
        }],
        line: 486
      },
      "54": {
        loc: {
          start: {
            line: 491,
            column: 8
          },
          end: {
            line: 493,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 491,
            column: 8
          },
          end: {
            line: 493,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 491
      },
      "55": {
        loc: {
          start: {
            line: 491,
            column: 12
          },
          end: {
            line: 491,
            column: 75
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 491,
            column: 12
          },
          end: {
            line: 491,
            column: 41
          }
        }, {
          start: {
            line: 491,
            column: 45
          },
          end: {
            line: 491,
            column: 75
          }
        }],
        line: 491
      },
      "56": {
        loc: {
          start: {
            line: 495,
            column: 8
          },
          end: {
            line: 497,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 495,
            column: 8
          },
          end: {
            line: 497,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 495
      },
      "57": {
        loc: {
          start: {
            line: 508,
            column: 8
          },
          end: {
            line: 515,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 508,
            column: 8
          },
          end: {
            line: 515,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 508
      },
      "58": {
        loc: {
          start: {
            line: 516,
            column: 8
          },
          end: {
            line: 523,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 516,
            column: 8
          },
          end: {
            line: 523,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 516
      },
      "59": {
        loc: {
          start: {
            line: 528,
            column: 12
          },
          end: {
            line: 545,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 528,
            column: 12
          },
          end: {
            line: 545,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 528
      },
      "60": {
        loc: {
          start: {
            line: 529,
            column: 16
          },
          end: {
            line: 544,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 529,
            column: 16
          },
          end: {
            line: 544,
            column: 17
          }
        }, {
          start: {
            line: 537,
            column: 21
          },
          end: {
            line: 544,
            column: 17
          }
        }],
        line: 529
      },
      "61": {
        loc: {
          start: {
            line: 537,
            column: 21
          },
          end: {
            line: 544,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 537,
            column: 21
          },
          end: {
            line: 544,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 537
      },
      "62": {
        loc: {
          start: {
            line: 550,
            column: 8
          },
          end: {
            line: 557,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 550,
            column: 8
          },
          end: {
            line: 557,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 550
      },
      "63": {
        loc: {
          start: {
            line: 575,
            column: 8
          },
          end: {
            line: 594,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 576,
            column: 12
          },
          end: {
            line: 577,
            column: 63
          }
        }, {
          start: {
            line: 578,
            column: 12
          },
          end: {
            line: 579,
            column: 63
          }
        }, {
          start: {
            line: 580,
            column: 12
          },
          end: {
            line: 581,
            column: 64
          }
        }, {
          start: {
            line: 582,
            column: 12
          },
          end: {
            line: 583,
            column: 64
          }
        }, {
          start: {
            line: 584,
            column: 12
          },
          end: {
            line: 585,
            column: 49
          }
        }, {
          start: {
            line: 586,
            column: 12
          },
          end: {
            line: 587,
            column: 49
          }
        }, {
          start: {
            line: 588,
            column: 12
          },
          end: {
            line: 589,
            column: 89
          }
        }, {
          start: {
            line: 590,
            column: 12
          },
          end: {
            line: 591,
            column: 90
          }
        }, {
          start: {
            line: 592,
            column: 12
          },
          end: {
            line: 593,
            column: 29
          }
        }],
        line: 575
      },
      "64": {
        loc: {
          start: {
            line: 589,
            column: 23
          },
          end: {
            line: 589,
            column: 88
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 589,
            column: 23
          },
          end: {
            line: 589,
            column: 53
          }
        }, {
          start: {
            line: 589,
            column: 57
          },
          end: {
            line: 589,
            column: 88
          }
        }],
        line: 589
      },
      "65": {
        loc: {
          start: {
            line: 591,
            column: 23
          },
          end: {
            line: 591,
            column: 89
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 591,
            column: 23
          },
          end: {
            line: 591,
            column: 53
          }
        }, {
          start: {
            line: 591,
            column: 57
          },
          end: {
            line: 591,
            column: 89
          }
        }],
        line: 591
      },
      "66": {
        loc: {
          start: {
            line: 602,
            column: 8
          },
          end: {
            line: 610,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 602,
            column: 8
          },
          end: {
            line: 610,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 602
      },
      "67": {
        loc: {
          start: {
            line: 611,
            column: 8
          },
          end: {
            line: 619,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 611,
            column: 8
          },
          end: {
            line: 619,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 611
      },
      "68": {
        loc: {
          start: {
            line: 623,
            column: 8
          },
          end: {
            line: 631,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 623,
            column: 8
          },
          end: {
            line: 631,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 623
      },
      "69": {
        loc: {
          start: {
            line: 632,
            column: 8
          },
          end: {
            line: 640,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 632,
            column: 8
          },
          end: {
            line: 640,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 632
      },
      "70": {
        loc: {
          start: {
            line: 642,
            column: 8
          },
          end: {
            line: 650,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 642,
            column: 8
          },
          end: {
            line: 650,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 642
      },
      "71": {
        loc: {
          start: {
            line: 642,
            column: 12
          },
          end: {
            line: 642,
            column: 75
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 642,
            column: 12
          },
          end: {
            line: 642,
            column: 41
          }
        }, {
          start: {
            line: 642,
            column: 45
          },
          end: {
            line: 642,
            column: 75
          }
        }],
        line: 642
      },
      "72": {
        loc: {
          start: {
            line: 665,
            column: 20
          },
          end: {
            line: 667,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 665,
            column: 20
          },
          end: {
            line: 667,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 665
      },
      "73": {
        loc: {
          start: {
            line: 693,
            column: 39
          },
          end: {
            line: 693,
            column: 121
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 693,
            column: 39
          },
          end: {
            line: 693,
            column: 75
          }
        }, {
          start: {
            line: 693,
            column: 79
          },
          end: {
            line: 693,
            column: 121
          }
        }],
        line: 693
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0,
      "210": 0,
      "211": 0,
      "212": 0,
      "213": 0,
      "214": 0,
      "215": 0,
      "216": 0,
      "217": 0,
      "218": 0,
      "219": 0,
      "220": 0,
      "221": 0,
      "222": 0,
      "223": 0,
      "224": 0,
      "225": 0,
      "226": 0,
      "227": 0,
      "228": 0,
      "229": 0,
      "230": 0,
      "231": 0,
      "232": 0,
      "233": 0,
      "234": 0,
      "235": 0,
      "236": 0,
      "237": 0,
      "238": 0,
      "239": 0,
      "240": 0,
      "241": 0,
      "242": 0,
      "243": 0,
      "244": 0,
      "245": 0,
      "246": 0,
      "247": 0,
      "248": 0,
      "249": 0,
      "250": 0,
      "251": 0,
      "252": 0,
      "253": 0,
      "254": 0,
      "255": 0,
      "256": 0,
      "257": 0,
      "258": 0,
      "259": 0,
      "260": 0,
      "261": 0,
      "262": 0,
      "263": 0,
      "264": 0,
      "265": 0,
      "266": 0,
      "267": 0,
      "268": 0,
      "269": 0,
      "270": 0,
      "271": 0,
      "272": 0,
      "273": 0,
      "274": 0,
      "275": 0,
      "276": 0,
      "277": 0,
      "278": 0,
      "279": 0,
      "280": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0, 0],
      "9": [0, 0],
      "10": [0, 0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0, 0],
      "25": [0, 0, 0, 0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0, 0, 0, 0],
      "32": [0, 0, 0, 0, 0, 0, 0, 0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0, 0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0],
      "61": [0, 0],
      "62": [0, 0],
      "63": [0, 0, 0, 0, 0, 0, 0, 0, 0],
      "64": [0, 0],
      "65": [0, 0],
      "66": [0, 0],
      "67": [0, 0],
      "68": [0, 0],
      "69": [0, 0],
      "70": [0, 0],
      "71": [0, 0],
      "72": [0, 0],
      "73": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\AdvancedFittingCalculator.ts",
      mappings: ";AAAA;;;;;;;;;;;;GAYG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,uCAAyB;AACzB,2CAA6B;AAC7B,mEAAgE;AAChE,uEAgBsC;AAEtC,MAAa,yBAA0B,SAAQ,6CAAqB;IAIlE;;OAEG;IACK,MAAM,CAAC,wBAAwB;QACrC,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC/B,IAAI,CAAC;gBACH,MAAM,WAAW,GAAG,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;gBACjE,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;YACtD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;gBAC9D,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;YAC/D,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACnC,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,4BAA4B,CACxC,MAAoC,EACpC,cAA8B,EAC9B,aAA6B;QAG7B,wCAAwC;QACxC,IAAI,CAAC,6BAA6B,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;QAE3D,wCAAwC;QACxC,MAAM,MAAM,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;QAEpE,+BAA+B;QAC/B,MAAM,QAAQ,GAAG,IAAI,CAAC,yBAAyB,CAAC,MAAM,EAAE,cAAc,EAAE,MAAM,CAAC,CAAC;QAEhF,2BAA2B;QAC3B,MAAM,aAAa,GAAG,IAAI,CAAC,sBAAsB,CAAC,QAAQ,EAAE,MAAM,EAAE,cAAc,CAAC,CAAC;QAEpF,2DAA2D;QAC3D,MAAM,kBAAkB,GAAG,aAAa;YACtC,CAAC,CAAC,IAAI,CAAC,2BAA2B,CAAC,MAAM,EAAE,aAAa,CAAC;YACzD,CAAC,CAAC,IAAI,CAAC,4BAA4B,EAAE,CAAC;QAExC,4BAA4B;QAC5B,MAAM,iBAAiB,GAAG,aAAa,GAAG,kBAAkB,CAAC,sBAAsB,CAAC;QAEpF,gCAAgC;QAChC,MAAM,kBAAkB,GAAG,IAAI,CAAC,2BAA2B,CAAC,MAAM,EAAE,cAAc,EAAE,iBAAiB,CAAC,CAAC;QAEvG,mBAAmB;QACnB,MAAM,iBAAiB,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,cAAc,EAAE,iBAAiB,CAAC,CAAC;QAE1F,2BAA2B;QAC3B,MAAM,eAAe,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,cAAc,EAAE,kBAAkB,EAAE,iBAAiB,CAAC,CAAC;QAEpH,gCAAgC;QAChC,OAAO;YACL,YAAY,EAAE,iBAAiB;YAC/B,gBAAgB,EAAE,IAAI,CAAC,yBAAyB,CAAC,cAAc,CAAC,QAAQ,EAAE,cAAc,CAAC,UAAU,CAAC;YACpG,OAAO,EAAE,iBAAiB,GAAG,IAAI,CAAC,yBAAyB,CAAC,cAAc,CAAC,QAAQ,EAAE,cAAc,CAAC,UAAU,CAAC;YAC/G,QAAQ,EAAE,iBAAiB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC;YACxD,iBAAiB,EAAE,MAAM;YACzB,kBAAkB,EAAE,kBAAkB;YACtC,kBAAkB,EAAE,kBAAkB;YACtC,iBAAiB,EAAE,iBAAiB;YACpC,eAAe,EAAE,eAAe;SACjC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,uBAAuB,CACpC,MAAoC,EACpC,cAA8B;QAG9B,iEAAiE;QACjE,IAAI,cAAc,CAAC,cAAc,GAAG,MAAM;YACtC,MAAM,CAAC,mBAAmB,CAAC,iBAAiB,KAAK,wCAAiB,CAAC,WAAW,EAAE,CAAC;YACnF,OAAO,wCAAiB,CAAC,WAAW,CAAC;QACvC,CAAC;QAED,2DAA2D;QAC3D,IAAI,MAAM,CAAC,UAAU,KAAK,SAAS;YAC/B,MAAM,CAAC,mBAAmB,CAAC,WAAW,CAAC,qBAAqB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5E,OAAO,wCAAiB,CAAC,eAAe,CAAC;QAC3C,CAAC;QAED,yDAAyD;QACzD,IAAI,MAAM,CAAC,UAAU,KAAK,UAAU;YAChC,MAAM,CAAC,mBAAmB,CAAC,iBAAiB;YAC5C,MAAM,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5D,OAAO,wCAAiB,CAAC,iBAAiB,CAAC;QAC7C,CAAC;QAED,iDAAiD;QACjD,OAAO,wCAAiB,CAAC,eAAe,CAAC;IAC3C,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,yBAAyB,CACtC,MAAoC,EACpC,cAA8B,EAC9B,MAAyB;QAGzB,MAAM,gBAAgB,GAAG,IAAI,CAAC,yBAAyB,CAAC,cAAc,CAAC,QAAQ,EAAE,cAAc,CAAC,UAAU,CAAC,CAAC;QAE5G,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,wCAAiB,CAAC,eAAe;gBACpC,OAAO,IAAI,CAAC,0BAA0B,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;YAEnE,KAAK,wCAAiB,CAAC,eAAe;gBACpC,OAAO,IAAI,CAAC,2BAA2B,CAAC,MAAM,EAAE,cAAc,EAAE,gBAAgB,CAAC,CAAC;YAEpF,KAAK,wCAAiB,CAAC,iBAAiB;gBACtC,OAAO,IAAI,CAAC,6BAA6B,CAAC,MAAM,EAAE,cAAc,EAAE,gBAAgB,CAAC,CAAC;YAEtF,KAAK,wCAAiB,CAAC,WAAW;gBAChC,OAAO,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,cAAc,EAAE,gBAAgB,CAAC,CAAC;YAEhF;gBACE,MAAM,IAAI,KAAK,CAAC,mCAAmC,MAAM,EAAE,CAAC,CAAC;QACjE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,0BAA0B,CACvC,MAAoC,EACpC,gBAAwB;QAExB,OAAO,MAAM,CAAC,mBAAmB,CAAC,WAAW,CAAC,WAAW,GAAG,gBAAgB,CAAC;IAC/E,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,2BAA2B,CACxC,MAAoC,EACpC,cAA8B,EAC9B,gBAAwB;QAGxB,IAAI,OAAO,GAAG,MAAM,CAAC,mBAAmB,CAAC,WAAW,CAAC,WAAW,CAAC;QAEjE,+BAA+B;QAC/B,KAAK,MAAM,UAAU,IAAI,MAAM,CAAC,mBAAmB,CAAC,WAAW,CAAC,qBAAqB,EAAE,CAAC;YACtF,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,SAAS,EAAE,MAAM,EAAE,cAAc,CAAC,CAAC;YAC5F,MAAM,UAAU,GAAG,IAAI,CAAC,4BAA4B,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;YACjF,OAAO,IAAI,UAAU,CAAC;QACxB,CAAC;QAED,8CAA8C;QAC9C,IAAI,MAAM,CAAC,mBAAmB,CAAC,WAAW,CAAC,kBAAkB,EAAE,OAAO,EAAE,CAAC;YACvE,MAAM,kBAAkB,GAAG,IAAI,CAAC,2BAA2B,CACzD,MAAM,CAAC,mBAAmB,CAAC,WAAW,CAAC,kBAAkB,EACzD,cAAc,CAAC,cAAc,CAC9B,CAAC;YACF,OAAO,IAAI,kBAAkB,CAAC;QAChC,CAAC;QAED,6BAA6B;QAC7B,KAAK,MAAM,UAAU,IAAI,MAAM,CAAC,mBAAmB,CAAC,WAAW,CAAC,mBAAmB,EAAE,CAAC;YACpF,OAAO,IAAI,UAAU,CAAC,gBAAgB,CAAC;QACzC,CAAC;QAED,OAAO,OAAO,GAAG,gBAAgB,CAAC;IACpC,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,6BAA6B,CAC1C,MAAoC,EACpC,cAA8B,EAC9B,gBAAwB;QAGxB,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,iBAAiB,IAAI,MAAM,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC/G,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;QACjE,CAAC;QAED,8CAA8C;QAC9C,MAAM,KAAK,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,mBAAmB,CAAC,iBAAiB,EAAE,cAAc,CAAC,CAAC;QAExG,wCAAwC;QACxC,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,SAAS,EAAE,MAAM,EAAE,cAAc,CAAC,CAAC;QAEvF,uCAAuC;QACvC,MAAM,YAAY,GAAG,IAAI,CAAC,2BAA2B,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC;QAE7E,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,uBAAuB,CACpC,MAAoC,EACpC,cAA8B,EAC9B,gBAAwB;QAGxB,0EAA0E;QAC1E,OAAO,IAAI,CAAC,2BAA2B,CAAC,MAAM,EAAE,cAAc,EAAE,gBAAgB,CAAC,CAAC;IACpF,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,4BAA4B,CACzC,UAA+B,EAC/B,cAAsB;QAGtB,2CAA2C;QAC3C,IAAI,cAAc,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,cAAc,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;YAC3F,OAAO,CAAC,IAAI,CAAC,aAAa,UAAU,CAAC,SAAS,UAAU,cAAc,yBAAyB,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC3J,CAAC;QAED,QAAQ,UAAU,CAAC,YAAY,EAAE,CAAC;YAChC,KAAK,QAAQ;gBACX,OAAO,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC;YAElF,KAAK,YAAY,CAAC,CAAC,CAAC;gBAClB,IAAI,MAAM,GAAG,CAAC,CAAC;gBACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;oBACxD,MAAM,IAAI,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;gBACrE,CAAC;gBACD,OAAO,MAAM,CAAC;YAChB,CAAC;YAED,KAAK,aAAa;gBAChB,OAAO,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC;YAE5F,KAAK,QAAQ;gBACX,0DAA0D;gBAC1D,kCAAkC;gBAClC,OAAO,GAAG,CAAC;YAEb;gBACE,MAAM,IAAI,KAAK,CAAC,kCAAkC,UAAU,CAAC,YAAY,EAAE,CAAC,CAAC;QACjF,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,iBAAiB,CAC9B,SAAiB,EACjB,MAAoC,EACpC,cAA8B;QAG9B,QAAQ,SAAS,EAAE,CAAC;YAClB,KAAK,UAAU;gBACb,OAAO,cAAc,CAAC,QAAQ,CAAC;YACjC,KAAK,iBAAiB;gBACpB,OAAO,cAAc,CAAC,cAAc,CAAC;YACvC,KAAK,aAAa;gBAChB,OAAO,cAAc,CAAC,UAAU,CAAC;YACnC,KAAK,aAAa;gBAChB,OAAO,cAAc,CAAC,WAAW,CAAC;YACpC,KAAK,UAAU;gBACb,OAAO,cAAc,CAAC,QAAQ,CAAC;YACjC,KAAK,0BAA0B;gBAC7B,OAAO,MAAM,CAAC,kBAAkB,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,MAAM,CAAC,kBAAkB,CAAC,UAAU,CAAC,aAAa,IAAI,EAAE,CAAC,CAAC;YAClH,KAAK,YAAY,CAAC,CAAC,CAAC;gBAClB,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,kBAAkB,CAAC,UAAU,CAAC,aAAa,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;gBACxG,MAAM,UAAU,GAAG,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,kBAAkB,CAAC,UAAU,CAAC,cAAc,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC1G,OAAO,UAAU,GAAG,SAAS,CAAC;YAChC,CAAC;YACD,KAAK,cAAc;gBACjB,OAAO,MAAM,CAAC,kBAAkB,CAAC,UAAU,CAAC,KAAK,GAAG,MAAM,CAAC,kBAAkB,CAAC,UAAU,CAAC,MAAM,CAAC;YAClG;gBACE,OAAO,CAAC,IAAI,CAAC,sBAAsB,SAAS,2BAA2B,CAAC,CAAC;gBACzE,OAAO,GAAG,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,2BAA2B,CACxC,UAAe,EACf,cAAsB;QAGtB,IAAI,cAAc,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,cAAc,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;YAC3F,OAAO,GAAG,CAAC,CAAC,oCAAoC;QAClD,CAAC;QAED,QAAQ,UAAU,CAAC,MAAM,EAAE,CAAC;YAC1B,KAAK,WAAW;gBACd,wCAAwC;gBACxC,OAAO,GAAG,GAAG,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,GAAG,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAEpG,KAAK,SAAS;gBACZ,8BAA8B;gBAC9B,OAAO,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAE3F,KAAK,QAAQ,CAAC,CAAC,CAAC;gBACd,+BAA+B;gBAC/B,IAAI,MAAM,GAAG,CAAC,CAAC;gBACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;oBACxD,MAAM,IAAI,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;gBACrE,CAAC;gBACD,OAAO,MAAM,CAAC;YAChB,CAAC;YAED;gBACE,OAAO,GAAG,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,sBAAsB,CACnC,MAA0B,EAC1B,cAA8B;QAG9B,4CAA4C;QAC5C,mFAAmF;QACnF,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,2BAA2B,CACxC,KAAuB,EACvB,cAAsB;QAGtB,MAAM,MAAM,GAAG,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAE1D,wCAAwC;QACxC,IAAI,cAAc,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAClC,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC;QACD,IAAI,cAAc,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAClD,IAAI,KAAK,CAAC,oBAAoB,EAAE,CAAC;gBAC/B,8BAA8B;gBAC9B,MAAM,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;gBACjC,MAAM,KAAK,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC5E,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,cAAc,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChE,CAAC;iBAAM,CAAC;gBACN,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACrC,CAAC;QACH,CAAC;QAED,4CAA4C;QAC5C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAC3C,IAAI,cAAc,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,cAAc,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBACvE,uBAAuB;gBACvB,MAAM,KAAK,GAAG,CAAC,cAAc,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC/E,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/D,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW;IACjC,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,sBAAsB,CACnC,QAAgB,EAChB,MAAoC,EACpC,cAA8B;QAG9B,IAAI,aAAa,GAAG,QAAQ,CAAC;QAC7B,MAAM,WAAW,GAAG,MAAM,CAAC,mBAAmB,CAAC,iBAAiB,CAAC;QAEjE,yBAAyB;QACzB,IAAI,WAAW,CAAC,qBAAqB,EAAE,CAAC;YACtC,MAAM,cAAc,GAAG,IAAI,CAAC,8BAA8B,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;YACvF,aAAa,IAAI,cAAc,CAAC;QAClC,CAAC;QAED,qBAAqB;QACrB,IAAI,WAAW,CAAC,iBAAiB,EAAE,CAAC;YAClC,MAAM,iBAAiB,GAAG,cAAc,CAAC,UAAU,GAAG,KAAK,CAAC,CAAC,uBAAuB;YACpF,aAAa,IAAI,iBAAiB,CAAC;QACrC,CAAC;QAED,uCAAuC;QACvC,IAAI,WAAW,CAAC,sBAAsB,EAAE,CAAC;YACvC,aAAa,IAAI,GAAG,CAAC,CAAC,0BAA0B;QAClD,CAAC;QAED,OAAO,aAAa,CAAC;IACvB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,8BAA8B,CAAC,WAAmB;QAC/D,+BAA+B;QAC/B,MAAM,YAAY,GAAG,EAAE,CAAC;QACxB,MAAM,SAAS,GAAG,CAAC,WAAW,GAAG,MAAM,CAAC,GAAG,CAAC,YAAY,GAAG,MAAM,CAAC,CAAC,CAAC,kCAAkC;QACtG,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,2BAA2B,CACxC,MAAoC,EACpC,aAA4B;QAG5B,MAAM,gBAAgB,GAAG,aAAa,CAAC,mBAAmB,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,eAAe;QAC1F,MAAM,kBAAkB,GAAG,aAAa,CAAC,qBAAqB,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAE9E,IAAI,iBAAiB,GAAG,GAAG,CAAC;QAC5B,MAAM,YAAY,GAAyB,EAAE,CAAC;QAE9C,wBAAwB;QACxB,KAAK,MAAM,QAAQ,IAAI,gBAAgB,EAAE,CAAC;YACxC,MAAM,WAAW,GAAG,IAAI,CAAC,4BAA4B,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YACxE,iBAAiB,IAAI,WAAW,CAAC,MAAM,CAAC;YACxC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACjC,CAAC;QAED,0BAA0B;QAC1B,KAAK,MAAM,UAAU,IAAI,kBAAkB,EAAE,CAAC;YAC5C,MAAM,WAAW,GAAG,IAAI,CAAC,8BAA8B,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;YAC5E,iBAAiB,IAAI,WAAW,CAAC,MAAM,CAAC;YACxC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACjC,CAAC;QAED,OAAO;YACL,sBAAsB,EAAE,iBAAiB;YACzC,sBAAsB,EAAE,YAAY;YACpC,uBAAuB,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC;SACnF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,4BAA4B;QACzC,OAAO;YACL,sBAAsB,EAAE,GAAG;YAC3B,sBAAsB,EAAE,EAAE;YAC1B,uBAAuB,EAAE,EAAE;SAC5B,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,4BAA4B,CACzC,eAA6C,EAC7C,cAA4C;QAG5C,qCAAqC;QACrC,mDAAmD;QACnD,IAAI,MAAM,GAAG,GAAG,CAAC;QACjB,IAAI,YAAY,GAA8B,KAAK,CAAC;QAEpD,sCAAsC;QACtC,KAAK,MAAM,MAAM,IAAI,cAAc,CAAC,mBAAmB,CAAC,kBAAkB,EAAE,CAAC;YAC3E,IAAI,MAAM,CAAC,mBAAmB,KAAK,eAAe,CAAC,IAAI,EAAE,CAAC;gBACxD,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC;gBAC1B,IAAI,MAAM,CAAC,SAAS,GAAG,GAAG,EAAE,CAAC;oBAC3B,YAAY,GAAG,MAAM,CAAC;gBACxB,CAAC;qBAAM,IAAI,MAAM,CAAC,SAAS,GAAG,GAAG,EAAE,CAAC;oBAClC,YAAY,GAAG,QAAQ,CAAC;gBAC1B,CAAC;qBAAM,CAAC;oBACN,YAAY,GAAG,KAAK,CAAC;gBACvB,CAAC;gBACD,MAAM;YACR,CAAC;QACH,CAAC;QAED,OAAO;YACL,iBAAiB,EAAE,eAAe,CAAC,EAAE;YACrC,QAAQ,EAAE,CAAC,EAAE,sBAAsB;YACnC,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,UAAU;YAChB,YAAY,EAAE,YAAY;SAC3B,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,8BAA8B,CAC3C,cAA4C,EAC5C,iBAA+C;QAG/C,qDAAqD;QACrD,OAAO;YACL,iBAAiB,EAAE,iBAAiB,CAAC,EAAE;YACvC,QAAQ,EAAE,CAAC;YACX,MAAM,EAAE,GAAG,EAAE,8CAA8C;YAC3D,IAAI,EAAE,YAAY;YAClB,YAAY,EAAE,KAAK;SACpB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,2BAA2B,CACxC,MAAoC,EACpC,cAA8B,EAC9B,YAAoB;QAGpB,MAAM,gBAAgB,GAAG,IAAI,CAAC,yBAAyB,CAAC,cAAc,CAAC,QAAQ,EAAE,cAAc,CAAC,UAAU,CAAC,CAAC;QAE5G,8DAA8D;QAC9D,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC,GAAG,YAAY,GAAG,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAEjG,+DAA+D;QAC/D,MAAM,eAAe,GAAG,IAAI,CAAC,wBAAwB,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;QAE9E,wBAAwB;QACxB,MAAM,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,cAAc,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;QAErF,6DAA6D;QAC7D,MAAM,cAAc,GAAG,MAAM,CAAC,mBAAmB,CAAC,eAAe,CAAC,eAAe,GAAG,GAAG,CAAC;QAExF,8BAA8B;QAC9B,MAAM,gBAAgB,GAAG,MAAM,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,sBAAsB,GAAG,GAAG,CAAC;QAEnG,OAAO;YACL,UAAU,EAAE,UAAU;YACtB,eAAe,EAAE,eAAe;YAChC,UAAU,EAAE,UAAU;YACtB,cAAc,EAAE,cAAc;YAC9B,gBAAgB,EAAE,gBAAgB;SACnC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,wBAAwB,CACrC,MAAoC,EACpC,cAA8B;QAG9B,oEAAoE;QACpE,MAAM,aAAa,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,qBAAqB;QAC5F,MAAM,eAAe,GAAG,MAAM,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,mBAAmB,GAAG,GAAG,CAAC;QAC/F,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,IAAI,MAAM,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;YACpC,YAAY,GAAG,CAAC,CAAC;QACnB,CAAC;aAAM,IAAI,MAAM,CAAC,UAAU,KAAK,UAAU,EAAE,CAAC;YAC5C,YAAY,GAAG,CAAC,CAAC;QACnB,CAAC;QAED,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,aAAa,GAAG,eAAe,GAAG,YAAY,CAAC,CAAC;IACrE,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,mBAAmB,CAAC,UAAkB,EAAE,YAAoB;QACzE,sEAAsE;QACtE,8BAA8B;QAC9B,MAAM,aAAa,GAAG,IAAI,CAAC;QAC3B,OAAO,CAAC,UAAU,GAAG,YAAY,GAAG,GAAG,CAAC,GAAG,aAAa,CAAC;IAC3D,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,6BAA6B,CAC1C,MAAoC,EACpC,cAA8B;QAG9B,oDAAoD;QACpD,MAAM,cAAc,GAAG,MAAM,CAAC,mBAAmB,CAAC,cAAc,CAAC;QACjE,IAAI,cAAc,CAAC,UAAU,GAAG,cAAc,CAAC,OAAO;YAClD,cAAc,CAAC,UAAU,GAAG,cAAc,CAAC,OAAO,EAAE,CAAC;YACvD,MAAM,IAAI,KAAK,CAAC,QAAQ,cAAc,CAAC,UAAU,yCAAyC,cAAc,CAAC,OAAO,KAAK,cAAc,CAAC,OAAO,OAAO,CAAC,CAAC;QACtJ,CAAC;QAED,kCAAkC;QAClC,IAAI,cAAc,CAAC,QAAQ,GAAG,GAAG,IAAI,cAAc,CAAC,QAAQ,GAAG,IAAI,EAAE,CAAC;YACpE,MAAM,IAAI,KAAK,CAAC,YAAY,cAAc,CAAC,QAAQ,+CAA+C,CAAC,CAAC;QACtG,CAAC;QAED,2BAA2B;QAC3B,IAAI,cAAc,CAAC,cAAc,GAAG,IAAI,EAAE,CAAC;YACzC,MAAM,IAAI,KAAK,CAAC,mBAAmB,cAAc,CAAC,cAAc,0CAA0C,CAAC,CAAC;QAC9G,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,eAAe,CAC5B,MAAoC,EACpC,cAA8B,EAC9B,YAAoB;QAGpB,MAAM,MAAM,GAAsB,EAAE,CAAC;QACrC,MAAM,QAAQ,GAAwB,EAAE,CAAC;QAEzC,uCAAuC;QACvC,MAAM,gBAAgB,GAAG,IAAI,CAAC,yBAAyB,CAAC,cAAc,CAAC,QAAQ,EAAE,cAAc,CAAC,UAAU,CAAC,CAAC;QAC5G,MAAM,OAAO,GAAG,YAAY,GAAG,gBAAgB,CAAC;QAEhD,IAAI,OAAO,GAAG,EAAE,EAAE,CAAC;YACjB,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,MAAM;gBACZ,OAAO,EAAE,6CAA6C;gBACtD,SAAS,EAAE,UAAU;gBACrB,KAAK,EAAE,OAAO;aACf,CAAC,CAAC;QACL,CAAC;QAED,IAAI,OAAO,GAAG,CAAC,EAAE,CAAC;YAChB,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,MAAM;gBACZ,OAAO,EAAE,mCAAmC;gBAC5C,SAAS,EAAE,eAAe;gBAC1B,KAAK,EAAE,YAAY;aACpB,CAAC,CAAC;QACL,CAAC;QAED,4CAA4C;QAC5C,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,eAAe,EAAE,CAAC;YAC1C,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,MAAM,EAAE,cAAc,CAAC,CAAC;YAChG,MAAM,UAAU,GAAG,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;YAEjF,IAAI,UAAU,EAAE,CAAC;gBACf,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;oBAC9B,MAAM,CAAC,IAAI,CAAC;wBACV,IAAI,EAAE,IAAI,CAAC,MAAM;wBACjB,OAAO,EAAE,IAAI,CAAC,OAAO;wBACrB,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS;wBACnC,KAAK,EAAE,cAAc;qBACtB,CAAC,CAAC;gBACL,CAAC;qBAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;oBACvC,QAAQ,CAAC,IAAI,CAAC;wBACZ,IAAI,EAAE,IAAI,CAAC,MAAM;wBACjB,OAAO,EAAE,IAAI,CAAC,OAAO;wBACrB,QAAQ,EAAE,QAAQ;wBAClB,cAAc,EAAE,6BAA6B;qBAC9C,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;QAED,2BAA2B;QAC3B,MAAM,iBAAiB,GAAG,MAAM,CAAC,mBAAmB,CAAC,iBAAiB,CAAC;QACvE,MAAM,gBAAgB,GAAG,YAAY,GAAG,CAAC,iBAAiB,CAAC,UAAU,GAAG,iBAAiB,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC;QAE5G,IAAI,gBAAgB,GAAG,YAAY,GAAG,GAAG,EAAE,CAAC;YAC1C,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,MAAM;gBACZ,OAAO,EAAE,+CAA+C;gBACxD,QAAQ,EAAE,QAAQ;gBAClB,cAAc,EAAE,iDAAiD;aAClE,CAAC,CAAC;QACL,CAAC;QAED,MAAM,gBAAgB,GAAqB;YACzC,eAAe,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YACpC,eAAe,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YACpC,kBAAkB,EAAE,IAAI;YACxB,wBAAwB,EAAE,IAAI;SAC/B,CAAC;QAEF,OAAO;YACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC5B,MAAM,EAAE,MAAM;YACd,QAAQ,EAAE,QAAQ;YAClB,gBAAgB,EAAE,gBAAgB;SACnC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,wBAAwB,CAAC,SAAc,EAAE,KAAsB;QAC5E,QAAQ,SAAS,CAAC,QAAQ,EAAE,CAAC;YAC3B,KAAK,GAAG;gBACN,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YACjD,KAAK,GAAG;gBACN,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YACjD,KAAK,IAAI;gBACP,OAAO,MAAM,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YAClD,KAAK,IAAI;gBACP,OAAO,MAAM,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YAClD,KAAK,GAAG;gBACN,OAAO,KAAK,KAAK,SAAS,CAAC,KAAK,CAAC;YACnC,KAAK,IAAI;gBACP,OAAO,KAAK,KAAK,SAAS,CAAC,KAAK,CAAC;YACnC,KAAK,IAAI;gBACP,OAAO,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAC3E,KAAK,QAAQ;gBACX,OAAO,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAC5E;gBACE,OAAO,KAAK,CAAC;QACjB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,uBAAuB,CACpC,MAAoC,EACpC,cAA8B,EAC9B,kBAAsC,EACtC,iBAAoC;QAGpC,MAAM,eAAe,GAAqB,EAAE,CAAC;QAE7C,oCAAoC;QACpC,IAAI,kBAAkB,CAAC,UAAU,GAAG,EAAE,EAAE,CAAC;YACvC,eAAe,CAAC,IAAI,CAAC;gBACnB,IAAI,EAAE,cAAc;gBACpB,QAAQ,EAAE,QAAQ;gBAClB,WAAW,EAAE,wEAAwE;gBACrF,eAAe,EAAE,4DAA4D;gBAC7E,kBAAkB,EAAE,QAAQ;aAC7B,CAAC,CAAC;QACL,CAAC;QAED,IAAI,kBAAkB,CAAC,eAAe,GAAG,EAAE,EAAE,CAAC;YAC5C,eAAe,CAAC,IAAI,CAAC;gBACnB,IAAI,EAAE,cAAc;gBACpB,QAAQ,EAAE,QAAQ;gBAClB,WAAW,EAAE,0DAA0D;gBACvE,eAAe,EAAE,2BAA2B;gBAC5C,kBAAkB,EAAE,QAAQ;aAC7B,CAAC,CAAC;QACL,CAAC;QAED,6BAA6B;QAC7B,MAAM,cAAc,GAAG,MAAM,CAAC,mBAAmB,CAAC,cAAc,CAAC;QACjE,MAAM,SAAS,GAAG,cAAc,CAAC,UAAU,GAAG,cAAc,CAAC,OAAO,CAAC;QAErE,IAAI,SAAS,GAAG,GAAG,EAAE,CAAC;YACpB,eAAe,CAAC,IAAI,CAAC;gBACnB,IAAI,EAAE,YAAY;gBAClB,QAAQ,EAAE,KAAK;gBACf,WAAW,EAAE,uEAAuE;gBACpF,eAAe,EAAE,mCAAmC;gBACpD,kBAAkB,EAAE,MAAM;aAC3B,CAAC,CAAC;QACL,CAAC;QAED,IAAI,SAAS,GAAG,GAAG,EAAE,CAAC;YACpB,eAAe,CAAC,IAAI,CAAC;gBACnB,IAAI,EAAE,YAAY;gBAClB,QAAQ,EAAE,MAAM;gBAChB,WAAW,EAAE,wDAAwD;gBACrE,eAAe,EAAE,iCAAiC;gBAClD,kBAAkB,EAAE,MAAM;aAC3B,CAAC,CAAC;QACL,CAAC;QAED,8BAA8B;QAC9B,IAAI,MAAM,CAAC,QAAQ,KAAK,SAAS,IAAI,MAAM,CAAC,QAAQ,KAAK,UAAU,EAAE,CAAC;YACpE,eAAe,CAAC,IAAI,CAAC;gBACnB,IAAI,EAAE,aAAa;gBACnB,QAAQ,EAAE,KAAK;gBACf,WAAW,EAAE,oEAAoE;gBACjF,eAAe,EAAE,wCAAwC;gBACzD,kBAAkB,EAAE,KAAK;aAC1B,CAAC,CAAC;QACL,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,uBAAuB,CAAC,SAAiB;QACrD,MAAM,IAAI,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAE7C,gDAAgD;QAChD,KAAK,MAAM,YAAY,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;YACxD,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;YAC/C,KAAK,MAAM,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC7C,MAAM,IAAI,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBAChC,KAAK,MAAM,UAAU,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC3C,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC;oBAChC,IAAI,MAAM,CAAC,EAAE,KAAK,SAAS,EAAE,CAAC;wBAC5B,OAAO,MAAsC,CAAC;oBAChD,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,qBAAqB;QACjC,MAAM,IAAI,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAC7C,MAAM,QAAQ,GAA4D,EAAE,CAAC;QAE7E,KAAK,MAAM,YAAY,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;YACxD,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;YAC/C,KAAK,MAAM,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC7C,MAAM,IAAI,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBAChC,KAAK,MAAM,UAAU,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC3C,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC;oBAChC,QAAQ,CAAC,IAAI,CAAC;wBACZ,EAAE,EAAE,MAAM,CAAC,EAAE;wBACb,WAAW,EAAE,MAAM,CAAC,WAAW;wBAC/B,QAAQ,EAAE,MAAM,CAAC,QAAQ;qBAC1B,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;IACrH,CAAC;;AAr0BH,8DAs0BC;AAr0BgB,8CAAoB,GAAQ,IAAI,CAAC;AACxB,wCAAc,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,mCAAmC,CAAC,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\AdvancedFittingCalculator.ts"],
      sourcesContent: ["/**\r\n * Advanced Fitting Calculator for Phase 3 Duct Physics Implementation\r\n * \r\n * This service extends the basic FittingLossCalculator with advanced capabilities including:\r\n * - Multi-parameter K-factor calculations\r\n * - Performance curve interpolation\r\n * - Interaction effects between adjacent fittings\r\n * - Method selection algorithms\r\n * - Complex fitting configurations\r\n * \r\n * @version 3.0.0\r\n * <AUTHOR> Suite Development Team\r\n */\r\n\r\nimport * as fs from 'fs';\r\nimport * as path from 'path';\r\nimport { FittingLossCalculator } from './FittingLossCalculator';\r\nimport {\r\n  AdvancedFittingConfiguration,\r\n  FlowConditions,\r\n  SystemContext,\r\n  AdvancedFittingLossResult,\r\n  CalculationMethod,\r\n  InteractionEffects,\r\n  FittingInteraction,\r\n  PerformanceMetrics,\r\n  ValidationResults,\r\n  ValidationError,\r\n  ValidationWarning,\r\n  ComplianceStatus,\r\n  Recommendation,\r\n  ParameterDependency,\r\n  PerformanceCurve\r\n} from './types/AdvancedFittingTypes';\r\n\r\nexport class AdvancedFittingCalculator extends FittingLossCalculator {\r\n  private static advancedFittingsData: any = null;\r\n  private static readonly DATA_FILE_PATH = path.join(__dirname, '../../data/advanced_fittings.json');\r\n\r\n  /**\r\n   * Load advanced fittings database\r\n   */\r\n  private static loadAdvancedFittingsData(): any {\r\n    if (!this.advancedFittingsData) {\r\n      try {\r\n        const dataContent = fs.readFileSync(this.DATA_FILE_PATH, 'utf8');\r\n        this.advancedFittingsData = JSON.parse(dataContent);\r\n      } catch (error) {\r\n        console.error('Error loading advanced fittings data:', error);\r\n        throw new Error('Failed to load advanced fittings database');\r\n      }\r\n    }\r\n    return this.advancedFittingsData;\r\n  }\r\n\r\n  /**\r\n   * Calculate pressure loss for advanced fitting configurations\r\n   */\r\n  public static calculateAdvancedFittingLoss(\r\n    config: AdvancedFittingConfiguration,\r\n    flowConditions: FlowConditions,\r\n    systemContext?: SystemContext\r\n  ): AdvancedFittingLossResult {\r\n    \r\n    // Validate configuration and conditions\r\n    this.validateAdvancedConfiguration(config, flowConditions);\r\n    \r\n    // Select appropriate calculation method\r\n    const method = this.selectCalculationMethod(config, flowConditions);\r\n    \r\n    // Calculate base pressure loss\r\n    const baseLoss = this.calculateBasePressureLoss(config, flowConditions, method);\r\n    \r\n    // Apply correction factors\r\n    const correctedLoss = this.applyCorrectionFactors(baseLoss, config, flowConditions);\r\n    \r\n    // Calculate interaction effects if system context provided\r\n    const interactionEffects = systemContext \r\n      ? this.calculateInteractionEffects(config, systemContext)\r\n      : this.getDefaultInteractionEffects();\r\n    \r\n    // Apply interaction effects\r\n    const finalPressureLoss = correctedLoss * interactionEffects.totalInteractionFactor;\r\n    \r\n    // Calculate performance metrics\r\n    const performanceMetrics = this.calculatePerformanceMetrics(config, flowConditions, finalPressureLoss);\r\n    \r\n    // Validate results\r\n    const validationResults = this.validateResults(config, flowConditions, finalPressureLoss);\r\n    \r\n    // Generate recommendations\r\n    const recommendations = this.generateRecommendations(config, flowConditions, performanceMetrics, validationResults);\r\n    \r\n    // Generate comprehensive result\r\n    return {\r\n      pressureLoss: finalPressureLoss,\r\n      velocityPressure: this.calculateVelocityPressure(flowConditions.velocity, flowConditions.airDensity),\r\n      kFactor: finalPressureLoss / this.calculateVelocityPressure(flowConditions.velocity, flowConditions.airDensity),\r\n      warnings: validationResults.warnings.map(w => w.message),\r\n      calculationMethod: method,\r\n      interactionEffects: interactionEffects,\r\n      performanceMetrics: performanceMetrics,\r\n      validationResults: validationResults,\r\n      recommendations: recommendations\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Select optimal calculation method based on fitting and flow conditions\r\n   */\r\n  private static selectCalculationMethod(\r\n    config: AdvancedFittingConfiguration,\r\n    flowConditions: FlowConditions\r\n  ): CalculationMethod {\r\n    \r\n    // High Reynolds number flows - use CFD-derived data if available\r\n    if (flowConditions.reynoldsNumber > 100000 && \r\n        config.pressureLossProfile.calculationMethod === CalculationMethod.CFD_DERIVED) {\r\n      return CalculationMethod.CFD_DERIVED;\r\n    }\r\n    \r\n    // Complex geometry fittings - use multi-parameter approach\r\n    if (config.complexity === 'complex' && \r\n        config.pressureLossProfile.kFactorData.parameterDependencies.length > 0) {\r\n      return CalculationMethod.MULTI_PARAMETER;\r\n    }\r\n    \r\n    // Variable performance fittings - use performance curves\r\n    if (config.complexity === 'variable' && \r\n        config.pressureLossProfile.performanceCurves && \r\n        config.pressureLossProfile.performanceCurves.length > 0) {\r\n      return CalculationMethod.PERFORMANCE_CURVE;\r\n    }\r\n    \r\n    // Default to single K-factor for simple fittings\r\n    return CalculationMethod.SINGLE_K_FACTOR;\r\n  }\r\n\r\n  /**\r\n   * Calculate base pressure loss using selected method\r\n   */\r\n  private static calculateBasePressureLoss(\r\n    config: AdvancedFittingConfiguration,\r\n    flowConditions: FlowConditions,\r\n    method: CalculationMethod\r\n  ): number {\r\n    \r\n    const velocityPressure = this.calculateVelocityPressure(flowConditions.velocity, flowConditions.airDensity);\r\n    \r\n    switch (method) {\r\n      case CalculationMethod.SINGLE_K_FACTOR:\r\n        return this.calculateSingleKFactorLoss(config, velocityPressure);\r\n        \r\n      case CalculationMethod.MULTI_PARAMETER:\r\n        return this.calculateMultiParameterLoss(config, flowConditions, velocityPressure);\r\n        \r\n      case CalculationMethod.PERFORMANCE_CURVE:\r\n        return this.calculatePerformanceCurveLoss(config, flowConditions, velocityPressure);\r\n        \r\n      case CalculationMethod.CFD_DERIVED:\r\n        return this.calculateCFDDerivedLoss(config, flowConditions, velocityPressure);\r\n        \r\n      default:\r\n        throw new Error(`Unsupported calculation method: ${method}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Calculate single K-factor pressure loss\r\n   */\r\n  private static calculateSingleKFactorLoss(\r\n    config: AdvancedFittingConfiguration,\r\n    velocityPressure: number\r\n  ): number {\r\n    return config.pressureLossProfile.kFactorData.baseKFactor * velocityPressure;\r\n  }\r\n\r\n  /**\r\n   * Calculate multi-parameter pressure loss\r\n   */\r\n  private static calculateMultiParameterLoss(\r\n    config: AdvancedFittingConfiguration,\r\n    flowConditions: FlowConditions,\r\n    velocityPressure: number\r\n  ): number {\r\n    \r\n    let kFactor = config.pressureLossProfile.kFactorData.baseKFactor;\r\n    \r\n    // Apply parameter dependencies\r\n    for (const dependency of config.pressureLossProfile.kFactorData.parameterDependencies) {\r\n      const parameterValue = this.getParameterValue(dependency.parameter, config, flowConditions);\r\n      const correction = this.calculateParameterCorrection(dependency, parameterValue);\r\n      kFactor *= correction;\r\n    }\r\n    \r\n    // Apply Reynolds number correction if enabled\r\n    if (config.pressureLossProfile.kFactorData.reynoldsCorrection?.enabled) {\r\n      const reynoldsCorrection = this.calculateReynoldsCorrection(\r\n        config.pressureLossProfile.kFactorData.reynoldsCorrection,\r\n        flowConditions.reynoldsNumber\r\n      );\r\n      kFactor *= reynoldsCorrection;\r\n    }\r\n    \r\n    // Apply geometry corrections\r\n    for (const correction of config.pressureLossProfile.kFactorData.geometryCorrections) {\r\n      kFactor *= correction.correctionFactor;\r\n    }\r\n    \r\n    return kFactor * velocityPressure;\r\n  }\r\n\r\n  /**\r\n   * Calculate performance curve-based pressure loss\r\n   */\r\n  private static calculatePerformanceCurveLoss(\r\n    config: AdvancedFittingConfiguration,\r\n    flowConditions: FlowConditions,\r\n    velocityPressure: number\r\n  ): number {\r\n    \r\n    if (!config.pressureLossProfile.performanceCurves || config.pressureLossProfile.performanceCurves.length === 0) {\r\n      throw new Error('No performance curves available for fitting');\r\n    }\r\n    \r\n    // Find the most appropriate performance curve\r\n    const curve = this.selectPerformanceCurve(config.pressureLossProfile.performanceCurves, flowConditions);\r\n    \r\n    // Get parameter value for interpolation\r\n    const parameterValue = this.getParameterValue(curve.parameter, config, flowConditions);\r\n    \r\n    // Interpolate pressure loss from curve\r\n    const pressureLoss = this.interpolatePerformanceCurve(curve, parameterValue);\r\n    \r\n    return pressureLoss;\r\n  }\r\n\r\n  /**\r\n   * Calculate CFD-derived pressure loss\r\n   */\r\n  private static calculateCFDDerivedLoss(\r\n    config: AdvancedFittingConfiguration,\r\n    flowConditions: FlowConditions,\r\n    velocityPressure: number\r\n  ): number {\r\n    \r\n    // For CFD-derived data, use multi-parameter approach with higher accuracy\r\n    return this.calculateMultiParameterLoss(config, flowConditions, velocityPressure);\r\n  }\r\n\r\n  /**\r\n   * Calculate parameter correction factor\r\n   */\r\n  private static calculateParameterCorrection(\r\n    dependency: ParameterDependency,\r\n    parameterValue: number\r\n  ): number {\r\n    \r\n    // Validate parameter is within valid range\r\n    if (parameterValue < dependency.validRange[0] || parameterValue > dependency.validRange[1]) {\r\n      console.warn(`Parameter ${dependency.parameter} value ${parameterValue} outside valid range [${dependency.validRange[0]}, ${dependency.validRange[1]}]`);\r\n    }\r\n    \r\n    switch (dependency.relationship) {\r\n      case 'linear':\r\n        return dependency.coefficients[0] + dependency.coefficients[1] * parameterValue;\r\n        \r\n      case 'polynomial': {\r\n        let result = 0;\r\n        for (let i = 0; i < dependency.coefficients.length; i++) {\r\n          result += dependency.coefficients[i] * Math.pow(parameterValue, i);\r\n        }\r\n        return result;\r\n      }\r\n        \r\n      case 'exponential':\r\n        return dependency.coefficients[0] * Math.exp(dependency.coefficients[1] * parameterValue);\r\n        \r\n      case 'lookup':\r\n        // For lookup tables, would need additional data structure\r\n        // For now, return base correction\r\n        return 1.0;\r\n        \r\n      default:\r\n        throw new Error(`Unsupported relationship type: ${dependency.relationship}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get parameter value from configuration or flow conditions\r\n   */\r\n  private static getParameterValue(\r\n    parameter: string,\r\n    config: AdvancedFittingConfiguration,\r\n    flowConditions: FlowConditions\r\n  ): number {\r\n    \r\n    switch (parameter) {\r\n      case 'velocity':\r\n        return flowConditions.velocity;\r\n      case 'reynolds_number':\r\n        return flowConditions.reynoldsNumber;\r\n      case 'volume_flow':\r\n        return flowConditions.volumeFlow;\r\n      case 'temperature':\r\n        return flowConditions.temperature;\r\n      case 'pressure':\r\n        return flowConditions.pressure;\r\n      case 'length_to_diameter_ratio':\r\n        return config.physicalProperties.dimensions.length / (config.physicalProperties.dimensions.inletDiameter || 12);\r\n      case 'area_ratio': {\r\n        const inletArea = Math.PI * Math.pow((config.physicalProperties.dimensions.inletDiameter || 12) / 2, 2);\r\n        const outletArea = Math.PI * Math.pow((config.physicalProperties.dimensions.outletDiameter || 12) / 2, 2);\r\n        return outletArea / inletArea;\r\n      }\r\n      case 'aspect_ratio':\r\n        return config.physicalProperties.dimensions.width / config.physicalProperties.dimensions.height;\r\n      default:\r\n        console.warn(`Unknown parameter: ${parameter}, using default value 1.0`);\r\n        return 1.0;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Calculate Reynolds number correction\r\n   */\r\n  private static calculateReynoldsCorrection(\r\n    correction: any,\r\n    reynoldsNumber: number\r\n  ): number {\r\n    \r\n    if (reynoldsNumber < correction.validRange[0] || reynoldsNumber > correction.validRange[1]) {\r\n      return 1.0; // No correction outside valid range\r\n    }\r\n    \r\n    switch (correction.method) {\r\n      case 'colebrook':\r\n        // Simplified Colebrook-White correction\r\n        return 1.0 + correction.coefficients[0] * Math.log10(reynoldsNumber) + correction.coefficients[1];\r\n        \r\n      case 'blasius':\r\n        // Blasius equation correction\r\n        return Math.pow(reynoldsNumber, correction.coefficients[0]) * correction.coefficients[1];\r\n        \r\n      case 'custom': {\r\n        // Custom polynomial correction\r\n        let result = 0;\r\n        for (let i = 0; i < correction.coefficients.length; i++) {\r\n          result += correction.coefficients[i] * Math.pow(reynoldsNumber, i);\r\n        }\r\n        return result;\r\n      }\r\n        \r\n      default:\r\n        return 1.0;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Select appropriate performance curve\r\n   */\r\n  private static selectPerformanceCurve(\r\n    curves: PerformanceCurve[],\r\n    flowConditions: FlowConditions\r\n  ): PerformanceCurve {\r\n    \r\n    // For now, select the first available curve\r\n    // In a more sophisticated implementation, would select based on current conditions\r\n    return curves[0];\r\n  }\r\n\r\n  /**\r\n   * Interpolate value from performance curve\r\n   */\r\n  private static interpolatePerformanceCurve(\r\n    curve: PerformanceCurve,\r\n    parameterValue: number\r\n  ): number {\r\n    \r\n    const points = curve.dataPoints.sort((a, b) => a.x - b.x);\r\n    \r\n    // Check if value is outside curve range\r\n    if (parameterValue <= points[0].x) {\r\n      return points[0].y;\r\n    }\r\n    if (parameterValue >= points[points.length - 1].x) {\r\n      if (curve.extrapolationAllowed) {\r\n        // Simple linear extrapolation\r\n        const lastTwo = points.slice(-2);\r\n        const slope = (lastTwo[1].y - lastTwo[0].y) / (lastTwo[1].x - lastTwo[0].x);\r\n        return lastTwo[1].y + slope * (parameterValue - lastTwo[1].x);\r\n      } else {\r\n        return points[points.length - 1].y;\r\n      }\r\n    }\r\n    \r\n    // Find surrounding points for interpolation\r\n    for (let i = 0; i < points.length - 1; i++) {\r\n      if (parameterValue >= points[i].x && parameterValue <= points[i + 1].x) {\r\n        // Linear interpolation\r\n        const ratio = (parameterValue - points[i].x) / (points[i + 1].x - points[i].x);\r\n        return points[i].y + ratio * (points[i + 1].y - points[i].y);\r\n      }\r\n    }\r\n    \r\n    return points[0].y; // Fallback\r\n  }\r\n\r\n  /**\r\n   * Apply correction factors to pressure loss\r\n   */\r\n  private static applyCorrectionFactors(\r\n    baseLoss: number,\r\n    config: AdvancedFittingConfiguration,\r\n    flowConditions: FlowConditions\r\n  ): number {\r\n    \r\n    let correctedLoss = baseLoss;\r\n    const corrections = config.pressureLossProfile.correctionFactors;\r\n    \r\n    // Temperature correction\r\n    if (corrections.temperatureCorrection) {\r\n      const tempCorrection = this.calculateTemperatureCorrection(flowConditions.temperature);\r\n      correctedLoss *= tempCorrection;\r\n    }\r\n    \r\n    // Density correction\r\n    if (corrections.densityCorrection) {\r\n      const densityCorrection = flowConditions.airDensity / 0.075; // Standard air density\r\n      correctedLoss *= densityCorrection;\r\n    }\r\n    \r\n    // Installation correction (simplified)\r\n    if (corrections.installationCorrection) {\r\n      correctedLoss *= 1.1; // 10% installation factor\r\n    }\r\n    \r\n    return correctedLoss;\r\n  }\r\n\r\n  /**\r\n   * Calculate temperature correction factor\r\n   */\r\n  private static calculateTemperatureCorrection(temperature: number): number {\r\n    // Standard temperature is 70\xB0F\r\n    const standardTemp = 70;\r\n    const tempRatio = (temperature + 459.67) / (standardTemp + 459.67); // Convert to absolute temperature\r\n    return Math.sqrt(tempRatio);\r\n  }\r\n\r\n  /**\r\n   * Calculate interaction effects between adjacent fittings\r\n   */\r\n  private static calculateInteractionEffects(\r\n    config: AdvancedFittingConfiguration,\r\n    systemContext: SystemContext\r\n  ): InteractionEffects {\r\n    \r\n    const upstreamFittings = systemContext.getUpstreamFittings(config.id, 10); // 10 diameters\r\n    const downstreamFittings = systemContext.getDownstreamFittings(config.id, 10);\r\n    \r\n    let interactionFactor = 1.0;\r\n    const interactions: FittingInteraction[] = [];\r\n    \r\n    // Upstream interactions\r\n    for (const upstream of upstreamFittings) {\r\n      const interaction = this.calculateUpstreamInteraction(upstream, config);\r\n      interactionFactor *= interaction.factor;\r\n      interactions.push(interaction);\r\n    }\r\n    \r\n    // Downstream interactions\r\n    for (const downstream of downstreamFittings) {\r\n      const interaction = this.calculateDownstreamInteraction(config, downstream);\r\n      interactionFactor *= interaction.factor;\r\n      interactions.push(interaction);\r\n    }\r\n    \r\n    return {\r\n      totalInteractionFactor: interactionFactor,\r\n      individualInteractions: interactions,\r\n      significantInteractions: interactions.filter(i => Math.abs(i.factor - 1.0) > 0.05)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get default interaction effects when no system context provided\r\n   */\r\n  private static getDefaultInteractionEffects(): InteractionEffects {\r\n    return {\r\n      totalInteractionFactor: 1.0,\r\n      individualInteractions: [],\r\n      significantInteractions: []\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate upstream fitting interaction\r\n   */\r\n  private static calculateUpstreamInteraction(\r\n    upstreamFitting: AdvancedFittingConfiguration,\r\n    currentFitting: AdvancedFittingConfiguration\r\n  ): FittingInteraction {\r\n    \r\n    // Simplified interaction calculation\r\n    // In practice, would use more sophisticated models\r\n    let factor = 1.0;\r\n    let significance: 'low' | 'medium' | 'high' = 'low';\r\n    \r\n    // Check for known interaction effects\r\n    for (const effect of currentFitting.compatibilityMatrix.interactionEffects) {\r\n      if (effect.adjacentFittingType === upstreamFitting.type) {\r\n        factor = effect.magnitude;\r\n        if (effect.magnitude > 1.2) {\r\n          significance = 'high';\r\n        } else if (effect.magnitude > 1.1) {\r\n          significance = 'medium';\r\n        } else {\r\n          significance = 'low';\r\n        }\r\n        break;\r\n      }\r\n    }\r\n    \r\n    return {\r\n      adjacentFittingId: upstreamFitting.id,\r\n      distance: 5, // Simplified distance\r\n      factor: factor,\r\n      type: 'upstream',\r\n      significance: significance\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate downstream fitting interaction\r\n   */\r\n  private static calculateDownstreamInteraction(\r\n    currentFitting: AdvancedFittingConfiguration,\r\n    downstreamFitting: AdvancedFittingConfiguration\r\n  ): FittingInteraction {\r\n\r\n    // Downstream interactions typically have less effect\r\n    return {\r\n      adjacentFittingId: downstreamFitting.id,\r\n      distance: 5,\r\n      factor: 1.0, // Minimal downstream effect for most fittings\r\n      type: 'downstream',\r\n      significance: 'low'\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate performance metrics for the fitting\r\n   */\r\n  private static calculatePerformanceMetrics(\r\n    config: AdvancedFittingConfiguration,\r\n    flowConditions: FlowConditions,\r\n    pressureLoss: number\r\n  ): PerformanceMetrics {\r\n\r\n    const velocityPressure = this.calculateVelocityPressure(flowConditions.velocity, flowConditions.airDensity);\r\n\r\n    // Calculate efficiency (inverse of pressure loss coefficient)\r\n    const efficiency = Math.max(0, Math.min(100, 100 * (1 - pressureLoss / (velocityPressure * 5))));\r\n\r\n    // Estimate noise generation based on velocity and fitting type\r\n    const noiseGeneration = this.calculateNoiseGeneration(config, flowConditions);\r\n\r\n    // Calculate energy loss\r\n    const energyLoss = this.calculateEnergyLoss(flowConditions.volumeFlow, pressureLoss);\r\n\r\n    // Calculate flow uniformity based on fitting characteristics\r\n    const flowUniformity = config.flowCharacteristics.velocityProfile.uniformityIndex * 100;\r\n\r\n    // Calculate pressure recovery\r\n    const pressureRecovery = config.flowCharacteristics.turbulenceFactors.pressureRecoveryFactor * 100;\r\n\r\n    return {\r\n      efficiency: efficiency,\r\n      noiseGeneration: noiseGeneration,\r\n      energyLoss: energyLoss,\r\n      flowUniformity: flowUniformity,\r\n      pressureRecovery: pressureRecovery\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate noise generation for the fitting\r\n   */\r\n  private static calculateNoiseGeneration(\r\n    config: AdvancedFittingConfiguration,\r\n    flowConditions: FlowConditions\r\n  ): number {\r\n\r\n    // Base noise calculation using velocity and fitting characteristics\r\n    const velocityNoise = 20 * Math.log10(flowConditions.velocity / 1000); // Reference 1000 FPM\r\n    const turbulenceNoise = config.flowCharacteristics.turbulenceFactors.turbulenceIntensity * 0.5;\r\n    let fittingNoise = 1;\r\n    if (config.complexity === 'complex') {\r\n      fittingNoise = 5;\r\n    } else if (config.complexity === 'variable') {\r\n      fittingNoise = 3;\r\n    }\r\n\r\n    return Math.max(0, velocityNoise + turbulenceNoise + fittingNoise);\r\n  }\r\n\r\n  /**\r\n   * Calculate energy loss in BTU/hr\r\n   */\r\n  private static calculateEnergyLoss(volumeFlow: number, pressureLoss: number): number {\r\n    // Energy loss = (CFM \xD7 Pressure Loss in in wg \xD7 4.5) / Fan Efficiency\r\n    // Assuming 70% fan efficiency\r\n    const fanEfficiency = 0.70;\r\n    return (volumeFlow * pressureLoss * 4.5) / fanEfficiency;\r\n  }\r\n\r\n  /**\r\n   * Validate advanced configuration and flow conditions\r\n   */\r\n  private static validateAdvancedConfiguration(\r\n    config: AdvancedFittingConfiguration,\r\n    flowConditions: FlowConditions\r\n  ): void {\r\n\r\n    // Validate flow is within fitting's operating range\r\n    const operatingRange = config.flowCharacteristics.operatingRange;\r\n    if (flowConditions.volumeFlow < operatingRange.minimum ||\r\n        flowConditions.volumeFlow > operatingRange.maximum) {\r\n      throw new Error(`Flow ${flowConditions.volumeFlow} CFM outside fitting operating range [${operatingRange.minimum}, ${operatingRange.maximum}] CFM`);\r\n    }\r\n\r\n    // Validate velocity is reasonable\r\n    if (flowConditions.velocity < 100 || flowConditions.velocity > 6000) {\r\n      throw new Error(`Velocity ${flowConditions.velocity} FPM outside reasonable range [100, 6000] FPM`);\r\n    }\r\n\r\n    // Validate Reynolds number\r\n    if (flowConditions.reynoldsNumber < 1000) {\r\n      throw new Error(`Reynolds number ${flowConditions.reynoldsNumber} too low for turbulent flow calculations`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Validate calculation results\r\n   */\r\n  private static validateResults(\r\n    config: AdvancedFittingConfiguration,\r\n    flowConditions: FlowConditions,\r\n    pressureLoss: number\r\n  ): ValidationResults {\r\n\r\n    const errors: ValidationError[] = [];\r\n    const warnings: ValidationWarning[] = [];\r\n\r\n    // Check for unreasonable pressure loss\r\n    const velocityPressure = this.calculateVelocityPressure(flowConditions.velocity, flowConditions.airDensity);\r\n    const kFactor = pressureLoss / velocityPressure;\r\n\r\n    if (kFactor > 10) {\r\n      errors.push({\r\n        code: 'E001',\r\n        message: 'Pressure loss coefficient unreasonably high',\r\n        parameter: 'k_factor',\r\n        value: kFactor\r\n      });\r\n    }\r\n\r\n    if (kFactor < 0) {\r\n      errors.push({\r\n        code: 'E002',\r\n        message: 'Negative pressure loss calculated',\r\n        parameter: 'pressure_loss',\r\n        value: pressureLoss\r\n      });\r\n    }\r\n\r\n    // Apply validation rules from configuration\r\n    for (const rule of config.validationRules) {\r\n      const parameterValue = this.getParameterValue(rule.condition.parameter, config, flowConditions);\r\n      const isViolated = this.checkValidationCondition(rule.condition, parameterValue);\r\n\r\n      if (isViolated) {\r\n        if (rule.severity === 'error') {\r\n          errors.push({\r\n            code: rule.ruleId,\r\n            message: rule.message,\r\n            parameter: rule.condition.parameter,\r\n            value: parameterValue\r\n          });\r\n        } else if (rule.severity === 'warning') {\r\n          warnings.push({\r\n            code: rule.ruleId,\r\n            message: rule.message,\r\n            severity: 'medium',\r\n            recommendation: 'Review operating conditions'\r\n          });\r\n        }\r\n      }\r\n    }\r\n\r\n    // Check uncertainty bounds\r\n    const uncertaintyBounds = config.pressureLossProfile.uncertaintyBounds;\r\n    const uncertaintyRange = pressureLoss * (uncertaintyBounds.upperBound - uncertaintyBounds.lowerBound) / 100;\r\n\r\n    if (uncertaintyRange > pressureLoss * 0.3) {\r\n      warnings.push({\r\n        code: 'W001',\r\n        message: 'High uncertainty in pressure loss calculation',\r\n        severity: 'medium',\r\n        recommendation: 'Consider using more accurate calculation method'\r\n      });\r\n    }\r\n\r\n    const complianceStatus: ComplianceStatus = {\r\n      smacnaCompliant: errors.length === 0,\r\n      ashraeCompliant: errors.length === 0,\r\n      localCodeCompliant: true,\r\n      customStandardsCompliant: true\r\n    };\r\n\r\n    return {\r\n      isValid: errors.length === 0,\r\n      errors: errors,\r\n      warnings: warnings,\r\n      complianceStatus: complianceStatus\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Check validation condition\r\n   */\r\n  private static checkValidationCondition(condition: any, value: number | string): boolean {\r\n    switch (condition.operator) {\r\n      case '>':\r\n        return Number(value) > Number(condition.value);\r\n      case '<':\r\n        return Number(value) < Number(condition.value);\r\n      case '>=':\r\n        return Number(value) >= Number(condition.value);\r\n      case '<=':\r\n        return Number(value) <= Number(condition.value);\r\n      case '=':\r\n        return value === condition.value;\r\n      case '!=':\r\n        return value !== condition.value;\r\n      case 'in':\r\n        return Array.isArray(condition.value) && condition.value.includes(value);\r\n      case 'not_in':\r\n        return Array.isArray(condition.value) && !condition.value.includes(value);\r\n      default:\r\n        return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Generate recommendations based on calculation results\r\n   */\r\n  private static generateRecommendations(\r\n    config: AdvancedFittingConfiguration,\r\n    flowConditions: FlowConditions,\r\n    performanceMetrics: PerformanceMetrics,\r\n    validationResults: ValidationResults\r\n  ): Recommendation[] {\r\n\r\n    const recommendations: Recommendation[] = [];\r\n\r\n    // Performance-based recommendations\r\n    if (performanceMetrics.efficiency < 70) {\r\n      recommendations.push({\r\n        type: 'optimization',\r\n        priority: 'medium',\r\n        description: 'Consider using a more efficient fitting design to reduce pressure loss',\r\n        expectedBenefit: 'Reduced energy consumption and improved system performance',\r\n        implementationCost: 'medium'\r\n      });\r\n    }\r\n\r\n    if (performanceMetrics.noiseGeneration > 50) {\r\n      recommendations.push({\r\n        type: 'optimization',\r\n        priority: 'medium',\r\n        description: 'Consider adding sound attenuation to reduce noise levels',\r\n        expectedBenefit: 'Improved acoustic comfort',\r\n        implementationCost: 'medium'\r\n      });\r\n    }\r\n\r\n    // Flow-based recommendations\r\n    const operatingRange = config.flowCharacteristics.operatingRange;\r\n    const flowRatio = flowConditions.volumeFlow / operatingRange.optimal;\r\n\r\n    if (flowRatio < 0.5) {\r\n      recommendations.push({\r\n        type: 'adjustment',\r\n        priority: 'low',\r\n        description: 'Flow is significantly below optimal range - consider resizing fitting',\r\n        expectedBenefit: 'Better performance and efficiency',\r\n        implementationCost: 'high'\r\n      });\r\n    }\r\n\r\n    if (flowRatio > 1.5) {\r\n      recommendations.push({\r\n        type: 'adjustment',\r\n        priority: 'high',\r\n        description: 'Flow exceeds optimal range - fitting may be undersized',\r\n        expectedBenefit: 'Reduced pressure loss and noise',\r\n        implementationCost: 'high'\r\n      });\r\n    }\r\n\r\n    // Maintenance recommendations\r\n    if (config.category === 'control' || config.category === 'terminal') {\r\n      recommendations.push({\r\n        type: 'maintenance',\r\n        priority: 'low',\r\n        description: 'Regular inspection and calibration recommended for control devices',\r\n        expectedBenefit: 'Maintained performance and reliability',\r\n        implementationCost: 'low'\r\n      });\r\n    }\r\n\r\n    return recommendations;\r\n  }\r\n\r\n  /**\r\n   * Get fitting configuration by ID from database\r\n   */\r\n  public static getFittingConfiguration(fittingId: string): AdvancedFittingConfiguration | null {\r\n    const data = this.loadAdvancedFittingsData();\r\n\r\n    // Search through all categories for the fitting\r\n    for (const categoryName of Object.keys(data.categories)) {\r\n      const category = data.categories[categoryName];\r\n      for (const typeName of Object.keys(category)) {\r\n        const type = category[typeName];\r\n        for (const configName of Object.keys(type)) {\r\n          const config = type[configName];\r\n          if (config.id === fittingId) {\r\n            return config as AdvancedFittingConfiguration;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    return null;\r\n  }\r\n\r\n  /**\r\n   * List all available fitting configurations\r\n   */\r\n  public static listAvailableFittings(): { id: string; description: string; category: string }[] {\r\n    const data = this.loadAdvancedFittingsData();\r\n    const fittings: { id: string; description: string; category: string }[] = [];\r\n\r\n    for (const categoryName of Object.keys(data.categories)) {\r\n      const category = data.categories[categoryName];\r\n      for (const typeName of Object.keys(category)) {\r\n        const type = category[typeName];\r\n        for (const configName of Object.keys(type)) {\r\n          const config = type[configName];\r\n          fittings.push({\r\n            id: config.id,\r\n            description: config.description,\r\n            category: config.category\r\n          });\r\n        }\r\n      }\r\n    }\r\n\r\n    return fittings.sort((a, b) => a.category.localeCompare(b.category) || a.description.localeCompare(b.description));\r\n  }\r\n}\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "033ecd21eb8c2abd4539ea7b1d7e9846a4117eef"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2f6r1jzz3n = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2f6r1jzz3n();
var __createBinding =
/* istanbul ignore next */
(cov_2f6r1jzz3n().s[0]++,
/* istanbul ignore next */
(cov_2f6r1jzz3n().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_2f6r1jzz3n().b[0][1]++, this.__createBinding) ||
/* istanbul ignore next */
(cov_2f6r1jzz3n().b[0][2]++, Object.create ?
/* istanbul ignore next */
(cov_2f6r1jzz3n().b[1][0]++, function (o, m, k, k2) {
  /* istanbul ignore next */
  cov_2f6r1jzz3n().f[0]++;
  cov_2f6r1jzz3n().s[1]++;
  if (k2 === undefined) {
    /* istanbul ignore next */
    cov_2f6r1jzz3n().b[2][0]++;
    cov_2f6r1jzz3n().s[2]++;
    k2 = k;
  } else
  /* istanbul ignore next */
  {
    cov_2f6r1jzz3n().b[2][1]++;
  }
  var desc =
  /* istanbul ignore next */
  (cov_2f6r1jzz3n().s[3]++, Object.getOwnPropertyDescriptor(m, k));
  /* istanbul ignore next */
  cov_2f6r1jzz3n().s[4]++;
  if (
  /* istanbul ignore next */
  (cov_2f6r1jzz3n().b[4][0]++, !desc) ||
  /* istanbul ignore next */
  (cov_2f6r1jzz3n().b[4][1]++, "get" in desc ?
  /* istanbul ignore next */
  (cov_2f6r1jzz3n().b[5][0]++, !m.__esModule) :
  /* istanbul ignore next */
  (cov_2f6r1jzz3n().b[5][1]++,
  /* istanbul ignore next */
  (cov_2f6r1jzz3n().b[6][0]++, desc.writable) ||
  /* istanbul ignore next */
  (cov_2f6r1jzz3n().b[6][1]++, desc.configurable)))) {
    /* istanbul ignore next */
    cov_2f6r1jzz3n().b[3][0]++;
    cov_2f6r1jzz3n().s[5]++;
    desc = {
      enumerable: true,
      get: function () {
        /* istanbul ignore next */
        cov_2f6r1jzz3n().f[1]++;
        cov_2f6r1jzz3n().s[6]++;
        return m[k];
      }
    };
  } else
  /* istanbul ignore next */
  {
    cov_2f6r1jzz3n().b[3][1]++;
  }
  cov_2f6r1jzz3n().s[7]++;
  Object.defineProperty(o, k2, desc);
}) :
/* istanbul ignore next */
(cov_2f6r1jzz3n().b[1][1]++, function (o, m, k, k2) {
  /* istanbul ignore next */
  cov_2f6r1jzz3n().f[2]++;
  cov_2f6r1jzz3n().s[8]++;
  if (k2 === undefined) {
    /* istanbul ignore next */
    cov_2f6r1jzz3n().b[7][0]++;
    cov_2f6r1jzz3n().s[9]++;
    k2 = k;
  } else
  /* istanbul ignore next */
  {
    cov_2f6r1jzz3n().b[7][1]++;
  }
  cov_2f6r1jzz3n().s[10]++;
  o[k2] = m[k];
})));
var __setModuleDefault =
/* istanbul ignore next */
(cov_2f6r1jzz3n().s[11]++,
/* istanbul ignore next */
(cov_2f6r1jzz3n().b[8][0]++, this) &&
/* istanbul ignore next */
(cov_2f6r1jzz3n().b[8][1]++, this.__setModuleDefault) ||
/* istanbul ignore next */
(cov_2f6r1jzz3n().b[8][2]++, Object.create ?
/* istanbul ignore next */
(cov_2f6r1jzz3n().b[9][0]++, function (o, v) {
  /* istanbul ignore next */
  cov_2f6r1jzz3n().f[3]++;
  cov_2f6r1jzz3n().s[12]++;
  Object.defineProperty(o, "default", {
    enumerable: true,
    value: v
  });
}) :
/* istanbul ignore next */
(cov_2f6r1jzz3n().b[9][1]++, function (o, v) {
  /* istanbul ignore next */
  cov_2f6r1jzz3n().f[4]++;
  cov_2f6r1jzz3n().s[13]++;
  o["default"] = v;
})));
var __importStar =
/* istanbul ignore next */
(cov_2f6r1jzz3n().s[14]++,
/* istanbul ignore next */
(cov_2f6r1jzz3n().b[10][0]++, this) &&
/* istanbul ignore next */
(cov_2f6r1jzz3n().b[10][1]++, this.__importStar) ||
/* istanbul ignore next */
(cov_2f6r1jzz3n().b[10][2]++, function () {
  /* istanbul ignore next */
  cov_2f6r1jzz3n().f[5]++;
  cov_2f6r1jzz3n().s[15]++;
  var ownKeys = function (o) {
    /* istanbul ignore next */
    cov_2f6r1jzz3n().f[6]++;
    cov_2f6r1jzz3n().s[16]++;
    ownKeys =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().b[11][0]++, Object.getOwnPropertyNames) ||
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().b[11][1]++, function (o) {
      /* istanbul ignore next */
      cov_2f6r1jzz3n().f[7]++;
      var ar =
      /* istanbul ignore next */
      (cov_2f6r1jzz3n().s[17]++, []);
      /* istanbul ignore next */
      cov_2f6r1jzz3n().s[18]++;
      for (var k in o) {
        /* istanbul ignore next */
        cov_2f6r1jzz3n().s[19]++;
        if (Object.prototype.hasOwnProperty.call(o, k)) {
          /* istanbul ignore next */
          cov_2f6r1jzz3n().b[12][0]++;
          cov_2f6r1jzz3n().s[20]++;
          ar[ar.length] = k;
        } else
        /* istanbul ignore next */
        {
          cov_2f6r1jzz3n().b[12][1]++;
        }
      }
      /* istanbul ignore next */
      cov_2f6r1jzz3n().s[21]++;
      return ar;
    });
    /* istanbul ignore next */
    cov_2f6r1jzz3n().s[22]++;
    return ownKeys(o);
  };
  /* istanbul ignore next */
  cov_2f6r1jzz3n().s[23]++;
  return function (mod) {
    /* istanbul ignore next */
    cov_2f6r1jzz3n().f[8]++;
    cov_2f6r1jzz3n().s[24]++;
    if (
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().b[14][0]++, mod) &&
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().b[14][1]++, mod.__esModule)) {
      /* istanbul ignore next */
      cov_2f6r1jzz3n().b[13][0]++;
      cov_2f6r1jzz3n().s[25]++;
      return mod;
    } else
    /* istanbul ignore next */
    {
      cov_2f6r1jzz3n().b[13][1]++;
    }
    var result =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().s[26]++, {});
    /* istanbul ignore next */
    cov_2f6r1jzz3n().s[27]++;
    if (mod != null) {
      /* istanbul ignore next */
      cov_2f6r1jzz3n().b[15][0]++;
      cov_2f6r1jzz3n().s[28]++;
      for (var k =
        /* istanbul ignore next */
        (cov_2f6r1jzz3n().s[29]++, ownKeys(mod)), i =
        /* istanbul ignore next */
        (cov_2f6r1jzz3n().s[30]++, 0); i < k.length; i++) {
        /* istanbul ignore next */
        cov_2f6r1jzz3n().s[31]++;
        if (k[i] !== "default") {
          /* istanbul ignore next */
          cov_2f6r1jzz3n().b[16][0]++;
          cov_2f6r1jzz3n().s[32]++;
          __createBinding(result, mod, k[i]);
        } else
        /* istanbul ignore next */
        {
          cov_2f6r1jzz3n().b[16][1]++;
        }
      }
    } else
    /* istanbul ignore next */
    {
      cov_2f6r1jzz3n().b[15][1]++;
    }
    cov_2f6r1jzz3n().s[33]++;
    __setModuleDefault(result, mod);
    /* istanbul ignore next */
    cov_2f6r1jzz3n().s[34]++;
    return result;
  };
}()));
/* istanbul ignore next */
cov_2f6r1jzz3n().s[35]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_2f6r1jzz3n().s[36]++;
exports.AdvancedFittingCalculator = void 0;
const fs =
/* istanbul ignore next */
(cov_2f6r1jzz3n().s[37]++, __importStar(require("fs")));
const path =
/* istanbul ignore next */
(cov_2f6r1jzz3n().s[38]++, __importStar(require("path")));
const FittingLossCalculator_1 =
/* istanbul ignore next */
(cov_2f6r1jzz3n().s[39]++, require("./FittingLossCalculator"));
const AdvancedFittingTypes_1 =
/* istanbul ignore next */
(cov_2f6r1jzz3n().s[40]++, require("./types/AdvancedFittingTypes"));
class AdvancedFittingCalculator extends
/* istanbul ignore next */
(FittingLossCalculator_1.FittingLossCalculator) {
  /**
   * Load advanced fittings database
   */
  static loadAdvancedFittingsData() {
    /* istanbul ignore next */
    cov_2f6r1jzz3n().f[9]++;
    cov_2f6r1jzz3n().s[41]++;
    if (!this.advancedFittingsData) {
      /* istanbul ignore next */
      cov_2f6r1jzz3n().b[17][0]++;
      cov_2f6r1jzz3n().s[42]++;
      try {
        const dataContent =
        /* istanbul ignore next */
        (cov_2f6r1jzz3n().s[43]++, fs.readFileSync(this.DATA_FILE_PATH, 'utf8'));
        /* istanbul ignore next */
        cov_2f6r1jzz3n().s[44]++;
        this.advancedFittingsData = JSON.parse(dataContent);
      } catch (error) {
        /* istanbul ignore next */
        cov_2f6r1jzz3n().s[45]++;
        console.error('Error loading advanced fittings data:', error);
        /* istanbul ignore next */
        cov_2f6r1jzz3n().s[46]++;
        throw new Error('Failed to load advanced fittings database');
      }
    } else
    /* istanbul ignore next */
    {
      cov_2f6r1jzz3n().b[17][1]++;
    }
    cov_2f6r1jzz3n().s[47]++;
    return this.advancedFittingsData;
  }
  /**
   * Calculate pressure loss for advanced fitting configurations
   */
  static calculateAdvancedFittingLoss(config, flowConditions, systemContext) {
    /* istanbul ignore next */
    cov_2f6r1jzz3n().f[10]++;
    cov_2f6r1jzz3n().s[48]++;
    // Validate configuration and conditions
    this.validateAdvancedConfiguration(config, flowConditions);
    // Select appropriate calculation method
    const method =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().s[49]++, this.selectCalculationMethod(config, flowConditions));
    // Calculate base pressure loss
    const baseLoss =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().s[50]++, this.calculateBasePressureLoss(config, flowConditions, method));
    // Apply correction factors
    const correctedLoss =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().s[51]++, this.applyCorrectionFactors(baseLoss, config, flowConditions));
    // Calculate interaction effects if system context provided
    const interactionEffects =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().s[52]++, systemContext ?
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().b[18][0]++, this.calculateInteractionEffects(config, systemContext)) :
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().b[18][1]++, this.getDefaultInteractionEffects()));
    // Apply interaction effects
    const finalPressureLoss =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().s[53]++, correctedLoss * interactionEffects.totalInteractionFactor);
    // Calculate performance metrics
    const performanceMetrics =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().s[54]++, this.calculatePerformanceMetrics(config, flowConditions, finalPressureLoss));
    // Validate results
    const validationResults =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().s[55]++, this.validateResults(config, flowConditions, finalPressureLoss));
    // Generate recommendations
    const recommendations =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().s[56]++, this.generateRecommendations(config, flowConditions, performanceMetrics, validationResults));
    // Generate comprehensive result
    /* istanbul ignore next */
    cov_2f6r1jzz3n().s[57]++;
    return {
      pressureLoss: finalPressureLoss,
      velocityPressure: this.calculateVelocityPressure(flowConditions.velocity, flowConditions.airDensity),
      kFactor: finalPressureLoss / this.calculateVelocityPressure(flowConditions.velocity, flowConditions.airDensity),
      warnings: validationResults.warnings.map(w => {
        /* istanbul ignore next */
        cov_2f6r1jzz3n().f[11]++;
        cov_2f6r1jzz3n().s[58]++;
        return w.message;
      }),
      calculationMethod: method,
      interactionEffects: interactionEffects,
      performanceMetrics: performanceMetrics,
      validationResults: validationResults,
      recommendations: recommendations
    };
  }
  /**
   * Select optimal calculation method based on fitting and flow conditions
   */
  static selectCalculationMethod(config, flowConditions) {
    /* istanbul ignore next */
    cov_2f6r1jzz3n().f[12]++;
    cov_2f6r1jzz3n().s[59]++;
    // High Reynolds number flows - use CFD-derived data if available
    if (
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().b[20][0]++, flowConditions.reynoldsNumber > 100000) &&
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().b[20][1]++, config.pressureLossProfile.calculationMethod === AdvancedFittingTypes_1.CalculationMethod.CFD_DERIVED)) {
      /* istanbul ignore next */
      cov_2f6r1jzz3n().b[19][0]++;
      cov_2f6r1jzz3n().s[60]++;
      return AdvancedFittingTypes_1.CalculationMethod.CFD_DERIVED;
    } else
    /* istanbul ignore next */
    {
      cov_2f6r1jzz3n().b[19][1]++;
    }
    // Complex geometry fittings - use multi-parameter approach
    cov_2f6r1jzz3n().s[61]++;
    if (
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().b[22][0]++, config.complexity === 'complex') &&
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().b[22][1]++, config.pressureLossProfile.kFactorData.parameterDependencies.length > 0)) {
      /* istanbul ignore next */
      cov_2f6r1jzz3n().b[21][0]++;
      cov_2f6r1jzz3n().s[62]++;
      return AdvancedFittingTypes_1.CalculationMethod.MULTI_PARAMETER;
    } else
    /* istanbul ignore next */
    {
      cov_2f6r1jzz3n().b[21][1]++;
    }
    // Variable performance fittings - use performance curves
    cov_2f6r1jzz3n().s[63]++;
    if (
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().b[24][0]++, config.complexity === 'variable') &&
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().b[24][1]++, config.pressureLossProfile.performanceCurves) &&
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().b[24][2]++, config.pressureLossProfile.performanceCurves.length > 0)) {
      /* istanbul ignore next */
      cov_2f6r1jzz3n().b[23][0]++;
      cov_2f6r1jzz3n().s[64]++;
      return AdvancedFittingTypes_1.CalculationMethod.PERFORMANCE_CURVE;
    } else
    /* istanbul ignore next */
    {
      cov_2f6r1jzz3n().b[23][1]++;
    }
    // Default to single K-factor for simple fittings
    cov_2f6r1jzz3n().s[65]++;
    return AdvancedFittingTypes_1.CalculationMethod.SINGLE_K_FACTOR;
  }
  /**
   * Calculate base pressure loss using selected method
   */
  static calculateBasePressureLoss(config, flowConditions, method) {
    /* istanbul ignore next */
    cov_2f6r1jzz3n().f[13]++;
    const velocityPressure =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().s[66]++, this.calculateVelocityPressure(flowConditions.velocity, flowConditions.airDensity));
    /* istanbul ignore next */
    cov_2f6r1jzz3n().s[67]++;
    switch (method) {
      case AdvancedFittingTypes_1.CalculationMethod.SINGLE_K_FACTOR:
        /* istanbul ignore next */
        cov_2f6r1jzz3n().b[25][0]++;
        cov_2f6r1jzz3n().s[68]++;
        return this.calculateSingleKFactorLoss(config, velocityPressure);
      case AdvancedFittingTypes_1.CalculationMethod.MULTI_PARAMETER:
        /* istanbul ignore next */
        cov_2f6r1jzz3n().b[25][1]++;
        cov_2f6r1jzz3n().s[69]++;
        return this.calculateMultiParameterLoss(config, flowConditions, velocityPressure);
      case AdvancedFittingTypes_1.CalculationMethod.PERFORMANCE_CURVE:
        /* istanbul ignore next */
        cov_2f6r1jzz3n().b[25][2]++;
        cov_2f6r1jzz3n().s[70]++;
        return this.calculatePerformanceCurveLoss(config, flowConditions, velocityPressure);
      case AdvancedFittingTypes_1.CalculationMethod.CFD_DERIVED:
        /* istanbul ignore next */
        cov_2f6r1jzz3n().b[25][3]++;
        cov_2f6r1jzz3n().s[71]++;
        return this.calculateCFDDerivedLoss(config, flowConditions, velocityPressure);
      default:
        /* istanbul ignore next */
        cov_2f6r1jzz3n().b[25][4]++;
        cov_2f6r1jzz3n().s[72]++;
        throw new Error(`Unsupported calculation method: ${method}`);
    }
  }
  /**
   * Calculate single K-factor pressure loss
   */
  static calculateSingleKFactorLoss(config, velocityPressure) {
    /* istanbul ignore next */
    cov_2f6r1jzz3n().f[14]++;
    cov_2f6r1jzz3n().s[73]++;
    return config.pressureLossProfile.kFactorData.baseKFactor * velocityPressure;
  }
  /**
   * Calculate multi-parameter pressure loss
   */
  static calculateMultiParameterLoss(config, flowConditions, velocityPressure) {
    /* istanbul ignore next */
    cov_2f6r1jzz3n().f[15]++;
    let kFactor =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().s[74]++, config.pressureLossProfile.kFactorData.baseKFactor);
    // Apply parameter dependencies
    /* istanbul ignore next */
    cov_2f6r1jzz3n().s[75]++;
    for (const dependency of config.pressureLossProfile.kFactorData.parameterDependencies) {
      const parameterValue =
      /* istanbul ignore next */
      (cov_2f6r1jzz3n().s[76]++, this.getParameterValue(dependency.parameter, config, flowConditions));
      const correction =
      /* istanbul ignore next */
      (cov_2f6r1jzz3n().s[77]++, this.calculateParameterCorrection(dependency, parameterValue));
      /* istanbul ignore next */
      cov_2f6r1jzz3n().s[78]++;
      kFactor *= correction;
    }
    // Apply Reynolds number correction if enabled
    /* istanbul ignore next */
    cov_2f6r1jzz3n().s[79]++;
    if (config.pressureLossProfile.kFactorData.reynoldsCorrection?.enabled) {
      /* istanbul ignore next */
      cov_2f6r1jzz3n().b[26][0]++;
      const reynoldsCorrection =
      /* istanbul ignore next */
      (cov_2f6r1jzz3n().s[80]++, this.calculateReynoldsCorrection(config.pressureLossProfile.kFactorData.reynoldsCorrection, flowConditions.reynoldsNumber));
      /* istanbul ignore next */
      cov_2f6r1jzz3n().s[81]++;
      kFactor *= reynoldsCorrection;
    } else
    /* istanbul ignore next */
    {
      cov_2f6r1jzz3n().b[26][1]++;
    }
    // Apply geometry corrections
    cov_2f6r1jzz3n().s[82]++;
    for (const correction of config.pressureLossProfile.kFactorData.geometryCorrections) {
      /* istanbul ignore next */
      cov_2f6r1jzz3n().s[83]++;
      kFactor *= correction.correctionFactor;
    }
    /* istanbul ignore next */
    cov_2f6r1jzz3n().s[84]++;
    return kFactor * velocityPressure;
  }
  /**
   * Calculate performance curve-based pressure loss
   */
  static calculatePerformanceCurveLoss(config, flowConditions, velocityPressure) {
    /* istanbul ignore next */
    cov_2f6r1jzz3n().f[16]++;
    cov_2f6r1jzz3n().s[85]++;
    if (
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().b[28][0]++, !config.pressureLossProfile.performanceCurves) ||
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().b[28][1]++, config.pressureLossProfile.performanceCurves.length === 0)) {
      /* istanbul ignore next */
      cov_2f6r1jzz3n().b[27][0]++;
      cov_2f6r1jzz3n().s[86]++;
      throw new Error('No performance curves available for fitting');
    } else
    /* istanbul ignore next */
    {
      cov_2f6r1jzz3n().b[27][1]++;
    }
    // Find the most appropriate performance curve
    const curve =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().s[87]++, this.selectPerformanceCurve(config.pressureLossProfile.performanceCurves, flowConditions));
    // Get parameter value for interpolation
    const parameterValue =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().s[88]++, this.getParameterValue(curve.parameter, config, flowConditions));
    // Interpolate pressure loss from curve
    const pressureLoss =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().s[89]++, this.interpolatePerformanceCurve(curve, parameterValue));
    /* istanbul ignore next */
    cov_2f6r1jzz3n().s[90]++;
    return pressureLoss;
  }
  /**
   * Calculate CFD-derived pressure loss
   */
  static calculateCFDDerivedLoss(config, flowConditions, velocityPressure) {
    /* istanbul ignore next */
    cov_2f6r1jzz3n().f[17]++;
    cov_2f6r1jzz3n().s[91]++;
    // For CFD-derived data, use multi-parameter approach with higher accuracy
    return this.calculateMultiParameterLoss(config, flowConditions, velocityPressure);
  }
  /**
   * Calculate parameter correction factor
   */
  static calculateParameterCorrection(dependency, parameterValue) {
    /* istanbul ignore next */
    cov_2f6r1jzz3n().f[18]++;
    cov_2f6r1jzz3n().s[92]++;
    // Validate parameter is within valid range
    if (
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().b[30][0]++, parameterValue < dependency.validRange[0]) ||
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().b[30][1]++, parameterValue > dependency.validRange[1])) {
      /* istanbul ignore next */
      cov_2f6r1jzz3n().b[29][0]++;
      cov_2f6r1jzz3n().s[93]++;
      console.warn(`Parameter ${dependency.parameter} value ${parameterValue} outside valid range [${dependency.validRange[0]}, ${dependency.validRange[1]}]`);
    } else
    /* istanbul ignore next */
    {
      cov_2f6r1jzz3n().b[29][1]++;
    }
    cov_2f6r1jzz3n().s[94]++;
    switch (dependency.relationship) {
      case 'linear':
        /* istanbul ignore next */
        cov_2f6r1jzz3n().b[31][0]++;
        cov_2f6r1jzz3n().s[95]++;
        return dependency.coefficients[0] + dependency.coefficients[1] * parameterValue;
      case 'polynomial':
        /* istanbul ignore next */
        cov_2f6r1jzz3n().b[31][1]++;
        {
          let result =
          /* istanbul ignore next */
          (cov_2f6r1jzz3n().s[96]++, 0);
          /* istanbul ignore next */
          cov_2f6r1jzz3n().s[97]++;
          for (let i =
          /* istanbul ignore next */
          (cov_2f6r1jzz3n().s[98]++, 0); i < dependency.coefficients.length; i++) {
            /* istanbul ignore next */
            cov_2f6r1jzz3n().s[99]++;
            result += dependency.coefficients[i] * Math.pow(parameterValue, i);
          }
          /* istanbul ignore next */
          cov_2f6r1jzz3n().s[100]++;
          return result;
        }
      case 'exponential':
        /* istanbul ignore next */
        cov_2f6r1jzz3n().b[31][2]++;
        cov_2f6r1jzz3n().s[101]++;
        return dependency.coefficients[0] * Math.exp(dependency.coefficients[1] * parameterValue);
      case 'lookup':
        /* istanbul ignore next */
        cov_2f6r1jzz3n().b[31][3]++;
        cov_2f6r1jzz3n().s[102]++;
        // For lookup tables, would need additional data structure
        // For now, return base correction
        return 1.0;
      default:
        /* istanbul ignore next */
        cov_2f6r1jzz3n().b[31][4]++;
        cov_2f6r1jzz3n().s[103]++;
        throw new Error(`Unsupported relationship type: ${dependency.relationship}`);
    }
  }
  /**
   * Get parameter value from configuration or flow conditions
   */
  static getParameterValue(parameter, config, flowConditions) {
    /* istanbul ignore next */
    cov_2f6r1jzz3n().f[19]++;
    cov_2f6r1jzz3n().s[104]++;
    switch (parameter) {
      case 'velocity':
        /* istanbul ignore next */
        cov_2f6r1jzz3n().b[32][0]++;
        cov_2f6r1jzz3n().s[105]++;
        return flowConditions.velocity;
      case 'reynolds_number':
        /* istanbul ignore next */
        cov_2f6r1jzz3n().b[32][1]++;
        cov_2f6r1jzz3n().s[106]++;
        return flowConditions.reynoldsNumber;
      case 'volume_flow':
        /* istanbul ignore next */
        cov_2f6r1jzz3n().b[32][2]++;
        cov_2f6r1jzz3n().s[107]++;
        return flowConditions.volumeFlow;
      case 'temperature':
        /* istanbul ignore next */
        cov_2f6r1jzz3n().b[32][3]++;
        cov_2f6r1jzz3n().s[108]++;
        return flowConditions.temperature;
      case 'pressure':
        /* istanbul ignore next */
        cov_2f6r1jzz3n().b[32][4]++;
        cov_2f6r1jzz3n().s[109]++;
        return flowConditions.pressure;
      case 'length_to_diameter_ratio':
        /* istanbul ignore next */
        cov_2f6r1jzz3n().b[32][5]++;
        cov_2f6r1jzz3n().s[110]++;
        return config.physicalProperties.dimensions.length / (
        /* istanbul ignore next */
        (cov_2f6r1jzz3n().b[33][0]++, config.physicalProperties.dimensions.inletDiameter) ||
        /* istanbul ignore next */
        (cov_2f6r1jzz3n().b[33][1]++, 12));
      case 'area_ratio':
        /* istanbul ignore next */
        cov_2f6r1jzz3n().b[32][6]++;
        {
          const inletArea =
          /* istanbul ignore next */
          (cov_2f6r1jzz3n().s[111]++, Math.PI * Math.pow((
          /* istanbul ignore next */
          (cov_2f6r1jzz3n().b[34][0]++, config.physicalProperties.dimensions.inletDiameter) ||
          /* istanbul ignore next */
          (cov_2f6r1jzz3n().b[34][1]++, 12)) / 2, 2));
          const outletArea =
          /* istanbul ignore next */
          (cov_2f6r1jzz3n().s[112]++, Math.PI * Math.pow((
          /* istanbul ignore next */
          (cov_2f6r1jzz3n().b[35][0]++, config.physicalProperties.dimensions.outletDiameter) ||
          /* istanbul ignore next */
          (cov_2f6r1jzz3n().b[35][1]++, 12)) / 2, 2));
          /* istanbul ignore next */
          cov_2f6r1jzz3n().s[113]++;
          return outletArea / inletArea;
        }
      case 'aspect_ratio':
        /* istanbul ignore next */
        cov_2f6r1jzz3n().b[32][7]++;
        cov_2f6r1jzz3n().s[114]++;
        return config.physicalProperties.dimensions.width / config.physicalProperties.dimensions.height;
      default:
        /* istanbul ignore next */
        cov_2f6r1jzz3n().b[32][8]++;
        cov_2f6r1jzz3n().s[115]++;
        console.warn(`Unknown parameter: ${parameter}, using default value 1.0`);
        /* istanbul ignore next */
        cov_2f6r1jzz3n().s[116]++;
        return 1.0;
    }
  }
  /**
   * Calculate Reynolds number correction
   */
  static calculateReynoldsCorrection(correction, reynoldsNumber) {
    /* istanbul ignore next */
    cov_2f6r1jzz3n().f[20]++;
    cov_2f6r1jzz3n().s[117]++;
    if (
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().b[37][0]++, reynoldsNumber < correction.validRange[0]) ||
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().b[37][1]++, reynoldsNumber > correction.validRange[1])) {
      /* istanbul ignore next */
      cov_2f6r1jzz3n().b[36][0]++;
      cov_2f6r1jzz3n().s[118]++;
      return 1.0; // No correction outside valid range
    } else
    /* istanbul ignore next */
    {
      cov_2f6r1jzz3n().b[36][1]++;
    }
    cov_2f6r1jzz3n().s[119]++;
    switch (correction.method) {
      case 'colebrook':
        /* istanbul ignore next */
        cov_2f6r1jzz3n().b[38][0]++;
        cov_2f6r1jzz3n().s[120]++;
        // Simplified Colebrook-White correction
        return 1.0 + correction.coefficients[0] * Math.log10(reynoldsNumber) + correction.coefficients[1];
      case 'blasius':
        /* istanbul ignore next */
        cov_2f6r1jzz3n().b[38][1]++;
        cov_2f6r1jzz3n().s[121]++;
        // Blasius equation correction
        return Math.pow(reynoldsNumber, correction.coefficients[0]) * correction.coefficients[1];
      case 'custom':
        /* istanbul ignore next */
        cov_2f6r1jzz3n().b[38][2]++;
        {
          // Custom polynomial correction
          let result =
          /* istanbul ignore next */
          (cov_2f6r1jzz3n().s[122]++, 0);
          /* istanbul ignore next */
          cov_2f6r1jzz3n().s[123]++;
          for (let i =
          /* istanbul ignore next */
          (cov_2f6r1jzz3n().s[124]++, 0); i < correction.coefficients.length; i++) {
            /* istanbul ignore next */
            cov_2f6r1jzz3n().s[125]++;
            result += correction.coefficients[i] * Math.pow(reynoldsNumber, i);
          }
          /* istanbul ignore next */
          cov_2f6r1jzz3n().s[126]++;
          return result;
        }
      default:
        /* istanbul ignore next */
        cov_2f6r1jzz3n().b[38][3]++;
        cov_2f6r1jzz3n().s[127]++;
        return 1.0;
    }
  }
  /**
   * Select appropriate performance curve
   */
  static selectPerformanceCurve(curves, flowConditions) {
    /* istanbul ignore next */
    cov_2f6r1jzz3n().f[21]++;
    cov_2f6r1jzz3n().s[128]++;
    // For now, select the first available curve
    // In a more sophisticated implementation, would select based on current conditions
    return curves[0];
  }
  /**
   * Interpolate value from performance curve
   */
  static interpolatePerformanceCurve(curve, parameterValue) {
    /* istanbul ignore next */
    cov_2f6r1jzz3n().f[22]++;
    const points =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().s[129]++, curve.dataPoints.sort((a, b) => {
      /* istanbul ignore next */
      cov_2f6r1jzz3n().f[23]++;
      cov_2f6r1jzz3n().s[130]++;
      return a.x - b.x;
    }));
    // Check if value is outside curve range
    /* istanbul ignore next */
    cov_2f6r1jzz3n().s[131]++;
    if (parameterValue <= points[0].x) {
      /* istanbul ignore next */
      cov_2f6r1jzz3n().b[39][0]++;
      cov_2f6r1jzz3n().s[132]++;
      return points[0].y;
    } else
    /* istanbul ignore next */
    {
      cov_2f6r1jzz3n().b[39][1]++;
    }
    cov_2f6r1jzz3n().s[133]++;
    if (parameterValue >= points[points.length - 1].x) {
      /* istanbul ignore next */
      cov_2f6r1jzz3n().b[40][0]++;
      cov_2f6r1jzz3n().s[134]++;
      if (curve.extrapolationAllowed) {
        /* istanbul ignore next */
        cov_2f6r1jzz3n().b[41][0]++;
        // Simple linear extrapolation
        const lastTwo =
        /* istanbul ignore next */
        (cov_2f6r1jzz3n().s[135]++, points.slice(-2));
        const slope =
        /* istanbul ignore next */
        (cov_2f6r1jzz3n().s[136]++, (lastTwo[1].y - lastTwo[0].y) / (lastTwo[1].x - lastTwo[0].x));
        /* istanbul ignore next */
        cov_2f6r1jzz3n().s[137]++;
        return lastTwo[1].y + slope * (parameterValue - lastTwo[1].x);
      } else {
        /* istanbul ignore next */
        cov_2f6r1jzz3n().b[41][1]++;
        cov_2f6r1jzz3n().s[138]++;
        return points[points.length - 1].y;
      }
    } else
    /* istanbul ignore next */
    {
      cov_2f6r1jzz3n().b[40][1]++;
    }
    // Find surrounding points for interpolation
    cov_2f6r1jzz3n().s[139]++;
    for (let i =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().s[140]++, 0); i < points.length - 1; i++) {
      /* istanbul ignore next */
      cov_2f6r1jzz3n().s[141]++;
      if (
      /* istanbul ignore next */
      (cov_2f6r1jzz3n().b[43][0]++, parameterValue >= points[i].x) &&
      /* istanbul ignore next */
      (cov_2f6r1jzz3n().b[43][1]++, parameterValue <= points[i + 1].x)) {
        /* istanbul ignore next */
        cov_2f6r1jzz3n().b[42][0]++;
        // Linear interpolation
        const ratio =
        /* istanbul ignore next */
        (cov_2f6r1jzz3n().s[142]++, (parameterValue - points[i].x) / (points[i + 1].x - points[i].x));
        /* istanbul ignore next */
        cov_2f6r1jzz3n().s[143]++;
        return points[i].y + ratio * (points[i + 1].y - points[i].y);
      } else
      /* istanbul ignore next */
      {
        cov_2f6r1jzz3n().b[42][1]++;
      }
    }
    /* istanbul ignore next */
    cov_2f6r1jzz3n().s[144]++;
    return points[0].y; // Fallback
  }
  /**
   * Apply correction factors to pressure loss
   */
  static applyCorrectionFactors(baseLoss, config, flowConditions) {
    /* istanbul ignore next */
    cov_2f6r1jzz3n().f[24]++;
    let correctedLoss =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().s[145]++, baseLoss);
    const corrections =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().s[146]++, config.pressureLossProfile.correctionFactors);
    // Temperature correction
    /* istanbul ignore next */
    cov_2f6r1jzz3n().s[147]++;
    if (corrections.temperatureCorrection) {
      /* istanbul ignore next */
      cov_2f6r1jzz3n().b[44][0]++;
      const tempCorrection =
      /* istanbul ignore next */
      (cov_2f6r1jzz3n().s[148]++, this.calculateTemperatureCorrection(flowConditions.temperature));
      /* istanbul ignore next */
      cov_2f6r1jzz3n().s[149]++;
      correctedLoss *= tempCorrection;
    } else
    /* istanbul ignore next */
    {
      cov_2f6r1jzz3n().b[44][1]++;
    }
    // Density correction
    cov_2f6r1jzz3n().s[150]++;
    if (corrections.densityCorrection) {
      /* istanbul ignore next */
      cov_2f6r1jzz3n().b[45][0]++;
      const densityCorrection =
      /* istanbul ignore next */
      (cov_2f6r1jzz3n().s[151]++, flowConditions.airDensity / 0.075); // Standard air density
      /* istanbul ignore next */
      cov_2f6r1jzz3n().s[152]++;
      correctedLoss *= densityCorrection;
    } else
    /* istanbul ignore next */
    {
      cov_2f6r1jzz3n().b[45][1]++;
    }
    // Installation correction (simplified)
    cov_2f6r1jzz3n().s[153]++;
    if (corrections.installationCorrection) {
      /* istanbul ignore next */
      cov_2f6r1jzz3n().b[46][0]++;
      cov_2f6r1jzz3n().s[154]++;
      correctedLoss *= 1.1; // 10% installation factor
    } else
    /* istanbul ignore next */
    {
      cov_2f6r1jzz3n().b[46][1]++;
    }
    cov_2f6r1jzz3n().s[155]++;
    return correctedLoss;
  }
  /**
   * Calculate temperature correction factor
   */
  static calculateTemperatureCorrection(temperature) {
    /* istanbul ignore next */
    cov_2f6r1jzz3n().f[25]++;
    // Standard temperature is 70°F
    const standardTemp =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().s[156]++, 70);
    const tempRatio =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().s[157]++, (temperature + 459.67) / (standardTemp + 459.67)); // Convert to absolute temperature
    /* istanbul ignore next */
    cov_2f6r1jzz3n().s[158]++;
    return Math.sqrt(tempRatio);
  }
  /**
   * Calculate interaction effects between adjacent fittings
   */
  static calculateInteractionEffects(config, systemContext) {
    /* istanbul ignore next */
    cov_2f6r1jzz3n().f[26]++;
    const upstreamFittings =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().s[159]++, systemContext.getUpstreamFittings(config.id, 10)); // 10 diameters
    const downstreamFittings =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().s[160]++, systemContext.getDownstreamFittings(config.id, 10));
    let interactionFactor =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().s[161]++, 1.0);
    const interactions =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().s[162]++, []);
    // Upstream interactions
    /* istanbul ignore next */
    cov_2f6r1jzz3n().s[163]++;
    for (const upstream of upstreamFittings) {
      const interaction =
      /* istanbul ignore next */
      (cov_2f6r1jzz3n().s[164]++, this.calculateUpstreamInteraction(upstream, config));
      /* istanbul ignore next */
      cov_2f6r1jzz3n().s[165]++;
      interactionFactor *= interaction.factor;
      /* istanbul ignore next */
      cov_2f6r1jzz3n().s[166]++;
      interactions.push(interaction);
    }
    // Downstream interactions
    /* istanbul ignore next */
    cov_2f6r1jzz3n().s[167]++;
    for (const downstream of downstreamFittings) {
      const interaction =
      /* istanbul ignore next */
      (cov_2f6r1jzz3n().s[168]++, this.calculateDownstreamInteraction(config, downstream));
      /* istanbul ignore next */
      cov_2f6r1jzz3n().s[169]++;
      interactionFactor *= interaction.factor;
      /* istanbul ignore next */
      cov_2f6r1jzz3n().s[170]++;
      interactions.push(interaction);
    }
    /* istanbul ignore next */
    cov_2f6r1jzz3n().s[171]++;
    return {
      totalInteractionFactor: interactionFactor,
      individualInteractions: interactions,
      significantInteractions: interactions.filter(i => {
        /* istanbul ignore next */
        cov_2f6r1jzz3n().f[27]++;
        cov_2f6r1jzz3n().s[172]++;
        return Math.abs(i.factor - 1.0) > 0.05;
      })
    };
  }
  /**
   * Get default interaction effects when no system context provided
   */
  static getDefaultInteractionEffects() {
    /* istanbul ignore next */
    cov_2f6r1jzz3n().f[28]++;
    cov_2f6r1jzz3n().s[173]++;
    return {
      totalInteractionFactor: 1.0,
      individualInteractions: [],
      significantInteractions: []
    };
  }
  /**
   * Calculate upstream fitting interaction
   */
  static calculateUpstreamInteraction(upstreamFitting, currentFitting) {
    /* istanbul ignore next */
    cov_2f6r1jzz3n().f[29]++;
    // Simplified interaction calculation
    // In practice, would use more sophisticated models
    let factor =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().s[174]++, 1.0);
    let significance =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().s[175]++, 'low');
    // Check for known interaction effects
    /* istanbul ignore next */
    cov_2f6r1jzz3n().s[176]++;
    for (const effect of currentFitting.compatibilityMatrix.interactionEffects) {
      /* istanbul ignore next */
      cov_2f6r1jzz3n().s[177]++;
      if (effect.adjacentFittingType === upstreamFitting.type) {
        /* istanbul ignore next */
        cov_2f6r1jzz3n().b[47][0]++;
        cov_2f6r1jzz3n().s[178]++;
        factor = effect.magnitude;
        /* istanbul ignore next */
        cov_2f6r1jzz3n().s[179]++;
        if (effect.magnitude > 1.2) {
          /* istanbul ignore next */
          cov_2f6r1jzz3n().b[48][0]++;
          cov_2f6r1jzz3n().s[180]++;
          significance = 'high';
        } else {
          /* istanbul ignore next */
          cov_2f6r1jzz3n().b[48][1]++;
          cov_2f6r1jzz3n().s[181]++;
          if (effect.magnitude > 1.1) {
            /* istanbul ignore next */
            cov_2f6r1jzz3n().b[49][0]++;
            cov_2f6r1jzz3n().s[182]++;
            significance = 'medium';
          } else {
            /* istanbul ignore next */
            cov_2f6r1jzz3n().b[49][1]++;
            cov_2f6r1jzz3n().s[183]++;
            significance = 'low';
          }
        }
        /* istanbul ignore next */
        cov_2f6r1jzz3n().s[184]++;
        break;
      } else
      /* istanbul ignore next */
      {
        cov_2f6r1jzz3n().b[47][1]++;
      }
    }
    /* istanbul ignore next */
    cov_2f6r1jzz3n().s[185]++;
    return {
      adjacentFittingId: upstreamFitting.id,
      distance: 5,
      // Simplified distance
      factor: factor,
      type: 'upstream',
      significance: significance
    };
  }
  /**
   * Calculate downstream fitting interaction
   */
  static calculateDownstreamInteraction(currentFitting, downstreamFitting) {
    /* istanbul ignore next */
    cov_2f6r1jzz3n().f[30]++;
    cov_2f6r1jzz3n().s[186]++;
    // Downstream interactions typically have less effect
    return {
      adjacentFittingId: downstreamFitting.id,
      distance: 5,
      factor: 1.0,
      // Minimal downstream effect for most fittings
      type: 'downstream',
      significance: 'low'
    };
  }
  /**
   * Calculate performance metrics for the fitting
   */
  static calculatePerformanceMetrics(config, flowConditions, pressureLoss) {
    /* istanbul ignore next */
    cov_2f6r1jzz3n().f[31]++;
    const velocityPressure =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().s[187]++, this.calculateVelocityPressure(flowConditions.velocity, flowConditions.airDensity));
    // Calculate efficiency (inverse of pressure loss coefficient)
    const efficiency =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().s[188]++, Math.max(0, Math.min(100, 100 * (1 - pressureLoss / (velocityPressure * 5)))));
    // Estimate noise generation based on velocity and fitting type
    const noiseGeneration =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().s[189]++, this.calculateNoiseGeneration(config, flowConditions));
    // Calculate energy loss
    const energyLoss =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().s[190]++, this.calculateEnergyLoss(flowConditions.volumeFlow, pressureLoss));
    // Calculate flow uniformity based on fitting characteristics
    const flowUniformity =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().s[191]++, config.flowCharacteristics.velocityProfile.uniformityIndex * 100);
    // Calculate pressure recovery
    const pressureRecovery =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().s[192]++, config.flowCharacteristics.turbulenceFactors.pressureRecoveryFactor * 100);
    /* istanbul ignore next */
    cov_2f6r1jzz3n().s[193]++;
    return {
      efficiency: efficiency,
      noiseGeneration: noiseGeneration,
      energyLoss: energyLoss,
      flowUniformity: flowUniformity,
      pressureRecovery: pressureRecovery
    };
  }
  /**
   * Calculate noise generation for the fitting
   */
  static calculateNoiseGeneration(config, flowConditions) {
    /* istanbul ignore next */
    cov_2f6r1jzz3n().f[32]++;
    // Base noise calculation using velocity and fitting characteristics
    const velocityNoise =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().s[194]++, 20 * Math.log10(flowConditions.velocity / 1000)); // Reference 1000 FPM
    const turbulenceNoise =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().s[195]++, config.flowCharacteristics.turbulenceFactors.turbulenceIntensity * 0.5);
    let fittingNoise =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().s[196]++, 1);
    /* istanbul ignore next */
    cov_2f6r1jzz3n().s[197]++;
    if (config.complexity === 'complex') {
      /* istanbul ignore next */
      cov_2f6r1jzz3n().b[50][0]++;
      cov_2f6r1jzz3n().s[198]++;
      fittingNoise = 5;
    } else {
      /* istanbul ignore next */
      cov_2f6r1jzz3n().b[50][1]++;
      cov_2f6r1jzz3n().s[199]++;
      if (config.complexity === 'variable') {
        /* istanbul ignore next */
        cov_2f6r1jzz3n().b[51][0]++;
        cov_2f6r1jzz3n().s[200]++;
        fittingNoise = 3;
      } else
      /* istanbul ignore next */
      {
        cov_2f6r1jzz3n().b[51][1]++;
      }
    }
    /* istanbul ignore next */
    cov_2f6r1jzz3n().s[201]++;
    return Math.max(0, velocityNoise + turbulenceNoise + fittingNoise);
  }
  /**
   * Calculate energy loss in BTU/hr
   */
  static calculateEnergyLoss(volumeFlow, pressureLoss) {
    /* istanbul ignore next */
    cov_2f6r1jzz3n().f[33]++;
    // Energy loss = (CFM × Pressure Loss in in wg × 4.5) / Fan Efficiency
    // Assuming 70% fan efficiency
    const fanEfficiency =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().s[202]++, 0.70);
    /* istanbul ignore next */
    cov_2f6r1jzz3n().s[203]++;
    return volumeFlow * pressureLoss * 4.5 / fanEfficiency;
  }
  /**
   * Validate advanced configuration and flow conditions
   */
  static validateAdvancedConfiguration(config, flowConditions) {
    /* istanbul ignore next */
    cov_2f6r1jzz3n().f[34]++;
    // Validate flow is within fitting's operating range
    const operatingRange =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().s[204]++, config.flowCharacteristics.operatingRange);
    /* istanbul ignore next */
    cov_2f6r1jzz3n().s[205]++;
    if (
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().b[53][0]++, flowConditions.volumeFlow < operatingRange.minimum) ||
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().b[53][1]++, flowConditions.volumeFlow > operatingRange.maximum)) {
      /* istanbul ignore next */
      cov_2f6r1jzz3n().b[52][0]++;
      cov_2f6r1jzz3n().s[206]++;
      throw new Error(`Flow ${flowConditions.volumeFlow} CFM outside fitting operating range [${operatingRange.minimum}, ${operatingRange.maximum}] CFM`);
    } else
    /* istanbul ignore next */
    {
      cov_2f6r1jzz3n().b[52][1]++;
    }
    // Validate velocity is reasonable
    cov_2f6r1jzz3n().s[207]++;
    if (
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().b[55][0]++, flowConditions.velocity < 100) ||
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().b[55][1]++, flowConditions.velocity > 6000)) {
      /* istanbul ignore next */
      cov_2f6r1jzz3n().b[54][0]++;
      cov_2f6r1jzz3n().s[208]++;
      throw new Error(`Velocity ${flowConditions.velocity} FPM outside reasonable range [100, 6000] FPM`);
    } else
    /* istanbul ignore next */
    {
      cov_2f6r1jzz3n().b[54][1]++;
    }
    // Validate Reynolds number
    cov_2f6r1jzz3n().s[209]++;
    if (flowConditions.reynoldsNumber < 1000) {
      /* istanbul ignore next */
      cov_2f6r1jzz3n().b[56][0]++;
      cov_2f6r1jzz3n().s[210]++;
      throw new Error(`Reynolds number ${flowConditions.reynoldsNumber} too low for turbulent flow calculations`);
    } else
    /* istanbul ignore next */
    {
      cov_2f6r1jzz3n().b[56][1]++;
    }
  }
  /**
   * Validate calculation results
   */
  static validateResults(config, flowConditions, pressureLoss) {
    /* istanbul ignore next */
    cov_2f6r1jzz3n().f[35]++;
    const errors =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().s[211]++, []);
    const warnings =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().s[212]++, []);
    // Check for unreasonable pressure loss
    const velocityPressure =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().s[213]++, this.calculateVelocityPressure(flowConditions.velocity, flowConditions.airDensity));
    const kFactor =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().s[214]++, pressureLoss / velocityPressure);
    /* istanbul ignore next */
    cov_2f6r1jzz3n().s[215]++;
    if (kFactor > 10) {
      /* istanbul ignore next */
      cov_2f6r1jzz3n().b[57][0]++;
      cov_2f6r1jzz3n().s[216]++;
      errors.push({
        code: 'E001',
        message: 'Pressure loss coefficient unreasonably high',
        parameter: 'k_factor',
        value: kFactor
      });
    } else
    /* istanbul ignore next */
    {
      cov_2f6r1jzz3n().b[57][1]++;
    }
    cov_2f6r1jzz3n().s[217]++;
    if (kFactor < 0) {
      /* istanbul ignore next */
      cov_2f6r1jzz3n().b[58][0]++;
      cov_2f6r1jzz3n().s[218]++;
      errors.push({
        code: 'E002',
        message: 'Negative pressure loss calculated',
        parameter: 'pressure_loss',
        value: pressureLoss
      });
    } else
    /* istanbul ignore next */
    {
      cov_2f6r1jzz3n().b[58][1]++;
    }
    // Apply validation rules from configuration
    cov_2f6r1jzz3n().s[219]++;
    for (const rule of config.validationRules) {
      const parameterValue =
      /* istanbul ignore next */
      (cov_2f6r1jzz3n().s[220]++, this.getParameterValue(rule.condition.parameter, config, flowConditions));
      const isViolated =
      /* istanbul ignore next */
      (cov_2f6r1jzz3n().s[221]++, this.checkValidationCondition(rule.condition, parameterValue));
      /* istanbul ignore next */
      cov_2f6r1jzz3n().s[222]++;
      if (isViolated) {
        /* istanbul ignore next */
        cov_2f6r1jzz3n().b[59][0]++;
        cov_2f6r1jzz3n().s[223]++;
        if (rule.severity === 'error') {
          /* istanbul ignore next */
          cov_2f6r1jzz3n().b[60][0]++;
          cov_2f6r1jzz3n().s[224]++;
          errors.push({
            code: rule.ruleId,
            message: rule.message,
            parameter: rule.condition.parameter,
            value: parameterValue
          });
        } else {
          /* istanbul ignore next */
          cov_2f6r1jzz3n().b[60][1]++;
          cov_2f6r1jzz3n().s[225]++;
          if (rule.severity === 'warning') {
            /* istanbul ignore next */
            cov_2f6r1jzz3n().b[61][0]++;
            cov_2f6r1jzz3n().s[226]++;
            warnings.push({
              code: rule.ruleId,
              message: rule.message,
              severity: 'medium',
              recommendation: 'Review operating conditions'
            });
          } else
          /* istanbul ignore next */
          {
            cov_2f6r1jzz3n().b[61][1]++;
          }
        }
      } else
      /* istanbul ignore next */
      {
        cov_2f6r1jzz3n().b[59][1]++;
      }
    }
    // Check uncertainty bounds
    const uncertaintyBounds =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().s[227]++, config.pressureLossProfile.uncertaintyBounds);
    const uncertaintyRange =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().s[228]++, pressureLoss * (uncertaintyBounds.upperBound - uncertaintyBounds.lowerBound) / 100);
    /* istanbul ignore next */
    cov_2f6r1jzz3n().s[229]++;
    if (uncertaintyRange > pressureLoss * 0.3) {
      /* istanbul ignore next */
      cov_2f6r1jzz3n().b[62][0]++;
      cov_2f6r1jzz3n().s[230]++;
      warnings.push({
        code: 'W001',
        message: 'High uncertainty in pressure loss calculation',
        severity: 'medium',
        recommendation: 'Consider using more accurate calculation method'
      });
    } else
    /* istanbul ignore next */
    {
      cov_2f6r1jzz3n().b[62][1]++;
    }
    const complianceStatus =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().s[231]++, {
      smacnaCompliant: errors.length === 0,
      ashraeCompliant: errors.length === 0,
      localCodeCompliant: true,
      customStandardsCompliant: true
    });
    /* istanbul ignore next */
    cov_2f6r1jzz3n().s[232]++;
    return {
      isValid: errors.length === 0,
      errors: errors,
      warnings: warnings,
      complianceStatus: complianceStatus
    };
  }
  /**
   * Check validation condition
   */
  static checkValidationCondition(condition, value) {
    /* istanbul ignore next */
    cov_2f6r1jzz3n().f[36]++;
    cov_2f6r1jzz3n().s[233]++;
    switch (condition.operator) {
      case '>':
        /* istanbul ignore next */
        cov_2f6r1jzz3n().b[63][0]++;
        cov_2f6r1jzz3n().s[234]++;
        return Number(value) > Number(condition.value);
      case '<':
        /* istanbul ignore next */
        cov_2f6r1jzz3n().b[63][1]++;
        cov_2f6r1jzz3n().s[235]++;
        return Number(value) < Number(condition.value);
      case '>=':
        /* istanbul ignore next */
        cov_2f6r1jzz3n().b[63][2]++;
        cov_2f6r1jzz3n().s[236]++;
        return Number(value) >= Number(condition.value);
      case '<=':
        /* istanbul ignore next */
        cov_2f6r1jzz3n().b[63][3]++;
        cov_2f6r1jzz3n().s[237]++;
        return Number(value) <= Number(condition.value);
      case '=':
        /* istanbul ignore next */
        cov_2f6r1jzz3n().b[63][4]++;
        cov_2f6r1jzz3n().s[238]++;
        return value === condition.value;
      case '!=':
        /* istanbul ignore next */
        cov_2f6r1jzz3n().b[63][5]++;
        cov_2f6r1jzz3n().s[239]++;
        return value !== condition.value;
      case 'in':
        /* istanbul ignore next */
        cov_2f6r1jzz3n().b[63][6]++;
        cov_2f6r1jzz3n().s[240]++;
        return /* istanbul ignore next */(cov_2f6r1jzz3n().b[64][0]++, Array.isArray(condition.value)) &&
        /* istanbul ignore next */
        (cov_2f6r1jzz3n().b[64][1]++, condition.value.includes(value));
      case 'not_in':
        /* istanbul ignore next */
        cov_2f6r1jzz3n().b[63][7]++;
        cov_2f6r1jzz3n().s[241]++;
        return /* istanbul ignore next */(cov_2f6r1jzz3n().b[65][0]++, Array.isArray(condition.value)) &&
        /* istanbul ignore next */
        (cov_2f6r1jzz3n().b[65][1]++, !condition.value.includes(value));
      default:
        /* istanbul ignore next */
        cov_2f6r1jzz3n().b[63][8]++;
        cov_2f6r1jzz3n().s[242]++;
        return false;
    }
  }
  /**
   * Generate recommendations based on calculation results
   */
  static generateRecommendations(config, flowConditions, performanceMetrics, validationResults) {
    /* istanbul ignore next */
    cov_2f6r1jzz3n().f[37]++;
    const recommendations =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().s[243]++, []);
    // Performance-based recommendations
    /* istanbul ignore next */
    cov_2f6r1jzz3n().s[244]++;
    if (performanceMetrics.efficiency < 70) {
      /* istanbul ignore next */
      cov_2f6r1jzz3n().b[66][0]++;
      cov_2f6r1jzz3n().s[245]++;
      recommendations.push({
        type: 'optimization',
        priority: 'medium',
        description: 'Consider using a more efficient fitting design to reduce pressure loss',
        expectedBenefit: 'Reduced energy consumption and improved system performance',
        implementationCost: 'medium'
      });
    } else
    /* istanbul ignore next */
    {
      cov_2f6r1jzz3n().b[66][1]++;
    }
    cov_2f6r1jzz3n().s[246]++;
    if (performanceMetrics.noiseGeneration > 50) {
      /* istanbul ignore next */
      cov_2f6r1jzz3n().b[67][0]++;
      cov_2f6r1jzz3n().s[247]++;
      recommendations.push({
        type: 'optimization',
        priority: 'medium',
        description: 'Consider adding sound attenuation to reduce noise levels',
        expectedBenefit: 'Improved acoustic comfort',
        implementationCost: 'medium'
      });
    } else
    /* istanbul ignore next */
    {
      cov_2f6r1jzz3n().b[67][1]++;
    }
    // Flow-based recommendations
    const operatingRange =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().s[248]++, config.flowCharacteristics.operatingRange);
    const flowRatio =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().s[249]++, flowConditions.volumeFlow / operatingRange.optimal);
    /* istanbul ignore next */
    cov_2f6r1jzz3n().s[250]++;
    if (flowRatio < 0.5) {
      /* istanbul ignore next */
      cov_2f6r1jzz3n().b[68][0]++;
      cov_2f6r1jzz3n().s[251]++;
      recommendations.push({
        type: 'adjustment',
        priority: 'low',
        description: 'Flow is significantly below optimal range - consider resizing fitting',
        expectedBenefit: 'Better performance and efficiency',
        implementationCost: 'high'
      });
    } else
    /* istanbul ignore next */
    {
      cov_2f6r1jzz3n().b[68][1]++;
    }
    cov_2f6r1jzz3n().s[252]++;
    if (flowRatio > 1.5) {
      /* istanbul ignore next */
      cov_2f6r1jzz3n().b[69][0]++;
      cov_2f6r1jzz3n().s[253]++;
      recommendations.push({
        type: 'adjustment',
        priority: 'high',
        description: 'Flow exceeds optimal range - fitting may be undersized',
        expectedBenefit: 'Reduced pressure loss and noise',
        implementationCost: 'high'
      });
    } else
    /* istanbul ignore next */
    {
      cov_2f6r1jzz3n().b[69][1]++;
    }
    // Maintenance recommendations
    cov_2f6r1jzz3n().s[254]++;
    if (
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().b[71][0]++, config.category === 'control') ||
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().b[71][1]++, config.category === 'terminal')) {
      /* istanbul ignore next */
      cov_2f6r1jzz3n().b[70][0]++;
      cov_2f6r1jzz3n().s[255]++;
      recommendations.push({
        type: 'maintenance',
        priority: 'low',
        description: 'Regular inspection and calibration recommended for control devices',
        expectedBenefit: 'Maintained performance and reliability',
        implementationCost: 'low'
      });
    } else
    /* istanbul ignore next */
    {
      cov_2f6r1jzz3n().b[70][1]++;
    }
    cov_2f6r1jzz3n().s[256]++;
    return recommendations;
  }
  /**
   * Get fitting configuration by ID from database
   */
  static getFittingConfiguration(fittingId) {
    /* istanbul ignore next */
    cov_2f6r1jzz3n().f[38]++;
    const data =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().s[257]++, this.loadAdvancedFittingsData());
    // Search through all categories for the fitting
    /* istanbul ignore next */
    cov_2f6r1jzz3n().s[258]++;
    for (const categoryName of Object.keys(data.categories)) {
      const category =
      /* istanbul ignore next */
      (cov_2f6r1jzz3n().s[259]++, data.categories[categoryName]);
      /* istanbul ignore next */
      cov_2f6r1jzz3n().s[260]++;
      for (const typeName of Object.keys(category)) {
        const type =
        /* istanbul ignore next */
        (cov_2f6r1jzz3n().s[261]++, category[typeName]);
        /* istanbul ignore next */
        cov_2f6r1jzz3n().s[262]++;
        for (const configName of Object.keys(type)) {
          const config =
          /* istanbul ignore next */
          (cov_2f6r1jzz3n().s[263]++, type[configName]);
          /* istanbul ignore next */
          cov_2f6r1jzz3n().s[264]++;
          if (config.id === fittingId) {
            /* istanbul ignore next */
            cov_2f6r1jzz3n().b[72][0]++;
            cov_2f6r1jzz3n().s[265]++;
            return config;
          } else
          /* istanbul ignore next */
          {
            cov_2f6r1jzz3n().b[72][1]++;
          }
        }
      }
    }
    /* istanbul ignore next */
    cov_2f6r1jzz3n().s[266]++;
    return null;
  }
  /**
   * List all available fitting configurations
   */
  static listAvailableFittings() {
    /* istanbul ignore next */
    cov_2f6r1jzz3n().f[39]++;
    const data =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().s[267]++, this.loadAdvancedFittingsData());
    const fittings =
    /* istanbul ignore next */
    (cov_2f6r1jzz3n().s[268]++, []);
    /* istanbul ignore next */
    cov_2f6r1jzz3n().s[269]++;
    for (const categoryName of Object.keys(data.categories)) {
      const category =
      /* istanbul ignore next */
      (cov_2f6r1jzz3n().s[270]++, data.categories[categoryName]);
      /* istanbul ignore next */
      cov_2f6r1jzz3n().s[271]++;
      for (const typeName of Object.keys(category)) {
        const type =
        /* istanbul ignore next */
        (cov_2f6r1jzz3n().s[272]++, category[typeName]);
        /* istanbul ignore next */
        cov_2f6r1jzz3n().s[273]++;
        for (const configName of Object.keys(type)) {
          const config =
          /* istanbul ignore next */
          (cov_2f6r1jzz3n().s[274]++, type[configName]);
          /* istanbul ignore next */
          cov_2f6r1jzz3n().s[275]++;
          fittings.push({
            id: config.id,
            description: config.description,
            category: config.category
          });
        }
      }
    }
    /* istanbul ignore next */
    cov_2f6r1jzz3n().s[276]++;
    return fittings.sort((a, b) => {
      /* istanbul ignore next */
      cov_2f6r1jzz3n().f[40]++;
      cov_2f6r1jzz3n().s[277]++;
      return /* istanbul ignore next */(cov_2f6r1jzz3n().b[73][0]++, a.category.localeCompare(b.category)) ||
      /* istanbul ignore next */
      (cov_2f6r1jzz3n().b[73][1]++, a.description.localeCompare(b.description));
    });
  }
}
/* istanbul ignore next */
cov_2f6r1jzz3n().s[278]++;
exports.AdvancedFittingCalculator = AdvancedFittingCalculator;
/* istanbul ignore next */
cov_2f6r1jzz3n().s[279]++;
AdvancedFittingCalculator.advancedFittingsData = null;
/* istanbul ignore next */
cov_2f6r1jzz3n().s[280]++;
AdvancedFittingCalculator.DATA_FILE_PATH = path.join(__dirname, '../../data/advanced_fittings.json');
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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