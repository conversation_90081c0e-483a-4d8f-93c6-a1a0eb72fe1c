{"version": 3, "names": ["cov_2f6r1jzz3n", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "name", "decl", "loc", "branchMap", "type", "locations", "undefined", "s", "f", "b", "inputSourceMap", "file", "mappings", "names", "sources", "sourcesContent", "version", "_coverageSchema", "coverage", "actualCoverage", "fs", "__importStar", "require", "FittingLossCalculator_1", "AdvancedFittingTypes_1", "AdvancedFittingCalculator", "FittingLossCalculator", "loadAdvancedFittingsData", "advancedFittingsData", "dataContent", "readFileSync", "DATA_FILE_PATH", "JSON", "parse", "error", "console", "Error", "calculateAdvancedFittingLoss", "config", "flowConditions", "systemContext", "validateAdvancedConfiguration", "method", "selectCalculationMethod", "baseLoss", "calculateBasePressureLoss", "<PERSON><PERSON><PERSON>", "applyCorrectionFactors", "interactionEffects", "calculateInteractionEffects", "getDefaultInteractionEffects", "finalPressureLoss", "totalInteractionFactor", "performanceMetrics", "calculatePerformanceMetrics", "validationResults", "validateResults", "recommendations", "generateRecommendations", "pressureLoss", "velocityPressure", "calculateVelocityPressure", "velocity", "airDensity", "kFactor", "warnings", "map", "w", "message", "calculationMethod", "reynoldsNumber", "pressureLossProfile", "CalculationMethod", "CFD_DERIVED", "complexity", "kFactorData", "parameterDependencies", "length", "MULTI_PARAMETER", "performanceCurves", "PERFORMANCE_CURVE", "SINGLE_K_FACTOR", "calculateSingleKFactorLoss", "calculateMultiParameterLoss", "calculatePerformanceCurveLoss", "calculateCFDDerivedLoss", "baseKFactor", "dependency", "parameterValue", "getParameterValue", "parameter", "correction", "calculateParameterCorrection", "reynoldsCorrection", "enabled", "calculateReynoldsCorrection", "geometryCorrections", "correctionFactor", "curve", "selectPerformanceCurve", "interpolatePerformanceCurve", "validRange", "warn", "relationship", "coefficients", "result", "i", "Math", "pow", "exp", "volumeFlow", "temperature", "pressure", "physicalProperties", "dimensions", "inletDiameter", "inletArea", "PI", "outletArea", "outletDiameter", "width", "height", "log10", "curves", "points", "dataPoints", "sort", "a", "x", "y", "extrapolationAllowed", "lastTwo", "slice", "slope", "ratio", "corrections", "correctionFactors", "temperatureCorrection", "tempCorrection", "calculateTemperatureCorrection", "densityCorrection", "installationCorrection", "standardTemp", "tempRatio", "sqrt", "upstreamFittings", "getUpstreamFittings", "id", "downstreamFittings", "getDownstreamFittings", "interactionFactor", "interactions", "upstream", "interaction", "calculateUpstreamInteraction", "factor", "push", "downstream", "calculateDownstreamInteraction", "individualInteractions", "significantInteractions", "filter", "abs", "upstreamFitting", "currentFitting", "significance", "effect", "compatibilityMatrix", "adjacentFittingType", "magnitude", "adjacentFittingId", "distance", "downstreamFitting", "efficiency", "max", "min", "noiseGeneration", "calculateNoiseGeneration", "energyLoss", "calculateEnergyLoss", "flowUniformity", "flowCharacteristics", "velocityProfile", "uniformityIndex", "pressureRecovery", "turbulenceFactors", "pressureRecoveryFactor", "velocityNoise", "turbulenceNoise", "turbulenceIntensity", "fitting<PERSON><PERSON>", "fanEfficiency", "operatingRange", "minimum", "maximum", "errors", "code", "value", "rule", "validationRules", "condition", "isViolated", "checkValidationCondition", "severity", "ruleId", "recommendation", "uncertaintyBounds", "uncertaintyRange", "upperBound", "lowerBound", "complianceStatus", "smacnaCompliant", "ashraeCompliant", "localCodeCompliant", "customStandardsCompliant", "<PERSON><PERSON><PERSON><PERSON>", "operator", "Number", "Array", "isArray", "includes", "priority", "description", "expectedBenefit", "implementationCost", "flowRatio", "optimal", "category", "getFittingConfiguration", "fittingId", "data", "categoryName", "Object", "keys", "categories", "typeName", "config<PERSON><PERSON>", "listAvailableFittings", "fittings", "localeCompare", "exports", "join", "__dirname"], "sources": ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\AdvancedFittingCalculator.ts"], "sourcesContent": ["/**\r\n * Advanced Fitting Calculator for Phase 3 Duct Physics Implementation\r\n * \r\n * This service extends the basic FittingLossCalculator with advanced capabilities including:\r\n * - Multi-parameter K-factor calculations\r\n * - Performance curve interpolation\r\n * - Interaction effects between adjacent fittings\r\n * - Method selection algorithms\r\n * - Complex fitting configurations\r\n * \r\n * @version 3.0.0\r\n * <AUTHOR> Suite Development Team\r\n */\r\n\r\nimport * as fs from 'fs';\r\nimport * as path from 'path';\r\nimport { FittingLossCalculator } from './FittingLossCalculator';\r\nimport {\r\n  AdvancedFittingConfiguration,\r\n  FlowConditions,\r\n  SystemContext,\r\n  AdvancedFittingLossResult,\r\n  CalculationMethod,\r\n  InteractionEffects,\r\n  FittingInteraction,\r\n  PerformanceMetrics,\r\n  ValidationResults,\r\n  ValidationError,\r\n  ValidationWarning,\r\n  ComplianceStatus,\r\n  Recommendation,\r\n  ParameterDependency,\r\n  PerformanceCurve\r\n} from './types/AdvancedFittingTypes';\r\n\r\nexport class AdvancedFittingCalculator extends FittingLossCalculator {\r\n  private static advancedFittingsData: any = null;\r\n  private static readonly DATA_FILE_PATH = path.join(__dirname, '../../data/advanced_fittings.json');\r\n\r\n  /**\r\n   * Load advanced fittings database\r\n   */\r\n  private static loadAdvancedFittingsData(): any {\r\n    if (!this.advancedFittingsData) {\r\n      try {\r\n        const dataContent = fs.readFileSync(this.DATA_FILE_PATH, 'utf8');\r\n        this.advancedFittingsData = JSON.parse(dataContent);\r\n      } catch (error) {\r\n        console.error('Error loading advanced fittings data:', error);\r\n        throw new Error('Failed to load advanced fittings database');\r\n      }\r\n    }\r\n    return this.advancedFittingsData;\r\n  }\r\n\r\n  /**\r\n   * Calculate pressure loss for advanced fitting configurations\r\n   */\r\n  public static calculateAdvancedFittingLoss(\r\n    config: AdvancedFittingConfiguration,\r\n    flowConditions: FlowConditions,\r\n    systemContext?: SystemContext\r\n  ): AdvancedFittingLossResult {\r\n    \r\n    // Validate configuration and conditions\r\n    this.validateAdvancedConfiguration(config, flowConditions);\r\n    \r\n    // Select appropriate calculation method\r\n    const method = this.selectCalculationMethod(config, flowConditions);\r\n    \r\n    // Calculate base pressure loss\r\n    const baseLoss = this.calculateBasePressureLoss(config, flowConditions, method);\r\n    \r\n    // Apply correction factors\r\n    const correctedLoss = this.applyCorrectionFactors(baseLoss, config, flowConditions);\r\n    \r\n    // Calculate interaction effects if system context provided\r\n    const interactionEffects = systemContext \r\n      ? this.calculateInteractionEffects(config, systemContext)\r\n      : this.getDefaultInteractionEffects();\r\n    \r\n    // Apply interaction effects\r\n    const finalPressureLoss = correctedLoss * interactionEffects.totalInteractionFactor;\r\n    \r\n    // Calculate performance metrics\r\n    const performanceMetrics = this.calculatePerformanceMetrics(config, flowConditions, finalPressureLoss);\r\n    \r\n    // Validate results\r\n    const validationResults = this.validateResults(config, flowConditions, finalPressureLoss);\r\n    \r\n    // Generate recommendations\r\n    const recommendations = this.generateRecommendations(config, flowConditions, performanceMetrics, validationResults);\r\n    \r\n    // Generate comprehensive result\r\n    return {\r\n      pressureLoss: finalPressureLoss,\r\n      velocityPressure: this.calculateVelocityPressure(flowConditions.velocity, flowConditions.airDensity),\r\n      kFactor: finalPressureLoss / this.calculateVelocityPressure(flowConditions.velocity, flowConditions.airDensity),\r\n      warnings: validationResults.warnings.map(w => w.message),\r\n      calculationMethod: method,\r\n      interactionEffects: interactionEffects,\r\n      performanceMetrics: performanceMetrics,\r\n      validationResults: validationResults,\r\n      recommendations: recommendations\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Select optimal calculation method based on fitting and flow conditions\r\n   */\r\n  private static selectCalculationMethod(\r\n    config: AdvancedFittingConfiguration,\r\n    flowConditions: FlowConditions\r\n  ): CalculationMethod {\r\n    \r\n    // High Reynolds number flows - use CFD-derived data if available\r\n    if (flowConditions.reynoldsNumber > 100000 && \r\n        config.pressureLossProfile.calculationMethod === CalculationMethod.CFD_DERIVED) {\r\n      return CalculationMethod.CFD_DERIVED;\r\n    }\r\n    \r\n    // Complex geometry fittings - use multi-parameter approach\r\n    if (config.complexity === 'complex' && \r\n        config.pressureLossProfile.kFactorData.parameterDependencies.length > 0) {\r\n      return CalculationMethod.MULTI_PARAMETER;\r\n    }\r\n    \r\n    // Variable performance fittings - use performance curves\r\n    if (config.complexity === 'variable' && \r\n        config.pressureLossProfile.performanceCurves && \r\n        config.pressureLossProfile.performanceCurves.length > 0) {\r\n      return CalculationMethod.PERFORMANCE_CURVE;\r\n    }\r\n    \r\n    // Default to single K-factor for simple fittings\r\n    return CalculationMethod.SINGLE_K_FACTOR;\r\n  }\r\n\r\n  /**\r\n   * Calculate base pressure loss using selected method\r\n   */\r\n  private static calculateBasePressureLoss(\r\n    config: AdvancedFittingConfiguration,\r\n    flowConditions: FlowConditions,\r\n    method: CalculationMethod\r\n  ): number {\r\n    \r\n    const velocityPressure = this.calculateVelocityPressure(flowConditions.velocity, flowConditions.airDensity);\r\n    \r\n    switch (method) {\r\n      case CalculationMethod.SINGLE_K_FACTOR:\r\n        return this.calculateSingleKFactorLoss(config, velocityPressure);\r\n        \r\n      case CalculationMethod.MULTI_PARAMETER:\r\n        return this.calculateMultiParameterLoss(config, flowConditions, velocityPressure);\r\n        \r\n      case CalculationMethod.PERFORMANCE_CURVE:\r\n        return this.calculatePerformanceCurveLoss(config, flowConditions, velocityPressure);\r\n        \r\n      case CalculationMethod.CFD_DERIVED:\r\n        return this.calculateCFDDerivedLoss(config, flowConditions, velocityPressure);\r\n        \r\n      default:\r\n        throw new Error(`Unsupported calculation method: ${method}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Calculate single K-factor pressure loss\r\n   */\r\n  private static calculateSingleKFactorLoss(\r\n    config: AdvancedFittingConfiguration,\r\n    velocityPressure: number\r\n  ): number {\r\n    return config.pressureLossProfile.kFactorData.baseKFactor * velocityPressure;\r\n  }\r\n\r\n  /**\r\n   * Calculate multi-parameter pressure loss\r\n   */\r\n  private static calculateMultiParameterLoss(\r\n    config: AdvancedFittingConfiguration,\r\n    flowConditions: FlowConditions,\r\n    velocityPressure: number\r\n  ): number {\r\n    \r\n    let kFactor = config.pressureLossProfile.kFactorData.baseKFactor;\r\n    \r\n    // Apply parameter dependencies\r\n    for (const dependency of config.pressureLossProfile.kFactorData.parameterDependencies) {\r\n      const parameterValue = this.getParameterValue(dependency.parameter, config, flowConditions);\r\n      const correction = this.calculateParameterCorrection(dependency, parameterValue);\r\n      kFactor *= correction;\r\n    }\r\n    \r\n    // Apply Reynolds number correction if enabled\r\n    if (config.pressureLossProfile.kFactorData.reynoldsCorrection?.enabled) {\r\n      const reynoldsCorrection = this.calculateReynoldsCorrection(\r\n        config.pressureLossProfile.kFactorData.reynoldsCorrection,\r\n        flowConditions.reynoldsNumber\r\n      );\r\n      kFactor *= reynoldsCorrection;\r\n    }\r\n    \r\n    // Apply geometry corrections\r\n    for (const correction of config.pressureLossProfile.kFactorData.geometryCorrections) {\r\n      kFactor *= correction.correctionFactor;\r\n    }\r\n    \r\n    return kFactor * velocityPressure;\r\n  }\r\n\r\n  /**\r\n   * Calculate performance curve-based pressure loss\r\n   */\r\n  private static calculatePerformanceCurveLoss(\r\n    config: AdvancedFittingConfiguration,\r\n    flowConditions: FlowConditions,\r\n    velocityPressure: number\r\n  ): number {\r\n    \r\n    if (!config.pressureLossProfile.performanceCurves || config.pressureLossProfile.performanceCurves.length === 0) {\r\n      throw new Error('No performance curves available for fitting');\r\n    }\r\n    \r\n    // Find the most appropriate performance curve\r\n    const curve = this.selectPerformanceCurve(config.pressureLossProfile.performanceCurves, flowConditions);\r\n    \r\n    // Get parameter value for interpolation\r\n    const parameterValue = this.getParameterValue(curve.parameter, config, flowConditions);\r\n    \r\n    // Interpolate pressure loss from curve\r\n    const pressureLoss = this.interpolatePerformanceCurve(curve, parameterValue);\r\n    \r\n    return pressureLoss;\r\n  }\r\n\r\n  /**\r\n   * Calculate CFD-derived pressure loss\r\n   */\r\n  private static calculateCFDDerivedLoss(\r\n    config: AdvancedFittingConfiguration,\r\n    flowConditions: FlowConditions,\r\n    velocityPressure: number\r\n  ): number {\r\n    \r\n    // For CFD-derived data, use multi-parameter approach with higher accuracy\r\n    return this.calculateMultiParameterLoss(config, flowConditions, velocityPressure);\r\n  }\r\n\r\n  /**\r\n   * Calculate parameter correction factor\r\n   */\r\n  private static calculateParameterCorrection(\r\n    dependency: ParameterDependency,\r\n    parameterValue: number\r\n  ): number {\r\n    \r\n    // Validate parameter is within valid range\r\n    if (parameterValue < dependency.validRange[0] || parameterValue > dependency.validRange[1]) {\r\n      console.warn(`Parameter ${dependency.parameter} value ${parameterValue} outside valid range [${dependency.validRange[0]}, ${dependency.validRange[1]}]`);\r\n    }\r\n    \r\n    switch (dependency.relationship) {\r\n      case 'linear':\r\n        return dependency.coefficients[0] + dependency.coefficients[1] * parameterValue;\r\n        \r\n      case 'polynomial': {\r\n        let result = 0;\r\n        for (let i = 0; i < dependency.coefficients.length; i++) {\r\n          result += dependency.coefficients[i] * Math.pow(parameterValue, i);\r\n        }\r\n        return result;\r\n      }\r\n        \r\n      case 'exponential':\r\n        return dependency.coefficients[0] * Math.exp(dependency.coefficients[1] * parameterValue);\r\n        \r\n      case 'lookup':\r\n        // For lookup tables, would need additional data structure\r\n        // For now, return base correction\r\n        return 1.0;\r\n        \r\n      default:\r\n        throw new Error(`Unsupported relationship type: ${dependency.relationship}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get parameter value from configuration or flow conditions\r\n   */\r\n  private static getParameterValue(\r\n    parameter: string,\r\n    config: AdvancedFittingConfiguration,\r\n    flowConditions: FlowConditions\r\n  ): number {\r\n    \r\n    switch (parameter) {\r\n      case 'velocity':\r\n        return flowConditions.velocity;\r\n      case 'reynolds_number':\r\n        return flowConditions.reynoldsNumber;\r\n      case 'volume_flow':\r\n        return flowConditions.volumeFlow;\r\n      case 'temperature':\r\n        return flowConditions.temperature;\r\n      case 'pressure':\r\n        return flowConditions.pressure;\r\n      case 'length_to_diameter_ratio':\r\n        return config.physicalProperties.dimensions.length / (config.physicalProperties.dimensions.inletDiameter || 12);\r\n      case 'area_ratio': {\r\n        const inletArea = Math.PI * Math.pow((config.physicalProperties.dimensions.inletDiameter || 12) / 2, 2);\r\n        const outletArea = Math.PI * Math.pow((config.physicalProperties.dimensions.outletDiameter || 12) / 2, 2);\r\n        return outletArea / inletArea;\r\n      }\r\n      case 'aspect_ratio':\r\n        return config.physicalProperties.dimensions.width / config.physicalProperties.dimensions.height;\r\n      default:\r\n        console.warn(`Unknown parameter: ${parameter}, using default value 1.0`);\r\n        return 1.0;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Calculate Reynolds number correction\r\n   */\r\n  private static calculateReynoldsCorrection(\r\n    correction: any,\r\n    reynoldsNumber: number\r\n  ): number {\r\n    \r\n    if (reynoldsNumber < correction.validRange[0] || reynoldsNumber > correction.validRange[1]) {\r\n      return 1.0; // No correction outside valid range\r\n    }\r\n    \r\n    switch (correction.method) {\r\n      case 'colebrook':\r\n        // Simplified Colebrook-White correction\r\n        return 1.0 + correction.coefficients[0] * Math.log10(reynoldsNumber) + correction.coefficients[1];\r\n        \r\n      case 'blasius':\r\n        // Blasius equation correction\r\n        return Math.pow(reynoldsNumber, correction.coefficients[0]) * correction.coefficients[1];\r\n        \r\n      case 'custom': {\r\n        // Custom polynomial correction\r\n        let result = 0;\r\n        for (let i = 0; i < correction.coefficients.length; i++) {\r\n          result += correction.coefficients[i] * Math.pow(reynoldsNumber, i);\r\n        }\r\n        return result;\r\n      }\r\n        \r\n      default:\r\n        return 1.0;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Select appropriate performance curve\r\n   */\r\n  private static selectPerformanceCurve(\r\n    curves: PerformanceCurve[],\r\n    flowConditions: FlowConditions\r\n  ): PerformanceCurve {\r\n    \r\n    // For now, select the first available curve\r\n    // In a more sophisticated implementation, would select based on current conditions\r\n    return curves[0];\r\n  }\r\n\r\n  /**\r\n   * Interpolate value from performance curve\r\n   */\r\n  private static interpolatePerformanceCurve(\r\n    curve: PerformanceCurve,\r\n    parameterValue: number\r\n  ): number {\r\n    \r\n    const points = curve.dataPoints.sort((a, b) => a.x - b.x);\r\n    \r\n    // Check if value is outside curve range\r\n    if (parameterValue <= points[0].x) {\r\n      return points[0].y;\r\n    }\r\n    if (parameterValue >= points[points.length - 1].x) {\r\n      if (curve.extrapolationAllowed) {\r\n        // Simple linear extrapolation\r\n        const lastTwo = points.slice(-2);\r\n        const slope = (lastTwo[1].y - lastTwo[0].y) / (lastTwo[1].x - lastTwo[0].x);\r\n        return lastTwo[1].y + slope * (parameterValue - lastTwo[1].x);\r\n      } else {\r\n        return points[points.length - 1].y;\r\n      }\r\n    }\r\n    \r\n    // Find surrounding points for interpolation\r\n    for (let i = 0; i < points.length - 1; i++) {\r\n      if (parameterValue >= points[i].x && parameterValue <= points[i + 1].x) {\r\n        // Linear interpolation\r\n        const ratio = (parameterValue - points[i].x) / (points[i + 1].x - points[i].x);\r\n        return points[i].y + ratio * (points[i + 1].y - points[i].y);\r\n      }\r\n    }\r\n    \r\n    return points[0].y; // Fallback\r\n  }\r\n\r\n  /**\r\n   * Apply correction factors to pressure loss\r\n   */\r\n  private static applyCorrectionFactors(\r\n    baseLoss: number,\r\n    config: AdvancedFittingConfiguration,\r\n    flowConditions: FlowConditions\r\n  ): number {\r\n    \r\n    let correctedLoss = baseLoss;\r\n    const corrections = config.pressureLossProfile.correctionFactors;\r\n    \r\n    // Temperature correction\r\n    if (corrections.temperatureCorrection) {\r\n      const tempCorrection = this.calculateTemperatureCorrection(flowConditions.temperature);\r\n      correctedLoss *= tempCorrection;\r\n    }\r\n    \r\n    // Density correction\r\n    if (corrections.densityCorrection) {\r\n      const densityCorrection = flowConditions.airDensity / 0.075; // Standard air density\r\n      correctedLoss *= densityCorrection;\r\n    }\r\n    \r\n    // Installation correction (simplified)\r\n    if (corrections.installationCorrection) {\r\n      correctedLoss *= 1.1; // 10% installation factor\r\n    }\r\n    \r\n    return correctedLoss;\r\n  }\r\n\r\n  /**\r\n   * Calculate temperature correction factor\r\n   */\r\n  private static calculateTemperatureCorrection(temperature: number): number {\r\n    // Standard temperature is 70°F\r\n    const standardTemp = 70;\r\n    const tempRatio = (temperature + 459.67) / (standardTemp + 459.67); // Convert to absolute temperature\r\n    return Math.sqrt(tempRatio);\r\n  }\r\n\r\n  /**\r\n   * Calculate interaction effects between adjacent fittings\r\n   */\r\n  private static calculateInteractionEffects(\r\n    config: AdvancedFittingConfiguration,\r\n    systemContext: SystemContext\r\n  ): InteractionEffects {\r\n    \r\n    const upstreamFittings = systemContext.getUpstreamFittings(config.id, 10); // 10 diameters\r\n    const downstreamFittings = systemContext.getDownstreamFittings(config.id, 10);\r\n    \r\n    let interactionFactor = 1.0;\r\n    const interactions: FittingInteraction[] = [];\r\n    \r\n    // Upstream interactions\r\n    for (const upstream of upstreamFittings) {\r\n      const interaction = this.calculateUpstreamInteraction(upstream, config);\r\n      interactionFactor *= interaction.factor;\r\n      interactions.push(interaction);\r\n    }\r\n    \r\n    // Downstream interactions\r\n    for (const downstream of downstreamFittings) {\r\n      const interaction = this.calculateDownstreamInteraction(config, downstream);\r\n      interactionFactor *= interaction.factor;\r\n      interactions.push(interaction);\r\n    }\r\n    \r\n    return {\r\n      totalInteractionFactor: interactionFactor,\r\n      individualInteractions: interactions,\r\n      significantInteractions: interactions.filter(i => Math.abs(i.factor - 1.0) > 0.05)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get default interaction effects when no system context provided\r\n   */\r\n  private static getDefaultInteractionEffects(): InteractionEffects {\r\n    return {\r\n      totalInteractionFactor: 1.0,\r\n      individualInteractions: [],\r\n      significantInteractions: []\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate upstream fitting interaction\r\n   */\r\n  private static calculateUpstreamInteraction(\r\n    upstreamFitting: AdvancedFittingConfiguration,\r\n    currentFitting: AdvancedFittingConfiguration\r\n  ): FittingInteraction {\r\n    \r\n    // Simplified interaction calculation\r\n    // In practice, would use more sophisticated models\r\n    let factor = 1.0;\r\n    let significance: 'low' | 'medium' | 'high' = 'low';\r\n    \r\n    // Check for known interaction effects\r\n    for (const effect of currentFitting.compatibilityMatrix.interactionEffects) {\r\n      if (effect.adjacentFittingType === upstreamFitting.type) {\r\n        factor = effect.magnitude;\r\n        if (effect.magnitude > 1.2) {\r\n          significance = 'high';\r\n        } else if (effect.magnitude > 1.1) {\r\n          significance = 'medium';\r\n        } else {\r\n          significance = 'low';\r\n        }\r\n        break;\r\n      }\r\n    }\r\n    \r\n    return {\r\n      adjacentFittingId: upstreamFitting.id,\r\n      distance: 5, // Simplified distance\r\n      factor: factor,\r\n      type: 'upstream',\r\n      significance: significance\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate downstream fitting interaction\r\n   */\r\n  private static calculateDownstreamInteraction(\r\n    currentFitting: AdvancedFittingConfiguration,\r\n    downstreamFitting: AdvancedFittingConfiguration\r\n  ): FittingInteraction {\r\n\r\n    // Downstream interactions typically have less effect\r\n    return {\r\n      adjacentFittingId: downstreamFitting.id,\r\n      distance: 5,\r\n      factor: 1.0, // Minimal downstream effect for most fittings\r\n      type: 'downstream',\r\n      significance: 'low'\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate performance metrics for the fitting\r\n   */\r\n  private static calculatePerformanceMetrics(\r\n    config: AdvancedFittingConfiguration,\r\n    flowConditions: FlowConditions,\r\n    pressureLoss: number\r\n  ): PerformanceMetrics {\r\n\r\n    const velocityPressure = this.calculateVelocityPressure(flowConditions.velocity, flowConditions.airDensity);\r\n\r\n    // Calculate efficiency (inverse of pressure loss coefficient)\r\n    const efficiency = Math.max(0, Math.min(100, 100 * (1 - pressureLoss / (velocityPressure * 5))));\r\n\r\n    // Estimate noise generation based on velocity and fitting type\r\n    const noiseGeneration = this.calculateNoiseGeneration(config, flowConditions);\r\n\r\n    // Calculate energy loss\r\n    const energyLoss = this.calculateEnergyLoss(flowConditions.volumeFlow, pressureLoss);\r\n\r\n    // Calculate flow uniformity based on fitting characteristics\r\n    const flowUniformity = config.flowCharacteristics.velocityProfile.uniformityIndex * 100;\r\n\r\n    // Calculate pressure recovery\r\n    const pressureRecovery = config.flowCharacteristics.turbulenceFactors.pressureRecoveryFactor * 100;\r\n\r\n    return {\r\n      efficiency: efficiency,\r\n      noiseGeneration: noiseGeneration,\r\n      energyLoss: energyLoss,\r\n      flowUniformity: flowUniformity,\r\n      pressureRecovery: pressureRecovery\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate noise generation for the fitting\r\n   */\r\n  private static calculateNoiseGeneration(\r\n    config: AdvancedFittingConfiguration,\r\n    flowConditions: FlowConditions\r\n  ): number {\r\n\r\n    // Base noise calculation using velocity and fitting characteristics\r\n    const velocityNoise = 20 * Math.log10(flowConditions.velocity / 1000); // Reference 1000 FPM\r\n    const turbulenceNoise = config.flowCharacteristics.turbulenceFactors.turbulenceIntensity * 0.5;\r\n    let fittingNoise = 1;\r\n    if (config.complexity === 'complex') {\r\n      fittingNoise = 5;\r\n    } else if (config.complexity === 'variable') {\r\n      fittingNoise = 3;\r\n    }\r\n\r\n    return Math.max(0, velocityNoise + turbulenceNoise + fittingNoise);\r\n  }\r\n\r\n  /**\r\n   * Calculate energy loss in BTU/hr\r\n   */\r\n  private static calculateEnergyLoss(volumeFlow: number, pressureLoss: number): number {\r\n    // Energy loss = (CFM × Pressure Loss in in wg × 4.5) / Fan Efficiency\r\n    // Assuming 70% fan efficiency\r\n    const fanEfficiency = 0.70;\r\n    return (volumeFlow * pressureLoss * 4.5) / fanEfficiency;\r\n  }\r\n\r\n  /**\r\n   * Validate advanced configuration and flow conditions\r\n   */\r\n  private static validateAdvancedConfiguration(\r\n    config: AdvancedFittingConfiguration,\r\n    flowConditions: FlowConditions\r\n  ): void {\r\n\r\n    // Validate flow is within fitting's operating range\r\n    const operatingRange = config.flowCharacteristics.operatingRange;\r\n    if (flowConditions.volumeFlow < operatingRange.minimum ||\r\n        flowConditions.volumeFlow > operatingRange.maximum) {\r\n      throw new Error(`Flow ${flowConditions.volumeFlow} CFM outside fitting operating range [${operatingRange.minimum}, ${operatingRange.maximum}] CFM`);\r\n    }\r\n\r\n    // Validate velocity is reasonable\r\n    if (flowConditions.velocity < 100 || flowConditions.velocity > 6000) {\r\n      throw new Error(`Velocity ${flowConditions.velocity} FPM outside reasonable range [100, 6000] FPM`);\r\n    }\r\n\r\n    // Validate Reynolds number\r\n    if (flowConditions.reynoldsNumber < 1000) {\r\n      throw new Error(`Reynolds number ${flowConditions.reynoldsNumber} too low for turbulent flow calculations`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Validate calculation results\r\n   */\r\n  private static validateResults(\r\n    config: AdvancedFittingConfiguration,\r\n    flowConditions: FlowConditions,\r\n    pressureLoss: number\r\n  ): ValidationResults {\r\n\r\n    const errors: ValidationError[] = [];\r\n    const warnings: ValidationWarning[] = [];\r\n\r\n    // Check for unreasonable pressure loss\r\n    const velocityPressure = this.calculateVelocityPressure(flowConditions.velocity, flowConditions.airDensity);\r\n    const kFactor = pressureLoss / velocityPressure;\r\n\r\n    if (kFactor > 10) {\r\n      errors.push({\r\n        code: 'E001',\r\n        message: 'Pressure loss coefficient unreasonably high',\r\n        parameter: 'k_factor',\r\n        value: kFactor\r\n      });\r\n    }\r\n\r\n    if (kFactor < 0) {\r\n      errors.push({\r\n        code: 'E002',\r\n        message: 'Negative pressure loss calculated',\r\n        parameter: 'pressure_loss',\r\n        value: pressureLoss\r\n      });\r\n    }\r\n\r\n    // Apply validation rules from configuration\r\n    for (const rule of config.validationRules) {\r\n      const parameterValue = this.getParameterValue(rule.condition.parameter, config, flowConditions);\r\n      const isViolated = this.checkValidationCondition(rule.condition, parameterValue);\r\n\r\n      if (isViolated) {\r\n        if (rule.severity === 'error') {\r\n          errors.push({\r\n            code: rule.ruleId,\r\n            message: rule.message,\r\n            parameter: rule.condition.parameter,\r\n            value: parameterValue\r\n          });\r\n        } else if (rule.severity === 'warning') {\r\n          warnings.push({\r\n            code: rule.ruleId,\r\n            message: rule.message,\r\n            severity: 'medium',\r\n            recommendation: 'Review operating conditions'\r\n          });\r\n        }\r\n      }\r\n    }\r\n\r\n    // Check uncertainty bounds\r\n    const uncertaintyBounds = config.pressureLossProfile.uncertaintyBounds;\r\n    const uncertaintyRange = pressureLoss * (uncertaintyBounds.upperBound - uncertaintyBounds.lowerBound) / 100;\r\n\r\n    if (uncertaintyRange > pressureLoss * 0.3) {\r\n      warnings.push({\r\n        code: 'W001',\r\n        message: 'High uncertainty in pressure loss calculation',\r\n        severity: 'medium',\r\n        recommendation: 'Consider using more accurate calculation method'\r\n      });\r\n    }\r\n\r\n    const complianceStatus: ComplianceStatus = {\r\n      smacnaCompliant: errors.length === 0,\r\n      ashraeCompliant: errors.length === 0,\r\n      localCodeCompliant: true,\r\n      customStandardsCompliant: true\r\n    };\r\n\r\n    return {\r\n      isValid: errors.length === 0,\r\n      errors: errors,\r\n      warnings: warnings,\r\n      complianceStatus: complianceStatus\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Check validation condition\r\n   */\r\n  private static checkValidationCondition(condition: any, value: number | string): boolean {\r\n    switch (condition.operator) {\r\n      case '>':\r\n        return Number(value) > Number(condition.value);\r\n      case '<':\r\n        return Number(value) < Number(condition.value);\r\n      case '>=':\r\n        return Number(value) >= Number(condition.value);\r\n      case '<=':\r\n        return Number(value) <= Number(condition.value);\r\n      case '=':\r\n        return value === condition.value;\r\n      case '!=':\r\n        return value !== condition.value;\r\n      case 'in':\r\n        return Array.isArray(condition.value) && condition.value.includes(value);\r\n      case 'not_in':\r\n        return Array.isArray(condition.value) && !condition.value.includes(value);\r\n      default:\r\n        return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Generate recommendations based on calculation results\r\n   */\r\n  private static generateRecommendations(\r\n    config: AdvancedFittingConfiguration,\r\n    flowConditions: FlowConditions,\r\n    performanceMetrics: PerformanceMetrics,\r\n    validationResults: ValidationResults\r\n  ): Recommendation[] {\r\n\r\n    const recommendations: Recommendation[] = [];\r\n\r\n    // Performance-based recommendations\r\n    if (performanceMetrics.efficiency < 70) {\r\n      recommendations.push({\r\n        type: 'optimization',\r\n        priority: 'medium',\r\n        description: 'Consider using a more efficient fitting design to reduce pressure loss',\r\n        expectedBenefit: 'Reduced energy consumption and improved system performance',\r\n        implementationCost: 'medium'\r\n      });\r\n    }\r\n\r\n    if (performanceMetrics.noiseGeneration > 50) {\r\n      recommendations.push({\r\n        type: 'optimization',\r\n        priority: 'medium',\r\n        description: 'Consider adding sound attenuation to reduce noise levels',\r\n        expectedBenefit: 'Improved acoustic comfort',\r\n        implementationCost: 'medium'\r\n      });\r\n    }\r\n\r\n    // Flow-based recommendations\r\n    const operatingRange = config.flowCharacteristics.operatingRange;\r\n    const flowRatio = flowConditions.volumeFlow / operatingRange.optimal;\r\n\r\n    if (flowRatio < 0.5) {\r\n      recommendations.push({\r\n        type: 'adjustment',\r\n        priority: 'low',\r\n        description: 'Flow is significantly below optimal range - consider resizing fitting',\r\n        expectedBenefit: 'Better performance and efficiency',\r\n        implementationCost: 'high'\r\n      });\r\n    }\r\n\r\n    if (flowRatio > 1.5) {\r\n      recommendations.push({\r\n        type: 'adjustment',\r\n        priority: 'high',\r\n        description: 'Flow exceeds optimal range - fitting may be undersized',\r\n        expectedBenefit: 'Reduced pressure loss and noise',\r\n        implementationCost: 'high'\r\n      });\r\n    }\r\n\r\n    // Maintenance recommendations\r\n    if (config.category === 'control' || config.category === 'terminal') {\r\n      recommendations.push({\r\n        type: 'maintenance',\r\n        priority: 'low',\r\n        description: 'Regular inspection and calibration recommended for control devices',\r\n        expectedBenefit: 'Maintained performance and reliability',\r\n        implementationCost: 'low'\r\n      });\r\n    }\r\n\r\n    return recommendations;\r\n  }\r\n\r\n  /**\r\n   * Get fitting configuration by ID from database\r\n   */\r\n  public static getFittingConfiguration(fittingId: string): AdvancedFittingConfiguration | null {\r\n    const data = this.loadAdvancedFittingsData();\r\n\r\n    // Search through all categories for the fitting\r\n    for (const categoryName of Object.keys(data.categories)) {\r\n      const category = data.categories[categoryName];\r\n      for (const typeName of Object.keys(category)) {\r\n        const type = category[typeName];\r\n        for (const configName of Object.keys(type)) {\r\n          const config = type[configName];\r\n          if (config.id === fittingId) {\r\n            return config as AdvancedFittingConfiguration;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    return null;\r\n  }\r\n\r\n  /**\r\n   * List all available fitting configurations\r\n   */\r\n  public static listAvailableFittings(): { id: string; description: string; category: string }[] {\r\n    const data = this.loadAdvancedFittingsData();\r\n    const fittings: { id: string; description: string; category: string }[] = [];\r\n\r\n    for (const categoryName of Object.keys(data.categories)) {\r\n      const category = data.categories[categoryName];\r\n      for (const typeName of Object.keys(category)) {\r\n        const type = category[typeName];\r\n        for (const configName of Object.keys(type)) {\r\n          const config = type[configName];\r\n          fittings.push({\r\n            id: config.id,\r\n            description: config.description,\r\n            category: config.category\r\n          });\r\n        }\r\n      }\r\n    }\r\n\r\n    return fittings.sort((a, b) => a.category.localeCompare(b.category) || a.description.localeCompare(b.description));\r\n  }\r\n}\r\n"], "mappings": ";;AAAA;;;;;;;;;;;;;AAAA;AAAA,SAAAA,eAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;MAAA;QAAAC,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAO,SAAA;MAAA;QAAAD,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAW,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,cAAA;MAAAC,IAAA;MAAAC,QAAA;MAAAC,KAAA;MAAAC,OAAA;MAAAC,cAAA;MAAAC,OAAA;IAAA;IAAAC,eAAA;IAAA5B,IAAA;EAAA;EAAA,IAAA6B,QAAA,GAAA5B,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAA0B,QAAA,CAAA9B,IAAA,KAAA8B,QAAA,CAAA9B,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAA6B,QAAA,CAAA9B,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAA0B,cAAA,GAAAD,QAAA,CAAA9B,IAAA;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAcA,MAAAgC,EAAA;AAAA;AAAA,CAAAjC,cAAA,GAAAoB,CAAA,QAAAc,YAAA,CAAAC,OAAA;AACA,MAAAlC,IAAA;AAAA;AAAA,CAAAD,cAAA,GAAAoB,CAAA,QAAAc,YAAA,CAAAC,OAAA;AACA,MAAAC,uBAAA;AAAA;AAAA,CAAApC,cAAA,GAAAoB,CAAA,QAAAe,OAAA;AACA,MAAAE,sBAAA;AAAA;AAAA,CAAArC,cAAA,GAAAoB,CAAA,QAAAe,OAAA;AAkBA,MAAaG,yBAA0B;AAAA;AAAA,CAAQF,uBAAA,CAAAG,qBAAqB;EAIlE;;;EAGQ,OAAOC,wBAAwBA,CAAA;IAAA;IAAAxC,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACrC,IAAI,CAAC,IAAI,CAACqB,oBAAoB,EAAE;MAAA;MAAAzC,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAC9B,IAAI;QACF,MAAMsB,WAAW;QAAA;QAAA,CAAA1C,cAAA,GAAAoB,CAAA,QAAGa,EAAE,CAACU,YAAY,CAAC,IAAI,CAACC,cAAc,EAAE,MAAM,CAAC;QAAC;QAAA5C,cAAA,GAAAoB,CAAA;QACjE,IAAI,CAACqB,oBAAoB,GAAGI,IAAI,CAACC,KAAK,CAACJ,WAAW,CAAC;MACrD,CAAC,CAAC,OAAOK,KAAK,EAAE;QAAA;QAAA/C,cAAA,GAAAoB,CAAA;QACd4B,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QAAC;QAAA/C,cAAA,GAAAoB,CAAA;QAC9D,MAAM,IAAI6B,KAAK,CAAC,2CAA2C,CAAC;MAC9D;IACF,CAAC;IAAA;IAAA;MAAAjD,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IACD,OAAO,IAAI,CAACqB,oBAAoB;EAClC;EAEA;;;EAGO,OAAOS,4BAA4BA,CACxCC,MAAoC,EACpCC,cAA8B,EAC9BC,aAA6B;IAAA;IAAArD,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAG7B;IACA,IAAI,CAACkC,6BAA6B,CAACH,MAAM,EAAEC,cAAc,CAAC;IAE1D;IACA,MAAMG,MAAM;IAAA;IAAA,CAAAvD,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACoC,uBAAuB,CAACL,MAAM,EAAEC,cAAc,CAAC;IAEnE;IACA,MAAMK,QAAQ;IAAA;IAAA,CAAAzD,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACsC,yBAAyB,CAACP,MAAM,EAAEC,cAAc,EAAEG,MAAM,CAAC;IAE/E;IACA,MAAMI,aAAa;IAAA;IAAA,CAAA3D,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACwC,sBAAsB,CAACH,QAAQ,EAAEN,MAAM,EAAEC,cAAc,CAAC;IAEnF;IACA,MAAMS,kBAAkB;IAAA;IAAA,CAAA7D,cAAA,GAAAoB,CAAA,QAAGiC,aAAa;IAAA;IAAA,CAAArD,cAAA,GAAAsB,CAAA,WACpC,IAAI,CAACwC,2BAA2B,CAACX,MAAM,EAAEE,aAAa,CAAC;IAAA;IAAA,CAAArD,cAAA,GAAAsB,CAAA,WACvD,IAAI,CAACyC,4BAA4B,EAAE;IAEvC;IACA,MAAMC,iBAAiB;IAAA;IAAA,CAAAhE,cAAA,GAAAoB,CAAA,QAAGuC,aAAa,GAAGE,kBAAkB,CAACI,sBAAsB;IAEnF;IACA,MAAMC,kBAAkB;IAAA;IAAA,CAAAlE,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAAC+C,2BAA2B,CAAChB,MAAM,EAAEC,cAAc,EAAEY,iBAAiB,CAAC;IAEtG;IACA,MAAMI,iBAAiB;IAAA;IAAA,CAAApE,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACiD,eAAe,CAAClB,MAAM,EAAEC,cAAc,EAAEY,iBAAiB,CAAC;IAEzF;IACA,MAAMM,eAAe;IAAA;IAAA,CAAAtE,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACmD,uBAAuB,CAACpB,MAAM,EAAEC,cAAc,EAAEc,kBAAkB,EAAEE,iBAAiB,CAAC;IAEnH;IAAA;IAAApE,cAAA,GAAAoB,CAAA;IACA,OAAO;MACLoD,YAAY,EAAER,iBAAiB;MAC/BS,gBAAgB,EAAE,IAAI,CAACC,yBAAyB,CAACtB,cAAc,CAACuB,QAAQ,EAAEvB,cAAc,CAACwB,UAAU,CAAC;MACpGC,OAAO,EAAEb,iBAAiB,GAAG,IAAI,CAACU,yBAAyB,CAACtB,cAAc,CAACuB,QAAQ,EAAEvB,cAAc,CAACwB,UAAU,CAAC;MAC/GE,QAAQ,EAAEV,iBAAiB,CAACU,QAAQ,CAACC,GAAG,CAACC,CAAC,IAAI;QAAA;QAAAhF,cAAA,GAAAqB,CAAA;QAAArB,cAAA,GAAAoB,CAAA;QAAA,OAAA4D,CAAC,CAACC,OAAO;MAAP,CAAO,CAAC;MACxDC,iBAAiB,EAAE3B,MAAM;MACzBM,kBAAkB,EAAEA,kBAAkB;MACtCK,kBAAkB,EAAEA,kBAAkB;MACtCE,iBAAiB,EAAEA,iBAAiB;MACpCE,eAAe,EAAEA;KAClB;EACH;EAEA;;;EAGQ,OAAOd,uBAAuBA,CACpCL,MAAoC,EACpCC,cAA8B;IAAA;IAAApD,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAG9B;IACA;IAAI;IAAA,CAAApB,cAAA,GAAAsB,CAAA,WAAA8B,cAAc,CAAC+B,cAAc,GAAG,MAAM;IAAA;IAAA,CAAAnF,cAAA,GAAAsB,CAAA,WACtC6B,MAAM,CAACiC,mBAAmB,CAACF,iBAAiB,KAAK7C,sBAAA,CAAAgD,iBAAiB,CAACC,WAAW,GAAE;MAAA;MAAAtF,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAClF,OAAOiB,sBAAA,CAAAgD,iBAAiB,CAACC,WAAW;IACtC,CAAC;IAAA;IAAA;MAAAtF,cAAA,GAAAsB,CAAA;IAAA;IAED;IAAAtB,cAAA,GAAAoB,CAAA;IACA;IAAI;IAAA,CAAApB,cAAA,GAAAsB,CAAA,WAAA6B,MAAM,CAACoC,UAAU,KAAK,SAAS;IAAA;IAAA,CAAAvF,cAAA,GAAAsB,CAAA,WAC/B6B,MAAM,CAACiC,mBAAmB,CAACI,WAAW,CAACC,qBAAqB,CAACC,MAAM,GAAG,CAAC,GAAE;MAAA;MAAA1F,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAC3E,OAAOiB,sBAAA,CAAAgD,iBAAiB,CAACM,eAAe;IAC1C,CAAC;IAAA;IAAA;MAAA3F,cAAA,GAAAsB,CAAA;IAAA;IAED;IAAAtB,cAAA,GAAAoB,CAAA;IACA;IAAI;IAAA,CAAApB,cAAA,GAAAsB,CAAA,WAAA6B,MAAM,CAACoC,UAAU,KAAK,UAAU;IAAA;IAAA,CAAAvF,cAAA,GAAAsB,CAAA,WAChC6B,MAAM,CAACiC,mBAAmB,CAACQ,iBAAiB;IAAA;IAAA,CAAA5F,cAAA,GAAAsB,CAAA,WAC5C6B,MAAM,CAACiC,mBAAmB,CAACQ,iBAAiB,CAACF,MAAM,GAAG,CAAC,GAAE;MAAA;MAAA1F,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAC3D,OAAOiB,sBAAA,CAAAgD,iBAAiB,CAACQ,iBAAiB;IAC5C,CAAC;IAAA;IAAA;MAAA7F,cAAA,GAAAsB,CAAA;IAAA;IAED;IAAAtB,cAAA,GAAAoB,CAAA;IACA,OAAOiB,sBAAA,CAAAgD,iBAAiB,CAACS,eAAe;EAC1C;EAEA;;;EAGQ,OAAOpC,yBAAyBA,CACtCP,MAAoC,EACpCC,cAA8B,EAC9BG,MAAyB;IAAA;IAAAvD,cAAA,GAAAqB,CAAA;IAGzB,MAAMoD,gBAAgB;IAAA;IAAA,CAAAzE,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACsD,yBAAyB,CAACtB,cAAc,CAACuB,QAAQ,EAAEvB,cAAc,CAACwB,UAAU,CAAC;IAAC;IAAA5E,cAAA,GAAAoB,CAAA;IAE5G,QAAQmC,MAAM;MACZ,KAAKlB,sBAAA,CAAAgD,iBAAiB,CAACS,eAAe;QAAA;QAAA9F,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACpC,OAAO,IAAI,CAAC2E,0BAA0B,CAAC5C,MAAM,EAAEsB,gBAAgB,CAAC;MAElE,KAAKpC,sBAAA,CAAAgD,iBAAiB,CAACM,eAAe;QAAA;QAAA3F,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACpC,OAAO,IAAI,CAAC4E,2BAA2B,CAAC7C,MAAM,EAAEC,cAAc,EAAEqB,gBAAgB,CAAC;MAEnF,KAAKpC,sBAAA,CAAAgD,iBAAiB,CAACQ,iBAAiB;QAAA;QAAA7F,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACtC,OAAO,IAAI,CAAC6E,6BAA6B,CAAC9C,MAAM,EAAEC,cAAc,EAAEqB,gBAAgB,CAAC;MAErF,KAAKpC,sBAAA,CAAAgD,iBAAiB,CAACC,WAAW;QAAA;QAAAtF,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAChC,OAAO,IAAI,CAAC8E,uBAAuB,CAAC/C,MAAM,EAAEC,cAAc,EAAEqB,gBAAgB,CAAC;MAE/E;QAAA;QAAAzE,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACE,MAAM,IAAI6B,KAAK,CAAC,mCAAmCM,MAAM,EAAE,CAAC;IAChE;EACF;EAEA;;;EAGQ,OAAOwC,0BAA0BA,CACvC5C,MAAoC,EACpCsB,gBAAwB;IAAA;IAAAzE,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAExB,OAAO+B,MAAM,CAACiC,mBAAmB,CAACI,WAAW,CAACW,WAAW,GAAG1B,gBAAgB;EAC9E;EAEA;;;EAGQ,OAAOuB,2BAA2BA,CACxC7C,MAAoC,EACpCC,cAA8B,EAC9BqB,gBAAwB;IAAA;IAAAzE,cAAA,GAAAqB,CAAA;IAGxB,IAAIwD,OAAO;IAAA;IAAA,CAAA7E,cAAA,GAAAoB,CAAA,QAAG+B,MAAM,CAACiC,mBAAmB,CAACI,WAAW,CAACW,WAAW;IAEhE;IAAA;IAAAnG,cAAA,GAAAoB,CAAA;IACA,KAAK,MAAMgF,UAAU,IAAIjD,MAAM,CAACiC,mBAAmB,CAACI,WAAW,CAACC,qBAAqB,EAAE;MACrF,MAAMY,cAAc;MAAA;MAAA,CAAArG,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACkF,iBAAiB,CAACF,UAAU,CAACG,SAAS,EAAEpD,MAAM,EAAEC,cAAc,CAAC;MAC3F,MAAMoD,UAAU;MAAA;MAAA,CAAAxG,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACqF,4BAA4B,CAACL,UAAU,EAAEC,cAAc,CAAC;MAAC;MAAArG,cAAA,GAAAoB,CAAA;MACjFyD,OAAO,IAAI2B,UAAU;IACvB;IAEA;IAAA;IAAAxG,cAAA,GAAAoB,CAAA;IACA,IAAI+B,MAAM,CAACiC,mBAAmB,CAACI,WAAW,CAACkB,kBAAkB,EAAEC,OAAO,EAAE;MAAA;MAAA3G,cAAA,GAAAsB,CAAA;MACtE,MAAMoF,kBAAkB;MAAA;MAAA,CAAA1G,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACwF,2BAA2B,CACzDzD,MAAM,CAACiC,mBAAmB,CAACI,WAAW,CAACkB,kBAAkB,EACzDtD,cAAc,CAAC+B,cAAc,CAC9B;MAAC;MAAAnF,cAAA,GAAAoB,CAAA;MACFyD,OAAO,IAAI6B,kBAAkB;IAC/B,CAAC;IAAA;IAAA;MAAA1G,cAAA,GAAAsB,CAAA;IAAA;IAED;IAAAtB,cAAA,GAAAoB,CAAA;IACA,KAAK,MAAMoF,UAAU,IAAIrD,MAAM,CAACiC,mBAAmB,CAACI,WAAW,CAACqB,mBAAmB,EAAE;MAAA;MAAA7G,cAAA,GAAAoB,CAAA;MACnFyD,OAAO,IAAI2B,UAAU,CAACM,gBAAgB;IACxC;IAAC;IAAA9G,cAAA,GAAAoB,CAAA;IAED,OAAOyD,OAAO,GAAGJ,gBAAgB;EACnC;EAEA;;;EAGQ,OAAOwB,6BAA6BA,CAC1C9C,MAAoC,EACpCC,cAA8B,EAC9BqB,gBAAwB;IAAA;IAAAzE,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAGxB;IAAI;IAAA,CAAApB,cAAA,GAAAsB,CAAA,YAAC6B,MAAM,CAACiC,mBAAmB,CAACQ,iBAAiB;IAAA;IAAA,CAAA5F,cAAA,GAAAsB,CAAA,WAAI6B,MAAM,CAACiC,mBAAmB,CAACQ,iBAAiB,CAACF,MAAM,KAAK,CAAC,GAAE;MAAA;MAAA1F,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAC9G,MAAM,IAAI6B,KAAK,CAAC,6CAA6C,CAAC;IAChE,CAAC;IAAA;IAAA;MAAAjD,cAAA,GAAAsB,CAAA;IAAA;IAED;IACA,MAAMyF,KAAK;IAAA;IAAA,CAAA/G,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAAC4F,sBAAsB,CAAC7D,MAAM,CAACiC,mBAAmB,CAACQ,iBAAiB,EAAExC,cAAc,CAAC;IAEvG;IACA,MAAMiD,cAAc;IAAA;IAAA,CAAArG,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACkF,iBAAiB,CAACS,KAAK,CAACR,SAAS,EAAEpD,MAAM,EAAEC,cAAc,CAAC;IAEtF;IACA,MAAMoB,YAAY;IAAA;IAAA,CAAAxE,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAAC6F,2BAA2B,CAACF,KAAK,EAAEV,cAAc,CAAC;IAAC;IAAArG,cAAA,GAAAoB,CAAA;IAE7E,OAAOoD,YAAY;EACrB;EAEA;;;EAGQ,OAAO0B,uBAAuBA,CACpC/C,MAAoC,EACpCC,cAA8B,EAC9BqB,gBAAwB;IAAA;IAAAzE,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAGxB;IACA,OAAO,IAAI,CAAC4E,2BAA2B,CAAC7C,MAAM,EAAEC,cAAc,EAAEqB,gBAAgB,CAAC;EACnF;EAEA;;;EAGQ,OAAOgC,4BAA4BA,CACzCL,UAA+B,EAC/BC,cAAsB;IAAA;IAAArG,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAGtB;IACA;IAAI;IAAA,CAAApB,cAAA,GAAAsB,CAAA,WAAA+E,cAAc,GAAGD,UAAU,CAACc,UAAU,CAAC,CAAC,CAAC;IAAA;IAAA,CAAAlH,cAAA,GAAAsB,CAAA,WAAI+E,cAAc,GAAGD,UAAU,CAACc,UAAU,CAAC,CAAC,CAAC,GAAE;MAAA;MAAAlH,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAC1F4B,OAAO,CAACmE,IAAI,CAAC,aAAaf,UAAU,CAACG,SAAS,UAAUF,cAAc,yBAAyBD,UAAU,CAACc,UAAU,CAAC,CAAC,CAAC,KAAKd,UAAU,CAACc,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;IAC1J,CAAC;IAAA;IAAA;MAAAlH,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAED,QAAQgF,UAAU,CAACgB,YAAY;MAC7B,KAAK,QAAQ;QAAA;QAAApH,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACX,OAAOgF,UAAU,CAACiB,YAAY,CAAC,CAAC,CAAC,GAAGjB,UAAU,CAACiB,YAAY,CAAC,CAAC,CAAC,GAAGhB,cAAc;MAEjF,KAAK,YAAY;QAAA;QAAArG,cAAA,GAAAsB,CAAA;QAAE;UACjB,IAAIgG,MAAM;UAAA;UAAA,CAAAtH,cAAA,GAAAoB,CAAA,QAAG,CAAC;UAAC;UAAApB,cAAA,GAAAoB,CAAA;UACf,KAAK,IAAImG,CAAC;UAAA;UAAA,CAAAvH,cAAA,GAAAoB,CAAA,QAAG,CAAC,GAAEmG,CAAC,GAAGnB,UAAU,CAACiB,YAAY,CAAC3B,MAAM,EAAE6B,CAAC,EAAE,EAAE;YAAA;YAAAvH,cAAA,GAAAoB,CAAA;YACvDkG,MAAM,IAAIlB,UAAU,CAACiB,YAAY,CAACE,CAAC,CAAC,GAAGC,IAAI,CAACC,GAAG,CAACpB,cAAc,EAAEkB,CAAC,CAAC;UACpE;UAAC;UAAAvH,cAAA,GAAAoB,CAAA;UACD,OAAOkG,MAAM;QACf;MAEA,KAAK,aAAa;QAAA;QAAAtH,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAChB,OAAOgF,UAAU,CAACiB,YAAY,CAAC,CAAC,CAAC,GAAGG,IAAI,CAACE,GAAG,CAACtB,UAAU,CAACiB,YAAY,CAAC,CAAC,CAAC,GAAGhB,cAAc,CAAC;MAE3F,KAAK,QAAQ;QAAA;QAAArG,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACX;QACA;QACA,OAAO,GAAG;MAEZ;QAAA;QAAApB,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACE,MAAM,IAAI6B,KAAK,CAAC,kCAAkCmD,UAAU,CAACgB,YAAY,EAAE,CAAC;IAChF;EACF;EAEA;;;EAGQ,OAAOd,iBAAiBA,CAC9BC,SAAiB,EACjBpD,MAAoC,EACpCC,cAA8B;IAAA;IAAApD,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAG9B,QAAQmF,SAAS;MACf,KAAK,UAAU;QAAA;QAAAvG,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACb,OAAOgC,cAAc,CAACuB,QAAQ;MAChC,KAAK,iBAAiB;QAAA;QAAA3E,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACpB,OAAOgC,cAAc,CAAC+B,cAAc;MACtC,KAAK,aAAa;QAAA;QAAAnF,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAChB,OAAOgC,cAAc,CAACuE,UAAU;MAClC,KAAK,aAAa;QAAA;QAAA3H,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAChB,OAAOgC,cAAc,CAACwE,WAAW;MACnC,KAAK,UAAU;QAAA;QAAA5H,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACb,OAAOgC,cAAc,CAACyE,QAAQ;MAChC,KAAK,0BAA0B;QAAA;QAAA7H,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAC7B,OAAO+B,MAAM,CAAC2E,kBAAkB,CAACC,UAAU,CAACrC,MAAM;QAAI;QAAA,CAAA1F,cAAA,GAAAsB,CAAA,WAAA6B,MAAM,CAAC2E,kBAAkB,CAACC,UAAU,CAACC,aAAa;QAAA;QAAA,CAAAhI,cAAA,GAAAsB,CAAA,WAAI,EAAE,EAAC;MACjH,KAAK,YAAY;QAAA;QAAAtB,cAAA,GAAAsB,CAAA;QAAE;UACjB,MAAM2G,SAAS;UAAA;UAAA,CAAAjI,cAAA,GAAAoB,CAAA,SAAGoG,IAAI,CAACU,EAAE,GAAGV,IAAI,CAACC,GAAG,CAAC;UAAC;UAAA,CAAAzH,cAAA,GAAAsB,CAAA,WAAA6B,MAAM,CAAC2E,kBAAkB,CAACC,UAAU,CAACC,aAAa;UAAA;UAAA,CAAAhI,cAAA,GAAAsB,CAAA,WAAI,EAAE,KAAI,CAAC,EAAE,CAAC,CAAC;UACvG,MAAM6G,UAAU;UAAA;UAAA,CAAAnI,cAAA,GAAAoB,CAAA,SAAGoG,IAAI,CAACU,EAAE,GAAGV,IAAI,CAACC,GAAG,CAAC;UAAC;UAAA,CAAAzH,cAAA,GAAAsB,CAAA,WAAA6B,MAAM,CAAC2E,kBAAkB,CAACC,UAAU,CAACK,cAAc;UAAA;UAAA,CAAApI,cAAA,GAAAsB,CAAA,WAAI,EAAE,KAAI,CAAC,EAAE,CAAC,CAAC;UAAC;UAAAtB,cAAA,GAAAoB,CAAA;UAC1G,OAAO+G,UAAU,GAAGF,SAAS;QAC/B;MACA,KAAK,cAAc;QAAA;QAAAjI,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACjB,OAAO+B,MAAM,CAAC2E,kBAAkB,CAACC,UAAU,CAACM,KAAK,GAAGlF,MAAM,CAAC2E,kBAAkB,CAACC,UAAU,CAACO,MAAM;MACjG;QAAA;QAAAtI,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACE4B,OAAO,CAACmE,IAAI,CAAC,sBAAsBZ,SAAS,2BAA2B,CAAC;QAAC;QAAAvG,cAAA,GAAAoB,CAAA;QACzE,OAAO,GAAG;IACd;EACF;EAEA;;;EAGQ,OAAOwF,2BAA2BA,CACxCJ,UAAe,EACfrB,cAAsB;IAAA;IAAAnF,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAGtB;IAAI;IAAA,CAAApB,cAAA,GAAAsB,CAAA,WAAA6D,cAAc,GAAGqB,UAAU,CAACU,UAAU,CAAC,CAAC,CAAC;IAAA;IAAA,CAAAlH,cAAA,GAAAsB,CAAA,WAAI6D,cAAc,GAAGqB,UAAU,CAACU,UAAU,CAAC,CAAC,CAAC,GAAE;MAAA;MAAAlH,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAC1F,OAAO,GAAG,CAAC,CAAC;IACd,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAED,QAAQoF,UAAU,CAACjD,MAAM;MACvB,KAAK,WAAW;QAAA;QAAAvD,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACd;QACA,OAAO,GAAG,GAAGoF,UAAU,CAACa,YAAY,CAAC,CAAC,CAAC,GAAGG,IAAI,CAACe,KAAK,CAACpD,cAAc,CAAC,GAAGqB,UAAU,CAACa,YAAY,CAAC,CAAC,CAAC;MAEnG,KAAK,SAAS;QAAA;QAAArH,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACZ;QACA,OAAOoG,IAAI,CAACC,GAAG,CAACtC,cAAc,EAAEqB,UAAU,CAACa,YAAY,CAAC,CAAC,CAAC,CAAC,GAAGb,UAAU,CAACa,YAAY,CAAC,CAAC,CAAC;MAE1F,KAAK,QAAQ;QAAA;QAAArH,cAAA,GAAAsB,CAAA;QAAE;UACb;UACA,IAAIgG,MAAM;UAAA;UAAA,CAAAtH,cAAA,GAAAoB,CAAA,SAAG,CAAC;UAAC;UAAApB,cAAA,GAAAoB,CAAA;UACf,KAAK,IAAImG,CAAC;UAAA;UAAA,CAAAvH,cAAA,GAAAoB,CAAA,SAAG,CAAC,GAAEmG,CAAC,GAAGf,UAAU,CAACa,YAAY,CAAC3B,MAAM,EAAE6B,CAAC,EAAE,EAAE;YAAA;YAAAvH,cAAA,GAAAoB,CAAA;YACvDkG,MAAM,IAAId,UAAU,CAACa,YAAY,CAACE,CAAC,CAAC,GAAGC,IAAI,CAACC,GAAG,CAACtC,cAAc,EAAEoC,CAAC,CAAC;UACpE;UAAC;UAAAvH,cAAA,GAAAoB,CAAA;UACD,OAAOkG,MAAM;QACf;MAEA;QAAA;QAAAtH,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACE,OAAO,GAAG;IACd;EACF;EAEA;;;EAGQ,OAAO4F,sBAAsBA,CACnCwB,MAA0B,EAC1BpF,cAA8B;IAAA;IAAApD,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAG9B;IACA;IACA,OAAOoH,MAAM,CAAC,CAAC,CAAC;EAClB;EAEA;;;EAGQ,OAAOvB,2BAA2BA,CACxCF,KAAuB,EACvBV,cAAsB;IAAA;IAAArG,cAAA,GAAAqB,CAAA;IAGtB,MAAMoH,MAAM;IAAA;IAAA,CAAAzI,cAAA,GAAAoB,CAAA,SAAG2F,KAAK,CAAC2B,UAAU,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEtH,CAAC,KAAK;MAAA;MAAAtB,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAAA,OAAAwH,CAAC,CAACC,CAAC,GAAGvH,CAAC,CAACuH,CAAC;IAAD,CAAC,CAAC;IAEzD;IAAA;IAAA7I,cAAA,GAAAoB,CAAA;IACA,IAAIiF,cAAc,IAAIoC,MAAM,CAAC,CAAC,CAAC,CAACI,CAAC,EAAE;MAAA;MAAA7I,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACjC,OAAOqH,MAAM,CAAC,CAAC,CAAC,CAACK,CAAC;IACpB,CAAC;IAAA;IAAA;MAAA9I,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IACD,IAAIiF,cAAc,IAAIoC,MAAM,CAACA,MAAM,CAAC/C,MAAM,GAAG,CAAC,CAAC,CAACmD,CAAC,EAAE;MAAA;MAAA7I,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACjD,IAAI2F,KAAK,CAACgC,oBAAoB,EAAE;QAAA;QAAA/I,cAAA,GAAAsB,CAAA;QAC9B;QACA,MAAM0H,OAAO;QAAA;QAAA,CAAAhJ,cAAA,GAAAoB,CAAA,SAAGqH,MAAM,CAACQ,KAAK,CAAC,CAAC,CAAC,CAAC;QAChC,MAAMC,KAAK;QAAA;QAAA,CAAAlJ,cAAA,GAAAoB,CAAA,SAAG,CAAC4H,OAAO,CAAC,CAAC,CAAC,CAACF,CAAC,GAAGE,OAAO,CAAC,CAAC,CAAC,CAACF,CAAC,KAAKE,OAAO,CAAC,CAAC,CAAC,CAACH,CAAC,GAAGG,OAAO,CAAC,CAAC,CAAC,CAACH,CAAC,CAAC;QAAC;QAAA7I,cAAA,GAAAoB,CAAA;QAC5E,OAAO4H,OAAO,CAAC,CAAC,CAAC,CAACF,CAAC,GAAGI,KAAK,IAAI7C,cAAc,GAAG2C,OAAO,CAAC,CAAC,CAAC,CAACH,CAAC,CAAC;MAC/D,CAAC,MAAM;QAAA;QAAA7I,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACL,OAAOqH,MAAM,CAACA,MAAM,CAAC/C,MAAM,GAAG,CAAC,CAAC,CAACoD,CAAC;MACpC;IACF,CAAC;IAAA;IAAA;MAAA9I,cAAA,GAAAsB,CAAA;IAAA;IAED;IAAAtB,cAAA,GAAAoB,CAAA;IACA,KAAK,IAAImG,CAAC;IAAA;IAAA,CAAAvH,cAAA,GAAAoB,CAAA,SAAG,CAAC,GAAEmG,CAAC,GAAGkB,MAAM,CAAC/C,MAAM,GAAG,CAAC,EAAE6B,CAAC,EAAE,EAAE;MAAA;MAAAvH,cAAA,GAAAoB,CAAA;MAC1C;MAAI;MAAA,CAAApB,cAAA,GAAAsB,CAAA,WAAA+E,cAAc,IAAIoC,MAAM,CAAClB,CAAC,CAAC,CAACsB,CAAC;MAAA;MAAA,CAAA7I,cAAA,GAAAsB,CAAA,WAAI+E,cAAc,IAAIoC,MAAM,CAAClB,CAAC,GAAG,CAAC,CAAC,CAACsB,CAAC,GAAE;QAAA;QAAA7I,cAAA,GAAAsB,CAAA;QACtE;QACA,MAAM6H,KAAK;QAAA;QAAA,CAAAnJ,cAAA,GAAAoB,CAAA,SAAG,CAACiF,cAAc,GAAGoC,MAAM,CAAClB,CAAC,CAAC,CAACsB,CAAC,KAAKJ,MAAM,CAAClB,CAAC,GAAG,CAAC,CAAC,CAACsB,CAAC,GAAGJ,MAAM,CAAClB,CAAC,CAAC,CAACsB,CAAC,CAAC;QAAC;QAAA7I,cAAA,GAAAoB,CAAA;QAC/E,OAAOqH,MAAM,CAAClB,CAAC,CAAC,CAACuB,CAAC,GAAGK,KAAK,IAAIV,MAAM,CAAClB,CAAC,GAAG,CAAC,CAAC,CAACuB,CAAC,GAAGL,MAAM,CAAClB,CAAC,CAAC,CAACuB,CAAC,CAAC;MAC9D,CAAC;MAAA;MAAA;QAAA9I,cAAA,GAAAsB,CAAA;MAAA;IACH;IAAC;IAAAtB,cAAA,GAAAoB,CAAA;IAED,OAAOqH,MAAM,CAAC,CAAC,CAAC,CAACK,CAAC,CAAC,CAAC;EACtB;EAEA;;;EAGQ,OAAOlF,sBAAsBA,CACnCH,QAAgB,EAChBN,MAAoC,EACpCC,cAA8B;IAAA;IAAApD,cAAA,GAAAqB,CAAA;IAG9B,IAAIsC,aAAa;IAAA;IAAA,CAAA3D,cAAA,GAAAoB,CAAA,SAAGqC,QAAQ;IAC5B,MAAM2F,WAAW;IAAA;IAAA,CAAApJ,cAAA,GAAAoB,CAAA,SAAG+B,MAAM,CAACiC,mBAAmB,CAACiE,iBAAiB;IAEhE;IAAA;IAAArJ,cAAA,GAAAoB,CAAA;IACA,IAAIgI,WAAW,CAACE,qBAAqB,EAAE;MAAA;MAAAtJ,cAAA,GAAAsB,CAAA;MACrC,MAAMiI,cAAc;MAAA;MAAA,CAAAvJ,cAAA,GAAAoB,CAAA,SAAG,IAAI,CAACoI,8BAA8B,CAACpG,cAAc,CAACwE,WAAW,CAAC;MAAC;MAAA5H,cAAA,GAAAoB,CAAA;MACvFuC,aAAa,IAAI4F,cAAc;IACjC,CAAC;IAAA;IAAA;MAAAvJ,cAAA,GAAAsB,CAAA;IAAA;IAED;IAAAtB,cAAA,GAAAoB,CAAA;IACA,IAAIgI,WAAW,CAACK,iBAAiB,EAAE;MAAA;MAAAzJ,cAAA,GAAAsB,CAAA;MACjC,MAAMmI,iBAAiB;MAAA;MAAA,CAAAzJ,cAAA,GAAAoB,CAAA,SAAGgC,cAAc,CAACwB,UAAU,GAAG,KAAK,EAAC,CAAC;MAAA;MAAA5E,cAAA,GAAAoB,CAAA;MAC7DuC,aAAa,IAAI8F,iBAAiB;IACpC,CAAC;IAAA;IAAA;MAAAzJ,cAAA,GAAAsB,CAAA;IAAA;IAED;IAAAtB,cAAA,GAAAoB,CAAA;IACA,IAAIgI,WAAW,CAACM,sBAAsB,EAAE;MAAA;MAAA1J,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACtCuC,aAAa,IAAI,GAAG,CAAC,CAAC;IACxB,CAAC;IAAA;IAAA;MAAA3D,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAED,OAAOuC,aAAa;EACtB;EAEA;;;EAGQ,OAAO6F,8BAA8BA,CAAC5B,WAAmB;IAAA;IAAA5H,cAAA,GAAAqB,CAAA;IAC/D;IACA,MAAMsI,YAAY;IAAA;IAAA,CAAA3J,cAAA,GAAAoB,CAAA,SAAG,EAAE;IACvB,MAAMwI,SAAS;IAAA;IAAA,CAAA5J,cAAA,GAAAoB,CAAA,SAAG,CAACwG,WAAW,GAAG,MAAM,KAAK+B,YAAY,GAAG,MAAM,CAAC,EAAC,CAAC;IAAA;IAAA3J,cAAA,GAAAoB,CAAA;IACpE,OAAOoG,IAAI,CAACqC,IAAI,CAACD,SAAS,CAAC;EAC7B;EAEA;;;EAGQ,OAAO9F,2BAA2BA,CACxCX,MAAoC,EACpCE,aAA4B;IAAA;IAAArD,cAAA,GAAAqB,CAAA;IAG5B,MAAMyI,gBAAgB;IAAA;IAAA,CAAA9J,cAAA,GAAAoB,CAAA,SAAGiC,aAAa,CAAC0G,mBAAmB,CAAC5G,MAAM,CAAC6G,EAAE,EAAE,EAAE,CAAC,EAAC,CAAC;IAC3E,MAAMC,kBAAkB;IAAA;IAAA,CAAAjK,cAAA,GAAAoB,CAAA,SAAGiC,aAAa,CAAC6G,qBAAqB,CAAC/G,MAAM,CAAC6G,EAAE,EAAE,EAAE,CAAC;IAE7E,IAAIG,iBAAiB;IAAA;IAAA,CAAAnK,cAAA,GAAAoB,CAAA,SAAG,GAAG;IAC3B,MAAMgJ,YAAY;IAAA;IAAA,CAAApK,cAAA,GAAAoB,CAAA,SAAyB,EAAE;IAE7C;IAAA;IAAApB,cAAA,GAAAoB,CAAA;IACA,KAAK,MAAMiJ,QAAQ,IAAIP,gBAAgB,EAAE;MACvC,MAAMQ,WAAW;MAAA;MAAA,CAAAtK,cAAA,GAAAoB,CAAA,SAAG,IAAI,CAACmJ,4BAA4B,CAACF,QAAQ,EAAElH,MAAM,CAAC;MAAC;MAAAnD,cAAA,GAAAoB,CAAA;MACxE+I,iBAAiB,IAAIG,WAAW,CAACE,MAAM;MAAC;MAAAxK,cAAA,GAAAoB,CAAA;MACxCgJ,YAAY,CAACK,IAAI,CAACH,WAAW,CAAC;IAChC;IAEA;IAAA;IAAAtK,cAAA,GAAAoB,CAAA;IACA,KAAK,MAAMsJ,UAAU,IAAIT,kBAAkB,EAAE;MAC3C,MAAMK,WAAW;MAAA;MAAA,CAAAtK,cAAA,GAAAoB,CAAA,SAAG,IAAI,CAACuJ,8BAA8B,CAACxH,MAAM,EAAEuH,UAAU,CAAC;MAAC;MAAA1K,cAAA,GAAAoB,CAAA;MAC5E+I,iBAAiB,IAAIG,WAAW,CAACE,MAAM;MAAC;MAAAxK,cAAA,GAAAoB,CAAA;MACxCgJ,YAAY,CAACK,IAAI,CAACH,WAAW,CAAC;IAChC;IAAC;IAAAtK,cAAA,GAAAoB,CAAA;IAED,OAAO;MACL6C,sBAAsB,EAAEkG,iBAAiB;MACzCS,sBAAsB,EAAER,YAAY;MACpCS,uBAAuB,EAAET,YAAY,CAACU,MAAM,CAACvD,CAAC,IAAI;QAAA;QAAAvH,cAAA,GAAAqB,CAAA;QAAArB,cAAA,GAAAoB,CAAA;QAAA,OAAAoG,IAAI,CAACuD,GAAG,CAACxD,CAAC,CAACiD,MAAM,GAAG,GAAG,CAAC,GAAG,IAAI;MAAJ,CAAI;KAClF;EACH;EAEA;;;EAGQ,OAAOzG,4BAA4BA,CAAA;IAAA;IAAA/D,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACzC,OAAO;MACL6C,sBAAsB,EAAE,GAAG;MAC3B2G,sBAAsB,EAAE,EAAE;MAC1BC,uBAAuB,EAAE;KAC1B;EACH;EAEA;;;EAGQ,OAAON,4BAA4BA,CACzCS,eAA6C,EAC7CC,cAA4C;IAAA;IAAAjL,cAAA,GAAAqB,CAAA;IAG5C;IACA;IACA,IAAImJ,MAAM;IAAA;IAAA,CAAAxK,cAAA,GAAAoB,CAAA,SAAG,GAAG;IAChB,IAAI8J,YAAY;IAAA;IAAA,CAAAlL,cAAA,GAAAoB,CAAA,SAA8B,KAAK;IAEnD;IAAA;IAAApB,cAAA,GAAAoB,CAAA;IACA,KAAK,MAAM+J,MAAM,IAAIF,cAAc,CAACG,mBAAmB,CAACvH,kBAAkB,EAAE;MAAA;MAAA7D,cAAA,GAAAoB,CAAA;MAC1E,IAAI+J,MAAM,CAACE,mBAAmB,KAAKL,eAAe,CAAC/J,IAAI,EAAE;QAAA;QAAAjB,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACvDoJ,MAAM,GAAGW,MAAM,CAACG,SAAS;QAAC;QAAAtL,cAAA,GAAAoB,CAAA;QAC1B,IAAI+J,MAAM,CAACG,SAAS,GAAG,GAAG,EAAE;UAAA;UAAAtL,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAoB,CAAA;UAC1B8J,YAAY,GAAG,MAAM;QACvB,CAAC,MAAM;UAAA;UAAAlL,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAoB,CAAA;UAAA,IAAI+J,MAAM,CAACG,SAAS,GAAG,GAAG,EAAE;YAAA;YAAAtL,cAAA,GAAAsB,CAAA;YAAAtB,cAAA,GAAAoB,CAAA;YACjC8J,YAAY,GAAG,QAAQ;UACzB,CAAC,MAAM;YAAA;YAAAlL,cAAA,GAAAsB,CAAA;YAAAtB,cAAA,GAAAoB,CAAA;YACL8J,YAAY,GAAG,KAAK;UACtB;QAAA;QAAC;QAAAlL,cAAA,GAAAoB,CAAA;QACD;MACF,CAAC;MAAA;MAAA;QAAApB,cAAA,GAAAsB,CAAA;MAAA;IACH;IAAC;IAAAtB,cAAA,GAAAoB,CAAA;IAED,OAAO;MACLmK,iBAAiB,EAAEP,eAAe,CAAChB,EAAE;MACrCwB,QAAQ,EAAE,CAAC;MAAE;MACbhB,MAAM,EAAEA,MAAM;MACdvJ,IAAI,EAAE,UAAU;MAChBiK,YAAY,EAAEA;KACf;EACH;EAEA;;;EAGQ,OAAOP,8BAA8BA,CAC3CM,cAA4C,EAC5CQ,iBAA+C;IAAA;IAAAzL,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAG/C;IACA,OAAO;MACLmK,iBAAiB,EAAEE,iBAAiB,CAACzB,EAAE;MACvCwB,QAAQ,EAAE,CAAC;MACXhB,MAAM,EAAE,GAAG;MAAE;MACbvJ,IAAI,EAAE,YAAY;MAClBiK,YAAY,EAAE;KACf;EACH;EAEA;;;EAGQ,OAAO/G,2BAA2BA,CACxChB,MAAoC,EACpCC,cAA8B,EAC9BoB,YAAoB;IAAA;IAAAxE,cAAA,GAAAqB,CAAA;IAGpB,MAAMoD,gBAAgB;IAAA;IAAA,CAAAzE,cAAA,GAAAoB,CAAA,SAAG,IAAI,CAACsD,yBAAyB,CAACtB,cAAc,CAACuB,QAAQ,EAAEvB,cAAc,CAACwB,UAAU,CAAC;IAE3G;IACA,MAAM8G,UAAU;IAAA;IAAA,CAAA1L,cAAA,GAAAoB,CAAA,SAAGoG,IAAI,CAACmE,GAAG,CAAC,CAAC,EAAEnE,IAAI,CAACoE,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAGpH,YAAY,IAAIC,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAEhG;IACA,MAAMoH,eAAe;IAAA;IAAA,CAAA7L,cAAA,GAAAoB,CAAA,SAAG,IAAI,CAAC0K,wBAAwB,CAAC3I,MAAM,EAAEC,cAAc,CAAC;IAE7E;IACA,MAAM2I,UAAU;IAAA;IAAA,CAAA/L,cAAA,GAAAoB,CAAA,SAAG,IAAI,CAAC4K,mBAAmB,CAAC5I,cAAc,CAACuE,UAAU,EAAEnD,YAAY,CAAC;IAEpF;IACA,MAAMyH,cAAc;IAAA;IAAA,CAAAjM,cAAA,GAAAoB,CAAA,SAAG+B,MAAM,CAAC+I,mBAAmB,CAACC,eAAe,CAACC,eAAe,GAAG,GAAG;IAEvF;IACA,MAAMC,gBAAgB;IAAA;IAAA,CAAArM,cAAA,GAAAoB,CAAA,SAAG+B,MAAM,CAAC+I,mBAAmB,CAACI,iBAAiB,CAACC,sBAAsB,GAAG,GAAG;IAAC;IAAAvM,cAAA,GAAAoB,CAAA;IAEnG,OAAO;MACLsK,UAAU,EAAEA,UAAU;MACtBG,eAAe,EAAEA,eAAe;MAChCE,UAAU,EAAEA,UAAU;MACtBE,cAAc,EAAEA,cAAc;MAC9BI,gBAAgB,EAAEA;KACnB;EACH;EAEA;;;EAGQ,OAAOP,wBAAwBA,CACrC3I,MAAoC,EACpCC,cAA8B;IAAA;IAAApD,cAAA,GAAAqB,CAAA;IAG9B;IACA,MAAMmL,aAAa;IAAA;IAAA,CAAAxM,cAAA,GAAAoB,CAAA,SAAG,EAAE,GAAGoG,IAAI,CAACe,KAAK,CAACnF,cAAc,CAACuB,QAAQ,GAAG,IAAI,CAAC,EAAC,CAAC;IACvE,MAAM8H,eAAe;IAAA;IAAA,CAAAzM,cAAA,GAAAoB,CAAA,SAAG+B,MAAM,CAAC+I,mBAAmB,CAACI,iBAAiB,CAACI,mBAAmB,GAAG,GAAG;IAC9F,IAAIC,YAAY;IAAA;IAAA,CAAA3M,cAAA,GAAAoB,CAAA,SAAG,CAAC;IAAC;IAAApB,cAAA,GAAAoB,CAAA;IACrB,IAAI+B,MAAM,CAACoC,UAAU,KAAK,SAAS,EAAE;MAAA;MAAAvF,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACnCuL,YAAY,GAAG,CAAC;IAClB,CAAC,MAAM;MAAA;MAAA3M,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,IAAI+B,MAAM,CAACoC,UAAU,KAAK,UAAU,EAAE;QAAA;QAAAvF,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAC3CuL,YAAY,GAAG,CAAC;MAClB,CAAC;MAAA;MAAA;QAAA3M,cAAA,GAAAsB,CAAA;MAAA;IAAD;IAAC;IAAAtB,cAAA,GAAAoB,CAAA;IAED,OAAOoG,IAAI,CAACmE,GAAG,CAAC,CAAC,EAAEa,aAAa,GAAGC,eAAe,GAAGE,YAAY,CAAC;EACpE;EAEA;;;EAGQ,OAAOX,mBAAmBA,CAACrE,UAAkB,EAAEnD,YAAoB;IAAA;IAAAxE,cAAA,GAAAqB,CAAA;IACzE;IACA;IACA,MAAMuL,aAAa;IAAA;IAAA,CAAA5M,cAAA,GAAAoB,CAAA,SAAG,IAAI;IAAC;IAAApB,cAAA,GAAAoB,CAAA;IAC3B,OAAQuG,UAAU,GAAGnD,YAAY,GAAG,GAAG,GAAIoI,aAAa;EAC1D;EAEA;;;EAGQ,OAAOtJ,6BAA6BA,CAC1CH,MAAoC,EACpCC,cAA8B;IAAA;IAAApD,cAAA,GAAAqB,CAAA;IAG9B;IACA,MAAMwL,cAAc;IAAA;IAAA,CAAA7M,cAAA,GAAAoB,CAAA,SAAG+B,MAAM,CAAC+I,mBAAmB,CAACW,cAAc;IAAC;IAAA7M,cAAA,GAAAoB,CAAA;IACjE;IAAI;IAAA,CAAApB,cAAA,GAAAsB,CAAA,WAAA8B,cAAc,CAACuE,UAAU,GAAGkF,cAAc,CAACC,OAAO;IAAA;IAAA,CAAA9M,cAAA,GAAAsB,CAAA,WAClD8B,cAAc,CAACuE,UAAU,GAAGkF,cAAc,CAACE,OAAO,GAAE;MAAA;MAAA/M,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACtD,MAAM,IAAI6B,KAAK,CAAC,QAAQG,cAAc,CAACuE,UAAU,yCAAyCkF,cAAc,CAACC,OAAO,KAAKD,cAAc,CAACE,OAAO,OAAO,CAAC;IACrJ,CAAC;IAAA;IAAA;MAAA/M,cAAA,GAAAsB,CAAA;IAAA;IAED;IAAAtB,cAAA,GAAAoB,CAAA;IACA;IAAI;IAAA,CAAApB,cAAA,GAAAsB,CAAA,WAAA8B,cAAc,CAACuB,QAAQ,GAAG,GAAG;IAAA;IAAA,CAAA3E,cAAA,GAAAsB,CAAA,WAAI8B,cAAc,CAACuB,QAAQ,GAAG,IAAI,GAAE;MAAA;MAAA3E,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACnE,MAAM,IAAI6B,KAAK,CAAC,YAAYG,cAAc,CAACuB,QAAQ,+CAA+C,CAAC;IACrG,CAAC;IAAA;IAAA;MAAA3E,cAAA,GAAAsB,CAAA;IAAA;IAED;IAAAtB,cAAA,GAAAoB,CAAA;IACA,IAAIgC,cAAc,CAAC+B,cAAc,GAAG,IAAI,EAAE;MAAA;MAAAnF,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACxC,MAAM,IAAI6B,KAAK,CAAC,mBAAmBG,cAAc,CAAC+B,cAAc,0CAA0C,CAAC;IAC7G,CAAC;IAAA;IAAA;MAAAnF,cAAA,GAAAsB,CAAA;IAAA;EACH;EAEA;;;EAGQ,OAAO+C,eAAeA,CAC5BlB,MAAoC,EACpCC,cAA8B,EAC9BoB,YAAoB;IAAA;IAAAxE,cAAA,GAAAqB,CAAA;IAGpB,MAAM2L,MAAM;IAAA;IAAA,CAAAhN,cAAA,GAAAoB,CAAA,SAAsB,EAAE;IACpC,MAAM0D,QAAQ;IAAA;IAAA,CAAA9E,cAAA,GAAAoB,CAAA,SAAwB,EAAE;IAExC;IACA,MAAMqD,gBAAgB;IAAA;IAAA,CAAAzE,cAAA,GAAAoB,CAAA,SAAG,IAAI,CAACsD,yBAAyB,CAACtB,cAAc,CAACuB,QAAQ,EAAEvB,cAAc,CAACwB,UAAU,CAAC;IAC3G,MAAMC,OAAO;IAAA;IAAA,CAAA7E,cAAA,GAAAoB,CAAA,SAAGoD,YAAY,GAAGC,gBAAgB;IAAC;IAAAzE,cAAA,GAAAoB,CAAA;IAEhD,IAAIyD,OAAO,GAAG,EAAE,EAAE;MAAA;MAAA7E,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAChB4L,MAAM,CAACvC,IAAI,CAAC;QACVwC,IAAI,EAAE,MAAM;QACZhI,OAAO,EAAE,6CAA6C;QACtDsB,SAAS,EAAE,UAAU;QACrB2G,KAAK,EAAErI;OACR,CAAC;IACJ,CAAC;IAAA;IAAA;MAAA7E,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAED,IAAIyD,OAAO,GAAG,CAAC,EAAE;MAAA;MAAA7E,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACf4L,MAAM,CAACvC,IAAI,CAAC;QACVwC,IAAI,EAAE,MAAM;QACZhI,OAAO,EAAE,mCAAmC;QAC5CsB,SAAS,EAAE,eAAe;QAC1B2G,KAAK,EAAE1I;OACR,CAAC;IACJ,CAAC;IAAA;IAAA;MAAAxE,cAAA,GAAAsB,CAAA;IAAA;IAED;IAAAtB,cAAA,GAAAoB,CAAA;IACA,KAAK,MAAM+L,IAAI,IAAIhK,MAAM,CAACiK,eAAe,EAAE;MACzC,MAAM/G,cAAc;MAAA;MAAA,CAAArG,cAAA,GAAAoB,CAAA,SAAG,IAAI,CAACkF,iBAAiB,CAAC6G,IAAI,CAACE,SAAS,CAAC9G,SAAS,EAAEpD,MAAM,EAAEC,cAAc,CAAC;MAC/F,MAAMkK,UAAU;MAAA;MAAA,CAAAtN,cAAA,GAAAoB,CAAA,SAAG,IAAI,CAACmM,wBAAwB,CAACJ,IAAI,CAACE,SAAS,EAAEhH,cAAc,CAAC;MAAC;MAAArG,cAAA,GAAAoB,CAAA;MAEjF,IAAIkM,UAAU,EAAE;QAAA;QAAAtN,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACd,IAAI+L,IAAI,CAACK,QAAQ,KAAK,OAAO,EAAE;UAAA;UAAAxN,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAoB,CAAA;UAC7B4L,MAAM,CAACvC,IAAI,CAAC;YACVwC,IAAI,EAAEE,IAAI,CAACM,MAAM;YACjBxI,OAAO,EAAEkI,IAAI,CAAClI,OAAO;YACrBsB,SAAS,EAAE4G,IAAI,CAACE,SAAS,CAAC9G,SAAS;YACnC2G,KAAK,EAAE7G;WACR,CAAC;QACJ,CAAC,MAAM;UAAA;UAAArG,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAoB,CAAA;UAAA,IAAI+L,IAAI,CAACK,QAAQ,KAAK,SAAS,EAAE;YAAA;YAAAxN,cAAA,GAAAsB,CAAA;YAAAtB,cAAA,GAAAoB,CAAA;YACtC0D,QAAQ,CAAC2F,IAAI,CAAC;cACZwC,IAAI,EAAEE,IAAI,CAACM,MAAM;cACjBxI,OAAO,EAAEkI,IAAI,CAAClI,OAAO;cACrBuI,QAAQ,EAAE,QAAQ;cAClBE,cAAc,EAAE;aACjB,CAAC;UACJ,CAAC;UAAA;UAAA;YAAA1N,cAAA,GAAAsB,CAAA;UAAA;QAAD;MACF,CAAC;MAAA;MAAA;QAAAtB,cAAA,GAAAsB,CAAA;MAAA;IACH;IAEA;IACA,MAAMqM,iBAAiB;IAAA;IAAA,CAAA3N,cAAA,GAAAoB,CAAA,SAAG+B,MAAM,CAACiC,mBAAmB,CAACuI,iBAAiB;IACtE,MAAMC,gBAAgB;IAAA;IAAA,CAAA5N,cAAA,GAAAoB,CAAA,SAAGoD,YAAY,IAAImJ,iBAAiB,CAACE,UAAU,GAAGF,iBAAiB,CAACG,UAAU,CAAC,GAAG,GAAG;IAAC;IAAA9N,cAAA,GAAAoB,CAAA;IAE5G,IAAIwM,gBAAgB,GAAGpJ,YAAY,GAAG,GAAG,EAAE;MAAA;MAAAxE,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACzC0D,QAAQ,CAAC2F,IAAI,CAAC;QACZwC,IAAI,EAAE,MAAM;QACZhI,OAAO,EAAE,+CAA+C;QACxDuI,QAAQ,EAAE,QAAQ;QAClBE,cAAc,EAAE;OACjB,CAAC;IACJ,CAAC;IAAA;IAAA;MAAA1N,cAAA,GAAAsB,CAAA;IAAA;IAED,MAAMyM,gBAAgB;IAAA;IAAA,CAAA/N,cAAA,GAAAoB,CAAA,SAAqB;MACzC4M,eAAe,EAAEhB,MAAM,CAACtH,MAAM,KAAK,CAAC;MACpCuI,eAAe,EAAEjB,MAAM,CAACtH,MAAM,KAAK,CAAC;MACpCwI,kBAAkB,EAAE,IAAI;MACxBC,wBAAwB,EAAE;KAC3B;IAAC;IAAAnO,cAAA,GAAAoB,CAAA;IAEF,OAAO;MACLgN,OAAO,EAAEpB,MAAM,CAACtH,MAAM,KAAK,CAAC;MAC5BsH,MAAM,EAAEA,MAAM;MACdlI,QAAQ,EAAEA,QAAQ;MAClBiJ,gBAAgB,EAAEA;KACnB;EACH;EAEA;;;EAGQ,OAAOR,wBAAwBA,CAACF,SAAc,EAAEH,KAAsB;IAAA;IAAAlN,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAC5E,QAAQiM,SAAS,CAACgB,QAAQ;MACxB,KAAK,GAAG;QAAA;QAAArO,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACN,OAAOkN,MAAM,CAACpB,KAAK,CAAC,GAAGoB,MAAM,CAACjB,SAAS,CAACH,KAAK,CAAC;MAChD,KAAK,GAAG;QAAA;QAAAlN,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACN,OAAOkN,MAAM,CAACpB,KAAK,CAAC,GAAGoB,MAAM,CAACjB,SAAS,CAACH,KAAK,CAAC;MAChD,KAAK,IAAI;QAAA;QAAAlN,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACP,OAAOkN,MAAM,CAACpB,KAAK,CAAC,IAAIoB,MAAM,CAACjB,SAAS,CAACH,KAAK,CAAC;MACjD,KAAK,IAAI;QAAA;QAAAlN,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACP,OAAOkN,MAAM,CAACpB,KAAK,CAAC,IAAIoB,MAAM,CAACjB,SAAS,CAACH,KAAK,CAAC;MACjD,KAAK,GAAG;QAAA;QAAAlN,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACN,OAAO8L,KAAK,KAAKG,SAAS,CAACH,KAAK;MAClC,KAAK,IAAI;QAAA;QAAAlN,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACP,OAAO8L,KAAK,KAAKG,SAAS,CAACH,KAAK;MAClC,KAAK,IAAI;QAAA;QAAAlN,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACP,OAAO,2BAAApB,cAAA,GAAAsB,CAAA,WAAAiN,KAAK,CAACC,OAAO,CAACnB,SAAS,CAACH,KAAK,CAAC;QAAA;QAAA,CAAAlN,cAAA,GAAAsB,CAAA,WAAI+L,SAAS,CAACH,KAAK,CAACuB,QAAQ,CAACvB,KAAK,CAAC;MAC1E,KAAK,QAAQ;QAAA;QAAAlN,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACX,OAAO,2BAAApB,cAAA,GAAAsB,CAAA,WAAAiN,KAAK,CAACC,OAAO,CAACnB,SAAS,CAACH,KAAK,CAAC;QAAA;QAAA,CAAAlN,cAAA,GAAAsB,CAAA,WAAI,CAAC+L,SAAS,CAACH,KAAK,CAACuB,QAAQ,CAACvB,KAAK,CAAC;MAC3E;QAAA;QAAAlN,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACE,OAAO,KAAK;IAChB;EACF;EAEA;;;EAGQ,OAAOmD,uBAAuBA,CACpCpB,MAAoC,EACpCC,cAA8B,EAC9Bc,kBAAsC,EACtCE,iBAAoC;IAAA;IAAApE,cAAA,GAAAqB,CAAA;IAGpC,MAAMiD,eAAe;IAAA;IAAA,CAAAtE,cAAA,GAAAoB,CAAA,SAAqB,EAAE;IAE5C;IAAA;IAAApB,cAAA,GAAAoB,CAAA;IACA,IAAI8C,kBAAkB,CAACwH,UAAU,GAAG,EAAE,EAAE;MAAA;MAAA1L,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACtCkD,eAAe,CAACmG,IAAI,CAAC;QACnBxJ,IAAI,EAAE,cAAc;QACpByN,QAAQ,EAAE,QAAQ;QAClBC,WAAW,EAAE,wEAAwE;QACrFC,eAAe,EAAE,4DAA4D;QAC7EC,kBAAkB,EAAE;OACrB,CAAC;IACJ,CAAC;IAAA;IAAA;MAAA7O,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAED,IAAI8C,kBAAkB,CAAC2H,eAAe,GAAG,EAAE,EAAE;MAAA;MAAA7L,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAC3CkD,eAAe,CAACmG,IAAI,CAAC;QACnBxJ,IAAI,EAAE,cAAc;QACpByN,QAAQ,EAAE,QAAQ;QAClBC,WAAW,EAAE,0DAA0D;QACvEC,eAAe,EAAE,2BAA2B;QAC5CC,kBAAkB,EAAE;OACrB,CAAC;IACJ,CAAC;IAAA;IAAA;MAAA7O,cAAA,GAAAsB,CAAA;IAAA;IAED;IACA,MAAMuL,cAAc;IAAA;IAAA,CAAA7M,cAAA,GAAAoB,CAAA,SAAG+B,MAAM,CAAC+I,mBAAmB,CAACW,cAAc;IAChE,MAAMiC,SAAS;IAAA;IAAA,CAAA9O,cAAA,GAAAoB,CAAA,SAAGgC,cAAc,CAACuE,UAAU,GAAGkF,cAAc,CAACkC,OAAO;IAAC;IAAA/O,cAAA,GAAAoB,CAAA;IAErE,IAAI0N,SAAS,GAAG,GAAG,EAAE;MAAA;MAAA9O,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACnBkD,eAAe,CAACmG,IAAI,CAAC;QACnBxJ,IAAI,EAAE,YAAY;QAClByN,QAAQ,EAAE,KAAK;QACfC,WAAW,EAAE,uEAAuE;QACpFC,eAAe,EAAE,mCAAmC;QACpDC,kBAAkB,EAAE;OACrB,CAAC;IACJ,CAAC;IAAA;IAAA;MAAA7O,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAED,IAAI0N,SAAS,GAAG,GAAG,EAAE;MAAA;MAAA9O,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACnBkD,eAAe,CAACmG,IAAI,CAAC;QACnBxJ,IAAI,EAAE,YAAY;QAClByN,QAAQ,EAAE,MAAM;QAChBC,WAAW,EAAE,wDAAwD;QACrEC,eAAe,EAAE,iCAAiC;QAClDC,kBAAkB,EAAE;OACrB,CAAC;IACJ,CAAC;IAAA;IAAA;MAAA7O,cAAA,GAAAsB,CAAA;IAAA;IAED;IAAAtB,cAAA,GAAAoB,CAAA;IACA;IAAI;IAAA,CAAApB,cAAA,GAAAsB,CAAA,WAAA6B,MAAM,CAAC6L,QAAQ,KAAK,SAAS;IAAA;IAAA,CAAAhP,cAAA,GAAAsB,CAAA,WAAI6B,MAAM,CAAC6L,QAAQ,KAAK,UAAU,GAAE;MAAA;MAAAhP,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACnEkD,eAAe,CAACmG,IAAI,CAAC;QACnBxJ,IAAI,EAAE,aAAa;QACnByN,QAAQ,EAAE,KAAK;QACfC,WAAW,EAAE,oEAAoE;QACjFC,eAAe,EAAE,wCAAwC;QACzDC,kBAAkB,EAAE;OACrB,CAAC;IACJ,CAAC;IAAA;IAAA;MAAA7O,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAED,OAAOkD,eAAe;EACxB;EAEA;;;EAGO,OAAO2K,uBAAuBA,CAACC,SAAiB;IAAA;IAAAlP,cAAA,GAAAqB,CAAA;IACrD,MAAM8N,IAAI;IAAA;IAAA,CAAAnP,cAAA,GAAAoB,CAAA,SAAG,IAAI,CAACoB,wBAAwB,EAAE;IAE5C;IAAA;IAAAxC,cAAA,GAAAoB,CAAA;IACA,KAAK,MAAMgO,YAAY,IAAIC,MAAM,CAACC,IAAI,CAACH,IAAI,CAACI,UAAU,CAAC,EAAE;MACvD,MAAMP,QAAQ;MAAA;MAAA,CAAAhP,cAAA,GAAAoB,CAAA,SAAG+N,IAAI,CAACI,UAAU,CAACH,YAAY,CAAC;MAAC;MAAApP,cAAA,GAAAoB,CAAA;MAC/C,KAAK,MAAMoO,QAAQ,IAAIH,MAAM,CAACC,IAAI,CAACN,QAAQ,CAAC,EAAE;QAC5C,MAAM/N,IAAI;QAAA;QAAA,CAAAjB,cAAA,GAAAoB,CAAA,SAAG4N,QAAQ,CAACQ,QAAQ,CAAC;QAAC;QAAAxP,cAAA,GAAAoB,CAAA;QAChC,KAAK,MAAMqO,UAAU,IAAIJ,MAAM,CAACC,IAAI,CAACrO,IAAI,CAAC,EAAE;UAC1C,MAAMkC,MAAM;UAAA;UAAA,CAAAnD,cAAA,GAAAoB,CAAA,SAAGH,IAAI,CAACwO,UAAU,CAAC;UAAC;UAAAzP,cAAA,GAAAoB,CAAA;UAChC,IAAI+B,MAAM,CAAC6G,EAAE,KAAKkF,SAAS,EAAE;YAAA;YAAAlP,cAAA,GAAAsB,CAAA;YAAAtB,cAAA,GAAAoB,CAAA;YAC3B,OAAO+B,MAAsC;UAC/C,CAAC;UAAA;UAAA;YAAAnD,cAAA,GAAAsB,CAAA;UAAA;QACH;MACF;IACF;IAAC;IAAAtB,cAAA,GAAAoB,CAAA;IAED,OAAO,IAAI;EACb;EAEA;;;EAGO,OAAOsO,qBAAqBA,CAAA;IAAA;IAAA1P,cAAA,GAAAqB,CAAA;IACjC,MAAM8N,IAAI;IAAA;IAAA,CAAAnP,cAAA,GAAAoB,CAAA,SAAG,IAAI,CAACoB,wBAAwB,EAAE;IAC5C,MAAMmN,QAAQ;IAAA;IAAA,CAAA3P,cAAA,GAAAoB,CAAA,SAA4D,EAAE;IAAC;IAAApB,cAAA,GAAAoB,CAAA;IAE7E,KAAK,MAAMgO,YAAY,IAAIC,MAAM,CAACC,IAAI,CAACH,IAAI,CAACI,UAAU,CAAC,EAAE;MACvD,MAAMP,QAAQ;MAAA;MAAA,CAAAhP,cAAA,GAAAoB,CAAA,SAAG+N,IAAI,CAACI,UAAU,CAACH,YAAY,CAAC;MAAC;MAAApP,cAAA,GAAAoB,CAAA;MAC/C,KAAK,MAAMoO,QAAQ,IAAIH,MAAM,CAACC,IAAI,CAACN,QAAQ,CAAC,EAAE;QAC5C,MAAM/N,IAAI;QAAA;QAAA,CAAAjB,cAAA,GAAAoB,CAAA,SAAG4N,QAAQ,CAACQ,QAAQ,CAAC;QAAC;QAAAxP,cAAA,GAAAoB,CAAA;QAChC,KAAK,MAAMqO,UAAU,IAAIJ,MAAM,CAACC,IAAI,CAACrO,IAAI,CAAC,EAAE;UAC1C,MAAMkC,MAAM;UAAA;UAAA,CAAAnD,cAAA,GAAAoB,CAAA,SAAGH,IAAI,CAACwO,UAAU,CAAC;UAAC;UAAAzP,cAAA,GAAAoB,CAAA;UAChCuO,QAAQ,CAAClF,IAAI,CAAC;YACZT,EAAE,EAAE7G,MAAM,CAAC6G,EAAE;YACb2E,WAAW,EAAExL,MAAM,CAACwL,WAAW;YAC/BK,QAAQ,EAAE7L,MAAM,CAAC6L;WAClB,CAAC;QACJ;MACF;IACF;IAAC;IAAAhP,cAAA,GAAAoB,CAAA;IAED,OAAOuO,QAAQ,CAAChH,IAAI,CAAC,CAACC,CAAC,EAAEtH,CAAC,KAAK;MAAA;MAAAtB,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAAA,kCAAApB,cAAA,GAAAsB,CAAA,WAAAsH,CAAC,CAACoG,QAAQ,CAACY,aAAa,CAACtO,CAAC,CAAC0N,QAAQ,CAAC;MAAA;MAAA,CAAAhP,cAAA,GAAAsB,CAAA,WAAIsH,CAAC,CAAC+F,WAAW,CAACiB,aAAa,CAACtO,CAAC,CAACqN,WAAW,CAAC;IAAD,CAAC,CAAC;EACpH;;;;AAr0BFkB,OAAA,CAAAvN,yBAAA,GAAAA,yBAAA;AAs0BC;AAAAtC,cAAA,GAAAoB,CAAA;AAr0BgBkB,yBAAA,CAAAG,oBAAoB,GAAQ,IAAI;AAAC;AAAAzC,cAAA,GAAAoB,CAAA;AACxBkB,yBAAA,CAAAM,cAAc,GAAG3C,IAAI,CAAC6P,IAAI,CAACC,SAAS,EAAE,mCAAmC,CAAC", "ignoreList": []}