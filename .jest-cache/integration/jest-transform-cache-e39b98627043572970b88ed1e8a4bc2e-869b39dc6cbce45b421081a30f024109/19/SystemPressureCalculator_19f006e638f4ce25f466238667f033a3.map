{"version": 3, "names": ["cov_2n09p8ow7h", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "name", "decl", "loc", "branchMap", "type", "locations", "undefined", "s", "f", "b", "inputSourceMap", "file", "mappings", "names", "sources", "sourcesContent", "version", "_coverageSchema", "coverage", "actualCoverage", "AirDuctCalculator_1", "require", "FittingLossCalculator_1", "AirPropertiesCalculator_1", "SystemPressureCalculator", "calculateSystemPressure", "inputs", "segments", "systemType", "designConditions", "calculationOptions", "validateSystemInputs", "airDensity", "calculateAirDensity", "segmentResults", "totalFrictionLoss", "totalMinorLoss", "totalLength", "velocities", "segment", "segmentResult", "calculateSegmentPressure", "push", "frictionLoss", "minor<PERSON><PERSON>", "length", "velocity", "totalPressureLoss", "averageVelocity", "reduce", "sum", "v", "maxVelocity", "Math", "max", "minVelocity", "min", "validation", "validateSystemResults", "roundToPrecision", "roundingPrecision", "systemWarnings", "warnings", "systemRecommendations", "recommendations", "complianceStatus", "compliance", "calculationMethod", "frictionMethod", "calculationDate", "Date", "options", "area", "calculateDuctArea", "airflow", "velocityPressure", "FittingLossCalculator", "calculateVelocityPressure", "reynoldsNumber", "frictionFactor", "kFactor", "fittingDetails", "ductInputs", "ductType", "ductShape", "frictionRate", "units", "material", "diameter", "calculateEquivalentDiameter", "width", "height", "calculateFrictionLoss", "calculateReynoldsNumber", "calculateFrictionFactor", "fittingConfig", "calculateFittingLoss", "pressureLoss", "totalLoss", "toFixed", "segmentId", "id", "segmentType", "PI", "pow", "Error", "conditions", "temperature", "barometricPressure", "altitude", "standardDensity", "tempRatio", "pressureRatio", "altitudeRatio", "exp", "standardLoss", "AirDuctCalculator", "densityRatio", "velocityFps", "diameterFt", "kinematicViscosity", "reynolds", "data", "velocityLimits", "SYSTEM_VELOCITY_LIMITS", "pressureLimits", "SYSTEM_PRESSURE_LIMITS", "velocityCompliant", "pressureCompliant", "smacnaCompliant", "recommended", "value", "precision", "factor", "round", "getSystemLimits", "supply", "pressure", "calculateEnhancedSystemPressure", "results", "totalFittingLoss", "totalElevationLoss", "notes", "calculateEnhancedSegmentPressure", "elevation", "elevationEffect", "calculateElevationPressure", "pressureChange", "totalAirflow", "map", "calculateAverageVelocity", "envWarnings", "validateEnvironmentalConditions", "analysis", "analyzeEnhancedSystemPerformance", "fitting<PERSON>oss", "elevationLoss", "systemMetrics", "frictionPercentage", "fittingPercentage", "elevationPercentage", "systemEfficiency", "calculateSystemEfficiency", "timestamp", "toISOString", "airConditions", "humidity", "airProps", "AirPropertiesCalculator", "calculateAirProperties", "calculateEnhancedFrictionLoss", "calculateEnhancedFittingLoss", "materialData", "getEnhancedMaterialRoughness", "materialAge", "surfaceCondition", "calculateCorrectedPressureLoss", "roughness", "equivalentDiameter", "materialRoughness", "density", "correctionFactors", "vpParams", "vpResult", "fittingResult", "correctedPressureLoss", "elevationEffects", "calculateElevationEffects", "totalVelocity", "count", "limits", "r", "hasEnvironmentalCorrections", "some", "abs", "combined", "hasAgingEffects", "viscosity", "relativeRoughness", "calculateEnhancedFrictionFactor", "pressureLossPsf", "i", "fNew", "log10", "sqrt", "exports", "return", "exhaust"], "sources": ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\SystemPressureCalculator.ts"], "sourcesContent": ["/**\r\n * SystemPressureCalculator - Modular calculation service for complete HVAC system pressure analysis\r\n * \r\n * MISSION-CRITICAL: Pure TypeScript functions for system-level pressure drop calculations\r\n * Combines friction losses (straight runs) with minor losses (fittings) for complete system analysis\r\n * \r\n * @see docs/implementation/duct-physics/system-pressure-calculations.md\r\n * @see backend/services/calculations/AirDuctCalculator.ts\r\n * @see backend/services/calculations/FittingLossCalculator.ts\r\n */\r\n\r\nimport { AirDuctCalculator, DuctSizingInputs } from './AirDuctCalculator';\r\nimport { FittingLossCalculator, FittingConfiguration, FittingLossResult } from './FittingLossCalculator';\r\nimport { AirPropertiesCalculator, AirConditions, AirProperties, VelocityPressureParams } from './AirPropertiesCalculator';\r\n\r\n/**\r\n * Duct segment for system calculations\r\n */\r\nexport interface DuctSegment {\r\n  id: string;\r\n  type: 'straight' | 'fitting';\r\n  ductShape: 'round' | 'rectangular';\r\n  \r\n  // Geometry\r\n  length?: number; // feet (for straight segments)\r\n  diameter?: number; // inches (for round ducts)\r\n  width?: number; // inches (for rectangular ducts)\r\n  height?: number; // inches (for rectangular ducts)\r\n  \r\n  // Flow properties\r\n  airflow: number; // CFM\r\n  material: string; // e.g., 'galvanized_steel'\r\n  \r\n  // Fitting properties (for fitting segments)\r\n  fittingConfig?: FittingConfiguration;\r\n  \r\n  // Environmental conditions\r\n  elevation?: number; // feet (for elevation pressure calculations)\r\n  temperature?: number; // °F (for air density corrections)\r\n  humidity?: number; // % RH (for air density corrections)\r\n  pressure?: number; // in Hg (for air density corrections)\r\n\r\n  // Material aging and condition\r\n  materialAge?: number; // years (for roughness corrections)\r\n  surfaceCondition?: 'excellent' | 'good' | 'fair' | 'poor'; // for roughness corrections\r\n\r\n  notes?: string;\r\n}\r\n\r\n/**\r\n * System calculation inputs\r\n */\r\nexport interface SystemCalculationInputs {\r\n  segments: DuctSegment[];\r\n  systemType: 'supply' | 'return' | 'exhaust';\r\n  designConditions: {\r\n    temperature: number; // °F\r\n    barometricPressure: number; // in Hg\r\n    altitude: number; // feet above sea level\r\n  };\r\n  calculationOptions: {\r\n    includeElevationEffects: boolean;\r\n    includeTemperatureEffects: boolean;\r\n    frictionMethod: 'darcy_weisbach' | 'colebrook_white';\r\n    roundingPrecision: number; // decimal places\r\n  };\r\n}\r\n\r\n/**\r\n * Segment calculation result\r\n */\r\nexport interface SegmentResult {\r\n  segmentId: string;\r\n  segmentType: 'straight' | 'fitting';\r\n  \r\n  // Flow properties\r\n  velocity: number; // FPM\r\n  velocityPressure: number; // inches w.g.\r\n  reynoldsNumber?: number;\r\n  \r\n  // Pressure losses\r\n  frictionLoss: number; // inches w.g. (for straight segments)\r\n  minorLoss: number; // inches w.g. (for fitting segments)\r\n  totalLoss: number; // inches w.g.\r\n  \r\n  // Additional data\r\n  frictionFactor?: number;\r\n  kFactor?: number;\r\n  fittingDetails?: FittingLossResult;\r\n  \r\n  // Validation\r\n  warnings: string[];\r\n  recommendations: string[];\r\n}\r\n\r\n/**\r\n * System calculation result\r\n */\r\nexport interface SystemCalculationResult {\r\n  // Summary\r\n  totalPressureLoss: number; // inches w.g.\r\n  totalFrictionLoss: number; // inches w.g.\r\n  totalMinorLoss: number; // inches w.g.\r\n  \r\n  // System properties\r\n  totalLength: number; // feet\r\n  averageVelocity: number; // FPM\r\n  maxVelocity: number; // FPM\r\n  minVelocity: number; // FPM\r\n  \r\n  // Detailed results\r\n  segmentResults: SegmentResult[];\r\n  \r\n  // System validation\r\n  systemWarnings: string[];\r\n  systemRecommendations: string[];\r\n  complianceStatus: {\r\n    velocityCompliant: boolean;\r\n    pressureCompliant: boolean;\r\n    smacnaCompliant: boolean;\r\n  };\r\n  \r\n  // Calculation metadata\r\n  calculationMethod: string;\r\n  calculationDate: Date;\r\n  designConditions: SystemCalculationInputs['designConditions'];\r\n}\r\n\r\n/**\r\n * SystemPressureCalculator - Pure calculation functions for system pressure analysis\r\n * CRITICAL: No dependencies on UI, storage, or external services\r\n */\r\nexport class SystemPressureCalculator {\r\n  \r\n  // SMACNA system pressure limits (inches w.g.)\r\n  private static readonly SYSTEM_PRESSURE_LIMITS = {\r\n    supply: { max: 6.0, recommended: 4.0 },\r\n    return: { max: 4.0, recommended: 2.5 },\r\n    exhaust: { max: 8.0, recommended: 5.0 }\r\n  };\r\n\r\n  // SMACNA velocity limits by system type (FPM)\r\n  private static readonly SYSTEM_VELOCITY_LIMITS = {\r\n    supply: { min: 400, max: 2500, recommended: 1500 },\r\n    return: { min: 300, max: 2000, recommended: 1200 },\r\n    exhaust: { min: 500, max: 3000, recommended: 1800 }\r\n  };\r\n\r\n  /**\r\n   * Calculate complete system pressure drop\r\n   */\r\n  public static calculateSystemPressure(inputs: SystemCalculationInputs): SystemCalculationResult {\r\n    const { segments, systemType, designConditions, calculationOptions } = inputs;\r\n    \r\n    // Validate inputs\r\n    this.validateSystemInputs(inputs);\r\n    \r\n    // Calculate air density for design conditions\r\n    const airDensity = this.calculateAirDensity(designConditions);\r\n    \r\n    // Process each segment\r\n    const segmentResults: SegmentResult[] = [];\r\n    let totalFrictionLoss = 0;\r\n    let totalMinorLoss = 0;\r\n    let totalLength = 0;\r\n    const velocities: number[] = [];\r\n    \r\n    for (const segment of segments) {\r\n      const segmentResult = this.calculateSegmentPressure(segment, airDensity, calculationOptions);\r\n      segmentResults.push(segmentResult);\r\n      \r\n      totalFrictionLoss += segmentResult.frictionLoss;\r\n      totalMinorLoss += segmentResult.minorLoss;\r\n      \r\n      if (segment.length) {\r\n        totalLength += segment.length;\r\n      }\r\n      \r\n      velocities.push(segmentResult.velocity);\r\n    }\r\n    \r\n    const totalPressureLoss = totalFrictionLoss + totalMinorLoss;\r\n    \r\n    // Calculate system statistics\r\n    const averageVelocity = velocities.reduce((sum, v) => sum + v, 0) / velocities.length;\r\n    const maxVelocity = Math.max(...velocities);\r\n    const minVelocity = Math.min(...velocities);\r\n    \r\n    // System validation\r\n    const validation = this.validateSystemResults({\r\n      totalPressureLoss,\r\n      averageVelocity,\r\n      maxVelocity,\r\n      minVelocity,\r\n      systemType,\r\n      segmentResults\r\n    });\r\n    \r\n    return {\r\n      totalPressureLoss: this.roundToPrecision(totalPressureLoss, calculationOptions.roundingPrecision),\r\n      totalFrictionLoss: this.roundToPrecision(totalFrictionLoss, calculationOptions.roundingPrecision),\r\n      totalMinorLoss: this.roundToPrecision(totalMinorLoss, calculationOptions.roundingPrecision),\r\n      totalLength,\r\n      averageVelocity: this.roundToPrecision(averageVelocity, calculationOptions.roundingPrecision),\r\n      maxVelocity: this.roundToPrecision(maxVelocity, calculationOptions.roundingPrecision),\r\n      minVelocity: this.roundToPrecision(minVelocity, calculationOptions.roundingPrecision),\r\n      segmentResults,\r\n      systemWarnings: validation.warnings,\r\n      systemRecommendations: validation.recommendations,\r\n      complianceStatus: validation.compliance,\r\n      calculationMethod: `${calculationOptions.frictionMethod}_with_fitting_losses`,\r\n      calculationDate: new Date(),\r\n      designConditions\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate pressure drop for a single segment\r\n   */\r\n  private static calculateSegmentPressure(\r\n    segment: DuctSegment,\r\n    airDensity: number,\r\n    options: SystemCalculationInputs['calculationOptions']\r\n  ): SegmentResult {\r\n    \r\n    const warnings: string[] = [];\r\n    const recommendations: string[] = [];\r\n    \r\n    // Calculate velocity and velocity pressure\r\n    const area = this.calculateDuctArea(segment);\r\n    const velocity = segment.airflow / area; // FPM\r\n    const velocityPressure = FittingLossCalculator.calculateVelocityPressure({ velocity, airDensity });\r\n    \r\n    let frictionLoss = 0;\r\n    let minorLoss = 0;\r\n    let reynoldsNumber: number | undefined;\r\n    let frictionFactor: number | undefined;\r\n    let kFactor: number | undefined;\r\n    let fittingDetails: FittingLossResult | undefined;\r\n    \r\n    if (segment.type === 'straight' && segment.length) {\r\n      // Calculate friction loss for straight segment\r\n      const ductInputs: DuctSizingInputs = {\r\n        airflow: segment.airflow,\r\n        ductType: segment.ductShape,\r\n        frictionRate: 0.08, // Will be recalculated\r\n        units: 'imperial',\r\n        material: segment.material\r\n      };\r\n      \r\n      // Use existing AirDuctCalculator for friction calculations\r\n      const diameter = segment.diameter || this.calculateEquivalentDiameter(segment.width!, segment.height!);\r\n      frictionLoss = this.calculateFrictionLoss(velocity, segment.length, diameter, segment.material, airDensity);\r\n      \r\n      // Calculate Reynolds number and friction factor for reference\r\n      reynoldsNumber = this.calculateReynoldsNumber(velocity, diameter, airDensity);\r\n      frictionFactor = this.calculateFrictionFactor(reynoldsNumber, segment.material, diameter);\r\n      \r\n    } else if (segment.type === 'fitting' && segment.fittingConfig) {\r\n      // Calculate minor loss for fitting\r\n      fittingDetails = FittingLossCalculator.calculateFittingLoss(\r\n        segment.fittingConfig,\r\n        velocity,\r\n        airDensity\r\n      );\r\n      \r\n      minorLoss = fittingDetails.pressureLoss;\r\n      kFactor = fittingDetails.kFactor;\r\n      warnings.push(...fittingDetails.warnings);\r\n      recommendations.push(...fittingDetails.recommendations);\r\n    }\r\n    \r\n    const totalLoss = frictionLoss + minorLoss;\r\n    \r\n    // Segment-level validation\r\n    if (velocity > 3000) {\r\n      warnings.push(`High velocity (${velocity.toFixed(0)} FPM) may cause noise issues`);\r\n      recommendations.push('Consider increasing duct size to reduce velocity');\r\n    }\r\n    \r\n    if (velocity < 300) {\r\n      warnings.push(`Low velocity (${velocity.toFixed(0)} FPM) may cause poor air distribution`);\r\n      recommendations.push('Consider decreasing duct size to increase velocity');\r\n    }\r\n    \r\n    return {\r\n      segmentId: segment.id,\r\n      segmentType: segment.type,\r\n      velocity: this.roundToPrecision(velocity, options.roundingPrecision),\r\n      velocityPressure: this.roundToPrecision(velocityPressure, options.roundingPrecision),\r\n      reynoldsNumber,\r\n      frictionLoss: this.roundToPrecision(frictionLoss, options.roundingPrecision),\r\n      minorLoss: this.roundToPrecision(minorLoss, options.roundingPrecision),\r\n      totalLoss: this.roundToPrecision(totalLoss, options.roundingPrecision),\r\n      frictionFactor,\r\n      kFactor,\r\n      fittingDetails,\r\n      warnings,\r\n      recommendations\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate duct cross-sectional area\r\n   */\r\n  private static calculateDuctArea(segment: DuctSegment): number {\r\n    if (segment.ductShape === 'round' && segment.diameter) {\r\n      return Math.PI * Math.pow(segment.diameter / 12, 2) / 4; // sq ft\r\n    } else if (segment.ductShape === 'rectangular' && segment.width && segment.height) {\r\n      return (segment.width * segment.height) / 144; // sq ft\r\n    }\r\n    \r\n    throw new Error(`Invalid duct geometry for segment ${segment.id}`);\r\n  }\r\n\r\n  /**\r\n   * Calculate equivalent diameter for rectangular ducts\r\n   */\r\n  private static calculateEquivalentDiameter(width: number, height: number): number {\r\n    // Equivalent diameter formula: De = 1.30 * (a*b)^0.625 / (a+b)^0.25\r\n    return 1.30 * Math.pow(width * height, 0.625) / Math.pow(width + height, 0.25);\r\n  }\r\n\r\n  /**\r\n   * Calculate air density based on design conditions\r\n   */\r\n  private static calculateAirDensity(conditions: SystemCalculationInputs['designConditions']): number {\r\n    const { temperature, barometricPressure, altitude } = conditions;\r\n    \r\n    // Standard air density at 70°F, 29.92 in Hg, sea level\r\n    const standardDensity = 0.075; // lb/ft³\r\n    \r\n    // Temperature correction (assuming ideal gas)\r\n    const tempRatio = (459.67 + 70) / (459.67 + temperature);\r\n    \r\n    // Pressure correction\r\n    const pressureRatio = barometricPressure / 29.92;\r\n    \r\n    // Altitude correction (approximate)\r\n    const altitudeRatio = Math.exp(-altitude / 26000);\r\n    \r\n    return standardDensity * tempRatio * pressureRatio * altitudeRatio;\r\n  }\r\n\r\n  /**\r\n   * Calculate friction loss using Darcy-Weisbach equation\r\n   */\r\n  private static calculateFrictionLoss(\r\n    velocity: number,\r\n    length: number,\r\n    diameter: number,\r\n    material: string,\r\n    airDensity: number\r\n  ): number {\r\n    // Use AirDuctCalculator's existing method but adjust for air density\r\n    const standardLoss = AirDuctCalculator['calculatePressureLoss'](velocity, length, diameter, material);\r\n    const densityRatio = airDensity / 0.075;\r\n    return standardLoss * densityRatio;\r\n  }\r\n\r\n  /**\r\n   * Calculate Reynolds number\r\n   */\r\n  private static calculateReynoldsNumber(velocity: number, diameter: number, airDensity: number): number {\r\n    const velocityFps = velocity / 60; // FPM to FPS\r\n    const diameterFt = diameter / 12; // inches to feet\r\n    const kinematicViscosity = 1.57e-4; // ft²/s at standard conditions\r\n    \r\n    return (velocityFps * diameterFt) / kinematicViscosity;\r\n  }\r\n\r\n  /**\r\n   * Calculate friction factor\r\n   */\r\n  private static calculateFrictionFactor(reynolds: number, material: string, diameter: number): number {\r\n    // Use AirDuctCalculator's existing method\r\n    return AirDuctCalculator['calculateFrictionFactor'](reynolds, material, diameter);\r\n  }\r\n\r\n  /**\r\n   * Validate system inputs\r\n   */\r\n  private static validateSystemInputs(inputs: SystemCalculationInputs): void {\r\n    if (!inputs.segments || inputs.segments.length === 0) {\r\n      throw new Error('System must contain at least one segment');\r\n    }\r\n    \r\n    for (const segment of inputs.segments) {\r\n      if (segment.airflow <= 0) {\r\n        throw new Error(`Invalid airflow for segment ${segment.id}`);\r\n      }\r\n      \r\n      if (segment.type === 'straight' && (!segment.length || segment.length <= 0)) {\r\n        throw new Error(`Straight segment ${segment.id} must have positive length`);\r\n      }\r\n      \r\n      if (segment.type === 'fitting' && !segment.fittingConfig) {\r\n        throw new Error(`Fitting segment ${segment.id} must have fitting configuration`);\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Validate system results\r\n   */\r\n  private static validateSystemResults(data: {\r\n    totalPressureLoss: number;\r\n    averageVelocity: number;\r\n    maxVelocity: number;\r\n    minVelocity: number;\r\n    systemType: string;\r\n    segmentResults: SegmentResult[];\r\n  }): {\r\n    warnings: string[];\r\n    recommendations: string[];\r\n    compliance: { velocityCompliant: boolean; pressureCompliant: boolean; smacnaCompliant: boolean };\r\n  } {\r\n    const warnings: string[] = [];\r\n    const recommendations: string[] = [];\r\n    \r\n    const velocityLimits = this.SYSTEM_VELOCITY_LIMITS[data.systemType];\r\n    const pressureLimits = this.SYSTEM_PRESSURE_LIMITS[data.systemType];\r\n    \r\n    // Velocity compliance\r\n    const velocityCompliant = data.maxVelocity <= velocityLimits.max && data.minVelocity >= velocityLimits.min;\r\n    if (!velocityCompliant) {\r\n      warnings.push(`System velocities outside SMACNA limits (${velocityLimits.min}-${velocityLimits.max} FPM)`);\r\n      recommendations.push('Resize ducts to achieve compliant velocities');\r\n    }\r\n    \r\n    // Pressure compliance\r\n    const pressureCompliant = data.totalPressureLoss <= pressureLimits.max;\r\n    if (!pressureCompliant) {\r\n      warnings.push(`System pressure loss (${data.totalPressureLoss.toFixed(2)} in wg) exceeds SMACNA limit (${pressureLimits.max} in wg)`);\r\n      recommendations.push('Reduce system pressure loss by optimizing duct sizes and minimizing fittings');\r\n    }\r\n    \r\n    // Overall SMACNA compliance\r\n    const smacnaCompliant = velocityCompliant && pressureCompliant;\r\n    \r\n    // Additional system-level checks\r\n    if (data.totalPressureLoss > pressureLimits.recommended) {\r\n      recommendations.push(`Consider reducing pressure loss below ${pressureLimits.recommended} in wg for optimal efficiency`);\r\n    }\r\n    \r\n    if (data.averageVelocity > velocityLimits.recommended) {\r\n      recommendations.push(`Consider reducing average velocity below ${velocityLimits.recommended} FPM for noise control`);\r\n    }\r\n    \r\n    return {\r\n      warnings,\r\n      recommendations,\r\n      compliance: { velocityCompliant, pressureCompliant, smacnaCompliant }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Round number to specified precision\r\n   */\r\n  private static roundToPrecision(value: number, precision: number): number {\r\n    const factor = Math.pow(10, precision);\r\n    return Math.round(value * factor) / factor;\r\n  }\r\n\r\n  /**\r\n   * Get system pressure limits for a system type\r\n   */\r\n  public static getSystemLimits(systemType: string): {\r\n    velocity: { min: number; max: number; recommended: number };\r\n    pressure: { max: number; recommended: number };\r\n  } {\r\n    return {\r\n      velocity: this.SYSTEM_VELOCITY_LIMITS[systemType] || this.SYSTEM_VELOCITY_LIMITS.supply,\r\n      pressure: this.SYSTEM_PRESSURE_LIMITS[systemType] || this.SYSTEM_PRESSURE_LIMITS.supply\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate enhanced system pressure with environmental corrections\r\n   */\r\n  public static calculateEnhancedSystemPressure(inputs: SystemCalculationInputs): SystemCalculationResult {\r\n    const { segments, systemType, designConditions, calculationOptions } = inputs;\r\n\r\n    // Validate inputs\r\n    this.validateSystemInputs(inputs);\r\n\r\n    const results: SegmentResult[] = [];\r\n    let totalPressureLoss = 0;\r\n    let totalFrictionLoss = 0;\r\n    let totalFittingLoss = 0;\r\n    let totalElevationLoss = 0;\r\n    const warnings: string[] = [];\r\n    const notes: string[] = [];\r\n\r\n    // Process each segment with enhanced calculations\r\n    for (const segment of segments) {\r\n      const segmentResult = this.calculateEnhancedSegmentPressure(segment, designConditions);\r\n\r\n      results.push(segmentResult);\r\n      totalPressureLoss += segmentResult.pressureLoss;\r\n\r\n      if (segmentResult.type === 'friction') {\r\n        totalFrictionLoss += segmentResult.pressureLoss;\r\n      } else if (segmentResult.type === 'fitting') {\r\n        totalFittingLoss += segmentResult.pressureLoss;\r\n      }\r\n\r\n      // Add elevation effects if present\r\n      if (segment.elevation !== undefined) {\r\n        const elevationEffect = this.calculateElevationPressure(segment);\r\n        totalElevationLoss += elevationEffect.pressureChange;\r\n        if (elevationEffect.warnings.length > 0) {\r\n          warnings.push(...elevationEffect.warnings);\r\n        }\r\n      }\r\n\r\n      // Collect warnings and notes\r\n      if (segmentResult.warnings) {\r\n        warnings.push(...segmentResult.warnings);\r\n      }\r\n      if (segmentResult.notes) {\r\n        notes.push(...segmentResult.notes);\r\n      }\r\n    }\r\n\r\n    // Calculate system-level metrics\r\n    const totalAirflow = Math.max(...segments.map(s => s.airflow));\r\n    const averageVelocity = this.calculateAverageVelocity(segments);\r\n\r\n    // Add environmental condition warnings\r\n    const envWarnings = this.validateEnvironmentalConditions(designConditions);\r\n    warnings.push(...envWarnings);\r\n\r\n    // Performance analysis with enhanced data\r\n    const analysis = this.analyzeEnhancedSystemPerformance(\r\n      results,\r\n      totalPressureLoss,\r\n      totalAirflow,\r\n      systemType,\r\n      designConditions\r\n    );\r\n\r\n    return {\r\n      totalPressureLoss: this.roundToPrecision(totalPressureLoss + totalElevationLoss, 4),\r\n      frictionLoss: this.roundToPrecision(totalFrictionLoss, 4),\r\n      fittingLoss: this.roundToPrecision(totalFittingLoss, 4),\r\n      elevationLoss: this.roundToPrecision(totalElevationLoss, 4),\r\n      segments: results,\r\n      systemMetrics: {\r\n        totalAirflow,\r\n        averageVelocity: this.roundToPrecision(averageVelocity, 1),\r\n        frictionPercentage: this.roundToPrecision((totalFrictionLoss / totalPressureLoss) * 100, 1),\r\n        fittingPercentage: this.roundToPrecision((totalFittingLoss / totalPressureLoss) * 100, 1),\r\n        elevationPercentage: this.roundToPrecision((totalElevationLoss / totalPressureLoss) * 100, 1),\r\n        systemEfficiency: this.calculateSystemEfficiency(totalFrictionLoss, totalFittingLoss)\r\n      },\r\n      analysis,\r\n      warnings,\r\n      notes,\r\n      calculationMethod: 'Enhanced with environmental corrections',\r\n      timestamp: new Date().toISOString()\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate enhanced segment pressure with environmental corrections\r\n   */\r\n  private static calculateEnhancedSegmentPressure(\r\n    segment: DuctSegment,\r\n    designConditions: any\r\n  ): SegmentResult {\r\n    const warnings: string[] = [];\r\n    const notes: string[] = [];\r\n\r\n    // Determine air conditions for this segment\r\n    const airConditions: AirConditions = {\r\n      temperature: segment.temperature || designConditions.temperature || 70,\r\n      pressure: segment.pressure || designConditions.barometricPressure,\r\n      altitude: segment.elevation || designConditions.altitude || 0,\r\n      humidity: segment.humidity || designConditions.humidity || 50\r\n    };\r\n\r\n    // Get enhanced air properties\r\n    const airProps = AirPropertiesCalculator.calculateAirProperties(airConditions);\r\n    warnings.push(...airProps.warnings);\r\n    notes.push(...airProps.notes);\r\n\r\n    if (segment.type === 'straight' && segment.length) {\r\n      // Enhanced friction loss calculation\r\n      return this.calculateEnhancedFrictionLoss(segment, airProps, warnings, notes);\r\n    } else if (segment.type === 'fitting' && segment.fittingConfig) {\r\n      // Enhanced fitting loss calculation\r\n      return this.calculateEnhancedFittingLoss(segment, airProps, warnings, notes);\r\n    }\r\n\r\n    // Fallback to basic calculation\r\n    return {\r\n      segmentId: segment.id,\r\n      type: 'unknown',\r\n      pressureLoss: 0,\r\n      velocity: 0,\r\n      warnings: ['Unknown segment type'],\r\n      notes: []\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate enhanced friction loss with material aging and environmental corrections\r\n   */\r\n  private static calculateEnhancedFrictionLoss(\r\n    segment: DuctSegment,\r\n    airProps: AirProperties,\r\n    warnings: string[],\r\n    notes: string[]\r\n  ): SegmentResult {\r\n    // Get enhanced material roughness\r\n    const materialData = AirPropertiesCalculator.getEnhancedMaterialRoughness(\r\n      segment.material,\r\n      segment.materialAge,\r\n      segment.surfaceCondition\r\n    );\r\n\r\n    warnings.push(...materialData.warnings);\r\n    notes.push(...materialData.notes);\r\n\r\n    // Calculate velocity\r\n    const area = segment.ductShape === 'round'\r\n      ? Math.PI * Math.pow((segment.diameter || 12) / 12, 2) / 4\r\n      : ((segment.width || 12) * (segment.height || 12)) / 144;\r\n    const velocity = segment.airflow / area;\r\n\r\n    // Enhanced pressure loss calculation using corrected air properties\r\n    const diameter = segment.ductShape === 'round'\r\n      ? segment.diameter || 12\r\n      : AirDuctCalculator.calculateEquivalentDiameter(segment.width || 12, segment.height || 12);\r\n\r\n    // Use enhanced calculation with corrected air density and viscosity\r\n    const pressureLoss = this.calculateCorrectedPressureLoss(\r\n      velocity,\r\n      segment.length || 0,\r\n      diameter,\r\n      materialData.roughness,\r\n      airProps\r\n    );\r\n\r\n    return {\r\n      segmentId: segment.id,\r\n      type: 'friction',\r\n      pressureLoss: this.roundToPrecision(pressureLoss, 4),\r\n      velocity: this.roundToPrecision(velocity, 1),\r\n      equivalentDiameter: this.roundToPrecision(diameter, 2),\r\n      materialRoughness: materialData.roughness,\r\n      airDensity: airProps.density,\r\n      correctionFactors: airProps.correctionFactors,\r\n      warnings,\r\n      notes\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate enhanced fitting loss with environmental corrections\r\n   */\r\n  private static calculateEnhancedFittingLoss(\r\n    segment: DuctSegment,\r\n    airProps: AirProperties,\r\n    warnings: string[],\r\n    notes: string[]\r\n  ): SegmentResult {\r\n    // Calculate velocity\r\n    const area = segment.ductShape === 'round'\r\n      ? Math.PI * Math.pow((segment.diameter || 12) / 12, 2) / 4\r\n      : ((segment.width || 12) * (segment.height || 12)) / 144;\r\n    const velocity = segment.airflow / area;\r\n\r\n    // Calculate velocity pressure with environmental corrections\r\n    const vpParams: VelocityPressureParams = {\r\n      velocity,\r\n      airConditions: {\r\n        temperature: segment.temperature || 70,\r\n        pressure: segment.pressure,\r\n        altitude: segment.elevation || 0,\r\n        humidity: segment.humidity || 50\r\n      }\r\n    };\r\n\r\n    const vpResult = AirPropertiesCalculator.calculateVelocityPressure(vpParams);\r\n    warnings.push(...vpResult.warnings);\r\n\r\n    // Calculate fitting loss using enhanced velocity pressure\r\n    const fittingResult = FittingLossCalculator.calculateFittingLoss(\r\n      segment.fittingConfig!,\r\n      velocity,\r\n      airProps.density\r\n    );\r\n\r\n    // Apply velocity pressure correction\r\n    const correctedPressureLoss = fittingResult.pressureLoss *\r\n      (vpResult.velocityPressure / Math.pow(velocity / 4005, 2));\r\n\r\n    return {\r\n      segmentId: segment.id,\r\n      type: 'fitting',\r\n      pressureLoss: this.roundToPrecision(correctedPressureLoss, 4),\r\n      velocity: this.roundToPrecision(velocity, 1),\r\n      kFactor: fittingResult.kFactor,\r\n      velocityPressure: vpResult.velocityPressure,\r\n      airDensity: airProps.density,\r\n      correctionFactors: vpResult.correctionFactors,\r\n      warnings,\r\n      notes\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate elevation pressure effects\r\n   */\r\n  private static calculateElevationPressure(segment: DuctSegment): {\r\n    pressureChange: number;\r\n    warnings: string[];\r\n  } {\r\n    const warnings: string[] = [];\r\n\r\n    if (!segment.elevation) {\r\n      return { pressureChange: 0, warnings };\r\n    }\r\n\r\n    // Calculate elevation effects using enhanced air properties\r\n    const elevationEffects = AirPropertiesCalculator.calculateElevationEffects(segment.elevation);\r\n    warnings.push(...elevationEffects.warnings);\r\n\r\n    // Pressure change due to elevation (simplified)\r\n    // ΔP = ρ × g × Δh / gc (converted to inches w.g.)\r\n    const airDensity = 0.075; // lb/ft³ at standard conditions\r\n    const pressureChange = (airDensity * segment.elevation) / 5.2; // Convert to inches w.g.\r\n\r\n    return {\r\n      pressureChange: pressureChange * elevationEffects.densityRatio,\r\n      warnings\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate average velocity across all segments\r\n   */\r\n  private static calculateAverageVelocity(segments: DuctSegment[]): number {\r\n    let totalVelocity = 0;\r\n    let count = 0;\r\n\r\n    for (const segment of segments) {\r\n      const area = segment.ductShape === 'round'\r\n        ? Math.PI * Math.pow((segment.diameter || 12) / 12, 2) / 4\r\n        : ((segment.width || 12) * (segment.height || 12)) / 144;\r\n\r\n      const velocity = segment.airflow / area;\r\n      totalVelocity += velocity;\r\n      count++;\r\n    }\r\n\r\n    return count > 0 ? totalVelocity / count : 0;\r\n  }\r\n\r\n  /**\r\n   * Validate environmental conditions\r\n   */\r\n  private static validateEnvironmentalConditions(designConditions: any): string[] {\r\n    const warnings: string[] = [];\r\n\r\n    if (designConditions.temperature < 32 || designConditions.temperature > 200) {\r\n      warnings.push(`Temperature ${designConditions.temperature}°F is outside normal HVAC range`);\r\n    }\r\n\r\n    if (designConditions.altitude && designConditions.altitude > 5000) {\r\n      warnings.push(`High altitude (${designConditions.altitude} ft) requires density corrections`);\r\n    }\r\n\r\n    if (designConditions.humidity && designConditions.humidity > 80) {\r\n      warnings.push(`High humidity (${designConditions.humidity}% RH) may cause condensation`);\r\n    }\r\n\r\n    return warnings;\r\n  }\r\n\r\n  /**\r\n   * Analyze enhanced system performance\r\n   */\r\n  private static analyzeEnhancedSystemPerformance(\r\n    results: SegmentResult[],\r\n    totalPressureLoss: number,\r\n    totalAirflow: number,\r\n    systemType: string,\r\n    designConditions: any\r\n  ): SystemAnalysis {\r\n    const warnings: string[] = [];\r\n    const recommendations: string[] = [];\r\n\r\n    // Enhanced performance analysis\r\n    const limits = this.getSystemLimits(systemType);\r\n    const averageVelocity = results.reduce((sum, r) => sum + (r.velocity || 0), 0) / results.length;\r\n\r\n    // Velocity compliance check\r\n    const velocityCompliant = averageVelocity >= limits.velocity.min &&\r\n                             averageVelocity <= limits.velocity.max;\r\n\r\n    if (!velocityCompliant) {\r\n      warnings.push(`Average velocity ${averageVelocity.toFixed(0)} FPM is outside recommended range`);\r\n    }\r\n\r\n    // Pressure compliance check\r\n    const pressureCompliant = totalPressureLoss <= limits.pressure.max;\r\n\r\n    if (!pressureCompliant) {\r\n      warnings.push(`Total pressure loss ${totalPressureLoss.toFixed(3)} in wg exceeds maximum`);\r\n    }\r\n\r\n    // Environmental impact analysis\r\n    const hasEnvironmentalCorrections = results.some(r =>\r\n      r.correctionFactors && Math.abs(r.correctionFactors.combined - 1.0) > 0.05\r\n    );\r\n\r\n    if (hasEnvironmentalCorrections) {\r\n      recommendations.push('Environmental conditions significantly affect system performance');\r\n    }\r\n\r\n    // Material aging analysis\r\n    const hasAgingEffects = results.some(r => r.materialRoughness && r.materialRoughness > 0.0005);\r\n\r\n    if (hasAgingEffects) {\r\n      recommendations.push('Consider duct cleaning or replacement for aged materials');\r\n    }\r\n\r\n    return {\r\n      warnings,\r\n      recommendations,\r\n      compliance: {\r\n        velocityCompliant,\r\n        pressureCompliant,\r\n        smacnaCompliant: velocityCompliant && pressureCompliant\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate corrected pressure loss with enhanced air properties\r\n   */\r\n  private static calculateCorrectedPressureLoss(\r\n    velocity: number,\r\n    length: number,\r\n    diameter: number,\r\n    roughness: number,\r\n    airProps: AirProperties\r\n  ): number {\r\n    // Convert units\r\n    const velocityFps = velocity / 60; // FPM to FPS\r\n    const diameterFt = diameter / 12; // inches to feet\r\n\r\n    // Calculate Reynolds number with corrected viscosity\r\n    const reynolds = (velocityFps * diameterFt * airProps.density) / airProps.viscosity;\r\n\r\n    // Calculate friction factor using enhanced roughness\r\n    const relativeRoughness = roughness / diameterFt;\r\n    const frictionFactor = this.calculateEnhancedFrictionFactor(reynolds, relativeRoughness);\r\n\r\n    // Darcy-Weisbach equation with corrected air density\r\n    const pressureLossPsf = frictionFactor * (length / diameterFt) *\r\n                           (airProps.density * Math.pow(velocityFps, 2)) / (2 * 32.174);\r\n\r\n    // Convert to inches w.g.\r\n    return pressureLossPsf / 5.2;\r\n  }\r\n\r\n  /**\r\n   * Enhanced friction factor calculation\r\n   */\r\n  private static calculateEnhancedFrictionFactor(reynolds: number, relativeRoughness: number): number {\r\n    // For laminar flow\r\n    if (reynolds < 2300) {\r\n      return 64 / reynolds;\r\n    }\r\n\r\n    // For turbulent flow - Colebrook-White equation (iterative solution)\r\n    let f = 0.02; // Initial guess\r\n\r\n    for (let i = 0; i < 10; i++) {\r\n      const fNew = 1 / Math.pow(-2 * Math.log10(relativeRoughness / 3.7 + 2.51 / (reynolds * Math.sqrt(f))), 2);\r\n\r\n      if (Math.abs(fNew - f) < 0.0001) {\r\n        break;\r\n      }\r\n      f = fNew;\r\n    }\r\n\r\n    return f;\r\n  }\r\n}\r\n"], "mappings": ";;AAAA;;;;;;;;;;AAAA;AAAA,SAAAA,eAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;MAAA;QAAAC,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAO,SAAA;MAAA;QAAAD,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;IAAA;IAAAW,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,cAAA;MAAAC,IAAA;MAAAC,QAAA;MAAAC,KAAA;MAAAC,OAAA;MAAAC,cAAA;MAAAC,OAAA;IAAA;IAAAC,eAAA;IAAA5B,IAAA;EAAA;EAAA,IAAA6B,QAAA,GAAA5B,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAA0B,QAAA,CAAA9B,IAAA,KAAA8B,QAAA,CAAA9B,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAA6B,QAAA,CAAA9B,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAA0B,cAAA,GAAAD,QAAA,CAAA9B,IAAA;EAAA;IAaA;IAAAD,cAAA,YAAAA,CAAA;MAAA,OAAAgC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAhC,cAAA;AAAAA,cAAA,GAAAoB,CAAA;;;;;;;AAFA,MAAAa,mBAAA;AAAA;AAAA,CAAAjC,cAAA,GAAAoB,CAAA,OAAAc,OAAA;AACA,MAAAC,uBAAA;AAAA;AAAA,CAAAnC,cAAA,GAAAoB,CAAA,OAAAc,OAAA;AACA,MAAAE,yBAAA;AAAA;AAAA,CAAApC,cAAA,GAAAoB,CAAA,OAAAc,OAAA;AAmHA;;;;AAIA,MAAaG,wBAAwB;EAgBnC;;;EAGO,OAAOC,uBAAuBA,CAACC,MAA+B;IAAA;IAAAvC,cAAA,GAAAqB,CAAA;IACnE,MAAM;MAAEmB,QAAQ;MAAEC,UAAU;MAAEC,gBAAgB;MAAEC;IAAkB,CAAE;IAAA;IAAA,CAAA3C,cAAA,GAAAoB,CAAA,OAAGmB,MAAM;IAE7E;IAAA;IAAAvC,cAAA,GAAAoB,CAAA;IACA,IAAI,CAACwB,oBAAoB,CAACL,MAAM,CAAC;IAEjC;IACA,MAAMM,UAAU;IAAA;IAAA,CAAA7C,cAAA,GAAAoB,CAAA,OAAG,IAAI,CAAC0B,mBAAmB,CAACJ,gBAAgB,CAAC;IAE7D;IACA,MAAMK,cAAc;IAAA;IAAA,CAAA/C,cAAA,GAAAoB,CAAA,OAAoB,EAAE;IAC1C,IAAI4B,iBAAiB;IAAA;IAAA,CAAAhD,cAAA,GAAAoB,CAAA,OAAG,CAAC;IACzB,IAAI6B,cAAc;IAAA;IAAA,CAAAjD,cAAA,GAAAoB,CAAA,QAAG,CAAC;IACtB,IAAI8B,WAAW;IAAA;IAAA,CAAAlD,cAAA,GAAAoB,CAAA,QAAG,CAAC;IACnB,MAAM+B,UAAU;IAAA;IAAA,CAAAnD,cAAA,GAAAoB,CAAA,QAAa,EAAE;IAAC;IAAApB,cAAA,GAAAoB,CAAA;IAEhC,KAAK,MAAMgC,OAAO,IAAIZ,QAAQ,EAAE;MAC9B,MAAMa,aAAa;MAAA;MAAA,CAAArD,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACkC,wBAAwB,CAACF,OAAO,EAAEP,UAAU,EAAEF,kBAAkB,CAAC;MAAC;MAAA3C,cAAA,GAAAoB,CAAA;MAC7F2B,cAAc,CAACQ,IAAI,CAACF,aAAa,CAAC;MAAC;MAAArD,cAAA,GAAAoB,CAAA;MAEnC4B,iBAAiB,IAAIK,aAAa,CAACG,YAAY;MAAC;MAAAxD,cAAA,GAAAoB,CAAA;MAChD6B,cAAc,IAAII,aAAa,CAACI,SAAS;MAAC;MAAAzD,cAAA,GAAAoB,CAAA;MAE1C,IAAIgC,OAAO,CAACM,MAAM,EAAE;QAAA;QAAA1D,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAClB8B,WAAW,IAAIE,OAAO,CAACM,MAAM;MAC/B,CAAC;MAAA;MAAA;QAAA1D,cAAA,GAAAsB,CAAA;MAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAED+B,UAAU,CAACI,IAAI,CAACF,aAAa,CAACM,QAAQ,CAAC;IACzC;IAEA,MAAMC,iBAAiB;IAAA;IAAA,CAAA5D,cAAA,GAAAoB,CAAA,QAAG4B,iBAAiB,GAAGC,cAAc;IAE5D;IACA,MAAMY,eAAe;IAAA;IAAA,CAAA7D,cAAA,GAAAoB,CAAA,QAAG+B,UAAU,CAACW,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAK;MAAA;MAAAhE,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAAA,OAAA2C,GAAG,GAAGC,CAAC;IAAD,CAAC,EAAE,CAAC,CAAC,GAAGb,UAAU,CAACO,MAAM;IACrF,MAAMO,WAAW;IAAA;IAAA,CAAAjE,cAAA,GAAAoB,CAAA,QAAG8C,IAAI,CAACC,GAAG,CAAC,GAAGhB,UAAU,CAAC;IAC3C,MAAMiB,WAAW;IAAA;IAAA,CAAApE,cAAA,GAAAoB,CAAA,QAAG8C,IAAI,CAACG,GAAG,CAAC,GAAGlB,UAAU,CAAC;IAE3C;IACA,MAAMmB,UAAU;IAAA;IAAA,CAAAtE,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACmD,qBAAqB,CAAC;MAC5CX,iBAAiB;MACjBC,eAAe;MACfI,WAAW;MACXG,WAAW;MACX3B,UAAU;MACVM;KACD,CAAC;IAAC;IAAA/C,cAAA,GAAAoB,CAAA;IAEH,OAAO;MACLwC,iBAAiB,EAAE,IAAI,CAACY,gBAAgB,CAACZ,iBAAiB,EAAEjB,kBAAkB,CAAC8B,iBAAiB,CAAC;MACjGzB,iBAAiB,EAAE,IAAI,CAACwB,gBAAgB,CAACxB,iBAAiB,EAAEL,kBAAkB,CAAC8B,iBAAiB,CAAC;MACjGxB,cAAc,EAAE,IAAI,CAACuB,gBAAgB,CAACvB,cAAc,EAAEN,kBAAkB,CAAC8B,iBAAiB,CAAC;MAC3FvB,WAAW;MACXW,eAAe,EAAE,IAAI,CAACW,gBAAgB,CAACX,eAAe,EAAElB,kBAAkB,CAAC8B,iBAAiB,CAAC;MAC7FR,WAAW,EAAE,IAAI,CAACO,gBAAgB,CAACP,WAAW,EAAEtB,kBAAkB,CAAC8B,iBAAiB,CAAC;MACrFL,WAAW,EAAE,IAAI,CAACI,gBAAgB,CAACJ,WAAW,EAAEzB,kBAAkB,CAAC8B,iBAAiB,CAAC;MACrF1B,cAAc;MACd2B,cAAc,EAAEJ,UAAU,CAACK,QAAQ;MACnCC,qBAAqB,EAAEN,UAAU,CAACO,eAAe;MACjDC,gBAAgB,EAAER,UAAU,CAACS,UAAU;MACvCC,iBAAiB,EAAE,GAAGrC,kBAAkB,CAACsC,cAAc,sBAAsB;MAC7EC,eAAe,EAAE,IAAIC,IAAI,EAAE;MAC3BzC;KACD;EACH;EAEA;;;EAGQ,OAAOY,wBAAwBA,CACrCF,OAAoB,EACpBP,UAAkB,EAClBuC,OAAsD;IAAA;IAAApF,cAAA,GAAAqB,CAAA;IAGtD,MAAMsD,QAAQ;IAAA;IAAA,CAAA3E,cAAA,GAAAoB,CAAA,QAAa,EAAE;IAC7B,MAAMyD,eAAe;IAAA;IAAA,CAAA7E,cAAA,GAAAoB,CAAA,QAAa,EAAE;IAEpC;IACA,MAAMiE,IAAI;IAAA;IAAA,CAAArF,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACkE,iBAAiB,CAAClC,OAAO,CAAC;IAC5C,MAAMO,QAAQ;IAAA;IAAA,CAAA3D,cAAA,GAAAoB,CAAA,QAAGgC,OAAO,CAACmC,OAAO,GAAGF,IAAI,EAAC,CAAC;IACzC,MAAMG,gBAAgB;IAAA;IAAA,CAAAxF,cAAA,GAAAoB,CAAA,QAAGe,uBAAA,CAAAsD,qBAAqB,CAACC,yBAAyB,CAAC;MAAE/B,QAAQ;MAAEd;IAAU,CAAE,CAAC;IAElG,IAAIW,YAAY;IAAA;IAAA,CAAAxD,cAAA,GAAAoB,CAAA,QAAG,CAAC;IACpB,IAAIqC,SAAS;IAAA;IAAA,CAAAzD,cAAA,GAAAoB,CAAA,QAAG,CAAC;IACjB,IAAIuE,cAAkC;IACtC,IAAIC,cAAkC;IACtC,IAAIC,OAA2B;IAC/B,IAAIC,cAA6C;IAAC;IAAA9F,cAAA,GAAAoB,CAAA;IAElD;IAAI;IAAA,CAAApB,cAAA,GAAAsB,CAAA,UAAA8B,OAAO,CAACnC,IAAI,KAAK,UAAU;IAAA;IAAA,CAAAjB,cAAA,GAAAsB,CAAA,UAAI8B,OAAO,CAACM,MAAM,GAAE;MAAA;MAAA1D,cAAA,GAAAsB,CAAA;MACjD;MACA,MAAMyE,UAAU;MAAA;MAAA,CAAA/F,cAAA,GAAAoB,CAAA,QAAqB;QACnCmE,OAAO,EAAEnC,OAAO,CAACmC,OAAO;QACxBS,QAAQ,EAAE5C,OAAO,CAAC6C,SAAS;QAC3BC,YAAY,EAAE,IAAI;QAAE;QACpBC,KAAK,EAAE,UAAU;QACjBC,QAAQ,EAAEhD,OAAO,CAACgD;OACnB;MAED;MACA,MAAMC,QAAQ;MAAA;MAAA,CAAArG,cAAA,GAAAoB,CAAA;MAAG;MAAA,CAAApB,cAAA,GAAAsB,CAAA,UAAA8B,OAAO,CAACiD,QAAQ;MAAA;MAAA,CAAArG,cAAA,GAAAsB,CAAA,UAAI,IAAI,CAACgF,2BAA2B,CAAClD,OAAO,CAACmD,KAAM,EAAEnD,OAAO,CAACoD,MAAO,CAAC;MAAC;MAAAxG,cAAA,GAAAoB,CAAA;MACvGoC,YAAY,GAAG,IAAI,CAACiD,qBAAqB,CAAC9C,QAAQ,EAAEP,OAAO,CAACM,MAAM,EAAE2C,QAAQ,EAAEjD,OAAO,CAACgD,QAAQ,EAAEvD,UAAU,CAAC;MAE3G;MAAA;MAAA7C,cAAA,GAAAoB,CAAA;MACAuE,cAAc,GAAG,IAAI,CAACe,uBAAuB,CAAC/C,QAAQ,EAAE0C,QAAQ,EAAExD,UAAU,CAAC;MAAC;MAAA7C,cAAA,GAAAoB,CAAA;MAC9EwE,cAAc,GAAG,IAAI,CAACe,uBAAuB,CAAChB,cAAc,EAAEvC,OAAO,CAACgD,QAAQ,EAAEC,QAAQ,CAAC;IAE3F,CAAC,MAAM;MAAA;MAAArG,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA;MAAI;MAAA,CAAApB,cAAA,GAAAsB,CAAA,UAAA8B,OAAO,CAACnC,IAAI,KAAK,SAAS;MAAA;MAAA,CAAAjB,cAAA,GAAAsB,CAAA,UAAI8B,OAAO,CAACwD,aAAa,GAAE;QAAA;QAAA5G,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAC9D;QACA0E,cAAc,GAAG3D,uBAAA,CAAAsD,qBAAqB,CAACoB,oBAAoB,CACzDzD,OAAO,CAACwD,aAAa,EACrBjD,QAAQ,EACRd,UAAU,CACX;QAAC;QAAA7C,cAAA,GAAAoB,CAAA;QAEFqC,SAAS,GAAGqC,cAAc,CAACgB,YAAY;QAAC;QAAA9G,cAAA,GAAAoB,CAAA;QACxCyE,OAAO,GAAGC,cAAc,CAACD,OAAO;QAAC;QAAA7F,cAAA,GAAAoB,CAAA;QACjCuD,QAAQ,CAACpB,IAAI,CAAC,GAAGuC,cAAc,CAACnB,QAAQ,CAAC;QAAC;QAAA3E,cAAA,GAAAoB,CAAA;QAC1CyD,eAAe,CAACtB,IAAI,CAAC,GAAGuC,cAAc,CAACjB,eAAe,CAAC;MACzD,CAAC;MAAA;MAAA;QAAA7E,cAAA,GAAAsB,CAAA;MAAA;IAAD;IAEA,MAAMyF,SAAS;IAAA;IAAA,CAAA/G,cAAA,GAAAoB,CAAA,QAAGoC,YAAY,GAAGC,SAAS;IAE1C;IAAA;IAAAzD,cAAA,GAAAoB,CAAA;IACA,IAAIuC,QAAQ,GAAG,IAAI,EAAE;MAAA;MAAA3D,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACnBuD,QAAQ,CAACpB,IAAI,CAAC,kBAAkBI,QAAQ,CAACqD,OAAO,CAAC,CAAC,CAAC,8BAA8B,CAAC;MAAC;MAAAhH,cAAA,GAAAoB,CAAA;MACnFyD,eAAe,CAACtB,IAAI,CAAC,kDAAkD,CAAC;IAC1E,CAAC;IAAA;IAAA;MAAAvD,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAED,IAAIuC,QAAQ,GAAG,GAAG,EAAE;MAAA;MAAA3D,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAClBuD,QAAQ,CAACpB,IAAI,CAAC,iBAAiBI,QAAQ,CAACqD,OAAO,CAAC,CAAC,CAAC,uCAAuC,CAAC;MAAC;MAAAhH,cAAA,GAAAoB,CAAA;MAC3FyD,eAAe,CAACtB,IAAI,CAAC,oDAAoD,CAAC;IAC5E,CAAC;IAAA;IAAA;MAAAvD,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAED,OAAO;MACL6F,SAAS,EAAE7D,OAAO,CAAC8D,EAAE;MACrBC,WAAW,EAAE/D,OAAO,CAACnC,IAAI;MACzB0C,QAAQ,EAAE,IAAI,CAACa,gBAAgB,CAACb,QAAQ,EAAEyB,OAAO,CAACX,iBAAiB,CAAC;MACpEe,gBAAgB,EAAE,IAAI,CAAChB,gBAAgB,CAACgB,gBAAgB,EAAEJ,OAAO,CAACX,iBAAiB,CAAC;MACpFkB,cAAc;MACdnC,YAAY,EAAE,IAAI,CAACgB,gBAAgB,CAAChB,YAAY,EAAE4B,OAAO,CAACX,iBAAiB,CAAC;MAC5EhB,SAAS,EAAE,IAAI,CAACe,gBAAgB,CAACf,SAAS,EAAE2B,OAAO,CAACX,iBAAiB,CAAC;MACtEsC,SAAS,EAAE,IAAI,CAACvC,gBAAgB,CAACuC,SAAS,EAAE3B,OAAO,CAACX,iBAAiB,CAAC;MACtEmB,cAAc;MACdC,OAAO;MACPC,cAAc;MACdnB,QAAQ;MACRE;KACD;EACH;EAEA;;;EAGQ,OAAOS,iBAAiBA,CAAClC,OAAoB;IAAA;IAAApD,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACnD;IAAI;IAAA,CAAApB,cAAA,GAAAsB,CAAA,UAAA8B,OAAO,CAAC6C,SAAS,KAAK,OAAO;IAAA;IAAA,CAAAjG,cAAA,GAAAsB,CAAA,UAAI8B,OAAO,CAACiD,QAAQ,GAAE;MAAA;MAAArG,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACrD,OAAO8C,IAAI,CAACkD,EAAE,GAAGlD,IAAI,CAACmD,GAAG,CAACjE,OAAO,CAACiD,QAAQ,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAC3D,CAAC,MAAM;MAAA;MAAArG,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA;MAAI;MAAA,CAAApB,cAAA,GAAAsB,CAAA,WAAA8B,OAAO,CAAC6C,SAAS,KAAK,aAAa;MAAA;MAAA,CAAAjG,cAAA,GAAAsB,CAAA,WAAI8B,OAAO,CAACmD,KAAK;MAAA;MAAA,CAAAvG,cAAA,GAAAsB,CAAA,WAAI8B,OAAO,CAACoD,MAAM,GAAE;QAAA;QAAAxG,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACjF,OAAQgC,OAAO,CAACmD,KAAK,GAAGnD,OAAO,CAACoD,MAAM,GAAI,GAAG,CAAC,CAAC;MACjD,CAAC;MAAA;MAAA;QAAAxG,cAAA,GAAAsB,CAAA;MAAA;IAAD;IAAC;IAAAtB,cAAA,GAAAoB,CAAA;IAED,MAAM,IAAIkG,KAAK,CAAC,qCAAqClE,OAAO,CAAC8D,EAAE,EAAE,CAAC;EACpE;EAEA;;;EAGQ,OAAOZ,2BAA2BA,CAACC,KAAa,EAAEC,MAAc;IAAA;IAAAxG,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACtE;IACA,OAAO,IAAI,GAAG8C,IAAI,CAACmD,GAAG,CAACd,KAAK,GAAGC,MAAM,EAAE,KAAK,CAAC,GAAGtC,IAAI,CAACmD,GAAG,CAACd,KAAK,GAAGC,MAAM,EAAE,IAAI,CAAC;EAChF;EAEA;;;EAGQ,OAAO1D,mBAAmBA,CAACyE,UAAuD;IAAA;IAAAvH,cAAA,GAAAqB,CAAA;IACxF,MAAM;MAAEmG,WAAW;MAAEC,kBAAkB;MAAEC;IAAQ,CAAE;IAAA;IAAA,CAAA1H,cAAA,GAAAoB,CAAA,QAAGmG,UAAU;IAEhE;IACA,MAAMI,eAAe;IAAA;IAAA,CAAA3H,cAAA,GAAAoB,CAAA,QAAG,KAAK,EAAC,CAAC;IAE/B;IACA,MAAMwG,SAAS;IAAA;IAAA,CAAA5H,cAAA,GAAAoB,CAAA,QAAG,CAAC,MAAM,GAAG,EAAE,KAAK,MAAM,GAAGoG,WAAW,CAAC;IAExD;IACA,MAAMK,aAAa;IAAA;IAAA,CAAA7H,cAAA,GAAAoB,CAAA,QAAGqG,kBAAkB,GAAG,KAAK;IAEhD;IACA,MAAMK,aAAa;IAAA;IAAA,CAAA9H,cAAA,GAAAoB,CAAA,QAAG8C,IAAI,CAAC6D,GAAG,CAAC,CAACL,QAAQ,GAAG,KAAK,CAAC;IAAC;IAAA1H,cAAA,GAAAoB,CAAA;IAElD,OAAOuG,eAAe,GAAGC,SAAS,GAAGC,aAAa,GAAGC,aAAa;EACpE;EAEA;;;EAGQ,OAAOrB,qBAAqBA,CAClC9C,QAAgB,EAChBD,MAAc,EACd2C,QAAgB,EAChBD,QAAgB,EAChBvD,UAAkB;IAAA;IAAA7C,cAAA,GAAAqB,CAAA;IAElB;IACA,MAAM2G,YAAY;IAAA;IAAA,CAAAhI,cAAA,GAAAoB,CAAA,QAAGa,mBAAA,CAAAgG,iBAAiB,CAAC,uBAAuB,CAAC,CAACtE,QAAQ,EAAED,MAAM,EAAE2C,QAAQ,EAAED,QAAQ,CAAC;IACrG,MAAM8B,YAAY;IAAA;IAAA,CAAAlI,cAAA,GAAAoB,CAAA,QAAGyB,UAAU,GAAG,KAAK;IAAC;IAAA7C,cAAA,GAAAoB,CAAA;IACxC,OAAO4G,YAAY,GAAGE,YAAY;EACpC;EAEA;;;EAGQ,OAAOxB,uBAAuBA,CAAC/C,QAAgB,EAAE0C,QAAgB,EAAExD,UAAkB;IAAA;IAAA7C,cAAA,GAAAqB,CAAA;IAC3F,MAAM8G,WAAW;IAAA;IAAA,CAAAnI,cAAA,GAAAoB,CAAA,QAAGuC,QAAQ,GAAG,EAAE,EAAC,CAAC;IACnC,MAAMyE,UAAU;IAAA;IAAA,CAAApI,cAAA,GAAAoB,CAAA,QAAGiF,QAAQ,GAAG,EAAE,EAAC,CAAC;IAClC,MAAMgC,kBAAkB;IAAA;IAAA,CAAArI,cAAA,GAAAoB,CAAA,QAAG,OAAO,EAAC,CAAC;IAAA;IAAApB,cAAA,GAAAoB,CAAA;IAEpC,OAAQ+G,WAAW,GAAGC,UAAU,GAAIC,kBAAkB;EACxD;EAEA;;;EAGQ,OAAO1B,uBAAuBA,CAAC2B,QAAgB,EAAElC,QAAgB,EAAEC,QAAgB;IAAA;IAAArG,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACzF;IACA,OAAOa,mBAAA,CAAAgG,iBAAiB,CAAC,yBAAyB,CAAC,CAACK,QAAQ,EAAElC,QAAQ,EAAEC,QAAQ,CAAC;EACnF;EAEA;;;EAGQ,OAAOzD,oBAAoBA,CAACL,MAA+B;IAAA;IAAAvC,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACjE;IAAI;IAAA,CAAApB,cAAA,GAAAsB,CAAA,YAACiB,MAAM,CAACC,QAAQ;IAAA;IAAA,CAAAxC,cAAA,GAAAsB,CAAA,WAAIiB,MAAM,CAACC,QAAQ,CAACkB,MAAM,KAAK,CAAC,GAAE;MAAA;MAAA1D,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACpD,MAAM,IAAIkG,KAAK,CAAC,0CAA0C,CAAC;IAC7D,CAAC;IAAA;IAAA;MAAAtH,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAED,KAAK,MAAMgC,OAAO,IAAIb,MAAM,CAACC,QAAQ,EAAE;MAAA;MAAAxC,cAAA,GAAAoB,CAAA;MACrC,IAAIgC,OAAO,CAACmC,OAAO,IAAI,CAAC,EAAE;QAAA;QAAAvF,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACxB,MAAM,IAAIkG,KAAK,CAAC,+BAA+BlE,OAAO,CAAC8D,EAAE,EAAE,CAAC;MAC9D,CAAC;MAAA;MAAA;QAAAlH,cAAA,GAAAsB,CAAA;MAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAED;MAAI;MAAA,CAAApB,cAAA,GAAAsB,CAAA,WAAA8B,OAAO,CAACnC,IAAI,KAAK,UAAU;MAAK;MAAA,CAAAjB,cAAA,GAAAsB,CAAA,YAAC8B,OAAO,CAACM,MAAM;MAAA;MAAA,CAAA1D,cAAA,GAAAsB,CAAA,WAAI8B,OAAO,CAACM,MAAM,IAAI,CAAC,EAAC,EAAE;QAAA;QAAA1D,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAC3E,MAAM,IAAIkG,KAAK,CAAC,oBAAoBlE,OAAO,CAAC8D,EAAE,4BAA4B,CAAC;MAC7E,CAAC;MAAA;MAAA;QAAAlH,cAAA,GAAAsB,CAAA;MAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAED;MAAI;MAAA,CAAApB,cAAA,GAAAsB,CAAA,WAAA8B,OAAO,CAACnC,IAAI,KAAK,SAAS;MAAA;MAAA,CAAAjB,cAAA,GAAAsB,CAAA,WAAI,CAAC8B,OAAO,CAACwD,aAAa,GAAE;QAAA;QAAA5G,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACxD,MAAM,IAAIkG,KAAK,CAAC,mBAAmBlE,OAAO,CAAC8D,EAAE,kCAAkC,CAAC;MAClF,CAAC;MAAA;MAAA;QAAAlH,cAAA,GAAAsB,CAAA;MAAA;IACH;EACF;EAEA;;;EAGQ,OAAOiD,qBAAqBA,CAACgE,IAOpC;IAAA;IAAAvI,cAAA,GAAAqB,CAAA;IAKC,MAAMsD,QAAQ;IAAA;IAAA,CAAA3E,cAAA,GAAAoB,CAAA,QAAa,EAAE;IAC7B,MAAMyD,eAAe;IAAA;IAAA,CAAA7E,cAAA,GAAAoB,CAAA,QAAa,EAAE;IAEpC,MAAMoH,cAAc;IAAA;IAAA,CAAAxI,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACqH,sBAAsB,CAACF,IAAI,CAAC9F,UAAU,CAAC;IACnE,MAAMiG,cAAc;IAAA;IAAA,CAAA1I,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACuH,sBAAsB,CAACJ,IAAI,CAAC9F,UAAU,CAAC;IAEnE;IACA,MAAMmG,iBAAiB;IAAA;IAAA,CAAA5I,cAAA,GAAAoB,CAAA;IAAG;IAAA,CAAApB,cAAA,GAAAsB,CAAA,WAAAiH,IAAI,CAACtE,WAAW,IAAIuE,cAAc,CAACrE,GAAG;IAAA;IAAA,CAAAnE,cAAA,GAAAsB,CAAA,WAAIiH,IAAI,CAACnE,WAAW,IAAIoE,cAAc,CAACnE,GAAG;IAAC;IAAArE,cAAA,GAAAoB,CAAA;IAC3G,IAAI,CAACwH,iBAAiB,EAAE;MAAA;MAAA5I,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACtBuD,QAAQ,CAACpB,IAAI,CAAC,4CAA4CiF,cAAc,CAACnE,GAAG,IAAImE,cAAc,CAACrE,GAAG,OAAO,CAAC;MAAC;MAAAnE,cAAA,GAAAoB,CAAA;MAC3GyD,eAAe,CAACtB,IAAI,CAAC,8CAA8C,CAAC;IACtE,CAAC;IAAA;IAAA;MAAAvD,cAAA,GAAAsB,CAAA;IAAA;IAED;IACA,MAAMuH,iBAAiB;IAAA;IAAA,CAAA7I,cAAA,GAAAoB,CAAA,QAAGmH,IAAI,CAAC3E,iBAAiB,IAAI8E,cAAc,CAACvE,GAAG;IAAC;IAAAnE,cAAA,GAAAoB,CAAA;IACvE,IAAI,CAACyH,iBAAiB,EAAE;MAAA;MAAA7I,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACtBuD,QAAQ,CAACpB,IAAI,CAAC,yBAAyBgF,IAAI,CAAC3E,iBAAiB,CAACoD,OAAO,CAAC,CAAC,CAAC,iCAAiC0B,cAAc,CAACvE,GAAG,SAAS,CAAC;MAAC;MAAAnE,cAAA,GAAAoB,CAAA;MACtIyD,eAAe,CAACtB,IAAI,CAAC,8EAA8E,CAAC;IACtG,CAAC;IAAA;IAAA;MAAAvD,cAAA,GAAAsB,CAAA;IAAA;IAED;IACA,MAAMwH,eAAe;IAAA;IAAA,CAAA9I,cAAA,GAAAoB,CAAA;IAAG;IAAA,CAAApB,cAAA,GAAAsB,CAAA,WAAAsH,iBAAiB;IAAA;IAAA,CAAA5I,cAAA,GAAAsB,CAAA,WAAIuH,iBAAiB;IAE9D;IAAA;IAAA7I,cAAA,GAAAoB,CAAA;IACA,IAAImH,IAAI,CAAC3E,iBAAiB,GAAG8E,cAAc,CAACK,WAAW,EAAE;MAAA;MAAA/I,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACvDyD,eAAe,CAACtB,IAAI,CAAC,yCAAyCmF,cAAc,CAACK,WAAW,+BAA+B,CAAC;IAC1H,CAAC;IAAA;IAAA;MAAA/I,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAED,IAAImH,IAAI,CAAC1E,eAAe,GAAG2E,cAAc,CAACO,WAAW,EAAE;MAAA;MAAA/I,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACrDyD,eAAe,CAACtB,IAAI,CAAC,4CAA4CiF,cAAc,CAACO,WAAW,wBAAwB,CAAC;IACtH,CAAC;IAAA;IAAA;MAAA/I,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAED,OAAO;MACLuD,QAAQ;MACRE,eAAe;MACfE,UAAU,EAAE;QAAE6D,iBAAiB;QAAEC,iBAAiB;QAAEC;MAAe;KACpE;EACH;EAEA;;;EAGQ,OAAOtE,gBAAgBA,CAACwE,KAAa,EAAEC,SAAiB;IAAA;IAAAjJ,cAAA,GAAAqB,CAAA;IAC9D,MAAM6H,MAAM;IAAA;IAAA,CAAAlJ,cAAA,GAAAoB,CAAA,SAAG8C,IAAI,CAACmD,GAAG,CAAC,EAAE,EAAE4B,SAAS,CAAC;IAAC;IAAAjJ,cAAA,GAAAoB,CAAA;IACvC,OAAO8C,IAAI,CAACiF,KAAK,CAACH,KAAK,GAAGE,MAAM,CAAC,GAAGA,MAAM;EAC5C;EAEA;;;EAGO,OAAOE,eAAeA,CAAC3G,UAAkB;IAAA;IAAAzC,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAI9C,OAAO;MACLuC,QAAQ;MAAE;MAAA,CAAA3D,cAAA,GAAAsB,CAAA,eAAI,CAACmH,sBAAsB,CAAChG,UAAU,CAAC;MAAA;MAAA,CAAAzC,cAAA,GAAAsB,CAAA,WAAI,IAAI,CAACmH,sBAAsB,CAACY,MAAM;MACvFC,QAAQ;MAAE;MAAA,CAAAtJ,cAAA,GAAAsB,CAAA,eAAI,CAACqH,sBAAsB,CAAClG,UAAU,CAAC;MAAA;MAAA,CAAAzC,cAAA,GAAAsB,CAAA,WAAI,IAAI,CAACqH,sBAAsB,CAACU,MAAM;KACxF;EACH;EAEA;;;EAGO,OAAOE,+BAA+BA,CAAChH,MAA+B;IAAA;IAAAvC,cAAA,GAAAqB,CAAA;IAC3E,MAAM;MAAEmB,QAAQ;MAAEC,UAAU;MAAEC,gBAAgB;MAAEC;IAAkB,CAAE;IAAA;IAAA,CAAA3C,cAAA,GAAAoB,CAAA,SAAGmB,MAAM;IAE7E;IAAA;IAAAvC,cAAA,GAAAoB,CAAA;IACA,IAAI,CAACwB,oBAAoB,CAACL,MAAM,CAAC;IAEjC,MAAMiH,OAAO;IAAA;IAAA,CAAAxJ,cAAA,GAAAoB,CAAA,SAAoB,EAAE;IACnC,IAAIwC,iBAAiB;IAAA;IAAA,CAAA5D,cAAA,GAAAoB,CAAA,SAAG,CAAC;IACzB,IAAI4B,iBAAiB;IAAA;IAAA,CAAAhD,cAAA,GAAAoB,CAAA,SAAG,CAAC;IACzB,IAAIqI,gBAAgB;IAAA;IAAA,CAAAzJ,cAAA,GAAAoB,CAAA,SAAG,CAAC;IACxB,IAAIsI,kBAAkB;IAAA;IAAA,CAAA1J,cAAA,GAAAoB,CAAA,SAAG,CAAC;IAC1B,MAAMuD,QAAQ;IAAA;IAAA,CAAA3E,cAAA,GAAAoB,CAAA,SAAa,EAAE;IAC7B,MAAMuI,KAAK;IAAA;IAAA,CAAA3J,cAAA,GAAAoB,CAAA,SAAa,EAAE;IAE1B;IAAA;IAAApB,cAAA,GAAAoB,CAAA;IACA,KAAK,MAAMgC,OAAO,IAAIZ,QAAQ,EAAE;MAC9B,MAAMa,aAAa;MAAA;MAAA,CAAArD,cAAA,GAAAoB,CAAA,SAAG,IAAI,CAACwI,gCAAgC,CAACxG,OAAO,EAAEV,gBAAgB,CAAC;MAAC;MAAA1C,cAAA,GAAAoB,CAAA;MAEvFoI,OAAO,CAACjG,IAAI,CAACF,aAAa,CAAC;MAAC;MAAArD,cAAA,GAAAoB,CAAA;MAC5BwC,iBAAiB,IAAIP,aAAa,CAACyD,YAAY;MAAC;MAAA9G,cAAA,GAAAoB,CAAA;MAEhD,IAAIiC,aAAa,CAACpC,IAAI,KAAK,UAAU,EAAE;QAAA;QAAAjB,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACrC4B,iBAAiB,IAAIK,aAAa,CAACyD,YAAY;MACjD,CAAC,MAAM;QAAA;QAAA9G,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAAA,IAAIiC,aAAa,CAACpC,IAAI,KAAK,SAAS,EAAE;UAAA;UAAAjB,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAoB,CAAA;UAC3CqI,gBAAgB,IAAIpG,aAAa,CAACyD,YAAY;QAChD,CAAC;QAAA;QAAA;UAAA9G,cAAA,GAAAsB,CAAA;QAAA;MAAD;MAEA;MAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACA,IAAIgC,OAAO,CAACyG,SAAS,KAAK1I,SAAS,EAAE;QAAA;QAAAnB,cAAA,GAAAsB,CAAA;QACnC,MAAMwI,eAAe;QAAA;QAAA,CAAA9J,cAAA,GAAAoB,CAAA,SAAG,IAAI,CAAC2I,0BAA0B,CAAC3G,OAAO,CAAC;QAAC;QAAApD,cAAA,GAAAoB,CAAA;QACjEsI,kBAAkB,IAAII,eAAe,CAACE,cAAc;QAAC;QAAAhK,cAAA,GAAAoB,CAAA;QACrD,IAAI0I,eAAe,CAACnF,QAAQ,CAACjB,MAAM,GAAG,CAAC,EAAE;UAAA;UAAA1D,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAoB,CAAA;UACvCuD,QAAQ,CAACpB,IAAI,CAAC,GAAGuG,eAAe,CAACnF,QAAQ,CAAC;QAC5C,CAAC;QAAA;QAAA;UAAA3E,cAAA,GAAAsB,CAAA;QAAA;MACH,CAAC;MAAA;MAAA;QAAAtB,cAAA,GAAAsB,CAAA;MAAA;MAED;MAAAtB,cAAA,GAAAoB,CAAA;MACA,IAAIiC,aAAa,CAACsB,QAAQ,EAAE;QAAA;QAAA3E,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAC1BuD,QAAQ,CAACpB,IAAI,CAAC,GAAGF,aAAa,CAACsB,QAAQ,CAAC;MAC1C,CAAC;MAAA;MAAA;QAAA3E,cAAA,GAAAsB,CAAA;MAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACD,IAAIiC,aAAa,CAACsG,KAAK,EAAE;QAAA;QAAA3J,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACvBuI,KAAK,CAACpG,IAAI,CAAC,GAAGF,aAAa,CAACsG,KAAK,CAAC;MACpC,CAAC;MAAA;MAAA;QAAA3J,cAAA,GAAAsB,CAAA;MAAA;IACH;IAEA;IACA,MAAM2I,YAAY;IAAA;IAAA,CAAAjK,cAAA,GAAAoB,CAAA,SAAG8C,IAAI,CAACC,GAAG,CAAC,GAAG3B,QAAQ,CAAC0H,GAAG,CAAC9I,CAAC,IAAI;MAAA;MAAApB,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAAA,OAAAA,CAAC,CAACmE,OAAO;IAAP,CAAO,CAAC,CAAC;IAC9D,MAAM1B,eAAe;IAAA;IAAA,CAAA7D,cAAA,GAAAoB,CAAA,SAAG,IAAI,CAAC+I,wBAAwB,CAAC3H,QAAQ,CAAC;IAE/D;IACA,MAAM4H,WAAW;IAAA;IAAA,CAAApK,cAAA,GAAAoB,CAAA,SAAG,IAAI,CAACiJ,+BAA+B,CAAC3H,gBAAgB,CAAC;IAAC;IAAA1C,cAAA,GAAAoB,CAAA;IAC3EuD,QAAQ,CAACpB,IAAI,CAAC,GAAG6G,WAAW,CAAC;IAE7B;IACA,MAAME,QAAQ;IAAA;IAAA,CAAAtK,cAAA,GAAAoB,CAAA,SAAG,IAAI,CAACmJ,gCAAgC,CACpDf,OAAO,EACP5F,iBAAiB,EACjBqG,YAAY,EACZxH,UAAU,EACVC,gBAAgB,CACjB;IAAC;IAAA1C,cAAA,GAAAoB,CAAA;IAEF,OAAO;MACLwC,iBAAiB,EAAE,IAAI,CAACY,gBAAgB,CAACZ,iBAAiB,GAAG8F,kBAAkB,EAAE,CAAC,CAAC;MACnFlG,YAAY,EAAE,IAAI,CAACgB,gBAAgB,CAACxB,iBAAiB,EAAE,CAAC,CAAC;MACzDwH,WAAW,EAAE,IAAI,CAAChG,gBAAgB,CAACiF,gBAAgB,EAAE,CAAC,CAAC;MACvDgB,aAAa,EAAE,IAAI,CAACjG,gBAAgB,CAACkF,kBAAkB,EAAE,CAAC,CAAC;MAC3DlH,QAAQ,EAAEgH,OAAO;MACjBkB,aAAa,EAAE;QACbT,YAAY;QACZpG,eAAe,EAAE,IAAI,CAACW,gBAAgB,CAACX,eAAe,EAAE,CAAC,CAAC;QAC1D8G,kBAAkB,EAAE,IAAI,CAACnG,gBAAgB,CAAExB,iBAAiB,GAAGY,iBAAiB,GAAI,GAAG,EAAE,CAAC,CAAC;QAC3FgH,iBAAiB,EAAE,IAAI,CAACpG,gBAAgB,CAAEiF,gBAAgB,GAAG7F,iBAAiB,GAAI,GAAG,EAAE,CAAC,CAAC;QACzFiH,mBAAmB,EAAE,IAAI,CAACrG,gBAAgB,CAAEkF,kBAAkB,GAAG9F,iBAAiB,GAAI,GAAG,EAAE,CAAC,CAAC;QAC7FkH,gBAAgB,EAAE,IAAI,CAACC,yBAAyB,CAAC/H,iBAAiB,EAAEyG,gBAAgB;OACrF;MACDa,QAAQ;MACR3F,QAAQ;MACRgF,KAAK;MACL3E,iBAAiB,EAAE,yCAAyC;MAC5DgG,SAAS,EAAE,IAAI7F,IAAI,EAAE,CAAC8F,WAAW;KAClC;EACH;EAEA;;;EAGQ,OAAOrB,gCAAgCA,CAC7CxG,OAAoB,EACpBV,gBAAqB;IAAA;IAAA1C,cAAA,GAAAqB,CAAA;IAErB,MAAMsD,QAAQ;IAAA;IAAA,CAAA3E,cAAA,GAAAoB,CAAA,SAAa,EAAE;IAC7B,MAAMuI,KAAK;IAAA;IAAA,CAAA3J,cAAA,GAAAoB,CAAA,SAAa,EAAE;IAE1B;IACA,MAAM8J,aAAa;IAAA;IAAA,CAAAlL,cAAA,GAAAoB,CAAA,SAAkB;MACnCoG,WAAW;MAAE;MAAA,CAAAxH,cAAA,GAAAsB,CAAA,WAAA8B,OAAO,CAACoE,WAAW;MAAA;MAAA,CAAAxH,cAAA,GAAAsB,CAAA,WAAIoB,gBAAgB,CAAC8E,WAAW;MAAA;MAAA,CAAAxH,cAAA,GAAAsB,CAAA,WAAI,EAAE;MACtEgI,QAAQ;MAAE;MAAA,CAAAtJ,cAAA,GAAAsB,CAAA,WAAA8B,OAAO,CAACkG,QAAQ;MAAA;MAAA,CAAAtJ,cAAA,GAAAsB,CAAA,WAAIoB,gBAAgB,CAAC+E,kBAAkB;MACjEC,QAAQ;MAAE;MAAA,CAAA1H,cAAA,GAAAsB,CAAA,WAAA8B,OAAO,CAACyG,SAAS;MAAA;MAAA,CAAA7J,cAAA,GAAAsB,CAAA,WAAIoB,gBAAgB,CAACgF,QAAQ;MAAA;MAAA,CAAA1H,cAAA,GAAAsB,CAAA,WAAI,CAAC;MAC7D6J,QAAQ;MAAE;MAAA,CAAAnL,cAAA,GAAAsB,CAAA,WAAA8B,OAAO,CAAC+H,QAAQ;MAAA;MAAA,CAAAnL,cAAA,GAAAsB,CAAA,WAAIoB,gBAAgB,CAACyI,QAAQ;MAAA;MAAA,CAAAnL,cAAA,GAAAsB,CAAA,WAAI,EAAE;KAC9D;IAED;IACA,MAAM8J,QAAQ;IAAA;IAAA,CAAApL,cAAA,GAAAoB,CAAA,SAAGgB,yBAAA,CAAAiJ,uBAAuB,CAACC,sBAAsB,CAACJ,aAAa,CAAC;IAAC;IAAAlL,cAAA,GAAAoB,CAAA;IAC/EuD,QAAQ,CAACpB,IAAI,CAAC,GAAG6H,QAAQ,CAACzG,QAAQ,CAAC;IAAC;IAAA3E,cAAA,GAAAoB,CAAA;IACpCuI,KAAK,CAACpG,IAAI,CAAC,GAAG6H,QAAQ,CAACzB,KAAK,CAAC;IAAC;IAAA3J,cAAA,GAAAoB,CAAA;IAE9B;IAAI;IAAA,CAAApB,cAAA,GAAAsB,CAAA,WAAA8B,OAAO,CAACnC,IAAI,KAAK,UAAU;IAAA;IAAA,CAAAjB,cAAA,GAAAsB,CAAA,WAAI8B,OAAO,CAACM,MAAM,GAAE;MAAA;MAAA1D,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACjD;MACA,OAAO,IAAI,CAACmK,6BAA6B,CAACnI,OAAO,EAAEgI,QAAQ,EAAEzG,QAAQ,EAAEgF,KAAK,CAAC;IAC/E,CAAC,MAAM;MAAA;MAAA3J,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA;MAAI;MAAA,CAAApB,cAAA,GAAAsB,CAAA,WAAA8B,OAAO,CAACnC,IAAI,KAAK,SAAS;MAAA;MAAA,CAAAjB,cAAA,GAAAsB,CAAA,WAAI8B,OAAO,CAACwD,aAAa,GAAE;QAAA;QAAA5G,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAC9D;QACA,OAAO,IAAI,CAACoK,4BAA4B,CAACpI,OAAO,EAAEgI,QAAQ,EAAEzG,QAAQ,EAAEgF,KAAK,CAAC;MAC9E,CAAC;MAAA;MAAA;QAAA3J,cAAA,GAAAsB,CAAA;MAAA;IAAD;IAEA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IACA,OAAO;MACL6F,SAAS,EAAE7D,OAAO,CAAC8D,EAAE;MACrBjG,IAAI,EAAE,SAAS;MACf6F,YAAY,EAAE,CAAC;MACfnD,QAAQ,EAAE,CAAC;MACXgB,QAAQ,EAAE,CAAC,sBAAsB,CAAC;MAClCgF,KAAK,EAAE;KACR;EACH;EAEA;;;EAGQ,OAAO4B,6BAA6BA,CAC1CnI,OAAoB,EACpBgI,QAAuB,EACvBzG,QAAkB,EAClBgF,KAAe;IAAA;IAAA3J,cAAA,GAAAqB,CAAA;IAEf;IACA,MAAMoK,YAAY;IAAA;IAAA,CAAAzL,cAAA,GAAAoB,CAAA,SAAGgB,yBAAA,CAAAiJ,uBAAuB,CAACK,4BAA4B,CACvEtI,OAAO,CAACgD,QAAQ,EAChBhD,OAAO,CAACuI,WAAW,EACnBvI,OAAO,CAACwI,gBAAgB,CACzB;IAAC;IAAA5L,cAAA,GAAAoB,CAAA;IAEFuD,QAAQ,CAACpB,IAAI,CAAC,GAAGkI,YAAY,CAAC9G,QAAQ,CAAC;IAAC;IAAA3E,cAAA,GAAAoB,CAAA;IACxCuI,KAAK,CAACpG,IAAI,CAAC,GAAGkI,YAAY,CAAC9B,KAAK,CAAC;IAEjC;IACA,MAAMtE,IAAI;IAAA;IAAA,CAAArF,cAAA,GAAAoB,CAAA,SAAGgC,OAAO,CAAC6C,SAAS,KAAK,OAAO;IAAA;IAAA,CAAAjG,cAAA,GAAAsB,CAAA,WACtC4C,IAAI,CAACkD,EAAE,GAAGlD,IAAI,CAACmD,GAAG,CAAC;IAAC;IAAA,CAAArH,cAAA,GAAAsB,CAAA,WAAA8B,OAAO,CAACiD,QAAQ;IAAA;IAAA,CAAArG,cAAA,GAAAsB,CAAA,WAAI,EAAE,KAAI,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC;IAAA;IAAA,CAAAtB,cAAA,GAAAsB,CAAA,WACvD;IAAC;IAAA,CAAAtB,cAAA,GAAAsB,CAAA,WAAA8B,OAAO,CAACmD,KAAK;IAAA;IAAA,CAAAvG,cAAA,GAAAsB,CAAA,WAAI,EAAE;IAAK;IAAA,CAAAtB,cAAA,GAAAsB,CAAA,WAAA8B,OAAO,CAACoD,MAAM;IAAA;IAAA,CAAAxG,cAAA,GAAAsB,CAAA,WAAI,EAAE,EAAC,GAAI,GAAG;IAC1D,MAAMqC,QAAQ;IAAA;IAAA,CAAA3D,cAAA,GAAAoB,CAAA,SAAGgC,OAAO,CAACmC,OAAO,GAAGF,IAAI;IAEvC;IACA,MAAMgB,QAAQ;IAAA;IAAA,CAAArG,cAAA,GAAAoB,CAAA,SAAGgC,OAAO,CAAC6C,SAAS,KAAK,OAAO;IAAA;IAAA,CAAAjG,cAAA,GAAAsB,CAAA;IAC1C;IAAA,CAAAtB,cAAA,GAAAsB,CAAA,WAAA8B,OAAO,CAACiD,QAAQ;IAAA;IAAA,CAAArG,cAAA,GAAAsB,CAAA,WAAI,EAAE;IAAA;IAAA,CAAAtB,cAAA,GAAAsB,CAAA,WACtBW,mBAAA,CAAAgG,iBAAiB,CAAC3B,2BAA2B;IAAC;IAAA,CAAAtG,cAAA,GAAAsB,CAAA,WAAA8B,OAAO,CAACmD,KAAK;IAAA;IAAA,CAAAvG,cAAA,GAAAsB,CAAA,WAAI,EAAE;IAAE;IAAA,CAAAtB,cAAA,GAAAsB,CAAA,WAAA8B,OAAO,CAACoD,MAAM;IAAA;IAAA,CAAAxG,cAAA,GAAAsB,CAAA,WAAI,EAAE,EAAC;IAE5F;IACA,MAAMwF,YAAY;IAAA;IAAA,CAAA9G,cAAA,GAAAoB,CAAA,SAAG,IAAI,CAACyK,8BAA8B,CACtDlI,QAAQ;IACR;IAAA,CAAA3D,cAAA,GAAAsB,CAAA,WAAA8B,OAAO,CAACM,MAAM;IAAA;IAAA,CAAA1D,cAAA,GAAAsB,CAAA,WAAI,CAAC,GACnB+E,QAAQ,EACRoF,YAAY,CAACK,SAAS,EACtBV,QAAQ,CACT;IAAC;IAAApL,cAAA,GAAAoB,CAAA;IAEF,OAAO;MACL6F,SAAS,EAAE7D,OAAO,CAAC8D,EAAE;MACrBjG,IAAI,EAAE,UAAU;MAChB6F,YAAY,EAAE,IAAI,CAACtC,gBAAgB,CAACsC,YAAY,EAAE,CAAC,CAAC;MACpDnD,QAAQ,EAAE,IAAI,CAACa,gBAAgB,CAACb,QAAQ,EAAE,CAAC,CAAC;MAC5CoI,kBAAkB,EAAE,IAAI,CAACvH,gBAAgB,CAAC6B,QAAQ,EAAE,CAAC,CAAC;MACtD2F,iBAAiB,EAAEP,YAAY,CAACK,SAAS;MACzCjJ,UAAU,EAAEuI,QAAQ,CAACa,OAAO;MAC5BC,iBAAiB,EAAEd,QAAQ,CAACc,iBAAiB;MAC7CvH,QAAQ;MACRgF;KACD;EACH;EAEA;;;EAGQ,OAAO6B,4BAA4BA,CACzCpI,OAAoB,EACpBgI,QAAuB,EACvBzG,QAAkB,EAClBgF,KAAe;IAAA;IAAA3J,cAAA,GAAAqB,CAAA;IAEf;IACA,MAAMgE,IAAI;IAAA;IAAA,CAAArF,cAAA,GAAAoB,CAAA,SAAGgC,OAAO,CAAC6C,SAAS,KAAK,OAAO;IAAA;IAAA,CAAAjG,cAAA,GAAAsB,CAAA,WACtC4C,IAAI,CAACkD,EAAE,GAAGlD,IAAI,CAACmD,GAAG,CAAC;IAAC;IAAA,CAAArH,cAAA,GAAAsB,CAAA,WAAA8B,OAAO,CAACiD,QAAQ;IAAA;IAAA,CAAArG,cAAA,GAAAsB,CAAA,WAAI,EAAE,KAAI,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC;IAAA;IAAA,CAAAtB,cAAA,GAAAsB,CAAA,WACvD;IAAC;IAAA,CAAAtB,cAAA,GAAAsB,CAAA,WAAA8B,OAAO,CAACmD,KAAK;IAAA;IAAA,CAAAvG,cAAA,GAAAsB,CAAA,WAAI,EAAE;IAAK;IAAA,CAAAtB,cAAA,GAAAsB,CAAA,WAAA8B,OAAO,CAACoD,MAAM;IAAA;IAAA,CAAAxG,cAAA,GAAAsB,CAAA,WAAI,EAAE,EAAC,GAAI,GAAG;IAC1D,MAAMqC,QAAQ;IAAA;IAAA,CAAA3D,cAAA,GAAAoB,CAAA,SAAGgC,OAAO,CAACmC,OAAO,GAAGF,IAAI;IAEvC;IACA,MAAM8G,QAAQ;IAAA;IAAA,CAAAnM,cAAA,GAAAoB,CAAA,SAA2B;MACvCuC,QAAQ;MACRuH,aAAa,EAAE;QACb1D,WAAW;QAAE;QAAA,CAAAxH,cAAA,GAAAsB,CAAA,WAAA8B,OAAO,CAACoE,WAAW;QAAA;QAAA,CAAAxH,cAAA,GAAAsB,CAAA,WAAI,EAAE;QACtCgI,QAAQ,EAAElG,OAAO,CAACkG,QAAQ;QAC1B5B,QAAQ;QAAE;QAAA,CAAA1H,cAAA,GAAAsB,CAAA,WAAA8B,OAAO,CAACyG,SAAS;QAAA;QAAA,CAAA7J,cAAA,GAAAsB,CAAA,WAAI,CAAC;QAChC6J,QAAQ;QAAE;QAAA,CAAAnL,cAAA,GAAAsB,CAAA,WAAA8B,OAAO,CAAC+H,QAAQ;QAAA;QAAA,CAAAnL,cAAA,GAAAsB,CAAA,WAAI,EAAE;;KAEnC;IAED,MAAM8K,QAAQ;IAAA;IAAA,CAAApM,cAAA,GAAAoB,CAAA,SAAGgB,yBAAA,CAAAiJ,uBAAuB,CAAC3F,yBAAyB,CAACyG,QAAQ,CAAC;IAAC;IAAAnM,cAAA,GAAAoB,CAAA;IAC7EuD,QAAQ,CAACpB,IAAI,CAAC,GAAG6I,QAAQ,CAACzH,QAAQ,CAAC;IAEnC;IACA,MAAM0H,aAAa;IAAA;IAAA,CAAArM,cAAA,GAAAoB,CAAA,SAAGe,uBAAA,CAAAsD,qBAAqB,CAACoB,oBAAoB,CAC9DzD,OAAO,CAACwD,aAAc,EACtBjD,QAAQ,EACRyH,QAAQ,CAACa,OAAO,CACjB;IAED;IACA,MAAMK,qBAAqB;IAAA;IAAA,CAAAtM,cAAA,GAAAoB,CAAA,SAAGiL,aAAa,CAACvF,YAAY,IACrDsF,QAAQ,CAAC5G,gBAAgB,GAAGtB,IAAI,CAACmD,GAAG,CAAC1D,QAAQ,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC;IAAC;IAAA3D,cAAA,GAAAoB,CAAA;IAE7D,OAAO;MACL6F,SAAS,EAAE7D,OAAO,CAAC8D,EAAE;MACrBjG,IAAI,EAAE,SAAS;MACf6F,YAAY,EAAE,IAAI,CAACtC,gBAAgB,CAAC8H,qBAAqB,EAAE,CAAC,CAAC;MAC7D3I,QAAQ,EAAE,IAAI,CAACa,gBAAgB,CAACb,QAAQ,EAAE,CAAC,CAAC;MAC5CkC,OAAO,EAAEwG,aAAa,CAACxG,OAAO;MAC9BL,gBAAgB,EAAE4G,QAAQ,CAAC5G,gBAAgB;MAC3C3C,UAAU,EAAEuI,QAAQ,CAACa,OAAO;MAC5BC,iBAAiB,EAAEE,QAAQ,CAACF,iBAAiB;MAC7CvH,QAAQ;MACRgF;KACD;EACH;EAEA;;;EAGQ,OAAOI,0BAA0BA,CAAC3G,OAAoB;IAAA;IAAApD,cAAA,GAAAqB,CAAA;IAI5D,MAAMsD,QAAQ;IAAA;IAAA,CAAA3E,cAAA,GAAAoB,CAAA,SAAa,EAAE;IAAC;IAAApB,cAAA,GAAAoB,CAAA;IAE9B,IAAI,CAACgC,OAAO,CAACyG,SAAS,EAAE;MAAA;MAAA7J,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACtB,OAAO;QAAE4I,cAAc,EAAE,CAAC;QAAErF;MAAQ,CAAE;IACxC,CAAC;IAAA;IAAA;MAAA3E,cAAA,GAAAsB,CAAA;IAAA;IAED;IACA,MAAMiL,gBAAgB;IAAA;IAAA,CAAAvM,cAAA,GAAAoB,CAAA,SAAGgB,yBAAA,CAAAiJ,uBAAuB,CAACmB,yBAAyB,CAACpJ,OAAO,CAACyG,SAAS,CAAC;IAAC;IAAA7J,cAAA,GAAAoB,CAAA;IAC9FuD,QAAQ,CAACpB,IAAI,CAAC,GAAGgJ,gBAAgB,CAAC5H,QAAQ,CAAC;IAE3C;IACA;IACA,MAAM9B,UAAU;IAAA;IAAA,CAAA7C,cAAA,GAAAoB,CAAA,SAAG,KAAK,EAAC,CAAC;IAC1B,MAAM4I,cAAc;IAAA;IAAA,CAAAhK,cAAA,GAAAoB,CAAA,SAAIyB,UAAU,GAAGO,OAAO,CAACyG,SAAS,GAAI,GAAG,EAAC,CAAC;IAAA;IAAA7J,cAAA,GAAAoB,CAAA;IAE/D,OAAO;MACL4I,cAAc,EAAEA,cAAc,GAAGuC,gBAAgB,CAACrE,YAAY;MAC9DvD;KACD;EACH;EAEA;;;EAGQ,OAAOwF,wBAAwBA,CAAC3H,QAAuB;IAAA;IAAAxC,cAAA,GAAAqB,CAAA;IAC7D,IAAIoL,aAAa;IAAA;IAAA,CAAAzM,cAAA,GAAAoB,CAAA,SAAG,CAAC;IACrB,IAAIsL,KAAK;IAAA;IAAA,CAAA1M,cAAA,GAAAoB,CAAA,SAAG,CAAC;IAAC;IAAApB,cAAA,GAAAoB,CAAA;IAEd,KAAK,MAAMgC,OAAO,IAAIZ,QAAQ,EAAE;MAC9B,MAAM6C,IAAI;MAAA;MAAA,CAAArF,cAAA,GAAAoB,CAAA,SAAGgC,OAAO,CAAC6C,SAAS,KAAK,OAAO;MAAA;MAAA,CAAAjG,cAAA,GAAAsB,CAAA,WACtC4C,IAAI,CAACkD,EAAE,GAAGlD,IAAI,CAACmD,GAAG,CAAC;MAAC;MAAA,CAAArH,cAAA,GAAAsB,CAAA,WAAA8B,OAAO,CAACiD,QAAQ;MAAA;MAAA,CAAArG,cAAA,GAAAsB,CAAA,WAAI,EAAE,KAAI,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC;MAAA;MAAA,CAAAtB,cAAA,GAAAsB,CAAA,WACvD;MAAC;MAAA,CAAAtB,cAAA,GAAAsB,CAAA,WAAA8B,OAAO,CAACmD,KAAK;MAAA;MAAA,CAAAvG,cAAA,GAAAsB,CAAA,WAAI,EAAE;MAAK;MAAA,CAAAtB,cAAA,GAAAsB,CAAA,WAAA8B,OAAO,CAACoD,MAAM;MAAA;MAAA,CAAAxG,cAAA,GAAAsB,CAAA,WAAI,EAAE,EAAC,GAAI,GAAG;MAE1D,MAAMqC,QAAQ;MAAA;MAAA,CAAA3D,cAAA,GAAAoB,CAAA,SAAGgC,OAAO,CAACmC,OAAO,GAAGF,IAAI;MAAC;MAAArF,cAAA,GAAAoB,CAAA;MACxCqL,aAAa,IAAI9I,QAAQ;MAAC;MAAA3D,cAAA,GAAAoB,CAAA;MAC1BsL,KAAK,EAAE;IACT;IAAC;IAAA1M,cAAA,GAAAoB,CAAA;IAED,OAAOsL,KAAK,GAAG,CAAC;IAAA;IAAA,CAAA1M,cAAA,GAAAsB,CAAA,WAAGmL,aAAa,GAAGC,KAAK;IAAA;IAAA,CAAA1M,cAAA,GAAAsB,CAAA,WAAG,CAAC;EAC9C;EAEA;;;EAGQ,OAAO+I,+BAA+BA,CAAC3H,gBAAqB;IAAA;IAAA1C,cAAA,GAAAqB,CAAA;IAClE,MAAMsD,QAAQ;IAAA;IAAA,CAAA3E,cAAA,GAAAoB,CAAA,SAAa,EAAE;IAAC;IAAApB,cAAA,GAAAoB,CAAA;IAE9B;IAAI;IAAA,CAAApB,cAAA,GAAAsB,CAAA,WAAAoB,gBAAgB,CAAC8E,WAAW,GAAG,EAAE;IAAA;IAAA,CAAAxH,cAAA,GAAAsB,CAAA,WAAIoB,gBAAgB,CAAC8E,WAAW,GAAG,GAAG,GAAE;MAAA;MAAAxH,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAC3EuD,QAAQ,CAACpB,IAAI,CAAC,eAAeb,gBAAgB,CAAC8E,WAAW,iCAAiC,CAAC;IAC7F,CAAC;IAAA;IAAA;MAAAxH,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAED;IAAI;IAAA,CAAApB,cAAA,GAAAsB,CAAA,WAAAoB,gBAAgB,CAACgF,QAAQ;IAAA;IAAA,CAAA1H,cAAA,GAAAsB,CAAA,WAAIoB,gBAAgB,CAACgF,QAAQ,GAAG,IAAI,GAAE;MAAA;MAAA1H,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACjEuD,QAAQ,CAACpB,IAAI,CAAC,kBAAkBb,gBAAgB,CAACgF,QAAQ,mCAAmC,CAAC;IAC/F,CAAC;IAAA;IAAA;MAAA1H,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAED;IAAI;IAAA,CAAApB,cAAA,GAAAsB,CAAA,WAAAoB,gBAAgB,CAACyI,QAAQ;IAAA;IAAA,CAAAnL,cAAA,GAAAsB,CAAA,WAAIoB,gBAAgB,CAACyI,QAAQ,GAAG,EAAE,GAAE;MAAA;MAAAnL,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAC/DuD,QAAQ,CAACpB,IAAI,CAAC,kBAAkBb,gBAAgB,CAACyI,QAAQ,8BAA8B,CAAC;IAC1F,CAAC;IAAA;IAAA;MAAAnL,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAED,OAAOuD,QAAQ;EACjB;EAEA;;;EAGQ,OAAO4F,gCAAgCA,CAC7Cf,OAAwB,EACxB5F,iBAAyB,EACzBqG,YAAoB,EACpBxH,UAAkB,EAClBC,gBAAqB;IAAA;IAAA1C,cAAA,GAAAqB,CAAA;IAErB,MAAMsD,QAAQ;IAAA;IAAA,CAAA3E,cAAA,GAAAoB,CAAA,SAAa,EAAE;IAC7B,MAAMyD,eAAe;IAAA;IAAA,CAAA7E,cAAA,GAAAoB,CAAA,SAAa,EAAE;IAEpC;IACA,MAAMuL,MAAM;IAAA;IAAA,CAAA3M,cAAA,GAAAoB,CAAA,SAAG,IAAI,CAACgI,eAAe,CAAC3G,UAAU,CAAC;IAC/C,MAAMoB,eAAe;IAAA;IAAA,CAAA7D,cAAA,GAAAoB,CAAA,SAAGoI,OAAO,CAAC1F,MAAM,CAAC,CAACC,GAAG,EAAE6I,CAAC,KAAK;MAAA;MAAA5M,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAAA,OAAA2C,GAAG;MAAI;MAAA,CAAA/D,cAAA,GAAAsB,CAAA,WAAAsL,CAAC,CAACjJ,QAAQ;MAAA;MAAA,CAAA3D,cAAA,GAAAsB,CAAA,WAAI,CAAC,EAAC;IAAD,CAAC,EAAE,CAAC,CAAC,GAAGkI,OAAO,CAAC9F,MAAM;IAE/F;IACA,MAAMkF,iBAAiB;IAAA;IAAA,CAAA5I,cAAA,GAAAoB,CAAA;IAAG;IAAA,CAAApB,cAAA,GAAAsB,CAAA,WAAAuC,eAAe,IAAI8I,MAAM,CAAChJ,QAAQ,CAACU,GAAG;IAAA;IAAA,CAAArE,cAAA,GAAAsB,CAAA,WACvCuC,eAAe,IAAI8I,MAAM,CAAChJ,QAAQ,CAACQ,GAAG;IAAC;IAAAnE,cAAA,GAAAoB,CAAA;IAEhE,IAAI,CAACwH,iBAAiB,EAAE;MAAA;MAAA5I,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACtBuD,QAAQ,CAACpB,IAAI,CAAC,oBAAoBM,eAAe,CAACmD,OAAO,CAAC,CAAC,CAAC,mCAAmC,CAAC;IAClG,CAAC;IAAA;IAAA;MAAAhH,cAAA,GAAAsB,CAAA;IAAA;IAED;IACA,MAAMuH,iBAAiB;IAAA;IAAA,CAAA7I,cAAA,GAAAoB,CAAA,SAAGwC,iBAAiB,IAAI+I,MAAM,CAACrD,QAAQ,CAACnF,GAAG;IAAC;IAAAnE,cAAA,GAAAoB,CAAA;IAEnE,IAAI,CAACyH,iBAAiB,EAAE;MAAA;MAAA7I,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACtBuD,QAAQ,CAACpB,IAAI,CAAC,uBAAuBK,iBAAiB,CAACoD,OAAO,CAAC,CAAC,CAAC,wBAAwB,CAAC;IAC5F,CAAC;IAAA;IAAA;MAAAhH,cAAA,GAAAsB,CAAA;IAAA;IAED;IACA,MAAMuL,2BAA2B;IAAA;IAAA,CAAA7M,cAAA,GAAAoB,CAAA,SAAGoI,OAAO,CAACsD,IAAI,CAACF,CAAC,IAChD;MAAA;MAAA5M,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAAA,kCAAApB,cAAA,GAAAsB,CAAA,WAAAsL,CAAC,CAACV,iBAAiB;MAAA;MAAA,CAAAlM,cAAA,GAAAsB,CAAA,WAAI4C,IAAI,CAAC6I,GAAG,CAACH,CAAC,CAACV,iBAAiB,CAACc,QAAQ,GAAG,GAAG,CAAC,GAAG,IAAI;IAAJ,CAAI,CAC3E;IAAC;IAAAhN,cAAA,GAAAoB,CAAA;IAEF,IAAIyL,2BAA2B,EAAE;MAAA;MAAA7M,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAC/ByD,eAAe,CAACtB,IAAI,CAAC,kEAAkE,CAAC;IAC1F,CAAC;IAAA;IAAA;MAAAvD,cAAA,GAAAsB,CAAA;IAAA;IAED;IACA,MAAM2L,eAAe;IAAA;IAAA,CAAAjN,cAAA,GAAAoB,CAAA,SAAGoI,OAAO,CAACsD,IAAI,CAACF,CAAC,IAAI;MAAA;MAAA5M,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAAA,kCAAApB,cAAA,GAAAsB,CAAA,WAAAsL,CAAC,CAACZ,iBAAiB;MAAA;MAAA,CAAAhM,cAAA,GAAAsB,CAAA,WAAIsL,CAAC,CAACZ,iBAAiB,GAAG,MAAM;IAAN,CAAM,CAAC;IAAC;IAAAhM,cAAA,GAAAoB,CAAA;IAE/F,IAAI6L,eAAe,EAAE;MAAA;MAAAjN,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACnByD,eAAe,CAACtB,IAAI,CAAC,0DAA0D,CAAC;IAClF,CAAC;IAAA;IAAA;MAAAvD,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAED,OAAO;MACLuD,QAAQ;MACRE,eAAe;MACfE,UAAU,EAAE;QACV6D,iBAAiB;QACjBC,iBAAiB;QACjBC,eAAe;QAAE;QAAA,CAAA9I,cAAA,GAAAsB,CAAA,WAAAsH,iBAAiB;QAAA;QAAA,CAAA5I,cAAA,GAAAsB,CAAA,WAAIuH,iBAAiB;;KAE1D;EACH;EAEA;;;EAGQ,OAAOgD,8BAA8BA,CAC3ClI,QAAgB,EAChBD,MAAc,EACd2C,QAAgB,EAChByF,SAAiB,EACjBV,QAAuB;IAAA;IAAApL,cAAA,GAAAqB,CAAA;IAEvB;IACA,MAAM8G,WAAW;IAAA;IAAA,CAAAnI,cAAA,GAAAoB,CAAA,SAAGuC,QAAQ,GAAG,EAAE,EAAC,CAAC;IACnC,MAAMyE,UAAU;IAAA;IAAA,CAAApI,cAAA,GAAAoB,CAAA,SAAGiF,QAAQ,GAAG,EAAE,EAAC,CAAC;IAElC;IACA,MAAMiC,QAAQ;IAAA;IAAA,CAAAtI,cAAA,GAAAoB,CAAA,SAAI+G,WAAW,GAAGC,UAAU,GAAGgD,QAAQ,CAACa,OAAO,GAAIb,QAAQ,CAAC8B,SAAS;IAEnF;IACA,MAAMC,iBAAiB;IAAA;IAAA,CAAAnN,cAAA,GAAAoB,CAAA,SAAG0K,SAAS,GAAG1D,UAAU;IAChD,MAAMxC,cAAc;IAAA;IAAA,CAAA5F,cAAA,GAAAoB,CAAA,SAAG,IAAI,CAACgM,+BAA+B,CAAC9E,QAAQ,EAAE6E,iBAAiB,CAAC;IAExF;IACA,MAAME,eAAe;IAAA;IAAA,CAAArN,cAAA,GAAAoB,CAAA,SAAGwE,cAAc,IAAIlC,MAAM,GAAG0E,UAAU,CAAC,IACtCgD,QAAQ,CAACa,OAAO,GAAG/H,IAAI,CAACmD,GAAG,CAACc,WAAW,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;IAEnF;IAAA;IAAAnI,cAAA,GAAAoB,CAAA;IACA,OAAOiM,eAAe,GAAG,GAAG;EAC9B;EAEA;;;EAGQ,OAAOD,+BAA+BA,CAAC9E,QAAgB,EAAE6E,iBAAyB;IAAA;IAAAnN,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACxF;IACA,IAAIkH,QAAQ,GAAG,IAAI,EAAE;MAAA;MAAAtI,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACnB,OAAO,EAAE,GAAGkH,QAAQ;IACtB,CAAC;IAAA;IAAA;MAAAtI,cAAA,GAAAsB,CAAA;IAAA;IAED;IACA,IAAID,CAAC;IAAA;IAAA,CAAArB,cAAA,GAAAoB,CAAA,SAAG,IAAI,EAAC,CAAC;IAAA;IAAApB,cAAA,GAAAoB,CAAA;IAEd,KAAK,IAAIkM,CAAC;IAAA;IAAA,CAAAtN,cAAA,GAAAoB,CAAA,SAAG,CAAC,GAAEkM,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC3B,MAAMC,IAAI;MAAA;MAAA,CAAAvN,cAAA,GAAAoB,CAAA,SAAG,CAAC,GAAG8C,IAAI,CAACmD,GAAG,CAAC,CAAC,CAAC,GAAGnD,IAAI,CAACsJ,KAAK,CAACL,iBAAiB,GAAG,GAAG,GAAG,IAAI,IAAI7E,QAAQ,GAAGpE,IAAI,CAACuJ,IAAI,CAACpM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MAAC;MAAArB,cAAA,GAAAoB,CAAA;MAE1G,IAAI8C,IAAI,CAAC6I,GAAG,CAACQ,IAAI,GAAGlM,CAAC,CAAC,GAAG,MAAM,EAAE;QAAA;QAAArB,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAC/B;MACF,CAAC;MAAA;MAAA;QAAApB,cAAA,GAAAsB,CAAA;MAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACDC,CAAC,GAAGkM,IAAI;IACV;IAAC;IAAAvN,cAAA,GAAAoB,CAAA;IAED,OAAOC,CAAC;EACV;;;;AAxvBFqM,OAAA,CAAArL,wBAAA,GAAAA,wBAAA;AAEE;AAAA;AAAArC,cAAA,GAAAoB,CAAA;AACwBiB,wBAAA,CAAAsG,sBAAsB,GAAG;EAC/CU,MAAM,EAAE;IAAElF,GAAG,EAAE,GAAG;IAAE4E,WAAW,EAAE;EAAG,CAAE;EACtC4E,MAAM,EAAE;IAAExJ,GAAG,EAAE,GAAG;IAAE4E,WAAW,EAAE;EAAG,CAAE;EACtC6E,OAAO,EAAE;IAAEzJ,GAAG,EAAE,GAAG;IAAE4E,WAAW,EAAE;EAAG;CACtC;AAED;AAAA;AAAA/I,cAAA,GAAAoB,CAAA;AACwBiB,wBAAA,CAAAoG,sBAAsB,GAAG;EAC/CY,MAAM,EAAE;IAAEhF,GAAG,EAAE,GAAG;IAAEF,GAAG,EAAE,IAAI;IAAE4E,WAAW,EAAE;EAAI,CAAE;EAClD4E,MAAM,EAAE;IAAEtJ,GAAG,EAAE,GAAG;IAAEF,GAAG,EAAE,IAAI;IAAE4E,WAAW,EAAE;EAAI,CAAE;EAClD6E,OAAO,EAAE;IAAEvJ,GAAG,EAAE,GAAG;IAAEF,GAAG,EAAE,IAAI;IAAE4E,WAAW,EAAE;EAAI;CAClD", "ignoreList": []}