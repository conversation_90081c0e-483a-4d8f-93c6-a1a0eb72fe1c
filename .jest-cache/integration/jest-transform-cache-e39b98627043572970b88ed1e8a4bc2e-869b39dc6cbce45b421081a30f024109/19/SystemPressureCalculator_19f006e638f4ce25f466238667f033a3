9bfea90b1708b5eb66ae7b9b1a119e71
"use strict";

/**
 * SystemPressureCalculator - Modular calculation service for complete HVAC system pressure analysis
 *
 * MISSION-CRITICAL: Pure TypeScript functions for system-level pressure drop calculations
 * Combines friction losses (straight runs) with minor losses (fittings) for complete system analysis
 *
 * @see docs/implementation/duct-physics/system-pressure-calculations.md
 * @see backend/services/calculations/AirDuctCalculator.ts
 * @see backend/services/calculations/FittingLossCalculator.ts
 */
/* istanbul ignore next */
function cov_2n09p8ow7h() {
  var path = "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\SystemPressureCalculator.ts";
  var hash = "a2c5d7f97851efed7c411172c8b9d1d67d60aed0";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\SystemPressureCalculator.ts",
    statementMap: {
      "0": {
        start: {
          line: 12,
          column: 0
        },
        end: {
          line: 12,
          column: 62
        }
      },
      "1": {
        start: {
          line: 13,
          column: 0
        },
        end: {
          line: 13,
          column: 42
        }
      },
      "2": {
        start: {
          line: 14,
          column: 28
        },
        end: {
          line: 14,
          column: 58
        }
      },
      "3": {
        start: {
          line: 15,
          column: 32
        },
        end: {
          line: 15,
          column: 66
        }
      },
      "4": {
        start: {
          line: 16,
          column: 34
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "5": {
        start: {
          line: 26,
          column: 79
        },
        end: {
          line: 26,
          column: 85
        }
      },
      "6": {
        start: {
          line: 28,
          column: 8
        },
        end: {
          line: 28,
          column: 42
        }
      },
      "7": {
        start: {
          line: 30,
          column: 27
        },
        end: {
          line: 30,
          column: 69
        }
      },
      "8": {
        start: {
          line: 32,
          column: 31
        },
        end: {
          line: 32,
          column: 33
        }
      },
      "9": {
        start: {
          line: 33,
          column: 32
        },
        end: {
          line: 33,
          column: 33
        }
      },
      "10": {
        start: {
          line: 34,
          column: 29
        },
        end: {
          line: 34,
          column: 30
        }
      },
      "11": {
        start: {
          line: 35,
          column: 26
        },
        end: {
          line: 35,
          column: 27
        }
      },
      "12": {
        start: {
          line: 36,
          column: 27
        },
        end: {
          line: 36,
          column: 29
        }
      },
      "13": {
        start: {
          line: 37,
          column: 8
        },
        end: {
          line: 46,
          column: 9
        }
      },
      "14": {
        start: {
          line: 38,
          column: 34
        },
        end: {
          line: 38,
          column: 104
        }
      },
      "15": {
        start: {
          line: 39,
          column: 12
        },
        end: {
          line: 39,
          column: 47
        }
      },
      "16": {
        start: {
          line: 40,
          column: 12
        },
        end: {
          line: 40,
          column: 60
        }
      },
      "17": {
        start: {
          line: 41,
          column: 12
        },
        end: {
          line: 41,
          column: 54
        }
      },
      "18": {
        start: {
          line: 42,
          column: 12
        },
        end: {
          line: 44,
          column: 13
        }
      },
      "19": {
        start: {
          line: 43,
          column: 16
        },
        end: {
          line: 43,
          column: 46
        }
      },
      "20": {
        start: {
          line: 45,
          column: 12
        },
        end: {
          line: 45,
          column: 52
        }
      },
      "21": {
        start: {
          line: 47,
          column: 34
        },
        end: {
          line: 47,
          column: 68
        }
      },
      "22": {
        start: {
          line: 49,
          column: 32
        },
        end: {
          line: 49,
          column: 93
        }
      },
      "23": {
        start: {
          line: 49,
          column: 62
        },
        end: {
          line: 49,
          column: 69
        }
      },
      "24": {
        start: {
          line: 50,
          column: 28
        },
        end: {
          line: 50,
          column: 51
        }
      },
      "25": {
        start: {
          line: 51,
          column: 28
        },
        end: {
          line: 51,
          column: 51
        }
      },
      "26": {
        start: {
          line: 53,
          column: 27
        },
        end: {
          line: 60,
          column: 10
        }
      },
      "27": {
        start: {
          line: 61,
          column: 8
        },
        end: {
          line: 76,
          column: 10
        }
      },
      "28": {
        start: {
          line: 82,
          column: 25
        },
        end: {
          line: 82,
          column: 27
        }
      },
      "29": {
        start: {
          line: 83,
          column: 32
        },
        end: {
          line: 83,
          column: 34
        }
      },
      "30": {
        start: {
          line: 85,
          column: 21
        },
        end: {
          line: 85,
          column: 52
        }
      },
      "31": {
        start: {
          line: 86,
          column: 25
        },
        end: {
          line: 86,
          column: 47
        }
      },
      "32": {
        start: {
          line: 87,
          column: 33
        },
        end: {
          line: 87,
          column: 130
        }
      },
      "33": {
        start: {
          line: 88,
          column: 27
        },
        end: {
          line: 88,
          column: 28
        }
      },
      "34": {
        start: {
          line: 89,
          column: 24
        },
        end: {
          line: 89,
          column: 25
        }
      },
      "35": {
        start: {
          line: 94,
          column: 8
        },
        end: {
          line: 117,
          column: 9
        }
      },
      "36": {
        start: {
          line: 96,
          column: 31
        },
        end: {
          line: 102,
          column: 13
        }
      },
      "37": {
        start: {
          line: 104,
          column: 29
        },
        end: {
          line: 104,
          column: 112
        }
      },
      "38": {
        start: {
          line: 105,
          column: 12
        },
        end: {
          line: 105,
          column: 120
        }
      },
      "39": {
        start: {
          line: 107,
          column: 12
        },
        end: {
          line: 107,
          column: 90
        }
      },
      "40": {
        start: {
          line: 108,
          column: 12
        },
        end: {
          line: 108,
          column: 102
        }
      },
      "41": {
        start: {
          line: 110,
          column: 13
        },
        end: {
          line: 117,
          column: 9
        }
      },
      "42": {
        start: {
          line: 112,
          column: 12
        },
        end: {
          line: 112,
          column: 141
        }
      },
      "43": {
        start: {
          line: 113,
          column: 12
        },
        end: {
          line: 113,
          column: 52
        }
      },
      "44": {
        start: {
          line: 114,
          column: 12
        },
        end: {
          line: 114,
          column: 45
        }
      },
      "45": {
        start: {
          line: 115,
          column: 12
        },
        end: {
          line: 115,
          column: 54
        }
      },
      "46": {
        start: {
          line: 116,
          column: 12
        },
        end: {
          line: 116,
          column: 68
        }
      },
      "47": {
        start: {
          line: 118,
          column: 26
        },
        end: {
          line: 118,
          column: 50
        }
      },
      "48": {
        start: {
          line: 120,
          column: 8
        },
        end: {
          line: 123,
          column: 9
        }
      },
      "49": {
        start: {
          line: 121,
          column: 12
        },
        end: {
          line: 121,
          column: 95
        }
      },
      "50": {
        start: {
          line: 122,
          column: 12
        },
        end: {
          line: 122,
          column: 85
        }
      },
      "51": {
        start: {
          line: 124,
          column: 8
        },
        end: {
          line: 127,
          column: 9
        }
      },
      "52": {
        start: {
          line: 125,
          column: 12
        },
        end: {
          line: 125,
          column: 103
        }
      },
      "53": {
        start: {
          line: 126,
          column: 12
        },
        end: {
          line: 126,
          column: 87
        }
      },
      "54": {
        start: {
          line: 128,
          column: 8
        },
        end: {
          line: 142,
          column: 10
        }
      },
      "55": {
        start: {
          line: 148,
          column: 8
        },
        end: {
          line: 153,
          column: 9
        }
      },
      "56": {
        start: {
          line: 149,
          column: 12
        },
        end: {
          line: 149,
          column: 68
        }
      },
      "57": {
        start: {
          line: 151,
          column: 13
        },
        end: {
          line: 153,
          column: 9
        }
      },
      "58": {
        start: {
          line: 152,
          column: 12
        },
        end: {
          line: 152,
          column: 58
        }
      },
      "59": {
        start: {
          line: 154,
          column: 8
        },
        end: {
          line: 154,
          column: 75
        }
      },
      "60": {
        start: {
          line: 161,
          column: 8
        },
        end: {
          line: 161,
          column: 87
        }
      },
      "61": {
        start: {
          line: 167,
          column: 62
        },
        end: {
          line: 167,
          column: 72
        }
      },
      "62": {
        start: {
          line: 169,
          column: 32
        },
        end: {
          line: 169,
          column: 37
        }
      },
      "63": {
        start: {
          line: 171,
          column: 26
        },
        end: {
          line: 171,
          column: 64
        }
      },
      "64": {
        start: {
          line: 173,
          column: 30
        },
        end: {
          line: 173,
          column: 56
        }
      },
      "65": {
        start: {
          line: 175,
          column: 30
        },
        end: {
          line: 175,
          column: 57
        }
      },
      "66": {
        start: {
          line: 176,
          column: 8
        },
        end: {
          line: 176,
          column: 75
        }
      },
      "67": {
        start: {
          line: 183,
          column: 29
        },
        end: {
          line: 183,
          column: 129
        }
      },
      "68": {
        start: {
          line: 184,
          column: 29
        },
        end: {
          line: 184,
          column: 47
        }
      },
      "69": {
        start: {
          line: 185,
          column: 8
        },
        end: {
          line: 185,
          column: 43
        }
      },
      "70": {
        start: {
          line: 191,
          column: 28
        },
        end: {
          line: 191,
          column: 41
        }
      },
      "71": {
        start: {
          line: 192,
          column: 27
        },
        end: {
          line: 192,
          column: 40
        }
      },
      "72": {
        start: {
          line: 193,
          column: 35
        },
        end: {
          line: 193,
          column: 42
        }
      },
      "73": {
        start: {
          line: 194,
          column: 8
        },
        end: {
          line: 194,
          column: 63
        }
      },
      "74": {
        start: {
          line: 201,
          column: 8
        },
        end: {
          line: 201,
          column: 110
        }
      },
      "75": {
        start: {
          line: 207,
          column: 8
        },
        end: {
          line: 209,
          column: 9
        }
      },
      "76": {
        start: {
          line: 208,
          column: 12
        },
        end: {
          line: 208,
          column: 72
        }
      },
      "77": {
        start: {
          line: 210,
          column: 8
        },
        end: {
          line: 220,
          column: 9
        }
      },
      "78": {
        start: {
          line: 211,
          column: 12
        },
        end: {
          line: 213,
          column: 13
        }
      },
      "79": {
        start: {
          line: 212,
          column: 16
        },
        end: {
          line: 212,
          column: 77
        }
      },
      "80": {
        start: {
          line: 214,
          column: 12
        },
        end: {
          line: 216,
          column: 13
        }
      },
      "81": {
        start: {
          line: 215,
          column: 16
        },
        end: {
          line: 215,
          column: 92
        }
      },
      "82": {
        start: {
          line: 217,
          column: 12
        },
        end: {
          line: 219,
          column: 13
        }
      },
      "83": {
        start: {
          line: 218,
          column: 16
        },
        end: {
          line: 218,
          column: 97
        }
      },
      "84": {
        start: {
          line: 226,
          column: 25
        },
        end: {
          line: 226,
          column: 27
        }
      },
      "85": {
        start: {
          line: 227,
          column: 32
        },
        end: {
          line: 227,
          column: 34
        }
      },
      "86": {
        start: {
          line: 228,
          column: 31
        },
        end: {
          line: 228,
          column: 75
        }
      },
      "87": {
        start: {
          line: 229,
          column: 31
        },
        end: {
          line: 229,
          column: 75
        }
      },
      "88": {
        start: {
          line: 231,
          column: 34
        },
        end: {
          line: 231,
          column: 114
        }
      },
      "89": {
        start: {
          line: 232,
          column: 8
        },
        end: {
          line: 235,
          column: 9
        }
      },
      "90": {
        start: {
          line: 233,
          column: 12
        },
        end: {
          line: 233,
          column: 119
        }
      },
      "91": {
        start: {
          line: 234,
          column: 12
        },
        end: {
          line: 234,
          column: 81
        }
      },
      "92": {
        start: {
          line: 237,
          column: 34
        },
        end: {
          line: 237,
          column: 78
        }
      },
      "93": {
        start: {
          line: 238,
          column: 8
        },
        end: {
          line: 241,
          column: 9
        }
      },
      "94": {
        start: {
          line: 239,
          column: 12
        },
        end: {
          line: 239,
          column: 146
        }
      },
      "95": {
        start: {
          line: 240,
          column: 12
        },
        end: {
          line: 240,
          column: 113
        }
      },
      "96": {
        start: {
          line: 243,
          column: 32
        },
        end: {
          line: 243,
          column: 70
        }
      },
      "97": {
        start: {
          line: 245,
          column: 8
        },
        end: {
          line: 247,
          column: 9
        }
      },
      "98": {
        start: {
          line: 246,
          column: 12
        },
        end: {
          line: 246,
          column: 133
        }
      },
      "99": {
        start: {
          line: 248,
          column: 8
        },
        end: {
          line: 250,
          column: 9
        }
      },
      "100": {
        start: {
          line: 249,
          column: 12
        },
        end: {
          line: 249,
          column: 129
        }
      },
      "101": {
        start: {
          line: 251,
          column: 8
        },
        end: {
          line: 255,
          column: 10
        }
      },
      "102": {
        start: {
          line: 261,
          column: 23
        },
        end: {
          line: 261,
          column: 46
        }
      },
      "103": {
        start: {
          line: 262,
          column: 8
        },
        end: {
          line: 262,
          column: 51
        }
      },
      "104": {
        start: {
          line: 268,
          column: 8
        },
        end: {
          line: 271,
          column: 10
        }
      },
      "105": {
        start: {
          line: 277,
          column: 79
        },
        end: {
          line: 277,
          column: 85
        }
      },
      "106": {
        start: {
          line: 279,
          column: 8
        },
        end: {
          line: 279,
          column: 42
        }
      },
      "107": {
        start: {
          line: 280,
          column: 24
        },
        end: {
          line: 280,
          column: 26
        }
      },
      "108": {
        start: {
          line: 281,
          column: 32
        },
        end: {
          line: 281,
          column: 33
        }
      },
      "109": {
        start: {
          line: 282,
          column: 32
        },
        end: {
          line: 282,
          column: 33
        }
      },
      "110": {
        start: {
          line: 283,
          column: 31
        },
        end: {
          line: 283,
          column: 32
        }
      },
      "111": {
        start: {
          line: 284,
          column: 33
        },
        end: {
          line: 284,
          column: 34
        }
      },
      "112": {
        start: {
          line: 285,
          column: 25
        },
        end: {
          line: 285,
          column: 27
        }
      },
      "113": {
        start: {
          line: 286,
          column: 22
        },
        end: {
          line: 286,
          column: 24
        }
      },
      "114": {
        start: {
          line: 288,
          column: 8
        },
        end: {
          line: 313,
          column: 9
        }
      },
      "115": {
        start: {
          line: 289,
          column: 34
        },
        end: {
          line: 289,
          column: 98
        }
      },
      "116": {
        start: {
          line: 290,
          column: 12
        },
        end: {
          line: 290,
          column: 40
        }
      },
      "117": {
        start: {
          line: 291,
          column: 12
        },
        end: {
          line: 291,
          column: 60
        }
      },
      "118": {
        start: {
          line: 292,
          column: 12
        },
        end: {
          line: 297,
          column: 13
        }
      },
      "119": {
        start: {
          line: 293,
          column: 16
        },
        end: {
          line: 293,
          column: 64
        }
      },
      "120": {
        start: {
          line: 295,
          column: 17
        },
        end: {
          line: 297,
          column: 13
        }
      },
      "121": {
        start: {
          line: 296,
          column: 16
        },
        end: {
          line: 296,
          column: 63
        }
      },
      "122": {
        start: {
          line: 299,
          column: 12
        },
        end: {
          line: 305,
          column: 13
        }
      },
      "123": {
        start: {
          line: 300,
          column: 40
        },
        end: {
          line: 300,
          column: 80
        }
      },
      "124": {
        start: {
          line: 301,
          column: 16
        },
        end: {
          line: 301,
          column: 69
        }
      },
      "125": {
        start: {
          line: 302,
          column: 16
        },
        end: {
          line: 304,
          column: 17
        }
      },
      "126": {
        start: {
          line: 303,
          column: 20
        },
        end: {
          line: 303,
          column: 63
        }
      },
      "127": {
        start: {
          line: 307,
          column: 12
        },
        end: {
          line: 309,
          column: 13
        }
      },
      "128": {
        start: {
          line: 308,
          column: 16
        },
        end: {
          line: 308,
          column: 57
        }
      },
      "129": {
        start: {
          line: 310,
          column: 12
        },
        end: {
          line: 312,
          column: 13
        }
      },
      "130": {
        start: {
          line: 311,
          column: 16
        },
        end: {
          line: 311,
          column: 51
        }
      },
      "131": {
        start: {
          line: 315,
          column: 29
        },
        end: {
          line: 315,
          column: 70
        }
      },
      "132": {
        start: {
          line: 315,
          column: 59
        },
        end: {
          line: 315,
          column: 68
        }
      },
      "133": {
        start: {
          line: 316,
          column: 32
        },
        end: {
          line: 316,
          column: 71
        }
      },
      "134": {
        start: {
          line: 318,
          column: 28
        },
        end: {
          line: 318,
          column: 82
        }
      },
      "135": {
        start: {
          line: 319,
          column: 8
        },
        end: {
          line: 319,
          column: 38
        }
      },
      "136": {
        start: {
          line: 321,
          column: 25
        },
        end: {
          line: 321,
          column: 134
        }
      },
      "137": {
        start: {
          line: 322,
          column: 8
        },
        end: {
          line: 341,
          column: 10
        }
      },
      "138": {
        start: {
          line: 347,
          column: 25
        },
        end: {
          line: 347,
          column: 27
        }
      },
      "139": {
        start: {
          line: 348,
          column: 22
        },
        end: {
          line: 348,
          column: 24
        }
      },
      "140": {
        start: {
          line: 350,
          column: 30
        },
        end: {
          line: 355,
          column: 9
        }
      },
      "141": {
        start: {
          line: 357,
          column: 25
        },
        end: {
          line: 357,
          column: 112
        }
      },
      "142": {
        start: {
          line: 358,
          column: 8
        },
        end: {
          line: 358,
          column: 44
        }
      },
      "143": {
        start: {
          line: 359,
          column: 8
        },
        end: {
          line: 359,
          column: 38
        }
      },
      "144": {
        start: {
          line: 360,
          column: 8
        },
        end: {
          line: 367,
          column: 9
        }
      },
      "145": {
        start: {
          line: 362,
          column: 12
        },
        end: {
          line: 362,
          column: 90
        }
      },
      "146": {
        start: {
          line: 364,
          column: 13
        },
        end: {
          line: 367,
          column: 9
        }
      },
      "147": {
        start: {
          line: 366,
          column: 12
        },
        end: {
          line: 366,
          column: 89
        }
      },
      "148": {
        start: {
          line: 369,
          column: 8
        },
        end: {
          line: 376,
          column: 10
        }
      },
      "149": {
        start: {
          line: 383,
          column: 29
        },
        end: {
          line: 383,
          column: 172
        }
      },
      "150": {
        start: {
          line: 384,
          column: 8
        },
        end: {
          line: 384,
          column: 48
        }
      },
      "151": {
        start: {
          line: 385,
          column: 8
        },
        end: {
          line: 385,
          column: 42
        }
      },
      "152": {
        start: {
          line: 387,
          column: 21
        },
        end: {
          line: 389,
          column: 68
        }
      },
      "153": {
        start: {
          line: 390,
          column: 25
        },
        end: {
          line: 390,
          column: 47
        }
      },
      "154": {
        start: {
          line: 392,
          column: 25
        },
        end: {
          line: 394,
          column: 122
        }
      },
      "155": {
        start: {
          line: 396,
          column: 29
        },
        end: {
          line: 396,
          column: 139
        }
      },
      "156": {
        start: {
          line: 397,
          column: 8
        },
        end: {
          line: 408,
          column: 10
        }
      },
      "157": {
        start: {
          line: 415,
          column: 21
        },
        end: {
          line: 417,
          column: 68
        }
      },
      "158": {
        start: {
          line: 418,
          column: 25
        },
        end: {
          line: 418,
          column: 47
        }
      },
      "159": {
        start: {
          line: 420,
          column: 25
        },
        end: {
          line: 428,
          column: 9
        }
      },
      "160": {
        start: {
          line: 429,
          column: 25
        },
        end: {
          line: 429,
          column: 110
        }
      },
      "161": {
        start: {
          line: 430,
          column: 8
        },
        end: {
          line: 430,
          column: 44
        }
      },
      "162": {
        start: {
          line: 432,
          column: 30
        },
        end: {
          line: 432,
          column: 147
        }
      },
      "163": {
        start: {
          line: 434,
          column: 38
        },
        end: {
          line: 435,
          column: 70
        }
      },
      "164": {
        start: {
          line: 436,
          column: 8
        },
        end: {
          line: 447,
          column: 10
        }
      },
      "165": {
        start: {
          line: 453,
          column: 25
        },
        end: {
          line: 453,
          column: 27
        }
      },
      "166": {
        start: {
          line: 454,
          column: 8
        },
        end: {
          line: 456,
          column: 9
        }
      },
      "167": {
        start: {
          line: 455,
          column: 12
        },
        end: {
          line: 455,
          column: 51
        }
      },
      "168": {
        start: {
          line: 458,
          column: 33
        },
        end: {
          line: 458,
          column: 127
        }
      },
      "169": {
        start: {
          line: 459,
          column: 8
        },
        end: {
          line: 459,
          column: 52
        }
      },
      "170": {
        start: {
          line: 462,
          column: 27
        },
        end: {
          line: 462,
          column: 32
        }
      },
      "171": {
        start: {
          line: 463,
          column: 31
        },
        end: {
          line: 463,
          column: 69
        }
      },
      "172": {
        start: {
          line: 464,
          column: 8
        },
        end: {
          line: 467,
          column: 10
        }
      },
      "173": {
        start: {
          line: 473,
          column: 28
        },
        end: {
          line: 473,
          column: 29
        }
      },
      "174": {
        start: {
          line: 474,
          column: 20
        },
        end: {
          line: 474,
          column: 21
        }
      },
      "175": {
        start: {
          line: 475,
          column: 8
        },
        end: {
          line: 482,
          column: 9
        }
      },
      "176": {
        start: {
          line: 476,
          column: 25
        },
        end: {
          line: 478,
          column: 72
        }
      },
      "177": {
        start: {
          line: 479,
          column: 29
        },
        end: {
          line: 479,
          column: 51
        }
      },
      "178": {
        start: {
          line: 480,
          column: 12
        },
        end: {
          line: 480,
          column: 38
        }
      },
      "179": {
        start: {
          line: 481,
          column: 12
        },
        end: {
          line: 481,
          column: 20
        }
      },
      "180": {
        start: {
          line: 483,
          column: 8
        },
        end: {
          line: 483,
          column: 53
        }
      },
      "181": {
        start: {
          line: 489,
          column: 25
        },
        end: {
          line: 489,
          column: 27
        }
      },
      "182": {
        start: {
          line: 490,
          column: 8
        },
        end: {
          line: 492,
          column: 9
        }
      },
      "183": {
        start: {
          line: 491,
          column: 12
        },
        end: {
          line: 491,
          column: 104
        }
      },
      "184": {
        start: {
          line: 493,
          column: 8
        },
        end: {
          line: 495,
          column: 9
        }
      },
      "185": {
        start: {
          line: 494,
          column: 12
        },
        end: {
          line: 494,
          column: 106
        }
      },
      "186": {
        start: {
          line: 496,
          column: 8
        },
        end: {
          line: 498,
          column: 9
        }
      },
      "187": {
        start: {
          line: 497,
          column: 12
        },
        end: {
          line: 497,
          column: 101
        }
      },
      "188": {
        start: {
          line: 499,
          column: 8
        },
        end: {
          line: 499,
          column: 24
        }
      },
      "189": {
        start: {
          line: 505,
          column: 25
        },
        end: {
          line: 505,
          column: 27
        }
      },
      "190": {
        start: {
          line: 506,
          column: 32
        },
        end: {
          line: 506,
          column: 34
        }
      },
      "191": {
        start: {
          line: 508,
          column: 23
        },
        end: {
          line: 508,
          column: 55
        }
      },
      "192": {
        start: {
          line: 509,
          column: 32
        },
        end: {
          line: 509,
          column: 103
        }
      },
      "193": {
        start: {
          line: 509,
          column: 59
        },
        end: {
          line: 509,
          column: 82
        }
      },
      "194": {
        start: {
          line: 511,
          column: 34
        },
        end: {
          line: 512,
          column: 50
        }
      },
      "195": {
        start: {
          line: 513,
          column: 8
        },
        end: {
          line: 515,
          column: 9
        }
      },
      "196": {
        start: {
          line: 514,
          column: 12
        },
        end: {
          line: 514,
          column: 109
        }
      },
      "197": {
        start: {
          line: 517,
          column: 34
        },
        end: {
          line: 517,
          column: 74
        }
      },
      "198": {
        start: {
          line: 518,
          column: 8
        },
        end: {
          line: 520,
          column: 9
        }
      },
      "199": {
        start: {
          line: 519,
          column: 12
        },
        end: {
          line: 519,
          column: 103
        }
      },
      "200": {
        start: {
          line: 522,
          column: 44
        },
        end: {
          line: 522,
          column: 137
        }
      },
      "201": {
        start: {
          line: 522,
          column: 62
        },
        end: {
          line: 522,
          column: 136
        }
      },
      "202": {
        start: {
          line: 523,
          column: 8
        },
        end: {
          line: 525,
          column: 9
        }
      },
      "203": {
        start: {
          line: 524,
          column: 12
        },
        end: {
          line: 524,
          column: 101
        }
      },
      "204": {
        start: {
          line: 527,
          column: 32
        },
        end: {
          line: 527,
          column: 102
        }
      },
      "205": {
        start: {
          line: 527,
          column: 50
        },
        end: {
          line: 527,
          column: 101
        }
      },
      "206": {
        start: {
          line: 528,
          column: 8
        },
        end: {
          line: 530,
          column: 9
        }
      },
      "207": {
        start: {
          line: 529,
          column: 12
        },
        end: {
          line: 529,
          column: 93
        }
      },
      "208": {
        start: {
          line: 531,
          column: 8
        },
        end: {
          line: 539,
          column: 10
        }
      },
      "209": {
        start: {
          line: 546,
          column: 28
        },
        end: {
          line: 546,
          column: 41
        }
      },
      "210": {
        start: {
          line: 547,
          column: 27
        },
        end: {
          line: 547,
          column: 40
        }
      },
      "211": {
        start: {
          line: 549,
          column: 25
        },
        end: {
          line: 549,
          column: 91
        }
      },
      "212": {
        start: {
          line: 551,
          column: 34
        },
        end: {
          line: 551,
          column: 56
        }
      },
      "213": {
        start: {
          line: 552,
          column: 31
        },
        end: {
          line: 552,
          column: 96
        }
      },
      "214": {
        start: {
          line: 554,
          column: 32
        },
        end: {
          line: 555,
          column: 72
        }
      },
      "215": {
        start: {
          line: 557,
          column: 8
        },
        end: {
          line: 557,
          column: 37
        }
      },
      "216": {
        start: {
          line: 564,
          column: 8
        },
        end: {
          line: 566,
          column: 9
        }
      },
      "217": {
        start: {
          line: 565,
          column: 12
        },
        end: {
          line: 565,
          column: 33
        }
      },
      "218": {
        start: {
          line: 568,
          column: 16
        },
        end: {
          line: 568,
          column: 20
        }
      },
      "219": {
        start: {
          line: 569,
          column: 8
        },
        end: {
          line: 575,
          column: 9
        }
      },
      "220": {
        start: {
          line: 569,
          column: 21
        },
        end: {
          line: 569,
          column: 22
        }
      },
      "221": {
        start: {
          line: 570,
          column: 25
        },
        end: {
          line: 570,
          column: 117
        }
      },
      "222": {
        start: {
          line: 571,
          column: 12
        },
        end: {
          line: 573,
          column: 13
        }
      },
      "223": {
        start: {
          line: 572,
          column: 16
        },
        end: {
          line: 572,
          column: 22
        }
      },
      "224": {
        start: {
          line: 574,
          column: 12
        },
        end: {
          line: 574,
          column: 21
        }
      },
      "225": {
        start: {
          line: 576,
          column: 8
        },
        end: {
          line: 576,
          column: 17
        }
      },
      "226": {
        start: {
          line: 579,
          column: 0
        },
        end: {
          line: 579,
          column: 60
        }
      },
      "227": {
        start: {
          line: 581,
          column: 0
        },
        end: {
          line: 585,
          column: 2
        }
      },
      "228": {
        start: {
          line: 587,
          column: 0
        },
        end: {
          line: 591,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 25,
            column: 4
          },
          end: {
            line: 25,
            column: 5
          }
        },
        loc: {
          start: {
            line: 25,
            column: 43
          },
          end: {
            line: 77,
            column: 5
          }
        },
        line: 25
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 49,
            column: 50
          },
          end: {
            line: 49,
            column: 51
          }
        },
        loc: {
          start: {
            line: 49,
            column: 62
          },
          end: {
            line: 49,
            column: 69
          }
        },
        line: 49
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 81,
            column: 4
          },
          end: {
            line: 81,
            column: 5
          }
        },
        loc: {
          start: {
            line: 81,
            column: 66
          },
          end: {
            line: 143,
            column: 5
          }
        },
        line: 81
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 147,
            column: 4
          },
          end: {
            line: 147,
            column: 5
          }
        },
        loc: {
          start: {
            line: 147,
            column: 38
          },
          end: {
            line: 155,
            column: 5
          }
        },
        line: 147
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 159,
            column: 4
          },
          end: {
            line: 159,
            column: 5
          }
        },
        loc: {
          start: {
            line: 159,
            column: 54
          },
          end: {
            line: 162,
            column: 5
          }
        },
        line: 159
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 166,
            column: 4
          },
          end: {
            line: 166,
            column: 5
          }
        },
        loc: {
          start: {
            line: 166,
            column: 43
          },
          end: {
            line: 177,
            column: 5
          }
        },
        line: 166
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 181,
            column: 4
          },
          end: {
            line: 181,
            column: 5
          }
        },
        loc: {
          start: {
            line: 181,
            column: 83
          },
          end: {
            line: 186,
            column: 5
          }
        },
        line: 181
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 190,
            column: 4
          },
          end: {
            line: 190,
            column: 5
          }
        },
        loc: {
          start: {
            line: 190,
            column: 67
          },
          end: {
            line: 195,
            column: 5
          }
        },
        line: 190
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 199,
            column: 4
          },
          end: {
            line: 199,
            column: 5
          }
        },
        loc: {
          start: {
            line: 199,
            column: 65
          },
          end: {
            line: 202,
            column: 5
          }
        },
        line: 199
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 206,
            column: 4
          },
          end: {
            line: 206,
            column: 5
          }
        },
        loc: {
          start: {
            line: 206,
            column: 40
          },
          end: {
            line: 221,
            column: 5
          }
        },
        line: 206
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 225,
            column: 4
          },
          end: {
            line: 225,
            column: 5
          }
        },
        loc: {
          start: {
            line: 225,
            column: 39
          },
          end: {
            line: 256,
            column: 5
          }
        },
        line: 225
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 260,
            column: 4
          },
          end: {
            line: 260,
            column: 5
          }
        },
        loc: {
          start: {
            line: 260,
            column: 46
          },
          end: {
            line: 263,
            column: 5
          }
        },
        line: 260
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 267,
            column: 4
          },
          end: {
            line: 267,
            column: 5
          }
        },
        loc: {
          start: {
            line: 267,
            column: 39
          },
          end: {
            line: 272,
            column: 5
          }
        },
        line: 267
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 276,
            column: 4
          },
          end: {
            line: 276,
            column: 5
          }
        },
        loc: {
          start: {
            line: 276,
            column: 51
          },
          end: {
            line: 342,
            column: 5
          }
        },
        line: 276
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 315,
            column: 54
          },
          end: {
            line: 315,
            column: 55
          }
        },
        loc: {
          start: {
            line: 315,
            column: 59
          },
          end: {
            line: 315,
            column: 68
          }
        },
        line: 315
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 346,
            column: 4
          },
          end: {
            line: 346,
            column: 5
          }
        },
        loc: {
          start: {
            line: 346,
            column: 71
          },
          end: {
            line: 377,
            column: 5
          }
        },
        line: 346
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 381,
            column: 4
          },
          end: {
            line: 381,
            column: 5
          }
        },
        loc: {
          start: {
            line: 381,
            column: 77
          },
          end: {
            line: 409,
            column: 5
          }
        },
        line: 381
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 413,
            column: 4
          },
          end: {
            line: 413,
            column: 5
          }
        },
        loc: {
          start: {
            line: 413,
            column: 76
          },
          end: {
            line: 448,
            column: 5
          }
        },
        line: 413
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 452,
            column: 4
          },
          end: {
            line: 452,
            column: 5
          }
        },
        loc: {
          start: {
            line: 452,
            column: 47
          },
          end: {
            line: 468,
            column: 5
          }
        },
        line: 452
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 472,
            column: 4
          },
          end: {
            line: 472,
            column: 5
          }
        },
        loc: {
          start: {
            line: 472,
            column: 46
          },
          end: {
            line: 484,
            column: 5
          }
        },
        line: 472
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 488,
            column: 4
          },
          end: {
            line: 488,
            column: 5
          }
        },
        loc: {
          start: {
            line: 488,
            column: 61
          },
          end: {
            line: 500,
            column: 5
          }
        },
        line: 488
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 504,
            column: 4
          },
          end: {
            line: 504,
            column: 5
          }
        },
        loc: {
          start: {
            line: 504,
            column: 116
          },
          end: {
            line: 540,
            column: 5
          }
        },
        line: 504
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 509,
            column: 47
          },
          end: {
            line: 509,
            column: 48
          }
        },
        loc: {
          start: {
            line: 509,
            column: 59
          },
          end: {
            line: 509,
            column: 82
          }
        },
        line: 509
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 522,
            column: 57
          },
          end: {
            line: 522,
            column: 58
          }
        },
        loc: {
          start: {
            line: 522,
            column: 62
          },
          end: {
            line: 522,
            column: 136
          }
        },
        line: 522
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 527,
            column: 45
          },
          end: {
            line: 527,
            column: 46
          }
        },
        loc: {
          start: {
            line: 527,
            column: 50
          },
          end: {
            line: 527,
            column: 101
          }
        },
        line: 527
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 544,
            column: 4
          },
          end: {
            line: 544,
            column: 5
          }
        },
        loc: {
          start: {
            line: 544,
            column: 91
          },
          end: {
            line: 558,
            column: 5
          }
        },
        line: 544
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 562,
            column: 4
          },
          end: {
            line: 562,
            column: 5
          }
        },
        loc: {
          start: {
            line: 562,
            column: 72
          },
          end: {
            line: 577,
            column: 5
          }
        },
        line: 562
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 42,
            column: 12
          },
          end: {
            line: 44,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 42,
            column: 12
          },
          end: {
            line: 44,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 42
      },
      "1": {
        loc: {
          start: {
            line: 94,
            column: 8
          },
          end: {
            line: 117,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 94,
            column: 8
          },
          end: {
            line: 117,
            column: 9
          }
        }, {
          start: {
            line: 110,
            column: 13
          },
          end: {
            line: 117,
            column: 9
          }
        }],
        line: 94
      },
      "2": {
        loc: {
          start: {
            line: 94,
            column: 12
          },
          end: {
            line: 94,
            column: 57
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 94,
            column: 12
          },
          end: {
            line: 94,
            column: 39
          }
        }, {
          start: {
            line: 94,
            column: 43
          },
          end: {
            line: 94,
            column: 57
          }
        }],
        line: 94
      },
      "3": {
        loc: {
          start: {
            line: 104,
            column: 29
          },
          end: {
            line: 104,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 104,
            column: 29
          },
          end: {
            line: 104,
            column: 45
          }
        }, {
          start: {
            line: 104,
            column: 49
          },
          end: {
            line: 104,
            column: 112
          }
        }],
        line: 104
      },
      "4": {
        loc: {
          start: {
            line: 110,
            column: 13
          },
          end: {
            line: 117,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 110,
            column: 13
          },
          end: {
            line: 117,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 110
      },
      "5": {
        loc: {
          start: {
            line: 110,
            column: 17
          },
          end: {
            line: 110,
            column: 68
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 110,
            column: 17
          },
          end: {
            line: 110,
            column: 43
          }
        }, {
          start: {
            line: 110,
            column: 47
          },
          end: {
            line: 110,
            column: 68
          }
        }],
        line: 110
      },
      "6": {
        loc: {
          start: {
            line: 120,
            column: 8
          },
          end: {
            line: 123,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 120,
            column: 8
          },
          end: {
            line: 123,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 120
      },
      "7": {
        loc: {
          start: {
            line: 124,
            column: 8
          },
          end: {
            line: 127,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 124,
            column: 8
          },
          end: {
            line: 127,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 124
      },
      "8": {
        loc: {
          start: {
            line: 148,
            column: 8
          },
          end: {
            line: 153,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 148,
            column: 8
          },
          end: {
            line: 153,
            column: 9
          }
        }, {
          start: {
            line: 151,
            column: 13
          },
          end: {
            line: 153,
            column: 9
          }
        }],
        line: 148
      },
      "9": {
        loc: {
          start: {
            line: 148,
            column: 12
          },
          end: {
            line: 148,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 148,
            column: 12
          },
          end: {
            line: 148,
            column: 41
          }
        }, {
          start: {
            line: 148,
            column: 45
          },
          end: {
            line: 148,
            column: 61
          }
        }],
        line: 148
      },
      "10": {
        loc: {
          start: {
            line: 151,
            column: 13
          },
          end: {
            line: 153,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 151,
            column: 13
          },
          end: {
            line: 153,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 151
      },
      "11": {
        loc: {
          start: {
            line: 151,
            column: 17
          },
          end: {
            line: 151,
            column: 87
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 151,
            column: 17
          },
          end: {
            line: 151,
            column: 52
          }
        }, {
          start: {
            line: 151,
            column: 56
          },
          end: {
            line: 151,
            column: 69
          }
        }, {
          start: {
            line: 151,
            column: 73
          },
          end: {
            line: 151,
            column: 87
          }
        }],
        line: 151
      },
      "12": {
        loc: {
          start: {
            line: 207,
            column: 8
          },
          end: {
            line: 209,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 207,
            column: 8
          },
          end: {
            line: 209,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 207
      },
      "13": {
        loc: {
          start: {
            line: 207,
            column: 12
          },
          end: {
            line: 207,
            column: 60
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 207,
            column: 12
          },
          end: {
            line: 207,
            column: 28
          }
        }, {
          start: {
            line: 207,
            column: 32
          },
          end: {
            line: 207,
            column: 60
          }
        }],
        line: 207
      },
      "14": {
        loc: {
          start: {
            line: 211,
            column: 12
          },
          end: {
            line: 213,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 211,
            column: 12
          },
          end: {
            line: 213,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 211
      },
      "15": {
        loc: {
          start: {
            line: 214,
            column: 12
          },
          end: {
            line: 216,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 214,
            column: 12
          },
          end: {
            line: 216,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 214
      },
      "16": {
        loc: {
          start: {
            line: 214,
            column: 16
          },
          end: {
            line: 214,
            column: 87
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 214,
            column: 16
          },
          end: {
            line: 214,
            column: 43
          }
        }, {
          start: {
            line: 214,
            column: 48
          },
          end: {
            line: 214,
            column: 63
          }
        }, {
          start: {
            line: 214,
            column: 67
          },
          end: {
            line: 214,
            column: 86
          }
        }],
        line: 214
      },
      "17": {
        loc: {
          start: {
            line: 217,
            column: 12
          },
          end: {
            line: 219,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 217,
            column: 12
          },
          end: {
            line: 219,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 217
      },
      "18": {
        loc: {
          start: {
            line: 217,
            column: 16
          },
          end: {
            line: 217,
            column: 68
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 217,
            column: 16
          },
          end: {
            line: 217,
            column: 42
          }
        }, {
          start: {
            line: 217,
            column: 46
          },
          end: {
            line: 217,
            column: 68
          }
        }],
        line: 217
      },
      "19": {
        loc: {
          start: {
            line: 231,
            column: 34
          },
          end: {
            line: 231,
            column: 114
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 231,
            column: 34
          },
          end: {
            line: 231,
            column: 72
          }
        }, {
          start: {
            line: 231,
            column: 76
          },
          end: {
            line: 231,
            column: 114
          }
        }],
        line: 231
      },
      "20": {
        loc: {
          start: {
            line: 232,
            column: 8
          },
          end: {
            line: 235,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 232,
            column: 8
          },
          end: {
            line: 235,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 232
      },
      "21": {
        loc: {
          start: {
            line: 238,
            column: 8
          },
          end: {
            line: 241,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 238,
            column: 8
          },
          end: {
            line: 241,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 238
      },
      "22": {
        loc: {
          start: {
            line: 243,
            column: 32
          },
          end: {
            line: 243,
            column: 70
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 243,
            column: 32
          },
          end: {
            line: 243,
            column: 49
          }
        }, {
          start: {
            line: 243,
            column: 53
          },
          end: {
            line: 243,
            column: 70
          }
        }],
        line: 243
      },
      "23": {
        loc: {
          start: {
            line: 245,
            column: 8
          },
          end: {
            line: 247,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 245,
            column: 8
          },
          end: {
            line: 247,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 245
      },
      "24": {
        loc: {
          start: {
            line: 248,
            column: 8
          },
          end: {
            line: 250,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 248,
            column: 8
          },
          end: {
            line: 250,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 248
      },
      "25": {
        loc: {
          start: {
            line: 269,
            column: 22
          },
          end: {
            line: 269,
            column: 99
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 269,
            column: 22
          },
          end: {
            line: 269,
            column: 61
          }
        }, {
          start: {
            line: 269,
            column: 65
          },
          end: {
            line: 269,
            column: 99
          }
        }],
        line: 269
      },
      "26": {
        loc: {
          start: {
            line: 270,
            column: 22
          },
          end: {
            line: 270,
            column: 99
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 270,
            column: 22
          },
          end: {
            line: 270,
            column: 61
          }
        }, {
          start: {
            line: 270,
            column: 65
          },
          end: {
            line: 270,
            column: 99
          }
        }],
        line: 270
      },
      "27": {
        loc: {
          start: {
            line: 292,
            column: 12
          },
          end: {
            line: 297,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 292,
            column: 12
          },
          end: {
            line: 297,
            column: 13
          }
        }, {
          start: {
            line: 295,
            column: 17
          },
          end: {
            line: 297,
            column: 13
          }
        }],
        line: 292
      },
      "28": {
        loc: {
          start: {
            line: 295,
            column: 17
          },
          end: {
            line: 297,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 295,
            column: 17
          },
          end: {
            line: 297,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 295
      },
      "29": {
        loc: {
          start: {
            line: 299,
            column: 12
          },
          end: {
            line: 305,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 299,
            column: 12
          },
          end: {
            line: 305,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 299
      },
      "30": {
        loc: {
          start: {
            line: 302,
            column: 16
          },
          end: {
            line: 304,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 302,
            column: 16
          },
          end: {
            line: 304,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 302
      },
      "31": {
        loc: {
          start: {
            line: 307,
            column: 12
          },
          end: {
            line: 309,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 307,
            column: 12
          },
          end: {
            line: 309,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 307
      },
      "32": {
        loc: {
          start: {
            line: 310,
            column: 12
          },
          end: {
            line: 312,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 310,
            column: 12
          },
          end: {
            line: 312,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 310
      },
      "33": {
        loc: {
          start: {
            line: 351,
            column: 25
          },
          end: {
            line: 351,
            column: 82
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 351,
            column: 25
          },
          end: {
            line: 351,
            column: 44
          }
        }, {
          start: {
            line: 351,
            column: 48
          },
          end: {
            line: 351,
            column: 76
          }
        }, {
          start: {
            line: 351,
            column: 80
          },
          end: {
            line: 351,
            column: 82
          }
        }],
        line: 351
      },
      "34": {
        loc: {
          start: {
            line: 352,
            column: 22
          },
          end: {
            line: 352,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 352,
            column: 22
          },
          end: {
            line: 352,
            column: 38
          }
        }, {
          start: {
            line: 352,
            column: 42
          },
          end: {
            line: 352,
            column: 77
          }
        }],
        line: 352
      },
      "35": {
        loc: {
          start: {
            line: 353,
            column: 22
          },
          end: {
            line: 353,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 353,
            column: 22
          },
          end: {
            line: 353,
            column: 39
          }
        }, {
          start: {
            line: 353,
            column: 43
          },
          end: {
            line: 353,
            column: 68
          }
        }, {
          start: {
            line: 353,
            column: 72
          },
          end: {
            line: 353,
            column: 73
          }
        }],
        line: 353
      },
      "36": {
        loc: {
          start: {
            line: 354,
            column: 22
          },
          end: {
            line: 354,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 354,
            column: 22
          },
          end: {
            line: 354,
            column: 38
          }
        }, {
          start: {
            line: 354,
            column: 42
          },
          end: {
            line: 354,
            column: 67
          }
        }, {
          start: {
            line: 354,
            column: 71
          },
          end: {
            line: 354,
            column: 73
          }
        }],
        line: 354
      },
      "37": {
        loc: {
          start: {
            line: 360,
            column: 8
          },
          end: {
            line: 367,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 360,
            column: 8
          },
          end: {
            line: 367,
            column: 9
          }
        }, {
          start: {
            line: 364,
            column: 13
          },
          end: {
            line: 367,
            column: 9
          }
        }],
        line: 360
      },
      "38": {
        loc: {
          start: {
            line: 360,
            column: 12
          },
          end: {
            line: 360,
            column: 57
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 360,
            column: 12
          },
          end: {
            line: 360,
            column: 39
          }
        }, {
          start: {
            line: 360,
            column: 43
          },
          end: {
            line: 360,
            column: 57
          }
        }],
        line: 360
      },
      "39": {
        loc: {
          start: {
            line: 364,
            column: 13
          },
          end: {
            line: 367,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 364,
            column: 13
          },
          end: {
            line: 367,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 364
      },
      "40": {
        loc: {
          start: {
            line: 364,
            column: 17
          },
          end: {
            line: 364,
            column: 68
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 364,
            column: 17
          },
          end: {
            line: 364,
            column: 43
          }
        }, {
          start: {
            line: 364,
            column: 47
          },
          end: {
            line: 364,
            column: 68
          }
        }],
        line: 364
      },
      "41": {
        loc: {
          start: {
            line: 387,
            column: 21
          },
          end: {
            line: 389,
            column: 68
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 388,
            column: 14
          },
          end: {
            line: 388,
            column: 70
          }
        }, {
          start: {
            line: 389,
            column: 14
          },
          end: {
            line: 389,
            column: 68
          }
        }],
        line: 387
      },
      "42": {
        loc: {
          start: {
            line: 388,
            column: 34
          },
          end: {
            line: 388,
            column: 56
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 388,
            column: 34
          },
          end: {
            line: 388,
            column: 50
          }
        }, {
          start: {
            line: 388,
            column: 54
          },
          end: {
            line: 388,
            column: 56
          }
        }],
        line: 388
      },
      "43": {
        loc: {
          start: {
            line: 389,
            column: 16
          },
          end: {
            line: 389,
            column: 35
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 389,
            column: 16
          },
          end: {
            line: 389,
            column: 29
          }
        }, {
          start: {
            line: 389,
            column: 33
          },
          end: {
            line: 389,
            column: 35
          }
        }],
        line: 389
      },
      "44": {
        loc: {
          start: {
            line: 389,
            column: 40
          },
          end: {
            line: 389,
            column: 60
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 389,
            column: 40
          },
          end: {
            line: 389,
            column: 54
          }
        }, {
          start: {
            line: 389,
            column: 58
          },
          end: {
            line: 389,
            column: 60
          }
        }],
        line: 389
      },
      "45": {
        loc: {
          start: {
            line: 392,
            column: 25
          },
          end: {
            line: 394,
            column: 122
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 393,
            column: 14
          },
          end: {
            line: 393,
            column: 36
          }
        }, {
          start: {
            line: 394,
            column: 14
          },
          end: {
            line: 394,
            column: 122
          }
        }],
        line: 392
      },
      "46": {
        loc: {
          start: {
            line: 393,
            column: 14
          },
          end: {
            line: 393,
            column: 36
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 393,
            column: 14
          },
          end: {
            line: 393,
            column: 30
          }
        }, {
          start: {
            line: 393,
            column: 34
          },
          end: {
            line: 393,
            column: 36
          }
        }],
        line: 393
      },
      "47": {
        loc: {
          start: {
            line: 394,
            column: 80
          },
          end: {
            line: 394,
            column: 99
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 394,
            column: 80
          },
          end: {
            line: 394,
            column: 93
          }
        }, {
          start: {
            line: 394,
            column: 97
          },
          end: {
            line: 394,
            column: 99
          }
        }],
        line: 394
      },
      "48": {
        loc: {
          start: {
            line: 394,
            column: 101
          },
          end: {
            line: 394,
            column: 121
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 394,
            column: 101
          },
          end: {
            line: 394,
            column: 115
          }
        }, {
          start: {
            line: 394,
            column: 119
          },
          end: {
            line: 394,
            column: 121
          }
        }],
        line: 394
      },
      "49": {
        loc: {
          start: {
            line: 396,
            column: 75
          },
          end: {
            line: 396,
            column: 94
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 396,
            column: 75
          },
          end: {
            line: 396,
            column: 89
          }
        }, {
          start: {
            line: 396,
            column: 93
          },
          end: {
            line: 396,
            column: 94
          }
        }],
        line: 396
      },
      "50": {
        loc: {
          start: {
            line: 415,
            column: 21
          },
          end: {
            line: 417,
            column: 68
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 416,
            column: 14
          },
          end: {
            line: 416,
            column: 70
          }
        }, {
          start: {
            line: 417,
            column: 14
          },
          end: {
            line: 417,
            column: 68
          }
        }],
        line: 415
      },
      "51": {
        loc: {
          start: {
            line: 416,
            column: 34
          },
          end: {
            line: 416,
            column: 56
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 416,
            column: 34
          },
          end: {
            line: 416,
            column: 50
          }
        }, {
          start: {
            line: 416,
            column: 54
          },
          end: {
            line: 416,
            column: 56
          }
        }],
        line: 416
      },
      "52": {
        loc: {
          start: {
            line: 417,
            column: 16
          },
          end: {
            line: 417,
            column: 35
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 417,
            column: 16
          },
          end: {
            line: 417,
            column: 29
          }
        }, {
          start: {
            line: 417,
            column: 33
          },
          end: {
            line: 417,
            column: 35
          }
        }],
        line: 417
      },
      "53": {
        loc: {
          start: {
            line: 417,
            column: 40
          },
          end: {
            line: 417,
            column: 60
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 417,
            column: 40
          },
          end: {
            line: 417,
            column: 54
          }
        }, {
          start: {
            line: 417,
            column: 58
          },
          end: {
            line: 417,
            column: 60
          }
        }],
        line: 417
      },
      "54": {
        loc: {
          start: {
            line: 423,
            column: 29
          },
          end: {
            line: 423,
            column: 54
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 423,
            column: 29
          },
          end: {
            line: 423,
            column: 48
          }
        }, {
          start: {
            line: 423,
            column: 52
          },
          end: {
            line: 423,
            column: 54
          }
        }],
        line: 423
      },
      "55": {
        loc: {
          start: {
            line: 425,
            column: 26
          },
          end: {
            line: 425,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 425,
            column: 26
          },
          end: {
            line: 425,
            column: 43
          }
        }, {
          start: {
            line: 425,
            column: 47
          },
          end: {
            line: 425,
            column: 48
          }
        }],
        line: 425
      },
      "56": {
        loc: {
          start: {
            line: 426,
            column: 26
          },
          end: {
            line: 426,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 426,
            column: 26
          },
          end: {
            line: 426,
            column: 42
          }
        }, {
          start: {
            line: 426,
            column: 46
          },
          end: {
            line: 426,
            column: 48
          }
        }],
        line: 426
      },
      "57": {
        loc: {
          start: {
            line: 454,
            column: 8
          },
          end: {
            line: 456,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 454,
            column: 8
          },
          end: {
            line: 456,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 454
      },
      "58": {
        loc: {
          start: {
            line: 476,
            column: 25
          },
          end: {
            line: 478,
            column: 72
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 477,
            column: 18
          },
          end: {
            line: 477,
            column: 74
          }
        }, {
          start: {
            line: 478,
            column: 18
          },
          end: {
            line: 478,
            column: 72
          }
        }],
        line: 476
      },
      "59": {
        loc: {
          start: {
            line: 477,
            column: 38
          },
          end: {
            line: 477,
            column: 60
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 477,
            column: 38
          },
          end: {
            line: 477,
            column: 54
          }
        }, {
          start: {
            line: 477,
            column: 58
          },
          end: {
            line: 477,
            column: 60
          }
        }],
        line: 477
      },
      "60": {
        loc: {
          start: {
            line: 478,
            column: 20
          },
          end: {
            line: 478,
            column: 39
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 478,
            column: 20
          },
          end: {
            line: 478,
            column: 33
          }
        }, {
          start: {
            line: 478,
            column: 37
          },
          end: {
            line: 478,
            column: 39
          }
        }],
        line: 478
      },
      "61": {
        loc: {
          start: {
            line: 478,
            column: 44
          },
          end: {
            line: 478,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 478,
            column: 44
          },
          end: {
            line: 478,
            column: 58
          }
        }, {
          start: {
            line: 478,
            column: 62
          },
          end: {
            line: 478,
            column: 64
          }
        }],
        line: 478
      },
      "62": {
        loc: {
          start: {
            line: 483,
            column: 15
          },
          end: {
            line: 483,
            column: 52
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 483,
            column: 27
          },
          end: {
            line: 483,
            column: 48
          }
        }, {
          start: {
            line: 483,
            column: 51
          },
          end: {
            line: 483,
            column: 52
          }
        }],
        line: 483
      },
      "63": {
        loc: {
          start: {
            line: 490,
            column: 8
          },
          end: {
            line: 492,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 490,
            column: 8
          },
          end: {
            line: 492,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 490
      },
      "64": {
        loc: {
          start: {
            line: 490,
            column: 12
          },
          end: {
            line: 490,
            column: 83
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 490,
            column: 12
          },
          end: {
            line: 490,
            column: 45
          }
        }, {
          start: {
            line: 490,
            column: 49
          },
          end: {
            line: 490,
            column: 83
          }
        }],
        line: 490
      },
      "65": {
        loc: {
          start: {
            line: 493,
            column: 8
          },
          end: {
            line: 495,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 493,
            column: 8
          },
          end: {
            line: 495,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 493
      },
      "66": {
        loc: {
          start: {
            line: 493,
            column: 12
          },
          end: {
            line: 493,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 493,
            column: 12
          },
          end: {
            line: 493,
            column: 37
          }
        }, {
          start: {
            line: 493,
            column: 41
          },
          end: {
            line: 493,
            column: 73
          }
        }],
        line: 493
      },
      "67": {
        loc: {
          start: {
            line: 496,
            column: 8
          },
          end: {
            line: 498,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 496,
            column: 8
          },
          end: {
            line: 498,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 496
      },
      "68": {
        loc: {
          start: {
            line: 496,
            column: 12
          },
          end: {
            line: 496,
            column: 71
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 496,
            column: 12
          },
          end: {
            line: 496,
            column: 37
          }
        }, {
          start: {
            line: 496,
            column: 41
          },
          end: {
            line: 496,
            column: 71
          }
        }],
        line: 496
      },
      "69": {
        loc: {
          start: {
            line: 509,
            column: 66
          },
          end: {
            line: 509,
            column: 81
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 509,
            column: 66
          },
          end: {
            line: 509,
            column: 76
          }
        }, {
          start: {
            line: 509,
            column: 80
          },
          end: {
            line: 509,
            column: 81
          }
        }],
        line: 509
      },
      "70": {
        loc: {
          start: {
            line: 511,
            column: 34
          },
          end: {
            line: 512,
            column: 50
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 511,
            column: 34
          },
          end: {
            line: 511,
            column: 72
          }
        }, {
          start: {
            line: 512,
            column: 12
          },
          end: {
            line: 512,
            column: 50
          }
        }],
        line: 511
      },
      "71": {
        loc: {
          start: {
            line: 513,
            column: 8
          },
          end: {
            line: 515,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 513,
            column: 8
          },
          end: {
            line: 515,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 513
      },
      "72": {
        loc: {
          start: {
            line: 518,
            column: 8
          },
          end: {
            line: 520,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 518,
            column: 8
          },
          end: {
            line: 520,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 518
      },
      "73": {
        loc: {
          start: {
            line: 522,
            column: 62
          },
          end: {
            line: 522,
            column: 136
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 522,
            column: 62
          },
          end: {
            line: 522,
            column: 81
          }
        }, {
          start: {
            line: 522,
            column: 85
          },
          end: {
            line: 522,
            column: 136
          }
        }],
        line: 522
      },
      "74": {
        loc: {
          start: {
            line: 523,
            column: 8
          },
          end: {
            line: 525,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 523,
            column: 8
          },
          end: {
            line: 525,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 523
      },
      "75": {
        loc: {
          start: {
            line: 527,
            column: 50
          },
          end: {
            line: 527,
            column: 101
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 527,
            column: 50
          },
          end: {
            line: 527,
            column: 69
          }
        }, {
          start: {
            line: 527,
            column: 73
          },
          end: {
            line: 527,
            column: 101
          }
        }],
        line: 527
      },
      "76": {
        loc: {
          start: {
            line: 528,
            column: 8
          },
          end: {
            line: 530,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 528,
            column: 8
          },
          end: {
            line: 530,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 528
      },
      "77": {
        loc: {
          start: {
            line: 537,
            column: 33
          },
          end: {
            line: 537,
            column: 71
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 537,
            column: 33
          },
          end: {
            line: 537,
            column: 50
          }
        }, {
          start: {
            line: 537,
            column: 54
          },
          end: {
            line: 537,
            column: 71
          }
        }],
        line: 537
      },
      "78": {
        loc: {
          start: {
            line: 564,
            column: 8
          },
          end: {
            line: 566,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 564,
            column: 8
          },
          end: {
            line: 566,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 564
      },
      "79": {
        loc: {
          start: {
            line: 571,
            column: 12
          },
          end: {
            line: 573,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 571,
            column: 12
          },
          end: {
            line: 573,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 571
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0,
      "210": 0,
      "211": 0,
      "212": 0,
      "213": 0,
      "214": 0,
      "215": 0,
      "216": 0,
      "217": 0,
      "218": 0,
      "219": 0,
      "220": 0,
      "221": 0,
      "222": 0,
      "223": 0,
      "224": 0,
      "225": 0,
      "226": 0,
      "227": 0,
      "228": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0, 0],
      "34": [0, 0],
      "35": [0, 0, 0],
      "36": [0, 0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0],
      "61": [0, 0],
      "62": [0, 0],
      "63": [0, 0],
      "64": [0, 0],
      "65": [0, 0],
      "66": [0, 0],
      "67": [0, 0],
      "68": [0, 0],
      "69": [0, 0],
      "70": [0, 0],
      "71": [0, 0],
      "72": [0, 0],
      "73": [0, 0],
      "74": [0, 0],
      "75": [0, 0],
      "76": [0, 0],
      "77": [0, 0],
      "78": [0, 0],
      "79": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\SystemPressureCalculator.ts",
      mappings: ";AAAA;;;;;;;;;GASG;;;AAEH,2DAA0E;AAC1E,mEAAyG;AACzG,uEAA0H;AAmH1H;;;GAGG;AACH,MAAa,wBAAwB;IAgBnC;;OAEG;IACI,MAAM,CAAC,uBAAuB,CAAC,MAA+B;QACnE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,GAAG,MAAM,CAAC;QAE9E,kBAAkB;QAClB,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;QAElC,8CAA8C;QAC9C,MAAM,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;QAE9D,uBAAuB;QACvB,MAAM,cAAc,GAAoB,EAAE,CAAC;QAC3C,IAAI,iBAAiB,GAAG,CAAC,CAAC;QAC1B,IAAI,cAAc,GAAG,CAAC,CAAC;QACvB,IAAI,WAAW,GAAG,CAAC,CAAC;QACpB,MAAM,UAAU,GAAa,EAAE,CAAC;QAEhC,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,MAAM,aAAa,GAAG,IAAI,CAAC,wBAAwB,CAAC,OAAO,EAAE,UAAU,EAAE,kBAAkB,CAAC,CAAC;YAC7F,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAEnC,iBAAiB,IAAI,aAAa,CAAC,YAAY,CAAC;YAChD,cAAc,IAAI,aAAa,CAAC,SAAS,CAAC;YAE1C,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;gBACnB,WAAW,IAAI,OAAO,CAAC,MAAM,CAAC;YAChC,CAAC;YAED,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAC1C,CAAC;QAED,MAAM,iBAAiB,GAAG,iBAAiB,GAAG,cAAc,CAAC;QAE7D,8BAA8B;QAC9B,MAAM,eAAe,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC;QACtF,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,CAAC;QAC5C,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,CAAC;QAE5C,oBAAoB;QACpB,MAAM,UAAU,GAAG,IAAI,CAAC,qBAAqB,CAAC;YAC5C,iBAAiB;YACjB,eAAe;YACf,WAAW;YACX,WAAW;YACX,UAAU;YACV,cAAc;SACf,CAAC,CAAC;QAEH,OAAO;YACL,iBAAiB,EAAE,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,kBAAkB,CAAC,iBAAiB,CAAC;YACjG,iBAAiB,EAAE,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,kBAAkB,CAAC,iBAAiB,CAAC;YACjG,cAAc,EAAE,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,kBAAkB,CAAC,iBAAiB,CAAC;YAC3F,WAAW;YACX,eAAe,EAAE,IAAI,CAAC,gBAAgB,CAAC,eAAe,EAAE,kBAAkB,CAAC,iBAAiB,CAAC;YAC7F,WAAW,EAAE,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,kBAAkB,CAAC,iBAAiB,CAAC;YACrF,WAAW,EAAE,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,kBAAkB,CAAC,iBAAiB,CAAC;YACrF,cAAc;YACd,cAAc,EAAE,UAAU,CAAC,QAAQ;YACnC,qBAAqB,EAAE,UAAU,CAAC,eAAe;YACjD,gBAAgB,EAAE,UAAU,CAAC,UAAU;YACvC,iBAAiB,EAAE,GAAG,kBAAkB,CAAC,cAAc,sBAAsB;YAC7E,eAAe,EAAE,IAAI,IAAI,EAAE;YAC3B,gBAAgB;SACjB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,wBAAwB,CACrC,OAAoB,EACpB,UAAkB,EAClB,OAAsD;QAGtD,MAAM,QAAQ,GAAa,EAAE,CAAC;QAC9B,MAAM,eAAe,GAAa,EAAE,CAAC;QAErC,2CAA2C;QAC3C,MAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QAC7C,MAAM,QAAQ,GAAG,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC,MAAM;QAC/C,MAAM,gBAAgB,GAAG,6CAAqB,CAAC,yBAAyB,CAAC,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC,CAAC;QAEnG,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,IAAI,cAAkC,CAAC;QACvC,IAAI,cAAkC,CAAC;QACvC,IAAI,OAA2B,CAAC;QAChC,IAAI,cAA6C,CAAC;QAElD,IAAI,OAAO,CAAC,IAAI,KAAK,UAAU,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YAClD,+CAA+C;YAC/C,MAAM,UAAU,GAAqB;gBACnC,OAAO,EAAE,OAAO,CAAC,OAAO;gBACxB,QAAQ,EAAE,OAAO,CAAC,SAAS;gBAC3B,YAAY,EAAE,IAAI,EAAE,uBAAuB;gBAC3C,KAAK,EAAE,UAAU;gBACjB,QAAQ,EAAE,OAAO,CAAC,QAAQ;aAC3B,CAAC;YAEF,2DAA2D;YAC3D,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC,KAAM,EAAE,OAAO,CAAC,MAAO,CAAC,CAAC;YACvG,YAAY,GAAG,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,OAAO,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;YAE5G,8DAA8D;YAC9D,cAAc,GAAG,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;YAC9E,cAAc,GAAG,IAAI,CAAC,uBAAuB,CAAC,cAAc,EAAE,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAE5F,CAAC;aAAM,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;YAC/D,mCAAmC;YACnC,cAAc,GAAG,6CAAqB,CAAC,oBAAoB,CACzD,OAAO,CAAC,aAAa,EACrB,QAAQ,EACR,UAAU,CACX,CAAC;YAEF,SAAS,GAAG,cAAc,CAAC,YAAY,CAAC;YACxC,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC;YACjC,QAAQ,CAAC,IAAI,CAAC,GAAG,cAAc,CAAC,QAAQ,CAAC,CAAC;YAC1C,eAAe,CAAC,IAAI,CAAC,GAAG,cAAc,CAAC,eAAe,CAAC,CAAC;QAC1D,CAAC;QAED,MAAM,SAAS,GAAG,YAAY,GAAG,SAAS,CAAC;QAE3C,2BAA2B;QAC3B,IAAI,QAAQ,GAAG,IAAI,EAAE,CAAC;YACpB,QAAQ,CAAC,IAAI,CAAC,kBAAkB,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,8BAA8B,CAAC,CAAC;YACnF,eAAe,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;QAC3E,CAAC;QAED,IAAI,QAAQ,GAAG,GAAG,EAAE,CAAC;YACnB,QAAQ,CAAC,IAAI,CAAC,iBAAiB,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,uCAAuC,CAAC,CAAC;YAC3F,eAAe,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;QAC7E,CAAC;QAED,OAAO;YACL,SAAS,EAAE,OAAO,CAAC,EAAE;YACrB,WAAW,EAAE,OAAO,CAAC,IAAI;YACzB,QAAQ,EAAE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,OAAO,CAAC,iBAAiB,CAAC;YACpE,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,OAAO,CAAC,iBAAiB,CAAC;YACpF,cAAc;YACd,YAAY,EAAE,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAAE,OAAO,CAAC,iBAAiB,CAAC;YAC5E,SAAS,EAAE,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,OAAO,CAAC,iBAAiB,CAAC;YACtE,SAAS,EAAE,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,OAAO,CAAC,iBAAiB,CAAC;YACtE,cAAc;YACd,OAAO;YACP,cAAc;YACd,QAAQ;YACR,eAAe;SAChB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,iBAAiB,CAAC,OAAoB;QACnD,IAAI,OAAO,CAAC,SAAS,KAAK,OAAO,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;YACtD,OAAO,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ;QACnE,CAAC;aAAM,IAAI,OAAO,CAAC,SAAS,KAAK,aAAa,IAAI,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YAClF,OAAO,CAAC,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,QAAQ;QACzD,CAAC;QAED,MAAM,IAAI,KAAK,CAAC,qCAAqC,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;IACrE,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,2BAA2B,CAAC,KAAa,EAAE,MAAc;QACtE,oEAAoE;QACpE,OAAO,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,MAAM,EAAE,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,MAAM,EAAE,IAAI,CAAC,CAAC;IACjF,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,mBAAmB,CAAC,UAAuD;QACxF,MAAM,EAAE,WAAW,EAAE,kBAAkB,EAAE,QAAQ,EAAE,GAAG,UAAU,CAAC;QAEjE,uDAAuD;QACvD,MAAM,eAAe,GAAG,KAAK,CAAC,CAAC,SAAS;QAExC,8CAA8C;QAC9C,MAAM,SAAS,GAAG,CAAC,MAAM,GAAG,EAAE,CAAC,GAAG,CAAC,MAAM,GAAG,WAAW,CAAC,CAAC;QAEzD,sBAAsB;QACtB,MAAM,aAAa,GAAG,kBAAkB,GAAG,KAAK,CAAC;QAEjD,oCAAoC;QACpC,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,CAAC;QAElD,OAAO,eAAe,GAAG,SAAS,GAAG,aAAa,GAAG,aAAa,CAAC;IACrE,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,qBAAqB,CAClC,QAAgB,EAChB,MAAc,EACd,QAAgB,EAChB,QAAgB,EAChB,UAAkB;QAElB,qEAAqE;QACrE,MAAM,YAAY,GAAG,qCAAiB,CAAC,uBAAuB,CAAC,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;QACtG,MAAM,YAAY,GAAG,UAAU,GAAG,KAAK,CAAC;QACxC,OAAO,YAAY,GAAG,YAAY,CAAC;IACrC,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,uBAAuB,CAAC,QAAgB,EAAE,QAAgB,EAAE,UAAkB;QAC3F,MAAM,WAAW,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAC,aAAa;QAChD,MAAM,UAAU,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAC,iBAAiB;QACnD,MAAM,kBAAkB,GAAG,OAAO,CAAC,CAAC,+BAA+B;QAEnE,OAAO,CAAC,WAAW,GAAG,UAAU,CAAC,GAAG,kBAAkB,CAAC;IACzD,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,uBAAuB,CAAC,QAAgB,EAAE,QAAgB,EAAE,QAAgB;QACzF,0CAA0C;QAC1C,OAAO,qCAAiB,CAAC,yBAAyB,CAAC,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;IACpF,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,oBAAoB,CAAC,MAA+B;QACjE,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrD,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;QAC9D,CAAC;QAED,KAAK,MAAM,OAAO,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;YACtC,IAAI,OAAO,CAAC,OAAO,IAAI,CAAC,EAAE,CAAC;gBACzB,MAAM,IAAI,KAAK,CAAC,+BAA+B,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;YAC/D,CAAC;YAED,IAAI,OAAO,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC,CAAC,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,IAAI,CAAC,CAAC,EAAE,CAAC;gBAC5E,MAAM,IAAI,KAAK,CAAC,oBAAoB,OAAO,CAAC,EAAE,4BAA4B,CAAC,CAAC;YAC9E,CAAC;YAED,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;gBACzD,MAAM,IAAI,KAAK,CAAC,mBAAmB,OAAO,CAAC,EAAE,kCAAkC,CAAC,CAAC;YACnF,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,qBAAqB,CAAC,IAOpC;QAKC,MAAM,QAAQ,GAAa,EAAE,CAAC;QAC9B,MAAM,eAAe,GAAa,EAAE,CAAC;QAErC,MAAM,cAAc,GAAG,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACpE,MAAM,cAAc,GAAG,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAEpE,sBAAsB;QACtB,MAAM,iBAAiB,GAAG,IAAI,CAAC,WAAW,IAAI,cAAc,CAAC,GAAG,IAAI,IAAI,CAAC,WAAW,IAAI,cAAc,CAAC,GAAG,CAAC;QAC3G,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACvB,QAAQ,CAAC,IAAI,CAAC,4CAA4C,cAAc,CAAC,GAAG,IAAI,cAAc,CAAC,GAAG,OAAO,CAAC,CAAC;YAC3G,eAAe,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;QACvE,CAAC;QAED,sBAAsB;QACtB,MAAM,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,IAAI,cAAc,CAAC,GAAG,CAAC;QACvE,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACvB,QAAQ,CAAC,IAAI,CAAC,yBAAyB,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC,iCAAiC,cAAc,CAAC,GAAG,SAAS,CAAC,CAAC;YACtI,eAAe,CAAC,IAAI,CAAC,8EAA8E,CAAC,CAAC;QACvG,CAAC;QAED,4BAA4B;QAC5B,MAAM,eAAe,GAAG,iBAAiB,IAAI,iBAAiB,CAAC;QAE/D,iCAAiC;QACjC,IAAI,IAAI,CAAC,iBAAiB,GAAG,cAAc,CAAC,WAAW,EAAE,CAAC;YACxD,eAAe,CAAC,IAAI,CAAC,yCAAyC,cAAc,CAAC,WAAW,+BAA+B,CAAC,CAAC;QAC3H,CAAC;QAED,IAAI,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC,WAAW,EAAE,CAAC;YACtD,eAAe,CAAC,IAAI,CAAC,4CAA4C,cAAc,CAAC,WAAW,wBAAwB,CAAC,CAAC;QACvH,CAAC;QAED,OAAO;YACL,QAAQ;YACR,eAAe;YACf,UAAU,EAAE,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,eAAe,EAAE;SACtE,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,gBAAgB,CAAC,KAAa,EAAE,SAAiB;QAC9D,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;QACvC,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC,GAAG,MAAM,CAAC;IAC7C,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,eAAe,CAAC,UAAkB;QAI9C,OAAO;YACL,QAAQ,EAAE,IAAI,CAAC,sBAAsB,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,sBAAsB,CAAC,MAAM;YACvF,QAAQ,EAAE,IAAI,CAAC,sBAAsB,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,sBAAsB,CAAC,MAAM;SACxF,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,+BAA+B,CAAC,MAA+B;QAC3E,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,GAAG,MAAM,CAAC;QAE9E,kBAAkB;QAClB,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;QAElC,MAAM,OAAO,GAAoB,EAAE,CAAC;QACpC,IAAI,iBAAiB,GAAG,CAAC,CAAC;QAC1B,IAAI,iBAAiB,GAAG,CAAC,CAAC;QAC1B,IAAI,gBAAgB,GAAG,CAAC,CAAC;QACzB,IAAI,kBAAkB,GAAG,CAAC,CAAC;QAC3B,MAAM,QAAQ,GAAa,EAAE,CAAC;QAC9B,MAAM,KAAK,GAAa,EAAE,CAAC;QAE3B,kDAAkD;QAClD,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,MAAM,aAAa,GAAG,IAAI,CAAC,gCAAgC,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC;YAEvF,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC5B,iBAAiB,IAAI,aAAa,CAAC,YAAY,CAAC;YAEhD,IAAI,aAAa,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;gBACtC,iBAAiB,IAAI,aAAa,CAAC,YAAY,CAAC;YAClD,CAAC;iBAAM,IAAI,aAAa,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;gBAC5C,gBAAgB,IAAI,aAAa,CAAC,YAAY,CAAC;YACjD,CAAC;YAED,mCAAmC;YACnC,IAAI,OAAO,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;gBACpC,MAAM,eAAe,GAAG,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;gBACjE,kBAAkB,IAAI,eAAe,CAAC,cAAc,CAAC;gBACrD,IAAI,eAAe,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACxC,QAAQ,CAAC,IAAI,CAAC,GAAG,eAAe,CAAC,QAAQ,CAAC,CAAC;gBAC7C,CAAC;YACH,CAAC;YAED,6BAA6B;YAC7B,IAAI,aAAa,CAAC,QAAQ,EAAE,CAAC;gBAC3B,QAAQ,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC,QAAQ,CAAC,CAAC;YAC3C,CAAC;YACD,IAAI,aAAa,CAAC,KAAK,EAAE,CAAC;gBACxB,KAAK,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC;YACrC,CAAC;QACH,CAAC;QAED,iCAAiC;QACjC,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;QAC/D,MAAM,eAAe,GAAG,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QAEhE,uCAAuC;QACvC,MAAM,WAAW,GAAG,IAAI,CAAC,+BAA+B,CAAC,gBAAgB,CAAC,CAAC;QAC3E,QAAQ,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC,CAAC;QAE9B,0CAA0C;QAC1C,MAAM,QAAQ,GAAG,IAAI,CAAC,gCAAgC,CACpD,OAAO,EACP,iBAAiB,EACjB,YAAY,EACZ,UAAU,EACV,gBAAgB,CACjB,CAAC;QAEF,OAAO;YACL,iBAAiB,EAAE,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,GAAG,kBAAkB,EAAE,CAAC,CAAC;YACnF,YAAY,EAAE,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,CAAC,CAAC;YACzD,WAAW,EAAE,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,CAAC,CAAC;YACvD,aAAa,EAAE,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,CAAC,CAAC;YAC3D,QAAQ,EAAE,OAAO;YACjB,aAAa,EAAE;gBACb,YAAY;gBACZ,eAAe,EAAE,IAAI,CAAC,gBAAgB,CAAC,eAAe,EAAE,CAAC,CAAC;gBAC1D,kBAAkB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC,iBAAiB,GAAG,iBAAiB,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC;gBAC3F,iBAAiB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC,gBAAgB,GAAG,iBAAiB,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC;gBACzF,mBAAmB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC,kBAAkB,GAAG,iBAAiB,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC;gBAC7F,gBAAgB,EAAE,IAAI,CAAC,yBAAyB,CAAC,iBAAiB,EAAE,gBAAgB,CAAC;aACtF;YACD,QAAQ;YACR,QAAQ;YACR,KAAK;YACL,iBAAiB,EAAE,yCAAyC;YAC5D,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,gCAAgC,CAC7C,OAAoB,EACpB,gBAAqB;QAErB,MAAM,QAAQ,GAAa,EAAE,CAAC;QAC9B,MAAM,KAAK,GAAa,EAAE,CAAC;QAE3B,4CAA4C;QAC5C,MAAM,aAAa,GAAkB;YACnC,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,gBAAgB,CAAC,WAAW,IAAI,EAAE;YACtE,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,gBAAgB,CAAC,kBAAkB;YACjE,QAAQ,EAAE,OAAO,CAAC,SAAS,IAAI,gBAAgB,CAAC,QAAQ,IAAI,CAAC;YAC7D,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,gBAAgB,CAAC,QAAQ,IAAI,EAAE;SAC9D,CAAC;QAEF,8BAA8B;QAC9B,MAAM,QAAQ,GAAG,iDAAuB,CAAC,sBAAsB,CAAC,aAAa,CAAC,CAAC;QAC/E,QAAQ,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACpC,KAAK,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;QAE9B,IAAI,OAAO,CAAC,IAAI,KAAK,UAAU,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YAClD,qCAAqC;YACrC,OAAO,IAAI,CAAC,6BAA6B,CAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QAChF,CAAC;aAAM,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;YAC/D,oCAAoC;YACpC,OAAO,IAAI,CAAC,4BAA4B,CAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QAC/E,CAAC;QAED,gCAAgC;QAChC,OAAO;YACL,SAAS,EAAE,OAAO,CAAC,EAAE;YACrB,IAAI,EAAE,SAAS;YACf,YAAY,EAAE,CAAC;YACf,QAAQ,EAAE,CAAC;YACX,QAAQ,EAAE,CAAC,sBAAsB,CAAC;YAClC,KAAK,EAAE,EAAE;SACV,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,6BAA6B,CAC1C,OAAoB,EACpB,QAAuB,EACvB,QAAkB,EAClB,KAAe;QAEf,kCAAkC;QAClC,MAAM,YAAY,GAAG,iDAAuB,CAAC,4BAA4B,CACvE,OAAO,CAAC,QAAQ,EAChB,OAAO,CAAC,WAAW,EACnB,OAAO,CAAC,gBAAgB,CACzB,CAAC;QAEF,QAAQ,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC;QACxC,KAAK,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;QAElC,qBAAqB;QACrB,MAAM,IAAI,GAAG,OAAO,CAAC,SAAS,KAAK,OAAO;YACxC,CAAC,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,QAAQ,IAAI,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC;YAC1D,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC;QAC3D,MAAM,QAAQ,GAAG,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;QAExC,oEAAoE;QACpE,MAAM,QAAQ,GAAG,OAAO,CAAC,SAAS,KAAK,OAAO;YAC5C,CAAC,CAAC,OAAO,CAAC,QAAQ,IAAI,EAAE;YACxB,CAAC,CAAC,qCAAiB,CAAC,2BAA2B,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE,EAAE,OAAO,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC;QAE7F,oEAAoE;QACpE,MAAM,YAAY,GAAG,IAAI,CAAC,8BAA8B,CACtD,QAAQ,EACR,OAAO,CAAC,MAAM,IAAI,CAAC,EACnB,QAAQ,EACR,YAAY,CAAC,SAAS,EACtB,QAAQ,CACT,CAAC;QAEF,OAAO;YACL,SAAS,EAAE,OAAO,CAAC,EAAE;YACrB,IAAI,EAAE,UAAU;YAChB,YAAY,EAAE,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAAE,CAAC,CAAC;YACpD,QAAQ,EAAE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC5C,kBAAkB,EAAE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,CAAC;YACtD,iBAAiB,EAAE,YAAY,CAAC,SAAS;YACzC,UAAU,EAAE,QAAQ,CAAC,OAAO;YAC5B,iBAAiB,EAAE,QAAQ,CAAC,iBAAiB;YAC7C,QAAQ;YACR,KAAK;SACN,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,4BAA4B,CACzC,OAAoB,EACpB,QAAuB,EACvB,QAAkB,EAClB,KAAe;QAEf,qBAAqB;QACrB,MAAM,IAAI,GAAG,OAAO,CAAC,SAAS,KAAK,OAAO;YACxC,CAAC,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,QAAQ,IAAI,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC;YAC1D,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC;QAC3D,MAAM,QAAQ,GAAG,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;QAExC,6DAA6D;QAC7D,MAAM,QAAQ,GAA2B;YACvC,QAAQ;YACR,aAAa,EAAE;gBACb,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,EAAE;gBACtC,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,QAAQ,EAAE,OAAO,CAAC,SAAS,IAAI,CAAC;gBAChC,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,EAAE;aACjC;SACF,CAAC;QAEF,MAAM,QAAQ,GAAG,iDAAuB,CAAC,yBAAyB,CAAC,QAAQ,CAAC,CAAC;QAC7E,QAAQ,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAEpC,0DAA0D;QAC1D,MAAM,aAAa,GAAG,6CAAqB,CAAC,oBAAoB,CAC9D,OAAO,CAAC,aAAc,EACtB,QAAQ,EACR,QAAQ,CAAC,OAAO,CACjB,CAAC;QAEF,qCAAqC;QACrC,MAAM,qBAAqB,GAAG,aAAa,CAAC,YAAY;YACtD,CAAC,QAAQ,CAAC,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;QAE7D,OAAO;YACL,SAAS,EAAE,OAAO,CAAC,EAAE;YACrB,IAAI,EAAE,SAAS;YACf,YAAY,EAAE,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,EAAE,CAAC,CAAC;YAC7D,QAAQ,EAAE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC5C,OAAO,EAAE,aAAa,CAAC,OAAO;YAC9B,gBAAgB,EAAE,QAAQ,CAAC,gBAAgB;YAC3C,UAAU,EAAE,QAAQ,CAAC,OAAO;YAC5B,iBAAiB,EAAE,QAAQ,CAAC,iBAAiB;YAC7C,QAAQ;YACR,KAAK;SACN,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,0BAA0B,CAAC,OAAoB;QAI5D,MAAM,QAAQ,GAAa,EAAE,CAAC;QAE9B,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;YACvB,OAAO,EAAE,cAAc,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC;QACzC,CAAC;QAED,4DAA4D;QAC5D,MAAM,gBAAgB,GAAG,iDAAuB,CAAC,yBAAyB,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAC9F,QAAQ,CAAC,IAAI,CAAC,GAAG,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QAE5C,gDAAgD;QAChD,kDAAkD;QAClD,MAAM,UAAU,GAAG,KAAK,CAAC,CAAC,gCAAgC;QAC1D,MAAM,cAAc,GAAG,CAAC,UAAU,GAAG,OAAO,CAAC,SAAS,CAAC,GAAG,GAAG,CAAC,CAAC,yBAAyB;QAExF,OAAO;YACL,cAAc,EAAE,cAAc,GAAG,gBAAgB,CAAC,YAAY;YAC9D,QAAQ;SACT,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,wBAAwB,CAAC,QAAuB;QAC7D,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,IAAI,KAAK,GAAG,CAAC,CAAC;QAEd,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,MAAM,IAAI,GAAG,OAAO,CAAC,SAAS,KAAK,OAAO;gBACxC,CAAC,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,QAAQ,IAAI,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC;gBAC1D,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC;YAE3D,MAAM,QAAQ,GAAG,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;YACxC,aAAa,IAAI,QAAQ,CAAC;YAC1B,KAAK,EAAE,CAAC;QACV,CAAC;QAED,OAAO,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,+BAA+B,CAAC,gBAAqB;QAClE,MAAM,QAAQ,GAAa,EAAE,CAAC;QAE9B,IAAI,gBAAgB,CAAC,WAAW,GAAG,EAAE,IAAI,gBAAgB,CAAC,WAAW,GAAG,GAAG,EAAE,CAAC;YAC5E,QAAQ,CAAC,IAAI,CAAC,eAAe,gBAAgB,CAAC,WAAW,iCAAiC,CAAC,CAAC;QAC9F,CAAC;QAED,IAAI,gBAAgB,CAAC,QAAQ,IAAI,gBAAgB,CAAC,QAAQ,GAAG,IAAI,EAAE,CAAC;YAClE,QAAQ,CAAC,IAAI,CAAC,kBAAkB,gBAAgB,CAAC,QAAQ,mCAAmC,CAAC,CAAC;QAChG,CAAC;QAED,IAAI,gBAAgB,CAAC,QAAQ,IAAI,gBAAgB,CAAC,QAAQ,GAAG,EAAE,EAAE,CAAC;YAChE,QAAQ,CAAC,IAAI,CAAC,kBAAkB,gBAAgB,CAAC,QAAQ,8BAA8B,CAAC,CAAC;QAC3F,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,gCAAgC,CAC7C,OAAwB,EACxB,iBAAyB,EACzB,YAAoB,EACpB,UAAkB,EAClB,gBAAqB;QAErB,MAAM,QAAQ,GAAa,EAAE,CAAC;QAC9B,MAAM,eAAe,GAAa,EAAE,CAAC;QAErC,gCAAgC;QAChC,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;QAChD,MAAM,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC;QAEhG,4BAA4B;QAC5B,MAAM,iBAAiB,GAAG,eAAe,IAAI,MAAM,CAAC,QAAQ,CAAC,GAAG;YACvC,eAAe,IAAI,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC;QAEhE,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACvB,QAAQ,CAAC,IAAI,CAAC,oBAAoB,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,mCAAmC,CAAC,CAAC;QACnG,CAAC;QAED,4BAA4B;QAC5B,MAAM,iBAAiB,GAAG,iBAAiB,IAAI,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC;QAEnE,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACvB,QAAQ,CAAC,IAAI,CAAC,uBAAuB,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC,wBAAwB,CAAC,CAAC;QAC7F,CAAC;QAED,gCAAgC;QAChC,MAAM,2BAA2B,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CACnD,CAAC,CAAC,iBAAiB,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,iBAAiB,CAAC,QAAQ,GAAG,GAAG,CAAC,GAAG,IAAI,CAC3E,CAAC;QAEF,IAAI,2BAA2B,EAAE,CAAC;YAChC,eAAe,CAAC,IAAI,CAAC,kEAAkE,CAAC,CAAC;QAC3F,CAAC;QAED,0BAA0B;QAC1B,MAAM,eAAe,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,iBAAiB,IAAI,CAAC,CAAC,iBAAiB,GAAG,MAAM,CAAC,CAAC;QAE/F,IAAI,eAAe,EAAE,CAAC;YACpB,eAAe,CAAC,IAAI,CAAC,0DAA0D,CAAC,CAAC;QACnF,CAAC;QAED,OAAO;YACL,QAAQ;YACR,eAAe;YACf,UAAU,EAAE;gBACV,iBAAiB;gBACjB,iBAAiB;gBACjB,eAAe,EAAE,iBAAiB,IAAI,iBAAiB;aACxD;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,8BAA8B,CAC3C,QAAgB,EAChB,MAAc,EACd,QAAgB,EAChB,SAAiB,EACjB,QAAuB;QAEvB,gBAAgB;QAChB,MAAM,WAAW,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAC,aAAa;QAChD,MAAM,UAAU,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAC,iBAAiB;QAEnD,qDAAqD;QACrD,MAAM,QAAQ,GAAG,CAAC,WAAW,GAAG,UAAU,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,QAAQ,CAAC,SAAS,CAAC;QAEpF,qDAAqD;QACrD,MAAM,iBAAiB,GAAG,SAAS,GAAG,UAAU,CAAC;QACjD,MAAM,cAAc,GAAG,IAAI,CAAC,+BAA+B,CAAC,QAAQ,EAAE,iBAAiB,CAAC,CAAC;QAEzF,qDAAqD;QACrD,MAAM,eAAe,GAAG,cAAc,GAAG,CAAC,MAAM,GAAG,UAAU,CAAC;YACvC,CAAC,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;QAEpF,yBAAyB;QACzB,OAAO,eAAe,GAAG,GAAG,CAAC;IAC/B,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,+BAA+B,CAAC,QAAgB,EAAE,iBAAyB;QACxF,mBAAmB;QACnB,IAAI,QAAQ,GAAG,IAAI,EAAE,CAAC;YACpB,OAAO,EAAE,GAAG,QAAQ,CAAC;QACvB,CAAC;QAED,qEAAqE;QACrE,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,gBAAgB;QAE9B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;YAC5B,MAAM,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,GAAG,GAAG,IAAI,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAE1G,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,MAAM,EAAE,CAAC;gBAChC,MAAM;YACR,CAAC;YACD,CAAC,GAAG,IAAI,CAAC;QACX,CAAC;QAED,OAAO,CAAC,CAAC;IACX,CAAC;;AAxvBH,4DAyvBC;AAvvBC,8CAA8C;AACtB,+CAAsB,GAAG;IAC/C,MAAM,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,WAAW,EAAE,GAAG,EAAE;IACtC,MAAM,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,WAAW,EAAE,GAAG,EAAE;IACtC,OAAO,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,WAAW,EAAE,GAAG,EAAE;CACxC,CAAC;AAEF,8CAA8C;AACtB,+CAAsB,GAAG;IAC/C,MAAM,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE;IAClD,MAAM,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE;IAClD,OAAO,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE;CACpD,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\SystemPressureCalculator.ts"],
      sourcesContent: ["/**\r\n * SystemPressureCalculator - Modular calculation service for complete HVAC system pressure analysis\r\n * \r\n * MISSION-CRITICAL: Pure TypeScript functions for system-level pressure drop calculations\r\n * Combines friction losses (straight runs) with minor losses (fittings) for complete system analysis\r\n * \r\n * @see docs/implementation/duct-physics/system-pressure-calculations.md\r\n * @see backend/services/calculations/AirDuctCalculator.ts\r\n * @see backend/services/calculations/FittingLossCalculator.ts\r\n */\r\n\r\nimport { AirDuctCalculator, DuctSizingInputs } from './AirDuctCalculator';\r\nimport { FittingLossCalculator, FittingConfiguration, FittingLossResult } from './FittingLossCalculator';\r\nimport { AirPropertiesCalculator, AirConditions, AirProperties, VelocityPressureParams } from './AirPropertiesCalculator';\r\n\r\n/**\r\n * Duct segment for system calculations\r\n */\r\nexport interface DuctSegment {\r\n  id: string;\r\n  type: 'straight' | 'fitting';\r\n  ductShape: 'round' | 'rectangular';\r\n  \r\n  // Geometry\r\n  length?: number; // feet (for straight segments)\r\n  diameter?: number; // inches (for round ducts)\r\n  width?: number; // inches (for rectangular ducts)\r\n  height?: number; // inches (for rectangular ducts)\r\n  \r\n  // Flow properties\r\n  airflow: number; // CFM\r\n  material: string; // e.g., 'galvanized_steel'\r\n  \r\n  // Fitting properties (for fitting segments)\r\n  fittingConfig?: FittingConfiguration;\r\n  \r\n  // Environmental conditions\r\n  elevation?: number; // feet (for elevation pressure calculations)\r\n  temperature?: number; // \xB0F (for air density corrections)\r\n  humidity?: number; // % RH (for air density corrections)\r\n  pressure?: number; // in Hg (for air density corrections)\r\n\r\n  // Material aging and condition\r\n  materialAge?: number; // years (for roughness corrections)\r\n  surfaceCondition?: 'excellent' | 'good' | 'fair' | 'poor'; // for roughness corrections\r\n\r\n  notes?: string;\r\n}\r\n\r\n/**\r\n * System calculation inputs\r\n */\r\nexport interface SystemCalculationInputs {\r\n  segments: DuctSegment[];\r\n  systemType: 'supply' | 'return' | 'exhaust';\r\n  designConditions: {\r\n    temperature: number; // \xB0F\r\n    barometricPressure: number; // in Hg\r\n    altitude: number; // feet above sea level\r\n  };\r\n  calculationOptions: {\r\n    includeElevationEffects: boolean;\r\n    includeTemperatureEffects: boolean;\r\n    frictionMethod: 'darcy_weisbach' | 'colebrook_white';\r\n    roundingPrecision: number; // decimal places\r\n  };\r\n}\r\n\r\n/**\r\n * Segment calculation result\r\n */\r\nexport interface SegmentResult {\r\n  segmentId: string;\r\n  segmentType: 'straight' | 'fitting';\r\n  \r\n  // Flow properties\r\n  velocity: number; // FPM\r\n  velocityPressure: number; // inches w.g.\r\n  reynoldsNumber?: number;\r\n  \r\n  // Pressure losses\r\n  frictionLoss: number; // inches w.g. (for straight segments)\r\n  minorLoss: number; // inches w.g. (for fitting segments)\r\n  totalLoss: number; // inches w.g.\r\n  \r\n  // Additional data\r\n  frictionFactor?: number;\r\n  kFactor?: number;\r\n  fittingDetails?: FittingLossResult;\r\n  \r\n  // Validation\r\n  warnings: string[];\r\n  recommendations: string[];\r\n}\r\n\r\n/**\r\n * System calculation result\r\n */\r\nexport interface SystemCalculationResult {\r\n  // Summary\r\n  totalPressureLoss: number; // inches w.g.\r\n  totalFrictionLoss: number; // inches w.g.\r\n  totalMinorLoss: number; // inches w.g.\r\n  \r\n  // System properties\r\n  totalLength: number; // feet\r\n  averageVelocity: number; // FPM\r\n  maxVelocity: number; // FPM\r\n  minVelocity: number; // FPM\r\n  \r\n  // Detailed results\r\n  segmentResults: SegmentResult[];\r\n  \r\n  // System validation\r\n  systemWarnings: string[];\r\n  systemRecommendations: string[];\r\n  complianceStatus: {\r\n    velocityCompliant: boolean;\r\n    pressureCompliant: boolean;\r\n    smacnaCompliant: boolean;\r\n  };\r\n  \r\n  // Calculation metadata\r\n  calculationMethod: string;\r\n  calculationDate: Date;\r\n  designConditions: SystemCalculationInputs['designConditions'];\r\n}\r\n\r\n/**\r\n * SystemPressureCalculator - Pure calculation functions for system pressure analysis\r\n * CRITICAL: No dependencies on UI, storage, or external services\r\n */\r\nexport class SystemPressureCalculator {\r\n  \r\n  // SMACNA system pressure limits (inches w.g.)\r\n  private static readonly SYSTEM_PRESSURE_LIMITS = {\r\n    supply: { max: 6.0, recommended: 4.0 },\r\n    return: { max: 4.0, recommended: 2.5 },\r\n    exhaust: { max: 8.0, recommended: 5.0 }\r\n  };\r\n\r\n  // SMACNA velocity limits by system type (FPM)\r\n  private static readonly SYSTEM_VELOCITY_LIMITS = {\r\n    supply: { min: 400, max: 2500, recommended: 1500 },\r\n    return: { min: 300, max: 2000, recommended: 1200 },\r\n    exhaust: { min: 500, max: 3000, recommended: 1800 }\r\n  };\r\n\r\n  /**\r\n   * Calculate complete system pressure drop\r\n   */\r\n  public static calculateSystemPressure(inputs: SystemCalculationInputs): SystemCalculationResult {\r\n    const { segments, systemType, designConditions, calculationOptions } = inputs;\r\n    \r\n    // Validate inputs\r\n    this.validateSystemInputs(inputs);\r\n    \r\n    // Calculate air density for design conditions\r\n    const airDensity = this.calculateAirDensity(designConditions);\r\n    \r\n    // Process each segment\r\n    const segmentResults: SegmentResult[] = [];\r\n    let totalFrictionLoss = 0;\r\n    let totalMinorLoss = 0;\r\n    let totalLength = 0;\r\n    const velocities: number[] = [];\r\n    \r\n    for (const segment of segments) {\r\n      const segmentResult = this.calculateSegmentPressure(segment, airDensity, calculationOptions);\r\n      segmentResults.push(segmentResult);\r\n      \r\n      totalFrictionLoss += segmentResult.frictionLoss;\r\n      totalMinorLoss += segmentResult.minorLoss;\r\n      \r\n      if (segment.length) {\r\n        totalLength += segment.length;\r\n      }\r\n      \r\n      velocities.push(segmentResult.velocity);\r\n    }\r\n    \r\n    const totalPressureLoss = totalFrictionLoss + totalMinorLoss;\r\n    \r\n    // Calculate system statistics\r\n    const averageVelocity = velocities.reduce((sum, v) => sum + v, 0) / velocities.length;\r\n    const maxVelocity = Math.max(...velocities);\r\n    const minVelocity = Math.min(...velocities);\r\n    \r\n    // System validation\r\n    const validation = this.validateSystemResults({\r\n      totalPressureLoss,\r\n      averageVelocity,\r\n      maxVelocity,\r\n      minVelocity,\r\n      systemType,\r\n      segmentResults\r\n    });\r\n    \r\n    return {\r\n      totalPressureLoss: this.roundToPrecision(totalPressureLoss, calculationOptions.roundingPrecision),\r\n      totalFrictionLoss: this.roundToPrecision(totalFrictionLoss, calculationOptions.roundingPrecision),\r\n      totalMinorLoss: this.roundToPrecision(totalMinorLoss, calculationOptions.roundingPrecision),\r\n      totalLength,\r\n      averageVelocity: this.roundToPrecision(averageVelocity, calculationOptions.roundingPrecision),\r\n      maxVelocity: this.roundToPrecision(maxVelocity, calculationOptions.roundingPrecision),\r\n      minVelocity: this.roundToPrecision(minVelocity, calculationOptions.roundingPrecision),\r\n      segmentResults,\r\n      systemWarnings: validation.warnings,\r\n      systemRecommendations: validation.recommendations,\r\n      complianceStatus: validation.compliance,\r\n      calculationMethod: `${calculationOptions.frictionMethod}_with_fitting_losses`,\r\n      calculationDate: new Date(),\r\n      designConditions\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate pressure drop for a single segment\r\n   */\r\n  private static calculateSegmentPressure(\r\n    segment: DuctSegment,\r\n    airDensity: number,\r\n    options: SystemCalculationInputs['calculationOptions']\r\n  ): SegmentResult {\r\n    \r\n    const warnings: string[] = [];\r\n    const recommendations: string[] = [];\r\n    \r\n    // Calculate velocity and velocity pressure\r\n    const area = this.calculateDuctArea(segment);\r\n    const velocity = segment.airflow / area; // FPM\r\n    const velocityPressure = FittingLossCalculator.calculateVelocityPressure({ velocity, airDensity });\r\n    \r\n    let frictionLoss = 0;\r\n    let minorLoss = 0;\r\n    let reynoldsNumber: number | undefined;\r\n    let frictionFactor: number | undefined;\r\n    let kFactor: number | undefined;\r\n    let fittingDetails: FittingLossResult | undefined;\r\n    \r\n    if (segment.type === 'straight' && segment.length) {\r\n      // Calculate friction loss for straight segment\r\n      const ductInputs: DuctSizingInputs = {\r\n        airflow: segment.airflow,\r\n        ductType: segment.ductShape,\r\n        frictionRate: 0.08, // Will be recalculated\r\n        units: 'imperial',\r\n        material: segment.material\r\n      };\r\n      \r\n      // Use existing AirDuctCalculator for friction calculations\r\n      const diameter = segment.diameter || this.calculateEquivalentDiameter(segment.width!, segment.height!);\r\n      frictionLoss = this.calculateFrictionLoss(velocity, segment.length, diameter, segment.material, airDensity);\r\n      \r\n      // Calculate Reynolds number and friction factor for reference\r\n      reynoldsNumber = this.calculateReynoldsNumber(velocity, diameter, airDensity);\r\n      frictionFactor = this.calculateFrictionFactor(reynoldsNumber, segment.material, diameter);\r\n      \r\n    } else if (segment.type === 'fitting' && segment.fittingConfig) {\r\n      // Calculate minor loss for fitting\r\n      fittingDetails = FittingLossCalculator.calculateFittingLoss(\r\n        segment.fittingConfig,\r\n        velocity,\r\n        airDensity\r\n      );\r\n      \r\n      minorLoss = fittingDetails.pressureLoss;\r\n      kFactor = fittingDetails.kFactor;\r\n      warnings.push(...fittingDetails.warnings);\r\n      recommendations.push(...fittingDetails.recommendations);\r\n    }\r\n    \r\n    const totalLoss = frictionLoss + minorLoss;\r\n    \r\n    // Segment-level validation\r\n    if (velocity > 3000) {\r\n      warnings.push(`High velocity (${velocity.toFixed(0)} FPM) may cause noise issues`);\r\n      recommendations.push('Consider increasing duct size to reduce velocity');\r\n    }\r\n    \r\n    if (velocity < 300) {\r\n      warnings.push(`Low velocity (${velocity.toFixed(0)} FPM) may cause poor air distribution`);\r\n      recommendations.push('Consider decreasing duct size to increase velocity');\r\n    }\r\n    \r\n    return {\r\n      segmentId: segment.id,\r\n      segmentType: segment.type,\r\n      velocity: this.roundToPrecision(velocity, options.roundingPrecision),\r\n      velocityPressure: this.roundToPrecision(velocityPressure, options.roundingPrecision),\r\n      reynoldsNumber,\r\n      frictionLoss: this.roundToPrecision(frictionLoss, options.roundingPrecision),\r\n      minorLoss: this.roundToPrecision(minorLoss, options.roundingPrecision),\r\n      totalLoss: this.roundToPrecision(totalLoss, options.roundingPrecision),\r\n      frictionFactor,\r\n      kFactor,\r\n      fittingDetails,\r\n      warnings,\r\n      recommendations\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate duct cross-sectional area\r\n   */\r\n  private static calculateDuctArea(segment: DuctSegment): number {\r\n    if (segment.ductShape === 'round' && segment.diameter) {\r\n      return Math.PI * Math.pow(segment.diameter / 12, 2) / 4; // sq ft\r\n    } else if (segment.ductShape === 'rectangular' && segment.width && segment.height) {\r\n      return (segment.width * segment.height) / 144; // sq ft\r\n    }\r\n    \r\n    throw new Error(`Invalid duct geometry for segment ${segment.id}`);\r\n  }\r\n\r\n  /**\r\n   * Calculate equivalent diameter for rectangular ducts\r\n   */\r\n  private static calculateEquivalentDiameter(width: number, height: number): number {\r\n    // Equivalent diameter formula: De = 1.30 * (a*b)^0.625 / (a+b)^0.25\r\n    return 1.30 * Math.pow(width * height, 0.625) / Math.pow(width + height, 0.25);\r\n  }\r\n\r\n  /**\r\n   * Calculate air density based on design conditions\r\n   */\r\n  private static calculateAirDensity(conditions: SystemCalculationInputs['designConditions']): number {\r\n    const { temperature, barometricPressure, altitude } = conditions;\r\n    \r\n    // Standard air density at 70\xB0F, 29.92 in Hg, sea level\r\n    const standardDensity = 0.075; // lb/ft\xB3\r\n    \r\n    // Temperature correction (assuming ideal gas)\r\n    const tempRatio = (459.67 + 70) / (459.67 + temperature);\r\n    \r\n    // Pressure correction\r\n    const pressureRatio = barometricPressure / 29.92;\r\n    \r\n    // Altitude correction (approximate)\r\n    const altitudeRatio = Math.exp(-altitude / 26000);\r\n    \r\n    return standardDensity * tempRatio * pressureRatio * altitudeRatio;\r\n  }\r\n\r\n  /**\r\n   * Calculate friction loss using Darcy-Weisbach equation\r\n   */\r\n  private static calculateFrictionLoss(\r\n    velocity: number,\r\n    length: number,\r\n    diameter: number,\r\n    material: string,\r\n    airDensity: number\r\n  ): number {\r\n    // Use AirDuctCalculator's existing method but adjust for air density\r\n    const standardLoss = AirDuctCalculator['calculatePressureLoss'](velocity, length, diameter, material);\r\n    const densityRatio = airDensity / 0.075;\r\n    return standardLoss * densityRatio;\r\n  }\r\n\r\n  /**\r\n   * Calculate Reynolds number\r\n   */\r\n  private static calculateReynoldsNumber(velocity: number, diameter: number, airDensity: number): number {\r\n    const velocityFps = velocity / 60; // FPM to FPS\r\n    const diameterFt = diameter / 12; // inches to feet\r\n    const kinematicViscosity = 1.57e-4; // ft\xB2/s at standard conditions\r\n    \r\n    return (velocityFps * diameterFt) / kinematicViscosity;\r\n  }\r\n\r\n  /**\r\n   * Calculate friction factor\r\n   */\r\n  private static calculateFrictionFactor(reynolds: number, material: string, diameter: number): number {\r\n    // Use AirDuctCalculator's existing method\r\n    return AirDuctCalculator['calculateFrictionFactor'](reynolds, material, diameter);\r\n  }\r\n\r\n  /**\r\n   * Validate system inputs\r\n   */\r\n  private static validateSystemInputs(inputs: SystemCalculationInputs): void {\r\n    if (!inputs.segments || inputs.segments.length === 0) {\r\n      throw new Error('System must contain at least one segment');\r\n    }\r\n    \r\n    for (const segment of inputs.segments) {\r\n      if (segment.airflow <= 0) {\r\n        throw new Error(`Invalid airflow for segment ${segment.id}`);\r\n      }\r\n      \r\n      if (segment.type === 'straight' && (!segment.length || segment.length <= 0)) {\r\n        throw new Error(`Straight segment ${segment.id} must have positive length`);\r\n      }\r\n      \r\n      if (segment.type === 'fitting' && !segment.fittingConfig) {\r\n        throw new Error(`Fitting segment ${segment.id} must have fitting configuration`);\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Validate system results\r\n   */\r\n  private static validateSystemResults(data: {\r\n    totalPressureLoss: number;\r\n    averageVelocity: number;\r\n    maxVelocity: number;\r\n    minVelocity: number;\r\n    systemType: string;\r\n    segmentResults: SegmentResult[];\r\n  }): {\r\n    warnings: string[];\r\n    recommendations: string[];\r\n    compliance: { velocityCompliant: boolean; pressureCompliant: boolean; smacnaCompliant: boolean };\r\n  } {\r\n    const warnings: string[] = [];\r\n    const recommendations: string[] = [];\r\n    \r\n    const velocityLimits = this.SYSTEM_VELOCITY_LIMITS[data.systemType];\r\n    const pressureLimits = this.SYSTEM_PRESSURE_LIMITS[data.systemType];\r\n    \r\n    // Velocity compliance\r\n    const velocityCompliant = data.maxVelocity <= velocityLimits.max && data.minVelocity >= velocityLimits.min;\r\n    if (!velocityCompliant) {\r\n      warnings.push(`System velocities outside SMACNA limits (${velocityLimits.min}-${velocityLimits.max} FPM)`);\r\n      recommendations.push('Resize ducts to achieve compliant velocities');\r\n    }\r\n    \r\n    // Pressure compliance\r\n    const pressureCompliant = data.totalPressureLoss <= pressureLimits.max;\r\n    if (!pressureCompliant) {\r\n      warnings.push(`System pressure loss (${data.totalPressureLoss.toFixed(2)} in wg) exceeds SMACNA limit (${pressureLimits.max} in wg)`);\r\n      recommendations.push('Reduce system pressure loss by optimizing duct sizes and minimizing fittings');\r\n    }\r\n    \r\n    // Overall SMACNA compliance\r\n    const smacnaCompliant = velocityCompliant && pressureCompliant;\r\n    \r\n    // Additional system-level checks\r\n    if (data.totalPressureLoss > pressureLimits.recommended) {\r\n      recommendations.push(`Consider reducing pressure loss below ${pressureLimits.recommended} in wg for optimal efficiency`);\r\n    }\r\n    \r\n    if (data.averageVelocity > velocityLimits.recommended) {\r\n      recommendations.push(`Consider reducing average velocity below ${velocityLimits.recommended} FPM for noise control`);\r\n    }\r\n    \r\n    return {\r\n      warnings,\r\n      recommendations,\r\n      compliance: { velocityCompliant, pressureCompliant, smacnaCompliant }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Round number to specified precision\r\n   */\r\n  private static roundToPrecision(value: number, precision: number): number {\r\n    const factor = Math.pow(10, precision);\r\n    return Math.round(value * factor) / factor;\r\n  }\r\n\r\n  /**\r\n   * Get system pressure limits for a system type\r\n   */\r\n  public static getSystemLimits(systemType: string): {\r\n    velocity: { min: number; max: number; recommended: number };\r\n    pressure: { max: number; recommended: number };\r\n  } {\r\n    return {\r\n      velocity: this.SYSTEM_VELOCITY_LIMITS[systemType] || this.SYSTEM_VELOCITY_LIMITS.supply,\r\n      pressure: this.SYSTEM_PRESSURE_LIMITS[systemType] || this.SYSTEM_PRESSURE_LIMITS.supply\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate enhanced system pressure with environmental corrections\r\n   */\r\n  public static calculateEnhancedSystemPressure(inputs: SystemCalculationInputs): SystemCalculationResult {\r\n    const { segments, systemType, designConditions, calculationOptions } = inputs;\r\n\r\n    // Validate inputs\r\n    this.validateSystemInputs(inputs);\r\n\r\n    const results: SegmentResult[] = [];\r\n    let totalPressureLoss = 0;\r\n    let totalFrictionLoss = 0;\r\n    let totalFittingLoss = 0;\r\n    let totalElevationLoss = 0;\r\n    const warnings: string[] = [];\r\n    const notes: string[] = [];\r\n\r\n    // Process each segment with enhanced calculations\r\n    for (const segment of segments) {\r\n      const segmentResult = this.calculateEnhancedSegmentPressure(segment, designConditions);\r\n\r\n      results.push(segmentResult);\r\n      totalPressureLoss += segmentResult.pressureLoss;\r\n\r\n      if (segmentResult.type === 'friction') {\r\n        totalFrictionLoss += segmentResult.pressureLoss;\r\n      } else if (segmentResult.type === 'fitting') {\r\n        totalFittingLoss += segmentResult.pressureLoss;\r\n      }\r\n\r\n      // Add elevation effects if present\r\n      if (segment.elevation !== undefined) {\r\n        const elevationEffect = this.calculateElevationPressure(segment);\r\n        totalElevationLoss += elevationEffect.pressureChange;\r\n        if (elevationEffect.warnings.length > 0) {\r\n          warnings.push(...elevationEffect.warnings);\r\n        }\r\n      }\r\n\r\n      // Collect warnings and notes\r\n      if (segmentResult.warnings) {\r\n        warnings.push(...segmentResult.warnings);\r\n      }\r\n      if (segmentResult.notes) {\r\n        notes.push(...segmentResult.notes);\r\n      }\r\n    }\r\n\r\n    // Calculate system-level metrics\r\n    const totalAirflow = Math.max(...segments.map(s => s.airflow));\r\n    const averageVelocity = this.calculateAverageVelocity(segments);\r\n\r\n    // Add environmental condition warnings\r\n    const envWarnings = this.validateEnvironmentalConditions(designConditions);\r\n    warnings.push(...envWarnings);\r\n\r\n    // Performance analysis with enhanced data\r\n    const analysis = this.analyzeEnhancedSystemPerformance(\r\n      results,\r\n      totalPressureLoss,\r\n      totalAirflow,\r\n      systemType,\r\n      designConditions\r\n    );\r\n\r\n    return {\r\n      totalPressureLoss: this.roundToPrecision(totalPressureLoss + totalElevationLoss, 4),\r\n      frictionLoss: this.roundToPrecision(totalFrictionLoss, 4),\r\n      fittingLoss: this.roundToPrecision(totalFittingLoss, 4),\r\n      elevationLoss: this.roundToPrecision(totalElevationLoss, 4),\r\n      segments: results,\r\n      systemMetrics: {\r\n        totalAirflow,\r\n        averageVelocity: this.roundToPrecision(averageVelocity, 1),\r\n        frictionPercentage: this.roundToPrecision((totalFrictionLoss / totalPressureLoss) * 100, 1),\r\n        fittingPercentage: this.roundToPrecision((totalFittingLoss / totalPressureLoss) * 100, 1),\r\n        elevationPercentage: this.roundToPrecision((totalElevationLoss / totalPressureLoss) * 100, 1),\r\n        systemEfficiency: this.calculateSystemEfficiency(totalFrictionLoss, totalFittingLoss)\r\n      },\r\n      analysis,\r\n      warnings,\r\n      notes,\r\n      calculationMethod: 'Enhanced with environmental corrections',\r\n      timestamp: new Date().toISOString()\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate enhanced segment pressure with environmental corrections\r\n   */\r\n  private static calculateEnhancedSegmentPressure(\r\n    segment: DuctSegment,\r\n    designConditions: any\r\n  ): SegmentResult {\r\n    const warnings: string[] = [];\r\n    const notes: string[] = [];\r\n\r\n    // Determine air conditions for this segment\r\n    const airConditions: AirConditions = {\r\n      temperature: segment.temperature || designConditions.temperature || 70,\r\n      pressure: segment.pressure || designConditions.barometricPressure,\r\n      altitude: segment.elevation || designConditions.altitude || 0,\r\n      humidity: segment.humidity || designConditions.humidity || 50\r\n    };\r\n\r\n    // Get enhanced air properties\r\n    const airProps = AirPropertiesCalculator.calculateAirProperties(airConditions);\r\n    warnings.push(...airProps.warnings);\r\n    notes.push(...airProps.notes);\r\n\r\n    if (segment.type === 'straight' && segment.length) {\r\n      // Enhanced friction loss calculation\r\n      return this.calculateEnhancedFrictionLoss(segment, airProps, warnings, notes);\r\n    } else if (segment.type === 'fitting' && segment.fittingConfig) {\r\n      // Enhanced fitting loss calculation\r\n      return this.calculateEnhancedFittingLoss(segment, airProps, warnings, notes);\r\n    }\r\n\r\n    // Fallback to basic calculation\r\n    return {\r\n      segmentId: segment.id,\r\n      type: 'unknown',\r\n      pressureLoss: 0,\r\n      velocity: 0,\r\n      warnings: ['Unknown segment type'],\r\n      notes: []\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate enhanced friction loss with material aging and environmental corrections\r\n   */\r\n  private static calculateEnhancedFrictionLoss(\r\n    segment: DuctSegment,\r\n    airProps: AirProperties,\r\n    warnings: string[],\r\n    notes: string[]\r\n  ): SegmentResult {\r\n    // Get enhanced material roughness\r\n    const materialData = AirPropertiesCalculator.getEnhancedMaterialRoughness(\r\n      segment.material,\r\n      segment.materialAge,\r\n      segment.surfaceCondition\r\n    );\r\n\r\n    warnings.push(...materialData.warnings);\r\n    notes.push(...materialData.notes);\r\n\r\n    // Calculate velocity\r\n    const area = segment.ductShape === 'round'\r\n      ? Math.PI * Math.pow((segment.diameter || 12) / 12, 2) / 4\r\n      : ((segment.width || 12) * (segment.height || 12)) / 144;\r\n    const velocity = segment.airflow / area;\r\n\r\n    // Enhanced pressure loss calculation using corrected air properties\r\n    const diameter = segment.ductShape === 'round'\r\n      ? segment.diameter || 12\r\n      : AirDuctCalculator.calculateEquivalentDiameter(segment.width || 12, segment.height || 12);\r\n\r\n    // Use enhanced calculation with corrected air density and viscosity\r\n    const pressureLoss = this.calculateCorrectedPressureLoss(\r\n      velocity,\r\n      segment.length || 0,\r\n      diameter,\r\n      materialData.roughness,\r\n      airProps\r\n    );\r\n\r\n    return {\r\n      segmentId: segment.id,\r\n      type: 'friction',\r\n      pressureLoss: this.roundToPrecision(pressureLoss, 4),\r\n      velocity: this.roundToPrecision(velocity, 1),\r\n      equivalentDiameter: this.roundToPrecision(diameter, 2),\r\n      materialRoughness: materialData.roughness,\r\n      airDensity: airProps.density,\r\n      correctionFactors: airProps.correctionFactors,\r\n      warnings,\r\n      notes\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate enhanced fitting loss with environmental corrections\r\n   */\r\n  private static calculateEnhancedFittingLoss(\r\n    segment: DuctSegment,\r\n    airProps: AirProperties,\r\n    warnings: string[],\r\n    notes: string[]\r\n  ): SegmentResult {\r\n    // Calculate velocity\r\n    const area = segment.ductShape === 'round'\r\n      ? Math.PI * Math.pow((segment.diameter || 12) / 12, 2) / 4\r\n      : ((segment.width || 12) * (segment.height || 12)) / 144;\r\n    const velocity = segment.airflow / area;\r\n\r\n    // Calculate velocity pressure with environmental corrections\r\n    const vpParams: VelocityPressureParams = {\r\n      velocity,\r\n      airConditions: {\r\n        temperature: segment.temperature || 70,\r\n        pressure: segment.pressure,\r\n        altitude: segment.elevation || 0,\r\n        humidity: segment.humidity || 50\r\n      }\r\n    };\r\n\r\n    const vpResult = AirPropertiesCalculator.calculateVelocityPressure(vpParams);\r\n    warnings.push(...vpResult.warnings);\r\n\r\n    // Calculate fitting loss using enhanced velocity pressure\r\n    const fittingResult = FittingLossCalculator.calculateFittingLoss(\r\n      segment.fittingConfig!,\r\n      velocity,\r\n      airProps.density\r\n    );\r\n\r\n    // Apply velocity pressure correction\r\n    const correctedPressureLoss = fittingResult.pressureLoss *\r\n      (vpResult.velocityPressure / Math.pow(velocity / 4005, 2));\r\n\r\n    return {\r\n      segmentId: segment.id,\r\n      type: 'fitting',\r\n      pressureLoss: this.roundToPrecision(correctedPressureLoss, 4),\r\n      velocity: this.roundToPrecision(velocity, 1),\r\n      kFactor: fittingResult.kFactor,\r\n      velocityPressure: vpResult.velocityPressure,\r\n      airDensity: airProps.density,\r\n      correctionFactors: vpResult.correctionFactors,\r\n      warnings,\r\n      notes\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate elevation pressure effects\r\n   */\r\n  private static calculateElevationPressure(segment: DuctSegment): {\r\n    pressureChange: number;\r\n    warnings: string[];\r\n  } {\r\n    const warnings: string[] = [];\r\n\r\n    if (!segment.elevation) {\r\n      return { pressureChange: 0, warnings };\r\n    }\r\n\r\n    // Calculate elevation effects using enhanced air properties\r\n    const elevationEffects = AirPropertiesCalculator.calculateElevationEffects(segment.elevation);\r\n    warnings.push(...elevationEffects.warnings);\r\n\r\n    // Pressure change due to elevation (simplified)\r\n    // \u0394P = \u03C1 \xD7 g \xD7 \u0394h / gc (converted to inches w.g.)\r\n    const airDensity = 0.075; // lb/ft\xB3 at standard conditions\r\n    const pressureChange = (airDensity * segment.elevation) / 5.2; // Convert to inches w.g.\r\n\r\n    return {\r\n      pressureChange: pressureChange * elevationEffects.densityRatio,\r\n      warnings\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate average velocity across all segments\r\n   */\r\n  private static calculateAverageVelocity(segments: DuctSegment[]): number {\r\n    let totalVelocity = 0;\r\n    let count = 0;\r\n\r\n    for (const segment of segments) {\r\n      const area = segment.ductShape === 'round'\r\n        ? Math.PI * Math.pow((segment.diameter || 12) / 12, 2) / 4\r\n        : ((segment.width || 12) * (segment.height || 12)) / 144;\r\n\r\n      const velocity = segment.airflow / area;\r\n      totalVelocity += velocity;\r\n      count++;\r\n    }\r\n\r\n    return count > 0 ? totalVelocity / count : 0;\r\n  }\r\n\r\n  /**\r\n   * Validate environmental conditions\r\n   */\r\n  private static validateEnvironmentalConditions(designConditions: any): string[] {\r\n    const warnings: string[] = [];\r\n\r\n    if (designConditions.temperature < 32 || designConditions.temperature > 200) {\r\n      warnings.push(`Temperature ${designConditions.temperature}\xB0F is outside normal HVAC range`);\r\n    }\r\n\r\n    if (designConditions.altitude && designConditions.altitude > 5000) {\r\n      warnings.push(`High altitude (${designConditions.altitude} ft) requires density corrections`);\r\n    }\r\n\r\n    if (designConditions.humidity && designConditions.humidity > 80) {\r\n      warnings.push(`High humidity (${designConditions.humidity}% RH) may cause condensation`);\r\n    }\r\n\r\n    return warnings;\r\n  }\r\n\r\n  /**\r\n   * Analyze enhanced system performance\r\n   */\r\n  private static analyzeEnhancedSystemPerformance(\r\n    results: SegmentResult[],\r\n    totalPressureLoss: number,\r\n    totalAirflow: number,\r\n    systemType: string,\r\n    designConditions: any\r\n  ): SystemAnalysis {\r\n    const warnings: string[] = [];\r\n    const recommendations: string[] = [];\r\n\r\n    // Enhanced performance analysis\r\n    const limits = this.getSystemLimits(systemType);\r\n    const averageVelocity = results.reduce((sum, r) => sum + (r.velocity || 0), 0) / results.length;\r\n\r\n    // Velocity compliance check\r\n    const velocityCompliant = averageVelocity >= limits.velocity.min &&\r\n                             averageVelocity <= limits.velocity.max;\r\n\r\n    if (!velocityCompliant) {\r\n      warnings.push(`Average velocity ${averageVelocity.toFixed(0)} FPM is outside recommended range`);\r\n    }\r\n\r\n    // Pressure compliance check\r\n    const pressureCompliant = totalPressureLoss <= limits.pressure.max;\r\n\r\n    if (!pressureCompliant) {\r\n      warnings.push(`Total pressure loss ${totalPressureLoss.toFixed(3)} in wg exceeds maximum`);\r\n    }\r\n\r\n    // Environmental impact analysis\r\n    const hasEnvironmentalCorrections = results.some(r =>\r\n      r.correctionFactors && Math.abs(r.correctionFactors.combined - 1.0) > 0.05\r\n    );\r\n\r\n    if (hasEnvironmentalCorrections) {\r\n      recommendations.push('Environmental conditions significantly affect system performance');\r\n    }\r\n\r\n    // Material aging analysis\r\n    const hasAgingEffects = results.some(r => r.materialRoughness && r.materialRoughness > 0.0005);\r\n\r\n    if (hasAgingEffects) {\r\n      recommendations.push('Consider duct cleaning or replacement for aged materials');\r\n    }\r\n\r\n    return {\r\n      warnings,\r\n      recommendations,\r\n      compliance: {\r\n        velocityCompliant,\r\n        pressureCompliant,\r\n        smacnaCompliant: velocityCompliant && pressureCompliant\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate corrected pressure loss with enhanced air properties\r\n   */\r\n  private static calculateCorrectedPressureLoss(\r\n    velocity: number,\r\n    length: number,\r\n    diameter: number,\r\n    roughness: number,\r\n    airProps: AirProperties\r\n  ): number {\r\n    // Convert units\r\n    const velocityFps = velocity / 60; // FPM to FPS\r\n    const diameterFt = diameter / 12; // inches to feet\r\n\r\n    // Calculate Reynolds number with corrected viscosity\r\n    const reynolds = (velocityFps * diameterFt * airProps.density) / airProps.viscosity;\r\n\r\n    // Calculate friction factor using enhanced roughness\r\n    const relativeRoughness = roughness / diameterFt;\r\n    const frictionFactor = this.calculateEnhancedFrictionFactor(reynolds, relativeRoughness);\r\n\r\n    // Darcy-Weisbach equation with corrected air density\r\n    const pressureLossPsf = frictionFactor * (length / diameterFt) *\r\n                           (airProps.density * Math.pow(velocityFps, 2)) / (2 * 32.174);\r\n\r\n    // Convert to inches w.g.\r\n    return pressureLossPsf / 5.2;\r\n  }\r\n\r\n  /**\r\n   * Enhanced friction factor calculation\r\n   */\r\n  private static calculateEnhancedFrictionFactor(reynolds: number, relativeRoughness: number): number {\r\n    // For laminar flow\r\n    if (reynolds < 2300) {\r\n      return 64 / reynolds;\r\n    }\r\n\r\n    // For turbulent flow - Colebrook-White equation (iterative solution)\r\n    let f = 0.02; // Initial guess\r\n\r\n    for (let i = 0; i < 10; i++) {\r\n      const fNew = 1 / Math.pow(-2 * Math.log10(relativeRoughness / 3.7 + 2.51 / (reynolds * Math.sqrt(f))), 2);\r\n\r\n      if (Math.abs(fNew - f) < 0.0001) {\r\n        break;\r\n      }\r\n      f = fNew;\r\n    }\r\n\r\n    return f;\r\n  }\r\n}\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "a2c5d7f97851efed7c411172c8b9d1d67d60aed0"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2n09p8ow7h = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2n09p8ow7h();
cov_2n09p8ow7h().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_2n09p8ow7h().s[1]++;
exports.SystemPressureCalculator = void 0;
const AirDuctCalculator_1 =
/* istanbul ignore next */
(cov_2n09p8ow7h().s[2]++, require("./AirDuctCalculator"));
const FittingLossCalculator_1 =
/* istanbul ignore next */
(cov_2n09p8ow7h().s[3]++, require("./FittingLossCalculator"));
const AirPropertiesCalculator_1 =
/* istanbul ignore next */
(cov_2n09p8ow7h().s[4]++, require("./AirPropertiesCalculator"));
/**
 * SystemPressureCalculator - Pure calculation functions for system pressure analysis
 * CRITICAL: No dependencies on UI, storage, or external services
 */
class SystemPressureCalculator {
  /**
   * Calculate complete system pressure drop
   */
  static calculateSystemPressure(inputs) {
    /* istanbul ignore next */
    cov_2n09p8ow7h().f[0]++;
    const {
      segments,
      systemType,
      designConditions,
      calculationOptions
    } =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[5]++, inputs);
    // Validate inputs
    /* istanbul ignore next */
    cov_2n09p8ow7h().s[6]++;
    this.validateSystemInputs(inputs);
    // Calculate air density for design conditions
    const airDensity =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[7]++, this.calculateAirDensity(designConditions));
    // Process each segment
    const segmentResults =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[8]++, []);
    let totalFrictionLoss =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[9]++, 0);
    let totalMinorLoss =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[10]++, 0);
    let totalLength =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[11]++, 0);
    const velocities =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[12]++, []);
    /* istanbul ignore next */
    cov_2n09p8ow7h().s[13]++;
    for (const segment of segments) {
      const segmentResult =
      /* istanbul ignore next */
      (cov_2n09p8ow7h().s[14]++, this.calculateSegmentPressure(segment, airDensity, calculationOptions));
      /* istanbul ignore next */
      cov_2n09p8ow7h().s[15]++;
      segmentResults.push(segmentResult);
      /* istanbul ignore next */
      cov_2n09p8ow7h().s[16]++;
      totalFrictionLoss += segmentResult.frictionLoss;
      /* istanbul ignore next */
      cov_2n09p8ow7h().s[17]++;
      totalMinorLoss += segmentResult.minorLoss;
      /* istanbul ignore next */
      cov_2n09p8ow7h().s[18]++;
      if (segment.length) {
        /* istanbul ignore next */
        cov_2n09p8ow7h().b[0][0]++;
        cov_2n09p8ow7h().s[19]++;
        totalLength += segment.length;
      } else
      /* istanbul ignore next */
      {
        cov_2n09p8ow7h().b[0][1]++;
      }
      cov_2n09p8ow7h().s[20]++;
      velocities.push(segmentResult.velocity);
    }
    const totalPressureLoss =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[21]++, totalFrictionLoss + totalMinorLoss);
    // Calculate system statistics
    const averageVelocity =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[22]++, velocities.reduce((sum, v) => {
      /* istanbul ignore next */
      cov_2n09p8ow7h().f[1]++;
      cov_2n09p8ow7h().s[23]++;
      return sum + v;
    }, 0) / velocities.length);
    const maxVelocity =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[24]++, Math.max(...velocities));
    const minVelocity =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[25]++, Math.min(...velocities));
    // System validation
    const validation =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[26]++, this.validateSystemResults({
      totalPressureLoss,
      averageVelocity,
      maxVelocity,
      minVelocity,
      systemType,
      segmentResults
    }));
    /* istanbul ignore next */
    cov_2n09p8ow7h().s[27]++;
    return {
      totalPressureLoss: this.roundToPrecision(totalPressureLoss, calculationOptions.roundingPrecision),
      totalFrictionLoss: this.roundToPrecision(totalFrictionLoss, calculationOptions.roundingPrecision),
      totalMinorLoss: this.roundToPrecision(totalMinorLoss, calculationOptions.roundingPrecision),
      totalLength,
      averageVelocity: this.roundToPrecision(averageVelocity, calculationOptions.roundingPrecision),
      maxVelocity: this.roundToPrecision(maxVelocity, calculationOptions.roundingPrecision),
      minVelocity: this.roundToPrecision(minVelocity, calculationOptions.roundingPrecision),
      segmentResults,
      systemWarnings: validation.warnings,
      systemRecommendations: validation.recommendations,
      complianceStatus: validation.compliance,
      calculationMethod: `${calculationOptions.frictionMethod}_with_fitting_losses`,
      calculationDate: new Date(),
      designConditions
    };
  }
  /**
   * Calculate pressure drop for a single segment
   */
  static calculateSegmentPressure(segment, airDensity, options) {
    /* istanbul ignore next */
    cov_2n09p8ow7h().f[2]++;
    const warnings =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[28]++, []);
    const recommendations =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[29]++, []);
    // Calculate velocity and velocity pressure
    const area =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[30]++, this.calculateDuctArea(segment));
    const velocity =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[31]++, segment.airflow / area); // FPM
    const velocityPressure =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[32]++, FittingLossCalculator_1.FittingLossCalculator.calculateVelocityPressure({
      velocity,
      airDensity
    }));
    let frictionLoss =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[33]++, 0);
    let minorLoss =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[34]++, 0);
    let reynoldsNumber;
    let frictionFactor;
    let kFactor;
    let fittingDetails;
    /* istanbul ignore next */
    cov_2n09p8ow7h().s[35]++;
    if (
    /* istanbul ignore next */
    (cov_2n09p8ow7h().b[2][0]++, segment.type === 'straight') &&
    /* istanbul ignore next */
    (cov_2n09p8ow7h().b[2][1]++, segment.length)) {
      /* istanbul ignore next */
      cov_2n09p8ow7h().b[1][0]++;
      // Calculate friction loss for straight segment
      const ductInputs =
      /* istanbul ignore next */
      (cov_2n09p8ow7h().s[36]++, {
        airflow: segment.airflow,
        ductType: segment.ductShape,
        frictionRate: 0.08,
        // Will be recalculated
        units: 'imperial',
        material: segment.material
      });
      // Use existing AirDuctCalculator for friction calculations
      const diameter =
      /* istanbul ignore next */
      (cov_2n09p8ow7h().s[37]++,
      /* istanbul ignore next */
      (cov_2n09p8ow7h().b[3][0]++, segment.diameter) ||
      /* istanbul ignore next */
      (cov_2n09p8ow7h().b[3][1]++, this.calculateEquivalentDiameter(segment.width, segment.height)));
      /* istanbul ignore next */
      cov_2n09p8ow7h().s[38]++;
      frictionLoss = this.calculateFrictionLoss(velocity, segment.length, diameter, segment.material, airDensity);
      // Calculate Reynolds number and friction factor for reference
      /* istanbul ignore next */
      cov_2n09p8ow7h().s[39]++;
      reynoldsNumber = this.calculateReynoldsNumber(velocity, diameter, airDensity);
      /* istanbul ignore next */
      cov_2n09p8ow7h().s[40]++;
      frictionFactor = this.calculateFrictionFactor(reynoldsNumber, segment.material, diameter);
    } else {
      /* istanbul ignore next */
      cov_2n09p8ow7h().b[1][1]++;
      cov_2n09p8ow7h().s[41]++;
      if (
      /* istanbul ignore next */
      (cov_2n09p8ow7h().b[5][0]++, segment.type === 'fitting') &&
      /* istanbul ignore next */
      (cov_2n09p8ow7h().b[5][1]++, segment.fittingConfig)) {
        /* istanbul ignore next */
        cov_2n09p8ow7h().b[4][0]++;
        cov_2n09p8ow7h().s[42]++;
        // Calculate minor loss for fitting
        fittingDetails = FittingLossCalculator_1.FittingLossCalculator.calculateFittingLoss(segment.fittingConfig, velocity, airDensity);
        /* istanbul ignore next */
        cov_2n09p8ow7h().s[43]++;
        minorLoss = fittingDetails.pressureLoss;
        /* istanbul ignore next */
        cov_2n09p8ow7h().s[44]++;
        kFactor = fittingDetails.kFactor;
        /* istanbul ignore next */
        cov_2n09p8ow7h().s[45]++;
        warnings.push(...fittingDetails.warnings);
        /* istanbul ignore next */
        cov_2n09p8ow7h().s[46]++;
        recommendations.push(...fittingDetails.recommendations);
      } else
      /* istanbul ignore next */
      {
        cov_2n09p8ow7h().b[4][1]++;
      }
    }
    const totalLoss =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[47]++, frictionLoss + minorLoss);
    // Segment-level validation
    /* istanbul ignore next */
    cov_2n09p8ow7h().s[48]++;
    if (velocity > 3000) {
      /* istanbul ignore next */
      cov_2n09p8ow7h().b[6][0]++;
      cov_2n09p8ow7h().s[49]++;
      warnings.push(`High velocity (${velocity.toFixed(0)} FPM) may cause noise issues`);
      /* istanbul ignore next */
      cov_2n09p8ow7h().s[50]++;
      recommendations.push('Consider increasing duct size to reduce velocity');
    } else
    /* istanbul ignore next */
    {
      cov_2n09p8ow7h().b[6][1]++;
    }
    cov_2n09p8ow7h().s[51]++;
    if (velocity < 300) {
      /* istanbul ignore next */
      cov_2n09p8ow7h().b[7][0]++;
      cov_2n09p8ow7h().s[52]++;
      warnings.push(`Low velocity (${velocity.toFixed(0)} FPM) may cause poor air distribution`);
      /* istanbul ignore next */
      cov_2n09p8ow7h().s[53]++;
      recommendations.push('Consider decreasing duct size to increase velocity');
    } else
    /* istanbul ignore next */
    {
      cov_2n09p8ow7h().b[7][1]++;
    }
    cov_2n09p8ow7h().s[54]++;
    return {
      segmentId: segment.id,
      segmentType: segment.type,
      velocity: this.roundToPrecision(velocity, options.roundingPrecision),
      velocityPressure: this.roundToPrecision(velocityPressure, options.roundingPrecision),
      reynoldsNumber,
      frictionLoss: this.roundToPrecision(frictionLoss, options.roundingPrecision),
      minorLoss: this.roundToPrecision(minorLoss, options.roundingPrecision),
      totalLoss: this.roundToPrecision(totalLoss, options.roundingPrecision),
      frictionFactor,
      kFactor,
      fittingDetails,
      warnings,
      recommendations
    };
  }
  /**
   * Calculate duct cross-sectional area
   */
  static calculateDuctArea(segment) {
    /* istanbul ignore next */
    cov_2n09p8ow7h().f[3]++;
    cov_2n09p8ow7h().s[55]++;
    if (
    /* istanbul ignore next */
    (cov_2n09p8ow7h().b[9][0]++, segment.ductShape === 'round') &&
    /* istanbul ignore next */
    (cov_2n09p8ow7h().b[9][1]++, segment.diameter)) {
      /* istanbul ignore next */
      cov_2n09p8ow7h().b[8][0]++;
      cov_2n09p8ow7h().s[56]++;
      return Math.PI * Math.pow(segment.diameter / 12, 2) / 4; // sq ft
    } else {
      /* istanbul ignore next */
      cov_2n09p8ow7h().b[8][1]++;
      cov_2n09p8ow7h().s[57]++;
      if (
      /* istanbul ignore next */
      (cov_2n09p8ow7h().b[11][0]++, segment.ductShape === 'rectangular') &&
      /* istanbul ignore next */
      (cov_2n09p8ow7h().b[11][1]++, segment.width) &&
      /* istanbul ignore next */
      (cov_2n09p8ow7h().b[11][2]++, segment.height)) {
        /* istanbul ignore next */
        cov_2n09p8ow7h().b[10][0]++;
        cov_2n09p8ow7h().s[58]++;
        return segment.width * segment.height / 144; // sq ft
      } else
      /* istanbul ignore next */
      {
        cov_2n09p8ow7h().b[10][1]++;
      }
    }
    /* istanbul ignore next */
    cov_2n09p8ow7h().s[59]++;
    throw new Error(`Invalid duct geometry for segment ${segment.id}`);
  }
  /**
   * Calculate equivalent diameter for rectangular ducts
   */
  static calculateEquivalentDiameter(width, height) {
    /* istanbul ignore next */
    cov_2n09p8ow7h().f[4]++;
    cov_2n09p8ow7h().s[60]++;
    // Equivalent diameter formula: De = 1.30 * (a*b)^0.625 / (a+b)^0.25
    return 1.30 * Math.pow(width * height, 0.625) / Math.pow(width + height, 0.25);
  }
  /**
   * Calculate air density based on design conditions
   */
  static calculateAirDensity(conditions) {
    /* istanbul ignore next */
    cov_2n09p8ow7h().f[5]++;
    const {
      temperature,
      barometricPressure,
      altitude
    } =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[61]++, conditions);
    // Standard air density at 70°F, 29.92 in Hg, sea level
    const standardDensity =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[62]++, 0.075); // lb/ft³
    // Temperature correction (assuming ideal gas)
    const tempRatio =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[63]++, (459.67 + 70) / (459.67 + temperature));
    // Pressure correction
    const pressureRatio =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[64]++, barometricPressure / 29.92);
    // Altitude correction (approximate)
    const altitudeRatio =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[65]++, Math.exp(-altitude / 26000));
    /* istanbul ignore next */
    cov_2n09p8ow7h().s[66]++;
    return standardDensity * tempRatio * pressureRatio * altitudeRatio;
  }
  /**
   * Calculate friction loss using Darcy-Weisbach equation
   */
  static calculateFrictionLoss(velocity, length, diameter, material, airDensity) {
    /* istanbul ignore next */
    cov_2n09p8ow7h().f[6]++;
    // Use AirDuctCalculator's existing method but adjust for air density
    const standardLoss =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[67]++, AirDuctCalculator_1.AirDuctCalculator['calculatePressureLoss'](velocity, length, diameter, material));
    const densityRatio =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[68]++, airDensity / 0.075);
    /* istanbul ignore next */
    cov_2n09p8ow7h().s[69]++;
    return standardLoss * densityRatio;
  }
  /**
   * Calculate Reynolds number
   */
  static calculateReynoldsNumber(velocity, diameter, airDensity) {
    /* istanbul ignore next */
    cov_2n09p8ow7h().f[7]++;
    const velocityFps =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[70]++, velocity / 60); // FPM to FPS
    const diameterFt =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[71]++, diameter / 12); // inches to feet
    const kinematicViscosity =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[72]++, 1.57e-4); // ft²/s at standard conditions
    /* istanbul ignore next */
    cov_2n09p8ow7h().s[73]++;
    return velocityFps * diameterFt / kinematicViscosity;
  }
  /**
   * Calculate friction factor
   */
  static calculateFrictionFactor(reynolds, material, diameter) {
    /* istanbul ignore next */
    cov_2n09p8ow7h().f[8]++;
    cov_2n09p8ow7h().s[74]++;
    // Use AirDuctCalculator's existing method
    return AirDuctCalculator_1.AirDuctCalculator['calculateFrictionFactor'](reynolds, material, diameter);
  }
  /**
   * Validate system inputs
   */
  static validateSystemInputs(inputs) {
    /* istanbul ignore next */
    cov_2n09p8ow7h().f[9]++;
    cov_2n09p8ow7h().s[75]++;
    if (
    /* istanbul ignore next */
    (cov_2n09p8ow7h().b[13][0]++, !inputs.segments) ||
    /* istanbul ignore next */
    (cov_2n09p8ow7h().b[13][1]++, inputs.segments.length === 0)) {
      /* istanbul ignore next */
      cov_2n09p8ow7h().b[12][0]++;
      cov_2n09p8ow7h().s[76]++;
      throw new Error('System must contain at least one segment');
    } else
    /* istanbul ignore next */
    {
      cov_2n09p8ow7h().b[12][1]++;
    }
    cov_2n09p8ow7h().s[77]++;
    for (const segment of inputs.segments) {
      /* istanbul ignore next */
      cov_2n09p8ow7h().s[78]++;
      if (segment.airflow <= 0) {
        /* istanbul ignore next */
        cov_2n09p8ow7h().b[14][0]++;
        cov_2n09p8ow7h().s[79]++;
        throw new Error(`Invalid airflow for segment ${segment.id}`);
      } else
      /* istanbul ignore next */
      {
        cov_2n09p8ow7h().b[14][1]++;
      }
      cov_2n09p8ow7h().s[80]++;
      if (
      /* istanbul ignore next */
      (cov_2n09p8ow7h().b[16][0]++, segment.type === 'straight') && (
      /* istanbul ignore next */
      (cov_2n09p8ow7h().b[16][1]++, !segment.length) ||
      /* istanbul ignore next */
      (cov_2n09p8ow7h().b[16][2]++, segment.length <= 0))) {
        /* istanbul ignore next */
        cov_2n09p8ow7h().b[15][0]++;
        cov_2n09p8ow7h().s[81]++;
        throw new Error(`Straight segment ${segment.id} must have positive length`);
      } else
      /* istanbul ignore next */
      {
        cov_2n09p8ow7h().b[15][1]++;
      }
      cov_2n09p8ow7h().s[82]++;
      if (
      /* istanbul ignore next */
      (cov_2n09p8ow7h().b[18][0]++, segment.type === 'fitting') &&
      /* istanbul ignore next */
      (cov_2n09p8ow7h().b[18][1]++, !segment.fittingConfig)) {
        /* istanbul ignore next */
        cov_2n09p8ow7h().b[17][0]++;
        cov_2n09p8ow7h().s[83]++;
        throw new Error(`Fitting segment ${segment.id} must have fitting configuration`);
      } else
      /* istanbul ignore next */
      {
        cov_2n09p8ow7h().b[17][1]++;
      }
    }
  }
  /**
   * Validate system results
   */
  static validateSystemResults(data) {
    /* istanbul ignore next */
    cov_2n09p8ow7h().f[10]++;
    const warnings =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[84]++, []);
    const recommendations =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[85]++, []);
    const velocityLimits =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[86]++, this.SYSTEM_VELOCITY_LIMITS[data.systemType]);
    const pressureLimits =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[87]++, this.SYSTEM_PRESSURE_LIMITS[data.systemType]);
    // Velocity compliance
    const velocityCompliant =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[88]++,
    /* istanbul ignore next */
    (cov_2n09p8ow7h().b[19][0]++, data.maxVelocity <= velocityLimits.max) &&
    /* istanbul ignore next */
    (cov_2n09p8ow7h().b[19][1]++, data.minVelocity >= velocityLimits.min));
    /* istanbul ignore next */
    cov_2n09p8ow7h().s[89]++;
    if (!velocityCompliant) {
      /* istanbul ignore next */
      cov_2n09p8ow7h().b[20][0]++;
      cov_2n09p8ow7h().s[90]++;
      warnings.push(`System velocities outside SMACNA limits (${velocityLimits.min}-${velocityLimits.max} FPM)`);
      /* istanbul ignore next */
      cov_2n09p8ow7h().s[91]++;
      recommendations.push('Resize ducts to achieve compliant velocities');
    } else
    /* istanbul ignore next */
    {
      cov_2n09p8ow7h().b[20][1]++;
    }
    // Pressure compliance
    const pressureCompliant =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[92]++, data.totalPressureLoss <= pressureLimits.max);
    /* istanbul ignore next */
    cov_2n09p8ow7h().s[93]++;
    if (!pressureCompliant) {
      /* istanbul ignore next */
      cov_2n09p8ow7h().b[21][0]++;
      cov_2n09p8ow7h().s[94]++;
      warnings.push(`System pressure loss (${data.totalPressureLoss.toFixed(2)} in wg) exceeds SMACNA limit (${pressureLimits.max} in wg)`);
      /* istanbul ignore next */
      cov_2n09p8ow7h().s[95]++;
      recommendations.push('Reduce system pressure loss by optimizing duct sizes and minimizing fittings');
    } else
    /* istanbul ignore next */
    {
      cov_2n09p8ow7h().b[21][1]++;
    }
    // Overall SMACNA compliance
    const smacnaCompliant =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[96]++,
    /* istanbul ignore next */
    (cov_2n09p8ow7h().b[22][0]++, velocityCompliant) &&
    /* istanbul ignore next */
    (cov_2n09p8ow7h().b[22][1]++, pressureCompliant));
    // Additional system-level checks
    /* istanbul ignore next */
    cov_2n09p8ow7h().s[97]++;
    if (data.totalPressureLoss > pressureLimits.recommended) {
      /* istanbul ignore next */
      cov_2n09p8ow7h().b[23][0]++;
      cov_2n09p8ow7h().s[98]++;
      recommendations.push(`Consider reducing pressure loss below ${pressureLimits.recommended} in wg for optimal efficiency`);
    } else
    /* istanbul ignore next */
    {
      cov_2n09p8ow7h().b[23][1]++;
    }
    cov_2n09p8ow7h().s[99]++;
    if (data.averageVelocity > velocityLimits.recommended) {
      /* istanbul ignore next */
      cov_2n09p8ow7h().b[24][0]++;
      cov_2n09p8ow7h().s[100]++;
      recommendations.push(`Consider reducing average velocity below ${velocityLimits.recommended} FPM for noise control`);
    } else
    /* istanbul ignore next */
    {
      cov_2n09p8ow7h().b[24][1]++;
    }
    cov_2n09p8ow7h().s[101]++;
    return {
      warnings,
      recommendations,
      compliance: {
        velocityCompliant,
        pressureCompliant,
        smacnaCompliant
      }
    };
  }
  /**
   * Round number to specified precision
   */
  static roundToPrecision(value, precision) {
    /* istanbul ignore next */
    cov_2n09p8ow7h().f[11]++;
    const factor =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[102]++, Math.pow(10, precision));
    /* istanbul ignore next */
    cov_2n09p8ow7h().s[103]++;
    return Math.round(value * factor) / factor;
  }
  /**
   * Get system pressure limits for a system type
   */
  static getSystemLimits(systemType) {
    /* istanbul ignore next */
    cov_2n09p8ow7h().f[12]++;
    cov_2n09p8ow7h().s[104]++;
    return {
      velocity:
      /* istanbul ignore next */
      (cov_2n09p8ow7h().b[25][0]++, this.SYSTEM_VELOCITY_LIMITS[systemType]) ||
      /* istanbul ignore next */
      (cov_2n09p8ow7h().b[25][1]++, this.SYSTEM_VELOCITY_LIMITS.supply),
      pressure:
      /* istanbul ignore next */
      (cov_2n09p8ow7h().b[26][0]++, this.SYSTEM_PRESSURE_LIMITS[systemType]) ||
      /* istanbul ignore next */
      (cov_2n09p8ow7h().b[26][1]++, this.SYSTEM_PRESSURE_LIMITS.supply)
    };
  }
  /**
   * Calculate enhanced system pressure with environmental corrections
   */
  static calculateEnhancedSystemPressure(inputs) {
    /* istanbul ignore next */
    cov_2n09p8ow7h().f[13]++;
    const {
      segments,
      systemType,
      designConditions,
      calculationOptions
    } =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[105]++, inputs);
    // Validate inputs
    /* istanbul ignore next */
    cov_2n09p8ow7h().s[106]++;
    this.validateSystemInputs(inputs);
    const results =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[107]++, []);
    let totalPressureLoss =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[108]++, 0);
    let totalFrictionLoss =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[109]++, 0);
    let totalFittingLoss =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[110]++, 0);
    let totalElevationLoss =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[111]++, 0);
    const warnings =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[112]++, []);
    const notes =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[113]++, []);
    // Process each segment with enhanced calculations
    /* istanbul ignore next */
    cov_2n09p8ow7h().s[114]++;
    for (const segment of segments) {
      const segmentResult =
      /* istanbul ignore next */
      (cov_2n09p8ow7h().s[115]++, this.calculateEnhancedSegmentPressure(segment, designConditions));
      /* istanbul ignore next */
      cov_2n09p8ow7h().s[116]++;
      results.push(segmentResult);
      /* istanbul ignore next */
      cov_2n09p8ow7h().s[117]++;
      totalPressureLoss += segmentResult.pressureLoss;
      /* istanbul ignore next */
      cov_2n09p8ow7h().s[118]++;
      if (segmentResult.type === 'friction') {
        /* istanbul ignore next */
        cov_2n09p8ow7h().b[27][0]++;
        cov_2n09p8ow7h().s[119]++;
        totalFrictionLoss += segmentResult.pressureLoss;
      } else {
        /* istanbul ignore next */
        cov_2n09p8ow7h().b[27][1]++;
        cov_2n09p8ow7h().s[120]++;
        if (segmentResult.type === 'fitting') {
          /* istanbul ignore next */
          cov_2n09p8ow7h().b[28][0]++;
          cov_2n09p8ow7h().s[121]++;
          totalFittingLoss += segmentResult.pressureLoss;
        } else
        /* istanbul ignore next */
        {
          cov_2n09p8ow7h().b[28][1]++;
        }
      }
      // Add elevation effects if present
      /* istanbul ignore next */
      cov_2n09p8ow7h().s[122]++;
      if (segment.elevation !== undefined) {
        /* istanbul ignore next */
        cov_2n09p8ow7h().b[29][0]++;
        const elevationEffect =
        /* istanbul ignore next */
        (cov_2n09p8ow7h().s[123]++, this.calculateElevationPressure(segment));
        /* istanbul ignore next */
        cov_2n09p8ow7h().s[124]++;
        totalElevationLoss += elevationEffect.pressureChange;
        /* istanbul ignore next */
        cov_2n09p8ow7h().s[125]++;
        if (elevationEffect.warnings.length > 0) {
          /* istanbul ignore next */
          cov_2n09p8ow7h().b[30][0]++;
          cov_2n09p8ow7h().s[126]++;
          warnings.push(...elevationEffect.warnings);
        } else
        /* istanbul ignore next */
        {
          cov_2n09p8ow7h().b[30][1]++;
        }
      } else
      /* istanbul ignore next */
      {
        cov_2n09p8ow7h().b[29][1]++;
      }
      // Collect warnings and notes
      cov_2n09p8ow7h().s[127]++;
      if (segmentResult.warnings) {
        /* istanbul ignore next */
        cov_2n09p8ow7h().b[31][0]++;
        cov_2n09p8ow7h().s[128]++;
        warnings.push(...segmentResult.warnings);
      } else
      /* istanbul ignore next */
      {
        cov_2n09p8ow7h().b[31][1]++;
      }
      cov_2n09p8ow7h().s[129]++;
      if (segmentResult.notes) {
        /* istanbul ignore next */
        cov_2n09p8ow7h().b[32][0]++;
        cov_2n09p8ow7h().s[130]++;
        notes.push(...segmentResult.notes);
      } else
      /* istanbul ignore next */
      {
        cov_2n09p8ow7h().b[32][1]++;
      }
    }
    // Calculate system-level metrics
    const totalAirflow =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[131]++, Math.max(...segments.map(s => {
      /* istanbul ignore next */
      cov_2n09p8ow7h().f[14]++;
      cov_2n09p8ow7h().s[132]++;
      return s.airflow;
    })));
    const averageVelocity =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[133]++, this.calculateAverageVelocity(segments));
    // Add environmental condition warnings
    const envWarnings =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[134]++, this.validateEnvironmentalConditions(designConditions));
    /* istanbul ignore next */
    cov_2n09p8ow7h().s[135]++;
    warnings.push(...envWarnings);
    // Performance analysis with enhanced data
    const analysis =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[136]++, this.analyzeEnhancedSystemPerformance(results, totalPressureLoss, totalAirflow, systemType, designConditions));
    /* istanbul ignore next */
    cov_2n09p8ow7h().s[137]++;
    return {
      totalPressureLoss: this.roundToPrecision(totalPressureLoss + totalElevationLoss, 4),
      frictionLoss: this.roundToPrecision(totalFrictionLoss, 4),
      fittingLoss: this.roundToPrecision(totalFittingLoss, 4),
      elevationLoss: this.roundToPrecision(totalElevationLoss, 4),
      segments: results,
      systemMetrics: {
        totalAirflow,
        averageVelocity: this.roundToPrecision(averageVelocity, 1),
        frictionPercentage: this.roundToPrecision(totalFrictionLoss / totalPressureLoss * 100, 1),
        fittingPercentage: this.roundToPrecision(totalFittingLoss / totalPressureLoss * 100, 1),
        elevationPercentage: this.roundToPrecision(totalElevationLoss / totalPressureLoss * 100, 1),
        systemEfficiency: this.calculateSystemEfficiency(totalFrictionLoss, totalFittingLoss)
      },
      analysis,
      warnings,
      notes,
      calculationMethod: 'Enhanced with environmental corrections',
      timestamp: new Date().toISOString()
    };
  }
  /**
   * Calculate enhanced segment pressure with environmental corrections
   */
  static calculateEnhancedSegmentPressure(segment, designConditions) {
    /* istanbul ignore next */
    cov_2n09p8ow7h().f[15]++;
    const warnings =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[138]++, []);
    const notes =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[139]++, []);
    // Determine air conditions for this segment
    const airConditions =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[140]++, {
      temperature:
      /* istanbul ignore next */
      (cov_2n09p8ow7h().b[33][0]++, segment.temperature) ||
      /* istanbul ignore next */
      (cov_2n09p8ow7h().b[33][1]++, designConditions.temperature) ||
      /* istanbul ignore next */
      (cov_2n09p8ow7h().b[33][2]++, 70),
      pressure:
      /* istanbul ignore next */
      (cov_2n09p8ow7h().b[34][0]++, segment.pressure) ||
      /* istanbul ignore next */
      (cov_2n09p8ow7h().b[34][1]++, designConditions.barometricPressure),
      altitude:
      /* istanbul ignore next */
      (cov_2n09p8ow7h().b[35][0]++, segment.elevation) ||
      /* istanbul ignore next */
      (cov_2n09p8ow7h().b[35][1]++, designConditions.altitude) ||
      /* istanbul ignore next */
      (cov_2n09p8ow7h().b[35][2]++, 0),
      humidity:
      /* istanbul ignore next */
      (cov_2n09p8ow7h().b[36][0]++, segment.humidity) ||
      /* istanbul ignore next */
      (cov_2n09p8ow7h().b[36][1]++, designConditions.humidity) ||
      /* istanbul ignore next */
      (cov_2n09p8ow7h().b[36][2]++, 50)
    });
    // Get enhanced air properties
    const airProps =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[141]++, AirPropertiesCalculator_1.AirPropertiesCalculator.calculateAirProperties(airConditions));
    /* istanbul ignore next */
    cov_2n09p8ow7h().s[142]++;
    warnings.push(...airProps.warnings);
    /* istanbul ignore next */
    cov_2n09p8ow7h().s[143]++;
    notes.push(...airProps.notes);
    /* istanbul ignore next */
    cov_2n09p8ow7h().s[144]++;
    if (
    /* istanbul ignore next */
    (cov_2n09p8ow7h().b[38][0]++, segment.type === 'straight') &&
    /* istanbul ignore next */
    (cov_2n09p8ow7h().b[38][1]++, segment.length)) {
      /* istanbul ignore next */
      cov_2n09p8ow7h().b[37][0]++;
      cov_2n09p8ow7h().s[145]++;
      // Enhanced friction loss calculation
      return this.calculateEnhancedFrictionLoss(segment, airProps, warnings, notes);
    } else {
      /* istanbul ignore next */
      cov_2n09p8ow7h().b[37][1]++;
      cov_2n09p8ow7h().s[146]++;
      if (
      /* istanbul ignore next */
      (cov_2n09p8ow7h().b[40][0]++, segment.type === 'fitting') &&
      /* istanbul ignore next */
      (cov_2n09p8ow7h().b[40][1]++, segment.fittingConfig)) {
        /* istanbul ignore next */
        cov_2n09p8ow7h().b[39][0]++;
        cov_2n09p8ow7h().s[147]++;
        // Enhanced fitting loss calculation
        return this.calculateEnhancedFittingLoss(segment, airProps, warnings, notes);
      } else
      /* istanbul ignore next */
      {
        cov_2n09p8ow7h().b[39][1]++;
      }
    }
    // Fallback to basic calculation
    /* istanbul ignore next */
    cov_2n09p8ow7h().s[148]++;
    return {
      segmentId: segment.id,
      type: 'unknown',
      pressureLoss: 0,
      velocity: 0,
      warnings: ['Unknown segment type'],
      notes: []
    };
  }
  /**
   * Calculate enhanced friction loss with material aging and environmental corrections
   */
  static calculateEnhancedFrictionLoss(segment, airProps, warnings, notes) {
    /* istanbul ignore next */
    cov_2n09p8ow7h().f[16]++;
    // Get enhanced material roughness
    const materialData =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[149]++, AirPropertiesCalculator_1.AirPropertiesCalculator.getEnhancedMaterialRoughness(segment.material, segment.materialAge, segment.surfaceCondition));
    /* istanbul ignore next */
    cov_2n09p8ow7h().s[150]++;
    warnings.push(...materialData.warnings);
    /* istanbul ignore next */
    cov_2n09p8ow7h().s[151]++;
    notes.push(...materialData.notes);
    // Calculate velocity
    const area =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[152]++, segment.ductShape === 'round' ?
    /* istanbul ignore next */
    (cov_2n09p8ow7h().b[41][0]++, Math.PI * Math.pow((
    /* istanbul ignore next */
    (cov_2n09p8ow7h().b[42][0]++, segment.diameter) ||
    /* istanbul ignore next */
    (cov_2n09p8ow7h().b[42][1]++, 12)) / 12, 2) / 4) :
    /* istanbul ignore next */
    (cov_2n09p8ow7h().b[41][1]++, (
    /* istanbul ignore next */
    (cov_2n09p8ow7h().b[43][0]++, segment.width) ||
    /* istanbul ignore next */
    (cov_2n09p8ow7h().b[43][1]++, 12)) * (
    /* istanbul ignore next */
    (cov_2n09p8ow7h().b[44][0]++, segment.height) ||
    /* istanbul ignore next */
    (cov_2n09p8ow7h().b[44][1]++, 12)) / 144));
    const velocity =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[153]++, segment.airflow / area);
    // Enhanced pressure loss calculation using corrected air properties
    const diameter =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[154]++, segment.ductShape === 'round' ?
    /* istanbul ignore next */
    (cov_2n09p8ow7h().b[45][0]++,
    /* istanbul ignore next */
    (cov_2n09p8ow7h().b[46][0]++, segment.diameter) ||
    /* istanbul ignore next */
    (cov_2n09p8ow7h().b[46][1]++, 12)) :
    /* istanbul ignore next */
    (cov_2n09p8ow7h().b[45][1]++, AirDuctCalculator_1.AirDuctCalculator.calculateEquivalentDiameter(
    /* istanbul ignore next */
    (cov_2n09p8ow7h().b[47][0]++, segment.width) ||
    /* istanbul ignore next */
    (cov_2n09p8ow7h().b[47][1]++, 12),
    /* istanbul ignore next */
    (cov_2n09p8ow7h().b[48][0]++, segment.height) ||
    /* istanbul ignore next */
    (cov_2n09p8ow7h().b[48][1]++, 12))));
    // Use enhanced calculation with corrected air density and viscosity
    const pressureLoss =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[155]++, this.calculateCorrectedPressureLoss(velocity,
    /* istanbul ignore next */
    (cov_2n09p8ow7h().b[49][0]++, segment.length) ||
    /* istanbul ignore next */
    (cov_2n09p8ow7h().b[49][1]++, 0), diameter, materialData.roughness, airProps));
    /* istanbul ignore next */
    cov_2n09p8ow7h().s[156]++;
    return {
      segmentId: segment.id,
      type: 'friction',
      pressureLoss: this.roundToPrecision(pressureLoss, 4),
      velocity: this.roundToPrecision(velocity, 1),
      equivalentDiameter: this.roundToPrecision(diameter, 2),
      materialRoughness: materialData.roughness,
      airDensity: airProps.density,
      correctionFactors: airProps.correctionFactors,
      warnings,
      notes
    };
  }
  /**
   * Calculate enhanced fitting loss with environmental corrections
   */
  static calculateEnhancedFittingLoss(segment, airProps, warnings, notes) {
    /* istanbul ignore next */
    cov_2n09p8ow7h().f[17]++;
    // Calculate velocity
    const area =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[157]++, segment.ductShape === 'round' ?
    /* istanbul ignore next */
    (cov_2n09p8ow7h().b[50][0]++, Math.PI * Math.pow((
    /* istanbul ignore next */
    (cov_2n09p8ow7h().b[51][0]++, segment.diameter) ||
    /* istanbul ignore next */
    (cov_2n09p8ow7h().b[51][1]++, 12)) / 12, 2) / 4) :
    /* istanbul ignore next */
    (cov_2n09p8ow7h().b[50][1]++, (
    /* istanbul ignore next */
    (cov_2n09p8ow7h().b[52][0]++, segment.width) ||
    /* istanbul ignore next */
    (cov_2n09p8ow7h().b[52][1]++, 12)) * (
    /* istanbul ignore next */
    (cov_2n09p8ow7h().b[53][0]++, segment.height) ||
    /* istanbul ignore next */
    (cov_2n09p8ow7h().b[53][1]++, 12)) / 144));
    const velocity =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[158]++, segment.airflow / area);
    // Calculate velocity pressure with environmental corrections
    const vpParams =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[159]++, {
      velocity,
      airConditions: {
        temperature:
        /* istanbul ignore next */
        (cov_2n09p8ow7h().b[54][0]++, segment.temperature) ||
        /* istanbul ignore next */
        (cov_2n09p8ow7h().b[54][1]++, 70),
        pressure: segment.pressure,
        altitude:
        /* istanbul ignore next */
        (cov_2n09p8ow7h().b[55][0]++, segment.elevation) ||
        /* istanbul ignore next */
        (cov_2n09p8ow7h().b[55][1]++, 0),
        humidity:
        /* istanbul ignore next */
        (cov_2n09p8ow7h().b[56][0]++, segment.humidity) ||
        /* istanbul ignore next */
        (cov_2n09p8ow7h().b[56][1]++, 50)
      }
    });
    const vpResult =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[160]++, AirPropertiesCalculator_1.AirPropertiesCalculator.calculateVelocityPressure(vpParams));
    /* istanbul ignore next */
    cov_2n09p8ow7h().s[161]++;
    warnings.push(...vpResult.warnings);
    // Calculate fitting loss using enhanced velocity pressure
    const fittingResult =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[162]++, FittingLossCalculator_1.FittingLossCalculator.calculateFittingLoss(segment.fittingConfig, velocity, airProps.density));
    // Apply velocity pressure correction
    const correctedPressureLoss =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[163]++, fittingResult.pressureLoss * (vpResult.velocityPressure / Math.pow(velocity / 4005, 2)));
    /* istanbul ignore next */
    cov_2n09p8ow7h().s[164]++;
    return {
      segmentId: segment.id,
      type: 'fitting',
      pressureLoss: this.roundToPrecision(correctedPressureLoss, 4),
      velocity: this.roundToPrecision(velocity, 1),
      kFactor: fittingResult.kFactor,
      velocityPressure: vpResult.velocityPressure,
      airDensity: airProps.density,
      correctionFactors: vpResult.correctionFactors,
      warnings,
      notes
    };
  }
  /**
   * Calculate elevation pressure effects
   */
  static calculateElevationPressure(segment) {
    /* istanbul ignore next */
    cov_2n09p8ow7h().f[18]++;
    const warnings =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[165]++, []);
    /* istanbul ignore next */
    cov_2n09p8ow7h().s[166]++;
    if (!segment.elevation) {
      /* istanbul ignore next */
      cov_2n09p8ow7h().b[57][0]++;
      cov_2n09p8ow7h().s[167]++;
      return {
        pressureChange: 0,
        warnings
      };
    } else
    /* istanbul ignore next */
    {
      cov_2n09p8ow7h().b[57][1]++;
    }
    // Calculate elevation effects using enhanced air properties
    const elevationEffects =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[168]++, AirPropertiesCalculator_1.AirPropertiesCalculator.calculateElevationEffects(segment.elevation));
    /* istanbul ignore next */
    cov_2n09p8ow7h().s[169]++;
    warnings.push(...elevationEffects.warnings);
    // Pressure change due to elevation (simplified)
    // ΔP = ρ × g × Δh / gc (converted to inches w.g.)
    const airDensity =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[170]++, 0.075); // lb/ft³ at standard conditions
    const pressureChange =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[171]++, airDensity * segment.elevation / 5.2); // Convert to inches w.g.
    /* istanbul ignore next */
    cov_2n09p8ow7h().s[172]++;
    return {
      pressureChange: pressureChange * elevationEffects.densityRatio,
      warnings
    };
  }
  /**
   * Calculate average velocity across all segments
   */
  static calculateAverageVelocity(segments) {
    /* istanbul ignore next */
    cov_2n09p8ow7h().f[19]++;
    let totalVelocity =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[173]++, 0);
    let count =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[174]++, 0);
    /* istanbul ignore next */
    cov_2n09p8ow7h().s[175]++;
    for (const segment of segments) {
      const area =
      /* istanbul ignore next */
      (cov_2n09p8ow7h().s[176]++, segment.ductShape === 'round' ?
      /* istanbul ignore next */
      (cov_2n09p8ow7h().b[58][0]++, Math.PI * Math.pow((
      /* istanbul ignore next */
      (cov_2n09p8ow7h().b[59][0]++, segment.diameter) ||
      /* istanbul ignore next */
      (cov_2n09p8ow7h().b[59][1]++, 12)) / 12, 2) / 4) :
      /* istanbul ignore next */
      (cov_2n09p8ow7h().b[58][1]++, (
      /* istanbul ignore next */
      (cov_2n09p8ow7h().b[60][0]++, segment.width) ||
      /* istanbul ignore next */
      (cov_2n09p8ow7h().b[60][1]++, 12)) * (
      /* istanbul ignore next */
      (cov_2n09p8ow7h().b[61][0]++, segment.height) ||
      /* istanbul ignore next */
      (cov_2n09p8ow7h().b[61][1]++, 12)) / 144));
      const velocity =
      /* istanbul ignore next */
      (cov_2n09p8ow7h().s[177]++, segment.airflow / area);
      /* istanbul ignore next */
      cov_2n09p8ow7h().s[178]++;
      totalVelocity += velocity;
      /* istanbul ignore next */
      cov_2n09p8ow7h().s[179]++;
      count++;
    }
    /* istanbul ignore next */
    cov_2n09p8ow7h().s[180]++;
    return count > 0 ?
    /* istanbul ignore next */
    (cov_2n09p8ow7h().b[62][0]++, totalVelocity / count) :
    /* istanbul ignore next */
    (cov_2n09p8ow7h().b[62][1]++, 0);
  }
  /**
   * Validate environmental conditions
   */
  static validateEnvironmentalConditions(designConditions) {
    /* istanbul ignore next */
    cov_2n09p8ow7h().f[20]++;
    const warnings =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[181]++, []);
    /* istanbul ignore next */
    cov_2n09p8ow7h().s[182]++;
    if (
    /* istanbul ignore next */
    (cov_2n09p8ow7h().b[64][0]++, designConditions.temperature < 32) ||
    /* istanbul ignore next */
    (cov_2n09p8ow7h().b[64][1]++, designConditions.temperature > 200)) {
      /* istanbul ignore next */
      cov_2n09p8ow7h().b[63][0]++;
      cov_2n09p8ow7h().s[183]++;
      warnings.push(`Temperature ${designConditions.temperature}°F is outside normal HVAC range`);
    } else
    /* istanbul ignore next */
    {
      cov_2n09p8ow7h().b[63][1]++;
    }
    cov_2n09p8ow7h().s[184]++;
    if (
    /* istanbul ignore next */
    (cov_2n09p8ow7h().b[66][0]++, designConditions.altitude) &&
    /* istanbul ignore next */
    (cov_2n09p8ow7h().b[66][1]++, designConditions.altitude > 5000)) {
      /* istanbul ignore next */
      cov_2n09p8ow7h().b[65][0]++;
      cov_2n09p8ow7h().s[185]++;
      warnings.push(`High altitude (${designConditions.altitude} ft) requires density corrections`);
    } else
    /* istanbul ignore next */
    {
      cov_2n09p8ow7h().b[65][1]++;
    }
    cov_2n09p8ow7h().s[186]++;
    if (
    /* istanbul ignore next */
    (cov_2n09p8ow7h().b[68][0]++, designConditions.humidity) &&
    /* istanbul ignore next */
    (cov_2n09p8ow7h().b[68][1]++, designConditions.humidity > 80)) {
      /* istanbul ignore next */
      cov_2n09p8ow7h().b[67][0]++;
      cov_2n09p8ow7h().s[187]++;
      warnings.push(`High humidity (${designConditions.humidity}% RH) may cause condensation`);
    } else
    /* istanbul ignore next */
    {
      cov_2n09p8ow7h().b[67][1]++;
    }
    cov_2n09p8ow7h().s[188]++;
    return warnings;
  }
  /**
   * Analyze enhanced system performance
   */
  static analyzeEnhancedSystemPerformance(results, totalPressureLoss, totalAirflow, systemType, designConditions) {
    /* istanbul ignore next */
    cov_2n09p8ow7h().f[21]++;
    const warnings =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[189]++, []);
    const recommendations =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[190]++, []);
    // Enhanced performance analysis
    const limits =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[191]++, this.getSystemLimits(systemType));
    const averageVelocity =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[192]++, results.reduce((sum, r) => {
      /* istanbul ignore next */
      cov_2n09p8ow7h().f[22]++;
      cov_2n09p8ow7h().s[193]++;
      return sum + (
      /* istanbul ignore next */
      (cov_2n09p8ow7h().b[69][0]++, r.velocity) ||
      /* istanbul ignore next */
      (cov_2n09p8ow7h().b[69][1]++, 0));
    }, 0) / results.length);
    // Velocity compliance check
    const velocityCompliant =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[194]++,
    /* istanbul ignore next */
    (cov_2n09p8ow7h().b[70][0]++, averageVelocity >= limits.velocity.min) &&
    /* istanbul ignore next */
    (cov_2n09p8ow7h().b[70][1]++, averageVelocity <= limits.velocity.max));
    /* istanbul ignore next */
    cov_2n09p8ow7h().s[195]++;
    if (!velocityCompliant) {
      /* istanbul ignore next */
      cov_2n09p8ow7h().b[71][0]++;
      cov_2n09p8ow7h().s[196]++;
      warnings.push(`Average velocity ${averageVelocity.toFixed(0)} FPM is outside recommended range`);
    } else
    /* istanbul ignore next */
    {
      cov_2n09p8ow7h().b[71][1]++;
    }
    // Pressure compliance check
    const pressureCompliant =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[197]++, totalPressureLoss <= limits.pressure.max);
    /* istanbul ignore next */
    cov_2n09p8ow7h().s[198]++;
    if (!pressureCompliant) {
      /* istanbul ignore next */
      cov_2n09p8ow7h().b[72][0]++;
      cov_2n09p8ow7h().s[199]++;
      warnings.push(`Total pressure loss ${totalPressureLoss.toFixed(3)} in wg exceeds maximum`);
    } else
    /* istanbul ignore next */
    {
      cov_2n09p8ow7h().b[72][1]++;
    }
    // Environmental impact analysis
    const hasEnvironmentalCorrections =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[200]++, results.some(r => {
      /* istanbul ignore next */
      cov_2n09p8ow7h().f[23]++;
      cov_2n09p8ow7h().s[201]++;
      return /* istanbul ignore next */(cov_2n09p8ow7h().b[73][0]++, r.correctionFactors) &&
      /* istanbul ignore next */
      (cov_2n09p8ow7h().b[73][1]++, Math.abs(r.correctionFactors.combined - 1.0) > 0.05);
    }));
    /* istanbul ignore next */
    cov_2n09p8ow7h().s[202]++;
    if (hasEnvironmentalCorrections) {
      /* istanbul ignore next */
      cov_2n09p8ow7h().b[74][0]++;
      cov_2n09p8ow7h().s[203]++;
      recommendations.push('Environmental conditions significantly affect system performance');
    } else
    /* istanbul ignore next */
    {
      cov_2n09p8ow7h().b[74][1]++;
    }
    // Material aging analysis
    const hasAgingEffects =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[204]++, results.some(r => {
      /* istanbul ignore next */
      cov_2n09p8ow7h().f[24]++;
      cov_2n09p8ow7h().s[205]++;
      return /* istanbul ignore next */(cov_2n09p8ow7h().b[75][0]++, r.materialRoughness) &&
      /* istanbul ignore next */
      (cov_2n09p8ow7h().b[75][1]++, r.materialRoughness > 0.0005);
    }));
    /* istanbul ignore next */
    cov_2n09p8ow7h().s[206]++;
    if (hasAgingEffects) {
      /* istanbul ignore next */
      cov_2n09p8ow7h().b[76][0]++;
      cov_2n09p8ow7h().s[207]++;
      recommendations.push('Consider duct cleaning or replacement for aged materials');
    } else
    /* istanbul ignore next */
    {
      cov_2n09p8ow7h().b[76][1]++;
    }
    cov_2n09p8ow7h().s[208]++;
    return {
      warnings,
      recommendations,
      compliance: {
        velocityCompliant,
        pressureCompliant,
        smacnaCompliant:
        /* istanbul ignore next */
        (cov_2n09p8ow7h().b[77][0]++, velocityCompliant) &&
        /* istanbul ignore next */
        (cov_2n09p8ow7h().b[77][1]++, pressureCompliant)
      }
    };
  }
  /**
   * Calculate corrected pressure loss with enhanced air properties
   */
  static calculateCorrectedPressureLoss(velocity, length, diameter, roughness, airProps) {
    /* istanbul ignore next */
    cov_2n09p8ow7h().f[25]++;
    // Convert units
    const velocityFps =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[209]++, velocity / 60); // FPM to FPS
    const diameterFt =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[210]++, diameter / 12); // inches to feet
    // Calculate Reynolds number with corrected viscosity
    const reynolds =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[211]++, velocityFps * diameterFt * airProps.density / airProps.viscosity);
    // Calculate friction factor using enhanced roughness
    const relativeRoughness =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[212]++, roughness / diameterFt);
    const frictionFactor =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[213]++, this.calculateEnhancedFrictionFactor(reynolds, relativeRoughness));
    // Darcy-Weisbach equation with corrected air density
    const pressureLossPsf =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[214]++, frictionFactor * (length / diameterFt) * (airProps.density * Math.pow(velocityFps, 2)) / (2 * 32.174));
    // Convert to inches w.g.
    /* istanbul ignore next */
    cov_2n09p8ow7h().s[215]++;
    return pressureLossPsf / 5.2;
  }
  /**
   * Enhanced friction factor calculation
   */
  static calculateEnhancedFrictionFactor(reynolds, relativeRoughness) {
    /* istanbul ignore next */
    cov_2n09p8ow7h().f[26]++;
    cov_2n09p8ow7h().s[216]++;
    // For laminar flow
    if (reynolds < 2300) {
      /* istanbul ignore next */
      cov_2n09p8ow7h().b[78][0]++;
      cov_2n09p8ow7h().s[217]++;
      return 64 / reynolds;
    } else
    /* istanbul ignore next */
    {
      cov_2n09p8ow7h().b[78][1]++;
    }
    // For turbulent flow - Colebrook-White equation (iterative solution)
    let f =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[218]++, 0.02); // Initial guess
    /* istanbul ignore next */
    cov_2n09p8ow7h().s[219]++;
    for (let i =
    /* istanbul ignore next */
    (cov_2n09p8ow7h().s[220]++, 0); i < 10; i++) {
      const fNew =
      /* istanbul ignore next */
      (cov_2n09p8ow7h().s[221]++, 1 / Math.pow(-2 * Math.log10(relativeRoughness / 3.7 + 2.51 / (reynolds * Math.sqrt(f))), 2));
      /* istanbul ignore next */
      cov_2n09p8ow7h().s[222]++;
      if (Math.abs(fNew - f) < 0.0001) {
        /* istanbul ignore next */
        cov_2n09p8ow7h().b[79][0]++;
        cov_2n09p8ow7h().s[223]++;
        break;
      } else
      /* istanbul ignore next */
      {
        cov_2n09p8ow7h().b[79][1]++;
      }
      cov_2n09p8ow7h().s[224]++;
      f = fNew;
    }
    /* istanbul ignore next */
    cov_2n09p8ow7h().s[225]++;
    return f;
  }
}
/* istanbul ignore next */
cov_2n09p8ow7h().s[226]++;
exports.SystemPressureCalculator = SystemPressureCalculator;
// SMACNA system pressure limits (inches w.g.)
/* istanbul ignore next */
cov_2n09p8ow7h().s[227]++;
SystemPressureCalculator.SYSTEM_PRESSURE_LIMITS = {
  supply: {
    max: 6.0,
    recommended: 4.0
  },
  return: {
    max: 4.0,
    recommended: 2.5
  },
  exhaust: {
    max: 8.0,
    recommended: 5.0
  }
};
// SMACNA velocity limits by system type (FPM)
/* istanbul ignore next */
cov_2n09p8ow7h().s[228]++;
SystemPressureCalculator.SYSTEM_VELOCITY_LIMITS = {
  supply: {
    min: 400,
    max: 2500,
    recommended: 1500
  },
  return: {
    min: 300,
    max: 2000,
    recommended: 1200
  },
  exhaust: {
    min: 500,
    max: 3000,
    recommended: 1800
  }
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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