{"version": 3, "names": ["cov_1lxbffvxp9", "actualCoverage", "fs", "s", "require", "path", "testFittingCoefficientsData", "f", "console", "log", "dataPath", "join", "__dirname", "rawData", "readFileSync", "data", "JSON", "parse", "metadata", "version", "standard", "roundFittings", "round_fittings", "hasElbows", "b", "elbows", "Object", "keys", "length", "hasTees", "tees", "hasTransitions", "transitions", "smoothElbow", "radius_to_diameter_ratios", "kFactorData", "kFactor", "K", "error", "message", "testVelocityPressureCalculation", "velocity", "standardAirDensity", "velocityPressure", "Math", "pow", "toFixed", "hotAirDensity", "adjustedVP", "testFittingLossCalculation", "airDensity", "pressureLoss", "testSystemCalculation", "airflow", "diameter", "straightLength1", "straightLength2", "area", "PI", "roughness", "diameterFt", "velocityFps", "kinematicViscosity", "reynolds", "relativeRoughness", "frictionFactor", "log10", "frictionLossPer100ft", "totalFrictionLoss", "elbowKFactorData", "elbowKFactor", "fitting<PERSON>oss", "totalPressureLoss", "frictionPercent", "fittingPercent", "testSMACNACompliance", "velocityLimits", "supply", "min", "max", "recommended", "return", "exhaust", "pressureLimits", "systemType", "vLimits", "velocityCompliant", "velocityOptimal", "pLimits", "pressureCompliant", "pressureOptimal", "overallCompliant", "testEnhancedAirProperties", "airPropsPath", "existsSync", "airPropsData", "temperature_properties", "altitude_corrections", "humidity_effects", "vpPath", "vpData", "velocity_pressure_table", "roughnessPath", "roughnessData", "materials", "galvSteel", "galvanized_steel", "aging_factors", "standardTemp", "standardDensity", "highTemp", "tempCorrectionFactor", "altitude", "altitudeCorrectionFactor", "combinedCorrection", "baseRoughness", "agingFactor", "aged<PERSON><PERSON>ness", "runAllTests", "tests", "passedTests", "totalTests", "test", "main", "module", "success", "process", "exit", "exports"], "sources": ["test-integration.js"], "sourcesContent": ["/**\r\n * Simple JavaScript test for duct physics integration\r\n * Tests the core functionality without TypeScript compilation issues\r\n */\r\n\r\nconst fs = require('fs');\r\nconst path = require('path');\r\n\r\n// Test 1: Verify fitting coefficients data file exists and is valid\r\nfunction testFittingCoefficientsData() {\r\n  console.log('\\n=== TEST 1: Fitting Coefficients Data ===');\r\n  \r\n  try {\r\n    const dataPath = path.join(__dirname, '../../data/fitting_coefficients.json');\r\n    const rawData = fs.readFileSync(dataPath, 'utf8');\r\n    const data = JSON.parse(rawData);\r\n    \r\n    console.log('✓ Fitting coefficients file loaded successfully');\r\n    console.log(`✓ Version: ${data.metadata.version}`);\r\n    console.log(`✓ Standard: ${data.metadata.standard}`);\r\n    \r\n    // Check for key fitting types\r\n    const roundFittings = data.round_fittings;\r\n    const hasElbows = roundFittings.elbows && Object.keys(roundFittings.elbows).length > 0;\r\n    const hasTees = roundFittings.tees && Object.keys(roundFittings.tees).length > 0;\r\n    const hasTransitions = roundFittings.transitions && Object.keys(roundFittings.transitions).length > 0;\r\n    \r\n    console.log(`✓ Round elbows: ${hasElbows ? 'Present' : 'Missing'}`);\r\n    console.log(`✓ Round tees: ${hasTees ? 'Present' : 'Missing'}`);\r\n    console.log(`✓ Transitions: ${hasTransitions ? 'Present' : 'Missing'}`);\r\n    \r\n    // Test specific K-factor lookup\r\n    const smoothElbow = roundFittings.elbows['90deg_round_smooth'];\r\n    if (smoothElbow && smoothElbow.radius_to_diameter_ratios) {\r\n      const kFactorData = smoothElbow.radius_to_diameter_ratios['1.5'];\r\n      const kFactor = kFactorData ? kFactorData.K : 'Not found';\r\n      console.log(`✓ 90° smooth elbow R/D=1.5 K-factor: ${kFactor}`);\r\n    }\r\n    \r\n    return true;\r\n  } catch (error) {\r\n    console.error('✗ Error loading fitting coefficients:', error.message);\r\n    return false;\r\n  }\r\n}\r\n\r\n// Test 2: Basic velocity pressure calculation\r\nfunction testVelocityPressureCalculation() {\r\n  console.log('\\n=== TEST 2: Velocity Pressure Calculation ===');\r\n  \r\n  try {\r\n    // Manual calculation: VP = (V/4005)² for standard air density\r\n    const velocity = 1000; // FPM\r\n    const standardAirDensity = 0.075; // lb/ft³\r\n    \r\n    const velocityPressure = Math.pow(velocity / 4005, 2) * (standardAirDensity / 0.075);\r\n    \r\n    console.log(`✓ Velocity: ${velocity} FPM`);\r\n    console.log(`✓ Velocity Pressure: ${velocityPressure.toFixed(4)} in wg`);\r\n    \r\n    // Test with different air density\r\n    const hotAirDensity = 0.060; // lb/ft³ (hot air)\r\n    const adjustedVP = Math.pow(velocity / 4005, 2) * (hotAirDensity / 0.075);\r\n    \r\n    console.log(`✓ Hot air VP (ρ=${hotAirDensity}): ${adjustedVP.toFixed(4)} in wg`);\r\n    console.log(`✓ Density ratio effect: ${(adjustedVP / velocityPressure).toFixed(3)}`);\r\n    \r\n    return true;\r\n  } catch (error) {\r\n    console.error('✗ Error in velocity pressure calculation:', error.message);\r\n    return false;\r\n  }\r\n}\r\n\r\n// Test 3: Basic fitting loss calculation\r\nfunction testFittingLossCalculation() {\r\n  console.log('\\n=== TEST 3: Fitting Loss Calculation ===');\r\n  \r\n  try {\r\n    // Load fitting coefficients\r\n    const dataPath = path.join(__dirname, '../../data/fitting_coefficients.json');\r\n    const data = JSON.parse(fs.readFileSync(dataPath, 'utf8'));\r\n    \r\n    // Test case: 90° smooth elbow with R/D = 1.5\r\n    const kFactorData = data.round_fittings.elbows['90deg_round_smooth'].radius_to_diameter_ratios['1.5'];\r\n    const kFactor = kFactorData.K;\r\n    const velocity = 1833; // FPM (1000 CFM in 10\" duct)\r\n    const airDensity = 0.075; // lb/ft³\r\n    \r\n    // Calculate velocity pressure\r\n    const velocityPressure = Math.pow(velocity / 4005, 2) * (airDensity / 0.075);\r\n    \r\n    // Calculate pressure loss\r\n    const pressureLoss = kFactor * velocityPressure;\r\n    \r\n    console.log(`✓ Fitting: 90° smooth elbow (R/D = 1.5)`);\r\n    console.log(`✓ K-factor: ${kFactor}`);\r\n    console.log(`✓ Velocity: ${velocity} FPM`);\r\n    console.log(`✓ Velocity Pressure: ${velocityPressure.toFixed(4)} in wg`);\r\n    console.log(`✓ Pressure Loss: ${pressureLoss.toFixed(4)} in wg`);\r\n    \r\n    // Validate reasonable result\r\n    if (pressureLoss > 0 && pressureLoss < 1.0) {\r\n      console.log('✓ Pressure loss is within reasonable range');\r\n    } else {\r\n      console.log('⚠ Pressure loss may be outside expected range');\r\n    }\r\n    \r\n    return true;\r\n  } catch (error) {\r\n    console.error('✗ Error in fitting loss calculation:', error.message);\r\n    return false;\r\n  }\r\n}\r\n\r\n// Test 4: System calculation example (user's scenario)\r\nfunction testSystemCalculation() {\r\n  console.log('\\n=== TEST 4: System Calculation (10″ duct → 10′ run → 90° elbow → 10′ run) ===');\r\n  \r\n  try {\r\n    // Load fitting coefficients\r\n    const dataPath = path.join(__dirname, '../../data/fitting_coefficients.json');\r\n    const data = JSON.parse(fs.readFileSync(dataPath, 'utf8'));\r\n    \r\n    // System parameters\r\n    const airflow = 1000; // CFM\r\n    const diameter = 10; // inches\r\n    const straightLength1 = 10; // feet\r\n    const straightLength2 = 10; // feet\r\n    \r\n    // Calculate duct area and velocity\r\n    const area = Math.PI * Math.pow(diameter / 12, 2) / 4; // sq ft\r\n    const velocity = airflow / area; // FPM\r\n    \r\n    console.log(`✓ Duct: ${diameter}\" round`);\r\n    console.log(`✓ Airflow: ${airflow} CFM`);\r\n    console.log(`✓ Area: ${area.toFixed(3)} sq ft`);\r\n    console.log(`✓ Velocity: ${velocity.toFixed(0)} FPM`);\r\n    \r\n    // Calculate friction loss (simplified Darcy-Weisbach)\r\n    // Using typical values for galvanized steel\r\n    const roughness = 0.0005; // feet\r\n    const diameterFt = diameter / 12;\r\n    const velocityFps = velocity / 60;\r\n    const kinematicViscosity = 1.57e-4; // ft²/s\r\n    const airDensity = 0.075; // lb/ft³\r\n    \r\n    const reynolds = (velocityFps * diameterFt) / kinematicViscosity;\r\n    const relativeRoughness = roughness / diameterFt;\r\n    \r\n    // Simplified friction factor (Colebrook-White approximation)\r\n    const frictionFactor = 0.25 / Math.pow(Math.log10(relativeRoughness / 3.7 + 5.74 / Math.pow(reynolds, 0.9)), 2);\r\n    \r\n    // Friction loss per 100 feet\r\n    const frictionLossPer100ft = frictionFactor * (100 / diameterFt) * (airDensity * Math.pow(velocityFps, 2)) / (2 * 32.174 * 5.2);\r\n    \r\n    // Total friction loss for 20 feet\r\n    const totalFrictionLoss = frictionLossPer100ft * (straightLength1 + straightLength2) / 100;\r\n    \r\n    console.log(`✓ Reynolds Number: ${reynolds.toFixed(0)}`);\r\n    console.log(`✓ Friction Factor: ${frictionFactor.toFixed(4)}`);\r\n    console.log(`✓ Friction Loss: ${totalFrictionLoss.toFixed(4)} in wg`);\r\n    \r\n    // Calculate fitting loss for 90° elbow\r\n    const elbowKFactorData = data.round_fittings.elbows['90deg_round_smooth'].radius_to_diameter_ratios['1.5'];\r\n    const elbowKFactor = elbowKFactorData.K;\r\n    const velocityPressure = Math.pow(velocity / 4005, 2);\r\n    const fittingLoss = elbowKFactor * velocityPressure;\r\n    \r\n    console.log(`✓ Elbow K-factor: ${elbowKFactor}`);\r\n    console.log(`✓ Velocity Pressure: ${velocityPressure.toFixed(4)} in wg`);\r\n    console.log(`✓ Fitting Loss: ${fittingLoss.toFixed(4)} in wg`);\r\n    \r\n    // Total system pressure loss\r\n    const totalPressureLoss = totalFrictionLoss + fittingLoss;\r\n    \r\n    console.log(`✓ Total Friction Loss: ${totalFrictionLoss.toFixed(4)} in wg`);\r\n    console.log(`✓ Total Minor Loss: ${fittingLoss.toFixed(4)} in wg`);\r\n    console.log(`✓ TOTAL SYSTEM PRESSURE LOSS: ${totalPressureLoss.toFixed(4)} in wg`);\r\n    \r\n    // Validate results\r\n    if (totalPressureLoss > 0 && totalPressureLoss < 1.0) {\r\n      console.log('✓ Total pressure loss is within reasonable range for this system');\r\n    } else {\r\n      console.log('⚠ Total pressure loss may be outside expected range');\r\n    }\r\n    \r\n    // Calculate percentage breakdown\r\n    const frictionPercent = (totalFrictionLoss / totalPressureLoss * 100).toFixed(1);\r\n    const fittingPercent = (fittingLoss / totalPressureLoss * 100).toFixed(1);\r\n    \r\n    console.log(`✓ Friction losses: ${frictionPercent}% of total`);\r\n    console.log(`✓ Fitting losses: ${fittingPercent}% of total`);\r\n    \r\n    return true;\r\n  } catch (error) {\r\n    console.error('✗ Error in system calculation:', error.message);\r\n    return false;\r\n  }\r\n}\r\n\r\n// Test 5: SMACNA compliance check\r\nfunction testSMACNACompliance() {\r\n  console.log('\\n=== TEST 5: SMACNA Compliance Check ===');\r\n  \r\n  try {\r\n    // SMACNA velocity limits (FPM)\r\n    const velocityLimits = {\r\n      supply: { min: 400, max: 2500, recommended: 1500 },\r\n      return: { min: 300, max: 2000, recommended: 1200 },\r\n      exhaust: { min: 500, max: 3000, recommended: 1800 }\r\n    };\r\n    \r\n    // SMACNA pressure limits (inches w.g.)\r\n    const pressureLimits = {\r\n      supply: { max: 6.0, recommended: 4.0 },\r\n      return: { max: 4.0, recommended: 2.5 },\r\n      exhaust: { max: 8.0, recommended: 5.0 }\r\n    };\r\n    \r\n    // Test case from previous calculation\r\n    const velocity = 1833; // FPM\r\n    const pressureLoss = 0.05; // in wg (example)\r\n    const systemType = 'supply';\r\n    \r\n    console.log(`✓ System Type: ${systemType}`);\r\n    console.log(`✓ Velocity: ${velocity} FPM`);\r\n    console.log(`✓ Pressure Loss: ${pressureLoss} in wg`);\r\n    \r\n    // Check velocity compliance\r\n    const vLimits = velocityLimits[systemType];\r\n    const velocityCompliant = velocity >= vLimits.min && velocity <= vLimits.max;\r\n    const velocityOptimal = velocity <= vLimits.recommended;\r\n    \r\n    console.log(`✓ Velocity Range: ${vLimits.min}-${vLimits.max} FPM (recommended: ≤${vLimits.recommended})`);\r\n    console.log(`✓ Velocity Compliant: ${velocityCompliant ? 'YES' : 'NO'}`);\r\n    console.log(`✓ Velocity Optimal: ${velocityOptimal ? 'YES' : 'NO'}`);\r\n    \r\n    // Check pressure compliance\r\n    const pLimits = pressureLimits[systemType];\r\n    const pressureCompliant = pressureLoss <= pLimits.max;\r\n    const pressureOptimal = pressureLoss <= pLimits.recommended;\r\n    \r\n    console.log(`✓ Pressure Limit: ≤${pLimits.max} in wg (recommended: ≤${pLimits.recommended})`);\r\n    console.log(`✓ Pressure Compliant: ${pressureCompliant ? 'YES' : 'NO'}`);\r\n    console.log(`✓ Pressure Optimal: ${pressureOptimal ? 'YES' : 'NO'}`);\r\n    \r\n    // Overall compliance\r\n    const overallCompliant = velocityCompliant && pressureCompliant;\r\n    console.log(`✓ SMACNA Compliant: ${overallCompliant ? 'YES' : 'NO'}`);\r\n    \r\n    return true;\r\n  } catch (error) {\r\n    console.error('✗ Error in SMACNA compliance check:', error.message);\r\n    return false;\r\n  }\r\n}\r\n\r\n// Test 6: Enhanced Air Properties and Environmental Corrections\r\nfunction testEnhancedAirProperties() {\r\n  console.log('\\n=== TEST 6: Enhanced Air Properties ===');\r\n\r\n  try {\r\n    // Test air properties data loading\r\n    const fs = require('fs');\r\n    const path = require('path');\r\n\r\n    // Check if air properties data file exists\r\n    const airPropsPath = path.join(__dirname, '../../data/air_properties.json');\r\n    if (!fs.existsSync(airPropsPath)) {\r\n      console.log('⚠ Air properties data file not found, skipping enhanced tests');\r\n      return true;\r\n    }\r\n\r\n    const airPropsData = JSON.parse(fs.readFileSync(airPropsPath, 'utf8'));\r\n    console.log('✓ Air properties data loaded successfully');\r\n    console.log(`✓ Temperature range: ${Object.keys(airPropsData.temperature_properties).length} data points`);\r\n    console.log(`✓ Altitude corrections: ${Object.keys(airPropsData.altitude_corrections).length} data points`);\r\n    console.log(`✓ Humidity effects: ${Object.keys(airPropsData.humidity_effects).length} data points`);\r\n\r\n    // Test velocity pressure data\r\n    const vpPath = path.join(__dirname, '../../data/velocity_pressure.json');\r\n    if (fs.existsSync(vpPath)) {\r\n      const vpData = JSON.parse(fs.readFileSync(vpPath, 'utf8'));\r\n      console.log('✓ Velocity pressure lookup table loaded');\r\n      console.log(`✓ Velocity range: ${Object.keys(vpData.velocity_pressure_table).length} data points`);\r\n    }\r\n\r\n    // Test enhanced duct roughness data\r\n    const roughnessPath = path.join(__dirname, '../../data/duct_roughness.json');\r\n    if (fs.existsSync(roughnessPath)) {\r\n      const roughnessData = JSON.parse(fs.readFileSync(roughnessPath, 'utf8'));\r\n      console.log('✓ Enhanced duct roughness data loaded');\r\n      console.log(`✓ Materials available: ${Object.keys(roughnessData.materials).length}`);\r\n\r\n      // Test aging factors\r\n      const galvSteel = roughnessData.materials.galvanized_steel;\r\n      if (galvSteel && galvSteel.aging_factors) {\r\n        console.log(`✓ Aging factors for galvanized steel: ${Object.keys(galvSteel.aging_factors).length} age ranges`);\r\n      }\r\n    }\r\n\r\n    // Test environmental condition calculations\r\n    console.log('\\n--- Environmental Condition Tests ---');\r\n\r\n    // Standard conditions\r\n    const standardTemp = 70; // °F\r\n    const standardDensity = 0.075; // lb/ft³\r\n\r\n    // High temperature condition\r\n    const highTemp = 150; // °F\r\n    const tempCorrectionFactor = (standardTemp + 459.67) / (highTemp + 459.67);\r\n    console.log(`✓ Temperature correction (${highTemp}°F): ${tempCorrectionFactor.toFixed(3)}`);\r\n\r\n    // High altitude condition\r\n    const altitude = 5000; // feet (Denver)\r\n    const altitudeCorrectionFactor = Math.pow(1 - (altitude * 6.87535e-6), 5.2561);\r\n    console.log(`✓ Altitude correction (${altitude} ft): ${altitudeCorrectionFactor.toFixed(3)}`);\r\n\r\n    // Combined environmental effect\r\n    const combinedCorrection = tempCorrectionFactor * altitudeCorrectionFactor;\r\n    console.log(`✓ Combined environmental correction: ${combinedCorrection.toFixed(3)}`);\r\n\r\n    // Test material aging effects\r\n    console.log('\\n--- Material Aging Tests ---');\r\n\r\n    const baseRoughness = 0.0003; // feet (new galvanized steel)\r\n    const agingFactor = 1.5; // 10-year aging factor\r\n    const agedRoughness = baseRoughness * agingFactor;\r\n\r\n    console.log(`✓ Base roughness: ${baseRoughness} ft`);\r\n    console.log(`✓ Aged roughness (10 years): ${agedRoughness} ft`);\r\n    console.log(`✓ Roughness increase: ${((agingFactor - 1) * 100).toFixed(0)}%`);\r\n\r\n    return true;\r\n  } catch (error) {\r\n    console.error('✗ Error in enhanced air properties test:', error.message);\r\n    return false;\r\n  }\r\n}\r\n\r\n// Main test runner\r\nfunction runAllTests() {\r\n  console.log('COMPREHENSIVE DUCT PHYSICS IMPLEMENTATION - INTEGRATION TESTS');\r\n  console.log('==============================================================');\r\n  \r\n  const tests = [\r\n    testFittingCoefficientsData,\r\n    testVelocityPressureCalculation,\r\n    testFittingLossCalculation,\r\n    testSystemCalculation,\r\n    testSMACNACompliance,\r\n    testEnhancedAirProperties\r\n  ];\r\n  \r\n  let passedTests = 0;\r\n  let totalTests = tests.length;\r\n  \r\n  for (const test of tests) {\r\n    try {\r\n      if (test()) {\r\n        passedTests++;\r\n      }\r\n    } catch (error) {\r\n      console.error(`✗ Test failed with error: ${error.message}`);\r\n    }\r\n  }\r\n  \r\n  console.log('\\n=== TEST SUMMARY ===');\r\n  console.log(`Passed: ${passedTests}/${totalTests} tests`);\r\n  \r\n  if (passedTests === totalTests) {\r\n    console.log('🎉 ALL TESTS PASSED! Phase 2 enhanced implementation is working correctly.');\r\n    console.log('\\nPhase 2 deliverables completed:');\r\n    console.log('✓ Air Properties Database (air_properties.json) - comprehensive temperature, pressure, humidity data');\r\n    console.log('✓ Enhanced Duct Roughness Database (duct_roughness.json) - aging factors and surface conditions');\r\n    console.log('✓ Velocity Pressure Tables (velocity_pressure.json) - pre-calculated lookup tables');\r\n    console.log('✓ Advanced Calculation Options (AirPropertiesCalculator) - environmental corrections');\r\n    console.log('✓ Enhanced System Integration (SystemPressureCalculator) - elevation and aging effects');\r\n    console.log('✓ Comprehensive testing and validation framework');\r\n    console.log('\\nReady for production deployment with enhanced duct physics capabilities!');\r\n    return true;\r\n  } else {\r\n    console.log('❌ Some tests failed. Please review the implementation.');\r\n    return false;\r\n  }\r\n}\r\n\r\n// Run tests if this file is executed directly\r\nif (require.main === module) {\r\n  const success = runAllTests();\r\n  process.exit(success ? 0 : 1);\r\n}\r\n\r\nmodule.exports = {\r\n  runAllTests,\r\n  testFittingCoefficientsData,\r\n  testVelocityPressureCalculation,\r\n  testFittingLossCalculation,\r\n  testSystemCalculation,\r\n  testSMACNACompliance\r\n};\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAeY;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAfZ;AACA;AACA;AACA;;AAEA,MAAME,EAAE;AAAA;AAAA,CAAAF,cAAA,GAAAG,CAAA,OAAGC,OAAO,CAAC,IAAI,CAAC;AACxB,MAAMC,IAAI;AAAA;AAAA,CAAAL,cAAA,GAAAG,CAAA,OAAGC,OAAO,CAAC,MAAM,CAAC;;AAE5B;AACA,SAASE,2BAA2BA,CAAA,EAAG;EAAA;EAAAN,cAAA,GAAAO,CAAA;EAAAP,cAAA,GAAAG,CAAA;EACrCK,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;EAAC;EAAAT,cAAA,GAAAG,CAAA;EAE3D,IAAI;IACF,MAAMO,QAAQ;IAAA;IAAA,CAAAV,cAAA,GAAAG,CAAA,OAAGE,IAAI,CAACM,IAAI,CAACC,SAAS,EAAE,sCAAsC,CAAC;IAC7E,MAAMC,OAAO;IAAA;IAAA,CAAAb,cAAA,GAAAG,CAAA,OAAGD,EAAE,CAACY,YAAY,CAACJ,QAAQ,EAAE,MAAM,CAAC;IACjD,MAAMK,IAAI;IAAA;IAAA,CAAAf,cAAA,GAAAG,CAAA,OAAGa,IAAI,CAACC,KAAK,CAACJ,OAAO,CAAC;IAAC;IAAAb,cAAA,GAAAG,CAAA;IAEjCK,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;IAAC;IAAAT,cAAA,GAAAG,CAAA;IAC/DK,OAAO,CAACC,GAAG,CAAC,cAAcM,IAAI,CAACG,QAAQ,CAACC,OAAO,EAAE,CAAC;IAAC;IAAAnB,cAAA,GAAAG,CAAA;IACnDK,OAAO,CAACC,GAAG,CAAC,eAAeM,IAAI,CAACG,QAAQ,CAACE,QAAQ,EAAE,CAAC;;IAEpD;IACA,MAAMC,aAAa;IAAA;IAAA,CAAArB,cAAA,GAAAG,CAAA,QAAGY,IAAI,CAACO,cAAc;IACzC,MAAMC,SAAS;IAAA;IAAA,CAAAvB,cAAA,GAAAG,CAAA;IAAG;IAAA,CAAAH,cAAA,GAAAwB,CAAA,UAAAH,aAAa,CAACI,MAAM;IAAA;IAAA,CAAAzB,cAAA,GAAAwB,CAAA,UAAIE,MAAM,CAACC,IAAI,CAACN,aAAa,CAACI,MAAM,CAAC,CAACG,MAAM,GAAG,CAAC;IACtF,MAAMC,OAAO;IAAA;IAAA,CAAA7B,cAAA,GAAAG,CAAA;IAAG;IAAA,CAAAH,cAAA,GAAAwB,CAAA,UAAAH,aAAa,CAACS,IAAI;IAAA;IAAA,CAAA9B,cAAA,GAAAwB,CAAA,UAAIE,MAAM,CAACC,IAAI,CAACN,aAAa,CAACS,IAAI,CAAC,CAACF,MAAM,GAAG,CAAC;IAChF,MAAMG,cAAc;IAAA;IAAA,CAAA/B,cAAA,GAAAG,CAAA;IAAG;IAAA,CAAAH,cAAA,GAAAwB,CAAA,UAAAH,aAAa,CAACW,WAAW;IAAA;IAAA,CAAAhC,cAAA,GAAAwB,CAAA,UAAIE,MAAM,CAACC,IAAI,CAACN,aAAa,CAACW,WAAW,CAAC,CAACJ,MAAM,GAAG,CAAC;IAAC;IAAA5B,cAAA,GAAAG,CAAA;IAEtGK,OAAO,CAACC,GAAG,CAAC,mBAAmBc,SAAS;IAAA;IAAA,CAAAvB,cAAA,GAAAwB,CAAA,UAAG,SAAS;IAAA;IAAA,CAAAxB,cAAA,GAAAwB,CAAA,UAAG,SAAS,GAAE,CAAC;IAAC;IAAAxB,cAAA,GAAAG,CAAA;IACpEK,OAAO,CAACC,GAAG,CAAC,iBAAiBoB,OAAO;IAAA;IAAA,CAAA7B,cAAA,GAAAwB,CAAA,UAAG,SAAS;IAAA;IAAA,CAAAxB,cAAA,GAAAwB,CAAA,UAAG,SAAS,GAAE,CAAC;IAAC;IAAAxB,cAAA,GAAAG,CAAA;IAChEK,OAAO,CAACC,GAAG,CAAC,kBAAkBsB,cAAc;IAAA;IAAA,CAAA/B,cAAA,GAAAwB,CAAA,UAAG,SAAS;IAAA;IAAA,CAAAxB,cAAA,GAAAwB,CAAA,UAAG,SAAS,GAAE,CAAC;;IAEvE;IACA,MAAMS,WAAW;IAAA;IAAA,CAAAjC,cAAA,GAAAG,CAAA,QAAGkB,aAAa,CAACI,MAAM,CAAC,oBAAoB,CAAC;IAAC;IAAAzB,cAAA,GAAAG,CAAA;IAC/D;IAAI;IAAA,CAAAH,cAAA,GAAAwB,CAAA,UAAAS,WAAW;IAAA;IAAA,CAAAjC,cAAA,GAAAwB,CAAA,UAAIS,WAAW,CAACC,yBAAyB,GAAE;MAAA;MAAAlC,cAAA,GAAAwB,CAAA;MACxD,MAAMW,WAAW;MAAA;MAAA,CAAAnC,cAAA,GAAAG,CAAA,QAAG8B,WAAW,CAACC,yBAAyB,CAAC,KAAK,CAAC;MAChE,MAAME,OAAO;MAAA;MAAA,CAAApC,cAAA,GAAAG,CAAA,QAAGgC,WAAW;MAAA;MAAA,CAAAnC,cAAA,GAAAwB,CAAA,UAAGW,WAAW,CAACE,CAAC;MAAA;MAAA,CAAArC,cAAA,GAAAwB,CAAA,UAAG,WAAW;MAAC;MAAAxB,cAAA,GAAAG,CAAA;MAC1DK,OAAO,CAACC,GAAG,CAAC,wCAAwC2B,OAAO,EAAE,CAAC;IAChE,CAAC;IAAA;IAAA;MAAApC,cAAA,GAAAwB,CAAA;IAAA;IAAAxB,cAAA,GAAAG,CAAA;IAED,OAAO,IAAI;EACb,CAAC,CAAC,OAAOmC,KAAK,EAAE;IAAA;IAAAtC,cAAA,GAAAG,CAAA;IACdK,OAAO,CAAC8B,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAACC,OAAO,CAAC;IAAC;IAAAvC,cAAA,GAAAG,CAAA;IACtE,OAAO,KAAK;EACd;AACF;;AAEA;AACA,SAASqC,+BAA+BA,CAAA,EAAG;EAAA;EAAAxC,cAAA,GAAAO,CAAA;EAAAP,cAAA,GAAAG,CAAA;EACzCK,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;EAAC;EAAAT,cAAA,GAAAG,CAAA;EAE/D,IAAI;IACF;IACA,MAAMsC,QAAQ;IAAA;IAAA,CAAAzC,cAAA,GAAAG,CAAA,QAAG,IAAI,EAAC,CAAC;IACvB,MAAMuC,kBAAkB;IAAA;IAAA,CAAA1C,cAAA,GAAAG,CAAA,QAAG,KAAK,EAAC,CAAC;;IAElC,MAAMwC,gBAAgB;IAAA;IAAA,CAAA3C,cAAA,GAAAG,CAAA,QAAGyC,IAAI,CAACC,GAAG,CAACJ,QAAQ,GAAG,IAAI,EAAE,CAAC,CAAC,IAAIC,kBAAkB,GAAG,KAAK,CAAC;IAAC;IAAA1C,cAAA,GAAAG,CAAA;IAErFK,OAAO,CAACC,GAAG,CAAC,eAAegC,QAAQ,MAAM,CAAC;IAAC;IAAAzC,cAAA,GAAAG,CAAA;IAC3CK,OAAO,CAACC,GAAG,CAAC,wBAAwBkC,gBAAgB,CAACG,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC;;IAExE;IACA,MAAMC,aAAa;IAAA;IAAA,CAAA/C,cAAA,GAAAG,CAAA,QAAG,KAAK,EAAC,CAAC;IAC7B,MAAM6C,UAAU;IAAA;IAAA,CAAAhD,cAAA,GAAAG,CAAA,QAAGyC,IAAI,CAACC,GAAG,CAACJ,QAAQ,GAAG,IAAI,EAAE,CAAC,CAAC,IAAIM,aAAa,GAAG,KAAK,CAAC;IAAC;IAAA/C,cAAA,GAAAG,CAAA;IAE1EK,OAAO,CAACC,GAAG,CAAC,mBAAmBsC,aAAa,MAAMC,UAAU,CAACF,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC;IAAC;IAAA9C,cAAA,GAAAG,CAAA;IACjFK,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAACuC,UAAU,GAAGL,gBAAgB,EAAEG,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;IAAC;IAAA9C,cAAA,GAAAG,CAAA;IAErF,OAAO,IAAI;EACb,CAAC,CAAC,OAAOmC,KAAK,EAAE;IAAA;IAAAtC,cAAA,GAAAG,CAAA;IACdK,OAAO,CAAC8B,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAACC,OAAO,CAAC;IAAC;IAAAvC,cAAA,GAAAG,CAAA;IAC1E,OAAO,KAAK;EACd;AACF;;AAEA;AACA,SAAS8C,0BAA0BA,CAAA,EAAG;EAAA;EAAAjD,cAAA,GAAAO,CAAA;EAAAP,cAAA,GAAAG,CAAA;EACpCK,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;EAAC;EAAAT,cAAA,GAAAG,CAAA;EAE1D,IAAI;IACF;IACA,MAAMO,QAAQ;IAAA;IAAA,CAAAV,cAAA,GAAAG,CAAA,QAAGE,IAAI,CAACM,IAAI,CAACC,SAAS,EAAE,sCAAsC,CAAC;IAC7E,MAAMG,IAAI;IAAA;IAAA,CAAAf,cAAA,GAAAG,CAAA,QAAGa,IAAI,CAACC,KAAK,CAACf,EAAE,CAACY,YAAY,CAACJ,QAAQ,EAAE,MAAM,CAAC,CAAC;;IAE1D;IACA,MAAMyB,WAAW;IAAA;IAAA,CAAAnC,cAAA,GAAAG,CAAA,QAAGY,IAAI,CAACO,cAAc,CAACG,MAAM,CAAC,oBAAoB,CAAC,CAACS,yBAAyB,CAAC,KAAK,CAAC;IACrG,MAAME,OAAO;IAAA;IAAA,CAAApC,cAAA,GAAAG,CAAA,QAAGgC,WAAW,CAACE,CAAC;IAC7B,MAAMI,QAAQ;IAAA;IAAA,CAAAzC,cAAA,GAAAG,CAAA,QAAG,IAAI,EAAC,CAAC;IACvB,MAAM+C,UAAU;IAAA;IAAA,CAAAlD,cAAA,GAAAG,CAAA,QAAG,KAAK,EAAC,CAAC;;IAE1B;IACA,MAAMwC,gBAAgB;IAAA;IAAA,CAAA3C,cAAA,GAAAG,CAAA,QAAGyC,IAAI,CAACC,GAAG,CAACJ,QAAQ,GAAG,IAAI,EAAE,CAAC,CAAC,IAAIS,UAAU,GAAG,KAAK,CAAC;;IAE5E;IACA,MAAMC,YAAY;IAAA;IAAA,CAAAnD,cAAA,GAAAG,CAAA,QAAGiC,OAAO,GAAGO,gBAAgB;IAAC;IAAA3C,cAAA,GAAAG,CAAA;IAEhDK,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;IAAC;IAAAT,cAAA,GAAAG,CAAA;IACvDK,OAAO,CAACC,GAAG,CAAC,eAAe2B,OAAO,EAAE,CAAC;IAAC;IAAApC,cAAA,GAAAG,CAAA;IACtCK,OAAO,CAACC,GAAG,CAAC,eAAegC,QAAQ,MAAM,CAAC;IAAC;IAAAzC,cAAA,GAAAG,CAAA;IAC3CK,OAAO,CAACC,GAAG,CAAC,wBAAwBkC,gBAAgB,CAACG,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC;IAAC;IAAA9C,cAAA,GAAAG,CAAA;IACzEK,OAAO,CAACC,GAAG,CAAC,oBAAoB0C,YAAY,CAACL,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC;;IAEhE;IAAA;IAAA9C,cAAA,GAAAG,CAAA;IACA;IAAI;IAAA,CAAAH,cAAA,GAAAwB,CAAA,WAAA2B,YAAY,GAAG,CAAC;IAAA;IAAA,CAAAnD,cAAA,GAAAwB,CAAA,WAAI2B,YAAY,GAAG,GAAG,GAAE;MAAA;MAAAnD,cAAA,GAAAwB,CAAA;MAAAxB,cAAA,GAAAG,CAAA;MAC1CK,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;IAC3D,CAAC,MAAM;MAAA;MAAAT,cAAA,GAAAwB,CAAA;MAAAxB,cAAA,GAAAG,CAAA;MACLK,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;IAC9D;IAAC;IAAAT,cAAA,GAAAG,CAAA;IAED,OAAO,IAAI;EACb,CAAC,CAAC,OAAOmC,KAAK,EAAE;IAAA;IAAAtC,cAAA,GAAAG,CAAA;IACdK,OAAO,CAAC8B,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAACC,OAAO,CAAC;IAAC;IAAAvC,cAAA,GAAAG,CAAA;IACrE,OAAO,KAAK;EACd;AACF;;AAEA;AACA,SAASiD,qBAAqBA,CAAA,EAAG;EAAA;EAAApD,cAAA,GAAAO,CAAA;EAAAP,cAAA,GAAAG,CAAA;EAC/BK,OAAO,CAACC,GAAG,CAAC,iFAAiF,CAAC;EAAC;EAAAT,cAAA,GAAAG,CAAA;EAE/F,IAAI;IACF;IACA,MAAMO,QAAQ;IAAA;IAAA,CAAAV,cAAA,GAAAG,CAAA,QAAGE,IAAI,CAACM,IAAI,CAACC,SAAS,EAAE,sCAAsC,CAAC;IAC7E,MAAMG,IAAI;IAAA;IAAA,CAAAf,cAAA,GAAAG,CAAA,QAAGa,IAAI,CAACC,KAAK,CAACf,EAAE,CAACY,YAAY,CAACJ,QAAQ,EAAE,MAAM,CAAC,CAAC;;IAE1D;IACA,MAAM2C,OAAO;IAAA;IAAA,CAAArD,cAAA,GAAAG,CAAA,QAAG,IAAI,EAAC,CAAC;IACtB,MAAMmD,QAAQ;IAAA;IAAA,CAAAtD,cAAA,GAAAG,CAAA,QAAG,EAAE,EAAC,CAAC;IACrB,MAAMoD,eAAe;IAAA;IAAA,CAAAvD,cAAA,GAAAG,CAAA,QAAG,EAAE,EAAC,CAAC;IAC5B,MAAMqD,eAAe;IAAA;IAAA,CAAAxD,cAAA,GAAAG,CAAA,QAAG,EAAE,EAAC,CAAC;;IAE5B;IACA,MAAMsD,IAAI;IAAA;IAAA,CAAAzD,cAAA,GAAAG,CAAA,QAAGyC,IAAI,CAACc,EAAE,GAAGd,IAAI,CAACC,GAAG,CAACS,QAAQ,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAC,CAAC;IACvD,MAAMb,QAAQ;IAAA;IAAA,CAAAzC,cAAA,GAAAG,CAAA,QAAGkD,OAAO,GAAGI,IAAI,EAAC,CAAC;IAAA;IAAAzD,cAAA,GAAAG,CAAA;IAEjCK,OAAO,CAACC,GAAG,CAAC,WAAW6C,QAAQ,SAAS,CAAC;IAAC;IAAAtD,cAAA,GAAAG,CAAA;IAC1CK,OAAO,CAACC,GAAG,CAAC,cAAc4C,OAAO,MAAM,CAAC;IAAC;IAAArD,cAAA,GAAAG,CAAA;IACzCK,OAAO,CAACC,GAAG,CAAC,WAAWgD,IAAI,CAACX,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC;IAAC;IAAA9C,cAAA,GAAAG,CAAA;IAChDK,OAAO,CAACC,GAAG,CAAC,eAAegC,QAAQ,CAACK,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC;;IAErD;IACA;IACA,MAAMa,SAAS;IAAA;IAAA,CAAA3D,cAAA,GAAAG,CAAA,QAAG,MAAM,EAAC,CAAC;IAC1B,MAAMyD,UAAU;IAAA;IAAA,CAAA5D,cAAA,GAAAG,CAAA,QAAGmD,QAAQ,GAAG,EAAE;IAChC,MAAMO,WAAW;IAAA;IAAA,CAAA7D,cAAA,GAAAG,CAAA,QAAGsC,QAAQ,GAAG,EAAE;IACjC,MAAMqB,kBAAkB;IAAA;IAAA,CAAA9D,cAAA,GAAAG,CAAA,QAAG,OAAO,EAAC,CAAC;IACpC,MAAM+C,UAAU;IAAA;IAAA,CAAAlD,cAAA,GAAAG,CAAA,QAAG,KAAK,EAAC,CAAC;;IAE1B,MAAM4D,QAAQ;IAAA;IAAA,CAAA/D,cAAA,GAAAG,CAAA,QAAI0D,WAAW,GAAGD,UAAU,GAAIE,kBAAkB;IAChE,MAAME,iBAAiB;IAAA;IAAA,CAAAhE,cAAA,GAAAG,CAAA,QAAGwD,SAAS,GAAGC,UAAU;;IAEhD;IACA,MAAMK,cAAc;IAAA;IAAA,CAAAjE,cAAA,GAAAG,CAAA,QAAG,IAAI,GAAGyC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACsB,KAAK,CAACF,iBAAiB,GAAG,GAAG,GAAG,IAAI,GAAGpB,IAAI,CAACC,GAAG,CAACkB,QAAQ,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;;IAE/G;IACA,MAAMI,oBAAoB;IAAA;IAAA,CAAAnE,cAAA,GAAAG,CAAA,QAAG8D,cAAc,IAAI,GAAG,GAAGL,UAAU,CAAC,IAAIV,UAAU,GAAGN,IAAI,CAACC,GAAG,CAACgB,WAAW,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,MAAM,GAAG,GAAG,CAAC;;IAE/H;IACA,MAAMO,iBAAiB;IAAA;IAAA,CAAApE,cAAA,GAAAG,CAAA,QAAGgE,oBAAoB,IAAIZ,eAAe,GAAGC,eAAe,CAAC,GAAG,GAAG;IAAC;IAAAxD,cAAA,GAAAG,CAAA;IAE3FK,OAAO,CAACC,GAAG,CAAC,sBAAsBsD,QAAQ,CAACjB,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;IAAC;IAAA9C,cAAA,GAAAG,CAAA;IACzDK,OAAO,CAACC,GAAG,CAAC,sBAAsBwD,cAAc,CAACnB,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;IAAC;IAAA9C,cAAA,GAAAG,CAAA;IAC/DK,OAAO,CAACC,GAAG,CAAC,oBAAoB2D,iBAAiB,CAACtB,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC;;IAErE;IACA,MAAMuB,gBAAgB;IAAA;IAAA,CAAArE,cAAA,GAAAG,CAAA,QAAGY,IAAI,CAACO,cAAc,CAACG,MAAM,CAAC,oBAAoB,CAAC,CAACS,yBAAyB,CAAC,KAAK,CAAC;IAC1G,MAAMoC,YAAY;IAAA;IAAA,CAAAtE,cAAA,GAAAG,CAAA,QAAGkE,gBAAgB,CAAChC,CAAC;IACvC,MAAMM,gBAAgB;IAAA;IAAA,CAAA3C,cAAA,GAAAG,CAAA,QAAGyC,IAAI,CAACC,GAAG,CAACJ,QAAQ,GAAG,IAAI,EAAE,CAAC,CAAC;IACrD,MAAM8B,WAAW;IAAA;IAAA,CAAAvE,cAAA,GAAAG,CAAA,QAAGmE,YAAY,GAAG3B,gBAAgB;IAAC;IAAA3C,cAAA,GAAAG,CAAA;IAEpDK,OAAO,CAACC,GAAG,CAAC,qBAAqB6D,YAAY,EAAE,CAAC;IAAC;IAAAtE,cAAA,GAAAG,CAAA;IACjDK,OAAO,CAACC,GAAG,CAAC,wBAAwBkC,gBAAgB,CAACG,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC;IAAC;IAAA9C,cAAA,GAAAG,CAAA;IACzEK,OAAO,CAACC,GAAG,CAAC,mBAAmB8D,WAAW,CAACzB,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC;;IAE9D;IACA,MAAM0B,iBAAiB;IAAA;IAAA,CAAAxE,cAAA,GAAAG,CAAA,QAAGiE,iBAAiB,GAAGG,WAAW;IAAC;IAAAvE,cAAA,GAAAG,CAAA;IAE1DK,OAAO,CAACC,GAAG,CAAC,0BAA0B2D,iBAAiB,CAACtB,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC;IAAC;IAAA9C,cAAA,GAAAG,CAAA;IAC5EK,OAAO,CAACC,GAAG,CAAC,uBAAuB8D,WAAW,CAACzB,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC;IAAC;IAAA9C,cAAA,GAAAG,CAAA;IACnEK,OAAO,CAACC,GAAG,CAAC,iCAAiC+D,iBAAiB,CAAC1B,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC;;IAElF;IAAA;IAAA9C,cAAA,GAAAG,CAAA;IACA;IAAI;IAAA,CAAAH,cAAA,GAAAwB,CAAA,WAAAgD,iBAAiB,GAAG,CAAC;IAAA;IAAA,CAAAxE,cAAA,GAAAwB,CAAA,WAAIgD,iBAAiB,GAAG,GAAG,GAAE;MAAA;MAAAxE,cAAA,GAAAwB,CAAA;MAAAxB,cAAA,GAAAG,CAAA;MACpDK,OAAO,CAACC,GAAG,CAAC,kEAAkE,CAAC;IACjF,CAAC,MAAM;MAAA;MAAAT,cAAA,GAAAwB,CAAA;MAAAxB,cAAA,GAAAG,CAAA;MACLK,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;IACpE;;IAEA;IACA,MAAMgE,eAAe;IAAA;IAAA,CAAAzE,cAAA,GAAAG,CAAA,SAAG,CAACiE,iBAAiB,GAAGI,iBAAiB,GAAG,GAAG,EAAE1B,OAAO,CAAC,CAAC,CAAC;IAChF,MAAM4B,cAAc;IAAA;IAAA,CAAA1E,cAAA,GAAAG,CAAA,SAAG,CAACoE,WAAW,GAAGC,iBAAiB,GAAG,GAAG,EAAE1B,OAAO,CAAC,CAAC,CAAC;IAAC;IAAA9C,cAAA,GAAAG,CAAA;IAE1EK,OAAO,CAACC,GAAG,CAAC,sBAAsBgE,eAAe,YAAY,CAAC;IAAC;IAAAzE,cAAA,GAAAG,CAAA;IAC/DK,OAAO,CAACC,GAAG,CAAC,qBAAqBiE,cAAc,YAAY,CAAC;IAAC;IAAA1E,cAAA,GAAAG,CAAA;IAE7D,OAAO,IAAI;EACb,CAAC,CAAC,OAAOmC,KAAK,EAAE;IAAA;IAAAtC,cAAA,GAAAG,CAAA;IACdK,OAAO,CAAC8B,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAACC,OAAO,CAAC;IAAC;IAAAvC,cAAA,GAAAG,CAAA;IAC/D,OAAO,KAAK;EACd;AACF;;AAEA;AACA,SAASwE,oBAAoBA,CAAA,EAAG;EAAA;EAAA3E,cAAA,GAAAO,CAAA;EAAAP,cAAA,GAAAG,CAAA;EAC9BK,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;EAAC;EAAAT,cAAA,GAAAG,CAAA;EAEzD,IAAI;IACF;IACA,MAAMyE,cAAc;IAAA;IAAA,CAAA5E,cAAA,GAAAG,CAAA,SAAG;MACrB0E,MAAM,EAAE;QAAEC,GAAG,EAAE,GAAG;QAAEC,GAAG,EAAE,IAAI;QAAEC,WAAW,EAAE;MAAK,CAAC;MAClDC,MAAM,EAAE;QAAEH,GAAG,EAAE,GAAG;QAAEC,GAAG,EAAE,IAAI;QAAEC,WAAW,EAAE;MAAK,CAAC;MAClDE,OAAO,EAAE;QAAEJ,GAAG,EAAE,GAAG;QAAEC,GAAG,EAAE,IAAI;QAAEC,WAAW,EAAE;MAAK;IACpD,CAAC;;IAED;IACA,MAAMG,cAAc;IAAA;IAAA,CAAAnF,cAAA,GAAAG,CAAA,SAAG;MACrB0E,MAAM,EAAE;QAAEE,GAAG,EAAE,GAAG;QAAEC,WAAW,EAAE;MAAI,CAAC;MACtCC,MAAM,EAAE;QAAEF,GAAG,EAAE,GAAG;QAAEC,WAAW,EAAE;MAAI,CAAC;MACtCE,OAAO,EAAE;QAAEH,GAAG,EAAE,GAAG;QAAEC,WAAW,EAAE;MAAI;IACxC,CAAC;;IAED;IACA,MAAMvC,QAAQ;IAAA;IAAA,CAAAzC,cAAA,GAAAG,CAAA,SAAG,IAAI,EAAC,CAAC;IACvB,MAAMgD,YAAY;IAAA;IAAA,CAAAnD,cAAA,GAAAG,CAAA,SAAG,IAAI,EAAC,CAAC;IAC3B,MAAMiF,UAAU;IAAA;IAAA,CAAApF,cAAA,GAAAG,CAAA,SAAG,QAAQ;IAAC;IAAAH,cAAA,GAAAG,CAAA;IAE5BK,OAAO,CAACC,GAAG,CAAC,kBAAkB2E,UAAU,EAAE,CAAC;IAAC;IAAApF,cAAA,GAAAG,CAAA;IAC5CK,OAAO,CAACC,GAAG,CAAC,eAAegC,QAAQ,MAAM,CAAC;IAAC;IAAAzC,cAAA,GAAAG,CAAA;IAC3CK,OAAO,CAACC,GAAG,CAAC,oBAAoB0C,YAAY,QAAQ,CAAC;;IAErD;IACA,MAAMkC,OAAO;IAAA;IAAA,CAAArF,cAAA,GAAAG,CAAA,SAAGyE,cAAc,CAACQ,UAAU,CAAC;IAC1C,MAAME,iBAAiB;IAAA;IAAA,CAAAtF,cAAA,GAAAG,CAAA;IAAG;IAAA,CAAAH,cAAA,GAAAwB,CAAA,WAAAiB,QAAQ,IAAI4C,OAAO,CAACP,GAAG;IAAA;IAAA,CAAA9E,cAAA,GAAAwB,CAAA,WAAIiB,QAAQ,IAAI4C,OAAO,CAACN,GAAG;IAC5E,MAAMQ,eAAe;IAAA;IAAA,CAAAvF,cAAA,GAAAG,CAAA,SAAGsC,QAAQ,IAAI4C,OAAO,CAACL,WAAW;IAAC;IAAAhF,cAAA,GAAAG,CAAA;IAExDK,OAAO,CAACC,GAAG,CAAC,qBAAqB4E,OAAO,CAACP,GAAG,IAAIO,OAAO,CAACN,GAAG,uBAAuBM,OAAO,CAACL,WAAW,GAAG,CAAC;IAAC;IAAAhF,cAAA,GAAAG,CAAA;IAC1GK,OAAO,CAACC,GAAG,CAAC,yBAAyB6E,iBAAiB;IAAA;IAAA,CAAAtF,cAAA,GAAAwB,CAAA,WAAG,KAAK;IAAA;IAAA,CAAAxB,cAAA,GAAAwB,CAAA,WAAG,IAAI,GAAE,CAAC;IAAC;IAAAxB,cAAA,GAAAG,CAAA;IACzEK,OAAO,CAACC,GAAG,CAAC,uBAAuB8E,eAAe;IAAA;IAAA,CAAAvF,cAAA,GAAAwB,CAAA,WAAG,KAAK;IAAA;IAAA,CAAAxB,cAAA,GAAAwB,CAAA,WAAG,IAAI,GAAE,CAAC;;IAEpE;IACA,MAAMgE,OAAO;IAAA;IAAA,CAAAxF,cAAA,GAAAG,CAAA,SAAGgF,cAAc,CAACC,UAAU,CAAC;IAC1C,MAAMK,iBAAiB;IAAA;IAAA,CAAAzF,cAAA,GAAAG,CAAA,SAAGgD,YAAY,IAAIqC,OAAO,CAACT,GAAG;IACrD,MAAMW,eAAe;IAAA;IAAA,CAAA1F,cAAA,GAAAG,CAAA,SAAGgD,YAAY,IAAIqC,OAAO,CAACR,WAAW;IAAC;IAAAhF,cAAA,GAAAG,CAAA;IAE5DK,OAAO,CAACC,GAAG,CAAC,sBAAsB+E,OAAO,CAACT,GAAG,yBAAyBS,OAAO,CAACR,WAAW,GAAG,CAAC;IAAC;IAAAhF,cAAA,GAAAG,CAAA;IAC9FK,OAAO,CAACC,GAAG,CAAC,yBAAyBgF,iBAAiB;IAAA;IAAA,CAAAzF,cAAA,GAAAwB,CAAA,WAAG,KAAK;IAAA;IAAA,CAAAxB,cAAA,GAAAwB,CAAA,WAAG,IAAI,GAAE,CAAC;IAAC;IAAAxB,cAAA,GAAAG,CAAA;IACzEK,OAAO,CAACC,GAAG,CAAC,uBAAuBiF,eAAe;IAAA;IAAA,CAAA1F,cAAA,GAAAwB,CAAA,WAAG,KAAK;IAAA;IAAA,CAAAxB,cAAA,GAAAwB,CAAA,WAAG,IAAI,GAAE,CAAC;;IAEpE;IACA,MAAMmE,gBAAgB;IAAA;IAAA,CAAA3F,cAAA,GAAAG,CAAA;IAAG;IAAA,CAAAH,cAAA,GAAAwB,CAAA,WAAA8D,iBAAiB;IAAA;IAAA,CAAAtF,cAAA,GAAAwB,CAAA,WAAIiE,iBAAiB;IAAC;IAAAzF,cAAA,GAAAG,CAAA;IAChEK,OAAO,CAACC,GAAG,CAAC,uBAAuBkF,gBAAgB;IAAA;IAAA,CAAA3F,cAAA,GAAAwB,CAAA,WAAG,KAAK;IAAA;IAAA,CAAAxB,cAAA,GAAAwB,CAAA,WAAG,IAAI,GAAE,CAAC;IAAC;IAAAxB,cAAA,GAAAG,CAAA;IAEtE,OAAO,IAAI;EACb,CAAC,CAAC,OAAOmC,KAAK,EAAE;IAAA;IAAAtC,cAAA,GAAAG,CAAA;IACdK,OAAO,CAAC8B,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAACC,OAAO,CAAC;IAAC;IAAAvC,cAAA,GAAAG,CAAA;IACpE,OAAO,KAAK;EACd;AACF;;AAEA;AACA,SAASyF,yBAAyBA,CAAA,EAAG;EAAA;EAAA5F,cAAA,GAAAO,CAAA;EAAAP,cAAA,GAAAG,CAAA;EACnCK,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;EAAC;EAAAT,cAAA,GAAAG,CAAA;EAEzD,IAAI;IACF;IACA,MAAMD,EAAE;IAAA;IAAA,CAAAF,cAAA,GAAAG,CAAA,SAAGC,OAAO,CAAC,IAAI,CAAC;IACxB,MAAMC,IAAI;IAAA;IAAA,CAAAL,cAAA,GAAAG,CAAA,SAAGC,OAAO,CAAC,MAAM,CAAC;;IAE5B;IACA,MAAMyF,YAAY;IAAA;IAAA,CAAA7F,cAAA,GAAAG,CAAA,SAAGE,IAAI,CAACM,IAAI,CAACC,SAAS,EAAE,gCAAgC,CAAC;IAAC;IAAAZ,cAAA,GAAAG,CAAA;IAC5E,IAAI,CAACD,EAAE,CAAC4F,UAAU,CAACD,YAAY,CAAC,EAAE;MAAA;MAAA7F,cAAA,GAAAwB,CAAA;MAAAxB,cAAA,GAAAG,CAAA;MAChCK,OAAO,CAACC,GAAG,CAAC,+DAA+D,CAAC;MAAC;MAAAT,cAAA,GAAAG,CAAA;MAC7E,OAAO,IAAI;IACb,CAAC;IAAA;IAAA;MAAAH,cAAA,GAAAwB,CAAA;IAAA;IAED,MAAMuE,YAAY;IAAA;IAAA,CAAA/F,cAAA,GAAAG,CAAA,SAAGa,IAAI,CAACC,KAAK,CAACf,EAAE,CAACY,YAAY,CAAC+E,YAAY,EAAE,MAAM,CAAC,CAAC;IAAC;IAAA7F,cAAA,GAAAG,CAAA;IACvEK,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;IAAC;IAAAT,cAAA,GAAAG,CAAA;IACzDK,OAAO,CAACC,GAAG,CAAC,wBAAwBiB,MAAM,CAACC,IAAI,CAACoE,YAAY,CAACC,sBAAsB,CAAC,CAACpE,MAAM,cAAc,CAAC;IAAC;IAAA5B,cAAA,GAAAG,CAAA;IAC3GK,OAAO,CAACC,GAAG,CAAC,2BAA2BiB,MAAM,CAACC,IAAI,CAACoE,YAAY,CAACE,oBAAoB,CAAC,CAACrE,MAAM,cAAc,CAAC;IAAC;IAAA5B,cAAA,GAAAG,CAAA;IAC5GK,OAAO,CAACC,GAAG,CAAC,uBAAuBiB,MAAM,CAACC,IAAI,CAACoE,YAAY,CAACG,gBAAgB,CAAC,CAACtE,MAAM,cAAc,CAAC;;IAEnG;IACA,MAAMuE,MAAM;IAAA;IAAA,CAAAnG,cAAA,GAAAG,CAAA,SAAGE,IAAI,CAACM,IAAI,CAACC,SAAS,EAAE,mCAAmC,CAAC;IAAC;IAAAZ,cAAA,GAAAG,CAAA;IACzE,IAAID,EAAE,CAAC4F,UAAU,CAACK,MAAM,CAAC,EAAE;MAAA;MAAAnG,cAAA,GAAAwB,CAAA;MACzB,MAAM4E,MAAM;MAAA;MAAA,CAAApG,cAAA,GAAAG,CAAA,SAAGa,IAAI,CAACC,KAAK,CAACf,EAAE,CAACY,YAAY,CAACqF,MAAM,EAAE,MAAM,CAAC,CAAC;MAAC;MAAAnG,cAAA,GAAAG,CAAA;MAC3DK,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MAAC;MAAAT,cAAA,GAAAG,CAAA;MACvDK,OAAO,CAACC,GAAG,CAAC,qBAAqBiB,MAAM,CAACC,IAAI,CAACyE,MAAM,CAACC,uBAAuB,CAAC,CAACzE,MAAM,cAAc,CAAC;IACpG,CAAC;IAAA;IAAA;MAAA5B,cAAA,GAAAwB,CAAA;IAAA;;IAED;IACA,MAAM8E,aAAa;IAAA;IAAA,CAAAtG,cAAA,GAAAG,CAAA,SAAGE,IAAI,CAACM,IAAI,CAACC,SAAS,EAAE,gCAAgC,CAAC;IAAC;IAAAZ,cAAA,GAAAG,CAAA;IAC7E,IAAID,EAAE,CAAC4F,UAAU,CAACQ,aAAa,CAAC,EAAE;MAAA;MAAAtG,cAAA,GAAAwB,CAAA;MAChC,MAAM+E,aAAa;MAAA;MAAA,CAAAvG,cAAA,GAAAG,CAAA,SAAGa,IAAI,CAACC,KAAK,CAACf,EAAE,CAACY,YAAY,CAACwF,aAAa,EAAE,MAAM,CAAC,CAAC;MAAC;MAAAtG,cAAA,GAAAG,CAAA;MACzEK,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MAAC;MAAAT,cAAA,GAAAG,CAAA;MACrDK,OAAO,CAACC,GAAG,CAAC,0BAA0BiB,MAAM,CAACC,IAAI,CAAC4E,aAAa,CAACC,SAAS,CAAC,CAAC5E,MAAM,EAAE,CAAC;;MAEpF;MACA,MAAM6E,SAAS;MAAA;MAAA,CAAAzG,cAAA,GAAAG,CAAA,SAAGoG,aAAa,CAACC,SAAS,CAACE,gBAAgB;MAAC;MAAA1G,cAAA,GAAAG,CAAA;MAC3D;MAAI;MAAA,CAAAH,cAAA,GAAAwB,CAAA,WAAAiF,SAAS;MAAA;MAAA,CAAAzG,cAAA,GAAAwB,CAAA,WAAIiF,SAAS,CAACE,aAAa,GAAE;QAAA;QAAA3G,cAAA,GAAAwB,CAAA;QAAAxB,cAAA,GAAAG,CAAA;QACxCK,OAAO,CAACC,GAAG,CAAC,yCAAyCiB,MAAM,CAACC,IAAI,CAAC8E,SAAS,CAACE,aAAa,CAAC,CAAC/E,MAAM,aAAa,CAAC;MAChH,CAAC;MAAA;MAAA;QAAA5B,cAAA,GAAAwB,CAAA;MAAA;IACH,CAAC;IAAA;IAAA;MAAAxB,cAAA,GAAAwB,CAAA;IAAA;;IAED;IAAAxB,cAAA,GAAAG,CAAA;IACAK,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;;IAEtD;IACA,MAAMmG,YAAY;IAAA;IAAA,CAAA5G,cAAA,GAAAG,CAAA,SAAG,EAAE,EAAC,CAAC;IACzB,MAAM0G,eAAe;IAAA;IAAA,CAAA7G,cAAA,GAAAG,CAAA,SAAG,KAAK,EAAC,CAAC;;IAE/B;IACA,MAAM2G,QAAQ;IAAA;IAAA,CAAA9G,cAAA,GAAAG,CAAA,SAAG,GAAG,EAAC,CAAC;IACtB,MAAM4G,oBAAoB;IAAA;IAAA,CAAA/G,cAAA,GAAAG,CAAA,SAAG,CAACyG,YAAY,GAAG,MAAM,KAAKE,QAAQ,GAAG,MAAM,CAAC;IAAC;IAAA9G,cAAA,GAAAG,CAAA;IAC3EK,OAAO,CAACC,GAAG,CAAC,6BAA6BqG,QAAQ,QAAQC,oBAAoB,CAACjE,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;;IAE3F;IACA,MAAMkE,QAAQ;IAAA;IAAA,CAAAhH,cAAA,GAAAG,CAAA,SAAG,IAAI,EAAC,CAAC;IACvB,MAAM8G,wBAAwB;IAAA;IAAA,CAAAjH,cAAA,GAAAG,CAAA,SAAGyC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAImE,QAAQ,GAAG,UAAW,EAAE,MAAM,CAAC;IAAC;IAAAhH,cAAA,GAAAG,CAAA;IAC/EK,OAAO,CAACC,GAAG,CAAC,0BAA0BuG,QAAQ,SAASC,wBAAwB,CAACnE,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;;IAE7F;IACA,MAAMoE,kBAAkB;IAAA;IAAA,CAAAlH,cAAA,GAAAG,CAAA,SAAG4G,oBAAoB,GAAGE,wBAAwB;IAAC;IAAAjH,cAAA,GAAAG,CAAA;IAC3EK,OAAO,CAACC,GAAG,CAAC,wCAAwCyG,kBAAkB,CAACpE,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;;IAEpF;IAAA;IAAA9C,cAAA,GAAAG,CAAA;IACAK,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;IAE7C,MAAM0G,aAAa;IAAA;IAAA,CAAAnH,cAAA,GAAAG,CAAA,SAAG,MAAM,EAAC,CAAC;IAC9B,MAAMiH,WAAW;IAAA;IAAA,CAAApH,cAAA,GAAAG,CAAA,SAAG,GAAG,EAAC,CAAC;IACzB,MAAMkH,aAAa;IAAA;IAAA,CAAArH,cAAA,GAAAG,CAAA,SAAGgH,aAAa,GAAGC,WAAW;IAAC;IAAApH,cAAA,GAAAG,CAAA;IAElDK,OAAO,CAACC,GAAG,CAAC,qBAAqB0G,aAAa,KAAK,CAAC;IAAC;IAAAnH,cAAA,GAAAG,CAAA;IACrDK,OAAO,CAACC,GAAG,CAAC,gCAAgC4G,aAAa,KAAK,CAAC;IAAC;IAAArH,cAAA,GAAAG,CAAA;IAChEK,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC,CAAC2G,WAAW,GAAG,CAAC,IAAI,GAAG,EAAEtE,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IAAC;IAAA9C,cAAA,GAAAG,CAAA;IAE9E,OAAO,IAAI;EACb,CAAC,CAAC,OAAOmC,KAAK,EAAE;IAAA;IAAAtC,cAAA,GAAAG,CAAA;IACdK,OAAO,CAAC8B,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAACC,OAAO,CAAC;IAAC;IAAAvC,cAAA,GAAAG,CAAA;IACzE,OAAO,KAAK;EACd;AACF;;AAEA;AACA,SAASmH,WAAWA,CAAA,EAAG;EAAA;EAAAtH,cAAA,GAAAO,CAAA;EAAAP,cAAA,GAAAG,CAAA;EACrBK,OAAO,CAACC,GAAG,CAAC,+DAA+D,CAAC;EAAC;EAAAT,cAAA,GAAAG,CAAA;EAC7EK,OAAO,CAACC,GAAG,CAAC,gEAAgE,CAAC;EAE7E,MAAM8G,KAAK;EAAA;EAAA,CAAAvH,cAAA,GAAAG,CAAA,SAAG,CACZG,2BAA2B,EAC3BkC,+BAA+B,EAC/BS,0BAA0B,EAC1BG,qBAAqB,EACrBuB,oBAAoB,EACpBiB,yBAAyB,CAC1B;EAED,IAAI4B,WAAW;EAAA;EAAA,CAAAxH,cAAA,GAAAG,CAAA,SAAG,CAAC;EACnB,IAAIsH,UAAU;EAAA;EAAA,CAAAzH,cAAA,GAAAG,CAAA,SAAGoH,KAAK,CAAC3F,MAAM;EAAC;EAAA5B,cAAA,GAAAG,CAAA;EAE9B,KAAK,MAAMuH,IAAI,IAAIH,KAAK,EAAE;IAAA;IAAAvH,cAAA,GAAAG,CAAA;IACxB,IAAI;MAAA;MAAAH,cAAA,GAAAG,CAAA;MACF,IAAIuH,IAAI,CAAC,CAAC,EAAE;QAAA;QAAA1H,cAAA,GAAAwB,CAAA;QAAAxB,cAAA,GAAAG,CAAA;QACVqH,WAAW,EAAE;MACf,CAAC;MAAA;MAAA;QAAAxH,cAAA,GAAAwB,CAAA;MAAA;IACH,CAAC,CAAC,OAAOc,KAAK,EAAE;MAAA;MAAAtC,cAAA,GAAAG,CAAA;MACdK,OAAO,CAAC8B,KAAK,CAAC,6BAA6BA,KAAK,CAACC,OAAO,EAAE,CAAC;IAC7D;EACF;EAAC;EAAAvC,cAAA,GAAAG,CAAA;EAEDK,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;EAAC;EAAAT,cAAA,GAAAG,CAAA;EACtCK,OAAO,CAACC,GAAG,CAAC,WAAW+G,WAAW,IAAIC,UAAU,QAAQ,CAAC;EAAC;EAAAzH,cAAA,GAAAG,CAAA;EAE1D,IAAIqH,WAAW,KAAKC,UAAU,EAAE;IAAA;IAAAzH,cAAA,GAAAwB,CAAA;IAAAxB,cAAA,GAAAG,CAAA;IAC9BK,OAAO,CAACC,GAAG,CAAC,4EAA4E,CAAC;IAAC;IAAAT,cAAA,GAAAG,CAAA;IAC1FK,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;IAAC;IAAAT,cAAA,GAAAG,CAAA;IACjDK,OAAO,CAACC,GAAG,CAAC,sGAAsG,CAAC;IAAC;IAAAT,cAAA,GAAAG,CAAA;IACpHK,OAAO,CAACC,GAAG,CAAC,iGAAiG,CAAC;IAAC;IAAAT,cAAA,GAAAG,CAAA;IAC/GK,OAAO,CAACC,GAAG,CAAC,oFAAoF,CAAC;IAAC;IAAAT,cAAA,GAAAG,CAAA;IAClGK,OAAO,CAACC,GAAG,CAAC,sFAAsF,CAAC;IAAC;IAAAT,cAAA,GAAAG,CAAA;IACpGK,OAAO,CAACC,GAAG,CAAC,wFAAwF,CAAC;IAAC;IAAAT,cAAA,GAAAG,CAAA;IACtGK,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;IAAC;IAAAT,cAAA,GAAAG,CAAA;IAChEK,OAAO,CAACC,GAAG,CAAC,4EAA4E,CAAC;IAAC;IAAAT,cAAA,GAAAG,CAAA;IAC1F,OAAO,IAAI;EACb,CAAC,MAAM;IAAA;IAAAH,cAAA,GAAAwB,CAAA;IAAAxB,cAAA,GAAAG,CAAA;IACLK,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;IAAC;IAAAT,cAAA,GAAAG,CAAA;IACtE,OAAO,KAAK;EACd;AACF;;AAEA;AAAA;AAAAH,cAAA,GAAAG,CAAA;AACA,IAAIC,OAAO,CAACuH,IAAI,KAAKC,MAAM,EAAE;EAAA;EAAA5H,cAAA,GAAAwB,CAAA;EAC3B,MAAMqG,OAAO;EAAA;EAAA,CAAA7H,cAAA,GAAAG,CAAA,SAAGmH,WAAW,CAAC,CAAC;EAAC;EAAAtH,cAAA,GAAAG,CAAA;EAC9B2H,OAAO,CAACC,IAAI,CAACF,OAAO;EAAA;EAAA,CAAA7H,cAAA,GAAAwB,CAAA,WAAG,CAAC;EAAA;EAAA,CAAAxB,cAAA,GAAAwB,CAAA,WAAG,CAAC,EAAC;AAC/B,CAAC;AAAA;AAAA;EAAAxB,cAAA,GAAAwB,CAAA;AAAA;AAAAxB,cAAA,GAAAG,CAAA;AAEDyH,MAAM,CAACI,OAAO,GAAG;EACfV,WAAW;EACXhH,2BAA2B;EAC3BkC,+BAA+B;EAC/BS,0BAA0B;EAC1BG,qBAAqB;EACrBuB;AACF,CAAC", "ignoreList": []}