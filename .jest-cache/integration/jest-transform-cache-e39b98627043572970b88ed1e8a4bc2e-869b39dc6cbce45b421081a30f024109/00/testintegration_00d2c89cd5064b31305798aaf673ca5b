61044e2b3285347773dc92b6bb7d6566
/* istanbul ignore next */
function cov_1lxbffvxp9() {
  var path = "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\test-integration.js";
  var hash = "f4a0bd1c5794a70dad9bb2af0854e87a61a16f0a";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\test-integration.js",
    statementMap: {
      "0": {
        start: {
          line: 6,
          column: 11
        },
        end: {
          line: 6,
          column: 24
        }
      },
      "1": {
        start: {
          line: 7,
          column: 13
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "2": {
        start: {
          line: 11,
          column: 2
        },
        end: {
          line: 11,
          column: 61
        }
      },
      "3": {
        start: {
          line: 13,
          column: 2
        },
        end: {
          line: 44,
          column: 3
        }
      },
      "4": {
        start: {
          line: 14,
          column: 21
        },
        end: {
          line: 14,
          column: 81
        }
      },
      "5": {
        start: {
          line: 15,
          column: 20
        },
        end: {
          line: 15,
          column: 53
        }
      },
      "6": {
        start: {
          line: 16,
          column: 17
        },
        end: {
          line: 16,
          column: 36
        }
      },
      "7": {
        start: {
          line: 18,
          column: 4
        },
        end: {
          line: 18,
          column: 67
        }
      },
      "8": {
        start: {
          line: 19,
          column: 4
        },
        end: {
          line: 19,
          column: 55
        }
      },
      "9": {
        start: {
          line: 20,
          column: 4
        },
        end: {
          line: 20,
          column: 57
        }
      },
      "10": {
        start: {
          line: 23,
          column: 26
        },
        end: {
          line: 23,
          column: 45
        }
      },
      "11": {
        start: {
          line: 24,
          column: 22
        },
        end: {
          line: 24,
          column: 90
        }
      },
      "12": {
        start: {
          line: 25,
          column: 20
        },
        end: {
          line: 25,
          column: 84
        }
      },
      "13": {
        start: {
          line: 26,
          column: 27
        },
        end: {
          line: 26,
          column: 105
        }
      },
      "14": {
        start: {
          line: 28,
          column: 4
        },
        end: {
          line: 28,
          column: 72
        }
      },
      "15": {
        start: {
          line: 29,
          column: 4
        },
        end: {
          line: 29,
          column: 68
        }
      },
      "16": {
        start: {
          line: 30,
          column: 4
        },
        end: {
          line: 30,
          column: 76
        }
      },
      "17": {
        start: {
          line: 33,
          column: 24
        },
        end: {
          line: 33,
          column: 66
        }
      },
      "18": {
        start: {
          line: 34,
          column: 4
        },
        end: {
          line: 38,
          column: 5
        }
      },
      "19": {
        start: {
          line: 35,
          column: 26
        },
        end: {
          line: 35,
          column: 70
        }
      },
      "20": {
        start: {
          line: 36,
          column: 22
        },
        end: {
          line: 36,
          column: 63
        }
      },
      "21": {
        start: {
          line: 37,
          column: 6
        },
        end: {
          line: 37,
          column: 69
        }
      },
      "22": {
        start: {
          line: 40,
          column: 4
        },
        end: {
          line: 40,
          column: 16
        }
      },
      "23": {
        start: {
          line: 42,
          column: 4
        },
        end: {
          line: 42,
          column: 74
        }
      },
      "24": {
        start: {
          line: 43,
          column: 4
        },
        end: {
          line: 43,
          column: 17
        }
      },
      "25": {
        start: {
          line: 49,
          column: 2
        },
        end: {
          line: 49,
          column: 65
        }
      },
      "26": {
        start: {
          line: 51,
          column: 2
        },
        end: {
          line: 72,
          column: 3
        }
      },
      "27": {
        start: {
          line: 53,
          column: 21
        },
        end: {
          line: 53,
          column: 25
        }
      },
      "28": {
        start: {
          line: 54,
          column: 31
        },
        end: {
          line: 54,
          column: 36
        }
      },
      "29": {
        start: {
          line: 56,
          column: 29
        },
        end: {
          line: 56,
          column: 88
        }
      },
      "30": {
        start: {
          line: 58,
          column: 4
        },
        end: {
          line: 58,
          column: 47
        }
      },
      "31": {
        start: {
          line: 59,
          column: 4
        },
        end: {
          line: 59,
          column: 77
        }
      },
      "32": {
        start: {
          line: 62,
          column: 26
        },
        end: {
          line: 62,
          column: 31
        }
      },
      "33": {
        start: {
          line: 63,
          column: 23
        },
        end: {
          line: 63,
          column: 77
        }
      },
      "34": {
        start: {
          line: 65,
          column: 4
        },
        end: {
          line: 65,
          column: 85
        }
      },
      "35": {
        start: {
          line: 66,
          column: 4
        },
        end: {
          line: 66,
          column: 89
        }
      },
      "36": {
        start: {
          line: 68,
          column: 4
        },
        end: {
          line: 68,
          column: 16
        }
      },
      "37": {
        start: {
          line: 70,
          column: 4
        },
        end: {
          line: 70,
          column: 78
        }
      },
      "38": {
        start: {
          line: 71,
          column: 4
        },
        end: {
          line: 71,
          column: 17
        }
      },
      "39": {
        start: {
          line: 77,
          column: 2
        },
        end: {
          line: 77,
          column: 60
        }
      },
      "40": {
        start: {
          line: 79,
          column: 2
        },
        end: {
          line: 113,
          column: 3
        }
      },
      "41": {
        start: {
          line: 81,
          column: 21
        },
        end: {
          line: 81,
          column: 81
        }
      },
      "42": {
        start: {
          line: 82,
          column: 17
        },
        end: {
          line: 82,
          column: 62
        }
      },
      "43": {
        start: {
          line: 85,
          column: 24
        },
        end: {
          line: 85,
          column: 105
        }
      },
      "44": {
        start: {
          line: 86,
          column: 20
        },
        end: {
          line: 86,
          column: 33
        }
      },
      "45": {
        start: {
          line: 87,
          column: 21
        },
        end: {
          line: 87,
          column: 25
        }
      },
      "46": {
        start: {
          line: 88,
          column: 23
        },
        end: {
          line: 88,
          column: 28
        }
      },
      "47": {
        start: {
          line: 91,
          column: 29
        },
        end: {
          line: 91,
          column: 80
        }
      },
      "48": {
        start: {
          line: 94,
          column: 25
        },
        end: {
          line: 94,
          column: 51
        }
      },
      "49": {
        start: {
          line: 96,
          column: 4
        },
        end: {
          line: 96,
          column: 59
        }
      },
      "50": {
        start: {
          line: 97,
          column: 4
        },
        end: {
          line: 97,
          column: 42
        }
      },
      "51": {
        start: {
          line: 98,
          column: 4
        },
        end: {
          line: 98,
          column: 47
        }
      },
      "52": {
        start: {
          line: 99,
          column: 4
        },
        end: {
          line: 99,
          column: 77
        }
      },
      "53": {
        start: {
          line: 100,
          column: 4
        },
        end: {
          line: 100,
          column: 69
        }
      },
      "54": {
        start: {
          line: 103,
          column: 4
        },
        end: {
          line: 107,
          column: 5
        }
      },
      "55": {
        start: {
          line: 104,
          column: 6
        },
        end: {
          line: 104,
          column: 64
        }
      },
      "56": {
        start: {
          line: 106,
          column: 6
        },
        end: {
          line: 106,
          column: 67
        }
      },
      "57": {
        start: {
          line: 109,
          column: 4
        },
        end: {
          line: 109,
          column: 16
        }
      },
      "58": {
        start: {
          line: 111,
          column: 4
        },
        end: {
          line: 111,
          column: 73
        }
      },
      "59": {
        start: {
          line: 112,
          column: 4
        },
        end: {
          line: 112,
          column: 17
        }
      },
      "60": {
        start: {
          line: 118,
          column: 2
        },
        end: {
          line: 118,
          column: 97
        }
      },
      "61": {
        start: {
          line: 120,
          column: 2
        },
        end: {
          line: 199,
          column: 3
        }
      },
      "62": {
        start: {
          line: 122,
          column: 21
        },
        end: {
          line: 122,
          column: 81
        }
      },
      "63": {
        start: {
          line: 123,
          column: 17
        },
        end: {
          line: 123,
          column: 62
        }
      },
      "64": {
        start: {
          line: 126,
          column: 20
        },
        end: {
          line: 126,
          column: 24
        }
      },
      "65": {
        start: {
          line: 127,
          column: 21
        },
        end: {
          line: 127,
          column: 23
        }
      },
      "66": {
        start: {
          line: 128,
          column: 28
        },
        end: {
          line: 128,
          column: 30
        }
      },
      "67": {
        start: {
          line: 129,
          column: 28
        },
        end: {
          line: 129,
          column: 30
        }
      },
      "68": {
        start: {
          line: 132,
          column: 17
        },
        end: {
          line: 132,
          column: 57
        }
      },
      "69": {
        start: {
          line: 133,
          column: 21
        },
        end: {
          line: 133,
          column: 35
        }
      },
      "70": {
        start: {
          line: 135,
          column: 4
        },
        end: {
          line: 135,
          column: 46
        }
      },
      "71": {
        start: {
          line: 136,
          column: 4
        },
        end: {
          line: 136,
          column: 45
        }
      },
      "72": {
        start: {
          line: 137,
          column: 4
        },
        end: {
          line: 137,
          column: 52
        }
      },
      "73": {
        start: {
          line: 138,
          column: 4
        },
        end: {
          line: 138,
          column: 58
        }
      },
      "74": {
        start: {
          line: 142,
          column: 22
        },
        end: {
          line: 142,
          column: 28
        }
      },
      "75": {
        start: {
          line: 143,
          column: 23
        },
        end: {
          line: 143,
          column: 36
        }
      },
      "76": {
        start: {
          line: 144,
          column: 24
        },
        end: {
          line: 144,
          column: 37
        }
      },
      "77": {
        start: {
          line: 145,
          column: 31
        },
        end: {
          line: 145,
          column: 38
        }
      },
      "78": {
        start: {
          line: 146,
          column: 23
        },
        end: {
          line: 146,
          column: 28
        }
      },
      "79": {
        start: {
          line: 148,
          column: 21
        },
        end: {
          line: 148,
          column: 68
        }
      },
      "80": {
        start: {
          line: 149,
          column: 30
        },
        end: {
          line: 149,
          column: 52
        }
      },
      "81": {
        start: {
          line: 152,
          column: 27
        },
        end: {
          line: 152,
          column: 115
        }
      },
      "82": {
        start: {
          line: 155,
          column: 33
        },
        end: {
          line: 155,
          column: 131
        }
      },
      "83": {
        start: {
          line: 158,
          column: 30
        },
        end: {
          line: 158,
          column: 94
        }
      },
      "84": {
        start: {
          line: 160,
          column: 4
        },
        end: {
          line: 160,
          column: 61
        }
      },
      "85": {
        start: {
          line: 161,
          column: 4
        },
        end: {
          line: 161,
          column: 67
        }
      },
      "86": {
        start: {
          line: 162,
          column: 4
        },
        end: {
          line: 162,
          column: 74
        }
      },
      "87": {
        start: {
          line: 165,
          column: 29
        },
        end: {
          line: 165,
          column: 110
        }
      },
      "88": {
        start: {
          line: 166,
          column: 25
        },
        end: {
          line: 166,
          column: 43
        }
      },
      "89": {
        start: {
          line: 167,
          column: 29
        },
        end: {
          line: 167,
          column: 57
        }
      },
      "90": {
        start: {
          line: 168,
          column: 24
        },
        end: {
          line: 168,
          column: 55
        }
      },
      "91": {
        start: {
          line: 170,
          column: 4
        },
        end: {
          line: 170,
          column: 53
        }
      },
      "92": {
        start: {
          line: 171,
          column: 4
        },
        end: {
          line: 171,
          column: 77
        }
      },
      "93": {
        start: {
          line: 172,
          column: 4
        },
        end: {
          line: 172,
          column: 67
        }
      },
      "94": {
        start: {
          line: 175,
          column: 30
        },
        end: {
          line: 175,
          column: 61
        }
      },
      "95": {
        start: {
          line: 177,
          column: 4
        },
        end: {
          line: 177,
          column: 80
        }
      },
      "96": {
        start: {
          line: 178,
          column: 4
        },
        end: {
          line: 178,
          column: 71
        }
      },
      "97": {
        start: {
          line: 179,
          column: 4
        },
        end: {
          line: 179,
          column: 87
        }
      },
      "98": {
        start: {
          line: 182,
          column: 4
        },
        end: {
          line: 186,
          column: 5
        }
      },
      "99": {
        start: {
          line: 183,
          column: 6
        },
        end: {
          line: 183,
          column: 86
        }
      },
      "100": {
        start: {
          line: 185,
          column: 6
        },
        end: {
          line: 185,
          column: 73
        }
      },
      "101": {
        start: {
          line: 189,
          column: 28
        },
        end: {
          line: 189,
          column: 84
        }
      },
      "102": {
        start: {
          line: 190,
          column: 27
        },
        end: {
          line: 190,
          column: 77
        }
      },
      "103": {
        start: {
          line: 192,
          column: 4
        },
        end: {
          line: 192,
          column: 67
        }
      },
      "104": {
        start: {
          line: 193,
          column: 4
        },
        end: {
          line: 193,
          column: 65
        }
      },
      "105": {
        start: {
          line: 195,
          column: 4
        },
        end: {
          line: 195,
          column: 16
        }
      },
      "106": {
        start: {
          line: 197,
          column: 4
        },
        end: {
          line: 197,
          column: 67
        }
      },
      "107": {
        start: {
          line: 198,
          column: 4
        },
        end: {
          line: 198,
          column: 17
        }
      },
      "108": {
        start: {
          line: 204,
          column: 2
        },
        end: {
          line: 204,
          column: 59
        }
      },
      "109": {
        start: {
          line: 206,
          column: 2
        },
        end: {
          line: 256,
          column: 3
        }
      },
      "110": {
        start: {
          line: 208,
          column: 27
        },
        end: {
          line: 212,
          column: 5
        }
      },
      "111": {
        start: {
          line: 215,
          column: 27
        },
        end: {
          line: 219,
          column: 5
        }
      },
      "112": {
        start: {
          line: 222,
          column: 21
        },
        end: {
          line: 222,
          column: 25
        }
      },
      "113": {
        start: {
          line: 223,
          column: 25
        },
        end: {
          line: 223,
          column: 29
        }
      },
      "114": {
        start: {
          line: 224,
          column: 23
        },
        end: {
          line: 224,
          column: 31
        }
      },
      "115": {
        start: {
          line: 226,
          column: 4
        },
        end: {
          line: 226,
          column: 48
        }
      },
      "116": {
        start: {
          line: 227,
          column: 4
        },
        end: {
          line: 227,
          column: 47
        }
      },
      "117": {
        start: {
          line: 228,
          column: 4
        },
        end: {
          line: 228,
          column: 58
        }
      },
      "118": {
        start: {
          line: 231,
          column: 20
        },
        end: {
          line: 231,
          column: 46
        }
      },
      "119": {
        start: {
          line: 232,
          column: 30
        },
        end: {
          line: 232,
          column: 80
        }
      },
      "120": {
        start: {
          line: 233,
          column: 28
        },
        end: {
          line: 233,
          column: 59
        }
      },
      "121": {
        start: {
          line: 235,
          column: 4
        },
        end: {
          line: 235,
          column: 110
        }
      },
      "122": {
        start: {
          line: 236,
          column: 4
        },
        end: {
          line: 236,
          column: 77
        }
      },
      "123": {
        start: {
          line: 237,
          column: 4
        },
        end: {
          line: 237,
          column: 73
        }
      },
      "124": {
        start: {
          line: 240,
          column: 20
        },
        end: {
          line: 240,
          column: 46
        }
      },
      "125": {
        start: {
          line: 241,
          column: 30
        },
        end: {
          line: 241,
          column: 57
        }
      },
      "126": {
        start: {
          line: 242,
          column: 28
        },
        end: {
          line: 242,
          column: 63
        }
      },
      "127": {
        start: {
          line: 244,
          column: 4
        },
        end: {
          line: 244,
          column: 98
        }
      },
      "128": {
        start: {
          line: 245,
          column: 4
        },
        end: {
          line: 245,
          column: 77
        }
      },
      "129": {
        start: {
          line: 246,
          column: 4
        },
        end: {
          line: 246,
          column: 73
        }
      },
      "130": {
        start: {
          line: 249,
          column: 29
        },
        end: {
          line: 249,
          column: 67
        }
      },
      "131": {
        start: {
          line: 250,
          column: 4
        },
        end: {
          line: 250,
          column: 74
        }
      },
      "132": {
        start: {
          line: 252,
          column: 4
        },
        end: {
          line: 252,
          column: 16
        }
      },
      "133": {
        start: {
          line: 254,
          column: 4
        },
        end: {
          line: 254,
          column: 72
        }
      },
      "134": {
        start: {
          line: 255,
          column: 4
        },
        end: {
          line: 255,
          column: 17
        }
      },
      "135": {
        start: {
          line: 261,
          column: 2
        },
        end: {
          line: 261,
          column: 59
        }
      },
      "136": {
        start: {
          line: 263,
          column: 2
        },
        end: {
          line: 339,
          column: 3
        }
      },
      "137": {
        start: {
          line: 265,
          column: 15
        },
        end: {
          line: 265,
          column: 28
        }
      },
      "138": {
        start: {
          line: 266,
          column: 17
        },
        end: {
          line: 266,
          column: 32
        }
      },
      "139": {
        start: {
          line: 269,
          column: 25
        },
        end: {
          line: 269,
          column: 79
        }
      },
      "140": {
        start: {
          line: 270,
          column: 4
        },
        end: {
          line: 273,
          column: 5
        }
      },
      "141": {
        start: {
          line: 271,
          column: 6
        },
        end: {
          line: 271,
          column: 83
        }
      },
      "142": {
        start: {
          line: 272,
          column: 6
        },
        end: {
          line: 272,
          column: 18
        }
      },
      "143": {
        start: {
          line: 275,
          column: 25
        },
        end: {
          line: 275,
          column: 74
        }
      },
      "144": {
        start: {
          line: 276,
          column: 4
        },
        end: {
          line: 276,
          column: 61
        }
      },
      "145": {
        start: {
          line: 277,
          column: 4
        },
        end: {
          line: 277,
          column: 111
        }
      },
      "146": {
        start: {
          line: 278,
          column: 4
        },
        end: {
          line: 278,
          column: 112
        }
      },
      "147": {
        start: {
          line: 279,
          column: 4
        },
        end: {
          line: 279,
          column: 104
        }
      },
      "148": {
        start: {
          line: 282,
          column: 19
        },
        end: {
          line: 282,
          column: 76
        }
      },
      "149": {
        start: {
          line: 283,
          column: 4
        },
        end: {
          line: 287,
          column: 5
        }
      },
      "150": {
        start: {
          line: 284,
          column: 21
        },
        end: {
          line: 284,
          column: 64
        }
      },
      "151": {
        start: {
          line: 285,
          column: 6
        },
        end: {
          line: 285,
          column: 61
        }
      },
      "152": {
        start: {
          line: 286,
          column: 6
        },
        end: {
          line: 286,
          column: 105
        }
      },
      "153": {
        start: {
          line: 290,
          column: 26
        },
        end: {
          line: 290,
          column: 80
        }
      },
      "154": {
        start: {
          line: 291,
          column: 4
        },
        end: {
          line: 301,
          column: 5
        }
      },
      "155": {
        start: {
          line: 292,
          column: 28
        },
        end: {
          line: 292,
          column: 78
        }
      },
      "156": {
        start: {
          line: 293,
          column: 6
        },
        end: {
          line: 293,
          column: 59
        }
      },
      "157": {
        start: {
          line: 294,
          column: 6
        },
        end: {
          line: 294,
          column: 91
        }
      },
      "158": {
        start: {
          line: 297,
          column: 24
        },
        end: {
          line: 297,
          column: 64
        }
      },
      "159": {
        start: {
          line: 298,
          column: 6
        },
        end: {
          line: 300,
          column: 7
        }
      },
      "160": {
        start: {
          line: 299,
          column: 8
        },
        end: {
          line: 299,
          column: 119
        }
      },
      "161": {
        start: {
          line: 304,
          column: 4
        },
        end: {
          line: 304,
          column: 59
        }
      },
      "162": {
        start: {
          line: 307,
          column: 25
        },
        end: {
          line: 307,
          column: 27
        }
      },
      "163": {
        start: {
          line: 308,
          column: 28
        },
        end: {
          line: 308,
          column: 33
        }
      },
      "164": {
        start: {
          line: 311,
          column: 21
        },
        end: {
          line: 311,
          column: 24
        }
      },
      "165": {
        start: {
          line: 312,
          column: 33
        },
        end: {
          line: 312,
          column: 78
        }
      },
      "166": {
        start: {
          line: 313,
          column: 4
        },
        end: {
          line: 313,
          column: 96
        }
      },
      "167": {
        start: {
          line: 316,
          column: 21
        },
        end: {
          line: 316,
          column: 25
        }
      },
      "168": {
        start: {
          line: 317,
          column: 37
        },
        end: {
          line: 317,
          column: 82
        }
      },
      "169": {
        start: {
          line: 318,
          column: 4
        },
        end: {
          line: 318,
          column: 98
        }
      },
      "170": {
        start: {
          line: 321,
          column: 31
        },
        end: {
          line: 321,
          column: 78
        }
      },
      "171": {
        start: {
          line: 322,
          column: 4
        },
        end: {
          line: 322,
          column: 89
        }
      },
      "172": {
        start: {
          line: 325,
          column: 4
        },
        end: {
          line: 325,
          column: 50
        }
      },
      "173": {
        start: {
          line: 327,
          column: 26
        },
        end: {
          line: 327,
          column: 32
        }
      },
      "174": {
        start: {
          line: 328,
          column: 24
        },
        end: {
          line: 328,
          column: 27
        }
      },
      "175": {
        start: {
          line: 329,
          column: 26
        },
        end: {
          line: 329,
          column: 53
        }
      },
      "176": {
        start: {
          line: 331,
          column: 4
        },
        end: {
          line: 331,
          column: 57
        }
      },
      "177": {
        start: {
          line: 332,
          column: 4
        },
        end: {
          line: 332,
          column: 68
        }
      },
      "178": {
        start: {
          line: 333,
          column: 4
        },
        end: {
          line: 333,
          column: 82
        }
      },
      "179": {
        start: {
          line: 335,
          column: 4
        },
        end: {
          line: 335,
          column: 16
        }
      },
      "180": {
        start: {
          line: 337,
          column: 4
        },
        end: {
          line: 337,
          column: 77
        }
      },
      "181": {
        start: {
          line: 338,
          column: 4
        },
        end: {
          line: 338,
          column: 17
        }
      },
      "182": {
        start: {
          line: 344,
          column: 2
        },
        end: {
          line: 344,
          column: 79
        }
      },
      "183": {
        start: {
          line: 345,
          column: 2
        },
        end: {
          line: 345,
          column: 80
        }
      },
      "184": {
        start: {
          line: 347,
          column: 16
        },
        end: {
          line: 354,
          column: 3
        }
      },
      "185": {
        start: {
          line: 356,
          column: 20
        },
        end: {
          line: 356,
          column: 21
        }
      },
      "186": {
        start: {
          line: 357,
          column: 19
        },
        end: {
          line: 357,
          column: 31
        }
      },
      "187": {
        start: {
          line: 359,
          column: 2
        },
        end: {
          line: 367,
          column: 3
        }
      },
      "188": {
        start: {
          line: 360,
          column: 4
        },
        end: {
          line: 366,
          column: 5
        }
      },
      "189": {
        start: {
          line: 361,
          column: 6
        },
        end: {
          line: 363,
          column: 7
        }
      },
      "190": {
        start: {
          line: 362,
          column: 8
        },
        end: {
          line: 362,
          column: 22
        }
      },
      "191": {
        start: {
          line: 365,
          column: 6
        },
        end: {
          line: 365,
          column: 66
        }
      },
      "192": {
        start: {
          line: 369,
          column: 2
        },
        end: {
          line: 369,
          column: 40
        }
      },
      "193": {
        start: {
          line: 370,
          column: 2
        },
        end: {
          line: 370,
          column: 60
        }
      },
      "194": {
        start: {
          line: 372,
          column: 2
        },
        end: {
          line: 386,
          column: 3
        }
      },
      "195": {
        start: {
          line: 373,
          column: 4
        },
        end: {
          line: 373,
          column: 94
        }
      },
      "196": {
        start: {
          line: 374,
          column: 4
        },
        end: {
          line: 374,
          column: 53
        }
      },
      "197": {
        start: {
          line: 375,
          column: 4
        },
        end: {
          line: 375,
          column: 120
        }
      },
      "198": {
        start: {
          line: 376,
          column: 4
        },
        end: {
          line: 376,
          column: 115
        }
      },
      "199": {
        start: {
          line: 377,
          column: 4
        },
        end: {
          line: 377,
          column: 102
        }
      },
      "200": {
        start: {
          line: 378,
          column: 4
        },
        end: {
          line: 378,
          column: 104
        }
      },
      "201": {
        start: {
          line: 379,
          column: 4
        },
        end: {
          line: 379,
          column: 106
        }
      },
      "202": {
        start: {
          line: 380,
          column: 4
        },
        end: {
          line: 380,
          column: 68
        }
      },
      "203": {
        start: {
          line: 381,
          column: 4
        },
        end: {
          line: 381,
          column: 94
        }
      },
      "204": {
        start: {
          line: 382,
          column: 4
        },
        end: {
          line: 382,
          column: 16
        }
      },
      "205": {
        start: {
          line: 384,
          column: 4
        },
        end: {
          line: 384,
          column: 74
        }
      },
      "206": {
        start: {
          line: 385,
          column: 4
        },
        end: {
          line: 385,
          column: 17
        }
      },
      "207": {
        start: {
          line: 390,
          column: 0
        },
        end: {
          line: 393,
          column: 1
        }
      },
      "208": {
        start: {
          line: 391,
          column: 18
        },
        end: {
          line: 391,
          column: 31
        }
      },
      "209": {
        start: {
          line: 392,
          column: 2
        },
        end: {
          line: 392,
          column: 32
        }
      },
      "210": {
        start: {
          line: 395,
          column: 0
        },
        end: {
          line: 402,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "testFittingCoefficientsData",
        decl: {
          start: {
            line: 10,
            column: 9
          },
          end: {
            line: 10,
            column: 36
          }
        },
        loc: {
          start: {
            line: 10,
            column: 39
          },
          end: {
            line: 45,
            column: 1
          }
        },
        line: 10
      },
      "1": {
        name: "testVelocityPressureCalculation",
        decl: {
          start: {
            line: 48,
            column: 9
          },
          end: {
            line: 48,
            column: 40
          }
        },
        loc: {
          start: {
            line: 48,
            column: 43
          },
          end: {
            line: 73,
            column: 1
          }
        },
        line: 48
      },
      "2": {
        name: "testFittingLossCalculation",
        decl: {
          start: {
            line: 76,
            column: 9
          },
          end: {
            line: 76,
            column: 35
          }
        },
        loc: {
          start: {
            line: 76,
            column: 38
          },
          end: {
            line: 114,
            column: 1
          }
        },
        line: 76
      },
      "3": {
        name: "testSystemCalculation",
        decl: {
          start: {
            line: 117,
            column: 9
          },
          end: {
            line: 117,
            column: 30
          }
        },
        loc: {
          start: {
            line: 117,
            column: 33
          },
          end: {
            line: 200,
            column: 1
          }
        },
        line: 117
      },
      "4": {
        name: "testSMACNACompliance",
        decl: {
          start: {
            line: 203,
            column: 9
          },
          end: {
            line: 203,
            column: 29
          }
        },
        loc: {
          start: {
            line: 203,
            column: 32
          },
          end: {
            line: 257,
            column: 1
          }
        },
        line: 203
      },
      "5": {
        name: "testEnhancedAirProperties",
        decl: {
          start: {
            line: 260,
            column: 9
          },
          end: {
            line: 260,
            column: 34
          }
        },
        loc: {
          start: {
            line: 260,
            column: 37
          },
          end: {
            line: 340,
            column: 1
          }
        },
        line: 260
      },
      "6": {
        name: "runAllTests",
        decl: {
          start: {
            line: 343,
            column: 9
          },
          end: {
            line: 343,
            column: 20
          }
        },
        loc: {
          start: {
            line: 343,
            column: 23
          },
          end: {
            line: 387,
            column: 1
          }
        },
        line: 343
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 24,
            column: 22
          },
          end: {
            line: 24,
            column: 90
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 24,
            column: 22
          },
          end: {
            line: 24,
            column: 42
          }
        }, {
          start: {
            line: 24,
            column: 46
          },
          end: {
            line: 24,
            column: 90
          }
        }],
        line: 24
      },
      "1": {
        loc: {
          start: {
            line: 25,
            column: 20
          },
          end: {
            line: 25,
            column: 84
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 25,
            column: 20
          },
          end: {
            line: 25,
            column: 38
          }
        }, {
          start: {
            line: 25,
            column: 42
          },
          end: {
            line: 25,
            column: 84
          }
        }],
        line: 25
      },
      "2": {
        loc: {
          start: {
            line: 26,
            column: 27
          },
          end: {
            line: 26,
            column: 105
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 27
          },
          end: {
            line: 26,
            column: 52
          }
        }, {
          start: {
            line: 26,
            column: 56
          },
          end: {
            line: 26,
            column: 105
          }
        }],
        line: 26
      },
      "3": {
        loc: {
          start: {
            line: 28,
            column: 35
          },
          end: {
            line: 28,
            column: 68
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 28,
            column: 47
          },
          end: {
            line: 28,
            column: 56
          }
        }, {
          start: {
            line: 28,
            column: 59
          },
          end: {
            line: 28,
            column: 68
          }
        }],
        line: 28
      },
      "4": {
        loc: {
          start: {
            line: 29,
            column: 33
          },
          end: {
            line: 29,
            column: 64
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 29,
            column: 43
          },
          end: {
            line: 29,
            column: 52
          }
        }, {
          start: {
            line: 29,
            column: 55
          },
          end: {
            line: 29,
            column: 64
          }
        }],
        line: 29
      },
      "5": {
        loc: {
          start: {
            line: 30,
            column: 34
          },
          end: {
            line: 30,
            column: 72
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 30,
            column: 51
          },
          end: {
            line: 30,
            column: 60
          }
        }, {
          start: {
            line: 30,
            column: 63
          },
          end: {
            line: 30,
            column: 72
          }
        }],
        line: 30
      },
      "6": {
        loc: {
          start: {
            line: 34,
            column: 4
          },
          end: {
            line: 38,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 34,
            column: 4
          },
          end: {
            line: 38,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 34
      },
      "7": {
        loc: {
          start: {
            line: 34,
            column: 8
          },
          end: {
            line: 34,
            column: 60
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 34,
            column: 8
          },
          end: {
            line: 34,
            column: 19
          }
        }, {
          start: {
            line: 34,
            column: 23
          },
          end: {
            line: 34,
            column: 60
          }
        }],
        line: 34
      },
      "8": {
        loc: {
          start: {
            line: 36,
            column: 22
          },
          end: {
            line: 36,
            column: 63
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 36,
            column: 36
          },
          end: {
            line: 36,
            column: 49
          }
        }, {
          start: {
            line: 36,
            column: 52
          },
          end: {
            line: 36,
            column: 63
          }
        }],
        line: 36
      },
      "9": {
        loc: {
          start: {
            line: 103,
            column: 4
          },
          end: {
            line: 107,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 103,
            column: 4
          },
          end: {
            line: 107,
            column: 5
          }
        }, {
          start: {
            line: 105,
            column: 11
          },
          end: {
            line: 107,
            column: 5
          }
        }],
        line: 103
      },
      "10": {
        loc: {
          start: {
            line: 103,
            column: 8
          },
          end: {
            line: 103,
            column: 46
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 103,
            column: 8
          },
          end: {
            line: 103,
            column: 24
          }
        }, {
          start: {
            line: 103,
            column: 28
          },
          end: {
            line: 103,
            column: 46
          }
        }],
        line: 103
      },
      "11": {
        loc: {
          start: {
            line: 182,
            column: 4
          },
          end: {
            line: 186,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 182,
            column: 4
          },
          end: {
            line: 186,
            column: 5
          }
        }, {
          start: {
            line: 184,
            column: 11
          },
          end: {
            line: 186,
            column: 5
          }
        }],
        line: 182
      },
      "12": {
        loc: {
          start: {
            line: 182,
            column: 8
          },
          end: {
            line: 182,
            column: 56
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 182,
            column: 8
          },
          end: {
            line: 182,
            column: 29
          }
        }, {
          start: {
            line: 182,
            column: 33
          },
          end: {
            line: 182,
            column: 56
          }
        }],
        line: 182
      },
      "13": {
        loc: {
          start: {
            line: 232,
            column: 30
          },
          end: {
            line: 232,
            column: 80
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 232,
            column: 30
          },
          end: {
            line: 232,
            column: 53
          }
        }, {
          start: {
            line: 232,
            column: 57
          },
          end: {
            line: 232,
            column: 80
          }
        }],
        line: 232
      },
      "14": {
        loc: {
          start: {
            line: 236,
            column: 41
          },
          end: {
            line: 236,
            column: 73
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 236,
            column: 61
          },
          end: {
            line: 236,
            column: 66
          }
        }, {
          start: {
            line: 236,
            column: 69
          },
          end: {
            line: 236,
            column: 73
          }
        }],
        line: 236
      },
      "15": {
        loc: {
          start: {
            line: 237,
            column: 39
          },
          end: {
            line: 237,
            column: 69
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 237,
            column: 57
          },
          end: {
            line: 237,
            column: 62
          }
        }, {
          start: {
            line: 237,
            column: 65
          },
          end: {
            line: 237,
            column: 69
          }
        }],
        line: 237
      },
      "16": {
        loc: {
          start: {
            line: 245,
            column: 41
          },
          end: {
            line: 245,
            column: 73
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 245,
            column: 61
          },
          end: {
            line: 245,
            column: 66
          }
        }, {
          start: {
            line: 245,
            column: 69
          },
          end: {
            line: 245,
            column: 73
          }
        }],
        line: 245
      },
      "17": {
        loc: {
          start: {
            line: 246,
            column: 39
          },
          end: {
            line: 246,
            column: 69
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 246,
            column: 57
          },
          end: {
            line: 246,
            column: 62
          }
        }, {
          start: {
            line: 246,
            column: 65
          },
          end: {
            line: 246,
            column: 69
          }
        }],
        line: 246
      },
      "18": {
        loc: {
          start: {
            line: 249,
            column: 29
          },
          end: {
            line: 249,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 249,
            column: 29
          },
          end: {
            line: 249,
            column: 46
          }
        }, {
          start: {
            line: 249,
            column: 50
          },
          end: {
            line: 249,
            column: 67
          }
        }],
        line: 249
      },
      "19": {
        loc: {
          start: {
            line: 250,
            column: 39
          },
          end: {
            line: 250,
            column: 70
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 250,
            column: 58
          },
          end: {
            line: 250,
            column: 63
          }
        }, {
          start: {
            line: 250,
            column: 66
          },
          end: {
            line: 250,
            column: 70
          }
        }],
        line: 250
      },
      "20": {
        loc: {
          start: {
            line: 270,
            column: 4
          },
          end: {
            line: 273,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 270,
            column: 4
          },
          end: {
            line: 273,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 270
      },
      "21": {
        loc: {
          start: {
            line: 283,
            column: 4
          },
          end: {
            line: 287,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 283,
            column: 4
          },
          end: {
            line: 287,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 283
      },
      "22": {
        loc: {
          start: {
            line: 291,
            column: 4
          },
          end: {
            line: 301,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 291,
            column: 4
          },
          end: {
            line: 301,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 291
      },
      "23": {
        loc: {
          start: {
            line: 298,
            column: 6
          },
          end: {
            line: 300,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 298,
            column: 6
          },
          end: {
            line: 300,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 298
      },
      "24": {
        loc: {
          start: {
            line: 298,
            column: 10
          },
          end: {
            line: 298,
            column: 46
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 298,
            column: 10
          },
          end: {
            line: 298,
            column: 19
          }
        }, {
          start: {
            line: 298,
            column: 23
          },
          end: {
            line: 298,
            column: 46
          }
        }],
        line: 298
      },
      "25": {
        loc: {
          start: {
            line: 361,
            column: 6
          },
          end: {
            line: 363,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 361,
            column: 6
          },
          end: {
            line: 363,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 361
      },
      "26": {
        loc: {
          start: {
            line: 372,
            column: 2
          },
          end: {
            line: 386,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 372,
            column: 2
          },
          end: {
            line: 386,
            column: 3
          }
        }, {
          start: {
            line: 383,
            column: 9
          },
          end: {
            line: 386,
            column: 3
          }
        }],
        line: 372
      },
      "27": {
        loc: {
          start: {
            line: 390,
            column: 0
          },
          end: {
            line: 393,
            column: 1
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 390,
            column: 0
          },
          end: {
            line: 393,
            column: 1
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 390
      },
      "28": {
        loc: {
          start: {
            line: 392,
            column: 15
          },
          end: {
            line: 392,
            column: 30
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 392,
            column: 25
          },
          end: {
            line: 392,
            column: 26
          }
        }, {
          start: {
            line: 392,
            column: 29
          },
          end: {
            line: 392,
            column: 30
          }
        }],
        line: 392
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0,
      "210": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "f4a0bd1c5794a70dad9bb2af0854e87a61a16f0a"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1lxbffvxp9 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1lxbffvxp9();
/**
 * Simple JavaScript test for duct physics integration
 * Tests the core functionality without TypeScript compilation issues
 */

const fs =
/* istanbul ignore next */
(cov_1lxbffvxp9().s[0]++, require('fs'));
const path =
/* istanbul ignore next */
(cov_1lxbffvxp9().s[1]++, require('path'));

// Test 1: Verify fitting coefficients data file exists and is valid
function testFittingCoefficientsData() {
  /* istanbul ignore next */
  cov_1lxbffvxp9().f[0]++;
  cov_1lxbffvxp9().s[2]++;
  console.log('\n=== TEST 1: Fitting Coefficients Data ===');
  /* istanbul ignore next */
  cov_1lxbffvxp9().s[3]++;
  try {
    const dataPath =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[4]++, path.join(__dirname, '../../data/fitting_coefficients.json'));
    const rawData =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[5]++, fs.readFileSync(dataPath, 'utf8'));
    const data =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[6]++, JSON.parse(rawData));
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[7]++;
    console.log('✓ Fitting coefficients file loaded successfully');
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[8]++;
    console.log(`✓ Version: ${data.metadata.version}`);
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[9]++;
    console.log(`✓ Standard: ${data.metadata.standard}`);

    // Check for key fitting types
    const roundFittings =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[10]++, data.round_fittings);
    const hasElbows =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[11]++,
    /* istanbul ignore next */
    (cov_1lxbffvxp9().b[0][0]++, roundFittings.elbows) &&
    /* istanbul ignore next */
    (cov_1lxbffvxp9().b[0][1]++, Object.keys(roundFittings.elbows).length > 0));
    const hasTees =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[12]++,
    /* istanbul ignore next */
    (cov_1lxbffvxp9().b[1][0]++, roundFittings.tees) &&
    /* istanbul ignore next */
    (cov_1lxbffvxp9().b[1][1]++, Object.keys(roundFittings.tees).length > 0));
    const hasTransitions =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[13]++,
    /* istanbul ignore next */
    (cov_1lxbffvxp9().b[2][0]++, roundFittings.transitions) &&
    /* istanbul ignore next */
    (cov_1lxbffvxp9().b[2][1]++, Object.keys(roundFittings.transitions).length > 0));
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[14]++;
    console.log(`✓ Round elbows: ${hasElbows ?
    /* istanbul ignore next */
    (cov_1lxbffvxp9().b[3][0]++, 'Present') :
    /* istanbul ignore next */
    (cov_1lxbffvxp9().b[3][1]++, 'Missing')}`);
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[15]++;
    console.log(`✓ Round tees: ${hasTees ?
    /* istanbul ignore next */
    (cov_1lxbffvxp9().b[4][0]++, 'Present') :
    /* istanbul ignore next */
    (cov_1lxbffvxp9().b[4][1]++, 'Missing')}`);
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[16]++;
    console.log(`✓ Transitions: ${hasTransitions ?
    /* istanbul ignore next */
    (cov_1lxbffvxp9().b[5][0]++, 'Present') :
    /* istanbul ignore next */
    (cov_1lxbffvxp9().b[5][1]++, 'Missing')}`);

    // Test specific K-factor lookup
    const smoothElbow =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[17]++, roundFittings.elbows['90deg_round_smooth']);
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[18]++;
    if (
    /* istanbul ignore next */
    (cov_1lxbffvxp9().b[7][0]++, smoothElbow) &&
    /* istanbul ignore next */
    (cov_1lxbffvxp9().b[7][1]++, smoothElbow.radius_to_diameter_ratios)) {
      /* istanbul ignore next */
      cov_1lxbffvxp9().b[6][0]++;
      const kFactorData =
      /* istanbul ignore next */
      (cov_1lxbffvxp9().s[19]++, smoothElbow.radius_to_diameter_ratios['1.5']);
      const kFactor =
      /* istanbul ignore next */
      (cov_1lxbffvxp9().s[20]++, kFactorData ?
      /* istanbul ignore next */
      (cov_1lxbffvxp9().b[8][0]++, kFactorData.K) :
      /* istanbul ignore next */
      (cov_1lxbffvxp9().b[8][1]++, 'Not found'));
      /* istanbul ignore next */
      cov_1lxbffvxp9().s[21]++;
      console.log(`✓ 90° smooth elbow R/D=1.5 K-factor: ${kFactor}`);
    } else
    /* istanbul ignore next */
    {
      cov_1lxbffvxp9().b[6][1]++;
    }
    cov_1lxbffvxp9().s[22]++;
    return true;
  } catch (error) {
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[23]++;
    console.error('✗ Error loading fitting coefficients:', error.message);
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[24]++;
    return false;
  }
}

// Test 2: Basic velocity pressure calculation
function testVelocityPressureCalculation() {
  /* istanbul ignore next */
  cov_1lxbffvxp9().f[1]++;
  cov_1lxbffvxp9().s[25]++;
  console.log('\n=== TEST 2: Velocity Pressure Calculation ===');
  /* istanbul ignore next */
  cov_1lxbffvxp9().s[26]++;
  try {
    // Manual calculation: VP = (V/4005)² for standard air density
    const velocity =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[27]++, 1000); // FPM
    const standardAirDensity =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[28]++, 0.075); // lb/ft³

    const velocityPressure =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[29]++, Math.pow(velocity / 4005, 2) * (standardAirDensity / 0.075));
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[30]++;
    console.log(`✓ Velocity: ${velocity} FPM`);
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[31]++;
    console.log(`✓ Velocity Pressure: ${velocityPressure.toFixed(4)} in wg`);

    // Test with different air density
    const hotAirDensity =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[32]++, 0.060); // lb/ft³ (hot air)
    const adjustedVP =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[33]++, Math.pow(velocity / 4005, 2) * (hotAirDensity / 0.075));
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[34]++;
    console.log(`✓ Hot air VP (ρ=${hotAirDensity}): ${adjustedVP.toFixed(4)} in wg`);
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[35]++;
    console.log(`✓ Density ratio effect: ${(adjustedVP / velocityPressure).toFixed(3)}`);
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[36]++;
    return true;
  } catch (error) {
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[37]++;
    console.error('✗ Error in velocity pressure calculation:', error.message);
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[38]++;
    return false;
  }
}

// Test 3: Basic fitting loss calculation
function testFittingLossCalculation() {
  /* istanbul ignore next */
  cov_1lxbffvxp9().f[2]++;
  cov_1lxbffvxp9().s[39]++;
  console.log('\n=== TEST 3: Fitting Loss Calculation ===');
  /* istanbul ignore next */
  cov_1lxbffvxp9().s[40]++;
  try {
    // Load fitting coefficients
    const dataPath =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[41]++, path.join(__dirname, '../../data/fitting_coefficients.json'));
    const data =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[42]++, JSON.parse(fs.readFileSync(dataPath, 'utf8')));

    // Test case: 90° smooth elbow with R/D = 1.5
    const kFactorData =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[43]++, data.round_fittings.elbows['90deg_round_smooth'].radius_to_diameter_ratios['1.5']);
    const kFactor =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[44]++, kFactorData.K);
    const velocity =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[45]++, 1833); // FPM (1000 CFM in 10" duct)
    const airDensity =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[46]++, 0.075); // lb/ft³

    // Calculate velocity pressure
    const velocityPressure =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[47]++, Math.pow(velocity / 4005, 2) * (airDensity / 0.075));

    // Calculate pressure loss
    const pressureLoss =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[48]++, kFactor * velocityPressure);
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[49]++;
    console.log(`✓ Fitting: 90° smooth elbow (R/D = 1.5)`);
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[50]++;
    console.log(`✓ K-factor: ${kFactor}`);
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[51]++;
    console.log(`✓ Velocity: ${velocity} FPM`);
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[52]++;
    console.log(`✓ Velocity Pressure: ${velocityPressure.toFixed(4)} in wg`);
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[53]++;
    console.log(`✓ Pressure Loss: ${pressureLoss.toFixed(4)} in wg`);

    // Validate reasonable result
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[54]++;
    if (
    /* istanbul ignore next */
    (cov_1lxbffvxp9().b[10][0]++, pressureLoss > 0) &&
    /* istanbul ignore next */
    (cov_1lxbffvxp9().b[10][1]++, pressureLoss < 1.0)) {
      /* istanbul ignore next */
      cov_1lxbffvxp9().b[9][0]++;
      cov_1lxbffvxp9().s[55]++;
      console.log('✓ Pressure loss is within reasonable range');
    } else {
      /* istanbul ignore next */
      cov_1lxbffvxp9().b[9][1]++;
      cov_1lxbffvxp9().s[56]++;
      console.log('⚠ Pressure loss may be outside expected range');
    }
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[57]++;
    return true;
  } catch (error) {
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[58]++;
    console.error('✗ Error in fitting loss calculation:', error.message);
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[59]++;
    return false;
  }
}

// Test 4: System calculation example (user's scenario)
function testSystemCalculation() {
  /* istanbul ignore next */
  cov_1lxbffvxp9().f[3]++;
  cov_1lxbffvxp9().s[60]++;
  console.log('\n=== TEST 4: System Calculation (10″ duct → 10′ run → 90° elbow → 10′ run) ===');
  /* istanbul ignore next */
  cov_1lxbffvxp9().s[61]++;
  try {
    // Load fitting coefficients
    const dataPath =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[62]++, path.join(__dirname, '../../data/fitting_coefficients.json'));
    const data =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[63]++, JSON.parse(fs.readFileSync(dataPath, 'utf8')));

    // System parameters
    const airflow =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[64]++, 1000); // CFM
    const diameter =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[65]++, 10); // inches
    const straightLength1 =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[66]++, 10); // feet
    const straightLength2 =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[67]++, 10); // feet

    // Calculate duct area and velocity
    const area =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[68]++, Math.PI * Math.pow(diameter / 12, 2) / 4); // sq ft
    const velocity =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[69]++, airflow / area); // FPM
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[70]++;
    console.log(`✓ Duct: ${diameter}" round`);
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[71]++;
    console.log(`✓ Airflow: ${airflow} CFM`);
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[72]++;
    console.log(`✓ Area: ${area.toFixed(3)} sq ft`);
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[73]++;
    console.log(`✓ Velocity: ${velocity.toFixed(0)} FPM`);

    // Calculate friction loss (simplified Darcy-Weisbach)
    // Using typical values for galvanized steel
    const roughness =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[74]++, 0.0005); // feet
    const diameterFt =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[75]++, diameter / 12);
    const velocityFps =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[76]++, velocity / 60);
    const kinematicViscosity =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[77]++, 1.57e-4); // ft²/s
    const airDensity =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[78]++, 0.075); // lb/ft³

    const reynolds =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[79]++, velocityFps * diameterFt / kinematicViscosity);
    const relativeRoughness =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[80]++, roughness / diameterFt);

    // Simplified friction factor (Colebrook-White approximation)
    const frictionFactor =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[81]++, 0.25 / Math.pow(Math.log10(relativeRoughness / 3.7 + 5.74 / Math.pow(reynolds, 0.9)), 2));

    // Friction loss per 100 feet
    const frictionLossPer100ft =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[82]++, frictionFactor * (100 / diameterFt) * (airDensity * Math.pow(velocityFps, 2)) / (2 * 32.174 * 5.2));

    // Total friction loss for 20 feet
    const totalFrictionLoss =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[83]++, frictionLossPer100ft * (straightLength1 + straightLength2) / 100);
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[84]++;
    console.log(`✓ Reynolds Number: ${reynolds.toFixed(0)}`);
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[85]++;
    console.log(`✓ Friction Factor: ${frictionFactor.toFixed(4)}`);
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[86]++;
    console.log(`✓ Friction Loss: ${totalFrictionLoss.toFixed(4)} in wg`);

    // Calculate fitting loss for 90° elbow
    const elbowKFactorData =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[87]++, data.round_fittings.elbows['90deg_round_smooth'].radius_to_diameter_ratios['1.5']);
    const elbowKFactor =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[88]++, elbowKFactorData.K);
    const velocityPressure =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[89]++, Math.pow(velocity / 4005, 2));
    const fittingLoss =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[90]++, elbowKFactor * velocityPressure);
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[91]++;
    console.log(`✓ Elbow K-factor: ${elbowKFactor}`);
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[92]++;
    console.log(`✓ Velocity Pressure: ${velocityPressure.toFixed(4)} in wg`);
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[93]++;
    console.log(`✓ Fitting Loss: ${fittingLoss.toFixed(4)} in wg`);

    // Total system pressure loss
    const totalPressureLoss =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[94]++, totalFrictionLoss + fittingLoss);
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[95]++;
    console.log(`✓ Total Friction Loss: ${totalFrictionLoss.toFixed(4)} in wg`);
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[96]++;
    console.log(`✓ Total Minor Loss: ${fittingLoss.toFixed(4)} in wg`);
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[97]++;
    console.log(`✓ TOTAL SYSTEM PRESSURE LOSS: ${totalPressureLoss.toFixed(4)} in wg`);

    // Validate results
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[98]++;
    if (
    /* istanbul ignore next */
    (cov_1lxbffvxp9().b[12][0]++, totalPressureLoss > 0) &&
    /* istanbul ignore next */
    (cov_1lxbffvxp9().b[12][1]++, totalPressureLoss < 1.0)) {
      /* istanbul ignore next */
      cov_1lxbffvxp9().b[11][0]++;
      cov_1lxbffvxp9().s[99]++;
      console.log('✓ Total pressure loss is within reasonable range for this system');
    } else {
      /* istanbul ignore next */
      cov_1lxbffvxp9().b[11][1]++;
      cov_1lxbffvxp9().s[100]++;
      console.log('⚠ Total pressure loss may be outside expected range');
    }

    // Calculate percentage breakdown
    const frictionPercent =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[101]++, (totalFrictionLoss / totalPressureLoss * 100).toFixed(1));
    const fittingPercent =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[102]++, (fittingLoss / totalPressureLoss * 100).toFixed(1));
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[103]++;
    console.log(`✓ Friction losses: ${frictionPercent}% of total`);
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[104]++;
    console.log(`✓ Fitting losses: ${fittingPercent}% of total`);
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[105]++;
    return true;
  } catch (error) {
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[106]++;
    console.error('✗ Error in system calculation:', error.message);
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[107]++;
    return false;
  }
}

// Test 5: SMACNA compliance check
function testSMACNACompliance() {
  /* istanbul ignore next */
  cov_1lxbffvxp9().f[4]++;
  cov_1lxbffvxp9().s[108]++;
  console.log('\n=== TEST 5: SMACNA Compliance Check ===');
  /* istanbul ignore next */
  cov_1lxbffvxp9().s[109]++;
  try {
    // SMACNA velocity limits (FPM)
    const velocityLimits =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[110]++, {
      supply: {
        min: 400,
        max: 2500,
        recommended: 1500
      },
      return: {
        min: 300,
        max: 2000,
        recommended: 1200
      },
      exhaust: {
        min: 500,
        max: 3000,
        recommended: 1800
      }
    });

    // SMACNA pressure limits (inches w.g.)
    const pressureLimits =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[111]++, {
      supply: {
        max: 6.0,
        recommended: 4.0
      },
      return: {
        max: 4.0,
        recommended: 2.5
      },
      exhaust: {
        max: 8.0,
        recommended: 5.0
      }
    });

    // Test case from previous calculation
    const velocity =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[112]++, 1833); // FPM
    const pressureLoss =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[113]++, 0.05); // in wg (example)
    const systemType =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[114]++, 'supply');
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[115]++;
    console.log(`✓ System Type: ${systemType}`);
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[116]++;
    console.log(`✓ Velocity: ${velocity} FPM`);
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[117]++;
    console.log(`✓ Pressure Loss: ${pressureLoss} in wg`);

    // Check velocity compliance
    const vLimits =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[118]++, velocityLimits[systemType]);
    const velocityCompliant =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[119]++,
    /* istanbul ignore next */
    (cov_1lxbffvxp9().b[13][0]++, velocity >= vLimits.min) &&
    /* istanbul ignore next */
    (cov_1lxbffvxp9().b[13][1]++, velocity <= vLimits.max));
    const velocityOptimal =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[120]++, velocity <= vLimits.recommended);
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[121]++;
    console.log(`✓ Velocity Range: ${vLimits.min}-${vLimits.max} FPM (recommended: ≤${vLimits.recommended})`);
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[122]++;
    console.log(`✓ Velocity Compliant: ${velocityCompliant ?
    /* istanbul ignore next */
    (cov_1lxbffvxp9().b[14][0]++, 'YES') :
    /* istanbul ignore next */
    (cov_1lxbffvxp9().b[14][1]++, 'NO')}`);
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[123]++;
    console.log(`✓ Velocity Optimal: ${velocityOptimal ?
    /* istanbul ignore next */
    (cov_1lxbffvxp9().b[15][0]++, 'YES') :
    /* istanbul ignore next */
    (cov_1lxbffvxp9().b[15][1]++, 'NO')}`);

    // Check pressure compliance
    const pLimits =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[124]++, pressureLimits[systemType]);
    const pressureCompliant =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[125]++, pressureLoss <= pLimits.max);
    const pressureOptimal =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[126]++, pressureLoss <= pLimits.recommended);
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[127]++;
    console.log(`✓ Pressure Limit: ≤${pLimits.max} in wg (recommended: ≤${pLimits.recommended})`);
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[128]++;
    console.log(`✓ Pressure Compliant: ${pressureCompliant ?
    /* istanbul ignore next */
    (cov_1lxbffvxp9().b[16][0]++, 'YES') :
    /* istanbul ignore next */
    (cov_1lxbffvxp9().b[16][1]++, 'NO')}`);
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[129]++;
    console.log(`✓ Pressure Optimal: ${pressureOptimal ?
    /* istanbul ignore next */
    (cov_1lxbffvxp9().b[17][0]++, 'YES') :
    /* istanbul ignore next */
    (cov_1lxbffvxp9().b[17][1]++, 'NO')}`);

    // Overall compliance
    const overallCompliant =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[130]++,
    /* istanbul ignore next */
    (cov_1lxbffvxp9().b[18][0]++, velocityCompliant) &&
    /* istanbul ignore next */
    (cov_1lxbffvxp9().b[18][1]++, pressureCompliant));
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[131]++;
    console.log(`✓ SMACNA Compliant: ${overallCompliant ?
    /* istanbul ignore next */
    (cov_1lxbffvxp9().b[19][0]++, 'YES') :
    /* istanbul ignore next */
    (cov_1lxbffvxp9().b[19][1]++, 'NO')}`);
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[132]++;
    return true;
  } catch (error) {
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[133]++;
    console.error('✗ Error in SMACNA compliance check:', error.message);
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[134]++;
    return false;
  }
}

// Test 6: Enhanced Air Properties and Environmental Corrections
function testEnhancedAirProperties() {
  /* istanbul ignore next */
  cov_1lxbffvxp9().f[5]++;
  cov_1lxbffvxp9().s[135]++;
  console.log('\n=== TEST 6: Enhanced Air Properties ===');
  /* istanbul ignore next */
  cov_1lxbffvxp9().s[136]++;
  try {
    // Test air properties data loading
    const fs =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[137]++, require('fs'));
    const path =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[138]++, require('path'));

    // Check if air properties data file exists
    const airPropsPath =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[139]++, path.join(__dirname, '../../data/air_properties.json'));
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[140]++;
    if (!fs.existsSync(airPropsPath)) {
      /* istanbul ignore next */
      cov_1lxbffvxp9().b[20][0]++;
      cov_1lxbffvxp9().s[141]++;
      console.log('⚠ Air properties data file not found, skipping enhanced tests');
      /* istanbul ignore next */
      cov_1lxbffvxp9().s[142]++;
      return true;
    } else
    /* istanbul ignore next */
    {
      cov_1lxbffvxp9().b[20][1]++;
    }
    const airPropsData =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[143]++, JSON.parse(fs.readFileSync(airPropsPath, 'utf8')));
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[144]++;
    console.log('✓ Air properties data loaded successfully');
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[145]++;
    console.log(`✓ Temperature range: ${Object.keys(airPropsData.temperature_properties).length} data points`);
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[146]++;
    console.log(`✓ Altitude corrections: ${Object.keys(airPropsData.altitude_corrections).length} data points`);
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[147]++;
    console.log(`✓ Humidity effects: ${Object.keys(airPropsData.humidity_effects).length} data points`);

    // Test velocity pressure data
    const vpPath =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[148]++, path.join(__dirname, '../../data/velocity_pressure.json'));
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[149]++;
    if (fs.existsSync(vpPath)) {
      /* istanbul ignore next */
      cov_1lxbffvxp9().b[21][0]++;
      const vpData =
      /* istanbul ignore next */
      (cov_1lxbffvxp9().s[150]++, JSON.parse(fs.readFileSync(vpPath, 'utf8')));
      /* istanbul ignore next */
      cov_1lxbffvxp9().s[151]++;
      console.log('✓ Velocity pressure lookup table loaded');
      /* istanbul ignore next */
      cov_1lxbffvxp9().s[152]++;
      console.log(`✓ Velocity range: ${Object.keys(vpData.velocity_pressure_table).length} data points`);
    } else
    /* istanbul ignore next */
    {
      cov_1lxbffvxp9().b[21][1]++;
    }

    // Test enhanced duct roughness data
    const roughnessPath =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[153]++, path.join(__dirname, '../../data/duct_roughness.json'));
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[154]++;
    if (fs.existsSync(roughnessPath)) {
      /* istanbul ignore next */
      cov_1lxbffvxp9().b[22][0]++;
      const roughnessData =
      /* istanbul ignore next */
      (cov_1lxbffvxp9().s[155]++, JSON.parse(fs.readFileSync(roughnessPath, 'utf8')));
      /* istanbul ignore next */
      cov_1lxbffvxp9().s[156]++;
      console.log('✓ Enhanced duct roughness data loaded');
      /* istanbul ignore next */
      cov_1lxbffvxp9().s[157]++;
      console.log(`✓ Materials available: ${Object.keys(roughnessData.materials).length}`);

      // Test aging factors
      const galvSteel =
      /* istanbul ignore next */
      (cov_1lxbffvxp9().s[158]++, roughnessData.materials.galvanized_steel);
      /* istanbul ignore next */
      cov_1lxbffvxp9().s[159]++;
      if (
      /* istanbul ignore next */
      (cov_1lxbffvxp9().b[24][0]++, galvSteel) &&
      /* istanbul ignore next */
      (cov_1lxbffvxp9().b[24][1]++, galvSteel.aging_factors)) {
        /* istanbul ignore next */
        cov_1lxbffvxp9().b[23][0]++;
        cov_1lxbffvxp9().s[160]++;
        console.log(`✓ Aging factors for galvanized steel: ${Object.keys(galvSteel.aging_factors).length} age ranges`);
      } else
      /* istanbul ignore next */
      {
        cov_1lxbffvxp9().b[23][1]++;
      }
    } else
    /* istanbul ignore next */
    {
      cov_1lxbffvxp9().b[22][1]++;
    }

    // Test environmental condition calculations
    cov_1lxbffvxp9().s[161]++;
    console.log('\n--- Environmental Condition Tests ---');

    // Standard conditions
    const standardTemp =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[162]++, 70); // °F
    const standardDensity =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[163]++, 0.075); // lb/ft³

    // High temperature condition
    const highTemp =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[164]++, 150); // °F
    const tempCorrectionFactor =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[165]++, (standardTemp + 459.67) / (highTemp + 459.67));
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[166]++;
    console.log(`✓ Temperature correction (${highTemp}°F): ${tempCorrectionFactor.toFixed(3)}`);

    // High altitude condition
    const altitude =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[167]++, 5000); // feet (Denver)
    const altitudeCorrectionFactor =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[168]++, Math.pow(1 - altitude * 6.87535e-6, 5.2561));
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[169]++;
    console.log(`✓ Altitude correction (${altitude} ft): ${altitudeCorrectionFactor.toFixed(3)}`);

    // Combined environmental effect
    const combinedCorrection =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[170]++, tempCorrectionFactor * altitudeCorrectionFactor);
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[171]++;
    console.log(`✓ Combined environmental correction: ${combinedCorrection.toFixed(3)}`);

    // Test material aging effects
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[172]++;
    console.log('\n--- Material Aging Tests ---');
    const baseRoughness =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[173]++, 0.0003); // feet (new galvanized steel)
    const agingFactor =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[174]++, 1.5); // 10-year aging factor
    const agedRoughness =
    /* istanbul ignore next */
    (cov_1lxbffvxp9().s[175]++, baseRoughness * agingFactor);
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[176]++;
    console.log(`✓ Base roughness: ${baseRoughness} ft`);
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[177]++;
    console.log(`✓ Aged roughness (10 years): ${agedRoughness} ft`);
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[178]++;
    console.log(`✓ Roughness increase: ${((agingFactor - 1) * 100).toFixed(0)}%`);
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[179]++;
    return true;
  } catch (error) {
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[180]++;
    console.error('✗ Error in enhanced air properties test:', error.message);
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[181]++;
    return false;
  }
}

// Main test runner
function runAllTests() {
  /* istanbul ignore next */
  cov_1lxbffvxp9().f[6]++;
  cov_1lxbffvxp9().s[182]++;
  console.log('COMPREHENSIVE DUCT PHYSICS IMPLEMENTATION - INTEGRATION TESTS');
  /* istanbul ignore next */
  cov_1lxbffvxp9().s[183]++;
  console.log('==============================================================');
  const tests =
  /* istanbul ignore next */
  (cov_1lxbffvxp9().s[184]++, [testFittingCoefficientsData, testVelocityPressureCalculation, testFittingLossCalculation, testSystemCalculation, testSMACNACompliance, testEnhancedAirProperties]);
  let passedTests =
  /* istanbul ignore next */
  (cov_1lxbffvxp9().s[185]++, 0);
  let totalTests =
  /* istanbul ignore next */
  (cov_1lxbffvxp9().s[186]++, tests.length);
  /* istanbul ignore next */
  cov_1lxbffvxp9().s[187]++;
  for (const test of tests) {
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[188]++;
    try {
      /* istanbul ignore next */
      cov_1lxbffvxp9().s[189]++;
      if (test()) {
        /* istanbul ignore next */
        cov_1lxbffvxp9().b[25][0]++;
        cov_1lxbffvxp9().s[190]++;
        passedTests++;
      } else
      /* istanbul ignore next */
      {
        cov_1lxbffvxp9().b[25][1]++;
      }
    } catch (error) {
      /* istanbul ignore next */
      cov_1lxbffvxp9().s[191]++;
      console.error(`✗ Test failed with error: ${error.message}`);
    }
  }
  /* istanbul ignore next */
  cov_1lxbffvxp9().s[192]++;
  console.log('\n=== TEST SUMMARY ===');
  /* istanbul ignore next */
  cov_1lxbffvxp9().s[193]++;
  console.log(`Passed: ${passedTests}/${totalTests} tests`);
  /* istanbul ignore next */
  cov_1lxbffvxp9().s[194]++;
  if (passedTests === totalTests) {
    /* istanbul ignore next */
    cov_1lxbffvxp9().b[26][0]++;
    cov_1lxbffvxp9().s[195]++;
    console.log('🎉 ALL TESTS PASSED! Phase 2 enhanced implementation is working correctly.');
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[196]++;
    console.log('\nPhase 2 deliverables completed:');
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[197]++;
    console.log('✓ Air Properties Database (air_properties.json) - comprehensive temperature, pressure, humidity data');
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[198]++;
    console.log('✓ Enhanced Duct Roughness Database (duct_roughness.json) - aging factors and surface conditions');
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[199]++;
    console.log('✓ Velocity Pressure Tables (velocity_pressure.json) - pre-calculated lookup tables');
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[200]++;
    console.log('✓ Advanced Calculation Options (AirPropertiesCalculator) - environmental corrections');
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[201]++;
    console.log('✓ Enhanced System Integration (SystemPressureCalculator) - elevation and aging effects');
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[202]++;
    console.log('✓ Comprehensive testing and validation framework');
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[203]++;
    console.log('\nReady for production deployment with enhanced duct physics capabilities!');
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[204]++;
    return true;
  } else {
    /* istanbul ignore next */
    cov_1lxbffvxp9().b[26][1]++;
    cov_1lxbffvxp9().s[205]++;
    console.log('❌ Some tests failed. Please review the implementation.');
    /* istanbul ignore next */
    cov_1lxbffvxp9().s[206]++;
    return false;
  }
}

// Run tests if this file is executed directly
/* istanbul ignore next */
cov_1lxbffvxp9().s[207]++;
if (require.main === module) {
  /* istanbul ignore next */
  cov_1lxbffvxp9().b[27][0]++;
  const success =
  /* istanbul ignore next */
  (cov_1lxbffvxp9().s[208]++, runAllTests());
  /* istanbul ignore next */
  cov_1lxbffvxp9().s[209]++;
  process.exit(success ?
  /* istanbul ignore next */
  (cov_1lxbffvxp9().b[28][0]++, 0) :
  /* istanbul ignore next */
  (cov_1lxbffvxp9().b[28][1]++, 1));
} else
/* istanbul ignore next */
{
  cov_1lxbffvxp9().b[27][1]++;
}
cov_1lxbffvxp9().s[210]++;
module.exports = {
  runAllTests,
  testFittingCoefficientsData,
  testVelocityPressureCalculation,
  testFittingLossCalculation,
  testSystemCalculation,
  testSMACNACompliance
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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