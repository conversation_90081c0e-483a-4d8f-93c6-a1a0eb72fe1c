41e33317957a01d8a38aeab59a085c9b
"use strict";

/**
 * SMACNAValidator - Pure Validation Functions for SMACNA Standards
 *
 * MISSION-CRITICAL: Pure TypeScript functions for SMACNA, ASHRAE, and NFPA compliance validation
 * Extracted from UI components for reusability and tier enforcement integration
 *
 * @see docs/implementation/tier-system/tier-boundaries-specification.md
 * @see docs/developer-guide/tier-implementation-checklist.md section 2.4
 */
/* istanbul ignore next */
function cov_24qkon3nhs() {
  var path = "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\SMACNAValidator.ts";
  var hash = "0f04be0a49d01d9c9109a1850f28d2439b72fd1c";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\SMACNAValidator.ts",
    statementMap: {
      "0": {
        start: {
          line: 11,
          column: 0
        },
        end: {
          line: 11,
          column: 62
        }
      },
      "1": {
        start: {
          line: 12,
          column: 0
        },
        end: {
          line: 12,
          column: 33
        }
      },
      "2": {
        start: {
          line: 23,
          column: 23
        },
        end: {
          line: 31,
          column: 9
        }
      },
      "3": {
        start: {
          line: 32,
          column: 8
        },
        end: {
          line: 53,
          column: 9
        }
      },
      "4": {
        start: {
          line: 34,
          column: 12
        },
        end: {
          line: 34,
          column: 48
        }
      },
      "5": {
        start: {
          line: 36,
          column: 12
        },
        end: {
          line: 36,
          column: 52
        }
      },
      "6": {
        start: {
          line: 38,
          column: 12
        },
        end: {
          line: 40,
          column: 13
        }
      },
      "7": {
        start: {
          line: 39,
          column: 16
        },
        end: {
          line: 39,
          column: 55
        }
      },
      "8": {
        start: {
          line: 42,
          column: 12
        },
        end: {
          line: 42,
          column: 44
        }
      },
      "9": {
        start: {
          line: 44,
          column: 12
        },
        end: {
          line: 44,
          column: 52
        }
      },
      "10": {
        start: {
          line: 46,
          column: 12
        },
        end: {
          line: 46,
          column: 67
        }
      },
      "11": {
        start: {
          line: 49,
          column: 12
        },
        end: {
          line: 49,
          column: 76
        }
      },
      "12": {
        start: {
          line: 50,
          column: 12
        },
        end: {
          line: 50,
          column: 35
        }
      },
      "13": {
        start: {
          line: 51,
          column: 12
        },
        end: {
          line: 51,
          column: 37
        }
      },
      "14": {
        start: {
          line: 52,
          column: 12
        },
        end: {
          line: 52,
          column: 29
        }
      },
      "15": {
        start: {
          line: 54,
          column: 8
        },
        end: {
          line: 54,
          column: 22
        }
      },
      "16": {
        start: {
          line: 60,
          column: 23
        },
        end: {
          line: 68,
          column: 9
        }
      },
      "17": {
        start: {
          line: 69,
          column: 8
        },
        end: {
          line: 101,
          column: 9
        }
      },
      "18": {
        start: {
          line: 70,
          column: 58
        },
        end: {
          line: 70,
          column: 62
        }
      },
      "19": {
        start: {
          line: 71,
          column: 30
        },
        end: {
          line: 71,
          column: 51
        }
      },
      "20": {
        start: {
          line: 73,
          column: 12
        },
        end: {
          line: 84,
          column: 13
        }
      },
      "21": {
        start: {
          line: 74,
          column: 16
        },
        end: {
          line: 77,
          column: 17
        }
      },
      "22": {
        start: {
          line: 75,
          column: 20
        },
        end: {
          line: 75,
          column: 164
        }
      },
      "23": {
        start: {
          line: 76,
          column: 20
        },
        end: {
          line: 76,
          column: 39
        }
      },
      "24": {
        start: {
          line: 80,
          column: 16
        },
        end: {
          line: 83,
          column: 17
        }
      },
      "25": {
        start: {
          line: 81,
          column: 20
        },
        end: {
          line: 81,
          column: 149
        }
      },
      "26": {
        start: {
          line: 82,
          column: 20
        },
        end: {
          line: 82,
          column: 39
        }
      },
      "27": {
        start: {
          line: 86,
          column: 12
        },
        end: {
          line: 94,
          column: 13
        }
      },
      "28": {
        start: {
          line: 87,
          column: 16
        },
        end: {
          line: 87,
          column: 96
        }
      },
      "29": {
        start: {
          line: 88,
          column: 16
        },
        end: {
          line: 88,
          column: 83
        }
      },
      "30": {
        start: {
          line: 89,
          column: 16
        },
        end: {
          line: 89,
          column: 35
        }
      },
      "31": {
        start: {
          line: 91,
          column: 17
        },
        end: {
          line: 94,
          column: 13
        }
      },
      "32": {
        start: {
          line: 92,
          column: 16
        },
        end: {
          line: 92,
          column: 87
        }
      },
      "33": {
        start: {
          line: 93,
          column: 16
        },
        end: {
          line: 93,
          column: 34
        }
      },
      "34": {
        start: {
          line: 97,
          column: 12
        },
        end: {
          line: 97,
          column: 76
        }
      },
      "35": {
        start: {
          line: 98,
          column: 12
        },
        end: {
          line: 98,
          column: 35
        }
      },
      "36": {
        start: {
          line: 99,
          column: 12
        },
        end: {
          line: 99,
          column: 37
        }
      },
      "37": {
        start: {
          line: 100,
          column: 12
        },
        end: {
          line: 100,
          column: 29
        }
      },
      "38": {
        start: {
          line: 102,
          column: 8
        },
        end: {
          line: 102,
          column: 22
        }
      },
      "39": {
        start: {
          line: 108,
          column: 23
        },
        end: {
          line: 116,
          column: 9
        }
      },
      "40": {
        start: {
          line: 117,
          column: 8
        },
        end: {
          line: 151,
          column: 9
        }
      },
      "41": {
        start: {
          line: 118,
          column: 12
        },
        end: {
          line: 121,
          column: 13
        }
      },
      "42": {
        start: {
          line: 119,
          column: 16
        },
        end: {
          line: 119,
          column: 98
        }
      },
      "43": {
        start: {
          line: 120,
          column: 16
        },
        end: {
          line: 120,
          column: 30
        }
      },
      "44": {
        start: {
          line: 122,
          column: 47
        },
        end: {
          line: 122,
          column: 51
        }
      },
      "45": {
        start: {
          line: 123,
          column: 30
        },
        end: {
          line: 123,
          column: 49
        }
      },
      "46": {
        start: {
          line: 125,
          column: 12
        },
        end: {
          line: 133,
          column: 13
        }
      },
      "47": {
        start: {
          line: 126,
          column: 16
        },
        end: {
          line: 126,
          column: 150
        }
      },
      "48": {
        start: {
          line: 127,
          column: 16
        },
        end: {
          line: 127,
          column: 41
        }
      },
      "49": {
        start: {
          line: 128,
          column: 16
        },
        end: {
          line: 128,
          column: 35
        }
      },
      "50": {
        start: {
          line: 130,
          column: 17
        },
        end: {
          line: 133,
          column: 13
        }
      },
      "51": {
        start: {
          line: 131,
          column: 16
        },
        end: {
          line: 131,
          column: 149
        }
      },
      "52": {
        start: {
          line: 132,
          column: 16
        },
        end: {
          line: 132,
          column: 35
        }
      },
      "53": {
        start: {
          line: 135,
          column: 12
        },
        end: {
          line: 138,
          column: 13
        }
      },
      "54": {
        start: {
          line: 136,
          column: 16
        },
        end: {
          line: 136,
          column: 167
        }
      },
      "55": {
        start: {
          line: 137,
          column: 16
        },
        end: {
          line: 137,
          column: 35
        }
      },
      "56": {
        start: {
          line: 140,
          column: 12
        },
        end: {
          line: 144,
          column: 13
        }
      },
      "57": {
        start: {
          line: 141,
          column: 16
        },
        end: {
          line: 141,
          column: 98
        }
      },
      "58": {
        start: {
          line: 142,
          column: 16
        },
        end: {
          line: 142,
          column: 109
        }
      },
      "59": {
        start: {
          line: 143,
          column: 16
        },
        end: {
          line: 143,
          column: 35
        }
      },
      "60": {
        start: {
          line: 147,
          column: 12
        },
        end: {
          line: 147,
          column: 74
        }
      },
      "61": {
        start: {
          line: 148,
          column: 12
        },
        end: {
          line: 148,
          column: 35
        }
      },
      "62": {
        start: {
          line: 149,
          column: 12
        },
        end: {
          line: 149,
          column: 37
        }
      },
      "63": {
        start: {
          line: 150,
          column: 12
        },
        end: {
          line: 150,
          column: 29
        }
      },
      "64": {
        start: {
          line: 152,
          column: 8
        },
        end: {
          line: 152,
          column: 22
        }
      },
      "65": {
        start: {
          line: 158,
          column: 24
        },
        end: {
          line: 158,
          column: 26
        }
      },
      "66": {
        start: {
          line: 160,
          column: 8
        },
        end: {
          line: 160,
          column: 61
        }
      },
      "67": {
        start: {
          line: 162,
          column: 8
        },
        end: {
          line: 162,
          column: 61
        }
      },
      "68": {
        start: {
          line: 164,
          column: 8
        },
        end: {
          line: 166,
          column: 9
        }
      },
      "69": {
        start: {
          line: 165,
          column: 12
        },
        end: {
          line: 165,
          column: 61
        }
      },
      "70": {
        start: {
          line: 167,
          column: 8
        },
        end: {
          line: 167,
          column: 23
        }
      },
      "71": {
        start: {
          line: 173,
          column: 53
        },
        end: {
          line: 173,
          column: 57
        }
      },
      "72": {
        start: {
          line: 174,
          column: 23
        },
        end: {
          line: 174,
          column: 107
        }
      },
      "73": {
        start: {
          line: 175,
          column: 8
        },
        end: {
          line: 191,
          column: 9
        }
      },
      "74": {
        start: {
          line: 176,
          column: 12
        },
        end: {
          line: 176,
          column: 130
        }
      },
      "75": {
        start: {
          line: 177,
          column: 12
        },
        end: {
          line: 177,
          column: 31
        }
      },
      "76": {
        start: {
          line: 179,
          column: 13
        },
        end: {
          line: 191,
          column: 9
        }
      },
      "77": {
        start: {
          line: 180,
          column: 12
        },
        end: {
          line: 180,
          column: 127
        }
      },
      "78": {
        start: {
          line: 181,
          column: 12
        },
        end: {
          line: 181,
          column: 37
        }
      },
      "79": {
        start: {
          line: 182,
          column: 12
        },
        end: {
          line: 182,
          column: 31
        }
      },
      "80": {
        start: {
          line: 184,
          column: 13
        },
        end: {
          line: 191,
          column: 9
        }
      },
      "81": {
        start: {
          line: 185,
          column: 12
        },
        end: {
          line: 185,
          column: 137
        }
      },
      "82": {
        start: {
          line: 186,
          column: 12
        },
        end: {
          line: 186,
          column: 31
        }
      },
      "83": {
        start: {
          line: 188,
          column: 13
        },
        end: {
          line: 191,
          column: 9
        }
      },
      "84": {
        start: {
          line: 189,
          column: 12
        },
        end: {
          line: 189,
          column: 124
        }
      },
      "85": {
        start: {
          line: 190,
          column: 12
        },
        end: {
          line: 190,
          column: 30
        }
      },
      "86": {
        start: {
          line: 197,
          column: 33
        },
        end: {
          line: 197,
          column: 37
        }
      },
      "87": {
        start: {
          line: 198,
          column: 26
        },
        end: {
          line: 198,
          column: 56
        }
      },
      "88": {
        start: {
          line: 199,
          column: 8
        },
        end: {
          line: 211,
          column: 9
        }
      },
      "89": {
        start: {
          line: 200,
          column: 12
        },
        end: {
          line: 200,
          column: 158
        }
      },
      "90": {
        start: {
          line: 201,
          column: 12
        },
        end: {
          line: 201,
          column: 37
        }
      },
      "91": {
        start: {
          line: 202,
          column: 12
        },
        end: {
          line: 202,
          column: 31
        }
      },
      "92": {
        start: {
          line: 204,
          column: 13
        },
        end: {
          line: 211,
          column: 9
        }
      },
      "93": {
        start: {
          line: 205,
          column: 12
        },
        end: {
          line: 205,
          column: 134
        }
      },
      "94": {
        start: {
          line: 206,
          column: 12
        },
        end: {
          line: 206,
          column: 31
        }
      },
      "95": {
        start: {
          line: 208,
          column: 13
        },
        end: {
          line: 211,
          column: 9
        }
      },
      "96": {
        start: {
          line: 209,
          column: 12
        },
        end: {
          line: 209,
          column: 127
        }
      },
      "97": {
        start: {
          line: 210,
          column: 12
        },
        end: {
          line: 210,
          column: 30
        }
      },
      "98": {
        start: {
          line: 217,
          column: 32
        },
        end: {
          line: 217,
          column: 36
        }
      },
      "99": {
        start: {
          line: 218,
          column: 8
        },
        end: {
          line: 219,
          column: 19
        }
      },
      "100": {
        start: {
          line: 219,
          column: 12
        },
        end: {
          line: 219,
          column: 19
        }
      },
      "101": {
        start: {
          line: 220,
          column: 26
        },
        end: {
          line: 220,
          column: 59
        }
      },
      "102": {
        start: {
          line: 221,
          column: 8
        },
        end: {
          line: 235,
          column: 9
        }
      },
      "103": {
        start: {
          line: 222,
          column: 12
        },
        end: {
          line: 222,
          column: 124
        }
      },
      "104": {
        start: {
          line: 223,
          column: 12
        },
        end: {
          line: 223,
          column: 37
        }
      },
      "105": {
        start: {
          line: 224,
          column: 12
        },
        end: {
          line: 224,
          column: 31
        }
      },
      "106": {
        start: {
          line: 225,
          column: 12
        },
        end: {
          line: 225,
          column: 94
        }
      },
      "107": {
        start: {
          line: 227,
          column: 13
        },
        end: {
          line: 235,
          column: 9
        }
      },
      "108": {
        start: {
          line: 228,
          column: 12
        },
        end: {
          line: 228,
          column: 132
        }
      },
      "109": {
        start: {
          line: 229,
          column: 12
        },
        end: {
          line: 229,
          column: 31
        }
      },
      "110": {
        start: {
          line: 230,
          column: 12
        },
        end: {
          line: 230,
          column: 89
        }
      },
      "111": {
        start: {
          line: 232,
          column: 13
        },
        end: {
          line: 235,
          column: 9
        }
      },
      "112": {
        start: {
          line: 233,
          column: 12
        },
        end: {
          line: 233,
          column: 138
        }
      },
      "113": {
        start: {
          line: 234,
          column: 12
        },
        end: {
          line: 234,
          column: 30
        }
      },
      "114": {
        start: {
          line: 241,
          column: 25
        },
        end: {
          line: 241,
          column: 29
        }
      },
      "115": {
        start: {
          line: 242,
          column: 8
        },
        end: {
          line: 245,
          column: 9
        }
      },
      "116": {
        start: {
          line: 243,
          column: 12
        },
        end: {
          line: 243,
          column: 124
        }
      },
      "117": {
        start: {
          line: 244,
          column: 12
        },
        end: {
          line: 244,
          column: 31
        }
      },
      "118": {
        start: {
          line: 247,
          column: 8
        },
        end: {
          line: 250,
          column: 9
        }
      },
      "119": {
        start: {
          line: 248,
          column: 12
        },
        end: {
          line: 248,
          column: 110
        }
      },
      "120": {
        start: {
          line: 249,
          column: 12
        },
        end: {
          line: 249,
          column: 30
        }
      },
      "121": {
        start: {
          line: 256,
          column: 64
        },
        end: {
          line: 256,
          column: 68
        }
      },
      "122": {
        start: {
          line: 258,
          column: 8
        },
        end: {
          line: 262,
          column: 9
        }
      },
      "123": {
        start: {
          line: 259,
          column: 12
        },
        end: {
          line: 259,
          column: 93
        }
      },
      "124": {
        start: {
          line: 260,
          column: 12
        },
        end: {
          line: 260,
          column: 101
        }
      },
      "125": {
        start: {
          line: 261,
          column: 12
        },
        end: {
          line: 261,
          column: 31
        }
      },
      "126": {
        start: {
          line: 264,
          column: 8
        },
        end: {
          line: 266,
          column: 9
        }
      },
      "127": {
        start: {
          line: 265,
          column: 12
        },
        end: {
          line: 265,
          column: 100
        }
      },
      "128": {
        start: {
          line: 267,
          column: 8
        },
        end: {
          line: 273,
          column: 9
        }
      },
      "129": {
        start: {
          line: 268,
          column: 33
        },
        end: {
          line: 268,
          column: 56
        }
      },
      "130": {
        start: {
          line: 269,
          column: 12
        },
        end: {
          line: 272,
          column: 13
        }
      },
      "131": {
        start: {
          line: 270,
          column: 16
        },
        end: {
          line: 270,
          column: 109
        }
      },
      "132": {
        start: {
          line: 271,
          column: 16
        },
        end: {
          line: 271,
          column: 34
        }
      },
      "133": {
        start: {
          line: 279,
          column: 20
        },
        end: {
          line: 279,
          column: 23
        }
      },
      "134": {
        start: {
          line: 282,
          column: 53
        },
        end: {
          line: 282,
          column: 57
        }
      },
      "135": {
        start: {
          line: 283,
          column: 32
        },
        end: {
          line: 283,
          column: 92
        }
      },
      "136": {
        start: {
          line: 285,
          column: 34
        },
        end: {
          line: 285,
          column: 88
        }
      },
      "137": {
        start: {
          line: 286,
          column: 8
        },
        end: {
          line: 288,
          column: 9
        }
      },
      "138": {
        start: {
          line: 287,
          column: 12
        },
        end: {
          line: 287,
          column: 23
        }
      },
      "139": {
        start: {
          line: 290,
          column: 8
        },
        end: {
          line: 290,
          column: 49
        }
      },
      "140": {
        start: {
          line: 296,
          column: 8
        },
        end: {
          line: 296,
          column: 65
        }
      },
      "141": {
        start: {
          line: 302,
          column: 8
        },
        end: {
          line: 302,
          column: 65
        }
      },
      "142": {
        start: {
          line: 308,
          column: 8
        },
        end: {
          line: 308,
          column: 63
        }
      },
      "143": {
        start: {
          line: 314,
          column: 23
        },
        end: {
          line: 314,
          column: 25
        }
      },
      "144": {
        start: {
          line: 315,
          column: 8
        },
        end: {
          line: 317,
          column: 9
        }
      },
      "145": {
        start: {
          line: 316,
          column: 12
        },
        end: {
          line: 316,
          column: 59
        }
      },
      "146": {
        start: {
          line: 318,
          column: 8
        },
        end: {
          line: 320,
          column: 9
        }
      },
      "147": {
        start: {
          line: 319,
          column: 12
        },
        end: {
          line: 319,
          column: 58
        }
      },
      "148": {
        start: {
          line: 321,
          column: 8
        },
        end: {
          line: 323,
          column: 9
        }
      },
      "149": {
        start: {
          line: 322,
          column: 12
        },
        end: {
          line: 322,
          column: 55
        }
      },
      "150": {
        start: {
          line: 324,
          column: 8
        },
        end: {
          line: 326,
          column: 9
        }
      },
      "151": {
        start: {
          line: 325,
          column: 12
        },
        end: {
          line: 325,
          column: 70
        }
      },
      "152": {
        start: {
          line: 327,
          column: 8
        },
        end: {
          line: 334,
          column: 9
        }
      },
      "153": {
        start: {
          line: 328,
          column: 12
        },
        end: {
          line: 330,
          column: 13
        }
      },
      "154": {
        start: {
          line: 329,
          column: 16
        },
        end: {
          line: 329,
          column: 82
        }
      },
      "155": {
        start: {
          line: 331,
          column: 12
        },
        end: {
          line: 333,
          column: 13
        }
      },
      "156": {
        start: {
          line: 332,
          column: 16
        },
        end: {
          line: 332,
          column: 83
        }
      },
      "157": {
        start: {
          line: 335,
          column: 8
        },
        end: {
          line: 339,
          column: 9
        }
      },
      "158": {
        start: {
          line: 336,
          column: 12
        },
        end: {
          line: 338,
          column: 13
        }
      },
      "159": {
        start: {
          line: 337,
          column: 16
        },
        end: {
          line: 337,
          column: 79
        }
      },
      "160": {
        start: {
          line: 340,
          column: 8
        },
        end: {
          line: 343,
          column: 10
        }
      },
      "161": {
        start: {
          line: 346,
          column: 0
        },
        end: {
          line: 346,
          column: 42
        }
      },
      "162": {
        start: {
          line: 348,
          column: 0
        },
        end: {
          line: 366,
          column: 2
        }
      },
      "163": {
        start: {
          line: 368,
          column: 0
        },
        end: {
          line: 378,
          column: 2
        }
      },
      "164": {
        start: {
          line: 380,
          column: 0
        },
        end: {
          line: 388,
          column: 2
        }
      },
      "165": {
        start: {
          line: 389,
          column: 0
        },
        end: {
          line: 389,
          column: 34
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 22,
            column: 4
          },
          end: {
            line: 22,
            column: 5
          }
        },
        loc: {
          start: {
            line: 22,
            column: 42
          },
          end: {
            line: 55,
            column: 5
          }
        },
        line: 22
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 59,
            column: 4
          },
          end: {
            line: 59,
            column: 5
          }
        },
        loc: {
          start: {
            line: 59,
            column: 42
          },
          end: {
            line: 103,
            column: 5
          }
        },
        line: 59
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 107,
            column: 4
          },
          end: {
            line: 107,
            column: 5
          }
        },
        loc: {
          start: {
            line: 107,
            column: 40
          },
          end: {
            line: 153,
            column: 5
          }
        },
        line: 107
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 157,
            column: 4
          },
          end: {
            line: 157,
            column: 5
          }
        },
        loc: {
          start: {
            line: 157,
            column: 38
          },
          end: {
            line: 168,
            column: 5
          }
        },
        line: 157
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 172,
            column: 4
          },
          end: {
            line: 172,
            column: 5
          }
        },
        loc: {
          start: {
            line: 172,
            column: 42
          },
          end: {
            line: 192,
            column: 5
          }
        },
        line: 172
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 196,
            column: 4
          },
          end: {
            line: 196,
            column: 5
          }
        },
        loc: {
          start: {
            line: 196,
            column: 46
          },
          end: {
            line: 212,
            column: 5
          }
        },
        line: 196
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 216,
            column: 4
          },
          end: {
            line: 216,
            column: 5
          }
        },
        loc: {
          start: {
            line: 216,
            column: 45
          },
          end: {
            line: 236,
            column: 5
          }
        },
        line: 216
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 240,
            column: 4
          },
          end: {
            line: 240,
            column: 5
          }
        },
        loc: {
          start: {
            line: 240,
            column: 38
          },
          end: {
            line: 251,
            column: 5
          }
        },
        line: 240
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 255,
            column: 4
          },
          end: {
            line: 255,
            column: 5
          }
        },
        loc: {
          start: {
            line: 255,
            column: 46
          },
          end: {
            line: 274,
            column: 5
          }
        },
        line: 255
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 278,
            column: 4
          },
          end: {
            line: 278,
            column: 5
          }
        },
        loc: {
          start: {
            line: 278,
            column: 46
          },
          end: {
            line: 291,
            column: 5
          }
        },
        line: 278
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 295,
            column: 4
          },
          end: {
            line: 295,
            column: 5
          }
        },
        loc: {
          start: {
            line: 295,
            column: 32
          },
          end: {
            line: 297,
            column: 5
          }
        },
        line: 295
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 301,
            column: 4
          },
          end: {
            line: 301,
            column: 5
          }
        },
        loc: {
          start: {
            line: 301,
            column: 32
          },
          end: {
            line: 303,
            column: 5
          }
        },
        line: 301
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 307,
            column: 4
          },
          end: {
            line: 307,
            column: 5
          }
        },
        loc: {
          start: {
            line: 307,
            column: 30
          },
          end: {
            line: 309,
            column: 5
          }
        },
        line: 307
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 313,
            column: 4
          },
          end: {
            line: 313,
            column: 5
          }
        },
        loc: {
          start: {
            line: 313,
            column: 35
          },
          end: {
            line: 344,
            column: 5
          }
        },
        line: 313
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 38,
            column: 12
          },
          end: {
            line: 40,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 38,
            column: 12
          },
          end: {
            line: 40,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 38
      },
      "1": {
        loc: {
          start: {
            line: 38,
            column: 16
          },
          end: {
            line: 38,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 38,
            column: 16
          },
          end: {
            line: 38,
            column: 47
          }
        }, {
          start: {
            line: 38,
            column: 51
          },
          end: {
            line: 38,
            column: 67
          }
        }],
        line: 38
      },
      "2": {
        loc: {
          start: {
            line: 70,
            column: 30
          },
          end: {
            line: 70,
            column: 53
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 70,
            column: 41
          },
          end: {
            line: 70,
            column: 53
          }
        }],
        line: 70
      },
      "3": {
        loc: {
          start: {
            line: 73,
            column: 12
          },
          end: {
            line: 84,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 73,
            column: 12
          },
          end: {
            line: 84,
            column: 13
          }
        }, {
          start: {
            line: 79,
            column: 17
          },
          end: {
            line: 84,
            column: 13
          }
        }],
        line: 73
      },
      "4": {
        loc: {
          start: {
            line: 74,
            column: 16
          },
          end: {
            line: 77,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 74,
            column: 16
          },
          end: {
            line: 77,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 74
      },
      "5": {
        loc: {
          start: {
            line: 80,
            column: 16
          },
          end: {
            line: 83,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 80,
            column: 16
          },
          end: {
            line: 83,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 80
      },
      "6": {
        loc: {
          start: {
            line: 86,
            column: 12
          },
          end: {
            line: 94,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 86,
            column: 12
          },
          end: {
            line: 94,
            column: 13
          }
        }, {
          start: {
            line: 91,
            column: 17
          },
          end: {
            line: 94,
            column: 13
          }
        }],
        line: 86
      },
      "7": {
        loc: {
          start: {
            line: 91,
            column: 17
          },
          end: {
            line: 94,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 91,
            column: 17
          },
          end: {
            line: 94,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 91
      },
      "8": {
        loc: {
          start: {
            line: 118,
            column: 12
          },
          end: {
            line: 121,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 118,
            column: 12
          },
          end: {
            line: 121,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 118
      },
      "9": {
        loc: {
          start: {
            line: 122,
            column: 30
          },
          end: {
            line: 122,
            column: 42
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 122,
            column: 41
          },
          end: {
            line: 122,
            column: 42
          }
        }],
        line: 122
      },
      "10": {
        loc: {
          start: {
            line: 125,
            column: 12
          },
          end: {
            line: 133,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 125,
            column: 12
          },
          end: {
            line: 133,
            column: 13
          }
        }, {
          start: {
            line: 130,
            column: 17
          },
          end: {
            line: 133,
            column: 13
          }
        }],
        line: 125
      },
      "11": {
        loc: {
          start: {
            line: 130,
            column: 17
          },
          end: {
            line: 133,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 130,
            column: 17
          },
          end: {
            line: 133,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 130
      },
      "12": {
        loc: {
          start: {
            line: 135,
            column: 12
          },
          end: {
            line: 138,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 135,
            column: 12
          },
          end: {
            line: 138,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 135
      },
      "13": {
        loc: {
          start: {
            line: 140,
            column: 12
          },
          end: {
            line: 144,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 140,
            column: 12
          },
          end: {
            line: 144,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 140
      },
      "14": {
        loc: {
          start: {
            line: 164,
            column: 8
          },
          end: {
            line: 166,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 164,
            column: 8
          },
          end: {
            line: 166,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 164
      },
      "15": {
        loc: {
          start: {
            line: 173,
            column: 26
          },
          end: {
            line: 173,
            column: 48
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 173,
            column: 40
          },
          end: {
            line: 173,
            column: 48
          }
        }],
        line: 173
      },
      "16": {
        loc: {
          start: {
            line: 174,
            column: 23
          },
          end: {
            line: 174,
            column: 107
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 174,
            column: 23
          },
          end: {
            line: 174,
            column: 66
          }
        }, {
          start: {
            line: 174,
            column: 70
          },
          end: {
            line: 174,
            column: 107
          }
        }],
        line: 174
      },
      "17": {
        loc: {
          start: {
            line: 175,
            column: 8
          },
          end: {
            line: 191,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 175,
            column: 8
          },
          end: {
            line: 191,
            column: 9
          }
        }, {
          start: {
            line: 179,
            column: 13
          },
          end: {
            line: 191,
            column: 9
          }
        }],
        line: 175
      },
      "18": {
        loc: {
          start: {
            line: 179,
            column: 13
          },
          end: {
            line: 191,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 179,
            column: 13
          },
          end: {
            line: 191,
            column: 9
          }
        }, {
          start: {
            line: 184,
            column: 13
          },
          end: {
            line: 191,
            column: 9
          }
        }],
        line: 179
      },
      "19": {
        loc: {
          start: {
            line: 184,
            column: 13
          },
          end: {
            line: 191,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 184,
            column: 13
          },
          end: {
            line: 191,
            column: 9
          }
        }, {
          start: {
            line: 188,
            column: 13
          },
          end: {
            line: 191,
            column: 9
          }
        }],
        line: 184
      },
      "20": {
        loc: {
          start: {
            line: 188,
            column: 13
          },
          end: {
            line: 191,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 188,
            column: 13
          },
          end: {
            line: 191,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 188
      },
      "21": {
        loc: {
          start: {
            line: 199,
            column: 8
          },
          end: {
            line: 211,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 199,
            column: 8
          },
          end: {
            line: 211,
            column: 9
          }
        }, {
          start: {
            line: 204,
            column: 13
          },
          end: {
            line: 211,
            column: 9
          }
        }],
        line: 199
      },
      "22": {
        loc: {
          start: {
            line: 204,
            column: 13
          },
          end: {
            line: 211,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 204,
            column: 13
          },
          end: {
            line: 211,
            column: 9
          }
        }, {
          start: {
            line: 208,
            column: 13
          },
          end: {
            line: 211,
            column: 9
          }
        }],
        line: 204
      },
      "23": {
        loc: {
          start: {
            line: 208,
            column: 13
          },
          end: {
            line: 211,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 208,
            column: 13
          },
          end: {
            line: 211,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 208
      },
      "24": {
        loc: {
          start: {
            line: 218,
            column: 8
          },
          end: {
            line: 219,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 218,
            column: 8
          },
          end: {
            line: 219,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 218
      },
      "25": {
        loc: {
          start: {
            line: 221,
            column: 8
          },
          end: {
            line: 235,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 221,
            column: 8
          },
          end: {
            line: 235,
            column: 9
          }
        }, {
          start: {
            line: 227,
            column: 13
          },
          end: {
            line: 235,
            column: 9
          }
        }],
        line: 221
      },
      "26": {
        loc: {
          start: {
            line: 227,
            column: 13
          },
          end: {
            line: 235,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 227,
            column: 13
          },
          end: {
            line: 235,
            column: 9
          }
        }, {
          start: {
            line: 232,
            column: 13
          },
          end: {
            line: 235,
            column: 9
          }
        }],
        line: 227
      },
      "27": {
        loc: {
          start: {
            line: 232,
            column: 13
          },
          end: {
            line: 235,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 232,
            column: 13
          },
          end: {
            line: 235,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 232
      },
      "28": {
        loc: {
          start: {
            line: 242,
            column: 8
          },
          end: {
            line: 245,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 242,
            column: 8
          },
          end: {
            line: 245,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 242
      },
      "29": {
        loc: {
          start: {
            line: 247,
            column: 8
          },
          end: {
            line: 250,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 247,
            column: 8
          },
          end: {
            line: 250,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 247
      },
      "30": {
        loc: {
          start: {
            line: 258,
            column: 8
          },
          end: {
            line: 262,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 258,
            column: 8
          },
          end: {
            line: 262,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 258
      },
      "31": {
        loc: {
          start: {
            line: 258,
            column: 12
          },
          end: {
            line: 258,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 258,
            column: 12
          },
          end: {
            line: 258,
            column: 37
          }
        }, {
          start: {
            line: 258,
            column: 41
          },
          end: {
            line: 258,
            column: 61
          }
        }],
        line: 258
      },
      "32": {
        loc: {
          start: {
            line: 264,
            column: 8
          },
          end: {
            line: 266,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 264,
            column: 8
          },
          end: {
            line: 266,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 264
      },
      "33": {
        loc: {
          start: {
            line: 264,
            column: 12
          },
          end: {
            line: 264,
            column: 60
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 264,
            column: 12
          },
          end: {
            line: 264,
            column: 32
          }
        }, {
          start: {
            line: 264,
            column: 36
          },
          end: {
            line: 264,
            column: 44
          }
        }, {
          start: {
            line: 264,
            column: 48
          },
          end: {
            line: 264,
            column: 60
          }
        }],
        line: 264
      },
      "34": {
        loc: {
          start: {
            line: 267,
            column: 8
          },
          end: {
            line: 273,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 267,
            column: 8
          },
          end: {
            line: 273,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 267
      },
      "35": {
        loc: {
          start: {
            line: 267,
            column: 12
          },
          end: {
            line: 267,
            column: 57
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 267,
            column: 12
          },
          end: {
            line: 267,
            column: 38
          }
        }, {
          start: {
            line: 267,
            column: 42
          },
          end: {
            line: 267,
            column: 47
          }
        }, {
          start: {
            line: 267,
            column: 51
          },
          end: {
            line: 267,
            column: 57
          }
        }],
        line: 267
      },
      "36": {
        loc: {
          start: {
            line: 269,
            column: 12
          },
          end: {
            line: 272,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 269,
            column: 12
          },
          end: {
            line: 272,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 269
      },
      "37": {
        loc: {
          start: {
            line: 282,
            column: 26
          },
          end: {
            line: 282,
            column: 48
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 282,
            column: 40
          },
          end: {
            line: 282,
            column: 48
          }
        }],
        line: 282
      },
      "38": {
        loc: {
          start: {
            line: 283,
            column: 32
          },
          end: {
            line: 283,
            column: 92
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 283,
            column: 32
          },
          end: {
            line: 283,
            column: 84
          }
        }, {
          start: {
            line: 283,
            column: 88
          },
          end: {
            line: 283,
            column: 92
          }
        }],
        line: 283
      },
      "39": {
        loc: {
          start: {
            line: 286,
            column: 8
          },
          end: {
            line: 288,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 286,
            column: 8
          },
          end: {
            line: 288,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 286
      },
      "40": {
        loc: {
          start: {
            line: 315,
            column: 8
          },
          end: {
            line: 317,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 315,
            column: 8
          },
          end: {
            line: 317,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 315
      },
      "41": {
        loc: {
          start: {
            line: 315,
            column: 12
          },
          end: {
            line: 315,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 315,
            column: 12
          },
          end: {
            line: 315,
            column: 26
          }
        }, {
          start: {
            line: 315,
            column: 30
          },
          end: {
            line: 315,
            column: 48
          }
        }],
        line: 315
      },
      "42": {
        loc: {
          start: {
            line: 318,
            column: 8
          },
          end: {
            line: 320,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 318,
            column: 8
          },
          end: {
            line: 320,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 318
      },
      "43": {
        loc: {
          start: {
            line: 318,
            column: 12
          },
          end: {
            line: 318,
            column: 46
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 318,
            column: 12
          },
          end: {
            line: 318,
            column: 25
          }
        }, {
          start: {
            line: 318,
            column: 29
          },
          end: {
            line: 318,
            column: 46
          }
        }],
        line: 318
      },
      "44": {
        loc: {
          start: {
            line: 321,
            column: 8
          },
          end: {
            line: 323,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 321,
            column: 8
          },
          end: {
            line: 323,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 321
      },
      "45": {
        loc: {
          start: {
            line: 321,
            column: 12
          },
          end: {
            line: 321,
            column: 40
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 321,
            column: 12
          },
          end: {
            line: 321,
            column: 22
          }
        }, {
          start: {
            line: 321,
            column: 26
          },
          end: {
            line: 321,
            column: 40
          }
        }],
        line: 321
      },
      "46": {
        loc: {
          start: {
            line: 324,
            column: 8
          },
          end: {
            line: 326,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 324,
            column: 8
          },
          end: {
            line: 326,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 324
      },
      "47": {
        loc: {
          start: {
            line: 327,
            column: 8
          },
          end: {
            line: 334,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 327,
            column: 8
          },
          end: {
            line: 334,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 327
      },
      "48": {
        loc: {
          start: {
            line: 328,
            column: 12
          },
          end: {
            line: 330,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 328,
            column: 12
          },
          end: {
            line: 330,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 328
      },
      "49": {
        loc: {
          start: {
            line: 328,
            column: 16
          },
          end: {
            line: 328,
            column: 46
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 328,
            column: 16
          },
          end: {
            line: 328,
            column: 27
          }
        }, {
          start: {
            line: 328,
            column: 31
          },
          end: {
            line: 328,
            column: 46
          }
        }],
        line: 328
      },
      "50": {
        loc: {
          start: {
            line: 331,
            column: 12
          },
          end: {
            line: 333,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 331,
            column: 12
          },
          end: {
            line: 333,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 331
      },
      "51": {
        loc: {
          start: {
            line: 331,
            column: 16
          },
          end: {
            line: 331,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 331,
            column: 16
          },
          end: {
            line: 331,
            column: 28
          }
        }, {
          start: {
            line: 331,
            column: 32
          },
          end: {
            line: 331,
            column: 48
          }
        }],
        line: 331
      },
      "52": {
        loc: {
          start: {
            line: 335,
            column: 8
          },
          end: {
            line: 339,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 335,
            column: 8
          },
          end: {
            line: 339,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 335
      },
      "53": {
        loc: {
          start: {
            line: 336,
            column: 12
          },
          end: {
            line: 338,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 336,
            column: 12
          },
          end: {
            line: 338,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 336
      },
      "54": {
        loc: {
          start: {
            line: 336,
            column: 16
          },
          end: {
            line: 336,
            column: 52
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 336,
            column: 16
          },
          end: {
            line: 336,
            column: 30
          }
        }, {
          start: {
            line: 336,
            column: 34
          },
          end: {
            line: 336,
            column: 52
          }
        }],
        line: 336
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0, 0],
      "34": [0, 0],
      "35": [0, 0, 0],
      "36": [0, 0],
      "37": [0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\SMACNAValidator.ts",
      mappings: ";AAAA;;;;;;;;GAQG;;;AAsFH;;;GAGG;AACH,MAAa,eAAe;IA8C1B;;;OAGG;IACI,MAAM,CAAC,wBAAwB,CAAC,IAAqB;QAC1D,MAAM,MAAM,GAAqB;YAC/B,OAAO,EAAE,IAAI;YACb,SAAS,EAAE,IAAI;YACf,MAAM,EAAE,EAAE;YACV,QAAQ,EAAE,EAAE;YACZ,eAAe,EAAE,EAAE;YACnB,iBAAiB,EAAE,8CAA8C;YACjE,KAAK,EAAE,GAAG;SACX,CAAC;QAEF,IAAI,CAAC;YACH,sBAAsB;YACtB,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;YAEpC,2BAA2B;YAC3B,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;YAExC,kDAAkD;YAClD,IAAI,IAAI,CAAC,QAAQ,KAAK,aAAa,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;gBACxD,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;YACzC,CAAC;YAED,kBAAkB;YAClB,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;YAEhC,uCAAuC;YACvC,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;YAExC,0BAA0B;YAC1B,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAEzD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,4BAA4B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAChE,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;YACvB,MAAM,CAAC,SAAS,GAAG,KAAK,CAAC;YACzB,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC;QACnB,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,wBAAwB,CAAC,IAAqB;QAC1D,MAAM,MAAM,GAAqB;YAC/B,OAAO,EAAE,IAAI;YACb,SAAS,EAAE,IAAI;YACf,MAAM,EAAE,EAAE;YACV,QAAQ,EAAE,EAAE;YACZ,eAAe,EAAE,EAAE;YACnB,iBAAiB,EAAE,qCAAqC;YACxD,KAAK,EAAE,GAAG;SACX,CAAC;QAEF,IAAI,CAAC;YACH,MAAM,EAAE,QAAQ,EAAE,QAAQ,GAAG,YAAY,EAAE,GAAG,IAAI,CAAC;YACnD,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC;YAExC,8BAA8B;YAC9B,IAAI,QAAQ,KAAK,UAAU,EAAE,CAAC;gBAC5B,IAAI,QAAQ,GAAG,SAAS,CAAC,eAAe,CAAC,YAAY,EAAE,CAAC;oBACtD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAClB,YAAY,QAAQ,yDAAyD,SAAS,CAAC,eAAe,CAAC,YAAY,MAAM,CAC1H,CAAC;oBACF,MAAM,CAAC,KAAK,IAAI,EAAE,CAAC;gBACrB,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,IAAI,QAAQ,GAAG,SAAS,CAAC,eAAe,CAAC,cAAc,EAAE,CAAC;oBACxD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAClB,YAAY,QAAQ,wCAAwC,SAAS,CAAC,eAAe,CAAC,cAAc,MAAM,CAC3G,CAAC;oBACF,MAAM,CAAC,KAAK,IAAI,EAAE,CAAC;gBACrB,CAAC;YACH,CAAC;YAED,4BAA4B;YAC5B,IAAI,QAAQ,GAAG,SAAS,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;gBAC5C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAClB,iBAAiB,QAAQ,gCAAgC,CAC1D,CAAC;gBACF,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;gBACnE,MAAM,CAAC,KAAK,IAAI,EAAE,CAAC;YACrB,CAAC;iBAAM,IAAI,QAAQ,GAAG,SAAS,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;gBACvD,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;gBACvE,MAAM,CAAC,KAAK,IAAI,CAAC,CAAC;YACpB,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,4BAA4B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAChE,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;YACvB,MAAM,CAAC,SAAS,GAAG,KAAK,CAAC;YACzB,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC;QACnB,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,sBAAsB,CAAC,IAAqB;QACxD,MAAM,MAAM,GAAqB;YAC/B,OAAO,EAAE,IAAI;YACb,SAAS,EAAE,IAAI;YACf,MAAM,EAAE,EAAE;YACV,QAAQ,EAAE,EAAE;YACZ,eAAe,EAAE,EAAE;YACnB,iBAAiB,EAAE,mEAAmE;YACtF,KAAK,EAAE,GAAG;SACX,CAAC;QAEF,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,WAAW,KAAK,QAAQ,EAAE,CAAC;gBAClC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,2DAA2D,CAAC,CAAC;gBAClF,OAAO,MAAM,CAAC;YAChB,CAAC;YAED,MAAM,EAAE,QAAQ,EAAE,QAAQ,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC;YACxC,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC;YAEtC,yCAAyC;YACzC,IAAI,QAAQ,GAAG,SAAS,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;gBAChD,MAAM,CAAC,MAAM,CAAC,IAAI,CAChB,YAAY,QAAQ,oCAAoC,SAAS,CAAC,cAAc,CAAC,OAAO,yBAAyB,CAClH,CAAC;gBACF,MAAM,CAAC,SAAS,GAAG,KAAK,CAAC;gBACzB,MAAM,CAAC,KAAK,IAAI,EAAE,CAAC;YACrB,CAAC;iBAAM,IAAI,QAAQ,GAAG,SAAS,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC;gBAC3D,MAAM,CAAC,QAAQ,CAAC,IAAI,CAClB,YAAY,QAAQ,gDAAgD,SAAS,CAAC,cAAc,CAAC,WAAW,MAAM,CAC/G,CAAC;gBACF,MAAM,CAAC,KAAK,IAAI,EAAE,CAAC;YACrB,CAAC;YAED,sBAAsB;YACtB,IAAI,QAAQ,GAAG,SAAS,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;gBAChD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAClB,mBAAmB,QAAQ,uDAAuD,SAAS,CAAC,cAAc,CAAC,OAAO,cAAc,CACjI,CAAC;gBACF,MAAM,CAAC,KAAK,IAAI,EAAE,CAAC;YACrB,CAAC;YAED,sCAAsC;YACtC,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;gBAC9B,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,2DAA2D,CAAC,CAAC;gBAClF,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,+DAA+D,CAAC,CAAC;gBAC7F,MAAM,CAAC,KAAK,IAAI,EAAE,CAAC;YACrB,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,0BAA0B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC9D,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;YACvB,MAAM,CAAC,SAAS,GAAG,KAAK,CAAC;YACzB,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC;QACnB,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,oBAAoB,CAAC,IAAqB;QACtD,MAAM,OAAO,GAAqC,EAAE,CAAC;QAErD,8CAA8C;QAC9C,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;QAErD,6CAA6C;QAC7C,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;QAErD,sCAAsC;QACtC,IAAI,IAAI,CAAC,WAAW,KAAK,QAAQ,EAAE,CAAC;YAClC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;QACnD,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,gBAAgB,CAAC,IAAqB,EAAE,MAAwB;QAC7E,MAAM,EAAE,QAAQ,EAAE,WAAW,GAAG,QAAQ,EAAE,GAAG,IAAI,CAAC;QAClD,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC;QAEpG,IAAI,QAAQ,GAAG,MAAM,CAAC,GAAG,EAAE,CAAC;YAC1B,MAAM,CAAC,QAAQ,CAAC,IAAI,CAClB,YAAY,QAAQ,mCAAmC,MAAM,CAAC,GAAG,YAAY,WAAW,OAAO,CAChG,CAAC;YACF,MAAM,CAAC,KAAK,IAAI,EAAE,CAAC;QACrB,CAAC;aAAM,IAAI,QAAQ,GAAG,MAAM,CAAC,GAAG,EAAE,CAAC;YACjC,MAAM,CAAC,MAAM,CAAC,IAAI,CAChB,YAAY,QAAQ,kCAAkC,MAAM,CAAC,GAAG,YAAY,WAAW,OAAO,CAC/F,CAAC;YACF,MAAM,CAAC,SAAS,GAAG,KAAK,CAAC;YACzB,MAAM,CAAC,KAAK,IAAI,EAAE,CAAC;QACrB,CAAC;aAAM,IAAI,QAAQ,GAAG,MAAM,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC;YAC3C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAClB,YAAY,QAAQ,gCAAgC,MAAM,CAAC,OAAO,kBAAkB,WAAW,OAAO,CACvG,CAAC;YACF,MAAM,CAAC,KAAK,IAAI,EAAE,CAAC;QACrB,CAAC;aAAM,IAAI,QAAQ,GAAG,MAAM,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC;YAC3C,MAAM,CAAC,eAAe,CAAC,IAAI,CACzB,8DAA8D,MAAM,CAAC,OAAO,MAAM,CACnF,CAAC;YACF,MAAM,CAAC,KAAK,IAAI,CAAC,CAAC;QACpB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,oBAAoB,CAAC,IAAqB,EAAE,MAAwB;QACjF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC;QAC9B,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC;QAEjD,IAAI,YAAY,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,IAAI,CAChB,iBAAiB,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,gDAAgD,SAAS,CAAC,OAAO,oBAAoB,CAC9H,CAAC;YACF,MAAM,CAAC,SAAS,GAAG,KAAK,CAAC;YACzB,MAAM,CAAC,KAAK,IAAI,EAAE,CAAC;QACrB,CAAC;aAAM,IAAI,YAAY,GAAG,SAAS,CAAC,IAAI,EAAE,CAAC;YACzC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAClB,sBAAsB,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,sDAAsD,CACpG,CAAC;YACF,MAAM,CAAC,KAAK,IAAI,EAAE,CAAC;QACrB,CAAC;aAAM,IAAI,YAAY,GAAG,SAAS,CAAC,GAAG,EAAE,CAAC;YACxC,MAAM,CAAC,eAAe,CAAC,IAAI,CACzB,qFAAqF,CACtF,CAAC;YACF,MAAM,CAAC,KAAK,IAAI,CAAC,CAAC;QACpB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,mBAAmB,CAAC,IAAqB,EAAE,MAAwB;QAChF,MAAM,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC;QAC7B,IAAI,CAAC,WAAW;YAAE,OAAO;QAEzB,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC;QAEpD,IAAI,WAAW,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC;YACpC,MAAM,CAAC,MAAM,CAAC,IAAI,CAChB,gBAAgB,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,gCAAgC,SAAS,CAAC,OAAO,IAAI,CAC5F,CAAC;YACF,MAAM,CAAC,SAAS,GAAG,KAAK,CAAC;YACzB,MAAM,CAAC,KAAK,IAAI,EAAE,CAAC;YACnB,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;QACpF,CAAC;aAAM,IAAI,WAAW,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC;YAC3C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAClB,gBAAgB,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,2DAA2D,CAClG,CAAC;YACF,MAAM,CAAC,KAAK,IAAI,EAAE,CAAC;YACnB,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;QAC/E,CAAC;aAAM,IAAI,WAAW,GAAG,SAAS,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC;YACjD,MAAM,CAAC,eAAe,CAAC,IAAI,CACzB,gGAAgG,CACjG,CAAC;YACF,MAAM,CAAC,KAAK,IAAI,CAAC,CAAC;QACpB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,YAAY,CAAC,IAAqB,EAAE,MAAwB;QACzE,MAAM,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC;QAEtB,IAAI,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,CAAC;YAC7C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAClB,wBAAwB,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,kDAAkD,CAC1F,CAAC;YACF,MAAM,CAAC,KAAK,IAAI,EAAE,CAAC;QACrB,CAAC;QAED,yEAAyE;QACzE,IAAI,IAAI,GAAG,GAAG,EAAE,CAAC;YACf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAClB,wBAAwB,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,oCAAoC,CAC5E,CAAC;YACF,MAAM,CAAC,KAAK,IAAI,CAAC,CAAC;QACpB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,oBAAoB,CAAC,IAAqB,EAAE,MAAwB;QACjF,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;QAE7D,oCAAoC;QACpC,IAAI,QAAQ,KAAK,YAAY,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,EAAE,CAAC;YACtD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,0DAA0D,CAAC,CAAC;YACjF,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,2DAA2D,CAAC,CAAC;YACzF,MAAM,CAAC,KAAK,IAAI,EAAE,CAAC;QACrB,CAAC;QAED,gCAAgC;QAChC,IAAI,QAAQ,KAAK,OAAO,IAAI,QAAQ,IAAI,QAAQ,GAAG,CAAC,EAAE,CAAC;YACrD,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,0DAA0D,CAAC,CAAC;QAC1F,CAAC;QAED,IAAI,QAAQ,KAAK,aAAa,IAAI,KAAK,IAAI,MAAM,EAAE,CAAC;YAClD,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YAC7C,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;gBACrB,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,sEAAsE,CAAC,CAAC;gBAC7F,MAAM,CAAC,KAAK,IAAI,CAAC,CAAC;YACpB,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,oBAAoB,CAAC,IAAqB,EAAE,MAAwB;QACjF,IAAI,KAAK,GAAG,GAAG,CAAC;QAEhB,iFAAiF;QACjF,2CAA2C;QAC3C,MAAM,EAAE,QAAQ,EAAE,WAAW,GAAG,QAAQ,EAAE,GAAG,IAAI,CAAC;QAClD,MAAM,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,OAAO,IAAI,IAAI,CAAC;QAErF,mDAAmD;QACnD,MAAM,iBAAiB,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,eAAe,CAAC,GAAG,eAAe,CAAC;QACjF,IAAI,iBAAiB,GAAG,GAAG,EAAE,CAAC;YAC5B,KAAK,IAAI,CAAC,CAAC,CAAC,wCAAwC;QACtD,CAAC;QAED,+CAA+C;QAC/C,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,kBAAkB;QAC9B,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;IAC3D,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,kBAAkB;QAC9B,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;IAC3D,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,gBAAgB;QAC5B,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,iBAAiB,CAAC,IAAqB;QACnD,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,EAAE,CAAC;YACzC,MAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,EAAE,CAAC;YACvC,MAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;QAChD,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,CAAC;YACjC,MAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;QAC7C,CAAC;QAED,IAAI,CAAC,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YACtD,MAAM,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;QAC5D,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;YACpC,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,EAAE,CAAC;gBACnC,MAAM,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;YACpE,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;gBACrC,MAAM,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;YACrE,CAAC;QACH,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;YAC9B,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,EAAE,CAAC;gBACzC,MAAM,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;YACjE,CAAC;QACH,CAAC;QAED,OAAO;YACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC5B,MAAM;SACP,CAAC;IACJ,CAAC;;AAlcH,0CAmcC;AAlcC,kCAAkC;AACV,gCAAgB,GAAoB;IAC1D,QAAQ,EAAE;QACR,MAAM,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE;QAC9C,MAAM,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE;QAC9C,OAAO,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE;KAChD;IACD,QAAQ,EAAE;QACR,GAAG,EAAE,IAAI,EAAO,uBAAuB;QACvC,MAAM,EAAE,IAAI,EAAI,0BAA0B;QAC1C,IAAI,EAAE,IAAI,EAAM,wBAAwB;QACxC,OAAO,EAAE,IAAI,CAAG,sBAAsB;KACvC;IACD,WAAW,EAAE;QACX,OAAO,EAAE,GAAG,EAAI,iBAAiB;QACjC,OAAO,EAAE,GAAG,EAAI,0BAA0B;QAC1C,OAAO,EAAE,GAAG,CAAI,cAAc;KAC/B;IACD,WAAW,EAAE,GAAG,CAAE,4BAA4B;CAC/C,CAAC;AAEF,uCAAuC;AACf,gCAAgB,GAAoB;IAC1D,eAAe,EAAE;QACf,YAAY,EAAE,GAAG,EAAK,0BAA0B;QAChD,cAAc,EAAE,IAAI,CAAE,4BAA4B;KACnD;IACD,aAAa,EAAE;QACb,KAAK,EAAE,IAAI,EAAM,sBAAsB;QACvC,QAAQ,EAAE,IAAI,EAAG,sBAAsB;QACvC,IAAI,EAAE,IAAI,CAAO,8BAA8B;KAChD;CACF,CAAC;AAEF,mCAAmC;AACX,8BAAc,GAAkB;IACtD,cAAc,EAAE;QACd,OAAO,EAAE,IAAI,EAAO,6BAA6B;QACjD,WAAW,EAAE,IAAI,CAAG,qCAAqC;KAC1D;IACD,cAAc,EAAE;QACd,OAAO,EAAE,GAAG,CAAQ,0BAA0B;KAC/C;CACF,CAAC;AAyZJ,kBAAe,eAAe,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\SMACNAValidator.ts"],
      sourcesContent: ["/**\r\n * SMACNAValidator - Pure Validation Functions for SMACNA Standards\r\n * \r\n * MISSION-CRITICAL: Pure TypeScript functions for SMACNA, ASHRAE, and NFPA compliance validation\r\n * Extracted from UI components for reusability and tier enforcement integration\r\n * \r\n * @see docs/implementation/tier-system/tier-boundaries-specification.md\r\n * @see docs/developer-guide/tier-implementation-checklist.md section 2.4\r\n */\r\n\r\n/**\r\n * Validation result structure\r\n */\r\nexport interface ValidationResult {\r\n  isValid: boolean;\r\n  compliant: boolean;\r\n  errors: string[];\r\n  warnings: string[];\r\n  recommendations: string[];\r\n  standardReference: string;\r\n  score: number; // 0-100, higher is better\r\n}\r\n\r\n/**\r\n * Calculation data for validation\r\n */\r\nexport interface CalculationData {\r\n  velocity: number; // FPM\r\n  frictionRate: number; // inches w.g. per 100 feet\r\n  ductType: 'round' | 'rectangular';\r\n  airflow: number; // CFM\r\n  diameter?: number; // inches (for round ducts)\r\n  width?: number; // inches (for rectangular ducts)\r\n  height?: number; // inches (for rectangular ducts)\r\n  aspectRatio?: number;\r\n  area: number; // sq ft\r\n  material?: string;\r\n  location?: 'occupied' | 'unoccupied';\r\n  application?: 'supply' | 'return' | 'exhaust' | 'grease';\r\n  pressure?: number; // inches w.g.\r\n  temperature?: number; // \xB0F\r\n}\r\n\r\n/**\r\n * SMACNA standards configuration\r\n */\r\ninterface SMACNAStandards {\r\n  velocity: {\r\n    supply: { min: number; max: number; optimal: number };\r\n    return: { min: number; max: number; optimal: number };\r\n    exhaust: { min: number; max: number; optimal: number };\r\n  };\r\n  friction: {\r\n    low: number; // inches w.g. per 100 feet\r\n    medium: number;\r\n    high: number;\r\n    maximum: number;\r\n  };\r\n  aspectRatio: {\r\n    maximum: number;\r\n    optimal: number;\r\n    minimum: number;\r\n  };\r\n  minimumArea: number; // sq ft\r\n}\r\n\r\n/**\r\n * ASHRAE standards configuration\r\n */\r\ninterface ASHRAEStandards {\r\n  comfortVelocity: {\r\n    occupiedZone: number; // FPM\r\n    unoccupiedZone: number; // FPM\r\n  };\r\n  noiseVelocity: {\r\n    quiet: number; // FPM\r\n    moderate: number; // FPM\r\n    loud: number; // FPM\r\n  };\r\n}\r\n\r\n/**\r\n * NFPA standards configuration\r\n */\r\ninterface NFPAStandards {\r\n  greaseVelocity: {\r\n    minimum: number; // FPM\r\n    recommended: number; // FPM\r\n  };\r\n  greasePressure: {\r\n    maximum: number; // inches w.g.\r\n  };\r\n}\r\n\r\n/**\r\n * SMACNAValidator - Pure validation functions for HVAC standards\r\n * CRITICAL: No dependencies on UI, storage, or external services\r\n */\r\nexport class SMACNAValidator {\r\n  // SMACNA standards (2012 edition)\r\n  private static readonly SMACNA_STANDARDS: SMACNAStandards = {\r\n    velocity: {\r\n      supply: { min: 400, max: 2500, optimal: 1500 },\r\n      return: { min: 300, max: 2000, optimal: 1200 },\r\n      exhaust: { min: 500, max: 3000, optimal: 1800 }\r\n    },\r\n    friction: {\r\n      low: 0.05,      // Low pressure systems\r\n      medium: 0.08,   // Medium pressure systems\r\n      high: 0.12,     // High pressure systems\r\n      maximum: 0.20   // Maximum recommended\r\n    },\r\n    aspectRatio: {\r\n      maximum: 4.0,   // SMACNA maximum\r\n      optimal: 2.5,   // Optimal for fabrication\r\n      minimum: 1.0    // Square duct\r\n    },\r\n    minimumArea: 0.1  // Minimum duct area (sq ft)\r\n  };\r\n\r\n  // ASHRAE standards (2021 Fundamentals)\r\n  private static readonly ASHRAE_STANDARDS: ASHRAEStandards = {\r\n    comfortVelocity: {\r\n      occupiedZone: 750,    // FPM for occupied spaces\r\n      unoccupiedZone: 1500  // FPM for unoccupied spaces\r\n    },\r\n    noiseVelocity: {\r\n      quiet: 1000,     // Libraries, bedrooms\r\n      moderate: 1500,  // Offices, classrooms\r\n      loud: 2000       // Factories, mechanical rooms\r\n    }\r\n  };\r\n\r\n  // NFPA 96 standards (2021 edition)\r\n  private static readonly NFPA_STANDARDS: NFPAStandards = {\r\n    greaseVelocity: {\r\n      minimum: 1500,      // Minimum for grease removal\r\n      recommended: 2000   // Recommended for effective cleaning\r\n    },\r\n    greasePressure: {\r\n      maximum: 2.0        // Maximum static pressure\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Validate calculation against SMACNA standards\r\n   * CRITICAL: Pure function with comprehensive validation\r\n   */\r\n  public static validateSMACNACompliance(data: CalculationData): ValidationResult {\r\n    const result: ValidationResult = {\r\n      isValid: true,\r\n      compliant: true,\r\n      errors: [],\r\n      warnings: [],\r\n      recommendations: [],\r\n      standardReference: 'SMACNA HVAC Duct Construction Standards 2012',\r\n      score: 100\r\n    };\r\n\r\n    try {\r\n      // Velocity validation\r\n      this.validateVelocity(data, result);\r\n      \r\n      // Friction rate validation\r\n      this.validateFrictionRate(data, result);\r\n      \r\n      // Aspect ratio validation (for rectangular ducts)\r\n      if (data.ductType === 'rectangular' && data.aspectRatio) {\r\n        this.validateAspectRatio(data, result);\r\n      }\r\n      \r\n      // Area validation\r\n      this.validateArea(data, result);\r\n      \r\n      // Material and construction validation\r\n      this.validateConstruction(data, result);\r\n\r\n      // Calculate overall score\r\n      result.score = this.calculateSMACNAScore(data, result);\r\n      \r\n    } catch (error) {\r\n      result.errors.push(`SMACNA validation error: ${error.message}`);\r\n      result.isValid = false;\r\n      result.compliant = false;\r\n      result.score = 0;\r\n    }\r\n\r\n    return result;\r\n  }\r\n\r\n  /**\r\n   * Validate calculation against ASHRAE standards\r\n   */\r\n  public static validateASHRAECompliance(data: CalculationData): ValidationResult {\r\n    const result: ValidationResult = {\r\n      isValid: true,\r\n      compliant: true,\r\n      errors: [],\r\n      warnings: [],\r\n      recommendations: [],\r\n      standardReference: 'ASHRAE Fundamentals 2021 Chapter 21',\r\n      score: 100\r\n    };\r\n\r\n    try {\r\n      const { velocity, location = 'unoccupied' } = data;\r\n      const standards = this.ASHRAE_STANDARDS;\r\n\r\n      // Comfort velocity validation\r\n      if (location === 'occupied') {\r\n        if (velocity > standards.comfortVelocity.occupiedZone) {\r\n          result.warnings.push(\r\n            `Velocity ${velocity} FPM in occupied zone exceeds ASHRAE comfort limit of ${standards.comfortVelocity.occupiedZone} FPM`\r\n          );\r\n          result.score -= 20;\r\n        }\r\n      } else {\r\n        if (velocity > standards.comfortVelocity.unoccupiedZone) {\r\n          result.warnings.push(\r\n            `Velocity ${velocity} FPM exceeds ASHRAE general limit of ${standards.comfortVelocity.unoccupiedZone} FPM`\r\n          );\r\n          result.score -= 10;\r\n        }\r\n      }\r\n\r\n      // Noise velocity validation\r\n      if (velocity > standards.noiseVelocity.loud) {\r\n        result.warnings.push(\r\n          `High velocity ${velocity} FPM may cause excessive noise`\r\n        );\r\n        result.recommendations.push('Consider noise attenuation measures');\r\n        result.score -= 15;\r\n      } else if (velocity > standards.noiseVelocity.moderate) {\r\n        result.recommendations.push('Monitor noise levels in occupied spaces');\r\n        result.score -= 5;\r\n      }\r\n\r\n    } catch (error) {\r\n      result.errors.push(`ASHRAE validation error: ${error.message}`);\r\n      result.isValid = false;\r\n      result.compliant = false;\r\n      result.score = 0;\r\n    }\r\n\r\n    return result;\r\n  }\r\n\r\n  /**\r\n   * Validate calculation against NFPA 96 standards (for grease ducts)\r\n   */\r\n  public static validateNFPACompliance(data: CalculationData): ValidationResult {\r\n    const result: ValidationResult = {\r\n      isValid: true,\r\n      compliant: true,\r\n      errors: [],\r\n      warnings: [],\r\n      recommendations: [],\r\n      standardReference: 'NFPA 96 Standard for Ventilation Control and Fire Protection 2021',\r\n      score: 100\r\n    };\r\n\r\n    try {\r\n      if (data.application !== 'grease') {\r\n        result.warnings.push('NFPA 96 validation only applies to grease exhaust systems');\r\n        return result;\r\n      }\r\n\r\n      const { velocity, pressure = 0 } = data;\r\n      const standards = this.NFPA_STANDARDS;\r\n\r\n      // Velocity validation for grease removal\r\n      if (velocity < standards.greaseVelocity.minimum) {\r\n        result.errors.push(\r\n          `Velocity ${velocity} FPM is below NFPA 96 minimum of ${standards.greaseVelocity.minimum} FPM for grease removal`\r\n        );\r\n        result.compliant = false;\r\n        result.score -= 50;\r\n      } else if (velocity < standards.greaseVelocity.recommended) {\r\n        result.warnings.push(\r\n          `Velocity ${velocity} FPM is below NFPA 96 recommended minimum of ${standards.greaseVelocity.recommended} FPM`\r\n        );\r\n        result.score -= 20;\r\n      }\r\n\r\n      // Pressure validation\r\n      if (pressure > standards.greasePressure.maximum) {\r\n        result.warnings.push(\r\n          `Static pressure ${pressure} inches w.g. exceeds NFPA 96 recommended maximum of ${standards.greasePressure.maximum} inches w.g.`\r\n        );\r\n        result.score -= 15;\r\n      }\r\n\r\n      // Additional grease duct requirements\r\n      if (data.ductType !== 'round') {\r\n        result.warnings.push('NFPA 96 recommends round ducts for grease exhaust systems');\r\n        result.recommendations.push('Consider using round duct for easier cleaning and maintenance');\r\n        result.score -= 10;\r\n      }\r\n\r\n    } catch (error) {\r\n      result.errors.push(`NFPA validation error: ${error.message}`);\r\n      result.isValid = false;\r\n      result.compliant = false;\r\n      result.score = 0;\r\n    }\r\n\r\n    return result;\r\n  }\r\n\r\n  /**\r\n   * Validate against all applicable standards\r\n   */\r\n  public static validateAllStandards(data: CalculationData): Record<string, ValidationResult> {\r\n    const results: Record<string, ValidationResult> = {};\r\n\r\n    // Always validate SMACNA for general ductwork\r\n    results.smacna = this.validateSMACNACompliance(data);\r\n\r\n    // Validate ASHRAE for comfort considerations\r\n    results.ashrae = this.validateASHRAECompliance(data);\r\n\r\n    // Validate NFPA if it's a grease duct\r\n    if (data.application === 'grease') {\r\n      results.nfpa = this.validateNFPACompliance(data);\r\n    }\r\n\r\n    return results;\r\n  }\r\n\r\n  /**\r\n   * Validate velocity against SMACNA standards\r\n   */\r\n  private static validateVelocity(data: CalculationData, result: ValidationResult): void {\r\n    const { velocity, application = 'supply' } = data;\r\n    const limits = this.SMACNA_STANDARDS.velocity[application] || this.SMACNA_STANDARDS.velocity.supply;\r\n\r\n    if (velocity < limits.min) {\r\n      result.warnings.push(\r\n        `Velocity ${velocity} FPM is below SMACNA minimum of ${limits.min} FPM for ${application} duct`\r\n      );\r\n      result.score -= 15;\r\n    } else if (velocity > limits.max) {\r\n      result.errors.push(\r\n        `Velocity ${velocity} FPM exceeds SMACNA maximum of ${limits.max} FPM for ${application} duct`\r\n      );\r\n      result.compliant = false;\r\n      result.score -= 30;\r\n    } else if (velocity > limits.optimal * 1.2) {\r\n      result.warnings.push(\r\n        `Velocity ${velocity} FPM is above optimal range (${limits.optimal} FPM \xB120%) for ${application} duct`\r\n      );\r\n      result.score -= 10;\r\n    } else if (velocity < limits.optimal * 0.8) {\r\n      result.recommendations.push(\r\n        `Consider reducing duct size to achieve optimal velocity of ${limits.optimal} FPM`\r\n      );\r\n      result.score -= 5;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Validate friction rate against SMACNA standards\r\n   */\r\n  private static validateFrictionRate(data: CalculationData, result: ValidationResult): void {\r\n    const { frictionRate } = data;\r\n    const standards = this.SMACNA_STANDARDS.friction;\r\n\r\n    if (frictionRate > standards.maximum) {\r\n      result.errors.push(\r\n        `Friction rate ${frictionRate.toFixed(3)} inches w.g./100ft exceeds SMACNA maximum of ${standards.maximum} inches w.g./100ft`\r\n      );\r\n      result.compliant = false;\r\n      result.score -= 25;\r\n    } else if (frictionRate > standards.high) {\r\n      result.warnings.push(\r\n        `High friction rate ${frictionRate.toFixed(3)} inches w.g./100ft may cause excessive pressure loss`\r\n      );\r\n      result.score -= 15;\r\n    } else if (frictionRate < standards.low) {\r\n      result.recommendations.push(\r\n        'Low friction rate indicates oversized duct - consider optimization for cost savings'\r\n      );\r\n      result.score -= 5;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Validate aspect ratio for rectangular ducts\r\n   */\r\n  private static validateAspectRatio(data: CalculationData, result: ValidationResult): void {\r\n    const { aspectRatio } = data;\r\n    if (!aspectRatio) return;\r\n\r\n    const standards = this.SMACNA_STANDARDS.aspectRatio;\r\n\r\n    if (aspectRatio > standards.maximum) {\r\n      result.errors.push(\r\n        `Aspect ratio ${aspectRatio.toFixed(1)}:1 exceeds SMACNA maximum of ${standards.maximum}:1`\r\n      );\r\n      result.compliant = false;\r\n      result.score -= 20;\r\n      result.recommendations.push('Consider using round duct or reducing aspect ratio');\r\n    } else if (aspectRatio > standards.optimal) {\r\n      result.warnings.push(\r\n        `Aspect ratio ${aspectRatio.toFixed(1)}:1 is above optimal range for fabrication and performance`\r\n      );\r\n      result.score -= 10;\r\n      result.recommendations.push('Aspect ratios between 2:1 and 3:1 are optimal');\r\n    } else if (aspectRatio < standards.minimum + 0.5) {\r\n      result.recommendations.push(\r\n        'Very low aspect ratio may be inefficient - consider increasing for better material utilization'\r\n      );\r\n      result.score -= 3;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Validate duct area\r\n   */\r\n  private static validateArea(data: CalculationData, result: ValidationResult): void {\r\n    const { area } = data;\r\n\r\n    if (area < this.SMACNA_STANDARDS.minimumArea) {\r\n      result.warnings.push(\r\n        `Very small duct area ${area.toFixed(2)} sq ft. Consider minimum duct size requirements.`\r\n      );\r\n      result.score -= 10;\r\n    }\r\n\r\n    // Check for extremely large areas that might indicate calculation errors\r\n    if (area > 100) {\r\n      result.warnings.push(\r\n        `Very large duct area ${area.toFixed(2)} sq ft. Verify calculation inputs.`\r\n      );\r\n      result.score -= 5;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Validate construction and material considerations\r\n   */\r\n  private static validateConstruction(data: CalculationData, result: ValidationResult): void {\r\n    const { material, ductType, diameter, width, height } = data;\r\n\r\n    // Material-specific recommendations\r\n    if (material === 'fiberglass' && data.velocity > 2000) {\r\n      result.warnings.push('High velocity with fiberglass ductwork may cause erosion');\r\n      result.recommendations.push('Consider metallic ductwork for high-velocity applications');\r\n      result.score -= 10;\r\n    }\r\n\r\n    // Size-specific recommendations\r\n    if (ductType === 'round' && diameter && diameter < 6) {\r\n      result.recommendations.push('Small round ducts may be difficult to clean and maintain');\r\n    }\r\n\r\n    if (ductType === 'rectangular' && width && height) {\r\n      const minDimension = Math.min(width, height);\r\n      if (minDimension < 6) {\r\n        result.warnings.push('Small duct dimensions may restrict airflow and be difficult to clean');\r\n        result.score -= 5;\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Calculate overall SMACNA compliance score\r\n   */\r\n  private static calculateSMACNAScore(data: CalculationData, result: ValidationResult): number {\r\n    let score = 100;\r\n\r\n    // Deduct points for errors and warnings (already done in individual validations)\r\n    // Additional scoring based on optimization\r\n    const { velocity, application = 'supply' } = data;\r\n    const optimalVelocity = this.SMACNA_STANDARDS.velocity[application]?.optimal || 1500;\r\n\r\n    // Bonus points for being close to optimal velocity\r\n    const velocityDeviation = Math.abs(velocity - optimalVelocity) / optimalVelocity;\r\n    if (velocityDeviation < 0.1) {\r\n      score += 5; // Bonus for being very close to optimal\r\n    }\r\n\r\n    // Ensure score doesn't go below 0 or above 100\r\n    return Math.max(0, Math.min(100, score));\r\n  }\r\n\r\n  /**\r\n   * Get SMACNA standards reference data\r\n   */\r\n  public static getSMACNAStandards(): SMACNAStandards {\r\n    return JSON.parse(JSON.stringify(this.SMACNA_STANDARDS));\r\n  }\r\n\r\n  /**\r\n   * Get ASHRAE standards reference data\r\n   */\r\n  public static getASHRAEStandards(): ASHRAEStandards {\r\n    return JSON.parse(JSON.stringify(this.ASHRAE_STANDARDS));\r\n  }\r\n\r\n  /**\r\n   * Get NFPA standards reference data\r\n   */\r\n  public static getNFPAStandards(): NFPAStandards {\r\n    return JSON.parse(JSON.stringify(this.NFPA_STANDARDS));\r\n  }\r\n\r\n  /**\r\n   * Validate input data completeness\r\n   */\r\n  public static validateInputData(data: CalculationData): { isValid: boolean; errors: string[] } {\r\n    const errors: string[] = [];\r\n\r\n    if (!data.velocity || data.velocity <= 0) {\r\n      errors.push('Velocity must be greater than 0');\r\n    }\r\n\r\n    if (!data.airflow || data.airflow <= 0) {\r\n      errors.push('Airflow must be greater than 0');\r\n    }\r\n\r\n    if (!data.area || data.area <= 0) {\r\n      errors.push('Area must be greater than 0');\r\n    }\r\n\r\n    if (!['round', 'rectangular'].includes(data.ductType)) {\r\n      errors.push('Duct type must be \"round\" or \"rectangular\"');\r\n    }\r\n\r\n    if (data.ductType === 'rectangular') {\r\n      if (!data.width || data.width <= 0) {\r\n        errors.push('Width must be greater than 0 for rectangular ducts');\r\n      }\r\n      if (!data.height || data.height <= 0) {\r\n        errors.push('Height must be greater than 0 for rectangular ducts');\r\n      }\r\n    }\r\n\r\n    if (data.ductType === 'round') {\r\n      if (!data.diameter || data.diameter <= 0) {\r\n        errors.push('Diameter must be greater than 0 for round ducts');\r\n      }\r\n    }\r\n\r\n    return {\r\n      isValid: errors.length === 0,\r\n      errors\r\n    };\r\n  }\r\n}\r\n\r\nexport default SMACNAValidator;\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "0f04be0a49d01d9c9109a1850f28d2439b72fd1c"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_24qkon3nhs = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_24qkon3nhs();
cov_24qkon3nhs().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_24qkon3nhs().s[1]++;
exports.SMACNAValidator = void 0;
/**
 * SMACNAValidator - Pure validation functions for HVAC standards
 * CRITICAL: No dependencies on UI, storage, or external services
 */
class SMACNAValidator {
  /**
   * Validate calculation against SMACNA standards
   * CRITICAL: Pure function with comprehensive validation
   */
  static validateSMACNACompliance(data) {
    /* istanbul ignore next */
    cov_24qkon3nhs().f[0]++;
    const result =
    /* istanbul ignore next */
    (cov_24qkon3nhs().s[2]++, {
      isValid: true,
      compliant: true,
      errors: [],
      warnings: [],
      recommendations: [],
      standardReference: 'SMACNA HVAC Duct Construction Standards 2012',
      score: 100
    });
    /* istanbul ignore next */
    cov_24qkon3nhs().s[3]++;
    try {
      /* istanbul ignore next */
      cov_24qkon3nhs().s[4]++;
      // Velocity validation
      this.validateVelocity(data, result);
      // Friction rate validation
      /* istanbul ignore next */
      cov_24qkon3nhs().s[5]++;
      this.validateFrictionRate(data, result);
      // Aspect ratio validation (for rectangular ducts)
      /* istanbul ignore next */
      cov_24qkon3nhs().s[6]++;
      if (
      /* istanbul ignore next */
      (cov_24qkon3nhs().b[1][0]++, data.ductType === 'rectangular') &&
      /* istanbul ignore next */
      (cov_24qkon3nhs().b[1][1]++, data.aspectRatio)) {
        /* istanbul ignore next */
        cov_24qkon3nhs().b[0][0]++;
        cov_24qkon3nhs().s[7]++;
        this.validateAspectRatio(data, result);
      } else
      /* istanbul ignore next */
      {
        cov_24qkon3nhs().b[0][1]++;
      }
      // Area validation
      cov_24qkon3nhs().s[8]++;
      this.validateArea(data, result);
      // Material and construction validation
      /* istanbul ignore next */
      cov_24qkon3nhs().s[9]++;
      this.validateConstruction(data, result);
      // Calculate overall score
      /* istanbul ignore next */
      cov_24qkon3nhs().s[10]++;
      result.score = this.calculateSMACNAScore(data, result);
    } catch (error) {
      /* istanbul ignore next */
      cov_24qkon3nhs().s[11]++;
      result.errors.push(`SMACNA validation error: ${error.message}`);
      /* istanbul ignore next */
      cov_24qkon3nhs().s[12]++;
      result.isValid = false;
      /* istanbul ignore next */
      cov_24qkon3nhs().s[13]++;
      result.compliant = false;
      /* istanbul ignore next */
      cov_24qkon3nhs().s[14]++;
      result.score = 0;
    }
    /* istanbul ignore next */
    cov_24qkon3nhs().s[15]++;
    return result;
  }
  /**
   * Validate calculation against ASHRAE standards
   */
  static validateASHRAECompliance(data) {
    /* istanbul ignore next */
    cov_24qkon3nhs().f[1]++;
    const result =
    /* istanbul ignore next */
    (cov_24qkon3nhs().s[16]++, {
      isValid: true,
      compliant: true,
      errors: [],
      warnings: [],
      recommendations: [],
      standardReference: 'ASHRAE Fundamentals 2021 Chapter 21',
      score: 100
    });
    /* istanbul ignore next */
    cov_24qkon3nhs().s[17]++;
    try {
      const {
        velocity,
        location =
        /* istanbul ignore next */
        (cov_24qkon3nhs().b[2][0]++, 'unoccupied')
      } =
      /* istanbul ignore next */
      (cov_24qkon3nhs().s[18]++, data);
      const standards =
      /* istanbul ignore next */
      (cov_24qkon3nhs().s[19]++, this.ASHRAE_STANDARDS);
      // Comfort velocity validation
      /* istanbul ignore next */
      cov_24qkon3nhs().s[20]++;
      if (location === 'occupied') {
        /* istanbul ignore next */
        cov_24qkon3nhs().b[3][0]++;
        cov_24qkon3nhs().s[21]++;
        if (velocity > standards.comfortVelocity.occupiedZone) {
          /* istanbul ignore next */
          cov_24qkon3nhs().b[4][0]++;
          cov_24qkon3nhs().s[22]++;
          result.warnings.push(`Velocity ${velocity} FPM in occupied zone exceeds ASHRAE comfort limit of ${standards.comfortVelocity.occupiedZone} FPM`);
          /* istanbul ignore next */
          cov_24qkon3nhs().s[23]++;
          result.score -= 20;
        } else
        /* istanbul ignore next */
        {
          cov_24qkon3nhs().b[4][1]++;
        }
      } else {
        /* istanbul ignore next */
        cov_24qkon3nhs().b[3][1]++;
        cov_24qkon3nhs().s[24]++;
        if (velocity > standards.comfortVelocity.unoccupiedZone) {
          /* istanbul ignore next */
          cov_24qkon3nhs().b[5][0]++;
          cov_24qkon3nhs().s[25]++;
          result.warnings.push(`Velocity ${velocity} FPM exceeds ASHRAE general limit of ${standards.comfortVelocity.unoccupiedZone} FPM`);
          /* istanbul ignore next */
          cov_24qkon3nhs().s[26]++;
          result.score -= 10;
        } else
        /* istanbul ignore next */
        {
          cov_24qkon3nhs().b[5][1]++;
        }
      }
      // Noise velocity validation
      /* istanbul ignore next */
      cov_24qkon3nhs().s[27]++;
      if (velocity > standards.noiseVelocity.loud) {
        /* istanbul ignore next */
        cov_24qkon3nhs().b[6][0]++;
        cov_24qkon3nhs().s[28]++;
        result.warnings.push(`High velocity ${velocity} FPM may cause excessive noise`);
        /* istanbul ignore next */
        cov_24qkon3nhs().s[29]++;
        result.recommendations.push('Consider noise attenuation measures');
        /* istanbul ignore next */
        cov_24qkon3nhs().s[30]++;
        result.score -= 15;
      } else {
        /* istanbul ignore next */
        cov_24qkon3nhs().b[6][1]++;
        cov_24qkon3nhs().s[31]++;
        if (velocity > standards.noiseVelocity.moderate) {
          /* istanbul ignore next */
          cov_24qkon3nhs().b[7][0]++;
          cov_24qkon3nhs().s[32]++;
          result.recommendations.push('Monitor noise levels in occupied spaces');
          /* istanbul ignore next */
          cov_24qkon3nhs().s[33]++;
          result.score -= 5;
        } else
        /* istanbul ignore next */
        {
          cov_24qkon3nhs().b[7][1]++;
        }
      }
    } catch (error) {
      /* istanbul ignore next */
      cov_24qkon3nhs().s[34]++;
      result.errors.push(`ASHRAE validation error: ${error.message}`);
      /* istanbul ignore next */
      cov_24qkon3nhs().s[35]++;
      result.isValid = false;
      /* istanbul ignore next */
      cov_24qkon3nhs().s[36]++;
      result.compliant = false;
      /* istanbul ignore next */
      cov_24qkon3nhs().s[37]++;
      result.score = 0;
    }
    /* istanbul ignore next */
    cov_24qkon3nhs().s[38]++;
    return result;
  }
  /**
   * Validate calculation against NFPA 96 standards (for grease ducts)
   */
  static validateNFPACompliance(data) {
    /* istanbul ignore next */
    cov_24qkon3nhs().f[2]++;
    const result =
    /* istanbul ignore next */
    (cov_24qkon3nhs().s[39]++, {
      isValid: true,
      compliant: true,
      errors: [],
      warnings: [],
      recommendations: [],
      standardReference: 'NFPA 96 Standard for Ventilation Control and Fire Protection 2021',
      score: 100
    });
    /* istanbul ignore next */
    cov_24qkon3nhs().s[40]++;
    try {
      /* istanbul ignore next */
      cov_24qkon3nhs().s[41]++;
      if (data.application !== 'grease') {
        /* istanbul ignore next */
        cov_24qkon3nhs().b[8][0]++;
        cov_24qkon3nhs().s[42]++;
        result.warnings.push('NFPA 96 validation only applies to grease exhaust systems');
        /* istanbul ignore next */
        cov_24qkon3nhs().s[43]++;
        return result;
      } else
      /* istanbul ignore next */
      {
        cov_24qkon3nhs().b[8][1]++;
      }
      const {
        velocity,
        pressure =
        /* istanbul ignore next */
        (cov_24qkon3nhs().b[9][0]++, 0)
      } =
      /* istanbul ignore next */
      (cov_24qkon3nhs().s[44]++, data);
      const standards =
      /* istanbul ignore next */
      (cov_24qkon3nhs().s[45]++, this.NFPA_STANDARDS);
      // Velocity validation for grease removal
      /* istanbul ignore next */
      cov_24qkon3nhs().s[46]++;
      if (velocity < standards.greaseVelocity.minimum) {
        /* istanbul ignore next */
        cov_24qkon3nhs().b[10][0]++;
        cov_24qkon3nhs().s[47]++;
        result.errors.push(`Velocity ${velocity} FPM is below NFPA 96 minimum of ${standards.greaseVelocity.minimum} FPM for grease removal`);
        /* istanbul ignore next */
        cov_24qkon3nhs().s[48]++;
        result.compliant = false;
        /* istanbul ignore next */
        cov_24qkon3nhs().s[49]++;
        result.score -= 50;
      } else {
        /* istanbul ignore next */
        cov_24qkon3nhs().b[10][1]++;
        cov_24qkon3nhs().s[50]++;
        if (velocity < standards.greaseVelocity.recommended) {
          /* istanbul ignore next */
          cov_24qkon3nhs().b[11][0]++;
          cov_24qkon3nhs().s[51]++;
          result.warnings.push(`Velocity ${velocity} FPM is below NFPA 96 recommended minimum of ${standards.greaseVelocity.recommended} FPM`);
          /* istanbul ignore next */
          cov_24qkon3nhs().s[52]++;
          result.score -= 20;
        } else
        /* istanbul ignore next */
        {
          cov_24qkon3nhs().b[11][1]++;
        }
      }
      // Pressure validation
      /* istanbul ignore next */
      cov_24qkon3nhs().s[53]++;
      if (pressure > standards.greasePressure.maximum) {
        /* istanbul ignore next */
        cov_24qkon3nhs().b[12][0]++;
        cov_24qkon3nhs().s[54]++;
        result.warnings.push(`Static pressure ${pressure} inches w.g. exceeds NFPA 96 recommended maximum of ${standards.greasePressure.maximum} inches w.g.`);
        /* istanbul ignore next */
        cov_24qkon3nhs().s[55]++;
        result.score -= 15;
      } else
      /* istanbul ignore next */
      {
        cov_24qkon3nhs().b[12][1]++;
      }
      // Additional grease duct requirements
      cov_24qkon3nhs().s[56]++;
      if (data.ductType !== 'round') {
        /* istanbul ignore next */
        cov_24qkon3nhs().b[13][0]++;
        cov_24qkon3nhs().s[57]++;
        result.warnings.push('NFPA 96 recommends round ducts for grease exhaust systems');
        /* istanbul ignore next */
        cov_24qkon3nhs().s[58]++;
        result.recommendations.push('Consider using round duct for easier cleaning and maintenance');
        /* istanbul ignore next */
        cov_24qkon3nhs().s[59]++;
        result.score -= 10;
      } else
      /* istanbul ignore next */
      {
        cov_24qkon3nhs().b[13][1]++;
      }
    } catch (error) {
      /* istanbul ignore next */
      cov_24qkon3nhs().s[60]++;
      result.errors.push(`NFPA validation error: ${error.message}`);
      /* istanbul ignore next */
      cov_24qkon3nhs().s[61]++;
      result.isValid = false;
      /* istanbul ignore next */
      cov_24qkon3nhs().s[62]++;
      result.compliant = false;
      /* istanbul ignore next */
      cov_24qkon3nhs().s[63]++;
      result.score = 0;
    }
    /* istanbul ignore next */
    cov_24qkon3nhs().s[64]++;
    return result;
  }
  /**
   * Validate against all applicable standards
   */
  static validateAllStandards(data) {
    /* istanbul ignore next */
    cov_24qkon3nhs().f[3]++;
    const results =
    /* istanbul ignore next */
    (cov_24qkon3nhs().s[65]++, {});
    // Always validate SMACNA for general ductwork
    /* istanbul ignore next */
    cov_24qkon3nhs().s[66]++;
    results.smacna = this.validateSMACNACompliance(data);
    // Validate ASHRAE for comfort considerations
    /* istanbul ignore next */
    cov_24qkon3nhs().s[67]++;
    results.ashrae = this.validateASHRAECompliance(data);
    // Validate NFPA if it's a grease duct
    /* istanbul ignore next */
    cov_24qkon3nhs().s[68]++;
    if (data.application === 'grease') {
      /* istanbul ignore next */
      cov_24qkon3nhs().b[14][0]++;
      cov_24qkon3nhs().s[69]++;
      results.nfpa = this.validateNFPACompliance(data);
    } else
    /* istanbul ignore next */
    {
      cov_24qkon3nhs().b[14][1]++;
    }
    cov_24qkon3nhs().s[70]++;
    return results;
  }
  /**
   * Validate velocity against SMACNA standards
   */
  static validateVelocity(data, result) {
    /* istanbul ignore next */
    cov_24qkon3nhs().f[4]++;
    const {
      velocity,
      application =
      /* istanbul ignore next */
      (cov_24qkon3nhs().b[15][0]++, 'supply')
    } =
    /* istanbul ignore next */
    (cov_24qkon3nhs().s[71]++, data);
    const limits =
    /* istanbul ignore next */
    (cov_24qkon3nhs().s[72]++,
    /* istanbul ignore next */
    (cov_24qkon3nhs().b[16][0]++, this.SMACNA_STANDARDS.velocity[application]) ||
    /* istanbul ignore next */
    (cov_24qkon3nhs().b[16][1]++, this.SMACNA_STANDARDS.velocity.supply));
    /* istanbul ignore next */
    cov_24qkon3nhs().s[73]++;
    if (velocity < limits.min) {
      /* istanbul ignore next */
      cov_24qkon3nhs().b[17][0]++;
      cov_24qkon3nhs().s[74]++;
      result.warnings.push(`Velocity ${velocity} FPM is below SMACNA minimum of ${limits.min} FPM for ${application} duct`);
      /* istanbul ignore next */
      cov_24qkon3nhs().s[75]++;
      result.score -= 15;
    } else {
      /* istanbul ignore next */
      cov_24qkon3nhs().b[17][1]++;
      cov_24qkon3nhs().s[76]++;
      if (velocity > limits.max) {
        /* istanbul ignore next */
        cov_24qkon3nhs().b[18][0]++;
        cov_24qkon3nhs().s[77]++;
        result.errors.push(`Velocity ${velocity} FPM exceeds SMACNA maximum of ${limits.max} FPM for ${application} duct`);
        /* istanbul ignore next */
        cov_24qkon3nhs().s[78]++;
        result.compliant = false;
        /* istanbul ignore next */
        cov_24qkon3nhs().s[79]++;
        result.score -= 30;
      } else {
        /* istanbul ignore next */
        cov_24qkon3nhs().b[18][1]++;
        cov_24qkon3nhs().s[80]++;
        if (velocity > limits.optimal * 1.2) {
          /* istanbul ignore next */
          cov_24qkon3nhs().b[19][0]++;
          cov_24qkon3nhs().s[81]++;
          result.warnings.push(`Velocity ${velocity} FPM is above optimal range (${limits.optimal} FPM ±20%) for ${application} duct`);
          /* istanbul ignore next */
          cov_24qkon3nhs().s[82]++;
          result.score -= 10;
        } else {
          /* istanbul ignore next */
          cov_24qkon3nhs().b[19][1]++;
          cov_24qkon3nhs().s[83]++;
          if (velocity < limits.optimal * 0.8) {
            /* istanbul ignore next */
            cov_24qkon3nhs().b[20][0]++;
            cov_24qkon3nhs().s[84]++;
            result.recommendations.push(`Consider reducing duct size to achieve optimal velocity of ${limits.optimal} FPM`);
            /* istanbul ignore next */
            cov_24qkon3nhs().s[85]++;
            result.score -= 5;
          } else
          /* istanbul ignore next */
          {
            cov_24qkon3nhs().b[20][1]++;
          }
        }
      }
    }
  }
  /**
   * Validate friction rate against SMACNA standards
   */
  static validateFrictionRate(data, result) {
    /* istanbul ignore next */
    cov_24qkon3nhs().f[5]++;
    const {
      frictionRate
    } =
    /* istanbul ignore next */
    (cov_24qkon3nhs().s[86]++, data);
    const standards =
    /* istanbul ignore next */
    (cov_24qkon3nhs().s[87]++, this.SMACNA_STANDARDS.friction);
    /* istanbul ignore next */
    cov_24qkon3nhs().s[88]++;
    if (frictionRate > standards.maximum) {
      /* istanbul ignore next */
      cov_24qkon3nhs().b[21][0]++;
      cov_24qkon3nhs().s[89]++;
      result.errors.push(`Friction rate ${frictionRate.toFixed(3)} inches w.g./100ft exceeds SMACNA maximum of ${standards.maximum} inches w.g./100ft`);
      /* istanbul ignore next */
      cov_24qkon3nhs().s[90]++;
      result.compliant = false;
      /* istanbul ignore next */
      cov_24qkon3nhs().s[91]++;
      result.score -= 25;
    } else {
      /* istanbul ignore next */
      cov_24qkon3nhs().b[21][1]++;
      cov_24qkon3nhs().s[92]++;
      if (frictionRate > standards.high) {
        /* istanbul ignore next */
        cov_24qkon3nhs().b[22][0]++;
        cov_24qkon3nhs().s[93]++;
        result.warnings.push(`High friction rate ${frictionRate.toFixed(3)} inches w.g./100ft may cause excessive pressure loss`);
        /* istanbul ignore next */
        cov_24qkon3nhs().s[94]++;
        result.score -= 15;
      } else {
        /* istanbul ignore next */
        cov_24qkon3nhs().b[22][1]++;
        cov_24qkon3nhs().s[95]++;
        if (frictionRate < standards.low) {
          /* istanbul ignore next */
          cov_24qkon3nhs().b[23][0]++;
          cov_24qkon3nhs().s[96]++;
          result.recommendations.push('Low friction rate indicates oversized duct - consider optimization for cost savings');
          /* istanbul ignore next */
          cov_24qkon3nhs().s[97]++;
          result.score -= 5;
        } else
        /* istanbul ignore next */
        {
          cov_24qkon3nhs().b[23][1]++;
        }
      }
    }
  }
  /**
   * Validate aspect ratio for rectangular ducts
   */
  static validateAspectRatio(data, result) {
    /* istanbul ignore next */
    cov_24qkon3nhs().f[6]++;
    const {
      aspectRatio
    } =
    /* istanbul ignore next */
    (cov_24qkon3nhs().s[98]++, data);
    /* istanbul ignore next */
    cov_24qkon3nhs().s[99]++;
    if (!aspectRatio) {
      /* istanbul ignore next */
      cov_24qkon3nhs().b[24][0]++;
      cov_24qkon3nhs().s[100]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_24qkon3nhs().b[24][1]++;
    }
    const standards =
    /* istanbul ignore next */
    (cov_24qkon3nhs().s[101]++, this.SMACNA_STANDARDS.aspectRatio);
    /* istanbul ignore next */
    cov_24qkon3nhs().s[102]++;
    if (aspectRatio > standards.maximum) {
      /* istanbul ignore next */
      cov_24qkon3nhs().b[25][0]++;
      cov_24qkon3nhs().s[103]++;
      result.errors.push(`Aspect ratio ${aspectRatio.toFixed(1)}:1 exceeds SMACNA maximum of ${standards.maximum}:1`);
      /* istanbul ignore next */
      cov_24qkon3nhs().s[104]++;
      result.compliant = false;
      /* istanbul ignore next */
      cov_24qkon3nhs().s[105]++;
      result.score -= 20;
      /* istanbul ignore next */
      cov_24qkon3nhs().s[106]++;
      result.recommendations.push('Consider using round duct or reducing aspect ratio');
    } else {
      /* istanbul ignore next */
      cov_24qkon3nhs().b[25][1]++;
      cov_24qkon3nhs().s[107]++;
      if (aspectRatio > standards.optimal) {
        /* istanbul ignore next */
        cov_24qkon3nhs().b[26][0]++;
        cov_24qkon3nhs().s[108]++;
        result.warnings.push(`Aspect ratio ${aspectRatio.toFixed(1)}:1 is above optimal range for fabrication and performance`);
        /* istanbul ignore next */
        cov_24qkon3nhs().s[109]++;
        result.score -= 10;
        /* istanbul ignore next */
        cov_24qkon3nhs().s[110]++;
        result.recommendations.push('Aspect ratios between 2:1 and 3:1 are optimal');
      } else {
        /* istanbul ignore next */
        cov_24qkon3nhs().b[26][1]++;
        cov_24qkon3nhs().s[111]++;
        if (aspectRatio < standards.minimum + 0.5) {
          /* istanbul ignore next */
          cov_24qkon3nhs().b[27][0]++;
          cov_24qkon3nhs().s[112]++;
          result.recommendations.push('Very low aspect ratio may be inefficient - consider increasing for better material utilization');
          /* istanbul ignore next */
          cov_24qkon3nhs().s[113]++;
          result.score -= 3;
        } else
        /* istanbul ignore next */
        {
          cov_24qkon3nhs().b[27][1]++;
        }
      }
    }
  }
  /**
   * Validate duct area
   */
  static validateArea(data, result) {
    /* istanbul ignore next */
    cov_24qkon3nhs().f[7]++;
    const {
      area
    } =
    /* istanbul ignore next */
    (cov_24qkon3nhs().s[114]++, data);
    /* istanbul ignore next */
    cov_24qkon3nhs().s[115]++;
    if (area < this.SMACNA_STANDARDS.minimumArea) {
      /* istanbul ignore next */
      cov_24qkon3nhs().b[28][0]++;
      cov_24qkon3nhs().s[116]++;
      result.warnings.push(`Very small duct area ${area.toFixed(2)} sq ft. Consider minimum duct size requirements.`);
      /* istanbul ignore next */
      cov_24qkon3nhs().s[117]++;
      result.score -= 10;
    } else
    /* istanbul ignore next */
    {
      cov_24qkon3nhs().b[28][1]++;
    }
    // Check for extremely large areas that might indicate calculation errors
    cov_24qkon3nhs().s[118]++;
    if (area > 100) {
      /* istanbul ignore next */
      cov_24qkon3nhs().b[29][0]++;
      cov_24qkon3nhs().s[119]++;
      result.warnings.push(`Very large duct area ${area.toFixed(2)} sq ft. Verify calculation inputs.`);
      /* istanbul ignore next */
      cov_24qkon3nhs().s[120]++;
      result.score -= 5;
    } else
    /* istanbul ignore next */
    {
      cov_24qkon3nhs().b[29][1]++;
    }
  }
  /**
   * Validate construction and material considerations
   */
  static validateConstruction(data, result) {
    /* istanbul ignore next */
    cov_24qkon3nhs().f[8]++;
    const {
      material,
      ductType,
      diameter,
      width,
      height
    } =
    /* istanbul ignore next */
    (cov_24qkon3nhs().s[121]++, data);
    // Material-specific recommendations
    /* istanbul ignore next */
    cov_24qkon3nhs().s[122]++;
    if (
    /* istanbul ignore next */
    (cov_24qkon3nhs().b[31][0]++, material === 'fiberglass') &&
    /* istanbul ignore next */
    (cov_24qkon3nhs().b[31][1]++, data.velocity > 2000)) {
      /* istanbul ignore next */
      cov_24qkon3nhs().b[30][0]++;
      cov_24qkon3nhs().s[123]++;
      result.warnings.push('High velocity with fiberglass ductwork may cause erosion');
      /* istanbul ignore next */
      cov_24qkon3nhs().s[124]++;
      result.recommendations.push('Consider metallic ductwork for high-velocity applications');
      /* istanbul ignore next */
      cov_24qkon3nhs().s[125]++;
      result.score -= 10;
    } else
    /* istanbul ignore next */
    {
      cov_24qkon3nhs().b[30][1]++;
    }
    // Size-specific recommendations
    cov_24qkon3nhs().s[126]++;
    if (
    /* istanbul ignore next */
    (cov_24qkon3nhs().b[33][0]++, ductType === 'round') &&
    /* istanbul ignore next */
    (cov_24qkon3nhs().b[33][1]++, diameter) &&
    /* istanbul ignore next */
    (cov_24qkon3nhs().b[33][2]++, diameter < 6)) {
      /* istanbul ignore next */
      cov_24qkon3nhs().b[32][0]++;
      cov_24qkon3nhs().s[127]++;
      result.recommendations.push('Small round ducts may be difficult to clean and maintain');
    } else
    /* istanbul ignore next */
    {
      cov_24qkon3nhs().b[32][1]++;
    }
    cov_24qkon3nhs().s[128]++;
    if (
    /* istanbul ignore next */
    (cov_24qkon3nhs().b[35][0]++, ductType === 'rectangular') &&
    /* istanbul ignore next */
    (cov_24qkon3nhs().b[35][1]++, width) &&
    /* istanbul ignore next */
    (cov_24qkon3nhs().b[35][2]++, height)) {
      /* istanbul ignore next */
      cov_24qkon3nhs().b[34][0]++;
      const minDimension =
      /* istanbul ignore next */
      (cov_24qkon3nhs().s[129]++, Math.min(width, height));
      /* istanbul ignore next */
      cov_24qkon3nhs().s[130]++;
      if (minDimension < 6) {
        /* istanbul ignore next */
        cov_24qkon3nhs().b[36][0]++;
        cov_24qkon3nhs().s[131]++;
        result.warnings.push('Small duct dimensions may restrict airflow and be difficult to clean');
        /* istanbul ignore next */
        cov_24qkon3nhs().s[132]++;
        result.score -= 5;
      } else
      /* istanbul ignore next */
      {
        cov_24qkon3nhs().b[36][1]++;
      }
    } else
    /* istanbul ignore next */
    {
      cov_24qkon3nhs().b[34][1]++;
    }
  }
  /**
   * Calculate overall SMACNA compliance score
   */
  static calculateSMACNAScore(data, result) {
    /* istanbul ignore next */
    cov_24qkon3nhs().f[9]++;
    let score =
    /* istanbul ignore next */
    (cov_24qkon3nhs().s[133]++, 100);
    // Deduct points for errors and warnings (already done in individual validations)
    // Additional scoring based on optimization
    const {
      velocity,
      application =
      /* istanbul ignore next */
      (cov_24qkon3nhs().b[37][0]++, 'supply')
    } =
    /* istanbul ignore next */
    (cov_24qkon3nhs().s[134]++, data);
    const optimalVelocity =
    /* istanbul ignore next */
    (cov_24qkon3nhs().s[135]++,
    /* istanbul ignore next */
    (cov_24qkon3nhs().b[38][0]++, this.SMACNA_STANDARDS.velocity[application]?.optimal) ||
    /* istanbul ignore next */
    (cov_24qkon3nhs().b[38][1]++, 1500));
    // Bonus points for being close to optimal velocity
    const velocityDeviation =
    /* istanbul ignore next */
    (cov_24qkon3nhs().s[136]++, Math.abs(velocity - optimalVelocity) / optimalVelocity);
    /* istanbul ignore next */
    cov_24qkon3nhs().s[137]++;
    if (velocityDeviation < 0.1) {
      /* istanbul ignore next */
      cov_24qkon3nhs().b[39][0]++;
      cov_24qkon3nhs().s[138]++;
      score += 5; // Bonus for being very close to optimal
    } else
    /* istanbul ignore next */
    {
      cov_24qkon3nhs().b[39][1]++;
    }
    // Ensure score doesn't go below 0 or above 100
    cov_24qkon3nhs().s[139]++;
    return Math.max(0, Math.min(100, score));
  }
  /**
   * Get SMACNA standards reference data
   */
  static getSMACNAStandards() {
    /* istanbul ignore next */
    cov_24qkon3nhs().f[10]++;
    cov_24qkon3nhs().s[140]++;
    return JSON.parse(JSON.stringify(this.SMACNA_STANDARDS));
  }
  /**
   * Get ASHRAE standards reference data
   */
  static getASHRAEStandards() {
    /* istanbul ignore next */
    cov_24qkon3nhs().f[11]++;
    cov_24qkon3nhs().s[141]++;
    return JSON.parse(JSON.stringify(this.ASHRAE_STANDARDS));
  }
  /**
   * Get NFPA standards reference data
   */
  static getNFPAStandards() {
    /* istanbul ignore next */
    cov_24qkon3nhs().f[12]++;
    cov_24qkon3nhs().s[142]++;
    return JSON.parse(JSON.stringify(this.NFPA_STANDARDS));
  }
  /**
   * Validate input data completeness
   */
  static validateInputData(data) {
    /* istanbul ignore next */
    cov_24qkon3nhs().f[13]++;
    const errors =
    /* istanbul ignore next */
    (cov_24qkon3nhs().s[143]++, []);
    /* istanbul ignore next */
    cov_24qkon3nhs().s[144]++;
    if (
    /* istanbul ignore next */
    (cov_24qkon3nhs().b[41][0]++, !data.velocity) ||
    /* istanbul ignore next */
    (cov_24qkon3nhs().b[41][1]++, data.velocity <= 0)) {
      /* istanbul ignore next */
      cov_24qkon3nhs().b[40][0]++;
      cov_24qkon3nhs().s[145]++;
      errors.push('Velocity must be greater than 0');
    } else
    /* istanbul ignore next */
    {
      cov_24qkon3nhs().b[40][1]++;
    }
    cov_24qkon3nhs().s[146]++;
    if (
    /* istanbul ignore next */
    (cov_24qkon3nhs().b[43][0]++, !data.airflow) ||
    /* istanbul ignore next */
    (cov_24qkon3nhs().b[43][1]++, data.airflow <= 0)) {
      /* istanbul ignore next */
      cov_24qkon3nhs().b[42][0]++;
      cov_24qkon3nhs().s[147]++;
      errors.push('Airflow must be greater than 0');
    } else
    /* istanbul ignore next */
    {
      cov_24qkon3nhs().b[42][1]++;
    }
    cov_24qkon3nhs().s[148]++;
    if (
    /* istanbul ignore next */
    (cov_24qkon3nhs().b[45][0]++, !data.area) ||
    /* istanbul ignore next */
    (cov_24qkon3nhs().b[45][1]++, data.area <= 0)) {
      /* istanbul ignore next */
      cov_24qkon3nhs().b[44][0]++;
      cov_24qkon3nhs().s[149]++;
      errors.push('Area must be greater than 0');
    } else
    /* istanbul ignore next */
    {
      cov_24qkon3nhs().b[44][1]++;
    }
    cov_24qkon3nhs().s[150]++;
    if (!['round', 'rectangular'].includes(data.ductType)) {
      /* istanbul ignore next */
      cov_24qkon3nhs().b[46][0]++;
      cov_24qkon3nhs().s[151]++;
      errors.push('Duct type must be "round" or "rectangular"');
    } else
    /* istanbul ignore next */
    {
      cov_24qkon3nhs().b[46][1]++;
    }
    cov_24qkon3nhs().s[152]++;
    if (data.ductType === 'rectangular') {
      /* istanbul ignore next */
      cov_24qkon3nhs().b[47][0]++;
      cov_24qkon3nhs().s[153]++;
      if (
      /* istanbul ignore next */
      (cov_24qkon3nhs().b[49][0]++, !data.width) ||
      /* istanbul ignore next */
      (cov_24qkon3nhs().b[49][1]++, data.width <= 0)) {
        /* istanbul ignore next */
        cov_24qkon3nhs().b[48][0]++;
        cov_24qkon3nhs().s[154]++;
        errors.push('Width must be greater than 0 for rectangular ducts');
      } else
      /* istanbul ignore next */
      {
        cov_24qkon3nhs().b[48][1]++;
      }
      cov_24qkon3nhs().s[155]++;
      if (
      /* istanbul ignore next */
      (cov_24qkon3nhs().b[51][0]++, !data.height) ||
      /* istanbul ignore next */
      (cov_24qkon3nhs().b[51][1]++, data.height <= 0)) {
        /* istanbul ignore next */
        cov_24qkon3nhs().b[50][0]++;
        cov_24qkon3nhs().s[156]++;
        errors.push('Height must be greater than 0 for rectangular ducts');
      } else
      /* istanbul ignore next */
      {
        cov_24qkon3nhs().b[50][1]++;
      }
    } else
    /* istanbul ignore next */
    {
      cov_24qkon3nhs().b[47][1]++;
    }
    cov_24qkon3nhs().s[157]++;
    if (data.ductType === 'round') {
      /* istanbul ignore next */
      cov_24qkon3nhs().b[52][0]++;
      cov_24qkon3nhs().s[158]++;
      if (
      /* istanbul ignore next */
      (cov_24qkon3nhs().b[54][0]++, !data.diameter) ||
      /* istanbul ignore next */
      (cov_24qkon3nhs().b[54][1]++, data.diameter <= 0)) {
        /* istanbul ignore next */
        cov_24qkon3nhs().b[53][0]++;
        cov_24qkon3nhs().s[159]++;
        errors.push('Diameter must be greater than 0 for round ducts');
      } else
      /* istanbul ignore next */
      {
        cov_24qkon3nhs().b[53][1]++;
      }
    } else
    /* istanbul ignore next */
    {
      cov_24qkon3nhs().b[52][1]++;
    }
    cov_24qkon3nhs().s[160]++;
    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
/* istanbul ignore next */
cov_24qkon3nhs().s[161]++;
exports.SMACNAValidator = SMACNAValidator;
// SMACNA standards (2012 edition)
/* istanbul ignore next */
cov_24qkon3nhs().s[162]++;
SMACNAValidator.SMACNA_STANDARDS = {
  velocity: {
    supply: {
      min: 400,
      max: 2500,
      optimal: 1500
    },
    return: {
      min: 300,
      max: 2000,
      optimal: 1200
    },
    exhaust: {
      min: 500,
      max: 3000,
      optimal: 1800
    }
  },
  friction: {
    low: 0.05,
    // Low pressure systems
    medium: 0.08,
    // Medium pressure systems
    high: 0.12,
    // High pressure systems
    maximum: 0.20 // Maximum recommended
  },
  aspectRatio: {
    maximum: 4.0,
    // SMACNA maximum
    optimal: 2.5,
    // Optimal for fabrication
    minimum: 1.0 // Square duct
  },
  minimumArea: 0.1 // Minimum duct area (sq ft)
};
// ASHRAE standards (2021 Fundamentals)
/* istanbul ignore next */
cov_24qkon3nhs().s[163]++;
SMACNAValidator.ASHRAE_STANDARDS = {
  comfortVelocity: {
    occupiedZone: 750,
    // FPM for occupied spaces
    unoccupiedZone: 1500 // FPM for unoccupied spaces
  },
  noiseVelocity: {
    quiet: 1000,
    // Libraries, bedrooms
    moderate: 1500,
    // Offices, classrooms
    loud: 2000 // Factories, mechanical rooms
  }
};
// NFPA 96 standards (2021 edition)
/* istanbul ignore next */
cov_24qkon3nhs().s[164]++;
SMACNAValidator.NFPA_STANDARDS = {
  greaseVelocity: {
    minimum: 1500,
    // Minimum for grease removal
    recommended: 2000 // Recommended for effective cleaning
  },
  greasePressure: {
    maximum: 2.0 // Maximum static pressure
  }
};
/* istanbul ignore next */
cov_24qkon3nhs().s[165]++;
exports.default = SMACNAValidator;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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