{"version": 3, "names": ["cov_2jmlri0rtb", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "name", "decl", "loc", "branchMap", "type", "locations", "undefined", "s", "f", "b", "inputSourceMap", "file", "mappings", "names", "sources", "sourcesContent", "version", "_coverageSchema", "coverage", "actualCoverage", "SystemOptimizationTypes_1", "require", "SystemPressureCalculator_1", "GeneticAlgorithm_1", "SimulatedAnnealing_1", "ParticleSwarmOptimization_1", "GradientDescent_1", "MultiObjectiveOptimizationFramework_1", "SystemOptimizationEngine", "initialize", "geneticAlgorithm", "GeneticAlgorithm", "simulatedAnnealing", "SimulatedAnnealing", "particleSwarm", "ParticleSwarmOptimization", "gradientDescent", "GradientDescent", "console", "log", "VERSION", "error", "Error", "optimizeSystem", "problem", "algorithm", "OptimizationAlgorithm", "GENETIC_ALGORITHM", "startTime", "performance", "now", "validateOptimizationProblem", "optimizationId", "generateOptimizationId", "objectiveFunction", "createObjectiveFunction", "constraintFunctions", "createConstraintFunctions", "optimizer", "createOptimizer", "result", "runOptimization", "finalResult", "postProcessResults", "optimizationHistory", "set", "history", "performanceMetrics", "statistics", "createErrorResult", "optimizeMultiObjective", "NSGA_II", "objectives", "length", "id", "objectiveFunctions", "map", "obj", "constraints", "constraint", "createConstraintFunction", "moFramework", "MultiObjectiveOptimizationFramework", "populationSize", "maxGenerations", "crossoverRate", "mutationRate", "eliteSize", "constraintHandling", "penaltyCoefficient", "paretoSettings", "maxSolutions", "diversityThreshold", "convergenceThreshold", "hypervolume", "enabled", "referencePoint", "spacing", "targetSpacing", "diversityMaintenance", "archiveSize", "optimizeSystemBalance", "systemConfiguration", "targetFlowRates", "Date", "description", "variables", "createBalancingVariables", "createBalancingObjectives", "algorithmSettings", "parameters", "maxIterations", "parallelization", "convergenceCriteria", "toleranceValue", "stagnationLimit", "improvementThreshold", "outputRequirements", "includeHistory", "detailedAnalysis", "sensitivityAnalysis", "uncertaintyAnalysis", "visualizations", "reportFormat", "optimizeEnergyEfficiency", "operatingConditions", "createEnergyOptimizationVariables", "objective", "OptimizationObjective", "MINIMIZE_ENERGY_CONSUMPTION", "weight", "evaluationFunction", "createEnergyObjectiveFunction", "units", "MINIMIZE_TOTAL_COST", "createCostObjectiveFunction", "aggregationMethod", "PARTICLE_SWARM", "inertiaWeight", "accelerationCoefficients", "variable", "bounds", "minimum", "maximum", "totalWeight", "reduce", "sum", "Math", "abs", "warn", "for<PERSON>ach", "configuredSystem", "applyVariablesToSystem", "systemPerformance", "SystemPressureCalculator", "calculateEnhancedSystemPressure", "segments", "systemType", "designConditions", "calculationOptions", "includeElevation", "includeFittings", "roundResults", "totalObjectiveValue", "objectiveValue", "MINIMIZE_PRESSURE_LOSS", "totalPressureLoss", "calculateEnergyConsumption", "calculateTotalCost", "MINIMIZE_NOISE_LEVEL", "calculateNoiseLevel", "MAXIMIZE_EFFICIENCY", "calculateSystemEfficiency", "Number", "MAX_VALUE", "evaluateConstraint", "baseSystem", "JSON", "parse", "stringify", "applyVariableToSystem", "system", "applyDuctSizeVariable", "applyFittingTypeVariable", "applyMaterialTypeVariable", "applyDamperPositionVariable", "applyFanSpeedVariable", "fan<PERSON>ower", "totalFlow", "annualOperatingHours", "energyConsumption", "materialCost", "installationCost", "operatingCost", "segment", "calculateSegmentMaterialCost", "energyRate", "lifecycleYears", "totalCost", "totalNoiseLevel", "segmentNoise", "calculateSegmentNoiseLevel", "pow", "log10", "theoreticalMinimumPressure", "actualPressure", "efficiency", "min", "MULTI_OBJECTIVE_GA", "SIMULATED_ANNEALING", "GRADIENT_DESCENT", "targetFlows", "random", "toString", "substr", "problemId", "status", "OptimizationStatus", "ERROR", "bestSolution", "analysis", "recommendations", "warnings", "errors", "message", "exports", "MAX_ITERATIONS_DEFAULT", "CONVERGENCE_TOLERANCE_DEFAULT", "Map"], "sources": ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\SystemOptimizationEngine.ts"], "sourcesContent": ["/**\r\n * System Optimization Engine for Phase 3 Priority 2\r\n * \r\n * Main optimization engine providing comprehensive system optimization capabilities including:\r\n * - Multi-objective optimization with Pareto analysis\r\n * - Multiple optimization algorithms (GA, SA, PSO, GD)\r\n * - Constraint handling and validation\r\n * - Performance analysis and recommendations\r\n * - Integration with existing Phase 1/2/3 Priority 1 components\r\n * \r\n * @version 3.0.0\r\n * <AUTHOR> Suite Development Team\r\n */\r\n\r\nimport {\r\n  OptimizationProblem,\r\n  OptimizationResult,\r\n  OptimizationSolution,\r\n  OptimizationAlgorithm,\r\n  OptimizationObjective,\r\n  OptimizationStatus,\r\n  OptimizationVariable,\r\n  OptimizationConstraint,\r\n  SystemConfiguration,\r\n  MultiObjectiveFunction,\r\n  OptimizationStatistics,\r\n  OptimizationHistory,\r\n  ResultAnalysis,\r\n  ObjectiveFunctionType,\r\n  ConstraintFunctionType\r\n} from './types/SystemOptimizationTypes';\r\n\r\n// Import existing Phase 1/2/3 Priority 1 services\r\nimport { SystemPressureCalculator } from './SystemPressureCalculator';\r\nimport { FittingLossCalculator } from './FittingLossCalculator';\r\nimport { AdvancedFittingCalculator } from './AdvancedFittingCalculator';\r\nimport { AirPropertiesCalculator } from './AirPropertiesCalculator';\r\n\r\n// Import optimization algorithms\r\nimport { GeneticAlgorithm } from './algorithms/GeneticAlgorithm';\r\nimport { SimulatedAnnealing } from './algorithms/SimulatedAnnealing';\r\nimport { ParticleSwarmOptimization } from './algorithms/ParticleSwarmOptimization';\r\nimport { GradientDescent } from './algorithms/GradientDescent';\r\nimport { MultiObjectiveOptimizationFramework } from './MultiObjectiveOptimizationFramework';\r\n\r\n/**\r\n * Main system optimization engine providing comprehensive optimization capabilities\r\n * for HVAC duct systems with multi-objective optimization and constraint handling\r\n */\r\nexport class SystemOptimizationEngine {\r\n  private static readonly VERSION = '3.0.0';\r\n  private static readonly MAX_ITERATIONS_DEFAULT = 1000;\r\n  private static readonly CONVERGENCE_TOLERANCE_DEFAULT = 1e-6;\r\n  \r\n  // Algorithm instances\r\n  private static geneticAlgorithm: GeneticAlgorithm;\r\n  private static simulatedAnnealing: SimulatedAnnealing;\r\n  private static particleSwarm: ParticleSwarmOptimization;\r\n  private static gradientDescent: GradientDescent;\r\n  \r\n  // Performance tracking\r\n  private static optimizationHistory: Map<string, OptimizationHistory> = new Map();\r\n  private static performanceMetrics: Map<string, OptimizationStatistics> = new Map();\r\n\r\n  /**\r\n   * Initialize the optimization engine with algorithm instances\r\n   */\r\n  public static initialize(): void {\r\n    try {\r\n      this.geneticAlgorithm = new GeneticAlgorithm();\r\n      this.simulatedAnnealing = new SimulatedAnnealing();\r\n      this.particleSwarm = new ParticleSwarmOptimization();\r\n      this.gradientDescent = new GradientDescent();\r\n      \r\n      console.log(`SystemOptimizationEngine v${this.VERSION} initialized successfully`);\r\n    } catch (error) {\r\n      console.error('Failed to initialize SystemOptimizationEngine:', error);\r\n      throw new Error(`Optimization engine initialization failed: ${error}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Main optimization method - solves optimization problems with specified algorithms\r\n   */\r\n  public static async optimizeSystem(\r\n    problem: OptimizationProblem,\r\n    algorithm: OptimizationAlgorithm = OptimizationAlgorithm.GENETIC_ALGORITHM\r\n  ): Promise<OptimizationResult> {\r\n    const startTime = performance.now();\r\n    \r\n    try {\r\n      // Validate optimization problem\r\n      this.validateOptimizationProblem(problem);\r\n      \r\n      // Initialize optimization tracking\r\n      const optimizationId = this.generateOptimizationId(problem);\r\n      \r\n      // Create objective function\r\n      const objectiveFunction = this.createObjectiveFunction(problem);\r\n      \r\n      // Create constraint functions\r\n      const constraintFunctions = this.createConstraintFunctions(problem);\r\n      \r\n      // Select and configure optimization algorithm\r\n      const optimizer = this.createOptimizer(algorithm, problem);\r\n      \r\n      // Run optimization\r\n      const result = await this.runOptimization(\r\n        optimizer,\r\n        objectiveFunction,\r\n        constraintFunctions,\r\n        problem,\r\n        optimizationId\r\n      );\r\n      \r\n      // Post-process and analyze results\r\n      const finalResult = await this.postProcessResults(result, problem, startTime);\r\n      \r\n      // Store optimization history\r\n      this.optimizationHistory.set(optimizationId, finalResult.history);\r\n      this.performanceMetrics.set(optimizationId, finalResult.statistics);\r\n      \r\n      return finalResult;\r\n      \r\n    } catch (error) {\r\n      console.error('Optimization failed:', error);\r\n      return this.createErrorResult(problem, error as Error, startTime);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Multi-objective optimization with Pareto analysis\r\n   */\r\n  public static async optimizeMultiObjective(\r\n    problem: OptimizationProblem,\r\n    algorithm: OptimizationAlgorithm = OptimizationAlgorithm.NSGA_II\r\n  ): Promise<OptimizationResult> {\r\n\r\n    if (problem.objectives.objectives.length < 2) {\r\n      throw new Error('Multi-objective optimization requires at least 2 objectives');\r\n    }\r\n\r\n    try {\r\n      console.log(`Starting multi-objective optimization for problem: ${problem.id}`);\r\n\r\n      // Create objective functions\r\n      const objectiveFunctions = problem.objectives.objectives.map(obj =>\r\n        this.createObjectiveFunction(obj, problem)\r\n      );\r\n\r\n      // Create constraint functions\r\n      const constraintFunctions = problem.constraints.map(constraint =>\r\n        this.createConstraintFunction(constraint, problem)\r\n      );\r\n\r\n      // Use Multi-objective Optimization Framework\r\n      const moFramework = new MultiObjectiveOptimizationFramework({\r\n        algorithm: 'nsga2',\r\n        populationSize: 100,\r\n        maxGenerations: 100,\r\n        crossoverRate: 0.9,\r\n        mutationRate: 0.1,\r\n        eliteSize: 10,\r\n        constraintHandling: 'penalty',\r\n        penaltyCoefficient: 1000,\r\n        paretoSettings: {\r\n          maxSolutions: 100,\r\n          diversityThreshold: 0.01,\r\n          convergenceThreshold: 1e-6,\r\n          hypervolume: {\r\n            enabled: true,\r\n            referencePoint: []\r\n          },\r\n          spacing: {\r\n            enabled: true,\r\n            targetSpacing: 0.1\r\n          }\r\n        },\r\n        diversityMaintenance: true,\r\n        archiveSize: 200\r\n      });\r\n\r\n      const result = await moFramework.optimizeMultiObjective(problem, objectiveFunctions, constraintFunctions);\r\n\r\n      console.log(`Multi-objective optimization completed for problem: ${problem.id}`);\r\n      return result;\r\n\r\n    } catch (error) {\r\n      console.error('Multi-objective optimization failed:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Optimize system balance for multi-zone HVAC systems\r\n   */\r\n  public static async optimizeSystemBalance(\r\n    systemConfiguration: SystemConfiguration,\r\n    targetFlowRates: { [zoneId: string]: number },\r\n    constraints: OptimizationConstraint[]\r\n  ): Promise<OptimizationResult> {\r\n    \r\n    // Create optimization problem for system balancing\r\n    const problem: OptimizationProblem = {\r\n      id: `balance_${systemConfiguration.id}_${Date.now()}`,\r\n      name: `System Balance Optimization - ${systemConfiguration.name}`,\r\n      description: 'Optimize damper positions and system configuration for balanced airflow',\r\n      systemConfiguration,\r\n      variables: this.createBalancingVariables(systemConfiguration, targetFlowRates),\r\n      objectives: this.createBalancingObjectives(targetFlowRates),\r\n      constraints,\r\n      algorithmSettings: {\r\n        algorithm: OptimizationAlgorithm.GENETIC_ALGORITHM,\r\n        parameters: {\r\n          populationSize: 50,\r\n          maxIterations: 200,\r\n          crossoverRate: 0.8,\r\n          mutationRate: 0.1\r\n        },\r\n        parallelization: { enabled: true }\r\n      },\r\n      convergenceCriteria: {\r\n        maxIterations: 200,\r\n        toleranceValue: 0.01,\r\n        stagnationLimit: 20,\r\n        improvementThreshold: 0.001\r\n      },\r\n      outputRequirements: {\r\n        includeHistory: true,\r\n        detailedAnalysis: true,\r\n        sensitivityAnalysis: true,\r\n        uncertaintyAnalysis: false,\r\n        visualizations: [\r\n          { type: 'convergence', parameters: {} },\r\n          { type: 'variable_history', parameters: {} }\r\n        ],\r\n        reportFormat: 'json'\r\n      }\r\n    };\r\n    \r\n    return this.optimizeSystem(problem, OptimizationAlgorithm.GENETIC_ALGORITHM);\r\n  }\r\n\r\n  /**\r\n   * Optimize for minimum energy consumption\r\n   */\r\n  public static async optimizeEnergyEfficiency(\r\n    systemConfiguration: SystemConfiguration,\r\n    operatingConditions: any,\r\n    constraints: OptimizationConstraint[]\r\n  ): Promise<OptimizationResult> {\r\n    \r\n    const problem: OptimizationProblem = {\r\n      id: `energy_${systemConfiguration.id}_${Date.now()}`,\r\n      name: `Energy Efficiency Optimization - ${systemConfiguration.name}`,\r\n      description: 'Optimize system configuration for minimum energy consumption',\r\n      systemConfiguration,\r\n      variables: this.createEnergyOptimizationVariables(systemConfiguration),\r\n      objectives: {\r\n        objectives: [\r\n          {\r\n            id: 'energy_consumption',\r\n            objective: OptimizationObjective.MINIMIZE_ENERGY_CONSUMPTION,\r\n            weight: 0.7,\r\n            description: 'Minimize total system energy consumption',\r\n            evaluationFunction: this.createEnergyObjectiveFunction(operatingConditions),\r\n            units: 'kWh/year'\r\n          },\r\n          {\r\n            id: 'total_cost',\r\n            objective: OptimizationObjective.MINIMIZE_TOTAL_COST,\r\n            weight: 0.3,\r\n            description: 'Minimize total system cost (initial + operating)',\r\n            evaluationFunction: this.createCostObjectiveFunction(),\r\n            units: 'USD'\r\n          }\r\n        ],\r\n        aggregationMethod: 'weighted_sum'\r\n      },\r\n      constraints,\r\n      algorithmSettings: {\r\n        algorithm: OptimizationAlgorithm.PARTICLE_SWARM,\r\n        parameters: {\r\n          populationSize: 40,\r\n          maxIterations: 300,\r\n          inertiaWeight: 0.9,\r\n          accelerationCoefficients: [2.0, 2.0]\r\n        },\r\n        parallelization: { enabled: true }\r\n      },\r\n      convergenceCriteria: {\r\n        maxIterations: 300,\r\n        toleranceValue: 0.005,\r\n        stagnationLimit: 30,\r\n        improvementThreshold: 0.001\r\n      },\r\n      outputRequirements: {\r\n        includeHistory: true,\r\n        detailedAnalysis: true,\r\n        sensitivityAnalysis: true,\r\n        uncertaintyAnalysis: true,\r\n        visualizations: [\r\n          { type: 'convergence', parameters: {} },\r\n          { type: 'pareto_front', parameters: {} }\r\n        ],\r\n        reportFormat: 'json'\r\n      }\r\n    };\r\n    \r\n    return this.optimizeMultiObjective(problem, OptimizationAlgorithm.PARTICLE_SWARM);\r\n  }\r\n\r\n  /**\r\n   * Validate optimization problem structure and constraints\r\n   */\r\n  private static validateOptimizationProblem(problem: OptimizationProblem): void {\r\n    if (!problem.id || !problem.systemConfiguration) {\r\n      throw new Error('Invalid optimization problem: missing required fields');\r\n    }\r\n    \r\n    if (problem.variables.length === 0) {\r\n      throw new Error('Optimization problem must have at least one variable');\r\n    }\r\n    \r\n    if (problem.objectives.objectives.length === 0) {\r\n      throw new Error('Optimization problem must have at least one objective');\r\n    }\r\n    \r\n    // Validate variable bounds\r\n    for (const variable of problem.variables) {\r\n      if (!variable.bounds || variable.bounds.minimum >= variable.bounds.maximum) {\r\n        throw new Error(`Invalid bounds for variable ${variable.id}`);\r\n      }\r\n    }\r\n    \r\n    // Validate objective weights\r\n    const totalWeight = problem.objectives.objectives.reduce((sum, obj) => sum + obj.weight, 0);\r\n    if (Math.abs(totalWeight - 1.0) > 1e-6) {\r\n      console.warn(`Objective weights sum to ${totalWeight}, normalizing to 1.0`);\r\n      problem.objectives.objectives.forEach(obj => obj.weight /= totalWeight);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Create objective function for optimization problem\r\n   */\r\n  private static createObjectiveFunction(problem: OptimizationProblem): ObjectiveFunctionType {\r\n    return (variables: OptimizationVariable[]): number => {\r\n      try {\r\n        // Apply variables to system configuration\r\n        const configuredSystem = this.applyVariablesToSystem(\r\n          problem.systemConfiguration,\r\n          variables\r\n        );\r\n        \r\n        // Calculate system performance using existing Phase 1/2/3 Priority 1 services\r\n        const systemPerformance = SystemPressureCalculator.calculateEnhancedSystemPressure({\r\n          segments: configuredSystem.segments,\r\n          systemType: configuredSystem.systemType,\r\n          designConditions: configuredSystem.designConditions,\r\n          calculationOptions: {\r\n            includeElevation: true,\r\n            includeFittings: true,\r\n            roundResults: false\r\n          }\r\n        });\r\n        \r\n        // Calculate objective values\r\n        let totalObjectiveValue = 0;\r\n        \r\n        for (const objective of problem.objectives.objectives) {\r\n          let objectiveValue: number;\r\n          \r\n          switch (objective.objective) {\r\n            case OptimizationObjective.MINIMIZE_PRESSURE_LOSS:\r\n              objectiveValue = systemPerformance.totalPressureLoss;\r\n              break;\r\n              \r\n            case OptimizationObjective.MINIMIZE_ENERGY_CONSUMPTION:\r\n              objectiveValue = this.calculateEnergyConsumption(systemPerformance, configuredSystem);\r\n              break;\r\n              \r\n            case OptimizationObjective.MINIMIZE_TOTAL_COST:\r\n              objectiveValue = this.calculateTotalCost(systemPerformance, configuredSystem);\r\n              break;\r\n              \r\n            case OptimizationObjective.MINIMIZE_NOISE_LEVEL:\r\n              objectiveValue = this.calculateNoiseLevel(systemPerformance, configuredSystem);\r\n              break;\r\n              \r\n            case OptimizationObjective.MAXIMIZE_EFFICIENCY:\r\n              objectiveValue = -this.calculateSystemEfficiency(systemPerformance, configuredSystem);\r\n              break;\r\n              \r\n            default:\r\n              objectiveValue = objective.evaluationFunction(variables);\r\n          }\r\n          \r\n          totalObjectiveValue += objective.weight * objectiveValue;\r\n        }\r\n        \r\n        return totalObjectiveValue;\r\n        \r\n      } catch (error) {\r\n        console.error('Error in objective function evaluation:', error);\r\n        return Number.MAX_VALUE; // Return large penalty for invalid solutions\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create constraint functions for optimization problem\r\n   */\r\n  private static createConstraintFunctions(problem: OptimizationProblem): ConstraintFunctionType[] {\r\n    return problem.constraints.map(constraint => {\r\n      return (variables: OptimizationVariable[]): number => {\r\n        try {\r\n          // Apply variables to system configuration\r\n          const configuredSystem = this.applyVariablesToSystem(\r\n            problem.systemConfiguration,\r\n            variables\r\n          );\r\n          \r\n          // Evaluate constraint based on type\r\n          return this.evaluateConstraint(constraint, configuredSystem, variables);\r\n          \r\n        } catch (error) {\r\n          console.error(`Error evaluating constraint ${constraint.id}:`, error);\r\n          return Number.MAX_VALUE; // Return large violation for invalid evaluations\r\n        }\r\n      };\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Apply optimization variables to system configuration\r\n   */\r\n  private static applyVariablesToSystem(\r\n    baseSystem: SystemConfiguration,\r\n    variables: OptimizationVariable[]\r\n  ): SystemConfiguration {\r\n    // Deep clone the system configuration\r\n    const configuredSystem: SystemConfiguration = JSON.parse(JSON.stringify(baseSystem));\r\n    \r\n    // Apply each variable to the appropriate system component\r\n    for (const variable of variables) {\r\n      this.applyVariableToSystem(configuredSystem, variable);\r\n    }\r\n    \r\n    return configuredSystem;\r\n  }\r\n\r\n  /**\r\n   * Apply a single variable to system configuration\r\n   */\r\n  private static applyVariableToSystem(\r\n    system: SystemConfiguration,\r\n    variable: OptimizationVariable\r\n  ): void {\r\n    // Implementation depends on variable type\r\n    switch (variable.type) {\r\n      case 'duct_size':\r\n        this.applyDuctSizeVariable(system, variable);\r\n        break;\r\n      case 'fitting_type':\r\n        this.applyFittingTypeVariable(system, variable);\r\n        break;\r\n      case 'material_type':\r\n        this.applyMaterialTypeVariable(system, variable);\r\n        break;\r\n      case 'damper_position':\r\n        this.applyDamperPositionVariable(system, variable);\r\n        break;\r\n      case 'fan_speed':\r\n        this.applyFanSpeedVariable(system, variable);\r\n        break;\r\n      default:\r\n        console.warn(`Unknown variable type: ${variable.type}`);\r\n    }\r\n  }\r\n\r\n  // Variable application methods (to be implemented based on specific requirements)\r\n  private static applyDuctSizeVariable(system: SystemConfiguration, variable: OptimizationVariable): void {\r\n    // Implementation for duct size variables\r\n  }\r\n\r\n  private static applyFittingTypeVariable(system: SystemConfiguration, variable: OptimizationVariable): void {\r\n    // Implementation for fitting type variables\r\n  }\r\n\r\n  private static applyMaterialTypeVariable(system: SystemConfiguration, variable: OptimizationVariable): void {\r\n    // Implementation for material type variables\r\n  }\r\n\r\n  private static applyDamperPositionVariable(system: SystemConfiguration, variable: OptimizationVariable): void {\r\n    // Implementation for damper position variables\r\n  }\r\n\r\n  private static applyFanSpeedVariable(system: SystemConfiguration, variable: OptimizationVariable): void {\r\n    // Implementation for fan speed variables\r\n  }\r\n\r\n  /**\r\n   * Calculate energy consumption for system configuration\r\n   */\r\n  private static calculateEnergyConsumption(\r\n    systemPerformance: any,\r\n    configuredSystem: SystemConfiguration\r\n  ): number {\r\n    // Simplified energy calculation - to be enhanced with detailed fan curves and operating schedules\r\n    const fanPower = systemPerformance.totalPressureLoss * systemPerformance.totalFlow / 6356; // Convert to HP\r\n    const annualOperatingHours = 8760; // Hours per year\r\n    const energyConsumption = fanPower * 0.746 * annualOperatingHours; // Convert to kWh/year\r\n    \r\n    return energyConsumption;\r\n  }\r\n\r\n  /**\r\n   * Calculate total cost for system configuration\r\n   */\r\n  private static calculateTotalCost(\r\n    systemPerformance: any,\r\n    configuredSystem: SystemConfiguration\r\n  ): number {\r\n    // Simplified cost calculation - to be enhanced with detailed cost models\r\n    let materialCost = 0;\r\n    let installationCost = 0;\r\n    let operatingCost = 0;\r\n    \r\n    // Calculate material costs\r\n    for (const segment of configuredSystem.segments) {\r\n      materialCost += this.calculateSegmentMaterialCost(segment);\r\n    }\r\n    \r\n    // Calculate installation costs (typically 1.5-2x material cost)\r\n    installationCost = materialCost * 1.75;\r\n    \r\n    // Calculate annual operating costs\r\n    const energyConsumption = this.calculateEnergyConsumption(systemPerformance, configuredSystem);\r\n    const energyRate = 0.12; // $/kWh\r\n    operatingCost = energyConsumption * energyRate;\r\n    \r\n    // Total cost over 20-year lifecycle\r\n    const lifecycleYears = 20;\r\n    const totalCost = materialCost + installationCost + (operatingCost * lifecycleYears);\r\n    \r\n    return totalCost;\r\n  }\r\n\r\n  /**\r\n   * Calculate noise level for system configuration\r\n   */\r\n  private static calculateNoiseLevel(\r\n    systemPerformance: any,\r\n    configuredSystem: SystemConfiguration\r\n  ): number {\r\n    // Simplified noise calculation - to be enhanced with detailed acoustic models\r\n    let totalNoiseLevel = 0;\r\n    \r\n    for (const segment of configuredSystem.segments) {\r\n      const segmentNoise = this.calculateSegmentNoiseLevel(segment, systemPerformance);\r\n      totalNoiseLevel += Math.pow(10, segmentNoise / 10); // Convert dB to linear scale for addition\r\n    }\r\n    \r\n    return 10 * Math.log10(totalNoiseLevel); // Convert back to dB\r\n  }\r\n\r\n  /**\r\n   * Calculate system efficiency\r\n   */\r\n  private static calculateSystemEfficiency(\r\n    systemPerformance: any,\r\n    configuredSystem: SystemConfiguration\r\n  ): number {\r\n    // Simplified efficiency calculation\r\n    const theoreticalMinimumPressure = systemPerformance.totalFlow * 0.1; // Minimum pressure for flow\r\n    const actualPressure = systemPerformance.totalPressureLoss;\r\n    const efficiency = theoreticalMinimumPressure / actualPressure;\r\n    \r\n    return Math.min(efficiency, 1.0); // Cap at 100% efficiency\r\n  }\r\n\r\n  // Helper methods for cost and noise calculations\r\n  private static calculateSegmentMaterialCost(segment: any): number {\r\n    // Placeholder implementation\r\n    return 100; // Base cost per segment\r\n  }\r\n\r\n  private static calculateSegmentNoiseLevel(segment: any, systemPerformance: any): number {\r\n    // Placeholder implementation\r\n    return 45; // Base noise level in dB\r\n  }\r\n\r\n  /**\r\n   * Evaluate constraint function\r\n   */\r\n  private static evaluateConstraint(\r\n    constraint: OptimizationConstraint,\r\n    configuredSystem: SystemConfiguration,\r\n    variables: OptimizationVariable[]\r\n  ): number {\r\n    // Placeholder implementation - to be enhanced with specific constraint evaluations\r\n    return 0; // Return 0 for satisfied constraints, positive for violations\r\n  }\r\n\r\n  /**\r\n   * Create optimizer instance based on algorithm type\r\n   */\r\n  private static createOptimizer(algorithm: OptimizationAlgorithm, problem: OptimizationProblem): any {\r\n    switch (algorithm) {\r\n      case OptimizationAlgorithm.GENETIC_ALGORITHM:\r\n      case OptimizationAlgorithm.MULTI_OBJECTIVE_GA:\r\n      case OptimizationAlgorithm.NSGA_II:\r\n        return this.geneticAlgorithm;\r\n      case OptimizationAlgorithm.SIMULATED_ANNEALING:\r\n        return this.simulatedAnnealing;\r\n      case OptimizationAlgorithm.PARTICLE_SWARM:\r\n        return this.particleSwarm;\r\n      case OptimizationAlgorithm.GRADIENT_DESCENT:\r\n        return this.gradientDescent;\r\n      default:\r\n        throw new Error(`Unsupported optimization algorithm: ${algorithm}`);\r\n    }\r\n  }\r\n\r\n  // Additional helper methods for creating variables and objectives\r\n  private static createBalancingVariables(\r\n    system: SystemConfiguration,\r\n    targetFlows: { [zoneId: string]: number }\r\n  ): OptimizationVariable[] {\r\n    // Placeholder implementation\r\n    return [];\r\n  }\r\n\r\n  private static createBalancingObjectives(targetFlows: { [zoneId: string]: number }): MultiObjectiveFunction {\r\n    // Placeholder implementation\r\n    return {\r\n      objectives: [],\r\n      aggregationMethod: 'weighted_sum'\r\n    };\r\n  }\r\n\r\n  private static createEnergyOptimizationVariables(system: SystemConfiguration): OptimizationVariable[] {\r\n    // Placeholder implementation\r\n    return [];\r\n  }\r\n\r\n  private static createEnergyObjectiveFunction(operatingConditions: any): ObjectiveFunctionType {\r\n    // Placeholder implementation\r\n    return (variables: OptimizationVariable[]) => 0;\r\n  }\r\n\r\n  private static createCostObjectiveFunction(): ObjectiveFunctionType {\r\n    // Placeholder implementation\r\n    return (variables: OptimizationVariable[]) => 0;\r\n  }\r\n\r\n  // Utility methods\r\n  private static generateOptimizationId(problem: OptimizationProblem): string {\r\n    return `opt_${problem.id}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\r\n  }\r\n\r\n  private static async runOptimization(\r\n    optimizer: any,\r\n    objectiveFunction: ObjectiveFunctionType,\r\n    constraintFunctions: ConstraintFunctionType[],\r\n    problem: OptimizationProblem,\r\n    optimizationId: string\r\n  ): Promise<OptimizationResult> {\r\n    // Placeholder implementation - to be completed with actual optimization logic\r\n    throw new Error('Optimization execution not yet implemented');\r\n  }\r\n\r\n  private static async postProcessResults(\r\n    result: OptimizationResult,\r\n    problem: OptimizationProblem,\r\n    startTime: number\r\n  ): Promise<OptimizationResult> {\r\n    // Placeholder implementation\r\n    return result;\r\n  }\r\n\r\n  private static createErrorResult(\r\n    problem: OptimizationProblem,\r\n    error: Error,\r\n    startTime: number\r\n  ): OptimizationResult {\r\n    // Placeholder implementation\r\n    return {\r\n      problemId: problem.id,\r\n      status: OptimizationStatus.ERROR,\r\n      bestSolution: {} as OptimizationSolution,\r\n      statistics: {} as OptimizationStatistics,\r\n      history: {} as OptimizationHistory,\r\n      analysis: {} as ResultAnalysis,\r\n      recommendations: [],\r\n      warnings: [],\r\n      errors: [error.message]\r\n    };\r\n  }\r\n}\r\n"], "mappings": ";;AAAA;;;;;;;;;;;;;AAAA;AAAA,SAAAA,eAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;MAAA;QAAAC,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAO,SAAA;MAAA;QAAAD,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAW,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,cAAA;MAAAC,IAAA;MAAAC,QAAA;MAAAC,KAAA;MAAAC,OAAA;MAAAC,cAAA;MAAAC,OAAA;IAAA;IAAAC,eAAA;IAAA5B,IAAA;EAAA;EAAA,IAAA6B,QAAA,GAAA5B,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAA0B,QAAA,CAAA9B,IAAA,KAAA8B,QAAA,CAAA9B,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAA6B,QAAA,CAAA9B,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAA0B,cAAA,GAAAD,QAAA,CAAA9B,IAAA;EAAA;;;;;;;;;;;;;;;;AAcA,MAAAgC,yBAAA;AAAA;AAAA,CAAAjC,cAAA,GAAAoB,CAAA,OAAAc,OAAA;AAkBA;AACA,MAAAC,0BAAA;AAAA;AAAA,CAAAnC,cAAA,GAAAoB,CAAA,OAAAc,OAAA;AAKA;AACA,MAAAE,kBAAA;AAAA;AAAA,CAAApC,cAAA,GAAAoB,CAAA,OAAAc,OAAA;AACA,MAAAG,oBAAA;AAAA;AAAA,CAAArC,cAAA,GAAAoB,CAAA,OAAAc,OAAA;AACA,MAAAI,2BAAA;AAAA;AAAA,CAAAtC,cAAA,GAAAoB,CAAA,OAAAc,OAAA;AACA,MAAAK,iBAAA;AAAA;AAAA,CAAAvC,cAAA,GAAAoB,CAAA,OAAAc,OAAA;AACA,MAAAM,qCAAA;AAAA;AAAA,CAAAxC,cAAA,GAAAoB,CAAA,OAAAc,OAAA;AAEA;;;;AAIA,MAAaO,wBAAwB;EAenC;;;EAGO,OAAOC,UAAUA,CAAA;IAAA;IAAA1C,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACtB,IAAI;MAAA;MAAApB,cAAA,GAAAoB,CAAA;MACF,IAAI,CAACuB,gBAAgB,GAAG,IAAIP,kBAAA,CAAAQ,gBAAgB,EAAE;MAAC;MAAA5C,cAAA,GAAAoB,CAAA;MAC/C,IAAI,CAACyB,kBAAkB,GAAG,IAAIR,oBAAA,CAAAS,kBAAkB,EAAE;MAAC;MAAA9C,cAAA,GAAAoB,CAAA;MACnD,IAAI,CAAC2B,aAAa,GAAG,IAAIT,2BAAA,CAAAU,yBAAyB,EAAE;MAAC;MAAAhD,cAAA,GAAAoB,CAAA;MACrD,IAAI,CAAC6B,eAAe,GAAG,IAAIV,iBAAA,CAAAW,eAAe,EAAE;MAAC;MAAAlD,cAAA,GAAAoB,CAAA;MAE7C+B,OAAO,CAACC,GAAG,CAAC,6BAA6B,IAAI,CAACC,OAAO,2BAA2B,CAAC;IACnF,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAA;MAAAtD,cAAA,GAAAoB,CAAA;MACd+B,OAAO,CAACG,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;MAAC;MAAAtD,cAAA,GAAAoB,CAAA;MACvE,MAAM,IAAImC,KAAK,CAAC,8CAA8CD,KAAK,EAAE,CAAC;IACxE;EACF;EAEA;;;EAGO,aAAaE,cAAcA,CAChCC,OAA4B,EAC5BC,SAAA;EAAA;EAAA,CAAA1D,cAAA,GAAAsB,CAAA,UAAmCW,yBAAA,CAAA0B,qBAAqB,CAACC,iBAAiB;IAAA;IAAA5D,cAAA,GAAAqB,CAAA;IAE1E,MAAMwC,SAAS;IAAA;IAAA,CAAA7D,cAAA,GAAAoB,CAAA,QAAG0C,WAAW,CAACC,GAAG,EAAE;IAAC;IAAA/D,cAAA,GAAAoB,CAAA;IAEpC,IAAI;MAAA;MAAApB,cAAA,GAAAoB,CAAA;MACF;MACA,IAAI,CAAC4C,2BAA2B,CAACP,OAAO,CAAC;MAEzC;MACA,MAAMQ,cAAc;MAAA;MAAA,CAAAjE,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAAC8C,sBAAsB,CAACT,OAAO,CAAC;MAE3D;MACA,MAAMU,iBAAiB;MAAA;MAAA,CAAAnE,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACgD,uBAAuB,CAACX,OAAO,CAAC;MAE/D;MACA,MAAMY,mBAAmB;MAAA;MAAA,CAAArE,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACkD,yBAAyB,CAACb,OAAO,CAAC;MAEnE;MACA,MAAMc,SAAS;MAAA;MAAA,CAAAvE,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACoD,eAAe,CAACd,SAAS,EAAED,OAAO,CAAC;MAE1D;MACA,MAAMgB,MAAM;MAAA;MAAA,CAAAzE,cAAA,GAAAoB,CAAA,QAAG,MAAM,IAAI,CAACsD,eAAe,CACvCH,SAAS,EACTJ,iBAAiB,EACjBE,mBAAmB,EACnBZ,OAAO,EACPQ,cAAc,CACf;MAED;MACA,MAAMU,WAAW;MAAA;MAAA,CAAA3E,cAAA,GAAAoB,CAAA,QAAG,MAAM,IAAI,CAACwD,kBAAkB,CAACH,MAAM,EAAEhB,OAAO,EAAEI,SAAS,CAAC;MAE7E;MAAA;MAAA7D,cAAA,GAAAoB,CAAA;MACA,IAAI,CAACyD,mBAAmB,CAACC,GAAG,CAACb,cAAc,EAAEU,WAAW,CAACI,OAAO,CAAC;MAAC;MAAA/E,cAAA,GAAAoB,CAAA;MAClE,IAAI,CAAC4D,kBAAkB,CAACF,GAAG,CAACb,cAAc,EAAEU,WAAW,CAACM,UAAU,CAAC;MAAC;MAAAjF,cAAA,GAAAoB,CAAA;MAEpE,OAAOuD,WAAW;IAEpB,CAAC,CAAC,OAAOrB,KAAK,EAAE;MAAA;MAAAtD,cAAA,GAAAoB,CAAA;MACd+B,OAAO,CAACG,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAAC;MAAAtD,cAAA,GAAAoB,CAAA;MAC7C,OAAO,IAAI,CAAC8D,iBAAiB,CAACzB,OAAO,EAAEH,KAAc,EAAEO,SAAS,CAAC;IACnE;EACF;EAEA;;;EAGO,aAAasB,sBAAsBA,CACxC1B,OAA4B,EAC5BC,SAAA;EAAA;EAAA,CAAA1D,cAAA,GAAAsB,CAAA,UAAmCW,yBAAA,CAAA0B,qBAAqB,CAACyB,OAAO;IAAA;IAAApF,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAGhE,IAAIqC,OAAO,CAAC4B,UAAU,CAACA,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;MAAA;MAAAtF,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAC5C,MAAM,IAAImC,KAAK,CAAC,6DAA6D,CAAC;IAChF,CAAC;IAAA;IAAA;MAAAvD,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAED,IAAI;MAAA;MAAApB,cAAA,GAAAoB,CAAA;MACF+B,OAAO,CAACC,GAAG,CAAC,sDAAsDK,OAAO,CAAC8B,EAAE,EAAE,CAAC;MAE/E;MACA,MAAMC,kBAAkB;MAAA;MAAA,CAAAxF,cAAA,GAAAoB,CAAA,QAAGqC,OAAO,CAAC4B,UAAU,CAACA,UAAU,CAACI,GAAG,CAACC,GAAG,IAC9D;QAAA;QAAA1F,cAAA,GAAAqB,CAAA;QAAArB,cAAA,GAAAoB,CAAA;QAAA,WAAI,CAACgD,uBAAuB,CAACsB,GAAG,EAAEjC,OAAO,CAAC;MAAD,CAAC,CAC3C;MAED;MACA,MAAMY,mBAAmB;MAAA;MAAA,CAAArE,cAAA,GAAAoB,CAAA,QAAGqC,OAAO,CAACkC,WAAW,CAACF,GAAG,CAACG,UAAU,IAC5D;QAAA;QAAA5F,cAAA,GAAAqB,CAAA;QAAArB,cAAA,GAAAoB,CAAA;QAAA,WAAI,CAACyE,wBAAwB,CAACD,UAAU,EAAEnC,OAAO,CAAC;MAAD,CAAC,CACnD;MAED;MACA,MAAMqC,WAAW;MAAA;MAAA,CAAA9F,cAAA,GAAAoB,CAAA,QAAG,IAAIoB,qCAAA,CAAAuD,mCAAmC,CAAC;QAC1DrC,SAAS,EAAE,OAAO;QAClBsC,cAAc,EAAE,GAAG;QACnBC,cAAc,EAAE,GAAG;QACnBC,aAAa,EAAE,GAAG;QAClBC,YAAY,EAAE,GAAG;QACjBC,SAAS,EAAE,EAAE;QACbC,kBAAkB,EAAE,SAAS;QAC7BC,kBAAkB,EAAE,IAAI;QACxBC,cAAc,EAAE;UACdC,YAAY,EAAE,GAAG;UACjBC,kBAAkB,EAAE,IAAI;UACxBC,oBAAoB,EAAE,IAAI;UAC1BC,WAAW,EAAE;YACXC,OAAO,EAAE,IAAI;YACbC,cAAc,EAAE;WACjB;UACDC,OAAO,EAAE;YACPF,OAAO,EAAE,IAAI;YACbG,aAAa,EAAE;;SAElB;QACDC,oBAAoB,EAAE,IAAI;QAC1BC,WAAW,EAAE;OACd,CAAC;MAEF,MAAMxC,MAAM;MAAA;MAAA,CAAAzE,cAAA,GAAAoB,CAAA,QAAG,MAAM0E,WAAW,CAACX,sBAAsB,CAAC1B,OAAO,EAAE+B,kBAAkB,EAAEnB,mBAAmB,CAAC;MAAC;MAAArE,cAAA,GAAAoB,CAAA;MAE1G+B,OAAO,CAACC,GAAG,CAAC,uDAAuDK,OAAO,CAAC8B,EAAE,EAAE,CAAC;MAAC;MAAAvF,cAAA,GAAAoB,CAAA;MACjF,OAAOqD,MAAM;IAEf,CAAC,CAAC,OAAOnB,KAAK,EAAE;MAAA;MAAAtD,cAAA,GAAAoB,CAAA;MACd+B,OAAO,CAACG,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAAC;MAAAtD,cAAA,GAAAoB,CAAA;MAC7D,MAAMkC,KAAK;IACb;EACF;EAEA;;;EAGO,aAAa4D,qBAAqBA,CACvCC,mBAAwC,EACxCC,eAA6C,EAC7CzB,WAAqC;IAAA;IAAA3F,cAAA,GAAAqB,CAAA;IAGrC;IACA,MAAMoC,OAAO;IAAA;IAAA,CAAAzD,cAAA,GAAAoB,CAAA,QAAwB;MACnCmE,EAAE,EAAE,WAAW4B,mBAAmB,CAAC5B,EAAE,IAAI8B,IAAI,CAACtD,GAAG,EAAE,EAAE;MACrDlD,IAAI,EAAE,iCAAiCsG,mBAAmB,CAACtG,IAAI,EAAE;MACjEyG,WAAW,EAAE,yEAAyE;MACtFH,mBAAmB;MACnBI,SAAS,EAAE,IAAI,CAACC,wBAAwB,CAACL,mBAAmB,EAAEC,eAAe,CAAC;MAC9E/B,UAAU,EAAE,IAAI,CAACoC,yBAAyB,CAACL,eAAe,CAAC;MAC3DzB,WAAW;MACX+B,iBAAiB,EAAE;QACjBhE,SAAS,EAAEzB,yBAAA,CAAA0B,qBAAqB,CAACC,iBAAiB;QAClD+D,UAAU,EAAE;UACV3B,cAAc,EAAE,EAAE;UAClB4B,aAAa,EAAE,GAAG;UAClB1B,aAAa,EAAE,GAAG;UAClBC,YAAY,EAAE;SACf;QACD0B,eAAe,EAAE;UAAEjB,OAAO,EAAE;QAAI;OACjC;MACDkB,mBAAmB,EAAE;QACnBF,aAAa,EAAE,GAAG;QAClBG,cAAc,EAAE,IAAI;QACpBC,eAAe,EAAE,EAAE;QACnBC,oBAAoB,EAAE;OACvB;MACDC,kBAAkB,EAAE;QAClBC,cAAc,EAAE,IAAI;QACpBC,gBAAgB,EAAE,IAAI;QACtBC,mBAAmB,EAAE,IAAI;QACzBC,mBAAmB,EAAE,KAAK;QAC1BC,cAAc,EAAE,CACd;UAAEtH,IAAI,EAAE,aAAa;UAAE0G,UAAU,EAAE;QAAE,CAAE,EACvC;UAAE1G,IAAI,EAAE,kBAAkB;UAAE0G,UAAU,EAAE;QAAE,CAAE,CAC7C;QACDa,YAAY,EAAE;;KAEjB;IAAC;IAAAxI,cAAA,GAAAoB,CAAA;IAEF,OAAO,IAAI,CAACoC,cAAc,CAACC,OAAO,EAAExB,yBAAA,CAAA0B,qBAAqB,CAACC,iBAAiB,CAAC;EAC9E;EAEA;;;EAGO,aAAa6E,wBAAwBA,CAC1CtB,mBAAwC,EACxCuB,mBAAwB,EACxB/C,WAAqC;IAAA;IAAA3F,cAAA,GAAAqB,CAAA;IAGrC,MAAMoC,OAAO;IAAA;IAAA,CAAAzD,cAAA,GAAAoB,CAAA,QAAwB;MACnCmE,EAAE,EAAE,UAAU4B,mBAAmB,CAAC5B,EAAE,IAAI8B,IAAI,CAACtD,GAAG,EAAE,EAAE;MACpDlD,IAAI,EAAE,oCAAoCsG,mBAAmB,CAACtG,IAAI,EAAE;MACpEyG,WAAW,EAAE,8DAA8D;MAC3EH,mBAAmB;MACnBI,SAAS,EAAE,IAAI,CAACoB,iCAAiC,CAACxB,mBAAmB,CAAC;MACtE9B,UAAU,EAAE;QACVA,UAAU,EAAE,CACV;UACEE,EAAE,EAAE,oBAAoB;UACxBqD,SAAS,EAAE3G,yBAAA,CAAA4G,qBAAqB,CAACC,2BAA2B;UAC5DC,MAAM,EAAE,GAAG;UACXzB,WAAW,EAAE,0CAA0C;UACvD0B,kBAAkB,EAAE,IAAI,CAACC,6BAA6B,CAACP,mBAAmB,CAAC;UAC3EQ,KAAK,EAAE;SACR,EACD;UACE3D,EAAE,EAAE,YAAY;UAChBqD,SAAS,EAAE3G,yBAAA,CAAA4G,qBAAqB,CAACM,mBAAmB;UACpDJ,MAAM,EAAE,GAAG;UACXzB,WAAW,EAAE,kDAAkD;UAC/D0B,kBAAkB,EAAE,IAAI,CAACI,2BAA2B,EAAE;UACtDF,KAAK,EAAE;SACR,CACF;QACDG,iBAAiB,EAAE;OACpB;MACD1D,WAAW;MACX+B,iBAAiB,EAAE;QACjBhE,SAAS,EAAEzB,yBAAA,CAAA0B,qBAAqB,CAAC2F,cAAc;QAC/C3B,UAAU,EAAE;UACV3B,cAAc,EAAE,EAAE;UAClB4B,aAAa,EAAE,GAAG;UAClB2B,aAAa,EAAE,GAAG;UAClBC,wBAAwB,EAAE,CAAC,GAAG,EAAE,GAAG;SACpC;QACD3B,eAAe,EAAE;UAAEjB,OAAO,EAAE;QAAI;OACjC;MACDkB,mBAAmB,EAAE;QACnBF,aAAa,EAAE,GAAG;QAClBG,cAAc,EAAE,KAAK;QACrBC,eAAe,EAAE,EAAE;QACnBC,oBAAoB,EAAE;OACvB;MACDC,kBAAkB,EAAE;QAClBC,cAAc,EAAE,IAAI;QACpBC,gBAAgB,EAAE,IAAI;QACtBC,mBAAmB,EAAE,IAAI;QACzBC,mBAAmB,EAAE,IAAI;QACzBC,cAAc,EAAE,CACd;UAAEtH,IAAI,EAAE,aAAa;UAAE0G,UAAU,EAAE;QAAE,CAAE,EACvC;UAAE1G,IAAI,EAAE,cAAc;UAAE0G,UAAU,EAAE;QAAE,CAAE,CACzC;QACDa,YAAY,EAAE;;KAEjB;IAAC;IAAAxI,cAAA,GAAAoB,CAAA;IAEF,OAAO,IAAI,CAAC+D,sBAAsB,CAAC1B,OAAO,EAAExB,yBAAA,CAAA0B,qBAAqB,CAAC2F,cAAc,CAAC;EACnF;EAEA;;;EAGQ,OAAOtF,2BAA2BA,CAACP,OAA4B;IAAA;IAAAzD,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACrE;IAAI;IAAA,CAAApB,cAAA,GAAAsB,CAAA,WAACmC,OAAO,CAAC8B,EAAE;IAAA;IAAA,CAAAvF,cAAA,GAAAsB,CAAA,UAAI,CAACmC,OAAO,CAAC0D,mBAAmB,GAAE;MAAA;MAAAnH,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAC/C,MAAM,IAAImC,KAAK,CAAC,uDAAuD,CAAC;IAC1E,CAAC;IAAA;IAAA;MAAAvD,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAED,IAAIqC,OAAO,CAAC8D,SAAS,CAACjC,MAAM,KAAK,CAAC,EAAE;MAAA;MAAAtF,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAClC,MAAM,IAAImC,KAAK,CAAC,sDAAsD,CAAC;IACzE,CAAC;IAAA;IAAA;MAAAvD,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAED,IAAIqC,OAAO,CAAC4B,UAAU,CAACA,UAAU,CAACC,MAAM,KAAK,CAAC,EAAE;MAAA;MAAAtF,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAC9C,MAAM,IAAImC,KAAK,CAAC,uDAAuD,CAAC;IAC1E,CAAC;IAAA;IAAA;MAAAvD,cAAA,GAAAsB,CAAA;IAAA;IAED;IAAAtB,cAAA,GAAAoB,CAAA;IACA,KAAK,MAAMqI,QAAQ,IAAIhG,OAAO,CAAC8D,SAAS,EAAE;MAAA;MAAAvH,cAAA,GAAAoB,CAAA;MACxC;MAAI;MAAA,CAAApB,cAAA,GAAAsB,CAAA,WAACmI,QAAQ,CAACC,MAAM;MAAA;MAAA,CAAA1J,cAAA,GAAAsB,CAAA,UAAImI,QAAQ,CAACC,MAAM,CAACC,OAAO,IAAIF,QAAQ,CAACC,MAAM,CAACE,OAAO,GAAE;QAAA;QAAA5J,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAC1E,MAAM,IAAImC,KAAK,CAAC,+BAA+BkG,QAAQ,CAAClE,EAAE,EAAE,CAAC;MAC/D,CAAC;MAAA;MAAA;QAAAvF,cAAA,GAAAsB,CAAA;MAAA;IACH;IAEA;IACA,MAAMuI,WAAW;IAAA;IAAA,CAAA7J,cAAA,GAAAoB,CAAA,QAAGqC,OAAO,CAAC4B,UAAU,CAACA,UAAU,CAACyE,MAAM,CAAC,CAACC,GAAG,EAAErE,GAAG,KAAK;MAAA;MAAA1F,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAAA,OAAA2I,GAAG,GAAGrE,GAAG,CAACqD,MAAM;IAAN,CAAM,EAAE,CAAC,CAAC;IAAC;IAAA/I,cAAA,GAAAoB,CAAA;IAC5F,IAAI4I,IAAI,CAACC,GAAG,CAACJ,WAAW,GAAG,GAAG,CAAC,GAAG,IAAI,EAAE;MAAA;MAAA7J,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACtC+B,OAAO,CAAC+G,IAAI,CAAC,4BAA4BL,WAAW,sBAAsB,CAAC;MAAC;MAAA7J,cAAA,GAAAoB,CAAA;MAC5EqC,OAAO,CAAC4B,UAAU,CAACA,UAAU,CAAC8E,OAAO,CAACzE,GAAG,IAAI;QAAA;QAAA1F,cAAA,GAAAqB,CAAA;QAAArB,cAAA,GAAAoB,CAAA;QAAA,OAAAsE,GAAG,CAACqD,MAAM,IAAIc,WAAW;MAAX,CAAW,CAAC;IACzE,CAAC;IAAA;IAAA;MAAA7J,cAAA,GAAAsB,CAAA;IAAA;EACH;EAEA;;;EAGQ,OAAO8C,uBAAuBA,CAACX,OAA4B;IAAA;IAAAzD,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACjE,OAAQmG,SAAiC,IAAY;MAAA;MAAAvH,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MACnD,IAAI;QACF;QACA,MAAMgJ,gBAAgB;QAAA;QAAA,CAAApK,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACiJ,sBAAsB,CAClD5G,OAAO,CAAC0D,mBAAmB,EAC3BI,SAAS,CACV;QAED;QACA,MAAM+C,iBAAiB;QAAA;QAAA,CAAAtK,cAAA,GAAAoB,CAAA,QAAGe,0BAAA,CAAAoI,wBAAwB,CAACC,+BAA+B,CAAC;UACjFC,QAAQ,EAAEL,gBAAgB,CAACK,QAAQ;UACnCC,UAAU,EAAEN,gBAAgB,CAACM,UAAU;UACvCC,gBAAgB,EAAEP,gBAAgB,CAACO,gBAAgB;UACnDC,kBAAkB,EAAE;YAClBC,gBAAgB,EAAE,IAAI;YACtBC,eAAe,EAAE,IAAI;YACrBC,YAAY,EAAE;;SAEjB,CAAC;QAEF;QACA,IAAIC,mBAAmB;QAAA;QAAA,CAAAhL,cAAA,GAAAoB,CAAA,QAAG,CAAC;QAAC;QAAApB,cAAA,GAAAoB,CAAA;QAE5B,KAAK,MAAMwH,SAAS,IAAInF,OAAO,CAAC4B,UAAU,CAACA,UAAU,EAAE;UACrD,IAAI4F,cAAsB;UAAC;UAAAjL,cAAA,GAAAoB,CAAA;UAE3B,QAAQwH,SAAS,CAACA,SAAS;YACzB,KAAK3G,yBAAA,CAAA4G,qBAAqB,CAACqC,sBAAsB;cAAA;cAAAlL,cAAA,GAAAsB,CAAA;cAAAtB,cAAA,GAAAoB,CAAA;cAC/C6J,cAAc,GAAGX,iBAAiB,CAACa,iBAAiB;cAAC;cAAAnL,cAAA,GAAAoB,CAAA;cACrD;YAEF,KAAKa,yBAAA,CAAA4G,qBAAqB,CAACC,2BAA2B;cAAA;cAAA9I,cAAA,GAAAsB,CAAA;cAAAtB,cAAA,GAAAoB,CAAA;cACpD6J,cAAc,GAAG,IAAI,CAACG,0BAA0B,CAACd,iBAAiB,EAAEF,gBAAgB,CAAC;cAAC;cAAApK,cAAA,GAAAoB,CAAA;cACtF;YAEF,KAAKa,yBAAA,CAAA4G,qBAAqB,CAACM,mBAAmB;cAAA;cAAAnJ,cAAA,GAAAsB,CAAA;cAAAtB,cAAA,GAAAoB,CAAA;cAC5C6J,cAAc,GAAG,IAAI,CAACI,kBAAkB,CAACf,iBAAiB,EAAEF,gBAAgB,CAAC;cAAC;cAAApK,cAAA,GAAAoB,CAAA;cAC9E;YAEF,KAAKa,yBAAA,CAAA4G,qBAAqB,CAACyC,oBAAoB;cAAA;cAAAtL,cAAA,GAAAsB,CAAA;cAAAtB,cAAA,GAAAoB,CAAA;cAC7C6J,cAAc,GAAG,IAAI,CAACM,mBAAmB,CAACjB,iBAAiB,EAAEF,gBAAgB,CAAC;cAAC;cAAApK,cAAA,GAAAoB,CAAA;cAC/E;YAEF,KAAKa,yBAAA,CAAA4G,qBAAqB,CAAC2C,mBAAmB;cAAA;cAAAxL,cAAA,GAAAsB,CAAA;cAAAtB,cAAA,GAAAoB,CAAA;cAC5C6J,cAAc,GAAG,CAAC,IAAI,CAACQ,yBAAyB,CAACnB,iBAAiB,EAAEF,gBAAgB,CAAC;cAAC;cAAApK,cAAA,GAAAoB,CAAA;cACtF;YAEF;cAAA;cAAApB,cAAA,GAAAsB,CAAA;cAAAtB,cAAA,GAAAoB,CAAA;cACE6J,cAAc,GAAGrC,SAAS,CAACI,kBAAkB,CAACzB,SAAS,CAAC;UAC5D;UAAC;UAAAvH,cAAA,GAAAoB,CAAA;UAED4J,mBAAmB,IAAIpC,SAAS,CAACG,MAAM,GAAGkC,cAAc;QAC1D;QAAC;QAAAjL,cAAA,GAAAoB,CAAA;QAED,OAAO4J,mBAAmB;MAE5B,CAAC,CAAC,OAAO1H,KAAK,EAAE;QAAA;QAAAtD,cAAA,GAAAoB,CAAA;QACd+B,OAAO,CAACG,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;QAAC;QAAAtD,cAAA,GAAAoB,CAAA;QAChE,OAAOsK,MAAM,CAACC,SAAS,CAAC,CAAC;MAC3B;IACF,CAAC;EACH;EAEA;;;EAGQ,OAAOrH,yBAAyBA,CAACb,OAA4B;IAAA;IAAAzD,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACnE,OAAOqC,OAAO,CAACkC,WAAW,CAACF,GAAG,CAACG,UAAU,IAAG;MAAA;MAAA5F,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAC1C,OAAQmG,SAAiC,IAAY;QAAA;QAAAvH,cAAA,GAAAqB,CAAA;QAAArB,cAAA,GAAAoB,CAAA;QACnD,IAAI;UACF;UACA,MAAMgJ,gBAAgB;UAAA;UAAA,CAAApK,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACiJ,sBAAsB,CAClD5G,OAAO,CAAC0D,mBAAmB,EAC3BI,SAAS,CACV;UAED;UAAA;UAAAvH,cAAA,GAAAoB,CAAA;UACA,OAAO,IAAI,CAACwK,kBAAkB,CAAChG,UAAU,EAAEwE,gBAAgB,EAAE7C,SAAS,CAAC;QAEzE,CAAC,CAAC,OAAOjE,KAAK,EAAE;UAAA;UAAAtD,cAAA,GAAAoB,CAAA;UACd+B,OAAO,CAACG,KAAK,CAAC,+BAA+BsC,UAAU,CAACL,EAAE,GAAG,EAAEjC,KAAK,CAAC;UAAC;UAAAtD,cAAA,GAAAoB,CAAA;UACtE,OAAOsK,MAAM,CAACC,SAAS,CAAC,CAAC;QAC3B;MACF,CAAC;IACH,CAAC,CAAC;EACJ;EAEA;;;EAGQ,OAAOtB,sBAAsBA,CACnCwB,UAA+B,EAC/BtE,SAAiC;IAAA;IAAAvH,cAAA,GAAAqB,CAAA;IAEjC;IACA,MAAM+I,gBAAgB;IAAA;IAAA,CAAApK,cAAA,GAAAoB,CAAA,QAAwB0K,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACH,UAAU,CAAC,CAAC;IAEpF;IAAA;IAAA7L,cAAA,GAAAoB,CAAA;IACA,KAAK,MAAMqI,QAAQ,IAAIlC,SAAS,EAAE;MAAA;MAAAvH,cAAA,GAAAoB,CAAA;MAChC,IAAI,CAAC6K,qBAAqB,CAAC7B,gBAAgB,EAAEX,QAAQ,CAAC;IACxD;IAAC;IAAAzJ,cAAA,GAAAoB,CAAA;IAED,OAAOgJ,gBAAgB;EACzB;EAEA;;;EAGQ,OAAO6B,qBAAqBA,CAClCC,MAA2B,EAC3BzC,QAA8B;IAAA;IAAAzJ,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAE9B;IACA,QAAQqI,QAAQ,CAACxI,IAAI;MACnB,KAAK,WAAW;QAAA;QAAAjB,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACd,IAAI,CAAC+K,qBAAqB,CAACD,MAAM,EAAEzC,QAAQ,CAAC;QAAC;QAAAzJ,cAAA,GAAAoB,CAAA;QAC7C;MACF,KAAK,cAAc;QAAA;QAAApB,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACjB,IAAI,CAACgL,wBAAwB,CAACF,MAAM,EAAEzC,QAAQ,CAAC;QAAC;QAAAzJ,cAAA,GAAAoB,CAAA;QAChD;MACF,KAAK,eAAe;QAAA;QAAApB,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAClB,IAAI,CAACiL,yBAAyB,CAACH,MAAM,EAAEzC,QAAQ,CAAC;QAAC;QAAAzJ,cAAA,GAAAoB,CAAA;QACjD;MACF,KAAK,iBAAiB;QAAA;QAAApB,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACpB,IAAI,CAACkL,2BAA2B,CAACJ,MAAM,EAAEzC,QAAQ,CAAC;QAAC;QAAAzJ,cAAA,GAAAoB,CAAA;QACnD;MACF,KAAK,WAAW;QAAA;QAAApB,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACd,IAAI,CAACmL,qBAAqB,CAACL,MAAM,EAAEzC,QAAQ,CAAC;QAAC;QAAAzJ,cAAA,GAAAoB,CAAA;QAC7C;MACF;QAAA;QAAApB,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACE+B,OAAO,CAAC+G,IAAI,CAAC,0BAA0BT,QAAQ,CAACxI,IAAI,EAAE,CAAC;IAC3D;EACF;EAEA;EACQ,OAAOkL,qBAAqBA,CAACD,MAA2B,EAAEzC,QAA8B;IAAA;IAAAzJ,cAAA,GAAAqB,CAAA;EAEhG,CAAC,CADC;EAGM,OAAO+K,wBAAwBA,CAACF,MAA2B,EAAEzC,QAA8B;IAAA;IAAAzJ,cAAA,GAAAqB,CAAA;EAEnG,CAAC,CADC;EAGM,OAAOgL,yBAAyBA,CAACH,MAA2B,EAAEzC,QAA8B;IAAA;IAAAzJ,cAAA,GAAAqB,CAAA;EAEpG,CAAC,CADC;EAGM,OAAOiL,2BAA2BA,CAACJ,MAA2B,EAAEzC,QAA8B;IAAA;IAAAzJ,cAAA,GAAAqB,CAAA;EAEtG,CAAC,CADC;EAGM,OAAOkL,qBAAqBA,CAACL,MAA2B,EAAEzC,QAA8B;IAAA;IAAAzJ,cAAA,GAAAqB,CAAA;EAEhG,CAAC,CADC;EAGF;;;EAGQ,OAAO+J,0BAA0BA,CACvCd,iBAAsB,EACtBF,gBAAqC;IAAA;IAAApK,cAAA,GAAAqB,CAAA;IAErC;IACA,MAAMmL,QAAQ;IAAA;IAAA,CAAAxM,cAAA,GAAAoB,CAAA,SAAGkJ,iBAAiB,CAACa,iBAAiB,GAAGb,iBAAiB,CAACmC,SAAS,GAAG,IAAI,EAAC,CAAC;IAC3F,MAAMC,oBAAoB;IAAA;IAAA,CAAA1M,cAAA,GAAAoB,CAAA,SAAG,IAAI,EAAC,CAAC;IACnC,MAAMuL,iBAAiB;IAAA;IAAA,CAAA3M,cAAA,GAAAoB,CAAA,SAAGoL,QAAQ,GAAG,KAAK,GAAGE,oBAAoB,EAAC,CAAC;IAAA;IAAA1M,cAAA,GAAAoB,CAAA;IAEnE,OAAOuL,iBAAiB;EAC1B;EAEA;;;EAGQ,OAAOtB,kBAAkBA,CAC/Bf,iBAAsB,EACtBF,gBAAqC;IAAA;IAAApK,cAAA,GAAAqB,CAAA;IAErC;IACA,IAAIuL,YAAY;IAAA;IAAA,CAAA5M,cAAA,GAAAoB,CAAA,SAAG,CAAC;IACpB,IAAIyL,gBAAgB;IAAA;IAAA,CAAA7M,cAAA,GAAAoB,CAAA,SAAG,CAAC;IACxB,IAAI0L,aAAa;IAAA;IAAA,CAAA9M,cAAA,GAAAoB,CAAA,SAAG,CAAC;IAErB;IAAA;IAAApB,cAAA,GAAAoB,CAAA;IACA,KAAK,MAAM2L,OAAO,IAAI3C,gBAAgB,CAACK,QAAQ,EAAE;MAAA;MAAAzK,cAAA,GAAAoB,CAAA;MAC/CwL,YAAY,IAAI,IAAI,CAACI,4BAA4B,CAACD,OAAO,CAAC;IAC5D;IAEA;IAAA;IAAA/M,cAAA,GAAAoB,CAAA;IACAyL,gBAAgB,GAAGD,YAAY,GAAG,IAAI;IAEtC;IACA,MAAMD,iBAAiB;IAAA;IAAA,CAAA3M,cAAA,GAAAoB,CAAA,SAAG,IAAI,CAACgK,0BAA0B,CAACd,iBAAiB,EAAEF,gBAAgB,CAAC;IAC9F,MAAM6C,UAAU;IAAA;IAAA,CAAAjN,cAAA,GAAAoB,CAAA,SAAG,IAAI,EAAC,CAAC;IAAA;IAAApB,cAAA,GAAAoB,CAAA;IACzB0L,aAAa,GAAGH,iBAAiB,GAAGM,UAAU;IAE9C;IACA,MAAMC,cAAc;IAAA;IAAA,CAAAlN,cAAA,GAAAoB,CAAA,SAAG,EAAE;IACzB,MAAM+L,SAAS;IAAA;IAAA,CAAAnN,cAAA,GAAAoB,CAAA,SAAGwL,YAAY,GAAGC,gBAAgB,GAAIC,aAAa,GAAGI,cAAe;IAAC;IAAAlN,cAAA,GAAAoB,CAAA;IAErF,OAAO+L,SAAS;EAClB;EAEA;;;EAGQ,OAAO5B,mBAAmBA,CAChCjB,iBAAsB,EACtBF,gBAAqC;IAAA;IAAApK,cAAA,GAAAqB,CAAA;IAErC;IACA,IAAI+L,eAAe;IAAA;IAAA,CAAApN,cAAA,GAAAoB,CAAA,SAAG,CAAC;IAAC;IAAApB,cAAA,GAAAoB,CAAA;IAExB,KAAK,MAAM2L,OAAO,IAAI3C,gBAAgB,CAACK,QAAQ,EAAE;MAC/C,MAAM4C,YAAY;MAAA;MAAA,CAAArN,cAAA,GAAAoB,CAAA,SAAG,IAAI,CAACkM,0BAA0B,CAACP,OAAO,EAAEzC,iBAAiB,CAAC;MAAC;MAAAtK,cAAA,GAAAoB,CAAA;MACjFgM,eAAe,IAAIpD,IAAI,CAACuD,GAAG,CAAC,EAAE,EAAEF,YAAY,GAAG,EAAE,CAAC,CAAC,CAAC;IACtD;IAAC;IAAArN,cAAA,GAAAoB,CAAA;IAED,OAAO,EAAE,GAAG4I,IAAI,CAACwD,KAAK,CAACJ,eAAe,CAAC,CAAC,CAAC;EAC3C;EAEA;;;EAGQ,OAAO3B,yBAAyBA,CACtCnB,iBAAsB,EACtBF,gBAAqC;IAAA;IAAApK,cAAA,GAAAqB,CAAA;IAErC;IACA,MAAMoM,0BAA0B;IAAA;IAAA,CAAAzN,cAAA,GAAAoB,CAAA,SAAGkJ,iBAAiB,CAACmC,SAAS,GAAG,GAAG,EAAC,CAAC;IACtE,MAAMiB,cAAc;IAAA;IAAA,CAAA1N,cAAA,GAAAoB,CAAA,SAAGkJ,iBAAiB,CAACa,iBAAiB;IAC1D,MAAMwC,UAAU;IAAA;IAAA,CAAA3N,cAAA,GAAAoB,CAAA,SAAGqM,0BAA0B,GAAGC,cAAc;IAAC;IAAA1N,cAAA,GAAAoB,CAAA;IAE/D,OAAO4I,IAAI,CAAC4D,GAAG,CAACD,UAAU,EAAE,GAAG,CAAC,CAAC,CAAC;EACpC;EAEA;EACQ,OAAOX,4BAA4BA,CAACD,OAAY;IAAA;IAAA/M,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACtD;IACA,OAAO,GAAG,CAAC,CAAC;EACd;EAEQ,OAAOkM,0BAA0BA,CAACP,OAAY,EAAEzC,iBAAsB;IAAA;IAAAtK,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAC5E;IACA,OAAO,EAAE,CAAC,CAAC;EACb;EAEA;;;EAGQ,OAAOwK,kBAAkBA,CAC/BhG,UAAkC,EAClCwE,gBAAqC,EACrC7C,SAAiC;IAAA;IAAAvH,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAEjC;IACA,OAAO,CAAC,CAAC,CAAC;EACZ;EAEA;;;EAGQ,OAAOoD,eAAeA,CAACd,SAAgC,EAAED,OAA4B;IAAA;IAAAzD,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAC3F,QAAQsC,SAAS;MACf,KAAKzB,yBAAA,CAAA0B,qBAAqB,CAACC,iBAAiB;QAAA;QAAA5D,cAAA,GAAAsB,CAAA;MAC5C,KAAKW,yBAAA,CAAA0B,qBAAqB,CAACkK,kBAAkB;QAAA;QAAA7N,cAAA,GAAAsB,CAAA;MAC7C,KAAKW,yBAAA,CAAA0B,qBAAqB,CAACyB,OAAO;QAAA;QAAApF,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAChC,OAAO,IAAI,CAACuB,gBAAgB;MAC9B,KAAKV,yBAAA,CAAA0B,qBAAqB,CAACmK,mBAAmB;QAAA;QAAA9N,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAC5C,OAAO,IAAI,CAACyB,kBAAkB;MAChC,KAAKZ,yBAAA,CAAA0B,qBAAqB,CAAC2F,cAAc;QAAA;QAAAtJ,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACvC,OAAO,IAAI,CAAC2B,aAAa;MAC3B,KAAKd,yBAAA,CAAA0B,qBAAqB,CAACoK,gBAAgB;QAAA;QAAA/N,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACzC,OAAO,IAAI,CAAC6B,eAAe;MAC7B;QAAA;QAAAjD,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACE,MAAM,IAAImC,KAAK,CAAC,uCAAuCG,SAAS,EAAE,CAAC;IACvE;EACF;EAEA;EACQ,OAAO8D,wBAAwBA,CACrC0E,MAA2B,EAC3B8B,WAAyC;IAAA;IAAAhO,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAEzC;IACA,OAAO,EAAE;EACX;EAEQ,OAAOqG,yBAAyBA,CAACuG,WAAyC;IAAA;IAAAhO,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAChF;IACA,OAAO;MACLiE,UAAU,EAAE,EAAE;MACdgE,iBAAiB,EAAE;KACpB;EACH;EAEQ,OAAOV,iCAAiCA,CAACuD,MAA2B;IAAA;IAAAlM,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAC1E;IACA,OAAO,EAAE;EACX;EAEQ,OAAO6H,6BAA6BA,CAACP,mBAAwB;IAAA;IAAA1I,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACnE;IACA,OAAQmG,SAAiC,IAAK;MAAA;MAAAvH,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAAA,QAAC;IAAD,CAAC;EACjD;EAEQ,OAAOgI,2BAA2BA,CAAA;IAAA;IAAApJ,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACxC;IACA,OAAQmG,SAAiC,IAAK;MAAA;MAAAvH,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAAA,QAAC;IAAD,CAAC;EACjD;EAEA;EACQ,OAAO8C,sBAAsBA,CAACT,OAA4B;IAAA;IAAAzD,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAChE,OAAO,OAAOqC,OAAO,CAAC8B,EAAE,IAAI8B,IAAI,CAACtD,GAAG,EAAE,IAAIiG,IAAI,CAACiE,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;EACrF;EAEQ,aAAazJ,eAAeA,CAClCH,SAAc,EACdJ,iBAAwC,EACxCE,mBAA6C,EAC7CZ,OAA4B,EAC5BQ,cAAsB;IAAA;IAAAjE,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAEtB;IACA,MAAM,IAAImC,KAAK,CAAC,4CAA4C,CAAC;EAC/D;EAEQ,aAAaqB,kBAAkBA,CACrCH,MAA0B,EAC1BhB,OAA4B,EAC5BI,SAAiB;IAAA;IAAA7D,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAEjB;IACA,OAAOqD,MAAM;EACf;EAEQ,OAAOS,iBAAiBA,CAC9BzB,OAA4B,EAC5BH,KAAY,EACZO,SAAiB;IAAA;IAAA7D,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAEjB;IACA,OAAO;MACLgN,SAAS,EAAE3K,OAAO,CAAC8B,EAAE;MACrB8I,MAAM,EAAEpM,yBAAA,CAAAqM,kBAAkB,CAACC,KAAK;MAChCC,YAAY,EAAE,EAA0B;MACxCvJ,UAAU,EAAE,EAA4B;MACxCF,OAAO,EAAE,EAAyB;MAClC0J,QAAQ,EAAE,EAAoB;MAC9BC,eAAe,EAAE,EAAE;MACnBC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,CAACtL,KAAK,CAACuL,OAAO;KACvB;EACH;;;;AA1oBFC,OAAA,CAAArM,wBAAA,GAAAA,wBAAA;AA2oBC;AAAAzC,cAAA,GAAAoB,CAAA;AA1oByBqB,wBAAA,CAAAY,OAAO,GAAG,OAAO;AAAC;AAAArD,cAAA,GAAAoB,CAAA;AAClBqB,wBAAA,CAAAsM,sBAAsB,GAAG,IAAI;AAAC;AAAA/O,cAAA,GAAAoB,CAAA;AAC9BqB,wBAAA,CAAAuM,6BAA6B,GAAG,IAAI;AAQ5D;AAAA;AAAAhP,cAAA,GAAAoB,CAAA;AACeqB,wBAAA,CAAAoC,mBAAmB,GAAqC,IAAIoK,GAAG,EAAE;AAAC;AAAAjP,cAAA,GAAAoB,CAAA;AAClEqB,wBAAA,CAAAuC,kBAAkB,GAAwC,IAAIiK,GAAG,EAAE", "ignoreList": []}