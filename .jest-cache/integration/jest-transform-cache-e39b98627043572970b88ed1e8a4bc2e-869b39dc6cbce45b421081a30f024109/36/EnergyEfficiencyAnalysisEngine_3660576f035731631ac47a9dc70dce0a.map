{"version": 3, "names": ["cov_9l8cmvs2f", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "name", "decl", "loc", "branchMap", "type", "locations", "undefined", "s", "f", "b", "inputSourceMap", "file", "mappings", "names", "sources", "sourcesContent", "version", "_coverageSchema", "coverage", "actualCoverage", "SystemAnalysisTypes_1", "require", "EnergyEfficiencyAnalysisEngine", "analyzeEnergyEfficiency", "systemConfiguration", "performanceMetrics", "operatingSchedule", "energyRates", "analysisId", "generateAnalysisId", "id", "timestamp", "Date", "energyConsumption", "calculateEnergyConsumption", "efficiencyMetrics", "calculateEnergyEfficiencyMetrics", "energyCosts", "calculateEnergyCosts", "carbonFootprint", "calculateCarbonFootprint", "benchmarkComparison", "performEnergyBenchmarking", "optimizationOpportunities", "identifyOptimizationOpportunities", "seasonalAnalysis", "performSeasonalEnergyAnalysis", "analysis", "systemId", "analysisTimestamp", "ENERGY_CACHE", "set", "error", "Error", "message", "fanPowerKW", "fan<PERSON>ower", "value", "designAirflow", "designParameters", "schedule", "hoursPerDay", "daysPerWeek", "weeksPerYear", "loadProfile", "annualOperatingHours", "fanConsumption", "createEnergyMeasurement", "EnergyUnits", "KWH", "TimeFrame", "ANNUALLY", "MeasurementSource", "CALCULATED", "auxiliaryConsumption", "ESTIMATED", "totalConsumption", "timeOfDayConsumption", "calculateTimeOfDayConsumption", "calculateLoadProfile", "peakDemand", "calculatePeakDemand", "consumptionByTimeOfDay", "units", "timeFrame", "source", "accuracy", "consumption", "hourlyProfile", "hour", "loadFactor", "for<PERSON>ach", "profile", "push", "timeOfDay", "powerDemand", "baseLoad", "peakLoad", "averageLoad", "diversityFactor", "demandProfile", "period", "demand", "peakPower", "peakTime", "peakDuration", "peakFrequency", "coincidentFactor", "demandCharges", "peakShavingPotential", "systemEfficiency", "specificFan<PERSON>ower", "buildingArea", "energyUtilizationIndex", "KWH_TO_BTU", "powerDensity", "efficiencyTrend", "calculateEfficiencyTrend", "calculateEfficiencyBenchmark", "overallEfficiency", "fanEfficiency", "transportEfficiency", "currentEfficiency", "trendDirection", "trendRate", "projectedEfficiency", "timeHorizon", "confidenceLevel", "ashraeSFPLimit", "industryAverageSFP", "bestPracticeSFP", "benchmarkType", "benchmarkSource", "currentValue", "benchmarkValue", "industryAverage", "bestPractice", "percentile", "calculateSFPPercentile", "complianceStatus", "improvementPotential", "Math", "max", "sfp", "rates", "energyRate", "demandRate", "fixedRate", "timeOfUseRates", "peak", "offPeak", "shoulder", "energyCost", "demandCost", "fixedCost", "totalCost", "currentCosts", "currency", "projectedCosts", "calculateCostProjections", "costSavingOpportunities", "identifyCostSavingOpportunities", "utilityRateStructure", "createUtilityRateStructure", "<PERSON><PERSON><PERSON><PERSON>", "annualDemandCost", "potentialSavings", "timeOfUsePricing", "enabled", "peakRate", "offPeakRate", "shoulderRate", "peakHours", "offPeakHours", "calculateTOUSavings", "escalationRate", "projections", "year", "escalationFactor", "pow", "cumulativeCost", "opportunities", "description", "implementationCost", "paybackPeriod", "savingsType", "confidence", "rateSchedule", "energyCharges", "flatRate", "tieredRates", "tier", "threshold", "rate", "Infinity", "ratchet<PERSON>lause", "seasonalRates", "summer", "winter", "fixedCharges", "customerCharge", "facilityCharge", "minimumBill", "touRates", "totalEnergy", "peakEnergyPercent", "offPeakEnergyPercent", "shoulderEnergyPercent", "currentCost", "touCost", "totalEnergyKWh", "operationalEmissions", "createEmissionMeasurement", "EMISSION_FACTORS", "GRID_AVERAGE", "EmissionUnits", "KG_CO2E", "EmissionScope", "SCOPE_2", "embodiedEmissions", "SCOPE_3", "totalEmissions", "emissionsBySource", "emissions", "percentage", "emissionFactor", "emissionsTrend", "currentEmissions", "projectedEmissions", "reductionPotential", "offsetOpportunities", "potential", "cost", "implementation", "currentIntensity", "benchmarkIntensity", "scope", "energyIntensity", "current", "benchmark", "efficiencyRating", "grade", "calculateEfficiencyGrade", "ashrae901", "energyStar", "leed", "calculateLEEDPoints", "energySavings", "costSavings", "emissionReduction", "efficiency", "points", "min", "category", "energySavingsPotential", "costSavingsPotential", "complexity", "priority", "requiredActions", "expectedResults", "energyReduction", "demandReduction", "totalSystemPressure", "baseConsumption", "seasonalBreakdown", "season", "operatingHours", "costs", "peakSeasons", "opportunity", "savings", "weatherSensitivity", "temperatureCoefficient", "humidityCoefficient", "baselineTemperature", "heatingThreshold", "coolingThreshold", "now", "random", "toString", "substring", "exports", "VERSION", "Map", "HP_TO_KW", "CFM_TO_M3S", "COAL", "NATURAL_GAS", "RENEWABLE"], "sources": ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\EnergyEfficiencyAnalysisEngine.ts"], "sourcesContent": ["/**\r\n * Energy Efficiency Analysis Engine\r\n * \r\n * Comprehensive energy efficiency analysis service for Phase 3 Priority 3: Advanced System Analysis Tools\r\n * Provides energy consumption analysis, fan power optimization, lifecycle energy calculations,\r\n * and carbon footprint assessment tools for HVAC duct systems.\r\n * \r\n * @version 3.0.0\r\n * <AUTHOR> Suite Development Team\r\n */\r\n\r\nimport {\r\n  EnergyAnalysis,\r\n  EnergyConsumption,\r\n  EnergyEfficiencyMetrics,\r\n  EnergyCosts,\r\n  CarbonFootprint,\r\n  EnergyBenchmark,\r\n  EnergyOptimizationOpportunity,\r\n  SeasonalEnergyAnalysis,\r\n  EnergyMeasurement,\r\n  EnergyUnits,\r\n  TimeFrame,\r\n  SystemConfiguration,\r\n  PerformanceMetrics,\r\n  EmissionMeasurement,\r\n  EmissionUnits,\r\n  EmissionScope,\r\n  MeasurementSource\r\n} from './types/SystemAnalysisTypes';\r\n\r\nimport { SystemPerformanceAnalysisEngine } from './SystemPerformanceAnalysisEngine';\r\nimport { AirPropertiesCalculator } from './AirPropertiesCalculator';\r\n\r\n/**\r\n * Energy Efficiency Analysis Engine\r\n * \r\n * Provides comprehensive energy efficiency analysis capabilities including:\r\n * - Energy consumption breakdown and analysis\r\n * - Fan power optimization calculations\r\n * - Lifecycle energy cost projections\r\n * - Carbon footprint assessment\r\n * - Energy benchmarking and comparison\r\n * - Seasonal energy analysis\r\n */\r\nexport class EnergyEfficiencyAnalysisEngine {\r\n  private static readonly VERSION = '3.0.0';\r\n  private static readonly ENERGY_CACHE = new Map<string, EnergyAnalysis>();\r\n  \r\n  // Energy conversion constants\r\n  private static readonly KWH_TO_BTU = 3412.14;\r\n  private static readonly HP_TO_KW = 0.746;\r\n  private static readonly CFM_TO_M3S = 0.000471947;\r\n  \r\n  // Carbon emission factors (kg CO2e per kWh)\r\n  private static readonly EMISSION_FACTORS = {\r\n    GRID_AVERAGE: 0.4, // US grid average\r\n    COAL: 0.82,\r\n    NATURAL_GAS: 0.35,\r\n    RENEWABLE: 0.02\r\n  };\r\n\r\n  /**\r\n   * Perform comprehensive energy efficiency analysis\r\n   */\r\n  public static async analyzeEnergyEfficiency(\r\n    systemConfiguration: SystemConfiguration,\r\n    performanceMetrics: PerformanceMetrics,\r\n    operatingSchedule?: OperatingSchedule,\r\n    energyRates?: EnergyRates\r\n  ): Promise<EnergyAnalysis> {\r\n    try {\r\n      const analysisId = this.generateAnalysisId(systemConfiguration.id);\r\n      const timestamp = new Date();\r\n\r\n      // Calculate energy consumption breakdown\r\n      const energyConsumption = await this.calculateEnergyConsumption(\r\n        systemConfiguration,\r\n        performanceMetrics,\r\n        operatingSchedule\r\n      );\r\n\r\n      // Calculate efficiency metrics\r\n      const efficiencyMetrics = await this.calculateEnergyEfficiencyMetrics(\r\n        systemConfiguration,\r\n        performanceMetrics,\r\n        energyConsumption\r\n      );\r\n\r\n      // Calculate energy costs\r\n      const energyCosts = await this.calculateEnergyCosts(\r\n        energyConsumption,\r\n        energyRates\r\n      );\r\n\r\n      // Calculate carbon footprint\r\n      const carbonFootprint = await this.calculateCarbonFootprint(\r\n        energyConsumption,\r\n        systemConfiguration\r\n      );\r\n\r\n      // Perform benchmark comparison\r\n      const benchmarkComparison = await this.performEnergyBenchmarking(\r\n        systemConfiguration,\r\n        efficiencyMetrics\r\n      );\r\n\r\n      // Identify optimization opportunities\r\n      const optimizationOpportunities = await this.identifyOptimizationOpportunities(\r\n        systemConfiguration,\r\n        performanceMetrics,\r\n        efficiencyMetrics,\r\n        energyCosts\r\n      );\r\n\r\n      // Perform seasonal analysis\r\n      const seasonalAnalysis = await this.performSeasonalEnergyAnalysis(\r\n        systemConfiguration,\r\n        energyConsumption\r\n      );\r\n\r\n      const analysis: EnergyAnalysis = {\r\n        id: analysisId,\r\n        systemId: systemConfiguration.id,\r\n        analysisTimestamp: timestamp,\r\n        energyConsumption,\r\n        efficiencyMetrics,\r\n        energyCosts,\r\n        carbonFootprint,\r\n        benchmarkComparison,\r\n        optimizationOpportunities,\r\n        seasonalAnalysis\r\n      };\r\n\r\n      // Cache the analysis\r\n      this.ENERGY_CACHE.set(analysisId, analysis);\r\n\r\n      return analysis;\r\n\r\n    } catch (error) {\r\n      throw new Error(`Energy efficiency analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Calculate comprehensive energy consumption breakdown\r\n   */\r\n  private static async calculateEnergyConsumption(\r\n    systemConfiguration: SystemConfiguration,\r\n    performanceMetrics: PerformanceMetrics,\r\n    operatingSchedule?: OperatingSchedule\r\n  ): Promise<EnergyConsumption> {\r\n    const fanPowerKW = performanceMetrics.fanPower.value;\r\n    const designAirflow = systemConfiguration.designParameters.designAirflow;\r\n    \r\n    // Default operating schedule if not provided\r\n    const schedule = operatingSchedule || {\r\n      hoursPerDay: 12,\r\n      daysPerWeek: 5,\r\n      weeksPerYear: 50,\r\n      loadProfile: 'constant' as const\r\n    };\r\n\r\n    const annualOperatingHours = schedule.hoursPerDay * schedule.daysPerWeek * schedule.weeksPerYear;\r\n\r\n    // Calculate fan energy consumption\r\n    const fanConsumption = this.createEnergyMeasurement(\r\n      fanPowerKW * annualOperatingHours,\r\n      EnergyUnits.KWH,\r\n      TimeFrame.ANNUALLY,\r\n      MeasurementSource.CALCULATED\r\n    );\r\n\r\n    // Calculate auxiliary equipment consumption (10% of fan power typically)\r\n    const auxiliaryConsumption = this.createEnergyMeasurement(\r\n      fanPowerKW * 0.1 * annualOperatingHours,\r\n      EnergyUnits.KWH,\r\n      TimeFrame.ANNUALLY,\r\n      MeasurementSource.ESTIMATED\r\n    );\r\n\r\n    // Total consumption\r\n    const totalConsumption = this.createEnergyMeasurement(\r\n      fanConsumption.value + auxiliaryConsumption.value,\r\n      EnergyUnits.KWH,\r\n      TimeFrame.ANNUALLY,\r\n      MeasurementSource.CALCULATED\r\n    );\r\n\r\n    // Calculate time-of-day consumption profile\r\n    const timeOfDayConsumption = this.calculateTimeOfDayConsumption(\r\n      fanPowerKW,\r\n      schedule\r\n    );\r\n\r\n    // Calculate load profile\r\n    const loadProfile = this.calculateLoadProfile(fanPowerKW, schedule);\r\n\r\n    // Calculate peak demand\r\n    const peakDemand = this.calculatePeakDemand(fanPowerKW, schedule);\r\n\r\n    return {\r\n      totalConsumption,\r\n      fanConsumption,\r\n      auxiliaryConsumption,\r\n      consumptionByTimeOfDay: timeOfDayConsumption,\r\n      loadProfile,\r\n      peakDemand\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create standardized energy measurement\r\n   */\r\n  private static createEnergyMeasurement(\r\n    value: number,\r\n    units: EnergyUnits,\r\n    timeFrame: TimeFrame,\r\n    source: MeasurementSource,\r\n    accuracy: number = 0.9\r\n  ): EnergyMeasurement {\r\n    return {\r\n      value,\r\n      units,\r\n      timeFrame,\r\n      accuracy,\r\n      source\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate time-of-day energy consumption\r\n   */\r\n  private static calculateTimeOfDayConsumption(\r\n    fanPowerKW: number,\r\n    schedule: OperatingSchedule\r\n  ): TimeOfDayConsumption[] {\r\n    const consumption: TimeOfDayConsumption[] = [];\r\n    \r\n    // Simplified time-of-day profile\r\n    const hourlyProfile = [\r\n      { hour: 6, loadFactor: 0.8 },\r\n      { hour: 8, loadFactor: 1.0 },\r\n      { hour: 12, loadFactor: 0.9 },\r\n      { hour: 17, loadFactor: 1.0 },\r\n      { hour: 20, loadFactor: 0.6 },\r\n      { hour: 22, loadFactor: 0.3 }\r\n    ];\r\n\r\n    hourlyProfile.forEach(profile => {\r\n      consumption.push({\r\n        timeOfDay: `${profile.hour}:00`,\r\n        powerDemand: fanPowerKW * profile.loadFactor,\r\n        energyConsumption: fanPowerKW * profile.loadFactor * 1, // 1 hour\r\n        loadFactor: profile.loadFactor\r\n      });\r\n    });\r\n\r\n    return consumption;\r\n  }\r\n\r\n  /**\r\n   * Calculate system load profile\r\n   */\r\n  private static calculateLoadProfile(\r\n    fanPowerKW: number,\r\n    schedule: OperatingSchedule\r\n  ): LoadProfile {\r\n    return {\r\n      baseLoad: fanPowerKW * 0.3, // 30% base load\r\n      peakLoad: fanPowerKW * 1.0, // 100% peak load\r\n      averageLoad: fanPowerKW * 0.75, // 75% average load\r\n      loadFactor: 0.75, // Average/Peak\r\n      diversityFactor: 0.85, // Accounting for non-coincident peaks\r\n      demandProfile: [\r\n        { period: 'morning', demand: fanPowerKW * 0.8 },\r\n        { period: 'midday', demand: fanPowerKW * 1.0 },\r\n        { period: 'afternoon', demand: fanPowerKW * 0.9 },\r\n        { period: 'evening', demand: fanPowerKW * 0.6 }\r\n      ]\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate peak demand characteristics\r\n   */\r\n  private static calculatePeakDemand(\r\n    fanPowerKW: number,\r\n    schedule: OperatingSchedule\r\n  ): PeakDemand {\r\n    return {\r\n      peakPower: fanPowerKW,\r\n      peakTime: '14:00', // 2 PM typical peak\r\n      peakDuration: 2, // 2 hours\r\n      peakFrequency: 'daily' as const,\r\n      coincidentFactor: 0.9, // 90% coincident with utility peak\r\n      demandCharges: fanPowerKW * 15, // $15/kW typical demand charge\r\n      peakShavingPotential: fanPowerKW * 0.2 // 20% potential reduction\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate energy efficiency metrics\r\n   */\r\n  private static async calculateEnergyEfficiencyMetrics(\r\n    systemConfiguration: SystemConfiguration,\r\n    performanceMetrics: PerformanceMetrics,\r\n    energyConsumption: EnergyConsumption\r\n  ): Promise<EnergyEfficiencyMetrics> {\r\n    const designAirflow = systemConfiguration.designParameters.designAirflow;\r\n    const fanPowerKW = performanceMetrics.fanPower.value;\r\n    const systemEfficiency = performanceMetrics.systemEfficiency.value;\r\n\r\n    // Calculate Specific Fan Power (SFP)\r\n    const specificFanPower = (fanPowerKW * 1000) / designAirflow; // W/CFM\r\n\r\n    // Calculate Energy Utilization Index (EUI)\r\n    const buildingArea = 10000; // Assumed building area in sq ft\r\n    const energyUtilizationIndex = (energyConsumption.totalConsumption.value * this.KWH_TO_BTU) / buildingArea;\r\n\r\n    // Calculate power density\r\n    const powerDensity = (fanPowerKW * 1000) / buildingArea; // W/sq ft\r\n\r\n    // Calculate efficiency trend (simplified)\r\n    const efficiencyTrend = this.calculateEfficiencyTrend(systemEfficiency);\r\n\r\n    // Benchmark comparison\r\n    const benchmarkComparison = this.calculateEfficiencyBenchmark(\r\n      specificFanPower,\r\n      systemEfficiency\r\n    );\r\n\r\n    return {\r\n      overallEfficiency: systemEfficiency,\r\n      fanEfficiency: performanceMetrics.fanEfficiency.value,\r\n      systemEfficiency: systemEfficiency,\r\n      transportEfficiency: performanceMetrics.transportEfficiency.value,\r\n      specificFanPower,\r\n      energyUtilizationIndex,\r\n      powerDensity,\r\n      efficiencyTrend,\r\n      benchmarkComparison\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate efficiency trend\r\n   */\r\n  private static calculateEfficiencyTrend(currentEfficiency: number): EfficiencyTrend {\r\n    return {\r\n      currentEfficiency,\r\n      trendDirection: 'stable' as const,\r\n      trendRate: 0, // % per year\r\n      projectedEfficiency: currentEfficiency,\r\n      timeHorizon: 12, // months\r\n      confidenceLevel: 80\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate efficiency benchmark\r\n   */\r\n  private static calculateEfficiencyBenchmark(\r\n    specificFanPower: number,\r\n    systemEfficiency: number\r\n  ): EfficiencyBenchmark {\r\n    // ASHRAE 90.1 SFP limits\r\n    const ashraeSFPLimit = 1.25; // W/CFM for VAV systems\r\n    const industryAverageSFP = 1.1; // W/CFM\r\n    const bestPracticeSFP = 0.8; // W/CFM\r\n\r\n    return {\r\n      benchmarkType: 'industry_standard' as const,\r\n      benchmarkSource: 'ASHRAE 90.1',\r\n      currentValue: specificFanPower,\r\n      benchmarkValue: ashraeSFPLimit,\r\n      industryAverage: industryAverageSFP,\r\n      bestPractice: bestPracticeSFP,\r\n      percentile: this.calculateSFPPercentile(specificFanPower),\r\n      complianceStatus: specificFanPower <= ashraeSFPLimit ? 'compliant' : 'non_compliant',\r\n      improvementPotential: Math.max(0, specificFanPower - bestPracticeSFP)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate SFP percentile ranking\r\n   */\r\n  private static calculateSFPPercentile(sfp: number): number {\r\n    // Simplified percentile calculation based on typical SFP distribution\r\n    if (sfp <= 0.8) return 95;\r\n    if (sfp <= 1.0) return 80;\r\n    if (sfp <= 1.1) return 60;\r\n    if (sfp <= 1.25) return 40;\r\n    if (sfp <= 1.5) return 20;\r\n    return 5;\r\n  }\r\n\r\n  /**\r\n   * Calculate energy costs\r\n   */\r\n  private static async calculateEnergyCosts(\r\n    energyConsumption: EnergyConsumption,\r\n    energyRates?: EnergyRates\r\n  ): Promise<EnergyCosts> {\r\n    // Default energy rates if not provided\r\n    const rates = energyRates || {\r\n      energyRate: 0.12, // $/kWh\r\n      demandRate: 15.0, // $/kW\r\n      fixedRate: 25.0, // $/month\r\n      timeOfUseRates: {\r\n        peak: 0.18,\r\n        offPeak: 0.08,\r\n        shoulder: 0.12\r\n      }\r\n    };\r\n\r\n    // Calculate current costs\r\n    const energyCost = energyConsumption.totalConsumption.value * rates.energyRate;\r\n    const demandCost = energyConsumption.peakDemand.peakPower * rates.demandRate * 12; // Annual\r\n    const fixedCost = rates.fixedRate * 12; // Annual\r\n    const totalCost = energyCost + demandCost + fixedCost;\r\n\r\n    const currentCosts = {\r\n      totalCost,\r\n      energyCost,\r\n      demandCost,\r\n      fixedCost,\r\n      currency: 'USD',\r\n      timeFrame: TimeFrame.ANNUALLY\r\n    };\r\n\r\n    // Calculate projected costs (5-year projection)\r\n    const projectedCosts = this.calculateCostProjections(currentCosts, 0.03); // 3% annual escalation\r\n\r\n    // Identify cost saving opportunities\r\n    const costSavingOpportunities = this.identifyCostSavingOpportunities(\r\n      energyConsumption,\r\n      currentCosts\r\n    );\r\n\r\n    // Create utility rate structure\r\n    const utilityRateStructure = this.createUtilityRateStructure(rates);\r\n\r\n    return {\r\n      currentCosts,\r\n      projectedCosts,\r\n      costSavingOpportunities,\r\n      utilityRateStructure,\r\n      demandCharges: {\r\n        currentDemand: energyConsumption.peakDemand.peakPower,\r\n        demandRate: rates.demandRate,\r\n        annualDemandCost: demandCost,\r\n        peakShavingPotential: energyConsumption.peakDemand.peakShavingPotential,\r\n        potentialSavings: energyConsumption.peakDemand.peakShavingPotential * rates.demandRate * 12\r\n      },\r\n      timeOfUsePricing: {\r\n        enabled: true,\r\n        peakRate: rates.timeOfUseRates.peak,\r\n        offPeakRate: rates.timeOfUseRates.offPeak,\r\n        shoulderRate: rates.timeOfUseRates.shoulder,\r\n        peakHours: '12:00-18:00',\r\n        offPeakHours: '22:00-06:00',\r\n        potentialSavings: this.calculateTOUSavings(energyConsumption, rates.timeOfUseRates)\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate cost projections\r\n   */\r\n  private static calculateCostProjections(\r\n    currentCosts: any,\r\n    escalationRate: number\r\n  ): CostProjection[] {\r\n    const projections: CostProjection[] = [];\r\n    \r\n    for (let year = 1; year <= 5; year++) {\r\n      const escalationFactor = Math.pow(1 + escalationRate, year);\r\n      projections.push({\r\n        year,\r\n        totalCost: currentCosts.totalCost * escalationFactor,\r\n        energyCost: currentCosts.energyCost * escalationFactor,\r\n        demandCost: currentCosts.demandCost * escalationFactor,\r\n        fixedCost: currentCosts.fixedCost * escalationFactor,\r\n        escalationRate,\r\n        cumulativeCost: currentCosts.totalCost * ((Math.pow(1 + escalationRate, year) - 1) / escalationRate)\r\n      });\r\n    }\r\n\r\n    return projections;\r\n  }\r\n\r\n  /**\r\n   * Identify cost saving opportunities\r\n   */\r\n  private static identifyCostSavingOpportunities(\r\n    energyConsumption: EnergyConsumption,\r\n    currentCosts: any\r\n  ): CostSavingOpportunity[] {\r\n    const opportunities: CostSavingOpportunity[] = [];\r\n\r\n    // Peak demand reduction opportunity\r\n    if (energyConsumption.peakDemand.peakShavingPotential > 0) {\r\n      opportunities.push({\r\n        id: 'peak_demand_reduction',\r\n        name: 'Peak Demand Reduction',\r\n        description: 'Reduce peak demand through load scheduling and control optimization',\r\n        potentialSavings: energyConsumption.peakDemand.peakShavingPotential * 15 * 12, // $15/kW * 12 months\r\n        implementationCost: 5000,\r\n        paybackPeriod: 18, // months\r\n        savingsType: 'demand_reduction' as const,\r\n        confidence: 0.8\r\n      });\r\n    }\r\n\r\n    // Energy efficiency improvement\r\n    opportunities.push({\r\n      id: 'efficiency_improvement',\r\n      name: 'System Efficiency Improvement',\r\n      description: 'Improve overall system efficiency through equipment upgrades and optimization',\r\n      potentialSavings: currentCosts.energyCost * 0.15, // 15% energy savings\r\n      implementationCost: 15000,\r\n      paybackPeriod: 36, // months\r\n      savingsType: 'energy_reduction' as const,\r\n      confidence: 0.7\r\n    });\r\n\r\n    return opportunities;\r\n  }\r\n\r\n  /**\r\n   * Create utility rate structure\r\n   */\r\n  private static createUtilityRateStructure(rates: EnergyRates): UtilityRateStructure {\r\n    return {\r\n      rateSchedule: 'Commercial General Service',\r\n      energyCharges: {\r\n        flatRate: rates.energyRate,\r\n        tieredRates: [\r\n          { tier: 1, threshold: 1000, rate: rates.energyRate * 0.9 },\r\n          { tier: 2, threshold: 5000, rate: rates.energyRate },\r\n          { tier: 3, threshold: Infinity, rate: rates.energyRate * 1.1 }\r\n        ]\r\n      },\r\n      demandCharges: {\r\n        rate: rates.demandRate,\r\n        ratchetClause: true,\r\n        seasonalRates: {\r\n          summer: rates.demandRate * 1.2,\r\n          winter: rates.demandRate * 0.8\r\n        }\r\n      },\r\n      fixedCharges: {\r\n        customerCharge: rates.fixedRate,\r\n        facilityCharge: 0,\r\n        minimumBill: rates.fixedRate\r\n      },\r\n      timeOfUseRates: rates.timeOfUseRates\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate time-of-use savings potential\r\n   */\r\n  private static calculateTOUSavings(\r\n    energyConsumption: EnergyConsumption,\r\n    touRates: any\r\n  ): number {\r\n    // Simplified TOU savings calculation\r\n    const totalEnergy = energyConsumption.totalConsumption.value;\r\n    const peakEnergyPercent = 0.3; // 30% during peak hours\r\n    const offPeakEnergyPercent = 0.4; // 40% during off-peak hours\r\n    const shoulderEnergyPercent = 0.3; // 30% during shoulder hours\r\n\r\n    const currentCost = totalEnergy * 0.12; // Flat rate\r\n    const touCost = (totalEnergy * peakEnergyPercent * touRates.peak) +\r\n                   (totalEnergy * offPeakEnergyPercent * touRates.offPeak) +\r\n                   (totalEnergy * shoulderEnergyPercent * touRates.shoulder);\r\n\r\n    return Math.max(0, currentCost - touCost);\r\n  }\r\n\r\n  /**\r\n   * Calculate carbon footprint\r\n   */\r\n  private static async calculateCarbonFootprint(\r\n    energyConsumption: EnergyConsumption,\r\n    systemConfiguration: SystemConfiguration\r\n  ): Promise<CarbonFootprint> {\r\n    const totalEnergyKWh = energyConsumption.totalConsumption.value;\r\n\r\n    // Calculate operational emissions\r\n    const operationalEmissions = this.createEmissionMeasurement(\r\n      totalEnergyKWh * this.EMISSION_FACTORS.GRID_AVERAGE,\r\n      EmissionUnits.KG_CO2E,\r\n      TimeFrame.ANNUALLY,\r\n      EmissionScope.SCOPE_2\r\n    );\r\n\r\n    // Calculate embodied emissions (simplified)\r\n    const embodiedEmissions = this.createEmissionMeasurement(\r\n      500, // Simplified embodied carbon for HVAC system\r\n      EmissionUnits.KG_CO2E,\r\n      TimeFrame.ANNUALLY,\r\n      EmissionScope.SCOPE_3\r\n    );\r\n\r\n    // Total emissions\r\n    const totalEmissions = this.createEmissionMeasurement(\r\n      operationalEmissions.value + embodiedEmissions.value,\r\n      EmissionUnits.KG_CO2E,\r\n      TimeFrame.ANNUALLY,\r\n      EmissionScope.SCOPE_2\r\n    );\r\n\r\n    // Emissions by source\r\n    const emissionsBySource = [\r\n      {\r\n        source: 'Electricity Grid',\r\n        emissions: operationalEmissions.value,\r\n        percentage: (operationalEmissions.value / totalEmissions.value) * 100,\r\n        emissionFactor: this.EMISSION_FACTORS.GRID_AVERAGE\r\n      },\r\n      {\r\n        source: 'Embodied Carbon',\r\n        emissions: embodiedEmissions.value,\r\n        percentage: (embodiedEmissions.value / totalEmissions.value) * 100,\r\n        emissionFactor: 0\r\n      }\r\n    ];\r\n\r\n    // Emissions trend (simplified)\r\n    const emissionsTrend = {\r\n      currentEmissions: totalEmissions.value,\r\n      trendDirection: 'stable' as const,\r\n      projectedEmissions: totalEmissions.value,\r\n      reductionPotential: totalEmissions.value * 0.3, // 30% reduction potential\r\n      timeHorizon: 10 // years\r\n    };\r\n\r\n    // Offset opportunities\r\n    const offsetOpportunities = [\r\n      {\r\n        type: 'Renewable Energy',\r\n        potential: operationalEmissions.value * 0.8, // 80% offset potential\r\n        cost: operationalEmissions.value * 0.02, // $0.02/kg CO2e\r\n        implementation: 'On-site solar or renewable energy credits'\r\n      },\r\n      {\r\n        type: 'Energy Efficiency',\r\n        potential: operationalEmissions.value * 0.2, // 20% reduction potential\r\n        cost: 0, // Cost savings\r\n        implementation: 'System optimization and efficiency improvements'\r\n      }\r\n    ];\r\n\r\n    // Benchmark comparison\r\n    const benchmarkComparison = {\r\n      benchmarkType: 'Industry Average',\r\n      currentIntensity: totalEmissions.value / systemConfiguration.designParameters.designAirflow, // kg CO2e/CFM\r\n      benchmarkIntensity: 0.15, // Industry average\r\n      percentile: 60,\r\n      improvementPotential: totalEmissions.value * 0.25\r\n    };\r\n\r\n    return {\r\n      totalEmissions,\r\n      operationalEmissions,\r\n      embodiedEmissions,\r\n      emissionsBySource,\r\n      emissionsTrend,\r\n      offsetOpportunities,\r\n      benchmarkComparison\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create standardized emission measurement\r\n   */\r\n  private static createEmissionMeasurement(\r\n    value: number,\r\n    units: EmissionUnits,\r\n    timeFrame: TimeFrame,\r\n    scope: EmissionScope,\r\n    accuracy: number = 0.8\r\n  ): EmissionMeasurement {\r\n    return {\r\n      value,\r\n      units,\r\n      timeFrame,\r\n      scope,\r\n      accuracy\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Perform energy benchmarking\r\n   */\r\n  private static async performEnergyBenchmarking(\r\n    systemConfiguration: SystemConfiguration,\r\n    efficiencyMetrics: EnergyEfficiencyMetrics\r\n  ): Promise<EnergyBenchmark> {\r\n    const sfp = efficiencyMetrics.specificFanPower;\r\n    const systemEfficiency = efficiencyMetrics.systemEfficiency;\r\n\r\n    return {\r\n      benchmarkType: 'Industry Standard',\r\n      benchmarkSource: 'ASHRAE 90.1 and Industry Data',\r\n      energyIntensity: {\r\n        current: sfp,\r\n        benchmark: 1.25, // ASHRAE 90.1 limit\r\n        industryAverage: 1.1,\r\n        bestPractice: 0.8,\r\n        percentile: this.calculateSFPPercentile(sfp)\r\n      },\r\n      efficiencyRating: {\r\n        current: systemEfficiency,\r\n        benchmark: 80,\r\n        industryAverage: 82,\r\n        bestPractice: 90,\r\n        grade: this.calculateEfficiencyGrade(systemEfficiency)\r\n      },\r\n      complianceStatus: {\r\n        ashrae901: sfp <= 1.25 ? 'compliant' : 'non_compliant',\r\n        energyStar: systemEfficiency >= 85 ? 'qualified' : 'not_qualified',\r\n        leed: this.calculateLEEDPoints(sfp, systemEfficiency)\r\n      },\r\n      improvementPotential: {\r\n        energySavings: Math.max(0, (sfp - 0.8) / sfp * 100), // % savings potential\r\n        costSavings: 0, // Would be calculated based on energy costs\r\n        emissionReduction: 0 // Would be calculated based on carbon intensity\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate efficiency grade\r\n   */\r\n  private static calculateEfficiencyGrade(efficiency: number): string {\r\n    if (efficiency >= 90) return 'A+';\r\n    if (efficiency >= 85) return 'A';\r\n    if (efficiency >= 80) return 'B';\r\n    if (efficiency >= 75) return 'C';\r\n    if (efficiency >= 70) return 'D';\r\n    return 'F';\r\n  }\r\n\r\n  /**\r\n   * Calculate LEED points\r\n   */\r\n  private static calculateLEEDPoints(sfp: number, efficiency: number): number {\r\n    let points = 0;\r\n\r\n    // LEED points for fan power efficiency\r\n    if (sfp <= 0.8) points += 2;\r\n    else if (sfp <= 1.0) points += 1;\r\n\r\n    // LEED points for system efficiency\r\n    if (efficiency >= 85) points += 1;\r\n\r\n    return Math.min(points, 3); // Maximum 3 points\r\n  }\r\n\r\n  /**\r\n   * Identify optimization opportunities\r\n   */\r\n  private static async identifyOptimizationOpportunities(\r\n    systemConfiguration: SystemConfiguration,\r\n    performanceMetrics: PerformanceMetrics,\r\n    efficiencyMetrics: EnergyEfficiencyMetrics,\r\n    energyCosts: EnergyCosts\r\n  ): Promise<EnergyOptimizationOpportunity[]> {\r\n    const opportunities: EnergyOptimizationOpportunity[] = [];\r\n\r\n    // Fan speed optimization\r\n    if (efficiencyMetrics.specificFanPower > 1.0) {\r\n      opportunities.push({\r\n        id: 'fan_speed_optimization',\r\n        name: 'Fan Speed Optimization',\r\n        description: 'Optimize fan speed control to reduce energy consumption while maintaining comfort',\r\n        category: 'Control Optimization',\r\n        energySavingsPotential: 15, // %\r\n        costSavingsPotential: energyCosts.currentCosts.energyCost * 0.15,\r\n        implementationCost: 3000,\r\n        paybackPeriod: 12, // months\r\n        complexity: 'Low',\r\n        priority: 'High',\r\n        requiredActions: [\r\n          'Install VFD if not present',\r\n          'Implement demand-based control',\r\n          'Optimize control sequences'\r\n        ],\r\n        expectedResults: {\r\n          energyReduction: 15,\r\n          demandReduction: 10,\r\n          emissionReduction: 12,\r\n          costSavings: energyCosts.currentCosts.energyCost * 0.15\r\n        }\r\n      });\r\n    }\r\n\r\n    // Duct system optimization\r\n    if (performanceMetrics.totalSystemPressure.value > 3.0) {\r\n      opportunities.push({\r\n        id: 'duct_optimization',\r\n        name: 'Duct System Optimization',\r\n        description: 'Reduce system pressure losses through duct sizing and layout optimization',\r\n        category: 'System Design',\r\n        energySavingsPotential: 20, // %\r\n        costSavingsPotential: energyCosts.currentCosts.energyCost * 0.20,\r\n        implementationCost: 15000,\r\n        paybackPeriod: 36, // months\r\n        complexity: 'High',\r\n        priority: 'Medium',\r\n        requiredActions: [\r\n          'Analyze duct sizing',\r\n          'Identify pressure loss sources',\r\n          'Redesign critical sections',\r\n          'Seal ductwork leaks'\r\n        ],\r\n        expectedResults: {\r\n          energyReduction: 20,\r\n          demandReduction: 18,\r\n          emissionReduction: 20,\r\n          costSavings: energyCosts.currentCosts.energyCost * 0.20\r\n        }\r\n      });\r\n    }\r\n\r\n    // Filter optimization\r\n    opportunities.push({\r\n      id: 'filter_optimization',\r\n      name: 'Filter System Optimization',\r\n      description: 'Optimize filter selection and maintenance to reduce pressure drop',\r\n      category: 'Maintenance',\r\n      energySavingsPotential: 8, // %\r\n      costSavingsPotential: energyCosts.currentCosts.energyCost * 0.08,\r\n      implementationCost: 2000,\r\n      paybackPeriod: 18, // months\r\n      complexity: 'Low',\r\n      priority: 'Medium',\r\n      requiredActions: [\r\n        'Evaluate filter efficiency vs pressure drop',\r\n        'Implement pressure monitoring',\r\n        'Optimize replacement schedule'\r\n      ],\r\n      expectedResults: {\r\n        energyReduction: 8,\r\n        demandReduction: 5,\r\n        emissionReduction: 8,\r\n        costSavings: energyCosts.currentCosts.energyCost * 0.08\r\n      }\r\n    });\r\n\r\n    return opportunities;\r\n  }\r\n\r\n  /**\r\n   * Perform seasonal energy analysis\r\n   */\r\n  private static async performSeasonalEnergyAnalysis(\r\n    systemConfiguration: SystemConfiguration,\r\n    energyConsumption: EnergyConsumption\r\n  ): Promise<SeasonalEnergyAnalysis> {\r\n    const baseConsumption = energyConsumption.totalConsumption.value;\r\n\r\n    return {\r\n      seasonalBreakdown: [\r\n        {\r\n          season: 'Spring',\r\n          energyConsumption: baseConsumption * 0.22, // 22% of annual\r\n          averageLoad: energyConsumption.loadProfile.averageLoad * 0.8,\r\n          peakLoad: energyConsumption.loadProfile.peakLoad * 0.7,\r\n          operatingHours: 2000,\r\n          efficiency: 85,\r\n          costs: baseConsumption * 0.22 * 0.12 // $0.12/kWh\r\n        },\r\n        {\r\n          season: 'Summer',\r\n          energyConsumption: baseConsumption * 0.35, // 35% of annual\r\n          averageLoad: energyConsumption.loadProfile.averageLoad * 1.2,\r\n          peakLoad: energyConsumption.loadProfile.peakLoad * 1.0,\r\n          operatingHours: 2500,\r\n          efficiency: 80, // Lower efficiency due to higher loads\r\n          costs: baseConsumption * 0.35 * 0.15 // Higher summer rates\r\n        },\r\n        {\r\n          season: 'Fall',\r\n          energyConsumption: baseConsumption * 0.25, // 25% of annual\r\n          averageLoad: energyConsumption.loadProfile.averageLoad * 0.9,\r\n          peakLoad: energyConsumption.loadProfile.peakLoad * 0.8,\r\n          operatingHours: 2200,\r\n          efficiency: 83,\r\n          costs: baseConsumption * 0.25 * 0.12\r\n        },\r\n        {\r\n          season: 'Winter',\r\n          energyConsumption: baseConsumption * 0.18, // 18% of annual\r\n          averageLoad: energyConsumption.loadProfile.averageLoad * 0.7,\r\n          peakLoad: energyConsumption.loadProfile.peakLoad * 0.6,\r\n          operatingHours: 1800,\r\n          efficiency: 87, // Higher efficiency at lower loads\r\n          costs: baseConsumption * 0.18 * 0.11 // Lower winter rates\r\n        }\r\n      ],\r\n      peakSeasons: ['Summer', 'Fall'],\r\n      optimizationOpportunities: [\r\n        {\r\n          season: 'Summer',\r\n          opportunity: 'Peak load management',\r\n          potential: 'Reduce peak demand by 15% through load scheduling',\r\n          savings: 2500\r\n        },\r\n        {\r\n          season: 'Winter',\r\n          opportunity: 'Extended economizer operation',\r\n          potential: 'Increase free cooling hours by 20%',\r\n          savings: 800\r\n        }\r\n      ],\r\n      weatherSensitivity: {\r\n        temperatureCoefficient: 0.02, // 2% change per degree F\r\n        humidityCoefficient: 0.005, // 0.5% change per % RH\r\n        baselineTemperature: 65, // °F\r\n        heatingThreshold: 55, // °F\r\n        coolingThreshold: 75 // °F\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Generate unique analysis ID\r\n   */\r\n  private static generateAnalysisId(systemId: string): string {\r\n    const timestamp = Date.now();\r\n    const random = Math.random().toString(36).substring(2, 8);\r\n    return `energy_analysis_${systemId}_${timestamp}_${random}`;\r\n  }\r\n}\r\n\r\n// Supporting interfaces\r\ninterface OperatingSchedule {\r\n  hoursPerDay: number;\r\n  daysPerWeek: number;\r\n  weeksPerYear: number;\r\n  loadProfile: 'constant' | 'variable' | 'scheduled';\r\n}\r\n\r\ninterface EnergyRates {\r\n  energyRate: number; // $/kWh\r\n  demandRate: number; // $/kW\r\n  fixedRate: number; // $/month\r\n  timeOfUseRates: {\r\n    peak: number;\r\n    offPeak: number;\r\n    shoulder: number;\r\n  };\r\n}\r\n\r\ninterface TimeOfDayConsumption {\r\n  timeOfDay: string;\r\n  powerDemand: number; // kW\r\n  energyConsumption: number; // kWh\r\n  loadFactor: number;\r\n}\r\n\r\ninterface LoadProfile {\r\n  baseLoad: number; // kW\r\n  peakLoad: number; // kW\r\n  averageLoad: number; // kW\r\n  loadFactor: number;\r\n  diversityFactor: number;\r\n  demandProfile: Array<{\r\n    period: string;\r\n    demand: number;\r\n  }>;\r\n}\r\n\r\ninterface PeakDemand {\r\n  peakPower: number; // kW\r\n  peakTime: string;\r\n  peakDuration: number; // hours\r\n  peakFrequency: 'daily' | 'weekly' | 'seasonal';\r\n  coincidentFactor: number;\r\n  demandCharges: number; // $/month\r\n  peakShavingPotential: number; // kW\r\n}\r\n\r\ninterface EfficiencyTrend {\r\n  currentEfficiency: number;\r\n  trendDirection: 'improving' | 'stable' | 'degrading';\r\n  trendRate: number; // % per year\r\n  projectedEfficiency: number;\r\n  timeHorizon: number; // months\r\n  confidenceLevel: number;\r\n}\r\n\r\ninterface EfficiencyBenchmark {\r\n  benchmarkType: string;\r\n  benchmarkSource: string;\r\n  currentValue: number;\r\n  benchmarkValue: number;\r\n  industryAverage: number;\r\n  bestPractice: number;\r\n  percentile: number;\r\n  complianceStatus: 'compliant' | 'non_compliant';\r\n  improvementPotential: number;\r\n}\r\n\r\ninterface CostProjection {\r\n  year: number;\r\n  totalCost: number;\r\n  energyCost: number;\r\n  demandCost: number;\r\n  fixedCost: number;\r\n  escalationRate: number;\r\n  cumulativeCost: number;\r\n}\r\n\r\ninterface CostSavingOpportunity {\r\n  id: string;\r\n  name: string;\r\n  description: string;\r\n  potentialSavings: number; // $/year\r\n  implementationCost: number;\r\n  paybackPeriod: number; // months\r\n  savingsType: 'energy_reduction' | 'demand_reduction' | 'rate_optimization';\r\n  confidence: number;\r\n}\r\n\r\ninterface UtilityRateStructure {\r\n  rateSchedule: string;\r\n  energyCharges: any;\r\n  demandCharges: any;\r\n  fixedCharges: any;\r\n  timeOfUseRates: any;\r\n}\r\n"], "mappings": ";;AAAA;;;;;;;;;;AAAA;AAAA,SAAAA,cAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;MAAA;QAAAC,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAO,SAAA;MAAA;QAAAD,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;IAAA;IAAAW,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,cAAA;MAAAC,IAAA;MAAAC,QAAA;MAAAC,KAAA;MAAAC,OAAA;MAAAC,cAAA;MAAAC,OAAA;IAAA;IAAAC,eAAA;IAAA5B,IAAA;EAAA;EAAA,IAAA6B,QAAA,GAAA5B,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAA0B,QAAA,CAAA9B,IAAA,KAAA8B,QAAA,CAAA9B,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAA6B,QAAA,CAAA9B,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAA0B,cAAA,GAAAD,QAAA,CAAA9B,IAAA;EAAA;;;;;;;;;;;;;;;;AAWA,MAAAgC,qBAAA;AAAA;AAAA,CAAAjC,aAAA,GAAAoB,CAAA,OAAAc,OAAA;AAuBA;;;;;;;;;;;AAWA,MAAaC,8BAA8B;EAiBzC;;;EAGO,aAAaC,uBAAuBA,CACzCC,mBAAwC,EACxCC,kBAAsC,EACtCC,iBAAqC,EACrCC,WAAyB;IAAA;IAAAxC,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAEzB,IAAI;MACF,MAAMqB,UAAU;MAAA;MAAA,CAAAzC,aAAA,GAAAoB,CAAA,OAAG,IAAI,CAACsB,kBAAkB,CAACL,mBAAmB,CAACM,EAAE,CAAC;MAClE,MAAMC,SAAS;MAAA;MAAA,CAAA5C,aAAA,GAAAoB,CAAA,OAAG,IAAIyB,IAAI,EAAE;MAE5B;MACA,MAAMC,iBAAiB;MAAA;MAAA,CAAA9C,aAAA,GAAAoB,CAAA,OAAG,MAAM,IAAI,CAAC2B,0BAA0B,CAC7DV,mBAAmB,EACnBC,kBAAkB,EAClBC,iBAAiB,CAClB;MAED;MACA,MAAMS,iBAAiB;MAAA;MAAA,CAAAhD,aAAA,GAAAoB,CAAA,OAAG,MAAM,IAAI,CAAC6B,gCAAgC,CACnEZ,mBAAmB,EACnBC,kBAAkB,EAClBQ,iBAAiB,CAClB;MAED;MACA,MAAMI,WAAW;MAAA;MAAA,CAAAlD,aAAA,GAAAoB,CAAA,OAAG,MAAM,IAAI,CAAC+B,oBAAoB,CACjDL,iBAAiB,EACjBN,WAAW,CACZ;MAED;MACA,MAAMY,eAAe;MAAA;MAAA,CAAApD,aAAA,GAAAoB,CAAA,OAAG,MAAM,IAAI,CAACiC,wBAAwB,CACzDP,iBAAiB,EACjBT,mBAAmB,CACpB;MAED;MACA,MAAMiB,mBAAmB;MAAA;MAAA,CAAAtD,aAAA,GAAAoB,CAAA,QAAG,MAAM,IAAI,CAACmC,yBAAyB,CAC9DlB,mBAAmB,EACnBW,iBAAiB,CAClB;MAED;MACA,MAAMQ,yBAAyB;MAAA;MAAA,CAAAxD,aAAA,GAAAoB,CAAA,QAAG,MAAM,IAAI,CAACqC,iCAAiC,CAC5EpB,mBAAmB,EACnBC,kBAAkB,EAClBU,iBAAiB,EACjBE,WAAW,CACZ;MAED;MACA,MAAMQ,gBAAgB;MAAA;MAAA,CAAA1D,aAAA,GAAAoB,CAAA,QAAG,MAAM,IAAI,CAACuC,6BAA6B,CAC/DtB,mBAAmB,EACnBS,iBAAiB,CAClB;MAED,MAAMc,QAAQ;MAAA;MAAA,CAAA5D,aAAA,GAAAoB,CAAA,QAAmB;QAC/BuB,EAAE,EAAEF,UAAU;QACdoB,QAAQ,EAAExB,mBAAmB,CAACM,EAAE;QAChCmB,iBAAiB,EAAElB,SAAS;QAC5BE,iBAAiB;QACjBE,iBAAiB;QACjBE,WAAW;QACXE,eAAe;QACfE,mBAAmB;QACnBE,yBAAyB;QACzBE;OACD;MAED;MAAA;MAAA1D,aAAA,GAAAoB,CAAA;MACA,IAAI,CAAC2C,YAAY,CAACC,GAAG,CAACvB,UAAU,EAAEmB,QAAQ,CAAC;MAAC;MAAA5D,aAAA,GAAAoB,CAAA;MAE5C,OAAOwC,QAAQ;IAEjB,CAAC,CAAC,OAAOK,KAAK,EAAE;MAAA;MAAAjE,aAAA,GAAAoB,CAAA;MACd,MAAM,IAAI8C,KAAK,CAAC,sCAAsCD,KAAK,YAAYC,KAAK;MAAA;MAAA,CAAAlE,aAAA,GAAAsB,CAAA,UAAG2C,KAAK,CAACE,OAAO;MAAA;MAAA,CAAAnE,aAAA,GAAAsB,CAAA,UAAG,eAAe,GAAE,CAAC;IACnH;EACF;EAEA;;;EAGQ,aAAayB,0BAA0BA,CAC7CV,mBAAwC,EACxCC,kBAAsC,EACtCC,iBAAqC;IAAA;IAAAvC,aAAA,GAAAqB,CAAA;IAErC,MAAM+C,UAAU;IAAA;IAAA,CAAApE,aAAA,GAAAoB,CAAA,QAAGkB,kBAAkB,CAAC+B,QAAQ,CAACC,KAAK;IACpD,MAAMC,aAAa;IAAA;IAAA,CAAAvE,aAAA,GAAAoB,CAAA,QAAGiB,mBAAmB,CAACmC,gBAAgB,CAACD,aAAa;IAExE;IACA,MAAME,QAAQ;IAAA;IAAA,CAAAzE,aAAA,GAAAoB,CAAA;IAAG;IAAA,CAAApB,aAAA,GAAAsB,CAAA,UAAAiB,iBAAiB;IAAA;IAAA,CAAAvC,aAAA,GAAAsB,CAAA,UAAI;MACpCoD,WAAW,EAAE,EAAE;MACfC,WAAW,EAAE,CAAC;MACdC,YAAY,EAAE,EAAE;MAChBC,WAAW,EAAE;KACd;IAED,MAAMC,oBAAoB;IAAA;IAAA,CAAA9E,aAAA,GAAAoB,CAAA,QAAGqD,QAAQ,CAACC,WAAW,GAAGD,QAAQ,CAACE,WAAW,GAAGF,QAAQ,CAACG,YAAY;IAEhG;IACA,MAAMG,cAAc;IAAA;IAAA,CAAA/E,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAAC4D,uBAAuB,CACjDZ,UAAU,GAAGU,oBAAoB,EACjC7C,qBAAA,CAAAgD,WAAW,CAACC,GAAG,EACfjD,qBAAA,CAAAkD,SAAS,CAACC,QAAQ,EAClBnD,qBAAA,CAAAoD,iBAAiB,CAACC,UAAU,CAC7B;IAED;IACA,MAAMC,oBAAoB;IAAA;IAAA,CAAAvF,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAAC4D,uBAAuB,CACvDZ,UAAU,GAAG,GAAG,GAAGU,oBAAoB,EACvC7C,qBAAA,CAAAgD,WAAW,CAACC,GAAG,EACfjD,qBAAA,CAAAkD,SAAS,CAACC,QAAQ,EAClBnD,qBAAA,CAAAoD,iBAAiB,CAACG,SAAS,CAC5B;IAED;IACA,MAAMC,gBAAgB;IAAA;IAAA,CAAAzF,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAAC4D,uBAAuB,CACnDD,cAAc,CAACT,KAAK,GAAGiB,oBAAoB,CAACjB,KAAK,EACjDrC,qBAAA,CAAAgD,WAAW,CAACC,GAAG,EACfjD,qBAAA,CAAAkD,SAAS,CAACC,QAAQ,EAClBnD,qBAAA,CAAAoD,iBAAiB,CAACC,UAAU,CAC7B;IAED;IACA,MAAMI,oBAAoB;IAAA;IAAA,CAAA1F,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAACuE,6BAA6B,CAC7DvB,UAAU,EACVK,QAAQ,CACT;IAED;IACA,MAAMI,WAAW;IAAA;IAAA,CAAA7E,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAACwE,oBAAoB,CAACxB,UAAU,EAAEK,QAAQ,CAAC;IAEnE;IACA,MAAMoB,UAAU;IAAA;IAAA,CAAA7F,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAAC0E,mBAAmB,CAAC1B,UAAU,EAAEK,QAAQ,CAAC;IAAC;IAAAzE,aAAA,GAAAoB,CAAA;IAElE,OAAO;MACLqE,gBAAgB;MAChBV,cAAc;MACdQ,oBAAoB;MACpBQ,sBAAsB,EAAEL,oBAAoB;MAC5Cb,WAAW;MACXgB;KACD;EACH;EAEA;;;EAGQ,OAAOb,uBAAuBA,CACpCV,KAAa,EACb0B,KAAkB,EAClBC,SAAoB,EACpBC,MAAyB,EACzBC,QAAA;EAAA;EAAA,CAAAnG,aAAA,GAAAsB,CAAA,UAAmB,GAAG;IAAA;IAAAtB,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAEtB,OAAO;MACLkD,KAAK;MACL0B,KAAK;MACLC,SAAS;MACTE,QAAQ;MACRD;KACD;EACH;EAEA;;;EAGQ,OAAOP,6BAA6BA,CAC1CvB,UAAkB,EAClBK,QAA2B;IAAA;IAAAzE,aAAA,GAAAqB,CAAA;IAE3B,MAAM+E,WAAW;IAAA;IAAA,CAAApG,aAAA,GAAAoB,CAAA,QAA2B,EAAE;IAE9C;IACA,MAAMiF,aAAa;IAAA;IAAA,CAAArG,aAAA,GAAAoB,CAAA,QAAG,CACpB;MAAEkF,IAAI,EAAE,CAAC;MAAEC,UAAU,EAAE;IAAG,CAAE,EAC5B;MAAED,IAAI,EAAE,CAAC;MAAEC,UAAU,EAAE;IAAG,CAAE,EAC5B;MAAED,IAAI,EAAE,EAAE;MAAEC,UAAU,EAAE;IAAG,CAAE,EAC7B;MAAED,IAAI,EAAE,EAAE;MAAEC,UAAU,EAAE;IAAG,CAAE,EAC7B;MAAED,IAAI,EAAE,EAAE;MAAEC,UAAU,EAAE;IAAG,CAAE,EAC7B;MAAED,IAAI,EAAE,EAAE;MAAEC,UAAU,EAAE;IAAG,CAAE,CAC9B;IAAC;IAAAvG,aAAA,GAAAoB,CAAA;IAEFiF,aAAa,CAACG,OAAO,CAACC,OAAO,IAAG;MAAA;MAAAzG,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MAC9BgF,WAAW,CAACM,IAAI,CAAC;QACfC,SAAS,EAAE,GAAGF,OAAO,CAACH,IAAI,KAAK;QAC/BM,WAAW,EAAExC,UAAU,GAAGqC,OAAO,CAACF,UAAU;QAC5CzD,iBAAiB,EAAEsB,UAAU,GAAGqC,OAAO,CAACF,UAAU,GAAG,CAAC;QAAE;QACxDA,UAAU,EAAEE,OAAO,CAACF;OACrB,CAAC;IACJ,CAAC,CAAC;IAAC;IAAAvG,aAAA,GAAAoB,CAAA;IAEH,OAAOgF,WAAW;EACpB;EAEA;;;EAGQ,OAAOR,oBAAoBA,CACjCxB,UAAkB,EAClBK,QAA2B;IAAA;IAAAzE,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAE3B,OAAO;MACLyF,QAAQ,EAAEzC,UAAU,GAAG,GAAG;MAAE;MAC5B0C,QAAQ,EAAE1C,UAAU,GAAG,GAAG;MAAE;MAC5B2C,WAAW,EAAE3C,UAAU,GAAG,IAAI;MAAE;MAChCmC,UAAU,EAAE,IAAI;MAAE;MAClBS,eAAe,EAAE,IAAI;MAAE;MACvBC,aAAa,EAAE,CACb;QAAEC,MAAM,EAAE,SAAS;QAAEC,MAAM,EAAE/C,UAAU,GAAG;MAAG,CAAE,EAC/C;QAAE8C,MAAM,EAAE,QAAQ;QAAEC,MAAM,EAAE/C,UAAU,GAAG;MAAG,CAAE,EAC9C;QAAE8C,MAAM,EAAE,WAAW;QAAEC,MAAM,EAAE/C,UAAU,GAAG;MAAG,CAAE,EACjD;QAAE8C,MAAM,EAAE,SAAS;QAAEC,MAAM,EAAE/C,UAAU,GAAG;MAAG,CAAE;KAElD;EACH;EAEA;;;EAGQ,OAAO0B,mBAAmBA,CAChC1B,UAAkB,EAClBK,QAA2B;IAAA;IAAAzE,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAE3B,OAAO;MACLgG,SAAS,EAAEhD,UAAU;MACrBiD,QAAQ,EAAE,OAAO;MAAE;MACnBC,YAAY,EAAE,CAAC;MAAE;MACjBC,aAAa,EAAE,OAAgB;MAC/BC,gBAAgB,EAAE,GAAG;MAAE;MACvBC,aAAa,EAAErD,UAAU,GAAG,EAAE;MAAE;MAChCsD,oBAAoB,EAAEtD,UAAU,GAAG,GAAG,CAAC;KACxC;EACH;EAEA;;;EAGQ,aAAanB,gCAAgCA,CACnDZ,mBAAwC,EACxCC,kBAAsC,EACtCQ,iBAAoC;IAAA;IAAA9C,aAAA,GAAAqB,CAAA;IAEpC,MAAMkD,aAAa;IAAA;IAAA,CAAAvE,aAAA,GAAAoB,CAAA,QAAGiB,mBAAmB,CAACmC,gBAAgB,CAACD,aAAa;IACxE,MAAMH,UAAU;IAAA;IAAA,CAAApE,aAAA,GAAAoB,CAAA,QAAGkB,kBAAkB,CAAC+B,QAAQ,CAACC,KAAK;IACpD,MAAMqD,gBAAgB;IAAA;IAAA,CAAA3H,aAAA,GAAAoB,CAAA,QAAGkB,kBAAkB,CAACqF,gBAAgB,CAACrD,KAAK;IAElE;IACA,MAAMsD,gBAAgB;IAAA;IAAA,CAAA5H,aAAA,GAAAoB,CAAA,QAAIgD,UAAU,GAAG,IAAI,GAAIG,aAAa,EAAC,CAAC;IAE9D;IACA,MAAMsD,YAAY;IAAA;IAAA,CAAA7H,aAAA,GAAAoB,CAAA,QAAG,KAAK,EAAC,CAAC;IAC5B,MAAM0G,sBAAsB;IAAA;IAAA,CAAA9H,aAAA,GAAAoB,CAAA,QAAI0B,iBAAiB,CAAC2C,gBAAgB,CAACnB,KAAK,GAAG,IAAI,CAACyD,UAAU,GAAIF,YAAY;IAE1G;IACA,MAAMG,YAAY;IAAA;IAAA,CAAAhI,aAAA,GAAAoB,CAAA,QAAIgD,UAAU,GAAG,IAAI,GAAIyD,YAAY,EAAC,CAAC;IAEzD;IACA,MAAMI,eAAe;IAAA;IAAA,CAAAjI,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAAC8G,wBAAwB,CAACP,gBAAgB,CAAC;IAEvE;IACA,MAAMrE,mBAAmB;IAAA;IAAA,CAAAtD,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAAC+G,4BAA4B,CAC3DP,gBAAgB,EAChBD,gBAAgB,CACjB;IAAC;IAAA3H,aAAA,GAAAoB,CAAA;IAEF,OAAO;MACLgH,iBAAiB,EAAET,gBAAgB;MACnCU,aAAa,EAAE/F,kBAAkB,CAAC+F,aAAa,CAAC/D,KAAK;MACrDqD,gBAAgB,EAAEA,gBAAgB;MAClCW,mBAAmB,EAAEhG,kBAAkB,CAACgG,mBAAmB,CAAChE,KAAK;MACjEsD,gBAAgB;MAChBE,sBAAsB;MACtBE,YAAY;MACZC,eAAe;MACf3E;KACD;EACH;EAEA;;;EAGQ,OAAO4E,wBAAwBA,CAACK,iBAAyB;IAAA;IAAAvI,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAC/D,OAAO;MACLmH,iBAAiB;MACjBC,cAAc,EAAE,QAAiB;MACjCC,SAAS,EAAE,CAAC;MAAE;MACdC,mBAAmB,EAAEH,iBAAiB;MACtCI,WAAW,EAAE,EAAE;MAAE;MACjBC,eAAe,EAAE;KAClB;EACH;EAEA;;;EAGQ,OAAOT,4BAA4BA,CACzCP,gBAAwB,EACxBD,gBAAwB;IAAA;IAAA3H,aAAA,GAAAqB,CAAA;IAExB;IACA,MAAMwH,cAAc;IAAA;IAAA,CAAA7I,aAAA,GAAAoB,CAAA,QAAG,IAAI,EAAC,CAAC;IAC7B,MAAM0H,kBAAkB;IAAA;IAAA,CAAA9I,aAAA,GAAAoB,CAAA,QAAG,GAAG,EAAC,CAAC;IAChC,MAAM2H,eAAe;IAAA;IAAA,CAAA/I,aAAA,GAAAoB,CAAA,QAAG,GAAG,EAAC,CAAC;IAAA;IAAApB,aAAA,GAAAoB,CAAA;IAE7B,OAAO;MACL4H,aAAa,EAAE,mBAA4B;MAC3CC,eAAe,EAAE,aAAa;MAC9BC,YAAY,EAAEtB,gBAAgB;MAC9BuB,cAAc,EAAEN,cAAc;MAC9BO,eAAe,EAAEN,kBAAkB;MACnCO,YAAY,EAAEN,eAAe;MAC7BO,UAAU,EAAE,IAAI,CAACC,sBAAsB,CAAC3B,gBAAgB,CAAC;MACzD4B,gBAAgB,EAAE5B,gBAAgB,IAAIiB,cAAc;MAAA;MAAA,CAAA7I,aAAA,GAAAsB,CAAA,UAAG,WAAW;MAAA;MAAA,CAAAtB,aAAA,GAAAsB,CAAA,UAAG,eAAe;MACpFmI,oBAAoB,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE/B,gBAAgB,GAAGmB,eAAe;KACrE;EACH;EAEA;;;EAGQ,OAAOQ,sBAAsBA,CAACK,GAAW;IAAA;IAAA5J,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAC/C;IACA,IAAIwI,GAAG,IAAI,GAAG,EAAE;MAAA;MAAA5J,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA,OAAO,EAAE;IAAA,CAAC;IAAA;IAAA;MAAApB,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAC1B,IAAIwI,GAAG,IAAI,GAAG,EAAE;MAAA;MAAA5J,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA,OAAO,EAAE;IAAA,CAAC;IAAA;IAAA;MAAApB,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAC1B,IAAIwI,GAAG,IAAI,GAAG,EAAE;MAAA;MAAA5J,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA,OAAO,EAAE;IAAA,CAAC;IAAA;IAAA;MAAApB,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAC1B,IAAIwI,GAAG,IAAI,IAAI,EAAE;MAAA;MAAA5J,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA,OAAO,EAAE;IAAA,CAAC;IAAA;IAAA;MAAApB,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAC3B,IAAIwI,GAAG,IAAI,GAAG,EAAE;MAAA;MAAA5J,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA,OAAO,EAAE;IAAA,CAAC;IAAA;IAAA;MAAApB,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAC1B,OAAO,CAAC;EACV;EAEA;;;EAGQ,aAAa+B,oBAAoBA,CACvCL,iBAAoC,EACpCN,WAAyB;IAAA;IAAAxC,aAAA,GAAAqB,CAAA;IAEzB;IACA,MAAMwI,KAAK;IAAA;IAAA,CAAA7J,aAAA,GAAAoB,CAAA;IAAG;IAAA,CAAApB,aAAA,GAAAsB,CAAA,UAAAkB,WAAW;IAAA;IAAA,CAAAxC,aAAA,GAAAsB,CAAA,UAAI;MAC3BwI,UAAU,EAAE,IAAI;MAAE;MAClBC,UAAU,EAAE,IAAI;MAAE;MAClBC,SAAS,EAAE,IAAI;MAAE;MACjBC,cAAc,EAAE;QACdC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,IAAI;QACbC,QAAQ,EAAE;;KAEb;IAED;IACA,MAAMC,UAAU;IAAA;IAAA,CAAArK,aAAA,GAAAoB,CAAA,QAAG0B,iBAAiB,CAAC2C,gBAAgB,CAACnB,KAAK,GAAGuF,KAAK,CAACC,UAAU;IAC9E,MAAMQ,UAAU;IAAA;IAAA,CAAAtK,aAAA,GAAAoB,CAAA,QAAG0B,iBAAiB,CAAC+C,UAAU,CAACuB,SAAS,GAAGyC,KAAK,CAACE,UAAU,GAAG,EAAE,EAAC,CAAC;IACnF,MAAMQ,SAAS;IAAA;IAAA,CAAAvK,aAAA,GAAAoB,CAAA,QAAGyI,KAAK,CAACG,SAAS,GAAG,EAAE,EAAC,CAAC;IACxC,MAAMQ,SAAS;IAAA;IAAA,CAAAxK,aAAA,GAAAoB,CAAA,QAAGiJ,UAAU,GAAGC,UAAU,GAAGC,SAAS;IAErD,MAAME,YAAY;IAAA;IAAA,CAAAzK,aAAA,GAAAoB,CAAA,QAAG;MACnBoJ,SAAS;MACTH,UAAU;MACVC,UAAU;MACVC,SAAS;MACTG,QAAQ,EAAE,KAAK;MACfzE,SAAS,EAAEhE,qBAAA,CAAAkD,SAAS,CAACC;KACtB;IAED;IACA,MAAMuF,cAAc;IAAA;IAAA,CAAA3K,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAACwJ,wBAAwB,CAACH,YAAY,EAAE,IAAI,CAAC,EAAC,CAAC;IAE1E;IACA,MAAMI,uBAAuB;IAAA;IAAA,CAAA7K,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAAC0J,+BAA+B,CAClEhI,iBAAiB,EACjB2H,YAAY,CACb;IAED;IACA,MAAMM,oBAAoB;IAAA;IAAA,CAAA/K,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAAC4J,0BAA0B,CAACnB,KAAK,CAAC;IAAC;IAAA7J,aAAA,GAAAoB,CAAA;IAEpE,OAAO;MACLqJ,YAAY;MACZE,cAAc;MACdE,uBAAuB;MACvBE,oBAAoB;MACpBtD,aAAa,EAAE;QACbwD,aAAa,EAAEnI,iBAAiB,CAAC+C,UAAU,CAACuB,SAAS;QACrD2C,UAAU,EAAEF,KAAK,CAACE,UAAU;QAC5BmB,gBAAgB,EAAEZ,UAAU;QAC5B5C,oBAAoB,EAAE5E,iBAAiB,CAAC+C,UAAU,CAAC6B,oBAAoB;QACvEyD,gBAAgB,EAAErI,iBAAiB,CAAC+C,UAAU,CAAC6B,oBAAoB,GAAGmC,KAAK,CAACE,UAAU,GAAG;OAC1F;MACDqB,gBAAgB,EAAE;QAChBC,OAAO,EAAE,IAAI;QACbC,QAAQ,EAAEzB,KAAK,CAACI,cAAc,CAACC,IAAI;QACnCqB,WAAW,EAAE1B,KAAK,CAACI,cAAc,CAACE,OAAO;QACzCqB,YAAY,EAAE3B,KAAK,CAACI,cAAc,CAACG,QAAQ;QAC3CqB,SAAS,EAAE,aAAa;QACxBC,YAAY,EAAE,aAAa;QAC3BP,gBAAgB,EAAE,IAAI,CAACQ,mBAAmB,CAAC7I,iBAAiB,EAAE+G,KAAK,CAACI,cAAc;;KAErF;EACH;EAEA;;;EAGQ,OAAOW,wBAAwBA,CACrCH,YAAiB,EACjBmB,cAAsB;IAAA;IAAA5L,aAAA,GAAAqB,CAAA;IAEtB,MAAMwK,WAAW;IAAA;IAAA,CAAA7L,aAAA,GAAAoB,CAAA,QAAqB,EAAE;IAAC;IAAApB,aAAA,GAAAoB,CAAA;IAEzC,KAAK,IAAI0K,IAAI;IAAA;IAAA,CAAA9L,aAAA,GAAAoB,CAAA,QAAG,CAAC,GAAE0K,IAAI,IAAI,CAAC,EAAEA,IAAI,EAAE,EAAE;MACpC,MAAMC,gBAAgB;MAAA;MAAA,CAAA/L,aAAA,GAAAoB,CAAA,QAAGsI,IAAI,CAACsC,GAAG,CAAC,CAAC,GAAGJ,cAAc,EAAEE,IAAI,CAAC;MAAC;MAAA9L,aAAA,GAAAoB,CAAA;MAC5DyK,WAAW,CAACnF,IAAI,CAAC;QACfoF,IAAI;QACJtB,SAAS,EAAEC,YAAY,CAACD,SAAS,GAAGuB,gBAAgB;QACpD1B,UAAU,EAAEI,YAAY,CAACJ,UAAU,GAAG0B,gBAAgB;QACtDzB,UAAU,EAAEG,YAAY,CAACH,UAAU,GAAGyB,gBAAgB;QACtDxB,SAAS,EAAEE,YAAY,CAACF,SAAS,GAAGwB,gBAAgB;QACpDH,cAAc;QACdK,cAAc,EAAExB,YAAY,CAACD,SAAS,IAAI,CAACd,IAAI,CAACsC,GAAG,CAAC,CAAC,GAAGJ,cAAc,EAAEE,IAAI,CAAC,GAAG,CAAC,IAAIF,cAAc;OACpG,CAAC;IACJ;IAAC;IAAA5L,aAAA,GAAAoB,CAAA;IAED,OAAOyK,WAAW;EACpB;EAEA;;;EAGQ,OAAOf,+BAA+BA,CAC5ChI,iBAAoC,EACpC2H,YAAiB;IAAA;IAAAzK,aAAA,GAAAqB,CAAA;IAEjB,MAAM6K,aAAa;IAAA;IAAA,CAAAlM,aAAA,GAAAoB,CAAA,QAA4B,EAAE;IAEjD;IAAA;IAAApB,aAAA,GAAAoB,CAAA;IACA,IAAI0B,iBAAiB,CAAC+C,UAAU,CAAC6B,oBAAoB,GAAG,CAAC,EAAE;MAAA;MAAA1H,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACzD8K,aAAa,CAACxF,IAAI,CAAC;QACjB/D,EAAE,EAAE,uBAAuB;QAC3B9B,IAAI,EAAE,uBAAuB;QAC7BsL,WAAW,EAAE,qEAAqE;QAClFhB,gBAAgB,EAAErI,iBAAiB,CAAC+C,UAAU,CAAC6B,oBAAoB,GAAG,EAAE,GAAG,EAAE;QAAE;QAC/E0E,kBAAkB,EAAE,IAAI;QACxBC,aAAa,EAAE,EAAE;QAAE;QACnBC,WAAW,EAAE,kBAA2B;QACxCC,UAAU,EAAE;OACb,CAAC;IACJ,CAAC;IAAA;IAAA;MAAAvM,aAAA,GAAAsB,CAAA;IAAA;IAED;IAAAtB,aAAA,GAAAoB,CAAA;IACA8K,aAAa,CAACxF,IAAI,CAAC;MACjB/D,EAAE,EAAE,wBAAwB;MAC5B9B,IAAI,EAAE,+BAA+B;MACrCsL,WAAW,EAAE,+EAA+E;MAC5FhB,gBAAgB,EAAEV,YAAY,CAACJ,UAAU,GAAG,IAAI;MAAE;MAClD+B,kBAAkB,EAAE,KAAK;MACzBC,aAAa,EAAE,EAAE;MAAE;MACnBC,WAAW,EAAE,kBAA2B;MACxCC,UAAU,EAAE;KACb,CAAC;IAAC;IAAAvM,aAAA,GAAAoB,CAAA;IAEH,OAAO8K,aAAa;EACtB;EAEA;;;EAGQ,OAAOlB,0BAA0BA,CAACnB,KAAkB;IAAA;IAAA7J,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAC1D,OAAO;MACLoL,YAAY,EAAE,4BAA4B;MAC1CC,aAAa,EAAE;QACbC,QAAQ,EAAE7C,KAAK,CAACC,UAAU;QAC1B6C,WAAW,EAAE,CACX;UAAEC,IAAI,EAAE,CAAC;UAAEC,SAAS,EAAE,IAAI;UAAEC,IAAI,EAAEjD,KAAK,CAACC,UAAU,GAAG;QAAG,CAAE,EAC1D;UAAE8C,IAAI,EAAE,CAAC;UAAEC,SAAS,EAAE,IAAI;UAAEC,IAAI,EAAEjD,KAAK,CAACC;QAAU,CAAE,EACpD;UAAE8C,IAAI,EAAE,CAAC;UAAEC,SAAS,EAAEE,QAAQ;UAAED,IAAI,EAAEjD,KAAK,CAACC,UAAU,GAAG;QAAG,CAAE;OAEjE;MACDrC,aAAa,EAAE;QACbqF,IAAI,EAAEjD,KAAK,CAACE,UAAU;QACtBiD,aAAa,EAAE,IAAI;QACnBC,aAAa,EAAE;UACbC,MAAM,EAAErD,KAAK,CAACE,UAAU,GAAG,GAAG;UAC9BoD,MAAM,EAAEtD,KAAK,CAACE,UAAU,GAAG;;OAE9B;MACDqD,YAAY,EAAE;QACZC,cAAc,EAAExD,KAAK,CAACG,SAAS;QAC/BsD,cAAc,EAAE,CAAC;QACjBC,WAAW,EAAE1D,KAAK,CAACG;OACpB;MACDC,cAAc,EAAEJ,KAAK,CAACI;KACvB;EACH;EAEA;;;EAGQ,OAAO0B,mBAAmBA,CAChC7I,iBAAoC,EACpC0K,QAAa;IAAA;IAAAxN,aAAA,GAAAqB,CAAA;IAEb;IACA,MAAMoM,WAAW;IAAA;IAAA,CAAAzN,aAAA,GAAAoB,CAAA,QAAG0B,iBAAiB,CAAC2C,gBAAgB,CAACnB,KAAK;IAC5D,MAAMoJ,iBAAiB;IAAA;IAAA,CAAA1N,aAAA,GAAAoB,CAAA,QAAG,GAAG,EAAC,CAAC;IAC/B,MAAMuM,oBAAoB;IAAA;IAAA,CAAA3N,aAAA,GAAAoB,CAAA,QAAG,GAAG,EAAC,CAAC;IAClC,MAAMwM,qBAAqB;IAAA;IAAA,CAAA5N,aAAA,GAAAoB,CAAA,QAAG,GAAG,EAAC,CAAC;IAEnC,MAAMyM,WAAW;IAAA;IAAA,CAAA7N,aAAA,GAAAoB,CAAA,QAAGqM,WAAW,GAAG,IAAI,EAAC,CAAC;IACxC,MAAMK,OAAO;IAAA;IAAA,CAAA9N,aAAA,GAAAoB,CAAA,QAAIqM,WAAW,GAAGC,iBAAiB,GAAGF,QAAQ,CAACtD,IAAI,GAChDuD,WAAW,GAAGE,oBAAoB,GAAGH,QAAQ,CAACrD,OAAQ,GACtDsD,WAAW,GAAGG,qBAAqB,GAAGJ,QAAQ,CAACpD,QAAS;IAAC;IAAApK,aAAA,GAAAoB,CAAA;IAEzE,OAAOsI,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEkE,WAAW,GAAGC,OAAO,CAAC;EAC3C;EAEA;;;EAGQ,aAAazK,wBAAwBA,CAC3CP,iBAAoC,EACpCT,mBAAwC;IAAA;IAAArC,aAAA,GAAAqB,CAAA;IAExC,MAAM0M,cAAc;IAAA;IAAA,CAAA/N,aAAA,GAAAoB,CAAA,QAAG0B,iBAAiB,CAAC2C,gBAAgB,CAACnB,KAAK;IAE/D;IACA,MAAM0J,oBAAoB;IAAA;IAAA,CAAAhO,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAAC6M,yBAAyB,CACzDF,cAAc,GAAG,IAAI,CAACG,gBAAgB,CAACC,YAAY,EACnDlM,qBAAA,CAAAmM,aAAa,CAACC,OAAO,EACrBpM,qBAAA,CAAAkD,SAAS,CAACC,QAAQ,EAClBnD,qBAAA,CAAAqM,aAAa,CAACC,OAAO,CACtB;IAED;IACA,MAAMC,iBAAiB;IAAA;IAAA,CAAAxO,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAAC6M,yBAAyB,CACtD,GAAG;IAAE;IACLhM,qBAAA,CAAAmM,aAAa,CAACC,OAAO,EACrBpM,qBAAA,CAAAkD,SAAS,CAACC,QAAQ,EAClBnD,qBAAA,CAAAqM,aAAa,CAACG,OAAO,CACtB;IAED;IACA,MAAMC,cAAc;IAAA;IAAA,CAAA1O,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAAC6M,yBAAyB,CACnDD,oBAAoB,CAAC1J,KAAK,GAAGkK,iBAAiB,CAAClK,KAAK,EACpDrC,qBAAA,CAAAmM,aAAa,CAACC,OAAO,EACrBpM,qBAAA,CAAAkD,SAAS,CAACC,QAAQ,EAClBnD,qBAAA,CAAAqM,aAAa,CAACC,OAAO,CACtB;IAED;IACA,MAAMI,iBAAiB;IAAA;IAAA,CAAA3O,aAAA,GAAAoB,CAAA,QAAG,CACxB;MACE8E,MAAM,EAAE,kBAAkB;MAC1B0I,SAAS,EAAEZ,oBAAoB,CAAC1J,KAAK;MACrCuK,UAAU,EAAGb,oBAAoB,CAAC1J,KAAK,GAAGoK,cAAc,CAACpK,KAAK,GAAI,GAAG;MACrEwK,cAAc,EAAE,IAAI,CAACZ,gBAAgB,CAACC;KACvC,EACD;MACEjI,MAAM,EAAE,iBAAiB;MACzB0I,SAAS,EAAEJ,iBAAiB,CAAClK,KAAK;MAClCuK,UAAU,EAAGL,iBAAiB,CAAClK,KAAK,GAAGoK,cAAc,CAACpK,KAAK,GAAI,GAAG;MAClEwK,cAAc,EAAE;KACjB,CACF;IAED;IACA,MAAMC,cAAc;IAAA;IAAA,CAAA/O,aAAA,GAAAoB,CAAA,QAAG;MACrB4N,gBAAgB,EAAEN,cAAc,CAACpK,KAAK;MACtCkE,cAAc,EAAE,QAAiB;MACjCyG,kBAAkB,EAAEP,cAAc,CAACpK,KAAK;MACxC4K,kBAAkB,EAAER,cAAc,CAACpK,KAAK,GAAG,GAAG;MAAE;MAChDqE,WAAW,EAAE,EAAE,CAAC;KACjB;IAED;IACA,MAAMwG,mBAAmB;IAAA;IAAA,CAAAnP,aAAA,GAAAoB,CAAA,QAAG,CAC1B;MACEH,IAAI,EAAE,kBAAkB;MACxBmO,SAAS,EAAEpB,oBAAoB,CAAC1J,KAAK,GAAG,GAAG;MAAE;MAC7C+K,IAAI,EAAErB,oBAAoB,CAAC1J,KAAK,GAAG,IAAI;MAAE;MACzCgL,cAAc,EAAE;KACjB,EACD;MACErO,IAAI,EAAE,mBAAmB;MACzBmO,SAAS,EAAEpB,oBAAoB,CAAC1J,KAAK,GAAG,GAAG;MAAE;MAC7C+K,IAAI,EAAE,CAAC;MAAE;MACTC,cAAc,EAAE;KACjB,CACF;IAED;IACA,MAAMhM,mBAAmB;IAAA;IAAA,CAAAtD,aAAA,GAAAoB,CAAA,QAAG;MAC1B4H,aAAa,EAAE,kBAAkB;MACjCuG,gBAAgB,EAAEb,cAAc,CAACpK,KAAK,GAAGjC,mBAAmB,CAACmC,gBAAgB,CAACD,aAAa;MAAE;MAC7FiL,kBAAkB,EAAE,IAAI;MAAE;MAC1BlG,UAAU,EAAE,EAAE;MACdG,oBAAoB,EAAEiF,cAAc,CAACpK,KAAK,GAAG;KAC9C;IAAC;IAAAtE,aAAA,GAAAoB,CAAA;IAEF,OAAO;MACLsN,cAAc;MACdV,oBAAoB;MACpBQ,iBAAiB;MACjBG,iBAAiB;MACjBI,cAAc;MACdI,mBAAmB;MACnB7L;KACD;EACH;EAEA;;;EAGQ,OAAO2K,yBAAyBA,CACtC3J,KAAa,EACb0B,KAAoB,EACpBC,SAAoB,EACpBwJ,KAAoB,EACpBtJ,QAAA;EAAA;EAAA,CAAAnG,aAAA,GAAAsB,CAAA,WAAmB,GAAG;IAAA;IAAAtB,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAEtB,OAAO;MACLkD,KAAK;MACL0B,KAAK;MACLC,SAAS;MACTwJ,KAAK;MACLtJ;KACD;EACH;EAEA;;;EAGQ,aAAa5C,yBAAyBA,CAC5ClB,mBAAwC,EACxCW,iBAA0C;IAAA;IAAAhD,aAAA,GAAAqB,CAAA;IAE1C,MAAMuI,GAAG;IAAA;IAAA,CAAA5J,aAAA,GAAAoB,CAAA,SAAG4B,iBAAiB,CAAC4E,gBAAgB;IAC9C,MAAMD,gBAAgB;IAAA;IAAA,CAAA3H,aAAA,GAAAoB,CAAA,SAAG4B,iBAAiB,CAAC2E,gBAAgB;IAAC;IAAA3H,aAAA,GAAAoB,CAAA;IAE5D,OAAO;MACL4H,aAAa,EAAE,mBAAmB;MAClCC,eAAe,EAAE,+BAA+B;MAChDyG,eAAe,EAAE;QACfC,OAAO,EAAE/F,GAAG;QACZgG,SAAS,EAAE,IAAI;QAAE;QACjBxG,eAAe,EAAE,GAAG;QACpBC,YAAY,EAAE,GAAG;QACjBC,UAAU,EAAE,IAAI,CAACC,sBAAsB,CAACK,GAAG;OAC5C;MACDiG,gBAAgB,EAAE;QAChBF,OAAO,EAAEhI,gBAAgB;QACzBiI,SAAS,EAAE,EAAE;QACbxG,eAAe,EAAE,EAAE;QACnBC,YAAY,EAAE,EAAE;QAChByG,KAAK,EAAE,IAAI,CAACC,wBAAwB,CAACpI,gBAAgB;OACtD;MACD6B,gBAAgB,EAAE;QAChBwG,SAAS,EAAEpG,GAAG,IAAI,IAAI;QAAA;QAAA,CAAA5J,aAAA,GAAAsB,CAAA,WAAG,WAAW;QAAA;QAAA,CAAAtB,aAAA,GAAAsB,CAAA,WAAG,eAAe;QACtD2O,UAAU,EAAEtI,gBAAgB,IAAI,EAAE;QAAA;QAAA,CAAA3H,aAAA,GAAAsB,CAAA,WAAG,WAAW;QAAA;QAAA,CAAAtB,aAAA,GAAAsB,CAAA,WAAG,eAAe;QAClE4O,IAAI,EAAE,IAAI,CAACC,mBAAmB,CAACvG,GAAG,EAAEjC,gBAAgB;OACrD;MACD8B,oBAAoB,EAAE;QACpB2G,aAAa,EAAE1G,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAACC,GAAG,GAAG,GAAG,IAAIA,GAAG,GAAG,GAAG,CAAC;QAAE;QACrDyG,WAAW,EAAE,CAAC;QAAE;QAChBC,iBAAiB,EAAE,CAAC,CAAC;;KAExB;EACH;EAEA;;;EAGQ,OAAOP,wBAAwBA,CAACQ,UAAkB;IAAA;IAAAvQ,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACxD,IAAImP,UAAU,IAAI,EAAE,EAAE;MAAA;MAAAvQ,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA,OAAO,IAAI;IAAA,CAAC;IAAA;IAAA;MAAApB,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAClC,IAAImP,UAAU,IAAI,EAAE,EAAE;MAAA;MAAAvQ,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA,OAAO,GAAG;IAAA,CAAC;IAAA;IAAA;MAAApB,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IACjC,IAAImP,UAAU,IAAI,EAAE,EAAE;MAAA;MAAAvQ,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA,OAAO,GAAG;IAAA,CAAC;IAAA;IAAA;MAAApB,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IACjC,IAAImP,UAAU,IAAI,EAAE,EAAE;MAAA;MAAAvQ,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA,OAAO,GAAG;IAAA,CAAC;IAAA;IAAA;MAAApB,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IACjC,IAAImP,UAAU,IAAI,EAAE,EAAE;MAAA;MAAAvQ,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA,OAAO,GAAG;IAAA,CAAC;IAAA;IAAA;MAAApB,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IACjC,OAAO,GAAG;EACZ;EAEA;;;EAGQ,OAAO+O,mBAAmBA,CAACvG,GAAW,EAAE2G,UAAkB;IAAA;IAAAvQ,aAAA,GAAAqB,CAAA;IAChE,IAAImP,MAAM;IAAA;IAAA,CAAAxQ,aAAA,GAAAoB,CAAA,SAAG,CAAC;IAEd;IAAA;IAAApB,aAAA,GAAAoB,CAAA;IACA,IAAIwI,GAAG,IAAI,GAAG,EAAE;MAAA;MAAA5J,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAAoP,MAAM,IAAI,CAAC;IAAA,CAAC,MACvB;MAAA;MAAAxQ,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA,IAAIwI,GAAG,IAAI,GAAG,EAAE;QAAA;QAAA5J,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QAAAoP,MAAM,IAAI,CAAC;MAAA,CAAC;MAAA;MAAA;QAAAxQ,aAAA,GAAAsB,CAAA;MAAA;IAAD;IAEhC;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IACA,IAAImP,UAAU,IAAI,EAAE,EAAE;MAAA;MAAAvQ,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAAoP,MAAM,IAAI,CAAC;IAAA,CAAC;IAAA;IAAA;MAAAxQ,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAElC,OAAOsI,IAAI,CAAC+G,GAAG,CAACD,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC;EAC9B;EAEA;;;EAGQ,aAAa/M,iCAAiCA,CACpDpB,mBAAwC,EACxCC,kBAAsC,EACtCU,iBAA0C,EAC1CE,WAAwB;IAAA;IAAAlD,aAAA,GAAAqB,CAAA;IAExB,MAAM6K,aAAa;IAAA;IAAA,CAAAlM,aAAA,GAAAoB,CAAA,SAAoC,EAAE;IAEzD;IAAA;IAAApB,aAAA,GAAAoB,CAAA;IACA,IAAI4B,iBAAiB,CAAC4E,gBAAgB,GAAG,GAAG,EAAE;MAAA;MAAA5H,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAC5C8K,aAAa,CAACxF,IAAI,CAAC;QACjB/D,EAAE,EAAE,wBAAwB;QAC5B9B,IAAI,EAAE,wBAAwB;QAC9BsL,WAAW,EAAE,mFAAmF;QAChGuE,QAAQ,EAAE,sBAAsB;QAChCC,sBAAsB,EAAE,EAAE;QAAE;QAC5BC,oBAAoB,EAAE1N,WAAW,CAACuH,YAAY,CAACJ,UAAU,GAAG,IAAI;QAChE+B,kBAAkB,EAAE,IAAI;QACxBC,aAAa,EAAE,EAAE;QAAE;QACnBwE,UAAU,EAAE,KAAK;QACjBC,QAAQ,EAAE,MAAM;QAChBC,eAAe,EAAE,CACf,4BAA4B,EAC5B,gCAAgC,EAChC,4BAA4B,CAC7B;QACDC,eAAe,EAAE;UACfC,eAAe,EAAE,EAAE;UACnBC,eAAe,EAAE,EAAE;UACnBZ,iBAAiB,EAAE,EAAE;UACrBD,WAAW,EAAEnN,WAAW,CAACuH,YAAY,CAACJ,UAAU,GAAG;;OAEtD,CAAC;IACJ,CAAC;IAAA;IAAA;MAAArK,aAAA,GAAAsB,CAAA;IAAA;IAED;IAAAtB,aAAA,GAAAoB,CAAA;IACA,IAAIkB,kBAAkB,CAAC6O,mBAAmB,CAAC7M,KAAK,GAAG,GAAG,EAAE;MAAA;MAAAtE,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACtD8K,aAAa,CAACxF,IAAI,CAAC;QACjB/D,EAAE,EAAE,mBAAmB;QACvB9B,IAAI,EAAE,0BAA0B;QAChCsL,WAAW,EAAE,2EAA2E;QACxFuE,QAAQ,EAAE,eAAe;QACzBC,sBAAsB,EAAE,EAAE;QAAE;QAC5BC,oBAAoB,EAAE1N,WAAW,CAACuH,YAAY,CAACJ,UAAU,GAAG,IAAI;QAChE+B,kBAAkB,EAAE,KAAK;QACzBC,aAAa,EAAE,EAAE;QAAE;QACnBwE,UAAU,EAAE,MAAM;QAClBC,QAAQ,EAAE,QAAQ;QAClBC,eAAe,EAAE,CACf,qBAAqB,EACrB,gCAAgC,EAChC,4BAA4B,EAC5B,qBAAqB,CACtB;QACDC,eAAe,EAAE;UACfC,eAAe,EAAE,EAAE;UACnBC,eAAe,EAAE,EAAE;UACnBZ,iBAAiB,EAAE,EAAE;UACrBD,WAAW,EAAEnN,WAAW,CAACuH,YAAY,CAACJ,UAAU,GAAG;;OAEtD,CAAC;IACJ,CAAC;IAAA;IAAA;MAAArK,aAAA,GAAAsB,CAAA;IAAA;IAED;IAAAtB,aAAA,GAAAoB,CAAA;IACA8K,aAAa,CAACxF,IAAI,CAAC;MACjB/D,EAAE,EAAE,qBAAqB;MACzB9B,IAAI,EAAE,4BAA4B;MAClCsL,WAAW,EAAE,mEAAmE;MAChFuE,QAAQ,EAAE,aAAa;MACvBC,sBAAsB,EAAE,CAAC;MAAE;MAC3BC,oBAAoB,EAAE1N,WAAW,CAACuH,YAAY,CAACJ,UAAU,GAAG,IAAI;MAChE+B,kBAAkB,EAAE,IAAI;MACxBC,aAAa,EAAE,EAAE;MAAE;MACnBwE,UAAU,EAAE,KAAK;MACjBC,QAAQ,EAAE,QAAQ;MAClBC,eAAe,EAAE,CACf,6CAA6C,EAC7C,+BAA+B,EAC/B,+BAA+B,CAChC;MACDC,eAAe,EAAE;QACfC,eAAe,EAAE,CAAC;QAClBC,eAAe,EAAE,CAAC;QAClBZ,iBAAiB,EAAE,CAAC;QACpBD,WAAW,EAAEnN,WAAW,CAACuH,YAAY,CAACJ,UAAU,GAAG;;KAEtD,CAAC;IAAC;IAAArK,aAAA,GAAAoB,CAAA;IAEH,OAAO8K,aAAa;EACtB;EAEA;;;EAGQ,aAAavI,6BAA6BA,CAChDtB,mBAAwC,EACxCS,iBAAoC;IAAA;IAAA9C,aAAA,GAAAqB,CAAA;IAEpC,MAAM+P,eAAe;IAAA;IAAA,CAAApR,aAAA,GAAAoB,CAAA,SAAG0B,iBAAiB,CAAC2C,gBAAgB,CAACnB,KAAK;IAAC;IAAAtE,aAAA,GAAAoB,CAAA;IAEjE,OAAO;MACLiQ,iBAAiB,EAAE,CACjB;QACEC,MAAM,EAAE,QAAQ;QAChBxO,iBAAiB,EAAEsO,eAAe,GAAG,IAAI;QAAE;QAC3CrK,WAAW,EAAEjE,iBAAiB,CAAC+B,WAAW,CAACkC,WAAW,GAAG,GAAG;QAC5DD,QAAQ,EAAEhE,iBAAiB,CAAC+B,WAAW,CAACiC,QAAQ,GAAG,GAAG;QACtDyK,cAAc,EAAE,IAAI;QACpBhB,UAAU,EAAE,EAAE;QACdiB,KAAK,EAAEJ,eAAe,GAAG,IAAI,GAAG,IAAI,CAAC;OACtC,EACD;QACEE,MAAM,EAAE,QAAQ;QAChBxO,iBAAiB,EAAEsO,eAAe,GAAG,IAAI;QAAE;QAC3CrK,WAAW,EAAEjE,iBAAiB,CAAC+B,WAAW,CAACkC,WAAW,GAAG,GAAG;QAC5DD,QAAQ,EAAEhE,iBAAiB,CAAC+B,WAAW,CAACiC,QAAQ,GAAG,GAAG;QACtDyK,cAAc,EAAE,IAAI;QACpBhB,UAAU,EAAE,EAAE;QAAE;QAChBiB,KAAK,EAAEJ,eAAe,GAAG,IAAI,GAAG,IAAI,CAAC;OACtC,EACD;QACEE,MAAM,EAAE,MAAM;QACdxO,iBAAiB,EAAEsO,eAAe,GAAG,IAAI;QAAE;QAC3CrK,WAAW,EAAEjE,iBAAiB,CAAC+B,WAAW,CAACkC,WAAW,GAAG,GAAG;QAC5DD,QAAQ,EAAEhE,iBAAiB,CAAC+B,WAAW,CAACiC,QAAQ,GAAG,GAAG;QACtDyK,cAAc,EAAE,IAAI;QACpBhB,UAAU,EAAE,EAAE;QACdiB,KAAK,EAAEJ,eAAe,GAAG,IAAI,GAAG;OACjC,EACD;QACEE,MAAM,EAAE,QAAQ;QAChBxO,iBAAiB,EAAEsO,eAAe,GAAG,IAAI;QAAE;QAC3CrK,WAAW,EAAEjE,iBAAiB,CAAC+B,WAAW,CAACkC,WAAW,GAAG,GAAG;QAC5DD,QAAQ,EAAEhE,iBAAiB,CAAC+B,WAAW,CAACiC,QAAQ,GAAG,GAAG;QACtDyK,cAAc,EAAE,IAAI;QACpBhB,UAAU,EAAE,EAAE;QAAE;QAChBiB,KAAK,EAAEJ,eAAe,GAAG,IAAI,GAAG,IAAI,CAAC;OACtC,CACF;MACDK,WAAW,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC;MAC/BjO,yBAAyB,EAAE,CACzB;QACE8N,MAAM,EAAE,QAAQ;QAChBI,WAAW,EAAE,sBAAsB;QACnCtC,SAAS,EAAE,mDAAmD;QAC9DuC,OAAO,EAAE;OACV,EACD;QACEL,MAAM,EAAE,QAAQ;QAChBI,WAAW,EAAE,+BAA+B;QAC5CtC,SAAS,EAAE,oCAAoC;QAC/CuC,OAAO,EAAE;OACV,CACF;MACDC,kBAAkB,EAAE;QAClBC,sBAAsB,EAAE,IAAI;QAAE;QAC9BC,mBAAmB,EAAE,KAAK;QAAE;QAC5BC,mBAAmB,EAAE,EAAE;QAAE;QACzBC,gBAAgB,EAAE,EAAE;QAAE;QACtBC,gBAAgB,EAAE,EAAE,CAAC;;KAExB;EACH;EAEA;;;EAGQ,OAAOvP,kBAAkBA,CAACmB,QAAgB;IAAA;IAAA7D,aAAA,GAAAqB,CAAA;IAChD,MAAMuB,SAAS;IAAA;IAAA,CAAA5C,aAAA,GAAAoB,CAAA,SAAGyB,IAAI,CAACqP,GAAG,EAAE;IAC5B,MAAMC,MAAM;IAAA;IAAA,CAAAnS,aAAA,GAAAoB,CAAA,SAAGsI,IAAI,CAACyI,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;IAAC;IAAArS,aAAA,GAAAoB,CAAA;IAC1D,OAAO,mBAAmByC,QAAQ,IAAIjB,SAAS,IAAIuP,MAAM,EAAE;EAC7D;;;;AA53BFG,OAAA,CAAAnQ,8BAAA,GAAAA,8BAAA;AA63BC;AAAAnC,aAAA,GAAAoB,CAAA;AA53ByBe,8BAAA,CAAAoQ,OAAO,GAAG,OAAO;AAAC;AAAAvS,aAAA,GAAAoB,CAAA;AAClBe,8BAAA,CAAA4B,YAAY,GAAG,IAAIyO,GAAG,EAA0B;AAExE;AAAA;AAAAxS,aAAA,GAAAoB,CAAA;AACwBe,8BAAA,CAAA4F,UAAU,GAAG,OAAO;AAAC;AAAA/H,aAAA,GAAAoB,CAAA;AACrBe,8BAAA,CAAAsQ,QAAQ,GAAG,KAAK;AAAC;AAAAzS,aAAA,GAAAoB,CAAA;AACjBe,8BAAA,CAAAuQ,UAAU,GAAG,WAAW;AAEhD;AAAA;AAAA1S,aAAA,GAAAoB,CAAA;AACwBe,8BAAA,CAAA+L,gBAAgB,GAAG;EACzCC,YAAY,EAAE,GAAG;EAAE;EACnBwE,IAAI,EAAE,IAAI;EACVC,WAAW,EAAE,IAAI;EACjBC,SAAS,EAAE;CACZ", "ignoreList": []}