e2037e3483286d50e908fe39255c373f
"use strict";

/**
 * Energy Efficiency Analysis Engine
 *
 * Comprehensive energy efficiency analysis service for Phase 3 Priority 3: Advanced System Analysis Tools
 * Provides energy consumption analysis, fan power optimization, lifecycle energy calculations,
 * and carbon footprint assessment tools for HVAC duct systems.
 *
 * @version 3.0.0
 * <AUTHOR> Suite Development Team
 */
/* istanbul ignore next */
function cov_9l8cmvs2f() {
  var path = "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\EnergyEfficiencyAnalysisEngine.ts";
  var hash = "1116583a26df174fea098d317d3ca0f74db9f629";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\EnergyEfficiencyAnalysisEngine.ts",
    statementMap: {
      "0": {
        start: {
          line: 12,
          column: 0
        },
        end: {
          line: 12,
          column: 62
        }
      },
      "1": {
        start: {
          line: 13,
          column: 0
        },
        end: {
          line: 13,
          column: 48
        }
      },
      "2": {
        start: {
          line: 14,
          column: 30
        },
        end: {
          line: 14,
          column: 68
        }
      },
      "3": {
        start: {
          line: 31,
          column: 8
        },
        end: {
          line: 66,
          column: 9
        }
      },
      "4": {
        start: {
          line: 32,
          column: 31
        },
        end: {
          line: 32,
          column: 78
        }
      },
      "5": {
        start: {
          line: 33,
          column: 30
        },
        end: {
          line: 33,
          column: 40
        }
      },
      "6": {
        start: {
          line: 35,
          column: 38
        },
        end: {
          line: 35,
          column: 135
        }
      },
      "7": {
        start: {
          line: 37,
          column: 38
        },
        end: {
          line: 37,
          column: 141
        }
      },
      "8": {
        start: {
          line: 39,
          column: 32
        },
        end: {
          line: 39,
          column: 95
        }
      },
      "9": {
        start: {
          line: 41,
          column: 36
        },
        end: {
          line: 41,
          column: 111
        }
      },
      "10": {
        start: {
          line: 43,
          column: 40
        },
        end: {
          line: 43,
          column: 116
        }
      },
      "11": {
        start: {
          line: 45,
          column: 46
        },
        end: {
          line: 45,
          column: 163
        }
      },
      "12": {
        start: {
          line: 47,
          column: 37
        },
        end: {
          line: 47,
          column: 117
        }
      },
      "13": {
        start: {
          line: 48,
          column: 29
        },
        end: {
          line: 59,
          column: 13
        }
      },
      "14": {
        start: {
          line: 61,
          column: 12
        },
        end: {
          line: 61,
          column: 56
        }
      },
      "15": {
        start: {
          line: 62,
          column: 12
        },
        end: {
          line: 62,
          column: 28
        }
      },
      "16": {
        start: {
          line: 65,
          column: 12
        },
        end: {
          line: 65,
          column: 126
        }
      },
      "17": {
        start: {
          line: 72,
          column: 27
        },
        end: {
          line: 72,
          column: 60
        }
      },
      "18": {
        start: {
          line: 73,
          column: 30
        },
        end: {
          line: 73,
          column: 80
        }
      },
      "19": {
        start: {
          line: 75,
          column: 25
        },
        end: {
          line: 80,
          column: 9
        }
      },
      "20": {
        start: {
          line: 81,
          column: 37
        },
        end: {
          line: 81,
          column: 104
        }
      },
      "21": {
        start: {
          line: 83,
          column: 31
        },
        end: {
          line: 83,
          column: 227
        }
      },
      "22": {
        start: {
          line: 85,
          column: 37
        },
        end: {
          line: 85,
          column: 238
        }
      },
      "23": {
        start: {
          line: 87,
          column: 33
        },
        end: {
          line: 87,
          column: 245
        }
      },
      "24": {
        start: {
          line: 89,
          column: 37
        },
        end: {
          line: 89,
          column: 93
        }
      },
      "25": {
        start: {
          line: 91,
          column: 28
        },
        end: {
          line: 91,
          column: 75
        }
      },
      "26": {
        start: {
          line: 93,
          column: 27
        },
        end: {
          line: 93,
          column: 73
        }
      },
      "27": {
        start: {
          line: 94,
          column: 8
        },
        end: {
          line: 101,
          column: 10
        }
      },
      "28": {
        start: {
          line: 107,
          column: 8
        },
        end: {
          line: 113,
          column: 10
        }
      },
      "29": {
        start: {
          line: 119,
          column: 28
        },
        end: {
          line: 119,
          column: 30
        }
      },
      "30": {
        start: {
          line: 121,
          column: 30
        },
        end: {
          line: 128,
          column: 9
        }
      },
      "31": {
        start: {
          line: 129,
          column: 8
        },
        end: {
          line: 136,
          column: 11
        }
      },
      "32": {
        start: {
          line: 130,
          column: 12
        },
        end: {
          line: 135,
          column: 15
        }
      },
      "33": {
        start: {
          line: 137,
          column: 8
        },
        end: {
          line: 137,
          column: 27
        }
      },
      "34": {
        start: {
          line: 143,
          column: 8
        },
        end: {
          line: 155,
          column: 10
        }
      },
      "35": {
        start: {
          line: 161,
          column: 8
        },
        end: {
          line: 169,
          column: 10
        }
      },
      "36": {
        start: {
          line: 175,
          column: 30
        },
        end: {
          line: 175,
          column: 80
        }
      },
      "37": {
        start: {
          line: 176,
          column: 27
        },
        end: {
          line: 176,
          column: 60
        }
      },
      "38": {
        start: {
          line: 177,
          column: 33
        },
        end: {
          line: 177,
          column: 74
        }
      },
      "39": {
        start: {
          line: 179,
          column: 33
        },
        end: {
          line: 179,
          column: 68
        }
      },
      "40": {
        start: {
          line: 181,
          column: 29
        },
        end: {
          line: 181,
          column: 34
        }
      },
      "41": {
        start: {
          line: 182,
          column: 39
        },
        end: {
          line: 182,
          column: 114
        }
      },
      "42": {
        start: {
          line: 184,
          column: 29
        },
        end: {
          line: 184,
          column: 63
        }
      },
      "43": {
        start: {
          line: 186,
          column: 32
        },
        end: {
          line: 186,
          column: 79
        }
      },
      "44": {
        start: {
          line: 188,
          column: 36
        },
        end: {
          line: 188,
          column: 105
        }
      },
      "45": {
        start: {
          line: 189,
          column: 8
        },
        end: {
          line: 199,
          column: 10
        }
      },
      "46": {
        start: {
          line: 205,
          column: 8
        },
        end: {
          line: 212,
          column: 10
        }
      },
      "47": {
        start: {
          line: 219,
          column: 31
        },
        end: {
          line: 219,
          column: 35
        }
      },
      "48": {
        start: {
          line: 220,
          column: 35
        },
        end: {
          line: 220,
          column: 38
        }
      },
      "49": {
        start: {
          line: 221,
          column: 32
        },
        end: {
          line: 221,
          column: 35
        }
      },
      "50": {
        start: {
          line: 222,
          column: 8
        },
        end: {
          line: 232,
          column: 10
        }
      },
      "51": {
        start: {
          line: 239,
          column: 8
        },
        end: {
          line: 240,
          column: 22
        }
      },
      "52": {
        start: {
          line: 240,
          column: 12
        },
        end: {
          line: 240,
          column: 22
        }
      },
      "53": {
        start: {
          line: 241,
          column: 8
        },
        end: {
          line: 242,
          column: 22
        }
      },
      "54": {
        start: {
          line: 242,
          column: 12
        },
        end: {
          line: 242,
          column: 22
        }
      },
      "55": {
        start: {
          line: 243,
          column: 8
        },
        end: {
          line: 244,
          column: 22
        }
      },
      "56": {
        start: {
          line: 244,
          column: 12
        },
        end: {
          line: 244,
          column: 22
        }
      },
      "57": {
        start: {
          line: 245,
          column: 8
        },
        end: {
          line: 246,
          column: 22
        }
      },
      "58": {
        start: {
          line: 246,
          column: 12
        },
        end: {
          line: 246,
          column: 22
        }
      },
      "59": {
        start: {
          line: 247,
          column: 8
        },
        end: {
          line: 248,
          column: 22
        }
      },
      "60": {
        start: {
          line: 248,
          column: 12
        },
        end: {
          line: 248,
          column: 22
        }
      },
      "61": {
        start: {
          line: 249,
          column: 8
        },
        end: {
          line: 249,
          column: 17
        }
      },
      "62": {
        start: {
          line: 256,
          column: 22
        },
        end: {
          line: 265,
          column: 9
        }
      },
      "63": {
        start: {
          line: 267,
          column: 27
        },
        end: {
          line: 267,
          column: 86
        }
      },
      "64": {
        start: {
          line: 268,
          column: 27
        },
        end: {
          line: 268,
          column: 89
        }
      },
      "65": {
        start: {
          line: 269,
          column: 26
        },
        end: {
          line: 269,
          column: 46
        }
      },
      "66": {
        start: {
          line: 270,
          column: 26
        },
        end: {
          line: 270,
          column: 61
        }
      },
      "67": {
        start: {
          line: 271,
          column: 29
        },
        end: {
          line: 278,
          column: 9
        }
      },
      "68": {
        start: {
          line: 280,
          column: 31
        },
        end: {
          line: 280,
          column: 80
        }
      },
      "69": {
        start: {
          line: 282,
          column: 40
        },
        end: {
          line: 282,
          column: 109
        }
      },
      "70": {
        start: {
          line: 284,
          column: 37
        },
        end: {
          line: 284,
          column: 75
        }
      },
      "71": {
        start: {
          line: 285,
          column: 8
        },
        end: {
          line: 306,
          column: 10
        }
      },
      "72": {
        start: {
          line: 312,
          column: 28
        },
        end: {
          line: 312,
          column: 30
        }
      },
      "73": {
        start: {
          line: 313,
          column: 8
        },
        end: {
          line: 324,
          column: 9
        }
      },
      "74": {
        start: {
          line: 313,
          column: 24
        },
        end: {
          line: 313,
          column: 25
        }
      },
      "75": {
        start: {
          line: 314,
          column: 37
        },
        end: {
          line: 314,
          column: 71
        }
      },
      "76": {
        start: {
          line: 315,
          column: 12
        },
        end: {
          line: 323,
          column: 15
        }
      },
      "77": {
        start: {
          line: 325,
          column: 8
        },
        end: {
          line: 325,
          column: 27
        }
      },
      "78": {
        start: {
          line: 331,
          column: 30
        },
        end: {
          line: 331,
          column: 32
        }
      },
      "79": {
        start: {
          line: 333,
          column: 8
        },
        end: {
          line: 344,
          column: 9
        }
      },
      "80": {
        start: {
          line: 334,
          column: 12
        },
        end: {
          line: 343,
          column: 15
        }
      },
      "81": {
        start: {
          line: 346,
          column: 8
        },
        end: {
          line: 355,
          column: 11
        }
      },
      "82": {
        start: {
          line: 356,
          column: 8
        },
        end: {
          line: 356,
          column: 29
        }
      },
      "83": {
        start: {
          line: 362,
          column: 8
        },
        end: {
          line: 386,
          column: 10
        }
      },
      "84": {
        start: {
          line: 393,
          column: 28
        },
        end: {
          line: 393,
          column: 68
        }
      },
      "85": {
        start: {
          line: 394,
          column: 34
        },
        end: {
          line: 394,
          column: 37
        }
      },
      "86": {
        start: {
          line: 395,
          column: 37
        },
        end: {
          line: 395,
          column: 40
        }
      },
      "87": {
        start: {
          line: 396,
          column: 38
        },
        end: {
          line: 396,
          column: 41
        }
      },
      "88": {
        start: {
          line: 397,
          column: 28
        },
        end: {
          line: 397,
          column: 46
        }
      },
      "89": {
        start: {
          line: 398,
          column: 24
        },
        end: {
          line: 400,
          column: 69
        }
      },
      "90": {
        start: {
          line: 401,
          column: 8
        },
        end: {
          line: 401,
          column: 50
        }
      },
      "91": {
        start: {
          line: 407,
          column: 31
        },
        end: {
          line: 407,
          column: 71
        }
      },
      "92": {
        start: {
          line: 409,
          column: 37
        },
        end: {
          line: 409,
          column: 252
        }
      },
      "93": {
        start: {
          line: 411,
          column: 34
        },
        end: {
          line: 412,
          column: 139
        }
      },
      "94": {
        start: {
          line: 414,
          column: 31
        },
        end: {
          line: 414,
          column: 247
        }
      },
      "95": {
        start: {
          line: 416,
          column: 34
        },
        end: {
          line: 429,
          column: 9
        }
      },
      "96": {
        start: {
          line: 431,
          column: 31
        },
        end: {
          line: 437,
          column: 9
        }
      },
      "97": {
        start: {
          line: 439,
          column: 36
        },
        end: {
          line: 452,
          column: 9
        }
      },
      "98": {
        start: {
          line: 454,
          column: 36
        },
        end: {
          line: 460,
          column: 9
        }
      },
      "99": {
        start: {
          line: 461,
          column: 8
        },
        end: {
          line: 469,
          column: 10
        }
      },
      "100": {
        start: {
          line: 475,
          column: 8
        },
        end: {
          line: 481,
          column: 10
        }
      },
      "101": {
        start: {
          line: 487,
          column: 20
        },
        end: {
          line: 487,
          column: 54
        }
      },
      "102": {
        start: {
          line: 488,
          column: 33
        },
        end: {
          line: 488,
          column: 67
        }
      },
      "103": {
        start: {
          line: 489,
          column: 8
        },
        end: {
          line: 516,
          column: 10
        }
      },
      "104": {
        start: {
          line: 522,
          column: 8
        },
        end: {
          line: 523,
          column: 24
        }
      },
      "105": {
        start: {
          line: 523,
          column: 12
        },
        end: {
          line: 523,
          column: 24
        }
      },
      "106": {
        start: {
          line: 524,
          column: 8
        },
        end: {
          line: 525,
          column: 23
        }
      },
      "107": {
        start: {
          line: 525,
          column: 12
        },
        end: {
          line: 525,
          column: 23
        }
      },
      "108": {
        start: {
          line: 526,
          column: 8
        },
        end: {
          line: 527,
          column: 23
        }
      },
      "109": {
        start: {
          line: 527,
          column: 12
        },
        end: {
          line: 527,
          column: 23
        }
      },
      "110": {
        start: {
          line: 528,
          column: 8
        },
        end: {
          line: 529,
          column: 23
        }
      },
      "111": {
        start: {
          line: 529,
          column: 12
        },
        end: {
          line: 529,
          column: 23
        }
      },
      "112": {
        start: {
          line: 530,
          column: 8
        },
        end: {
          line: 531,
          column: 23
        }
      },
      "113": {
        start: {
          line: 531,
          column: 12
        },
        end: {
          line: 531,
          column: 23
        }
      },
      "114": {
        start: {
          line: 532,
          column: 8
        },
        end: {
          line: 532,
          column: 19
        }
      },
      "115": {
        start: {
          line: 538,
          column: 21
        },
        end: {
          line: 538,
          column: 22
        }
      },
      "116": {
        start: {
          line: 540,
          column: 8
        },
        end: {
          line: 543,
          column: 24
        }
      },
      "117": {
        start: {
          line: 541,
          column: 12
        },
        end: {
          line: 541,
          column: 24
        }
      },
      "118": {
        start: {
          line: 542,
          column: 13
        },
        end: {
          line: 543,
          column: 24
        }
      },
      "119": {
        start: {
          line: 543,
          column: 12
        },
        end: {
          line: 543,
          column: 24
        }
      },
      "120": {
        start: {
          line: 545,
          column: 8
        },
        end: {
          line: 546,
          column: 24
        }
      },
      "121": {
        start: {
          line: 546,
          column: 12
        },
        end: {
          line: 546,
          column: 24
        }
      },
      "122": {
        start: {
          line: 547,
          column: 8
        },
        end: {
          line: 547,
          column: 35
        }
      },
      "123": {
        start: {
          line: 553,
          column: 30
        },
        end: {
          line: 553,
          column: 32
        }
      },
      "124": {
        start: {
          line: 555,
          column: 8
        },
        end: {
          line: 579,
          column: 9
        }
      },
      "125": {
        start: {
          line: 556,
          column: 12
        },
        end: {
          line: 578,
          column: 15
        }
      },
      "126": {
        start: {
          line: 581,
          column: 8
        },
        end: {
          line: 606,
          column: 9
        }
      },
      "127": {
        start: {
          line: 582,
          column: 12
        },
        end: {
          line: 605,
          column: 15
        }
      },
      "128": {
        start: {
          line: 608,
          column: 8
        },
        end: {
          line: 630,
          column: 11
        }
      },
      "129": {
        start: {
          line: 631,
          column: 8
        },
        end: {
          line: 631,
          column: 29
        }
      },
      "130": {
        start: {
          line: 637,
          column: 32
        },
        end: {
          line: 637,
          column: 72
        }
      },
      "131": {
        start: {
          line: 638,
          column: 8
        },
        end: {
          line: 699,
          column: 10
        }
      },
      "132": {
        start: {
          line: 705,
          column: 26
        },
        end: {
          line: 705,
          column: 36
        }
      },
      "133": {
        start: {
          line: 706,
          column: 23
        },
        end: {
          line: 706,
          column: 65
        }
      },
      "134": {
        start: {
          line: 707,
          column: 8
        },
        end: {
          line: 707,
          column: 68
        }
      },
      "135": {
        start: {
          line: 710,
          column: 0
        },
        end: {
          line: 710,
          column: 72
        }
      },
      "136": {
        start: {
          line: 711,
          column: 0
        },
        end: {
          line: 711,
          column: 49
        }
      },
      "137": {
        start: {
          line: 712,
          column: 0
        },
        end: {
          line: 712,
          column: 56
        }
      },
      "138": {
        start: {
          line: 714,
          column: 0
        },
        end: {
          line: 714,
          column: 52
        }
      },
      "139": {
        start: {
          line: 715,
          column: 0
        },
        end: {
          line: 715,
          column: 48
        }
      },
      "140": {
        start: {
          line: 716,
          column: 0
        },
        end: {
          line: 716,
          column: 56
        }
      },
      "141": {
        start: {
          line: 718,
          column: 0
        },
        end: {
          line: 723,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 30,
            column: 4
          },
          end: {
            line: 30,
            column: 5
          }
        },
        loc: {
          start: {
            line: 30,
            column: 114
          },
          end: {
            line: 67,
            column: 5
          }
        },
        line: 30
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 71,
            column: 4
          },
          end: {
            line: 71,
            column: 5
          }
        },
        loc: {
          start: {
            line: 71,
            column: 104
          },
          end: {
            line: 102,
            column: 5
          }
        },
        line: 71
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 106,
            column: 4
          },
          end: {
            line: 106,
            column: 5
          }
        },
        loc: {
          start: {
            line: 106,
            column: 84
          },
          end: {
            line: 114,
            column: 5
          }
        },
        line: 106
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 118,
            column: 4
          },
          end: {
            line: 118,
            column: 5
          }
        },
        loc: {
          start: {
            line: 118,
            column: 63
          },
          end: {
            line: 138,
            column: 5
          }
        },
        line: 118
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 129,
            column: 30
          },
          end: {
            line: 129,
            column: 31
          }
        },
        loc: {
          start: {
            line: 129,
            column: 41
          },
          end: {
            line: 136,
            column: 9
          }
        },
        line: 129
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 142,
            column: 4
          },
          end: {
            line: 142,
            column: 5
          }
        },
        loc: {
          start: {
            line: 142,
            column: 54
          },
          end: {
            line: 156,
            column: 5
          }
        },
        line: 142
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 160,
            column: 4
          },
          end: {
            line: 160,
            column: 5
          }
        },
        loc: {
          start: {
            line: 160,
            column: 53
          },
          end: {
            line: 170,
            column: 5
          }
        },
        line: 160
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 174,
            column: 4
          },
          end: {
            line: 174,
            column: 5
          }
        },
        loc: {
          start: {
            line: 174,
            column: 110
          },
          end: {
            line: 200,
            column: 5
          }
        },
        line: 174
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 204,
            column: 4
          },
          end: {
            line: 204,
            column: 5
          }
        },
        loc: {
          start: {
            line: 204,
            column: 55
          },
          end: {
            line: 213,
            column: 5
          }
        },
        line: 204
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 217,
            column: 4
          },
          end: {
            line: 217,
            column: 5
          }
        },
        loc: {
          start: {
            line: 217,
            column: 76
          },
          end: {
            line: 233,
            column: 5
          }
        },
        line: 217
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 237,
            column: 4
          },
          end: {
            line: 237,
            column: 5
          }
        },
        loc: {
          start: {
            line: 237,
            column: 39
          },
          end: {
            line: 250,
            column: 5
          }
        },
        line: 237
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 254,
            column: 4
          },
          end: {
            line: 254,
            column: 5
          }
        },
        loc: {
          start: {
            line: 254,
            column: 70
          },
          end: {
            line: 307,
            column: 5
          }
        },
        line: 254
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 311,
            column: 4
          },
          end: {
            line: 311,
            column: 5
          }
        },
        loc: {
          start: {
            line: 311,
            column: 66
          },
          end: {
            line: 326,
            column: 5
          }
        },
        line: 311
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 330,
            column: 4
          },
          end: {
            line: 330,
            column: 5
          }
        },
        loc: {
          start: {
            line: 330,
            column: 76
          },
          end: {
            line: 357,
            column: 5
          }
        },
        line: 330
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 361,
            column: 4
          },
          end: {
            line: 361,
            column: 5
          }
        },
        loc: {
          start: {
            line: 361,
            column: 45
          },
          end: {
            line: 387,
            column: 5
          }
        },
        line: 361
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 391,
            column: 4
          },
          end: {
            line: 391,
            column: 5
          }
        },
        loc: {
          start: {
            line: 391,
            column: 60
          },
          end: {
            line: 402,
            column: 5
          }
        },
        line: 391
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 406,
            column: 4
          },
          end: {
            line: 406,
            column: 5
          }
        },
        loc: {
          start: {
            line: 406,
            column: 82
          },
          end: {
            line: 470,
            column: 5
          }
        },
        line: 406
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 474,
            column: 4
          },
          end: {
            line: 474,
            column: 5
          }
        },
        loc: {
          start: {
            line: 474,
            column: 85
          },
          end: {
            line: 482,
            column: 5
          }
        },
        line: 474
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 486,
            column: 4
          },
          end: {
            line: 486,
            column: 5
          }
        },
        loc: {
          start: {
            line: 486,
            column: 83
          },
          end: {
            line: 517,
            column: 5
          }
        },
        line: 486
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 521,
            column: 4
          },
          end: {
            line: 521,
            column: 5
          }
        },
        loc: {
          start: {
            line: 521,
            column: 48
          },
          end: {
            line: 533,
            column: 5
          }
        },
        line: 521
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 537,
            column: 4
          },
          end: {
            line: 537,
            column: 5
          }
        },
        loc: {
          start: {
            line: 537,
            column: 48
          },
          end: {
            line: 548,
            column: 5
          }
        },
        line: 537
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 552,
            column: 4
          },
          end: {
            line: 552,
            column: 5
          }
        },
        loc: {
          start: {
            line: 552,
            column: 124
          },
          end: {
            line: 632,
            column: 5
          }
        },
        line: 552
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 636,
            column: 4
          },
          end: {
            line: 636,
            column: 5
          }
        },
        loc: {
          start: {
            line: 636,
            column: 87
          },
          end: {
            line: 700,
            column: 5
          }
        },
        line: 636
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 704,
            column: 4
          },
          end: {
            line: 704,
            column: 5
          }
        },
        loc: {
          start: {
            line: 704,
            column: 40
          },
          end: {
            line: 708,
            column: 5
          }
        },
        line: 704
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 65,
            column: 66
          },
          end: {
            line: 65,
            column: 122
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 65,
            column: 91
          },
          end: {
            line: 65,
            column: 104
          }
        }, {
          start: {
            line: 65,
            column: 107
          },
          end: {
            line: 65,
            column: 122
          }
        }],
        line: 65
      },
      "1": {
        loc: {
          start: {
            line: 75,
            column: 25
          },
          end: {
            line: 80,
            column: 9
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 75,
            column: 25
          },
          end: {
            line: 75,
            column: 42
          }
        }, {
          start: {
            line: 75,
            column: 46
          },
          end: {
            line: 80,
            column: 9
          }
        }],
        line: 75
      },
      "2": {
        loc: {
          start: {
            line: 106,
            column: 68
          },
          end: {
            line: 106,
            column: 82
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 106,
            column: 79
          },
          end: {
            line: 106,
            column: 82
          }
        }],
        line: 106
      },
      "3": {
        loc: {
          start: {
            line: 230,
            column: 30
          },
          end: {
            line: 230,
            column: 96
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 230,
            column: 67
          },
          end: {
            line: 230,
            column: 78
          }
        }, {
          start: {
            line: 230,
            column: 81
          },
          end: {
            line: 230,
            column: 96
          }
        }],
        line: 230
      },
      "4": {
        loc: {
          start: {
            line: 239,
            column: 8
          },
          end: {
            line: 240,
            column: 22
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 239,
            column: 8
          },
          end: {
            line: 240,
            column: 22
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 239
      },
      "5": {
        loc: {
          start: {
            line: 241,
            column: 8
          },
          end: {
            line: 242,
            column: 22
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 241,
            column: 8
          },
          end: {
            line: 242,
            column: 22
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 241
      },
      "6": {
        loc: {
          start: {
            line: 243,
            column: 8
          },
          end: {
            line: 244,
            column: 22
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 243,
            column: 8
          },
          end: {
            line: 244,
            column: 22
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 243
      },
      "7": {
        loc: {
          start: {
            line: 245,
            column: 8
          },
          end: {
            line: 246,
            column: 22
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 245,
            column: 8
          },
          end: {
            line: 246,
            column: 22
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 245
      },
      "8": {
        loc: {
          start: {
            line: 247,
            column: 8
          },
          end: {
            line: 248,
            column: 22
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 247,
            column: 8
          },
          end: {
            line: 248,
            column: 22
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 247
      },
      "9": {
        loc: {
          start: {
            line: 256,
            column: 22
          },
          end: {
            line: 265,
            column: 9
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 256,
            column: 22
          },
          end: {
            line: 256,
            column: 33
          }
        }, {
          start: {
            line: 256,
            column: 37
          },
          end: {
            line: 265,
            column: 9
          }
        }],
        line: 256
      },
      "10": {
        loc: {
          start: {
            line: 333,
            column: 8
          },
          end: {
            line: 344,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 333,
            column: 8
          },
          end: {
            line: 344,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 333
      },
      "11": {
        loc: {
          start: {
            line: 474,
            column: 69
          },
          end: {
            line: 474,
            column: 83
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 474,
            column: 80
          },
          end: {
            line: 474,
            column: 83
          }
        }],
        line: 474
      },
      "12": {
        loc: {
          start: {
            line: 507,
            column: 27
          },
          end: {
            line: 507,
            column: 70
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 507,
            column: 41
          },
          end: {
            line: 507,
            column: 52
          }
        }, {
          start: {
            line: 507,
            column: 55
          },
          end: {
            line: 507,
            column: 70
          }
        }],
        line: 507
      },
      "13": {
        loc: {
          start: {
            line: 508,
            column: 28
          },
          end: {
            line: 508,
            column: 82
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 508,
            column: 53
          },
          end: {
            line: 508,
            column: 64
          }
        }, {
          start: {
            line: 508,
            column: 67
          },
          end: {
            line: 508,
            column: 82
          }
        }],
        line: 508
      },
      "14": {
        loc: {
          start: {
            line: 522,
            column: 8
          },
          end: {
            line: 523,
            column: 24
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 522,
            column: 8
          },
          end: {
            line: 523,
            column: 24
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 522
      },
      "15": {
        loc: {
          start: {
            line: 524,
            column: 8
          },
          end: {
            line: 525,
            column: 23
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 524,
            column: 8
          },
          end: {
            line: 525,
            column: 23
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 524
      },
      "16": {
        loc: {
          start: {
            line: 526,
            column: 8
          },
          end: {
            line: 527,
            column: 23
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 526,
            column: 8
          },
          end: {
            line: 527,
            column: 23
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 526
      },
      "17": {
        loc: {
          start: {
            line: 528,
            column: 8
          },
          end: {
            line: 529,
            column: 23
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 528,
            column: 8
          },
          end: {
            line: 529,
            column: 23
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 528
      },
      "18": {
        loc: {
          start: {
            line: 530,
            column: 8
          },
          end: {
            line: 531,
            column: 23
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 530,
            column: 8
          },
          end: {
            line: 531,
            column: 23
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 530
      },
      "19": {
        loc: {
          start: {
            line: 540,
            column: 8
          },
          end: {
            line: 543,
            column: 24
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 540,
            column: 8
          },
          end: {
            line: 543,
            column: 24
          }
        }, {
          start: {
            line: 542,
            column: 13
          },
          end: {
            line: 543,
            column: 24
          }
        }],
        line: 540
      },
      "20": {
        loc: {
          start: {
            line: 542,
            column: 13
          },
          end: {
            line: 543,
            column: 24
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 542,
            column: 13
          },
          end: {
            line: 543,
            column: 24
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 542
      },
      "21": {
        loc: {
          start: {
            line: 545,
            column: 8
          },
          end: {
            line: 546,
            column: 24
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 545,
            column: 8
          },
          end: {
            line: 546,
            column: 24
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 545
      },
      "22": {
        loc: {
          start: {
            line: 555,
            column: 8
          },
          end: {
            line: 579,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 555,
            column: 8
          },
          end: {
            line: 579,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 555
      },
      "23": {
        loc: {
          start: {
            line: 581,
            column: 8
          },
          end: {
            line: 606,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 581,
            column: 8
          },
          end: {
            line: 606,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 581
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\EnergyEfficiencyAnalysisEngine.ts",
      mappings: ";AAAA;;;;;;;;;GASG;;;AAEH,qEAkBqC;AAKrC;;;;;;;;;;GAUG;AACH,MAAa,8BAA8B;IAiBzC;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,uBAAuB,CACzC,mBAAwC,EACxC,kBAAsC,EACtC,iBAAqC,EACrC,WAAyB;QAEzB,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC;YACnE,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;YAE7B,yCAAyC;YACzC,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAC7D,mBAAmB,EACnB,kBAAkB,EAClB,iBAAiB,CAClB,CAAC;YAEF,+BAA+B;YAC/B,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,gCAAgC,CACnE,mBAAmB,EACnB,kBAAkB,EAClB,iBAAiB,CAClB,CAAC;YAEF,yBAAyB;YACzB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,oBAAoB,CACjD,iBAAiB,EACjB,WAAW,CACZ,CAAC;YAEF,6BAA6B;YAC7B,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,wBAAwB,CACzD,iBAAiB,EACjB,mBAAmB,CACpB,CAAC;YAEF,+BAA+B;YAC/B,MAAM,mBAAmB,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAC9D,mBAAmB,EACnB,iBAAiB,CAClB,CAAC;YAEF,sCAAsC;YACtC,MAAM,yBAAyB,GAAG,MAAM,IAAI,CAAC,iCAAiC,CAC5E,mBAAmB,EACnB,kBAAkB,EAClB,iBAAiB,EACjB,WAAW,CACZ,CAAC;YAEF,4BAA4B;YAC5B,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,6BAA6B,CAC/D,mBAAmB,EACnB,iBAAiB,CAClB,CAAC;YAEF,MAAM,QAAQ,GAAmB;gBAC/B,EAAE,EAAE,UAAU;gBACd,QAAQ,EAAE,mBAAmB,CAAC,EAAE;gBAChC,iBAAiB,EAAE,SAAS;gBAC5B,iBAAiB;gBACjB,iBAAiB;gBACjB,WAAW;gBACX,eAAe;gBACf,mBAAmB;gBACnB,yBAAyB;gBACzB,gBAAgB;aACjB,CAAC;YAEF,qBAAqB;YACrB,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YAE5C,OAAO,QAAQ,CAAC;QAElB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,sCAAsC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;QACpH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,KAAK,CAAC,0BAA0B,CAC7C,mBAAwC,EACxC,kBAAsC,EACtC,iBAAqC;QAErC,MAAM,UAAU,GAAG,kBAAkB,CAAC,QAAQ,CAAC,KAAK,CAAC;QACrD,MAAM,aAAa,GAAG,mBAAmB,CAAC,gBAAgB,CAAC,aAAa,CAAC;QAEzE,6CAA6C;QAC7C,MAAM,QAAQ,GAAG,iBAAiB,IAAI;YACpC,WAAW,EAAE,EAAE;YACf,WAAW,EAAE,CAAC;YACd,YAAY,EAAE,EAAE;YAChB,WAAW,EAAE,UAAmB;SACjC,CAAC;QAEF,MAAM,oBAAoB,GAAG,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,CAAC;QAEjG,mCAAmC;QACnC,MAAM,cAAc,GAAG,IAAI,CAAC,uBAAuB,CACjD,UAAU,GAAG,oBAAoB,EACjC,iCAAW,CAAC,GAAG,EACf,+BAAS,CAAC,QAAQ,EAClB,uCAAiB,CAAC,UAAU,CAC7B,CAAC;QAEF,yEAAyE;QACzE,MAAM,oBAAoB,GAAG,IAAI,CAAC,uBAAuB,CACvD,UAAU,GAAG,GAAG,GAAG,oBAAoB,EACvC,iCAAW,CAAC,GAAG,EACf,+BAAS,CAAC,QAAQ,EAClB,uCAAiB,CAAC,SAAS,CAC5B,CAAC;QAEF,oBAAoB;QACpB,MAAM,gBAAgB,GAAG,IAAI,CAAC,uBAAuB,CACnD,cAAc,CAAC,KAAK,GAAG,oBAAoB,CAAC,KAAK,EACjD,iCAAW,CAAC,GAAG,EACf,+BAAS,CAAC,QAAQ,EAClB,uCAAiB,CAAC,UAAU,CAC7B,CAAC;QAEF,4CAA4C;QAC5C,MAAM,oBAAoB,GAAG,IAAI,CAAC,6BAA6B,CAC7D,UAAU,EACV,QAAQ,CACT,CAAC;QAEF,yBAAyB;QACzB,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QAEpE,wBAAwB;QACxB,MAAM,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QAElE,OAAO;YACL,gBAAgB;YAChB,cAAc;YACd,oBAAoB;YACpB,sBAAsB,EAAE,oBAAoB;YAC5C,WAAW;YACX,UAAU;SACX,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,uBAAuB,CACpC,KAAa,EACb,KAAkB,EAClB,SAAoB,EACpB,MAAyB,EACzB,WAAmB,GAAG;QAEtB,OAAO;YACL,KAAK;YACL,KAAK;YACL,SAAS;YACT,QAAQ;YACR,MAAM;SACP,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,6BAA6B,CAC1C,UAAkB,EAClB,QAA2B;QAE3B,MAAM,WAAW,GAA2B,EAAE,CAAC;QAE/C,iCAAiC;QACjC,MAAM,aAAa,GAAG;YACpB,EAAE,IAAI,EAAE,CAAC,EAAE,UAAU,EAAE,GAAG,EAAE;YAC5B,EAAE,IAAI,EAAE,CAAC,EAAE,UAAU,EAAE,GAAG,EAAE;YAC5B,EAAE,IAAI,EAAE,EAAE,EAAE,UAAU,EAAE,GAAG,EAAE;YAC7B,EAAE,IAAI,EAAE,EAAE,EAAE,UAAU,EAAE,GAAG,EAAE;YAC7B,EAAE,IAAI,EAAE,EAAE,EAAE,UAAU,EAAE,GAAG,EAAE;YAC7B,EAAE,IAAI,EAAE,EAAE,EAAE,UAAU,EAAE,GAAG,EAAE;SAC9B,CAAC;QAEF,aAAa,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YAC9B,WAAW,CAAC,IAAI,CAAC;gBACf,SAAS,EAAE,GAAG,OAAO,CAAC,IAAI,KAAK;gBAC/B,WAAW,EAAE,UAAU,GAAG,OAAO,CAAC,UAAU;gBAC5C,iBAAiB,EAAE,UAAU,GAAG,OAAO,CAAC,UAAU,GAAG,CAAC,EAAE,SAAS;gBACjE,UAAU,EAAE,OAAO,CAAC,UAAU;aAC/B,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,oBAAoB,CACjC,UAAkB,EAClB,QAA2B;QAE3B,OAAO;YACL,QAAQ,EAAE,UAAU,GAAG,GAAG,EAAE,gBAAgB;YAC5C,QAAQ,EAAE,UAAU,GAAG,GAAG,EAAE,iBAAiB;YAC7C,WAAW,EAAE,UAAU,GAAG,IAAI,EAAE,mBAAmB;YACnD,UAAU,EAAE,IAAI,EAAE,eAAe;YACjC,eAAe,EAAE,IAAI,EAAE,sCAAsC;YAC7D,aAAa,EAAE;gBACb,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,GAAG,GAAG,EAAE;gBAC/C,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,GAAG,GAAG,EAAE;gBAC9C,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,UAAU,GAAG,GAAG,EAAE;gBACjD,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,GAAG,GAAG,EAAE;aAChD;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,mBAAmB,CAChC,UAAkB,EAClB,QAA2B;QAE3B,OAAO;YACL,SAAS,EAAE,UAAU;YACrB,QAAQ,EAAE,OAAO,EAAE,oBAAoB;YACvC,YAAY,EAAE,CAAC,EAAE,UAAU;YAC3B,aAAa,EAAE,OAAgB;YAC/B,gBAAgB,EAAE,GAAG,EAAE,mCAAmC;YAC1D,aAAa,EAAE,UAAU,GAAG,EAAE,EAAE,+BAA+B;YAC/D,oBAAoB,EAAE,UAAU,GAAG,GAAG,CAAC,0BAA0B;SAClE,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,KAAK,CAAC,gCAAgC,CACnD,mBAAwC,EACxC,kBAAsC,EACtC,iBAAoC;QAEpC,MAAM,aAAa,GAAG,mBAAmB,CAAC,gBAAgB,CAAC,aAAa,CAAC;QACzE,MAAM,UAAU,GAAG,kBAAkB,CAAC,QAAQ,CAAC,KAAK,CAAC;QACrD,MAAM,gBAAgB,GAAG,kBAAkB,CAAC,gBAAgB,CAAC,KAAK,CAAC;QAEnE,qCAAqC;QACrC,MAAM,gBAAgB,GAAG,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,aAAa,CAAC,CAAC,QAAQ;QAEtE,2CAA2C;QAC3C,MAAM,YAAY,GAAG,KAAK,CAAC,CAAC,iCAAiC;QAC7D,MAAM,sBAAsB,GAAG,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,YAAY,CAAC;QAE3G,0BAA0B;QAC1B,MAAM,YAAY,GAAG,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,YAAY,CAAC,CAAC,UAAU;QAEnE,0CAA0C;QAC1C,MAAM,eAAe,GAAG,IAAI,CAAC,wBAAwB,CAAC,gBAAgB,CAAC,CAAC;QAExE,uBAAuB;QACvB,MAAM,mBAAmB,GAAG,IAAI,CAAC,4BAA4B,CAC3D,gBAAgB,EAChB,gBAAgB,CACjB,CAAC;QAEF,OAAO;YACL,iBAAiB,EAAE,gBAAgB;YACnC,aAAa,EAAE,kBAAkB,CAAC,aAAa,CAAC,KAAK;YACrD,gBAAgB,EAAE,gBAAgB;YAClC,mBAAmB,EAAE,kBAAkB,CAAC,mBAAmB,CAAC,KAAK;YACjE,gBAAgB;YAChB,sBAAsB;YACtB,YAAY;YACZ,eAAe;YACf,mBAAmB;SACpB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,wBAAwB,CAAC,iBAAyB;QAC/D,OAAO;YACL,iBAAiB;YACjB,cAAc,EAAE,QAAiB;YACjC,SAAS,EAAE,CAAC,EAAE,aAAa;YAC3B,mBAAmB,EAAE,iBAAiB;YACtC,WAAW,EAAE,EAAE,EAAE,SAAS;YAC1B,eAAe,EAAE,EAAE;SACpB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,4BAA4B,CACzC,gBAAwB,EACxB,gBAAwB;QAExB,yBAAyB;QACzB,MAAM,cAAc,GAAG,IAAI,CAAC,CAAC,wBAAwB;QACrD,MAAM,kBAAkB,GAAG,GAAG,CAAC,CAAC,QAAQ;QACxC,MAAM,eAAe,GAAG,GAAG,CAAC,CAAC,QAAQ;QAErC,OAAO;YACL,aAAa,EAAE,mBAA4B;YAC3C,eAAe,EAAE,aAAa;YAC9B,YAAY,EAAE,gBAAgB;YAC9B,cAAc,EAAE,cAAc;YAC9B,eAAe,EAAE,kBAAkB;YACnC,YAAY,EAAE,eAAe;YAC7B,UAAU,EAAE,IAAI,CAAC,sBAAsB,CAAC,gBAAgB,CAAC;YACzD,gBAAgB,EAAE,gBAAgB,IAAI,cAAc,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,eAAe;YACpF,oBAAoB,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,gBAAgB,GAAG,eAAe,CAAC;SACtE,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,sBAAsB,CAAC,GAAW;QAC/C,sEAAsE;QACtE,IAAI,GAAG,IAAI,GAAG;YAAE,OAAO,EAAE,CAAC;QAC1B,IAAI,GAAG,IAAI,GAAG;YAAE,OAAO,EAAE,CAAC;QAC1B,IAAI,GAAG,IAAI,GAAG;YAAE,OAAO,EAAE,CAAC;QAC1B,IAAI,GAAG,IAAI,IAAI;YAAE,OAAO,EAAE,CAAC;QAC3B,IAAI,GAAG,IAAI,GAAG;YAAE,OAAO,EAAE,CAAC;QAC1B,OAAO,CAAC,CAAC;IACX,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,KAAK,CAAC,oBAAoB,CACvC,iBAAoC,EACpC,WAAyB;QAEzB,uCAAuC;QACvC,MAAM,KAAK,GAAG,WAAW,IAAI;YAC3B,UAAU,EAAE,IAAI,EAAE,QAAQ;YAC1B,UAAU,EAAE,IAAI,EAAE,OAAO;YACzB,SAAS,EAAE,IAAI,EAAE,UAAU;YAC3B,cAAc,EAAE;gBACd,IAAI,EAAE,IAAI;gBACV,OAAO,EAAE,IAAI;gBACb,QAAQ,EAAE,IAAI;aACf;SACF,CAAC;QAEF,0BAA0B;QAC1B,MAAM,UAAU,GAAG,iBAAiB,CAAC,gBAAgB,CAAC,KAAK,GAAG,KAAK,CAAC,UAAU,CAAC;QAC/E,MAAM,UAAU,GAAG,iBAAiB,CAAC,UAAU,CAAC,SAAS,GAAG,KAAK,CAAC,UAAU,GAAG,EAAE,CAAC,CAAC,SAAS;QAC5F,MAAM,SAAS,GAAG,KAAK,CAAC,SAAS,GAAG,EAAE,CAAC,CAAC,SAAS;QACjD,MAAM,SAAS,GAAG,UAAU,GAAG,UAAU,GAAG,SAAS,CAAC;QAEtD,MAAM,YAAY,GAAG;YACnB,SAAS;YACT,UAAU;YACV,UAAU;YACV,SAAS;YACT,QAAQ,EAAE,KAAK;YACf,SAAS,EAAE,+BAAS,CAAC,QAAQ;SAC9B,CAAC;QAEF,gDAAgD;QAChD,MAAM,cAAc,GAAG,IAAI,CAAC,wBAAwB,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,CAAC,uBAAuB;QAEjG,qCAAqC;QACrC,MAAM,uBAAuB,GAAG,IAAI,CAAC,+BAA+B,CAClE,iBAAiB,EACjB,YAAY,CACb,CAAC;QAEF,gCAAgC;QAChC,MAAM,oBAAoB,GAAG,IAAI,CAAC,0BAA0B,CAAC,KAAK,CAAC,CAAC;QAEpE,OAAO;YACL,YAAY;YACZ,cAAc;YACd,uBAAuB;YACvB,oBAAoB;YACpB,aAAa,EAAE;gBACb,aAAa,EAAE,iBAAiB,CAAC,UAAU,CAAC,SAAS;gBACrD,UAAU,EAAE,KAAK,CAAC,UAAU;gBAC5B,gBAAgB,EAAE,UAAU;gBAC5B,oBAAoB,EAAE,iBAAiB,CAAC,UAAU,CAAC,oBAAoB;gBACvE,gBAAgB,EAAE,iBAAiB,CAAC,UAAU,CAAC,oBAAoB,GAAG,KAAK,CAAC,UAAU,GAAG,EAAE;aAC5F;YACD,gBAAgB,EAAE;gBAChB,OAAO,EAAE,IAAI;gBACb,QAAQ,EAAE,KAAK,CAAC,cAAc,CAAC,IAAI;gBACnC,WAAW,EAAE,KAAK,CAAC,cAAc,CAAC,OAAO;gBACzC,YAAY,EAAE,KAAK,CAAC,cAAc,CAAC,QAAQ;gBAC3C,SAAS,EAAE,aAAa;gBACxB,YAAY,EAAE,aAAa;gBAC3B,gBAAgB,EAAE,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,EAAE,KAAK,CAAC,cAAc,CAAC;aACpF;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,wBAAwB,CACrC,YAAiB,EACjB,cAAsB;QAEtB,MAAM,WAAW,GAAqB,EAAE,CAAC;QAEzC,KAAK,IAAI,IAAI,GAAG,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC;YACrC,MAAM,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,cAAc,EAAE,IAAI,CAAC,CAAC;YAC5D,WAAW,CAAC,IAAI,CAAC;gBACf,IAAI;gBACJ,SAAS,EAAE,YAAY,CAAC,SAAS,GAAG,gBAAgB;gBACpD,UAAU,EAAE,YAAY,CAAC,UAAU,GAAG,gBAAgB;gBACtD,UAAU,EAAE,YAAY,CAAC,UAAU,GAAG,gBAAgB;gBACtD,SAAS,EAAE,YAAY,CAAC,SAAS,GAAG,gBAAgB;gBACpD,cAAc;gBACd,cAAc,EAAE,YAAY,CAAC,SAAS,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,cAAc,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,cAAc,CAAC;aACrG,CAAC,CAAC;QACL,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,+BAA+B,CAC5C,iBAAoC,EACpC,YAAiB;QAEjB,MAAM,aAAa,GAA4B,EAAE,CAAC;QAElD,oCAAoC;QACpC,IAAI,iBAAiB,CAAC,UAAU,CAAC,oBAAoB,GAAG,CAAC,EAAE,CAAC;YAC1D,aAAa,CAAC,IAAI,CAAC;gBACjB,EAAE,EAAE,uBAAuB;gBAC3B,IAAI,EAAE,uBAAuB;gBAC7B,WAAW,EAAE,qEAAqE;gBAClF,gBAAgB,EAAE,iBAAiB,CAAC,UAAU,CAAC,oBAAoB,GAAG,EAAE,GAAG,EAAE,EAAE,qBAAqB;gBACpG,kBAAkB,EAAE,IAAI;gBACxB,aAAa,EAAE,EAAE,EAAE,SAAS;gBAC5B,WAAW,EAAE,kBAA2B;gBACxC,UAAU,EAAE,GAAG;aAChB,CAAC,CAAC;QACL,CAAC;QAED,gCAAgC;QAChC,aAAa,CAAC,IAAI,CAAC;YACjB,EAAE,EAAE,wBAAwB;YAC5B,IAAI,EAAE,+BAA+B;YACrC,WAAW,EAAE,+EAA+E;YAC5F,gBAAgB,EAAE,YAAY,CAAC,UAAU,GAAG,IAAI,EAAE,qBAAqB;YACvE,kBAAkB,EAAE,KAAK;YACzB,aAAa,EAAE,EAAE,EAAE,SAAS;YAC5B,WAAW,EAAE,kBAA2B;YACxC,UAAU,EAAE,GAAG;SAChB,CAAC,CAAC;QAEH,OAAO,aAAa,CAAC;IACvB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,0BAA0B,CAAC,KAAkB;QAC1D,OAAO;YACL,YAAY,EAAE,4BAA4B;YAC1C,aAAa,EAAE;gBACb,QAAQ,EAAE,KAAK,CAAC,UAAU;gBAC1B,WAAW,EAAE;oBACX,EAAE,IAAI,EAAE,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,UAAU,GAAG,GAAG,EAAE;oBAC1D,EAAE,IAAI,EAAE,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,UAAU,EAAE;oBACpD,EAAE,IAAI,EAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,CAAC,UAAU,GAAG,GAAG,EAAE;iBAC/D;aACF;YACD,aAAa,EAAE;gBACb,IAAI,EAAE,KAAK,CAAC,UAAU;gBACtB,aAAa,EAAE,IAAI;gBACnB,aAAa,EAAE;oBACb,MAAM,EAAE,KAAK,CAAC,UAAU,GAAG,GAAG;oBAC9B,MAAM,EAAE,KAAK,CAAC,UAAU,GAAG,GAAG;iBAC/B;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,KAAK,CAAC,SAAS;gBAC/B,cAAc,EAAE,CAAC;gBACjB,WAAW,EAAE,KAAK,CAAC,SAAS;aAC7B;YACD,cAAc,EAAE,KAAK,CAAC,cAAc;SACrC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,mBAAmB,CAChC,iBAAoC,EACpC,QAAa;QAEb,qCAAqC;QACrC,MAAM,WAAW,GAAG,iBAAiB,CAAC,gBAAgB,CAAC,KAAK,CAAC;QAC7D,MAAM,iBAAiB,GAAG,GAAG,CAAC,CAAC,wBAAwB;QACvD,MAAM,oBAAoB,GAAG,GAAG,CAAC,CAAC,4BAA4B;QAC9D,MAAM,qBAAqB,GAAG,GAAG,CAAC,CAAC,4BAA4B;QAE/D,MAAM,WAAW,GAAG,WAAW,GAAG,IAAI,CAAC,CAAC,YAAY;QACpD,MAAM,OAAO,GAAG,CAAC,WAAW,GAAG,iBAAiB,GAAG,QAAQ,CAAC,IAAI,CAAC;YAClD,CAAC,WAAW,GAAG,oBAAoB,GAAG,QAAQ,CAAC,OAAO,CAAC;YACvD,CAAC,WAAW,GAAG,qBAAqB,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAEzE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,WAAW,GAAG,OAAO,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,KAAK,CAAC,wBAAwB,CAC3C,iBAAoC,EACpC,mBAAwC;QAExC,MAAM,cAAc,GAAG,iBAAiB,CAAC,gBAAgB,CAAC,KAAK,CAAC;QAEhE,kCAAkC;QAClC,MAAM,oBAAoB,GAAG,IAAI,CAAC,yBAAyB,CACzD,cAAc,GAAG,IAAI,CAAC,gBAAgB,CAAC,YAAY,EACnD,mCAAa,CAAC,OAAO,EACrB,+BAAS,CAAC,QAAQ,EAClB,mCAAa,CAAC,OAAO,CACtB,CAAC;QAEF,4CAA4C;QAC5C,MAAM,iBAAiB,GAAG,IAAI,CAAC,yBAAyB,CACtD,GAAG,EAAE,6CAA6C;QAClD,mCAAa,CAAC,OAAO,EACrB,+BAAS,CAAC,QAAQ,EAClB,mCAAa,CAAC,OAAO,CACtB,CAAC;QAEF,kBAAkB;QAClB,MAAM,cAAc,GAAG,IAAI,CAAC,yBAAyB,CACnD,oBAAoB,CAAC,KAAK,GAAG,iBAAiB,CAAC,KAAK,EACpD,mCAAa,CAAC,OAAO,EACrB,+BAAS,CAAC,QAAQ,EAClB,mCAAa,CAAC,OAAO,CACtB,CAAC;QAEF,sBAAsB;QACtB,MAAM,iBAAiB,GAAG;YACxB;gBACE,MAAM,EAAE,kBAAkB;gBAC1B,SAAS,EAAE,oBAAoB,CAAC,KAAK;gBACrC,UAAU,EAAE,CAAC,oBAAoB,CAAC,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,GAAG,GAAG;gBACrE,cAAc,EAAE,IAAI,CAAC,gBAAgB,CAAC,YAAY;aACnD;YACD;gBACE,MAAM,EAAE,iBAAiB;gBACzB,SAAS,EAAE,iBAAiB,CAAC,KAAK;gBAClC,UAAU,EAAE,CAAC,iBAAiB,CAAC,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,GAAG,GAAG;gBAClE,cAAc,EAAE,CAAC;aAClB;SACF,CAAC;QAEF,+BAA+B;QAC/B,MAAM,cAAc,GAAG;YACrB,gBAAgB,EAAE,cAAc,CAAC,KAAK;YACtC,cAAc,EAAE,QAAiB;YACjC,kBAAkB,EAAE,cAAc,CAAC,KAAK;YACxC,kBAAkB,EAAE,cAAc,CAAC,KAAK,GAAG,GAAG,EAAE,0BAA0B;YAC1E,WAAW,EAAE,EAAE,CAAC,QAAQ;SACzB,CAAC;QAEF,uBAAuB;QACvB,MAAM,mBAAmB,GAAG;YAC1B;gBACE,IAAI,EAAE,kBAAkB;gBACxB,SAAS,EAAE,oBAAoB,CAAC,KAAK,GAAG,GAAG,EAAE,uBAAuB;gBACpE,IAAI,EAAE,oBAAoB,CAAC,KAAK,GAAG,IAAI,EAAE,gBAAgB;gBACzD,cAAc,EAAE,2CAA2C;aAC5D;YACD;gBACE,IAAI,EAAE,mBAAmB;gBACzB,SAAS,EAAE,oBAAoB,CAAC,KAAK,GAAG,GAAG,EAAE,0BAA0B;gBACvE,IAAI,EAAE,CAAC,EAAE,eAAe;gBACxB,cAAc,EAAE,iDAAiD;aAClE;SACF,CAAC;QAEF,uBAAuB;QACvB,MAAM,mBAAmB,GAAG;YAC1B,aAAa,EAAE,kBAAkB;YACjC,gBAAgB,EAAE,cAAc,CAAC,KAAK,GAAG,mBAAmB,CAAC,gBAAgB,CAAC,aAAa,EAAE,cAAc;YAC3G,kBAAkB,EAAE,IAAI,EAAE,mBAAmB;YAC7C,UAAU,EAAE,EAAE;YACd,oBAAoB,EAAE,cAAc,CAAC,KAAK,GAAG,IAAI;SAClD,CAAC;QAEF,OAAO;YACL,cAAc;YACd,oBAAoB;YACpB,iBAAiB;YACjB,iBAAiB;YACjB,cAAc;YACd,mBAAmB;YACnB,mBAAmB;SACpB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,yBAAyB,CACtC,KAAa,EACb,KAAoB,EACpB,SAAoB,EACpB,KAAoB,EACpB,WAAmB,GAAG;QAEtB,OAAO;YACL,KAAK;YACL,KAAK;YACL,SAAS;YACT,KAAK;YACL,QAAQ;SACT,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,KAAK,CAAC,yBAAyB,CAC5C,mBAAwC,EACxC,iBAA0C;QAE1C,MAAM,GAAG,GAAG,iBAAiB,CAAC,gBAAgB,CAAC;QAC/C,MAAM,gBAAgB,GAAG,iBAAiB,CAAC,gBAAgB,CAAC;QAE5D,OAAO;YACL,aAAa,EAAE,mBAAmB;YAClC,eAAe,EAAE,+BAA+B;YAChD,eAAe,EAAE;gBACf,OAAO,EAAE,GAAG;gBACZ,SAAS,EAAE,IAAI,EAAE,oBAAoB;gBACrC,eAAe,EAAE,GAAG;gBACpB,YAAY,EAAE,GAAG;gBACjB,UAAU,EAAE,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC;aAC7C;YACD,gBAAgB,EAAE;gBAChB,OAAO,EAAE,gBAAgB;gBACzB,SAAS,EAAE,EAAE;gBACb,eAAe,EAAE,EAAE;gBACnB,YAAY,EAAE,EAAE;gBAChB,KAAK,EAAE,IAAI,CAAC,wBAAwB,CAAC,gBAAgB,CAAC;aACvD;YACD,gBAAgB,EAAE;gBAChB,SAAS,EAAE,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,eAAe;gBACtD,UAAU,EAAE,gBAAgB,IAAI,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,eAAe;gBAClE,IAAI,EAAE,IAAI,CAAC,mBAAmB,CAAC,GAAG,EAAE,gBAAgB,CAAC;aACtD;YACD,oBAAoB,EAAE;gBACpB,aAAa,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,sBAAsB;gBAC3E,WAAW,EAAE,CAAC,EAAE,4CAA4C;gBAC5D,iBAAiB,EAAE,CAAC,CAAC,gDAAgD;aACtE;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,wBAAwB,CAAC,UAAkB;QACxD,IAAI,UAAU,IAAI,EAAE;YAAE,OAAO,IAAI,CAAC;QAClC,IAAI,UAAU,IAAI,EAAE;YAAE,OAAO,GAAG,CAAC;QACjC,IAAI,UAAU,IAAI,EAAE;YAAE,OAAO,GAAG,CAAC;QACjC,IAAI,UAAU,IAAI,EAAE;YAAE,OAAO,GAAG,CAAC;QACjC,IAAI,UAAU,IAAI,EAAE;YAAE,OAAO,GAAG,CAAC;QACjC,OAAO,GAAG,CAAC;IACb,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,mBAAmB,CAAC,GAAW,EAAE,UAAkB;QAChE,IAAI,MAAM,GAAG,CAAC,CAAC;QAEf,uCAAuC;QACvC,IAAI,GAAG,IAAI,GAAG;YAAE,MAAM,IAAI,CAAC,CAAC;aACvB,IAAI,GAAG,IAAI,GAAG;YAAE,MAAM,IAAI,CAAC,CAAC;QAEjC,oCAAoC;QACpC,IAAI,UAAU,IAAI,EAAE;YAAE,MAAM,IAAI,CAAC,CAAC;QAElC,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,mBAAmB;IACjD,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,KAAK,CAAC,iCAAiC,CACpD,mBAAwC,EACxC,kBAAsC,EACtC,iBAA0C,EAC1C,WAAwB;QAExB,MAAM,aAAa,GAAoC,EAAE,CAAC;QAE1D,yBAAyB;QACzB,IAAI,iBAAiB,CAAC,gBAAgB,GAAG,GAAG,EAAE,CAAC;YAC7C,aAAa,CAAC,IAAI,CAAC;gBACjB,EAAE,EAAE,wBAAwB;gBAC5B,IAAI,EAAE,wBAAwB;gBAC9B,WAAW,EAAE,mFAAmF;gBAChG,QAAQ,EAAE,sBAAsB;gBAChC,sBAAsB,EAAE,EAAE,EAAE,IAAI;gBAChC,oBAAoB,EAAE,WAAW,CAAC,YAAY,CAAC,UAAU,GAAG,IAAI;gBAChE,kBAAkB,EAAE,IAAI;gBACxB,aAAa,EAAE,EAAE,EAAE,SAAS;gBAC5B,UAAU,EAAE,KAAK;gBACjB,QAAQ,EAAE,MAAM;gBAChB,eAAe,EAAE;oBACf,4BAA4B;oBAC5B,gCAAgC;oBAChC,4BAA4B;iBAC7B;gBACD,eAAe,EAAE;oBACf,eAAe,EAAE,EAAE;oBACnB,eAAe,EAAE,EAAE;oBACnB,iBAAiB,EAAE,EAAE;oBACrB,WAAW,EAAE,WAAW,CAAC,YAAY,CAAC,UAAU,GAAG,IAAI;iBACxD;aACF,CAAC,CAAC;QACL,CAAC;QAED,2BAA2B;QAC3B,IAAI,kBAAkB,CAAC,mBAAmB,CAAC,KAAK,GAAG,GAAG,EAAE,CAAC;YACvD,aAAa,CAAC,IAAI,CAAC;gBACjB,EAAE,EAAE,mBAAmB;gBACvB,IAAI,EAAE,0BAA0B;gBAChC,WAAW,EAAE,2EAA2E;gBACxF,QAAQ,EAAE,eAAe;gBACzB,sBAAsB,EAAE,EAAE,EAAE,IAAI;gBAChC,oBAAoB,EAAE,WAAW,CAAC,YAAY,CAAC,UAAU,GAAG,IAAI;gBAChE,kBAAkB,EAAE,KAAK;gBACzB,aAAa,EAAE,EAAE,EAAE,SAAS;gBAC5B,UAAU,EAAE,MAAM;gBAClB,QAAQ,EAAE,QAAQ;gBAClB,eAAe,EAAE;oBACf,qBAAqB;oBACrB,gCAAgC;oBAChC,4BAA4B;oBAC5B,qBAAqB;iBACtB;gBACD,eAAe,EAAE;oBACf,eAAe,EAAE,EAAE;oBACnB,eAAe,EAAE,EAAE;oBACnB,iBAAiB,EAAE,EAAE;oBACrB,WAAW,EAAE,WAAW,CAAC,YAAY,CAAC,UAAU,GAAG,IAAI;iBACxD;aACF,CAAC,CAAC;QACL,CAAC;QAED,sBAAsB;QACtB,aAAa,CAAC,IAAI,CAAC;YACjB,EAAE,EAAE,qBAAqB;YACzB,IAAI,EAAE,4BAA4B;YAClC,WAAW,EAAE,mEAAmE;YAChF,QAAQ,EAAE,aAAa;YACvB,sBAAsB,EAAE,CAAC,EAAE,IAAI;YAC/B,oBAAoB,EAAE,WAAW,CAAC,YAAY,CAAC,UAAU,GAAG,IAAI;YAChE,kBAAkB,EAAE,IAAI;YACxB,aAAa,EAAE,EAAE,EAAE,SAAS;YAC5B,UAAU,EAAE,KAAK;YACjB,QAAQ,EAAE,QAAQ;YAClB,eAAe,EAAE;gBACf,6CAA6C;gBAC7C,+BAA+B;gBAC/B,+BAA+B;aAChC;YACD,eAAe,EAAE;gBACf,eAAe,EAAE,CAAC;gBAClB,eAAe,EAAE,CAAC;gBAClB,iBAAiB,EAAE,CAAC;gBACpB,WAAW,EAAE,WAAW,CAAC,YAAY,CAAC,UAAU,GAAG,IAAI;aACxD;SACF,CAAC,CAAC;QAEH,OAAO,aAAa,CAAC;IACvB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,KAAK,CAAC,6BAA6B,CAChD,mBAAwC,EACxC,iBAAoC;QAEpC,MAAM,eAAe,GAAG,iBAAiB,CAAC,gBAAgB,CAAC,KAAK,CAAC;QAEjE,OAAO;YACL,iBAAiB,EAAE;gBACjB;oBACE,MAAM,EAAE,QAAQ;oBAChB,iBAAiB,EAAE,eAAe,GAAG,IAAI,EAAE,gBAAgB;oBAC3D,WAAW,EAAE,iBAAiB,CAAC,WAAW,CAAC,WAAW,GAAG,GAAG;oBAC5D,QAAQ,EAAE,iBAAiB,CAAC,WAAW,CAAC,QAAQ,GAAG,GAAG;oBACtD,cAAc,EAAE,IAAI;oBACpB,UAAU,EAAE,EAAE;oBACd,KAAK,EAAE,eAAe,GAAG,IAAI,GAAG,IAAI,CAAC,YAAY;iBAClD;gBACD;oBACE,MAAM,EAAE,QAAQ;oBAChB,iBAAiB,EAAE,eAAe,GAAG,IAAI,EAAE,gBAAgB;oBAC3D,WAAW,EAAE,iBAAiB,CAAC,WAAW,CAAC,WAAW,GAAG,GAAG;oBAC5D,QAAQ,EAAE,iBAAiB,CAAC,WAAW,CAAC,QAAQ,GAAG,GAAG;oBACtD,cAAc,EAAE,IAAI;oBACpB,UAAU,EAAE,EAAE,EAAE,uCAAuC;oBACvD,KAAK,EAAE,eAAe,GAAG,IAAI,GAAG,IAAI,CAAC,sBAAsB;iBAC5D;gBACD;oBACE,MAAM,EAAE,MAAM;oBACd,iBAAiB,EAAE,eAAe,GAAG,IAAI,EAAE,gBAAgB;oBAC3D,WAAW,EAAE,iBAAiB,CAAC,WAAW,CAAC,WAAW,GAAG,GAAG;oBAC5D,QAAQ,EAAE,iBAAiB,CAAC,WAAW,CAAC,QAAQ,GAAG,GAAG;oBACtD,cAAc,EAAE,IAAI;oBACpB,UAAU,EAAE,EAAE;oBACd,KAAK,EAAE,eAAe,GAAG,IAAI,GAAG,IAAI;iBACrC;gBACD;oBACE,MAAM,EAAE,QAAQ;oBAChB,iBAAiB,EAAE,eAAe,GAAG,IAAI,EAAE,gBAAgB;oBAC3D,WAAW,EAAE,iBAAiB,CAAC,WAAW,CAAC,WAAW,GAAG,GAAG;oBAC5D,QAAQ,EAAE,iBAAiB,CAAC,WAAW,CAAC,QAAQ,GAAG,GAAG;oBACtD,cAAc,EAAE,IAAI;oBACpB,UAAU,EAAE,EAAE,EAAE,mCAAmC;oBACnD,KAAK,EAAE,eAAe,GAAG,IAAI,GAAG,IAAI,CAAC,qBAAqB;iBAC3D;aACF;YACD,WAAW,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC;YAC/B,yBAAyB,EAAE;gBACzB;oBACE,MAAM,EAAE,QAAQ;oBAChB,WAAW,EAAE,sBAAsB;oBACnC,SAAS,EAAE,mDAAmD;oBAC9D,OAAO,EAAE,IAAI;iBACd;gBACD;oBACE,MAAM,EAAE,QAAQ;oBAChB,WAAW,EAAE,+BAA+B;oBAC5C,SAAS,EAAE,oCAAoC;oBAC/C,OAAO,EAAE,GAAG;iBACb;aACF;YACD,kBAAkB,EAAE;gBAClB,sBAAsB,EAAE,IAAI,EAAE,yBAAyB;gBACvD,mBAAmB,EAAE,KAAK,EAAE,uBAAuB;gBACnD,mBAAmB,EAAE,EAAE,EAAE,KAAK;gBAC9B,gBAAgB,EAAE,EAAE,EAAE,KAAK;gBAC3B,gBAAgB,EAAE,EAAE,CAAC,KAAK;aAC3B;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,kBAAkB,CAAC,QAAgB;QAChD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC1D,OAAO,mBAAmB,QAAQ,IAAI,SAAS,IAAI,MAAM,EAAE,CAAC;IAC9D,CAAC;;AA53BH,wEA63BC;AA53ByB,sCAAO,GAAG,OAAO,CAAC;AAClB,2CAAY,GAAG,IAAI,GAAG,EAA0B,CAAC;AAEzE,8BAA8B;AACN,yCAAU,GAAG,OAAO,CAAC;AACrB,uCAAQ,GAAG,KAAK,CAAC;AACjB,yCAAU,GAAG,WAAW,CAAC;AAEjD,4CAA4C;AACpB,+CAAgB,GAAG;IACzC,YAAY,EAAE,GAAG,EAAE,kBAAkB;IACrC,IAAI,EAAE,IAAI;IACV,WAAW,EAAE,IAAI;IACjB,SAAS,EAAE,IAAI;CAChB,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\EnergyEfficiencyAnalysisEngine.ts"],
      sourcesContent: ["/**\r\n * Energy Efficiency Analysis Engine\r\n * \r\n * Comprehensive energy efficiency analysis service for Phase 3 Priority 3: Advanced System Analysis Tools\r\n * Provides energy consumption analysis, fan power optimization, lifecycle energy calculations,\r\n * and carbon footprint assessment tools for HVAC duct systems.\r\n * \r\n * @version 3.0.0\r\n * <AUTHOR> Suite Development Team\r\n */\r\n\r\nimport {\r\n  EnergyAnalysis,\r\n  EnergyConsumption,\r\n  EnergyEfficiencyMetrics,\r\n  EnergyCosts,\r\n  CarbonFootprint,\r\n  EnergyBenchmark,\r\n  EnergyOptimizationOpportunity,\r\n  SeasonalEnergyAnalysis,\r\n  EnergyMeasurement,\r\n  EnergyUnits,\r\n  TimeFrame,\r\n  SystemConfiguration,\r\n  PerformanceMetrics,\r\n  EmissionMeasurement,\r\n  EmissionUnits,\r\n  EmissionScope,\r\n  MeasurementSource\r\n} from './types/SystemAnalysisTypes';\r\n\r\nimport { SystemPerformanceAnalysisEngine } from './SystemPerformanceAnalysisEngine';\r\nimport { AirPropertiesCalculator } from './AirPropertiesCalculator';\r\n\r\n/**\r\n * Energy Efficiency Analysis Engine\r\n * \r\n * Provides comprehensive energy efficiency analysis capabilities including:\r\n * - Energy consumption breakdown and analysis\r\n * - Fan power optimization calculations\r\n * - Lifecycle energy cost projections\r\n * - Carbon footprint assessment\r\n * - Energy benchmarking and comparison\r\n * - Seasonal energy analysis\r\n */\r\nexport class EnergyEfficiencyAnalysisEngine {\r\n  private static readonly VERSION = '3.0.0';\r\n  private static readonly ENERGY_CACHE = new Map<string, EnergyAnalysis>();\r\n  \r\n  // Energy conversion constants\r\n  private static readonly KWH_TO_BTU = 3412.14;\r\n  private static readonly HP_TO_KW = 0.746;\r\n  private static readonly CFM_TO_M3S = 0.000471947;\r\n  \r\n  // Carbon emission factors (kg CO2e per kWh)\r\n  private static readonly EMISSION_FACTORS = {\r\n    GRID_AVERAGE: 0.4, // US grid average\r\n    COAL: 0.82,\r\n    NATURAL_GAS: 0.35,\r\n    RENEWABLE: 0.02\r\n  };\r\n\r\n  /**\r\n   * Perform comprehensive energy efficiency analysis\r\n   */\r\n  public static async analyzeEnergyEfficiency(\r\n    systemConfiguration: SystemConfiguration,\r\n    performanceMetrics: PerformanceMetrics,\r\n    operatingSchedule?: OperatingSchedule,\r\n    energyRates?: EnergyRates\r\n  ): Promise<EnergyAnalysis> {\r\n    try {\r\n      const analysisId = this.generateAnalysisId(systemConfiguration.id);\r\n      const timestamp = new Date();\r\n\r\n      // Calculate energy consumption breakdown\r\n      const energyConsumption = await this.calculateEnergyConsumption(\r\n        systemConfiguration,\r\n        performanceMetrics,\r\n        operatingSchedule\r\n      );\r\n\r\n      // Calculate efficiency metrics\r\n      const efficiencyMetrics = await this.calculateEnergyEfficiencyMetrics(\r\n        systemConfiguration,\r\n        performanceMetrics,\r\n        energyConsumption\r\n      );\r\n\r\n      // Calculate energy costs\r\n      const energyCosts = await this.calculateEnergyCosts(\r\n        energyConsumption,\r\n        energyRates\r\n      );\r\n\r\n      // Calculate carbon footprint\r\n      const carbonFootprint = await this.calculateCarbonFootprint(\r\n        energyConsumption,\r\n        systemConfiguration\r\n      );\r\n\r\n      // Perform benchmark comparison\r\n      const benchmarkComparison = await this.performEnergyBenchmarking(\r\n        systemConfiguration,\r\n        efficiencyMetrics\r\n      );\r\n\r\n      // Identify optimization opportunities\r\n      const optimizationOpportunities = await this.identifyOptimizationOpportunities(\r\n        systemConfiguration,\r\n        performanceMetrics,\r\n        efficiencyMetrics,\r\n        energyCosts\r\n      );\r\n\r\n      // Perform seasonal analysis\r\n      const seasonalAnalysis = await this.performSeasonalEnergyAnalysis(\r\n        systemConfiguration,\r\n        energyConsumption\r\n      );\r\n\r\n      const analysis: EnergyAnalysis = {\r\n        id: analysisId,\r\n        systemId: systemConfiguration.id,\r\n        analysisTimestamp: timestamp,\r\n        energyConsumption,\r\n        efficiencyMetrics,\r\n        energyCosts,\r\n        carbonFootprint,\r\n        benchmarkComparison,\r\n        optimizationOpportunities,\r\n        seasonalAnalysis\r\n      };\r\n\r\n      // Cache the analysis\r\n      this.ENERGY_CACHE.set(analysisId, analysis);\r\n\r\n      return analysis;\r\n\r\n    } catch (error) {\r\n      throw new Error(`Energy efficiency analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Calculate comprehensive energy consumption breakdown\r\n   */\r\n  private static async calculateEnergyConsumption(\r\n    systemConfiguration: SystemConfiguration,\r\n    performanceMetrics: PerformanceMetrics,\r\n    operatingSchedule?: OperatingSchedule\r\n  ): Promise<EnergyConsumption> {\r\n    const fanPowerKW = performanceMetrics.fanPower.value;\r\n    const designAirflow = systemConfiguration.designParameters.designAirflow;\r\n    \r\n    // Default operating schedule if not provided\r\n    const schedule = operatingSchedule || {\r\n      hoursPerDay: 12,\r\n      daysPerWeek: 5,\r\n      weeksPerYear: 50,\r\n      loadProfile: 'constant' as const\r\n    };\r\n\r\n    const annualOperatingHours = schedule.hoursPerDay * schedule.daysPerWeek * schedule.weeksPerYear;\r\n\r\n    // Calculate fan energy consumption\r\n    const fanConsumption = this.createEnergyMeasurement(\r\n      fanPowerKW * annualOperatingHours,\r\n      EnergyUnits.KWH,\r\n      TimeFrame.ANNUALLY,\r\n      MeasurementSource.CALCULATED\r\n    );\r\n\r\n    // Calculate auxiliary equipment consumption (10% of fan power typically)\r\n    const auxiliaryConsumption = this.createEnergyMeasurement(\r\n      fanPowerKW * 0.1 * annualOperatingHours,\r\n      EnergyUnits.KWH,\r\n      TimeFrame.ANNUALLY,\r\n      MeasurementSource.ESTIMATED\r\n    );\r\n\r\n    // Total consumption\r\n    const totalConsumption = this.createEnergyMeasurement(\r\n      fanConsumption.value + auxiliaryConsumption.value,\r\n      EnergyUnits.KWH,\r\n      TimeFrame.ANNUALLY,\r\n      MeasurementSource.CALCULATED\r\n    );\r\n\r\n    // Calculate time-of-day consumption profile\r\n    const timeOfDayConsumption = this.calculateTimeOfDayConsumption(\r\n      fanPowerKW,\r\n      schedule\r\n    );\r\n\r\n    // Calculate load profile\r\n    const loadProfile = this.calculateLoadProfile(fanPowerKW, schedule);\r\n\r\n    // Calculate peak demand\r\n    const peakDemand = this.calculatePeakDemand(fanPowerKW, schedule);\r\n\r\n    return {\r\n      totalConsumption,\r\n      fanConsumption,\r\n      auxiliaryConsumption,\r\n      consumptionByTimeOfDay: timeOfDayConsumption,\r\n      loadProfile,\r\n      peakDemand\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create standardized energy measurement\r\n   */\r\n  private static createEnergyMeasurement(\r\n    value: number,\r\n    units: EnergyUnits,\r\n    timeFrame: TimeFrame,\r\n    source: MeasurementSource,\r\n    accuracy: number = 0.9\r\n  ): EnergyMeasurement {\r\n    return {\r\n      value,\r\n      units,\r\n      timeFrame,\r\n      accuracy,\r\n      source\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate time-of-day energy consumption\r\n   */\r\n  private static calculateTimeOfDayConsumption(\r\n    fanPowerKW: number,\r\n    schedule: OperatingSchedule\r\n  ): TimeOfDayConsumption[] {\r\n    const consumption: TimeOfDayConsumption[] = [];\r\n    \r\n    // Simplified time-of-day profile\r\n    const hourlyProfile = [\r\n      { hour: 6, loadFactor: 0.8 },\r\n      { hour: 8, loadFactor: 1.0 },\r\n      { hour: 12, loadFactor: 0.9 },\r\n      { hour: 17, loadFactor: 1.0 },\r\n      { hour: 20, loadFactor: 0.6 },\r\n      { hour: 22, loadFactor: 0.3 }\r\n    ];\r\n\r\n    hourlyProfile.forEach(profile => {\r\n      consumption.push({\r\n        timeOfDay: `${profile.hour}:00`,\r\n        powerDemand: fanPowerKW * profile.loadFactor,\r\n        energyConsumption: fanPowerKW * profile.loadFactor * 1, // 1 hour\r\n        loadFactor: profile.loadFactor\r\n      });\r\n    });\r\n\r\n    return consumption;\r\n  }\r\n\r\n  /**\r\n   * Calculate system load profile\r\n   */\r\n  private static calculateLoadProfile(\r\n    fanPowerKW: number,\r\n    schedule: OperatingSchedule\r\n  ): LoadProfile {\r\n    return {\r\n      baseLoad: fanPowerKW * 0.3, // 30% base load\r\n      peakLoad: fanPowerKW * 1.0, // 100% peak load\r\n      averageLoad: fanPowerKW * 0.75, // 75% average load\r\n      loadFactor: 0.75, // Average/Peak\r\n      diversityFactor: 0.85, // Accounting for non-coincident peaks\r\n      demandProfile: [\r\n        { period: 'morning', demand: fanPowerKW * 0.8 },\r\n        { period: 'midday', demand: fanPowerKW * 1.0 },\r\n        { period: 'afternoon', demand: fanPowerKW * 0.9 },\r\n        { period: 'evening', demand: fanPowerKW * 0.6 }\r\n      ]\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate peak demand characteristics\r\n   */\r\n  private static calculatePeakDemand(\r\n    fanPowerKW: number,\r\n    schedule: OperatingSchedule\r\n  ): PeakDemand {\r\n    return {\r\n      peakPower: fanPowerKW,\r\n      peakTime: '14:00', // 2 PM typical peak\r\n      peakDuration: 2, // 2 hours\r\n      peakFrequency: 'daily' as const,\r\n      coincidentFactor: 0.9, // 90% coincident with utility peak\r\n      demandCharges: fanPowerKW * 15, // $15/kW typical demand charge\r\n      peakShavingPotential: fanPowerKW * 0.2 // 20% potential reduction\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate energy efficiency metrics\r\n   */\r\n  private static async calculateEnergyEfficiencyMetrics(\r\n    systemConfiguration: SystemConfiguration,\r\n    performanceMetrics: PerformanceMetrics,\r\n    energyConsumption: EnergyConsumption\r\n  ): Promise<EnergyEfficiencyMetrics> {\r\n    const designAirflow = systemConfiguration.designParameters.designAirflow;\r\n    const fanPowerKW = performanceMetrics.fanPower.value;\r\n    const systemEfficiency = performanceMetrics.systemEfficiency.value;\r\n\r\n    // Calculate Specific Fan Power (SFP)\r\n    const specificFanPower = (fanPowerKW * 1000) / designAirflow; // W/CFM\r\n\r\n    // Calculate Energy Utilization Index (EUI)\r\n    const buildingArea = 10000; // Assumed building area in sq ft\r\n    const energyUtilizationIndex = (energyConsumption.totalConsumption.value * this.KWH_TO_BTU) / buildingArea;\r\n\r\n    // Calculate power density\r\n    const powerDensity = (fanPowerKW * 1000) / buildingArea; // W/sq ft\r\n\r\n    // Calculate efficiency trend (simplified)\r\n    const efficiencyTrend = this.calculateEfficiencyTrend(systemEfficiency);\r\n\r\n    // Benchmark comparison\r\n    const benchmarkComparison = this.calculateEfficiencyBenchmark(\r\n      specificFanPower,\r\n      systemEfficiency\r\n    );\r\n\r\n    return {\r\n      overallEfficiency: systemEfficiency,\r\n      fanEfficiency: performanceMetrics.fanEfficiency.value,\r\n      systemEfficiency: systemEfficiency,\r\n      transportEfficiency: performanceMetrics.transportEfficiency.value,\r\n      specificFanPower,\r\n      energyUtilizationIndex,\r\n      powerDensity,\r\n      efficiencyTrend,\r\n      benchmarkComparison\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate efficiency trend\r\n   */\r\n  private static calculateEfficiencyTrend(currentEfficiency: number): EfficiencyTrend {\r\n    return {\r\n      currentEfficiency,\r\n      trendDirection: 'stable' as const,\r\n      trendRate: 0, // % per year\r\n      projectedEfficiency: currentEfficiency,\r\n      timeHorizon: 12, // months\r\n      confidenceLevel: 80\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate efficiency benchmark\r\n   */\r\n  private static calculateEfficiencyBenchmark(\r\n    specificFanPower: number,\r\n    systemEfficiency: number\r\n  ): EfficiencyBenchmark {\r\n    // ASHRAE 90.1 SFP limits\r\n    const ashraeSFPLimit = 1.25; // W/CFM for VAV systems\r\n    const industryAverageSFP = 1.1; // W/CFM\r\n    const bestPracticeSFP = 0.8; // W/CFM\r\n\r\n    return {\r\n      benchmarkType: 'industry_standard' as const,\r\n      benchmarkSource: 'ASHRAE 90.1',\r\n      currentValue: specificFanPower,\r\n      benchmarkValue: ashraeSFPLimit,\r\n      industryAverage: industryAverageSFP,\r\n      bestPractice: bestPracticeSFP,\r\n      percentile: this.calculateSFPPercentile(specificFanPower),\r\n      complianceStatus: specificFanPower <= ashraeSFPLimit ? 'compliant' : 'non_compliant',\r\n      improvementPotential: Math.max(0, specificFanPower - bestPracticeSFP)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate SFP percentile ranking\r\n   */\r\n  private static calculateSFPPercentile(sfp: number): number {\r\n    // Simplified percentile calculation based on typical SFP distribution\r\n    if (sfp <= 0.8) return 95;\r\n    if (sfp <= 1.0) return 80;\r\n    if (sfp <= 1.1) return 60;\r\n    if (sfp <= 1.25) return 40;\r\n    if (sfp <= 1.5) return 20;\r\n    return 5;\r\n  }\r\n\r\n  /**\r\n   * Calculate energy costs\r\n   */\r\n  private static async calculateEnergyCosts(\r\n    energyConsumption: EnergyConsumption,\r\n    energyRates?: EnergyRates\r\n  ): Promise<EnergyCosts> {\r\n    // Default energy rates if not provided\r\n    const rates = energyRates || {\r\n      energyRate: 0.12, // $/kWh\r\n      demandRate: 15.0, // $/kW\r\n      fixedRate: 25.0, // $/month\r\n      timeOfUseRates: {\r\n        peak: 0.18,\r\n        offPeak: 0.08,\r\n        shoulder: 0.12\r\n      }\r\n    };\r\n\r\n    // Calculate current costs\r\n    const energyCost = energyConsumption.totalConsumption.value * rates.energyRate;\r\n    const demandCost = energyConsumption.peakDemand.peakPower * rates.demandRate * 12; // Annual\r\n    const fixedCost = rates.fixedRate * 12; // Annual\r\n    const totalCost = energyCost + demandCost + fixedCost;\r\n\r\n    const currentCosts = {\r\n      totalCost,\r\n      energyCost,\r\n      demandCost,\r\n      fixedCost,\r\n      currency: 'USD',\r\n      timeFrame: TimeFrame.ANNUALLY\r\n    };\r\n\r\n    // Calculate projected costs (5-year projection)\r\n    const projectedCosts = this.calculateCostProjections(currentCosts, 0.03); // 3% annual escalation\r\n\r\n    // Identify cost saving opportunities\r\n    const costSavingOpportunities = this.identifyCostSavingOpportunities(\r\n      energyConsumption,\r\n      currentCosts\r\n    );\r\n\r\n    // Create utility rate structure\r\n    const utilityRateStructure = this.createUtilityRateStructure(rates);\r\n\r\n    return {\r\n      currentCosts,\r\n      projectedCosts,\r\n      costSavingOpportunities,\r\n      utilityRateStructure,\r\n      demandCharges: {\r\n        currentDemand: energyConsumption.peakDemand.peakPower,\r\n        demandRate: rates.demandRate,\r\n        annualDemandCost: demandCost,\r\n        peakShavingPotential: energyConsumption.peakDemand.peakShavingPotential,\r\n        potentialSavings: energyConsumption.peakDemand.peakShavingPotential * rates.demandRate * 12\r\n      },\r\n      timeOfUsePricing: {\r\n        enabled: true,\r\n        peakRate: rates.timeOfUseRates.peak,\r\n        offPeakRate: rates.timeOfUseRates.offPeak,\r\n        shoulderRate: rates.timeOfUseRates.shoulder,\r\n        peakHours: '12:00-18:00',\r\n        offPeakHours: '22:00-06:00',\r\n        potentialSavings: this.calculateTOUSavings(energyConsumption, rates.timeOfUseRates)\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate cost projections\r\n   */\r\n  private static calculateCostProjections(\r\n    currentCosts: any,\r\n    escalationRate: number\r\n  ): CostProjection[] {\r\n    const projections: CostProjection[] = [];\r\n    \r\n    for (let year = 1; year <= 5; year++) {\r\n      const escalationFactor = Math.pow(1 + escalationRate, year);\r\n      projections.push({\r\n        year,\r\n        totalCost: currentCosts.totalCost * escalationFactor,\r\n        energyCost: currentCosts.energyCost * escalationFactor,\r\n        demandCost: currentCosts.demandCost * escalationFactor,\r\n        fixedCost: currentCosts.fixedCost * escalationFactor,\r\n        escalationRate,\r\n        cumulativeCost: currentCosts.totalCost * ((Math.pow(1 + escalationRate, year) - 1) / escalationRate)\r\n      });\r\n    }\r\n\r\n    return projections;\r\n  }\r\n\r\n  /**\r\n   * Identify cost saving opportunities\r\n   */\r\n  private static identifyCostSavingOpportunities(\r\n    energyConsumption: EnergyConsumption,\r\n    currentCosts: any\r\n  ): CostSavingOpportunity[] {\r\n    const opportunities: CostSavingOpportunity[] = [];\r\n\r\n    // Peak demand reduction opportunity\r\n    if (energyConsumption.peakDemand.peakShavingPotential > 0) {\r\n      opportunities.push({\r\n        id: 'peak_demand_reduction',\r\n        name: 'Peak Demand Reduction',\r\n        description: 'Reduce peak demand through load scheduling and control optimization',\r\n        potentialSavings: energyConsumption.peakDemand.peakShavingPotential * 15 * 12, // $15/kW * 12 months\r\n        implementationCost: 5000,\r\n        paybackPeriod: 18, // months\r\n        savingsType: 'demand_reduction' as const,\r\n        confidence: 0.8\r\n      });\r\n    }\r\n\r\n    // Energy efficiency improvement\r\n    opportunities.push({\r\n      id: 'efficiency_improvement',\r\n      name: 'System Efficiency Improvement',\r\n      description: 'Improve overall system efficiency through equipment upgrades and optimization',\r\n      potentialSavings: currentCosts.energyCost * 0.15, // 15% energy savings\r\n      implementationCost: 15000,\r\n      paybackPeriod: 36, // months\r\n      savingsType: 'energy_reduction' as const,\r\n      confidence: 0.7\r\n    });\r\n\r\n    return opportunities;\r\n  }\r\n\r\n  /**\r\n   * Create utility rate structure\r\n   */\r\n  private static createUtilityRateStructure(rates: EnergyRates): UtilityRateStructure {\r\n    return {\r\n      rateSchedule: 'Commercial General Service',\r\n      energyCharges: {\r\n        flatRate: rates.energyRate,\r\n        tieredRates: [\r\n          { tier: 1, threshold: 1000, rate: rates.energyRate * 0.9 },\r\n          { tier: 2, threshold: 5000, rate: rates.energyRate },\r\n          { tier: 3, threshold: Infinity, rate: rates.energyRate * 1.1 }\r\n        ]\r\n      },\r\n      demandCharges: {\r\n        rate: rates.demandRate,\r\n        ratchetClause: true,\r\n        seasonalRates: {\r\n          summer: rates.demandRate * 1.2,\r\n          winter: rates.demandRate * 0.8\r\n        }\r\n      },\r\n      fixedCharges: {\r\n        customerCharge: rates.fixedRate,\r\n        facilityCharge: 0,\r\n        minimumBill: rates.fixedRate\r\n      },\r\n      timeOfUseRates: rates.timeOfUseRates\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate time-of-use savings potential\r\n   */\r\n  private static calculateTOUSavings(\r\n    energyConsumption: EnergyConsumption,\r\n    touRates: any\r\n  ): number {\r\n    // Simplified TOU savings calculation\r\n    const totalEnergy = energyConsumption.totalConsumption.value;\r\n    const peakEnergyPercent = 0.3; // 30% during peak hours\r\n    const offPeakEnergyPercent = 0.4; // 40% during off-peak hours\r\n    const shoulderEnergyPercent = 0.3; // 30% during shoulder hours\r\n\r\n    const currentCost = totalEnergy * 0.12; // Flat rate\r\n    const touCost = (totalEnergy * peakEnergyPercent * touRates.peak) +\r\n                   (totalEnergy * offPeakEnergyPercent * touRates.offPeak) +\r\n                   (totalEnergy * shoulderEnergyPercent * touRates.shoulder);\r\n\r\n    return Math.max(0, currentCost - touCost);\r\n  }\r\n\r\n  /**\r\n   * Calculate carbon footprint\r\n   */\r\n  private static async calculateCarbonFootprint(\r\n    energyConsumption: EnergyConsumption,\r\n    systemConfiguration: SystemConfiguration\r\n  ): Promise<CarbonFootprint> {\r\n    const totalEnergyKWh = energyConsumption.totalConsumption.value;\r\n\r\n    // Calculate operational emissions\r\n    const operationalEmissions = this.createEmissionMeasurement(\r\n      totalEnergyKWh * this.EMISSION_FACTORS.GRID_AVERAGE,\r\n      EmissionUnits.KG_CO2E,\r\n      TimeFrame.ANNUALLY,\r\n      EmissionScope.SCOPE_2\r\n    );\r\n\r\n    // Calculate embodied emissions (simplified)\r\n    const embodiedEmissions = this.createEmissionMeasurement(\r\n      500, // Simplified embodied carbon for HVAC system\r\n      EmissionUnits.KG_CO2E,\r\n      TimeFrame.ANNUALLY,\r\n      EmissionScope.SCOPE_3\r\n    );\r\n\r\n    // Total emissions\r\n    const totalEmissions = this.createEmissionMeasurement(\r\n      operationalEmissions.value + embodiedEmissions.value,\r\n      EmissionUnits.KG_CO2E,\r\n      TimeFrame.ANNUALLY,\r\n      EmissionScope.SCOPE_2\r\n    );\r\n\r\n    // Emissions by source\r\n    const emissionsBySource = [\r\n      {\r\n        source: 'Electricity Grid',\r\n        emissions: operationalEmissions.value,\r\n        percentage: (operationalEmissions.value / totalEmissions.value) * 100,\r\n        emissionFactor: this.EMISSION_FACTORS.GRID_AVERAGE\r\n      },\r\n      {\r\n        source: 'Embodied Carbon',\r\n        emissions: embodiedEmissions.value,\r\n        percentage: (embodiedEmissions.value / totalEmissions.value) * 100,\r\n        emissionFactor: 0\r\n      }\r\n    ];\r\n\r\n    // Emissions trend (simplified)\r\n    const emissionsTrend = {\r\n      currentEmissions: totalEmissions.value,\r\n      trendDirection: 'stable' as const,\r\n      projectedEmissions: totalEmissions.value,\r\n      reductionPotential: totalEmissions.value * 0.3, // 30% reduction potential\r\n      timeHorizon: 10 // years\r\n    };\r\n\r\n    // Offset opportunities\r\n    const offsetOpportunities = [\r\n      {\r\n        type: 'Renewable Energy',\r\n        potential: operationalEmissions.value * 0.8, // 80% offset potential\r\n        cost: operationalEmissions.value * 0.02, // $0.02/kg CO2e\r\n        implementation: 'On-site solar or renewable energy credits'\r\n      },\r\n      {\r\n        type: 'Energy Efficiency',\r\n        potential: operationalEmissions.value * 0.2, // 20% reduction potential\r\n        cost: 0, // Cost savings\r\n        implementation: 'System optimization and efficiency improvements'\r\n      }\r\n    ];\r\n\r\n    // Benchmark comparison\r\n    const benchmarkComparison = {\r\n      benchmarkType: 'Industry Average',\r\n      currentIntensity: totalEmissions.value / systemConfiguration.designParameters.designAirflow, // kg CO2e/CFM\r\n      benchmarkIntensity: 0.15, // Industry average\r\n      percentile: 60,\r\n      improvementPotential: totalEmissions.value * 0.25\r\n    };\r\n\r\n    return {\r\n      totalEmissions,\r\n      operationalEmissions,\r\n      embodiedEmissions,\r\n      emissionsBySource,\r\n      emissionsTrend,\r\n      offsetOpportunities,\r\n      benchmarkComparison\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create standardized emission measurement\r\n   */\r\n  private static createEmissionMeasurement(\r\n    value: number,\r\n    units: EmissionUnits,\r\n    timeFrame: TimeFrame,\r\n    scope: EmissionScope,\r\n    accuracy: number = 0.8\r\n  ): EmissionMeasurement {\r\n    return {\r\n      value,\r\n      units,\r\n      timeFrame,\r\n      scope,\r\n      accuracy\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Perform energy benchmarking\r\n   */\r\n  private static async performEnergyBenchmarking(\r\n    systemConfiguration: SystemConfiguration,\r\n    efficiencyMetrics: EnergyEfficiencyMetrics\r\n  ): Promise<EnergyBenchmark> {\r\n    const sfp = efficiencyMetrics.specificFanPower;\r\n    const systemEfficiency = efficiencyMetrics.systemEfficiency;\r\n\r\n    return {\r\n      benchmarkType: 'Industry Standard',\r\n      benchmarkSource: 'ASHRAE 90.1 and Industry Data',\r\n      energyIntensity: {\r\n        current: sfp,\r\n        benchmark: 1.25, // ASHRAE 90.1 limit\r\n        industryAverage: 1.1,\r\n        bestPractice: 0.8,\r\n        percentile: this.calculateSFPPercentile(sfp)\r\n      },\r\n      efficiencyRating: {\r\n        current: systemEfficiency,\r\n        benchmark: 80,\r\n        industryAverage: 82,\r\n        bestPractice: 90,\r\n        grade: this.calculateEfficiencyGrade(systemEfficiency)\r\n      },\r\n      complianceStatus: {\r\n        ashrae901: sfp <= 1.25 ? 'compliant' : 'non_compliant',\r\n        energyStar: systemEfficiency >= 85 ? 'qualified' : 'not_qualified',\r\n        leed: this.calculateLEEDPoints(sfp, systemEfficiency)\r\n      },\r\n      improvementPotential: {\r\n        energySavings: Math.max(0, (sfp - 0.8) / sfp * 100), // % savings potential\r\n        costSavings: 0, // Would be calculated based on energy costs\r\n        emissionReduction: 0 // Would be calculated based on carbon intensity\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate efficiency grade\r\n   */\r\n  private static calculateEfficiencyGrade(efficiency: number): string {\r\n    if (efficiency >= 90) return 'A+';\r\n    if (efficiency >= 85) return 'A';\r\n    if (efficiency >= 80) return 'B';\r\n    if (efficiency >= 75) return 'C';\r\n    if (efficiency >= 70) return 'D';\r\n    return 'F';\r\n  }\r\n\r\n  /**\r\n   * Calculate LEED points\r\n   */\r\n  private static calculateLEEDPoints(sfp: number, efficiency: number): number {\r\n    let points = 0;\r\n\r\n    // LEED points for fan power efficiency\r\n    if (sfp <= 0.8) points += 2;\r\n    else if (sfp <= 1.0) points += 1;\r\n\r\n    // LEED points for system efficiency\r\n    if (efficiency >= 85) points += 1;\r\n\r\n    return Math.min(points, 3); // Maximum 3 points\r\n  }\r\n\r\n  /**\r\n   * Identify optimization opportunities\r\n   */\r\n  private static async identifyOptimizationOpportunities(\r\n    systemConfiguration: SystemConfiguration,\r\n    performanceMetrics: PerformanceMetrics,\r\n    efficiencyMetrics: EnergyEfficiencyMetrics,\r\n    energyCosts: EnergyCosts\r\n  ): Promise<EnergyOptimizationOpportunity[]> {\r\n    const opportunities: EnergyOptimizationOpportunity[] = [];\r\n\r\n    // Fan speed optimization\r\n    if (efficiencyMetrics.specificFanPower > 1.0) {\r\n      opportunities.push({\r\n        id: 'fan_speed_optimization',\r\n        name: 'Fan Speed Optimization',\r\n        description: 'Optimize fan speed control to reduce energy consumption while maintaining comfort',\r\n        category: 'Control Optimization',\r\n        energySavingsPotential: 15, // %\r\n        costSavingsPotential: energyCosts.currentCosts.energyCost * 0.15,\r\n        implementationCost: 3000,\r\n        paybackPeriod: 12, // months\r\n        complexity: 'Low',\r\n        priority: 'High',\r\n        requiredActions: [\r\n          'Install VFD if not present',\r\n          'Implement demand-based control',\r\n          'Optimize control sequences'\r\n        ],\r\n        expectedResults: {\r\n          energyReduction: 15,\r\n          demandReduction: 10,\r\n          emissionReduction: 12,\r\n          costSavings: energyCosts.currentCosts.energyCost * 0.15\r\n        }\r\n      });\r\n    }\r\n\r\n    // Duct system optimization\r\n    if (performanceMetrics.totalSystemPressure.value > 3.0) {\r\n      opportunities.push({\r\n        id: 'duct_optimization',\r\n        name: 'Duct System Optimization',\r\n        description: 'Reduce system pressure losses through duct sizing and layout optimization',\r\n        category: 'System Design',\r\n        energySavingsPotential: 20, // %\r\n        costSavingsPotential: energyCosts.currentCosts.energyCost * 0.20,\r\n        implementationCost: 15000,\r\n        paybackPeriod: 36, // months\r\n        complexity: 'High',\r\n        priority: 'Medium',\r\n        requiredActions: [\r\n          'Analyze duct sizing',\r\n          'Identify pressure loss sources',\r\n          'Redesign critical sections',\r\n          'Seal ductwork leaks'\r\n        ],\r\n        expectedResults: {\r\n          energyReduction: 20,\r\n          demandReduction: 18,\r\n          emissionReduction: 20,\r\n          costSavings: energyCosts.currentCosts.energyCost * 0.20\r\n        }\r\n      });\r\n    }\r\n\r\n    // Filter optimization\r\n    opportunities.push({\r\n      id: 'filter_optimization',\r\n      name: 'Filter System Optimization',\r\n      description: 'Optimize filter selection and maintenance to reduce pressure drop',\r\n      category: 'Maintenance',\r\n      energySavingsPotential: 8, // %\r\n      costSavingsPotential: energyCosts.currentCosts.energyCost * 0.08,\r\n      implementationCost: 2000,\r\n      paybackPeriod: 18, // months\r\n      complexity: 'Low',\r\n      priority: 'Medium',\r\n      requiredActions: [\r\n        'Evaluate filter efficiency vs pressure drop',\r\n        'Implement pressure monitoring',\r\n        'Optimize replacement schedule'\r\n      ],\r\n      expectedResults: {\r\n        energyReduction: 8,\r\n        demandReduction: 5,\r\n        emissionReduction: 8,\r\n        costSavings: energyCosts.currentCosts.energyCost * 0.08\r\n      }\r\n    });\r\n\r\n    return opportunities;\r\n  }\r\n\r\n  /**\r\n   * Perform seasonal energy analysis\r\n   */\r\n  private static async performSeasonalEnergyAnalysis(\r\n    systemConfiguration: SystemConfiguration,\r\n    energyConsumption: EnergyConsumption\r\n  ): Promise<SeasonalEnergyAnalysis> {\r\n    const baseConsumption = energyConsumption.totalConsumption.value;\r\n\r\n    return {\r\n      seasonalBreakdown: [\r\n        {\r\n          season: 'Spring',\r\n          energyConsumption: baseConsumption * 0.22, // 22% of annual\r\n          averageLoad: energyConsumption.loadProfile.averageLoad * 0.8,\r\n          peakLoad: energyConsumption.loadProfile.peakLoad * 0.7,\r\n          operatingHours: 2000,\r\n          efficiency: 85,\r\n          costs: baseConsumption * 0.22 * 0.12 // $0.12/kWh\r\n        },\r\n        {\r\n          season: 'Summer',\r\n          energyConsumption: baseConsumption * 0.35, // 35% of annual\r\n          averageLoad: energyConsumption.loadProfile.averageLoad * 1.2,\r\n          peakLoad: energyConsumption.loadProfile.peakLoad * 1.0,\r\n          operatingHours: 2500,\r\n          efficiency: 80, // Lower efficiency due to higher loads\r\n          costs: baseConsumption * 0.35 * 0.15 // Higher summer rates\r\n        },\r\n        {\r\n          season: 'Fall',\r\n          energyConsumption: baseConsumption * 0.25, // 25% of annual\r\n          averageLoad: energyConsumption.loadProfile.averageLoad * 0.9,\r\n          peakLoad: energyConsumption.loadProfile.peakLoad * 0.8,\r\n          operatingHours: 2200,\r\n          efficiency: 83,\r\n          costs: baseConsumption * 0.25 * 0.12\r\n        },\r\n        {\r\n          season: 'Winter',\r\n          energyConsumption: baseConsumption * 0.18, // 18% of annual\r\n          averageLoad: energyConsumption.loadProfile.averageLoad * 0.7,\r\n          peakLoad: energyConsumption.loadProfile.peakLoad * 0.6,\r\n          operatingHours: 1800,\r\n          efficiency: 87, // Higher efficiency at lower loads\r\n          costs: baseConsumption * 0.18 * 0.11 // Lower winter rates\r\n        }\r\n      ],\r\n      peakSeasons: ['Summer', 'Fall'],\r\n      optimizationOpportunities: [\r\n        {\r\n          season: 'Summer',\r\n          opportunity: 'Peak load management',\r\n          potential: 'Reduce peak demand by 15% through load scheduling',\r\n          savings: 2500\r\n        },\r\n        {\r\n          season: 'Winter',\r\n          opportunity: 'Extended economizer operation',\r\n          potential: 'Increase free cooling hours by 20%',\r\n          savings: 800\r\n        }\r\n      ],\r\n      weatherSensitivity: {\r\n        temperatureCoefficient: 0.02, // 2% change per degree F\r\n        humidityCoefficient: 0.005, // 0.5% change per % RH\r\n        baselineTemperature: 65, // \xB0F\r\n        heatingThreshold: 55, // \xB0F\r\n        coolingThreshold: 75 // \xB0F\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Generate unique analysis ID\r\n   */\r\n  private static generateAnalysisId(systemId: string): string {\r\n    const timestamp = Date.now();\r\n    const random = Math.random().toString(36).substring(2, 8);\r\n    return `energy_analysis_${systemId}_${timestamp}_${random}`;\r\n  }\r\n}\r\n\r\n// Supporting interfaces\r\ninterface OperatingSchedule {\r\n  hoursPerDay: number;\r\n  daysPerWeek: number;\r\n  weeksPerYear: number;\r\n  loadProfile: 'constant' | 'variable' | 'scheduled';\r\n}\r\n\r\ninterface EnergyRates {\r\n  energyRate: number; // $/kWh\r\n  demandRate: number; // $/kW\r\n  fixedRate: number; // $/month\r\n  timeOfUseRates: {\r\n    peak: number;\r\n    offPeak: number;\r\n    shoulder: number;\r\n  };\r\n}\r\n\r\ninterface TimeOfDayConsumption {\r\n  timeOfDay: string;\r\n  powerDemand: number; // kW\r\n  energyConsumption: number; // kWh\r\n  loadFactor: number;\r\n}\r\n\r\ninterface LoadProfile {\r\n  baseLoad: number; // kW\r\n  peakLoad: number; // kW\r\n  averageLoad: number; // kW\r\n  loadFactor: number;\r\n  diversityFactor: number;\r\n  demandProfile: Array<{\r\n    period: string;\r\n    demand: number;\r\n  }>;\r\n}\r\n\r\ninterface PeakDemand {\r\n  peakPower: number; // kW\r\n  peakTime: string;\r\n  peakDuration: number; // hours\r\n  peakFrequency: 'daily' | 'weekly' | 'seasonal';\r\n  coincidentFactor: number;\r\n  demandCharges: number; // $/month\r\n  peakShavingPotential: number; // kW\r\n}\r\n\r\ninterface EfficiencyTrend {\r\n  currentEfficiency: number;\r\n  trendDirection: 'improving' | 'stable' | 'degrading';\r\n  trendRate: number; // % per year\r\n  projectedEfficiency: number;\r\n  timeHorizon: number; // months\r\n  confidenceLevel: number;\r\n}\r\n\r\ninterface EfficiencyBenchmark {\r\n  benchmarkType: string;\r\n  benchmarkSource: string;\r\n  currentValue: number;\r\n  benchmarkValue: number;\r\n  industryAverage: number;\r\n  bestPractice: number;\r\n  percentile: number;\r\n  complianceStatus: 'compliant' | 'non_compliant';\r\n  improvementPotential: number;\r\n}\r\n\r\ninterface CostProjection {\r\n  year: number;\r\n  totalCost: number;\r\n  energyCost: number;\r\n  demandCost: number;\r\n  fixedCost: number;\r\n  escalationRate: number;\r\n  cumulativeCost: number;\r\n}\r\n\r\ninterface CostSavingOpportunity {\r\n  id: string;\r\n  name: string;\r\n  description: string;\r\n  potentialSavings: number; // $/year\r\n  implementationCost: number;\r\n  paybackPeriod: number; // months\r\n  savingsType: 'energy_reduction' | 'demand_reduction' | 'rate_optimization';\r\n  confidence: number;\r\n}\r\n\r\ninterface UtilityRateStructure {\r\n  rateSchedule: string;\r\n  energyCharges: any;\r\n  demandCharges: any;\r\n  fixedCharges: any;\r\n  timeOfUseRates: any;\r\n}\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "1116583a26df174fea098d317d3ca0f74db9f629"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_9l8cmvs2f = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_9l8cmvs2f();
cov_9l8cmvs2f().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_9l8cmvs2f().s[1]++;
exports.EnergyEfficiencyAnalysisEngine = void 0;
const SystemAnalysisTypes_1 =
/* istanbul ignore next */
(cov_9l8cmvs2f().s[2]++, require("./types/SystemAnalysisTypes"));
/**
 * Energy Efficiency Analysis Engine
 *
 * Provides comprehensive energy efficiency analysis capabilities including:
 * - Energy consumption breakdown and analysis
 * - Fan power optimization calculations
 * - Lifecycle energy cost projections
 * - Carbon footprint assessment
 * - Energy benchmarking and comparison
 * - Seasonal energy analysis
 */
class EnergyEfficiencyAnalysisEngine {
  /**
   * Perform comprehensive energy efficiency analysis
   */
  static async analyzeEnergyEfficiency(systemConfiguration, performanceMetrics, operatingSchedule, energyRates) {
    /* istanbul ignore next */
    cov_9l8cmvs2f().f[0]++;
    cov_9l8cmvs2f().s[3]++;
    try {
      const analysisId =
      /* istanbul ignore next */
      (cov_9l8cmvs2f().s[4]++, this.generateAnalysisId(systemConfiguration.id));
      const timestamp =
      /* istanbul ignore next */
      (cov_9l8cmvs2f().s[5]++, new Date());
      // Calculate energy consumption breakdown
      const energyConsumption =
      /* istanbul ignore next */
      (cov_9l8cmvs2f().s[6]++, await this.calculateEnergyConsumption(systemConfiguration, performanceMetrics, operatingSchedule));
      // Calculate efficiency metrics
      const efficiencyMetrics =
      /* istanbul ignore next */
      (cov_9l8cmvs2f().s[7]++, await this.calculateEnergyEfficiencyMetrics(systemConfiguration, performanceMetrics, energyConsumption));
      // Calculate energy costs
      const energyCosts =
      /* istanbul ignore next */
      (cov_9l8cmvs2f().s[8]++, await this.calculateEnergyCosts(energyConsumption, energyRates));
      // Calculate carbon footprint
      const carbonFootprint =
      /* istanbul ignore next */
      (cov_9l8cmvs2f().s[9]++, await this.calculateCarbonFootprint(energyConsumption, systemConfiguration));
      // Perform benchmark comparison
      const benchmarkComparison =
      /* istanbul ignore next */
      (cov_9l8cmvs2f().s[10]++, await this.performEnergyBenchmarking(systemConfiguration, efficiencyMetrics));
      // Identify optimization opportunities
      const optimizationOpportunities =
      /* istanbul ignore next */
      (cov_9l8cmvs2f().s[11]++, await this.identifyOptimizationOpportunities(systemConfiguration, performanceMetrics, efficiencyMetrics, energyCosts));
      // Perform seasonal analysis
      const seasonalAnalysis =
      /* istanbul ignore next */
      (cov_9l8cmvs2f().s[12]++, await this.performSeasonalEnergyAnalysis(systemConfiguration, energyConsumption));
      const analysis =
      /* istanbul ignore next */
      (cov_9l8cmvs2f().s[13]++, {
        id: analysisId,
        systemId: systemConfiguration.id,
        analysisTimestamp: timestamp,
        energyConsumption,
        efficiencyMetrics,
        energyCosts,
        carbonFootprint,
        benchmarkComparison,
        optimizationOpportunities,
        seasonalAnalysis
      });
      // Cache the analysis
      /* istanbul ignore next */
      cov_9l8cmvs2f().s[14]++;
      this.ENERGY_CACHE.set(analysisId, analysis);
      /* istanbul ignore next */
      cov_9l8cmvs2f().s[15]++;
      return analysis;
    } catch (error) {
      /* istanbul ignore next */
      cov_9l8cmvs2f().s[16]++;
      throw new Error(`Energy efficiency analysis failed: ${error instanceof Error ?
      /* istanbul ignore next */
      (cov_9l8cmvs2f().b[0][0]++, error.message) :
      /* istanbul ignore next */
      (cov_9l8cmvs2f().b[0][1]++, 'Unknown error')}`);
    }
  }
  /**
   * Calculate comprehensive energy consumption breakdown
   */
  static async calculateEnergyConsumption(systemConfiguration, performanceMetrics, operatingSchedule) {
    /* istanbul ignore next */
    cov_9l8cmvs2f().f[1]++;
    const fanPowerKW =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[17]++, performanceMetrics.fanPower.value);
    const designAirflow =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[18]++, systemConfiguration.designParameters.designAirflow);
    // Default operating schedule if not provided
    const schedule =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[19]++,
    /* istanbul ignore next */
    (cov_9l8cmvs2f().b[1][0]++, operatingSchedule) ||
    /* istanbul ignore next */
    (cov_9l8cmvs2f().b[1][1]++, {
      hoursPerDay: 12,
      daysPerWeek: 5,
      weeksPerYear: 50,
      loadProfile: 'constant'
    }));
    const annualOperatingHours =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[20]++, schedule.hoursPerDay * schedule.daysPerWeek * schedule.weeksPerYear);
    // Calculate fan energy consumption
    const fanConsumption =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[21]++, this.createEnergyMeasurement(fanPowerKW * annualOperatingHours, SystemAnalysisTypes_1.EnergyUnits.KWH, SystemAnalysisTypes_1.TimeFrame.ANNUALLY, SystemAnalysisTypes_1.MeasurementSource.CALCULATED));
    // Calculate auxiliary equipment consumption (10% of fan power typically)
    const auxiliaryConsumption =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[22]++, this.createEnergyMeasurement(fanPowerKW * 0.1 * annualOperatingHours, SystemAnalysisTypes_1.EnergyUnits.KWH, SystemAnalysisTypes_1.TimeFrame.ANNUALLY, SystemAnalysisTypes_1.MeasurementSource.ESTIMATED));
    // Total consumption
    const totalConsumption =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[23]++, this.createEnergyMeasurement(fanConsumption.value + auxiliaryConsumption.value, SystemAnalysisTypes_1.EnergyUnits.KWH, SystemAnalysisTypes_1.TimeFrame.ANNUALLY, SystemAnalysisTypes_1.MeasurementSource.CALCULATED));
    // Calculate time-of-day consumption profile
    const timeOfDayConsumption =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[24]++, this.calculateTimeOfDayConsumption(fanPowerKW, schedule));
    // Calculate load profile
    const loadProfile =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[25]++, this.calculateLoadProfile(fanPowerKW, schedule));
    // Calculate peak demand
    const peakDemand =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[26]++, this.calculatePeakDemand(fanPowerKW, schedule));
    /* istanbul ignore next */
    cov_9l8cmvs2f().s[27]++;
    return {
      totalConsumption,
      fanConsumption,
      auxiliaryConsumption,
      consumptionByTimeOfDay: timeOfDayConsumption,
      loadProfile,
      peakDemand
    };
  }
  /**
   * Create standardized energy measurement
   */
  static createEnergyMeasurement(value, units, timeFrame, source, accuracy =
  /* istanbul ignore next */
  (cov_9l8cmvs2f().b[2][0]++, 0.9)) {
    /* istanbul ignore next */
    cov_9l8cmvs2f().f[2]++;
    cov_9l8cmvs2f().s[28]++;
    return {
      value,
      units,
      timeFrame,
      accuracy,
      source
    };
  }
  /**
   * Calculate time-of-day energy consumption
   */
  static calculateTimeOfDayConsumption(fanPowerKW, schedule) {
    /* istanbul ignore next */
    cov_9l8cmvs2f().f[3]++;
    const consumption =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[29]++, []);
    // Simplified time-of-day profile
    const hourlyProfile =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[30]++, [{
      hour: 6,
      loadFactor: 0.8
    }, {
      hour: 8,
      loadFactor: 1.0
    }, {
      hour: 12,
      loadFactor: 0.9
    }, {
      hour: 17,
      loadFactor: 1.0
    }, {
      hour: 20,
      loadFactor: 0.6
    }, {
      hour: 22,
      loadFactor: 0.3
    }]);
    /* istanbul ignore next */
    cov_9l8cmvs2f().s[31]++;
    hourlyProfile.forEach(profile => {
      /* istanbul ignore next */
      cov_9l8cmvs2f().f[4]++;
      cov_9l8cmvs2f().s[32]++;
      consumption.push({
        timeOfDay: `${profile.hour}:00`,
        powerDemand: fanPowerKW * profile.loadFactor,
        energyConsumption: fanPowerKW * profile.loadFactor * 1,
        // 1 hour
        loadFactor: profile.loadFactor
      });
    });
    /* istanbul ignore next */
    cov_9l8cmvs2f().s[33]++;
    return consumption;
  }
  /**
   * Calculate system load profile
   */
  static calculateLoadProfile(fanPowerKW, schedule) {
    /* istanbul ignore next */
    cov_9l8cmvs2f().f[5]++;
    cov_9l8cmvs2f().s[34]++;
    return {
      baseLoad: fanPowerKW * 0.3,
      // 30% base load
      peakLoad: fanPowerKW * 1.0,
      // 100% peak load
      averageLoad: fanPowerKW * 0.75,
      // 75% average load
      loadFactor: 0.75,
      // Average/Peak
      diversityFactor: 0.85,
      // Accounting for non-coincident peaks
      demandProfile: [{
        period: 'morning',
        demand: fanPowerKW * 0.8
      }, {
        period: 'midday',
        demand: fanPowerKW * 1.0
      }, {
        period: 'afternoon',
        demand: fanPowerKW * 0.9
      }, {
        period: 'evening',
        demand: fanPowerKW * 0.6
      }]
    };
  }
  /**
   * Calculate peak demand characteristics
   */
  static calculatePeakDemand(fanPowerKW, schedule) {
    /* istanbul ignore next */
    cov_9l8cmvs2f().f[6]++;
    cov_9l8cmvs2f().s[35]++;
    return {
      peakPower: fanPowerKW,
      peakTime: '14:00',
      // 2 PM typical peak
      peakDuration: 2,
      // 2 hours
      peakFrequency: 'daily',
      coincidentFactor: 0.9,
      // 90% coincident with utility peak
      demandCharges: fanPowerKW * 15,
      // $15/kW typical demand charge
      peakShavingPotential: fanPowerKW * 0.2 // 20% potential reduction
    };
  }
  /**
   * Calculate energy efficiency metrics
   */
  static async calculateEnergyEfficiencyMetrics(systemConfiguration, performanceMetrics, energyConsumption) {
    /* istanbul ignore next */
    cov_9l8cmvs2f().f[7]++;
    const designAirflow =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[36]++, systemConfiguration.designParameters.designAirflow);
    const fanPowerKW =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[37]++, performanceMetrics.fanPower.value);
    const systemEfficiency =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[38]++, performanceMetrics.systemEfficiency.value);
    // Calculate Specific Fan Power (SFP)
    const specificFanPower =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[39]++, fanPowerKW * 1000 / designAirflow); // W/CFM
    // Calculate Energy Utilization Index (EUI)
    const buildingArea =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[40]++, 10000); // Assumed building area in sq ft
    const energyUtilizationIndex =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[41]++, energyConsumption.totalConsumption.value * this.KWH_TO_BTU / buildingArea);
    // Calculate power density
    const powerDensity =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[42]++, fanPowerKW * 1000 / buildingArea); // W/sq ft
    // Calculate efficiency trend (simplified)
    const efficiencyTrend =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[43]++, this.calculateEfficiencyTrend(systemEfficiency));
    // Benchmark comparison
    const benchmarkComparison =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[44]++, this.calculateEfficiencyBenchmark(specificFanPower, systemEfficiency));
    /* istanbul ignore next */
    cov_9l8cmvs2f().s[45]++;
    return {
      overallEfficiency: systemEfficiency,
      fanEfficiency: performanceMetrics.fanEfficiency.value,
      systemEfficiency: systemEfficiency,
      transportEfficiency: performanceMetrics.transportEfficiency.value,
      specificFanPower,
      energyUtilizationIndex,
      powerDensity,
      efficiencyTrend,
      benchmarkComparison
    };
  }
  /**
   * Calculate efficiency trend
   */
  static calculateEfficiencyTrend(currentEfficiency) {
    /* istanbul ignore next */
    cov_9l8cmvs2f().f[8]++;
    cov_9l8cmvs2f().s[46]++;
    return {
      currentEfficiency,
      trendDirection: 'stable',
      trendRate: 0,
      // % per year
      projectedEfficiency: currentEfficiency,
      timeHorizon: 12,
      // months
      confidenceLevel: 80
    };
  }
  /**
   * Calculate efficiency benchmark
   */
  static calculateEfficiencyBenchmark(specificFanPower, systemEfficiency) {
    /* istanbul ignore next */
    cov_9l8cmvs2f().f[9]++;
    // ASHRAE 90.1 SFP limits
    const ashraeSFPLimit =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[47]++, 1.25); // W/CFM for VAV systems
    const industryAverageSFP =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[48]++, 1.1); // W/CFM
    const bestPracticeSFP =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[49]++, 0.8); // W/CFM
    /* istanbul ignore next */
    cov_9l8cmvs2f().s[50]++;
    return {
      benchmarkType: 'industry_standard',
      benchmarkSource: 'ASHRAE 90.1',
      currentValue: specificFanPower,
      benchmarkValue: ashraeSFPLimit,
      industryAverage: industryAverageSFP,
      bestPractice: bestPracticeSFP,
      percentile: this.calculateSFPPercentile(specificFanPower),
      complianceStatus: specificFanPower <= ashraeSFPLimit ?
      /* istanbul ignore next */
      (cov_9l8cmvs2f().b[3][0]++, 'compliant') :
      /* istanbul ignore next */
      (cov_9l8cmvs2f().b[3][1]++, 'non_compliant'),
      improvementPotential: Math.max(0, specificFanPower - bestPracticeSFP)
    };
  }
  /**
   * Calculate SFP percentile ranking
   */
  static calculateSFPPercentile(sfp) {
    /* istanbul ignore next */
    cov_9l8cmvs2f().f[10]++;
    cov_9l8cmvs2f().s[51]++;
    // Simplified percentile calculation based on typical SFP distribution
    if (sfp <= 0.8) {
      /* istanbul ignore next */
      cov_9l8cmvs2f().b[4][0]++;
      cov_9l8cmvs2f().s[52]++;
      return 95;
    } else
    /* istanbul ignore next */
    {
      cov_9l8cmvs2f().b[4][1]++;
    }
    cov_9l8cmvs2f().s[53]++;
    if (sfp <= 1.0) {
      /* istanbul ignore next */
      cov_9l8cmvs2f().b[5][0]++;
      cov_9l8cmvs2f().s[54]++;
      return 80;
    } else
    /* istanbul ignore next */
    {
      cov_9l8cmvs2f().b[5][1]++;
    }
    cov_9l8cmvs2f().s[55]++;
    if (sfp <= 1.1) {
      /* istanbul ignore next */
      cov_9l8cmvs2f().b[6][0]++;
      cov_9l8cmvs2f().s[56]++;
      return 60;
    } else
    /* istanbul ignore next */
    {
      cov_9l8cmvs2f().b[6][1]++;
    }
    cov_9l8cmvs2f().s[57]++;
    if (sfp <= 1.25) {
      /* istanbul ignore next */
      cov_9l8cmvs2f().b[7][0]++;
      cov_9l8cmvs2f().s[58]++;
      return 40;
    } else
    /* istanbul ignore next */
    {
      cov_9l8cmvs2f().b[7][1]++;
    }
    cov_9l8cmvs2f().s[59]++;
    if (sfp <= 1.5) {
      /* istanbul ignore next */
      cov_9l8cmvs2f().b[8][0]++;
      cov_9l8cmvs2f().s[60]++;
      return 20;
    } else
    /* istanbul ignore next */
    {
      cov_9l8cmvs2f().b[8][1]++;
    }
    cov_9l8cmvs2f().s[61]++;
    return 5;
  }
  /**
   * Calculate energy costs
   */
  static async calculateEnergyCosts(energyConsumption, energyRates) {
    /* istanbul ignore next */
    cov_9l8cmvs2f().f[11]++;
    // Default energy rates if not provided
    const rates =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[62]++,
    /* istanbul ignore next */
    (cov_9l8cmvs2f().b[9][0]++, energyRates) ||
    /* istanbul ignore next */
    (cov_9l8cmvs2f().b[9][1]++, {
      energyRate: 0.12,
      // $/kWh
      demandRate: 15.0,
      // $/kW
      fixedRate: 25.0,
      // $/month
      timeOfUseRates: {
        peak: 0.18,
        offPeak: 0.08,
        shoulder: 0.12
      }
    }));
    // Calculate current costs
    const energyCost =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[63]++, energyConsumption.totalConsumption.value * rates.energyRate);
    const demandCost =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[64]++, energyConsumption.peakDemand.peakPower * rates.demandRate * 12); // Annual
    const fixedCost =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[65]++, rates.fixedRate * 12); // Annual
    const totalCost =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[66]++, energyCost + demandCost + fixedCost);
    const currentCosts =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[67]++, {
      totalCost,
      energyCost,
      demandCost,
      fixedCost,
      currency: 'USD',
      timeFrame: SystemAnalysisTypes_1.TimeFrame.ANNUALLY
    });
    // Calculate projected costs (5-year projection)
    const projectedCosts =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[68]++, this.calculateCostProjections(currentCosts, 0.03)); // 3% annual escalation
    // Identify cost saving opportunities
    const costSavingOpportunities =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[69]++, this.identifyCostSavingOpportunities(energyConsumption, currentCosts));
    // Create utility rate structure
    const utilityRateStructure =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[70]++, this.createUtilityRateStructure(rates));
    /* istanbul ignore next */
    cov_9l8cmvs2f().s[71]++;
    return {
      currentCosts,
      projectedCosts,
      costSavingOpportunities,
      utilityRateStructure,
      demandCharges: {
        currentDemand: energyConsumption.peakDemand.peakPower,
        demandRate: rates.demandRate,
        annualDemandCost: demandCost,
        peakShavingPotential: energyConsumption.peakDemand.peakShavingPotential,
        potentialSavings: energyConsumption.peakDemand.peakShavingPotential * rates.demandRate * 12
      },
      timeOfUsePricing: {
        enabled: true,
        peakRate: rates.timeOfUseRates.peak,
        offPeakRate: rates.timeOfUseRates.offPeak,
        shoulderRate: rates.timeOfUseRates.shoulder,
        peakHours: '12:00-18:00',
        offPeakHours: '22:00-06:00',
        potentialSavings: this.calculateTOUSavings(energyConsumption, rates.timeOfUseRates)
      }
    };
  }
  /**
   * Calculate cost projections
   */
  static calculateCostProjections(currentCosts, escalationRate) {
    /* istanbul ignore next */
    cov_9l8cmvs2f().f[12]++;
    const projections =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[72]++, []);
    /* istanbul ignore next */
    cov_9l8cmvs2f().s[73]++;
    for (let year =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[74]++, 1); year <= 5; year++) {
      const escalationFactor =
      /* istanbul ignore next */
      (cov_9l8cmvs2f().s[75]++, Math.pow(1 + escalationRate, year));
      /* istanbul ignore next */
      cov_9l8cmvs2f().s[76]++;
      projections.push({
        year,
        totalCost: currentCosts.totalCost * escalationFactor,
        energyCost: currentCosts.energyCost * escalationFactor,
        demandCost: currentCosts.demandCost * escalationFactor,
        fixedCost: currentCosts.fixedCost * escalationFactor,
        escalationRate,
        cumulativeCost: currentCosts.totalCost * ((Math.pow(1 + escalationRate, year) - 1) / escalationRate)
      });
    }
    /* istanbul ignore next */
    cov_9l8cmvs2f().s[77]++;
    return projections;
  }
  /**
   * Identify cost saving opportunities
   */
  static identifyCostSavingOpportunities(energyConsumption, currentCosts) {
    /* istanbul ignore next */
    cov_9l8cmvs2f().f[13]++;
    const opportunities =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[78]++, []);
    // Peak demand reduction opportunity
    /* istanbul ignore next */
    cov_9l8cmvs2f().s[79]++;
    if (energyConsumption.peakDemand.peakShavingPotential > 0) {
      /* istanbul ignore next */
      cov_9l8cmvs2f().b[10][0]++;
      cov_9l8cmvs2f().s[80]++;
      opportunities.push({
        id: 'peak_demand_reduction',
        name: 'Peak Demand Reduction',
        description: 'Reduce peak demand through load scheduling and control optimization',
        potentialSavings: energyConsumption.peakDemand.peakShavingPotential * 15 * 12,
        // $15/kW * 12 months
        implementationCost: 5000,
        paybackPeriod: 18,
        // months
        savingsType: 'demand_reduction',
        confidence: 0.8
      });
    } else
    /* istanbul ignore next */
    {
      cov_9l8cmvs2f().b[10][1]++;
    }
    // Energy efficiency improvement
    cov_9l8cmvs2f().s[81]++;
    opportunities.push({
      id: 'efficiency_improvement',
      name: 'System Efficiency Improvement',
      description: 'Improve overall system efficiency through equipment upgrades and optimization',
      potentialSavings: currentCosts.energyCost * 0.15,
      // 15% energy savings
      implementationCost: 15000,
      paybackPeriod: 36,
      // months
      savingsType: 'energy_reduction',
      confidence: 0.7
    });
    /* istanbul ignore next */
    cov_9l8cmvs2f().s[82]++;
    return opportunities;
  }
  /**
   * Create utility rate structure
   */
  static createUtilityRateStructure(rates) {
    /* istanbul ignore next */
    cov_9l8cmvs2f().f[14]++;
    cov_9l8cmvs2f().s[83]++;
    return {
      rateSchedule: 'Commercial General Service',
      energyCharges: {
        flatRate: rates.energyRate,
        tieredRates: [{
          tier: 1,
          threshold: 1000,
          rate: rates.energyRate * 0.9
        }, {
          tier: 2,
          threshold: 5000,
          rate: rates.energyRate
        }, {
          tier: 3,
          threshold: Infinity,
          rate: rates.energyRate * 1.1
        }]
      },
      demandCharges: {
        rate: rates.demandRate,
        ratchetClause: true,
        seasonalRates: {
          summer: rates.demandRate * 1.2,
          winter: rates.demandRate * 0.8
        }
      },
      fixedCharges: {
        customerCharge: rates.fixedRate,
        facilityCharge: 0,
        minimumBill: rates.fixedRate
      },
      timeOfUseRates: rates.timeOfUseRates
    };
  }
  /**
   * Calculate time-of-use savings potential
   */
  static calculateTOUSavings(energyConsumption, touRates) {
    /* istanbul ignore next */
    cov_9l8cmvs2f().f[15]++;
    // Simplified TOU savings calculation
    const totalEnergy =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[84]++, energyConsumption.totalConsumption.value);
    const peakEnergyPercent =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[85]++, 0.3); // 30% during peak hours
    const offPeakEnergyPercent =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[86]++, 0.4); // 40% during off-peak hours
    const shoulderEnergyPercent =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[87]++, 0.3); // 30% during shoulder hours
    const currentCost =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[88]++, totalEnergy * 0.12); // Flat rate
    const touCost =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[89]++, totalEnergy * peakEnergyPercent * touRates.peak + totalEnergy * offPeakEnergyPercent * touRates.offPeak + totalEnergy * shoulderEnergyPercent * touRates.shoulder);
    /* istanbul ignore next */
    cov_9l8cmvs2f().s[90]++;
    return Math.max(0, currentCost - touCost);
  }
  /**
   * Calculate carbon footprint
   */
  static async calculateCarbonFootprint(energyConsumption, systemConfiguration) {
    /* istanbul ignore next */
    cov_9l8cmvs2f().f[16]++;
    const totalEnergyKWh =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[91]++, energyConsumption.totalConsumption.value);
    // Calculate operational emissions
    const operationalEmissions =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[92]++, this.createEmissionMeasurement(totalEnergyKWh * this.EMISSION_FACTORS.GRID_AVERAGE, SystemAnalysisTypes_1.EmissionUnits.KG_CO2E, SystemAnalysisTypes_1.TimeFrame.ANNUALLY, SystemAnalysisTypes_1.EmissionScope.SCOPE_2));
    // Calculate embodied emissions (simplified)
    const embodiedEmissions =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[93]++, this.createEmissionMeasurement(500,
    // Simplified embodied carbon for HVAC system
    SystemAnalysisTypes_1.EmissionUnits.KG_CO2E, SystemAnalysisTypes_1.TimeFrame.ANNUALLY, SystemAnalysisTypes_1.EmissionScope.SCOPE_3));
    // Total emissions
    const totalEmissions =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[94]++, this.createEmissionMeasurement(operationalEmissions.value + embodiedEmissions.value, SystemAnalysisTypes_1.EmissionUnits.KG_CO2E, SystemAnalysisTypes_1.TimeFrame.ANNUALLY, SystemAnalysisTypes_1.EmissionScope.SCOPE_2));
    // Emissions by source
    const emissionsBySource =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[95]++, [{
      source: 'Electricity Grid',
      emissions: operationalEmissions.value,
      percentage: operationalEmissions.value / totalEmissions.value * 100,
      emissionFactor: this.EMISSION_FACTORS.GRID_AVERAGE
    }, {
      source: 'Embodied Carbon',
      emissions: embodiedEmissions.value,
      percentage: embodiedEmissions.value / totalEmissions.value * 100,
      emissionFactor: 0
    }]);
    // Emissions trend (simplified)
    const emissionsTrend =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[96]++, {
      currentEmissions: totalEmissions.value,
      trendDirection: 'stable',
      projectedEmissions: totalEmissions.value,
      reductionPotential: totalEmissions.value * 0.3,
      // 30% reduction potential
      timeHorizon: 10 // years
    });
    // Offset opportunities
    const offsetOpportunities =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[97]++, [{
      type: 'Renewable Energy',
      potential: operationalEmissions.value * 0.8,
      // 80% offset potential
      cost: operationalEmissions.value * 0.02,
      // $0.02/kg CO2e
      implementation: 'On-site solar or renewable energy credits'
    }, {
      type: 'Energy Efficiency',
      potential: operationalEmissions.value * 0.2,
      // 20% reduction potential
      cost: 0,
      // Cost savings
      implementation: 'System optimization and efficiency improvements'
    }]);
    // Benchmark comparison
    const benchmarkComparison =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[98]++, {
      benchmarkType: 'Industry Average',
      currentIntensity: totalEmissions.value / systemConfiguration.designParameters.designAirflow,
      // kg CO2e/CFM
      benchmarkIntensity: 0.15,
      // Industry average
      percentile: 60,
      improvementPotential: totalEmissions.value * 0.25
    });
    /* istanbul ignore next */
    cov_9l8cmvs2f().s[99]++;
    return {
      totalEmissions,
      operationalEmissions,
      embodiedEmissions,
      emissionsBySource,
      emissionsTrend,
      offsetOpportunities,
      benchmarkComparison
    };
  }
  /**
   * Create standardized emission measurement
   */
  static createEmissionMeasurement(value, units, timeFrame, scope, accuracy =
  /* istanbul ignore next */
  (cov_9l8cmvs2f().b[11][0]++, 0.8)) {
    /* istanbul ignore next */
    cov_9l8cmvs2f().f[17]++;
    cov_9l8cmvs2f().s[100]++;
    return {
      value,
      units,
      timeFrame,
      scope,
      accuracy
    };
  }
  /**
   * Perform energy benchmarking
   */
  static async performEnergyBenchmarking(systemConfiguration, efficiencyMetrics) {
    /* istanbul ignore next */
    cov_9l8cmvs2f().f[18]++;
    const sfp =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[101]++, efficiencyMetrics.specificFanPower);
    const systemEfficiency =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[102]++, efficiencyMetrics.systemEfficiency);
    /* istanbul ignore next */
    cov_9l8cmvs2f().s[103]++;
    return {
      benchmarkType: 'Industry Standard',
      benchmarkSource: 'ASHRAE 90.1 and Industry Data',
      energyIntensity: {
        current: sfp,
        benchmark: 1.25,
        // ASHRAE 90.1 limit
        industryAverage: 1.1,
        bestPractice: 0.8,
        percentile: this.calculateSFPPercentile(sfp)
      },
      efficiencyRating: {
        current: systemEfficiency,
        benchmark: 80,
        industryAverage: 82,
        bestPractice: 90,
        grade: this.calculateEfficiencyGrade(systemEfficiency)
      },
      complianceStatus: {
        ashrae901: sfp <= 1.25 ?
        /* istanbul ignore next */
        (cov_9l8cmvs2f().b[12][0]++, 'compliant') :
        /* istanbul ignore next */
        (cov_9l8cmvs2f().b[12][1]++, 'non_compliant'),
        energyStar: systemEfficiency >= 85 ?
        /* istanbul ignore next */
        (cov_9l8cmvs2f().b[13][0]++, 'qualified') :
        /* istanbul ignore next */
        (cov_9l8cmvs2f().b[13][1]++, 'not_qualified'),
        leed: this.calculateLEEDPoints(sfp, systemEfficiency)
      },
      improvementPotential: {
        energySavings: Math.max(0, (sfp - 0.8) / sfp * 100),
        // % savings potential
        costSavings: 0,
        // Would be calculated based on energy costs
        emissionReduction: 0 // Would be calculated based on carbon intensity
      }
    };
  }
  /**
   * Calculate efficiency grade
   */
  static calculateEfficiencyGrade(efficiency) {
    /* istanbul ignore next */
    cov_9l8cmvs2f().f[19]++;
    cov_9l8cmvs2f().s[104]++;
    if (efficiency >= 90) {
      /* istanbul ignore next */
      cov_9l8cmvs2f().b[14][0]++;
      cov_9l8cmvs2f().s[105]++;
      return 'A+';
    } else
    /* istanbul ignore next */
    {
      cov_9l8cmvs2f().b[14][1]++;
    }
    cov_9l8cmvs2f().s[106]++;
    if (efficiency >= 85) {
      /* istanbul ignore next */
      cov_9l8cmvs2f().b[15][0]++;
      cov_9l8cmvs2f().s[107]++;
      return 'A';
    } else
    /* istanbul ignore next */
    {
      cov_9l8cmvs2f().b[15][1]++;
    }
    cov_9l8cmvs2f().s[108]++;
    if (efficiency >= 80) {
      /* istanbul ignore next */
      cov_9l8cmvs2f().b[16][0]++;
      cov_9l8cmvs2f().s[109]++;
      return 'B';
    } else
    /* istanbul ignore next */
    {
      cov_9l8cmvs2f().b[16][1]++;
    }
    cov_9l8cmvs2f().s[110]++;
    if (efficiency >= 75) {
      /* istanbul ignore next */
      cov_9l8cmvs2f().b[17][0]++;
      cov_9l8cmvs2f().s[111]++;
      return 'C';
    } else
    /* istanbul ignore next */
    {
      cov_9l8cmvs2f().b[17][1]++;
    }
    cov_9l8cmvs2f().s[112]++;
    if (efficiency >= 70) {
      /* istanbul ignore next */
      cov_9l8cmvs2f().b[18][0]++;
      cov_9l8cmvs2f().s[113]++;
      return 'D';
    } else
    /* istanbul ignore next */
    {
      cov_9l8cmvs2f().b[18][1]++;
    }
    cov_9l8cmvs2f().s[114]++;
    return 'F';
  }
  /**
   * Calculate LEED points
   */
  static calculateLEEDPoints(sfp, efficiency) {
    /* istanbul ignore next */
    cov_9l8cmvs2f().f[20]++;
    let points =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[115]++, 0);
    // LEED points for fan power efficiency
    /* istanbul ignore next */
    cov_9l8cmvs2f().s[116]++;
    if (sfp <= 0.8) {
      /* istanbul ignore next */
      cov_9l8cmvs2f().b[19][0]++;
      cov_9l8cmvs2f().s[117]++;
      points += 2;
    } else {
      /* istanbul ignore next */
      cov_9l8cmvs2f().b[19][1]++;
      cov_9l8cmvs2f().s[118]++;
      if (sfp <= 1.0) {
        /* istanbul ignore next */
        cov_9l8cmvs2f().b[20][0]++;
        cov_9l8cmvs2f().s[119]++;
        points += 1;
      } else
      /* istanbul ignore next */
      {
        cov_9l8cmvs2f().b[20][1]++;
      }
    }
    // LEED points for system efficiency
    /* istanbul ignore next */
    cov_9l8cmvs2f().s[120]++;
    if (efficiency >= 85) {
      /* istanbul ignore next */
      cov_9l8cmvs2f().b[21][0]++;
      cov_9l8cmvs2f().s[121]++;
      points += 1;
    } else
    /* istanbul ignore next */
    {
      cov_9l8cmvs2f().b[21][1]++;
    }
    cov_9l8cmvs2f().s[122]++;
    return Math.min(points, 3); // Maximum 3 points
  }
  /**
   * Identify optimization opportunities
   */
  static async identifyOptimizationOpportunities(systemConfiguration, performanceMetrics, efficiencyMetrics, energyCosts) {
    /* istanbul ignore next */
    cov_9l8cmvs2f().f[21]++;
    const opportunities =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[123]++, []);
    // Fan speed optimization
    /* istanbul ignore next */
    cov_9l8cmvs2f().s[124]++;
    if (efficiencyMetrics.specificFanPower > 1.0) {
      /* istanbul ignore next */
      cov_9l8cmvs2f().b[22][0]++;
      cov_9l8cmvs2f().s[125]++;
      opportunities.push({
        id: 'fan_speed_optimization',
        name: 'Fan Speed Optimization',
        description: 'Optimize fan speed control to reduce energy consumption while maintaining comfort',
        category: 'Control Optimization',
        energySavingsPotential: 15,
        // %
        costSavingsPotential: energyCosts.currentCosts.energyCost * 0.15,
        implementationCost: 3000,
        paybackPeriod: 12,
        // months
        complexity: 'Low',
        priority: 'High',
        requiredActions: ['Install VFD if not present', 'Implement demand-based control', 'Optimize control sequences'],
        expectedResults: {
          energyReduction: 15,
          demandReduction: 10,
          emissionReduction: 12,
          costSavings: energyCosts.currentCosts.energyCost * 0.15
        }
      });
    } else
    /* istanbul ignore next */
    {
      cov_9l8cmvs2f().b[22][1]++;
    }
    // Duct system optimization
    cov_9l8cmvs2f().s[126]++;
    if (performanceMetrics.totalSystemPressure.value > 3.0) {
      /* istanbul ignore next */
      cov_9l8cmvs2f().b[23][0]++;
      cov_9l8cmvs2f().s[127]++;
      opportunities.push({
        id: 'duct_optimization',
        name: 'Duct System Optimization',
        description: 'Reduce system pressure losses through duct sizing and layout optimization',
        category: 'System Design',
        energySavingsPotential: 20,
        // %
        costSavingsPotential: energyCosts.currentCosts.energyCost * 0.20,
        implementationCost: 15000,
        paybackPeriod: 36,
        // months
        complexity: 'High',
        priority: 'Medium',
        requiredActions: ['Analyze duct sizing', 'Identify pressure loss sources', 'Redesign critical sections', 'Seal ductwork leaks'],
        expectedResults: {
          energyReduction: 20,
          demandReduction: 18,
          emissionReduction: 20,
          costSavings: energyCosts.currentCosts.energyCost * 0.20
        }
      });
    } else
    /* istanbul ignore next */
    {
      cov_9l8cmvs2f().b[23][1]++;
    }
    // Filter optimization
    cov_9l8cmvs2f().s[128]++;
    opportunities.push({
      id: 'filter_optimization',
      name: 'Filter System Optimization',
      description: 'Optimize filter selection and maintenance to reduce pressure drop',
      category: 'Maintenance',
      energySavingsPotential: 8,
      // %
      costSavingsPotential: energyCosts.currentCosts.energyCost * 0.08,
      implementationCost: 2000,
      paybackPeriod: 18,
      // months
      complexity: 'Low',
      priority: 'Medium',
      requiredActions: ['Evaluate filter efficiency vs pressure drop', 'Implement pressure monitoring', 'Optimize replacement schedule'],
      expectedResults: {
        energyReduction: 8,
        demandReduction: 5,
        emissionReduction: 8,
        costSavings: energyCosts.currentCosts.energyCost * 0.08
      }
    });
    /* istanbul ignore next */
    cov_9l8cmvs2f().s[129]++;
    return opportunities;
  }
  /**
   * Perform seasonal energy analysis
   */
  static async performSeasonalEnergyAnalysis(systemConfiguration, energyConsumption) {
    /* istanbul ignore next */
    cov_9l8cmvs2f().f[22]++;
    const baseConsumption =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[130]++, energyConsumption.totalConsumption.value);
    /* istanbul ignore next */
    cov_9l8cmvs2f().s[131]++;
    return {
      seasonalBreakdown: [{
        season: 'Spring',
        energyConsumption: baseConsumption * 0.22,
        // 22% of annual
        averageLoad: energyConsumption.loadProfile.averageLoad * 0.8,
        peakLoad: energyConsumption.loadProfile.peakLoad * 0.7,
        operatingHours: 2000,
        efficiency: 85,
        costs: baseConsumption * 0.22 * 0.12 // $0.12/kWh
      }, {
        season: 'Summer',
        energyConsumption: baseConsumption * 0.35,
        // 35% of annual
        averageLoad: energyConsumption.loadProfile.averageLoad * 1.2,
        peakLoad: energyConsumption.loadProfile.peakLoad * 1.0,
        operatingHours: 2500,
        efficiency: 80,
        // Lower efficiency due to higher loads
        costs: baseConsumption * 0.35 * 0.15 // Higher summer rates
      }, {
        season: 'Fall',
        energyConsumption: baseConsumption * 0.25,
        // 25% of annual
        averageLoad: energyConsumption.loadProfile.averageLoad * 0.9,
        peakLoad: energyConsumption.loadProfile.peakLoad * 0.8,
        operatingHours: 2200,
        efficiency: 83,
        costs: baseConsumption * 0.25 * 0.12
      }, {
        season: 'Winter',
        energyConsumption: baseConsumption * 0.18,
        // 18% of annual
        averageLoad: energyConsumption.loadProfile.averageLoad * 0.7,
        peakLoad: energyConsumption.loadProfile.peakLoad * 0.6,
        operatingHours: 1800,
        efficiency: 87,
        // Higher efficiency at lower loads
        costs: baseConsumption * 0.18 * 0.11 // Lower winter rates
      }],
      peakSeasons: ['Summer', 'Fall'],
      optimizationOpportunities: [{
        season: 'Summer',
        opportunity: 'Peak load management',
        potential: 'Reduce peak demand by 15% through load scheduling',
        savings: 2500
      }, {
        season: 'Winter',
        opportunity: 'Extended economizer operation',
        potential: 'Increase free cooling hours by 20%',
        savings: 800
      }],
      weatherSensitivity: {
        temperatureCoefficient: 0.02,
        // 2% change per degree F
        humidityCoefficient: 0.005,
        // 0.5% change per % RH
        baselineTemperature: 65,
        // °F
        heatingThreshold: 55,
        // °F
        coolingThreshold: 75 // °F
      }
    };
  }
  /**
   * Generate unique analysis ID
   */
  static generateAnalysisId(systemId) {
    /* istanbul ignore next */
    cov_9l8cmvs2f().f[23]++;
    const timestamp =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[132]++, Date.now());
    const random =
    /* istanbul ignore next */
    (cov_9l8cmvs2f().s[133]++, Math.random().toString(36).substring(2, 8));
    /* istanbul ignore next */
    cov_9l8cmvs2f().s[134]++;
    return `energy_analysis_${systemId}_${timestamp}_${random}`;
  }
}
/* istanbul ignore next */
cov_9l8cmvs2f().s[135]++;
exports.EnergyEfficiencyAnalysisEngine = EnergyEfficiencyAnalysisEngine;
/* istanbul ignore next */
cov_9l8cmvs2f().s[136]++;
EnergyEfficiencyAnalysisEngine.VERSION = '3.0.0';
/* istanbul ignore next */
cov_9l8cmvs2f().s[137]++;
EnergyEfficiencyAnalysisEngine.ENERGY_CACHE = new Map();
// Energy conversion constants
/* istanbul ignore next */
cov_9l8cmvs2f().s[138]++;
EnergyEfficiencyAnalysisEngine.KWH_TO_BTU = 3412.14;
/* istanbul ignore next */
cov_9l8cmvs2f().s[139]++;
EnergyEfficiencyAnalysisEngine.HP_TO_KW = 0.746;
/* istanbul ignore next */
cov_9l8cmvs2f().s[140]++;
EnergyEfficiencyAnalysisEngine.CFM_TO_M3S = 0.000471947;
// Carbon emission factors (kg CO2e per kWh)
/* istanbul ignore next */
cov_9l8cmvs2f().s[141]++;
EnergyEfficiencyAnalysisEngine.EMISSION_FACTORS = {
  GRID_AVERAGE: 0.4,
  // US grid average
  COAL: 0.82,
  NATURAL_GAS: 0.35,
  RENEWABLE: 0.02
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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