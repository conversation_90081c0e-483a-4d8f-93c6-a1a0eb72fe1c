a8e30555d9f988ffd246a577a3e60dad
"use strict";

/**
 * System Optimization Engine for Phase 3 Priority 2
 *
 * Main optimization engine providing comprehensive system optimization capabilities including:
 * - Multi-objective optimization with Pareto analysis
 * - Multiple optimization algorithms (GA, SA, PSO, GD)
 * - Constraint handling and validation
 * - Performance analysis and recommendations
 * - Integration with existing Phase 1/2/3 Priority 1 components
 *
 * @version 3.0.0
 * <AUTHOR> Suite Development Team
 */
/* istanbul ignore next */
function cov_2jmlri0rtb() {
  var path = "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\SystemOptimizationEngine.ts";
  var hash = "39fb248e97b128603ca43114140f271357ef8b1c";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\SystemOptimizationEngine.ts",
    statementMap: {
      "0": {
        start: {
          line: 15,
          column: 0
        },
        end: {
          line: 15,
          column: 62
        }
      },
      "1": {
        start: {
          line: 16,
          column: 0
        },
        end: {
          line: 16,
          column: 42
        }
      },
      "2": {
        start: {
          line: 17,
          column: 34
        },
        end: {
          line: 17,
          column: 76
        }
      },
      "3": {
        start: {
          line: 19,
          column: 35
        },
        end: {
          line: 19,
          column: 72
        }
      },
      "4": {
        start: {
          line: 21,
          column: 27
        },
        end: {
          line: 21,
          column: 67
        }
      },
      "5": {
        start: {
          line: 22,
          column: 29
        },
        end: {
          line: 22,
          column: 71
        }
      },
      "6": {
        start: {
          line: 23,
          column: 36
        },
        end: {
          line: 23,
          column: 85
        }
      },
      "7": {
        start: {
          line: 24,
          column: 26
        },
        end: {
          line: 24,
          column: 65
        }
      },
      "8": {
        start: {
          line: 25,
          column: 46
        },
        end: {
          line: 25,
          column: 94
        }
      },
      "9": {
        start: {
          line: 35,
          column: 8
        },
        end: {
          line: 45,
          column: 9
        }
      },
      "10": {
        start: {
          line: 36,
          column: 12
        },
        end: {
          line: 36,
          column: 78
        }
      },
      "11": {
        start: {
          line: 37,
          column: 12
        },
        end: {
          line: 37,
          column: 84
        }
      },
      "12": {
        start: {
          line: 38,
          column: 12
        },
        end: {
          line: 38,
          column: 93
        }
      },
      "13": {
        start: {
          line: 39,
          column: 12
        },
        end: {
          line: 39,
          column: 75
        }
      },
      "14": {
        start: {
          line: 40,
          column: 12
        },
        end: {
          line: 40,
          column: 94
        }
      },
      "15": {
        start: {
          line: 43,
          column: 12
        },
        end: {
          line: 43,
          column: 83
        }
      },
      "16": {
        start: {
          line: 44,
          column: 12
        },
        end: {
          line: 44,
          column: 83
        }
      },
      "17": {
        start: {
          line: 51,
          column: 26
        },
        end: {
          line: 51,
          column: 43
        }
      },
      "18": {
        start: {
          line: 52,
          column: 8
        },
        end: {
          line: 75,
          column: 9
        }
      },
      "19": {
        start: {
          line: 54,
          column: 12
        },
        end: {
          line: 54,
          column: 54
        }
      },
      "20": {
        start: {
          line: 56,
          column: 35
        },
        end: {
          line: 56,
          column: 71
        }
      },
      "21": {
        start: {
          line: 58,
          column: 38
        },
        end: {
          line: 58,
          column: 75
        }
      },
      "22": {
        start: {
          line: 60,
          column: 40
        },
        end: {
          line: 60,
          column: 79
        }
      },
      "23": {
        start: {
          line: 62,
          column: 30
        },
        end: {
          line: 62,
          column: 70
        }
      },
      "24": {
        start: {
          line: 64,
          column: 27
        },
        end: {
          line: 64,
          column: 129
        }
      },
      "25": {
        start: {
          line: 66,
          column: 32
        },
        end: {
          line: 66,
          column: 89
        }
      },
      "26": {
        start: {
          line: 68,
          column: 12
        },
        end: {
          line: 68,
          column: 78
        }
      },
      "27": {
        start: {
          line: 69,
          column: 12
        },
        end: {
          line: 69,
          column: 80
        }
      },
      "28": {
        start: {
          line: 70,
          column: 12
        },
        end: {
          line: 70,
          column: 31
        }
      },
      "29": {
        start: {
          line: 73,
          column: 12
        },
        end: {
          line: 73,
          column: 57
        }
      },
      "30": {
        start: {
          line: 74,
          column: 12
        },
        end: {
          line: 74,
          column: 69
        }
      },
      "31": {
        start: {
          line: 81,
          column: 8
        },
        end: {
          line: 83,
          column: 9
        }
      },
      "32": {
        start: {
          line: 82,
          column: 12
        },
        end: {
          line: 82,
          column: 91
        }
      },
      "33": {
        start: {
          line: 84,
          column: 8
        },
        end: {
          line: 123,
          column: 9
        }
      },
      "34": {
        start: {
          line: 85,
          column: 12
        },
        end: {
          line: 85,
          column: 92
        }
      },
      "35": {
        start: {
          line: 87,
          column: 39
        },
        end: {
          line: 87,
          column: 123
        }
      },
      "36": {
        start: {
          line: 87,
          column: 80
        },
        end: {
          line: 87,
          column: 122
        }
      },
      "37": {
        start: {
          line: 89,
          column: 40
        },
        end: {
          line: 89,
          column: 129
        }
      },
      "38": {
        start: {
          line: 89,
          column: 78
        },
        end: {
          line: 89,
          column: 128
        }
      },
      "39": {
        start: {
          line: 91,
          column: 32
        },
        end: {
          line: 115,
          column: 14
        }
      },
      "40": {
        start: {
          line: 116,
          column: 27
        },
        end: {
          line: 116,
          column: 117
        }
      },
      "41": {
        start: {
          line: 117,
          column: 12
        },
        end: {
          line: 117,
          column: 93
        }
      },
      "42": {
        start: {
          line: 118,
          column: 12
        },
        end: {
          line: 118,
          column: 26
        }
      },
      "43": {
        start: {
          line: 121,
          column: 12
        },
        end: {
          line: 121,
          column: 73
        }
      },
      "44": {
        start: {
          line: 122,
          column: 12
        },
        end: {
          line: 122,
          column: 24
        }
      },
      "45": {
        start: {
          line: 130,
          column: 24
        },
        end: {
          line: 165,
          column: 9
        }
      },
      "46": {
        start: {
          line: 166,
          column: 8
        },
        end: {
          line: 166,
          column: 111
        }
      },
      "47": {
        start: {
          line: 172,
          column: 24
        },
        end: {
          line: 227,
          column: 9
        }
      },
      "48": {
        start: {
          line: 228,
          column: 8
        },
        end: {
          line: 228,
          column: 116
        }
      },
      "49": {
        start: {
          line: 234,
          column: 8
        },
        end: {
          line: 236,
          column: 9
        }
      },
      "50": {
        start: {
          line: 235,
          column: 12
        },
        end: {
          line: 235,
          column: 85
        }
      },
      "51": {
        start: {
          line: 237,
          column: 8
        },
        end: {
          line: 239,
          column: 9
        }
      },
      "52": {
        start: {
          line: 238,
          column: 12
        },
        end: {
          line: 238,
          column: 84
        }
      },
      "53": {
        start: {
          line: 240,
          column: 8
        },
        end: {
          line: 242,
          column: 9
        }
      },
      "54": {
        start: {
          line: 241,
          column: 12
        },
        end: {
          line: 241,
          column: 85
        }
      },
      "55": {
        start: {
          line: 244,
          column: 8
        },
        end: {
          line: 248,
          column: 9
        }
      },
      "56": {
        start: {
          line: 245,
          column: 12
        },
        end: {
          line: 247,
          column: 13
        }
      },
      "57": {
        start: {
          line: 246,
          column: 16
        },
        end: {
          line: 246,
          column: 78
        }
      },
      "58": {
        start: {
          line: 250,
          column: 28
        },
        end: {
          line: 250,
          column: 99
        }
      },
      "59": {
        start: {
          line: 250,
          column: 79
        },
        end: {
          line: 250,
          column: 95
        }
      },
      "60": {
        start: {
          line: 251,
          column: 8
        },
        end: {
          line: 254,
          column: 9
        }
      },
      "61": {
        start: {
          line: 252,
          column: 12
        },
        end: {
          line: 252,
          column: 88
        }
      },
      "62": {
        start: {
          line: 253,
          column: 12
        },
        end: {
          line: 253,
          column: 84
        }
      },
      "63": {
        start: {
          line: 253,
          column: 57
        },
        end: {
          line: 253,
          column: 82
        }
      },
      "64": {
        start: {
          line: 260,
          column: 8
        },
        end: {
          line: 306,
          column: 10
        }
      },
      "65": {
        start: {
          line: 261,
          column: 12
        },
        end: {
          line: 305,
          column: 13
        }
      },
      "66": {
        start: {
          line: 263,
          column: 41
        },
        end: {
          line: 263,
          column: 108
        }
      },
      "67": {
        start: {
          line: 265,
          column: 42
        },
        end: {
          line: 274,
          column: 18
        }
      },
      "68": {
        start: {
          line: 276,
          column: 42
        },
        end: {
          line: 276,
          column: 43
        }
      },
      "69": {
        start: {
          line: 277,
          column: 16
        },
        end: {
          line: 299,
          column: 17
        }
      },
      "70": {
        start: {
          line: 279,
          column: 20
        },
        end: {
          line: 297,
          column: 21
        }
      },
      "71": {
        start: {
          line: 281,
          column: 28
        },
        end: {
          line: 281,
          column: 81
        }
      },
      "72": {
        start: {
          line: 282,
          column: 28
        },
        end: {
          line: 282,
          column: 34
        }
      },
      "73": {
        start: {
          line: 284,
          column: 28
        },
        end: {
          line: 284,
          column: 114
        }
      },
      "74": {
        start: {
          line: 285,
          column: 28
        },
        end: {
          line: 285,
          column: 34
        }
      },
      "75": {
        start: {
          line: 287,
          column: 28
        },
        end: {
          line: 287,
          column: 106
        }
      },
      "76": {
        start: {
          line: 288,
          column: 28
        },
        end: {
          line: 288,
          column: 34
        }
      },
      "77": {
        start: {
          line: 290,
          column: 28
        },
        end: {
          line: 290,
          column: 107
        }
      },
      "78": {
        start: {
          line: 291,
          column: 28
        },
        end: {
          line: 291,
          column: 34
        }
      },
      "79": {
        start: {
          line: 293,
          column: 28
        },
        end: {
          line: 293,
          column: 114
        }
      },
      "80": {
        start: {
          line: 294,
          column: 28
        },
        end: {
          line: 294,
          column: 34
        }
      },
      "81": {
        start: {
          line: 296,
          column: 28
        },
        end: {
          line: 296,
          column: 85
        }
      },
      "82": {
        start: {
          line: 298,
          column: 20
        },
        end: {
          line: 298,
          column: 77
        }
      },
      "83": {
        start: {
          line: 300,
          column: 16
        },
        end: {
          line: 300,
          column: 43
        }
      },
      "84": {
        start: {
          line: 303,
          column: 16
        },
        end: {
          line: 303,
          column: 80
        }
      },
      "85": {
        start: {
          line: 304,
          column: 16
        },
        end: {
          line: 304,
          column: 40
        }
      },
      "86": {
        start: {
          line: 312,
          column: 8
        },
        end: {
          line: 325,
          column: 11
        }
      },
      "87": {
        start: {
          line: 313,
          column: 12
        },
        end: {
          line: 324,
          column: 14
        }
      },
      "88": {
        start: {
          line: 314,
          column: 16
        },
        end: {
          line: 323,
          column: 17
        }
      },
      "89": {
        start: {
          line: 316,
          column: 45
        },
        end: {
          line: 316,
          column: 112
        }
      },
      "90": {
        start: {
          line: 318,
          column: 20
        },
        end: {
          line: 318,
          column: 92
        }
      },
      "91": {
        start: {
          line: 321,
          column: 20
        },
        end: {
          line: 321,
          column: 90
        }
      },
      "92": {
        start: {
          line: 322,
          column: 20
        },
        end: {
          line: 322,
          column: 44
        }
      },
      "93": {
        start: {
          line: 332,
          column: 33
        },
        end: {
          line: 332,
          column: 71
        }
      },
      "94": {
        start: {
          line: 334,
          column: 8
        },
        end: {
          line: 336,
          column: 9
        }
      },
      "95": {
        start: {
          line: 335,
          column: 12
        },
        end: {
          line: 335,
          column: 67
        }
      },
      "96": {
        start: {
          line: 337,
          column: 8
        },
        end: {
          line: 337,
          column: 32
        }
      },
      "97": {
        start: {
          line: 344,
          column: 8
        },
        end: {
          line: 362,
          column: 9
        }
      },
      "98": {
        start: {
          line: 346,
          column: 16
        },
        end: {
          line: 346,
          column: 61
        }
      },
      "99": {
        start: {
          line: 347,
          column: 16
        },
        end: {
          line: 347,
          column: 22
        }
      },
      "100": {
        start: {
          line: 349,
          column: 16
        },
        end: {
          line: 349,
          column: 64
        }
      },
      "101": {
        start: {
          line: 350,
          column: 16
        },
        end: {
          line: 350,
          column: 22
        }
      },
      "102": {
        start: {
          line: 352,
          column: 16
        },
        end: {
          line: 352,
          column: 65
        }
      },
      "103": {
        start: {
          line: 353,
          column: 16
        },
        end: {
          line: 353,
          column: 22
        }
      },
      "104": {
        start: {
          line: 355,
          column: 16
        },
        end: {
          line: 355,
          column: 67
        }
      },
      "105": {
        start: {
          line: 356,
          column: 16
        },
        end: {
          line: 356,
          column: 22
        }
      },
      "106": {
        start: {
          line: 358,
          column: 16
        },
        end: {
          line: 358,
          column: 61
        }
      },
      "107": {
        start: {
          line: 359,
          column: 16
        },
        end: {
          line: 359,
          column: 22
        }
      },
      "108": {
        start: {
          line: 361,
          column: 16
        },
        end: {
          line: 361,
          column: 72
        }
      },
      "109": {
        start: {
          line: 385,
          column: 25
        },
        end: {
          line: 385,
          column: 97
        }
      },
      "110": {
        start: {
          line: 386,
          column: 37
        },
        end: {
          line: 386,
          column: 41
        }
      },
      "111": {
        start: {
          line: 387,
          column: 34
        },
        end: {
          line: 387,
          column: 73
        }
      },
      "112": {
        start: {
          line: 388,
          column: 8
        },
        end: {
          line: 388,
          column: 33
        }
      },
      "113": {
        start: {
          line: 395,
          column: 27
        },
        end: {
          line: 395,
          column: 28
        }
      },
      "114": {
        start: {
          line: 396,
          column: 31
        },
        end: {
          line: 396,
          column: 32
        }
      },
      "115": {
        start: {
          line: 397,
          column: 28
        },
        end: {
          line: 397,
          column: 29
        }
      },
      "116": {
        start: {
          line: 399,
          column: 8
        },
        end: {
          line: 401,
          column: 9
        }
      },
      "117": {
        start: {
          line: 400,
          column: 12
        },
        end: {
          line: 400,
          column: 71
        }
      },
      "118": {
        start: {
          line: 403,
          column: 8
        },
        end: {
          line: 403,
          column: 47
        }
      },
      "119": {
        start: {
          line: 405,
          column: 34
        },
        end: {
          line: 405,
          column: 102
        }
      },
      "120": {
        start: {
          line: 406,
          column: 27
        },
        end: {
          line: 406,
          column: 31
        }
      },
      "121": {
        start: {
          line: 407,
          column: 8
        },
        end: {
          line: 407,
          column: 55
        }
      },
      "122": {
        start: {
          line: 409,
          column: 31
        },
        end: {
          line: 409,
          column: 33
        }
      },
      "123": {
        start: {
          line: 410,
          column: 26
        },
        end: {
          line: 410,
          column: 92
        }
      },
      "124": {
        start: {
          line: 411,
          column: 8
        },
        end: {
          line: 411,
          column: 25
        }
      },
      "125": {
        start: {
          line: 418,
          column: 30
        },
        end: {
          line: 418,
          column: 31
        }
      },
      "126": {
        start: {
          line: 419,
          column: 8
        },
        end: {
          line: 422,
          column: 9
        }
      },
      "127": {
        start: {
          line: 420,
          column: 33
        },
        end: {
          line: 420,
          column: 92
        }
      },
      "128": {
        start: {
          line: 421,
          column: 12
        },
        end: {
          line: 421,
          column: 63
        }
      },
      "129": {
        start: {
          line: 423,
          column: 8
        },
        end: {
          line: 423,
          column: 48
        }
      },
      "130": {
        start: {
          line: 430,
          column: 43
        },
        end: {
          line: 430,
          column: 76
        }
      },
      "131": {
        start: {
          line: 431,
          column: 31
        },
        end: {
          line: 431,
          column: 66
        }
      },
      "132": {
        start: {
          line: 432,
          column: 27
        },
        end: {
          line: 432,
          column: 70
        }
      },
      "133": {
        start: {
          line: 433,
          column: 8
        },
        end: {
          line: 433,
          column: 41
        }
      },
      "134": {
        start: {
          line: 438,
          column: 8
        },
        end: {
          line: 438,
          column: 19
        }
      },
      "135": {
        start: {
          line: 442,
          column: 8
        },
        end: {
          line: 442,
          column: 18
        }
      },
      "136": {
        start: {
          line: 449,
          column: 8
        },
        end: {
          line: 449,
          column: 17
        }
      },
      "137": {
        start: {
          line: 455,
          column: 8
        },
        end: {
          line: 468,
          column: 9
        }
      },
      "138": {
        start: {
          line: 459,
          column: 16
        },
        end: {
          line: 459,
          column: 45
        }
      },
      "139": {
        start: {
          line: 461,
          column: 16
        },
        end: {
          line: 461,
          column: 47
        }
      },
      "140": {
        start: {
          line: 463,
          column: 16
        },
        end: {
          line: 463,
          column: 42
        }
      },
      "141": {
        start: {
          line: 465,
          column: 16
        },
        end: {
          line: 465,
          column: 44
        }
      },
      "142": {
        start: {
          line: 467,
          column: 16
        },
        end: {
          line: 467,
          column: 84
        }
      },
      "143": {
        start: {
          line: 473,
          column: 8
        },
        end: {
          line: 473,
          column: 18
        }
      },
      "144": {
        start: {
          line: 477,
          column: 8
        },
        end: {
          line: 480,
          column: 10
        }
      },
      "145": {
        start: {
          line: 484,
          column: 8
        },
        end: {
          line: 484,
          column: 18
        }
      },
      "146": {
        start: {
          line: 488,
          column: 8
        },
        end: {
          line: 488,
          column: 32
        }
      },
      "147": {
        start: {
          line: 488,
          column: 30
        },
        end: {
          line: 488,
          column: 31
        }
      },
      "148": {
        start: {
          line: 492,
          column: 8
        },
        end: {
          line: 492,
          column: 32
        }
      },
      "149": {
        start: {
          line: 492,
          column: 30
        },
        end: {
          line: 492,
          column: 31
        }
      },
      "150": {
        start: {
          line: 496,
          column: 8
        },
        end: {
          line: 496,
          column: 92
        }
      },
      "151": {
        start: {
          line: 500,
          column: 8
        },
        end: {
          line: 500,
          column: 70
        }
      },
      "152": {
        start: {
          line: 504,
          column: 8
        },
        end: {
          line: 504,
          column: 22
        }
      },
      "153": {
        start: {
          line: 508,
          column: 8
        },
        end: {
          line: 518,
          column: 10
        }
      },
      "154": {
        start: {
          line: 521,
          column: 0
        },
        end: {
          line: 521,
          column: 60
        }
      },
      "155": {
        start: {
          line: 522,
          column: 0
        },
        end: {
          line: 522,
          column: 43
        }
      },
      "156": {
        start: {
          line: 523,
          column: 0
        },
        end: {
          line: 523,
          column: 55
        }
      },
      "157": {
        start: {
          line: 524,
          column: 0
        },
        end: {
          line: 524,
          column: 62
        }
      },
      "158": {
        start: {
          line: 526,
          column: 0
        },
        end: {
          line: 526,
          column: 57
        }
      },
      "159": {
        start: {
          line: 527,
          column: 0
        },
        end: {
          line: 527,
          column: 56
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 34,
            column: 4
          },
          end: {
            line: 34,
            column: 5
          }
        },
        loc: {
          start: {
            line: 34,
            column: 24
          },
          end: {
            line: 46,
            column: 5
          }
        },
        line: 34
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 50,
            column: 4
          },
          end: {
            line: 50,
            column: 5
          }
        },
        loc: {
          start: {
            line: 50,
            column: 120
          },
          end: {
            line: 76,
            column: 5
          }
        },
        line: 50
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 80,
            column: 4
          },
          end: {
            line: 80,
            column: 5
          }
        },
        loc: {
          start: {
            line: 80,
            column: 118
          },
          end: {
            line: 124,
            column: 5
          }
        },
        line: 80
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 87,
            column: 73
          },
          end: {
            line: 87,
            column: 74
          }
        },
        loc: {
          start: {
            line: 87,
            column: 80
          },
          end: {
            line: 87,
            column: 122
          }
        },
        line: 87
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 89,
            column: 64
          },
          end: {
            line: 89,
            column: 65
          }
        },
        loc: {
          start: {
            line: 89,
            column: 78
          },
          end: {
            line: 89,
            column: 128
          }
        },
        line: 89
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 128,
            column: 4
          },
          end: {
            line: 128,
            column: 5
          }
        },
        loc: {
          start: {
            line: 128,
            column: 90
          },
          end: {
            line: 167,
            column: 5
          }
        },
        line: 128
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 171,
            column: 4
          },
          end: {
            line: 171,
            column: 5
          }
        },
        loc: {
          start: {
            line: 171,
            column: 97
          },
          end: {
            line: 229,
            column: 5
          }
        },
        line: 171
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 233,
            column: 4
          },
          end: {
            line: 233,
            column: 5
          }
        },
        loc: {
          start: {
            line: 233,
            column: 48
          },
          end: {
            line: 255,
            column: 5
          }
        },
        line: 233
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 250,
            column: 65
          },
          end: {
            line: 250,
            column: 66
          }
        },
        loc: {
          start: {
            line: 250,
            column: 79
          },
          end: {
            line: 250,
            column: 95
          }
        },
        line: 250
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 253,
            column: 50
          },
          end: {
            line: 253,
            column: 51
          }
        },
        loc: {
          start: {
            line: 253,
            column: 57
          },
          end: {
            line: 253,
            column: 82
          }
        },
        line: 253
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 259,
            column: 4
          },
          end: {
            line: 259,
            column: 5
          }
        },
        loc: {
          start: {
            line: 259,
            column: 44
          },
          end: {
            line: 307,
            column: 5
          }
        },
        line: 259
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 260,
            column: 15
          },
          end: {
            line: 260,
            column: 16
          }
        },
        loc: {
          start: {
            line: 260,
            column: 30
          },
          end: {
            line: 306,
            column: 9
          }
        },
        line: 260
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 311,
            column: 4
          },
          end: {
            line: 311,
            column: 5
          }
        },
        loc: {
          start: {
            line: 311,
            column: 46
          },
          end: {
            line: 326,
            column: 5
          }
        },
        line: 311
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 312,
            column: 39
          },
          end: {
            line: 312,
            column: 40
          }
        },
        loc: {
          start: {
            line: 312,
            column: 53
          },
          end: {
            line: 325,
            column: 9
          }
        },
        line: 312
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 313,
            column: 19
          },
          end: {
            line: 313,
            column: 20
          }
        },
        loc: {
          start: {
            line: 313,
            column: 34
          },
          end: {
            line: 324,
            column: 13
          }
        },
        line: 313
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 330,
            column: 4
          },
          end: {
            line: 330,
            column: 5
          }
        },
        loc: {
          start: {
            line: 330,
            column: 57
          },
          end: {
            line: 338,
            column: 5
          }
        },
        line: 330
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 342,
            column: 4
          },
          end: {
            line: 342,
            column: 5
          }
        },
        loc: {
          start: {
            line: 342,
            column: 51
          },
          end: {
            line: 363,
            column: 5
          }
        },
        line: 342
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 365,
            column: 4
          },
          end: {
            line: 365,
            column: 5
          }
        },
        loc: {
          start: {
            line: 365,
            column: 51
          },
          end: {
            line: 367,
            column: 5
          }
        },
        line: 365
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 368,
            column: 4
          },
          end: {
            line: 368,
            column: 5
          }
        },
        loc: {
          start: {
            line: 368,
            column: 54
          },
          end: {
            line: 370,
            column: 5
          }
        },
        line: 368
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 371,
            column: 4
          },
          end: {
            line: 371,
            column: 5
          }
        },
        loc: {
          start: {
            line: 371,
            column: 55
          },
          end: {
            line: 373,
            column: 5
          }
        },
        line: 371
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 374,
            column: 4
          },
          end: {
            line: 374,
            column: 5
          }
        },
        loc: {
          start: {
            line: 374,
            column: 57
          },
          end: {
            line: 376,
            column: 5
          }
        },
        line: 374
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 377,
            column: 4
          },
          end: {
            line: 377,
            column: 5
          }
        },
        loc: {
          start: {
            line: 377,
            column: 51
          },
          end: {
            line: 379,
            column: 5
          }
        },
        line: 377
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 383,
            column: 4
          },
          end: {
            line: 383,
            column: 5
          }
        },
        loc: {
          start: {
            line: 383,
            column: 75
          },
          end: {
            line: 389,
            column: 5
          }
        },
        line: 383
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 393,
            column: 4
          },
          end: {
            line: 393,
            column: 5
          }
        },
        loc: {
          start: {
            line: 393,
            column: 67
          },
          end: {
            line: 412,
            column: 5
          }
        },
        line: 393
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 416,
            column: 4
          },
          end: {
            line: 416,
            column: 5
          }
        },
        loc: {
          start: {
            line: 416,
            column: 68
          },
          end: {
            line: 424,
            column: 5
          }
        },
        line: 416
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 428,
            column: 4
          },
          end: {
            line: 428,
            column: 5
          }
        },
        loc: {
          start: {
            line: 428,
            column: 74
          },
          end: {
            line: 434,
            column: 5
          }
        },
        line: 428
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 436,
            column: 4
          },
          end: {
            line: 436,
            column: 5
          }
        },
        loc: {
          start: {
            line: 436,
            column: 49
          },
          end: {
            line: 439,
            column: 5
          }
        },
        line: 436
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 440,
            column: 4
          },
          end: {
            line: 440,
            column: 5
          }
        },
        loc: {
          start: {
            line: 440,
            column: 66
          },
          end: {
            line: 443,
            column: 5
          }
        },
        line: 440
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 447,
            column: 4
          },
          end: {
            line: 447,
            column: 5
          }
        },
        loc: {
          start: {
            line: 447,
            column: 71
          },
          end: {
            line: 450,
            column: 5
          }
        },
        line: 447
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 454,
            column: 4
          },
          end: {
            line: 454,
            column: 5
          }
        },
        loc: {
          start: {
            line: 454,
            column: 47
          },
          end: {
            line: 469,
            column: 5
          }
        },
        line: 454
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 471,
            column: 4
          },
          end: {
            line: 471,
            column: 5
          }
        },
        loc: {
          start: {
            line: 471,
            column: 57
          },
          end: {
            line: 474,
            column: 5
          }
        },
        line: 471
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 475,
            column: 4
          },
          end: {
            line: 475,
            column: 5
          }
        },
        loc: {
          start: {
            line: 475,
            column: 50
          },
          end: {
            line: 481,
            column: 5
          }
        },
        line: 475
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 482,
            column: 4
          },
          end: {
            line: 482,
            column: 5
          }
        },
        loc: {
          start: {
            line: 482,
            column: 53
          },
          end: {
            line: 485,
            column: 5
          }
        },
        line: 482
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 486,
            column: 4
          },
          end: {
            line: 486,
            column: 5
          }
        },
        loc: {
          start: {
            line: 486,
            column: 62
          },
          end: {
            line: 489,
            column: 5
          }
        },
        line: 486
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 488,
            column: 15
          },
          end: {
            line: 488,
            column: 16
          }
        },
        loc: {
          start: {
            line: 488,
            column: 30
          },
          end: {
            line: 488,
            column: 31
          }
        },
        line: 488
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 490,
            column: 4
          },
          end: {
            line: 490,
            column: 5
          }
        },
        loc: {
          start: {
            line: 490,
            column: 41
          },
          end: {
            line: 493,
            column: 5
          }
        },
        line: 490
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 492,
            column: 15
          },
          end: {
            line: 492,
            column: 16
          }
        },
        loc: {
          start: {
            line: 492,
            column: 30
          },
          end: {
            line: 492,
            column: 31
          }
        },
        line: 492
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 495,
            column: 4
          },
          end: {
            line: 495,
            column: 5
          }
        },
        loc: {
          start: {
            line: 495,
            column: 43
          },
          end: {
            line: 497,
            column: 5
          }
        },
        line: 495
      },
      "38": {
        name: "(anonymous_38)",
        decl: {
          start: {
            line: 498,
            column: 4
          },
          end: {
            line: 498,
            column: 5
          }
        },
        loc: {
          start: {
            line: 498,
            column: 109
          },
          end: {
            line: 501,
            column: 5
          }
        },
        line: 498
      },
      "39": {
        name: "(anonymous_39)",
        decl: {
          start: {
            line: 502,
            column: 4
          },
          end: {
            line: 502,
            column: 5
          }
        },
        loc: {
          start: {
            line: 502,
            column: 64
          },
          end: {
            line: 505,
            column: 5
          }
        },
        line: 502
      },
      "40": {
        name: "(anonymous_40)",
        decl: {
          start: {
            line: 506,
            column: 4
          },
          end: {
            line: 506,
            column: 5
          }
        },
        loc: {
          start: {
            line: 506,
            column: 56
          },
          end: {
            line: 519,
            column: 5
          }
        },
        line: 506
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 50,
            column: 41
          },
          end: {
            line: 50,
            column: 118
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 50,
            column: 53
          },
          end: {
            line: 50,
            column: 118
          }
        }],
        line: 50
      },
      "1": {
        loc: {
          start: {
            line: 80,
            column: 49
          },
          end: {
            line: 80,
            column: 116
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 80,
            column: 61
          },
          end: {
            line: 80,
            column: 116
          }
        }],
        line: 80
      },
      "2": {
        loc: {
          start: {
            line: 81,
            column: 8
          },
          end: {
            line: 83,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 81,
            column: 8
          },
          end: {
            line: 83,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 81
      },
      "3": {
        loc: {
          start: {
            line: 234,
            column: 8
          },
          end: {
            line: 236,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 234,
            column: 8
          },
          end: {
            line: 236,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 234
      },
      "4": {
        loc: {
          start: {
            line: 234,
            column: 12
          },
          end: {
            line: 234,
            column: 55
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 234,
            column: 12
          },
          end: {
            line: 234,
            column: 23
          }
        }, {
          start: {
            line: 234,
            column: 27
          },
          end: {
            line: 234,
            column: 55
          }
        }],
        line: 234
      },
      "5": {
        loc: {
          start: {
            line: 237,
            column: 8
          },
          end: {
            line: 239,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 237,
            column: 8
          },
          end: {
            line: 239,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 237
      },
      "6": {
        loc: {
          start: {
            line: 240,
            column: 8
          },
          end: {
            line: 242,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 240,
            column: 8
          },
          end: {
            line: 242,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 240
      },
      "7": {
        loc: {
          start: {
            line: 245,
            column: 12
          },
          end: {
            line: 247,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 245,
            column: 12
          },
          end: {
            line: 247,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 245
      },
      "8": {
        loc: {
          start: {
            line: 245,
            column: 16
          },
          end: {
            line: 245,
            column: 86
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 245,
            column: 16
          },
          end: {
            line: 245,
            column: 32
          }
        }, {
          start: {
            line: 245,
            column: 36
          },
          end: {
            line: 245,
            column: 86
          }
        }],
        line: 245
      },
      "9": {
        loc: {
          start: {
            line: 251,
            column: 8
          },
          end: {
            line: 254,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 251,
            column: 8
          },
          end: {
            line: 254,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 251
      },
      "10": {
        loc: {
          start: {
            line: 279,
            column: 20
          },
          end: {
            line: 297,
            column: 21
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 280,
            column: 24
          },
          end: {
            line: 282,
            column: 34
          }
        }, {
          start: {
            line: 283,
            column: 24
          },
          end: {
            line: 285,
            column: 34
          }
        }, {
          start: {
            line: 286,
            column: 24
          },
          end: {
            line: 288,
            column: 34
          }
        }, {
          start: {
            line: 289,
            column: 24
          },
          end: {
            line: 291,
            column: 34
          }
        }, {
          start: {
            line: 292,
            column: 24
          },
          end: {
            line: 294,
            column: 34
          }
        }, {
          start: {
            line: 295,
            column: 24
          },
          end: {
            line: 296,
            column: 85
          }
        }],
        line: 279
      },
      "11": {
        loc: {
          start: {
            line: 344,
            column: 8
          },
          end: {
            line: 362,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 345,
            column: 12
          },
          end: {
            line: 347,
            column: 22
          }
        }, {
          start: {
            line: 348,
            column: 12
          },
          end: {
            line: 350,
            column: 22
          }
        }, {
          start: {
            line: 351,
            column: 12
          },
          end: {
            line: 353,
            column: 22
          }
        }, {
          start: {
            line: 354,
            column: 12
          },
          end: {
            line: 356,
            column: 22
          }
        }, {
          start: {
            line: 357,
            column: 12
          },
          end: {
            line: 359,
            column: 22
          }
        }, {
          start: {
            line: 360,
            column: 12
          },
          end: {
            line: 361,
            column: 72
          }
        }],
        line: 344
      },
      "12": {
        loc: {
          start: {
            line: 455,
            column: 8
          },
          end: {
            line: 468,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 456,
            column: 12
          },
          end: {
            line: 456,
            column: 83
          }
        }, {
          start: {
            line: 457,
            column: 12
          },
          end: {
            line: 457,
            column: 84
          }
        }, {
          start: {
            line: 458,
            column: 12
          },
          end: {
            line: 459,
            column: 45
          }
        }, {
          start: {
            line: 460,
            column: 12
          },
          end: {
            line: 461,
            column: 47
          }
        }, {
          start: {
            line: 462,
            column: 12
          },
          end: {
            line: 463,
            column: 42
          }
        }, {
          start: {
            line: 464,
            column: 12
          },
          end: {
            line: 465,
            column: 44
          }
        }, {
          start: {
            line: 466,
            column: 12
          },
          end: {
            line: 467,
            column: 84
          }
        }],
        line: 455
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0
    },
    b: {
      "0": [0],
      "1": [0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0, 0, 0, 0, 0],
      "11": [0, 0, 0, 0, 0, 0],
      "12": [0, 0, 0, 0, 0, 0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\SystemOptimizationEngine.ts",
      mappings: ";AAAA;;;;;;;;;;;;GAYG;;;AAEH,6EAgByC;AAEzC,kDAAkD;AAClD,yEAAsE;AAKtE,iCAAiC;AACjC,oEAAiE;AACjE,wEAAqE;AACrE,sFAAmF;AACnF,kEAA+D;AAC/D,+FAA4F;AAE5F;;;GAGG;AACH,MAAa,wBAAwB;IAenC;;OAEG;IACI,MAAM,CAAC,UAAU;QACtB,IAAI,CAAC;YACH,IAAI,CAAC,gBAAgB,GAAG,IAAI,mCAAgB,EAAE,CAAC;YAC/C,IAAI,CAAC,kBAAkB,GAAG,IAAI,uCAAkB,EAAE,CAAC;YACnD,IAAI,CAAC,aAAa,GAAG,IAAI,qDAAyB,EAAE,CAAC;YACrD,IAAI,CAAC,eAAe,GAAG,IAAI,iCAAe,EAAE,CAAC;YAE7C,OAAO,CAAC,GAAG,CAAC,6BAA6B,IAAI,CAAC,OAAO,2BAA2B,CAAC,CAAC;QACpF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,gDAAgD,EAAE,KAAK,CAAC,CAAC;YACvE,MAAM,IAAI,KAAK,CAAC,8CAA8C,KAAK,EAAE,CAAC,CAAC;QACzE,CAAC;IACH,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,cAAc,CAChC,OAA4B,EAC5B,YAAmC,+CAAqB,CAAC,iBAAiB;QAE1E,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QAEpC,IAAI,CAAC;YACH,gCAAgC;YAChC,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC,CAAC;YAE1C,mCAAmC;YACnC,MAAM,cAAc,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;YAE5D,4BAA4B;YAC5B,MAAM,iBAAiB,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;YAEhE,8BAA8B;YAC9B,MAAM,mBAAmB,GAAG,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC;YAEpE,8CAA8C;YAC9C,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YAE3D,mBAAmB;YACnB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CACvC,SAAS,EACT,iBAAiB,EACjB,mBAAmB,EACnB,OAAO,EACP,cAAc,CACf,CAAC;YAEF,mCAAmC;YACnC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;YAE9E,6BAA6B;YAC7B,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,cAAc,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC;YAClE,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,cAAc,EAAE,WAAW,CAAC,UAAU,CAAC,CAAC;YAEpE,OAAO,WAAW,CAAC;QAErB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;YAC7C,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,KAAc,EAAE,SAAS,CAAC,CAAC;QACpE,CAAC;IACH,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,sBAAsB,CACxC,OAA4B,EAC5B,YAAmC,+CAAqB,CAAC,OAAO;QAGhE,IAAI,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC7C,MAAM,IAAI,KAAK,CAAC,6DAA6D,CAAC,CAAC;QACjF,CAAC;QAED,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,sDAAsD,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;YAEhF,6BAA6B;YAC7B,MAAM,kBAAkB,GAAG,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CACjE,IAAI,CAAC,uBAAuB,CAAC,GAAG,EAAE,OAAO,CAAC,CAC3C,CAAC;YAEF,8BAA8B;YAC9B,MAAM,mBAAmB,GAAG,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAC/D,IAAI,CAAC,wBAAwB,CAAC,UAAU,EAAE,OAAO,CAAC,CACnD,CAAC;YAEF,6CAA6C;YAC7C,MAAM,WAAW,GAAG,IAAI,yEAAmC,CAAC;gBAC1D,SAAS,EAAE,OAAO;gBAClB,cAAc,EAAE,GAAG;gBACnB,cAAc,EAAE,GAAG;gBACnB,aAAa,EAAE,GAAG;gBAClB,YAAY,EAAE,GAAG;gBACjB,SAAS,EAAE,EAAE;gBACb,kBAAkB,EAAE,SAAS;gBAC7B,kBAAkB,EAAE,IAAI;gBACxB,cAAc,EAAE;oBACd,YAAY,EAAE,GAAG;oBACjB,kBAAkB,EAAE,IAAI;oBACxB,oBAAoB,EAAE,IAAI;oBAC1B,WAAW,EAAE;wBACX,OAAO,EAAE,IAAI;wBACb,cAAc,EAAE,EAAE;qBACnB;oBACD,OAAO,EAAE;wBACP,OAAO,EAAE,IAAI;wBACb,aAAa,EAAE,GAAG;qBACnB;iBACF;gBACD,oBAAoB,EAAE,IAAI;gBAC1B,WAAW,EAAE,GAAG;aACjB,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,WAAW,CAAC,sBAAsB,CAAC,OAAO,EAAE,kBAAkB,EAAE,mBAAmB,CAAC,CAAC;YAE1G,OAAO,CAAC,GAAG,CAAC,uDAAuD,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;YACjF,OAAO,MAAM,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;YAC7D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,qBAAqB,CACvC,mBAAwC,EACxC,eAA6C,EAC7C,WAAqC;QAGrC,mDAAmD;QACnD,MAAM,OAAO,GAAwB;YACnC,EAAE,EAAE,WAAW,mBAAmB,CAAC,EAAE,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE;YACrD,IAAI,EAAE,iCAAiC,mBAAmB,CAAC,IAAI,EAAE;YACjE,WAAW,EAAE,yEAAyE;YACtF,mBAAmB;YACnB,SAAS,EAAE,IAAI,CAAC,wBAAwB,CAAC,mBAAmB,EAAE,eAAe,CAAC;YAC9E,UAAU,EAAE,IAAI,CAAC,yBAAyB,CAAC,eAAe,CAAC;YAC3D,WAAW;YACX,iBAAiB,EAAE;gBACjB,SAAS,EAAE,+CAAqB,CAAC,iBAAiB;gBAClD,UAAU,EAAE;oBACV,cAAc,EAAE,EAAE;oBAClB,aAAa,EAAE,GAAG;oBAClB,aAAa,EAAE,GAAG;oBAClB,YAAY,EAAE,GAAG;iBAClB;gBACD,eAAe,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE;aACnC;YACD,mBAAmB,EAAE;gBACnB,aAAa,EAAE,GAAG;gBAClB,cAAc,EAAE,IAAI;gBACpB,eAAe,EAAE,EAAE;gBACnB,oBAAoB,EAAE,KAAK;aAC5B;YACD,kBAAkB,EAAE;gBAClB,cAAc,EAAE,IAAI;gBACpB,gBAAgB,EAAE,IAAI;gBACtB,mBAAmB,EAAE,IAAI;gBACzB,mBAAmB,EAAE,KAAK;gBAC1B,cAAc,EAAE;oBACd,EAAE,IAAI,EAAE,aAAa,EAAE,UAAU,EAAE,EAAE,EAAE;oBACvC,EAAE,IAAI,EAAE,kBAAkB,EAAE,UAAU,EAAE,EAAE,EAAE;iBAC7C;gBACD,YAAY,EAAE,MAAM;aACrB;SACF,CAAC;QAEF,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,+CAAqB,CAAC,iBAAiB,CAAC,CAAC;IAC/E,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,wBAAwB,CAC1C,mBAAwC,EACxC,mBAAwB,EACxB,WAAqC;QAGrC,MAAM,OAAO,GAAwB;YACnC,EAAE,EAAE,UAAU,mBAAmB,CAAC,EAAE,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE;YACpD,IAAI,EAAE,oCAAoC,mBAAmB,CAAC,IAAI,EAAE;YACpE,WAAW,EAAE,8DAA8D;YAC3E,mBAAmB;YACnB,SAAS,EAAE,IAAI,CAAC,iCAAiC,CAAC,mBAAmB,CAAC;YACtE,UAAU,EAAE;gBACV,UAAU,EAAE;oBACV;wBACE,EAAE,EAAE,oBAAoB;wBACxB,SAAS,EAAE,+CAAqB,CAAC,2BAA2B;wBAC5D,MAAM,EAAE,GAAG;wBACX,WAAW,EAAE,0CAA0C;wBACvD,kBAAkB,EAAE,IAAI,CAAC,6BAA6B,CAAC,mBAAmB,CAAC;wBAC3E,KAAK,EAAE,UAAU;qBAClB;oBACD;wBACE,EAAE,EAAE,YAAY;wBAChB,SAAS,EAAE,+CAAqB,CAAC,mBAAmB;wBACpD,MAAM,EAAE,GAAG;wBACX,WAAW,EAAE,kDAAkD;wBAC/D,kBAAkB,EAAE,IAAI,CAAC,2BAA2B,EAAE;wBACtD,KAAK,EAAE,KAAK;qBACb;iBACF;gBACD,iBAAiB,EAAE,cAAc;aAClC;YACD,WAAW;YACX,iBAAiB,EAAE;gBACjB,SAAS,EAAE,+CAAqB,CAAC,cAAc;gBAC/C,UAAU,EAAE;oBACV,cAAc,EAAE,EAAE;oBAClB,aAAa,EAAE,GAAG;oBAClB,aAAa,EAAE,GAAG;oBAClB,wBAAwB,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;iBACrC;gBACD,eAAe,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE;aACnC;YACD,mBAAmB,EAAE;gBACnB,aAAa,EAAE,GAAG;gBAClB,cAAc,EAAE,KAAK;gBACrB,eAAe,EAAE,EAAE;gBACnB,oBAAoB,EAAE,KAAK;aAC5B;YACD,kBAAkB,EAAE;gBAClB,cAAc,EAAE,IAAI;gBACpB,gBAAgB,EAAE,IAAI;gBACtB,mBAAmB,EAAE,IAAI;gBACzB,mBAAmB,EAAE,IAAI;gBACzB,cAAc,EAAE;oBACd,EAAE,IAAI,EAAE,aAAa,EAAE,UAAU,EAAE,EAAE,EAAE;oBACvC,EAAE,IAAI,EAAE,cAAc,EAAE,UAAU,EAAE,EAAE,EAAE;iBACzC;gBACD,YAAY,EAAE,MAAM;aACrB;SACF,CAAC;QAEF,OAAO,IAAI,CAAC,sBAAsB,CAAC,OAAO,EAAE,+CAAqB,CAAC,cAAc,CAAC,CAAC;IACpF,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,2BAA2B,CAAC,OAA4B;QACrE,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE,CAAC;YAChD,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;QAC3E,CAAC;QAED,IAAI,OAAO,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACnC,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;QAC1E,CAAC;QAED,IAAI,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC/C,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;QAC3E,CAAC;QAED,2BAA2B;QAC3B,KAAK,MAAM,QAAQ,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACzC,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,MAAM,CAAC,OAAO,IAAI,QAAQ,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBAC3E,MAAM,IAAI,KAAK,CAAC,+BAA+B,QAAQ,CAAC,EAAE,EAAE,CAAC,CAAC;YAChE,CAAC;QACH,CAAC;QAED,6BAA6B;QAC7B,MAAM,WAAW,GAAG,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAC5F,IAAI,IAAI,CAAC,GAAG,CAAC,WAAW,GAAG,GAAG,CAAC,GAAG,IAAI,EAAE,CAAC;YACvC,OAAO,CAAC,IAAI,CAAC,4BAA4B,WAAW,sBAAsB,CAAC,CAAC;YAC5E,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,IAAI,WAAW,CAAC,CAAC;QAC1E,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,uBAAuB,CAAC,OAA4B;QACjE,OAAO,CAAC,SAAiC,EAAU,EAAE;YACnD,IAAI,CAAC;gBACH,0CAA0C;gBAC1C,MAAM,gBAAgB,GAAG,IAAI,CAAC,sBAAsB,CAClD,OAAO,CAAC,mBAAmB,EAC3B,SAAS,CACV,CAAC;gBAEF,8EAA8E;gBAC9E,MAAM,iBAAiB,GAAG,mDAAwB,CAAC,+BAA+B,CAAC;oBACjF,QAAQ,EAAE,gBAAgB,CAAC,QAAQ;oBACnC,UAAU,EAAE,gBAAgB,CAAC,UAAU;oBACvC,gBAAgB,EAAE,gBAAgB,CAAC,gBAAgB;oBACnD,kBAAkB,EAAE;wBAClB,gBAAgB,EAAE,IAAI;wBACtB,eAAe,EAAE,IAAI;wBACrB,YAAY,EAAE,KAAK;qBACpB;iBACF,CAAC,CAAC;gBAEH,6BAA6B;gBAC7B,IAAI,mBAAmB,GAAG,CAAC,CAAC;gBAE5B,KAAK,MAAM,SAAS,IAAI,OAAO,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;oBACtD,IAAI,cAAsB,CAAC;oBAE3B,QAAQ,SAAS,CAAC,SAAS,EAAE,CAAC;wBAC5B,KAAK,+CAAqB,CAAC,sBAAsB;4BAC/C,cAAc,GAAG,iBAAiB,CAAC,iBAAiB,CAAC;4BACrD,MAAM;wBAER,KAAK,+CAAqB,CAAC,2BAA2B;4BACpD,cAAc,GAAG,IAAI,CAAC,0BAA0B,CAAC,iBAAiB,EAAE,gBAAgB,CAAC,CAAC;4BACtF,MAAM;wBAER,KAAK,+CAAqB,CAAC,mBAAmB;4BAC5C,cAAc,GAAG,IAAI,CAAC,kBAAkB,CAAC,iBAAiB,EAAE,gBAAgB,CAAC,CAAC;4BAC9E,MAAM;wBAER,KAAK,+CAAqB,CAAC,oBAAoB;4BAC7C,cAAc,GAAG,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,EAAE,gBAAgB,CAAC,CAAC;4BAC/E,MAAM;wBAER,KAAK,+CAAqB,CAAC,mBAAmB;4BAC5C,cAAc,GAAG,CAAC,IAAI,CAAC,yBAAyB,CAAC,iBAAiB,EAAE,gBAAgB,CAAC,CAAC;4BACtF,MAAM;wBAER;4BACE,cAAc,GAAG,SAAS,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;oBAC7D,CAAC;oBAED,mBAAmB,IAAI,SAAS,CAAC,MAAM,GAAG,cAAc,CAAC;gBAC3D,CAAC;gBAED,OAAO,mBAAmB,CAAC;YAE7B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAC;gBAChE,OAAO,MAAM,CAAC,SAAS,CAAC,CAAC,6CAA6C;YACxE,CAAC;QACH,CAAC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,yBAAyB,CAAC,OAA4B;QACnE,OAAO,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;YAC1C,OAAO,CAAC,SAAiC,EAAU,EAAE;gBACnD,IAAI,CAAC;oBACH,0CAA0C;oBAC1C,MAAM,gBAAgB,GAAG,IAAI,CAAC,sBAAsB,CAClD,OAAO,CAAC,mBAAmB,EAC3B,SAAS,CACV,CAAC;oBAEF,oCAAoC;oBACpC,OAAO,IAAI,CAAC,kBAAkB,CAAC,UAAU,EAAE,gBAAgB,EAAE,SAAS,CAAC,CAAC;gBAE1E,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,KAAK,CAAC,+BAA+B,UAAU,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;oBACtE,OAAO,MAAM,CAAC,SAAS,CAAC,CAAC,iDAAiD;gBAC5E,CAAC;YACH,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,sBAAsB,CACnC,UAA+B,EAC/B,SAAiC;QAEjC,sCAAsC;QACtC,MAAM,gBAAgB,GAAwB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;QAErF,0DAA0D;QAC1D,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;YACjC,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,EAAE,QAAQ,CAAC,CAAC;QACzD,CAAC;QAED,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,qBAAqB,CAClC,MAA2B,EAC3B,QAA8B;QAE9B,0CAA0C;QAC1C,QAAQ,QAAQ,CAAC,IAAI,EAAE,CAAC;YACtB,KAAK,WAAW;gBACd,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;gBAC7C,MAAM;YACR,KAAK,cAAc;gBACjB,IAAI,CAAC,wBAAwB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;gBAChD,MAAM;YACR,KAAK,eAAe;gBAClB,IAAI,CAAC,yBAAyB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;gBACjD,MAAM;YACR,KAAK,iBAAiB;gBACpB,IAAI,CAAC,2BAA2B,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;gBACnD,MAAM;YACR,KAAK,WAAW;gBACd,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;gBAC7C,MAAM;YACR;gBACE,OAAO,CAAC,IAAI,CAAC,0BAA0B,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;IAED,kFAAkF;IAC1E,MAAM,CAAC,qBAAqB,CAAC,MAA2B,EAAE,QAA8B;QAC9F,yCAAyC;IAC3C,CAAC;IAEO,MAAM,CAAC,wBAAwB,CAAC,MAA2B,EAAE,QAA8B;QACjG,4CAA4C;IAC9C,CAAC;IAEO,MAAM,CAAC,yBAAyB,CAAC,MAA2B,EAAE,QAA8B;QAClG,6CAA6C;IAC/C,CAAC;IAEO,MAAM,CAAC,2BAA2B,CAAC,MAA2B,EAAE,QAA8B;QACpG,+CAA+C;IACjD,CAAC;IAEO,MAAM,CAAC,qBAAqB,CAAC,MAA2B,EAAE,QAA8B;QAC9F,yCAAyC;IAC3C,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,0BAA0B,CACvC,iBAAsB,EACtB,gBAAqC;QAErC,kGAAkG;QAClG,MAAM,QAAQ,GAAG,iBAAiB,CAAC,iBAAiB,GAAG,iBAAiB,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC,gBAAgB;QAC3G,MAAM,oBAAoB,GAAG,IAAI,CAAC,CAAC,iBAAiB;QACpD,MAAM,iBAAiB,GAAG,QAAQ,GAAG,KAAK,GAAG,oBAAoB,CAAC,CAAC,sBAAsB;QAEzF,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,kBAAkB,CAC/B,iBAAsB,EACtB,gBAAqC;QAErC,yEAAyE;QACzE,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,IAAI,gBAAgB,GAAG,CAAC,CAAC;QACzB,IAAI,aAAa,GAAG,CAAC,CAAC;QAEtB,2BAA2B;QAC3B,KAAK,MAAM,OAAO,IAAI,gBAAgB,CAAC,QAAQ,EAAE,CAAC;YAChD,YAAY,IAAI,IAAI,CAAC,4BAA4B,CAAC,OAAO,CAAC,CAAC;QAC7D,CAAC;QAED,gEAAgE;QAChE,gBAAgB,GAAG,YAAY,GAAG,IAAI,CAAC;QAEvC,mCAAmC;QACnC,MAAM,iBAAiB,GAAG,IAAI,CAAC,0BAA0B,CAAC,iBAAiB,EAAE,gBAAgB,CAAC,CAAC;QAC/F,MAAM,UAAU,GAAG,IAAI,CAAC,CAAC,QAAQ;QACjC,aAAa,GAAG,iBAAiB,GAAG,UAAU,CAAC;QAE/C,oCAAoC;QACpC,MAAM,cAAc,GAAG,EAAE,CAAC;QAC1B,MAAM,SAAS,GAAG,YAAY,GAAG,gBAAgB,GAAG,CAAC,aAAa,GAAG,cAAc,CAAC,CAAC;QAErF,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,mBAAmB,CAChC,iBAAsB,EACtB,gBAAqC;QAErC,8EAA8E;QAC9E,IAAI,eAAe,GAAG,CAAC,CAAC;QAExB,KAAK,MAAM,OAAO,IAAI,gBAAgB,CAAC,QAAQ,EAAE,CAAC;YAChD,MAAM,YAAY,GAAG,IAAI,CAAC,0BAA0B,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC;YACjF,eAAe,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,YAAY,GAAG,EAAE,CAAC,CAAC,CAAC,0CAA0C;QAChG,CAAC;QAED,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC,qBAAqB;IAChE,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,yBAAyB,CACtC,iBAAsB,EACtB,gBAAqC;QAErC,oCAAoC;QACpC,MAAM,0BAA0B,GAAG,iBAAiB,CAAC,SAAS,GAAG,GAAG,CAAC,CAAC,4BAA4B;QAClG,MAAM,cAAc,GAAG,iBAAiB,CAAC,iBAAiB,CAAC;QAC3D,MAAM,UAAU,GAAG,0BAA0B,GAAG,cAAc,CAAC;QAE/D,OAAO,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC,CAAC,yBAAyB;IAC7D,CAAC;IAED,iDAAiD;IACzC,MAAM,CAAC,4BAA4B,CAAC,OAAY;QACtD,6BAA6B;QAC7B,OAAO,GAAG,CAAC,CAAC,wBAAwB;IACtC,CAAC;IAEO,MAAM,CAAC,0BAA0B,CAAC,OAAY,EAAE,iBAAsB;QAC5E,6BAA6B;QAC7B,OAAO,EAAE,CAAC,CAAC,yBAAyB;IACtC,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,kBAAkB,CAC/B,UAAkC,EAClC,gBAAqC,EACrC,SAAiC;QAEjC,mFAAmF;QACnF,OAAO,CAAC,CAAC,CAAC,8DAA8D;IAC1E,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,eAAe,CAAC,SAAgC,EAAE,OAA4B;QAC3F,QAAQ,SAAS,EAAE,CAAC;YAClB,KAAK,+CAAqB,CAAC,iBAAiB,CAAC;YAC7C,KAAK,+CAAqB,CAAC,kBAAkB,CAAC;YAC9C,KAAK,+CAAqB,CAAC,OAAO;gBAChC,OAAO,IAAI,CAAC,gBAAgB,CAAC;YAC/B,KAAK,+CAAqB,CAAC,mBAAmB;gBAC5C,OAAO,IAAI,CAAC,kBAAkB,CAAC;YACjC,KAAK,+CAAqB,CAAC,cAAc;gBACvC,OAAO,IAAI,CAAC,aAAa,CAAC;YAC5B,KAAK,+CAAqB,CAAC,gBAAgB;gBACzC,OAAO,IAAI,CAAC,eAAe,CAAC;YAC9B;gBACE,MAAM,IAAI,KAAK,CAAC,uCAAuC,SAAS,EAAE,CAAC,CAAC;QACxE,CAAC;IACH,CAAC;IAED,kEAAkE;IAC1D,MAAM,CAAC,wBAAwB,CACrC,MAA2B,EAC3B,WAAyC;QAEzC,6BAA6B;QAC7B,OAAO,EAAE,CAAC;IACZ,CAAC;IAEO,MAAM,CAAC,yBAAyB,CAAC,WAAyC;QAChF,6BAA6B;QAC7B,OAAO;YACL,UAAU,EAAE,EAAE;YACd,iBAAiB,EAAE,cAAc;SAClC,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,iCAAiC,CAAC,MAA2B;QAC1E,6BAA6B;QAC7B,OAAO,EAAE,CAAC;IACZ,CAAC;IAEO,MAAM,CAAC,6BAA6B,CAAC,mBAAwB;QACnE,6BAA6B;QAC7B,OAAO,CAAC,SAAiC,EAAE,EAAE,CAAC,CAAC,CAAC;IAClD,CAAC;IAEO,MAAM,CAAC,2BAA2B;QACxC,6BAA6B;QAC7B,OAAO,CAAC,SAAiC,EAAE,EAAE,CAAC,CAAC,CAAC;IAClD,CAAC;IAED,kBAAkB;IACV,MAAM,CAAC,sBAAsB,CAAC,OAA4B;QAChE,OAAO,OAAO,OAAO,CAAC,EAAE,IAAI,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IACtF,CAAC;IAEO,MAAM,CAAC,KAAK,CAAC,eAAe,CAClC,SAAc,EACd,iBAAwC,EACxC,mBAA6C,EAC7C,OAA4B,EAC5B,cAAsB;QAEtB,8EAA8E;QAC9E,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;IAChE,CAAC;IAEO,MAAM,CAAC,KAAK,CAAC,kBAAkB,CACrC,MAA0B,EAC1B,OAA4B,EAC5B,SAAiB;QAEjB,6BAA6B;QAC7B,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,MAAM,CAAC,iBAAiB,CAC9B,OAA4B,EAC5B,KAAY,EACZ,SAAiB;QAEjB,6BAA6B;QAC7B,OAAO;YACL,SAAS,EAAE,OAAO,CAAC,EAAE;YACrB,MAAM,EAAE,4CAAkB,CAAC,KAAK;YAChC,YAAY,EAAE,EAA0B;YACxC,UAAU,EAAE,EAA4B;YACxC,OAAO,EAAE,EAAyB;YAClC,QAAQ,EAAE,EAAoB;YAC9B,eAAe,EAAE,EAAE;YACnB,QAAQ,EAAE,EAAE;YACZ,MAAM,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC;SACxB,CAAC;IACJ,CAAC;;AA1oBH,4DA2oBC;AA1oByB,gCAAO,GAAG,OAAO,CAAC;AAClB,+CAAsB,GAAG,IAAI,CAAC;AAC9B,sDAA6B,GAAG,IAAI,CAAC;AAQ7D,uBAAuB;AACR,4CAAmB,GAAqC,IAAI,GAAG,EAAE,CAAC;AAClE,2CAAkB,GAAwC,IAAI,GAAG,EAAE,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\SystemOptimizationEngine.ts"],
      sourcesContent: ["/**\r\n * System Optimization Engine for Phase 3 Priority 2\r\n * \r\n * Main optimization engine providing comprehensive system optimization capabilities including:\r\n * - Multi-objective optimization with Pareto analysis\r\n * - Multiple optimization algorithms (GA, SA, PSO, GD)\r\n * - Constraint handling and validation\r\n * - Performance analysis and recommendations\r\n * - Integration with existing Phase 1/2/3 Priority 1 components\r\n * \r\n * @version 3.0.0\r\n * <AUTHOR> Suite Development Team\r\n */\r\n\r\nimport {\r\n  OptimizationProblem,\r\n  OptimizationResult,\r\n  OptimizationSolution,\r\n  OptimizationAlgorithm,\r\n  OptimizationObjective,\r\n  OptimizationStatus,\r\n  OptimizationVariable,\r\n  OptimizationConstraint,\r\n  SystemConfiguration,\r\n  MultiObjectiveFunction,\r\n  OptimizationStatistics,\r\n  OptimizationHistory,\r\n  ResultAnalysis,\r\n  ObjectiveFunctionType,\r\n  ConstraintFunctionType\r\n} from './types/SystemOptimizationTypes';\r\n\r\n// Import existing Phase 1/2/3 Priority 1 services\r\nimport { SystemPressureCalculator } from './SystemPressureCalculator';\r\nimport { FittingLossCalculator } from './FittingLossCalculator';\r\nimport { AdvancedFittingCalculator } from './AdvancedFittingCalculator';\r\nimport { AirPropertiesCalculator } from './AirPropertiesCalculator';\r\n\r\n// Import optimization algorithms\r\nimport { GeneticAlgorithm } from './algorithms/GeneticAlgorithm';\r\nimport { SimulatedAnnealing } from './algorithms/SimulatedAnnealing';\r\nimport { ParticleSwarmOptimization } from './algorithms/ParticleSwarmOptimization';\r\nimport { GradientDescent } from './algorithms/GradientDescent';\r\nimport { MultiObjectiveOptimizationFramework } from './MultiObjectiveOptimizationFramework';\r\n\r\n/**\r\n * Main system optimization engine providing comprehensive optimization capabilities\r\n * for HVAC duct systems with multi-objective optimization and constraint handling\r\n */\r\nexport class SystemOptimizationEngine {\r\n  private static readonly VERSION = '3.0.0';\r\n  private static readonly MAX_ITERATIONS_DEFAULT = 1000;\r\n  private static readonly CONVERGENCE_TOLERANCE_DEFAULT = 1e-6;\r\n  \r\n  // Algorithm instances\r\n  private static geneticAlgorithm: GeneticAlgorithm;\r\n  private static simulatedAnnealing: SimulatedAnnealing;\r\n  private static particleSwarm: ParticleSwarmOptimization;\r\n  private static gradientDescent: GradientDescent;\r\n  \r\n  // Performance tracking\r\n  private static optimizationHistory: Map<string, OptimizationHistory> = new Map();\r\n  private static performanceMetrics: Map<string, OptimizationStatistics> = new Map();\r\n\r\n  /**\r\n   * Initialize the optimization engine with algorithm instances\r\n   */\r\n  public static initialize(): void {\r\n    try {\r\n      this.geneticAlgorithm = new GeneticAlgorithm();\r\n      this.simulatedAnnealing = new SimulatedAnnealing();\r\n      this.particleSwarm = new ParticleSwarmOptimization();\r\n      this.gradientDescent = new GradientDescent();\r\n      \r\n      console.log(`SystemOptimizationEngine v${this.VERSION} initialized successfully`);\r\n    } catch (error) {\r\n      console.error('Failed to initialize SystemOptimizationEngine:', error);\r\n      throw new Error(`Optimization engine initialization failed: ${error}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Main optimization method - solves optimization problems with specified algorithms\r\n   */\r\n  public static async optimizeSystem(\r\n    problem: OptimizationProblem,\r\n    algorithm: OptimizationAlgorithm = OptimizationAlgorithm.GENETIC_ALGORITHM\r\n  ): Promise<OptimizationResult> {\r\n    const startTime = performance.now();\r\n    \r\n    try {\r\n      // Validate optimization problem\r\n      this.validateOptimizationProblem(problem);\r\n      \r\n      // Initialize optimization tracking\r\n      const optimizationId = this.generateOptimizationId(problem);\r\n      \r\n      // Create objective function\r\n      const objectiveFunction = this.createObjectiveFunction(problem);\r\n      \r\n      // Create constraint functions\r\n      const constraintFunctions = this.createConstraintFunctions(problem);\r\n      \r\n      // Select and configure optimization algorithm\r\n      const optimizer = this.createOptimizer(algorithm, problem);\r\n      \r\n      // Run optimization\r\n      const result = await this.runOptimization(\r\n        optimizer,\r\n        objectiveFunction,\r\n        constraintFunctions,\r\n        problem,\r\n        optimizationId\r\n      );\r\n      \r\n      // Post-process and analyze results\r\n      const finalResult = await this.postProcessResults(result, problem, startTime);\r\n      \r\n      // Store optimization history\r\n      this.optimizationHistory.set(optimizationId, finalResult.history);\r\n      this.performanceMetrics.set(optimizationId, finalResult.statistics);\r\n      \r\n      return finalResult;\r\n      \r\n    } catch (error) {\r\n      console.error('Optimization failed:', error);\r\n      return this.createErrorResult(problem, error as Error, startTime);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Multi-objective optimization with Pareto analysis\r\n   */\r\n  public static async optimizeMultiObjective(\r\n    problem: OptimizationProblem,\r\n    algorithm: OptimizationAlgorithm = OptimizationAlgorithm.NSGA_II\r\n  ): Promise<OptimizationResult> {\r\n\r\n    if (problem.objectives.objectives.length < 2) {\r\n      throw new Error('Multi-objective optimization requires at least 2 objectives');\r\n    }\r\n\r\n    try {\r\n      console.log(`Starting multi-objective optimization for problem: ${problem.id}`);\r\n\r\n      // Create objective functions\r\n      const objectiveFunctions = problem.objectives.objectives.map(obj =>\r\n        this.createObjectiveFunction(obj, problem)\r\n      );\r\n\r\n      // Create constraint functions\r\n      const constraintFunctions = problem.constraints.map(constraint =>\r\n        this.createConstraintFunction(constraint, problem)\r\n      );\r\n\r\n      // Use Multi-objective Optimization Framework\r\n      const moFramework = new MultiObjectiveOptimizationFramework({\r\n        algorithm: 'nsga2',\r\n        populationSize: 100,\r\n        maxGenerations: 100,\r\n        crossoverRate: 0.9,\r\n        mutationRate: 0.1,\r\n        eliteSize: 10,\r\n        constraintHandling: 'penalty',\r\n        penaltyCoefficient: 1000,\r\n        paretoSettings: {\r\n          maxSolutions: 100,\r\n          diversityThreshold: 0.01,\r\n          convergenceThreshold: 1e-6,\r\n          hypervolume: {\r\n            enabled: true,\r\n            referencePoint: []\r\n          },\r\n          spacing: {\r\n            enabled: true,\r\n            targetSpacing: 0.1\r\n          }\r\n        },\r\n        diversityMaintenance: true,\r\n        archiveSize: 200\r\n      });\r\n\r\n      const result = await moFramework.optimizeMultiObjective(problem, objectiveFunctions, constraintFunctions);\r\n\r\n      console.log(`Multi-objective optimization completed for problem: ${problem.id}`);\r\n      return result;\r\n\r\n    } catch (error) {\r\n      console.error('Multi-objective optimization failed:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Optimize system balance for multi-zone HVAC systems\r\n   */\r\n  public static async optimizeSystemBalance(\r\n    systemConfiguration: SystemConfiguration,\r\n    targetFlowRates: { [zoneId: string]: number },\r\n    constraints: OptimizationConstraint[]\r\n  ): Promise<OptimizationResult> {\r\n    \r\n    // Create optimization problem for system balancing\r\n    const problem: OptimizationProblem = {\r\n      id: `balance_${systemConfiguration.id}_${Date.now()}`,\r\n      name: `System Balance Optimization - ${systemConfiguration.name}`,\r\n      description: 'Optimize damper positions and system configuration for balanced airflow',\r\n      systemConfiguration,\r\n      variables: this.createBalancingVariables(systemConfiguration, targetFlowRates),\r\n      objectives: this.createBalancingObjectives(targetFlowRates),\r\n      constraints,\r\n      algorithmSettings: {\r\n        algorithm: OptimizationAlgorithm.GENETIC_ALGORITHM,\r\n        parameters: {\r\n          populationSize: 50,\r\n          maxIterations: 200,\r\n          crossoverRate: 0.8,\r\n          mutationRate: 0.1\r\n        },\r\n        parallelization: { enabled: true }\r\n      },\r\n      convergenceCriteria: {\r\n        maxIterations: 200,\r\n        toleranceValue: 0.01,\r\n        stagnationLimit: 20,\r\n        improvementThreshold: 0.001\r\n      },\r\n      outputRequirements: {\r\n        includeHistory: true,\r\n        detailedAnalysis: true,\r\n        sensitivityAnalysis: true,\r\n        uncertaintyAnalysis: false,\r\n        visualizations: [\r\n          { type: 'convergence', parameters: {} },\r\n          { type: 'variable_history', parameters: {} }\r\n        ],\r\n        reportFormat: 'json'\r\n      }\r\n    };\r\n    \r\n    return this.optimizeSystem(problem, OptimizationAlgorithm.GENETIC_ALGORITHM);\r\n  }\r\n\r\n  /**\r\n   * Optimize for minimum energy consumption\r\n   */\r\n  public static async optimizeEnergyEfficiency(\r\n    systemConfiguration: SystemConfiguration,\r\n    operatingConditions: any,\r\n    constraints: OptimizationConstraint[]\r\n  ): Promise<OptimizationResult> {\r\n    \r\n    const problem: OptimizationProblem = {\r\n      id: `energy_${systemConfiguration.id}_${Date.now()}`,\r\n      name: `Energy Efficiency Optimization - ${systemConfiguration.name}`,\r\n      description: 'Optimize system configuration for minimum energy consumption',\r\n      systemConfiguration,\r\n      variables: this.createEnergyOptimizationVariables(systemConfiguration),\r\n      objectives: {\r\n        objectives: [\r\n          {\r\n            id: 'energy_consumption',\r\n            objective: OptimizationObjective.MINIMIZE_ENERGY_CONSUMPTION,\r\n            weight: 0.7,\r\n            description: 'Minimize total system energy consumption',\r\n            evaluationFunction: this.createEnergyObjectiveFunction(operatingConditions),\r\n            units: 'kWh/year'\r\n          },\r\n          {\r\n            id: 'total_cost',\r\n            objective: OptimizationObjective.MINIMIZE_TOTAL_COST,\r\n            weight: 0.3,\r\n            description: 'Minimize total system cost (initial + operating)',\r\n            evaluationFunction: this.createCostObjectiveFunction(),\r\n            units: 'USD'\r\n          }\r\n        ],\r\n        aggregationMethod: 'weighted_sum'\r\n      },\r\n      constraints,\r\n      algorithmSettings: {\r\n        algorithm: OptimizationAlgorithm.PARTICLE_SWARM,\r\n        parameters: {\r\n          populationSize: 40,\r\n          maxIterations: 300,\r\n          inertiaWeight: 0.9,\r\n          accelerationCoefficients: [2.0, 2.0]\r\n        },\r\n        parallelization: { enabled: true }\r\n      },\r\n      convergenceCriteria: {\r\n        maxIterations: 300,\r\n        toleranceValue: 0.005,\r\n        stagnationLimit: 30,\r\n        improvementThreshold: 0.001\r\n      },\r\n      outputRequirements: {\r\n        includeHistory: true,\r\n        detailedAnalysis: true,\r\n        sensitivityAnalysis: true,\r\n        uncertaintyAnalysis: true,\r\n        visualizations: [\r\n          { type: 'convergence', parameters: {} },\r\n          { type: 'pareto_front', parameters: {} }\r\n        ],\r\n        reportFormat: 'json'\r\n      }\r\n    };\r\n    \r\n    return this.optimizeMultiObjective(problem, OptimizationAlgorithm.PARTICLE_SWARM);\r\n  }\r\n\r\n  /**\r\n   * Validate optimization problem structure and constraints\r\n   */\r\n  private static validateOptimizationProblem(problem: OptimizationProblem): void {\r\n    if (!problem.id || !problem.systemConfiguration) {\r\n      throw new Error('Invalid optimization problem: missing required fields');\r\n    }\r\n    \r\n    if (problem.variables.length === 0) {\r\n      throw new Error('Optimization problem must have at least one variable');\r\n    }\r\n    \r\n    if (problem.objectives.objectives.length === 0) {\r\n      throw new Error('Optimization problem must have at least one objective');\r\n    }\r\n    \r\n    // Validate variable bounds\r\n    for (const variable of problem.variables) {\r\n      if (!variable.bounds || variable.bounds.minimum >= variable.bounds.maximum) {\r\n        throw new Error(`Invalid bounds for variable ${variable.id}`);\r\n      }\r\n    }\r\n    \r\n    // Validate objective weights\r\n    const totalWeight = problem.objectives.objectives.reduce((sum, obj) => sum + obj.weight, 0);\r\n    if (Math.abs(totalWeight - 1.0) > 1e-6) {\r\n      console.warn(`Objective weights sum to ${totalWeight}, normalizing to 1.0`);\r\n      problem.objectives.objectives.forEach(obj => obj.weight /= totalWeight);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Create objective function for optimization problem\r\n   */\r\n  private static createObjectiveFunction(problem: OptimizationProblem): ObjectiveFunctionType {\r\n    return (variables: OptimizationVariable[]): number => {\r\n      try {\r\n        // Apply variables to system configuration\r\n        const configuredSystem = this.applyVariablesToSystem(\r\n          problem.systemConfiguration,\r\n          variables\r\n        );\r\n        \r\n        // Calculate system performance using existing Phase 1/2/3 Priority 1 services\r\n        const systemPerformance = SystemPressureCalculator.calculateEnhancedSystemPressure({\r\n          segments: configuredSystem.segments,\r\n          systemType: configuredSystem.systemType,\r\n          designConditions: configuredSystem.designConditions,\r\n          calculationOptions: {\r\n            includeElevation: true,\r\n            includeFittings: true,\r\n            roundResults: false\r\n          }\r\n        });\r\n        \r\n        // Calculate objective values\r\n        let totalObjectiveValue = 0;\r\n        \r\n        for (const objective of problem.objectives.objectives) {\r\n          let objectiveValue: number;\r\n          \r\n          switch (objective.objective) {\r\n            case OptimizationObjective.MINIMIZE_PRESSURE_LOSS:\r\n              objectiveValue = systemPerformance.totalPressureLoss;\r\n              break;\r\n              \r\n            case OptimizationObjective.MINIMIZE_ENERGY_CONSUMPTION:\r\n              objectiveValue = this.calculateEnergyConsumption(systemPerformance, configuredSystem);\r\n              break;\r\n              \r\n            case OptimizationObjective.MINIMIZE_TOTAL_COST:\r\n              objectiveValue = this.calculateTotalCost(systemPerformance, configuredSystem);\r\n              break;\r\n              \r\n            case OptimizationObjective.MINIMIZE_NOISE_LEVEL:\r\n              objectiveValue = this.calculateNoiseLevel(systemPerformance, configuredSystem);\r\n              break;\r\n              \r\n            case OptimizationObjective.MAXIMIZE_EFFICIENCY:\r\n              objectiveValue = -this.calculateSystemEfficiency(systemPerformance, configuredSystem);\r\n              break;\r\n              \r\n            default:\r\n              objectiveValue = objective.evaluationFunction(variables);\r\n          }\r\n          \r\n          totalObjectiveValue += objective.weight * objectiveValue;\r\n        }\r\n        \r\n        return totalObjectiveValue;\r\n        \r\n      } catch (error) {\r\n        console.error('Error in objective function evaluation:', error);\r\n        return Number.MAX_VALUE; // Return large penalty for invalid solutions\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create constraint functions for optimization problem\r\n   */\r\n  private static createConstraintFunctions(problem: OptimizationProblem): ConstraintFunctionType[] {\r\n    return problem.constraints.map(constraint => {\r\n      return (variables: OptimizationVariable[]): number => {\r\n        try {\r\n          // Apply variables to system configuration\r\n          const configuredSystem = this.applyVariablesToSystem(\r\n            problem.systemConfiguration,\r\n            variables\r\n          );\r\n          \r\n          // Evaluate constraint based on type\r\n          return this.evaluateConstraint(constraint, configuredSystem, variables);\r\n          \r\n        } catch (error) {\r\n          console.error(`Error evaluating constraint ${constraint.id}:`, error);\r\n          return Number.MAX_VALUE; // Return large violation for invalid evaluations\r\n        }\r\n      };\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Apply optimization variables to system configuration\r\n   */\r\n  private static applyVariablesToSystem(\r\n    baseSystem: SystemConfiguration,\r\n    variables: OptimizationVariable[]\r\n  ): SystemConfiguration {\r\n    // Deep clone the system configuration\r\n    const configuredSystem: SystemConfiguration = JSON.parse(JSON.stringify(baseSystem));\r\n    \r\n    // Apply each variable to the appropriate system component\r\n    for (const variable of variables) {\r\n      this.applyVariableToSystem(configuredSystem, variable);\r\n    }\r\n    \r\n    return configuredSystem;\r\n  }\r\n\r\n  /**\r\n   * Apply a single variable to system configuration\r\n   */\r\n  private static applyVariableToSystem(\r\n    system: SystemConfiguration,\r\n    variable: OptimizationVariable\r\n  ): void {\r\n    // Implementation depends on variable type\r\n    switch (variable.type) {\r\n      case 'duct_size':\r\n        this.applyDuctSizeVariable(system, variable);\r\n        break;\r\n      case 'fitting_type':\r\n        this.applyFittingTypeVariable(system, variable);\r\n        break;\r\n      case 'material_type':\r\n        this.applyMaterialTypeVariable(system, variable);\r\n        break;\r\n      case 'damper_position':\r\n        this.applyDamperPositionVariable(system, variable);\r\n        break;\r\n      case 'fan_speed':\r\n        this.applyFanSpeedVariable(system, variable);\r\n        break;\r\n      default:\r\n        console.warn(`Unknown variable type: ${variable.type}`);\r\n    }\r\n  }\r\n\r\n  // Variable application methods (to be implemented based on specific requirements)\r\n  private static applyDuctSizeVariable(system: SystemConfiguration, variable: OptimizationVariable): void {\r\n    // Implementation for duct size variables\r\n  }\r\n\r\n  private static applyFittingTypeVariable(system: SystemConfiguration, variable: OptimizationVariable): void {\r\n    // Implementation for fitting type variables\r\n  }\r\n\r\n  private static applyMaterialTypeVariable(system: SystemConfiguration, variable: OptimizationVariable): void {\r\n    // Implementation for material type variables\r\n  }\r\n\r\n  private static applyDamperPositionVariable(system: SystemConfiguration, variable: OptimizationVariable): void {\r\n    // Implementation for damper position variables\r\n  }\r\n\r\n  private static applyFanSpeedVariable(system: SystemConfiguration, variable: OptimizationVariable): void {\r\n    // Implementation for fan speed variables\r\n  }\r\n\r\n  /**\r\n   * Calculate energy consumption for system configuration\r\n   */\r\n  private static calculateEnergyConsumption(\r\n    systemPerformance: any,\r\n    configuredSystem: SystemConfiguration\r\n  ): number {\r\n    // Simplified energy calculation - to be enhanced with detailed fan curves and operating schedules\r\n    const fanPower = systemPerformance.totalPressureLoss * systemPerformance.totalFlow / 6356; // Convert to HP\r\n    const annualOperatingHours = 8760; // Hours per year\r\n    const energyConsumption = fanPower * 0.746 * annualOperatingHours; // Convert to kWh/year\r\n    \r\n    return energyConsumption;\r\n  }\r\n\r\n  /**\r\n   * Calculate total cost for system configuration\r\n   */\r\n  private static calculateTotalCost(\r\n    systemPerformance: any,\r\n    configuredSystem: SystemConfiguration\r\n  ): number {\r\n    // Simplified cost calculation - to be enhanced with detailed cost models\r\n    let materialCost = 0;\r\n    let installationCost = 0;\r\n    let operatingCost = 0;\r\n    \r\n    // Calculate material costs\r\n    for (const segment of configuredSystem.segments) {\r\n      materialCost += this.calculateSegmentMaterialCost(segment);\r\n    }\r\n    \r\n    // Calculate installation costs (typically 1.5-2x material cost)\r\n    installationCost = materialCost * 1.75;\r\n    \r\n    // Calculate annual operating costs\r\n    const energyConsumption = this.calculateEnergyConsumption(systemPerformance, configuredSystem);\r\n    const energyRate = 0.12; // $/kWh\r\n    operatingCost = energyConsumption * energyRate;\r\n    \r\n    // Total cost over 20-year lifecycle\r\n    const lifecycleYears = 20;\r\n    const totalCost = materialCost + installationCost + (operatingCost * lifecycleYears);\r\n    \r\n    return totalCost;\r\n  }\r\n\r\n  /**\r\n   * Calculate noise level for system configuration\r\n   */\r\n  private static calculateNoiseLevel(\r\n    systemPerformance: any,\r\n    configuredSystem: SystemConfiguration\r\n  ): number {\r\n    // Simplified noise calculation - to be enhanced with detailed acoustic models\r\n    let totalNoiseLevel = 0;\r\n    \r\n    for (const segment of configuredSystem.segments) {\r\n      const segmentNoise = this.calculateSegmentNoiseLevel(segment, systemPerformance);\r\n      totalNoiseLevel += Math.pow(10, segmentNoise / 10); // Convert dB to linear scale for addition\r\n    }\r\n    \r\n    return 10 * Math.log10(totalNoiseLevel); // Convert back to dB\r\n  }\r\n\r\n  /**\r\n   * Calculate system efficiency\r\n   */\r\n  private static calculateSystemEfficiency(\r\n    systemPerformance: any,\r\n    configuredSystem: SystemConfiguration\r\n  ): number {\r\n    // Simplified efficiency calculation\r\n    const theoreticalMinimumPressure = systemPerformance.totalFlow * 0.1; // Minimum pressure for flow\r\n    const actualPressure = systemPerformance.totalPressureLoss;\r\n    const efficiency = theoreticalMinimumPressure / actualPressure;\r\n    \r\n    return Math.min(efficiency, 1.0); // Cap at 100% efficiency\r\n  }\r\n\r\n  // Helper methods for cost and noise calculations\r\n  private static calculateSegmentMaterialCost(segment: any): number {\r\n    // Placeholder implementation\r\n    return 100; // Base cost per segment\r\n  }\r\n\r\n  private static calculateSegmentNoiseLevel(segment: any, systemPerformance: any): number {\r\n    // Placeholder implementation\r\n    return 45; // Base noise level in dB\r\n  }\r\n\r\n  /**\r\n   * Evaluate constraint function\r\n   */\r\n  private static evaluateConstraint(\r\n    constraint: OptimizationConstraint,\r\n    configuredSystem: SystemConfiguration,\r\n    variables: OptimizationVariable[]\r\n  ): number {\r\n    // Placeholder implementation - to be enhanced with specific constraint evaluations\r\n    return 0; // Return 0 for satisfied constraints, positive for violations\r\n  }\r\n\r\n  /**\r\n   * Create optimizer instance based on algorithm type\r\n   */\r\n  private static createOptimizer(algorithm: OptimizationAlgorithm, problem: OptimizationProblem): any {\r\n    switch (algorithm) {\r\n      case OptimizationAlgorithm.GENETIC_ALGORITHM:\r\n      case OptimizationAlgorithm.MULTI_OBJECTIVE_GA:\r\n      case OptimizationAlgorithm.NSGA_II:\r\n        return this.geneticAlgorithm;\r\n      case OptimizationAlgorithm.SIMULATED_ANNEALING:\r\n        return this.simulatedAnnealing;\r\n      case OptimizationAlgorithm.PARTICLE_SWARM:\r\n        return this.particleSwarm;\r\n      case OptimizationAlgorithm.GRADIENT_DESCENT:\r\n        return this.gradientDescent;\r\n      default:\r\n        throw new Error(`Unsupported optimization algorithm: ${algorithm}`);\r\n    }\r\n  }\r\n\r\n  // Additional helper methods for creating variables and objectives\r\n  private static createBalancingVariables(\r\n    system: SystemConfiguration,\r\n    targetFlows: { [zoneId: string]: number }\r\n  ): OptimizationVariable[] {\r\n    // Placeholder implementation\r\n    return [];\r\n  }\r\n\r\n  private static createBalancingObjectives(targetFlows: { [zoneId: string]: number }): MultiObjectiveFunction {\r\n    // Placeholder implementation\r\n    return {\r\n      objectives: [],\r\n      aggregationMethod: 'weighted_sum'\r\n    };\r\n  }\r\n\r\n  private static createEnergyOptimizationVariables(system: SystemConfiguration): OptimizationVariable[] {\r\n    // Placeholder implementation\r\n    return [];\r\n  }\r\n\r\n  private static createEnergyObjectiveFunction(operatingConditions: any): ObjectiveFunctionType {\r\n    // Placeholder implementation\r\n    return (variables: OptimizationVariable[]) => 0;\r\n  }\r\n\r\n  private static createCostObjectiveFunction(): ObjectiveFunctionType {\r\n    // Placeholder implementation\r\n    return (variables: OptimizationVariable[]) => 0;\r\n  }\r\n\r\n  // Utility methods\r\n  private static generateOptimizationId(problem: OptimizationProblem): string {\r\n    return `opt_${problem.id}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\r\n  }\r\n\r\n  private static async runOptimization(\r\n    optimizer: any,\r\n    objectiveFunction: ObjectiveFunctionType,\r\n    constraintFunctions: ConstraintFunctionType[],\r\n    problem: OptimizationProblem,\r\n    optimizationId: string\r\n  ): Promise<OptimizationResult> {\r\n    // Placeholder implementation - to be completed with actual optimization logic\r\n    throw new Error('Optimization execution not yet implemented');\r\n  }\r\n\r\n  private static async postProcessResults(\r\n    result: OptimizationResult,\r\n    problem: OptimizationProblem,\r\n    startTime: number\r\n  ): Promise<OptimizationResult> {\r\n    // Placeholder implementation\r\n    return result;\r\n  }\r\n\r\n  private static createErrorResult(\r\n    problem: OptimizationProblem,\r\n    error: Error,\r\n    startTime: number\r\n  ): OptimizationResult {\r\n    // Placeholder implementation\r\n    return {\r\n      problemId: problem.id,\r\n      status: OptimizationStatus.ERROR,\r\n      bestSolution: {} as OptimizationSolution,\r\n      statistics: {} as OptimizationStatistics,\r\n      history: {} as OptimizationHistory,\r\n      analysis: {} as ResultAnalysis,\r\n      recommendations: [],\r\n      warnings: [],\r\n      errors: [error.message]\r\n    };\r\n  }\r\n}\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "39fb248e97b128603ca43114140f271357ef8b1c"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2jmlri0rtb = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2jmlri0rtb();
cov_2jmlri0rtb().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_2jmlri0rtb().s[1]++;
exports.SystemOptimizationEngine = void 0;
const SystemOptimizationTypes_1 =
/* istanbul ignore next */
(cov_2jmlri0rtb().s[2]++, require("./types/SystemOptimizationTypes"));
// Import existing Phase 1/2/3 Priority 1 services
const SystemPressureCalculator_1 =
/* istanbul ignore next */
(cov_2jmlri0rtb().s[3]++, require("./SystemPressureCalculator"));
// Import optimization algorithms
const GeneticAlgorithm_1 =
/* istanbul ignore next */
(cov_2jmlri0rtb().s[4]++, require("./algorithms/GeneticAlgorithm"));
const SimulatedAnnealing_1 =
/* istanbul ignore next */
(cov_2jmlri0rtb().s[5]++, require("./algorithms/SimulatedAnnealing"));
const ParticleSwarmOptimization_1 =
/* istanbul ignore next */
(cov_2jmlri0rtb().s[6]++, require("./algorithms/ParticleSwarmOptimization"));
const GradientDescent_1 =
/* istanbul ignore next */
(cov_2jmlri0rtb().s[7]++, require("./algorithms/GradientDescent"));
const MultiObjectiveOptimizationFramework_1 =
/* istanbul ignore next */
(cov_2jmlri0rtb().s[8]++, require("./MultiObjectiveOptimizationFramework"));
/**
 * Main system optimization engine providing comprehensive optimization capabilities
 * for HVAC duct systems with multi-objective optimization and constraint handling
 */
class SystemOptimizationEngine {
  /**
   * Initialize the optimization engine with algorithm instances
   */
  static initialize() {
    /* istanbul ignore next */
    cov_2jmlri0rtb().f[0]++;
    cov_2jmlri0rtb().s[9]++;
    try {
      /* istanbul ignore next */
      cov_2jmlri0rtb().s[10]++;
      this.geneticAlgorithm = new GeneticAlgorithm_1.GeneticAlgorithm();
      /* istanbul ignore next */
      cov_2jmlri0rtb().s[11]++;
      this.simulatedAnnealing = new SimulatedAnnealing_1.SimulatedAnnealing();
      /* istanbul ignore next */
      cov_2jmlri0rtb().s[12]++;
      this.particleSwarm = new ParticleSwarmOptimization_1.ParticleSwarmOptimization();
      /* istanbul ignore next */
      cov_2jmlri0rtb().s[13]++;
      this.gradientDescent = new GradientDescent_1.GradientDescent();
      /* istanbul ignore next */
      cov_2jmlri0rtb().s[14]++;
      console.log(`SystemOptimizationEngine v${this.VERSION} initialized successfully`);
    } catch (error) {
      /* istanbul ignore next */
      cov_2jmlri0rtb().s[15]++;
      console.error('Failed to initialize SystemOptimizationEngine:', error);
      /* istanbul ignore next */
      cov_2jmlri0rtb().s[16]++;
      throw new Error(`Optimization engine initialization failed: ${error}`);
    }
  }
  /**
   * Main optimization method - solves optimization problems with specified algorithms
   */
  static async optimizeSystem(problem, algorithm =
  /* istanbul ignore next */
  (cov_2jmlri0rtb().b[0][0]++, SystemOptimizationTypes_1.OptimizationAlgorithm.GENETIC_ALGORITHM)) {
    /* istanbul ignore next */
    cov_2jmlri0rtb().f[1]++;
    const startTime =
    /* istanbul ignore next */
    (cov_2jmlri0rtb().s[17]++, performance.now());
    /* istanbul ignore next */
    cov_2jmlri0rtb().s[18]++;
    try {
      /* istanbul ignore next */
      cov_2jmlri0rtb().s[19]++;
      // Validate optimization problem
      this.validateOptimizationProblem(problem);
      // Initialize optimization tracking
      const optimizationId =
      /* istanbul ignore next */
      (cov_2jmlri0rtb().s[20]++, this.generateOptimizationId(problem));
      // Create objective function
      const objectiveFunction =
      /* istanbul ignore next */
      (cov_2jmlri0rtb().s[21]++, this.createObjectiveFunction(problem));
      // Create constraint functions
      const constraintFunctions =
      /* istanbul ignore next */
      (cov_2jmlri0rtb().s[22]++, this.createConstraintFunctions(problem));
      // Select and configure optimization algorithm
      const optimizer =
      /* istanbul ignore next */
      (cov_2jmlri0rtb().s[23]++, this.createOptimizer(algorithm, problem));
      // Run optimization
      const result =
      /* istanbul ignore next */
      (cov_2jmlri0rtb().s[24]++, await this.runOptimization(optimizer, objectiveFunction, constraintFunctions, problem, optimizationId));
      // Post-process and analyze results
      const finalResult =
      /* istanbul ignore next */
      (cov_2jmlri0rtb().s[25]++, await this.postProcessResults(result, problem, startTime));
      // Store optimization history
      /* istanbul ignore next */
      cov_2jmlri0rtb().s[26]++;
      this.optimizationHistory.set(optimizationId, finalResult.history);
      /* istanbul ignore next */
      cov_2jmlri0rtb().s[27]++;
      this.performanceMetrics.set(optimizationId, finalResult.statistics);
      /* istanbul ignore next */
      cov_2jmlri0rtb().s[28]++;
      return finalResult;
    } catch (error) {
      /* istanbul ignore next */
      cov_2jmlri0rtb().s[29]++;
      console.error('Optimization failed:', error);
      /* istanbul ignore next */
      cov_2jmlri0rtb().s[30]++;
      return this.createErrorResult(problem, error, startTime);
    }
  }
  /**
   * Multi-objective optimization with Pareto analysis
   */
  static async optimizeMultiObjective(problem, algorithm =
  /* istanbul ignore next */
  (cov_2jmlri0rtb().b[1][0]++, SystemOptimizationTypes_1.OptimizationAlgorithm.NSGA_II)) {
    /* istanbul ignore next */
    cov_2jmlri0rtb().f[2]++;
    cov_2jmlri0rtb().s[31]++;
    if (problem.objectives.objectives.length < 2) {
      /* istanbul ignore next */
      cov_2jmlri0rtb().b[2][0]++;
      cov_2jmlri0rtb().s[32]++;
      throw new Error('Multi-objective optimization requires at least 2 objectives');
    } else
    /* istanbul ignore next */
    {
      cov_2jmlri0rtb().b[2][1]++;
    }
    cov_2jmlri0rtb().s[33]++;
    try {
      /* istanbul ignore next */
      cov_2jmlri0rtb().s[34]++;
      console.log(`Starting multi-objective optimization for problem: ${problem.id}`);
      // Create objective functions
      const objectiveFunctions =
      /* istanbul ignore next */
      (cov_2jmlri0rtb().s[35]++, problem.objectives.objectives.map(obj => {
        /* istanbul ignore next */
        cov_2jmlri0rtb().f[3]++;
        cov_2jmlri0rtb().s[36]++;
        return this.createObjectiveFunction(obj, problem);
      }));
      // Create constraint functions
      const constraintFunctions =
      /* istanbul ignore next */
      (cov_2jmlri0rtb().s[37]++, problem.constraints.map(constraint => {
        /* istanbul ignore next */
        cov_2jmlri0rtb().f[4]++;
        cov_2jmlri0rtb().s[38]++;
        return this.createConstraintFunction(constraint, problem);
      }));
      // Use Multi-objective Optimization Framework
      const moFramework =
      /* istanbul ignore next */
      (cov_2jmlri0rtb().s[39]++, new MultiObjectiveOptimizationFramework_1.MultiObjectiveOptimizationFramework({
        algorithm: 'nsga2',
        populationSize: 100,
        maxGenerations: 100,
        crossoverRate: 0.9,
        mutationRate: 0.1,
        eliteSize: 10,
        constraintHandling: 'penalty',
        penaltyCoefficient: 1000,
        paretoSettings: {
          maxSolutions: 100,
          diversityThreshold: 0.01,
          convergenceThreshold: 1e-6,
          hypervolume: {
            enabled: true,
            referencePoint: []
          },
          spacing: {
            enabled: true,
            targetSpacing: 0.1
          }
        },
        diversityMaintenance: true,
        archiveSize: 200
      }));
      const result =
      /* istanbul ignore next */
      (cov_2jmlri0rtb().s[40]++, await moFramework.optimizeMultiObjective(problem, objectiveFunctions, constraintFunctions));
      /* istanbul ignore next */
      cov_2jmlri0rtb().s[41]++;
      console.log(`Multi-objective optimization completed for problem: ${problem.id}`);
      /* istanbul ignore next */
      cov_2jmlri0rtb().s[42]++;
      return result;
    } catch (error) {
      /* istanbul ignore next */
      cov_2jmlri0rtb().s[43]++;
      console.error('Multi-objective optimization failed:', error);
      /* istanbul ignore next */
      cov_2jmlri0rtb().s[44]++;
      throw error;
    }
  }
  /**
   * Optimize system balance for multi-zone HVAC systems
   */
  static async optimizeSystemBalance(systemConfiguration, targetFlowRates, constraints) {
    /* istanbul ignore next */
    cov_2jmlri0rtb().f[5]++;
    // Create optimization problem for system balancing
    const problem =
    /* istanbul ignore next */
    (cov_2jmlri0rtb().s[45]++, {
      id: `balance_${systemConfiguration.id}_${Date.now()}`,
      name: `System Balance Optimization - ${systemConfiguration.name}`,
      description: 'Optimize damper positions and system configuration for balanced airflow',
      systemConfiguration,
      variables: this.createBalancingVariables(systemConfiguration, targetFlowRates),
      objectives: this.createBalancingObjectives(targetFlowRates),
      constraints,
      algorithmSettings: {
        algorithm: SystemOptimizationTypes_1.OptimizationAlgorithm.GENETIC_ALGORITHM,
        parameters: {
          populationSize: 50,
          maxIterations: 200,
          crossoverRate: 0.8,
          mutationRate: 0.1
        },
        parallelization: {
          enabled: true
        }
      },
      convergenceCriteria: {
        maxIterations: 200,
        toleranceValue: 0.01,
        stagnationLimit: 20,
        improvementThreshold: 0.001
      },
      outputRequirements: {
        includeHistory: true,
        detailedAnalysis: true,
        sensitivityAnalysis: true,
        uncertaintyAnalysis: false,
        visualizations: [{
          type: 'convergence',
          parameters: {}
        }, {
          type: 'variable_history',
          parameters: {}
        }],
        reportFormat: 'json'
      }
    });
    /* istanbul ignore next */
    cov_2jmlri0rtb().s[46]++;
    return this.optimizeSystem(problem, SystemOptimizationTypes_1.OptimizationAlgorithm.GENETIC_ALGORITHM);
  }
  /**
   * Optimize for minimum energy consumption
   */
  static async optimizeEnergyEfficiency(systemConfiguration, operatingConditions, constraints) {
    /* istanbul ignore next */
    cov_2jmlri0rtb().f[6]++;
    const problem =
    /* istanbul ignore next */
    (cov_2jmlri0rtb().s[47]++, {
      id: `energy_${systemConfiguration.id}_${Date.now()}`,
      name: `Energy Efficiency Optimization - ${systemConfiguration.name}`,
      description: 'Optimize system configuration for minimum energy consumption',
      systemConfiguration,
      variables: this.createEnergyOptimizationVariables(systemConfiguration),
      objectives: {
        objectives: [{
          id: 'energy_consumption',
          objective: SystemOptimizationTypes_1.OptimizationObjective.MINIMIZE_ENERGY_CONSUMPTION,
          weight: 0.7,
          description: 'Minimize total system energy consumption',
          evaluationFunction: this.createEnergyObjectiveFunction(operatingConditions),
          units: 'kWh/year'
        }, {
          id: 'total_cost',
          objective: SystemOptimizationTypes_1.OptimizationObjective.MINIMIZE_TOTAL_COST,
          weight: 0.3,
          description: 'Minimize total system cost (initial + operating)',
          evaluationFunction: this.createCostObjectiveFunction(),
          units: 'USD'
        }],
        aggregationMethod: 'weighted_sum'
      },
      constraints,
      algorithmSettings: {
        algorithm: SystemOptimizationTypes_1.OptimizationAlgorithm.PARTICLE_SWARM,
        parameters: {
          populationSize: 40,
          maxIterations: 300,
          inertiaWeight: 0.9,
          accelerationCoefficients: [2.0, 2.0]
        },
        parallelization: {
          enabled: true
        }
      },
      convergenceCriteria: {
        maxIterations: 300,
        toleranceValue: 0.005,
        stagnationLimit: 30,
        improvementThreshold: 0.001
      },
      outputRequirements: {
        includeHistory: true,
        detailedAnalysis: true,
        sensitivityAnalysis: true,
        uncertaintyAnalysis: true,
        visualizations: [{
          type: 'convergence',
          parameters: {}
        }, {
          type: 'pareto_front',
          parameters: {}
        }],
        reportFormat: 'json'
      }
    });
    /* istanbul ignore next */
    cov_2jmlri0rtb().s[48]++;
    return this.optimizeMultiObjective(problem, SystemOptimizationTypes_1.OptimizationAlgorithm.PARTICLE_SWARM);
  }
  /**
   * Validate optimization problem structure and constraints
   */
  static validateOptimizationProblem(problem) {
    /* istanbul ignore next */
    cov_2jmlri0rtb().f[7]++;
    cov_2jmlri0rtb().s[49]++;
    if (
    /* istanbul ignore next */
    (cov_2jmlri0rtb().b[4][0]++, !problem.id) ||
    /* istanbul ignore next */
    (cov_2jmlri0rtb().b[4][1]++, !problem.systemConfiguration)) {
      /* istanbul ignore next */
      cov_2jmlri0rtb().b[3][0]++;
      cov_2jmlri0rtb().s[50]++;
      throw new Error('Invalid optimization problem: missing required fields');
    } else
    /* istanbul ignore next */
    {
      cov_2jmlri0rtb().b[3][1]++;
    }
    cov_2jmlri0rtb().s[51]++;
    if (problem.variables.length === 0) {
      /* istanbul ignore next */
      cov_2jmlri0rtb().b[5][0]++;
      cov_2jmlri0rtb().s[52]++;
      throw new Error('Optimization problem must have at least one variable');
    } else
    /* istanbul ignore next */
    {
      cov_2jmlri0rtb().b[5][1]++;
    }
    cov_2jmlri0rtb().s[53]++;
    if (problem.objectives.objectives.length === 0) {
      /* istanbul ignore next */
      cov_2jmlri0rtb().b[6][0]++;
      cov_2jmlri0rtb().s[54]++;
      throw new Error('Optimization problem must have at least one objective');
    } else
    /* istanbul ignore next */
    {
      cov_2jmlri0rtb().b[6][1]++;
    }
    // Validate variable bounds
    cov_2jmlri0rtb().s[55]++;
    for (const variable of problem.variables) {
      /* istanbul ignore next */
      cov_2jmlri0rtb().s[56]++;
      if (
      /* istanbul ignore next */
      (cov_2jmlri0rtb().b[8][0]++, !variable.bounds) ||
      /* istanbul ignore next */
      (cov_2jmlri0rtb().b[8][1]++, variable.bounds.minimum >= variable.bounds.maximum)) {
        /* istanbul ignore next */
        cov_2jmlri0rtb().b[7][0]++;
        cov_2jmlri0rtb().s[57]++;
        throw new Error(`Invalid bounds for variable ${variable.id}`);
      } else
      /* istanbul ignore next */
      {
        cov_2jmlri0rtb().b[7][1]++;
      }
    }
    // Validate objective weights
    const totalWeight =
    /* istanbul ignore next */
    (cov_2jmlri0rtb().s[58]++, problem.objectives.objectives.reduce((sum, obj) => {
      /* istanbul ignore next */
      cov_2jmlri0rtb().f[8]++;
      cov_2jmlri0rtb().s[59]++;
      return sum + obj.weight;
    }, 0));
    /* istanbul ignore next */
    cov_2jmlri0rtb().s[60]++;
    if (Math.abs(totalWeight - 1.0) > 1e-6) {
      /* istanbul ignore next */
      cov_2jmlri0rtb().b[9][0]++;
      cov_2jmlri0rtb().s[61]++;
      console.warn(`Objective weights sum to ${totalWeight}, normalizing to 1.0`);
      /* istanbul ignore next */
      cov_2jmlri0rtb().s[62]++;
      problem.objectives.objectives.forEach(obj => {
        /* istanbul ignore next */
        cov_2jmlri0rtb().f[9]++;
        cov_2jmlri0rtb().s[63]++;
        return obj.weight /= totalWeight;
      });
    } else
    /* istanbul ignore next */
    {
      cov_2jmlri0rtb().b[9][1]++;
    }
  }
  /**
   * Create objective function for optimization problem
   */
  static createObjectiveFunction(problem) {
    /* istanbul ignore next */
    cov_2jmlri0rtb().f[10]++;
    cov_2jmlri0rtb().s[64]++;
    return variables => {
      /* istanbul ignore next */
      cov_2jmlri0rtb().f[11]++;
      cov_2jmlri0rtb().s[65]++;
      try {
        // Apply variables to system configuration
        const configuredSystem =
        /* istanbul ignore next */
        (cov_2jmlri0rtb().s[66]++, this.applyVariablesToSystem(problem.systemConfiguration, variables));
        // Calculate system performance using existing Phase 1/2/3 Priority 1 services
        const systemPerformance =
        /* istanbul ignore next */
        (cov_2jmlri0rtb().s[67]++, SystemPressureCalculator_1.SystemPressureCalculator.calculateEnhancedSystemPressure({
          segments: configuredSystem.segments,
          systemType: configuredSystem.systemType,
          designConditions: configuredSystem.designConditions,
          calculationOptions: {
            includeElevation: true,
            includeFittings: true,
            roundResults: false
          }
        }));
        // Calculate objective values
        let totalObjectiveValue =
        /* istanbul ignore next */
        (cov_2jmlri0rtb().s[68]++, 0);
        /* istanbul ignore next */
        cov_2jmlri0rtb().s[69]++;
        for (const objective of problem.objectives.objectives) {
          let objectiveValue;
          /* istanbul ignore next */
          cov_2jmlri0rtb().s[70]++;
          switch (objective.objective) {
            case SystemOptimizationTypes_1.OptimizationObjective.MINIMIZE_PRESSURE_LOSS:
              /* istanbul ignore next */
              cov_2jmlri0rtb().b[10][0]++;
              cov_2jmlri0rtb().s[71]++;
              objectiveValue = systemPerformance.totalPressureLoss;
              /* istanbul ignore next */
              cov_2jmlri0rtb().s[72]++;
              break;
            case SystemOptimizationTypes_1.OptimizationObjective.MINIMIZE_ENERGY_CONSUMPTION:
              /* istanbul ignore next */
              cov_2jmlri0rtb().b[10][1]++;
              cov_2jmlri0rtb().s[73]++;
              objectiveValue = this.calculateEnergyConsumption(systemPerformance, configuredSystem);
              /* istanbul ignore next */
              cov_2jmlri0rtb().s[74]++;
              break;
            case SystemOptimizationTypes_1.OptimizationObjective.MINIMIZE_TOTAL_COST:
              /* istanbul ignore next */
              cov_2jmlri0rtb().b[10][2]++;
              cov_2jmlri0rtb().s[75]++;
              objectiveValue = this.calculateTotalCost(systemPerformance, configuredSystem);
              /* istanbul ignore next */
              cov_2jmlri0rtb().s[76]++;
              break;
            case SystemOptimizationTypes_1.OptimizationObjective.MINIMIZE_NOISE_LEVEL:
              /* istanbul ignore next */
              cov_2jmlri0rtb().b[10][3]++;
              cov_2jmlri0rtb().s[77]++;
              objectiveValue = this.calculateNoiseLevel(systemPerformance, configuredSystem);
              /* istanbul ignore next */
              cov_2jmlri0rtb().s[78]++;
              break;
            case SystemOptimizationTypes_1.OptimizationObjective.MAXIMIZE_EFFICIENCY:
              /* istanbul ignore next */
              cov_2jmlri0rtb().b[10][4]++;
              cov_2jmlri0rtb().s[79]++;
              objectiveValue = -this.calculateSystemEfficiency(systemPerformance, configuredSystem);
              /* istanbul ignore next */
              cov_2jmlri0rtb().s[80]++;
              break;
            default:
              /* istanbul ignore next */
              cov_2jmlri0rtb().b[10][5]++;
              cov_2jmlri0rtb().s[81]++;
              objectiveValue = objective.evaluationFunction(variables);
          }
          /* istanbul ignore next */
          cov_2jmlri0rtb().s[82]++;
          totalObjectiveValue += objective.weight * objectiveValue;
        }
        /* istanbul ignore next */
        cov_2jmlri0rtb().s[83]++;
        return totalObjectiveValue;
      } catch (error) {
        /* istanbul ignore next */
        cov_2jmlri0rtb().s[84]++;
        console.error('Error in objective function evaluation:', error);
        /* istanbul ignore next */
        cov_2jmlri0rtb().s[85]++;
        return Number.MAX_VALUE; // Return large penalty for invalid solutions
      }
    };
  }
  /**
   * Create constraint functions for optimization problem
   */
  static createConstraintFunctions(problem) {
    /* istanbul ignore next */
    cov_2jmlri0rtb().f[12]++;
    cov_2jmlri0rtb().s[86]++;
    return problem.constraints.map(constraint => {
      /* istanbul ignore next */
      cov_2jmlri0rtb().f[13]++;
      cov_2jmlri0rtb().s[87]++;
      return variables => {
        /* istanbul ignore next */
        cov_2jmlri0rtb().f[14]++;
        cov_2jmlri0rtb().s[88]++;
        try {
          // Apply variables to system configuration
          const configuredSystem =
          /* istanbul ignore next */
          (cov_2jmlri0rtb().s[89]++, this.applyVariablesToSystem(problem.systemConfiguration, variables));
          // Evaluate constraint based on type
          /* istanbul ignore next */
          cov_2jmlri0rtb().s[90]++;
          return this.evaluateConstraint(constraint, configuredSystem, variables);
        } catch (error) {
          /* istanbul ignore next */
          cov_2jmlri0rtb().s[91]++;
          console.error(`Error evaluating constraint ${constraint.id}:`, error);
          /* istanbul ignore next */
          cov_2jmlri0rtb().s[92]++;
          return Number.MAX_VALUE; // Return large violation for invalid evaluations
        }
      };
    });
  }
  /**
   * Apply optimization variables to system configuration
   */
  static applyVariablesToSystem(baseSystem, variables) {
    /* istanbul ignore next */
    cov_2jmlri0rtb().f[15]++;
    // Deep clone the system configuration
    const configuredSystem =
    /* istanbul ignore next */
    (cov_2jmlri0rtb().s[93]++, JSON.parse(JSON.stringify(baseSystem)));
    // Apply each variable to the appropriate system component
    /* istanbul ignore next */
    cov_2jmlri0rtb().s[94]++;
    for (const variable of variables) {
      /* istanbul ignore next */
      cov_2jmlri0rtb().s[95]++;
      this.applyVariableToSystem(configuredSystem, variable);
    }
    /* istanbul ignore next */
    cov_2jmlri0rtb().s[96]++;
    return configuredSystem;
  }
  /**
   * Apply a single variable to system configuration
   */
  static applyVariableToSystem(system, variable) {
    /* istanbul ignore next */
    cov_2jmlri0rtb().f[16]++;
    cov_2jmlri0rtb().s[97]++;
    // Implementation depends on variable type
    switch (variable.type) {
      case 'duct_size':
        /* istanbul ignore next */
        cov_2jmlri0rtb().b[11][0]++;
        cov_2jmlri0rtb().s[98]++;
        this.applyDuctSizeVariable(system, variable);
        /* istanbul ignore next */
        cov_2jmlri0rtb().s[99]++;
        break;
      case 'fitting_type':
        /* istanbul ignore next */
        cov_2jmlri0rtb().b[11][1]++;
        cov_2jmlri0rtb().s[100]++;
        this.applyFittingTypeVariable(system, variable);
        /* istanbul ignore next */
        cov_2jmlri0rtb().s[101]++;
        break;
      case 'material_type':
        /* istanbul ignore next */
        cov_2jmlri0rtb().b[11][2]++;
        cov_2jmlri0rtb().s[102]++;
        this.applyMaterialTypeVariable(system, variable);
        /* istanbul ignore next */
        cov_2jmlri0rtb().s[103]++;
        break;
      case 'damper_position':
        /* istanbul ignore next */
        cov_2jmlri0rtb().b[11][3]++;
        cov_2jmlri0rtb().s[104]++;
        this.applyDamperPositionVariable(system, variable);
        /* istanbul ignore next */
        cov_2jmlri0rtb().s[105]++;
        break;
      case 'fan_speed':
        /* istanbul ignore next */
        cov_2jmlri0rtb().b[11][4]++;
        cov_2jmlri0rtb().s[106]++;
        this.applyFanSpeedVariable(system, variable);
        /* istanbul ignore next */
        cov_2jmlri0rtb().s[107]++;
        break;
      default:
        /* istanbul ignore next */
        cov_2jmlri0rtb().b[11][5]++;
        cov_2jmlri0rtb().s[108]++;
        console.warn(`Unknown variable type: ${variable.type}`);
    }
  }
  // Variable application methods (to be implemented based on specific requirements)
  static applyDuctSizeVariable(system, variable) {
    /* istanbul ignore next */
    cov_2jmlri0rtb().f[17]++;
  } // Implementation for duct size variables
  static applyFittingTypeVariable(system, variable) {
    /* istanbul ignore next */
    cov_2jmlri0rtb().f[18]++;
  } // Implementation for fitting type variables
  static applyMaterialTypeVariable(system, variable) {
    /* istanbul ignore next */
    cov_2jmlri0rtb().f[19]++;
  } // Implementation for material type variables
  static applyDamperPositionVariable(system, variable) {
    /* istanbul ignore next */
    cov_2jmlri0rtb().f[20]++;
  } // Implementation for damper position variables
  static applyFanSpeedVariable(system, variable) {
    /* istanbul ignore next */
    cov_2jmlri0rtb().f[21]++;
  } // Implementation for fan speed variables
  /**
   * Calculate energy consumption for system configuration
   */
  static calculateEnergyConsumption(systemPerformance, configuredSystem) {
    /* istanbul ignore next */
    cov_2jmlri0rtb().f[22]++;
    // Simplified energy calculation - to be enhanced with detailed fan curves and operating schedules
    const fanPower =
    /* istanbul ignore next */
    (cov_2jmlri0rtb().s[109]++, systemPerformance.totalPressureLoss * systemPerformance.totalFlow / 6356); // Convert to HP
    const annualOperatingHours =
    /* istanbul ignore next */
    (cov_2jmlri0rtb().s[110]++, 8760); // Hours per year
    const energyConsumption =
    /* istanbul ignore next */
    (cov_2jmlri0rtb().s[111]++, fanPower * 0.746 * annualOperatingHours); // Convert to kWh/year
    /* istanbul ignore next */
    cov_2jmlri0rtb().s[112]++;
    return energyConsumption;
  }
  /**
   * Calculate total cost for system configuration
   */
  static calculateTotalCost(systemPerformance, configuredSystem) {
    /* istanbul ignore next */
    cov_2jmlri0rtb().f[23]++;
    // Simplified cost calculation - to be enhanced with detailed cost models
    let materialCost =
    /* istanbul ignore next */
    (cov_2jmlri0rtb().s[113]++, 0);
    let installationCost =
    /* istanbul ignore next */
    (cov_2jmlri0rtb().s[114]++, 0);
    let operatingCost =
    /* istanbul ignore next */
    (cov_2jmlri0rtb().s[115]++, 0);
    // Calculate material costs
    /* istanbul ignore next */
    cov_2jmlri0rtb().s[116]++;
    for (const segment of configuredSystem.segments) {
      /* istanbul ignore next */
      cov_2jmlri0rtb().s[117]++;
      materialCost += this.calculateSegmentMaterialCost(segment);
    }
    // Calculate installation costs (typically 1.5-2x material cost)
    /* istanbul ignore next */
    cov_2jmlri0rtb().s[118]++;
    installationCost = materialCost * 1.75;
    // Calculate annual operating costs
    const energyConsumption =
    /* istanbul ignore next */
    (cov_2jmlri0rtb().s[119]++, this.calculateEnergyConsumption(systemPerformance, configuredSystem));
    const energyRate =
    /* istanbul ignore next */
    (cov_2jmlri0rtb().s[120]++, 0.12); // $/kWh
    /* istanbul ignore next */
    cov_2jmlri0rtb().s[121]++;
    operatingCost = energyConsumption * energyRate;
    // Total cost over 20-year lifecycle
    const lifecycleYears =
    /* istanbul ignore next */
    (cov_2jmlri0rtb().s[122]++, 20);
    const totalCost =
    /* istanbul ignore next */
    (cov_2jmlri0rtb().s[123]++, materialCost + installationCost + operatingCost * lifecycleYears);
    /* istanbul ignore next */
    cov_2jmlri0rtb().s[124]++;
    return totalCost;
  }
  /**
   * Calculate noise level for system configuration
   */
  static calculateNoiseLevel(systemPerformance, configuredSystem) {
    /* istanbul ignore next */
    cov_2jmlri0rtb().f[24]++;
    // Simplified noise calculation - to be enhanced with detailed acoustic models
    let totalNoiseLevel =
    /* istanbul ignore next */
    (cov_2jmlri0rtb().s[125]++, 0);
    /* istanbul ignore next */
    cov_2jmlri0rtb().s[126]++;
    for (const segment of configuredSystem.segments) {
      const segmentNoise =
      /* istanbul ignore next */
      (cov_2jmlri0rtb().s[127]++, this.calculateSegmentNoiseLevel(segment, systemPerformance));
      /* istanbul ignore next */
      cov_2jmlri0rtb().s[128]++;
      totalNoiseLevel += Math.pow(10, segmentNoise / 10); // Convert dB to linear scale for addition
    }
    /* istanbul ignore next */
    cov_2jmlri0rtb().s[129]++;
    return 10 * Math.log10(totalNoiseLevel); // Convert back to dB
  }
  /**
   * Calculate system efficiency
   */
  static calculateSystemEfficiency(systemPerformance, configuredSystem) {
    /* istanbul ignore next */
    cov_2jmlri0rtb().f[25]++;
    // Simplified efficiency calculation
    const theoreticalMinimumPressure =
    /* istanbul ignore next */
    (cov_2jmlri0rtb().s[130]++, systemPerformance.totalFlow * 0.1); // Minimum pressure for flow
    const actualPressure =
    /* istanbul ignore next */
    (cov_2jmlri0rtb().s[131]++, systemPerformance.totalPressureLoss);
    const efficiency =
    /* istanbul ignore next */
    (cov_2jmlri0rtb().s[132]++, theoreticalMinimumPressure / actualPressure);
    /* istanbul ignore next */
    cov_2jmlri0rtb().s[133]++;
    return Math.min(efficiency, 1.0); // Cap at 100% efficiency
  }
  // Helper methods for cost and noise calculations
  static calculateSegmentMaterialCost(segment) {
    /* istanbul ignore next */
    cov_2jmlri0rtb().f[26]++;
    cov_2jmlri0rtb().s[134]++;
    // Placeholder implementation
    return 100; // Base cost per segment
  }
  static calculateSegmentNoiseLevel(segment, systemPerformance) {
    /* istanbul ignore next */
    cov_2jmlri0rtb().f[27]++;
    cov_2jmlri0rtb().s[135]++;
    // Placeholder implementation
    return 45; // Base noise level in dB
  }
  /**
   * Evaluate constraint function
   */
  static evaluateConstraint(constraint, configuredSystem, variables) {
    /* istanbul ignore next */
    cov_2jmlri0rtb().f[28]++;
    cov_2jmlri0rtb().s[136]++;
    // Placeholder implementation - to be enhanced with specific constraint evaluations
    return 0; // Return 0 for satisfied constraints, positive for violations
  }
  /**
   * Create optimizer instance based on algorithm type
   */
  static createOptimizer(algorithm, problem) {
    /* istanbul ignore next */
    cov_2jmlri0rtb().f[29]++;
    cov_2jmlri0rtb().s[137]++;
    switch (algorithm) {
      case SystemOptimizationTypes_1.OptimizationAlgorithm.GENETIC_ALGORITHM:
        /* istanbul ignore next */
        cov_2jmlri0rtb().b[12][0]++;
      case SystemOptimizationTypes_1.OptimizationAlgorithm.MULTI_OBJECTIVE_GA:
        /* istanbul ignore next */
        cov_2jmlri0rtb().b[12][1]++;
      case SystemOptimizationTypes_1.OptimizationAlgorithm.NSGA_II:
        /* istanbul ignore next */
        cov_2jmlri0rtb().b[12][2]++;
        cov_2jmlri0rtb().s[138]++;
        return this.geneticAlgorithm;
      case SystemOptimizationTypes_1.OptimizationAlgorithm.SIMULATED_ANNEALING:
        /* istanbul ignore next */
        cov_2jmlri0rtb().b[12][3]++;
        cov_2jmlri0rtb().s[139]++;
        return this.simulatedAnnealing;
      case SystemOptimizationTypes_1.OptimizationAlgorithm.PARTICLE_SWARM:
        /* istanbul ignore next */
        cov_2jmlri0rtb().b[12][4]++;
        cov_2jmlri0rtb().s[140]++;
        return this.particleSwarm;
      case SystemOptimizationTypes_1.OptimizationAlgorithm.GRADIENT_DESCENT:
        /* istanbul ignore next */
        cov_2jmlri0rtb().b[12][5]++;
        cov_2jmlri0rtb().s[141]++;
        return this.gradientDescent;
      default:
        /* istanbul ignore next */
        cov_2jmlri0rtb().b[12][6]++;
        cov_2jmlri0rtb().s[142]++;
        throw new Error(`Unsupported optimization algorithm: ${algorithm}`);
    }
  }
  // Additional helper methods for creating variables and objectives
  static createBalancingVariables(system, targetFlows) {
    /* istanbul ignore next */
    cov_2jmlri0rtb().f[30]++;
    cov_2jmlri0rtb().s[143]++;
    // Placeholder implementation
    return [];
  }
  static createBalancingObjectives(targetFlows) {
    /* istanbul ignore next */
    cov_2jmlri0rtb().f[31]++;
    cov_2jmlri0rtb().s[144]++;
    // Placeholder implementation
    return {
      objectives: [],
      aggregationMethod: 'weighted_sum'
    };
  }
  static createEnergyOptimizationVariables(system) {
    /* istanbul ignore next */
    cov_2jmlri0rtb().f[32]++;
    cov_2jmlri0rtb().s[145]++;
    // Placeholder implementation
    return [];
  }
  static createEnergyObjectiveFunction(operatingConditions) {
    /* istanbul ignore next */
    cov_2jmlri0rtb().f[33]++;
    cov_2jmlri0rtb().s[146]++;
    // Placeholder implementation
    return variables => {
      /* istanbul ignore next */
      cov_2jmlri0rtb().f[34]++;
      cov_2jmlri0rtb().s[147]++;
      return 0;
    };
  }
  static createCostObjectiveFunction() {
    /* istanbul ignore next */
    cov_2jmlri0rtb().f[35]++;
    cov_2jmlri0rtb().s[148]++;
    // Placeholder implementation
    return variables => {
      /* istanbul ignore next */
      cov_2jmlri0rtb().f[36]++;
      cov_2jmlri0rtb().s[149]++;
      return 0;
    };
  }
  // Utility methods
  static generateOptimizationId(problem) {
    /* istanbul ignore next */
    cov_2jmlri0rtb().f[37]++;
    cov_2jmlri0rtb().s[150]++;
    return `opt_${problem.id}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  static async runOptimization(optimizer, objectiveFunction, constraintFunctions, problem, optimizationId) {
    /* istanbul ignore next */
    cov_2jmlri0rtb().f[38]++;
    cov_2jmlri0rtb().s[151]++;
    // Placeholder implementation - to be completed with actual optimization logic
    throw new Error('Optimization execution not yet implemented');
  }
  static async postProcessResults(result, problem, startTime) {
    /* istanbul ignore next */
    cov_2jmlri0rtb().f[39]++;
    cov_2jmlri0rtb().s[152]++;
    // Placeholder implementation
    return result;
  }
  static createErrorResult(problem, error, startTime) {
    /* istanbul ignore next */
    cov_2jmlri0rtb().f[40]++;
    cov_2jmlri0rtb().s[153]++;
    // Placeholder implementation
    return {
      problemId: problem.id,
      status: SystemOptimizationTypes_1.OptimizationStatus.ERROR,
      bestSolution: {},
      statistics: {},
      history: {},
      analysis: {},
      recommendations: [],
      warnings: [],
      errors: [error.message]
    };
  }
}
/* istanbul ignore next */
cov_2jmlri0rtb().s[154]++;
exports.SystemOptimizationEngine = SystemOptimizationEngine;
/* istanbul ignore next */
cov_2jmlri0rtb().s[155]++;
SystemOptimizationEngine.VERSION = '3.0.0';
/* istanbul ignore next */
cov_2jmlri0rtb().s[156]++;
SystemOptimizationEngine.MAX_ITERATIONS_DEFAULT = 1000;
/* istanbul ignore next */
cov_2jmlri0rtb().s[157]++;
SystemOptimizationEngine.CONVERGENCE_TOLERANCE_DEFAULT = 1e-6;
// Performance tracking
/* istanbul ignore next */
cov_2jmlri0rtb().s[158]++;
SystemOptimizationEngine.optimizationHistory = new Map();
/* istanbul ignore next */
cov_2jmlri0rtb().s[159]++;
SystemOptimizationEngine.performanceMetrics = new Map();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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