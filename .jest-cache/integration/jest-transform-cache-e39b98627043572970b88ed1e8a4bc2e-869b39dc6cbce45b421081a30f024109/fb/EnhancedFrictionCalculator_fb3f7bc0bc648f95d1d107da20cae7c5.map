{"version": 3, "names": ["cov_1tnf2sn61", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "name", "decl", "loc", "branchMap", "type", "locations", "undefined", "s", "f", "b", "inputSourceMap", "file", "mappings", "names", "sources", "sourcesContent", "version", "_coverageSchema", "coverage", "actualCoverage", "AirPropertiesCalculator_1", "require", "FrictionMethod", "exports", "FlowRegime", "MaterialAge", "SurfaceCondition", "EnhancedFrictionCalculator", "calculateFrictionLoss", "input", "velocity", "hydraulicDiameter", "length", "material", "method", "ENHANCED_DARCY", "airConditions", "materialAge", "GOOD", "surfaceCondition", "customRoughness", "ductShape", "aspectRatio", "correctionFactors", "validationLevel", "warnings", "recommendations", "validateInputs", "airProps", "AirPropertiesCalculator", "calculateAirProperties", "density", "STANDARD_AIR", "viscosity", "push", "velocityFps", "diameterFt", "reynoldsNumber", "flowRegime", "classifyFlowRegime", "materialProperties", "getMaterialProperties", "relativeRoughness", "adjustedRoughness", "frictionFactor", "calculateFrictionFactor", "environmentalCorrections", "calculateEnvironmentalCorrections", "shapeCorrection", "calculateShapeCorrection", "additionalCorrections", "calculateAdditionalCorrections", "baseFrictionLoss", "Math", "pow", "totalCorrection", "combined", "correctedFrictionLoss", "frictionRate", "uncertaintyBounds", "calculateUncertaintyBounds", "generateRecommendations", "frictionLoss", "accuracy", "METHOD_ACCURACY", "calculationDetails", "formula", "getFormulaDescription", "intermediateValues", "standardReference", "getStandardReference", "getOptimalMethod", "REYNOLDS_TRANSITIONS", "LAMINAR_MAX", "COLEBROOK_WHITE", "HAALAND", "SWAMEE_JAIN", "CHEN", "Error", "LAMINAR", "TRANSITIONAL_MAX", "TRANSITIONAL", "TURBULENT_SMOOTH", "TURBULENT_ROUGH", "FULLY_ROUGH", "baseRoughness", "materialData", "getEnhancedMaterialRoughness", "error", "getDefaultRoughness", "agingFactor", "AGING_FACTORS", "surfaceFactor", "SURFACE_FACTORS", "combinedFactor", "defaultRoughness", "colebrookWhite", "MOODY", "moodyApproximation", "<PERSON><PERSON><PERSON><PERSON>", "haaland", "chen", "ZIGRANG_SYLVESTER", "zigrangSylvester", "enhanced<PERSON><PERSON>cy", "i", "fNew", "log10", "sqrt", "abs", "numerator", "term1", "term2", "A", "fLaminar", "fTurbulent", "weight", "airDensity", "corrections", "temperature", "pressure", "humidity", "densityRatio", "installation", "maintenance", "environmental", "baseAccuracy", "uncertainty", "lower", "upper", "confidenceLevel", "descriptions", "VERSION", "TURBULENT_MIN", "NEW", "AVERAGE", "POOR", "VERY_POOR", "EXCELLENT"], "sources": ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\EnhancedFrictionCalculator.ts"], "sourcesContent": ["/**\r\n * Enhanced Friction Calculator\r\n * \r\n * Comprehensive friction calculation service for Phase 3: Advanced Calculation Modules\r\n * Provides multiple friction factor calculation methods, material aging effects,\r\n * environmental corrections, and advanced features for HVAC duct systems.\r\n * \r\n * @version 3.0.0\r\n * <AUTHOR> Suite Development Team\r\n */\r\n\r\nimport { AirPropertiesCalculator, AirConditions } from './AirPropertiesCalculator';\r\n\r\n/**\r\n * Friction calculation method options\r\n */\r\nexport enum FrictionMethod {\r\n  COLEBROOK_WHITE = 'colebrook_white',\r\n  MOODY = 'moody',\r\n  SWAMEE_JAIN = 'swamee_jain',\r\n  HAALAND = 'haaland',\r\n  CHEN = 'chen',\r\n  ZIGRANG_SYLVESTER = 'zigrang_sylvester',\r\n  ENHANCED_DARCY = 'enhanced_darcy'\r\n}\r\n\r\n/**\r\n * Flow regime classification\r\n */\r\nexport enum FlowRegime {\r\n  LAMINAR = 'laminar',\r\n  TRANSITIONAL = 'transitional',\r\n  TURBULENT_SMOOTH = 'turbulent_smooth',\r\n  TURBULENT_ROUGH = 'turbulent_rough',\r\n  FULLY_ROUGH = 'fully_rough'\r\n}\r\n\r\n/**\r\n * Material aging condition\r\n */\r\nexport enum MaterialAge {\r\n  NEW = 'new',\r\n  GOOD = 'good',\r\n  AVERAGE = 'average',\r\n  POOR = 'poor',\r\n  VERY_POOR = 'very_poor'\r\n}\r\n\r\n/**\r\n * Surface condition classification\r\n */\r\nexport enum SurfaceCondition {\r\n  EXCELLENT = 'excellent',\r\n  GOOD = 'good',\r\n  AVERAGE = 'average',\r\n  POOR = 'poor',\r\n  VERY_POOR = 'very_poor'\r\n}\r\n\r\n/**\r\n * Enhanced friction calculation input parameters\r\n */\r\nexport interface FrictionCalculationInput {\r\n  velocity: number;                    // FPM\r\n  hydraulicDiameter: number;           // inches\r\n  length: number;                      // feet\r\n  material: string;                    // Material type\r\n  method?: FrictionMethod;             // Calculation method\r\n  airConditions?: AirConditions;       // Environmental conditions\r\n  materialAge?: MaterialAge;           // Material aging condition\r\n  surfaceCondition?: SurfaceCondition; // Surface condition\r\n  customRoughness?: number;            // Custom roughness value (feet)\r\n  ductShape?: 'round' | 'rectangular' | 'oval'; // Duct shape\r\n  aspectRatio?: number;                // For rectangular ducts (width/height)\r\n  correctionFactors?: {\r\n    installation: number;              // Installation quality factor\r\n    maintenance: number;               // Maintenance factor\r\n    environmental: number;             // Environmental exposure factor\r\n  };\r\n  validationLevel?: 'none' | 'basic' | 'standard' | 'strict';\r\n}\r\n\r\n/**\r\n * Enhanced friction calculation result\r\n */\r\nexport interface FrictionCalculationResult {\r\n  frictionLoss: number;                // inches w.g.\r\n  frictionFactor: number;              // Darcy friction factor\r\n  frictionRate: number;                // inches w.g. per 100 feet\r\n  method: FrictionMethod;              // Method used\r\n  flowRegime: FlowRegime;              // Flow regime classification\r\n  reynoldsNumber: number;              // Reynolds number\r\n  relativeRoughness: number;           // Relative roughness (ε/D)\r\n  materialProperties: {\r\n    baseRoughness: number;             // Base material roughness (feet)\r\n    adjustedRoughness: number;         // Adjusted for aging/condition (feet)\r\n    agingFactor: number;               // Aging adjustment factor\r\n    surfaceFactor: number;             // Surface condition factor\r\n    combinedFactor: number;            // Combined adjustment factor\r\n  };\r\n  environmentalCorrections: {\r\n    temperature: number;               // Temperature correction factor\r\n    pressure: number;                  // Pressure correction factor\r\n    humidity: number;                  // Humidity correction factor\r\n    combined: number;                  // Combined environmental factor\r\n  };\r\n  accuracy: number;                    // Estimated accuracy (0-1)\r\n  uncertaintyBounds?: {\r\n    lower: number;                     // Lower bound (inches w.g.)\r\n    upper: number;                     // Upper bound (inches w.g.)\r\n    confidenceLevel: number;           // Confidence level (0-1)\r\n  };\r\n  warnings: string[];\r\n  recommendations: string[];\r\n  calculationDetails: {\r\n    formula: string;                   // Formula used\r\n    iterations?: number;               // Number of iterations (for iterative methods)\r\n    convergence?: number;              // Convergence criteria achieved\r\n    intermediateValues: Record<string, number>;\r\n    standardReference: string;         // Reference standard\r\n  };\r\n}\r\n\r\n/**\r\n * Enhanced Friction Calculator\r\n * \r\n * Comprehensive friction calculation service providing multiple calculation methods,\r\n * material aging effects, environmental corrections, and advanced features.\r\n */\r\nexport class EnhancedFrictionCalculator {\r\n  private static readonly VERSION = '3.0.0';\r\n  \r\n  // Standard air properties at 70°F, sea level\r\n  private static readonly STANDARD_AIR = {\r\n    density: 0.075,      // lb/ft³\r\n    viscosity: 1.2e-5    // lb/(ft·s)\r\n  };\r\n\r\n  // Reynolds number transition points\r\n  private static readonly REYNOLDS_TRANSITIONS = {\r\n    LAMINAR_MAX: 2300,\r\n    TRANSITIONAL_MAX: 4000,\r\n    TURBULENT_MIN: 4000\r\n  };\r\n\r\n  // Method accuracy estimates\r\n  private static readonly METHOD_ACCURACY = {\r\n    [FrictionMethod.COLEBROOK_WHITE]: 0.98,\r\n    [FrictionMethod.MOODY]: 0.95,\r\n    [FrictionMethod.SWAMEE_JAIN]: 0.96,\r\n    [FrictionMethod.HAALAND]: 0.97,\r\n    [FrictionMethod.CHEN]: 0.96,\r\n    [FrictionMethod.ZIGRANG_SYLVESTER]: 0.97,\r\n    [FrictionMethod.ENHANCED_DARCY]: 0.99\r\n  };\r\n\r\n  // Material aging factors\r\n  private static readonly AGING_FACTORS = {\r\n    [MaterialAge.NEW]: 1.0,\r\n    [MaterialAge.GOOD]: 1.2,\r\n    [MaterialAge.AVERAGE]: 1.5,\r\n    [MaterialAge.POOR]: 2.0,\r\n    [MaterialAge.VERY_POOR]: 3.0\r\n  };\r\n\r\n  // Surface condition factors\r\n  private static readonly SURFACE_FACTORS = {\r\n    [SurfaceCondition.EXCELLENT]: 0.8,\r\n    [SurfaceCondition.GOOD]: 1.0,\r\n    [SurfaceCondition.AVERAGE]: 1.3,\r\n    [SurfaceCondition.POOR]: 1.7,\r\n    [SurfaceCondition.VERY_POOR]: 2.5\r\n  };\r\n\r\n  /**\r\n   * Calculate enhanced friction loss with comprehensive corrections\r\n   */\r\n  public static calculateFrictionLoss(input: FrictionCalculationInput): FrictionCalculationResult {\r\n    const {\r\n      velocity,\r\n      hydraulicDiameter,\r\n      length,\r\n      material,\r\n      method = FrictionMethod.ENHANCED_DARCY,\r\n      airConditions,\r\n      materialAge = MaterialAge.GOOD,\r\n      surfaceCondition = SurfaceCondition.GOOD,\r\n      customRoughness,\r\n      ductShape = 'round',\r\n      aspectRatio,\r\n      correctionFactors,\r\n      validationLevel = 'standard'\r\n    } = input;\r\n\r\n    const warnings: string[] = [];\r\n    const recommendations: string[] = [];\r\n\r\n    // Validate inputs\r\n    this.validateInputs(input, validationLevel, warnings);\r\n\r\n    // Get air properties\r\n    const airProps = airConditions ? \r\n      AirPropertiesCalculator.calculateAirProperties(airConditions) :\r\n      { \r\n        density: this.STANDARD_AIR.density, \r\n        viscosity: this.STANDARD_AIR.viscosity,\r\n        warnings: []\r\n      };\r\n    \r\n    warnings.push(...airProps.warnings);\r\n\r\n    // Calculate Reynolds number\r\n    const velocityFps = velocity / 60; // Convert FPM to fps\r\n    const diameterFt = hydraulicDiameter / 12; // Convert inches to feet\r\n    const reynoldsNumber = (airProps.density * velocityFps * diameterFt) / airProps.viscosity;\r\n\r\n    // Determine flow regime\r\n    const flowRegime = this.classifyFlowRegime(reynoldsNumber);\r\n\r\n    // Get material properties\r\n    const materialProperties = this.getMaterialProperties(\r\n      material,\r\n      materialAge,\r\n      surfaceCondition,\r\n      customRoughness,\r\n      warnings\r\n    );\r\n\r\n    // Calculate relative roughness\r\n    const relativeRoughness = materialProperties.adjustedRoughness / diameterFt;\r\n\r\n    // Calculate friction factor using specified method\r\n    const frictionFactor = this.calculateFrictionFactor(\r\n      method,\r\n      reynoldsNumber,\r\n      relativeRoughness,\r\n      flowRegime,\r\n      warnings\r\n    );\r\n\r\n    // Calculate environmental corrections\r\n    const environmentalCorrections = this.calculateEnvironmentalCorrections(\r\n      airConditions,\r\n      airProps.density\r\n    );\r\n\r\n    // Apply shape corrections for non-round ducts\r\n    const shapeCorrection = this.calculateShapeCorrection(ductShape, aspectRatio);\r\n\r\n    // Apply additional correction factors\r\n    const additionalCorrections = this.calculateAdditionalCorrections(correctionFactors);\r\n\r\n    // Calculate friction loss using Darcy-Weisbach equation\r\n    const baseFrictionLoss = frictionFactor * (length / diameterFt) * \r\n                            (airProps.density * Math.pow(velocityFps, 2)) / (2 * 32.174);\r\n\r\n    // Apply all corrections\r\n    const totalCorrection = environmentalCorrections.combined * shapeCorrection * additionalCorrections;\r\n    const correctedFrictionLoss = (baseFrictionLoss / 5.2) * totalCorrection; // Convert to inches w.g.\r\n\r\n    // Calculate friction rate\r\n    const frictionRate = (correctedFrictionLoss / length) * 100; // inches w.g. per 100 feet\r\n\r\n    // Calculate uncertainty bounds\r\n    const uncertaintyBounds = this.calculateUncertaintyBounds(\r\n      correctedFrictionLoss,\r\n      method,\r\n      flowRegime,\r\n      relativeRoughness\r\n    );\r\n\r\n    // Generate recommendations\r\n    this.generateRecommendations(\r\n      velocity,\r\n      reynoldsNumber,\r\n      frictionRate,\r\n      materialProperties,\r\n      recommendations\r\n    );\r\n\r\n    return {\r\n      frictionLoss: correctedFrictionLoss,\r\n      frictionFactor,\r\n      frictionRate,\r\n      method,\r\n      flowRegime,\r\n      reynoldsNumber,\r\n      relativeRoughness,\r\n      materialProperties,\r\n      environmentalCorrections,\r\n      accuracy: this.METHOD_ACCURACY[method],\r\n      uncertaintyBounds,\r\n      warnings,\r\n      recommendations,\r\n      calculationDetails: {\r\n        formula: this.getFormulaDescription(method),\r\n        intermediateValues: {\r\n          baseFrictionLoss,\r\n          totalCorrection,\r\n          velocityFps,\r\n          diameterFt\r\n        },\r\n        standardReference: this.getStandardReference(method)\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get optimal friction calculation method for given conditions\r\n   */\r\n  public static getOptimalMethod(\r\n    reynoldsNumber: number,\r\n    relativeRoughness: number,\r\n    accuracy: 'standard' | 'high' | 'maximum' = 'standard'\r\n  ): FrictionMethod {\r\n    // For laminar flow\r\n    if (reynoldsNumber < this.REYNOLDS_TRANSITIONS.LAMINAR_MAX) {\r\n      return FrictionMethod.ENHANCED_DARCY; // Uses analytical solution for laminar flow\r\n    }\r\n\r\n    // For turbulent flow, choose based on accuracy requirements\r\n    if (accuracy === 'maximum') {\r\n      return FrictionMethod.COLEBROOK_WHITE; // Most accurate but iterative\r\n    }\r\n\r\n    if (accuracy === 'high') {\r\n      return FrictionMethod.HAALAND; // Good accuracy, explicit\r\n    }\r\n\r\n    // For standard accuracy, choose based on relative roughness\r\n    if (relativeRoughness < 0.001) {\r\n      return FrictionMethod.SWAMEE_JAIN; // Good for smooth pipes\r\n    } else {\r\n      return FrictionMethod.CHEN; // Good for rough pipes\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Validate input parameters\r\n   */\r\n  private static validateInputs(\r\n    input: FrictionCalculationInput,\r\n    validationLevel: string,\r\n    warnings: string[]\r\n  ): void {\r\n    if (validationLevel === 'none') return;\r\n\r\n    const { velocity, hydraulicDiameter, length } = input;\r\n\r\n    // Basic validation\r\n    if (velocity <= 0) throw new Error('Velocity must be positive');\r\n    if (hydraulicDiameter <= 0) throw new Error('Hydraulic diameter must be positive');\r\n    if (length <= 0) throw new Error('Length must be positive');\r\n\r\n    if (validationLevel === 'basic') return;\r\n\r\n    // Standard validation\r\n    if (velocity > 6000) {\r\n      warnings.push('High velocity may cause noise and energy efficiency issues');\r\n    }\r\n    if (velocity < 300) {\r\n      warnings.push('Low velocity may indicate oversized ductwork');\r\n    }\r\n\r\n    if (validationLevel === 'strict') {\r\n      // Strict validation\r\n      if (hydraulicDiameter < 4 || hydraulicDiameter > 120) {\r\n        warnings.push('Hydraulic diameter outside typical HVAC range (4-120 inches)');\r\n      }\r\n      if (length > 1000) {\r\n        warnings.push('Very long duct run - consider intermediate pressure calculations');\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Classify flow regime based on Reynolds number\r\n   */\r\n  private static classifyFlowRegime(reynoldsNumber: number): FlowRegime {\r\n    if (reynoldsNumber < this.REYNOLDS_TRANSITIONS.LAMINAR_MAX) {\r\n      return FlowRegime.LAMINAR;\r\n    } else if (reynoldsNumber < this.REYNOLDS_TRANSITIONS.TRANSITIONAL_MAX) {\r\n      return FlowRegime.TRANSITIONAL;\r\n    } else if (reynoldsNumber < 100000) {\r\n      return FlowRegime.TURBULENT_SMOOTH;\r\n    } else if (reynoldsNumber < 1000000) {\r\n      return FlowRegime.TURBULENT_ROUGH;\r\n    } else {\r\n      return FlowRegime.FULLY_ROUGH;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get material properties with aging and surface condition adjustments\r\n   */\r\n  private static getMaterialProperties(\r\n    material: string,\r\n    materialAge: MaterialAge,\r\n    surfaceCondition: SurfaceCondition,\r\n    customRoughness?: number,\r\n    warnings: string[] = []\r\n  ) {\r\n    let baseRoughness: number;\r\n\r\n    if (customRoughness !== undefined) {\r\n      baseRoughness = customRoughness;\r\n    } else {\r\n      // Get base roughness from material database\r\n      try {\r\n        const materialData = AirPropertiesCalculator.getEnhancedMaterialRoughness(\r\n          material,\r\n          materialAge,\r\n          surfaceCondition\r\n        );\r\n        baseRoughness = materialData.adjustedRoughness;\r\n        warnings.push(...materialData.warnings);\r\n      } catch (error) {\r\n        // Fallback to default values\r\n        baseRoughness = this.getDefaultRoughness(material);\r\n        warnings.push(`Using default roughness for material: ${material}`);\r\n      }\r\n    }\r\n\r\n    const agingFactor = this.AGING_FACTORS[materialAge];\r\n    const surfaceFactor = this.SURFACE_FACTORS[surfaceCondition];\r\n    const combinedFactor = agingFactor * surfaceFactor;\r\n    const adjustedRoughness = baseRoughness * combinedFactor;\r\n\r\n    return {\r\n      baseRoughness,\r\n      adjustedRoughness,\r\n      agingFactor,\r\n      surfaceFactor,\r\n      combinedFactor\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get default roughness values for common materials\r\n   */\r\n  private static getDefaultRoughness(material: string): number {\r\n    const defaultRoughness: Record<string, number> = {\r\n      'galvanized_steel': 0.0005,\r\n      'stainless_steel': 0.00015,\r\n      'aluminum': 0.00015,\r\n      'pvc': 0.000005,\r\n      'fiberglass': 0.0003,\r\n      'concrete': 0.003,\r\n      'cast_iron': 0.00085,\r\n      'flexible_duct': 0.003\r\n    };\r\n\r\n    return defaultRoughness[material] || 0.0005; // Default to galvanized steel\r\n  }\r\n\r\n  /**\r\n   * Calculate friction factor using specified method\r\n   */\r\n  private static calculateFrictionFactor(\r\n    method: FrictionMethod,\r\n    reynoldsNumber: number,\r\n    relativeRoughness: number,\r\n    flowRegime: FlowRegime,\r\n    warnings: string[]\r\n  ): number {\r\n    // For laminar flow, use analytical solution regardless of method\r\n    if (flowRegime === FlowRegime.LAMINAR) {\r\n      return 64 / reynoldsNumber;\r\n    }\r\n\r\n    switch (method) {\r\n      case FrictionMethod.COLEBROOK_WHITE:\r\n        return this.colebrookWhite(reynoldsNumber, relativeRoughness);\r\n        \r\n      case FrictionMethod.MOODY:\r\n        return this.moodyApproximation(reynoldsNumber, relativeRoughness);\r\n        \r\n      case FrictionMethod.SWAMEE_JAIN:\r\n        return this.swameeJain(reynoldsNumber, relativeRoughness);\r\n        \r\n      case FrictionMethod.HAALAND:\r\n        return this.haaland(reynoldsNumber, relativeRoughness);\r\n        \r\n      case FrictionMethod.CHEN:\r\n        return this.chen(reynoldsNumber, relativeRoughness);\r\n        \r\n      case FrictionMethod.ZIGRANG_SYLVESTER:\r\n        return this.zigrangSylvester(reynoldsNumber, relativeRoughness);\r\n        \r\n      case FrictionMethod.ENHANCED_DARCY:\r\n        return this.enhancedDarcy(reynoldsNumber, relativeRoughness, flowRegime);\r\n        \r\n      default:\r\n        warnings.push(`Unknown method ${method}, using Colebrook-White`);\r\n        return this.colebrookWhite(reynoldsNumber, relativeRoughness);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Colebrook-White equation (iterative solution)\r\n   */\r\n  private static colebrookWhite(reynoldsNumber: number, relativeRoughness: number): number {\r\n    let f = 0.02; // Initial guess\r\n    \r\n    for (let i = 0; i < 20; i++) {\r\n      const fNew = 1 / Math.pow(-2 * Math.log10(\r\n        relativeRoughness / 3.7 + 2.51 / (reynoldsNumber * Math.sqrt(f))\r\n      ), 2);\r\n      \r\n      if (Math.abs(fNew - f) < 0.0001) {\r\n        break;\r\n      }\r\n      f = fNew;\r\n    }\r\n    \r\n    return f;\r\n  }\r\n\r\n  /**\r\n   * Swamee-Jain approximation\r\n   */\r\n  private static swameeJain(reynoldsNumber: number, relativeRoughness: number): number {\r\n    const numerator = Math.pow(Math.log10(relativeRoughness / 3.7 + 5.74 / Math.pow(reynoldsNumber, 0.9)), 2);\r\n    return 0.25 / numerator;\r\n  }\r\n\r\n  /**\r\n   * Haaland approximation\r\n   */\r\n  private static haaland(reynoldsNumber: number, relativeRoughness: number): number {\r\n    const term1 = relativeRoughness / 3.7;\r\n    const term2 = 6.9 / reynoldsNumber;\r\n    return 1 / Math.pow(-1.8 * Math.log10(Math.pow(term1, 1.11) + term2), 2);\r\n  }\r\n\r\n  /**\r\n   * Chen approximation\r\n   */\r\n  private static chen(reynoldsNumber: number, relativeRoughness: number): number {\r\n    const A = Math.pow(Math.log10(relativeRoughness / 3.7065 - 5.0452 / reynoldsNumber * \r\n                      Math.log10(Math.pow(relativeRoughness, 1.1098) / 2.8257 + \r\n                      Math.pow(7.149 / reynoldsNumber, 0.8981))), 2);\r\n    return 1 / (4 * A);\r\n  }\r\n\r\n  /**\r\n   * Zigrang-Sylvester approximation\r\n   */\r\n  private static zigrangSylvester(reynoldsNumber: number, relativeRoughness: number): number {\r\n    const A = -2 * Math.log10(relativeRoughness / 3.7 + 5.02 / reynoldsNumber * \r\n              Math.log10(relativeRoughness / 3.7 + 5.02 / reynoldsNumber * \r\n              Math.log10(relativeRoughness / 3.7 + 13 / reynoldsNumber)));\r\n    return 1 / Math.pow(A, 2);\r\n  }\r\n\r\n  /**\r\n   * Moody diagram approximation\r\n   */\r\n  private static moodyApproximation(reynoldsNumber: number, relativeRoughness: number): number {\r\n    // Simplified Moody approximation\r\n    if (relativeRoughness < 0.0001) {\r\n      // Smooth pipe approximation\r\n      return 0.316 / Math.pow(reynoldsNumber, 0.25);\r\n    } else {\r\n      // Rough pipe approximation\r\n      return this.swameeJain(reynoldsNumber, relativeRoughness);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Enhanced Darcy method with flow regime optimization\r\n   */\r\n  private static enhancedDarcy(\r\n    reynoldsNumber: number, \r\n    relativeRoughness: number, \r\n    flowRegime: FlowRegime\r\n  ): number {\r\n    switch (flowRegime) {\r\n      case FlowRegime.LAMINAR:\r\n        return 64 / reynoldsNumber;\r\n        \r\n      case FlowRegime.TRANSITIONAL:\r\n        // Interpolate between laminar and turbulent\r\n        const fLaminar = 64 / reynoldsNumber;\r\n        const fTurbulent = this.colebrookWhite(4000, relativeRoughness);\r\n        const weight = (reynoldsNumber - 2300) / (4000 - 2300);\r\n        return fLaminar * (1 - weight) + fTurbulent * weight;\r\n        \r\n      case FlowRegime.TURBULENT_SMOOTH:\r\n        return this.haaland(reynoldsNumber, relativeRoughness);\r\n        \r\n      case FlowRegime.TURBULENT_ROUGH:\r\n      case FlowRegime.FULLY_ROUGH:\r\n        return this.colebrookWhite(reynoldsNumber, relativeRoughness);\r\n        \r\n      default:\r\n        return this.colebrookWhite(reynoldsNumber, relativeRoughness);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Calculate environmental corrections\r\n   */\r\n  private static calculateEnvironmentalCorrections(\r\n    airConditions?: AirConditions,\r\n    airDensity?: number\r\n  ) {\r\n    const corrections = {\r\n      temperature: 1.0,\r\n      pressure: 1.0,\r\n      humidity: 1.0,\r\n      combined: 1.0\r\n    };\r\n\r\n    if (airDensity) {\r\n      const densityRatio = airDensity / this.STANDARD_AIR.density;\r\n      corrections.temperature = densityRatio;\r\n      corrections.pressure = densityRatio;\r\n      corrections.humidity = densityRatio;\r\n      corrections.combined = densityRatio;\r\n    }\r\n\r\n    return corrections;\r\n  }\r\n\r\n  /**\r\n   * Calculate shape correction for non-round ducts\r\n   */\r\n  private static calculateShapeCorrection(\r\n    ductShape: string,\r\n    aspectRatio?: number\r\n  ): number {\r\n    if (ductShape === 'round') {\r\n      return 1.0;\r\n    }\r\n\r\n    if (ductShape === 'rectangular' && aspectRatio) {\r\n      // Correction for rectangular ducts based on aspect ratio\r\n      if (aspectRatio <= 1.5) return 1.0;\r\n      if (aspectRatio <= 2.0) return 1.05;\r\n      if (aspectRatio <= 3.0) return 1.10;\r\n      if (aspectRatio <= 4.0) return 1.15;\r\n      return 1.20; // High aspect ratio penalty\r\n    }\r\n\r\n    return 1.05; // Default correction for non-round shapes\r\n  }\r\n\r\n  /**\r\n   * Calculate additional correction factors\r\n   */\r\n  private static calculateAdditionalCorrections(\r\n    correctionFactors?: FrictionCalculationInput['correctionFactors']\r\n  ): number {\r\n    if (!correctionFactors) return 1.0;\r\n\r\n    const { installation = 1.0, maintenance = 1.0, environmental = 1.0 } = correctionFactors;\r\n    return installation * maintenance * environmental;\r\n  }\r\n\r\n  /**\r\n   * Calculate uncertainty bounds\r\n   */\r\n  private static calculateUncertaintyBounds(\r\n    frictionLoss: number,\r\n    method: FrictionMethod,\r\n    flowRegime: FlowRegime,\r\n    relativeRoughness: number\r\n  ) {\r\n    let baseAccuracy = this.METHOD_ACCURACY[method];\r\n\r\n    // Adjust accuracy based on flow regime\r\n    if (flowRegime === FlowRegime.TRANSITIONAL) {\r\n      baseAccuracy *= 0.9; // Reduced accuracy in transitional regime\r\n    }\r\n\r\n    // Adjust accuracy based on relative roughness\r\n    if (relativeRoughness > 0.05) {\r\n      baseAccuracy *= 0.95; // Reduced accuracy for very rough surfaces\r\n    }\r\n\r\n    const uncertainty = frictionLoss * (1 - baseAccuracy);\r\n\r\n    return {\r\n      lower: frictionLoss - uncertainty,\r\n      upper: frictionLoss + uncertainty,\r\n      confidenceLevel: baseAccuracy\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Generate recommendations\r\n   */\r\n  private static generateRecommendations(\r\n    velocity: number,\r\n    reynoldsNumber: number,\r\n    frictionRate: number,\r\n    materialProperties: any,\r\n    recommendations: string[]\r\n  ): void {\r\n    // Velocity recommendations\r\n    if (velocity > 4000) {\r\n      recommendations.push('High velocity detected - consider larger duct size to reduce friction and noise');\r\n    }\r\n\r\n    // Friction rate recommendations\r\n    if (frictionRate > 0.15) {\r\n      recommendations.push('High friction rate - consider smoother materials or larger ducts');\r\n    }\r\n\r\n    // Material recommendations\r\n    if (materialProperties.combinedFactor > 2.0) {\r\n      recommendations.push('Material aging/condition significantly affects friction - consider cleaning or replacement');\r\n    }\r\n\r\n    // Flow regime recommendations\r\n    if (reynoldsNumber < 4000) {\r\n      recommendations.push('Low Reynolds number - verify velocity calculations and consider system optimization');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get formula description for the method\r\n   */\r\n  private static getFormulaDescription(method: FrictionMethod): string {\r\n    const descriptions: Record<FrictionMethod, string> = {\r\n      [FrictionMethod.COLEBROOK_WHITE]: '1/√f = -2log₁₀(ε/3.7D + 2.51/(Re√f))',\r\n      [FrictionMethod.MOODY]: 'Moody diagram approximation',\r\n      [FrictionMethod.SWAMEE_JAIN]: 'f = 0.25/[log₁₀(ε/3.7D + 5.74/Re^0.9)]²',\r\n      [FrictionMethod.HAALAND]: '1/√f = -1.8log₁₀[(ε/3.7D)^1.11 + 6.9/Re]',\r\n      [FrictionMethod.CHEN]: 'Chen explicit approximation',\r\n      [FrictionMethod.ZIGRANG_SYLVESTER]: 'Zigrang-Sylvester explicit approximation',\r\n      [FrictionMethod.ENHANCED_DARCY]: 'Flow regime optimized Darcy-Weisbach'\r\n    };\r\n\r\n    return descriptions[method] || 'Unknown method';\r\n  }\r\n\r\n  /**\r\n   * Get standard reference for the method\r\n   */\r\n  private static getStandardReference(method: FrictionMethod): string {\r\n    return 'ASHRAE Fundamentals, Chapter 21 - Duct Design';\r\n  }\r\n}\r\n"], "mappings": ";;AAAA;;;;;;;;;;AAAA;AAAA,SAAAA,cAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;MAAA;QAAAC,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAO,SAAA;MAAA;QAAAD,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAW,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,cAAA;MAAAC,IAAA;MAAAC,QAAA;MAAAC,KAAA;MAAAC,OAAA;MAAAC,cAAA;MAAAC,OAAA;IAAA;IAAAC,eAAA;IAAA5B,IAAA;EAAA;EAAA,IAAA6B,QAAA,GAAA5B,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAA0B,QAAA,CAAA9B,IAAA,KAAA8B,QAAA,CAAA9B,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAA6B,QAAA,CAAA9B,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAA0B,cAAA,GAAAD,QAAA,CAAA9B,IAAA;EAAA;;;;;;;;;;;;;;;;AAWA,MAAAgC,yBAAA;AAAA;AAAA,CAAAjC,aAAA,GAAAoB,CAAA,OAAAc,OAAA;AAEA;;;AAGA,IAAYC,cAQX;AAAA;AAAAnC,aAAA,GAAAoB,CAAA;AARD,WAAYe,cAAc;EAAA;EAAAnC,aAAA,GAAAqB,CAAA;EAAArB,aAAA,GAAAoB,CAAA;EACxBe,cAAA,uCAAmC;EAAA;EAAAnC,aAAA,GAAAoB,CAAA;EACnCe,cAAA,mBAAe;EAAA;EAAAnC,aAAA,GAAAoB,CAAA;EACfe,cAAA,+BAA2B;EAAA;EAAAnC,aAAA,GAAAoB,CAAA;EAC3Be,cAAA,uBAAmB;EAAA;EAAAnC,aAAA,GAAAoB,CAAA;EACnBe,cAAA,iBAAa;EAAA;EAAAnC,aAAA,GAAAoB,CAAA;EACbe,cAAA,2CAAuC;EAAA;EAAAnC,aAAA,GAAAoB,CAAA;EACvCe,cAAA,qCAAiC;AACnC,CAAC;AARW;AAAA,CAAAnC,aAAA,GAAAsB,CAAA,UAAAa,cAAc;AAAA;AAAA,CAAAnC,aAAA,GAAAsB,CAAA,UAAAc,OAAA,CAAAD,cAAA,GAAdA,cAAc;AAU1B;;;AAGA,IAAYE,UAMX;AAAA;AAAArC,aAAA,GAAAoB,CAAA;AAND,WAAYiB,UAAU;EAAA;EAAArC,aAAA,GAAAqB,CAAA;EAAArB,aAAA,GAAAoB,CAAA;EACpBiB,UAAA,uBAAmB;EAAA;EAAArC,aAAA,GAAAoB,CAAA;EACnBiB,UAAA,iCAA6B;EAAA;EAAArC,aAAA,GAAAoB,CAAA;EAC7BiB,UAAA,yCAAqC;EAAA;EAAArC,aAAA,GAAAoB,CAAA;EACrCiB,UAAA,uCAAmC;EAAA;EAAArC,aAAA,GAAAoB,CAAA;EACnCiB,UAAA,+BAA2B;AAC7B,CAAC;AANW;AAAA,CAAArC,aAAA,GAAAsB,CAAA,UAAAe,UAAU;AAAA;AAAA,CAAArC,aAAA,GAAAsB,CAAA,UAAAc,OAAA,CAAAC,UAAA,GAAVA,UAAU;AAQtB;;;AAGA,IAAYC,WAMX;AAAA;AAAAtC,aAAA,GAAAoB,CAAA;AAND,WAAYkB,WAAW;EAAA;EAAAtC,aAAA,GAAAqB,CAAA;EAAArB,aAAA,GAAAoB,CAAA;EACrBkB,WAAA,eAAW;EAAA;EAAAtC,aAAA,GAAAoB,CAAA;EACXkB,WAAA,iBAAa;EAAA;EAAAtC,aAAA,GAAAoB,CAAA;EACbkB,WAAA,uBAAmB;EAAA;EAAAtC,aAAA,GAAAoB,CAAA;EACnBkB,WAAA,iBAAa;EAAA;EAAAtC,aAAA,GAAAoB,CAAA;EACbkB,WAAA,2BAAuB;AACzB,CAAC;AANW;AAAA,CAAAtC,aAAA,GAAAsB,CAAA,UAAAgB,WAAW;AAAA;AAAA,CAAAtC,aAAA,GAAAsB,CAAA,UAAAc,OAAA,CAAAE,WAAA,GAAXA,WAAW;AAQvB;;;AAGA,IAAYC,gBAMX;AAAA;AAAAvC,aAAA,GAAAoB,CAAA;AAND,WAAYmB,gBAAgB;EAAA;EAAAvC,aAAA,GAAAqB,CAAA;EAAArB,aAAA,GAAAoB,CAAA;EAC1BmB,gBAAA,2BAAuB;EAAA;EAAAvC,aAAA,GAAAoB,CAAA;EACvBmB,gBAAA,iBAAa;EAAA;EAAAvC,aAAA,GAAAoB,CAAA;EACbmB,gBAAA,uBAAmB;EAAA;EAAAvC,aAAA,GAAAoB,CAAA;EACnBmB,gBAAA,iBAAa;EAAA;EAAAvC,aAAA,GAAAoB,CAAA;EACbmB,gBAAA,2BAAuB;AACzB,CAAC;AANW;AAAA,CAAAvC,aAAA,GAAAsB,CAAA,UAAAiB,gBAAgB;AAAA;AAAA,CAAAvC,aAAA,GAAAsB,CAAA,UAAAc,OAAA,CAAAG,gBAAA,GAAhBA,gBAAgB;AAwE5B;;;;;;AAMA,MAAaC,0BAA0B;EA6CrC;;;EAGO,OAAOC,qBAAqBA,CAACC,KAA+B;IAAA;IAAA1C,aAAA,GAAAqB,CAAA;IACjE,MAAM;MACJsB,QAAQ;MACRC,iBAAiB;MACjBC,MAAM;MACNC,QAAQ;MACRC,MAAM;MAAA;MAAA,CAAA/C,aAAA,GAAAsB,CAAA,UAAGa,cAAc,CAACa,cAAc;MACtCC,aAAa;MACbC,WAAW;MAAA;MAAA,CAAAlD,aAAA,GAAAsB,CAAA,UAAGgB,WAAW,CAACa,IAAI;MAC9BC,gBAAgB;MAAA;MAAA,CAAApD,aAAA,GAAAsB,CAAA,UAAGiB,gBAAgB,CAACY,IAAI;MACxCE,eAAe;MACfC,SAAS;MAAA;MAAA,CAAAtD,aAAA,GAAAsB,CAAA,UAAG,OAAO;MACnBiC,WAAW;MACXC,iBAAiB;MACjBC,eAAe;MAAA;MAAA,CAAAzD,aAAA,GAAAsB,CAAA,UAAG,UAAU;IAAA,CAC7B;IAAA;IAAA,CAAAtB,aAAA,GAAAoB,CAAA,QAAGsB,KAAK;IAET,MAAMgB,QAAQ;IAAA;IAAA,CAAA1D,aAAA,GAAAoB,CAAA,QAAa,EAAE;IAC7B,MAAMuC,eAAe;IAAA;IAAA,CAAA3D,aAAA,GAAAoB,CAAA,QAAa,EAAE;IAEpC;IAAA;IAAApB,aAAA,GAAAoB,CAAA;IACA,IAAI,CAACwC,cAAc,CAAClB,KAAK,EAAEe,eAAe,EAAEC,QAAQ,CAAC;IAErD;IACA,MAAMG,QAAQ;IAAA;IAAA,CAAA7D,aAAA,GAAAoB,CAAA,QAAG6B,aAAa;IAAA;IAAA,CAAAjD,aAAA,GAAAsB,CAAA,UAC5BW,yBAAA,CAAA6B,uBAAuB,CAACC,sBAAsB,CAACd,aAAa,CAAC;IAAA;IAAA,CAAAjD,aAAA,GAAAsB,CAAA,UAC7D;MACE0C,OAAO,EAAE,IAAI,CAACC,YAAY,CAACD,OAAO;MAClCE,SAAS,EAAE,IAAI,CAACD,YAAY,CAACC,SAAS;MACtCR,QAAQ,EAAE;KACX;IAAC;IAAA1D,aAAA,GAAAoB,CAAA;IAEJsC,QAAQ,CAACS,IAAI,CAAC,GAAGN,QAAQ,CAACH,QAAQ,CAAC;IAEnC;IACA,MAAMU,WAAW;IAAA;IAAA,CAAApE,aAAA,GAAAoB,CAAA,QAAGuB,QAAQ,GAAG,EAAE,EAAC,CAAC;IACnC,MAAM0B,UAAU;IAAA;IAAA,CAAArE,aAAA,GAAAoB,CAAA,QAAGwB,iBAAiB,GAAG,EAAE,EAAC,CAAC;IAC3C,MAAM0B,cAAc;IAAA;IAAA,CAAAtE,aAAA,GAAAoB,CAAA,QAAIyC,QAAQ,CAACG,OAAO,GAAGI,WAAW,GAAGC,UAAU,GAAIR,QAAQ,CAACK,SAAS;IAEzF;IACA,MAAMK,UAAU;IAAA;IAAA,CAAAvE,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAACoD,kBAAkB,CAACF,cAAc,CAAC;IAE1D;IACA,MAAMG,kBAAkB;IAAA;IAAA,CAAAzE,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAACsD,qBAAqB,CACnD5B,QAAQ,EACRI,WAAW,EACXE,gBAAgB,EAChBC,eAAe,EACfK,QAAQ,CACT;IAED;IACA,MAAMiB,iBAAiB;IAAA;IAAA,CAAA3E,aAAA,GAAAoB,CAAA,QAAGqD,kBAAkB,CAACG,iBAAiB,GAAGP,UAAU;IAE3E;IACA,MAAMQ,cAAc;IAAA;IAAA,CAAA7E,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAAC0D,uBAAuB,CACjD/B,MAAM,EACNuB,cAAc,EACdK,iBAAiB,EACjBJ,UAAU,EACVb,QAAQ,CACT;IAED;IACA,MAAMqB,wBAAwB;IAAA;IAAA,CAAA/E,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAAC4D,iCAAiC,CACrE/B,aAAa,EACbY,QAAQ,CAACG,OAAO,CACjB;IAED;IACA,MAAMiB,eAAe;IAAA;IAAA,CAAAjF,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAAC8D,wBAAwB,CAAC5B,SAAS,EAAEC,WAAW,CAAC;IAE7E;IACA,MAAM4B,qBAAqB;IAAA;IAAA,CAAAnF,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAACgE,8BAA8B,CAAC5B,iBAAiB,CAAC;IAEpF;IACA,MAAM6B,gBAAgB;IAAA;IAAA,CAAArF,aAAA,GAAAoB,CAAA,QAAGyD,cAAc,IAAIhC,MAAM,GAAGwB,UAAU,CAAC,IACtCR,QAAQ,CAACG,OAAO,GAAGsB,IAAI,CAACC,GAAG,CAACnB,WAAW,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;IAEpF;IACA,MAAMoB,eAAe;IAAA;IAAA,CAAAxF,aAAA,GAAAoB,CAAA,QAAG2D,wBAAwB,CAACU,QAAQ,GAAGR,eAAe,GAAGE,qBAAqB;IACnG,MAAMO,qBAAqB;IAAA;IAAA,CAAA1F,aAAA,GAAAoB,CAAA,QAAIiE,gBAAgB,GAAG,GAAG,GAAIG,eAAe,EAAC,CAAC;IAE1E;IACA,MAAMG,YAAY;IAAA;IAAA,CAAA3F,aAAA,GAAAoB,CAAA,QAAIsE,qBAAqB,GAAG7C,MAAM,GAAI,GAAG,EAAC,CAAC;IAE7D;IACA,MAAM+C,iBAAiB;IAAA;IAAA,CAAA5F,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAACyE,0BAA0B,CACvDH,qBAAqB,EACrB3C,MAAM,EACNwB,UAAU,EACVI,iBAAiB,CAClB;IAED;IAAA;IAAA3E,aAAA,GAAAoB,CAAA;IACA,IAAI,CAAC0E,uBAAuB,CAC1BnD,QAAQ,EACR2B,cAAc,EACdqB,YAAY,EACZlB,kBAAkB,EAClBd,eAAe,CAChB;IAAC;IAAA3D,aAAA,GAAAoB,CAAA;IAEF,OAAO;MACL2E,YAAY,EAAEL,qBAAqB;MACnCb,cAAc;MACdc,YAAY;MACZ5C,MAAM;MACNwB,UAAU;MACVD,cAAc;MACdK,iBAAiB;MACjBF,kBAAkB;MAClBM,wBAAwB;MACxBiB,QAAQ,EAAE,IAAI,CAACC,eAAe,CAAClD,MAAM,CAAC;MACtC6C,iBAAiB;MACjBlC,QAAQ;MACRC,eAAe;MACfuC,kBAAkB,EAAE;QAClBC,OAAO,EAAE,IAAI,CAACC,qBAAqB,CAACrD,MAAM,CAAC;QAC3CsD,kBAAkB,EAAE;UAClBhB,gBAAgB;UAChBG,eAAe;UACfpB,WAAW;UACXC;SACD;QACDiC,iBAAiB,EAAE,IAAI,CAACC,oBAAoB,CAACxD,MAAM;;KAEtD;EACH;EAEA;;;EAGO,OAAOyD,gBAAgBA,CAC5BlC,cAAsB,EACtBK,iBAAyB,EACzBqB,QAAA;EAAA;EAAA,CAAAhG,aAAA,GAAAsB,CAAA,WAA4C,UAAU;IAAA;IAAAtB,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAEtD;IACA,IAAIkD,cAAc,GAAG,IAAI,CAACmC,oBAAoB,CAACC,WAAW,EAAE;MAAA;MAAA1G,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAC1D,OAAOe,cAAc,CAACa,cAAc,CAAC,CAAC;IACxC,CAAC;IAAA;IAAA;MAAAhD,aAAA,GAAAsB,CAAA;IAAA;IAED;IAAAtB,aAAA,GAAAoB,CAAA;IACA,IAAI4E,QAAQ,KAAK,SAAS,EAAE;MAAA;MAAAhG,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAC1B,OAAOe,cAAc,CAACwE,eAAe,CAAC,CAAC;IACzC,CAAC;IAAA;IAAA;MAAA3G,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAED,IAAI4E,QAAQ,KAAK,MAAM,EAAE;MAAA;MAAAhG,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACvB,OAAOe,cAAc,CAACyE,OAAO,CAAC,CAAC;IACjC,CAAC;IAAA;IAAA;MAAA5G,aAAA,GAAAsB,CAAA;IAAA;IAED;IAAAtB,aAAA,GAAAoB,CAAA;IACA,IAAIuD,iBAAiB,GAAG,KAAK,EAAE;MAAA;MAAA3E,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAC7B,OAAOe,cAAc,CAAC0E,WAAW,CAAC,CAAC;IACrC,CAAC,MAAM;MAAA;MAAA7G,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACL,OAAOe,cAAc,CAAC2E,IAAI,CAAC,CAAC;IAC9B;EACF;EAEA;;;EAGQ,OAAOlD,cAAcA,CAC3BlB,KAA+B,EAC/Be,eAAuB,EACvBC,QAAkB;IAAA;IAAA1D,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAElB,IAAIqC,eAAe,KAAK,MAAM,EAAE;MAAA;MAAAzD,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA;IAAA,CAAO;IAAA;IAAA;MAAApB,aAAA,GAAAsB,CAAA;IAAA;IAEvC,MAAM;MAAEqB,QAAQ;MAAEC,iBAAiB;MAAEC;IAAM,CAAE;IAAA;IAAA,CAAA7C,aAAA,GAAAoB,CAAA,QAAGsB,KAAK;IAErD;IAAA;IAAA1C,aAAA,GAAAoB,CAAA;IACA,IAAIuB,QAAQ,IAAI,CAAC,EAAE;MAAA;MAAA3C,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA,MAAM,IAAI2F,KAAK,CAAC,2BAA2B,CAAC;IAAA,CAAC;IAAA;IAAA;MAAA/G,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAChE,IAAIwB,iBAAiB,IAAI,CAAC,EAAE;MAAA;MAAA5C,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA,MAAM,IAAI2F,KAAK,CAAC,qCAAqC,CAAC;IAAA,CAAC;IAAA;IAAA;MAAA/G,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IACnF,IAAIyB,MAAM,IAAI,CAAC,EAAE;MAAA;MAAA7C,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA,MAAM,IAAI2F,KAAK,CAAC,yBAAyB,CAAC;IAAA,CAAC;IAAA;IAAA;MAAA/G,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAE5D,IAAIqC,eAAe,KAAK,OAAO,EAAE;MAAA;MAAAzD,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA;IAAA,CAAO;IAAA;IAAA;MAAApB,aAAA,GAAAsB,CAAA;IAAA;IAExC;IAAAtB,aAAA,GAAAoB,CAAA;IACA,IAAIuB,QAAQ,GAAG,IAAI,EAAE;MAAA;MAAA3C,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACnBsC,QAAQ,CAACS,IAAI,CAAC,4DAA4D,CAAC;IAC7E,CAAC;IAAA;IAAA;MAAAnE,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IACD,IAAIuB,QAAQ,GAAG,GAAG,EAAE;MAAA;MAAA3C,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAClBsC,QAAQ,CAACS,IAAI,CAAC,8CAA8C,CAAC;IAC/D,CAAC;IAAA;IAAA;MAAAnE,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAED,IAAIqC,eAAe,KAAK,QAAQ,EAAE;MAAA;MAAAzD,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAChC;MACA;MAAI;MAAA,CAAApB,aAAA,GAAAsB,CAAA,WAAAsB,iBAAiB,GAAG,CAAC;MAAA;MAAA,CAAA5C,aAAA,GAAAsB,CAAA,WAAIsB,iBAAiB,GAAG,GAAG,GAAE;QAAA;QAAA5C,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACpDsC,QAAQ,CAACS,IAAI,CAAC,8DAA8D,CAAC;MAC/E,CAAC;MAAA;MAAA;QAAAnE,aAAA,GAAAsB,CAAA;MAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACD,IAAIyB,MAAM,GAAG,IAAI,EAAE;QAAA;QAAA7C,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACjBsC,QAAQ,CAACS,IAAI,CAAC,kEAAkE,CAAC;MACnF,CAAC;MAAA;MAAA;QAAAnE,aAAA,GAAAsB,CAAA;MAAA;IACH,CAAC;IAAA;IAAA;MAAAtB,aAAA,GAAAsB,CAAA;IAAA;EACH;EAEA;;;EAGQ,OAAOkD,kBAAkBA,CAACF,cAAsB;IAAA;IAAAtE,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACtD,IAAIkD,cAAc,GAAG,IAAI,CAACmC,oBAAoB,CAACC,WAAW,EAAE;MAAA;MAAA1G,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAC1D,OAAOiB,UAAU,CAAC2E,OAAO;IAC3B,CAAC,MAAM;MAAA;MAAAhH,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA,IAAIkD,cAAc,GAAG,IAAI,CAACmC,oBAAoB,CAACQ,gBAAgB,EAAE;QAAA;QAAAjH,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACtE,OAAOiB,UAAU,CAAC6E,YAAY;MAChC,CAAC,MAAM;QAAA;QAAAlH,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QAAA,IAAIkD,cAAc,GAAG,MAAM,EAAE;UAAA;UAAAtE,aAAA,GAAAsB,CAAA;UAAAtB,aAAA,GAAAoB,CAAA;UAClC,OAAOiB,UAAU,CAAC8E,gBAAgB;QACpC,CAAC,MAAM;UAAA;UAAAnH,aAAA,GAAAsB,CAAA;UAAAtB,aAAA,GAAAoB,CAAA;UAAA,IAAIkD,cAAc,GAAG,OAAO,EAAE;YAAA;YAAAtE,aAAA,GAAAsB,CAAA;YAAAtB,aAAA,GAAAoB,CAAA;YACnC,OAAOiB,UAAU,CAAC+E,eAAe;UACnC,CAAC,MAAM;YAAA;YAAApH,aAAA,GAAAsB,CAAA;YAAAtB,aAAA,GAAAoB,CAAA;YACL,OAAOiB,UAAU,CAACgF,WAAW;UAC/B;QAAA;MAAA;IAAA;EACF;EAEA;;;EAGQ,OAAO3C,qBAAqBA,CAClC5B,QAAgB,EAChBI,WAAwB,EACxBE,gBAAkC,EAClCC,eAAwB,EACxBK,QAAA;EAAA;EAAA,CAAA1D,aAAA,GAAAsB,CAAA,WAAqB,EAAE;IAAA;IAAAtB,aAAA,GAAAqB,CAAA;IAEvB,IAAIiG,aAAqB;IAAC;IAAAtH,aAAA,GAAAoB,CAAA;IAE1B,IAAIiC,eAAe,KAAKlC,SAAS,EAAE;MAAA;MAAAnB,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACjCkG,aAAa,GAAGjE,eAAe;IACjC,CAAC,MAAM;MAAA;MAAArD,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACL;MACA,IAAI;QACF,MAAMmG,YAAY;QAAA;QAAA,CAAAvH,aAAA,GAAAoB,CAAA,QAAGa,yBAAA,CAAA6B,uBAAuB,CAAC0D,4BAA4B,CACvE1E,QAAQ,EACRI,WAAW,EACXE,gBAAgB,CACjB;QAAC;QAAApD,aAAA,GAAAoB,CAAA;QACFkG,aAAa,GAAGC,YAAY,CAAC3C,iBAAiB;QAAC;QAAA5E,aAAA,GAAAoB,CAAA;QAC/CsC,QAAQ,CAACS,IAAI,CAAC,GAAGoD,YAAY,CAAC7D,QAAQ,CAAC;MACzC,CAAC,CAAC,OAAO+D,KAAK,EAAE;QAAA;QAAAzH,aAAA,GAAAoB,CAAA;QACd;QACAkG,aAAa,GAAG,IAAI,CAACI,mBAAmB,CAAC5E,QAAQ,CAAC;QAAC;QAAA9C,aAAA,GAAAoB,CAAA;QACnDsC,QAAQ,CAACS,IAAI,CAAC,yCAAyCrB,QAAQ,EAAE,CAAC;MACpE;IACF;IAEA,MAAM6E,WAAW;IAAA;IAAA,CAAA3H,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAACwG,aAAa,CAAC1E,WAAW,CAAC;IACnD,MAAM2E,aAAa;IAAA;IAAA,CAAA7H,aAAA,GAAAoB,CAAA,QAAG,IAAI,CAAC0G,eAAe,CAAC1E,gBAAgB,CAAC;IAC5D,MAAM2E,cAAc;IAAA;IAAA,CAAA/H,aAAA,GAAAoB,CAAA,SAAGuG,WAAW,GAAGE,aAAa;IAClD,MAAMjD,iBAAiB;IAAA;IAAA,CAAA5E,aAAA,GAAAoB,CAAA,SAAGkG,aAAa,GAAGS,cAAc;IAAC;IAAA/H,aAAA,GAAAoB,CAAA;IAEzD,OAAO;MACLkG,aAAa;MACb1C,iBAAiB;MACjB+C,WAAW;MACXE,aAAa;MACbE;KACD;EACH;EAEA;;;EAGQ,OAAOL,mBAAmBA,CAAC5E,QAAgB;IAAA;IAAA9C,aAAA,GAAAqB,CAAA;IACjD,MAAM2G,gBAAgB;IAAA;IAAA,CAAAhI,aAAA,GAAAoB,CAAA,SAA2B;MAC/C,kBAAkB,EAAE,MAAM;MAC1B,iBAAiB,EAAE,OAAO;MAC1B,UAAU,EAAE,OAAO;MACnB,KAAK,EAAE,QAAQ;MACf,YAAY,EAAE,MAAM;MACpB,UAAU,EAAE,KAAK;MACjB,WAAW,EAAE,OAAO;MACpB,eAAe,EAAE;KAClB;IAAC;IAAApB,aAAA,GAAAoB,CAAA;IAEF,OAAO,2BAAApB,aAAA,GAAAsB,CAAA,WAAA0G,gBAAgB,CAAClF,QAAQ,CAAC;IAAA;IAAA,CAAA9C,aAAA,GAAAsB,CAAA,WAAI,MAAM,EAAC,CAAC;EAC/C;EAEA;;;EAGQ,OAAOwD,uBAAuBA,CACpC/B,MAAsB,EACtBuB,cAAsB,EACtBK,iBAAyB,EACzBJ,UAAsB,EACtBb,QAAkB;IAAA;IAAA1D,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAElB;IACA,IAAImD,UAAU,KAAKlC,UAAU,CAAC2E,OAAO,EAAE;MAAA;MAAAhH,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACrC,OAAO,EAAE,GAAGkD,cAAc;IAC5B,CAAC;IAAA;IAAA;MAAAtE,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAED,QAAQ2B,MAAM;MACZ,KAAKZ,cAAc,CAACwE,eAAe;QAAA;QAAA3G,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACjC,OAAO,IAAI,CAAC6G,cAAc,CAAC3D,cAAc,EAAEK,iBAAiB,CAAC;MAE/D,KAAKxC,cAAc,CAAC+F,KAAK;QAAA;QAAAlI,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACvB,OAAO,IAAI,CAAC+G,kBAAkB,CAAC7D,cAAc,EAAEK,iBAAiB,CAAC;MAEnE,KAAKxC,cAAc,CAAC0E,WAAW;QAAA;QAAA7G,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QAC7B,OAAO,IAAI,CAACgH,UAAU,CAAC9D,cAAc,EAAEK,iBAAiB,CAAC;MAE3D,KAAKxC,cAAc,CAACyE,OAAO;QAAA;QAAA5G,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACzB,OAAO,IAAI,CAACiH,OAAO,CAAC/D,cAAc,EAAEK,iBAAiB,CAAC;MAExD,KAAKxC,cAAc,CAAC2E,IAAI;QAAA;QAAA9G,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACtB,OAAO,IAAI,CAACkH,IAAI,CAAChE,cAAc,EAAEK,iBAAiB,CAAC;MAErD,KAAKxC,cAAc,CAACoG,iBAAiB;QAAA;QAAAvI,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACnC,OAAO,IAAI,CAACoH,gBAAgB,CAAClE,cAAc,EAAEK,iBAAiB,CAAC;MAEjE,KAAKxC,cAAc,CAACa,cAAc;QAAA;QAAAhD,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QAChC,OAAO,IAAI,CAACqH,aAAa,CAACnE,cAAc,EAAEK,iBAAiB,EAAEJ,UAAU,CAAC;MAE1E;QAAA;QAAAvE,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACEsC,QAAQ,CAACS,IAAI,CAAC,kBAAkBpB,MAAM,yBAAyB,CAAC;QAAC;QAAA/C,aAAA,GAAAoB,CAAA;QACjE,OAAO,IAAI,CAAC6G,cAAc,CAAC3D,cAAc,EAAEK,iBAAiB,CAAC;IACjE;EACF;EAEA;;;EAGQ,OAAOsD,cAAcA,CAAC3D,cAAsB,EAAEK,iBAAyB;IAAA;IAAA3E,aAAA,GAAAqB,CAAA;IAC7E,IAAIA,CAAC;IAAA;IAAA,CAAArB,aAAA,GAAAoB,CAAA,SAAG,IAAI,EAAC,CAAC;IAAA;IAAApB,aAAA,GAAAoB,CAAA;IAEd,KAAK,IAAIsH,CAAC;IAAA;IAAA,CAAA1I,aAAA,GAAAoB,CAAA,SAAG,CAAC,GAAEsH,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC3B,MAAMC,IAAI;MAAA;MAAA,CAAA3I,aAAA,GAAAoB,CAAA,SAAG,CAAC,GAAGkE,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,GAAGD,IAAI,CAACsD,KAAK,CACvCjE,iBAAiB,GAAG,GAAG,GAAG,IAAI,IAAIL,cAAc,GAAGgB,IAAI,CAACuD,IAAI,CAACxH,CAAC,CAAC,CAAC,CACjE,EAAE,CAAC,CAAC;MAAC;MAAArB,aAAA,GAAAoB,CAAA;MAEN,IAAIkE,IAAI,CAACwD,GAAG,CAACH,IAAI,GAAGtH,CAAC,CAAC,GAAG,MAAM,EAAE;QAAA;QAAArB,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QAC/B;MACF,CAAC;MAAA;MAAA;QAAApB,aAAA,GAAAsB,CAAA;MAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACDC,CAAC,GAAGsH,IAAI;IACV;IAAC;IAAA3I,aAAA,GAAAoB,CAAA;IAED,OAAOC,CAAC;EACV;EAEA;;;EAGQ,OAAO+G,UAAUA,CAAC9D,cAAsB,EAAEK,iBAAyB;IAAA;IAAA3E,aAAA,GAAAqB,CAAA;IACzE,MAAM0H,SAAS;IAAA;IAAA,CAAA/I,aAAA,GAAAoB,CAAA,SAAGkE,IAAI,CAACC,GAAG,CAACD,IAAI,CAACsD,KAAK,CAACjE,iBAAiB,GAAG,GAAG,GAAG,IAAI,GAAGW,IAAI,CAACC,GAAG,CAACjB,cAAc,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IAAC;IAAAtE,aAAA,GAAAoB,CAAA;IAC1G,OAAO,IAAI,GAAG2H,SAAS;EACzB;EAEA;;;EAGQ,OAAOV,OAAOA,CAAC/D,cAAsB,EAAEK,iBAAyB;IAAA;IAAA3E,aAAA,GAAAqB,CAAA;IACtE,MAAM2H,KAAK;IAAA;IAAA,CAAAhJ,aAAA,GAAAoB,CAAA,SAAGuD,iBAAiB,GAAG,GAAG;IACrC,MAAMsE,KAAK;IAAA;IAAA,CAAAjJ,aAAA,GAAAoB,CAAA,SAAG,GAAG,GAAGkD,cAAc;IAAC;IAAAtE,aAAA,GAAAoB,CAAA;IACnC,OAAO,CAAC,GAAGkE,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,GAAGD,IAAI,CAACsD,KAAK,CAACtD,IAAI,CAACC,GAAG,CAACyD,KAAK,EAAE,IAAI,CAAC,GAAGC,KAAK,CAAC,EAAE,CAAC,CAAC;EAC1E;EAEA;;;EAGQ,OAAOX,IAAIA,CAAChE,cAAsB,EAAEK,iBAAyB;IAAA;IAAA3E,aAAA,GAAAqB,CAAA;IACnE,MAAM6H,CAAC;IAAA;IAAA,CAAAlJ,aAAA,GAAAoB,CAAA,SAAGkE,IAAI,CAACC,GAAG,CAACD,IAAI,CAACsD,KAAK,CAACjE,iBAAiB,GAAG,MAAM,GAAG,MAAM,GAAGL,cAAc,GAChEgB,IAAI,CAACsD,KAAK,CAACtD,IAAI,CAACC,GAAG,CAACZ,iBAAiB,EAAE,MAAM,CAAC,GAAG,MAAM,GACvDW,IAAI,CAACC,GAAG,CAAC,KAAK,GAAGjB,cAAc,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAAC;IAAAtE,aAAA,GAAAoB,CAAA;IACjE,OAAO,CAAC,IAAI,CAAC,GAAG8H,CAAC,CAAC;EACpB;EAEA;;;EAGQ,OAAOV,gBAAgBA,CAAClE,cAAsB,EAAEK,iBAAyB;IAAA;IAAA3E,aAAA,GAAAqB,CAAA;IAC/E,MAAM6H,CAAC;IAAA;IAAA,CAAAlJ,aAAA,GAAAoB,CAAA,SAAG,CAAC,CAAC,GAAGkE,IAAI,CAACsD,KAAK,CAACjE,iBAAiB,GAAG,GAAG,GAAG,IAAI,GAAGL,cAAc,GAC/DgB,IAAI,CAACsD,KAAK,CAACjE,iBAAiB,GAAG,GAAG,GAAG,IAAI,GAAGL,cAAc,GAC1DgB,IAAI,CAACsD,KAAK,CAACjE,iBAAiB,GAAG,GAAG,GAAG,EAAE,GAAGL,cAAc,CAAC,CAAC,CAAC;IAAC;IAAAtE,aAAA,GAAAoB,CAAA;IACtE,OAAO,CAAC,GAAGkE,IAAI,CAACC,GAAG,CAAC2D,CAAC,EAAE,CAAC,CAAC;EAC3B;EAEA;;;EAGQ,OAAOf,kBAAkBA,CAAC7D,cAAsB,EAAEK,iBAAyB;IAAA;IAAA3E,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACjF;IACA,IAAIuD,iBAAiB,GAAG,MAAM,EAAE;MAAA;MAAA3E,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAC9B;MACA,OAAO,KAAK,GAAGkE,IAAI,CAACC,GAAG,CAACjB,cAAc,EAAE,IAAI,CAAC;IAC/C,CAAC,MAAM;MAAA;MAAAtE,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACL;MACA,OAAO,IAAI,CAACgH,UAAU,CAAC9D,cAAc,EAAEK,iBAAiB,CAAC;IAC3D;EACF;EAEA;;;EAGQ,OAAO8D,aAAaA,CAC1BnE,cAAsB,EACtBK,iBAAyB,EACzBJ,UAAsB;IAAA;IAAAvE,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAEtB,QAAQmD,UAAU;MAChB,KAAKlC,UAAU,CAAC2E,OAAO;QAAA;QAAAhH,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACrB,OAAO,EAAE,GAAGkD,cAAc;MAE5B,KAAKjC,UAAU,CAAC6E,YAAY;QAAA;QAAAlH,aAAA,GAAAsB,CAAA;QAC1B;QACA,MAAM6H,QAAQ;QAAA;QAAA,CAAAnJ,aAAA,GAAAoB,CAAA,SAAG,EAAE,GAAGkD,cAAc;QACpC,MAAM8E,UAAU;QAAA;QAAA,CAAApJ,aAAA,GAAAoB,CAAA,SAAG,IAAI,CAAC6G,cAAc,CAAC,IAAI,EAAEtD,iBAAiB,CAAC;QAC/D,MAAM0E,MAAM;QAAA;QAAA,CAAArJ,aAAA,GAAAoB,CAAA,SAAG,CAACkD,cAAc,GAAG,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC;QAAC;QAAAtE,aAAA,GAAAoB,CAAA;QACvD,OAAO+H,QAAQ,IAAI,CAAC,GAAGE,MAAM,CAAC,GAAGD,UAAU,GAAGC,MAAM;MAEtD,KAAKhH,UAAU,CAAC8E,gBAAgB;QAAA;QAAAnH,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QAC9B,OAAO,IAAI,CAACiH,OAAO,CAAC/D,cAAc,EAAEK,iBAAiB,CAAC;MAExD,KAAKtC,UAAU,CAAC+E,eAAe;QAAA;QAAApH,aAAA,GAAAsB,CAAA;MAC/B,KAAKe,UAAU,CAACgF,WAAW;QAAA;QAAArH,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACzB,OAAO,IAAI,CAAC6G,cAAc,CAAC3D,cAAc,EAAEK,iBAAiB,CAAC;MAE/D;QAAA;QAAA3E,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACE,OAAO,IAAI,CAAC6G,cAAc,CAAC3D,cAAc,EAAEK,iBAAiB,CAAC;IACjE;EACF;EAEA;;;EAGQ,OAAOK,iCAAiCA,CAC9C/B,aAA6B,EAC7BqG,UAAmB;IAAA;IAAAtJ,aAAA,GAAAqB,CAAA;IAEnB,MAAMkI,WAAW;IAAA;IAAA,CAAAvJ,aAAA,GAAAoB,CAAA,SAAG;MAClBoI,WAAW,EAAE,GAAG;MAChBC,QAAQ,EAAE,GAAG;MACbC,QAAQ,EAAE,GAAG;MACbjE,QAAQ,EAAE;KACX;IAAC;IAAAzF,aAAA,GAAAoB,CAAA;IAEF,IAAIkI,UAAU,EAAE;MAAA;MAAAtJ,aAAA,GAAAsB,CAAA;MACd,MAAMqI,YAAY;MAAA;MAAA,CAAA3J,aAAA,GAAAoB,CAAA,SAAGkI,UAAU,GAAG,IAAI,CAACrF,YAAY,CAACD,OAAO;MAAC;MAAAhE,aAAA,GAAAoB,CAAA;MAC5DmI,WAAW,CAACC,WAAW,GAAGG,YAAY;MAAC;MAAA3J,aAAA,GAAAoB,CAAA;MACvCmI,WAAW,CAACE,QAAQ,GAAGE,YAAY;MAAC;MAAA3J,aAAA,GAAAoB,CAAA;MACpCmI,WAAW,CAACG,QAAQ,GAAGC,YAAY;MAAC;MAAA3J,aAAA,GAAAoB,CAAA;MACpCmI,WAAW,CAAC9D,QAAQ,GAAGkE,YAAY;IACrC,CAAC;IAAA;IAAA;MAAA3J,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAED,OAAOmI,WAAW;EACpB;EAEA;;;EAGQ,OAAOrE,wBAAwBA,CACrC5B,SAAiB,EACjBC,WAAoB;IAAA;IAAAvD,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAEpB,IAAIkC,SAAS,KAAK,OAAO,EAAE;MAAA;MAAAtD,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACzB,OAAO,GAAG;IACZ,CAAC;IAAA;IAAA;MAAApB,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAED;IAAI;IAAA,CAAApB,aAAA,GAAAsB,CAAA,WAAAgC,SAAS,KAAK,aAAa;IAAA;IAAA,CAAAtD,aAAA,GAAAsB,CAAA,WAAIiC,WAAW,GAAE;MAAA;MAAAvD,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAC9C;MACA,IAAImC,WAAW,IAAI,GAAG,EAAE;QAAA;QAAAvD,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QAAA,OAAO,GAAG;MAAA,CAAC;MAAA;MAAA;QAAApB,aAAA,GAAAsB,CAAA;MAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACnC,IAAImC,WAAW,IAAI,GAAG,EAAE;QAAA;QAAAvD,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QAAA,OAAO,IAAI;MAAA,CAAC;MAAA;MAAA;QAAApB,aAAA,GAAAsB,CAAA;MAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACpC,IAAImC,WAAW,IAAI,GAAG,EAAE;QAAA;QAAAvD,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QAAA,OAAO,IAAI;MAAA,CAAC;MAAA;MAAA;QAAApB,aAAA,GAAAsB,CAAA;MAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACpC,IAAImC,WAAW,IAAI,GAAG,EAAE;QAAA;QAAAvD,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QAAA,OAAO,IAAI;MAAA,CAAC;MAAA;MAAA;QAAApB,aAAA,GAAAsB,CAAA;MAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACpC,OAAO,IAAI,CAAC,CAAC;IACf,CAAC;IAAA;IAAA;MAAApB,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAED,OAAO,IAAI,CAAC,CAAC;EACf;EAEA;;;EAGQ,OAAOgE,8BAA8BA,CAC3C5B,iBAAiE;IAAA;IAAAxD,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAEjE,IAAI,CAACoC,iBAAiB,EAAE;MAAA;MAAAxD,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAAA,OAAO,GAAG;IAAA,CAAC;IAAA;IAAA;MAAApB,aAAA,GAAAsB,CAAA;IAAA;IAEnC,MAAM;MAAEsI,YAAY;MAAA;MAAA,CAAA5J,aAAA,GAAAsB,CAAA,WAAG,GAAG;MAAEuI,WAAW;MAAA;MAAA,CAAA7J,aAAA,GAAAsB,CAAA,WAAG,GAAG;MAAEwI,aAAa;MAAA;MAAA,CAAA9J,aAAA,GAAAsB,CAAA,WAAG,GAAG;IAAA,CAAE;IAAA;IAAA,CAAAtB,aAAA,GAAAoB,CAAA,SAAGoC,iBAAiB;IAAC;IAAAxD,aAAA,GAAAoB,CAAA;IACzF,OAAOwI,YAAY,GAAGC,WAAW,GAAGC,aAAa;EACnD;EAEA;;;EAGQ,OAAOjE,0BAA0BA,CACvCE,YAAoB,EACpBhD,MAAsB,EACtBwB,UAAsB,EACtBI,iBAAyB;IAAA;IAAA3E,aAAA,GAAAqB,CAAA;IAEzB,IAAI0I,YAAY;IAAA;IAAA,CAAA/J,aAAA,GAAAoB,CAAA,SAAG,IAAI,CAAC6E,eAAe,CAAClD,MAAM,CAAC;IAE/C;IAAA;IAAA/C,aAAA,GAAAoB,CAAA;IACA,IAAImD,UAAU,KAAKlC,UAAU,CAAC6E,YAAY,EAAE;MAAA;MAAAlH,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAC1C2I,YAAY,IAAI,GAAG,CAAC,CAAC;IACvB,CAAC;IAAA;IAAA;MAAA/J,aAAA,GAAAsB,CAAA;IAAA;IAED;IAAAtB,aAAA,GAAAoB,CAAA;IACA,IAAIuD,iBAAiB,GAAG,IAAI,EAAE;MAAA;MAAA3E,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAC5B2I,YAAY,IAAI,IAAI,CAAC,CAAC;IACxB,CAAC;IAAA;IAAA;MAAA/J,aAAA,GAAAsB,CAAA;IAAA;IAED,MAAM0I,WAAW;IAAA;IAAA,CAAAhK,aAAA,GAAAoB,CAAA,SAAG2E,YAAY,IAAI,CAAC,GAAGgE,YAAY,CAAC;IAAC;IAAA/J,aAAA,GAAAoB,CAAA;IAEtD,OAAO;MACL6I,KAAK,EAAElE,YAAY,GAAGiE,WAAW;MACjCE,KAAK,EAAEnE,YAAY,GAAGiE,WAAW;MACjCG,eAAe,EAAEJ;KAClB;EACH;EAEA;;;EAGQ,OAAOjE,uBAAuBA,CACpCnD,QAAgB,EAChB2B,cAAsB,EACtBqB,YAAoB,EACpBlB,kBAAuB,EACvBd,eAAyB;IAAA;IAAA3D,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAEzB;IACA,IAAIuB,QAAQ,GAAG,IAAI,EAAE;MAAA;MAAA3C,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACnBuC,eAAe,CAACQ,IAAI,CAAC,iFAAiF,CAAC;IACzG,CAAC;IAAA;IAAA;MAAAnE,aAAA,GAAAsB,CAAA;IAAA;IAED;IAAAtB,aAAA,GAAAoB,CAAA;IACA,IAAIuE,YAAY,GAAG,IAAI,EAAE;MAAA;MAAA3F,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACvBuC,eAAe,CAACQ,IAAI,CAAC,kEAAkE,CAAC;IAC1F,CAAC;IAAA;IAAA;MAAAnE,aAAA,GAAAsB,CAAA;IAAA;IAED;IAAAtB,aAAA,GAAAoB,CAAA;IACA,IAAIqD,kBAAkB,CAACsD,cAAc,GAAG,GAAG,EAAE;MAAA;MAAA/H,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAC3CuC,eAAe,CAACQ,IAAI,CAAC,4FAA4F,CAAC;IACpH,CAAC;IAAA;IAAA;MAAAnE,aAAA,GAAAsB,CAAA;IAAA;IAED;IAAAtB,aAAA,GAAAoB,CAAA;IACA,IAAIkD,cAAc,GAAG,IAAI,EAAE;MAAA;MAAAtE,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAoB,CAAA;MACzBuC,eAAe,CAACQ,IAAI,CAAC,qFAAqF,CAAC;IAC7G,CAAC;IAAA;IAAA;MAAAnE,aAAA,GAAAsB,CAAA;IAAA;EACH;EAEA;;;EAGQ,OAAO8E,qBAAqBA,CAACrD,MAAsB;IAAA;IAAA/C,aAAA,GAAAqB,CAAA;IACzD,MAAM+I,YAAY;IAAA;IAAA,CAAApK,aAAA,GAAAoB,CAAA,SAAmC;MACnD,CAACe,cAAc,CAACwE,eAAe,GAAG,sCAAsC;MACxE,CAACxE,cAAc,CAAC+F,KAAK,GAAG,6BAA6B;MACrD,CAAC/F,cAAc,CAAC0E,WAAW,GAAG,yCAAyC;MACvE,CAAC1E,cAAc,CAACyE,OAAO,GAAG,0CAA0C;MACpE,CAACzE,cAAc,CAAC2E,IAAI,GAAG,6BAA6B;MACpD,CAAC3E,cAAc,CAACoG,iBAAiB,GAAG,0CAA0C;MAC9E,CAACpG,cAAc,CAACa,cAAc,GAAG;KAClC;IAAC;IAAAhD,aAAA,GAAAoB,CAAA;IAEF,OAAO,2BAAApB,aAAA,GAAAsB,CAAA,WAAA8I,YAAY,CAACrH,MAAM,CAAC;IAAA;IAAA,CAAA/C,aAAA,GAAAsB,CAAA,WAAI,gBAAgB;EACjD;EAEA;;;EAGQ,OAAOiF,oBAAoBA,CAACxD,MAAsB;IAAA;IAAA/C,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACxD,OAAO,+CAA+C;EACxD;;;;AAtmBFgB,OAAA,CAAAI,0BAAA,GAAAA,0BAAA;AAumBC;AAAAxC,aAAA,GAAAoB,CAAA;AAtmByBoB,0BAAA,CAAA6H,OAAO,GAAG,OAAO;AAEzC;AAAA;AAAArK,aAAA,GAAAoB,CAAA;AACwBoB,0BAAA,CAAAyB,YAAY,GAAG;EACrCD,OAAO,EAAE,KAAK;EAAO;EACrBE,SAAS,EAAE,MAAM,CAAI;CACtB;AAED;AAAA;AAAAlE,aAAA,GAAAoB,CAAA;AACwBoB,0BAAA,CAAAiE,oBAAoB,GAAG;EAC7CC,WAAW,EAAE,IAAI;EACjBO,gBAAgB,EAAE,IAAI;EACtBqD,aAAa,EAAE;CAChB;AAED;AAAA;AAAAtK,aAAA,GAAAoB,CAAA;AACwBoB,0BAAA,CAAAyD,eAAe,GAAG;EACxC,CAAC9D,cAAc,CAACwE,eAAe,GAAG,IAAI;EACtC,CAACxE,cAAc,CAAC+F,KAAK,GAAG,IAAI;EAC5B,CAAC/F,cAAc,CAAC0E,WAAW,GAAG,IAAI;EAClC,CAAC1E,cAAc,CAACyE,OAAO,GAAG,IAAI;EAC9B,CAACzE,cAAc,CAAC2E,IAAI,GAAG,IAAI;EAC3B,CAAC3E,cAAc,CAACoG,iBAAiB,GAAG,IAAI;EACxC,CAACpG,cAAc,CAACa,cAAc,GAAG;CAClC;AAED;AAAA;AAAAhD,aAAA,GAAAoB,CAAA;AACwBoB,0BAAA,CAAAoF,aAAa,GAAG;EACtC,CAACtF,WAAW,CAACiI,GAAG,GAAG,GAAG;EACtB,CAACjI,WAAW,CAACa,IAAI,GAAG,GAAG;EACvB,CAACb,WAAW,CAACkI,OAAO,GAAG,GAAG;EAC1B,CAAClI,WAAW,CAACmI,IAAI,GAAG,GAAG;EACvB,CAACnI,WAAW,CAACoI,SAAS,GAAG;CAC1B;AAED;AAAA;AAAA1K,aAAA,GAAAoB,CAAA;AACwBoB,0BAAA,CAAAsF,eAAe,GAAG;EACxC,CAACvF,gBAAgB,CAACoI,SAAS,GAAG,GAAG;EACjC,CAACpI,gBAAgB,CAACY,IAAI,GAAG,GAAG;EAC5B,CAACZ,gBAAgB,CAACiI,OAAO,GAAG,GAAG;EAC/B,CAACjI,gBAAgB,CAACkI,IAAI,GAAG,GAAG;EAC5B,CAAClI,gBAAgB,CAACmI,SAAS,GAAG;CAC/B", "ignoreList": []}