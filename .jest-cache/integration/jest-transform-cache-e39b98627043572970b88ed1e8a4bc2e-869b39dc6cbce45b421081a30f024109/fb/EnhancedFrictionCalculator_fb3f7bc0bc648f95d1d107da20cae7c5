485b0c051357464092b1e240b7f6f8b0
"use strict";

/**
 * Enhanced Friction Calculator
 *
 * Comprehensive friction calculation service for Phase 3: Advanced Calculation Modules
 * Provides multiple friction factor calculation methods, material aging effects,
 * environmental corrections, and advanced features for HVAC duct systems.
 *
 * @version 3.0.0
 * <AUTHOR> Suite Development Team
 */
/* istanbul ignore next */
function cov_1tnf2sn61() {
  var path = "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\EnhancedFrictionCalculator.ts";
  var hash = "6c4a005658abbd3c25503516a88b98f54e743e96";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\EnhancedFrictionCalculator.ts",
    statementMap: {
      "0": {
        start: {
          line: 12,
          column: 0
        },
        end: {
          line: 12,
          column: 62
        }
      },
      "1": {
        start: {
          line: 13,
          column: 0
        },
        end: {
          line: 13,
          column: 139
        }
      },
      "2": {
        start: {
          line: 14,
          column: 34
        },
        end: {
          line: 14,
          column: 70
        }
      },
      "3": {
        start: {
          line: 19,
          column: 0
        },
        end: {
          line: 27,
          column: 69
        }
      },
      "4": {
        start: {
          line: 20,
          column: 4
        },
        end: {
          line: 20,
          column: 58
        }
      },
      "5": {
        start: {
          line: 21,
          column: 4
        },
        end: {
          line: 21,
          column: 38
        }
      },
      "6": {
        start: {
          line: 22,
          column: 4
        },
        end: {
          line: 22,
          column: 50
        }
      },
      "7": {
        start: {
          line: 23,
          column: 4
        },
        end: {
          line: 23,
          column: 42
        }
      },
      "8": {
        start: {
          line: 24,
          column: 4
        },
        end: {
          line: 24,
          column: 36
        }
      },
      "9": {
        start: {
          line: 25,
          column: 4
        },
        end: {
          line: 25,
          column: 62
        }
      },
      "10": {
        start: {
          line: 26,
          column: 4
        },
        end: {
          line: 26,
          column: 56
        }
      },
      "11": {
        start: {
          line: 32,
          column: 0
        },
        end: {
          line: 38,
          column: 57
        }
      },
      "12": {
        start: {
          line: 33,
          column: 4
        },
        end: {
          line: 33,
          column: 38
        }
      },
      "13": {
        start: {
          line: 34,
          column: 4
        },
        end: {
          line: 34,
          column: 48
        }
      },
      "14": {
        start: {
          line: 35,
          column: 4
        },
        end: {
          line: 35,
          column: 56
        }
      },
      "15": {
        start: {
          line: 36,
          column: 4
        },
        end: {
          line: 36,
          column: 54
        }
      },
      "16": {
        start: {
          line: 37,
          column: 4
        },
        end: {
          line: 37,
          column: 46
        }
      },
      "17": {
        start: {
          line: 43,
          column: 0
        },
        end: {
          line: 49,
          column: 60
        }
      },
      "18": {
        start: {
          line: 44,
          column: 4
        },
        end: {
          line: 44,
          column: 31
        }
      },
      "19": {
        start: {
          line: 45,
          column: 4
        },
        end: {
          line: 45,
          column: 33
        }
      },
      "20": {
        start: {
          line: 46,
          column: 4
        },
        end: {
          line: 46,
          column: 39
        }
      },
      "21": {
        start: {
          line: 47,
          column: 4
        },
        end: {
          line: 47,
          column: 33
        }
      },
      "22": {
        start: {
          line: 48,
          column: 4
        },
        end: {
          line: 48,
          column: 43
        }
      },
      "23": {
        start: {
          line: 54,
          column: 0
        },
        end: {
          line: 60,
          column: 75
        }
      },
      "24": {
        start: {
          line: 55,
          column: 4
        },
        end: {
          line: 55,
          column: 48
        }
      },
      "25": {
        start: {
          line: 56,
          column: 4
        },
        end: {
          line: 56,
          column: 38
        }
      },
      "26": {
        start: {
          line: 57,
          column: 4
        },
        end: {
          line: 57,
          column: 44
        }
      },
      "27": {
        start: {
          line: 58,
          column: 4
        },
        end: {
          line: 58,
          column: 38
        }
      },
      "28": {
        start: {
          line: 59,
          column: 4
        },
        end: {
          line: 59,
          column: 48
        }
      },
      "29": {
        start: {
          line: 72,
          column: 295
        },
        end: {
          line: 72,
          column: 300
        }
      },
      "30": {
        start: {
          line: 73,
          column: 25
        },
        end: {
          line: 73,
          column: 27
        }
      },
      "31": {
        start: {
          line: 74,
          column: 32
        },
        end: {
          line: 74,
          column: 34
        }
      },
      "32": {
        start: {
          line: 76,
          column: 8
        },
        end: {
          line: 76,
          column: 62
        }
      },
      "33": {
        start: {
          line: 78,
          column: 25
        },
        end: {
          line: 84,
          column: 13
        }
      },
      "34": {
        start: {
          line: 85,
          column: 8
        },
        end: {
          line: 85,
          column: 44
        }
      },
      "35": {
        start: {
          line: 87,
          column: 28
        },
        end: {
          line: 87,
          column: 41
        }
      },
      "36": {
        start: {
          line: 88,
          column: 27
        },
        end: {
          line: 88,
          column: 49
        }
      },
      "37": {
        start: {
          line: 89,
          column: 31
        },
        end: {
          line: 89,
          column: 97
        }
      },
      "38": {
        start: {
          line: 91,
          column: 27
        },
        end: {
          line: 91,
          column: 66
        }
      },
      "39": {
        start: {
          line: 93,
          column: 35
        },
        end: {
          line: 93,
          column: 129
        }
      },
      "40": {
        start: {
          line: 95,
          column: 34
        },
        end: {
          line: 95,
          column: 83
        }
      },
      "41": {
        start: {
          line: 97,
          column: 31
        },
        end: {
          line: 97,
          column: 124
        }
      },
      "42": {
        start: {
          line: 99,
          column: 41
        },
        end: {
          line: 99,
          column: 112
        }
      },
      "43": {
        start: {
          line: 101,
          column: 32
        },
        end: {
          line: 101,
          column: 85
        }
      },
      "44": {
        start: {
          line: 103,
          column: 38
        },
        end: {
          line: 103,
          column: 92
        }
      },
      "45": {
        start: {
          line: 105,
          column: 33
        },
        end: {
          line: 106,
          column: 72
        }
      },
      "46": {
        start: {
          line: 108,
          column: 32
        },
        end: {
          line: 108,
          column: 107
        }
      },
      "47": {
        start: {
          line: 109,
          column: 38
        },
        end: {
          line: 109,
          column: 80
        }
      },
      "48": {
        start: {
          line: 111,
          column: 29
        },
        end: {
          line: 111,
          column: 67
        }
      },
      "49": {
        start: {
          line: 113,
          column: 34
        },
        end: {
          line: 113,
          column: 127
        }
      },
      "50": {
        start: {
          line: 115,
          column: 8
        },
        end: {
          line: 115,
          column: 114
        }
      },
      "51": {
        start: {
          line: 116,
          column: 8
        },
        end: {
          line: 140,
          column: 10
        }
      },
      "52": {
        start: {
          line: 147,
          column: 8
        },
        end: {
          line: 149,
          column: 9
        }
      },
      "53": {
        start: {
          line: 148,
          column: 12
        },
        end: {
          line: 148,
          column: 49
        }
      },
      "54": {
        start: {
          line: 151,
          column: 8
        },
        end: {
          line: 153,
          column: 9
        }
      },
      "55": {
        start: {
          line: 152,
          column: 12
        },
        end: {
          line: 152,
          column: 50
        }
      },
      "56": {
        start: {
          line: 154,
          column: 8
        },
        end: {
          line: 156,
          column: 9
        }
      },
      "57": {
        start: {
          line: 155,
          column: 12
        },
        end: {
          line: 155,
          column: 42
        }
      },
      "58": {
        start: {
          line: 158,
          column: 8
        },
        end: {
          line: 163,
          column: 9
        }
      },
      "59": {
        start: {
          line: 159,
          column: 12
        },
        end: {
          line: 159,
          column: 46
        }
      },
      "60": {
        start: {
          line: 162,
          column: 12
        },
        end: {
          line: 162,
          column: 39
        }
      },
      "61": {
        start: {
          line: 169,
          column: 8
        },
        end: {
          line: 170,
          column: 19
        }
      },
      "62": {
        start: {
          line: 170,
          column: 12
        },
        end: {
          line: 170,
          column: 19
        }
      },
      "63": {
        start: {
          line: 171,
          column: 56
        },
        end: {
          line: 171,
          column: 61
        }
      },
      "64": {
        start: {
          line: 173,
          column: 8
        },
        end: {
          line: 174,
          column: 57
        }
      },
      "65": {
        start: {
          line: 174,
          column: 12
        },
        end: {
          line: 174,
          column: 57
        }
      },
      "66": {
        start: {
          line: 175,
          column: 8
        },
        end: {
          line: 176,
          column: 67
        }
      },
      "67": {
        start: {
          line: 176,
          column: 12
        },
        end: {
          line: 176,
          column: 67
        }
      },
      "68": {
        start: {
          line: 177,
          column: 8
        },
        end: {
          line: 178,
          column: 55
        }
      },
      "69": {
        start: {
          line: 178,
          column: 12
        },
        end: {
          line: 178,
          column: 55
        }
      },
      "70": {
        start: {
          line: 179,
          column: 8
        },
        end: {
          line: 180,
          column: 19
        }
      },
      "71": {
        start: {
          line: 180,
          column: 12
        },
        end: {
          line: 180,
          column: 19
        }
      },
      "72": {
        start: {
          line: 182,
          column: 8
        },
        end: {
          line: 184,
          column: 9
        }
      },
      "73": {
        start: {
          line: 183,
          column: 12
        },
        end: {
          line: 183,
          column: 88
        }
      },
      "74": {
        start: {
          line: 185,
          column: 8
        },
        end: {
          line: 187,
          column: 9
        }
      },
      "75": {
        start: {
          line: 186,
          column: 12
        },
        end: {
          line: 186,
          column: 74
        }
      },
      "76": {
        start: {
          line: 188,
          column: 8
        },
        end: {
          line: 196,
          column: 9
        }
      },
      "77": {
        start: {
          line: 190,
          column: 12
        },
        end: {
          line: 192,
          column: 13
        }
      },
      "78": {
        start: {
          line: 191,
          column: 16
        },
        end: {
          line: 191,
          column: 94
        }
      },
      "79": {
        start: {
          line: 193,
          column: 12
        },
        end: {
          line: 195,
          column: 13
        }
      },
      "80": {
        start: {
          line: 194,
          column: 16
        },
        end: {
          line: 194,
          column: 98
        }
      },
      "81": {
        start: {
          line: 202,
          column: 8
        },
        end: {
          line: 216,
          column: 9
        }
      },
      "82": {
        start: {
          line: 203,
          column: 12
        },
        end: {
          line: 203,
          column: 38
        }
      },
      "83": {
        start: {
          line: 205,
          column: 13
        },
        end: {
          line: 216,
          column: 9
        }
      },
      "84": {
        start: {
          line: 206,
          column: 12
        },
        end: {
          line: 206,
          column: 43
        }
      },
      "85": {
        start: {
          line: 208,
          column: 13
        },
        end: {
          line: 216,
          column: 9
        }
      },
      "86": {
        start: {
          line: 209,
          column: 12
        },
        end: {
          line: 209,
          column: 47
        }
      },
      "87": {
        start: {
          line: 211,
          column: 13
        },
        end: {
          line: 216,
          column: 9
        }
      },
      "88": {
        start: {
          line: 212,
          column: 12
        },
        end: {
          line: 212,
          column: 46
        }
      },
      "89": {
        start: {
          line: 215,
          column: 12
        },
        end: {
          line: 215,
          column: 42
        }
      },
      "90": {
        start: {
          line: 223,
          column: 8
        },
        end: {
          line: 238,
          column: 9
        }
      },
      "91": {
        start: {
          line: 224,
          column: 12
        },
        end: {
          line: 224,
          column: 44
        }
      },
      "92": {
        start: {
          line: 228,
          column: 12
        },
        end: {
          line: 237,
          column: 13
        }
      },
      "93": {
        start: {
          line: 229,
          column: 37
        },
        end: {
          line: 229,
          column: 156
        }
      },
      "94": {
        start: {
          line: 230,
          column: 16
        },
        end: {
          line: 230,
          column: 63
        }
      },
      "95": {
        start: {
          line: 231,
          column: 16
        },
        end: {
          line: 231,
          column: 56
        }
      },
      "96": {
        start: {
          line: 235,
          column: 16
        },
        end: {
          line: 235,
          column: 67
        }
      },
      "97": {
        start: {
          line: 236,
          column: 16
        },
        end: {
          line: 236,
          column: 83
        }
      },
      "98": {
        start: {
          line: 239,
          column: 28
        },
        end: {
          line: 239,
          column: 59
        }
      },
      "99": {
        start: {
          line: 240,
          column: 30
        },
        end: {
          line: 240,
          column: 68
        }
      },
      "100": {
        start: {
          line: 241,
          column: 31
        },
        end: {
          line: 241,
          column: 58
        }
      },
      "101": {
        start: {
          line: 242,
          column: 34
        },
        end: {
          line: 242,
          column: 64
        }
      },
      "102": {
        start: {
          line: 243,
          column: 8
        },
        end: {
          line: 249,
          column: 10
        }
      },
      "103": {
        start: {
          line: 255,
          column: 33
        },
        end: {
          line: 264,
          column: 9
        }
      },
      "104": {
        start: {
          line: 265,
          column: 8
        },
        end: {
          line: 265,
          column: 52
        }
      },
      "105": {
        start: {
          line: 272,
          column: 8
        },
        end: {
          line: 274,
          column: 9
        }
      },
      "106": {
        start: {
          line: 273,
          column: 12
        },
        end: {
          line: 273,
          column: 39
        }
      },
      "107": {
        start: {
          line: 275,
          column: 8
        },
        end: {
          line: 293,
          column: 9
        }
      },
      "108": {
        start: {
          line: 277,
          column: 16
        },
        end: {
          line: 277,
          column: 78
        }
      },
      "109": {
        start: {
          line: 279,
          column: 16
        },
        end: {
          line: 279,
          column: 82
        }
      },
      "110": {
        start: {
          line: 281,
          column: 16
        },
        end: {
          line: 281,
          column: 74
        }
      },
      "111": {
        start: {
          line: 283,
          column: 16
        },
        end: {
          line: 283,
          column: 71
        }
      },
      "112": {
        start: {
          line: 285,
          column: 16
        },
        end: {
          line: 285,
          column: 68
        }
      },
      "113": {
        start: {
          line: 287,
          column: 16
        },
        end: {
          line: 287,
          column: 80
        }
      },
      "114": {
        start: {
          line: 289,
          column: 16
        },
        end: {
          line: 289,
          column: 89
        }
      },
      "115": {
        start: {
          line: 291,
          column: 16
        },
        end: {
          line: 291,
          column: 81
        }
      },
      "116": {
        start: {
          line: 292,
          column: 16
        },
        end: {
          line: 292,
          column: 78
        }
      },
      "117": {
        start: {
          line: 299,
          column: 16
        },
        end: {
          line: 299,
          column: 20
        }
      },
      "118": {
        start: {
          line: 300,
          column: 8
        },
        end: {
          line: 306,
          column: 9
        }
      },
      "119": {
        start: {
          line: 300,
          column: 21
        },
        end: {
          line: 300,
          column: 22
        }
      },
      "120": {
        start: {
          line: 301,
          column: 25
        },
        end: {
          line: 301,
          column: 123
        }
      },
      "121": {
        start: {
          line: 302,
          column: 12
        },
        end: {
          line: 304,
          column: 13
        }
      },
      "122": {
        start: {
          line: 303,
          column: 16
        },
        end: {
          line: 303,
          column: 22
        }
      },
      "123": {
        start: {
          line: 305,
          column: 12
        },
        end: {
          line: 305,
          column: 21
        }
      },
      "124": {
        start: {
          line: 307,
          column: 8
        },
        end: {
          line: 307,
          column: 17
        }
      },
      "125": {
        start: {
          line: 313,
          column: 26
        },
        end: {
          line: 313,
          column: 113
        }
      },
      "126": {
        start: {
          line: 314,
          column: 8
        },
        end: {
          line: 314,
          column: 32
        }
      },
      "127": {
        start: {
          line: 320,
          column: 22
        },
        end: {
          line: 320,
          column: 45
        }
      },
      "128": {
        start: {
          line: 321,
          column: 22
        },
        end: {
          line: 321,
          column: 42
        }
      },
      "129": {
        start: {
          line: 322,
          column: 8
        },
        end: {
          line: 322,
          column: 81
        }
      },
      "130": {
        start: {
          line: 328,
          column: 18
        },
        end: {
          line: 330,
          column: 62
        }
      },
      "131": {
        start: {
          line: 331,
          column: 8
        },
        end: {
          line: 331,
          column: 27
        }
      },
      "132": {
        start: {
          line: 337,
          column: 18
        },
        end: {
          line: 339,
          column: 75
        }
      },
      "133": {
        start: {
          line: 340,
          column: 8
        },
        end: {
          line: 340,
          column: 34
        }
      },
      "134": {
        start: {
          line: 347,
          column: 8
        },
        end: {
          line: 354,
          column: 9
        }
      },
      "135": {
        start: {
          line: 349,
          column: 12
        },
        end: {
          line: 349,
          column: 58
        }
      },
      "136": {
        start: {
          line: 353,
          column: 12
        },
        end: {
          line: 353,
          column: 70
        }
      },
      "137": {
        start: {
          line: 360,
          column: 8
        },
        end: {
          line: 376,
          column: 9
        }
      },
      "138": {
        start: {
          line: 362,
          column: 16
        },
        end: {
          line: 362,
          column: 43
        }
      },
      "139": {
        start: {
          line: 365,
          column: 33
        },
        end: {
          line: 365,
          column: 52
        }
      },
      "140": {
        start: {
          line: 366,
          column: 35
        },
        end: {
          line: 366,
          column: 79
        }
      },
      "141": {
        start: {
          line: 367,
          column: 31
        },
        end: {
          line: 367,
          column: 70
        }
      },
      "142": {
        start: {
          line: 368,
          column: 16
        },
        end: {
          line: 368,
          column: 69
        }
      },
      "143": {
        start: {
          line: 370,
          column: 16
        },
        end: {
          line: 370,
          column: 71
        }
      },
      "144": {
        start: {
          line: 373,
          column: 16
        },
        end: {
          line: 373,
          column: 78
        }
      },
      "145": {
        start: {
          line: 375,
          column: 16
        },
        end: {
          line: 375,
          column: 78
        }
      },
      "146": {
        start: {
          line: 382,
          column: 28
        },
        end: {
          line: 387,
          column: 9
        }
      },
      "147": {
        start: {
          line: 388,
          column: 8
        },
        end: {
          line: 394,
          column: 9
        }
      },
      "148": {
        start: {
          line: 389,
          column: 33
        },
        end: {
          line: 389,
          column: 71
        }
      },
      "149": {
        start: {
          line: 390,
          column: 12
        },
        end: {
          line: 390,
          column: 51
        }
      },
      "150": {
        start: {
          line: 391,
          column: 12
        },
        end: {
          line: 391,
          column: 48
        }
      },
      "151": {
        start: {
          line: 392,
          column: 12
        },
        end: {
          line: 392,
          column: 48
        }
      },
      "152": {
        start: {
          line: 393,
          column: 12
        },
        end: {
          line: 393,
          column: 48
        }
      },
      "153": {
        start: {
          line: 395,
          column: 8
        },
        end: {
          line: 395,
          column: 27
        }
      },
      "154": {
        start: {
          line: 401,
          column: 8
        },
        end: {
          line: 403,
          column: 9
        }
      },
      "155": {
        start: {
          line: 402,
          column: 12
        },
        end: {
          line: 402,
          column: 23
        }
      },
      "156": {
        start: {
          line: 404,
          column: 8
        },
        end: {
          line: 415,
          column: 9
        }
      },
      "157": {
        start: {
          line: 406,
          column: 12
        },
        end: {
          line: 407,
          column: 27
        }
      },
      "158": {
        start: {
          line: 407,
          column: 16
        },
        end: {
          line: 407,
          column: 27
        }
      },
      "159": {
        start: {
          line: 408,
          column: 12
        },
        end: {
          line: 409,
          column: 28
        }
      },
      "160": {
        start: {
          line: 409,
          column: 16
        },
        end: {
          line: 409,
          column: 28
        }
      },
      "161": {
        start: {
          line: 410,
          column: 12
        },
        end: {
          line: 411,
          column: 28
        }
      },
      "162": {
        start: {
          line: 411,
          column: 16
        },
        end: {
          line: 411,
          column: 28
        }
      },
      "163": {
        start: {
          line: 412,
          column: 12
        },
        end: {
          line: 413,
          column: 28
        }
      },
      "164": {
        start: {
          line: 413,
          column: 16
        },
        end: {
          line: 413,
          column: 28
        }
      },
      "165": {
        start: {
          line: 414,
          column: 12
        },
        end: {
          line: 414,
          column: 24
        }
      },
      "166": {
        start: {
          line: 416,
          column: 8
        },
        end: {
          line: 416,
          column: 20
        }
      },
      "167": {
        start: {
          line: 422,
          column: 8
        },
        end: {
          line: 423,
          column: 23
        }
      },
      "168": {
        start: {
          line: 423,
          column: 12
        },
        end: {
          line: 423,
          column: 23
        }
      },
      "169": {
        start: {
          line: 424,
          column: 79
        },
        end: {
          line: 424,
          column: 96
        }
      },
      "170": {
        start: {
          line: 425,
          column: 8
        },
        end: {
          line: 425,
          column: 58
        }
      },
      "171": {
        start: {
          line: 431,
          column: 27
        },
        end: {
          line: 431,
          column: 55
        }
      },
      "172": {
        start: {
          line: 433,
          column: 8
        },
        end: {
          line: 435,
          column: 9
        }
      },
      "173": {
        start: {
          line: 434,
          column: 12
        },
        end: {
          line: 434,
          column: 32
        }
      },
      "174": {
        start: {
          line: 437,
          column: 8
        },
        end: {
          line: 439,
          column: 9
        }
      },
      "175": {
        start: {
          line: 438,
          column: 12
        },
        end: {
          line: 438,
          column: 33
        }
      },
      "176": {
        start: {
          line: 440,
          column: 28
        },
        end: {
          line: 440,
          column: 61
        }
      },
      "177": {
        start: {
          line: 441,
          column: 8
        },
        end: {
          line: 445,
          column: 10
        }
      },
      "178": {
        start: {
          line: 452,
          column: 8
        },
        end: {
          line: 454,
          column: 9
        }
      },
      "179": {
        start: {
          line: 453,
          column: 12
        },
        end: {
          line: 453,
          column: 116
        }
      },
      "180": {
        start: {
          line: 456,
          column: 8
        },
        end: {
          line: 458,
          column: 9
        }
      },
      "181": {
        start: {
          line: 457,
          column: 12
        },
        end: {
          line: 457,
          column: 101
        }
      },
      "182": {
        start: {
          line: 460,
          column: 8
        },
        end: {
          line: 462,
          column: 9
        }
      },
      "183": {
        start: {
          line: 461,
          column: 12
        },
        end: {
          line: 461,
          column: 127
        }
      },
      "184": {
        start: {
          line: 464,
          column: 8
        },
        end: {
          line: 466,
          column: 9
        }
      },
      "185": {
        start: {
          line: 465,
          column: 12
        },
        end: {
          line: 465,
          column: 120
        }
      },
      "186": {
        start: {
          line: 472,
          column: 29
        },
        end: {
          line: 480,
          column: 9
        }
      },
      "187": {
        start: {
          line: 481,
          column: 8
        },
        end: {
          line: 481,
          column: 56
        }
      },
      "188": {
        start: {
          line: 487,
          column: 8
        },
        end: {
          line: 487,
          column: 63
        }
      },
      "189": {
        start: {
          line: 490,
          column: 0
        },
        end: {
          line: 490,
          column: 64
        }
      },
      "190": {
        start: {
          line: 491,
          column: 0
        },
        end: {
          line: 491,
          column: 45
        }
      },
      "191": {
        start: {
          line: 493,
          column: 0
        },
        end: {
          line: 496,
          column: 2
        }
      },
      "192": {
        start: {
          line: 498,
          column: 0
        },
        end: {
          line: 502,
          column: 2
        }
      },
      "193": {
        start: {
          line: 504,
          column: 0
        },
        end: {
          line: 512,
          column: 2
        }
      },
      "194": {
        start: {
          line: 514,
          column: 0
        },
        end: {
          line: 520,
          column: 2
        }
      },
      "195": {
        start: {
          line: 522,
          column: 0
        },
        end: {
          line: 528,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 19,
            column: 1
          },
          end: {
            line: 19,
            column: 2
          }
        },
        loc: {
          start: {
            line: 19,
            column: 27
          },
          end: {
            line: 27,
            column: 1
          }
        },
        line: 19
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 32,
            column: 1
          },
          end: {
            line: 32,
            column: 2
          }
        },
        loc: {
          start: {
            line: 32,
            column: 23
          },
          end: {
            line: 38,
            column: 1
          }
        },
        line: 32
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 43,
            column: 1
          },
          end: {
            line: 43,
            column: 2
          }
        },
        loc: {
          start: {
            line: 43,
            column: 24
          },
          end: {
            line: 49,
            column: 1
          }
        },
        line: 43
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 54,
            column: 1
          },
          end: {
            line: 54,
            column: 2
          }
        },
        loc: {
          start: {
            line: 54,
            column: 29
          },
          end: {
            line: 60,
            column: 1
          }
        },
        line: 54
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 71,
            column: 4
          },
          end: {
            line: 71,
            column: 5
          }
        },
        loc: {
          start: {
            line: 71,
            column: 40
          },
          end: {
            line: 141,
            column: 5
          }
        },
        line: 71
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 145,
            column: 4
          },
          end: {
            line: 145,
            column: 5
          }
        },
        loc: {
          start: {
            line: 145,
            column: 86
          },
          end: {
            line: 164,
            column: 5
          }
        },
        line: 145
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 168,
            column: 4
          },
          end: {
            line: 168,
            column: 5
          }
        },
        loc: {
          start: {
            line: 168,
            column: 60
          },
          end: {
            line: 197,
            column: 5
          }
        },
        line: 168
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 201,
            column: 4
          },
          end: {
            line: 201,
            column: 5
          }
        },
        loc: {
          start: {
            line: 201,
            column: 46
          },
          end: {
            line: 217,
            column: 5
          }
        },
        line: 201
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 221,
            column: 4
          },
          end: {
            line: 221,
            column: 5
          }
        },
        loc: {
          start: {
            line: 221,
            column: 106
          },
          end: {
            line: 250,
            column: 5
          }
        },
        line: 221
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 254,
            column: 4
          },
          end: {
            line: 254,
            column: 5
          }
        },
        loc: {
          start: {
            line: 254,
            column: 41
          },
          end: {
            line: 266,
            column: 5
          }
        },
        line: 254
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 270,
            column: 4
          },
          end: {
            line: 270,
            column: 5
          }
        },
        loc: {
          start: {
            line: 270,
            column: 100
          },
          end: {
            line: 294,
            column: 5
          }
        },
        line: 270
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 298,
            column: 4
          },
          end: {
            line: 298,
            column: 5
          }
        },
        loc: {
          start: {
            line: 298,
            column: 61
          },
          end: {
            line: 308,
            column: 5
          }
        },
        line: 298
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 312,
            column: 4
          },
          end: {
            line: 312,
            column: 5
          }
        },
        loc: {
          start: {
            line: 312,
            column: 57
          },
          end: {
            line: 315,
            column: 5
          }
        },
        line: 312
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 319,
            column: 4
          },
          end: {
            line: 319,
            column: 5
          }
        },
        loc: {
          start: {
            line: 319,
            column: 54
          },
          end: {
            line: 323,
            column: 5
          }
        },
        line: 319
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 327,
            column: 4
          },
          end: {
            line: 327,
            column: 5
          }
        },
        loc: {
          start: {
            line: 327,
            column: 51
          },
          end: {
            line: 332,
            column: 5
          }
        },
        line: 327
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 336,
            column: 4
          },
          end: {
            line: 336,
            column: 5
          }
        },
        loc: {
          start: {
            line: 336,
            column: 63
          },
          end: {
            line: 341,
            column: 5
          }
        },
        line: 336
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 345,
            column: 4
          },
          end: {
            line: 345,
            column: 5
          }
        },
        loc: {
          start: {
            line: 345,
            column: 65
          },
          end: {
            line: 355,
            column: 5
          }
        },
        line: 345
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 359,
            column: 4
          },
          end: {
            line: 359,
            column: 5
          }
        },
        loc: {
          start: {
            line: 359,
            column: 72
          },
          end: {
            line: 377,
            column: 5
          }
        },
        line: 359
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 381,
            column: 4
          },
          end: {
            line: 381,
            column: 5
          }
        },
        loc: {
          start: {
            line: 381,
            column: 72
          },
          end: {
            line: 396,
            column: 5
          }
        },
        line: 381
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 400,
            column: 4
          },
          end: {
            line: 400,
            column: 5
          }
        },
        loc: {
          start: {
            line: 400,
            column: 60
          },
          end: {
            line: 417,
            column: 5
          }
        },
        line: 400
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 421,
            column: 4
          },
          end: {
            line: 421,
            column: 5
          }
        },
        loc: {
          start: {
            line: 421,
            column: 61
          },
          end: {
            line: 426,
            column: 5
          }
        },
        line: 421
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 430,
            column: 4
          },
          end: {
            line: 430,
            column: 5
          }
        },
        loc: {
          start: {
            line: 430,
            column: 91
          },
          end: {
            line: 446,
            column: 5
          }
        },
        line: 430
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 450,
            column: 4
          },
          end: {
            line: 450,
            column: 5
          }
        },
        loc: {
          start: {
            line: 450,
            column: 112
          },
          end: {
            line: 467,
            column: 5
          }
        },
        line: 450
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 471,
            column: 4
          },
          end: {
            line: 471,
            column: 5
          }
        },
        loc: {
          start: {
            line: 471,
            column: 41
          },
          end: {
            line: 482,
            column: 5
          }
        },
        line: 471
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 486,
            column: 4
          },
          end: {
            line: 486,
            column: 5
          }
        },
        loc: {
          start: {
            line: 486,
            column: 40
          },
          end: {
            line: 488,
            column: 5
          }
        },
        line: 486
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 27,
            column: 3
          },
          end: {
            line: 27,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 27,
            column: 3
          },
          end: {
            line: 27,
            column: 17
          }
        }, {
          start: {
            line: 27,
            column: 22
          },
          end: {
            line: 27,
            column: 66
          }
        }],
        line: 27
      },
      "1": {
        loc: {
          start: {
            line: 38,
            column: 3
          },
          end: {
            line: 38,
            column: 55
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 38,
            column: 3
          },
          end: {
            line: 38,
            column: 13
          }
        }, {
          start: {
            line: 38,
            column: 18
          },
          end: {
            line: 38,
            column: 54
          }
        }],
        line: 38
      },
      "2": {
        loc: {
          start: {
            line: 49,
            column: 3
          },
          end: {
            line: 49,
            column: 58
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 49,
            column: 3
          },
          end: {
            line: 49,
            column: 14
          }
        }, {
          start: {
            line: 49,
            column: 19
          },
          end: {
            line: 49,
            column: 57
          }
        }],
        line: 49
      },
      "3": {
        loc: {
          start: {
            line: 60,
            column: 3
          },
          end: {
            line: 60,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 60,
            column: 3
          },
          end: {
            line: 60,
            column: 19
          }
        }, {
          start: {
            line: 60,
            column: 24
          },
          end: {
            line: 60,
            column: 72
          }
        }],
        line: 60
      },
      "4": {
        loc: {
          start: {
            line: 72,
            column: 63
          },
          end: {
            line: 72,
            column: 101
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 72,
            column: 72
          },
          end: {
            line: 72,
            column: 101
          }
        }],
        line: 72
      },
      "5": {
        loc: {
          start: {
            line: 72,
            column: 118
          },
          end: {
            line: 72,
            column: 148
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 72,
            column: 132
          },
          end: {
            line: 72,
            column: 148
          }
        }],
        line: 72
      },
      "6": {
        loc: {
          start: {
            line: 72,
            column: 150
          },
          end: {
            line: 72,
            column: 190
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 72,
            column: 169
          },
          end: {
            line: 72,
            column: 190
          }
        }],
        line: 72
      },
      "7": {
        loc: {
          start: {
            line: 72,
            column: 209
          },
          end: {
            line: 72,
            column: 228
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 72,
            column: 221
          },
          end: {
            line: 72,
            column: 228
          }
        }],
        line: 72
      },
      "8": {
        loc: {
          start: {
            line: 72,
            column: 262
          },
          end: {
            line: 72,
            column: 290
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 72,
            column: 280
          },
          end: {
            line: 72,
            column: 290
          }
        }],
        line: 72
      },
      "9": {
        loc: {
          start: {
            line: 78,
            column: 25
          },
          end: {
            line: 84,
            column: 13
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 79,
            column: 12
          },
          end: {
            line: 79,
            column: 99
          }
        }, {
          start: {
            line: 80,
            column: 12
          },
          end: {
            line: 84,
            column: 13
          }
        }],
        line: 78
      },
      "10": {
        loc: {
          start: {
            line: 145,
            column: 63
          },
          end: {
            line: 145,
            column: 84
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 145,
            column: 74
          },
          end: {
            line: 145,
            column: 84
          }
        }],
        line: 145
      },
      "11": {
        loc: {
          start: {
            line: 147,
            column: 8
          },
          end: {
            line: 149,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 147,
            column: 8
          },
          end: {
            line: 149,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 147
      },
      "12": {
        loc: {
          start: {
            line: 151,
            column: 8
          },
          end: {
            line: 153,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 151,
            column: 8
          },
          end: {
            line: 153,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 151
      },
      "13": {
        loc: {
          start: {
            line: 154,
            column: 8
          },
          end: {
            line: 156,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 154,
            column: 8
          },
          end: {
            line: 156,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 154
      },
      "14": {
        loc: {
          start: {
            line: 158,
            column: 8
          },
          end: {
            line: 163,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 158,
            column: 8
          },
          end: {
            line: 163,
            column: 9
          }
        }, {
          start: {
            line: 161,
            column: 13
          },
          end: {
            line: 163,
            column: 9
          }
        }],
        line: 158
      },
      "15": {
        loc: {
          start: {
            line: 169,
            column: 8
          },
          end: {
            line: 170,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 169,
            column: 8
          },
          end: {
            line: 170,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 169
      },
      "16": {
        loc: {
          start: {
            line: 173,
            column: 8
          },
          end: {
            line: 174,
            column: 57
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 173,
            column: 8
          },
          end: {
            line: 174,
            column: 57
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 173
      },
      "17": {
        loc: {
          start: {
            line: 175,
            column: 8
          },
          end: {
            line: 176,
            column: 67
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 175,
            column: 8
          },
          end: {
            line: 176,
            column: 67
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 175
      },
      "18": {
        loc: {
          start: {
            line: 177,
            column: 8
          },
          end: {
            line: 178,
            column: 55
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 177,
            column: 8
          },
          end: {
            line: 178,
            column: 55
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 177
      },
      "19": {
        loc: {
          start: {
            line: 179,
            column: 8
          },
          end: {
            line: 180,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 179,
            column: 8
          },
          end: {
            line: 180,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 179
      },
      "20": {
        loc: {
          start: {
            line: 182,
            column: 8
          },
          end: {
            line: 184,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 182,
            column: 8
          },
          end: {
            line: 184,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 182
      },
      "21": {
        loc: {
          start: {
            line: 185,
            column: 8
          },
          end: {
            line: 187,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 185,
            column: 8
          },
          end: {
            line: 187,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 185
      },
      "22": {
        loc: {
          start: {
            line: 188,
            column: 8
          },
          end: {
            line: 196,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 188,
            column: 8
          },
          end: {
            line: 196,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 188
      },
      "23": {
        loc: {
          start: {
            line: 190,
            column: 12
          },
          end: {
            line: 192,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 190,
            column: 12
          },
          end: {
            line: 192,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 190
      },
      "24": {
        loc: {
          start: {
            line: 190,
            column: 16
          },
          end: {
            line: 190,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 190,
            column: 16
          },
          end: {
            line: 190,
            column: 37
          }
        }, {
          start: {
            line: 190,
            column: 41
          },
          end: {
            line: 190,
            column: 64
          }
        }],
        line: 190
      },
      "25": {
        loc: {
          start: {
            line: 193,
            column: 12
          },
          end: {
            line: 195,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 193,
            column: 12
          },
          end: {
            line: 195,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 193
      },
      "26": {
        loc: {
          start: {
            line: 202,
            column: 8
          },
          end: {
            line: 216,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 202,
            column: 8
          },
          end: {
            line: 216,
            column: 9
          }
        }, {
          start: {
            line: 205,
            column: 13
          },
          end: {
            line: 216,
            column: 9
          }
        }],
        line: 202
      },
      "27": {
        loc: {
          start: {
            line: 205,
            column: 13
          },
          end: {
            line: 216,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 205,
            column: 13
          },
          end: {
            line: 216,
            column: 9
          }
        }, {
          start: {
            line: 208,
            column: 13
          },
          end: {
            line: 216,
            column: 9
          }
        }],
        line: 205
      },
      "28": {
        loc: {
          start: {
            line: 208,
            column: 13
          },
          end: {
            line: 216,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 208,
            column: 13
          },
          end: {
            line: 216,
            column: 9
          }
        }, {
          start: {
            line: 211,
            column: 13
          },
          end: {
            line: 216,
            column: 9
          }
        }],
        line: 208
      },
      "29": {
        loc: {
          start: {
            line: 211,
            column: 13
          },
          end: {
            line: 216,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 211,
            column: 13
          },
          end: {
            line: 216,
            column: 9
          }
        }, {
          start: {
            line: 214,
            column: 13
          },
          end: {
            line: 216,
            column: 9
          }
        }],
        line: 211
      },
      "30": {
        loc: {
          start: {
            line: 221,
            column: 91
          },
          end: {
            line: 221,
            column: 104
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 221,
            column: 102
          },
          end: {
            line: 221,
            column: 104
          }
        }],
        line: 221
      },
      "31": {
        loc: {
          start: {
            line: 223,
            column: 8
          },
          end: {
            line: 238,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 223,
            column: 8
          },
          end: {
            line: 238,
            column: 9
          }
        }, {
          start: {
            line: 226,
            column: 13
          },
          end: {
            line: 238,
            column: 9
          }
        }],
        line: 223
      },
      "32": {
        loc: {
          start: {
            line: 265,
            column: 15
          },
          end: {
            line: 265,
            column: 51
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 265,
            column: 15
          },
          end: {
            line: 265,
            column: 41
          }
        }, {
          start: {
            line: 265,
            column: 45
          },
          end: {
            line: 265,
            column: 51
          }
        }],
        line: 265
      },
      "33": {
        loc: {
          start: {
            line: 272,
            column: 8
          },
          end: {
            line: 274,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 272,
            column: 8
          },
          end: {
            line: 274,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 272
      },
      "34": {
        loc: {
          start: {
            line: 275,
            column: 8
          },
          end: {
            line: 293,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 276,
            column: 12
          },
          end: {
            line: 277,
            column: 78
          }
        }, {
          start: {
            line: 278,
            column: 12
          },
          end: {
            line: 279,
            column: 82
          }
        }, {
          start: {
            line: 280,
            column: 12
          },
          end: {
            line: 281,
            column: 74
          }
        }, {
          start: {
            line: 282,
            column: 12
          },
          end: {
            line: 283,
            column: 71
          }
        }, {
          start: {
            line: 284,
            column: 12
          },
          end: {
            line: 285,
            column: 68
          }
        }, {
          start: {
            line: 286,
            column: 12
          },
          end: {
            line: 287,
            column: 80
          }
        }, {
          start: {
            line: 288,
            column: 12
          },
          end: {
            line: 289,
            column: 89
          }
        }, {
          start: {
            line: 290,
            column: 12
          },
          end: {
            line: 292,
            column: 78
          }
        }],
        line: 275
      },
      "35": {
        loc: {
          start: {
            line: 302,
            column: 12
          },
          end: {
            line: 304,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 302,
            column: 12
          },
          end: {
            line: 304,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 302
      },
      "36": {
        loc: {
          start: {
            line: 347,
            column: 8
          },
          end: {
            line: 354,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 347,
            column: 8
          },
          end: {
            line: 354,
            column: 9
          }
        }, {
          start: {
            line: 351,
            column: 13
          },
          end: {
            line: 354,
            column: 9
          }
        }],
        line: 347
      },
      "37": {
        loc: {
          start: {
            line: 360,
            column: 8
          },
          end: {
            line: 376,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 361,
            column: 12
          },
          end: {
            line: 362,
            column: 43
          }
        }, {
          start: {
            line: 363,
            column: 12
          },
          end: {
            line: 368,
            column: 69
          }
        }, {
          start: {
            line: 369,
            column: 12
          },
          end: {
            line: 370,
            column: 71
          }
        }, {
          start: {
            line: 371,
            column: 12
          },
          end: {
            line: 371,
            column: 44
          }
        }, {
          start: {
            line: 372,
            column: 12
          },
          end: {
            line: 373,
            column: 78
          }
        }, {
          start: {
            line: 374,
            column: 12
          },
          end: {
            line: 375,
            column: 78
          }
        }],
        line: 360
      },
      "38": {
        loc: {
          start: {
            line: 388,
            column: 8
          },
          end: {
            line: 394,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 388,
            column: 8
          },
          end: {
            line: 394,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 388
      },
      "39": {
        loc: {
          start: {
            line: 401,
            column: 8
          },
          end: {
            line: 403,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 401,
            column: 8
          },
          end: {
            line: 403,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 401
      },
      "40": {
        loc: {
          start: {
            line: 404,
            column: 8
          },
          end: {
            line: 415,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 404,
            column: 8
          },
          end: {
            line: 415,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 404
      },
      "41": {
        loc: {
          start: {
            line: 404,
            column: 12
          },
          end: {
            line: 404,
            column: 54
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 404,
            column: 12
          },
          end: {
            line: 404,
            column: 39
          }
        }, {
          start: {
            line: 404,
            column: 43
          },
          end: {
            line: 404,
            column: 54
          }
        }],
        line: 404
      },
      "42": {
        loc: {
          start: {
            line: 406,
            column: 12
          },
          end: {
            line: 407,
            column: 27
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 406,
            column: 12
          },
          end: {
            line: 407,
            column: 27
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 406
      },
      "43": {
        loc: {
          start: {
            line: 408,
            column: 12
          },
          end: {
            line: 409,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 408,
            column: 12
          },
          end: {
            line: 409,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 408
      },
      "44": {
        loc: {
          start: {
            line: 410,
            column: 12
          },
          end: {
            line: 411,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 410,
            column: 12
          },
          end: {
            line: 411,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 410
      },
      "45": {
        loc: {
          start: {
            line: 412,
            column: 12
          },
          end: {
            line: 413,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 412,
            column: 12
          },
          end: {
            line: 413,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 412
      },
      "46": {
        loc: {
          start: {
            line: 422,
            column: 8
          },
          end: {
            line: 423,
            column: 23
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 422,
            column: 8
          },
          end: {
            line: 423,
            column: 23
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 422
      },
      "47": {
        loc: {
          start: {
            line: 424,
            column: 16
          },
          end: {
            line: 424,
            column: 34
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 424,
            column: 31
          },
          end: {
            line: 424,
            column: 34
          }
        }],
        line: 424
      },
      "48": {
        loc: {
          start: {
            line: 424,
            column: 36
          },
          end: {
            line: 424,
            column: 53
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 424,
            column: 50
          },
          end: {
            line: 424,
            column: 53
          }
        }],
        line: 424
      },
      "49": {
        loc: {
          start: {
            line: 424,
            column: 55
          },
          end: {
            line: 424,
            column: 74
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 424,
            column: 71
          },
          end: {
            line: 424,
            column: 74
          }
        }],
        line: 424
      },
      "50": {
        loc: {
          start: {
            line: 433,
            column: 8
          },
          end: {
            line: 435,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 433,
            column: 8
          },
          end: {
            line: 435,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 433
      },
      "51": {
        loc: {
          start: {
            line: 437,
            column: 8
          },
          end: {
            line: 439,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 437,
            column: 8
          },
          end: {
            line: 439,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 437
      },
      "52": {
        loc: {
          start: {
            line: 452,
            column: 8
          },
          end: {
            line: 454,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 452,
            column: 8
          },
          end: {
            line: 454,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 452
      },
      "53": {
        loc: {
          start: {
            line: 456,
            column: 8
          },
          end: {
            line: 458,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 456,
            column: 8
          },
          end: {
            line: 458,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 456
      },
      "54": {
        loc: {
          start: {
            line: 460,
            column: 8
          },
          end: {
            line: 462,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 460,
            column: 8
          },
          end: {
            line: 462,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 460
      },
      "55": {
        loc: {
          start: {
            line: 464,
            column: 8
          },
          end: {
            line: 466,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 464,
            column: 8
          },
          end: {
            line: 466,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 464
      },
      "56": {
        loc: {
          start: {
            line: 481,
            column: 15
          },
          end: {
            line: 481,
            column: 55
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 481,
            column: 15
          },
          end: {
            line: 481,
            column: 35
          }
        }, {
          start: {
            line: 481,
            column: 39
          },
          end: {
            line: 481,
            column: 55
          }
        }],
        line: 481
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0],
      "5": [0],
      "6": [0],
      "7": [0],
      "8": [0],
      "9": [0, 0],
      "10": [0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0, 0, 0, 0, 0, 0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0, 0, 0, 0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0],
      "48": [0],
      "49": [0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\EnhancedFrictionCalculator.ts",
      mappings: ";AAAA;;;;;;;;;GASG;;;AAEH,uEAAmF;AAEnF;;GAEG;AACH,IAAY,cAQX;AARD,WAAY,cAAc;IACxB,qDAAmC,CAAA;IACnC,iCAAe,CAAA;IACf,6CAA2B,CAAA;IAC3B,qCAAmB,CAAA;IACnB,+BAAa,CAAA;IACb,yDAAuC,CAAA;IACvC,mDAAiC,CAAA;AACnC,CAAC,EARW,cAAc,8BAAd,cAAc,QAQzB;AAED;;GAEG;AACH,IAAY,UAMX;AAND,WAAY,UAAU;IACpB,iCAAmB,CAAA;IACnB,2CAA6B,CAAA;IAC7B,mDAAqC,CAAA;IACrC,iDAAmC,CAAA;IACnC,yCAA2B,CAAA;AAC7B,CAAC,EANW,UAAU,0BAAV,UAAU,QAMrB;AAED;;GAEG;AACH,IAAY,WAMX;AAND,WAAY,WAAW;IACrB,0BAAW,CAAA;IACX,4BAAa,CAAA;IACb,kCAAmB,CAAA;IACnB,4BAAa,CAAA;IACb,sCAAuB,CAAA;AACzB,CAAC,EANW,WAAW,2BAAX,WAAW,QAMtB;AAED;;GAEG;AACH,IAAY,gBAMX;AAND,WAAY,gBAAgB;IAC1B,2CAAuB,CAAA;IACvB,iCAAa,CAAA;IACb,uCAAmB,CAAA;IACnB,iCAAa,CAAA;IACb,2CAAuB,CAAA;AACzB,CAAC,EANW,gBAAgB,gCAAhB,gBAAgB,QAM3B;AAkED;;;;;GAKG;AACH,MAAa,0BAA0B;IA6CrC;;OAEG;IACI,MAAM,CAAC,qBAAqB,CAAC,KAA+B;QACjE,MAAM,EACJ,QAAQ,EACR,iBAAiB,EACjB,MAAM,EACN,QAAQ,EACR,MAAM,GAAG,cAAc,CAAC,cAAc,EACtC,aAAa,EACb,WAAW,GAAG,WAAW,CAAC,IAAI,EAC9B,gBAAgB,GAAG,gBAAgB,CAAC,IAAI,EACxC,eAAe,EACf,SAAS,GAAG,OAAO,EACnB,WAAW,EACX,iBAAiB,EACjB,eAAe,GAAG,UAAU,EAC7B,GAAG,KAAK,CAAC;QAEV,MAAM,QAAQ,GAAa,EAAE,CAAC;QAC9B,MAAM,eAAe,GAAa,EAAE,CAAC;QAErC,kBAAkB;QAClB,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,eAAe,EAAE,QAAQ,CAAC,CAAC;QAEtD,qBAAqB;QACrB,MAAM,QAAQ,GAAG,aAAa,CAAC,CAAC;YAC9B,iDAAuB,CAAC,sBAAsB,CAAC,aAAa,CAAC,CAAC,CAAC;YAC/D;gBACE,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO;gBAClC,SAAS,EAAE,IAAI,CAAC,YAAY,CAAC,SAAS;gBACtC,QAAQ,EAAE,EAAE;aACb,CAAC;QAEJ,QAAQ,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAEpC,4BAA4B;QAC5B,MAAM,WAAW,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAC,qBAAqB;QACxD,MAAM,UAAU,GAAG,iBAAiB,GAAG,EAAE,CAAC,CAAC,yBAAyB;QACpE,MAAM,cAAc,GAAG,CAAC,QAAQ,CAAC,OAAO,GAAG,WAAW,GAAG,UAAU,CAAC,GAAG,QAAQ,CAAC,SAAS,CAAC;QAE1F,wBAAwB;QACxB,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;QAE3D,0BAA0B;QAC1B,MAAM,kBAAkB,GAAG,IAAI,CAAC,qBAAqB,CACnD,QAAQ,EACR,WAAW,EACX,gBAAgB,EAChB,eAAe,EACf,QAAQ,CACT,CAAC;QAEF,+BAA+B;QAC/B,MAAM,iBAAiB,GAAG,kBAAkB,CAAC,iBAAiB,GAAG,UAAU,CAAC;QAE5E,mDAAmD;QACnD,MAAM,cAAc,GAAG,IAAI,CAAC,uBAAuB,CACjD,MAAM,EACN,cAAc,EACd,iBAAiB,EACjB,UAAU,EACV,QAAQ,CACT,CAAC;QAEF,sCAAsC;QACtC,MAAM,wBAAwB,GAAG,IAAI,CAAC,iCAAiC,CACrE,aAAa,EACb,QAAQ,CAAC,OAAO,CACjB,CAAC;QAEF,8CAA8C;QAC9C,MAAM,eAAe,GAAG,IAAI,CAAC,wBAAwB,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;QAE9E,sCAAsC;QACtC,MAAM,qBAAqB,GAAG,IAAI,CAAC,8BAA8B,CAAC,iBAAiB,CAAC,CAAC;QAErF,wDAAwD;QACxD,MAAM,gBAAgB,GAAG,cAAc,GAAG,CAAC,MAAM,GAAG,UAAU,CAAC;YACvC,CAAC,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;QAErF,wBAAwB;QACxB,MAAM,eAAe,GAAG,wBAAwB,CAAC,QAAQ,GAAG,eAAe,GAAG,qBAAqB,CAAC;QACpG,MAAM,qBAAqB,GAAG,CAAC,gBAAgB,GAAG,GAAG,CAAC,GAAG,eAAe,CAAC,CAAC,yBAAyB;QAEnG,0BAA0B;QAC1B,MAAM,YAAY,GAAG,CAAC,qBAAqB,GAAG,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,2BAA2B;QAExF,+BAA+B;QAC/B,MAAM,iBAAiB,GAAG,IAAI,CAAC,0BAA0B,CACvD,qBAAqB,EACrB,MAAM,EACN,UAAU,EACV,iBAAiB,CAClB,CAAC;QAEF,2BAA2B;QAC3B,IAAI,CAAC,uBAAuB,CAC1B,QAAQ,EACR,cAAc,EACd,YAAY,EACZ,kBAAkB,EAClB,eAAe,CAChB,CAAC;QAEF,OAAO;YACL,YAAY,EAAE,qBAAqB;YACnC,cAAc;YACd,YAAY;YACZ,MAAM;YACN,UAAU;YACV,cAAc;YACd,iBAAiB;YACjB,kBAAkB;YAClB,wBAAwB;YACxB,QAAQ,EAAE,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;YACtC,iBAAiB;YACjB,QAAQ;YACR,eAAe;YACf,kBAAkB,EAAE;gBAClB,OAAO,EAAE,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC;gBAC3C,kBAAkB,EAAE;oBAClB,gBAAgB;oBAChB,eAAe;oBACf,WAAW;oBACX,UAAU;iBACX;gBACD,iBAAiB,EAAE,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC;aACrD;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,gBAAgB,CAC5B,cAAsB,EACtB,iBAAyB,EACzB,WAA4C,UAAU;QAEtD,mBAAmB;QACnB,IAAI,cAAc,GAAG,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE,CAAC;YAC3D,OAAO,cAAc,CAAC,cAAc,CAAC,CAAC,4CAA4C;QACpF,CAAC;QAED,4DAA4D;QAC5D,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;YAC3B,OAAO,cAAc,CAAC,eAAe,CAAC,CAAC,8BAA8B;QACvE,CAAC;QAED,IAAI,QAAQ,KAAK,MAAM,EAAE,CAAC;YACxB,OAAO,cAAc,CAAC,OAAO,CAAC,CAAC,0BAA0B;QAC3D,CAAC;QAED,4DAA4D;QAC5D,IAAI,iBAAiB,GAAG,KAAK,EAAE,CAAC;YAC9B,OAAO,cAAc,CAAC,WAAW,CAAC,CAAC,wBAAwB;QAC7D,CAAC;aAAM,CAAC;YACN,OAAO,cAAc,CAAC,IAAI,CAAC,CAAC,uBAAuB;QACrD,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,cAAc,CAC3B,KAA+B,EAC/B,eAAuB,EACvB,QAAkB;QAElB,IAAI,eAAe,KAAK,MAAM;YAAE,OAAO;QAEvC,MAAM,EAAE,QAAQ,EAAE,iBAAiB,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC;QAEtD,mBAAmB;QACnB,IAAI,QAAQ,IAAI,CAAC;YAAE,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAChE,IAAI,iBAAiB,IAAI,CAAC;YAAE,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;QACnF,IAAI,MAAM,IAAI,CAAC;YAAE,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAE5D,IAAI,eAAe,KAAK,OAAO;YAAE,OAAO;QAExC,sBAAsB;QACtB,IAAI,QAAQ,GAAG,IAAI,EAAE,CAAC;YACpB,QAAQ,CAAC,IAAI,CAAC,4DAA4D,CAAC,CAAC;QAC9E,CAAC;QACD,IAAI,QAAQ,GAAG,GAAG,EAAE,CAAC;YACnB,QAAQ,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;QAChE,CAAC;QAED,IAAI,eAAe,KAAK,QAAQ,EAAE,CAAC;YACjC,oBAAoB;YACpB,IAAI,iBAAiB,GAAG,CAAC,IAAI,iBAAiB,GAAG,GAAG,EAAE,CAAC;gBACrD,QAAQ,CAAC,IAAI,CAAC,8DAA8D,CAAC,CAAC;YAChF,CAAC;YACD,IAAI,MAAM,GAAG,IAAI,EAAE,CAAC;gBAClB,QAAQ,CAAC,IAAI,CAAC,kEAAkE,CAAC,CAAC;YACpF,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,kBAAkB,CAAC,cAAsB;QACtD,IAAI,cAAc,GAAG,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE,CAAC;YAC3D,OAAO,UAAU,CAAC,OAAO,CAAC;QAC5B,CAAC;aAAM,IAAI,cAAc,GAAG,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,EAAE,CAAC;YACvE,OAAO,UAAU,CAAC,YAAY,CAAC;QACjC,CAAC;aAAM,IAAI,cAAc,GAAG,MAAM,EAAE,CAAC;YACnC,OAAO,UAAU,CAAC,gBAAgB,CAAC;QACrC,CAAC;aAAM,IAAI,cAAc,GAAG,OAAO,EAAE,CAAC;YACpC,OAAO,UAAU,CAAC,eAAe,CAAC;QACpC,CAAC;aAAM,CAAC;YACN,OAAO,UAAU,CAAC,WAAW,CAAC;QAChC,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,qBAAqB,CAClC,QAAgB,EAChB,WAAwB,EACxB,gBAAkC,EAClC,eAAwB,EACxB,WAAqB,EAAE;QAEvB,IAAI,aAAqB,CAAC;QAE1B,IAAI,eAAe,KAAK,SAAS,EAAE,CAAC;YAClC,aAAa,GAAG,eAAe,CAAC;QAClC,CAAC;aAAM,CAAC;YACN,4CAA4C;YAC5C,IAAI,CAAC;gBACH,MAAM,YAAY,GAAG,iDAAuB,CAAC,4BAA4B,CACvE,QAAQ,EACR,WAAW,EACX,gBAAgB,CACjB,CAAC;gBACF,aAAa,GAAG,YAAY,CAAC,iBAAiB,CAAC;gBAC/C,QAAQ,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC;YAC1C,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,6BAA6B;gBAC7B,aAAa,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;gBACnD,QAAQ,CAAC,IAAI,CAAC,yCAAyC,QAAQ,EAAE,CAAC,CAAC;YACrE,CAAC;QACH,CAAC;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;QACpD,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC;QAC7D,MAAM,cAAc,GAAG,WAAW,GAAG,aAAa,CAAC;QACnD,MAAM,iBAAiB,GAAG,aAAa,GAAG,cAAc,CAAC;QAEzD,OAAO;YACL,aAAa;YACb,iBAAiB;YACjB,WAAW;YACX,aAAa;YACb,cAAc;SACf,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,mBAAmB,CAAC,QAAgB;QACjD,MAAM,gBAAgB,GAA2B;YAC/C,kBAAkB,EAAE,MAAM;YAC1B,iBAAiB,EAAE,OAAO;YAC1B,UAAU,EAAE,OAAO;YACnB,KAAK,EAAE,QAAQ;YACf,YAAY,EAAE,MAAM;YACpB,UAAU,EAAE,KAAK;YACjB,WAAW,EAAE,OAAO;YACpB,eAAe,EAAE,KAAK;SACvB,CAAC;QAEF,OAAO,gBAAgB,CAAC,QAAQ,CAAC,IAAI,MAAM,CAAC,CAAC,8BAA8B;IAC7E,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,uBAAuB,CACpC,MAAsB,EACtB,cAAsB,EACtB,iBAAyB,EACzB,UAAsB,EACtB,QAAkB;QAElB,iEAAiE;QACjE,IAAI,UAAU,KAAK,UAAU,CAAC,OAAO,EAAE,CAAC;YACtC,OAAO,EAAE,GAAG,cAAc,CAAC;QAC7B,CAAC;QAED,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,cAAc,CAAC,eAAe;gBACjC,OAAO,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,iBAAiB,CAAC,CAAC;YAEhE,KAAK,cAAc,CAAC,KAAK;gBACvB,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,iBAAiB,CAAC,CAAC;YAEpE,KAAK,cAAc,CAAC,WAAW;gBAC7B,OAAO,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,iBAAiB,CAAC,CAAC;YAE5D,KAAK,cAAc,CAAC,OAAO;gBACzB,OAAO,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,iBAAiB,CAAC,CAAC;YAEzD,KAAK,cAAc,CAAC,IAAI;gBACtB,OAAO,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,iBAAiB,CAAC,CAAC;YAEtD,KAAK,cAAc,CAAC,iBAAiB;gBACnC,OAAO,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,iBAAiB,CAAC,CAAC;YAElE,KAAK,cAAc,CAAC,cAAc;gBAChC,OAAO,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,iBAAiB,EAAE,UAAU,CAAC,CAAC;YAE3E;gBACE,QAAQ,CAAC,IAAI,CAAC,kBAAkB,MAAM,yBAAyB,CAAC,CAAC;gBACjE,OAAO,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,iBAAiB,CAAC,CAAC;QAClE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,cAAc,CAAC,cAAsB,EAAE,iBAAyB;QAC7E,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,gBAAgB;QAE9B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;YAC5B,MAAM,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CACvC,iBAAiB,GAAG,GAAG,GAAG,IAAI,GAAG,CAAC,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CACjE,EAAE,CAAC,CAAC,CAAC;YAEN,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,MAAM,EAAE,CAAC;gBAChC,MAAM;YACR,CAAC;YACD,CAAC,GAAG,IAAI,CAAC;QACX,CAAC;QAED,OAAO,CAAC,CAAC;IACX,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,UAAU,CAAC,cAAsB,EAAE,iBAAyB;QACzE,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC1G,OAAO,IAAI,GAAG,SAAS,CAAC;IAC1B,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,OAAO,CAAC,cAAsB,EAAE,iBAAyB;QACtE,MAAM,KAAK,GAAG,iBAAiB,GAAG,GAAG,CAAC;QACtC,MAAM,KAAK,GAAG,GAAG,GAAG,cAAc,CAAC;QACnC,OAAO,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;IAC3E,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,IAAI,CAAC,cAAsB,EAAE,iBAAyB;QACnE,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,MAAM,GAAG,MAAM,GAAG,cAAc;YAChE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,iBAAiB,EAAE,MAAM,CAAC,GAAG,MAAM;gBACvD,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,cAAc,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACjE,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IACrB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,gBAAgB,CAAC,cAAsB,EAAE,iBAAyB;QAC/E,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,GAAG,GAAG,IAAI,GAAG,cAAc;YAC/D,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,GAAG,GAAG,IAAI,GAAG,cAAc;gBAC1D,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,GAAG,GAAG,EAAE,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;QACtE,OAAO,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,kBAAkB,CAAC,cAAsB,EAAE,iBAAyB;QACjF,iCAAiC;QACjC,IAAI,iBAAiB,GAAG,MAAM,EAAE,CAAC;YAC/B,4BAA4B;YAC5B,OAAO,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;QAChD,CAAC;aAAM,CAAC;YACN,2BAA2B;YAC3B,OAAO,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,iBAAiB,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,aAAa,CAC1B,cAAsB,EACtB,iBAAyB,EACzB,UAAsB;QAEtB,QAAQ,UAAU,EAAE,CAAC;YACnB,KAAK,UAAU,CAAC,OAAO;gBACrB,OAAO,EAAE,GAAG,cAAc,CAAC;YAE7B,KAAK,UAAU,CAAC,YAAY;gBAC1B,4CAA4C;gBAC5C,MAAM,QAAQ,GAAG,EAAE,GAAG,cAAc,CAAC;gBACrC,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,iBAAiB,CAAC,CAAC;gBAChE,MAAM,MAAM,GAAG,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;gBACvD,OAAO,QAAQ,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,UAAU,GAAG,MAAM,CAAC;YAEvD,KAAK,UAAU,CAAC,gBAAgB;gBAC9B,OAAO,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,iBAAiB,CAAC,CAAC;YAEzD,KAAK,UAAU,CAAC,eAAe,CAAC;YAChC,KAAK,UAAU,CAAC,WAAW;gBACzB,OAAO,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,iBAAiB,CAAC,CAAC;YAEhE;gBACE,OAAO,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,iBAAiB,CAAC,CAAC;QAClE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,iCAAiC,CAC9C,aAA6B,EAC7B,UAAmB;QAEnB,MAAM,WAAW,GAAG;YAClB,WAAW,EAAE,GAAG;YAChB,QAAQ,EAAE,GAAG;YACb,QAAQ,EAAE,GAAG;YACb,QAAQ,EAAE,GAAG;SACd,CAAC;QAEF,IAAI,UAAU,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;YAC5D,WAAW,CAAC,WAAW,GAAG,YAAY,CAAC;YACvC,WAAW,CAAC,QAAQ,GAAG,YAAY,CAAC;YACpC,WAAW,CAAC,QAAQ,GAAG,YAAY,CAAC;YACpC,WAAW,CAAC,QAAQ,GAAG,YAAY,CAAC;QACtC,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,wBAAwB,CACrC,SAAiB,EACjB,WAAoB;QAEpB,IAAI,SAAS,KAAK,OAAO,EAAE,CAAC;YAC1B,OAAO,GAAG,CAAC;QACb,CAAC;QAED,IAAI,SAAS,KAAK,aAAa,IAAI,WAAW,EAAE,CAAC;YAC/C,yDAAyD;YACzD,IAAI,WAAW,IAAI,GAAG;gBAAE,OAAO,GAAG,CAAC;YACnC,IAAI,WAAW,IAAI,GAAG;gBAAE,OAAO,IAAI,CAAC;YACpC,IAAI,WAAW,IAAI,GAAG;gBAAE,OAAO,IAAI,CAAC;YACpC,IAAI,WAAW,IAAI,GAAG;gBAAE,OAAO,IAAI,CAAC;YACpC,OAAO,IAAI,CAAC,CAAC,4BAA4B;QAC3C,CAAC;QAED,OAAO,IAAI,CAAC,CAAC,0CAA0C;IACzD,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,8BAA8B,CAC3C,iBAAiE;QAEjE,IAAI,CAAC,iBAAiB;YAAE,OAAO,GAAG,CAAC;QAEnC,MAAM,EAAE,YAAY,GAAG,GAAG,EAAE,WAAW,GAAG,GAAG,EAAE,aAAa,GAAG,GAAG,EAAE,GAAG,iBAAiB,CAAC;QACzF,OAAO,YAAY,GAAG,WAAW,GAAG,aAAa,CAAC;IACpD,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,0BAA0B,CACvC,YAAoB,EACpB,MAAsB,EACtB,UAAsB,EACtB,iBAAyB;QAEzB,IAAI,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QAEhD,uCAAuC;QACvC,IAAI,UAAU,KAAK,UAAU,CAAC,YAAY,EAAE,CAAC;YAC3C,YAAY,IAAI,GAAG,CAAC,CAAC,0CAA0C;QACjE,CAAC;QAED,8CAA8C;QAC9C,IAAI,iBAAiB,GAAG,IAAI,EAAE,CAAC;YAC7B,YAAY,IAAI,IAAI,CAAC,CAAC,2CAA2C;QACnE,CAAC;QAED,MAAM,WAAW,GAAG,YAAY,GAAG,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC;QAEtD,OAAO;YACL,KAAK,EAAE,YAAY,GAAG,WAAW;YACjC,KAAK,EAAE,YAAY,GAAG,WAAW;YACjC,eAAe,EAAE,YAAY;SAC9B,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,uBAAuB,CACpC,QAAgB,EAChB,cAAsB,EACtB,YAAoB,EACpB,kBAAuB,EACvB,eAAyB;QAEzB,2BAA2B;QAC3B,IAAI,QAAQ,GAAG,IAAI,EAAE,CAAC;YACpB,eAAe,CAAC,IAAI,CAAC,iFAAiF,CAAC,CAAC;QAC1G,CAAC;QAED,gCAAgC;QAChC,IAAI,YAAY,GAAG,IAAI,EAAE,CAAC;YACxB,eAAe,CAAC,IAAI,CAAC,kEAAkE,CAAC,CAAC;QAC3F,CAAC;QAED,2BAA2B;QAC3B,IAAI,kBAAkB,CAAC,cAAc,GAAG,GAAG,EAAE,CAAC;YAC5C,eAAe,CAAC,IAAI,CAAC,4FAA4F,CAAC,CAAC;QACrH,CAAC;QAED,8BAA8B;QAC9B,IAAI,cAAc,GAAG,IAAI,EAAE,CAAC;YAC1B,eAAe,CAAC,IAAI,CAAC,qFAAqF,CAAC,CAAC;QAC9G,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,qBAAqB,CAAC,MAAsB;QACzD,MAAM,YAAY,GAAmC;YACnD,CAAC,cAAc,CAAC,eAAe,CAAC,EAAE,sCAAsC;YACxE,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,6BAA6B;YACrD,CAAC,cAAc,CAAC,WAAW,CAAC,EAAE,yCAAyC;YACvE,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,0CAA0C;YACpE,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,6BAA6B;YACpD,CAAC,cAAc,CAAC,iBAAiB,CAAC,EAAE,0CAA0C;YAC9E,CAAC,cAAc,CAAC,cAAc,CAAC,EAAE,sCAAsC;SACxE,CAAC;QAEF,OAAO,YAAY,CAAC,MAAM,CAAC,IAAI,gBAAgB,CAAC;IAClD,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,oBAAoB,CAAC,MAAsB;QACxD,OAAO,+CAA+C,CAAC;IACzD,CAAC;;AAtmBH,gEAumBC;AAtmByB,kCAAO,GAAG,OAAO,CAAC;AAE1C,6CAA6C;AACrB,uCAAY,GAAG;IACrC,OAAO,EAAE,KAAK,EAAO,SAAS;IAC9B,SAAS,EAAE,MAAM,CAAI,YAAY;CAClC,CAAC;AAEF,oCAAoC;AACZ,+CAAoB,GAAG;IAC7C,WAAW,EAAE,IAAI;IACjB,gBAAgB,EAAE,IAAI;IACtB,aAAa,EAAE,IAAI;CACpB,CAAC;AAEF,4BAA4B;AACJ,0CAAe,GAAG;IACxC,CAAC,cAAc,CAAC,eAAe,CAAC,EAAE,IAAI;IACtC,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,IAAI;IAC5B,CAAC,cAAc,CAAC,WAAW,CAAC,EAAE,IAAI;IAClC,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,IAAI;IAC9B,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,IAAI;IAC3B,CAAC,cAAc,CAAC,iBAAiB,CAAC,EAAE,IAAI;IACxC,CAAC,cAAc,CAAC,cAAc,CAAC,EAAE,IAAI;CACtC,CAAC;AAEF,yBAAyB;AACD,wCAAa,GAAG;IACtC,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,GAAG;IACtB,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,GAAG;IACvB,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE,GAAG;IAC1B,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,GAAG;IACvB,CAAC,WAAW,CAAC,SAAS,CAAC,EAAE,GAAG;CAC7B,CAAC;AAEF,4BAA4B;AACJ,0CAAe,GAAG;IACxC,CAAC,gBAAgB,CAAC,SAAS,CAAC,EAAE,GAAG;IACjC,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,GAAG;IAC5B,CAAC,gBAAgB,CAAC,OAAO,CAAC,EAAE,GAAG;IAC/B,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,GAAG;IAC5B,CAAC,gBAAgB,CAAC,SAAS,CAAC,EAAE,GAAG;CAClC,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\EnhancedFrictionCalculator.ts"],
      sourcesContent: ["/**\r\n * Enhanced Friction Calculator\r\n * \r\n * Comprehensive friction calculation service for Phase 3: Advanced Calculation Modules\r\n * Provides multiple friction factor calculation methods, material aging effects,\r\n * environmental corrections, and advanced features for HVAC duct systems.\r\n * \r\n * @version 3.0.0\r\n * <AUTHOR> Suite Development Team\r\n */\r\n\r\nimport { AirPropertiesCalculator, AirConditions } from './AirPropertiesCalculator';\r\n\r\n/**\r\n * Friction calculation method options\r\n */\r\nexport enum FrictionMethod {\r\n  COLEBROOK_WHITE = 'colebrook_white',\r\n  MOODY = 'moody',\r\n  SWAMEE_JAIN = 'swamee_jain',\r\n  HAALAND = 'haaland',\r\n  CHEN = 'chen',\r\n  ZIGRANG_SYLVESTER = 'zigrang_sylvester',\r\n  ENHANCED_DARCY = 'enhanced_darcy'\r\n}\r\n\r\n/**\r\n * Flow regime classification\r\n */\r\nexport enum FlowRegime {\r\n  LAMINAR = 'laminar',\r\n  TRANSITIONAL = 'transitional',\r\n  TURBULENT_SMOOTH = 'turbulent_smooth',\r\n  TURBULENT_ROUGH = 'turbulent_rough',\r\n  FULLY_ROUGH = 'fully_rough'\r\n}\r\n\r\n/**\r\n * Material aging condition\r\n */\r\nexport enum MaterialAge {\r\n  NEW = 'new',\r\n  GOOD = 'good',\r\n  AVERAGE = 'average',\r\n  POOR = 'poor',\r\n  VERY_POOR = 'very_poor'\r\n}\r\n\r\n/**\r\n * Surface condition classification\r\n */\r\nexport enum SurfaceCondition {\r\n  EXCELLENT = 'excellent',\r\n  GOOD = 'good',\r\n  AVERAGE = 'average',\r\n  POOR = 'poor',\r\n  VERY_POOR = 'very_poor'\r\n}\r\n\r\n/**\r\n * Enhanced friction calculation input parameters\r\n */\r\nexport interface FrictionCalculationInput {\r\n  velocity: number;                    // FPM\r\n  hydraulicDiameter: number;           // inches\r\n  length: number;                      // feet\r\n  material: string;                    // Material type\r\n  method?: FrictionMethod;             // Calculation method\r\n  airConditions?: AirConditions;       // Environmental conditions\r\n  materialAge?: MaterialAge;           // Material aging condition\r\n  surfaceCondition?: SurfaceCondition; // Surface condition\r\n  customRoughness?: number;            // Custom roughness value (feet)\r\n  ductShape?: 'round' | 'rectangular' | 'oval'; // Duct shape\r\n  aspectRatio?: number;                // For rectangular ducts (width/height)\r\n  correctionFactors?: {\r\n    installation: number;              // Installation quality factor\r\n    maintenance: number;               // Maintenance factor\r\n    environmental: number;             // Environmental exposure factor\r\n  };\r\n  validationLevel?: 'none' | 'basic' | 'standard' | 'strict';\r\n}\r\n\r\n/**\r\n * Enhanced friction calculation result\r\n */\r\nexport interface FrictionCalculationResult {\r\n  frictionLoss: number;                // inches w.g.\r\n  frictionFactor: number;              // Darcy friction factor\r\n  frictionRate: number;                // inches w.g. per 100 feet\r\n  method: FrictionMethod;              // Method used\r\n  flowRegime: FlowRegime;              // Flow regime classification\r\n  reynoldsNumber: number;              // Reynolds number\r\n  relativeRoughness: number;           // Relative roughness (\u03B5/D)\r\n  materialProperties: {\r\n    baseRoughness: number;             // Base material roughness (feet)\r\n    adjustedRoughness: number;         // Adjusted for aging/condition (feet)\r\n    agingFactor: number;               // Aging adjustment factor\r\n    surfaceFactor: number;             // Surface condition factor\r\n    combinedFactor: number;            // Combined adjustment factor\r\n  };\r\n  environmentalCorrections: {\r\n    temperature: number;               // Temperature correction factor\r\n    pressure: number;                  // Pressure correction factor\r\n    humidity: number;                  // Humidity correction factor\r\n    combined: number;                  // Combined environmental factor\r\n  };\r\n  accuracy: number;                    // Estimated accuracy (0-1)\r\n  uncertaintyBounds?: {\r\n    lower: number;                     // Lower bound (inches w.g.)\r\n    upper: number;                     // Upper bound (inches w.g.)\r\n    confidenceLevel: number;           // Confidence level (0-1)\r\n  };\r\n  warnings: string[];\r\n  recommendations: string[];\r\n  calculationDetails: {\r\n    formula: string;                   // Formula used\r\n    iterations?: number;               // Number of iterations (for iterative methods)\r\n    convergence?: number;              // Convergence criteria achieved\r\n    intermediateValues: Record<string, number>;\r\n    standardReference: string;         // Reference standard\r\n  };\r\n}\r\n\r\n/**\r\n * Enhanced Friction Calculator\r\n * \r\n * Comprehensive friction calculation service providing multiple calculation methods,\r\n * material aging effects, environmental corrections, and advanced features.\r\n */\r\nexport class EnhancedFrictionCalculator {\r\n  private static readonly VERSION = '3.0.0';\r\n  \r\n  // Standard air properties at 70\xB0F, sea level\r\n  private static readonly STANDARD_AIR = {\r\n    density: 0.075,      // lb/ft\xB3\r\n    viscosity: 1.2e-5    // lb/(ft\xB7s)\r\n  };\r\n\r\n  // Reynolds number transition points\r\n  private static readonly REYNOLDS_TRANSITIONS = {\r\n    LAMINAR_MAX: 2300,\r\n    TRANSITIONAL_MAX: 4000,\r\n    TURBULENT_MIN: 4000\r\n  };\r\n\r\n  // Method accuracy estimates\r\n  private static readonly METHOD_ACCURACY = {\r\n    [FrictionMethod.COLEBROOK_WHITE]: 0.98,\r\n    [FrictionMethod.MOODY]: 0.95,\r\n    [FrictionMethod.SWAMEE_JAIN]: 0.96,\r\n    [FrictionMethod.HAALAND]: 0.97,\r\n    [FrictionMethod.CHEN]: 0.96,\r\n    [FrictionMethod.ZIGRANG_SYLVESTER]: 0.97,\r\n    [FrictionMethod.ENHANCED_DARCY]: 0.99\r\n  };\r\n\r\n  // Material aging factors\r\n  private static readonly AGING_FACTORS = {\r\n    [MaterialAge.NEW]: 1.0,\r\n    [MaterialAge.GOOD]: 1.2,\r\n    [MaterialAge.AVERAGE]: 1.5,\r\n    [MaterialAge.POOR]: 2.0,\r\n    [MaterialAge.VERY_POOR]: 3.0\r\n  };\r\n\r\n  // Surface condition factors\r\n  private static readonly SURFACE_FACTORS = {\r\n    [SurfaceCondition.EXCELLENT]: 0.8,\r\n    [SurfaceCondition.GOOD]: 1.0,\r\n    [SurfaceCondition.AVERAGE]: 1.3,\r\n    [SurfaceCondition.POOR]: 1.7,\r\n    [SurfaceCondition.VERY_POOR]: 2.5\r\n  };\r\n\r\n  /**\r\n   * Calculate enhanced friction loss with comprehensive corrections\r\n   */\r\n  public static calculateFrictionLoss(input: FrictionCalculationInput): FrictionCalculationResult {\r\n    const {\r\n      velocity,\r\n      hydraulicDiameter,\r\n      length,\r\n      material,\r\n      method = FrictionMethod.ENHANCED_DARCY,\r\n      airConditions,\r\n      materialAge = MaterialAge.GOOD,\r\n      surfaceCondition = SurfaceCondition.GOOD,\r\n      customRoughness,\r\n      ductShape = 'round',\r\n      aspectRatio,\r\n      correctionFactors,\r\n      validationLevel = 'standard'\r\n    } = input;\r\n\r\n    const warnings: string[] = [];\r\n    const recommendations: string[] = [];\r\n\r\n    // Validate inputs\r\n    this.validateInputs(input, validationLevel, warnings);\r\n\r\n    // Get air properties\r\n    const airProps = airConditions ? \r\n      AirPropertiesCalculator.calculateAirProperties(airConditions) :\r\n      { \r\n        density: this.STANDARD_AIR.density, \r\n        viscosity: this.STANDARD_AIR.viscosity,\r\n        warnings: []\r\n      };\r\n    \r\n    warnings.push(...airProps.warnings);\r\n\r\n    // Calculate Reynolds number\r\n    const velocityFps = velocity / 60; // Convert FPM to fps\r\n    const diameterFt = hydraulicDiameter / 12; // Convert inches to feet\r\n    const reynoldsNumber = (airProps.density * velocityFps * diameterFt) / airProps.viscosity;\r\n\r\n    // Determine flow regime\r\n    const flowRegime = this.classifyFlowRegime(reynoldsNumber);\r\n\r\n    // Get material properties\r\n    const materialProperties = this.getMaterialProperties(\r\n      material,\r\n      materialAge,\r\n      surfaceCondition,\r\n      customRoughness,\r\n      warnings\r\n    );\r\n\r\n    // Calculate relative roughness\r\n    const relativeRoughness = materialProperties.adjustedRoughness / diameterFt;\r\n\r\n    // Calculate friction factor using specified method\r\n    const frictionFactor = this.calculateFrictionFactor(\r\n      method,\r\n      reynoldsNumber,\r\n      relativeRoughness,\r\n      flowRegime,\r\n      warnings\r\n    );\r\n\r\n    // Calculate environmental corrections\r\n    const environmentalCorrections = this.calculateEnvironmentalCorrections(\r\n      airConditions,\r\n      airProps.density\r\n    );\r\n\r\n    // Apply shape corrections for non-round ducts\r\n    const shapeCorrection = this.calculateShapeCorrection(ductShape, aspectRatio);\r\n\r\n    // Apply additional correction factors\r\n    const additionalCorrections = this.calculateAdditionalCorrections(correctionFactors);\r\n\r\n    // Calculate friction loss using Darcy-Weisbach equation\r\n    const baseFrictionLoss = frictionFactor * (length / diameterFt) * \r\n                            (airProps.density * Math.pow(velocityFps, 2)) / (2 * 32.174);\r\n\r\n    // Apply all corrections\r\n    const totalCorrection = environmentalCorrections.combined * shapeCorrection * additionalCorrections;\r\n    const correctedFrictionLoss = (baseFrictionLoss / 5.2) * totalCorrection; // Convert to inches w.g.\r\n\r\n    // Calculate friction rate\r\n    const frictionRate = (correctedFrictionLoss / length) * 100; // inches w.g. per 100 feet\r\n\r\n    // Calculate uncertainty bounds\r\n    const uncertaintyBounds = this.calculateUncertaintyBounds(\r\n      correctedFrictionLoss,\r\n      method,\r\n      flowRegime,\r\n      relativeRoughness\r\n    );\r\n\r\n    // Generate recommendations\r\n    this.generateRecommendations(\r\n      velocity,\r\n      reynoldsNumber,\r\n      frictionRate,\r\n      materialProperties,\r\n      recommendations\r\n    );\r\n\r\n    return {\r\n      frictionLoss: correctedFrictionLoss,\r\n      frictionFactor,\r\n      frictionRate,\r\n      method,\r\n      flowRegime,\r\n      reynoldsNumber,\r\n      relativeRoughness,\r\n      materialProperties,\r\n      environmentalCorrections,\r\n      accuracy: this.METHOD_ACCURACY[method],\r\n      uncertaintyBounds,\r\n      warnings,\r\n      recommendations,\r\n      calculationDetails: {\r\n        formula: this.getFormulaDescription(method),\r\n        intermediateValues: {\r\n          baseFrictionLoss,\r\n          totalCorrection,\r\n          velocityFps,\r\n          diameterFt\r\n        },\r\n        standardReference: this.getStandardReference(method)\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get optimal friction calculation method for given conditions\r\n   */\r\n  public static getOptimalMethod(\r\n    reynoldsNumber: number,\r\n    relativeRoughness: number,\r\n    accuracy: 'standard' | 'high' | 'maximum' = 'standard'\r\n  ): FrictionMethod {\r\n    // For laminar flow\r\n    if (reynoldsNumber < this.REYNOLDS_TRANSITIONS.LAMINAR_MAX) {\r\n      return FrictionMethod.ENHANCED_DARCY; // Uses analytical solution for laminar flow\r\n    }\r\n\r\n    // For turbulent flow, choose based on accuracy requirements\r\n    if (accuracy === 'maximum') {\r\n      return FrictionMethod.COLEBROOK_WHITE; // Most accurate but iterative\r\n    }\r\n\r\n    if (accuracy === 'high') {\r\n      return FrictionMethod.HAALAND; // Good accuracy, explicit\r\n    }\r\n\r\n    // For standard accuracy, choose based on relative roughness\r\n    if (relativeRoughness < 0.001) {\r\n      return FrictionMethod.SWAMEE_JAIN; // Good for smooth pipes\r\n    } else {\r\n      return FrictionMethod.CHEN; // Good for rough pipes\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Validate input parameters\r\n   */\r\n  private static validateInputs(\r\n    input: FrictionCalculationInput,\r\n    validationLevel: string,\r\n    warnings: string[]\r\n  ): void {\r\n    if (validationLevel === 'none') return;\r\n\r\n    const { velocity, hydraulicDiameter, length } = input;\r\n\r\n    // Basic validation\r\n    if (velocity <= 0) throw new Error('Velocity must be positive');\r\n    if (hydraulicDiameter <= 0) throw new Error('Hydraulic diameter must be positive');\r\n    if (length <= 0) throw new Error('Length must be positive');\r\n\r\n    if (validationLevel === 'basic') return;\r\n\r\n    // Standard validation\r\n    if (velocity > 6000) {\r\n      warnings.push('High velocity may cause noise and energy efficiency issues');\r\n    }\r\n    if (velocity < 300) {\r\n      warnings.push('Low velocity may indicate oversized ductwork');\r\n    }\r\n\r\n    if (validationLevel === 'strict') {\r\n      // Strict validation\r\n      if (hydraulicDiameter < 4 || hydraulicDiameter > 120) {\r\n        warnings.push('Hydraulic diameter outside typical HVAC range (4-120 inches)');\r\n      }\r\n      if (length > 1000) {\r\n        warnings.push('Very long duct run - consider intermediate pressure calculations');\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Classify flow regime based on Reynolds number\r\n   */\r\n  private static classifyFlowRegime(reynoldsNumber: number): FlowRegime {\r\n    if (reynoldsNumber < this.REYNOLDS_TRANSITIONS.LAMINAR_MAX) {\r\n      return FlowRegime.LAMINAR;\r\n    } else if (reynoldsNumber < this.REYNOLDS_TRANSITIONS.TRANSITIONAL_MAX) {\r\n      return FlowRegime.TRANSITIONAL;\r\n    } else if (reynoldsNumber < 100000) {\r\n      return FlowRegime.TURBULENT_SMOOTH;\r\n    } else if (reynoldsNumber < 1000000) {\r\n      return FlowRegime.TURBULENT_ROUGH;\r\n    } else {\r\n      return FlowRegime.FULLY_ROUGH;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get material properties with aging and surface condition adjustments\r\n   */\r\n  private static getMaterialProperties(\r\n    material: string,\r\n    materialAge: MaterialAge,\r\n    surfaceCondition: SurfaceCondition,\r\n    customRoughness?: number,\r\n    warnings: string[] = []\r\n  ) {\r\n    let baseRoughness: number;\r\n\r\n    if (customRoughness !== undefined) {\r\n      baseRoughness = customRoughness;\r\n    } else {\r\n      // Get base roughness from material database\r\n      try {\r\n        const materialData = AirPropertiesCalculator.getEnhancedMaterialRoughness(\r\n          material,\r\n          materialAge,\r\n          surfaceCondition\r\n        );\r\n        baseRoughness = materialData.adjustedRoughness;\r\n        warnings.push(...materialData.warnings);\r\n      } catch (error) {\r\n        // Fallback to default values\r\n        baseRoughness = this.getDefaultRoughness(material);\r\n        warnings.push(`Using default roughness for material: ${material}`);\r\n      }\r\n    }\r\n\r\n    const agingFactor = this.AGING_FACTORS[materialAge];\r\n    const surfaceFactor = this.SURFACE_FACTORS[surfaceCondition];\r\n    const combinedFactor = agingFactor * surfaceFactor;\r\n    const adjustedRoughness = baseRoughness * combinedFactor;\r\n\r\n    return {\r\n      baseRoughness,\r\n      adjustedRoughness,\r\n      agingFactor,\r\n      surfaceFactor,\r\n      combinedFactor\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get default roughness values for common materials\r\n   */\r\n  private static getDefaultRoughness(material: string): number {\r\n    const defaultRoughness: Record<string, number> = {\r\n      'galvanized_steel': 0.0005,\r\n      'stainless_steel': 0.00015,\r\n      'aluminum': 0.00015,\r\n      'pvc': 0.000005,\r\n      'fiberglass': 0.0003,\r\n      'concrete': 0.003,\r\n      'cast_iron': 0.00085,\r\n      'flexible_duct': 0.003\r\n    };\r\n\r\n    return defaultRoughness[material] || 0.0005; // Default to galvanized steel\r\n  }\r\n\r\n  /**\r\n   * Calculate friction factor using specified method\r\n   */\r\n  private static calculateFrictionFactor(\r\n    method: FrictionMethod,\r\n    reynoldsNumber: number,\r\n    relativeRoughness: number,\r\n    flowRegime: FlowRegime,\r\n    warnings: string[]\r\n  ): number {\r\n    // For laminar flow, use analytical solution regardless of method\r\n    if (flowRegime === FlowRegime.LAMINAR) {\r\n      return 64 / reynoldsNumber;\r\n    }\r\n\r\n    switch (method) {\r\n      case FrictionMethod.COLEBROOK_WHITE:\r\n        return this.colebrookWhite(reynoldsNumber, relativeRoughness);\r\n        \r\n      case FrictionMethod.MOODY:\r\n        return this.moodyApproximation(reynoldsNumber, relativeRoughness);\r\n        \r\n      case FrictionMethod.SWAMEE_JAIN:\r\n        return this.swameeJain(reynoldsNumber, relativeRoughness);\r\n        \r\n      case FrictionMethod.HAALAND:\r\n        return this.haaland(reynoldsNumber, relativeRoughness);\r\n        \r\n      case FrictionMethod.CHEN:\r\n        return this.chen(reynoldsNumber, relativeRoughness);\r\n        \r\n      case FrictionMethod.ZIGRANG_SYLVESTER:\r\n        return this.zigrangSylvester(reynoldsNumber, relativeRoughness);\r\n        \r\n      case FrictionMethod.ENHANCED_DARCY:\r\n        return this.enhancedDarcy(reynoldsNumber, relativeRoughness, flowRegime);\r\n        \r\n      default:\r\n        warnings.push(`Unknown method ${method}, using Colebrook-White`);\r\n        return this.colebrookWhite(reynoldsNumber, relativeRoughness);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Colebrook-White equation (iterative solution)\r\n   */\r\n  private static colebrookWhite(reynoldsNumber: number, relativeRoughness: number): number {\r\n    let f = 0.02; // Initial guess\r\n    \r\n    for (let i = 0; i < 20; i++) {\r\n      const fNew = 1 / Math.pow(-2 * Math.log10(\r\n        relativeRoughness / 3.7 + 2.51 / (reynoldsNumber * Math.sqrt(f))\r\n      ), 2);\r\n      \r\n      if (Math.abs(fNew - f) < 0.0001) {\r\n        break;\r\n      }\r\n      f = fNew;\r\n    }\r\n    \r\n    return f;\r\n  }\r\n\r\n  /**\r\n   * Swamee-Jain approximation\r\n   */\r\n  private static swameeJain(reynoldsNumber: number, relativeRoughness: number): number {\r\n    const numerator = Math.pow(Math.log10(relativeRoughness / 3.7 + 5.74 / Math.pow(reynoldsNumber, 0.9)), 2);\r\n    return 0.25 / numerator;\r\n  }\r\n\r\n  /**\r\n   * Haaland approximation\r\n   */\r\n  private static haaland(reynoldsNumber: number, relativeRoughness: number): number {\r\n    const term1 = relativeRoughness / 3.7;\r\n    const term2 = 6.9 / reynoldsNumber;\r\n    return 1 / Math.pow(-1.8 * Math.log10(Math.pow(term1, 1.11) + term2), 2);\r\n  }\r\n\r\n  /**\r\n   * Chen approximation\r\n   */\r\n  private static chen(reynoldsNumber: number, relativeRoughness: number): number {\r\n    const A = Math.pow(Math.log10(relativeRoughness / 3.7065 - 5.0452 / reynoldsNumber * \r\n                      Math.log10(Math.pow(relativeRoughness, 1.1098) / 2.8257 + \r\n                      Math.pow(7.149 / reynoldsNumber, 0.8981))), 2);\r\n    return 1 / (4 * A);\r\n  }\r\n\r\n  /**\r\n   * Zigrang-Sylvester approximation\r\n   */\r\n  private static zigrangSylvester(reynoldsNumber: number, relativeRoughness: number): number {\r\n    const A = -2 * Math.log10(relativeRoughness / 3.7 + 5.02 / reynoldsNumber * \r\n              Math.log10(relativeRoughness / 3.7 + 5.02 / reynoldsNumber * \r\n              Math.log10(relativeRoughness / 3.7 + 13 / reynoldsNumber)));\r\n    return 1 / Math.pow(A, 2);\r\n  }\r\n\r\n  /**\r\n   * Moody diagram approximation\r\n   */\r\n  private static moodyApproximation(reynoldsNumber: number, relativeRoughness: number): number {\r\n    // Simplified Moody approximation\r\n    if (relativeRoughness < 0.0001) {\r\n      // Smooth pipe approximation\r\n      return 0.316 / Math.pow(reynoldsNumber, 0.25);\r\n    } else {\r\n      // Rough pipe approximation\r\n      return this.swameeJain(reynoldsNumber, relativeRoughness);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Enhanced Darcy method with flow regime optimization\r\n   */\r\n  private static enhancedDarcy(\r\n    reynoldsNumber: number, \r\n    relativeRoughness: number, \r\n    flowRegime: FlowRegime\r\n  ): number {\r\n    switch (flowRegime) {\r\n      case FlowRegime.LAMINAR:\r\n        return 64 / reynoldsNumber;\r\n        \r\n      case FlowRegime.TRANSITIONAL:\r\n        // Interpolate between laminar and turbulent\r\n        const fLaminar = 64 / reynoldsNumber;\r\n        const fTurbulent = this.colebrookWhite(4000, relativeRoughness);\r\n        const weight = (reynoldsNumber - 2300) / (4000 - 2300);\r\n        return fLaminar * (1 - weight) + fTurbulent * weight;\r\n        \r\n      case FlowRegime.TURBULENT_SMOOTH:\r\n        return this.haaland(reynoldsNumber, relativeRoughness);\r\n        \r\n      case FlowRegime.TURBULENT_ROUGH:\r\n      case FlowRegime.FULLY_ROUGH:\r\n        return this.colebrookWhite(reynoldsNumber, relativeRoughness);\r\n        \r\n      default:\r\n        return this.colebrookWhite(reynoldsNumber, relativeRoughness);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Calculate environmental corrections\r\n   */\r\n  private static calculateEnvironmentalCorrections(\r\n    airConditions?: AirConditions,\r\n    airDensity?: number\r\n  ) {\r\n    const corrections = {\r\n      temperature: 1.0,\r\n      pressure: 1.0,\r\n      humidity: 1.0,\r\n      combined: 1.0\r\n    };\r\n\r\n    if (airDensity) {\r\n      const densityRatio = airDensity / this.STANDARD_AIR.density;\r\n      corrections.temperature = densityRatio;\r\n      corrections.pressure = densityRatio;\r\n      corrections.humidity = densityRatio;\r\n      corrections.combined = densityRatio;\r\n    }\r\n\r\n    return corrections;\r\n  }\r\n\r\n  /**\r\n   * Calculate shape correction for non-round ducts\r\n   */\r\n  private static calculateShapeCorrection(\r\n    ductShape: string,\r\n    aspectRatio?: number\r\n  ): number {\r\n    if (ductShape === 'round') {\r\n      return 1.0;\r\n    }\r\n\r\n    if (ductShape === 'rectangular' && aspectRatio) {\r\n      // Correction for rectangular ducts based on aspect ratio\r\n      if (aspectRatio <= 1.5) return 1.0;\r\n      if (aspectRatio <= 2.0) return 1.05;\r\n      if (aspectRatio <= 3.0) return 1.10;\r\n      if (aspectRatio <= 4.0) return 1.15;\r\n      return 1.20; // High aspect ratio penalty\r\n    }\r\n\r\n    return 1.05; // Default correction for non-round shapes\r\n  }\r\n\r\n  /**\r\n   * Calculate additional correction factors\r\n   */\r\n  private static calculateAdditionalCorrections(\r\n    correctionFactors?: FrictionCalculationInput['correctionFactors']\r\n  ): number {\r\n    if (!correctionFactors) return 1.0;\r\n\r\n    const { installation = 1.0, maintenance = 1.0, environmental = 1.0 } = correctionFactors;\r\n    return installation * maintenance * environmental;\r\n  }\r\n\r\n  /**\r\n   * Calculate uncertainty bounds\r\n   */\r\n  private static calculateUncertaintyBounds(\r\n    frictionLoss: number,\r\n    method: FrictionMethod,\r\n    flowRegime: FlowRegime,\r\n    relativeRoughness: number\r\n  ) {\r\n    let baseAccuracy = this.METHOD_ACCURACY[method];\r\n\r\n    // Adjust accuracy based on flow regime\r\n    if (flowRegime === FlowRegime.TRANSITIONAL) {\r\n      baseAccuracy *= 0.9; // Reduced accuracy in transitional regime\r\n    }\r\n\r\n    // Adjust accuracy based on relative roughness\r\n    if (relativeRoughness > 0.05) {\r\n      baseAccuracy *= 0.95; // Reduced accuracy for very rough surfaces\r\n    }\r\n\r\n    const uncertainty = frictionLoss * (1 - baseAccuracy);\r\n\r\n    return {\r\n      lower: frictionLoss - uncertainty,\r\n      upper: frictionLoss + uncertainty,\r\n      confidenceLevel: baseAccuracy\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Generate recommendations\r\n   */\r\n  private static generateRecommendations(\r\n    velocity: number,\r\n    reynoldsNumber: number,\r\n    frictionRate: number,\r\n    materialProperties: any,\r\n    recommendations: string[]\r\n  ): void {\r\n    // Velocity recommendations\r\n    if (velocity > 4000) {\r\n      recommendations.push('High velocity detected - consider larger duct size to reduce friction and noise');\r\n    }\r\n\r\n    // Friction rate recommendations\r\n    if (frictionRate > 0.15) {\r\n      recommendations.push('High friction rate - consider smoother materials or larger ducts');\r\n    }\r\n\r\n    // Material recommendations\r\n    if (materialProperties.combinedFactor > 2.0) {\r\n      recommendations.push('Material aging/condition significantly affects friction - consider cleaning or replacement');\r\n    }\r\n\r\n    // Flow regime recommendations\r\n    if (reynoldsNumber < 4000) {\r\n      recommendations.push('Low Reynolds number - verify velocity calculations and consider system optimization');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get formula description for the method\r\n   */\r\n  private static getFormulaDescription(method: FrictionMethod): string {\r\n    const descriptions: Record<FrictionMethod, string> = {\r\n      [FrictionMethod.COLEBROOK_WHITE]: '1/\u221Af = -2log\u2081\u2080(\u03B5/3.7D + 2.51/(Re\u221Af))',\r\n      [FrictionMethod.MOODY]: 'Moody diagram approximation',\r\n      [FrictionMethod.SWAMEE_JAIN]: 'f = 0.25/[log\u2081\u2080(\u03B5/3.7D + 5.74/Re^0.9)]\xB2',\r\n      [FrictionMethod.HAALAND]: '1/\u221Af = -1.8log\u2081\u2080[(\u03B5/3.7D)^1.11 + 6.9/Re]',\r\n      [FrictionMethod.CHEN]: 'Chen explicit approximation',\r\n      [FrictionMethod.ZIGRANG_SYLVESTER]: 'Zigrang-Sylvester explicit approximation',\r\n      [FrictionMethod.ENHANCED_DARCY]: 'Flow regime optimized Darcy-Weisbach'\r\n    };\r\n\r\n    return descriptions[method] || 'Unknown method';\r\n  }\r\n\r\n  /**\r\n   * Get standard reference for the method\r\n   */\r\n  private static getStandardReference(method: FrictionMethod): string {\r\n    return 'ASHRAE Fundamentals, Chapter 21 - Duct Design';\r\n  }\r\n}\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "6c4a005658abbd3c25503516a88b98f54e743e96"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1tnf2sn61 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1tnf2sn61();
cov_1tnf2sn61().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1tnf2sn61().s[1]++;
exports.EnhancedFrictionCalculator = exports.SurfaceCondition = exports.MaterialAge = exports.FlowRegime = exports.FrictionMethod = void 0;
const AirPropertiesCalculator_1 =
/* istanbul ignore next */
(cov_1tnf2sn61().s[2]++, require("./AirPropertiesCalculator"));
/**
 * Friction calculation method options
 */
var FrictionMethod;
/* istanbul ignore next */
cov_1tnf2sn61().s[3]++;
(function (FrictionMethod) {
  /* istanbul ignore next */
  cov_1tnf2sn61().f[0]++;
  cov_1tnf2sn61().s[4]++;
  FrictionMethod["COLEBROOK_WHITE"] = "colebrook_white";
  /* istanbul ignore next */
  cov_1tnf2sn61().s[5]++;
  FrictionMethod["MOODY"] = "moody";
  /* istanbul ignore next */
  cov_1tnf2sn61().s[6]++;
  FrictionMethod["SWAMEE_JAIN"] = "swamee_jain";
  /* istanbul ignore next */
  cov_1tnf2sn61().s[7]++;
  FrictionMethod["HAALAND"] = "haaland";
  /* istanbul ignore next */
  cov_1tnf2sn61().s[8]++;
  FrictionMethod["CHEN"] = "chen";
  /* istanbul ignore next */
  cov_1tnf2sn61().s[9]++;
  FrictionMethod["ZIGRANG_SYLVESTER"] = "zigrang_sylvester";
  /* istanbul ignore next */
  cov_1tnf2sn61().s[10]++;
  FrictionMethod["ENHANCED_DARCY"] = "enhanced_darcy";
})(
/* istanbul ignore next */
(cov_1tnf2sn61().b[0][0]++, FrictionMethod) ||
/* istanbul ignore next */
(cov_1tnf2sn61().b[0][1]++, exports.FrictionMethod = FrictionMethod = {}));
/**
 * Flow regime classification
 */
var FlowRegime;
/* istanbul ignore next */
cov_1tnf2sn61().s[11]++;
(function (FlowRegime) {
  /* istanbul ignore next */
  cov_1tnf2sn61().f[1]++;
  cov_1tnf2sn61().s[12]++;
  FlowRegime["LAMINAR"] = "laminar";
  /* istanbul ignore next */
  cov_1tnf2sn61().s[13]++;
  FlowRegime["TRANSITIONAL"] = "transitional";
  /* istanbul ignore next */
  cov_1tnf2sn61().s[14]++;
  FlowRegime["TURBULENT_SMOOTH"] = "turbulent_smooth";
  /* istanbul ignore next */
  cov_1tnf2sn61().s[15]++;
  FlowRegime["TURBULENT_ROUGH"] = "turbulent_rough";
  /* istanbul ignore next */
  cov_1tnf2sn61().s[16]++;
  FlowRegime["FULLY_ROUGH"] = "fully_rough";
})(
/* istanbul ignore next */
(cov_1tnf2sn61().b[1][0]++, FlowRegime) ||
/* istanbul ignore next */
(cov_1tnf2sn61().b[1][1]++, exports.FlowRegime = FlowRegime = {}));
/**
 * Material aging condition
 */
var MaterialAge;
/* istanbul ignore next */
cov_1tnf2sn61().s[17]++;
(function (MaterialAge) {
  /* istanbul ignore next */
  cov_1tnf2sn61().f[2]++;
  cov_1tnf2sn61().s[18]++;
  MaterialAge["NEW"] = "new";
  /* istanbul ignore next */
  cov_1tnf2sn61().s[19]++;
  MaterialAge["GOOD"] = "good";
  /* istanbul ignore next */
  cov_1tnf2sn61().s[20]++;
  MaterialAge["AVERAGE"] = "average";
  /* istanbul ignore next */
  cov_1tnf2sn61().s[21]++;
  MaterialAge["POOR"] = "poor";
  /* istanbul ignore next */
  cov_1tnf2sn61().s[22]++;
  MaterialAge["VERY_POOR"] = "very_poor";
})(
/* istanbul ignore next */
(cov_1tnf2sn61().b[2][0]++, MaterialAge) ||
/* istanbul ignore next */
(cov_1tnf2sn61().b[2][1]++, exports.MaterialAge = MaterialAge = {}));
/**
 * Surface condition classification
 */
var SurfaceCondition;
/* istanbul ignore next */
cov_1tnf2sn61().s[23]++;
(function (SurfaceCondition) {
  /* istanbul ignore next */
  cov_1tnf2sn61().f[3]++;
  cov_1tnf2sn61().s[24]++;
  SurfaceCondition["EXCELLENT"] = "excellent";
  /* istanbul ignore next */
  cov_1tnf2sn61().s[25]++;
  SurfaceCondition["GOOD"] = "good";
  /* istanbul ignore next */
  cov_1tnf2sn61().s[26]++;
  SurfaceCondition["AVERAGE"] = "average";
  /* istanbul ignore next */
  cov_1tnf2sn61().s[27]++;
  SurfaceCondition["POOR"] = "poor";
  /* istanbul ignore next */
  cov_1tnf2sn61().s[28]++;
  SurfaceCondition["VERY_POOR"] = "very_poor";
})(
/* istanbul ignore next */
(cov_1tnf2sn61().b[3][0]++, SurfaceCondition) ||
/* istanbul ignore next */
(cov_1tnf2sn61().b[3][1]++, exports.SurfaceCondition = SurfaceCondition = {}));
/**
 * Enhanced Friction Calculator
 *
 * Comprehensive friction calculation service providing multiple calculation methods,
 * material aging effects, environmental corrections, and advanced features.
 */
class EnhancedFrictionCalculator {
  /**
   * Calculate enhanced friction loss with comprehensive corrections
   */
  static calculateFrictionLoss(input) {
    /* istanbul ignore next */
    cov_1tnf2sn61().f[4]++;
    const {
      velocity,
      hydraulicDiameter,
      length,
      material,
      method =
      /* istanbul ignore next */
      (cov_1tnf2sn61().b[4][0]++, FrictionMethod.ENHANCED_DARCY),
      airConditions,
      materialAge =
      /* istanbul ignore next */
      (cov_1tnf2sn61().b[5][0]++, MaterialAge.GOOD),
      surfaceCondition =
      /* istanbul ignore next */
      (cov_1tnf2sn61().b[6][0]++, SurfaceCondition.GOOD),
      customRoughness,
      ductShape =
      /* istanbul ignore next */
      (cov_1tnf2sn61().b[7][0]++, 'round'),
      aspectRatio,
      correctionFactors,
      validationLevel =
      /* istanbul ignore next */
      (cov_1tnf2sn61().b[8][0]++, 'standard')
    } =
    /* istanbul ignore next */
    (cov_1tnf2sn61().s[29]++, input);
    const warnings =
    /* istanbul ignore next */
    (cov_1tnf2sn61().s[30]++, []);
    const recommendations =
    /* istanbul ignore next */
    (cov_1tnf2sn61().s[31]++, []);
    // Validate inputs
    /* istanbul ignore next */
    cov_1tnf2sn61().s[32]++;
    this.validateInputs(input, validationLevel, warnings);
    // Get air properties
    const airProps =
    /* istanbul ignore next */
    (cov_1tnf2sn61().s[33]++, airConditions ?
    /* istanbul ignore next */
    (cov_1tnf2sn61().b[9][0]++, AirPropertiesCalculator_1.AirPropertiesCalculator.calculateAirProperties(airConditions)) :
    /* istanbul ignore next */
    (cov_1tnf2sn61().b[9][1]++, {
      density: this.STANDARD_AIR.density,
      viscosity: this.STANDARD_AIR.viscosity,
      warnings: []
    }));
    /* istanbul ignore next */
    cov_1tnf2sn61().s[34]++;
    warnings.push(...airProps.warnings);
    // Calculate Reynolds number
    const velocityFps =
    /* istanbul ignore next */
    (cov_1tnf2sn61().s[35]++, velocity / 60); // Convert FPM to fps
    const diameterFt =
    /* istanbul ignore next */
    (cov_1tnf2sn61().s[36]++, hydraulicDiameter / 12); // Convert inches to feet
    const reynoldsNumber =
    /* istanbul ignore next */
    (cov_1tnf2sn61().s[37]++, airProps.density * velocityFps * diameterFt / airProps.viscosity);
    // Determine flow regime
    const flowRegime =
    /* istanbul ignore next */
    (cov_1tnf2sn61().s[38]++, this.classifyFlowRegime(reynoldsNumber));
    // Get material properties
    const materialProperties =
    /* istanbul ignore next */
    (cov_1tnf2sn61().s[39]++, this.getMaterialProperties(material, materialAge, surfaceCondition, customRoughness, warnings));
    // Calculate relative roughness
    const relativeRoughness =
    /* istanbul ignore next */
    (cov_1tnf2sn61().s[40]++, materialProperties.adjustedRoughness / diameterFt);
    // Calculate friction factor using specified method
    const frictionFactor =
    /* istanbul ignore next */
    (cov_1tnf2sn61().s[41]++, this.calculateFrictionFactor(method, reynoldsNumber, relativeRoughness, flowRegime, warnings));
    // Calculate environmental corrections
    const environmentalCorrections =
    /* istanbul ignore next */
    (cov_1tnf2sn61().s[42]++, this.calculateEnvironmentalCorrections(airConditions, airProps.density));
    // Apply shape corrections for non-round ducts
    const shapeCorrection =
    /* istanbul ignore next */
    (cov_1tnf2sn61().s[43]++, this.calculateShapeCorrection(ductShape, aspectRatio));
    // Apply additional correction factors
    const additionalCorrections =
    /* istanbul ignore next */
    (cov_1tnf2sn61().s[44]++, this.calculateAdditionalCorrections(correctionFactors));
    // Calculate friction loss using Darcy-Weisbach equation
    const baseFrictionLoss =
    /* istanbul ignore next */
    (cov_1tnf2sn61().s[45]++, frictionFactor * (length / diameterFt) * (airProps.density * Math.pow(velocityFps, 2)) / (2 * 32.174));
    // Apply all corrections
    const totalCorrection =
    /* istanbul ignore next */
    (cov_1tnf2sn61().s[46]++, environmentalCorrections.combined * shapeCorrection * additionalCorrections);
    const correctedFrictionLoss =
    /* istanbul ignore next */
    (cov_1tnf2sn61().s[47]++, baseFrictionLoss / 5.2 * totalCorrection); // Convert to inches w.g.
    // Calculate friction rate
    const frictionRate =
    /* istanbul ignore next */
    (cov_1tnf2sn61().s[48]++, correctedFrictionLoss / length * 100); // inches w.g. per 100 feet
    // Calculate uncertainty bounds
    const uncertaintyBounds =
    /* istanbul ignore next */
    (cov_1tnf2sn61().s[49]++, this.calculateUncertaintyBounds(correctedFrictionLoss, method, flowRegime, relativeRoughness));
    // Generate recommendations
    /* istanbul ignore next */
    cov_1tnf2sn61().s[50]++;
    this.generateRecommendations(velocity, reynoldsNumber, frictionRate, materialProperties, recommendations);
    /* istanbul ignore next */
    cov_1tnf2sn61().s[51]++;
    return {
      frictionLoss: correctedFrictionLoss,
      frictionFactor,
      frictionRate,
      method,
      flowRegime,
      reynoldsNumber,
      relativeRoughness,
      materialProperties,
      environmentalCorrections,
      accuracy: this.METHOD_ACCURACY[method],
      uncertaintyBounds,
      warnings,
      recommendations,
      calculationDetails: {
        formula: this.getFormulaDescription(method),
        intermediateValues: {
          baseFrictionLoss,
          totalCorrection,
          velocityFps,
          diameterFt
        },
        standardReference: this.getStandardReference(method)
      }
    };
  }
  /**
   * Get optimal friction calculation method for given conditions
   */
  static getOptimalMethod(reynoldsNumber, relativeRoughness, accuracy =
  /* istanbul ignore next */
  (cov_1tnf2sn61().b[10][0]++, 'standard')) {
    /* istanbul ignore next */
    cov_1tnf2sn61().f[5]++;
    cov_1tnf2sn61().s[52]++;
    // For laminar flow
    if (reynoldsNumber < this.REYNOLDS_TRANSITIONS.LAMINAR_MAX) {
      /* istanbul ignore next */
      cov_1tnf2sn61().b[11][0]++;
      cov_1tnf2sn61().s[53]++;
      return FrictionMethod.ENHANCED_DARCY; // Uses analytical solution for laminar flow
    } else
    /* istanbul ignore next */
    {
      cov_1tnf2sn61().b[11][1]++;
    }
    // For turbulent flow, choose based on accuracy requirements
    cov_1tnf2sn61().s[54]++;
    if (accuracy === 'maximum') {
      /* istanbul ignore next */
      cov_1tnf2sn61().b[12][0]++;
      cov_1tnf2sn61().s[55]++;
      return FrictionMethod.COLEBROOK_WHITE; // Most accurate but iterative
    } else
    /* istanbul ignore next */
    {
      cov_1tnf2sn61().b[12][1]++;
    }
    cov_1tnf2sn61().s[56]++;
    if (accuracy === 'high') {
      /* istanbul ignore next */
      cov_1tnf2sn61().b[13][0]++;
      cov_1tnf2sn61().s[57]++;
      return FrictionMethod.HAALAND; // Good accuracy, explicit
    } else
    /* istanbul ignore next */
    {
      cov_1tnf2sn61().b[13][1]++;
    }
    // For standard accuracy, choose based on relative roughness
    cov_1tnf2sn61().s[58]++;
    if (relativeRoughness < 0.001) {
      /* istanbul ignore next */
      cov_1tnf2sn61().b[14][0]++;
      cov_1tnf2sn61().s[59]++;
      return FrictionMethod.SWAMEE_JAIN; // Good for smooth pipes
    } else {
      /* istanbul ignore next */
      cov_1tnf2sn61().b[14][1]++;
      cov_1tnf2sn61().s[60]++;
      return FrictionMethod.CHEN; // Good for rough pipes
    }
  }
  /**
   * Validate input parameters
   */
  static validateInputs(input, validationLevel, warnings) {
    /* istanbul ignore next */
    cov_1tnf2sn61().f[6]++;
    cov_1tnf2sn61().s[61]++;
    if (validationLevel === 'none') {
      /* istanbul ignore next */
      cov_1tnf2sn61().b[15][0]++;
      cov_1tnf2sn61().s[62]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_1tnf2sn61().b[15][1]++;
    }
    const {
      velocity,
      hydraulicDiameter,
      length
    } =
    /* istanbul ignore next */
    (cov_1tnf2sn61().s[63]++, input);
    // Basic validation
    /* istanbul ignore next */
    cov_1tnf2sn61().s[64]++;
    if (velocity <= 0) {
      /* istanbul ignore next */
      cov_1tnf2sn61().b[16][0]++;
      cov_1tnf2sn61().s[65]++;
      throw new Error('Velocity must be positive');
    } else
    /* istanbul ignore next */
    {
      cov_1tnf2sn61().b[16][1]++;
    }
    cov_1tnf2sn61().s[66]++;
    if (hydraulicDiameter <= 0) {
      /* istanbul ignore next */
      cov_1tnf2sn61().b[17][0]++;
      cov_1tnf2sn61().s[67]++;
      throw new Error('Hydraulic diameter must be positive');
    } else
    /* istanbul ignore next */
    {
      cov_1tnf2sn61().b[17][1]++;
    }
    cov_1tnf2sn61().s[68]++;
    if (length <= 0) {
      /* istanbul ignore next */
      cov_1tnf2sn61().b[18][0]++;
      cov_1tnf2sn61().s[69]++;
      throw new Error('Length must be positive');
    } else
    /* istanbul ignore next */
    {
      cov_1tnf2sn61().b[18][1]++;
    }
    cov_1tnf2sn61().s[70]++;
    if (validationLevel === 'basic') {
      /* istanbul ignore next */
      cov_1tnf2sn61().b[19][0]++;
      cov_1tnf2sn61().s[71]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_1tnf2sn61().b[19][1]++;
    }
    // Standard validation
    cov_1tnf2sn61().s[72]++;
    if (velocity > 6000) {
      /* istanbul ignore next */
      cov_1tnf2sn61().b[20][0]++;
      cov_1tnf2sn61().s[73]++;
      warnings.push('High velocity may cause noise and energy efficiency issues');
    } else
    /* istanbul ignore next */
    {
      cov_1tnf2sn61().b[20][1]++;
    }
    cov_1tnf2sn61().s[74]++;
    if (velocity < 300) {
      /* istanbul ignore next */
      cov_1tnf2sn61().b[21][0]++;
      cov_1tnf2sn61().s[75]++;
      warnings.push('Low velocity may indicate oversized ductwork');
    } else
    /* istanbul ignore next */
    {
      cov_1tnf2sn61().b[21][1]++;
    }
    cov_1tnf2sn61().s[76]++;
    if (validationLevel === 'strict') {
      /* istanbul ignore next */
      cov_1tnf2sn61().b[22][0]++;
      cov_1tnf2sn61().s[77]++;
      // Strict validation
      if (
      /* istanbul ignore next */
      (cov_1tnf2sn61().b[24][0]++, hydraulicDiameter < 4) ||
      /* istanbul ignore next */
      (cov_1tnf2sn61().b[24][1]++, hydraulicDiameter > 120)) {
        /* istanbul ignore next */
        cov_1tnf2sn61().b[23][0]++;
        cov_1tnf2sn61().s[78]++;
        warnings.push('Hydraulic diameter outside typical HVAC range (4-120 inches)');
      } else
      /* istanbul ignore next */
      {
        cov_1tnf2sn61().b[23][1]++;
      }
      cov_1tnf2sn61().s[79]++;
      if (length > 1000) {
        /* istanbul ignore next */
        cov_1tnf2sn61().b[25][0]++;
        cov_1tnf2sn61().s[80]++;
        warnings.push('Very long duct run - consider intermediate pressure calculations');
      } else
      /* istanbul ignore next */
      {
        cov_1tnf2sn61().b[25][1]++;
      }
    } else
    /* istanbul ignore next */
    {
      cov_1tnf2sn61().b[22][1]++;
    }
  }
  /**
   * Classify flow regime based on Reynolds number
   */
  static classifyFlowRegime(reynoldsNumber) {
    /* istanbul ignore next */
    cov_1tnf2sn61().f[7]++;
    cov_1tnf2sn61().s[81]++;
    if (reynoldsNumber < this.REYNOLDS_TRANSITIONS.LAMINAR_MAX) {
      /* istanbul ignore next */
      cov_1tnf2sn61().b[26][0]++;
      cov_1tnf2sn61().s[82]++;
      return FlowRegime.LAMINAR;
    } else {
      /* istanbul ignore next */
      cov_1tnf2sn61().b[26][1]++;
      cov_1tnf2sn61().s[83]++;
      if (reynoldsNumber < this.REYNOLDS_TRANSITIONS.TRANSITIONAL_MAX) {
        /* istanbul ignore next */
        cov_1tnf2sn61().b[27][0]++;
        cov_1tnf2sn61().s[84]++;
        return FlowRegime.TRANSITIONAL;
      } else {
        /* istanbul ignore next */
        cov_1tnf2sn61().b[27][1]++;
        cov_1tnf2sn61().s[85]++;
        if (reynoldsNumber < 100000) {
          /* istanbul ignore next */
          cov_1tnf2sn61().b[28][0]++;
          cov_1tnf2sn61().s[86]++;
          return FlowRegime.TURBULENT_SMOOTH;
        } else {
          /* istanbul ignore next */
          cov_1tnf2sn61().b[28][1]++;
          cov_1tnf2sn61().s[87]++;
          if (reynoldsNumber < 1000000) {
            /* istanbul ignore next */
            cov_1tnf2sn61().b[29][0]++;
            cov_1tnf2sn61().s[88]++;
            return FlowRegime.TURBULENT_ROUGH;
          } else {
            /* istanbul ignore next */
            cov_1tnf2sn61().b[29][1]++;
            cov_1tnf2sn61().s[89]++;
            return FlowRegime.FULLY_ROUGH;
          }
        }
      }
    }
  }
  /**
   * Get material properties with aging and surface condition adjustments
   */
  static getMaterialProperties(material, materialAge, surfaceCondition, customRoughness, warnings =
  /* istanbul ignore next */
  (cov_1tnf2sn61().b[30][0]++, [])) {
    /* istanbul ignore next */
    cov_1tnf2sn61().f[8]++;
    let baseRoughness;
    /* istanbul ignore next */
    cov_1tnf2sn61().s[90]++;
    if (customRoughness !== undefined) {
      /* istanbul ignore next */
      cov_1tnf2sn61().b[31][0]++;
      cov_1tnf2sn61().s[91]++;
      baseRoughness = customRoughness;
    } else {
      /* istanbul ignore next */
      cov_1tnf2sn61().b[31][1]++;
      cov_1tnf2sn61().s[92]++;
      // Get base roughness from material database
      try {
        const materialData =
        /* istanbul ignore next */
        (cov_1tnf2sn61().s[93]++, AirPropertiesCalculator_1.AirPropertiesCalculator.getEnhancedMaterialRoughness(material, materialAge, surfaceCondition));
        /* istanbul ignore next */
        cov_1tnf2sn61().s[94]++;
        baseRoughness = materialData.adjustedRoughness;
        /* istanbul ignore next */
        cov_1tnf2sn61().s[95]++;
        warnings.push(...materialData.warnings);
      } catch (error) {
        /* istanbul ignore next */
        cov_1tnf2sn61().s[96]++;
        // Fallback to default values
        baseRoughness = this.getDefaultRoughness(material);
        /* istanbul ignore next */
        cov_1tnf2sn61().s[97]++;
        warnings.push(`Using default roughness for material: ${material}`);
      }
    }
    const agingFactor =
    /* istanbul ignore next */
    (cov_1tnf2sn61().s[98]++, this.AGING_FACTORS[materialAge]);
    const surfaceFactor =
    /* istanbul ignore next */
    (cov_1tnf2sn61().s[99]++, this.SURFACE_FACTORS[surfaceCondition]);
    const combinedFactor =
    /* istanbul ignore next */
    (cov_1tnf2sn61().s[100]++, agingFactor * surfaceFactor);
    const adjustedRoughness =
    /* istanbul ignore next */
    (cov_1tnf2sn61().s[101]++, baseRoughness * combinedFactor);
    /* istanbul ignore next */
    cov_1tnf2sn61().s[102]++;
    return {
      baseRoughness,
      adjustedRoughness,
      agingFactor,
      surfaceFactor,
      combinedFactor
    };
  }
  /**
   * Get default roughness values for common materials
   */
  static getDefaultRoughness(material) {
    /* istanbul ignore next */
    cov_1tnf2sn61().f[9]++;
    const defaultRoughness =
    /* istanbul ignore next */
    (cov_1tnf2sn61().s[103]++, {
      'galvanized_steel': 0.0005,
      'stainless_steel': 0.00015,
      'aluminum': 0.00015,
      'pvc': 0.000005,
      'fiberglass': 0.0003,
      'concrete': 0.003,
      'cast_iron': 0.00085,
      'flexible_duct': 0.003
    });
    /* istanbul ignore next */
    cov_1tnf2sn61().s[104]++;
    return /* istanbul ignore next */(cov_1tnf2sn61().b[32][0]++, defaultRoughness[material]) ||
    /* istanbul ignore next */
    (cov_1tnf2sn61().b[32][1]++, 0.0005); // Default to galvanized steel
  }
  /**
   * Calculate friction factor using specified method
   */
  static calculateFrictionFactor(method, reynoldsNumber, relativeRoughness, flowRegime, warnings) {
    /* istanbul ignore next */
    cov_1tnf2sn61().f[10]++;
    cov_1tnf2sn61().s[105]++;
    // For laminar flow, use analytical solution regardless of method
    if (flowRegime === FlowRegime.LAMINAR) {
      /* istanbul ignore next */
      cov_1tnf2sn61().b[33][0]++;
      cov_1tnf2sn61().s[106]++;
      return 64 / reynoldsNumber;
    } else
    /* istanbul ignore next */
    {
      cov_1tnf2sn61().b[33][1]++;
    }
    cov_1tnf2sn61().s[107]++;
    switch (method) {
      case FrictionMethod.COLEBROOK_WHITE:
        /* istanbul ignore next */
        cov_1tnf2sn61().b[34][0]++;
        cov_1tnf2sn61().s[108]++;
        return this.colebrookWhite(reynoldsNumber, relativeRoughness);
      case FrictionMethod.MOODY:
        /* istanbul ignore next */
        cov_1tnf2sn61().b[34][1]++;
        cov_1tnf2sn61().s[109]++;
        return this.moodyApproximation(reynoldsNumber, relativeRoughness);
      case FrictionMethod.SWAMEE_JAIN:
        /* istanbul ignore next */
        cov_1tnf2sn61().b[34][2]++;
        cov_1tnf2sn61().s[110]++;
        return this.swameeJain(reynoldsNumber, relativeRoughness);
      case FrictionMethod.HAALAND:
        /* istanbul ignore next */
        cov_1tnf2sn61().b[34][3]++;
        cov_1tnf2sn61().s[111]++;
        return this.haaland(reynoldsNumber, relativeRoughness);
      case FrictionMethod.CHEN:
        /* istanbul ignore next */
        cov_1tnf2sn61().b[34][4]++;
        cov_1tnf2sn61().s[112]++;
        return this.chen(reynoldsNumber, relativeRoughness);
      case FrictionMethod.ZIGRANG_SYLVESTER:
        /* istanbul ignore next */
        cov_1tnf2sn61().b[34][5]++;
        cov_1tnf2sn61().s[113]++;
        return this.zigrangSylvester(reynoldsNumber, relativeRoughness);
      case FrictionMethod.ENHANCED_DARCY:
        /* istanbul ignore next */
        cov_1tnf2sn61().b[34][6]++;
        cov_1tnf2sn61().s[114]++;
        return this.enhancedDarcy(reynoldsNumber, relativeRoughness, flowRegime);
      default:
        /* istanbul ignore next */
        cov_1tnf2sn61().b[34][7]++;
        cov_1tnf2sn61().s[115]++;
        warnings.push(`Unknown method ${method}, using Colebrook-White`);
        /* istanbul ignore next */
        cov_1tnf2sn61().s[116]++;
        return this.colebrookWhite(reynoldsNumber, relativeRoughness);
    }
  }
  /**
   * Colebrook-White equation (iterative solution)
   */
  static colebrookWhite(reynoldsNumber, relativeRoughness) {
    /* istanbul ignore next */
    cov_1tnf2sn61().f[11]++;
    let f =
    /* istanbul ignore next */
    (cov_1tnf2sn61().s[117]++, 0.02); // Initial guess
    /* istanbul ignore next */
    cov_1tnf2sn61().s[118]++;
    for (let i =
    /* istanbul ignore next */
    (cov_1tnf2sn61().s[119]++, 0); i < 20; i++) {
      const fNew =
      /* istanbul ignore next */
      (cov_1tnf2sn61().s[120]++, 1 / Math.pow(-2 * Math.log10(relativeRoughness / 3.7 + 2.51 / (reynoldsNumber * Math.sqrt(f))), 2));
      /* istanbul ignore next */
      cov_1tnf2sn61().s[121]++;
      if (Math.abs(fNew - f) < 0.0001) {
        /* istanbul ignore next */
        cov_1tnf2sn61().b[35][0]++;
        cov_1tnf2sn61().s[122]++;
        break;
      } else
      /* istanbul ignore next */
      {
        cov_1tnf2sn61().b[35][1]++;
      }
      cov_1tnf2sn61().s[123]++;
      f = fNew;
    }
    /* istanbul ignore next */
    cov_1tnf2sn61().s[124]++;
    return f;
  }
  /**
   * Swamee-Jain approximation
   */
  static swameeJain(reynoldsNumber, relativeRoughness) {
    /* istanbul ignore next */
    cov_1tnf2sn61().f[12]++;
    const numerator =
    /* istanbul ignore next */
    (cov_1tnf2sn61().s[125]++, Math.pow(Math.log10(relativeRoughness / 3.7 + 5.74 / Math.pow(reynoldsNumber, 0.9)), 2));
    /* istanbul ignore next */
    cov_1tnf2sn61().s[126]++;
    return 0.25 / numerator;
  }
  /**
   * Haaland approximation
   */
  static haaland(reynoldsNumber, relativeRoughness) {
    /* istanbul ignore next */
    cov_1tnf2sn61().f[13]++;
    const term1 =
    /* istanbul ignore next */
    (cov_1tnf2sn61().s[127]++, relativeRoughness / 3.7);
    const term2 =
    /* istanbul ignore next */
    (cov_1tnf2sn61().s[128]++, 6.9 / reynoldsNumber);
    /* istanbul ignore next */
    cov_1tnf2sn61().s[129]++;
    return 1 / Math.pow(-1.8 * Math.log10(Math.pow(term1, 1.11) + term2), 2);
  }
  /**
   * Chen approximation
   */
  static chen(reynoldsNumber, relativeRoughness) {
    /* istanbul ignore next */
    cov_1tnf2sn61().f[14]++;
    const A =
    /* istanbul ignore next */
    (cov_1tnf2sn61().s[130]++, Math.pow(Math.log10(relativeRoughness / 3.7065 - 5.0452 / reynoldsNumber * Math.log10(Math.pow(relativeRoughness, 1.1098) / 2.8257 + Math.pow(7.149 / reynoldsNumber, 0.8981))), 2));
    /* istanbul ignore next */
    cov_1tnf2sn61().s[131]++;
    return 1 / (4 * A);
  }
  /**
   * Zigrang-Sylvester approximation
   */
  static zigrangSylvester(reynoldsNumber, relativeRoughness) {
    /* istanbul ignore next */
    cov_1tnf2sn61().f[15]++;
    const A =
    /* istanbul ignore next */
    (cov_1tnf2sn61().s[132]++, -2 * Math.log10(relativeRoughness / 3.7 + 5.02 / reynoldsNumber * Math.log10(relativeRoughness / 3.7 + 5.02 / reynoldsNumber * Math.log10(relativeRoughness / 3.7 + 13 / reynoldsNumber))));
    /* istanbul ignore next */
    cov_1tnf2sn61().s[133]++;
    return 1 / Math.pow(A, 2);
  }
  /**
   * Moody diagram approximation
   */
  static moodyApproximation(reynoldsNumber, relativeRoughness) {
    /* istanbul ignore next */
    cov_1tnf2sn61().f[16]++;
    cov_1tnf2sn61().s[134]++;
    // Simplified Moody approximation
    if (relativeRoughness < 0.0001) {
      /* istanbul ignore next */
      cov_1tnf2sn61().b[36][0]++;
      cov_1tnf2sn61().s[135]++;
      // Smooth pipe approximation
      return 0.316 / Math.pow(reynoldsNumber, 0.25);
    } else {
      /* istanbul ignore next */
      cov_1tnf2sn61().b[36][1]++;
      cov_1tnf2sn61().s[136]++;
      // Rough pipe approximation
      return this.swameeJain(reynoldsNumber, relativeRoughness);
    }
  }
  /**
   * Enhanced Darcy method with flow regime optimization
   */
  static enhancedDarcy(reynoldsNumber, relativeRoughness, flowRegime) {
    /* istanbul ignore next */
    cov_1tnf2sn61().f[17]++;
    cov_1tnf2sn61().s[137]++;
    switch (flowRegime) {
      case FlowRegime.LAMINAR:
        /* istanbul ignore next */
        cov_1tnf2sn61().b[37][0]++;
        cov_1tnf2sn61().s[138]++;
        return 64 / reynoldsNumber;
      case FlowRegime.TRANSITIONAL:
        /* istanbul ignore next */
        cov_1tnf2sn61().b[37][1]++;
        // Interpolate between laminar and turbulent
        const fLaminar =
        /* istanbul ignore next */
        (cov_1tnf2sn61().s[139]++, 64 / reynoldsNumber);
        const fTurbulent =
        /* istanbul ignore next */
        (cov_1tnf2sn61().s[140]++, this.colebrookWhite(4000, relativeRoughness));
        const weight =
        /* istanbul ignore next */
        (cov_1tnf2sn61().s[141]++, (reynoldsNumber - 2300) / (4000 - 2300));
        /* istanbul ignore next */
        cov_1tnf2sn61().s[142]++;
        return fLaminar * (1 - weight) + fTurbulent * weight;
      case FlowRegime.TURBULENT_SMOOTH:
        /* istanbul ignore next */
        cov_1tnf2sn61().b[37][2]++;
        cov_1tnf2sn61().s[143]++;
        return this.haaland(reynoldsNumber, relativeRoughness);
      case FlowRegime.TURBULENT_ROUGH:
        /* istanbul ignore next */
        cov_1tnf2sn61().b[37][3]++;
      case FlowRegime.FULLY_ROUGH:
        /* istanbul ignore next */
        cov_1tnf2sn61().b[37][4]++;
        cov_1tnf2sn61().s[144]++;
        return this.colebrookWhite(reynoldsNumber, relativeRoughness);
      default:
        /* istanbul ignore next */
        cov_1tnf2sn61().b[37][5]++;
        cov_1tnf2sn61().s[145]++;
        return this.colebrookWhite(reynoldsNumber, relativeRoughness);
    }
  }
  /**
   * Calculate environmental corrections
   */
  static calculateEnvironmentalCorrections(airConditions, airDensity) {
    /* istanbul ignore next */
    cov_1tnf2sn61().f[18]++;
    const corrections =
    /* istanbul ignore next */
    (cov_1tnf2sn61().s[146]++, {
      temperature: 1.0,
      pressure: 1.0,
      humidity: 1.0,
      combined: 1.0
    });
    /* istanbul ignore next */
    cov_1tnf2sn61().s[147]++;
    if (airDensity) {
      /* istanbul ignore next */
      cov_1tnf2sn61().b[38][0]++;
      const densityRatio =
      /* istanbul ignore next */
      (cov_1tnf2sn61().s[148]++, airDensity / this.STANDARD_AIR.density);
      /* istanbul ignore next */
      cov_1tnf2sn61().s[149]++;
      corrections.temperature = densityRatio;
      /* istanbul ignore next */
      cov_1tnf2sn61().s[150]++;
      corrections.pressure = densityRatio;
      /* istanbul ignore next */
      cov_1tnf2sn61().s[151]++;
      corrections.humidity = densityRatio;
      /* istanbul ignore next */
      cov_1tnf2sn61().s[152]++;
      corrections.combined = densityRatio;
    } else
    /* istanbul ignore next */
    {
      cov_1tnf2sn61().b[38][1]++;
    }
    cov_1tnf2sn61().s[153]++;
    return corrections;
  }
  /**
   * Calculate shape correction for non-round ducts
   */
  static calculateShapeCorrection(ductShape, aspectRatio) {
    /* istanbul ignore next */
    cov_1tnf2sn61().f[19]++;
    cov_1tnf2sn61().s[154]++;
    if (ductShape === 'round') {
      /* istanbul ignore next */
      cov_1tnf2sn61().b[39][0]++;
      cov_1tnf2sn61().s[155]++;
      return 1.0;
    } else
    /* istanbul ignore next */
    {
      cov_1tnf2sn61().b[39][1]++;
    }
    cov_1tnf2sn61().s[156]++;
    if (
    /* istanbul ignore next */
    (cov_1tnf2sn61().b[41][0]++, ductShape === 'rectangular') &&
    /* istanbul ignore next */
    (cov_1tnf2sn61().b[41][1]++, aspectRatio)) {
      /* istanbul ignore next */
      cov_1tnf2sn61().b[40][0]++;
      cov_1tnf2sn61().s[157]++;
      // Correction for rectangular ducts based on aspect ratio
      if (aspectRatio <= 1.5) {
        /* istanbul ignore next */
        cov_1tnf2sn61().b[42][0]++;
        cov_1tnf2sn61().s[158]++;
        return 1.0;
      } else
      /* istanbul ignore next */
      {
        cov_1tnf2sn61().b[42][1]++;
      }
      cov_1tnf2sn61().s[159]++;
      if (aspectRatio <= 2.0) {
        /* istanbul ignore next */
        cov_1tnf2sn61().b[43][0]++;
        cov_1tnf2sn61().s[160]++;
        return 1.05;
      } else
      /* istanbul ignore next */
      {
        cov_1tnf2sn61().b[43][1]++;
      }
      cov_1tnf2sn61().s[161]++;
      if (aspectRatio <= 3.0) {
        /* istanbul ignore next */
        cov_1tnf2sn61().b[44][0]++;
        cov_1tnf2sn61().s[162]++;
        return 1.10;
      } else
      /* istanbul ignore next */
      {
        cov_1tnf2sn61().b[44][1]++;
      }
      cov_1tnf2sn61().s[163]++;
      if (aspectRatio <= 4.0) {
        /* istanbul ignore next */
        cov_1tnf2sn61().b[45][0]++;
        cov_1tnf2sn61().s[164]++;
        return 1.15;
      } else
      /* istanbul ignore next */
      {
        cov_1tnf2sn61().b[45][1]++;
      }
      cov_1tnf2sn61().s[165]++;
      return 1.20; // High aspect ratio penalty
    } else
    /* istanbul ignore next */
    {
      cov_1tnf2sn61().b[40][1]++;
    }
    cov_1tnf2sn61().s[166]++;
    return 1.05; // Default correction for non-round shapes
  }
  /**
   * Calculate additional correction factors
   */
  static calculateAdditionalCorrections(correctionFactors) {
    /* istanbul ignore next */
    cov_1tnf2sn61().f[20]++;
    cov_1tnf2sn61().s[167]++;
    if (!correctionFactors) {
      /* istanbul ignore next */
      cov_1tnf2sn61().b[46][0]++;
      cov_1tnf2sn61().s[168]++;
      return 1.0;
    } else
    /* istanbul ignore next */
    {
      cov_1tnf2sn61().b[46][1]++;
    }
    const {
      installation =
      /* istanbul ignore next */
      (cov_1tnf2sn61().b[47][0]++, 1.0),
      maintenance =
      /* istanbul ignore next */
      (cov_1tnf2sn61().b[48][0]++, 1.0),
      environmental =
      /* istanbul ignore next */
      (cov_1tnf2sn61().b[49][0]++, 1.0)
    } =
    /* istanbul ignore next */
    (cov_1tnf2sn61().s[169]++, correctionFactors);
    /* istanbul ignore next */
    cov_1tnf2sn61().s[170]++;
    return installation * maintenance * environmental;
  }
  /**
   * Calculate uncertainty bounds
   */
  static calculateUncertaintyBounds(frictionLoss, method, flowRegime, relativeRoughness) {
    /* istanbul ignore next */
    cov_1tnf2sn61().f[21]++;
    let baseAccuracy =
    /* istanbul ignore next */
    (cov_1tnf2sn61().s[171]++, this.METHOD_ACCURACY[method]);
    // Adjust accuracy based on flow regime
    /* istanbul ignore next */
    cov_1tnf2sn61().s[172]++;
    if (flowRegime === FlowRegime.TRANSITIONAL) {
      /* istanbul ignore next */
      cov_1tnf2sn61().b[50][0]++;
      cov_1tnf2sn61().s[173]++;
      baseAccuracy *= 0.9; // Reduced accuracy in transitional regime
    } else
    /* istanbul ignore next */
    {
      cov_1tnf2sn61().b[50][1]++;
    }
    // Adjust accuracy based on relative roughness
    cov_1tnf2sn61().s[174]++;
    if (relativeRoughness > 0.05) {
      /* istanbul ignore next */
      cov_1tnf2sn61().b[51][0]++;
      cov_1tnf2sn61().s[175]++;
      baseAccuracy *= 0.95; // Reduced accuracy for very rough surfaces
    } else
    /* istanbul ignore next */
    {
      cov_1tnf2sn61().b[51][1]++;
    }
    const uncertainty =
    /* istanbul ignore next */
    (cov_1tnf2sn61().s[176]++, frictionLoss * (1 - baseAccuracy));
    /* istanbul ignore next */
    cov_1tnf2sn61().s[177]++;
    return {
      lower: frictionLoss - uncertainty,
      upper: frictionLoss + uncertainty,
      confidenceLevel: baseAccuracy
    };
  }
  /**
   * Generate recommendations
   */
  static generateRecommendations(velocity, reynoldsNumber, frictionRate, materialProperties, recommendations) {
    /* istanbul ignore next */
    cov_1tnf2sn61().f[22]++;
    cov_1tnf2sn61().s[178]++;
    // Velocity recommendations
    if (velocity > 4000) {
      /* istanbul ignore next */
      cov_1tnf2sn61().b[52][0]++;
      cov_1tnf2sn61().s[179]++;
      recommendations.push('High velocity detected - consider larger duct size to reduce friction and noise');
    } else
    /* istanbul ignore next */
    {
      cov_1tnf2sn61().b[52][1]++;
    }
    // Friction rate recommendations
    cov_1tnf2sn61().s[180]++;
    if (frictionRate > 0.15) {
      /* istanbul ignore next */
      cov_1tnf2sn61().b[53][0]++;
      cov_1tnf2sn61().s[181]++;
      recommendations.push('High friction rate - consider smoother materials or larger ducts');
    } else
    /* istanbul ignore next */
    {
      cov_1tnf2sn61().b[53][1]++;
    }
    // Material recommendations
    cov_1tnf2sn61().s[182]++;
    if (materialProperties.combinedFactor > 2.0) {
      /* istanbul ignore next */
      cov_1tnf2sn61().b[54][0]++;
      cov_1tnf2sn61().s[183]++;
      recommendations.push('Material aging/condition significantly affects friction - consider cleaning or replacement');
    } else
    /* istanbul ignore next */
    {
      cov_1tnf2sn61().b[54][1]++;
    }
    // Flow regime recommendations
    cov_1tnf2sn61().s[184]++;
    if (reynoldsNumber < 4000) {
      /* istanbul ignore next */
      cov_1tnf2sn61().b[55][0]++;
      cov_1tnf2sn61().s[185]++;
      recommendations.push('Low Reynolds number - verify velocity calculations and consider system optimization');
    } else
    /* istanbul ignore next */
    {
      cov_1tnf2sn61().b[55][1]++;
    }
  }
  /**
   * Get formula description for the method
   */
  static getFormulaDescription(method) {
    /* istanbul ignore next */
    cov_1tnf2sn61().f[23]++;
    const descriptions =
    /* istanbul ignore next */
    (cov_1tnf2sn61().s[186]++, {
      [FrictionMethod.COLEBROOK_WHITE]: '1/√f = -2log₁₀(ε/3.7D + 2.51/(Re√f))',
      [FrictionMethod.MOODY]: 'Moody diagram approximation',
      [FrictionMethod.SWAMEE_JAIN]: 'f = 0.25/[log₁₀(ε/3.7D + 5.74/Re^0.9)]²',
      [FrictionMethod.HAALAND]: '1/√f = -1.8log₁₀[(ε/3.7D)^1.11 + 6.9/Re]',
      [FrictionMethod.CHEN]: 'Chen explicit approximation',
      [FrictionMethod.ZIGRANG_SYLVESTER]: 'Zigrang-Sylvester explicit approximation',
      [FrictionMethod.ENHANCED_DARCY]: 'Flow regime optimized Darcy-Weisbach'
    });
    /* istanbul ignore next */
    cov_1tnf2sn61().s[187]++;
    return /* istanbul ignore next */(cov_1tnf2sn61().b[56][0]++, descriptions[method]) ||
    /* istanbul ignore next */
    (cov_1tnf2sn61().b[56][1]++, 'Unknown method');
  }
  /**
   * Get standard reference for the method
   */
  static getStandardReference(method) {
    /* istanbul ignore next */
    cov_1tnf2sn61().f[24]++;
    cov_1tnf2sn61().s[188]++;
    return 'ASHRAE Fundamentals, Chapter 21 - Duct Design';
  }
}
/* istanbul ignore next */
cov_1tnf2sn61().s[189]++;
exports.EnhancedFrictionCalculator = EnhancedFrictionCalculator;
/* istanbul ignore next */
cov_1tnf2sn61().s[190]++;
EnhancedFrictionCalculator.VERSION = '3.0.0';
// Standard air properties at 70°F, sea level
/* istanbul ignore next */
cov_1tnf2sn61().s[191]++;
EnhancedFrictionCalculator.STANDARD_AIR = {
  density: 0.075,
  // lb/ft³
  viscosity: 1.2e-5 // lb/(ft·s)
};
// Reynolds number transition points
/* istanbul ignore next */
cov_1tnf2sn61().s[192]++;
EnhancedFrictionCalculator.REYNOLDS_TRANSITIONS = {
  LAMINAR_MAX: 2300,
  TRANSITIONAL_MAX: 4000,
  TURBULENT_MIN: 4000
};
// Method accuracy estimates
/* istanbul ignore next */
cov_1tnf2sn61().s[193]++;
EnhancedFrictionCalculator.METHOD_ACCURACY = {
  [FrictionMethod.COLEBROOK_WHITE]: 0.98,
  [FrictionMethod.MOODY]: 0.95,
  [FrictionMethod.SWAMEE_JAIN]: 0.96,
  [FrictionMethod.HAALAND]: 0.97,
  [FrictionMethod.CHEN]: 0.96,
  [FrictionMethod.ZIGRANG_SYLVESTER]: 0.97,
  [FrictionMethod.ENHANCED_DARCY]: 0.99
};
// Material aging factors
/* istanbul ignore next */
cov_1tnf2sn61().s[194]++;
EnhancedFrictionCalculator.AGING_FACTORS = {
  [MaterialAge.NEW]: 1.0,
  [MaterialAge.GOOD]: 1.2,
  [MaterialAge.AVERAGE]: 1.5,
  [MaterialAge.POOR]: 2.0,
  [MaterialAge.VERY_POOR]: 3.0
};
// Surface condition factors
/* istanbul ignore next */
cov_1tnf2sn61().s[195]++;
EnhancedFrictionCalculator.SURFACE_FACTORS = {
  [SurfaceCondition.EXCELLENT]: 0.8,
  [SurfaceCondition.GOOD]: 1.0,
  [SurfaceCondition.AVERAGE]: 1.3,
  [SurfaceCondition.POOR]: 1.7,
  [SurfaceCondition.VERY_POOR]: 2.5
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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