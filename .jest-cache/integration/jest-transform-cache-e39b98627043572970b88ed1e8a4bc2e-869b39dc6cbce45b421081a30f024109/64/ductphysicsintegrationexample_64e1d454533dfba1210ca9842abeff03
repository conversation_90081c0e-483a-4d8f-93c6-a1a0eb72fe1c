356de92674d4673ceff0187def7afe11
"use strict";

/**
 * Duct Physics Integration Example
 *
 * Demonstrates the comprehensive duct physics implementation
 * Shows how to use FittingLossCalculator and SystemPressureCalculator
 *
 * Example: 10″ round duct → 10′ run → 90° elbow → 10′ run
 */
/* istanbul ignore next */
function cov_165opjsltb() {
  var path = "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\examples\\duct-physics-integration-example.ts";
  var hash = "32e0985b9193cd19b6c197b2ac6e7c8778809559";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\examples\\duct-physics-integration-example.ts",
    statementMap: {
      "0": {
        start: {
          line: 10,
          column: 0
        },
        end: {
          line: 10,
          column: 62
        }
      },
      "1": {
        start: {
          line: 11,
          column: 0
        },
        end: {
          line: 11,
          column: 78
        }
      },
      "2": {
        start: {
          line: 12,
          column: 0
        },
        end: {
          line: 12,
          column: 84
        }
      },
      "3": {
        start: {
          line: 13,
          column: 0
        },
        end: {
          line: 13,
          column: 60
        }
      },
      "4": {
        start: {
          line: 14,
          column: 0
        },
        end: {
          line: 14,
          column: 80
        }
      },
      "5": {
        start: {
          line: 15,
          column: 0
        },
        end: {
          line: 15,
          column: 40
        }
      },
      "6": {
        start: {
          line: 16,
          column: 32
        },
        end: {
          line: 16,
          column: 67
        }
      },
      "7": {
        start: {
          line: 17,
          column: 35
        },
        end: {
          line: 17,
          column: 73
        }
      },
      "8": {
        start: {
          line: 18,
          column: 28
        },
        end: {
          line: 18,
          column: 59
        }
      },
      "9": {
        start: {
          line: 23,
          column: 4
        },
        end: {
          line: 23,
          column: 62
        }
      },
      "10": {
        start: {
          line: 25,
          column: 24
        },
        end: {
          line: 30,
          column: 5
        }
      },
      "11": {
        start: {
          line: 31,
          column: 21
        },
        end: {
          line: 31,
          column: 25
        }
      },
      "12": {
        start: {
          line: 32,
          column: 19
        },
        end: {
          line: 32,
          column: 108
        }
      },
      "13": {
        start: {
          line: 33,
          column: 4
        },
        end: {
          line: 33,
          column: 49
        }
      },
      "14": {
        start: {
          line: 34,
          column: 4
        },
        end: {
          line: 34,
          column: 49
        }
      },
      "15": {
        start: {
          line: 35,
          column: 4
        },
        end: {
          line: 35,
          column: 84
        }
      },
      "16": {
        start: {
          line: 36,
          column: 4
        },
        end: {
          line: 36,
          column: 76
        }
      },
      "17": {
        start: {
          line: 37,
          column: 4
        },
        end: {
          line: 37,
          column: 60
        }
      },
      "18": {
        start: {
          line: 38,
          column: 4
        },
        end: {
          line: 40,
          column: 5
        }
      },
      "19": {
        start: {
          line: 39,
          column: 8
        },
        end: {
          line: 39,
          column: 65
        }
      },
      "20": {
        start: {
          line: 41,
          column: 4
        },
        end: {
          line: 43,
          column: 5
        }
      },
      "21": {
        start: {
          line: 42,
          column: 8
        },
        end: {
          line: 42,
          column: 79
        }
      },
      "22": {
        start: {
          line: 44,
          column: 4
        },
        end: {
          line: 44,
          column: 18
        }
      },
      "23": {
        start: {
          line: 51,
          column: 4
        },
        end: {
          line: 51,
          column: 70
        }
      },
      "24": {
        start: {
          line: 52,
          column: 4
        },
        end: {
          line: 52,
          column: 74
        }
      },
      "25": {
        start: {
          line: 54,
          column: 21
        },
        end: {
          line: 93,
          column: 5
        }
      },
      "26": {
        start: {
          line: 95,
          column: 19
        },
        end: {
          line: 109,
          column: 5
        }
      },
      "27": {
        start: {
          line: 111,
          column: 25
        },
        end: {
          line: 111,
          column: 108
        }
      },
      "28": {
        start: {
          line: 112,
          column: 4
        },
        end: {
          line: 112,
          column: 37
        }
      },
      "29": {
        start: {
          line: 113,
          column: 4
        },
        end: {
          line: 113,
          column: 82
        }
      },
      "30": {
        start: {
          line: 114,
          column: 4
        },
        end: {
          line: 114,
          column: 82
        }
      },
      "31": {
        start: {
          line: 115,
          column: 4
        },
        end: {
          line: 115,
          column: 76
        }
      },
      "32": {
        start: {
          line: 116,
          column: 4
        },
        end: {
          line: 116,
          column: 68
        }
      },
      "33": {
        start: {
          line: 117,
          column: 4
        },
        end: {
          line: 117,
          column: 75
        }
      },
      "34": {
        start: {
          line: 118,
          column: 4
        },
        end: {
          line: 118,
          column: 67
        }
      },
      "35": {
        start: {
          line: 119,
          column: 4
        },
        end: {
          line: 119,
          column: 38
        }
      },
      "36": {
        start: {
          line: 120,
          column: 4
        },
        end: {
          line: 134,
          column: 7
        }
      },
      "37": {
        start: {
          line: 121,
          column: 8
        },
        end: {
          line: 121,
          column: 70
        }
      },
      "38": {
        start: {
          line: 122,
          column: 8
        },
        end: {
          line: 122,
          column: 56
        }
      },
      "39": {
        start: {
          line: 123,
          column: 8
        },
        end: {
          line: 123,
          column: 61
        }
      },
      "40": {
        start: {
          line: 124,
          column: 8
        },
        end: {
          line: 124,
          column: 80
        }
      },
      "41": {
        start: {
          line: 125,
          column: 8
        },
        end: {
          line: 125,
          column: 72
        }
      },
      "42": {
        start: {
          line: 126,
          column: 8
        },
        end: {
          line: 126,
          column: 66
        }
      },
      "43": {
        start: {
          line: 127,
          column: 8
        },
        end: {
          line: 127,
          column: 66
        }
      },
      "44": {
        start: {
          line: 128,
          column: 8
        },
        end: {
          line: 130,
          column: 9
        }
      },
      "45": {
        start: {
          line: 129,
          column: 12
        },
        end: {
          line: 129,
          column: 60
        }
      },
      "46": {
        start: {
          line: 131,
          column: 8
        },
        end: {
          line: 133,
          column: 9
        }
      },
      "47": {
        start: {
          line: 132,
          column: 12
        },
        end: {
          line: 132,
          column: 72
        }
      },
      "48": {
        start: {
          line: 135,
          column: 4
        },
        end: {
          line: 135,
          column: 40
        }
      },
      "49": {
        start: {
          line: 136,
          column: 4
        },
        end: {
          line: 136,
          column: 92
        }
      },
      "50": {
        start: {
          line: 137,
          column: 4
        },
        end: {
          line: 137,
          column: 92
        }
      },
      "51": {
        start: {
          line: 138,
          column: 4
        },
        end: {
          line: 138,
          column: 88
        }
      },
      "52": {
        start: {
          line: 139,
          column: 4
        },
        end: {
          line: 142,
          column: 5
        }
      },
      "53": {
        start: {
          line: 140,
          column: 8
        },
        end: {
          line: 140,
          column: 42
        }
      },
      "54": {
        start: {
          line: 141,
          column: 8
        },
        end: {
          line: 141,
          column: 86
        }
      },
      "55": {
        start: {
          line: 141,
          column: 55
        },
        end: {
          line: 141,
          column: 84
        }
      },
      "56": {
        start: {
          line: 143,
          column: 4
        },
        end: {
          line: 146,
          column: 5
        }
      },
      "57": {
        start: {
          line: 144,
          column: 8
        },
        end: {
          line: 144,
          column: 49
        }
      },
      "58": {
        start: {
          line: 145,
          column: 8
        },
        end: {
          line: 145,
          column: 85
        }
      },
      "59": {
        start: {
          line: 145,
          column: 58
        },
        end: {
          line: 145,
          column: 83
        }
      },
      "60": {
        start: {
          line: 147,
          column: 4
        },
        end: {
          line: 147,
          column: 24
        }
      },
      "61": {
        start: {
          line: 153,
          column: 4
        },
        end: {
          line: 153,
          column: 52
        }
      },
      "62": {
        start: {
          line: 154,
          column: 4
        },
        end: {
          line: 154,
          column: 78
        }
      },
      "63": {
        start: {
          line: 155,
          column: 28
        },
        end: {
          line: 232,
          column: 5
        }
      },
      "64": {
        start: {
          line: 233,
          column: 26
        },
        end: {
          line: 247,
          column: 5
        }
      },
      "65": {
        start: {
          line: 248,
          column: 26
        },
        end: {
          line: 248,
          column: 116
        }
      },
      "66": {
        start: {
          line: 249,
          column: 4
        },
        end: {
          line: 249,
          column: 45
        }
      },
      "67": {
        start: {
          line: 250,
          column: 4
        },
        end: {
          line: 250,
          column: 83
        }
      },
      "68": {
        start: {
          line: 251,
          column: 4
        },
        end: {
          line: 251,
          column: 130
        }
      },
      "69": {
        start: {
          line: 252,
          column: 4
        },
        end: {
          line: 252,
          column: 70
        }
      },
      "70": {
        start: {
          line: 253,
          column: 4
        },
        end: {
          line: 253,
          column: 101
        }
      },
      "71": {
        start: {
          line: 254,
          column: 4
        },
        end: {
          line: 254,
          column: 25
        }
      },
      "72": {
        start: {
          line: 260,
          column: 4
        },
        end: {
          line: 260,
          column: 66
        }
      },
      "73": {
        start: {
          line: 262,
          column: 24
        },
        end: {
          line: 268,
          column: 6
        }
      },
      "74": {
        start: {
          line: 269,
          column: 4
        },
        end: {
          line: 269,
          column: 58
        }
      },
      "75": {
        start: {
          line: 270,
          column: 4
        },
        end: {
          line: 270,
          column: 74
        }
      },
      "76": {
        start: {
          line: 271,
          column: 4
        },
        end: {
          line: 271,
          column: 59
        }
      },
      "77": {
        start: {
          line: 272,
          column: 4
        },
        end: {
          line: 272,
          column: 77
        }
      },
      "78": {
        start: {
          line: 274,
          column: 29
        },
        end: {
          line: 284,
          column: 5
        }
      },
      "79": {
        start: {
          line: 285,
          column: 27
        },
        end: {
          line: 299,
          column: 5
        }
      },
      "80": {
        start: {
          line: 300,
          column: 27
        },
        end: {
          line: 300,
          column: 118
        }
      },
      "81": {
        start: {
          line: 301,
          column: 4
        },
        end: {
          line: 301,
          column: 74
        }
      },
      "82": {
        start: {
          line: 302,
          column: 4
        },
        end: {
          line: 302,
          column: 80
        }
      },
      "83": {
        start: {
          line: 303,
          column: 4
        },
        end: {
          line: 303,
          column: 87
        }
      },
      "84": {
        start: {
          line: 304,
          column: 4
        },
        end: {
          line: 304,
          column: 103
        }
      },
      "85": {
        start: {
          line: 305,
          column: 4
        },
        end: {
          line: 305,
          column: 33
        }
      },
      "86": {
        start: {
          line: 306,
          column: 4
        },
        end: {
          line: 306,
          column: 126
        }
      },
      "87": {
        start: {
          line: 307,
          column: 4
        },
        end: {
          line: 307,
          column: 141
        }
      },
      "88": {
        start: {
          line: 313,
          column: 4
        },
        end: {
          line: 313,
          column: 84
        }
      },
      "89": {
        start: {
          line: 314,
          column: 4
        },
        end: {
          line: 314,
          column: 84
        }
      },
      "90": {
        start: {
          line: 315,
          column: 4
        },
        end: {
          line: 328,
          column: 5
        }
      },
      "91": {
        start: {
          line: 317,
          column: 8
        },
        end: {
          line: 317,
          column: 44
        }
      },
      "92": {
        start: {
          line: 318,
          column: 8
        },
        end: {
          line: 318,
          column: 47
        }
      },
      "93": {
        start: {
          line: 319,
          column: 8
        },
        end: {
          line: 319,
          column: 35
        }
      },
      "94": {
        start: {
          line: 320,
          column: 8
        },
        end: {
          line: 320,
          column: 45
        }
      },
      "95": {
        start: {
          line: 321,
          column: 8
        },
        end: {
          line: 321,
          column: 69
        }
      },
      "96": {
        start: {
          line: 322,
          column: 8
        },
        end: {
          line: 322,
          column: 68
        }
      },
      "97": {
        start: {
          line: 325,
          column: 8
        },
        end: {
          line: 325,
          column: 53
        }
      },
      "98": {
        start: {
          line: 326,
          column: 8
        },
        end: {
          line: 326,
          column: 29
        }
      },
      "99": {
        start: {
          line: 327,
          column: 8
        },
        end: {
          line: 327,
          column: 24
        }
      },
      "100": {
        start: {
          line: 331,
          column: 0
        },
        end: {
          line: 333,
          column: 1
        }
      },
      "101": {
        start: {
          line: 332,
          column: 4
        },
        end: {
          line: 332,
          column: 21
        }
      }
    },
    fnMap: {
      "0": {
        name: "demonstrateFittingLossCalculation",
        decl: {
          start: {
            line: 22,
            column: 9
          },
          end: {
            line: 22,
            column: 42
          }
        },
        loc: {
          start: {
            line: 22,
            column: 45
          },
          end: {
            line: 45,
            column: 1
          }
        },
        line: 22
      },
      "1": {
        name: "demonstrateSystemPressureCalculation",
        decl: {
          start: {
            line: 50,
            column: 9
          },
          end: {
            line: 50,
            column: 45
          }
        },
        loc: {
          start: {
            line: 50,
            column: 48
          },
          end: {
            line: 148,
            column: 1
          }
        },
        line: 50
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 120,
            column: 40
          },
          end: {
            line: 120,
            column: 41
          }
        },
        loc: {
          start: {
            line: 120,
            column: 60
          },
          end: {
            line: 134,
            column: 5
          }
        },
        line: 120
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 141,
            column: 44
          },
          end: {
            line: 141,
            column: 45
          }
        },
        loc: {
          start: {
            line: 141,
            column: 55
          },
          end: {
            line: 141,
            column: 84
          }
        },
        line: 141
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 145,
            column: 51
          },
          end: {
            line: 145,
            column: 52
          }
        },
        loc: {
          start: {
            line: 145,
            column: 58
          },
          end: {
            line: 145,
            column: 83
          }
        },
        line: 145
      },
      "5": {
        name: "demonstrateComplexSystem",
        decl: {
          start: {
            line: 152,
            column: 9
          },
          end: {
            line: 152,
            column: 33
          }
        },
        loc: {
          start: {
            line: 152,
            column: 36
          },
          end: {
            line: 255,
            column: 1
          }
        },
        line: 152
      },
      "6": {
        name: "demonstrateIntegrationWithExisting",
        decl: {
          start: {
            line: 259,
            column: 9
          },
          end: {
            line: 259,
            column: 43
          }
        },
        loc: {
          start: {
            line: 259,
            column: 46
          },
          end: {
            line: 308,
            column: 1
          }
        },
        line: 259
      },
      "7": {
        name: "runAllExamples",
        decl: {
          start: {
            line: 312,
            column: 9
          },
          end: {
            line: 312,
            column: 23
          }
        },
        loc: {
          start: {
            line: 312,
            column: 26
          },
          end: {
            line: 329,
            column: 1
          }
        },
        line: 312
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 38,
            column: 4
          },
          end: {
            line: 40,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 38,
            column: 4
          },
          end: {
            line: 40,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 38
      },
      "1": {
        loc: {
          start: {
            line: 41,
            column: 4
          },
          end: {
            line: 43,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 41,
            column: 4
          },
          end: {
            line: 43,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 41
      },
      "2": {
        loc: {
          start: {
            line: 128,
            column: 8
          },
          end: {
            line: 130,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 128,
            column: 8
          },
          end: {
            line: 130,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 128
      },
      "3": {
        loc: {
          start: {
            line: 131,
            column: 8
          },
          end: {
            line: 133,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 131,
            column: 8
          },
          end: {
            line: 133,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 131
      },
      "4": {
        loc: {
          start: {
            line: 139,
            column: 4
          },
          end: {
            line: 142,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 139,
            column: 4
          },
          end: {
            line: 142,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 139
      },
      "5": {
        loc: {
          start: {
            line: 143,
            column: 4
          },
          end: {
            line: 146,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 143,
            column: 4
          },
          end: {
            line: 146,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 143
      },
      "6": {
        loc: {
          start: {
            line: 306,
            column: 37
          },
          end: {
            line: 306,
            column: 122
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 306,
            column: 107
          },
          end: {
            line: 306,
            column: 113
          }
        }, {
          start: {
            line: 306,
            column: 116
          },
          end: {
            line: 306,
            column: 122
          }
        }],
        line: 306
      },
      "7": {
        loc: {
          start: {
            line: 307,
            column: 42
          },
          end: {
            line: 307,
            column: 137
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 307,
            column: 122
          },
          end: {
            line: 307,
            column: 128
          }
        }, {
          start: {
            line: 307,
            column: 131
          },
          end: {
            line: 307,
            column: 137
          }
        }],
        line: 307
      },
      "8": {
        loc: {
          start: {
            line: 331,
            column: 0
          },
          end: {
            line: 333,
            column: 1
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 331,
            column: 0
          },
          end: {
            line: 333,
            column: 1
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 331
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\examples\\duct-physics-integration-example.ts",
      mappings: ";AAAA;;;;;;;GAOG;;AA0WD,8EAAiC;AACjC,oFAAoC;AACpC,4DAAwB;AACxB,gFAAkC;AAClC,wCAAc;AA5WhB,oEAAuF;AACvF,0EAA6G;AAC7G,4DAAyD;AAEzD;;GAEG;AACH,SAAS,iCAAiC;IACxC,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;IAE1D,2CAA2C;IAC3C,MAAM,WAAW,GAAyB;QACxC,IAAI,EAAE,oBAAoB;QAC1B,SAAS,EAAE,OAAO;QAClB,QAAQ,EAAE,EAAE;QACZ,SAAS,EAAE,KAAK;KACjB,CAAC;IAEF,MAAM,QAAQ,GAAG,IAAI,CAAC,CAAC,6CAA6C;IACpE,MAAM,MAAM,GAAG,6CAAqB,CAAC,oBAAoB,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;IAEjF,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;IAC7C,OAAO,CAAC,GAAG,CAAC,eAAe,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;IAC7C,OAAO,CAAC,GAAG,CAAC,wBAAwB,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;IAChF,OAAO,CAAC,GAAG,CAAC,oBAAoB,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;IACxE,OAAO,CAAC,GAAG,CAAC,oBAAoB,MAAM,CAAC,aAAa,EAAE,CAAC,CAAC;IAExD,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAC/B,OAAO,CAAC,GAAG,CAAC,eAAe,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC3D,CAAC;IAED,IAAI,MAAM,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACtC,OAAO,CAAC,GAAG,CAAC,sBAAsB,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACzE,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,oCAAoC;IAC3C,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAC;IAClE,OAAO,CAAC,GAAG,CAAC,wDAAwD,CAAC,CAAC;IAEtE,yBAAyB;IACzB,MAAM,QAAQ,GAAkB;QAC9B,8BAA8B;QAC9B;YACE,EAAE,EAAE,gBAAgB;YACpB,IAAI,EAAE,UAAU;YAChB,SAAS,EAAE,OAAO;YAClB,MAAM,EAAE,EAAE;YACV,QAAQ,EAAE,EAAE;YACZ,OAAO,EAAE,IAAI;YACb,QAAQ,EAAE,kBAAkB;YAC5B,KAAK,EAAE,wBAAwB;SAChC;QAED,2BAA2B;QAC3B;YACE,EAAE,EAAE,aAAa;YACjB,IAAI,EAAE,SAAS;YACf,SAAS,EAAE,OAAO;YAClB,QAAQ,EAAE,EAAE;YACZ,OAAO,EAAE,IAAI;YACb,QAAQ,EAAE,kBAAkB;YAC5B,aAAa,EAAE;gBACb,IAAI,EAAE,oBAAoB;gBAC1B,SAAS,EAAE,OAAO;gBAClB,QAAQ,EAAE,EAAE;gBACZ,SAAS,EAAE,KAAK;aACjB;YACD,KAAK,EAAE,kBAAkB;SAC1B;QAED,+BAA+B;QAC/B;YACE,EAAE,EAAE,gBAAgB;YACpB,IAAI,EAAE,UAAU;YAChB,SAAS,EAAE,OAAO;YAClB,MAAM,EAAE,EAAE;YACV,QAAQ,EAAE,EAAE;YACZ,OAAO,EAAE,IAAI;YACb,QAAQ,EAAE,kBAAkB;YAC5B,KAAK,EAAE,yBAAyB;SACjC;KACF,CAAC;IAEF,4BAA4B;IAC5B,MAAM,MAAM,GAA4B;QACtC,QAAQ;QACR,UAAU,EAAE,QAAQ;QACpB,gBAAgB,EAAE;YAChB,WAAW,EAAE,EAAE,EAAE,KAAK;YACtB,kBAAkB,EAAE,KAAK,EAAE,QAAQ;YACnC,QAAQ,EAAE,CAAC,CAAC,uBAAuB;SACpC;QACD,kBAAkB,EAAE;YAClB,uBAAuB,EAAE,KAAK;YAC9B,yBAAyB,EAAE,IAAI;YAC/B,cAAc,EAAE,gBAAgB;YAChC,iBAAiB,EAAE,CAAC;SACrB;KACF,CAAC;IAEF,iCAAiC;IACjC,MAAM,YAAY,GAAG,mDAAwB,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC;IAE9E,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;IACjC,OAAO,CAAC,GAAG,CAAC,0BAA0B,YAAY,CAAC,iBAAiB,QAAQ,CAAC,CAAC;IAC9E,OAAO,CAAC,GAAG,CAAC,0BAA0B,YAAY,CAAC,iBAAiB,QAAQ,CAAC,CAAC;IAC9E,OAAO,CAAC,GAAG,CAAC,uBAAuB,YAAY,CAAC,cAAc,QAAQ,CAAC,CAAC;IACxE,OAAO,CAAC,GAAG,CAAC,mBAAmB,YAAY,CAAC,WAAW,OAAO,CAAC,CAAC;IAChE,OAAO,CAAC,GAAG,CAAC,uBAAuB,YAAY,CAAC,eAAe,MAAM,CAAC,CAAC;IACvE,OAAO,CAAC,GAAG,CAAC,mBAAmB,YAAY,CAAC,WAAW,MAAM,CAAC,CAAC;IAE/D,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;IAClC,YAAY,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;QACrD,OAAO,CAAC,GAAG,CAAC,aAAa,KAAK,GAAG,CAAC,KAAK,OAAO,CAAC,SAAS,IAAI,CAAC,CAAC;QAC9D,OAAO,CAAC,GAAG,CAAC,aAAa,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC;QAChD,OAAO,CAAC,GAAG,CAAC,iBAAiB,OAAO,CAAC,QAAQ,MAAM,CAAC,CAAC;QACrD,OAAO,CAAC,GAAG,CAAC,0BAA0B,OAAO,CAAC,gBAAgB,QAAQ,CAAC,CAAC;QACxE,OAAO,CAAC,GAAG,CAAC,sBAAsB,OAAO,CAAC,YAAY,QAAQ,CAAC,CAAC;QAChE,OAAO,CAAC,GAAG,CAAC,mBAAmB,OAAO,CAAC,SAAS,QAAQ,CAAC,CAAC;QAC1D,OAAO,CAAC,GAAG,CAAC,mBAAmB,OAAO,CAAC,SAAS,QAAQ,CAAC,CAAC;QAE1D,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YACpB,OAAO,CAAC,GAAG,CAAC,iBAAiB,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;QAClD,CAAC;QAED,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChC,OAAO,CAAC,GAAG,CAAC,iBAAiB,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;IACpC,OAAO,CAAC,GAAG,CAAC,yBAAyB,YAAY,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,CAAC,CAAC;IACxF,OAAO,CAAC,GAAG,CAAC,yBAAyB,YAAY,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,CAAC,CAAC;IACxF,OAAO,CAAC,GAAG,CAAC,uBAAuB,YAAY,CAAC,gBAAgB,CAAC,eAAe,EAAE,CAAC,CAAC;IAEpF,IAAI,YAAY,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAC3C,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;QAClC,YAAY,CAAC,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,OAAO,EAAE,CAAC,CAAC,CAAC;IAChF,CAAC;IAED,IAAI,YAAY,CAAC,qBAAqB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAClD,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;QACzC,YAAY,CAAC,qBAAqB,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,GAAG,EAAE,CAAC,CAAC,CAAC;IAC/E,CAAC;IAED,OAAO,YAAY,CAAC;AACtB,CAAC;AAED;;GAEG;AACH,SAAS,wBAAwB;IAC/B,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;IAChD,OAAO,CAAC,GAAG,CAAC,4DAA4D,CAAC,CAAC;IAE1E,MAAM,eAAe,GAAkB;QACrC,iCAAiC;QACjC;YACE,EAAE,EAAE,YAAY;YAChB,IAAI,EAAE,UAAU;YAChB,SAAS,EAAE,OAAO;YAClB,MAAM,EAAE,EAAE;YACV,QAAQ,EAAE,EAAE;YACZ,OAAO,EAAE,IAAI;YACb,QAAQ,EAAE,kBAAkB;SAC7B;QAED,yBAAyB;QACzB;YACE,EAAE,EAAE,UAAU;YACd,IAAI,EAAE,SAAS;YACf,SAAS,EAAE,OAAO;YAClB,QAAQ,EAAE,EAAE;YACZ,OAAO,EAAE,IAAI;YACb,QAAQ,EAAE,kBAAkB;YAC5B,aAAa,EAAE;gBACb,IAAI,EAAE,wBAAwB;gBAC9B,SAAS,EAAE,OAAO;gBAClB,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,kBAAkB;gBAC3B,SAAS,EAAE,KAAK,CAAC,oBAAoB;aACtC;SACF;QAED,6BAA6B;QAC7B;YACE,EAAE,EAAE,qBAAqB;YACzB,IAAI,EAAE,SAAS;YACf,SAAS,EAAE,OAAO;YAClB,QAAQ,EAAE,EAAE;YACZ,OAAO,EAAE,IAAI,EAAE,4BAA4B;YAC3C,QAAQ,EAAE,kBAAkB;YAC5B,aAAa,EAAE;gBACb,IAAI,EAAE,wBAAwB;gBAC9B,SAAS,EAAE,OAAO;gBAClB,QAAQ,EAAE,EAAE;gBACZ,SAAS,EAAE,KAAK,CAAC,YAAY;aAC9B;SACF;QAED,eAAe;QACf;YACE,EAAE,EAAE,aAAa;YACjB,IAAI,EAAE,UAAU;YAChB,SAAS,EAAE,OAAO;YAClB,MAAM,EAAE,EAAE;YACV,QAAQ,EAAE,EAAE;YACZ,OAAO,EAAE,IAAI;YACb,QAAQ,EAAE,kBAAkB;SAC7B;QAED,YAAY;QACZ;YACE,EAAE,EAAE,aAAa;YACjB,IAAI,EAAE,SAAS;YACf,SAAS,EAAE,OAAO;YAClB,QAAQ,EAAE,EAAE;YACZ,OAAO,EAAE,IAAI;YACb,QAAQ,EAAE,kBAAkB;YAC5B,aAAa,EAAE;gBACb,IAAI,EAAE,oBAAoB;gBAC1B,SAAS,EAAE,OAAO;gBAClB,QAAQ,EAAE,EAAE;gBACZ,SAAS,EAAE,KAAK;aACjB;SACF;QAED,wBAAwB;QACxB;YACE,EAAE,EAAE,WAAW;YACf,IAAI,EAAE,UAAU;YAChB,SAAS,EAAE,OAAO;YAClB,MAAM,EAAE,CAAC;YACT,QAAQ,EAAE,EAAE;YACZ,OAAO,EAAE,IAAI;YACb,QAAQ,EAAE,kBAAkB;SAC7B;KACF,CAAC;IAEF,MAAM,aAAa,GAA4B;QAC7C,QAAQ,EAAE,eAAe;QACzB,UAAU,EAAE,QAAQ;QACpB,gBAAgB,EAAE;YAChB,WAAW,EAAE,EAAE;YACf,kBAAkB,EAAE,KAAK;YACzB,QAAQ,EAAE,CAAC;SACZ;QACD,kBAAkB,EAAE;YAClB,uBAAuB,EAAE,KAAK;YAC9B,yBAAyB,EAAE,IAAI;YAC/B,cAAc,EAAE,gBAAgB;YAChC,iBAAiB,EAAE,CAAC;SACrB;KACF,CAAC;IAEF,MAAM,aAAa,GAAG,mDAAwB,CAAC,uBAAuB,CAAC,aAAa,CAAC,CAAC;IAEtF,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;IACzC,OAAO,CAAC,GAAG,CAAC,0BAA0B,aAAa,CAAC,iBAAiB,QAAQ,CAAC,CAAC;IAC/E,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,aAAa,CAAC,iBAAiB,GAAG,aAAa,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAC9H,OAAO,CAAC,GAAG,CAAC,oBAAoB,aAAa,CAAC,WAAW,OAAO,CAAC,CAAC;IAClE,OAAO,CAAC,GAAG,CAAC,qBAAqB,aAAa,CAAC,WAAW,MAAM,aAAa,CAAC,WAAW,MAAM,CAAC,CAAC;IAEjG,OAAO,aAAa,CAAC;AACvB,CAAC;AAED;;GAEG;AACH,SAAS,kCAAkC;IACzC,OAAO,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAC;IAE9D,kDAAkD;IAClD,MAAM,WAAW,GAAG,qCAAiB,CAAC,mBAAmB,CAAC;QACxD,OAAO,EAAE,IAAI;QACb,QAAQ,EAAE,OAAO;QACjB,YAAY,EAAE,IAAI;QAClB,KAAK,EAAE,UAAU;QACjB,QAAQ,EAAE,kBAAkB;KAC7B,CAAC,CAAC;IAEH,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;IACtD,OAAO,CAAC,GAAG,CAAC,2BAA2B,WAAW,CAAC,QAAQ,SAAS,CAAC,CAAC;IACtE,OAAO,CAAC,GAAG,CAAC,eAAe,WAAW,CAAC,QAAQ,MAAM,CAAC,CAAC;IACvD,OAAO,CAAC,GAAG,CAAC,oBAAoB,WAAW,CAAC,YAAY,eAAe,CAAC,CAAC;IAEzE,sDAAsD;IACtD,MAAM,gBAAgB,GAAkB;QACtC;YACE,EAAE,EAAE,YAAY;YAChB,IAAI,EAAE,UAAU;YAChB,SAAS,EAAE,OAAO;YAClB,MAAM,EAAE,GAAG,EAAE,0BAA0B;YACvC,QAAQ,EAAE,WAAW,CAAC,QAAQ;YAC9B,OAAO,EAAE,IAAI;YACb,QAAQ,EAAE,kBAAkB;SAC7B;KACF,CAAC;IAEF,MAAM,cAAc,GAA4B;QAC9C,QAAQ,EAAE,gBAAgB;QAC1B,UAAU,EAAE,QAAQ;QACpB,gBAAgB,EAAE;YAChB,WAAW,EAAE,EAAE;YACf,kBAAkB,EAAE,KAAK;YACzB,QAAQ,EAAE,CAAC;SACZ;QACD,kBAAkB,EAAE;YAClB,uBAAuB,EAAE,KAAK;YAC9B,yBAAyB,EAAE,IAAI;YAC/B,cAAc,EAAE,gBAAgB;YAChC,iBAAiB,EAAE,CAAC;SACrB;KACF,CAAC;IAEF,MAAM,cAAc,GAAG,mDAAwB,CAAC,uBAAuB,CAAC,cAAc,CAAC,CAAC;IAExF,OAAO,CAAC,GAAG,CAAC,wDAAwD,CAAC,CAAC;IACtE,OAAO,CAAC,GAAG,CAAC,0BAA0B,cAAc,CAAC,eAAe,MAAM,CAAC,CAAC;IAC5E,OAAO,CAAC,GAAG,CAAC,6BAA6B,cAAc,CAAC,iBAAiB,QAAQ,CAAC,CAAC;IACnF,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC;IAEnG,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;IAC7B,OAAO,CAAC,GAAG,CAAC,qBAAqB,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,QAAQ,GAAG,cAAc,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;IAC1H,OAAO,CAAC,GAAG,CAAC,0BAA0B,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,YAAY,GAAG,cAAc,CAAC,iBAAiB,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;AAC3I,CAAC;AAED;;GAEG;AACH,SAAS,cAAc;IACrB,OAAO,CAAC,GAAG,CAAC,kEAAkE,CAAC,CAAC;IAChF,OAAO,CAAC,GAAG,CAAC,kEAAkE,CAAC,CAAC;IAEhF,IAAI,CAAC;QACH,mBAAmB;QACnB,iCAAiC,EAAE,CAAC;QACpC,oCAAoC,EAAE,CAAC;QACvC,wBAAwB,EAAE,CAAC;QAC3B,kCAAkC,EAAE,CAAC;QAErC,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAC;QAC7D,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;IAE9D,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC;QAC7C,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACrB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;AACH,CAAC;AAWD,iDAAiD;AACjD,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,cAAc,EAAE,CAAC;AACnB,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\examples\\duct-physics-integration-example.ts"],
      sourcesContent: ["/**\r\n * Duct Physics Integration Example\r\n * \r\n * Demonstrates the comprehensive duct physics implementation\r\n * Shows how to use FittingLossCalculator and SystemPressureCalculator\r\n * \r\n * Example: 10\u2033 round duct \u2192 10\u2032 run \u2192 90\xB0 elbow \u2192 10\u2032 run\r\n */\r\n\r\nimport { FittingLossCalculator, FittingConfiguration } from '../FittingLossCalculator';\r\nimport { SystemPressureCalculator, DuctSegment, SystemCalculationInputs } from '../SystemPressureCalculator';\r\nimport { AirDuctCalculator } from '../AirDuctCalculator';\r\n\r\n/**\r\n * Example 1: Individual Fitting Loss Calculation\r\n */\r\nfunction demonstrateFittingLossCalculation() {\r\n  console.log('\\n=== FITTING LOSS CALCULATION EXAMPLE ===');\r\n  \r\n  // Example: 90\xB0 smooth elbow with R/D = 1.5\r\n  const elbowConfig: FittingConfiguration = {\r\n    type: '90deg_round_smooth',\r\n    ductShape: 'round',\r\n    diameter: 10,\r\n    parameter: '1.5'\r\n  };\r\n\r\n  const velocity = 1833; // FPM (calculated from 1000 CFM in 10\" duct)\r\n  const result = FittingLossCalculator.calculateFittingLoss(elbowConfig, velocity);\r\n\r\n  console.log('90\xB0 Smooth Elbow (R/D = 1.5):');\r\n  console.log(`  K-factor: ${result.kFactor}`);\r\n  console.log(`  Velocity Pressure: ${result.velocityPressure.toFixed(4)} in wg`);\r\n  console.log(`  Pressure Loss: ${result.pressureLoss.toFixed(4)} in wg`);\r\n  console.log(`  Configuration: ${result.configuration}`);\r\n  \r\n  if (result.warnings.length > 0) {\r\n    console.log(`  Warnings: ${result.warnings.join(', ')}`);\r\n  }\r\n  \r\n  if (result.recommendations.length > 0) {\r\n    console.log(`  Recommendations: ${result.recommendations.join(', ')}`);\r\n  }\r\n\r\n  return result;\r\n}\r\n\r\n/**\r\n * Example 2: Complete System Pressure Drop Calculation\r\n * User's example: 10\u2033 round duct \u2192 10\u2032 run \u2192 90\xB0 elbow \u2192 10\u2032 run\r\n */\r\nfunction demonstrateSystemPressureCalculation() {\r\n  console.log('\\n=== SYSTEM PRESSURE DROP CALCULATION EXAMPLE ===');\r\n  console.log('System: 10\u2033 round duct \u2192 10\u2032 run \u2192 90\xB0 elbow \u2192 10\u2032 run');\r\n  \r\n  // Define system segments\r\n  const segments: DuctSegment[] = [\r\n    // First straight run: 10 feet\r\n    {\r\n      id: 'straight-run-1',\r\n      type: 'straight',\r\n      ductShape: 'round',\r\n      length: 10,\r\n      diameter: 10,\r\n      airflow: 1000,\r\n      material: 'galvanized_steel',\r\n      notes: 'First straight section'\r\n    },\r\n    \r\n    // 90\xB0 elbow with R/D = 1.5\r\n    {\r\n      id: 'elbow-90deg',\r\n      type: 'fitting',\r\n      ductShape: 'round',\r\n      diameter: 10,\r\n      airflow: 1000,\r\n      material: 'galvanized_steel',\r\n      fittingConfig: {\r\n        type: '90deg_round_smooth',\r\n        ductShape: 'round',\r\n        diameter: 10,\r\n        parameter: '1.5'\r\n      },\r\n      notes: '90\xB0 smooth elbow'\r\n    },\r\n    \r\n    // Second straight run: 10 feet\r\n    {\r\n      id: 'straight-run-2',\r\n      type: 'straight',\r\n      ductShape: 'round',\r\n      length: 10,\r\n      diameter: 10,\r\n      airflow: 1000,\r\n      material: 'galvanized_steel',\r\n      notes: 'Second straight section'\r\n    }\r\n  ];\r\n\r\n  // System calculation inputs\r\n  const inputs: SystemCalculationInputs = {\r\n    segments,\r\n    systemType: 'supply',\r\n    designConditions: {\r\n      temperature: 70, // \xB0F\r\n      barometricPressure: 29.92, // in Hg\r\n      altitude: 0 // feet above sea level\r\n    },\r\n    calculationOptions: {\r\n      includeElevationEffects: false,\r\n      includeTemperatureEffects: true,\r\n      frictionMethod: 'darcy_weisbach',\r\n      roundingPrecision: 4\r\n    }\r\n  };\r\n\r\n  // Calculate system pressure drop\r\n  const systemResult = SystemPressureCalculator.calculateSystemPressure(inputs);\r\n\r\n  console.log('\\nSystem Results:');\r\n  console.log(`  Total Pressure Loss: ${systemResult.totalPressureLoss} in wg`);\r\n  console.log(`  Total Friction Loss: ${systemResult.totalFrictionLoss} in wg`);\r\n  console.log(`  Total Minor Loss: ${systemResult.totalMinorLoss} in wg`);\r\n  console.log(`  Total Length: ${systemResult.totalLength} feet`);\r\n  console.log(`  Average Velocity: ${systemResult.averageVelocity} FPM`);\r\n  console.log(`  Max Velocity: ${systemResult.maxVelocity} FPM`);\r\n\r\n  console.log('\\nSegment Details:');\r\n  systemResult.segmentResults.forEach((segment, index) => {\r\n    console.log(`  Segment ${index + 1} (${segment.segmentId}):`);\r\n    console.log(`    Type: ${segment.segmentType}`);\r\n    console.log(`    Velocity: ${segment.velocity} FPM`);\r\n    console.log(`    Velocity Pressure: ${segment.velocityPressure} in wg`);\r\n    console.log(`    Friction Loss: ${segment.frictionLoss} in wg`);\r\n    console.log(`    Minor Loss: ${segment.minorLoss} in wg`);\r\n    console.log(`    Total Loss: ${segment.totalLoss} in wg`);\r\n    \r\n    if (segment.kFactor) {\r\n      console.log(`    K-factor: ${segment.kFactor}`);\r\n    }\r\n    \r\n    if (segment.warnings.length > 0) {\r\n      console.log(`    Warnings: ${segment.warnings.join(', ')}`);\r\n    }\r\n  });\r\n\r\n  console.log('\\nCompliance Status:');\r\n  console.log(`  Velocity Compliant: ${systemResult.complianceStatus.velocityCompliant}`);\r\n  console.log(`  Pressure Compliant: ${systemResult.complianceStatus.pressureCompliant}`);\r\n  console.log(`  SMACNA Compliant: ${systemResult.complianceStatus.smacnaCompliant}`);\r\n\r\n  if (systemResult.systemWarnings.length > 0) {\r\n    console.log('\\nSystem Warnings:');\r\n    systemResult.systemWarnings.forEach(warning => console.log(`  - ${warning}`));\r\n  }\r\n\r\n  if (systemResult.systemRecommendations.length > 0) {\r\n    console.log('\\nSystem Recommendations:');\r\n    systemResult.systemRecommendations.forEach(rec => console.log(`  - ${rec}`));\r\n  }\r\n\r\n  return systemResult;\r\n}\r\n\r\n/**\r\n * Example 3: Complex System with Multiple Fittings\r\n */\r\nfunction demonstrateComplexSystem() {\r\n  console.log('\\n=== COMPLEX SYSTEM EXAMPLE ===');\r\n  console.log('System: Supply duct with multiple fittings and transitions');\r\n  \r\n  const complexSegments: DuctSegment[] = [\r\n    // Main trunk: 12\" round, 20 feet\r\n    {\r\n      id: 'main-trunk',\r\n      type: 'straight',\r\n      ductShape: 'round',\r\n      length: 20,\r\n      diameter: 12,\r\n      airflow: 2000,\r\n      material: 'galvanized_steel'\r\n    },\r\n    \r\n    // Tee for branch takeoff\r\n    {\r\n      id: 'main-tee',\r\n      type: 'fitting',\r\n      ductShape: 'round',\r\n      diameter: 12,\r\n      airflow: 2000,\r\n      material: 'galvanized_steel',\r\n      fittingConfig: {\r\n        type: 'tee_round_branch_90deg',\r\n        ductShape: 'round',\r\n        diameter: 12,\r\n        subtype: 'straight_through',\r\n        parameter: '0.6' // Branch area ratio\r\n      }\r\n    },\r\n    \r\n    // Transition from 12\" to 10\"\r\n    {\r\n      id: 'transition-12-to-10',\r\n      type: 'fitting',\r\n      ductShape: 'round',\r\n      diameter: 12,\r\n      airflow: 1500, // Reduced flow after branch\r\n      material: 'galvanized_steel',\r\n      fittingConfig: {\r\n        type: 'round_to_round_gradual',\r\n        ductShape: 'round',\r\n        diameter: 12,\r\n        parameter: '2.5' // L/D ratio\r\n      }\r\n    },\r\n    \r\n    // 10\" duct run\r\n    {\r\n      id: 'reduced-run',\r\n      type: 'straight',\r\n      ductShape: 'round',\r\n      length: 15,\r\n      diameter: 10,\r\n      airflow: 1500,\r\n      material: 'galvanized_steel'\r\n    },\r\n    \r\n    // 90\xB0 elbow\r\n    {\r\n      id: 'final-elbow',\r\n      type: 'fitting',\r\n      ductShape: 'round',\r\n      diameter: 10,\r\n      airflow: 1500,\r\n      material: 'galvanized_steel',\r\n      fittingConfig: {\r\n        type: '90deg_round_smooth',\r\n        ductShape: 'round',\r\n        diameter: 10,\r\n        parameter: '1.5'\r\n      }\r\n    },\r\n    \r\n    // Final run to diffuser\r\n    {\r\n      id: 'final-run',\r\n      type: 'straight',\r\n      ductShape: 'round',\r\n      length: 8,\r\n      diameter: 10,\r\n      airflow: 1500,\r\n      material: 'galvanized_steel'\r\n    }\r\n  ];\r\n\r\n  const complexInputs: SystemCalculationInputs = {\r\n    segments: complexSegments,\r\n    systemType: 'supply',\r\n    designConditions: {\r\n      temperature: 75,\r\n      barometricPressure: 29.92,\r\n      altitude: 0\r\n    },\r\n    calculationOptions: {\r\n      includeElevationEffects: false,\r\n      includeTemperatureEffects: true,\r\n      frictionMethod: 'darcy_weisbach',\r\n      roundingPrecision: 3\r\n    }\r\n  };\r\n\r\n  const complexResult = SystemPressureCalculator.calculateSystemPressure(complexInputs);\r\n\r\n  console.log('\\nComplex System Results:');\r\n  console.log(`  Total Pressure Loss: ${complexResult.totalPressureLoss} in wg`);\r\n  console.log(`  Friction vs Minor Loss Ratio: ${(complexResult.totalFrictionLoss / complexResult.totalMinorLoss).toFixed(2)}`);\r\n  console.log(`  System Length: ${complexResult.totalLength} feet`);\r\n  console.log(`  Velocity Range: ${complexResult.minVelocity} - ${complexResult.maxVelocity} FPM`);\r\n\r\n  return complexResult;\r\n}\r\n\r\n/**\r\n * Example 4: Comparison with Existing AirDuctCalculator\r\n */\r\nfunction demonstrateIntegrationWithExisting() {\r\n  console.log('\\n=== INTEGRATION WITH EXISTING CALCULATOR ===');\r\n  \r\n  // Use existing AirDuctCalculator for basic sizing\r\n  const basicResult = AirDuctCalculator.calculateDuctSizing({\r\n    airflow: 1000,\r\n    ductType: 'round',\r\n    frictionRate: 0.08,\r\n    units: 'imperial',\r\n    material: 'galvanized_steel'\r\n  });\r\n\r\n  console.log('Basic Duct Sizing (AirDuctCalculator):');\r\n  console.log(`  Recommended Diameter: ${basicResult.diameter}\" round`);\r\n  console.log(`  Velocity: ${basicResult.velocity} FPM`);\r\n  console.log(`  Pressure Loss: ${basicResult.pressureLoss} in wg/100 ft`);\r\n\r\n  // Now use new system calculator for complete analysis\r\n  const enhancedSegments: DuctSegment[] = [\r\n    {\r\n      id: 'sized-duct',\r\n      type: 'straight',\r\n      ductShape: 'round',\r\n      length: 100, // 100 feet for comparison\r\n      diameter: basicResult.diameter,\r\n      airflow: 1000,\r\n      material: 'galvanized_steel'\r\n    }\r\n  ];\r\n\r\n  const enhancedInputs: SystemCalculationInputs = {\r\n    segments: enhancedSegments,\r\n    systemType: 'supply',\r\n    designConditions: {\r\n      temperature: 70,\r\n      barometricPressure: 29.92,\r\n      altitude: 0\r\n    },\r\n    calculationOptions: {\r\n      includeElevationEffects: false,\r\n      includeTemperatureEffects: true,\r\n      frictionMethod: 'darcy_weisbach',\r\n      roundingPrecision: 4\r\n    }\r\n  };\r\n\r\n  const enhancedResult = SystemPressureCalculator.calculateSystemPressure(enhancedInputs);\r\n\r\n  console.log('\\nEnhanced System Analysis (SystemPressureCalculator):');\r\n  console.log(`  Calculated Velocity: ${enhancedResult.averageVelocity} FPM`);\r\n  console.log(`  Friction Loss (100 ft): ${enhancedResult.totalFrictionLoss} in wg`);\r\n  console.log(`  Pressure Loss Rate: ${(enhancedResult.totalFrictionLoss).toFixed(4)} in wg/100 ft`);\r\n\r\n  console.log('\\nComparison:');\r\n  console.log(`  Velocity Match: ${Math.abs(basicResult.velocity - enhancedResult.averageVelocity) < 1 ? 'PASS' : 'FAIL'}`);\r\n  console.log(`  Pressure Loss Match: ${Math.abs(basicResult.pressureLoss - enhancedResult.totalFrictionLoss) < 0.001 ? 'PASS' : 'FAIL'}`);\r\n}\r\n\r\n/**\r\n * Main execution function\r\n */\r\nfunction runAllExamples() {\r\n  console.log('COMPREHENSIVE DUCT PHYSICS IMPLEMENTATION - INTEGRATION EXAMPLES');\r\n  console.log('================================================================');\r\n  \r\n  try {\r\n    // Run all examples\r\n    demonstrateFittingLossCalculation();\r\n    demonstrateSystemPressureCalculation();\r\n    demonstrateComplexSystem();\r\n    demonstrateIntegrationWithExisting();\r\n    \r\n    console.log('\\n=== ALL EXAMPLES COMPLETED SUCCESSFULLY ===');\r\n    console.log('Phase 1 implementation is working correctly!');\r\n    \r\n  } catch (error) {\r\n    console.error('\\n=== ERROR IN EXAMPLES ===');\r\n    console.error(error);\r\n    process.exit(1);\r\n  }\r\n}\r\n\r\n// Export functions for testing\r\nexport {\r\n  demonstrateFittingLossCalculation,\r\n  demonstrateSystemPressureCalculation,\r\n  demonstrateComplexSystem,\r\n  demonstrateIntegrationWithExisting,\r\n  runAllExamples\r\n};\r\n\r\n// Run examples if this file is executed directly\r\nif (require.main === module) {\r\n  runAllExamples();\r\n}\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "32e0985b9193cd19b6c197b2ac6e7c8778809559"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_165opjsltb = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_165opjsltb();
cov_165opjsltb().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_165opjsltb().s[1]++;
exports.demonstrateFittingLossCalculation = demonstrateFittingLossCalculation;
/* istanbul ignore next */
cov_165opjsltb().s[2]++;
exports.demonstrateSystemPressureCalculation = demonstrateSystemPressureCalculation;
/* istanbul ignore next */
cov_165opjsltb().s[3]++;
exports.demonstrateComplexSystem = demonstrateComplexSystem;
/* istanbul ignore next */
cov_165opjsltb().s[4]++;
exports.demonstrateIntegrationWithExisting = demonstrateIntegrationWithExisting;
/* istanbul ignore next */
cov_165opjsltb().s[5]++;
exports.runAllExamples = runAllExamples;
const FittingLossCalculator_1 =
/* istanbul ignore next */
(cov_165opjsltb().s[6]++, require("../FittingLossCalculator"));
const SystemPressureCalculator_1 =
/* istanbul ignore next */
(cov_165opjsltb().s[7]++, require("../SystemPressureCalculator"));
const AirDuctCalculator_1 =
/* istanbul ignore next */
(cov_165opjsltb().s[8]++, require("../AirDuctCalculator"));
/**
 * Example 1: Individual Fitting Loss Calculation
 */
function demonstrateFittingLossCalculation() {
  /* istanbul ignore next */
  cov_165opjsltb().f[0]++;
  cov_165opjsltb().s[9]++;
  console.log('\n=== FITTING LOSS CALCULATION EXAMPLE ===');
  // Example: 90° smooth elbow with R/D = 1.5
  const elbowConfig =
  /* istanbul ignore next */
  (cov_165opjsltb().s[10]++, {
    type: '90deg_round_smooth',
    ductShape: 'round',
    diameter: 10,
    parameter: '1.5'
  });
  const velocity =
  /* istanbul ignore next */
  (cov_165opjsltb().s[11]++, 1833); // FPM (calculated from 1000 CFM in 10" duct)
  const result =
  /* istanbul ignore next */
  (cov_165opjsltb().s[12]++, FittingLossCalculator_1.FittingLossCalculator.calculateFittingLoss(elbowConfig, velocity));
  /* istanbul ignore next */
  cov_165opjsltb().s[13]++;
  console.log('90° Smooth Elbow (R/D = 1.5):');
  /* istanbul ignore next */
  cov_165opjsltb().s[14]++;
  console.log(`  K-factor: ${result.kFactor}`);
  /* istanbul ignore next */
  cov_165opjsltb().s[15]++;
  console.log(`  Velocity Pressure: ${result.velocityPressure.toFixed(4)} in wg`);
  /* istanbul ignore next */
  cov_165opjsltb().s[16]++;
  console.log(`  Pressure Loss: ${result.pressureLoss.toFixed(4)} in wg`);
  /* istanbul ignore next */
  cov_165opjsltb().s[17]++;
  console.log(`  Configuration: ${result.configuration}`);
  /* istanbul ignore next */
  cov_165opjsltb().s[18]++;
  if (result.warnings.length > 0) {
    /* istanbul ignore next */
    cov_165opjsltb().b[0][0]++;
    cov_165opjsltb().s[19]++;
    console.log(`  Warnings: ${result.warnings.join(', ')}`);
  } else
  /* istanbul ignore next */
  {
    cov_165opjsltb().b[0][1]++;
  }
  cov_165opjsltb().s[20]++;
  if (result.recommendations.length > 0) {
    /* istanbul ignore next */
    cov_165opjsltb().b[1][0]++;
    cov_165opjsltb().s[21]++;
    console.log(`  Recommendations: ${result.recommendations.join(', ')}`);
  } else
  /* istanbul ignore next */
  {
    cov_165opjsltb().b[1][1]++;
  }
  cov_165opjsltb().s[22]++;
  return result;
}
/**
 * Example 2: Complete System Pressure Drop Calculation
 * User's example: 10″ round duct → 10′ run → 90° elbow → 10′ run
 */
function demonstrateSystemPressureCalculation() {
  /* istanbul ignore next */
  cov_165opjsltb().f[1]++;
  cov_165opjsltb().s[23]++;
  console.log('\n=== SYSTEM PRESSURE DROP CALCULATION EXAMPLE ===');
  /* istanbul ignore next */
  cov_165opjsltb().s[24]++;
  console.log('System: 10″ round duct → 10′ run → 90° elbow → 10′ run');
  // Define system segments
  const segments =
  /* istanbul ignore next */
  (cov_165opjsltb().s[25]++, [
  // First straight run: 10 feet
  {
    id: 'straight-run-1',
    type: 'straight',
    ductShape: 'round',
    length: 10,
    diameter: 10,
    airflow: 1000,
    material: 'galvanized_steel',
    notes: 'First straight section'
  },
  // 90° elbow with R/D = 1.5
  {
    id: 'elbow-90deg',
    type: 'fitting',
    ductShape: 'round',
    diameter: 10,
    airflow: 1000,
    material: 'galvanized_steel',
    fittingConfig: {
      type: '90deg_round_smooth',
      ductShape: 'round',
      diameter: 10,
      parameter: '1.5'
    },
    notes: '90° smooth elbow'
  },
  // Second straight run: 10 feet
  {
    id: 'straight-run-2',
    type: 'straight',
    ductShape: 'round',
    length: 10,
    diameter: 10,
    airflow: 1000,
    material: 'galvanized_steel',
    notes: 'Second straight section'
  }]);
  // System calculation inputs
  const inputs =
  /* istanbul ignore next */
  (cov_165opjsltb().s[26]++, {
    segments,
    systemType: 'supply',
    designConditions: {
      temperature: 70,
      // °F
      barometricPressure: 29.92,
      // in Hg
      altitude: 0 // feet above sea level
    },
    calculationOptions: {
      includeElevationEffects: false,
      includeTemperatureEffects: true,
      frictionMethod: 'darcy_weisbach',
      roundingPrecision: 4
    }
  });
  // Calculate system pressure drop
  const systemResult =
  /* istanbul ignore next */
  (cov_165opjsltb().s[27]++, SystemPressureCalculator_1.SystemPressureCalculator.calculateSystemPressure(inputs));
  /* istanbul ignore next */
  cov_165opjsltb().s[28]++;
  console.log('\nSystem Results:');
  /* istanbul ignore next */
  cov_165opjsltb().s[29]++;
  console.log(`  Total Pressure Loss: ${systemResult.totalPressureLoss} in wg`);
  /* istanbul ignore next */
  cov_165opjsltb().s[30]++;
  console.log(`  Total Friction Loss: ${systemResult.totalFrictionLoss} in wg`);
  /* istanbul ignore next */
  cov_165opjsltb().s[31]++;
  console.log(`  Total Minor Loss: ${systemResult.totalMinorLoss} in wg`);
  /* istanbul ignore next */
  cov_165opjsltb().s[32]++;
  console.log(`  Total Length: ${systemResult.totalLength} feet`);
  /* istanbul ignore next */
  cov_165opjsltb().s[33]++;
  console.log(`  Average Velocity: ${systemResult.averageVelocity} FPM`);
  /* istanbul ignore next */
  cov_165opjsltb().s[34]++;
  console.log(`  Max Velocity: ${systemResult.maxVelocity} FPM`);
  /* istanbul ignore next */
  cov_165opjsltb().s[35]++;
  console.log('\nSegment Details:');
  /* istanbul ignore next */
  cov_165opjsltb().s[36]++;
  systemResult.segmentResults.forEach((segment, index) => {
    /* istanbul ignore next */
    cov_165opjsltb().f[2]++;
    cov_165opjsltb().s[37]++;
    console.log(`  Segment ${index + 1} (${segment.segmentId}):`);
    /* istanbul ignore next */
    cov_165opjsltb().s[38]++;
    console.log(`    Type: ${segment.segmentType}`);
    /* istanbul ignore next */
    cov_165opjsltb().s[39]++;
    console.log(`    Velocity: ${segment.velocity} FPM`);
    /* istanbul ignore next */
    cov_165opjsltb().s[40]++;
    console.log(`    Velocity Pressure: ${segment.velocityPressure} in wg`);
    /* istanbul ignore next */
    cov_165opjsltb().s[41]++;
    console.log(`    Friction Loss: ${segment.frictionLoss} in wg`);
    /* istanbul ignore next */
    cov_165opjsltb().s[42]++;
    console.log(`    Minor Loss: ${segment.minorLoss} in wg`);
    /* istanbul ignore next */
    cov_165opjsltb().s[43]++;
    console.log(`    Total Loss: ${segment.totalLoss} in wg`);
    /* istanbul ignore next */
    cov_165opjsltb().s[44]++;
    if (segment.kFactor) {
      /* istanbul ignore next */
      cov_165opjsltb().b[2][0]++;
      cov_165opjsltb().s[45]++;
      console.log(`    K-factor: ${segment.kFactor}`);
    } else
    /* istanbul ignore next */
    {
      cov_165opjsltb().b[2][1]++;
    }
    cov_165opjsltb().s[46]++;
    if (segment.warnings.length > 0) {
      /* istanbul ignore next */
      cov_165opjsltb().b[3][0]++;
      cov_165opjsltb().s[47]++;
      console.log(`    Warnings: ${segment.warnings.join(', ')}`);
    } else
    /* istanbul ignore next */
    {
      cov_165opjsltb().b[3][1]++;
    }
  });
  /* istanbul ignore next */
  cov_165opjsltb().s[48]++;
  console.log('\nCompliance Status:');
  /* istanbul ignore next */
  cov_165opjsltb().s[49]++;
  console.log(`  Velocity Compliant: ${systemResult.complianceStatus.velocityCompliant}`);
  /* istanbul ignore next */
  cov_165opjsltb().s[50]++;
  console.log(`  Pressure Compliant: ${systemResult.complianceStatus.pressureCompliant}`);
  /* istanbul ignore next */
  cov_165opjsltb().s[51]++;
  console.log(`  SMACNA Compliant: ${systemResult.complianceStatus.smacnaCompliant}`);
  /* istanbul ignore next */
  cov_165opjsltb().s[52]++;
  if (systemResult.systemWarnings.length > 0) {
    /* istanbul ignore next */
    cov_165opjsltb().b[4][0]++;
    cov_165opjsltb().s[53]++;
    console.log('\nSystem Warnings:');
    /* istanbul ignore next */
    cov_165opjsltb().s[54]++;
    systemResult.systemWarnings.forEach(warning => {
      /* istanbul ignore next */
      cov_165opjsltb().f[3]++;
      cov_165opjsltb().s[55]++;
      return console.log(`  - ${warning}`);
    });
  } else
  /* istanbul ignore next */
  {
    cov_165opjsltb().b[4][1]++;
  }
  cov_165opjsltb().s[56]++;
  if (systemResult.systemRecommendations.length > 0) {
    /* istanbul ignore next */
    cov_165opjsltb().b[5][0]++;
    cov_165opjsltb().s[57]++;
    console.log('\nSystem Recommendations:');
    /* istanbul ignore next */
    cov_165opjsltb().s[58]++;
    systemResult.systemRecommendations.forEach(rec => {
      /* istanbul ignore next */
      cov_165opjsltb().f[4]++;
      cov_165opjsltb().s[59]++;
      return console.log(`  - ${rec}`);
    });
  } else
  /* istanbul ignore next */
  {
    cov_165opjsltb().b[5][1]++;
  }
  cov_165opjsltb().s[60]++;
  return systemResult;
}
/**
 * Example 3: Complex System with Multiple Fittings
 */
function demonstrateComplexSystem() {
  /* istanbul ignore next */
  cov_165opjsltb().f[5]++;
  cov_165opjsltb().s[61]++;
  console.log('\n=== COMPLEX SYSTEM EXAMPLE ===');
  /* istanbul ignore next */
  cov_165opjsltb().s[62]++;
  console.log('System: Supply duct with multiple fittings and transitions');
  const complexSegments =
  /* istanbul ignore next */
  (cov_165opjsltb().s[63]++, [
  // Main trunk: 12" round, 20 feet
  {
    id: 'main-trunk',
    type: 'straight',
    ductShape: 'round',
    length: 20,
    diameter: 12,
    airflow: 2000,
    material: 'galvanized_steel'
  },
  // Tee for branch takeoff
  {
    id: 'main-tee',
    type: 'fitting',
    ductShape: 'round',
    diameter: 12,
    airflow: 2000,
    material: 'galvanized_steel',
    fittingConfig: {
      type: 'tee_round_branch_90deg',
      ductShape: 'round',
      diameter: 12,
      subtype: 'straight_through',
      parameter: '0.6' // Branch area ratio
    }
  },
  // Transition from 12" to 10"
  {
    id: 'transition-12-to-10',
    type: 'fitting',
    ductShape: 'round',
    diameter: 12,
    airflow: 1500,
    // Reduced flow after branch
    material: 'galvanized_steel',
    fittingConfig: {
      type: 'round_to_round_gradual',
      ductShape: 'round',
      diameter: 12,
      parameter: '2.5' // L/D ratio
    }
  },
  // 10" duct run
  {
    id: 'reduced-run',
    type: 'straight',
    ductShape: 'round',
    length: 15,
    diameter: 10,
    airflow: 1500,
    material: 'galvanized_steel'
  },
  // 90° elbow
  {
    id: 'final-elbow',
    type: 'fitting',
    ductShape: 'round',
    diameter: 10,
    airflow: 1500,
    material: 'galvanized_steel',
    fittingConfig: {
      type: '90deg_round_smooth',
      ductShape: 'round',
      diameter: 10,
      parameter: '1.5'
    }
  },
  // Final run to diffuser
  {
    id: 'final-run',
    type: 'straight',
    ductShape: 'round',
    length: 8,
    diameter: 10,
    airflow: 1500,
    material: 'galvanized_steel'
  }]);
  const complexInputs =
  /* istanbul ignore next */
  (cov_165opjsltb().s[64]++, {
    segments: complexSegments,
    systemType: 'supply',
    designConditions: {
      temperature: 75,
      barometricPressure: 29.92,
      altitude: 0
    },
    calculationOptions: {
      includeElevationEffects: false,
      includeTemperatureEffects: true,
      frictionMethod: 'darcy_weisbach',
      roundingPrecision: 3
    }
  });
  const complexResult =
  /* istanbul ignore next */
  (cov_165opjsltb().s[65]++, SystemPressureCalculator_1.SystemPressureCalculator.calculateSystemPressure(complexInputs));
  /* istanbul ignore next */
  cov_165opjsltb().s[66]++;
  console.log('\nComplex System Results:');
  /* istanbul ignore next */
  cov_165opjsltb().s[67]++;
  console.log(`  Total Pressure Loss: ${complexResult.totalPressureLoss} in wg`);
  /* istanbul ignore next */
  cov_165opjsltb().s[68]++;
  console.log(`  Friction vs Minor Loss Ratio: ${(complexResult.totalFrictionLoss / complexResult.totalMinorLoss).toFixed(2)}`);
  /* istanbul ignore next */
  cov_165opjsltb().s[69]++;
  console.log(`  System Length: ${complexResult.totalLength} feet`);
  /* istanbul ignore next */
  cov_165opjsltb().s[70]++;
  console.log(`  Velocity Range: ${complexResult.minVelocity} - ${complexResult.maxVelocity} FPM`);
  /* istanbul ignore next */
  cov_165opjsltb().s[71]++;
  return complexResult;
}
/**
 * Example 4: Comparison with Existing AirDuctCalculator
 */
function demonstrateIntegrationWithExisting() {
  /* istanbul ignore next */
  cov_165opjsltb().f[6]++;
  cov_165opjsltb().s[72]++;
  console.log('\n=== INTEGRATION WITH EXISTING CALCULATOR ===');
  // Use existing AirDuctCalculator for basic sizing
  const basicResult =
  /* istanbul ignore next */
  (cov_165opjsltb().s[73]++, AirDuctCalculator_1.AirDuctCalculator.calculateDuctSizing({
    airflow: 1000,
    ductType: 'round',
    frictionRate: 0.08,
    units: 'imperial',
    material: 'galvanized_steel'
  }));
  /* istanbul ignore next */
  cov_165opjsltb().s[74]++;
  console.log('Basic Duct Sizing (AirDuctCalculator):');
  /* istanbul ignore next */
  cov_165opjsltb().s[75]++;
  console.log(`  Recommended Diameter: ${basicResult.diameter}" round`);
  /* istanbul ignore next */
  cov_165opjsltb().s[76]++;
  console.log(`  Velocity: ${basicResult.velocity} FPM`);
  /* istanbul ignore next */
  cov_165opjsltb().s[77]++;
  console.log(`  Pressure Loss: ${basicResult.pressureLoss} in wg/100 ft`);
  // Now use new system calculator for complete analysis
  const enhancedSegments =
  /* istanbul ignore next */
  (cov_165opjsltb().s[78]++, [{
    id: 'sized-duct',
    type: 'straight',
    ductShape: 'round',
    length: 100,
    // 100 feet for comparison
    diameter: basicResult.diameter,
    airflow: 1000,
    material: 'galvanized_steel'
  }]);
  const enhancedInputs =
  /* istanbul ignore next */
  (cov_165opjsltb().s[79]++, {
    segments: enhancedSegments,
    systemType: 'supply',
    designConditions: {
      temperature: 70,
      barometricPressure: 29.92,
      altitude: 0
    },
    calculationOptions: {
      includeElevationEffects: false,
      includeTemperatureEffects: true,
      frictionMethod: 'darcy_weisbach',
      roundingPrecision: 4
    }
  });
  const enhancedResult =
  /* istanbul ignore next */
  (cov_165opjsltb().s[80]++, SystemPressureCalculator_1.SystemPressureCalculator.calculateSystemPressure(enhancedInputs));
  /* istanbul ignore next */
  cov_165opjsltb().s[81]++;
  console.log('\nEnhanced System Analysis (SystemPressureCalculator):');
  /* istanbul ignore next */
  cov_165opjsltb().s[82]++;
  console.log(`  Calculated Velocity: ${enhancedResult.averageVelocity} FPM`);
  /* istanbul ignore next */
  cov_165opjsltb().s[83]++;
  console.log(`  Friction Loss (100 ft): ${enhancedResult.totalFrictionLoss} in wg`);
  /* istanbul ignore next */
  cov_165opjsltb().s[84]++;
  console.log(`  Pressure Loss Rate: ${enhancedResult.totalFrictionLoss.toFixed(4)} in wg/100 ft`);
  /* istanbul ignore next */
  cov_165opjsltb().s[85]++;
  console.log('\nComparison:');
  /* istanbul ignore next */
  cov_165opjsltb().s[86]++;
  console.log(`  Velocity Match: ${Math.abs(basicResult.velocity - enhancedResult.averageVelocity) < 1 ?
  /* istanbul ignore next */
  (cov_165opjsltb().b[6][0]++, 'PASS') :
  /* istanbul ignore next */
  (cov_165opjsltb().b[6][1]++, 'FAIL')}`);
  /* istanbul ignore next */
  cov_165opjsltb().s[87]++;
  console.log(`  Pressure Loss Match: ${Math.abs(basicResult.pressureLoss - enhancedResult.totalFrictionLoss) < 0.001 ?
  /* istanbul ignore next */
  (cov_165opjsltb().b[7][0]++, 'PASS') :
  /* istanbul ignore next */
  (cov_165opjsltb().b[7][1]++, 'FAIL')}`);
}
/**
 * Main execution function
 */
function runAllExamples() {
  /* istanbul ignore next */
  cov_165opjsltb().f[7]++;
  cov_165opjsltb().s[88]++;
  console.log('COMPREHENSIVE DUCT PHYSICS IMPLEMENTATION - INTEGRATION EXAMPLES');
  /* istanbul ignore next */
  cov_165opjsltb().s[89]++;
  console.log('================================================================');
  /* istanbul ignore next */
  cov_165opjsltb().s[90]++;
  try {
    /* istanbul ignore next */
    cov_165opjsltb().s[91]++;
    // Run all examples
    demonstrateFittingLossCalculation();
    /* istanbul ignore next */
    cov_165opjsltb().s[92]++;
    demonstrateSystemPressureCalculation();
    /* istanbul ignore next */
    cov_165opjsltb().s[93]++;
    demonstrateComplexSystem();
    /* istanbul ignore next */
    cov_165opjsltb().s[94]++;
    demonstrateIntegrationWithExisting();
    /* istanbul ignore next */
    cov_165opjsltb().s[95]++;
    console.log('\n=== ALL EXAMPLES COMPLETED SUCCESSFULLY ===');
    /* istanbul ignore next */
    cov_165opjsltb().s[96]++;
    console.log('Phase 1 implementation is working correctly!');
  } catch (error) {
    /* istanbul ignore next */
    cov_165opjsltb().s[97]++;
    console.error('\n=== ERROR IN EXAMPLES ===');
    /* istanbul ignore next */
    cov_165opjsltb().s[98]++;
    console.error(error);
    /* istanbul ignore next */
    cov_165opjsltb().s[99]++;
    process.exit(1);
  }
}
// Run examples if this file is executed directly
/* istanbul ignore next */
cov_165opjsltb().s[100]++;
if (require.main === module) {
  /* istanbul ignore next */
  cov_165opjsltb().b[8][0]++;
  cov_165opjsltb().s[101]++;
  runAllExamples();
} else
/* istanbul ignore next */
{
  cov_165opjsltb().b[8][1]++;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJjb3ZfMTY1b3Bqc2x0YiIsInBhdGgiLCJoYXNoIiwiZ2xvYmFsIiwiRnVuY3Rpb24iLCJnY3YiLCJjb3ZlcmFnZURhdGEiLCJzdGF0ZW1lbnRNYXAiLCJzdGFydCIsImxpbmUiLCJjb2x1bW4iLCJlbmQiLCJmbk1hcCIsIm5hbWUiLCJkZWNsIiwibG9jIiwiYnJhbmNoTWFwIiwidHlwZSIsImxvY2F0aW9ucyIsInVuZGVmaW5lZCIsInMiLCJmIiwiYiIsImlucHV0U291cmNlTWFwIiwiZmlsZSIsIm1hcHBpbmdzIiwibmFtZXMiLCJzb3VyY2VzIiwic291cmNlc0NvbnRlbnQiLCJ2ZXJzaW9uIiwiX2NvdmVyYWdlU2NoZW1hIiwiY292ZXJhZ2UiLCJhY3R1YWxDb3ZlcmFnZSIsImV4cG9ydHMiLCJkZW1vbnN0cmF0ZUZpdHRpbmdMb3NzQ2FsY3VsYXRpb24iLCJkZW1vbnN0cmF0ZVN5c3RlbVByZXNzdXJlQ2FsY3VsYXRpb24iLCJkZW1vbnN0cmF0ZUNvbXBsZXhTeXN0ZW0iLCJkZW1vbnN0cmF0ZUludGVncmF0aW9uV2l0aEV4aXN0aW5nIiwicnVuQWxsRXhhbXBsZXMiLCJGaXR0aW5nTG9zc0NhbGN1bGF0b3JfMSIsInJlcXVpcmUiLCJTeXN0ZW1QcmVzc3VyZUNhbGN1bGF0b3JfMSIsIkFpckR1Y3RDYWxjdWxhdG9yXzEiLCJjb25zb2xlIiwibG9nIiwiZWxib3dDb25maWciLCJkdWN0U2hhcGUiLCJkaWFtZXRlciIsInBhcmFtZXRlciIsInZlbG9jaXR5IiwicmVzdWx0IiwiRml0dGluZ0xvc3NDYWxjdWxhdG9yIiwiY2FsY3VsYXRlRml0dGluZ0xvc3MiLCJrRmFjdG9yIiwidmVsb2NpdHlQcmVzc3VyZSIsInRvRml4ZWQiLCJwcmVzc3VyZUxvc3MiLCJjb25maWd1cmF0aW9uIiwid2FybmluZ3MiLCJsZW5ndGgiLCJqb2luIiwicmVjb21tZW5kYXRpb25zIiwic2VnbWVudHMiLCJpZCIsImFpcmZsb3ciLCJtYXRlcmlhbCIsIm5vdGVzIiwiZml0dGluZ0NvbmZpZyIsImlucHV0cyIsInN5c3RlbVR5cGUiLCJkZXNpZ25Db25kaXRpb25zIiwidGVtcGVyYXR1cmUiLCJiYXJvbWV0cmljUHJlc3N1cmUiLCJhbHRpdHVkZSIsImNhbGN1bGF0aW9uT3B0aW9ucyIsImluY2x1ZGVFbGV2YXRpb25FZmZlY3RzIiwiaW5jbHVkZVRlbXBlcmF0dXJlRWZmZWN0cyIsImZyaWN0aW9uTWV0aG9kIiwicm91bmRpbmdQcmVjaXNpb24iLCJzeXN0ZW1SZXN1bHQiLCJTeXN0ZW1QcmVzc3VyZUNhbGN1bGF0b3IiLCJjYWxjdWxhdGVTeXN0ZW1QcmVzc3VyZSIsInRvdGFsUHJlc3N1cmVMb3NzIiwidG90YWxGcmljdGlvbkxvc3MiLCJ0b3RhbE1pbm9yTG9zcyIsInRvdGFsTGVuZ3RoIiwiYXZlcmFnZVZlbG9jaXR5IiwibWF4VmVsb2NpdHkiLCJzZWdtZW50UmVzdWx0cyIsImZvckVhY2giLCJzZWdtZW50IiwiaW5kZXgiLCJzZWdtZW50SWQiLCJzZWdtZW50VHlwZSIsImZyaWN0aW9uTG9zcyIsIm1pbm9yTG9zcyIsInRvdGFsTG9zcyIsImNvbXBsaWFuY2VTdGF0dXMiLCJ2ZWxvY2l0eUNvbXBsaWFudCIsInByZXNzdXJlQ29tcGxpYW50Iiwic21hY25hQ29tcGxpYW50Iiwic3lzdGVtV2FybmluZ3MiLCJ3YXJuaW5nIiwic3lzdGVtUmVjb21tZW5kYXRpb25zIiwicmVjIiwiY29tcGxleFNlZ21lbnRzIiwic3VidHlwZSIsImNvbXBsZXhJbnB1dHMiLCJjb21wbGV4UmVzdWx0IiwibWluVmVsb2NpdHkiLCJiYXNpY1Jlc3VsdCIsIkFpckR1Y3RDYWxjdWxhdG9yIiwiY2FsY3VsYXRlRHVjdFNpemluZyIsImR1Y3RUeXBlIiwiZnJpY3Rpb25SYXRlIiwidW5pdHMiLCJlbmhhbmNlZFNlZ21lbnRzIiwiZW5oYW5jZWRJbnB1dHMiLCJlbmhhbmNlZFJlc3VsdCIsIk1hdGgiLCJhYnMiLCJlcnJvciIsInByb2Nlc3MiLCJleGl0IiwibWFpbiIsIm1vZHVsZSJdLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcam9obnJcXERvd25sb2Fkc1xcU2l6ZVdpc2VfU3VpdGVfQXBwXFxiYWNrZW5kXFxzZXJ2aWNlc1xcY2FsY3VsYXRpb25zXFxleGFtcGxlc1xcZHVjdC1waHlzaWNzLWludGVncmF0aW9uLWV4YW1wbGUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXHJcbiAqIER1Y3QgUGh5c2ljcyBJbnRlZ3JhdGlvbiBFeGFtcGxlXHJcbiAqIFxyXG4gKiBEZW1vbnN0cmF0ZXMgdGhlIGNvbXByZWhlbnNpdmUgZHVjdCBwaHlzaWNzIGltcGxlbWVudGF0aW9uXHJcbiAqIFNob3dzIGhvdyB0byB1c2UgRml0dGluZ0xvc3NDYWxjdWxhdG9yIGFuZCBTeXN0ZW1QcmVzc3VyZUNhbGN1bGF0b3JcclxuICogXHJcbiAqIEV4YW1wbGU6IDEw4oCzIHJvdW5kIGR1Y3Qg4oaSIDEw4oCyIHJ1biDihpIgOTDCsCBlbGJvdyDihpIgMTDigLIgcnVuXHJcbiAqL1xyXG5cclxuaW1wb3J0IHsgRml0dGluZ0xvc3NDYWxjdWxhdG9yLCBGaXR0aW5nQ29uZmlndXJhdGlvbiB9IGZyb20gJy4uL0ZpdHRpbmdMb3NzQ2FsY3VsYXRvcic7XHJcbmltcG9ydCB7IFN5c3RlbVByZXNzdXJlQ2FsY3VsYXRvciwgRHVjdFNlZ21lbnQsIFN5c3RlbUNhbGN1bGF0aW9uSW5wdXRzIH0gZnJvbSAnLi4vU3lzdGVtUHJlc3N1cmVDYWxjdWxhdG9yJztcclxuaW1wb3J0IHsgQWlyRHVjdENhbGN1bGF0b3IgfSBmcm9tICcuLi9BaXJEdWN0Q2FsY3VsYXRvcic7XHJcblxyXG4vKipcclxuICogRXhhbXBsZSAxOiBJbmRpdmlkdWFsIEZpdHRpbmcgTG9zcyBDYWxjdWxhdGlvblxyXG4gKi9cclxuZnVuY3Rpb24gZGVtb25zdHJhdGVGaXR0aW5nTG9zc0NhbGN1bGF0aW9uKCkge1xyXG4gIGNvbnNvbGUubG9nKCdcXG49PT0gRklUVElORyBMT1NTIENBTENVTEFUSU9OIEVYQU1QTEUgPT09Jyk7XHJcbiAgXHJcbiAgLy8gRXhhbXBsZTogOTDCsCBzbW9vdGggZWxib3cgd2l0aCBSL0QgPSAxLjVcclxuICBjb25zdCBlbGJvd0NvbmZpZzogRml0dGluZ0NvbmZpZ3VyYXRpb24gPSB7XHJcbiAgICB0eXBlOiAnOTBkZWdfcm91bmRfc21vb3RoJyxcclxuICAgIGR1Y3RTaGFwZTogJ3JvdW5kJyxcclxuICAgIGRpYW1ldGVyOiAxMCxcclxuICAgIHBhcmFtZXRlcjogJzEuNSdcclxuICB9O1xyXG5cclxuICBjb25zdCB2ZWxvY2l0eSA9IDE4MzM7IC8vIEZQTSAoY2FsY3VsYXRlZCBmcm9tIDEwMDAgQ0ZNIGluIDEwXCIgZHVjdClcclxuICBjb25zdCByZXN1bHQgPSBGaXR0aW5nTG9zc0NhbGN1bGF0b3IuY2FsY3VsYXRlRml0dGluZ0xvc3MoZWxib3dDb25maWcsIHZlbG9jaXR5KTtcclxuXHJcbiAgY29uc29sZS5sb2coJzkwwrAgU21vb3RoIEVsYm93IChSL0QgPSAxLjUpOicpO1xyXG4gIGNvbnNvbGUubG9nKGAgIEstZmFjdG9yOiAke3Jlc3VsdC5rRmFjdG9yfWApO1xyXG4gIGNvbnNvbGUubG9nKGAgIFZlbG9jaXR5IFByZXNzdXJlOiAke3Jlc3VsdC52ZWxvY2l0eVByZXNzdXJlLnRvRml4ZWQoNCl9IGluIHdnYCk7XHJcbiAgY29uc29sZS5sb2coYCAgUHJlc3N1cmUgTG9zczogJHtyZXN1bHQucHJlc3N1cmVMb3NzLnRvRml4ZWQoNCl9IGluIHdnYCk7XHJcbiAgY29uc29sZS5sb2coYCAgQ29uZmlndXJhdGlvbjogJHtyZXN1bHQuY29uZmlndXJhdGlvbn1gKTtcclxuICBcclxuICBpZiAocmVzdWx0Lndhcm5pbmdzLmxlbmd0aCA+IDApIHtcclxuICAgIGNvbnNvbGUubG9nKGAgIFdhcm5pbmdzOiAke3Jlc3VsdC53YXJuaW5ncy5qb2luKCcsICcpfWApO1xyXG4gIH1cclxuICBcclxuICBpZiAocmVzdWx0LnJlY29tbWVuZGF0aW9ucy5sZW5ndGggPiAwKSB7XHJcbiAgICBjb25zb2xlLmxvZyhgICBSZWNvbW1lbmRhdGlvbnM6ICR7cmVzdWx0LnJlY29tbWVuZGF0aW9ucy5qb2luKCcsICcpfWApO1xyXG4gIH1cclxuXHJcbiAgcmV0dXJuIHJlc3VsdDtcclxufVxyXG5cclxuLyoqXHJcbiAqIEV4YW1wbGUgMjogQ29tcGxldGUgU3lzdGVtIFByZXNzdXJlIERyb3AgQ2FsY3VsYXRpb25cclxuICogVXNlcidzIGV4YW1wbGU6IDEw4oCzIHJvdW5kIGR1Y3Qg4oaSIDEw4oCyIHJ1biDihpIgOTDCsCBlbGJvdyDihpIgMTDigLIgcnVuXHJcbiAqL1xyXG5mdW5jdGlvbiBkZW1vbnN0cmF0ZVN5c3RlbVByZXNzdXJlQ2FsY3VsYXRpb24oKSB7XHJcbiAgY29uc29sZS5sb2coJ1xcbj09PSBTWVNURU0gUFJFU1NVUkUgRFJPUCBDQUxDVUxBVElPTiBFWEFNUExFID09PScpO1xyXG4gIGNvbnNvbGUubG9nKCdTeXN0ZW06IDEw4oCzIHJvdW5kIGR1Y3Qg4oaSIDEw4oCyIHJ1biDihpIgOTDCsCBlbGJvdyDihpIgMTDigLIgcnVuJyk7XHJcbiAgXHJcbiAgLy8gRGVmaW5lIHN5c3RlbSBzZWdtZW50c1xyXG4gIGNvbnN0IHNlZ21lbnRzOiBEdWN0U2VnbWVudFtdID0gW1xyXG4gICAgLy8gRmlyc3Qgc3RyYWlnaHQgcnVuOiAxMCBmZWV0XHJcbiAgICB7XHJcbiAgICAgIGlkOiAnc3RyYWlnaHQtcnVuLTEnLFxyXG4gICAgICB0eXBlOiAnc3RyYWlnaHQnLFxyXG4gICAgICBkdWN0U2hhcGU6ICdyb3VuZCcsXHJcbiAgICAgIGxlbmd0aDogMTAsXHJcbiAgICAgIGRpYW1ldGVyOiAxMCxcclxuICAgICAgYWlyZmxvdzogMTAwMCxcclxuICAgICAgbWF0ZXJpYWw6ICdnYWx2YW5pemVkX3N0ZWVsJyxcclxuICAgICAgbm90ZXM6ICdGaXJzdCBzdHJhaWdodCBzZWN0aW9uJ1xyXG4gICAgfSxcclxuICAgIFxyXG4gICAgLy8gOTDCsCBlbGJvdyB3aXRoIFIvRCA9IDEuNVxyXG4gICAge1xyXG4gICAgICBpZDogJ2VsYm93LTkwZGVnJyxcclxuICAgICAgdHlwZTogJ2ZpdHRpbmcnLFxyXG4gICAgICBkdWN0U2hhcGU6ICdyb3VuZCcsXHJcbiAgICAgIGRpYW1ldGVyOiAxMCxcclxuICAgICAgYWlyZmxvdzogMTAwMCxcclxuICAgICAgbWF0ZXJpYWw6ICdnYWx2YW5pemVkX3N0ZWVsJyxcclxuICAgICAgZml0dGluZ0NvbmZpZzoge1xyXG4gICAgICAgIHR5cGU6ICc5MGRlZ19yb3VuZF9zbW9vdGgnLFxyXG4gICAgICAgIGR1Y3RTaGFwZTogJ3JvdW5kJyxcclxuICAgICAgICBkaWFtZXRlcjogMTAsXHJcbiAgICAgICAgcGFyYW1ldGVyOiAnMS41J1xyXG4gICAgICB9LFxyXG4gICAgICBub3RlczogJzkwwrAgc21vb3RoIGVsYm93J1xyXG4gICAgfSxcclxuICAgIFxyXG4gICAgLy8gU2Vjb25kIHN0cmFpZ2h0IHJ1bjogMTAgZmVldFxyXG4gICAge1xyXG4gICAgICBpZDogJ3N0cmFpZ2h0LXJ1bi0yJyxcclxuICAgICAgdHlwZTogJ3N0cmFpZ2h0JyxcclxuICAgICAgZHVjdFNoYXBlOiAncm91bmQnLFxyXG4gICAgICBsZW5ndGg6IDEwLFxyXG4gICAgICBkaWFtZXRlcjogMTAsXHJcbiAgICAgIGFpcmZsb3c6IDEwMDAsXHJcbiAgICAgIG1hdGVyaWFsOiAnZ2FsdmFuaXplZF9zdGVlbCcsXHJcbiAgICAgIG5vdGVzOiAnU2Vjb25kIHN0cmFpZ2h0IHNlY3Rpb24nXHJcbiAgICB9XHJcbiAgXTtcclxuXHJcbiAgLy8gU3lzdGVtIGNhbGN1bGF0aW9uIGlucHV0c1xyXG4gIGNvbnN0IGlucHV0czogU3lzdGVtQ2FsY3VsYXRpb25JbnB1dHMgPSB7XHJcbiAgICBzZWdtZW50cyxcclxuICAgIHN5c3RlbVR5cGU6ICdzdXBwbHknLFxyXG4gICAgZGVzaWduQ29uZGl0aW9uczoge1xyXG4gICAgICB0ZW1wZXJhdHVyZTogNzAsIC8vIMKwRlxyXG4gICAgICBiYXJvbWV0cmljUHJlc3N1cmU6IDI5LjkyLCAvLyBpbiBIZ1xyXG4gICAgICBhbHRpdHVkZTogMCAvLyBmZWV0IGFib3ZlIHNlYSBsZXZlbFxyXG4gICAgfSxcclxuICAgIGNhbGN1bGF0aW9uT3B0aW9uczoge1xyXG4gICAgICBpbmNsdWRlRWxldmF0aW9uRWZmZWN0czogZmFsc2UsXHJcbiAgICAgIGluY2x1ZGVUZW1wZXJhdHVyZUVmZmVjdHM6IHRydWUsXHJcbiAgICAgIGZyaWN0aW9uTWV0aG9kOiAnZGFyY3lfd2Vpc2JhY2gnLFxyXG4gICAgICByb3VuZGluZ1ByZWNpc2lvbjogNFxyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIC8vIENhbGN1bGF0ZSBzeXN0ZW0gcHJlc3N1cmUgZHJvcFxyXG4gIGNvbnN0IHN5c3RlbVJlc3VsdCA9IFN5c3RlbVByZXNzdXJlQ2FsY3VsYXRvci5jYWxjdWxhdGVTeXN0ZW1QcmVzc3VyZShpbnB1dHMpO1xyXG5cclxuICBjb25zb2xlLmxvZygnXFxuU3lzdGVtIFJlc3VsdHM6Jyk7XHJcbiAgY29uc29sZS5sb2coYCAgVG90YWwgUHJlc3N1cmUgTG9zczogJHtzeXN0ZW1SZXN1bHQudG90YWxQcmVzc3VyZUxvc3N9IGluIHdnYCk7XHJcbiAgY29uc29sZS5sb2coYCAgVG90YWwgRnJpY3Rpb24gTG9zczogJHtzeXN0ZW1SZXN1bHQudG90YWxGcmljdGlvbkxvc3N9IGluIHdnYCk7XHJcbiAgY29uc29sZS5sb2coYCAgVG90YWwgTWlub3IgTG9zczogJHtzeXN0ZW1SZXN1bHQudG90YWxNaW5vckxvc3N9IGluIHdnYCk7XHJcbiAgY29uc29sZS5sb2coYCAgVG90YWwgTGVuZ3RoOiAke3N5c3RlbVJlc3VsdC50b3RhbExlbmd0aH0gZmVldGApO1xyXG4gIGNvbnNvbGUubG9nKGAgIEF2ZXJhZ2UgVmVsb2NpdHk6ICR7c3lzdGVtUmVzdWx0LmF2ZXJhZ2VWZWxvY2l0eX0gRlBNYCk7XHJcbiAgY29uc29sZS5sb2coYCAgTWF4IFZlbG9jaXR5OiAke3N5c3RlbVJlc3VsdC5tYXhWZWxvY2l0eX0gRlBNYCk7XHJcblxyXG4gIGNvbnNvbGUubG9nKCdcXG5TZWdtZW50IERldGFpbHM6Jyk7XHJcbiAgc3lzdGVtUmVzdWx0LnNlZ21lbnRSZXN1bHRzLmZvckVhY2goKHNlZ21lbnQsIGluZGV4KSA9PiB7XHJcbiAgICBjb25zb2xlLmxvZyhgICBTZWdtZW50ICR7aW5kZXggKyAxfSAoJHtzZWdtZW50LnNlZ21lbnRJZH0pOmApO1xyXG4gICAgY29uc29sZS5sb2coYCAgICBUeXBlOiAke3NlZ21lbnQuc2VnbWVudFR5cGV9YCk7XHJcbiAgICBjb25zb2xlLmxvZyhgICAgIFZlbG9jaXR5OiAke3NlZ21lbnQudmVsb2NpdHl9IEZQTWApO1xyXG4gICAgY29uc29sZS5sb2coYCAgICBWZWxvY2l0eSBQcmVzc3VyZTogJHtzZWdtZW50LnZlbG9jaXR5UHJlc3N1cmV9IGluIHdnYCk7XHJcbiAgICBjb25zb2xlLmxvZyhgICAgIEZyaWN0aW9uIExvc3M6ICR7c2VnbWVudC5mcmljdGlvbkxvc3N9IGluIHdnYCk7XHJcbiAgICBjb25zb2xlLmxvZyhgICAgIE1pbm9yIExvc3M6ICR7c2VnbWVudC5taW5vckxvc3N9IGluIHdnYCk7XHJcbiAgICBjb25zb2xlLmxvZyhgICAgIFRvdGFsIExvc3M6ICR7c2VnbWVudC50b3RhbExvc3N9IGluIHdnYCk7XHJcbiAgICBcclxuICAgIGlmIChzZWdtZW50LmtGYWN0b3IpIHtcclxuICAgICAgY29uc29sZS5sb2coYCAgICBLLWZhY3RvcjogJHtzZWdtZW50LmtGYWN0b3J9YCk7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIGlmIChzZWdtZW50Lndhcm5pbmdzLmxlbmd0aCA+IDApIHtcclxuICAgICAgY29uc29sZS5sb2coYCAgICBXYXJuaW5nczogJHtzZWdtZW50Lndhcm5pbmdzLmpvaW4oJywgJyl9YCk7XHJcbiAgICB9XHJcbiAgfSk7XHJcblxyXG4gIGNvbnNvbGUubG9nKCdcXG5Db21wbGlhbmNlIFN0YXR1czonKTtcclxuICBjb25zb2xlLmxvZyhgICBWZWxvY2l0eSBDb21wbGlhbnQ6ICR7c3lzdGVtUmVzdWx0LmNvbXBsaWFuY2VTdGF0dXMudmVsb2NpdHlDb21wbGlhbnR9YCk7XHJcbiAgY29uc29sZS5sb2coYCAgUHJlc3N1cmUgQ29tcGxpYW50OiAke3N5c3RlbVJlc3VsdC5jb21wbGlhbmNlU3RhdHVzLnByZXNzdXJlQ29tcGxpYW50fWApO1xyXG4gIGNvbnNvbGUubG9nKGAgIFNNQUNOQSBDb21wbGlhbnQ6ICR7c3lzdGVtUmVzdWx0LmNvbXBsaWFuY2VTdGF0dXMuc21hY25hQ29tcGxpYW50fWApO1xyXG5cclxuICBpZiAoc3lzdGVtUmVzdWx0LnN5c3RlbVdhcm5pbmdzLmxlbmd0aCA+IDApIHtcclxuICAgIGNvbnNvbGUubG9nKCdcXG5TeXN0ZW0gV2FybmluZ3M6Jyk7XHJcbiAgICBzeXN0ZW1SZXN1bHQuc3lzdGVtV2FybmluZ3MuZm9yRWFjaCh3YXJuaW5nID0+IGNvbnNvbGUubG9nKGAgIC0gJHt3YXJuaW5nfWApKTtcclxuICB9XHJcblxyXG4gIGlmIChzeXN0ZW1SZXN1bHQuc3lzdGVtUmVjb21tZW5kYXRpb25zLmxlbmd0aCA+IDApIHtcclxuICAgIGNvbnNvbGUubG9nKCdcXG5TeXN0ZW0gUmVjb21tZW5kYXRpb25zOicpO1xyXG4gICAgc3lzdGVtUmVzdWx0LnN5c3RlbVJlY29tbWVuZGF0aW9ucy5mb3JFYWNoKHJlYyA9PiBjb25zb2xlLmxvZyhgICAtICR7cmVjfWApKTtcclxuICB9XHJcblxyXG4gIHJldHVybiBzeXN0ZW1SZXN1bHQ7XHJcbn1cclxuXHJcbi8qKlxyXG4gKiBFeGFtcGxlIDM6IENvbXBsZXggU3lzdGVtIHdpdGggTXVsdGlwbGUgRml0dGluZ3NcclxuICovXHJcbmZ1bmN0aW9uIGRlbW9uc3RyYXRlQ29tcGxleFN5c3RlbSgpIHtcclxuICBjb25zb2xlLmxvZygnXFxuPT09IENPTVBMRVggU1lTVEVNIEVYQU1QTEUgPT09Jyk7XHJcbiAgY29uc29sZS5sb2coJ1N5c3RlbTogU3VwcGx5IGR1Y3Qgd2l0aCBtdWx0aXBsZSBmaXR0aW5ncyBhbmQgdHJhbnNpdGlvbnMnKTtcclxuICBcclxuICBjb25zdCBjb21wbGV4U2VnbWVudHM6IER1Y3RTZWdtZW50W10gPSBbXHJcbiAgICAvLyBNYWluIHRydW5rOiAxMlwiIHJvdW5kLCAyMCBmZWV0XHJcbiAgICB7XHJcbiAgICAgIGlkOiAnbWFpbi10cnVuaycsXHJcbiAgICAgIHR5cGU6ICdzdHJhaWdodCcsXHJcbiAgICAgIGR1Y3RTaGFwZTogJ3JvdW5kJyxcclxuICAgICAgbGVuZ3RoOiAyMCxcclxuICAgICAgZGlhbWV0ZXI6IDEyLFxyXG4gICAgICBhaXJmbG93OiAyMDAwLFxyXG4gICAgICBtYXRlcmlhbDogJ2dhbHZhbml6ZWRfc3RlZWwnXHJcbiAgICB9LFxyXG4gICAgXHJcbiAgICAvLyBUZWUgZm9yIGJyYW5jaCB0YWtlb2ZmXHJcbiAgICB7XHJcbiAgICAgIGlkOiAnbWFpbi10ZWUnLFxyXG4gICAgICB0eXBlOiAnZml0dGluZycsXHJcbiAgICAgIGR1Y3RTaGFwZTogJ3JvdW5kJyxcclxuICAgICAgZGlhbWV0ZXI6IDEyLFxyXG4gICAgICBhaXJmbG93OiAyMDAwLFxyXG4gICAgICBtYXRlcmlhbDogJ2dhbHZhbml6ZWRfc3RlZWwnLFxyXG4gICAgICBmaXR0aW5nQ29uZmlnOiB7XHJcbiAgICAgICAgdHlwZTogJ3RlZV9yb3VuZF9icmFuY2hfOTBkZWcnLFxyXG4gICAgICAgIGR1Y3RTaGFwZTogJ3JvdW5kJyxcclxuICAgICAgICBkaWFtZXRlcjogMTIsXHJcbiAgICAgICAgc3VidHlwZTogJ3N0cmFpZ2h0X3Rocm91Z2gnLFxyXG4gICAgICAgIHBhcmFtZXRlcjogJzAuNicgLy8gQnJhbmNoIGFyZWEgcmF0aW9cclxuICAgICAgfVxyXG4gICAgfSxcclxuICAgIFxyXG4gICAgLy8gVHJhbnNpdGlvbiBmcm9tIDEyXCIgdG8gMTBcIlxyXG4gICAge1xyXG4gICAgICBpZDogJ3RyYW5zaXRpb24tMTItdG8tMTAnLFxyXG4gICAgICB0eXBlOiAnZml0dGluZycsXHJcbiAgICAgIGR1Y3RTaGFwZTogJ3JvdW5kJyxcclxuICAgICAgZGlhbWV0ZXI6IDEyLFxyXG4gICAgICBhaXJmbG93OiAxNTAwLCAvLyBSZWR1Y2VkIGZsb3cgYWZ0ZXIgYnJhbmNoXHJcbiAgICAgIG1hdGVyaWFsOiAnZ2FsdmFuaXplZF9zdGVlbCcsXHJcbiAgICAgIGZpdHRpbmdDb25maWc6IHtcclxuICAgICAgICB0eXBlOiAncm91bmRfdG9fcm91bmRfZ3JhZHVhbCcsXHJcbiAgICAgICAgZHVjdFNoYXBlOiAncm91bmQnLFxyXG4gICAgICAgIGRpYW1ldGVyOiAxMixcclxuICAgICAgICBwYXJhbWV0ZXI6ICcyLjUnIC8vIEwvRCByYXRpb1xyXG4gICAgICB9XHJcbiAgICB9LFxyXG4gICAgXHJcbiAgICAvLyAxMFwiIGR1Y3QgcnVuXHJcbiAgICB7XHJcbiAgICAgIGlkOiAncmVkdWNlZC1ydW4nLFxyXG4gICAgICB0eXBlOiAnc3RyYWlnaHQnLFxyXG4gICAgICBkdWN0U2hhcGU6ICdyb3VuZCcsXHJcbiAgICAgIGxlbmd0aDogMTUsXHJcbiAgICAgIGRpYW1ldGVyOiAxMCxcclxuICAgICAgYWlyZmxvdzogMTUwMCxcclxuICAgICAgbWF0ZXJpYWw6ICdnYWx2YW5pemVkX3N0ZWVsJ1xyXG4gICAgfSxcclxuICAgIFxyXG4gICAgLy8gOTDCsCBlbGJvd1xyXG4gICAge1xyXG4gICAgICBpZDogJ2ZpbmFsLWVsYm93JyxcclxuICAgICAgdHlwZTogJ2ZpdHRpbmcnLFxyXG4gICAgICBkdWN0U2hhcGU6ICdyb3VuZCcsXHJcbiAgICAgIGRpYW1ldGVyOiAxMCxcclxuICAgICAgYWlyZmxvdzogMTUwMCxcclxuICAgICAgbWF0ZXJpYWw6ICdnYWx2YW5pemVkX3N0ZWVsJyxcclxuICAgICAgZml0dGluZ0NvbmZpZzoge1xyXG4gICAgICAgIHR5cGU6ICc5MGRlZ19yb3VuZF9zbW9vdGgnLFxyXG4gICAgICAgIGR1Y3RTaGFwZTogJ3JvdW5kJyxcclxuICAgICAgICBkaWFtZXRlcjogMTAsXHJcbiAgICAgICAgcGFyYW1ldGVyOiAnMS41J1xyXG4gICAgICB9XHJcbiAgICB9LFxyXG4gICAgXHJcbiAgICAvLyBGaW5hbCBydW4gdG8gZGlmZnVzZXJcclxuICAgIHtcclxuICAgICAgaWQ6ICdmaW5hbC1ydW4nLFxyXG4gICAgICB0eXBlOiAnc3RyYWlnaHQnLFxyXG4gICAgICBkdWN0U2hhcGU6ICdyb3VuZCcsXHJcbiAgICAgIGxlbmd0aDogOCxcclxuICAgICAgZGlhbWV0ZXI6IDEwLFxyXG4gICAgICBhaXJmbG93OiAxNTAwLFxyXG4gICAgICBtYXRlcmlhbDogJ2dhbHZhbml6ZWRfc3RlZWwnXHJcbiAgICB9XHJcbiAgXTtcclxuXHJcbiAgY29uc3QgY29tcGxleElucHV0czogU3lzdGVtQ2FsY3VsYXRpb25JbnB1dHMgPSB7XHJcbiAgICBzZWdtZW50czogY29tcGxleFNlZ21lbnRzLFxyXG4gICAgc3lzdGVtVHlwZTogJ3N1cHBseScsXHJcbiAgICBkZXNpZ25Db25kaXRpb25zOiB7XHJcbiAgICAgIHRlbXBlcmF0dXJlOiA3NSxcclxuICAgICAgYmFyb21ldHJpY1ByZXNzdXJlOiAyOS45MixcclxuICAgICAgYWx0aXR1ZGU6IDBcclxuICAgIH0sXHJcbiAgICBjYWxjdWxhdGlvbk9wdGlvbnM6IHtcclxuICAgICAgaW5jbHVkZUVsZXZhdGlvbkVmZmVjdHM6IGZhbHNlLFxyXG4gICAgICBpbmNsdWRlVGVtcGVyYXR1cmVFZmZlY3RzOiB0cnVlLFxyXG4gICAgICBmcmljdGlvbk1ldGhvZDogJ2RhcmN5X3dlaXNiYWNoJyxcclxuICAgICAgcm91bmRpbmdQcmVjaXNpb246IDNcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBjb25zdCBjb21wbGV4UmVzdWx0ID0gU3lzdGVtUHJlc3N1cmVDYWxjdWxhdG9yLmNhbGN1bGF0ZVN5c3RlbVByZXNzdXJlKGNvbXBsZXhJbnB1dHMpO1xyXG5cclxuICBjb25zb2xlLmxvZygnXFxuQ29tcGxleCBTeXN0ZW0gUmVzdWx0czonKTtcclxuICBjb25zb2xlLmxvZyhgICBUb3RhbCBQcmVzc3VyZSBMb3NzOiAke2NvbXBsZXhSZXN1bHQudG90YWxQcmVzc3VyZUxvc3N9IGluIHdnYCk7XHJcbiAgY29uc29sZS5sb2coYCAgRnJpY3Rpb24gdnMgTWlub3IgTG9zcyBSYXRpbzogJHsoY29tcGxleFJlc3VsdC50b3RhbEZyaWN0aW9uTG9zcyAvIGNvbXBsZXhSZXN1bHQudG90YWxNaW5vckxvc3MpLnRvRml4ZWQoMil9YCk7XHJcbiAgY29uc29sZS5sb2coYCAgU3lzdGVtIExlbmd0aDogJHtjb21wbGV4UmVzdWx0LnRvdGFsTGVuZ3RofSBmZWV0YCk7XHJcbiAgY29uc29sZS5sb2coYCAgVmVsb2NpdHkgUmFuZ2U6ICR7Y29tcGxleFJlc3VsdC5taW5WZWxvY2l0eX0gLSAke2NvbXBsZXhSZXN1bHQubWF4VmVsb2NpdHl9IEZQTWApO1xyXG5cclxuICByZXR1cm4gY29tcGxleFJlc3VsdDtcclxufVxyXG5cclxuLyoqXHJcbiAqIEV4YW1wbGUgNDogQ29tcGFyaXNvbiB3aXRoIEV4aXN0aW5nIEFpckR1Y3RDYWxjdWxhdG9yXHJcbiAqL1xyXG5mdW5jdGlvbiBkZW1vbnN0cmF0ZUludGVncmF0aW9uV2l0aEV4aXN0aW5nKCkge1xyXG4gIGNvbnNvbGUubG9nKCdcXG49PT0gSU5URUdSQVRJT04gV0lUSCBFWElTVElORyBDQUxDVUxBVE9SID09PScpO1xyXG4gIFxyXG4gIC8vIFVzZSBleGlzdGluZyBBaXJEdWN0Q2FsY3VsYXRvciBmb3IgYmFzaWMgc2l6aW5nXHJcbiAgY29uc3QgYmFzaWNSZXN1bHQgPSBBaXJEdWN0Q2FsY3VsYXRvci5jYWxjdWxhdGVEdWN0U2l6aW5nKHtcclxuICAgIGFpcmZsb3c6IDEwMDAsXHJcbiAgICBkdWN0VHlwZTogJ3JvdW5kJyxcclxuICAgIGZyaWN0aW9uUmF0ZTogMC4wOCxcclxuICAgIHVuaXRzOiAnaW1wZXJpYWwnLFxyXG4gICAgbWF0ZXJpYWw6ICdnYWx2YW5pemVkX3N0ZWVsJ1xyXG4gIH0pO1xyXG5cclxuICBjb25zb2xlLmxvZygnQmFzaWMgRHVjdCBTaXppbmcgKEFpckR1Y3RDYWxjdWxhdG9yKTonKTtcclxuICBjb25zb2xlLmxvZyhgICBSZWNvbW1lbmRlZCBEaWFtZXRlcjogJHtiYXNpY1Jlc3VsdC5kaWFtZXRlcn1cIiByb3VuZGApO1xyXG4gIGNvbnNvbGUubG9nKGAgIFZlbG9jaXR5OiAke2Jhc2ljUmVzdWx0LnZlbG9jaXR5fSBGUE1gKTtcclxuICBjb25zb2xlLmxvZyhgICBQcmVzc3VyZSBMb3NzOiAke2Jhc2ljUmVzdWx0LnByZXNzdXJlTG9zc30gaW4gd2cvMTAwIGZ0YCk7XHJcblxyXG4gIC8vIE5vdyB1c2UgbmV3IHN5c3RlbSBjYWxjdWxhdG9yIGZvciBjb21wbGV0ZSBhbmFseXNpc1xyXG4gIGNvbnN0IGVuaGFuY2VkU2VnbWVudHM6IER1Y3RTZWdtZW50W10gPSBbXHJcbiAgICB7XHJcbiAgICAgIGlkOiAnc2l6ZWQtZHVjdCcsXHJcbiAgICAgIHR5cGU6ICdzdHJhaWdodCcsXHJcbiAgICAgIGR1Y3RTaGFwZTogJ3JvdW5kJyxcclxuICAgICAgbGVuZ3RoOiAxMDAsIC8vIDEwMCBmZWV0IGZvciBjb21wYXJpc29uXHJcbiAgICAgIGRpYW1ldGVyOiBiYXNpY1Jlc3VsdC5kaWFtZXRlcixcclxuICAgICAgYWlyZmxvdzogMTAwMCxcclxuICAgICAgbWF0ZXJpYWw6ICdnYWx2YW5pemVkX3N0ZWVsJ1xyXG4gICAgfVxyXG4gIF07XHJcblxyXG4gIGNvbnN0IGVuaGFuY2VkSW5wdXRzOiBTeXN0ZW1DYWxjdWxhdGlvbklucHV0cyA9IHtcclxuICAgIHNlZ21lbnRzOiBlbmhhbmNlZFNlZ21lbnRzLFxyXG4gICAgc3lzdGVtVHlwZTogJ3N1cHBseScsXHJcbiAgICBkZXNpZ25Db25kaXRpb25zOiB7XHJcbiAgICAgIHRlbXBlcmF0dXJlOiA3MCxcclxuICAgICAgYmFyb21ldHJpY1ByZXNzdXJlOiAyOS45MixcclxuICAgICAgYWx0aXR1ZGU6IDBcclxuICAgIH0sXHJcbiAgICBjYWxjdWxhdGlvbk9wdGlvbnM6IHtcclxuICAgICAgaW5jbHVkZUVsZXZhdGlvbkVmZmVjdHM6IGZhbHNlLFxyXG4gICAgICBpbmNsdWRlVGVtcGVyYXR1cmVFZmZlY3RzOiB0cnVlLFxyXG4gICAgICBmcmljdGlvbk1ldGhvZDogJ2RhcmN5X3dlaXNiYWNoJyxcclxuICAgICAgcm91bmRpbmdQcmVjaXNpb246IDRcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBjb25zdCBlbmhhbmNlZFJlc3VsdCA9IFN5c3RlbVByZXNzdXJlQ2FsY3VsYXRvci5jYWxjdWxhdGVTeXN0ZW1QcmVzc3VyZShlbmhhbmNlZElucHV0cyk7XHJcblxyXG4gIGNvbnNvbGUubG9nKCdcXG5FbmhhbmNlZCBTeXN0ZW0gQW5hbHlzaXMgKFN5c3RlbVByZXNzdXJlQ2FsY3VsYXRvcik6Jyk7XHJcbiAgY29uc29sZS5sb2coYCAgQ2FsY3VsYXRlZCBWZWxvY2l0eTogJHtlbmhhbmNlZFJlc3VsdC5hdmVyYWdlVmVsb2NpdHl9IEZQTWApO1xyXG4gIGNvbnNvbGUubG9nKGAgIEZyaWN0aW9uIExvc3MgKDEwMCBmdCk6ICR7ZW5oYW5jZWRSZXN1bHQudG90YWxGcmljdGlvbkxvc3N9IGluIHdnYCk7XHJcbiAgY29uc29sZS5sb2coYCAgUHJlc3N1cmUgTG9zcyBSYXRlOiAkeyhlbmhhbmNlZFJlc3VsdC50b3RhbEZyaWN0aW9uTG9zcykudG9GaXhlZCg0KX0gaW4gd2cvMTAwIGZ0YCk7XHJcblxyXG4gIGNvbnNvbGUubG9nKCdcXG5Db21wYXJpc29uOicpO1xyXG4gIGNvbnNvbGUubG9nKGAgIFZlbG9jaXR5IE1hdGNoOiAke01hdGguYWJzKGJhc2ljUmVzdWx0LnZlbG9jaXR5IC0gZW5oYW5jZWRSZXN1bHQuYXZlcmFnZVZlbG9jaXR5KSA8IDEgPyAnUEFTUycgOiAnRkFJTCd9YCk7XHJcbiAgY29uc29sZS5sb2coYCAgUHJlc3N1cmUgTG9zcyBNYXRjaDogJHtNYXRoLmFicyhiYXNpY1Jlc3VsdC5wcmVzc3VyZUxvc3MgLSBlbmhhbmNlZFJlc3VsdC50b3RhbEZyaWN0aW9uTG9zcykgPCAwLjAwMSA/ICdQQVNTJyA6ICdGQUlMJ31gKTtcclxufVxyXG5cclxuLyoqXHJcbiAqIE1haW4gZXhlY3V0aW9uIGZ1bmN0aW9uXHJcbiAqL1xyXG5mdW5jdGlvbiBydW5BbGxFeGFtcGxlcygpIHtcclxuICBjb25zb2xlLmxvZygnQ09NUFJFSEVOU0lWRSBEVUNUIFBIWVNJQ1MgSU1QTEVNRU5UQVRJT04gLSBJTlRFR1JBVElPTiBFWEFNUExFUycpO1xyXG4gIGNvbnNvbGUubG9nKCc9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09Jyk7XHJcbiAgXHJcbiAgdHJ5IHtcclxuICAgIC8vIFJ1biBhbGwgZXhhbXBsZXNcclxuICAgIGRlbW9uc3RyYXRlRml0dGluZ0xvc3NDYWxjdWxhdGlvbigpO1xyXG4gICAgZGVtb25zdHJhdGVTeXN0ZW1QcmVzc3VyZUNhbGN1bGF0aW9uKCk7XHJcbiAgICBkZW1vbnN0cmF0ZUNvbXBsZXhTeXN0ZW0oKTtcclxuICAgIGRlbW9uc3RyYXRlSW50ZWdyYXRpb25XaXRoRXhpc3RpbmcoKTtcclxuICAgIFxyXG4gICAgY29uc29sZS5sb2coJ1xcbj09PSBBTEwgRVhBTVBMRVMgQ09NUExFVEVEIFNVQ0NFU1NGVUxMWSA9PT0nKTtcclxuICAgIGNvbnNvbGUubG9nKCdQaGFzZSAxIGltcGxlbWVudGF0aW9uIGlzIHdvcmtpbmcgY29ycmVjdGx5IScpO1xyXG4gICAgXHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoJ1xcbj09PSBFUlJPUiBJTiBFWEFNUExFUyA9PT0nKTtcclxuICAgIGNvbnNvbGUuZXJyb3IoZXJyb3IpO1xyXG4gICAgcHJvY2Vzcy5leGl0KDEpO1xyXG4gIH1cclxufVxyXG5cclxuLy8gRXhwb3J0IGZ1bmN0aW9ucyBmb3IgdGVzdGluZ1xyXG5leHBvcnQge1xyXG4gIGRlbW9uc3RyYXRlRml0dGluZ0xvc3NDYWxjdWxhdGlvbixcclxuICBkZW1vbnN0cmF0ZVN5c3RlbVByZXNzdXJlQ2FsY3VsYXRpb24sXHJcbiAgZGVtb25zdHJhdGVDb21wbGV4U3lzdGVtLFxyXG4gIGRlbW9uc3RyYXRlSW50ZWdyYXRpb25XaXRoRXhpc3RpbmcsXHJcbiAgcnVuQWxsRXhhbXBsZXNcclxufTtcclxuXHJcbi8vIFJ1biBleGFtcGxlcyBpZiB0aGlzIGZpbGUgaXMgZXhlY3V0ZWQgZGlyZWN0bHlcclxuaWYgKHJlcXVpcmUubWFpbiA9PT0gbW9kdWxlKSB7XHJcbiAgcnVuQWxsRXhhbXBsZXMoKTtcclxufVxyXG4iXSwibWFwcGluZ3MiOiI7O0FBQUE7Ozs7Ozs7O0FBQUE7QUFBQSxTQUFBQSxlQUFBO0VBQUEsSUFBQUMsSUFBQTtFQUFBLElBQUFDLElBQUE7RUFBQSxJQUFBQyxNQUFBLE9BQUFDLFFBQUE7RUFBQSxJQUFBQyxHQUFBO0VBQUEsSUFBQUMsWUFBQTtJQUFBTCxJQUFBO0lBQUFNLFlBQUE7TUFBQTtRQUFBQyxLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7SUFBQTtJQUFBRSxLQUFBO01BQUE7UUFBQUMsSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO0lBQUE7SUFBQU8sU0FBQTtNQUFBO1FBQUFELEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7SUFBQTtJQUFBVyxDQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO0lBQUE7SUFBQUMsQ0FBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7SUFBQTtJQUFBQyxDQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO0lBQUE7SUFBQUMsY0FBQTtNQUFBQyxJQUFBO01BQUFDLFFBQUE7TUFBQUMsS0FBQTtNQUFBQyxPQUFBO01BQUFDLGNBQUE7TUFBQUMsT0FBQTtJQUFBO0lBQUFDLGVBQUE7SUFBQTVCLElBQUE7RUFBQTtFQUFBLElBQUE2QixRQUFBLEdBQUE1QixNQUFBLENBQUFFLEdBQUEsTUFBQUYsTUFBQSxDQUFBRSxHQUFBO0VBQUEsS0FBQTBCLFFBQUEsQ0FBQTlCLElBQUEsS0FBQThCLFFBQUEsQ0FBQTlCLElBQUEsRUFBQUMsSUFBQSxLQUFBQSxJQUFBO0lBQUE2QixRQUFBLENBQUE5QixJQUFBLElBQUFLLFlBQUE7RUFBQTtFQUFBLElBQUEwQixjQUFBLEdBQUFELFFBQUEsQ0FBQTlCLElBQUE7RUFBQTtJQVNBO0lBQUFELGNBQUEsWUFBQUEsQ0FBQTtNQUFBLE9BQUFnQyxjQUFBO0lBQUE7RUFBQTtFQUFBLE9BQUFBLGNBQUE7QUFBQTtBQUFBaEMsY0FBQTtBQUFBQSxjQUFBLEdBQUFvQixDQUFBOzs7Ozs7QUF3V0VhLE9BQUEsQ0FBQUMsaUNBQUEsR0FBQUEsaUNBQUE7QUFBaUM7QUFBQWxDLGNBQUEsR0FBQW9CLENBQUE7QUFDakNhLE9BQUEsQ0FBQUUsb0NBQUEsR0FBQUEsb0NBQUE7QUFBb0M7QUFBQW5DLGNBQUEsR0FBQW9CLENBQUE7QUFDcENhLE9BQUEsQ0FBQUcsd0JBQUEsR0FBQUEsd0JBQUE7QUFBd0I7QUFBQXBDLGNBQUEsR0FBQW9CLENBQUE7QUFDeEJhLE9BQUEsQ0FBQUksa0NBQUEsR0FBQUEsa0NBQUE7QUFBa0M7QUFBQXJDLGNBQUEsR0FBQW9CLENBQUE7QUFDbENhLE9BQUEsQ0FBQUssY0FBQSxHQUFBQSxjQUFBO0FBNVdGLE1BQUFDLHVCQUFBO0FBQUE7QUFBQSxDQUFBdkMsY0FBQSxHQUFBb0IsQ0FBQSxPQUFBb0IsT0FBQTtBQUNBLE1BQUFDLDBCQUFBO0FBQUE7QUFBQSxDQUFBekMsY0FBQSxHQUFBb0IsQ0FBQSxPQUFBb0IsT0FBQTtBQUNBLE1BQUFFLG1CQUFBO0FBQUE7QUFBQSxDQUFBMUMsY0FBQSxHQUFBb0IsQ0FBQSxPQUFBb0IsT0FBQTtBQUVBOzs7QUFHQSxTQUFTTixpQ0FBaUNBLENBQUE7RUFBQTtFQUFBbEMsY0FBQSxHQUFBcUIsQ0FBQTtFQUFBckIsY0FBQSxHQUFBb0IsQ0FBQTtFQUN4Q3VCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLDRDQUE0QyxDQUFDO0VBRXpEO0VBQ0EsTUFBTUMsV0FBVztFQUFBO0VBQUEsQ0FBQTdDLGNBQUEsR0FBQW9CLENBQUEsUUFBeUI7SUFDeENILElBQUksRUFBRSxvQkFBb0I7SUFDMUI2QixTQUFTLEVBQUUsT0FBTztJQUNsQkMsUUFBUSxFQUFFLEVBQUU7SUFDWkMsU0FBUyxFQUFFO0dBQ1o7RUFFRCxNQUFNQyxRQUFRO0VBQUE7RUFBQSxDQUFBakQsY0FBQSxHQUFBb0IsQ0FBQSxRQUFHLElBQUksRUFBQyxDQUFDO0VBQ3ZCLE1BQU04QixNQUFNO0VBQUE7RUFBQSxDQUFBbEQsY0FBQSxHQUFBb0IsQ0FBQSxRQUFHbUIsdUJBQUEsQ0FBQVkscUJBQXFCLENBQUNDLG9CQUFvQixDQUFDUCxXQUFXLEVBQUVJLFFBQVEsQ0FBQztFQUFDO0VBQUFqRCxjQUFBLEdBQUFvQixDQUFBO0VBRWpGdUIsT0FBTyxDQUFDQyxHQUFHLENBQUMsK0JBQStCLENBQUM7RUFBQztFQUFBNUMsY0FBQSxHQUFBb0IsQ0FBQTtFQUM3Q3VCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLGVBQWVNLE1BQU0sQ0FBQ0csT0FBTyxFQUFFLENBQUM7RUFBQztFQUFBckQsY0FBQSxHQUFBb0IsQ0FBQTtFQUM3Q3VCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLHdCQUF3Qk0sTUFBTSxDQUFDSSxnQkFBZ0IsQ0FBQ0MsT0FBTyxDQUFDLENBQUMsQ0FBQyxRQUFRLENBQUM7RUFBQztFQUFBdkQsY0FBQSxHQUFBb0IsQ0FBQTtFQUNoRnVCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLG9CQUFvQk0sTUFBTSxDQUFDTSxZQUFZLENBQUNELE9BQU8sQ0FBQyxDQUFDLENBQUMsUUFBUSxDQUFDO0VBQUM7RUFBQXZELGNBQUEsR0FBQW9CLENBQUE7RUFDeEV1QixPQUFPLENBQUNDLEdBQUcsQ0FBQyxvQkFBb0JNLE1BQU0sQ0FBQ08sYUFBYSxFQUFFLENBQUM7RUFBQztFQUFBekQsY0FBQSxHQUFBb0IsQ0FBQTtFQUV4RCxJQUFJOEIsTUFBTSxDQUFDUSxRQUFRLENBQUNDLE1BQU0sR0FBRyxDQUFDLEVBQUU7SUFBQTtJQUFBM0QsY0FBQSxHQUFBc0IsQ0FBQTtJQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtJQUM5QnVCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLGVBQWVNLE1BQU0sQ0FBQ1EsUUFBUSxDQUFDRSxJQUFJLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FBQztFQUMxRCxDQUFDO0VBQUE7RUFBQTtJQUFBNUQsY0FBQSxHQUFBc0IsQ0FBQTtFQUFBO0VBQUF0QixjQUFBLEdBQUFvQixDQUFBO0VBRUQsSUFBSThCLE1BQU0sQ0FBQ1csZUFBZSxDQUFDRixNQUFNLEdBQUcsQ0FBQyxFQUFFO0lBQUE7SUFBQTNELGNBQUEsR0FBQXNCLENBQUE7SUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7SUFDckN1QixPQUFPLENBQUNDLEdBQUcsQ0FBQyxzQkFBc0JNLE1BQU0sQ0FBQ1csZUFBZSxDQUFDRCxJQUFJLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FBQztFQUN4RSxDQUFDO0VBQUE7RUFBQTtJQUFBNUQsY0FBQSxHQUFBc0IsQ0FBQTtFQUFBO0VBQUF0QixjQUFBLEdBQUFvQixDQUFBO0VBRUQsT0FBTzhCLE1BQU07QUFDZjtBQUVBOzs7O0FBSUEsU0FBU2Ysb0NBQW9DQSxDQUFBO0VBQUE7RUFBQW5DLGNBQUEsR0FBQXFCLENBQUE7RUFBQXJCLGNBQUEsR0FBQW9CLENBQUE7RUFDM0N1QixPQUFPLENBQUNDLEdBQUcsQ0FBQyxvREFBb0QsQ0FBQztFQUFDO0VBQUE1QyxjQUFBLEdBQUFvQixDQUFBO0VBQ2xFdUIsT0FBTyxDQUFDQyxHQUFHLENBQUMsd0RBQXdELENBQUM7RUFFckU7RUFDQSxNQUFNa0IsUUFBUTtFQUFBO0VBQUEsQ0FBQTlELGNBQUEsR0FBQW9CLENBQUEsUUFBa0I7RUFDOUI7RUFDQTtJQUNFMkMsRUFBRSxFQUFFLGdCQUFnQjtJQUNwQjlDLElBQUksRUFBRSxVQUFVO0lBQ2hCNkIsU0FBUyxFQUFFLE9BQU87SUFDbEJhLE1BQU0sRUFBRSxFQUFFO0lBQ1ZaLFFBQVEsRUFBRSxFQUFFO0lBQ1ppQixPQUFPLEVBQUUsSUFBSTtJQUNiQyxRQUFRLEVBQUUsa0JBQWtCO0lBQzVCQyxLQUFLLEVBQUU7R0FDUjtFQUVEO0VBQ0E7SUFDRUgsRUFBRSxFQUFFLGFBQWE7SUFDakI5QyxJQUFJLEVBQUUsU0FBUztJQUNmNkIsU0FBUyxFQUFFLE9BQU87SUFDbEJDLFFBQVEsRUFBRSxFQUFFO0lBQ1ppQixPQUFPLEVBQUUsSUFBSTtJQUNiQyxRQUFRLEVBQUUsa0JBQWtCO0lBQzVCRSxhQUFhLEVBQUU7TUFDYmxELElBQUksRUFBRSxvQkFBb0I7TUFDMUI2QixTQUFTLEVBQUUsT0FBTztNQUNsQkMsUUFBUSxFQUFFLEVBQUU7TUFDWkMsU0FBUyxFQUFFO0tBQ1o7SUFDRGtCLEtBQUssRUFBRTtHQUNSO0VBRUQ7RUFDQTtJQUNFSCxFQUFFLEVBQUUsZ0JBQWdCO0lBQ3BCOUMsSUFBSSxFQUFFLFVBQVU7SUFDaEI2QixTQUFTLEVBQUUsT0FBTztJQUNsQmEsTUFBTSxFQUFFLEVBQUU7SUFDVlosUUFBUSxFQUFFLEVBQUU7SUFDWmlCLE9BQU8sRUFBRSxJQUFJO0lBQ2JDLFFBQVEsRUFBRSxrQkFBa0I7SUFDNUJDLEtBQUssRUFBRTtHQUNSLENBQ0Y7RUFFRDtFQUNBLE1BQU1FLE1BQU07RUFBQTtFQUFBLENBQUFwRSxjQUFBLEdBQUFvQixDQUFBLFFBQTRCO0lBQ3RDMEMsUUFBUTtJQUNSTyxVQUFVLEVBQUUsUUFBUTtJQUNwQkMsZ0JBQWdCLEVBQUU7TUFDaEJDLFdBQVcsRUFBRSxFQUFFO01BQUU7TUFDakJDLGtCQUFrQixFQUFFLEtBQUs7TUFBRTtNQUMzQkMsUUFBUSxFQUFFLENBQUMsQ0FBQztLQUNiO0lBQ0RDLGtCQUFrQixFQUFFO01BQ2xCQyx1QkFBdUIsRUFBRSxLQUFLO01BQzlCQyx5QkFBeUIsRUFBRSxJQUFJO01BQy9CQyxjQUFjLEVBQUUsZ0JBQWdCO01BQ2hDQyxpQkFBaUIsRUFBRTs7R0FFdEI7RUFFRDtFQUNBLE1BQU1DLFlBQVk7RUFBQTtFQUFBLENBQUEvRSxjQUFBLEdBQUFvQixDQUFBLFFBQUdxQiwwQkFBQSxDQUFBdUMsd0JBQXdCLENBQUNDLHVCQUF1QixDQUFDYixNQUFNLENBQUM7RUFBQztFQUFBcEUsY0FBQSxHQUFBb0IsQ0FBQTtFQUU5RXVCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLG1CQUFtQixDQUFDO0VBQUM7RUFBQTVDLGNBQUEsR0FBQW9CLENBQUE7RUFDakN1QixPQUFPLENBQUNDLEdBQUcsQ0FBQywwQkFBMEJtQyxZQUFZLENBQUNHLGlCQUFpQixRQUFRLENBQUM7RUFBQztFQUFBbEYsY0FBQSxHQUFBb0IsQ0FBQTtFQUM5RXVCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLDBCQUEwQm1DLFlBQVksQ0FBQ0ksaUJBQWlCLFFBQVEsQ0FBQztFQUFDO0VBQUFuRixjQUFBLEdBQUFvQixDQUFBO0VBQzlFdUIsT0FBTyxDQUFDQyxHQUFHLENBQUMsdUJBQXVCbUMsWUFBWSxDQUFDSyxjQUFjLFFBQVEsQ0FBQztFQUFDO0VBQUFwRixjQUFBLEdBQUFvQixDQUFBO0VBQ3hFdUIsT0FBTyxDQUFDQyxHQUFHLENBQUMsbUJBQW1CbUMsWUFBWSxDQUFDTSxXQUFXLE9BQU8sQ0FBQztFQUFDO0VBQUFyRixjQUFBLEdBQUFvQixDQUFBO0VBQ2hFdUIsT0FBTyxDQUFDQyxHQUFHLENBQUMsdUJBQXVCbUMsWUFBWSxDQUFDTyxlQUFlLE1BQU0sQ0FBQztFQUFDO0VBQUF0RixjQUFBLEdBQUFvQixDQUFBO0VBQ3ZFdUIsT0FBTyxDQUFDQyxHQUFHLENBQUMsbUJBQW1CbUMsWUFBWSxDQUFDUSxXQUFXLE1BQU0sQ0FBQztFQUFDO0VBQUF2RixjQUFBLEdBQUFvQixDQUFBO0VBRS9EdUIsT0FBTyxDQUFDQyxHQUFHLENBQUMsb0JBQW9CLENBQUM7RUFBQztFQUFBNUMsY0FBQSxHQUFBb0IsQ0FBQTtFQUNsQzJELFlBQVksQ0FBQ1MsY0FBYyxDQUFDQyxPQUFPLENBQUMsQ0FBQ0MsT0FBTyxFQUFFQyxLQUFLLEtBQUk7SUFBQTtJQUFBM0YsY0FBQSxHQUFBcUIsQ0FBQTtJQUFBckIsY0FBQSxHQUFBb0IsQ0FBQTtJQUNyRHVCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLGFBQWErQyxLQUFLLEdBQUcsQ0FBQyxLQUFLRCxPQUFPLENBQUNFLFNBQVMsSUFBSSxDQUFDO0lBQUM7SUFBQTVGLGNBQUEsR0FBQW9CLENBQUE7SUFDOUR1QixPQUFPLENBQUNDLEdBQUcsQ0FBQyxhQUFhOEMsT0FBTyxDQUFDRyxXQUFXLEVBQUUsQ0FBQztJQUFDO0lBQUE3RixjQUFBLEdBQUFvQixDQUFBO0lBQ2hEdUIsT0FBTyxDQUFDQyxHQUFHLENBQUMsaUJBQWlCOEMsT0FBTyxDQUFDekMsUUFBUSxNQUFNLENBQUM7SUFBQztJQUFBakQsY0FBQSxHQUFBb0IsQ0FBQTtJQUNyRHVCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLDBCQUEwQjhDLE9BQU8sQ0FBQ3BDLGdCQUFnQixRQUFRLENBQUM7SUFBQztJQUFBdEQsY0FBQSxHQUFBb0IsQ0FBQTtJQUN4RXVCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLHNCQUFzQjhDLE9BQU8sQ0FBQ0ksWUFBWSxRQUFRLENBQUM7SUFBQztJQUFBOUYsY0FBQSxHQUFBb0IsQ0FBQTtJQUNoRXVCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLG1CQUFtQjhDLE9BQU8sQ0FBQ0ssU0FBUyxRQUFRLENBQUM7SUFBQztJQUFBL0YsY0FBQSxHQUFBb0IsQ0FBQTtJQUMxRHVCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLG1CQUFtQjhDLE9BQU8sQ0FBQ00sU0FBUyxRQUFRLENBQUM7SUFBQztJQUFBaEcsY0FBQSxHQUFBb0IsQ0FBQTtJQUUxRCxJQUFJc0UsT0FBTyxDQUFDckMsT0FBTyxFQUFFO01BQUE7TUFBQXJELGNBQUEsR0FBQXNCLENBQUE7TUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7TUFDbkJ1QixPQUFPLENBQUNDLEdBQUcsQ0FBQyxpQkFBaUI4QyxPQUFPLENBQUNyQyxPQUFPLEVBQUUsQ0FBQztJQUNqRCxDQUFDO0lBQUE7SUFBQTtNQUFBckQsY0FBQSxHQUFBc0IsQ0FBQTtJQUFBO0lBQUF0QixjQUFBLEdBQUFvQixDQUFBO0lBRUQsSUFBSXNFLE9BQU8sQ0FBQ2hDLFFBQVEsQ0FBQ0MsTUFBTSxHQUFHLENBQUMsRUFBRTtNQUFBO01BQUEzRCxjQUFBLEdBQUFzQixDQUFBO01BQUF0QixjQUFBLEdBQUFvQixDQUFBO01BQy9CdUIsT0FBTyxDQUFDQyxHQUFHLENBQUMsaUJBQWlCOEMsT0FBTyxDQUFDaEMsUUFBUSxDQUFDRSxJQUFJLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FBQztJQUM3RCxDQUFDO0lBQUE7SUFBQTtNQUFBNUQsY0FBQSxHQUFBc0IsQ0FBQTtJQUFBO0VBQ0gsQ0FBQyxDQUFDO0VBQUM7RUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7RUFFSHVCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLHNCQUFzQixDQUFDO0VBQUM7RUFBQTVDLGNBQUEsR0FBQW9CLENBQUE7RUFDcEN1QixPQUFPLENBQUNDLEdBQUcsQ0FBQyx5QkFBeUJtQyxZQUFZLENBQUNrQixnQkFBZ0IsQ0FBQ0MsaUJBQWlCLEVBQUUsQ0FBQztFQUFDO0VBQUFsRyxjQUFBLEdBQUFvQixDQUFBO0VBQ3hGdUIsT0FBTyxDQUFDQyxHQUFHLENBQUMseUJBQXlCbUMsWUFBWSxDQUFDa0IsZ0JBQWdCLENBQUNFLGlCQUFpQixFQUFFLENBQUM7RUFBQztFQUFBbkcsY0FBQSxHQUFBb0IsQ0FBQTtFQUN4RnVCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLHVCQUF1Qm1DLFlBQVksQ0FBQ2tCLGdCQUFnQixDQUFDRyxlQUFlLEVBQUUsQ0FBQztFQUFDO0VBQUFwRyxjQUFBLEdBQUFvQixDQUFBO0VBRXBGLElBQUkyRCxZQUFZLENBQUNzQixjQUFjLENBQUMxQyxNQUFNLEdBQUcsQ0FBQyxFQUFFO0lBQUE7SUFBQTNELGNBQUEsR0FBQXNCLENBQUE7SUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7SUFDMUN1QixPQUFPLENBQUNDLEdBQUcsQ0FBQyxvQkFBb0IsQ0FBQztJQUFDO0lBQUE1QyxjQUFBLEdBQUFvQixDQUFBO0lBQ2xDMkQsWUFBWSxDQUFDc0IsY0FBYyxDQUFDWixPQUFPLENBQUNhLE9BQU8sSUFBSTtNQUFBO01BQUF0RyxjQUFBLEdBQUFxQixDQUFBO01BQUFyQixjQUFBLEdBQUFvQixDQUFBO01BQUEsT0FBQXVCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLE9BQU8wRCxPQUFPLEVBQUUsQ0FBQztJQUFELENBQUMsQ0FBQztFQUMvRSxDQUFDO0VBQUE7RUFBQTtJQUFBdEcsY0FBQSxHQUFBc0IsQ0FBQTtFQUFBO0VBQUF0QixjQUFBLEdBQUFvQixDQUFBO0VBRUQsSUFBSTJELFlBQVksQ0FBQ3dCLHFCQUFxQixDQUFDNUMsTUFBTSxHQUFHLENBQUMsRUFBRTtJQUFBO0lBQUEzRCxjQUFBLEdBQUFzQixDQUFBO0lBQUF0QixjQUFBLEdBQUFvQixDQUFBO0lBQ2pEdUIsT0FBTyxDQUFDQyxHQUFHLENBQUMsMkJBQTJCLENBQUM7SUFBQztJQUFBNUMsY0FBQSxHQUFBb0IsQ0FBQTtJQUN6QzJELFlBQVksQ0FBQ3dCLHFCQUFxQixDQUFDZCxPQUFPLENBQUNlLEdBQUcsSUFBSTtNQUFBO01BQUF4RyxjQUFBLEdBQUFxQixDQUFBO01BQUFyQixjQUFBLEdBQUFvQixDQUFBO01BQUEsT0FBQXVCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLE9BQU80RCxHQUFHLEVBQUUsQ0FBQztJQUFELENBQUMsQ0FBQztFQUM5RSxDQUFDO0VBQUE7RUFBQTtJQUFBeEcsY0FBQSxHQUFBc0IsQ0FBQTtFQUFBO0VBQUF0QixjQUFBLEdBQUFvQixDQUFBO0VBRUQsT0FBTzJELFlBQVk7QUFDckI7QUFFQTs7O0FBR0EsU0FBUzNDLHdCQUF3QkEsQ0FBQTtFQUFBO0VBQUFwQyxjQUFBLEdBQUFxQixDQUFBO0VBQUFyQixjQUFBLEdBQUFvQixDQUFBO0VBQy9CdUIsT0FBTyxDQUFDQyxHQUFHLENBQUMsa0NBQWtDLENBQUM7RUFBQztFQUFBNUMsY0FBQSxHQUFBb0IsQ0FBQTtFQUNoRHVCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLDREQUE0RCxDQUFDO0VBRXpFLE1BQU02RCxlQUFlO0VBQUE7RUFBQSxDQUFBekcsY0FBQSxHQUFBb0IsQ0FBQSxRQUFrQjtFQUNyQztFQUNBO0lBQ0UyQyxFQUFFLEVBQUUsWUFBWTtJQUNoQjlDLElBQUksRUFBRSxVQUFVO0lBQ2hCNkIsU0FBUyxFQUFFLE9BQU87SUFDbEJhLE1BQU0sRUFBRSxFQUFFO0lBQ1ZaLFFBQVEsRUFBRSxFQUFFO0lBQ1ppQixPQUFPLEVBQUUsSUFBSTtJQUNiQyxRQUFRLEVBQUU7R0FDWDtFQUVEO0VBQ0E7SUFDRUYsRUFBRSxFQUFFLFVBQVU7SUFDZDlDLElBQUksRUFBRSxTQUFTO0lBQ2Y2QixTQUFTLEVBQUUsT0FBTztJQUNsQkMsUUFBUSxFQUFFLEVBQUU7SUFDWmlCLE9BQU8sRUFBRSxJQUFJO0lBQ2JDLFFBQVEsRUFBRSxrQkFBa0I7SUFDNUJFLGFBQWEsRUFBRTtNQUNibEQsSUFBSSxFQUFFLHdCQUF3QjtNQUM5QjZCLFNBQVMsRUFBRSxPQUFPO01BQ2xCQyxRQUFRLEVBQUUsRUFBRTtNQUNaMkQsT0FBTyxFQUFFLGtCQUFrQjtNQUMzQjFELFNBQVMsRUFBRSxLQUFLLENBQUM7O0dBRXBCO0VBRUQ7RUFDQTtJQUNFZSxFQUFFLEVBQUUscUJBQXFCO0lBQ3pCOUMsSUFBSSxFQUFFLFNBQVM7SUFDZjZCLFNBQVMsRUFBRSxPQUFPO0lBQ2xCQyxRQUFRLEVBQUUsRUFBRTtJQUNaaUIsT0FBTyxFQUFFLElBQUk7SUFBRTtJQUNmQyxRQUFRLEVBQUUsa0JBQWtCO0lBQzVCRSxhQUFhLEVBQUU7TUFDYmxELElBQUksRUFBRSx3QkFBd0I7TUFDOUI2QixTQUFTLEVBQUUsT0FBTztNQUNsQkMsUUFBUSxFQUFFLEVBQUU7TUFDWkMsU0FBUyxFQUFFLEtBQUssQ0FBQzs7R0FFcEI7RUFFRDtFQUNBO0lBQ0VlLEVBQUUsRUFBRSxhQUFhO0lBQ2pCOUMsSUFBSSxFQUFFLFVBQVU7SUFDaEI2QixTQUFTLEVBQUUsT0FBTztJQUNsQmEsTUFBTSxFQUFFLEVBQUU7SUFDVlosUUFBUSxFQUFFLEVBQUU7SUFDWmlCLE9BQU8sRUFBRSxJQUFJO0lBQ2JDLFFBQVEsRUFBRTtHQUNYO0VBRUQ7RUFDQTtJQUNFRixFQUFFLEVBQUUsYUFBYTtJQUNqQjlDLElBQUksRUFBRSxTQUFTO0lBQ2Y2QixTQUFTLEVBQUUsT0FBTztJQUNsQkMsUUFBUSxFQUFFLEVBQUU7SUFDWmlCLE9BQU8sRUFBRSxJQUFJO0lBQ2JDLFFBQVEsRUFBRSxrQkFBa0I7SUFDNUJFLGFBQWEsRUFBRTtNQUNibEQsSUFBSSxFQUFFLG9CQUFvQjtNQUMxQjZCLFNBQVMsRUFBRSxPQUFPO01BQ2xCQyxRQUFRLEVBQUUsRUFBRTtNQUNaQyxTQUFTLEVBQUU7O0dBRWQ7RUFFRDtFQUNBO0lBQ0VlLEVBQUUsRUFBRSxXQUFXO0lBQ2Y5QyxJQUFJLEVBQUUsVUFBVTtJQUNoQjZCLFNBQVMsRUFBRSxPQUFPO0lBQ2xCYSxNQUFNLEVBQUUsQ0FBQztJQUNUWixRQUFRLEVBQUUsRUFBRTtJQUNaaUIsT0FBTyxFQUFFLElBQUk7SUFDYkMsUUFBUSxFQUFFO0dBQ1gsQ0FDRjtFQUVELE1BQU0wQyxhQUFhO0VBQUE7RUFBQSxDQUFBM0csY0FBQSxHQUFBb0IsQ0FBQSxRQUE0QjtJQUM3QzBDLFFBQVEsRUFBRTJDLGVBQWU7SUFDekJwQyxVQUFVLEVBQUUsUUFBUTtJQUNwQkMsZ0JBQWdCLEVBQUU7TUFDaEJDLFdBQVcsRUFBRSxFQUFFO01BQ2ZDLGtCQUFrQixFQUFFLEtBQUs7TUFDekJDLFFBQVEsRUFBRTtLQUNYO0lBQ0RDLGtCQUFrQixFQUFFO01BQ2xCQyx1QkFBdUIsRUFBRSxLQUFLO01BQzlCQyx5QkFBeUIsRUFBRSxJQUFJO01BQy9CQyxjQUFjLEVBQUUsZ0JBQWdCO01BQ2hDQyxpQkFBaUIsRUFBRTs7R0FFdEI7RUFFRCxNQUFNOEIsYUFBYTtFQUFBO0VBQUEsQ0FBQTVHLGNBQUEsR0FBQW9CLENBQUEsUUFBR3FCLDBCQUFBLENBQUF1Qyx3QkFBd0IsQ0FBQ0MsdUJBQXVCLENBQUMwQixhQUFhLENBQUM7RUFBQztFQUFBM0csY0FBQSxHQUFBb0IsQ0FBQTtFQUV0RnVCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLDJCQUEyQixDQUFDO0VBQUM7RUFBQTVDLGNBQUEsR0FBQW9CLENBQUE7RUFDekN1QixPQUFPLENBQUNDLEdBQUcsQ0FBQywwQkFBMEJnRSxhQUFhLENBQUMxQixpQkFBaUIsUUFBUSxDQUFDO0VBQUM7RUFBQWxGLGNBQUEsR0FBQW9CLENBQUE7RUFDL0V1QixPQUFPLENBQUNDLEdBQUcsQ0FBQyxtQ0FBbUMsQ0FBQ2dFLGFBQWEsQ0FBQ3pCLGlCQUFpQixHQUFHeUIsYUFBYSxDQUFDeEIsY0FBYyxFQUFFN0IsT0FBTyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUM7RUFBQztFQUFBdkQsY0FBQSxHQUFBb0IsQ0FBQTtFQUM5SHVCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLG9CQUFvQmdFLGFBQWEsQ0FBQ3ZCLFdBQVcsT0FBTyxDQUFDO0VBQUM7RUFBQXJGLGNBQUEsR0FBQW9CLENBQUE7RUFDbEV1QixPQUFPLENBQUNDLEdBQUcsQ0FBQyxxQkFBcUJnRSxhQUFhLENBQUNDLFdBQVcsTUFBTUQsYUFBYSxDQUFDckIsV0FBVyxNQUFNLENBQUM7RUFBQztFQUFBdkYsY0FBQSxHQUFBb0IsQ0FBQTtFQUVqRyxPQUFPd0YsYUFBYTtBQUN0QjtBQUVBOzs7QUFHQSxTQUFTdkUsa0NBQWtDQSxDQUFBO0VBQUE7RUFBQXJDLGNBQUEsR0FBQXFCLENBQUE7RUFBQXJCLGNBQUEsR0FBQW9CLENBQUE7RUFDekN1QixPQUFPLENBQUNDLEdBQUcsQ0FBQyxnREFBZ0QsQ0FBQztFQUU3RDtFQUNBLE1BQU1rRSxXQUFXO0VBQUE7RUFBQSxDQUFBOUcsY0FBQSxHQUFBb0IsQ0FBQSxRQUFHc0IsbUJBQUEsQ0FBQXFFLGlCQUFpQixDQUFDQyxtQkFBbUIsQ0FBQztJQUN4RGhELE9BQU8sRUFBRSxJQUFJO0lBQ2JpRCxRQUFRLEVBQUUsT0FBTztJQUNqQkMsWUFBWSxFQUFFLElBQUk7SUFDbEJDLEtBQUssRUFBRSxVQUFVO0lBQ2pCbEQsUUFBUSxFQUFFO0dBQ1gsQ0FBQztFQUFDO0VBQUFqRSxjQUFBLEdBQUFvQixDQUFBO0VBRUh1QixPQUFPLENBQUNDLEdBQUcsQ0FBQyx3Q0FBd0MsQ0FBQztFQUFDO0VBQUE1QyxjQUFBLEdBQUFvQixDQUFBO0VBQ3REdUIsT0FBTyxDQUFDQyxHQUFHLENBQUMsMkJBQTJCa0UsV0FBVyxDQUFDL0QsUUFBUSxTQUFTLENBQUM7RUFBQztFQUFBL0MsY0FBQSxHQUFBb0IsQ0FBQTtFQUN0RXVCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLGVBQWVrRSxXQUFXLENBQUM3RCxRQUFRLE1BQU0sQ0FBQztFQUFDO0VBQUFqRCxjQUFBLEdBQUFvQixDQUFBO0VBQ3ZEdUIsT0FBTyxDQUFDQyxHQUFHLENBQUMsb0JBQW9Ca0UsV0FBVyxDQUFDdEQsWUFBWSxlQUFlLENBQUM7RUFFeEU7RUFDQSxNQUFNNEQsZ0JBQWdCO0VBQUE7RUFBQSxDQUFBcEgsY0FBQSxHQUFBb0IsQ0FBQSxRQUFrQixDQUN0QztJQUNFMkMsRUFBRSxFQUFFLFlBQVk7SUFDaEI5QyxJQUFJLEVBQUUsVUFBVTtJQUNoQjZCLFNBQVMsRUFBRSxPQUFPO0lBQ2xCYSxNQUFNLEVBQUUsR0FBRztJQUFFO0lBQ2JaLFFBQVEsRUFBRStELFdBQVcsQ0FBQy9ELFFBQVE7SUFDOUJpQixPQUFPLEVBQUUsSUFBSTtJQUNiQyxRQUFRLEVBQUU7R0FDWCxDQUNGO0VBRUQsTUFBTW9ELGNBQWM7RUFBQTtFQUFBLENBQUFySCxjQUFBLEdBQUFvQixDQUFBLFFBQTRCO0lBQzlDMEMsUUFBUSxFQUFFc0QsZ0JBQWdCO0lBQzFCL0MsVUFBVSxFQUFFLFFBQVE7SUFDcEJDLGdCQUFnQixFQUFFO01BQ2hCQyxXQUFXLEVBQUUsRUFBRTtNQUNmQyxrQkFBa0IsRUFBRSxLQUFLO01BQ3pCQyxRQUFRLEVBQUU7S0FDWDtJQUNEQyxrQkFBa0IsRUFBRTtNQUNsQkMsdUJBQXVCLEVBQUUsS0FBSztNQUM5QkMseUJBQXlCLEVBQUUsSUFBSTtNQUMvQkMsY0FBYyxFQUFFLGdCQUFnQjtNQUNoQ0MsaUJBQWlCLEVBQUU7O0dBRXRCO0VBRUQsTUFBTXdDLGNBQWM7RUFBQTtFQUFBLENBQUF0SCxjQUFBLEdBQUFvQixDQUFBLFFBQUdxQiwwQkFBQSxDQUFBdUMsd0JBQXdCLENBQUNDLHVCQUF1QixDQUFDb0MsY0FBYyxDQUFDO0VBQUM7RUFBQXJILGNBQUEsR0FBQW9CLENBQUE7RUFFeEZ1QixPQUFPLENBQUNDLEdBQUcsQ0FBQyx3REFBd0QsQ0FBQztFQUFDO0VBQUE1QyxjQUFBLEdBQUFvQixDQUFBO0VBQ3RFdUIsT0FBTyxDQUFDQyxHQUFHLENBQUMsMEJBQTBCMEUsY0FBYyxDQUFDaEMsZUFBZSxNQUFNLENBQUM7RUFBQztFQUFBdEYsY0FBQSxHQUFBb0IsQ0FBQTtFQUM1RXVCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLDZCQUE2QjBFLGNBQWMsQ0FBQ25DLGlCQUFpQixRQUFRLENBQUM7RUFBQztFQUFBbkYsY0FBQSxHQUFBb0IsQ0FBQTtFQUNuRnVCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLHlCQUEwQjBFLGNBQWMsQ0FBQ25DLGlCQUFpQixDQUFFNUIsT0FBTyxDQUFDLENBQUMsQ0FBQyxlQUFlLENBQUM7RUFBQztFQUFBdkQsY0FBQSxHQUFBb0IsQ0FBQTtFQUVuR3VCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLGVBQWUsQ0FBQztFQUFDO0VBQUE1QyxjQUFBLEdBQUFvQixDQUFBO0VBQzdCdUIsT0FBTyxDQUFDQyxHQUFHLENBQUMscUJBQXFCMkUsSUFBSSxDQUFDQyxHQUFHLENBQUNWLFdBQVcsQ0FBQzdELFFBQVEsR0FBR3FFLGNBQWMsQ0FBQ2hDLGVBQWUsQ0FBQyxHQUFHLENBQUM7RUFBQTtFQUFBLENBQUF0RixjQUFBLEdBQUFzQixDQUFBLFVBQUcsTUFBTTtFQUFBO0VBQUEsQ0FBQXRCLGNBQUEsR0FBQXNCLENBQUEsVUFBRyxNQUFNLEdBQUUsQ0FBQztFQUFDO0VBQUF0QixjQUFBLEdBQUFvQixDQUFBO0VBQzFIdUIsT0FBTyxDQUFDQyxHQUFHLENBQUMsMEJBQTBCMkUsSUFBSSxDQUFDQyxHQUFHLENBQUNWLFdBQVcsQ0FBQ3RELFlBQVksR0FBRzhELGNBQWMsQ0FBQ25DLGlCQUFpQixDQUFDLEdBQUcsS0FBSztFQUFBO0VBQUEsQ0FBQW5GLGNBQUEsR0FBQXNCLENBQUEsVUFBRyxNQUFNO0VBQUE7RUFBQSxDQUFBdEIsY0FBQSxHQUFBc0IsQ0FBQSxVQUFHLE1BQU0sR0FBRSxDQUFDO0FBQzFJO0FBRUE7OztBQUdBLFNBQVNnQixjQUFjQSxDQUFBO0VBQUE7RUFBQXRDLGNBQUEsR0FBQXFCLENBQUE7RUFBQXJCLGNBQUEsR0FBQW9CLENBQUE7RUFDckJ1QixPQUFPLENBQUNDLEdBQUcsQ0FBQyxrRUFBa0UsQ0FBQztFQUFDO0VBQUE1QyxjQUFBLEdBQUFvQixDQUFBO0VBQ2hGdUIsT0FBTyxDQUFDQyxHQUFHLENBQUMsa0VBQWtFLENBQUM7RUFBQztFQUFBNUMsY0FBQSxHQUFBb0IsQ0FBQTtFQUVoRixJQUFJO0lBQUE7SUFBQXBCLGNBQUEsR0FBQW9CLENBQUE7SUFDRjtJQUNBYyxpQ0FBaUMsRUFBRTtJQUFDO0lBQUFsQyxjQUFBLEdBQUFvQixDQUFBO0lBQ3BDZSxvQ0FBb0MsRUFBRTtJQUFDO0lBQUFuQyxjQUFBLEdBQUFvQixDQUFBO0lBQ3ZDZ0Isd0JBQXdCLEVBQUU7SUFBQztJQUFBcEMsY0FBQSxHQUFBb0IsQ0FBQTtJQUMzQmlCLGtDQUFrQyxFQUFFO0lBQUM7SUFBQXJDLGNBQUEsR0FBQW9CLENBQUE7SUFFckN1QixPQUFPLENBQUNDLEdBQUcsQ0FBQywrQ0FBK0MsQ0FBQztJQUFDO0lBQUE1QyxjQUFBLEdBQUFvQixDQUFBO0lBQzdEdUIsT0FBTyxDQUFDQyxHQUFHLENBQUMsOENBQThDLENBQUM7RUFFN0QsQ0FBQyxDQUFDLE9BQU82RSxLQUFLLEVBQUU7SUFBQTtJQUFBekgsY0FBQSxHQUFBb0IsQ0FBQTtJQUNkdUIsT0FBTyxDQUFDOEUsS0FBSyxDQUFDLDZCQUE2QixDQUFDO0lBQUM7SUFBQXpILGNBQUEsR0FBQW9CLENBQUE7SUFDN0N1QixPQUFPLENBQUM4RSxLQUFLLENBQUNBLEtBQUssQ0FBQztJQUFDO0lBQUF6SCxjQUFBLEdBQUFvQixDQUFBO0lBQ3JCc0csT0FBTyxDQUFDQyxJQUFJLENBQUMsQ0FBQyxDQUFDO0VBQ2pCO0FBQ0Y7QUFXQTtBQUFBO0FBQUEzSCxjQUFBLEdBQUFvQixDQUFBO0FBQ0EsSUFBSW9CLE9BQU8sQ0FBQ29GLElBQUksS0FBS0MsTUFBTSxFQUFFO0VBQUE7RUFBQTdILGNBQUEsR0FBQXNCLENBQUE7RUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7RUFDM0JrQixjQUFjLEVBQUU7QUFDbEIsQ0FBQztBQUFBO0FBQUE7RUFBQXRDLGNBQUEsR0FBQXNCLENBQUE7QUFBQSIsImlnbm9yZUxpc3QiOltdfQ==