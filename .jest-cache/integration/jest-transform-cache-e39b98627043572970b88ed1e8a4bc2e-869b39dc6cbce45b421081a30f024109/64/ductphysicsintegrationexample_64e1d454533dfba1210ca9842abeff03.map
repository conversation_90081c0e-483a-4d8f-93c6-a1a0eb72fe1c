{"version": 3, "names": ["cov_165opjsltb", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "name", "decl", "loc", "branchMap", "type", "locations", "undefined", "s", "f", "b", "inputSourceMap", "file", "mappings", "names", "sources", "sourcesContent", "version", "_coverageSchema", "coverage", "actualCoverage", "exports", "demonstrateFittingLossCalculation", "demonstrateSystemPressureCalculation", "demonstrateComplexSystem", "demonstrateIntegrationWithExisting", "runAllExamples", "FittingLossCalculator_1", "require", "SystemPressureCalculator_1", "AirDuctCalculator_1", "console", "log", "elbowConfig", "ductShape", "diameter", "parameter", "velocity", "result", "FittingLossCalculator", "calculateFittingLoss", "kFactor", "velocityPressure", "toFixed", "pressureLoss", "configuration", "warnings", "length", "join", "recommendations", "segments", "id", "airflow", "material", "notes", "fittingConfig", "inputs", "systemType", "designConditions", "temperature", "barometricPressure", "altitude", "calculationOptions", "includeElevationEffects", "includeTemperatureEffects", "frictionMethod", "roundingPrecision", "systemResult", "SystemPressureCalculator", "calculateSystemPressure", "totalPressureLoss", "totalFrictionLoss", "totalMinorLoss", "totalLength", "averageVelocity", "maxVelocity", "segmentResults", "for<PERSON>ach", "segment", "index", "segmentId", "segmentType", "frictionLoss", "minor<PERSON><PERSON>", "totalLoss", "complianceStatus", "velocityCompliant", "pressureCompliant", "smacnaCompliant", "systemWarnings", "warning", "systemRecommendations", "rec", "complexSegments", "subtype", "complexInputs", "complexResult", "minVelocity", "basicResult", "AirDuctCalculator", "calculateDuctSizing", "ductType", "frictionRate", "units", "enhancedSegments", "enhancedInputs", "enhancedResult", "Math", "abs", "error", "process", "exit", "main", "module"], "sources": ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\examples\\duct-physics-integration-example.ts"], "sourcesContent": ["/**\r\n * Duct Physics Integration Example\r\n * \r\n * Demonstrates the comprehensive duct physics implementation\r\n * Shows how to use FittingLossCalculator and SystemPressureCalculator\r\n * \r\n * Example: 10″ round duct → 10′ run → 90° elbow → 10′ run\r\n */\r\n\r\nimport { FittingLossCalculator, FittingConfiguration } from '../FittingLossCalculator';\r\nimport { SystemPressureCalculator, DuctSegment, SystemCalculationInputs } from '../SystemPressureCalculator';\r\nimport { AirDuctCalculator } from '../AirDuctCalculator';\r\n\r\n/**\r\n * Example 1: Individual Fitting Loss Calculation\r\n */\r\nfunction demonstrateFittingLossCalculation() {\r\n  console.log('\\n=== FITTING LOSS CALCULATION EXAMPLE ===');\r\n  \r\n  // Example: 90° smooth elbow with R/D = 1.5\r\n  const elbowConfig: FittingConfiguration = {\r\n    type: '90deg_round_smooth',\r\n    ductShape: 'round',\r\n    diameter: 10,\r\n    parameter: '1.5'\r\n  };\r\n\r\n  const velocity = 1833; // FPM (calculated from 1000 CFM in 10\" duct)\r\n  const result = FittingLossCalculator.calculateFittingLoss(elbowConfig, velocity);\r\n\r\n  console.log('90° Smooth Elbow (R/D = 1.5):');\r\n  console.log(`  K-factor: ${result.kFactor}`);\r\n  console.log(`  Velocity Pressure: ${result.velocityPressure.toFixed(4)} in wg`);\r\n  console.log(`  Pressure Loss: ${result.pressureLoss.toFixed(4)} in wg`);\r\n  console.log(`  Configuration: ${result.configuration}`);\r\n  \r\n  if (result.warnings.length > 0) {\r\n    console.log(`  Warnings: ${result.warnings.join(', ')}`);\r\n  }\r\n  \r\n  if (result.recommendations.length > 0) {\r\n    console.log(`  Recommendations: ${result.recommendations.join(', ')}`);\r\n  }\r\n\r\n  return result;\r\n}\r\n\r\n/**\r\n * Example 2: Complete System Pressure Drop Calculation\r\n * User's example: 10″ round duct → 10′ run → 90° elbow → 10′ run\r\n */\r\nfunction demonstrateSystemPressureCalculation() {\r\n  console.log('\\n=== SYSTEM PRESSURE DROP CALCULATION EXAMPLE ===');\r\n  console.log('System: 10″ round duct → 10′ run → 90° elbow → 10′ run');\r\n  \r\n  // Define system segments\r\n  const segments: DuctSegment[] = [\r\n    // First straight run: 10 feet\r\n    {\r\n      id: 'straight-run-1',\r\n      type: 'straight',\r\n      ductShape: 'round',\r\n      length: 10,\r\n      diameter: 10,\r\n      airflow: 1000,\r\n      material: 'galvanized_steel',\r\n      notes: 'First straight section'\r\n    },\r\n    \r\n    // 90° elbow with R/D = 1.5\r\n    {\r\n      id: 'elbow-90deg',\r\n      type: 'fitting',\r\n      ductShape: 'round',\r\n      diameter: 10,\r\n      airflow: 1000,\r\n      material: 'galvanized_steel',\r\n      fittingConfig: {\r\n        type: '90deg_round_smooth',\r\n        ductShape: 'round',\r\n        diameter: 10,\r\n        parameter: '1.5'\r\n      },\r\n      notes: '90° smooth elbow'\r\n    },\r\n    \r\n    // Second straight run: 10 feet\r\n    {\r\n      id: 'straight-run-2',\r\n      type: 'straight',\r\n      ductShape: 'round',\r\n      length: 10,\r\n      diameter: 10,\r\n      airflow: 1000,\r\n      material: 'galvanized_steel',\r\n      notes: 'Second straight section'\r\n    }\r\n  ];\r\n\r\n  // System calculation inputs\r\n  const inputs: SystemCalculationInputs = {\r\n    segments,\r\n    systemType: 'supply',\r\n    designConditions: {\r\n      temperature: 70, // °F\r\n      barometricPressure: 29.92, // in Hg\r\n      altitude: 0 // feet above sea level\r\n    },\r\n    calculationOptions: {\r\n      includeElevationEffects: false,\r\n      includeTemperatureEffects: true,\r\n      frictionMethod: 'darcy_weisbach',\r\n      roundingPrecision: 4\r\n    }\r\n  };\r\n\r\n  // Calculate system pressure drop\r\n  const systemResult = SystemPressureCalculator.calculateSystemPressure(inputs);\r\n\r\n  console.log('\\nSystem Results:');\r\n  console.log(`  Total Pressure Loss: ${systemResult.totalPressureLoss} in wg`);\r\n  console.log(`  Total Friction Loss: ${systemResult.totalFrictionLoss} in wg`);\r\n  console.log(`  Total Minor Loss: ${systemResult.totalMinorLoss} in wg`);\r\n  console.log(`  Total Length: ${systemResult.totalLength} feet`);\r\n  console.log(`  Average Velocity: ${systemResult.averageVelocity} FPM`);\r\n  console.log(`  Max Velocity: ${systemResult.maxVelocity} FPM`);\r\n\r\n  console.log('\\nSegment Details:');\r\n  systemResult.segmentResults.forEach((segment, index) => {\r\n    console.log(`  Segment ${index + 1} (${segment.segmentId}):`);\r\n    console.log(`    Type: ${segment.segmentType}`);\r\n    console.log(`    Velocity: ${segment.velocity} FPM`);\r\n    console.log(`    Velocity Pressure: ${segment.velocityPressure} in wg`);\r\n    console.log(`    Friction Loss: ${segment.frictionLoss} in wg`);\r\n    console.log(`    Minor Loss: ${segment.minorLoss} in wg`);\r\n    console.log(`    Total Loss: ${segment.totalLoss} in wg`);\r\n    \r\n    if (segment.kFactor) {\r\n      console.log(`    K-factor: ${segment.kFactor}`);\r\n    }\r\n    \r\n    if (segment.warnings.length > 0) {\r\n      console.log(`    Warnings: ${segment.warnings.join(', ')}`);\r\n    }\r\n  });\r\n\r\n  console.log('\\nCompliance Status:');\r\n  console.log(`  Velocity Compliant: ${systemResult.complianceStatus.velocityCompliant}`);\r\n  console.log(`  Pressure Compliant: ${systemResult.complianceStatus.pressureCompliant}`);\r\n  console.log(`  SMACNA Compliant: ${systemResult.complianceStatus.smacnaCompliant}`);\r\n\r\n  if (systemResult.systemWarnings.length > 0) {\r\n    console.log('\\nSystem Warnings:');\r\n    systemResult.systemWarnings.forEach(warning => console.log(`  - ${warning}`));\r\n  }\r\n\r\n  if (systemResult.systemRecommendations.length > 0) {\r\n    console.log('\\nSystem Recommendations:');\r\n    systemResult.systemRecommendations.forEach(rec => console.log(`  - ${rec}`));\r\n  }\r\n\r\n  return systemResult;\r\n}\r\n\r\n/**\r\n * Example 3: Complex System with Multiple Fittings\r\n */\r\nfunction demonstrateComplexSystem() {\r\n  console.log('\\n=== COMPLEX SYSTEM EXAMPLE ===');\r\n  console.log('System: Supply duct with multiple fittings and transitions');\r\n  \r\n  const complexSegments: DuctSegment[] = [\r\n    // Main trunk: 12\" round, 20 feet\r\n    {\r\n      id: 'main-trunk',\r\n      type: 'straight',\r\n      ductShape: 'round',\r\n      length: 20,\r\n      diameter: 12,\r\n      airflow: 2000,\r\n      material: 'galvanized_steel'\r\n    },\r\n    \r\n    // Tee for branch takeoff\r\n    {\r\n      id: 'main-tee',\r\n      type: 'fitting',\r\n      ductShape: 'round',\r\n      diameter: 12,\r\n      airflow: 2000,\r\n      material: 'galvanized_steel',\r\n      fittingConfig: {\r\n        type: 'tee_round_branch_90deg',\r\n        ductShape: 'round',\r\n        diameter: 12,\r\n        subtype: 'straight_through',\r\n        parameter: '0.6' // Branch area ratio\r\n      }\r\n    },\r\n    \r\n    // Transition from 12\" to 10\"\r\n    {\r\n      id: 'transition-12-to-10',\r\n      type: 'fitting',\r\n      ductShape: 'round',\r\n      diameter: 12,\r\n      airflow: 1500, // Reduced flow after branch\r\n      material: 'galvanized_steel',\r\n      fittingConfig: {\r\n        type: 'round_to_round_gradual',\r\n        ductShape: 'round',\r\n        diameter: 12,\r\n        parameter: '2.5' // L/D ratio\r\n      }\r\n    },\r\n    \r\n    // 10\" duct run\r\n    {\r\n      id: 'reduced-run',\r\n      type: 'straight',\r\n      ductShape: 'round',\r\n      length: 15,\r\n      diameter: 10,\r\n      airflow: 1500,\r\n      material: 'galvanized_steel'\r\n    },\r\n    \r\n    // 90° elbow\r\n    {\r\n      id: 'final-elbow',\r\n      type: 'fitting',\r\n      ductShape: 'round',\r\n      diameter: 10,\r\n      airflow: 1500,\r\n      material: 'galvanized_steel',\r\n      fittingConfig: {\r\n        type: '90deg_round_smooth',\r\n        ductShape: 'round',\r\n        diameter: 10,\r\n        parameter: '1.5'\r\n      }\r\n    },\r\n    \r\n    // Final run to diffuser\r\n    {\r\n      id: 'final-run',\r\n      type: 'straight',\r\n      ductShape: 'round',\r\n      length: 8,\r\n      diameter: 10,\r\n      airflow: 1500,\r\n      material: 'galvanized_steel'\r\n    }\r\n  ];\r\n\r\n  const complexInputs: SystemCalculationInputs = {\r\n    segments: complexSegments,\r\n    systemType: 'supply',\r\n    designConditions: {\r\n      temperature: 75,\r\n      barometricPressure: 29.92,\r\n      altitude: 0\r\n    },\r\n    calculationOptions: {\r\n      includeElevationEffects: false,\r\n      includeTemperatureEffects: true,\r\n      frictionMethod: 'darcy_weisbach',\r\n      roundingPrecision: 3\r\n    }\r\n  };\r\n\r\n  const complexResult = SystemPressureCalculator.calculateSystemPressure(complexInputs);\r\n\r\n  console.log('\\nComplex System Results:');\r\n  console.log(`  Total Pressure Loss: ${complexResult.totalPressureLoss} in wg`);\r\n  console.log(`  Friction vs Minor Loss Ratio: ${(complexResult.totalFrictionLoss / complexResult.totalMinorLoss).toFixed(2)}`);\r\n  console.log(`  System Length: ${complexResult.totalLength} feet`);\r\n  console.log(`  Velocity Range: ${complexResult.minVelocity} - ${complexResult.maxVelocity} FPM`);\r\n\r\n  return complexResult;\r\n}\r\n\r\n/**\r\n * Example 4: Comparison with Existing AirDuctCalculator\r\n */\r\nfunction demonstrateIntegrationWithExisting() {\r\n  console.log('\\n=== INTEGRATION WITH EXISTING CALCULATOR ===');\r\n  \r\n  // Use existing AirDuctCalculator for basic sizing\r\n  const basicResult = AirDuctCalculator.calculateDuctSizing({\r\n    airflow: 1000,\r\n    ductType: 'round',\r\n    frictionRate: 0.08,\r\n    units: 'imperial',\r\n    material: 'galvanized_steel'\r\n  });\r\n\r\n  console.log('Basic Duct Sizing (AirDuctCalculator):');\r\n  console.log(`  Recommended Diameter: ${basicResult.diameter}\" round`);\r\n  console.log(`  Velocity: ${basicResult.velocity} FPM`);\r\n  console.log(`  Pressure Loss: ${basicResult.pressureLoss} in wg/100 ft`);\r\n\r\n  // Now use new system calculator for complete analysis\r\n  const enhancedSegments: DuctSegment[] = [\r\n    {\r\n      id: 'sized-duct',\r\n      type: 'straight',\r\n      ductShape: 'round',\r\n      length: 100, // 100 feet for comparison\r\n      diameter: basicResult.diameter,\r\n      airflow: 1000,\r\n      material: 'galvanized_steel'\r\n    }\r\n  ];\r\n\r\n  const enhancedInputs: SystemCalculationInputs = {\r\n    segments: enhancedSegments,\r\n    systemType: 'supply',\r\n    designConditions: {\r\n      temperature: 70,\r\n      barometricPressure: 29.92,\r\n      altitude: 0\r\n    },\r\n    calculationOptions: {\r\n      includeElevationEffects: false,\r\n      includeTemperatureEffects: true,\r\n      frictionMethod: 'darcy_weisbach',\r\n      roundingPrecision: 4\r\n    }\r\n  };\r\n\r\n  const enhancedResult = SystemPressureCalculator.calculateSystemPressure(enhancedInputs);\r\n\r\n  console.log('\\nEnhanced System Analysis (SystemPressureCalculator):');\r\n  console.log(`  Calculated Velocity: ${enhancedResult.averageVelocity} FPM`);\r\n  console.log(`  Friction Loss (100 ft): ${enhancedResult.totalFrictionLoss} in wg`);\r\n  console.log(`  Pressure Loss Rate: ${(enhancedResult.totalFrictionLoss).toFixed(4)} in wg/100 ft`);\r\n\r\n  console.log('\\nComparison:');\r\n  console.log(`  Velocity Match: ${Math.abs(basicResult.velocity - enhancedResult.averageVelocity) < 1 ? 'PASS' : 'FAIL'}`);\r\n  console.log(`  Pressure Loss Match: ${Math.abs(basicResult.pressureLoss - enhancedResult.totalFrictionLoss) < 0.001 ? 'PASS' : 'FAIL'}`);\r\n}\r\n\r\n/**\r\n * Main execution function\r\n */\r\nfunction runAllExamples() {\r\n  console.log('COMPREHENSIVE DUCT PHYSICS IMPLEMENTATION - INTEGRATION EXAMPLES');\r\n  console.log('================================================================');\r\n  \r\n  try {\r\n    // Run all examples\r\n    demonstrateFittingLossCalculation();\r\n    demonstrateSystemPressureCalculation();\r\n    demonstrateComplexSystem();\r\n    demonstrateIntegrationWithExisting();\r\n    \r\n    console.log('\\n=== ALL EXAMPLES COMPLETED SUCCESSFULLY ===');\r\n    console.log('Phase 1 implementation is working correctly!');\r\n    \r\n  } catch (error) {\r\n    console.error('\\n=== ERROR IN EXAMPLES ===');\r\n    console.error(error);\r\n    process.exit(1);\r\n  }\r\n}\r\n\r\n// Export functions for testing\r\nexport {\r\n  demonstrateFittingLossCalculation,\r\n  demonstrateSystemPressureCalculation,\r\n  demonstrateComplexSystem,\r\n  demonstrateIntegrationWithExisting,\r\n  runAllExamples\r\n};\r\n\r\n// Run examples if this file is executed directly\r\nif (require.main === module) {\r\n  runAllExamples();\r\n}\r\n"], "mappings": ";;AAAA;;;;;;;;AAAA;AAAA,SAAAA,eAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;MAAA;QAAAC,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAO,SAAA;MAAA;QAAAD,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;IAAA;IAAAW,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,cAAA;MAAAC,IAAA;MAAAC,QAAA;MAAAC,KAAA;MAAAC,OAAA;MAAAC,cAAA;MAAAC,OAAA;IAAA;IAAAC,eAAA;IAAA5B,IAAA;EAAA;EAAA,IAAA6B,QAAA,GAAA5B,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAA0B,QAAA,CAAA9B,IAAA,KAAA8B,QAAA,CAAA9B,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAA6B,QAAA,CAAA9B,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAA0B,cAAA,GAAAD,QAAA,CAAA9B,IAAA;EAAA;IASA;IAAAD,cAAA,YAAAA,CAAA;MAAA,OAAAgC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAhC,cAAA;AAAAA,cAAA,GAAAoB,CAAA;;;;;;AAwWEa,OAAA,CAAAC,iCAAA,GAAAA,iCAAA;AAAiC;AAAAlC,cAAA,GAAAoB,CAAA;AACjCa,OAAA,CAAAE,oCAAA,GAAAA,oCAAA;AAAoC;AAAAnC,cAAA,GAAAoB,CAAA;AACpCa,OAAA,CAAAG,wBAAA,GAAAA,wBAAA;AAAwB;AAAApC,cAAA,GAAAoB,CAAA;AACxBa,OAAA,CAAAI,kCAAA,GAAAA,kCAAA;AAAkC;AAAArC,cAAA,GAAAoB,CAAA;AAClCa,OAAA,CAAAK,cAAA,GAAAA,cAAA;AA5WF,MAAAC,uBAAA;AAAA;AAAA,CAAAvC,cAAA,GAAAoB,CAAA,OAAAoB,OAAA;AACA,MAAAC,0BAAA;AAAA;AAAA,CAAAzC,cAAA,GAAAoB,CAAA,OAAAoB,OAAA;AACA,MAAAE,mBAAA;AAAA;AAAA,CAAA1C,cAAA,GAAAoB,CAAA,OAAAoB,OAAA;AAEA;;;AAGA,SAASN,iCAAiCA,CAAA;EAAA;EAAAlC,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EACxCuB,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;EAEzD;EACA,MAAMC,WAAW;EAAA;EAAA,CAAA7C,cAAA,GAAAoB,CAAA,QAAyB;IACxCH,IAAI,EAAE,oBAAoB;IAC1B6B,SAAS,EAAE,OAAO;IAClBC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE;GACZ;EAED,MAAMC,QAAQ;EAAA;EAAA,CAAAjD,cAAA,GAAAoB,CAAA,QAAG,IAAI,EAAC,CAAC;EACvB,MAAM8B,MAAM;EAAA;EAAA,CAAAlD,cAAA,GAAAoB,CAAA,QAAGmB,uBAAA,CAAAY,qBAAqB,CAACC,oBAAoB,CAACP,WAAW,EAAEI,QAAQ,CAAC;EAAC;EAAAjD,cAAA,GAAAoB,CAAA;EAEjFuB,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;EAAC;EAAA5C,cAAA,GAAAoB,CAAA;EAC7CuB,OAAO,CAACC,GAAG,CAAC,eAAeM,MAAM,CAACG,OAAO,EAAE,CAAC;EAAC;EAAArD,cAAA,GAAAoB,CAAA;EAC7CuB,OAAO,CAACC,GAAG,CAAC,wBAAwBM,MAAM,CAACI,gBAAgB,CAACC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC;EAAC;EAAAvD,cAAA,GAAAoB,CAAA;EAChFuB,OAAO,CAACC,GAAG,CAAC,oBAAoBM,MAAM,CAACM,YAAY,CAACD,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC;EAAC;EAAAvD,cAAA,GAAAoB,CAAA;EACxEuB,OAAO,CAACC,GAAG,CAAC,oBAAoBM,MAAM,CAACO,aAAa,EAAE,CAAC;EAAC;EAAAzD,cAAA,GAAAoB,CAAA;EAExD,IAAI8B,MAAM,CAACQ,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;IAAA;IAAA3D,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAC9BuB,OAAO,CAACC,GAAG,CAAC,eAAeM,MAAM,CAACQ,QAAQ,CAACE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;EAC1D,CAAC;EAAA;EAAA;IAAA5D,cAAA,GAAAsB,CAAA;EAAA;EAAAtB,cAAA,GAAAoB,CAAA;EAED,IAAI8B,MAAM,CAACW,eAAe,CAACF,MAAM,GAAG,CAAC,EAAE;IAAA;IAAA3D,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAoB,CAAA;IACrCuB,OAAO,CAACC,GAAG,CAAC,sBAAsBM,MAAM,CAACW,eAAe,CAACD,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;EACxE,CAAC;EAAA;EAAA;IAAA5D,cAAA,GAAAsB,CAAA;EAAA;EAAAtB,cAAA,GAAAoB,CAAA;EAED,OAAO8B,MAAM;AACf;AAEA;;;;AAIA,SAASf,oCAAoCA,CAAA;EAAA;EAAAnC,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EAC3CuB,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;EAAC;EAAA5C,cAAA,GAAAoB,CAAA;EAClEuB,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;EAErE;EACA,MAAMkB,QAAQ;EAAA;EAAA,CAAA9D,cAAA,GAAAoB,CAAA,QAAkB;EAC9B;EACA;IACE2C,EAAE,EAAE,gBAAgB;IACpB9C,IAAI,EAAE,UAAU;IAChB6B,SAAS,EAAE,OAAO;IAClBa,MAAM,EAAE,EAAE;IACVZ,QAAQ,EAAE,EAAE;IACZiB,OAAO,EAAE,IAAI;IACbC,QAAQ,EAAE,kBAAkB;IAC5BC,KAAK,EAAE;GACR;EAED;EACA;IACEH,EAAE,EAAE,aAAa;IACjB9C,IAAI,EAAE,SAAS;IACf6B,SAAS,EAAE,OAAO;IAClBC,QAAQ,EAAE,EAAE;IACZiB,OAAO,EAAE,IAAI;IACbC,QAAQ,EAAE,kBAAkB;IAC5BE,aAAa,EAAE;MACblD,IAAI,EAAE,oBAAoB;MAC1B6B,SAAS,EAAE,OAAO;MAClBC,QAAQ,EAAE,EAAE;MACZC,SAAS,EAAE;KACZ;IACDkB,KAAK,EAAE;GACR;EAED;EACA;IACEH,EAAE,EAAE,gBAAgB;IACpB9C,IAAI,EAAE,UAAU;IAChB6B,SAAS,EAAE,OAAO;IAClBa,MAAM,EAAE,EAAE;IACVZ,QAAQ,EAAE,EAAE;IACZiB,OAAO,EAAE,IAAI;IACbC,QAAQ,EAAE,kBAAkB;IAC5BC,KAAK,EAAE;GACR,CACF;EAED;EACA,MAAME,MAAM;EAAA;EAAA,CAAApE,cAAA,GAAAoB,CAAA,QAA4B;IACtC0C,QAAQ;IACRO,UAAU,EAAE,QAAQ;IACpBC,gBAAgB,EAAE;MAChBC,WAAW,EAAE,EAAE;MAAE;MACjBC,kBAAkB,EAAE,KAAK;MAAE;MAC3BC,QAAQ,EAAE,CAAC,CAAC;KACb;IACDC,kBAAkB,EAAE;MAClBC,uBAAuB,EAAE,KAAK;MAC9BC,yBAAyB,EAAE,IAAI;MAC/BC,cAAc,EAAE,gBAAgB;MAChCC,iBAAiB,EAAE;;GAEtB;EAED;EACA,MAAMC,YAAY;EAAA;EAAA,CAAA/E,cAAA,GAAAoB,CAAA,QAAGqB,0BAAA,CAAAuC,wBAAwB,CAACC,uBAAuB,CAACb,MAAM,CAAC;EAAC;EAAApE,cAAA,GAAAoB,CAAA;EAE9EuB,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;EAAC;EAAA5C,cAAA,GAAAoB,CAAA;EACjCuB,OAAO,CAACC,GAAG,CAAC,0BAA0BmC,YAAY,CAACG,iBAAiB,QAAQ,CAAC;EAAC;EAAAlF,cAAA,GAAAoB,CAAA;EAC9EuB,OAAO,CAACC,GAAG,CAAC,0BAA0BmC,YAAY,CAACI,iBAAiB,QAAQ,CAAC;EAAC;EAAAnF,cAAA,GAAAoB,CAAA;EAC9EuB,OAAO,CAACC,GAAG,CAAC,uBAAuBmC,YAAY,CAACK,cAAc,QAAQ,CAAC;EAAC;EAAApF,cAAA,GAAAoB,CAAA;EACxEuB,OAAO,CAACC,GAAG,CAAC,mBAAmBmC,YAAY,CAACM,WAAW,OAAO,CAAC;EAAC;EAAArF,cAAA,GAAAoB,CAAA;EAChEuB,OAAO,CAACC,GAAG,CAAC,uBAAuBmC,YAAY,CAACO,eAAe,MAAM,CAAC;EAAC;EAAAtF,cAAA,GAAAoB,CAAA;EACvEuB,OAAO,CAACC,GAAG,CAAC,mBAAmBmC,YAAY,CAACQ,WAAW,MAAM,CAAC;EAAC;EAAAvF,cAAA,GAAAoB,CAAA;EAE/DuB,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;EAAC;EAAA5C,cAAA,GAAAoB,CAAA;EAClC2D,YAAY,CAACS,cAAc,CAACC,OAAO,CAAC,CAACC,OAAO,EAAEC,KAAK,KAAI;IAAA;IAAA3F,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACrDuB,OAAO,CAACC,GAAG,CAAC,aAAa+C,KAAK,GAAG,CAAC,KAAKD,OAAO,CAACE,SAAS,IAAI,CAAC;IAAC;IAAA5F,cAAA,GAAAoB,CAAA;IAC9DuB,OAAO,CAACC,GAAG,CAAC,aAAa8C,OAAO,CAACG,WAAW,EAAE,CAAC;IAAC;IAAA7F,cAAA,GAAAoB,CAAA;IAChDuB,OAAO,CAACC,GAAG,CAAC,iBAAiB8C,OAAO,CAACzC,QAAQ,MAAM,CAAC;IAAC;IAAAjD,cAAA,GAAAoB,CAAA;IACrDuB,OAAO,CAACC,GAAG,CAAC,0BAA0B8C,OAAO,CAACpC,gBAAgB,QAAQ,CAAC;IAAC;IAAAtD,cAAA,GAAAoB,CAAA;IACxEuB,OAAO,CAACC,GAAG,CAAC,sBAAsB8C,OAAO,CAACI,YAAY,QAAQ,CAAC;IAAC;IAAA9F,cAAA,GAAAoB,CAAA;IAChEuB,OAAO,CAACC,GAAG,CAAC,mBAAmB8C,OAAO,CAACK,SAAS,QAAQ,CAAC;IAAC;IAAA/F,cAAA,GAAAoB,CAAA;IAC1DuB,OAAO,CAACC,GAAG,CAAC,mBAAmB8C,OAAO,CAACM,SAAS,QAAQ,CAAC;IAAC;IAAAhG,cAAA,GAAAoB,CAAA;IAE1D,IAAIsE,OAAO,CAACrC,OAAO,EAAE;MAAA;MAAArD,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACnBuB,OAAO,CAACC,GAAG,CAAC,iBAAiB8C,OAAO,CAACrC,OAAO,EAAE,CAAC;IACjD,CAAC;IAAA;IAAA;MAAArD,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAED,IAAIsE,OAAO,CAAChC,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;MAAA;MAAA3D,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAC/BuB,OAAO,CAACC,GAAG,CAAC,iBAAiB8C,OAAO,CAAChC,QAAQ,CAACE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IAC7D,CAAC;IAAA;IAAA;MAAA5D,cAAA,GAAAsB,CAAA;IAAA;EACH,CAAC,CAAC;EAAC;EAAAtB,cAAA,GAAAoB,CAAA;EAEHuB,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;EAAC;EAAA5C,cAAA,GAAAoB,CAAA;EACpCuB,OAAO,CAACC,GAAG,CAAC,yBAAyBmC,YAAY,CAACkB,gBAAgB,CAACC,iBAAiB,EAAE,CAAC;EAAC;EAAAlG,cAAA,GAAAoB,CAAA;EACxFuB,OAAO,CAACC,GAAG,CAAC,yBAAyBmC,YAAY,CAACkB,gBAAgB,CAACE,iBAAiB,EAAE,CAAC;EAAC;EAAAnG,cAAA,GAAAoB,CAAA;EACxFuB,OAAO,CAACC,GAAG,CAAC,uBAAuBmC,YAAY,CAACkB,gBAAgB,CAACG,eAAe,EAAE,CAAC;EAAC;EAAApG,cAAA,GAAAoB,CAAA;EAEpF,IAAI2D,YAAY,CAACsB,cAAc,CAAC1C,MAAM,GAAG,CAAC,EAAE;IAAA;IAAA3D,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAC1CuB,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IAAC;IAAA5C,cAAA,GAAAoB,CAAA;IAClC2D,YAAY,CAACsB,cAAc,CAACZ,OAAO,CAACa,OAAO,IAAI;MAAA;MAAAtG,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAAA,OAAAuB,OAAO,CAACC,GAAG,CAAC,OAAO0D,OAAO,EAAE,CAAC;IAAD,CAAC,CAAC;EAC/E,CAAC;EAAA;EAAA;IAAAtG,cAAA,GAAAsB,CAAA;EAAA;EAAAtB,cAAA,GAAAoB,CAAA;EAED,IAAI2D,YAAY,CAACwB,qBAAqB,CAAC5C,MAAM,GAAG,CAAC,EAAE;IAAA;IAAA3D,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAoB,CAAA;IACjDuB,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;IAAC;IAAA5C,cAAA,GAAAoB,CAAA;IACzC2D,YAAY,CAACwB,qBAAqB,CAACd,OAAO,CAACe,GAAG,IAAI;MAAA;MAAAxG,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAAA,OAAAuB,OAAO,CAACC,GAAG,CAAC,OAAO4D,GAAG,EAAE,CAAC;IAAD,CAAC,CAAC;EAC9E,CAAC;EAAA;EAAA;IAAAxG,cAAA,GAAAsB,CAAA;EAAA;EAAAtB,cAAA,GAAAoB,CAAA;EAED,OAAO2D,YAAY;AACrB;AAEA;;;AAGA,SAAS3C,wBAAwBA,CAAA;EAAA;EAAApC,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EAC/BuB,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;EAAC;EAAA5C,cAAA,GAAAoB,CAAA;EAChDuB,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;EAEzE,MAAM6D,eAAe;EAAA;EAAA,CAAAzG,cAAA,GAAAoB,CAAA,QAAkB;EACrC;EACA;IACE2C,EAAE,EAAE,YAAY;IAChB9C,IAAI,EAAE,UAAU;IAChB6B,SAAS,EAAE,OAAO;IAClBa,MAAM,EAAE,EAAE;IACVZ,QAAQ,EAAE,EAAE;IACZiB,OAAO,EAAE,IAAI;IACbC,QAAQ,EAAE;GACX;EAED;EACA;IACEF,EAAE,EAAE,UAAU;IACd9C,IAAI,EAAE,SAAS;IACf6B,SAAS,EAAE,OAAO;IAClBC,QAAQ,EAAE,EAAE;IACZiB,OAAO,EAAE,IAAI;IACbC,QAAQ,EAAE,kBAAkB;IAC5BE,aAAa,EAAE;MACblD,IAAI,EAAE,wBAAwB;MAC9B6B,SAAS,EAAE,OAAO;MAClBC,QAAQ,EAAE,EAAE;MACZ2D,OAAO,EAAE,kBAAkB;MAC3B1D,SAAS,EAAE,KAAK,CAAC;;GAEpB;EAED;EACA;IACEe,EAAE,EAAE,qBAAqB;IACzB9C,IAAI,EAAE,SAAS;IACf6B,SAAS,EAAE,OAAO;IAClBC,QAAQ,EAAE,EAAE;IACZiB,OAAO,EAAE,IAAI;IAAE;IACfC,QAAQ,EAAE,kBAAkB;IAC5BE,aAAa,EAAE;MACblD,IAAI,EAAE,wBAAwB;MAC9B6B,SAAS,EAAE,OAAO;MAClBC,QAAQ,EAAE,EAAE;MACZC,SAAS,EAAE,KAAK,CAAC;;GAEpB;EAED;EACA;IACEe,EAAE,EAAE,aAAa;IACjB9C,IAAI,EAAE,UAAU;IAChB6B,SAAS,EAAE,OAAO;IAClBa,MAAM,EAAE,EAAE;IACVZ,QAAQ,EAAE,EAAE;IACZiB,OAAO,EAAE,IAAI;IACbC,QAAQ,EAAE;GACX;EAED;EACA;IACEF,EAAE,EAAE,aAAa;IACjB9C,IAAI,EAAE,SAAS;IACf6B,SAAS,EAAE,OAAO;IAClBC,QAAQ,EAAE,EAAE;IACZiB,OAAO,EAAE,IAAI;IACbC,QAAQ,EAAE,kBAAkB;IAC5BE,aAAa,EAAE;MACblD,IAAI,EAAE,oBAAoB;MAC1B6B,SAAS,EAAE,OAAO;MAClBC,QAAQ,EAAE,EAAE;MACZC,SAAS,EAAE;;GAEd;EAED;EACA;IACEe,EAAE,EAAE,WAAW;IACf9C,IAAI,EAAE,UAAU;IAChB6B,SAAS,EAAE,OAAO;IAClBa,MAAM,EAAE,CAAC;IACTZ,QAAQ,EAAE,EAAE;IACZiB,OAAO,EAAE,IAAI;IACbC,QAAQ,EAAE;GACX,CACF;EAED,MAAM0C,aAAa;EAAA;EAAA,CAAA3G,cAAA,GAAAoB,CAAA,QAA4B;IAC7C0C,QAAQ,EAAE2C,eAAe;IACzBpC,UAAU,EAAE,QAAQ;IACpBC,gBAAgB,EAAE;MAChBC,WAAW,EAAE,EAAE;MACfC,kBAAkB,EAAE,KAAK;MACzBC,QAAQ,EAAE;KACX;IACDC,kBAAkB,EAAE;MAClBC,uBAAuB,EAAE,KAAK;MAC9BC,yBAAyB,EAAE,IAAI;MAC/BC,cAAc,EAAE,gBAAgB;MAChCC,iBAAiB,EAAE;;GAEtB;EAED,MAAM8B,aAAa;EAAA;EAAA,CAAA5G,cAAA,GAAAoB,CAAA,QAAGqB,0BAAA,CAAAuC,wBAAwB,CAACC,uBAAuB,CAAC0B,aAAa,CAAC;EAAC;EAAA3G,cAAA,GAAAoB,CAAA;EAEtFuB,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;EAAC;EAAA5C,cAAA,GAAAoB,CAAA;EACzCuB,OAAO,CAACC,GAAG,CAAC,0BAA0BgE,aAAa,CAAC1B,iBAAiB,QAAQ,CAAC;EAAC;EAAAlF,cAAA,GAAAoB,CAAA;EAC/EuB,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAACgE,aAAa,CAACzB,iBAAiB,GAAGyB,aAAa,CAACxB,cAAc,EAAE7B,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;EAAC;EAAAvD,cAAA,GAAAoB,CAAA;EAC9HuB,OAAO,CAACC,GAAG,CAAC,oBAAoBgE,aAAa,CAACvB,WAAW,OAAO,CAAC;EAAC;EAAArF,cAAA,GAAAoB,CAAA;EAClEuB,OAAO,CAACC,GAAG,CAAC,qBAAqBgE,aAAa,CAACC,WAAW,MAAMD,aAAa,CAACrB,WAAW,MAAM,CAAC;EAAC;EAAAvF,cAAA,GAAAoB,CAAA;EAEjG,OAAOwF,aAAa;AACtB;AAEA;;;AAGA,SAASvE,kCAAkCA,CAAA;EAAA;EAAArC,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EACzCuB,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;EAE7D;EACA,MAAMkE,WAAW;EAAA;EAAA,CAAA9G,cAAA,GAAAoB,CAAA,QAAGsB,mBAAA,CAAAqE,iBAAiB,CAACC,mBAAmB,CAAC;IACxDhD,OAAO,EAAE,IAAI;IACbiD,QAAQ,EAAE,OAAO;IACjBC,YAAY,EAAE,IAAI;IAClBC,KAAK,EAAE,UAAU;IACjBlD,QAAQ,EAAE;GACX,CAAC;EAAC;EAAAjE,cAAA,GAAAoB,CAAA;EAEHuB,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;EAAC;EAAA5C,cAAA,GAAAoB,CAAA;EACtDuB,OAAO,CAACC,GAAG,CAAC,2BAA2BkE,WAAW,CAAC/D,QAAQ,SAAS,CAAC;EAAC;EAAA/C,cAAA,GAAAoB,CAAA;EACtEuB,OAAO,CAACC,GAAG,CAAC,eAAekE,WAAW,CAAC7D,QAAQ,MAAM,CAAC;EAAC;EAAAjD,cAAA,GAAAoB,CAAA;EACvDuB,OAAO,CAACC,GAAG,CAAC,oBAAoBkE,WAAW,CAACtD,YAAY,eAAe,CAAC;EAExE;EACA,MAAM4D,gBAAgB;EAAA;EAAA,CAAApH,cAAA,GAAAoB,CAAA,QAAkB,CACtC;IACE2C,EAAE,EAAE,YAAY;IAChB9C,IAAI,EAAE,UAAU;IAChB6B,SAAS,EAAE,OAAO;IAClBa,MAAM,EAAE,GAAG;IAAE;IACbZ,QAAQ,EAAE+D,WAAW,CAAC/D,QAAQ;IAC9BiB,OAAO,EAAE,IAAI;IACbC,QAAQ,EAAE;GACX,CACF;EAED,MAAMoD,cAAc;EAAA;EAAA,CAAArH,cAAA,GAAAoB,CAAA,QAA4B;IAC9C0C,QAAQ,EAAEsD,gBAAgB;IAC1B/C,UAAU,EAAE,QAAQ;IACpBC,gBAAgB,EAAE;MAChBC,WAAW,EAAE,EAAE;MACfC,kBAAkB,EAAE,KAAK;MACzBC,QAAQ,EAAE;KACX;IACDC,kBAAkB,EAAE;MAClBC,uBAAuB,EAAE,KAAK;MAC9BC,yBAAyB,EAAE,IAAI;MAC/BC,cAAc,EAAE,gBAAgB;MAChCC,iBAAiB,EAAE;;GAEtB;EAED,MAAMwC,cAAc;EAAA;EAAA,CAAAtH,cAAA,GAAAoB,CAAA,QAAGqB,0BAAA,CAAAuC,wBAAwB,CAACC,uBAAuB,CAACoC,cAAc,CAAC;EAAC;EAAArH,cAAA,GAAAoB,CAAA;EAExFuB,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;EAAC;EAAA5C,cAAA,GAAAoB,CAAA;EACtEuB,OAAO,CAACC,GAAG,CAAC,0BAA0B0E,cAAc,CAAChC,eAAe,MAAM,CAAC;EAAC;EAAAtF,cAAA,GAAAoB,CAAA;EAC5EuB,OAAO,CAACC,GAAG,CAAC,6BAA6B0E,cAAc,CAACnC,iBAAiB,QAAQ,CAAC;EAAC;EAAAnF,cAAA,GAAAoB,CAAA;EACnFuB,OAAO,CAACC,GAAG,CAAC,yBAA0B0E,cAAc,CAACnC,iBAAiB,CAAE5B,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;EAAC;EAAAvD,cAAA,GAAAoB,CAAA;EAEnGuB,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;EAAC;EAAA5C,cAAA,GAAAoB,CAAA;EAC7BuB,OAAO,CAACC,GAAG,CAAC,qBAAqB2E,IAAI,CAACC,GAAG,CAACV,WAAW,CAAC7D,QAAQ,GAAGqE,cAAc,CAAChC,eAAe,CAAC,GAAG,CAAC;EAAA;EAAA,CAAAtF,cAAA,GAAAsB,CAAA,UAAG,MAAM;EAAA;EAAA,CAAAtB,cAAA,GAAAsB,CAAA,UAAG,MAAM,GAAE,CAAC;EAAC;EAAAtB,cAAA,GAAAoB,CAAA;EAC1HuB,OAAO,CAACC,GAAG,CAAC,0BAA0B2E,IAAI,CAACC,GAAG,CAACV,WAAW,CAACtD,YAAY,GAAG8D,cAAc,CAACnC,iBAAiB,CAAC,GAAG,KAAK;EAAA;EAAA,CAAAnF,cAAA,GAAAsB,CAAA,UAAG,MAAM;EAAA;EAAA,CAAAtB,cAAA,GAAAsB,CAAA,UAAG,MAAM,GAAE,CAAC;AAC1I;AAEA;;;AAGA,SAASgB,cAAcA,CAAA;EAAA;EAAAtC,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EACrBuB,OAAO,CAACC,GAAG,CAAC,kEAAkE,CAAC;EAAC;EAAA5C,cAAA,GAAAoB,CAAA;EAChFuB,OAAO,CAACC,GAAG,CAAC,kEAAkE,CAAC;EAAC;EAAA5C,cAAA,GAAAoB,CAAA;EAEhF,IAAI;IAAA;IAAApB,cAAA,GAAAoB,CAAA;IACF;IACAc,iCAAiC,EAAE;IAAC;IAAAlC,cAAA,GAAAoB,CAAA;IACpCe,oCAAoC,EAAE;IAAC;IAAAnC,cAAA,GAAAoB,CAAA;IACvCgB,wBAAwB,EAAE;IAAC;IAAApC,cAAA,GAAAoB,CAAA;IAC3BiB,kCAAkC,EAAE;IAAC;IAAArC,cAAA,GAAAoB,CAAA;IAErCuB,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;IAAC;IAAA5C,cAAA,GAAAoB,CAAA;IAC7DuB,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;EAE7D,CAAC,CAAC,OAAO6E,KAAK,EAAE;IAAA;IAAAzH,cAAA,GAAAoB,CAAA;IACduB,OAAO,CAAC8E,KAAK,CAAC,6BAA6B,CAAC;IAAC;IAAAzH,cAAA,GAAAoB,CAAA;IAC7CuB,OAAO,CAAC8E,KAAK,CAACA,KAAK,CAAC;IAAC;IAAAzH,cAAA,GAAAoB,CAAA;IACrBsG,OAAO,CAACC,IAAI,CAAC,CAAC,CAAC;EACjB;AACF;AAWA;AAAA;AAAA3H,cAAA,GAAAoB,CAAA;AACA,IAAIoB,OAAO,CAACoF,IAAI,KAAKC,MAAM,EAAE;EAAA;EAAA7H,cAAA,GAAAsB,CAAA;EAAAtB,cAAA,GAAAoB,CAAA;EAC3BkB,cAAc,EAAE;AAClB,CAAC;AAAA;AAAA;EAAAtC,cAAA,GAAAsB,CAAA;AAAA", "ignoreList": []}