{"version": 3, "names": ["cov_1rnufq1amz", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "name", "decl", "loc", "branchMap", "type", "locations", "s", "f", "b", "inputSourceMap", "file", "mappings", "names", "sources", "sourcesContent", "version", "_coverageSchema", "coverage", "actualCoverage", "FittingCategory", "exports", "FittingComplexity", "PerformanceClass", "AdvancedFittingTypes", "FlowPattern", "CalculationMethod"], "sources": ["C:\\Users\\<USER>\\Downloads\\SizeWise_Suite_App\\backend\\services\\calculations\\types\\AdvancedFittingTypes.ts"], "sourcesContent": ["/**\r\n * Advanced Fitting Types for Phase 3 Duct Physics Implementation\r\n * \r\n * This module defines comprehensive TypeScript interfaces for advanced HVAC fitting\r\n * configurations, supporting complex multi-parameter calculations, performance curves,\r\n * and interaction effects.\r\n * \r\n * @version 3.0.0\r\n * <AUTHOR> Suite Development Team\r\n */\r\n\r\nimport { FittingConfiguration, FittingLossResult } from '../FittingLossCalculator';\r\n\r\n// ============================================================================\r\n// Core Advanced Fitting Configuration\r\n// ============================================================================\r\n\r\nexport interface AdvancedFittingConfiguration extends FittingConfiguration {\r\n  // Core identification\r\n  id: string;\r\n  version: string;\r\n  category: FittingCategory;\r\n  complexity: FittingComplexity;\r\n  \r\n  // Performance characteristics\r\n  performanceClass: PerformanceClass;\r\n  flowCharacteristics: FlowCharacteristics;\r\n  pressureLossProfile: PressureLossProfile;\r\n  \r\n  // Physical properties\r\n  physicalProperties: FittingPhysicalProperties;\r\n  installationRequirements: InstallationRequirements;\r\n  \r\n  // Validation and constraints\r\n  validationRules: ValidationRule[];\r\n  compatibilityMatrix: CompatibilityMatrix;\r\n  \r\n  // Metadata\r\n  manufacturer?: string;\r\n  model?: string;\r\n  certifications: string[];\r\n  lastUpdated: Date;\r\n}\r\n\r\n// ============================================================================\r\n// Enumerations\r\n// ============================================================================\r\n\r\nexport enum FittingCategory {\r\n  TRANSITION = 'transition',\r\n  TERMINAL = 'terminal',\r\n  JUNCTION = 'junction',\r\n  CONTROL = 'control',\r\n  SPECIALTY = 'specialty'\r\n}\r\n\r\nexport enum FittingComplexity {\r\n  SIMPLE = 'simple',           // Single K-factor\r\n  COMPLEX = 'complex',         // Multiple parameters\r\n  VARIABLE = 'variable',       // Flow-dependent\r\n  CUSTOM = 'custom'            // User-defined\r\n}\r\n\r\nexport enum PerformanceClass {\r\n  STANDARD = 'standard',       // Standard commercial\r\n  HIGH_VELOCITY = 'high_velocity', // >2500 FPM\r\n  LOW_PRESSURE = 'low_pressure',   // <2\" w.g.\r\n  INDUSTRIAL = 'industrial',   // Heavy-duty applications\r\n  PRECISION = 'precision'      // Laboratory/cleanroom\r\n}\r\n\r\nexport enum AdvancedFittingTypes {\r\n  // Transitions\r\n  TRANSITION_RECT_TO_ROUND = 'transition_rect_to_round',\r\n  TRANSITION_ROUND_TO_RECT = 'transition_round_to_rect',\r\n  TRANSITION_GRADUAL = 'transition_gradual',\r\n  TRANSITION_ABRUPT = 'transition_abrupt',\r\n  \r\n  // Terminals\r\n  VAV_BOX = 'vav_box',\r\n  CAV_BOX = 'cav_box',\r\n  DIFFUSER_LINEAR = 'diffuser_linear',\r\n  DIFFUSER_CEILING = 'diffuser_ceiling',\r\n  GRILLE_RETURN = 'grille_return',\r\n  GRILLE_SUPPLY = 'grille_supply',\r\n  \r\n  // Controls\r\n  FIRE_DAMPER = 'fire_damper',\r\n  SMOKE_DAMPER = 'smoke_damper',\r\n  VOLUME_DAMPER = 'volume_damper',\r\n  BACKDRAFT_DAMPER = 'backdraft_damper',\r\n  \r\n  // Specialty\r\n  EXHAUST_HOOD = 'exhaust_hood',\r\n  SOUND_ATTENUATOR = 'sound_attenuator',\r\n  HEAT_EXCHANGER = 'heat_exchanger',\r\n  FILTER_SECTION = 'filter_section'\r\n}\r\n\r\nexport enum FlowPattern {\r\n  STRAIGHT_THROUGH = 'straight_through',\r\n  BRANCH_90 = 'branch_90',\r\n  BRANCH_45 = 'branch_45',\r\n  CONVERGING = 'converging',\r\n  DIVERGING = 'diverging',\r\n  SWIRL = 'swirl'\r\n}\r\n\r\nexport enum CalculationMethod {\r\n  SINGLE_K_FACTOR = 'single_k_factor',\r\n  MULTI_PARAMETER = 'multi_parameter',\r\n  PERFORMANCE_CURVE = 'performance_curve',\r\n  CFD_DERIVED = 'cfd_derived',\r\n  EMPIRICAL = 'empirical'\r\n}\r\n\r\n// ============================================================================\r\n// Flow Characteristics\r\n// ============================================================================\r\n\r\nexport interface FlowCharacteristics {\r\n  nominalFlow: FlowRange;\r\n  operatingRange: FlowRange;\r\n  turndownRatio: number;\r\n  flowPattern: FlowPattern;\r\n  velocityProfile: VelocityProfile;\r\n  turbulenceFactors: TurbulenceFactors;\r\n}\r\n\r\nexport interface FlowRange {\r\n  minimum: number;    // CFM\r\n  maximum: number;    // CFM\r\n  optimal: number;    // CFM\r\n  units: 'cfm' | 'l/s' | 'm3/h';\r\n}\r\n\r\nexport interface VelocityProfile {\r\n  uniformityIndex: number;     // 0-1, where 1 is perfectly uniform\r\n  peakVelocityRatio: number;   // Peak velocity / Average velocity\r\n  boundaryLayerThickness: number; // inches\r\n  flowSeparationRisk: 'low' | 'medium' | 'high';\r\n}\r\n\r\nexport interface TurbulenceFactors {\r\n  turbulenceIntensity: number; // %\r\n  mixingFactor: number;        // 0-1\r\n  pressureRecoveryFactor: number; // 0-1\r\n  downstreamDevelopmentLength: number; // diameters\r\n}\r\n\r\n// ============================================================================\r\n// Pressure Loss Profile\r\n// ============================================================================\r\n\r\nexport interface PressureLossProfile {\r\n  calculationMethod: CalculationMethod;\r\n  kFactorData: KFactorData;\r\n  performanceCurves?: PerformanceCurve[];\r\n  correctionFactors: CorrectionFactors;\r\n  uncertaintyBounds: UncertaintyBounds;\r\n}\r\n\r\nexport interface KFactorData {\r\n  baseKFactor: number;\r\n  parameterDependencies: ParameterDependency[];\r\n  reynoldsCorrection?: ReynoldsCorrection;\r\n  geometryCorrections: GeometryCorrection[];\r\n}\r\n\r\nexport interface ParameterDependency {\r\n  parameter: string;\r\n  relationship: 'linear' | 'polynomial' | 'exponential' | 'lookup';\r\n  coefficients: number[];\r\n  validRange: [number, number];\r\n  description: string;\r\n}\r\n\r\nexport interface ReynoldsCorrection {\r\n  enabled: boolean;\r\n  method: 'colebrook' | 'blasius' | 'custom';\r\n  coefficients: number[];\r\n  validRange: [number, number];\r\n}\r\n\r\nexport interface GeometryCorrection {\r\n  parameter: string;\r\n  correctionFactor: number;\r\n  applicableRange: [number, number];\r\n  description: string;\r\n}\r\n\r\nexport interface PerformanceCurve {\r\n  parameter: string;\r\n  units: string;\r\n  dataPoints: DataPoint[];\r\n  interpolationMethod: 'linear' | 'cubic' | 'spline';\r\n  extrapolationAllowed: boolean;\r\n}\r\n\r\nexport interface DataPoint {\r\n  x: number;\r\n  y: number;\r\n  uncertainty?: number;\r\n}\r\n\r\nexport interface CorrectionFactors {\r\n  temperatureCorrection: boolean;\r\n  densityCorrection: boolean;\r\n  viscosityCorrection: boolean;\r\n  roughnessCorrection: boolean;\r\n  installationCorrection: boolean;\r\n}\r\n\r\nexport interface UncertaintyBounds {\r\n  lowerBound: number;  // % below nominal\r\n  upperBound: number;  // % above nominal\r\n  confidenceLevel: number; // %\r\n  basisOfUncertainty: string;\r\n}\r\n\r\n// ============================================================================\r\n// Physical Properties and Installation\r\n// ============================================================================\r\n\r\nexport interface FittingPhysicalProperties {\r\n  dimensions: FittingDimensions;\r\n  materials: MaterialProperties[];\r\n  weight: number; // lbs\r\n  thermalProperties: ThermalProperties;\r\n  acousticProperties: AcousticProperties;\r\n}\r\n\r\nexport interface FittingDimensions {\r\n  length: number;     // inches\r\n  width: number;      // inches\r\n  height: number;     // inches\r\n  inletDiameter?: number;  // inches\r\n  outletDiameter?: number; // inches\r\n  connectionType: 'flanged' | 'slip' | 'welded' | 'threaded';\r\n}\r\n\r\nexport interface MaterialProperties {\r\n  material: string;\r\n  thickness: number;  // inches\r\n  roughness: number;  // inches\r\n  corrosionResistance: 'low' | 'medium' | 'high';\r\n  temperatureRating: [number, number]; // [min, max] °F\r\n}\r\n\r\nexport interface ThermalProperties {\r\n  thermalConductivity: number; // BTU/(hr·ft·°F)\r\n  heatCapacity: number;        // BTU/(lb·°F)\r\n  thermalExpansion: number;    // in/(in·°F)\r\n  insulationRequired: boolean;\r\n}\r\n\r\nexport interface AcousticProperties {\r\n  insertionLoss: number[];     // dB at octave bands\r\n  transmissionLoss: number[];  // dB at octave bands\r\n  regeneratedNoise: number[];  // dB at octave bands\r\n  octaveBands: number[];       // Hz\r\n}\r\n\r\nexport interface InstallationRequirements {\r\n  minimumStraightLength: {\r\n    upstream: number;   // diameters\r\n    downstream: number; // diameters\r\n  };\r\n  supportRequirements: SupportRequirement[];\r\n  accessRequirements: AccessRequirement[];\r\n  clearanceRequirements: ClearanceRequirement[];\r\n  specialTools: string[];\r\n}\r\n\r\nexport interface SupportRequirement {\r\n  location: string;\r\n  loadCapacity: number; // lbs\r\n  supportType: 'hanger' | 'bracket' | 'stand' | 'spring';\r\n}\r\n\r\nexport interface AccessRequirement {\r\n  purpose: 'maintenance' | 'inspection' | 'adjustment';\r\n  minimumClearance: number; // inches\r\n  frequency: 'daily' | 'weekly' | 'monthly' | 'annual';\r\n}\r\n\r\nexport interface ClearanceRequirement {\r\n  direction: 'top' | 'bottom' | 'left' | 'right' | 'front' | 'back';\r\n  minimumDistance: number; // inches\r\n  reason: string;\r\n}\r\n\r\n// ============================================================================\r\n// Validation and Compatibility\r\n// ============================================================================\r\n\r\nexport interface ValidationRule {\r\n  ruleId: string;\r\n  description: string;\r\n  severity: 'error' | 'warning' | 'info';\r\n  condition: ValidationCondition;\r\n  message: string;\r\n}\r\n\r\nexport interface ValidationCondition {\r\n  parameter: string;\r\n  operator: '>' | '<' | '=' | '>=' | '<=' | '!=' | 'in' | 'not_in';\r\n  value: number | string | number[] | string[];\r\n}\r\n\r\nexport interface CompatibilityMatrix {\r\n  compatibleWith: string[];      // Fitting IDs\r\n  incompatibleWith: string[];    // Fitting IDs\r\n  requiresSpecialHandling: string[]; // Fitting IDs\r\n  interactionEffects: InteractionEffect[];\r\n}\r\n\r\nexport interface InteractionEffect {\r\n  adjacentFittingType: string;\r\n  distance: number;              // diameters\r\n  effect: 'increase' | 'decrease' | 'neutral';\r\n  magnitude: number;             // multiplier\r\n  description: string;\r\n}\r\n\r\n// ============================================================================\r\n// Flow Conditions and System Context\r\n// ============================================================================\r\n\r\nexport interface FlowConditions {\r\n  velocity: number;           // FPM\r\n  volumeFlow: number;         // CFM\r\n  massFlow: number;           // lb/min\r\n  reynoldsNumber: number;\r\n  airDensity: number;         // lb/ft³\r\n  viscosity: number;          // lb/(ft·s)\r\n  temperature: number;        // °F\r\n  pressure: number;           // in Hg\r\n  turbulenceIntensity: number; // %\r\n}\r\n\r\nexport interface SystemContext {\r\n  systemId: string;\r\n  adjacentFittings: Map<string, AdvancedFittingConfiguration>;\r\n  ductGeometry: DuctGeometry;\r\n  flowDistribution: FlowDistribution;\r\n  \r\n  getUpstreamFittings(fittingId: string, distance: number): AdvancedFittingConfiguration[];\r\n  getDownstreamFittings(fittingId: string, distance: number): AdvancedFittingConfiguration[];\r\n  getLocalFlowConditions(fittingId: string): FlowConditions;\r\n}\r\n\r\nexport interface DuctGeometry {\r\n  shape: 'round' | 'rectangular';\r\n  dimensions: number[];        // [diameter] or [width, height]\r\n  roughness: number;          // inches\r\n  material: string;\r\n}\r\n\r\nexport interface FlowDistribution {\r\n  totalFlow: number;          // CFM\r\n  branchFlows: Map<string, number>; // branch ID -> CFM\r\n  pressureDistribution: Map<string, number>; // location -> pressure\r\n}\r\n\r\n// ============================================================================\r\n// Advanced Calculation Results\r\n// ============================================================================\r\n\r\nexport interface AdvancedFittingLossResult extends FittingLossResult {\r\n  calculationMethod: CalculationMethod;\r\n  interactionEffects: InteractionEffects;\r\n  performanceMetrics: PerformanceMetrics;\r\n  validationResults: ValidationResults;\r\n  recommendations: Recommendation[];\r\n}\r\n\r\nexport interface InteractionEffects {\r\n  totalInteractionFactor: number;\r\n  individualInteractions: FittingInteraction[];\r\n  significantInteractions: FittingInteraction[];\r\n}\r\n\r\nexport interface FittingInteraction {\r\n  adjacentFittingId: string;\r\n  distance: number;           // diameters\r\n  factor: number;             // pressure loss multiplier\r\n  type: 'upstream' | 'downstream';\r\n  significance: 'low' | 'medium' | 'high';\r\n}\r\n\r\nexport interface PerformanceMetrics {\r\n  efficiency: number;         // %\r\n  noiseGeneration: number;    // dB\r\n  energyLoss: number;         // BTU/hr\r\n  flowUniformity: number;     // %\r\n  pressureRecovery: number;   // %\r\n}\r\n\r\nexport interface ValidationResults {\r\n  isValid: boolean;\r\n  errors: ValidationError[];\r\n  warnings: ValidationWarning[];\r\n  complianceStatus: ComplianceStatus;\r\n}\r\n\r\nexport interface ValidationError {\r\n  code: string;\r\n  message: string;\r\n  parameter: string;\r\n  value: number | string;\r\n}\r\n\r\nexport interface ValidationWarning {\r\n  code: string;\r\n  message: string;\r\n  severity: 'low' | 'medium' | 'high';\r\n  recommendation: string;\r\n}\r\n\r\nexport interface ComplianceStatus {\r\n  smacnaCompliant: boolean;\r\n  ashraeCompliant: boolean;\r\n  localCodeCompliant: boolean;\r\n  customStandardsCompliant: boolean;\r\n}\r\n\r\nexport interface Recommendation {\r\n  type: 'optimization' | 'maintenance' | 'replacement' | 'adjustment';\r\n  priority: 'low' | 'medium' | 'high' | 'critical';\r\n  description: string;\r\n  expectedBenefit: string;\r\n  implementationCost: 'low' | 'medium' | 'high';\r\n}\r\n"], "mappings": ";;AAAA;;;;;;;;;;AAAA;AAAA,SAAAA,eAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;MAAA;QAAAC,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAO,SAAA;MAAA;QAAAD,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAU,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,cAAA;MAAAC,IAAA;MAAAC,QAAA;MAAAC,KAAA;MAAAC,OAAA;MAAAC,cAAA;MAAAC,OAAA;IAAA;IAAAC,eAAA;IAAA3B,IAAA;EAAA;EAAA,IAAA4B,QAAA,GAAA3B,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAAyB,QAAA,CAAA7B,IAAA,KAAA6B,QAAA,CAAA7B,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAA4B,QAAA,CAAA7B,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAAyB,cAAA,GAAAD,QAAA,CAAA7B,IAAA;EAAA;IA8CA;IAAAD,cAAA,YAAAA,CAAA;MAAA,OAAA+B,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAA/B,cAAA;AAAAA,cAAA,GAAAmB,CAAA;;;;;;;AAFA;AACA;AACA;AAEA,IAAYa,eAMX;AAAA;AAAAhC,cAAA,GAAAmB,CAAA;AAND,WAAYa,eAAe;EAAA;EAAAhC,cAAA,GAAAoB,CAAA;EAAApB,cAAA,GAAAmB,CAAA;EACzBa,eAAA,6BAAyB;EAAA;EAAAhC,cAAA,GAAAmB,CAAA;EACzBa,eAAA,yBAAqB;EAAA;EAAAhC,cAAA,GAAAmB,CAAA;EACrBa,eAAA,yBAAqB;EAAA;EAAAhC,cAAA,GAAAmB,CAAA;EACrBa,eAAA,uBAAmB;EAAA;EAAAhC,cAAA,GAAAmB,CAAA;EACnBa,eAAA,2BAAuB;AACzB,CAAC;AANW;AAAA,CAAAhC,cAAA,GAAAqB,CAAA,UAAAW,eAAe;AAAA;AAAA,CAAAhC,cAAA,GAAAqB,CAAA,UAAAY,OAAA,CAAAD,eAAA,GAAfA,eAAe;AAQ3B,IAAYE,iBAKX;AAAA;AAAAlC,cAAA,GAAAmB,CAAA;AALD,WAAYe,iBAAiB;EAAA;EAAAlC,cAAA,GAAAoB,CAAA;EAAApB,cAAA,GAAAmB,CAAA;EAC3Be,iBAAA,qBAAiB;EAAA;EAAAlC,cAAA,GAAAmB,CAAA;EACjBe,iBAAA,uBAAmB;EAAA;EAAAlC,cAAA,GAAAmB,CAAA;EACnBe,iBAAA,yBAAqB;EAAA;EAAAlC,cAAA,GAAAmB,CAAA;EACrBe,iBAAA,qBAAiB,EAAY;AAC/B,CAAC;AALW;AAAA,CAAAlC,cAAA,GAAAqB,CAAA,UAAAa,iBAAiB;AAAA;AAAA,CAAAlC,cAAA,GAAAqB,CAAA,UAAAY,OAAA,CAAAC,iBAAA,GAAjBA,iBAAiB;AAO7B,IAAYC,gBAMX;AAAA;AAAAnC,cAAA,GAAAmB,CAAA;AAND,WAAYgB,gBAAgB;EAAA;EAAAnC,cAAA,GAAAoB,CAAA;EAAApB,cAAA,GAAAmB,CAAA;EAC1BgB,gBAAA,yBAAqB;EAAA;EAAAnC,cAAA,GAAAmB,CAAA;EACrBgB,gBAAA,mCAA+B;EAAA;EAAAnC,cAAA,GAAAmB,CAAA;EAC/BgB,gBAAA,iCAA6B;EAAA;EAAAnC,cAAA,GAAAmB,CAAA;EAC7BgB,gBAAA,6BAAyB;EAAA;EAAAnC,cAAA,GAAAmB,CAAA;EACzBgB,gBAAA,2BAAuB,EAAM;AAC/B,CAAC;AANW;AAAA,CAAAnC,cAAA,GAAAqB,CAAA,UAAAc,gBAAgB;AAAA;AAAA,CAAAnC,cAAA,GAAAqB,CAAA,UAAAY,OAAA,CAAAE,gBAAA,GAAhBA,gBAAgB;AAQ5B,IAAYC,oBA0BX;AAAA;AAAApC,cAAA,GAAAmB,CAAA;AA1BD,WAAYiB,oBAAoB;EAAA;EAAApC,cAAA,GAAAoB,CAAA;EAAApB,cAAA,GAAAmB,CAAA;EAC9B;EACAiB,oBAAA,yDAAqD;EAAA;EAAApC,cAAA,GAAAmB,CAAA;EACrDiB,oBAAA,yDAAqD;EAAA;EAAApC,cAAA,GAAAmB,CAAA;EACrDiB,oBAAA,6CAAyC;EAAA;EAAApC,cAAA,GAAAmB,CAAA;EACzCiB,oBAAA,2CAAuC;EAEvC;EAAA;EAAApC,cAAA,GAAAmB,CAAA;EACAiB,oBAAA,uBAAmB;EAAA;EAAApC,cAAA,GAAAmB,CAAA;EACnBiB,oBAAA,uBAAmB;EAAA;EAAApC,cAAA,GAAAmB,CAAA;EACnBiB,oBAAA,uCAAmC;EAAA;EAAApC,cAAA,GAAAmB,CAAA;EACnCiB,oBAAA,yCAAqC;EAAA;EAAApC,cAAA,GAAAmB,CAAA;EACrCiB,oBAAA,mCAA+B;EAAA;EAAApC,cAAA,GAAAmB,CAAA;EAC/BiB,oBAAA,mCAA+B;EAE/B;EAAA;EAAApC,cAAA,GAAAmB,CAAA;EACAiB,oBAAA,+BAA2B;EAAA;EAAApC,cAAA,GAAAmB,CAAA;EAC3BiB,oBAAA,iCAA6B;EAAA;EAAApC,cAAA,GAAAmB,CAAA;EAC7BiB,oBAAA,mCAA+B;EAAA;EAAApC,cAAA,GAAAmB,CAAA;EAC/BiB,oBAAA,yCAAqC;EAErC;EAAA;EAAApC,cAAA,GAAAmB,CAAA;EACAiB,oBAAA,iCAA6B;EAAA;EAAApC,cAAA,GAAAmB,CAAA;EAC7BiB,oBAAA,yCAAqC;EAAA;EAAApC,cAAA,GAAAmB,CAAA;EACrCiB,oBAAA,qCAAiC;EAAA;EAAApC,cAAA,GAAAmB,CAAA;EACjCiB,oBAAA,qCAAiC;AACnC,CAAC;AA1BW;AAAA,CAAApC,cAAA,GAAAqB,CAAA,UAAAe,oBAAoB;AAAA;AAAA,CAAApC,cAAA,GAAAqB,CAAA,UAAAY,OAAA,CAAAG,oBAAA,GAApBA,oBAAoB;AA4BhC,IAAYC,WAOX;AAAA;AAAArC,cAAA,GAAAmB,CAAA;AAPD,WAAYkB,WAAW;EAAA;EAAArC,cAAA,GAAAoB,CAAA;EAAApB,cAAA,GAAAmB,CAAA;EACrBkB,WAAA,yCAAqC;EAAA;EAAArC,cAAA,GAAAmB,CAAA;EACrCkB,WAAA,2BAAuB;EAAA;EAAArC,cAAA,GAAAmB,CAAA;EACvBkB,WAAA,2BAAuB;EAAA;EAAArC,cAAA,GAAAmB,CAAA;EACvBkB,WAAA,6BAAyB;EAAA;EAAArC,cAAA,GAAAmB,CAAA;EACzBkB,WAAA,2BAAuB;EAAA;EAAArC,cAAA,GAAAmB,CAAA;EACvBkB,WAAA,mBAAe;AACjB,CAAC;AAPW;AAAA,CAAArC,cAAA,GAAAqB,CAAA,UAAAgB,WAAW;AAAA;AAAA,CAAArC,cAAA,GAAAqB,CAAA,UAAAY,OAAA,CAAAI,WAAA,GAAXA,WAAW;AASvB,IAAYC,iBAMX;AAAA;AAAAtC,cAAA,GAAAmB,CAAA;AAND,WAAYmB,iBAAiB;EAAA;EAAAtC,cAAA,GAAAoB,CAAA;EAAApB,cAAA,GAAAmB,CAAA;EAC3BmB,iBAAA,uCAAmC;EAAA;EAAAtC,cAAA,GAAAmB,CAAA;EACnCmB,iBAAA,uCAAmC;EAAA;EAAAtC,cAAA,GAAAmB,CAAA;EACnCmB,iBAAA,2CAAuC;EAAA;EAAAtC,cAAA,GAAAmB,CAAA;EACvCmB,iBAAA,+BAA2B;EAAA;EAAAtC,cAAA,GAAAmB,CAAA;EAC3BmB,iBAAA,2BAAuB;AACzB,CAAC;AANW;AAAA,CAAAtC,cAAA,GAAAqB,CAAA,UAAAiB,iBAAiB;AAAA;AAAA,CAAAtC,cAAA,GAAAqB,CAAA,UAAAY,OAAA,CAAAK,iBAAA,GAAjBA,iBAAiB", "ignoreList": []}