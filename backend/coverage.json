{"meta": {"format": 3, "version": "7.10.1", "timestamp": "2025-08-03T00:21:18.550373", "branch_coverage": false, "show_contexts": false}, "files": {"__init__.py": {"executed_lines": [1], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [1], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "analytics\\advanced_reporting_analytics.py": {"executed_lines": [1, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16], "summary": {"covered_lines": 11, "num_statements": 358, "percent_covered": 3.0726256983240225, "percent_covered_display": "3", "missing_lines": 347, "excluded_lines": 0}, "missing_lines": [17, 20, 21, 23, 25, 26, 27, 28, 29, 30, 31, 32, 33, 35, 37, 38, 39, 40, 41, 42, 43, 44, 46, 48, 49, 50, 51, 52, 53, 54, 56, 58, 59, 60, 61, 62, 63, 65, 66, 68, 69, 70, 71, 72, 73, 74, 76, 77, 79, 80, 81, 82, 83, 84, 85, 86, 87, 89, 90, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 103, 104, 106, 107, 108, 109, 110, 111, 112, 113, 114, 116, 117, 119, 120, 121, 122, 123, 124, 125, 126, 127, 129, 130, 132, 133, 134, 135, 136, 137, 138, 139, 140, 142, 145, 146, 147, 149, 151, 153, 154, 155, 158, 159, 160, 161, 162, 163, 164, 165, 167, 170, 171, 173, 175, 176, 177, 179, 181, 182, 184, 185, 186, 188, 190, 193, 194, 195, 209, 212, 213, 214, 215, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 231, 232, 234, 236, 238, 240, 241, 242, 254, 256, 258, 260, 261, 262, 274, 276, 278, 281, 282, 284, 285, 286, 297, 299, 301, 304, 305, 306, 313, 315, 317, 319, 320, 322, 323, 324, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 339, 341, 344, 345, 347, 349, 351, 352, 354, 356, 357, 358, 361, 372, 373, 375, 376, 377, 388, 391, 401, 402, 403, 406, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 422, 424, 427, 429, 430, 431, 432, 433, 434, 437, 438, 439, 440, 445, 453, 456, 458, 459, 460, 461, 464, 465, 466, 467, 473, 475, 483, 486, 488, 489, 490, 491, 494, 495, 497, 498, 499, 506, 508, 515, 517, 519, 520, 521, 527, 535, 538, 541, 542, 543, 545, 554, 556, 557, 564, 567, 569, 579, 582, 583, 584, 585, 588, 589, 590, 591, 592, 594, 595, 596, 598, 600, 603, 650, 653, 700, 702, 704, 750, 752, 754, 801, 802, 804, 806, 807, 808, 810, 811, 813, 815, 817, 818, 819, 821, 830, 831, 832, 839, 841, 843, 845, 847, 849, 851, 853, 855, 866], "excluded_lines": [], "functions": {"DataProcessor.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [146, 147], "excluded_lines": []}, "DataProcessor.process_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 19, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 19, "excluded_lines": 0}, "missing_lines": [151, 153, 154, 155, 158, 159, 160, 161, 162, 163, 164, 165, 167, 170, 171, 173, 175, 176, 177], "excluded_lines": []}, "DataProcessor._is_cache_valid": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [181, 182, 184, 185, 186, 188], "excluded_lines": []}, "DataProcessor._get_project_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 25, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 25, "excluded_lines": 0}, "missing_lines": [193, 194, 195, 209, 212, 213, 214, 215, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 231, 232, 234, 236], "excluded_lines": []}, "DataProcessor._get_calculation_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [240, 241, 242, 254, 256], "excluded_lines": []}, "DataProcessor._get_user_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [260, 261, 262, 274, 276], "excluded_lines": []}, "DataProcessor._get_system_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [281, 282, 284, 285, 286, 297, 299], "excluded_lines": []}, "DataProcessor._get_sample_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [304, 305, 306, 313, 315], "excluded_lines": []}, "DataProcessor.aggregate_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [319, 320, 322, 323, 324, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 339], "excluded_lines": []}, "ReportGenerator.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [345], "excluded_lines": []}, "ReportGenerator.generate_report": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [349, 351, 352, 354, 356, 357, 358, 361, 372, 373, 375, 376, 377], "excluded_lines": []}, "ReportGenerator._generate_chart_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [391, 401, 402, 403, 406, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 422], "excluded_lines": []}, "ReportGenerator._process_time_series_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [427, 429, 430, 431, 432, 433, 434, 437, 438, 439, 440, 445], "excluded_lines": []}, "ReportGenerator._process_categorical_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [456, 458, 459, 460, 461, 464, 465, 466, 467, 473, 475], "excluded_lines": []}, "ReportGenerator._process_pie_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [486, 488, 489, 490, 491, 494, 495, 497, 498, 499, 506, 508], "excluded_lines": []}, "ReportGenerator._process_scatter_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [517, 519, 520, 521, 527], "excluded_lines": []}, "ReportGenerator._process_table_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [538, 541, 542, 543, 545], "excluded_lines": []}, "ReportGenerator._process_gauge_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [556, 557, 564, 567, 569], "excluded_lines": []}, "AdvancedReportingAnalyticsSystem.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [583, 584, 585, 588, 589, 590, 591, 592, 594, 595, 596, 598], "excluded_lines": []}, "AdvancedReportingAnalyticsSystem._initialize_default_templates": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [603, 650, 653, 700], "excluded_lines": []}, "AdvancedReportingAnalyticsSystem._initialize_default_dashboards": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [704, 750], "excluded_lines": []}, "AdvancedReportingAnalyticsSystem._initialize_default_kpis": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [754, 801, 802], "excluded_lines": []}, "AdvancedReportingAnalyticsSystem.generate_report": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [806, 807, 808, 810, 811, 813], "excluded_lines": []}, "AdvancedReportingAnalyticsSystem.get_dashboard_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [817, 818, 819, 821, 830, 831, 832, 839], "excluded_lines": []}, "AdvancedReportingAnalyticsSystem.get_kpis": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [843], "excluded_lines": []}, "AdvancedReportingAnalyticsSystem.get_report_templates": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [847], "excluded_lines": []}, "AdvancedReportingAnalyticsSystem.get_dashboards": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [851], "excluded_lines": []}, "AdvancedReportingAnalyticsSystem.get_analytics_status": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [855], "excluded_lines": []}, "": {"executed_lines": [1, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16], "summary": {"covered_lines": 11, "num_statements": 145, "percent_covered": 7.586206896551724, "percent_covered_display": "8", "missing_lines": 134, "excluded_lines": 0}, "missing_lines": [17, 20, 21, 23, 25, 26, 27, 28, 29, 30, 31, 32, 33, 35, 37, 38, 39, 40, 41, 42, 43, 44, 46, 48, 49, 50, 51, 52, 53, 54, 56, 58, 59, 60, 61, 62, 63, 65, 66, 68, 69, 70, 71, 72, 73, 74, 76, 77, 79, 80, 81, 82, 83, 84, 85, 86, 87, 89, 90, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 103, 104, 106, 107, 108, 109, 110, 111, 112, 113, 114, 116, 117, 119, 120, 121, 122, 123, 124, 125, 126, 127, 129, 130, 132, 133, 134, 135, 136, 137, 138, 139, 140, 142, 145, 149, 179, 190, 238, 258, 278, 301, 317, 341, 344, 347, 388, 424, 453, 483, 515, 535, 554, 579, 582, 600, 702, 752, 804, 815, 841, 845, 849, 853, 866], "excluded_lines": []}}, "classes": {"ReportType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ChartType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AggregationType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TimeRange": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DataSource": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ChartConfig": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ReportTemplate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ReportInstance": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Dashboard": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "KPI": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DataProcessor": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 92, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 92, "excluded_lines": 0}, "missing_lines": [146, 147, 151, 153, 154, 155, 158, 159, 160, 161, 162, 163, 164, 165, 167, 170, 171, 173, 175, 176, 177, 181, 182, 184, 185, 186, 188, 193, 194, 195, 209, 212, 213, 214, 215, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 231, 232, 234, 236, 240, 241, 242, 254, 256, 260, 261, 262, 274, 276, 281, 282, 284, 285, 286, 297, 299, 304, 305, 306, 313, 315, 319, 320, 322, 323, 324, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 339], "excluded_lines": []}, "ReportGenerator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 82, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 82, "excluded_lines": 0}, "missing_lines": [345, 349, 351, 352, 354, 356, 357, 358, 361, 372, 373, 375, 376, 377, 391, 401, 402, 403, 406, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 422, 427, 429, 430, 431, 432, 433, 434, 437, 438, 439, 440, 445, 456, 458, 459, 460, 461, 464, 465, 466, 467, 473, 475, 486, 488, 489, 490, 491, 494, 495, 497, 498, 499, 506, 508, 517, 519, 520, 521, 527, 538, 541, 542, 543, 545, 556, 557, 564, 567, 569], "excluded_lines": []}, "AdvancedReportingAnalyticsSystem": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 39, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 39, "excluded_lines": 0}, "missing_lines": [583, 584, 585, 588, 589, 590, 591, 592, 594, 595, 596, 598, 603, 650, 653, 700, 704, 750, 754, 801, 802, 806, 807, 808, 810, 811, 813, 817, 818, 819, 821, 830, 831, 832, 839, 843, 847, 851, 855], "excluded_lines": []}, "": {"executed_lines": [1, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16], "summary": {"covered_lines": 11, "num_statements": 145, "percent_covered": 7.586206896551724, "percent_covered_display": "8", "missing_lines": 134, "excluded_lines": 0}, "missing_lines": [17, 20, 21, 23, 25, 26, 27, 28, 29, 30, 31, 32, 33, 35, 37, 38, 39, 40, 41, 42, 43, 44, 46, 48, 49, 50, 51, 52, 53, 54, 56, 58, 59, 60, 61, 62, 63, 65, 66, 68, 69, 70, 71, 72, 73, 74, 76, 77, 79, 80, 81, 82, 83, 84, 85, 86, 87, 89, 90, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 103, 104, 106, 107, 108, 109, 110, 111, 112, 113, 114, 116, 117, 119, 120, 121, 122, 123, 124, 125, 126, 127, 129, 130, 132, 133, 134, 135, 136, 137, 138, 139, 140, 142, 145, 149, 179, 190, 238, 258, 278, 301, 317, 341, 344, 347, 388, 424, 453, 483, 515, 535, 554, 579, 582, 600, 702, 752, 804, 815, 841, 845, 849, 853, 866], "excluded_lines": []}}}, "api\\__init__.py": {"executed_lines": [1, 8, 9, 15, 17, 19], "summary": {"covered_lines": 5, "num_statements": 10, "percent_covered": 50.0, "percent_covered_display": "50", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [10, 11, 12, 13, 14], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 8, 9, 15, 17, 19], "summary": {"covered_lines": 5, "num_statements": 10, "percent_covered": 50.0, "percent_covered_display": "50", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [10, 11, 12, 13, 14], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 8, 9, 15, 17, 19], "summary": {"covered_lines": 5, "num_statements": 10, "percent_covered": 50.0, "percent_covered_display": "50", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [10, 11, 12, 13, 14], "excluded_lines": []}}}, "api\\analytics_routes.py": {"executed_lines": [1, 6, 7, 8, 9, 10, 11, 12, 14], "summary": {"covered_lines": 8, "num_statements": 134, "percent_covered": 5.970149253731344, "percent_covered_display": "6", "missing_lines": 126, "excluded_lines": 0}, "missing_lines": [22, 24, 25, 27, 29, 32, 33, 49, 50, 62, 73, 74, 79, 80, 82, 83, 84, 100, 108, 109, 114, 115, 117, 118, 119, 133, 141, 142, 147, 148, 150, 152, 153, 155, 156, 160, 162, 167, 168, 172, 173, 178, 179, 181, 182, 183, 197, 205, 206, 211, 212, 214, 215, 216, 217, 219, 220, 226, 227, 229, 230, 234, 236, 248, 249, 253, 254, 259, 260, 262, 263, 264, 266, 269, 272, 273, 274, 276, 289, 302, 303, 308, 309, 311, 312, 313, 314, 319, 333, 334, 339, 340, 342, 343, 344, 345, 350, 352, 354, 355, 364, 365, 372, 374, 375, 378, 381, 382, 383, 384, 385, 386, 387, 388, 390, 391, 399, 404, 405, 410, 411, 413, 414, 416, 422, 423], "excluded_lines": [], "functions": {"get_analytics_dashboard": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [27, 29, 32, 33, 49, 50, 62, 73, 74], "excluded_lines": []}, "get_kpis": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [82, 83, 84, 100, 108, 109], "excluded_lines": []}, "get_dashboards": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [117, 118, 119, 133, 141, 142], "excluded_lines": []}, "get_dashboard_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [150, 152, 153, 155, 156, 160, 162, 167, 168, 172, 173], "excluded_lines": []}, "get_report_templates": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [181, 182, 183, 197, 205, 206], "excluded_lines": []}, "generate_report": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [214, 215, 216, 217, 219, 220, 226, 227, 229, 230, 234, 236, 248, 249, 253, 254], "excluded_lines": []}, "get_reports": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [262, 263, 264, 266, 269, 272, 273, 274, 276, 289, 302, 303], "excluded_lines": []}, "get_report": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [311, 312, 313, 314, 319, 333, 334], "excluded_lines": []}, "export_report": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 27, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 27, "excluded_lines": 0}, "missing_lines": [342, 343, 344, 345, 350, 352, 354, 355, 364, 365, 372, 374, 375, 378, 381, 382, 383, 384, 385, 386, 387, 388, 390, 391, 399, 404, 405], "excluded_lines": []}, "health_check": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [413, 414, 416, 422, 423], "excluded_lines": []}, "": {"executed_lines": [1, 6, 7, 8, 9, 10, 11, 12, 14], "summary": {"covered_lines": 8, "num_statements": 29, "percent_covered": 27.586206896551722, "percent_covered_display": "28", "missing_lines": 21, "excluded_lines": 0}, "missing_lines": [22, 24, 25, 79, 80, 114, 115, 147, 148, 178, 179, 211, 212, 259, 260, 308, 309, 339, 340, 410, 411], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 6, 7, 8, 9, 10, 11, 12, 14], "summary": {"covered_lines": 8, "num_statements": 134, "percent_covered": 5.970149253731344, "percent_covered_display": "6", "missing_lines": 126, "excluded_lines": 0}, "missing_lines": [22, 24, 25, 27, 29, 32, 33, 49, 50, 62, 73, 74, 79, 80, 82, 83, 84, 100, 108, 109, 114, 115, 117, 118, 119, 133, 141, 142, 147, 148, 150, 152, 153, 155, 156, 160, 162, 167, 168, 172, 173, 178, 179, 181, 182, 183, 197, 205, 206, 211, 212, 214, 215, 216, 217, 219, 220, 226, 227, 229, 230, 234, 236, 248, 249, 253, 254, 259, 260, 262, 263, 264, 266, 269, 272, 273, 274, 276, 289, 302, 303, 308, 309, 311, 312, 313, 314, 319, 333, 334, 339, 340, 342, 343, 344, 345, 350, 352, 354, 355, 364, 365, 372, 374, 375, 378, 381, 382, 383, 384, 385, 386, 387, 388, 390, 391, 399, 404, 405, 410, 411, 413, 414, 416, 422, 423], "excluded_lines": []}}}, "api\\calculations.py": {"executed_lines": [1, 7, 8, 9, 10, 13, 16, 19, 23, 27, 30, 31, 39, 41, 43, 44, 45, 97, 98, 121, 122, 140, 141, 184, 185, 217, 218, 243, 244, 350, 351, 412, 413, 528, 529, 576, 577, 601, 602, 626, 627], "summary": {"covered_lines": 40, "num_statements": 207, "percent_covered": 19.32367149758454, "percent_covered_display": "19", "missing_lines": 167, "excluded_lines": 0}, "missing_lines": [32, 34, 35, 36, 37, 59, 61, 62, 64, 65, 68, 71, 74, 84, 85, 86, 88, 91, 93, 94, 95, 100, 101, 103, 104, 106, 107, 109, 115, 117, 118, 119, 124, 125, 126, 128, 134, 136, 137, 138, 143, 145, 173, 178, 180, 181, 182, 187, 189, 211, 213, 214, 215, 222, 223, 226, 235, 236, 238, 239, 240, 260, 261, 263, 264, 267, 268, 269, 270, 271, 274, 275, 276, 277, 285, 286, 287, 288, 289, 292, 303, 306, 334, 335, 341, 343, 345, 346, 347, 365, 366, 368, 369, 372, 373, 374, 375, 383, 390, 403, 405, 407, 408, 409, 434, 435, 438, 439, 440, 441, 444, 445, 446, 447, 448, 451, 452, 453, 454, 455, 458, 459, 460, 461, 462, 465, 466, 467, 468, 476, 490, 493, 519, 521, 523, 524, 525, 533, 534, 570, 572, 573, 574, 581, 582, 585, 594, 595, 597, 598, 599, 606, 607, 610, 619, 620, 622, 623, 624, 631, 632, 635, 645, 646, 648, 649, 650], "excluded_lines": [], "functions": {"calculate_air_duct": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [59, 61, 62, 64, 65, 68, 71, 74, 84, 85, 86, 88, 91, 93, 94, 95], "excluded_lines": []}, "validate_air_duct_input": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [100, 101, 103, 104, 106, 107, 109, 115, 117, 118, 119], "excluded_lines": []}, "get_air_duct_standard_sizes": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [124, 125, 126, 128, 134, 136, 137, 138], "excluded_lines": []}, "get_air_duct_materials": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [143, 145, 173, 178, 180, 181, 182], "excluded_lines": []}, "get_air_duct_info": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [187, 189, 211, 213, 214, 215], "excluded_lines": []}, "calculate_grease_duct": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [222, 223, 226, 235, 236, 238, 239, 240], "excluded_lines": []}, "calculate_velocity_pressure": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 28, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 28, "excluded_lines": 0}, "missing_lines": [260, 261, 263, 264, 267, 268, 269, 270, 271, 274, 275, 276, 277, 285, 286, 287, 288, 289, 292, 303, 306, 334, 335, 341, 343, 345, 346, 347], "excluded_lines": []}, "calculate_velocity_from_pressure": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [365, 366, 368, 369, 372, 373, 374, 375, 383, 390, 403, 405, 407, 408, 409], "excluded_lines": []}, "calculate_enhanced_friction": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 33, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 33, "excluded_lines": 0}, "missing_lines": [434, 435, 438, 439, 440, 441, 444, 445, 446, 447, 448, 451, 452, 453, 454, 455, 458, 459, 460, 461, 462, 465, 466, 467, 468, 476, 490, 493, 519, 521, 523, 524, 525], "excluded_lines": []}, "get_advanced_calculators_info": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [533, 534, 570, 572, 573, 574], "excluded_lines": []}, "calculate_engine_exhaust": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [581, 582, 585, 594, 595, 597, 598, 599], "excluded_lines": []}, "calculate_boiler_vent": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [606, 607, 610, 619, 620, 622, 623, 624], "excluded_lines": []}, "calculate_estimate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [631, 632, 635, 645, 646, 648, 649, 650], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 10, 13, 16, 19, 23, 27, 30, 31, 39, 41, 43, 44, 45, 97, 98, 121, 122, 140, 141, 184, 185, 217, 218, 243, 244, 350, 351, 412, 413, 528, 529, 576, 577, 601, 602, 626, 627], "summary": {"covered_lines": 40, "num_statements": 45, "percent_covered": 88.88888888888889, "percent_covered_display": "89", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [32, 34, 35, 36, 37], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 7, 8, 9, 10, 13, 16, 19, 23, 27, 30, 31, 39, 41, 43, 44, 45, 97, 98, 121, 122, 140, 141, 184, 185, 217, 218, 243, 244, 350, 351, 412, 413, 528, 529, 576, 577, 601, 602, 626, 627], "summary": {"covered_lines": 40, "num_statements": 207, "percent_covered": 19.32367149758454, "percent_covered_display": "19", "missing_lines": 167, "excluded_lines": 0}, "missing_lines": [32, 34, 35, 36, 37, 59, 61, 62, 64, 65, 68, 71, 74, 84, 85, 86, 88, 91, 93, 94, 95, 100, 101, 103, 104, 106, 107, 109, 115, 117, 118, 119, 124, 125, 126, 128, 134, 136, 137, 138, 143, 145, 173, 178, 180, 181, 182, 187, 189, 211, 213, 214, 215, 222, 223, 226, 235, 236, 238, 239, 240, 260, 261, 263, 264, 267, 268, 269, 270, 271, 274, 275, 276, 277, 285, 286, 287, 288, 289, 292, 303, 306, 334, 335, 341, 343, 345, 346, 347, 365, 366, 368, 369, 372, 373, 374, 375, 383, 390, 403, 405, 407, 408, 409, 434, 435, 438, 439, 440, 441, 444, 445, 446, 447, 448, 451, 452, 453, 454, 455, 458, 459, 460, 461, 462, 465, 466, 467, 468, 476, 490, 493, 519, 521, 523, 524, 525, 533, 534, 570, 572, 573, 574, 581, 582, 585, 594, 595, 597, 598, 599, 606, 607, 610, 619, 620, 622, 623, 624, 631, 632, 635, 645, 646, 648, 649, 650], "excluded_lines": []}}}, "api\\compliance_routes.py": {"executed_lines": [1, 6, 7, 8, 9, 10, 11, 12, 14, 22, 25, 27, 28, 29, 45, 46, 47, 65, 66, 67, 82, 83, 84, 124, 125, 126, 165, 166, 167, 213, 214, 215, 241, 242, 243, 290, 291, 292, 341, 342, 343, 359, 374, 375, 378, 379, 382, 383], "summary": {"covered_lines": 47, "num_statements": 197, "percent_covered": 23.85786802030457, "percent_covered_display": "24", "missing_lines": 150, "excluded_lines": 0}, "missing_lines": [31, 33, 34, 35, 38, 40, 41, 42, 43, 49, 50, 51, 53, 54, 55, 56, 57, 59, 60, 61, 62, 63, 69, 70, 77, 78, 79, 80, 86, 88, 89, 90, 91, 94, 95, 98, 99, 100, 103, 106, 119, 120, 121, 122, 128, 129, 131, 132, 133, 134, 138, 139, 141, 144, 145, 146, 160, 161, 162, 163, 169, 170, 171, 173, 175, 176, 177, 179, 180, 181, 182, 183, 184, 185, 186, 189, 192, 193, 194, 208, 209, 210, 211, 217, 218, 221, 222, 223, 236, 237, 238, 239, 245, 246, 248, 249, 252, 253, 254, 255, 258, 259, 260, 261, 262, 265, 279, 281, 282, 284, 286, 287, 288, 294, 296, 297, 298, 299, 302, 303, 306, 307, 308, 311, 314, 315, 318, 319, 321, 322, 323, 324, 325, 326, 328, 330, 337, 338, 339, 345, 346, 354, 355, 356, 357, 361, 371, 376, 380, 384], "excluded_lines": [], "functions": {"get_compliance_dashboard": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [31, 33, 34, 35, 38, 40, 41, 42, 43], "excluded_lines": []}, "get_compliance_status": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [49, 50, 51, 53, 54, 55, 56, 57, 59, 60, 61, 62, 63], "excluded_lines": []}, "get_supported_frameworks": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [69, 70, 77, 78, 79, 80], "excluded_lines": []}, "conduct_assessment": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [86, 88, 89, 90, 91, 94, 95, 98, 99, 100, 103, 106, 119, 120, 121, 122], "excluded_lines": []}, "get_requirements": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [128, 129, 131, 132, 133, 134, 138, 139, 141, 144, 145, 146, 160, 161, 162, 163], "excluded_lines": []}, "get_assessments": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 23, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 23, "excluded_lines": 0}, "missing_lines": [169, 170, 171, 173, 175, 176, 177, 179, 180, 181, 182, 183, 184, 185, 186, 189, 192, 193, 194, 208, 209, 210, 211], "excluded_lines": []}, "get_data_governance_policies": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [217, 218, 221, 222, 223, 236, 237, 238, 239], "excluded_lines": []}, "create_data_governance_policy": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 21, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 21, "excluded_lines": 0}, "missing_lines": [245, 246, 248, 249, 252, 253, 254, 255, 258, 259, 260, 261, 262, 265, 279, 281, 282, 284, 286, 287, 288], "excluded_lines": []}, "export_compliance_report": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 26, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 26, "excluded_lines": 0}, "missing_lines": [294, 296, 297, 298, 299, 302, 303, 306, 307, 308, 311, 314, 315, 318, 319, 321, 322, 323, 324, 325, 326, 328, 330, 337, 338, 339], "excluded_lines": []}, "health_check": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [345, 346, 354, 355, 356, 357], "excluded_lines": []}, "get_framework_description": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [361, 371], "excluded_lines": []}, "not_found": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [376], "excluded_lines": []}, "method_not_allowed": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [380], "excluded_lines": []}, "internal_error": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [384], "excluded_lines": []}, "": {"executed_lines": [1, 6, 7, 8, 9, 10, 11, 12, 14, 22, 25, 27, 28, 29, 45, 46, 47, 65, 66, 67, 82, 83, 84, 124, 125, 126, 165, 166, 167, 213, 214, 215, 241, 242, 243, 290, 291, 292, 341, 342, 343, 359, 374, 375, 378, 379, 382, 383], "summary": {"covered_lines": 47, "num_statements": 47, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 6, 7, 8, 9, 10, 11, 12, 14, 22, 25, 27, 28, 29, 45, 46, 47, 65, 66, 67, 82, 83, 84, 124, 125, 126, 165, 166, 167, 213, 214, 215, 241, 242, 243, 290, 291, 292, 341, 342, 343, 359, 374, 375, 378, 379, 382, 383], "summary": {"covered_lines": 47, "num_statements": 197, "percent_covered": 23.85786802030457, "percent_covered_display": "24", "missing_lines": 150, "excluded_lines": 0}, "missing_lines": [31, 33, 34, 35, 38, 40, 41, 42, 43, 49, 50, 51, 53, 54, 55, 56, 57, 59, 60, 61, 62, 63, 69, 70, 77, 78, 79, 80, 86, 88, 89, 90, 91, 94, 95, 98, 99, 100, 103, 106, 119, 120, 121, 122, 128, 129, 131, 132, 133, 134, 138, 139, 141, 144, 145, 146, 160, 161, 162, 163, 169, 170, 171, 173, 175, 176, 177, 179, 180, 181, 182, 183, 184, 185, 186, 189, 192, 193, 194, 208, 209, 210, 211, 217, 218, 221, 222, 223, 236, 237, 238, 239, 245, 246, 248, 249, 252, 253, 254, 255, 258, 259, 260, 261, 262, 265, 279, 281, 282, 284, 286, 287, 288, 294, 296, 297, 298, 299, 302, 303, 306, 307, 308, 311, 314, 315, 318, 319, 321, 322, 323, 324, 325, 326, 328, 330, 337, 338, 339, 345, 346, 354, 355, 356, 357, 361, 371, 376, 380, 384], "excluded_lines": []}}}, "api\\exports.py": {"executed_lines": [1, 7, 8, 9, 10, 11, 13, 15, 17, 18, 56, 57, 84, 85, 112, 113, 159, 160], "summary": {"covered_lines": 17, "num_statements": 59, "percent_covered": 28.8135593220339, "percent_covered_display": "29", "missing_lines": 42, "excluded_lines": 0}, "missing_lines": [29, 30, 35, 36, 39, 49, 50, 52, 53, 54, 61, 62, 66, 68, 77, 78, 80, 81, 82, 89, 90, 94, 96, 105, 106, 108, 109, 110, 117, 118, 120, 121, 124, 143, 145, 152, 153, 155, 156, 157, 164, 195], "excluded_lines": [], "functions": {"export_pdf": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [29, 30, 35, 36, 39, 49, 50, 52, 53, 54], "excluded_lines": []}, "export_excel": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [61, 62, 66, 68, 77, 78, 80, 81, 82], "excluded_lines": []}, "export_csv": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [89, 90, 94, 96, 105, 106, 108, 109, 110], "excluded_lines": []}, "export_json": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [117, 118, 120, 121, 124, 143, 145, 152, 153, 155, 156, 157], "excluded_lines": []}, "get_export_formats": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [164, 195], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 10, 11, 13, 15, 17, 18, 56, 57, 84, 85, 112, 113, 159, 160], "summary": {"covered_lines": 17, "num_statements": 17, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 7, 8, 9, 10, 11, 13, 15, 17, 18, 56, 57, 84, 85, 112, 113, 159, 160], "summary": {"covered_lines": 17, "num_statements": 59, "percent_covered": 28.8135593220339, "percent_covered_display": "29", "missing_lines": 42, "excluded_lines": 0}, "missing_lines": [29, 30, 35, 36, 39, 49, 50, 52, 53, 54, 61, 62, 66, 68, 77, 78, 80, 81, 82, 89, 90, 94, 96, 105, 106, 108, 109, 110, 117, 118, 120, 121, 124, 143, 145, 152, 153, 155, 156, 157, 164, 195], "excluded_lines": []}}}, "api\\mongodb_api.py": {"executed_lines": [1, 8, 9, 10, 11, 12, 14, 16, 18, 27, 28, 48, 49, 66, 67, 89, 90, 107, 108, 128, 129, 144, 145, 165, 166, 180, 181, 201, 202, 217, 218, 240, 241, 255, 256], "summary": {"covered_lines": 34, "num_statements": 148, "percent_covered": 22.972972972972972, "percent_covered_display": "23", "missing_lines": 114, "excluded_lines": 0}, "missing_lines": [20, 21, 22, 23, 25, 30, 31, 33, 34, 36, 38, 44, 45, 46, 51, 52, 54, 55, 57, 62, 63, 64, 69, 70, 72, 73, 75, 77, 78, 80, 85, 86, 87, 92, 93, 95, 96, 98, 103, 104, 105, 110, 111, 113, 114, 116, 118, 124, 125, 126, 131, 132, 133, 135, 140, 141, 142, 147, 148, 150, 151, 153, 155, 161, 162, 163, 168, 169, 171, 176, 177, 178, 183, 184, 186, 187, 189, 191, 197, 198, 199, 204, 205, 206, 208, 213, 214, 215, 220, 221, 223, 224, 226, 228, 229, 231, 236, 237, 238, 243, 244, 246, 251, 252, 253, 258, 260, 261, 262, 263, 265, 271, 272, 273], "excluded_lines": [], "functions": {"run_async": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [20, 21, 22, 23, 25], "excluded_lines": []}, "create_project": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [30, 31, 33, 34, 36, 38, 44, 45, 46], "excluded_lines": []}, "get_project": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [51, 52, 54, 55, 57, 62, 63, 64], "excluded_lines": []}, "update_project": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [69, 70, 72, 73, 75, 77, 78, 80, 85, 86, 87], "excluded_lines": []}, "delete_project": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [92, 93, 95, 96, 98, 103, 104, 105], "excluded_lines": []}, "save_spatial_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [110, 111, 113, 114, 116, 118, 124, 125, 126], "excluded_lines": []}, "get_spatial_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [131, 132, 133, 135, 140, 141, 142], "excluded_lines": []}, "save_hvac_system": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [147, 148, 150, 151, 153, 155, 161, 162, 163], "excluded_lines": []}, "get_hvac_systems": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [168, 169, 171, 176, 177, 178], "excluded_lines": []}, "save_calculation_result": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [183, 184, 186, 187, 189, 191, 197, 198, 199], "excluded_lines": []}, "get_calculation_results": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [204, 205, 206, 208, 213, 214, 215], "excluded_lines": []}, "save_user_preferences": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [220, 221, 223, 224, 226, 228, 229, 231, 236, 237, 238], "excluded_lines": []}, "get_user_preferences": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [243, 244, 246, 251, 252, 253], "excluded_lines": []}, "mongodb_health": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [258, 260, 261, 262, 263, 265, 271, 272, 273], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 14, 16, 18, 27, 28, 48, 49, 66, 67, 89, 90, 107, 108, 128, 129, 144, 145, 165, 166, 180, 181, 201, 202, 217, 218, 240, 241, 255, 256], "summary": {"covered_lines": 34, "num_statements": 34, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 8, 9, 10, 11, 12, 14, 16, 18, 27, 28, 48, 49, 66, 67, 89, 90, 107, 108, 128, 129, 144, 145, 165, 166, 180, 181, 201, 202, 217, 218, 240, 241, 255, 256], "summary": {"covered_lines": 34, "num_statements": 148, "percent_covered": 22.972972972972972, "percent_covered_display": "23", "missing_lines": 114, "excluded_lines": 0}, "missing_lines": [20, 21, 22, 23, 25, 30, 31, 33, 34, 36, 38, 44, 45, 46, 51, 52, 54, 55, 57, 62, 63, 64, 69, 70, 72, 73, 75, 77, 78, 80, 85, 86, 87, 92, 93, 95, 96, 98, 103, 104, 105, 110, 111, 113, 114, 116, 118, 124, 125, 126, 131, 132, 133, 135, 140, 141, 142, 147, 148, 150, 151, 153, 155, 161, 162, 163, 168, 169, 171, 176, 177, 178, 183, 184, 186, 187, 189, 191, 197, 198, 199, 204, 205, 206, 208, 213, 214, 215, 220, 221, 223, 224, 226, 228, 229, 231, 236, 237, 238, 243, 244, 246, 251, 252, 253, 258, 260, 261, 262, 263, 265, 271, 272, 273], "excluded_lines": []}}}, "api\\validation.py": {"executed_lines": [1, 7, 8, 10, 12, 14, 15, 58, 59, 86, 87, 114, 115], "summary": {"covered_lines": 12, "num_statements": 44, "percent_covered": 27.272727272727273, "percent_covered_display": "27", "missing_lines": 32, "excluded_lines": 0}, "missing_lines": [27, 28, 31, 51, 52, 54, 55, 56, 63, 64, 67, 79, 80, 82, 83, 84, 91, 92, 95, 107, 108, 110, 111, 112, 119, 120, 123, 132, 133, 135, 136, 137], "excluded_lines": [], "functions": {"validate_smacna": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [27, 28, 31, 51, 52, 54, 55, 56], "excluded_lines": []}, "validate_nfpa": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [63, 64, 67, 79, 80, 82, 83, 84], "excluded_lines": []}, "validate_ashrae": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [91, 92, 95, 107, 108, 110, 111, 112], "excluded_lines": []}, "validate_units": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [119, 120, 123, 132, 133, 135, 136, 137], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 10, 12, 14, 15, 58, 59, 86, 87, 114, 115], "summary": {"covered_lines": 12, "num_statements": 12, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 7, 8, 10, 12, 14, 15, 58, 59, 86, 87, 114, 115], "summary": {"covered_lines": 12, "num_statements": 44, "percent_covered": 27.272727272727273, "percent_covered_display": "27", "missing_lines": 32, "excluded_lines": 0}, "missing_lines": [27, 28, 31, 51, 52, 54, 55, 56, 63, 64, 67, 79, 80, 82, 83, 84, 91, 92, 95, 107, 108, 110, 111, 112, 119, 120, 123, 132, 133, 135, 136, 137], "excluded_lines": []}}}, "app.py": {"executed_lines": [1, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 23, 26, 29, 47, 49, 51, 54, 58, 59, 60, 63, 64, 65, 66, 69, 71, 74, 75, 78, 79, 80, 81, 82, 90, 91, 92, 93, 95, 96, 97, 98, 101, 187], "summary": {"covered_lines": 46, "num_statements": 95, "percent_covered": 48.421052631578945, "percent_covered_display": "48", "missing_lines": 49, "excluded_lines": 1}, "missing_lines": [86, 87, 102, 104, 106, 107, 108, 109, 110, 111, 112, 115, 116, 118, 119, 121, 122, 123, 124, 125, 126, 127, 129, 137, 138, 140, 161, 162, 164, 165, 169, 170, 171, 172, 174, 175, 176, 177, 179, 180, 181, 182, 184, 185, 188, 189, 190, 192, 193], "excluded_lines": [166], "functions": {"create_app": {"executed_lines": [51, 54, 58, 59, 60, 63, 64, 65, 66, 69, 71, 74, 75, 78, 79, 80, 81, 82, 90, 91, 92, 93, 95, 96, 97, 98, 101], "summary": {"covered_lines": 27, "num_statements": 44, "percent_covered": 61.36363636363637, "percent_covered_display": "61", "missing_lines": 17, "excluded_lines": 0}, "missing_lines": [86, 87, 102, 115, 116, 137, 138, 161, 162, 169, 170, 174, 175, 179, 180, 184, 185], "excluded_lines": []}, "create_app.initialize_mongodb": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [104, 106, 107, 108, 109, 110, 111, 112], "excluded_lines": []}, "create_app.health_check": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [118, 119, 121, 122, 123, 124, 125, 126, 127, 129], "excluded_lines": []}, "create_app.api_info": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [140], "excluded_lines": []}, "create_app.sentry_debug": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 1}, "missing_lines": [164, 165], "excluded_lines": [166]}, "create_app.bad_request": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [171, 172], "excluded_lines": []}, "create_app.not_found": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [176, 177], "excluded_lines": []}, "create_app.internal_error": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [181, 182], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 23, 26, 29, 47, 49, 187], "summary": {"covered_lines": 19, "num_statements": 24, "percent_covered": 79.16666666666667, "percent_covered_display": "79", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [188, 189, 190, 192, 193], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 23, 26, 29, 47, 49, 51, 54, 58, 59, 60, 63, 64, 65, 66, 69, 71, 74, 75, 78, 79, 80, 81, 82, 90, 91, 92, 93, 95, 96, 97, 98, 101, 187], "summary": {"covered_lines": 46, "num_statements": 95, "percent_covered": 48.421052631578945, "percent_covered_display": "48", "missing_lines": 49, "excluded_lines": 1}, "missing_lines": [86, 87, 102, 104, 106, 107, 108, 109, 110, 111, 112, 115, 116, 118, 119, 121, 122, 123, 124, 125, 126, 127, 129, 137, 138, 140, 161, 162, 164, 165, 169, 170, 171, 172, 174, 175, 176, 177, 179, 180, 181, 182, 184, 185, 188, 189, 190, 192, 193], "excluded_lines": [166]}}}, "compliance\\compliance_management_system.py": {"executed_lines": [1, 6, 7, 8, 9, 10, 11, 12, 13, 14, 17, 18, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 31, 32, 33, 34, 35, 36, 37, 39, 40, 41, 42, 43, 44, 45, 46, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 106, 107, 109, 110, 118, 120, 123, 165, 167, 168, 170, 171, 182, 184, 186, 228, 230, 231, 233, 234, 240, 242, 244, 273, 275, 276, 278, 279, 280, 281, 282, 285, 286, 287, 288, 290, 291, 293, 295, 297, 298, 299, 300, 302, 303, 305, 307, 309, 336, 337, 339, 359, 404, 438, 465, 469, 479, 536, 557], "summary": {"covered_lines": 138, "num_statements": 223, "percent_covered": 61.88340807174888, "percent_covered_display": "62", "missing_lines": 85, "excluded_lines": 0}, "missing_lines": [341, 343, 344, 346, 348, 349, 350, 353, 354, 356, 357, 362, 365, 366, 367, 369, 370, 371, 373, 374, 376, 377, 378, 380, 381, 382, 383, 384, 385, 386, 388, 402, 406, 407, 409, 411, 412, 414, 415, 416, 417, 420, 421, 423, 436, 440, 441, 442, 443, 444, 454, 455, 457, 467, 471, 472, 473, 474, 475, 476, 477, 481, 490, 491, 492, 493, 496, 502, 515, 516, 517, 520, 525, 534, 538, 539, 540, 542, 544, 545, 546, 548, 549, 551, 553], "excluded_lines": [], "functions": {"SOC2ComplianceManager.__init__": {"executed_lines": [110], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SOC2ComplianceManager.get_requirements": {"executed_lines": [120, 123, 165], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "GDPRComplianceManager.__init__": {"executed_lines": [171], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "GDPRComplianceManager.get_requirements": {"executed_lines": [184, 186, 228], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "HIPAAComplianceManager.__init__": {"executed_lines": [234], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "HIPAAComplianceManager.get_requirements": {"executed_lines": [242, 244, 273], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ComplianceManagementSystem.__init__": {"executed_lines": [279, 280, 281, 282, 285, 286, 287, 288, 290, 291, 293], "summary": {"covered_lines": 11, "num_statements": 11, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ComplianceManagementSystem._initialize_requirements": {"executed_lines": [297, 298, 299, 300, 302, 303, 305], "summary": {"covered_lines": 7, "num_statements": 7, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ComplianceManagementSystem._initialize_default_policies": {"executed_lines": [309, 336, 337], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ComplianceManagementSystem.conduct_assessment": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [341, 343, 344, 346, 348, 349, 350, 353, 354, 356, 357], "excluded_lines": []}, "ComplianceManagementSystem._assess_requirement": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 21, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 21, "excluded_lines": 0}, "missing_lines": [362, 365, 366, 367, 369, 370, 371, 373, 374, 376, 377, 378, 380, 381, 382, 383, 384, 385, 386, 388, 402], "excluded_lines": []}, "ComplianceManagementSystem._generate_compliance_report": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [406, 407, 409, 411, 412, 414, 415, 416, 417, 420, 421, 423, 436], "excluded_lines": []}, "ComplianceManagementSystem.get_compliance_status": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [440, 441, 442, 443, 444, 454, 455, 457], "excluded_lines": []}, "ComplianceManagementSystem.get_data_governance_policies": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [467], "excluded_lines": []}, "ComplianceManagementSystem.create_data_governance_policy": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [471, 472, 473, 474, 475, 476, 477], "excluded_lines": []}, "ComplianceManagementSystem.generate_compliance_dashboard_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [481, 490, 491, 492, 493, 496, 502, 515, 516, 517, 520, 525, 534], "excluded_lines": []}, "ComplianceManagementSystem.export_compliance_report": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [538, 539, 540, 542, 544, 545, 546, 548, 549, 551, 553], "excluded_lines": []}, "": {"executed_lines": [1, 6, 7, 8, 9, 10, 11, 12, 13, 14, 17, 18, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 31, 32, 33, 34, 35, 36, 37, 39, 40, 41, 42, 43, 44, 45, 46, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 106, 107, 109, 118, 167, 168, 170, 182, 230, 231, 233, 240, 275, 276, 278, 295, 307, 339, 359, 404, 438, 465, 469, 479, 536, 557], "summary": {"covered_lines": 105, "num_statements": 105, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"ComplianceFramework": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ComplianceStatus": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DataClassification": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ComplianceRequirement": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ComplianceAssessment": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DataGovernancePolicy": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ComplianceReport": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SOC2ComplianceManager": {"executed_lines": [110, 120, 123, 165], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "GDPRComplianceManager": {"executed_lines": [171, 184, 186, 228], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "HIPAAComplianceManager": {"executed_lines": [234, 242, 244, 273], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ComplianceManagementSystem": {"executed_lines": [279, 280, 281, 282, 285, 286, 287, 288, 290, 291, 293, 297, 298, 299, 300, 302, 303, 305, 309, 336, 337], "summary": {"covered_lines": 21, "num_statements": 106, "percent_covered": 19.81132075471698, "percent_covered_display": "20", "missing_lines": 85, "excluded_lines": 0}, "missing_lines": [341, 343, 344, 346, 348, 349, 350, 353, 354, 356, 357, 362, 365, 366, 367, 369, 370, 371, 373, 374, 376, 377, 378, 380, 381, 382, 383, 384, 385, 386, 388, 402, 406, 407, 409, 411, 412, 414, 415, 416, 417, 420, 421, 423, 436, 440, 441, 442, 443, 444, 454, 455, 457, 467, 471, 472, 473, 474, 475, 476, 477, 481, 490, 491, 492, 493, 496, 502, 515, 516, 517, 520, 525, 534, 538, 539, 540, 542, 544, 545, 546, 548, 549, 551, 553], "excluded_lines": []}, "": {"executed_lines": [1, 6, 7, 8, 9, 10, 11, 12, 13, 14, 17, 18, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 31, 32, 33, 34, 35, 36, 37, 39, 40, 41, 42, 43, 44, 45, 46, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 106, 107, 109, 118, 167, 168, 170, 182, 230, 231, 233, 240, 275, 276, 278, 295, 307, 339, 359, 404, 438, 465, 469, 479, 536, 557], "summary": {"covered_lines": 105, "num_statements": 105, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "config\\mongodb_config.py": {"executed_lines": [1, 8, 9, 10, 11, 12, 13, 14, 17, 18, 24, 26, 27, 29, 30, 31, 32, 33, 34, 36, 39, 41, 43, 45, 47, 48, 49, 50, 52, 55, 56, 73, 75, 102, 104, 105, 106, 119, 125, 127, 129, 130, 131, 136, 147, 158, 160, 162, 164, 171, 202], "summary": {"covered_lines": 49, "num_statements": 122, "percent_covered": 40.16393442622951, "percent_covered_display": "40", "missing_lines": 73, "excluded_lines": 0}, "missing_lines": [19, 21, 22, 53, 59, 61, 62, 63, 64, 65, 67, 68, 70, 71, 77, 78, 79, 93, 94, 96, 97, 98, 100, 121, 122, 123, 133, 134, 138, 139, 140, 141, 142, 143, 144, 145, 149, 150, 151, 153, 154, 155, 166, 167, 169, 173, 174, 177, 186, 188, 189, 190, 191, 194, 196, 198, 199, 200, 204, 206, 207, 208, 211, 212, 213, 216, 217, 220, 221, 223, 225, 226, 227], "excluded_lines": [], "functions": {"MongoDBConfig.__init__": {"executed_lines": [30, 31, 32, 33, 34], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "MongoDBConfig._get_connection_string": {"executed_lines": [39, 41, 43, 45, 47, 48, 49, 50, 52, 55, 56, 73], "summary": {"covered_lines": 12, "num_statements": 23, "percent_covered": 52.17391304347826, "percent_covered_display": "52", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [53, 59, 61, 62, 63, 64, 65, 67, 68, 70, 71], "excluded_lines": []}, "MongoDBConfig.get_sync_client": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [77, 78, 79, 93, 94, 96, 97, 98, 100], "excluded_lines": []}, "MongoDBConfig.get_async_client": {"executed_lines": [104, 105, 106, 119, 125], "summary": {"covered_lines": 5, "num_statements": 8, "percent_covered": 62.5, "percent_covered_display": "62", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [121, 122, 123], "excluded_lines": []}, "MongoDBConfig.get_database": {"executed_lines": [129, 130, 131], "summary": {"covered_lines": 3, "num_statements": 5, "percent_covered": 60.0, "percent_covered_display": "60", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [133, 134], "excluded_lines": []}, "MongoDBConfig.test_connection": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [138, 139, 140, 141, 142, 143, 144, 145], "excluded_lines": []}, "MongoDBConfig.close_connections": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [149, 150, 151, 153, 154, 155], "excluded_lines": []}, "get_mongodb_database": {"executed_lines": [162], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "get_mongodb_client": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [166, 167, 169], "excluded_lines": []}, "init_mongodb_collections": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [173, 174, 177, 186, 188, 189, 190, 191, 194, 196, 198, 199, 200], "excluded_lines": []}, "create_indexes": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [204, 206, 207, 208, 211, 212, 213, 216, 217, 220, 221, 223, 225, 226, 227], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 13, 14, 17, 18, 24, 26, 27, 29, 36, 75, 102, 127, 136, 147, 158, 160, 164, 171, 202], "summary": {"covered_lines": 23, "num_statements": 26, "percent_covered": 88.46153846153847, "percent_covered_display": "88", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [19, 21, 22], "excluded_lines": []}}, "classes": {"MongoDBConfig": {"executed_lines": [30, 31, 32, 33, 34, 39, 41, 43, 45, 47, 48, 49, 50, 52, 55, 56, 73, 104, 105, 106, 119, 125, 129, 130, 131], "summary": {"covered_lines": 25, "num_statements": 64, "percent_covered": 39.0625, "percent_covered_display": "39", "missing_lines": 39, "excluded_lines": 0}, "missing_lines": [53, 59, 61, 62, 63, 64, 65, 67, 68, 70, 71, 77, 78, 79, 93, 94, 96, 97, 98, 100, 121, 122, 123, 133, 134, 138, 139, 140, 141, 142, 143, 144, 145, 149, 150, 151, 153, 154, 155], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 13, 14, 17, 18, 24, 26, 27, 29, 36, 75, 102, 127, 136, 147, 158, 160, 162, 164, 171, 202], "summary": {"covered_lines": 24, "num_statements": 58, "percent_covered": 41.37931034482759, "percent_covered_display": "41", "missing_lines": 34, "excluded_lines": 0}, "missing_lines": [19, 21, 22, 166, 167, 169, 173, 174, 177, 186, 188, 189, 190, 191, 194, 196, 198, 199, 200, 204, 206, 207, 208, 211, 212, 213, 216, 217, 220, 221, 223, 225, 226, 227], "excluded_lines": []}}}, "database\\PerformanceOptimizer.py": {"executed_lines": [1, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 26, 32, 33, 34, 36, 37, 38, 39, 40, 43, 44, 45, 46, 47, 50, 51, 52, 54, 55, 56, 58, 59, 60, 61, 62, 65, 66, 67, 70, 71, 72, 74, 75, 76, 77, 78, 79, 80, 81, 82, 85, 86, 87, 89, 90, 91, 92, 95, 96, 97, 98, 99, 102, 103, 104, 105, 108, 109, 110, 113, 114, 115, 121, 122, 124, 128, 129, 130, 133, 134, 135, 138, 139, 140, 143, 144, 146, 171, 201, 229, 252, 280, 309, 323, 363, 421, 443, 512, 549, 617, 651, 676, 692, 716, 718, 722, 726, 727], "summary": {"covered_lines": 106, "num_statements": 340, "percent_covered": 31.176470588235293, "percent_covered_display": "31", "missing_lines": 234, "excluded_lines": 0}, "missing_lines": [148, 150, 153, 156, 159, 162, 163, 165, 167, 168, 169, 173, 175, 188, 191, 193, 197, 198, 199, 203, 205, 219, 221, 225, 226, 227, 231, 232, 243, 245, 248, 249, 250, 254, 255, 257, 266, 267, 268, 269, 270, 273, 275, 277, 278, 282, 283, 284, 286, 287, 288, 291, 292, 293, 295, 298, 299, 305, 311, 313, 316, 318, 320, 321, 325, 326, 328, 349, 350, 351, 352, 353, 354, 355, 358, 360, 361, 365, 366, 369, 377, 386, 394, 403, 411, 416, 418, 419, 423, 424, 425, 426, 429, 430, 433, 434, 437, 439, 440, 441, 445, 447, 449, 450, 451, 454, 455, 457, 464, 465, 466, 467, 468, 471, 477, 478, 479, 482, 483, 484, 486, 487, 490, 491, 492, 493, 494, 497, 498, 499, 500, 502, 503, 505, 507, 508, 510, 514, 515, 518, 519, 521, 522, 524, 525, 528, 529, 532, 533, 536, 537, 539, 540, 542, 543, 546, 547, 551, 552, 553, 555, 558, 559, 561, 562, 563, 564, 565, 567, 569, 613, 614, 615, 619, 622, 623, 625, 626, 628, 629, 632, 633, 636, 637, 639, 640, 643, 644, 646, 647, 649, 653, 654, 656, 657, 660, 661, 662, 663, 666, 667, 673, 674, 678, 679, 681, 682, 683, 684, 685, 687, 688, 690, 694, 695, 697, 698, 700, 701, 703, 704, 706, 708, 709, 720, 724, 730, 731, 732, 733, 736, 737, 738, 740], "excluded_lines": [], "functions": {"DatabasePerformanceOptimizer.__init__": {"executed_lines": [128, 129, 130, 133, 134, 135, 138, 139, 140, 143, 144], "summary": {"covered_lines": 11, "num_statements": 11, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DatabasePerformanceOptimizer.initialize": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [148, 150, 153, 156, 159, 162, 163, 165, 167, 168, 169], "excluded_lines": []}, "DatabasePerformanceOptimizer._initialize_postgresql": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [173, 175, 188, 191, 193, 197, 198, 199], "excluded_lines": []}, "DatabasePerformanceOptimizer._initialize_mongodb": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [203, 205, 219, 221, 225, 226, 227], "excluded_lines": []}, "DatabasePerformanceOptimizer._initialize_redis": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [231, 232, 243, 245, 248, 249, 250], "excluded_lines": []}, "DatabasePerformanceOptimizer._configure_postgresql_performance": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [254, 255, 257, 266, 267, 268, 269, 270, 273, 275, 277, 278], "excluded_lines": []}, "DatabasePerformanceOptimizer._setup_postgresql_monitoring": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [282, 283, 286, 287], "excluded_lines": []}, "DatabasePerformanceOptimizer._setup_postgresql_monitoring.receive_before_cursor_execute": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [284], "excluded_lines": []}, "DatabasePerformanceOptimizer._setup_postgresql_monitoring.receive_after_cursor_execute": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [288, 291, 292, 293, 295, 298, 299, 305], "excluded_lines": []}, "DatabasePerformanceOptimizer._create_optimized_indexes": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [311, 313, 316, 318, 320, 321], "excluded_lines": []}, "DatabasePerformanceOptimizer._create_postgresql_indexes": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [325, 326, 328, 349, 350, 351, 352, 353, 354, 355, 358, 360, 361], "excluded_lines": []}, "DatabasePerformanceOptimizer._create_mongodb_indexes": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [365, 366, 369, 377, 386, 394, 403, 411, 416, 418, 419], "excluded_lines": []}, "DatabasePerformanceOptimizer._performance_monitor": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [423, 424, 425, 426, 429, 430, 433, 434, 437, 439, 440, 441], "excluded_lines": []}, "DatabasePerformanceOptimizer._collect_performance_metrics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 37, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 37, "excluded_lines": 0}, "missing_lines": [445, 447, 449, 450, 451, 454, 455, 457, 464, 465, 466, 467, 468, 471, 477, 478, 479, 482, 483, 484, 486, 487, 490, 491, 492, 493, 494, 497, 498, 499, 500, 502, 503, 505, 507, 508, 510], "excluded_lines": []}, "DatabasePerformanceOptimizer._auto_optimize": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 20, "excluded_lines": 0}, "missing_lines": [514, 515, 518, 519, 521, 522, 524, 525, 528, 529, 532, 533, 536, 537, 539, 540, 542, 543, 546, 547], "excluded_lines": []}, "DatabasePerformanceOptimizer.get_performance_report": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [551, 552, 553, 555, 558, 559, 561, 562, 563, 564, 565, 567, 569, 613, 614, 615], "excluded_lines": []}, "DatabasePerformanceOptimizer._generate_recommendations": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [619, 622, 623, 625, 626, 628, 629, 632, 633, 636, 637, 639, 640, 643, 644, 646, 647, 649], "excluded_lines": []}, "DatabasePerformanceOptimizer.optimize_query_cache": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [653, 654, 656, 657, 660, 661, 662, 663, 666, 667, 673, 674], "excluded_lines": []}, "DatabasePerformanceOptimizer.get_cached_query": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [678, 679, 681, 682, 683, 684, 685, 687, 688, 690], "excluded_lines": []}, "DatabasePerformanceOptimizer.cleanup": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [694, 695, 697, 698, 700, 701, 703, 704, 706, 708, 709], "excluded_lines": []}, "initialize_database_optimization": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [720], "excluded_lines": []}, "get_performance_report": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [724], "excluded_lines": []}, "optimized_query_cache": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [730, 731, 732, 733, 736, 737, 738, 740], "excluded_lines": []}, "": {"executed_lines": [1, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 26, 32, 33, 34, 36, 37, 38, 39, 40, 43, 44, 45, 46, 47, 50, 51, 52, 54, 55, 56, 58, 59, 60, 61, 62, 65, 66, 67, 70, 71, 72, 74, 75, 76, 77, 78, 79, 80, 81, 82, 85, 86, 87, 89, 90, 91, 92, 95, 96, 97, 98, 99, 102, 103, 104, 105, 108, 109, 110, 113, 114, 115, 121, 122, 124, 146, 171, 201, 229, 252, 280, 309, 323, 363, 421, 443, 512, 549, 617, 651, 676, 692, 716, 718, 722, 726, 727], "summary": {"covered_lines": 95, "num_statements": 95, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"PostgreSQLConfig": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "MongoDBConfig": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "CacheConfig": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "PerformanceMetrics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DatabasePerformanceOptimizer": {"executed_lines": [128, 129, 130, 133, 134, 135, 138, 139, 140, 143, 144], "summary": {"covered_lines": 11, "num_statements": 235, "percent_covered": 4.680851063829787, "percent_covered_display": "5", "missing_lines": 224, "excluded_lines": 0}, "missing_lines": [148, 150, 153, 156, 159, 162, 163, 165, 167, 168, 169, 173, 175, 188, 191, 193, 197, 198, 199, 203, 205, 219, 221, 225, 226, 227, 231, 232, 243, 245, 248, 249, 250, 254, 255, 257, 266, 267, 268, 269, 270, 273, 275, 277, 278, 282, 283, 284, 286, 287, 288, 291, 292, 293, 295, 298, 299, 305, 311, 313, 316, 318, 320, 321, 325, 326, 328, 349, 350, 351, 352, 353, 354, 355, 358, 360, 361, 365, 366, 369, 377, 386, 394, 403, 411, 416, 418, 419, 423, 424, 425, 426, 429, 430, 433, 434, 437, 439, 440, 441, 445, 447, 449, 450, 451, 454, 455, 457, 464, 465, 466, 467, 468, 471, 477, 478, 479, 482, 483, 484, 486, 487, 490, 491, 492, 493, 494, 497, 498, 499, 500, 502, 503, 505, 507, 508, 510, 514, 515, 518, 519, 521, 522, 524, 525, 528, 529, 532, 533, 536, 537, 539, 540, 542, 543, 546, 547, 551, 552, 553, 555, 558, 559, 561, 562, 563, 564, 565, 567, 569, 613, 614, 615, 619, 622, 623, 625, 626, 628, 629, 632, 633, 636, 637, 639, 640, 643, 644, 646, 647, 649, 653, 654, 656, 657, 660, 661, 662, 663, 666, 667, 673, 674, 678, 679, 681, 682, 683, 684, 685, 687, 688, 690, 694, 695, 697, 698, 700, 701, 703, 704, 706, 708, 709], "excluded_lines": []}, "": {"executed_lines": [1, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 26, 32, 33, 34, 36, 37, 38, 39, 40, 43, 44, 45, 46, 47, 50, 51, 52, 54, 55, 56, 58, 59, 60, 61, 62, 65, 66, 67, 70, 71, 72, 74, 75, 76, 77, 78, 79, 80, 81, 82, 85, 86, 87, 89, 90, 91, 92, 95, 96, 97, 98, 99, 102, 103, 104, 105, 108, 109, 110, 113, 114, 115, 121, 122, 124, 146, 171, 201, 229, 252, 280, 309, 323, 363, 421, 443, 512, 549, 617, 651, 676, 692, 716, 718, 722, 726, 727], "summary": {"covered_lines": 95, "num_statements": 105, "percent_covered": 90.47619047619048, "percent_covered_display": "90", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [720, 724, 730, 731, 732, 733, 736, 737, 738, 740], "excluded_lines": []}}}, "middleware\\input_validator.py": {"executed_lines": [1, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 18, 20, 21, 23, 26, 27, 30, 46, 55, 63, 71, 73, 77, 80, 81, 82, 83, 86, 87, 88, 90, 93, 94, 95, 96, 99, 101, 104, 107, 108, 109, 110, 112, 114, 116, 118, 120, 121, 122, 124, 126, 129, 142, 143, 144, 145, 147, 149, 150, 151, 153, 155, 158, 160, 162, 165, 166, 167, 168, 169, 170, 181, 183, 185, 188, 190, 191, 193, 194, 195, 196, 197, 198, 208, 211, 212, 214, 216, 217, 219, 221, 259, 261, 263, 264, 271, 272, 273, 275, 282, 283, 284, 290, 291, 292, 308, 310, 311, 312, 314, 315, 317, 319, 320, 325, 327, 328, 330, 331, 338, 339, 342, 344, 345, 348, 349, 351, 352, 353, 355, 356, 358, 360, 363, 365, 367, 370, 374, 377, 378, 379, 381, 384, 386, 387, 395, 396, 405, 408, 409, 411, 412, 413, 416, 418, 420, 426], "summary": {"covered_lines": 154, "num_statements": 183, "percent_covered": 84.15300546448087, "percent_covered_display": "84", "missing_lines": 29, "excluded_lines": 0}, "missing_lines": [74, 156, 171, 172, 173, 174, 175, 176, 179, 186, 199, 200, 201, 202, 203, 204, 206, 265, 266, 268, 298, 299, 300, 371, 375, 398, 399, 400, 414], "excluded_lines": [], "functions": {"InputSanitizer.__init__": {"executed_lines": [26, 27, 30, 46, 55, 63], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "InputSanitizer.sanitize_string": {"executed_lines": [73, 77, 80, 81, 82, 83, 86, 87, 88, 90, 93, 94, 95, 96, 99, 101, 104, 107, 108, 109, 110, 112], "summary": {"covered_lines": 22, "num_statements": 23, "percent_covered": 95.65217391304348, "percent_covered_display": "96", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [74], "excluded_lines": []}, "InputSanitizer.sanitize_number": {"executed_lines": [116, 118, 120, 121, 122, 124, 126, 129, 142, 143, 144, 145, 147, 149, 150, 151], "summary": {"covered_lines": 16, "num_statements": 16, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "InputSanitizer.sanitize_dict": {"executed_lines": [155, 158, 160, 162, 165, 166, 167, 168, 169, 170, 181], "summary": {"covered_lines": 11, "num_statements": 19, "percent_covered": 57.89473684210526, "percent_covered_display": "58", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [156, 171, 172, 173, 174, 175, 176, 179], "excluded_lines": []}, "InputSanitizer.sanitize_list": {"executed_lines": [185, 188, 190, 191, 193, 194, 195, 196, 197, 198, 208], "summary": {"covered_lines": 11, "num_statements": 19, "percent_covered": 57.89473684210526, "percent_covered_display": "58", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [186, 199, 200, 201, 202, 203, 204, 206], "excluded_lines": []}, "InputValidator.__init__": {"executed_lines": [216, 217], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "InputValidator._load_validation_schemas": {"executed_lines": [221], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "InputValidator.validate_and_sanitize": {"executed_lines": [261, 263, 264, 271, 272, 273, 275, 282, 283, 284, 290, 291, 292], "summary": {"covered_lines": 13, "num_statements": 19, "percent_covered": 68.42105263157895, "percent_covered_display": "68", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [265, 266, 268, 298, 299, 300], "excluded_lines": []}, "validate_input": {"executed_lines": [310, 311, 345], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "validate_input.decorator": {"executed_lines": [312, 344], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "validate_input.decorator.decorated_function": {"executed_lines": [314, 315, 317, 319, 320, 325, 327, 328, 330, 331, 338, 339, 342], "summary": {"covered_lines": 13, "num_statements": 13, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "InputValidationMiddleware.__init__": {"executed_lines": [352, 353, 355, 356], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "InputValidationMiddleware.init_app": {"executed_lines": [360, 363, 365], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "InputValidationMiddleware.before_request": {"executed_lines": [370, 374, 377, 378, 379, 381, 384, 386, 387, 395, 396], "summary": {"covered_lines": 11, "num_statements": 16, "percent_covered": 68.75, "percent_covered_display": "69", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [371, 375, 398, 399, 400], "excluded_lines": []}, "InputValidationMiddleware.should_skip_validation": {"executed_lines": [408, 409, 411, 412, 413, 416], "summary": {"covered_lines": 6, "num_statements": 7, "percent_covered": 85.71428571428571, "percent_covered_display": "86", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [414], "excluded_lines": []}, "InputValidationMiddleware.get_schema_for_endpoint": {"executed_lines": [420, 426], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 18, 20, 21, 23, 71, 114, 153, 183, 211, 212, 214, 219, 259, 308, 348, 349, 351, 358, 367, 405, 418], "summary": {"covered_lines": 28, "num_statements": 28, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"InputSanitizer": {"executed_lines": [26, 27, 30, 46, 55, 63, 73, 77, 80, 81, 82, 83, 86, 87, 88, 90, 93, 94, 95, 96, 99, 101, 104, 107, 108, 109, 110, 112, 116, 118, 120, 121, 122, 124, 126, 129, 142, 143, 144, 145, 147, 149, 150, 151, 155, 158, 160, 162, 165, 166, 167, 168, 169, 170, 181, 185, 188, 190, 191, 193, 194, 195, 196, 197, 198, 208], "summary": {"covered_lines": 66, "num_statements": 83, "percent_covered": 79.51807228915662, "percent_covered_display": "80", "missing_lines": 17, "excluded_lines": 0}, "missing_lines": [74, 156, 171, 172, 173, 174, 175, 176, 179, 186, 199, 200, 201, 202, 203, 204, 206], "excluded_lines": []}, "InputValidator": {"executed_lines": [216, 217, 221, 261, 263, 264, 271, 272, 273, 275, 282, 283, 284, 290, 291, 292], "summary": {"covered_lines": 16, "num_statements": 22, "percent_covered": 72.72727272727273, "percent_covered_display": "73", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [265, 266, 268, 298, 299, 300], "excluded_lines": []}, "InputValidationMiddleware": {"executed_lines": [352, 353, 355, 356, 360, 363, 365, 370, 374, 377, 378, 379, 381, 384, 386, 387, 395, 396, 408, 409, 411, 412, 413, 416, 420, 426], "summary": {"covered_lines": 26, "num_statements": 32, "percent_covered": 81.25, "percent_covered_display": "81", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [371, 375, 398, 399, 400, 414], "excluded_lines": []}, "": {"executed_lines": [1, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 18, 20, 21, 23, 71, 114, 153, 183, 211, 212, 214, 219, 259, 308, 310, 311, 312, 314, 315, 317, 319, 320, 325, 327, 328, 330, 331, 338, 339, 342, 344, 345, 348, 349, 351, 358, 367, 405, 418], "summary": {"covered_lines": 46, "num_statements": 46, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "middleware\\rate_limiter.py": {"executed_lines": [1, 6, 7, 8, 9, 10, 11, 12, 13, 16, 18, 19, 21, 23, 31, 38, 46, 48, 51, 52, 53, 56, 57, 59, 61, 63, 65, 66, 67, 68, 69, 71, 73, 76, 77, 80, 81, 83, 85, 86, 87, 90, 91, 94, 95, 98, 99, 107, 108, 109, 110, 112, 119, 120, 122, 124, 126, 127, 128, 129, 130, 132, 135, 137, 138, 139, 141, 144, 147, 149, 150, 155, 156, 159, 162, 163, 165, 167, 168, 171, 172, 174, 175, 176, 178, 179, 181, 183, 184, 187, 189, 191, 194, 197, 198, 200, 201, 207, 208, 211, 213, 215, 216, 218, 220, 223, 225, 226, 227, 230, 231, 233], "summary": {"covered_lines": 109, "num_statements": 110, "percent_covered": 99.0909090909091, "percent_covered_display": "99", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [195], "excluded_lines": [], "functions": {"RateLimiter.__init__": {"executed_lines": [23, 31, 38, 46], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RateLimiter.get_client_identifier": {"executed_lines": [51, 52, 53, 56, 57, 59, 61], "summary": {"covered_lines": 7, "num_statements": 7, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RateLimiter.get_user_tier": {"executed_lines": [65, 66, 67, 68, 69, 71], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RateLimiter.get_rate_limit_config": {"executed_lines": [76, 77, 80, 81], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RateLimiter.is_rate_limited": {"executed_lines": [85, 86, 87, 90, 91, 94, 95, 98, 99, 107, 108, 109, 110, 112, 119, 120, 122], "summary": {"covered_lines": 17, "num_statements": 17, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RateLimiter.add_rate_limit_headers": {"executed_lines": [126, 127, 128, 129, 130, 132], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "rate_limit": {"executed_lines": [137, 138, 168], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "rate_limit.decorator": {"executed_lines": [139, 167], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "rate_limit.decorator.decorated_function": {"executed_lines": [141, 144, 147, 149, 150, 155, 156, 159, 162, 163, 165], "summary": {"covered_lines": 11, "num_statements": 11, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RateLimitMiddleware.__init__": {"executed_lines": [175, 176, 178, 179], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RateLimitMiddleware.init_app": {"executed_lines": [183, 184, 187, 189], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RateLimitMiddleware.before_request": {"executed_lines": [194, 197, 198, 200, 201, 207, 208, 211], "summary": {"covered_lines": 8, "num_statements": 9, "percent_covered": 88.88888888888889, "percent_covered_display": "89", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [195], "excluded_lines": []}, "RateLimitMiddleware.after_request": {"executed_lines": [215, 216, 218], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RateLimitMiddleware.should_skip_rate_limiting": {"executed_lines": [223, 225, 226, 227, 230, 231, 233], "summary": {"covered_lines": 7, "num_statements": 7, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 6, 7, 8, 9, 10, 11, 12, 13, 16, 18, 19, 21, 48, 63, 73, 83, 124, 135, 171, 172, 174, 181, 191, 213, 220], "summary": {"covered_lines": 23, "num_statements": 23, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"RateLimiter": {"executed_lines": [23, 31, 38, 46, 51, 52, 53, 56, 57, 59, 61, 65, 66, 67, 68, 69, 71, 76, 77, 80, 81, 85, 86, 87, 90, 91, 94, 95, 98, 99, 107, 108, 109, 110, 112, 119, 120, 122, 126, 127, 128, 129, 130, 132], "summary": {"covered_lines": 44, "num_statements": 44, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RateLimitMiddleware": {"executed_lines": [175, 176, 178, 179, 183, 184, 187, 189, 194, 197, 198, 200, 201, 207, 208, 211, 215, 216, 218, 223, 225, 226, 227, 230, 231, 233], "summary": {"covered_lines": 26, "num_statements": 27, "percent_covered": 96.29629629629629, "percent_covered_display": "96", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [195], "excluded_lines": []}, "": {"executed_lines": [1, 6, 7, 8, 9, 10, 11, 12, 13, 16, 18, 19, 21, 48, 63, 73, 83, 124, 135, 137, 138, 139, 141, 144, 147, 149, 150, 155, 156, 159, 162, 163, 165, 167, 168, 171, 172, 174, 181, 191, 213, 220], "summary": {"covered_lines": 39, "num_statements": 39, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "middleware\\security_headers.py": {"executed_lines": [1, 6, 7, 8, 9, 11, 13, 14, 16, 18, 21, 85, 119, 136, 137, 139, 142, 145, 147, 149, 151, 152, 154, 156, 159, 160, 161, 162, 163, 165, 167, 170, 171, 172, 173, 174, 176, 178, 180, 182, 185, 186, 188, 190, 193, 194, 195, 199, 202, 203, 208, 210, 212, 215, 216, 219, 220, 223, 228, 229, 230, 232, 234, 239, 240, 241, 242, 244, 245, 249, 251, 253, 254, 262, 263, 264, 267, 268, 269, 270, 272, 278, 280, 281, 284, 287, 288, 289, 290, 291, 292, 293, 294, 295, 298, 299, 300, 301, 306, 311, 312, 313, 318, 320], "summary": {"covered_lines": 102, "num_statements": 113, "percent_covered": 90.26548672566372, "percent_covered_display": "90", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [196, 205, 206, 224, 225, 265, 274, 275, 276, 303, 315], "excluded_lines": [], "functions": {"SecurityHeadersMiddleware.__init__": {"executed_lines": [18, 21, 85, 119, 136, 137], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SecurityHeadersMiddleware.init_app": {"executed_lines": [142, 145, 147], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SecurityHeadersMiddleware.get_environment": {"executed_lines": [151, 152], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SecurityHeadersMiddleware.get_headers_for_request": {"executed_lines": [156, 159, 160, 161, 162, 163, 165, 167, 170, 171, 172, 173, 174, 176], "summary": {"covered_lines": 14, "num_statements": 14, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SecurityHeadersMiddleware.add_security_headers": {"executed_lines": [180, 182, 185, 186, 188, 190, 193, 194, 195, 199, 202, 203, 208], "summary": {"covered_lines": 13, "num_statements": 16, "percent_covered": 81.25, "percent_covered_display": "81", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [196, 205, 206], "excluded_lines": []}, "SecurityHeadersMiddleware._add_content_specific_headers": {"executed_lines": [212, 215, 216, 219, 220, 223, 228, 229, 230], "summary": {"covered_lines": 9, "num_statements": 11, "percent_covered": 81.81818181818181, "percent_covered_display": "82", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [224, 225], "excluded_lines": []}, "SecurityHeadersMiddleware._log_security_headers": {"executed_lines": [234, 239, 240, 241, 242, 244, 245], "summary": {"covered_lines": 7, "num_statements": 7, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SecurityHeadersMiddleware.validate_csp_policy": {"executed_lines": [251, 253, 254, 262, 263, 264, 267, 268, 269, 270, 272], "summary": {"covered_lines": 11, "num_statements": 15, "percent_covered": 73.33333333333333, "percent_covered_display": "73", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [265, 274, 275, 276], "excluded_lines": []}, "SecurityHeadersMiddleware.update_csp_for_endpoint": {"executed_lines": [280, 281, 284, 287, 288, 289, 290, 291, 292, 293, 294, 295, 298, 299, 300, 301, 306, 311, 312, 313], "summary": {"covered_lines": 20, "num_statements": 22, "percent_covered": 90.9090909090909, "percent_covered_display": "91", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [303, 315], "excluded_lines": []}, "create_security_headers_middleware": {"executed_lines": [320], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 6, 7, 8, 9, 11, 13, 14, 16, 139, 149, 154, 178, 210, 232, 249, 278, 318], "summary": {"covered_lines": 16, "num_statements": 16, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"SecurityHeadersMiddleware": {"executed_lines": [18, 21, 85, 119, 136, 137, 142, 145, 147, 151, 152, 156, 159, 160, 161, 162, 163, 165, 167, 170, 171, 172, 173, 174, 176, 180, 182, 185, 186, 188, 190, 193, 194, 195, 199, 202, 203, 208, 212, 215, 216, 219, 220, 223, 228, 229, 230, 234, 239, 240, 241, 242, 244, 245, 251, 253, 254, 262, 263, 264, 267, 268, 269, 270, 272, 280, 281, 284, 287, 288, 289, 290, 291, 292, 293, 294, 295, 298, 299, 300, 301, 306, 311, 312, 313], "summary": {"covered_lines": 85, "num_statements": 96, "percent_covered": 88.54166666666667, "percent_covered_display": "89", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [196, 205, 206, 224, 225, 265, 274, 275, 276, 303, 315], "excluded_lines": []}, "": {"executed_lines": [1, 6, 7, 8, 9, 11, 13, 14, 16, 139, 149, 154, 178, 210, 232, 249, 278, 318, 320], "summary": {"covered_lines": 17, "num_statements": 17, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "security\\credential_manager.py": {"executed_lines": [1, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 18, 20, 21, 23, 25, 26, 28, 29, 32, 76, 84, 86, 88, 90, 91, 97, 98, 102, 108, 110, 112, 114, 116, 117, 118, 120, 121, 122, 124, 127, 129, 131, 132, 133, 134, 135, 140, 143, 144, 145, 148, 149, 151, 157, 159, 160, 161, 162, 164, 168, 171, 172, 173, 174, 180, 181, 183, 189, 191, 212, 214, 222, 224, 232, 234, 242, 248, 249, 250, 252, 253, 254, 255, 259, 260, 263, 264, 265, 266, 267, 268, 269, 273, 275, 277, 284, 285, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 298, 300, 302, 304, 306, 310, 312, 316, 317, 319, 321, 323, 325], "summary": {"covered_lines": 120, "num_statements": 138, "percent_covered": 86.95652173913044, "percent_covered_display": "87", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [100, 104, 105, 106, 136, 137, 138, 153, 154, 155, 165, 166, 175, 176, 177, 185, 186, 187], "excluded_lines": [], "functions": {"CredentialManager.__init__": {"executed_lines": [25, 26, 28, 29, 32, 76], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "CredentialManager._initialize_encryption": {"executed_lines": [86, 88, 90, 91, 97, 98, 102], "summary": {"covered_lines": 7, "num_statements": 11, "percent_covered": 63.63636363636363, "percent_covered_display": "64", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [100, 104, 105, 106], "excluded_lines": []}, "CredentialManager._generate_secure_key": {"executed_lines": [110], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "CredentialManager.get_credential": {"executed_lines": [114, 116, 117, 118, 120, 121, 122, 124, 127, 129, 131, 132, 133, 134, 135, 140, 143, 144, 145, 148, 149, 151], "summary": {"covered_lines": 22, "num_statements": 28, "percent_covered": 78.57142857142857, "percent_covered_display": "79", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [136, 137, 138, 153, 154, 155], "excluded_lines": []}, "CredentialManager.set_credential": {"executed_lines": [159, 160, 161, 162, 164, 168, 171, 172, 173, 174, 180, 181, 183], "summary": {"covered_lines": 13, "num_statements": 21, "percent_covered": 61.904761904761905, "percent_covered_display": "62", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [165, 166, 175, 176, 177, 185, 186, 187], "excluded_lines": []}, "CredentialManager.get_database_config": {"executed_lines": [191], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "CredentialManager.get_application_secrets": {"executed_lines": [214], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "CredentialManager.get_external_api_keys": {"executed_lines": [224], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "CredentialManager.validate_credentials": {"executed_lines": [234, 242, 248, 249, 250, 252, 253, 254, 255, 259, 260, 263, 264, 265, 266, 267, 268, 269, 273], "summary": {"covered_lines": 19, "num_statements": 19, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "CredentialManager.generate_env_template": {"executed_lines": [277, 284, 285, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 298, 300, 302, 304, 306], "summary": {"covered_lines": 18, "num_statements": 18, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "get_credential_manager": {"executed_lines": [316, 317, 319], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "get_secure_config": {"executed_lines": [323, 325], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 18, 20, 21, 23, 84, 108, 112, 157, 189, 212, 222, 232, 275, 310, 312, 321], "summary": {"covered_lines": 26, "num_statements": 26, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"CredentialManager": {"executed_lines": [25, 26, 28, 29, 32, 76, 86, 88, 90, 91, 97, 98, 102, 110, 114, 116, 117, 118, 120, 121, 122, 124, 127, 129, 131, 132, 133, 134, 135, 140, 143, 144, 145, 148, 149, 151, 159, 160, 161, 162, 164, 168, 171, 172, 173, 174, 180, 181, 183, 191, 214, 224, 234, 242, 248, 249, 250, 252, 253, 254, 255, 259, 260, 263, 264, 265, 266, 267, 268, 269, 273, 277, 284, 285, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 298, 300, 302, 304, 306], "summary": {"covered_lines": 89, "num_statements": 107, "percent_covered": 83.17757009345794, "percent_covered_display": "83", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [100, 104, 105, 106, 136, 137, 138, 153, 154, 155, 165, 166, 175, 176, 177, 185, 186, 187], "excluded_lines": []}, "": {"executed_lines": [1, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 18, 20, 21, 23, 84, 108, 112, 157, 189, 212, 222, 232, 275, 310, 312, 316, 317, 319, 321, 323, 325], "summary": {"covered_lines": 31, "num_statements": 31, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "sentry_config.py": {"executed_lines": [1, 8, 9, 10, 11, 12, 15, 16, 17, 23, 29, 39, 43, 47, 53, 58, 61, 62, 63, 66, 91, 92, 93, 96, 99, 100, 103, 116, 117, 118, 122, 126, 130, 133, 136, 139, 140, 143, 152, 159, 162, 182, 216, 252, 276], "summary": {"covered_lines": 44, "num_statements": 81, "percent_covered": 54.32098765432099, "percent_covered_display": "54", "missing_lines": 37, "excluded_lines": 0}, "missing_lines": [18, 19, 20, 40, 44, 119, 123, 128, 193, 199, 200, 203, 204, 205, 208, 209, 210, 213, 227, 228, 229, 231, 232, 235, 236, 237, 239, 241, 243, 245, 246, 249, 263, 264, 265, 266, 272], "excluded_lines": [], "functions": {"init_sentry": {"executed_lines": [39, 43, 47, 53, 58, 61, 62, 63, 66, 91, 92, 93, 96, 99, 100], "summary": {"covered_lines": 15, "num_statements": 17, "percent_covered": 88.23529411764706, "percent_covered_display": "88", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [40, 44], "excluded_lines": []}, "filter_errors": {"executed_lines": [116, 117, 118, 122, 126, 130], "summary": {"covered_lines": 6, "num_statements": 9, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [119, 123, 128], "excluded_lines": []}, "configure_structured_logging": {"executed_lines": [136, 162], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "configure_structured_logging.add_sentry_breadcrumb": {"executed_lines": [139, 140, 143, 152, 159], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "capture_calculation_performance": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [193, 199, 200, 203, 204, 205, 208, 209, 210, 213], "excluded_lines": []}, "sanitize_calculation_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [227, 228, 229, 231, 232, 235, 236, 237, 239, 241, 243, 245, 246, 249], "excluded_lines": []}, "capture_api_error": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [263, 264, 265, 266, 272], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 15, 16, 17, 23, 29, 103, 133, 182, 216, 252, 276], "summary": {"covered_lines": 16, "num_statements": 19, "percent_covered": 84.21052631578948, "percent_covered_display": "84", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [18, 19, 20], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 8, 9, 10, 11, 12, 15, 16, 17, 23, 29, 39, 43, 47, 53, 58, 61, 62, 63, 66, 91, 92, 93, 96, 99, 100, 103, 116, 117, 118, 122, 126, 130, 133, 136, 139, 140, 143, 152, 159, 162, 182, 216, 252, 276], "summary": {"covered_lines": 44, "num_statements": 81, "percent_covered": 54.32098765432099, "percent_covered_display": "54", "missing_lines": 37, "excluded_lines": 0}, "missing_lines": [18, 19, 20, 40, 44, 119, 123, 128, 193, 199, 200, 203, 204, 205, 208, 209, 210, 213, 227, 228, 229, 231, 232, 235, 236, 237, 239, 241, 243, 245, 246, 249, 263, 264, 265, 266, 272], "excluded_lines": []}}}, "services\\mongodb_service.py": {"executed_lines": [1, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 23, 25, 26, 28, 29, 30, 31, 32, 39, 56, 87, 124, 143, 164, 183, 219, 277, 299, 317, 335, 352, 374, 394, 407, 430, 448, 507, 549, 585, 607, 610], "summary": {"covered_lines": 40, "num_statements": 315, "percent_covered": 12.698412698412698, "percent_covered_display": "13", "missing_lines": 275, "excluded_lines": 0}, "missing_lines": [41, 42, 45, 46, 47, 50, 51, 58, 59, 60, 61, 64, 69, 70, 73, 74, 76, 80, 81, 83, 84, 85, 89, 90, 92, 94, 95, 96, 97, 98, 101, 107, 108, 111, 112, 116, 117, 119, 120, 122, 126, 127, 129, 134, 135, 136, 137, 139, 140, 141, 145, 147, 148, 149, 150, 153, 155, 156, 157, 158, 160, 161, 162, 166, 167, 168, 171, 172, 176, 177, 179, 180, 185, 186, 187, 188, 189, 192, 193, 196, 197, 199, 200, 203, 204, 206, 211, 212, 214, 215, 217, 221, 222, 223, 225, 226, 227, 228, 229, 232, 233, 235, 236, 237, 238, 239, 242, 243, 245, 248, 251, 254, 255, 257, 259, 264, 265, 267, 268, 271, 272, 273, 275, 279, 280, 281, 282, 284, 285, 287, 288, 289, 291, 293, 294, 296, 301, 302, 303, 304, 306, 307, 310, 312, 313, 315, 319, 320, 321, 323, 324, 325, 327, 329, 330, 332, 337, 338, 339, 341, 342, 345, 347, 348, 350, 354, 355, 356, 357, 359, 360, 362, 363, 364, 366, 368, 369, 371, 376, 377, 378, 380, 386, 387, 389, 390, 392, 396, 397, 398, 399, 400, 402, 403, 405, 409, 410, 412, 413, 415, 416, 417, 418, 419, 420, 421, 423, 432, 433, 434, 436, 441, 442, 444, 445, 450, 451, 453, 455, 456, 457, 458, 459, 462, 476, 477, 480, 487, 493, 494, 498, 499, 501, 502, 504, 512, 514, 516, 524, 525, 528, 533, 534, 537, 538, 540, 541, 543, 544, 546, 551, 553, 556, 557, 558, 559, 560, 566, 567, 569, 581, 582, 583, 587, 589, 591, 592, 593, 594, 595, 596, 600, 601, 603, 604], "excluded_lines": [], "functions": {"EnhancedMongoDBService.__init__": {"executed_lines": [29, 30, 31, 32], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "EnhancedMongoDBService._track_query_performance": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [41, 42, 45, 46, 47, 50, 51], "excluded_lines": []}, "EnhancedMongoDBService.create_project": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [58, 59, 60, 61, 64, 69, 70, 73, 74, 76, 80, 81, 83, 84, 85], "excluded_lines": []}, "EnhancedMongoDBService.get_project": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [89, 90, 92, 94, 95, 96, 97, 98, 101, 107, 108, 111, 112, 116, 117, 119, 120, 122], "excluded_lines": []}, "EnhancedMongoDBService.update_project": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [126, 127, 129, 134, 135, 136, 137, 139, 140, 141], "excluded_lines": []}, "EnhancedMongoDBService.delete_project": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [145, 147, 148, 149, 150, 153, 155, 156, 157, 158, 160, 161, 162], "excluded_lines": []}, "EnhancedMongoDBService._invalidate_project_cache": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [166, 167, 168, 171, 172, 176, 177, 179, 180], "excluded_lines": []}, "EnhancedMongoDBService.save_spatial_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 19, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 19, "excluded_lines": 0}, "missing_lines": [185, 186, 187, 188, 189, 192, 193, 196, 197, 199, 200, 203, 204, 206, 211, 212, 214, 215, 217], "excluded_lines": []}, "EnhancedMongoDBService.bulk_save_spatial_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 32, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 32, "excluded_lines": 0}, "missing_lines": [221, 222, 223, 225, 226, 227, 228, 229, 232, 233, 235, 236, 237, 238, 239, 242, 243, 245, 248, 251, 254, 255, 257, 259, 264, 265, 267, 268, 271, 272, 273, 275], "excluded_lines": []}, "EnhancedMongoDBService.get_spatial_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [279, 280, 281, 282, 284, 285, 287, 288, 289, 291, 293, 294, 296], "excluded_lines": []}, "EnhancedMongoDBService.save_hvac_system": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [301, 302, 303, 304, 306, 307, 310, 312, 313, 315], "excluded_lines": []}, "EnhancedMongoDBService.get_hvac_systems": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [319, 320, 321, 323, 324, 325, 327, 329, 330, 332], "excluded_lines": []}, "EnhancedMongoDBService.save_calculation_result": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [337, 338, 339, 341, 342, 345, 347, 348, 350], "excluded_lines": []}, "EnhancedMongoDBService.get_calculation_results": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [354, 355, 356, 357, 359, 360, 362, 363, 364, 366, 368, 369, 371], "excluded_lines": []}, "EnhancedMongoDBService.save_user_preferences": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [376, 377, 378, 380, 386, 387, 389, 390, 392], "excluded_lines": []}, "EnhancedMongoDBService.get_user_preferences": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [396, 397, 398, 399, 400, 402, 403, 405], "excluded_lines": []}, "EnhancedMongoDBService._calculate_bounds": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [409, 410, 412, 413, 415, 416, 417, 418, 419, 420, 421, 423], "excluded_lines": []}, "EnhancedMongoDBService._invalidate_spatial_cache": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [432, 433, 434, 436, 441, 442, 444, 445], "excluded_lines": []}, "EnhancedMongoDBService.get_project_analytics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 20, "excluded_lines": 0}, "missing_lines": [450, 451, 453, 455, 456, 457, 458, 459, 462, 476, 477, 480, 487, 493, 494, 498, 499, 501, 502, 504], "excluded_lines": []}, "EnhancedMongoDBService.find_spatial_data_in_bounds": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [512, 514, 516, 524, 525, 528, 533, 534, 537, 538, 540, 541, 543, 544, 546], "excluded_lines": []}, "EnhancedMongoDBService.get_service_metrics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [551, 553, 556, 557, 558, 559, 560, 566, 567, 569, 581, 582, 583], "excluded_lines": []}, "EnhancedMongoDBService.optimize_collections": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [587, 589, 591, 592, 593, 594, 595, 596, 600, 601, 603, 604], "excluded_lines": []}, "": {"executed_lines": [1, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 23, 25, 26, 28, 39, 56, 87, 124, 143, 164, 183, 219, 277, 299, 317, 335, 352, 374, 394, 407, 430, 448, 507, 549, 585, 607, 610], "summary": {"covered_lines": 36, "num_statements": 36, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"EnhancedMongoDBService": {"executed_lines": [29, 30, 31, 32], "summary": {"covered_lines": 4, "num_statements": 279, "percent_covered": 1.4336917562724014, "percent_covered_display": "1", "missing_lines": 275, "excluded_lines": 0}, "missing_lines": [41, 42, 45, 46, 47, 50, 51, 58, 59, 60, 61, 64, 69, 70, 73, 74, 76, 80, 81, 83, 84, 85, 89, 90, 92, 94, 95, 96, 97, 98, 101, 107, 108, 111, 112, 116, 117, 119, 120, 122, 126, 127, 129, 134, 135, 136, 137, 139, 140, 141, 145, 147, 148, 149, 150, 153, 155, 156, 157, 158, 160, 161, 162, 166, 167, 168, 171, 172, 176, 177, 179, 180, 185, 186, 187, 188, 189, 192, 193, 196, 197, 199, 200, 203, 204, 206, 211, 212, 214, 215, 217, 221, 222, 223, 225, 226, 227, 228, 229, 232, 233, 235, 236, 237, 238, 239, 242, 243, 245, 248, 251, 254, 255, 257, 259, 264, 265, 267, 268, 271, 272, 273, 275, 279, 280, 281, 282, 284, 285, 287, 288, 289, 291, 293, 294, 296, 301, 302, 303, 304, 306, 307, 310, 312, 313, 315, 319, 320, 321, 323, 324, 325, 327, 329, 330, 332, 337, 338, 339, 341, 342, 345, 347, 348, 350, 354, 355, 356, 357, 359, 360, 362, 363, 364, 366, 368, 369, 371, 376, 377, 378, 380, 386, 387, 389, 390, 392, 396, 397, 398, 399, 400, 402, 403, 405, 409, 410, 412, 413, 415, 416, 417, 418, 419, 420, 421, 423, 432, 433, 434, 436, 441, 442, 444, 445, 450, 451, 453, 455, 456, 457, 458, 459, 462, 476, 477, 480, 487, 493, 494, 498, 499, 501, 502, 504, 512, 514, 516, 524, 525, 528, 533, 534, 537, 538, 540, 541, 543, 544, 546, 551, 553, 556, 557, 558, 559, 560, 566, 567, 569, 581, 582, 583, 587, 589, 591, 592, 593, 594, 595, 596, 600, 601, 603, 604], "excluded_lines": []}, "": {"executed_lines": [1, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 23, 25, 26, 28, 39, 56, 87, 124, 143, 164, 183, 219, 277, 299, 317, 335, 352, 374, 394, 407, 430, 448, 507, 549, 585, 607, 610], "summary": {"covered_lines": 36, "num_statements": 36, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "test_security.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 117, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 117, "excluded_lines": 0}, "missing_lines": [9, 10, 11, 12, 14, 16, 17, 18, 20, 23, 24, 26, 27, 28, 30, 31, 32, 34, 35, 37, 38, 39, 41, 43, 44, 45, 48, 54, 55, 56, 58, 59, 60, 62, 63, 65, 66, 67, 69, 71, 72, 75, 76, 79, 80, 81, 83, 84, 86, 87, 88, 90, 92, 94, 95, 98, 99, 102, 103, 105, 107, 108, 109, 111, 113, 114, 115, 117, 118, 121, 122, 124, 125, 126, 128, 129, 131, 132, 133, 135, 137, 138, 139, 140, 141, 143, 151, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 165, 166, 167, 169, 170, 172, 173, 174, 175, 176, 177, 179, 180, 182, 183, 184, 186, 187, 189, 190], "excluded_lines": [], "functions": {"test_cryptography_package": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 17, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 17, "excluded_lines": 0}, "missing_lines": [16, 17, 18, 20, 23, 24, 26, 27, 28, 30, 31, 32, 34, 35, 37, 38, 39], "excluded_lines": []}, "test_jwt_functionality": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [43, 44, 45, 48, 54, 55, 56, 58, 59, 60, 62, 63, 65, 66, 67], "excluded_lines": []}, "test_password_hashing": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [71, 72, 75, 76, 79, 80, 81, 83, 84, 86, 87, 88], "excluded_lines": []}, "test_database_connections": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [92, 94, 95, 98, 99, 102, 103, 105, 107, 108, 109], "excluded_lines": []}, "test_flask_security": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [113, 114, 115, 117, 118, 121, 122, 124, 125, 128, 129, 131, 132, 133], "excluded_lines": []}, "test_flask_security.test_route": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [126], "excluded_lines": []}, "main": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 35, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 35, "excluded_lines": 0}, "missing_lines": [137, 138, 139, 140, 141, 143, 151, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 165, 166, 167, 169, 170, 172, 173, 174, 175, 176, 177, 179, 180, 182, 183, 184, 186, 187], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [9, 10, 11, 12, 14, 41, 69, 90, 111, 135, 189, 190], "excluded_lines": []}}, "classes": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 117, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 117, "excluded_lines": 0}, "missing_lines": [9, 10, 11, 12, 14, 16, 17, 18, 20, 23, 24, 26, 27, 28, 30, 31, 32, 34, 35, 37, 38, 39, 41, 43, 44, 45, 48, 54, 55, 56, 58, 59, 60, 62, 63, 65, 66, 67, 69, 71, 72, 75, 76, 79, 80, 81, 83, 84, 86, 87, 88, 90, 92, 94, 95, 98, 99, 102, 103, 105, 107, 108, 109, 111, 113, 114, 115, 117, 118, 121, 122, 124, 125, 126, 128, 129, 131, 132, 133, 135, 137, 138, 139, 140, 141, 143, 151, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 165, 166, 167, 169, 170, 172, 173, 174, 175, 176, 177, 179, 180, 182, 183, 184, 186, 187, 189, 190], "excluded_lines": []}}}, "tests\\__init__.py": {"executed_lines": [0], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "tests\\test_analytics_routes.py": {"executed_lines": [1, 6, 7, 8, 9, 10, 13, 14, 15, 17, 18, 20, 21, 22, 25, 26, 28, 29, 31, 32, 33, 34, 36, 37, 41, 43, 44, 46, 48, 50, 52, 55, 56, 58, 59, 61, 62, 63, 64, 66, 67, 69, 71, 73, 76, 78, 83, 85, 86, 88, 93, 96, 98, 100, 105, 108, 110, 111, 131, 132, 134, 135, 137, 138, 139, 140, 142, 143, 145, 147, 149, 152, 154, 158, 160, 163, 165, 169, 171, 174, 176, 178, 181, 183, 189, 190, 192, 193, 195, 196, 197, 198, 200, 201, 203, 205, 207, 210, 212, 217, 219, 222, 224, 226, 229, 231, 233, 236, 239, 240, 242, 243, 245, 246, 247, 248, 250, 251, 253, 255, 257, 269, 274, 276, 278, 290, 295, 297, 299, 311, 316, 318, 320, 327, 328, 333, 336, 337, 339, 340, 342, 343, 344, 345, 347, 348, 350, 352, 354, 361, 365, 367, 369, 376, 380, 382, 384, 391, 395, 397, 399, 402, 405, 406, 408, 409, 411, 412, 413, 414, 416, 417, 419, 421, 423, 430, 431, 434, 436, 438, 444, 445, 448, 450, 451, 463, 464, 466, 467, 469, 470, 471, 472, 474, 475, 477, 479, 483, 486, 488, 490, 492, 497, 499, 502, 503, 504, 505, 509, 510, 513, 514, 516, 517, 519, 520, 521, 522, 524, 525, 527, 529, 531, 533, 534, 535, 537, 540, 541, 543, 545, 552, 556, 558, 560, 562, 564, 565, 566, 567, 570, 571, 572, 573, 574, 577, 578, 581, 583, 584], "summary": {"covered_lines": 240, "num_statements": 263, "percent_covered": 91.25475285171103, "percent_covered_display": "91", "missing_lines": 23, "excluded_lines": 0}, "missing_lines": [39, 79, 81, 114, 120, 122, 124, 125, 126, 127, 128, 155, 156, 166, 167, 184, 186, 213, 215, 454, 456, 457, 460], "excluded_lines": [], "functions": {"TestAnalyticsBlueprint.app": {"executed_lines": [31, 32, 33, 34], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestAnalyticsBlueprint.client": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [39], "excluded_lines": []}, "TestAnalyticsBlueprint.test_blueprint_registration": {"executed_lines": [43, 44], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestAnalyticsBlueprint.test_blueprint_url_prefix": {"executed_lines": [48, 50, 52], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestAnalyticsDashboard.app": {"executed_lines": [61, 62, 63, 64], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestAnalyticsDashboard.client": {"executed_lines": [69], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestAnalyticsDashboard.test_dashboard_overview": {"executed_lines": [73, 76, 78], "summary": {"covered_lines": 3, "num_statements": 5, "percent_covered": 60.0, "percent_covered_display": "60", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [79, 81], "excluded_lines": []}, "TestAnalyticsDashboard.test_dashboard_with_date_range": {"executed_lines": [85, 86, 88, 93, 96], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestAnalyticsDashboard.test_dashboard_invalid_date_range": {"executed_lines": [100, 105, 108], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestAnalyticsDashboard.test_dashboard_data_structure": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [114, 120, 122, 124, 125, 126, 127, 128], "excluded_lines": []}, "TestUsageAnalytics.app": {"executed_lines": [137, 138, 139, 140], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestUsageAnalytics.client": {"executed_lines": [145], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestUsageAnalytics.test_usage_statistics": {"executed_lines": [149, 152, 154], "summary": {"covered_lines": 3, "num_statements": 5, "percent_covered": 60.0, "percent_covered_display": "60", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [155, 156], "excluded_lines": []}, "TestUsageAnalytics.test_tool_usage_breakdown": {"executed_lines": [160, 163, 165], "summary": {"covered_lines": 3, "num_statements": 5, "percent_covered": 60.0, "percent_covered_display": "60", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [166, 167], "excluded_lines": []}, "TestUsageAnalytics.test_user_activity_metrics": {"executed_lines": [171, 174], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestUsageAnalytics.test_calculation_trends": {"executed_lines": [178, 181, 183], "summary": {"covered_lines": 3, "num_statements": 5, "percent_covered": 60.0, "percent_covered_display": "60", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [184, 186], "excluded_lines": []}, "TestPerformanceAnalytics.app": {"executed_lines": [195, 196, 197, 198], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestPerformanceAnalytics.client": {"executed_lines": [203], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestPerformanceAnalytics.test_performance_metrics": {"executed_lines": [207, 210, 212], "summary": {"covered_lines": 3, "num_statements": 5, "percent_covered": 60.0, "percent_covered_display": "60", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [213, 215], "excluded_lines": []}, "TestPerformanceAnalytics.test_response_time_analytics": {"executed_lines": [219, 222], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestPerformanceAnalytics.test_error_rate_analytics": {"executed_lines": [226, 229], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestPerformanceAnalytics.test_system_health_metrics": {"executed_lines": [233, 236], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestAnalyticsDataCollection.app": {"executed_lines": [245, 246, 247, 248], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestAnalyticsDataCollection.client": {"executed_lines": [253], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestAnalyticsDataCollection.test_track_calculation_event": {"executed_lines": [257, 269, 274], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestAnalyticsDataCollection.test_track_user_session": {"executed_lines": [278, 290, 295], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestAnalyticsDataCollection.test_track_error_event": {"executed_lines": [299, 311, 316], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestAnalyticsDataCollection.test_invalid_event_data": {"executed_lines": [320, 327, 328, 333], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestAnalyticsReporting.app": {"executed_lines": [342, 343, 344, 345], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestAnalyticsReporting.client": {"executed_lines": [350], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestAnalyticsReporting.test_generate_usage_report": {"executed_lines": [354, 361, 365], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestAnalyticsReporting.test_generate_performance_report": {"executed_lines": [369, 376, 380], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestAnalyticsReporting.test_export_analytics_data": {"executed_lines": [384, 391, 395], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestAnalyticsReporting.test_scheduled_reports": {"executed_lines": [399, 402], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestAnalyticsAuthentication.app": {"executed_lines": [411, 412, 413, 414], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestAnalyticsAuthentication.client": {"executed_lines": [419], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestAnalyticsAuthentication.test_unauthorized_access": {"executed_lines": [423, 430, 431, 434], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestAnalyticsAuthentication.test_admin_only_endpoints": {"executed_lines": [438, 444, 445, 448], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestAnalyticsAuthentication.test_valid_token_access": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [454, 456, 457, 460], "excluded_lines": []}, "TestAnalyticsErrorHandling.app": {"executed_lines": [469, 470, 471, 472], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestAnalyticsErrorHandling.client": {"executed_lines": [477], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestAnalyticsErrorHandling.test_database_connection_error": {"executed_lines": [483, 486], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestAnalyticsErrorHandling.test_malformed_request_handling": {"executed_lines": [490, 492, 497], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestAnalyticsErrorHandling.test_rate_limiting_compliance": {"executed_lines": [502, 503, 504, 505, 509, 510], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestAnalyticsPerformance.app": {"executed_lines": [519, 520, 521, 522], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestAnalyticsPerformance.client": {"executed_lines": [527], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestAnalyticsPerformance.test_dashboard_response_time": {"executed_lines": [531, 533, 534, 535, 537, 540, 541], "summary": {"covered_lines": 7, "num_statements": 7, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestAnalyticsPerformance.test_large_data_export_handling": {"executed_lines": [545, 552, 556], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestAnalyticsPerformance.test_concurrent_analytics_requests": {"executed_lines": [560, 562, 564, 570, 571, 572, 573, 574, 577, 578, 581, 583, 584], "summary": {"covered_lines": 13, "num_statements": 13, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestAnalyticsPerformance.test_concurrent_analytics_requests.make_request": {"executed_lines": [565, 566, 567], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 6, 7, 8, 9, 10, 13, 14, 15, 17, 18, 20, 21, 22, 25, 26, 28, 29, 36, 37, 41, 46, 55, 56, 58, 59, 66, 67, 71, 83, 98, 110, 111, 131, 132, 134, 135, 142, 143, 147, 158, 169, 176, 189, 190, 192, 193, 200, 201, 205, 217, 224, 231, 239, 240, 242, 243, 250, 251, 255, 276, 297, 318, 336, 337, 339, 340, 347, 348, 352, 367, 382, 397, 405, 406, 408, 409, 416, 417, 421, 436, 450, 451, 463, 464, 466, 467, 474, 475, 479, 488, 499, 513, 514, 516, 517, 524, 525, 529, 543, 558], "summary": {"covered_lines": 91, "num_statements": 91, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"TestAnalyticsBlueprint": {"executed_lines": [31, 32, 33, 34, 43, 44, 48, 50, 52], "summary": {"covered_lines": 9, "num_statements": 10, "percent_covered": 90.0, "percent_covered_display": "90", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [39], "excluded_lines": []}, "TestAnalyticsDashboard": {"executed_lines": [61, 62, 63, 64, 69, 73, 76, 78, 85, 86, 88, 93, 96, 100, 105, 108], "summary": {"covered_lines": 16, "num_statements": 26, "percent_covered": 61.53846153846154, "percent_covered_display": "62", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [79, 81, 114, 120, 122, 124, 125, 126, 127, 128], "excluded_lines": []}, "TestUsageAnalytics": {"executed_lines": [137, 138, 139, 140, 145, 149, 152, 154, 160, 163, 165, 171, 174, 178, 181, 183], "summary": {"covered_lines": 16, "num_statements": 22, "percent_covered": 72.72727272727273, "percent_covered_display": "73", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [155, 156, 166, 167, 184, 186], "excluded_lines": []}, "TestPerformanceAnalytics": {"executed_lines": [195, 196, 197, 198, 203, 207, 210, 212, 219, 222, 226, 229, 233, 236], "summary": {"covered_lines": 14, "num_statements": 16, "percent_covered": 87.5, "percent_covered_display": "88", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [213, 215], "excluded_lines": []}, "TestAnalyticsDataCollection": {"executed_lines": [245, 246, 247, 248, 253, 257, 269, 274, 278, 290, 295, 299, 311, 316, 320, 327, 328, 333], "summary": {"covered_lines": 18, "num_statements": 18, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestAnalyticsReporting": {"executed_lines": [342, 343, 344, 345, 350, 354, 361, 365, 369, 376, 380, 384, 391, 395, 399, 402], "summary": {"covered_lines": 16, "num_statements": 16, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestAnalyticsAuthentication": {"executed_lines": [411, 412, 413, 414, 419, 423, 430, 431, 434, 438, 444, 445, 448], "summary": {"covered_lines": 13, "num_statements": 17, "percent_covered": 76.47058823529412, "percent_covered_display": "76", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [454, 456, 457, 460], "excluded_lines": []}, "TestAnalyticsErrorHandling": {"executed_lines": [469, 470, 471, 472, 477, 483, 486, 490, 492, 497, 502, 503, 504, 505, 509, 510], "summary": {"covered_lines": 16, "num_statements": 16, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestAnalyticsPerformance": {"executed_lines": [519, 520, 521, 522, 527, 531, 533, 534, 535, 537, 540, 541, 545, 552, 556, 560, 562, 564, 565, 566, 567, 570, 571, 572, 573, 574, 577, 578, 581, 583, 584], "summary": {"covered_lines": 31, "num_statements": 31, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 6, 7, 8, 9, 10, 13, 14, 15, 17, 18, 20, 21, 22, 25, 26, 28, 29, 36, 37, 41, 46, 55, 56, 58, 59, 66, 67, 71, 83, 98, 110, 111, 131, 132, 134, 135, 142, 143, 147, 158, 169, 176, 189, 190, 192, 193, 200, 201, 205, 217, 224, 231, 239, 240, 242, 243, 250, 251, 255, 276, 297, 318, 336, 337, 339, 340, 347, 348, 352, 367, 382, 397, 405, 406, 408, 409, 416, 417, 421, 436, 450, 451, 463, 464, 466, 467, 474, 475, 479, 488, 499, 513, 514, 516, 517, 524, 525, 529, 543, 558], "summary": {"covered_lines": 91, "num_statements": 91, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "tests\\test_app.py": {"executed_lines": [1, 6, 7, 8, 9, 10, 13, 16, 17, 19, 21, 27, 29, 33, 35, 39, 41, 46, 47, 49, 50, 51, 53, 59, 61, 66, 68, 79, 80, 82, 84, 89, 94, 96, 102, 104, 111, 112, 114, 116, 125, 127, 135, 137, 148, 149, 151, 152, 154, 157, 158, 162, 171, 181, 188, 194, 201, 202, 204, 205, 207, 210, 211, 215, 223, 235, 246, 247, 249, 250, 252, 255, 256, 260, 274, 284, 295, 296, 298, 299, 301, 304, 305, 309, 322, 350, 351, 353, 354, 356, 359, 360, 372, 379], "summary": {"covered_lines": 85, "num_statements": 194, "percent_covered": 43.81443298969072, "percent_covered_display": "44", "missing_lines": 109, "excluded_lines": 0}, "missing_lines": [23, 24, 25, 31, 37, 43, 44, 56, 57, 64, 71, 74, 75, 76, 92, 99, 100, 108, 118, 119, 122, 123, 129, 130, 133, 139, 141, 145, 155, 160, 164, 166, 167, 168, 169, 173, 175, 176, 177, 178, 179, 183, 186, 190, 192, 196, 198, 208, 213, 217, 219, 221, 225, 226, 227, 229, 230, 233, 238, 243, 253, 258, 262, 265, 271, 272, 276, 279, 281, 282, 287, 292, 302, 307, 311, 313, 314, 315, 317, 319, 320, 324, 325, 327, 329, 330, 331, 332, 335, 336, 337, 338, 339, 342, 343, 346, 347, 357, 362, 363, 364, 367, 370, 376, 377, 383, 386, 387, 388], "excluded_lines": [], "functions": {"TestAppFactory.test_create_app_default_config": {"executed_lines": [21], "summary": {"covered_lines": 1, "num_statements": 4, "percent_covered": 25.0, "percent_covered_display": "25", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [23, 24, 25], "excluded_lines": []}, "TestAppFactory.test_create_app_testing_config": {"executed_lines": [29], "summary": {"covered_lines": 1, "num_statements": 2, "percent_covered": 50.0, "percent_covered_display": "50", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [31], "excluded_lines": []}, "TestAppFactory.test_create_app_development_config": {"executed_lines": [35], "summary": {"covered_lines": 1, "num_statements": 2, "percent_covered": 50.0, "percent_covered_display": "50", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [37], "excluded_lines": []}, "TestAppFactory.test_create_app_production_config": {"executed_lines": [41], "summary": {"covered_lines": 1, "num_statements": 3, "percent_covered": 33.333333333333336, "percent_covered_display": "33", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [43, 44], "excluded_lines": []}, "TestAppFactory.test_create_app_with_credential_manager": {"executed_lines": [49, 50, 51, 53], "summary": {"covered_lines": 4, "num_statements": 6, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [56, 57], "excluded_lines": []}, "TestAppFactory.test_create_app_cors_configuration": {"executed_lines": [61], "summary": {"covered_lines": 1, "num_statements": 2, "percent_covered": 50.0, "percent_covered_display": "50", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [64], "excluded_lines": []}, "TestAppFactory.test_create_app_blueprints_registered": {"executed_lines": [68], "summary": {"covered_lines": 1, "num_statements": 5, "percent_covered": 20.0, "percent_covered_display": "20", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [71, 74, 75, 76], "excluded_lines": []}, "TestAppConfiguration.test_config_from_environment": {"executed_lines": [84, 89], "summary": {"covered_lines": 2, "num_statements": 3, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [92], "excluded_lines": []}, "TestAppConfiguration.test_config_security_settings": {"executed_lines": [96], "summary": {"covered_lines": 1, "num_statements": 3, "percent_covered": 33.333333333333336, "percent_covered_display": "33", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [99, 100], "excluded_lines": []}, "TestAppConfiguration.test_config_database_settings": {"executed_lines": [104], "summary": {"covered_lines": 1, "num_statements": 2, "percent_covered": 50.0, "percent_covered_display": "50", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [108], "excluded_lines": []}, "TestAppMiddleware.test_security_middleware_integration": {"executed_lines": [116], "summary": {"covered_lines": 1, "num_statements": 5, "percent_covered": 20.0, "percent_covered_display": "20", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [118, 119, 122, 123], "excluded_lines": []}, "TestAppMiddleware.test_rate_limiting_middleware_integration": {"executed_lines": [127], "summary": {"covered_lines": 1, "num_statements": 4, "percent_covered": 25.0, "percent_covered_display": "25", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [129, 130, 133], "excluded_lines": []}, "TestAppMiddleware.test_input_validation_middleware_integration": {"executed_lines": [137], "summary": {"covered_lines": 1, "num_statements": 4, "percent_covered": 25.0, "percent_covered_display": "25", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [139, 141, 145], "excluded_lines": []}, "TestAppRoutes.app": {"executed_lines": [154], "summary": {"covered_lines": 1, "num_statements": 2, "percent_covered": 50.0, "percent_covered_display": "50", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [155], "excluded_lines": []}, "TestAppRoutes.client": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [160], "excluded_lines": []}, "TestAppRoutes.test_health_check_endpoint": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [164, 166, 167, 168, 169], "excluded_lines": []}, "TestAppRoutes.test_api_info_endpoint": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [173, 175, 176, 177, 178, 179], "excluded_lines": []}, "TestAppRoutes.test_cors_headers": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [183, 186], "excluded_lines": []}, "TestAppRoutes.test_404_error_handling": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [190, 192], "excluded_lines": []}, "TestAppRoutes.test_method_not_allowed_handling": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [196, 198], "excluded_lines": []}, "TestAppErrorHandling.app": {"executed_lines": [207], "summary": {"covered_lines": 1, "num_statements": 2, "percent_covered": 50.0, "percent_covered_display": "50", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [208], "excluded_lines": []}, "TestAppErrorHandling.client": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [213], "excluded_lines": []}, "TestAppErrorHandling.test_json_error_responses": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [217, 219, 221], "excluded_lines": []}, "TestAppErrorHandling.test_internal_server_error_handling": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [225, 226, 229, 230, 233], "excluded_lines": []}, "TestAppErrorHandling.test_internal_server_error_handling.test_error": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [227], "excluded_lines": []}, "TestAppErrorHandling.test_validation_error_handling": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [238, 243], "excluded_lines": []}, "TestAppSecurity.app": {"executed_lines": [252], "summary": {"covered_lines": 1, "num_statements": 2, "percent_covered": 50.0, "percent_covered_display": "50", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [253], "excluded_lines": []}, "TestAppSecurity.client": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [258], "excluded_lines": []}, "TestAppSecurity.test_security_headers_present": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [262, 265, 271, 272], "excluded_lines": []}, "TestAppSecurity.test_sensitive_headers_removed": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [276, 279, 281, 282], "excluded_lines": []}, "TestAppSecurity.test_content_type_validation": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [287, 292], "excluded_lines": []}, "TestAppPerformance.app": {"executed_lines": [301], "summary": {"covered_lines": 1, "num_statements": 2, "percent_covered": 50.0, "percent_covered_display": "50", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [302], "excluded_lines": []}, "TestAppPerformance.client": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [307], "excluded_lines": []}, "TestAppPerformance.test_response_time_health_check": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [311, 313, 314, 315, 317, 319, 320], "excluded_lines": []}, "TestAppPerformance.test_concurrent_requests_handling": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [324, 325, 327, 329, 335, 336, 337, 338, 339, 342, 343, 346, 347], "excluded_lines": []}, "TestAppPerformance.test_concurrent_requests_handling.make_request": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [330, 331, 332], "excluded_lines": []}, "TestAppIntegration.app": {"executed_lines": [356], "summary": {"covered_lines": 1, "num_statements": 2, "percent_covered": 50.0, "percent_covered_display": "50", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [357], "excluded_lines": []}, "TestAppIntegration.test_credential_manager_integration": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [362, 363, 364, 367, 370], "excluded_lines": []}, "TestAppIntegration.test_sentry_integration_configuration": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [376, 377], "excluded_lines": []}, "TestAppIntegration.test_database_configuration_loading": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [383, 386, 387, 388], "excluded_lines": []}, "": {"executed_lines": [1, 6, 7, 8, 9, 10, 13, 16, 17, 19, 27, 33, 39, 46, 47, 59, 66, 79, 80, 82, 94, 102, 111, 112, 114, 125, 135, 148, 149, 151, 152, 157, 158, 162, 171, 181, 188, 194, 201, 202, 204, 205, 210, 211, 215, 223, 235, 246, 247, 249, 250, 255, 256, 260, 274, 284, 295, 296, 298, 299, 304, 305, 309, 322, 350, 351, 353, 354, 359, 360, 372, 379], "summary": {"covered_lines": 63, "num_statements": 63, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"TestAppFactory": {"executed_lines": [21, 29, 35, 41, 49, 50, 51, 53, 61, 68], "summary": {"covered_lines": 10, "num_statements": 24, "percent_covered": 41.666666666666664, "percent_covered_display": "42", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [23, 24, 25, 31, 37, 43, 44, 56, 57, 64, 71, 74, 75, 76], "excluded_lines": []}, "TestAppConfiguration": {"executed_lines": [84, 89, 96, 104], "summary": {"covered_lines": 4, "num_statements": 8, "percent_covered": 50.0, "percent_covered_display": "50", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [92, 99, 100, 108], "excluded_lines": []}, "TestAppMiddleware": {"executed_lines": [116, 127, 137], "summary": {"covered_lines": 3, "num_statements": 13, "percent_covered": 23.076923076923077, "percent_covered_display": "23", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [118, 119, 122, 123, 129, 130, 133, 139, 141, 145], "excluded_lines": []}, "TestAppRoutes": {"executed_lines": [154], "summary": {"covered_lines": 1, "num_statements": 20, "percent_covered": 5.0, "percent_covered_display": "5", "missing_lines": 19, "excluded_lines": 0}, "missing_lines": [155, 160, 164, 166, 167, 168, 169, 173, 175, 176, 177, 178, 179, 183, 186, 190, 192, 196, 198], "excluded_lines": []}, "TestAppErrorHandling": {"executed_lines": [207], "summary": {"covered_lines": 1, "num_statements": 14, "percent_covered": 7.142857142857143, "percent_covered_display": "7", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [208, 213, 217, 219, 221, 225, 226, 227, 229, 230, 233, 238, 243], "excluded_lines": []}, "TestAppSecurity": {"executed_lines": [252], "summary": {"covered_lines": 1, "num_statements": 13, "percent_covered": 7.6923076923076925, "percent_covered_display": "8", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [253, 258, 262, 265, 271, 272, 276, 279, 281, 282, 287, 292], "excluded_lines": []}, "TestAppPerformance": {"executed_lines": [301], "summary": {"covered_lines": 1, "num_statements": 26, "percent_covered": 3.8461538461538463, "percent_covered_display": "4", "missing_lines": 25, "excluded_lines": 0}, "missing_lines": [302, 307, 311, 313, 314, 315, 317, 319, 320, 324, 325, 327, 329, 330, 331, 332, 335, 336, 337, 338, 339, 342, 343, 346, 347], "excluded_lines": []}, "TestAppIntegration": {"executed_lines": [356], "summary": {"covered_lines": 1, "num_statements": 13, "percent_covered": 7.6923076923076925, "percent_covered_display": "8", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [357, 362, 363, 364, 367, 370, 376, 377, 383, 386, 387, 388], "excluded_lines": []}, "": {"executed_lines": [1, 6, 7, 8, 9, 10, 13, 16, 17, 19, 27, 33, 39, 46, 47, 59, 66, 79, 80, 82, 94, 102, 111, 112, 114, 125, 135, 148, 149, 151, 152, 157, 158, 162, 171, 181, 188, 194, 201, 202, 204, 205, 210, 211, 215, 223, 235, 246, 247, 249, 250, 255, 256, 260, 274, 284, 295, 296, 298, 299, 304, 305, 309, 322, 350, 351, 353, 354, 359, 360, 372, 379], "summary": {"covered_lines": 63, "num_statements": 63, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "tests\\test_calculations.py": {"executed_lines": [1, 6, 7, 8, 9, 11, 14, 15, 17, 18, 20, 21, 22, 23, 25, 26, 30, 32, 33, 35, 38, 40, 42, 45, 46, 48, 49, 51, 52, 53, 54, 56, 57, 59, 61, 63, 70, 75, 81, 83, 88, 93, 95, 97, 104, 109, 111, 113, 120, 125, 127, 129, 136, 141, 143, 145, 160, 161, 166, 169, 170, 172, 173, 175, 176, 177, 178, 180, 181, 183, 185, 187, 195, 200, 202, 204, 210, 211, 218, 223, 226, 227, 229, 230, 232, 233, 234, 235, 237, 238, 240, 242, 244, 252, 257, 259, 261, 263, 264, 272, 277, 280, 281, 283, 284, 286, 287, 288, 289, 291, 292, 294, 296, 298, 306, 311, 313, 315, 317, 318, 326, 331, 334, 335, 337, 338, 340, 341, 342, 343, 345, 346, 348, 350, 352, 359, 360, 365, 367, 369, 376, 381, 383, 385, 390, 392, 394, 398, 401, 402, 404, 405, 407, 408, 409, 410, 412, 413, 415, 417, 419, 426, 431, 433, 435, 442, 447, 449, 450, 472, 473, 475, 476, 478, 479, 480, 481, 483, 484, 486, 488, 490, 492, 499, 500, 503, 505, 508, 509, 511, 513, 514, 516, 518, 519, 520, 526, 529, 532, 533, 534, 535, 536, 539, 540, 543, 545, 546], "summary": {"covered_lines": 199, "num_statements": 207, "percent_covered": 96.13526570048309, "percent_covered_display": "96", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [28, 77, 78, 79, 453, 455, 462, 469], "excluded_lines": [], "functions": {"TestCalculationsBlueprint.app": {"executed_lines": [20, 21, 22, 23], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCalculationsBlueprint.client": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [28], "excluded_lines": []}, "TestCalculationsBlueprint.test_blueprint_registration": {"executed_lines": [32, 33], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCalculationsBlueprint.test_blueprint_url_prefix": {"executed_lines": [38, 40, 42], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestAirDuctCalculations.app": {"executed_lines": [51, 52, 53, 54], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestAirDuctCalculations.client": {"executed_lines": [59], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestAirDuctCalculations.test_air_duct_calculation_valid_input": {"executed_lines": [63, 70, 75], "summary": {"covered_lines": 3, "num_statements": 6, "percent_covered": 50.0, "percent_covered_display": "50", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [77, 78, 79], "excluded_lines": []}, "TestAirDuctCalculations.test_air_duct_calculation_missing_required_fields": {"executed_lines": [83, 88, 93], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestAirDuctCalculations.test_air_duct_calculation_invalid_values": {"executed_lines": [97, 104, 109], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestAirDuctCalculations.test_air_duct_calculation_round_duct": {"executed_lines": [113, 120, 125], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestAirDuctCalculations.test_air_duct_calculation_metric_units": {"executed_lines": [129, 136, 141], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestAirDuctCalculations.test_air_duct_calculation_edge_cases": {"executed_lines": [145, 160, 161, 166], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestGreaseDuctCalculations.app": {"executed_lines": [175, 176, 177, 178], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestGreaseDuctCalculations.client": {"executed_lines": [183], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestGreaseDuctCalculations.test_grease_duct_calculation_valid_input": {"executed_lines": [187, 195, 200], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestGreaseDuctCalculations.test_grease_duct_calculation_different_appliances": {"executed_lines": [204, 210, 211, 218, 223], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestEngineExhaustCalculations.app": {"executed_lines": [232, 233, 234, 235], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestEngineExhaustCalculations.client": {"executed_lines": [240], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestEngineExhaustCalculations.test_engine_exhaust_calculation_valid_input": {"executed_lines": [244, 252, 257], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestEngineExhaustCalculations.test_engine_exhaust_different_engine_types": {"executed_lines": [261, 263, 264, 272, 277], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestBoilerVentCalculations.app": {"executed_lines": [286, 287, 288, 289], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestBoilerVentCalculations.client": {"executed_lines": [294], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestBoilerVentCalculations.test_boiler_vent_calculation_valid_input": {"executed_lines": [298, 306, 311], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestBoilerVentCalculations.test_boiler_vent_different_fuel_types": {"executed_lines": [315, 317, 318, 326, 331], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCalculationValidation.app": {"executed_lines": [340, 341, 342, 343], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCalculationValidation.client": {"executed_lines": [348], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCalculationValidation.test_malicious_input_handling": {"executed_lines": [352, 359, 360, 365], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCalculationValidation.test_oversized_input_handling": {"executed_lines": [369, 376, 381], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCalculationValidation.test_invalid_json_handling": {"executed_lines": [385, 390], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCalculationValidation.test_missing_content_type": {"executed_lines": [394, 398], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCalculationErrorHandling.app": {"executed_lines": [407, 408, 409, 410], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCalculationErrorHandling.client": {"executed_lines": [415], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCalculationErrorHandling.test_division_by_zero_handling": {"executed_lines": [419, 426, 431], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCalculationErrorHandling.test_calculation_overflow_handling": {"executed_lines": [435, 442, 447], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCalculationErrorHandling.test_internal_calculation_error_handling": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [453, 455, 462, 469], "excluded_lines": []}, "TestCalculationPerformance.app": {"executed_lines": [478, 479, 480, 481], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCalculationPerformance.client": {"executed_lines": [486], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCalculationPerformance.test_calculation_response_time": {"executed_lines": [490, 492, 499, 500, 503, 505, 508, 509], "summary": {"covered_lines": 8, "num_statements": 8, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCalculationPerformance.test_concurrent_calculations": {"executed_lines": [513, 514, 516, 518, 532, 533, 534, 535, 536, 539, 540, 543, 545, 546], "summary": {"covered_lines": 14, "num_statements": 14, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCalculationPerformance.test_concurrent_calculations.make_calculation": {"executed_lines": [519, 520, 526, 529], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 6, 7, 8, 9, 11, 14, 15, 17, 18, 25, 26, 30, 35, 45, 46, 48, 49, 56, 57, 61, 81, 95, 111, 127, 143, 169, 170, 172, 173, 180, 181, 185, 202, 226, 227, 229, 230, 237, 238, 242, 259, 280, 281, 283, 284, 291, 292, 296, 313, 334, 335, 337, 338, 345, 346, 350, 367, 383, 392, 401, 402, 404, 405, 412, 413, 417, 433, 449, 450, 472, 473, 475, 476, 483, 484, 488, 511], "summary": {"covered_lines": 69, "num_statements": 69, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"TestCalculationsBlueprint": {"executed_lines": [20, 21, 22, 23, 32, 33, 38, 40, 42], "summary": {"covered_lines": 9, "num_statements": 10, "percent_covered": 90.0, "percent_covered_display": "90", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [28], "excluded_lines": []}, "TestAirDuctCalculations": {"executed_lines": [51, 52, 53, 54, 59, 63, 70, 75, 83, 88, 93, 97, 104, 109, 113, 120, 125, 129, 136, 141, 145, 160, 161, 166], "summary": {"covered_lines": 24, "num_statements": 27, "percent_covered": 88.88888888888889, "percent_covered_display": "89", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [77, 78, 79], "excluded_lines": []}, "TestGreaseDuctCalculations": {"executed_lines": [175, 176, 177, 178, 183, 187, 195, 200, 204, 210, 211, 218, 223], "summary": {"covered_lines": 13, "num_statements": 13, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestEngineExhaustCalculations": {"executed_lines": [232, 233, 234, 235, 240, 244, 252, 257, 261, 263, 264, 272, 277], "summary": {"covered_lines": 13, "num_statements": 13, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestBoilerVentCalculations": {"executed_lines": [286, 287, 288, 289, 294, 298, 306, 311, 315, 317, 318, 326, 331], "summary": {"covered_lines": 13, "num_statements": 13, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCalculationValidation": {"executed_lines": [340, 341, 342, 343, 348, 352, 359, 360, 365, 369, 376, 381, 385, 390, 394, 398], "summary": {"covered_lines": 16, "num_statements": 16, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCalculationErrorHandling": {"executed_lines": [407, 408, 409, 410, 415, 419, 426, 431, 435, 442, 447], "summary": {"covered_lines": 11, "num_statements": 15, "percent_covered": 73.33333333333333, "percent_covered_display": "73", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [453, 455, 462, 469], "excluded_lines": []}, "TestCalculationPerformance": {"executed_lines": [478, 479, 480, 481, 486, 490, 492, 499, 500, 503, 505, 508, 509, 513, 514, 516, 518, 519, 520, 526, 529, 532, 533, 534, 535, 536, 539, 540, 543, 545, 546], "summary": {"covered_lines": 31, "num_statements": 31, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 6, 7, 8, 9, 11, 14, 15, 17, 18, 25, 26, 30, 35, 45, 46, 48, 49, 56, 57, 61, 81, 95, 111, 127, 143, 169, 170, 172, 173, 180, 181, 185, 202, 226, 227, 229, 230, 237, 238, 242, 259, 280, 281, 283, 284, 291, 292, 296, 313, 334, 335, 337, 338, 345, 346, 350, 367, 383, 392, 401, 402, 404, 405, 412, 413, 417, 433, 449, 450, 472, 473, 475, 476, 483, 484, 488, 511], "summary": {"covered_lines": 69, "num_statements": 69, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "tests\\test_compliance_routes.py": {"executed_lines": [1, 6, 7, 8, 9, 10, 13, 14, 21, 22, 24, 25, 27, 28, 29, 30, 32, 33, 37, 39, 40, 42, 44, 46, 48, 51, 52, 54, 55, 57, 58, 59, 60, 62, 63, 65, 67, 69, 77, 82, 84, 89, 91, 100, 105, 107, 109, 117, 122, 124, 126, 136, 141, 144, 145, 147, 148, 150, 151, 152, 153, 155, 156, 158, 160, 162, 168, 172, 174, 179, 181, 191, 196, 198, 200, 208, 213, 216, 217, 219, 220, 222, 223, 224, 225, 227, 228, 230, 232, 234, 244, 249, 251, 253, 259, 263, 265, 270, 272, 278, 282, 285, 286, 288, 289, 291, 292, 293, 294, 296, 297, 299, 301, 303, 306, 308, 313, 315, 318, 320, 325, 327, 333, 337, 339, 341, 344, 347, 348, 350, 351, 353, 354, 355, 356, 358, 359, 361, 363, 365, 373, 374, 379, 381, 383, 390, 391, 396, 398, 400, 405, 410, 412, 414, 420, 425, 428, 429, 431, 432, 434, 435, 436, 437, 439, 440, 442, 444, 446, 452, 457, 459, 461, 467, 468, 471, 473, 474, 495, 496, 498, 499, 501, 502, 503, 504, 506, 507, 509, 511, 513, 515, 522, 523, 526, 528, 531, 532, 534, 536, 538, 540, 541, 542, 547, 550, 553, 554, 555, 556, 557, 560, 561, 564, 566, 567, 569, 571, 581, 586], "summary": {"covered_lines": 211, "num_statements": 235, "percent_covered": 89.7872340425532, "percent_covered_display": "90", "missing_lines": 24, "excluded_lines": 0}, "missing_lines": [15, 17, 18, 35, 85, 86, 87, 175, 176, 177, 266, 267, 268, 309, 310, 311, 321, 322, 323, 477, 479, 480, 486, 492], "excluded_lines": [], "functions": {"TestComplianceBlueprint.app": {"executed_lines": [27, 28, 29, 30], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestComplianceBlueprint.client": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [35], "excluded_lines": []}, "TestComplianceBlueprint.test_blueprint_registration": {"executed_lines": [39, 40], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestComplianceBlueprint.test_blueprint_url_prefix": {"executed_lines": [44, 46, 48], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestHVACCodeCompliance.app": {"executed_lines": [57, 58, 59, 60], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestHVACCodeCompliance.client": {"executed_lines": [65], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestHVACCodeCompliance.test_check_imc_compliance": {"executed_lines": [69, 77, 82, 84], "summary": {"covered_lines": 4, "num_statements": 7, "percent_covered": 57.142857142857146, "percent_covered_display": "57", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [85, 86, 87], "excluded_lines": []}, "TestHVACCodeCompliance.test_check_nfpa_compliance": {"executed_lines": [91, 100, 105], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestHVACCodeCompliance.test_check_ashrae_compliance": {"executed_lines": [109, 117, 122], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestHVACCodeCompliance.test_check_local_code_compliance": {"executed_lines": [126, 136, 141], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestComplianceValidation.app": {"executed_lines": [150, 151, 152, 153], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestComplianceValidation.client": {"executed_lines": [158], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestComplianceValidation.test_get_compliance_requirements": {"executed_lines": [162, 168, 172, 174], "summary": {"covered_lines": 4, "num_statements": 7, "percent_covered": 57.142857142857146, "percent_covered_display": "57", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [175, 176, 177], "excluded_lines": []}, "TestComplianceValidation.test_validate_system_design": {"executed_lines": [181, 191, 196], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestComplianceValidation.test_check_installation_compliance": {"executed_lines": [200, 208, 213], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestComplianceReporting.app": {"executed_lines": [222, 223, 224, 225], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestComplianceReporting.client": {"executed_lines": [230], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestComplianceReporting.test_generate_compliance_report": {"executed_lines": [234, 244, 249], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestComplianceReporting.test_get_compliance_checklist": {"executed_lines": [253, 259, 263, 265], "summary": {"covered_lines": 4, "num_statements": 7, "percent_covered": 57.142857142857146, "percent_covered_display": "57", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [266, 267, 268], "excluded_lines": []}, "TestComplianceReporting.test_export_compliance_data": {"executed_lines": [272, 278, 282], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestComplianceCodeDatabase.app": {"executed_lines": [291, 292, 293, 294], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestComplianceCodeDatabase.client": {"executed_lines": [299], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestComplianceCodeDatabase.test_get_available_codes": {"executed_lines": [303, 306, 308], "summary": {"covered_lines": 3, "num_statements": 6, "percent_covered": 50.0, "percent_covered_display": "50", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [309, 310, 311], "excluded_lines": []}, "TestComplianceCodeDatabase.test_get_code_details": {"executed_lines": [315, 318, 320], "summary": {"covered_lines": 3, "num_statements": 6, "percent_covered": 50.0, "percent_covered_display": "50", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [321, 322, 323], "excluded_lines": []}, "TestComplianceCodeDatabase.test_search_code_sections": {"executed_lines": [327, 333, 337], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestComplianceCodeDatabase.test_get_jurisdiction_requirements": {"executed_lines": [341, 344], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestComplianceErrorHandling.app": {"executed_lines": [353, 354, 355, 356], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestComplianceErrorHandling.client": {"executed_lines": [361], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestComplianceErrorHandling.test_invalid_compliance_data": {"executed_lines": [365, 373, 374, 379], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestComplianceErrorHandling.test_malicious_input_handling": {"executed_lines": [383, 390, 391, 396], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestComplianceErrorHandling.test_missing_required_fields": {"executed_lines": [400, 405, 410], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestComplianceErrorHandling.test_unsupported_code_type": {"executed_lines": [414, 420, 425], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestComplianceAuthentication.app": {"executed_lines": [434, 435, 436, 437], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestComplianceAuthentication.client": {"executed_lines": [442], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestComplianceAuthentication.test_unauthorized_compliance_check": {"executed_lines": [446, 452, 457], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestComplianceAuthentication.test_premium_feature_access": {"executed_lines": [461, 467, 468, 471], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestComplianceAuthentication.test_valid_token_access": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [477, 479, 480, 486, 492], "excluded_lines": []}, "TestCompliancePerformance.app": {"executed_lines": [501, 502, 503, 504], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCompliancePerformance.client": {"executed_lines": [509], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCompliancePerformance.test_compliance_check_response_time": {"executed_lines": [513, 515, 522, 523, 526, 528, 531, 532], "summary": {"covered_lines": 8, "num_statements": 8, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCompliancePerformance.test_concurrent_compliance_checks": {"executed_lines": [536, 538, 540, 553, 554, 555, 556, 557, 560, 561, 564, 566, 567], "summary": {"covered_lines": 13, "num_statements": 13, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCompliancePerformance.test_concurrent_compliance_checks.make_compliance_check": {"executed_lines": [541, 542, 547, 550], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCompliancePerformance.test_large_system_validation": {"executed_lines": [571, 581, 586], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 6, 7, 8, 9, 10, 13, 14, 21, 22, 24, 25, 32, 33, 37, 42, 51, 52, 54, 55, 62, 63, 67, 89, 107, 124, 144, 145, 147, 148, 155, 156, 160, 179, 198, 216, 217, 219, 220, 227, 228, 232, 251, 270, 285, 286, 288, 289, 296, 297, 301, 313, 325, 339, 347, 348, 350, 351, 358, 359, 363, 381, 398, 412, 428, 429, 431, 432, 439, 440, 444, 459, 473, 474, 495, 496, 498, 499, 506, 507, 511, 534, 569], "summary": {"covered_lines": 74, "num_statements": 77, "percent_covered": 96.1038961038961, "percent_covered_display": "96", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [15, 17, 18], "excluded_lines": []}}, "classes": {"TestComplianceBlueprint": {"executed_lines": [27, 28, 29, 30, 39, 40, 44, 46, 48], "summary": {"covered_lines": 9, "num_statements": 10, "percent_covered": 90.0, "percent_covered_display": "90", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [35], "excluded_lines": []}, "TestHVACCodeCompliance": {"executed_lines": [57, 58, 59, 60, 65, 69, 77, 82, 84, 91, 100, 105, 109, 117, 122, 126, 136, 141], "summary": {"covered_lines": 18, "num_statements": 21, "percent_covered": 85.71428571428571, "percent_covered_display": "86", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [85, 86, 87], "excluded_lines": []}, "TestComplianceValidation": {"executed_lines": [150, 151, 152, 153, 158, 162, 168, 172, 174, 181, 191, 196, 200, 208, 213], "summary": {"covered_lines": 15, "num_statements": 18, "percent_covered": 83.33333333333333, "percent_covered_display": "83", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [175, 176, 177], "excluded_lines": []}, "TestComplianceReporting": {"executed_lines": [222, 223, 224, 225, 230, 234, 244, 249, 253, 259, 263, 265, 272, 278, 282], "summary": {"covered_lines": 15, "num_statements": 18, "percent_covered": 83.33333333333333, "percent_covered_display": "83", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [266, 267, 268], "excluded_lines": []}, "TestComplianceCodeDatabase": {"executed_lines": [291, 292, 293, 294, 299, 303, 306, 308, 315, 318, 320, 327, 333, 337, 341, 344], "summary": {"covered_lines": 16, "num_statements": 22, "percent_covered": 72.72727272727273, "percent_covered_display": "73", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [309, 310, 311, 321, 322, 323], "excluded_lines": []}, "TestComplianceErrorHandling": {"executed_lines": [353, 354, 355, 356, 361, 365, 373, 374, 379, 383, 390, 391, 396, 400, 405, 410, 414, 420, 425], "summary": {"covered_lines": 19, "num_statements": 19, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestComplianceAuthentication": {"executed_lines": [434, 435, 436, 437, 442, 446, 452, 457, 461, 467, 468, 471], "summary": {"covered_lines": 12, "num_statements": 17, "percent_covered": 70.58823529411765, "percent_covered_display": "71", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [477, 479, 480, 486, 492], "excluded_lines": []}, "TestCompliancePerformance": {"executed_lines": [501, 502, 503, 504, 509, 513, 515, 522, 523, 526, 528, 531, 532, 536, 538, 540, 541, 542, 547, 550, 553, 554, 555, 556, 557, 560, 561, 564, 566, 567, 571, 581, 586], "summary": {"covered_lines": 33, "num_statements": 33, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 6, 7, 8, 9, 10, 13, 14, 21, 22, 24, 25, 32, 33, 37, 42, 51, 52, 54, 55, 62, 63, 67, 89, 107, 124, 144, 145, 147, 148, 155, 156, 160, 179, 198, 216, 217, 219, 220, 227, 228, 232, 251, 270, 285, 286, 288, 289, 296, 297, 301, 313, 325, 339, 347, 348, 350, 351, 358, 359, 363, 381, 398, 412, 428, 429, 431, 432, 439, 440, 444, 459, 473, 474, 495, 496, 498, 499, 506, 507, 511, 534, 569], "summary": {"covered_lines": 74, "num_statements": 77, "percent_covered": 96.1038961038961, "percent_covered_display": "96", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [15, 17, 18], "excluded_lines": []}}}, "tests\\test_credential_manager.py": {"executed_lines": [1, 6, 7, 8, 9, 10, 13, 14, 16, 19, 20, 25, 26, 27, 28, 30, 32, 35, 36, 38, 40, 41, 42, 43, 44, 46, 48, 49, 50, 52, 54, 55, 57, 58, 59, 61, 63, 65, 66, 68, 70, 71, 73, 75, 78, 79, 80, 82, 84, 85, 87, 89, 90, 92, 94, 95, 96, 98, 100, 101, 103, 104, 107, 108, 111, 112, 114, 116, 117, 119, 122, 123, 124, 126, 128, 129, 130, 132, 133, 134, 135, 136, 138, 140, 142, 143, 144, 145, 146, 149, 150, 151, 153, 156, 158, 160, 161, 162, 164, 166, 169, 170, 171, 173, 175, 176, 178, 181, 184, 185, 187, 189, 191, 194, 195, 196, 198, 200, 202, 203, 204, 205, 207, 209, 211, 212, 213, 214, 217, 218, 220, 222, 223, 225, 227, 230, 231, 233, 234, 236, 238, 240, 242, 243, 244, 245, 248, 249, 250, 253, 254, 257, 258, 261, 262, 264, 267, 268, 269, 271, 272, 274, 275, 276, 279, 280, 281, 283, 285, 287, 288, 290, 293, 295, 297, 298, 300, 303, 304, 307, 308, 311, 312, 313, 316], "summary": {"covered_lines": 181, "num_statements": 182, "percent_covered": 99.45054945054945, "percent_covered_display": "99", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [317], "excluded_lines": [], "functions": {"TestCredentialManager.setup_method": {"executed_lines": [19, 20, 25, 26, 27, 28, 30], "summary": {"covered_lines": 7, "num_statements": 7, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCredentialManager.teardown_method": {"executed_lines": [35, 36], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCredentialManager.test_initialization_without_encryption": {"executed_lines": [40, 41, 42, 43, 44], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCredentialManager.test_initialization_with_encryption": {"executed_lines": [48, 49, 50], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCredentialManager.test_generate_secure_key": {"executed_lines": [54, 55, 57, 58, 59], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCredentialManager.test_get_credential_from_environment": {"executed_lines": [63, 65, 66], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCredentialManager.test_get_credential_with_default": {"executed_lines": [70, 71], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCredentialManager.test_get_credential_with_secure_default": {"executed_lines": [75, 78, 79, 80], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCredentialManager.test_get_credential_unknown_category": {"executed_lines": [84, 85], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCredentialManager.test_get_credential_unknown_credential": {"executed_lines": [89, 90], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCredentialManager.test_set_credential_without_encryption": {"executed_lines": [94, 95, 96], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCredentialManager.test_set_credential_with_encryption": {"executed_lines": [100, 101, 103, 104, 107, 108, 111, 112], "summary": {"covered_lines": 8, "num_statements": 8, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCredentialManager.test_set_credential_unknown_category": {"executed_lines": [116, 117], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCredentialManager.test_get_database_config": {"executed_lines": [122, 123, 124, 126, 128, 129, 130, 132, 133, 134, 135, 136], "summary": {"covered_lines": 12, "num_statements": 12, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCredentialManager.test_get_application_secrets": {"executed_lines": [140, 142, 143, 144, 145, 146, 149, 150, 151], "summary": {"covered_lines": 9, "num_statements": 9, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCredentialManager.test_get_external_api_keys": {"executed_lines": [156, 158, 160, 161, 162, 164], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCredentialManager.test_validate_credentials_all_present": {"executed_lines": [169, 170, 171, 173, 175, 176], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCredentialManager.test_validate_credentials_missing_required": {"executed_lines": [181, 184, 185], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCredentialManager.test_validate_credentials_weak_passwords": {"executed_lines": [189, 191, 194, 195, 196], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCredentialManager.test_generate_env_template_with_examples": {"executed_lines": [200, 202, 203, 204, 205], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCredentialManager.test_generate_env_template_without_examples": {"executed_lines": [209, 211, 212, 213, 214], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCredentialManagerGlobalFunctions.test_get_credential_manager_singleton": {"executed_lines": [222, 223, 225], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCredentialManagerGlobalFunctions.test_get_credential_manager_with_encryption": {"executed_lines": [230, 231, 233, 234, 236], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCredentialManagerGlobalFunctions.test_get_secure_config": {"executed_lines": [240, 242, 243, 244, 245, 248, 249, 250, 253, 254, 257, 258], "summary": {"covered_lines": 12, "num_statements": 12, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCredentialManagerIntegration.test_mongodb_config_integration": {"executed_lines": [267, 268, 269, 271, 272, 274, 275, 276, 279, 280, 281], "summary": {"covered_lines": 11, "num_statements": 11, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCredentialManagerIntegration.test_flask_app_integration": {"executed_lines": [285, 287, 288, 290, 293], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCredentialManagerIntegration.test_encryption_roundtrip": {"executed_lines": [297, 298, 300, 303, 304, 307, 308, 311, 312, 313], "summary": {"covered_lines": 10, "num_statements": 10, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 6, 7, 8, 9, 10, 13, 14, 16, 32, 38, 46, 52, 61, 68, 73, 82, 87, 92, 98, 114, 119, 138, 153, 166, 178, 187, 198, 207, 217, 218, 220, 227, 238, 261, 262, 264, 283, 295, 316], "summary": {"covered_lines": 36, "num_statements": 37, "percent_covered": 97.29729729729729, "percent_covered_display": "97", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [317], "excluded_lines": []}}, "classes": {"TestCredentialManager": {"executed_lines": [19, 20, 25, 26, 27, 28, 30, 35, 36, 40, 41, 42, 43, 44, 48, 49, 50, 54, 55, 57, 58, 59, 63, 65, 66, 70, 71, 75, 78, 79, 80, 84, 85, 89, 90, 94, 95, 96, 100, 101, 103, 104, 107, 108, 111, 112, 116, 117, 122, 123, 124, 126, 128, 129, 130, 132, 133, 134, 135, 136, 140, 142, 143, 144, 145, 146, 149, 150, 151, 156, 158, 160, 161, 162, 164, 169, 170, 171, 173, 175, 176, 181, 184, 185, 189, 191, 194, 195, 196, 200, 202, 203, 204, 205, 209, 211, 212, 213, 214], "summary": {"covered_lines": 99, "num_statements": 99, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCredentialManagerGlobalFunctions": {"executed_lines": [222, 223, 225, 230, 231, 233, 234, 236, 240, 242, 243, 244, 245, 248, 249, 250, 253, 254, 257, 258], "summary": {"covered_lines": 20, "num_statements": 20, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCredentialManagerIntegration": {"executed_lines": [267, 268, 269, 271, 272, 274, 275, 276, 279, 280, 281, 285, 287, 288, 290, 293, 297, 298, 300, 303, 304, 307, 308, 311, 312, 313], "summary": {"covered_lines": 26, "num_statements": 26, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 6, 7, 8, 9, 10, 13, 14, 16, 32, 38, 46, 52, 61, 68, 73, 82, 87, 92, 98, 114, 119, 138, 153, 166, 178, 187, 198, 207, 217, 218, 220, 227, 238, 261, 262, 264, 283, 295, 316], "summary": {"covered_lines": 36, "num_statements": 37, "percent_covered": 97.29729729729729, "percent_covered_display": "97", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [317], "excluded_lines": []}}}, "tests\\test_exports.py": {"executed_lines": [1, 6, 7, 8, 9, 10, 11, 14, 15, 22, 23, 25, 26, 28, 29, 30, 31, 33, 34, 38, 40, 41, 43, 45, 47, 49, 52, 53, 55, 56, 58, 59, 60, 61, 63, 64, 66, 68, 70, 76, 81, 83, 87, 89, 95, 100, 102, 105, 107, 114, 119, 121, 123, 131, 136, 138, 142, 143, 145, 146, 148, 149, 150, 151, 153, 154, 156, 158, 160, 168, 173, 175, 177, 184, 189, 191, 193, 200, 205, 208, 209, 211, 212, 214, 215, 216, 217, 219, 220, 222, 224, 226, 234, 239, 241, 244, 246, 255, 260, 262, 264, 272, 277, 280, 281, 283, 284, 286, 287, 288, 289, 291, 292, 294, 296, 298, 301, 303, 308, 310, 313, 315, 319, 321, 330, 335, 338, 339, 341, 342, 344, 345, 346, 347, 349, 350, 352, 354, 356, 361, 366, 368, 370, 376, 381, 383, 385, 391, 396, 398, 399, 419, 420, 422, 423, 425, 426, 427, 428, 430, 431, 433, 435, 437, 442, 447, 449, 451, 456, 461, 463, 465, 470, 475, 477, 478, 496, 497, 499, 500, 502, 503, 504, 505, 507, 508, 510, 512, 514, 516, 521, 522, 525, 527, 530, 531, 533, 535, 541, 546, 548, 550, 552, 554, 555, 556, 560, 563, 566, 567, 568, 569, 570, 573, 574, 577, 579, 580, 582, 584, 590, 595, 598, 599, 601, 602, 604, 605, 606, 607, 609, 610, 612, 614, 616, 622, 627, 629, 631, 634, 636, 638, 641, 643], "summary": {"covered_lines": 234, "num_statements": 259, "percent_covered": 90.34749034749035, "percent_covered_display": "90", "missing_lines": 25, "excluded_lines": 0}, "missing_lines": [16, 18, 19, 36, 85, 103, 139, 242, 304, 305, 306, 316, 317, 402, 404, 405, 410, 416, 481, 483, 488, 493, 644, 645, 646], "excluded_lines": [], "functions": {"TestExportsBlueprint.app": {"executed_lines": [28, 29, 30, 31], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestExportsBlueprint.client": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [36], "excluded_lines": []}, "TestExportsBlueprint.test_blueprint_registration": {"executed_lines": [40, 41], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestExportsBlueprint.test_blueprint_url_prefix": {"executed_lines": [45, 47, 49], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCalculationExports.app": {"executed_lines": [58, 59, 60, 61], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCalculationExports.client": {"executed_lines": [66], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCalculationExports.test_export_calculation_results_json": {"executed_lines": [70, 76, 81, 83], "summary": {"covered_lines": 4, "num_statements": 5, "percent_covered": 80.0, "percent_covered_display": "80", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [85], "excluded_lines": []}, "TestCalculationExports.test_export_calculation_results_csv": {"executed_lines": [89, 95, 100, 102], "summary": {"covered_lines": 4, "num_statements": 5, "percent_covered": 80.0, "percent_covered_display": "80", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [103], "excluded_lines": []}, "TestCalculationExports.test_export_calculation_results_excel": {"executed_lines": [107, 114, 119], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCalculationExports.test_export_calculation_results_pdf": {"executed_lines": [123, 131, 136, 138], "summary": {"covered_lines": 4, "num_statements": 5, "percent_covered": 80.0, "percent_covered_display": "80", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [139], "excluded_lines": []}, "TestProjectExports.app": {"executed_lines": [148, 149, 150, 151], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestProjectExports.client": {"executed_lines": [156], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestProjectExports.test_export_project_data": {"executed_lines": [160, 168, 173], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestProjectExports.test_export_project_summary_report": {"executed_lines": [177, 184, 189], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestProjectExports.test_export_multiple_projects": {"executed_lines": [193, 200, 205], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestDiagramExports.app": {"executed_lines": [214, 215, 216, 217], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestDiagramExports.client": {"executed_lines": [222], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestDiagramExports.test_export_duct_diagram_svg": {"executed_lines": [226, 234, 239, 241], "summary": {"covered_lines": 4, "num_statements": 5, "percent_covered": 80.0, "percent_covered_display": "80", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [242], "excluded_lines": []}, "TestDiagramExports.test_export_duct_diagram_png": {"executed_lines": [246, 255, 260], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestDiagramExports.test_export_3d_model": {"executed_lines": [264, 272, 277], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestExportFormats.app": {"executed_lines": [286, 287, 288, 289], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestExportFormats.client": {"executed_lines": [294], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestExportFormats.test_get_supported_formats": {"executed_lines": [298, 301, 303], "summary": {"covered_lines": 3, "num_statements": 6, "percent_covered": 50.0, "percent_covered_display": "50", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [304, 305, 306], "excluded_lines": []}, "TestExportFormats.test_get_format_capabilities": {"executed_lines": [310, 313, 315], "summary": {"covered_lines": 3, "num_statements": 5, "percent_covered": 60.0, "percent_covered_display": "60", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [316, 317], "excluded_lines": []}, "TestExportFormats.test_validate_export_format": {"executed_lines": [321, 330, 335], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestExportSecurity.app": {"executed_lines": [344, 345, 346, 347], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestExportSecurity.client": {"executed_lines": [352], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestExportSecurity.test_unauthorized_export_access": {"executed_lines": [356, 361, 366], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestExportSecurity.test_export_access_control": {"executed_lines": [370, 376, 381], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestExportSecurity.test_export_data_sanitization": {"executed_lines": [385, 391, 396], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestExportSecurity.test_valid_token_export_access": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [402, 404, 405, 410, 416], "excluded_lines": []}, "TestExportErrorHandling.app": {"executed_lines": [425, 426, 427, 428], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestExportErrorHandling.client": {"executed_lines": [433], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestExportErrorHandling.test_invalid_export_format": {"executed_lines": [437, 442, 447], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestExportErrorHandling.test_missing_export_data": {"executed_lines": [451, 456, 461], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestExportErrorHandling.test_nonexistent_resource_export": {"executed_lines": [465, 470, 475], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestExportErrorHandling.test_export_generation_failure": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [481, 483, 488, 493], "excluded_lines": []}, "TestExportPerformance.app": {"executed_lines": [502, 503, 504, 505], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestExportPerformance.client": {"executed_lines": [510], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestExportPerformance.test_small_export_response_time": {"executed_lines": [514, 516, 521, 522, 525, 527, 530, 531], "summary": {"covered_lines": 8, "num_statements": 8, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestExportPerformance.test_large_export_handling": {"executed_lines": [535, 541, 546], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestExportPerformance.test_concurrent_exports": {"executed_lines": [550, 552, 554, 566, 567, 568, 569, 570, 573, 574, 577, 579, 580], "summary": {"covered_lines": 13, "num_statements": 13, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestExportPerformance.test_concurrent_exports.make_export_request": {"executed_lines": [555, 556, 560, 563], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestExportPerformance.test_export_file_size_limits": {"executed_lines": [584, 590, 595], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestExportFileHandling.app": {"executed_lines": [604, 605, 606, 607], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestExportFileHandling.client": {"executed_lines": [612], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestExportFileHandling.test_export_file_cleanup": {"executed_lines": [616, 622, 627], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestExportFileHandling.test_export_download_links": {"executed_lines": [631, 634], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestExportFileHandling.test_export_status_tracking": {"executed_lines": [638, 641, 643], "summary": {"covered_lines": 3, "num_statements": 6, "percent_covered": 50.0, "percent_covered_display": "50", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [644, 645, 646], "excluded_lines": []}, "": {"executed_lines": [1, 6, 7, 8, 9, 10, 11, 14, 15, 22, 23, 25, 26, 33, 34, 38, 43, 52, 53, 55, 56, 63, 64, 68, 87, 105, 121, 142, 143, 145, 146, 153, 154, 158, 175, 191, 208, 209, 211, 212, 219, 220, 224, 244, 262, 280, 281, 283, 284, 291, 292, 296, 308, 319, 338, 339, 341, 342, 349, 350, 354, 368, 383, 398, 399, 419, 420, 422, 423, 430, 431, 435, 449, 463, 477, 478, 496, 497, 499, 500, 507, 508, 512, 533, 548, 582, 598, 599, 601, 602, 609, 610, 614, 629, 636], "summary": {"covered_lines": 85, "num_statements": 88, "percent_covered": 96.5909090909091, "percent_covered_display": "97", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [16, 18, 19], "excluded_lines": []}}, "classes": {"TestExportsBlueprint": {"executed_lines": [28, 29, 30, 31, 40, 41, 45, 47, 49], "summary": {"covered_lines": 9, "num_statements": 10, "percent_covered": 90.0, "percent_covered_display": "90", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [36], "excluded_lines": []}, "TestCalculationExports": {"executed_lines": [58, 59, 60, 61, 66, 70, 76, 81, 83, 89, 95, 100, 102, 107, 114, 119, 123, 131, 136, 138], "summary": {"covered_lines": 20, "num_statements": 23, "percent_covered": 86.95652173913044, "percent_covered_display": "87", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [85, 103, 139], "excluded_lines": []}, "TestProjectExports": {"executed_lines": [148, 149, 150, 151, 156, 160, 168, 173, 177, 184, 189, 193, 200, 205], "summary": {"covered_lines": 14, "num_statements": 14, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestDiagramExports": {"executed_lines": [214, 215, 216, 217, 222, 226, 234, 239, 241, 246, 255, 260, 264, 272, 277], "summary": {"covered_lines": 15, "num_statements": 16, "percent_covered": 93.75, "percent_covered_display": "94", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [242], "excluded_lines": []}, "TestExportFormats": {"executed_lines": [286, 287, 288, 289, 294, 298, 301, 303, 310, 313, 315, 321, 330, 335], "summary": {"covered_lines": 14, "num_statements": 19, "percent_covered": 73.6842105263158, "percent_covered_display": "74", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [304, 305, 306, 316, 317], "excluded_lines": []}, "TestExportSecurity": {"executed_lines": [344, 345, 346, 347, 352, 356, 361, 366, 370, 376, 381, 385, 391, 396], "summary": {"covered_lines": 14, "num_statements": 19, "percent_covered": 73.6842105263158, "percent_covered_display": "74", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [402, 404, 405, 410, 416], "excluded_lines": []}, "TestExportErrorHandling": {"executed_lines": [425, 426, 427, 428, 433, 437, 442, 447, 451, 456, 461, 465, 470, 475], "summary": {"covered_lines": 14, "num_statements": 18, "percent_covered": 77.77777777777777, "percent_covered_display": "78", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [481, 483, 488, 493], "excluded_lines": []}, "TestExportPerformance": {"executed_lines": [502, 503, 504, 505, 510, 514, 516, 521, 522, 525, 527, 530, 531, 535, 541, 546, 550, 552, 554, 555, 556, 560, 563, 566, 567, 568, 569, 570, 573, 574, 577, 579, 580, 584, 590, 595], "summary": {"covered_lines": 36, "num_statements": 36, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestExportFileHandling": {"executed_lines": [604, 605, 606, 607, 612, 616, 622, 627, 631, 634, 638, 641, 643], "summary": {"covered_lines": 13, "num_statements": 16, "percent_covered": 81.25, "percent_covered_display": "81", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [644, 645, 646], "excluded_lines": []}, "": {"executed_lines": [1, 6, 7, 8, 9, 10, 11, 14, 15, 22, 23, 25, 26, 33, 34, 38, 43, 52, 53, 55, 56, 63, 64, 68, 87, 105, 121, 142, 143, 145, 146, 153, 154, 158, 175, 191, 208, 209, 211, 212, 219, 220, 224, 244, 262, 280, 281, 283, 284, 291, 292, 296, 308, 319, 338, 339, 341, 342, 349, 350, 354, 368, 383, 398, 399, 419, 420, 422, 423, 430, 431, 435, 449, 463, 477, 478, 496, 497, 499, 500, 507, 508, 512, 533, 548, 582, 598, 599, 601, 602, 609, 610, 614, 629, 636], "summary": {"covered_lines": 85, "num_statements": 88, "percent_covered": 96.5909090909091, "percent_covered_display": "97", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [16, 18, 19], "excluded_lines": []}}}, "tests\\test_input_validation.py": {"executed_lines": [1, 6, 7, 8, 9, 10, 15, 16, 18, 20, 22, 24, 25, 27, 29, 30, 31, 33, 35, 36, 38, 40, 41, 42, 44, 47, 48, 51, 52, 54, 57, 58, 61, 62, 64, 66, 67, 69, 71, 72, 74, 75, 77, 80, 81, 84, 85, 91, 93, 94, 96, 98, 104, 106, 107, 108, 110, 112, 122, 124, 125, 126, 128, 130, 135, 137, 138, 140, 142, 144, 146, 148, 150, 155, 157, 158, 159, 162, 163, 165, 167, 169, 171, 179, 181, 182, 183, 184, 186, 188, 194, 196, 197, 198, 200, 202, 209, 211, 212, 214, 216, 223, 225, 226, 227, 229, 231, 236, 238, 239, 241, 243, 245, 248, 249, 251, 253, 258, 261, 262, 265, 266, 268, 270, 271, 273, 275, 276, 277, 278, 280, 287, 288, 289, 290, 292, 294, 295, 296, 299, 304, 305, 309, 311, 312, 313, 316, 317, 318, 323, 324, 326, 328, 329, 332, 333, 334, 336, 337, 340, 342, 344, 345, 346, 348, 350, 352, 353, 355, 357, 359, 360, 362, 364, 366, 370, 371, 372, 373, 375, 377, 379, 383, 384, 385, 388], "summary": {"covered_lines": 181, "num_statements": 191, "percent_covered": 94.76439790575917, "percent_covered_display": "95", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [88, 89, 297, 306, 307, 314, 319, 320, 338, 389], "excluded_lines": [], "functions": {"TestInputSanitizer.setup_method": {"executed_lines": [20], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestInputSanitizer.test_sanitize_string_basic": {"executed_lines": [24, 25], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestInputSanitizer.test_sanitize_string_html_escape": {"executed_lines": [29, 30, 31], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestInputSanitizer.test_sanitize_string_sql_injection": {"executed_lines": [35, 36], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestInputSanitizer.test_sanitize_string_path_traversal": {"executed_lines": [40, 41, 42], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestInputSanitizer.test_sanitize_string_hvac_duct_type": {"executed_lines": [47, 48, 51, 52], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestInputSanitizer.test_sanitize_string_hvac_units": {"executed_lines": [57, 58, 61, 62], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestInputSanitizer.test_sanitize_number_valid": {"executed_lines": [66, 67], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestInputSanitizer.test_sanitize_number_string_conversion": {"executed_lines": [71, 72, 74, 75], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestInputSanitizer.test_sanitize_number_hvac_range_validation": {"executed_lines": [80, 81, 84, 85], "summary": {"covered_lines": 4, "num_statements": 6, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [88, 89], "excluded_lines": []}, "TestInputSanitizer.test_sanitize_number_invalid_string": {"executed_lines": [93, 94], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestInputSanitizer.test_sanitize_dict_basic": {"executed_lines": [98, 104, 106, 107, 108], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestInputSanitizer.test_sanitize_dict_nested": {"executed_lines": [112, 122, 124, 125, 126], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestInputSanitizer.test_sanitize_dict_malicious_content": {"executed_lines": [130, 135, 137, 138], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestInputSanitizer.test_sanitize_list_basic": {"executed_lines": [142, 144, 146], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestInputSanitizer.test_sanitize_list_nested": {"executed_lines": [150, 155, 157, 158, 159], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestInputValidator.setup_method": {"executed_lines": [167], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestInputValidator.test_validate_air_duct_calculation_valid": {"executed_lines": [171, 179, 181, 182, 183, 184], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestInputValidator.test_validate_air_duct_calculation_missing_required": {"executed_lines": [188, 194, 196, 197, 198], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestInputValidator.test_validate_air_duct_calculation_invalid_values": {"executed_lines": [202, 209, 211, 212], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestInputValidator.test_validate_project_valid": {"executed_lines": [216, 223, 225, 226, 227], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestInputValidator.test_validate_project_invalid_units": {"executed_lines": [231, 236, 238, 239], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestInputValidator.test_validate_unknown_schema": {"executed_lines": [243, 245, 248, 249], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestInputValidator.test_validate_malicious_input": {"executed_lines": [253, 258, 261, 262], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestValidateInputDecorator.setup_method": {"executed_lines": [270, 271], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestValidateInputDecorator.test_decorator_valid_input": {"executed_lines": [275, 276, 277, 280, 287, 288, 289, 290], "summary": {"covered_lines": 8, "num_statements": 8, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestValidateInputDecorator.test_decorator_valid_input.test_endpoint": {"executed_lines": [278], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestValidateInputDecorator.test_decorator_invalid_input": {"executed_lines": [294, 295, 296, 299, 304, 305], "summary": {"covered_lines": 6, "num_statements": 8, "percent_covered": 75.0, "percent_covered_display": "75", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [306, 307], "excluded_lines": []}, "TestValidateInputDecorator.test_decorator_invalid_input.test_endpoint": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [297], "excluded_lines": []}, "TestValidateInputDecorator.test_decorator_no_input_required": {"executed_lines": [311, 312, 313, 316, 317, 318], "summary": {"covered_lines": 6, "num_statements": 8, "percent_covered": 75.0, "percent_covered_display": "75", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [319, 320], "excluded_lines": []}, "TestValidateInputDecorator.test_decorator_no_input_required.test_endpoint": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [314], "excluded_lines": []}, "TestInputValidationMiddleware.setup_method": {"executed_lines": [328, 329, 332, 333, 336, 337, 340], "summary": {"covered_lines": 7, "num_statements": 7, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestInputValidationMiddleware.setup_method.test_route": {"executed_lines": [334], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestInputValidationMiddleware.setup_method.health_route": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [338], "excluded_lines": []}, "TestInputValidationMiddleware.test_middleware_initialization": {"executed_lines": [344, 345, 346], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestInputValidationMiddleware.test_middleware_skip_get_requests": {"executed_lines": [350, 352, 353], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestInputValidationMiddleware.test_middleware_skip_health_check": {"executed_lines": [357, 359, 360], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestInputValidationMiddleware.test_middleware_process_valid_json": {"executed_lines": [364, 366, 370, 371, 372, 373], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestInputValidationMiddleware.test_middleware_reject_malicious_input": {"executed_lines": [377, 379, 383, 384, 385], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 6, 7, 8, 9, 10, 15, 16, 18, 22, 27, 33, 38, 44, 54, 64, 69, 77, 91, 96, 110, 128, 140, 148, 162, 163, 165, 169, 186, 200, 214, 229, 241, 251, 265, 266, 268, 273, 292, 309, 323, 324, 326, 342, 348, 355, 362, 375, 388], "summary": {"covered_lines": 44, "num_statements": 45, "percent_covered": 97.77777777777777, "percent_covered_display": "98", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [389], "excluded_lines": []}}, "classes": {"TestInputSanitizer": {"executed_lines": [20, 24, 25, 29, 30, 31, 35, 36, 40, 41, 42, 47, 48, 51, 52, 57, 58, 61, 62, 66, 67, 71, 72, 74, 75, 80, 81, 84, 85, 93, 94, 98, 104, 106, 107, 108, 112, 122, 124, 125, 126, 130, 135, 137, 138, 142, 144, 146, 150, 155, 157, 158, 159], "summary": {"covered_lines": 53, "num_statements": 55, "percent_covered": 96.36363636363636, "percent_covered_display": "96", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [88, 89], "excluded_lines": []}, "TestInputValidator": {"executed_lines": [167, 171, 179, 181, 182, 183, 184, 188, 194, 196, 197, 198, 202, 209, 211, 212, 216, 223, 225, 226, 227, 231, 236, 238, 239, 243, 245, 248, 249, 253, 258, 261, 262], "summary": {"covered_lines": 33, "num_statements": 33, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestValidateInputDecorator": {"executed_lines": [270, 271, 275, 276, 277, 278, 280, 287, 288, 289, 290, 294, 295, 296, 299, 304, 305, 311, 312, 313, 316, 317, 318], "summary": {"covered_lines": 23, "num_statements": 29, "percent_covered": 79.3103448275862, "percent_covered_display": "79", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [297, 306, 307, 314, 319, 320], "excluded_lines": []}, "TestInputValidationMiddleware": {"executed_lines": [328, 329, 332, 333, 334, 336, 337, 340, 344, 345, 346, 350, 352, 353, 357, 359, 360, 364, 366, 370, 371, 372, 373, 377, 379, 383, 384, 385], "summary": {"covered_lines": 28, "num_statements": 29, "percent_covered": 96.55172413793103, "percent_covered_display": "97", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [338], "excluded_lines": []}, "": {"executed_lines": [1, 6, 7, 8, 9, 10, 15, 16, 18, 22, 27, 33, 38, 44, 54, 64, 69, 77, 91, 96, 110, 128, 140, 148, 162, 163, 165, 169, 186, 200, 214, 229, 241, 251, 265, 266, 268, 273, 292, 309, 323, 324, 326, 342, 348, 355, 362, 375, 388], "summary": {"covered_lines": 44, "num_statements": 45, "percent_covered": 97.77777777777777, "percent_covered_display": "98", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [389], "excluded_lines": []}}}, "tests\\test_mongodb_api.py": {"executed_lines": [1, 6, 7, 8, 9, 10, 13, 14, 21, 22, 24, 25, 27, 28, 29, 30, 32, 33, 37, 39, 42, 44, 46, 48, 51, 52, 54, 55, 57, 58, 59, 60, 62, 63, 65, 67, 69, 72, 74, 79, 80, 92, 93, 103, 105, 108, 110, 115, 116, 118, 119, 121, 122, 123, 124, 126, 127, 129, 131, 133, 145, 150, 152, 157, 159, 160, 163, 165, 170, 172, 173, 179, 184, 186, 188, 189, 192, 194, 196, 202, 206, 208, 214, 215, 217, 218, 220, 221, 222, 223, 225, 226, 228, 230, 232, 252, 257, 259, 263, 265, 266, 269, 271, 276, 278, 279, 284, 288, 290, 295, 297, 298, 307, 312, 314, 316, 317, 320, 323, 324, 326, 327, 329, 330, 331, 332, 334, 335, 337, 339, 341, 342, 356, 361, 363, 365, 366, 369, 371, 375, 377, 378, 389, 394, 397, 398, 400, 401, 403, 404, 405, 406, 408, 409, 411, 413, 415, 423, 424, 429, 431, 433, 440, 441, 446, 448, 450, 459, 464, 467, 468, 470, 471, 473, 474, 475, 476, 478, 479, 481, 483, 484, 494, 495, 508, 510, 511, 514, 516, 518, 519, 522, 525, 526, 528, 529, 531, 532, 533, 534, 536, 537, 539, 541, 543, 545, 546, 547, 549, 552, 553, 555, 557, 565, 570, 572, 574, 576, 578, 579, 580, 581, 584, 585, 586, 587, 588, 591, 592, 595, 597, 598, 601, 602, 604, 605, 607, 608, 609, 610, 612, 613, 615, 617, 619, 625, 626, 629, 631, 634, 635, 638, 640, 641], "summary": {"covered_lines": 244, "num_statements": 292, "percent_covered": 83.56164383561644, "percent_covered_display": "84", "missing_lines": 48, "excluded_lines": 0}, "missing_lines": [15, 17, 18, 35, 40, 75, 76, 77, 83, 84, 85, 87, 90, 96, 98, 101, 111, 112, 153, 154, 155, 166, 167, 168, 209, 210, 211, 260, 261, 272, 273, 274, 291, 292, 293, 372, 373, 487, 489, 492, 498, 500, 501, 506, 644, 646, 647, 650], "excluded_lines": [], "functions": {"TestMongoDBBlueprint.app": {"executed_lines": [27, 28, 29, 30], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestMongoDBBlueprint.client": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [35], "excluded_lines": []}, "TestMongoDBBlueprint.test_blueprint_registration": {"executed_lines": [39], "summary": {"covered_lines": 1, "num_statements": 2, "percent_covered": 50.0, "percent_covered_display": "50", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [40], "excluded_lines": []}, "TestMongoDBBlueprint.test_blueprint_url_prefix": {"executed_lines": [44, 46, 48], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestMongoDBConnection.app": {"executed_lines": [57, 58, 59, 60], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestMongoDBConnection.client": {"executed_lines": [65], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestMongoDBConnection.test_mongodb_health_check": {"executed_lines": [69, 72, 74], "summary": {"covered_lines": 3, "num_statements": 6, "percent_covered": 50.0, "percent_covered_display": "50", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [75, 76, 77], "excluded_lines": []}, "TestMongoDBConnection.test_mongodb_connection_success": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [83, 84, 85, 87, 90], "excluded_lines": []}, "TestMongoDBConnection.test_mongodb_connection_failure": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [96, 98, 101], "excluded_lines": []}, "TestMongoDBConnection.test_mongodb_database_info": {"executed_lines": [105, 108, 110], "summary": {"covered_lines": 3, "num_statements": 5, "percent_covered": 60.0, "percent_covered_display": "60", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [111, 112], "excluded_lines": []}, "TestProjectDataOperations.app": {"executed_lines": [121, 122, 123, 124], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestProjectDataOperations.client": {"executed_lines": [129], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestProjectDataOperations.test_create_project": {"executed_lines": [133, 145, 150, 152], "summary": {"covered_lines": 4, "num_statements": 7, "percent_covered": 57.142857142857146, "percent_covered_display": "57", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [153, 154, 155], "excluded_lines": []}, "TestProjectDataOperations.test_get_project": {"executed_lines": [159, 160, 163, 165], "summary": {"covered_lines": 4, "num_statements": 7, "percent_covered": 57.142857142857146, "percent_covered_display": "57", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [166, 167, 168], "excluded_lines": []}, "TestProjectDataOperations.test_update_project": {"executed_lines": [172, 173, 179, 184], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestProjectDataOperations.test_delete_project": {"executed_lines": [188, 189, 192], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestProjectDataOperations.test_list_projects": {"executed_lines": [196, 202, 206, 208], "summary": {"covered_lines": 4, "num_statements": 7, "percent_covered": 57.142857142857146, "percent_covered_display": "57", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [209, 210, 211], "excluded_lines": []}, "TestCalculationDataOperations.app": {"executed_lines": [220, 221, 222, 223], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCalculationDataOperations.client": {"executed_lines": [228], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCalculationDataOperations.test_save_calculation_result": {"executed_lines": [232, 252, 257, 259], "summary": {"covered_lines": 4, "num_statements": 6, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [260, 261], "excluded_lines": []}, "TestCalculationDataOperations.test_get_calculation_result": {"executed_lines": [265, 266, 269, 271], "summary": {"covered_lines": 4, "num_statements": 7, "percent_covered": 57.142857142857146, "percent_covered_display": "57", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [272, 273, 274], "excluded_lines": []}, "TestCalculationDataOperations.test_list_project_calculations": {"executed_lines": [278, 279, 284, 288, 290], "summary": {"covered_lines": 5, "num_statements": 8, "percent_covered": 62.5, "percent_covered_display": "62", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [291, 292, 293], "excluded_lines": []}, "TestCalculationDataOperations.test_update_calculation_result": {"executed_lines": [297, 298, 307, 312], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestCalculationDataOperations.test_delete_calculation_result": {"executed_lines": [316, 317, 320], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestUserDataOperations.app": {"executed_lines": [329, 330, 331, 332], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestUserDataOperations.client": {"executed_lines": [337], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestUserDataOperations.test_save_user_preferences": {"executed_lines": [341, 342, 356, 361], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestUserDataOperations.test_get_user_preferences": {"executed_lines": [365, 366, 369, 371], "summary": {"covered_lines": 4, "num_statements": 6, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [372, 373], "excluded_lines": []}, "TestUserDataOperations.test_save_user_session_data": {"executed_lines": [377, 378, 389, 394], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestDataValidation.app": {"executed_lines": [403, 404, 405, 406], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestDataValidation.client": {"executed_lines": [411], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestDataValidation.test_invalid_project_data": {"executed_lines": [415, 423, 424, 429], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestDataValidation.test_invalid_calculation_data": {"executed_lines": [433, 440, 441, 446], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestDataValidation.test_malicious_input_sanitization": {"executed_lines": [450, 459, 464], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestMongoDBErrorHandling.app": {"executed_lines": [473, 474, 475, 476], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestMongoDBErrorHandling.client": {"executed_lines": [481], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestMongoDBErrorHandling.test_database_connection_error": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [487, 489, 492], "excluded_lines": []}, "TestMongoDBErrorHandling.test_collection_operation_error": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [498, 500, 501, 506], "excluded_lines": []}, "TestMongoDBErrorHandling.test_invalid_object_id": {"executed_lines": [510, 511, 514], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestMongoDBErrorHandling.test_document_not_found": {"executed_lines": [518, 519, 522], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestMongoDBPerformance.app": {"executed_lines": [531, 532, 533, 534], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestMongoDBPerformance.client": {"executed_lines": [539], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestMongoDBPerformance.test_query_response_time": {"executed_lines": [543, 545, 546, 547, 549, 552, 553], "summary": {"covered_lines": 7, "num_statements": 7, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestMongoDBPerformance.test_large_data_handling": {"executed_lines": [557, 565, 570], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestMongoDBPerformance.test_concurrent_database_operations": {"executed_lines": [574, 576, 578, 584, 585, 586, 587, 588, 591, 592, 595, 597, 598], "summary": {"covered_lines": 13, "num_statements": 13, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestMongoDBPerformance.test_concurrent_database_operations.make_database_request": {"executed_lines": [579, 580, 581], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestMongoDBSecurity.app": {"executed_lines": [607, 608, 609, 610], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestMongoDBSecurity.client": {"executed_lines": [615], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestMongoDBSecurity.test_unauthorized_database_access": {"executed_lines": [619, 625, 626, 629], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestMongoDBSecurity.test_user_data_isolation": {"executed_lines": [634, 635, 638], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestMongoDBSecurity.test_valid_token_database_access": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [644, 646, 647, 650], "excluded_lines": []}, "": {"executed_lines": [1, 6, 7, 8, 9, 10, 13, 14, 21, 22, 24, 25, 32, 33, 37, 42, 51, 52, 54, 55, 62, 63, 67, 79, 80, 92, 93, 103, 115, 116, 118, 119, 126, 127, 131, 157, 170, 186, 194, 214, 215, 217, 218, 225, 226, 230, 263, 276, 295, 314, 323, 324, 326, 327, 334, 335, 339, 363, 375, 397, 398, 400, 401, 408, 409, 413, 431, 448, 467, 468, 470, 471, 478, 479, 483, 484, 494, 495, 508, 516, 525, 526, 528, 529, 536, 537, 541, 555, 572, 601, 602, 604, 605, 612, 613, 617, 631, 640, 641], "summary": {"covered_lines": 89, "num_statements": 92, "percent_covered": 96.73913043478261, "percent_covered_display": "97", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [15, 17, 18], "excluded_lines": []}}, "classes": {"TestMongoDBBlueprint": {"executed_lines": [27, 28, 29, 30, 39, 44, 46, 48], "summary": {"covered_lines": 8, "num_statements": 10, "percent_covered": 80.0, "percent_covered_display": "80", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [35, 40], "excluded_lines": []}, "TestMongoDBConnection": {"executed_lines": [57, 58, 59, 60, 65, 69, 72, 74, 105, 108, 110], "summary": {"covered_lines": 11, "num_statements": 24, "percent_covered": 45.833333333333336, "percent_covered_display": "46", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [75, 76, 77, 83, 84, 85, 87, 90, 96, 98, 101, 111, 112], "excluded_lines": []}, "TestProjectDataOperations": {"executed_lines": [121, 122, 123, 124, 129, 133, 145, 150, 152, 159, 160, 163, 165, 172, 173, 179, 184, 188, 189, 192, 196, 202, 206, 208], "summary": {"covered_lines": 24, "num_statements": 33, "percent_covered": 72.72727272727273, "percent_covered_display": "73", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [153, 154, 155, 166, 167, 168, 209, 210, 211], "excluded_lines": []}, "TestCalculationDataOperations": {"executed_lines": [220, 221, 222, 223, 228, 232, 252, 257, 259, 265, 266, 269, 271, 278, 279, 284, 288, 290, 297, 298, 307, 312, 316, 317, 320], "summary": {"covered_lines": 25, "num_statements": 33, "percent_covered": 75.75757575757575, "percent_covered_display": "76", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [260, 261, 272, 273, 274, 291, 292, 293], "excluded_lines": []}, "TestUserDataOperations": {"executed_lines": [329, 330, 331, 332, 337, 341, 342, 356, 361, 365, 366, 369, 371, 377, 378, 389, 394], "summary": {"covered_lines": 17, "num_statements": 19, "percent_covered": 89.47368421052632, "percent_covered_display": "89", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [372, 373], "excluded_lines": []}, "TestDataValidation": {"executed_lines": [403, 404, 405, 406, 411, 415, 423, 424, 429, 433, 440, 441, 446, 450, 459, 464], "summary": {"covered_lines": 16, "num_statements": 16, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestMongoDBErrorHandling": {"executed_lines": [473, 474, 475, 476, 481, 510, 511, 514, 518, 519, 522], "summary": {"covered_lines": 11, "num_statements": 18, "percent_covered": 61.111111111111114, "percent_covered_display": "61", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [487, 489, 492, 498, 500, 501, 506], "excluded_lines": []}, "TestMongoDBPerformance": {"executed_lines": [531, 532, 533, 534, 539, 543, 545, 546, 547, 549, 552, 553, 557, 565, 570, 574, 576, 578, 579, 580, 581, 584, 585, 586, 587, 588, 591, 592, 595, 597, 598], "summary": {"covered_lines": 31, "num_statements": 31, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestMongoDBSecurity": {"executed_lines": [607, 608, 609, 610, 615, 619, 625, 626, 629, 634, 635, 638], "summary": {"covered_lines": 12, "num_statements": 16, "percent_covered": 75.0, "percent_covered_display": "75", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [644, 646, 647, 650], "excluded_lines": []}, "": {"executed_lines": [1, 6, 7, 8, 9, 10, 13, 14, 21, 22, 24, 25, 32, 33, 37, 42, 51, 52, 54, 55, 62, 63, 67, 79, 80, 92, 93, 103, 115, 116, 118, 119, 126, 127, 131, 157, 170, 186, 194, 214, 215, 217, 218, 225, 226, 230, 263, 276, 295, 314, 323, 324, 326, 327, 334, 335, 339, 363, 375, 397, 398, 400, 401, 408, 409, 413, 431, 448, 467, 468, 470, 471, 478, 479, 483, 484, 494, 495, 508, 516, 525, 526, 528, 529, 536, 537, 541, 555, 572, 601, 602, 604, 605, 612, 613, 617, 631, 640, 641], "summary": {"covered_lines": 89, "num_statements": 92, "percent_covered": 96.73913043478261, "percent_covered_display": "97", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [15, 17, 18], "excluded_lines": []}}}, "tests\\test_pr30_validation.py": {"executed_lines": [1, 6, 7, 8, 9, 10, 11, 12, 15, 16, 18, 20, 29, 30, 33, 35, 37, 44, 46, 47, 52, 54, 61, 62, 65, 67, 69, 83, 88, 89, 90, 92, 95, 98, 99, 101, 102, 103, 104, 106, 108, 109, 112, 113, 115, 117, 124, 125, 126, 128, 130, 137, 138, 140, 141, 144, 145, 146, 148, 150, 153, 159, 160, 162, 163, 164, 165, 167, 169, 176, 179, 180, 181, 183, 185, 193, 195, 196, 197, 199, 200, 203, 206, 207, 209, 212, 229, 239, 240, 242, 244, 255, 262, 263, 266, 267, 269, 272, 280, 282, 283, 285, 288, 296, 298, 299, 305], "summary": {"covered_lines": 101, "num_statements": 105, "percent_covered": 96.19047619047619, "percent_covered_display": "96", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [50, 300, 302, 306], "excluded_lines": [], "functions": {"TestJsonSchemaUpdates.test_basic_validation": {"executed_lines": [20, 29, 30, 33], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestJsonSchemaUpdates.test_enhanced_error_handling": {"executed_lines": [37, 44, 46, 47], "summary": {"covered_lines": 4, "num_statements": 5, "percent_covered": 80.0, "percent_covered_display": "80", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [50], "excluded_lines": []}, "TestJsonSchemaUpdates.test_iri_format_support": {"executed_lines": [54, 61, 62, 65], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestJsonSchemaUpdates.test_validation_performance": {"executed_lines": [69, 83, 88, 89, 90, 92, 95], "summary": {"covered_lines": 7, "num_statements": 7, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestPydanticCoreUpdates.test_basic_model_validation": {"executed_lines": [117, 124, 125, 126], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestPydanticCoreUpdates.test_enhanced_validation_errors": {"executed_lines": [130, 137, 138, 140, 141, 144, 145, 146], "summary": {"covered_lines": 8, "num_statements": 8, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestPydanticCoreUpdates.test_missing_sentinel_functionality": {"executed_lines": [150, 153, 159, 160, 162, 163, 164, 165], "summary": {"covered_lines": 8, "num_statements": 8, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestPydanticCoreUpdates.test_field_level_exclusion": {"executed_lines": [169, 176, 179, 180, 181], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestPydanticCoreUpdates.test_validation_performance": {"executed_lines": [185, 193, 195, 196, 197, 199, 200, 203], "summary": {"covered_lines": 8, "num_statements": 8, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestIntegrationCompatibility.test_mongodb_schema_validation": {"executed_lines": [212, 229, 239, 240], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestIntegrationCompatibility.test_api_response_validation": {"executed_lines": [244, 255, 262, 263], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSecurityValidation.test_input_sanitization": {"executed_lines": [272, 280, 282, 283], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSecurityValidation.test_schema_security": {"executed_lines": [288, 296, 298, 299], "summary": {"covered_lines": 4, "num_statements": 6, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [300, 302], "excluded_lines": []}, "": {"executed_lines": [1, 6, 7, 8, 9, 10, 11, 12, 15, 16, 18, 35, 52, 67, 98, 99, 101, 102, 103, 104, 106, 108, 109, 112, 113, 115, 128, 148, 167, 183, 206, 207, 209, 242, 266, 267, 269, 285, 305], "summary": {"covered_lines": 33, "num_statements": 34, "percent_covered": 97.05882352941177, "percent_covered_display": "97", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [306], "excluded_lines": []}}, "classes": {"TestJsonSchemaUpdates": {"executed_lines": [20, 29, 30, 33, 37, 44, 46, 47, 54, 61, 62, 65, 69, 83, 88, 89, 90, 92, 95], "summary": {"covered_lines": 19, "num_statements": 20, "percent_covered": 95.0, "percent_covered_display": "95", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [50], "excluded_lines": []}, "HVACCalculationModel": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "HVACCalculationModel.Config": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestPydanticCoreUpdates": {"executed_lines": [117, 124, 125, 126, 130, 137, 138, 140, 141, 144, 145, 146, 150, 153, 159, 160, 162, 163, 164, 165, 169, 176, 179, 180, 181, 185, 193, 195, 196, 197, 199, 200, 203], "summary": {"covered_lines": 33, "num_statements": 33, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestIntegrationCompatibility": {"executed_lines": [212, 229, 239, 240, 244, 255, 262, 263], "summary": {"covered_lines": 8, "num_statements": 8, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSecurityValidation": {"executed_lines": [272, 280, 282, 283, 288, 296, 298, 299], "summary": {"covered_lines": 8, "num_statements": 10, "percent_covered": 80.0, "percent_covered_display": "80", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [300, 302], "excluded_lines": []}, "": {"executed_lines": [1, 6, 7, 8, 9, 10, 11, 12, 15, 16, 18, 35, 52, 67, 98, 99, 101, 102, 103, 104, 106, 108, 109, 112, 113, 115, 128, 148, 167, 183, 206, 207, 209, 242, 266, 267, 269, 285, 305], "summary": {"covered_lines": 33, "num_statements": 34, "percent_covered": 97.05882352941177, "percent_covered_display": "97", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [306], "excluded_lines": []}}}, "tests\\test_rate_limiting.py": {"executed_lines": [1, 6, 7, 8, 9, 10, 11, 12, 15, 16, 18, 21, 22, 25, 26, 28, 30, 31, 32, 33, 34, 36, 38, 39, 40, 41, 42, 44, 46, 47, 48, 49, 50, 52, 54, 55, 56, 57, 58, 59, 61, 63, 64, 65, 66, 67, 68, 70, 72, 73, 74, 76, 78, 79, 80, 81, 82, 83, 85, 88, 89, 90, 91, 93, 94, 96, 98, 100, 101, 102, 104, 107, 109, 110, 112, 114, 116, 117, 118, 120, 123, 125, 126, 127, 129, 132, 133, 135, 137, 138, 140, 147, 149, 150, 151, 152, 155, 156, 158, 160, 161, 163, 165, 166, 167, 168, 170, 173, 174, 176, 178, 179, 180, 183, 190, 191, 192, 193, 196, 197, 199, 201, 202, 205, 206, 209, 210, 213, 215, 216, 217, 219, 221, 223, 224, 226, 228, 230, 231, 233, 235, 237, 238, 241, 242, 244, 246, 247, 250, 253, 254, 255, 257, 259, 261, 262, 269, 271, 272, 273, 274, 276, 278, 279, 286, 288, 289, 290, 291, 294], "summary": {"covered_lines": 165, "num_statements": 169, "percent_covered": 97.63313609467455, "percent_covered_display": "98", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [181, 207, 211, 295], "excluded_lines": [], "functions": {"TestRateLimiter.setup_method": {"executed_lines": [21, 22, 25, 26], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestRateLimiter.test_get_client_identifier_authenticated": {"executed_lines": [30, 31, 32, 33, 34], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestRateLimiter.test_get_client_identifier_anonymous": {"executed_lines": [38, 39, 40, 41, 42], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestRateLimiter.test_get_user_tier_premium": {"executed_lines": [46, 47, 48, 49, 50], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestRateLimiter.test_get_user_tier_authenticated": {"executed_lines": [54, 55, 56, 57, 58, 59], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestRateLimiter.test_get_user_tier_anonymous": {"executed_lines": [63, 64, 65, 66, 67, 68], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestRateLimiter.test_rate_limit_config_endpoint_specific": {"executed_lines": [72, 73, 74], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestRateLimiter.test_rate_limit_config_user_tier": {"executed_lines": [78, 79, 80, 81, 82, 83], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestRateLimiter.test_is_rate_limited_under_limit": {"executed_lines": [88, 89, 90, 91, 93, 94, 96, 98, 100, 101, 102], "summary": {"covered_lines": 11, "num_statements": 11, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestRateLimiter.test_is_rate_limited_over_limit": {"executed_lines": [107, 109, 110, 112, 114, 116, 117, 118], "summary": {"covered_lines": 8, "num_statements": 8, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestRateLimiter.test_is_rate_limited_redis_error": {"executed_lines": [123, 125, 126, 127, 129, 132, 133], "summary": {"covered_lines": 7, "num_statements": 7, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestRateLimiter.test_add_rate_limit_headers": {"executed_lines": [137, 138, 140, 147, 149, 150, 151, 152], "summary": {"covered_lines": 8, "num_statements": 8, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestRateLimitDecorator.setup_method": {"executed_lines": [160, 161], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestRateLimitDecorator.test_rate_limit_decorator_success": {"executed_lines": [165, 166, 167, 170, 173, 174], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestRateLimitDecorator.test_rate_limit_decorator_success.test_endpoint": {"executed_lines": [168], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestRateLimitDecorator.test_rate_limit_decorator_blocked": {"executed_lines": [178, 179, 180, 183, 190, 191, 192, 193], "summary": {"covered_lines": 8, "num_statements": 8, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestRateLimitDecorator.test_rate_limit_decorator_blocked.test_endpoint": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [181], "excluded_lines": []}, "TestRateLimitMiddleware.setup_method": {"executed_lines": [201, 202, 205, 206, 209, 210], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestRateLimitMiddleware.setup_method.test_route": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [207], "excluded_lines": []}, "TestRateLimitMiddleware.setup_method.health_route": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [211], "excluded_lines": []}, "TestRateLimitMiddleware.test_middleware_initialization": {"executed_lines": [215, 216, 217], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestRateLimitMiddleware.test_middleware_skip_health_check": {"executed_lines": [221, 223, 224], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestRateLimitMiddleware.test_middleware_skip_options_request": {"executed_lines": [228, 230, 231], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestRateLimitMiddleware.test_middleware_process_normal_request": {"executed_lines": [235, 237, 238], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestRateLimitingIntegration.setup_method": {"executed_lines": [246, 247, 250, 253, 254, 257], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestRateLimitingIntegration.setup_method.test_route": {"executed_lines": [255], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestRateLimitingIntegration.test_rate_limiting_headers_added": {"executed_lines": [261, 262, 269, 271, 272, 273, 274], "summary": {"covered_lines": 7, "num_statements": 7, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestRateLimitingIntegration.test_rate_limiting_blocks_requests": {"executed_lines": [278, 279, 286, 288, 289, 290, 291], "summary": {"covered_lines": 7, "num_statements": 7, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 6, 7, 8, 9, 10, 11, 12, 15, 16, 18, 28, 36, 44, 52, 61, 70, 76, 85, 104, 120, 135, 155, 156, 158, 163, 176, 196, 197, 199, 213, 219, 226, 233, 241, 242, 244, 259, 276, 294], "summary": {"covered_lines": 35, "num_statements": 36, "percent_covered": 97.22222222222223, "percent_covered_display": "97", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [295], "excluded_lines": []}}, "classes": {"TestRateLimiter": {"executed_lines": [21, 22, 25, 26, 30, 31, 32, 33, 34, 38, 39, 40, 41, 42, 46, 47, 48, 49, 50, 54, 55, 56, 57, 58, 59, 63, 64, 65, 66, 67, 68, 72, 73, 74, 78, 79, 80, 81, 82, 83, 88, 89, 90, 91, 93, 94, 96, 98, 100, 101, 102, 107, 109, 110, 112, 114, 116, 117, 118, 123, 125, 126, 127, 129, 132, 133, 137, 138, 140, 147, 149, 150, 151, 152], "summary": {"covered_lines": 74, "num_statements": 74, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestRateLimitDecorator": {"executed_lines": [160, 161, 165, 166, 167, 168, 170, 173, 174, 178, 179, 180, 183, 190, 191, 192, 193], "summary": {"covered_lines": 17, "num_statements": 18, "percent_covered": 94.44444444444444, "percent_covered_display": "94", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [181], "excluded_lines": []}, "TestRateLimitMiddleware": {"executed_lines": [201, 202, 205, 206, 209, 210, 215, 216, 217, 221, 223, 224, 228, 230, 231, 235, 237, 238], "summary": {"covered_lines": 18, "num_statements": 20, "percent_covered": 90.0, "percent_covered_display": "90", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [207, 211], "excluded_lines": []}, "TestRateLimitingIntegration": {"executed_lines": [246, 247, 250, 253, 254, 255, 257, 261, 262, 269, 271, 272, 273, 274, 278, 279, 286, 288, 289, 290, 291], "summary": {"covered_lines": 21, "num_statements": 21, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 6, 7, 8, 9, 10, 11, 12, 15, 16, 18, 28, 36, 44, 52, 61, 70, 76, 85, 104, 120, 135, 155, 156, 158, 163, 176, 196, 197, 199, 213, 219, 226, 233, 241, 242, 244, 259, 276, 294], "summary": {"covered_lines": 35, "num_statements": 36, "percent_covered": 97.22222222222223, "percent_covered_display": "97", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [295], "excluded_lines": []}}}, "tests\\test_security_headers.py": {"executed_lines": [1, 6, 7, 8, 9, 10, 13, 14, 16, 18, 19, 20, 21, 24, 25, 26, 28, 29, 30, 32, 33, 34, 36, 37, 38, 40, 42, 43, 45, 47, 50, 51, 52, 53, 54, 57, 58, 59, 60, 62, 64, 66, 67, 70, 71, 72, 73, 75, 76, 79, 80, 81, 82, 84, 85, 86, 88, 91, 94, 95, 97, 98, 101, 102, 103, 104, 106, 107, 108, 110, 113, 114, 115, 116, 117, 119, 121, 124, 125, 126, 127, 128, 130, 132, 135, 136, 137, 139, 141, 144, 145, 146, 148, 150, 153, 155, 157, 160, 162, 164, 166, 167, 170, 171, 172, 174, 176, 178, 179, 180, 182, 183, 184, 186, 189, 190, 193, 194, 196, 198, 199, 204, 207, 208, 210, 211, 212, 214, 216, 217, 219, 220, 223, 224, 226, 228, 230, 231, 234, 237, 238, 240, 241, 242, 244, 247, 248, 249, 251, 253, 254, 257, 260, 262, 263, 265, 266, 268, 270, 271, 272, 275, 281, 283, 284, 285, 286, 288, 289, 290, 293, 296, 297, 302, 303, 304, 307, 308, 310, 312, 313, 315, 318, 319, 321, 323, 324, 325, 327, 328, 330, 332, 333, 335, 338, 342, 343, 345, 346, 347, 349, 350, 351, 354, 355, 358, 359, 362], "summary": {"covered_lines": 204, "num_statements": 207, "percent_covered": 98.55072463768116, "percent_covered_display": "99", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [276, 279, 363], "excluded_lines": [], "functions": {"TestSecurityHeadersMiddleware.setup_method": {"executed_lines": [18, 19, 20, 21, 24, 25, 28, 29, 32, 33, 36, 37], "summary": {"covered_lines": 12, "num_statements": 12, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSecurityHeadersMiddleware.setup_method.test_route": {"executed_lines": [26], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSecurityHeadersMiddleware.setup_method.auth_route": {"executed_lines": [30], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSecurityHeadersMiddleware.setup_method.calculation_route": {"executed_lines": [34], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSecurityHeadersMiddleware.setup_method.health_route": {"executed_lines": [38], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSecurityHeadersMiddleware.test_middleware_initialization": {"executed_lines": [42, 43], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSecurityHeadersMiddleware.test_default_security_headers_applied": {"executed_lines": [47, 50, 51, 52, 53, 54, 57, 58, 59, 60], "summary": {"covered_lines": 10, "num_statements": 10, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSecurityHeadersMiddleware.test_content_security_policy_header": {"executed_lines": [64, 66, 67, 70, 71, 72, 73], "summary": {"covered_lines": 7, "num_statements": 7, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSecurityHeadersMiddleware.test_development_environment_headers": {"executed_lines": [79, 80, 81, 82, 84, 85, 88, 91, 94, 95], "summary": {"covered_lines": 10, "num_statements": 10, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSecurityHeadersMiddleware.test_development_environment_headers.test_route": {"executed_lines": [86], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSecurityHeadersMiddleware.test_production_environment_headers": {"executed_lines": [101, 102, 103, 104, 106, 107, 110, 113, 114, 115, 116, 117], "summary": {"covered_lines": 12, "num_statements": 12, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSecurityHeadersMiddleware.test_production_environment_headers.test_route": {"executed_lines": [108], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSecurityHeadersMiddleware.test_auth_endpoint_specific_headers": {"executed_lines": [121, 124, 125, 126, 127, 128], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSecurityHeadersMiddleware.test_calculation_endpoint_headers": {"executed_lines": [132, 135, 136, 137], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSecurityHeadersMiddleware.test_health_endpoint_headers": {"executed_lines": [141, 144, 145, 146], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSecurityHeadersMiddleware.test_sensitive_headers_removed": {"executed_lines": [150, 153], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSecurityHeadersMiddleware.test_json_response_headers": {"executed_lines": [157, 160], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSecurityHeadersMiddleware.test_permissions_policy_header": {"executed_lines": [164, 166, 167, 170, 171, 172], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSecurityHeadersMiddleware.test_cross_origin_headers": {"executed_lines": [176, 178, 179, 180, 182, 183, 184], "summary": {"covered_lines": 7, "num_statements": 7, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSecurityHeadersMiddleware.test_csp_validation": {"executed_lines": [189, 190, 193, 194], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSecurityHeadersMiddleware.test_update_csp_for_endpoint": {"executed_lines": [198, 199, 204, 207, 208, 210, 211, 212], "summary": {"covered_lines": 8, "num_statements": 8, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSecurityHeadersMiddleware.test_factory_function": {"executed_lines": [216, 217, 219, 220], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSecurityHeadersIntegration.test_middleware_with_cors": {"executed_lines": [228, 230, 231, 234, 237, 238, 240, 241, 244, 247, 248, 249], "summary": {"covered_lines": 12, "num_statements": 12, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSecurityHeadersIntegration.test_middleware_with_cors.test_route": {"executed_lines": [242], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSecurityHeadersIntegration.test_middleware_error_handling": {"executed_lines": [253, 254, 257, 260, 262, 265, 266, 268, 270, 271, 275], "summary": {"covered_lines": 11, "num_statements": 13, "percent_covered": 84.61538461538461, "percent_covered_display": "85", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [276, 279], "excluded_lines": []}, "TestSecurityHeadersIntegration.test_middleware_error_handling.failing_method": {"executed_lines": [263], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSecurityHeadersIntegration.test_middleware_error_handling.test_route": {"executed_lines": [272], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSecurityHeadersIntegration.test_multiple_requests_consistency": {"executed_lines": [283, 284, 285, 286, 288, 289, 293, 296, 297, 302, 303, 304], "summary": {"covered_lines": 12, "num_statements": 12, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSecurityHeadersIntegration.test_multiple_requests_consistency.test_route": {"executed_lines": [290], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSecurityHeadersConfiguration.test_custom_header_configuration": {"executed_lines": [312, 313, 315, 318, 319, 321, 323, 324, 327, 328], "summary": {"covered_lines": 10, "num_statements": 10, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSecurityHeadersConfiguration.test_custom_header_configuration.test_route": {"executed_lines": [325], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSecurityHeadersConfiguration.test_endpoint_specific_configuration": {"executed_lines": [332, 333, 335, 338, 342, 343, 345, 346, 349, 350, 354, 355, 358, 359], "summary": {"covered_lines": 14, "num_statements": 14, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSecurityHeadersConfiguration.test_endpoint_specific_configuration.custom_route": {"executed_lines": [347], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSecurityHeadersConfiguration.test_endpoint_specific_configuration.other_route": {"executed_lines": [351], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 6, 7, 8, 9, 10, 13, 14, 16, 40, 45, 62, 75, 76, 97, 98, 119, 130, 139, 148, 155, 162, 174, 186, 196, 214, 223, 224, 226, 251, 281, 307, 308, 310, 330, 362], "summary": {"covered_lines": 32, "num_statements": 33, "percent_covered": 96.96969696969697, "percent_covered_display": "97", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [363], "excluded_lines": []}}, "classes": {"TestSecurityHeadersMiddleware": {"executed_lines": [18, 19, 20, 21, 24, 25, 26, 28, 29, 30, 32, 33, 34, 36, 37, 38, 42, 43, 47, 50, 51, 52, 53, 54, 57, 58, 59, 60, 64, 66, 67, 70, 71, 72, 73, 79, 80, 81, 82, 84, 85, 86, 88, 91, 94, 95, 101, 102, 103, 104, 106, 107, 108, 110, 113, 114, 115, 116, 117, 121, 124, 125, 126, 127, 128, 132, 135, 136, 137, 141, 144, 145, 146, 150, 153, 157, 160, 164, 166, 167, 170, 171, 172, 176, 178, 179, 180, 182, 183, 184, 189, 190, 193, 194, 198, 199, 204, 207, 208, 210, 211, 212, 216, 217, 219, 220], "summary": {"covered_lines": 106, "num_statements": 106, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSecurityHeadersIntegration": {"executed_lines": [228, 230, 231, 234, 237, 238, 240, 241, 242, 244, 247, 248, 249, 253, 254, 257, 260, 262, 263, 265, 266, 268, 270, 271, 272, 275, 283, 284, 285, 286, 288, 289, 290, 293, 296, 297, 302, 303, 304], "summary": {"covered_lines": 39, "num_statements": 41, "percent_covered": 95.1219512195122, "percent_covered_display": "95", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [276, 279], "excluded_lines": []}, "TestSecurityHeadersConfiguration": {"executed_lines": [312, 313, 315, 318, 319, 321, 323, 324, 325, 327, 328, 332, 333, 335, 338, 342, 343, 345, 346, 347, 349, 350, 351, 354, 355, 358, 359], "summary": {"covered_lines": 27, "num_statements": 27, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 6, 7, 8, 9, 10, 13, 14, 16, 40, 45, 62, 75, 76, 97, 98, 119, 130, 139, 148, 155, 162, 174, 186, 196, 214, 223, 224, 226, 251, 281, 307, 308, 310, 330, 362], "summary": {"covered_lines": 32, "num_statements": 33, "percent_covered": 96.96969696969697, "percent_covered_display": "97", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [363], "excluded_lines": []}}}, "tests\\test_sentry_config.py": {"executed_lines": [1, 6, 7, 8, 9, 12, 13, 14, 16, 17, 19, 20, 23, 24, 26, 27, 29, 30, 33, 38, 39, 41, 42, 45, 47, 48, 50, 54, 56, 63, 64, 66, 70, 72, 79, 80, 82, 83, 85, 86, 87, 89, 90, 92, 93, 96, 104, 105, 107, 108, 111, 112, 113, 115, 116, 121, 124, 125, 126, 128, 129, 132, 135, 136, 138, 139, 141, 142, 143, 145, 147, 149, 150, 152, 155, 157, 159, 160, 162, 170, 172, 175, 176, 178, 179, 181, 182, 185, 187, 188, 190, 191, 193, 194, 197, 199, 200, 202, 203, 205, 206, 207, 210, 211, 212, 214, 217, 218, 220, 221, 223, 227, 229, 230, 232, 233, 235, 239, 241, 242, 244, 245, 247, 251, 253, 254, 256, 257, 259, 263, 265, 266, 269, 270, 272, 273, 275, 276, 278, 279, 296, 297, 299, 300, 302, 303, 309, 310, 312, 313, 315, 322, 324, 326, 327, 330, 331, 333, 334, 335, 337, 338, 340, 350, 352, 355, 356, 358, 359, 362, 365, 366, 370, 371, 374, 377, 378, 379, 381, 383, 386, 388, 389, 390, 392, 393, 394, 397, 398, 400, 401, 403, 404, 406, 407, 413, 414, 416, 417, 419, 420, 426, 427, 429, 430, 432, 433, 441, 442, 444, 446, 449, 450, 452, 453, 454, 457, 458, 459, 461, 462, 463, 466, 468, 469, 471, 472, 475, 478, 479], "summary": {"covered_lines": 219, "num_statements": 245, "percent_covered": 89.38775510204081, "percent_covered_display": "89", "missing_lines": 26, "excluded_lines": 0}, "missing_lines": [34, 35, 36, 57, 60, 61, 73, 76, 97, 100, 101, 102, 119, 281, 283, 291, 294, 306, 367, 368, 410, 411, 423, 424, 436, 438], "excluded_lines": [], "functions": {"TestSentryInitialization.test_sentry_init_with_dsn": {"executed_lines": [29, 30, 33], "summary": {"covered_lines": 3, "num_statements": 6, "percent_covered": 50.0, "percent_covered_display": "50", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [34, 35, 36], "excluded_lines": []}, "TestSentryInitialization.test_sentry_init_without_dsn": {"executed_lines": [41, 42, 45], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSentryInitialization.test_sentry_init_production_config": {"executed_lines": [50, 54, 56], "summary": {"covered_lines": 3, "num_statements": 6, "percent_covered": 50.0, "percent_covered_display": "50", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [57, 60, 61], "excluded_lines": []}, "TestSentryInitialization.test_sentry_init_development_config": {"executed_lines": [66, 70, 72], "summary": {"covered_lines": 3, "num_statements": 5, "percent_covered": 60.0, "percent_covered_display": "60", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [73, 76], "excluded_lines": []}, "TestSentryFlaskIntegration.app": {"executed_lines": [85, 86, 87], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSentryFlaskIntegration.test_flask_sentry_configuration": {"executed_lines": [92, 93, 96], "summary": {"covered_lines": 3, "num_statements": 7, "percent_covered": 42.857142857142854, "percent_covered_display": "43", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [97, 100, 101, 102], "excluded_lines": []}, "TestSentryFlaskIntegration.test_flask_sentry_error_handling": {"executed_lines": [107, 108, 111, 112, 115, 116], "summary": {"covered_lines": 6, "num_statements": 7, "percent_covered": 85.71428571428571, "percent_covered_display": "86", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [119], "excluded_lines": []}, "TestSentryFlaskIntegration.test_flask_sentry_error_handling.test_error": {"executed_lines": [113], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSentryFlaskIntegration.test_flask_app_without_sentry": {"executed_lines": [124, 125, 128, 129, 132], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSentryFlaskIntegration.test_flask_app_without_sentry.test_route": {"executed_lines": [126], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSentryErrorCapture.test_exception_capture": {"executed_lines": [141, 142, 143, 145, 147], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSentryErrorCapture.test_message_capture": {"executed_lines": [152, 155, 157], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSentryErrorCapture.test_breadcrumb_capture": {"executed_lines": [162, 170, 172], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSentryPerformanceMonitoring.test_transaction_creation": {"executed_lines": [181, 182, 185, 187, 188], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSentryPerformanceMonitoring.test_span_creation": {"executed_lines": [193, 194, 197, 199, 200], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSentryPerformanceMonitoring.test_scope_configuration": {"executed_lines": [205, 206, 207, 210, 211, 212, 214], "summary": {"covered_lines": 7, "num_statements": 7, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSentryConfiguration.test_sentry_environment_configuration": {"executed_lines": [223, 227, 229, 230], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSentryConfiguration.test_sentry_release_configuration": {"executed_lines": [235, 239, 241, 242], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSentryConfiguration.test_sentry_sample_rate_configuration": {"executed_lines": [247, 251, 253, 254], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSentryConfiguration.test_sentry_profiles_sample_rate_configuration": {"executed_lines": [259, 263, 265, 266], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSentryFiltering.test_sentry_before_send_filter": {"executed_lines": [275, 276, 278, 279], "summary": {"covered_lines": 4, "num_statements": 8, "percent_covered": 50.0, "percent_covered_display": "50", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [281, 283, 291, 294], "excluded_lines": []}, "TestSentryFiltering.test_sentry_ignore_errors": {"executed_lines": [299, 300, 302, 303], "summary": {"covered_lines": 4, "num_statements": 5, "percent_covered": 80.0, "percent_covered_display": "80", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [306], "excluded_lines": []}, "TestSentryContextData.test_user_context": {"executed_lines": [315, 322, 324], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSentryContextData.test_tag_context": {"executed_lines": [330, 331, 333, 334, 335], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSentryContextData.test_custom_context": {"executed_lines": [340, 350, 352], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSentryErrorHandling.test_sentry_init_failure": {"executed_lines": [362, 365, 366], "summary": {"covered_lines": 3, "num_statements": 5, "percent_covered": 60.0, "percent_covered_display": "60", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [367, 368], "excluded_lines": []}, "TestSentryErrorHandling.test_sentry_capture_failure": {"executed_lines": [374, 377, 378, 379, 381], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSentryErrorHandling.test_sentry_disabled_gracefully": {"executed_lines": [386, 388, 389, 392, 393, 394], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSentryErrorHandling.test_sentry_disabled_gracefully.test_route": {"executed_lines": [390], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSentryIntegrations.test_flask_integration": {"executed_lines": [403, 404, 406, 407], "summary": {"covered_lines": 4, "num_statements": 6, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [410, 411], "excluded_lines": []}, "TestSentryIntegrations.test_logging_integration": {"executed_lines": [416, 417, 419, 420], "summary": {"covered_lines": 4, "num_statements": 6, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [423, 424], "excluded_lines": []}, "TestSentryIntegrations.test_sqlalchemy_integration": {"executed_lines": [429, 430, 432, 433], "summary": {"covered_lines": 4, "num_statements": 6, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [436, 438], "excluded_lines": []}, "TestSentryPerformance.test_sentry_overhead_minimal": {"executed_lines": [446, 449, 450, 452, 453, 454, 457, 458, 459, 461, 462, 463, 466], "summary": {"covered_lines": 13, "num_statements": 13, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSentryPerformance.test_transaction_performance_tracking": {"executed_lines": [471, 472, 475, 478, 479], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 6, 7, 8, 9, 12, 13, 14, 16, 17, 19, 20, 23, 24, 26, 27, 38, 39, 47, 48, 63, 64, 79, 80, 82, 83, 89, 90, 104, 105, 121, 135, 136, 138, 139, 149, 150, 159, 160, 175, 176, 178, 179, 190, 191, 202, 203, 217, 218, 220, 221, 232, 233, 244, 245, 256, 257, 269, 270, 272, 273, 296, 297, 309, 310, 312, 313, 326, 327, 337, 338, 355, 356, 358, 359, 370, 371, 383, 397, 398, 400, 401, 413, 414, 426, 427, 441, 442, 444, 468, 469], "summary": {"covered_lines": 80, "num_statements": 80, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"TestSentryInitialization": {"executed_lines": [29, 30, 33, 41, 42, 45, 50, 54, 56, 66, 70, 72], "summary": {"covered_lines": 12, "num_statements": 20, "percent_covered": 60.0, "percent_covered_display": "60", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [34, 35, 36, 57, 60, 61, 73, 76], "excluded_lines": []}, "TestSentryFlaskIntegration": {"executed_lines": [85, 86, 87, 92, 93, 96, 107, 108, 111, 112, 113, 115, 116, 124, 125, 126, 128, 129, 132], "summary": {"covered_lines": 19, "num_statements": 24, "percent_covered": 79.16666666666667, "percent_covered_display": "79", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [97, 100, 101, 102, 119], "excluded_lines": []}, "TestSentryErrorCapture": {"executed_lines": [141, 142, 143, 145, 147, 152, 155, 157, 162, 170, 172], "summary": {"covered_lines": 11, "num_statements": 11, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSentryPerformanceMonitoring": {"executed_lines": [181, 182, 185, 187, 188, 193, 194, 197, 199, 200, 205, 206, 207, 210, 211, 212, 214], "summary": {"covered_lines": 17, "num_statements": 17, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSentryConfiguration": {"executed_lines": [223, 227, 229, 230, 235, 239, 241, 242, 247, 251, 253, 254, 259, 263, 265, 266], "summary": {"covered_lines": 16, "num_statements": 16, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSentryFiltering": {"executed_lines": [275, 276, 278, 279, 299, 300, 302, 303], "summary": {"covered_lines": 8, "num_statements": 13, "percent_covered": 61.53846153846154, "percent_covered_display": "62", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [281, 283, 291, 294, 306], "excluded_lines": []}, "TestSentryContextData": {"executed_lines": [315, 322, 324, 330, 331, 333, 334, 335, 340, 350, 352], "summary": {"covered_lines": 11, "num_statements": 11, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TestSentryErrorHandling": {"executed_lines": [362, 365, 366, 374, 377, 378, 379, 381, 386, 388, 389, 390, 392, 393, 394], "summary": {"covered_lines": 15, "num_statements": 17, "percent_covered": 88.23529411764706, "percent_covered_display": "88", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [367, 368], "excluded_lines": []}, "TestSentryIntegrations": {"executed_lines": [403, 404, 406, 407, 416, 417, 419, 420, 429, 430, 432, 433], "summary": {"covered_lines": 12, "num_statements": 18, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [410, 411, 423, 424, 436, 438], "excluded_lines": []}, "TestSentryPerformance": {"executed_lines": [446, 449, 450, 452, 453, 454, 457, 458, 459, 461, 462, 463, 466, 471, 472, 475, 478, 479], "summary": {"covered_lines": 18, "num_statements": 18, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 6, 7, 8, 9, 12, 13, 14, 16, 17, 19, 20, 23, 24, 26, 27, 38, 39, 47, 48, 63, 64, 79, 80, 82, 83, 89, 90, 104, 105, 121, 135, 136, 138, 139, 149, 150, 159, 160, 175, 176, 178, 179, 190, 191, 202, 203, 217, 218, 220, 221, 232, 233, 244, 245, 256, 257, 269, 270, 272, 273, 296, 297, 309, 310, 312, 313, 326, 327, 337, 338, 355, 356, 358, 359, 370, 371, 383, 397, 398, 400, 401, 413, 414, 426, 427, 441, 442, 444, 468, 469], "summary": {"covered_lines": 80, "num_statements": 80, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "verify_sentry.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 17, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 17, "excluded_lines": 0}, "missing_lines": [10, 11, 14, 30, 32, 33, 34, 35, 37, 38, 40, 42, 43, 44, 45, 46, 48], "excluded_lines": [], "functions": {"hello_world": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [34, 35], "excluded_lines": []}, "health_check": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [40], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [10, 11, 14, 30, 32, 33, 37, 38, 42, 43, 44, 45, 46, 48], "excluded_lines": []}}, "classes": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 17, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 17, "excluded_lines": 0}, "missing_lines": [10, 11, 14, 30, 32, 33, 34, 35, 37, 38, 40, 42, 43, 44, 45, 46, 48], "excluded_lines": []}}}}, "totals": {"covered_lines": 3346, "num_statements": 5560, "percent_covered": 60.17985611510792, "percent_covered_display": "60", "missing_lines": 2214, "excluded_lines": 1}}