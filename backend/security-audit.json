{"dependencies": [{"name": "boolean-py", "version": "5.0", "vulns": []}, {"name": "cachecontrol", "version": "0.14.3", "vulns": []}, {"name": "certifi", "version": "2025.7.14", "vulns": []}, {"name": "charset-normalizer", "version": "3.4.2", "vulns": []}, {"name": "cyclonedx-python-lib", "version": "9.1.0", "vulns": []}, {"name": "defusedxml", "version": "0.7.1", "vulns": []}, {"name": "filelock", "version": "3.18.0", "vulns": []}, {"name": "idna", "version": "3.10", "vulns": []}, {"name": "license-expression", "version": "30.4.4", "vulns": []}, {"name": "markdown-it-py", "version": "3.0.0", "vulns": []}, {"name": "mdurl", "version": "0.1.2", "vulns": []}, {"name": "msgpack", "version": "1.1.1", "vulns": []}, {"name": "packageurl-python", "version": "0.17.3", "vulns": []}, {"name": "packaging", "version": "25.0", "vulns": []}, {"name": "pip", "version": "24.0", "vulns": []}, {"name": "pip-api", "version": "0.0.34", "vulns": []}, {"name": "pip-audit", "version": "2.9.0", "vulns": []}, {"name": "pip-requirements-parser", "version": "32.0.1", "vulns": []}, {"name": "platformdirs", "version": "4.3.8", "vulns": []}, {"name": "py-serializable", "version": "2.1.0", "vulns": []}, {"name": "pygments", "version": "2.19.2", "vulns": []}, {"name": "pyparsing", "version": "3.2.3", "vulns": []}, {"name": "requests", "version": "2.32.4", "vulns": []}, {"name": "rich", "version": "14.1.0", "vulns": []}, {"name": "setuptools", "version": "65.5.0", "vulns": [{"id": "PYSEC-2022-43012", "fix_versions": ["65.5.1"], "aliases": ["CVE-2022-40897"], "description": "Python Packaging Authority (PyPA) setuptools before 65.5.1 allows remote attackers to cause a denial of service via HTML in a crafted package or custom PackageIndex page. There is a Regular Expression Denial of Service (ReDoS) in package_index.py."}, {"id": "PYSEC-2025-49", "fix_versions": ["78.1.1"], "aliases": ["CVE-2025-47273", "GHSA-5rjg-fvgr-3xxf"], "description": "setuptools is a package that allows users to download, build, install, upgrade, and uninstall Python packages. A path traversal vulnerability in `PackageIndex` is present in setuptools prior to version 78.1.1. An attacker would be allowed to write files to arbitrary locations on the filesystem with the permissions of the process running the Python code, which could escalate to remote code execution depending on the context. Version 78.1.1 fixes the issue."}]}, {"name": "sortedcontainers", "version": "2.4.0", "vulns": []}, {"name": "toml", "version": "0.10.2", "vulns": []}, {"name": "urllib3", "version": "2.5.0", "vulns": []}], "fixes": []}