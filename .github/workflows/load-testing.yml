name: Load Testing

on:
  schedule:
    # Run load tests weekly on Monday at 2 AM UTC
    - cron: '0 2 * * 1'
  workflow_dispatch:
    inputs:
      test_type:
        description: 'Type of load test to run'
        required: true
        default: 'light'
        type: choice
        options:
          - light
          - medium
          - heavy
          - spike
          - all
      target_environment:
        description: 'Target environment for load testing'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production
  pull_request:
    paths:
      - 'backend/**'
      - 'frontend/**'
      - '.github/workflows/load-testing.yml'
    types: [opened, synchronize]

env:
  NODE_VERSION: '18'
  PYTHON_VERSION: '3.11'

jobs:
  backend-load-test:
    runs-on: ubuntu-latest
    if: github.event_name != 'pull_request' || contains(github.event.pull_request.labels.*.name, 'load-test')
    
    services:
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: sizewise_test
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          cache: 'pip'

      - name: Install backend dependencies
        run: |
          cd backend
          pip install -r requirements.txt
          pip install locust

      - name: Setup test environment
        run: |
          cd backend
          cp .env.example .env.test
          # Configure test environment variables
          echo "REDIS_URL=redis://localhost:6379" >> .env.test
          echo "DATABASE_URL=postgresql://postgres:postgres@localhost:5432/sizewise_test" >> .env.test
          echo "FLASK_ENV=testing" >> .env.test

      - name: Start backend server
        run: |
          cd backend
          python app.py &
          sleep 10
          # Wait for server to be ready
          curl --retry 10 --retry-delay 2 --retry-connrefused http://localhost:5000/api/health
        env:
          FLASK_ENV: testing

      - name: Run backend load tests
        run: |
          cd backend
          python scripts/run_load_tests.py ${{ github.event.inputs.test_type || 'light' }} --host http://localhost:5000
        timeout-minutes: 30

      - name: Upload backend load test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: backend-load-test-results
          path: backend/load-test-results/
          retention-days: 30

      - name: Generate backend load test report
        if: always()
        run: |
          cd backend
          python scripts/run_load_tests.py report > load-test-summary.txt
          cat load-test-summary.txt

      - name: Comment PR with backend results
        if: github.event_name == 'pull_request' && always()
        uses: actions/github-script@v6
        with:
          script: |
            const fs = require('fs');
            try {
              const summary = fs.readFileSync('backend/load-test-summary.txt', 'utf8');
              github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: `## Backend Load Test Results\n\n\`\`\`\n${summary}\n\`\`\``
              });
            } catch (error) {
              console.log('No load test summary found');
            }

  frontend-load-test:
    runs-on: ubuntu-latest
    if: github.event_name != 'pull_request' || contains(github.event.pull_request.labels.*.name, 'load-test')
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: Install frontend dependencies
        run: |
          cd frontend
          npm ci

      - name: Install Playwright browsers
        run: |
          cd frontend
          npx playwright install --with-deps

      - name: Build frontend
        run: |
          cd frontend
          npm run build

      - name: Start frontend server
        run: |
          cd frontend
          npm start &
          sleep 15
          # Wait for server to be ready
          curl --retry 10 --retry-delay 2 --retry-connrefused http://localhost:3000
        env:
          NODE_ENV: production

      - name: Run frontend load tests
        run: |
          cd frontend
          case "${{ github.event.inputs.test_type || 'light' }}" in
            "light") npm run test:load:light ;;
            "medium") npm run test:load:medium ;;
            "stress") npm run test:load:stress ;;
            *) npm run test:load ;;
          esac
        timeout-minutes: 20

      - name: Upload frontend load test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: frontend-load-test-results
          path: |
            frontend/test-results/
            frontend/playwright-report/
          retention-days: 30

      - name: Generate frontend load test report
        if: always()
        run: |
          cd frontend
          echo "## Frontend Load Test Summary" > load-test-summary.md
          echo "" >> load-test-summary.md
          if [ -d "test-results" ]; then
            echo "Test results generated successfully." >> load-test-summary.md
            echo "Check artifacts for detailed reports." >> load-test-summary.md
          else
            echo "No test results found." >> load-test-summary.md
          fi

      - name: Comment PR with frontend results
        if: github.event_name == 'pull_request' && always()
        uses: actions/github-script@v6
        with:
          script: |
            const fs = require('fs');
            try {
              const summary = fs.readFileSync('frontend/load-test-summary.md', 'utf8');
              github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: summary
              });
            } catch (error) {
              console.log('No frontend load test summary found');
            }

  performance-analysis:
    runs-on: ubuntu-latest
    needs: [backend-load-test, frontend-load-test]
    if: always() && (needs.backend-load-test.result == 'success' || needs.frontend-load-test.result == 'success')
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download backend results
        uses: actions/download-artifact@v3
        continue-on-error: true
        with:
          name: backend-load-test-results
          path: backend-results/

      - name: Download frontend results
        uses: actions/download-artifact@v3
        continue-on-error: true
        with:
          name: frontend-load-test-results
          path: frontend-results/

      - name: Analyze performance trends
        run: |
          echo "# Load Testing Performance Analysis" > performance-analysis.md
          echo "" >> performance-analysis.md
          echo "## Test Configuration" >> performance-analysis.md
          echo "- Test Type: ${{ github.event.inputs.test_type || 'light' }}" >> performance-analysis.md
          echo "- Environment: ${{ github.event.inputs.target_environment || 'staging' }}" >> performance-analysis.md
          echo "- Timestamp: $(date -u)" >> performance-analysis.md
          echo "" >> performance-analysis.md
          
          if [ -d "backend-results" ]; then
            echo "## Backend Results" >> performance-analysis.md
            echo "Backend load test results available in artifacts." >> performance-analysis.md
            echo "" >> performance-analysis.md
          fi
          
          if [ -d "frontend-results" ]; then
            echo "## Frontend Results" >> performance-analysis.md
            echo "Frontend load test results available in artifacts." >> performance-analysis.md
            echo "" >> performance-analysis.md
          fi
          
          echo "## Recommendations" >> performance-analysis.md
          echo "1. Review response time trends" >> performance-analysis.md
          echo "2. Check error rates and failure patterns" >> performance-analysis.md
          echo "3. Monitor resource utilization" >> performance-analysis.md
          echo "4. Compare with previous baselines" >> performance-analysis.md

      - name: Upload performance analysis
        uses: actions/upload-artifact@v3
        with:
          name: performance-analysis
          path: performance-analysis.md
          retention-days: 90

      - name: Create performance issue
        if: failure() && github.event_name == 'schedule'
        uses: actions/github-script@v6
        with:
          script: |
            const fs = require('fs');
            const analysis = fs.readFileSync('performance-analysis.md', 'utf8');
            
            github.rest.issues.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title: `Performance Alert: Load Test Failure - ${new Date().toISOString().split('T')[0]}`,
              body: `${analysis}\n\n**Action Required**: Load tests have failed. Please investigate performance degradation.`,
              labels: ['performance', 'load-testing', 'alert']
            });

  cleanup:
    runs-on: ubuntu-latest
    needs: [backend-load-test, frontend-load-test, performance-analysis]
    if: always()
    
    steps:
      - name: Cleanup test resources
        run: |
          echo "Load testing workflow completed"
          echo "Results and artifacts have been uploaded"
          echo "Check the Actions tab for detailed reports"
